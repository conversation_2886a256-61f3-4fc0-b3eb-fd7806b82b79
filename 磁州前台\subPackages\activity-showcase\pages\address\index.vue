<template>
  <view class="address-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">收货地址</view>
        <view class="navbar-right">
          <view class="add-btn" @click="addAddress">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 地址列表 -->
      <view class="address-list" v-if="addressList.length > 0">
        <view 
          class="address-item" 
          v-for="(address, index) in addressList" 
          :key="address.id"
          @click="selectAddress(address)"
        >
          <view class="address-content">
            <view class="address-header">
              <view class="contact-info">
                <text class="contact-name">{{ address.name }}</text>
                <text class="contact-phone">{{ address.phone }}</text>
              </view>
              <view class="address-tags">
                <view class="tag default" v-if="address.isDefault">
                  <text class="tag-text">默认</text>
                </view>
                <view class="tag" :class="address.type">
                  <text class="tag-text">{{ getAddressTypeText(address.type) }}</text>
                </view>
              </view>
            </view>
            
            <text class="address-detail">{{ getFullAddress(address) }}</text>
            
            <view class="address-actions">
              <view class="action-btn" @click.stop="editAddress(address)">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
                <text class="action-text">编辑</text>
              </view>
              
              <view class="action-btn" @click.stop="deleteAddress(address)">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="3 6 5 6 21 6"></polyline>
                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                </svg>
                <text class="action-text">删除</text>
              </view>
              
              <view class="action-btn" @click.stop="setDefault(address)" v-if="!address.isDefault">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
                </svg>
                <text class="action-text">设为默认</text>
              </view>
            </view>
          </view>
          
          <view class="address-radio" v-if="isSelectMode">
            <view class="radio-btn" :class="{ checked: selectedAddressId === address.id }">
              <view class="radio-dot" v-if="selectedAddressId === address.id"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <view class="empty-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
        </view>
        <text class="empty-text">暂无收货地址</text>
        <text class="empty-desc">添加收货地址，享受便捷购物体验</text>
        <view class="add-address-btn" @click="addAddress">
          <text class="btn-text">添加地址</text>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 底部确认按钮 -->
    <view class="bottom-actions" v-if="isSelectMode && addressList.length > 0">
      <view class="confirm-btn" :class="{ disabled: !selectedAddressId }" @click="confirmSelection">
        <text class="btn-text">确认选择</text>
      </view>
    </view>

    <!-- 地址编辑弹窗 -->
    <view class="address-modal" v-if="showAddressModal" @click="hideAddressModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ isEditMode ? '编辑地址' : '新增地址' }}</text>
          <view class="close-btn" @click="hideAddressModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">收货人</text>
            <input class="form-input" v-model="addressForm.name" placeholder="请输入收货人姓名" />
          </view>
          
          <view class="form-group">
            <text class="form-label">手机号</text>
            <input class="form-input" v-model="addressForm.phone" placeholder="请输入手机号" type="number" />
          </view>
          
          <view class="form-group">
            <text class="form-label">所在地区</text>
            <view class="region-picker" @click="showRegionPicker">
              <text class="region-text" v-if="addressForm.region">{{ addressForm.region }}</text>
              <text class="region-placeholder" v-else>请选择省市区</text>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label">详细地址</text>
            <textarea class="form-textarea" v-model="addressForm.detail" placeholder="请输入详细地址（街道、门牌号等）" />
          </view>
          
          <view class="form-group">
            <text class="form-label">地址类型</text>
            <view class="type-options">
              <view 
                class="type-option" 
                v-for="(type, index) in addressTypes" 
                :key="index"
                :class="{ active: addressForm.type === type.value }"
                @click="selectAddressType(type.value)"
              >
                <text class="type-text">{{ type.label }}</text>
              </view>
            </view>
          </view>
          
          <view class="form-group">
            <view class="checkbox-group">
              <view class="checkbox-item" @click="toggleDefault">
                <view class="checkbox" :class="{ checked: addressForm.isDefault }">
                  <svg v-if="addressForm.isDefault" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </view>
                <text class="checkbox-text">设为默认地址</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="modal-actions">
          <view class="action-btn cancel" @click="hideAddressModal">
            <text class="btn-text">取消</text>
          </view>
          <view class="action-btn confirm" @click="saveAddress">
            <text class="btn-text">保存</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const isSelectMode = ref(false)
const selectedAddressId = ref(null)
const showAddressModal = ref(false)
const isEditMode = ref(false)

// 地址表单
const addressForm = ref({
  id: null,
  name: '',
  phone: '',
  region: '',
  detail: '',
  type: 'home',
  isDefault: false
})

// 地址类型
const addressTypes = ref([
  { label: '家', value: 'home' },
  { label: '公司', value: 'company' },
  { label: '学校', value: 'school' }
])

// 地址列表
const addressList = ref([
  {
    id: 1,
    name: '张三',
    phone: '138****8888',
    province: '北京市',
    city: '北京市',
    district: '朝阳区',
    detail: '三里屯街道工体北路8号院',
    type: 'home',
    isDefault: true
  },
  {
    id: 2,
    name: '李四',
    phone: '139****9999',
    province: '上海市',
    city: '上海市',
    district: '浦东新区',
    detail: '陆家嘴金融贸易区世纪大道100号',
    type: 'company',
    isDefault: false
  }
])

// 页面加载
onMounted(() => {
  console.log('收货地址页面加载')
  
  // 检查是否为选择模式
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options
  
  if (options.select === 'true') {
    isSelectMode.value = true
    if (options.selectedId) {
      selectedAddressId.value = parseInt(options.selectedId)
    }
  }
  
  loadAddressList()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function addAddress() {
  isEditMode.value = false
  resetAddressForm()
  showAddressModal.value = true
}

function editAddress(address) {
  isEditMode.value = true
  addressForm.value = {
    id: address.id,
    name: address.name,
    phone: address.phone,
    region: `${address.province} ${address.city} ${address.district}`,
    detail: address.detail,
    type: address.type,
    isDefault: address.isDefault
  }
  showAddressModal.value = true
}

function deleteAddress(address) {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个地址吗？',
    success: (res) => {
      if (res.confirm) {
        const index = addressList.value.findIndex(item => item.id === address.id)
        if (index > -1) {
          addressList.value.splice(index, 1)
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    }
  })
}

function setDefault(address) {
  // 取消其他地址的默认状态
  addressList.value.forEach(item => {
    item.isDefault = false
  })
  
  // 设置当前地址为默认
  address.isDefault = true
  
  uni.showToast({
    title: '设置成功',
    icon: 'success'
  })
}

function selectAddress(address) {
  if (isSelectMode.value) {
    selectedAddressId.value = address.id
  }
}

function confirmSelection() {
  if (!selectedAddressId.value) return
  
  const selectedAddress = addressList.value.find(item => item.id === selectedAddressId.value)
  if (selectedAddress) {
    // 返回选中的地址
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    
    if (prevPage) {
      prevPage.$vm.selectedAddress = selectedAddress
    }
    
    uni.navigateBack()
  }
}

function hideAddressModal() {
  showAddressModal.value = false
  resetAddressForm()
}

function showRegionPicker() {
  // 这里应该调用地区选择器
  uni.showToast({
    title: '地区选择器',
    icon: 'none'
  })
}

function selectAddressType(type) {
  addressForm.value.type = type
}

function toggleDefault() {
  addressForm.value.isDefault = !addressForm.value.isDefault
}

function saveAddress() {
  // 验证表单
  if (!addressForm.value.name.trim()) {
    uni.showToast({
      title: '请输入收货人姓名',
      icon: 'none'
    })
    return
  }
  
  if (!addressForm.value.phone.trim()) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none'
    })
    return
  }
  
  if (!addressForm.value.region.trim()) {
    uni.showToast({
      title: '请选择所在地区',
      icon: 'none'
    })
    return
  }
  
  if (!addressForm.value.detail.trim()) {
    uni.showToast({
      title: '请输入详细地址',
      icon: 'none'
    })
    return
  }
  
  // 保存地址
  if (isEditMode.value) {
    // 编辑模式
    const index = addressList.value.findIndex(item => item.id === addressForm.value.id)
    if (index > -1) {
      const regionParts = addressForm.value.region.split(' ')
      addressList.value[index] = {
        ...addressList.value[index],
        name: addressForm.value.name,
        phone: addressForm.value.phone,
        province: regionParts[0] || '',
        city: regionParts[1] || '',
        district: regionParts[2] || '',
        detail: addressForm.value.detail,
        type: addressForm.value.type,
        isDefault: addressForm.value.isDefault
      }
    }
  } else {
    // 新增模式
    const regionParts = addressForm.value.region.split(' ')
    const newAddress = {
      id: Date.now(),
      name: addressForm.value.name,
      phone: addressForm.value.phone,
      province: regionParts[0] || '',
      city: regionParts[1] || '',
      district: regionParts[2] || '',
      detail: addressForm.value.detail,
      type: addressForm.value.type,
      isDefault: addressForm.value.isDefault
    }
    
    addressList.value.push(newAddress)
  }
  
  // 如果设置为默认地址，取消其他地址的默认状态
  if (addressForm.value.isDefault) {
    addressList.value.forEach(item => {
      if (item.id !== addressForm.value.id) {
        item.isDefault = false
      }
    })
  }
  
  hideAddressModal()
  
  uni.showToast({
    title: isEditMode.value ? '编辑成功' : '添加成功',
    icon: 'success'
  })
}

function resetAddressForm() {
  addressForm.value = {
    id: null,
    name: '',
    phone: '',
    region: '',
    detail: '',
    type: 'home',
    isDefault: false
  }
}

function getAddressTypeText(type) {
  const typeObj = addressTypes.value.find(item => item.value === type)
  return typeObj ? typeObj.label : '其他'
}

function getFullAddress(address) {
  return `${address.province} ${address.city} ${address.district} ${address.detail}`
}

function loadAddressList() {
  // 模拟加载地址列表
  setTimeout(() => {
    console.log('地址列表加载完成')
  }, 500)
}
</script>

<style scoped>
/* 收货地址样式开始 */
.address-container {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 80px;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(33, 150, 243, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn, .add-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
}

.back-btn svg, .add-btn svg {
  color: white;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
}

/* 地址列表样式 */
.address-list {
  padding: 16px;
}

.address-item {
  display: flex;
  background: white;
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.address-item:active {
  transform: scale(0.98);
}

.address-content {
  flex: 1;
  padding: 16px;
}

.address-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contact-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.contact-phone {
  font-size: 14px;
  color: #666;
}

.address-tags {
  display: flex;
  gap: 6px;
}

.tag {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
}

.tag.default {
  background: #2196F3;
}

.tag.home {
  background: #4CAF50;
}

.tag.company {
  background: #FF9800;
}

.tag.school {
  background: #9C27B0;
}

.tag-text {
  color: white;
  font-weight: 500;
}

.address-detail {
  display: block;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12px;
}

.address-actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 0;
}

.action-btn svg {
  color: #666;
}

.action-text {
  font-size: 12px;
  color: #666;
}

.address-radio {
  display: flex;
  align-items: center;
  padding: 16px;
}

.radio-btn {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio-btn.checked {
  border-color: #2196F3;
}

.radio-dot {
  width: 10px;
  height: 10px;
  background: #2196F3;
  border-radius: 5px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80px 20px;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-icon svg {
  color: #999;
}

.empty-text {
  font-size: 16px;
  color: #666;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #999;
  text-align: center;
  margin-bottom: 24px;
}

.add-address-btn {
  padding: 12px 24px;
  background: #2196F3;
  border-radius: 24px;
}

.add-address-btn .btn-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

/* 底部按钮样式 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  z-index: 1000;
}

.confirm-btn {
  width: 100%;
  padding: 12px;
  background: #2196F3;
  border-radius: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.confirm-btn.disabled {
  background: #ccc;
}

.confirm-btn .btn-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

/* 地址编辑弹窗样式 */
.address-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.close-btn svg {
  color: #666;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
}

.form-textarea {
  min-height: 80px;
  resize: none;
}

.region-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.region-text {
  font-size: 16px;
  color: #333;
}

.region-placeholder {
  font-size: 16px;
  color: #999;
}

.region-picker svg {
  color: #666;
}

.type-options {
  display: flex;
  gap: 8px;
}

.type-option {
  flex: 1;
  padding: 10px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.type-option.active {
  background: rgba(33, 150, 243, 0.1);
  border-color: #2196F3;
}

.type-text {
  font-size: 14px;
  color: #333;
}

.type-option.active .type-text {
  color: #2196F3;
  font-weight: 500;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #ddd;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #2196F3;
  border-color: #2196F3;
}

.checkbox svg {
  color: white;
}

.checkbox-text {
  font-size: 14px;
  color: #333;
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
}

.action-btn.cancel {
  background: #f5f5f5;
}

.action-btn.confirm {
  background: #2196F3;
}

.action-btn .btn-text {
  font-size: 16px;
  font-weight: 500;
}

.action-btn.cancel .btn-text {
  color: #666;
}

.action-btn.confirm .btn-text {
  color: white;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 收货地址样式结束 */
</style>
