{"version": 3, "file": "create.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/discount/create.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXNjb3VudFxjcmVhdGUudnVl"], "sourcesContent": ["<!-- 满减活动创建页面 (create.vue) -->\n<template>\n    <view class=\"discount-create-container\">\n      <!-- 自定义导航栏 -->\n      <view class=\"navbar\">\n        <view class=\"navbar-back\" @tap=\"goBack\">\n          <view class=\"back-icon\"></view>\n        </view>\n        <text class=\"navbar-title\">创建满减活动</text>\n        <view class=\"navbar-right\">\n          <text class=\"save-btn\" @tap=\"saveDiscount\">保存</text>\n        </view>\n      </view>\n      \n      <!-- 创建表单 -->\n      <view class=\"create-form\">\n        <!-- 基本信息 -->\n        <view class=\"form-section\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">基本信息</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"item-label\">活动名称</text>\n            <input class=\"item-input\" v-model=\"discountForm.title\" placeholder=\"请输入活动名称\" maxlength=\"20\" />\n            <text class=\"input-count\">{{discountForm.title.length}}/20</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"item-label\">活动时间</text>\n            <view class=\"date-picker\" @tap=\"showDatePicker('start')\">\n              <text class=\"date-text\">{{discountForm.startDate || '开始日期'}}</text>\n            </view>\n            <text class=\"date-separator\">至</text>\n            <view class=\"date-picker\" @tap=\"showDatePicker('end')\">\n              <text class=\"date-text\">{{discountForm.endDate || '结束日期'}}</text>\n            </view>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"item-label\">活动状态</text>\n            <view class=\"status-switch\">\n              <text class=\"status-text\">{{discountForm.status === 'active' ? '启用' : '暂停'}}</text>\n              <switch \n                :checked=\"discountForm.status === 'active'\" \n                @change=\"onStatusChange\" \n                color=\"#F8D800\" \n              />\n            </view>\n          </view>\n        </view>\n        \n        <!-- 满减规则 -->\n        <view class=\"form-section\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">满减规则</text>\n            <text class=\"add-rule\" @tap=\"addRule\">+ 添加规则</text>\n          </view>\n          \n          <view class=\"rules-list\">\n            <view \n              class=\"rule-item\" \n              v-for=\"(rule, index) in discountForm.rules\" \n              :key=\"index\"\n            >\n              <view class=\"rule-inputs\">\n                <view class=\"rule-input-group\">\n                  <text class=\"input-prefix\">满</text>\n                  <input \n                    class=\"rule-input\" \n                    type=\"digit\" \n                    v-model=\"rule.minAmount\" \n                    placeholder=\"0.00\"\n                  />\n                  <text class=\"input-suffix\">元</text>\n                </view>\n                <text class=\"rule-separator\">减</text>\n                <view class=\"rule-input-group\">\n                  <input \n                    class=\"rule-input\" \n                    type=\"digit\" \n                    v-model=\"rule.discountAmount\" \n                    placeholder=\"0.00\"\n                  />\n                  <text class=\"input-suffix\">元</text>\n                </view>\n              </view>\n              <view class=\"rule-delete\" @tap=\"deleteRule(index)\">\n                <text class=\"delete-icon\">×</text>\n              </view>\n            </view>\n            \n            <view class=\"empty-rules\" v-if=\"discountForm.rules.length === 0\">\n              <text class=\"empty-text\">请添加满减规则</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 使用设置 -->\n        <view class=\"form-section\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">使用设置</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"item-label\">适用商品</text>\n            <view class=\"item-right\" @tap=\"selectProducts\">\n              <text class=\"item-value\">{{discountForm.applicableProducts || '全部商品'}}</text>\n              <text class=\"item-arrow\">></text>\n            </view>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"item-label\">叠加使用</text>\n            <switch \n              :checked=\"discountForm.canStack\" \n              @change=\"onStackChange\" \n              color=\"#F8D800\" \n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"item-label\">每人限用</text>\n            <view class=\"limit-input-group\">\n              <input \n                class=\"limit-input\" \n                type=\"number\" \n                v-model=\"discountForm.perPersonLimit\" \n                placeholder=\"不限制\"\n              />\n              <text class=\"input-suffix\">次</text>\n            </view>\n          </view>\n          \n          <view class=\"form-item textarea-item\">\n            <text class=\"item-label\">活动说明</text>\n            <textarea \n              class=\"item-textarea\" \n              v-model=\"discountForm.instructions\" \n              placeholder=\"请输入活动说明\" \n              maxlength=\"200\"\n            ></textarea>\n            <text class=\"textarea-count\">{{discountForm.instructions.length}}/200</text>\n          </view>\n        </view>\n        \n        <!-- 分销设置 -->\n        <view class=\"form-section\" v-if=\"hasMerchantDistribution\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">分销设置</text>\n            <text class=\"section-desc\">设置此活动的分销规则和佣金</text>\n          </view>\n          \n          <distribution-setting \n            :initial-settings=\"discountForm.distributionSettings\"\n            @update=\"updateDistributionSettings\"\n          />\n        </view>\n        \n        <!-- 营销建议 -->\n        <view class=\"form-section\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">营销建议</text>\n          </view>\n          \n          <view class=\"tips-list\">\n            <view class=\"tip-item\">\n              <view class=\"tip-icon\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                  <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                  <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n                  <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\n                </svg>\n              </view>\n              <text class=\"tip-text\">满减活动是提高客单价的有效方式，建议满减门槛设置在平均客单价的1.2-1.5倍</text>\n          </view>\n          <view class=\"tip-item\">\n            <view class=\"tip-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n                <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\n              </svg>\n            </view>\n            <text class=\"tip-text\">多级满减规则可以覆盖不同消费能力的用户，提高活动参与度</text>\n          </view>\n          <view class=\"tip-item\">\n            <view class=\"tip-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n                <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\n              </svg>\n            </view>\n            <text class=\"tip-text\">建议优惠金额控制在满减门槛的10%-15%之间，既有吸引力又能保证利润</text>\n          </view>\n          </view>\n        </view>\n\n        <!-- 活动推广 -->\n        <view class=\"form-section\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">活动推广</text>\n            <text class=\"section-desc\">发布、置顶或刷新活动，提升曝光率</text>\n          </view>\n          \n          <MarketingPromotionActions \n            :activity-type=\"'discount'\"\n            :activity-id=\"tempDiscountId\"\n            :publish-mode-only=\"true\"\n            :show-actions=\"['publish']\"\n            @action-completed=\"handlePromotionCompleted\"\n          />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部保存按钮 -->\n    <view class=\"bottom-save-bar\">\n      <button class=\"save-button\" @tap=\"saveDiscount\">保存</button>\n    </view>\n    \n    <!-- 日期选择器 -->\n    <uni-calendar \n      v-if=\"showDatePickerDialog\"\n      :insert=\"false\"\n      :start-date=\"'2020-01-01'\"\n      :end-date=\"'2030-12-31'\"\n      @confirm=\"onDateSelect\"\n      @close=\"closeDatePicker\"\n    />\n</template>\n\n<script>\nimport { ref, reactive, onMounted, getCurrentInstance } from 'vue';\nimport DistributionSetting from '../distribution/components/DistributionSetting.vue';\nimport { distributionService } from '/subPackages/merchant-admin-marketing/services/distributionService';\nimport MarketingPromotionActions from '/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue';\n\nexport default {\n  components: {\n    DistributionSetting,\n    MarketingPromotionActions\n  },\n  setup() {\n    // 响应式状态\n    const discountForm = reactive({\n      title: '',\n      startDate: '',\n      endDate: '',\n      status: 'active',\n      rules: [],\n      applicableProducts: '全部商品',\n      canStack: false,\n      perPersonLimit: '3',\n      instructions: '',\n      distributionSettings: {\n        enabled: false,\n        commissionMode: 'percentage',\n        commissions: {\n          level1: '',\n          level2: '',\n          level3: ''\n        },\n        enableLevel3: false\n      }\n    });\n    \n    const showDatePickerDialog = ref(false);\n    const currentDatePicker = ref(''); // 'start' or 'end'\n    const hasMerchantDistribution = ref(false); // 商家是否开通分销功能\n    const tempDiscountId = ref('temp-' + Date.now()); // 临时ID，实际应该从后端获取\n    \n    // 方法\n    function goBack() {\n      uni.navigateBack();\n    }\n    \n    function showDatePicker(type) {\n      currentDatePicker.value = type;\n      showDatePickerDialog.value = true;\n    }\n    \n    function closeDatePicker() {\n      showDatePickerDialog.value = false;\n    }\n    \n    function onDateSelect(e) {\n      const selectedDate = e.fulldate;\n      if (currentDatePicker.value === 'start') {\n        discountForm.startDate = selectedDate;\n      } else {\n        discountForm.endDate = selectedDate;\n      }\n      closeDatePicker();\n    }\n    \n    function onStatusChange(e) {\n      discountForm.status = e.detail.value ? 'active' : 'paused';\n    }\n    \n    function onStackChange(e) {\n      discountForm.canStack = e.detail.value;\n    }\n    \n    function addRule() {\n      discountForm.rules.push({\n        minAmount: '',\n        discountAmount: ''\n      });\n    }\n    \n    function deleteRule(index) {\n      discountForm.rules.splice(index, 1);\n    }\n    \n    function selectProducts() {\n      uni.showActionSheet({\n        itemList: ['全部商品', '指定商品', '指定分类'],\n        success: (res) => {\n          switch(res.tapIndex) {\n            case 0:\n              discountForm.applicableProducts = '全部商品';\n              break;\n            case 1:\n              // 在实际应用中，这里应该打开商品选择页面\n              discountForm.applicableProducts = '指定商品';\n              break;\n            case 2:\n              // 在实际应用中，这里应该打开分类选择页面\n              discountForm.applicableProducts = '指定分类';\n              break;\n          }\n        }\n      });\n    }\n\n    // 更新分销设置\n    function updateDistributionSettings(settings) {\n      discountForm.distributionSettings = settings;\n    }\n    \n    // 检查商家是否开通分销功能\n    async function checkMerchantDistribution() {\n      try {\n        // 获取商家信息\n        const instance = getCurrentInstance();\n        const merchantInfo = instance.proxy.$store.state.merchant.merchantInfo || {};\n        \n        // 检查是否开通分销功能\n        hasMerchantDistribution.value = await distributionService.checkMerchantDistribution(merchantInfo);\n        \n        // 如果开通了分销功能，获取默认设置\n        if (hasMerchantDistribution.value) {\n          const settings = await distributionService.getMerchantDistributionSettings(merchantInfo);\n          discountForm.distributionSettings = settings;\n        }\n      } catch (error) {\n        console.error('检查分销功能失败', error);\n        hasMerchantDistribution.value = false;\n      }\n    }\n    \n    function validateForm() {\n      if (!discountForm.title.trim()) {\n        uni.showToast({\n          title: '请输入活动名称',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (!discountForm.startDate || !discountForm.endDate) {\n        uni.showToast({\n          title: '请选择活动时间',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      if (discountForm.rules.length === 0) {\n        uni.showToast({\n          title: '请添加至少一条满减规则',\n          icon: 'none'\n        });\n        return false;\n      }\n      \n      for (const rule of discountForm.rules) {\n        if (!rule.minAmount || !rule.discountAmount) {\n          uni.showToast({\n            title: '请完善满减规则',\n            icon: 'none'\n          });\n          return false;\n        }\n        \n        if (parseFloat(rule.discountAmount) >= parseFloat(rule.minAmount)) {\n          uni.showToast({\n            title: '优惠金额不能大于等于满减金额',\n            icon: 'none'\n          });\n          return false;\n        }\n      }\n      \n      return true;\n    }\n    \n    // 保存满减活动\n    async function saveDiscount() {\n      if (!validateForm()) {\n        return;\n      }\n      \n      // 在实际应用中，这里应该调用API保存数据\n      \n      uni.showLoading({\n        title: '保存中...'\n      });\n\n      // 处理分销设置\n      if (hasMerchantDistribution.value && discountForm.distributionSettings.enabled) {\n        // 验证分销设置\n        const { valid, errors } = distributionService.validateDistributionSettings(discountForm.distributionSettings);\n        if (!valid) {\n          uni.showToast({\n            title: errors[0],\n            icon: 'none'\n          });\n          return;\n        }\n        \n        // 保存分销设置\n        const success = await distributionService.saveActivityDistributionSettings('discount', 'temp-id-123', discountForm.distributionSettings);\n        if (!success) {\n          return;\n        }\n      }\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        \n        uni.showToast({\n          title: '创建成功',\n          icon: 'success'\n        });\n        \n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      }, 1000);\n    }\n    \n    // 处理推广操作完成事件\n    const handlePromotionCompleted = (data) => {\n      console.log('推广操作完成:', data);\n      // 根据不同操作类型处理结果\n      if (data.action === 'publish') {\n        uni.showToast({\n          title: '发布成功',\n          icon: 'success'\n        });\n      } else if (data.action === 'top') {\n        uni.showToast({\n          title: '置顶成功',\n          icon: 'success'\n        });\n      } else if (data.action === 'refresh') {\n        uni.showToast({\n          title: '刷新成功',\n          icon: 'success'\n        });\n      }\n    };\n    \n    onMounted(() => {\n      // 默认添加一条空规则\n      addRule();\n      checkMerchantDistribution();\n    });\n    \n    return {\n      discountForm,\n      showDatePickerDialog,\n      currentDatePicker,\n      goBack,\n      showDatePicker,\n      closeDatePicker,\n      onDateSelect,\n      onStatusChange,\n      onStackChange,\n      addRule,\n      deleteRule,\n      selectProducts,\n      saveDiscount,\n      updateDistributionSettings,\n      checkMerchantDistribution,\n      hasMerchantDistribution,\n      handlePromotionCompleted,\n      tempDiscountId\n    };\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.discount-create-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 80px; /* 为底部按钮留出空间 */\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FDEB71, #F8D800);\n  color: #333;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #333;\n  border-bottom: 2px solid #333;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  padding: 0 10px;\n}\n\n.save-btn {\n  font-size: 16px;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 表单样式 */\n.create-form {\n  padding: 15px;\n}\n\n.form-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.section-desc {\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n.add-rule {\n  font-size: 14px;\n  color: #F8D800;\n}\n\n.form-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.form-item:last-child {\n  border-bottom: none;\n}\n\n.item-label {\n  width: 80px;\n  font-size: 14px;\n  color: #666;\n}\n\n.item-input {\n  flex: 1;\n  height: 24px;\n  font-size: 14px;\n  color: #333;\n}\n\n.input-count {\n  font-size: 12px;\n  color: #999;\n  margin-left: 10px;\n}\n\n.date-picker {\n  flex: 1;\n  height: 36px;\n  background: #f5f5f5;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 10px;\n}\n\n.date-text {\n  font-size: 14px;\n  color: #333;\n}\n\n.date-separator {\n  margin: 0 10px;\n  color: #999;\n}\n\n.status-switch {\n  flex: 1;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.status-text {\n  font-size: 14px;\n  color: #333;\n  margin-right: 10px;\n}\n\n/* 规则样式 */\n.rules-list {\n  \n}\n\n.rule-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n  background: #f9f9f9;\n  border-radius: 8px;\n  padding: 12px;\n}\n\n.rule-inputs {\n  flex: 1;\n  display: flex;\n  align-items: center;\n}\n\n.rule-input-group {\n  display: flex;\n  align-items: center;\n  background: #fff;\n  border-radius: 6px;\n  padding: 8px 12px;\n  border: 1px solid #eee;\n}\n\n.input-prefix {\n  font-size: 14px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.rule-input {\n  width: 80px;\n  font-size: 14px;\n  color: #333;\n  text-align: center;\n}\n\n.input-suffix {\n  font-size: 14px;\n  color: #666;\n  margin-left: 5px;\n}\n\n.rule-separator {\n  margin: 0 10px;\n  color: #666;\n  font-size: 14px;\n}\n\n.rule-delete {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 10px;\n}\n\n.delete-icon {\n  font-size: 18px;\n  color: #999;\n}\n\n.empty-rules {\n  padding: 30px 0;\n  display: flex;\n  justify-content: center;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 使用设置样式 */\n.item-right {\n  flex: 1;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.item-value {\n  font-size: 14px;\n  color: #333;\n  margin-right: 5px;\n}\n\n.item-arrow {\n  font-size: 14px;\n  color: #999;\n}\n\n.limit-input-group {\n  flex: 1;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.limit-input {\n  width: 60px;\n  font-size: 14px;\n  color: #333;\n  text-align: right;\n}\n\n.textarea-item {\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.item-textarea {\n  width: 100%;\n  height: 100px;\n  font-size: 14px;\n  color: #333;\n  background: #f9f9f9;\n  border-radius: 8px;\n  padding: 10px;\n  margin-top: 10px;\n  box-sizing: border-box;\n}\n\n.textarea-count {\n  align-self: flex-end;\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n/* 分销设置样式 */\n.form-section .distribution-setting {\n  margin-top: 0;\n  padding: 0;\n}\n\n/* 营销建议样式 */\n.tips-list {\n  \n}\n\n.tip-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 12px;\n}\n\n.tip-item:last-child {\n  margin-bottom: 0;\n}\n\n.tip-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n  margin-top: 2px;\n  color: #F8D800;\n}\n\n.tip-text {\n  flex: 1;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.5;\n}\n\n/* 活动推广样式 */\n.form-section .marketing-promotion-container {\n  margin-top: 10px;\n}\n\n/* 底部保存按钮样式 */\n.bottom-save-bar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  padding: 15px;\n  background: #fff;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  z-index: 90;\n}\n\n.save-button {\n  width: 100%;\n  height: 44px;\n  background: linear-gradient(135deg, #FDEB71, #F8D800);\n  color: #333;\n  font-size: 16px;\n  font-weight: 500;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/discount/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "uni", "getCurrentInstance", "distributionService", "onMounted"], "mappings": ";;;AA2OA,MAAK,sBAAuB,MAAW;AAEvC,MAAK,4BAA6B,MAAW;AAE7C,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,QAAQ;AAEN,UAAM,eAAeA,cAAAA,SAAS;AAAA,MAC5B,OAAO;AAAA,MACP,WAAW;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO,CAAE;AAAA,MACT,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,sBAAsB;AAAA,QACpB,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAED,UAAM,uBAAuBC,kBAAI,KAAK;AACtC,UAAM,oBAAoBA,kBAAI,EAAE;AAChC,UAAM,0BAA0BA,kBAAI,KAAK;AACzC,UAAM,iBAAiBA,cAAAA,IAAI,UAAU,KAAK,IAAK,CAAA;AAG/C,aAAS,SAAS;AAChBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,aAAS,eAAe,MAAM;AAC5B,wBAAkB,QAAQ;AAC1B,2BAAqB,QAAQ;AAAA,IAC/B;AAEA,aAAS,kBAAkB;AACzB,2BAAqB,QAAQ;AAAA,IAC/B;AAEA,aAAS,aAAa,GAAG;AACvB,YAAM,eAAe,EAAE;AACvB,UAAI,kBAAkB,UAAU,SAAS;AACvC,qBAAa,YAAY;AAAA,aACpB;AACL,qBAAa,UAAU;AAAA,MACzB;AACA;IACF;AAEA,aAAS,eAAe,GAAG;AACzB,mBAAa,SAAS,EAAE,OAAO,QAAQ,WAAW;AAAA,IACpD;AAEA,aAAS,cAAc,GAAG;AACxB,mBAAa,WAAW,EAAE,OAAO;AAAA,IACnC;AAEA,aAAS,UAAU;AACjB,mBAAa,MAAM,KAAK;AAAA,QACtB,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAEA,aAAS,WAAW,OAAO;AACzB,mBAAa,MAAM,OAAO,OAAO,CAAC;AAAA,IACpC;AAEA,aAAS,iBAAiB;AACxBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS,CAAC,QAAQ;AAChB,kBAAO,IAAI,UAAQ;AAAA,YACjB,KAAK;AACH,2BAAa,qBAAqB;AAClC;AAAA,YACF,KAAK;AAEH,2BAAa,qBAAqB;AAClC;AAAA,YACF,KAAK;AAEH,2BAAa,qBAAqB;AAClC;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAGA,aAAS,2BAA2B,UAAU;AAC5C,mBAAa,uBAAuB;AAAA,IACtC;AAGA,mBAAe,4BAA4B;AACzC,UAAI;AAEF,cAAM,WAAWC,cAAAA;AACjB,cAAM,eAAe,SAAS,MAAM,OAAO,MAAM,SAAS,gBAAgB;AAG1E,gCAAwB,QAAQ,MAAMC,gEAAmB,oBAAC,0BAA0B,YAAY;AAGhG,YAAI,wBAAwB,OAAO;AACjC,gBAAM,WAAW,MAAMA,gEAAAA,oBAAoB,gCAAgC,YAAY;AACvF,uBAAa,uBAAuB;AAAA,QACtC;AAAA,MACA,SAAO,OAAO;AACdF,sBAAc,MAAA,MAAA,SAAA,mFAAA,YAAY,KAAK;AAC/B,gCAAwB,QAAQ;AAAA,MAClC;AAAA,IACF;AAEA,aAAS,eAAe;AACtB,UAAI,CAAC,aAAa,MAAM,QAAQ;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,aAAa,aAAa,CAAC,aAAa,SAAS;AACpDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,MAAM,WAAW,GAAG;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,iBAAW,QAAQ,aAAa,OAAO;AACrC,YAAI,CAAC,KAAK,aAAa,CAAC,KAAK,gBAAgB;AAC3CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,YAAI,WAAW,KAAK,cAAc,KAAK,WAAW,KAAK,SAAS,GAAG;AACjEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAGA,mBAAe,eAAe;AAC5B,UAAI,CAAC,aAAY,GAAI;AACnB;AAAA,MACF;AAIAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,UAAI,wBAAwB,SAAS,aAAa,qBAAqB,SAAS;AAE9E,cAAM,EAAE,OAAO,WAAWE,gEAAmB,oBAAC,6BAA6B,aAAa,oBAAoB;AAC5G,YAAI,CAAC,OAAO;AACVF,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,CAAC;AAAA,YACf,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF;AAGA,cAAM,UAAU,MAAME,gEAAAA,oBAAoB,iCAAiC,YAAY,eAAe,aAAa,oBAAoB;AACvI,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAAA,MACF;AAEA,iBAAW,MAAM;AACfF,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,2BAA2B,CAAC,SAAS;AACzCA,oBAAY,MAAA,MAAA,OAAA,mFAAA,WAAW,IAAI;AAE3B,UAAI,KAAK,WAAW,WAAW;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,iBACQ,KAAK,WAAW,OAAO;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,WAAW,KAAK,WAAW,WAAW;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA;AAGFG,kBAAAA,UAAU,MAAM;AAEd;AACA;IACF,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtfA,GAAG,WAAW,eAAe;"}