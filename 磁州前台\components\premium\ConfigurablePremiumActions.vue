<template>
  <view class="premium-actions-container">
    <!-- 直接显示模式 -->
    <view class="direct-options" v-if="showMode === 'direct'">
      <!-- 发布页面显示的选项 -->
      <template v-if="pageType === 'publish'">
        <!-- 广告发布选项 -->
        <view class="premium-option-card ad-option" @click="selectDirectOption('publish', 'ad')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container">
                <image class="option-icon" src="/static/images/premium/ad-publish.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title">看广告发布</text>
                <text class="option-desc" v-if="isCarpool">看一个广告发布一条信息</text>
                <text class="option-desc" v-else>看一个广告免费发布一天</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag free-tag">
                <text>免费</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 付费发布选项 -->
        <view class="premium-option-card paid-option" @click="selectDirectOption('publish', 'paid')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container paid-icon">
                <image class="option-icon" src="/static/images/premium/paid-publish.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title">付费发布</text>
                <text class="option-desc" v-if="isCarpool">付费1元发布一条信息</text>
                <text class="option-desc" v-else>3天/1周/1个月任选</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag paid-tag">
                <text>付费</text>
              </view>
            </view>
          </view>
        </view>
      </template>

      <!-- 置顶页面显示的选项 -->
      <template v-else-if="pageType === 'top' || pageType === 'merchant_top' || pageType === 'carpool_top' || pageType === 'publish_top' || pageType.endsWith('_top')">
        <!-- 广告置顶选项 -->
        <view class="premium-option-card ad-option" @click="selectDirectOption('top', 'ad')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container">
                <image class="option-icon" src="/static/images/premium/ad-top.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title bold-title">看广告置顶</text>
                <text class="option-desc" v-if="pageType.startsWith('merchant')">免费置顶店铺2小时，排名次于付费</text>
                <text class="option-desc" v-else>免费置顶2小时，排名次于付费</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag free-tag">
                <text>免费</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 付费置顶选项 -->
        <view class="premium-option-card paid-option" @click="selectDirectOption('top', 'paid')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container paid-icon">
                <image class="option-icon" src="/static/images/premium/paid-top.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title bold-title">付费置顶</text>
                <text class="option-desc" v-if="pageType.startsWith('merchant')">获得店铺优先展示位置，排名最靠前</text>
                <text class="option-desc" v-else>获得优先展示位置，排名最靠前</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag paid-tag">
                <text>付费</text>
              </view>
            </view>
          </view>
        </view>
      </template>

      <!-- 刷新页面显示的选项 -->
      <template v-else-if="pageType === 'refresh' || pageType === 'merchant_refresh' || pageType === 'carpool_refresh' || pageType === 'publish_refresh' || pageType.endsWith('_refresh')">
        <!-- 广告刷新选项 -->
        <view class="premium-option-card ad-option" @click="selectDirectOption('refresh', 'ad')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container">
                <image class="option-icon" src="/static/images/premium/ad-refresh.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title">看广告刷新</text>
                <text class="option-desc" v-if="pageType.startsWith('merchant')">刷新店铺信息，提升排名到最新位置</text>
                <text class="option-desc" v-else>刷新后置顶信息排名立刻升到置顶第一位！未置顶的会升到未置顶第一位！</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag free-tag">
                <text>免费</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 付费刷新选项 -->
        <view class="premium-option-card paid-option" @click="selectDirectOption('refresh', 'paid')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container paid-icon">
                <image class="option-icon" src="/static/images/premium/paid-refresh.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title">付费刷新</text>
                <text class="option-desc" v-if="pageType.startsWith('merchant')">付费刷新店铺信息，提升排名</text>
                <text class="option-desc" v-else>刷新后置顶信息排名立刻升到置顶第一位！未置顶的会升到未置顶第一位！</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag paid-tag">
                <text>付费</text>
              </view>
            </view>
          </view>
        </view>
      </template>

      <!-- 商家入驻页面显示的选项 -->
      <template v-else-if="pageType === 'merchant_join'">
        <!-- 看广告入驻选项 -->
        <view class="premium-option-card ad-option" @click="selectDirectOption('join', 'ad')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container">
                <image class="option-icon" src="/static/images/premium/ad-join.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title bold-title">看广告免费入驻</text>
                <text class="option-desc">观看广告获得1个月免费特权</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag free-tag">
                <text>免费</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 付费入驻选项 -->
        <view class="premium-option-card paid-option" @click="selectDirectOption('join', 'paid')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container paid-icon">
                <image class="option-icon" src="/static/images/premium/paid-join.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title bold-title">付费入驻</text>
                <text class="option-desc">解锁全部功能，提升曝光量</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag paid-tag">
                <text>付费</text>
              </view>
            </view>
          </view>
        </view>
      </template>

      <!-- 商家续费页面显示的选项 -->
      <template v-else-if="pageType === 'merchant_renew'">
        <!-- 看广告续费选项 -->
        <view class="premium-option-card ad-option" @click="selectDirectOption('renew', 'ad')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container">
                <image class="option-icon" src="/static/images/premium/ad-renew.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title bold-title">看广告续费</text>
                <text class="option-desc">观看广告延长7天会员</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag free-tag">
                <text>免费</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 付费续费选项 -->
        <view class="premium-option-card paid-option" @click="selectDirectOption('renew', 'paid')">
          <view class="option-inner">
            <view class="option-left">
              <view class="option-icon-container paid-icon">
                <image class="option-icon" src="/static/images/premium/paid-renew.png" mode="aspectFit"></image>
              </view>
              <view class="option-content">
                <text class="option-title bold-title">付费续费</text>
                <text class="option-desc">选择续费时长，享受会员特权</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-tag paid-tag">
                <text>付费</text>
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <!-- 付费选项模态框 -->
    <view class="payment-modal" v-if="showPaymentModal" @click="closePaymentModal">
      <view class="payment-content" @click.stop>
        <view class="payment-header">
          <text class="payment-title">{{ paymentModalConfig.title }}</text>
          <view class="close-btn" @click="closePaymentModal">
            <text>×</text>
          </view>
        </view>

        <view class="payment-body">
          <text class="payment-desc">{{ paymentModalConfig.description }}</text>

          <view class="duration-options">
            <view
              class="duration-item"
              :class="{ active: selectedDuration === option.duration }"
              v-for="option in paymentModalConfig.options"
              :key="option.duration"
              @click="selectDuration(option)"
            >
              <view class="duration-info">
                <text class="duration-text">{{ option.duration }}</text>
                <text class="duration-price">¥{{ option.price }}</text>
              </view>
              <view class="duration-badge" v-if="option.recommended">
                <text>推荐</text>
              </view>
            </view>
          </view>
        </view>

        <view class="payment-footer">
          <view class="payment-btn cancel-btn" @click="closePaymentModal">
            <text>取消</text>
          </view>
          <view class="payment-btn confirm-btn" @click="confirmPayment" :class="{ disabled: !selectedDuration }">
            <text>确认支付</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import {
  getPromotionConfig,
  getButtonConfig,
  getPricingConfig,
  getAdRewardConfig,
  isFeatureEnabled,
  logUserAction
} from '@/utils/promotion-config.js';

// 组件属性
const props = defineProps({
  pageType: {
    type: String,
    default: ''
  },
  showMode: {
    type: String,
    default: 'direct'
  },
  itemData: {
    type: Object,
    default: () => ({})
  }
});

// 组件事件
const emit = defineEmits(['action-completed', 'action-cancelled']);

// 配置数据
const promotionConfig = ref(null);
const configLoaded = ref(false);

// 判断是否为拼车系统发布页面
const isCarpool = computed(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const route = currentPage ? currentPage.route || '' : '';
  return route.includes('carpool');
});

// 付费模态框相关状态
const showPaymentModal = ref(false);
const selectedDuration = ref('');
const currentPaymentAction = ref('');
const paymentModalConfig = ref({
  title: '',
  description: '',
  options: []
});

// 直接选择选项
const selectDirectOption = (action, optionType) => {
  console.log('选择操作:', action, optionType);

  // 创建选项对象
  let option = null;

  if (action === 'publish') {
    if (optionType === 'ad') {
      if (isCarpool.value) {
        option = {
          title: '免费发布',
          subtitle: '观看15秒广告发布一条信息',
          price: '免费',
          icon: '/static/images/premium/ad-publish.png',
          type: 'ad',
          duration: '一条信息'
        };
      } else {
        option = {
          title: '免费发布',
          subtitle: '观看15秒广告后发布',
          price: '免费',
          icon: '/static/images/premium/ad-publish.png',
          type: 'ad',
          duration: '1天'
        };
      }
    }
  } else if (action === 'top') {
    if (optionType === 'ad') {
      option = {
        title: '广告置顶',
        subtitle: '观看30秒广告获得2小时置顶',
        price: '免费',
        icon: '/static/images/premium/ad-top.png',
        type: 'ad',
        duration: '2小时'
      };
    }
  } else if (action === 'refresh') {
    if (optionType === 'ad') {
      option = {
        title: '广告刷新',
        subtitle: '观看15秒广告刷新一次',
        price: '免费',
        icon: '/static/images/premium/ad-refresh.png',
        type: 'ad'
      };
    }
  } else if (action === 'join') {
    if (optionType === 'ad') {
      option = {
        title: '看广告入驻',
        subtitle: '观看30秒广告获得1个月免费特权',
        price: '免费',
        icon: '/static/images/premium/ad-join.png',
        type: 'ad',
        duration: '1个月'
      };
    }
  } else if (action === 'renew') {
    if (optionType === 'ad') {
      option = {
        title: '看广告续费',
        subtitle: '观看30秒广告延长7天会员',
        price: '免费',
        icon: '/static/images/premium/ad-renew.png',
        type: 'ad',
        duration: '7天'
      };
    }
  }

  // 处理不同类型的操作
  if (optionType === 'ad') {
    if (option) {
      showAd(option, action);
    }
  } else if (optionType === 'paid') {
    // 付费选项直接显示模态框
    openPaymentModal(action);
  }
};

// 显示广告
const showAd = (option, action) => {
  uni.showLoading({
    title: '正在加载广告...'
  });

  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '广告观看完成',
      icon: 'success'
    });

    // 发送完成事件
    emit('action-completed', {
      action: action,
      type: 'ad',
      data: option
    });
  }, 2000);
};

// 显示付费模态框
const openPaymentModal = async (action) => {
  currentPaymentAction.value = action;
  selectedDuration.value = '';

  try {
    // 从后台获取价格配置
    const pricingOptions = await getPricingConfig(action);

    // 根据不同操作配置模态框
    let title = '选择时长';
    let description = '选择您需要的时长';

    if (action === 'publish') {
      title = '选择发布时长';
      description = '选择您希望发布的时长';
    } else if (action === 'top') {
      title = '选择置顶时长';
      description = '选择您希望置顶的时长';
    } else if (action === 'refresh') {
      title = '选择刷新套餐';
      description = '选择您需要的刷新次数';
    } else if (action === 'join') {
      title = '选择入驻时长';
      description = '选择您的商家会员时长';
    } else if (action === 'renew') {
      title = '选择续费时长';
      description = '选择您要续费的时长';
    }

    paymentModalConfig.value = {
      title,
      description,
      options: pricingOptions || []
    };

    showPaymentModal.value = true;

    // 记录用户操作
    logUserAction('open_payment_modal', {
      action,
      pageType: props.pageType,
      itemId: props.itemData?.id
    });

  } catch (error) {
    console.error('获取价格配置失败:', error);
    uni.showToast({
      title: '获取价格信息失败',
      icon: 'none'
    });
  }
};

// 选择时长
const selectDuration = (option) => {
  selectedDuration.value = option.duration;
};

// 关闭付费模态框
const closePaymentModal = () => {
  showPaymentModal.value = false;
  selectedDuration.value = '';

  // 发送取消事件
  if (currentPaymentAction.value) {
    emit('action-cancelled', {
      action: currentPaymentAction.value,
      type: 'payment',
      reason: 'user_cancelled'
    });
  }

  currentPaymentAction.value = '';
};

// 确认支付
const confirmPayment = () => {
  if (!selectedDuration.value) {
    uni.showToast({
      title: '请选择时长',
      icon: 'none'
    });
    return;
  }

  // 找到选中的选项
  const selectedOption = paymentModalConfig.value.options.find(
    option => option.duration === selectedDuration.value
  );

  if (!selectedOption) return;

  // 关闭模态框
  showPaymentModal.value = false;

  // 模拟支付
  uni.showLoading({
    title: '正在支付...'
  });

  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '支付成功',
      icon: 'success'
    });

    // 发送完成事件
    emit('action-completed', {
      action: currentPaymentAction.value,
      type: 'payment',
      data: {
        title: `付费${currentPaymentAction.value === 'publish' ? '发布' :
                     currentPaymentAction.value === 'top' ? '置顶' :
                     currentPaymentAction.value === 'refresh' ? '刷新' :
                     currentPaymentAction.value === 'join' ? '入驻' : '续费'}`,
        duration: selectedOption.duration,
        price: selectedOption.price,
        type: 'payment'
      }
    });

    // 重置状态
    selectedDuration.value = '';
    currentPaymentAction.value = '';
  }, 1500);
};

// 初始化配置
const initConfig = async () => {
  try {
    // 检查功能是否启用
    const enabled = await isFeatureEnabled(props.pageType);
    if (!enabled) {
      console.log('推广功能已禁用:', props.pageType);
      return;
    }

    // 加载完整配置
    promotionConfig.value = await getPromotionConfig();
    configLoaded.value = true;

    console.log('推广配置加载完成:', promotionConfig.value);
  } catch (error) {
    console.error('初始化推广配置失败:', error);
    configLoaded.value = true; // 即使失败也标记为已加载，使用默认配置
  }
};

// 组件挂载时初始化
onMounted(() => {
  initConfig();
});
</script>

<style lang="scss" scoped>
.premium-actions-container {
  width: 100%;
  margin: 20rpx 0;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;

  /* 添加容器动画 */
  animation: fadeInUp 0.3s ease-out;
}

/* 容器动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.direct-options {
  padding: 0;
}

.premium-option-card {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0); /* 启用硬件加速 */
}

/* 触摸反馈 */
.premium-option-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 悬停效果（适用于支持的设备） */
@media (hover: hover) {
  .premium-option-card:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
  }
}

.option-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
}

.option-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.option-icon-container {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  transition: transform 0.3s ease;
}

.option-icon-container.paid-icon {
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
}

.option-icon-container:active {
  transform: scale(0.9);
}

.option-icon {
  width: 24rpx;
  height: 24rpx;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.option-title.bold-title {
  font-weight: 700;
}

.option-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  line-height: 1.4;
}

.option-right {
  display: flex;
  align-items: center;
}

.option-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.free-tag {
  background: linear-gradient(135deg, #4A90E2, #3B7DFC);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.3);
}

.paid-tag {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.option-tag:active {
  transform: scale(0.95);
}

/* 广告选项特殊样式 */
.ad-option {
  border-left: 4rpx solid #4A90E2;
}

.ad-option .option-icon-container {
  background: linear-gradient(135deg, #4A90E2, #3B7DFC);
}

/* 付费选项特殊样式 */
.paid-option {
  border-left: 4rpx solid #FF6B6B;
}

.paid-option .option-icon-container {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
}

/* 付费模态框样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

.payment-content {
  width: 90%;
  max-width: 650rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
}

.payment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.payment-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  color: #666;
  font-size: 40rpx;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.payment-body {
  padding: 30rpx;
}

.payment-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.duration-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.duration-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  background: white;
  transition: all 0.3s ease;
  position: relative;
}

.duration-item:active {
  transform: scale(0.98);
}

.duration-item.active {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #f8faff, #eef2ff);
  box-shadow: 0 4rpx 15rpx rgba(79, 70, 229, 0.15);
}

.duration-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.duration-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.duration-price {
  font-size: 40rpx;
  font-weight: 700;
  color: #ff6b6b;
}

.duration-badge {
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e53);
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.payment-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.payment-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.confirm-btn {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(79, 70, 229, 0.3);
}

.confirm-btn:active {
  transform: scale(0.98);
}

.confirm-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
  box-shadow: none;
  pointer-events: none;
}

/* 模态框动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .option-inner {
    padding: 16rpx;
  }

  .option-icon-container {
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
  }

  .option-icon {
    width: 20rpx;
    height: 20rpx;
  }

  .option-title {
    font-size: 28rpx;
  }

  .option-desc {
    font-size: 24rpx;
  }

  .option-tag {
    padding: 6rpx 12rpx;
    font-size: 22rpx;
  }

  .payment-content {
    width: 95%;
  }

  .payment-header,
  .payment-body,
  .payment-footer {
    padding: 20rpx;
  }

  .duration-item {
    padding: 20rpx;
  }

  .duration-text {
    font-size: 28rpx;
  }

  .duration-price {
    font-size: 36rpx;
  }

  .payment-btn {
    height: 80rpx;
    font-size: 28rpx;
  }
}
</style>