/**
 * 推广服务
 * 处理各类内容的推广功能
 */

const promotionService = {
  /**
   * 打开推广工具
   * @param {string} type - 推广类型
   * @param {object} data - 推广数据
   */
  showPromotionTools(type, data) {
    if (!type || !data || !data.id) {
      uni.showToast({
        title: '推广数据不完整',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到推广工具页面
    uni.navigateTo({
      url: `/subPackages/promotion/pages/promotion-tool?type=${type}&id=${data.id}`
    });
  },
  
  /**
   * 生成分享链接
   * @param {string} type - 推广类型
   * @param {string} id - 内容ID
   * @param {string} [userId] - 推广用户ID，可选
   * @returns {string} 分享链接
   */
  generateShareLink(type, id, userId) {
    const baseUrl = 'https://cizhou.life';
    let path = '';
    
    switch (type) {
      case 'carpool':
        path = `/pages/carpool/detail?id=${id}`;
        break;
      case 'product':
        path = `/pages/product/detail?id=${id}`;
        break;
      case 'house':
        path = `/pages/house/detail?id=${id}`;
        break;
      case 'service':
        path = `/pages/service/detail?id=${id}`;
        break;
      case 'merchant':
        path = `/pages/business/shop-detail?id=${id}`;
        break;
      case 'content':
        path = `/pages/content/detail?id=${id}`;
        break;
      case 'activity':
        path = `/pages/activity/detail?id=${id}`;
        break;
      case 'community':
        path = `/pages/community/detail?id=${id}`;
        break;
      default:
        path = `/pages/index/index?id=${id}&type=${type}`;
    }
    
    // 添加推广用户ID
    if (userId) {
      path += `&promoter=${userId}`;
    }
    
    return `${baseUrl}${path}`;
  },
  
  /**
   * 生成小程序码
   * @param {string} type - 推广类型
   * @param {string} id - 内容ID
   * @param {string} [userId] - 推广用户ID，可选
   * @returns {Promise<string>} 小程序码临时路径
   */
  async generateQrcode(type, id, userId) {
    return new Promise((resolve, reject) => {
      uni.showLoading({
        title: '生成中...'
      });
      
      // 构建参数
      const scene = userId ? `id=${id}&u=${userId}` : `id=${id}`;
      const page = this._getPagePath(type);
      
      // 调用云函数生成小程序码
      uni.request({
        url: 'https://api.cizhou.life/api/promotion/qrcode',
        method: 'POST',
        data: {
          scene,
          page,
          width: 280
        },
        success: (res) => {
          uni.hideLoading();
          if (res.data && res.data.code === 0 && res.data.data) {
            resolve(res.data.data.tempFilePath);
          } else {
            uni.showToast({
              title: '生成小程序码失败',
              icon: 'none'
            });
            reject(new Error('生成小程序码失败'));
          }
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },
  
  /**
   * 生成海报
   * @param {string} type - 推广类型
   * @param {object} data - 推广数据
   * @param {string} userId - 推广用户ID
   * @param {object} options - 自定义选项
   * @returns {Promise<string>} 海报临时路径
   */
  async generatePoster(type, data, userId, options = {}) {
    return new Promise((resolve, reject) => {
      try {
        uni.showLoading({
          title: '生成海报中...'
        });
        
        // 创建画布上下文
        const ctx = uni.createCanvasContext('posterCanvas');
        
        // 应用自定义主题颜色
        const theme = options.theme || { bgColor: '#FFFFFF', textColor: '#333333' };
        
        // 绘制背景
        ctx.setFillStyle(theme.bgColor);
        ctx.fillRect(0, 0, 600, 900);
        
        // 绘制顶部渐变背景
        const grd = ctx.createLinearGradient(0, 0, 600, 150);
        if (theme.bgColor === '#FFFFFF' || theme.bgColor === '#F5F7FA') {
          // 默认蓝色渐变
          grd.addColorStop(0, '#3846cd');
          grd.addColorStop(1, '#2c3aa0');
        } else {
          // 使用主题颜色的深浅变化
          grd.addColorStop(0, theme.bgColor);
          grd.addColorStop(1, this._darkenColor(theme.bgColor, 20));
        }
        ctx.setFillStyle(grd);
        ctx.fillRect(0, 0, 600, 150);
        
        // 绘制磁州生活网Logo
        ctx.setFillStyle('#ffffff');
        ctx.setFontSize(28);
        ctx.fillText('磁州生活网', 40, 60);
        
        // 绘制标题
        ctx.setFillStyle(theme.textColor);
        ctx.setFontSize(36);
        ctx.fillText(this._truncateText(data.title || '内容标题', 16), 40, 200);
        
        // 绘制图片占位区域
        ctx.setFillStyle('#f5f5f5');
        ctx.fillRect(40, 240, 520, 300);
        
        // 绘制图片中心的图标
        ctx.setFillStyle('#cccccc');
        ctx.fillRect(270, 360, 60, 60);
        ctx.setFillStyle('#ffffff');
        ctx.fillRect(290, 380, 20, 20);
        
        // 绘制内容信息
        ctx.setFillStyle(theme.textColor === '#FFFFFF' ? '#DDDDDD' : '#666666');
        ctx.setFontSize(28);
        
        let yPosition = 580;
        
        // 根据不同类型绘制不同内容
        switch (type) {
          case 'carpool':
            ctx.fillText(`出发: ${data.departure || '起点'}`, 40, yPosition);
            yPosition += 50;
            ctx.fillText(`目的地: ${data.destination || '终点'}`, 40, yPosition);
            yPosition += 50;
            ctx.fillText(`时间: ${data.departureTime || '出发时间'}`, 40, yPosition);
            break;
            
          case 'product':
            ctx.fillText(`价格: ¥${data.price || '0.00'}`, 40, yPosition);
            if (data.originalPrice) {
              ctx.setFillStyle('#999999');
              ctx.fillText(`原价: ¥${data.originalPrice}`, 240, yPosition);
              ctx.setFillStyle(theme.textColor === '#FFFFFF' ? '#DDDDDD' : '#666666');
            }
            yPosition += 50;
            if (data.description) {
              this._drawWrappedText(ctx, data.description, 40, yPosition, 520, 28, 2);
              yPosition += 70;
            }
            break;
            
          case 'house':
            ctx.fillText(`价格: ${data.price || '0'}${data.priceUnit || ''}`, 40, yPosition);
            yPosition += 50;
            ctx.fillText(`${data.roomType || '户型'} | ${data.area || '0'}㎡`, 40, yPosition);
            yPosition += 50;
            ctx.fillText(`位置: ${data.location || '地址'}`, 40, yPosition);
            break;
            
          case 'merchant':
            ctx.fillText(`类型: ${data.category || '商家类型'}`, 40, yPosition);
            yPosition += 50;
            ctx.fillText(`地址: ${data.address || '商家地址'}`, 40, yPosition);
            break;
            
          default:
            if (data.description) {
              this._drawWrappedText(ctx, data.description, 40, yPosition, 520, 28, 3);
              yPosition += 100;
            } else {
              ctx.fillText('内容描述', 40, yPosition);
              yPosition += 50;
            }
        }
        
        // 添加自定义文案
        if (options.customText) {
          yPosition += 50;
          ctx.setFillStyle(theme.textColor);
          ctx.setFontSize(30);
          this._drawWrappedText(ctx, options.customText, 40, yPosition, 520, 40, 2);
          yPosition += 80;
        }
        
        // 绘制二维码区域
        yPosition = Math.max(yPosition + 30, 700);
        
        // 绘制二维码背景
        ctx.setFillStyle(theme.bgColor === '#FFFFFF' ? '#f8f8f8' : this._lightenColor(theme.bgColor, 10));
        ctx.fillRect(40, yPosition, 520, 160);
        
        // 绘制二维码占位
        ctx.setFillStyle('#DDDDDD');
        ctx.fillRect(420, yPosition + 10, 120, 120);
        
        // 绘制二维码中心的小图标
        ctx.setFillStyle('#AAAAAA');
        ctx.fillRect(460, yPosition + 50, 40, 40);
        
        // 绘制二维码提示文字
        ctx.setFillStyle(theme.textColor);
        ctx.setFontSize(24);
        ctx.fillText('扫码查看详情', 60, yPosition + 50);
        
        ctx.setFillStyle(theme.textColor === '#FFFFFF' ? '#CCCCCC' : '#666666');
        ctx.setFontSize(20);
        ctx.fillText('长按识别小程序码', 60, yPosition + 90);
        
        // 显示推广员ID
        if (options.showId && userId) {
          ctx.setFillStyle(theme.textColor === '#FFFFFF' ? '#CCCCCC' : '#666666');
          ctx.setFontSize(18);
          ctx.fillText(`推广员ID: ${userId}`, 60, yPosition + 130);
        }
        
        // 绘制底部版权信息
        ctx.setFillStyle('#999999');
        ctx.setFontSize(20);
        ctx.fillText('来自磁州生活网', 240, 880);
        
        // 绘制完成
        ctx.draw(false, () => {
          setTimeout(() => {
            uni.canvasToTempFilePath({
              canvasId: 'posterCanvas',
              success: (res) => {
                uni.hideLoading();
                resolve(res.tempFilePath);
                
                // 记录生成海报事件
                this.recordPromotion(type, data.id || '0', 'poster');
              },
              fail: (err) => {
                uni.hideLoading();
                console.error('生成海报失败', err);
                
                // 如果生成失败，使用模板图片
                if (options.template) {
                  resolve(options.template);
                } else {
                  // 根据类型返回不同的模拟海报路径
                  let posterPath;
                  switch (type) {
                    case 'carpool':
                      posterPath = '/static/images/distribution/carpool-poster.png';
                      break;
                    case 'product':
                      posterPath = '/static/images/distribution/product-poster.png';
                      break;
                    case 'house':
                      posterPath = '/static/images/distribution/house-poster.png';
                      break;
                    case 'merchant':
                      posterPath = '/static/images/distribution/merchant-poster.png';
                      break;
                    case 'service':
                      posterPath = '/static/images/distribution/service-poster.png';
                      break;
                    default:
                      posterPath = '/static/images/distribution/default-poster.png';
                  }
                  resolve(posterPath);
                }
              }
            });
          }, 500); // 给一点时间让Canvas渲染完成
        });
      } catch (err) {
        uni.hideLoading();
        console.error('生成海报失败', err);
        reject(err);
      }
    });
  },
  
  /**
   * 保存海报到相册
   * @param {string} filePath - 海报文件路径
   * @returns {Promise<boolean>} 是否保存成功
   */
  async savePosterToAlbum(filePath) {
    return new Promise((resolve, reject) => {
      uni.saveImageToPhotosAlbum({
        filePath,
        success: () => {
          uni.showToast({
            title: '已保存到相册',
            icon: 'success'
          });
          resolve(true);
        },
        fail: (err) => {
          if (err.errMsg.indexOf('auth deny') > -1) {
            uni.showModal({
              title: '提示',
              content: '需要授权保存到相册',
        success: (res) => {
                if (res.confirm) {
                  uni.openSetting();
                }
              }
            });
          } else {
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
          reject(err);
        }
      });
    });
  },
  
  /**
   * 记录推广数据
   * @param {string} type - 推广类型
   * @param {string} id - 内容ID
   * @param {string} method - 推广方式：link, qrcode, poster
   * @returns {Promise<boolean>} 是否记录成功
   */
  async recordPromotion(type, id, method) {
    try {
      // 在开发环境中，只记录日志，不发送请求
      console.log('记录推广数据', {
        contentType: type,
        contentId: id,
        promotionMethod: method
      });
      return true;
    } catch (err) {
      console.error('记录推广数据失败', err);
      return false;
    }
  },
  
  /**
   * 获取页面路径
   * @private
   * @param {string} type - 推广类型
   * @returns {string} 页面路径
   */
  _getPagePath(type) {
    switch (type) {
      case 'carpool':
        return 'pages/carpool/detail';
      case 'product':
        return 'pages/product/detail';
      case 'house':
        return 'pages/house/detail';
      case 'service':
        return 'pages/service/detail';
      case 'merchant':
        return 'pages/business/shop-detail';
      case 'content':
        return 'pages/content/detail';
      case 'activity':
        return 'pages/activity/detail';
      case 'community':
        return 'pages/community/detail';
      default:
        return 'pages/index/index';
    }
  },
  
  /**
   * 截断文本
   * @private
   * @param {string} text - 原文本
   * @param {number} maxLength - 最大长度
   * @returns {string} 截断后的文本
   */
  _truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  },
  
  /**
   * 绘制自动换行文本
   * @private
   * @param {object} ctx - 画布上下文
   * @param {string} text - 文本内容
   * @param {number} x - x坐标
   * @param {number} y - y坐标
   * @param {number} maxWidth - 最大宽度
   * @param {number} lineHeight - 行高
   * @param {number} maxLines - 最大行数
   */
  _drawWrappedText(ctx, text, x, y, maxWidth, lineHeight, maxLines) {
    if (!text) return;
    
    const chars = text.split('');
    let line = '';
    let lineCount = 0;
    
    for (let i = 0; i < chars.length; i++) {
      const testLine = line + chars[i];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > maxWidth && i > 0) {
        ctx.fillText(line, x, y);
        line = chars[i];
        y += lineHeight;
        lineCount++;
        
        if (lineCount >= maxLines - 1 && i < chars.length - 1) {
          // 达到最大行数，显示省略号
          line += '...';
          break;
        }
      } else {
        line = testLine;
      }
    }
    
    ctx.fillText(line, x, y);
  },
  
  /**
   * 使颜色变深
   * @private
   * @param {string} hex - 十六进制颜色值
   * @param {number} percent - 变深的百分比
   * @returns {string} 变深后的颜色
   */
  _darkenColor(hex, percent) {
    // 移除#号
    hex = hex.replace('#', '');
    
    // 转换为RGB
    let r = parseInt(hex.substring(0, 2), 16);
    let g = parseInt(hex.substring(2, 4), 16);
    let b = parseInt(hex.substring(4, 6), 16);
    
    // 变暗
    r = Math.floor(r * (100 - percent) / 100);
    g = Math.floor(g * (100 - percent) / 100);
    b = Math.floor(b * (100 - percent) / 100);
    
    // 确保值在0-255范围内
    r = (r < 0) ? 0 : ((r > 255) ? 255 : r);
    g = (g < 0) ? 0 : ((g > 255) ? 255 : g);
    b = (b < 0) ? 0 : ((b > 255) ? 255 : b);
    
    // 转换回十六进制
    return '#' + 
      (r.toString(16).padStart(2, '0')) + 
      (g.toString(16).padStart(2, '0')) + 
      (b.toString(16).padStart(2, '0'));
  },
  
  /**
   * 使颜色变亮
   * @private
   * @param {string} hex - 十六进制颜色值
   * @param {number} percent - 变亮的百分比
   * @returns {string} 变亮后的颜色
   */
  _lightenColor(hex, percent) {
    // 移除#号
    hex = hex.replace('#', '');
    
    // 转换为RGB
    let r = parseInt(hex.substring(0, 2), 16);
    let g = parseInt(hex.substring(2, 4), 16);
    let b = parseInt(hex.substring(4, 6), 16);
    
    // 变亮
    r = Math.floor(r + (255 - r) * percent / 100);
    g = Math.floor(g + (255 - g) * percent / 100);
    b = Math.floor(b + (255 - b) * percent / 100);
    
    // 确保值在0-255范围内
    r = (r < 0) ? 0 : ((r > 255) ? 255 : r);
    g = (g < 0) ? 0 : ((g > 255) ? 255 : g);
    b = (b < 0) ? 0 : ((b > 255) ? 255 : b);
    
    // 转换回十六进制
    return '#' + 
      (r.toString(16).padStart(2, '0')) + 
      (g.toString(16).padStart(2, '0')) + 
      (b.toString(16).padStart(2, '0'));
  }
};

export default promotionService; 