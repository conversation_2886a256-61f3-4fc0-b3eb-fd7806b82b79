/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 使用系统默认字体 */
.publish-container {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 替换所有使用阿里妈妈字体的地方 */
.title, .heading, .subtitle, .publish-title, .section-title {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-weight: bold;
  /* 使用系统字体的粗体代替阿里妈妈黑体 */
}
.publish-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  position: relative;
  padding-bottom: 30rpx;
  padding-top: 44px;
  /* 为状态栏预留空间 */
}

/* 内容区滚动视图 */
.content-scroll {
  flex: 1;
  box-sizing: border-box;
  padding-top: 150rpx;
  /* 增加顶部内边距，为固定导航栏留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.navbar-left {
  position: absolute;
  left: 20rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 48rpx;
  height: 48rpx;
  display: block;
}
.navbar-right {
  position: absolute;
  right: 20rpx;
  display: flex;
}
.navbar-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}
.navbar-btn:active {
  background-color: rgba(255, 255, 255, 0.2);
}
.navbar-btn-icon {
  width: 44rpx;
  height: 44rpx;
}

/* 免责声明 */
.disclaimer {
  padding: 20rpx 30rpx 10rpx 30rpx;
  margin: 10rpx 20rpx 25rpx;
  /* 去掉背景卡片 */
  /* background: #fff; */
  /* border-radius: 10rpx; */
}
.disclaimer-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.disclaimer-item {
  display: flex;
  align-items: flex-start;
  font-size: 24rpx;
  color: #444;
  line-height: 1.7;
}
.disclaimer-index {
  font-weight: bold;
  margin-right: 8rpx;
  color: #0052CC;
  font-size: 26rpx;
  flex-shrink: 0;
}
.disclaimer-content {
  flex: 1;
  color: #999;
}
.disclaimer-note {
  margin-top: 12rpx;
  font-size: 20rpx;
  color: #999;
  text-align: left;
}

/* 入驻卡片 */
.entry-cards {
  padding: 0 30rpx;
  margin-bottom: 15rpx;
  position: relative;
  z-index: 5;
}
.entry-card {
  margin-bottom: 15rpx;
  background: #EEF1F6;
  border-radius: 16rpx;
  box-shadow: 10rpx 10rpx 20rpx rgba(174, 184, 210, 0.8), -10rpx -10rpx 20rpx rgba(255, 255, 255, 0.9), inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 20rpx 20rpx 20rpx;
  position: relative;
  z-index: 2;
  border: none;
  overflow: hidden;
  transition: all 0.2s ease;
}
.entry-card:active {
  transform: scale(0.98);
  box-shadow: 6rpx 6rpx 12rpx rgba(174, 184, 210, 0.8), -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.9), inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);
}
.entry-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
  z-index: 0;
}
.join-card-left {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}
.join-icon-wrap {
  width: 70rpx;
  height: 70rpx;
  background: #EEF1F6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  box-shadow: 5rpx 5rpx 10rpx rgba(174, 184, 210, 0.6), -5rpx -5rpx 10rpx rgba(255, 255, 255, 0.9), inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.4);
  position: relative;
  z-index: 3;
  border: none;
  transition: all 0.2s ease;
}
.personal-icon-wrap {
  background: #EEF1F6;
}
.join-icon {
  width: 42rpx;
  height: 42rpx;
  opacity: 1;
  filter: none;
}
.join-info {
  display: flex;
  flex-direction: column;
}
.join-title {
  color: #3D56C1;
  font-size: 28rpx;
  font-weight: 700;
  margin-bottom: 6rpx;
  text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
  font-family: "AlimamaShuHeiTi", sans-serif;
  letter-spacing: 1rpx;
}
.personal-title {
  color: #4a89dc;
}
.join-desc {
  color: #5F6A8A;
  font-size: 22rpx;
  text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);
}
.join-btn {
  background: linear-gradient(145deg, #394FC2, #4A67D7);
  color: #ffffff;
  font-size: 24rpx;
  padding: 0 24rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  margin: 0;
  position: relative;
  z-index: 2;
  box-shadow: 5rpx 5rpx 10rpx rgba(61, 86, 193, 0.3), -2rpx -2rpx 8rpx rgba(255, 255, 255, 0.3), inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);
  font-weight: 600;
  border: none;
  transition: all 0.2s ease;
  font-family: "AlimamaShuHeiTi", sans-serif;
  text-align: center;
  letter-spacing: 1px;
}
.join-btn:active {
  transform: scale(0.96);
  box-shadow: 2rpx 2rpx 4rpx rgba(61, 86, 193, 0.3), -1rpx -1rpx 4rpx rgba(255, 255, 255, 0.3), inset 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.1);
}
.personal-join-btn {
  background: linear-gradient(145deg, #3e7ece, #4a89dc);
  box-shadow: 5rpx 5rpx 10rpx rgba(74, 137, 220, 0.3), -2rpx -2rpx 8rpx rgba(255, 255, 255, 0.3), inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);
}
.hot-badge {
  position: absolute;
  top: -2rpx;
  right: 6rpx;
  background: #FF5757;
  color: #ffffff;
  font-size: 18rpx;
  font-weight: bold;
  padding: 4rpx 10rpx;
  border-radius: 0 0 10rpx 10rpx;
  box-shadow: 0 3rpx 6rpx rgba(255, 87, 87, 0.4);
  display: block;
}

/* 发布类目 */
.category-section {
  margin: 0 20rpx 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 20rpx 25rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.6s ease-in-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
}
.category-section-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  padding: 8rpx 0 16rpx;
  position: relative;
  display: flex;
  align-items: center;
}
.category-section-title::before {
  content: "";
  display: block;
  width: 8rpx;
  height: 32rpx;
  background-color: #0052CC;
  border-radius: 4rpx;
  margin-right: 16rpx;
}
.category-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.category-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 0;
  position: relative;
  transition: all 0.2s;
  animation: fadeIn 0.3s ease-in-out;
  animation-fill-mode: both;
}

/* 简化延迟动画 */
.category-item:nth-child(1) {
  animation-delay: 0.1s;
}
.category-item:nth-child(2) {
  animation-delay: 0.1s;
}
.category-item:nth-child(3) {
  animation-delay: 0.1s;
}
.category-item:nth-child(4) {
  animation-delay: 0.1s;
}
.category-item:nth-child(5) {
  animation-delay: 0.1s;
}
.category-item:nth-child(6) {
  animation-delay: 0.15s;
}
.category-item:nth-child(7) {
  animation-delay: 0.15s;
}
.category-item:nth-child(8) {
  animation-delay: 0.15s;
}
.category-item:nth-child(9) {
  animation-delay: 0.15s;
}
.category-item:nth-child(10) {
  animation-delay: 0.15s;
}
.category-item:nth-child(11) {
  animation-delay: 0.2s;
}
.category-item:nth-child(12) {
  animation-delay: 0.2s;
}
.category-item:nth-child(13) {
  animation-delay: 0.2s;
}
.category-item:nth-child(14) {
  animation-delay: 0.2s;
}
.category-item:nth-child(15) {
  animation-delay: 0.2s;
}
.category-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  border-radius: 0;
}
.category-item-hover {
  transform: scale(0.95);
  background-color: rgba(0, 0, 0, 0.03);
}
.category-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 8rpx;
  position: relative;
  z-index: 2;
  transition: all 0.2s;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  overflow: hidden;
}
.category-item-hover .category-icon-wrapper {
  transform: scale(0.9);
}
.category-icon {
  width: 75rpx;
  height: 75rpx;
}
.category-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  position: relative;
  z-index: 2;
  font-weight: 500;
  margin-top: 6rpx;
}
.test-icon-wrapper {
  background-color: rgba(255, 107, 107, 0.1);
  border: 2rpx dashed #ff6b6b;
}
.test-icon {
  font-size: 32rpx;
  color: #ff4f4f;
  font-weight: 600;
}
.test-category-item {
  position: relative;
}
.test-badge {
  position: absolute;
  top: 6rpx;
  right: 8rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff4f4f);
  color: white;
  font-size: 16rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.3);
  font-weight: 500;
  z-index: 3;
}

/* 商家入驻测试按钮样式 */
.merchant-test-category-item {
  position: relative;
}
.merchant-test-icon-wrapper {
  background-color: #0052CC;
}
.merchant-test-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4d4f;
  color: #ffffff;
  font-size: 10px;
  height: 16px;
  min-width: 16px;
  line-height: 16px;
  text-align: center;
  border-radius: 8px;
  padding: 0 4px;
  font-weight: bold;
}