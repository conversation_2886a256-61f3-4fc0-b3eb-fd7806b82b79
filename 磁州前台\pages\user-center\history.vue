<template>
  <view class="history-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">浏览记录</view>
      <view class="navbar-right" @click="clearHistory">
        <text class="clear-text">清空</text>
      </view>
    </view>
    
    <!-- 浏览记录列表 -->
    <view class="history-content" :style="{ paddingTop: navbarHeight + 'px' }">
      <view v-if="historyList.length > 0" class="history-list">
        <!-- 日期分组 -->
        <view class="date-group" v-for="(group, date) in groupedHistory" :key="date">
          <view class="date-header">
            <text class="date-text">{{ formatDate(date) }}</text>
          </view>
          
          <!-- 浏览记录项 -->
          <view class="history-item" v-for="(item, index) in group" :key="index" @click="viewDetail(item)">
            <view class="history-left">
              <image class="history-image" :src="item.image" mode="aspectFill"></image>
            </view>
            <view class="history-right">
              <view class="history-title">{{ item.title }}</view>
              <view class="history-desc">{{ item.desc }}</view>
              <view class="history-meta">
                <text class="history-type">{{ item.type }}</text>
                <text class="history-time">{{ formatTime(item.time) }}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 底部提示 -->
        <view class="list-bottom">没有更多了</view>
      </view>
      <view v-else class="empty-view">
        <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
        <view class="empty-text">暂无浏览记录</view>
      </view>
    </view>
    
    <!-- 清空确认弹窗 -->
    <view class="clear-popup" v-if="showClearConfirm" @click="cancelClear">
      <view class="clear-dialog" @click.stop>
        <view class="clear-title">确认清空浏览记录？</view>
        <view class="clear-desc">清空后将无法恢复</view>
        <view class="clear-actions">
          <view class="clear-btn cancel" @click="cancelClear">取消</view>
          <view class="clear-btn confirm" @click="confirmClear">确认清空</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64, // 导航栏高度
      historyList: [],
      showClearConfirm: false
    }
  },
  computed: {
    // 按日期分组
    groupedHistory() {
      const groups = {};
      
      this.historyList.forEach(item => {
        // 提取日期部分
        const date = new Date(item.time).toISOString().slice(0, 10);
        
        if (!groups[date]) {
          groups[date] = [];
        }
        
        groups[date].push(item);
      });
      
      return groups;
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 加载浏览记录
    this.loadHistoryData();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 加载浏览记录数据
    loadHistoryData() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        this.historyList = [
          // 今天
          {
            id: 'history_1',
            title: '专业家政保洁服务',
            desc: '专业保洁，上门服务，价格实惠',
            image: '/static/images/service1.jpg',
            type: '服务',
            time: new Date().toISOString()
          },
          {
            id: 'history_2',
            title: '黄金地段餐饮店整体转让',
            desc: '位于商业街黄金地段，客流稳定，接手即可盈利',
            image: '/static/images/shop1.jpg',
            type: '生意转让',
            time: new Date().toISOString()
          },
          
          // 昨天
          {
            id: 'history_3',
            title: '求职行政文员',
            desc: '有1年工作经验，熟悉办公软件，可立即上岗',
            image: '/static/images/avatar.png',
            type: '求职信息',
            time: new Date(Date.now() - 86400000).toISOString()
          },
          {
            id: 'history_4',
            title: '餐饮店急招服务员',
            desc: '工作轻松，待遇优厚，提供食宿',
            image: '/static/images/service2.jpg',
            type: '招聘信息',
            time: new Date(Date.now() - 86400000).toISOString()
          },
          
          // 前天
          {
            id: 'history_5',
            title: '市中心两室一厅出租',
            desc: '位于市中心，交通便利，家具家电齐全',
            image: '/static/images/house1.jpg',
            type: '房屋出租',
            time: new Date(Date.now() - 86400000 * 2).toISOString()
          },
          {
            id: 'history_6',
            title: '二手苹果手机出售',
            desc: 'iPhone 13 Pro，128G，成色新，无拆修',
            image: '/static/images/phone.jpg',
            type: '二手闲置',
            time: new Date(Date.now() - 86400000 * 2).toISOString()
          }
        ];
      }, 500);
    },
    
    // 格式化日期显示
    formatDate(dateStr) {
      const date = new Date(dateStr);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (date.toDateString() === today.toDateString()) {
        return '今天';
      } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天';
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
    },
    
    // 格式化时间显示
    formatTime(timeStr) {
      const date = new Date(timeStr);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      return `${hours}:${minutes}`;
    },
    
    // 查看详情
    viewDetail(item) {
      let url = '';
      
      // 根据不同类型跳转到不同页面
      switch(item.type) {
        case '服务':
          url = `/pages/publish/home-service-detail?id=${item.id}`;
          break;
        case '生意转让':
          url = `/pages/publish/business-transfer-detail?id=${item.id}`;
          break;
        case '求职信息':
          url = `/pages/publish/job-seeking-detail?id=${item.id}`;
          break;
        case '招聘信息':
          url = `/pages/publish/job-detail?id=${item.id}`;
          break;
        case '房屋出租':
          url = `/pages/publish/house-rent-detail?id=${item.id}`;
          break;
        case '二手闲置':
          url = `/pages/publish/second-hand-detail?id=${item.id}`;
          break;
        default:
          url = `/pages/publish/info-detail?id=${item.id}`;
      }
      
      uni.navigateTo({ url });
    },
    
    // 清空历史记录
    clearHistory() {
      this.showClearConfirm = true;
    },
    
    // 取消清空
    cancelClear() {
      this.showClearConfirm = false;
    },
    
    // 确认清空
    confirmClear() {
      this.historyList = [];
      this.showClearConfirm = false;
      
      uni.showToast({
        title: '已清空浏览记录',
        icon: 'success'
      });
    }
  }
}
</script>

<style>
.history-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: #0052CC;
  color: #fff;
  z-index: 100;
}

.navbar-left {
  width: 80rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
  text-align: right;
}

.clear-text {
  font-size: 14px;
  color: #fff;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 历史记录内容 */
.history-content {
  padding: 15px;
}

/* 日期分组 */
.date-group {
  margin-bottom: 20px;
}

.date-header {
  padding: 10px 5px;
  margin-bottom: 10px;
}

.date-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 历史记录项 */
.history-item {
  display: flex;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.history-left {
  width: 140rpx;
  height: 140rpx;
  margin-right: 15px;
}

.history-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.history-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.history-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.history-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.history-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.history-type {
  color: #0052CC;
  background-color: rgba(0, 82, 204, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.history-time {
  color: #999;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  color: #999;
}

/* 清空确认弹窗 */
.clear-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 101;
}

.clear-dialog {
  width: 80%;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
}

.clear-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 10px;
}

.clear-desc {
  font-size: 14px;
  color: #999;
  text-align: center;
  margin-bottom: 20px;
}

.clear-actions {
  display: flex;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.clear-btn {
  flex: 1;
  text-align: center;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
}

.cancel {
  color: #999;
}

.confirm {
  color: #ff4d4f;
  font-weight: 500;
}
</style> 