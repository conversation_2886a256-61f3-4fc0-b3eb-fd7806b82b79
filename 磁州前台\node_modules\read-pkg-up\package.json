{"name": "read-pkg-up", "version": "7.0.1", "description": "Read the closest package.json file", "license": "MIT", "repository": "sindresorhus/read-pkg-up", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["json", "read", "parse", "file", "fs", "graceful", "load", "package", "find", "up", "find-up", "findup", "look-up", "look", "search", "match", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}, "devDependencies": {"ava": "^2.4.0", "tsd": "^0.9.0", "xo": "^0.25.3"}}