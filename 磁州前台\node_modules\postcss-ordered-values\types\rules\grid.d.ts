/**
 * @param {import('postcss-value-parser').ParsedValue} gridAutoFlow
 * @return {import('postcss-value-parser').ParsedValue | string}
 */
export function normalizeGridAutoFlow(gridAutoFlow: import('postcss-value-parser').ParsedValue): import('postcss-value-parser').ParsedValue | string;
/**
 * @param {import('postcss-value-parser').ParsedValue} gridGap
 * @return {import('postcss-value-parser').ParsedValue | string}
 */
export function normalizeGridColumnRowGap(gridGap: import('postcss-value-parser').ParsedValue): import('postcss-value-parser').ParsedValue | string;
/**
 * @param {import('postcss-value-parser').ParsedValue} grid
 * @return {string | string[]}
 */
export function normalizeGridColumnRow(grid: import('postcss-value-parser').ParsedValue): string | string[];
