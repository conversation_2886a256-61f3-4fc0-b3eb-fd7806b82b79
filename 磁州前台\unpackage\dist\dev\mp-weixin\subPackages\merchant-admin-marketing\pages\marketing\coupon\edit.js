"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  setup() {
    const couponId = common_vendor.ref("");
    const couponForm = common_vendor.reactive({
      id: "",
      title: "",
      type: "discount",
      typeText: "满减券",
      value: "",
      minSpend: "",
      startDate: formatDate(/* @__PURE__ */ new Date()),
      expireDate: formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3)),
      // 30天后
      validDays: "30",
      totalCount: "",
      perPersonLimit: "1",
      applicableProducts: "全部商品",
      instructions: ""
    });
    const validityType = common_vendor.ref("fixed");
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function showHelp() {
      common_vendor.index.showModal({
        title: "帮助信息",
        content: "编辑优惠券页面可以修改优惠券的各项参数，包括名称、金额、有效期等。保存后将立即生效。",
        showCancel: false
      });
    }
    function showCouponTypeSelector() {
      common_vendor.index.showActionSheet({
        itemList: ["满减券", "折扣券", "无门槛券"],
        success: (res) => {
          const types = ["discount", "percent", "free"];
          const texts = ["满减券", "折扣券", "无门槛券"];
          couponForm.type = types[res.tapIndex];
          couponForm.typeText = texts[res.tapIndex];
        }
      });
    }
    function setValidityType(type) {
      validityType.value = type;
    }
    function showStartDatePicker() {
      common_vendor.index.showDatePicker({
        current: couponForm.startDate,
        success: (res) => {
          couponForm.startDate = res.date;
        }
      });
    }
    function showEndDatePicker() {
      common_vendor.index.showDatePicker({
        current: couponForm.expireDate,
        success: (res) => {
          couponForm.expireDate = res.date;
        }
      });
    }
    function showProductSelector() {
      common_vendor.index.showActionSheet({
        itemList: ["全部商品", "指定商品", "指定分类"],
        success: (res) => {
          const options = ["全部商品", "指定商品", "指定分类"];
          couponForm.applicableProducts = options[res.tapIndex];
          if (res.tapIndex > 0) {
            common_vendor.index.showToast({
              title: "请在下一页选择",
              icon: "none"
            });
          }
        }
      });
    }
    function saveCoupon() {
      if (!couponForm.title) {
        return common_vendor.index.showToast({
          title: "请输入优惠券名称",
          icon: "none"
        });
      }
      if (!couponForm.value) {
        return common_vendor.index.showToast({
          title: "请输入优惠金额",
          icon: "none"
        });
      }
      if (couponForm.type !== "free" && !couponForm.minSpend) {
        return common_vendor.index.showToast({
          title: "请输入使用门槛",
          icon: "none"
        });
      }
      if (!couponForm.totalCount) {
        return common_vendor.index.showToast({
          title: "请输入发放总量",
          icon: "none"
        });
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    }
    function formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    }
    function loadCouponData(id) {
      couponId.value = id;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        Object.assign(couponForm, {
          id,
          title: "新客专享优惠",
          type: "discount",
          typeText: "满减券",
          value: "10",
          minSpend: "100",
          startDate: "2023-04-15",
          expireDate: "2023-05-15",
          validDays: "30",
          totalCount: "500",
          perPersonLimit: "1",
          applicableProducts: "全部商品",
          instructions: "仅限新用户使用，不可与其他优惠同时使用"
        });
        common_vendor.index.hideLoading();
      }, 500);
    }
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (options.id) {
        loadCouponData(options.id);
      }
    });
    return {
      couponForm,
      validityType,
      goBack,
      showHelp,
      showCouponTypeSelector,
      setValidityType,
      showStartDatePicker,
      showEndDatePicker,
      showProductSelector,
      saveCoupon
    };
  }
};
if (!Array) {
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_rect + _component_line + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    b: common_vendor.o((...args) => $setup.showHelp && $setup.showHelp(...args)),
    c: $setup.couponForm.title,
    d: common_vendor.o(($event) => $setup.couponForm.title = $event.detail.value),
    e: common_vendor.t($setup.couponForm.typeText),
    f: common_vendor.o((...args) => $setup.showCouponTypeSelector && $setup.showCouponTypeSelector(...args)),
    g: $setup.couponForm.value,
    h: common_vendor.o(($event) => $setup.couponForm.value = $event.detail.value),
    i: $setup.couponForm.minSpend,
    j: common_vendor.o(($event) => $setup.couponForm.minSpend = $event.detail.value),
    k: $setup.validityType === "fixed"
  }, $setup.validityType === "fixed" ? {} : {}, {
    l: $setup.validityType === "fixed" ? 1 : "",
    m: common_vendor.o(($event) => $setup.setValidityType("fixed")),
    n: $setup.validityType === "dynamic"
  }, $setup.validityType === "dynamic" ? {} : {}, {
    o: $setup.validityType === "dynamic" ? 1 : "",
    p: common_vendor.o(($event) => $setup.setValidityType("dynamic")),
    q: $setup.validityType === "fixed"
  }, $setup.validityType === "fixed" ? {
    r: common_vendor.t($setup.couponForm.startDate),
    s: common_vendor.p({
      x: "3",
      y: "4",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    t: common_vendor.p({
      x1: "16",
      y1: "2",
      x2: "16",
      y2: "6"
    }),
    v: common_vendor.p({
      x1: "8",
      y1: "2",
      x2: "8",
      y2: "6"
    }),
    w: common_vendor.p({
      x1: "3",
      y1: "10",
      x2: "21",
      y2: "10"
    }),
    x: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    y: common_vendor.o((...args) => $setup.showStartDatePicker && $setup.showStartDatePicker(...args))
  } : {}, {
    z: $setup.validityType === "fixed"
  }, $setup.validityType === "fixed" ? {
    A: common_vendor.t($setup.couponForm.expireDate),
    B: common_vendor.p({
      x: "3",
      y: "4",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    C: common_vendor.p({
      x1: "16",
      y1: "2",
      x2: "16",
      y2: "6"
    }),
    D: common_vendor.p({
      x1: "8",
      y1: "2",
      x2: "8",
      y2: "6"
    }),
    E: common_vendor.p({
      x1: "3",
      y1: "10",
      x2: "21",
      y2: "10"
    }),
    F: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    G: common_vendor.o((...args) => $setup.showEndDatePicker && $setup.showEndDatePicker(...args))
  } : {}, {
    H: $setup.validityType === "dynamic"
  }, $setup.validityType === "dynamic" ? {
    I: $setup.couponForm.validDays,
    J: common_vendor.o(($event) => $setup.couponForm.validDays = $event.detail.value)
  } : {}, {
    K: $setup.couponForm.totalCount,
    L: common_vendor.o(($event) => $setup.couponForm.totalCount = $event.detail.value),
    M: $setup.couponForm.perPersonLimit,
    N: common_vendor.o(($event) => $setup.couponForm.perPersonLimit = $event.detail.value),
    O: common_vendor.t($setup.couponForm.applicableProducts),
    P: common_vendor.o((...args) => $setup.showProductSelector && $setup.showProductSelector(...args)),
    Q: $setup.couponForm.instructions,
    R: common_vendor.o(($event) => $setup.couponForm.instructions = $event.detail.value),
    S: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    T: common_vendor.o((...args) => $setup.saveCoupon && $setup.saveCoupon(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/coupon/edit.js.map
