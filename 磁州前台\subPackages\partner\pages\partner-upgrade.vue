<template>
  <view class="upgrade-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">合伙人升级</view>
      <view class="navbar-right">
        <!-- 预留位置与发布页面保持一致 -->
      </view>
    </view>
    
    <!-- 添加顶部安全区域 -->
    <view class="safe-area-top"></view>

    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>

    <!-- 当前等级信息 -->
    <view class="current-level-card">
      <view class="level-header">
        <text class="level-title">当前等级</text>
      </view>
      <view class="level-content">
        <view class="level-info">
          <view class="level-icon-wrapper" :class="`level-${partnerInfo.level}`">
            <image class="level-icon" :src="getLevelIcon(partnerInfo.level)"></image>
          </view>
          <view class="level-detail">
            <text class="level-name">{{ getLevelName(partnerInfo.level) }}</text>
            <text class="level-desc">一级佣金{{ getCommissionRate(1) }}% / 二级佣金{{ getCommissionRate(2) }}%</text>
          </view>
        </view>
        <view class="level-tag">{{ partnerInfo.level }}级</view>
      </view>
    </view>

    <!-- 升级选项 -->
    <view class="upgrade-options">
      <view class="option-header">
        <text class="option-title">选择升级套餐</text>
      </view>
      
      <view class="option-list">
        <view 
          class="option-item" 
          v-for="(option, index) in upgradeOptions" 
          :key="index" 
          :class="{ active: selectedOption === index, disabled: option.level <= partnerInfo.level }"
          @click="selectOption(index, option)"
        >
          <!-- 等级标识 -->
          <view class="option-badge" :class="`level-${option.level}`">
            <text>{{ option.level }}</text>
          </view>
          
          <view class="option-top">
            <view class="option-left">
              <view class="option-icon-wrapper" :class="`level-${option.level}`">
                <image class="option-icon" :src="option.icon"></image>
              </view>
              <view class="option-info">
                <text class="option-name">{{ option.name }}</text>
                <text class="option-desc">{{ option.desc }}</text>
              </view>
            </view>
            <view class="option-price">
              <text class="price-value">¥{{ option.price }}</text>
              <text class="price-unit">/年</text>
            </view>
          </view>

          <view class="option-benefits">
            <view class="benefit-item" v-for="(benefit, i) in option.benefits" :key="i">
              <image class="check-icon" src="/static/images/tabbar/check.png"></image>
              <text class="benefit-text">{{ benefit }}</text>
            </view>
          </view>

          <view class="option-status" v-if="option.level <= partnerInfo.level">
            <text class="status-text">当前等级</text>
          </view>
          
          <!-- 选中标记 -->
          <view class="option-selected" v-if="selectedOption === index">
            <image class="selected-icon" src="/static/images/tabbar/check.png"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付按钮 -->
    <view class="payment-action">
      <view class="payment-info" v-if="selectedOption !== null">
        <text class="payment-price">¥{{ upgradeOptions[selectedOption].price }}</text>
        <text class="payment-desc">{{ upgradeOptions[selectedOption].name }}年费</text>
      </view>
      <button class="payment-btn" :disabled="selectedOption === null || isPaymentDisabled" @click="processPayment">
        <text class="btn-text">{{ paymentBtnText }}</text>
      </button>
    </view>

    <!-- 升级说明 -->
    <view class="upgrade-notes">
      <view class="notes-header">
        <text class="notes-title">升级说明</text>
      </view>
      <view class="notes-content">
        <view class="note-item">
          <view class="note-dot"></view>
          <text class="note-text">升级后立即生效，有效期为一年</text>
        </view>
        <view class="note-item">
          <view class="note-dot"></view>
          <text class="note-text">升级费用一经支付，不予退还</text>
        </view>
        <view class="note-item">
          <view class="note-dot"></view>
          <text class="note-text">到期前15天可续费，续费后有效期顺延</text>
        </view>
        <view class="note-item">
          <view class="note-dot"></view>
          <text class="note-text">如有疑问，请联系客服</text>
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getLocalUserInfo } from '@/utils/userProfile.js';

// 响应式数据
const partnerInfo = reactive({
  level: 1, // 默认等级
  nickname: '',
  avatar: ''
});

const selectedOption = ref(null);
const isPaymentDisabled = ref(false);
const paymentBtnText = ref('立即支付');
const showAnimation = ref(false);

const upgradeOptions = [
  {
    level: 2,
    name: '银牌合伙人',
    desc: '提升推广收益',
    price: 198,
    icon: '/static/images/tabbar/银牌.png',
    benefits: [
      '一级佣金提升至8%',
      '二级佣金提升至3%',
      '专属推广海报'
    ]
  },
  {
    level: 3,
    name: '金牌合伙人',
    desc: '更高佣金比例',
    price: 498,
    icon: '/static/images/tabbar/金牌.png',
    benefits: [
      '一级佣金提升至12%',
      '二级佣金提升至5%',
      '专属推广海报',
      '优先展示排名'
    ]
  },
  {
    level: 4,
    name: '钻石合伙人',
    desc: '最高权益',
    price: 998,
    icon: '/static/images/tabbar/钻石.png',
    benefits: [
      '一级佣金提升至15%',
      '二级佣金提升至8%',
      '专属推广海报',
      '优先展示排名',
      '专属客服服务'
    ]
  }
];

// 生命周期钩子
onMounted(() => {
  // 获取用户信息
  getUserInfo();
  // 获取合伙人信息
  getPartnerInfo();
  
  // 添加动画效果
  setTimeout(() => {
    showAnimation.value = true;
  }, 300);
});

// 方法
// 获取用户信息
const getUserInfo = () => {
  const userInfo = getLocalUserInfo();
  if (userInfo) {
    partnerInfo.nickname = userInfo.nickname;
    partnerInfo.avatar = userInfo.avatar;
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 获取合伙人信息
const getPartnerInfo = () => {
  // 模拟数据，实际应从API获取
  Object.assign(partnerInfo, {
    level: 1
  });
};

// 获取等级图标
const getLevelIcon = (level) => {
  const icons = {
    1: '/static/images/tabbar/普通.png',
    2: '/static/images/tabbar/银牌.png',
    3: '/static/images/tabbar/金牌.png',
    4: '/static/images/tabbar/钻石.png'
  };
  return icons[level] || icons[1];
};

// 获取等级名称
const getLevelName = (level) => {
  const levelNames = {
    1: '普通合伙人',
    2: '银牌合伙人',
    3: '金牌合伙人',
    4: '钻石合伙人'
  };
  return levelNames[level] || '未知等级';
};

// 获取佣金比例
const getCommissionRate = (level) => {
  // 根据合伙人等级返回不同的佣金比例
  const commissionRates = {
    1: { 1: 5, 2: 2 },    // 普通合伙人：一级5%，二级2%
    2: { 1: 8, 2: 3 },    // 银牌合伙人：一级8%，二级3%
    3: { 1: 12, 2: 5 },   // 金牌合伙人：一级12%，二级5%
    4: { 1: 15, 2: 8 }    // 钻石合伙人：一级15%，二级8%
  };
  
  return commissionRates[partnerInfo.level]?.[level] || 0;
};

// 选择升级选项
const selectOption = (index, option) => {
  // 如果选项等级小于等于当前等级，不允许选择
  if (option.level <= partnerInfo.level) {
    uni.showToast({
      title: '已达到该等级',
      icon: 'none'
    });
    return;
  }
  
  // 添加触感反馈
  if (uni.vibrateShort) {
    uni.vibrateShort({
      success: () => {
        console.log('振动成功');
      }
    });
  }
  
  selectedOption.value = index;
};

// 处理支付
const processPayment = () => {
  if (selectedOption.value === null) {
    uni.showToast({
      title: '请选择升级套餐',
      icon: 'none'
    });
    return;
  }
  
  const option = upgradeOptions[selectedOption.value];
  
  // 显示支付确认
  uni.showModal({
    title: '支付确认',
    content: `确认支付¥${option.price}升级为${option.name}？`,
    success: (res) => {
      if (res.confirm) {
        // 模拟支付过程
        isPaymentDisabled.value = true;
        paymentBtnText.value = '支付中...';
        
        setTimeout(() => {
          // 支付成功
          partnerInfo.level = option.level;
          
          // 显示成功动画
          uni.showToast({
            title: '升级成功',
            icon: 'success',
            duration: 2000
          });
          
          // 重置状态
          setTimeout(() => {
            isPaymentDisabled.value = false;
            paymentBtnText.value = '立即支付';
            selectedOption.value = null;
            
            // 播放成功动画
            playSuccessAnimation();
          }, 1500);
        }, 1500);
      }
    }
  });
};

// 播放成功动画
const playSuccessAnimation = () => {
  // 这里可以添加更复杂的动画效果
  // 例如使用uni.createAnimation创建动画
  // 或者使用CSS动画类
  
  // 简单的动画示例：闪烁效果
  showAnimation.value = false;
  setTimeout(() => {
    showAnimation.value = true;
  }, 100);
};
</script>

<style lang="scss" scoped>
.upgrade-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 400rpx;
    background-image: linear-gradient(135deg, #0066FF, #0052CC);
    z-index: 0;
    border-bottom-left-radius: 30rpx;
    border-bottom-right-radius: 30rpx;
  }
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
  opacity: 0.5;
}

.bg-circle-1 {
  top: 300rpx;
  left: -150rpx;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.2), transparent 70%);
}

.bg-circle-2 {
  top: 600rpx;
  right: -100rpx;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255, 107, 0, 0.15), transparent 70%);
}

.bg-circle-3 {
  bottom: 200rpx;
  left: -120rpx;
  width: 240rpx;
  height: 240rpx;
  background: radial-gradient(circle, rgba(54, 203, 203, 0.15), transparent 70%);
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px; /* 状态栏高度 */
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  box-shadow: 0 4rpx 20rpx rgba(0, 82, 204, 0.25);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20; /* 确保在标题上层，可以被点击 */
}

.back-icon {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.safe-area-top {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}

/* 当前等级卡片 */
.current-level-card {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
  overflow: hidden;
  animation: scaleIn 0.5s ease-out;
  
  &::before {
    content: '';
    position: absolute;
    top: -100rpx;
    right: -100rpx;
    width: 300rpx;
    height: 300rpx;
    background: radial-gradient(circle, rgba(22, 119, 255, 0.1), transparent 70%);
    border-radius: 50%;
    z-index: -1;
  }
}

.level-header {
  margin-bottom: 20rpx;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10rpx;
    width: 60rpx;
    height: 4rpx;
    background: linear-gradient(to right, #0066FF, #36CBCB);
    border-radius: 2rpx;
  }
}

.level-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.level-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30rpx;
}

.level-info {
  display: flex;
  align-items: center;
}

.level-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  
  &.level-1 {
    background: linear-gradient(135deg, #1677FF, #0E5FD8);
  }
  
  &.level-2 {
    background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
  }
  
  &.level-3 {
    background: linear-gradient(135deg, #FFD700, #FFA500);
  }
  
  &.level-4 {
    background: linear-gradient(135deg, #B9F2FF, #00BFFF);
  }
}

.level-icon {
  width: 60%;
  height: 60%;
}

.level-detail {
  display: flex;
  flex-direction: column;
}

.level-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}

.level-desc {
  font-size: 24rpx;
  color: #666666;
}

.level-tag {
  padding: 10rpx 24rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #666666;
}

/* 升级选项 */
.upgrade-options {
  margin: 30rpx;
  position: relative;
  z-index: 1;
}

.option-header {
  margin-bottom: 20rpx;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10rpx;
    width: 60rpx;
    height: 4rpx;
    background: linear-gradient(to right, #0066FF, #36CBCB);
    border-radius: 2rpx;
  }
}

.option-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.option-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 30rpx;
}

.option-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &.active {
    transform: scale(1.02);
    box-shadow: 0 10rpx 30rpx rgba(22, 119, 255, 0.15);
    border: 2rpx solid #0066FF;
  }
  
  &.disabled {
    opacity: 0.7;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.5);
      z-index: 10;
    }
  }
}

.option-badge {
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
  border-bottom-left-radius: 20rpx;
  
  &.level-2 {
    background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
  }
  
  &.level-3 {
    background: linear-gradient(135deg, #FFD700, #FFA500);
  }
  
  &.level-4 {
    background: linear-gradient(135deg, #B9F2FF, #00BFFF);
  }
}

.option-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.option-left {
  display: flex;
  align-items: center;
}

.option-icon-wrapper {
  width: 70rpx;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  
  &.level-2 {
    background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
  }
  
  &.level-3 {
    background: linear-gradient(135deg, #FFD700, #FFA500);
  }
  
  &.level-4 {
    background: linear-gradient(135deg, #B9F2FF, #00BFFF);
  }
}

.option-icon {
  width: 60%;
  height: 60%;
}

.option-info {
  display: flex;
  flex-direction: column;
}

.option-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #666666;
}

.option-price {
  display: flex;
  align-items: baseline;
}

.price-value {
  font-size: 40rpx;
  font-weight: 700;
  color: #FF6B00;
}

.price-unit {
  font-size: 24rpx;
  color: #999999;
  margin-left: 4rpx;
}

.option-benefits {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.check-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}

.benefit-text {
  font-size: 26rpx;
  color: #666666;
}

.option-status {
  position: absolute;
  top: 60rpx;
  right: -60rpx;
  background-color: #999999;
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 80rpx;
  transform: rotate(45deg);
  z-index: 5;
}

.option-selected {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #0066FF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-icon {
  width: 60%;
  height: 60%;
}

/* 支付按钮 */
.payment-action {
  margin: 40rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.payment-info {
  display: flex;
  flex-direction: column;
}

.payment-price {
  font-size: 48rpx;
  font-weight: 700;
  color: #FF6B00;
}

.payment-desc {
  font-size: 24rpx;
  color: #999999;
}

.payment-btn {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  padding: 20rpx 60rpx;
  border-radius: 50rpx;
  border: none;
  box-shadow: 0 6rpx 15rpx rgba(0, 82, 204, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 10rpx rgba(0, 82, 204, 0.2);
  }
  
  &:disabled {
    background: #cccccc;
    box-shadow: none;
  }
}

.btn-text {
  display: block;
  text-align: center;
}

/* 升级说明 */
.upgrade-notes {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.notes-header {
  margin-bottom: 20rpx;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10rpx;
    width: 60rpx;
    height: 4rpx;
    background: linear-gradient(to right, #0066FF, #36CBCB);
    border-radius: 2rpx;
  }
}

.notes-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.notes-content {
  margin-top: 20rpx;
}

.note-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.note-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #0066FF;
  margin-top: 12rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.note-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}

/* 动画 */
@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>