{"version": 3, "file": "newsList.js", "sources": ["mock/news/newsList.js"], "sourcesContent": ["// 新闻列表模拟数据\r\nexport const newsList = [\r\n  {\r\n    id: 1,\r\n    title: '磁县城区道路改造工程开始，请注意绕行',\r\n    description: '为提升城市道路品质，改善市民出行环境，磁县将对城区主要道路进行升级改造。',\r\n    time: '2024-03-15 10:30',\r\n    category: '政务资讯',\r\n    image: '/static/images/banner/banner-1.png',\r\n    views: 1234,\r\n    likes: 88,\r\n    comments: 32\r\n  },\r\n  {\r\n    id: 2,\r\n    title: '磁县第二届美食文化节将于下月举办',\r\n    description: '为展示磁县特色美食文化，促进餐饮业发展，磁县将举办第二届美食文化节。',\r\n    time: '2024-03-14 16:45',\r\n    category: '活动通知',\r\n    image: '/static/images/banner/banner-2.png',\r\n    views: 956,\r\n    likes: 76,\r\n    comments: 28\r\n  },\r\n  {\r\n    id: 3,\r\n    title: '磁县新增三所幼儿园，解决入园难问题',\r\n    description: '为解决适龄儿童入园难问题，磁县今年新建三所公办幼儿园，预计9月开始招生。',\r\n    time: '2024-03-13 09:15',\r\n    category: '便民信息',\r\n    image: '/static/images/banner/banner-3.png',\r\n    views: 845,\r\n    likes: 62,\r\n    comments: 19\r\n  },\r\n  {\r\n    id: 4,\r\n    title: '磁县多家企业联合招聘会本周六举行',\r\n    description: '为促进就业，磁县将举办大型招聘会，多家知名企业参与，提供上千个就业岗位。',\r\n    time: '2024-03-12 14:20',\r\n    category: '招聘信息',\r\n    image: '/static/images/banner/banner-4.png',\r\n    views: 1567,\r\n    likes: 45,\r\n    comments: 37\r\n  },\r\n  {\r\n    id: 5,\r\n    title: '磁县房地产市场稳中有升，新项目陆续开工',\r\n    description: '近期磁县房地产市场呈现稳中有升态势，多个新住宅项目陆续开工建设。',\r\n    time: '2024-03-11 11:30',\r\n    category: '房产资讯',\r\n    image: '/static/images/banner/banner-5.png',\r\n    views: 1023,\r\n    likes: 39,\r\n    comments: 26\r\n  },\r\n  {\r\n    id: 6,\r\n    title: '磁县实施\"绿色家园\"工程，城区将新增多处公园',\r\n    description: '为改善城市生态环境，提升居民生活品质，磁县启动\"绿色家园\"工程。',\r\n    time: '2024-03-10 15:40',\r\n    category: '政务资讯',\r\n    image: '/static/images/banner/banner-6.png',\r\n    views: 876,\r\n    likes: 71,\r\n    comments: 23\r\n  },\r\n  {\r\n    id: 7,\r\n    title: '磁县文化馆免费开放系列活动即将启动',\r\n    description: '磁县文化馆将举办一系列免费文化活动，包括书法展、摄影展和传统手工艺展示等。',\r\n    time: '2024-03-09 13:25',\r\n    category: '活动通知',\r\n    image: '/static/images/banner/banner-7.png',\r\n    views: 734,\r\n    likes: 58,\r\n    comments: 17\r\n  },\r\n  {\r\n    id: 8,\r\n    title: '磁县推出便民服务新举措，办事更加便捷',\r\n    description: '磁县行政服务中心推出一系列便民新举措，实现\"最多跑一次\"和\"一网通办\"。',\r\n    time: '2024-03-08 10:50',\r\n    category: '便民信息',\r\n    image: '/static/images/banner/banner-8.png',\r\n    views: 892,\r\n    likes: 67,\r\n    comments: 21\r\n  }\r\n];\r\n\r\n// 模拟获取新闻列表的API函数\r\nexport const fetchNewsList = (categoryId = 0, page = 1, pageSize = 10) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      let result = [...newsList];\r\n      \r\n      // 如果有分类筛选\r\n      if (categoryId !== 0) {\r\n        const categoryMap = {\r\n          1: '政务资讯',\r\n          2: '便民信息',\r\n          3: '活动通知',\r\n          4: '招聘信息',\r\n          5: '房产资讯'\r\n        };\r\n        result = result.filter(item => item.category === categoryMap[categoryId]);\r\n      }\r\n      \r\n      // 分页处理\r\n      const start = (page - 1) * pageSize;\r\n      const end = start + pageSize;\r\n      const data = result.slice(start, end);\r\n      \r\n      // 返回数据和分页信息\r\n      resolve({\r\n        list: data,\r\n        total: result.length,\r\n        hasMore: end < result.length\r\n      });\r\n    }, 500);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,WAAW;AAAA,EACtB;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACX;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACX;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACX;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACX;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACX;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACX;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACX;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,EACX;AACH;AAGY,MAAC,gBAAgB,CAAC,aAAa,GAAG,OAAO,GAAG,WAAW,OAAO;AACxE,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,SAAS,CAAC,GAAG,QAAQ;AAGzB,UAAI,eAAe,GAAG;AACpB,cAAM,cAAc;AAAA,UAClB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACb;AACQ,iBAAS,OAAO,OAAO,UAAQ,KAAK,aAAa,YAAY,UAAU,CAAC;AAAA,MACzE;AAGD,YAAM,SAAS,OAAO,KAAK;AAC3B,YAAM,MAAM,QAAQ;AACpB,YAAM,OAAO,OAAO,MAAM,OAAO,GAAG;AAGpC,cAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO,OAAO;AAAA,QACd,SAAS,MAAM,OAAO;AAAA,MAC9B,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;"}