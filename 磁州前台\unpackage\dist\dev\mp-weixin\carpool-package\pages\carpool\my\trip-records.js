"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "trip-records",
  setup(__props, { expose: __expose }) {
    const statusBarHeight = common_vendor.ref(20);
    const tabs = common_vendor.ref([
      { label: "全部", value: "all" },
      { label: "进行中", value: "ongoing" },
      { label: "已完成", value: "completed" },
      { label: "已取消", value: "canceled" }
    ]);
    const currentTab = common_vendor.ref("all");
    const tripList = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 20;
      loadTrips();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTab = (tab) => {
      if (currentTab.value === tab)
        return;
      currentTab.value = tab;
      page.value = 1;
      tripList.value = [];
      hasMore.value = true;
      loadTrips();
    };
    const loadTrips = () => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      setTimeout(() => {
        const mockTrips = [
          {
            id: "1001",
            role: "driver",
            status: "completed",
            startPoint: "磁县政府",
            endPoint: "邯郸火车站",
            departureTime: "2023-11-15 10:30",
            passengerCount: 3,
            price: 35,
            isRated: true
          },
          {
            id: "1002",
            role: "passenger",
            status: "ongoing",
            startPoint: "磁县老城区",
            endPoint: "邯郸科技学院",
            departureTime: "2023-12-05 07:30",
            passengerCount: 1,
            price: 25,
            isRated: false
          },
          {
            id: "1003",
            role: "driver",
            status: "canceled",
            startPoint: "磁县新城区",
            endPoint: "邯郸东站",
            departureTime: "2023-11-05 09:15",
            passengerCount: 0,
            price: 30,
            isRated: false
          }
        ];
        let filteredTrips = mockTrips;
        if (currentTab.value !== "all") {
          filteredTrips = mockTrips.filter((item) => item.status === currentTab.value);
        }
        if (page.value === 1) {
          tripList.value = filteredTrips;
        } else {
          tripList.value = [...tripList.value, ...filteredTrips];
        }
        if (page.value >= 2) {
          hasMore.value = false;
        }
        isLoading.value = false;
        isRefreshing.value = false;
      }, 1e3);
    };
    const getStatusText = (status) => {
      const statusMap = {
        "ongoing": "进行中",
        "completed": "已完成",
        "canceled": "已取消"
      };
      return statusMap[status] || "未知状态";
    };
    const loadMore = () => {
      if (hasMore.value && !isLoading.value) {
        page.value++;
        loadTrips();
      }
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      page.value = 1;
      hasMore.value = true;
      loadTrips();
    };
    const viewTripDetail = (item) => {
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/trip-detail/index?id=${item.id}`
      });
    };
    const contactDriver = (item) => {
      common_vendor.index.showModal({
        title: `联系${item.role === "driver" ? "乘客" : "车主"}`,
        content: `是否拨打${item.role === "driver" ? "乘客" : "车主"}电话？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.makePhoneCall({
              phoneNumber: "13812345678",
              fail: () => {
                common_vendor.index.showToast({
                  title: "拨打电话失败",
                  icon: "none"
                });
              }
            });
          }
        }
      });
    };
    const rateTrip = (item) => {
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/rating/index?id=${item.id}&role=${item.role}`
      });
    };
    __expose({
      loadTrips,
      switchTab
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.label),
            b: index,
            c: currentTab.value === tab.value ? 1 : "",
            d: common_vendor.o(($event) => switchTab(tab.value), index)
          };
        }),
        e: statusBarHeight.value + 44 + "px",
        f: tripList.value.length > 0
      }, tripList.value.length > 0 ? {
        g: common_vendor.f(tripList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.role === "driver" ? "我是车主" : "我是乘客"),
            b: item.role === "driver" ? 1 : "",
            c: item.role === "passenger" ? 1 : "",
            d: common_vendor.t(getStatusText(item.status)),
            e: common_vendor.n(item.status),
            f: common_vendor.t(item.startPoint),
            g: common_vendor.t(item.endPoint),
            h: common_vendor.t(item.departureTime),
            i: common_vendor.t(item.role === "driver" ? "乘客数" : "同行人数"),
            j: common_vendor.t(item.passengerCount),
            k: common_vendor.t(item.price.toFixed(2)),
            l: common_vendor.o(($event) => viewTripDetail(item), item.id),
            m: item.status === "ongoing"
          }, item.status === "ongoing" ? {
            n: common_vendor.t(item.role === "driver" ? "乘客" : "车主"),
            o: common_vendor.o(($event) => contactDriver(item), item.id)
          } : {}, {
            p: item.status === "completed" && !item.isRated
          }, item.status === "completed" && !item.isRated ? {
            q: common_vendor.o(($event) => rateTrip(item), item.id)
          } : {}, {
            r: item.id
          });
        })
      } : {}, {
        h: tripList.value.length === 0 && !isLoading.value
      }, tripList.value.length === 0 && !isLoading.value ? {
        i: common_assets._imports_1$36
      } : {}, {
        j: isLoading.value && !isRefreshing.value
      }, isLoading.value && !isRefreshing.value ? {} : {}, {
        k: tripList.value.length > 0 && !hasMore.value
      }, tripList.value.length > 0 && !hasMore.value ? {} : {}, {
        l: common_vendor.o(loadMore),
        m: common_vendor.o(onRefresh),
        n: isRefreshing.value
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/trip-records.js.map
