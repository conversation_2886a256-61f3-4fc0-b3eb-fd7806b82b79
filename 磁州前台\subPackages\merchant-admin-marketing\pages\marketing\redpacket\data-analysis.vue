<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包数据</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 数据筛选 -->
    <view class="filter-section">
      <view class="filter-header">
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
        <view class="type-filter" @click="showTypeFilter">
          <text class="filter-text">{{currentType}}</text>
          <view class="filter-icon"></view>
        </view>
      </view>
    </view>
    
    <!-- 数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">数据概览</text>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{redpacketData.totalCount}}</view>
          <view class="card-label">发放总数</view>
          <view class="card-trend" :class="redpacketData.countTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{redpacketData.countGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">¥{{formatNumber(redpacketData.totalAmount)}}</view>
          <view class="card-label">红包总额</view>
          <view class="card-trend" :class="redpacketData.amountTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{redpacketData.amountGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{redpacketData.receiveRate}}%</view>
          <view class="card-label">领取率</view>
          <view class="card-trend" :class="redpacketData.receiveRateTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{redpacketData.receiveRateGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{redpacketData.conversionRate}}%</view>
          <view class="card-label">转化率</view>
          <view class="card-trend" :class="redpacketData.conversionTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{redpacketData.conversionGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 趋势图表 -->
    <view class="chart-section">
      <view class="section-header">
        <text class="section-title">趋势分析</text>
        <view class="chart-tabs">
          <view 
            class="chart-tab" 
            v-for="(tab, index) in chartTabs" 
            :key="index"
            :class="{ active: currentChartTab === index }"
            @tap="switchChartTab(index)">
            <text class="tab-text">{{tab}}</text>
          </view>
        </view>
      </view>
      
      <view class="chart-container">
        <image class="chart-placeholder" src="/static/images/redpacket/chart-placeholder.png" mode="widthFix"></image>
      </view>
    </view>
    
    <!-- 用户分析 -->
    <view class="user-analysis-section">
      <view class="section-header">
        <text class="section-title">用户分析</text>
      </view>
      
      <view class="user-stats">
        <view class="user-stat-item">
          <view class="stat-circle" :style="{ background: 'conic-gradient(#FF5858 ' + (userData.newUserRate * 3.6) + 'deg, #F5F7FA 0)' }">
            <view class="inner-circle">
              <text class="circle-value">{{userData.newUserRate}}%</text>
            </view>
          </view>
          <text class="stat-label">新用户占比</text>
        </view>
        
        <view class="user-stat-item">
          <view class="stat-circle" :style="{ background: 'conic-gradient(#4ECDC4 ' + (userData.activeUserRate * 3.6) + 'deg, #F5F7FA 0)' }">
            <view class="inner-circle">
              <text class="circle-value">{{userData.activeUserRate}}%</text>
            </view>
          </view>
          <text class="stat-label">活跃用户</text>
        </view>
        
        <view class="user-stat-item">
          <view class="stat-circle" :style="{ background: 'conic-gradient(#FFD166 ' + (userData.repurchaseRate * 3.6) + 'deg, #F5F7FA 0)' }">
            <view class="inner-circle">
              <text class="circle-value">{{userData.repurchaseRate}}%</text>
            </view>
          </view>
          <text class="stat-label">复购率</text>
        </view>
      </view>
    </view>
    
    <!-- 红包类型分析 -->
    <view class="type-analysis-section">
      <view class="section-header">
        <text class="section-title">红包类型分析</text>
      </view>
      
      <view class="type-stats">
        <view class="type-stat-item" v-for="(item, index) in typeData" :key="index">
          <view class="type-bar-container">
            <view class="type-bar" :style="{ width: item.percentage + '%', background: item.color }"></view>
          </view>
          <view class="type-info">
            <view class="type-name">{{item.name}}</view>
            <view class="type-value">{{item.count}}个 ({{item.percentage}}%)</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 红包效果排行 -->
    <view class="ranking-section">
      <view class="section-header">
        <text class="section-title">红包效果排行</text>
      </view>
      
      <view class="ranking-tabs">
        <view 
          class="ranking-tab" 
          v-for="(tab, index) in rankingTabs" 
          :key="index"
          :class="{ active: currentRankingTab === index }"
          @tap="switchRankingTab(index)">
          <text class="tab-text">{{tab}}</text>
        </view>
      </view>
      
      <view class="ranking-list">
        <view class="ranking-item" v-for="(item, index) in rankingList" :key="index">
          <view class="ranking-number" :class="{ 'top-three': index < 3 }">{{index + 1}}</view>
          <view class="ranking-content">
            <text class="ranking-title">{{item.title}}</text>
            <text class="ranking-time">{{item.time}}</text>
          </view>
          <view class="ranking-value">
            <text class="value-text">{{item.value}}</text>
            <text class="value-unit">{{rankingValueUnit}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 导出报表按钮 -->
    <view class="export-button" @click="exportReport">
      <text class="export-text">导出数据报表</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      currentType: '全部类型',
      
      // 红包数据概览
      redpacketData: {
        totalCount: 5862,
        countTrend: 'up',
        countGrowth: '12.5%',
        
        totalAmount: 58620.50,
        amountTrend: 'up',
        amountGrowth: '15.2%',
        
        receiveRate: 76.8,
        receiveRateTrend: 'up',
        receiveRateGrowth: '3.5%',
        
        conversionRate: 42.3,
        conversionTrend: 'up',
        conversionGrowth: '5.2%'
      },
      
      // 图表选项
      chartTabs: ['发放数量', '领取金额', '转化率'],
      currentChartTab: 0,
      
      // 用户数据
      userData: {
        newUserRate: 35,
        activeUserRate: 68,
        repurchaseRate: 42
      },
      
      // 红包类型数据
      typeData: [
        {
          name: '普通红包',
          count: 3256,
          percentage: 55,
          color: '#FF5858'
        },
        {
          name: '裂变红包',
          count: 1528,
          percentage: 26,
          color: '#4ECDC4'
        },
        {
          name: '群发红包',
          count: 782,
          percentage: 13,
          color: '#FFD166'
        },
        {
          name: '红包雨',
          count: 296,
          percentage: 6,
          color: '#6A0572'
        }
      ],
      
      // 排行榜选项
      rankingTabs: ['转化率', '领取率', '分享率'],
      currentRankingTab: 0,
      
      // 排行榜数据
      rankingList: [
        {
          title: '新用户专享红包',
          time: '2023-04-15 ~ 2023-04-20',
          value: 68.5
        },
        {
          title: '五一节日红包',
          time: '2023-05-01 ~ 2023-05-07',
          value: 62.3
        },
        {
          title: '周末限时红包',
          time: '2023-04-22 ~ 2023-04-23',
          value: 58.9
        },
        {
          title: '会员生日红包',
          time: '2023-04-01 ~ 2023-04-30',
          value: 52.4
        },
        {
          title: '满减活动红包',
          time: '2023-04-10 ~ 2023-04-15',
          value: 48.7
        }
      ]
    }
  },
  computed: {
    rankingValueUnit() {
      const units = ['%', '%', '%'];
      return units[this.currentRankingTab];
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showModal({
        title: '红包数据帮助',
        content: '在此页面您可以查看红包活动的各项数据指标和分析报告，帮助您优化红包营销策略。',
        showCancel: false
      });
    },
    showDatePicker() {
      // 显示日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    showTypeFilter() {
      // 显示类型筛选
      uni.showToast({
        title: '类型筛选功能开发中',
        icon: 'none'
      });
    },
    formatNumber(num) {
      return num.toFixed(2);
    },
    switchChartTab(index) {
      this.currentChartTab = index;
    },
    switchRankingTab(index) {
      this.currentRankingTab = index;
    },
    exportReport() {
      uni.showToast({
        title: '数据报表导出功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 筛选样式 */
.filter-section {
  background-color: #fff;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-picker,
.type-filter {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  padding: 8px 12px;
  border-radius: 15px;
}

.date-text,
.filter-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon,
.filter-icon {
  width: 10px;
  height: 10px;
  border-left: 1px solid #666;
  border-bottom: 1px solid #666;
  transform: rotate(-45deg);
}

/* 数据概览样式 */
.overview-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.overview-header {
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stats-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.stats-card {
  width: 48%;
  background: linear-gradient(135deg, #FFF, #F5F7FA);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.card-value {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.card-label {
  font-size: 12px;
  color: #666;
}

.card-trend {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #FF5858;
}

.card-trend.down {
  color: #2ED573;
}

.trend-arrow {
  width: 8px;
  height: 8px;
  border-left: 1px solid currentColor;
  border-top: 1px solid currentColor;
  margin-right: 2px;
}

.up .trend-arrow {
  transform: rotate(45deg);
}

.down .trend-arrow {
  transform: rotate(-135deg);
}

/* 图表样式 */
.chart-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-tabs {
  display: flex;
}

.chart-tab {
  padding: 5px 10px;
  margin-left: 5px;
  border-radius: 15px;
  background-color: #F5F7FA;
}

.chart-tab.active {
  background-color: #FF5858;
  color: #fff;
}

.tab-text {
  font-size: 12px;
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 100%;
  height: auto;
}

/* 用户分析样式 */
.user-analysis-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 15px;
}

.user-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-circle {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  position: relative;
  margin-bottom: 10px;
}

.inner-circle {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 类型分析样式 */
.type-analysis-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.type-stats {
  margin-top: 15px;
}

.type-stat-item {
  margin-bottom: 15px;
}

.type-bar-container {
  height: 10px;
  background-color: #F5F7FA;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 5px;
}

.type-bar {
  height: 100%;
  border-radius: 5px;
}

.type-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.type-name {
  color: #666;
}

.type-value {
  color: #333;
  font-weight: 500;
}

/* 排行榜样式 */
.ranking-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ranking-tabs {
  display: flex;
  margin-bottom: 15px;
}

.ranking-tab {
  padding: 5px 15px;
  margin-right: 10px;
  border-radius: 15px;
  background-color: #F5F7FA;
}

.ranking-tab.active {
  background-color: #FF5858;
  color: #fff;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #F5F7FA;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
}

.ranking-number.top-three {
  background-color: #FF5858;
  color: #fff;
}

.ranking-content {
  flex: 1;
}

.ranking-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.ranking-time {
  font-size: 12px;
  color: #999;
}

.ranking-value {
  text-align: right;
}

.value-text {
  font-size: 16px;
  font-weight: bold;
  color: #FF5858;
}

.value-unit {
  font-size: 12px;
  color: #666;
  margin-left: 2px;
}

/* 导出按钮 */
.export-button {
  margin: 15px;
  padding: 12px 0;
  background-color: #fff;
  border-radius: 25px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}

.export-text {
  font-size: 14px;
  color: #FF5858;
  font-weight: 500;
}
</style>