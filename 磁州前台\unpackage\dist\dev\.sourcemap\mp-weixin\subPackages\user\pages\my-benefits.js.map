{"version": 3, "file": "my-benefits.js", "sources": ["subPackages/user/pages/my-benefits.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcdXNlclxwYWdlc1xteS1iZW5lZml0cy52dWU"], "sourcesContent": ["<template>\n  <view class=\"benefits-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">我的福利</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 福利概览卡片 -->\n    <view class=\"overview-card\">\n      <view class=\"overview-header\">\n        <text class=\"overview-title\">福利概览</text>\n        <text class=\"overview-subtitle\">{{userInfo.nickname || '微信用户'}}，这是您的专属福利</text>\n      </view>\n      <view class=\"overview-content\">\n        <view class=\"overview-item\" @click=\"navigateTo('/pages/services/coupon')\">\n          <text class=\"overview-number\">{{statistics.couponCount || 0}}</text>\n          <text class=\"overview-label\">可用优惠券</text>\n        </view>\n        <view class=\"overview-item\" @click=\"navigateTo('/pages/user/my-red-packets')\">\n          <text class=\"overview-number\">{{statistics.redPacketCount || 0}}</text>\n          <text class=\"overview-label\">可领红包</text>\n        </view>\n        <view class=\"overview-item\" @click=\"navigateTo('/subPackages/checkin/pages/points')\">\n          <text class=\"overview-number\">{{statistics.pointsCount || 0}}</text>\n          <text class=\"overview-label\">积分</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 福利分类导航 -->\n    <view class=\"benefits-nav\">\n      <view class=\"nav-item\" @click=\"navigateTo('/pages/services/coupon')\">\n        <view class=\"nav-icon-wrap\">\n          <image class=\"nav-icon\" src=\"/static/images/tabbar/卡券.png\"></image>\n          <text v-if=\"statistics.couponCount > 0\" class=\"nav-badge\">{{statistics.couponCount}}</text>\n        </view>\n        <text class=\"nav-label\">优惠券</text>\n        <text class=\"nav-desc\">折扣、满减等优惠</text>\n        <image class=\"nav-arrow\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n      </view>\n      \n      <view class=\"nav-item\" @click=\"navigateTo('/pages/user/my-red-packets')\">\n        <view class=\"nav-icon-wrap\">\n          <image class=\"nav-icon\" src=\"/static/images/tabbar/我的红包.png\"></image>\n          <text v-if=\"statistics.redPacketCount > 0\" class=\"nav-badge\">{{statistics.redPacketCount}}</text>\n        </view>\n        <text class=\"nav-label\">红包</text>\n        <text class=\"nav-desc\">商家发放的现金红包</text>\n        <image class=\"nav-arrow\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n      </view>\n      \n      <view class=\"nav-item\" @click=\"navigateTo('/subPackages/checkin/pages/points')\">\n        <view class=\"nav-icon-wrap\">\n          <image class=\"nav-icon\" src=\"/static/images/tabbar/每日签到.png\"></image>\n        </view>\n        <text class=\"nav-label\">积分商城</text>\n        <text class=\"nav-desc\">积分兑换礼品</text>\n        <image class=\"nav-arrow\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n      </view>\n      \n      <view class=\"nav-item\" @click=\"navigateTo('/pages/user-center/activity-rewards')\">\n        <view class=\"nav-icon-wrap\">\n          <image class=\"nav-icon\" src=\"/static/images/tabbar/活动.png\"></image>\n          <text v-if=\"statistics.activityRewardCount > 0\" class=\"nav-badge\">{{statistics.activityRewardCount}}</text>\n        </view>\n        <text class=\"nav-label\">活动奖励</text>\n        <text class=\"nav-desc\">参与活动获得的奖励</text>\n        <image class=\"nav-arrow\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n      </view>\n    </view>\n    \n    <!-- 最近获得的福利 -->\n    <view class=\"recent-benefits\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">最近获得的福利</text>\n        <text class=\"section-more\" @click=\"showAllBenefits\">查看全部</text>\n      </view>\n      \n      <view class=\"benefits-list\">\n        <view v-if=\"recentBenefits.length > 0\">\n          <view class=\"benefit-item\" v-for=\"(item, index) in recentBenefits\" :key=\"index\" @click=\"viewBenefitDetail(item)\">\n            <image class=\"benefit-icon\" :src=\"getBenefitIcon(item.type)\"></image>\n            <view class=\"benefit-info\">\n              <text class=\"benefit-name\">{{item.name}}</text>\n              <text class=\"benefit-desc\">{{item.description}}</text>\n              <text class=\"benefit-time\">{{item.time}}</text>\n            </view>\n            <view class=\"benefit-status\" :class=\"{'status-expired': item.status === 'expired'}\">\n              {{getBenefitStatus(item.status)}}\n            </view>\n          </view>\n        </view>\n        <view v-else class=\"empty-view\">\n          <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n          <view class=\"empty-text\">暂无福利记录</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 推荐活动 -->\n    <view class=\"recommended-activities\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">推荐活动</text>\n        <text class=\"section-more\" @click=\"navigateTo('/pages/activity/list')\">更多活动</text>\n      </view>\n      \n      <scroll-view scroll-x class=\"activities-scroll\">\n        <view class=\"activities-list\">\n          <view class=\"activity-card\" v-for=\"(item, index) in recommendedActivities\" :key=\"index\" @click=\"navigateTo('/pages/activity/detail?id=' + item.id)\">\n            <image class=\"activity-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\n            <view class=\"activity-info\">\n              <text class=\"activity-name\">{{item.name}}</text>\n              <text class=\"activity-desc\">{{item.description}}</text>\n              <view class=\"activity-meta\">\n                <text class=\"activity-time\">{{item.time}}</text>\n                <text class=\"activity-tag\">{{item.tag}}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\nimport { getLocalUserInfo } from '@/utils/userProfile.js';\nimport { smartNavigate } from '@/utils/navigation.js';\n\n// Vue 3 Composition API 代码开始\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 用户信息\nconst userInfo = ref({\n  nickname: '',\n  avatar: ''\n});\n\n// 统计数据\nconst statistics = ref({\n  couponCount: 3,\n  redPacketCount: 2,\n  pointsCount: 520,\n  activityRewardCount: 1\n});\n\n// 最近获得的福利\nconst recentBenefits = ref([\n  {\n    id: '1',\n    type: 'coupon',\n    name: '满100减20优惠券',\n    description: '适用于全部商家',\n    time: '2023-05-15 获得',\n    status: 'available'\n  },\n  {\n    id: '2',\n    type: 'redPacket',\n    name: '5元现金红包',\n    description: '来自\"品味咖啡\"',\n    time: '2023-05-14 获得',\n    status: 'available'\n  },\n  {\n    id: '3',\n    type: 'points',\n    name: '签到积分',\n    description: '+10积分',\n    time: '2023-05-13 获得',\n    status: 'used'\n  },\n  {\n    id: '4',\n    type: 'coupon',\n    name: '满50减10优惠券',\n    description: '适用于餐饮美食',\n    time: '2023-05-10 获得',\n    status: 'expired'\n  }\n]);\n\n// 推荐活动\nconst recommendedActivities = ref([\n  {\n    id: '1',\n    name: '夏日狂欢购物节',\n    description: '满100送50，多重好礼等你来',\n    time: '05-20 至 05-30',\n    tag: '购物',\n    image: '/static/images/activity/shopping.jpg'\n  },\n  {\n    id: '2',\n    name: '美食品鉴会',\n    description: '新店开业，免费品尝',\n    time: '05-25 19:00',\n    tag: '美食',\n    image: '/static/images/activity/food.jpg'\n  },\n  {\n    id: '3',\n    name: '亲子嘉年华',\n    description: '儿童乐园门票半价',\n    time: '05-28 全天',\n    tag: '亲子',\n    image: '/static/images/activity/family.jpg'\n  }\n]);\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n  \n  // 获取用户信息\n  getUserInfo();\n  \n  // 加载福利数据\n  loadBenefitsData();\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 页面跳转\nconst navigateTo = (url) => {\n  smartNavigate(url).catch(err => {\n    console.error('智能导航失败:', err);\n  });\n};\n\n// 获取用户信息\nconst getUserInfo = () => {\n  // 从本地存储获取用户信息\n  const localUserInfo = getLocalUserInfo();\n  if (localUserInfo) {\n    userInfo.value = localUserInfo;\n  }\n};\n\n// 加载福利数据\nconst loadBenefitsData = () => {\n  // 这里应该是实际的API调用，获取用户的福利数据\n  // 当前使用模拟数据\n  console.log('加载福利数据');\n};\n\n// 查看所有福利\nconst showAllBenefits = () => {\n  uni.showToast({\n    title: '功能开发中',\n    icon: 'none'\n  });\n};\n\n// 查看福利详情\nconst viewBenefitDetail = (item) => {\n  switch(item.type) {\n    case 'coupon':\n      navigateTo('/pages/services/coupon');\n      break;\n    case 'redPacket':\n      navigateTo('/pages/user/my-red-packets');\n      break;\n    case 'points':\n      navigateTo('/subPackages/checkin/pages/points');\n      break;\n    default:\n      uni.showToast({\n        title: '查看详情',\n        icon: 'none'\n      });\n  }\n};\n\n// 获取福利图标\nconst getBenefitIcon = (type) => {\n  switch(type) {\n    case 'coupon':\n      return '/static/images/tabbar/卡券.png';\n    case 'redPacket':\n      return '/static/images/tabbar/我的红包.png';\n    case 'points':\n      return '/static/images/tabbar/每日签到.png';\n    default:\n      return '/static/images/tabbar/活动.png';\n  }\n};\n\n// 获取福利状态文本\nconst getBenefitStatus = (status) => {\n  switch(status) {\n    case 'available':\n      return '可使用';\n    case 'used':\n      return '已使用';\n    case 'expired':\n      return '已过期';\n    default:\n      return '未知';\n  }\n};\n// Vue 3 Composition API 代码结束\n</script>\n\n<style lang=\"scss\" scoped>\n.benefits-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 20rpx;\n}\n\n/* 导航栏样式 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 15px;\n  background: linear-gradient(135deg, #3a7afe, #6ca6ff);\n  color: #fff;\n  z-index: 100;\n}\n\n.navbar-left {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 36rpx;\n  height: 36rpx;\n}\n\n.navbar-title {\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.navbar-right {\n  width: 60rpx;\n}\n\n/* 福利概览卡片 */\n.overview-card {\n  margin: calc(44px + var(--status-bar-height)) 20rpx 20rpx;\n  padding: 30rpx;\n  background: linear-gradient(135deg, #3a7afe, #6ca6ff);\n  border-radius: 16rpx;\n  color: #fff;\n  box-shadow: 0 4rpx 20rpx rgba(106, 166, 255, 0.3);\n}\n\n.overview-header {\n  margin-bottom: 30rpx;\n}\n\n.overview-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.overview-subtitle {\n  font-size: 24rpx;\n  opacity: 0.8;\n}\n\n.overview-content {\n  display: flex;\n  justify-content: space-between;\n}\n\n.overview-item {\n  text-align: center;\n  flex: 1;\n}\n\n.overview-number {\n  font-size: 48rpx;\n  font-weight: bold;\n  display: block;\n}\n\n.overview-label {\n  font-size: 24rpx;\n  opacity: 0.8;\n}\n\n/* 福利分类导航 */\n.benefits-nav {\n  margin: 20rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.nav-item {\n  display: flex;\n  align-items: center;\n  padding: 30rpx 20rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  position: relative;\n}\n\n.nav-item:last-child {\n  border-bottom: none;\n}\n\n.nav-icon-wrap {\n  width: 80rpx;\n  height: 80rpx;\n  background-color: #f0f5ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n  position: relative;\n}\n\n.nav-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.nav-badge {\n  position: absolute;\n  top: -6rpx;\n  right: -6rpx;\n  background-color: #ff4d4f;\n  color: #fff;\n  font-size: 20rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  border-radius: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 6rpx;\n}\n\n.nav-label {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  flex: 1;\n}\n\n.nav-desc {\n  font-size: 24rpx;\n  color: #999;\n  margin-right: 20rpx;\n}\n\n.nav-arrow {\n  width: 32rpx;\n  height: 32rpx;\n  transform: rotate(90deg);\n}\n\n/* 最近获得的福利 */\n.recent-benefits {\n  margin: 20rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.section-more {\n  font-size: 24rpx;\n  color: #3a7afe;\n}\n\n.benefits-list {\n  margin-top: 20rpx;\n}\n\n.benefit-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.benefit-item:last-child {\n  border-bottom: none;\n}\n\n.benefit-icon {\n  width: 80rpx;\n  height: 80rpx;\n  margin-right: 20rpx;\n}\n\n.benefit-info {\n  flex: 1;\n}\n\n.benefit-name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 6rpx;\n  display: block;\n}\n\n.benefit-desc {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 6rpx;\n  display: block;\n}\n\n.benefit-time {\n  font-size: 22rpx;\n  color: #999;\n  display: block;\n}\n\n.benefit-status {\n  font-size: 24rpx;\n  color: #3a7afe;\n  padding: 4rpx 12rpx;\n  background-color: #f0f5ff;\n  border-radius: 6rpx;\n}\n\n.status-expired {\n  color: #999;\n  background-color: #f5f5f5;\n}\n\n/* 推荐活动 */\n.recommended-activities {\n  margin: 20rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.activities-scroll {\n  width: 100%;\n  white-space: nowrap;\n}\n\n.activities-list {\n  display: flex;\n  padding: 10rpx 0;\n}\n\n.activity-card {\n  width: 400rpx;\n  margin-right: 20rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  background-color: #fff;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n  display: inline-block;\n}\n\n.activity-image {\n  width: 100%;\n  height: 200rpx;\n}\n\n.activity-info {\n  padding: 16rpx;\n}\n\n.activity-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 6rpx;\n  display: block;\n  white-space: normal;\n}\n\n.activity-desc {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  display: block;\n  white-space: normal;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.activity-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.activity-time {\n  font-size: 22rpx;\n  color: #999;\n}\n\n.activity-tag {\n  font-size: 22rpx;\n  color: #3a7afe;\n  background-color: #f0f5ff;\n  padding: 2rpx 10rpx;\n  border-radius: 4rpx;\n}\n\n/* 空状态 */\n.empty-view {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60rpx 0;\n}\n\n.empty-icon {\n  width: 160rpx;\n  height: 160rpx;\n  margin-bottom: 20rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/user/pages/my-benefits.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "smartNavigate", "getLocalUserInfo"], "mappings": ";;;;;;;;AAwIA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,UAAU;AAAA,MACV,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,qBAAqB;AAAA,IACvB,CAAC;AAGD,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AAGD,UAAM,wBAAwBA,cAAAA,IAAI;AAAA,MAChC;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAGhC;AAGA;IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BC,uBAAAA,cAAc,GAAG,EAAE,MAAM,SAAO;AAC9BD,4FAAc,WAAW,GAAG;AAAA,MAChC,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AAExB,YAAM,gBAAgBE,kBAAAA;AACtB,UAAI,eAAe;AACjB,iBAAS,QAAQ;AAAA,MAClB;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAG7BF,oBAAAA,MAAY,MAAA,OAAA,iDAAA,QAAQ;AAAA,IACtB;AAGA,UAAM,kBAAkB,MAAM;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAClC,cAAO,KAAK,MAAI;AAAA,QACd,KAAK;AACH,qBAAW,wBAAwB;AACnC;AAAA,QACF,KAAK;AACH,qBAAW,4BAA4B;AACvC;AAAA,QACF,KAAK;AACH,qBAAW,mCAAmC;AAC9C;AAAA,QACF;AACEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,MACJ;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAC/B,cAAO,MAAI;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnC,cAAO,QAAM;AAAA,QACX,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtTA,GAAG,WAAW,eAAe;"}