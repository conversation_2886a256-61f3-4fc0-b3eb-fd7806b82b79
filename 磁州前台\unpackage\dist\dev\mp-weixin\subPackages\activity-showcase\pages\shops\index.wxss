
.shops-container.data-v-9c228401 {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar.data-v-9c228401 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}
.navbar-bg.data-v-9c228401 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
}
.navbar-content.data-v-9c228401 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: var(--status-bar-height) 30rpx 0;
}
.back-btn.data-v-9c228401, .search-btn.data-v-9c228401 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title.data-v-9c228401 {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.navbar-right.data-v-9c228401 {
  display: flex;
  align-items: center;
}

/* 搜索和筛选栏样式 */
.search-filter-bar.data-v-9c228401 {
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  padding: 20rpx;
  margin-top: calc(var(--status-bar-height) + 90rpx);
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.search-box.data-v-9c228401 {
  display: flex;
  align-items: center;
  height: 70rpx;
  background: #F5F5F5;
  border-radius: 35rpx;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}
.search-icon.data-v-9c228401 {
  margin-right: 10rpx;
}
.search-placeholder.data-v-9c228401 {
  font-size: 28rpx;
  color: #999999;
}
.filter-options.data-v-9c228401 {
  display: flex;
  justify-content: space-around;
}
.filter-option.data-v-9c228401 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  padding: 10rpx 0;
}
.filter-option.active.data-v-9c228401 {
  color: #FF9500;
  font-weight: 500;
}
.sort-icon.data-v-9c228401, .filter-icon.data-v-9c228401 {
  margin-left: 6rpx;
}

/* 分类标签栏样式 */
.category-scroll.data-v-9c228401 {
  background: #FFFFFF;
  padding: 20rpx 0;
  white-space: nowrap;
  border-bottom: 1rpx solid #EEEEEE;
}
.category-list.data-v-9c228401 {
  display: flex;
  padding: 0 20rpx;
}
.category-item.data-v-9c228401 {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666666;
  background: #F5F5F5;
}
.category-item.active.data-v-9c228401 {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
  border: 1rpx solid rgba(255, 149, 0, 0.3);
}

/* 商铺列表样式 */
.shops-scroll.data-v-9c228401 {
  height: calc(100vh - var(--status-bar-height) - 90rpx - 180rpx - 80rpx);
}
.shops-list.data-v-9c228401 {
  padding: 20rpx;
}
.shop-card.data-v-9c228401 {
  display: flex;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.shop-logo.data-v-9c228401 {
  width: 180rpx;
  height: 180rpx;
  object-fit: cover;
}
.shop-info.data-v-9c228401 {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}
.shop-header.data-v-9c228401 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.shop-name.data-v-9c228401 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.shop-rating.data-v-9c228401 {
  display: flex;
  align-items: center;
}
.rating-value.data-v-9c228401 {
  font-size: 28rpx;
  color: #FF9500;
  font-weight: 500;
  margin-right: 6rpx;
}
.shop-meta.data-v-9c228401 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}
.meta-item.data-v-9c228401 {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}
.meta-icon.data-v-9c228401 {
  margin-right: 6rpx;
}
.meta-text.data-v-9c228401 {
  font-size: 24rpx;
  color: #999999;
}
.shop-tags.data-v-9c228401 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}
.shop-tag.data-v-9c228401 {
  font-size: 22rpx;
  color: #666666;
  background: #F5F5F5;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}
.more-tag.data-v-9c228401 {
  font-size: 22rpx;
  color: #999999;
  padding: 4rpx 12rpx;
}
.shop-promotion.data-v-9c228401 {
  display: flex;
  align-items: center;
  margin-top: auto;
}
.promotion-tag.data-v-9c228401 {
  font-size: 22rpx;
  color: #FFFFFF;
  background: #FF9500;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}
.promotion-text.data-v-9c228401 {
  font-size: 24rpx;
  color: #FF9500;
}

/* 加载更多样式 */
.loading-more.data-v-9c228401 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}
.loading-spinner.data-v-9c228401 {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #EEEEEE;
  border-top-color: #FF9500;
  border-radius: 50%;
  animation: spin-9c228401 1s linear infinite;
  margin-right: 10rpx;
}
@keyframes spin-9c228401 {
to {
    transform: rotate(360deg);
}
}
.loading-text.data-v-9c228401 {
  font-size: 28rpx;
  color: #999999;
}

/* 空状态样式 */
.empty-state.data-v-9c228401 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image.data-v-9c228401 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-9c228401 {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.empty-state .action-btn.data-v-9c228401 {
  padding: 15rpx 60rpx;
  font-size: 28rpx;
  color: #FFFFFF;
}

/* 筛选弹窗样式 */
.filter-popup.data-v-9c228401 {
  background: #FFFFFF;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  padding: 30rpx;
  max-height: 70vh;
}
.filter-header.data-v-9c228401 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.filter-title.data-v-9c228401 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.filter-close.data-v-9c228401 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-content.data-v-9c228401 {
  max-height: calc(70vh - 180rpx);
  overflow-y: auto;
}
.filter-section.data-v-9c228401 {
  margin-bottom: 30rpx;
}
.section-title.data-v-9c228401 {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.filter-options.data-v-9c228401 {
  display: flex;
  flex-wrap: wrap;
}
.filter-option.data-v-9c228401 {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666666;
  background: #F5F5F5;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.filter-option.active.data-v-9c228401 {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
  border: 1rpx solid rgba(255, 149, 0, 0.3);
}
.filter-footer.data-v-9c228401 {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #F0F0F0;
}
.filter-reset.data-v-9c228401, .filter-apply.data-v-9c228401 {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}
.filter-reset.data-v-9c228401 {
  background: #F5F5F5;
  color: #666666;
  margin-right: 20rpx;
}
.filter-apply.data-v-9c228401 {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.2);
}
