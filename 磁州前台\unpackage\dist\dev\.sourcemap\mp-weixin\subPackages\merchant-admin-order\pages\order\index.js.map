{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin-order/pages/order/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tb3JkZXJccGFnZXNcb3JkZXJcaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"order-management-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">订单管理</text>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 订单管理视图选择 -->\n    <view class=\"view-selector\">\n      <view \n        v-for=\"(item, index) in viewOptions\" \n        :key=\"index\" \n        class=\"view-option\"\n        :class=\"{'active': currentView === item.value}\"\n        @click=\"switchView(item.value)\">\n        <view class=\"view-icon-container\" :class=\"'bg-' + item.value\">\n        </view>\n        <text class=\"view-name\">{{item.name}}</text>\n      </view>\n    </view>\n    \n    <!-- 订单筛选条件 -->\n    <view class=\"filter-section\">\n      <scroll-view scroll-x=\"true\" class=\"filter-scroll\">\n        <view \n          v-for=\"(status, index) in orderStatuses\" \n          :key=\"index\"\n          :class=\"['status-tag', {'active': currentStatus === status.value}]\"\n          @click=\"filterByStatus(status.value)\">\n          {{status.name}}\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 主要内容区域 -->\n    <view class=\"content-main\">\n      <block v-if=\"orders.length > 0\">\n        <!-- 列表视图 -->\n        <view v-if=\"currentView === 'list'\" class=\"order-list\">\n          <navigator \n            v-for=\"(order, index) in orders\" \n            :key=\"index\"\n            :url=\"`./detail?id=${order.id}`\"\n            class=\"order-item\">\n            <view class=\"order-header\">\n              <text class=\"order-number\">订单号: {{order.orderNo}}</text>\n              <view class=\"order-status-tag\" :class=\"'status-' + order.status\">\n                {{getStatusText(order.status)}}\n              </view>\n            </view>\n            <view class=\"order-info\">\n              <view class=\"customer-info\">\n                <text class=\"label\">客户:</text>\n                <text class=\"value\">{{order.customerName}}</text>\n              </view>\n              <view class=\"time-info\">\n                <text class=\"label\">下单时间:</text>\n                <text class=\"value\">{{order.createTime}}</text>\n              </view>\n              <view class=\"amount-info\">\n                <text class=\"label\">订单金额:</text>\n                <text class=\"value price\">¥{{order.totalAmount}}</text>\n              </view>\n            </view>\n            <view class=\"order-actions\">\n              <view class=\"action-btn primary\" @click.stop=\"handleOrder(order.id)\">处理订单</view>\n              <view class=\"action-btn\" @click.stop=\"contactCustomer(order.id)\">联系客户</view>\n            </view>\n          </navigator>\n        </view>\n        \n        <!-- 详情视图占位 -->\n        <view v-else-if=\"currentView === 'detail'\" class=\"order-detail-view\">\n          <text>详情视图正在开发中...</text>\n          <button class=\"nav-btn\" @click=\"navigateTo('./detail')\">查看订单详情示例</button>\n        </view>\n        \n        <!-- 日历视图占位 -->\n        <view v-else-if=\"currentView === 'calendar'\" class=\"order-calendar-view\">\n          <text>日历视图正在开发中...</text>\n          <button class=\"nav-btn\" @click=\"navigateTo('./calendar')\">查看日历视图示例</button>\n        </view>\n      </block>\n      \n      <!-- 空状态 -->\n      <view v-else class=\"empty-state\">\n        <view class=\"empty-icon-container\">\n        <view class=\"empty-icon\">📭</view>\n        </view>\n        <text class=\"empty-text\">暂无订单数据</text>\n        <view class=\"refresh-btn\" @click=\"loadOrders\">刷新</view>\n      </view>\n    </view>\n    \n    <!-- 底部导航 -->\n    <view class=\"tab-bar\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(item, index) in tabItems\" \n        :key=\"index\"\n        :class=\"{ active: currentTab === item.id }\"\n        @tap=\"switchTab(item.id)\">\n        <view class=\"active-indicator\" v-if=\"currentTab === item.id\"></view>\n        <view class=\"tab-icon\" :class=\"item.icon\"></view>\n        <text class=\"tab-text\">{{item.text}}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      currentView: 'list', // list, detail, calendar\n      currentStatus: 'all',\n      viewOptions: [\n        { name: '列表视图', value: 'list', icon: '📋' },\n        { name: '详情视图', value: 'detail', icon: '📝' },\n        { name: '日历视图', value: 'calendar', icon: '📅' }\n      ],\n      orderStatuses: [\n        { name: '全部', value: 'all' },\n        { name: '待处理', value: 'pending' },\n        { name: '处理中', value: 'processing' },\n        { name: '已完成', value: 'completed' },\n        { name: '已取消', value: 'cancelled' },\n        { name: '退款中', value: 'refunding' }\n      ],\n      orders: [\n        {\n          id: '1001',\n          orderNo: 'CZ20230501001',\n          status: 'pending',\n          customerName: '张三',\n          createTime: '2023-05-01 10:30',\n          totalAmount: '128.00'\n        },\n        {\n          id: '1002',\n          orderNo: 'CZ20230501002',\n          status: 'processing',\n          customerName: '李四',\n          createTime: '2023-05-01 11:45',\n          totalAmount: '256.50'\n        },\n        {\n          id: '1003',\n          orderNo: 'CZ20230502003',\n          status: 'completed',\n          customerName: '王五',\n          createTime: '2023-05-02 09:15',\n          totalAmount: '89.90'\n        }\n      ],\n      currentTab: 3, // 当前是订单管理\n      tabItems: [\n        {\n          id: 0,\n          icon: 'dashboard',\n          text: '商家中心',\n          url: '/subPackages/merchant-admin-home/pages/merchant-home/index'\n        },\n        {\n          id: 1,\n          icon: 'store',\n          text: '店铺管理',\n          url: '/subPackages/merchant-admin/pages/store/index'\n        },\n        {\n          id: 2,\n          icon: 'marketing',\n          text: '营销中心',\n          url: '/subPackages/merchant-admin-marketing/pages/marketing/index'\n        },\n        {\n          id: 3,\n          icon: 'orders',\n          text: '订单管理',\n          url: '/subPackages/merchant-admin-order/pages/order/index'\n        },\n        {\n          id: 'more',\n          icon: 'more',\n          text: '更多',\n          url: ''\n        }\n      ]\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    switchView(view) {\n      this.currentView = view;\n    },\n    filterByStatus(status) {\n      this.currentStatus = status;\n      // 这里应该根据状态筛选订单\n      this.loadOrders();\n    },\n    showFilterOptions() {\n      uni.showToast({\n        title: '筛选功能开发中',\n        icon: 'none'\n      });\n    },\n    loadOrders() {\n      // 这里应该从服务器加载订单数据\n      console.log('加载订单数据，状态:', this.currentStatus);\n    },\n    handleOrder(orderId) {\n      uni.navigateTo({\n        url: `./detail?id=${orderId}&action=process`\n      });\n    },\n    contactCustomer(orderId) {\n      uni.showToast({\n        title: '联系客户功能开发中',\n        icon: 'none'\n      });\n    },\n    // 切换底部导航栏\n    switchTab(tabId) {\n      // 处理\"更多\"选项\n      if (tabId === 'more') {\n        this.showMoreOptions();\n        return;\n      }\n      \n      if (tabId === this.currentTab) return;\n      \n      this.currentTab = tabId;\n      \n      // 特殊处理订单管理标签\n      if (tabId === 3) {\n        // 当前已经在订单管理页面，不需要跳转\n        return;\n      }\n      \n      // 使用redirectTo而不是navigateTo，避免堆栈过多\n      uni.redirectTo({\n        url: this.tabItems[tabId].url,\n        fail: (err) => {\n          console.error('redirectTo失败:', err);\n          // 如果redirectTo失败，尝试使用switchTab\n          uni.switchTab({\n            url: this.tabItems[tabId].url,\n            fail: (switchErr) => {\n              console.error('switchTab也失败:', switchErr);\n              uni.showToast({\n                title: '页面跳转失败，请稍后再试',\n                icon: 'none'\n              });\n            }\n          });\n        }\n      });\n    },\n    \n    // 显示更多选项弹出菜单\n    showMoreOptions() {\n      // 准备更多菜单中的选项\n      const moreOptions = ['客户运营', '分析洞察', '系统设置'];\n      \n      uni.showActionSheet({\n        itemList: moreOptions,\n        success: (res) => {\n          // 根据选择的选项进行跳转\n          const routes = [\n            '/subPackages/merchant-admin-customer/pages/customer/index',\n            '/subPackages/merchant-admin/pages/settings/index'\n          ];\n          uni.navigateTo({\n            url: routes[res.tapIndex]\n          });\n        }\n      });\n    },\n    getStatusColor(status) {\n      const colors = {\n        pending: '#FF9800',\n        processing: '#2196F3',\n        completed: '#4CAF50',\n        cancelled: '#9E9E9E',\n        refunding: '#F44336'\n      };\n      return colors[status] || '#333333';\n    },\n    getStatusText(status) {\n      const texts = {\n        pending: '待处理',\n        processing: '处理中',\n        completed: '已完成',\n        cancelled: '已取消',\n        refunding: '退款中'\n      };\n      return texts[status] || '未知状态';\n    },\n    navigateTo(url) {\n      uni.navigateTo({ url });\n    }\n  },\n  onLoad() {\n    this.loadOrders();\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.order-management-container {\n  min-height: 100vh;\n  position: relative;\n  background-color: #f5f8fc;\n  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 80px); /* 增加底部间距，避免被底部导航栏遮挡 */\n  display: flex;\n  flex-direction: column;\n}\n\n.navbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 44px 16px 10px;\n  background: linear-gradient(135deg, #1677FF, #065DD2);\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  border-radius: 0;\n}\n\n.navbar-back {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  background: transparent;\n  border-radius: 0;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n  margin-left: 16px;\n}\n\n.navbar-title {\n  color: #fff;\n  font-size: 18px;\n  font-weight: 700; /* 增加字体粗细 */\n  flex: 1;\n  text-align: center;\n}\n\n.navbar-right {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.view-selector {\n  display: flex;\n  background-color: #fff;\n  padding: 16px 0;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.05);\n  border-radius: 20px;\n  margin: 90px 16px 0;\n  z-index: 10;\n  position: relative;\n}\n\n.view-option {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 8px 0;\n  position: relative;\n}\n\n.view-option.active {\n  color: #1677FF;\n}\n\n.view-option.active::after {\n  content: '';\n  position: absolute;\n  bottom: -8px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 20px;\n  height: 3px;\n  background-color: #1677FF;\n  border-radius: 3px;\n}\n\n.view-icon-container {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 4px;\n}\n\n.bg-list {\n  background: linear-gradient(135deg, #4CAF50, #2E7D32);\n}\n\n.bg-detail {\n  background: linear-gradient(135deg, #2196F3, #0D47A1);\n}\n\n.bg-calendar {\n  background: linear-gradient(135deg, #9C27B0, #6A1B9A);\n}\n\n.view-name {\n  font-size: 12px;\n  margin-top: 4px;\n}\n\n.filter-section {\n  display: flex;\n  padding: 16px;\n  background-color: #fff;\n  margin: 16px 16px 0;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.05);\n  border-radius: 16px;\n}\n\n.filter-scroll {\n  flex: 1;\n  white-space: nowrap;\n}\n\n.status-tag {\n  display: inline-block;\n  padding: 8px 16px;\n  margin-right: 8px;\n  background-color: #f0f0f0;\n  border-radius: 24px;\n  font-size: 12px;\n  color: #666;\n  transition: all 0.3s ease;\n}\n\n.status-tag.active {\n  background-color: #e6f7ff;\n  color: #1677FF;\n  border: 1px solid #91caff;\n  font-weight: 500;\n  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);\n}\n\n.filter-button {\n  display: flex;\n  align-items: center;\n  padding: 0 12px;\n  margin-left: 8px;\n  border-left: 1px solid #eee;\n}\n\n.filter-icon-container {\n  width: 32px;\n  height: 32px;\n  border-radius: 16px;\n  background: linear-gradient(135deg, #FF9800, #F57C00);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 8px;\n}\n\n.filter-icon {\n  font-size: 16px;\n}\n\n.content-main {\n  flex: 1;\n  padding: 16px;\n  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 40px); /* 增加底部内边距，避免被底部导航栏遮挡 */\n  overflow-y: auto;\n}\n\n.order-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.order-item {\n  background-color: #fff;\n  border-radius: 16px;\n  padding: 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.05);\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n}\n\n.order-item:active {\n  transform: scale(0.98);\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n}\n\n.order-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n  padding-bottom: 12px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.order-number {\n  font-size: 14px;\n  color: #333;\n  font-weight: 600;\n}\n\n.order-status-tag {\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n  color: #fff;\n}\n\n.status-pending {\n  background: linear-gradient(135deg, #FF9800, #F57C00);\n}\n\n.status-processing {\n  background: linear-gradient(135deg, #2196F3, #1976D2);\n}\n\n.status-completed {\n  background: linear-gradient(135deg, #4CAF50, #2E7D32);\n}\n\n.status-cancelled {\n  background: linear-gradient(135deg, #9E9E9E, #616161);\n}\n\n.status-refunding {\n  background: linear-gradient(135deg, #F44336, #C62828);\n}\n\n.order-info {\n  margin-bottom: 16px;\n  background-color: #f9f9f9;\n  padding: 12px;\n  border-radius: 12px;\n}\n\n.customer-info, .time-info, .amount-info {\n  display: flex;\n  margin-bottom: 8px;\n}\n\n.amount-info {\n  margin-bottom: 0;\n}\n\n.label {\n  width: 70px;\n  color: #666;\n  font-size: 13px;\n}\n\n.value {\n  flex: 1;\n  font-size: 13px;\n  color: #333;\n}\n\n.price {\n  font-weight: 700;\n  color: #ff6a00;\n}\n\n.order-actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.action-btn {\n  padding: 8px 16px;\n  border-radius: 24px;\n  font-size: 13px;\n  margin-left: 12px;\n  background-color: #f0f0f0;\n  color: #333;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.action-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 1px 2px rgba(0,0,0,0.1);\n}\n\n.action-btn.primary {\n  background: linear-gradient(135deg, #1677FF, #0D47A1);\n  color: #fff;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 40px 0;\n}\n\n.empty-icon-container {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #E0E0E0, #BDBDBD);\n  border-radius: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16px;\n}\n\n.empty-icon {\n  font-size: 32px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #999;\n  margin-bottom: 24px;\n}\n\n.refresh-btn {\n  padding: 12px 24px;\n  background: linear-gradient(135deg, #1677FF, #0D47A1);\n  color: #fff;\n  border-radius: 24px;\n  font-size: 14px;\n  font-weight: 500;\n  box-shadow: 0 4px 8px rgba(22, 119, 255, 0.2);\n  transition: all 0.3s ease;\n}\n\n.refresh-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);\n}\n\n.order-detail-view, .order-calendar-view {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  background-color: #fff;\n  border-radius: 16px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.05);\n}\n\n.nav-btn {\n  margin-top: 20px;\n  background: linear-gradient(135deg, #1677FF, #0D47A1);\n  color: #fff;\n  font-size: 14px;\n  padding: 10px 24px;\n  border-radius: 24px;\n  border: none;\n  font-weight: 500;\n  box-shadow: 0 4px 8px rgba(22, 119, 255, 0.2);\n  transition: all 0.3s ease;\n}\n\n.tab-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 60px;\n  background-color: #FFFFFF;\n  border-top: 1px solid var(--border-light);\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  padding: 0 10px;\n  padding-bottom: env(safe-area-inset-bottom);\n  flex-shrink: 0;\n  border-radius: 0;\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  position: relative;\n  transition: all 0.2s ease;\n  padding: 8px 0;\n  margin: 0 8px;\n}\n\n.tab-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 3px;\n  transition: all 0.25s cubic-bezier(0.3, 0.7, 0.4, 1.5);\n  background-position: center;\n  background-repeat: no-repeat;\n  background-size: 22px;\n  opacity: 0.7;\n}\n\n.tab-text {\n  font-size: 10px;\n  color: var(--text-tertiary);\n  transition: color 0.2s ease, transform 0.25s ease;\n  transform: scale(0.9);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n  padding: 0 2px;\n}\n\n.active-indicator {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 16px;\n  height: 3px;\n  border-radius: 0 0 4px 4px;\n  background: linear-gradient(90deg, var(--brand-primary), var(--accent-purple));\n}\n\n.tab-item.active .tab-icon {\n  transform: translateY(-2px);\n  opacity: 1;\n}\n\n.tab-item.active .tab-text {\n  color: var(--brand-primary);\n  font-weight: 500;\n  transform: scale(1);\n}\n\n/* 导航图标样式 */\n.tab-icon.dashboard {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.store {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.marketing {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.orders {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E\");\n}\n\n.tab-icon.more {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n@media screen and (max-width: 375px) {\n  /* 在较小屏幕上优化导航栏 */\n  .tab-text {\n    font-size: 9px;\n  }\n  \n  .tab-icon {\n    margin-bottom: 2px;\n    background-size: 20px;\n  }\n}\n\n/* 在较宽屏幕上优化导航栏，确保元素不会挤压 */\n@media screen and (min-width: 400px) {\n  .tab-bar {\n    padding: 0 10px;\n  }\n  \n  .tab-item {\n    margin: 0 5px;\n  }\n}\n\n/* 添加底部安全区域 */\n.safe-area-bottom {\n  height: calc(60px + env(safe-area-inset-bottom));\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-order/pages/order/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAmHA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA,QACX,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,KAAM;AAAA,QAC3C,EAAE,MAAM,QAAQ,OAAO,UAAU,MAAM,KAAM;AAAA,QAC7C,EAAE,MAAM,QAAQ,OAAO,YAAY,MAAM,KAAK;AAAA,MAC/C;AAAA,MACD,eAAe;AAAA,QACb,EAAE,MAAM,MAAM,OAAO,MAAO;AAAA,QAC5B,EAAE,MAAM,OAAO,OAAO,UAAW;AAAA,QACjC,EAAE,MAAM,OAAO,OAAO,aAAc;AAAA,QACpC,EAAE,MAAM,OAAO,OAAO,YAAa;AAAA,QACnC,EAAE,MAAM,OAAO,OAAO,YAAa;AAAA,QACnC,EAAE,MAAM,OAAO,OAAO,YAAY;AAAA,MACnC;AAAA,MACD,QAAQ;AAAA,QACN;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,aAAa;AAAA,QACf;AAAA,MACD;AAAA,MACD,YAAY;AAAA;AAAA,MACZ,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW,MAAM;AACf,WAAK,cAAc;AAAA,IACpB;AAAA,IACD,eAAe,QAAQ;AACrB,WAAK,gBAAgB;AAErB,WAAK,WAAU;AAAA,IAChB;AAAA,IACD,oBAAoB;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa;AAEXA,wGAAY,cAAc,KAAK,aAAa;AAAA,IAC7C;AAAA,IACD,YAAY,SAAS;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,eAAe,OAAO;AAAA,MAC7B,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB,SAAS;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAED,UAAU,OAAO;AAEf,UAAI,UAAU,QAAQ;AACpB,aAAK,gBAAe;AACpB;AAAA,MACF;AAEA,UAAI,UAAU,KAAK;AAAY;AAE/B,WAAK,aAAa;AAGlB,UAAI,UAAU,GAAG;AAEf;AAAA,MACF;AAGAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,KAAK,SAAS,KAAK,EAAE;AAAA,QAC1B,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,iEAAc,iBAAiB,GAAG;AAElCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK,KAAK,SAAS,KAAK,EAAE;AAAA,YAC1B,MAAM,CAAC,cAAc;AACnBA,4BAAA,MAAA,MAAA,SAAA,iEAAc,iBAAiB,SAAS;AACxCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAEhB,YAAM,cAAc,CAAC,QAAQ,QAAQ,MAAM;AAE3CA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAEhB,gBAAM,SAAS;AAAA,YACb;AAAA,YACA;AAAA;AAEFA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,OAAO,IAAI,QAAQ;AAAA,UAC1B,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,eAAe,QAAQ;AACrB,YAAM,SAAS;AAAA,QACb,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA;AAEb,aAAO,OAAO,MAAM,KAAK;AAAA,IAC1B;AAAA,IACD,cAAc,QAAQ;AACpB,YAAM,QAAQ;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA;AAEb,aAAO,MAAM,MAAM,KAAK;AAAA,IACzB;AAAA,IACD,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW,EAAE,IAAE,CAAG;AAAA,IACxB;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,WAAU;AAAA,EACjB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtTA,GAAG,WAAW,eAAe;"}