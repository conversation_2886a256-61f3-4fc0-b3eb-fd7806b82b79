
.publish-container.data-v-5405ed41 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.custom-navbar.data-v-5405ed41 {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 10;
}
.navbar-title.data-v-5405ed41 {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  z-index: 1;
}
.navbar-left.data-v-5405ed41, .navbar-right.data-v-5405ed41 {
  position: absolute;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}
.navbar-left.data-v-5405ed41 {
  left: 20rpx;
  width: 60rpx;
}
.navbar-right.data-v-5405ed41 {
  right: 20rpx;
  width: 60rpx;
}
.back-icon.data-v-5405ed41 {
  width: 40rpx;
  height: 40rpx;
  background: none;
}
.tab-container.data-v-5405ed41 {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  background: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #eee;
}
.tab-item.data-v-5405ed41 {
  position: relative;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 25%;
  box-sizing: border-box;
}
.tab-text.data-v-5405ed41 {
  font-size: 30rpx;
  color: #666;
}
.tab-item.active .tab-text.data-v-5405ed41 {
  color: #0052d9;
  font-weight: bold;
}
.active-line.data-v-5405ed41 {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 6rpx;
  background: #0052d9;
  border-radius: 3rpx;
}
.list-container.data-v-5405ed41 {
  padding: 20rpx;
}
.info-card.data-v-5405ed41 {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.info-header.data-v-5405ed41 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.info-title.data-v-5405ed41 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.info-status.data-v-5405ed41 {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
}
.status-reviewing.data-v-5405ed41 {
  background: #FFF8E6;
  color: #FF9900;
}
.status-passed.data-v-5405ed41 {
  background: #E6F7E6;
  color: #52C41A;
}
.status-expired.data-v-5405ed41 {
  background: #F0F0F0;
  color: #999;
}
.info-content.data-v-5405ed41 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}
.info-stats.data-v-5405ed41 {
  display: flex;
  color: #999;
  font-size: 24rpx;
  margin-bottom: 30rpx;
}
.info-date.data-v-5405ed41 {
  margin-right: 30rpx;
}
.info-views.data-v-5405ed41 {
  margin-right: 30rpx;
}
.info-actions.data-v-5405ed41 {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 8px;
}
.activity-btn.data-v-5405ed41 {
  height: 28px;
  padding: 0 12px;
  border-radius: 14px;
  font-size: 12px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);
  border: 0.5px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ECF5FF 0%, #E2F0FF 100%);
  color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
}
.activity-btn.data-v-5405ed41::before {
  display: none;
}
.activity-btn.data-v-5405ed41::after {
  display: none;
}
.activity-btn.data-v-5405ed41:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #E2F0FF 0%, #D8EAFF 100%);
}
.activity-btn[data-type="edit"].data-v-5405ed41 {
  background: linear-gradient(135deg, #EEF6FF 0%, #E2F0FF 100%);
  color: #0070E0;
}
.activity-btn[data-type="promote"].data-v-5405ed41 {
  background: linear-gradient(135deg, #FFF2E6 0%, #FFE8D1 100%);
  color: #FF7D00;
}
.activity-btn[data-type="share"].data-v-5405ed41 {
  background: linear-gradient(135deg, #E9F9F0 0%, #DCF2E5 100%);
  color: #27AE60;
}
.empty-container.data-v-5405ed41 {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon.data-v-5405ed41 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-5405ed41 {
  font-size: 30rpx;
  color: #999;
}

/* 置顶卡片样式 */
.top-overlay.data-v-5405ed41 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.top-card.data-v-5405ed41 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  width: 85%;
  max-width: 650rpx;
}
.top-title.data-v-5405ed41 {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}
.top-block.data-v-5405ed41 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
}
.ad-block.data-v-5405ed41 {
  background-color: #F8FFF9;
}
.pay-block.data-v-5405ed41 {
  background-color: #FFF9F0;
}
.block-left.data-v-5405ed41 {
  flex: 1;
}
.block-main.data-v-5405ed41 {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}
.block-sub.data-v-5405ed41 {
  font-size: 24rpx;
  color: #999;
}
.block-right.data-v-5405ed41 {
  display: flex;
  align-items: center;
}
.arrow.data-v-5405ed41 {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #999;
}
.ad-tips-overlay.data-v-5405ed41, .ad-overlay.data-v-5405ed41 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ad-tips-card.data-v-5405ed41 {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}
.ad-tips-title.data-v-5405ed41 {
  font-size: 34rpx;
  font-weight: bold;
  text-align: center;
  padding: 30rpx 0;
  color: #333;
  border-bottom: 1rpx solid #eee;
}
.ad-tips-content.data-v-5405ed41 {
  padding: 30rpx;
}
.ad-tips-item.data-v-5405ed41 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}
.highlight.data-v-5405ed41 {
  color: #FF6600;
  font-weight: bold;
}
.ad-tips-btns.data-v-5405ed41 {
  display: flex;
  border-top: 1rpx solid #eee;
}
.ad-tips-btn.data-v-5405ed41 {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
}
.ad-tips-btn.cancel.data-v-5405ed41 {
  color: #999;
  border-right: 1rpx solid #eee;
}
.ad-tips-btn.confirm.data-v-5405ed41 {
  color: #0066FF;
  font-weight: bold;
}
.ad-container.data-v-5405ed41 {
  width: 90%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}
.ad-header.data-v-5405ed41 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
}
.ad-title.data-v-5405ed41 {
  font-size: 28rpx;
  color: #999;
}
.ad-countdown.data-v-5405ed41 {
  font-size: 26rpx;
  color: #FF6600;
  font-weight: bold;
}
.ad-content.data-v-5405ed41 {
  position: relative;
  height: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.ad-image.data-v-5405ed41 {
  width: 100%;
  height: 100%;
}
.ad-message.data-v-5405ed41 {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
}
.ad-footer.data-v-5405ed41 {
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
}
.ad-tips.data-v-5405ed41 {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}
.ad-close-btn.data-v-5405ed41 {
  align-self: center;
  width: 80%;
  text-align: center;
  padding: 16rpx 0;
  border-radius: 50rpx;
  font-size: 28rpx;
  background-color: #f0f0f0;
  color: #999;
}
.ad-close-active.data-v-5405ed41 {
  background-color: #0066FF;
  color: #fff;
}
.top-result.data-v-5405ed41 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  pointer-events: none;
}
.top-result-content.data-v-5405ed41 {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 40rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.top-result-icon.data-v-5405ed41 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.top-result-text.data-v-5405ed41 {
  color: #fff;
  font-size: 30rpx;
}

/* 付费置顶选项弹出层 */
.top-options-dropdown.data-v-5405ed41 {
  background-color: #fff;
  width: 100%;
  border-radius: 0 0 20rpx 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  animation: dropDown-5405ed41 0.3s ease;
}
@keyframes dropDown-5405ed41 {
from { 
    opacity: 0;
    transform: translateY(-50%);
}
to { 
    opacity: 1;
    transform: translateY(0);
}
}
.top-options-title.data-v-5405ed41 {
  text-align: center;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
  position: relative;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.top-options-list.data-v-5405ed41 {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 10rpx;
}
.top-option-item.data-v-5405ed41 {
  width: 30%;
  height: 140rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  transition: all 0.2s;
}
.top-option-item.active.data-v-5405ed41 {
  background-color: #FFF5E6;
  border-color: #FF9500;
}
.top-option-days.data-v-5405ed41 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}
.top-option-price.data-v-5405ed41 {
  font-size: 28rpx;
  color: #FF9500;
  font-weight: 600;
}

/* 付费按钮样式优化 */
.pay-btn.data-v-5405ed41 {
  background-color: #FF9500;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

/* 免费按钮样式优化 */
.free-btn.data-v-5405ed41 {
  background-color: #34C759;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

/* 自定义弹窗样式 */
.custom-modal.data-v-5405ed41 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.65);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}
.modal-mask.data-v-5405ed41 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 1001;
}
.modal-content.data-v-5405ed41 {
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 16rpx;
  overflow: hidden;
  width: 85%;
  max-width: 600rpx;
  position: relative;
  z-index: 1002;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.25);
  transform: translateY(0);
  animation: modal-in-5405ed41 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}
@keyframes modal-in-5405ed41 {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.modal-title.data-v-5405ed41 {
  font-size: 34rpx;
  font-weight: 600;
  text-align: center;
  padding: 30rpx 0;
  color: #000;
  border-bottom: 0.5rpx solid rgba(60, 60, 67, 0.12);
}
.modal-body.data-v-5405ed41 {
  padding: 35rpx 30rpx;
}
.modal-text.data-v-5405ed41 {
  font-size: 28rpx;
  color: rgba(60, 60, 67, 0.85);
  margin-bottom: 20rpx;
  line-height: 1.5;
  text-align: center;
  letter-spacing: 0.5rpx;
}
.price-text.data-v-5405ed41 {
  font-size: 30rpx;
  color: rgba(60, 60, 67, 0.85);
  text-align: center;
  margin: 25rpx 0 15rpx;
  font-weight: 500;
}
.price-amount.data-v-5405ed41 {
  font-size: 40rpx;
  color: #FF2D55;
  font-weight: 600;
  margin: 0 6rpx;
}
.rank-hint.data-v-5405ed41 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 25rpx 0;
  font-size: 26rpx;
  color: rgba(60, 60, 67, 0.7);
  background-color: rgba(0, 122, 255, 0.08);
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
}
.rank-icon.data-v-5405ed41 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.special-hint.data-v-5405ed41 {
  font-size: 26rpx;
  color: #FF9500;
  text-align: center;
  margin: 18rpx 0 10rpx;
  font-weight: 500;
}
.modal-footer.data-v-5405ed41 {
  display: flex;
  justify-content: space-between;
  padding: 0;
  border-top: 0.5rpx solid rgba(60, 60, 67, 0.12);
}
.modal-btn.data-v-5405ed41 {
  flex: 1;
  text-align: center;
  padding: 25rpx 0;
  font-size: 32rpx;
  border-radius: 0;
  border: none;
  margin: 0;
  line-height: 1.5;
  font-weight: 500;
  transition: background-color 0.2s ease;
}
.modal-btn.data-v-5405ed41::after {
  border: none;
}
.cancel-btn.data-v-5405ed41 {
  background-color: rgba(242, 242, 247, 0.95);
  color: #007AFF;
}
.cancel-btn.data-v-5405ed41:active {
  background-color: rgba(229, 229, 234, 1);
}
.confirm-btn.data-v-5405ed41 {
  background-color: #007AFF;
  color: white;
}
.confirm-btn.data-v-5405ed41:active {
  background-color: #0062CC;
}
