<template>
  <view class="order-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg" :style="{
        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
      }"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">订单详情</view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y="true"
      :scroll-anchoring="true"
      :enhanced="true"
      :bounces="true"
      :show-scrollbar="false"
    >
      <!-- 订单状态卡片 -->
      <view class="status-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="status-header">
          <text class="status-text">{{ getOrderStatusText(orderDetail.status) }}</text>
          <text class="status-time">{{ orderDetail.statusUpdateTime }}</text>
        </view>
        <view class="status-info">
          <text class="status-desc">{{ getOrderStatusDesc(orderDetail.status) }}</text>
        </view>
      </view>

      <!-- 订单信息卡片 -->
      <view class="order-info-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-title">订单信息</view>
        <view class="info-item">
          <text class="info-label">订单编号</text>
          <view class="info-value-copy">
            <text class="info-value">{{ orderDetail.orderNumber }}</text>
            <view class="copy-btn" @click="copyOrderNumber">
              <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
        </view>
        <view class="info-item">
          <text class="info-label">下单时间</text>
          <text class="info-value">{{ orderDetail.orderTime }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">支付方式</text>
          <text class="info-value">{{ orderDetail.paymentMethod }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">支付时间</text>
          <text class="info-value">{{ orderDetail.paymentTime }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">订单来源</text>
          <text class="info-value">{{ orderDetail.orderSource }}</text>
        </view>
      </view>

      <!-- 商品信息卡片 -->
      <view class="product-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-title">商品信息</view>
        <view class="product-item">
          <image class="product-image" :src="orderDetail.productImage" mode="aspectFill"></image>
          <view class="product-info">
            <text class="product-name">{{ orderDetail.productName }}</text>
            <text class="product-specs">{{ orderDetail.productSpecs }}</text>
            <view class="product-price-qty">
              <text class="product-price">¥{{ orderDetail.productPrice }}</text>
              <text class="product-qty">x{{ orderDetail.quantity }}</text>
            </view>
          </view>
        </view>
        <view class="price-summary">
          <view class="price-item">
            <text class="price-label">商品总价</text>
            <text class="price-value">¥{{ orderDetail.totalProductPrice }}</text>
          </view>
          <view class="price-item">
            <text class="price-label">运费</text>
            <text class="price-value">¥{{ orderDetail.shippingFee }}</text>
          </view>
          <view class="price-item">
            <text class="price-label">优惠</text>
            <text class="price-value">-¥{{ orderDetail.discount }}</text>
          </view>
          <view class="price-item total">
            <text class="price-label">实付款</text>
            <text class="price-value">¥{{ orderDetail.actualPayment }}</text>
          </view>
        </view>
      </view>

      <!-- 佣金信息卡片 -->
      <view class="commission-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-title">佣金信息</view>
        <view class="commission-item">
          <text class="commission-label">佣金比例</text>
          <text class="commission-value">{{ orderDetail.commissionRate }}%</text>
        </view>
        <view class="commission-item">
          <text class="commission-label">预计佣金</text>
          <text class="commission-value highlight">¥{{ orderDetail.commission }}</text>
        </view>
        <view class="commission-item">
          <text class="commission-label">结算状态</text>
          <text class="commission-status" :class="orderDetail.settlementStatus">{{ getSettlementStatusText(orderDetail.settlementStatus) }}</text>
        </view>
        <view class="commission-item" v-if="orderDetail.settlementStatus === 'settled'">
          <text class="commission-label">结算时间</text>
          <text class="commission-value">{{ orderDetail.settlementTime }}</text>
        </view>
        <view class="commission-note">
          <text>注：订单完成后{{ settlementDays }}天自动结算佣金</text>
        </view>
      </view>

      <!-- 收货人信息卡片 -->
      <view class="receiver-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-title">收货人信息</view>
        <view class="receiver-info">
          <view class="receiver-header">
            <view class="receiver-name-phone">
              <text class="receiver-name">{{ orderDetail.receiverName }}</text>
              <text class="receiver-phone">{{ orderDetail.receiverPhone }}</text>
            </view>
            <view class="copy-btn" @click="copyAddress">
              <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
          <text class="receiver-address">{{ orderDetail.receiverAddress }}</text>
        </view>
      </view>

      <!-- 物流信息卡片 -->
      <view class="logistics-card" v-if="orderDetail.status === 'delivered' || orderDetail.status === 'completed'" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-title">物流信息</view>
        <view class="logistics-info">
          <view class="logistics-header">
            <text class="logistics-company">{{ orderDetail.logisticsCompany }}</text>
            <text class="logistics-number">{{ orderDetail.trackingNumber }}</text>
            <view class="copy-btn" @click="copyTrackingNumber">
              <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
          <view class="logistics-timeline">
            <view 
              v-for="(item, index) in orderDetail.logisticsInfo" 
              :key="index"
              class="timeline-item"
            >
              <view class="timeline-point" :class="{ active: index === 0 }"></view>
              <view class="timeline-content">
                <text class="timeline-status">{{ item.status }}</text>
                <text class="timeline-time">{{ item.time }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 结算天数
const settlementDays = ref(7);

// 订单详情数据
const orderDetail = ref({
  id: 'O001',
  orderNumber: '20230101001',
  status: 'completed',
  statusUpdateTime: '2023-01-05 12:00:00',
  orderTime: '2023-01-01 12:00:00',
  paymentMethod: '微信支付',
  paymentTime: '2023-01-01 12:05:30',
  orderSource: '分销推广',
  productName: 'iPhone 14 Pro 深空黑 256G',
  productImage: 'https://via.placeholder.com/100',
  productSpecs: '深空黑 256G',
  productPrice: 7999,
  quantity: 1,
  totalProductPrice: 7999,
  shippingFee: 0,
  discount: 200,
  actualPayment: 7799,
  commissionRate: 5,
  commission: 389.95,
  settlementStatus: 'pending', // pending, settled, failed
  settlementTime: '',
  receiverName: '张三',
  receiverPhone: '13800138000',
  receiverAddress: '北京市朝阳区三里屯街道123号',
  logisticsCompany: '顺丰速运',
  trackingNumber: 'SF1234567890',
  logisticsInfo: [
    { status: '已签收，签收人：张三', time: '2023-01-05 10:30:00' },
    { status: '派送中，派送员：李四，电话：13900139000', time: '2023-01-05 09:00:00' },
    { status: '到达【北京朝阳区三里屯营业点】', time: '2023-01-04 20:00:00' },
    { status: '到达【北京转运中心】', time: '2023-01-03 18:00:00' },
    { status: '已发货', time: '2023-01-02 14:00:00' },
    { status: '订单已出库', time: '2023-01-02 10:00:00' }
  ]
});

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 复制订单号
const copyOrderNumber = () => {
  uni.setClipboardData({
    data: orderDetail.value.orderNumber,
    success: () => {
      uni.showToast({
        title: '订单号已复制',
        icon: 'none'
      });
    }
  });
};

// 复制地址
const copyAddress = () => {
  const address = `${orderDetail.value.receiverName} ${orderDetail.value.receiverPhone} ${orderDetail.value.receiverAddress}`;
  uni.setClipboardData({
    data: address,
    success: () => {
      uni.showToast({
        title: '地址已复制',
        icon: 'none'
      });
    }
  });
};

// 复制物流单号
const copyTrackingNumber = () => {
  uni.setClipboardData({
    data: orderDetail.value.trackingNumber,
    success: () => {
      uni.showToast({
        title: '物流单号已复制',
        icon: 'none'
      });
    }
  });
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
  switch(status) {
    case 'pending_payment':
      return '待付款';
    case 'pending_delivery':
      return '待发货';
    case 'delivered':
      return '已发货';
    case 'completed':
      return '已完成';
    case 'invalid':
      return '已失效';
    default:
      return '未知状态';
  }
};

// 获取订单状态描述
const getOrderStatusDesc = (status) => {
  switch(status) {
    case 'pending_payment':
      return '请在24小时内完成支付，逾期订单将自动取消';
    case 'pending_delivery':
      return '商家正在处理您的订单，请耐心等待';
    case 'delivered':
      return '商品已发出，请注意查收';
    case 'completed':
      return '订单已完成，感谢您的购买';
    case 'invalid':
      return '订单已失效，如有疑问请联系客服';
    default:
      return '';
  }
};

// 获取结算状态文本
const getSettlementStatusText = (status) => {
  switch(status) {
    case 'pending':
      return '待结算';
    case 'settled':
      return '已结算';
    case 'failed':
      return '结算失败';
    default:
      return '未知状态';
  }
};

// 页面加载
onMounted(() => {
  // 获取订单ID
  const eventChannel = getOpenerEventChannel();
  if (eventChannel) {
    eventChannel.on('getOrderId', (data) => {
      // 根据订单ID获取订单详情
      // fetchOrderDetail(data.id);
      console.log('订单ID:', data.id);
    });
  }

  // 获取页面参数
  const query = uni.getLaunchOptionsSync().query;
  if (query && query.id) {
    // 根据订单ID获取订单详情
    // fetchOrderDetail(query.id);
    console.log('订单ID:', query.id);
  }
});

// 获取订单详情
const fetchOrderDetail = (id) => {
  // 这里应该是实际的API调用
  // 示例中使用模拟数据
};
</script>

<style lang="scss" scoped>
.order-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F8F8FA;
  position: relative;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  
  .navbar-bg {
    height: 180rpx;
    width: 100%;
  }
  
  .navbar-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height);
    
    .back-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .navbar-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #FFFFFF;
    }
  }
}

.content-scroll {
  flex: 1;
  padding: 30rpx;
  padding-top: calc(180rpx + 30rpx);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
}

.status-card {
  color: #FFFFFF;
  
  .status-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    
    .status-text {
      font-size: 36rpx;
      font-weight: 600;
    }
    
    .status-time {
      font-size: 24rpx;
    }
  }
  
  .status-info {
    .status-desc {
      font-size: 28rpx;
    }
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  .info-label {
    color: #666666;
    font-size: 28rpx;
  }
  
  .info-value {
    color: #333333;
    font-size: 28rpx;
  }
  
  .info-value-copy {
    display: flex;
    align-items: center;
    
    .copy-btn {
      margin-left: 10rpx;
      padding: 6rpx;
    }
  }
}

.product-item {
  display: flex;
  margin-bottom: 30rpx;
  
  .product-image {
    width: 160rpx;
    height: 160rpx;
    border-radius: 16rpx;
    margin-right: 20rpx;
  }
  
  .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    .product-name {
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 10rpx;
    }
    
    .product-specs {
      font-size: 24rpx;
      color: #999999;
      margin-bottom: 10rpx;
    }
    
    .product-price-qty {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .product-price {
        font-size: 32rpx;
        color: #333333;
        font-weight: 600;
      }
      
      .product-qty {
        font-size: 28rpx;
        color: #999999;
      }
    }
  }
}

.price-summary {
  border-top: 1px solid #EEEEEE;
  padding-top: 20rpx;
  
  .price-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
    
    .price-label {
      font-size: 28rpx;
      color: #666666;
    }
    
    .price-value {
      font-size: 28rpx;
      color: #333333;
    }
    
    &.total {
      margin-top: 20rpx;
      border-top: 1px solid #EEEEEE;
      padding-top: 20rpx;
      
      .price-label {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }
      
      .price-value {
        font-size: 32rpx;
        font-weight: 600;
        color: #FF3B69;
      }
    }
  }
}

.commission-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  
  .commission-label {
    font-size: 28rpx;
    color: #666666;
  }
  
  .commission-value {
    font-size: 28rpx;
    color: #333333;
    
    &.highlight {
      color: #FF3B69;
      font-weight: 600;
    }
  }
  
  .commission-status {
    font-size: 28rpx;
    
    &.pending {
      color: #FF9500;
    }
    
    &.settled {
      color: #34C759;
    }
    
    &.failed {
      color: #FF3B30;
    }
  }
}

.commission-note {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999999;
}

.receiver-info {
  .receiver-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .receiver-name-phone {
      display: flex;
      align-items: center;
      
      .receiver-name {
        font-size: 32rpx;
        color: #333333;
        font-weight: 600;
        margin-right: 20rpx;
      }
      
      .receiver-phone {
        font-size: 28rpx;
        color: #666666;
      }
    }
  }
  
  .receiver-address {
    font-size: 28rpx;
    color: #333333;
    line-height: 1.5;
  }
}

.logistics-info {
  .logistics-header {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    
    .logistics-company {
      font-size: 28rpx;
      color: #333333;
      font-weight: 600;
      margin-right: 20rpx;
    }
    
    .logistics-number {
      flex: 1;
      font-size: 28rpx;
      color: #666666;
    }
  }
  
  .logistics-timeline {
    .timeline-item {
      display: flex;
      margin-bottom: 30rpx;
      position: relative;
      
      &:not(:last-child)::before {
        content: '';
        position: absolute;
        top: 30rpx;
        left: 10rpx;
        width: 2rpx;
        height: calc(100% - 10rpx);
        background-color: #EEEEEE;
      }
      
      .timeline-point {
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background-color: #CCCCCC;
        margin-right: 20rpx;
        margin-top: 10rpx;
        
        &.active {
          background-color: #AC39FF;
          box-shadow: 0 0 0 6rpx rgba(172, 57, 255, 0.2);
        }
      }
      
      .timeline-content {
        flex: 1;
        
        .timeline-status {
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 10rpx;
          line-height: 1.5;
        }
        
        .timeline-time {
          font-size: 24rpx;
          color: #999999;
        }
      }
    }
  }
}

.safe-area-bottom {
  height: 50rpx;
}

/* 适配小屏幕手机 */
@media screen and (max-width: 375px) {
  .card-title {
    font-size: 28rpx;
  }
  
  .info-item {
    .info-label, .info-value {
      font-size: 24rpx;
    }
  }
  
  .product-item {
    .product-info {
      .product-name {
        font-size: 24rpx;
      }
      
      .product-specs {
        font-size: 22rpx;
      }
      
      .product-price-qty {
        .product-price {
          font-size: 28rpx;
        }
        
        .product-qty {
          font-size: 24rpx;
        }
      }
    }
  }
}
</style> 