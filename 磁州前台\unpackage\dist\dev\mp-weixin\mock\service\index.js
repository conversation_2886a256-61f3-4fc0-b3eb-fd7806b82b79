"use strict";
const mock_service_serviceList = require("./serviceList.js");
const mock_service_serviceDetail = require("./serviceDetail.js");
const mock_service_publishList = require("./publishList.js");
const serviceApi = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  cancelCollectService: mock_service_serviceDetail.cancelCollectService,
  collectService: mock_service_serviceDetail.collectService,
  fetchHotServices: mock_service_serviceList.fetchHotServices,
  fetchMoreReviews: mock_service_serviceDetail.fetchMoreReviews,
  fetchPublishDetail: mock_service_publishList.fetchPublishDetail,
  fetchPublishList: mock_service_publishList.fetchPublishList,
  fetchRelatedServices: mock_service_serviceDetail.fetchRelatedServices,
  fetchServiceCategories: mock_service_serviceList.fetchServiceCategories,
  fetchServiceDetail: mock_service_serviceDetail.fetchServiceDetail,
  fetchServiceList: mock_service_serviceList.fetchServiceList,
  publishList: mock_service_publishList.publishList,
  relatedServices: mock_service_serviceDetail.relatedServices,
  serviceCategories: mock_service_serviceList.serviceCategories,
  serviceDetail: mock_service_serviceDetail.serviceDetail,
  serviceList: mock_service_serviceList.serviceList
}, Symbol.toStringTag, { value: "Module" }));
exports.serviceApi = serviceApi;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/service/index.js.map
