{"version": 3, "file": "shop-test.js", "sources": ["pages/test/shop-test.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC9zaG9wLXRlc3QudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<text class=\"title\">商家页面跳转测试</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"test-list\">\r\n\t\t\t<view class=\"test-item\" v-for=\"shop in shopList\" :key=\"shop.id\" @click=\"goToShopDetail(shop.id)\">\r\n\t\t\t\t<text class=\"shop-name\">{{shop.shopName}}</text>\r\n\t\t\t\t<text class=\"shop-desc\">{{shop.category}} | {{shop.scale}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tshopList: [\r\n\t\t\t\t{\r\n\t\t\t\t\tid: \"1\",\r\n\t\t\t\t\tshopName: \"五分利电器\",\r\n\t\t\t\t\tcategory: \"数码电器\",\r\n\t\t\t\t\tscale: \"10-20人\"\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: \"2\",\r\n\t\t\t\t\tshopName: \"金鼎家居\",\r\n\t\t\t\t\tcategory: \"家居家装\",\r\n\t\t\t\t\tscale: \"20-50人\"\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: \"3\",\r\n\t\t\t\t\tshopName: \"鲜丰水果\",\r\n\t\t\t\t\tcategory: \"生鲜果蔬\",\r\n\t\t\t\t\tscale: \"5-10人\"\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: \"4\",\r\n\t\t\t\t\tshopName: \"磁州书院\",\r\n\t\t\t\t\tcategory: \"文化教育\",\r\n\t\t\t\t\tscale: \"10-15人\"\r\n\t\t\t\t}\r\n\t\t\t]\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgoToShopDetail(id) {\r\n\t\t\tconsole.log('跳转到商家详情页，ID:', id);\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/business/shop-detail?id=${id}`\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tpadding: 40rpx;\r\n}\r\n\r\n.header {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.title {\r\n\tfont-size: 40rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.test-list {\r\n\tbackground-color: #f5f7fa;\r\n\tborder-radius: 16rpx;\r\n\toverflow: hidden;\r\n}\r\n\r\n.test-item {\r\n\tpadding: 30rpx;\r\n\tbackground-color: #fff;\r\n\tmargin-bottom: 20rpx;\r\n\tborder-radius: 12rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.shop-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.shop-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/test/shop-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAgBA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,QACT;AAAA,UACC,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,UAAU;AAAA,UACV,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,UAAU;AAAA,UACV,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,UAAU;AAAA,UACV,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,UAAU;AAAA,UACV,OAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,eAAe,IAAI;AAClBA,oBAAY,MAAA,MAAA,OAAA,kCAAA,gBAAgB,EAAE;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,kCAAkC,EAAE;AAAA,MAC1C,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;ACtDA,GAAG,WAAW,eAAe;"}