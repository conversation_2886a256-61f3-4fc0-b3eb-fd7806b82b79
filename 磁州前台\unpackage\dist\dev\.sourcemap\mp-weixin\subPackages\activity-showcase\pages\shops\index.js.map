{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/shops/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcc2hvcHNcaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"shops-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        <view class=\"navbar-title\">商铺列表</view>\n        <view class=\"navbar-right\">\n          <view class=\"search-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/search/index?type=shops')\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n              <path d=\"M21 21l-4.35-4.35\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 搜索和筛选区域 -->\n    <view class=\"search-filter-bar\">\n      <view class=\"search-box\" @click=\"navigateTo('/subPackages/activity-showcase/pages/search/index?type=shops')\">\n        <svg class=\"search-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n          <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n          <path d=\"M21 21l-4.35-4.35\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n        </svg>\n        <text class=\"search-placeholder\">搜索商铺名称、类别</text>\n      </view>\n      \n      <view class=\"filter-options\">\n        <view \n          class=\"filter-option\"\n          :class=\"{ active: currentFilter === 'distance' }\"\n          @click=\"setFilter('distance')\"\n        >\n          <text>距离</text>\n          <svg class=\"sort-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M7 10l5 5 5-5\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        \n        <view \n          class=\"filter-option\"\n          :class=\"{ active: currentFilter === 'rating' }\"\n          @click=\"setFilter('rating')\"\n        >\n          <text>评分</text>\n          <svg class=\"sort-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M7 10l5 5 5-5\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        \n        <view \n          class=\"filter-option\"\n          :class=\"{ active: currentFilter === 'sales' }\"\n          @click=\"setFilter('sales')\"\n        >\n          <text>销量</text>\n          <svg class=\"sort-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M7 10l5 5 5-5\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        \n        <view \n          class=\"filter-option\"\n          @click=\"showFilter\"\n        >\n          <text>筛选</text>\n          <svg class=\"filter-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n            <path d=\"M22 3H2l8 9.46V19l4 2v-8.54L22 3z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n      </view>\n    </view>\n\n    <!-- 商铺分类标签栏 -->\n    <scroll-view class=\"category-scroll\" scroll-x>\n      <view class=\"category-list\">\n        <view \n          v-for=\"(category, index) in shopCategories\" \n          :key=\"index\"\n          class=\"category-item\"\n          :class=\"{ active: currentCategory === index }\"\n          @click=\"switchCategory(index)\"\n        >\n          <text>{{ category.name }}</text>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 商铺列表区域 -->\n    <scroll-view \n      class=\"shops-scroll\" \n      scroll-y \n      refresher-enabled\n      :refresher-triggered=\"isRefreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @scrolltolower=\"loadMore\"\n    >\n      <view class=\"shops-list\">\n        <view \n          v-for=\"shop in getFilteredShops()\" \n          :key=\"shop.id\"\n          class=\"shop-card\"\n          @click=\"viewShopDetail(shop)\"\n        >\n          <!-- 商铺Logo -->\n          <image :src=\"shop.logo\" class=\"shop-logo\" mode=\"aspectFill\"></image>\n          \n          <!-- 商铺信息 -->\n          <view class=\"shop-info\">\n            <view class=\"shop-header\">\n              <text class=\"shop-name\">{{ shop.name }}</text>\n              <view class=\"shop-rating\">\n                <text class=\"rating-value\">{{ shop.rating }}</text>\n                <svg class=\"star-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                  <polygon points=\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\" fill=\"#FF9500\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></polygon>\n                </svg>\n              </view>\n            </view>\n            \n            <view class=\"shop-meta\">\n              <view class=\"meta-item\">\n                <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                  <path d=\"M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                  <path d=\"M9 22V12h6v10\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                </svg>\n                <text class=\"meta-text\">{{ shop.category }}</text>\n              </view>\n              \n              <view class=\"meta-item\">\n                <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                  <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                  <circle cx=\"12\" cy=\"10\" r=\"3\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                </svg>\n                <text class=\"meta-text\">{{ shop.distance }}</text>\n              </view>\n              \n              <view class=\"meta-item\">\n                <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                  <path d=\"M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                  <circle cx=\"9\" cy=\"7\" r=\"4\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                  <path d=\"M23 21v-2a4 4 0 00-3-3.87m-4-12a4 4 0 010 7.75\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                </svg>\n                <text class=\"meta-text\">月售{{ shop.monthlySales }}+</text>\n              </view>\n            </view>\n            \n            <view class=\"shop-tags\">\n              <view \n                v-for=\"(tag, tagIndex) in shop.tags.slice(0, 3)\" \n                :key=\"tagIndex\"\n                class=\"shop-tag\"\n              >\n                {{ tag }}\n              </view>\n              <view class=\"more-tag\" v-if=\"shop.tags.length > 3\">+{{ shop.tags.length - 3 }}</view>\n            </view>\n            \n            <view class=\"shop-promotion\" v-if=\"shop.promotion\">\n              <view class=\"promotion-tag\">{{ shop.promotion.type }}</view>\n              <text class=\"promotion-text\">{{ shop.promotion.text }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 加载更多提示 -->\n      <view class=\"loading-more\" v-if=\"isLoadingMore\">\n        <view class=\"loading-spinner\"></view>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n\n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"getFilteredShops().length === 0\">\n        <image class=\"empty-image\" src=\"/static/images/empty-shops.png\"></image>\n        <text class=\"empty-text\">暂无相关商铺</text>\n        <view class=\"action-btn\" @click=\"resetFilters\" :style=\"{\n          background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',\n          borderRadius: '35px',\n          boxShadow: '0 5px 15px rgba(255,149,0,0.3)'\n        }\">\n          <text>重置筛选</text>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 筛选弹窗 -->\n    <uni-popup ref=\"filterPopup\" type=\"bottom\">\n      <view class=\"filter-popup\">\n        <view class=\"filter-header\">\n          <text class=\"filter-title\">筛选</text>\n          <view class=\"filter-close\" @click=\"closeFilter\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n              <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"filter-content\">\n          <!-- 商铺分类筛选 -->\n          <view class=\"filter-section\">\n            <text class=\"section-title\">商铺分类</text>\n            <view class=\"filter-options\">\n              <view \n                v-for=\"(category, index) in allCategories\" \n                :key=\"index\"\n                class=\"filter-option\"\n                :class=\"{ active: selectedCategories.includes(index) }\"\n                @click=\"toggleCategory(index)\"\n              >\n                {{ category.name }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 评分筛选 -->\n          <view class=\"filter-section\">\n            <text class=\"section-title\">商铺评分</text>\n            <view class=\"filter-options\">\n              <view \n                v-for=\"(option, index) in ratingOptions\" \n                :key=\"index\"\n                class=\"filter-option\"\n                :class=\"{ active: selectedRating === index }\"\n                @click=\"selectRating(index)\"\n              >\n                {{ option.label }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 特色服务筛选 -->\n          <view class=\"filter-section\">\n            <text class=\"section-title\">特色服务</text>\n            <view class=\"filter-options\">\n              <view \n                v-for=\"(option, index) in serviceOptions\" \n                :key=\"index\"\n                class=\"filter-option\"\n                :class=\"{ active: selectedServices.includes(index) }\"\n                @click=\"toggleService(index)\"\n              >\n                {{ option.label }}\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"filter-footer\">\n          <view class=\"filter-reset\" @click=\"resetFilters\">\n            <text>重置</text>\n          </view>\n          <view class=\"filter-apply\" @click=\"applyFilter\">\n            <text>确定</text>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 页面状态\nconst currentCategory = ref(0);\nconst currentFilter = ref('distance');\nconst isRefreshing = ref(false);\nconst isLoadingMore = ref(false);\nconst shopsList = ref([]);\nconst filterPopup = ref(null);\n\n// 筛选选项\nconst selectedCategories = ref([0]);\nconst selectedRating = ref(0);\nconst selectedServices = ref([]);\n\n// 商铺分类\nconst shopCategories = [\n  { name: '全部', value: 'all' },\n  { name: '餐饮美食', value: 'food' },\n  { name: '休闲娱乐', value: 'entertainment' },\n  { name: '旅游住宿', value: 'travel' },\n  { name: '生活服务', value: 'service' },\n  { name: '文化教育', value: 'education' },\n  { name: '体育健身', value: 'sports' },\n  { name: '亲子活动', value: 'family' }\n];\n\n// 所有分类（包含子分类）\nconst allCategories = [\n  { name: '全部', value: 'all' },\n  { name: '餐饮美食', value: 'food' },\n  { name: '休闲娱乐', value: 'entertainment' },\n  { name: '旅游住宿', value: 'travel' },\n  { name: '生活服务', value: 'service' },\n  { name: '文化教育', value: 'education' },\n  { name: '体育健身', value: 'sports' },\n  { name: '亲子活动', value: 'family' },\n  { name: '中餐', value: 'chinese_food' },\n  { name: '西餐', value: 'western_food' },\n  { name: '快餐', value: 'fast_food' },\n  { name: '咖啡甜品', value: 'cafe' },\n  { name: '酒吧', value: 'bar' }\n];\n\n// 评分选项\nconst ratingOptions = [\n  { label: '全部', value: 0 },\n  { label: '4.5分以上', value: 4.5 },\n  { label: '4.0分以上', value: 4.0 },\n  { label: '3.5分以上', value: 3.5 }\n];\n\n// 特色服务选项\nconst serviceOptions = [\n  { label: '免费WiFi', value: 'wifi' },\n  { label: '停车场', value: 'parking' },\n  { label: '无障碍设施', value: 'accessibility' },\n  { label: '提供发票', value: 'invoice' },\n  { label: '接受预订', value: 'reservation' },\n  { label: '会员特惠', value: 'member' }\n];\n\n// 模拟数据\nconst mockShops = [\n  {\n    id: '1001',\n    name: '磁州文化体验馆',\n    logo: '/static/demo/shop1.jpg',\n    category: '文化教育',\n    rating: 4.8,\n    distance: '1.2km',\n    monthlySales: 256,\n    tags: ['文化体验', '非遗传承', '亲子互动', '团建活动'],\n    promotion: {\n      type: '满减',\n      text: '满200减30'\n    }\n  },\n  {\n    id: '1002',\n    name: '磁州美食城',\n    logo: '/static/demo/shop2.jpg',\n    category: '餐饮美食',\n    rating: 4.5,\n    distance: '0.8km',\n    monthlySales: 512,\n    tags: ['特色美食', '团购优惠', '家庭聚餐'],\n    promotion: {\n      type: '优惠',\n      text: '新用户立减15元'\n    }\n  },\n  {\n    id: '1003',\n    name: '磁州户外拓展中心',\n    logo: '/static/demo/shop3.jpg',\n    category: '体育健身',\n    rating: 4.7,\n    distance: '3.5km',\n    monthlySales: 128,\n    tags: ['户外活动', '团队建设', '亲子互动', '野外生存'],\n    promotion: null\n  },\n  {\n    id: '1004',\n    name: '磁州亲子乐园',\n    logo: '/static/demo/shop4.jpg',\n    category: '亲子活动',\n    rating: 4.6,\n    distance: '2.1km',\n    monthlySales: 320,\n    tags: ['儿童娱乐', '亲子互动', '教育启蒙'],\n    promotion: {\n      type: '折扣',\n      text: '周末8.5折'\n    }\n  },\n  {\n    id: '1005',\n    name: '磁州艺术中心',\n    logo: '/static/demo/shop5.jpg',\n    category: '文化教育',\n    rating: 4.9,\n    distance: '1.5km',\n    monthlySales: 96,\n    tags: ['艺术展览', '文化讲座', '艺术培训'],\n    promotion: {\n      type: '活动',\n      text: '新展开幕，门票半价'\n    }\n  }\n];\n\n// 生命周期\nonMounted(() => {\n  loadShops();\n});\n\n// 方法\nconst loadShops = () => {\n  // 模拟加载数据\n  shopsList.value = mockShops;\n};\n\nconst getFilteredShops = () => {\n  let result = [...shopsList.value];\n  \n  // 应用分类筛选\n  if (currentCategory.value !== 0) {\n    const category = shopCategories[currentCategory.value].name;\n    result = result.filter(shop => shop.category === category);\n  }\n  \n  // 应用排序\n  switch (currentFilter.value) {\n    case 'distance':\n      result.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));\n      break;\n    case 'rating':\n      result.sort((a, b) => b.rating - a.rating);\n      break;\n    case 'sales':\n      result.sort((a, b) => b.monthlySales - a.monthlySales);\n      break;\n  }\n  \n  return result;\n};\n\nconst switchCategory = (index) => {\n  currentCategory.value = index;\n};\n\nconst setFilter = (filter) => {\n  currentFilter.value = filter;\n};\n\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  setTimeout(() => {\n    loadShops();\n    isRefreshing.value = false;\n  }, 1000);\n};\n\nconst loadMore = () => {\n  // 模拟加载更多\n  if (shopsList.value.length >= 10) return;\n  \n  isLoadingMore.value = true;\n  setTimeout(() => {\n    // 模拟添加更多数据\n    const newShops = [\n      {\n        id: '1006',\n        name: '磁州咖啡馆',\n        logo: '/static/demo/shop6.jpg',\n        category: '休闲娱乐',\n        rating: 4.4,\n        distance: '0.5km',\n        monthlySales: 420,\n        tags: ['咖啡', '下午茶', '轻食'],\n        promotion: {\n          type: '优惠',\n          text: '下午茶套餐8折'\n        }\n      },\n      {\n        id: '1007',\n        name: '磁州民宿',\n        logo: '/static/demo/shop7.jpg',\n        category: '旅游住宿',\n        rating: 4.8,\n        distance: '4.2km',\n        monthlySales: 85,\n        tags: ['特色民宿', '乡村体验', '亲近自然'],\n        promotion: {\n          type: '满减',\n          text: '连住3晚减100'\n        }\n      }\n    ];\n    \n    shopsList.value = [...shopsList.value, ...newShops];\n    isLoadingMore.value = false;\n  }, 1500);\n};\n\nconst viewShopDetail = (shop) => {\n  navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.id}`);\n};\n\nconst showFilter = () => {\n  filterPopup.value.open();\n};\n\nconst closeFilter = () => {\n  filterPopup.value.close();\n};\n\nconst toggleCategory = (index) => {\n  const position = selectedCategories.value.indexOf(index);\n  if (index === 0) {\n    // 如果选择\"全部\"，清除其他选项\n    selectedCategories.value = [0];\n  } else {\n    // 如果选择其他选项，移除\"全部\"选项\n    if (selectedCategories.value.includes(0)) {\n      selectedCategories.value = selectedCategories.value.filter(item => item !== 0);\n    }\n    \n    // 切换选中状态\n    if (position !== -1) {\n      selectedCategories.value.splice(position, 1);\n      // 如果没有选项，默认选中\"全部\"\n      if (selectedCategories.value.length === 0) {\n        selectedCategories.value = [0];\n      }\n    } else {\n      selectedCategories.value.push(index);\n    }\n  }\n};\n\nconst selectRating = (index) => {\n  selectedRating.value = index;\n};\n\nconst toggleService = (index) => {\n  const position = selectedServices.value.indexOf(index);\n  if (position !== -1) {\n    selectedServices.value.splice(position, 1);\n  } else {\n    selectedServices.value.push(index);\n  }\n};\n\nconst resetFilters = () => {\n  selectedCategories.value = [0];\n  selectedRating.value = 0;\n  selectedServices.value = [];\n  currentCategory.value = 0;\n  currentFilter.value = 'distance';\n};\n\nconst applyFilter = () => {\n  // 应用筛选\n  console.log('应用筛选', {\n    categories: selectedCategories.value.map(index => allCategories[index].value),\n    rating: ratingOptions[selectedRating.value].value,\n    services: selectedServices.value.map(index => serviceOptions[index].value)\n  });\n  \n  // 模拟筛选结果\n  uni.showToast({\n    title: '筛选已应用',\n    icon: 'success'\n  });\n  \n  closeFilter();\n};\n\nconst goBack = () => {\n  uni.navigateBack();\n};\n\nconst navigateTo = (url) => {\n  uni.navigateTo({ url });\n};\n</script>\n\n<style scoped>\n.shops-container {\n  min-height: 100vh;\n  background-color: #F5F5F5;\n  padding-bottom: 30rpx;\n}\n\n/* 导航栏样式 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 100;\n}\n\n.navbar-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);\n}\n\n.navbar-content {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 90rpx;\n  padding: var(--status-bar-height) 30rpx 0;\n}\n\n.back-btn, .search-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.navbar-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #FFFFFF;\n}\n\n.navbar-right {\n  display: flex;\n  align-items: center;\n}\n\n/* 搜索和筛选栏样式 */\n.search-filter-bar {\n  display: flex;\n  flex-direction: column;\n  background: #FFFFFF;\n  padding: 20rpx;\n  margin-top: calc(var(--status-bar-height) + 90rpx);\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  height: 70rpx;\n  background: #F5F5F5;\n  border-radius: 35rpx;\n  padding: 0 30rpx;\n  margin-bottom: 20rpx;\n}\n\n.search-icon {\n  margin-right: 10rpx;\n}\n\n.search-placeholder {\n  font-size: 28rpx;\n  color: #999999;\n}\n\n.filter-options {\n  display: flex;\n  justify-content: space-around;\n}\n\n.filter-option {\n  display: flex;\n  align-items: center;\n  font-size: 28rpx;\n  color: #666666;\n  padding: 10rpx 0;\n}\n\n.filter-option.active {\n  color: #FF9500;\n  font-weight: 500;\n}\n\n.sort-icon, .filter-icon {\n  margin-left: 6rpx;\n}\n\n/* 分类标签栏样式 */\n.category-scroll {\n  background: #FFFFFF;\n  padding: 20rpx 0;\n  white-space: nowrap;\n  border-bottom: 1rpx solid #EEEEEE;\n}\n\n.category-list {\n  display: flex;\n  padding: 0 20rpx;\n}\n\n.category-item {\n  display: inline-block;\n  padding: 10rpx 30rpx;\n  margin-right: 20rpx;\n  border-radius: 30rpx;\n  font-size: 28rpx;\n  color: #666666;\n  background: #F5F5F5;\n}\n\n.category-item.active {\n  background: rgba(255, 149, 0, 0.1);\n  color: #FF9500;\n  border: 1rpx solid rgba(255, 149, 0, 0.3);\n}\n\n/* 商铺列表样式 */\n.shops-scroll {\n  height: calc(100vh - var(--status-bar-height) - 90rpx - 180rpx - 80rpx);\n}\n\n.shops-list {\n  padding: 20rpx;\n}\n\n.shop-card {\n  display: flex;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.shop-logo {\n  width: 180rpx;\n  height: 180rpx;\n  object-fit: cover;\n}\n\n.shop-info {\n  flex: 1;\n  padding: 20rpx;\n  display: flex;\n  flex-direction: column;\n}\n\n.shop-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.shop-name {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n}\n\n.shop-rating {\n  display: flex;\n  align-items: center;\n}\n\n.rating-value {\n  font-size: 28rpx;\n  color: #FF9500;\n  font-weight: 500;\n  margin-right: 6rpx;\n}\n\n.shop-meta {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 10rpx;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  margin-right: 20rpx;\n  margin-bottom: 10rpx;\n}\n\n.meta-icon {\n  margin-right: 6rpx;\n}\n\n.meta-text {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.shop-tags {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 10rpx;\n}\n\n.shop-tag {\n  font-size: 22rpx;\n  color: #666666;\n  background: #F5F5F5;\n  padding: 4rpx 12rpx;\n  border-radius: 6rpx;\n  margin-right: 10rpx;\n  margin-bottom: 10rpx;\n}\n\n.more-tag {\n  font-size: 22rpx;\n  color: #999999;\n  padding: 4rpx 12rpx;\n}\n\n.shop-promotion {\n  display: flex;\n  align-items: center;\n  margin-top: auto;\n}\n\n.promotion-tag {\n  font-size: 22rpx;\n  color: #FFFFFF;\n  background: #FF9500;\n  padding: 4rpx 10rpx;\n  border-radius: 6rpx;\n  margin-right: 10rpx;\n}\n\n.promotion-text {\n  font-size: 24rpx;\n  color: #FF9500;\n}\n\n/* 加载更多样式 */\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 30rpx 0;\n}\n\n.loading-spinner {\n  width: 40rpx;\n  height: 40rpx;\n  border: 4rpx solid #EEEEEE;\n  border-top-color: #FF9500;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-right: 10rpx;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #999999;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999999;\n  margin-bottom: 30rpx;\n}\n\n.empty-state .action-btn {\n  padding: 15rpx 60rpx;\n  font-size: 28rpx;\n  color: #FFFFFF;\n}\n\n/* 筛选弹窗样式 */\n.filter-popup {\n  background: #FFFFFF;\n  border-top-left-radius: 30rpx;\n  border-top-right-radius: 30rpx;\n  padding: 30rpx;\n  max-height: 70vh;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.filter-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n}\n\n.filter-close {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.filter-content {\n  max-height: calc(70vh - 180rpx);\n  overflow-y: auto;\n}\n\n.filter-section {\n  margin-bottom: 30rpx;\n}\n\n.section-title {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n  margin-bottom: 20rpx;\n}\n\n.filter-options {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.filter-option {\n  padding: 10rpx 30rpx;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  color: #666666;\n  background: #F5F5F5;\n  margin-right: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.filter-option.active {\n  background: rgba(255, 149, 0, 0.1);\n  color: #FF9500;\n  border: 1rpx solid rgba(255, 149, 0, 0.3);\n}\n\n.filter-footer {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 30rpx;\n  padding-top: 20rpx;\n  border-top: 1rpx solid #F0F0F0;\n}\n\n.filter-reset, .filter-apply {\n  flex: 1;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n}\n\n.filter-reset {\n  background: #F5F5F5;\n  color: #666666;\n  margin-right: 20rpx;\n}\n\n.filter-apply {\n  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);\n  color: #FFFFFF;\n  box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.2);\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/shops/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;;;AA8QA,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAC7B,UAAM,gBAAgBA,cAAAA,IAAI,UAAU;AACpC,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AACxB,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAG5B,UAAM,qBAAqBA,cAAG,IAAC,CAAC,CAAC,CAAC;AAClC,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAC5B,UAAM,mBAAmBA,cAAAA,IAAI,CAAA,CAAE;AAG/B,UAAM,iBAAiB;AAAA,MACrB,EAAE,MAAM,MAAM,OAAO,MAAO;AAAA,MAC5B,EAAE,MAAM,QAAQ,OAAO,OAAQ;AAAA,MAC/B,EAAE,MAAM,QAAQ,OAAO,gBAAiB;AAAA,MACxC,EAAE,MAAM,QAAQ,OAAO,SAAU;AAAA,MACjC,EAAE,MAAM,QAAQ,OAAO,UAAW;AAAA,MAClC,EAAE,MAAM,QAAQ,OAAO,YAAa;AAAA,MACpC,EAAE,MAAM,QAAQ,OAAO,SAAU;AAAA,MACjC,EAAE,MAAM,QAAQ,OAAO,SAAU;AAAA,IACnC;AAGA,UAAM,gBAAgB;AAAA,MACpB,EAAE,MAAM,MAAM,OAAO,MAAO;AAAA,MAC5B,EAAE,MAAM,QAAQ,OAAO,OAAQ;AAAA,MAC/B,EAAE,MAAM,QAAQ,OAAO,gBAAiB;AAAA,MACxC,EAAE,MAAM,QAAQ,OAAO,SAAU;AAAA,MACjC,EAAE,MAAM,QAAQ,OAAO,UAAW;AAAA,MAClC,EAAE,MAAM,QAAQ,OAAO,YAAa;AAAA,MACpC,EAAE,MAAM,QAAQ,OAAO,SAAU;AAAA,MACjC,EAAE,MAAM,QAAQ,OAAO,SAAU;AAAA,MACjC,EAAE,MAAM,MAAM,OAAO,eAAgB;AAAA,MACrC,EAAE,MAAM,MAAM,OAAO,eAAgB;AAAA,MACrC,EAAE,MAAM,MAAM,OAAO,YAAa;AAAA,MAClC,EAAE,MAAM,QAAQ,OAAO,OAAQ;AAAA,MAC/B,EAAE,MAAM,MAAM,OAAO,MAAO;AAAA,IAC9B;AAGA,UAAM,gBAAgB;AAAA,MACpB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,UAAU,OAAO,IAAK;AAAA,MAC/B,EAAE,OAAO,UAAU,OAAO,EAAK;AAAA,MAC/B,EAAE,OAAO,UAAU,OAAO,IAAK;AAAA,IACjC;AAGA,UAAM,iBAAiB;AAAA,MACrB,EAAE,OAAO,UAAU,OAAO,OAAQ;AAAA,MAClC,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,MAClC,EAAE,OAAO,SAAS,OAAO,gBAAiB;AAAA,MAC1C,EAAE,OAAO,QAAQ,OAAO,UAAW;AAAA,MACnC,EAAE,OAAO,QAAQ,OAAO,cAAe;AAAA,MACvC,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,IACpC;AAGA,UAAM,YAAY;AAAA,MAChB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,cAAc;AAAA,QACd,MAAM,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACrC,WAAW;AAAA,UACT,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,cAAc;AAAA,QACd,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,QAC7B,WAAW;AAAA,UACT,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,cAAc;AAAA,QACd,MAAM,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACrC,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,cAAc;AAAA,QACd,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,QAC7B,WAAW;AAAA,UACT,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,cAAc;AAAA,QACd,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,QAC7B,WAAW;AAAA,UACT,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAAA,MACF;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,UAAM,YAAY,MAAM;AAEtB,gBAAU,QAAQ;AAAA,IACpB;AAEA,UAAM,mBAAmB,MAAM;AAC7B,UAAI,SAAS,CAAC,GAAG,UAAU,KAAK;AAGhC,UAAI,gBAAgB,UAAU,GAAG;AAC/B,cAAM,WAAW,eAAe,gBAAgB,KAAK,EAAE;AACvD,iBAAS,OAAO,OAAO,UAAQ,KAAK,aAAa,QAAQ;AAAA,MAC1D;AAGD,cAAQ,cAAc,OAAK;AAAA,QACzB,KAAK;AACH,iBAAO,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,QAAQ,IAAI,WAAW,EAAE,QAAQ,CAAC;AACrE;AAAA,QACF,KAAK;AACH,iBAAO,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM;AACzC;AAAA,QACF,KAAK;AACH,iBAAO,KAAK,CAAC,GAAG,MAAM,EAAE,eAAe,EAAE,YAAY;AACrD;AAAA,MACH;AAED,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,CAAC,UAAU;AAChC,sBAAgB,QAAQ;AAAA,IAC1B;AAEA,UAAM,YAAY,CAAC,WAAW;AAC5B,oBAAc,QAAQ;AAAA,IACxB;AAEA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,iBAAW,MAAM;AACf;AACA,qBAAa,QAAQ;AAAA,MACtB,GAAE,GAAI;AAAA,IACT;AAEA,UAAM,WAAW,MAAM;AAErB,UAAI,UAAU,MAAM,UAAU;AAAI;AAElC,oBAAc,QAAQ;AACtB,iBAAW,MAAM;AAEf,cAAM,WAAW;AAAA,UACf;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,cAAc;AAAA,YACd,MAAM,CAAC,MAAM,OAAO,IAAI;AAAA,YACxB,WAAW;AAAA,cACT,MAAM;AAAA,cACN,MAAM;AAAA,YACP;AAAA,UACF;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,cAAc;AAAA,YACd,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,YAC7B,WAAW;AAAA,cACT,MAAM;AAAA,cACN,MAAM;AAAA,YACP;AAAA,UACF;AAAA,QACP;AAEI,kBAAU,QAAQ,CAAC,GAAG,UAAU,OAAO,GAAG,QAAQ;AAClD,sBAAc,QAAQ;AAAA,MACvB,GAAE,IAAI;AAAA,IACT;AAEA,UAAM,iBAAiB,CAAC,SAAS;AAC/B,iBAAW,wDAAwD,KAAK,EAAE,EAAE;AAAA,IAC9E;AAEA,UAAM,aAAa,MAAM;AACvB,kBAAY,MAAM;IACpB;AAEA,UAAM,cAAc,MAAM;AACxB,kBAAY,MAAM;IACpB;AAEA,UAAM,iBAAiB,CAAC,UAAU;AAChC,YAAM,WAAW,mBAAmB,MAAM,QAAQ,KAAK;AACvD,UAAI,UAAU,GAAG;AAEf,2BAAmB,QAAQ,CAAC,CAAC;AAAA,MACjC,OAAS;AAEL,YAAI,mBAAmB,MAAM,SAAS,CAAC,GAAG;AACxC,6BAAmB,QAAQ,mBAAmB,MAAM,OAAO,UAAQ,SAAS,CAAC;AAAA,QAC9E;AAGD,YAAI,aAAa,IAAI;AACnB,6BAAmB,MAAM,OAAO,UAAU,CAAC;AAE3C,cAAI,mBAAmB,MAAM,WAAW,GAAG;AACzC,+BAAmB,QAAQ,CAAC,CAAC;AAAA,UAC9B;AAAA,QACP,OAAW;AACL,6BAAmB,MAAM,KAAK,KAAK;AAAA,QACpC;AAAA,MACF;AAAA,IACH;AAEA,UAAM,eAAe,CAAC,UAAU;AAC9B,qBAAe,QAAQ;AAAA,IACzB;AAEA,UAAM,gBAAgB,CAAC,UAAU;AAC/B,YAAM,WAAW,iBAAiB,MAAM,QAAQ,KAAK;AACrD,UAAI,aAAa,IAAI;AACnB,yBAAiB,MAAM,OAAO,UAAU,CAAC;AAAA,MAC7C,OAAS;AACL,yBAAiB,MAAM,KAAK,KAAK;AAAA,MAClC;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AACzB,yBAAmB,QAAQ,CAAC,CAAC;AAC7B,qBAAe,QAAQ;AACvB,uBAAiB,QAAQ;AACzB,sBAAgB,QAAQ;AACxB,oBAAc,QAAQ;AAAA,IACxB;AAEA,UAAM,cAAc,MAAM;AAExBC,oBAAAA,MAAY,MAAA,OAAA,8DAAA,QAAQ;AAAA,QAClB,YAAY,mBAAmB,MAAM,IAAI,WAAS,cAAc,KAAK,EAAE,KAAK;AAAA,QAC5E,QAAQ,cAAc,eAAe,KAAK,EAAE;AAAA,QAC5C,UAAU,iBAAiB,MAAM,IAAI,WAAS,eAAe,KAAK,EAAE,KAAK;AAAA,MAC7E,CAAG;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAED;IACF;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9jBA,GAAG,WAAW,eAAe;"}