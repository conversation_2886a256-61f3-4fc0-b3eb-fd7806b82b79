/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* iOS风格的个人主页样式 */
.profile-container {
  width: 100%;
  height: 100vh;
  background-color: #f2f2f7;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

/* 导航栏样式 */
.ios-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #007AFF;
  /* 修改为蓝色，与系统标题栏一致 */
  color: #FFFFFF;
  z-index: 100;
  padding: 0 16px;
  border-bottom: none;
}
.navbar-left, .navbar-right {
  flex: 0 0 auto;
  width: 44px;
  /* Reduced from 64px for better positioning */
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 12px;
  /* Reduced padding for better positioning */
  z-index: 100;
  position: relative;
}
.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #FFFFFF;
  /* 修改为白色，与蓝色背景搭配 */
  line-height: 44px;
}
.back-button, .action-button {
  height: 32px;
  /* Reduced from 36px */
  width: 32px;
  /* Reduced from 36px */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  /* Adjusted to match the new size */
  background-color: transparent;
  /* Removed background color */
  cursor: pointer;
}
.back-button:active {
  background-color: rgba(255, 255, 255, 0.15);
  /* Lighter background when pressed */
  transform: scale(0.92);
  /* Slightly more pronounced scale effect */
}
.back-icon, .action-icon {
  width: 20px;
  /* Reduced from 22px */
  height: 20px;
  /* Reduced from 22px */
  /* 添加滤镜使图标变为白色 */
  filter: brightness(0) invert(1);
}

/* 内容区域 */
.profile-content {
  height: 100vh;
  width: 100%;
}

/* 个人信息卡片 */
.profile-header {
  padding: 16px;
  margin-bottom: 8px;
}
.profile-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
}
.profile-avatar-container {
  margin-right: 16px;
}
.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  border: 0.5px solid rgba(60, 60, 67, 0.1);
  background-color: #ffffff;
}
.profile-info {
  flex: 1;
}
.profile-name-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 6px;
}
.profile-name {
  font-size: 22px;
  font-weight: 700;
  color: #000000;
  margin-right: 8px;
}
.vip-badge {
  background-color: #007AFF;
  border-radius: 4px;
  padding: 2px 6px;
}
.vip-text {
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 600;
}
.profile-id-row {
  margin-bottom: 6px;
}
.profile-id {
  font-size: 14px;
  color: #8E8E93;
}
.profile-motto {
  margin-bottom: 6px;
}
.motto-text {
  font-size: 16px;
  color: #3A3A3C;
  line-height: 1.3;
}

/* 操作按钮 */
.profile-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}
.action-btn {
  width: 100px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  flex: none;
}
.action-btn::after {
  display: none;
}
.action-btn:active {
  transform: translateY(1px) scale(0.97);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.edit-btn {
  background: linear-gradient(135deg, #F9F9F9 0%, #E8E8E8 100%);
  color: #333333;
  border: 0.5px solid rgba(0, 0, 0, 0.05);
}
.stats-btn {
  background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%);
  color: #FFFFFF;
  border: 0.5px solid rgba(0, 122, 255, 0.3);
}

/* 数据统计卡片 */
.stats-card {
  margin: 12px 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-around;
  padding: 16px 0;
}
.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
}
.stats-value {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 4px;
}
.stats-label {
  font-size: 12px;
  color: #8E8E93;
}

/* 内容标签页 */
.content-tabs {
  display: flex;
  background-color: #FFFFFF;
  height: 44px;
  border-bottom: 0.5px solid rgba(60, 60, 67, 0.1);
  position: relative;
  margin-bottom: 1px;
}
.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  position: relative;
}
.tab-text {
  font-size: 15px;
  font-weight: 600;
  color: #8E8E93;
  transition: color 0.3s;
}
.tab-item.active .tab-text {
  color: #007AFF;
}
.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 2px;
  background-color: #007AFF;
  transition: left 0.3s ease;
}

/* 店铺卡片 */
.shop-card {
  margin: 12px 16px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}
.shop-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
}
.shop-icon {
  width: 54px;
  height: 54px;
  border-radius: 10px;
  margin-right: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  transition: opacity 0.2s, transform 0.2s;
}
.shop-icon:active {
  opacity: 0.8;
  transform: scale(0.98);
}
.shop-info {
  flex: 1;
}
.shop-name {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 6px;
  display: block;
  transition: color 0.2s;
}
.shop-name:active {
  color: #007AFF;
}
.shop-description {
  font-size: 14px;
  color: #666666;
  line-height: 1.4;
  display: block;
}
.shop-contact {
  margin: 12px 0 16px;
  background-color: #F8F8FA;
  border-radius: 10px;
  padding: 10px 12px;
}
.contact-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
}
.contact-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.contact-text {
  font-size: 15px;
  color: #3A3A3C;
  flex: 1;
}
.address-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.navigation-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
  margin-left: 5px;
}
.shop-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 8px;
}
.shop-btn {
  padding: 6rpx 20rpx;
  font-size: 24rpx;
  border-radius: 24rpx;
  margin-left: 10rpx;
}
.primary-btn {
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
.secondary-btn {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  border: 1px solid rgba(0, 122, 255, 0.2);
}

/* 活动卡片 */
.activities-card {
  margin: 16px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: visible;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
}
.add-btn {
  font-size: 13px;
  font-weight: 600;
  background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 100%);
  color: #007AFF;
  padding: 5px 10px;
  border-radius: 14px;
  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.15);
  position: relative;
  overflow: hidden;
  border: 0.5px solid rgba(0, 122, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
}
.add-btn::before {
  content: "+";
  margin-right: 3px;
  font-size: 16px;
  font-weight: 700;
  line-height: 0.8;
  background: linear-gradient(135deg, #0095FF 0%, #006EE6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.add-btn::after {
  display: none;
}
.add-btn:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 1px 5px rgba(0, 122, 255, 0.15);
  background: linear-gradient(135deg, #E6F3FF 0%, #D9ECFF 100%);
}
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.activity-item {
  padding: 14px;
  background-color: #F7F7F9;
  border-radius: 12px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 14px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}
.activity-item:last-child {
  margin-bottom: 0;
  /* 最后一项不需要底部外边距 */
}
.activity-image {
  width: 100%;
  height: 140px;
  border-radius: 10px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  object-fit: cover;
}
.activity-content {
  margin-bottom: 12px;
}
.activity-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  flex-wrap: wrap;
}
.activity-title {
  font-size: 16px;
  font-weight: 700;
  color: #222222;
  max-width: 70%;
}
.activity-time {
  font-size: 13px;
  color: #8E8E93;
  flex-shrink: 0;
}
.activity-desc {
  font-size: 14px;
  color: #555555;
  line-height: 1.4;
}
.activity-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 8px;
}
.activity-btn {
  height: 28px;
  padding: 0 12px;
  border-radius: 14px;
  font-size: 12px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);
  border: 0.5px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ECF5FF 0%, #E2F0FF 100%);
  color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
}
.activity-btn::before {
  display: none;
}
.activity-btn::after {
  display: none;
}
.activity-btn:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #E2F0FF 0%, #D8EAFF 100%);
}
.activity-btn.primary {
  background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%);
  color: #FFFFFF;
  border: 0.5px solid rgba(0, 122, 255, 0.7);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}
.activity-btn.primary:active {
  background: linear-gradient(135deg, #0075E6 0%, #005CB8 100%);
}

/* 添加不同按钮类型的样式 */
.activity-btn[data-type=edit] {
  background: linear-gradient(135deg, #EEF6FF 0%, #E2F0FF 100%);
  color: #0070E0;
}
.activity-btn[data-type=promote] {
  background: linear-gradient(135deg, #FFF2E6 0%, #FFE8D1 100%);
  color: #FF7D00;
}
.activity-btn[data-type=share] {
  background: linear-gradient(135deg, #E9F9F0 0%, #DCF2E5 100%);
  color: #27AE60;
}

/* 空状态 */
.empty-card {
  margin: 12px 16px;
  padding: 32px 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}
.empty-icon.large {
  width: 100px;
  height: 100px;
}
.empty-text {
  font-size: 16px;
  color: #8E8E93;
  margin-bottom: 16px;
}
.create-btn {
  padding: 10px 28px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #007AFF 0%, #0066CC 100%);
  color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 122, 255, 0.25);
  transition: all 0.3s ease;
}
.create-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(0, 122, 255, 0.2);
}

/* 适配iPhone状态栏 */
@supports (padding-top: constant(safe-area-inset-top)) {
.ios-navbar {
    padding-top: constant(safe-area-inset-top);
}
}
@supports (padding-top: env(safe-area-inset-top)) {
.ios-navbar {
    padding-top: env(safe-area-inset-top);
}
}
.tab-scroll {
  height: 100%;
  box-sizing: border-box;
  padding-bottom: 20px;
  /* 添加底部内边距，确保内容完全显示 */
}

/* 添加设置图标样式 */
.settings-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  opacity: 1;
}
.action-button {
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
  background-color: rgba(255, 255, 255, 0.2);
}
.action-button:active {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 添加设置按钮样式 */
.settings-btn {
  font-size: 13px;
  color: #007AFF;
  padding: 4px 8px;
  margin-left: auto;
  font-weight: 500;
  border-radius: 4px;
  background-color: rgba(0, 122, 255, 0.08);
}
.settings-btn:active {
  background-color: rgba(0, 122, 255, 0.15);
}

/* 发布内容样式 */
.published-list {
  padding: 15px;
}
.published-item {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.03);
}
.publish-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.publish-type {
  font-size: 14px;
  color: #007AFF;
  font-weight: 600;
  padding: 2px 8px;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 4px;
}
.publish-date {
  font-size: 13px;
  color: #888888;
}
.publish-content {
  margin-bottom: 15px;
}
.publish-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
  display: block;
}
.publish-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 12px;
  display: block;
}
.publish-images {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4px;
}
.publish-image {
  width: calc(33.33% - 8px);
  height: 80px;
  margin: 4px;
  border-radius: 6px;
}
.publish-actions {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
  gap: 10px;
}
.add-more {
  padding: 16px 0;
  display: flex;
  justify-content: center;
}
.add-btn {
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
.add-btn:active {
  background-color: #eaeaea;
}
.add-icon {
  font-size: 18px;
  margin-right: 6px;
  font-weight: 600;
}

/* 自定义弹窗样式 */
.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.modal-content {
  background-color: #ffffff;
  border-radius: 22px;
  padding: 24px 20px;
  width: 75%;
  max-width: 300px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}
.modal-title {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  text-align: center;
}
.modal-body {
  margin-bottom: 20px;
}
.modal-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
  text-align: center;
}
.price-text {
  font-size: 15px;
  color: #333;
  margin: 14px 0;
  text-align: center;
}
.price-amount {
  font-weight: 600;
  color: #FF3B30;
  font-size: 18px;
}
.rank-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 14px 0;
  background-color: #F8F8F8;
  padding: 10px;
  border-radius: 14px;
}
.rank-icon {
  width: 18px;
  height: 18px;
  margin-right: 6px;
}
.special-hint {
  font-size: 14px;
  color: #FF3B30;
  font-weight: 600;
  font-style: italic;
  margin-top: 14px;
  text-align: center;
}
.modal-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 12px;
}
.modal-btn {
  flex: 1;
  padding: 10px 0;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 600;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}
.modal-btn::after {
  border: none;
}
.modal-btn:active {
  transform: scale(0.96);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
.cancel-btn {
  background-color: #F5F5F5;
  color: #333;
}
.confirm-btn {
  background-color: #007AFF;
  color: #fff;
}

/* 优化我的发布页面操作按钮样式 */
.publish-actions button,
.published-item .activity-btn {
  min-width: 54px;
  height: 28px;
  padding: 0 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 600;
  background: linear-gradient(135deg, #ECF5FF 0%, #E2F0FF 100%);
  color: #007AFF;
  border: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.publish-actions button:active,
.published-item .activity-btn:active {
  background: linear-gradient(135deg, #E2F0FF 0%, #D8EAFF 100%);
  transform: scale(0.97);
}
.publish-actions button .icon,
.published-item .activity-btn .icon {
  display: none !important;
}