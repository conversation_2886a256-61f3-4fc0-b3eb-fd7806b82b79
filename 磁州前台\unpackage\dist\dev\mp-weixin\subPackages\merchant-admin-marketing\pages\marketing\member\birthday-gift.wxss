/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-01e8e90e, html.data-v-01e8e90e, #app.data-v-01e8e90e, .index-container.data-v-01e8e90e {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.birthday-gift-container.data-v-01e8e90e {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.page-header.data-v-01e8e90e {
  margin-bottom: 20rpx;
}
.title-section .page-title.data-v-01e8e90e {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.title-section .page-subtitle.data-v-01e8e90e {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.section-card.data-v-01e8e90e {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-card .section-title.data-v-01e8e90e {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.section-card .section-title.data-v-01e8e90e::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #4A00E0;
  border-radius: 4rpx;
}
.switch-item.data-v-01e8e90e {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.switch-item .switch-content .switch-title.data-v-01e8e90e {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.switch-item .switch-content .switch-desc.data-v-01e8e90e {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.form-item.data-v-01e8e90e {
  margin-bottom: 24rpx;
}
.form-item.data-v-01e8e90e:last-child {
  margin-bottom: 0;
}
.form-item .form-label.data-v-01e8e90e {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}
.form-item .form-input.data-v-01e8e90e {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.form-item .form-input-group.data-v-01e8e90e {
  display: flex;
  align-items: center;
}
.form-item .form-input-group .form-input.data-v-01e8e90e {
  width: 200rpx;
}
.form-item .form-input-group .input-suffix.data-v-01e8e90e {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #333;
}
.form-item .form-textarea.data-v-01e8e90e {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.form-item .radio-group.data-v-01e8e90e {
  display: flex;
}
.form-item .radio-group .radio-item.data-v-01e8e90e {
  padding: 12rpx 30rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.form-item .radio-group .radio-item .radio-text.data-v-01e8e90e {
  font-size: 28rpx;
  color: #333;
}
.form-item .radio-group .radio-item.active.data-v-01e8e90e {
  background-color: #4A00E0;
  border-color: #4A00E0;
}
.form-item .radio-group .radio-item.active .radio-text.data-v-01e8e90e {
  color: #fff;
}
.form-item.switch-item.data-v-01e8e90e {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-item.switch-item .form-label.data-v-01e8e90e {
  margin-bottom: 0;
}
.privilege-list.data-v-01e8e90e {
  margin-bottom: 24rpx;
}
.privilege-list .privilege-item.data-v-01e8e90e {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.privilege-list .privilege-item.data-v-01e8e90e:last-child {
  border-bottom: none;
}
.privilege-list .privilege-item .privilege-checkbox.data-v-01e8e90e {
  width: 40rpx;
  height: 40rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.privilege-list .privilege-item .privilege-checkbox.checked.data-v-01e8e90e {
  background-color: #4A00E0;
  border-color: #4A00E0;
}
.privilege-list .privilege-item .privilege-checkbox .checkbox-inner.data-v-01e8e90e {
  width: 20rpx;
  height: 20rpx;
  background-color: #fff;
}
.privilege-list .privilege-item .privilege-content.data-v-01e8e90e {
  flex: 1;
}
.privilege-list .privilege-item .privilege-content .privilege-name.data-v-01e8e90e {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.privilege-list .privilege-item .privilege-content .privilege-desc.data-v-01e8e90e {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.privilege-list .privilege-item .privilege-config .config-text.data-v-01e8e90e {
  font-size: 28rpx;
  color: #4A00E0;
}
.add-btn.data-v-01e8e90e {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}
.level-list .level-item.data-v-01e8e90e {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.level-list .level-item.data-v-01e8e90e:last-child {
  border-bottom: none;
}
.level-list .level-item .level-checkbox.data-v-01e8e90e {
  width: 40rpx;
  height: 40rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.level-list .level-item .level-checkbox.checked.data-v-01e8e90e {
  background-color: #4A00E0;
  border-color: #4A00E0;
}
.level-list .level-item .level-checkbox .checkbox-inner.data-v-01e8e90e {
  width: 20rpx;
  height: 20rpx;
  background-color: #fff;
}
.level-list .level-item .level-content.data-v-01e8e90e {
  flex: 1;
}
.level-list .level-item .level-content .level-name.data-v-01e8e90e {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.level-list .level-item .level-content .level-desc.data-v-01e8e90e {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.greeting-preview .preview-title.data-v-01e8e90e {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}
.greeting-preview .preview-card.data-v-01e8e90e {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  overflow: hidden;
}
.greeting-preview .preview-card .preview-header.data-v-01e8e90e {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}
.greeting-preview .preview-card .preview-header .preview-logo.data-v-01e8e90e {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.greeting-preview .preview-card .preview-header .preview-shop-name.data-v-01e8e90e {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.greeting-preview .preview-card .preview-content.data-v-01e8e90e {
  padding: 30rpx 20rpx;
}
.greeting-preview .preview-card .preview-content .preview-greeting.data-v-01e8e90e {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}
.greeting-preview .preview-card .preview-footer.data-v-01e8e90e {
  padding: 20rpx;
  display: flex;
  justify-content: center;
  border-top: 1rpx solid #f0f0f0;
}
.greeting-preview .preview-card .preview-footer .preview-btn.data-v-01e8e90e {
  padding: 12rpx 30rpx;
  background-color: #4A00E0;
  color: #fff;
  font-size: 28rpx;
  border-radius: 30rpx;
}
.bottom-bar.data-v-01e8e90e {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.bottom-bar .save-btn.data-v-01e8e90e {
  width: 100%;
  height: 90rpx;
  background-color: #4A00E0;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}