/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-27964770, html.data-v-27964770, #app.data-v-27964770, .index-container.data-v-27964770 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.promotion-report-container.data-v-27964770 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content.data-v-27964770 {
  margin-top: 100rpx;
  padding: 20rpx;
}
.stats-cards.data-v-27964770 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.stats-card.data-v-27964770 {
  width: 48%;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.stats-title.data-v-27964770 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.stats-value.data-v-27964770 {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}
.report-tabs.data-v-27964770 {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.tab-header.data-v-27964770 {
  display: flex;
  border-bottom: 1rpx solid #eee;
}
.tab.data-v-27964770 {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.tab.active.data-v-27964770 {
  color: #8A2BE2;
  font-weight: bold;
}
.tab.active.data-v-27964770::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #8A2BE2;
}
.tab-content.data-v-27964770 {
  min-height: 500rpx;
  padding: 20rpx;
}
.empty-tip.data-v-27964770 {
  text-align: center;
  padding: 60rpx 0;
}
.empty-image.data-v-27964770 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text.data-v-27964770 {
  color: #999;
  font-size: 28rpx;
}
.order-item.data-v-27964770 {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.order-header.data-v-27964770 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.order-no.data-v-27964770 {
  font-size: 26rpx;
  color: #666;
}
.order-status.data-v-27964770 {
  font-size: 26rpx;
  color: #8A2BE2;
}
.goods-info.data-v-27964770 {
  display: flex;
  margin-bottom: 20rpx;
}
.goods-image.data-v-27964770 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.goods-detail.data-v-27964770 {
  flex: 1;
}
.goods-name.data-v-27964770 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.goods-price.data-v-27964770 {
  font-size: 26rpx;
  color: #ff6b6b;
}
.commission-info.data-v-27964770 {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}
.commission-label.data-v-27964770 {
  font-size: 26rpx;
  color: #666;
}
.commission-value.data-v-27964770 {
  font-size: 26rpx;
  color: #ff6b6b;
}
.order-footer.data-v-27964770 {
  text-align: right;
}
.order-time.data-v-27964770 {
  font-size: 24rpx;
  color: #999;
}
.commission-item.data-v-27964770 {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.commission-header.data-v-27964770 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.commission-title.data-v-27964770 {
  font-size: 28rpx;
  color: #333;
}
.commission-amount.data-v-27964770 {
  font-size: 28rpx;
  color: #ff6b6b;
}
.commission-amount.income.data-v-27964770 {
  color: #27ae60;
}
.commission-time.data-v-27964770 {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}