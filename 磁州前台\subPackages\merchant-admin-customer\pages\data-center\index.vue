<template>
  <view class="data-center-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">客户数据中心</text>
      <view class="navbar-right">
        <view class="export-icon" @click="exportData">📊</view>
      </view>
    </view>
    
    <!-- 数据概览卡片 -->
    <view class="overview-card">
      <view class="card-header">
        <text class="card-title">客户数据概览</text>
        <view class="date-selector">
          <text class="date-label">{{currentDateRange}}</text>
          <view class="date-icon" @click="showDatePicker">📅</view>
        </view>
      </view>
      <view class="overview-data">
        <view class="data-item">
          <text class="data-value">{{customerData.totalCustomers}}</text>
          <text class="data-label">总客户数</text>
        </view>
        <view class="data-item">
          <text class="data-value">{{customerData.activeRate}}%</text>
          <text class="data-label">活跃率</text>
        </view>
        <view class="data-item">
          <text class="data-value">¥{{customerData.avgValue}}</text>
          <text class="data-label">客均价值</text>
        </view>
        <view class="data-item">
          <text class="data-value">{{customerData.retentionRate}}%</text>
          <text class="data-label">留存率</text>
        </view>
      </view>
      <view class="chart-container">
        <!-- 这里应该是图表组件，暂时用占位符 -->
        <view class="chart-placeholder">
          <text class="chart-text">客户增长趋势图表</text>
        </view>
      </view>
    </view>
    
    <!-- 分析模块 -->
    <view class="analysis-section">
      <view class="section-header">
        <text class="section-title">客户分析</text>
      </view>
      
      <view class="analysis-grid">
        <!-- 客户标签管理 -->
        <view class="analysis-card" @click="navigateTo('./tags')">
          <view class="card-icon tag-icon">🏷️</view>
          <text class="card-name">客户标签管理</text>
          <text class="card-desc">自动标签、手动标签</text>
        </view>
        
        <!-- 客户分群 -->
        <view class="analysis-card" @click="navigateTo('./segments')">
          <view class="card-icon segment-icon">👥</view>
          <text class="card-name">客户分群</text>
          <text class="card-desc">消费偏好、购买习惯</text>
        </view>
        
        <!-- 活跃度分析 -->
        <view class="analysis-card" @click="navigateTo('./activity')">
          <view class="card-icon activity-icon">📈</view>
          <text class="card-name">活跃度分析</text>
          <text class="card-desc">访问频率、互动深度</text>
        </view>
        
        <!-- 生命周期 -->
        <view class="analysis-card" @click="navigateTo('./lifecycle')">
          <view class="card-icon lifecycle-icon">🔄</view>
          <text class="card-name">生命周期跟踪</text>
          <text class="card-desc">生命周期阶段分析</text>
        </view>
      </view>
    </view>
    
    <!-- 客户画像 -->
    <view class="portrait-section">
      <view class="section-header">
        <text class="section-title">客户画像</text>
        <text class="section-more" @click="navigateTo('./portrait')">查看详情</text>
      </view>
      
      <view class="portrait-card">
        <view class="portrait-item">
          <text class="portrait-label">性别比例</text>
          <view class="portrait-chart">
            <view class="pie-chart">
              <view class="pie-segment male" style="transform: rotate(0deg); clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 50%, 100% 100%, 50% 100%);">
              </view>
              <view class="pie-segment female" style="transform: rotate(180deg); clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 50%, 50% 50%);">
              </view>
              <view class="pie-center">
                <text class="pie-text">性别</text>
              </view>
            </view>
            <view class="pie-legend">
              <view class="legend-item">
                <view class="legend-color male"></view>
                <text class="legend-text">男性 45%</text>
              </view>
              <view class="legend-item">
                <view class="legend-color female"></view>
                <text class="legend-text">女性 55%</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="portrait-item">
          <text class="portrait-label">年龄分布</text>
          <view class="bar-chart">
            <view class="bar-item">
              <text class="bar-label">18-24岁</text>
              <view class="bar-wrapper">
                <view class="bar-fill" style="width: 15%;"></view>
                <text class="bar-value">15%</text>
              </view>
            </view>
            <view class="bar-item">
              <text class="bar-label">25-34岁</text>
              <view class="bar-wrapper">
                <view class="bar-fill" style="width: 35%;"></view>
                <text class="bar-value">35%</text>
              </view>
            </view>
            <view class="bar-item">
              <text class="bar-label">35-44岁</text>
              <view class="bar-wrapper">
                <view class="bar-fill" style="width: 28%;"></view>
                <text class="bar-value">28%</text>
              </view>
            </view>
            <view class="bar-item">
              <text class="bar-label">45岁以上</text>
              <view class="bar-wrapper">
                <view class="bar-fill" style="width: 22%;"></view>
                <text class="bar-value">22%</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 消费习惯 -->
    <view class="habit-section">
      <view class="section-header">
        <text class="section-title">消费习惯分析</text>
        <text class="section-more" @click="navigateTo('./habits')">查看详情</text>
      </view>
      
      <view class="habit-card">
        <view class="habit-item">
          <view class="habit-icon">🕒</view>
          <view class="habit-content">
            <text class="habit-title">消费高峰期</text>
            <text class="habit-value">18:00 - 20:00</text>
          </view>
        </view>
        <view class="habit-item">
          <view class="habit-icon">📆</view>
          <view class="habit-content">
            <text class="habit-title">消费频次</text>
            <text class="habit-value">平均 12天/次</text>
          </view>
        </view>
        <view class="habit-item">
          <view class="habit-icon">💰</view>
          <view class="habit-content">
            <text class="habit-title">客单价</text>
            <text class="habit-value">¥76.50</text>
          </view>
        </view>
        <view class="habit-item">
          <view class="habit-icon">🛒</view>
          <view class="habit-content">
            <text class="habit-title">热门品类</text>
            <text class="habit-value">生鲜果蔬</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDateRange: '2023-05-01 至 2023-05-31',
      customerData: {
        totalCustomers: '1,256',
        activeRate: '29.3',
        avgValue: '368.50',
        retentionRate: '68.5'
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    exportData() {
      uni.showActionSheet({
        itemList: ['导出Excel', '导出PDF', '生成分析报告'],
        success: (res) => {
          uni.showToast({
            title: '导出功能开发中',
            icon: 'none'
          });
        }
      });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.data-center-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.export-icon {
  font-size: 20px;
  color: #fff;
}

.overview-card {
  margin: 16px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-selector {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.date-icon {
  margin-left: 8px;
  font-size: 16px;
}

.overview-data {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.data-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.data-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.data-label {
  font-size: 12px;
  color: #999;
}

.chart-container {
  height: 200px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.chart-placeholder {
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 14px;
  color: #999;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #1677FF;
}

.analysis-grid {
  padding: 0 16px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.analysis-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 12px;
}

.tag-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.segment-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.activity-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.lifecycle-icon {
  background-color: #fff1f0;
  color: #f5222d;
}

.card-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.portrait-card, .habit-card {
  margin: 0 16px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

.portrait-item {
  margin-bottom: 16px;
}

.portrait-item:last-child {
  margin-bottom: 0;
}

.portrait-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.portrait-chart {
  display: flex;
  align-items: center;
}

.pie-chart {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  position: relative;
  margin-right: 16px;
}

.pie-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  top: 0;
  left: 0;
}

.male {
  background-color: #1677FF;
}

.female {
  background-color: #FF69B4;
}

.pie-center {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #fff;
  top: 25px;
  left: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-text {
  font-size: 12px;
  color: #666;
}

.pie-legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

.bar-chart {
  padding: 0 8px;
}

.bar-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.bar-item:last-child {
  margin-bottom: 0;
}

.bar-label {
  width: 60px;
  font-size: 12px;
  color: #666;
}

.bar-wrapper {
  flex: 1;
  height: 16px;
  background-color: #f0f0f0;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background-color: #1677FF;
  border-radius: 8px;
}

.bar-value {
  position: absolute;
  right: 8px;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  font-size: 10px;
  color: #fff;
}

.habit-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.habit-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.habit-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 12px;
}

.habit-content {
  flex: 1;
}

.habit-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
  display: block;
}

.habit-value {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
</style> 