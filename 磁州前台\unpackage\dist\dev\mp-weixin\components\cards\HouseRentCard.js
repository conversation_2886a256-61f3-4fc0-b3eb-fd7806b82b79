"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_polyline + _component_svg + _component_rect + _component_line + _component_circle)();
}
if (!Math) {
  BaseInfoCard();
}
const BaseInfoCard = () => "./BaseInfoCard.js";
const _sfc_main = {
  __name: "HouseRentCard",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  setup(__props) {
    function getFacilityIconClass(facility) {
      const facilityMap = {
        "空调": "ac-icon",
        "热水器": "water-icon",
        "洗衣机": "washer-icon",
        "冰箱": "fridge-icon",
        "电视": "tv-icon",
        "宽带": "wifi-icon",
        "衣柜": "wardrobe-icon",
        "床": "bed-icon",
        "沙发": "sofa-icon",
        "天然气": "gas-icon"
      };
      return facilityMap[facility] || "default-icon";
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.item.houseType
      }, __props.item.houseType ? {
        b: common_vendor.p({
          d: "M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
        }),
        c: common_vendor.p({
          points: "9 22 9 12 15 12 15 22"
        }),
        d: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.t(__props.item.houseType)
      } : {}, {
        f: __props.item.size
      }, __props.item.size ? {
        g: common_vendor.p({
          x: "3",
          y: "3",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2"
        }),
        h: common_vendor.p({
          x1: "3",
          y1: "9",
          x2: "21",
          y2: "9"
        }),
        i: common_vendor.p({
          x1: "9",
          y1: "21",
          x2: "9",
          y2: "9"
        }),
        j: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        k: common_vendor.t(__props.item.size)
      } : {}, {
        l: __props.item.floor
      }, __props.item.floor ? {
        m: common_vendor.p({
          x: "2",
          y: "3",
          width: "20",
          height: "14",
          rx: "2",
          ry: "2"
        }),
        n: common_vendor.p({
          x1: "8",
          y1: "21",
          x2: "16",
          y2: "21"
        }),
        o: common_vendor.p({
          x1: "12",
          y1: "17",
          x2: "12",
          y2: "21"
        }),
        p: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        q: common_vendor.t(__props.item.floor)
      } : {}, {
        r: __props.item.direction
      }, __props.item.direction ? {
        s: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10"
        }),
        t: common_vendor.p({
          points: "16.24 7.76 14.12 14.12 7.76 16.24"
        }),
        v: common_vendor.p({
          x1: "12",
          y1: "12",
          x2: "12.01",
          y2: "12"
        }),
        w: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.t(__props.item.direction)
      } : {}, {
        y: __props.item.facilities && __props.item.facilities.length
      }, __props.item.facilities && __props.item.facilities.length ? {
        z: common_vendor.f(__props.item.facilities, (facility, index, i0) => {
          return {
            a: "cc27e780-17-" + i0 + "," + ("cc27e780-16-" + i0),
            b: "cc27e780-16-" + i0 + ",cc27e780-0",
            c: common_vendor.n(getFacilityIconClass(facility)),
            d: common_vendor.t(facility),
            e: index
          };
        }),
        A: common_vendor.p({
          points: "20 6 9 17 4 12"
        }),
        B: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        })
      } : {}, {
        C: __props.item.features && __props.item.features.length
      }, __props.item.features && __props.item.features.length ? {
        D: common_vendor.f(__props.item.features, (feature, index, i0) => {
          return {
            a: common_vendor.t(feature),
            b: index
          };
        })
      } : {}, {
        E: __props.item.location
      }, __props.item.location ? {
        F: common_vendor.p({
          d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
        }),
        G: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3"
        }),
        H: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        I: common_vendor.t(__props.item.location)
      } : {}, {
        J: common_vendor.p({
          item: __props.item
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cc27e780"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/cards/HouseRentCard.js.map
