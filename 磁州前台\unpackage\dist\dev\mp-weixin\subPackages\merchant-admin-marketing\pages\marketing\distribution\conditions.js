"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  __name: "conditions",
  setup(__props) {
    const selectedCondition = common_vendor.ref("purchase");
    const purchaseAmount = common_vendor.ref("100");
    const specificProducts = common_vendor.ref(true);
    const requireApproval = common_vendor.ref(true);
    const autoApprove = common_vendor.ref(false);
    const selectedProducts = common_vendor.ref([
      {
        id: "1",
        name: "分销员入门套餐",
        price: "99.00",
        image: "/static/images/products/product-1.jpg"
      },
      {
        id: "2",
        name: "分销员高级套餐",
        price: "199.00",
        image: "/static/images/products/product-2.jpg"
      }
    ]);
    common_vendor.onMounted(() => {
      getConditionSettings();
    });
    const getConditionSettings = () => {
    };
    const selectCondition = (condition) => {
      selectedCondition.value = condition;
    };
    const toggleSpecificProducts = (e) => {
      specificProducts.value = e.detail.value;
    };
    const toggleRequireApproval = (e) => {
      requireApproval.value = e.detail.value;
      if (!requireApproval.value) {
        autoApprove.value = false;
      }
    };
    const toggleAutoApprove = (e) => {
      autoApprove.value = e.detail.value;
    };
    const addProduct = () => {
      common_vendor.index.showToast({
        title: "添加商品功能开发中",
        icon: "none"
      });
    };
    const removeProduct = (index) => {
      selectedProducts.value.splice(index, 1);
    };
    const saveSettings = () => {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "分销员条件帮助",
        content: "您可以设置用户成为分销员的条件，包括无条件、申请、购买商品和邀请等方式。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: selectedCondition.value === "none"
      }, selectedCondition.value === "none" ? {} : {}, {
        d: selectedCondition.value === "none" ? 1 : "",
        e: common_vendor.o(($event) => selectCondition("none")),
        f: selectedCondition.value === "apply"
      }, selectedCondition.value === "apply" ? {} : {}, {
        g: selectedCondition.value === "apply" ? 1 : "",
        h: common_vendor.o(($event) => selectCondition("apply")),
        i: selectedCondition.value === "purchase"
      }, selectedCondition.value === "purchase" ? {} : {}, {
        j: selectedCondition.value === "purchase" ? 1 : "",
        k: common_vendor.o(($event) => selectCondition("purchase")),
        l: selectedCondition.value === "invite"
      }, selectedCondition.value === "invite" ? {} : {}, {
        m: selectedCondition.value === "invite" ? 1 : "",
        n: common_vendor.o(($event) => selectCondition("invite")),
        o: selectedCondition.value === "purchase"
      }, selectedCondition.value === "purchase" ? common_vendor.e({
        p: purchaseAmount.value,
        q: common_vendor.o(($event) => purchaseAmount.value = $event.detail.value),
        r: specificProducts.value,
        s: common_vendor.o(toggleSpecificProducts),
        t: specificProducts.value
      }, specificProducts.value ? {
        v: common_vendor.f(selectedProducts.value, (product, index, i0) => {
          return {
            a: product.image,
            b: common_vendor.t(product.name),
            c: common_vendor.t(product.price),
            d: common_vendor.o(($event) => removeProduct(index), index),
            e: index
          };
        }),
        w: common_vendor.o(addProduct)
      } : {}) : {}, {
        x: selectedCondition.value === "apply" || selectedCondition.value === "purchase"
      }, selectedCondition.value === "apply" || selectedCondition.value === "purchase" ? common_vendor.e({
        y: requireApproval.value,
        z: common_vendor.o(toggleRequireApproval),
        A: requireApproval.value
      }, requireApproval.value ? {
        B: autoApprove.value,
        C: common_vendor.o(toggleAutoApprove)
      } : {}) : {}, {
        D: common_vendor.o(saveSettings)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions.js.map
