{"version": 3, "file": "commission-rules.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25cY29tbWlzc2lvbi1ydWxlcy52dWU"], "sourcesContent": ["<template>\n  <view class=\"commission-rules-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">佣金规则设置</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 佣金模式设置 -->\n    <view class=\"section-card\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">佣金模式</text>\n      </view>\n      \n      <view class=\"mode-options\">\n        <view \n          class=\"mode-option\" \n          :class=\"{ 'active': settings.commissionMode === 'percentage' }\"\n          @click=\"settings.commissionMode = 'percentage'\"\n        >\n          <view class=\"option-icon percentage\"></view>\n          <view class=\"option-content\">\n            <text class=\"option-title\">按比例计算</text>\n            <text class=\"option-desc\">按商品售价的百分比计算佣金</text>\n          </view>\n        </view>\n        \n        <view \n          class=\"mode-option\" \n          :class=\"{ 'active': settings.commissionMode === 'fixed' }\"\n          @click=\"settings.commissionMode = 'fixed'\"\n        >\n          <view class=\"option-icon fixed\"></view>\n          <view class=\"option-content\">\n            <text class=\"option-title\">固定金额</text>\n            <text class=\"option-desc\">每件商品固定金额佣金</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 默认佣金设置 -->\n    <view class=\"section-card\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">默认佣金设置</text>\n        <text class=\"section-desc\">设置全平台通用的默认佣金比例，可在商品详情单独设置</text>\n      </view>\n      \n      <view class=\"commission-levels\">\n        <view class=\"level-item\">\n          <view class=\"level-header\">\n            <view class=\"level-icon level1\"></view>\n            <text class=\"level-name\">一级分销</text>\n          </view>\n          <view class=\"level-input-wrap\">\n            <input \n              class=\"level-input\" \n              type=\"digit\" \n              v-model=\"settings.defaultCommission.level1\" \n              :placeholder=\"settings.commissionMode === 'percentage' ? '佣金比例' : '固定金额'\"\n            />\n            <text class=\"input-unit\">{{settings.commissionMode === 'percentage' ? '%' : '元'}}</text>\n          </view>\n        </view>\n        \n        <view class=\"level-item\">\n          <view class=\"level-header\">\n            <view class=\"level-icon level2\"></view>\n            <text class=\"level-name\">二级分销</text>\n          </view>\n          <view class=\"level-input-wrap\">\n            <input \n              class=\"level-input\" \n              type=\"digit\" \n              v-model=\"settings.defaultCommission.level2\" \n              :placeholder=\"settings.commissionMode === 'percentage' ? '佣金比例' : '固定金额'\"\n            />\n            <text class=\"input-unit\">{{settings.commissionMode === 'percentage' ? '%' : '元'}}</text>\n          </view>\n        </view>\n        \n        <view class=\"level-item\" v-if=\"settings.enableLevel3\">\n          <view class=\"level-header\">\n            <view class=\"level-icon level3\"></view>\n            <text class=\"level-name\">三级分销</text>\n          </view>\n          <view class=\"level-input-wrap\">\n            <input \n              class=\"level-input\" \n              type=\"digit\" \n              v-model=\"settings.defaultCommission.level3\" \n              :placeholder=\"settings.commissionMode === 'percentage' ? '佣金比例' : '固定金额'\"\n            />\n            <text class=\"input-unit\">{{settings.commissionMode === 'percentage' ? '%' : '元'}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"switch-item\">\n        <view class=\"switch-label\">\n          <text class=\"label-text\">启用三级分销</text>\n        </view>\n        <switch \n          :checked=\"settings.enableLevel3\" \n          color=\"#6B0FBE\" \n          @change=\"toggleLevel3\"\n        />\n      </view>\n    </view>\n    \n    <!-- 佣金计算规则 -->\n    <view class=\"section-card\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">佣金计算规则</text>\n      </view>\n      \n      <view class=\"rule-item\">\n        <view class=\"rule-label\">\n          <text class=\"label-text\">计算基数</text>\n        </view>\n        <view class=\"rule-value\">\n          <picker \n            mode=\"selector\" \n            :range=\"baseOptions\" \n            range-key=\"name\"\n            :value=\"baseIndex\"\n            @change=\"onBaseChange\"\n          >\n            <view class=\"picker-value\">\n              <text>{{baseOptions[baseIndex].name}}</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </picker>\n        </view>\n      </view>\n      \n      <view class=\"rule-item\">\n        <view class=\"rule-label\">\n          <text class=\"label-text\">结算时机</text>\n        </view>\n        <view class=\"rule-value\">\n          <picker \n            mode=\"selector\" \n            :range=\"settlementOptions\" \n            range-key=\"name\"\n            :value=\"settlementIndex\"\n            @change=\"onSettlementChange\"\n          >\n            <view class=\"picker-value\">\n              <text>{{settlementOptions[settlementIndex].name}}</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </picker>\n        </view>\n      </view>\n      \n      <view class=\"rule-item\">\n        <view class=\"rule-label\">\n          <text class=\"label-text\">冻结天数</text>\n          <text class=\"label-desc\">订单完成后需等待的天数</text>\n        </view>\n        <view class=\"rule-value\">\n          <input \n            class=\"rule-input\" \n            type=\"number\" \n            v-model=\"settings.freezeDays\" \n            placeholder=\"天数\"\n          />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 佣金限制 -->\n    <view class=\"section-card\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">佣金限制</text>\n      </view>\n      \n      <view class=\"switch-item\">\n        <view class=\"switch-label\">\n          <text class=\"label-text\">设置佣金上限</text>\n          <text class=\"label-desc\">启用后，单笔订单佣金不超过设定值</text>\n        </view>\n        <switch \n          :checked=\"settings.enableMaxCommission\" \n          color=\"#6B0FBE\" \n          @change=\"toggleMaxCommission\"\n        />\n      </view>\n      \n      <view class=\"limit-input\" v-if=\"settings.enableMaxCommission\">\n        <text class=\"limit-label\">最高佣金金额</text>\n        <view class=\"input-wrap\">\n          <text class=\"currency\">¥</text>\n          <input \n            class=\"limit-value\" \n            type=\"digit\" \n            v-model=\"settings.maxCommission\" \n            placeholder=\"请输入金额\"\n          />\n        </view>\n      </view>\n      \n      <view class=\"switch-item\">\n        <view class=\"switch-label\">\n          <text class=\"label-text\">自购返佣</text>\n          <text class=\"label-desc\">分销员购买自己分销的商品是否获得佣金</text>\n        </view>\n        <switch \n          :checked=\"settings.selfPurchaseCommission\" \n          color=\"#6B0FBE\" \n          @change=\"toggleSelfPurchase\"\n        />\n      </view>\n    </view>\n    \n    <!-- 保存按钮 -->\n    <view class=\"save-section\">\n      <button class=\"save-btn\" @click=\"saveSettings\">保存设置</button>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport distributionService from '@/utils/distributionService';\n\n// 佣金规则设置\nconst settings = reactive({\n  commissionMode: 'percentage', // 佣金模式: percentage(按比例), fixed(固定金额)\n  defaultCommission: {\n    level1: 10, // 一级佣金比例\n    level2: 5,  // 二级佣金比例\n    level3: 3   // 三级佣金比例\n  },\n  enableLevel3: false, // 是否启用三级分销\n  calculationBase: 'total', // 计算基数: total(订单总额), profit(利润)\n  settlementTiming: 'completed', // 结算时机: paid(支付后), completed(完成后), confirmed(确认收货后)\n  freezeDays: 7, // 冻结天数\n  enableMaxCommission: false, // 是否启用最高佣金限制\n  maxCommission: 100, // 最高佣金金额\n  selfPurchaseCommission: true // 自购是否返佣\n});\n\n// 计算基数选项\nconst baseOptions = [\n  { value: 'total', name: '订单总额' },\n  { value: 'profit', name: '商品利润' }\n];\n\n// 结算时机选项\nconst settlementOptions = [\n  { value: 'paid', name: '支付后' },\n  { value: 'completed', name: '完成后' },\n  { value: 'confirmed', name: '确认收货后' }\n];\n\n// 当前选中的计算基数索引\nconst baseIndex = computed(() => {\n  return baseOptions.findIndex(option => option.value === settings.calculationBase);\n});\n\n// 当前选中的结算时机索引\nconst settlementIndex = computed(() => {\n  return settlementOptions.findIndex(option => option.value === settings.settlementTiming);\n});\n\n// 页面加载\nonMounted(async () => {\n  // 获取佣金规则设置\n  await getCommissionRules();\n});\n\n// 获取佣金规则设置\nconst getCommissionRules = async () => {\n  try {\n    const result = await distributionService.getCommissionRules();\n    \n    if (result) {\n      Object.assign(settings, result);\n    }\n  } catch (error) {\n    console.error('获取佣金规则设置失败', error);\n    uni.showToast({\n      title: '获取佣金规则设置失败',\n      icon: 'none'\n    });\n  }\n};\n\n// 切换三级分销\nconst toggleLevel3 = (e) => {\n  settings.enableLevel3 = e.detail.value;\n};\n\n// 切换最高佣金限制\nconst toggleMaxCommission = (e) => {\n  settings.enableMaxCommission = e.detail.value;\n};\n\n// 切换自购返佣\nconst toggleSelfPurchase = (e) => {\n  settings.selfPurchaseCommission = e.detail.value;\n};\n\n// 计算基数变化\nconst onBaseChange = (e) => {\n  const index = e.detail.value;\n  settings.calculationBase = baseOptions[index].value;\n};\n\n// 结算时机变化\nconst onSettlementChange = (e) => {\n  const index = e.detail.value;\n  settings.settlementTiming = settlementOptions[index].value;\n};\n\n// 保存设置\nconst saveSettings = async () => {\n  try {\n    uni.showLoading({\n      title: '保存中...',\n      mask: true\n    });\n    \n    const result = await distributionService.saveCommissionRules(settings);\n    \n    uni.hideLoading();\n    \n    if (result.success) {\n      uni.showToast({\n        title: '保存成功',\n        icon: 'success'\n      });\n    } else {\n      uni.showModal({\n        title: '保存失败',\n        content: result.message || '请稍后再试',\n        showCancel: false\n      });\n    }\n  } catch (error) {\n    uni.hideLoading();\n    console.error('保存佣金规则设置失败', error);\n    uni.showToast({\n      title: '保存失败',\n      icon: 'none'\n    });\n  }\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 显示帮助\nconst showHelp = () => {\n  uni.showModal({\n    title: '佣金规则帮助',\n    content: '佣金规则设置用于配置分销佣金的计算方式、结算时机和限制条件。您可以设置按比例或固定金额计算佣金，并配置多级分销的佣金分配。',\n    showCancel: false\n  });\n};\n</script>\n\n<style lang=\"scss\">\n.commission-rules-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 30rpx;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 88rpx 32rpx 30rpx;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 24rpx;\n  height: 24rpx;\n  border-left: 4rpx solid #fff;\n  border-bottom: 4rpx solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 36rpx;\n  font-weight: 600;\n  letter-spacing: 1rpx;\n}\n\n.navbar-right {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 24rpx;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  font-weight: bold;\n}\n\n/* 卡片通用样式 */\n.section-card {\n  margin: 30rpx;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  margin-bottom: 30rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.section-desc {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 8rpx;\n  display: block;\n}\n\n/* 佣金模式 */\n.mode-options {\n  display: flex;\n  margin: 0 -10rpx;\n}\n\n.mode-option {\n  flex: 1;\n  margin: 0 10rpx;\n  background: #F5F7FA;\n  border-radius: 20rpx;\n  padding: 20rpx;\n  display: flex;\n  align-items: center;\n  border: 2rpx solid transparent;\n}\n\n.mode-option.active {\n  border-color: #6B0FBE;\n  background: rgba(107, 15, 190, 0.05);\n}\n\n.option-icon {\n  width: 80rpx;\n  height: 80rpx;\n  margin-right: 20rpx;\n  background-size: contain;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n\n.option-icon.percentage {\n  background-color: #6B0FBE;\n  border-radius: 50%;\n  position: relative;\n}\n\n.option-icon.percentage::after {\n  content: '%';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.option-icon.fixed {\n  background-color: #FF9500;\n  border-radius: 50%;\n  position: relative;\n}\n\n.option-icon.fixed::after {\n  content: '¥';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.option-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.option-desc {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 佣金等级 */\n.commission-levels {\n  margin-bottom: 30rpx;\n}\n\n.level-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.level-header {\n  display: flex;\n  align-items: center;\n}\n\n.level-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 16rpx;\n  background-size: contain;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n\n.level-icon.level1 {\n  background-color: #6B0FBE;\n  border-radius: 50%;\n  position: relative;\n}\n\n.level-icon.level1::after {\n  content: '1';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 24rpx;\n  font-weight: bold;\n}\n\n.level-icon.level2 {\n  background-color: #409EFF;\n  border-radius: 50%;\n  position: relative;\n}\n\n.level-icon.level2::after {\n  content: '2';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 24rpx;\n  font-weight: bold;\n}\n\n.level-icon.level3 {\n  background-color: #67C23A;\n  border-radius: 50%;\n  position: relative;\n}\n\n.level-icon.level3::after {\n  content: '3';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 24rpx;\n  font-weight: bold;\n}\n\n.level-name {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.level-input-wrap {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 10rpx;\n  padding: 0 20rpx;\n  height: 80rpx;\n  width: 200rpx;\n}\n\n.level-input {\n  flex: 1;\n  height: 100%;\n  font-size: 28rpx;\n  color: #333;\n  text-align: right;\n}\n\n.input-unit {\n  font-size: 28rpx;\n  color: #333;\n  margin-left: 10rpx;\n}\n\n/* 开关项 */\n.switch-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.switch-item:last-child {\n  margin-bottom: 0;\n}\n\n.switch-label {\n  flex: 1;\n}\n\n.label-text {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 4rpx;\n  display: block;\n}\n\n.label-desc {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 规则项 */\n.rule-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.rule-label {\n  flex: 1;\n}\n\n.rule-value {\n  width: 200rpx;\n}\n\n.picker-value {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.arrow-icon {\n  width: 16rpx;\n  height: 16rpx;\n  border-right: 2rpx solid #999;\n  border-bottom: 2rpx solid #999;\n  transform: rotate(45deg);\n  margin-left: 10rpx;\n}\n\n.rule-input {\n  background: #F5F7FA;\n  border-radius: 10rpx;\n  padding: 0 20rpx;\n  height: 80rpx;\n  font-size: 28rpx;\n  color: #333;\n  text-align: center;\n}\n\n/* 限制输入 */\n.limit-input {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.limit-label {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.input-wrap {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 10rpx;\n  padding: 0 20rpx;\n  height: 80rpx;\n  width: 200rpx;\n}\n\n.currency {\n  font-size: 28rpx;\n  color: #333;\n  margin-right: 10rpx;\n}\n\n.limit-value {\n  flex: 1;\n  height: 100%;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 保存按钮 */\n.save-section {\n  margin: 30rpx;\n}\n\n.save-btn {\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 32rpx;\n  padding: 20rpx 0;\n  line-height: 1.5;\n  width: 100%;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "computed", "onMounted", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAyOA,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,gBAAgB;AAAA;AAAA,MAChB,mBAAmB;AAAA,QACjB,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,MACT;AAAA,MACD,cAAc;AAAA;AAAA,MACd,iBAAiB;AAAA;AAAA,MACjB,kBAAkB;AAAA;AAAA,MAClB,YAAY;AAAA;AAAA,MACZ,qBAAqB;AAAA;AAAA,MACrB,eAAe;AAAA;AAAA,MACf,wBAAwB;AAAA;AAAA,IAC1B,CAAC;AAGD,UAAM,cAAc;AAAA,MAClB,EAAE,OAAO,SAAS,MAAM,OAAQ;AAAA,MAChC,EAAE,OAAO,UAAU,MAAM,OAAQ;AAAA,IACnC;AAGA,UAAM,oBAAoB;AAAA,MACxB,EAAE,OAAO,QAAQ,MAAM,MAAO;AAAA,MAC9B,EAAE,OAAO,aAAa,MAAM,MAAO;AAAA,MACnC,EAAE,OAAO,aAAa,MAAM,QAAS;AAAA,IACvC;AAGA,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC/B,aAAO,YAAY,UAAU,YAAU,OAAO,UAAU,SAAS,eAAe;AAAA,IAClF,CAAC;AAGD,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;AACrC,aAAO,kBAAkB,UAAU,YAAU,OAAO,UAAU,SAAS,gBAAgB;AAAA,IACzF,CAAC;AAGDC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,mBAAkB;AAAA,IAC1B,CAAC;AAGD,UAAM,qBAAqB,YAAY;AACrC,UAAI;AACF,cAAM,SAAS,MAAMC,8CAAoB;AAEzC,YAAI,QAAQ;AACV,iBAAO,OAAO,UAAU,MAAM;AAAA,QAC/B;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,iGAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,eAAe,EAAE,OAAO;AAAA,IACnC;AAGA,UAAM,sBAAsB,CAAC,MAAM;AACjC,eAAS,sBAAsB,EAAE,OAAO;AAAA,IAC1C;AAGA,UAAM,qBAAqB,CAAC,MAAM;AAChC,eAAS,yBAAyB,EAAE,OAAO;AAAA,IAC7C;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,YAAM,QAAQ,EAAE,OAAO;AACvB,eAAS,kBAAkB,YAAY,KAAK,EAAE;AAAA,IAChD;AAGA,UAAM,qBAAqB,CAAC,MAAM;AAChC,YAAM,QAAQ,EAAE,OAAO;AACvB,eAAS,mBAAmB,kBAAkB,KAAK,EAAE;AAAA,IACvD;AAGA,UAAM,eAAe,YAAY;AAC/B,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,SAAS,MAAMD,0BAAAA,oBAAoB,oBAAoB,QAAQ;AAErEC,sBAAG,MAAC,YAAW;AAEf,YAAI,OAAO,SAAS;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACP,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,OAAO,WAAW;AAAA,YAC3B,YAAY;AAAA,UACpB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,iGAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/WA,GAAG,WAAWC,SAAe;"}