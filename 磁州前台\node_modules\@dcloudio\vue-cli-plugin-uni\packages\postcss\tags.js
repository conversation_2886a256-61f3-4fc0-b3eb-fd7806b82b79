module.exports = {
  br: 'r',
  hr: 'r',

  p: 'r',
  h1: 'r',
  h2: 'r',
  h3: 'r',
  h4: 'r',
  h5: 'r',
  h6: 'r',
  abbr: 'r',
  address: 'r',
  b: 'r',
  bdi: 'r',
  bdo: 'r',
  blockquote: 'r',
  cite: 'r',
  code: 'r',
  del: 'r',
  ins: 'r',
  dfn: 'r',
  em: 'r',
  strong: 'r',
  samp: 'r',
  kbd: 'r',
  var: 'r',
  i: 'r',
  mark: 'r',
  pre: 'r',
  q: 'r',
  ruby: 'r',
  rp: 'r',
  rt: 'r',
  s: 'r',
  small: 'r',
  sub: 'r',
  sup: 'r',
  time: 'r',
  u: 'r',
  wbr: 'r',

  // 表单元素
  // 'form': 'r',
  // 'input': 'r',
  // 'textarea': 'r',
  // 'button': 'r',
  select: 'r',
  option: 'r',
  optgroup: 'r',
  // 'label': 'r',
  fieldset: 'r',
  datalist: 'r',
  legend: 'r',
  output: 'r',

  // 框架
  iframe: 'r',
  // 图像
  img: 'r',
  // 'canvas': 'r',
  figure: 'r',
  figcaption: 'r',

  // 音视频
  // 'audio': 'r',
  source: 'r',
  // 'video': 'r',
  track: 'r',
  // 链接
  a: 'r',
  nav: 'r',
  link: 'r',
  // 列表
  ul: 'r',
  ol: 'r',
  li: 'r',
  dl: 'r',
  dt: 'r',
  dd: 'r',
  menu: 'r',
  command: 'r',

  // 表格table
  table: 'r',
  caption: 'r',
  th: 'r',
  td: 'r',
  tr: 'r',
  thead: 'r',
  tbody: 'r',
  tfoot: 'r',
  col: 'r',
  colgroup: 'r',
  // 样式 节
  div: 'r',
  main: 'r',
  span: 'r',
  header: 'r',
  footer: 'r',
  section: 'r',
  article: 'r',
  aside: 'r',
  details: 'r',
  dialog: 'r',
  summary: 'r',

  // 'progress': 'r',
  meter: 'r', // todo
  head: 'r', // todo
  meta: 'r', // todo
  base: 'r', // todo
  // 'map': 'r', // TODO不是很恰当
  area: 'r', // j结合map使用

  script: 'r',
  noscript: 'r',
  embed: 'r',
  object: 'r',
  param: 'r',
  body: 'page',
  html: 'page'
}
