{"name": "uni-app-tongcheng", "version": "1.0.0", "description": "同城生活服务平台", "main": "main.js", "scripts": {"dev": "npx @dcloudio/vite-plugin-uni", "dev:mp-weixin": "npx @dcloudio/vite-plugin-uni -p mp-weixin", "build": "npx @dcloudio/vite-plugin-uni build", "build:mp-weixin": "npx @dcloudio/vite-plugin-uni build -p mp-weixin"}, "author": "", "license": "ISC", "dependencies": {"@dcloudio/uni-app": "^2.0.2", "@dcloudio/uni-app-plus": "^2.0.2", "@dcloudio/uni-h5": "^2.0.2", "@dcloudio/uni-mp-alipay": "^2.0.2", "@dcloudio/uni-mp-baidu": "^2.0.2", "@dcloudio/uni-mp-qq": "^2.0.2", "@dcloudio/uni-mp-toutiao": "^2.0.2", "@dcloudio/uni-mp-weixin": "^2.0.2", "@dcloudio/uni-quickapp-native": "^2.0.2", "@dcloudio/uni-quickapp-webview": "^2.0.2", "@dcloudio/uni-stat": "^2.0.2", "@vue/shared": "^3.0.0", "vue": "^3.2.45"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^2.0.2", "@dcloudio/uni-cli-shared": "^2.0.2", "@dcloudio/vite-plugin-uni": "^3.0.0", "sass": "^1.58.3", "typescript": "^4.9.5", "vite": "^4.1.4"}}