{"version": 3, "file": "vip-service.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/vip-service.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcdmlwLXNlcnZpY2UudnVl"], "sourcesContent": ["<!-- 专属客服页面开始 -->\r\n<template>\r\n  <view class=\"vip-service-container\">\r\n    <!-- 页面标题区域 -->\r\n    <view class=\"page-header\">\r\n      <view class=\"title-section\">\r\n        <text class=\"page-title\">专属客服</text>\r\n        <text class=\"page-subtitle\">管理会员专属客服服务</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 专属客服开关 -->\r\n    <view class=\"section-card\">\r\n      <view class=\"switch-item\">\r\n        <view class=\"switch-content\">\r\n          <text class=\"switch-title\">专属客服服务</text>\r\n          <text class=\"switch-desc\">开启后，指定等级会员可享受专属客服服务</text>\r\n        </view>\r\n        <switch :checked=\"serviceSettings.enabled\" @change=\"toggleVipService\" color=\"#4A00E0\" />\r\n      </view>\r\n    </view>\r\n    \r\n    <block v-if=\"serviceSettings.enabled\">\r\n      <!-- 服务设置 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">服务设置</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">服务类型</text>\r\n          <view class=\"checkbox-group\">\r\n            <view \r\n              v-for=\"(type, index) in serviceTypes\" \r\n              :key=\"index\" \r\n              class=\"checkbox-item\" \r\n              :class=\"{ active: isServiceTypeSelected(type.value) }\"\r\n              @click=\"toggleServiceType(type.value)\"\r\n            >\r\n              <view class=\"checkbox-icon\">\r\n                <view class=\"checkbox-inner\" v-if=\"isServiceTypeSelected(type.value)\"></view>\r\n              </view>\r\n              <text class=\"checkbox-text\">{{ type.label }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">优先级</text>\r\n          <view class=\"radio-group\">\r\n            <view \r\n              v-for=\"(priority, index) in priorityOptions\" \r\n              :key=\"index\" \r\n              class=\"radio-item\" \r\n              :class=\"{ active: serviceSettings.priority === priority.value }\"\r\n              @click=\"setPriority(priority.value)\"\r\n            >\r\n              <text class=\"radio-text\">{{ priority.label }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">响应时间</text>\r\n          <view class=\"form-input-group\">\r\n            <input type=\"number\" class=\"form-input\" v-model=\"serviceSettings.responseTime\" />\r\n            <text class=\"input-suffix\">分钟内</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item switch-item\">\r\n          <text class=\"form-label\">7×24小时服务</text>\r\n          <switch :checked=\"serviceSettings.fullTimeService\" @change=\"toggleFullTimeService\" color=\"#4A00E0\" />\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 客服人员设置 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">客服人员</view>\r\n        \r\n        <view class=\"staff-list\">\r\n          <view class=\"staff-item\" v-for=\"(staff, index) in staffList\" :key=\"index\">\r\n            <view class=\"staff-info\">\r\n              <image class=\"staff-avatar\" :src=\"staff.avatar\" mode=\"aspectFill\"></image>\r\n              <view class=\"staff-detail\">\r\n                <view class=\"staff-name\">{{ staff.name }}</view>\r\n                <view class=\"staff-position\">{{ staff.position }}</view>\r\n              </view>\r\n            </view>\r\n            <view class=\"staff-actions\">\r\n              <view class=\"staff-status\" :class=\"{ active: staff.active }\">\r\n                {{ staff.active ? '在线' : '离线' }}\r\n              </view>\r\n              <view class=\"action-btn edit\" @click=\"editStaff(staff)\">编辑</view>\r\n              <view class=\"action-btn delete\" @click=\"confirmDeleteStaff(staff)\">删除</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <button class=\"add-btn\" @click=\"showAddStaffModal\">添加客服人员</button>\r\n      </view>\r\n      \r\n      <!-- 适用会员等级 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">适用会员等级</view>\r\n        \r\n        <view class=\"level-list\">\r\n          <view class=\"level-item\" v-for=\"(level, index) in memberLevels\" :key=\"index\">\r\n            <view class=\"level-checkbox\" :class=\"{ checked: level.selected }\" @click=\"toggleLevel(level)\">\r\n              <view class=\"checkbox-inner\" v-if=\"level.selected\"></view>\r\n            </view>\r\n            <view class=\"level-content\">\r\n              <text class=\"level-name\">{{level.name}}</text>\r\n              <text class=\"level-desc\">{{level.memberCount}}名会员</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 服务说明 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">服务说明</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <textarea class=\"form-textarea\" v-model=\"serviceSettings.description\" placeholder=\"请输入专属客服服务说明\" />\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 保存按钮 -->\r\n      <view class=\"bottom-bar\">\r\n        <button class=\"save-btn\" @click=\"saveSettings\">保存设置</button>\r\n      </view>\r\n    </block>\r\n    \r\n    <!-- 添加/编辑客服弹窗 -->\r\n    <uni-popup ref=\"staffFormPopup\" type=\"center\">\r\n      <view class=\"staff-form-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">{{ isEditing ? '编辑客服' : '添加客服' }}</text>\r\n          <text class=\"popup-close\" @click=\"closeStaffModal\">×</text>\r\n        </view>\r\n        <view class=\"popup-body\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">客服姓名</text>\r\n            <input class=\"form-input\" v-model=\"staffForm.name\" placeholder=\"请输入客服姓名\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">客服头像</text>\r\n            <view class=\"avatar-upload\">\r\n              <image v-if=\"staffForm.avatar\" class=\"preview-avatar\" :src=\"staffForm.avatar\" mode=\"aspectFill\"></image>\r\n              <view v-else class=\"upload-btn\" @click=\"chooseAvatar\">\r\n                <text class=\"upload-icon\">+</text>\r\n                <text class=\"upload-text\">上传头像</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">职位</text>\r\n            <input class=\"form-input\" v-model=\"staffForm.position\" placeholder=\"请输入客服职位\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">手机号码</text>\r\n            <input class=\"form-input\" v-model=\"staffForm.phone\" placeholder=\"请输入客服手机号码\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">微信号</text>\r\n            <input class=\"form-input\" v-model=\"staffForm.wechat\" placeholder=\"请输入客服微信号\" />\r\n          </view>\r\n          <view class=\"form-item switch-item\">\r\n            <text class=\"form-label\">在线状态</text>\r\n            <switch :checked=\"staffForm.active\" @change=\"toggleStaffStatus\" color=\"#4A00E0\" />\r\n          </view>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <button class=\"cancel-btn\" @click=\"closeStaffModal\">取消</button>\r\n          <button class=\"confirm-btn\" @click=\"saveStaffForm\">确认</button>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 删除确认弹窗 -->\r\n    <uni-popup ref=\"deleteConfirmPopup\" type=\"dialog\">\r\n      <uni-popup-dialog\r\n        type=\"warning\"\r\n        title=\"删除确认\"\r\n        content=\"确定要删除该客服人员吗？删除后将无法恢复。\"\r\n        :before-close=\"true\"\r\n        @confirm=\"deleteStaff\"\r\n        @close=\"closeDeleteConfirm\"\r\n      ></uni-popup-dialog>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 专属客服服务设置\r\n      serviceSettings: {\r\n        enabled: true,\r\n        serviceTypes: ['online', 'phone', 'wechat'],\r\n        priority: 'high',\r\n        responseTime: 5,\r\n        fullTimeService: false,\r\n        description: '会员专享一对一客服服务，为您提供更快速、更专业、更贴心的服务体验。'\r\n      },\r\n      \r\n      // 客服类型选项\r\n      serviceTypes: [\r\n        { label: '在线客服', value: 'online' },\r\n        { label: '电话客服', value: 'phone' },\r\n        { label: '微信客服', value: 'wechat' },\r\n        { label: '上门服务', value: 'visit' }\r\n      ],\r\n      \r\n      // 优先级选项\r\n      priorityOptions: [\r\n        { label: '普通', value: 'normal' },\r\n        { label: '优先', value: 'medium' },\r\n        { label: '最高', value: 'high' }\r\n      ],\r\n      \r\n      // 客服人员列表\r\n      staffList: [\r\n        {\r\n          id: 1,\r\n          name: '张小美',\r\n          avatar: '/static/images/avatar-1.png',\r\n          position: '高级客服专员',\r\n          phone: '13800138001',\r\n          wechat: 'cs_xiaomei',\r\n          active: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '李大壮',\r\n          avatar: '/static/images/avatar-2.png',\r\n          position: '客服主管',\r\n          phone: '13800138002',\r\n          wechat: 'cs_dazhuang',\r\n          active: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '王晓丽',\r\n          avatar: '/static/images/avatar-3.png',\r\n          position: '资深客服专员',\r\n          phone: '13800138003',\r\n          wechat: 'cs_xiaoli',\r\n          active: false\r\n        }\r\n      ],\r\n      \r\n      // 会员等级\r\n      memberLevels: [\r\n        {\r\n          id: 1,\r\n          name: '普通会员',\r\n          memberCount: 2156,\r\n          selected: false\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '银卡会员',\r\n          memberCount: 864,\r\n          selected: false\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '金卡会员',\r\n          memberCount: 426,\r\n          selected: true\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '钻石会员',\r\n          memberCount: 116,\r\n          selected: true\r\n        }\r\n      ],\r\n      \r\n      // 客服表单\r\n      staffForm: {\r\n        id: '',\r\n        name: '',\r\n        avatar: '',\r\n        position: '',\r\n        phone: '',\r\n        wechat: '',\r\n        active: true\r\n      },\r\n      \r\n      isEditing: false,\r\n      currentStaffId: null\r\n    };\r\n  },\r\n  methods: {\r\n    // 切换专属客服服务\r\n    toggleVipService(e) {\r\n      this.serviceSettings.enabled = e.detail.value;\r\n    },\r\n    \r\n    // 检查服务类型是否选中\r\n    isServiceTypeSelected(type) {\r\n      return this.serviceSettings.serviceTypes.includes(type);\r\n    },\r\n    \r\n    // 切换服务类型\r\n    toggleServiceType(type) {\r\n      const index = this.serviceSettings.serviceTypes.indexOf(type);\r\n      if (index === -1) {\r\n        this.serviceSettings.serviceTypes.push(type);\r\n      } else {\r\n        this.serviceSettings.serviceTypes.splice(index, 1);\r\n      }\r\n    },\r\n    \r\n    // 设置优先级\r\n    setPriority(priority) {\r\n      this.serviceSettings.priority = priority;\r\n    },\r\n    \r\n    // 切换全时服务\r\n    toggleFullTimeService(e) {\r\n      this.serviceSettings.fullTimeService = e.detail.value;\r\n    },\r\n    \r\n    // 切换会员等级\r\n    toggleLevel(level) {\r\n      const index = this.memberLevels.findIndex(item => item.id === level.id);\r\n      if (index !== -1) {\r\n        this.memberLevels[index].selected = !this.memberLevels[index].selected;\r\n      }\r\n    },\r\n    \r\n    // 显示添加客服弹窗\r\n    showAddStaffModal() {\r\n      this.isEditing = false;\r\n      this.staffForm = {\r\n        id: '',\r\n        name: '',\r\n        avatar: '',\r\n        position: '',\r\n        phone: '',\r\n        wechat: '',\r\n        active: true\r\n      };\r\n      this.$refs.staffFormPopup.open();\r\n    },\r\n    \r\n    // 编辑客服\r\n    editStaff(staff) {\r\n      this.isEditing = true;\r\n      this.currentStaffId = staff.id;\r\n      this.staffForm = JSON.parse(JSON.stringify(staff)); // 深拷贝\r\n      this.$refs.staffFormPopup.open();\r\n    },\r\n    \r\n    // 关闭客服表单弹窗\r\n    closeStaffModal() {\r\n      this.$refs.staffFormPopup.close();\r\n    },\r\n    \r\n    // 切换客服状态\r\n    toggleStaffStatus(e) {\r\n      this.staffForm.active = e.detail.value;\r\n    },\r\n    \r\n    // 选择头像\r\n    chooseAvatar() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        success: (res) => {\r\n          this.staffForm.avatar = res.tempFilePaths[0];\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 保存客服表单\r\n    saveStaffForm() {\r\n      // 表单验证\r\n      if (!this.staffForm.name) {\r\n        uni.showToast({\r\n          title: '请输入客服姓名',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.staffForm.position) {\r\n        uni.showToast({\r\n          title: '请输入客服职位',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 保存数据\r\n      if (this.isEditing) {\r\n        // 编辑现有客服\r\n        const index = this.staffList.findIndex(item => item.id === this.currentStaffId);\r\n        if (index !== -1) {\r\n          this.staffList.splice(index, 1, JSON.parse(JSON.stringify(this.staffForm)));\r\n        }\r\n      } else {\r\n        // 添加新客服\r\n        this.staffForm.id = Date.now();\r\n        this.staffList.push(JSON.parse(JSON.stringify(this.staffForm)));\r\n      }\r\n      \r\n      // 关闭弹窗\r\n      this.closeStaffModal();\r\n      \r\n      // 提示保存成功\r\n      uni.showToast({\r\n        title: this.isEditing ? '客服修改成功' : '客服添加成功'\r\n      });\r\n    },\r\n    \r\n    // 确认删除客服\r\n    confirmDeleteStaff(staff) {\r\n      this.currentStaffId = staff.id;\r\n      this.$refs.deleteConfirmPopup.open();\r\n    },\r\n    \r\n    // 删除客服\r\n    deleteStaff() {\r\n      const index = this.staffList.findIndex(item => item.id === this.currentStaffId);\r\n      if (index !== -1) {\r\n        this.staffList.splice(index, 1);\r\n      }\r\n      \r\n      this.$refs.deleteConfirmPopup.close();\r\n      \r\n      uni.showToast({\r\n        title: '客服删除成功'\r\n      });\r\n    },\r\n    \r\n    // 关闭删除确认弹窗\r\n    closeDeleteConfirm() {\r\n      this.$refs.deleteConfirmPopup.close();\r\n    },\r\n    \r\n    // 保存设置\r\n    saveSettings() {\r\n      // 这里应该调用API保存设置\r\n      uni.showToast({\r\n        title: '设置保存成功',\r\n        icon: 'success'\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.vip-service-container {\r\n  padding: 20rpx;\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.page-header {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.title-section {\r\n  .page-title {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n  \r\n  .page-subtitle {\r\n    font-size: 24rpx;\r\n    color: #666;\r\n    margin-top: 6rpx;\r\n  }\r\n}\r\n\r\n.section-card {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 20rpx;\r\n  padding: 24rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .section-title {\r\n    font-size: 30rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin-bottom: 20rpx;\r\n    position: relative;\r\n    padding-left: 20rpx;\r\n    \r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      left: 0;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      width: 8rpx;\r\n      height: 30rpx;\r\n      background-color: #4A00E0;\r\n      border-radius: 4rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.switch-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  \r\n  .switch-content {\r\n    .switch-title {\r\n      font-size: 30rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n    \r\n    .switch-desc {\r\n      font-size: 24rpx;\r\n      color: #666;\r\n      margin-top: 6rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 24rpx;\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n  \r\n  .form-label {\r\n    display: block;\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    margin-bottom: 12rpx;\r\n  }\r\n  \r\n  .form-input {\r\n    width: 100%;\r\n    height: 80rpx;\r\n    border: 1rpx solid #ddd;\r\n    border-radius: 8rpx;\r\n    padding: 0 20rpx;\r\n    font-size: 28rpx;\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .form-input-group {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .form-input {\r\n      width: 200rpx;\r\n    }\r\n    \r\n    .input-suffix {\r\n      margin-left: 10rpx;\r\n      font-size: 28rpx;\r\n      color: #333;\r\n    }\r\n  }\r\n  \r\n  .form-textarea {\r\n    width: 100%;\r\n    height: 160rpx;\r\n    border: 1rpx solid #ddd;\r\n    border-radius: 8rpx;\r\n    padding: 20rpx;\r\n    font-size: 28rpx;\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .checkbox-group {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    \r\n    .checkbox-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-right: 30rpx;\r\n      margin-bottom: 20rpx;\r\n      \r\n      .checkbox-icon {\r\n        width: 40rpx;\r\n        height: 40rpx;\r\n        border: 1rpx solid #ddd;\r\n        border-radius: 8rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-right: 10rpx;\r\n        \r\n        .checkbox-inner {\r\n          width: 24rpx;\r\n          height: 24rpx;\r\n          background-color: #4A00E0;\r\n        }\r\n      }\r\n      \r\n      &.active .checkbox-icon {\r\n        border-color: #4A00E0;\r\n      }\r\n      \r\n      .checkbox-text {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .radio-group {\r\n    display: flex;\r\n    \r\n    .radio-item {\r\n      padding: 12rpx 30rpx;\r\n      border: 1rpx solid #ddd;\r\n      border-radius: 8rpx;\r\n      margin-right: 20rpx;\r\n      \r\n      .radio-text {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n      }\r\n      \r\n      &.active {\r\n        background-color: #4A00E0;\r\n        border-color: #4A00E0;\r\n        \r\n        .radio-text {\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  &.switch-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    \r\n    .form-label {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  \r\n  .avatar-upload {\r\n    .preview-avatar {\r\n      width: 120rpx;\r\n      height: 120rpx;\r\n      border-radius: 50%;\r\n    }\r\n    \r\n    .upload-btn {\r\n      width: 120rpx;\r\n      height: 120rpx;\r\n      border: 1rpx dashed #ddd;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      \r\n      .upload-icon {\r\n        font-size: 40rpx;\r\n        color: #999;\r\n        margin-bottom: 4rpx;\r\n      }\r\n      \r\n      .upload-text {\r\n        font-size: 20rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.staff-list {\r\n  margin-bottom: 24rpx;\r\n  \r\n  .staff-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20rpx 0;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n    \r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    .staff-info {\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .staff-avatar {\r\n        width: 80rpx;\r\n        height: 80rpx;\r\n        border-radius: 50%;\r\n        margin-right: 20rpx;\r\n      }\r\n      \r\n      .staff-detail {\r\n        .staff-name {\r\n          font-size: 28rpx;\r\n          color: #333;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        .staff-position {\r\n          font-size: 24rpx;\r\n          color: #666;\r\n          margin-top: 6rpx;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .staff-actions {\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .staff-status {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n        padding: 4rpx 16rpx;\r\n        background-color: #f5f5f5;\r\n        border-radius: 20rpx;\r\n        margin-right: 16rpx;\r\n        \r\n        &.active {\r\n          color: #4A00E0;\r\n          background-color: rgba(74, 0, 224, 0.1);\r\n        }\r\n      }\r\n      \r\n      .action-btn {\r\n        font-size: 24rpx;\r\n        padding: 4rpx 16rpx;\r\n        border-radius: 20rpx;\r\n        margin-left: 10rpx;\r\n        \r\n        &.edit {\r\n          color: #4A00E0;\r\n          background-color: rgba(74, 0, 224, 0.1);\r\n        }\r\n        \r\n        &.delete {\r\n          color: #ff4d4f;\r\n          background-color: rgba(255, 77, 79, 0.1);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.add-btn {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  background-color: #f5f5f5;\r\n  color: #666;\r\n  font-size: 28rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.level-list {\r\n  .level-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx 0;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n    \r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    .level-checkbox {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      border: 1rpx solid #ddd;\r\n      border-radius: 8rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 20rpx;\r\n      \r\n      &.checked {\r\n        background-color: #4A00E0;\r\n        border-color: #4A00E0;\r\n      }\r\n      \r\n      .checkbox-inner {\r\n        width: 20rpx;\r\n        height: 20rpx;\r\n        background-color: #fff;\r\n      }\r\n    }\r\n    \r\n    .level-content {\r\n      flex: 1;\r\n      \r\n      .level-name {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        font-weight: bold;\r\n      }\r\n      \r\n      .level-desc {\r\n        font-size: 24rpx;\r\n        color: #666;\r\n        margin-top: 6rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.bottom-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  padding: 20rpx;\r\n  background-color: #fff;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .save-btn {\r\n    width: 100%;\r\n    height: 90rpx;\r\n    background-color: #4A00E0;\r\n    color: #fff;\r\n    font-size: 32rpx;\r\n    border-radius: 45rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n.staff-form-popup {\r\n  width: 650rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  \r\n  .popup-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24rpx;\r\n    border-bottom: 1rpx solid #eee;\r\n    \r\n    .popup-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n    \r\n    .popup-close {\r\n      font-size: 40rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n  \r\n  .popup-body {\r\n    padding: 24rpx;\r\n    max-height: 60vh;\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  .popup-footer {\r\n    display: flex;\r\n    border-top: 1rpx solid #eee;\r\n    \r\n    button {\r\n      flex: 1;\r\n      height: 90rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 32rpx;\r\n      border-radius: 0;\r\n      \r\n      &.cancel-btn {\r\n        background-color: #f5f5f5;\r\n        color: #666;\r\n      }\r\n      \r\n      &.confirm-btn {\r\n        background-color: #4A00E0;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<!-- 专属客服页面结束 --> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/vip-service.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiMA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,iBAAiB;AAAA,QACf,SAAS;AAAA,QACT,cAAc,CAAC,UAAU,SAAS,QAAQ;AAAA,QAC1C,UAAU;AAAA,QACV,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,aAAa;AAAA,MACd;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,QAClC,EAAE,OAAO,QAAQ,OAAO,QAAS;AAAA,QACjC,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,QAClC,EAAE,OAAO,QAAQ,OAAO,QAAQ;AAAA,MACjC;AAAA;AAAA,MAGD,iBAAiB;AAAA,QACf,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,QAChC,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,QAChC,EAAE,OAAO,MAAM,OAAO,OAAO;AAAA,MAC9B;AAAA;AAAA,MAGD,WAAW;AAAA,QACT;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAAA,MACD;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AAAA,MACD;AAAA;AAAA,MAGD,WAAW;AAAA,QACT,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAAA,MAED,WAAW;AAAA,MACX,gBAAgB;AAAA;EAEnB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,iBAAiB,GAAG;AAClB,WAAK,gBAAgB,UAAU,EAAE,OAAO;AAAA,IACzC;AAAA;AAAA,IAGD,sBAAsB,MAAM;AAC1B,aAAO,KAAK,gBAAgB,aAAa,SAAS,IAAI;AAAA,IACvD;AAAA;AAAA,IAGD,kBAAkB,MAAM;AACtB,YAAM,QAAQ,KAAK,gBAAgB,aAAa,QAAQ,IAAI;AAC5D,UAAI,UAAU,IAAI;AAChB,aAAK,gBAAgB,aAAa,KAAK,IAAI;AAAA,aACtC;AACL,aAAK,gBAAgB,aAAa,OAAO,OAAO,CAAC;AAAA,MACnD;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,UAAU;AACpB,WAAK,gBAAgB,WAAW;AAAA,IACjC;AAAA;AAAA,IAGD,sBAAsB,GAAG;AACvB,WAAK,gBAAgB,kBAAkB,EAAE,OAAO;AAAA,IACjD;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,YAAM,QAAQ,KAAK,aAAa,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACtE,UAAI,UAAU,IAAI;AAChB,aAAK,aAAa,KAAK,EAAE,WAAW,CAAC,KAAK,aAAa,KAAK,EAAE;AAAA,MAChE;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,YAAY;AACjB,WAAK,YAAY;AAAA,QACf,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA;AAEV,WAAK,MAAM,eAAe;IAC3B;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,YAAY;AACjB,WAAK,iBAAiB,MAAM;AAC5B,WAAK,YAAY,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AACjD,WAAK,MAAM,eAAe;IAC3B;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,MAAM,eAAe;IAC3B;AAAA;AAAA,IAGD,kBAAkB,GAAG;AACnB,WAAK,UAAU,SAAS,EAAE,OAAO;AAAA,IAClC;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAChB,eAAK,UAAU,SAAS,IAAI,cAAc,CAAC;AAAA,QAC7C;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AAEd,UAAI,CAAC,KAAK,UAAU,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,UAAU,UAAU;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,UAAI,KAAK,WAAW;AAElB,cAAM,QAAQ,KAAK,UAAU,UAAU,UAAQ,KAAK,OAAO,KAAK,cAAc;AAC9E,YAAI,UAAU,IAAI;AAChB,eAAK,UAAU,OAAO,OAAO,GAAG,KAAK,MAAM,KAAK,UAAU,KAAK,SAAS,CAAC,CAAC;AAAA,QAC5E;AAAA,aACK;AAEL,aAAK,UAAU,KAAK,KAAK,IAAG;AAC5B,aAAK,UAAU,KAAK,KAAK,MAAM,KAAK,UAAU,KAAK,SAAS,CAAC,CAAC;AAAA,MAChE;AAGA,WAAK,gBAAe;AAGpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,YAAY,WAAW;AAAA,MACrC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB,OAAO;AACxB,WAAK,iBAAiB,MAAM;AAC5B,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,cAAc;AACZ,YAAM,QAAQ,KAAK,UAAU,UAAU,UAAQ,KAAK,OAAO,KAAK,cAAc;AAC9E,UAAI,UAAU,IAAI;AAChB,aAAK,UAAU,OAAO,OAAO,CAAC;AAAA,MAChC;AAEA,WAAK,MAAM,mBAAmB;AAE9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,MACT,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,eAAe;AAEbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACncA,GAAG,WAAW,eAAe;"}