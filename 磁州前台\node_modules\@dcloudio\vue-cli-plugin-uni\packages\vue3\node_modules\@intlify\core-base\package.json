{"name": "@intlify/core-base", "version": "9.1.7", "description": "@intlify/core-base", "keywords": ["core", "fundamental", "i18n", "internationalization", "intlify"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/core-base#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/core-base.esm-bundler.js", "unpkg": "dist/core-base.global.js", "jsdelivr": "dist/core-base.global.js", "types": "dist/core-base.d.ts", "dependencies": {"@intlify/devtools-if": "9.1.7", "@intlify/message-compiler": "9.1.7", "@intlify/message-resolver": "9.1.7", "@intlify/runtime": "9.1.7", "@intlify/shared": "9.1.7", "@intlify/vue-devtools": "9.1.7"}, "engines": {"node": ">= 10"}, "buildOptions": {"name": "IntlifyCoreBase", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "publishConfig": {"access": "public"}, "sideEffects": false}