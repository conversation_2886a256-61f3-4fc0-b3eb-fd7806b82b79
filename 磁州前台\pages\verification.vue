<template>
  <view class="container">
    <text>正在跳转到核销中心...</text>
  </view>
</template>

<script>
export default {
  onLoad() {
    // 延迟一下再跳转，显示加载提示
    setTimeout(() => {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/verification/index',
        fail: (err) => {
          console.error('跳转到核销中心失败:', err);
          uni.showToast({
            title: '跳转失败，请稍后再试',
            icon: 'none'
          });
        }
      });
    }, 300);
  }
}
</script>

<style>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 16px;
  color: #666;
}
</style> 