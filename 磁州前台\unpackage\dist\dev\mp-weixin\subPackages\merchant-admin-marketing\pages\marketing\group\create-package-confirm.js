"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      packageInfo: {
        name: "",
        category: "",
        groupSize: 2,
        startDate: "",
        endDate: "",
        description: ""
      },
      priceInfo: {
        marketPrice: "0.00",
        regularPrice: "0.00",
        groupPrice: "0.00",
        limitPerUser: 1,
        stock: 100
      },
      packageItems: [],
      paymentInfo: {
        paymentType: "offline",
        // offline: 到店支付, online: 线上支付
        verifyType: "offline",
        // offline: 到店核销, online: 线上核销
        verifyCodeValidDays: 30,
        verifyTimes: 1
      },
      showSuccessModal: false
    };
  },
  onLoad() {
    try {
      const savedInfo = common_vendor.index.getStorageSync("packageInfo");
      if (savedInfo) {
        this.packageInfo = JSON.parse(savedInfo);
      }
      const savedPriceInfo = common_vendor.index.getStorageSync("packagePriceInfo");
      if (savedPriceInfo) {
        this.priceInfo = JSON.parse(savedPriceInfo);
      }
      const savedItems = common_vendor.index.getStorageSync("packageItems");
      if (savedItems) {
        this.packageItems = JSON.parse(savedItems);
      }
      const savedPaymentInfo = common_vendor.index.getStorageSync("packagePaymentInfo");
      if (savedPaymentInfo) {
        this.paymentInfo = JSON.parse(savedPaymentInfo);
      } else {
        common_vendor.index.setStorageSync("packagePaymentInfo", JSON.stringify(this.paymentInfo));
      }
    } catch (e) {
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm.vue:248", "读取本地存储失败:", e);
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    editSection(section) {
      switch (section) {
        case "info":
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info"
          });
          break;
        case "price":
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price"
          });
          break;
        case "items":
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items"
          });
          break;
        case "payment":
          common_vendor.index.showToast({
            title: "支付和核销设置功能开发中",
            icon: "none"
          });
          break;
      }
    },
    calculateTotalValue() {
      let total = 0;
      this.packageItems.forEach((item) => {
        total += parseFloat(item.price) * parseFloat(item.quantity);
      });
      return total.toFixed(2);
    },
    submitPackage() {
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        this.showSuccessModal = true;
        try {
          common_vendor.index.removeStorageSync("packageInfo");
          common_vendor.index.removeStorageSync("packagePriceInfo");
          common_vendor.index.removeStorageSync("packageItems");
          common_vendor.index.removeStorageSync("packagePaymentInfo");
          common_vendor.index.removeStorageSync("packageType");
        } catch (e) {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm.vue:310", "清除本地存储失败:", e);
        }
      }, 1500);
    },
    goToList() {
      common_vendor.index.redirectTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/group/package-management"
      });
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "帮助信息",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.o(($event) => $options.editSection("info")),
    d: common_vendor.t($data.packageInfo.name),
    e: common_vendor.t($data.packageInfo.category),
    f: common_vendor.t($data.packageInfo.groupSize),
    g: common_vendor.t($data.packageInfo.startDate),
    h: common_vendor.t($data.packageInfo.endDate),
    i: $data.packageInfo.description
  }, $data.packageInfo.description ? {
    j: common_vendor.t($data.packageInfo.description)
  } : {}, {
    k: common_vendor.o(($event) => $options.editSection("price")),
    l: common_vendor.t($data.priceInfo.marketPrice),
    m: common_vendor.t($data.priceInfo.regularPrice),
    n: common_vendor.t($data.priceInfo.groupPrice),
    o: common_vendor.t($data.priceInfo.limitPerUser > 0 ? $data.priceInfo.limitPerUser + "件" : "不限购"),
    p: common_vendor.t($data.priceInfo.stock),
    q: common_vendor.o(($event) => $options.editSection("items")),
    r: common_vendor.f($data.packageItems, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(index + 1),
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.quantity),
        d: common_vendor.t(item.unit),
        e: common_vendor.t(item.price),
        f: item.description
      }, item.description ? {
        g: common_vendor.t(item.description)
      } : {}, {
        h: index
      });
    }),
    s: common_vendor.t($options.calculateTotalValue()),
    t: common_vendor.o(($event) => $options.editSection("payment")),
    v: common_vendor.t($data.paymentInfo.paymentType === "online" ? "线上支付" : "到店支付"),
    w: common_vendor.t($data.paymentInfo.verifyType === "online" ? "线上核销" : "到店核销"),
    x: $data.paymentInfo.verifyType === "offline"
  }, $data.paymentInfo.verifyType === "offline" ? {
    y: common_vendor.t($data.paymentInfo.verifyCodeValidDays)
  } : {}, {
    z: $data.paymentInfo.verifyType === "offline"
  }, $data.paymentInfo.verifyType === "offline" ? {
    A: common_vendor.t($data.paymentInfo.verifyTimes)
  } : {}, {
    B: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    C: common_vendor.o((...args) => $options.submitPackage && $options.submitPackage(...args)),
    D: $data.showSuccessModal
  }, $data.showSuccessModal ? {
    E: common_vendor.o((...args) => $options.goToList && $options.goToList(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm.js.map
