/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.favorites-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}
.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}
.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 24px;
  height: 24px;
}
.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}
.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.edit-text {
  color: #FFFFFF;
  font-size: 14px;
}

/* 内容区域 */
.scrollable-content {
  flex: 1;
  margin-top: calc(44px + var(--status-bar-height) + 5px);
  padding: 0;
  margin-bottom: 60px;
}

/* 卡片容器 */
.card-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 5px 0;
}

/* 卡片项目 */
.card-item {
  width: 90%;
  display: flex;
  position: relative;
}

/* 选择区域 */
.select-area {
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

/* 复选框 */
.checkbox {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #DDDDDD;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
}
.checkbox.checked {
  border-color: #9C27B0;
  background-color: #9C27B0;
}
.checkbox-inner {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #FFFFFF;
}

/* 卡片内容 */
.card-content {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-left: 0;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
}
.user-details {
  flex: 1;
}
.user-name {
  font-size: 15px;
  font-weight: 500;
  color: #333333;
}
.user-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
}
.publish-time, .trip-type {
  font-size: 12px;
  color: #999999;
}

/* 路线信息 */
.route-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  border-top: 1px solid #F5F5F5;
  padding-top: 12px;
}
.route-points {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.start-point, .end-point {
  display: flex;
  align-items: center;
  gap: 10px;
}
.point-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.start {
  background-color: #1677FF;
}
.end {
  background-color: #FF5722;
}
.route-line {
  width: 2px;
  height: 20px;
  background-color: #DDDDDD;
  margin-left: 5px;
}
.point-text {
  font-size: 16px;
  color: #333333;
}
.trip-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}
.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}
.info-icon {
  width: 24px;
  height: 24px;
}
.info-text {
  font-size: 14px;
  color: #666666;
}
.price {
  color: #FF5722;
  font-weight: 500;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #F5F5F5;
}
.meta-info {
  display: flex;
  gap: 16px;
}
.views, .messages {
  display: flex;
  align-items: center;
  gap: 4px;
}
.meta-icon {
  width: 24px;
  height: 24px;
}
.meta-text {
  font-size: 12px;
  color: #999999;
}
.action-area {
  display: flex;
  align-items: center;
  gap: 10px;
}
.cancel-btn {
  font-size: 13px;
  font-weight: 500;
  padding: 4px 14px;
  border-radius: 16px;
  background-color: #FF5722;
  color: #FFFFFF;
  text-align: center;
  box-shadow: 0 2px 6px rgba(255, 87, 34, 0.25);
  margin-right: 6px;
}
.cancel-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  filter: brightness(0) invert(1);
}
.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.status-active {
  background-color: rgba(22, 119, 255, 0.1);
  color: #1677FF;
}
.status-pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}
.status-expired {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}
.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}
.empty-text {
  font-size: 16px;
  color: #999999;
  margin-bottom: 20px;
}
.browse-button {
  background-color: #9C27B0;
  color: #FFFFFF;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
}

/* 加载状态 */
.loading-state {
  padding: 16px 0;
  text-align: center;
}
.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 列表底部 */
.list-bottom {
  padding: 16px 0;
  text-align: center;
}
.bottom-text {
  font-size: 14px;
  color: #999999;
}

/* 批量操作栏 */
.batch-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 60px;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.select-all {
  display: flex;
  align-items: center;
  gap: 8px;
}
.select-text {
  font-size: 14px;
  color: #333333;
}
.batch-button {
  flex: 1;
  height: 36px;
  line-height: 36px;
  border-radius: 18px;
  background-color: #9C27B0;
  color: #FFFFFF;
  font-size: 14px;
  margin-left: 16px;
}
.batch-button[disabled] {
  background-color: #E0E0E0;
  color: #999999;
}

/* 悬浮编辑按钮 */
.float-edit-btn {
  position: fixed;
  right: 20px;
  bottom: 80px;
  background-color: #1677FF;
  color: #FFFFFF;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  font-size: 14px;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}