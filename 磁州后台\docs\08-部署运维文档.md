# 磁州生活网后台管理系统 - 部署运维文档

## 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025-01-04
- **文档类型**: 部署运维文档
- **目标读者**: 运维工程师、DevOps工程师

## 1. 环境规划

### 1.1 环境分类
```
开发环境 (Development)
├── 用途: 开发人员日常开发测试
├── 配置: 单机部署，资源配置较低
└── 数据: 模拟数据，可随意重置

测试环境 (Testing)
├── 用途: 功能测试、集成测试
├── 配置: 接近生产环境配置
└── 数据: 测试数据，定期重置

预发布环境 (Staging)
├── 用途: 生产发布前最后验证
├── 配置: 与生产环境完全一致
└── 数据: 生产数据副本

生产环境 (Production)
├── 用途: 正式对外提供服务
├── 配置: 高可用、高性能配置
└── 数据: 真实业务数据
```

### 1.2 服务器规划

#### 1.2.1 生产环境服务器配置
```yaml
# 应用服务器
app-servers:
  - name: app-01
    cpu: 8 cores
    memory: 16GB
    disk: 200GB SSD
    role: [gateway, user-service, content-service]
  
  - name: app-02
    cpu: 8 cores
    memory: 16GB
    disk: 200GB SSD
    role: [merchant-service, carpool-service, cashback-service]

# 数据库服务器
db-servers:
  - name: db-master
    cpu: 16 cores
    memory: 32GB
    disk: 1TB SSD
    role: MySQL Master
  
  - name: db-slave-01
    cpu: 16 cores
    memory: 32GB
    disk: 1TB SSD
    role: MySQL Slave

# 缓存服务器
cache-servers:
  - name: redis-01
    cpu: 4 cores
    memory: 16GB
    disk: 100GB SSD
    role: Redis Master
  
  - name: redis-02
    cpu: 4 cores
    memory: 16GB
    disk: 100GB SSD
    role: Redis Slave

# 监控服务器
monitor-server:
  - name: monitor-01
    cpu: 8 cores
    memory: 16GB
    disk: 500GB SSD
    role: [Prometheus, Grafana, ELK]
```

## 2. Docker部署

### 2.1 Dockerfile规范

#### 2.1.1 Java应用Dockerfile
```dockerfile
# 多阶段构建
FROM maven:3.8.6-openjdk-17-slim AS builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests

# 运行时镜像
FROM openjdk:17-jre-slim

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 安装必要工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY --from=builder /app/target/*.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs && chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["java", "-jar", \
  "-Xms512m", "-Xmx1024m", \
  "-Dspring.profiles.active=${SPRING_PROFILES_ACTIVE:prod}", \
  "-Djava.security.egd=file:/dev/./urandom", \
  "app.jar"]
```

#### 2.1.2 前端应用Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源码并构建
COPY . .
RUN npm run build

# 运行时镜像
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
```

### 2.2 Docker Compose配置

#### 2.2.1 开发环境配置
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: cizhou-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: cizhou_admin
      MYSQL_USER: cizhou
      MYSQL_PASSWORD: cizhou123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/conf:/etc/mysql/conf.d
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - cizhou-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: cizhou-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf
    networks:
      - cizhou-network

  # Nacos注册中心
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: cizhou-nacos
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: root123456
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    volumes:
      - nacos_data:/home/<USER>/data
    networks:
      - cizhou-network

  # API网关
  gateway:
    build:
      context: ./cizhou-admin-gateway
      dockerfile: Dockerfile
    container_name: cizhou-gateway
    environment:
      SPRING_PROFILES_ACTIVE: dev
      NACOS_SERVER_ADDR: nacos:8848
    ports:
      - "8080:8080"
    depends_on:
      - nacos
      - redis
    networks:
      - cizhou-network

  # 用户服务
  user-service:
    build:
      context: ./cizhou-admin-modules/user-service
      dockerfile: Dockerfile
    container_name: cizhou-user-service
    environment:
      SPRING_PROFILES_ACTIVE: dev
      NACOS_SERVER_ADDR: nacos:8848
      DB_HOST: mysql
      REDIS_HOST: redis
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - cizhou-network

volumes:
  mysql_data:
  redis_data:
  nacos_data:

networks:
  cizhou-network:
    driver: bridge
```

#### 2.2.2 生产环境配置
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  gateway:
    image: cizhou/admin-gateway:${VERSION}
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    environment:
      SPRING_PROFILES_ACTIVE: prod
      NACOS_SERVER_ADDR: nacos:8848
      JVM_OPTS: "-Xms512m -Xmx1024m"
    ports:
      - "8080:8080"
    networks:
      - cizhou-network
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  user-service:
    image: cizhou/admin-user-service:${VERSION}
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
    environment:
      SPRING_PROFILES_ACTIVE: prod
      NACOS_SERVER_ADDR: nacos:8848
      DB_HOST: ${DB_HOST}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: ${REDIS_HOST}
    networks:
      - cizhou-network

networks:
  cizhou-network:
    external: true
```

## 3. Kubernetes部署

### 3.1 命名空间配置
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: cizhou-admin
  labels:
    name: cizhou-admin
    environment: production
```

### 3.2 ConfigMap配置
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cizhou-admin-config
  namespace: cizhou-admin
data:
  application.yml: |
    spring:
      profiles:
        active: prod
      cloud:
        nacos:
          discovery:
            server-addr: nacos-service:8848
          config:
            server-addr: nacos-service:8848
    
    logging:
      level:
        com.cizhou: info
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

---
apiVersion: v1
kind: Secret
metadata:
  name: cizhou-admin-secret
  namespace: cizhou-admin
type: Opaque
data:
  db-username: Y2l6aG91  # base64 encoded
  db-password: Y2l6aG91MTIz  # base64 encoded
  redis-password: ""
```

### 3.3 Deployment配置
```yaml
# k8s/gateway-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cizhou-admin-gateway
  namespace: cizhou-admin
  labels:
    app: cizhou-admin-gateway
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cizhou-admin-gateway
  template:
    metadata:
      labels:
        app: cizhou-admin-gateway
    spec:
      containers:
      - name: gateway
        image: cizhou/admin-gateway:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: NACOS_SERVER_ADDR
          value: "nacos-service:8848"
        - name: JVM_OPTS
          value: "-Xms512m -Xmx1024m"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        volumeMounts:
        - name: config
          mountPath: /app/config
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: config
        configMap:
          name: cizhou-admin-config
      - name: logs
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: cizhou-admin-gateway-service
  namespace: cizhou-admin
spec:
  selector:
    app: cizhou-admin-gateway
  ports:
  - protocol: TCP
    port: 8080
    targetPort: 8080
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cizhou-admin-gateway-ingress
  namespace: cizhou-admin
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - admin.cizhou.com
    secretName: cizhou-admin-tls
  rules:
  - host: admin.cizhou.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cizhou-admin-gateway-service
            port:
              number: 8080
```

### 3.4 HPA自动扩缩容
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cizhou-admin-gateway-hpa
  namespace: cizhou-admin
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cizhou-admin-gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
```

## 4. 监控配置

### 4.1 Prometheus配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Spring Boot应用监控
  - job_name: 'cizhou-admin'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['gateway:8080', 'user-service:8080']
    scrape_interval: 30s

  # MySQL监控
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # 系统监控
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
```

### 4.2 告警规则
```yaml
# monitoring/rules/application.yml
groups:
- name: application
  rules:
  # 应用存活检查
  - alert: ApplicationDown
    expr: up{job="cizhou-admin"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "应用服务不可用"
      description: "{{ $labels.instance }} 应用服务已停止响应超过1分钟"

  # 高错误率告警
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "应用错误率过高"
      description: "{{ $labels.instance }} 5xx错误率超过10%"

  # 高响应时间告警
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "应用响应时间过长"
      description: "{{ $labels.instance }} 95%响应时间超过2秒"

  # 内存使用率告警
  - alert: HighMemoryUsage
    expr: (process_resident_memory_bytes / 1024 / 1024) > 1024
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "应用内存使用率过高"
      description: "{{ $labels.instance }} 内存使用超过1GB"
```

### 4.3 Grafana仪表盘
```json
{
  "dashboard": {
    "title": "磁州生活网后台管理系统监控",
    "panels": [
      {
        "title": "QPS",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "错误率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      }
    ]
  }
}
```

## 5. 日志管理

### 5.1 ELK Stack配置
```yaml
# logging/elasticsearch.yml
version: '3.8'
services:
  elasticsearch:
    image: elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data

  logstash:
    image: logstash:8.8.0
    ports:
      - "5044:5044"
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
      - ./logstash/config:/usr/share/logstash/config
    depends_on:
      - elasticsearch

  kibana:
    image: kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch

volumes:
  es_data:
```

### 5.2 Logstash配置
```ruby
# logstash/pipeline/logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "cizhou-admin" {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} \[%{DATA:thread}\] %{LOGLEVEL:level} \[%{DATA:traceId}\] %{DATA:logger} - %{GREEDYDATA:msg}" 
      }
    }
    
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSS" ]
    }
    
    if [level] == "ERROR" {
      mutate {
        add_tag => [ "error" ]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "cizhou-admin-%{+YYYY.MM.dd}"
  }
  
  if "error" in [tags] {
    email {
      to => ["<EMAIL>"]
      subject => "应用错误告警"
      body => "错误信息: %{msg}"
    }
  }
}
```

## 6. 备份策略

### 6.1 数据库备份
```bash
#!/bin/bash
# scripts/backup-mysql.sh

# 配置变量
DB_HOST="localhost"
DB_USER="backup_user"
DB_PASSWORD="backup_password"
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份所有数据库
databases=("cizhou_admin_user" "cizhou_admin_content" "cizhou_admin_merchant")

for db in "${databases[@]}"; do
    echo "备份数据库: $db"
    mysqldump -h$DB_HOST -u$DB_USER -p$DB_PASSWORD \
        --single-transaction \
        --routines \
        --triggers \
        $db > $BACKUP_DIR/${db}_${DATE}.sql
    
    # 压缩备份文件
    gzip $BACKUP_DIR/${db}_${DATE}.sql
    
    echo "备份完成: ${db}_${DATE}.sql.gz"
done

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "数据库备份完成"
```

### 6.2 Redis备份
```bash
#!/bin/bash
# scripts/backup-redis.sh

REDIS_HOST="localhost"
REDIS_PORT="6379"
BACKUP_DIR="/backup/redis"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 执行BGSAVE命令
redis-cli -h $REDIS_HOST -p $REDIS_PORT BGSAVE

# 等待备份完成
while [ $(redis-cli -h $REDIS_HOST -p $REDIS_PORT LASTSAVE) -eq $(redis-cli -h $REDIS_HOST -p $REDIS_PORT LASTSAVE) ]; do
    sleep 1
done

# 复制RDB文件
cp /var/lib/redis/dump.rdb $BACKUP_DIR/dump_${DATE}.rdb

# 压缩备份文件
gzip $BACKUP_DIR/dump_${DATE}.rdb

echo "Redis备份完成: dump_${DATE}.rdb.gz"
```

## 7. 部署脚本

### 7.1 自动化部署脚本
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

# 配置变量
PROJECT_NAME="cizhou-admin"
VERSION=${1:-latest}
ENVIRONMENT=${2:-prod}
REGISTRY="registry.cizhou.com"

echo "开始部署 $PROJECT_NAME:$VERSION 到 $ENVIRONMENT 环境"

# 1. 构建镜像
echo "构建Docker镜像..."
docker build -t $REGISTRY/$PROJECT_NAME-gateway:$VERSION ./cizhou-admin-gateway
docker build -t $REGISTRY/$PROJECT_NAME-user-service:$VERSION ./cizhou-admin-modules/user-service

# 2. 推送镜像
echo "推送镜像到仓库..."
docker push $REGISTRY/$PROJECT_NAME-gateway:$VERSION
docker push $REGISTRY/$PROJECT_NAME-user-service:$VERSION

# 3. 更新Kubernetes部署
echo "更新Kubernetes部署..."
kubectl set image deployment/cizhou-admin-gateway \
    gateway=$REGISTRY/$PROJECT_NAME-gateway:$VERSION \
    -n cizhou-admin

kubectl set image deployment/cizhou-admin-user-service \
    user-service=$REGISTRY/$PROJECT_NAME-user-service:$VERSION \
    -n cizhou-admin

# 4. 等待部署完成
echo "等待部署完成..."
kubectl rollout status deployment/cizhou-admin-gateway -n cizhou-admin
kubectl rollout status deployment/cizhou-admin-user-service -n cizhou-admin

# 5. 健康检查
echo "执行健康检查..."
sleep 30
curl -f http://admin.cizhou.com/actuator/health || exit 1

echo "部署完成！"
```

---

**文档状态**: 部署运维文档完成，待运维团队评审
**下一步**: 项目启动和团队协作
