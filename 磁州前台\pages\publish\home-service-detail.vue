<template>
  <view class="service-detail-container">
    <!-- 添加自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">到家服务详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <!-- 隐藏的Canvas用于绘制海报 -->
    <canvas canvas-id="posterCanvas" class="poster-canvas" style="width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;"></canvas>
    
    <!-- 悬浮海报按钮 -->
    <view class="float-poster-btn" @click="generateShareImage">
      <image src="/static/images/tabbar/海报.png" class="poster-icon"></image>
      <text class="poster-text">海报</text>
    </view>
    
    <!-- 举报按钮 -->
    
    <view class="service-detail-wrapper">
      <!-- 服务基本信息卡片 -->
      <view class="content-card service-info-card">
        <view class="service-header">
          <view class="service-title-row">
            <text class="service-title">{{serviceData.title}}</text>
            <text class="service-price">{{serviceData.price}}</text>
          </view>
          <view class="service-meta">
            <view class="service-tag-group">
              <view class="service-tag" v-for="(tag, index) in serviceData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="service-publish-time">发布于 {{formatTime(serviceData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 服务图片轮播 -->
        <swiper class="service-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
          <swiper-item v-for="(image, index) in serviceData.images" :key="index">
            <image :src="image" mode="aspectFill" class="service-image"></image>
          </swiper-item>
        </swiper>
        
        <!-- 基本信息 -->
        <view class="service-basic-info">
          <view class="info-item">
            <text class="info-label">服务类型</text>
            <text class="info-value">{{serviceData.type}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">服务范围</text>
            <text class="info-value">{{serviceData.area}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">服务时间</text>
            <text class="info-value">{{serviceData.time}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">服务方式</text>
            <text class="info-value">{{serviceData.method}}</text>
          </view>
        </view>
      </view>
      
      <!-- 服务内容 -->
      <view class="content-card service-content-card">
        <view class="section-title">服务内容</view>
        <view class="content-list">
          <view class="content-item" v-for="(item, index) in serviceData.contents" :key="index">
            <text class="content-icon iconfont" :class="item.icon"></text>
            <text class="content-text">{{item.name}}</text>
          </view>
        </view>
      </view>
      
      <!-- 服务说明 -->
      <view class="content-card service-desc-card">
        <view class="section-title">服务说明</view>
        <view class="desc-content">
          <rich-text :nodes="serviceData.description"></rich-text>
        </view>
      </view>
      
      <!-- 服务保障 -->
      <view class="content-card service-guarantee-card">
        <view class="section-title">服务保障</view>
        <view class="guarantee-list">
          <view class="guarantee-item" v-for="(item, index) in serviceData.guarantees" :key="index">
            <text class="guarantee-icon iconfont" :class="item.icon"></text>
            <view class="guarantee-info">
              <text class="guarantee-title">{{item.title}}</text>
              <text class="guarantee-desc">{{item.desc}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 服务商信息 -->
      <view class="content-card provider-card">
        <view class="section-title">服务商信息</view>
        <view class="provider-header">
          <view class="provider-avatar">
            <image :src="serviceData.provider.avatar" mode="aspectFill"></image>
          </view>
          <view class="provider-info">
            <text class="provider-name">{{serviceData.provider.name}}</text>
            <view class="provider-meta">
              <text class="provider-type">{{serviceData.provider.type}}</text>
              <text class="provider-rating">好评率 {{serviceData.provider.rating}}%</text>
            </view>
          </view>
          <view class="provider-auth" v-if="serviceData.provider.isVerified">
            <text class="iconfont icon-verified"></text>
            <text class="auth-text">已认证</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="section-title">联系方式</view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{serviceData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{serviceData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 举报卡片 -->
      <view class="content-card report-card" @click="showReportOptions">
        <view class="report-content">
          <image src="/static/images/tabbar/举报.png" class="report-icon"></image>
          <view class="report-text-container">
            <view class="report-title">如遇无效、虚假信息，请立即举报</view>
            <view class="report-subtitle">平台将尽快核实处理</view>
          </view>
          <view class="report-arrow">
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
      
      <!-- 相关服务推荐 -->
      <view class="content-card related-services-card">
        <view class="section-title">相关服务推荐</view>
        <view class="related-services-content">
          <!-- 简洁的服务列表 -->
          <view class="related-services-list">
            <view class="related-service-item" 
                 v-for="(service, index) in relatedServices.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToServiceDetail(service.id, service.type)">
              <view class="service-item-content">
                <view class="service-item-left">
                  <image class="provider-logo" :src="service.providerLogo" mode="aspectFill"></image>
                </view>
                <view class="service-item-middle">
                  <text class="service-item-title">{{service.title}}</text>
                  <view class="service-item-provider">{{service.providerName}}</view>
                  <view class="service-item-tags">
                    <text class="service-item-tag" v-for="(tag, tagIndex) in service.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="service-item-tag-more" v-if="service.tags.length > 2">+{{service.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="service-item-right">
                  <text class="service-item-price">{{service.price}}</text>
                </view>
              </view>
            </view>
            
            <!-- 暂无数据提示 -->
            <view class="empty-related-services" v-if="relatedServices.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关服务</text>
            </view>
          </view>
          
          <!-- 查看更多按钮 -->
          <view class="view-more-btn" v-if="relatedServices.length > 0" @click.stop="navigateToServiceList">
            <text class="view-more-text">查看更多服务信息</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ReportCard from '@/components/ReportCard.vue';

// 状态栏高度
const statusBarHeight = ref(20);

// 获取状态栏高度
onMounted(() => {
  try {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
  } catch (e) {
    console.error('获取状态栏高度失败', e);
  }
});

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  });
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);
const posterImagePath = ref('');
const showPosterFlag = ref(false);

// 相关服务列表
const relatedServices = ref([]);

// 加载相关服务信息
const loadRelatedServices = () => {
  // 获取当前服务类型的代码
  const currentType = serviceData.value.type;
  const currentTypeCode = getSubTypeCode(currentType);
  
  // 根据当前服务类型加载相关服务
  setTimeout(() => {
    // 根据服务类型设置不同的推荐服务
    switch(currentTypeCode) {
      case 'locksmith': // 开锁换锁
        relatedServices.value = [
          {
            id: 'locksmith001',
            title: '24小时紧急开锁',
            price: '60元起',
            providerName: '安全开锁中心',
            providerLogo: '/static/images/tabbar/开锁.png',
            tags: ['24小时服务', '快速上门', '技术精湛'],
            type: 'locksmith'
          },
          {
            id: 'locksmith002',
            title: '智能锁安装服务',
            price: '150元起',
            providerName: '智能家居安装',
            providerLogo: '/static/images/tabbar/智能锁.png',
            tags: ['指纹锁', '密码锁', '刷卡锁'],
            type: 'locksmith'
          },
          {
            id: 'locksmith003',
            title: '保险柜开锁服务',
            price: '200元起',
            providerName: '专业开锁技师',
            providerLogo: '/static/images/tabbar/保险柜.png',
            tags: ['保险柜', '保险箱', '密码箱'],
            type: 'locksmith'
          }
        ];
        break;
      case 'installation': // 上门安装
        relatedServices.value = [
          {
            id: 'installation001',
            title: '家具组装服务',
            price: '80元起',
            providerName: '家具安装中心',
            providerLogo: '/static/images/tabbar/家具.png',
            tags: ['专业工具', '经验丰富', '安装保障'],
            type: 'installation'
          },
          {
            id: 'installation002',
            title: '电器安装服务',
            price: '100元起',
            providerName: '电器安装专家',
            providerLogo: '/static/images/tabbar/电器.png',
            tags: ['空调安装', '电视安装', '洗衣机安装'],
            type: 'installation'
          },
          {
            id: 'installation003',
            title: '灯具安装服务',
            price: '60元起',
            providerName: '灯具安装师傅',
            providerLogo: '/static/images/tabbar/灯具.png',
            tags: ['吊灯安装', '射灯安装', '壁灯安装'],
            type: 'installation'
          }
        ];
        break;
      case 'home_cleaning': // 家政保洁
      default:
    relatedServices.value = [
      {
        id: 'service001',
        title: '专业擦玻璃服务',
        price: '40元/平方',
        providerName: '阿姨到家家政',
        providerLogo: '/static/images/tabbar/清洁.png',
            tags: ['高空作业', '安全保障', '专业设备'],
            type: 'home_cleaning'
      },
      {
        id: 'service002',
        title: '空调深度清洗',
        price: '100元/台起',
        providerName: '磁县家电清洗中心',
        providerLogo: '/static/images/tabbar/空调.png',
            tags: ['上门服务', '专业工具', '彻底除菌'],
            type: 'home_cleaning'
      },
      {
        id: 'service003',
        title: '深度除螨服务',
        price: '288元起',
        providerName: '优家净家政',
        providerLogo: '/static/images/tabbar/除螨.png',
            tags: ['沙发床垫', '紫外除螨', '深度杀菌'],
            type: 'home_cleaning'
      }
    ];
        break;
    }
  }, 500);
};

// 跳转到服务详情页
const navigateToServiceDetail = (id, type) => {
  // 避免重复跳转当前页面
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  if (id === options.id) {
    return;
  }
  
  // 如果没有传入服务类型，使用当前服务类型
  const serviceType = type || getSubTypeCode(serviceData.value.type);
  
  uni.navigateTo({
    url: `/pages/publish/home-service-detail?id=${id}&type=${serviceType}`
  });
};

// 跳转到服务列表页
const navigateToServiceList = (e) => {
  if (e) e.stopPropagation();
  // 获取当前服务类型作为筛选条件
  const serviceCategory = serviceData.value.type || '';
  const serviceTypeCode = getSubTypeCode(serviceCategory);
  
  // 跳转到到家服务列表页，并传递正确的子分类参数
  uni.navigateTo({
    url: `/pages/service/home-service-list?subType=${serviceTypeCode}&subName=${encodeURIComponent(serviceCategory)}` 
  });
};

// 根据分类名称获取相应的类型编码
const getSubTypeCode = (categoryName) => {
  const categoryMap = {
    '家政保洁': 'home_cleaning',
    '家政服务': 'home_cleaning',
    '维修改造': 'repair',
    '上门安装': 'installation',
    '开锁换锁': 'locksmith',
    '搬家拉货': 'moving',
    '上门美容': 'beauty',
    '上门家教': 'tutor',
    '宠物服务': 'pet_service',
    '上门疏通': 'plumbing'
  };
  
  return categoryMap[categoryName] || 'home_cleaning';
};

const serviceData = ref({
  id: 'service12345',
  title: '专业家政保洁服务',
  price: '50-120元/小时起',
  tags: ['专业保洁', '上门服务', '可预约', '环保用品', '全城服务'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  images: [
    '/static/images/service1.jpg',
    '/static/images/service2.jpg',
    '/static/images/service3.jpg',
    '/static/images/service4.jpg',
    '/static/images/service5.jpg'
  ],
  type: '家政保洁',
  area: '磁县城区及周边10公里',
  time: '8:00-20:00（节假日不休）',
  method: '上门服务，提前预约',
  contents: [
    { name: '日常保洁（擦拭、除尘、吸尘、拖地等）', icon: 'icon-clean' },
    { name: '深度保洁（角落清洁、污渍处理等）', icon: 'icon-deep-clean' },
    { name: '开荒保洁（新房入住前全面清洁）', icon: 'icon-new-clean' },
    { name: '家电清洗（空调、冰箱、洗衣机等）', icon: 'icon-appliance' },
    { name: '玻璃清洗（内外玻璃、高层玻璃）', icon: 'icon-window' },
    { name: '地板打蜡（木地板、瓷砖等）', icon: 'icon-floor' },
    { name: '沙发清洗（布艺、真皮沙发）', icon: 'icon-sofa' },
    { name: '厨房深度清洁（油烟机、灶台等）', icon: 'icon-kitchen' }
  ],
  description: '<p>磁县家政服务中心提供专业的家政保洁服务，服务范围包括但不限于：</p><ul><li>日常保洁：室内清洁、地面清洁、家具清洁、卫生间清洁等</li><li>深度保洁：死角清洁、顽固污渍处理、除菌消毒等</li><li>开荒保洁：新房入住前的全面清洁，包括装修后遗留物清理</li><li>家电清洗：专业拆卸清洗空调、冰箱、洗衣机、热水器等</li><li>玻璃清洗：内外玻璃、门窗、高层落地窗等</li><li>地板打蜡：各类地板专业打蜡、抛光、养护</li></ul><p>我们的服务特点：</p><ul><li>专业团队：全部员工经过专业培训，持证上岗</li><li>专业设备：使用先进清洁设备，提高清洁效率和质量</li><li>环保材料：使用环保清洁用品，安全健康无污染</li><li>灵活预约：支持线上预约，可提前1-7天预约服务</li><li>售后保障：服务不满意可重做，损坏物品照价赔偿</li></ul>',
  guarantees: [
    {
      icon: 'icon-quality',
      title: '品质保障',
      desc: '专业保洁人员，持证上岗，统一着装，规范操作'
    },
    {
      icon: 'icon-safe',
      title: '安全保障',
      desc: '员工背景调查，使用环保清洁用品，损坏赔付'
    },
    {
      icon: 'icon-time',
      title: '准时服务',
      desc: '准时上门，不迟到，如有延误提前通知'
    },
    {
      icon: 'icon-satisfaction',
      title: '满意保障',
      desc: '服务不满意可重做，服务质量有问题可投诉退款'
    },
    {
      icon: 'icon-privacy',
      title: '隐私保障',
      desc: '保护客户隐私，不拍摄客户家庭环境'
    }
  ],
  provider: {
    name: '磁县家政服务中心',
    avatar: '/static/images/avatar.png',
    type: '专业机构（5年经验）',
    rating: 98,
    isVerified: true
  },
  contact: {
    name: '王经理（服务总监）',
    phone: '13912345678'
  }
});

// 收藏方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

// 分享方法
const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

// 拨打电话方法
const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: serviceData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  // 检查是否已登录
  const userInfo = uni.getStorageSync('userInfo');
  if (!userInfo) {
    uni.showModal({
      title: '提示',
      content: '发送私信需要先登录，是否前往登录？',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login?redirect=' + encodeURIComponent(`/pages/publish/home-service-detail?id=${serviceData.value.id}`)
          });
        }
      }
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/chat?targetId=${serviceData.value.provider.id}&targetName=${serviceData.value.provider.name}&targetAvatar=${encodeURIComponent(serviceData.value.provider.avatar)}`
  });
};

// 生成海报的方法
const generateShareImage = () => {
  uni.showLoading({
    title: '正在生成海报...',
    mask: true
  });
  
  // 创建海报数据对象
  const posterData = {
    title: serviceData.value.title,
    price: serviceData.value.price,
    type: serviceData.value.type,
    address: serviceData.value.area,
    phone: serviceData.value.contact ? serviceData.value.contact.phone : '',
    description: serviceData.value.description ? serviceData.value.description.substring(0, 60) + '...' : '',
    qrcode: '/static/images/tabbar/客服微信.png',
    logo: '/static/images/tabbar/家政服务.png',
    bgImage: serviceData.value.cover || '/static/images/banner/banner-1.png'
  };
  
  // #ifdef H5
  // H5环境不支持canvas绘制图片保存，提示用户
  setTimeout(() => {
    uni.hideLoading();
    uni.showModal({
      title: '提示',
      content: 'H5环境暂不支持保存海报，请使用App或小程序',
      showCancel: false
    });
  }, 1000);
  return;
  // #endif
  
  // 绘制海报
  const ctx = uni.createCanvasContext('posterCanvas');
  
  // 绘制背景
  ctx.save();
  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
  // 添加半透明蒙层
  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');
  ctx.fillRect(0, 0, 600, 900);
  ctx.restore();
  
  // 绘制白色卡片背景
  ctx.save();
  ctx.setFillStyle('#ffffff');
  ctx.fillRect(30, 280, 540, 550);
  ctx.restore();
  
  // 绘制Logo
  ctx.save();
  ctx.beginPath();
  ctx.arc(300, 200, 80, 0, 2 * Math.PI);
  ctx.setFillStyle('#ffffff');
  ctx.fill();
  // 在圆形内绘制Logo
  ctx.clip();
  ctx.drawImage(posterData.logo, 220, 120, 160, 160);
  ctx.restore();
  
  // 绘制服务标题
  ctx.setFillStyle('#333333');
  ctx.setFontSize(32);
  ctx.setTextAlign('center');
  ctx.fillText(posterData.title, 300, 350);
  
  // 绘制价格
  ctx.setFillStyle('#FF6B6B');
  ctx.setFontSize(28);
  ctx.fillText(posterData.price, 300, 400);
  
  // 分割线
  ctx.beginPath();
  ctx.setStrokeStyle('#eeeeee');
  ctx.setLineWidth(2);
  ctx.moveTo(100, 430);
  ctx.lineTo(500, 430);
  ctx.stroke();
  
  // 绘制服务类型
  ctx.setFillStyle('#666666');
  ctx.setFontSize(24);
  ctx.setTextAlign('left');
  ctx.fillText('服务类型: ' + posterData.type, 80, 480);
  
  // 绘制服务区域
  ctx.fillText('服务区域: ' + posterData.address, 80, 520);
  
  // A wrap text function
  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {
    if (text.length === 0) return;
    
    const words = text.split('');
    let line = '';
    let testLine = '';
    let lineCount = 0;
    
    for (let n = 0; n < words.length; n++) {
      testLine += words[n];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > maxWidth && n > 0) {
        ctx.fillText(line, x, y + (lineCount * lineHeight));
        line = words[n];
        testLine = words[n];
        lineCount++;
        
        if (lineCount >= 3) {
          line += '...';
          ctx.fillText(line, x, y + (lineCount * lineHeight));
          break;
        }
      } else {
        line = testLine;
      }
    }
    
    if (lineCount < 3) {
      ctx.fillText(line, x, y + (lineCount * lineHeight));
    }
  };
  
  // 绘制简介
  ctx.setFillStyle('#666666');
  wrapText(ctx, posterData.description, 80, 560, 440, 35);
  
  // 绘制电话
  if (posterData.phone) {
    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);
  }
  
  // 绘制小程序码
  ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);
  
  // 提示文字
  ctx.setFillStyle('#999999');
  ctx.setFontSize(20);
  ctx.setTextAlign('center');
  ctx.fillText('长按识别二维码查看详情', 300, 880);
  
  // 应用平台Logo
  ctx.setFillStyle('#333333');
  ctx.setFontSize(24);
  ctx.fillText('磁县同城 - 家政服务', 300, 840);
  
  // 绘制完成，输出图片
  ctx.draw(false, () => {
    setTimeout(() => {
      // 延迟确保canvas已完成渲染
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: (res) => {
          uni.hideLoading();
          showPosterModal(res.tempFilePath);
        },
        fail: (err) => {
          console.error('生成海报失败', err);
          uni.hideLoading();
          uni.showToast({
            title: '生成海报失败',
            icon: 'none'
          });
        }
      });
    }, 800);
  });
};

// 显示海报预览和保存选项
const showPosterModal = (posterPath) => {
  posterImagePath.value = posterPath;
  showPosterFlag.value = true;
  
  uni.showModal({
    title: '海报已生成',
    content: '海报已生成，是否保存到相册？',
    confirmText: '保存',
    success: (res) => {
      if (res.confirm) {
        savePosterToAlbum(posterPath);
      } else {
        // 预览图片
        uni.previewImage({
          urls: [posterPath],
          current: posterPath
        });
      }
    }
  });
};

// 保存海报到相册
const savePosterToAlbum = (posterPath) => {
  uni.showLoading({
    title: '正在保存...'
  });
  
  uni.saveImageToPhotosAlbum({
    filePath: posterPath,
    success: () => {
      uni.hideLoading();
      uni.showToast({
        title: '已保存到相册',
        icon: 'success'
      });
    },
    fail: (err) => {
      uni.hideLoading();
      console.error('保存失败', err);
      
      if (err.errMsg.indexOf('auth deny') > -1) {
        uni.showModal({
          title: '提示',
          content: '保存失败，请授权相册权限后重试',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              uni.openSetting();
            }
          }
        });
      } else {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  });
};

// 举报相关
const showReportOptions = () => {
  uni.showActionSheet({
    itemList: ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'],
    success: (res) => {
      const reportReasonIndex = res.tapIndex;
      const reasons = ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'];
      showReportInputDialog(reasons[reportReasonIndex]);
    }
  });
};

const showReportInputDialog = (reason) => {
  // 检查是否登录
  const hasLogin = uni.getStorageSync('token') || false;
  if (!hasLogin) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再进行举报',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
    return;
  }
  
  uni.showModal({
    title: '举报内容',
    content: `您选择的举报原因是: ${reason}，请确认是否提交举报？`,
    confirmText: '确认举报',
    success: (res) => {
      if (res.confirm) {
        submitReport(reason, '');
      }
    }
  });
};

const submitReport = (reason, content) => {
  uni.showLoading({
    title: '提交中...'
  });
  
  // 这里模拟提交举报信息到服务器
    setTimeout(() => {
    uni.hideLoading();
    
    uni.showToast({
      title: '举报成功',
      icon: 'success'
    });
    
    // 实际开发中，这里应该调用API提交举报信息
    // const reportData = {
    //   type: 'homeService',
    //   id: serviceData.value.id,
    //   reason: reason,
    //   content: content
    // };
    // submitReportAPI(reportData).then(() => {
    //   uni.showToast({
    //     title: '举报成功',
    //     icon: 'success'
    //   });
    // });
    }, 1500);
};

// 生命周期钩子
onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 获取服务ID和类型
  const id = options.id || '';
  const serviceType = options.type || '';
  
  // 加载服务数据
  loadServiceData(id, serviceType);
  
  // 加载相关服务推荐
  loadRelatedServices();
});

// 加载服务数据方法
const loadServiceData = (id, serviceType) => {
  // 根据不同服务类型加载不同数据
  console.log('加载服务类型:', serviceType, '服务ID:', id || '默认服务');
  
  switch(serviceType) {
    case 'installation': // 上门安装
      loadInstallationData(id);
      break;
    case 'locksmith': // 开锁换锁
      loadLocksmithData(id);
      break;
    case 'repair': // 维修改造
      loadRepairData(id);
      break;
    case 'moving': // 搬家拉货
      loadMovingData(id);
      break;
    case 'beauty': // 上门美容
      loadBeautyData(id);
      break;
    case 'tutor': // 上门家教
      loadTutorData(id);
      break;
    case 'pet_service': // 宠物服务
      loadPetServiceData(id);
      break;
    case 'plumbing': // 上门疏通
      loadPlumbingData(id);
      break;
    case 'home_cleaning': // 家政保洁
    default:
      // 使用默认的家政保洁数据
      // 不需要修改serviceData.value，因为它已经默认是家政保洁数据
      break;
  }
  
  // 修改页面标题
  uni.setNavigationBarTitle({
    title: serviceData.value.title || '服务详情'
  });
};

// 加载上门安装数据
const loadInstallationData = (id) => {
  serviceData.value = {
    id: id || 'installation12345',
    title: '专业上门安装服务',
    price: '60-200元/次起',
    tags: ['专业工具', '上门服务', '可预约', '全城服务'],
    publishTime: Date.now() - 86400000 * 3, // 3天前
    images: [
      '/static/images/service2.jpg',
      '/static/images/service3.jpg',
      '/static/images/service1.jpg'
    ],
    type: '上门安装',
    area: '磁县城区及周边15公里',
    time: '8:00-21:00（节假日照常服务）',
    method: '上门服务，提前预约',
    contents: [
      { name: '家具安装（床、柜、桌椅等）', icon: 'icon-furniture' },
      { name: '电器安装（电视、空调等）', icon: 'icon-appliance' },
      { name: '灯具安装（吊灯、壁灯等）', icon: 'icon-light' },
      { name: '卫浴安装（花洒、马桶等）', icon: 'icon-bathroom' },
      { name: '门窗安装（室内门、推拉门等）', icon: 'icon-door' },
      { name: '五金安装（挂钩、置物架等）', icon: 'icon-hardware' }
    ],
    description: '<p>磁县专业上门安装服务，提供各类家具、电器、灯具等安装服务：</p><ul><li>家具安装：床、衣柜、书柜、桌椅、沙发等</li><li>电器安装：电视机、空调、洗衣机、热水器等</li><li>灯具安装：吊灯、吸顶灯、壁灯、射灯等</li><li>卫浴安装：花洒、马桶、浴室柜、浴缸等</li><li>门窗安装：室内门、推拉门、折叠门等</li><li>五金安装：窗帘杆、挂钩、置物架等</li></ul><p>服务特点：</p><ul><li>专业师傅：经验丰富，技术精湛</li><li>专业工具：配备各类专业安装工具</li><li>上门服务：直接到府上安装，省时省力</li><li>质量保障：安装完成后检查确认，确保质量</li></ul>',
    guarantees: [
      {
        icon: 'icon-quality',
        title: '品质保障',
        desc: '专业安装师傅，持证上岗，经验丰富'
      },
      {
        icon: 'icon-safe',
        title: '安全保障',
        desc: '师傅背景调查，安装规范，损坏赔付'
      },
      {
        icon: 'icon-time',
        title: '准时服务',
        desc: '准时上门，不迟到，如有延误提前通知'
      }
    ],
    provider: {
      name: '磁县家居安装服务中心',
      avatar: '/static/images/avatar.png',
      type: '专业机构（6年经验）',
      rating: 97,
      isVerified: true
    },
    contact: {
      name: '李师傅（安装主管）',
      phone: '13987654321'
    }
  };
};

// 加载开锁换锁数据
const loadLocksmithData = (id) => {
  serviceData.value = {
    id: id || 'locksmith12345',
    title: '专业开锁换锁服务',
    price: '60-180元/次起',
    tags: ['24小时服务', '快速上门', '专业技师', '全城服务'],
    publishTime: Date.now() - 86400000 * 1, // 1天前
    images: [
      '/static/images/service3.jpg',
      '/static/images/service1.jpg',
      '/static/images/service2.jpg'
    ],
    type: '开锁换锁',
    area: '磁县城区及周边20公里',
    time: '全天24小时（节假日不休）',
    method: '上门服务，随叫随到',
    contents: [
      { name: '开锁服务（防盗门、保险柜等）', icon: 'icon-unlock' },
      { name: '换锁服务（门锁、抽屉锁等）', icon: 'icon-lock' },
      { name: '修锁服务（锁芯修复等）', icon: 'icon-repair' },
      { name: '配钥匙（各类钥匙配制）', icon: 'icon-key' },
      { name: '安装锁具（新锁安装）', icon: 'icon-install' },
      { name: '智能锁安装（指纹锁、密码锁）', icon: 'icon-smart' }
    ],
    description: '<p>磁县专业开锁换锁服务，提供24小时上门服务：</p><ul><li>开锁服务：防盗门、木门、保险柜、抽屉、汽车等各类锁具开启</li><li>换锁服务：更换各类门锁、抽屉锁、保险柜锁等</li><li>修锁服务：修复锁芯、门锁调整等</li><li>配钥匙：各类钥匙复制、配制</li><li>安装锁具：新锁具安装、加装等</li><li>智能锁安装：指纹锁、密码锁、刷卡锁等智能锁安装</li></ul><p>服务特点：</p><ul><li>24小时服务：随时响应您的需求</li><li>快速上门：接单后30分钟内到达</li><li>专业技师：持证上岗，技术精湛</li><li>合理收费：透明价格，无隐形消费</li></ul>',
    guarantees: [
      {
        icon: 'icon-quality',
        title: '品质保障',
        desc: '专业开锁师傅，持证上岗，经验丰富'
      },
      {
        icon: 'icon-safe',
        title: '安全保障',
        desc: '师傅身份验证，开锁需验证身份'
      },
      {
        icon: 'icon-time',
        title: '快速响应',
        desc: '30分钟内上门，紧急情况优先处理'
      }
    ],
    provider: {
      name: '磁县安全开锁服务中心',
      avatar: '/static/images/avatar.png',
      type: '专业机构（8年经验）',
      rating: 98,
      isVerified: true
    },
    contact: {
      name: '王师傅（开锁技师）',
      phone: '13876543210'
    }
  };
};

// 加载维修改造数据
const loadRepairData = (id) => {
  serviceData.value = {
    id: id || 'repair12345',
    title: '专业维修改造服务',
    price: '80-300元/次起',
    tags: ['专业维修', '上门服务', '可预约', '全城服务'],
    publishTime: Date.now() - 86400000 * 2, // 2天前
    images: [
      '/static/images/service1.jpg',
      '/static/images/service2.jpg',
      '/static/images/service3.jpg'
    ],
    type: '维修改造',
    area: '磁县城区及周边15公里',
    time: '8:00-20:00（节假日照常服务）',
    method: '上门服务，提前预约',
    contents: [
      { name: '水电维修（水管、电路等）', icon: 'icon-water-electric' },
      { name: '家具维修（桌椅、柜子等）', icon: 'icon-furniture' },
      { name: '家电维修（冰箱、洗衣机等）', icon: 'icon-appliance' },
      { name: '门窗维修（门锁、合页等）', icon: 'icon-door' },
      { name: '墙面维修（墙漆、裂缝等）', icon: 'icon-wall' },
      { name: '小型改造（隔断、架子等）', icon: 'icon-renovation' }
    ],
    description: '<p>磁县专业维修改造服务，解决您家中各类维修需求：</p><ul><li>水电维修：水管漏水、电路故障、开关插座更换等</li><li>家具维修：桌椅修复、柜门调整、五金更换等</li><li>家电维修：常见家电故障排除、清洗保养等</li><li>门窗维修：门锁更换、合页调整、密封条更换等</li><li>墙面维修：墙漆修补、裂缝处理、壁纸修复等</li><li>小型改造：简易隔断、置物架安装、小型装修等</li></ul><p>服务特点：</p><ul><li>专业师傅：经验丰富，技术全面</li><li>快速响应：当天预约，快速上门</li><li>合理收费：先检查后报价，认可后施工</li><li>质量保障：维修后保修期内免费返修</li></ul>',
    guarantees: [
      {
        icon: 'icon-quality',
        title: '品质保障',
        desc: '专业维修师傅，技术全面，经验丰富'
      },
      {
        icon: 'icon-safe',
        title: '安全保障',
        desc: '规范操作，确保安全，损坏赔付'
      },
      {
        icon: 'icon-time',
        title: '保修服务',
        desc: '维修后提供保修期，期内免费返修'
      }
    ],
    provider: {
      name: '磁县万能维修中心',
      avatar: '/static/images/avatar.png',
      type: '专业机构（7年经验）',
      rating: 96,
      isVerified: true
    },
    contact: {
      name: '张师傅（维修主管）',
      phone: '13765432109'
    }
  };
};

// 其他服务类型的数据加载方法可以根据需要添加
const loadMovingData = (id) => {
  // 搬家拉货数据
  serviceData.value = {
    id: id || 'moving12345',
    title: '专业搬家拉货服务',
    price: '100-500元/次起',
    tags: ['专业搬运', '全城服务', '可预约', '价格合理'],
    type: '搬家拉货',
    // ...其他搬家拉货相关数据
  };
};

const loadBeautyData = (id) => {
  // 上门美容数据
  serviceData.value = {
    id: id || 'beauty12345',
    title: '专业上门美容服务',
    price: '80-300元/次起',
    tags: ['专业美容', '上门服务', '可预约', '全城服务'],
    type: '上门美容',
    // ...其他上门美容相关数据
  };
};

const loadTutorData = (id) => {
  // 上门家教数据
  serviceData.value = {
    id: id || 'tutor12345',
    title: '专业上门家教服务',
    price: '100-200元/小时起',
    tags: ['专业教师', '上门授课', '可预约', '全城服务'],
    type: '上门家教',
    // ...其他上门家教相关数据
  };
};

const loadPetServiceData = (id) => {
  // 宠物服务数据
  serviceData.value = {
    id: id || 'pet_service12345',
    title: '专业宠物上门服务',
    price: '80-300元/次起',
    tags: ['宠物美容', '宠物医疗', '上门服务', '全城服务'],
    type: '宠物服务',
    // ...其他宠物服务相关数据
  };
};

const loadPlumbingData = (id) => {
  // 上门疏通数据
  serviceData.value = {
    id: id || 'plumbing12345',
    title: '专业上门疏通服务',
    price: '80-200元/次起',
    tags: ['管道疏通', '马桶疏通', '上门服务', '全城服务'],
    type: '上门疏通',
    // ...其他上门疏通相关数据
  };
};
</script>

<style>
.service-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 150rpx;
  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}

.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.service-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 内容卡片基础样式 */
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 服务基本信息卡片 */
.service-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.service-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}

.service-meta {
  margin-bottom: 24rpx;
}

.service-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.service-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.service-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.service-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.service-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.service-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 区块标题优化 - 添加蓝色竖线 */
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}

/* 服务内容 */
.content-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 16rpx;
}

.content-item {
  width: 33.33%;
  padding: 12rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.content-icon {
  font-size: 32rpx;
  color: #1890ff;
  margin-right: 8rpx;
}

.content-text {
  font-size: 26rpx;
  color: #666;
}

/* 服务说明 */
.desc-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  border: 1px solid rgba(24, 144, 255, 0.1);
}

/* 服务保障 */
.guarantee-list {
  display: flex;
  flex-direction: column;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 16rpx;
}

.guarantee-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.guarantee-item:last-child {
  border-bottom: none;
}

.guarantee-icon {
  font-size: 40rpx;
  color: #1890ff;
  margin-right: 16rpx;
}

.guarantee-info {
  flex: 1;
}

.guarantee-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.guarantee-desc {
  font-size: 26rpx;
  color: #666;
}

/* 服务商信息 */
.provider-header {
  display: flex;
  align-items: center;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 20rpx;
}

.provider-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  border: 2rpx solid #e8e8e8;
}

.provider-avatar image {
  width: 100%;
  height: 100%;
}

.provider-info {
  flex: 1;
}

.provider-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.provider-meta {
  display: flex;
  align-items: center;
}

.provider-type, .provider-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 联系方式样式 - 电话显示为绿色，提示为黄色 */
.contact-content {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #888;
}

.contact-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.contact-phone {
  color: #52c41a; /* 修改为绿色 */
  font-weight: 500;
}

.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  padding: 10rpx 16rpx;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8rpx;
}

.tips-icon {
  font-size: 24rpx;
  color: #ff9800; /* 黄色 */
  margin-right: 8rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #ff9800; /* 黄色 */
}

/* 举报卡片样式 */
.report-card {
  margin: 0 auto 24rpx;
  padding: 0;
  overflow: hidden;
}

.report-content {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #fff;
  position: relative;
}

.report-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.report-text-container {
  flex: 1;
}

.report-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.report-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  line-height: 1.4;
}

.report-arrow {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 40rpx;
}

.arrow-icon {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
}

.arrow-icon::after {
  content: '';
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #999;
  border-right: 2rpx solid #999;
  transform: rotate(45deg);
}

.report-content:active {
  background-color: #f9f9f9;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}

.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}

.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

/* 相关服务推荐样式 */
.related-services-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.related-services-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

/* 相关服务列表样式 */
.related-services-list {
  margin-bottom: 12px;
}

.related-service-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.related-service-item:last-child {
  border-bottom: none;
}

.service-item-content {
  display: flex;
  align-items: center;
}

.service-item-left {
  margin-right: 12px;
}

.provider-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.service-item-middle {
  flex: 1;
  overflow: hidden;
}

.service-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.service-item-provider {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.service-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.service-item-tag {
  font-size: 11px;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
}

.service-item-tag-more {
  font-size: 11px;
  color: #999;
}

.service-item-right {
  min-width: 80px;
  text-align: right;
}

.service-item-price {
  font-size: 15px;
  font-weight: 500;
  color: #ff5252;
}

/* 查看更多按钮样式 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}

.view-more-text {
  font-size: 14px;
  color: #1890ff;
}

.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #1890ff;
}

/* 空数据提示样式 */
.empty-related-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}
</style> 