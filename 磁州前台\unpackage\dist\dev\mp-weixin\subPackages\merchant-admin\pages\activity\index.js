"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 20,
      // 默认值，将在onLoad中获取真实高度
      refreshing: false,
      loading: true,
      currentTab: "all",
      currentType: "all",
      // 当前选中的活动类型
      hasMore: true,
      page: 1,
      // 活动统计数据
      statistics: {
        total: 8,
        running: 3,
        upcoming: 1,
        ended: 4
      },
      // 筛选标签
      filterTabs: [
        { label: "全部", value: "all" },
        { label: "进行中", value: "running" },
        { label: "未开始", value: "upcoming" },
        { label: "已结束", value: "ended" },
        { label: "最近发布", value: "recent" },
        { label: "高参与度", value: "popular" }
      ],
      // 活动类型选项
      activityTypes: [
        { label: "全部类型", value: "all" },
        { label: "团购活动", value: "groupon" },
        { label: "优惠券", value: "coupon" },
        { label: "秒杀活动", value: "seckill" },
        { label: "满减活动", value: "discount" },
        { label: "积分兑换", value: "points" }
      ],
      // 活动列表数据
      activityList: [
        {
          id: 1,
          title: "夏季特惠，全场满300减50",
          coverImage: "/static/images/activity-1.jpg",
          startTime: "2023-07-01",
          endTime: "2023-08-31",
          status: "running",
          views: 1258,
          participants: 78,
          isTop: true,
          type: "discount"
          // 活动类型：满减活动
        },
        {
          id: 2,
          title: "开业庆典，免费品尝活动",
          coverImage: "/static/images/activity-2.jpg",
          startTime: "2023-06-15",
          endTime: "2023-06-20",
          status: "ended",
          views: 876,
          participants: 126,
          isTop: false,
          type: "coupon"
          // 活动类型：优惠券
        },
        {
          id: 3,
          title: "周年庆典，抽奖赢大礼",
          coverImage: "/static/images/activity-3.jpg",
          startTime: "2023-12-01",
          endTime: "2023-12-10",
          status: "upcoming",
          views: 322,
          participants: 0,
          isTop: false,
          type: "groupon"
          // 活动类型：团购活动
        }
      ]
    };
  },
  onLoad() {
    this.getStatusBarHeight();
    this.loadActivityData();
  },
  methods: {
    // 获取状态栏高度
    getStatusBarHeight() {
      common_vendor.index.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
        }
      });
    },
    // 返回上一页
    goBack() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:295", "返回按钮点击");
      common_vendor.index.navigateBack();
    },
    // 显示更多选项
    showMoreOptions() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:300", "更多选项按钮点击");
      common_vendor.index.showActionSheet({
        itemList: ["批量管理", "活动统计", "帮助中心"],
        success: (res) => {
          common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:305", "选择了: ", res.tapIndex);
        }
      });
    },
    // 创建新活动
    createNewActivity() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:311", "创建新活动按钮点击");
      common_vendor.index.showLoading({
        title: "正在跳转...",
        mask: true
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.navigateTo({
          url: "/subPackages/merchant-admin-marketing/pages/marketing/index",
          success: () => {
            common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:327", "跳转到营销中心成功");
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at subPackages/merchant-admin/pages/activity/index.vue:330", "跳转失败:", err);
            common_vendor.index.showModal({
              title: "提示",
              content: "无法访问营销中心页面",
              showCancel: false
            });
          }
        });
      }, 300);
    },
    // 下拉刷新
    onRefresh() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:342", "下拉刷新触发");
      this.refreshing = true;
      this.page = 1;
      this.hasMore = true;
      this.loadActivityData();
    },
    // 加载活动数据
    loadActivityData() {
      this.loading = true;
      setTimeout(() => {
        if (this.page === 1) {
          const filteredList = this.getFilteredActivities();
          this.activityList = filteredList;
        } else {
          common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:362", "加载更多数据，页码：", this.page);
        }
        this.loading = false;
        this.refreshing = false;
        if (this.page > 1) {
          this.hasMore = false;
        }
      }, 1e3);
    },
    // 获取筛选后的活动列表
    getFilteredActivities() {
      const allActivities = [
        {
          id: 1,
          title: "夏季特惠，全场满300减50",
          coverImage: "/static/images/activity-1.jpg",
          startTime: "2023-07-01",
          endTime: "2023-08-31",
          status: "running",
          views: 1258,
          participants: 78,
          isTop: true,
          type: "discount"
        },
        {
          id: 2,
          title: "开业庆典，免费品尝活动",
          coverImage: "/static/images/activity-2.jpg",
          startTime: "2023-06-15",
          endTime: "2023-06-20",
          status: "ended",
          views: 876,
          participants: 126,
          isTop: false,
          type: "coupon"
        },
        {
          id: 3,
          title: "周年庆典，抽奖赢大礼",
          coverImage: "/static/images/activity-3.jpg",
          startTime: "2023-12-01",
          endTime: "2023-12-10",
          status: "upcoming",
          views: 322,
          participants: 0,
          isTop: false,
          type: "groupon"
        },
        {
          id: 4,
          title: "限时秒杀，爆款5折起",
          coverImage: "/static/images/activity-1.jpg",
          startTime: "2023-08-15",
          endTime: "2023-08-17",
          status: "running",
          views: 1890,
          participants: 245,
          isTop: false,
          type: "seckill"
        },
        {
          id: 5,
          title: "会员积分兑换活动",
          coverImage: "/static/images/activity-2.jpg",
          startTime: "2023-07-20",
          endTime: "2023-09-20",
          status: "running",
          views: 768,
          participants: 124,
          isTop: false,
          type: "points"
        }
      ];
      return allActivities.filter((activity) => {
        const typeMatch = this.currentType === "all" || activity.type === this.currentType;
        let statusMatch = true;
        if (this.currentTab === "running") {
          statusMatch = activity.status === "running";
        } else if (this.currentTab === "upcoming") {
          statusMatch = activity.status === "upcoming";
        } else if (this.currentTab === "ended") {
          statusMatch = activity.status === "ended";
        }
        return typeMatch && statusMatch;
      });
    },
    // 加载更多活动
    loadMoreActivities() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:460", "加载更多按钮点击");
      if (!this.hasMore)
        return;
      this.page++;
      this.loadActivityData();
    },
    // 切换标签
    switchTab(tabValue) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:468", "切换状态标签：", tabValue);
      this.currentTab = tabValue;
      this.page = 1;
      this.hasMore = true;
      this.loadActivityData();
    },
    // 显示排序选项
    showSortOptions() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:476", "显示排序选项");
      common_vendor.index.showActionSheet({
        itemList: ["最新发布", "即将开始", "即将结束", "参与人数"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "排序方式已更改",
            icon: "none"
          });
        }
      });
    },
    // 显示筛选选项
    showFilterOptions() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:489", "显示筛选选项");
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin/pages/activity/filter"
      });
    },
    // 获取活动状态文本
    getStatusText(status) {
      const statusMap = {
        "running": "进行中",
        "ended": "已结束",
        "upcoming": "未开始"
      };
      return statusMap[status] || "未知状态";
    },
    // 格式化日期范围
    formatDateRange(start, end) {
      if (!start || !end)
        return "";
      const startDate = new Date(start);
      const endDate = new Date(end);
      const startMonth = startDate.getMonth() + 1;
      const startDay = startDate.getDate();
      const endMonth = endDate.getMonth() + 1;
      const endDay = endDate.getDate();
      return `${startMonth}.${startDay} - ${endMonth}.${endDay}`;
    },
    // 查看活动详情
    viewActivityDetail(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:519", "查看活动详情，ID：", id);
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin/pages/activity/detail?id=${id}`
      });
    },
    // 编辑活动
    editActivity(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:526", "编辑活动按钮点击，ID：", id);
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin/pages/activity/edit?id=${id}`
      });
    },
    // 推广活动
    promoteActivity(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:533", "推广活动按钮点击，ID：", id);
      const activity = this.activityList.find((item) => item.id === id);
      const topAction = activity && activity.isTop ? "取消置顶" : "置顶活动";
      common_vendor.index.showActionSheet({
        itemList: [topAction, "推荐到首页", "短信推广", "分享到群", "朋友圈宣传"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.toggleTopStatus(id);
          } else {
            common_vendor.index.showToast({
              title: "推广设置成功",
              icon: "success"
            });
          }
        }
      });
    },
    // 切换活动置顶状态
    toggleTopStatus(id) {
      const index = this.activityList.findIndex((item) => item.id === id);
      if (index !== -1) {
        const isTop = !this.activityList[index].isTop;
        this.activityList[index].isTop = isTop;
        common_vendor.index.showToast({
          title: isTop ? "活动已置顶" : "已取消置顶",
          icon: "success"
        });
      }
    },
    // 分享活动
    shareActivity(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:572", "分享活动按钮点击，ID：", id);
      common_vendor.index.showToast({
        title: "已打开分享菜单",
        icon: "none",
        duration: 1500
      });
      setTimeout(() => {
        common_vendor.index.showToast({
          title: "活动已分享",
          icon: "success"
        });
      }, 1500);
    },
    // 显示活动更多选项
    showActivityOptions(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:589", "更多按钮点击，ID：", id);
      common_vendor.index.showActionSheet({
        itemList: ["查看数据", "复制活动", "下架活动", "删除活动"],
        success: (res) => {
          const actions = ["viewData", "duplicateActivity", "stopActivity", "deleteActivity"];
          const action = actions[res.tapIndex];
          if (action && this[action]) {
            this[action](id);
          }
        }
      });
    },
    // 查看数据
    viewData(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:605", "查看数据，ID：", id);
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin/pages/activity/data?id=${id}`
      });
    },
    // 复制活动
    duplicateActivity(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:612", "复制活动，ID：", id);
      common_vendor.index.showModal({
        title: "复制活动",
        content: "确定要复制该活动吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "活动复制成功",
              icon: "success"
            });
          }
        }
      });
    },
    // 下架活动
    stopActivity(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:628", "下架活动，ID：", id);
      common_vendor.index.showModal({
        title: "下架活动",
        content: "确定要下架该活动吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "活动已下架",
              icon: "success"
            });
          }
        }
      });
    },
    // 删除活动
    deleteActivity(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:644", "删除活动，ID：", id);
      common_vendor.index.showModal({
        title: "删除活动",
        content: "确定要删除该活动吗？删除后不可恢复",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "活动已删除",
              icon: "success"
            });
          }
        }
      });
    },
    // 切换活动类型
    switchType(typeValue) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin/pages/activity/index.vue:660", "切换活动类型：", typeValue);
      this.currentType = typeValue;
      this.page = 1;
      this.hasMore = true;
      this.loadActivityData();
    },
    // 获取活动类型文本
    getTypeText(type) {
      const typeMap = {
        "groupon": "团购活动",
        "coupon": "优惠券",
        "seckill": "秒杀活动",
        "discount": "满减活动",
        "points": "积分兑换"
      };
      return typeMap[type] || "未知类型";
    }
  }
};
const __injectCSSVars__ = () => {
  common_vendor.useCssVars((_ctx) => ({
    "34f4e901": _ctx.statusBarHeight + "px",
    "547e3084": _ctx.statusBarHeight + 44 + "px",
    "7c848ece": `calc(100vh - ${_ctx.statusBarHeight + 44}px)`
  }));
};
const __setup__ = _sfc_main.setup;
_sfc_main.setup = __setup__ ? (props, ctx) => {
  __injectCSSVars__();
  return __setup__(props, ctx);
} : __injectCSSVars__;
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: common_vendor.p({
      d: "M15 18L9 12L15 6",
      stroke: "white",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    c: common_vendor.p({
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    d: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    e: common_vendor.p({
      d: "M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z",
      fill: "white",
      stroke: "white",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    f: common_vendor.p({
      d: "M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z",
      fill: "white",
      stroke: "white",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    g: common_vendor.p({
      d: "M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z",
      fill: "white",
      stroke: "white",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    h: common_vendor.p({
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    i: common_vendor.o((...args) => $options.showMoreOptions && $options.showMoreOptions(...args)),
    j: common_vendor.t($data.statistics.total),
    k: common_vendor.t($data.statistics.running),
    l: common_vendor.t($data.statistics.upcoming),
    m: common_vendor.t($data.statistics.ended),
    n: common_vendor.f($data.activityTypes, (type, index, i0) => {
      return {
        a: common_vendor.t(type.label),
        b: "type-" + index,
        c: $data.currentType === type.value ? 1 : "",
        d: common_vendor.o(($event) => $options.switchType(type.value), "type-" + index)
      };
    }),
    o: common_vendor.f($data.filterTabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.label),
        b: "status-" + index,
        c: $data.currentTab === tab.value ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(tab.value), "status-" + index)
      };
    }),
    p: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    q: $data.activityList.length === 0
  }, $data.activityList.length === 0 ? {
    r: common_assets._imports_2$43,
    s: common_vendor.o((...args) => $options.createNewActivity && $options.createNewActivity(...args))
  } : {
    t: common_vendor.f($data.activityList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.getTypeText(item.type)),
        b: common_vendor.n("type-" + item.type),
        c: common_vendor.t($options.getStatusText(item.status)),
        d: common_vendor.n("status-" + item.status),
        e: item.isTop
      }, item.isTop ? {} : {}, {
        f: item.coverImage,
        g: common_vendor.t(item.title),
        h: "36c93d94-7-" + i0 + "," + ("36c93d94-6-" + i0),
        i: "36c93d94-6-" + i0,
        j: common_vendor.t($options.formatDateRange(item.startTime, item.endTime)),
        k: "36c93d94-9-" + i0 + "," + ("36c93d94-8-" + i0),
        l: "36c93d94-10-" + i0 + "," + ("36c93d94-8-" + i0),
        m: "36c93d94-8-" + i0,
        n: common_vendor.t(item.views),
        o: "36c93d94-12-" + i0 + "," + ("36c93d94-11-" + i0),
        p: "36c93d94-11-" + i0,
        q: common_vendor.t(item.participants),
        r: common_vendor.o(($event) => $options.editActivity(item.id), index),
        s: common_vendor.o(($event) => $options.promoteActivity(item.id), index),
        t: common_vendor.o(($event) => $options.shareActivity(item.id), index),
        v: common_vendor.o(($event) => $options.showActivityOptions(item.id), index),
        w: index,
        x: common_vendor.o(($event) => $options.viewActivityDetail(item.id), index)
      });
    }),
    v: common_vendor.p({
      d: "M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",
      stroke: "#8E8E93",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    w: common_vendor.p({
      width: "14",
      height: "14",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    x: common_vendor.p({
      d: "M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z",
      stroke: "#8E8E93",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    y: common_vendor.p({
      d: "M2.45825 12C3.73253 7.94288 7.52281 5 12.0004 5C16.4781 5 20.2684 7.94291 21.5426 12C20.2684 16.0571 16.4781 19 12.0005 19C7.52281 19 3.73251 16.0571 2.45825 12Z",
      stroke: "#8E8E93",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    z: common_vendor.p({
      width: "14",
      height: "14",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    A: common_vendor.p({
      d: "M17 21V19C17 16.7909 15.2091 15 13 15H5C2.79086 15 1 16.7909 1 19V21M23 21V19C22.9986 17.1771 21.765 15.5857 20 15.13M16 3.13C17.7699 3.58317 19.0078 5.17799 19.0078 7.005C19.0078 8.83201 17.7699 10.4268 16 10.88M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z",
      stroke: "#8E8E93",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    B: common_vendor.p({
      width: "14",
      height: "14",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    })
  }, {
    C: $data.hasMore
  }, $data.hasMore ? {
    D: common_vendor.o((...args) => $options.loadMoreActivities && $options.loadMoreActivities(...args))
  } : $data.activityList.length > 0 ? {} : {}, {
    E: $data.activityList.length > 0
  }), {
    F: $data.refreshing,
    G: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    H: common_vendor.o((...args) => $options.createNewActivity && $options.createNewActivity(...args)),
    I: common_vendor.s(_ctx.__cssVars())
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/pages/activity/index.js.map
