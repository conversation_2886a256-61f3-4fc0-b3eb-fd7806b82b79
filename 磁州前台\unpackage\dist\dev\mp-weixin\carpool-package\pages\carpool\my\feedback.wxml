<view class="feedback-container"><view class="custom-header" style="{{'padding-top:' + c}}"><view class="header-content"><view class="left-action" bindtap="{{b}}"><image src="{{a}}" class="action-icon back-icon"></image></view><view class="title-area"><text class="page-title">意见反馈</text></view><view class="right-action"></view></view></view><view class="scrollable-content" style="{{'padding-top:' + t}}"><view class="feedback-form"><view class="form-section"><view class="section-title">反馈类型</view><view class="feedback-types"><view wx:for="{{d}}" wx:for-item="type" wx:key="b" class="{{['type-item', type.c && 'active']}}" bindtap="{{type.d}}"><text class="type-text">{{type.a}}</text></view></view></view><view class="form-section"><view class="section-title">反馈内容</view><view class="content-textarea-wrapper"><block wx:if="{{r0}}"><textarea class="content-textarea" placeholder="请详细描述您遇到的问题或建议，以便我们更好地解决和改进..." maxlength="500" placeholder-class="textarea-placeholder" value="{{e}}" bindinput="{{f}}"></textarea></block><view class="word-count">{{g}}/500</view></view></view><view class="form-section"><view class="section-title">上传图片(选填)</view><view class="upload-section"><view class="image-grid"><view wx:for="{{h}}" wx:for-item="image" wx:key="c" class="image-item"><image src="{{image.a}}" mode="aspectFill" class="preview-image"></image><view class="delete-icon" catchtap="{{image.b}}">×</view></view><view wx:if="{{i}}" class="upload-item" bindtap="{{k}}"><image src="{{j}}" mode="aspectFit" class="upload-icon"></image><text class="upload-text">添加图片</text></view></view><text class="upload-hint">最多上传3张图片，每张不超过5MB</text></view></view><view class="form-section"><view class="section-title">联系方式(选填)</view><view class="contact-input-wrapper"><input class="contact-input" placeholder="请留下您的手机号或微信，方便我们联系您" placeholder-class="input-placeholder" value="{{l}}" bindinput="{{m}}"/></view></view></view><button disabled="{{n}}" class="{{['submit-button', o && 'disabled']}}" bindtap="{{p}}"> 提交反馈 </button><view wx:if="{{q}}" class="history-section"><view class="history-header"><view class="history-title">历史反馈</view></view><view class="history-list"><view wx:for="{{r}}" wx:for-item="item" wx:key="f" class="history-item" bindtap="{{item.g}}"><view class="history-content"><view class="history-info"><text class="history-type">{{item.a}}</text><text class="history-time">{{item.b}}</text></view><text class="history-brief">{{item.c}}</text></view><view class="{{['history-status', item.e]}}"><text class="status-text">{{item.d}}</text><image src="{{s}}" mode="aspectFit" class="arrow-icon"></image></view></view></view></view></view><view wx:if="{{v}}" class="popup-mask" bindtap="{{w}}"></view><view wx:if="{{x}}" class="popup-container"><view class="popup-header"><text class="popup-title">反馈详情</text><view class="popup-close" bindtap="{{y}}">×</view></view><scroll-view class="popup-content" scroll-y><view class="detail-section"><view class="detail-label">反馈类型</view><view class="detail-value">{{z}}</view></view><view class="detail-section"><view class="detail-label">提交时间</view><view class="detail-value">{{A}}</view></view><view class="detail-section"><view class="detail-label">处理状态</view><view class="{{['detail-value', C]}}">{{B}}</view></view><view class="detail-section"><view class="detail-label">反馈内容</view><view class="detail-value content">{{D}}</view></view><view wx:if="{{E}}" class="detail-section"><view class="detail-label">附件图片</view><view class="detail-images"><image wx:for="{{F}}" wx:for-item="img" wx:key="a" src="{{img.b}}" mode="widthFix" class="detail-image" bindtap="{{img.c}}"></image></view></view><view wx:if="{{G}}" class="detail-section"><view class="detail-label">官方回复</view><view class="detail-value reply"><text class="reply-time">{{H}}</text><text class="reply-content">{{I}}</text></view></view></scroll-view></view><view wx:if="{{J}}" class="popup-mask"></view><view wx:if="{{K}}" class="success-popup"><image src="{{L}}" mode="aspectFit" class="success-icon"></image><text class="success-title">提交成功</text><text class="success-message">感谢您的反馈，我们会尽快处理</text><button class="success-button" bindtap="{{M}}">确定</button></view></view>