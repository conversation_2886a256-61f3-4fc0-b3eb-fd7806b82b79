{"name": "postcss-merge-longhand", "version": "5.1.7", "description": "Merge longhand properties into shorthand with PostCSS.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["LICENSE-MIT", "src", "types"], "keywords": ["css", "minify", "optimise", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"postcss-value-parser": "^4.2.0", "stylehacks": "^5.1.1"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}}