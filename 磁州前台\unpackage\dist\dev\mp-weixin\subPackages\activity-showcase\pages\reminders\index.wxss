
.reminders-container.data-v-f3ba5f86 {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar.data-v-f3ba5f86 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}
.navbar-bg.data-v-f3ba5f86 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #34C759 0%, #7ED321 100%);
}
.navbar-content.data-v-f3ba5f86 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 112rpx; /* 原来是107rpx，再增加5rpx */
  padding: var(--status-bar-height) 30rpx 0;
}
.back-btn.data-v-f3ba5f86, .settings-btn.data-v-f3ba5f86 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-f3ba5f86 {
  width: 36rpx;
  height: 36rpx;
}
.navbar-title.data-v-f3ba5f86 {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.navbar-right.data-v-f3ba5f86 {
  display: flex;
  align-items: center;
}

/* 标签栏样式 */
.reminder-tabs.data-v-f3ba5f86 {
  display: flex;
  background: #FFFFFF;
  padding: 0 20rpx;
  margin-top: calc(var(--status-bar-height) + 112rpx); /* 原来是107rpx，再增加5rpx */
  border-bottom: 1rpx solid #EEEEEE;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.tab-item.data-v-f3ba5f86 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}
.tab-text.data-v-f3ba5f86 {
  font-size: 28rpx;
  color: #333333;
  padding: 0 10rpx;
}
.tab-item.active .tab-text.data-v-f3ba5f86 {
  color: #34C759;
  font-weight: 500;
}
.tab-indicator.data-v-f3ba5f86 {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 6rpx;
  border-radius: 3rpx;
}

/* 提醒列表样式 */
.reminders-swiper.data-v-f3ba5f86 {
  height: calc(100vh - var(--status-bar-height) - 112rpx - 70rpx); /* 原来是107rpx，再增加5rpx */
}
.tab-content.data-v-f3ba5f86 {
  height: 100%;
  padding: 20rpx;
}
.reminders-list.data-v-f3ba5f86 {
  padding-bottom: 30rpx;
}
.reminder-card.data-v-f3ba5f86 {
  display: flex;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  border-left: 6rpx solid #34C759;
}
.reminder-card.read.data-v-f3ba5f86 {
  border-left: 6rpx solid #DDDDDD;
  opacity: 0.8;
}
.reminder-icon.data-v-f3ba5f86 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx;
}
.reminder-content.data-v-f3ba5f86 {
  flex: 1;
  padding: 20rpx 30rpx 20rpx 0;
}
.reminder-header.data-v-f3ba5f86 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.reminder-title.data-v-f3ba5f86 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.reminder-time.data-v-f3ba5f86 {
  font-size: 24rpx;
  color: #999999;
}
.reminder-desc.data-v-f3ba5f86 {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}
.activity-info.data-v-f3ba5f86 {
  display: flex;
  background: #F9F9F9;
  border-radius: 10rpx;
  padding: 15rpx;
  margin-bottom: 20rpx;
}
.activity-image.data-v-f3ba5f86 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 15rpx;
}
.activity-details.data-v-f3ba5f86 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.activity-name.data-v-f3ba5f86 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10rpx;
}
.activity-meta.data-v-f3ba5f86 {
  display: flex;
  flex-direction: column;
}
.activity-date.data-v-f3ba5f86, .activity-location.data-v-f3ba5f86 {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 5rpx;
}
.reminder-actions.data-v-f3ba5f86 {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.action-btn.data-v-f3ba5f86 {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}
.action-btn.secondary.data-v-f3ba5f86 {
  background: #F5F5F5;
  color: #666666;
  border: 1rpx solid #DDDDDD;
  box-shadow: none;
}

/* 空状态样式 */
.empty-state.data-v-f3ba5f86 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image.data-v-f3ba5f86 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-f3ba5f86 {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.empty-state .action-btn.data-v-f3ba5f86 {
  padding: 15rpx 60rpx;
  font-size: 28rpx;
  color: #FFFFFF;
}

/* 设置弹窗样式 */
.settings-popup.data-v-f3ba5f86 {
  background: #FFFFFF;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  padding: 30rpx;
  max-height: 70vh;
}
.settings-header.data-v-f3ba5f86 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.settings-title.data-v-f3ba5f86 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.settings-close.data-v-f3ba5f86 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.settings-content.data-v-f3ba5f86 {
  max-height: calc(70vh - 180rpx);
  overflow-y: auto;
}
.settings-section.data-v-f3ba5f86 {
  margin-bottom: 30rpx;
}
.section-title.data-v-f3ba5f86 {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.settings-item.data-v-f3ba5f86 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}
.settings-item.data-v-f3ba5f86:last-child {
  border-bottom: none;
}
.item-label.data-v-f3ba5f86 {
  font-size: 28rpx;
  color: #333333;
}
.picker-value.data-v-f3ba5f86 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
}
.clear-options.data-v-f3ba5f86 {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}
.clear-btn.data-v-f3ba5f86 {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  background: #F5F5F5;
  color: #666666;
  margin-right: 20rpx;
}
.clear-btn.danger.data-v-f3ba5f86 {
  background: linear-gradient(135deg, #FF3B30 0%, #FF9580 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);
  margin-right: 0;
}
