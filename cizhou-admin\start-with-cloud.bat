@echo off
chcp 65001 >nul

echo.
echo ========================================
echo   磁州生活网云服务启动方案
echo ========================================
echo.

echo 此方案使用免费的云数据库服务，无需本地安装
echo.

echo 推荐的免费云服务：
echo.

echo [1] 数据库服务：
echo     - PlanetScale (MySQL兼容)
echo     - Supabase (PostgreSQL)
echo     - MongoDB Atlas (MongoDB)
echo     - 阿里云RDS免费试用
echo.

echo [2] Redis服务：
echo     - Redis Cloud (免费30MB)
echo     - Upstash Redis (免费10K请求/天)
echo     - 阿里云Redis免费试用
echo.

echo [3] 配置步骤：
echo     1. 注册云服务账号
echo     2. 创建数据库实例
echo     3. 获取连接信息
echo     4. 修改配置文件
echo.

echo ========================================
echo 配置文件修改
echo ========================================
echo.

echo 需要修改以下配置文件中的数据库连接信息：
echo.

echo backend\cizhou-auth\src\main\resources\application.yml
echo backend\cizhou-gateway\src\main\resources\application.yml
echo.

echo 修改内容示例：
echo.
echo spring:
echo   datasource:
echo     url: **********************************************
echo     username: your-username
echo     password: your-password
echo.
echo   redis:
echo     host: your-redis-host
echo     port: 6379
echo     password: your-redis-password
echo.

echo ========================================
echo 启动步骤
echo ========================================
echo.

echo 配置完成后，按以下步骤启动：
echo.

echo [1] 初始化云数据库：
echo     - 连接到云数据库
echo     - 执行 backend\sql\init.sql
echo.

echo [2] 启动后端服务：
echo     cd backend\cizhou-auth
echo     mvn spring-boot:run
echo.

echo [3] 启动网关服务：
echo     cd backend\cizhou-gateway
echo     mvn spring-boot:run
echo.

echo [4] 启动前端服务：
echo     cd frontend
echo     npm install
echo     npm run dev
echo.

echo ========================================
echo 免费云服务推荐
echo ========================================
echo.

echo MySQL数据库：
echo   - PlanetScale: https://planetscale.com/
echo   - 阿里云RDS: https://www.aliyun.com/product/rds
echo.

echo Redis缓存：
echo   - Upstash: https://upstash.com/
echo   - Redis Cloud: https://redis.com/redis-enterprise-cloud/
echo.

echo 一体化方案：
echo   - Supabase: https://supabase.com/ (数据库+认证+存储)
echo   - Firebase: https://firebase.google.com/
echo.

pause
