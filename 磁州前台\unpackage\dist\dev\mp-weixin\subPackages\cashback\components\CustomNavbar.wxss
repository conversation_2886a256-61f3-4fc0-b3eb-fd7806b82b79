/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-1bb327dd, html.data-v-1bb327dd, #app.data-v-1bb327dd, .index-container.data-v-1bb327dd {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.custom-navbar.data-v-1bb327dd {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.custom-navbar .navbar-content.data-v-1bb327dd {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}
.custom-navbar .navbar-back.data-v-1bb327dd, .custom-navbar .navbar-close.data-v-1bb327dd, .custom-navbar .navbar-placeholder.data-v-1bb327dd {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 16px;
}
.custom-navbar .navbar-back .back-icon.data-v-1bb327dd, .custom-navbar .navbar-close .back-icon.data-v-1bb327dd, .custom-navbar .navbar-placeholder .back-icon.data-v-1bb327dd {
  width: 20px;
  height: 20px;
}
.custom-navbar .navbar-back .close-icon.data-v-1bb327dd, .custom-navbar .navbar-close .close-icon.data-v-1bb327dd, .custom-navbar .navbar-placeholder .close-icon.data-v-1bb327dd {
  color: #FFFFFF;
}
.custom-navbar .navbar-title.data-v-1bb327dd {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}