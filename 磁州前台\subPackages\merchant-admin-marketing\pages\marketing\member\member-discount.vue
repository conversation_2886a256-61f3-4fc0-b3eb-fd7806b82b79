<!-- 会员折扣页面开始 -->
<template>
  <view class="member-discount-container">
    <!-- 页面标题区域 -->
    <view class="page-header">
      <view class="title-section">
        <text class="page-title">会员折扣</text>
        <text class="page-subtitle">管理会员专享商品折扣</text>
      </view>
      
      <!-- 添加折扣按钮 -->
      <view class="add-discount-btn" @click="showAddDiscountModal">
        <text class="btn-text">添加折扣</text>
        <text class="btn-icon">+</text>
      </view>
    </view>
    
    <!-- 会员折扣列表 -->
    <view class="discount-list">
      <view v-if="discounts.length === 0" class="empty-tip">
        <image class="empty-icon" src="/static/images/empty-data.svg"></image>
        <text class="empty-text">暂无会员折扣，点击"添加折扣"创建</text>
      </view>
      
      <view v-else class="discount-cards">
        <view v-for="(discount, index) in discounts" :key="index" class="discount-card">
          <view class="discount-card-header" :style="{ backgroundColor: discount.color }">
            <view class="discount-name">{{ discount.name }}</view>
            <view class="discount-actions">
              <text class="action-btn edit" @click="editDiscount(discount)">编辑</text>
              <text class="action-btn delete" @click="confirmDeleteDiscount(discount)">删除</text>
            </view>
          </view>
          <view class="discount-card-body">
            <view class="discount-info-item">
              <text class="info-label">折扣图标：</text>
              <image class="discount-icon" :src="discount.icon" mode="aspectFit"></image>
            </view>
            <view class="discount-info-item">
              <text class="info-label">适用等级：</text>
              <view class="level-tags">
                <text 
                  v-for="(level, idx) in discount.applicableLevels" 
                  :key="idx" 
                  class="level-tag"
                >{{ level }}</text>
              </view>
            </view>
            <view class="discount-info-item">
              <text class="info-label">折扣力度：</text>
              <text class="info-value">{{ discount.value }}</text>
            </view>
            <view class="discount-info-item">
              <text class="info-label">适用范围：</text>
              <text class="info-value">{{ discount.scope }}</text>
            </view>
            <view class="discount-info-item">
              <text class="info-label">折扣说明：</text>
              <text class="info-value">{{ discount.description || '暂无折扣说明' }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 添加/编辑折扣弹窗 -->
    <uni-popup ref="discountFormPopup" type="center">
      <view class="discount-form-popup">
        <view class="popup-header">
          <text class="popup-title">{{ isEditing ? '编辑折扣' : '添加折扣' }}</text>
          <text class="popup-close" @click="closeDiscountModal">×</text>
        </view>
        <view class="popup-body">
          <view class="form-item">
            <text class="form-label">折扣名称</text>
            <input class="form-input" v-model="discountForm.name" placeholder="请输入折扣名称" />
          </view>
          <view class="form-item">
            <text class="form-label">折扣颜色</text>
            <view class="color-picker">
              <view 
                v-for="(color, idx) in colorOptions" 
                :key="idx" 
                class="color-option"
                :class="{ active: discountForm.color === color }"
                :style="{ backgroundColor: color }"
                @click="discountForm.color = color"
              ></view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">折扣图标</text>
            <view class="icon-upload">
              <image v-if="discountForm.icon" class="preview-icon" :src="discountForm.icon" mode="aspectFit"></image>
              <view v-else class="upload-btn" @click="chooseIcon">
                <text class="upload-icon">+</text>
                <text class="upload-text">上传图标</text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">折扣力度</text>
            <input class="form-input" v-model="discountForm.value" placeholder="请输入折扣力度，如9折、8.5折等" />
          </view>
          <view class="form-item">
            <text class="form-label">适用范围</text>
            <picker class="form-picker" :range="discountScopes" @change="onScopeChange">
              <view class="picker-value">{{ discountForm.scope || '请选择适用范围' }}</view>
            </picker>
          </view>
          <view class="form-item">
            <text class="form-label">适用等级</text>
            <view class="level-checkboxes">
              <view 
                v-for="(level, idx) in memberLevels" 
                :key="idx" 
                class="level-checkbox"
                :class="{ active: isLevelSelected(level.name) }"
                @click="toggleLevelSelection(level.name)"
              >
                <text class="checkbox-icon">{{ isLevelSelected(level.name) ? '✓' : '' }}</text>
                <text class="checkbox-label">{{ level.name }}</text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">折扣说明</text>
            <textarea class="form-textarea" v-model="discountForm.description" placeholder="请输入折扣说明"></textarea>
          </view>
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @click="closeDiscountModal">取消</button>
          <button class="confirm-btn" @click="saveDiscountForm">确认</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 删除确认弹窗 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog
        type="warning"
        title="删除确认"
        content="确定要删除该会员折扣吗？删除后将无法恢复。"
        :before-close="true"
        @confirm="deleteDiscount"
        @close="closeDeleteConfirm"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      discounts: [], // 会员折扣列表
      discountForm: {
        id: '',
        name: '',
        color: '#FF6B22', // 默认橙色
        icon: '',
        value: '',
        scope: '',
        applicableLevels: [],
        description: ''
      },
      isEditing: false, // 是否为编辑模式
      currentDiscountId: null, // 当前编辑的折扣ID
      colorOptions: [
        '#FF6B22', // 橙色
        '#8A2BE2', // 紫色
        '#1E90FF', // 道奇蓝
        '#32CD32', // 酸橙绿
        '#FFD700', // 金色
        '#FF69B4', // 热粉红
        '#20B2AA', // 浅海绿
        '#FF8C00'  // 深橙色
      ],
      discountScopes: [
        '全部商品',
        '指定分类商品',
        '指定商品',
        '会员专享商品'
      ],
      memberLevels: [] // 会员等级列表
    };
  },
  onLoad() {
    this.fetchDiscounts();
    this.fetchMemberLevels();
  },
  methods: {
    // 获取会员折扣列表
    fetchDiscounts() {
      // 模拟数据，实际项目中应从API获取
      this.discounts = [
        {
          id: '1',
          name: '银卡会员折扣',
          color: '#C0C0C0',
          icon: '/static/images/discount-silver.svg',
          value: '9.5折',
          scope: '全部商品',
          applicableLevels: ['银卡会员'],
          description: '银卡会员购买全部商品享受9.5折优惠'
        },
        {
          id: '2',
          name: '金卡会员折扣',
          color: '#FFD700',
          icon: '/static/images/discount-gold.svg',
          value: '9折',
          scope: '全部商品',
          applicableLevels: ['金卡会员'],
          description: '金卡会员购买全部商品享受9折优惠'
        },
        {
          id: '3',
          name: '钻石会员折扣',
          color: '#B9F2FF',
          icon: '/static/images/discount-diamond.svg',
          value: '8.5折',
          scope: '全部商品',
          applicableLevels: ['钻石会员'],
          description: '钻石会员购买全部商品享受8.5折优惠'
        }
      ];
    },
    
    // 获取会员等级列表
    fetchMemberLevels() {
      // 模拟数据，实际项目中应从API获取
      this.memberLevels = [
        {
          id: '1',
          name: '普通会员'
        },
        {
          id: '2',
          name: '银卡会员'
        },
        {
          id: '3',
          name: '金卡会员'
        },
        {
          id: '4',
          name: '钻石会员'
        }
      ];
    },
    
    // 显示添加折扣弹窗
    showAddDiscountModal() {
      this.isEditing = false;
      this.discountForm = {
        id: '',
        name: '',
        color: '#FF6B22',
        icon: '',
        value: '',
        scope: '',
        applicableLevels: [],
        description: ''
      };
      this.$refs.discountFormPopup.open();
    },
    
    // 编辑折扣
    editDiscount(discount) {
      this.isEditing = true;
      this.currentDiscountId = discount.id;
      this.discountForm = JSON.parse(JSON.stringify(discount)); // 深拷贝
      this.$refs.discountFormPopup.open();
    },
    
    // 关闭折扣表单弹窗
    closeDiscountModal() {
      this.$refs.discountFormPopup.close();
    },
    
    // 保存折扣表单
    saveDiscountForm() {
      // 表单验证
      if (!this.discountForm.name) {
        uni.showToast({
          title: '请输入折扣名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.discountForm.value) {
        uni.showToast({
          title: '请输入折扣力度',
          icon: 'none'
        });
        return;
      }
      
      if (!this.discountForm.scope) {
        uni.showToast({
          title: '请选择适用范围',
          icon: 'none'
        });
        return;
      }
      
      if (this.discountForm.applicableLevels.length === 0) {
        uni.showToast({
          title: '请选择适用等级',
          icon: 'none'
        });
        return;
      }
      
      // 保存数据
      if (this.isEditing) {
        // 编辑现有折扣
        const index = this.discounts.findIndex(item => item.id === this.currentDiscountId);
        if (index !== -1) {
          this.discounts.splice(index, 1, JSON.parse(JSON.stringify(this.discountForm)));
        }
      } else {
        // 添加新折扣
        this.discountForm.id = Date.now().toString(); // 生成临时ID
        this.discounts.push(JSON.parse(JSON.stringify(this.discountForm)));
      }
      
      // 关闭弹窗
      this.closeDiscountModal();
      
      // 提示保存成功
      uni.showToast({
        title: this.isEditing ? '折扣修改成功' : '折扣添加成功'
      });
    },
    
    // 确认删除折扣
    confirmDeleteDiscount(discount) {
      this.currentDiscountId = discount.id;
      this.$refs.deleteConfirmPopup.open();
    },
    
    // 删除折扣
    deleteDiscount() {
      const index = this.discounts.findIndex(item => item.id === this.currentDiscountId);
      if (index !== -1) {
        this.discounts.splice(index, 1);
      }
      
      this.$refs.deleteConfirmPopup.close();
      
      uni.showToast({
        title: '折扣删除成功'
      });
    },
    
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    
    // 选择图标
    chooseIcon() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.discountForm.icon = res.tempFilePaths[0];
        }
      });
    },
    
    // 选择范围变更
    onScopeChange(e) {
      const index = e.detail.value;
      this.discountForm.scope = this.discountScopes[index];
    },
    
    // 判断等级是否被选中
    isLevelSelected(levelName) {
      return this.discountForm.applicableLevels.includes(levelName);
    },
    
    // 切换等级选择
    toggleLevelSelection(levelName) {
      const index = this.discountForm.applicableLevels.indexOf(levelName);
      if (index === -1) {
        this.discountForm.applicableLevels.push(levelName);
      } else {
        this.discountForm.applicableLevels.splice(index, 1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.member-discount-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-section {
  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .page-subtitle {
    font-size: 24rpx;
    color: #666;
    margin-top: 6rpx;
  }
}

.add-discount-btn {
  display: flex;
  align-items: center;
  background-color: #4A00E0;
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  
  .btn-text {
    font-size: 28rpx;
  }
  
  .btn-icon {
    font-size: 32rpx;
    margin-left: 8rpx;
  }
}

.discount-list {
  .empty-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    background-color: #fff;
    border-radius: 12rpx;
    
    .empty-icon {
      width: 160rpx;
      height: 160rpx;
      margin-bottom: 20rpx;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.discount-cards {
  .discount-card {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .discount-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 24rpx;
      color: #fff;
      
      .discount-name {
        font-size: 32rpx;
        font-weight: bold;
      }
      
      .discount-actions {
        display: flex;
        
        .action-btn {
          font-size: 24rpx;
          padding: 6rpx 16rpx;
          border-radius: 30rpx;
          margin-left: 16rpx;
          
          &.edit {
            background-color: rgba(255, 255, 255, 0.3);
          }
          
          &.delete {
            background-color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
    
    .discount-card-body {
      padding: 24rpx;
      
      .discount-info-item {
        display: flex;
        margin-bottom: 16rpx;
        
        .info-label {
          width: 160rpx;
          font-size: 28rpx;
          color: #666;
        }
        
        .info-value {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
        
        .discount-icon {
          width: 60rpx;
          height: 60rpx;
        }
        
        .level-tags {
          display: flex;
          flex-wrap: wrap;
          
          .level-tag {
            font-size: 24rpx;
            color: #4A00E0;
            background-color: rgba(74, 0, 224, 0.1);
            padding: 6rpx 16rpx;
            border-radius: 6rpx;
            margin-right: 12rpx;
            margin-bottom: 12rpx;
          }
        }
      }
    }
  }
}

.discount-form-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    border-bottom: 1rpx solid #eee;
    
    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .popup-close {
      font-size: 40rpx;
      color: #999;
    }
  }
  
  .popup-body {
    padding: 24rpx;
    max-height: 60vh;
    overflow-y: auto;
    
    .form-item {
      margin-bottom: 24rpx;
      
      .form-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 12rpx;
      }
      
      .form-input {
        width: 100%;
        height: 80rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
      
      .form-textarea {
        width: 100%;
        height: 160rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        padding: 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
      
      .form-picker {
        width: 100%;
        height: 80rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        
        .picker-value {
          color: #333;
        }
      }
      
      .color-picker {
        display: flex;
        flex-wrap: wrap;
        
        .color-option {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          position: relative;
          
          &.active::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 32rpx;
          }
        }
      }
      
      .icon-upload {
        .preview-icon {
          width: 100rpx;
          height: 100rpx;
          border-radius: 8rpx;
        }
        
        .upload-btn {
          width: 100rpx;
          height: 100rpx;
          border: 1rpx dashed #ddd;
          border-radius: 8rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          
          .upload-icon {
            font-size: 40rpx;
            color: #999;
            margin-bottom: 4rpx;
          }
          
          .upload-text {
            font-size: 20rpx;
            color: #999;
          }
        }
      }
      
      .level-checkboxes {
        display: flex;
        flex-wrap: wrap;
        
        .level-checkbox {
          display: flex;
          align-items: center;
          margin-right: 30rpx;
          margin-bottom: 20rpx;
          
          .checkbox-icon {
            width: 40rpx;
            height: 40rpx;
            border: 1rpx solid #ddd;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10rpx;
            color: #fff;
            font-size: 24rpx;
          }
          
          &.active .checkbox-icon {
            background-color: #4A00E0;
            border-color: #4A00E0;
          }
          
          .checkbox-label {
            font-size: 28rpx;
            color: #333;
          }
        }
      }
    }
  }
  
  .popup-footer {
    display: flex;
    border-top: 1rpx solid #eee;
    
    button {
      flex: 1;
      height: 90rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      border-radius: 0;
      
      &.cancel-btn {
        background-color: #f5f5f5;
        color: #666;
      }
      
      &.confirm-btn {
        background-color: #4A00E0;
        color: #fff;
      }
    }
  }
}
</style>
<!-- 会员折扣页面结束 --> 