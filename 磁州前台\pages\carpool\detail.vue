<template>
  <view class="carpool-detail">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">拼车详情</text>
    </view>
    
    <!-- 添加悬浮推广按钮 -->
    <FloatPromotionButton 
      v-if="hasPromotionPermission || showFloatPromotionButton" 
      @click="showCarpoolPromotion" 
      :position="{right: '30rpx', bottom: '180rpx'}"
      size="100rpx"
    />
    
    <!-- 拼车信息 -->
    <view class="carpool-info">
      <view class="route-info">
        <view class="departure">
          <view class="location-dot departure-dot"></view>
          <text class="location-text">{{carpoolInfo.departure}}</text>
        </view>
        <view class="route-line"></view>
        <view class="destination">
          <view class="location-dot destination-dot"></view>
          <text class="location-text">{{carpoolInfo.destination}}</text>
        </view>
      </view>
      
      <view class="time-info">
        <view class="info-item">
          <text class="item-label">出发时间</text>
          <text class="item-value">{{carpoolInfo.departureTime}}</text>
        </view>
        <view class="info-item">
          <text class="item-label">座位数</text>
          <text class="item-value">{{carpoolInfo.seats}}个</text>
        </view>
        <view class="info-item">
          <text class="item-label">车费</text>
          <text class="item-value price">¥{{carpoolInfo.price}}/人</text>
        </view>
      </view>
      
      <view class="driver-info">
        <image class="driver-avatar" :src="carpoolInfo.driverAvatar" mode="aspectFill"></image>
        <view class="driver-detail">
          <text class="driver-name">{{carpoolInfo.driverName}}</text>
          <text class="driver-car">{{carpoolInfo.carModel}}</text>
        </view>
        <button class="contact-btn" @click="contactDriver">联系司机</button>
      </view>
      
      <view class="note-info" v-if="carpoolInfo.note">
        <text class="note-label">备注</text>
        <text class="note-content">{{carpoolInfo.note}}</text>
      </view>
    </view>
    
    <!-- 操作区域 -->
    <view class="action-area">
      <view class="action-buttons">
        <button class="main-btn" @click="joinCarpool" v-if="!isPublisher">我要上车</button>
        <view class="publisher-actions" v-if="isPublisher">
          <button class="action-btn edit" @click="editCarpool">编辑</button>
          <button class="action-btn cancel" @click="cancelCarpool">取消行程</button>
          <PromotionToolButton 
            v-if="hasPromotionPermission"
            @click="showCarpoolPromotion"
            buttonText="推广"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import PromotionToolButton from '@/components/PromotionToolButton.vue';
import FloatPromotionButton from '@/components/FloatPromotionButton.vue';
import carpoolPromotionMixin from '@/mixins/carpoolPromotionMixin';
// 引入真实API
import api from '@/api/index.js';

// 拼车信息
const carpoolInfo = reactive({
  id: 'cp12345',
  departure: '磁县城区',
  destination: '邯郸火车站',
  departureTime: '2023-07-20 08:00',
  seats: 3,
  price: 15,
  driverName: '张师傅',
  driverAvatar: '/static/images/avatar-default.png',
  carModel: '大众朗逸 - 白色',
  note: '准时出发，不等人，有行李提前说',
  publisherId: 'user123',
  // 为了演示添加推广相关字段
  canDistribute: true
});

// 当前用户ID
const currentUserId = ref('user123'); // 实际应从用户状态中获取

// 是否是发布者
const isPublisher = computed(() => {
  return currentUserId.value === carpoolInfo.publisherId;
});

// 推广相关
const hasPromotionPermission = ref(false);
const promotionData = ref({});
const showFloatPromotionButton = ref(true); // 强制显示悬浮按钮用于测试

// 页面加载
onMounted(() => {
  // 实际应用中，这里应该从服务器获取拼车详情
  loadCarpoolDetail();
  
  // 初始化推广功能
  initPromotion();
});

// 初始化推广功能
const initPromotion = () => {
  // 检查推广权限
  hasPromotionPermission.value = carpoolPromotionMixin.methods.isContentOwner.call({
    $store: {
      state: {
        user: {
          userId: currentUserId.value
        }
      }
    },
    carpoolInfo: carpoolInfo
  }) || carpoolPromotionMixin.methods.isCommissionContent.call({
    carpoolInfo: carpoolInfo
  });
  
  // 生成推广数据
  if (hasPromotionPermission.value) {
    promotionData.value = carpoolPromotionMixin.methods.generatePromotionData.call({
      carpoolInfo: carpoolInfo,
      promotionData: {}
    }).promotionData;
  }
};

// 显示拼车推广
const showCarpoolPromotion = () => {
  carpoolPromotionMixin.methods.showCarpoolPromotion.call({
    hasPromotionPermission: hasPromotionPermission.value,
    openPromotionTools: () => {
      uni.navigateTo({
        url: '/subPackages/promotion/pages/promotion-tool?type=carpool&id=' + carpoolInfo.id
      });
    }
  });
};

// 加载拼车详情 - 使用真实API
const loadCarpoolDetail = async () => {
  try {
    // 获取页面参数中的拼车ID
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const carpoolId = currentPage.options.id;

    if (!carpoolId) {
      uni.showToast({
        title: '拼车信息不存在',
        icon: 'none'
      });
      return;
    }

    // 显示加载状态
    uni.showLoading({
      title: '加载中...'
    });

    // 调用真实API获取拼车详情
    const result = await api.carpool.getDetail(carpoolId);

    if (result.success) {
      // 更新拼车信息
      Object.assign(carpoolInfo, result.data);
      console.log('拼车详情加载成功:', result.data);
    } else {
      uni.showToast({
        title: result.message || '加载失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('加载拼车详情失败:', error);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 加入拼车
const joinCarpool = () => {
  uni.showModal({
    title: '确认上车',
    content: '确定要加入这个拼车行程吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '预约成功',
          icon: 'success'
        });
      }
    }
  });
};

// 编辑拼车
const editCarpool = () => {
  uni.navigateTo({
    url: `/pages/carpool/edit?id=${carpoolInfo.id}`
  });
};

// 取消拼车
const cancelCarpool = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个拼车行程吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '已取消行程',
          icon: 'success'
        });
        
        setTimeout(() => {
          goBack();
        }, 1500);
      }
    }
  });
};

// 联系司机
const contactDriver = () => {
  uni.showActionSheet({
    itemList: ['打电话', '发消息'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 打电话
        uni.makePhoneCall({
          phoneNumber: '13800138000' // 实际应使用真实电话
        });
      } else {
        // 发消息
        uni.navigateTo({
          url: `/pages/chat/index?userId=${carpoolInfo.publisherId}`
        });
      }
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.carpool-detail {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx;
}

.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.carpool-info {
  margin: 20rpx;
  border-radius: 16rpx;
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.route-info {
  padding: 20rpx 0;
}

.departure, .destination {
  display: flex;
  align-items: center;
  margin: 20rpx 0;
}

.location-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.departure-dot {
  background-color: #6B0FBE;
}

.destination-dot {
  background-color: #FF3B30;
}

.route-line {
  width: 2rpx;
  height: 40rpx;
  background-color: #ddd;
  margin-left: 7rpx;
}

.location-text {
  font-size: 32rpx;
  color: #333;
}

.time-info {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.item-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.item-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.price {
  color: #FF3B30;
}

.driver-info {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #eee;
}

.driver-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.driver-detail {
  flex: 1;
}

.driver-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.driver-car {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.contact-btn {
  padding: 10rpx 30rpx;
  background-color: #6B0FBE;
  color: #fff;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.note-info {
  padding: 30rpx 0 10rpx;
}

.note-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.note-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.action-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.main-btn {
  width: 80%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.publisher-actions {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.action-btn {
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.edit {
  background-color: #F5F7FA;
  color: #6B0FBE;
  border: 1px solid #6B0FBE;
}

.cancel {
  background-color: #FFF0F0;
  color: #FF3B30;
  border: 1px solid #FF3B30;
}
</style> 