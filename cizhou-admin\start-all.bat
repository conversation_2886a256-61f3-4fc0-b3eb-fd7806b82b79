@echo off
chcp 65001 >nul

echo 🚀 一键启动磁州生活网后台管理系统（前端+后端）

REM 检查当前目录
if not exist "backend" (
    echo ❌ 请在项目根目录（cizhou-admin）下运行此脚本
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ 请在项目根目录（cizhou-admin）下运行此脚本
    pause
    exit /b 1
)

REM 检查必要的工具
echo 🔍 检查环境依赖...

REM 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装，请先安装Docker
    pause
    exit /b 1
)

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker
    pause
    exit /b 1
)

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装，请先安装Node.js 18+
    pause
    exit /b 1
)

REM 检查Java
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java未安装，请先安装JDK 17+
    pause
    exit /b 1
)

REM 检查Maven
mvn --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Maven未安装，请先安装Maven 3.8+
    pause
    exit /b 1
)

echo ✅ 环境检查通过

REM 启动后端基础服务
echo 📦 启动后端基础服务...
cd backend
call start-dev.bat

if errorlevel 1 (
    echo ❌ 后端服务启动失败
    pause
    exit /b 1
)

REM 等待后端服务完全启动
echo ⏳ 等待后端服务完全启动...
timeout /t 10 /nobreak >nul

REM 检查后端服务状态
echo 🔍 检查后端服务状态...
set /a count=0
:check_backend
set /a count+=1
curl -s http://localhost:8080/actuator/health >nul 2>&1
if not errorlevel 1 (
    echo ✅ 后端服务启动成功
    goto backend_ready
)
if %count% geq 30 (
    echo ❌ 后端服务启动超时
    pause
    exit /b 1
)
echo 等待后端服务启动... (%count%/30)
timeout /t 2 /nobreak >nul
goto check_backend

:backend_ready
REM 启动前端服务
echo 🎨 启动前端服务...
cd ..\frontend

REM 检查前端依赖
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm config set registry https://registry.npmmirror.com/
    npm install
    
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
)

REM 启动前端开发服务器
echo 🚀 启动前端开发服务器...
echo.
echo 🎉 系统启动完成！
echo.
echo 📋 访问地址：
echo    前端管理后台: http://localhost:3000
echo    后端API网关:  http://localhost:8080
echo    Nacos控制台:  http://localhost:8848/nacos (nacos/nacos)
echo.
echo 🔑 默认管理员账号：
echo    用户名: admin
echo    密码:   admin123
echo.
echo 💡 提示：
echo    - 按 Ctrl+C 停止前端服务
echo    - 后端服务将继续在Docker中运行
echo    - 如需停止后端服务，请运行: docker-compose -f backend/docker-compose.dev.yml down
echo.

REM 启动前端（这会阻塞直到用户停止）
npm run dev

echo 👋 前端服务已停止
echo 💡 后端服务仍在运行，如需停止请手动执行：
echo    cd backend && docker-compose -f docker-compose.dev.yml down
pause
