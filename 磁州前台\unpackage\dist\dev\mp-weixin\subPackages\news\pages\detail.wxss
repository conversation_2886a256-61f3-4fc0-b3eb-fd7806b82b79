
.detail-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx; /* 为底部导航栏留出空间 */
		font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Aria<PERSON>, sans-serif;
}
	
	/* 卡片通用样式 */
.card-style {
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		margin: 20rpx 24rpx;
		overflow: hidden;
		position: relative;
}
	
	/* 相关信息区域样式 */
.related-info-section {
		margin: 20rpx 0 30rpx 0;
		background: #fff;
}
.related-info-title {
		display: flex;
		align-items: center;
		padding: 24rpx;
		margin: 0 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
}
.premium-bar {
		width: 6rpx;
		height: 36rpx;
		background: linear-gradient(180deg, #0052CC, #1677FF);
		border-radius: 3rpx;
		margin-right: 16rpx;
}
.related-title-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		letter-spacing: 0.5rpx;
}
	
	/* 顶部区域样式 */
.detail-header {
		padding: 30rpx;
		position: relative;
		overflow: visible;
}
.header-top {
		margin-bottom: 20rpx;
}
.category-badge {
		display: inline-block;
		font-size: 22rpx;
		font-weight: 500;
		color: #0052CC;
		background: rgba(0, 82, 204, 0.1);
		padding: 6rpx 18rpx;
		border-radius: 20rpx;
		margin-bottom: 16rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 82, 204, 0.2);
}
.detail-title {
		font-size: 42rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 26rpx;
		line-height: 1.4;
		letter-spacing: 0.5rpx;
}
.detail-meta {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		color: #888;
		border-top: 1rpx solid rgba(0, 0, 0, 0.05);
		padding-top: 20rpx;
		position: relative;
}
.meta-item {
		display: flex;
		align-items: center;
		margin-right: 24rpx;
		font-size: 24rpx;
}
.meta-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
		opacity: 0.8;
}
	
	/* 导航按钮样式 - 便民信息 */
.home-nav-btn {
		display: none; /* 隐藏旧的导航按钮 */
}
	
	/* 真正的悬浮导航键样式 */
.info-nav-btn {
		position: fixed;
		right: 30rpx;
		top: 150rpx;
		background: #ffffff;
		border-radius: 45rpx;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
		padding: 0;
		width: 80rpx;
		height: 160rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		overflow: visible;
		z-index: 100;
		transition: all 0.3s;
}
.info-nav-btn:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
}
.info-hot-tag {
		background: #ff4d4f;
		color: #fff;
		font-size: 16rpx;
		padding: 4rpx 12rpx;
		position: absolute;
		top: -15rpx;
		right: -20rpx;
		border-radius: 10rpx;
		font-weight: bold;
		transform: rotate(15deg);
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
		z-index: 999;
}
.info-btn-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 22rpx;
		width: 100%;
		height: 100%;
}
.info-icon-group {
		width: 44rpx;
		height: 44rpx;
		display: grid;
		grid-template-columns: 1fr 1fr;
		grid-template-rows: 1fr 1fr;
		gap: 4rpx;
		margin-bottom: 8rpx;
}
.info-icon-item {
		width: 100%;
		height: 100%;
		background-color: #0052CC;
		border-radius: 2rpx;
}
.info-icon-item:first-child {
		background-color: #ff4d4f;
}
.info-nav-text {
		font-size: 20rpx;
		color: #333;
		font-weight: 700;
		line-height: 1.2;
		margin-bottom: 4rpx;
}
	
	/* 内容区域样式 */
.detail-content {
		padding: 30rpx;
}
.content-image-container {
		position: relative;
		margin: 0 -30rpx 30rpx;
		overflow: hidden;
}
.content-image {
		width: 100%;
}
.image-caption {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
		color: #fff;
		font-size: 24rpx;
		padding: 24rpx 20rpx 16rpx;
		text-align: center;
}
.text-container {
		margin-bottom: 30rpx;
}
.content-text {
		font-size: 32rpx;
		color: #333;
		line-height: 1.8;
		white-space: pre-wrap;
		letter-spacing: 0.5rpx;
}
.content-footer {
		display: flex;
		justify-content: space-between;
		padding-top: 30rpx;
		border-top: 1rpx dashed rgba(0, 0, 0, 0.1);
		font-size: 24rpx;
		color: #999;
}
.content-source {
		font-style: italic;
}
.report-text {
		color: #666;
		background: rgba(0, 0, 0, 0.05);
		padding: 6rpx 16rpx;
		border-radius: 20rpx;
}
	
	/* 互动区域样式 */
.detail-stats {
		display: flex;
		align-items: center;
		justify-content: space-around;
		padding: 20rpx 30rpx;
}
.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 14rpx 20rpx;
		flex: 1;
		position: relative;
		transition: transform 0.2s;
}
.stat-item:active {
		transform: scale(0.95);
}
.stat-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 8rpx;
}
.stat-text {
		font-size: 24rpx;
		color: #666;
		font-weight: 500;
}
.stat-text.active {
		color: #0052CC;
		font-weight: 600;
}
.stat-text.active {
		color: #0052CC;
		font-weight: 600;
}
.share-btn {
		background: transparent;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0;
		line-height: 1;
		border: none;
		font-size: 24rpx;
		color: #666;
		font-weight: 500;
}
.share-btn::after {
		display: none;
		border: none;
		background: none;
}
	
	/* 评论区样式 */
.comment-section {
		padding: 30rpx;
}
.section-title {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		position: relative;
		padding-left: 20rpx;
}
.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.comment-count {
		font-size: 26rpx;
		color: #999;
		margin-left: 10rpx;
		position: relative;
		top: 2rpx;
}
.comment-list {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
}
.comment-item {
		display: flex;
		padding-bottom: 24rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.comment-item:last-child {
		padding-bottom: 0;
		border-bottom: none;
}
.comment-avatar {
		width: 72rpx;
		height: 72rpx;
		border-radius: 50%;
		margin-right: 20rpx;
		background-color: #f5f5f5;
		border: 1rpx solid #f0f0f0;
		flex-shrink: 0;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.comment-content {
		flex: 1;
		overflow: hidden;
}
.comment-header {
		display: flex;
		align-items: center;
}
.comment-name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-right: 12rpx;
}
.user-badge {
		background: linear-gradient(to right, #0052CC, #2196F3);
		color: #fff;
		font-size: 20rpx;
		padding: 2rpx 10rpx;
		border-radius: 10rpx;
		font-weight: normal;
		box-shadow: 0 2rpx 6rpx rgba(0, 82, 204, 0.2);
}
.comment-text {
		font-size: 30rpx;
		color: #333;
		line-height: 1.6;
		margin: 12rpx 0;
		word-break: break-all;
}
.comment-footer {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 8rpx 0;
}
.comment-time {
		font-size: 24rpx;
		color: #999;
}
.comment-actions {
		display: flex;
		align-items: center;
}
.action-item {
		display: flex;
		align-items: center;
		margin-left: 30rpx;
		padding: 4rpx 8rpx;
}
.action-icon {
		width: 30rpx;
		height: 30rpx;
		margin-right: 8rpx;
}
.reply-btn text, .like-btn text {
		font-size: 24rpx;
		color: #666;
}
.like-btn text.active {
		color: #0052CC;
}
	
	/* 回复区域 */
.reply-list {
		margin: 20rpx 0 10rpx 0;
		padding: 20rpx;
		background-color: #f8f9fc;
		border-radius: 12rpx;
		border: 1rpx solid rgba(0, 0, 0, 0.03);
}
.reply-item {
		margin-bottom: 16rpx;
		padding-bottom: 16rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.reply-item:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
}
.reply-content {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
}
.reply-name {
		font-size: 26rpx;
		font-weight: bold;
		color: #0052CC;
}
.reply-badge {
		background: linear-gradient(to right, #0052CC, #2196F3);
		color: #fff;
		font-size: 18rpx;
		padding: 0 8rpx;
		border-radius: 8rpx;
		margin: 0 6rpx;
		line-height: 1.5;
}
.reply-to {
		font-size: 26rpx;
		color: #999;
		margin: 0 6rpx;
}
.reply-text {
		font-size: 26rpx;
		color: #333;
		line-height: 1.5;
		word-break: break-all;
}
.reply-footer {
		display: flex;
		justify-content: space-between;
		margin-top: 8rpx;
}
.reply-time {
		font-size: 22rpx;
		color: #999;
}
.view-more-replies {
		font-size: 24rpx;
		color: #0052CC;
		text-align: center;
		padding: 12rpx 0;
		margin-top: 10rpx;
		background: rgba(0, 82, 204, 0.05);
		border-radius: 8rpx;
}
.more-icon {
		display: inline-block;
		transform: scale(0.7);
}
.view-more-comments {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 0;
		margin-top: 10rpx;
		font-size: 28rpx;
		color: #0052CC;
		background: rgba(0, 82, 204, 0.05);
		border-radius: 8rpx;
}
.more-comments-icon {
		width: 24rpx;
		height: 24rpx;
		margin-left: 8rpx;
}
	
	/* 无评论提示 */
.no-comment {
		padding: 60rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
}
.no-comment-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
		opacity: 0.6;
}
.no-comment-text {
		font-size: 28rpx;
		color: #999;
}
	
	/* 评论输入框 */
.comment-input-container {
		padding: 20rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom) / 2);
		margin-bottom: 100rpx; /* 为底部导航栏留出空间 */
}
.comment-input-wrapper {
		display: flex;
		align-items: center;
		background-color: #f5f7fa;
		border-radius: 36rpx;
		padding: 6rpx 10rpx 6rpx 30rpx;
		box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.comment-input {
		flex: 1;
		height: 68rpx;
		font-size: 28rpx;
		color: #333;
		background-color: transparent;
}
.send-btn {
		height: 60rpx;
		line-height: 60rpx;
		background-color: #eaecf0;
		color: #999;
		font-size: 28rpx;
		font-weight: 500;
		text-align: center;
		border-radius: 30rpx;
		padding: 0 24rpx;
		margin-left: 10rpx;
		transition: all 0.2s;
}
.send-btn.active {
		background: linear-gradient(to right, #0052CC, #2196F3);
		color: #fff;
		box-shadow: 0 2rpx 8rpx rgba(0, 82, 204, 0.3);
}
	
	/* 底部导航栏样式 */
.bottom-tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(env(safe-area-inset-bottom) / 2);
		background-color: #ffffff;
		display: flex;
		justify-content: space-around;
		align-items: center;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.08);
		z-index: 100;
		border-top: 1rpx solid rgba(0, 0, 0, 0.05);
		transform: translateY(-5rpx); /* 向上移动一点距离 */
}
.tabbar-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex: 1;
		height: 100%;
		padding: 10rpx 0;
}
.tabbar-icon {
		width: 44rpx;
		height: 44rpx;
		margin-bottom: 6rpx;
}
.tabbar-text {
		font-size: 22rpx;
		color: #7A7E83;
		line-height: 1;
}
	
	/* 回到顶部按钮 */
.back-to-top {
		position: fixed;
		right: 30rpx;
		bottom: 240rpx; /* 增加距离底部的高度 */
		width: 50rpx;
		height: 50rpx;
		background: rgba(255, 255, 255, 0.95);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
		z-index: 90;
		opacity: 0.9;
		transition: all 0.3s;
}
.back-to-top:active {
		transform: scale(0.9);
}
.top-icon {
		width: 40rpx;
		height: 40rpx;
}
	
	/* 全部信息模块样式 */
.all-info {
		margin-bottom: 30rpx;
		padding: 0 0 22rpx 0;
		margin-left: 25rpx;
		margin-right: 25rpx;
		overflow: hidden;
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.05);
}
.all-info-title-row {
		display: flex;
		align-items: center;
		margin: 26rpx 26rpx 20rpx 26rpx;
		padding-top: 10rpx;
		border-bottom: 0.5px solid rgba(230, 233, 240, 0.8);
		padding-bottom: 20rpx;
}
.all-info-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 12rpx;
		vertical-align: middle;
}
.all-info-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		display: inline-block;
		margin: 0;
		letter-spacing: 0.5px;
		text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}
.all-info-tabs-container {
		position: relative;
		width: 100%;
		z-index: 10;
		padding-top: 5rpx;
		padding-bottom: 5rpx;
}
	
	/* 分类标签 */
.all-info-tabs {
		margin: 0 15rpx 22rpx 15rpx;
		padding: 0;
		white-space: nowrap;
		display: flex;
		flex-wrap: nowrap;
		overflow-x: auto;
		-webkit-overflow-scrolling: touch;
		scrollbar-width: none;
		width: auto;
		box-sizing: border-box;
		padding-bottom: 15rpx;
		padding-top: 8rpx;
		background-color: #ffffff;
		z-index: 100;
		transition: all 0.3s ease;
}
	
	/* 固定在顶部的标签样式 */
.fixed-tabs {
		position: fixed;
		top: calc(var(--status-bar-height, 44px) + 44px); /* 确保在导航栏下方 */
		left: 0;
		right: 0;
		margin: 0;
		padding: 15rpx 20rpx 15rpx 30rpx;
		z-index: 98; /* 确保在导航栏下方但高于其他内容 */
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
		border-bottom: 1px solid rgba(235, 238, 245, 0.8);
		background-color: rgba(255, 255, 255, 0.97);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		animation: fadeInDown 0.3s ease;
		width: 100%;
		box-sizing: border-box;
}

	/* 添加一个淡入动画 */
@keyframes fadeInDown {
from {
			opacity: 0;
			transform: translateY(-15px);
}
to {
			opacity: 1;
			transform: translateY(0);
}
}
.all-info-tabs::-webkit-scrollbar {
		display: none;
}
.all-info-tab {
		display: inline-block;
		padding: 12rpx 24rpx;
		margin-right: 12rpx;
		font-size: 26rpx;
		color: #555;
		border-radius: 16rpx;
		background: none;
		vertical-align: middle;
		flex-shrink: 0;
		position: relative;
		z-index: 1;
		line-height: 1.4;
		transition: all 0.2s ease;
}
.all-info-tab:active {
		opacity: 0.7;
		transform: scale(0.96);
		background-color: rgba(0, 0, 0, 0.05);
}
.all-info-tabs .all-info-tab:last-child {
		margin-right: 30rpx;
}
.all-info-tab.active {
		color: #1677ff;
		background: linear-gradient(to bottom, #e6f0ff, #d2e6ff);
		font-weight: 500;
		box-shadow: 0 2rpx 6rpx rgba(22, 119, 255, 0.15);
		transform: translateY(-2rpx);
		transition: all 0.3s ease;
}
.all-info-list {
		padding: 0 22rpx;
		overflow: hidden;
}
.all-info-item {
		background: #ffffff;
		border-radius: 12rpx;
		padding: 26rpx 24rpx 22rpx 24rpx;
		margin-bottom: 22rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}
.info-item-header {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
}
.info-tag {
		font-size: 22rpx;
		color: #1677ff;
		background: rgba(230, 240, 255, 0.7);
		padding: 6rpx 16rpx;
		border-radius: 6rpx;
		margin-right: 12rpx;
		font-weight: 500;
		box-shadow: 0 1rpx 3rpx rgba(22, 119, 255, 0.1);
}
.info-time {
		font-size: 22rpx;
		color: #999;
}
.info-content {
		font-size: 26rpx;
		color: #333;
		margin-bottom: 15rpx;
		line-height: 1.5;
}
.info-meta {
		font-size: 22rpx;
		color: #999;
		display: flex;
		justify-content: space-between;
}
.info-views {
		display: inline-block;
}
.topped-item {
		background: #fefefe;
		border-left: 4rpx solid #ff6b6b;
		box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.08);
}
.premium-topped {
		border-left: 4rpx solid #ffab2b;
		box-shadow: 0 4rpx 12rpx rgba(255, 171, 43, 0.08);
}
.topped-tag {
		color: #ff6b6b;
		background: rgba(255, 107, 107, 0.1);
		box-shadow: 0 1rpx 3rpx rgba(255, 107, 107, 0.1);
}
.topped-badge {
		color: #ff6b6b;
		background: rgba(255, 107, 107, 0.1);
}
.premium-badge {
		color: #ffab2b;
		background: rgba(255, 171, 43, 0.1);
		box-shadow: 0 1rpx 3rpx rgba(255, 171, 43, 0.1);
}
.tabs-placeholder {
		height: 80rpx;
		width: 100%;
}
.fade-in {
		animation: fadeIn 0.5s ease;
}
@keyframes fadeIn {
from {
			opacity: 0;
			transform: translateY(20rpx);
}
to {
			opacity: 1;
			transform: translateY(0);
}
}
.ad-banner {
		margin-bottom: 22rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}
.ad-banner-image {
		width: 100%;
		height: 140rpx;
		border-radius: 12rpx;
}
.tabs-placeholder-container {
		height: 80rpx;
		width: 100%;
}
	
	/* 红包相关样式 */
.red-packet-item {
		background-color: #FFF9F9;
		border-left: 4rpx solid #FF4D4F;
}
.red-packet-tag {
		background-color: #FF4D4F;
		color: #FFFFFF;
}
.red-packet-badge {
		background-color: #FF4D4F;
		color: #FFFFFF;
		font-weight: bold;
		animation: pulse 1.5s infinite;
}
@keyframes pulse {
0% {
			transform: scale(1);
}
50% {
			transform: scale(1.05);
}
100% {
			transform: scale(1);
}
}
.info-meta {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.info-meta-left {
		display: flex;
		align-items: center;
}
.red-packet-info {
		display: flex;
		align-items: center;
		background-color: #FFF0F0;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
}
.red-packet-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 6rpx;
}
.red-packet-amount {
		color: #FF4D4F;
		font-size: 24rpx;
		font-weight: bold;
		margin-right: 6rpx;
}
.red-packet-count {
		color: #FF4D4F;
		font-size: 22rpx;
}
