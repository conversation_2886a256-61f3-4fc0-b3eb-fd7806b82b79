<template>
  <div class="post-detail-container">
    <div class="post-detail-wrapper">
      <!-- 顶部导航栏 -->
      <div class="header-nav">
        <el-button class="back-btn" icon="el-icon-arrow-left" circle @click="goBack"></el-button>
        <div class="actions">
          <el-button type="text" icon="el-icon-share" @click="share">分享</el-button>
          <el-button type="text" icon="el-icon-star-off" @click="favorite">收藏</el-button>
        </div>
      </div>

      <!-- 信息主体卡片 -->
      <div class="content-card main-info">
        <div class="post-header">
          <h1 class="post-title">{{ post.title }}</h1>
          <div class="post-meta">
            <span class="post-time">发布于 {{ formatTime(post.createdAt) }}</span>
            <span class="post-category">{{ post.category }}</span>
            <span class="post-views"><i class="el-icon-view"></i> {{ post.views }}</span>
          </div>
        </div>

        <div class="post-gallery" v-if="post.images && post.images.length > 0">
          <el-carousel :interval="4000" type="card" height="300px" v-if="post.images.length > 1">
            <el-carousel-item v-for="(image, index) in post.images" :key="index">
              <img :src="image" class="carousel-image" @click="previewImage(index)">
            </el-carousel-item>
          </el-carousel>
          <div class="single-image" v-else>
            <img :src="post.images[0]" @click="previewImage(0)">
          </div>
        </div>

        <div class="divider"></div>

        <div class="post-content">
          <p v-html="post.content"></p>
        </div>

        <div class="post-tags" v-if="post.tags && post.tags.length > 0">
          <el-tag v-for="tag in post.tags" :key="tag" size="small" effect="plain" class="tag">{{ tag }}</el-tag>
        </div>
      </div>

      <!-- 发布者信息卡片 -->
      <div class="content-card publisher-info">
        <div class="publisher-header">
          <h3>发布者信息</h3>
        </div>
        <div class="publisher-content">
          <div class="publisher-avatar">
            <el-avatar :size="64" :src="post.publisher?.avatar"></el-avatar>
          </div>
          <div class="publisher-details">
            <div class="publisher-name">{{ post.publisher?.username }}</div>
            <div class="publisher-stats">
              <div class="stat-item">
                <div class="stat-value">{{ post.publisher?.posts || 0 }}</div>
                <div class="stat-label">发布</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ post.publisher?.followers || 0 }}</div>
                <div class="stat-label">粉丝</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ post.publisher?.rating || '5.0' }}</div>
                <div class="stat-label">评分</div>
              </div>
            </div>
          </div>
          <el-button type="primary" class="contact-btn" round>联系TA</el-button>
        </div>
      </div>

      <!-- 位置信息卡片 -->
      <div class="content-card location-info" v-if="post.location">
        <div class="location-header">
          <h3>位置信息</h3>
        </div>
        <div class="location-content">
          <i class="el-icon-location-information"></i>
          <span>{{ post.location.address }}</span>
        </div>
        <div class="location-map">
          <img src="https://via.placeholder.com/600x200?text=Map+Preview" alt="位置地图" class="map-preview">
        </div>
      </div>

      <!-- 相关推荐卡片 -->
      <div class="content-card related-posts">
        <div class="related-header">
          <h3>相关推荐</h3>
        </div>
        <div class="related-content">
          <div class="related-item" v-for="(item, index) in relatedPosts" :key="index" @click="navigateToPost(item.id)">
            <div class="related-image">
              <img :src="item.coverImage" alt="相关信息">
            </div>
            <div class="related-info">
              <div class="related-title">{{ item.title }}</div>
              <div class="related-price" v-if="item.price">¥ {{ item.price }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 评论区卡片 -->
      <div class="content-card comment-section">
        <div class="comment-header">
          <h3>评论区</h3>
        </div>
        <Comment
          :target-id="postId"
          target-type="post"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import Comment from '@/components/Comment.vue'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const route = useRoute()
const router = useRouter()
const postId = ref(route.params.id)

// 模拟数据，实际应从API获取
const post = ref({
  id: postId.value,
  title: '精装修三室两厅，交通便利，拎包入住',
  content: '位于城市中心，周边配套设施齐全，距离地铁站仅500米。房屋为精装修，家具家电齐全，随时可以入住。小区环境优美，安静舒适，24小时保安巡逻，安全有保障。适合一家人居住，看房方便，欢迎随时联系。',
  category: '房屋出租',
  createdAt: new Date(Date.now() - 3600000 * 24 * 3),
  views: 256,
  tags: ['精装修', '地铁附近', '拎包入住', '家电齐全'],
  images: [
    'https://via.placeholder.com/800x450?text=Room+1',
    'https://via.placeholder.com/800x450?text=Room+2',
    'https://via.placeholder.com/800x450?text=Room+3',
  ],
  publisher: {
    username: '房产小能手',
    avatar: 'https://via.placeholder.com/100?text=Avatar',
    posts: 32,
    followers: 128,
    rating: 4.8
  },
  location: {
    address: '某某市某某区某某街123号',
    latitude: 30.123456,
    longitude: 120.123456
  }
})

// 相关推荐数据
const relatedPosts = ref([
  {
    id: 101,
    title: '市中心两室一厅，近地铁',
    coverImage: 'https://via.placeholder.com/150?text=Related+1',
    price: '3500/月'
  },
  {
    id: 102,
    title: '豪华装修大三房，双卫带阳台',
    coverImage: 'https://via.placeholder.com/150?text=Related+2',
    price: '5200/月'
  },
  {
    id: 103,
    title: '单身公寓，精致小巧',
    coverImage: 'https://via.placeholder.com/150?text=Related+3',
    price: '2000/月'
  }
])

// 格式化时间函数
const formatTime = (time) => {
  return formatDistanceToNow(new Date(time), {
    addSuffix: true,
    locale: zhCN
  })
}

// 图片预览
const previewImage = (index) => {
  // 实现图片预览逻辑
  ElMessage.success(`预览图片${index + 1}`)
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 分享
const share = () => {
  ElMessage.success('分享功能待实现')
}

// 收藏
const favorite = () => {
  ElMessage.success('收藏成功')
}

// 跳转到其他信息页面
const navigateToPost = (id) => {
  router.push(`/post/${id}`)
}

onMounted(() => {
  // 加载信息详情数据
  console.log('加载信息详情:', postId.value)
})
</script>

<style scoped>
.post-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 0 0 30px 0;
}

.post-detail-wrapper {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 顶部导航栏 */
.header-nav {
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 5px;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.back-btn {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.actions {
  display: flex;
  gap: 15px;
}

/* 通用卡片样式 */
.content-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  padding: 25px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
}

/* 信息主体卡片 */
.main-info {
  margin-top: 15px;
}

.post-header {
  margin-bottom: 20px;
}

.post-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
}

.post-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  color: #8c8c8c;
  font-size: 14px;
  gap: 15px;
}

.post-gallery {
  margin: 25px 0;
  border-radius: 8px;
  overflow: hidden;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
}

.single-image img {
  width: 100%;
  border-radius: 8px;
  cursor: pointer;
}

.divider {
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
  margin: 20px 0;
}

.post-content {
  font-size: 16px;
  line-height: 1.8;
  color: #444;
  margin-bottom: 20px;
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
}

.tag {
  margin-right: 0;
  border-radius: 4px;
}

/* 发布者信息卡片 */
.publisher-info {
  padding: 20px;
}

.publisher-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.publisher-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.publisher-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.publisher-avatar {
  margin-right: 15px;
}

.publisher-details {
  flex-grow: 1;
}

.publisher-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.publisher-stats {
  display: flex;
  gap: 25px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

.contact-btn {
  padding: 8px 20px;
}

/* 位置信息卡片 */
.location-info {
  padding: 20px;
}

.location-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.location-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.location-content {
  display: flex;
  align-items: center;
  color: #555;
  margin-bottom: 15px;
}

.location-content i {
  color: #409EFF;
  font-size: 18px;
  margin-right: 10px;
}

.location-map {
  border-radius: 8px;
  overflow: hidden;
}

.map-preview {
  width: 100%;
  display: block;
  border-radius: 8px;
}

/* 相关推荐卡片 */
.related-posts {
  padding: 20px;
}

.related-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.related-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.related-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.related-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.related-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.related-image {
  height: 120px;
  overflow: hidden;
}

.related-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-info {
  padding: 10px;
}

.related-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-price {
  font-size: 14px;
  font-weight: 600;
  color: #f56c6c;
}

/* 评论区卡片 */
.comment-section {
  padding: 20px;
}

.comment-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.comment-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 适配平板 */
@media (max-width: 900px) {
  .post-detail-wrapper {
    padding: 0 15px;
  }
  
  .post-title {
    font-size: 22px;
  }
  
  .related-content {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

/* 适配手机 */
@media (max-width: 600px) {
  .post-detail-wrapper {
    padding: 0 10px;
  }
  
  .content-card {
    padding: 15px;
  }
  
  .post-title {
    font-size: 20px;
  }
  
  .publisher-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .publisher-avatar {
    margin-bottom: 15px;
  }
  
  .contact-btn {
    margin-top: 15px;
    width: 100%;
  }
  
  .related-content {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
}
</style> 