"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentTab: "refund",
      // refund, complaint, review
      tabs: [
        { name: "退款管理", value: "refund", count: 2 },
        { name: "投诉处理", value: "complaint", count: 1 },
        { name: "评价管理", value: "review", count: 0 }
      ],
      refundList: [
        {
          id: "4001",
          refundNo: "RF20230510001",
          orderNo: "CZ20230501001",
          status: "pending",
          // pending, approved, rejected, completed
          createTime: "2023-05-10 14:30",
          customerName: "张三",
          customerId: "10086",
          customerAvatar: "/static/images/default-avatar.png",
          amount: "128.00",
          reason: "商品质量不符合预期，水果有部分已经变质。"
        },
        {
          id: "4002",
          refundNo: "RF20230511002",
          orderNo: "CZ20230502003",
          status: "approved",
          createTime: "2023-05-11 09:15",
          customerName: "李四",
          customerId: "10087",
          customerAvatar: "/static/images/default-avatar.png",
          amount: "89.90",
          reason: "配送时间过长，蔬菜已经不新鲜。"
        }
      ],
      complaintList: [
        {
          id: "5001",
          title: "配送员服务态度差",
          orderNo: "CZ20230503005",
          status: "pending",
          // pending, processing, resolved
          createTime: "2023-05-12 16:45",
          customerId: "10088",
          content: "配送员态度非常恶劣，送货上门后直接扔在门口就走了，敲门也不回应。"
        }
      ],
      reviewList: [
        {
          id: "6001",
          customerName: "王五",
          customerAvatar: "/static/images/default-avatar.png",
          rating: 4,
          content: "水果非常新鲜，包装也很精美，就是价格稍微贵了点。下次还会购买。",
          images: [
            "/static/images/review-1.jpg",
            "/static/images/review-2.jpg"
          ],
          createTime: "2023-05-13 10:20",
          orderNo: "CZ20230505008",
          reply: null,
          replyTime: null
        },
        {
          id: "6002",
          customerName: "赵六",
          customerAvatar: "/static/images/default-avatar.png",
          rating: 5,
          content: "蔬菜很新鲜，配送速度也很快，非常满意！",
          images: [],
          createTime: "2023-05-14 15:30",
          orderNo: "CZ20230506010",
          reply: "感谢您的支持，我们会继续努力提供优质服务！",
          replyTime: "2023-05-14 16:05"
        },
        {
          id: "6003",
          customerName: "钱七",
          customerAvatar: "/static/images/default-avatar.png",
          rating: 2,
          content: "送来的水果有几个已经坏了，很失望。",
          images: [
            "/static/images/review-3.jpg"
          ],
          createTime: "2023-05-15 09:40",
          orderNo: "CZ20230507012",
          reply: null,
          replyTime: null
        }
      ],
      replyContent: {}
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    switchTab(tab) {
      this.currentTab = tab;
    },
    viewRefundDetail(id) {
      common_vendor.index.showToast({
        title: "查看退款详情功能开发中",
        icon: "none"
      });
    },
    approveRefund(id) {
      common_vendor.index.showModal({
        title: "确认退款",
        content: "确认同意该退款申请？",
        success: (res) => {
          if (res.confirm) {
            const index = this.refundList.findIndex((item) => item.id === id);
            if (index !== -1) {
              this.refundList[index].status = "approved";
              common_vendor.index.showToast({
                title: "已同意退款",
                icon: "success"
              });
            }
          }
        }
      });
    },
    rejectRefund(id) {
      common_vendor.index.showModal({
        title: "拒绝退款",
        content: "确认拒绝该退款申请？请确保已与客户沟通。",
        success: (res) => {
          if (res.confirm) {
            const index = this.refundList.findIndex((item) => item.id === id);
            if (index !== -1) {
              this.refundList[index].status = "rejected";
              common_vendor.index.showToast({
                title: "已拒绝退款",
                icon: "success"
              });
            }
          }
        }
      });
    },
    viewComplaintDetail(id) {
      common_vendor.index.showToast({
        title: "查看投诉详情功能开发中",
        icon: "none"
      });
    },
    handleComplaint(id) {
      common_vendor.index.showModal({
        title: "处理投诉",
        content: "确认开始处理该投诉？",
        success: (res) => {
          if (res.confirm) {
            const index = this.complaintList.findIndex((item) => item.id === id);
            if (index !== -1) {
              this.complaintList[index].status = "processing";
              common_vendor.index.showToast({
                title: "已开始处理投诉",
                icon: "success"
              });
            }
          }
        }
      });
    },
    contactCustomer(customerId) {
      common_vendor.index.showActionSheet({
        itemList: ["拨打电话", "发送消息"],
        success: (res) => {
          if (res.tapIndex === 0) {
            common_vendor.index.showToast({
              title: "拨打电话功能开发中",
              icon: "none"
            });
          } else if (res.tapIndex === 1) {
            common_vendor.index.showToast({
              title: "消息功能开发中",
              icon: "none"
            });
          }
        }
      });
    },
    previewImage(images, current) {
      common_vendor.index.previewImage({
        urls: images,
        current: images[current]
      });
    },
    submitReply(reviewId) {
      if (!this.replyContent[reviewId]) {
        common_vendor.index.showToast({
          title: "请输入回复内容",
          icon: "none"
        });
        return;
      }
      const index = this.reviewList.findIndex((item) => item.id === reviewId);
      if (index !== -1) {
        this.reviewList[index].reply = this.replyContent[reviewId];
        this.reviewList[index].replyTime = this.getCurrentTime();
        this.replyContent[reviewId] = "";
        common_vendor.index.showToast({
          title: "回复成功",
          icon: "success"
        });
      }
    },
    getCurrentTime() {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const hour = String(now.getHours()).padStart(2, "0");
      const minute = String(now.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}`;
    },
    getRefundStatusText(status) {
      const texts = {
        pending: "待处理",
        approved: "已同意",
        rejected: "已拒绝",
        completed: "已完成"
      };
      return texts[status] || "未知状态";
    },
    getComplaintStatusText(status) {
      const texts = {
        pending: "待处理",
        processing: "处理中",
        resolved: "已解决"
      };
      return texts[status] || "未知状态";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.f($data.tabs, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.name),
        b: tab.count > 0
      }, tab.count > 0 ? {
        c: common_vendor.t(tab.count)
      } : {}, {
        d: index,
        e: $data.currentTab === tab.value ? 1 : "",
        f: common_vendor.o(($event) => $options.switchTab(tab.value), index)
      });
    }),
    c: $data.currentTab === "refund"
  }, $data.currentTab === "refund" ? common_vendor.e({
    d: common_vendor.f($data.refundList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.refundNo),
        b: common_vendor.t($options.getRefundStatusText(item.status)),
        c: common_vendor.n("status-" + item.status),
        d: common_vendor.t(item.orderNo),
        e: common_vendor.t(item.createTime),
        f: item.customerAvatar,
        g: common_vendor.t(item.customerName),
        h: common_vendor.t(item.amount),
        i: common_vendor.t(item.reason),
        j: item.status === "pending"
      }, item.status === "pending" ? {
        k: common_vendor.o(($event) => $options.approveRefund(item.id), index)
      } : {}, {
        l: item.status === "pending"
      }, item.status === "pending" ? {
        m: common_vendor.o(($event) => $options.rejectRefund(item.id), index)
      } : {}, {
        n: ["approved", "rejected"].includes(item.status)
      }, ["approved", "rejected"].includes(item.status) ? {
        o: common_vendor.o(($event) => $options.contactCustomer(item.customerId), index)
      } : {}, {
        p: index,
        q: common_vendor.o(($event) => $options.viewRefundDetail(item.id), index)
      });
    }),
    e: $data.refundList.length === 0
  }, $data.refundList.length === 0 ? {} : {}) : {}, {
    f: $data.currentTab === "complaint"
  }, $data.currentTab === "complaint" ? common_vendor.e({
    g: common_vendor.f($data.complaintList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.title),
        b: common_vendor.t($options.getComplaintStatusText(item.status)),
        c: common_vendor.n("status-" + item.status),
        d: common_vendor.t(item.orderNo),
        e: common_vendor.t(item.createTime),
        f: common_vendor.t(item.content),
        g: item.status === "pending"
      }, item.status === "pending" ? {
        h: common_vendor.o(($event) => $options.handleComplaint(item.id), index)
      } : {}, {
        i: common_vendor.o(($event) => $options.contactCustomer(item.customerId), index),
        j: index,
        k: common_vendor.o(($event) => $options.viewComplaintDetail(item.id), index)
      });
    }),
    h: $data.complaintList.length === 0
  }, $data.complaintList.length === 0 ? {} : {}) : {}, {
    i: $data.currentTab === "review"
  }, $data.currentTab === "review" ? common_vendor.e({
    j: common_vendor.f($data.reviewList, (item, index, i0) => {
      return common_vendor.e({
        a: item.customerAvatar,
        b: common_vendor.t(item.customerName),
        c: common_vendor.f(5, (i, k1, i1) => {
          return {
            a: i,
            b: i <= item.rating ? 1 : ""
          };
        }),
        d: common_vendor.t(item.rating),
        e: common_vendor.t(item.content),
        f: item.images && item.images.length > 0
      }, item.images && item.images.length > 0 ? {
        g: common_vendor.f(item.images, (img, imgIndex, i1) => {
          return {
            a: imgIndex,
            b: img,
            c: common_vendor.o(($event) => $options.previewImage(item.images, imgIndex), imgIndex)
          };
        })
      } : {}, {
        h: common_vendor.t(item.createTime),
        i: common_vendor.t(item.orderNo),
        j: item.reply
      }, item.reply ? {
        k: common_vendor.t(item.reply),
        l: common_vendor.t(item.replyTime)
      } : {
        m: common_vendor.o(($event) => $options.submitReply(item.id), index),
        n: $data.replyContent[item.id],
        o: common_vendor.o(($event) => $data.replyContent[item.id] = $event.detail.value, index),
        p: common_vendor.o(($event) => $options.submitReply(item.id), index)
      }, {
        q: index
      });
    }),
    k: $data.reviewList.length === 0
  }, $data.reviewList.length === 0 ? {} : {}) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-order/pages/orders/after-sale.js.map
