<template>
	<view class="my-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-title">我的</view>
			<view class="navbar-right">
				<!-- 预留位置与发布页面保持一致 -->
			</view>
		</view>
		
		<!-- 用户信息区域 -->
		<view class="user-card" @click="handleUserCardClick">
			<image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'"></image>
			<view class="user-info">
				<text class="user-name">{{ userInfo.nickname || '微信用户' }}</text>
				<text class="user-desc">{{ userInfo.nickname ? '欢迎来到磁州同城' : '点击授权登录，享受更多服务' }}</text>
			</view>
		</view>
		
		<!-- 用户授权弹窗 -->
		<view class="auth-popup" v-if="showAuthPopup" @click="closeAuthPopup">
			<view class="auth-container" @click.stop>
				<view class="auth-header">
					<text class="auth-title">微信授权</text>
					<view class="close-btn" @click="closeAuthPopup">×</view>
				</view>
				<view class="auth-content">
					<image class="auth-logo" src="/static/images/cizhou-logo.png" mode="aspectFit"></image>
					<text class="auth-desc">磁州同城申请获取您的头像和昵称</text>
					
					<!-- 用户授权组件 -->
					<UserAuth 
						buttonText="一键授权" 
						@update:userInfo="onUserInfoUpdated">
						<view class="auth-success">
							<text class="success-icon">✓</text>
							<text class="success-text">授权成功</text>
						</view>
					</UserAuth>
				</view>
			</view>
		</view>
		
		<!-- 我的服务区域 -->
		<view class="section-card">
			<view class="section-header">
				<text class="section-title">我的服务</text>
			</view>
			<view class="service-grid">
				<!-- 第一排 -->
				<view class="service-row">
					<!-- 活动中心 (直接实现而不是使用组件) -->
					<view class="service-item" @click="navigateTo('/subPackages/activity-showcase/pages/index/index')">
						<view class="service-icon-wrap activity-icon-wrap has-badge">
							<view class="activity-icon-bg">
								<svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="32" height="32">
									<path d="M512 85.333c-235.52 0-426.667 191.147-426.667 426.667S276.48 938.667 512 938.667 938.667 747.52 938.667 512 747.52 85.333 512 85.333zm0 768c-188.16 0-341.333-153.173-341.333-341.333S323.84 170.667 512 170.667 853.333 323.84 853.333 512 700.16 853.333 512 853.333z" fill="#FFFFFF"></path>
									<path d="M512 256c-23.68 0-42.667 19.2-42.667 42.667v213.333c0 11.52 4.693 22.187 12.373 29.867l149.333 149.333c16.64 16.64 43.733 16.64 60.373 0 16.64-16.64 16.64-43.733 0-60.373L554.667 494.08V298.667c0-23.467-18.987-42.667-42.667-42.667z" fill="#FFFFFF"></path>
								</svg>
							</view>
							<text class="badge">热门</text>
						</view>
						<text class="service-name">活动中心</text>
					</view>
					
					<!-- 我的福利 -->
					<view class="service-item" @click="navigateTo('/subPackages/user/pages/my-benefits')">
						<view class="service-icon-wrap benefits-icon-wrap has-badge">
							<image class="service-icon" src="/static/images/tabbar/福利.png"></image>
							<text class="badge">6</text>
						</view>
						<text class="service-name">我的福利</text>
					</view>
					
					<!-- 每日签到 (使用新每日签到系统) -->
					<view class="service-item" @click="navigateTo('/subPackages/checkin/pages/points')">
						<view class="service-icon-wrap points-icon-wrap">
							<image class="service-icon" src="/static/images/tabbar/每日签到.png"></image>
						</view>
						<text class="service-name">每日签到</text>
					</view>
					
					<!-- 我的钱包 -->
					<view class="service-item" @click="navigateTo('/subPackages/payment/pages/wallet')">
						<view class="service-icon-wrap">
							<image class="service-icon" src="/static/images/tabbar/我的钱包.png"></image>
						</view>
						<text class="service-name">我的钱包</text>
					</view>
					
					<!-- 个人主页 -->
					<view class="service-item" @click="navigateTo('/pages/user-center/profile')">
						<view class="service-icon-wrap">
							<image class="service-icon" src="/static/images/tabbar/我的主页.png"></image>
						</view>
						<text class="service-name">个人主页</text>
					</view>
				</view>
					
				<!-- 第二排 -->
				<view class="service-row">
					<!-- 返利商城 -->
					<view class="service-item" @click="navigateToCashback">
						<view class="service-icon-wrap cashback-icon-wrap has-badge">
							<image class="service-icon" src="/static/images/tabbar/钱包.png"></image>
							<text class="badge">新</text>
						</view>
						<text class="service-name">返利商城</text>
					</view>
					
					<!-- 我的消息 -->
					<view class="service-item" @click="navigateTo('/subPackages/user/pages/messages')">
						<view class="service-icon-wrap has-badge">
							<image class="service-icon" src="/static/images/tabbar/消息.png"></image>
							<text class="badge">3</text>
						</view>
						<text class="service-name">我的消息</text>
					</view>
				
					<!-- 团购订单 -->
					<view class="service-item" @click="navigateTo('/subPackages/user/pages/group-orders')">
						<view class="service-icon-wrap has-badge">
							<image class="service-icon" src="/static/images/tabbar/团购.png"></image>
							<text class="badge">2</text>
						</view>
						<text class="service-name">团购订单</text>
					</view>
				
					<!-- 我的发布 -->
					<view class="service-item" @click="navigateTo('/pages/my/publish')">
						<view class="service-icon-wrap has-badge">
							<image class="service-icon" src="/static/images/tabbar/发布.png"></image>
							<text class="badge">3</text>
						</view>
						<text class="service-name">我的发布</text>
					</view>
				</view>
				
				<!-- 第三排 -->
				<view class="service-row">
					<!-- 商家后台 -->
					<view class="service-item" @click="navigateTo('/subPackages/merchant-admin-home/pages/merchant-home/index')">
						<view class="service-icon-wrap">
							<image class="service-icon" src="/static/images/tabbar/规则说明.png"></image>
						</view>
						<text class="service-name">商家后台</text>
					</view>

					<!-- 进群服务 -->
					<view class="service-item" @click="navigateTo('/pages/group/group')">
						<view class="service-icon-wrap">
							<image class="service-icon" src="/static/images/tabbar/chat-grey.png"></image>
						</view>
						<text class="service-name">进群</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 我的工具区域 -->
		<view class="section-card">
			<view class="section-header">
				<text class="section-title">我的工具</text>
			</view>
			
			<!-- 收藏/关注 -->
			<view class="tool-item" @click="navigateTo('/subPackages/user/pages/favorites')">
				<view class="tool-left">
					<image class="tool-icon" src="/static/images/tabbar/收藏关注.png"></image>
					<text class="tool-name">收藏/关注</text>
				</view>
				<image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
			</view>
			
			<!-- 接打记录 -->
			<view class="tool-item" @click="navigateTo('/subPackages/user/pages/call-history')">
				<view class="tool-left">
					<image class="tool-icon" src="/static/images/tabbar/拨打记录.png"></image>
					<text class="tool-name">接打记录</text>
				</view>
				<image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
			</view>
			
			<!-- 浏览记录 -->
			<view class="tool-item" @click="navigateTo('/subPackages/user/pages/history')">
				<view class="tool-left">
					<image class="tool-icon" src="/static/images/tabbar/历史记录.png"></image>
					<text class="tool-name">浏览记录</text>
				</view>
				<image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
			</view>
			
			<!-- 我的卡券 -->
			<view class="tool-item" @click="navigateTo('/subPackages/service/pages/coupon')">
				<view class="tool-left">
					<image class="tool-icon" src="/static/images/tabbar/卡券.png"></image>
					<text class="tool-name">我的卡券</text>
				</view>
				<image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
			</view>
			
			<!-- 刷新套餐 -->
			<view class="tool-item" @click="goToRefreshPackage">
				<view class="tool-left">
					<image class="tool-icon" src="/static/images/tabbar/套餐.png"></image>
					<text class="tool-name">刷新套餐</text>
				</view>
				<image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
			</view>
		</view>
		
		<!-- 合作共赢区域 -->
		<view class="section-card">
			<view class="section-header">
				<text class="section-title">合作共赢</text>
			</view>
			<view class="service-grid">
				<view class="service-row">
					<!-- 分销中心 -->
					<view class="service-item" @click="navigateTo('/subPackages/distribution/pages/index')">
						<view class="service-icon-wrap distribution-icon-wrap">
							<image class="service-icon" src="/static/images/distribution/icon-distribution.png"></image>
						</view>
						<text class="service-name">我要赚钱</text>
					</view>
					
					<!-- 合伙人中心 -->
					<view class="service-item" @click="navigateTo('/subPackages/partner/pages/partner')">
						<view class="service-icon-wrap partner-icon-wrap">
							<image class="service-icon" src="/static/images/tabbar/合伙人.png"></image>
						</view>
						<text class="service-name">合伙人</text>
					</view>
					
					<!-- 区域加盟 -->
					<view class="service-item" @click="navigateTo('/subPackages/franchise/pages/index')">
						<view class="service-icon-wrap franchise-icon-wrap">
							<image class="service-icon" src="/static/images/tabbar/加盟.png"></image>
						</view>
						<text class="service-name">区域加盟</text>
					</view>
				</view>
			</view>
		</view>
		

	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
	import UserAuth from '@/components/UserAuth.vue';

// --- 响应式状态 ---
const userInfo = ref({
					nickname: '',
					avatar: ''
});
const showAuthPopup = ref(false);

// --- 方法 ---

// 处理用户卡片点击事件
const handleUserCardClick = () => {
  if (!userInfo.value.nickname) {
    showAuthPopup.value = true;
				} else {
    // 已登录，可以跳转到用户信息编辑页或个人主页
    navigateTo('/pages/user-center/profile');
				}
};
			
			// 关闭授权弹窗
const closeAuthPopup = () => {
  showAuthPopup.value = false;
};
			
			// 用户信息更新回调
const onUserInfoUpdated = (newUserInfo) => {
  userInfo.value = newUserInfo;
  showAuthPopup.value = false; // 授权成功后关闭弹窗
};

// 页面跳转
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 跳转到刷新套餐页面
const goToRefreshPackage = () => {
  console.log('跳转到刷新套餐页面');
  
  // 直接打开页面
  uni.navigateTo({
    url: '/pages/my/refresh-package',
    fail: (err) => {
      console.error('跳转失败:', err);
      // 尝试使用相对路径
      uni.navigateTo({
        url: './refresh-package',
        fail: (err2) => {
          console.error('相对路径跳转失败:', err2);
          uni.showToast({
            title: '页面加载中，请稍后再试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }
  });
};

// 跳转到返利商城
const navigateToCashback = () => {
  console.log('跳转到返利商城');
  
  // 使用redirectTo而不是navigateTo
  uni.redirectTo({
    url: '/subPackages/cashback/pages/index/index',
    fail: (err) => {
      console.error('返利商城跳转失败:', err);
      // 尝试不同的路径格式
      uni.redirectTo({
        url: 'subPackages/cashback/pages/index/index',
        fail: (err2) => {
          console.error('第二次尝试跳转失败:', err2);
          // 最后尝试使用reLaunch
          uni.reLaunch({
            url: '/subPackages/cashback/pages/index/index',
            fail: (err3) => {
              console.error('reLaunch跳转失败:', err3);
              uni.showToast({
                title: '返利商城正在升级中，请稍后再试',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }
      });
    }
  });
};

// 加载本地存储的用户信息
const loadUserInfo = () => {
  const storedUserInfo = uni.getStorageSync('userInfo');
  if (storedUserInfo) {
    userInfo.value = storedUserInfo;
  }
};

// --- 生命周期 ---
onMounted(() => {
  loadUserInfo();
  // 可以在这里获取真实的活动和拼团数量
});
</script>

<style lang="scss">
	.my-container {
		min-height: 100vh;
		background-color: #F5F5F7; /* 更浅的苹果风格背景色 */
		padding-bottom: 30rpx;
		padding-top: 44px; /* 为状态栏预留空间 */
	}
	
	/* 自定义导航栏 */
	.custom-navbar {
		background: linear-gradient(135deg, #0A84FF, #0066FF); /* 更鲜明的苹果蓝渐变 */
		height: 88rpx;
		padding-top: 44px; /* 状态栏高度 */
		display: flex;
		align-items: center;
		position: fixed; /* 固定定位 */
		top: 0;
		left: 0;
		right: 0;
		box-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.2); /* 更明显的阴影 */
		z-index: 100;
		backdrop-filter: blur(10px); /* 苹果风格模糊效果 */
		-webkit-backdrop-filter: blur(10px);
	}
	
	.navbar-title {
		flex: 1;
		text-align: center;
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: 600;
		letter-spacing: 1px; /* 增加字间距 */
		position: absolute;
		left: 0;
		right: 0;
		margin: 0 auto;
		text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* 文字阴影 */
	}
	
	/* 用户信息卡片 */
	.user-card {
		background: linear-gradient(135deg, #FFFFFF, #F8F8FA); /* 微妙的渐变背景 */
		margin: 150rpx 24rpx 30rpx; /* 增加顶部边距，为固定导航栏留出空间 */
		padding: 40rpx 36rpx; /* 增加内边距 */
		display: flex;
		align-items: center;
		border-radius: 32rpx; /* 更大圆角 */
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 
		            0 2rpx 4rpx rgba(0, 0, 0, 0.03); /* 双层阴影增强立体感 */
		position: relative;
		overflow: hidden;
		border: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */
	}
	
	/* 用户卡片装饰元素 */
	.user-card::before {
		content: '';
		position: absolute;
		top: -80rpx;
		right: -80rpx;
		width: 200rpx;
		height: 200rpx;
		background: linear-gradient(135deg, rgba(10, 132, 255, 0.05), rgba(10, 132, 255, 0.1));
		border-radius: 100rpx;
		z-index: 0;
	}
	
	.avatar {
		width: 128rpx;
		height: 128rpx;
		border-radius: 64rpx; /* 完美圆形 */
		background-color: #f0f0f0;
		border: 3rpx solid #FFFFFF;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12); /* 增强阴影 */
		position: relative;
		z-index: 1;
	}
	
	.user-info {
		margin-left: 28rpx; /* 增大间距 */
		display: flex;
		flex-direction: column;
		position: relative;
		z-index: 1;
	}
	
	.user-name {
		font-size: 36rpx; /* 增大字体 */
		font-weight: 600; /* 增强字重 */
		color: #1A1A1A; /* 苹果深灰色 */
		margin-bottom: 10rpx;
		letter-spacing: 0.5px;
	}
	
	.user-desc {
		font-size: 26rpx;
		color: #8E8E93; /* 苹果次要文本颜色 */
		letter-spacing: 0.3px;
	}
	
	/* 区域卡片样式 */
	.section-card {
		background-color: #FFFFFF;
		border-radius: 32rpx; /* 更大圆角 */
		margin: 24rpx;
		padding: 28rpx 24rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06), 
		            0 1rpx 4rpx rgba(0, 0, 0, 0.03); /* 双层阴影增强立体感 */
		position: relative;
		overflow: hidden;
		border: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */
		transition: transform 0.2s, box-shadow 0.2s;
	}
	
	.section-card:active {
		transform: scale(0.99);
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.04);
	}
	
	.section-header {
		padding: 10rpx 16rpx 20rpx;
		border-bottom: 0.5px solid rgba(60, 60, 67, 0.08); /* 更薄的分隔线 */
		margin-bottom: 16rpx;
		position: relative;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: 600; /* 苹果风格中等字重 */
		color: #1A1A1A; /* 苹果深灰色 */
		position: relative;
		padding-left: 20rpx;
		letter-spacing: 0.5px;
	}
	
	.section-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 4rpx;
		height: 24rpx;
		background: linear-gradient(to bottom, #0A84FF, #0066FF); /* 渐变色装饰线 */
		border-radius: 2rpx;
	}
	
	/* 服务网格样式 */
	.service-grid {
		display: flex;
		flex-direction: column;
		padding: 24rpx 10rpx 10rpx;
	}
	
	.service-row {
		display: flex;
		justify-content: flex-start;
		width: 100%;
		margin-bottom: 36rpx; /* 增大行间距 */
	}
	
	.service-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 15rpx;
	}
	
	.service-icon-wrap {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin-bottom: 12rpx;
		
		&.has-badge {
			.badge {
				position: absolute;
				top: -10rpx;
				right: -10rpx;
				background-color: #FF3B30;
				color: #FFFFFF;
				font-size: 20rpx;
				padding: 2rpx 8rpx;
				border-radius: 10rpx;
				transform: scale(0.8);
			}
		}
		
		&.activity-icon-wrap {
			.activity-icon-bg {
				width: 80rpx;
				height: 80rpx;
				border-radius: 20rpx;
				background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);
				display: flex;
				justify-content: center;
				align-items: center;
				box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.3);
	}
		}
		
		&.benefits-icon-wrap {
			background: linear-gradient(135deg, #FF9500 0%, #FF5E3A 100%);
			box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.3);
	}
	
		&.points-icon-wrap {
			background: linear-gradient(135deg, #4CD964 0%, #34C759 100%);
			box-shadow: 0 4rpx 8rpx rgba(76, 217, 100, 0.3);
	}
	
		&.cashback-icon-wrap {
			background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
			box-shadow: 0 4rpx 8rpx rgba(0, 122, 255, 0.3);
	}
	
		&.distribution-icon-wrap {
			background: linear-gradient(135deg, #FF2D55 0%, #FF3B30 100%);
			box-shadow: 0 4rpx 8rpx rgba(255, 45, 85, 0.3);
	}
	
		&.partner-icon-wrap {
			background: linear-gradient(135deg, #5856D6 0%, #AF52DE 100%);
			box-shadow: 0 4rpx 8rpx rgba(88, 86, 214, 0.3);
	}
	
		&.franchise-icon-wrap {
			background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
			box-shadow: 0 4rpx 8rpx rgba(255, 204, 0, 0.3);
		}
	}
	
	.has-badge .badge {
		position: absolute;
		top: -6rpx;
		right: -6rpx;
		min-width: 36rpx;
		height: 36rpx;
		padding: 0 8rpx;
		background-color: #FF3B30; /* 苹果红色 */
		color: #FFFFFF;
		font-size: 20rpx;
		font-weight: 600;
		border-radius: 18rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 6rpx rgba(255, 59, 48, 0.3); /* 徽章阴影 */
		border: 1px solid #FFFFFF; /* 白色边框 */
	}
	
	.service-icon {
		width: 52rpx;
		height: 52rpx;
		filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1)); /* SVG图标阴影 */
	}
	
	.service-name {
		font-size: 26rpx;
		color: #4D4D4D;
		margin-top: 4rpx;
		font-weight: 500; /* 稍微加粗 */
	}
	
	/* 工具项样式 */
	.tool-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 28rpx;
		margin: 8rpx 16rpx;
		border-radius: 24rpx; /* 增大圆角 */
		transition: all 0.2s ease;
		position: relative;
	}
	
	.tool-item:active {
		background-color: rgba(0, 0, 0, 0.04); /* 点击时的背景变化 */
		transform: scale(0.99);
	}
	
	.tool-item:not(:last-child)::after {
		content: '';
		position: absolute;
		left: 80rpx;
		right: 0;
		bottom: 0;
		height: 0.5px;
		background-color: rgba(60, 60, 67, 0.08); /* 苹果风格分隔线 */
	}
	
	.tool-left {
		display: flex;
		align-items: center;
	}
	
	.tool-icon {
		width: 44rpx;
		height: 44rpx;
		margin-right: 24rpx;
		background: linear-gradient(135deg, #F0F6FF, #E6F0FF); /* 渐变背景 */
		border-radius: 16rpx; /* 增大圆角 */
		padding: 12rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05); /* 微妙的阴影 */
	}
	
	.tool-name {
		font-size: 30rpx;
		color: #1A1A1A; /* 苹果深灰色 */
		font-weight: 500; /* 稍微加粗 */
	}
	
	.tool-badge {
		padding: 6rpx 14rpx;
		background-color: #FF3B30;
		color: #fff;
		font-size: 20rpx;
		font-weight: 600;
		border-radius: 20rpx;
		margin-right: 16rpx;
		box-shadow: 0 2rpx 6rpx rgba(255, 59, 48, 0.2); /* 徽章阴影 */
	}
	
	.arrow-icon {
		width: 28rpx;
		height: 28rpx;
		transform: rotate(90deg);
		opacity: 0.3;
	}
	
	/* 授权弹窗样式 */
	.auth-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6); /* 更深的半透明背景 */
		z-index: 999;
		display: flex;
		justify-content: center;
		align-items: center;
		backdrop-filter: blur(10px); /* 苹果风格模糊背景 */
		-webkit-backdrop-filter: blur(10px);
	}
	
	.auth-container {
		width: 600rpx;
		background-color: rgba(255, 255, 255, 0.95); /* 半透明背景 */
		border-radius: 32rpx; /* 更大圆角 */
		overflow: hidden;
		box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.15); /* 明显的卡片阴影 */
		border: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */
		animation: authPopIn 0.3s ease-out; /* 弹出动画 */
	}
	
	@keyframes authPopIn {
		from {
			opacity: 0;
			transform: scale(0.9);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}
	
	.auth-header {
		position: relative;
		padding: 32rpx;
		border-bottom: 0.5px solid rgba(60, 60, 67, 0.1); /* 苹果风格分隔线 */
		text-align: center;
	}
	
	.auth-title {
		font-size: 34rpx;
		font-weight: 600; /* 苹果字重 */
		color: #1A1A1A; /* 苹果深灰色 */
		letter-spacing: 0.5px;
	}
	
	.close-btn {
		position: absolute;
		right: 20rpx;
		top: 20rpx;
		width: 44rpx;
		height: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
		color: #8E8E93; /* 苹果次要文本颜色 */
		border-radius: 22rpx;
		background-color: rgba(60, 60, 67, 0.05);
		transition: all 0.2s ease;
	}
	
	.close-btn:active {
		background-color: rgba(60, 60, 67, 0.1); /* 点击效果 */
		transform: scale(0.95);
	}
	
	.auth-content {
		padding: 48rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.auth-logo {
		width: 128rpx;
		height: 128rpx;
		margin-bottom: 36rpx;
		border-radius: 64rpx;
		background-color: #f0f0f0;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12); /* 增强阴影 */
		border: 3rpx solid #FFFFFF;
	}
	
	.auth-desc {
		font-size: 30rpx;
		color: #666666;
		margin-bottom: 48rpx;
		text-align: center;
		line-height: 1.5;
	}
	
	.auth-success {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 24rpx;
	}
	
	.success-icon {
		font-size: 64rpx;
		color: #34C759; /* 苹果绿色 */
		margin-bottom: 20rpx;
		text-shadow: 0 2rpx 6rpx rgba(52, 199, 89, 0.2); /* 文字阴影 */
	}
	
	.success-text {
		font-size: 32rpx;
		color: #34C759; /* 苹果绿色 */
		font-weight: 600; /* 苹果字重 */
		letter-spacing: 0.5px;
	}
	
	/* 功能入口区样式 */
	.function-section {
		background-color: #FFFFFF;
		border-radius: 32rpx; /* 更大圆角 */
		margin: 24rpx;
		padding: 28rpx 24rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06),
		            0 1rpx 4rpx rgba(0, 0, 0, 0.03); /* 双层阴影增强立体感 */
		position: relative;
		overflow: hidden;
		border: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */
	}
	
	.function-row {
		display: flex;
		justify-content: flex-start;
		width: 100%;
		margin-bottom: 24rpx;
	}
	
	.function-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.function-icon-wrap {
		position: relative;
		width: 96rpx;
		height: 96rpx;
		margin-bottom: 16rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(135deg, #F0F6FF, #E6F0FF); /* 渐变背景 */
		border-radius: 48rpx; /* 完全圆形 */
		transition: all 0.25s ease;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05),
		            0 1rpx 3rpx rgba(0, 0, 0, 0.03); /* 双层阴影 */
		border: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */
	}
	
	.function-icon-wrap:active {
		transform: scale(0.92); /* 点击时的缩放效果 */
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
	}
	
	.function-icon {
		width: 52rpx;
		height: 52rpx;
		filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1)); /* SVG图标阴影 */
	}
	
	.function-name {
		font-size: 26rpx;
		color: #4D4D4D;
		font-weight: 500; /* 稍微加粗 */
	}
	
	/* 新商家后台入口样式 */
	.menu-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 28rpx;
		margin: 8rpx 16rpx;
		border-radius: 24rpx; /* 增大圆角 */
		transition: all 0.2s ease;
		position: relative;
	}
	
	.menu-item:active {
		background-color: rgba(0, 0, 0, 0.04); /* 点击时的背景变化 */
		transform: scale(0.99);
	}
	
	.menu-item:not(:last-child)::after {
		content: '';
		position: absolute;
		left: 80rpx;
		right: 0;
		bottom: 0;
		height: 0.5px;
		background-color: rgba(60, 60, 67, 0.08); /* 苹果风格分隔线 */
	}
	
	.menu-icon {
		width: 44rpx;
		height: 44rpx;
		margin-right: 24rpx;
		background: linear-gradient(135deg, #F0F6FF, #E6F0FF); /* 渐变背景 */
		border-radius: 16rpx; /* 增大圆角 */
		padding: 12rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05); /* 微妙的阴影 */
	}
	
	.menu-text {
		font-size: 30rpx;
		color: #1A1A1A; /* 苹果深灰色 */
		font-weight: 500; /* 稍微加粗 */
	}
	
	.menu-badge {
		font-size: 20rpx;
		color: #FF3B30; /* 苹果红色 */
		font-weight: 600;
		margin-left: 8rpx;
		padding: 4rpx 12rpx;
		background-color: rgba(255, 59, 48, 0.1);
		border-radius: 10rpx;
	}
	
	.menu-arrow {
		width: 28rpx;
		height: 28rpx;
		transform: rotate(90deg);
		opacity: 0.3;
	}
</style> 
