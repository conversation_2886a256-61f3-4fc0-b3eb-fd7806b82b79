<template>
  <view class="post-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/icons/back-white.svg" class="back-icon"></image>
      </view>
      <view class="navbar-title">信息详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <view class="post-detail-wrapper">
    <!-- 顶部导航栏 -->
      <view class="header-nav" style="display: none;">
        <view class="back-btn" @click="goBack">
          <text class="iconfont icon-left"></text>
        </view>
        <view class="actions">
          <!-- 这里可以添加其他操作按钮 -->
        </view>
      </view>

      <!-- 隐藏的分享触发按钮 -->
      <button id="shareButton" class="share-trigger" open-type="share" style="position: absolute; opacity: 0; width: 1px; height: 1px; overflow: hidden; left: -100px;"></button>

      <!-- 信息主体卡片 -->
      <view class="content-card main-info">
        <view class="post-header">
          <view class="post-title">{{ post.title }}</view>
          <view class="post-meta">
            <text class="post-time">发布于 {{ formatTime(post.createdAt) }}</text>
            <text class="post-category">{{ post.category }}</text>
            <view class="post-views">
              <text class="iconfont icon-eye"></text>
              <text>{{ post.views }}</text>
            </view>
          </view>
        </view>

        <view class="post-gallery" v-if="post.images && post.images.length > 0">
          <swiper class="swiper" 
                indicator-dots 
                :autoplay="true" 
                :interval="4000" 
                :duration="500" 
                circular
                v-if="post.images.length > 1">
            <swiper-item v-for="(image, index) in post.images" :key="index" @click="previewImage(index)">
              <image :src="image" class="carousel-image" mode="aspectFill"></image>
          </swiper-item>
        </swiper>
          <view class="single-image" v-else>
            <image :src="post.images[0]" @click="previewImage(0)" mode="widthFix"></image>
          </view>
        </view>

        <view class="divider"></view>

        <view class="post-content">
          <rich-text :nodes="post.content"></rich-text>
        </view>

        <view class="post-tags" v-if="post.tags && post.tags.length > 0">
          <uni-tag v-for="tag in post.tags" :key="tag" :text="tag" size="small" type="default" :inverted="true" class="tag"></uni-tag>
        </view>
      </view>

      <!-- 发布者信息卡片 -->
      <view class="content-card publisher-info">
        <view class="publisher-header">
          <text class="card-title">发布者信息</text>
        </view>
        <view class="publisher-content">
          <view class="publisher-avatar">
            <image class="avatar-image" :src="post.publisher?.avatar" mode="aspectFill"></image>
          </view>
          <view class="publisher-details">
            <view class="publisher-name">{{ post.publisher?.username }}</view>
            <view class="publisher-stats">
              <view class="stat-item">
                <view class="stat-value">{{ post.publisher?.posts || 0 }}</view>
                <view class="stat-label">发布</view>
              </view>
              <view class="stat-item">
                <view class="stat-value">{{ post.publisher?.followers || 0 }}</view>
                <view class="stat-label">粉丝</view>
              </view>
              <view class="stat-item">
                <view class="stat-value">{{ post.publisher?.rating || '5.0' }}</view>
                <view class="stat-label">评分</view>
              </view>
            </view>
          </view>
          <button class="contact-btn" type="primary" size="mini" @click="contactPublisher">联系TA</button>
        </view>
      </view>

      <!-- 位置信息卡片 -->
      <view class="content-card location-info" v-if="post.location">
        <view class="location-header">
          <text class="card-title">位置信息</text>
        </view>
        <view class="location-content">
          <text class="iconfont icon-location"></text>
          <text>{{ post.location.address }}</text>
        </view>
        <view class="location-map">
          <image src="https://via.placeholder.com/600x200?text=Map+Preview" mode="widthFix" class="map-preview"></image>
        </view>
      </view>

      <!-- 相关推荐卡片 -->
      <view class="content-card related-posts">
        <view class="related-header">
          <text class="card-title">相关推荐</text>
        </view>
        <view class="related-content">
          <view class="related-item" v-for="(item, index) in relatedPosts" :key="index" @click="navigateToPost(item.id)">
            <view class="related-image">
              <image :src="item.coverImage" mode="aspectFill"></image>
            </view>
            <view class="related-info">
              <view class="related-title">{{ item.title }}</view>
              <view class="related-price" v-if="item.price">¥ {{ item.price }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 评论区卡片 -->
      <view class="content-card comment-section">
        <view class="comment-header">
          <text class="card-title">评论区</text>
        </view>
        <!-- 评论组件，可以自定义或使用uni-app插件 -->
        <view class="comment-list">
          <view class="comment-empty" v-if="comments.length === 0">
            <text>暂无评论，快来发表第一条评论吧！</text>
          </view>
          <view class="comment-item" v-for="(comment, index) in comments" :key="index">
            <view class="comment-user">
              <image class="comment-avatar" :src="comment.avatar" mode="aspectFill"></image>
              <view class="comment-user-info">
                <text class="comment-username">{{ comment.username }}</text>
                <text class="comment-time">{{ formatTime(comment.createdAt) }}</text>
              </view>
            </view>
            <view class="comment-content">{{ comment.content }}</view>
          </view>
        </view>
        <view class="comment-input-area">
          <input class="comment-input" type="text" placeholder="写下你的评论..." v-model="commentText" confirm-type="send" @confirm="submitComment" />
          <button class="comment-submit" type="primary" size="mini" @click="submitComment">发送</button>
      </view>
      </view>
    </view>
    
    <!-- 底部互动工具栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="makePhoneCall">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
    
    <!-- 分享提示 -->
    <view class="share-tip" v-if="showShareTip">
      <text class="tip-text">分享此信息可获得置顶特权</text>
      <view class="tip-close" @click="closeShareTip">×</view>
    </view>

    <!-- 添加分享成功奖励弹窗 -->
    <view class="share-reward-overlay" v-if="showShareReward" @click="hideShareReward">
      <view class="share-reward-card" @click.stop>
        <view class="reward-close" @click="hideShareReward">×</view>
        <view class="reward-title">分享成功</view>
        <view class="reward-icon">🎁</view>
        <view class="reward-message">恭喜获得信息置顶特权</view>
        <view class="reward-desc">您的信息将在首页获得更多曝光</view>
        <view class="reward-btn" @click="hideShareReward">太棒了</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { checkText } from '@/utils/contentCheck.js'

// 响应式数据
const postId = ref('')
const commentText = ref('')
const comments = ref([])
const shouldAutoShare = ref(false)
const post = ref({
  id: '',
  title: '精装修三室两厅，交通便利，拎包入住',
  content: '位于城市中心，周边配套设施齐全，距离地铁站仅500米。房屋为精装修，家具家电齐全，随时可以入住。小区环境优美，安静舒适，24小时保安巡逻，安全有保障。适合一家人居住，看房方便，欢迎随时联系。',
  category: '房屋出租',
  createdAt: Date.now() - 3600000 * 24 * 3,
  views: 256,
  tags: ['精装修', '地铁附近', '拎包入住', '家电齐全'],
  images: [
    'https://via.placeholder.com/800x450?text=Room+1',
    'https://via.placeholder.com/800x450?text=Room+2',
    'https://via.placeholder.com/800x450?text=Room+3',
  ],
  publisher: {
    username: '房产小能手',
    avatar: 'https://via.placeholder.com/100?text=Avatar',
    posts: 32,
    followers: 128,
    rating: 4.8
  },
  location: {
    address: '某某市某某区某某街123号',
    latitude: 30.123456,
    longitude: 120.123456
  }
})

const relatedPosts = ref([
  {
    id: 101,
    title: '市中心两室一厅，近地铁',
    coverImage: 'https://via.placeholder.com/150?text=Related+1',
    price: '3500/月'
  },
  {
    id: 102,
    title: '豪华装修大三房，双卫带阳台',
    coverImage: 'https://via.placeholder.com/150?text=Related+2',
    price: '5200/月'
  },
  {
    id: 103,
    title: '单身公寓，精致小巧',
    coverImage: 'https://via.placeholder.com/150?text=Related+3',
    price: '2000/月'
  }
])

// 添加控制变量
const showShareGuide = ref(false)
const showShareArrow = ref(false)
const btnHighlight = ref(false)
const showShareTip = ref(false)
const isLiked = ref(false)
const isCollected = ref(false)

// 分享奖励控制变量
const showShareReward = ref(false)

// 生命周期钩子
onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.$page?.options || {}
  postId.value = options.id || '1'
  post.value.id = postId.value
  console.log('加载信息详情:', postId.value)
  
  // 检查是否需要自动分享
  if (options.autoShare === 'true' || uni.getStorageSync('autoShare')) {
    shouldAutoShare.value = true
    // 清除标记，避免重复触发
    uni.removeStorageSync('autoShare')
    // 页面加载后显示分享提示
    nextTick(() => {
      showSimpleShareTip()
    })
  }
  
  // 模拟评论数据
  comments.value = [
    {
      id: 1,
      username: '用户123',
      avatar: 'https://via.placeholder.com/40?text=U1',
      content: '房子位置很好，周边设施齐全，交通便利。',
      createdAt: Date.now() - 3600000 * 48
    },
    {
      id: 2,
      username: '小明',
      avatar: 'https://via.placeholder.com/40?text=U2',
      content: '请问有停车位吗？',
      createdAt: Date.now() - 3600000 * 24
    }
  ]
})

// 修改为更简单的分享提示
const showSimpleShareTip = () => {
  console.log('显示简单分享提示')
  
  // 显示顶部提示条
  showShareTip.value = true
  
  // 设置5秒后自动关闭
  setTimeout(() => {
    showShareTip.value = false
  }, 5000)
}

// 隐藏分享提示
const closeShareTip = () => {
  showShareTip.value = false
}

// 点赞功能
const toggleLike = () => {
  isLiked.value = !isLiked.value
  uni.showToast({
    title: isLiked.value ? '点赞成功' : '已取消点赞',
    icon: 'none'
  })
}

// 收藏功能
const toggleCollect = () => {
  isCollected.value = !isCollected.value
  uni.showToast({
    title: isCollected.value ? '收藏成功' : '已取消收藏',
    icon: 'none'
  })
}

// 方法
const formatTime = (time) => {
  const now = Date.now()
  const diff = now - time
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return Math.floor(diff / 60000) + '分钟前'
  } else if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前'
  } else if (diff < 2592000000) {
    return Math.floor(diff / 86400000) + '天前'
  } else if (diff < 31536000000) {
    return Math.floor(diff / 2592000000) + '个月前'
  } else {
    return Math.floor(diff / 31536000000) + '年前'
  }
}

const previewImage = (index) => {
  uni.previewImage({
    current: index,
    urls: post.value.images
  })
}

const goBack = () => {
  uni.navigateBack()
}

const contactPublisher = () => {
  uni.showToast({
    title: '即将跳转到聊天页面',
    icon: 'none'
  })
}

const navigateToPost = (id) => {
  uni.navigateTo({
    url: `/pages/publish/info-detail?id=${id}`
  })
}

// 修改提交评论方法
async function submitComment() {
  if (!commentText.trim()) {
    uni.showToast({
      title: '请输入评论内容',
      icon: 'none'
    });
    return;
  }
  
  // 显示加载中
  uni.showLoading({
    title: '提交中...'
  });
  
  try {
    // 内容审核
    const checkResult = await checkText(commentText);
    
    // 审核未通过
    if (!checkResult.pass) {
      uni.hideLoading();
      uni.showModal({
        title: '提示',
        content: `评论内容审核未通过：${checkResult.message}`,
        showCancel: false
      });
      return;
    }
    
    // 审核通过，添加评论
    // 模拟添加评论
    const newComment = {
      id: Date.now().toString(),
      username: '用户' + Math.floor(Math.random() * 1000),
      avatar: '/static/images/avatar.jpg',
      content: commentText,
      createdAt: new Date().toISOString()
    };
    
    // 将新评论添加到评论列表
    comments.value.unshift(newComment);
    
    // 清空评论文本
    commentText = '';
    
    uni.hideLoading();
    uni.showToast({
      title: '评论成功',
      icon: 'success'
    });
  } catch (error) {
    console.error('评论失败:', error);
    uni.hideLoading();
    uni.showToast({
      title: '评论失败，请重试',
      icon: 'none'
    });
  }
}

// 隐藏分享奖励弹窗
const hideShareReward = () => {
  showShareReward.value = false
}

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打电话
const makePhoneCall = () => {
  // 判断是否有联系电话
  if (post.value.publisher && post.value.publisher.phone) {
    uni.makePhoneCall({
      phoneNumber: post.value.publisher.phone,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败:', err);
        uni.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  } else {
    // 如果没有电话，提示用户
    uni.showModal({
      title: '提示',
      content: '该信息暂无联系电话，请通过私信联系发布者',
      showCancel: false
    });
  }
};

// 打开私信聊天
const openChat = () => {
  if (!post.value.publisher || !post.value.publisher.id) {
    uni.showToast({
      title: '无法获取发布者信息',
      icon: 'none'
    });
    return;
  }
  
  // 这里可以根据实际情况跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/index?userId=${post.value.publisher.id}&username=${encodeURIComponent(post.value.publisher.username || '用户')}`
  });
};

// 定义页面分享行为
defineExpose({
  onShareAppMessage() {
    // 准备分享数据
    console.log('触发onShareAppMessage，准备分享数据')
    
    // 自动采集分享标题
    let shareTitle = ''
    if (post.value.title) {
      shareTitle = post.value.title
    } else if (post.value.content) {
      // 使用内容的前20个字符作为标题
      shareTitle = post.value.content.substring(0, 20) + (post.value.content.length > 20 ? '...' : '')
    } else {
      shareTitle = '磁州同城信息'
    }
    
    // 自动采集分享图片
    let shareImageUrl = ''
    if (post.value.images && post.value.images.length > 0) {
      shareImageUrl = post.value.images[0] // 使用第一张图片作为分享图片
    }
    
    // 构建分享路径，确保分享出去后能正确打开
    const sharePath = `/pages/publish/info-detail?id=${post.value.id}`
    
    console.log('分享数据：', {
      title: shareTitle,
      path: sharePath,
      imageUrl: shareImageUrl
    })
    
    return {
      title: shareTitle,
      path: sharePath,
      imageUrl: shareImageUrl,
      success: function() {
        // 显示分享成功提示
        uni.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 1500
        })
        
        // 标记已分享
        uni.setStorageSync('hasShared', true)
        
        // 隐藏分享提示
        showShareTip.value = false
        
        // 显示分享奖励弹窗
        setTimeout(() => {
          showShareReward.value = true
          
          // 记录获得置顶奖励
          uni.setStorageSync('hasTopReward', true)
        }, 1000)
      },
      fail: function(err) {
        console.error('分享失败', err)
      }
    }
  },
  
  // 分享到朋友圈
  onShareTimeline() {
    console.log('触发分享到朋友圈')
    
    // 自动采集分享标题
    let shareTitle = ''
    if (post.value.title) {
      shareTitle = post.value.title
    } else if (post.value.content) {
      // 使用内容的前20个字符作为标题
      shareTitle = post.value.content.substring(0, 20) + (post.value.content.length > 20 ? '...' : '')
    } else {
      shareTitle = '磁州同城信息'
    }
    
    // 自动采集分享图片
    let shareImageUrl = ''
    if (post.value.images && post.value.images.length > 0) {
      shareImageUrl = post.value.images[0] // 使用第一张图片作为分享图片
    }
    
    return {
      title: shareTitle,
      query: `id=${post.value.id}`,
      imageUrl: shareImageUrl
    }
  }
})
</script>

<style>
/* 全局样式 */
.post-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 130rpx;
  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

.post-detail-wrapper {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 顶部导航栏 */
.header-nav {
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 10rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.actions {
  display: flex;
  gap: 30rpx;
}

.action-btn {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 28rpx;
  margin-left: 6rpx;
  color: #666;
}

/* 通用卡片样式 */
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
  padding: 30rpx;
}

.card-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

/* 信息主体卡片 */
.main-info {
  margin-top: 20rpx;
}

.post-header {
  margin-bottom: 24rpx;
}

.post-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  margin-bottom: 16rpx;
}

.post-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  font-size: 26rpx;
  color: #999;
  gap: 16rpx;
}

.post-category {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.post-views {
  display: flex;
  align-items: center;
}

.post-gallery {
  margin-bottom: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.swiper {
  height: 400rpx;
  border-radius: 12rpx;
}

.carousel-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.single-image image {
  width: 100%;
  border-radius: 12rpx;
}

.divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 24rpx 0;
}

.post-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  margin-right: 10rpx;
}

/* 发布者信息 */
.publisher-content {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.publisher-details {
  flex: 1;
}

.publisher-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.publisher-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.contact-btn {
  background-color: #007aff;
  margin-left: 20rpx;
}

/* 位置信息 */
.location-content {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.location-map {
  border-radius: 12rpx;
  overflow: hidden;
}

.map-preview {
  width: 100%;
  border-radius: 12rpx;
}

/* 相关推荐 */
.related-content {
  display: flex;
  overflow-x: auto;
  gap: 20rpx;
  padding: 10rpx 0;
}

.related-item {
  flex: 0 0 220rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.related-image {
  height: 160rpx;
  overflow: hidden;
}

.related-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-info {
  padding: 10rpx;
}

.related-title {
  font-size: 24rpx;
  color: #333;
  line-height: 1.3;
  margin-bottom: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.related-price {
  font-size: 24rpx;
  color: #f56c6c;
  font-weight: bold;
}

/* 评论区 */
.comment-empty {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.comment-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.comment-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.comment-username {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.comment-input-area {
  display: flex;
  margin-top: 20rpx;
  align-items: center;
}

.comment-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
}

.comment-submit {
  margin-left: 16rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
}

/* 样式适配 */
@media screen and (max-width: 375px) {
  .post-title {
    font-size: 36rpx;
  }
  
  .content-card {
    padding: 24rpx;
  }
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 分享提示样式 */
.share-tip {
  position: fixed;
  top: 120rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 40rpx;
  z-index: 100;
  display: flex;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

.tip-text {
  margin-right: 15rpx;
}

.tip-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #ccc;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 添加分享成功奖励弹窗 */
.share-reward-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.share-reward-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  max-width: 750rpx;
  width: 100%;
}

.reward-close {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999;
}

.reward-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.reward-icon {
  font-size: 40rpx;
  color: #1677FF;
  margin-bottom: 10rpx;
}

.reward-message {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.reward-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.reward-btn {
  background-color: #1677FF;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 35rpx;
  font-size: 28rpx;
  align-self: flex-end;
}

.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  flex: 1;
}

.navbar-left {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-right {
  position: absolute;
  right: 30rpx;
}
</style> 