<template>
  <!-- 磁县同城信息模块 - 苹果风格设计 -->
  <view class="info-module">
    <!-- 模块标题区 - 苹果风格设计 -->
    <view class="info-header all-info-title-row">
      <view class="info-title-container">
        <view class="info-title-bar"></view>
        <text class="info-title">磁县同城信息</text>
        <view class="info-badge">
          <text class="info-badge-text">本地精选</text>
        </view>
      </view>
      <view class="info-publish-btn" @click="navigateTo('/pages/publish/detail')">
        <view class="info-publish-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
        </view>
        <text class="info-publish-text">发布</text>
      </view>
    </view>
    
    <!-- 标签导航栏 - 苹果风格设计 -->
    <view class="info-tabs-container" :class="{'sticky-tabs': isSticky}" :style="stickyStyle">
      <scroll-view 
        class="info-tabs" 
        scroll-x 
        show-scrollbar="false"
        :scroll-with-animation="true"
        :enhanced="true"
        :bounces="true"
        :scroll-left="tabsScrollLeft"
      >
        <view 
          class="info-tab" 
          :class="{active: currentInfoTab === 0}" 
          @click="switchInfoTab(0)"
          :id="`tab-0`"
        >
          <view class="tab-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
            </svg>
          </view>
          <text class="tab-text">最新发布</text>
          <view class="tab-line" v-if="currentInfoTab === 0"></view>
        </view>
        <view 
          v-for="(cat, idx) in visibleCategories" 
          :key="cat" 
          class="info-tab" 
          :class="{active: currentInfoTab === infoCategories.indexOf(cat)+1}" 
          @click="switchInfoTab(infoCategories.indexOf(cat)+1)"
          :id="`tab-${infoCategories.indexOf(cat)+1}`"
        >
          <view class="tab-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path v-if="cat === '招聘信息' || cat === '求职信息'" d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle v-if="cat === '招聘信息' || cat === '求职信息'" cx="8.5" cy="7" r="4"></circle>
              <path v-if="cat === '房屋出租' || cat === '房屋出售'" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline v-if="cat === '房屋出租' || cat === '房屋出售'" points="9 22 9 12 15 12 15 22"></polyline>
              <circle v-if="cat === '二手闲置' || cat === '二手车辆'" cx="9" cy="21" r="1"></circle>
              <circle v-if="cat === '二手闲置' || cat === '二手车辆'" cx="20" cy="21" r="1"></circle>
              <path v-if="cat === '二手闲置' || cat === '二手车辆'" d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
              <path v-if="cat === '到家服务' || cat === '寻找服务'" d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
              <circle v-if="cat === '宠物信息'" cx="12" cy="12" r="10"></circle>
              <path v-if="cat === '宠物信息'" d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
              <line v-if="cat === '宠物信息'" x1="12" y1="17" x2="12.01" y2="17"></line>
              <rect v-if="cat === '其他服务'" x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <circle v-if="cat === '其他服务'" cx="8.5" cy="8.5" r="1.5"></circle>
              <polyline v-if="cat === '其他服务'" points="21 15 16 10 5 21"></polyline>
            </svg>
          </view>
          <text class="tab-text">{{cat}}</text>
          <view class="tab-line" v-if="currentInfoTab === infoCategories.indexOf(cat)+1"></view>
        </view>
        
        <!-- 筛选按钮 -->
        <view class="filter-tab" @click="showFilterOptions">
          <view class="filter-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="4" y1="21" x2="4" y2="14"></line>
              <line x1="4" y1="10" x2="4" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12" y2="3"></line>
              <line x1="20" y1="21" x2="20" y2="16"></line>
              <line x1="20" y1="12" x2="20" y2="3"></line>
              <line x1="1" y1="14" x2="7" y2="14"></line>
              <line x1="9" y1="8" x2="15" y2="8"></line>
              <line x1="17" y1="16" x2="23" y2="16"></line>
            </svg>
          </view>
          <text class="filter-text">筛选</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 占位元素，当标签栏吸顶时保持布局 -->
    <view class="tabs-placeholder" v-if="isSticky" :style="{height: tabsHeight + 'rpx'}"></view>
    
    <!-- 信息列表 - 苹果风格设计 -->
    <view class="info-list">
      <!-- 置顶提示横幅 - 仅在有置顶信息时显示 -->
      <view class="top-banner" v-if="hasTopItems">
        <view class="top-banner-content">
          <view class="top-banner-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
          </view>
          <text class="top-banner-text">置顶信息优先展示，获得更多曝光</text>
          <view class="top-banner-btn" @click="showTopOptions">
            <text>我要置顶</text>
          </view>
        </view>
      </view>
      
      <!-- 全部信息列表（包括置顶信息和普通信息） -->
      <template v-for="(item, globalIndex) in combinedInfoList" :key="item.key">
        <!-- 信息项内容 - 使用卡片工厂组件 -->
        <info-card-factory 
          v-if="!item.isAd" 
          :item="item"
          @click="navigateToInfoDetail(item)"
        />
        
        <!-- 广告位 - 苹果风格设计 -->
        <view v-else class="ad-banner">
          <view class="ad-content">
            <view class="ad-image-container">
              <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="ad-svg">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <line x1="8" y1="21" x2="16" y2="21"></line>
                <line x1="12" y1="17" x2="12" y2="21"></line>
                <text x="12" y="12" text-anchor="middle" font-size="6" fill="currentColor">广告</text>
              </svg>
            </view>
            <view class="ad-label">广告</view>
          </view>
        </view>
      </template>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMoreData">
        <view class="loading-indicator">
          <view class="loading-spinner-container">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loading-spinner-svg">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
          </view>
          <text class="loading-text">加载更多</text>
        </view>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-else>
        <text class="no-more-text">已经到底啦</text>
      </view>
    </view>
    
    <!-- 置顶选项弹窗 -->
    <view class="top-options-popup" v-if="showTopOptionsModal" @click="hideTopOptions">
      <view class="top-options-content" @click.stop>
        <view class="top-options-header">
          <text class="top-options-title">选择置顶方式</text>
          <view class="top-options-close" @click="hideTopOptions">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        
        <view class="top-options-body">
          <view class="top-option" @click="selectTopOption('paid')">
            <view class="top-option-icon paid-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </view>
            <view class="top-option-info">
              <text class="top-option-title">付费置顶</text>
              <text class="top-option-desc">使用余额支付，立即置顶信息</text>
            </view>
            <view class="top-option-action">
              <text class="top-action-text">去支付</text>
              <view class="top-action-arrow">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </view>
            </view>
          </view>
          
          <view class="top-option" @click="selectTopOption('ad')">
            <view class="top-option-icon ad-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <line x1="8" y1="21" x2="16" y2="21"></line>
                <line x1="12" y1="17" x2="12" y2="21"></line>
              </svg>
            </view>
            <view class="top-option-info">
              <text class="top-option-title">看广告置顶</text>
              <text class="top-option-desc">观看广告视频，免费获得置顶机会</text>
            </view>
            <view class="top-option-action">
              <text class="top-action-text">去观看</text>
              <view class="top-action-arrow">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </view>
            </view>
          </view>
        </view>
        
        <view class="top-options-footer">
          <text class="top-options-tips">置顶信息将获得更多曝光和更高的点击率</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { onPageScroll } from '@dcloudio/uni-app';
import InfoCardFactory from '../cards/InfoCardFactory.vue';

const props = defineProps({
  allInfoList: {
    type: Array,
    default: () => []
  },
  toppedInfoList: {
    type: Array,
    default: () => []
  },
  adBanner: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['tab-change']);

// 状态变量
const currentInfoTab = ref(0);
const visibleCategories = ref([
  '到家服务', '寻找服务', '生意转让', '招聘信息', '求职信息',
  '房屋出租', '房屋出售', '二手车辆', '宠物信息', '商家活动',
  '婚恋交友', '车辆服务', '二手闲置', '磁州拼车', '教育培训', '其他服务'
]);
const infoCategories = ref([
  '到家服务', '寻找服务', '生意转让', '招聘信息', '求职信息',
  '房屋出租', '房屋出售', '二手车辆', '宠物信息', '商家活动',
  '婚恋交友', '车辆服务', '二手闲置', '磁州拼车', '教育培训', '其他服务'
]);

// 吸顶相关
const isSticky = ref(false);
const stickyTop = ref(0);
const tabsHeight = ref(88);
const tabsScrollLeft = ref(0);

// 置顶选项弹窗
const showTopOptionsModal = ref(false);

// 加载更多
const hasMoreData = ref(true);

// 计算属性
const combinedInfoList = computed(() => {
  let toppedItems = [];
  let normalItems = [];
  
  if (currentInfoTab.value === 0) {
    toppedItems = props.toppedInfoList || [];
    normalItems = props.allInfoList || [];
  } else {
    const selectedCategory = infoCategories.value[currentInfoTab.value - 1];
    toppedItems = (props.toppedInfoList || []).filter(item => item.category === selectedCategory);
    normalItems = (props.allInfoList || []).filter(item => item.category === selectedCategory);
  }

  toppedItems = toppedItems.map((item, index) => ({
    ...item,
    isTopped: true,
    key: `top-${item.id || index}-${Date.now()}`
  }));
  normalItems = normalItems.map((item, index) => ({
    ...item,
    isTopped: false,
    key: `normal-${item.id || index}-${Date.now()}`
  }));
  
  const allItems = [...toppedItems, ...normalItems];
  const result = [];
  let uniqueCounter = 0;
  
  const adImages = [
    '/static/images/banner/ad-banner.jpg',
    '/static/images/banner/banner-1.png',
    '/static/images/banner/banner-2.png',
    '/static/images/banner/banner-3.jpg'
  ];
  
  if (props.adBanner && props.adBanner.image) {
    adImages.unshift(props.adBanner.image);
  }
  
  allItems.forEach((item, index) => {
    result.push(item);
    if ((index + 1) % 5 === 0) {
      const ad = {
        isAd: true,
        image: adImages[uniqueCounter % adImages.length],
        key: `ad-${uniqueCounter++}-${Date.now()}`
      };
      result.push(ad);
    }
  });
  return result;
});

const stickyStyle = computed(() => ({
  top: `${stickyTop.value}px`
}));

const hasTopItems = computed(() => {
  return props.toppedInfoList && props.toppedInfoList.length > 0;
});

// 方法
function setSticky(sticky, top = 0) {
  isSticky.value = sticky;
  stickyTop.value = top;
}

function switchInfoTab(index) {
  if (currentInfoTab.value === index) return;
  currentInfoTab.value = index;
  emit('tab-change', {
    index,
    name: index === 0 ? '最新发布' : infoCategories.value[index - 1]
  });

  nextTick(() => {
    const query = uni.createSelectorQuery();
    query.select(`#tab-${index}`).boundingClientRect(data => {
      if (data) {
        const screenWidth = uni.getSystemInfoSync().windowWidth;
        const scrollTarget = data.left - screenWidth / 2 + data.width / 2;
        tabsScrollLeft.value = scrollTarget;
      }
    }).exec();
  });
}

function navigateTo(url) {
  if (!url) return;
  
  uni.navigateTo({
    url: url,
    fail: (err) => {
      console.error('页面跳转失败:', err);
      uni.showToast({
        title: '页面开发中',
        icon: 'none'
      });
    }
  });
}

function navigateToInfoDetail(item) {
  // 详情页路由映射
  const detailPageMap = {
    '招聘信息': 'job-detail',
    '求职信息': 'job-seeking-detail',
    '房屋出租': 'house-rent-detail',
    '房屋出售': 'house-sale-detail',
    '二手车辆': 'car-detail',
    '宠物信息': 'pet-detail',
    '二手闲置': 'second-hand-detail',
    '婚恋交友': 'dating-detail',
    '商家活动': 'merchant-activity-detail',
    '磁州拼车': 'carpool-detail',
    '教育培训': 'education-detail',
    '到家服务': 'home-service-detail',
    '寻找服务': 'find-service-detail',
    '生意转让': 'business-transfer-detail',
    // 默认跳转到通用信息详情页
    'default': 'info-detail'
  };

  const pageType = detailPageMap[item.category] || detailPageMap['default'];

  // 构建参数
  let params = {
    id: item.id,
    category: encodeURIComponent(item.category || '')
  };
  
  // 构建URL
  const url = `/pages/publish/${pageType}?${Object.entries(params)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')}`;
  
  console.log('跳转到详情页:', url);
  navigateTo(url);
}

function showTopOptions() {
  showTopOptionsModal.value = true;
}

function hideTopOptions() {
  showTopOptionsModal.value = false;
}

function selectTopOption(type) {
  if (type === 'paid') {
    navigateTo('/pages/top/paid');
  } else if (type === 'ad') {
    // 显示广告
    uni.showToast({
      title: '正在加载广告...',
      icon: 'loading'
    });
    
    setTimeout(() => {
      uni.hideToast();
      uni.showModal({
        title: '广告观看完成',
        content: '恭喜您获得2小时置顶特权！',
        showCancel: false,
        success: () => {
          hideTopOptions();
        }
      });
    }, 1500);
  }
}

// 点赞信息
function likeInfo(item) {
  console.log('点赞信息:', item.id);
  // 实际应用中应该调用API进行点赞
  uni.showToast({
    title: '点赞成功',
    icon: 'success'
  });
}

// 评论信息
function commentInfo(item) {
  console.log('评论信息:', item.id);
  navigateTo(`/pages/publish/comment?id=${item.id}&category=${encodeURIComponent(item.category || '')}`);
}

// 分享信息
function shareInfo(item) {
  console.log('分享信息:', item.id);
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    title: item.content,
    summary: `来自磁州生活网的${item.category}信息`,
    imageUrl: item.images && item.images.length > 0 ? item.images[0] : '',
    success: function() {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
    },
    fail: function() {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  });
}

// 添加方法
function showFilterOptions() {
  uni.showToast({
    title: '筛选功能开发中',
    icon: 'none'
  });
}

// 优化吸顶效果
const tabsTop = ref(0);
const scrollTimer = ref(null);

onMounted(() => {
  // 设置初始值
  isSticky.value = false;
  tabsHeight.value = 80;
  
  // 获取标签栏高度
  nextTick(() => {
    const query = uni.createSelectorQuery();
    query.select('.info-tabs-container').boundingClientRect(data => {
      if (data) {
        tabsHeight.value = data.height;
        tabsTop.value = data.top;
      }
    }).exec();
  });
})

// 注意：setSticky方法已在前面定义

// 暴露方法给父组件
defineExpose({
  setSticky
});
</script>

<style lang="scss" scoped>
/* 磁县同城信息模块 - 苹果风格设计 */
.info-module {
  margin: 24rpx;
  border-radius: 35rpx;
  background-color: #FFFFFF;
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12), 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
}

/* 模块标题区 */
.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.info-title-container {
  display: flex;
  align-items: center;
}

.info-title-bar {
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(180deg, #007AFF, #5AC8FA);
  border-radius: 3rpx;
  margin-right: 16rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  letter-spacing: 0.8rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.info-badge {
  margin-left: 12rpx;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
}

.info-badge-text {
  font-size: 20rpx;
  color: #007AFF;
}

.info-publish-btn {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.info-publish-icon {
  color: #FFFFFF;
  margin-right: 8rpx;
}

.info-publish-text {
  font-size: 26rpx;
  color: #FFFFFF;
  font-weight: 500;
}

/* 标签导航栏 */
.info-tabs-container {
  width: 100%;
  background-color: #FFFFFF;
  padding: 10rpx 0;
  z-index: 100;
  transition: all 0.3s;
}

.sticky-tabs {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.info-tabs {
  white-space: nowrap;
  width: 100%;
  height: 80rpx;
}

.info-tab {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin: 0 8rpx;
  position: relative;
  transition: all 0.3s;
}

.info-tab:first-child {
  margin-left: 20rpx;
}

.tab-icon {
  margin-right: 6rpx;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-text {
  font-size: 26rpx;
  color: #636366;
  font-weight: 500;
}

.info-tab.active .tab-text {
  color: #007AFF;
  font-weight: 600;
}

.info-tab.active .tab-icon {
  color: #007AFF;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 4rpx;
  background: #007AFF;
  border-radius: 2rpx;
}

.filter-tab {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin: 0 20rpx;
  background: linear-gradient(135deg, #F2F2F7, #E5E5EA);
  border-radius: 35rpx;
  color: #636366;
}

.filter-icon {
  margin-right: 6rpx;
  color: #636366;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 占位元素 */
.tabs-placeholder {
  width: 100%;
}

/* 置顶标识 - 重新设计 */
.top-indicator {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
}

.top-badge {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-bottom-left-radius: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.paid-badge {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  box-shadow: 0 8rpx 20rpx rgba(255, 59, 48, 0.2), 0 4rpx 8rpx rgba(255, 59, 48, 0.1);
}

.ad-badge {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.2), 0 4rpx 8rpx rgba(0, 122, 255, 0.1);
}

.top-badge-icon {
  color: #FFFFFF;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.top-badge-icon svg {
  width: 14px;
  height: 14px;
}

.top-badge-text {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 置顶提示横幅 */
.top-banner {
  margin: 20rpx 24rpx 0;
  border-radius: 24rpx;
  background: linear-gradient(135deg, rgba(255, 204, 0, 0.15), rgba(255, 149, 0, 0.15));
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(255, 149, 0, 0.1);
}

.top-banner-content {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
}

.top-banner-icon {
  color: #FF9500;
  margin-right: 12rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.top-banner-text {
  flex: 1;
  font-size: 26rpx;
  color: #FF9500;
  font-weight: 500;
}

.top-banner-btn {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.2);
}

.top-banner-btn text {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 600;
}

/* 信息列表 */
.info-list {
  padding: 16rpx 24rpx 24rpx;
}

/* 信息项 - 高级感苹果风格重新设计 */
.info-item {
  margin-bottom: 24rpx;
  border-radius: 35rpx;
  background-color: #FFFFFF;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  border: none;
  overflow: hidden;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
  transform: translateZ(0) perspective(1000px) rotateX(0deg);
  backface-visibility: hidden;
  padding: 28rpx 24rpx;
}

.info-item:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 置顶项特殊样式 */
.topped-item {
  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);
  box-shadow: 0 14rpx 36rpx rgba(255, 149, 0, 0.16), 0 6rpx 16rpx rgba(255, 149, 0, 0.1);
  border-left: 6rpx solid #FF9500;
}

.paid-top {
  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);
  border-left: 6rpx solid #FF9500;
  box-shadow: 0 14rpx 36rpx rgba(255, 149, 0, 0.16), 0 6rpx 16rpx rgba(255, 149, 0, 0.1);
}

.ad-top {
  background: linear-gradient(135deg, #FFFFFF, #F0F7FF);
  border-left: 6rpx solid #007AFF;
  box-shadow: 0 14rpx 36rpx rgba(0, 122, 255, 0.16), 0 6rpx 16rpx rgba(0, 122, 255, 0.1);
}

/* 内容区 - 高级感重新设计 */
.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 删除重复的卡片样式 */
/* 确保卡片大小不变 */
/* .info-item {
  padding: 24rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
} */

/* 头部信息 - 高级感重新设计 */
.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-category {
  background: linear-gradient(135deg, #0062CC, #0091E6);
  border-radius: 10rpx;
  padding: 4rpx 12rpx;
  border: 0.5rpx solid rgba(0, 98, 204, 0.2);
  box-shadow: 0 4rpx 8rpx rgba(0, 98, 204, 0.15);
  transform: translateZ(0);
  margin-right: 12rpx;
  display: inline-flex;
  align-items: center;
}

.topped-category {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  border: 0.5rpx solid rgba(255, 59, 48, 0.2);
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.15);
}

.category-text {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.topped-category .category-text {
  color: #FFFFFF;
}

.info-time {
  font-size: 22rpx;
  color: #8E8E93;
  font-weight: 400;
  background-color: rgba(142, 142, 147, 0.08);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 主要内容 - 高级感重新设计 */
.info-main {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 12rpx;
  margin-bottom: 20rpx;
  gap: 20rpx;
  width: 100%;
}

.info-content-wrapper {
  flex: 1;
  overflow: hidden;
  min-width: 0; /* 确保文本可以正确换行 */
}

.info-text {
  font-size: 30rpx;
  color: #1C1C1E;
  line-height: 1.5;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all; /* 确保长文本可以正确换行 */
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.bold-text {
  font-weight: 600;
  color: #000000;
}

/* 右侧图片样式 */
.info-images-right {
  width: 180rpx;
  height: 180rpx;
  flex-shrink: 0;
  position: relative;
  margin-left: 12rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}

.info-image-container-right {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #f2f2f7;
  position: relative;
}

.info-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16rpx;
  transition: transform 0.3s ease;
}

.info-image:active {
  transform: scale(1.05);
}

.image-count-right {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  z-index: 2;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

/* 隐藏旧的图片布局 */
.info-images {
  display: none;
}

/* 内容标签 - 新增设计元素 */
.info-tags {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4rpx 16rpx;
}

.info-tag {
  background-color: rgba(0, 122, 255, 0.08);
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  margin: 6rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05), 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.info-tag-text {
  font-size: 22rpx;
  color: #007AFF;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 图片预览 - 高级感重新设计 */
.info-images {
  display: flex;
  flex-wrap: wrap;
  margin: 14rpx -4rpx 0; /* 从20rpx -6rpx 0减少到14rpx -4rpx 0 */
  position: relative;
}

.info-image-container {
  width: calc(33.33% - 8rpx); /* 从calc(33.33% - 12rpx)减少到calc(33.33% - 8rpx) */
  height: 160rpx; /* 从200rpx减少到160rpx */
  margin: 4rpx; /* 从6rpx减少到4rpx */
  border-radius: 16rpx; /* 从24rpx减少到16rpx */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08); /* 减轻阴影效果 */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.8); /* 从2rpx减少到1rpx */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa, #e4e8f0);
  overflow: hidden;
  position: relative;
}

.info-image-container:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.info-image-svg {
  width: 40%;
  height: 40%;
  color: #8E8E93;
  opacity: 0.5;
  position: absolute;
  z-index: 1;
}

.single-image {
  width: 70%; /* 从75%减少到70% */
  height: 240rpx; /* 从320rpx减少到240rpx */
}

.double-image {
  width: calc(50% - 8rpx); /* 从calc(50% - 12rpx)减少到calc(50% - 8rpx) */
  height: 200rpx; /* 从260rpx减少到200rpx */
}

.image-count {
  position: absolute;
  right: 16rpx;
  bottom: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10rpx);
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 600;
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 信息状态标签 - 新增设计元素 */
.info-status {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  z-index: 2;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.info-status-text {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

/* 底部信息 - 高级感重新设计 */
.info-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 0.5rpx solid rgba(60, 60, 67, 0.1);
  position: relative;
  z-index: 1;
  margin-top: 4rpx;
}

.info-user {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: inset 0 0.5rpx 2rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5rpx);
  -webkit-backdrop-filter: blur(5rpx);
}

.user-avatar-container {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 10rpx;
  border: 0.5rpx solid #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 122, 255, 0.1);
  overflow: hidden;
}

.user-avatar-svg {
  width: 24px;
  height: 24px;
  color: #007AFF;
}

.user-name {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.info-stats {
  display: flex;
  align-items: center;
}

.info-views {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: inset 0 0.5rpx 2rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5rpx);
  -webkit-backdrop-filter: blur(5rpx);
}

.view-icon {
  color: #636366;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-icon svg {
  width: 14px;
  height: 14px;
}

.view-count {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 红包信息 - 高级感重新设计 */
.info-redpacket {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 24rpx;
  padding: 8rpx 16rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 59, 48, 0.2), 0 2rpx 6rpx rgba(255, 59, 48, 0.1);
  transform: translateZ(0);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
}

.info-redpacket:active {
  transform: scale(0.96);
  box-shadow: 0 3rpx 8rpx rgba(255, 59, 48, 0.15);
}

.redpacket-icon {
  color: #FFFFFF;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.redpacket-icon svg {
  width: 14px;
  height: 14px;
}

.redpacket-amount {
  font-size: 26rpx;
  color: #FFFFFF;
  font-weight: 700;
  margin-right: 10rpx;
  letter-spacing: 1rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.redpacket-btn {
  background-color: #FFFFFF;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.redpacket-btn text {
  font-size: 22rpx;
  color: #FF3B30;
  font-weight: 700;
}

/* 互动按钮组 - 新增设计元素 */
.info-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 0.5rpx solid rgba(60, 60, 67, 0.1);
  position: relative;
}

.info-actions:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.5rpx;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
}

.info-action {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 20rpx;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 24rpx;
  box-shadow: inset 0 0.5rpx 2rpx rgba(0, 0, 0, 0.05);
  margin: 0 6rpx;
}

.info-action:active {
  transform: scale(0.92);
  background-color: rgba(0, 0, 0, 0.04);
}

.info-action-icon {
  color: #8E8E93;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-action-icon svg {
  width: 16px;
  height: 16px;
}

.info-action-text {
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 广告位 */
.ad-banner {
  margin-bottom: 24rpx;
  border-radius: 35rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.12), 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateZ(0);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
}

.ad-banner:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.ad-content {
  width: 100%;
  position: relative;
}

.ad-image-container {
  width: 100%;
  height: 180rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa, #e4e8f0);
  border-radius: 16rpx;
  overflow: hidden;
}

.ad-svg {
  width: 60%;
  height: 60%;
  color: #8E8E93;
  opacity: 0.7;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.ad-label {
  position: absolute;
  right: 16rpx;
  bottom: 16rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

/* 加载更多 */
.load-more {
  padding: 30rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-indicator {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  box-shadow: inset 0 0.5rpx 3rpx rgba(0, 0, 0, 0.05), 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.loading-spinner-container {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.loading-spinner-svg {
  width: 28rpx;
  height: 28rpx;
  color: #007AFF;
  animation: spin 1s linear infinite;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 没有更多数据 */
.no-more {
  padding: 30rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-more-text {
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  box-shadow: inset 0 0.5rpx 3rpx rgba(0, 0, 0, 0.05), 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 置顶选项弹窗 */
.top-options-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.top-options-content {
  width: 650rpx;
  background-color: #FFFFFF;
  border-radius: 35rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: popup 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes popup {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.top-options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx;
  border-bottom: 0.5rpx solid rgba(60, 60, 67, 0.1);
}

.top-options-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.top-options-close {
  width: 52rpx;
  height: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.top-options-close:active {
  background-color: rgba(0, 0, 0, 0.08);
  transform: scale(0.95);
}

.top-options-body {
  padding: 28rpx;
}

.top-option {
  display: flex;
  align-items: center;
  padding: 28rpx;
  margin-bottom: 24rpx;
  border-radius: 24rpx;
  background-color: #F2F2F7;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
}

.top-option:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}

.top-option:last-child {
  margin-bottom: 0;
}

.top-option-icon {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}

.paid-icon {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  color: #FFFFFF;
}

.ad-icon {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #FFFFFF;
}

.top-option-info {
  flex: 1;
}

.top-option-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 6rpx;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.top-option-desc {
  font-size: 26rpx;
  color: #8E8E93;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.top-option-action {
  display: flex;
  align-items: center;
  background-color: rgba(0, 122, 255, 0.1);
  padding: 10rpx 20rpx;
  border-radius: 24rpx;
}

.top-action-text {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: 500;
  margin-right: 8rpx;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.top-action-arrow {
  color: #007AFF;
}

.top-options-footer {
  padding: 28rpx;
  border-top: 0.5rpx solid rgba(60, 60, 67, 0.1);
  display: flex;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.01);
}

.top-options-tips {
  font-size: 26rpx;
  color: #8E8E93;
  text-align: center;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 房屋出租信息简洁展示 */
.house-rent-info {
  margin-top: 12rpx;
  margin-bottom: 12rpx;
  padding: 16rpx;
  border-radius: 16rpx;
  background-color: rgba(0, 0, 0, 0.03);
  backdrop-filter: blur(5rpx);
  -webkit-backdrop-filter: blur(5rpx);
  box-shadow: inset 0 1rpx 5rpx rgba(0, 0, 0, 0.05);
}

.house-rent-detail {
  display: flex;
  justify-content: space-between;
}

.house-rent-item {
  display: flex;
  align-items: center;
}

.house-icon {
  color: #636366;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
}

.house-text {
  font-size: 22rpx;
  color: #636366;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
</style>