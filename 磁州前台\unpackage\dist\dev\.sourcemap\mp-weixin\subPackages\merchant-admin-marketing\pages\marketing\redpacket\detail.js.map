{"version": 3, "file": "detail.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">红包详情</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"more-icon\" @click=\"showMoreActions\">...</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包基本信息 -->\r\n    <view class=\"redpacket-header\">\r\n      <view class=\"redpacket-cover\">\r\n        <image class=\"cover-image\" :src=\"redpacketData.coverUrl\" mode=\"aspectFill\"></image>\r\n        <view class=\"redpacket-status\" :class=\"'status-'+redpacketData.status\">{{redpacketData.statusText}}</view>\r\n      </view>\r\n      <view class=\"redpacket-info\">\r\n        <text class=\"redpacket-name\">{{redpacketData.name}}</text>\r\n        <view class=\"redpacket-type\">\r\n          <view class=\"type-tag\" :class=\"'type-'+redpacketData.type\">{{redpacketData.typeText}}</view>\r\n        </view>\r\n        <view class=\"redpacket-time\">\r\n          <view class=\"time-icon\"></view>\r\n          <text class=\"time-text\">{{redpacketData.timeRange}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 数据概览卡片 -->\r\n    <view class=\"stats-card\">\r\n      <view class=\"stats-header\">\r\n        <text class=\"stats-title\">数据概览</text>\r\n        <view class=\"stats-action\" @click=\"viewFullStats\">\r\n          <text class=\"action-text\">查看详细</text>\r\n          <view class=\"action-arrow\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-grid\">\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-value\">{{redpacketData.totalCount}}</text>\r\n          <text class=\"stats-label\">总数量</text>\r\n        </view>\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-value\">{{redpacketData.sentCount}}</text>\r\n          <text class=\"stats-label\">已发放</text>\r\n        </view>\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-value\">{{redpacketData.receivedCount}}</text>\r\n          <text class=\"stats-label\">已领取</text>\r\n        </view>\r\n        <view class=\"stats-item\">\r\n          <text class=\"stats-value\">{{redpacketData.usedCount}}</text>\r\n          <text class=\"stats-label\">已使用</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"progress-section\">\r\n        <view class=\"progress-header\">\r\n          <text class=\"progress-title\">发放进度</text>\r\n          <text class=\"progress-value\">{{redpacketData.sentCount}}/{{redpacketData.totalCount}}</text>\r\n        </view>\r\n        <view class=\"progress-bar\">\r\n          <view class=\"progress-fill\" :style=\"{ width: (redpacketData.sentCount / redpacketData.totalCount * 100) + '%' }\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包设置信息 -->\r\n    <view class=\"settings-card\">\r\n      <view class=\"settings-header\">\r\n        <text class=\"settings-title\">红包设置</text>\r\n        <view class=\"settings-action\" v-if=\"redpacketData.status === 'draft'\" @click=\"editRedpacket\">\r\n          <text class=\"action-text\">编辑</text>\r\n          <view class=\"action-arrow\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"settings-list\">\r\n        <view class=\"settings-item\">\r\n          <text class=\"item-label\">红包金额</text>\r\n          <text class=\"item-value\" v-if=\"redpacketData.amountType === 'fixed'\">¥{{redpacketData.fixedAmount}}/个</text>\r\n          <text class=\"item-value\" v-else-if=\"redpacketData.amountType === 'random'\">¥{{redpacketData.minAmount}}-{{redpacketData.maxAmount}}/个</text>\r\n        </view>\r\n        \r\n        <view class=\"settings-item\">\r\n          <text class=\"item-label\">发放对象</text>\r\n          <text class=\"item-value\">{{redpacketData.targetText}}</text>\r\n        </view>\r\n        \r\n        <view class=\"settings-item\">\r\n          <text class=\"item-label\">使用门槛</text>\r\n          <text class=\"item-value\">{{redpacketData.thresholdText}}</text>\r\n        </view>\r\n        \r\n        <view class=\"settings-item\">\r\n          <text class=\"item-label\">有效期</text>\r\n          <text class=\"item-value\">领取后{{redpacketData.validity}}天有效</text>\r\n        </view>\r\n        \r\n        <view class=\"settings-item\">\r\n          <text class=\"item-label\">单用户领取上限</text>\r\n          <text class=\"item-value\">{{redpacketData.userLimit}}个</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 使用说明 -->\r\n    <view class=\"description-card\">\r\n      <view class=\"description-header\">\r\n        <text class=\"description-title\">使用说明</text>\r\n      </view>\r\n      <view class=\"description-content\">\r\n        <text class=\"description-text\">{{redpacketData.description}}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 领取记录 -->\r\n    <view class=\"records-card\">\r\n      <view class=\"records-header\">\r\n        <text class=\"records-title\">领取记录</text>\r\n        <view class=\"records-action\" @click=\"viewAllRecords\">\r\n          <text class=\"action-text\">查看全部</text>\r\n          <view class=\"action-arrow\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"records-list\">\r\n        <view class=\"records-item\" v-for=\"(record, index) in redpacketData.records\" :key=\"index\">\r\n          <view class=\"user-avatar\">\r\n            <image class=\"avatar-image\" :src=\"record.avatar\" mode=\"aspectFill\"></image>\r\n          </view>\r\n          <view class=\"record-info\">\r\n            <text class=\"user-name\">{{record.userName}}</text>\r\n            <text class=\"record-time\">{{record.time}}</text>\r\n          </view>\r\n          <view class=\"record-amount\">\r\n            <text class=\"amount-value\">¥{{record.amount}}</text>\r\n            <text class=\"amount-status\" v-if=\"record.used\">已使用</text>\r\n            <text class=\"amount-status unused\" v-else>未使用</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"empty-records\" v-if=\"redpacketData.records.length === 0\">\r\n          <view class=\"empty-icon\"></view>\r\n          <text class=\"empty-text\">暂无领取记录</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部操作按钮 -->\r\n    <view class=\"bottom-actions\">\r\n      <view class=\"action-button share\" v-if=\"redpacketData.status === 'active'\" @click=\"shareRedpacket\">\r\n        <view class=\"button-icon share-icon\"></view>\r\n        <text class=\"button-text\">分享红包</text>\r\n      </view>\r\n      \r\n      <view class=\"action-button start\" v-if=\"redpacketData.status === 'upcoming'\" @click=\"startRedpacket\">\r\n        <view class=\"button-icon start-icon\"></view>\r\n        <text class=\"button-text\">立即开始</text>\r\n      </view>\r\n      \r\n      <view class=\"action-button stop\" v-if=\"redpacketData.status === 'active'\" @click=\"stopRedpacket\">\r\n        <view class=\"button-icon stop-icon\"></view>\r\n        <text class=\"button-text\">结束活动</text>\r\n      </view>\r\n      \r\n      <view class=\"action-button delete\" v-if=\"redpacketData.status === 'draft' || redpacketData.status === 'ended'\" @click=\"deleteRedpacket\">\r\n        <view class=\"button-icon delete-icon\"></view>\r\n        <text class=\"button-text\">删除活动</text>\r\n      </view>\r\n      \r\n      <view class=\"action-button duplicate\" @click=\"duplicateRedpacket\">\r\n        <view class=\"button-icon duplicate-icon\"></view>\r\n        <text class=\"button-text\">复制活动</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分享弹窗 -->\r\n    <view class=\"share-popup\" v-if=\"showSharePopup\">\r\n      <view class=\"popup-mask\" @click=\"closeSharePopup\"></view>\r\n      <view class=\"popup-content\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">分享红包</text>\r\n          <view class=\"popup-close\" @click=\"closeSharePopup\">×</view>\r\n        </view>\r\n        \r\n        <view class=\"share-options\">\r\n          <view class=\"share-option\" v-for=\"(option, index) in shareOptions\" :key=\"index\" @click=\"shareVia(option.type)\">\r\n            <view class=\"option-icon\" :style=\"{ background: option.color }\">\r\n              <image class=\"icon-image\" :src=\"option.icon\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <text class=\"option-text\">{{option.name}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"share-qrcode\">\r\n          <image class=\"qrcode-image\" src=\"/static/images/redpacket/qrcode-placeholder.png\" mode=\"aspectFit\"></image>\r\n          <text class=\"qrcode-tip\">保存二维码分享</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      showSharePopup: false,\r\n      \r\n      // 红包详情数据\r\n      redpacketData: {\r\n        id: 1,\r\n        name: '新用户专享红包',\r\n        coverUrl: '/static/images/redpacket/cover-placeholder.jpg',\r\n        type: 'normal',\r\n        typeText: '普通红包',\r\n        status: 'active',\r\n        statusText: '进行中',\r\n        timeRange: '2023-04-15 ~ 2023-04-30',\r\n        \r\n        // 数据统计\r\n        totalCount: 1000,\r\n        sentCount: 568,\r\n        receivedCount: 452,\r\n        usedCount: 326,\r\n        \r\n        // 红包设置\r\n        amountType: 'fixed',\r\n        fixedAmount: '10.00',\r\n        minAmount: '',\r\n        maxAmount: '',\r\n        target: 'all',\r\n        targetText: '所有用户',\r\n        threshold: 'none',\r\n        thresholdText: '无门槛',\r\n        validity: '7',\r\n        userLimit: '1',\r\n        \r\n        // 使用说明\r\n        description: '1. 每位用户限领1个红包\\n2. 红包领取后7天内有效\\n3. 红包可在下单时直接抵扣\\n4. 不可与其他优惠同时使用\\n5. 最终解释权归商家所有',\r\n        \r\n        // 领取记录\r\n        records: [\r\n          {\r\n            userName: '用户1354***89',\r\n            avatar: '/static/images/redpacket/avatar1.jpg',\r\n            time: '2023-04-20 14:32:56',\r\n            amount: '10.00',\r\n            used: true\r\n          },\r\n          {\r\n            userName: '用户1567***23',\r\n            avatar: '/static/images/redpacket/avatar2.jpg',\r\n            time: '2023-04-20 14:28:12',\r\n            amount: '10.00',\r\n            used: false\r\n          },\r\n          {\r\n            userName: '用户1892***45',\r\n            avatar: '/static/images/redpacket/avatar3.jpg',\r\n            time: '2023-04-20 14:15:38',\r\n            amount: '10.00',\r\n            used: true\r\n          }\r\n        ]\r\n      },\r\n      \r\n      // 分享选项\r\n      shareOptions: [\r\n        {\r\n          name: '微信好友',\r\n          type: 'wechat',\r\n          icon: '/static/images/redpacket/wechat-icon.png',\r\n          color: '#07C160'\r\n        },\r\n        {\r\n          name: '朋友圈',\r\n          type: 'moments',\r\n          icon: '/static/images/redpacket/moments-icon.png',\r\n          color: '#07C160'\r\n        },\r\n        {\r\n          name: '微博',\r\n          type: 'weibo',\r\n          icon: '/static/images/redpacket/weibo-icon.png',\r\n          color: '#E6162D'\r\n        },\r\n        {\r\n          name: '短信',\r\n          type: 'sms',\r\n          icon: '/static/images/redpacket/sms-icon.png',\r\n          color: '#FF9500'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    showMoreActions() {\r\n      uni.showActionSheet({\r\n        itemList: ['导出数据', '复制链接', '设为模板'],\r\n        success: (res) => {\r\n          switch (res.tapIndex) {\r\n            case 0:\r\n              this.exportData();\r\n              break;\r\n            case 1:\r\n              this.copyLink();\r\n              break;\r\n            case 2:\r\n              this.saveAsTemplate();\r\n              break;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    exportData() {\r\n      uni.showToast({\r\n        title: '导出功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    copyLink() {\r\n      uni.setClipboardData({\r\n        data: 'https://example.com/redpacket/' + this.redpacketData.id,\r\n        success: () => {\r\n          uni.showToast({\r\n            title: '链接已复制',\r\n            icon: 'success'\r\n          });\r\n        }\r\n      });\r\n    },\r\n    saveAsTemplate() {\r\n      uni.showToast({\r\n        title: '已保存为模板',\r\n        icon: 'success'\r\n      });\r\n    },\r\n    viewFullStats() {\r\n      uni.showToast({\r\n        title: '详细数据功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    editRedpacket() {\r\n      uni.showToast({\r\n        title: '编辑功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    viewAllRecords() {\r\n      uni.showToast({\r\n        title: '查看全部记录功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    shareRedpacket() {\r\n      this.showSharePopup = true;\r\n    },\r\n    closeSharePopup() {\r\n      this.showSharePopup = false;\r\n    },\r\n    shareVia(type) {\r\n      uni.showToast({\r\n        title: '分享功能开发中',\r\n        icon: 'none'\r\n      });\r\n      this.closeSharePopup();\r\n    },\r\n    startRedpacket() {\r\n      uni.showModal({\r\n        title: '确认开始',\r\n        content: '确定要立即开始此红包活动吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '活动已开始',\r\n              icon: 'success'\r\n            });\r\n            this.redpacketData.status = 'active';\r\n            this.redpacketData.statusText = '进行中';\r\n          }\r\n        }\r\n      });\r\n    },\r\n    stopRedpacket() {\r\n      uni.showModal({\r\n        title: '确认结束',\r\n        content: '确定要结束此红包活动吗？结束后不可恢复。',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '活动已结束',\r\n              icon: 'success'\r\n            });\r\n            this.redpacketData.status = 'ended';\r\n            this.redpacketData.statusText = '已结束';\r\n          }\r\n        }\r\n      });\r\n    },\r\n    deleteRedpacket() {\r\n      uni.showModal({\r\n        title: '确认删除',\r\n        content: '确定要删除此红包活动吗？删除后不可恢复。',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '删除成功',\r\n              icon: 'success'\r\n            });\r\n            setTimeout(() => {\r\n              uni.navigateBack();\r\n            }, 1500);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    duplicateRedpacket() {\r\n      uni.showToast({\r\n        title: '复制功能开发中',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.page-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 80px;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.more-icon {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  transform: rotate(90deg);\r\n}\r\n\r\n/* 红包头部信息 */\r\n.redpacket-header {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.redpacket-cover {\r\n  width: 100%;\r\n  height: 160px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.cover-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.redpacket-status {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 10px;\r\n  padding: 4px 10px;\r\n  border-radius: 15px;\r\n  font-size: 12px;\r\n}\r\n\r\n.status-active {\r\n  background-color: #E8F5E9;\r\n  color: #388E3C;\r\n}\r\n\r\n.status-upcoming {\r\n  background-color: #E3F2FD;\r\n  color: #1976D2;\r\n}\r\n\r\n.status-ended {\r\n  background-color: #EEEEEE;\r\n  color: #757575;\r\n}\r\n\r\n.status-draft {\r\n  background-color: #FFF3E0;\r\n  color: #E65100;\r\n}\r\n\r\n.redpacket-info {\r\n  padding: 0 5px;\r\n}\r\n\r\n.redpacket-name {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.redpacket-type {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.type-tag {\r\n  display: inline-block;\r\n  padding: 3px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.type-normal {\r\n  background-color: #FFE0E0;\r\n  color: #FF5858;\r\n}\r\n\r\n.type-fission {\r\n  background-color: #E0F7F4;\r\n  color: #4ECDC4;\r\n}\r\n\r\n.type-mass {\r\n  background-color: #FFF3E0;\r\n  color: #FFD166;\r\n}\r\n\r\n.type-rain {\r\n  background-color: #E8E0F7;\r\n  color: #6A0572;\r\n}\r\n\r\n.redpacket-time {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.time-icon {\r\n  width: 14px;\r\n  height: 14px;\r\n  background-color: #ccc;\r\n  border-radius: 7px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.time-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n/* 数据概览卡片 */\r\n.stats-card {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  margin: 0 15px 15px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.stats-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.stats-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.stats-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.action-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-right: 5px;\r\n}\r\n\r\n.action-arrow {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 1px solid #999;\r\n  border-right: 1px solid #999;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.stats-grid {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.stats-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.stats-value {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.progress-section {\r\n  padding-top: 10px;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.progress-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.progress-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.progress-value {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.progress-bar {\r\n  height: 6px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #FF5858, #FF0000);\r\n  border-radius: 3px;\r\n}\r\n\r\n/* 红包设置卡片 */\r\n.settings-card {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  margin: 0 15px 15px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.settings-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.settings-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.settings-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.settings-list {\r\n  padding-top: 5px;\r\n}\r\n\r\n.settings-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 10px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.settings-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.item-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.item-value {\r\n  font-size: 14px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 使用说明卡片 */\r\n.description-card {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  margin: 0 15px 15px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.description-header {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.description-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.description-content {\r\n  padding: 10px;\r\n  background-color: #F9F9F9;\r\n  border-radius: 8px;\r\n}\r\n\r\n.description-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 领取记录卡片 */\r\n.records-card {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  margin: 0 15px 15px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.records-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.records-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.records-action {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.records-list {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.records-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.records-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.user-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  overflow: hidden;\r\n  margin-right: 10px;\r\n}\r\n\r\n.avatar-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.record-info {\r\n  flex: 1;\r\n}\r\n\r\n.user-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  display: block;\r\n}\r\n\r\n.record-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.record-amount {\r\n  text-align: right;\r\n}\r\n\r\n.amount-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #FF5858;\r\n  display: block;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.amount-status {\r\n  font-size: 12px;\r\n  color: #388E3C;\r\n}\r\n\r\n.amount-status.unused {\r\n  color: #999;\r\n}\r\n\r\n.empty-records {\r\n  padding: 30px 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.empty-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 30px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.bottom-actions {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: #fff;\r\n  display: flex;\r\n  padding: 10px 15px;\r\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.action-button {\r\n  flex: 1;\r\n  height: 44px;\r\n  border-radius: 22px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 5px;\r\n}\r\n\r\n.action-button.share {\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n  color: #fff;\r\n}\r\n\r\n.action-button.start {\r\n  background: linear-gradient(135deg, #4ECDC4, #2BAF9F);\r\n  color: #fff;\r\n}\r\n\r\n.action-button.stop {\r\n  background: linear-gradient(135deg, #FF9500, #FF5E3A);\r\n  color: #fff;\r\n}\r\n\r\n.action-button.delete {\r\n  background-color: #F5F7FA;\r\n  color: #FF3B30;\r\n}\r\n\r\n.action-button.duplicate {\r\n  background-color: #F5F7FA;\r\n  color: #666;\r\n}\r\n\r\n.button-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.button-text {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 分享弹窗 */\r\n.share-popup {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1000;\r\n}\r\n\r\n.popup-mask {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.popup-content {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: #fff;\r\n  border-top-left-radius: 16px;\r\n  border-top-right-radius: 16px;\r\n  padding: 15px;\r\n  transform: translateY(0);\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.popup-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.popup-close {\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 20px;\r\n  color: #999;\r\n}\r\n\r\n.share-options {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.share-option {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.option-icon {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.icon-image {\r\n  width: 30px;\r\n  height: 30px;\r\n}\r\n\r\n.option-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.share-qrcode {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 15px 0;\r\n  border-top: 1px solid #eee;\r\n}\r\n\r\n.qrcode-image {\r\n  width: 150px;\r\n  height: 150px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.qrcode-tip {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAiNA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,gBAAgB;AAAA;AAAA,MAGhB,eAAe;AAAA,QACb,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,WAAW;AAAA;AAAA,QAGX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,eAAe;AAAA,QACf,WAAW;AAAA;AAAA,QAGX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,eAAe;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA;AAAA,QAGX,aAAa;AAAA;AAAA,QAGb,SAAS;AAAA,UACP;AAAA,YACE,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,MAAM;AAAA,UACP;AAAA,UACD;AAAA,YACE,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,MAAM;AAAA,UACP;AAAA,UACD;AAAA,YACE,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACD;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,kBAAkB;AAChBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,MAAM;AAAA,QACjC,SAAS,CAAC,QAAQ;AAChB,kBAAQ,IAAI,UAAQ;AAAA,YAClB,KAAK;AACH,mBAAK,WAAU;AACf;AAAA,YACF,KAAK;AACH,mBAAK,SAAQ;AACb;AAAA,YACF,KAAK;AACH,mBAAK,eAAc;AACnB;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,mCAAmC,KAAK,cAAc;AAAA,QAC5D,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACf,WAAK,iBAAiB;AAAA,IACvB;AAAA,IACD,kBAAkB;AAChB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IACD,SAAS,MAAM;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AACD,WAAK,gBAAe;AAAA,IACrB;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD,iBAAK,cAAc,SAAS;AAC5B,iBAAK,cAAc,aAAa;AAAA,UAClC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD,iBAAK,cAAc,SAAS;AAC5B,iBAAK,cAAc,aAAa;AAAA,UAClC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD,uBAAW,MAAM;AACfA,4BAAG,MAAC,aAAY;AAAA,YACjB,GAAE,IAAI;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,qBAAqB;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChbA,GAAG,WAAW,eAAe;"}