/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 56px;
  background-color: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #F0F0F0;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 100;
}
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}
.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
}

/* 强制SVG图标显示 */
.tab-icon svg {
  width: 22px !important;
  height: 22px !important;
  min-width: 22px !important;
  min-height: 22px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  fill: currentColor !important;
}
.tab-text {
  font-size: 10px;
  color: #999999;
}
.tab-item.active .tab-icon {
  color: #1989FA;
}
.tab-item.active .tab-text {
  color: #1989FA;
}

/* 营销中心用橙色 */
.tab-item.active[data-tab=marketing] .tab-icon {
  color: #FF7600;
}
.tab-item.active[data-tab=marketing] .tab-text {
  color: #FF7600;
}

/* 数据概览用蓝色 */
.tab-item.active[data-tab=dashboard] .tab-icon {
  color: #1989FA;
}
.tab-item.active[data-tab=dashboard] .tab-text {
  color: #1989FA;
}

/* 添加底部空间 */
.bottom-space {
  height: 60px;
}