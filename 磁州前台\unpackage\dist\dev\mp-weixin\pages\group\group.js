"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      currentCategory: "hot",
      categories: [
        { id: "hot", name: "热门群" },
        { id: "service", name: "服务群" },
        { id: "bus", name: "拼车群" },
        { id: "community", name: "小区群" },
        { id: "town", name: "老乡群" },
        { id: "village", name: "乡镇群" },
        { id: "shop", name: "商家群" },
        { id: "job", name: "求职群" },
        { id: "parents", name: "家长群" },
        { id: "hobby", name: "兴趣群" }
      ],
      groups: [
        {
          id: 1,
          name: "磁县招聘求职群",
          desc: "本地招聘求职信息发布平台，每日更新...",
          avatar: "/static/images/tabbar/group-avatar1.png",
          category: "hot",
          memberCount: 358
        },
        {
          id: 2,
          name: "磁县生活服务群",
          desc: "本地生活服务信息共享",
          avatar: "/static/images/tabbar/group-avatar2.png",
          category: "service",
          memberCount: 206
        },
        {
          id: 3,
          name: "磁县-邯郸拼车群",
          desc: "磁县到邯郸的拼车信息",
          avatar: "/static/images/tabbar/group-avatar3.png",
          category: "bus",
          memberCount: 129
        },
        {
          id: 4,
          name: "怡馨小区业主群",
          desc: "小区业主交流互助平台",
          avatar: "/static/images/tabbar/group-avatar4.png",
          category: "community",
          memberCount: 186
        },
        {
          id: 5,
          name: "磁县老乡群",
          desc: "磁县老乡交流群",
          avatar: "/static/images/tabbar/group-avatar5.png",
          category: "town",
          memberCount: 412
        },
        {
          id: 6,
          name: "观台镇群",
          desc: "观台镇本地信息分享",
          avatar: "/static/images/tabbar/group-avatar6.png",
          category: "village",
          memberCount: 168
        },
        {
          id: 7,
          name: "磁县家电商家群",
          desc: "家电商家交流平台",
          category: "shop",
          memberCount: 98
        },
        {
          id: 8,
          name: "磁县初中家长群",
          desc: "初中家长信息交流平台",
          category: "parents",
          memberCount: 145
        }
      ],
      isSearching: false
    };
  },
  computed: {
    filteredGroups() {
      return this.groups.filter((group) => group.category === this.currentCategory);
    },
    displayGroups() {
      if (!this.searchKeyword.trim() || !this.isSearching) {
        return this.filteredGroups;
      }
      const keyword = this.searchKeyword.toLowerCase().trim();
      return this.groups.filter(
        (group) => group.name.toLowerCase().includes(keyword) || group.desc.toLowerCase().includes(keyword)
      );
    }
  },
  onLoad() {
  },
  onShareAppMessage() {
    return {
      title: "快来加入磁县拼车群，小区群，老乡群，求职招聘群，家长群把！",
      path: "/pages/group/group"
    };
  },
  methods: {
    changeCategory(categoryId) {
      this.currentCategory = categoryId;
      this.searchKeyword = "";
      this.isSearching = false;
    },
    searchGroups() {
      this.isSearching = !!this.searchKeyword.trim();
    },
    useVoiceSearch() {
      common_vendor.index.showToast({
        title: "语音搜索功能开发中",
        icon: "none"
      });
    },
    joinGroup(group) {
      common_vendor.index.showModal({
        title: "加入群聊",
        content: `是否加入"${group.name}"？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "已发送加群申请",
              icon: "success"
            });
          }
        }
      });
    },
    onShare() {
      if (common_vendor.wx$1 && common_vendor.wx$1.showShareMenu) {
        common_vendor.wx$1.showShareMenu({ withShareTicket: true });
      }
      common_vendor.index.showToast({ title: '请点击右上角"..."进行转发', icon: "none" });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$16,
    b: common_vendor.o((...args) => $options.searchGroups && $options.searchGroups(...args)),
    c: $data.searchKeyword,
    d: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    e: common_assets._imports_1$9,
    f: common_vendor.o((...args) => $options.useVoiceSearch && $options.useVoiceSearch(...args)),
    g: common_assets._imports_0$10,
    h: common_vendor.o((...args) => $options.searchGroups && $options.searchGroups(...args)),
    i: common_vendor.f($data.categories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: $data.currentCategory === category.id ? 1 : "",
        c: index,
        d: common_vendor.o(($event) => $options.changeCategory(category.id), index)
      };
    }),
    j: common_vendor.f($options.displayGroups, (group, index, i0) => {
      return {
        a: group.avatar || "/static/images/tabbar/default-group.png",
        b: common_vendor.t(group.name),
        c: common_vendor.t(group.desc.length > 8 ? group.desc.substring(0, 8) + "..." : group.desc),
        d: common_vendor.t(group.memberCount || "88"),
        e: common_vendor.o(($event) => $options.joinGroup(group), index),
        f: index
      };
    }),
    k: $options.displayGroups.length === 0
  }, $options.displayGroups.length === 0 ? {} : {}, {
    l: common_assets._imports_0$15,
    m: common_vendor.o((...args) => $options.onShare && $options.onShare(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/group/group.js.map
