"use strict";
const api_userApi = require("./userApi.js");
const api_contentApi = require("./contentApi.js");
const api_carpoolApi = require("./carpoolApi.js");
const api_homeApi = require("./homeApi.js");
const api_publishApi = require("./publishApi.js");
const api = {
  // 用户相关API
  user: {
    login: api_userApi.userApi.login.bind(api_userApi.userApi),
    wxLogin: api_userApi.userApi.wxLogin.bind(api_userApi.userApi),
    register: api_userApi.userApi.register.bind(api_userApi.userApi),
    sendSmsCode: api_userApi.userApi.sendSmsCode.bind(api_userApi.userApi),
    getUserInfo: api_userApi.userApi.getUserInfo.bind(api_userApi.userApi),
    updateUserInfo: api_userApi.userApi.updateUserInfo.bind(api_userApi.userApi),
    changePassword: api_userApi.userApi.changePassword.bind(api_userApi.userApi),
    logout: api_userApi.userApi.logout.bind(api_userApi.userApi),
    isLoggedIn: api_userApi.userApi.isLoggedIn.bind(api_userApi.userApi),
    getCurrentUser: api_userApi.userApi.getCurrentUser.bind(api_userApi.userApi),
    getToken: api_userApi.userApi.getToken.bind(api_userApi.userApi)
  },
  // 新闻相关API
  news: {
    getCategories: api_contentApi.contentApi.getNewsCategories.bind(api_contentApi.contentApi),
    getList: api_contentApi.contentApi.getNewsList.bind(api_contentApi.contentApi),
    getDetail: api_contentApi.contentApi.getNewsDetail.bind(api_contentApi.contentApi)
  },
  // 信息相关API
  info: {
    getCategories: api_contentApi.contentApi.getInfoCategories.bind(api_contentApi.contentApi),
    getTopped: api_contentApi.contentApi.getToppedInfo.bind(api_contentApi.contentApi),
    getAll: api_contentApi.contentApi.getInfoList.bind(api_contentApi.contentApi),
    getDetail: api_contentApi.contentApi.getInfoDetail.bind(api_contentApi.contentApi),
    publish: api_contentApi.contentApi.publishInfo.bind(api_contentApi.contentApi)
  },
  // 商家相关API
  business: {
    getCategories: api_contentApi.contentApi.getBusinessCategories.bind(api_contentApi.contentApi),
    getList: api_contentApi.contentApi.getBusinessList.bind(api_contentApi.contentApi),
    getDetail: api_contentApi.contentApi.getBusinessDetail.bind(api_contentApi.contentApi)
  },
  // 拼车相关API
  carpool: {
    getList: api_carpoolApi.carpoolApi.getCarpoolList.bind(api_carpoolApi.carpoolApi),
    getDetail: api_carpoolApi.carpoolApi.getCarpoolDetail.bind(api_carpoolApi.carpoolApi),
    publish: api_carpoolApi.carpoolApi.publishCarpool.bind(api_carpoolApi.carpoolApi),
    update: api_carpoolApi.carpoolApi.updateCarpool.bind(api_carpoolApi.carpoolApi),
    delete: api_carpoolApi.carpoolApi.deleteCarpool.bind(api_carpoolApi.carpoolApi),
    getMy: api_carpoolApi.carpoolApi.getMyCarpoolList.bind(api_carpoolApi.carpoolApi),
    apply: api_carpoolApi.carpoolApi.applyCarpool.bind(api_carpoolApi.carpoolApi),
    handleApplication: api_carpoolApi.carpoolApi.handleApplication.bind(api_carpoolApi.carpoolApi),
    getApplications: api_carpoolApi.carpoolApi.getApplications.bind(api_carpoolApi.carpoolApi),
    top: api_carpoolApi.carpoolApi.topCarpool.bind(api_carpoolApi.carpoolApi),
    refresh: api_carpoolApi.carpoolApi.refreshCarpool.bind(api_carpoolApi.carpoolApi),
    getHotRoutes: api_carpoolApi.carpoolApi.getHotRoutes.bind(api_carpoolApi.carpoolApi),
    search: api_carpoolApi.carpoolApi.searchCarpool.bind(api_carpoolApi.carpoolApi),
    getStats: api_carpoolApi.carpoolApi.getCarpoolStats.bind(api_carpoolApi.carpoolApi),
    report: api_carpoolApi.carpoolApi.reportCarpool.bind(api_carpoolApi.carpoolApi)
  },
  // 通用内容API
  content: {
    uploadImage: api_contentApi.contentApi.uploadImage.bind(api_contentApi.contentApi),
    search: api_contentApi.contentApi.searchContent.bind(api_contentApi.contentApi)
  },
  // 首页相关API - 与后台管理系统互通
  home: {
    getBanners: api_homeApi.homeApi.getBanners.bind(api_homeApi.homeApi),
    getHomeConfig: api_homeApi.homeApi.getHomeConfig.bind(api_homeApi.homeApi),
    getHomeStats: api_homeApi.homeApi.getHomeStats.bind(api_homeApi.homeApi),
    getServiceCategories: api_homeApi.homeApi.getServiceCategories.bind(api_homeApi.homeApi),
    getMerchantRecommend: api_homeApi.homeApi.getMerchantRecommend.bind(api_homeApi.homeApi),
    getCityNews: api_homeApi.homeApi.getCityNews.bind(api_homeApi.homeApi),
    getFeatureConfig: api_homeApi.homeApi.getFeatureConfig.bind(api_homeApi.homeApi),
    recordPageView: api_homeApi.homeApi.recordPageView.bind(api_homeApi.homeApi),
    getAdBanner: api_homeApi.homeApi.getAdBanner.bind(api_homeApi.homeApi)
  },
  // 发布相关API - 与后台管理系统互通
  publish: {
    publishPost: api_publishApi.publishApi.publishPost.bind(api_publishApi.publishApi),
    getPosts: api_publishApi.publishApi.getPosts.bind(api_publishApi.publishApi),
    getPost: api_publishApi.publishApi.getPost.bind(api_publishApi.publishApi),
    updatePost: api_publishApi.publishApi.updatePost.bind(api_publishApi.publishApi),
    deletePost: api_publishApi.publishApi.deletePost.bind(api_publishApi.publishApi),
    topPost: api_publishApi.publishApi.topPost.bind(api_publishApi.publishApi),
    refreshPost: api_publishApi.publishApi.refreshPost.bind(api_publishApi.publishApi),
    addRedPacket: api_publishApi.publishApi.addRedPacket.bind(api_publishApi.publishApi),
    claimRedPacket: api_publishApi.publishApi.claimRedPacket.bind(api_publishApi.publishApi),
    getConfigs: api_publishApi.publishApi.getConfigs.bind(api_publishApi.publishApi),
    getPublishConfigs: api_publishApi.publishApi.getPublishConfigs.bind(api_publishApi.publishApi),
    uploadImage: api_publishApi.publishApi.uploadImage.bind(api_publishApi.publishApi),
    getCategories: api_publishApi.publishApi.getCategories.bind(api_publishApi.publishApi),
    getHomePosts: api_publishApi.publishApi.getHomePosts.bind(api_publishApi.publishApi),
    getCategoryPosts: api_publishApi.publishApi.getCategoryPosts.bind(api_publishApi.publishApi),
    getTopPosts: api_publishApi.publishApi.getTopPosts.bind(api_publishApi.publishApi),
    searchPosts: api_publishApi.publishApi.searchPosts.bind(api_publishApi.publishApi),
    getMyPosts: api_publishApi.publishApi.getMyPosts.bind(api_publishApi.publishApi),
    getPostStats: api_publishApi.publishApi.getPostStats.bind(api_publishApi.publishApi)
  }
};
exports.api = api;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/index.js.map
