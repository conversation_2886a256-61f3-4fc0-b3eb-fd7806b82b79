/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 页面容器 */
.flash-management-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}
.navbar-left {
  width: 40px;
}
.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
}
.title-text {
  font-size: 18px;
  font-weight: 600;
}
.navbar-right {
  width: 40px;
}

/* 顶部操作区 */
.top-actions {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: calc(100vh - 80px);
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}

/* 概览区域 */
.overview-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-title {
  font-size: 17px;
  font-weight: 600;
  color: #333333;
}
.date-picker {
  display: flex;
  align-items: center;
  background: rgba(255, 120, 0, 0.08);
  padding: 4px 8px;
  border-radius: 16px;
}
.date-text {
  font-size: 14px;
  color: #FF7600;
  margin-right: 4px;
}
.arrow-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #FF7600;
}
.overview-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.overview-card {
  width: 48%;
  background: #F9F9F9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}
.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
}
.card-label {
  font-size: 12px;
  color: #666666;
}

/* 秒杀活动管理 */
.flash-sales-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}
.filter-buttons {
  display: flex;
  overflow-x: auto;
  margin-top: 8px;
  width: 100%;
}
.filter-button {
  padding: 6px 12px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 14px;
  color: #666666;
  background: #F5F5F5;
  white-space: nowrap;
  transition: all 0.3s ease;
}
.filter-button.active {
  background: #FF7600;
  color: white;
}

/* 秒杀活动列表 */
.flash-sales-list {
  margin-top: 8px;
}
.flash-sale-item {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #EEEEEE;
}
.item-main {
  display: flex;
}
.item-image-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin-right: 12px;
  overflow: hidden;
  border-radius: 4px;
}
.item-image {
  width: 100%;
  height: 100%;
}
.item-status {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 0 0 8px;
  color: white;
  z-index: 2;
}
.status-active {
  background: #34C759;
}
.status-upcoming {
  background: #007AFF;
}
.status-ended {
  background: #8E8E93;
}
.item-content {
  flex: 1;
}
.item-name {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6px;
  line-height: 1.3;
}
.item-price {
  margin-bottom: 6px;
}
.price-now {
  font-size: 17px;
  font-weight: 600;
  color: #FF3B30;
  margin-right: 8px;
}
.price-original {
  font-size: 14px;
  color: #999999;
  text-decoration: line-through;
}
.item-stats {
  display: flex;
  margin-bottom: 6px;
}
.stat-item {
  margin-right: 16px;
}
.stat-value {
  font-size: 14px;
  color: #333333;
  margin-right: 4px;
}
.stat-label {
  font-size: 12px;
  color: #999999;
}
.item-time {
  font-size: 12px;
  color: #666666;
}
.item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  border-top: 1px solid #F5F5F5;
  padding-top: 12px;
}
.action-button {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  background: #F5F5F5;
}
.action-button.edit {
  background: rgba(94, 92, 230, 0.1);
}
.action-button.share {
  background: rgba(52, 199, 89, 0.1);
}
.action-button.delete {
  background: rgba(255, 59, 48, 0.1);
}

/* 空状态 */
.empty-state {
  padding: 32px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-image {
  width: 100px;
  height: 100px;
  margin-bottom: 16px;
}
.empty-text {
  font-size: 17px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
}
.empty-desc {
  font-size: 14px;
  color: #666666;
  text-align: center;
  margin-bottom: 20px;
}

/* 数据分析 */
.analytics-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.time-selector {
  display: flex;
  margin-top: 8px;
  background: #F5F5F5;
  border-radius: 16px;
  overflow: hidden;
  align-self: flex-end;
}
.time-option {
  padding: 6px 12px;
  font-size: 14px;
  color: #666666;
  text-align: center;
}
.time-option.active {
  background: #FF7600;
  color: white;
}
.analytics-chart {
  padding: 12px 0;
}
.chart-legend {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}
.legend-item {
  display: flex;
  align-items: center;
  margin: 0 16px;
}
.legend-color {
  width: 16px;
  height: 8px;
  border-radius: 4px;
  margin-right: 8px;
}
.legend-color.sales {
  background: #FF7600;
}
.legend-color.orders {
  background: #007AFF;
}
.legend-text {
  font-size: 12px;
  color: #666666;
}
.chart-container {
  height: 200px;
  padding: 16px 0;
}
.chart-bars {
  display: flex;
  align-items: flex-end;
  height: 150px;
  justify-content: space-between;
  padding: 0 8px;
}
.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 14%;
}
.bar-group {
  display: flex;
  width: 100%;
  height: 150px;
  align-items: flex-end;
  justify-content: center;
}
.bar {
  border-radius: 2px;
}
.bar.sales {
  width: 12px;
  background: #FF7600;
  margin-right: 4px;
}
.bar.orders {
  width: 12px;
  background: #007AFF;
}
.bar-label {
  font-size: 10px;
  color: #999999;
  margin-top: 8px;
}

/* 秒杀活动技巧 */
.tips-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.tips-list {
  margin-top: 8px;
}
.tip-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}
.tip-item:last-child {
  border-bottom: none;
}
.tip-icon {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  color: #FF7600;
}
.tip-content {
  flex: 1;
}
.tip-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}
.tip-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}
@media screen and (min-width: 768px) {
.overview-card {
    width: 23%;
}
.filter-buttons {
    width: auto;
    margin-top: 0;
}
}