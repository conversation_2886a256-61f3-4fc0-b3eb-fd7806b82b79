<template>
  <view class="contact-history-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">联系历史</text>
        </view>
        <view class="right-action">
          <!-- 预留位置 -->
        </view>
      </view>
    </view>
    
    <!-- 联系历史列表 -->
    <view class="history-list" :style="{ paddingTop: (statusBarHeight + 44 + 20) + 'px' }">
      <view class="empty-state" v-if="contactHistory.length === 0">
        <image src="/static/images/icons/empty-history.png" class="empty-icon"></image>
        <text class="empty-text">暂无联系历史</text>
      </view>
      
      <view class="history-item" v-for="(item, index) in contactHistory" :key="index">
        <view class="history-header">
          <view class="driver-info">
            <text class="driver-name">{{getDriverName(item)}}</text>
            <text class="driver-phone">{{formatPhone(item.phoneNumber)}}</text>
          </view>
          <text class="contact-time">{{formatDate(item.contactTime)}}</text>
        </view>
        
        <view class="trip-info">
          <view class="route-info">
            <view class="route-point">
              <view class="point start"></view>
              <text class="point-text">{{item.startLocation}}</text>
            </view>
            <view class="route-line"></view>
            <view class="route-point">
              <view class="point end"></view>
              <text class="point-text">{{item.endLocation}}</text>
            </view>
          </view>
          <view class="time-info">
            <text class="departure-date">{{item.departureDate}}</text>
            <text class="departure-time">{{item.departureTime}}</text>
          </view>
        </view>
        
        <view class="action-buttons">
          <button class="action-btn call" @click="callDriver(item)">
            <image src="/static/images/icons/phone.png" class="btn-icon"></image>
            <text>再次联系</text>
          </button>
          <button class="action-btn rate" @click="rateDriver(item)">
            <image src="/static/images/icons/star.png" class="btn-icon"></image>
            <text>{{hasRated(item) ? '修改评价' : '评价司机'}}</text>
          </button>
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';

// 状态栏高度
const statusBarHeight = ref(20);

// 联系历史和评价历史数据
const contactHistory = ref([]);
const ratingsHistory = ref([]);

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight || 20;
  
  loadContactHistory();
  loadRatingsHistory();
  
  // 模拟一些测试数据
  if (contactHistory.value.length === 0) {
    contactHistory.value = [
      {
        driverId: '1001',
        carpoolId: '2001',
        phoneNumber: '13812345678',
        startLocation: '磁县政府',
        endLocation: '邯郸火车站',
        departureDate: '2023-10-20',
        departureTime: '09:30',
        contactTime: new Date().toISOString()
      },
      {
        driverId: '1002',
        carpoolId: '2002',
        phoneNumber: '13987654321',
        startLocation: '磁县老城区',
        endLocation: '邯郸科技学院',
        departureDate: '2023-10-21',
        departureTime: '14:00',
        contactTime: new Date(Date.now() - 86400000).toISOString() // 昨天
      }
    ];
  }
});

onShow(() => {
  // 页面显示时重新加载数据，以防评价后返回
  loadContactHistory();
  loadRatingsHistory();
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 加载联系历史
const loadContactHistory = () => {
  const history = uni.getStorageSync('contactHistory') || [];
  // 按联系时间倒序排序
  contactHistory.value = history.sort((a, b) => {
    return new Date(b.contactTime) - new Date(a.contactTime);
  });
};

// 加载评价历史
const loadRatingsHistory = () => {
  ratingsHistory.value = uni.getStorageSync('ratingsHistory') || [];
};

// 获取司机姓名
const getDriverName = (item) => {
  const driversInfo = uni.getStorageSync('driversInfo') || {};
  if (driversInfo[item.driverId]) {
    return driversInfo[item.driverId].name;
  }
  return '司机' + item.phoneNumber.substr(-4);
};

// 格式化电话号码
const formatPhone = (phone) => {
  if (!phone) return '';
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    // 今天，显示时间
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `今天 ${hours}:${minutes}`;
  } else if (diffDays === 1) {
    return '昨天';
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
};

// 再次联系司机
const callDriver = (item) => {
  // 更新联系时间
  const contactHistoryData = uni.getStorageSync('contactHistory') || [];
  const index = contactHistoryData.findIndex(contact => 
    contact.driverId === item.driverId && 
    contact.carpoolId === item.carpoolId
  );
  
  if (index !== -1) {
    contactHistoryData[index].contactTime = new Date().toISOString();
    uni.setStorageSync('contactHistory', contactHistoryData);
  }
  
  // 拨打电话
  uni.makePhoneCall({
    phoneNumber: item.phoneNumber,
    fail: (err) => {
      console.error('拨打电话失败', err);
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

// 评价司机
const rateDriver = (item) => {
  console.log('点击评价司机按钮', item);
  // 确保所有参数都存在
  if (!item.driverId || !item.phoneNumber) {
    uni.showToast({
      title: '缺少司机信息，无法评价',
      icon: 'none'
    });
    return;
  }
  
  // 使用正确的页面路径格式
  const url = `/carpool-package/pages/carpool/my/create-rating?driverId=${item.driverId}&phoneNumber=${item.phoneNumber}&carpoolId=${item.carpoolId}&startLocation=${encodeURIComponent(item.startLocation)}&endLocation=${encodeURIComponent(item.endLocation)}&departureTime=${item.departureTime}&departureDate=${item.departureDate}`;
  console.log('尝试跳转到路径:', url);
  
  uni.navigateTo({
    url: url,
    success: (res) => {
      console.log('导航到评价页面成功');
    },
    fail: (err) => {
      console.error('导航到评价页面失败', err);
      // 尝试使用不同的路径格式作为备选
      const alternativeUrl = `../create-rating?driverId=${item.driverId}&phoneNumber=${item.phoneNumber}&carpoolId=${item.carpoolId}&startLocation=${encodeURIComponent(item.startLocation)}&endLocation=${encodeURIComponent(item.endLocation)}&departureTime=${item.departureTime}&departureDate=${item.departureDate}`;
      console.log('尝试备选路径:', alternativeUrl);
      
      uni.navigateTo({
        url: alternativeUrl,
        success: () => console.log('使用备选路径成功导航'),
        fail: (error) => {
          console.error('备选路径也失败', error);
          uni.showToast({
            title: '跳转评价页面失败',
            icon: 'none'
          });
        }
      });
    }
  });
};

// 检查是否已评价
const hasRated = (item) => {
  return ratingsHistory.value.some(rating => 
    rating.driverId === item.driverId && 
    rating.carpoolId === item.carpoolId
  );
};
</script>

<style lang="scss">
.contact-history-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 历史列表 */
.history-list {
  position: relative;
  padding: 20rpx 32rpx;
}

.history-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.driver-info {
  display: flex;
  flex-direction: column;
}

.driver-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.driver-phone {
  font-size: 24rpx;
  color: #666666;
}

.contact-time {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 行程信息 */
.trip-info {
  background-color: #F9F9F9;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.route-info {
  margin-bottom: 12rpx;
}

.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.point {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  margin-right: 12rpx;
}

.point.start {
  background-color: #34C759;
}

.point.end {
  background-color: #FF3B30;
}

.point-text {
  font-size: 26rpx;
  color: #333333;
}

.route-line {
  width: 2rpx;
  height: 30rpx;
  background-color: #DDDDDD;
  margin-left: 7rpx;
  margin-bottom: 12rpx;
}

.time-info {
  display: flex;
  justify-content: space-between;
}

.departure-date, .departure-time {
  font-size: 24rpx;
  color: #666666;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  margin-left: 16rpx;
  font-size: 24rpx;
}

.action-btn.call {
  background-color: #F2F7FD;
  color: #0A84FF;
  border: 1px solid #0A84FF;
}

.action-btn.rate {
  background-color: #0A84FF;
  color: #FFFFFF;
}

.btn-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.action-btn.rate .btn-icon {
  filter: brightness(0) invert(1);
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}
</style> 