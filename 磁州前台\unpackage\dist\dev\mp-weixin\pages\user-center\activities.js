"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      currentTab: 0,
      swiperHeight: 500,
      tabs: [
        { name: "全部活动", count: 0 },
        { name: "我的拼团", count: 0 },
        { name: "我的红包", count: 0 }
      ],
      allActivities: [],
      grouponActivities: [],
      redpacketActivities: [],
      hasMore: [true, true, true],
      loadingStatus: [false, false, false],
      page: [1, 1, 1]
    };
  },
  onLoad(options) {
    if (options && options.tab) {
      this.currentTab = parseInt(options.tab);
    }
    this.loadAllActivities();
    this.loadGrouponActivities();
    this.loadRedpacketActivities();
    this.calcSwiperHeight();
  },
  onReady() {
    this.calcSwiperHeight();
  },
  methods: {
    // 计算swiper高度
    calcSwiperHeight() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".content").boundingClientRect((data) => {
        const windowHeight = common_vendor.index.getSystemInfoSync().windowHeight;
        const navHeight = common_vendor.index.getSystemInfoSync().statusBarHeight + 44;
        const tabHeight = 50;
        this.swiperHeight = windowHeight - navHeight - tabHeight - 20;
      }).exec();
    },
    // 切换标签
    switchTab(index) {
      this.currentTab = index;
    },
    // 滑动切换
    onSwiperChange(e) {
      this.currentTab = e.detail.current;
    },
    // 加载全部活动
    loadAllActivities() {
      if (!this.hasMore[0] || this.loadingStatus[0])
        return;
      this.loadingStatus[0] = true;
      setTimeout(() => {
        const activities = [
          {
            id: 1,
            type: "groupon",
            title: "新鲜水果大礼包限时拼团",
            image: "/static/images/activity-1.jpg",
            status: "ongoing",
            time: "2023-05-03 14:23",
            shop: {
              id: 101,
              name: "鲜果日记",
              logo: "/static/images/shop-1.jpg"
            }
          },
          {
            id: 2,
            type: "redpacket",
            title: "新店开业红包大派送",
            image: "/static/images/activity-2.jpg",
            status: "used",
            time: "2023-05-02 10:15",
            shop: {
              id: 102,
              name: "时尚精品店",
              logo: "/static/images/shop-2.jpg"
            }
          },
          {
            id: 3,
            type: "coupon",
            title: "满100减30优惠券",
            image: "/static/images/activity-4.jpg",
            status: "unused",
            time: "2023-05-01 09:45",
            shop: {
              id: 104,
              name: "日用百货",
              logo: "/static/images/shop-4.jpg"
            }
          }
        ];
        this.allActivities = [...this.allActivities, ...activities];
        this.hasMore[0] = this.page[0] < 2;
        this.loadingStatus[0] = false;
        this.page[0]++;
        this.tabs[0].count = this.allActivities.length;
      }, 500);
    },
    // 加载拼团活动
    loadGrouponActivities() {
      if (!this.hasMore[1] || this.loadingStatus[1])
        return;
      this.loadingStatus[1] = true;
      setTimeout(() => {
        const activities = [
          {
            id: 1,
            title: "新鲜水果大礼包限时拼团",
            image: "/static/images/activity-1.jpg",
            status: "ongoing",
            currentCount: 2,
            needCount: 3,
            leftTime: "11:24:36",
            time: "2023-05-03 14:23",
            shop: {
              id: 101,
              name: "鲜果日记",
              logo: "/static/images/shop-1.jpg"
            }
          },
          {
            id: 5,
            title: "美食团购大优惠",
            image: "/static/images/activity-5.jpg",
            status: "success",
            currentCount: 3,
            needCount: 3,
            leftTime: "00:00:00",
            time: "2023-04-28 19:45",
            shop: {
              id: 105,
              name: "美食广场",
              logo: "/static/images/shop-5.jpg"
            }
          }
        ];
        this.grouponActivities = [...this.grouponActivities, ...activities];
        this.hasMore[1] = this.page[1] < 2;
        this.loadingStatus[1] = false;
        this.page[1]++;
        this.tabs[1].count = this.grouponActivities.length;
      }, 500);
    },
    // 加载红包活动
    loadRedpacketActivities() {
      if (!this.hasMore[2] || this.loadingStatus[2])
        return;
      this.loadingStatus[2] = true;
      setTimeout(() => {
        const activities = [
          {
            id: 2,
            title: "新店开业红包大派送",
            image: "/static/images/activity-2.jpg",
            status: "used",
            amount: "15.00",
            desc: "已使用",
            time: "2023-05-02 10:15",
            validTime: "2023-05-10",
            shop: {
              id: 102,
              name: "时尚精品店",
              logo: "/static/images/shop-2.jpg"
            }
          },
          {
            id: 6,
            title: "周末红包雨",
            image: "/static/images/activity-6.jpg",
            status: "unused",
            amount: "10.00",
            desc: "满100可用",
            time: "2023-04-30 16:28",
            validTime: "2023-05-15",
            shop: {
              id: 106,
              name: "数码专营店",
              logo: "/static/images/shop-6.jpg"
            }
          },
          {
            id: 8,
            title: "五一假期红包",
            image: "/static/images/activity-8.jpg",
            status: "expired",
            amount: "20.00",
            desc: "已过期",
            time: "2023-04-25 09:10",
            validTime: "2023-05-01",
            shop: {
              id: 108,
              name: "生活超市",
              logo: "/static/images/shop-8.jpg"
            }
          }
        ];
        this.redpacketActivities = [...this.redpacketActivities, ...activities];
        this.hasMore[2] = this.page[2] < 2;
        this.loadingStatus[2] = false;
        this.page[2]++;
        this.tabs[2].count = this.redpacketActivities.length;
      }, 500);
    },
    // 加载更多
    loadMore(tabIndex) {
      switch (tabIndex) {
        case 0:
          this.loadAllActivities();
          break;
        case 1:
          this.loadGrouponActivities();
          break;
        case 2:
          this.loadRedpacketActivities();
          break;
      }
    },
    // 查看活动详情
    viewActivity(item) {
      let url = "";
      if (item.type === "groupon" || this.currentTab === 1) {
        url = `/pages/user-center/groupon-detail?id=${item.id}`;
      } else if (item.type === "redpacket" || this.currentTab === 2) {
        url = `/pages/user-center/redpacket-detail?id=${item.id}`;
      } else if (item.type === "coupon") {
        url = `/pages/user-center/coupon-detail?id=${item.id}`;
      } else {
        url = `/pages/activity/detail?id=${item.id}`;
      }
      common_vendor.index.navigateTo({ url });
    },
    // 获取活动类型名称
    getTypeName(type) {
      const typeMap = {
        "groupon": "拼团",
        "redpacket": "红包",
        "seckill": "秒杀",
        "coupon": "优惠券"
      };
      return typeMap[type] || "活动";
    },
    // 获取活动状态文本
    getStatusText(status) {
      const statusMap = {
        "ongoing": "进行中",
        "success": "已成功",
        "failed": "已失败",
        "used": "已使用",
        "unused": "未使用",
        "expired": "已过期"
      };
      return statusMap[status] || "未知";
    }
  }
};
if (!Array) {
  const _component_template = common_vendor.resolveComponent("template");
  const _component_cu_custom = common_vendor.resolveComponent("cu-custom");
  (_component_template + _component_cu_custom)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      bgColor: "bg-white",
      isBack: true
    }),
    b: common_vendor.f($data.tabs, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.name),
        b: tab.count > 0
      }, tab.count > 0 ? {
        c: common_vendor.t(tab.count)
      } : {}, {
        d: index,
        e: $data.currentTab === index ? 1 : "",
        f: common_vendor.o(($event) => $options.switchTab(index), index)
      });
    }),
    c: common_vendor.f($data.allActivities, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t($options.getTypeName(item.type)),
        c: common_vendor.n("tag-" + item.type),
        d: common_vendor.t($options.getStatusText(item.status)),
        e: common_vendor.n("status-" + item.status),
        f: common_vendor.t(item.title),
        g: item.shop.logo,
        h: common_vendor.t(item.shop.name),
        i: common_vendor.t(item.time),
        j: index,
        k: common_vendor.o(($event) => $options.viewActivity(item), index)
      };
    }),
    d: $data.loadingStatus[0]
  }, $data.loadingStatus[0] ? {} : {}, {
    e: !$data.hasMore[0] && $data.allActivities.length > 0
  }, !$data.hasMore[0] && $data.allActivities.length > 0 ? {} : {}, {
    f: $data.allActivities.length === 0
  }, $data.allActivities.length === 0 ? {
    g: common_assets._imports_0$22
  } : {}, {
    h: common_vendor.o(($event) => $options.loadMore(0)),
    i: common_vendor.f($data.grouponActivities, (item, index, i0) => {
      return common_vendor.e({
        a: item.image,
        b: common_vendor.t($options.getStatusText(item.status)),
        c: common_vendor.n("status-" + item.status),
        d: common_vendor.t(item.title),
        e: item.shop.logo,
        f: common_vendor.t(item.shop.name),
        g: common_vendor.t(item.currentCount),
        h: common_vendor.t(item.needCount),
        i: item.currentCount / item.needCount * 100 + "%",
        j: common_vendor.t(item.time),
        k: item.status === "ongoing"
      }, item.status === "ongoing" ? {
        l: common_vendor.t(item.leftTime)
      } : {}, {
        m: index,
        n: common_vendor.o(($event) => $options.viewActivity(item), index)
      });
    }),
    j: $data.loadingStatus[1]
  }, $data.loadingStatus[1] ? {} : {}, {
    k: !$data.hasMore[1] && $data.grouponActivities.length > 0
  }, !$data.hasMore[1] && $data.grouponActivities.length > 0 ? {} : {}, {
    l: $data.grouponActivities.length === 0
  }, $data.grouponActivities.length === 0 ? {
    m: common_assets._imports_1$16
  } : {}, {
    n: common_vendor.o(($event) => $options.loadMore(1)),
    o: common_vendor.f($data.redpacketActivities, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t($options.getStatusText(item.status)),
        c: common_vendor.n("status-" + item.status),
        d: common_vendor.t(item.title),
        e: item.shop.logo,
        f: common_vendor.t(item.shop.name),
        g: common_vendor.t(item.amount),
        h: common_vendor.t(item.desc),
        i: common_vendor.t(item.time),
        j: common_vendor.t(item.validTime),
        k: index,
        l: common_vendor.o(($event) => $options.viewActivity(item), index)
      };
    }),
    p: $data.loadingStatus[2]
  }, $data.loadingStatus[2] ? {} : {}, {
    q: !$data.hasMore[2] && $data.redpacketActivities.length > 0
  }, !$data.hasMore[2] && $data.redpacketActivities.length > 0 ? {} : {}, {
    r: $data.redpacketActivities.length === 0
  }, $data.redpacketActivities.length === 0 ? {
    s: common_assets._imports_2$11
  } : {}, {
    t: common_vendor.o(($event) => $options.loadMore(2)),
    v: $data.currentTab,
    w: common_vendor.o((...args) => $options.onSwiperChange && $options.onSwiperChange(...args)),
    x: $data.swiperHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user-center/activities.js.map
