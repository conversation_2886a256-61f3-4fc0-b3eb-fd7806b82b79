<template>
  <!-- 活动卡片组件 - 苹果风格设计 -->
  <view class="activity-card" :class="[`activity-type-${item.type}`, {'activity-ended': item.status === 'ended'}]">
    <!-- 活动类型标签（移至左上角） -->
    <view class="activity-type-tag" :class="`type-${item.type}`">
      <text class="type-text">{{typeText}}</text>
    </view>
    
    <!-- 活动封面图 -->
    <!-- 活动封面图 -->
    <!-- 活动封面图 -->
    <view class="activity-cover-container">
      <image :src="item.coverImage" class="activity-cover" mode="aspectFill"></image>
      
      <!-- 分销标识 - 新设计，左下角斜角标签 -->
      <view class="distribution-badge-new">
        <view class="distribution-badge-inner">
          <view class="distribution-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"></path>
            </svg>
          </view>
          <text class="distribution-text">推广赚佣金</text>
        </view>
      </view>
      
    <!-- 置顶标签（右上角） -->
    <view class="top-badge paid-badge" v-if="item.isPaidTop">
      <view class="top-badge-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"></path>
        </svg>
      </view>
      <text class="top-badge-text">付费置顶</text>
    </view>
    
    <view class="top-badge ad-badge" v-if="item.isAdTop">
      <view class="top-badge-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
          <path d="M16 3L12 7 8 3"></path>
        </svg>
      </view>
      <text class="top-badge-text">广告置顶</text>
    </view>
      
      <!-- 收藏按钮 -->
      <view class="favorite-btn" @click.stop="toggleFavorite">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="item.isFavorite ? 'currentColor' : 'none'" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
      </view>
      
      <!-- 活动热度 -->
      <view class="activity-hot" v-if="item.hot">
        <view class="hot-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M8 2h8l4 10-4 10H8L4 12 8 2z"></path>
          </svg>
        </view>
        <text class="hot-text">热门</text>
      </view>
    </view>
    
    <!-- 活动内容区 -->
    <view class="activity-content">
      <!-- 活动标题 -->
      <view class="activity-title-row">
        <text class="activity-title">{{item.title}}</text>
      </view>
      
      <!-- 活动时间 -->
      <view class="activity-time">
        <view class="time-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
        </view>
        <text class="time-text">{{item.startTime}} - {{item.endTime}}</text>
      </view>
      
      <!-- 活动地点 -->
      <view class="activity-location">
        <view class="location-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
        </view>
        <text class="location-text">{{item.location}}</text>
      </view>
      
      <!-- 活动特定信息区域 -->
      <view class="activity-special-info">
        <!-- 插槽：特定活动类型的特殊信息 -->
        <slot name="special-info">
          <!-- 拼团活动特定信息 -->
          <view v-if="item.type === 'groupBuy'" class="group-buy-info">
            <view class="price-info">
              <text class="current-price">¥{{item.groupPrice}}</text>
              <text class="original-price">¥{{item.originalPrice}}</text>
            </view>
            <view class="group-progress">
              <view class="progress-bar">
                <view class="progress-inner" :style="{width: groupProgress + '%'}"></view>
              </view>
              <text class="progress-text">还差{{item.groupSize - item.currentGroupMembers}}人成团</text>
            </view>
          </view>
          
          <!-- 秒杀活动特定信息 -->
          <view v-else-if="item.type === 'flashSale'" class="flash-sale-info">
            <view class="price-info">
              <text class="current-price">¥{{item.salePrice}}</text>
              <text class="original-price">¥{{item.originalPrice}}</text>
              <text class="discount">{{discountPercent}}折</text>
            </view>
            <view class="countdown" v-if="item.status === 'ongoing' || item.status === 'upcoming'">
              <text class="countdown-label">{{item.status === 'ongoing' ? '距结束' : '距开始'}}</text>
              <view class="countdown-time">
                <text class="time-block">{{countdownHours}}</text>
                <text class="time-separator">:</text>
                <text class="time-block">{{countdownMinutes}}</text>
                <text class="time-separator">:</text>
                <text class="time-block">{{countdownSeconds}}</text>
              </view>
            </view>
            <view class="stock-info">
              <view class="stock-progress">
                <view class="progress-inner" :style="{width: stockProgress + '%'}"></view>
              </view>
              <text class="stock-text">已抢{{item.soldCount}}/{{item.totalStock}}</text>
            </view>
          </view>
          
          <!-- 优惠券活动特定信息 -->
          <view v-else-if="item.type === 'coupon'" class="coupon-info">
            <view class="coupon-value">
              <text class="value-symbol" v-if="item.couponType === 'cash'">¥</text>
              <text class="value-number">{{item.couponValue}}</text>
              <text class="value-unit" v-if="item.couponType === 'discount'">折</text>
            </view>
            <view class="coupon-condition" v-if="item.couponCondition">
              <text class="condition-text">{{item.couponCondition}}</text>
            </view>
            <view class="coupon-validity">
              <text class="validity-text">有效期至: {{item.couponValidity}}</text>
            </view>
          </view>
          
          <!-- 满减活动特定信息 -->
          <view v-else-if="item.type === 'discount'" class="discount-info">
            <view class="discount-rules">
              <view class="rule-item" v-for="(rule, index) in item.discountRules" :key="index">
                <text class="rule-text">满{{rule.threshold}}减{{rule.discount}}</text>
              </view>
            </view>
            <view class="merchant-count">
              <text class="merchant-text">{{item.merchantCount}}家商家参与</text>
            </view>
          </view>
        </slot>
      </view>
      
      <!-- 活动参与信息 -->
      <view class="activity-participation">
        <view class="participants" v-if="item.participants && item.participants.length > 0">
          <view class="avatar-group">
            <image 
              v-for="(participant, index) in displayParticipants" 
              :key="index" 
              :src="participant.avatar" 
              class="participant-avatar"
              :style="{zIndex: displayParticipants.length - index}"
            ></image>
            <view class="more-participants" v-if="item.participants.length > maxDisplayParticipants">
              <text>+{{item.participants.length - maxDisplayParticipants}}</text>
            </view>
          </view>
          <text class="participant-count">{{item.participants.length}}人参与</text>
        </view>
          
          <!-- 分销收益标识 - 新设计 -->
          <view class="distribution-profit-new">
            <view class="profit-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </view>
            <text class="profit-text-new">最高赚¥{{getDistributionProfit()}}</text>
          </view>
        
        <!-- 活动操作按钮 -->
        <view class="activity-action">
          <view class="action-btn" :class="actionBtnClass" @click.stop="handleActionClick">
            <text class="action-text">{{actionBtnText}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['favorite', 'action']);

// 最大显示参与人数
const maxDisplayParticipants = 3;

// 显示的参与者头像
const displayParticipants = computed(() => {
  if (!props.item.participants) return [];
  return props.item.participants.slice(0, maxDisplayParticipants);
});

// 活动类型文本
const typeText = computed(() => {
  switch(props.item.type) {
    case 'groupBuy':
      return '拼团';
    case 'flashSale':
      return '秒杀';
    case 'coupon':
      return '优惠券';
    case 'discount':
      return '满减';
    default:
      return '活动';
  }
});

// 拼团进度
const groupProgress = computed(() => {
  if (props.item.type !== 'groupBuy') return 0;
  return (props.item.currentGroupMembers / props.item.groupSize) * 100;
});

// 库存进度
const stockProgress = computed(() => {
  if (props.item.type !== 'flashSale') return 0;
  return (props.item.soldCount / props.item.totalStock) * 100;
});

// 折扣百分比
const discountPercent = computed(() => {
  if (props.item.type !== 'flashSale') return '';
  return ((props.item.salePrice / props.item.originalPrice) * 10).toFixed(1);
});

// 倒计时相关
const countdownHours = ref('00');
const countdownMinutes = ref('00');
const countdownSeconds = ref('00');

// 初始化倒计时
if (props.item.type === 'flashSale' && (props.item.status === 'ongoing' || props.item.status === 'upcoming')) {
  // 实际项目中应该使用定时器更新倒计时
  // 这里仅作为示例
  countdownHours.value = '01';
  countdownMinutes.value = '30';
  countdownSeconds.value = '45';
}

// 操作按钮文本
const actionBtnText = computed(() => {
  if (props.item.status === 'ended') return '查看详情';
  
  switch(props.item.type) {
    case 'groupBuy':
      return props.item.status === 'upcoming' ? '预约拼团' : '立即拼团';
    case 'flashSale':
      return props.item.status === 'upcoming' ? '提醒我' : '立即抢购';
    case 'coupon':
      return '立即领取';
    case 'discount':
      return '去使用';
    default:
      return '立即参与';
  }
});

// 操作按钮样式类
const actionBtnClass = computed(() => {
  if (props.item.status === 'ended') return 'btn-disabled';
  return `btn-${props.item.type}`;
});

// 计算分销收益
function getDistributionProfit() {
  // 根据不同类型的活动计算不同的分销收益
  switch(props.item.type) {
    case 'groupBuy':
      return (props.item.groupPrice * 0.1).toFixed(2);
    case 'flashSale':
      return (props.item.salePrice * 0.15).toFixed(2);
    case 'coupon':
      return props.item.couponType === 'cash' ? (props.item.couponValue * 0.05).toFixed(2) : '5.00';
    case 'discount':
      return props.item.discountRules && props.item.discountRules.length > 0 
        ? (props.item.discountRules[0].discount * 0.2).toFixed(2) 
        : '8.00';
    default:
      return '10.00';
  }
}

// 收藏切换
function toggleFavorite(event) {
  event.stopPropagation();
  emit('favorite', props.item.id);
}

// 操作按钮点击
function handleActionClick(event) {
  event.stopPropagation();
  emit('action', {
    id: props.item.id,
    type: props.item.type,
    status: props.item.status
  });
}
</script>

<style scoped>
/* 活动卡片基础样式 - 苹果风格 */
.activity-card {
  position: relative;
  margin: 20rpx;
  border-radius: 35rpx;
  background-color: #ffffff;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08), 0 2rpx 10rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 分销标识 - 新设计（左下角斜角标签） */
.distribution-badge-new {
  position: absolute;
  left: 0;
  bottom: 60rpx;
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  padding: 6rpx 16rpx;
  border-top-right-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);
  z-index: 10;
  display: flex;
  align-items: center;
}

.distribution-badge-inner {
  display: flex;
  align-items: center;
}

.distribution-icon {
  margin-right: 6rpx;
  color: #FFFFFF;
}

.distribution-text {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 活动卡片悬停效果 */
.activity-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.06);
}

/* 已结束活动样式 */
.activity-ended {
  opacity: 0.7;
}

/* 活动类型标签 - 移至左上角 */
.activity-type-tag {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
}

.type-groupBuy {
  background-color: rgba(52, 199, 89, 0.9);
  color: #ffffff;
}

.type-flashSale {
  background-color: rgba(255, 59, 48, 0.9);
  color: #ffffff;
}

.type-coupon {
  background-color: rgba(255, 149, 0, 0.9);
  color: #ffffff;
}

.type-discount {
  background-color: rgba(90, 200, 250, 0.9);
  color: #ffffff;
}

.type-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 置顶标签样式 */
.top-badge {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-bottom-left-radius: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.paid-badge {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  box-shadow: 0 8rpx 20rpx rgba(255, 59, 48, 0.2), 0 4rpx 8rpx rgba(255, 59, 48, 0.1);
}

.ad-badge {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.2), 0 4rpx 8rpx rgba(0, 122, 255, 0.1);
}

.top-badge-icon {
  color: #FFFFFF;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.top-badge-text {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 活动封面 */
.activity-cover-container {
  position: relative;
  width: 100%;
  height: 300rpx;
  overflow: hidden;
}

.activity-cover {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.activity-card:active .activity-cover {
  transform: scale(1.05);
}

/* 收藏按钮 */
.favorite-btn {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff3b30;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* 热门标签 */
.activity-hot {
  position: absolute;
  left: 20rpx;
  bottom: 20rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: rgba(255, 59, 48, 0.9);
  color: #ffffff;
  display: flex;
  align-items: center;
  z-index: 10;
}

.hot-icon {
  margin-right: 6rpx;
}

.hot-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 活动内容区 */
.activity-content {
  padding: 24rpx;
}

/* 活动标题 */
.activity-title-row {
  margin-bottom: 16rpx;
}

.activity-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 活动时间 */
.activity-time {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.time-icon {
  margin-right: 8rpx;
  color: #8e8e93;
}

.time-text {
  font-size: 24rpx;
  color: #8e8e93;
}

/* 活动地点 */
.activity-location {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.location-icon {
  margin-right: 8rpx;
  color: #8e8e93;
}

.location-text {
  font-size: 24rpx;
  color: #8e8e93;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90%;
}

/* 活动特定信息区域 */
.activity-special-info {
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 20rpx;
}

/* 拼团活动特定样式 */
.group-buy-info {
  display: flex;
  flex-direction: column;
}

.price-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.current-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff3b30;
  margin-right: 12rpx;
}

.original-price {
  font-size: 24rpx;
  color: #8e8e93;
  text-decoration: line-through;
}

.discount {
  font-size: 24rpx;
  color: #ff3b30;
  margin-left: 12rpx;
  padding: 2rpx 8rpx;
  background-color: rgba(255, 59, 48, 0.1);
  border-radius: 10rpx;
}

.group-progress {
  margin-top: 12rpx;
}

.progress-bar {
  height: 10rpx;
  background-color: #e5e5ea;
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-inner {
  height: 100%;
  background-color: #34c759;
  border-radius: 5rpx;
}

.progress-text {
  font-size: 22rpx;
  color: #34c759;
}

/* 秒杀活动特定样式 */
.flash-sale-info {
  display: flex;
  flex-direction: column;
}

.countdown {
  display: flex;
  align-items: center;
  margin: 12rpx 0;
}

.countdown-label {
  font-size: 24rpx;
  color: #8e8e93;
  margin-right: 12rpx;
}

.countdown-time {
  display: flex;
  align-items: center;
}

.time-block {
  width: 40rpx;
  height: 40rpx;
  background-color: #1c1c1e;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
}

.time-separator {
  margin: 0 6rpx;
  color: #1c1c1e;
  font-weight: 600;
}

.stock-info {
  margin-top: 12rpx;
}

.stock-progress {
  height: 10rpx;
  background-color: #e5e5ea;
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.stock-text {
  font-size: 22rpx;
  color: #ff3b30;
}

/* 优惠券活动特定样式 */
.coupon-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx;
  background: linear-gradient(135deg, #ff9500, #ff3b30);
  border-radius: 16rpx;
  color: #ffffff;
}

.coupon-value {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}

.value-symbol {
  font-size: 24rpx;
  font-weight: 500;
}

.value-number {
  font-size: 48rpx;
  font-weight: 700;
}

.value-unit {
  font-size: 24rpx;
  font-weight: 500;
  margin-left: 4rpx;
}

.coupon-condition {
  margin-bottom: 8rpx;
}

.condition-text {
  font-size: 22rpx;
}

.coupon-validity {
  font-size: 20rpx;
  opacity: 0.8;
}

/* 满减活动特定样式 */
.discount-info {
  display: flex;
  flex-direction: column;
}

.discount-rules {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 12rpx;
}

.rule-item {
  padding: 4rpx 12rpx;
  background-color: rgba(90, 200, 250, 0.1);
  border-radius: 10rpx;
  border: 1rpx solid rgba(90, 200, 250, 0.3);
}

.rule-text {
  font-size: 22rpx;
  color: #5ac8fa;
}

.merchant-count {
  margin-top: 8rpx;
}

.merchant-text {
  font-size: 22rpx;
  color: #8e8e93;
}

/* 活动参与信息 */
.activity-participation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.participants {
  display: flex;
  align-items: center;
}

.avatar-group {
  display: flex;
  margin-right: 12rpx;
}

.participant-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  border: 2rpx solid #ffffff;
  margin-left: -15rpx;
}

.participant-avatar:first-child {
  margin-left: 0;
}

.more-participants {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #f2f2f7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -15rpx;
  border: 2rpx solid #ffffff;
}

.more-participants text {
  font-size: 18rpx;
  color: #8e8e93;
}

.participant-count {
  font-size: 24rpx;
  color: #8e8e93;
}

/* 分销收益标识 - 新设计 */
.distribution-profit-new {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.15);
  margin-right: 15rpx;
}

.profit-icon {
  color: #FFFFFF;
  margin-right: 6rpx;
}

.profit-text-new {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 600;
}

/* 活动操作按钮 */
.activity-action {
  display: flex;
}

.action-btn {
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.btn-groupBuy {
  background-color: #34c759;
  color: #ffffff;
}

.btn-flashSale {
  background-color: #ff3b30;
  color: #ffffff;
}

.btn-coupon {
  background-color: #ff9500;
  color: #ffffff;
}

.btn-discount {
  background-color: #5ac8fa;
  color: #ffffff;
}

.btn-disabled {
  background-color: #e5e5ea;
  color: #8e8e93;
}

.action-text {
  font-size: 26rpx;
}

/* 活动类型特定卡片样式 */
.activity-type-groupBuy {
  border-left: 6rpx solid #34c759;
}

.activity-type-flashSale {
  border-left: 6rpx solid #ff3b30;
}

.activity-type-coupon {
  border-left: 6rpx solid #ff9500;
}

.activity-type-discount {
  border-left: 6rpx solid #5ac8fa;
}
</style> 