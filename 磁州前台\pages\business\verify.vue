<template>
  <view class="verify-container">
    <!-- 顶部背景渐变 -->
    <view class="top-gradient"></view>
    
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">商家认证</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view scroll-y class="content">
      <!-- 认证说明卡片 -->
      <view class="info-card">
        <view class="info-header">
          <image src="/static/images/tabbar/认证.png" class="info-icon"></image>
          <text class="info-title">商家认证说明</text>
        </view>
        <view class="info-content">
          <text class="info-text">商家认证后，您的店铺将获得官方认证标识，提升用户信任度和转化率。认证商家在平台享有搜索排名优先、流量扶持等多重特权。</text>
        </view>
        <view class="info-tips">
          <text class="tip-title">认证须知：</text>
          <text class="tip-item">· 认证过程大约需要1-3个工作日</text>
          <text class="tip-item">· 请确保上传资料真实有效</text>
          <text class="tip-item">· 认证成功后12个月内有效</text>
        </view>
      </view>
      
      <!-- 认证表单 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="form-label">商家名称</text>
          <input class="form-input" v-model="formData.shopName" placeholder="与营业执照一致" />
        </view>
        
        <view class="form-item">
          <text class="form-label">法定代表人</text>
          <input class="form-input" v-model="formData.legalPerson" placeholder="营业执照上的法定代表人" />
        </view>
        
        <view class="form-item">
          <text class="form-label">经营地址</text>
          <input class="form-input" v-model="formData.address" placeholder="详细经营地址" />
        </view>
        
        <view class="form-item">
          <text class="form-label">联系电话</text>
          <input class="form-input" v-model="formData.contactPhone" placeholder="联系电话" type="number" />
        </view>
        
        <view class="form-item">
          <text class="form-label">经营范围</text>
          <textarea class="form-textarea" v-model="formData.businessScope" placeholder="与营业执照一致"></textarea>
        </view>
      </view>
      
      <!-- 证件上传 -->
      <view class="form-section">
        <view class="section-title">证件上传</view>
        
        <view class="watermark-notice">
          <text class="watermark-notice-text">上传的图片将自动添加"只用于磁州生活网认证"水印，请确保图片清晰可见</text>
        </view>
        
        <view class="upload-item">
          <text class="upload-label">营业执照</text>
          <view class="upload-desc">请上传营业执照正本清晰照片</view>
          <view class="upload-wrapper" @click="chooseImage('license')">
            <view class="upload-placeholder" v-if="!formData.licenseImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.licenseImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
        
        <view class="upload-item">
          <text class="upload-label">法人身份证正面</text>
          <view class="upload-desc">请上传法人身份证人像面</view>
          <view class="upload-wrapper" @click="chooseImage('idCardFront')">
            <view class="upload-placeholder" v-if="!formData.idCardFrontImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.idCardFrontImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
        
        <view class="upload-item">
          <text class="upload-label">法人身份证背面</text>
          <view class="upload-desc">请上传法人身份证国徽面</view>
          <view class="upload-wrapper" @click="chooseImage('idCardBack')">
            <view class="upload-placeholder" v-if="!formData.idCardBackImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.idCardBackImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
        
        <view class="upload-item">
          <text class="upload-label">店铺门头照</text>
          <view class="upload-desc">请上传清晰的店铺门头照片</view>
          <view class="upload-wrapper" @click="chooseImage('storefront')">
            <view class="upload-placeholder" v-if="!formData.storefrontImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.storefrontImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
      </view>
      
      <!-- 行业资质 -->
      <view class="form-section">
        <view class="section-title">行业资质（可选）</view>
        <view class="form-tip">根据您的经营类型，可上传相关行业资质证明，增加认证通过率</view>
        
        <view class="upload-item">
          <text class="upload-label">行业资质证明</text>
          <view class="upload-desc">如：食品经营许可证、卫生许可证等</view>
          <view class="upload-wrapper" @click="chooseImage('qualification')">
            <view class="upload-placeholder" v-if="!formData.qualificationImage">
              <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
              <text class="upload-text">点击上传</text>
            </view>
            <image v-else :src="formData.qualificationImage" mode="aspectFit" class="preview-image"></image>
          </view>
        </view>
      </view>
      
      <!-- 认证协议 -->
      <view class="agreement-section">
        <checkbox-group @change="checkboxChange">
          <label class="agreement-label">
            <checkbox value="agree" :checked="isAgreed" color="#1677FF" />
            <text class="agreement-text">我已阅读并同意</text>
            <text class="agreement-link" @click="showAgreement">《商家认证服务协议》</text>
          </label>
        </checkbox-group>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-section">
        <button class="submit-btn" :disabled="!isFormValid" :class="{'submit-btn-disabled': !isFormValid}" @click="submitVerify">提交认证申请</button>
        <view class="submit-tip">提交后，平台将在1-3个工作日内完成审核</view>
      </view>
    </scroll-view>
    
    <!-- 隐藏的画布，用于生成水印图片 -->
    <canvas canvas-id="watermarkCanvas" style="position: absolute; left: -9999px; width: 300px; height: 300px;"></canvas>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

const statusBarHeight = ref(20);
const isAgreed = ref(false);
const formData = reactive({
  shopName: '',
  legalPerson: '',
  address: '',
  contactPhone: '',
  businessScope: '',
  licenseImage: '',
  idCardFrontImage: '',
  idCardBackImage: '',
  storefrontImage: '',
  qualificationImage: ''
});

const isFormValid = computed(() => {
  return isAgreed.value &&
    formData.shopName &&
    formData.legalPerson &&
    formData.address &&
    formData.contactPhone &&
    formData.businessScope &&
    formData.licenseImage &&
    formData.idCardFrontImage &&
    formData.idCardBackImage &&
    formData.storefrontImage;
});

onLoad(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;

  // 如果有商家信息，自动填充部分表单
  loadMerchantInfo();
});

const goBack = () => {
  uni.navigateBack();
};

const loadMerchantInfo = () => {
  // 模拟从本地或API获取商家信息
  const merchantInfo = uni.getStorageSync('lastMerchantData');
  if (merchantInfo) {
    formData.shopName = merchantInfo.shopName || '';
    formData.address = merchantInfo.address || '';
    formData.contactPhone = merchantInfo.contactPhone || '';
  }
};

const chooseImage = (type) => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0];

      // 显示上传中提示
      uni.showLoading({
        title: '处理中...',
        mask: true
      });

      // 添加水印
      addWatermark(tempFilePath, type);
    }
  });
};

const addWatermark = (imagePath, type) => {
  const watermarkText = '只用于磁州生活网认证';

  uni.getImageInfo({
    src: imagePath,
    success: (imageInfo) => {
      const canvasWidth = imageInfo.width;
      const canvasHeight = imageInfo.height;
      const ctx = uni.createCanvasContext('watermarkCanvas');

      // 调整canvas尺寸
      const canvasElement = uni.createSelectorQuery().select('#watermarkCanvas');
      // this is a bit of a hack as there is no direct way to set canvas size for this API
      // so we rely on the style attribute, which we set before calling this function
      // but to be safe, let's just assume the style was set correctly
      
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);
      ctx.drawImage(imagePath, 0, 0, canvasWidth, canvasHeight);

      // 设置水印样式
      ctx.setFontSize(20);
      ctx.setFillStyle('rgba(0, 0, 0, 0.3)');
      ctx.setGlobalAlpha(0.5);
      ctx.rotate(-20 * Math.PI / 180);

      // 平铺水印
      const textWidth = ctx.measureText(watermarkText).width;
      for (let y = -canvasHeight; y < canvasHeight * 2; y += 100) {
        for (let x = -canvasWidth; x < canvasWidth * 2; x += textWidth + 80) {
            ctx.fillText(watermarkText, x, y);
        }
      }
      
      ctx.draw(false, () => {
        uni.canvasToTempFilePath({
          canvasId: 'watermarkCanvas',
          success: (res) => {
            formData[type + 'Image'] = res.tempFilePath;
            uni.hideLoading();
          },
          fail: (err) => {
            console.error('Canvas to temp file path failed:', err);
            uni.hideLoading();
            uni.showToast({ title: '图片处理失败', icon: 'none' });
          }
        });
      });
    },
    fail: (err) => {
        console.error('Get image info failed:', err);
        uni.hideLoading();
        uni.showToast({ title: '无法获取图片信息', icon: 'none' });
    }
  });
};

const checkboxChange = (e) => {
  isAgreed.value = e.detail.value.includes('agree');
};

const showAgreement = () => {
  uni.showModal({
    title: '商家认证服务协议',
    content: '这里是详细的商家认证服务协议内容...（内容待填充）',
    showCancel: false,
    confirmText: '我已阅读'
  });
};

const submitVerify = () => {
  if (!isFormValid.value) {
    uni.showToast({
      title: '请填写完整的必填信息并同意协议',
      icon: 'none'
    });
    return;
  }
  
  uni.showLoading({
    title: '提交中...',
    mask: true
  });
  
  // 模拟提交到后端
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '提交成功！待审核',
      icon: 'success'
    });

    // 可以在这里将formData保存到本地或上传
    console.log('提交的表单数据:', formData);

    // 跳转到成功页或状态页
    setTimeout(() => {
        uni.redirectTo({
            url: '/pages/business/success?from=verify'
        });
    }, 1500);
    
  }, 2000);
};

</script>

<style lang="scss">
.verify-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fc;
}

/* 顶部渐变背景 */
.top-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 25vh;
  background: linear-gradient(135deg, #0052cc, #1677ff);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  z-index: 0;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  position: relative;
  z-index: 1;
}

.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.navbar-right {
  width: 44px;
}

/* 内容区域 */
.content {
  flex: 1;
  position: relative;
  z-index: 1;
  padding: 0 20px 30px;
  box-sizing: border-box;
}

/* 认证说明卡片 */
.info-card {
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 8px 25px rgba(0, 82, 204, 0.1);
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.info-title {
  font-size: 18px;
  font-weight: bold;
  color: #0052CC;
}

.info-content {
  margin-bottom: 12px;
}

.info-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.info-tips {
  background-color: #f0f8ff;
  border-radius: 8px;
  padding: 12px;
}

.tip-title {
  font-size: 14px;
  color: #0052CC;
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
}

.tip-item {
  font-size: 12px;
  color: #333;
  line-height: 1.6;
  display: block;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 8px 25px rgba(0, 82, 204, 0.08);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  padding-bottom: 10px;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.form-input, .form-textarea {
  font-size: 14px;
  color: #333;
  background-color: #f7f7f7;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #eee;
  transition: border-color 0.3s;
}

.form-input:focus, .form-textarea:focus {
  border-color: #1677FF;
}

.form-textarea {
  height: 80px;
  width: auto;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
}

/* 证件上传 */
.watermark-notice {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #0052cc;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.upload-item {
  margin-bottom: 20px;
}

.upload-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.upload-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.upload-wrapper {
  width: 100%;
  height: 180px;
  background-color: #f7f7f7;
  border-radius: 8px;
  border: 1px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.upload-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
}

.preview-image {
  width: 100%;
  height: 100%;
}

/* 协议 */
.agreement-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.agreement-label {
  display: flex;
  align-items: center;
}

.agreement-text {
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}

.agreement-link {
  font-size: 14px;
  color: #1677FF;
  margin-left: 4px;
}

/* 提交按钮 */
.submit-section {
  margin: 30px 0;
  text-align: center;
}

.submit-btn {
  height: 50px;
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  font-size: 16px;
  border-radius: 25px;
  box-shadow: 0 6px 12px rgba(0, 82, 204, 0.2);
  line-height: 50px;
}

.submit-btn-disabled {
  background: #ccc;
  box-shadow: none;
}

.submit-tip {
  font-size: 12px;
  color: #999;
  margin-top: 12px;
}

</style>