作为代码小白，我理解您需要一个全面且清晰的模拟数据分离方案。我将为您制定一个完整的计划，从系统排查到实现全部分离。

## 模拟数据分离方案

### 第一步：系统排查需要分离的文件
```bash
dir /s /b *.vue | findstr -v "node_modules"
```
```bash
Get-ChildItem -Path . -Filter *.vue -Recurse -ErrorAction SilentlyContinue | Where-Object { $_.FullName -notlike "*node_modules*" } | Select-Object FullName
```
<no message>
```bash
Get-ChildItem -Path . -Filter *.js -Recurse -ErrorAction SilentlyContinue | Where-Object { $_.FullName -notlike "*node_modules*" -and $_.FullName -notlike "*mock*" } | Select-Object -First 20 FullName
```
<no message>
```bash
Get-ChildItem -Path . -Filter "pages\publish\*.vue" -Recurse -ErrorAction SilentlyContinue | Select-Object FullName
```
<no message>
```bash
Get-ChildItem -Path . -Filter "subPackages\news\*.vue" -Recurse -ErrorAction SilentlyContinue | Select-Object FullName
```
<no message>
```bash
Get-ChildItem -Path "subPackages\news\pages" -Filter "*.vue" -Recurse -ErrorAction SilentlyContinue | Select-Object FullName
```
<no message>
Read file: subPackages/news/pages/detail.vue
<no message>
Read file: subPackages/news/pages/detail.vue
<no message>
Read file: subPackages/news/pages/detail.vue
<no message>
Read file: subPackages/news/pages/detail.vue
<no message>
Read file: mock/news/newsDetail.js
<no message>
Read file: mock/info/categories.js
<no message>
Read file: mock/info/toppedInfo.js
<no message>
Read file: mock/info/normalInfo.js
<no message>
Read file: mock/common/adBanner.js
<no message>
Read file: mock/api.js
<no message>
```bash
Get-ChildItem -Path "pages\publish" -Filter "*.vue" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1 FullName
```
<no message>
Read file: pages\publish\business-transfer-detail.vue
<no message>
Read file: pages\publish\business-transfer-detail.vue
<no message>
Ran tool
基于对项目的系统排查和分析，我为您制定了一个全面的模拟数据分离方案。以下是详细计划：

## 一、模拟数据分离总体方案

### 1. 模拟数据目录结构

如上图所示，我们将按功能模块组织模拟数据，主要包括：

- **news**: 新闻相关数据
- **info**: 信息相关数据
- **business**: 商圈相关数据
- **publish**: 发布相关数据
- **user**: 用户相关数据
- **activity**: 活动相关数据
- **payment**: 支付相关数据
- **service**: 服务相关数据
- **common**: 通用数据

### 2. 每个模块的文件结构

每个模块包含以下文件：
- **index.js**: 模块入口，导出所有API
- **具体数据文件**: 如categories.js、detail.js等
- **API函数**: 每个数据文件中包含获取数据的API函数

### 3. 统一API入口

使用mock/api.js作为统一入口，便于组件调用

## 二、分离进度计划

### 第一阶段：基础模块分离（已完成）
- [x] 新闻模块 (news)
- [x] 信息模块 (info)
- [x] 商圈模块 (business)
- [x] 通用模块 (common)

### 第二阶段：核心功能模块分离
- [ ] 发布模块 (publish)
- [ ] 用户模块 (user)
- [ ] 活动模块 (activity)

### 第三阶段：辅助功能模块分离
- [ ] 支付模块 (payment)
- [ ] 服务模块 (service)
- [ ] 其他模块

## 三、实施计划

### 1. 发布模块分离

首先实现发布模块的数据分离，因为这是用户最常用的功能之一：
<no message>
<no message>
<no message>
<no message>
<no message>
Read file: pages/publish/business-transfer-detail.vue
<no message>
<no message>
<no message>
<no message>
<no message>
<no message>
<no message>
<no message>
Ran tool
我已经为您实施了一个全面的模拟数据分离方案，并完成了部分模块的实现。以下是当前进度和后续计划：

## 一、已完成的工作

1. **系统排查**：通过分析项目结构，确定了需要分离的模块和文件
2. **模块化架构设计**：设计了清晰的模块化目录结构
3. **API统一入口**：实现了统一的API入口，便于组件调用
4. **已完成模块**：
   - 新闻模块 (news)
   - 信息模块 (info)
   - 商圈模块 (business)
   - 通用模块 (common)
   - 发布模块部分功能 (publish)
     - 生意转让 (businessTransfer)
     - 房屋出租 (houseRent)
     - 招聘信息 (jobInfo)

## 二、后续实施计划

### 近期计划（发布模块完善）

1. **完成发布模块其他类型**：
   - 房屋出售 (houseSale)
   - 二手车辆 (carInfo)
   - 二手闲置 (secondHand)
   - 其他服务类型

### 中期计划（核心功能模块）

1. **用户模块 (user)**：
   - 用户资料 (profile)
   - 用户设置 (settings)
   - 登录/注册 (auth)

2. **活动模块 (activity)**：
   - 活动列表 (activityList)
   - 活动详情 (activityDetail)

### 远期计划（辅助功能模块）

1. **支付模块 (payment)**：
   - 钱包 (wallet)
   - 交易记录 (transaction)

2. **服务模块 (service)**：
   - 服务列表 (serviceList)
   - 服务详情 (serviceDetail)

## 三、实施方法与规范

1. **文件命名规范**：
   - 模块名使用小驼峰命名法
   - API函数使用fetch前缀

2. **数据结构规范**：
   - 详情数据使用对象格式
   - 列表数据使用数组格式
   - 所有API函数返回Promise

3. **API函数规范**：
   - 统一添加延迟模拟网络请求
   - 详情API接受ID参数
   - 列表API接受过滤参数

4. **代码组织规范**：
   - 每个模块有独立的index.js导出所有API
   - 所有模块通过api.js统一导出
   - 组件中通过mockApi调用

通过这种模块化的数据分离方案，我们可以使代码更加清晰、易于维护，同时减少组件中的硬编码数据，提高代码复用性和可维护性。