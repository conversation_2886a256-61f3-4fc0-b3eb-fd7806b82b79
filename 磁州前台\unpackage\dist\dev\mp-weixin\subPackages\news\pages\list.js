"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const mock_api = require("../../../mock/api.js");
if (!Math) {
  FabButtons();
}
const FabButtons = () => "../../../components/FabButtons.js";
const _sfc_main = {
  __name: "list",
  setup(__props) {
    const currentCategory = common_vendor.ref(0);
    const categories = common_vendor.ref([]);
    const newsList = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const page = common_vendor.ref(1);
    const hasMore = common_vendor.ref(true);
    const switchCategory = (categoryId) => {
      if (currentCategory.value === categoryId)
        return;
      currentCategory.value = categoryId;
      page.value = 1;
      newsList.value = [];
      hasMore.value = true;
      fetchNewsList();
    };
    const goToDetail = (id) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/news/pages/detail?id=${id}`
      });
    };
    const loadMore = () => {
      if (loading.value || !hasMore.value)
        return;
      page.value++;
      fetchNewsList();
    };
    const fetchNewsList = async () => {
      loading.value = true;
      try {
        const result = await mock_api.default.news.getList(currentCategory.value, page.value);
        if (page.value === 1) {
          newsList.value = result.list;
        } else {
          newsList.value = [...newsList.value, ...result.list];
        }
        hasMore.value = result.hasMore;
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/news/pages/list.vue:117", "获取新闻列表失败:", error);
        common_vendor.index.showToast({
          title: "获取资讯失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    };
    const fetchCategories = async () => {
      try {
        const result = await mock_api.default.news.getCategories();
        categories.value = result;
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/news/pages/list.vue:132", "获取新闻分类失败:", error);
      }
    };
    common_vendor.watch(currentCategory, () => {
      common_vendor.index.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    });
    common_vendor.onMounted(() => {
      fetchCategories();
      fetchNewsList();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(categories.value, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: currentCategory.value === item.id ? 1 : "",
            c: item.id,
            d: common_vendor.o(($event) => switchCategory(item.id), item.id)
          };
        }),
        b: common_vendor.f(newsList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.title),
            b: common_vendor.t(item.description),
            c: common_vendor.t(item.time),
            d: common_vendor.t(item.category),
            e: item.image
          }, item.image ? {
            f: item.image
          } : {}, {
            g: common_vendor.t(item.views),
            h: common_vendor.t(item.likes),
            i: common_vendor.t(item.comments),
            j: index,
            k: common_vendor.o(($event) => goToDetail(item.id), index)
          });
        }),
        c: common_assets._imports_2$38,
        d: common_assets._imports_1$43,
        e: common_assets._imports_3$34,
        f: hasMore.value && !loading.value
      }, hasMore.value && !loading.value ? {
        g: common_vendor.o(loadMore)
      } : {}, {
        h: loading.value
      }, loading.value ? {} : {}, {
        i: !hasMore.value && newsList.value.length > 0
      }, !hasMore.value && newsList.value.length > 0 ? {} : {}, {
        j: !loading.value && newsList.value.length === 0
      }, !loading.value && newsList.value.length === 0 ? {} : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/news/pages/list.js.map
