{"version": 3, "file": "index.js", "sources": ["subPackages/cashback/pages/search/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2FzaGJhY2tccGFnZXNcc2VhcmNoXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"search-container\">\n    <!-- 自定义导航栏 -->\n    <custom-navbar title=\"商品搜索\" :show-back=\"true\"></custom-navbar>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-container\">\n      <!-- 搜索框 -->\n      <view class=\"search-section\">\n        <view class=\"search-bar\">\n          <svg class=\"search-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n            <path fill=\"#999999\" d=\"M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z\" />\n          </svg>\n          <input class=\"search-input\" type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索商品名称\" confirm-type=\"search\" @confirm=\"searchProducts\" />\n          <view class=\"clear-button\" v-if=\"searchKeyword\" @tap=\"clearSearch\">\n            <svg class=\"clear-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path fill=\"#CCCCCC\" d=\"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\" />\n            </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 热门搜索 -->\n      <view class=\"hot-search-section\" v-if=\"!searchKeyword && !searchResults.length\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">热门搜索</text>\n      </view>\n        <view class=\"hot-search-tags\">\n          <view class=\"tag-item\" v-for=\"(tag, index) in hotSearchTags\" :key=\"index\" @tap=\"selectTag(tag)\">\n            <text>{{ tag }}</text>\n        </view>\n      </view>\n    </view>\n    \n      <!-- 搜索历史 -->\n      <view class=\"search-history-section\" v-if=\"!searchKeyword && !searchResults.length && searchHistory.length > 0\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">搜索历史</text>\n          <view class=\"clear-history\" @tap=\"clearHistory\">\n            <svg class=\"delete-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path fill=\"#999999\" d=\"M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19M8,9H16V19H8V9M15.5,4L14.5,3H9.5L8.5,4H5V6H19V4H15.5Z\" />\n            </svg>\n            <text>清空</text>\n        </view>\n        </view>\n        <view class=\"history-tags\">\n          <view class=\"tag-item\" v-for=\"(item, index) in searchHistory\" :key=\"index\" @tap=\"selectTag(item)\">\n            <text>{{ item }}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 搜索结果 -->\n      <view class=\"search-results-section\" v-if=\"searchResults.length > 0\">\n        <view class=\"filter-bar\">\n          <view class=\"filter-item\" :class=\"{'filter-item--active': sortBy === 'default'}\" @tap=\"sortResults('default')\">\n            <text>综合</text>\n          </view>\n          <view class=\"filter-item\" :class=\"{'filter-item--active': sortBy === 'price'}\" @tap=\"sortResults('price')\">\n            <text>价格</text>\n            <view class=\"sort-arrows\">\n              <svg class=\"arrow-up\" viewBox=\"0 0 24 24\" width=\"12\" height=\"12\" :class=\"{'active': sortBy === 'price' && sortOrder === 'asc'}\">\n                <path fill=\"currentColor\" d=\"M7,15L12,10L17,15H7Z\" />\n              </svg>\n              <svg class=\"arrow-down\" viewBox=\"0 0 24 24\" width=\"12\" height=\"12\" :class=\"{'active': sortBy === 'price' && sortOrder === 'desc'}\">\n                <path fill=\"currentColor\" d=\"M7,10L12,15L17,10H7Z\" />\n              </svg>\n            </view>\n          </view>\n          <view class=\"filter-item\" :class=\"{'filter-item--active': sortBy === 'cashback'}\" @tap=\"sortResults('cashback')\">\n            <text>返利</text>\n            <view class=\"sort-arrows\">\n              <svg class=\"arrow-up\" viewBox=\"0 0 24 24\" width=\"12\" height=\"12\" :class=\"{'active': sortBy === 'cashback' && sortOrder === 'asc'}\">\n                <path fill=\"currentColor\" d=\"M7,15L12,10L17,15H7Z\" />\n              </svg>\n              <svg class=\"arrow-down\" viewBox=\"0 0 24 24\" width=\"12\" height=\"12\" :class=\"{'active': sortBy === 'cashback' && sortOrder === 'desc'}\">\n                <path fill=\"currentColor\" d=\"M7,10L12,15L17,10H7Z\" />\n              </svg>\n            </view>\n          </view>\n          <view class=\"filter-item\" :class=\"{'filter-item--active': showPriceCompare}\" @tap=\"togglePriceCompare\">\n            <text>比价</text>\n          </view>\n        </view>\n        \n        <view class=\"products-grid\" v-if=\"!showPriceCompare\">\n          <product-card\n            v-for=\"(product, index) in sortedSearchResults\"\n            :key=\"index\"\n            :product=\"product\"\n            @tap=\"navigateToDetail(product)\"\n          ></product-card>\n        </view>\n        \n        <view class=\"price-compare-list\" v-else>\n          <view class=\"compare-item\" v-for=\"(product, index) in sortedSearchResults\" :key=\"index\">\n            <view class=\"compare-header\" @tap=\"toggleProductPlatforms(product.id)\">\n              <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\n            <view class=\"product-info\">\n                <text class=\"product-title\">{{ product.title }}</text>\n                <view class=\"price-range\">\n                  <text class=\"price-label\">价格区间：</text>\n                  <text class=\"price-value\">¥{{ product.minPrice }} - ¥{{ product.maxPrice }}</text>\n                </view>\n              </view>\n              <svg class=\"expand-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" :class=\"{'expanded': expandedProducts.includes(product.id)}\">\n                <path fill=\"#999999\" d=\"M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z\" />\n              </svg>\n            </view>\n            \n            <view class=\"platform-list\" v-if=\"expandedProducts.includes(product.id)\">\n              <view class=\"platform-item\" v-for=\"(platform, pIndex) in product.platforms\" :key=\"pIndex\" @tap=\"navigateToPlatformDetail(product, platform)\">\n                <view class=\"platform-info\">\n                  <image class=\"platform-icon\" :src=\"platform.icon\" mode=\"aspectFit\"></image>\n                  <text class=\"platform-name\">{{ platform.name }}</text>\n                </view>\n                <view class=\"platform-price\">\n                  <text class=\"price-value\">¥{{ platform.price }}</text>\n                  <text class=\"cashback-value\">返¥{{ platform.cashback }}</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"loading-more\" v-if=\"loading\">\n          <text>加载中...</text>\n        </view>\n        <view class=\"no-more\" v-if=\"!loading && !hasMore && searchResults.length > 0\">\n          <text>没有更多了</text>\n        </view>\n        </view>\n        \n      <!-- 无搜索结果 -->\n      <view class=\"empty-result\" v-if=\"searched && searchResults.length === 0\">\n        <image class=\"empty-image\" src=\"/static/images/cashback/empty-search.png\" mode=\"aspectFit\"></image>\n        <text>未找到相关商品</text>\n          <text class=\"empty-tips\">换个关键词试试吧</text>\n        </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport CustomNavbar from '../../components/CustomNavbar.vue';\nimport ProductCard from '../../components/ProductCard.vue';\n\nexport default {\n  components: {\n    CustomNavbar,\n    ProductCard\n  },\n  data() {\n    return {\n      searchKeyword: '',\n      searched: false,\n      loading: false,\n      hasMore: true,\n      page: 1,\n      sortBy: 'default',\n      sortOrder: 'asc',\n      showPriceCompare: false,\n      expandedProducts: [],\n      hotSearchTags: ['iPhone 15', '华为Mate60', '小米14', '笔记本电脑', '耳机', '电视'],\n      searchHistory: [],\n      searchResults: []\n    };\n  },\n  computed: {\n    sortedSearchResults() {\n      if (!this.searchResults.length) return [];\n      \n      let results = [...this.searchResults];\n      \n      if (this.sortBy === 'default') {\n        return results;\n      } else if (this.sortBy === 'price') {\n        return this.sortOrder === 'asc' \n          ? results.sort((a, b) => parseFloat(a.price) - parseFloat(b.price))\n          : results.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));\n      } else if (this.sortBy === 'cashback') {\n        return this.sortOrder === 'asc'\n          ? results.sort((a, b) => parseFloat(a.cashback) - parseFloat(b.cashback))\n          : results.sort((a, b) => parseFloat(b.cashback) - parseFloat(a.cashback));\n      }\n      \n      return results;\n    }\n  },\n  onLoad(options) {\n    // 设置页面不显示系统导航栏\n    uni.setNavigationBarColor({\n      frontColor: '#ffffff',\n      backgroundColor: '#9C27B0'\n    });\n    \n    // 获取搜索历史\n    this.getSearchHistory();\n    \n    // 如果有搜索关键词参数\n    if (options.keyword) {\n      this.searchKeyword = decodeURIComponent(options.keyword);\n      this.searchProducts();\n    }\n  },\n  methods: {\n    // 搜索商品\n    searchProducts() {\n      if (!this.searchKeyword.trim()) return;\n      \n      this.searched = true;\n      this.loading = true;\n      this.page = 1;\n      this.searchResults = [];\n      this.hasMore = true;\n      \n      // 保存到搜索历史\n      this.saveSearchHistory(this.searchKeyword);\n      \n      // 模拟搜索请求\n      setTimeout(() => {\n        // 这里应该是真实的API请求\n        this.searchResults = this.getMockSearchResults();\n        this.loading = false;\n        \n        // 计算每个商品的最高和最低价格\n        this.searchResults.forEach(product => {\n          const prices = product.platforms.map(p => parseFloat(p.price));\n          product.minPrice = Math.min(...prices).toFixed(2);\n          product.maxPrice = Math.max(...prices).toFixed(2);\n        });\n      }, 1000);\n    },\n    \n    // 加载更多搜索结果\n    loadMoreResults() {\n      if (!this.hasMore || this.loading) return;\n      \n      this.loading = true;\n      this.page++;\n      \n      // 模拟加载更多\n      setTimeout(() => {\n        // 这里应该是真实的API请求\n        if (this.page > 2) {\n          this.hasMore = false;\n        } else {\n          const moreResults = this.getMockSearchResults();\n          this.searchResults = [...this.searchResults, ...moreResults];\n        }\n        this.loading = false;\n      }, 1000);\n    },\n    \n    // 排序搜索结果\n    sortResults(type) {\n      if (this.sortBy === type) {\n        // 切换排序方向\n        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n      } else {\n        this.sortBy = type;\n        this.sortOrder = type === 'default' ? 'asc' : 'desc';\n      }\n    },\n    \n    // 切换比价显示\n    togglePriceCompare() {\n      this.showPriceCompare = !this.showPriceCompare;\n    },\n    \n    // 切换产品平台列表展开/收起\n    toggleProductPlatforms(productId) {\n      const index = this.expandedProducts.indexOf(productId);\n      if (index > -1) {\n        this.expandedProducts.splice(index, 1);\n      } else {\n        this.expandedProducts.push(productId);\n      }\n    },\n    \n    // 选择标签\n    selectTag(tag) {\n      this.searchKeyword = tag;\n      this.searchProducts();\n    },\n    \n    // 清空搜索\n    clearSearch() {\n      this.searchKeyword = '';\n      this.searched = false;\n      this.searchResults = [];\n    },\n    \n    // 获取搜索历史\n    getSearchHistory() {\n      const history = uni.getStorageSync('searchHistory');\n        if (history) {\n        this.searchHistory = JSON.parse(history);\n      }\n    },\n    \n    // 保存搜索历史\n    saveSearchHistory(keyword) {\n      if (!keyword.trim()) return;\n      \n      // 移除已存在的相同关键词\n      const index = this.searchHistory.indexOf(keyword);\n      if (index > -1) {\n        this.searchHistory.splice(index, 1);\n      }\n      \n      // 添加到历史记录开头\n      this.searchHistory.unshift(keyword);\n      \n      // 限制历史记录数量\n      if (this.searchHistory.length > 10) {\n        this.searchHistory = this.searchHistory.slice(0, 10);\n      }\n      \n      // 保存到本地存储\n      uni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));\n    },\n    \n    // 清空搜索历史\n    clearHistory() {\n      uni.showModal({\n        title: '提示',\n        content: '确定要清空搜索历史吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.searchHistory = [];\n            uni.removeStorageSync('searchHistory');\n          }\n        }\n      });\n    },\n    \n    // 导航到商品详情\n    navigateToDetail(product) {\n      uni.navigateTo({\n        url: `/subPackages/cashback/pages/product-detail/index?id=${product.id}`\n      });\n    },\n    \n    // 导航到平台商品详情\n    navigateToPlatformDetail(product, platform) {\n      uni.navigateTo({\n        url: `/subPackages/cashback/pages/product-detail/index?id=${product.id}&platform=${platform.name}`\n      });\n    },\n    \n    // 模拟搜索结果数据\n    getMockSearchResults() {\n      return [\n        {\n          id: 1,\n          title: 'Apple iPhone 15 Pro Max (A2850) 256GB 原色钛金属',\n          image: '/static/images/cashback/product-1.png',\n          price: '9999.00',\n          cashback: '300.00',\n          platform: '京东',\n          platforms: [\n            {\n              name: '京东',\n              icon: '/static/images/cashback/platform-jd.png',\n              price: '9999.00',\n              cashback: '300.00'\n            },\n            {\n              name: '天猫',\n              icon: '/static/images/cashback/platform-tmall.png',\n              price: '9989.00',\n              cashback: '280.00'\n            },\n            {\n              name: '苏宁',\n              icon: '/static/images/cashback/platform-suning.png',\n              price: '10099.00',\n              cashback: '290.00'\n            }\n          ]\n        },\n        {\n          id: 2,\n          title: 'Apple iPhone 15 Pro 256GB 黑色钛金属',\n          image: '/static/images/cashback/product-2.png',\n          price: '8999.00',\n          cashback: '270.00',\n          platform: '天猫',\n          platforms: [\n            {\n              name: '京东',\n              icon: '/static/images/cashback/platform-jd.png',\n              price: '8999.00',\n              cashback: '260.00'\n            },\n            {\n              name: '天猫',\n              icon: '/static/images/cashback/platform-tmall.png',\n              price: '8989.00',\n              cashback: '270.00'\n            },\n            {\n              name: '拼多多',\n              icon: '/static/images/cashback/platform-pdd.png',\n              price: '8899.00',\n              cashback: '220.00'\n            }\n          ]\n        },\n        {\n          id: 3,\n          title: 'Apple iPhone 15 128GB 蓝色',\n          image: '/static/images/cashback/product-3.png',\n          price: '5999.00',\n          cashback: '180.00',\n          platform: '拼多多',\n          platforms: [\n            {\n              name: '京东',\n              icon: '/static/images/cashback/platform-jd.png',\n              price: '5999.00',\n              cashback: '150.00'\n            },\n            {\n              name: '天猫',\n              icon: '/static/images/cashback/platform-tmall.png',\n              price: '6099.00',\n              cashback: '160.00'\n            },\n            {\n              name: '拼多多',\n              icon: '/static/images/cashback/platform-pdd.png',\n              price: '5899.00',\n              cashback: '180.00'\n            }\n          ]\n        },\n        {\n          id: 4,\n          title: 'Apple iPhone 14 Pro Max 256GB 深空黑色',\n          image: '/static/images/cashback/product-4.png',\n          price: '8499.00',\n          cashback: '250.00',\n          platform: '京东',\n          platforms: [\n            {\n              name: '京东',\n              icon: '/static/images/cashback/platform-jd.png',\n              price: '8499.00',\n              cashback: '250.00'\n            },\n            {\n              name: '天猫',\n              icon: '/static/images/cashback/platform-tmall.png',\n              price: '8599.00',\n              cashback: '240.00'\n            },\n            {\n              name: '苏宁',\n              icon: '/static/images/cashback/platform-suning.png',\n              price: '8549.00',\n              cashback: '230.00'\n            }\n          ]\n        }\n      ];\n    }\n  },\n  onReachBottom() {\n    // 滚动到底部加载更多\n    this.loadMoreResults();\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.search-container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.content-container {\n  padding-top: calc(var(--status-bar-height) + 44px);\n  padding-bottom: 20px;\n}\n\n.search-section {\n  padding: 16px;\n  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);\n  border-bottom-left-radius: 20px;\n  border-bottom-right-radius: 20px;\n  padding-top: 24px;\n}\n\n.search-bar {\n  display: flex;\n  align-items: center;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 10px 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  \n  .search-icon {\n    margin-right: 8px;\n}\n\n.search-input {\n  flex: 1;\n    height: 20px;\n    font-size: 14px;\n  color: #333333;\n}\n\n  .clear-button {\n    width: 16px;\n    height: 16px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n}\n\n.hot-search-section, .search-history-section {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 16px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n\n  .section-title {\n    font-size: 16px;\n  font-weight: 600;\n  color: #333333;\n}\n\n.clear-history {\n  display: flex;\n  align-items: center;\n\n    .delete-icon {\n      margin-right: 4px;\n}\n\n    text {\n      font-size: 14px;\n  color: #999999;\n    }\n  }\n}\n\n.hot-search-tags, .history-tags {\n  display: flex;\n  flex-wrap: wrap;\n  \n  .tag-item {\n    background-color: #F5F5F5;\n    border-radius: 16px;\n    padding: 6px 12px;\n    margin-right: 8px;\n    margin-bottom: 8px;\n    \n    text {\n      font-size: 14px;\n      color: #666666;\n    }\n  }\n}\n\n.search-results-section {\n  margin-top: 16px;\n}\n\n.filter-bar {\n  display: flex;\n  background-color: #FFFFFF;\n  padding: 12px 16px;\n  border-bottom: 1px solid #EEEEEE;\n\n.filter-item {\n  display: flex;\n  align-items: center;\n    margin-right: 24px;\n  position: relative;\n    \n    text {\n      font-size: 14px;\n      color: #666666;\n}\n\n.sort-arrows {\n  display: flex;\n  flex-direction: column;\n      margin-left: 4px;\n      \n      .arrow-up, .arrow-down {\n        color: #CCCCCC;\n        \n        &.active {\n          color: #9C27B0;\n        }\n      }\n    }\n    \n    &--active {\n      text {\n        color: #9C27B0;\n        font-weight: 500;\n      }\n      \n      &::after {\n        content: '';\n        position: absolute;\n        bottom: -12px;\n        left: 0;\n        right: 0;\n        height: 2px;\n        background-color: #9C27B0;\n      }\n    }\n  }\n}\n\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 12px;\n  padding: 16px;\n}\n\n.price-compare-list {\n  background-color: #FFFFFF;\n  \n  .compare-item {\n    border-bottom: 8px solid #F5F5F5;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n  }\n  \n  .compare-header {\n    display: flex;\n    padding: 16px;\n    position: relative;\n\n.product-image {\n      width: 80px;\n      height: 80px;\n      border-radius: 8px;\n      margin-right: 12px;\n}\n\n.product-info {\n      flex: 1;\n\n.product-title {\n        font-size: 14px;\n  color: #333333;\n  line-height: 1.4;\n        margin-bottom: 8px;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n      \n      .price-range {\n        .price-label {\n          font-size: 12px;\n          color: #999999;\n        }\n        \n        .price-value {\n          font-size: 14px;\n          color: #FF6B6B;\n          font-weight: 500;\n        }\n      }\n    }\n    \n    .expand-icon {\n      position: absolute;\n      right: 16px;\n      top: 50%;\n      transform: translateY(-50%);\n      transition: transform 0.3s;\n      \n      &.expanded {\n        transform: translateY(-50%) rotate(180deg);\n      }\n    }\n  }\n  \n  .platform-list {\n    background-color: #F9F9F9;\n    padding: 0 16px;\n    \n    .platform-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #EEEEEE;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .platform-info {\n  display: flex;\n        align-items: center;\n        \n        .platform-icon {\n          width: 20px;\n          height: 20px;\n          margin-right: 8px;\n        }\n        \n        .platform-name {\n          font-size: 14px;\n          color: #666666;\n        }\n      }\n      \n      .platform-price {\n  display: flex;\n        flex-direction: column;\n        align-items: flex-end;\n        \n        .price-value {\n          font-size: 14px;\n          color: #FF6B6B;\n          font-weight: 500;\n          margin-bottom: 2px;\n}\n\n.cashback-value {\n          font-size: 12px;\n          color: #9C27B0;\n        }\n      }\n    }\n  }\n}\n\n.loading-more, .no-more {\n  text-align: center;\n  padding: 16px 0;\n\n  text {\n    font-size: 14px;\n  color: #999999;\n  }\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 60px 0;\n  \n  .empty-image {\n    width: 120px;\n    height: 120px;\n    margin-bottom: 16px;\n  }\n  \n  text {\n    font-size: 16px;\n    color: #666666;\n    margin-bottom: 8px;\n}\n\n.empty-tips {\n    font-size: 14px;\n  color: #999999;\n  }\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/pages/search/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAgJA,MAAK,eAAgB,MAAW;AAChC,MAAK,cAAe,MAAW;AAE/B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,eAAe;AAAA,MACf,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,kBAAkB,CAAE;AAAA,MACpB,eAAe,CAAC,aAAa,YAAY,QAAQ,SAAS,MAAM,IAAI;AAAA,MACpE,eAAe,CAAE;AAAA,MACjB,eAAe,CAAC;AAAA;EAEnB;AAAA,EACD,UAAU;AAAA,IACR,sBAAsB;AACpB,UAAI,CAAC,KAAK,cAAc;AAAQ,eAAO,CAAA;AAEvC,UAAI,UAAU,CAAC,GAAG,KAAK,aAAa;AAEpC,UAAI,KAAK,WAAW,WAAW;AAC7B,eAAO;AAAA,MACT,WAAW,KAAK,WAAW,SAAS;AAClC,eAAO,KAAK,cAAc,QACtB,QAAQ,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,KAAK,IAAI,WAAW,EAAE,KAAK,CAAC,IAChE,QAAQ,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,KAAK,IAAI,WAAW,EAAE,KAAK,CAAC;AAAA,MACtE,WAAW,KAAK,WAAW,YAAY;AACrC,eAAO,KAAK,cAAc,QACtB,QAAQ,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,QAAQ,IAAI,WAAW,EAAE,QAAQ,CAAC,IACtE,QAAQ,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,QAAQ,IAAI,WAAW,EAAE,QAAQ,CAAC;AAAA,MAC5E;AAEA,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AAEdA,kBAAAA,MAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;AAGD,SAAK,iBAAgB;AAGrB,QAAI,QAAQ,SAAS;AACnB,WAAK,gBAAgB,mBAAmB,QAAQ,OAAO;AACvD,WAAK,eAAc;AAAA,IACrB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,iBAAiB;AACf,UAAI,CAAC,KAAK,cAAc,KAAM;AAAE;AAEhC,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,OAAO;AACZ,WAAK,gBAAgB;AACrB,WAAK,UAAU;AAGf,WAAK,kBAAkB,KAAK,aAAa;AAGzC,iBAAW,MAAM;AAEf,aAAK,gBAAgB,KAAK;AAC1B,aAAK,UAAU;AAGf,aAAK,cAAc,QAAQ,aAAW;AACpC,gBAAM,SAAS,QAAQ,UAAU,IAAI,OAAK,WAAW,EAAE,KAAK,CAAC;AAC7D,kBAAQ,WAAW,KAAK,IAAI,GAAG,MAAM,EAAE,QAAQ,CAAC;AAChD,kBAAQ,WAAW,KAAK,IAAI,GAAG,MAAM,EAAE,QAAQ,CAAC;AAAA,QAClD,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI,CAAC,KAAK,WAAW,KAAK;AAAS;AAEnC,WAAK,UAAU;AACf,WAAK;AAGL,iBAAW,MAAM;AAEf,YAAI,KAAK,OAAO,GAAG;AACjB,eAAK,UAAU;AAAA,eACV;AACL,gBAAM,cAAc,KAAK;AACzB,eAAK,gBAAgB,CAAC,GAAG,KAAK,eAAe,GAAG,WAAW;AAAA,QAC7D;AACA,aAAK,UAAU;AAAA,MAChB,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,YAAY,MAAM;AAChB,UAAI,KAAK,WAAW,MAAM;AAExB,aAAK,YAAY,KAAK,cAAc,QAAQ,SAAS;AAAA,aAChD;AACL,aAAK,SAAS;AACd,aAAK,YAAY,SAAS,YAAY,QAAQ;AAAA,MAChD;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,mBAAmB,CAAC,KAAK;AAAA,IAC/B;AAAA;AAAA,IAGD,uBAAuB,WAAW;AAChC,YAAM,QAAQ,KAAK,iBAAiB,QAAQ,SAAS;AACrD,UAAI,QAAQ,IAAI;AACd,aAAK,iBAAiB,OAAO,OAAO,CAAC;AAAA,aAChC;AACL,aAAK,iBAAiB,KAAK,SAAS;AAAA,MACtC;AAAA,IACD;AAAA;AAAA,IAGD,UAAU,KAAK;AACb,WAAK,gBAAgB;AACrB,WAAK,eAAc;AAAA,IACpB;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,WAAW;AAChB,WAAK,gBAAgB;IACtB;AAAA;AAAA,IAGD,mBAAmB;AACjB,YAAM,UAAUA,cAAAA,MAAI,eAAe,eAAe;AAChD,UAAI,SAAS;AACb,aAAK,gBAAgB,KAAK,MAAM,OAAO;AAAA,MACzC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,SAAS;AACzB,UAAI,CAAC,QAAQ,KAAI;AAAI;AAGrB,YAAM,QAAQ,KAAK,cAAc,QAAQ,OAAO;AAChD,UAAI,QAAQ,IAAI;AACd,aAAK,cAAc,OAAO,OAAO,CAAC;AAAA,MACpC;AAGA,WAAK,cAAc,QAAQ,OAAO;AAGlC,UAAI,KAAK,cAAc,SAAS,IAAI;AAClC,aAAK,gBAAgB,KAAK,cAAc,MAAM,GAAG,EAAE;AAAA,MACrD;AAGAA,oBAAG,MAAC,eAAe,iBAAiB,KAAK,UAAU,KAAK,aAAa,CAAC;AAAA,IACvE;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,gBAAgB;AACrBA,gCAAI,kBAAkB,eAAe;AAAA,UACvC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,SAAS;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,uDAAuD,QAAQ,EAAE;AAAA,MACxE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,yBAAyB,SAAS,UAAU;AAC1CA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,uDAAuD,QAAQ,EAAE,aAAa,SAAS,IAAI;AAAA,MAClG,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,uBAAuB;AACrB,aAAO;AAAA,QACL;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,UACV,WAAW;AAAA,YACT;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,UACV,WAAW;AAAA,YACT;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,UACV,WAAW;AAAA,YACT;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,UACV,WAAW;AAAA,YACT;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,cACP,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA;IAEJ;AAAA,EACD;AAAA,EACD,gBAAgB;AAEd,SAAK,gBAAe;AAAA,EACtB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxdA,GAAG,WAAW,eAAe;"}