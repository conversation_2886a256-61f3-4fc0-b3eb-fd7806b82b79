<template>
  <view class="orders-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" mode="aspectFit" :style="{
            width: '40rpx',
            height: '40rpx'
          }"></image>
        </view>
        <view class="navbar-title">订单管理</view>
        <view class="navbar-right">
          <view class="search-btn" @click="navigateTo('/subPackages/activity-showcase/pages/search/index?type=orders')">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <circle cx="11" cy="11" r="8" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              <path d="M21 21l-4.35-4.35" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单类型标签栏 -->
    <view class="order-tabs">
      <view 
        v-for="(tab, index) in orderTabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <view class="tab-indicator" v-if="currentTab === index" :style="{
          background: 'linear-gradient(90deg, #5AC8FA 0%, #90E0FF 100%)'
        }"></view>
      </view>
    </view>

    <!-- 订单列表区域 -->
    <swiper class="orders-swiper" :current="currentTab" @change="onSwiperChange">
      <swiper-item v-for="(tab, tabIndex) in orderTabs" :key="tabIndex">
        <scroll-view 
          class="tab-content" 
          scroll-y 
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          @scrolltolower="loadMore"
        >
          <view class="order-list">
            <view 
              v-for="order in getOrdersByStatus(tab.status)" 
              :key="order.id"
              class="order-card"
              @click="viewOrderDetail(order)"
            >
              <!-- 订单头部 -->
              <view class="order-header">
                <view class="shop-info">
                  <svg class="shop-icon" viewBox="0 0 24 24" width="16" height="16">
                    <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M9 22V12h6v10" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                  <text class="shop-name">{{ order.shopName }}</text>
                  <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
                    <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                </view>
                <view class="order-status" :style="{ color: getStatusColor(order.status) }">
                  {{ getStatusText(order.status) }}
                </view>
              </view>

              <!-- 订单内容 -->
              <view class="order-content">
                <view 
                  v-for="(item, itemIndex) in order.items" 
                  :key="itemIndex"
                  class="order-item"
                >
                  <image :src="item.image" class="item-image" mode="aspectFill"></image>
                  <view class="item-info">
                    <text class="item-name">{{ item.name }}</text>
                    <text class="item-spec">{{ item.specification }}</text>
                    <view class="item-price-qty">
                      <text class="item-price">¥{{ item.price.toFixed(2) }}</text>
                      <text class="item-qty">x{{ item.quantity }}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 订单底部 -->
              <view class="order-footer">
                <view class="order-total">
                  <text class="total-text">共{{ getTotalQuantity(order) }}件商品</text>
                  <text class="total-price">合计: ¥{{ order.totalAmount.toFixed(2) }}</text>
                </view>
                <view class="order-actions">
                  <view 
                    v-if="order.status === 'pending_payment'"
                    class="action-btn cancel"
                    @click.stop="cancelOrder(order)"
                  >
                    取消订单
                  </view>
                  <view 
                    class="action-btn primary"
                    :style="{
                      background: getPrimaryActionBgColor(order.status),
                      color: '#FFFFFF'
                    }"
                    @click.stop="handlePrimaryAction(order)"
                  >
                    {{ getPrimaryActionText(order.status) }}
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-if="getOrdersByStatus(tab.status).length === 0">
            <image class="empty-image" :src="tab.emptyImage || '/static/images/empty-orders.png'"></image>
            <text class="empty-text">{{ tab.emptyText || '暂无相关订单' }}</text>
            <view class="action-btn" @click="navigateTo('/subPackages/activity-showcase/pages/index/index')" :style="{
              background: 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',
              borderRadius: '35px',
              boxShadow: '0 5px 15px rgba(90,200,250,0.3)'
            }">
              <text>去逛逛</text>
            </view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 页面状态
const currentTab = ref(0);
const isRefreshing = ref(false);
const orderList = ref([]);

// 模拟数据
const mockOrders = [
  {
    id: '202405120001',
    shopName: '磁州活动中心',
    status: 'pending_payment',
    createTime: '2024-05-12 14:30',
    totalAmount: 199.00,
    items: [
      {
        id: 1,
        name: '夏季亲子户外拓展活动',
        specification: '2大1小家庭套餐',
        price: 199.00,
        quantity: 1,
        image: '/static/demo/activity1.jpg'
      }
    ]
  },
  {
    id: '202405110002',
    shopName: '磁州文化馆',
    status: 'pending_delivery',
    createTime: '2024-05-11 10:15',
    totalAmount: 98.00,
    items: [
      {
        id: 2,
        name: '传统文化体验课',
        specification: '单人票',
        price: 49.00,
        quantity: 2,
        image: '/static/demo/activity2.jpg'
      }
    ]
  },
  {
    id: '202405100003',
    shopName: '磁州体育中心',
    status: 'pending_receipt',
    createTime: '2024-05-10 16:45',
    totalAmount: 150.00,
    items: [
      {
        id: 3,
        name: '篮球训练营',
        specification: '月卡',
        price: 150.00,
        quantity: 1,
        image: '/static/demo/activity3.jpg'
      }
    ]
  },
  {
    id: '202405090004',
    shopName: '磁州美食街',
    status: 'pending_review',
    createTime: '2024-05-09 19:20',
    totalAmount: 88.00,
    items: [
      {
        id: 4,
        name: '美食节门票',
        specification: '双人票',
        price: 88.00,
        quantity: 1,
        image: '/static/demo/activity4.jpg'
      }
    ]
  },
  {
    id: '202405080005',
    shopName: '磁州艺术中心',
    status: 'completed',
    createTime: '2024-05-08 13:10',
    totalAmount: 120.00,
    items: [
      {
        id: 5,
        name: '油画体验课',
        specification: '单人票',
        price: 120.00,
        quantity: 1,
        image: '/static/demo/activity5.jpg'
      }
    ]
  }
];

// 订单标签页
const orderTabs = [
  { name: '全部', status: 'all', emptyText: '暂无订单', emptyImage: '/static/images/empty-orders.png' },
  { name: '待付款', status: 'pending_payment', emptyText: '暂无待付款订单', emptyImage: '/static/images/empty-payment.png' },
  { name: '待发货', status: 'pending_delivery', emptyText: '暂无待发货订单', emptyImage: '/static/images/empty-delivery.png' },
  { name: '待收货', status: 'pending_receipt', emptyText: '暂无待收货订单', emptyImage: '/static/images/empty-receipt.png' },
  { name: '待评价', status: 'pending_review', emptyText: '暂无待评价订单', emptyImage: '/static/images/empty-review.png' }
];

// 生命周期
onMounted(() => {
  loadOrders();
});

// 方法
const loadOrders = () => {
  // 模拟加载数据
  orderList.value = mockOrders;
};

const getOrdersByStatus = (status) => {
  if (status === 'all') {
    return orderList.value;
  }
  return orderList.value.filter(order => order.status === status);
};

const switchTab = (index) => {
  currentTab.value = index;
};

const onSwiperChange = (e) => {
  currentTab.value = e.detail.current;
};

const onRefresh = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    loadOrders();
    isRefreshing.value = false;
  }, 1000);
};

const loadMore = () => {
  // 模拟加载更多
  console.log('加载更多订单');
};

const getTotalQuantity = (order) => {
  return order.items.reduce((total, item) => total + item.quantity, 0);
};

const getStatusText = (status) => {
  const statusMap = {
    'pending_payment': '待付款',
    'pending_delivery': '待发货',
    'pending_receipt': '待收货',
    'pending_review': '待评价',
    'completed': '已完成',
    'cancelled': '已取消',
    'after_sale': '售后中'
  };
  return statusMap[status] || '未知状态';
};

const getStatusColor = (status) => {
  const colorMap = {
    'pending_payment': '#FF9500',
    'pending_delivery': '#5AC8FA',
    'pending_receipt': '#5AC8FA',
    'pending_review': '#34C759',
    'completed': '#8E8E93',
    'cancelled': '#8E8E93',
    'after_sale': '#FF3B30'
  };
  return colorMap[status] || '#8E8E93';
};

const getPrimaryActionText = (status) => {
  const actionMap = {
    'pending_payment': '立即付款',
    'pending_delivery': '提醒发货',
    'pending_receipt': '确认收货',
    'pending_review': '去评价',
    'completed': '再次购买',
    'cancelled': '删除订单',
    'after_sale': '查看进度'
  };
  return actionMap[status] || '查看详情';
};

const getPrimaryActionBgColor = (status) => {
  const bgColorMap = {
    'pending_payment': 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',
    'pending_delivery': 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',
    'pending_receipt': 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',
    'pending_review': 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',
    'completed': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)',
    'cancelled': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)',
    'after_sale': 'linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)'
  };
  return bgColorMap[status] || 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)';
};

const handlePrimaryAction = (order) => {
  switch (order.status) {
    case 'pending_payment':
      navigateTo(`/subPackages/activity-showcase/pages/payment/index?orderId=${order.id}`);
      break;
    case 'pending_delivery':
      remindDelivery(order);
      break;
    case 'pending_receipt':
      confirmReceipt(order);
      break;
    case 'pending_review':
      navigateTo(`/subPackages/activity-showcase/pages/review/index?orderId=${order.id}`);
      break;
    case 'completed':
      buyAgain(order);
      break;
    case 'cancelled':
      deleteOrder(order);
      break;
    default:
      viewOrderDetail(order);
  }
};

const viewOrderDetail = (order) => {
  navigateTo(`/subPackages/activity-showcase/pages/orders/detail?id=${order.id}`);
};

const cancelOrder = (order) => {
  uni.showModal({
    title: '提示',
    content: '确认取消该订单吗？',
    success: function(res) {
      if (res.confirm) {
        // 模拟取消订单
        uni.showToast({
          title: '订单已取消',
          icon: 'success'
        });
        // 更新订单状态
        const index = orderList.value.findIndex(item => item.id === order.id);
        if (index !== -1) {
          orderList.value[index].status = 'cancelled';
        }
      }
    }
  });
};

const remindDelivery = (order) => {
  uni.showToast({
    title: '已提醒商家发货',
    icon: 'success'
  });
};

const confirmReceipt = (order) => {
  uni.showModal({
    title: '提示',
    content: '确认已收到商品吗？',
    success: function(res) {
      if (res.confirm) {
        // 模拟确认收货
        uni.showToast({
          title: '确认收货成功',
          icon: 'success'
        });
        // 更新订单状态
        const index = orderList.value.findIndex(item => item.id === order.id);
        if (index !== -1) {
          orderList.value[index].status = 'pending_review';
        }
      }
    }
  });
};

const buyAgain = (order) => {
  // 模拟再次购买
  uni.showToast({
    title: '已添加到购物车',
    icon: 'success'
  });
};

const deleteOrder = (order) => {
  uni.showModal({
    title: '提示',
    content: '确认删除该订单吗？',
    success: function(res) {
      if (res.confirm) {
        // 模拟删除订单
        uni.showToast({
          title: '订单已删除',
          icon: 'success'
        });
        // 从列表中移除
        const index = orderList.value.findIndex(item => item.id === order.id);
        if (index !== -1) {
          orderList.value.splice(index, 1);
        }
      }
    }
  });
};

const goBack = () => {
  uni.navigateBack();
};

const navigateTo = (url) => {
  uni.navigateTo({ url });
};
</script>

<style scoped>
.orders-container {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.navbar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%);
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 103rpx; /* 原来是98rpx，增加5rpx */
  padding: calc(var(--status-bar-height) + 8rpx) 30rpx 0; /* 向下移动8rpx */
}

.back-btn, .search-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* 标签栏样式 */
.order-tabs {
  display: flex;
  background: #FFFFFF;
  padding: 0 20rpx;
  margin-top: calc(var(--status-bar-height) + 111rpx); /* 原来是106rpx，增加5rpx */
  border-bottom: 1rpx solid #EEEEEE;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}

.tab-text {
  font-size: 28rpx;
  color: #333333;
  padding: 0 10rpx;
}

.tab-item.active .tab-text {
  color: #5AC8FA;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 6rpx;
  border-radius: 3rpx;
}

/* 订单列表样式 */
.orders-swiper {
  height: calc(100vh - var(--status-bar-height) - 111rpx - 70rpx); /* 原来是106rpx，增加5rpx */
}

.tab-content {
  height: 100%;
  padding: 20rpx;
}

.order-list {
  padding-bottom: 30rpx;
}

.order-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #F5F5F5;
}

.shop-info {
  display: flex;
  align-items: center;
}

.shop-icon {
  margin-right: 10rpx;
}

.shop-name {
  font-size: 28rpx;
  color: #333333;
  margin-right: 10rpx;
}

.arrow-icon {
  margin-left: 10rpx;
}

.order-status {
  font-size: 26rpx;
  font-weight: 500;
}

.order-content {
  padding: 20rpx 30rpx;
}

.order-item {
  display: flex;
  margin-bottom: 20rpx;
}

.order-item:last-child {
  margin-bottom: 0;
}

.item-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-spec {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.item-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.item-qty {
  font-size: 24rpx;
  color: #999999;
}

.order-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #F5F5F5;
}

.order-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20rpx;
}

.total-text {
  font-size: 24rpx;
  color: #999999;
  margin-right: 10rpx;
}

.total-price {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.action-btn {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
}

.action-btn.cancel {
  border: 1rpx solid #DDDDDD;
  color: #666666;
}

.action-btn.primary {
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.empty-state .action-btn {
  padding: 15rpx 60rpx;
  font-size: 28rpx;
  color: #FFFFFF;
}
</style> 