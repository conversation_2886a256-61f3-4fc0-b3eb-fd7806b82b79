version: 2
jobs:
  build:
    docker:
      # https://circleci.com/docs/2.0/circleci-images/#nodejs
      - image: circleci/node:6

    working_directory: ~/vue-style-loader

    steps:
      - checkout

      # Download and cache dependencies
      - restore_cache:
          keys:
          - v1-dependencies-{{ checksum "package.json" }}
          # fallback to using the latest cache if no exact match is found
          - v1-dependencies-

      - run: yarn install

      - save_cache:
          paths:
            - node_modules
          key: v1-dependencies-{{ checksum "package.json" }}

      # run tests!
      - run: yarn test
