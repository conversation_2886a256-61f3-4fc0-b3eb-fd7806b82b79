"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  setup() {
    const couponId = common_vendor.ref("");
    const activeTab = common_vendor.ref("all");
    const searchKeyword = common_vendor.ref("");
    const startDate = common_vendor.ref("");
    const endDate = common_vendor.ref("");
    const showDateFilter = common_vendor.ref(false);
    const showFilterPopup = common_vendor.ref(false);
    const showDatePicker = common_vendor.ref(false);
    const currentDatePicker = common_vendor.ref("");
    const timeRange = common_vendor.ref("thisMonth");
    const selectedChannels = common_vendor.ref(["all"]);
    const sortBy = common_vendor.ref("timeDesc");
    const currentPage = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMoreData = common_vendor.ref(true);
    const records = common_vendor.ref([
      {
        id: 1,
        userName: "张三",
        userAvatar: "/static/images/avatar-1.jpg",
        userPhone: "13812345678",
        couponTitle: "新客专享优惠",
        couponValue: 10,
        minSpend: 100,
        claimTime: "2023-04-20 14:30:25",
        useTime: "2023-04-22 18:45:12",
        expireTime: "2023-05-20 23:59:59",
        status: "used",
        source: "扫码领取"
      },
      {
        id: 2,
        userName: "李四",
        userAvatar: "/static/images/avatar-2.jpg",
        userPhone: "13987654321",
        couponTitle: "新客专享优惠",
        couponValue: 10,
        minSpend: 100,
        claimTime: "2023-04-20 15:45:36",
        useTime: "",
        expireTime: "2023-05-20 23:59:59",
        status: "unused",
        source: "分享领取"
      },
      {
        id: 3,
        userName: "王五",
        userAvatar: "/static/images/avatar-3.jpg",
        userPhone: "13765432198",
        couponTitle: "新客专享优惠",
        couponValue: 10,
        minSpend: 100,
        claimTime: "2023-04-21 09:20:48",
        useTime: "",
        expireTime: "2023-05-20 23:59:59",
        status: "unused",
        source: "活动领取"
      },
      {
        id: 4,
        userName: "赵六",
        userAvatar: "/static/images/avatar-4.jpg",
        userPhone: "13698765432",
        couponTitle: "新客专享优惠",
        couponValue: 10,
        minSpend: 100,
        claimTime: "2023-03-15 11:30:22",
        useTime: "",
        expireTime: "2023-04-15 23:59:59",
        status: "expired",
        source: "手动发放"
      },
      {
        id: 5,
        userName: "钱七",
        userAvatar: "/static/images/avatar-5.jpg",
        userPhone: "13567891234",
        couponTitle: "新客专享优惠",
        couponValue: 10,
        minSpend: 100,
        claimTime: "2023-04-22 16:40:15",
        useTime: "2023-04-23 12:30:45",
        expireTime: "2023-05-22 23:59:59",
        status: "used",
        source: "扫码领取"
      }
    ]);
    const totalRecords = common_vendor.computed(() => records.value.length);
    const usedCount = common_vendor.computed(() => records.value.filter((r) => r.status === "used").length);
    const unusedCount = common_vendor.computed(() => records.value.filter((r) => r.status === "unused").length);
    const expiredCount = common_vendor.computed(() => records.value.filter((r) => r.status === "expired").length);
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function showMoreOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["导出数据", "刷新"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              exportData();
              break;
            case 1:
              refreshRecords();
              break;
          }
        }
      });
    }
    function exportData() {
      common_vendor.index.showLoading({
        title: "导出中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "导出成功",
          icon: "success"
        });
      }, 1500);
    }
    function refreshRecords() {
      common_vendor.index.showLoading({
        title: "刷新中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }, 800);
    }
    function switchTab(tab) {
      activeTab.value = tab;
      loadRecords();
    }
    function searchRecords() {
      if (!searchKeyword.value.trim()) {
        return;
      }
      common_vendor.index.showLoading({
        title: "搜索中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
      }, 500);
    }
    function clearSearch() {
      searchKeyword.value = "";
      loadRecords();
    }
    function hideFilterPopup() {
      showFilterPopup.value = false;
    }
    function selectTimeRange(range) {
      timeRange.value = range;
      if (range === "custom") {
        showDateFilter.value = true;
      } else {
        showDateFilter.value = false;
        setDateRangeByTimeRange(range);
      }
    }
    function setDateRangeByTimeRange(range) {
      const now = /* @__PURE__ */ new Date();
      let start = /* @__PURE__ */ new Date();
      let end = /* @__PURE__ */ new Date();
      switch (range) {
        case "today":
          start.setHours(0, 0, 0, 0);
          break;
        case "yesterday":
          start.setDate(now.getDate() - 1);
          start.setHours(0, 0, 0, 0);
          end.setDate(now.getDate() - 1);
          end.setHours(23, 59, 59, 999);
          break;
        case "thisWeek":
          const dayOfWeek = now.getDay() || 7;
          start.setDate(now.getDate() - dayOfWeek + 1);
          start.setHours(0, 0, 0, 0);
          break;
        case "thisMonth":
          start.setDate(1);
          start.setHours(0, 0, 0, 0);
          break;
        default:
          startDate.value = "";
          endDate.value = "";
          return;
      }
      startDate.value = formatDate(start);
      endDate.value = formatDate(end);
    }
    function formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    }
    function showStartDatePicker() {
      currentDatePicker.value = "start";
      showDatePicker.value = true;
    }
    function showEndDatePicker() {
      currentDatePicker.value = "end";
      showDatePicker.value = true;
    }
    function onDateSelect(e) {
      const selectedDate = e.fulldate;
      if (currentDatePicker.value === "start") {
        startDate.value = selectedDate;
      } else {
        endDate.value = selectedDate;
      }
      closeDatePicker();
    }
    function closeDatePicker() {
      showDatePicker.value = false;
    }
    function resetDateFilter() {
      startDate.value = "";
      endDate.value = "";
    }
    function applyDateFilter() {
      showDateFilter.value = false;
      loadRecords();
    }
    function toggleChannel(channel) {
      if (channel === "all") {
        if (selectedChannels.value.includes("all")) {
          selectedChannels.value = [];
        } else {
          selectedChannels.value = ["all"];
        }
      } else {
        const allIndex = selectedChannels.value.indexOf("all");
        if (allIndex !== -1) {
          selectedChannels.value.splice(allIndex, 1);
        }
        const index = selectedChannels.value.indexOf(channel);
        if (index === -1) {
          selectedChannels.value.push(channel);
        } else {
          selectedChannels.value.splice(index, 1);
        }
        if (selectedChannels.value.length === 0) {
          selectedChannels.value = ["all"];
        }
      }
    }
    function setSortOption(sort) {
      sortBy.value = sort;
    }
    function resetFilters() {
      timeRange.value = "thisMonth";
      selectedChannels.value = ["all"];
      sortBy.value = "timeDesc";
      startDate.value = "";
      endDate.value = "";
      showDateFilter.value = false;
    }
    function applyFilters() {
      hideFilterPopup();
      loadRecords();
    }
    function loadRecords() {
      currentPage.value = 1;
      hasMoreData.value = true;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
      }, 500);
    }
    function loadMoreRecords() {
      if (!hasMoreData.value)
        return;
      currentPage.value += 1;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        if (currentPage.value >= 3) {
          hasMoreData.value = false;
        }
      }, 500);
    }
    function maskPhone(phone) {
      if (!phone)
        return "";
      return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    }
    function getStatusClass(status) {
      switch (status) {
        case "used":
          return "status-used";
        case "unused":
          return "status-unused";
        case "expired":
          return "status-expired";
        default:
          return "";
      }
    }
    function getStatusText(status) {
      switch (status) {
        case "used":
          return "已使用";
        case "unused":
          return "未使用";
        case "expired":
          return "已过期";
        default:
          return "未知状态";
      }
    }
    function viewUserDetail(record) {
      common_vendor.index.showToast({
        title: `查看用户：${record.userName}`,
        icon: "none"
      });
    }
    function sendMessage(record) {
      common_vendor.index.showToast({
        title: `发送消息给：${record.userName}`,
        icon: "none"
      });
    }
    function getCurrentDate() {
      const now = /* @__PURE__ */ new Date();
      return formatDate(now);
    }
    function loadCouponRecords(id) {
      couponId.value = id;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
      }, 500);
    }
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage2 = pages[pages.length - 1];
      const options = ((_a = currentPage2.$page) == null ? void 0 : _a.options) || {};
      if (options.id) {
        loadCouponRecords(options.id);
      }
    });
    return {
      activeTab,
      searchKeyword,
      startDate,
      endDate,
      showDateFilter,
      showFilterPopup,
      showDatePicker,
      timeRange,
      selectedChannels,
      sortBy,
      records,
      totalRecords,
      usedCount,
      unusedCount,
      expiredCount,
      hasMoreData,
      goBack,
      showMoreOptions,
      switchTab,
      searchRecords,
      clearSearch,
      showFilterPopup,
      hideFilterPopup,
      selectTimeRange,
      showStartDatePicker,
      showEndDatePicker,
      onDateSelect,
      closeDatePicker,
      resetDateFilter,
      applyDateFilter,
      toggleChannel,
      setSortOption,
      resetFilters,
      applyFilters,
      loadMoreRecords,
      maskPhone,
      getStatusClass,
      getStatusText,
      viewUserDetail,
      sendMessage,
      getCurrentDate
    };
  }
};
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_uni_calendar = common_vendor.resolveComponent("uni-calendar");
  (_component_circle + _component_svg + _component_uni_calendar)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    b: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "1"
    }),
    c: common_vendor.p({
      cx: "19",
      cy: "12",
      r: "1"
    }),
    d: common_vendor.p({
      cx: "5",
      cy: "12",
      r: "1"
    }),
    e: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    f: common_vendor.o((...args) => $setup.showMoreOptions && $setup.showMoreOptions(...args)),
    g: $setup.activeTab === "all" ? 1 : "",
    h: common_vendor.o(($event) => $setup.switchTab("all")),
    i: $setup.activeTab === "unused" ? 1 : "",
    j: common_vendor.o(($event) => $setup.switchTab("unused")),
    k: $setup.activeTab === "used" ? 1 : "",
    l: common_vendor.o(($event) => $setup.switchTab("used")),
    m: $setup.activeTab === "expired" ? 1 : "",
    n: common_vendor.o(($event) => $setup.switchTab("expired")),
    o: common_vendor.o((...args) => $setup.searchRecords && $setup.searchRecords(...args)),
    p: $setup.searchKeyword,
    q: common_vendor.o(($event) => $setup.searchKeyword = $event.detail.value),
    r: $setup.searchKeyword
  }, $setup.searchKeyword ? {
    s: common_vendor.o((...args) => $setup.clearSearch && $setup.clearSearch(...args))
  } : {}, {
    t: common_vendor.o((...args) => _ctx.openFilterPopup && _ctx.openFilterPopup(...args)),
    v: $setup.showDateFilter
  }, $setup.showDateFilter ? {
    w: common_vendor.t($setup.startDate || "请选择"),
    x: common_vendor.o((...args) => $setup.showStartDatePicker && $setup.showStartDatePicker(...args)),
    y: common_vendor.t($setup.endDate || "请选择"),
    z: common_vendor.o((...args) => $setup.showEndDatePicker && $setup.showEndDatePicker(...args)),
    A: common_vendor.o((...args) => $setup.resetDateFilter && $setup.resetDateFilter(...args)),
    B: common_vendor.o((...args) => $setup.applyDateFilter && $setup.applyDateFilter(...args))
  } : {}, {
    C: common_vendor.t($setup.totalRecords),
    D: common_vendor.t($setup.usedCount),
    E: common_vendor.t($setup.unusedCount),
    F: common_vendor.t($setup.expiredCount),
    G: $setup.records.length > 0
  }, $setup.records.length > 0 ? common_vendor.e({
    H: common_vendor.f($setup.records, (record, index, i0) => {
      return common_vendor.e({
        a: record.userAvatar,
        b: common_vendor.t(record.userName),
        c: common_vendor.t($setup.maskPhone(record.userPhone)),
        d: common_vendor.t($setup.getStatusText(record.status)),
        e: common_vendor.n($setup.getStatusClass(record.status)),
        f: common_vendor.t(record.couponTitle),
        g: common_vendor.t(record.couponValue),
        h: common_vendor.t(record.minSpend),
        i: common_vendor.t(record.claimTime),
        j: record.useTime
      }, record.useTime ? {
        k: common_vendor.t(record.useTime)
      } : {}, {
        l: common_vendor.t(record.expireTime),
        m: common_vendor.t(record.source),
        n: common_vendor.o(($event) => $setup.viewUserDetail(record), index),
        o: common_vendor.o(($event) => $setup.sendMessage(record), index),
        p: index
      });
    }),
    I: $setup.hasMoreData
  }, $setup.hasMoreData ? {
    J: common_vendor.o((...args) => $setup.loadMoreRecords && $setup.loadMoreRecords(...args))
  } : {}) : {
    K: common_assets._imports_0$30
  }, {
    L: $setup.showFilterPopup
  }, $setup.showFilterPopup ? {
    M: common_vendor.o((...args) => $setup.hideFilterPopup && $setup.hideFilterPopup(...args)),
    N: common_vendor.o((...args) => $setup.hideFilterPopup && $setup.hideFilterPopup(...args)),
    O: $setup.timeRange === "today" ? 1 : "",
    P: common_vendor.o(($event) => $setup.selectTimeRange("today")),
    Q: $setup.timeRange === "yesterday" ? 1 : "",
    R: common_vendor.o(($event) => $setup.selectTimeRange("yesterday")),
    S: $setup.timeRange === "thisWeek" ? 1 : "",
    T: common_vendor.o(($event) => $setup.selectTimeRange("thisWeek")),
    U: $setup.timeRange === "thisMonth" ? 1 : "",
    V: common_vendor.o(($event) => $setup.selectTimeRange("thisMonth")),
    W: $setup.timeRange === "custom" ? 1 : "",
    X: common_vendor.o(($event) => $setup.selectTimeRange("custom")),
    Y: $setup.selectedChannels.includes("all") ? 1 : "",
    Z: common_vendor.o(($event) => $setup.toggleChannel("all")),
    aa: $setup.selectedChannels.includes("scan") ? 1 : "",
    ab: common_vendor.o(($event) => $setup.toggleChannel("scan")),
    ac: $setup.selectedChannels.includes("share") ? 1 : "",
    ad: common_vendor.o(($event) => $setup.toggleChannel("share")),
    ae: $setup.selectedChannels.includes("activity") ? 1 : "",
    af: common_vendor.o(($event) => $setup.toggleChannel("activity")),
    ag: $setup.selectedChannels.includes("manual") ? 1 : "",
    ah: common_vendor.o(($event) => $setup.toggleChannel("manual")),
    ai: $setup.sortBy === "timeDesc" ? 1 : "",
    aj: common_vendor.o(($event) => $setup.setSortOption("timeDesc")),
    ak: $setup.sortBy === "timeAsc" ? 1 : "",
    al: common_vendor.o(($event) => $setup.setSortOption("timeAsc")),
    am: common_vendor.o((...args) => $setup.resetFilters && $setup.resetFilters(...args)),
    an: common_vendor.o((...args) => $setup.applyFilters && $setup.applyFilters(...args))
  } : {}, {
    ao: $setup.showDatePicker
  }, $setup.showDatePicker ? {
    ap: common_vendor.o($setup.onDateSelect),
    aq: common_vendor.o($setup.closeDatePicker),
    ar: common_vendor.p({
      insert: false,
      ["start-date"]: "2020-01-01",
      ["end-date"]: $setup.getCurrentDate()
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/coupon/records.js.map
