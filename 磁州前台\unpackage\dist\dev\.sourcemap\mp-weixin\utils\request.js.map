{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["/**\r\n * 网络请求封装模块\r\n */\r\n\r\n// API基础URL - 根据环境自动切换\r\nconst BASE_URL = process.env.NODE_ENV === 'development'\r\n  ? 'http://localhost:8080/api'     // 开发环境 - 修复路径匹配\r\n  : 'https://api.czshw.com/api';    // 生产环境\r\n\r\n// HTTP请求方法\r\nconst HTTP_METHOD = {\r\n  GET: 'GET',\r\n  POST: 'POST',\r\n  PUT: 'PUT',\r\n  DELETE: 'DELETE'\r\n};\r\n\r\n// 错误码映射表\r\nconst ERROR_CODE_MAP = {\r\n  400: '请求参数错误',\r\n  401: '未授权，请重新登录',\r\n  403: '拒绝访问',\r\n  404: '请求的资源不存在',\r\n  500: '服务器内部错误',\r\n  502: '网关错误',\r\n  503: '服务不可用',\r\n  504: '网关超时'\r\n};\r\n\r\n// 请求队列，用于取消重复请求\r\nlet requestQueue = [];\r\n\r\n/**\r\n * 移除请求队列中的请求\r\n * @param {Object} config 请求配置\r\n */\r\nconst removeRequest = (config) => {\r\n  const index = requestQueue.findIndex(item => {\r\n    return item.url === config.url && \r\n           item.method === config.method && \r\n           JSON.stringify(item.data) === JSON.stringify(config.data);\r\n  });\r\n  \r\n  if (index !== -1) {\r\n    requestQueue.splice(index, 1);\r\n  }\r\n};\r\n\r\n/**\r\n * 检查是否存在相同的请求\r\n * @param {Object} config 请求配置\r\n * @returns {Boolean} 是否存在\r\n */\r\nconst checkSameRequest = (config) => {\r\n  const index = requestQueue.findIndex(item => {\r\n    return item.url === config.url && \r\n           item.method === config.method && \r\n           JSON.stringify(item.data) === JSON.stringify(config.data);\r\n  });\r\n  \r\n  if (index !== -1) {\r\n    return true;\r\n  }\r\n  \r\n  // 不存在则添加到队列中\r\n  requestQueue.push(config);\r\n  return false;\r\n};\r\n\r\n/**\r\n * 构建完整的API URL\r\n * @param {String} url 接口路径\r\n * @returns {String} 完整的URL\r\n */\r\nconst buildUrl = (url) => {\r\n  if (url.startsWith('http')) {\r\n    return url;\r\n  }\r\n  return `${BASE_URL}${url.startsWith('/') ? url : `/${url}`}`;\r\n};\r\n\r\n/**\r\n * 从本地存储获取token\r\n * @returns {String} token\r\n */\r\nconst getToken = () => {\r\n  try {\r\n    const userInfo = uni.getStorageSync('user_info');\r\n    return userInfo ? userInfo.token : '';\r\n  } catch (e) {\r\n    console.error('获取token失败', e);\r\n    return '';\r\n  }\r\n};\r\n\r\n/**\r\n * 处理响应错误\r\n * @param {Object} response 响应对象\r\n * @returns {Object} 错误对象\r\n */\r\nconst handleResponseError = (response) => {\r\n  const { statusCode, data } = response;\r\n  \r\n  // 处理HTTP错误\r\n  if (statusCode !== 200) {\r\n    const message = ERROR_CODE_MAP[statusCode] || '网络异常，请稍后再试';\r\n    \r\n    // 401特殊处理：清除登录信息并跳转\r\n    if (statusCode === 401) {\r\n      uni.removeStorageSync('user_info');\r\n      setTimeout(() => {\r\n        uni.navigateTo({\r\n          url: '/pages/login/login'\r\n        });\r\n      }, 1500);\r\n    }\r\n    \r\n    return Promise.reject({ message, response });\r\n  }\r\n  \r\n  // 处理业务错误\r\n  if (data && data.code !== 0 && data.code !== 200) {\r\n    return Promise.reject({ message: data.message || '请求失败', response });\r\n  }\r\n  \r\n  return data;\r\n};\r\n\r\n/**\r\n * 统一请求封装\r\n * @param {Object} options 请求配置\r\n * @returns {Promise} Promise对象\r\n */\r\nconst request = (options = {}) => {\r\n  const { url, method = HTTP_METHOD.GET, data = {}, header = {}, loading = true, retry = 1 } = options;\r\n  \r\n  // 构建请求配置\r\n  const config = {\r\n    url: buildUrl(url),\r\n    method,\r\n    data,\r\n    header: {\r\n      'Content-Type': 'application/json',\r\n      ...header\r\n    }\r\n  };\r\n  \r\n  // 添加token\r\n  const token = getToken();\r\n  if (token) {\r\n    config.header.Authorization = `Bearer ${token}`;\r\n  }\r\n  \r\n  // 如果是重复请求，直接返回拒绝\r\n  if (checkSameRequest(config)) {\r\n    return Promise.reject({ message: '请勿重复请求' });\r\n  }\r\n  \r\n  // 显示加载中\r\n  if (loading) {\r\n    uni.showLoading({\r\n      title: '加载中...',\r\n      mask: true\r\n    });\r\n  }\r\n  \r\n  // 发起请求\r\n  return new Promise((resolve, reject) => {\r\n    uni.request({\r\n      ...config,\r\n      success: (response) => {\r\n        try {\r\n          const result = handleResponseError(response);\r\n          resolve(result);\r\n        } catch (error) {\r\n          reject(error);\r\n        }\r\n      },\r\n      fail: (error) => {\r\n        // 网络错误时重试\r\n        if (retry > 0 && (error.errMsg || '').includes('request:fail')) {\r\n          setTimeout(() => {\r\n            options.retry = retry - 1;\r\n            resolve(request(options));\r\n          }, 1000);\r\n          return;\r\n        }\r\n        \r\n        reject({ message: '网络连接失败，请检查网络', error });\r\n      },\r\n      complete: () => {\r\n        // 隐藏加载中\r\n        if (loading) {\r\n          uni.hideLoading();\r\n        }\r\n        // 从队列中移除\r\n        removeRequest(config);\r\n      }\r\n    });\r\n  });\r\n};\r\n\r\n// 导出不同请求方法\r\nexport default {\r\n  get: (url, data = {}, options = {}) => {\r\n    return request({ url, method: HTTP_METHOD.GET, data, ...options });\r\n  },\r\n  post: (url, data = {}, options = {}) => {\r\n    return request({ url, method: HTTP_METHOD.POST, data, ...options });\r\n  },\r\n  put: (url, data = {}, options = {}) => {\r\n    return request({ url, method: HTTP_METHOD.PUT, data, ...options });\r\n  },\r\n  delete: (url, data = {}, options = {}) => {\r\n    return request({ url, method: HTTP_METHOD.DELETE, data, ...options });\r\n  },\r\n  // 导出原始request方法，供特殊需求使用\r\n  request\r\n}; "], "names": ["uni"], "mappings": ";;AAKA,MAAM,WACF;AAIJ,MAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AACV;AAGA,MAAM,iBAAiB;AAAA,EACrB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAGA,IAAI,eAAe,CAAA;AAMnB,MAAM,gBAAgB,CAAC,WAAW;AAC1B,QAAA,QAAQ,aAAa,UAAU,CAAQ,SAAA;AAC3C,WAAO,KAAK,QAAQ,OAAO,OACpB,KAAK,WAAW,OAAO,UACvB,KAAK,UAAU,KAAK,IAAI,MAAM,KAAK,UAAU,OAAO,IAAI;AAAA,EAAA,CAChE;AAED,MAAI,UAAU,IAAI;AACH,iBAAA,OAAO,OAAO,CAAC;AAAA,EAC9B;AACF;AAOA,MAAM,mBAAmB,CAAC,WAAW;AAC7B,QAAA,QAAQ,aAAa,UAAU,CAAQ,SAAA;AAC3C,WAAO,KAAK,QAAQ,OAAO,OACpB,KAAK,WAAW,OAAO,UACvB,KAAK,UAAU,KAAK,IAAI,MAAM,KAAK,UAAU,OAAO,IAAI;AAAA,EAAA,CAChE;AAED,MAAI,UAAU,IAAI;AACT,WAAA;AAAA,EACT;AAGA,eAAa,KAAK,MAAM;AACjB,SAAA;AACT;AAOA,MAAM,WAAW,CAAC,QAAQ;AACpB,MAAA,IAAI,WAAW,MAAM,GAAG;AACnB,WAAA;AAAA,EACT;AACO,SAAA,GAAG,QAAQ,GAAG,IAAI,WAAW,GAAG,IAAI,MAAM,IAAI,GAAG,EAAE;AAC5D;AAMA,MAAM,WAAW,MAAM;AACjB,MAAA;AACI,UAAA,WAAWA,cAAAA,MAAI,eAAe,WAAW;AACxC,WAAA,WAAW,SAAS,QAAQ;AAAA,WAC5B,GAAG;AACVA,kBAAA,MAAA,MAAA,SAAA,0BAAc,aAAa,CAAC;AACrB,WAAA;AAAA,EACT;AACF;AAOA,MAAM,sBAAsB,CAAC,aAAa;AAClC,QAAA,EAAE,YAAY,KAAS,IAAA;AAG7B,MAAI,eAAe,KAAK;AAChB,UAAA,UAAU,eAAe,UAAU,KAAK;AAG9C,QAAI,eAAe,KAAK;AACtBA,0BAAI,kBAAkB,WAAW;AACjC,iBAAW,MAAM;AACfA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QAAA,CACN;AAAA,SACA,IAAI;AAAA,IACT;AAEA,WAAO,QAAQ,OAAO,EAAE,SAAS,SAAU,CAAA;AAAA,EAC7C;AAGA,MAAI,QAAQ,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK;AACzC,WAAA,QAAQ,OAAO,EAAE,SAAS,KAAK,WAAW,QAAQ,UAAU;AAAA,EACrE;AAEO,SAAA;AACT;AAOA,MAAM,UAAU,CAAC,UAAU,OAAO;AAChC,QAAM,EAAE,KAAK,SAAS,YAAY,KAAK,OAAO,CAAI,GAAA,SAAS,CAAI,GAAA,UAAU,MAAM,QAAQ,MAAM;AAG7F,QAAM,SAAS;AAAA,IACb,KAAK,SAAS,GAAG;AAAA,IACjB;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,GAAG;AAAA,IACL;AAAA,EAAA;AAIF,QAAM,QAAQ;AACd,MAAI,OAAO;AACF,WAAA,OAAO,gBAAgB,UAAU,KAAK;AAAA,EAC/C;AAGI,MAAA,iBAAiB,MAAM,GAAG;AAC5B,WAAO,QAAQ,OAAO,EAAE,SAAS,SAAU,CAAA;AAAA,EAC7C;AAGA,MAAI,SAAS;AACXA,kBAAAA,MAAI,YAAY;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,IAAA,CACP;AAAA,EACH;AAGA,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,kBAAAA,MAAI,QAAQ;AAAA,MACV,GAAG;AAAA,MACH,SAAS,CAAC,aAAa;AACjB,YAAA;AACI,gBAAA,SAAS,oBAAoB,QAAQ;AAC3C,kBAAQ,MAAM;AAAA,iBACP,OAAO;AACd,iBAAO,KAAK;AAAA,QACd;AAAA,MACF;AAAA,MACA,MAAM,CAAC,UAAU;AAEf,YAAI,QAAQ,MAAM,MAAM,UAAU,IAAI,SAAS,cAAc,GAAG;AAC9D,qBAAW,MAAM;AACf,oBAAQ,QAAQ,QAAQ;AAChB,oBAAA,QAAQ,OAAO,CAAC;AAAA,aACvB,GAAI;AACP;AAAA,QACF;AAEA,eAAO,EAAE,SAAS,gBAAgB,MAAO,CAAA;AAAA,MAC3C;AAAA,MACA,UAAU,MAAM;AAEd,YAAI,SAAS;AACXA,wBAAA,MAAI,YAAY;AAAA,QAClB;AAEA,sBAAc,MAAM;AAAA,MACtB;AAAA,IAAA,CACD;AAAA,EAAA,CACF;AACH;AAGA,MAAe,YAAA;AAAA,EACb,KAAK,CAAC,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,MAAO;AAC9B,WAAA,QAAQ,EAAE,KAAK,QAAQ,YAAY,KAAK,MAAM,GAAG,QAAA,CAAS;AAAA,EACnE;AAAA,EACA,MAAM,CAAC,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,MAAO;AAC/B,WAAA,QAAQ,EAAE,KAAK,QAAQ,YAAY,MAAM,MAAM,GAAG,QAAA,CAAS;AAAA,EACpE;AAAA,EACA,KAAK,CAAC,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,MAAO;AAC9B,WAAA,QAAQ,EAAE,KAAK,QAAQ,YAAY,KAAK,MAAM,GAAG,QAAA,CAAS;AAAA,EACnE;AAAA,EACA,QAAQ,CAAC,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,MAAO;AACjC,WAAA,QAAQ,EAAE,KAAK,QAAQ,YAAY,QAAQ,MAAM,GAAG,QAAA,CAAS;AAAA,EACtE;AAAA;AAAA,EAEA;AACF;;"}