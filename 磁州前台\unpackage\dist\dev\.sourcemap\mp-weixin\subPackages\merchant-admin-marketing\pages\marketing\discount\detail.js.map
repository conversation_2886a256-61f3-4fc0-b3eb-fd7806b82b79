{"version": 3, "file": "detail.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/discount/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXNjb3VudFxkZXRhaWwudnVl"], "sourcesContent": ["<!-- 满减活动详情页面 (detail.vue) -->\n<template>\n  <view class=\"discount-detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @tap=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">满减活动详情</text>\n      <view class=\"navbar-right\">\n        <view class=\"more-icon\" @tap=\"showMoreOptions\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <circle cx=\"12\" cy=\"12\" r=\"1\"></circle>\n            <circle cx=\"19\" cy=\"12\" r=\"1\"></circle>\n            <circle cx=\"5\" cy=\"12\" r=\"1\"></circle>\n          </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 活动卡片 -->\n    <view class=\"discount-card\">\n      <view class=\"discount-header\">\n        <text class=\"discount-title\">{{discountData.title}}</text>\n        <view class=\"discount-status\" :class=\"'status-'+discountData.status\">{{discountData.statusText}}</view>\n      </view>\n      \n      <view class=\"discount-rules\">\n        <view class=\"rule-item\" v-for=\"(rule, index) in discountData.rules\" :key=\"index\">\n          <view class=\"rule-content\">\n            <text class=\"rule-text\">满 {{rule.minAmount}} 元减 {{rule.discountAmount}} 元</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"validity-period\">\n        <text class=\"validity-label\">活动时间:</text>\n        <text class=\"validity-value\">{{discountData.timeRange}}</text>\n      </view>\n      \n      <view class=\"discount-qrcode\" v-if=\"discountData.status === 'active'\">\n        <image class=\"qrcode-image\" :src=\"discountData.qrCodeUrl\" mode=\"aspectFit\"></image>\n        <text class=\"qrcode-hint\">扫描二维码分享活动</text>\n      </view>\n    </view>\n    \n    <!-- 使用情况 -->\n    <view class=\"usage-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">使用情况</text>\n      </view>\n      \n      <view class=\"stats-grid\">\n        <view class=\"stats-item\">\n          <text class=\"stats-value\">{{discountData.totalOrders}}</text>\n          <text class=\"stats-label\">参与订单</text>\n        </view>\n        <view class=\"stats-item\">\n          <text class=\"stats-value\">{{discountData.totalUsers}}</text>\n          <text class=\"stats-label\">参与用户</text>\n        </view>\n        <view class=\"stats-item\">\n          <text class=\"stats-value\">¥{{discountData.totalDiscount}}</text>\n          <text class=\"stats-label\">优惠金额</text>\n        </view>\n        <view class=\"stats-item\">\n          <text class=\"stats-value\">{{discountData.conversionRate}}%</text>\n          <text class=\"stats-label\">转化率</text>\n        </view>\n      </view>\n      \n      <view class=\"chart-container\">\n        <text class=\"chart-title\">优惠金额趋势</text>\n        <view class=\"chart-placeholder\">\n          <text class=\"placeholder-text\">图表加载中...</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 活动设置 -->\n    <view class=\"settings-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">活动设置</text>\n      </view>\n      \n      <view class=\"settings-list\">\n        <view class=\"settings-item\">\n          <text class=\"item-label\">活动类型</text>\n          <text class=\"item-value\">{{discountData.typeText}}</text>\n        </view>\n        <view class=\"settings-item\">\n          <text class=\"item-label\">适用商品</text>\n          <text class=\"item-value\">{{discountData.applicableProducts}}</text>\n        </view>\n        <view class=\"settings-item\">\n          <text class=\"item-label\">叠加使用</text>\n          <text class=\"item-value\">{{discountData.canStack ? '允许' : '不允许'}}</text>\n        </view>\n        <view class=\"settings-item\">\n          <text class=\"item-label\">每人限用</text>\n          <text class=\"item-value\">{{discountData.perPersonLimit}}次</text>\n        </view>\n        <view class=\"settings-item\">\n          <text class=\"item-label\">活动说明</text>\n          <text class=\"item-value\">{{discountData.instructions}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 订单记录 -->\n    <view class=\"records-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">订单记录</text>\n        <text class=\"view-all\" @tap=\"viewAllRecords\">查看全部</text>\n      </view>\n      \n      <view class=\"records-list\">\n        <view class=\"record-item\" v-for=\"(record, index) in discountData.recentRecords\" :key=\"index\">\n          <view class=\"record-info\">\n            <text class=\"order-number\">订单号: {{record.orderNumber}}</text>\n            <text class=\"record-time\">{{record.time}}</text>\n          </view>\n          <view class=\"record-details\">\n            <text class=\"user-name\">{{record.userName}}</text>\n            <text class=\"discount-amount\">-¥{{record.discountAmount}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"empty-records\" v-if=\"discountData.recentRecords.length === 0\">\n        <text class=\"empty-text\">暂无订单记录</text>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-action-bar\">\n      <view class=\"action-button edit\" @tap=\"editDiscount\">\n        <view class=\"button-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <path d=\"M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z\"></path>\n          </svg>\n        </view>\n        <text class=\"button-text\">编辑</text>\n      </view>\n      \n      <view \n        class=\"action-button\" \n        :class=\"discountData.status === 'active' ? 'pause' : 'activate'\"\n        @tap=\"toggleDiscountStatus\">\n        <view class=\"button-icon\">\n          <svg v-if=\"discountData.status === 'active'\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <rect x=\"6\" y=\"4\" width=\"4\" height=\"16\"></rect>\n            <rect x=\"14\" y=\"4\" width=\"4\" height=\"16\"></rect>\n          </svg>\n          <svg v-else xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <polygon points=\"5 3 19 12 5 21 5 3\"></polygon>\n          </svg>\n        </view>\n        <text class=\"button-text\">{{discountData.status === 'active' ? '暂停' : '启用'}}</text>\n      </view>\n      \n      <view class=\"action-button share\" @tap=\"shareDiscount\">\n        <view class=\"button-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <circle cx=\"18\" cy=\"5\" r=\"3\"></circle>\n            <circle cx=\"6\" cy=\"12\" r=\"3\"></circle>\n            <circle cx=\"18\" cy=\"19\" r=\"3\"></circle>\n            <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\"></line>\n            <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\"></line>\n          </svg>\n        </view>\n        <text class=\"button-text\">分享</text>\n      </view>\n      \n      <view class=\"action-button delete\" @tap=\"confirmDeleteDiscount\">\n        <view class=\"button-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <polyline points=\"3 6 5 6 21 6\"></polyline>\n            <path d=\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\"></path>\n            <line x1=\"10\" y1=\"11\" x2=\"10\" y2=\"17\"></line>\n            <line x1=\"14\" y1=\"11\" x2=\"14\" y2=\"17\"></line>\n          </svg>\n        </view>\n        <text class=\"button-text\">删除</text>\n      </view>\n    </view>\n    \n    <!-- 分享弹窗 -->\n    <view class=\"share-popup\" v-if=\"showSharePopup\">\n      <view class=\"popup-mask\" @tap=\"hideSharePopup\"></view>\n      <view class=\"popup-content\">\n        <view class=\"popup-header\">\n          <text class=\"popup-title\">分享活动</text>\n          <view class=\"popup-close\" @tap=\"hideSharePopup\">×</view>\n        </view>\n        \n        <view class=\"share-options\">\n          <view class=\"share-option\" @tap=\"shareToChannel('wechat')\">\n            <view class=\"option-icon wechat\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <path d=\"M9.5 9.5c-.3-2.1 1.6-4 4.8-4.3 3.1-.3 5.5 1.2 5.9 3.5.4 2.3-1.5 4.8-4.5 5.2-1 .1-1.9 0-2.7-.4l-2.4 1.2.5-2.2c-1.1-.8-1.8-1.9-1.6-3z\"></path>\n                <path d=\"M4 14.5c-.5-2.5 2.3-5 6.2-5.5 3.9-.5 7.5 1 8 3.5.5 2.5-1.8 5-5.7 5.5-1.2.2-2.3 0-3.4-.4l-3 1.5.6-2.7c-1.4-1-2.4-2.3-2.7-3.9z\"></path>\n              </svg>\n            </view>\n            <text class=\"option-name\">微信</text>\n          </view>\n          \n          <view class=\"share-option\" @tap=\"shareToChannel('moments')\">\n            <view class=\"option-icon moments\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                <path d=\"M12 8L12 16\"></path>\n                <path d=\"M8 12L16 12\"></path>\n              </svg>\n            </view>\n            <text class=\"option-name\">朋友圈</text>\n          </view>\n          \n          <view class=\"share-option\" @tap=\"shareToChannel('link')\">\n            <view class=\"option-icon link\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"></path>\n                <path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"></path>\n              </svg>\n            </view>\n            <text class=\"option-name\">复制链接</text>\n          </view>\n          \n          <view class=\"share-option\" @tap=\"shareToChannel('qrcode')\">\n            <view class=\"option-icon qrcode\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\"></rect>\n                <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\"></rect>\n                <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\"></rect>\n                <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\"></rect>\n              </svg>\n            </view>\n            <text class=\"option-name\">二维码</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue';\n\nexport default {\n  setup() {\n    // 响应式状态\n    const discountId = ref('');\n    const discountData = reactive({\n      id: 1,\n      title: '春季促销活动',\n      status: 'active',\n      statusText: '进行中',\n      rules: [\n        { minAmount: 100, discountAmount: 10 },\n        { minAmount: 200, discountAmount: 25 },\n        { minAmount: 300, discountAmount: 50 }\n      ],\n      timeRange: '2023-04-01 ~ 2023-04-30',\n      qrCodeUrl: '/static/images/discount-qrcode-placeholder.png',\n      totalOrders: 352,\n      totalUsers: 280,\n      totalDiscount: '8,562.50',\n      conversionRate: 65.3,\n      typeText: '满减活动',\n      applicableProducts: '全部商品',\n      canStack: false,\n      perPersonLimit: 3,\n      instructions: '活动期间，每位用户最多可使用3次满减优惠，不可与其他优惠同时使用',\n      recentRecords: [\n        {\n          id: 1,\n          orderNumber: 'DD20230420001',\n          userName: '张三',\n          time: '2023-04-20 14:30',\n          discountAmount: '25.00'\n        },\n        {\n          id: 2,\n          orderNumber: 'DD20230420002',\n          userName: '李四',\n          time: '2023-04-20 15:45',\n          discountAmount: '10.00'\n        },\n        {\n          id: 3,\n          orderNumber: 'DD20230421003',\n          userName: '王五',\n          time: '2023-04-21 09:20',\n          discountAmount: '50.00'\n        }\n      ]\n    });\n    \n    const showSharePopup = ref(false);\n    \n    // 方法\n    function goBack() {\n      uni.navigateBack();\n    }\n    \n    function showMoreOptions() {\n      uni.showActionSheet({\n        itemList: ['复制活动信息', '导出数据', '设为模板'],\n        success: (res) => {\n          // 处理更多选项\n          switch(res.tapIndex) {\n            case 0:\n              // 复制活动信息\n              uni.setClipboardData({\n                data: `${discountData.title}：${discountData.rules.map(rule => `满${rule.minAmount}减${rule.discountAmount}`).join('，')}，活动时间：${discountData.timeRange}`,\n                success: () => {\n                  uni.showToast({\n                    title: '已复制活动信息',\n                    icon: 'success'\n                  });\n                }\n              });\n              break;\n            case 1:\n              // 导出数据\n              uni.showToast({\n                title: '正在导出数据...',\n                icon: 'loading'\n              });\n              setTimeout(() => {\n                uni.showToast({\n                  title: '导出成功',\n                  icon: 'success'\n                });\n              }, 1500);\n              break;\n            case 2:\n              // 设为模板\n              uni.showToast({\n                title: '已设为模板',\n                icon: 'success'\n              });\n              break;\n          }\n        }\n      });\n    }\n    \n    function toggleDiscountStatus() {\n      const isActive = discountData.status === 'active';\n      const newStatus = isActive ? 'paused' : 'active';\n      const statusText = isActive ? '已暂停' : '进行中';\n      \n      // 在实际应用中，这里应该调用API更新状态\n      \n      // 本地状态更新示例\n      discountData.status = newStatus;\n      discountData.statusText = statusText;\n      \n      uni.showToast({\n        title: isActive ? '已暂停活动' : '已启用活动',\n        icon: 'success'\n      });\n    }\n    \n    function confirmDeleteDiscount() {\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除\"${discountData.title}\"吗？此操作无法撤销。`,\n        confirmColor: '#FF3B30',\n        success: (res) => {\n          if (res.confirm) {\n            // 在实际应用中，这里应该调用API删除\n            uni.showToast({\n              title: '已删除活动',\n              icon: 'success'\n            });\n            \n            setTimeout(() => {\n              uni.navigateBack();\n            }, 1500);\n          }\n        }\n      });\n    }\n    \n    function editDiscount() {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/edit?id=${discountData.id}`,\n        animationType: 'slide-in-right'\n      });\n    }\n    \n    function viewAllRecords() {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/records?id=${discountData.id}`,\n        animationType: 'slide-in-right'\n      });\n    }\n    \n    function shareDiscount() {\n      showSharePopup.value = true;\n    }\n    \n    function hideSharePopup() {\n      showSharePopup.value = false;\n    }\n    \n    function shareToChannel(channel) {\n      // 在实际应用中，这里应该实现不同渠道的分享逻辑\n      \n      uni.showToast({\n        title: `已分享到${channel === 'wechat' ? '微信' : channel === 'moments' ? '朋友圈' : channel === 'link' ? '链接已复制' : '二维码已保存'}`,\n        icon: 'success'\n      });\n      \n      hideSharePopup();\n    }\n    \n    function loadDiscountData(id) {\n      // 在实际应用中，这里应该调用API获取满减活动详情\n      discountId.value = id;\n      \n      // 模拟加载数据\n      uni.showLoading({\n        title: '加载中...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n      }, 500);\n    }\n    \n    onMounted(() => {\n      const pages = getCurrentPages();\n      const currentPage = pages[pages.length - 1];\n      const options = currentPage.$page?.options || {};\n      \n      if (options.id) {\n        loadDiscountData(options.id);\n      }\n    });\n    \n    return {\n      discountData,\n      showSharePopup,\n      goBack,\n      showMoreOptions,\n      toggleDiscountStatus,\n      confirmDeleteDiscount,\n      editDiscount,\n      viewAllRecords,\n      shareDiscount,\n      hideSharePopup,\n      shareToChannel\n    };\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.discount-detail-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 80px; /* 为底部操作栏留出空间 */\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FDEB71, #F8D800);\n  color: #333;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #333;\n  border-bottom: 2px solid #333;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.more-icon {\n  width: 24px;\n  height: 24px;\n  color: #333;\n}\n\n/* 活动卡片样式 */\n.discount-card {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: hidden;\n}\n\n.discount-card::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #FDEB71, #F8D800);\n}\n\n.discount-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.discount-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.discount-status {\n  padding: 4px 10px;\n  border-radius: 12px;\n  font-size: 12px;\n  color: white;\n}\n\n.status-active {\n  background: #34C759;\n}\n\n.status-expired {\n  background: #8E8E93;\n}\n\n.status-upcoming {\n  background: #FF9500;\n}\n\n.status-paused {\n  background: #FF9500;\n}\n\n.discount-rules {\n  margin-bottom: 15px;\n}\n\n.rule-item {\n  margin-bottom: 8px;\n}\n\n.rule-content {\n  background: rgba(248, 216, 0, 0.1);\n  border-radius: 8px;\n  padding: 8px 12px;\n  display: inline-block;\n}\n\n.rule-text {\n  font-size: 15px;\n  font-weight: 500;\n  color: #D4B100;\n}\n\n.validity-period {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.validity-label {\n  font-size: 14px;\n  color: #999;\n  margin-right: 5px;\n}\n\n.validity-value {\n  font-size: 14px;\n  color: #333;\n}\n\n.discount-qrcode {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px dashed #eee;\n}\n\n.qrcode-image {\n  width: 120px;\n  height: 120px;\n  margin-bottom: 10px;\n}\n\n.qrcode-hint {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 使用情况样式 */\n.usage-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.stats-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 15px;\n}\n\n.stats-item {\n  width: 25%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stats-value {\n  font-size: 18px;\n  font-weight: bold;\n  color: #F8D800;\n  margin-bottom: 5px;\n}\n\n.stats-label {\n  font-size: 12px;\n  color: #999;\n}\n\n.chart-container {\n  padding: 15px 0;\n  border-top: 1px solid #f0f0f0;\n}\n\n.chart-title {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.chart-placeholder {\n  height: 150px;\n  background: #f9f9f9;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.placeholder-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 活动设置样式 */\n.settings-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.settings-list {\n  \n}\n\n.settings-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.settings-item:last-child {\n  border-bottom: none;\n}\n\n.item-label {\n  font-size: 14px;\n  color: #666;\n}\n\n.item-value {\n  font-size: 14px;\n  color: #333;\n  max-width: 60%;\n  text-align: right;\n}\n\n/* 订单记录样式 */\n.records-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.view-all {\n  font-size: 14px;\n  color: #F8D800;\n}\n\n.records-list {\n  \n}\n\n.record-item {\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.record-item:last-child {\n  border-bottom: none;\n}\n\n.record-info {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 5px;\n}\n\n.order-number {\n  font-size: 14px;\n  color: #333;\n}\n\n.record-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.record-details {\n  display: flex;\n  justify-content: space-between;\n}\n\n.user-name {\n  font-size: 14px;\n  color: #666;\n}\n\n.discount-amount {\n  font-size: 14px;\n  color: #F8D800;\n  font-weight: 500;\n}\n\n.empty-records {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 30px 0;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 底部操作栏样式 */\n.bottom-action-bar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #fff;\n  display: flex;\n  padding: 10px 15px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  z-index: 90;\n}\n\n.action-button {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 5px 0;\n}\n\n.button-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 4px;\n}\n\n.button-text {\n  font-size: 12px;\n}\n\n.action-button.edit {\n  color: #007AFF;\n}\n\n.action-button.pause {\n  color: #FF9500;\n}\n\n.action-button.activate {\n  color: #34C759;\n}\n\n.action-button.share {\n  color: #5856D6;\n}\n\n.action-button.delete {\n  color: #FF3B30;\n}\n\n/* 分享弹窗样式 */\n.share-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 999;\n}\n\n.popup-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n}\n\n.popup-content {\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  border-top-left-radius: 16px;\n  border-top-right-radius: 16px;\n  padding: 15px;\n  transform: translateY(0);\n  transition: transform 0.3s;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.popup-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.popup-close {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  color: #999;\n}\n\n.share-options {\n  display: flex;\n  justify-content: space-around;\n  padding: 10px 0 20px;\n}\n\n.share-option {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.option-icon {\n  width: 50px;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 8px;\n  color: #fff;\n}\n\n.option-icon.wechat {\n  background: #07C160;\n}\n\n.option-icon.moments {\n  background: #07C160;\n}\n\n.option-icon.link {\n  background: #007AFF;\n}\n\n.option-icon.qrcode {\n  background: #FF9500;\n}\n\n.option-name {\n  font-size: 12px;\n  color: #333;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/discount/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "onMounted"], "mappings": ";;AAwPA,MAAK,YAAU;AAAA,EACb,QAAQ;AAEN,UAAM,aAAaA,kBAAI,EAAE;AACzB,UAAM,eAAeC,cAAAA,SAAS;AAAA,MAC5B,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,OAAO;AAAA,QACL,EAAE,WAAW,KAAK,gBAAgB,GAAI;AAAA,QACtC,EAAE,WAAW,KAAK,gBAAgB,GAAI;AAAA,QACtC,EAAE,WAAW,KAAK,gBAAgB,GAAG;AAAA,MACtC;AAAA,MACD,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,eAAe;AAAA,QACb;AAAA,UACE,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,UAAU;AAAA,UACV,MAAM;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,UAAU;AAAA,UACV,MAAM;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,UAAU;AAAA,UACV,MAAM;AAAA,UACN,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF,CAAC;AAED,UAAM,iBAAiBD,kBAAI,KAAK;AAGhC,aAAS,SAAS;AAChBE,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,aAAS,kBAAkB;AACzBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,UAAU,QAAQ,MAAM;AAAA,QACnC,SAAS,CAAC,QAAQ;AAEhB,kBAAO,IAAI,UAAQ;AAAA,YACjB,KAAK;AAEHA,4BAAAA,MAAI,iBAAiB;AAAA,gBACnB,MAAM,GAAG,aAAa,KAAK,IAAI,aAAa,MAAM,IAAI,UAAQ,IAAI,KAAK,SAAS,IAAI,KAAK,cAAc,EAAE,EAAE,KAAK,GAAG,CAAC,SAAS,aAAa,SAAS;AAAA,gBACnJ,SAAS,MAAM;AACbA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBACR,CAAC;AAAA,gBACH;AAAA,cACF,CAAC;AACD;AAAA,YACF,KAAK;AAEHA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AACD,yBAAW,MAAM;AACfA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,cACF,GAAE,IAAI;AACP;AAAA,YACF,KAAK;AAEHA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AACD;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,uBAAuB;AAC9B,YAAM,WAAW,aAAa,WAAW;AACzC,YAAM,YAAY,WAAW,WAAW;AACxC,YAAM,aAAa,WAAW,QAAQ;AAKtC,mBAAa,SAAS;AACtB,mBAAa,aAAa;AAE1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,WAAW,UAAU;AAAA,QAC5B,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,aAAS,wBAAwB;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,aAAa,KAAK;AAAA,QACpC,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAED,uBAAW,MAAM;AACfA,4BAAG,MAAC,aAAY;AAAA,YACjB,GAAE,IAAI;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,eAAe;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0EAA0E,aAAa,EAAE;AAAA,QAC9F,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6EAA6E,aAAa,EAAE;AAAA,QACjG,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,aAAS,gBAAgB;AACvB,qBAAe,QAAQ;AAAA,IACzB;AAEA,aAAS,iBAAiB;AACxB,qBAAe,QAAQ;AAAA,IACzB;AAEA,aAAS,eAAe,SAAS;AAG/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,OAAO,YAAY,WAAW,OAAO,YAAY,YAAY,QAAQ,YAAY,SAAS,UAAU,QAAQ;AAAA,QACnH,MAAM;AAAA,MACR,CAAC;AAED;IACF;AAEA,aAAS,iBAAiB,IAAI;AAE5B,iBAAW,QAAQ;AAGnBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAEAC,kBAAAA,UAAU,MAAM;;AACd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,YAAU,iBAAY,UAAZ,mBAAmB,YAAW,CAAA;AAE9C,UAAI,QAAQ,IAAI;AACd,yBAAiB,QAAQ,EAAE;AAAA,MAC7B;AAAA,IACF,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxcA,GAAG,WAAW,eAAe;"}