"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "points-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const currentTab = common_vendor.ref(0);
    const isRefreshing = common_vendor.ref(false);
    const tabs = ["全部", "获取", "使用"];
    const records = common_vendor.ref([
      {
        id: 1,
        title: "每日签到",
        time: "10:25",
        date: "2023-11-25",
        points: 5,
        type: "income"
      },
      {
        id: 2,
        title: "浏览商家完成",
        time: "15:40",
        date: "2023-11-25",
        points: 10,
        type: "income"
      },
      {
        id: 3,
        title: "兑换优惠券",
        time: "18:33",
        date: "2023-11-24",
        points: 100,
        type: "expense"
      },
      {
        id: 4,
        title: "分享小程序",
        time: "09:15",
        date: "2023-11-23",
        points: 20,
        type: "income"
      },
      {
        id: 5,
        title: "评论互动",
        time: "14:22",
        date: "2023-11-22",
        points: 5,
        type: "income"
      },
      {
        id: 6,
        title: "兑换会员月卡",
        time: "11:05",
        date: "2023-11-20",
        points: 500,
        type: "expense"
      }
    ]);
    const filteredRecords = common_vendor.computed(() => {
      if (currentTab.value === 0) {
        return records.value;
      } else if (currentTab.value === 1) {
        return records.value.filter((record) => record.type === "income");
      } else {
        return records.value.filter((record) => record.type === "expense");
      }
    });
    function formatDateLabel(dateStr) {
      const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
      const yesterday = new Date(Date.now() - 864e5).toISOString().split("T")[0];
      if (dateStr === today) {
        return "今天";
      } else if (dateStr === yesterday) {
        return "昨天";
      } else {
        const date = new Date(dateStr);
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
    }
    const groupedRecords = common_vendor.computed(() => {
      const groups = {};
      filteredRecords.value.forEach((record) => {
        if (!groups[record.date]) {
          groups[record.date] = {
            date: formatDateLabel(record.date),
            records: []
          };
        }
        groups[record.date].records.push(record);
      });
      return Object.values(groups).sort((a, b) => {
        const dateA = new Date(a.date.replace("今天", (/* @__PURE__ */ new Date()).toISOString().split("T")[0]));
        const dateB = new Date(b.date.replace("今天", (/* @__PURE__ */ new Date()).toISOString().split("T")[0]));
        return dateB - dateA;
      });
    });
    common_vendor.onMounted(() => {
      const windowInfo = common_vendor.index.getWindowInfo();
      statusBarHeight.value = windowInfo.statusBarHeight || 20;
    });
    function switchTab(index) {
      currentTab.value = index;
    }
    function refreshRecords() {
      isRefreshing.value = true;
      setTimeout(() => {
        isRefreshing.value = false;
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "none"
        });
      }, 1500);
    }
    function goBack() {
      common_vendor.index.navigateBack();
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + 44 + "px",
        b: common_assets._imports_0$7,
        c: common_vendor.o(goBack),
        d: statusBarHeight.value + "px",
        e: common_vendor.f(tabs, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab),
            b: currentTab.value === index ? 1 : "",
            c: index,
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        f: filteredRecords.value.length > 0
      }, filteredRecords.value.length > 0 ? {
        g: common_vendor.f(groupedRecords.value, (group, groupIndex, i0) => {
          return {
            a: common_vendor.t(group.date),
            b: common_vendor.f(group.records, (record, index, i1) => {
              return {
                a: common_vendor.t(record.title),
                b: common_vendor.t(record.time),
                c: common_vendor.t(record.type === "income" ? "+" : "-"),
                d: common_vendor.t(record.points),
                e: record.type === "income" ? 1 : "",
                f: record.type === "expense" ? 1 : "",
                g: index
              };
            }),
            c: groupIndex
          };
        })
      } : {
        h: common_assets._imports_1$45
      }, {
        i: common_vendor.o(refreshRecords),
        j: isRefreshing.value,
        k: statusBarHeight.value + 44 + "px"
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/checkin/pages/points-detail.js.map
