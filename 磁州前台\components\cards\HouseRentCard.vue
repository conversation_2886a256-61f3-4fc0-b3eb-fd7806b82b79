<template>
  <BaseInfoCard :item="item">
    <template #content>
      <view class="house-details">
        <!-- 房屋基本信息 -->
        <view class="house-basic-info">
          <view class="house-type" v-if="item.houseType">
            <view class="house-icon type-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </view>
            <text class="house-text">{{item.houseType}}</text>
          </view>
          
          <view class="house-size" v-if="item.size">
            <view class="house-icon size-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="3" y1="9" x2="21" y2="9"></line>
                <line x1="9" y1="21" x2="9" y2="9"></line>
              </svg>
            </view>
            <text class="house-text">{{item.size}}㎡</text>
          </view>
          
          <view class="house-floor" v-if="item.floor">
            <view class="house-icon floor-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <line x1="8" y1="21" x2="16" y2="21"></line>
                <line x1="12" y1="17" x2="12" y2="21"></line>
              </svg>
            </view>
            <text class="house-text">{{item.floor}}</text>
          </view>
          
          <view class="house-direction" v-if="item.direction">
            <view class="house-icon direction-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="16.24 7.76 14.12 14.12 7.76 16.24"></polyline>
                <line x1="12" y1="12" x2="12.01" y2="12"></line>
              </svg>
            </view>
            <text class="house-text">{{item.direction}}</text>
          </view>
        </view>
        
        <!-- 房屋配套设施 -->
        <view class="house-facilities" v-if="item.facilities && item.facilities.length">
          <view class="facility-tag" v-for="(facility, index) in item.facilities" :key="index">
            <view class="facility-icon" :class="getFacilityIconClass(facility)">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </view>
            <text class="facility-text">{{facility}}</text>
          </view>
        </view>
        
        <!-- 房屋特色标签 -->
        <view class="house-features" v-if="item.features && item.features.length">
          <view class="feature-tag" v-for="(feature, index) in item.features" :key="index">
            <text class="feature-text">{{feature}}</text>
          </view>
        </view>
        
        <!-- 房屋地理位置 -->
        <view class="house-location" v-if="item.location">
          <view class="location-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </view>
          <text class="location-text">{{item.location}}</text>
        </view>
      </view>
    </template>
  </BaseInfoCard>
</template>

<script setup>
import BaseInfoCard from './BaseInfoCard.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

function getFacilityIconClass(facility) {
  const facilityMap = {
    '空调': 'ac-icon',
    '热水器': 'water-icon',
    '洗衣机': 'washer-icon',
    '冰箱': 'fridge-icon',
    '电视': 'tv-icon',
    '宽带': 'wifi-icon',
    '衣柜': 'wardrobe-icon',
    '床': 'bed-icon',
    '沙发': 'sofa-icon',
    '天然气': 'gas-icon'
  };
  
  return facilityMap[facility] || 'default-icon';
}
</script>

<style scoped>
.house-details {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

/* 房屋基本信息 */
.house-basic-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.house-type, .house-size, .house-floor, .house-direction {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.house-icon {
  margin-right: 8rpx;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.type-icon {
  color: #FF9500;
}

.size-icon {
  color: #34C759;
}

.floor-icon {
  color: #5856D6;
}

.direction-icon {
  color: #007AFF;
}

.house-text {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
}

/* 房屋配套设施 */
.house-facilities {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.facility-tag {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 176, 199, 0.1));
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid rgba(52, 199, 89, 0.2);
}

.facility-icon {
  margin-right: 6rpx;
  color: #34C759;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ac-icon, .water-icon {
  color: #007AFF;
}

.washer-icon, .fridge-icon {
  color: #5856D6;
}

.tv-icon, .wifi-icon {
  color: #FF9500;
}

.wardrobe-icon, .bed-icon, .sofa-icon {
  color: #FF3B30;
}

.gas-icon {
  color: #FF2D55;
}

.facility-text {
  font-size: 22rpx;
  color: #34C759;
  font-weight: 500;
}

/* 房屋特色标签 */
.house-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.feature-tag {
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 59, 48, 0.1));
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid rgba(255, 149, 0, 0.2);
}

.feature-text {
  font-size: 22rpx;
  color: #FF9500;
  font-weight: 500;
}

/* 房屋地理位置 */
.house-location {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 12rpx 16rpx;
  border-radius: 16rpx;
  margin-top: 16rpx;
}

.location-icon {
  color: #007AFF;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-text {
  font-size: 26rpx;
  color: #636366;
  flex: 1;
}
</style> 