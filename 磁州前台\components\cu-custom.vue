<template>
  <view class="cu-custom" :style="[{height: CustomBar + 'px'}]">
    <view class="cu-bar fixed" :style="style" :class="[bgImage!=''?'none-bg text-white bg-img':'',bgColor]">
      <view class="action back-action" @tap="BackPage" hover-class="back-hover" v-if="isBack">
        <text class="cuIcon-back back-icon"></text>
        <slot name="backText"></slot>
      </view>
      <view class="content" :style="[{top:StatusBar + 'px'}]">
        <slot name="content"></slot>
      </view>
      <slot name="right"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'cu-custom',
  data() {
    return {
      StatusBar: this.StatusBar,
      CustomBar: this.CustomBar
    };
  },
  computed: {
    style() {
      var StatusBar = this.StatusBar;
      var CustomBar = this.CustomBar;
      var bgImage = this.bgImage;
      var style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
      if (this.bgImage) {
        style = `${style}background-image:url(${bgImage});`;
      }
      return style;
    }
  },
  props: {
    bgColor: {
      type: String,
      default: ''
    },
    isBack: {
      type: Boolean,
      default: false
    },
    bgImage: {
      type: String,
      default: ''
    }
  },
  created() {
    this.StatusBar = uni.getSystemInfoSync().statusBarHeight;
    this.CustomBar = this.StatusBar + 66;
  },
  methods: {
    BackPage() {
      console.log('返回按钮被点击');
      
      // 添加振动反馈
      uni.vibrateShort({
        success: function() {
          console.log('振动成功');
        }
      });
      
      // 延迟100毫秒再返回，让用户感受到振动反馈
      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
          fail: () => {
            // 如果返回失败，跳转到我的页面
            uni.switchTab({
              url: '/pages/my/my'
            });
          }
        });
      }, 100);
      
      // 触发返回事件
      this.$emit('back');
    }
  }
}
</script>

<style>
.cu-custom {
  display: block;
  position: relative;
  width: 100%;
  z-index: 9999;
}

.cu-bar {
  display: flex;
  position: relative;
  align-items: center;
  min-height: 30rpx;
  justify-content: space-between;
  padding: 0 15rpx;
}

.cu-bar.fixed {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1024;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar .action {
  display: flex;
  align-items: center;
  height: 30rpx;
  justify-content: center;
  max-width: 100%;
  margin: 0;
  position: relative;
}

.cu-bar .back-action {
  margin-left: 15rpx;
  font-size: 24rpx;
  min-width: 30rpx;
  height: 44rpx;
  padding: 0 10rpx;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-radius: 16rpx;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.back-hover {
  opacity: 0.8;
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.1);
}

.cu-bar .content {
  font-size: 32rpx;
  position: absolute;
  left: 0;
  right: 0;
  top: 0px;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44rpx;
  transform: translateY(50%);
  color: #ffffff;
  font-weight: 600;
  text-align: center;
  width: 100%;
  padding: 0 60rpx;
}

.back-icon {
  width: 38rpx;
  height: 38rpx;
  margin-right: 0;
}

.cu-custom .cu-bar {
  min-height: 0;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  padding-bottom: 0px;
}

.cu-custom .cu-bar .action {
  display: flex;
  align-items: center;
  height: 30rpx;
  justify-content: flex-start;
  max-width: 60rpx;
  margin-left: 15rpx;
  font-size: 22rpx;
}

.cu-custom .cu-bar .action .back-icon {
  width: 38rpx;
  height: 38rpx;
  margin-right: 0;
}

.text-white {
  color: #ffffff;
}

/* 添加统一的蓝色系风格 */
.bg-blue {
  background-color: #007AFF;
  color: #ffffff;
}

.bg-gradient-blue {
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  color: #ffffff;
}

.bg-white {
  background-color: #FFFFFF;
  color: #333333;
}

/* 增加点击区域和反馈 */
.cu-bar .action.back-action {
  cursor: pointer;
  transition: all 0.2s;
}

/* 确保文本居中 */
.text-center {
  text-align: center;
  width: 100%;
  display: block;
}
</style> 