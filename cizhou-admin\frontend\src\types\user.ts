// 用户相关类型定义

export interface UserInfo {
  id: string
  username: string
  realName: string
  phone: string
  email: string
  avatar: string
  status: number
  createTime: string
  lastLoginTime: string
  roles: string[]
  permissions: string[]
}

export interface LoginParams {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

export interface LoginResponse {
  token: string
  userInfo: UserInfo
  expiresIn: number
}

export interface ChangePasswordParams {
  oldPassword: string
  newPassword: string
}

export interface UserPermissions {
  permissions: string[]
  roles: string[]
}

export interface RefreshTokenResponse {
  token: string
  expiresIn: number
}

// 用户列表查询参数
export interface UserListParams {
  page: number
  size: number
  username?: string
  realName?: string
  phone?: string
  email?: string
  status?: number
  startTime?: string
  endTime?: string
}

// 用户列表项
export interface UserListItem {
  id: string
  username: string
  realName: string
  phone: string
  email: string
  avatar: string
  status: number
  level: number
  points: number
  balance: number
  registerSource: string
  createTime: string
  lastLoginTime: string
}

// 用户认证信息
export interface UserAuthInfo {
  id: string
  userId: string
  authType: number
  realName: string
  idCard: string
  idCardFront: string
  idCardBack: string
  businessLicense?: string
  businessLicenseImg?: string
  status: number
  auditorId?: string
  auditTime?: string
  auditRemark?: string
  applyTime: string
}

// 用户认证审核参数
export interface UserAuthAuditParams {
  id: string
  status: number
  auditRemark?: string
}

// 用户状态枚举
export enum UserStatus {
  DISABLED = 0,
  NORMAL = 1,
  FROZEN = 2
}

// 用户等级枚举
export enum UserLevel {
  NORMAL = 1,
  VIP = 2,
  SVIP = 3
}

// 认证状态枚举
export enum AuthStatus {
  PENDING = 0,
  APPROVED = 1,
  REJECTED = 2
}

// 认证类型枚举
export enum AuthType {
  ID_CARD = 1,
  BUSINESS_LICENSE = 2
}
