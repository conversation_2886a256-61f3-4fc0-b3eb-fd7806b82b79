"use strict";
const common_vendor = require("../common/vendor.js");
const utils_distributionService = require("../utils/distributionService.js");
const common_assets = require("../common/assets.js");
const _sfc_main = {
  name: "DistributionSection",
  props: {
    // 商品/活动ID
    itemId: {
      type: [String, Number],
      required: true
    },
    // 商品/活动类型: product, coupon, discount, flash, group
    itemType: {
      type: String,
      default: "product"
    },
    // 商品/活动标题
    itemTitle: {
      type: String,
      default: ""
    },
    // 商品/活动价格
    itemPrice: {
      type: [String, Number],
      default: 0
    },
    // 佣金比例
    commissionRate: {
      type: [String, Number],
      default: 20
    }
  },
  computed: {
    // 计算佣金金额
    commissionAmount() {
      const price = parseFloat(this.itemPrice);
      const rate = parseFloat(this.commissionRate);
      if (isNaN(price) || isNaN(rate))
        return "0";
      return (price * rate / 100).toFixed(2);
    },
    // 分销标题
    title() {
      const typeMap = {
        "product": "分销此商品",
        "coupon": "分销此优惠券",
        "discount": "分销此活动",
        "flash": "分销此秒杀",
        "group": "分销此拼团"
      };
      return typeMap[this.itemType] || "分销此商品";
    },
    // 根据活动类型设置对应的样式类名
    itemTypeClass() {
      return `type-${this.itemType}`;
    }
  },
  methods: {
    // 处理点击事件
    async handleClick() {
      const isDistributor = common_vendor.index.getStorageSync("isDistributor") || false;
      if (isDistributor) {
        this.generateDistributionContent();
      } else {
        this.showDistributionPopup();
      }
    },
    // 生成分销内容
    async generateDistributionContent() {
      common_vendor.index.showActionSheet({
        itemList: ["生成分销海报", "复制分销链接", "生成小程序码"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.generatePoster();
              break;
            case 1:
              this.copyLink();
              break;
            case 2:
              this.generateQrCode();
              break;
          }
        }
      });
    },
    // 生成分销海报
    async generatePoster() {
      common_vendor.index.showLoading({ title: "生成中..." });
      try {
        const result = await utils_distributionService.distributionService.getDistributionPoster({
          type: this.itemType,
          id: this.itemId,
          title: this.itemTitle,
          price: this.itemPrice
        });
        common_vendor.index.hideLoading();
        if (result && result.posterUrl) {
          common_vendor.index.previewImage({
            urls: [result.posterUrl],
            current: result.posterUrl,
            success: () => {
              common_vendor.index.showToast({
                title: "长按图片保存",
                icon: "none"
              });
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "生成海报失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "生成海报失败",
          icon: "none"
        });
      }
    },
    // 复制分销链接
    async copyLink() {
      common_vendor.index.showLoading({ title: "生成中..." });
      try {
        const result = await utils_distributionService.distributionService.generatePromotionLink({
          type: this.itemType,
          id: this.itemId,
          distributorId: common_vendor.index.getStorageSync("userId") || "0"
        });
        common_vendor.index.hideLoading();
        if (result && result.url) {
          common_vendor.index.setClipboardData({
            data: result.url,
            success: () => {
              common_vendor.index.showToast({
                title: "链接已复制",
                icon: "success"
              });
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "生成链接失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "生成链接失败",
          icon: "none"
        });
      }
    },
    // 生成小程序码
    async generateQrCode() {
      common_vendor.index.showLoading({ title: "生成中..." });
      try {
        const result = await utils_distributionService.distributionService.generateProductCode({
          type: this.itemType,
          id: this.itemId
        });
        common_vendor.index.hideLoading();
        if (result && result.qrCodeUrl) {
          common_vendor.index.previewImage({
            urls: [result.qrCodeUrl],
            current: result.qrCodeUrl,
            success: () => {
              common_vendor.index.showToast({
                title: "长按图片保存",
                icon: "none"
              });
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "生成小程序码失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "生成小程序码失败",
          icon: "none"
        });
      }
    },
    // 显示分销申请弹窗
    showDistributionPopup() {
      common_vendor.index.navigateTo({
        url: "/subPackages/distribution/pages/apply"
      });
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$69,
    b: common_vendor.n($options.itemTypeClass),
    c: common_vendor.t($options.title),
    d: common_vendor.t($options.commissionAmount),
    e: common_vendor.p({
      d: "M9 6L15 12L9 18",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    f: common_vendor.p({
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }),
    g: common_vendor.n($options.itemTypeClass),
    h: common_vendor.o((...args) => $options.handleClick && $options.handleClick(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/distribution-section.js.map
