/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-0c65924e, html.data-v-0c65924e, #app.data-v-0c65924e, .index-container.data-v-0c65924e {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.search-container.data-v-0c65924e {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content-container.data-v-0c65924e {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}
.search-section.data-v-0c65924e {
  padding: 16px;
  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  padding-top: 24px;
}
.search-bar.data-v-0c65924e {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 10px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.search-bar .search-icon.data-v-0c65924e {
  margin-right: 8px;
}
.search-bar .search-input.data-v-0c65924e {
  flex: 1;
  height: 20px;
  font-size: 14px;
  color: #333333;
}
.search-bar .clear-button.data-v-0c65924e {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hot-search-section.data-v-0c65924e, .search-history-section.data-v-0c65924e {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.section-header.data-v-0c65924e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-header .section-title.data-v-0c65924e {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}
.section-header .clear-history.data-v-0c65924e {
  display: flex;
  align-items: center;
}
.section-header .clear-history .delete-icon.data-v-0c65924e {
  margin-right: 4px;
}
.section-header .clear-history text.data-v-0c65924e {
  font-size: 14px;
  color: #999999;
}
.hot-search-tags.data-v-0c65924e, .history-tags.data-v-0c65924e {
  display: flex;
  flex-wrap: wrap;
}
.hot-search-tags .tag-item.data-v-0c65924e, .history-tags .tag-item.data-v-0c65924e {
  background-color: #F5F5F5;
  border-radius: 16px;
  padding: 6px 12px;
  margin-right: 8px;
  margin-bottom: 8px;
}
.hot-search-tags .tag-item text.data-v-0c65924e, .history-tags .tag-item text.data-v-0c65924e {
  font-size: 14px;
  color: #666666;
}
.search-results-section.data-v-0c65924e {
  margin-top: 16px;
}
.filter-bar.data-v-0c65924e {
  display: flex;
  background-color: #FFFFFF;
  padding: 12px 16px;
  border-bottom: 1px solid #EEEEEE;
}
.filter-bar .filter-item.data-v-0c65924e {
  display: flex;
  align-items: center;
  margin-right: 24px;
  position: relative;
}
.filter-bar .filter-item text.data-v-0c65924e {
  font-size: 14px;
  color: #666666;
}
.filter-bar .filter-item .sort-arrows.data-v-0c65924e {
  display: flex;
  flex-direction: column;
  margin-left: 4px;
}
.filter-bar .filter-item .sort-arrows .arrow-up.data-v-0c65924e, .filter-bar .filter-item .sort-arrows .arrow-down.data-v-0c65924e {
  color: #CCCCCC;
}
.filter-bar .filter-item .sort-arrows .arrow-up.active.data-v-0c65924e, .filter-bar .filter-item .sort-arrows .arrow-down.active.data-v-0c65924e {
  color: #9C27B0;
}
.filter-bar .filter-item--active text.data-v-0c65924e {
  color: #9C27B0;
  font-weight: 500;
}
.filter-bar .filter-item--active.data-v-0c65924e::after {
  content: "";
  position: absolute;
  bottom: -12px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #9C27B0;
}
.products-grid.data-v-0c65924e {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 16px;
}
.price-compare-list.data-v-0c65924e {
  background-color: #FFFFFF;
}
.price-compare-list .compare-item.data-v-0c65924e {
  border-bottom: 8px solid #F5F5F5;
}
.price-compare-list .compare-item.data-v-0c65924e:last-child {
  border-bottom: none;
}
.price-compare-list .compare-header.data-v-0c65924e {
  display: flex;
  padding: 16px;
  position: relative;
}
.price-compare-list .compare-header .product-image.data-v-0c65924e {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 12px;
}
.price-compare-list .compare-header .product-info.data-v-0c65924e {
  flex: 1;
}
.price-compare-list .compare-header .product-info .product-title.data-v-0c65924e {
  font-size: 14px;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.price-compare-list .compare-header .product-info .price-range .price-label.data-v-0c65924e {
  font-size: 12px;
  color: #999999;
}
.price-compare-list .compare-header .product-info .price-range .price-value.data-v-0c65924e {
  font-size: 14px;
  color: #FF6B6B;
  font-weight: 500;
}
.price-compare-list .compare-header .expand-icon.data-v-0c65924e {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s;
}
.price-compare-list .compare-header .expand-icon.expanded.data-v-0c65924e {
  transform: translateY(-50%) rotate(180deg);
}
.price-compare-list .platform-list.data-v-0c65924e {
  background-color: #F9F9F9;
  padding: 0 16px;
}
.price-compare-list .platform-list .platform-item.data-v-0c65924e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #EEEEEE;
}
.price-compare-list .platform-list .platform-item.data-v-0c65924e:last-child {
  border-bottom: none;
}
.price-compare-list .platform-list .platform-item .platform-info.data-v-0c65924e {
  display: flex;
  align-items: center;
}
.price-compare-list .platform-list .platform-item .platform-info .platform-icon.data-v-0c65924e {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.price-compare-list .platform-list .platform-item .platform-info .platform-name.data-v-0c65924e {
  font-size: 14px;
  color: #666666;
}
.price-compare-list .platform-list .platform-item .platform-price.data-v-0c65924e {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.price-compare-list .platform-list .platform-item .platform-price .price-value.data-v-0c65924e {
  font-size: 14px;
  color: #FF6B6B;
  font-weight: 500;
  margin-bottom: 2px;
}
.price-compare-list .platform-list .platform-item .platform-price .cashback-value.data-v-0c65924e {
  font-size: 12px;
  color: #9C27B0;
}
.loading-more.data-v-0c65924e, .no-more.data-v-0c65924e {
  text-align: center;
  padding: 16px 0;
}
.loading-more text.data-v-0c65924e, .no-more text.data-v-0c65924e {
  font-size: 14px;
  color: #999999;
}
.empty-result.data-v-0c65924e {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}
.empty-result .empty-image.data-v-0c65924e {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}
.empty-result text.data-v-0c65924e {
  font-size: 16px;
  color: #666666;
  margin-bottom: 8px;
}
.empty-result .empty-tips.data-v-0c65924e {
  font-size: 14px;
  color: #999999;
}