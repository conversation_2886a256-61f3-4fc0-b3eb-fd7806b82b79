
.favorites-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: #0052CC;
  color: #fff;
  z-index: 100;
}
.navbar-left {
  width: 80rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
  text-align: right;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 选项卡样式 */
.tabs-container {
  position: fixed;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  z-index: 99;
}
.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.tab-text {
  font-size: 14px;
  color: #333;
  padding: 0 15px;
}
.tab-item.active .tab-text {
  color: #0052CC;
  font-weight: bold;
}
.tab-line {
  position: absolute;
  bottom: 0;
  height: 3px;
  background-color: #0052CC;
  border-radius: 2px;
  transition: transform 0.3s;
}

/* 内容区域 */
.content-area {
  position: relative;
  height: 100vh;
}
.content-swiper {
  height: 100%;
}
.tab-scroll {
  height: 100%;
}

/* 收藏列表 */
.collect-list {
  padding: 15px;
}
.collect-item {
  display: flex;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.collect-left {
  width: 120rpx;
  height: 120rpx;
  margin-right: 15px;
}
.collect-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
.collect-right {
  flex: 1;
}
.collect-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.collect-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.collect-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

/* 关注列表 */
.follow-list {
  padding: 15px;
}
.follow-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.follow-left {
  margin-right: 15px;
}
.follow-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}
.follow-middle {
  flex: 1;
}
.follow-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}
.follow-info {
  font-size: 13px;
  color: #999;
}
.follow-right {
  margin-left: 15px;
}
.follow-btn {
  min-width: 150rpx;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 15px;
  font-size: 13px;
  border-radius: 30rpx;
  background-color: #0052CC;
  color: #fff;
}
.follow-btn[disabled] {
  background-color: #eee;
  color: #999;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  color: #999;
}
