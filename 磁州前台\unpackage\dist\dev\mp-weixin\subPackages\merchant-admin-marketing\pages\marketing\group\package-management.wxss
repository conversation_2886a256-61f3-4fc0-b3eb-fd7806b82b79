/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.package-management-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}
.page-content {
  height: calc(100vh - 59px - env(safe-area-inset-bottom));
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
  font-size: 12px;
  color: #666;
}
.date-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #666;
  margin-left: 5px;
}
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}
.stats-card {
  flex: 1;
  min-width: calc(50% - 10px);
  margin: 5px;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  position: relative;
}
.card-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.card-label {
  font-size: 12px;
  color: #999;
}
.card-trend {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  font-size: 12px;
}
.card-trend.up {
  color: #34C759;
}
.card-trend.down {
  color: #FF3B30;
}
.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 2px;
}
.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}
.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 套餐列表样式 */
.packages-section {
  margin: 15px;
  margin-top: 0;
}
.filter-dropdown {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
  font-size: 12px;
  color: #666;
}
.filter-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #666;
  margin-left: 5px;
}
.packages-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.package-item {
  background: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.package-header {
  padding: 15px;
  border-bottom: 1px solid #F5F7FA;
}
.package-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}
.package-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.package-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.package-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.package-status.inactive {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}
.package-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}
.package-content {
  padding: 15px;
  display: flex;
  border-bottom: 1px solid #F5F7FA;
}
.package-items {
  flex: 1;
  padding-right: 15px;
}
.package-items-text {
  font-size: 13px;
  color: #666;
  line-height: 1.6;
}
.package-price-info {
  width: 100px;
  flex-shrink: 0;
  border-left: 1px dashed #E5E7EB;
  padding-left: 15px;
}
.price-row {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}
.price-row:last-child {
  margin-bottom: 0;
}
.price-label {
  font-size: 12px;
  color: #999;
  width: 45px;
}
.price-original {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}
.price-group {
  font-size: 12px;
  color: #FF3B30;
  font-weight: 600;
}
.price-save {
  font-size: 12px;
  color: #FF9500;
}
.package-footer {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.package-stats {
  display: flex;
  gap: 15px;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
.stat-label {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}
.package-actions {
  display: flex;
  gap: 10px;
}
.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.action-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.edit-icon {
  background: rgba(0, 122, 255, 0.1);
  position: relative;
}
.edit-icon:before {
  content: "";
  width: 12px;
  height: 12px;
  border: 1px solid #007AFF;
  border-radius: 2px;
  transform: rotate(45deg);
}
.share-icon {
  background: rgba(52, 199, 89, 0.1);
  position: relative;
}
.share-icon:before {
  content: "";
  width: 12px;
  height: 6px;
  border-left: 1px solid #34C759;
  border-right: 1px solid #34C759;
  border-bottom: 1px solid #34C759;
}
.more-icon {
  background: rgba(142, 142, 147, 0.1);
  position: relative;
}
.more-icon:before {
  content: "";
  width: 12px;
  height: 2px;
  background: #8E8E93;
  position: absolute;
  top: 11px;
  left: 6px;
}
.more-icon:after {
  content: "";
  width: 2px;
  height: 12px;
  background: #8E8E93;
  position: absolute;
  top: 6px;
  left: 11px;
}
.action-text {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 15px;
}
.empty-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.empty-subtext {
  font-size: 14px;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  right: 20px;
  bottom: 30px;
  width: auto;
  height: 50px;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(126, 48, 225, 0.3);
  z-index: 10;
  padding: 0 20px;
}
.fab-content {
  display: flex;
  align-items: center;
}
.fab-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #FFFFFF;
  margin-right: 8px;
}
.fab-text {
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
}

/* 创建套餐分步骤向导样式 */
.step-wizard {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
}
.wizard-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.wizard-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 400px;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
}
.wizard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.wizard-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.close-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #999;
}
.wizard-body {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.wizard-step {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #F5F7FA;
  border-radius: 8px;
}
.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
  margin-right: 10px;
}
.step-info {
  flex: 1;
}
.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.step-desc {
  font-size: 12px;
  color: #999;
}
.step-arrow {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #999;
  margin-left: 10px;
}