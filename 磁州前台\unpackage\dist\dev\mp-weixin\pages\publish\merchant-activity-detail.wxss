/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx;
  /* 为底部操作栏留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-left {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  flex: 1;
  text-align: center;
}
.navbar-right {
  position: absolute;
  right: 30rpx;
}

/* 详情包装器 */
.detail-wrapper {
  padding: 24rpx;
  padding-top: 144px;
  /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 内容卡片通用样式 */
.content-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 章节标题 */
.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 6rpx;
  height: 32rpx;
  width: 8rpx;
  background: linear-gradient(to bottom, #3846cd, #5868e0);
  border-radius: 4rpx;
}

/* 活动封面 */
.cover-card {
  padding: 0;
  overflow: hidden;
}
.cover-swiper {
  height: 400rpx;
  width: 100%;
}
.cover-image {
  width: 100%;
  height: 100%;
}

/* 活动信息卡片 */
.activity-header {
  margin-bottom: 20rpx;
}
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.main-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}
.activity-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
}
.status-upcoming {
  background-color: #e6f7ff;
  color: #1890ff;
}
.status-ongoing {
  background-color: #f6ffed;
  color: #52c41a;
}
.status-ended {
  background-color: #f5f5f5;
  color: #999;
}
.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.tag-group {
  display: flex;
  flex-wrap: wrap;
}
.info-tag {
  font-size: 22rpx;
  color: #666;
  background-color: #f5f7fa;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}
.publish-time {
  font-size: 22rpx;
  color: #999;
}

/* 基本信息列表 */
.basic-info {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}
.info-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}
.info-label {
  color: #666;
  font-size: 28rpx;
}
.info-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

/* 描述和规则 */
.description-content, .rules-content {
  color: #333;
  font-size: 28rpx;
  line-height: 1.6;
}

/* 位置卡片 */
.location-content {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}
.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin: 0 10rpx;
}
.location-arrow {
  color: #999;
  font-size: 24rpx;
}
.location-map {
  margin-top: 20rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}
.map-preview {
  width: 100%;
  height: 100%;
}

/* 联系方式卡片 */
.contact-content {
  padding: 10rpx 0;
}
.contact-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}
.contact-label {
  color: #666;
  font-size: 28rpx;
}
.contact-value {
  color: #333;
  font-size: 28rpx;
}
.contact-phone {
  color: #3846cd;
}
.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}
.tips-icon {
  color: #3846cd;
  font-size: 28rpx;
  margin-right: 10rpx;
}
.tips-text {
  color: #999;
  font-size: 24rpx;
}

/* 红包卡片 */
.red-packet-section {
  padding: 10rpx 0;
}
.red-packet-container {
  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);
  border-radius: 12rpx;
  padding: 4rpx;
  position: relative;
  overflow: hidden;
}
.red-packet-blur-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("../../../static/images/tabbar/红包背景.png") no-repeat center/cover;
  opacity: 0.1;
}
.red-packet-content {
  background: #FFF;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}
.red-packet-left {
  display: flex;
  align-items: center;
}
.red-packet-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
}
.red-packet-info {
  display: flex;
  flex-direction: column;
}
.red-packet-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4D4F;
  margin-bottom: 6rpx;
}
.red-packet-desc {
  font-size: 22rpx;
  color: #999;
}
.red-packet-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.red-packet-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF4D4F;
  margin-bottom: 10rpx;
}
.prefix {
  font-size: 22rpx;
  font-weight: normal;
}
.grab-btn {
  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);
  color: #FFF;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

/* 相关活动推荐 */
.related-activities-list {
  padding: 10rpx 0;
}
.related-activity-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}
.related-activity-item:last-child {
  border-bottom: none;
}
.related-activity-left {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}
.related-activity-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.related-activity-info {
  flex: 1;
}
.related-activity-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.related-activity-meta {
  display: flex;
  margin-bottom: 6rpx;
}
.related-meta-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}
.related-activity-date {
  font-size: 22rpx;
  color: #999;
}

/* 底部操作栏 */
.footer-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #FFF;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 90;
  padding-bottom: env(safe-area-inset-bottom);
}
.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.action-icon {
  font-size: 40rpx;
  color: #999;
  margin-bottom: 6rpx;
}
.icon-collected {
  color: #3846cd;
}
.action-text {
  font-size: 24rpx;
  color: #666;
}
.contact-action {
  flex: 2;
  background: linear-gradient(135deg, #3846cd, #5868e0);
  height: 70rpx;
  border-radius: 35rpx;
  margin: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.contact-action .action-text {
  color: #FFF;
  font-size: 28rpx;
  font-weight: 500;
}

/* 海报弹窗 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.poster-container {
  width: 80%;
  background: #FFF;
  border-radius: 16rpx;
  overflow: hidden;
}
.poster-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}
.poster-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.close-icon {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}
.poster-image-container {
  padding: 30rpx;
  display: flex;
  justify-content: center;
}
.poster-preview {
  width: 100%;
  border-radius: 8rpx;
}
.poster-footer {
  display: flex;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}
.poster-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
}
.save-btn {
  background: #f5f7fa;
  color: #666;
}
.share-btn {
  background: linear-gradient(135deg, #3846cd, #5868e0);
  color: #FFF;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 140rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 80;
}
.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}
.poster-text {
  font-size: 20rpx;
  color: #666;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* 海报Canvas */
.poster-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
}