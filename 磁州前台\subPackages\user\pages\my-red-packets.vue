<template>
  <view class="my-red-packets">
    <!-- 顶部标签页 -->
    <view class="tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>
    
    <!-- 红包列表 -->
    <scroll-view 
      class="red-packet-list"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      @refresherrefresh="onRefresh"
    >
      <view 
        v-for="(item, index) in redPacketList" 
        :key="index"
        class="red-packet-item"
        @click="viewRedPacketDetail(item)"
      >
        <view class="red-packet-info">
          <view class="title">{{ item.title }}</view>
          <view class="time">{{ formatTime(item.createTime) }}</view>
          <view class="amount" v-if="item.type === 'received'">
            获得 {{ formatAmount(item.amount) }}元
          </view>
          <view class="amount" v-else>
            发出 {{ formatAmount(item.totalAmount) }}元
          </view>
        </view>
        <view class="status" :class="item.status">
          {{ getStatusText(item.status) }}
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading" v-if="loading">加载中...</view>
      <view class="no-more" v-if="!hasMore && redPacketList.length > 0">没有更多了</view>
      <view class="empty" v-if="!loading && redPacketList.length === 0">
        <image src="/static/images/empty-red-packet.png" mode="aspectFit"></image>
        <text>暂无红包记录</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getMyRedPackets } from '@/utils/redPacket.js';
import { formatTime, formatAmount } from '@/utils/format.js';

// Vue 3 Composition API 代码开始
// 标签页配置
const tabs = ref([
  { name: '收到的红包', type: 'received' },
  { name: '发出的红包', type: 'sent' }
]);

// 当前选中的标签页
const currentTab = ref(0);

// 红包列表数据
const redPacketList = ref([]);

// 分页和加载状态
const page = ref(1);
const pageSize = ref(20);
const loading = ref(false);
const hasMore = ref(true);

// 缓存机制
const cache = reactive(new Map());

// 生命周期钩子
onMounted(() => {
  loadData();
});

// 切换标签页
const switchTab = (index) => {
  if (currentTab.value === index) return;
  currentTab.value = index;
  page.value = 1;
  redPacketList.value = [];
  hasMore.value = true;
  loadData();
};

// 加载数据
const loadData = async () => {
  if (loading.value || !hasMore.value) return;
  
  loading.value = true;
  const type = tabs.value[currentTab.value].type;
  const cacheKey = `${type}_${page.value}`;
  
  try {
    // 检查缓存
    if (cache.has(cacheKey)) {
      const cachedData = cache.get(cacheKey);
      redPacketList.value = [...redPacketList.value, ...cachedData.list];
      hasMore.value = cachedData.hasMore;
    } else {
      const res = await getMyRedPackets({
        type,
        page: page.value,
        pageSize: pageSize.value
      });
      
      redPacketList.value = [...redPacketList.value, ...res.list];
      hasMore.value = res.hasMore;
      
      // 设置缓存
      cache.set(cacheKey, {
        list: res.list,
        hasMore: res.hasMore
      });
    }
    
    page.value++;
  } catch (err) {
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 下拉刷新
const onRefresh = async () => {
  page.value = 1;
  redPacketList.value = [];
  hasMore.value = true;
  cache.clear(); // 清除缓存
  await loadData();
  uni.stopPullDownRefresh();
};

// 加载更多
const loadMore = () => {
  loadData();
};

// 查看红包详情
const viewRedPacketDetail = (item) => {
  uni.navigateTo({
    url: `/pages/red-packet/detail?id=${item.id}`
  });
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待领取',
    received: '已领取',
    expired: '已过期',
    completed: '已领完'
  };
  return statusMap[status] || status;
};
// Vue 3 Composition API 代码结束
</script>

<style lang="scss">
.my-red-packets {
  min-height: 100vh;
  background-color: #f8f8f8;
  
  .tabs {
    display: flex;
    background-color: #fff;
    padding: 20rpx 0;
    position: sticky;
    top: 0;
    z-index: 1;
    
    .tab-item {
      flex: 1;
      text-align: center;
      font-size: 28rpx;
      color: #666;
      position: relative;
      padding: 20rpx 0;
      
      &.active {
        color: #FF6347;
        font-weight: 500;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background-color: #FF6347;
          border-radius: 2rpx;
        }
      }
    }
  }
  
  .red-packet-list {
    height: calc(100vh - 100rpx);
    
    .red-packet-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      background-color: #fff;
      margin-bottom: 2rpx;
      
      .red-packet-info {
        flex: 1;
        
        .title {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 10rpx;
        }
        
        .time {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 10rpx;
        }
        
        .amount {
          font-size: 32rpx;
          color: #FF6347;
          font-weight: 500;
        }
      }
      
      .status {
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
        
        &.pending {
          color: #FF6347;
          background-color: #FFF0F5;
        }
        
        &.received {
          color: #52c41a;
          background-color: #f6ffed;
        }
        
        &.expired {
          color: #999;
          background-color: #f5f5f5;
        }
        
        &.completed {
          color: #1890ff;
          background-color: #e6f7ff;
        }
      }
    }
    
    .loading, .no-more {
      text-align: center;
      padding: 30rpx;
      color: #999;
      font-size: 24rpx;
    }
    
    .empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 100rpx 0;
      
      image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
      }
      
      text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}
</style> 
