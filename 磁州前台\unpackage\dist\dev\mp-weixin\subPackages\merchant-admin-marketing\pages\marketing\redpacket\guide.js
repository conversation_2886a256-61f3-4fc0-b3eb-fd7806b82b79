"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      openFaq: -1
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    shareGuide() {
      common_vendor.index.showActionSheet({
        itemList: ["分享给好友", "分享到朋友圈", "复制链接"],
        success: function(res) {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    },
    toggleFaq(index) {
      this.openFaq = this.openFaq === index ? -1 : index;
    },
    contactService() {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    }
  }
};
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_circle + _component_line + _component_svg + _component_path + _component_polyline)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.p({
      cx: "18",
      cy: "5",
      r: "3"
    }),
    c: common_vendor.p({
      cx: "6",
      cy: "12",
      r: "3"
    }),
    d: common_vendor.p({
      cx: "18",
      cy: "19",
      r: "3"
    }),
    e: common_vendor.p({
      x1: "8.59",
      y1: "13.51",
      x2: "15.42",
      y2: "17.49"
    }),
    f: common_vendor.p({
      x1: "15.41",
      y1: "6.51",
      x2: "8.59",
      y2: "10.49"
    }),
    g: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    h: common_vendor.o((...args) => $options.shareGuide && $options.shareGuide(...args)),
    i: common_assets._imports_0$37,
    j: common_vendor.p({
      d: "M12 2L2 7l10 5 10-5-10-5z"
    }),
    k: common_vendor.p({
      d: "M2 17l10 5 10-5"
    }),
    l: common_vendor.p({
      d: "M2 12l10 5 10-5"
    }),
    m: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    n: common_assets._imports_1$47,
    o: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    p: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12",
      y2: "16"
    }),
    q: common_vendor.p({
      x1: "8",
      y1: "12",
      x2: "16",
      y2: "12"
    }),
    r: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    s: common_vendor.p({
      points: "20 6 9 17 4 12"
    }),
    t: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    v: common_vendor.p({
      points: "20 6 9 17 4 12"
    }),
    w: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    x: common_vendor.p({
      points: "20 6 9 17 4 12"
    }),
    y: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    z: common_vendor.p({
      points: "20 6 9 17 4 12"
    }),
    A: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    B: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
    }),
    C: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4"
    }),
    D: common_vendor.p({
      d: "M23 21v-2a4 4 0 0 0-3-3.87"
    }),
    E: common_vendor.p({
      d: "M16 3.13a4 4 0 0 1 0 7.75"
    }),
    F: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    G: common_vendor.p({
      d: "M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
    }),
    H: common_vendor.p({
      points: "14 2 14 8 20 8"
    }),
    I: common_vendor.p({
      x1: "16",
      y1: "13",
      x2: "8",
      y2: "13"
    }),
    J: common_vendor.p({
      x1: "16",
      y1: "17",
      x2: "8",
      y2: "17"
    }),
    K: common_vendor.p({
      points: "10 9 9 9 8 9"
    }),
    L: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    M: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    N: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12",
      y2: "12"
    }),
    O: common_vendor.p({
      x1: "12",
      y1: "16",
      x2: "12.01",
      y2: "16"
    }),
    P: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    Q: $data.openFaq === 0 ? 1 : "",
    R: $data.openFaq === 0
  }, $data.openFaq === 0 ? {} : {}, {
    S: common_vendor.o(($event) => $options.toggleFaq(0)),
    T: $data.openFaq === 1 ? 1 : "",
    U: $data.openFaq === 1
  }, $data.openFaq === 1 ? {} : {}, {
    V: common_vendor.o(($event) => $options.toggleFaq(1)),
    W: $data.openFaq === 2 ? 1 : "",
    X: $data.openFaq === 2
  }, $data.openFaq === 2 ? {} : {}, {
    Y: common_vendor.o(($event) => $options.toggleFaq(2)),
    Z: common_vendor.o((...args) => $options.contactService && $options.contactService(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-91859119"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide.js.map
