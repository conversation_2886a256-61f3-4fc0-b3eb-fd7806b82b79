
.promotion-center-container.data-v-648aa685 {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar.data-v-648aa685 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}
.navbar-bg.data-v-648aa685 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
}
.navbar-content.data-v-648aa685 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: var(--status-bar-height) 30rpx 0;
}
.back-btn.data-v-648aa685, .message-btn.data-v-648aa685 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.message-btn.data-v-648aa685 {
  position: relative;
}
.badge.data-v-648aa685 {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}
.navbar-title.data-v-648aa685 {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.navbar-right.data-v-648aa685 {
  display: flex;
  align-items: center;
}

/* 轮播图样式 */
.banner-swiper.data-v-648aa685 {
  height: 300rpx;
  width: 100%;
}
.banner-image.data-v-648aa685 {
  width: 100%;
  height: 100%;
}

/* 促销类型导航样式 */
.promotion-nav.data-v-648aa685 {
  display: flex;
  flex-wrap: wrap;
  background: #FFFFFF;
  padding: 30rpx 20rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.nav-item.data-v-648aa685 {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}
.nav-icon.data-v-648aa685 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}
.nav-text.data-v-648aa685 {
  font-size: 24rpx;
  color: #333333;
}

/* 通用区域样式 */
.section.data-v-648aa685 {
  background: #FFFFFF;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.section-header.data-v-648aa685 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.header-left.data-v-648aa685 {
  display: flex;
  align-items: center;
}
.section-title.data-v-648aa685 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 15rpx;
}
.section-tag.data-v-648aa685 {
  font-size: 22rpx;
  color: #FFFFFF;
  background: #FF3B69;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.header-right.data-v-648aa685 {
  display: flex;
  align-items: center;
}
.view-all.data-v-648aa685 {
  font-size: 26rpx;
  color: #999999;
  margin-right: 6rpx;
}

/* 限时特惠区域样式 */
.flash-sale-section .section-title.data-v-648aa685 {
  color: #FF3B30;
}
.countdown.data-v-648aa685 {
  display: flex;
  align-items: center;
  background: rgba(255, 59, 48, 0.1);
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
}
.countdown-label.data-v-648aa685 {
  font-size: 22rpx;
  color: #FF3B30;
  margin-right: 6rpx;
}
.time-block.data-v-648aa685 {
  width: 40rpx;
  height: 40rpx;
  background: #FF3B30;
  color: #FFFFFF;
  font-size: 22rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
}
.time-colon.data-v-648aa685 {
  font-size: 22rpx;
  color: #FF3B30;
  margin: 0 4rpx;
}
.flash-sale-scroll.data-v-648aa685 {
  white-space: nowrap;
}
.flash-sale-list.data-v-648aa685 {
  display: flex;
  padding: 10rpx 0;
}
.flash-sale-item.data-v-648aa685 {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
  border-radius: 15rpx;
  overflow: hidden;
  background: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  position: relative;
}
.item-image.data-v-648aa685 {
  width: 100%;
  height: 280rpx;
  object-fit: cover;
}
.discount-tag.data-v-648aa685 {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: rgba(255, 59, 48, 0.8);
  color: #FFFFFF;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.item-info.data-v-648aa685 {
  padding: 15rpx;
}
.item-title.data-v-648aa685 {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
  height: 72rpx;
}
.item-price.data-v-648aa685 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.price-current.data-v-648aa685 {
  font-size: 28rpx;
  font-weight: bold;
  color: #FF3B30;
  margin-right: 10rpx;
}
.price-original.data-v-648aa685 {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}
.progress-bar.data-v-648aa685 {
  height: 8rpx;
  background: #F0F0F0;
  border-radius: 4rpx;
  margin-bottom: 10rpx;
  overflow: hidden;
}
.progress-fill.data-v-648aa685 {
  height: 100%;
  background: #FF3B30;
  border-radius: 4rpx;
}
.item-sales.data-v-648aa685 {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999999;
}
.sales-status.data-v-648aa685 {
  color: #FF3B30;
}

/* 拼团优惠区域样式 */
.group-buy-section .section-title.data-v-648aa685 {
  color: #FF9500;
}
.group-buy-section .section-tag.data-v-648aa685 {
  background: #FF9500;
}
.group-buy-grid.data-v-648aa685 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.group-buy-item.data-v-648aa685 {
  width: 48%;
  border-radius: 15rpx;
  overflow: hidden;
  background: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}
.group-buy-item .item-image.data-v-648aa685 {
  width: 100%;
  height: 200rpx;
}
.group-buy-item .item-info.data-v-648aa685 {
  padding: 15rpx;
}
.group-info.data-v-648aa685 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.group-price.data-v-648aa685 {
  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
}
.price-label.data-v-648aa685 {
  font-size: 22rpx;
  color: #666666;
  margin-right: 6rpx;
}
.group-btn.data-v-648aa685 {
  font-size: 24rpx;
  color: #FFFFFF;
  background: #FF9500;
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
}
.group-buy-list.data-v-648aa685 {
  margin-top: 20rpx;
}
.list-item.data-v-648aa685 {
  display: flex;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}
.list-item.data-v-648aa685:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.list-item-image.data-v-648aa685 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}
.list-item-info.data-v-648aa685 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.list-item-title.data-v-648aa685 {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.list-item-price.data-v-648aa685 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 满减优惠区域样式 */
.discount-section .section-title.data-v-648aa685 {
  color: #5AC8FA;
}
.discount-section .section-tag.data-v-648aa685 {
  background: #5AC8FA;
}
.discount-grid.data-v-648aa685 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.discount-shop.data-v-648aa685 {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background: #F9F9F9;
  border-radius: 15rpx;
}
.shop-logo.data-v-648aa685 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}
.shop-info.data-v-648aa685 {
  flex: 1;
}
.shop-name.data-v-648aa685 {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.discount-tags.data-v-648aa685 {
  display: flex;
  flex-wrap: wrap;
}
.discount-tags .discount-tag.data-v-648aa685 {
  font-size: 20rpx;
  color: #5AC8FA;
  background: rgba(90, 200, 250, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
}

/* 优惠券专区样式 */
.coupon-section .section-title.data-v-648aa685 {
  color: #FF9500;
}
.coupon-section .section-tag.data-v-648aa685 {
  background: #FF9500;
}
.coupon-scroll.data-v-648aa685 {
  white-space: nowrap;
}
.coupon-list.data-v-648aa685 {
  display: flex;
  padding: 10rpx 0;
}
.coupon-item.data-v-648aa685 {
  display: inline-flex;
  width: 500rpx;
  height: 180rpx;
  margin-right: 20rpx;
  border-radius: 15rpx;
  overflow: hidden;
  position: relative;
}
.coupon-item.data-v-648aa685::before {
  content: '';
  position: absolute;
  left: 160rpx;
  top: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #F5F5F5;
}
.coupon-item.data-v-648aa685::after {
  content: '';
  position: absolute;
  left: 160rpx;
  bottom: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #F5F5F5;
}
.coupon-left.data-v-648aa685 {
  width: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}
.coupon-value.data-v-648aa685 {
  display: flex;
  align-items: baseline;
}
.currency.data-v-648aa685 {
  font-size: 24rpx;
  color: #FFFFFF;
}
.amount.data-v-648aa685 {
  font-size: 48rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.unit.data-v-648aa685 {
  font-size: 24rpx;
  color: #FFFFFF;
}
.coupon-condition.data-v-648aa685 {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 10rpx;
}
.coupon-divider.data-v-648aa685 {
  width: 1rpx;
  height: 140rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 20rpx 0;
  position: relative;
  z-index: 1;
}
.coupon-right.data-v-648aa685 {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.coupon-name.data-v-648aa685 {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 6rpx;
}
.coupon-shop.data-v-648aa685 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 6rpx;
}
.coupon-validity.data-v-648aa685 {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}
.coupon-btn.data-v-648aa685 {
  align-self: flex-end;
  font-size: 24rpx;
  color: #FFFFFF;
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 15rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

/* 推荐活动区域样式 */
.activity-section .section-title.data-v-648aa685 {
  color: #FF3B69;
}
.activity-list.data-v-648aa685 {
  display: flex;
  flex-direction: column;
}
.activity-item.data-v-648aa685 {
  display: flex;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}
.activity-item.data-v-648aa685:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.activity-image.data-v-648aa685 {
  width: 200rpx;
  height: 150rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}
.activity-info.data-v-648aa685 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.activity-title.data-v-648aa685 {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.activity-meta.data-v-648aa685 {
  display: flex;
  margin-bottom: 10rpx;
}
.meta-item.data-v-648aa685 {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}
.meta-icon.data-v-648aa685 {
  margin-right: 6rpx;
}
.meta-text.data-v-648aa685 {
  font-size: 24rpx;
  color: #999999;
}
.activity-bottom.data-v-648aa685 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.price-tag.data-v-648aa685 {
  display: flex;
  align-items: baseline;
}
.price-symbol.data-v-648aa685 {
  font-size: 22rpx;
  color: #FF3B69;
  margin-right: 2rpx;
}
.price-value.data-v-648aa685 {
  font-size: 28rpx;
  font-weight: bold;
  color: #FF3B69;
}
.price-free.data-v-648aa685 {
  font-size: 24rpx;
  color: #34C759;
  font-weight: 500;
}
.participants.data-v-648aa685 {
  display: flex;
  align-items: center;
}
.avatar-group.data-v-648aa685 {
  display: flex;
  margin-right: 10rpx;
}
.participant-avatar.data-v-648aa685 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
  margin-left: -10rpx;
}
.participant-avatar.data-v-648aa685:first-child {
  margin-left: 0;
}
.participant-count.data-v-648aa685 {
  font-size: 22rpx;
  color: #999999;
}
