/**
 * 磁州前台动画工具
 * 提供各种动画效果、过渡动画、交互动画等功能
 */

// 动画配置
const ANIMATION_CONFIG = {
  // 动画持续时间
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
    slower: 800
  },
  
  // 缓动函数
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    bounceIn: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    bounceOut: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    backIn: 'cubic-bezier(0.6, -0.28, 0.735, 0.045)',
    backOut: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
  },
  
  // 预设动画
  presets: {
    fadeIn: {
      from: { opacity: 0 },
      to: { opacity: 1 },
      duration: 300,
      easing: 'ease-out'
    },
    fadeOut: {
      from: { opacity: 1 },
      to: { opacity: 0 },
      duration: 300,
      easing: 'ease-in'
    },
    slideInUp: {
      from: { opacity: 0, transform: 'translateY(20px)' },
      to: { opacity: 1, transform: 'translateY(0)' },
      duration: 400,
      easing: 'ease-out'
    },
    slideInDown: {
      from: { opacity: 0, transform: 'translateY(-20px)' },
      to: { opacity: 1, transform: 'translateY(0)' },
      duration: 400,
      easing: 'ease-out'
    },
    slideInLeft: {
      from: { opacity: 0, transform: 'translateX(-20px)' },
      to: { opacity: 1, transform: 'translateX(0)' },
      duration: 400,
      easing: 'ease-out'
    },
    slideInRight: {
      from: { opacity: 0, transform: 'translateX(20px)' },
      to: { opacity: 1, transform: 'translateX(0)' },
      duration: 400,
      easing: 'ease-out'
    },
    scaleIn: {
      from: { opacity: 0, transform: 'scale(0.9)' },
      to: { opacity: 1, transform: 'scale(1)' },
      duration: 300,
      easing: 'ease-out'
    },
    scaleOut: {
      from: { opacity: 1, transform: 'scale(1)' },
      to: { opacity: 0, transform: 'scale(0.9)' },
      duration: 300,
      easing: 'ease-in'
    },
    bounceIn: {
      from: { opacity: 0, transform: 'scale(0.3)' },
      to: { opacity: 1, transform: 'scale(1)' },
      duration: 600,
      easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    },
    shake: {
      keyframes: [
        { transform: 'translateX(0)' },
        { transform: 'translateX(-2px)' },
        { transform: 'translateX(2px)' },
        { transform: 'translateX(-2px)' },
        { transform: 'translateX(2px)' },
        { transform: 'translateX(0)' }
      ],
      duration: 500,
      easing: 'ease-in-out'
    },
    pulse: {
      keyframes: [
        { transform: 'scale(1)' },
        { transform: 'scale(1.05)' },
        { transform: 'scale(1)' }
      ],
      duration: 1000,
      easing: 'ease-in-out',
      iterations: 'infinite'
    },
    rotate: {
      from: { transform: 'rotate(0deg)' },
      to: { transform: 'rotate(360deg)' },
      duration: 1000,
      easing: 'linear',
      iterations: 'infinite'
    }
  }
}

// 动画管理类
class AnimationManager {
  constructor() {
    this.runningAnimations = new Map()
    this.animationId = 0
  }
  
  // 创建动画
  createAnimation(options = {}) {
    const animation = uni.createAnimation({
      duration: options.duration || ANIMATION_CONFIG.duration.normal,
      timingFunction: options.easing || ANIMATION_CONFIG.easing.ease,
      delay: options.delay || 0,
      transformOrigin: options.transformOrigin || '50% 50% 0'
    })
    
    return animation
  }
  
  // 执行预设动画
  runPresetAnimation(element, presetName, options = {}) {
    const preset = ANIMATION_CONFIG.presets[presetName]
    if (!preset) {
      console.warn(`动画预设 ${presetName} 不存在`)
      return Promise.reject(new Error(`动画预设 ${presetName} 不存在`))
    }
    
    return this.runAnimation(element, {
      ...preset,
      ...options
    })
  }
  
  // 执行自定义动画
  runAnimation(element, config) {
    return new Promise((resolve, reject) => {
      try {
        const animationId = ++this.animationId
        const animation = this.createAnimation({
          duration: config.duration || ANIMATION_CONFIG.duration.normal,
          easing: config.easing || ANIMATION_CONFIG.easing.ease,
          delay: config.delay || 0
        })
        
        // 应用动画属性
        if (config.from) {
          this.applyAnimationProperties(animation, config.from)
        }
        
        if (config.to) {
          this.applyAnimationProperties(animation, config.to)
        }
        
        if (config.keyframes) {
          config.keyframes.forEach((keyframe, index) => {
            if (index === 0) return // 跳过第一帧
            this.applyAnimationProperties(animation, keyframe)
          })
        }
        
        // 记录运行中的动画
        this.runningAnimations.set(animationId, {
          element,
          config,
          startTime: Date.now()
        })
        
        // 执行动画
        const animationData = animation.export()
        if (element && element.setData) {
          element.setData({
            animationData
          })
        }
        
        // 动画完成后清理
        setTimeout(() => {
          this.runningAnimations.delete(animationId)
          resolve(animationId)
        }, (config.duration || ANIMATION_CONFIG.duration.normal) + (config.delay || 0))
        
      } catch (error) {
        reject(error)
      }
    })
  }
  
  // 应用动画属性
  applyAnimationProperties(animation, properties) {
    Object.keys(properties).forEach(key => {
      const value = properties[key]
      
      switch (key) {
        case 'opacity':
          animation.opacity(value)
          break
        case 'backgroundColor':
          animation.backgroundColor(value)
          break
        case 'width':
          animation.width(value)
          break
        case 'height':
          animation.height(value)
          break
        case 'top':
          animation.top(value)
          break
        case 'left':
          animation.left(value)
          break
        case 'right':
          animation.right(value)
          break
        case 'bottom':
          animation.bottom(value)
          break
        case 'transform':
          this.parseTransform(animation, value)
          break
        default:
          console.warn(`不支持的动画属性: ${key}`)
      }
    })
    
    return animation
  }
  
  // 解析transform属性
  parseTransform(animation, transformValue) {
    // 解析 transform 字符串，如 "translateX(10px) scale(1.2) rotate(45deg)"
    const transforms = transformValue.match(/(\w+)\([^)]+\)/g) || []
    
    transforms.forEach(transform => {
      const [, func, value] = transform.match(/(\w+)\(([^)]+)\)/) || []
      
      switch (func) {
        case 'translateX':
          animation.translateX(parseFloat(value))
          break
        case 'translateY':
          animation.translateY(parseFloat(value))
          break
        case 'translateZ':
          animation.translateZ(parseFloat(value))
          break
        case 'translate3d':
          const [x, y, z] = value.split(',').map(v => parseFloat(v.trim()))
          animation.translate3d(x, y, z)
          break
        case 'scale':
          animation.scale(parseFloat(value))
          break
        case 'scaleX':
          animation.scaleX(parseFloat(value))
          break
        case 'scaleY':
          animation.scaleY(parseFloat(value))
          break
        case 'rotate':
          animation.rotate(parseFloat(value))
          break
        case 'rotateX':
          animation.rotateX(parseFloat(value))
          break
        case 'rotateY':
          animation.rotateY(parseFloat(value))
          break
        case 'rotateZ':
          animation.rotateZ(parseFloat(value))
          break
        case 'skewX':
          animation.skewX(parseFloat(value))
          break
        case 'skewY':
          animation.skewY(parseFloat(value))
          break
      }
    })
  }
  
  // 停止动画
  stopAnimation(animationId) {
    if (this.runningAnimations.has(animationId)) {
      this.runningAnimations.delete(animationId)
      return true
    }
    return false
  }
  
  // 停止所有动画
  stopAllAnimations() {
    this.runningAnimations.clear()
  }
  
  // 获取运行中的动画数量
  getRunningAnimationCount() {
    return this.runningAnimations.size
  }
  
  // 创建序列动画
  createSequence(animations) {
    return animations.reduce((promise, animationConfig) => {
      return promise.then(() => {
        return this.runAnimation(animationConfig.element, animationConfig.config)
      })
    }, Promise.resolve())
  }
  
  // 创建并行动画
  createParallel(animations) {
    const promises = animations.map(animationConfig => {
      return this.runAnimation(animationConfig.element, animationConfig.config)
    })
    return Promise.all(promises)
  }
  
  // 创建交错动画
  createStagger(animations, staggerDelay = 100) {
    return animations.reduce((promise, animationConfig, index) => {
      return promise.then(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            this.runAnimation(animationConfig.element, animationConfig.config)
              .then(resolve)
          }, index * staggerDelay)
        })
      })
    }, Promise.resolve())
  }
  
  // 创建弹簧动画
  createSpringAnimation(element, config) {
    const springConfig = {
      ...config,
      easing: ANIMATION_CONFIG.easing.bounceOut,
      duration: config.duration || 600
    }
    
    return this.runAnimation(element, springConfig)
  }
  
  // 创建缓动动画
  createEaseAnimation(element, config, easeType = 'easeOut') {
    const easeConfig = {
      ...config,
      easing: ANIMATION_CONFIG.easing[easeType] || ANIMATION_CONFIG.easing.easeOut
    }
    
    return this.runAnimation(element, easeConfig)
  }
  
  // 创建循环动画
  createLoopAnimation(element, config, iterations = 'infinite') {
    const loopConfig = {
      ...config,
      iterations
    }
    
    return this.runAnimation(element, loopConfig)
  }
}

// 创建全局动画管理器实例
const animationManager = new AnimationManager()

// 导出工具函数
export {
  animationManager,
  ANIMATION_CONFIG
}

// 便捷方法
export const createAnimation = (options) => animationManager.createAnimation(options)
export const runPresetAnimation = (element, presetName, options) => 
  animationManager.runPresetAnimation(element, presetName, options)
export const runAnimation = (element, config) => animationManager.runAnimation(element, config)
export const stopAnimation = (animationId) => animationManager.stopAnimation(animationId)
export const stopAllAnimations = () => animationManager.stopAllAnimations()
export const createSequence = (animations) => animationManager.createSequence(animations)
export const createParallel = (animations) => animationManager.createParallel(animations)
export const createStagger = (animations, delay) => animationManager.createStagger(animations, delay)

// 默认导出
export default animationManager
