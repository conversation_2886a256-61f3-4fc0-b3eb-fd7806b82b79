"use strict";
const serviceDetail = {
  id: "service-001",
  title: "家电维修",
  icon: "/static/images/service/repair.png",
  images: [
    "/static/images/service/repair-1.jpg",
    "/static/images/service/repair-2.jpg",
    "/static/images/service/repair-3.jpg",
    "/static/images/service/repair-4.jpg"
  ],
  description: "提供各类家电维修服务，包括空调、冰箱、洗衣机、电视等。我们的技师均有5年以上维修经验，持证上岗，服务范围覆盖磁县全境。",
  price: "上门费30元起",
  priceDetails: [
    { name: "上门检测费", price: 30, unit: "次" },
    { name: "小型家电维修", price: 50, unit: "次起" },
    { name: "中型家电维修", price: 80, unit: "次起" },
    { name: "大型家电维修", price: 120, unit: "次起" }
  ],
  rating: 4.8,
  orderCount: 1256,
  category: "家居服务",
  tags: ["上门服务", "快速响应", "专业维修"],
  serviceTime: "8:00-21:00",
  serviceArea: "磁县城区及周边5公里范围",
  provider: {
    id: "provider-001",
    name: "磁县家电维修中心",
    logo: "/static/images/service/provider-logo.png",
    address: "磁县城区幸福路456号",
    phone: "0310-12345678",
    certification: ["营业执照", "服务资质证书"],
    establishTime: "2015-05",
    employeeCount: 15,
    introduction: "磁县家电维修中心成立于2015年，是磁县规模最大、服务最专业的家电维修机构。我们拥有一支技术精湛、经验丰富的维修团队，致力于为磁县居民提供高质量的家电维修服务。"
  },
  serviceProcess: [
    { step: 1, title: "预约下单", description: "在线选择服务项目，填写地址和预约时间" },
    { step: 2, title: "技师上门", description: "技师按预约时间上门，进行故障检测" },
    { step: 3, title: "报价确认", description: "技师检测后报价，客户确认后进行维修" },
    { step: 4, title: "维修完成", description: "维修完成后，客户验收并支付费用" },
    { step: 5, title: "售后保障", description: "维修后30天内免费保修" }
  ],
  serviceGuarantee: [
    { title: "专业技师", description: "所有技师均持证上岗，有5年以上维修经验" },
    { title: "品质保障", description: "使用原厂配件，30天内免费保修" },
    { title: "准时上门", description: "预约后准时上门，超时赔付" },
    { title: "明码标价", description: "检测后明确报价，不存在隐形消费" }
  ],
  faq: [
    {
      question: "如何预约上门维修服务？",
      answer: "您可以通过我们的小程序预约，也可以直接拨打服务热线0310-12345678进行预约。"
    },
    {
      question: "维修费用如何收取？",
      answer: "我们收取30元上门检测费，维修费用根据故障类型和配件更换情况而定，技师会在检测后给出明确报价，您确认后我们才会进行维修。"
    },
    {
      question: "维修后有保修吗？",
      answer: "我们提供30天的免费保修服务，如果在保修期内出现同样的故障，我们将免费上门维修。"
    },
    {
      question: "可以提供发票吗？",
      answer: "可以，我们可以提供正规发票，请在下单时备注需要开具发票。"
    }
  ],
  reviews: [
    {
      id: "review-001",
      user: {
        id: "user-001",
        nickname: "磁州居民",
        avatar: "/static/images/tabbar/user-blue.png"
      },
      rating: 5,
      content: "服务很专业，技师很有经验，很快就解决了冰箱不制冷的问题，价格也合理，非常满意！",
      time: "2024-03-15 14:30",
      images: [
        "/static/images/service/review-1.jpg",
        "/static/images/service/review-2.jpg"
      ],
      likes: 12
    },
    {
      id: "review-002",
      user: {
        id: "user-002",
        nickname: "老磁州",
        avatar: "/static/images/tabbar/user-blue.png"
      },
      rating: 4,
      content: "师傅准时上门，态度很好，空调漏水问题解决得很彻底，就是价格稍微有点贵。",
      time: "2024-03-14 10:15",
      images: [],
      likes: 8
    },
    {
      id: "review-003",
      user: {
        id: "user-003",
        nickname: "城市生活家",
        avatar: "/static/images/tabbar/user-blue.png"
      },
      rating: 5,
      content: "第二次找他们维修了，服务一如既往的好，洗衣机不排水的问题很快就解决了，而且没有乱收费，良心商家！",
      time: "2024-03-13 16:45",
      images: [
        "/static/images/service/review-3.jpg"
      ],
      likes: 15
    }
  ],
  publishTime: "2024-01-01 09:00:00",
  updateTime: "2024-03-01 10:00:00",
  views: 3256,
  collections: 145,
  shares: 87,
  isCollected: false
};
const relatedServices = [
  {
    id: "service-002",
    title: "保洁服务",
    icon: "/static/images/service/cleaning.png",
    description: "提供专业的家庭保洁、办公室保洁、开荒保洁等服务",
    price: "50元/小时起",
    rating: 4.7,
    orderCount: 986,
    category: "家居服务",
    tags: ["专业保洁", "定期保洁", "深度清洁"]
  },
  {
    id: "service-003",
    title: "搬家服务",
    icon: "/static/images/service/moving.png",
    description: "提供专业的居民搬家、企业搬迁、小型搬运等服务",
    price: "200元/次起",
    rating: 4.6,
    orderCount: 758,
    category: "家居服务",
    tags: ["专业搬运", "包装服务", "全程保险"]
  },
  {
    id: "service-004",
    title: "管道疏通",
    icon: "/static/images/service/plumbing.png",
    description: "提供厨房、卫生间等各类管道疏通服务",
    price: "80元/次起",
    rating: 4.5,
    orderCount: 632,
    category: "家居服务",
    tags: ["快速响应", "彻底疏通", "免费复查"]
  }
];
const fetchServiceDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(serviceDetail);
    }, 500);
  });
};
const fetchRelatedServices = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(relatedServices);
    }, 500);
  });
};
const fetchMoreReviews = (serviceId, page = 2) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const moreReviews = [
        {
          id: "review-004",
          user: {
            id: "user-004",
            nickname: "家电爱好者",
            avatar: "/static/images/tabbar/user-blue.png"
          },
          rating: 5,
          content: "电视突然不亮了，联系了他们，当天就上门维修好了，而且费用比我想象的便宜，非常感谢！",
          time: "2024-03-12 11:20",
          images: [],
          likes: 7
        },
        {
          id: "review-005",
          user: {
            id: "user-005",
            nickname: "磁县新居民",
            avatar: "/static/images/tabbar/user-blue.png"
          },
          rating: 4,
          content: "师傅技术不错，但是上门时间晚了半小时，希望能改进一下。",
          time: "2024-03-11 15:30",
          images: [],
          likes: 3
        }
      ];
      resolve({
        list: moreReviews,
        hasMore: false
      });
    }, 800);
  });
};
const collectService = (serviceId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: "收藏成功",
        data: {
          isCollected: true,
          collections: serviceDetail.collections + 1
        }
      });
    }, 300);
  });
};
const cancelCollectService = (serviceId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: "已取消收藏",
        data: {
          isCollected: false,
          collections: serviceDetail.collections
        }
      });
    }, 300);
  });
};
exports.cancelCollectService = cancelCollectService;
exports.collectService = collectService;
exports.fetchMoreReviews = fetchMoreReviews;
exports.fetchRelatedServices = fetchRelatedServices;
exports.fetchServiceDetail = fetchServiceDetail;
exports.relatedServices = relatedServices;
exports.serviceDetail = serviceDetail;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/service/serviceDetail.js.map
