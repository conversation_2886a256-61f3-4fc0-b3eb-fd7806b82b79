<template>
  <view class="sales-analysis-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">销售分析</text>
      <view class="navbar-right">
        <view class="export-icon" @click="exportData">📊</view>
      </view>
    </view>
    
    <!-- 日期选择器 -->
    <view class="date-selector-container">
      <view class="date-selector" @click="showDatePicker">
        <text class="date-text">{{currentDateRange}}</text>
        <text class="date-icon">📅</text>
      </view>
    </view>
    
    <!-- 销售概览 -->
    <view class="overview-section">
      <view class="overview-card">
        <view class="overview-item">
          <text class="overview-value">¥{{salesData.totalSales}}</text>
          <text class="overview-label">总销售额</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{salesData.totalOrders}}</text>
          <text class="overview-label">总订单数</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">¥{{salesData.avgOrderValue}}</text>
          <text class="overview-label">平均客单价</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{salesData.growthRate}}%</text>
          <text class="overview-label">同比增长</text>
          <view :class="['trend-icon', salesData.growthRate >= 0 ? 'up' : 'down']"></view>
        </view>
      </view>
    </view>
    
    <!-- 销售趋势图 -->
    <view class="trend-section">
      <view class="section-header">
        <text class="section-title">销售趋势</text>
        <view class="chart-tabs">
          <view 
            v-for="(tab, index) in chartTabs" 
            :key="index"
            :class="['tab-item', {'active': currentChartTab === tab.value}]"
            @click="switchChartTab(tab.value)">
            {{tab.name}}
          </view>
        </view>
      </view>
      
      <view class="chart-card">
        <view class="chart-container">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">{{getChartTitle()}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 商品销量分析 -->
    <view class="product-section">
      <view class="section-header">
        <text class="section-title">商品销量分析</text>
        <text class="section-more" @click="navigateTo('./product')">查看详情</text>
      </view>
      
      <view class="product-card">
        <view class="sub-section">
          <text class="sub-title">热销商品 TOP5</text>
          <view class="product-list">
            <view 
              v-for="(product, index) in topProducts" 
              :key="index"
              class="product-item">
              <view class="ranking-number">{{index + 1}}</view>
              <image class="product-image" :src="product.image" mode="aspectFill"></image>
              <view class="product-info">
                <text class="product-name">{{product.name}}</text>
                <text class="product-sales">销量: {{product.sales}}件</text>
              </view>
              <view class="product-amount">
                <text class="amount-label">销售额</text>
                <text class="amount-value">¥{{product.amount}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="sub-section">
          <text class="sub-title">滞销商品 TOP5</text>
          <view class="product-list">
            <view 
              v-for="(product, index) in lowProducts" 
              :key="index"
              class="product-item">
              <view class="ranking-number low">{{index + 1}}</view>
              <image class="product-image" :src="product.image" mode="aspectFill"></image>
              <view class="product-info">
                <text class="product-name">{{product.name}}</text>
                <text class="product-sales">销量: {{product.sales}}件</text>
              </view>
              <view class="product-amount">
                <text class="amount-label">库存量</text>
                <text class="amount-value">{{product.stock}}件</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 时段分析 -->
    <view class="time-section">
      <view class="section-header">
        <text class="section-title">时段分析</text>
        <text class="section-more" @click="navigateTo('./time')">查看详情</text>
      </view>
      
      <view class="time-card">
        <view class="time-chart">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">销售时段分布图</text>
          </view>
        </view>
        
        <view class="time-info">
          <view class="time-item">
            <view class="time-icon peak-icon">🔥</view>
            <view class="time-content">
              <text class="time-title">销售高峰期</text>
              <text class="time-value">{{timeData.peakTime}}</text>
            </view>
          </view>
          
          <view class="time-item">
            <view class="time-icon low-icon">📉</view>
            <view class="time-content">
              <text class="time-title">销售低谷期</text>
              <text class="time-value">{{timeData.lowTime}}</text>
            </view>
          </view>
          
          <view class="time-item">
            <view class="time-icon day-icon">📆</view>
            <view class="time-content">
              <text class="time-title">销售最佳日</text>
              <text class="time-value">{{timeData.bestDay}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 利润分析 -->
    <view class="profit-section">
      <view class="section-header">
        <text class="section-title">利润分析</text>
        <text class="section-more" @click="navigateTo('./profit')">查看详情</text>
      </view>
      
      <view class="profit-card">
        <view class="profit-overview">
          <view class="profit-item">
            <text class="profit-value">¥{{profitData.totalProfit}}</text>
            <text class="profit-label">总利润</text>
          </view>
          <view class="profit-item">
            <text class="profit-value">{{profitData.profitRate}}%</text>
            <text class="profit-label">平均利润率</text>
          </view>
          <view class="profit-item">
            <text class="profit-value">¥{{profitData.highestProfit}}</text>
            <text class="profit-label">最高单品利润</text>
          </view>
        </view>
        
        <view class="profit-chart">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">利润分布图</text>
          </view>
        </view>
        
        <view class="profit-tips">
          <view class="tip-icon">💡</view>
          <view class="tip-content">
            <text class="tip-title">利润优化建议</text>
            <text class="tip-text">{{profitData.optimizationTip}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDateRange: '2023-05-01 至 2023-05-31',
      currentChartTab: 'daily',
      chartTabs: [
        { name: '日', value: 'daily' },
        { name: '周', value: 'weekly' },
        { name: '月', value: 'monthly' }
      ],
      salesData: {
        totalSales: '128,560.50',
        totalOrders: '1,256',
        avgOrderValue: '102.36',
        growthRate: 15.8
      },
      topProducts: [
        {
          name: '有机蔬菜礼盒',
          image: '/static/images/product-1.png',
          sales: 256,
          amount: '12,800.00'
        },
        {
          name: '进口水果组合',
          image: '/static/images/product-2.png',
          sales: 198,
          amount: '9,900.00'
        },
        {
          name: '特级橄榄油',
          image: '/static/images/product-3.png',
          sales: 156,
          amount: '7,800.00'
        },
        {
          name: '有机鸡蛋',
          image: '/static/images/product-4.png',
          sales: 142,
          amount: '5,680.00'
        },
        {
          name: '全麦面包',
          image: '/static/images/product-5.png',
          sales: 135,
          amount: '4,050.00'
        }
      ],
      lowProducts: [
        {
          name: '进口坚果礼盒',
          image: '/static/images/product-6.png',
          sales: 12,
          stock: 86
        },
        {
          name: '高端红酒',
          image: '/static/images/product-7.png',
          sales: 15,
          stock: 72
        },
        {
          name: '特色调料组合',
          image: '/static/images/product-8.png',
          sales: 18,
          stock: 65
        },
        {
          name: '有机蜂蜜',
          image: '/static/images/product-9.png',
          sales: 20,
          stock: 58
        },
        {
          name: '手工巧克力',
          image: '/static/images/product-10.png',
          sales: 22,
          stock: 45
        }
      ],
      timeData: {
        peakTime: '18:00 - 20:00',
        lowTime: '14:00 - 16:00',
        bestDay: '周六'
      },
      profitData: {
        totalProfit: '45,860.20',
        profitRate: '35.7',
        highestProfit: '3,560.00',
        optimizationTip: '建议提高"有机蔬菜礼盒"的价格，增加促销力度降低"进口坚果礼盒"库存'
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    switchChartTab(tab) {
      this.currentChartTab = tab;
    },
    getChartTitle() {
      const titles = {
        daily: '日销售趋势图',
        weekly: '周销售趋势图',
        monthly: '月销售趋势图'
      };
      return titles[this.currentChartTab] || '销售趋势图';
    },
    exportData() {
      uni.showActionSheet({
        itemList: ['导出Excel', '导出PDF', '生成分析报告'],
        success: (res) => {
          uni.showToast({
            title: '导出功能开发中',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style>
.sales-analysis-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.export-icon {
  font-size: 20px;
  color: #fff;
}

.date-selector-container {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

.date-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.date-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.date-icon {
  font-size: 16px;
}

.overview-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.overview-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.overview-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #999;
}

.trend-icon {
  width: 0;
  height: 0;
  position: absolute;
  right: -15px;
  top: 8px;
}

.trend-icon.up {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 8px solid #52c41a;
}

.trend-icon.down {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid #f5222d;
}

.trend-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #1677FF;
}

.chart-tabs {
  display: flex;
}

.tab-item {
  padding: 4px 12px;
  font-size: 12px;
  color: #666;
  background-color: #f0f0f0;
  margin-left: 8px;
  border-radius: 12px;
}

.tab-item.active {
  color: #fff;
  background-color: #1677FF;
}

.chart-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.chart-container {
  height: 200px;
}

.chart-placeholder {
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 14px;
  color: #999;
}

.product-section, .time-section, .profit-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.product-card, .time-card, .profit-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.sub-section {
  margin-bottom: 16px;
}

.sub-section:last-child {
  margin-bottom: 0;
}

.sub-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.product-list {
  
}

.product-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #1677FF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #fff;
  margin-right: 12px;
}

.ranking-number.low {
  background-color: #ff4d4f;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-color: #f0f0f0;
  margin-right: 12px;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.product-sales {
  font-size: 12px;
  color: #999;
}

.product-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.amount-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.amount-value {
  font-size: 14px;
  font-weight: 600;
  color: #ff6a00;
}

.time-chart {
  height: 180px;
  margin-bottom: 16px;
}

.time-info {
  display: flex;
  justify-content: space-between;
}

.time-item {
  display: flex;
  align-items: center;
}

.time-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 8px;
}

.peak-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.low-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.day-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.time-content {
  
}

.time-title {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
  display: block;
}

.time-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.profit-overview {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.profit-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profit-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.profit-label {
  font-size: 12px;
  color: #999;
}

.profit-chart {
  height: 180px;
  margin-bottom: 16px;
}

.profit-tips {
  display: flex;
  background-color: #f6ffed;
  padding: 12px;
  border-radius: 4px;
}

.tip-icon {
  font-size: 20px;
  margin-right: 12px;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.tip-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}
</style> 