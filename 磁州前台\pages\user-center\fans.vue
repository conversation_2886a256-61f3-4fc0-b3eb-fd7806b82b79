<template>
  <view class="fans-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的粉丝</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 粉丝列表 -->
    <view class="fans-list" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <!-- 无数据提示 -->
      <view class="empty-container" v-if="fansList.length === 0">
        <image src="/static/images/empty.png" class="empty-icon"></image>
        <view class="empty-text">暂无粉丝</view>
      </view>
      
      <!-- 用户列表 -->
      <view class="user-item" v-for="(user, index) in fansList" :key="index">
        <!-- 用户头像 -->
        <view class="user-avatar" @click="goToUserProfile(user.userId)">
          <image :src="user.avatar || '/static/images/default-avatar.png'" class="avatar-img" mode="aspectFill"></image>
        </view>
        
        <!-- 用户信息 -->
        <view class="user-info" @click="goToUserProfile(user.userId)">
          <view class="user-name">{{ user.nickname }}</view>
          <view class="user-signature">{{ user.signature || '这个人很懒，什么都没留下' }}</view>
        </view>
        
        <!-- 关注按钮 -->
        <view class="follow-btn" :class="{ 'followed': user.isFollowed }" @click="toggleFollow(user)">
          {{ user.isFollowed ? '已关注' : '关注' }}
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" v-if="fansList.length > 0 && hasMore">
        <text class="loading-text">加载中...</text>
      </view>
      <view class="no-more" v-if="fansList.length > 0 && !hasMore">
        <text class="no-more-text">没有更多了</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      fansList: [],
      page: 1,
      pageSize: 10,
      hasMore: true,
      isLoading: false
    }
  },
  created() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
  },
  onLoad() {
    this.getFansList();
  },
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.page++;
      this.getFansList();
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取粉丝列表
    getFansList() {
      if (this.isLoading) return;
      this.isLoading = true;
      
      // 模拟获取粉丝列表数据
      setTimeout(() => {
        // 模拟数据
        const mockData = [
          {
            userId: '2001',
            nickname: '小明',
            avatar: '/static/images/default-avatar.png',
            signature: '喜欢旅行和美食',
            isFollowed: false
          },
          {
            userId: '2002',
            nickname: '小红',
            avatar: '/static/images/default-avatar.png',
            signature: '爱生活，爱自己',
            isFollowed: true
          },
          {
            userId: '2003',
            nickname: '小刚',
            avatar: '/static/images/default-avatar.png',
            signature: '努力成为更好的自己',
            isFollowed: false
          },
          {
            userId: '2004',
            nickname: '小丽',
            avatar: '/static/images/default-avatar.png',
            signature: '保持微笑，保持热爱',
            isFollowed: true
          },
          {
            userId: '2005',
            nickname: '小华',
            avatar: '/static/images/default-avatar.png',
            signature: '简单生活，快乐每一天',
            isFollowed: false
          }
        ];
        
        // 第一页直接赋值，后续页面追加
        if (this.page === 1) {
          this.fansList = mockData;
        } else {
          this.fansList = [...this.fansList, ...mockData];
        }
        
        // 判断是否还有更多数据
        this.hasMore = this.page < 3; // 模拟只有3页数据
        this.isLoading = false;
      }, 500);
    },
    
    // 切换关注状态
    toggleFollow(user) {
      // 模拟关注/取消关注
      if (user.isFollowed) {
        // 已关注，执行取消关注
        uni.showModal({
          title: '提示',
          content: `确定取消关注 ${user.nickname} 吗？`,
          success: (res) => {
            if (res.confirm) {
              // 模拟取消关注操作
              user.isFollowed = false;
              
              uni.showToast({
                title: '已取消关注',
                icon: 'success'
              });
            }
          }
        });
      } else {
        // 未关注，执行关注
        user.isFollowed = true;
        
        uni.showToast({
          title: '已关注',
          icon: 'success'
        });
      }
    },
    
    // 跳转到用户主页
    goToUserProfile(userId) {
      uni.navigateTo({
        url: `/pages/user-center/profile?userId=${userId}`
      });
    }
  }
}
</script>

<style>
.fans-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 粉丝列表样式 */
.fans-list {
  padding: 20rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 用户列表项 */
.user-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50rpx;
}

.user-info {
  flex: 1;
  overflow: hidden;
}

.user-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.user-signature {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.follow-btn {
  min-width: 120rpx;
  height: 60rpx;
  background-color: #0052CC;
  color: #fff;
  font-size: 26rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
}

.followed {
  background-color: #f0f0f0;
  color: #666;
}

/* 加载更多 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text, .no-more-text {
  font-size: 24rpx;
  color: #999;
}
</style> 