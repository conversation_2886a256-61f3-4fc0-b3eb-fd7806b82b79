"use strict";
const common_vendor = require("../common/vendor.js");
if (!Math) {
  ConfigurablePremiumActions();
}
const ConfigurablePremiumActions = () => "./premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "PromotionToolButton",
  props: {
    // 页面类型
    pageType: {
      type: String,
      default: "publish",
      validator: (value) => [
        "publish",
        "merchant_join",
        "merchant_renew",
        "merchant_top",
        "merchant_refresh",
        "carpool_top",
        "carpool_refresh",
        "product_top",
        "product_refresh"
      ].includes(value)
    },
    // 显示模式
    showMode: {
      type: String,
      default: "button",
      validator: (value) => ["button", "direct", "selection"].includes(value)
    },
    // 项目数据
    itemData: {
      type: Object,
      default: () => ({
        id: "",
        title: "推广",
        description: "提升曝光度"
      })
    }
  },
  emits: ["action-completed", "action-cancelled"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const handleActionCompleted = (result) => {
      common_vendor.index.__f__("log", "at components/PromotionToolButton.vue:50", "推广操作完成:", result);
      emit("action-completed", result);
    };
    const handleActionCancelled = (result) => {
      common_vendor.index.__f__("log", "at components/PromotionToolButton.vue:56", "推广操作取消:", result);
      emit("action-cancelled", result);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleActionCompleted),
        b: common_vendor.o(handleActionCancelled),
        c: common_vendor.p({
          pageType: __props.pageType,
          showMode: __props.showMode,
          itemData: __props.itemData
        })
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-40fbc48b"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/PromotionToolButton.js.map
