
.wallet-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(to right, #0052CC, #0066FF);
  margin: 0 30rpx;
  padding: a0rpx;
  border-radius: 20rpx;
  color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.2);
}
.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin: 30rpx 0 20rpx 40rpx;
}
.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin: 0 0 40rpx 40rpx;
}
.balance-buttons {
  display: flex;
  justify-content: space-around;
  margin: 30rpx 40rpx 40rpx;
}
.balance-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0;
}
.withdraw-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.5);
}
.recharge-btn {
  background-color: #fff;
  color: #0052CC;
}

/* 钱包功能区 */
.wallet-functions {
  background-color: #fff;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 20rpx 0;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.function-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.function-item:last-child {
  border-bottom: none;
}
.function-left {
  display: flex;
  align-items: center;
}
.function-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  background-color: #f8f9fc;
  border-radius: 20rpx;
  padding: 10rpx;
}
.function-name {
  font-size: 28rpx;
  color: #333;
}
.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  transform: rotate(90deg);
  opacity: 0.5;
}

/* 交易记录区域 */
.transaction-section {
  background-color: #fff;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  margin-bottom: 20rpx;
}
.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}
.section-more {
  font-size: 24rpx;
  color: #0052CC;
}
.transaction-list {
  min-height: 300rpx;
}
.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.transaction-item:last-child {
  border-bottom: none;
}
.transaction-left {
  display: flex;
  flex-direction: column;
}
.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.transaction-time {
  font-size: 24rpx;
  color: #999;
}
.transaction-amount {
  font-size: 32rpx;
  font-weight: 500;
}
.income {
  color: #07c160;
}
.expense {
  color: #f56c6c;
}

/* 空状态 */
.empty-view {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
