{"version": 3, "file": "levels.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/levels.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25cbGV2ZWxzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"levels-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">分销等级设置</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 等级开关 -->\n    <view class=\"switch-card\">\n      <view class=\"switch-item\">\n        <view class=\"switch-label\">\n          <text class=\"label-text\">启用分销等级</text>\n          <text class=\"label-desc\">开启后，分销员将根据业绩自动升级</text>\n        </view>\n        <switch \n          :checked=\"settings.enableLevels\" \n          color=\"#6B0FBE\" \n          @change=\"toggleLevels\"\n        />\n      </view>\n    </view>\n    \n    <!-- 等级列表 -->\n    <view class=\"levels-list\" v-if=\"settings.enableLevels\">\n      <view \n        v-for=\"(level, index) in levels\" \n        :key=\"index\" \n        class=\"level-card\"\n      >\n        <view class=\"level-header\">\n          <view class=\"level-title\">\n            <text class=\"level-name\">{{level.name}}</text>\n            <text class=\"level-tag\" :style=\"{ backgroundColor: level.color }\">{{level.tag}}</text>\n      </view>\n          <view class=\"level-actions\">\n            <view class=\"edit-icon\" @click=\"editLevel(index)\"></view>\n            <view class=\"delete-icon\" v-if=\"index > 0\" @click=\"deleteLevel(index)\"></view>\n        </view>\n      </view>\n      \n        <view class=\"level-content\">\n          <view class=\"level-item\">\n            <text class=\"item-label\">升级条件</text>\n            <text class=\"item-value\">{{getUpgradeText(level)}}</text>\n    </view>\n    \n          <view class=\"level-item\">\n            <text class=\"item-label\">佣金比例</text>\n            <text class=\"item-value\">一级：{{level.commissionRates.level1}}% / 二级：{{level.commissionRates.level2}}%</text>\n      </view>\n      \n          <view class=\"level-item\">\n            <text class=\"item-label\">特权说明</text>\n            <text class=\"item-value\">{{level.benefits || '无特殊权益'}}</text>\n        </view>\n        </view>\n      </view>\n      \n      <!-- 添加等级按钮 -->\n      <view class=\"add-level\" @click=\"addLevel\">\n        <view class=\"add-icon\"></view>\n        <text class=\"add-text\">添加等级</text>\n      </view>\n    </view>\n    \n    <!-- 等级编辑弹窗 -->\n    <view class=\"level-modal\" v-if=\"showLevelModal\">\n      <view class=\"modal-mask\" @click=\"closeLevelModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">{{isEditMode ? '编辑等级' : '添加等级'}}</text>\n          <view class=\"close-icon\" @click=\"closeLevelModal\"></view>\n        </view>\n        \n        <view class=\"form-content\">\n          <view class=\"form-item\">\n            <text class=\"form-label\">等级名称</text>\n            <input class=\"form-input\" type=\"text\" v-model=\"formData.name\" placeholder=\"请输入等级名称\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">等级标签</text>\n            <input class=\"form-input\" type=\"text\" v-model=\"formData.tag\" placeholder=\"请输入等级标签\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">标签颜色</text>\n            <view class=\"color-picker\">\n              <view \n                v-for=\"(color, index) in colorOptions\" \n                :key=\"index\" \n                class=\"color-option\" \n                :style=\"{ backgroundColor: color }\"\n                :class=\"{ 'active': formData.color === color }\"\n                @click=\"formData.color = color\"\n              ></view>\n            </view>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">升级条件</text>\n            <view class=\"condition-type\">\n              <view \n                class=\"type-option\" \n                :class=\"{ 'active': formData.upgradeType === 'sales' }\"\n                @click=\"formData.upgradeType = 'sales'\"\n              >\n                <text>销售额</text>\n              </view>\n              <view \n                class=\"type-option\" \n                :class=\"{ 'active': formData.upgradeType === 'orders' }\"\n                @click=\"formData.upgradeType = 'orders'\"\n              >\n                <text>订单数</text>\n              </view>\n            </view>\n            <input \n              class=\"form-input\" \n              type=\"number\" \n              v-model=\"formData.upgradeValue\" \n              :placeholder=\"`请输入最低${formData.upgradeType === 'sales' ? '销售额' : '订单数'}`\" \n            />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">一级佣金比例 (%)</text>\n            <input class=\"form-input\" type=\"digit\" v-model=\"formData.commissionRates.level1\" placeholder=\"请输入一级佣金比例\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">二级佣金比例 (%)</text>\n            <input class=\"form-input\" type=\"digit\" v-model=\"formData.commissionRates.level2\" placeholder=\"请输入二级佣金比例\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">特权说明</text>\n            <textarea class=\"form-textarea\" v-model=\"formData.benefits\" placeholder=\"请输入该等级特有的权益说明\" />\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <button class=\"cancel-btn\" @click=\"closeLevelModal\">取消</button>\n          <button class=\"submit-btn\" :disabled=\"!canSubmit\" :class=\"{ 'disabled': !canSubmit }\" @click=\"submitLevel\">确定</button>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 保存按钮 -->\n    <view class=\"save-section\" v-if=\"settings.enableLevels\">\n      <button class=\"save-btn\" @click=\"saveSettings\">保存设置</button>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport distributionService from '@/utils/distributionService';\n\n// 等级设置\nconst settings = reactive({\n  enableLevels: true,\n  autoUpgrade: true,\n  downgradeEnable: false,\n  upgradeCycle: 'monthly'\n});\n\n// 等级列表\nconst levels = ref([\n  {\n    id: 1,\n    name: '普通分销员',\n    tag: '初级',\n    color: '#67C23A',\n    upgradeType: 'sales',\n    upgradeValue: 0,\n    commissionRates: {\n      level1: 10,\n      level2: 5\n    },\n    benefits: '基础分销权益'\n  },\n  {\n    id: 2,\n    name: '高级分销员',\n    tag: '高级',\n    color: '#409EFF',\n    upgradeType: 'sales',\n    upgradeValue: 1000,\n    commissionRates: {\n      level1: 15,\n      level2: 8\n    },\n    benefits: '专属活动优先参与权'\n  },\n  {\n    id: 3,\n    name: '金牌分销员',\n    tag: '金牌',\n    color: '#E6A23C',\n    upgradeType: 'sales',\n    upgradeValue: 5000,\n    commissionRates: {\n      level1: 20,\n      level2: 10\n    },\n    benefits: '专属客服、活动优先参与权'\n  }\n]);\n\n// 颜色选项\nconst colorOptions = [\n  '#67C23A', '#409EFF', '#E6A23C', '#F56C6C', '#909399', \n  '#6B0FBE', '#FF9500', '#00C58E', '#1989FA', '#FF5722'\n];\n\n// 等级编辑弹窗\nconst showLevelModal = ref(false);\n\n// 是否是编辑模式\nconst isEditMode = ref(false);\n\n// 当前编辑的等级索引\nconst currentLevelIndex = ref(-1);\n\n// 表单数据\nconst formData = reactive({\n  name: '',\n  tag: '',\n  color: '#67C23A',\n  upgradeType: 'sales',\n  upgradeValue: 0,\n  commissionRates: {\n    level1: 0,\n    level2: 0\n  },\n  benefits: ''\n});\n\n// 是否可以提交\nconst canSubmit = computed(() => {\n  return formData.name.trim() !== '' && \n         formData.tag.trim() !== '' && \n         formData.upgradeValue !== '' &&\n         formData.commissionRates.level1 !== '' &&\n         formData.commissionRates.level2 !== '';\n});\n\n// 页面加载\nonMounted(async () => {\n  // 获取分销等级设置\n  await getDistributionLevels();\n});\n\n// 获取分销等级设置\nconst getDistributionLevels = async () => {\n  try {\n    const result = await distributionService.getDistributionLevels();\n    \n    if (result) {\n      // 更新设置\n      Object.assign(settings, result.settings);\n      \n      // 更新等级列表\n      if (result.levels && result.levels.length > 0) {\n        levels.value = result.levels;\n      }\n    }\n  } catch (error) {\n    console.error('获取分销等级设置失败', error);\n    uni.showToast({\n      title: '获取分销等级设置失败',\n      icon: 'none'\n    });\n  }\n};\n\n// 切换等级开关\nconst toggleLevels = (e) => {\n  settings.enableLevels = e.detail.value;\n};\n\n// 获取升级条件文本\nconst getUpgradeText = (level) => {\n  if (level.upgradeType === 'sales') {\n    return `累计销售额 ≥ ${level.upgradeValue}元`;\n  } else {\n    return `累计订单数 ≥ ${level.upgradeValue}单`;\n  }\n};\n\n// 添加等级\nconst addLevel = () => {\n  isEditMode.value = false;\n  currentLevelIndex.value = -1;\n  \n  // 重置表单数据\n  formData.name = '';\n  formData.tag = '';\n  formData.color = '#67C23A';\n  formData.upgradeType = 'sales';\n  formData.upgradeValue = '';\n  formData.commissionRates.level1 = '';\n  formData.commissionRates.level2 = '';\n  formData.benefits = '';\n  \n  showLevelModal.value = true;\n};\n\n// 编辑等级\nconst editLevel = (index) => {\n  isEditMode.value = true;\n  currentLevelIndex.value = index;\n  \n  const level = levels.value[index];\n  \n  // 填充表单数据\n  formData.name = level.name;\n  formData.tag = level.tag;\n  formData.color = level.color;\n  formData.upgradeType = level.upgradeType;\n  formData.upgradeValue = level.upgradeValue;\n  formData.commissionRates.level1 = level.commissionRates.level1;\n  formData.commissionRates.level2 = level.commissionRates.level2;\n  formData.benefits = level.benefits;\n  \n  showLevelModal.value = true;\n};\n\n// 删除等级\nconst deleteLevel = (index) => {\n  uni.showModal({\n    title: '删除等级',\n    content: '确定要删除该等级吗？删除后无法恢复。',\n    success: (res) => {\n      if (res.confirm) {\n        levels.value.splice(index, 1);\n      }\n    }\n  });\n};\n\n// 关闭等级编辑弹窗\nconst closeLevelModal = () => {\n  showLevelModal.value = false;\n};\n\n// 提交等级\nconst submitLevel = () => {\n  if (!canSubmit.value) return;\n  \n  const newLevel = {\n    name: formData.name,\n    tag: formData.tag,\n    color: formData.color,\n    upgradeType: formData.upgradeType,\n    upgradeValue: parseFloat(formData.upgradeValue),\n    commissionRates: {\n      level1: parseFloat(formData.commissionRates.level1),\n      level2: parseFloat(formData.commissionRates.level2)\n    },\n    benefits: formData.benefits\n  };\n  \n  if (isEditMode.value) {\n    // 编辑模式\n    levels.value[currentLevelIndex.value] = {\n      ...levels.value[currentLevelIndex.value],\n      ...newLevel\n    };\n  } else {\n    // 添加模式\n    newLevel.id = levels.value.length > 0 ? Math.max(...levels.value.map(l => l.id)) + 1 : 1;\n    levels.value.push(newLevel);\n  }\n  \n  closeLevelModal();\n};\n\n// 保存设置\nconst saveSettings = async () => {\n  try {\n  uni.showLoading({\n      title: '保存中...',\n      mask: true\n    });\n    \n    const result = await distributionService.saveDistributionLevels({\n      settings,\n      levels: levels.value\n    });\n    \n    uni.hideLoading();\n    \n    if (result.success) {\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success'\n    });\n    } else {\n      uni.showModal({\n        title: '保存失败',\n        content: result.message || '请稍后再试',\n        showCancel: false\n      });\n    }\n  } catch (error) {\n    uni.hideLoading();\n    console.error('保存分销等级设置失败', error);\n    uni.showToast({\n      title: '保存失败',\n      icon: 'none'\n    });\n  }\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 显示帮助\nconst showHelp = () => {\n  uni.showModal({\n    title: '分销等级帮助',\n    content: '分销等级系统可以根据分销员的业绩设置不同的佣金比例和特权，激励分销员提升业绩。系统会根据设置的条件自动升级分销员等级。',\n    showCancel: false\n  });\n};\n</script>\n\n<style lang=\"scss\">\n.levels-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 30rpx;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 88rpx 32rpx 30rpx;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 24rpx;\n  height: 24rpx;\n  border-left: 4rpx solid #fff;\n  border-bottom: 4rpx solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 36rpx;\n  font-weight: 600;\n  letter-spacing: 1rpx;\n}\n\n.navbar-right {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 24rpx;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  font-weight: bold;\n}\n\n/* 开关卡片 */\n.switch-card {\n  margin: 30rpx;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.switch-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.switch-label {\n  flex: 1;\n}\n\n.label-text {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.label-desc {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 等级列表 */\n.levels-list {\n  margin: 30rpx;\n}\n\n.level-card {\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.level-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.level-title {\n  display: flex;\n  align-items: center;\n}\n\n.level-name {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-right: 16rpx;\n}\n\n.level-tag {\n  font-size: 24rpx;\n  color: #fff;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.level-actions {\n  display: flex;\n  align-items: center;\n}\n\n.edit-icon,\n.delete-icon {\n  width: 40rpx;\n  height: 40rpx;\n  background-size: contain;\n  background-repeat: no-repeat;\n  background-position: center;\n  margin-left: 20rpx;\n}\n\n.edit-icon {\n  background-color: #409EFF;\n  border-radius: 50%;\n  position: relative;\n}\n\n.edit-icon::before,\n.edit-icon::after {\n  content: '';\n  position: absolute;\n  background-color: white;\n}\n\n.edit-icon::before {\n  width: 16rpx;\n  height: 2rpx;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.edit-icon::after {\n  width: 2rpx;\n  height: 16rpx;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.delete-icon {\n  background-color: #F56C6C;\n  border-radius: 50%;\n  position: relative;\n}\n\n.delete-icon::before {\n  content: '';\n  position: absolute;\n  width: 16rpx;\n  height: 2rpx;\n  background-color: white;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%) rotate(45deg);\n}\n\n.delete-icon::after {\n  content: '';\n  position: absolute;\n  width: 16rpx;\n  height: 2rpx;\n  background-color: white;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%) rotate(-45deg);\n}\n\n.level-content {\n  padding-top: 20rpx;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.level-item {\n  margin-bottom: 16rpx;\n}\n\n.level-item:last-child {\n  margin-bottom: 0;\n}\n\n.item-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.item-value {\n  font-size: 26rpx;\n  color: #333;\n}\n\n/* 添加等级 */\n.add-level {\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2rpx dashed #ddd;\n}\n\n.add-icon {\n  width: 40rpx;\n  height: 40rpx;\n  background-color: #6B0FBE;\n  border-radius: 50%;\n  position: relative;\n  margin-right: 16rpx;\n}\n\n.add-icon::before,\n.add-icon::after {\n  content: '';\n  position: absolute;\n  background-color: white;\n}\n\n.add-icon::before {\n  width: 20rpx;\n  height: 2rpx;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.add-icon::after {\n  width: 2rpx;\n  height: 20rpx;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.add-text {\n  font-size: 28rpx;\n  color: #6B0FBE;\n}\n\n/* 等级编辑弹窗 */\n.level-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n}\n\n.modal-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n}\n\n.modal-content {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 80%;\n  max-height: 90%;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n  overflow-y: auto;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.modal-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.close-icon {\n  width: 40rpx;\n  height: 40rpx;\n  position: relative;\n}\n\n.close-icon::before,\n.close-icon::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 32rpx;\n  height: 2rpx;\n  background-color: #999;\n}\n\n.close-icon::before {\n  transform: translate(-50%, -50%) rotate(45deg);\n}\n\n.close-icon::after {\n  transform: translate(-50%, -50%) rotate(-45deg);\n}\n\n.form-content {\n  margin-bottom: 30rpx;\n}\n\n.form-item {\n  margin-bottom: 20rpx;\n}\n\n.form-label {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 12rpx;\n  display: block;\n}\n\n.form-input {\n  width: 100%;\n  height: 80rpx;\n  background: #F5F7FA;\n  border-radius: 10rpx;\n  padding: 0 20rpx;\n  font-size: 28rpx;\n  color: #333;\n  box-sizing: border-box;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 160rpx;\n  background: #F5F7FA;\n  border-radius: 10rpx;\n  padding: 20rpx;\n  font-size: 28rpx;\n  color: #333;\n  box-sizing: border-box;\n}\n\n.color-picker {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -10rpx;\n}\n\n.color-option {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 30rpx;\n  margin: 10rpx;\n  border: 2rpx solid transparent;\n}\n\n.color-option.active {\n  border-color: #333;\n  box-shadow: 0 0 0 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.condition-type {\n  display: flex;\n  margin-bottom: 16rpx;\n}\n\n.type-option {\n  flex: 1;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  color: #666;\n  background: #F5F7FA;\n  border-radius: 10rpx;\n  margin-right: 20rpx;\n}\n\n.type-option:last-child {\n  margin-right: 0;\n}\n\n.type-option.active {\n  background: #6B0FBE;\n  color: #fff;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: space-between;\n}\n\n.cancel-btn,\n.submit-btn {\n  width: 48%;\n  height: 80rpx;\n  border-radius: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n}\n\n.cancel-btn {\n  background: #F5F7FA;\n  color: #666;\n}\n\n.submit-btn {\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n}\n\n.submit-btn.disabled {\n  background: #cccccc;\n  color: #ffffff;\n}\n\n/* 保存按钮 */\n.save-section {\n  margin: 30rpx;\n}\n\n.save-btn {\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 32rpx;\n  padding: 20rpx 0;\n  line-height: 1.5;\n  width: 100%;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/levels.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "onMounted", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAsKA,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,cAAc;AAAA,IAChB,CAAC;AAGD,UAAM,SAASC,cAAAA,IAAI;AAAA,MACjB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,QACb,cAAc;AAAA,QACd,iBAAiB;AAAA,UACf,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,QACb,cAAc;AAAA,QACd,iBAAiB;AAAA,UACf,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,QACb,cAAc;AAAA,QACd,iBAAiB;AAAA,UACf,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,UAAU;AAAA,MACX;AAAA,IACH,CAAC;AAGD,UAAM,eAAe;AAAA,MACnB;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,MAC5C;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,MAAW;AAAA,IAC9C;AAGA,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAGhC,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAG5B,UAAM,oBAAoBA,cAAAA,IAAI,EAAE;AAGhC,UAAM,WAAWD,cAAAA,SAAS;AAAA,MACxB,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,MACP,aAAa;AAAA,MACb,cAAc;AAAA,MACd,iBAAiB;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAAA,MACD,UAAU;AAAA,IACZ,CAAC;AAGD,UAAM,YAAYE,cAAQ,SAAC,MAAM;AAC/B,aAAO,SAAS,KAAK,KAAI,MAAO,MACzB,SAAS,IAAI,KAAI,MAAO,MACxB,SAAS,iBAAiB,MAC1B,SAAS,gBAAgB,WAAW,MACpC,SAAS,gBAAgB,WAAW;AAAA,IAC7C,CAAC;AAGDC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,sBAAqB;AAAA,IAC7B,CAAC;AAGD,UAAM,wBAAwB,YAAY;AACxC,UAAI;AACF,cAAM,SAAS,MAAMC,8CAAoB;AAEzC,YAAI,QAAQ;AAEV,iBAAO,OAAO,UAAU,OAAO,QAAQ;AAGvC,cAAI,OAAO,UAAU,OAAO,OAAO,SAAS,GAAG;AAC7C,mBAAO,QAAQ,OAAO;AAAA,UACvB;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,uFAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,eAAe,EAAE,OAAO;AAAA,IACnC;AAGA,UAAM,iBAAiB,CAAC,UAAU;AAChC,UAAI,MAAM,gBAAgB,SAAS;AACjC,eAAO,WAAW,MAAM,YAAY;AAAA,MACxC,OAAS;AACL,eAAO,WAAW,MAAM,YAAY;AAAA,MACrC;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,iBAAW,QAAQ;AACnB,wBAAkB,QAAQ;AAG1B,eAAS,OAAO;AAChB,eAAS,MAAM;AACf,eAAS,QAAQ;AACjB,eAAS,cAAc;AACvB,eAAS,eAAe;AACxB,eAAS,gBAAgB,SAAS;AAClC,eAAS,gBAAgB,SAAS;AAClC,eAAS,WAAW;AAEpB,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AACnB,wBAAkB,QAAQ;AAE1B,YAAM,QAAQ,OAAO,MAAM,KAAK;AAGhC,eAAS,OAAO,MAAM;AACtB,eAAS,MAAM,MAAM;AACrB,eAAS,QAAQ,MAAM;AACvB,eAAS,cAAc,MAAM;AAC7B,eAAS,eAAe,MAAM;AAC9B,eAAS,gBAAgB,SAAS,MAAM,gBAAgB;AACxD,eAAS,gBAAgB,SAAS,MAAM,gBAAgB;AACxD,eAAS,WAAW,MAAM;AAE1B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,cAAc,CAAC,UAAU;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,mBAAO,MAAM,OAAO,OAAO,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,UAAU;AAAO;AAEtB,YAAM,WAAW;AAAA,QACf,MAAM,SAAS;AAAA,QACf,KAAK,SAAS;AAAA,QACd,OAAO,SAAS;AAAA,QAChB,aAAa,SAAS;AAAA,QACtB,cAAc,WAAW,SAAS,YAAY;AAAA,QAC9C,iBAAiB;AAAA,UACf,QAAQ,WAAW,SAAS,gBAAgB,MAAM;AAAA,UAClD,QAAQ,WAAW,SAAS,gBAAgB,MAAM;AAAA,QACnD;AAAA,QACD,UAAU,SAAS;AAAA,MACvB;AAEE,UAAI,WAAW,OAAO;AAEpB,eAAO,MAAM,kBAAkB,KAAK,IAAI;AAAA,UACtC,GAAG,OAAO,MAAM,kBAAkB,KAAK;AAAA,UACvC,GAAG;AAAA,QACT;AAAA,MACA,OAAS;AAEL,iBAAS,KAAK,OAAO,MAAM,SAAS,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,IAAI,OAAK,EAAE,EAAE,CAAC,IAAI,IAAI;AACvF,eAAO,MAAM,KAAK,QAAQ;AAAA,MAC3B;AAED;IACF;AAGA,UAAM,eAAe,YAAY;AAC/B,UAAI;AACJA,sBAAAA,MAAI,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,SAAS,MAAMD,0BAAmB,oBAAC,uBAAuB;AAAA,UAC9D;AAAA,UACA,QAAQ,OAAO;AAAA,QACrB,CAAK;AAEDC,sBAAG,MAAC,YAAW;AAEf,YAAI,OAAO,SAAS;AACpBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACZ,CAAK;AAAA,QACL,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,OAAO,WAAW;AAAA,YAC3B,YAAY;AAAA,UACpB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,uFAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjbA,GAAG,WAAWC,SAAe;"}