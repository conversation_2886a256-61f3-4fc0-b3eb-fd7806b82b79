{"version": 3, "file": "detail.js", "sources": ["subPackages/activity-showcase/pages/shops/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcc2hvcHNcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"product-detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"navigateBack\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n        </view>\n        <view class=\"navbar-title\">商品详情</view>\n        <view class=\"navbar-right\">\n          <view class=\"share-btn\" @click=\"shareProduct\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <path d=\"M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 内容区域 -->\n    <scroll-view class=\"content-scroll\" scroll-y>\n      <!-- 商品轮播图 -->\n      <swiper class=\"product-swiper\" indicator-dots autoplay circular>\n        <swiper-item v-for=\"(img, index) in product.images\" :key=\"index\">\n          <image class=\"swiper-image\" :src=\"img\" mode=\"aspectFill\" @click=\"previewImage(product.images, index)\"></image>\n        </swiper-item>\n      </swiper>\n\n      <!-- 商品信息 -->\n      <view class=\"product-info-card\">\n        <view class=\"price-row\">\n          <view class=\"price-box\">\n            <text class=\"price-symbol\">¥</text>\n            <text class=\"price-value\">{{ product.price }}</text>\n            <text class=\"original-price\" v-if=\"product.originalPrice\">¥{{ product.originalPrice }}</text>\n          </view>\n          <view class=\"sold-count\">已售{{ product.sold }}件</view>\n        </view>\n\n        <view class=\"product-name\">{{ product.name }}</view>\n        \n        <view class=\"tags-row\" v-if=\"product.tags && product.tags.length\">\n          <view class=\"tag\" v-for=\"(tag, index) in product.tags\" :key=\"index\">{{ tag }}</view>\n        </view>\n      </view>\n\n      <!-- 商家信息 -->\n      <view class=\"shop-info-card\">\n        <view class=\"shop-header\" @click=\"viewShopDetail\">\n          <image class=\"shop-avatar\" :src=\"product.shop.avatar\" mode=\"aspectFill\"></image>\n          <view class=\"shop-detail\">\n            <text class=\"shop-name\">{{ product.shop.name }}</text>\n            <view class=\"shop-rating\">\n              <view v-for=\"i in 5\" :key=\"i\" class=\"star\" :class=\"{ 'active': i <= product.shop.rating }\">\n                <svg viewBox=\"0 0 24 24\" width=\"12\" height=\"12\">\n                  <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" fill=\"currentColor\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                </svg>\n              </view>\n              <text class=\"rating-value\">{{ product.shop.rating }}</text>\n            </view>\n          </view>\n          <view class=\"view-shop-btn\">\n            进店逛逛\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n      </view>\n\n      <!-- 商品规格 -->\n      <view class=\"specs-card\">\n        <view class=\"specs-item\" @click=\"showSpecsSelector\">\n          <text class=\"specs-label\">规格</text>\n          <view class=\"specs-value\">\n            <text>{{ selectedSpecs || '请选择规格' }}</text>\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"specs-item\" @click=\"showDeliveryInfo\">\n          <text class=\"specs-label\">配送</text>\n          <view class=\"specs-value\">\n            <text>{{ product.delivery }}</text>\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"specs-item\" @click=\"showServiceInfo\">\n          <text class=\"specs-label\">服务</text>\n          <view class=\"specs-value\">\n            <view class=\"service-tags\">\n              <view class=\"service-tag\" v-for=\"(service, index) in product.services\" :key=\"index\">\n                <svg viewBox=\"0 0 24 24\" width=\"14\" height=\"14\">\n                  <path d=\"M20 6L9 17l-5-5\" stroke=\"#07C160\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                </svg>\n                <text>{{ service }}</text>\n              </view>\n            </view>\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n      </view>\n\n      <!-- 商品详情 -->\n      <view class=\"detail-card\">\n        <view class=\"detail-header\">\n          <text class=\"detail-title\">商品详情</text>\n        </view>\n        <view class=\"detail-content\">\n          <rich-text :nodes=\"product.description\"></rich-text>\n          <view class=\"detail-images\">\n            <image \n              v-for=\"(img, index) in product.detailImages\" \n              :key=\"index\" \n              :src=\"img\" \n              mode=\"widthFix\" \n              class=\"detail-image\"\n            ></image>\n          </view>\n        </view>\n      </view>\n\n      <!-- 商品评价 -->\n      <view class=\"reviews-card\">\n        <view class=\"reviews-header\">\n          <text class=\"reviews-title\">商品评价({{ product.reviewCount }})</text>\n          <view class=\"view-all\" @click=\"viewAllReviews\">\n            查看全部\n            <svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"reviews-content\">\n          <view class=\"review-item\" v-for=\"(review, index) in product.reviews\" :key=\"index\">\n            <view class=\"review-header\">\n              <image class=\"reviewer-avatar\" :src=\"review.avatar\" mode=\"aspectFill\"></image>\n              <view class=\"reviewer-info\">\n                <text class=\"reviewer-name\">{{ review.name }}</text>\n                <view class=\"review-rating\">\n                  <view v-for=\"i in 5\" :key=\"i\" class=\"star\" :class=\"{ 'active': i <= review.rating }\">\n                    <svg viewBox=\"0 0 24 24\" width=\"12\" height=\"12\">\n                      <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" fill=\"currentColor\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                    </svg>\n                  </view>\n                  <text class=\"review-date\">{{ review.date }}</text>\n                </view>\n              </view>\n            </view>\n            <view class=\"review-content\">{{ review.content }}</view>\n            <view class=\"review-images\" v-if=\"review.images && review.images.length\">\n              <image \n                v-for=\"(img, imgIndex) in review.images\" \n                :key=\"imgIndex\" \n                :src=\"img\" \n                mode=\"aspectFill\"\n                class=\"review-image\"\n                @click=\"previewImage(review.images, imgIndex)\"\n              ></image>\n            </view>\n            <view class=\"specs-info\" v-if=\"review.specs\">{{ review.specs }}</view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 推荐商品 -->\n      <view class=\"recommend-card\">\n        <view class=\"recommend-header\">\n          <text class=\"recommend-title\">猜你喜欢</text>\n        </view>\n        <view class=\"recommend-list\">\n          <view \n            class=\"recommend-item\" \n            v-for=\"(item, index) in recommendProducts\" \n            :key=\"index\"\n            @click=\"viewProductDetail(item.id)\"\n          >\n            <image class=\"recommend-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\n            <view class=\"recommend-info\">\n              <text class=\"recommend-name\">{{ item.name }}</text>\n              <view class=\"recommend-price-row\">\n                <text class=\"recommend-price\">¥{{ item.price }}</text>\n                <text class=\"recommend-sold\">已售{{ item.sold }}件</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 底部操作栏 -->\n    <view class=\"footer-bar\">\n      <view class=\"footer-left\">\n        <view class=\"action-btn\" @click=\"navigateToShop\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n            <path d=\"M20 7h-7.05a5 5 0 00-9.9 0H3a1 1 0 00-1 1v1a1 1 0 001 1v8a3 3 0 003 3h8a3 3 0 003-3v-8a1 1 0 001-1V8a1 1 0 00-1-1zM10 20H6a1 1 0 01-1-1v-8h5v9zm8-1a1 1 0 01-1 1h-4v-9h5v8z\" stroke=\"currentColor\" fill=\"none\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            <circle cx=\"10\" cy=\"5\" r=\"2\" stroke=\"currentColor\" fill=\"none\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n          </svg>\n          <text>店铺</text>\n        </view>\n        <view class=\"action-btn\" @click=\"toggleFavorite\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" :class=\"{ 'active': isFavorite }\">\n            <path d=\"M20.84 4.61a5.5 5.5 0 00-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 00-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 000-7.78z\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n          <text>收藏</text>\n        </view>\n        <view class=\"action-btn\" @click=\"contactService\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n            <path d=\"M21 11.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n          <text>客服</text>\n        </view>\n      </view>\n      <view class=\"footer-right\">\n        <button class=\"cart-btn\" @click=\"addToCart\">加入购物车</button>\n        <button class=\"buy-btn\" @click=\"buyNow\">立即购买</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\n\n// 商品信息\nconst product = ref({\n  id: '1',\n  name: '磁州特产手工小酥肉',\n  price: 68.00,\n  originalPrice: 88.00,\n  sold: 1024,\n  tags: ['限时特惠', '包邮', '48小时发货'],\n  delivery: '免运费',\n  services: ['7天无理由退货', '正品保证', '极速退款'],\n  description: '<p>磁州特产手工小酥肉，传统工艺制作，选用上等五花肉，经过腌制、裹粉、油炸等多道工序精心制作而成。肉质酥脆，口感香浓，回味无穷。</p>',\n  detailImages: [\n    '/static/images/products/detail1.jpg',\n    '/static/images/products/detail2.jpg',\n    '/static/images/products/detail3.jpg'\n  ],\n  images: [\n    '/static/images/products/product1-1.jpg',\n    '/static/images/products/product1-2.jpg',\n    '/static/images/products/product1-3.jpg'\n  ],\n  specs: [\n    {\n      name: '规格',\n      options: ['小份(250g)', '中份(500g)', '大份(1000g)']\n    },\n    {\n      name: '口味',\n      options: ['原味', '微辣', '麻辣']\n    }\n  ],\n  reviewCount: 238,\n  reviews: [\n    {\n      name: '张先生',\n      avatar: '/static/images/avatars/user1.jpg',\n      rating: 5,\n      date: '2023-06-15',\n      content: '味道非常好，包装也很精美，送货速度快，下次还会再买！',\n      specs: '规格：中份(500g) 口味：原味',\n      images: [\n        '/static/images/reviews/review1-1.jpg',\n        '/static/images/reviews/review1-2.jpg'\n      ]\n    },\n    {\n      name: '李女士',\n      avatar: '/static/images/avatars/user2.jpg',\n      rating: 4,\n      date: '2023-06-10',\n      content: '肉质很嫩，味道不错，就是有点咸，下次尝试微辣口味。',\n      specs: '规格：小份(250g) 口味：原味',\n      images: []\n    }\n  ],\n  shop: {\n    id: '101',\n    name: '磁州好味食品专营店',\n    avatar: '/static/images/shops/shop1.jpg',\n    rating: 4.8\n  }\n});\n\n// 推荐商品\nconst recommendProducts = ref([\n  {\n    id: '2',\n    name: '磁州传统手工豆腐脑',\n    price: 12.80,\n    sold: 3256,\n    image: '/static/images/products/product2.jpg'\n  },\n  {\n    id: '3',\n    name: '磁州特色糖醋里脊',\n    price: 48.00,\n    sold: 1856,\n    image: '/static/images/products/product3.jpg'\n  },\n  {\n    id: '4',\n    name: '磁州农家小炒肉',\n    price: 38.00,\n    sold: 2048,\n    image: '/static/images/products/product4.jpg'\n  }\n]);\n\n// 收藏状态\nconst isFavorite = ref(false);\n\n// 已选规格\nconst selectedSpecs = ref('');\n\n// 返回上一页\nconst navigateBack = () => {\n  uni.navigateBack();\n};\n\n// 分享商品\nconst shareProduct = () => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\n// 预览图片\nconst previewImage = (images, current) => {\n  uni.previewImage({\n    urls: images,\n    current: images[current]\n  });\n};\n\n// 查看店铺详情\nconst viewShopDetail = () => {\n  uni.navigateTo({\n    url: `/subPackages/activity-showcase/pages/shops/detail/index?id=${product.value.shop.id}`\n  });\n};\n\n// 查看商品详情\nconst viewProductDetail = (id) => {\n  uni.navigateTo({\n    url: `/subPackages/activity-showcase/pages/products/detail/index?id=${id}`\n  });\n};\n\n// 查看全部评价\nconst viewAllReviews = () => {\n  uni.navigateTo({\n    url: `/subPackages/activity-showcase/pages/products/reviews?id=${product.value.id}`\n  });\n};\n\n// 显示规格选择器\nconst showSpecsSelector = () => {\n  uni.showToast({\n    title: '正在加载规格选择器...',\n    icon: 'none'\n  });\n  // 实际项目中这里应该显示规格选择弹窗\n};\n\n// 显示配送信息\nconst showDeliveryInfo = () => {\n  uni.showModal({\n    title: '配送信息',\n    content: '本商品支持全国配送，订单满99元免运费，不满99元收取10元运费。预计3-5天送达。',\n    showCancel: false\n  });\n};\n\n// 显示服务信息\nconst showServiceInfo = () => {\n  uni.showModal({\n    title: '服务说明',\n    content: '7天无理由退货：商品签收后7天内，在不影响二次销售的情况下可申请无理由退货。\\n正品保证：所有商品均为正品，假一赔十。\\n极速退款：审核通过后24小时内退款到账。',\n    showCancel: false\n  });\n};\n\n// 切换收藏状态\nconst toggleFavorite = () => {\n  isFavorite.value = !isFavorite.value;\n  uni.showToast({\n    title: isFavorite.value ? '已收藏' : '已取消收藏',\n    icon: 'none'\n  });\n};\n\n// 联系客服\nconst contactService = () => {\n  uni.showToast({\n    title: '正在接入客服系统...',\n    icon: 'none'\n  });\n};\n\n// 导航到店铺\nconst navigateToShop = () => {\n  uni.navigateTo({\n    url: `/subPackages/activity-showcase/pages/shops/detail/index?id=${product.value.shop.id}`\n  });\n};\n\n// 加入购物车\nconst addToCart = () => {\n  if (!selectedSpecs.value) {\n    showSpecsSelector();\n    return;\n  }\n  \n  uni.showToast({\n    title: '已加入购物车',\n    icon: 'success'\n  });\n};\n\n// 立即购买\nconst buyNow = () => {\n  if (!selectedSpecs.value) {\n    showSpecsSelector();\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/subPackages/activity-showcase/pages/order/confirm?productId=${product.value.id}&specs=${encodeURIComponent(selectedSpecs.value)}`\n  });\n};\n\nonMounted(() => {\n  // 获取商品详情数据\n  // 实际项目中这里应该调用API获取数据\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.product-detail-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #F2F2F7;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  \n  .navbar-bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(to right, #FF3B69, #FF7A9E);\n  }\n  \n  .navbar-content {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    padding: 0 30rpx;\n    padding-top: var(--status-bar-height, 25px);\n    box-sizing: border-box;\n  }\n  \n  .navbar-title {\n    font-size: 36rpx;\n    font-weight: 600;\n    color: #FFFFFF;\n    letter-spacing: 0.5px;\n    position: absolute;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n  \n  .back-btn, .share-btn {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .back-btn {\n    position: absolute;\n    left: 30rpx;\n  }\n  \n  .navbar-right {\n    position: absolute;\n    right: 30rpx;\n  }\n}\n\n/* 内容区域 */\n.content-scroll {\n  flex: 1;\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\n  margin-bottom: 120rpx; /* 底部操作栏高度 */\n}\n\n/* 商品轮播图 */\n.product-swiper {\n  width: 100%;\n  height: 750rpx;\n  \n  .swiper-image {\n    width: 100%;\n    height: 100%;\n  }\n}\n\n/* 商品信息卡片 */\n.product-info-card {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .price-row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16rpx;\n    \n    .price-box {\n      display: flex;\n      align-items: baseline;\n      \n      .price-symbol {\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #FF3B69;\n      }\n      \n      .price-value {\n        font-size: 48rpx;\n        font-weight: 600;\n        color: #FF3B69;\n        margin-right: 16rpx;\n      }\n      \n      .original-price {\n        font-size: 24rpx;\n        color: #999999;\n        text-decoration: line-through;\n      }\n    }\n    \n    .sold-count {\n      font-size: 24rpx;\n      color: #999999;\n    }\n  }\n  \n  .product-name {\n    font-size: 36rpx;\n    font-weight: 600;\n    color: #333333;\n    margin-bottom: 16rpx;\n    line-height: 1.4;\n  }\n  \n  .tags-row {\n    display: flex;\n    flex-wrap: wrap;\n    \n    .tag {\n      font-size: 24rpx;\n      color: #FF3B69;\n      background-color: rgba(255, 59, 105, 0.1);\n      border-radius: 6rpx;\n      padding: 4rpx 12rpx;\n      margin-right: 16rpx;\n      margin-bottom: 10rpx;\n    }\n  }\n}\n\n/* 商家信息卡片 */\n.shop-info-card {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .shop-header {\n    display: flex;\n    align-items: center;\n    \n    .shop-avatar {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: 50%;\n      margin-right: 16rpx;\n    }\n    \n    .shop-detail {\n      flex: 1;\n      \n      .shop-name {\n        font-size: 28rpx;\n        font-weight: 500;\n        color: #333333;\n        margin-bottom: 6rpx;\n      }\n      \n      .shop-rating {\n        display: flex;\n        align-items: center;\n        \n        .star {\n          color: #CCCCCC;\n          margin-right: 4rpx;\n          \n          &.active {\n            color: #FFCC00;\n          }\n        }\n        \n        .rating-value {\n          font-size: 24rpx;\n          color: #999999;\n          margin-left: 6rpx;\n        }\n      }\n    }\n    \n    .view-shop-btn {\n      font-size: 28rpx;\n      color: #666666;\n      display: flex;\n      align-items: center;\n      padding: 10rpx 20rpx;\n      border: 1px solid #DDDDDD;\n      border-radius: 30rpx;\n    }\n  }\n}\n\n/* 规格卡片 */\n.specs-card {\n  background-color: #FFFFFF;\n  padding: 0 30rpx;\n  margin-bottom: 20rpx;\n  \n  .specs-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 30rpx 0;\n    border-bottom: 1px solid #F2F2F7;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .specs-label {\n      font-size: 28rpx;\n      color: #999999;\n    }\n    \n    .specs-value {\n      flex: 1;\n      display: flex;\n      justify-content: flex-end;\n      align-items: center;\n      font-size: 28rpx;\n      color: #333333;\n      \n      .service-tags {\n        display: flex;\n        flex-wrap: wrap;\n        justify-content: flex-end;\n        max-width: 400rpx;\n        \n        .service-tag {\n          display: flex;\n          align-items: center;\n          margin-left: 16rpx;\n          margin-bottom: 6rpx;\n          \n          svg {\n            margin-right: 4rpx;\n          }\n          \n          text {\n            font-size: 24rpx;\n            color: #666666;\n          }\n        }\n      }\n      \n      svg {\n        margin-left: 10rpx;\n      }\n    }\n  }\n}\n\n/* 商品详情卡片 */\n.detail-card {\n  background-color: #FFFFFF;\n  margin-bottom: 20rpx;\n  \n  .detail-header {\n    padding: 30rpx;\n    border-bottom: 1px solid #F2F2F7;\n    \n    .detail-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n  }\n  \n  .detail-content {\n    padding: 30rpx;\n    \n    .detail-images {\n      margin-top: 20rpx;\n      \n      .detail-image {\n        width: 100%;\n        margin-bottom: 20rpx;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n      }\n    }\n  }\n}\n\n/* 评价卡片 */\n.reviews-card {\n  background-color: #FFFFFF;\n  margin-bottom: 20rpx;\n  \n  .reviews-header {\n    padding: 30rpx;\n    border-bottom: 1px solid #F2F2F7;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    \n    .reviews-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n    \n    .view-all {\n      font-size: 28rpx;\n      color: #999999;\n      display: flex;\n      align-items: center;\n    }\n  }\n  \n  .reviews-content {\n    padding: 0 30rpx;\n    \n    .review-item {\n      padding: 30rpx 0;\n      border-bottom: 1px solid #F2F2F7;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .review-header {\n        display: flex;\n        align-items: center;\n        margin-bottom: 16rpx;\n        \n        .reviewer-avatar {\n          width: 64rpx;\n          height: 64rpx;\n          border-radius: 50%;\n          margin-right: 16rpx;\n        }\n        \n        .reviewer-info {\n          flex: 1;\n          \n          .reviewer-name {\n            font-size: 28rpx;\n            font-weight: 500;\n            color: #333333;\n            margin-bottom: 6rpx;\n          }\n          \n          .review-rating {\n            display: flex;\n            align-items: center;\n            \n            .star {\n              color: #CCCCCC;\n              margin-right: 4rpx;\n              \n              &.active {\n                color: #FFCC00;\n              }\n            }\n            \n            .review-date {\n              font-size: 24rpx;\n              color: #999999;\n              margin-left: 10rpx;\n            }\n          }\n        }\n      }\n      \n      .review-content {\n        font-size: 28rpx;\n        color: #333333;\n        line-height: 1.5;\n        margin-bottom: 16rpx;\n      }\n      \n      .review-images {\n        display: flex;\n        flex-wrap: wrap;\n        margin-bottom: 16rpx;\n        \n        .review-image {\n          width: 160rpx;\n          height: 160rpx;\n          margin-right: 10rpx;\n          margin-bottom: 10rpx;\n          border-radius: 8rpx;\n        }\n      }\n      \n      .specs-info {\n        font-size: 24rpx;\n        color: #999999;\n      }\n    }\n  }\n}\n\n/* 推荐商品卡片 */\n.recommend-card {\n  background-color: #FFFFFF;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  \n  .recommend-header {\n    margin-bottom: 20rpx;\n    \n    .recommend-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333333;\n    }\n  }\n  \n  .recommend-list {\n    display: flex;\n    flex-wrap: nowrap;\n    overflow-x: auto;\n    \n    .recommend-item {\n      width: 240rpx;\n      margin-right: 20rpx;\n      flex-shrink: 0;\n      \n      &:last-child {\n        margin-right: 0;\n      }\n      \n      .recommend-image {\n        width: 240rpx;\n        height: 240rpx;\n        border-radius: 16rpx;\n        margin-bottom: 10rpx;\n      }\n      \n      .recommend-info {\n        .recommend-name {\n          font-size: 28rpx;\n          color: #333333;\n          margin-bottom: 6rpx;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n        \n        .recommend-price-row {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          \n          .recommend-price {\n            font-size: 28rpx;\n            font-weight: 600;\n            color: #FF3B69;\n          }\n          \n          .recommend-sold {\n            font-size: 24rpx;\n            color: #999999;\n          }\n        }\n      }\n    }\n  }\n}\n\n/* 底部操作栏 */\n.footer-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 120rpx;\n  background-color: #FFFFFF;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 30rpx;\n  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\n  z-index: 100;\n  \n  .footer-left {\n    display: flex;\n    \n    .action-btn {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      margin-right: 40rpx;\n      \n      .icon {\n        color: #999999;\n        margin-bottom: 6rpx;\n        \n        &.active {\n          color: #FF3B69;\n        }\n      }\n      \n      text {\n        font-size: 24rpx;\n        color: #999999;\n      }\n    }\n  }\n  \n  .footer-right {\n    display: flex;\n    \n    .cart-btn, .buy-btn {\n      height: 80rpx;\n      line-height: 80rpx;\n      border-radius: 40rpx;\n      padding: 0 40rpx;\n      font-size: 32rpx;\n      font-weight: 500;\n      border: none;\n    }\n    \n    .cart-btn {\n      background-color: #FFF0F5;\n      color: #FF3B69;\n      margin-right: 20rpx;\n    }\n    \n    .buy-btn {\n      background: linear-gradient(to right, #FF3B69, #FF7A9E);\n      color: #FFFFFF;\n    }\n  }\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/shops/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted"], "mappings": ";;;;;;;;;;;AA4OA,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,eAAe;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM,QAAQ;AAAA,MAC7B,UAAU;AAAA,MACV,UAAU,CAAC,WAAW,QAAQ,MAAM;AAAA,MACpC,aAAa;AAAA,MACb,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,OAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,SAAS,CAAC,YAAY,YAAY,WAAW;AAAA,QAC9C;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,SAAS,CAAC,MAAM,MAAM,IAAI;AAAA,QAC3B;AAAA,MACF;AAAA,MACD,aAAa;AAAA,MACb,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,UACD;AAAA,QACF;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ,CAAE;AAAA,QACX;AAAA,MACF;AAAA,MACD,MAAM;AAAA,QACJ,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AAGD,UAAM,oBAAoBA,cAAAA,IAAI;AAAA,MAC5B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAG5B,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAG5B,UAAM,eAAe,MAAM;AACzBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,MAC9C,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,QAAQ,YAAY;AACxCA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,SAAS,OAAO,OAAO;AAAA,MAC3B,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,8DAA8D,QAAQ,MAAM,KAAK,EAAE;AAAA,MAC5F,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,OAAO;AAChCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iEAAiE,EAAE;AAAA,MAC5E,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4DAA4D,QAAQ,MAAM,EAAE;AAAA,MACrF,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IAEH;AAGA,UAAM,mBAAmB,MAAM;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,iBAAW,QAAQ,CAAC,WAAW;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,WAAW,QAAQ,QAAQ;AAAA,QAClC,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,8DAA8D,QAAQ,MAAM,KAAK,EAAE;AAAA,MAC5F,CAAG;AAAA,IACH;AAGA,UAAM,YAAY,MAAM;AACtB,UAAI,CAAC,cAAc,OAAO;AACxB;AACA;AAAA,MACD;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnB,UAAI,CAAC,cAAc,OAAO;AACxB;AACA;AAAA,MACD;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gEAAgE,QAAQ,MAAM,EAAE,UAAU,mBAAmB,cAAc,KAAK,CAAC;AAAA,MAC1I,CAAG;AAAA,IACH;AAEAC,kBAAAA,UAAU,MAAM;AAAA,IAGhB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjcD,GAAG,WAAW,eAAe;"}