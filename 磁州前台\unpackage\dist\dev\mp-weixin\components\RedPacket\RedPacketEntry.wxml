<view class="data-v-851f2d80"><view class="red-packet-card data-v-851f2d80" bindtap="{{e}}"><image class="icon data-v-851f2d80" src="{{a}}"/><view class="info data-v-851f2d80"><view class="title data-v-851f2d80">任务红包</view><view class="desc data-v-851f2d80">设置任务红包，用户转发/助力后可抢</view></view><view class="{{['status', 'data-v-851f2d80', c && 'set']}}">{{b}}</view><image class="arrow data-v-851f2d80" src="{{d}}"/></view><uni-popup wx:if="{{p}}" class="data-v-851f2d80" u-s="{{['d']}}" u-i="851f2d80-0" bind:__l="__l" bindupdateModelValue="{{o}}" u-p="{{p}}"><view class="red-packet-setup-popup data-v-851f2d80"><view class="popup-title data-v-851f2d80">设置任务红包</view><view class="form-row data-v-851f2d80"><text class="label data-v-851f2d80">红包总金额</text><input class="data-v-851f2d80" type="number" placeholder="请输入金额" value="{{f}}" bindinput="{{g}}"/><text class="unit data-v-851f2d80">元</text></view><view class="form-row data-v-851f2d80"><text class="label data-v-851f2d80">红包个数</text><input class="data-v-851f2d80" type="number" placeholder="请输入个数" value="{{h}}" bindinput="{{i}}"/><text class="unit data-v-851f2d80">个</text></view><view class="form-row data-v-851f2d80"><text class="label data-v-851f2d80">任务要求</text><picker class="data-v-851f2d80" range="{{k}}" value="{{l}}" bindchange="{{m}}"><view class="picker-value data-v-851f2d80">{{j}}</view></picker></view><button class="primary-btn data-v-851f2d80" bindtap="{{n}}">确定</button></view></uni-popup></view>