"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      verificationCode: "",
      showVerificationPopup: false,
      verificationData: {
        type: "",
        name: "",
        user: "",
        code: "",
        expiry: ""
      },
      historyRecords: [
        {
          typeText: "拼团",
          typeClass: "type-group",
          title: "双人下午茶套餐拼团",
          code: "GP20230618001",
          time: "今天 14:30",
          status: "已核销",
          statusClass: "status-success"
        },
        {
          typeText: "优惠券",
          typeClass: "type-coupon",
          title: "新店开业满100减20券",
          code: "CP20230618002",
          time: "今天 11:15",
          status: "已核销",
          statusClass: "status-success"
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "手动核销帮助",
        icon: "none"
      });
    },
    onCodeInput(e) {
      this.verificationCode = e.detail.value;
    },
    clearCode() {
      this.verificationCode = "";
    },
    verifyCode() {
      if (!this.verificationCode) {
        common_vendor.index.showToast({
          title: "请输入核销码",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "验证中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        this.verificationData = {
          type: "拼团活动",
          name: "双人下午茶套餐拼团",
          user: "张三 (138****8888)",
          code: this.verificationCode,
          expiry: "2023-06-25 23:59:59"
        };
        this.showVerificationPopup = true;
      }, 1e3);
    },
    viewAllRecords() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/verification/records"
      });
    },
    showRecordDetail(record) {
      common_vendor.index.showToast({
        title: "查看记录: " + record.code,
        icon: "none"
      });
    },
    closePopup() {
      this.showVerificationPopup = false;
    },
    confirmVerification() {
      common_vendor.index.showLoading({
        title: "核销中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "核销成功",
          icon: "success"
        });
        this.closePopup();
        this.verificationCode = "";
        this.historyRecords.unshift({
          typeText: "拼团",
          typeClass: "type-group",
          title: this.verificationData.name,
          code: this.verificationData.code,
          time: "刚刚",
          status: "已核销",
          statusClass: "status-success"
        });
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.o([($event) => $data.verificationCode = $event.detail.value, (...args) => $options.onCodeInput && $options.onCodeInput(...args)]),
    d: common_vendor.o((...args) => $options.verifyCode && $options.verifyCode(...args)),
    e: $data.verificationCode,
    f: $data.verificationCode
  }, $data.verificationCode ? {
    g: common_vendor.o((...args) => $options.clearCode && $options.clearCode(...args))
  } : {}, {
    h: $data.verificationCode.length > 0 ? 1 : "",
    i: common_vendor.o((...args) => $options.verifyCode && $options.verifyCode(...args)),
    j: common_vendor.o((...args) => $options.viewAllRecords && $options.viewAllRecords(...args)),
    k: $data.historyRecords.length === 0
  }, $data.historyRecords.length === 0 ? {} : {
    l: common_vendor.f($data.historyRecords, (record, index, i0) => {
      return {
        a: common_vendor.t(record.typeText),
        b: common_vendor.n(record.typeClass),
        c: common_vendor.t(record.title),
        d: common_vendor.t(record.code),
        e: common_vendor.t(record.time),
        f: common_vendor.t(record.status),
        g: common_vendor.n(record.statusClass),
        h: index,
        i: common_vendor.o(($event) => $options.showRecordDetail(record), index)
      };
    })
  }, {
    m: $data.showVerificationPopup
  }, $data.showVerificationPopup ? {
    n: common_vendor.o((...args) => $options.closePopup && $options.closePopup(...args)),
    o: common_vendor.t($data.verificationData.type),
    p: common_vendor.t($data.verificationData.name),
    q: common_vendor.t($data.verificationData.user),
    r: common_vendor.t($data.verificationData.code),
    s: common_vendor.t($data.verificationData.expiry),
    t: common_vendor.o((...args) => $options.closePopup && $options.closePopup(...args)),
    v: common_vendor.o((...args) => $options.confirmVerification && $options.confirmVerification(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/verification/manual.js.map
