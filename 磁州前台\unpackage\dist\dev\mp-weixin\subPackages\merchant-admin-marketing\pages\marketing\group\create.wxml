<view class="group-create-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">创建拼团活动</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><progress-steps wx:if="{{c}}" u-i="295e49c0-0" bind:__l="__l" u-p="{{c}}"></progress-steps><scroll-view scroll-y class="form-scroll-view" enable-back-to-top scroll-into-view="{{cc}}"><view wx:if="{{d}}" class="form-section" id="step1"><section-header wx:if="{{e}}" u-i="295e49c0-1" bind:__l="__l" u-p="{{e}}"></section-header><view class="selection-options"><view class="{{['selection-option', g && 'active']}}" bindtap="{{h}}"><view class="option-radio"><view wx:if="{{f}}" class="radio-inner"></view></view><view class="option-content"><text class="option-title">选择现有商品</text><text class="option-desc">从已有商品库中选择商品添加到拼团</text></view></view><view class="{{['selection-option', j && 'active']}}" bindtap="{{k}}"><view class="option-radio"><view wx:if="{{i}}" class="radio-inner"></view></view><view class="option-content"><text class="option-title">新建拼团商品</text><text class="option-desc">创建新商品并添加到拼团活动中</text></view></view></view><view wx:if="{{l}}" class="product-selection"><view class="search-bar"><svg-icon wx:if="{{m}}" u-i="295e49c0-2" bind:__l="__l" u-p="{{m}}"></svg-icon><input class="search-input" placeholder="搜索商品名称" bindinput="{{n}}" value="{{o}}"/></view><view class="filter-options"><view class="filter-item" bindtap="{{q}}"><text>商品类别</text><svg-icon wx:if="{{p}}" u-i="295e49c0-3" bind:__l="__l" u-p="{{p}}"></svg-icon></view><view class="filter-item" bindtap="{{s}}"><text>价格区间</text><svg-icon wx:if="{{r}}" u-i="295e49c0-4" bind:__l="__l" u-p="{{r}}"></svg-icon></view><view class="filter-item" bindtap="{{v}}"><text>库存状态</text><svg-icon wx:if="{{t}}" u-i="295e49c0-5" bind:__l="__l" u-p="{{t}}"></svg-icon></view></view><view class="product-list"><view wx:for="{{w}}" wx:for-item="product" wx:key="i" class="product-item" bindtap="{{product.j}}"><view class="{{['checkbox', product.b && 'checked']}}"><view wx:if="{{product.a}}" class="checkbox-inner"></view></view><image class="product-img" src="{{product.c}}" mode="aspectFill"></image><view class="product-info"><text class="product-name">{{product.d}}</text><view class="product-price"><text class="price-current">¥{{product.e}}</text><text wx:if="{{product.f}}" class="price-original">¥{{product.g}}</text></view><text class="product-stock">库存: {{product.h}}件</text></view></view></view><view wx:if="{{x}}" class="selected-products"><view class="selected-header"><text class="selected-title">已选商品({{y}})</text><text class="clear-all" bindtap="{{z}}">清空</text></view><view class="selected-list"><view wx:for="{{A}}" wx:for-item="product" wx:key="e" class="selected-item"><image class="selected-img" src="{{product.a}}" mode="aspectFill"></image><view class="selected-info"><text class="selected-name">{{product.b}}</text><text class="selected-price">¥{{product.c}}</text></view><view class="remove-btn" catchtap="{{product.d}}">×</view></view></view></view></view><view wx:if="{{B}}" class="create-product"><icon-button wx:if="{{D}}" bindclick="{{C}}" u-i="295e49c0-6" bind:__l="__l" u-p="{{D}}"></icon-button><text class="create-product-tip">创建新商品后可自动关联到拼团活动</text></view></view><view wx:if="{{E}}" class="form-section" id="step2"><section-header wx:if="{{F}}" u-i="295e49c0-7" bind:__l="__l" u-p="{{F}}"></section-header><view class="form-content"><form-group wx:if="{{J}}" u-s="{{['d']}}" u-i="295e49c0-8" bind:__l="__l" u-p="{{J}}"><input class="form-input" type="text" placeholder="请输入拼团活动名称" maxlength="30" value="{{G}}" bindinput="{{H}}"/><text class="input-counter">{{I}}/30</text></form-group><form-group wx:if="{{Q}}" u-s="{{['d']}}" u-i="295e49c0-9" bind:__l="__l" u-p="{{Q}}"><view class="radio-group"><view class="{{['radio-item', L && 'active']}}" bindtap="{{M}}"><view class="radio-dot"><view wx:if="{{K}}" class="radio-dot-inner"></view></view><text class="radio-label">普通拼团</text></view><view class="{{['radio-item', O && 'active']}}" bindtap="{{P}}"><view class="radio-dot"><view wx:if="{{N}}" class="radio-dot-inner"></view></view><text class="radio-label">套餐拼团</text></view></view></form-group><view wx:if="{{R}}" class="package-settings"><form-group wx:if="{{V}}" u-s="{{['d']}}" u-i="295e49c0-10" bind:__l="__l" u-p="{{V}}"><input class="form-input" type="text" placeholder="如：四菜一汤家庭套餐" maxlength="30" value="{{S}}" bindinput="{{T}}"/><text class="input-counter">{{U}}/30</text></form-group><form-group wx:if="{{Z}}" u-s="{{['d']}}" u-i="295e49c0-11" bind:__l="__l" u-p="{{Z}}"><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入套餐描述信息" maxlength="100" value="{{W}}" bindinput="{{X}}"></textarea></block><text class="input-counter">{{Y}}/100</text></form-group><form-group wx:if="{{ad}}" u-s="{{['d']}}" u-i="295e49c0-12" bind:__l="__l" u-p="{{ad}}"><view class="package-items"><view wx:for="{{aa}}" wx:for-item="item" wx:key="k" class="package-item"><view class="package-item-header"><text class="package-item-title">商品 {{item.a}}</text><view wx:if="{{ab}}" class="package-item-remove" bindtap="{{item.b}}">×</view></view><view class="package-item-content"><view class="package-item-row"><text class="package-item-label">商品名称:</text><input class="package-item-input" placeholder="请输入商品名称" value="{{item.c}}" bindinput="{{item.d}}"/></view><view class="package-item-row"><text class="package-item-label">原价(元):</text><input class="package-item-input" type="digit" placeholder="0.00" value="{{item.e}}" bindinput="{{item.f}}"/></view><view class="package-item-row"><text class="package-item-label">数量:</text><view class="number-picker small"><view class="number-btn minus" bindtap="{{item.g}}">-</view><input class="number-input" type="number" value="{{item.h}}" bindinput="{{item.i}}"/><view class="number-btn plus" bindtap="{{item.j}}">+</view></view></view></view></view><view class="add-package-item" bindtap="{{ac}}"><view class="add-icon">+</view><text class="add-text">添加商品</text></view></view></form-group></view><form-group wx:if="{{ag}}" u-s="{{['d']}}" u-i="295e49c0-13" bind:__l="__l" u-p="{{ag}}"><view class="price-input-wrapper"><text class="price-symbol">¥</text><input class="form-input price-input" type="digit" placeholder="请输入商品市场价" value="{{ae}}" bindinput="{{af}}"/></view></form-group><form-group wx:if="{{aj}}" u-s="{{['d']}}" u-i="295e49c0-14" bind:__l="__l" u-p="{{aj}}"><view class="price-input-wrapper"><text class="price-symbol">¥</text><input class="form-input price-input" type="digit" placeholder="请输入商品日常价" value="{{ah}}" bindinput="{{ai}}"/></view></form-group><form-group wx:if="{{am}}" u-s="{{['d']}}" u-i="295e49c0-15" bind:__l="__l" u-p="{{am}}"><view class="price-input-wrapper"><text class="price-symbol">¥</text><input class="form-input price-input" type="digit" placeholder="请输入拼团价格" value="{{ak}}" bindinput="{{al}}"/></view></form-group><form-group wx:if="{{ap}}" u-s="{{['d']}}" u-i="295e49c0-16" bind:__l="__l" u-p="{{ap}}"><view class="switch-container"><switch checked="{{an}}" bindchange="{{ao}}" color="#9040FF"/></view></form-group><form-group wx:if="{{av}}" u-s="{{['d']}}" u-i="295e49c0-17" bind:__l="__l" u-p="{{av}}"><view class="number-picker"><view class="number-btn minus" bindtap="{{aq}}">-</view><input class="number-input" type="number" value="{{ar}}" bindinput="{{as}}"/><view class="number-btn plus" bindtap="{{at}}">+</view></view></form-group><form-group wx:if="{{aA}}" u-s="{{['d']}}" u-i="295e49c0-18" bind:__l="__l" u-p="{{aA}}"><view class="number-picker"><view class="number-btn minus" bindtap="{{aw}}">-</view><input class="number-input" type="number" value="{{ax}}" bindinput="{{ay}}"/><view class="number-btn plus" bindtap="{{az}}">+</view><text class="unit-text">件</text></view></form-group><form-group wx:if="{{bd}}" u-s="{{['d']}}" u-i="295e49c0-19" bind:__l="__l" u-p="{{bd}}"><view class="radio-group"><view class="{{['radio-item', aC && 'active']}}" bindtap="{{aD}}"><view class="radio-dot"><view wx:if="{{aB}}" class="radio-dot-inner"></view></view><text class="radio-label">固定时间段</text></view><view class="{{['radio-item', aF && 'active']}}" bindtap="{{aG}}"><view class="radio-dot"><view wx:if="{{aE}}" class="radio-dot-inner"></view></view><text class="radio-label">动态时间段</text></view></view><view wx:if="{{aH}}" class="datetime-picker"><picker mode="date" value="{{aK}}" bindchange="{{aL}}" class="picker-component"><view class="datetime-field"><text wx:if="{{aI}}" class="datetime-placeholder">开始日期</text><text wx:else class="datetime-value">{{aJ}}</text><view class="datetime-icon"></view></view></picker><picker mode="time" value="{{aO}}" bindchange="{{aP}}" class="picker-component"><view class="datetime-field"><text wx:if="{{aM}}" class="datetime-placeholder">开始时间</text><text wx:else class="datetime-value">{{aN}}</text><view class="datetime-icon"></view></view></picker><text class="datetime-separator">至</text><picker mode="date" value="{{aS}}" bindchange="{{aT}}" class="picker-component"><view class="datetime-field"><text wx:if="{{aQ}}" class="datetime-placeholder">结束日期</text><text wx:else class="datetime-value">{{aR}}</text><view class="datetime-icon"></view></view></picker><picker mode="time" value="{{aW}}" bindchange="{{aX}}" class="picker-component"><view class="datetime-field"><text wx:if="{{aU}}" class="datetime-placeholder">结束时间</text><text wx:else class="datetime-value">{{aV}}</text><view class="datetime-icon"></view></view></picker></view><view wx:if="{{aY}}" class="dynamic-days"><view class="number-picker"><view class="number-btn minus" bindtap="{{aZ}}">-</view><input class="number-input" type="number" value="{{ba}}" bindinput="{{bb}}"/><view class="number-btn plus" bindtap="{{bc}}">+</view><text class="unit-text">天</text></view><text class="form-tip">拼团创建成功后，活动自动生效并持续指定天数</text></view></form-group><form-group wx:if="{{bi}}" u-s="{{['d']}}" u-i="295e49c0-20" bind:__l="__l" u-p="{{bi}}"><view class="number-picker"><view class="number-btn minus" bindtap="{{be}}">-</view><input class="number-input" type="number" value="{{bf}}" bindinput="{{bg}}"/><view class="number-btn plus" bindtap="{{bh}}">+</view></view></form-group><form-group wx:if="{{bp}}" u-s="{{['d']}}" u-i="295e49c0-21" bind:__l="__l" u-p="{{bp}}"><view class="radio-group"><view class="{{['radio-item', bk && 'active']}}" bindtap="{{bl}}"><view class="radio-dot"><view wx:if="{{bj}}" class="radio-dot-inner"></view></view><text class="radio-label">到店支付</text></view><view class="{{['radio-item', bn && 'active']}}" bindtap="{{bo}}"><view class="radio-dot"><view wx:if="{{bm}}" class="radio-dot-inner"></view></view><text class="radio-label">在线支付</text></view></view></form-group><form-group wx:if="{{bx}}" u-s="{{['d']}}" u-i="295e49c0-22" bind:__l="__l" u-p="{{bx}}"><view class="radio-group"><view class="{{['radio-item', br && 'active']}}" bindtap="{{bs}}"><view class="radio-dot"><view wx:if="{{bq}}" class="radio-dot-inner"></view></view><text class="radio-label">到店核销</text></view><view class="{{['radio-item', bv && 'active']}}" bindtap="{{bw}}"><view class="radio-dot"><view wx:if="{{bt}}" class="radio-dot-inner"></view></view><text class="radio-label">在线核销</text></view></view></form-group><form-group wx:if="{{bA}}" u-s="{{['d']}}" u-i="295e49c0-23" bind:__l="__l" u-p="{{bA}}"><view class="switch-container"><switch checked="{{by}}" bindchange="{{bz}}" color="#9040FF"/></view></form-group><form-group wx:if="{{bF}}" u-s="{{['d']}}" u-i="295e49c0-24" bind:__l="__l" u-p="{{bF}}"><view class="number-picker"><view class="number-btn minus" bindtap="{{bB}}">-</view><input class="number-input" type="number" value="{{bC}}" bindinput="{{bD}}"/><view class="number-btn plus" bindtap="{{bE}}">+</view><text class="unit-text">天</text></view></form-group><form-group wx:if="{{bI}}" u-s="{{['d']}}" u-i="295e49c0-25" bind:__l="__l" u-p="{{bI}}"><view class="switch-container"><switch checked="{{bG}}" bindchange="{{bH}}" color="#9040FF"/></view></form-group><form-group wx:if="{{bJ}}" u-s="{{['d']}}" u-i="295e49c0-26" bind:__l="__l" u-p="{{bM}}"><distribution-setting wx:if="{{bL}}" bindupdate="{{bK}}" u-i="295e49c0-27,295e49c0-26" bind:__l="__l" u-p="{{bL}}"/></form-group></view></view><view wx:if="{{bN}}" class="form-section" id="step3"><section-header wx:if="{{bO}}" u-i="295e49c0-28" bind:__l="__l" u-p="{{bO}}"></section-header><view class="form-content"><view class="preview-card"><view class="preview-header"><text class="preview-title">{{bP}}</text><text class="preview-dates">{{bQ}} 至 {{bR}}</text></view><view class="preview-content"><view class="preview-products"><view wx:for="{{bS}}" wx:for-item="product" wx:key="e" class="preview-product-item"><image class="product-image" src="{{product.a}}" mode="aspectFill"></image><view class="product-info"><text class="product-name">{{product.b}}</text><view class="product-price"><text class="group-price">¥{{product.c}}</text><text class="original-price">¥{{product.d}}</text></view></view></view></view><view class="group-rules"><view class="rule-item"><text class="rule-label">拼团人数:</text><text class="rule-value">{{bT}}人</text></view><view class="rule-item"><text class="rule-label">拼团时限:</text><text class="rule-value">{{bU}}小时</text></view><view class="rule-item"><text class="rule-label">单人限购:</text><text class="rule-value">{{bV}}</text></view><view class="rule-item"><text class="rule-label">活动库存:</text><text class="rule-value">{{bW}}</text></view></view></view></view><form-group wx:if="{{bZ}}" u-s="{{['d']}}" u-i="295e49c0-29" bind:__l="__l" u-p="{{bZ}}"><marketing-promotion-actions wx:if="{{bY}}" bindactionCompleted="{{bX}}" u-i="295e49c0-30,295e49c0-29" bind:__l="__l" u-p="{{bY}}"/></form-group><view class="form-actions"><button class="btn-secondary" bindtap="{{ca}}">上一步</button><button class="btn-primary" bindtap="{{cb}}">创建拼团</button></view></view></view></scroll-view><view class="footer-buttons"><button wx:if="{{cd}}" class="btn btn-secondary" bindtap="{{ce}}">上一步</button><button wx:if="{{cf}}" class="btn btn-primary" bindtap="{{cg}}">下一步</button><button wx:if="{{ch}}" class="btn btn-primary" bindtap="{{ci}}">创建活动</button></view></view>