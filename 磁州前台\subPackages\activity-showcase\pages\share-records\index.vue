<template>
  <view class="share-records-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" mode="aspectFit" class="back-icon"></image>
        </view>
        <view class="navbar-title">分享记录</view>
        <view class="navbar-right">
          <view class="filter-btn" @click="showFilter">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 分享类型标签栏 -->
    <view class="share-tabs">
      <view 
        v-for="(tab, index) in shareTabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <view class="tab-indicator" v-if="currentTab === index" :style="{
          background: 'linear-gradient(90deg, #5856D6 0%, #A09CFF 100%)'
        }"></view>
      </view>
    </view>

    <!-- 分享统计卡片 -->
    <view class="stats-card">
      <view class="stat-item">
        <text class="stat-value">{{ shareStats.totalShares }}</text>
        <text class="stat-label">总分享</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-value">{{ shareStats.totalViews }}</text>
        <text class="stat-label">总浏览</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-value">{{ shareStats.totalRegistrations }}</text>
        <text class="stat-label">报名转化</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-value">{{ shareStats.totalCommission }}</text>
        <text class="stat-label">累计佣金</text>
      </view>
    </view>

    <!-- 分享记录列表区域 -->
    <swiper class="records-swiper" :current="currentTab" @change="onSwiperChange">
      <swiper-item v-for="(tab, tabIndex) in shareTabs" :key="tabIndex">
        <scroll-view 
          class="tab-content" 
          scroll-y 
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          @scrolltolower="loadMore"
        >
          <view class="records-list">
            <view 
              v-for="record in getRecordsByType(tab.type)" 
              :key="record.id"
              class="record-card"
              @click="viewRecordDetail(record)"
            >
              <!-- 分享内容预览 -->
              <view class="record-preview">
                <image :src="record.image" class="record-image" mode="aspectFill"></image>
                <view class="record-type-tag" :style="{
                  background: getTypeTagBackground(record.contentType)
                }">
                  {{ getTypeTagText(record.contentType) }}
                </view>
              </view>
              
              <!-- 分享记录信息 -->
              <view class="record-info">
                <text class="record-title">{{ record.title }}</text>
                
                <view class="record-meta">
                  <view class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                      <line x1="16" y1="2" x2="16" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                      <line x1="8" y1="2" x2="8" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                      <line x1="3" y1="10" x2="21" y2="10" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                    </svg>
                    <text class="meta-text">{{ record.date }}</text>
                  </view>
                  
                  <view class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                      <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                      <circle cx="9" cy="7" r="4" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                      <path d="M23 21v-2a4 4 0 00-3-3.87m-4-12a4 4 0 010 7.75" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                    <text class="meta-text">{{ record.platform }}</text>
                  </view>
                </view>
                
                <view class="record-stats">
                  <view class="stat-row">
                    <view class="mini-stat">
                      <svg class="stat-icon" viewBox="0 0 24 24" width="16" height="16">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                        <circle cx="12" cy="12" r="3" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                      </svg>
                      <text class="stat-text">{{ record.views }} 浏览</text>
                    </view>
                    
                    <view class="mini-stat">
                      <svg class="stat-icon" viewBox="0 0 24 24" width="16" height="16">
                        <path d="M20.84 4.61a5.5 5.5 0 00-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 00-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 000-7.78z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg>
                      <text class="stat-text">{{ record.likes }} 点赞</text>
                    </view>
                    
                    <view class="mini-stat">
                      <svg class="stat-icon" viewBox="0 0 24 24" width="16" height="16">
                        <path d="M21 11.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg>
                      <text class="stat-text">{{ record.comments }} 评论</text>
                    </view>
                  </view>
                  
                  <view class="stat-row">
                    <view class="mini-stat highlight">
                      <svg class="stat-icon" viewBox="0 0 24 24" width="16" height="16">
                        <path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" stroke="#5856D6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                        <circle cx="8.5" cy="7" r="4" stroke="#5856D6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                        <line x1="20" y1="8" x2="20" y2="14" stroke="#5856D6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                        <line x1="23" y1="11" x2="17" y2="11" stroke="#5856D6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                      </svg>
                      <text class="stat-text">{{ record.registrations }} 报名</text>
                    </view>
                    
                    <view class="mini-stat highlight">
                      <svg class="stat-icon" viewBox="0 0 24 24" width="16" height="16">
                        <line x1="12" y1="1" x2="12" y2="23" stroke="#5856D6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                        <path d="M17 5H9.5a3.5 3.5 0 000 7h5a3.5 3.5 0 010 7H6" stroke="#5856D6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg>
                      <text class="stat-text">¥{{ record.commission.toFixed(2) }} 佣金</text>
                    </view>
                  </view>
                </view>
                
                <view class="record-actions">
                  <view 
                    class="action-btn"
                    @click.stop="shareAgain(record)"
                  >
                    <svg class="action-icon" viewBox="0 0 24 24" width="16" height="16">
                      <circle cx="18" cy="5" r="3" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                      <circle cx="6" cy="12" r="3" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                      <circle cx="18" cy="19" r="3" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                      <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                      <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                    </svg>
                    <text>再次分享</text>
                  </view>
                  
                  <view 
                    class="action-btn primary"
                    :style="{
                      background: 'linear-gradient(135deg, #5856D6 0%, #A09CFF 100%)',
                      color: '#FFFFFF'
                    }"
                    @click.stop="viewAnalytics(record)"
                  >
                    <svg class="action-icon" viewBox="0 0 24 24" width="16" height="16">
                      <line x1="18" y1="20" x2="18" y2="10" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                      <line x1="12" y1="20" x2="12" y2="4" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                      <line x1="6" y1="20" x2="6" y2="14" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                    </svg>
                    <text>查看数据</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-if="getRecordsByType(tab.type).length === 0">
            <image class="empty-image" :src="tab.emptyImage || '/static/images/empty-shares.png'"></image>
            <text class="empty-text">{{ tab.emptyText || '暂无分享记录' }}</text>
            <view class="action-btn" @click="navigateTo('/subPackages/activity-showcase/pages/list/index')" :style="{
              background: 'linear-gradient(135deg, #5856D6 0%, #A09CFF 100%)',
              borderRadius: '35px',
              boxShadow: '0 5px 15px rgba(88,86,214,0.3)'
            }">
              <text>去分享活动</text>
            </view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-popup">
        <view class="filter-header">
          <text class="filter-title">筛选</text>
          <view class="filter-close" @click="closeFilter">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <line x1="18" y1="6" x2="6" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              <line x1="6" y1="6" x2="18" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            </svg>
          </view>
        </view>
        
        <view class="filter-content">
          <!-- 时间筛选 -->
          <view class="filter-section">
            <text class="section-title">时间范围</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in timeOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedTimeOption === index }"
                @click="selectTimeOption(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
          
          <!-- 分享平台筛选 -->
          <view class="filter-section">
            <text class="section-title">分享平台</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in platformOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedPlatformOptions.includes(index) }"
                @click="togglePlatformOption(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
          
          <!-- 内容类型筛选 -->
          <view class="filter-section">
            <text class="section-title">内容类型</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in contentTypeOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedContentTypeOptions.includes(index) }"
                @click="toggleContentTypeOption(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="filter-footer">
          <view class="filter-reset" @click="resetFilter">
            <text>重置</text>
          </view>
          <view class="filter-apply" @click="applyFilter">
            <text>确定</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 页面状态
const currentTab = ref(0);
const isRefreshing = ref(false);
const recordsList = ref([]);
const filterPopup = ref(null);

// 分享统计数据
const shareStats = ref({
  totalShares: 56,
  totalViews: 2348,
  totalRegistrations: 32,
  totalCommission: '¥328.50'
});

// 筛选选项
const selectedTimeOption = ref(0);
const selectedPlatformOptions = ref([0]);
const selectedContentTypeOptions = ref([0]);

// 筛选选项数据
const timeOptions = [
  { label: '全部时间', value: 'all' },
  { label: '最近一周', value: 'week' },
  { label: '最近一月', value: 'month' },
  { label: '最近三月', value: 'three_months' },
  { label: '自定义', value: 'custom' }
];

const platformOptions = [
  { label: '全部平台', value: 'all' },
  { label: '微信', value: 'wechat' },
  { label: '朋友圈', value: 'moments' },
  { label: '微博', value: 'weibo' },
  { label: '抖音', value: 'douyin' },
  { label: '其他', value: 'other' }
];

const contentTypeOptions = [
  { label: '全部类型', value: 'all' },
  { label: '活动', value: 'activity' },
  { label: '产品', value: 'product' },
  { label: '优惠券', value: 'coupon' },
  { label: '海报', value: 'poster' }
];

// 分享标签页
const shareTabs = [
  { name: '全部', type: 'all', emptyText: '暂无分享记录', emptyImage: '/static/images/empty-shares.png' },
  { name: '活动', type: 'activity', emptyText: '暂无活动分享', emptyImage: '/static/images/empty-activity-shares.png' },
  { name: '产品', type: 'product', emptyText: '暂无产品分享', emptyImage: '/static/images/empty-product-shares.png' },
  { name: '优惠券', type: 'coupon', emptyText: '暂无优惠券分享', emptyImage: '/static/images/empty-coupon-shares.png' }
];

// 模拟数据
const mockRecords = [
  {
    id: '1001',
    title: '磁州文化节',
    contentType: 'activity',
    platform: '微信群',
    date: '2024-05-15',
    image: '/static/demo/activity1.jpg',
    views: 245,
    likes: 32,
    comments: 18,
    registrations: 12,
    commission: 120.00
  },
  {
    id: '1002',
    title: '亲子户外拓展活动',
    contentType: 'activity',
    platform: '朋友圈',
    date: '2024-05-12',
    image: '/static/demo/activity2.jpg',
    views: 189,
    likes: 24,
    comments: 9,
    registrations: 8,
    commission: 80.00
  },
  {
    id: '1003',
    title: '磁州特产礼盒',
    contentType: 'product',
    platform: '微信群',
    date: '2024-05-10',
    image: '/static/demo/product1.jpg',
    views: 156,
    likes: 18,
    comments: 7,
    registrations: 5,
    commission: 50.00
  },
  {
    id: '1004',
    title: '新人专享优惠券',
    contentType: 'coupon',
    platform: '抖音',
    date: '2024-05-08',
    image: '/static/demo/coupon1.jpg',
    views: 324,
    likes: 45,
    comments: 12,
    registrations: 7,
    commission: 35.00
  },
  {
    id: '1005',
    title: '磁州文化体验套餐',
    contentType: 'product',
    platform: '微博',
    date: '2024-05-05',
    image: '/static/demo/product2.jpg',
    views: 132,
    likes: 16,
    comments: 5,
    registrations: 0,
    commission: 0.00
  }
];

// 生命周期
onMounted(() => {
  loadRecords();
});

// 方法
const loadRecords = () => {
  // 模拟加载数据
  recordsList.value = mockRecords;
};

const getRecordsByType = (type) => {
  if (type === 'all') {
    return recordsList.value;
  }
  return recordsList.value.filter(record => record.contentType === type);
};

const switchTab = (index) => {
  currentTab.value = index;
};

const onSwiperChange = (e) => {
  currentTab.value = e.detail.current;
};

const onRefresh = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    loadRecords();
    isRefreshing.value = false;
  }, 1000);
};

const loadMore = () => {
  // 模拟加载更多
  console.log('加载更多记录');
};

const getTypeTagText = (type) => {
  const typeMap = {
    'activity': '活动',
    'product': '产品',
    'coupon': '优惠券',
    'poster': '海报'
  };
  return typeMap[type] || '未知类型';
};

const getTypeTagBackground = (type) => {
  const bgMap = {
    'activity': 'rgba(255, 59, 105, 0.8)',
    'product': 'rgba(90, 200, 250, 0.8)',
    'coupon': 'rgba(255, 149, 0, 0.8)',
    'poster': 'rgba(52, 199, 89, 0.8)'
  };
  return bgMap[type] || 'rgba(142, 142, 147, 0.8)';
};

const viewRecordDetail = (record) => {
  switch (record.contentType) {
    case 'activity':
      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);
      break;
    case 'product':
      navigateTo(`/subPackages/activity-showcase/pages/products/detail?id=${record.id}`);
      break;
    case 'coupon':
      navigateTo(`/subPackages/activity-showcase/pages/coupon/detail?id=${record.id}`);
      break;
    default:
      navigateTo(`/subPackages/activity-showcase/pages/distribution/poster/index?id=${record.id}`);
  }
};

const shareAgain = (record) => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const viewAnalytics = (record) => {
  navigateTo(`/subPackages/activity-showcase/pages/share-analytics/index?id=${record.id}`);
};

const showFilter = () => {
  filterPopup.value.open();
};

const closeFilter = () => {
  filterPopup.value.close();
};

const selectTimeOption = (index) => {
  selectedTimeOption.value = index;
};

const togglePlatformOption = (index) => {
  const position = selectedPlatformOptions.value.indexOf(index);
  if (index === 0) {
    // 如果选择"全部"，清除其他选项
    selectedPlatformOptions.value = [0];
  } else {
    // 如果选择其他选项，移除"全部"选项
    if (selectedPlatformOptions.value.includes(0)) {
      selectedPlatformOptions.value = selectedPlatformOptions.value.filter(item => item !== 0);
    }
    
    // 切换选中状态
    if (position !== -1) {
      selectedPlatformOptions.value.splice(position, 1);
      // 如果没有选项，默认选中"全部"
      if (selectedPlatformOptions.value.length === 0) {
        selectedPlatformOptions.value = [0];
      }
    } else {
      selectedPlatformOptions.value.push(index);
    }
  }
};

const toggleContentTypeOption = (index) => {
  const position = selectedContentTypeOptions.value.indexOf(index);
  if (index === 0) {
    // 如果选择"全部"，清除其他选项
    selectedContentTypeOptions.value = [0];
  } else {
    // 如果选择其他选项，移除"全部"选项
    if (selectedContentTypeOptions.value.includes(0)) {
      selectedContentTypeOptions.value = selectedContentTypeOptions.value.filter(item => item !== 0);
    }
    
    // 切换选中状态
    if (position !== -1) {
      selectedContentTypeOptions.value.splice(position, 1);
      // 如果没有选项，默认选中"全部"
      if (selectedContentTypeOptions.value.length === 0) {
        selectedContentTypeOptions.value = [0];
      }
    } else {
      selectedContentTypeOptions.value.push(index);
    }
  }
};

const resetFilter = () => {
  selectedTimeOption.value = 0;
  selectedPlatformOptions.value = [0];
  selectedContentTypeOptions.value = [0];
};

const applyFilter = () => {
  // 应用筛选
  console.log('应用筛选', {
    time: timeOptions[selectedTimeOption.value].value,
    platforms: selectedPlatformOptions.value.map(index => platformOptions[index].value),
    contentTypes: selectedContentTypeOptions.value.map(index => contentTypeOptions[index].value)
  });
  
  // 模拟筛选结果
  uni.showToast({
    title: '筛选已应用',
    icon: 'success'
  });
  
  closeFilter();
};

const goBack = () => {
  uni.navigateBack();
};

const navigateTo = (url) => {
  uni.navigateTo({ url });
};
</script>

<style scoped>
.share-records-container {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.navbar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #5856D6 0%, #A09CFF 100%);
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 113rpx; /* 原来是110rpx，增加3rpx */
  padding: var(--status-bar-height) 30rpx 0;
}

.back-btn, .filter-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* 标签栏样式 */
.share-tabs {
  display: flex;
  background: #FFFFFF;
  padding: 0 20rpx;
  margin-top: calc(var(--status-bar-height) + 113rpx); /* 原来是110rpx，增加3rpx */
  border-bottom: 1rpx solid #EEEEEE;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}

.tab-text {
  font-size: 28rpx;
  color: #333333;
  padding: 0 10rpx;
}

.tab-item.active .tab-text {
  color: #5856D6;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 6rpx;
  border-radius: 3rpx;
}

/* 统计卡片样式 */
.stats-card {
  display: flex;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999999;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background: #EEEEEE;
  margin: 0 10rpx;
}

/* 分享记录列表样式 */
.records-swiper {
  height: calc(100vh - var(--status-bar-height) - 113rpx - 70rpx - 130rpx); /* 原来是110rpx，增加3rpx */
}

.tab-content {
  height: 100%;
  padding: 0 20rpx 20rpx;
}

.records-list {
  padding-bottom: 30rpx;
}

.record-card {
  display: flex;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.record-preview {
  width: 200rpx;
  height: 200rpx;
  position: relative;
}

.record-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.record-type-tag {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: #FFFFFF;
}

.record-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.record-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.record-meta {
  display: flex;
  margin-bottom: 10rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.meta-icon {
  margin-right: 6rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #999999;
}

.record-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-row {
  display: flex;
  margin-bottom: 10rpx;
}

.mini-stat {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.stat-icon {
  margin-right: 6rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #999999;
}

.mini-stat.highlight .stat-text {
  color: #5856D6;
  font-weight: 500;
}

.record-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 10rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-left: 15rpx;
  border: 1rpx solid #DDDDDD;
  color: #666666;
}

.action-icon {
  margin-right: 6rpx;
}

.action-btn.primary {
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
  border: none;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.empty-state .action-btn {
  padding: 15rpx 60rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  border: none;
}

/* 筛选弹窗样式 */
.filter-popup {
  background: #FFFFFF;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  padding: 30rpx;
  max-height: 70vh;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.filter-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-content {
  max-height: calc(70vh - 180rpx);
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666666;
  background: #F5F5F5;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.filter-option.active {
  background: rgba(88, 86, 214, 0.1);
  color: #5856D6;
  border: 1rpx solid rgba(88, 86, 214, 0.3);
}

.filter-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #F0F0F0;
}

.filter-reset, .filter-apply {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.filter-reset {
  background: #F5F5F5;
  color: #666666;
  margin-right: 20rpx;
}

.filter-apply {
  background: linear-gradient(135deg, #5856D6 0%, #A09CFF 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(88, 86, 214, 0.2);
}
</style>