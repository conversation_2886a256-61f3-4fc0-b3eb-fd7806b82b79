<template>
  <view class="promotion-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">推广海报</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 海报预览 -->
    <view class="poster-preview-section">
      <view class="preview-card" :style="{ backgroundColor: selectedTheme.bgColor }">
        <image class="poster-image" :src="posterPath || previewData.image || defaultPosterPath" mode="aspectFit"></image>
        <view class="preview-footer">
          <text class="preview-text" :style="{ color: selectedTheme.textColor }">预览效果</text>
        </view>
      </view>
      
      <view class="action-buttons">
        <button class="action-button save" @click="savePoster">
          <view class="button-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7 10L12 15L17 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 15V3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text>保存到相册</text>
        </button>
        
        <button class="action-button share" @click="shareToWechat">
          <view class="button-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="18" cy="5" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="6" cy="12" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="18" cy="19" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text>分享海报</text>
        </button>
      </view>
    </view>
    
    <!-- 内容预览 -->
    <view class="content-preview">
      <view class="preview-info">
        <text class="preview-title">{{ previewData.title || '加载中...' }}</text>
        <view class="preview-detail" v-if="previewData.type === 'carpool'">
          <text class="detail-item">{{ previewData.departure }} → {{ previewData.destination }}</text>
          <text class="detail-item">{{ previewData.departureTime }}</text>
        </view>
        <view class="preview-detail" v-else-if="previewData.type === 'product'">
          <text class="detail-item price">¥{{ previewData.price }}</text>
          <text class="detail-item original-price" v-if="previewData.originalPrice">¥{{ previewData.originalPrice }}</text>
        </view>
        <view class="preview-detail" v-else-if="previewData.type === 'house'">
          <text class="detail-item">{{ previewData.roomType }} | {{ previewData.area }}㎡</text>
          <text class="detail-item price">{{ previewData.price }}{{ previewData.priceUnit }}</text>
        </view>
        <view class="preview-detail" v-else-if="previewData.type === 'merchant'">
          <text class="detail-item">{{ previewData.category }}</text>
          <text class="detail-item">{{ previewData.address }}</text>
        </view>
        <view class="preview-detail" v-else>
          <text class="detail-item">{{ previewData.description }}</text>
        </view>
      </view>
    </view>
    
    <!-- 佣金信息 -->
    <view class="commission-info" v-if="hasCommission">
      <view class="commission-card">
        <view class="commission-header">
          <image class="commission-icon" src="/static/images/commission-icon.png" mode="aspectFit"></image>
          <text class="commission-title">推广佣金</text>
        </view>
        <view class="commission-content">
          <text class="commission-rate">佣金比例: {{ commissionRate }}%</text>
          <text class="commission-estimate">预计佣金: ¥{{ estimatedCommission }}</text>
        </view>
      </view>
    </view>
    
    <!-- 自定义选项 -->
    <view class="customization-section">
      <!-- 海报模板选择 -->
      <view class="option-group">
        <text class="option-title">海报模板</text>
        <scroll-view scroll-x class="template-scroll">
          <view class="template-list">
            <view 
              v-for="(poster, index) in posterTemplates" 
              :key="index" 
              class="template-item"
              :class="{ active: currentPosterIndex === index }"
              @click="selectPoster(index)"
            >
              <image class="template-thumb" :src="poster.thumb" mode="aspectFill"></image>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 主题颜色选择 -->
      <view class="option-group">
        <text class="option-title">主题颜色</text>
        <view class="theme-options">
          <view 
            v-for="(theme, index) in themeColors" 
            :key="index" 
            class="theme-option" 
            :class="{ active: selectedTheme === theme }"
            :style="{ backgroundColor: theme.bgColor }"
            @click="selectTheme(theme)"
          >
            <view class="theme-check" v-if="selectedTheme === theme">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 6L9 17L4 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 其他选项 -->
      <view class="option-group">
        <view class="toggle-option">
          <text class="toggle-label">显示推广员ID</text>
          <switch :checked="showId" @change="toggleId" color="#3846cd" />
        </view>
        
        <view class="toggle-option">
          <text class="toggle-label">添加推广文案</text>
          <switch :checked="showPromoText" @change="togglePromoText" color="#3846cd" />
        </view>
      </view>
      
      <!-- 自定义文案 -->
      <view class="option-group" v-if="showPromoText">
        <text class="option-title">自定义文案</text>
        <input 
          class="custom-input" 
          type="text" 
          v-model="customText" 
          placeholder="输入自定义文案（最多20字）" 
          maxlength="20"
          @input="onCustomTextChange"
        />
      </view>
    </view>
    
    <!-- 用于生成海报的隐藏画布 -->
    <canvas canvas-id="posterCanvas" style="width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;"></canvas>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import promotionService from '@/utils/promotionService';

// 页面参数
const contentType = ref('');
const contentId = ref('');

// 预览数据
const previewData = reactive({
  type: '',
  title: '',
  image: '',
  description: '',
  // 拼车特有字段
  departure: '',
  destination: '',
  departureTime: '',
  // 商品特有字段
  price: 0,
  originalPrice: 0,
  // 房产特有字段
  roomType: '',
  area: 0,
  priceUnit: '',
  // 商家特有字段
  category: '',
  address: ''
});

// 佣金相关
const hasCommission = ref(false);
const commissionRate = ref(0);
const estimatedCommission = ref('0.00');

// 二维码和海报
const showPosterPopup = ref(false);
const posterPath = ref('');

// 当前用户ID
const userId = ref('');

// 默认海报路径
const defaultPosterPath = '/static/images/default-poster.png';

// 海报模板
const posterTemplates = ref([
  {
    name: '商品推广',
    thumb: '/static/images/distribution/poster-thumb-1.png',
    url: '/static/images/distribution/poster-1.png',
    type: 'product'
  },
  {
    name: '店铺推广',
    thumb: '/static/images/distribution/poster-thumb-2.png',
    url: '/static/images/distribution/poster-2.png',
    type: 'store'
  },
  {
    name: '活动推广',
    thumb: '/static/images/distribution/poster-thumb-3.png',
    url: '/static/images/distribution/poster-3.png',
    type: 'activity'
  },
  {
    name: '会员推广',
    thumb: '/static/images/distribution/poster-thumb-4.png',
    url: '/static/images/distribution/poster-4.png',
    type: 'member'
  },
  {
    name: '节日主题',
    thumb: '/static/images/distribution/poster-thumb-5.png',
    url: '/static/images/distribution/poster-5.png',
    type: 'general'
  }
]);

// 特定类型的模板
const typeTemplates = {
  'carpool': [
    {
      name: '拼车专用',
      thumb: '/static/images/distribution/carpool-thumb-1.png',
      url: '/static/images/distribution/carpool-1.png',
      type: 'carpool'
    }
  ],
  'secondhand': [
    {
      name: '二手商品专用',
      thumb: '/static/images/distribution/secondhand-thumb-1.png',
      url: '/static/images/distribution/secondhand-1.png',
      type: 'secondhand'
    }
  ],
  'house': [
    {
      name: '房屋租售专用',
      thumb: '/static/images/distribution/house-thumb-1.png',
      url: '/static/images/distribution/house-1.png',
      type: 'house'
    }
  ]
};

// 主题颜色
const themeColors = [
  { bgColor: '#FFFFFF', textColor: '#333333' },
  { bgColor: '#3846CD', textColor: '#FFFFFF' },
  { bgColor: '#2C3AA0', textColor: '#FFFFFF' },
  { bgColor: '#F5F7FA', textColor: '#333333' },
  { bgColor: '#FFE8F0', textColor: '#FF3B30' },
  { bgColor: '#E8F8FF', textColor: '#007AFF' }
];

// 状态变量
const currentPosterIndex = ref(0);
const selectedTheme = ref(themeColors[0]);
const showId = ref(true);
const showPromoText = ref(false);
const customText = ref('');

// 当前海报
const currentPoster = computed(() => {
  if (posterTemplates.value.length === 0) return '';
  return posterTemplates.value[currentPosterIndex.value].url;
});

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  contentType.value = options.type || '';
  contentId.value = options.id || '';
  
  // 获取用户信息
  const userInfo = uni.getStorageSync('userInfo');
  if (userInfo && userInfo.userId) {
    userId.value = userInfo.userId;
  }
  
  // 加载内容数据
  loadContentData();
  
  // 加载适合的海报模板
  loadPosterTemplates();
  
  // 延迟生成海报，确保数据已加载
  setTimeout(() => {
    generatePoster();
  }, 1500);
});

// 加载海报模板
const loadPosterTemplates = () => {
  // 获取特定类型的模板
  const typeSpecificTemplates = typeTemplates[contentType.value] || [];
  
  // 合并模板
  posterTemplates.value = [
    ...typeSpecificTemplates,
    ...posterTemplates.value.filter(template => 
      template.type === 'general' || template.type === contentType.value
    )
  ];
};

// 选择海报模板
const selectPoster = (index) => {
  currentPosterIndex.value = index;
  // 延迟生成海报，避免频繁调用
  setTimeout(() => {
    generatePoster();
  }, 100);
};

// 选择主题颜色
const selectTheme = (theme) => {
  selectedTheme.value = theme;
  // 延迟生成海报，避免频繁调用
  setTimeout(() => {
    generatePoster();
  }, 100);
};

// 切换显示推广员ID
const toggleId = (e) => {
  showId.value = e.detail.value;
  // 延迟生成海报，避免频繁调用
  setTimeout(() => {
    generatePoster();
  }, 100);
};

// 切换显示推广文案
const togglePromoText = (e) => {
  showPromoText.value = e.detail.value;
  if (showPromoText.value && !customText.value) {
    generateDefaultText();
  }
  // 延迟生成海报，避免频繁调用
  setTimeout(() => {
    generatePoster();
  }, 100);
};

// 处理自定义文案变化
const onCustomTextChange = () => {
  // 使用防抖，避免频繁生成海报
  if (window.customTextTimer) {
    clearTimeout(window.customTextTimer);
  }
  window.customTextTimer = setTimeout(() => {
    generatePoster();
  }, 500);
};

// 生成默认文案
const generateDefaultText = () => {
  switch(contentType.value) {
    case 'carpool':
      customText.value = `【${previewData.title}】${previewData.departure}→${previewData.destination}，出发时间：${previewData.departureTime}`;
      break;
    case 'product':
      customText.value = `【${previewData.title}】价格：¥${previewData.price}`;
      break;
    case 'house':
      customText.value = `【${previewData.title}】${previewData.roomType}，${previewData.area}㎡，${previewData.price}${previewData.priceUnit}`;
      break;
    default:
      customText.value = `【${previewData.title}】`;
  }
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '推广海报使用帮助',
    content: '选择喜欢的海报模板，自定义颜色和文案，生成精美海报进行分享推广。',
    showCancel: false
  });
};

// 加载内容数据
const loadContentData = () => {
  if (!contentType.value || !contentId.value) {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    });
    setTimeout(() => {
      goBack();
    }, 1500);
    return;
  }
  
  // 显示加载中
  uni.showLoading({
    title: '加载中...'
  });
  
  // 根据不同内容类型获取数据
  switch (contentType.value) {
    case 'carpool':
      loadCarpoolData();
      break;
    case 'product':
      loadProductData();
      break;
    case 'merchant':
      loadMerchantData();
      break;
    case 'house':
      loadHouseData();
      break;
    case 'service':
      loadServiceData();
      break;
    case 'community':
      loadCommunityData();
      break;
    case 'activity':
      loadActivityData();
      break;
    case 'content':
      loadContentPageData();
      break;
    default:
      // 通用加载逻辑
      loadGenericData();
  }
};

// 加载拼车数据
const loadCarpoolData = () => {
  // 模拟API调用
  setTimeout(() => {
    previewData.type = 'carpool';
    previewData.title = '磁县到邯郸拼车';
    previewData.image = '/static/images/carpool-default.jpg';
    previewData.departure = '磁县城区';
    previewData.destination = '邯郸火车站';
    previewData.departureTime = '2023-07-20 08:00';
    previewData.price = 15;
    
    // 设置佣金信息
    hasCommission.value = false;
    commissionRate.value = 0;
    estimatedCommission.value = '0.00';
    
    uni.hideLoading();
  }, 500);
};

// 加载商品数据
const loadProductData = () => {
  // 模拟API调用
  setTimeout(() => {
    previewData.type = 'product';
    previewData.title = '有机新鲜蔬菜礼盒';
    previewData.image = '/static/images/product-default.jpg';
    previewData.price = 99.8;
    previewData.originalPrice = 128;
    previewData.description = '本地新鲜蔬菜，无农药，当天采摘';
    
    // 设置佣金信息
    hasCommission.value = true;
    commissionRate.value = 10;
    estimatedCommission.value = (previewData.price * commissionRate.value / 100).toFixed(2);
    
    uni.hideLoading();
  }, 500);
};

// 加载商家数据
const loadMerchantData = () => {
  // 模拟API调用
  setTimeout(() => {
    previewData.type = 'merchant';
    previewData.title = '磁州同城生活馆';
    previewData.image = '/static/images/shop-default.jpg';
    previewData.category = '生活服务';
    previewData.address = '磁县城区中心广场东侧100米';
    previewData.description = '提供本地生活服务、特产销售等';
    
    // 设置佣金信息
    hasCommission.value = true;
    commissionRate.value = 15;
    estimatedCommission.value = '5.00';
    
    uni.hideLoading();
  }, 500);
};

// 加载房产数据
const loadHouseData = () => {
  // 模拟API调用
  setTimeout(() => {
    previewData.type = 'house';
    previewData.title = '城区精装两居室出租';
    previewData.image = '/static/images/house-default.jpg';
    previewData.roomType = '2室1厅1卫';
    previewData.area = 89;
    previewData.price = 1200;
    previewData.priceUnit = '/月';
    previewData.description = '城区中心位置，交通便利，拎包入住';
    
    // 设置佣金信息
    hasCommission.value = true;
    commissionRate.value = 30;
    estimatedCommission.value = '360.00';
    
    uni.hideLoading();
  }, 500);
};

// 加载服务数据
const loadServiceData = () => {
  // 模拟API调用
  setTimeout(() => {
    previewData.type = 'service';
    previewData.title = '专业家电维修';
    previewData.image = '/static/images/service-default.jpg';
    previewData.price = 50;
    previewData.description = '各类家电维修，上门服务，快速响应';
    
    // 设置佣金信息
    hasCommission.value = true;
    commissionRate.value = 20;
    estimatedCommission.value = '10.00';
    
    uni.hideLoading();
  }, 500);
};

// 加载社区信息数据
const loadCommunityData = () => {
  // 模拟API调用
  setTimeout(() => {
    previewData.type = 'community';
    previewData.title = '社区便民信息';
    previewData.image = '/static/images/community-default.jpg';
    previewData.description = '社区便民服务信息，包括水电维修、家政服务等';
    
    // 设置佣金信息
    hasCommission.value = false;
    
    uni.hideLoading();
  }, 500);
};

// 加载活动数据
const loadActivityData = () => {
  // 模拟API调用
  setTimeout(() => {
    previewData.type = 'activity';
    previewData.title = '周末农产品展销会';
    previewData.image = '/static/images/activity-default.jpg';
    previewData.description = '本周末在城区广场举办农产品展销会，欢迎参加';
    
    // 设置佣金信息
    hasCommission.value = true;
    commissionRate.value = 5;
    estimatedCommission.value = '2.50';
    
    uni.hideLoading();
  }, 500);
};

// 加载内容页面数据
const loadContentPageData = () => {
  // 模拟API调用
  setTimeout(() => {
    previewData.type = 'content';
    previewData.title = '磁州特产推荐';
    previewData.image = '/static/images/content-default.jpg';
    previewData.description = '磁州地区特色农产品与手工艺品推荐';
    
    // 设置佣金信息
    hasCommission.value = false;
    
    uni.hideLoading();
  }, 500);
};

// 加载通用数据
const loadGenericData = () => {
  // 模拟API调用
  setTimeout(() => {
    previewData.type = contentType.value;
    previewData.title = '磁州生活网内容';
    previewData.image = '/static/images/default.jpg';
    previewData.description = '磁州生活网为您提供本地生活服务';
    
    // 设置佣金信息
    hasCommission.value = false;
    
    uni.hideLoading();
  }, 500);
};

// 分享到微信好友
const shareToWechat = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    title: previewData.title,
    summary: customText.value || previewData.description || '分享来自磁州生活网',
    imageUrl: posterPath.value || previewData.image,
    href: promotionService.generateShareLink(contentType.value, contentId.value, userId.value),
    success: function() {
      console.log('分享成功');
      // 记录分享事件
      promotionService.recordPromotion(contentType.value, contentId.value, 'wechat');
    },
    fail: function() {
      console.log('分享失败');
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  });
};

// 生成海报
const generatePoster = async () => {
  try {
    // 重置海报路径，强制重新生成
    posterPath.value = '';
    
    uni.showLoading({
      title: '生成中...'
    });
    
    // 生成海报
    posterPath.value = await promotionService.generatePoster(
      contentType.value,
      previewData,
      userId.value,
      {
        template: currentPoster.value,
        theme: selectedTheme.value,
        showId: showId.value,
        customText: showPromoText.value ? customText.value : ''
      }
    );
    
    uni.hideLoading();
  } catch (err) {
    console.error('生成海报失败', err);
    uni.hideLoading();
    
    // 设置默认海报路径，确保UI不会空白
    posterPath.value = previewData.image || defaultPosterPath;
    
    uni.showToast({
      title: '生成海报失败',
      icon: 'none'
    });
  }
};

// 保存海报
const savePoster = async () => {
  if (!posterPath.value) {
    // 先生成海报
    await generatePoster();
  }
  
  if (posterPath.value) {
    try {
      uni.showLoading({
        title: '保存中...'
      });
      
      await promotionService.savePosterToAlbum(posterPath.value);
      
      uni.hideLoading();
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
    } catch (err) {
      console.error('保存海报失败', err);
      uni.hideLoading();
      uni.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.promotion-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #3846cd, #2c3aa0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(56, 70, 205, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 海报预览部分 */
.poster-preview-section {
  margin: 16px;
}

.preview-card {
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.poster-image {
  width: 100%;
  max-width: 300px;
  height: 400px;
  border-radius: 10px;
  background-color: #f5f5f5;
  object-fit: contain;
}

.preview-footer {
  margin-top: 12px;
  text-align: center;
}

.preview-text {
  font-size: 14px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  border: none;
}

.action-button.save {
  background: linear-gradient(135deg, #34C759, #32D74B);
  color: #FFFFFF;
}

.action-button.share {
  background: linear-gradient(135deg, #3846cd, #2c3aa0);
  color: #FFFFFF;
}

.button-icon {
  margin-right: 6px;
}

/* 自定义选项部分 */
.customization-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.option-group {
  margin-bottom: 20px;
}

.option-group:last-child {
  margin-bottom: 0;
}

.option-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.template-scroll {
  width: 100%;
  white-space: nowrap;
}

.template-list {
  display: flex;
  padding-bottom: 8px;
}

.template-item {
  width: 80px;
  height: 120px;
  margin-right: 12px;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.template-item.active {
  border-color: #3846cd;
  transform: scale(1.05);
}

.template-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.theme-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.theme-option {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
}

.theme-option.active {
  border-color: #3846cd;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(56, 70, 205, 0.2);
}

.theme-check {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.toggle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.toggle-option:last-child {
  border-bottom: none;
}

.toggle-label {
  font-size: 14px;
  color: #333;
}

.custom-input {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 14px;
  color: #333;
  border: 1px solid transparent;
}

.custom-input:focus {
  border-color: #3846cd;
  background-color: #FFFFFF;
}

/* 内容预览 */
.content-preview {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.preview-info {
  padding: 0;
}

.preview-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.preview-detail {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.detail-item {
  font-size: 14px;
  color: #666;
  margin-right: 20px;
  margin-bottom: 5px;
}

.price {
  color: #ff4400;
  font-weight: bold;
}

.original-price {
  color: #999;
  text-decoration: line-through;
}

/* 佣金信息 */
.commission-info {
  margin: 16px;
}

.commission-card {
  background: linear-gradient(135deg, #fff8f0, #fff5e6);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(255, 153, 0, 0.1);
}

.commission-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.commission-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.commission-title {
  font-size: 16px;
  font-weight: bold;
  color: #ff9900;
}

.commission-content {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
}

.commission-rate, .commission-estimate {
  font-size: 14px;
  color: #ff6600;
}
</style> 