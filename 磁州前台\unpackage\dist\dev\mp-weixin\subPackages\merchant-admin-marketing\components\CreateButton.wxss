/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-973321a6, html.data-v-973321a6, #app.data-v-973321a6, .index-container.data-v-973321a6 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.create-button.data-v-973321a6 {
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  padding: 0 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 默认主题 - 紫色渐变 */
.theme-default.data-v-973321a6 {
  background: linear-gradient(135deg, #9254de, #7a36d6);
  box-shadow: 0 4px 10px rgba(122, 54, 214, 0.2);
}

/* 优惠券主题 - 红橙色渐变 */
.theme-coupon.data-v-973321a6 {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  box-shadow: 0 4px 10px rgba(255, 94, 98, 0.2);
}

/* 满减活动主题 - 黄色渐变 */
.theme-discount.data-v-973321a6 {
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  box-shadow: 0 4px 10px rgba(248, 216, 0, 0.2);
}

/* 拼团活动主题 - 紫色渐变 */
.theme-group.data-v-973321a6 {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  box-shadow: 0 4px 10px rgba(144, 64, 255, 0.2);
}

/* 秒杀活动主题 - 橙红色渐变 */
.theme-flash.data-v-973321a6 {
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  box-shadow: 0 4px 10px rgba(255, 118, 0, 0.2);
}

/* 积分商城主题 - 橙色渐变 */
.theme-points.data-v-973321a6 {
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  box-shadow: 0 4px 10px rgba(255, 118, 0, 0.2);
}
.create-button.data-v-973321a6:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.button-content.data-v-973321a6 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.plus-icon.data-v-973321a6 {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin-right: 4px;
}
.button-text.data-v-973321a6 {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}