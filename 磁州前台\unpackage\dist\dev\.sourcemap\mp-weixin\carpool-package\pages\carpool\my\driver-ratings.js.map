{"version": 3, "file": "driver-ratings.js", "sources": ["carpool-package/pages/carpool/my/driver-ratings.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcZHJpdmVyLXJhdGluZ3MudnVl"], "sourcesContent": ["<template>\n  <view class=\"ratings-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">我的评价</text>\n        </view>\n        <view class=\"right-action\">\n          <!-- 预留位置 -->\n        </view>\n      </view>\n    </view>\n    \n    <!-- 评分统计 -->\n    <view class=\"rating-summary\" :style=\"{ marginTop: (statusBarHeight + 44) + 'px' }\">\n      <view class=\"summary-left\">\n        <view class=\"average-score\">{{averageScore}}</view>\n        <view class=\"total-ratings\">共{{totalRatings}}条评价</view>\n      </view>\n      <view class=\"summary-right\">\n        <view class=\"star-row\">\n          <text class=\"star-level\">5星</text>\n          <view class=\"progress-bar\">\n            <view class=\"progress-fill\" :style=\"{width: fiveStarPercent + '%'}\"></view>\n          </view>\n          <text class=\"star-count\">{{fiveStarCount}}</text>\n        </view>\n        <view class=\"star-row\">\n          <text class=\"star-level\">4星</text>\n          <view class=\"progress-bar\">\n            <view class=\"progress-fill\" :style=\"{width: fourStarPercent + '%'}\"></view>\n          </view>\n          <text class=\"star-count\">{{fourStarCount}}</text>\n        </view>\n        <view class=\"star-row\">\n          <text class=\"star-level\">3星</text>\n          <view class=\"progress-bar\">\n            <view class=\"progress-fill\" :style=\"{width: threeStarPercent + '%'}\"></view>\n          </view>\n          <text class=\"star-count\">{{threeStarCount}}</text>\n        </view>\n        <view class=\"star-row\">\n          <text class=\"star-level\">2星</text>\n          <view class=\"progress-bar\">\n            <view class=\"progress-fill\" :style=\"{width: twoStarPercent + '%'}\"></view>\n          </view>\n          <text class=\"star-count\">{{twoStarCount}}</text>\n        </view>\n        <view class=\"star-row\">\n          <text class=\"star-level\">1星</text>\n          <view class=\"progress-bar\">\n            <view class=\"progress-fill\" :style=\"{width: oneStarPercent + '%'}\"></view>\n          </view>\n          <text class=\"star-count\">{{oneStarCount}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 标签统计 -->\n    <view class=\"tags-summary\">\n      <view class=\"section-title\">评价标签</view>\n      <view class=\"tags-container\">\n        <view class=\"tag-item\" v-for=\"(tag, index) in topTags\" :key=\"index\">\n          <text class=\"tag-text\">{{tag.name}}</text>\n          <text class=\"tag-count\">{{tag.count}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 筛选选项 -->\n    <view class=\"filter-options\">\n      <view class=\"filter-tabs\">\n        <view \n          class=\"tab-item\" \n          :class=\"{ active: activeFilter === 'all' }\"\n          @click=\"setFilter('all')\"\n        >全部</view>\n        <view \n          class=\"tab-item\" \n          :class=\"{ active: activeFilter === 'good' }\"\n          @click=\"setFilter('good')\"\n        >好评</view>\n        <view \n          class=\"tab-item\" \n          :class=\"{ active: activeFilter === 'medium' }\"\n          @click=\"setFilter('medium')\"\n        >中评</view>\n        <view \n          class=\"tab-item\" \n          :class=\"{ active: activeFilter === 'bad' }\"\n          @click=\"setFilter('bad')\"\n        >差评</view>\n      </view>\n    </view>\n    \n    <!-- 评价列表 -->\n    <view class=\"ratings-list\">\n      <view class=\"empty-state\" v-if=\"filteredRatings.length === 0\">\n        <image src=\"/static/images/icons/empty-ratings.png\" class=\"empty-icon\"></image>\n        <text class=\"empty-text\">暂无{{filterText}}评价</text>\n      </view>\n      \n      <view class=\"rating-item\" v-for=\"(item, index) in filteredRatings\" :key=\"index\">\n        <view class=\"rating-header\">\n          <view class=\"user-info\" v-if=\"!item.isAnonymous\">\n            <image :src=\"item.userAvatar\" class=\"user-avatar\"></image>\n            <text class=\"user-name\">{{item.userName}}</text>\n          </view>\n          <view class=\"user-info\" v-else>\n            <image src=\"/static/images/avatar/anonymous.png\" class=\"user-avatar\"></image>\n            <text class=\"user-name\">匿名用户</text>\n          </view>\n          <view class=\"rating-score\">\n            <view class=\"star-display\">\n              <image \n                v-for=\"i in 5\" \n                :key=\"i\" \n                :src=\"i <= item.rating ? '/static/images/icons/star-filled.png' : '/static/images/icons/star-empty.png'\" \n                class=\"star-icon-small\"\n              ></image>\n            </view>\n            <text class=\"rating-date\">{{formatDate(item.createTime)}}</text>\n          </view>\n        </view>\n        \n        <view class=\"rating-tags\" v-if=\"item.tags && item.tags.length > 0\">\n          <view class=\"tag-pill\" v-for=\"(tag, tagIndex) in item.tags\" :key=\"tagIndex\">\n            {{tag}}\n          </view>\n        </view>\n        \n        <view class=\"rating-comment\" v-if=\"item.comment\">\n          {{item.comment}}\n        </view>\n        \n        <view class=\"trip-info\">\n          <text class=\"trip-route\">{{item.startLocation}} → {{item.endLocation}}</text>\n          <text class=\"trip-time\">{{item.departureDate}} {{item.departureTime}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 评价数据\nconst ratings = ref([]);\nconst activeFilter = ref('all');\nconst averageScore = ref(4.8);\nconst totalRatings = ref(0);\nconst fiveStarCount = ref(0);\nconst fourStarCount = ref(0);\nconst threeStarCount = ref(0);\nconst twoStarCount = ref(0);\nconst oneStarCount = ref(0);\nconst topTags = ref([]);\n\n// 计算属性\nconst fiveStarPercent = computed(() => {\n  return totalRatings.value > 0 ? (fiveStarCount.value / totalRatings.value) * 100 : 0;\n});\n\nconst fourStarPercent = computed(() => {\n  return totalRatings.value > 0 ? (fourStarCount.value / totalRatings.value) * 100 : 0;\n});\n\nconst threeStarPercent = computed(() => {\n  return totalRatings.value > 0 ? (threeStarCount.value / totalRatings.value) * 100 : 0;\n});\n\nconst twoStarPercent = computed(() => {\n  return totalRatings.value > 0 ? (twoStarCount.value / totalRatings.value) * 100 : 0;\n});\n\nconst oneStarPercent = computed(() => {\n  return totalRatings.value > 0 ? (oneStarCount.value / totalRatings.value) * 100 : 0;\n});\n\nconst filteredRatings = computed(() => {\n  if (activeFilter.value === 'all') {\n    return ratings.value;\n  } else if (activeFilter.value === 'good') {\n    return ratings.value.filter(item => item.rating >= 4);\n  } else if (activeFilter.value === 'medium') {\n    return ratings.value.filter(item => item.rating === 3);\n  } else if (activeFilter.value === 'bad') {\n    return ratings.value.filter(item => item.rating <= 2);\n      }\n  return ratings.value;\n});\n\nconst filterText = computed(() => {\n      const filterMap = {\n        all: '',\n        good: '好',\n        medium: '中',\n        bad: '差'\n      };\n  return filterMap[activeFilter.value];\n});\n\n// 页面加载\nonMounted(() => {\n  // 获取状态栏高度\n  const systemInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = systemInfo.statusBarHeight || 20;\n  \n  // 加载评价数据\n  loadRatings();\n});\n\n    // 返回上一页\nconst goBack = () => {\n      uni.navigateBack();\n};\n    \n    // 加载评价数据\nconst loadRatings = () => {\n      // 实际应用中应该从API获取数据\n      // 这里使用模拟数据\n      uni.showLoading({\n        title: '加载中...'\n      });\n      \n      setTimeout(() => {\n        // 模拟数据\n    ratings.value = [\n          {\n            id: 'R001',\n            userName: '李先生',\n            userAvatar: '/static/images/avatar/user3.png',\n            rating: 5,\n            tags: ['准时出发', '路线合理', '态度友好'],\n            comment: '司机师傅非常准时，提前到达上车地点等候，路线选择也很合理，避开了拥堵路段，全程行车平稳，服务态度也很好！',\n            isAnonymous: false,\n            createTime: '2023-06-18T09:30:00',\n            startLocation: '磁县火车站',\n            endLocation: '邯郸东站',\n            departureDate: '2023-06-15',\n            departureTime: '14:30'\n          },\n          {\n            id: 'R002',\n            userName: '王女士',\n            userAvatar: '/static/images/avatar/user4.png',\n            rating: 4,\n            tags: ['驾驶平稳', '车内整洁'],\n            comment: '车内很干净，司机开车也很稳，就是上车地点稍微有点难找。',\n            isAnonymous: false,\n            createTime: '2023-06-16T15:45:00',\n            startLocation: '磁县人民医院',\n            endLocation: '邯郸市区',\n            departureDate: '2023-06-16',\n            departureTime: '10:00'\n          },\n          {\n            id: 'R003',\n            userName: '',\n            userAvatar: '',\n            rating: 5,\n            tags: ['价格合理', '态度友好', '准时出发'],\n            comment: '价格很合理，比其他拼车便宜，司机人也很好，会主动帮忙搬行李。',\n            isAnonymous: true,\n            createTime: '2023-06-14T18:20:00',\n            startLocation: '磁县商业街',\n            endLocation: '邯郸高铁站',\n            departureDate: '2023-06-14',\n            departureTime: '16:30'\n          },\n          {\n            id: 'R004',\n            userName: '张先生',\n            userAvatar: '/static/images/avatar/user2.png',\n            rating: 3,\n            tags: ['价格合理'],\n            comment: '一般，没什么特别的，价格还可以。',\n            isAnonymous: false,\n            createTime: '2023-06-12T11:05:00',\n            startLocation: '磁县第一中学',\n            endLocation: '邯郸火车站',\n            departureDate: '2023-06-12',\n            departureTime: '09:00'\n          },\n          {\n            id: 'R005',\n            userName: '赵女士',\n            userAvatar: '/static/images/avatar/user5.png',\n            rating: 2,\n            tags: [],\n            comment: '迟到了15分钟，态度也不是很好，车内有异味。',\n            isAnonymous: false,\n            createTime: '2023-06-10T20:15:00',\n            startLocation: '磁县政府',\n            endLocation: '邯郸市区',\n            departureDate: '2023-06-10',\n            departureTime: '18:30'\n          }\n        ];\n        \n        // 计算统计数据\n    calculateStatistics();\n        \n        uni.hideLoading();\n      }, 1000);\n};\n    \n    // 计算统计数据\nconst calculateStatistics = () => {\n      // 计算总数和各星级数量\n  totalRatings.value = ratings.value.length;\n  fiveStarCount.value = ratings.value.filter(item => item.rating === 5).length;\n  fourStarCount.value = ratings.value.filter(item => item.rating === 4).length;\n  threeStarCount.value = ratings.value.filter(item => item.rating === 3).length;\n  twoStarCount.value = ratings.value.filter(item => item.rating === 2).length;\n  oneStarCount.value = ratings.value.filter(item => item.rating === 1).length;\n      \n      // 计算平均分\n  if (totalRatings.value > 0) {\n    const totalScore = ratings.value.reduce((sum, item) => sum + item.rating, 0);\n    averageScore.value = (totalScore / totalRatings.value).toFixed(1);\n      }\n      \n      // 计算标签统计\n      const tagMap = {};\n  ratings.value.forEach(item => {\n        if (item.tags && item.tags.length > 0) {\n          item.tags.forEach(tag => {\n            if (tagMap[tag]) {\n              tagMap[tag]++;\n            } else {\n              tagMap[tag] = 1;\n            }\n          });\n        }\n      });\n      \n      // 转换为数组并排序\n      const tagArray = Object.keys(tagMap).map(key => ({\n        name: key,\n        count: tagMap[key]\n      }));\n      \n      tagArray.sort((a, b) => b.count - a.count);\n  topTags.value = tagArray.slice(0, 6); // 取前6个标签\n};\n    \n    // 设置筛选条件\nconst setFilter = (filter) => {\n  activeFilter.value = filter;\n};\n    \n    // 格式化日期\nconst formatDate = (dateString) => {\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));\n      \n      if (diffDays === 0) {\n        return '今天';\n      } else if (diffDays === 1) {\n        return '昨天';\n      } else if (diffDays < 7) {\n        return `${diffDays}天前`;\n      } else {\n        const year = date.getFullYear();\n        const month = date.getMonth() + 1;\n        const day = date.getDate();\n        return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;\n      }\n};\n</script>\n\n<style lang=\"scss\">\n.ratings-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  position: relative;\n  padding-top: calc(90rpx + var(--status-bar-height, 40px));\n  padding-bottom: 40rpx;\n}\n\n/* 自定义导航栏 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background-color: #0A84FF;\n}\n\n.header-content {\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 12px;\n}\n\n.left-action, .right-action {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n  /* 图标是黑色的，需要转为白色 */\n  filter: brightness(0) invert(1);\n}\n\n.title-area {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.page-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #FFFFFF;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n/* 评分统计 */\n.rating-summary {\n  margin: 20rpx 32rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  display: flex;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.summary-left {\n  width: 30%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  border-right: 1px solid #F2F2F7;\n  padding-right: 24rpx;\n}\n\n.average-score {\n  font-size: 48rpx;\n  font-weight: 600;\n  color: #FF9F0A;\n  margin-bottom: 8rpx;\n}\n\n.total-ratings {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.summary-right {\n  flex: 1;\n  padding-left: 24rpx;\n}\n\n.star-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.star-level {\n  width: 60rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.progress-bar {\n  flex: 1;\n  height: 16rpx;\n  background-color: #F2F2F7;\n  border-radius: 8rpx;\n  margin: 0 16rpx;\n  overflow: hidden;\n}\n\n.progress-fill {\n  height: 100%;\n  background-color: #FF9F0A;\n  border-radius: 8rpx;\n}\n\n.star-count {\n  width: 40rpx;\n  font-size: 24rpx;\n  color: #8E8E93;\n  text-align: right;\n}\n\n/* 标签统计 */\n.tags-summary {\n  margin: 0 32rpx 32rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.section-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 20rpx;\n}\n\n.tags-container {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.tag-item {\n  padding: 10rpx 20rpx;\n  background-color: #F5F5F5;\n  border-radius: 30rpx;\n  margin-right: 16rpx;\n  margin-bottom: 16rpx;\n  display: flex;\n  align-items: center;\n}\n\n.tag-text {\n  font-size: 26rpx;\n  color: #333333;\n}\n\n.tag-count {\n  font-size: 24rpx;\n  color: #FF9F0A;\n  margin-left: 8rpx;\n}\n\n/* 筛选选项 */\n.filter-options {\n  margin: 0 32rpx 20rpx;\n}\n\n.filter-tabs {\n  display: flex;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.tab-item {\n  flex: 1;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  color: #666666;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #0A84FF;\n  font-weight: 500;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 25%;\n  width: 50%;\n  height: 4rpx;\n  background-color: #0A84FF;\n  border-radius: 2rpx;\n}\n\n/* 评价列表 */\n.ratings-list {\n  margin: 0 32rpx;\n}\n\n.empty-state {\n  padding: 60rpx 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.empty-icon {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 20rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n\n.rating-item {\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 24rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n}\n\n.rating-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n}\n\n.user-avatar {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 30rpx;\n  margin-right: 12rpx;\n}\n\n.user-name {\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.rating-score {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n\n.star-display {\n  display: flex;\n  margin-bottom: 6rpx;\n}\n\n.star-icon-small {\n  width: 24rpx;\n  height: 24rpx;\n  margin-left: 4rpx;\n}\n\n.rating-date {\n  font-size: 22rpx;\n  color: #8E8E93;\n}\n\n.rating-tags {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 16rpx;\n}\n\n.tag-pill {\n  padding: 6rpx 16rpx;\n  background-color: #F5F5F5;\n  border-radius: 20rpx;\n  font-size: 22rpx;\n  color: #666666;\n  margin-right: 12rpx;\n  margin-bottom: 8rpx;\n}\n\n.rating-comment {\n  font-size: 28rpx;\n  color: #333333;\n  line-height: 1.5;\n  margin-bottom: 16rpx;\n}\n\n.trip-info {\n  padding-top: 16rpx;\n  border-top: 1px solid #F2F2F7;\n  display: flex;\n  justify-content: space-between;\n}\n\n.trip-route {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n.trip-time {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n  width: 100%;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/driver-ratings.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AA2JA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,UAAUA,cAAAA,IAAI,CAAA,CAAE;AACtB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,eAAeA,cAAAA,IAAI,GAAG;AAC5B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,gBAAgBA,cAAAA,IAAI,CAAC;AAC3B,UAAM,gBAAgBA,cAAAA,IAAI,CAAC;AAC3B,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAC5B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,UAAUA,cAAAA,IAAI,CAAA,CAAE;AAGtB,UAAM,kBAAkBC,cAAQ,SAAC,MAAM;AACrC,aAAO,aAAa,QAAQ,IAAK,cAAc,QAAQ,aAAa,QAAS,MAAM;AAAA,IACrF,CAAC;AAED,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;AACrC,aAAO,aAAa,QAAQ,IAAK,cAAc,QAAQ,aAAa,QAAS,MAAM;AAAA,IACrF,CAAC;AAED,UAAM,mBAAmBA,cAAQ,SAAC,MAAM;AACtC,aAAO,aAAa,QAAQ,IAAK,eAAe,QAAQ,aAAa,QAAS,MAAM;AAAA,IACtF,CAAC;AAED,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AACpC,aAAO,aAAa,QAAQ,IAAK,aAAa,QAAQ,aAAa,QAAS,MAAM;AAAA,IACpF,CAAC;AAED,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AACpC,aAAO,aAAa,QAAQ,IAAK,aAAa,QAAQ,aAAa,QAAS,MAAM;AAAA,IACpF,CAAC;AAED,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;AACrC,UAAI,aAAa,UAAU,OAAO;AAChC,eAAO,QAAQ;AAAA,MACnB,WAAa,aAAa,UAAU,QAAQ;AACxC,eAAO,QAAQ,MAAM,OAAO,UAAQ,KAAK,UAAU,CAAC;AAAA,MACxD,WAAa,aAAa,UAAU,UAAU;AAC1C,eAAO,QAAQ,MAAM,OAAO,UAAQ,KAAK,WAAW,CAAC;AAAA,MACzD,WAAa,aAAa,UAAU,OAAO;AACvC,eAAO,QAAQ,MAAM,OAAO,UAAQ,KAAK,UAAU,CAAC;AAAA,MACjD;AACL,aAAO,QAAQ;AAAA,IACjB,CAAC;AAED,UAAM,aAAaA,cAAQ,SAAC,MAAM;AAC5B,YAAM,YAAY;AAAA,QAChB,KAAK;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,KAAK;AAAA,MACb;AACE,aAAO,UAAU,aAAa,KAAK;AAAA,IACrC,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,aAAaC,oBAAI;AACvB,sBAAgB,QAAQ,WAAW,mBAAmB;AAGtD;IACF,CAAC;AAGD,UAAM,SAAS,MAAM;AACfA,oBAAG,MAAC,aAAY;AAAA,IACtB;AAGA,UAAM,cAAc,MAAM;AAGpBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACf,CAAO;AAED,iBAAW,MAAM;AAEnB,gBAAQ,QAAQ;AAAA,UACV;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,YAC7B,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,eAAe;AAAA,YACf,aAAa;AAAA,YACb,eAAe;AAAA,YACf,eAAe;AAAA,UAChB;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,MAAM,CAAC,QAAQ,MAAM;AAAA,YACrB,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,eAAe;AAAA,YACf,aAAa;AAAA,YACb,eAAe;AAAA,YACf,eAAe;AAAA,UAChB;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,YAC7B,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,eAAe;AAAA,YACf,aAAa;AAAA,YACb,eAAe;AAAA,YACf,eAAe;AAAA,UAChB;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,MAAM,CAAC,MAAM;AAAA,YACb,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,eAAe;AAAA,YACf,aAAa;AAAA,YACb,eAAe;AAAA,YACf,eAAe;AAAA,UAChB;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,MAAM,CAAE;AAAA,YACR,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,eAAe;AAAA,YACf,aAAa;AAAA,YACb,eAAe;AAAA,YACf,eAAe;AAAA,UAChB;AAAA,QACX;AAGI;AAEIA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAI;AAAA,IACb;AAGA,UAAM,sBAAsB,MAAM;AAEhC,mBAAa,QAAQ,QAAQ,MAAM;AACnC,oBAAc,QAAQ,QAAQ,MAAM,OAAO,UAAQ,KAAK,WAAW,CAAC,EAAE;AACtE,oBAAc,QAAQ,QAAQ,MAAM,OAAO,UAAQ,KAAK,WAAW,CAAC,EAAE;AACtE,qBAAe,QAAQ,QAAQ,MAAM,OAAO,UAAQ,KAAK,WAAW,CAAC,EAAE;AACvE,mBAAa,QAAQ,QAAQ,MAAM,OAAO,UAAQ,KAAK,WAAW,CAAC,EAAE;AACrE,mBAAa,QAAQ,QAAQ,MAAM,OAAO,UAAQ,KAAK,WAAW,CAAC,EAAE;AAGrE,UAAI,aAAa,QAAQ,GAAG;AAC1B,cAAM,aAAa,QAAQ,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,QAAQ,CAAC;AAC3E,qBAAa,SAAS,aAAa,aAAa,OAAO,QAAQ,CAAC;AAAA,MAC7D;AAGD,YAAM,SAAS,CAAA;AACnB,cAAQ,MAAM,QAAQ,UAAQ;AACxB,YAAI,KAAK,QAAQ,KAAK,KAAK,SAAS,GAAG;AACrC,eAAK,KAAK,QAAQ,SAAO;AACvB,gBAAI,OAAO,GAAG,GAAG;AACf,qBAAO,GAAG;AAAA,YACxB,OAAmB;AACL,qBAAO,GAAG,IAAI;AAAA,YACf;AAAA,UACb,CAAW;AAAA,QACF;AAAA,MACT,CAAO;AAGD,YAAM,WAAW,OAAO,KAAK,MAAM,EAAE,IAAI,UAAQ;AAAA,QAC/C,MAAM;AAAA,QACN,OAAO,OAAO,GAAG;AAAA,MAClB,EAAC;AAEF,eAAS,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAC7C,cAAQ,QAAQ,SAAS,MAAM,GAAG,CAAC;AAAA,IACrC;AAGA,UAAM,YAAY,CAAC,WAAW;AAC5B,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,aAAa,CAAC,eAAe;AAC7B,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,MAAM,oBAAI;AAChB,YAAM,WAAW,KAAK,OAAO,MAAM,SAAS,MAAO,KAAK,KAAK,GAAG;AAEhE,UAAI,aAAa,GAAG;AAClB,eAAO;AAAA,MACf,WAAiB,aAAa,GAAG;AACzB,eAAO;AAAA,MACf,WAAiB,WAAW,GAAG;AACvB,eAAO,GAAG,QAAQ;AAAA,MAC1B,OAAa;AACL,cAAM,OAAO,KAAK;AAClB,cAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,cAAM,MAAM,KAAK;AACjB,eAAO,GAAG,IAAI,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,MAAM,MAAM,GAAG;AAAA,MACjF;AAAA,IACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5XA,GAAG,WAAWC,SAAe;"}