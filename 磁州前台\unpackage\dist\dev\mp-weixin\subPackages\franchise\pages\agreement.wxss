/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-495bcd48, html.data-v-495bcd48, #app.data-v-495bcd48, .index-container.data-v-495bcd48 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.agreement-container.data-v-495bcd48 {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx;
}
.agreement-content.data-v-495bcd48 {
  padding: 40rpx 30rpx;
  background-color: #FFFFFF;
  margin: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.agreement-title.data-v-495bcd48 {
  font-size: 38rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  margin-bottom: 20rpx;
}
.agreement-time.data-v-495bcd48 {
  font-size: 26rpx;
  color: #999999;
  text-align: center;
  margin-bottom: 40rpx;
}
.agreement-section.data-v-495bcd48 {
  margin-bottom: 40rpx;
}
.agreement-section.data-v-495bcd48:last-child {
  margin-bottom: 0;
}
.section-title.data-v-495bcd48 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.section-content.data-v-495bcd48 {
  padding-left: 20rpx;
}
.text-block.data-v-495bcd48 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 15rpx;
}
.text-block.data-v-495bcd48:last-child {
  margin-bottom: 0;
}
.bottom-actions.data-v-495bcd48 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.action-btn.data-v-495bcd48 {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin: 0;
}
.action-btn.agree-btn.data-v-495bcd48 {
  width: 100%;
  background: linear-gradient(90deg, #1677FF, #4F9DFF);
  color: #FFFFFF;
}