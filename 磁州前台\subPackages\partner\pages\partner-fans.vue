<template>
  <view class="fans-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的粉丝</view>
      <view class="navbar-right">
        <!-- 预留位置与发布页面保持一致 -->
      </view>
    </view>
    
    <!-- 添加顶部安全区域 -->
    <view class="safe-area-top"></view>

    <!-- 数据统计卡片 -->
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-num">{{totalFans}}</text>
        <text class="stats-label">粉丝总数</text>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <text class="stats-num">{{newFans}}</text>
        <text class="stats-label">今日新增</text>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <text class="stats-num">{{activeFans}}</text>
        <text class="stats-label">活跃粉丝</text>
      </view>
    </view>

    <!-- 筛选区域 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view 
          class="tab-item" 
          :class="{'active': currentTab === 'all'}"
          @tap="switchTab('all')"
        >全部粉丝</view>
        <view 
          class="tab-item" 
          :class="{'active': currentTab === 'new'}"
          @tap="switchTab('new')"
        >新增粉丝</view>
        <view 
          class="tab-item" 
          :class="{'active': currentTab === 'active'}"
          @tap="switchTab('active')"
        >活跃粉丝</view>
        <view 
          class="tab-item" 
          :class="{'active': currentTab === 'valuable'}"
          @tap="switchTab('valuable')"
        >消费粉丝</view>
      </view>
      <view class="search-box">
        <text class="cuIcon-search"></text>
        <input type="text" v-model="searchKeyword" placeholder="搜索粉丝昵称" @confirm="searchFans" />
        <text class="cuIcon-close" v-if="searchKeyword" @tap="clearSearch"></text>
      </view>
    </view>

    <!-- 粉丝列表 -->
    <view class="fans-list" v-if="filteredFans.length > 0">
      <view class="fans-item" v-for="(item, index) in filteredFans" :key="index">
        <view class="fans-avatar">
          <image :src="item.avatar" mode="aspectFill"></image>
          <view class="fans-badge" v-if="item.isVip">VIP</view>
        </view>
        <view class="fans-info">
          <view class="fans-name-row">
            <text class="fans-name">{{item.nickname}}</text>
            <text class="fans-tag" v-if="isToday(item.joinTime)">今日新增</text>
          </view>
          <view class="fans-data">
            <text class="join-time">加入时间: {{formatDate(item.joinTime)}}</text>
            <text class="contribution">贡献: ¥{{item.contribution}}</text>
          </view>
        </view>
        <view class="fans-action">
          <button class="message-btn" @tap="sendMessage(item)">私信</button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image src="/static/images/no-fans.png" mode="aspectFit"></image>
      <text>{{emptyText}}</text>
      <button class="share-btn" @tap="shareToFriends">去邀请好友</button>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="filteredFans.length > 0 && hasMore">
      <text @tap="loadMore" v-if="!isLoading">加载更多</text>
      <text v-else>加载中...</text>
    </view>

    <view class="load-end" v-if="filteredFans.length > 0 && !hasMore">
      <text>- 已经到底了 -</text>
    </view>

    <!-- 分享弹窗 -->
    <view class="share-modal" v-if="showShareModal">
      <view class="modal-mask" @tap="closeShareModal"></view>
      <view class="modal-content">
        <view class="modal-title">分享到</view>
        <view class="share-options">
          <view class="share-option" @tap="shareToWechat">
            <image src="/static/images/wechat.png" mode="aspectFit"></image>
            <text>微信好友</text>
          </view>
          <view class="share-option" @tap="shareToMoments">
            <image src="/static/images/moments.png" mode="aspectFit"></image>
            <text>朋友圈</text>
          </view>
          <view class="share-option" @tap="navigateToPoster">
            <image src="/static/images/poster.png" mode="aspectFit"></image>
            <text>推广海报</text>
          </view>
          <view class="share-option" @tap="copyLink">
            <image src="/static/images/copy-link.png" mode="aspectFit"></image>
            <text>复制链接</text>
          </view>
        </view>
        <button class="cancel-btn" @tap="closeShareModal">取消</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Vue 3 Composition API 代码开始
// 数据部分
const totalFans = ref(128); // 粉丝总数
const newFans = ref(5); // 今日新增
const activeFans = ref(42); // 活跃粉丝
const currentTab = ref('all'); // 当前选中的标签
const searchKeyword = ref(''); // 搜索关键词
const fansList = ref([]); // 粉丝列表
const page = ref(1); // 当前页码
const pageSize = ref(10); // 每页条数
const hasMore = ref(true); // 是否有更多数据
const isLoading = ref(false); // 是否正在加载
const showShareModal = ref(false); // 是否显示分享弹窗

// 计算属性
// 过滤后的粉丝列表
const filteredFans = computed(() => {
  let result = [...fansList.value];
  
  // 按标签筛选
  if (currentTab.value === 'new') {
    result = result.filter(item => isToday(item.joinTime));
  } else if (currentTab.value === 'active') {
    result = result.filter(item => item.activeLevel >= 3);
  } else if (currentTab.value === 'valuable') {
    result = result.filter(item => item.contribution > 0);
  }
  
  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(item => 
      item.nickname.toLowerCase().includes(keyword)
    );
  }
  
  return result;
});

// 空状态文本
const emptyText = computed(() => {
  if (searchKeyword.value) {
    return '没有找到相关粉丝';
  } else if (currentTab.value === 'new') {
    return '今日暂无新增粉丝';
  } else if (currentTab.value === 'active') {
    return '暂无活跃粉丝';
  } else if (currentTab.value === 'valuable') {
    return '暂无消费粉丝';
  } else {
    return '暂无粉丝，去邀请好友吧';
  }
});

// 生命周期钩子
onMounted(() => {
  // 初始化加载数据
  loadData();
});

// 方法
// 切换标签
const switchTab = (tab) => {
  currentTab.value = tab;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 搜索粉丝
const searchFans = () => {
  // 重置页码，重新加载
  page.value = 1;
  hasMore.value = true;
  loadData();
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = '';
  searchFans();
};

// 加载数据
const loadData = () => {
  isLoading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    // 模拟数据
    const mockData = generateMockFans();
    
    if (page.value === 1) {
      fansList.value = mockData;
    } else {
      fansList.value = [...fansList.value, ...mockData];
    }
    
    // 判断是否还有更多数据
    hasMore.value = page.value < 3; // 模拟只有3页数据
    
    isLoading.value = false;
  }, 500);
};

// 加载更多
const loadMore = () => {
  if (isLoading.value || !hasMore.value) return;
  
  page.value++;
  loadData();
};

// 发送私信
const sendMessage = (fan) => {
  uni.navigateTo({
    url: `/pages/message/chat?userId=${fan.id}&nickname=${fan.nickname}`
  });
};

// 分享给朋友
const shareToFriends = () => {
  showShareModal.value = true;
};

// 关闭分享弹窗
const closeShareModal = () => {
  showShareModal.value = false;
};

// 分享到微信
const shareToWechat = () => {
  uni.showToast({
    title: '已分享到微信',
    icon: 'success'
  });
  closeShareModal();
};

// 分享到朋友圈
const shareToMoments = () => {
  uni.showToast({
    title: '已分享到朋友圈',
    icon: 'success'
  });
  closeShareModal();
};

// 跳转到海报页面
const navigateToPoster = () => {
  uni.navigateTo({
    url: '/pages/my/partner-poster'
  });
  closeShareModal();
};

// 复制链接
const copyLink = () => {
  uni.setClipboardData({
    data: 'https://example.com/register?ref=' + Math.random().toString(36).slice(2),
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success'
      });
      closeShareModal();
    }
  });
};

// 格式化日期
const formatDate = (dateStr) => {
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 判断是否为今天
const isToday = (dateStr) => {
  const today = new Date();
  const date = new Date(dateStr);
  
  return today.getFullYear() === date.getFullYear() 
    && today.getMonth() === date.getMonth() 
    && today.getDate() === date.getDate();
};

// 生成模拟粉丝数据
const generateMockFans = () => {
  const mockFans = [];
  const nicknames = ['奔跑的小猪', '小小科技迷', '梦想家', '爱笑的眼睛', '春风十里', '南方姑娘', '山川湖海', '时光漫步'];
  const avatars = [
    '/static/images/avatar-1.png',
    '/static/images/avatar-2.png',
    '/static/images/avatar-3.png',
    '/static/images/avatar-4.png',
    '/static/images/avatar-5.png',
    '/static/images/avatar-6.png'
  ];
  
  // 随机生成10条记录
  for (let i = 0; i < 10; i++) {
    const now = new Date();
    // 随机日期，最近30天内
    const randomDays = Math.floor(Math.random() * 30);
    const joinTime = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);
    
    // 随机活跃度 1-5
    const activeLevel = Math.floor(Math.random() * 5) + 1;
    
    // 随机贡献值
    const contribution = randomDays === 0 ? 0 : Math.floor(Math.random() * 1000);
    
    // 随机VIP状态
    const isVip = Math.random() > 0.7;
    
    mockFans.push({
      id: 'user_' + Date.now() + i,
      nickname: nicknames[Math.floor(Math.random() * nicknames.length)],
      avatar: avatars[Math.floor(Math.random() * avatars.length)],
      joinTime,
      activeLevel,
      contribution,
      isVip
    });
  }
  
  return mockFans;
};
// Vue 3 Composition API 代码结束
</script>

<style lang="scss" scoped>
.fans-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}

.stats-card {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-num {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #999999;
}

.stats-divider {
  width: 1px;
  height: 60rpx;
  background-color: #eeeeee;
}

.filter-section {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

.filter-tabs {
  display: flex;
  overflow-x: auto;
  margin-bottom: 20rpx;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

.tab-item {
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  white-space: nowrap;
  
  &.active {
    color: #1677FF;
    font-weight: 500;
    
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: #1677FF;
      border-radius: 2rpx;
    }
  }
}

.search-box {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  
  .cuIcon-search {
    font-size: 32rpx;
    color: #999999;
    margin-right: 10rpx;
  }
  
  input {
    flex: 1;
    height: 100%;
    font-size: 28rpx;
  }
  
  .cuIcon-close {
    font-size: 32rpx;
    color: #999999;
    padding: 10rpx;
  }
}

.fans-list {
  padding: 0 30rpx;
}

.fans-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.fans-avatar {
  position: relative;
  margin-right: 20rpx;
  
  image {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
  }
  
  .fans-badge {
    position: absolute;
    bottom: 0;
    right: 0;
    background: linear-gradient(135deg, #FF9500, #FF6000);
    color: #ffffff;
    font-size: 20rpx;
    padding: 4rpx 8rpx;
    border-radius: 10rpx;
    transform: scale(0.8);
    transform-origin: right bottom;
  }
}

.fans-info {
  flex: 1;
}

.fans-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.fans-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-right: 10rpx;
}

.fans-tag {
  background-color: #e6f4ff;
  color: #1677FF;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
}

.fans-data {
  font-size: 24rpx;
  color: #999999;
  
  .join-time {
    margin-right: 20rpx;
  }
  
  .contribution {
    color: #ff6000;
  }
}

.fans-action {
  .message-btn {
    background: #f0f7ff;
    color: #1677FF;
    font-size: 26rpx;
    min-width: 120rpx;
    height: 60rpx;
    line-height: 60rpx;
    padding: 0 20rpx;
    border-radius: 30rpx;
    border: 1px solid #1677FF;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  
  image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 30rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 40rpx;
  }
  
  .share-btn {
    width: 300rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: linear-gradient(135deg, #1677FF, #0E5FD8);
    color: #ffffff;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}

.load-more, .load-end {
  text-align: center;
  font-size: 26rpx;
  color: #999999;
  padding: 30rpx 0;
}

.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  
  .modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
  }
  
  .modal-content {
    width: 100%;
    background-color: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
    padding-bottom: env(safe-area-inset-bottom);
    position: relative;
    z-index: 1000;
  }
  
  .modal-title {
    text-align: center;
    font-size: 30rpx;
    font-weight: 500;
    color: #333333;
    padding: 30rpx 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .share-options {
    display: flex;
    flex-wrap: wrap;
    padding: 30rpx;
  }
  
  .share-option {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx 0;
    
    image {
      width: 100rpx;
      height: 100rpx;
      margin-bottom: 15rpx;
    }
    
    text {
      font-size: 26rpx;
      color: #666666;
    }
  }
  
  .cancel-btn {
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 32rpx;
    color: #333333;
    border-top: 10rpx solid #f5f5f5;
  }
}

/* 自定义导航栏样式 */
.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px; /* 状态栏高度 */
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: 'AlimamaShuHeiTi', sans-serif;
  text-align: center;
}

.navbar-right {
  width: 40rpx;
  height: 40rpx;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20; /* 确保在标题上层，可以被点击 */
}

.back-icon {
  width: 100%;
  height: 100%;
}

.safe-area-top {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}
</style> 
