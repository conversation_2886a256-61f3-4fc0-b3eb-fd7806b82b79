<template>
  <view class="refresh-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">刷新套餐</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 已有刷新次数 - 改为卡片设计 -->
    <view class="refresh-card" :style="{ marginTop: (navbarHeight + 20) + 'px' }">
      <view class="refresh-card-content">
        <view class="refresh-card-left" style="background-color: #ffffff;">
          <image class="refresh-big-icon" src="/static/images/tabbar/套餐.png"></image>
        </view>
        <view class="refresh-card-center">
          <view class="refresh-card-title">已有刷新次数</view>
          <view class="refresh-card-count">{{ userRefreshCount }}</view>
          <view class="refresh-card-expire">有效期至：{{ expiryDate }}</view>
        </view>
        <view class="refresh-card-right">
          <button class="use-now-btn" @click="navigateToPublish">立即使用</button>
        </view>
      </view>
    </view>
    
    <!-- 说明区域 -->
    <view class="info-card">
      <view class="info-title">什么是刷新套餐？</view>
      <view class="info-desc">
        <text>刷新套餐用于提升您发布的信息的排名，使信息优先展示在列表前端，增加曝光度；</text>
        <text>购买刷新套餐后可以手动刷新您的发布内容，也可以设置自动刷新。</text>
      </view>
    </view>
    
    <!-- 套餐列表 -->
    <view class="package-list">
      <view 
        class="package-item" 
        v-for="(item, index) in packageList" 
        :key="index"
        :class="{ active: selectedPackage === index }"
        @click="selectPackage(index)"
      >
        <view class="package-select">
          <view class="select-dot" :class="{ selected: selectedPackage === index }"></view>
        </view>
        <view class="package-content">
          <view class="package-name">{{ item.name }}</view>
          <view class="package-details">
            <text class="package-count">{{ item.count }}次</text>
            <text class="package-validity">有效期1年</text>
          </view>
          <view class="package-feature" v-if="item.feature">{{ item.feature }}</view>
        </view>
        <view class="package-price">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{ item.price }}</text>
        </view>
      </view>
    </view>
    
    <!-- 常见问题 -->
    <view class="faq-section">
      <view class="section-title">常见问题</view>
      <view class="faq-item" v-for="(item, index) in faqList" :key="index" @click="toggleFaq(index)">
        <view class="faq-header">
          <text class="faq-question">{{ item.question }}</text>
          <image class="faq-icon" :class="{ 'faq-icon-open': item.isOpen }" src="/static/images/tabbar/arrow-up.png"></image>
        </view>
        <view class="faq-answer" v-if="item.isOpen">{{ item.answer }}</view>
      </view>
    </view>
    
    <!-- 底部购买按钮 -->
    <view class="bottom-bar">
      <view class="total-price">
        <text>总计：</text>
        <text class="price-symbol">¥</text>
        <text class="total-value">{{ selectedPrice }}</text>
      </view>
      <button class="buy-button" @click="handleBuy">立即购买</button>
    </view>
    
    <!-- 客服悬浮按钮 -->
    <fab-buttons :pageName="'refresh'"></fab-buttons>
  </view>
</template>

<script>
import { smartNavigate } from '@/utils/navigation.js';
import FabButtons from '@/components/FabButtons.vue';

export default {
  components: {
    FabButtons
  },
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      selectedPackage: 1, // 默认选中第二个套餐
      userRefreshCount: 33,
      expiryDate: '2025-05-20', // 默认有效期
      packageList: [
        {
          id: 'p1',
          name: '体验套餐',
          count: 10,
          validity: 365,
          price: 15.00,
          feature: '适合初次尝试'
        },
        {
          id: 'p2',
          name: '热门套餐',
          count: 30,
          validity: 365,
          price: 39.00,
          feature: '最受欢迎'
        },
        {
          id: 'p3',
          name: '尊享套餐',
          count: 100,
          validity: 365,
          price: 99.00,
          feature: '性价比最高'
        }
      ],
      faqList: [
        {
          question: '刷新后排名会提升多少？',
          answer: '刷新后您的发布信息将立即显示在同类信息的前列，具体排名受时间、区域以及其他用户刷新情况影响。',
          isOpen: false
        },
        {
          question: '刷新次数可以退款吗？',
          answer: '已购买的刷新次数不支持退款，建议根据自己的需求购买合适的套餐。',
          isOpen: false
        },
        {
          question: '一天可以刷新几次？',
          answer: '同一条信息每天最多可刷新3次，每次刷新间隔需大于4小时。',
          isOpen: false
        },
        {
          question: '刷新次数过期了怎么办？',
          answer: '刷新次数有效期为1年，建议在有效期内使用完毕。过期后需要重新购买套餐。',
          isOpen: false
        }
      ]
    }
  },
  computed: {
    selectedPrice() {
      return this.packageList[this.selectedPackage]?.price.toFixed(2) || '0.00';
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 获取用户当前刷新次数和有效期
    this.getUserRefreshInfo();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 选择套餐
    selectPackage(index) {
      this.selectedPackage = index;
    },
    
    // 切换FAQ显示状态
    toggleFaq(index) {
      this.faqList[index].isOpen = !this.faqList[index].isOpen;
    },
    
    // 获取用户当前刷新次数和有效期
    getUserRefreshInfo() {
      // 模拟API请求
      setTimeout(() => {
        // 保留33次刷新，展示图片中的数值
        this.userRefreshCount = 33;
        
        // 计算有效期（当前日期加一年）
        const now = new Date();
        const nextYear = new Date(now.setFullYear(now.getFullYear() + 1));
        this.expiryDate = this.formatDate(nextYear);
      }, 500);
    },
    
    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    // 跳转到我的发布页面
    navigateToPublish() {
      smartNavigate('/pages/my/publish').catch(err => {
        console.error('跳转到我的发布页面失败:', err);
        uni.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      });
    },
    
    // 处理购买按钮点击
    handleBuy() {
      const selectedPackage = this.packageList[this.selectedPackage];
      
      uni.showModal({
        title: '确认购买',
        content: `您确定要购买【${selectedPackage.name}】吗？价格：¥${selectedPackage.price}`,
        success: (res) => {
          if (res.confirm) {
            // 调用支付接口
            this.processPurchase(selectedPackage);
          }
        }
      });
    },
    
    // 处理购买流程
    processPurchase(packageInfo) {
      // 显示加载提示
      uni.showLoading({
        title: '处理中...'
      });
      
      // 模拟支付请求
      setTimeout(() => {
        uni.hideLoading();
        
        // 模拟支付成功
        uni.showToast({
          title: '购买成功',
          icon: 'success',
          duration: 2000
        });
        
        // 更新刷新次数
        this.userRefreshCount += packageInfo.count;
        
        // 可以跳转到支付成功页面或者留在当前页面
      }, 1500);
    }
  }
}
</script>

<style>
.refresh-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 刷新卡片 - 新设计 */
.refresh-card {
  margin: 30rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #0069FF, #0052DD, #0045CC);
  box-shadow: 0 16rpx 32rpx rgba(0, 102, 255, 0.25);
  overflow: hidden;
  position: relative;
}

.refresh-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  border-radius: 20rpx 20rpx 0 0;
  z-index: 1;
}

.refresh-card-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
  position: relative;
  z-index: 2;
}

.refresh-card-left {
  margin-right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 45rpx;
}

.refresh-big-icon {
  width: 54rpx;
  height: 54rpx;
}

.refresh-card-center {
  flex: 1;
}

.refresh-card-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}

.refresh-card-count {
  font-size: 60rpx;
  font-weight: bold;
  color: #ffffff;
  line-height: 1.2;
}

.refresh-card-expire {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 8rpx;
}

.refresh-card-right {
  margin-left: 20rpx;
}

.use-now-btn {
  background-color: #ffffff;
  color: #0066FF;
  font-size: 28rpx;
  font-weight: bold;
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 30rpx;
  border-radius: 35rpx;
  border: none;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  margin: 0;
  min-width: 170rpx;
  text-align: center;
  transition: all 0.2s ease;
}

.use-now-btn:active {
  transform: translateY(3rpx);
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 信息卡片 */
.info-card {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.info-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.info-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.info-desc text {
  display: block;
  margin-bottom: 10rpx;
}

/* 套餐列表 */
.package-list {
  margin: 30rpx;
}

.package-item {
  display: flex;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.package-item.active {
  border-color: #0066FF;
  background-color: #f0f6ff;
}

.package-select {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.select-dot {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-dot.selected {
  border-color: #0066FF;
}

.select-dot.selected::after {
  content: "";
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  background-color: #0066FF;
}

.package-content {
  flex: 1;
}

.package-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.package-details {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.package-count {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.package-validity {
  font-size: 28rpx;
  color: #666;
}

.package-feature {
  display: inline-block;
  font-size: 24rpx;
  color: #ff6600;
  background-color: rgba(255, 102, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.package-price {
  display: flex;
  align-items: center;
  color: #ff6600;
}

.price-symbol {
  font-size: 24rpx;
  margin-right: 2rpx;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
}

/* FAQ部分 */
.faq-section {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
}

.faq-item {
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
  padding-bottom: 20rpx;
}

.faq-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.faq-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-question {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.faq-icon {
  width: 32rpx;
  height: 32rpx;
  transform: rotate(90deg);
  transition: all 0.3s;
}

.faq-icon-open {
  transform: rotate(270deg);
}

.faq-answer {
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
  line-height: 1.6;
  padding-left: 20rpx;
  border-left: 4rpx solid #f0f0f0;
}

/* 底部购买栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.total-price {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.total-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6600;
}

.buy-button {
  background-color: #0066FF;
  color: #fff;
  font-size: 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 40rpx;
  border-radius: 35rpx;
  margin: 0;
}

.buy-button:active {
  background-color: #0052CC;
}
</style> 