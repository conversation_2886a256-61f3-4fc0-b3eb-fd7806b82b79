"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_navigation = require("../../utils/navigation.js");
const common_assets = require("../../common/assets.js");
const FabButtons = () => "../../components/FabButtons.js";
const _sfc_main = {
  components: {
    FabButtons
  },
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      selectedPackage: 1,
      // 默认选中第二个套餐
      userRefreshCount: 33,
      expiryDate: "2025-05-20",
      // 默认有效期
      packageList: [
        {
          id: "p1",
          name: "体验套餐",
          count: 10,
          validity: 365,
          price: 15,
          feature: "适合初次尝试"
        },
        {
          id: "p2",
          name: "热门套餐",
          count: 30,
          validity: 365,
          price: 39,
          feature: "最受欢迎"
        },
        {
          id: "p3",
          name: "尊享套餐",
          count: 100,
          validity: 365,
          price: 99,
          feature: "性价比最高"
        }
      ],
      faqList: [
        {
          question: "刷新后排名会提升多少？",
          answer: "刷新后您的发布信息将立即显示在同类信息的前列，具体排名受时间、区域以及其他用户刷新情况影响。",
          isOpen: false
        },
        {
          question: "刷新次数可以退款吗？",
          answer: "已购买的刷新次数不支持退款，建议根据自己的需求购买合适的套餐。",
          isOpen: false
        },
        {
          question: "一天可以刷新几次？",
          answer: "同一条信息每天最多可刷新3次，每次刷新间隔需大于4小时。",
          isOpen: false
        },
        {
          question: "刷新次数过期了怎么办？",
          answer: "刷新次数有效期为1年，建议在有效期内使用完毕。过期后需要重新购买套餐。",
          isOpen: false
        }
      ]
    };
  },
  computed: {
    selectedPrice() {
      var _a;
      return ((_a = this.packageList[this.selectedPackage]) == null ? void 0 : _a.price.toFixed(2)) || "0.00";
    }
  },
  onLoad() {
    const sysInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    this.getUserRefreshInfo();
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 选择套餐
    selectPackage(index) {
      this.selectedPackage = index;
    },
    // 切换FAQ显示状态
    toggleFaq(index) {
      this.faqList[index].isOpen = !this.faqList[index].isOpen;
    },
    // 获取用户当前刷新次数和有效期
    getUserRefreshInfo() {
      setTimeout(() => {
        this.userRefreshCount = 33;
        const now = /* @__PURE__ */ new Date();
        const nextYear = new Date(now.setFullYear(now.getFullYear() + 1));
        this.expiryDate = this.formatDate(nextYear);
      }, 500);
    },
    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 跳转到我的发布页面
    navigateToPublish() {
      utils_navigation.smartNavigate("/pages/my/publish").catch((err) => {
        common_vendor.index.__f__("error", "at pages/my/refresh-package.vue:212", "跳转到我的发布页面失败:", err);
        common_vendor.index.showToast({
          title: "跳转失败，请重试",
          icon: "none"
        });
      });
    },
    // 处理购买按钮点击
    handleBuy() {
      const selectedPackage = this.packageList[this.selectedPackage];
      common_vendor.index.showModal({
        title: "确认购买",
        content: `您确定要购买【${selectedPackage.name}】吗？价格：¥${selectedPackage.price}`,
        success: (res) => {
          if (res.confirm) {
            this.processPurchase(selectedPackage);
          }
        }
      });
    },
    // 处理购买流程
    processPurchase(packageInfo) {
      common_vendor.index.showLoading({
        title: "处理中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "购买成功",
          icon: "success",
          duration: 2e3
        });
        this.userRefreshCount += packageInfo.count;
      }, 1500);
    }
  }
};
if (!Array) {
  const _component_fab_buttons = common_vendor.resolveComponent("fab-buttons");
  _component_fab_buttons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$5,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: $data.statusBarHeight + "px",
    d: common_assets._imports_1$13,
    e: common_vendor.t($data.userRefreshCount),
    f: common_vendor.t($data.expiryDate),
    g: common_vendor.o((...args) => $options.navigateToPublish && $options.navigateToPublish(...args)),
    h: $data.navbarHeight + 20 + "px",
    i: common_vendor.f($data.packageList, (item, index, i0) => {
      return common_vendor.e({
        a: $data.selectedPackage === index ? 1 : "",
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.count),
        d: item.feature
      }, item.feature ? {
        e: common_vendor.t(item.feature)
      } : {}, {
        f: common_vendor.t(item.price),
        g: index,
        h: $data.selectedPackage === index ? 1 : "",
        i: common_vendor.o(($event) => $options.selectPackage(index), index)
      });
    }),
    j: common_vendor.f($data.faqList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.question),
        b: item.isOpen ? 1 : "",
        c: item.isOpen
      }, item.isOpen ? {
        d: common_vendor.t(item.answer)
      } : {}, {
        e: index,
        f: common_vendor.o(($event) => $options.toggleFaq(index), index)
      });
    }),
    k: common_assets._imports_0$14,
    l: common_vendor.t($options.selectedPrice),
    m: common_vendor.o((...args) => $options.handleBuy && $options.handleBuy(...args)),
    n: common_vendor.p({
      pageName: "refresh"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/refresh-package.js.map
