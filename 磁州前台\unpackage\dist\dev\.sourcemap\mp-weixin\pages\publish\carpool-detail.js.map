{"version": 3, "file": "carpool-detail.js", "sources": ["pages/publish/carpool-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9jYXJwb29sLWRldGFpbC52dWU"], "sourcesContent": ["<template>\n  <view class=\"detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\" style=\"filter: brightness(0) invert(1);\"></image>\n          </view>\n      <view class=\"navbar-title\">拼车详情</view>\n      <view class=\"navbar-right\" @click=\"showActionSheet\">\n        <image src=\"/static/images/tabbar/more-vertical.png\" class=\"more-icon\" style=\"filter: brightness(0) invert(1);\"></image>\n            </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <scroll-view class=\"detail-content\" scroll-y :style=\"{ paddingTop: navigationBarHeight + 'px' }\">\n      <!-- 标签 -->\n      <view class=\"header-tag-container\">\n        <view class=\"header-tag\" :class=\"tripData.type\">\n          <text>{{typeText}}</text>\n          <view class=\"verified-tag\" v-if=\"tripData.isVerified\">已认证</view>\n          </view>\n        </view>\n        \n      <!-- 信息头部 -->\n      <view class=\"detail-header\">\n        <!-- 行程信息 -->\n        <view class=\"route-card\">\n          <view class=\"route-points\">\n            <view class=\"route-point\">\n              <view class=\"point-dot start\"></view>\n            <view class=\"point-info\">\n              <text class=\"point-name\">{{tripData.startPoint}}</text>\n                <text class=\"point-address\" v-if=\"tripData.startAddress\">（{{tripData.startAddress}}）</text>\n            </view>\n          </view>\n            \n            <view class=\"route-divider\">\n              <view class=\"divider-line\"></view>\n              <view class=\"divider-info\">\n                <text class=\"divider-text\">约{{tripData.distance}}公里</text>\n              </view>\n            </view>\n            \n            <view class=\"route-point\">\n              <view class=\"point-dot end\"></view>\n            <view class=\"point-info\">\n              <text class=\"point-name\">{{tripData.endPoint}}</text>\n                <text class=\"point-address\" v-if=\"tripData.endAddress\">（{{tripData.endAddress}}）</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 行程详情 -->\n        <view class=\"trip-details-card\">\n          <view class=\"trip-details-header\">\n            <text class=\"section-title\">行程详情</text>\n          </view>\n          \n          <view class=\"trip-details-content\">\n            <view class=\"trip-details-item\">\n              <text class=\"trip-details-label\">上车地点</text>\n              <text class=\"trip-details-value\">{{tripData.startPoint}}</text>\n          </view>\n            <view class=\"trip-details-item\">\n              <text class=\"trip-details-label\">下车地点</text>\n              <text class=\"trip-details-value\">{{tripData.endPoint}}</text>\n          </view>\n            <view class=\"trip-details-item\">\n              <text class=\"trip-details-label\">行程距离</text>\n              <text class=\"trip-details-value\">{{tripData.distance}}公里</text>\n          </view>\n            <view class=\"trip-details-item\">\n              <text class=\"trip-details-label\">预计用时</text>\n              <text class=\"trip-details-value\">约{{tripData.duration}}小时</text>\n            </view>\n            <view class=\"trip-details-item\" v-if=\"tripData.tripNotes\">\n              <text class=\"trip-details-label\">备注</text>\n              <text class=\"trip-details-value\">{{tripData.tripNotes}}</text>\n            </view>\n        </view>\n      </view>\n      \n        <!-- 出发时间 -->\n        <view class=\"info-card\">\n          <view class=\"info-item-new\">\n            <text class=\"info-label-new\">出发时间</text>\n            <text class=\"info-value-new\">{{tripData.date}} {{tripData.startTime}}</text>\n          </view>\n          \n          <view class=\"info-item-new\" v-if=\"tripData.type === 'people-to-car'\">\n            <text class=\"info-label-new\">乘车人数</text>\n            <text class=\"info-value-new\">{{tripData.passengers}}人</text>\n        </view>\n          \n          <view class=\"info-item-new\" v-if=\"tripData.type === 'car-to-people'\">\n            <text class=\"info-label-new\">空余座位</text>\n            <text class=\"info-value-new\">{{tripData.remainingSeats}}个</text>\n      </view>\n      \n          <view class=\"info-item-new\" v-if=\"tripData.price\">\n            <text class=\"info-label-new\">参考价格</text>\n            <text class=\"info-value-new price-value\">¥{{tripData.price}}</text>\n            </view>\n          </view>\n        </view>\n      \n      <!-- 用户信息 -->\n      <view class=\"user-card\">\n        <view class=\"user-header\">\n          <text class=\"section-title\">发布人信息</text>\n      </view>\n      \n        <view class=\"user-info\">\n          <image class=\"user-avatar\" :src=\"tripData.driver.avatar\" mode=\"aspectFill\"></image>\n          <view class=\"user-details\">\n            <view class=\"user-name-row\">\n              <text class=\"user-name\">{{tripData.driver.name}}</text>\n              <view class=\"user-badges\">\n                <view class=\"user-badge verified\" v-if=\"tripData.driver.isVerified\">已认证</view>\n                <view class=\"user-badge premium\" v-if=\"tripData.driver.isPremium\">置顶</view>\n          </view>\n            </view>\n            <text class=\"user-meta\">{{formatTime(tripData.publishTime)}} 发布</text>\n          </view>\n          </view>\n        \n        <!-- 司机评分 - 仅在车找人类型显示 -->\n        <view class=\"driver-rating\" v-if=\"tripData.type === 'car-to-people'\">\n          <view class=\"rating-header\">\n            <text class=\"rating-title\">司机评分</text>\n            <text class=\"rating-count\">{{tripData.driver.ratingCount || 0}}人评价</text>\n        </view>\n          <view class=\"rating-stars\">\n            <view class=\"star-container\">\n              <view class=\"star-fill\" :style=\"{ width: (tripData.driver.rating || 0) * 20 + '%' }\"></view>\n              <view class=\"star-bg\"></view>\n          </view>\n            <text class=\"rating-value\">{{tripData.driver.rating || 0}}</text>\n          </view>\n          <view class=\"rating-tags\" v-if=\"tripData.driver.ratingTags && tripData.driver.ratingTags.length > 0\">\n            <view class=\"rating-tag\" v-for=\"(tag, index) in tripData.driver.ratingTags\" :key=\"index\">\n              {{tag}}\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 车辆信息 -->\n      <view class=\"car-card\" v-if=\"tripData.type && tripData.type.includes('car-to')\">\n        <view class=\"car-header\">\n          <text class=\"section-title\">车辆信息</text>\n        </view>\n        \n        <view class=\"car-info\">\n          <view class=\"car-item\">\n            <text class=\"car-label\">车型</text>\n            <text class=\"car-value\">{{tripData.carType}}</text>\n          </view>\n          <view class=\"car-item\">\n            <text class=\"car-label\">颜色</text>\n            <text class=\"car-value\">{{tripData.carColor || '未提供'}}</text>\n          </view>\n          <view class=\"car-item\">\n            <text class=\"car-label\">车牌</text>\n            <text class=\"car-value\">{{tripData.carPlate || '未提供'}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 补充说明 -->\n      <view class=\"remark-card\" v-if=\"tripData.remark\">\n        <view class=\"remark-header\">\n          <text class=\"section-title\">补充说明</text>\n        </view>\n        \n        <view class=\"remark-content\">\n          <text>{{tripData.remark}}</text>\n              </view>\n              </view>\n      \n      <!-- 免责声明 -->\n      <view class=\"disclaimer-card\">\n        <view class=\"disclaimer-header\">\n          <text class=\"section-title\">免责声明</text>\n            </view>\n        <view class=\"disclaimer-content\">\n          <view class=\"disclaimer-icon\">\n            <image src=\"/static/images/tabbar/声明.png\" mode=\"aspectFit\"></image>\n          </view>\n          <text class=\"disclaimer-text\">本平台仅提供信息对接服务，不对拼车双方的行为提供任何担保，不承担任何法律责任。请用户自行甄别信息真实性，注意出行安全。使用本平台即表示您已同意以上条款。</text>\n          </view>\n        </view>\n        \n      <!-- 添加举报卡片 -->\n      <report-card :content-id=\"tripData.id\" content-type=\"carpool\"></report-card>\n        \n      <!-- 发布时间 -->\n      <view class=\"publish-time-card\">\n        <view class=\"publish-time\">\n          <text>发布时间：{{formatTime(tripData.publishTime)}}</text>\n          <text>信息有效期至：{{tripData.expiryTime || '未设置'}}</text>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-actions\">\n      <view class=\"action-group\">\n        <button class=\"action-btn share\" open-type=\"share\">\n          <image src=\"/static/images/tabbar/a分享.png\" mode=\"aspectFit\"></image>\n          <text>转发</text>\n        </button>\n        <button class=\"action-btn favorite\" @click=\"toggleCollect\">\n          <image :src=\"isCollected ? '/static/images/tabbar/a收藏选中.png' : '/static/images/tabbar/a收藏.png'\" mode=\"aspectFit\"></image>\n          <text>{{isCollected ? '已收藏' : '收藏'}}</text>\n        </button>\n        <button class=\"action-btn rate\" @click=\"rateDriver\">\n          <image src=\"/static/images/tabbar/a评价.png\" mode=\"aspectFit\"></image>\n          <text>评价</text>\n        </button>\n        <button class=\"action-btn message\" @click=\"openChat\">\n          <image src=\"/static/images/tabbar/a私信.png\" mode=\"aspectFit\"></image>\n          <text>私信</text>\n        </button>\n      </view>\n      <view class=\"call-btn-container\">\n        <button class=\"call-btn\" @click=\"callPhone\">\n          <image src=\"/static/images/tabbar/电话.png\" mode=\"aspectFit\" style=\"filter: brightness(0) invert(1);\"></image>\n          <text>联系司机</text>\n      </button>\n      </view>\n        </view>\n    \n    <!-- 安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue'\nimport ReportCard from '@/components/ReportCard.vue'\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst statusBarHeight = ref(20);\nconst navigationBarHeight = ref(60);\n\n// 计算状态栏和导航栏高度\nconst getSystemInfo = () => {\n  uni.getSystemInfo({\n    success: (res) => {\n      statusBarHeight.value = res.statusBarHeight || 20;\n      // 导航栏高度：状态栏 + 标题栏(44px)\n      navigationBarHeight.value = statusBarHeight.value + 44;\n    }\n  });\n};\n\nconst tripData = ref({\n  id: 'trip12345',\n  type: 'car-to-people', // 'car-to-people' 或 'people-to-car'\n  title: '磁县→邯郸',\n  price: '50',\n  tags: ['准点发车', '舒适车型', '可带行李'],\n  publishTime: Date.now() - 86400000 * 2, // 2天前\n  startPoint: '磁县汽车站',\n  startAddress: '河北省邯郸市磁县磁州镇汽车站',\n  endPoint: '邯郸东站',\n  endAddress: '河北省邯郸市丛台区邯郸东站',\n  startTime: '08:00',\n  endTime: '09:30',\n  date: '2024-03-20',\n  distance: '45',\n  duration: '1.5',\n  remainingSeats: 3,\n  carType: '舒适型轿车',\n  carColor: '白色',\n  carPlate: '冀D·12345',\n  tripNotes: '可提前预约，支持改签',\n  isVerified: true,\n  driver: {\n    name: '王师傅',\n    avatar: '/static/images/avatar.png',\n    type: '专业司机',\n    isVerified: true,\n    isPremium: true,\n    rating: 4.8,\n    ratingCount: 56,\n    ratingTags: ['准时', '礼貌', '车内整洁'],\n    trips: 128,\n    years: 5\n  },\n  contact: {\n    name: '王师傅',\n    phone: '138****1234'\n  },\n  expiryTime: '2024-03-25'\n});\n\n// 计算属性\nconst typeText = computed(() => {\n  if (tripData.value.type === 'car-to-people') {\n    return '车找人';\n  } else if (tripData.value.type === 'people-to-car') {\n    return '人找车';\n  }\n  return '拼车信息';\n});\n\n// 方法\nconst goBack = () => {\n  uni.navigateBack();\n};\n\nconst showActionSheet = () => {\n  uni.showActionSheet({\n    itemList: ['举报', '分享', '收藏'],\n    success: function (res) {\n      if (res.tapIndex === 0) {\n        // 举报\n    uni.showToast({\n          title: '举报功能开发中',\n          icon: 'none'\n    });\n      } else if (res.tapIndex === 1) {\n        // 分享\n  uni.showShareMenu({\n          withShareTicket: true\n        });\n      } else if (res.tapIndex === 2) {\n        // 收藏\n        toggleCollect();\n      }\n    }\n  });\n};\n\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  uni.showToast({\n    title: isCollected.value ? '已收藏' : '已取消收藏',\n    icon: 'none'\n  });\n};\n\nconst callPhone = () => {\n  if (tripData.value.contact.phone) {\n  uni.makePhoneCall({\n      phoneNumber: tripData.value.contact.phone.replace(/\\*+/g, '')\n    });\n  } else {\n      uni.showToast({\n      title: '电话号码不可用',\n        icon: 'none'\n      });\n    }\n};\n\nconst openChat = () => {\n  uni.showToast({\n    title: '私信功能开发中',\n    icon: 'none'\n  });\n};\n\nconst rateDriver = () => {\n  uni.showToast({\n    title: '评价功能开发中',\n    icon: 'none'\n  });\n};\n\n// 页面加载\nonMounted(() => {\n  // 获取系统信息和状态栏高度\n  getSystemInfo();\n  \n  // 获取页面参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  if (options.id) {\n    // 这里应该根据ID从服务器获取拼车信息\n    console.log('获取拼车信息，ID:', options.id);\n    // fetchCarpoolInfo(options.id);\n  }\n});\n\n// 分享功能\ndefineExpose({\n  onShareAppMessage() {\n    return {\n      title: `${tripData.value.startPoint}→${tripData.value.endPoint} ${tripData.value.date} ${tripData.value.startTime}出发`,\n      path: `/pages/publish/carpool-detail?id=${tripData.value.id}`\n    };\n  }\n});\n</script>\n\n<style>\n.detail-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left, .navbar-right {\n  width: 32px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon, .more-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.navbar-title {\n  color: #FFFFFF;\n  font-size: 18px;\n  font-weight: 600;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n/* 内容区域 */\n.detail-content {\n  flex: 1;\n  box-sizing: border-box;\n}\n\n/* 标签 */\n.header-tag-container {\n  padding: 15rpx 30rpx 10rpx;\n  margin-top: 0;\n}\n\n.header-tag {\n  display: inline-flex;\n  align-items: center;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 14px;\n  color: #ffffff;\n}\n\n.car-to-people {\n  background-color: #1989fa;\n}\n\n.people-to-car {\n  background-color: #ff6b00;\n}\n\n.verified-tag {\n  margin-left: 8px;\n  padding: 2px 6px;\n  background-color: rgba(255, 255, 255, 0.2);\n  border-radius: 10px;\n  font-size: 12px;\n}\n\n/* 行程信息 */\n.route-card {\n  margin: 0 30rpx 30rpx;\n  padding: 20rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.route-points {\n  position: relative;\n}\n\n.route-point {\n  display: flex;\n  align-items: center;\n  padding: 10px 0;\n}\n\n.point-dot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  margin-right: 10px;\n}\n\n.start {\n  background-color: #1989fa;\n}\n\n.end {\n  background-color: #ff6b00;\n}\n\n.point-info {\n  flex: 1;\n}\n\n.point-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.point-address {\n  font-size: 12px;\n  color: #999;\n  margin-top: 2px;\n}\n\n.route-divider {\n  padding-left: 6px;\n  margin: 5px 0;\n  height: 30px;\n  position: relative;\n}\n\n.divider-line {\n  position: absolute;\n  left: 6px;\n  top: 0;\n  bottom: 0;\n  width: 1px;\n  background-color: #ddd;\n}\n\n.divider-info {\n  margin-left: 20px;\n}\n\n.divider-text {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 行程详情 */\n.trip-details-card, .info-card, .user-card, .car-card, .remark-card, .disclaimer-card {\n  margin: 0 30rpx 30rpx;\n  padding: 20rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.trip-details-content {\n  margin-top: 10px;\n}\n\n.trip-details-item {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.trip-details-label {\n  width: 80px;\n  color: #666;\n  font-size: 14px;\n}\n\n.trip-details-value {\n  flex: 1;\n  color: #333;\n  font-size: 14px;\n}\n\n/* 信息卡片 */\n.info-item-new {\n  display: flex;\n  justify-content: space-between;\n  padding: 10px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.info-item-new:last-child {\n  border-bottom: none;\n}\n\n.info-label-new {\n  color: #666;\n  font-size: 14px;\n}\n\n.info-value-new {\n  color: #333;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.price-value {\n  color: #ff6b00;\n}\n\n/* 用户信息 */\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-top: 10px;\n}\n\n.user-avatar {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  margin-right: 15px;\n}\n\n.user-details {\n  flex: 1;\n}\n\n.user-name-row {\n  display: flex;\n  align-items: center;\n}\n\n.user-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-right: 10px;\n}\n\n.user-badges {\n  display: flex;\n}\n\n.user-badge {\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 12px;\n  margin-right: 5px;\n}\n\n.verified {\n  background-color: #1989fa;\n  color: #fff;\n}\n\n.premium {\n  background-color: #ff6b00;\n  color: #fff;\n}\n\n.user-meta {\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n/* 司机评分 */\n.driver-rating {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.rating-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.rating-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n}\n\n.rating-count {\n  font-size: 12px;\n  color: #999;\n}\n\n.rating-stars {\n  display: flex;\n  align-items: center;\n}\n\n.star-container {\n  position: relative;\n  width: 100px;\n  height: 20px;\n  margin-right: 10px;\n}\n\n.star-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('../../../static/images/tabbar/star-bg.png') repeat-x;\n  background-size: 20px 20px;\n}\n\n.star-fill {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  background: url('../../../static/images/tabbar/star-fill.png') repeat-x;\n  background-size: 20px 20px;\n}\n\n.rating-value {\n  font-size: 16px;\n  font-weight: 500;\n  color: #ff6b00;\n}\n\n.rating-tags {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.rating-tag {\n  padding: 4px 8px;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n  font-size: 12px;\n  color: #666;\n  margin-right: 8px;\n  margin-bottom: 8px;\n}\n\n/* 车辆信息 */\n.car-info {\n  margin-top: 10px;\n}\n\n.car-item {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.car-label {\n  width: 80px;\n  color: #666;\n  font-size: 14px;\n}\n\n.car-value {\n  flex: 1;\n  color: #333;\n  font-size: 14px;\n}\n\n/* 补充说明 */\n.remark-content {\n  font-size: 14px;\n  color: #333;\n  line-height: 1.5;\n}\n\n/* 免责声明 */\n.disclaimer-content {\n  display: flex;\n  align-items: flex-start;\n}\n\n.disclaimer-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 10px;\n}\n\n.disclaimer-icon image {\n  width: 100%;\n  height: 100%;\n}\n\n.disclaimer-text {\n  flex: 1;\n  font-size: 12px;\n  color: #999;\n  line-height: 1.5;\n}\n\n/* 发布时间 */\n.publish-time-card {\n  margin: 0 30rpx 30rpx;\n  padding: 10rpx 15rpx;\n}\n\n.publish-time {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.publish-time text {\n  font-size: 12px;\n  color: #999;\n  line-height: 1.5;\n}\n\n/* 底部操作栏 */\n.bottom-actions {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  display: flex;\n  padding: 10px 15px;\n  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);\n  z-index: 99;\n}\n\n.action-group {\n  display: flex;\n  flex: 1;\n}\n\n.action-btn {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  background-color: transparent;\n  flex: 1;\n  height: 60px;\n  line-height: 1;\n}\n\n.action-btn::after {\n  border: none;\n}\n\n.action-btn image {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 5px;\n}\n\n.action-btn text {\n  font-size: 12px;\n  color: #666;\n}\n\n.call-btn-container {\n  width: 120px;\n  margin-left: 10px;\n}\n\n.call-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 60px;\n  background-color: #1989fa;\n  color: #fff;\n  border-radius: 4px;\n  font-size: 16px;\n  line-height: 1;\n}\n\n.call-btn::after {\n  border: none;\n}\n\n.call-btn image {\n  width: 20px;\n  height: 20px;\n  margin-right: 5px;\n}\n\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom, 0);\n  background-color: #fff;\n}\n\n.carpool-detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/carpool-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "computed", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AAiPA,MAAA,aAAA,MAAA;;;;AAGA,UAAA,aAAA,CAAA,cAAA;AACA,YAAA,OAAA,IAAA,KAAA,SAAA;AACA,aAAA,GAAA,KAAA,aAAA,CAAA,IAAA,KAAA,SAAA;AAAA,IACA;AAGA,UAAA,cAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,kBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,sBAAAA,cAAAA,IAAA,EAAA;AAGA,UAAA,gBAAA,MAAA;AACAC,oBAAAA,MAAA,cAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,0BAAA,QAAA,IAAA,mBAAA;AAEA,8BAAA,QAAA,gBAAA,QAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,WAAAD,cAAAA,IAAA;AAAA,MACA,IAAA;AAAA,MACA,MAAA;AAAA;AAAA,MACA,OAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA,CAAA,QAAA,QAAA,MAAA;AAAA,MACA,aAAA,KAAA,IAAA,IAAA,QAAA;AAAA;AAAA,MACA,YAAA;AAAA,MACA,cAAA;AAAA,MACA,UAAA;AAAA,MACA,YAAA;AAAA,MACA,WAAA;AAAA,MACA,SAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,gBAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,MACA,QAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,QACA,MAAA;AAAA,QACA,YAAA;AAAA,QACA,WAAA;AAAA,QACA,QAAA;AAAA,QACA,aAAA;AAAA,QACA,YAAA,CAAA,MAAA,MAAA,MAAA;AAAA,QACA,OAAA;AAAA,QACA,OAAA;AAAA,MACA;AAAA,MACA,SAAA;AAAA,QACA,MAAA;AAAA,QACA,OAAA;AAAA,MACA;AAAA,MACA,YAAA;AAAA,IACA,CAAA;AAGA,UAAA,WAAAE,cAAA,SAAA,MAAA;AACA,UAAA,SAAA,MAAA,SAAA,iBAAA;AACA,eAAA;AAAA,MACA,WAAA,SAAA,MAAA,SAAA,iBAAA;AACA,eAAA;AAAA,MACA;AACA,aAAA;AAAA,IACA,CAAA;AAGA,UAAA,SAAA,MAAA;AACAD,oBAAA,MAAA,aAAA;AAAA,IACA;AAEA,UAAA,kBAAA,MAAA;AACAA,oBAAAA,MAAA,gBAAA;AAAA,QACA,UAAA,CAAA,MAAA,MAAA,IAAA;AAAA,QACA,SAAA,SAAA,KAAA;AACA,cAAA,IAAA,aAAA,GAAA;AAEAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA,YACA,CAAA;AAAA,UACA,WAAA,IAAA,aAAA,GAAA;AAEAA,0BAAAA,MAAA,cAAA;AAAA,cACA,iBAAA;AAAA,YACA,CAAA;AAAA,UACA,WAAA,IAAA,aAAA,GAAA;AAEA;UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,gBAAA,MAAA;AACA,kBAAA,QAAA,CAAA,YAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA,YAAA,QAAA,QAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,YAAA,MAAA;AACA,UAAA,SAAA,MAAA,QAAA,OAAA;AACAA,sBAAAA,MAAA,cAAA;AAAA,UACA,aAAA,SAAA,MAAA,QAAA,MAAA,QAAA,QAAA,EAAA;AAAA,QACA,CAAA;AAAA,MACA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,WAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,aAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGAE,kBAAAA,UAAA,MAAA;AAEA;AAGA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,UAAA,YAAA,WAAA;AAEA,UAAA,QAAA,IAAA;AAEAF,sBAAA,MAAA,MAAA,OAAA,2CAAA,cAAA,QAAA,EAAA;AAAA,MAEA;AAAA,IACA,CAAA;AAGA,aAAA;AAAA,MACA,oBAAA;AACA,eAAA;AAAA,UACA,OAAA,GAAA,SAAA,MAAA,UAAA,IAAA,SAAA,MAAA,QAAA,IAAA,SAAA,MAAA,IAAA,IAAA,SAAA,MAAA,SAAA;AAAA,UACA,MAAA,oCAAA,SAAA,MAAA,EAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnZA,GAAG,WAAWG,SAAe;"}