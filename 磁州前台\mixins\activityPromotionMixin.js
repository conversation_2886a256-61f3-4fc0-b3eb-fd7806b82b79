/**
 * 活动推广混入
 * 为各类活动详情页提供推广能力
 */
import basePromotionMixin from './basePromotionMixin';

export default {
  mixins: [basePromotionMixin],

  data() {
    return {
      // 设置页面类型为活动
      pageType: 'activity'
    };
  },

  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 获取当前用户ID
      const currentUserId = this.$store?.state?.user?.userId || '';
      // 获取活动组织者ID
      const organizerId = this.activityDetail?.organizerId || this.activity?.organizerId || '';
      // 获取活动商家ID
      const merchantId = this.activityDetail?.merchantId || this.activity?.merchantId || '';
      // 获取活动运营权限
      const isOperator = this.$store?.state?.user?.permissions?.includes('ACTIVITY_OPERATION') || false;

      // 判断当前用户是否是活动组织者、商家或有运营权限
      return (currentUserId && organizerId && currentUserId === organizerId) || 
             (currentUserId && merchantId && currentUserId === merchantId) || 
             isOperator;
    },

    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 判断活动是否可分销
      const canDistribute = this.activityDetail?.canDistribute || this.activity?.canDistribute || false;
      // 是否有分销设置
      const hasDistributionSetting = !!(this.activityDetail?.commissionRate || this.activity?.commissionRate);
      // 是否是付费活动
      const isPaid = !!(this.activityDetail?.price || this.activity?.price);

      return (canDistribute || hasDistributionSetting) && isPaid;
    },

    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      // 获取活动数据
      const activity = this.activityDetail || this.activity || {};
      
      // 构建推广数据
      this.promotionData = {
        id: activity.id || '',
        title: activity.title || activity.name || '',
        image: activity.coverImage || activity.cover || activity.image || '',
        startTime: activity.startTime || activity.beginTime || '',
        endTime: activity.endTime || '',
        location: activity.location || activity.address || '',
        organizer: activity.organizer || activity.organizerName || '',
        price: activity.price || 0,
        // 如果有更多活动特定字段，可以在这里添加
      };
    },

    /**
     * 显示活动推广浮层
     */
    showActivityPromotion() {
      // 如果没有推广权限，显示提示
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '暂无推广权限',
          icon: 'none'
        });
        return;
      }

      // 打开推广工具
      this.openPromotionTools();
    }
  }
}; 