{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/distribution/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzdHJpYnV0aW9uXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"distribution-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"navigateBack\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n        <view class=\"navbar-title\">分销中心</view>\r\n        <view class=\"navbar-right\">\r\n          <view class=\"help-btn\" @click=\"showHelp\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n              <path d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              <line x1=\"12\" y1=\"17\" x2=\"12.01\" y2=\"17\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 内容区域 -->\r\n    <scroll-view \r\n      class=\"content-scroll\" \r\n      scroll-y=\"true\"\r\n      :scroll-anchoring=\"true\"\r\n      :enhanced=\"true\"\r\n      :bounces=\"true\"\r\n      :show-scrollbar=\"false\"\r\n      @scrolltolower=\"loadMore\"\r\n    >\r\n      <!-- 分销员信息卡片 -->\r\n      <view class=\"distributor-card\" :style=\"{\r\n        borderRadius: '35px',\r\n        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n        background: 'linear-gradient(135deg, #FFFFFF 0%, #F9F5FF 100%)',\r\n        padding: '30rpx',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"distributor-header\">\r\n          <view class=\"distributor-avatar-container\">\r\n            <image class=\"distributor-avatar\" :src=\"distributorInfo.avatar\" mode=\"aspectFill\"></image>\r\n            <view class=\"distributor-badge\" v-if=\"distributorInfo.isVerified\" :style=\"{\r\n              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'\r\n            }\">\r\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"12\" height=\"12\">\r\n                <path d=\"M9 12l2 2 4-4\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </view>\r\n          </view>\r\n          <view class=\"distributor-info\">\r\n            <view class=\"distributor-name-container\">\r\n              <text class=\"distributor-name\">{{ distributorInfo.name }}</text>\r\n              <view class=\"distributor-level\" :style=\"{\r\n                background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'\r\n              }\">\r\n                <text>{{ distributorInfo.level }}</text>\r\n              </view>\r\n            </view>\r\n            <text class=\"distributor-id\">ID: {{ distributorInfo.id }}</text>\r\n            <view class=\"distributor-stats\">\r\n              <text class=\"stat-item\">已推广 {{ distributorInfo.promotedDays }} 天</text>\r\n              <text class=\"stat-divider\">|</text>\r\n              <text class=\"stat-item\">团队 {{ distributorInfo.teamCount }} 人</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"qrcode-btn\" @click=\"showQrcode\" :style=\"{\r\n            background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'\r\n          }\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n              <path d=\"M3 3h7v7H3z M14 3h7v7h-7z M3 14h7v7H3z M17 17h2v2h-2z M14 14h2v2h-2z M19 14h2v2h-2z M14 19h7v2h-7z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n            <text>推广码</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"level-progress\">\r\n          <view class=\"level-progress-header\">\r\n            <text class=\"level-text\">{{ distributorInfo.level }}</text>\r\n            <text class=\"next-level-text\">{{ distributorInfo.nextLevel }}</text>\r\n          </view>\r\n          <view class=\"progress-bar-bg\" :style=\"{\r\n            background: 'rgba(172,57,255,0.2)',\r\n            borderRadius: '10rpx',\r\n            height: '10rpx',\r\n            width: '100%'\r\n          }\">\r\n            <view class=\"progress-bar-fill\" :style=\"{\r\n              width: distributorInfo.upgradeProgress + '%',\r\n              background: 'linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%)',\r\n              borderRadius: '10rpx',\r\n              height: '100%'\r\n            }\"></view>\r\n          </view>\r\n          <text class=\"progress-hint\">再推广{{ distributorInfo.ordersToUpgrade }}个订单升级为{{ distributorInfo.nextLevel }}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 收益概览卡片 -->\r\n      <view class=\"earnings-card\" :style=\"{\r\n        borderRadius: '35px',\r\n        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n        background: '#FFFFFF',\r\n        padding: '30rpx',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">收益概览</text>\r\n          <view class=\"header-right\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/earnings/index')\">\r\n            <text class=\"view-all\">查看全部</text>\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#AC39FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"total-earnings\">\r\n          <view class=\"earnings-label\">\r\n            <text>累计收益(元)</text>\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" @click=\"showEarningsTip\">\r\n              <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n              <path d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              <line x1=\"12\" y1=\"17\" x2=\"12.01\" y2=\"17\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\r\n            </svg>\r\n          </view>\r\n          <text class=\"earnings-value\">{{ distributorInfo.totalEarnings }}</text>\r\n          <view class=\"earnings-trend\">\r\n            <text class=\"trend-value\">较上月 </text>\r\n            <text class=\"trend-up\" v-if=\"distributorInfo.earningsTrend > 0\">+{{ distributorInfo.earningsTrend }}%</text>\r\n            <text class=\"trend-down\" v-else>{{ distributorInfo.earningsTrend }}%</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"earnings-grid\">\r\n          <view class=\"earnings-grid-item\">\r\n            <text class=\"grid-value\">{{ distributorInfo.todayEarnings }}</text>\r\n            <text class=\"grid-label\">今日收益(元)</text>\r\n          </view>\r\n          <view class=\"earnings-grid-item\">\r\n            <text class=\"grid-value\">{{ distributorInfo.pendingEarnings }}</text>\r\n            <text class=\"grid-label\">待结算(元)</text>\r\n          </view>\r\n          <view class=\"earnings-grid-item\">\r\n            <text class=\"grid-value\">{{ distributorInfo.withdrawableAmount }}</text>\r\n            <text class=\"grid-label\">可提现(元)</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"action-buttons\">\r\n          <view class=\"action-btn withdraw-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/withdraw/index')\" :style=\"{\r\n            background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'\r\n          }\">\r\n            <text>立即提现</text>\r\n          </view>\r\n          <view class=\"action-btn record-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/records/index')\">\r\n            <text>收益明细</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 功能入口 -->\r\n      <view class=\"feature-grid\" :style=\"{\r\n        borderRadius: '35px',\r\n        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n        background: '#FFFFFF',\r\n        padding: '30rpx',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"feature-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/orders/index')\">\r\n          <view class=\"feature-icon\" :style=\"{\r\n            background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)'\r\n          }\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path d=\"M9 17h6M9 13h6M9 9h6M5 21V5a2 2 0 012-2h10a2 2 0 012 2v16l-3-2-2 2-2-2-2 2-2-2-3 2z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n          <text class=\"feature-name\">分销订单</text>\r\n          <text class=\"feature-count\">{{ orderStats.totalCount }}单</text>\r\n        </view>\r\n        \r\n        <view class=\"feature-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/team/index')\">\r\n          <view class=\"feature-icon\" :style=\"{\r\n            background: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)'\r\n          }\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path d=\"M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n          <text class=\"feature-name\">我的团队</text>\r\n          <text class=\"feature-count\">{{ distributorInfo.teamCount }}人</text>\r\n        </view>\r\n        \r\n        <view class=\"feature-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/poster/index')\">\r\n          <view class=\"feature-icon\" :style=\"{\r\n            background: 'linear-gradient(135deg, #5AC8FA 0%, #1C84FF 100%)'\r\n          }\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></rect>\r\n              <circle cx=\"8.5\" cy=\"8.5\" r=\"1.5\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n              <path d=\"M21 15l-5-5L5 21\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n          <text class=\"feature-name\">推广海报</text>\r\n          <text class=\"feature-count\">{{ posterStats.count }}张</text>\r\n        </view>\r\n        \r\n        <view class=\"feature-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/products/index')\">\r\n          <view class=\"feature-icon\" :style=\"{\r\n            background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)'\r\n          }\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <circle cx=\"9\" cy=\"21\" r=\"1\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n              <circle cx=\"20\" cy=\"21\" r=\"1\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\r\n              <path d=\"M1 1h4l2.68 13.39a2 2 0 002 1.61h9.72a2 2 0 002-1.61L23 6H6\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n          <text class=\"feature-name\">推广商品</text>\r\n          <text class=\"feature-count\">{{ productStats.count }}个</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 分销订单 -->\r\n      <view class=\"orders-card\" :style=\"{\r\n        borderRadius: '35px',\r\n        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n        background: '#FFFFFF',\r\n        padding: '30rpx',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">最近订单</text>\r\n          <view class=\"view-all-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/orders/index')\">\r\n            <text>查看全部</text>\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"order-tabs\">\r\n          <view \r\n            v-for=\"(tab, index) in orderTabs\" \r\n            :key=\"index\"\r\n            class=\"order-tab\"\r\n            :class=\"{ active: currentOrderTab === index }\"\r\n            @click=\"switchOrderTab(index)\"\r\n          >\r\n            <text>{{ tab.name }}</text>\r\n            <view class=\"tab-indicator\" v-if=\"currentOrderTab === index\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"order-list\">\r\n          <view \r\n            v-for=\"(order, index) in getOrdersByStatus(orderTabs[currentOrderTab].status)\"\r\n            :key=\"order.id\"\r\n            class=\"order-item\"\r\n            @click=\"navigateTo(`/subPackages/activity-showcase/pages/distribution/order-detail/index?id=${order.id}`)\"\r\n          >\r\n            <view class=\"order-header\">\r\n              <text class=\"order-id\">订单号：{{ order.orderNumber }}</text>\r\n              <text class=\"order-status\" :style=\"{\r\n                color: getOrderStatusColor(order.status)\r\n              }\">{{ getOrderStatusText(order.status) }}</text>\r\n            </view>\r\n            \r\n            <view class=\"order-product\">\r\n              <image class=\"product-image\" :src=\"order.productImage\" mode=\"aspectFill\"></image>\r\n              <view class=\"product-info\">\r\n                <text class=\"product-name\">{{ order.productName }}</text>\r\n                <view class=\"product-price-qty\">\r\n                  <text class=\"product-price\">¥{{ order.productPrice }}</text>\r\n                  <text class=\"product-qty\">x{{ order.quantity }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"order-footer\">\r\n              <view class=\"order-time\">\r\n                <text>{{ order.orderTime }}</text>\r\n              </view>\r\n              <view class=\"order-commission\">\r\n                <text class=\"commission-label\">预计收益：</text>\r\n                <text class=\"commission-value\">¥{{ order.commission }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 空状态 -->\r\n          <view class=\"empty-state\" v-if=\"getOrdersByStatus(orderTabs[currentOrderTab].status).length === 0\">\r\n            <text class=\"empty-text\">暂无{{ orderTabs[currentOrderTab].name }}订单</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 团队概览 -->\r\n      <view class=\"team-card\" :style=\"{\r\n        borderRadius: '35px',\r\n        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n        background: '#FFFFFF',\r\n        padding: '30rpx',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">团队概览</text>\r\n          <view class=\"view-all-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/team/index')\">\r\n            <text>查看全部</text>\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"team-stats\">\r\n          <view class=\"team-stat-item\">\r\n            <text class=\"stat-value\">{{ teamStats.totalMembers }}</text>\r\n            <text class=\"stat-label\">团队总人数</text>\r\n          </view>\r\n          <view class=\"team-stat-item\">\r\n            <text class=\"stat-value\">{{ teamStats.directMembers }}</text>\r\n            <text class=\"stat-label\">直属成员</text>\r\n          </view>\r\n          <view class=\"team-stat-item\">\r\n            <text class=\"stat-value\">{{ teamStats.indirectMembers }}</text>\r\n            <text class=\"stat-label\">间接成员</text>\r\n          </view>\r\n          <view class=\"team-stat-item\">\r\n            <text class=\"stat-value\">{{ teamStats.newMembers }}</text>\r\n            <text class=\"stat-label\">本月新增</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"team-members\">\r\n          <text class=\"section-subtitle\">团队成员</text>\r\n          <view class=\"member-list\">\r\n            <view \r\n              v-for=\"(member, index) in teamMembers\" \r\n              :key=\"member.id\"\r\n              class=\"member-item\"\r\n              @click=\"navigateTo(`/subPackages/activity-showcase/pages/distribution/member-detail/index?id=${member.id}`)\"\r\n            >\r\n              <image class=\"member-avatar\" :src=\"member.avatar\" mode=\"aspectFill\"></image>\r\n              <view class=\"member-info\">\r\n                <view class=\"member-name-level\">\r\n                  <text class=\"member-name\">{{ member.name }}</text>\r\n                  <view class=\"member-level\" :style=\"{\r\n                    background: getLevelColor(member.level)\r\n                  }\">\r\n                    <text>{{ member.level }}</text>\r\n                  </view>\r\n                </view>\r\n                <view class=\"member-stats\">\r\n                  <text class=\"member-stat\">{{ member.joinTime }}</text>\r\n                  <text class=\"member-stat-divider\">|</text>\r\n                  <text class=\"member-stat\">贡献收益: ¥{{ member.contribution }}</text>\r\n                </view>\r\n              </view>\r\n              <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                <path d=\"M9 18l6-6-6-6\" stroke=\"#C7C7CC\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 空状态 -->\r\n          <view class=\"empty-state\" v-if=\"teamMembers.length === 0\">\r\n            <text class=\"empty-text\">暂无团队成员</text>\r\n            <view class=\"action-btn invite-btn\" @click=\"showInviteQrcode\" :style=\"{\r\n              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'\r\n            }\">\r\n              <text>邀请好友加入</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部安全区域 -->\r\n      <view class=\"safe-area-bottom\"></view>\r\n    </scroll-view>\r\n    \r\n    <!-- 底部导航栏 -->\r\n    <view class=\"tabbar\">\r\n      <view \r\n        class=\"tabbar-item\" \r\n        :class=\"{active: currentTabBar === 'home'}\" \r\n        @click=\"switchTabBar('home')\"\r\n        data-tab=\"home\"\r\n      >\r\n        <view class=\"tab-icon home\"></view>\r\n        <text class=\"tabbar-text\">首页</text>\r\n      </view>\r\n      <view \r\n        class=\"tabbar-item\" \r\n        :class=\"{active: currentTabBar === 'discover'}\" \r\n        @click=\"switchTabBar('discover')\"\r\n        data-tab=\"discover\"\r\n      >\r\n        <view class=\"tab-icon discover\"></view>\r\n        <text class=\"tabbar-text\">本地商城</text>\r\n      </view>\r\n      <view \r\n        class=\"tabbar-item\" \r\n        :class=\"{active: currentTabBar === 'distribution'}\" \r\n        @click=\"switchTabBar('distribution')\"\r\n        data-tab=\"distribution\"\r\n      >\r\n        <view class=\"tab-icon distribution\"></view>\r\n        <text class=\"tabbar-text\">分销</text>\r\n      </view>\r\n      <view \r\n        class=\"tabbar-item\" \r\n        :class=\"{active: currentTabBar === 'message'}\" \r\n        @click=\"switchTabBar('message')\"\r\n        data-tab=\"message\"\r\n      >\r\n        <view class=\"tab-icon message\">\r\n          <view class=\"badge\" v-if=\"unreadMessageCount > 0\">{{ unreadMessageCount > 99 ? '99+' : unreadMessageCount }}</view>\r\n        </view>\r\n        <text class=\"tabbar-text\">消息</text>\r\n      </view>\r\n      <view \r\n        class=\"tabbar-item\" \r\n        :class=\"{active: currentTabBar === 'my'}\" \r\n        @click=\"switchTabBar('my')\"\r\n        data-tab=\"my\"\r\n      >\r\n        <view class=\"tab-icon user\"></view>\r\n        <text class=\"tabbar-text\">我的</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\n\r\n// 底部导航栏当前选中项\r\nconst currentTabBar = ref('distribution');\r\n\r\n// 未读消息数量\r\nconst unreadMessageCount = ref(3);\r\n\r\n// 分销员信息\r\nconst distributorInfo = ref({\r\n  name: '张三',\r\n  id: 'D0001',\r\n  avatar: 'https://via.placeholder.com/100',\r\n  isVerified: true,\r\n  level: '初级分销商',\r\n  nextLevel: '中级分销商',\r\n  promotedDays: 30,\r\n  teamCount: 10,\r\n  upgradeProgress: 75,\r\n  ordersToUpgrade: 20,\r\n  totalEarnings: 1234.56,\r\n  pendingEarnings: 123.45,\r\n  withdrawableAmount: 1111.11,\r\n  todayEarnings: 23.45,\r\n  earningsTrend: 15\r\n});\r\n\r\n// 分销订单统计\r\nconst orderStats = ref({\r\n  totalCount: 150,\r\n  todayCount: 20,\r\n  pendingCount: 10,\r\n  completedCount: 120\r\n});\r\n\r\n// 推广海报统计\r\nconst posterStats = ref({\r\n  count: 8\r\n});\r\n\r\n// 推广商品统计\r\nconst productStats = ref({\r\n  count: 80\r\n});\r\n\r\n// 团队统计\r\nconst teamStats = ref({\r\n  totalMembers: 100,\r\n  directMembers: 30,\r\n  indirectMembers: 70,\r\n  newMembers: 15\r\n});\r\n\r\n// 订单标签页\r\nconst currentOrderTab = ref(0);\r\nconst orderTabs = ref([\r\n  { name: '全部', status: 'all' },\r\n  { name: '待付款', status: 'pending_payment' },\r\n  { name: '待发货', status: 'pending_delivery' },\r\n  { name: '已发货', status: 'delivered' },\r\n  { name: '已完成', status: 'completed' },\r\n  { name: '已失效', status: 'invalid' }\r\n]);\r\n\r\n// 团队成员\r\nconst teamMembers = ref([\r\n  { id: 'M001', name: '李四', avatar: 'https://via.placeholder.com/50', level: '初级分销商', joinTime: '2023-01-01', contribution: 123.45 },\r\n  { id: 'M002', name: '王五', avatar: 'https://via.placeholder.com/50', level: '中级分销商', joinTime: '2023-02-15', contribution: 234.56 },\r\n  { id: 'M003', name: '赵六', avatar: 'https://via.placeholder.com/50', level: '高级分销商', joinTime: '2023-03-01', contribution: 345.67 }\r\n]);\r\n\r\n// 返回上一页\r\nconst navigateBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 显示帮助\r\nconst showHelp = () => {\r\n  uni.showToast({\r\n    title: '分销规则说明',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 显示二维码\r\nconst showQrcode = () => {\r\n  uni.showToast({\r\n    title: '显示推广二维码',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 显示收益提示\r\nconst showEarningsTip = () => {\r\n  uni.showToast({\r\n    title: '累计收益包括已结算和未结算的总收益',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 显示邀请二维码\r\nconst showInviteQrcode = () => {\r\n  uni.showToast({\r\n    title: '显示邀请二维码',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 切换订单标签页\r\nconst switchOrderTab = (index) => {\r\n  currentOrderTab.value = index;\r\n};\r\n\r\n// 获取订单状态颜色\r\nconst getOrderStatusColor = (status) => {\r\n  switch(status) {\r\n    case 'pending_payment':\r\n      return '#FF9500';\r\n    case 'pending_delivery':\r\n      return '#5AC8FA';\r\n    case 'delivered':\r\n      return '#34C759';\r\n    case 'completed':\r\n      return '#AC39FF';\r\n    case 'invalid':\r\n      return '#8E8E93';\r\n    default:\r\n      return '#333333';\r\n  }\r\n};\r\n\r\n// 获取订单状态文本\r\nconst getOrderStatusText = (status) => {\r\n  switch(status) {\r\n    case 'pending_payment':\r\n      return '待付款';\r\n    case 'pending_delivery':\r\n      return '待发货';\r\n    case 'delivered':\r\n      return '已发货';\r\n    case 'completed':\r\n      return '已完成';\r\n    case 'invalid':\r\n      return '已失效';\r\n    default:\r\n      return '未知状态';\r\n  }\r\n};\r\n\r\n// 获取等级颜色\r\nconst getLevelColor = (level) => {\r\n  switch(level) {\r\n    case '初级分销商':\r\n      return 'linear-gradient(135deg, #5AC8FA 0%, #1C84FF 100%)';\r\n    case '中级分销商':\r\n      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';\r\n    case '高级分销商':\r\n      return 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)';\r\n    default:\r\n      return 'linear-gradient(135deg, #8E8E93 0%, #C7C7CC 100%)';\r\n  }\r\n};\r\n\r\n// 根据状态获取订单列表\r\nconst getOrdersByStatus = (status) => {\r\n  // 模拟数据\r\n  const allOrders = [\r\n    {\r\n      id: 'O001',\r\n      orderNumber: '20230101001',\r\n      status: 'completed',\r\n      productName: 'iPhone 14 Pro 深空黑 256G',\r\n      productImage: 'https://via.placeholder.com/100',\r\n      productPrice: 7999,\r\n      quantity: 1,\r\n      orderTime: '2023-01-01 12:00:00',\r\n      commission: 399.95\r\n    },\r\n    {\r\n      id: 'O002',\r\n      orderNumber: '20230102001',\r\n      status: 'pending_payment',\r\n      productName: '华为Mate 50 Pro 曜金黑 512G',\r\n      productImage: 'https://via.placeholder.com/100',\r\n      productPrice: 6999,\r\n      quantity: 1,\r\n      orderTime: '2023-01-02 12:00:00',\r\n      commission: 349.95\r\n    },\r\n    {\r\n      id: 'O003',\r\n      orderNumber: '20230103001',\r\n      status: 'pending_delivery',\r\n      productName: '小米12S Ultra 陶瓷白 256G',\r\n      productImage: 'https://via.placeholder.com/100',\r\n      productPrice: 5999,\r\n      quantity: 1,\r\n      orderTime: '2023-01-03 12:00:00',\r\n      commission: 299.95\r\n    },\r\n    {\r\n      id: 'O004',\r\n      orderNumber: '20230104001',\r\n      status: 'delivered',\r\n      productName: 'OPPO Find X5 Pro 陶瓷白 256G',\r\n      productImage: 'https://via.placeholder.com/100',\r\n      productPrice: 4999,\r\n      quantity: 1,\r\n      orderTime: '2023-01-04 12:00:00',\r\n      commission: 249.95\r\n    },\r\n    {\r\n      id: 'O005',\r\n      orderNumber: '20230105001',\r\n      status: 'invalid',\r\n      productName: '三星Galaxy S22 Ultra 幻影黑 512G',\r\n      productImage: 'https://via.placeholder.com/100',\r\n      productPrice: 8999,\r\n      quantity: 1,\r\n      orderTime: '2023-01-05 12:00:00',\r\n      commission: 449.95\r\n    }\r\n  ];\r\n  \r\n  if (status === 'all') {\r\n    return allOrders;\r\n  }\r\n  \r\n  return allOrders.filter(order => order.status === status);\r\n};\r\n\r\n// 页面导航\r\nconst navigateTo = (url) => {\r\n  uni.navigateTo({ url });\r\n};\r\n\r\n// 切换底部导航栏\r\nconst switchTabBar = (tab) => {\r\n  if (currentTabBar.value === tab) return;\r\n      \r\n  currentTabBar.value = tab;\r\n      \r\n  // 根据选中的标签页进行相应的导航\r\n  switch(tab) {\r\n    case 'home':\r\n      uni.reLaunch({\r\n        url: '/subPackages/activity-showcase/pages/index/index',\r\n        fail: (err) => {\r\n          console.error('页面跳转失败:', err);\r\n          uni.showToast({\r\n            title: '页面跳转失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n      break;\r\n    case 'discover':\r\n      uni.navigateTo({\r\n        url: '/subPackages/activity-showcase/pages/discover/index',\r\n        fail: (err) => {\r\n          console.error('页面跳转失败:', err);\r\n          uni.showToast({\r\n            title: '页面跳转失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n      break;\r\n    case 'distribution':\r\n      // 已在分销中心页面，不需要导航\r\n      break;\r\n    case 'message':\r\n      uni.navigateTo({\r\n        url: '/subPackages/activity-showcase/pages/message/index',\r\n        fail: (err) => {\r\n          console.error('页面跳转失败:', err);\r\n          uni.showToast({\r\n            title: '页面跳转失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n      break;\r\n    case 'my':\r\n      uni.navigateTo({\r\n        url: '/subPackages/activity-showcase/pages/my/index',\r\n        fail: (err) => {\r\n          console.error('页面跳转失败:', err);\r\n          uni.showToast({\r\n            title: '页面跳转失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n      break;\r\n  }\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  // 加载更多数据...\r\n};\r\n\r\n// 页面加载\r\nonMounted(() => {\r\n  // 初始化数据\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.distribution-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #F2F2F7;\r\n  position: relative;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);\r\n    backdrop-filter: blur(10px);\r\n    -webkit-backdrop-filter: blur(10px);\r\n    box-shadow: 0 4px 6px rgba(172,57,255,0.15);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding: 0 30rpx;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .navbar-title {\r\n    font-size: 36rpx;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    letter-spacing: 0.5px;\r\n  }\r\n  \r\n  .back-btn, .help-btn {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n    }\r\n  }\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  position: absolute;\r\n  top: calc(var(--status-bar-height, 25px) + 62px);\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 100rpx;\r\n  padding: 30rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 底部安全区域 */\r\n.safe-area-bottom {\r\n  height: 34px; /* iOS 安全区域高度 */\r\n}\r\n\r\n/* 分销员信息卡片 */\r\n.distributor-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  \r\n  .distributor-header {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 30rpx;\r\n    \r\n    .distributor-avatar-container {\r\n      position: relative;\r\n      margin-right: 20rpx;\r\n      \r\n      .distributor-avatar {\r\n        width: 120rpx;\r\n        height: 120rpx;\r\n        border-radius: 50%;\r\n        border: 3rpx solid #FFFFFF;\r\n        box-shadow: 0 4px 10px rgba(0,0,0,0.1);\r\n      }\r\n      \r\n      .distributor-badge {\r\n        position: absolute;\r\n        bottom: 0;\r\n        right: 0;\r\n        width: 36rpx;\r\n        height: 36rpx;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border: 2rpx solid #FFFFFF;\r\n      }\r\n    }\r\n    \r\n    .distributor-info {\r\n      flex: 1;\r\n      \r\n      .distributor-name-container {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 10rpx;\r\n        \r\n        .distributor-name {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #333333;\r\n          margin-right: 10rpx;\r\n        }\r\n        \r\n        .distributor-level {\r\n          padding: 4rpx 12rpx;\r\n          border-radius: 20rpx;\r\n          \r\n          text {\r\n            font-size: 20rpx;\r\n            color: #FFFFFF;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .distributor-id {\r\n        font-size: 24rpx;\r\n        color: #8E8E93;\r\n        margin-bottom: 10rpx;\r\n      }\r\n      \r\n      .distributor-stats {\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        .stat-item {\r\n          font-size: 24rpx;\r\n          color: #8E8E93;\r\n        }\r\n        \r\n        .stat-divider {\r\n          margin: 0 10rpx;\r\n          font-size: 24rpx;\r\n          color: #D1D1D6;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .qrcode-btn {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 100rpx;\r\n      height: 100rpx;\r\n      border-radius: 20rpx;\r\n      \r\n      .icon {\r\n        width: 40rpx;\r\n        height: 40rpx;\r\n        margin-bottom: 5rpx;\r\n      }\r\n      \r\n      text {\r\n        font-size: 20rpx;\r\n        color: #FFFFFF;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .level-progress {\r\n    margin-top: 10rpx;\r\n    \r\n    .level-progress-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin-bottom: 10rpx;\r\n      \r\n      .level-text, .next-level-text {\r\n        font-size: 24rpx;\r\n        color: #333333;\r\n      }\r\n    }\r\n    \r\n    .progress-hint {\r\n      font-size: 22rpx;\r\n      color: #8E8E93;\r\n      margin-top: 10rpx;\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n/* 收益概览卡片 */\r\n.earnings-card {\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .card-title {\r\n      font-size: 32rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n    }\r\n    \r\n    .header-right {\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .view-all {\r\n        font-size: 24rpx;\r\n        color: #AC39FF;\r\n        margin-right: 5rpx;\r\n      }\r\n      \r\n      .icon {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .total-earnings {\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n    \r\n    .earnings-label {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-bottom: 10rpx;\r\n      \r\n      text {\r\n        font-size: 28rpx;\r\n        color: #8E8E93;\r\n        margin-right: 5rpx;\r\n      }\r\n      \r\n      .icon {\r\n        width: 28rpx;\r\n        height: 28rpx;\r\n      }\r\n    }\r\n    \r\n    .earnings-value {\r\n      font-size: 48rpx;\r\n      font-weight: 700;\r\n      color: #AC39FF;\r\n      margin-bottom: 10rpx;\r\n    }\r\n    \r\n    .earnings-trend {\r\n      .trend-value {\r\n        font-size: 24rpx;\r\n        color: #8E8E93;\r\n      }\r\n      \r\n      .trend-up {\r\n        font-size: 24rpx;\r\n        color: #FF3B30;\r\n      }\r\n      \r\n      .trend-down {\r\n        font-size: 24rpx;\r\n        color: #34C759;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .earnings-grid {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    margin-bottom: 30rpx;\r\n    \r\n    .earnings-grid-item {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      \r\n      .grid-value {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #333333;\r\n        margin-bottom: 5rpx;\r\n      }\r\n      \r\n      .grid-label {\r\n        font-size: 24rpx;\r\n        color: #8E8E93;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .action-buttons {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    \r\n    .action-btn {\r\n      flex: 1;\r\n      height: 80rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border-radius: 40rpx;\r\n      \r\n      text {\r\n        font-size: 28rpx;\r\n        font-weight: 500;\r\n      }\r\n      \r\n      &.withdraw-btn {\r\n        margin-right: 15rpx;\r\n        \r\n        text {\r\n          color: #FFFFFF;\r\n        }\r\n      }\r\n      \r\n      &.record-btn {\r\n        border: 2rpx solid #AC39FF;\r\n        \r\n        text {\r\n          color: #AC39FF;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 功能入口 */\r\n.feature-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  gap: 20rpx;\r\n  \r\n  .feature-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    \r\n    .feature-icon {\r\n      width: 80rpx;\r\n      height: 80rpx;\r\n      border-radius: 20rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-bottom: 10rpx;\r\n      \r\n      .icon {\r\n        width: 40rpx;\r\n        height: 40rpx;\r\n      }\r\n    }\r\n    \r\n    .feature-name {\r\n      font-size: 24rpx;\r\n      color: #333333;\r\n      margin-bottom: 5rpx;\r\n    }\r\n    \r\n    .feature-count {\r\n      font-size: 20rpx;\r\n      color: #8E8E93;\r\n    }\r\n  }\r\n}\r\n\r\n/* 卡片头部样式 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n  \r\n  .card-title {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333333;\r\n  }\r\n  \r\n  .header-right {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .view-all {\r\n      font-size: 24rpx;\r\n      color: #AC39FF;\r\n      margin-right: 5rpx;\r\n    }\r\n    \r\n    .icon {\r\n      width: 24rpx;\r\n      height: 24rpx;\r\n    }\r\n  }\r\n  \r\n  .view-all-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);\r\n    border-radius: 30rpx;\r\n    padding: 10rpx 20rpx;\r\n    box-shadow: 0 4rpx 8rpx rgba(172, 57, 255, 0.25);\r\n    transition: all 0.3s ease;\r\n    \r\n    &:active {\r\n      transform: scale(0.95);\r\n      box-shadow: 0 2rpx 4rpx rgba(172, 57, 255, 0.15);\r\n    }\r\n    \r\n    text {\r\n      font-size: 24rpx;\r\n      color: #FFFFFF;\r\n      font-weight: 500;\r\n      margin-right: 5rpx;\r\n    }\r\n    \r\n    .icon {\r\n      width: 24rpx;\r\n      height: 24rpx;\r\n    }\r\n  }\r\n}\r\n\r\n/* 订单卡片 */\r\n.orders-card {\r\n  margin-bottom: 30rpx;\r\n  \r\n  .order-tabs {\r\n    display: flex;\r\n    overflow-x: auto;\r\n    margin: 20rpx 0;\r\n    \r\n    .order-tab {\r\n      padding: 10rpx 20rpx;\r\n      position: relative;\r\n      white-space: nowrap;\r\n      \r\n      text {\r\n        font-size: 28rpx;\r\n        color: #8E8E93;\r\n        transition: color 0.3s ease;\r\n      }\r\n      \r\n      .tab-indicator {\r\n        position: absolute;\r\n        bottom: -5rpx;\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        width: 40rpx;\r\n        height: 3px;\r\n        border-radius: 1.5px;\r\n        background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);\r\n        transition: all 0.3s ease;\r\n      }\r\n      \r\n      &.active {\r\n        text {\r\n          color: #AC39FF;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .order-list {\r\n    .order-item {\r\n      background: #F9F9F9;\r\n      border-radius: 20rpx;\r\n      padding: 20rpx;\r\n      margin-bottom: 30rpx;\r\n      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n      transition: transform 0.2s ease;\r\n      \r\n      &:active {\r\n        transform: scale(0.98);\r\n      }\r\n      \r\n      .order-header {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-bottom: 15rpx;\r\n        \r\n        .order-id {\r\n          font-size: 24rpx;\r\n          color: #8E8E93;\r\n        }\r\n        \r\n        .order-status {\r\n          font-size: 24rpx;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n      \r\n      .order-product {\r\n        display: flex;\r\n        margin-bottom: 15rpx;\r\n        \r\n        .product-image {\r\n          width: 100rpx;\r\n          height: 100rpx;\r\n          border-radius: 10rpx;\r\n          margin-right: 15rpx;\r\n        }\r\n        \r\n        .product-info {\r\n          flex: 1;\r\n          \r\n          .product-name {\r\n            font-size: 28rpx;\r\n            color: #333333;\r\n            margin-bottom: 10rpx;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 2;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          }\r\n          \r\n          .product-price-qty {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            \r\n            .product-price {\r\n              font-size: 28rpx;\r\n              color: #FF3B69;\r\n              font-weight: 600;\r\n            }\r\n            \r\n            .product-qty {\r\n              font-size: 24rpx;\r\n              color: #8E8E93;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .order-footer {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        \r\n        .order-time {\r\n          font-size: 24rpx;\r\n          color: #8E8E93;\r\n        }\r\n        \r\n        .order-commission {\r\n          display: flex;\r\n          align-items: center;\r\n          \r\n          .commission-label {\r\n            font-size: 24rpx;\r\n            color: #8E8E93;\r\n          }\r\n          \r\n          .commission-value {\r\n            font-size: 28rpx;\r\n            color: #FF3B69;\r\n            font-weight: 600;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 团队卡片 */\r\n.team-card {\r\n  margin-bottom: 30rpx;\r\n  \r\n  .team-stats {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    margin-bottom: 30rpx;\r\n    \r\n    .team-stat-item {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      \r\n      .stat-value {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #333333;\r\n        margin-bottom: 5rpx;\r\n      }\r\n      \r\n      .stat-label {\r\n        font-size: 24rpx;\r\n        color: #8E8E93;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .team-members {\r\n    .section-subtitle {\r\n      font-size: 28rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-bottom: 20rpx;\r\n      display: block;\r\n    }\r\n    \r\n    .member-list {\r\n      .member-item {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 20rpx;\r\n        border-bottom: 1rpx solid #F2F2F7;\r\n        margin-bottom: 20rpx;\r\n        background: #F9F9F9;\r\n        border-radius: 20rpx;\r\n        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n        transition: transform 0.2s ease;\r\n        \r\n        &:active {\r\n          transform: scale(0.98);\r\n        }\r\n        \r\n        &:last-child {\r\n          border-bottom: none;\r\n        }\r\n        \r\n        .member-avatar {\r\n          width: 80rpx;\r\n          height: 80rpx;\r\n          border-radius: 50%;\r\n          margin-right: 15rpx;\r\n        }\r\n        \r\n        .member-info {\r\n          flex: 1;\r\n          \r\n          .member-name-level {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 10rpx;\r\n            \r\n            .member-name {\r\n              font-size: 28rpx;\r\n              color: #333333;\r\n              margin-right: 10rpx;\r\n            }\r\n            \r\n            .member-level {\r\n              padding: 4rpx 12rpx;\r\n              border-radius: 20rpx;\r\n              \r\n              text {\r\n                font-size: 20rpx;\r\n                color: #FFFFFF;\r\n                font-weight: 500;\r\n              }\r\n            }\r\n          }\r\n          \r\n          .member-stats {\r\n            display: flex;\r\n            align-items: center;\r\n            \r\n            .member-stat {\r\n              font-size: 24rpx;\r\n              color: #8E8E93;\r\n            }\r\n            \r\n            .member-stat-divider {\r\n              margin: 0 10rpx;\r\n              font-size: 24rpx;\r\n              color: #D1D1D6;\r\n            }\r\n          }\r\n        }\r\n        \r\n        .arrow-icon {\r\n          width: 32rpx;\r\n          height: 32rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40rpx 0;\r\n  background: #F9F9F9;\r\n  border-radius: 20rpx;\r\n  margin: 20rpx 0;\r\n  \r\n  .empty-image {\r\n    width: 160rpx;\r\n    height: 160rpx;\r\n    margin-bottom: 20rpx;\r\n    opacity: 0.7;\r\n  }\r\n  \r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #8E8E93;\r\n    margin-bottom: 30rpx;\r\n    font-weight: 500;\r\n  }\r\n  \r\n  .action-btn {\r\n    padding: 16rpx 40rpx;\r\n    border-radius: 35px;\r\n    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);\r\n    box-shadow: 0 4rpx 8rpx rgba(172, 57, 255, 0.25);\r\n    \r\n    text {\r\n      color: #FFFFFF;\r\n      font-size: 28rpx;\r\n      font-weight: 500;\r\n    }\r\n    \r\n    &:active {\r\n      opacity: 0.9;\r\n      transform: scale(0.98);\r\n      box-shadow: 0 2rpx 4rpx rgba(172, 57, 255, 0.15);\r\n    }\r\n  }\r\n}\r\n\r\n/* 底部导航栏 */\r\n.tabbar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 100rpx;\r\n  background: #FFFFFF;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  z-index: 99;\r\n  border-top: 1rpx solid #EEEEEE;\r\n}\r\n  \r\n.tabbar-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  padding: 6px 0;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n}\r\n    \r\n.tabbar-item:active {\r\n  transform: scale(0.9);\r\n}\r\n    \r\n.tabbar-item.active .tab-icon {\r\n  transform: translateY(-5rpx);\r\n}\r\n      \r\n.tabbar-item.active .tabbar-text {\r\n  color: #FF3B69;\r\n  font-weight: 600;\r\n  transform: translateY(-2rpx);\r\n}\r\n    \r\n.tab-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  margin-bottom: 4px;\r\n  color: #999999;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-size: contain;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n}\r\n\r\n/* 首页图标 */\r\n.tab-icon.home {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n.tabbar-item.active[data-tab=\"home\"] .tab-icon.home {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n/* 发现图标 */\r\n.tab-icon.discover {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n.tabbar-item.active[data-tab=\"discover\"] .tab-icon.discover {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n/* 分销图标 */\r\n.tab-icon.distribution {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n.tabbar-item.active[data-tab=\"distribution\"] .tab-icon.distribution {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n/* 消息图标 */\r\n.tab-icon.message {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n.tabbar-item.active[data-tab=\"message\"] .tab-icon.message {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n/* 我的图标 */\r\n.tab-icon.user {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n.tabbar-item.active[data-tab=\"my\"] .tab-icon.user {\r\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\r\n}\r\n\r\n.tabbar-item.active .tab-icon {\r\n  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));\r\n}\r\n      \r\n.badge {\r\n  position: absolute;\r\n  top: -8rpx;\r\n  right: -12rpx;\r\n  min-width: 32rpx;\r\n  height: 32rpx;\r\n  border-radius: 16rpx;\r\n  background: linear-gradient(135deg, #FF453A, #FF2D55);\r\n  color: #FFFFFF;\r\n  font-size: 18rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 0 6rpx;\r\n  box-sizing: border-box;\r\n  font-weight: 600;\r\n  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\r\n  transform: scale(0.9);\r\n}\r\n    \r\n.tabbar-text {\r\n  font-size: 22rpx;\r\n  color: #8E8E93;\r\n  margin-top: 2rpx;\r\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\r\n}\r\n    \r\n.tabbar-item::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%) scaleX(0);\r\n  width: 30rpx;\r\n  height: 4rpx;\r\n  background: #FF3B69;\r\n  border-radius: 2rpx;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.tabbar-item.active::after {\r\n  transform: translateX(-50%) scaleX(1);\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/distribution/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted"], "mappings": ";;;;;;;;;;;;;AAubA,UAAM,gBAAgBA,cAAAA,IAAI,cAAc;AAGxC,UAAM,qBAAqBA,cAAAA,IAAI,CAAC;AAGhC,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,cAAc;AAAA,MACd,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,eAAe;AAAA,MACf,eAAe;AAAA,IACjB,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,gBAAgB;AAAA,IAClB,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,OAAO;AAAA,IACT,CAAC;AAGD,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB,OAAO;AAAA,IACT,CAAC;AAGD,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAC7B,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,EAAE,MAAM,MAAM,QAAQ,MAAO;AAAA,MAC7B,EAAE,MAAM,OAAO,QAAQ,kBAAmB;AAAA,MAC1C,EAAE,MAAM,OAAO,QAAQ,mBAAoB;AAAA,MAC3C,EAAE,MAAM,OAAO,QAAQ,YAAa;AAAA,MACpC,EAAE,MAAM,OAAO,QAAQ,YAAa;AAAA,MACpC,EAAE,MAAM,OAAO,QAAQ,UAAW;AAAA,IACpC,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,EAAE,IAAI,QAAQ,MAAM,MAAM,QAAQ,kCAAkC,OAAO,SAAS,UAAU,cAAc,cAAc,OAAQ;AAAA,MAClI,EAAE,IAAI,QAAQ,MAAM,MAAM,QAAQ,kCAAkC,OAAO,SAAS,UAAU,cAAc,cAAc,OAAQ;AAAA,MAClI,EAAE,IAAI,QAAQ,MAAM,MAAM,QAAQ,kCAAkC,OAAO,SAAS,UAAU,cAAc,cAAc,OAAQ;AAAA,IACpI,CAAC;AAGD,UAAM,eAAe,MAAM;AACzBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,UAAU;AAChC,sBAAgB,QAAQ;AAAA,IAC1B;AAGA,UAAM,sBAAsB,CAAC,WAAW;AACtC,cAAO,QAAM;AAAA,QACX,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,qBAAqB,CAAC,WAAW;AACrC,cAAO,QAAM;AAAA,QACX,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,UAAU;AAC/B,cAAO,OAAK;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,WAAW;AAEpC,YAAM,YAAY;AAAA,QAChB;AAAA,UACE,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,cAAc;AAAA,UACd,cAAc;AAAA,UACd,UAAU;AAAA,UACV,WAAW;AAAA,UACX,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,cAAc;AAAA,UACd,cAAc;AAAA,UACd,UAAU;AAAA,UACV,WAAW;AAAA,UACX,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,cAAc;AAAA,UACd,cAAc;AAAA,UACd,UAAU;AAAA,UACV,WAAW;AAAA,UACX,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,cAAc;AAAA,UACd,cAAc;AAAA,UACd,UAAU;AAAA,UACV,WAAW;AAAA,UACX,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,cAAc;AAAA,UACd,cAAc;AAAA,UACd,UAAU;AAAA,UACV,WAAW;AAAA,UACX,YAAY;AAAA,QACb;AAAA,MACL;AAEE,UAAI,WAAW,OAAO;AACpB,eAAO;AAAA,MACR;AAED,aAAO,UAAU,OAAO,WAAS,MAAM,WAAW,MAAM;AAAA,IAC1D;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,eAAe,CAAC,QAAQ;AAC5B,UAAI,cAAc,UAAU;AAAK;AAEjC,oBAAc,QAAQ;AAGtB,cAAO,KAAG;AAAA,QACR,KAAK;AACHA,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAc,MAAA,MAAA,SAAA,qEAAA,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAc,MAAA,MAAA,SAAA,qEAAA,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACF,KAAK;AAEH;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAc,MAAA,MAAA,SAAA,qEAAA,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAc,MAAA,MAAA,SAAA,qEAAA,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,MACH;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AAAA,IAEvB;AAGAC,kBAAAA,UAAU,MAAM;AAAA,IAEhB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACruBD,GAAG,WAAW,eAAe;"}