{"version": 3, "file": "userApi.js", "sources": ["api/userApi.js"], "sourcesContent": ["/**\n * 用户API服务\n * 替换Mock数据，实现真实的后台API调用\n */\n\nimport request from '@/utils/request';\n\n// 用户API服务类\nclass UserApiService {\n  \n  /**\n   * 用户登录\n   * @param {Object} credentials - 登录凭据\n   * @param {string} credentials.phone - 手机号\n   * @param {string} credentials.password - 密码\n   * @returns {Promise} 登录结果\n   */\n  async login(credentials) {\n    try {\n      const response = await request.post('/auth/login', {\n        phone: credentials.phone,\n        password: credentials.password\n      });\n      \n      if (response.data && response.data.token) {\n        // 保存用户信息和token\n        uni.setStorageSync('token', response.data.token);\n        uni.setStorageSync('userInfo', response.data.user);\n        \n        return {\n          success: true,\n          data: response.data,\n          message: '登录成功'\n        };\n      } else {\n        return {\n          success: false,\n          message: response.message || '登录失败'\n        };\n      }\n    } catch (error) {\n      console.error('登录失败:', error);\n      return {\n        success: false,\n        message: error.message || '网络错误，请重试'\n      };\n    }\n  }\n\n  /**\n   * 微信小程序登录\n   * @param {Object} wxData - 微信登录数据\n   * @param {string} wxData.code - 微信授权码\n   * @param {Object} wxData.userInfo - 微信用户信息\n   * @returns {Promise} 登录结果\n   */\n  async wxLogin(wxData) {\n    try {\n      const response = await request.post('/auth/wx-login', {\n        code: wxData.code,\n        user_info: wxData.userInfo\n      });\n      \n      if (response.data && response.data.token) {\n        // 保存用户信息和token\n        uni.setStorageSync('token', response.data.token);\n        uni.setStorageSync('userInfo', response.data.user);\n        \n        return {\n          success: true,\n          data: response.data,\n          message: '登录成功'\n        };\n      } else {\n        return {\n          success: false,\n          message: response.message || '微信登录失败'\n        };\n      }\n    } catch (error) {\n      console.error('微信登录失败:', error);\n      return {\n        success: false,\n        message: error.message || '网络错误，请重试'\n      };\n    }\n  }\n\n  /**\n   * 用户注册\n   * @param {Object} userData - 注册数据\n   * @param {string} userData.phone - 手机号\n   * @param {string} userData.password - 密码\n   * @param {string} userData.nickname - 昵称\n   * @param {string} userData.sms_code - 短信验证码\n   * @returns {Promise} 注册结果\n   */\n  async register(userData) {\n    try {\n      const response = await request.post('/auth/register', userData);\n      \n      if (response.data) {\n        return {\n          success: true,\n          data: response.data,\n          message: '注册成功'\n        };\n      } else {\n        return {\n          success: false,\n          message: response.message || '注册失败'\n        };\n      }\n    } catch (error) {\n      console.error('注册失败:', error);\n      return {\n        success: false,\n        message: error.message || '网络错误，请重试'\n      };\n    }\n  }\n\n  /**\n   * 发送短信验证码\n   * @param {string} phone - 手机号\n   * @param {string} type - 验证码类型 (register, login, reset_password)\n   * @returns {Promise} 发送结果\n   */\n  async sendSmsCode(phone, type = 'register') {\n    try {\n      const response = await request.post('/auth/send-sms', {\n        phone: phone,\n        type: type\n      });\n      \n      return {\n        success: true,\n        message: '验证码发送成功'\n      };\n    } catch (error) {\n      console.error('发送验证码失败:', error);\n      return {\n        success: false,\n        message: error.message || '发送失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 获取用户信息\n   * @returns {Promise} 用户信息\n   */\n  async getUserInfo() {\n    try {\n      const response = await request.get('/user/profile');\n      \n      if (response.data) {\n        // 更新本地存储的用户信息\n        uni.setStorageSync('userInfo', response.data);\n        \n        return {\n          success: true,\n          data: response.data\n        };\n      } else {\n        return {\n          success: false,\n          message: '获取用户信息失败'\n        };\n      }\n    } catch (error) {\n      console.error('获取用户信息失败:', error);\n      return {\n        success: false,\n        message: error.message || '网络错误'\n      };\n    }\n  }\n\n  /**\n   * 更新用户信息\n   * @param {Object} userData - 用户数据\n   * @returns {Promise} 更新结果\n   */\n  async updateUserInfo(userData) {\n    try {\n      const response = await request.put('/user/profile', userData);\n      \n      if (response.data) {\n        // 更新本地存储的用户信息\n        uni.setStorageSync('userInfo', response.data);\n        \n        return {\n          success: true,\n          data: response.data,\n          message: '更新成功'\n        };\n      } else {\n        return {\n          success: false,\n          message: response.message || '更新失败'\n        };\n      }\n    } catch (error) {\n      console.error('更新用户信息失败:', error);\n      return {\n        success: false,\n        message: error.message || '网络错误，请重试'\n      };\n    }\n  }\n\n  /**\n   * 修改密码\n   * @param {Object} passwordData - 密码数据\n   * @param {string} passwordData.old_password - 旧密码\n   * @param {string} passwordData.new_password - 新密码\n   * @returns {Promise} 修改结果\n   */\n  async changePassword(passwordData) {\n    try {\n      const response = await request.put('/user/password', passwordData);\n      \n      return {\n        success: true,\n        message: '密码修改成功'\n      };\n    } catch (error) {\n      console.error('修改密码失败:', error);\n      return {\n        success: false,\n        message: error.message || '修改失败，请重试'\n      };\n    }\n  }\n\n  /**\n   * 用户登出\n   * @returns {Promise} 登出结果\n   */\n  async logout() {\n    try {\n      await request.post('/auth/logout');\n      \n      // 清除本地存储\n      uni.removeStorageSync('token');\n      uni.removeStorageSync('userInfo');\n      \n      return {\n        success: true,\n        message: '退出成功'\n      };\n    } catch (error) {\n      console.error('退出失败:', error);\n      \n      // 即使API调用失败，也清除本地存储\n      uni.removeStorageSync('token');\n      uni.removeStorageSync('userInfo');\n      \n      return {\n        success: true,\n        message: '退出成功'\n      };\n    }\n  }\n\n  /**\n   * 检查登录状态\n   * @returns {boolean} 是否已登录\n   */\n  isLoggedIn() {\n    const token = uni.getStorageSync('token');\n    const userInfo = uni.getStorageSync('userInfo');\n    return !!(token && userInfo);\n  }\n\n  /**\n   * 获取当前用户信息（从本地存储）\n   * @returns {Object|null} 用户信息\n   */\n  getCurrentUser() {\n    return uni.getStorageSync('userInfo') || null;\n  }\n\n  /**\n   * 获取用户token（从本地存储）\n   * @returns {string|null} 用户token\n   */\n  getToken() {\n    return uni.getStorageSync('token') || null;\n  }\n}\n\n// 创建单例实例\nconst userApi = new UserApiService();\n\nexport default userApi;\n"], "names": ["request", "uni"], "mappings": ";;;AAQA,MAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASnB,MAAM,MAAM,aAAa;AACvB,QAAI;AACF,YAAM,WAAW,MAAMA,sBAAQ,KAAK,eAAe;AAAA,QACjD,OAAO,YAAY;AAAA,QACnB,UAAU,YAAY;AAAA,MAC9B,CAAO;AAED,UAAI,SAAS,QAAQ,SAAS,KAAK,OAAO;AAExCC,sBAAG,MAAC,eAAe,SAAS,SAAS,KAAK,KAAK;AAC/CA,sBAAG,MAAC,eAAe,YAAY,SAAS,KAAK,IAAI;AAEjD,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,SAAS;AAAA,UACf,SAAS;AAAA,QACnB;AAAA,MACA,OAAa;AACL,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,SAAS,WAAW;AAAA,QACvC;AAAA,MACO;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,wBAAA,SAAS,KAAK;AAC5B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,QAAQ,QAAQ;AACpB,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,KAAK,kBAAkB;AAAA,QACpD,MAAM,OAAO;AAAA,QACb,WAAW,OAAO;AAAA,MAC1B,CAAO;AAED,UAAI,SAAS,QAAQ,SAAS,KAAK,OAAO;AAExCC,sBAAG,MAAC,eAAe,SAAS,SAAS,KAAK,KAAK;AAC/CA,sBAAG,MAAC,eAAe,YAAY,SAAS,KAAK,IAAI;AAEjD,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,SAAS;AAAA,UACf,SAAS;AAAA,QACnB;AAAA,MACA,OAAa;AACL,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,SAAS,WAAW;AAAA,QACvC;AAAA,MACO;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,wBAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,MAAM,SAAS,UAAU;AACvB,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,KAAK,kBAAkB,QAAQ;AAE9D,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,SAAS;AAAA,UACf,SAAS;AAAA,QACnB;AAAA,MACA,OAAa;AACL,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,SAAS,WAAW;AAAA,QACvC;AAAA,MACO;AAAA,IACF,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,yBAAA,SAAS,KAAK;AAC5B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,YAAY,OAAO,OAAO,YAAY;AAC1C,QAAI;AACF,YAAM,WAAW,MAAMD,sBAAQ,KAAK,kBAAkB;AAAA,QACpD;AAAA,QACA;AAAA,MACR,CAAO;AAED,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,kEAAc,YAAY,KAAK;AAC/B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,cAAc;AAClB,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,eAAe;AAElD,UAAI,SAAS,MAAM;AAEjBC,sBAAAA,MAAI,eAAe,YAAY,SAAS,IAAI;AAE5C,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,SAAS;AAAA,QACzB;AAAA,MACA,OAAa;AACL,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,QACnB;AAAA,MACO;AAAA,IACF,SAAQ,OAAO;AACdA,kEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,eAAe,UAAU;AAC7B,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,IAAI,iBAAiB,QAAQ;AAE5D,UAAI,SAAS,MAAM;AAEjBC,sBAAAA,MAAI,eAAe,YAAY,SAAS,IAAI;AAE5C,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,SAAS;AAAA,UACf,SAAS;AAAA,QACnB;AAAA,MACA,OAAa;AACL,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,SAAS,WAAW;AAAA,QACvC;AAAA,MACO;AAAA,IACF,SAAQ,OAAO;AACdA,kEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,eAAe,cAAc;AACjC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAO,QAAC,IAAI,kBAAkB,YAAY;AAEjE,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAClC;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,SAAS;AACb,QAAI;AACF,YAAMD,cAAO,QAAC,KAAK,cAAc;AAGjCC,0BAAI,kBAAkB,OAAO;AAC7BA,0BAAI,kBAAkB,UAAU;AAEhC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACjB;AAAA,IACK,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,yBAAA,SAAS,KAAK;AAG5BA,0BAAI,kBAAkB,OAAO;AAC7BA,0BAAI,kBAAkB,UAAU;AAEhC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACjB;AAAA,IACK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,aAAa;AACX,UAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,UAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,WAAO,CAAC,EAAE,SAAS;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,iBAAiB;AACf,WAAOA,oBAAI,eAAe,UAAU,KAAK;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW;AACT,WAAOA,oBAAI,eAAe,OAAO,KAAK;AAAA,EACvC;AACH;AAGK,MAAC,UAAU,IAAI,eAAc;;"}