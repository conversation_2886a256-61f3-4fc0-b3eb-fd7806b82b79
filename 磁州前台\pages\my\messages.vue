<template>
  <view class="messages-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的消息</view>
      <view class="navbar-right" @click="clearAllMessages">
        <text class="clear-text">清空</text>
      </view>
    </view>
    
    <!-- 消息分类选项卡 -->
    <view class="tabs-container" :style="{ top: navbarHeight + 'px' }">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <view class="badge" v-if="tab.unread > 0">{{ tab.unread > 99 ? '99+' : tab.unread }}</view>
      </view>
      <view class="tab-line" :style="tabLineStyle"></view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingTop: (navbarHeight + tabsHeight) + 'px' }">
      <swiper class="content-swiper" :current="currentTab" @change="onSwiperChange">
        <!-- 系统消息 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(0)" refresher-enabled :refresher-triggered="refreshing[0]" @refresherrefresh="onRefresh(0)">
            <view v-if="systemMessages.length > 0" class="message-list">
              <view class="message-item" v-for="(item, index) in systemMessages" :key="index" @click="viewSystemMessage(item)">
                <view class="message-left">
                  <image class="message-icon" :src="item.icon || '/static/images/tabbar/系统通知.png'" mode="aspectFill"></image>
                  <view class="unread-dot" v-if="!item.isRead"></view>
                </view>
                <view class="message-center">
                  <view class="message-title">{{ item.title }}</view>
                  <view class="message-content">{{ item.content }}</view>
                </view>
                <view class="message-right">
                  <text class="message-time">{{ item.time }}</text>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无系统消息</view>
            </view>
            <view v-if="systemMessages.length > 0 && !hasMore[0]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
        
        <!-- 互动消息 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(1)" refresher-enabled :refresher-triggered="refreshing[1]" @refresherrefresh="onRefresh(1)">
            <view v-if="interactionMessages.length > 0" class="message-list">
              <view class="message-item" v-for="(item, index) in interactionMessages" :key="index" @click="viewInteractionMessage(item)">
                <view class="message-left">
                  <image class="message-avatar" :src="item.userAvatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
                  <view class="unread-dot" v-if="!item.isRead"></view>
                </view>
                <view class="message-center">
                  <view class="message-title">
                    <text class="user-name">{{ item.userName }}</text>
                    <text class="action-text">{{ getActionText(item.action) }}</text>
                  </view>
                  <view class="message-content">{{ item.content }}</view>
                </view>
                <view class="message-right">
                  <text class="message-time">{{ item.time }}</text>
                  <image v-if="item.contentImage" class="content-preview" :src="item.contentImage" mode="aspectFill"></image>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无互动消息</view>
            </view>
            <view v-if="interactionMessages.length > 0 && !hasMore[1]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
        
        <!-- 聊天消息 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(2)" refresher-enabled :refresher-triggered="refreshing[2]" @refresherrefresh="onRefresh(2)">
            <view v-if="chatMessages.length > 0" class="chat-list">
              <view class="chat-item" v-for="(item, index) in chatMessages" :key="index" @click="navigateToChat(item)">
                <view class="chat-left">
                  <image class="chat-avatar" :src="item.userAvatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
                  <view class="chat-badge" v-if="item.unreadCount > 0">{{ item.unreadCount > 99 ? '99+' : item.unreadCount }}</view>
                </view>
                <view class="chat-center">
                  <view class="chat-name">{{ item.userName }}</view>
                  <view class="chat-last-message">{{ item.lastMessage }}</view>
                </view>
                <view class="chat-right">
                  <text class="chat-time">{{ item.time }}</text>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无聊天消息</view>
            </view>
            <view v-if="chatMessages.length > 0 && !hasMore[2]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
import { smartNavigate } from '@/utils/navigation.js';

export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64, // 导航栏高度
      tabsHeight: 44, // 选项卡高度
      tabs: [
        { name: '系统', unread: 2 },
        { name: '互动', unread: 5 },
        { name: '聊天', unread: 3 }
      ],
      currentTab: 0,
      systemMessages: [],
      interactionMessages: [],
      chatMessages: [],
      page: [1, 1, 1], // 当前页码
      pageSize: 10, // 每页显示数量
      hasMore: [true, true, true], // 是否有更多数据
      refreshing: [false, false, false], // 刷新状态
    }
  },
  computed: {
    tabLineStyle() {
      return {
        transform: `translateX(${this.currentTab * 100}%)`
      }
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 加载初始数据
    this.loadSystemMessages();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 切换选项卡
    switchTab(index) {
      if (this.currentTab === index) return;
      this.currentTab = index;
      
      // 加载对应选项卡的数据
      switch (index) {
        case 0:
          if (this.systemMessages.length === 0) this.loadSystemMessages();
          break;
        case 1:
          if (this.interactionMessages.length === 0) this.loadInteractionMessages();
          break;
        case 2:
          if (this.chatMessages.length === 0) this.loadChatMessages();
          break;
      }
    },
    
    // 轮播图切换事件
    onSwiperChange(e) {
      this.switchTab(e.detail.current);
    },
    
    // 清空所有消息
    clearAllMessages() {
      uni.showModal({
        title: '提示',
        content: '确定要清空当前分类的所有消息吗？',
        success: (res) => {
          if (res.confirm) {
            switch (this.currentTab) {
              case 0:
                this.systemMessages = [];
                this.tabs[0].unread = 0;
                break;
              case 1:
                this.interactionMessages = [];
                this.tabs[1].unread = 0;
                break;
              case 2:
                this.chatMessages = [];
                this.tabs[2].unread = 0;
                break;
            }
            
            uni.showToast({
              title: '清空成功',
              icon: 'success'
            });
          }
        }
      });
    },
    
    // 获取互动动作文本
    getActionText(action) {
      const actionMap = {
        'like': '点赞了你的',
        'comment': '评论了你的',
        'collect': '收藏了你的',
        'follow': '关注了你',
        'mention': '提到了你'
      };
      return actionMap[action] || '';
    },
    
    // 加载系统消息
    loadSystemMessages() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `system_${this.page[0]}_${i}`,
          title: `系统通知 ${this.page[0]}_${i}`,
          content: '您的帐号已完成实名认证，现在可以使用更多功能啦！',
          time: this.getRandomTime(),
          icon: '/static/images/tabbar/系统通知.png',
          isRead: Math.random() > 0.3 // 70%概率已读
        }));
        
        if (this.page[0] === 1) {
          this.systemMessages = mockData;
        } else {
          this.systemMessages = [...this.systemMessages, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[0] = this.page[0] < 3;
        
        // 关闭刷新状态
        this.refreshing[0] = false;
      }, 500);
    },
    
    // 加载互动消息
    loadInteractionMessages() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        const actions = ['like', 'comment', 'collect', 'follow', 'mention'];
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `interaction_${this.page[1]}_${i}`,
          userName: `用户${Math.floor(Math.random() * 1000)}`,
          userAvatar: '/static/images/default-avatar.png',
          action: actions[Math.floor(Math.random() * actions.length)],
          content: '这是一条动态内容，描述具体的互动信息...',
          contentImage: Math.random() > 0.5 ? '/static/images/service1.jpg' : '', // 50%概率有图片
          time: this.getRandomTime(),
          isRead: Math.random() > 0.5 // 50%概率已读
        }));
        
        if (this.page[1] === 1) {
          this.interactionMessages = mockData;
        } else {
          this.interactionMessages = [...this.interactionMessages, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[1] = this.page[1] < 3;
        
        // 关闭刷新状态
        this.refreshing[1] = false;
      }, 500);
    },
    
    // 加载聊天消息
    loadChatMessages() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        const mockData = Array.from({ length: 10 }, (_, i) => ({
          id: `chat_${this.page[2]}_${i}`,
          userName: `用户${Math.floor(Math.random() * 1000)}`,
          userAvatar: '/static/images/default-avatar.png',
          lastMessage: '您好，请问有什么可以帮助您的吗？',
          time: this.getRandomTime(),
          unreadCount: Math.floor(Math.random() * 10) // 0-9条未读消息
        }));
        
        if (this.page[2] === 1) {
          this.chatMessages = mockData;
        } else {
          this.chatMessages = [...this.chatMessages, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[2] = this.page[2] < 3;
        
        // 关闭刷新状态
        this.refreshing[2] = false;
      }, 500);
    },
    
    // 生成随机时间
    getRandomTime() {
      const today = new Date();
      const randomDays = Math.floor(Math.random() * 7); // 0-6天前
      const randomHours = Math.floor(Math.random() * 24); // 0-23小时
      const randomMinutes = Math.floor(Math.random() * 60); // 0-59分钟
      
      const date = new Date(today);
      date.setDate(date.getDate() - randomDays);
      date.setHours(randomHours, randomMinutes);
      
      // 如果是今天，返回时间，否则返回日期
      if (randomDays === 0) {
        return `${randomHours.toString().padStart(2, '0')}:${randomMinutes.toString().padStart(2, '0')}`;
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
    },
    
    // 加载更多数据
    loadMore(tabIndex) {
      if (!this.hasMore[tabIndex] || this.refreshing[tabIndex]) return;
      
      this.page[tabIndex]++;
      
      switch (tabIndex) {
        case 0:
          this.loadSystemMessages();
          break;
        case 1:
          this.loadInteractionMessages();
          break;
        case 2:
          this.loadChatMessages();
          break;
      }
    },
    
    // 下拉刷新
    onRefresh(tabIndex) {
      this.refreshing[tabIndex] = true;
      this.page[tabIndex] = 1;
      
      switch (tabIndex) {
        case 0:
          this.loadSystemMessages();
          break;
        case 1:
          this.loadInteractionMessages();
          break;
        case 2:
          this.loadChatMessages();
          break;
      }
    },
    
    // 查看系统消息
    viewSystemMessage(item) {
      // 标记为已读
      if (!item.isRead) {
        item.isRead = true;
        this.updateUnreadCount(0);
      }
      
      // 导航到消息详情页
      smartNavigate(`/pages/my/message-detail?id=${item.id}&type=system`);
    },
    
    // 查看互动消息
    viewInteractionMessage(item) {
      // 标记为已读
      if (!item.isRead) {
        item.isRead = true;
        this.updateUnreadCount(1);
      }
      
      // 根据不同类型的互动跳转到对应页面
      if (item.action === 'follow') {
        smartNavigate(`/pages/my/profile?userId=${item.userId}`);
      } else {
        smartNavigate(`/pages/publish/info-detail?id=${item.contentId}`);
      }
    },
    
    // 跳转到聊天页面
    navigateToChat(item) {
      // 重置未读数
      item.unreadCount = 0;
      this.updateUnreadCount(2);
      
      // 导航到聊天页面
      smartNavigate(`/pages/message/chat?userId=${item.id}`);
    },
    
    // 更新未读消息数量
    updateUnreadCount(tabIndex) {
      let count = 0;
      
      switch (tabIndex) {
        case 0:
          count = this.systemMessages.filter(item => !item.isRead).length;
          break;
        case 1:
          count = this.interactionMessages.filter(item => !item.isRead).length;
          break;
        case 2:
          count = this.chatMessages.reduce((sum, item) => sum + item.unreadCount, 0);
          break;
      }
      
      this.tabs[tabIndex].unread = count;
    }
  }
}
</script>

<style>
.messages-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.clear-text {
  font-size: 14px;
  color: #fff;
}

/* 选项卡 */
.tabs-container {
  position: fixed;
  display: flex;
  width: 100%;
  background-color: #fff;
  z-index: 100;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;
  height: 44px;
}

.tab-item {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
}

.tab-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  transition: all 0.3s;
}

.tab-item.active .tab-text {
  color: #0066FF;
  font-weight: 500;
}

.badge {
  position: absolute;
  top: 8rpx;
  right: 50%;
  margin-right: -50rpx;
  min-width: 32rpx;
  height: 32rpx;
  background-color: #ff5252;
  color: #fff;
  font-size: 20rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 6rpx;
  width: 33.3% !important;
  background-color: #0066FF;
  border-radius: 6rpx;
  transition: transform 0.3s ease;
  z-index: 101;
  transform: translateX(0);
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-swiper {
  width: 100%;
  height: calc(100vh - 108px); /* 状态栏(~20px) + 导航栏(44px) + 选项卡(44px) */
}

.tab-scroll {
  height: 100%;
  box-sizing: border-box;
}

/* 消息列表 */
.message-list, .chat-list {
  padding: 0 30rpx;
}

.message-item, .chat-item {
  display: flex;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.message-left, .chat-left {
  position: relative;
  margin-right: 20rpx;
}

.message-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background-color: #f5f7fa;
}

.message-avatar, .chat-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f5f7fa;
}

.unread-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff5252;
  border-radius: 8rpx;
}

.chat-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background-color: #ff5252;
  color: #fff;
  font-size: 20rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.message-center, .chat-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

.message-title, .chat-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.user-name {
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-text {
  color: #666;
  font-weight: 400;
}

.message-content, .chat-last-message {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.message-right, .chat-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  margin-left: 20rpx;
}

.message-time, .chat-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.content-preview {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background-color: #f5f7fa;
}

/* 空状态 */
.empty-view {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 30rpx 0;
}
</style> 