<template>
  <view class="activity-overview-card" :style="cardStyle">
    <view class="card-header">
      <text class="card-title">活动参与概览</text>
      <view class="card-more" @click="$emit('viewMore')">
        <text>查看详情</text>
        <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
          <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </view>
    </view>
    
    <view class="card-content">
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{ activityStats.totalParticipated || 0 }}</text>
          <text class="stats-label">已参与</text>
          <text class="stats-change" :class="{'increase': activityStats.participatedChange > 0, 'decrease': activityStats.participatedChange < 0}">
            {{ formatChange(activityStats.participatedChange) }}
          </text>
        </view>
        
        <view class="stats-item">
          <text class="stats-value">{{ activityStats.ongoing || 0 }}</text>
          <text class="stats-label">进行中</text>
          <text class="stats-change" :class="{'increase': activityStats.ongoingChange > 0, 'decrease': activityStats.ongoingChange < 0}">
            {{ formatChange(activityStats.ongoingChange) }}
          </text>
        </view>
        
        <view class="stats-item">
          <text class="stats-value">{{ activityStats.completed || 0 }}</text>
          <text class="stats-label">已完成</text>
          <text class="stats-change" :class="{'increase': activityStats.completedChange > 0, 'decrease': activityStats.completedChange < 0}">
            {{ formatChange(activityStats.completedChange) }}
          </text>
        </view>
        
        <view class="stats-item">
          <text class="stats-value">{{ activityStats.favorites || 0 }}</text>
          <text class="stats-label">已收藏</text>
          <text class="stats-change" :class="{'increase': activityStats.favoritesChange > 0, 'decrease': activityStats.favoritesChange < 0}">
            {{ formatChange(activityStats.favoritesChange) }}
          </text>
        </view>
      </view>
      
      <view class="chart-container" v-if="showChart">
        <view class="chart-title">活动类型分布</view>
        <view class="pie-chart">
          <!-- 简易饼图实现 -->
          <view class="pie-segments">
            <view 
              v-for="(segment, index) in pieChartData" 
              :key="index"
              class="pie-segment"
              :style="{
                'background-color': segment.color,
                'width': '100%',
                'height': '100%',
                'clip-path': `polygon(50% 50%, ${50 + 50 * Math.cos(segment.startAngle)}% ${50 + 50 * Math.sin(segment.startAngle)}%, ${50 + 50 * Math.cos(segment.midAngle)}% ${50 + 50 * Math.sin(segment.midAngle)}%, ${50 + 50 * Math.cos(segment.endAngle)}% ${50 + 50 * Math.sin(segment.endAngle)}%)`
              }"
            ></view>
          </view>
          <view class="pie-center"></view>
        </view>
        
        <view class="chart-legend">
          <view 
            class="legend-item" 
            v-for="(item, index) in pieChartData" 
            :key="index"
          >
            <view class="legend-color" :style="{ 'background-color': item.color }"></view>
            <text class="legend-label">{{ item.name }}</text>
            <text class="legend-value">{{ item.value }}%</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

// 组件属性定义
const props = defineProps({
  activityStats: {
    type: Object,
    default: () => ({
      totalParticipated: 0,
      ongoing: 0,
      completed: 0,
      favorites: 0,
      participatedChange: 0,
      ongoingChange: 0,
      completedChange: 0,
      favoritesChange: 0,
      typeDistribution: []
    })
  },
  showChart: {
    type: Boolean,
    default: true
  },
  cardStyle: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
defineEmits(['viewMore']);

// 格式化变化率
const formatChange = (change) => {
  if (!change) return '0%';
  return change > 0 ? `+${change}%` : `${change}%`;
};

// 计算饼图数据
const pieChartData = computed(() => {
  const distribution = props.activityStats.typeDistribution || [];
  
  // 如果没有数据，返回默认数据
  if (distribution.length === 0) {
    return [
      { name: '拼团', value: 40, color: '#FF3B69' },
      { name: '秒杀', value: 25, color: '#FF7A9E' },
      { name: '优惠券', value: 20, color: '#FF9500' },
      { name: '满减', value: 15, color: '#5AC8FA' }
    ];
  }
  
  // 计算角度
  let startAngle = 0;
  const result = distribution.map((item, index) => {
    const angle = (item.value / 100) * Math.PI * 2;
    const segment = {
      name: item.name,
      value: item.value,
      color: item.color || getDefaultColor(index),
      startAngle: startAngle,
      midAngle: startAngle + angle / 2,
      endAngle: startAngle + angle
    };
    startAngle += angle;
    return segment;
  });
  
  return result;
});

// 获取默认颜色
const getDefaultColor = (index) => {
  const colors = ['#FF3B69', '#FF7A9E', '#FF9500', '#5AC8FA', '#34C759'];
  return colors[index % colors.length];
};
</script>

<style lang="scss" scoped>
.activity-overview-card {
  width: 100%;
  border-radius: 35px;
  background-color: #FFFFFF;
  box-shadow: 0 8px 20px rgba(255,59,105,0.15);
  padding: 30rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
  transform: perspective(1000px) rotateX(2deg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .card-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .card-more {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #FF3B69;
    
    .icon {
      margin-left: 4rpx;
    }
  }
}

.card-content {
  display: flex;
  flex-direction: column;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  padding-bottom: 30rpx;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  
  .stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    
    .stats-value {
      font-size: 40rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 8rpx;
    }
    
    .stats-label {
      font-size: 24rpx;
      color: #8E8E93;
      margin-bottom: 8rpx;
    }
    
    .stats-change {
      font-size: 20rpx;
      padding: 2rpx 8rpx;
      border-radius: 10rpx;
      
      &.increase {
        color: #34C759;
        background-color: rgba(52,199,89,0.1);
      }
      
      &.decrease {
        color: #FF3B30;
        background-color: rgba(255,59,48,0.1);
      }
    }
  }
}

.chart-container {
  margin-top: 30rpx;
  
  .chart-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 20rpx;
  }
  
  .pie-chart {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    margin: 0 auto;
    
    .pie-segments {
      position: relative;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      overflow: hidden;
    }
    
    .pie-segment {
      position: absolute;
      top: 0;
      left: 0;
    }
    
    .pie-center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80rpx;
      height: 80rpx;
      background-color: #FFFFFF;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
  }
  
  .chart-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin-top: 30rpx;
    
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      min-width: 45%;
      
      .legend-color {
        width: 20rpx;
        height: 20rpx;
        border-radius: 10rpx;
        margin-right: 8rpx;
      }
      
      .legend-label {
        font-size: 24rpx;
        color: #333333;
        margin-right: 8rpx;
      }
      
      .legend-value {
        font-size: 24rpx;
        color: #8E8E93;
      }
    }
  }
}
</style> 