{"version": 3, "file": "festival-redpacket-strategy.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/festival-redpacket-strategy.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcZmVzdGl2YWwtcmVkcGFja2V0LXN0cmF0ZWd5LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">节日红包攻略</text>\n      <view class=\"navbar-right\">\n        <view class=\"share-icon\" @click=\"shareGuide\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <circle cx=\"18\" cy=\"5\" r=\"3\"></circle>\n            <circle cx=\"6\" cy=\"12\" r=\"3\"></circle>\n            <circle cx=\"18\" cy=\"19\" r=\"3\"></circle>\n            <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\"></line>\n            <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\"></line>\n          </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 页面内容 -->\n    <scroll-view scroll-y class=\"content-scroll\">\n      <!-- 页面头部 -->\n      <view class=\"page-header\">\n        <view class=\"header-bg\" style=\"background: linear-gradient(135deg, #FF9A8B, #FF6B6B);\">\n          <text class=\"header-title\">节日期间红包营销策略指南</text>\n          <text class=\"header-subtitle\">提升节日营销效果的实用攻略</text>\n        </view>\n      </view>\n      \n      <!-- 内容部分 -->\n      <view class=\"content-section\">\n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF6B6B\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n              <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"></line>\n              <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"></line>\n              <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"></line>\n            </svg>\n          </view>\n          <text class=\"title-text\">节日红包的重要性</text>\n        </view>\n        \n        <view class=\"content-text\">\n          <text>节日期间是商家营销的黄金时期，用户消费意愿强，社交分享频率高。节日红包营销能够抓住用户情感共鸣，提升品牌好感度，刺激消费决策，是节日营销的重要手段。数据显示，节日期间发放红包的商家比普通时期销售额平均提升35%以上。</text>\n        </view>\n        \n        <view class=\"content-image\">\n          <image src=\"/static/images/festival-redpacket.png\" mode=\"widthFix\"></image>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF6B6B\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n  <path d=\"M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z\"></path>\n  <line x1=\"7\" y1=\"7\" x2=\"7.01\" y2=\"7\"></line>\n</svg>\n          </view>\n          <text class=\"title-text\">主要节日红包策略</text>\n        </view>\n        \n        <view class=\"festival-list\">\n          <view class=\"festival-item\">\n            <view class=\"festival-header\">\n              <view class=\"festival-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n                <text>春</text>\n              </view>\n              <text class=\"festival-name\">春节红包</text>\n            </view>\n            <view class=\"festival-content\">\n              <text class=\"festival-desc\">春节是中国最重要的传统节日，红包寓意\"压岁钱\"，象征着祝福和好运。</text>\n              <view class=\"strategy-points\">\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">设计传统喜庆的红包封面，融入\"福\"、\"财\"等元素</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">发放金额可设置为\"6.66\"、\"8.88\"等吉利数字</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">结合\"集五福\"等互动玩法提高用户参与度</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"festival-item\">\n            <view class=\"festival-header\">\n              <view class=\"festival-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n                <text>元</text>\n              </view>\n              <text class=\"festival-name\">元旦红包</text>\n            </view>\n            <view class=\"festival-content\">\n              <text class=\"festival-desc\">元旦是新年的第一天，象征着新的开始，红包寓意新年好运。</text>\n              <view class=\"strategy-points\">\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">设计以\"新年新气象\"为主题的红包封面</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">结合年终盘点或新年愿望清单等互动活动</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">推出\"跨年红包雨\"活动增强节日氛围</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"festival-item\">\n            <view class=\"festival-header\">\n              <view class=\"festival-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n                <text>情</text>\n              </view>\n              <text class=\"festival-name\">情人节红包</text>\n            </view>\n            <view class=\"festival-content\">\n              <text class=\"festival-desc\">情人节是表达爱意的节日，红包可以作为爱的礼物和惊喜。</text>\n              <view class=\"strategy-points\">\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">设计浪漫粉色或红色系的红包封面</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">推出\"情侣专享\"双人红包，两人均可领取</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">结合表白、告白等互动玩法增强情感连接</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"festival-item\">\n            <view class=\"festival-header\">\n              <view class=\"festival-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n                <text>618</text>\n              </view>\n              <text class=\"festival-name\">618购物节</text>\n            </view>\n            <view class=\"festival-content\">\n              <text class=\"festival-desc\">618已成为仅次于双11的电商购物节，红包是刺激消费的重要手段。</text>\n              <view class=\"strategy-points\">\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">设计突出\"618\"数字的红包封面</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">推出满减、折扣等多种类型的红包</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">设置阶梯式红包，消费越多优惠越大</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"festival-item\">\n            <view class=\"festival-header\">\n              <view class=\"festival-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n                <text>双</text>\n              </view>\n              <text class=\"festival-name\">双11购物节</text>\n            </view>\n            <view class=\"festival-content\">\n              <text class=\"festival-desc\">双11是全球最大的购物狂欢节，红包是引流和促销的核心工具。</text>\n              <view class=\"strategy-points\">\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">设计\"双11\"主题的红包封面</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">推出预售红包、定金膨胀等玩法</text>\n                </view>\n                <view class=\"strategy-point\">\n                  <text class=\"point-dot\"></text>\n                  <text class=\"point-text\">设置\"秒杀红包\"增强紧迫感</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF6B6B\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n              <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"16\"></line>\n              <line x1=\"8\" y1=\"12\" x2=\"16\" y2=\"12\"></line>\n            </svg>\n          </view>\n          <text class=\"title-text\">节日红包设计要点</text>\n        </view>\n        \n        <view class=\"design-points\">\n          <view class=\"design-point\">\n            <view class=\"point-header\">\n              <view class=\"point-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF6B6B\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <path d=\"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z\"></path>\n                  <path d=\"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z\"></path>\n                </svg>\n              </view>\n              <text class=\"point-title\">节日主题化设计</text>\n            </view>\n            <text class=\"point-desc\">红包的视觉设计应与节日氛围相符，使用节日相关的元素、色彩和图案，增强用户的节日感受。</text>\n          </view>\n          \n          <view class=\"design-point\">\n            <view class=\"point-header\">\n              <view class=\"point-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF6B6B\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <polygon points=\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"></polygon>\n                </svg>\n              </view>\n              <text class=\"point-title\">差异化红包金额</text>\n            </view>\n            <text class=\"point-desc\">根据节日特点和用户价值设置不同金额的红包，如春节可设置较大金额，日常节日可设置小额多次的红包。</text>\n          </view>\n          \n          <view class=\"design-point\">\n            <view class=\"point-header\">\n              <view class=\"point-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF6B6B\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n                  <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n                  <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n                  <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n                </svg>\n              </view>\n              <text class=\"point-title\">社交裂变传播</text>\n            </view>\n            <text class=\"point-desc\">设计能够在社交媒体上分享的红包形式，鼓励用户将红包分享给亲友，扩大品牌影响力。</text>\n          </view>\n          \n          <view class=\"design-point\">\n            <view class=\"point-header\">\n              <view class=\"point-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF6B6B\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n                  <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\n                  <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\n                </svg>\n              </view>\n              <text class=\"point-title\">多平台联动</text>\n            </view>\n            <text class=\"point-desc\">将红包活动与社交媒体、小程序、公众号等多平台联动，形成全渠道的节日营销矩阵。</text>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF6B6B\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\n              <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\n            </svg>\n          </view>\n          <text class=\"title-text\">节日红包投放时机</text>\n        </view>\n        \n        <view class=\"timing-chart\">\n          <view class=\"chart-header\">\n            <text class=\"chart-title\">节日红包投放时间轴</text>\n          </view>\n          <view class=\"timeline\">\n            <view class=\"timeline-item\">\n              <view class=\"timeline-point\"></view>\n              <view class=\"timeline-content\">\n                <text class=\"timeline-title\">节前预热期</text>\n                <text class=\"timeline-desc\">节日前7-15天，发放预热红包，提前引导用户关注</text>\n              </view>\n            </view>\n            <view class=\"timeline-item\">\n              <view class=\"timeline-point\"></view>\n              <view class=\"timeline-content\">\n                <text class=\"timeline-title\">节日预售期</text>\n                <text class=\"timeline-desc\">节日前3-7天，发放预售红包，刺激用户提前下单</text>\n              </view>\n            </view>\n            <view class=\"timeline-item\">\n              <view class=\"timeline-point\"></view>\n              <view class=\"timeline-content\">\n                <text class=\"timeline-title\">节日高峰期</text>\n                <text class=\"timeline-desc\">节日当天及前后1-2天，发放大额红包，促进转化</text>\n              </view>\n            </view>\n            <view class=\"timeline-item\">\n              <view class=\"timeline-point\"></view>\n              <view class=\"timeline-content\">\n                <text class=\"timeline-title\">节后延续期</text>\n                <text class=\"timeline-desc\">节日后3-5天，发放复购红包，延续节日效应</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"section-title\">\n          <view class=\"title-icon\" style=\"background-color: rgba(255, 107, 107, 0.2);\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF6B6B\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n              <polyline points=\"14 2 14 8 20 8\"></polyline>\n              <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n              <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n              <polyline points=\"10 9 9 9 8 9\"></polyline>\n            </svg>\n          </view>\n          <text class=\"title-text\">成功案例分析</text>\n        </view>\n        \n        <view class=\"case-study\">\n          <view class=\"case-header\">\n            <text class=\"case-title\">某餐饮品牌春节红包案例</text>\n          </view>\n          <view class=\"case-content\">\n            <text class=\"case-desc\">某全国连锁餐饮品牌在春节期间推出\"团圆红包\"活动，用户可通过扫描餐厅二维码获取红包，并可分享给亲友。红包金额设置为8.8元、18.8元、88.8元等吉利数字，使用门槛为消费满88元。活动期间，该品牌客流量提升了45%，新客获取增长了60%，销售额同比增长了52%。</text>\n          </view>\n          <view class=\"case-results\">\n            <view class=\"result-item\">\n              <text class=\"result-label\">客流提升</text>\n              <text class=\"result-value\">45%</text>\n            </view>\n            <view class=\"result-item\">\n              <text class=\"result-label\">新客增长</text>\n              <text class=\"result-value\">60%</text>\n            </view>\n            <view class=\"result-item\">\n              <text class=\"result-label\">销售增长</text>\n              <text class=\"result-value\">52%</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系我们 -->\n      <view class=\"contact-section\">\n        <text class=\"contact-title\">需要更多节日营销方案？</text>\n        <text class=\"contact-desc\">我们的专业团队可为您定制节日红包营销方案</text>\n        <button class=\"contact-btn\" @click=\"contactService\">获取方案</button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      \n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    shareGuide() {\n      uni.showActionSheet({\n        itemList: ['分享给好友', '分享到朋友圈', '复制链接'],\n        success: function(res) {\n          uni.showToast({\n            title: '分享成功',\n            icon: 'success'\n          });\n        }\n      });\n    },\n    contactService() {\n      uni.makePhoneCall({\n        phoneNumber: '************'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* 导航栏样式 */\n.navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background-color: #fff;\n  padding: 0 15px;\n  position: relative;\n}\n\n.navbar-back {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #333;\n  border-left: 2px solid #333;\n  transform: rotate(-45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 600;\n  color: #333;\n}\n\n.navbar-right {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.share-icon {\n  color: #333;\n}\n\n/* 内容滚动区 */\n.content-scroll {\n  flex: 1;\n}\n\n/* 页面头部 */\n.page-header {\n  height: 180px;\n  position: relative;\n  overflow: hidden;\n}\n\n.header-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding: 20px;\n}\n\n.header-title {\n  font-size: 24px;\n  font-weight: 700;\n  color: #fff;\n  margin-bottom: 10px;\n}\n\n.header-subtitle {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 内容部分 */\n.content-section {\n  padding: 20px 15px;\n  background-color: #fff;\n  border-radius: 15px 15px 0 0;\n  margin-top: -20px;\n  position: relative;\n  z-index: 1;\n}\n\n.section-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n  margin-top: 25px;\n}\n\n.section-title:first-child {\n  margin-top: 0;\n}\n\n.title-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n}\n\n.title-text {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.content-text {\n  font-size: 15px;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.content-image {\n  width: 100%;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 20px;\n}\n\n.content-image image {\n  width: 100%;\n}\n\n/* 节日列表 */\n.festival-list {\n  margin-bottom: 20px;\n}\n\n.festival-item {\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 15px;\n}\n\n.festival-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.festival-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n}\n\n.festival-icon text {\n  font-size: 16px;\n  font-weight: 600;\n  color: #FF6B6B;\n}\n\n.festival-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.festival-content {\n  padding-left: 50px;\n}\n\n.festival-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n  margin-bottom: 10px;\n}\n\n.strategy-points {\n  margin-top: 10px;\n}\n\n.strategy-point {\n  display: flex;\n  margin-bottom: 8px;\n}\n\n.point-dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 3px;\n  background-color: #FF6B6B;\n  margin-top: 6px;\n  margin-right: 8px;\n  flex-shrink: 0;\n}\n\n.point-text {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 设计要点 */\n.design-points {\n  margin-bottom: 20px;\n}\n\n.design-point {\n  margin-bottom: 15px;\n  padding: 15px;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n}\n\n.point-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.point-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n}\n\n.point-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.point-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n/* 时间轴 */\n.timing-chart {\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 20px;\n}\n\n.chart-header {\n  margin-bottom: 15px;\n}\n\n.chart-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.timeline {\n  position: relative;\n  padding-left: 20px;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 4px;\n  height: 100%;\n  width: 2px;\n  background-color: #FF6B6B;\n}\n\n.timeline-item {\n  position: relative;\n  padding-bottom: 20px;\n}\n\n.timeline-item:last-child {\n  padding-bottom: 0;\n}\n\n.timeline-point {\n  position: absolute;\n  left: -20px;\n  top: 5px;\n  width: 10px;\n  height: 10px;\n  border-radius: 5px;\n  background-color: #FF6B6B;\n}\n\n.timeline-content {\n  padding-left: 10px;\n}\n\n.timeline-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.timeline-desc {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 案例分析 */\n.case-study {\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 20px;\n}\n\n.case-header {\n  margin-bottom: 10px;\n}\n\n.case-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.case-content {\n  margin-bottom: 15px;\n}\n\n.case-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.case-results {\n  display: flex;\n  justify-content: space-between;\n  border-top: 1px solid #eee;\n  padding-top: 15px;\n}\n\n.result-item {\n  text-align: center;\n  flex: 1;\n}\n\n.result-label {\n  font-size: 12px;\n  color: #999;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.result-value {\n  font-size: 18px;\n  font-weight: 600;\n  color: #FF6B6B;\n}\n\n/* 联系我们 */\n.contact-section {\n  margin: 0 15px 30px;\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 10px;\n  text-align: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n}\n\n.contact-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 10px;\n}\n\n.contact-desc {\n  font-size: 14px;\n  color: #666;\n  display: block;\n  margin-bottom: 15px;\n}\n\n.contact-btn {\n  background-color: #FF6B6B;\n  color: #fff;\n  border: none;\n  border-radius: 20px;\n  padding: 8px 20px;\n  font-size: 14px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/festival-redpacket-strategy.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAoWA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO,CAEP;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,MAAM;AAAA,QACpC,SAAS,SAAS,KAAK;AACrBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9XA,GAAG,WAAW,eAAe;"}