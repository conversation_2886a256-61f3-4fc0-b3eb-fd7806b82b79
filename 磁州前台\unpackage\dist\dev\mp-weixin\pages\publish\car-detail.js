"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  ReportCard();
}
const ReportCard = () => "../../components/ReportCard.js";
const _sfc_main = {
  __name: "car-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.onMounted(() => {
      try {
        const sysInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = sysInfo.statusBarHeight || 20;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/car-detail.vue:220", "获取状态栏高度失败", e);
      }
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      const id = options.id || "";
      common_vendor.index.__f__("log", "at pages/publish/car-detail.vue:230", "车辆详情页ID:", id);
      loadRelatedCars();
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const isCollected = common_vendor.ref(false);
    const carData = common_vendor.ref({
      id: "car12345",
      title: "2019款大众帕萨特",
      price: "15.8万",
      tags: ["一手车", "车况良好", "可分期"],
      publishTime: Date.now() - 864e5 * 2,
      // 2天前
      images: [
        "/static/images/car1.jpg",
        "/static/images/car2.jpg",
        "/static/images/car3.jpg"
      ],
      brand: "大众帕萨特 2019款 330TSI 豪华版",
      registerTime: "2019年6月",
      mileage: "3.5万公里",
      emission: "国六",
      configs: [
        { name: "自动挡", icon: "icon-gear" },
        { name: "真皮座椅", icon: "icon-seat" },
        { name: "倒车影像", icon: "icon-camera" },
        { name: "定速巡航", icon: "icon-cruise" },
        { name: "天窗", icon: "icon-sunroof" },
        { name: "导航", icon: "icon-navigation" }
      ],
      conditions: [
        { label: "车况", value: "良好" },
        { label: "过户次数", value: "0次" },
        { label: "保养记录", value: "全程4S店" },
        { label: "事故情况", value: "无事故" }
      ],
      description: "2019年6月上牌，一手车，全程4S店保养，车况良好，无事故，无泡水，无火烧。配置齐全，真皮座椅，倒车影像，定速巡航，天窗，导航等。价格可谈，支持分期付款。",
      owner: {
        name: "张先生",
        avatar: "/static/images/avatar.png",
        type: "个人",
        rating: "A+",
        isVerified: true
      },
      contact: {
        name: "张先生",
        phone: "13912345678"
      }
    });
    const similarCars = common_vendor.ref([
      {
        id: "car001",
        title: "2018款大众帕萨特",
        price: "14.5万",
        brand: "大众帕萨特",
        mileage: "4.2万公里",
        image: "/static/images/car-similar1.jpg"
      },
      {
        id: "car002",
        title: "2020款大众帕萨特",
        price: "16.8万",
        brand: "大众帕萨特",
        mileage: "2.8万公里",
        image: "/static/images/car-similar2.jpg"
      }
    ]);
    const relatedCars = common_vendor.ref([]);
    const loadRelatedCars = () => {
      setTimeout(() => {
        relatedCars.value = [
          ...similarCars.value.map((car) => ({
            ...car,
            tags: ["车况好", "无事故"]
          })),
          {
            id: "car003",
            title: "2019款本田雅阁",
            price: "16.2万",
            brand: "本田雅阁",
            mileage: "3.6万公里",
            image: "/static/images/car-similar3.jpg",
            tags: ["一手车", "全程4S保养", "无事故"]
          }
        ];
      }, 500);
    };
    const navigateToCarDetail = (carId) => {
      if (carId === carData.value.id) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/publish/car-detail?id=${carId}`
      });
    };
    const navigateToCarList = (e) => {
      var _a;
      if (e)
        e.stopPropagation();
      const carCategory = ((_a = carData.value.tags) == null ? void 0 : _a[0]) || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=second_car&title=${encodeURIComponent("二手车辆")}&category=${encodeURIComponent(carCategory)}&active=second_car`
      });
    };
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: carData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const openChat = () => {
      if (!carData.value.owner || !carData.value.owner.id) {
        common_vendor.index.showToast({
          title: "无法获取发布者信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/chat/index?userId=${carData.value.owner.id}&username=${encodeURIComponent(carData.value.owner.name || "用户")}`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(carData.value.title),
        e: common_vendor.t(carData.value.price),
        f: common_vendor.f(carData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        g: common_vendor.t(formatTime(carData.value.publishTime)),
        h: common_vendor.f(carData.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        i: common_vendor.t(carData.value.brand),
        j: common_vendor.t(carData.value.registerTime),
        k: common_vendor.t(carData.value.mileage),
        l: common_vendor.t(carData.value.emission),
        m: common_vendor.f(carData.value.configs, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.name),
            c: index
          };
        }),
        n: common_vendor.f(carData.value.conditions, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        o: common_vendor.t(carData.value.description),
        p: carData.value.owner.avatar,
        q: common_vendor.t(carData.value.owner.name),
        r: common_vendor.t(carData.value.owner.type),
        s: common_vendor.t(carData.value.owner.rating),
        t: carData.value.owner.isVerified
      }, carData.value.owner.isVerified ? {} : {}, {
        v: common_vendor.t(carData.value.contact.name),
        w: common_vendor.t(carData.value.contact.phone),
        x: common_vendor.o(callPhone),
        y: common_vendor.f(relatedCars.value.slice(0, 3), (car, index, i0) => {
          return common_vendor.e({
            a: car.image,
            b: common_vendor.t(car.title),
            c: common_vendor.t(car.brand),
            d: common_vendor.t(car.mileage),
            e: common_vendor.f(car.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            f: car.tags && car.tags.length > 2
          }, car.tags && car.tags.length > 2 ? {
            g: common_vendor.t(car.tags.length - 2)
          } : {}, {
            h: common_vendor.t(car.price),
            i: index,
            j: common_vendor.o(($event) => navigateToCarDetail(car.id), index)
          });
        }),
        z: relatedCars.value.length === 0
      }, relatedCars.value.length === 0 ? {
        A: common_assets._imports_1$3
      } : {}, {
        B: relatedCars.value.length > 0
      }, relatedCars.value.length > 0 ? {
        C: common_vendor.o(navigateToCarList)
      } : {}, {
        D: common_assets._imports_12,
        E: common_vendor.o(goToHome),
        F: common_assets._imports_3$2,
        G: common_vendor.o(toggleCollect),
        H: common_assets._imports_3$3,
        I: common_assets._imports_14,
        J: common_vendor.o(openChat),
        K: common_vendor.o(callPhone)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/car-detail.js.map
