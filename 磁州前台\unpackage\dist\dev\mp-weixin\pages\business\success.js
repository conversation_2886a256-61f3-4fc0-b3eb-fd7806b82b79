"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  ConfigurablePremiumActions();
}
const ConfigurablePremiumActions = () => "../../components/premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "success",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const shopInfo = common_vendor.ref({
      logo: "",
      name: "品牌专卖店",
      id: "shop123456"
    });
    const celebrateIcon = common_vendor.ref("");
    const memberType = common_vendor.ref("基础版");
    const expiryDate = common_vendor.ref("2024-12-31");
    const isTestMerchant = common_vendor.ref(false);
    const isVerified = common_vendor.ref(false);
    const refreshCount = common_vendor.ref(0);
    const activeTab = common_vendor.ref("top");
    const topData = common_vendor.ref({
      id: "",
      title: "商家置顶",
      description: "置顶您的店铺，获得更多曝光"
    });
    const refreshData = common_vendor.ref({
      id: "",
      title: "商家刷新",
      description: "刷新您的店铺信息到最新"
    });
    const memberClass = common_vendor.computed(() => {
      const classMap = {
        "基础版": "basic-badge",
        "高级版": "premium-badge",
        "尊贵版": "deluxe-badge",
        "免费版": "free-badge",
        "测试版": "test-badge"
      };
      return classMap[memberType.value] || "basic-badge";
    });
    common_vendor.onLoad((options) => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      getCelebrateIcon();
      if (options.isTest === "true") {
        isTestMerchant.value = true;
        if (options.shopName) {
          shopInfo.value.name = decodeURIComponent(options.shopName);
        }
        if (options.memberType) {
          memberType.value = decodeURIComponent(options.memberType);
        }
      } else if (options.id) {
        loadMerchantData(options.id);
      } else if (options.shopId) {
        getShopInfo(options.shopId);
      }
      const shopId = options.shopId || options.id || "shop_" + Date.now();
      topData.value.id = shopId;
      refreshData.value.id = shopId;
      if (shopInfo.value.id !== shopId) {
        shopInfo.value.id = shopId;
      }
      if (options.memberType && !isTestMerchant.value) {
        memberType.value = decodeURIComponent(options.memberType);
        const now = /* @__PURE__ */ new Date();
        if (memberType.value === "免费版") {
          now.setMonth(now.getMonth() + 1);
        } else {
          now.setFullYear(now.getFullYear() + 1);
        }
        expiryDate.value = now.getFullYear() + "-" + String(now.getMonth() + 1).padStart(2, "0") + "-" + String(now.getDate()).padStart(2, "0");
      }
      try {
        const userRefreshData = common_vendor.index.getStorageSync("userRefreshData") || {};
        const shopId2 = options.id || options.shopId || "";
        if (shopId2 && userRefreshData[shopId2]) {
          refreshCount.value = userRefreshData[shopId2].count || 0;
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/business/success.vue:333", "加载刷新次数失败:", e);
        refreshCount.value = 0;
      }
    });
    const goBack = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const goToVerify = () => {
      common_vendor.index.navigateTo({
        url: "/pages/business/verify?id=" + shopInfo.value.id
      });
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({
        url
      });
    };
    const goToMerchantCenter = () => {
      common_vendor.index.switchTab({
        url: "/pages/my/my"
      });
    };
    const goToShopPage = () => {
      common_vendor.index.navigateTo({
        url: "/pages/business/shop-detail?id=" + shopInfo.value.id
      });
    };
    const contactCustomerService = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    };
    const handleTopCompleted = (result) => {
      common_vendor.index.__f__("log", "at pages/business/success.vue:378", "置顶操作完成:", result);
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "广告观看完成，店铺已置顶",
          icon: "success"
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: `付费成功，店铺已置顶`,
          icon: "success"
        });
      }
    };
    const handleTopCancelled = (result) => {
      common_vendor.index.__f__("log", "at pages/business/success.vue:395", "置顶操作取消:", result);
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "已取消观看广告",
          icon: "none"
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: "已取消支付",
          icon: "none"
        });
      }
    };
    const handleRefreshCompleted = (result) => {
      common_vendor.index.__f__("log", "at pages/business/success.vue:412", "刷新操作完成:", result);
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "广告观看完成，店铺已刷新",
          icon: "success"
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: `付费成功，店铺已刷新`,
          icon: "success"
        });
      }
      if (result.data && result.data.remainingCount !== void 0) {
        refreshCount.value = result.data.remainingCount;
      }
    };
    const handleRefreshCancelled = (result) => {
      common_vendor.index.__f__("log", "at pages/business/success.vue:434", "刷新操作取消:", result);
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "已取消观看广告",
          icon: "none"
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: "已取消支付",
          icon: "none"
        });
      }
    };
    const getShopInfo = (shopId) => {
      setTimeout(() => {
        shopInfo.value = {
          logo: "/static/images/default-shop.png",
          name: "示例店铺 " + shopId,
          id: shopId
        };
      }, 500);
    };
    const loadMerchantData = (id) => {
      common_vendor.index.__f__("log", "at pages/business/success.vue:461", "加载商家数据:", id);
      try {
        const merchantTestData = common_vendor.index.getStorageSync("merchantTestData") || [];
        const merchant = merchantTestData.find((item) => item.id === id);
        if (merchant) {
          isTestMerchant.value = true;
          shopInfo.value = {
            logo: merchant.logo || "/static/images/default-shop.png",
            name: merchant.shopName || "测试商家",
            id: merchant.id
          };
          memberType.value = "测试版";
          return;
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/business/success.vue:479", "获取测试商家数据失败:", e);
      }
      if (id === "test") {
        isTestMerchant.value = true;
        shopInfo.value.name = "内部测试商家";
        memberType.value = "测试版";
      }
    };
    const getCelebrateIcon = () => {
      setTimeout(() => {
      }, 1e3);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: common_assets._imports_0$13,
        c: common_vendor.o(goBack),
        d: celebrateIcon.value || "/static/images/tabbar/成功.png",
        e: shopInfo.value.logo || "/static/images/default-shop.png",
        f: common_vendor.t(shopInfo.value.name),
        g: !isVerified.value
      }, !isVerified.value ? {
        h: common_assets._imports_1$7,
        i: common_vendor.o(goToVerify)
      } : {}, {
        j: isVerified.value
      }, isVerified.value ? {
        k: common_assets._imports_2$5
      } : {}, {
        l: isTestMerchant.value
      }, isTestMerchant.value ? {} : {}, {
        m: memberType.value
      }, memberType.value ? {
        n: common_vendor.t(memberType.value),
        o: common_vendor.n(memberClass.value),
        p: common_vendor.t(expiryDate.value)
      } : {}, {
        q: !isVerified.value
      }, !isVerified.value ? {
        r: common_assets._imports_1$7,
        s: common_vendor.o(goToVerify)
      } : {}, {
        t: common_assets._imports_5$3,
        v: common_vendor.o(($event) => navigateTo("/subPackages/merchant-plugin/pages/goods")),
        w: common_assets._imports_15$2,
        x: common_vendor.o(($event) => navigateTo("/subPackages/merchant-plugin/pages/orders")),
        y: common_assets._imports_7$2,
        z: common_vendor.o(($event) => navigateTo("/subPackages/merchant-plugin/pages/marketing")),
        A: common_assets._imports_11$3,
        B: common_vendor.o(($event) => navigateTo("/subPackages/merchant-plugin/pages/analysis")),
        C: common_vendor.o(goToMerchantCenter),
        D: common_vendor.o(goToShopPage),
        E: activeTab.value === "top" ? 1 : "",
        F: common_vendor.o(($event) => activeTab.value = "top"),
        G: activeTab.value === "refresh" ? 1 : "",
        H: common_vendor.o(($event) => activeTab.value = "refresh"),
        I: activeTab.value === "top"
      }, activeTab.value === "top" ? {
        J: common_vendor.o(handleTopCompleted),
        K: common_vendor.o(handleTopCancelled),
        L: common_vendor.p({
          showMode: "direct",
          pageType: "merchant_top",
          itemData: topData.value
        })
      } : {}, {
        M: activeTab.value === "refresh"
      }, activeTab.value === "refresh" ? {
        N: common_vendor.o(handleRefreshCompleted),
        O: common_vendor.o(handleRefreshCancelled),
        P: common_vendor.p({
          showMode: "direct",
          pageType: "merchant_refresh",
          itemData: refreshData.value
        })
      } : {}, {
        Q: common_assets._imports_0$14,
        R: common_vendor.o(($event) => navigateTo("/subPackages/merchant-plugin/pages/goods")),
        S: common_assets._imports_0$14,
        T: common_vendor.o(($event) => navigateTo("/subPackages/merchant-plugin/pages/marketing")),
        U: common_assets._imports_0$14,
        V: common_vendor.o(goToMerchantCenter),
        W: common_vendor.o(contactCustomerService)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/business/success.js.map
