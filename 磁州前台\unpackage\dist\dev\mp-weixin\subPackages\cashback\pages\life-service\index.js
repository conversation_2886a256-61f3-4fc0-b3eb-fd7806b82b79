"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const CustomNavbar = () => "../../components/CustomNavbar.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      serviceType: "",
      pageTitle: "",
      vipList: [
        {
          id: 1,
          name: "腾讯视频VIP",
          logo: "/static/images/cashback/vip-tencent.png",
          desc: "月卡/季卡/年卡",
          price: "19.8"
        },
        {
          id: 2,
          name: "爱奇艺VIP",
          logo: "/static/images/cashback/vip-iqiyi.png",
          desc: "月卡/季卡/年卡",
          price: "19.0"
        },
        {
          id: 3,
          name: "优酷VIP",
          logo: "/static/images/cashback/vip-youku.png",
          desc: "月卡/季卡/年卡",
          price: "18.8"
        },
        {
          id: 4,
          name: "芒果TV VIP",
          logo: "/static/images/cashback/vip-mgtv.png",
          desc: "月卡/季卡/年卡",
          price: "15.0"
        },
        {
          id: 5,
          name: "网易云音乐VIP",
          logo: "/static/images/cashback/vip-netease.png",
          desc: "月卡/季卡/年卡",
          price: "8.0"
        },
        {
          id: 6,
          name: "QQ音乐VIP",
          logo: "/static/images/cashback/vip-qqmusic.png",
          desc: "月卡/季卡/年卡",
          price: "15.0"
        }
      ]
    };
  },
  onLoad(options) {
    this.serviceType = options.type || "takeout";
    this.setPageTitle();
  },
  methods: {
    setPageTitle() {
      switch (this.serviceType) {
        case "takeout":
          this.pageTitle = "外卖红包";
          break;
        case "taxi":
          this.pageTitle = "打车红包";
          break;
        case "movie":
          this.pageTitle = "电影票优惠";
          break;
        case "express":
          this.pageTitle = "快递返现";
          break;
        case "coupon":
          this.pageTitle = "优惠券";
          break;
        case "vip":
          this.pageTitle = "会员充值";
          break;
        default:
          this.pageTitle = "生活服务";
      }
    },
    goToPlatformService(platform) {
      common_vendor.index.showToast({
        title: "平台服务功能正在开发中",
        icon: "none",
        duration: 2e3
      });
    },
    goToVipDetail(vip) {
      common_vendor.index.showToast({
        title: "会员充值功能正在开发中",
        icon: "none",
        duration: 2e3
      });
    },
    getCoupon() {
      common_vendor.index.showLoading({
        title: "领取中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "领取成功",
          icon: "success"
        });
      }, 1e3);
    }
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_custom_navbar + _component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: $data.pageTitle,
      ["show-back"]: true
    }),
    b: $data.serviceType === "takeout"
  }, $data.serviceType === "takeout" ? {
    c: common_vendor.p({
      fill: "#FF6B6B",
      d: "M15.5,21L14,8H16.23L15.1,3.46L16.84,3L18.09,8H22L20.5,21H15.5M5,11H10A3,3 0 0,1 13,14H2A3,3 0 0,1 5,11M13,18A3,3 0 0,1 10,21H5A3,3 0 0,1 2,18H13M3,15H8L9.5,16.5L11,15H12A1,1 0 0,1 13,16A1,1 0 0,1 12,17H3A1,1 0 0,1 2,16A1,1 0 0,1 3,15Z"
    }),
    d: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "32",
      height: "32"
    }),
    e: common_assets._imports_0$55,
    f: common_vendor.p({
      fill: "#CCCCCC",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    g: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    h: common_vendor.o(($event) => $options.goToPlatformService("meituan")),
    i: common_assets._imports_1$51,
    j: common_vendor.p({
      fill: "#CCCCCC",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    k: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    l: common_vendor.o(($event) => $options.goToPlatformService("eleme"))
  } : {}, {
    m: $data.serviceType === "taxi"
  }, $data.serviceType === "taxi" ? {
    n: common_vendor.p({
      fill: "#FFA726",
      d: "M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z"
    }),
    o: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "32",
      height: "32"
    }),
    p: common_assets._imports_2$45,
    q: common_vendor.p({
      fill: "#CCCCCC",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    r: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    s: common_vendor.o(($event) => $options.goToPlatformService("didi")),
    t: common_assets._imports_3$38,
    v: common_vendor.p({
      fill: "#CCCCCC",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    w: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    x: common_vendor.o(($event) => $options.goToPlatformService("caocao"))
  } : {}, {
    y: $data.serviceType === "movie"
  }, $data.serviceType === "movie" ? {
    z: common_vendor.p({
      fill: "#E91E63",
      d: "M18,9H16V7H18M18,13H16V11H18M18,17H16V15H18M8,9H6V7H8M8,13H6V11H8M8,17H6V15H8M18,3V5H16V3H8V5H6V3H4V21H6V19H8V21H16V19H18V21H20V3H18Z"
    }),
    A: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "32",
      height: "32"
    }),
    B: common_assets._imports_4$28,
    C: common_vendor.p({
      fill: "#CCCCCC",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    D: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    E: common_vendor.o(($event) => $options.goToPlatformService("maoyan")),
    F: common_assets._imports_5$25,
    G: common_vendor.p({
      fill: "#CCCCCC",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    H: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    I: common_vendor.o(($event) => $options.goToPlatformService("taopiaopiao"))
  } : {}, {
    J: $data.serviceType === "express"
  }, $data.serviceType === "express" ? {
    K: common_vendor.p({
      fill: "#2196F3",
      d: "M3,14H5V20H19V14H21V21A1,1 0 0,1 20,22H4A1,1 0 0,1 3,21V14M17,4H7V2H17V4M17.5,5L12,10.5L6.5,5H17.5M20,6.4L17.9,8.5L15.5,6.1L16.9,4.7L20,7.8V6.4M5.93,4.7L7.33,6.1L4.93,8.5L2.83,6.4V7.8L5.93,4.7Z"
    }),
    L: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "32",
      height: "32"
    }),
    M: common_assets._imports_6$21,
    N: common_vendor.p({
      fill: "#CCCCCC",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    O: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    P: common_vendor.o(($event) => $options.goToPlatformService("sf")),
    Q: common_assets._imports_7$12,
    R: common_vendor.p({
      fill: "#CCCCCC",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    S: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24"
    }),
    T: common_vendor.o(($event) => $options.goToPlatformService("jd-express"))
  } : {}, {
    U: $data.serviceType === "coupon"
  }, $data.serviceType === "coupon" ? {
    V: common_assets._imports_8$10,
    W: common_vendor.o((...args) => $options.getCoupon && $options.getCoupon(...args))
  } : {}, {
    X: $data.serviceType === "vip"
  }, $data.serviceType === "vip" ? {
    Y: common_vendor.p({
      fill: "#4CAF50",
      d: "M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z"
    }),
    Z: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "32",
      height: "32"
    }),
    aa: common_vendor.f($data.vipList, (item, index, i0) => {
      return {
        a: item.logo,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.desc),
        d: common_vendor.t(item.price),
        e: index,
        f: common_vendor.o(($event) => $options.goToVipDetail(item), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0b99154b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/cashback/pages/life-service/index.js.map
