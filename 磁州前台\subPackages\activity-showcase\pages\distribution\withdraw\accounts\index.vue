<template>
  <view class="withdraw-accounts-page">
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">提现账户</view>
      <view class="nav-add" @click="addAccount">
        <text class="iconfont icon-add"></text>
      </view>
    </view>

    <!-- 账户列表 -->
    <view class="account-list">
      <view v-for="account in accounts" :key="account.id" class="account-item">
        <view class="account-icon">
          <text :class="['iconfont', getAccountIcon(account.type)]"></text>
        </view>
        <view class="account-info">
          <view class="account-name">{{ account.name }}</view>
          <view class="account-number">{{ maskAccountNumber(account.number) }}</view>
          <view v-if="account.isDefault" class="default-badge">默认</view>
        </view>
        <view class="account-actions">
          <text class="action-btn" @click="editAccount(account)">编辑</text>
          <text class="action-btn delete" @click="deleteAccount(account)">删除</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="accounts.length === 0" class="empty-state">
        <image src="/static/images/empty-account.png" class="empty-image" />
        <text class="empty-text">暂无提现账户</text>
        <text class="empty-desc">添加提现账户后可快速提现</text>
        <button class="add-btn" @click="addAccount">添加账户</button>
      </view>
    </view>

    <!-- 添加账户弹窗 -->
    <uni-popup ref="accountPopup" type="bottom">
      <view class="account-form">
        <view class="form-header">
          <text class="form-title">{{ isEdit ? '编辑' : '添加' }}提现账户</text>
          <text class="form-close" @click="closeForm">×</text>
        </view>
        
        <view class="form-content">
          <view class="form-item">
            <text class="form-label">账户类型</text>
            <picker :value="formData.typeIndex" :range="accountTypes" range-key="label" @change="onTypeChange">
              <view class="picker-value">
                <text :class="['type-icon', 'iconfont', getAccountIcon(accountTypes[formData.typeIndex].value)]"></text>
                <text>{{ accountTypes[formData.typeIndex].label }}</text>
                <text class="iconfont icon-arrow-down"></text>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">{{ getAccountLabel() }}</text>
            <input v-model="formData.name" :placeholder="getNamePlaceholder()" class="form-input" />
          </view>
          
          <view class="form-item">
            <text class="form-label">{{ getNumberLabel() }}</text>
            <input v-model="formData.number" :placeholder="getNumberPlaceholder()" class="form-input" />
          </view>
          
          <view v-if="formData.type === 'bank'" class="form-item">
            <text class="form-label">开户行</text>
            <picker :value="formData.bankIndex" :range="bankList" range-key="name" @change="onBankChange">
              <view class="picker-value">
                <text>{{ formData.bankIndex >= 0 ? bankList[formData.bankIndex].name : '请选择开户行' }}</text>
                <text class="iconfont icon-arrow-down"></text>
              </view>
            </picker>
          </view>
          
          <view v-if="formData.type === 'bank'" class="form-item">
            <text class="form-label">开户支行</text>
            <input v-model="formData.branchName" placeholder="请输入开户支行" class="form-input" />
          </view>
          
          <view class="form-item">
            <label class="checkbox-label">
              <checkbox :checked="formData.isDefault" @change="onDefaultChange" />
              <text>设为默认账户</text>
            </label>
          </view>
        </view>
        
        <view class="form-actions">
          <button class="btn-cancel" @click="closeForm">取消</button>
          <button class="btn-confirm" @click="saveAccount">保存</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

const accounts = ref([
  {
    id: 1,
    type: 'wechat',
    name: '张三',
    number: 'wx_13812345678',
    isDefault: true
  },
  {
    id: 2,
    type: 'alipay',
    name: '张三',
    number: 'alipay_13812345678',
    isDefault: false
  },
  {
    id: 3,
    type: 'bank',
    name: '张三',
    number: '6217001234567890123',
    bankName: '中国建设银行',
    branchName: '北京分行营业部',
    isDefault: false
  }
])

const isEdit = ref(false)
const currentAccount = ref(null)

const accountTypes = [
  { label: '微信零钱', value: 'wechat' },
  { label: '支付宝', value: 'alipay' },
  { label: '银行卡', value: 'bank' }
]

const bankList = [
  { name: '中国工商银行', code: 'ICBC' },
  { name: '中国建设银行', code: 'CCB' },
  { name: '中国农业银行', code: 'ABC' },
  { name: '中国银行', code: 'BOC' },
  { name: '招商银行', code: 'CMB' },
  { name: '交通银行', code: 'BOCOM' },
  { name: '中信银行', code: 'CITIC' },
  { name: '光大银行', code: 'CEB' },
  { name: '华夏银行', code: 'HXB' },
  { name: '民生银行', code: 'CMBC' }
]

const formData = reactive({
  typeIndex: 0,
  type: 'wechat',
  name: '',
  number: '',
  bankIndex: -1,
  bankName: '',
  branchName: '',
  isDefault: false
})

const getAccountIcon = (type) => {
  const icons = {
    alipay: 'icon-alipay',
    wechat: 'icon-wechat-pay',
    bank: 'icon-bank-card'
  }
  return icons[type] || 'icon-wallet'
}

const maskAccountNumber = (number) => {
  if (!number) return ''
  
  if (number.startsWith('wx_')) {
    const phone = number.substring(3)
    return phone.substring(0, 3) + '****' + phone.substring(7)
  } else if (number.startsWith('alipay_')) {
    const phone = number.substring(7)
    return phone.substring(0, 3) + '****' + phone.substring(7)
  } else if (number.length > 8) {
    return number.substring(0, 4) + '****' + number.substring(number.length - 4)
  }
  return number
}

const getAccountLabel = () => {
  const labels = {
    wechat: '微信昵称',
    alipay: '支付宝昵称',
    bank: '持卡人姓名'
  }
  return labels[formData.type] || '账户名称'
}

const getNamePlaceholder = () => {
  const placeholders = {
    wechat: '请输入微信昵称',
    alipay: '请输入支付宝昵称',
    bank: '请输入持卡人姓名'
  }
  return placeholders[formData.type] || '请输入账户名称'
}

const getNumberLabel = () => {
  const labels = {
    wechat: '微信号/手机号',
    alipay: '支付宝账号/手机号',
    bank: '银行卡号'
  }
  return labels[formData.type] || '账户号码'
}

const getNumberPlaceholder = () => {
  const placeholders = {
    wechat: '请输入微信号或绑定手机号',
    alipay: '请输入支付宝账号或手机号',
    bank: '请输入银行卡号'
  }
  return placeholders[formData.type] || '请输入账户号码'
}

const goBack = () => {
  uni.navigateBack()
}

const addAccount = () => {
  isEdit.value = false
  currentAccount.value = null
  resetForm()
  uni.$refs.accountPopup.open()
}

const editAccount = (account) => {
  isEdit.value = true
  currentAccount.value = account
  
  const typeIndex = accountTypes.findIndex(t => t.value === account.type)
  const bankIndex = account.bankName ? bankList.findIndex(b => b.name === account.bankName) : -1
  
  Object.assign(formData, {
    typeIndex,
    type: account.type,
    name: account.name,
    number: account.number,
    bankIndex,
    bankName: account.bankName || '',
    branchName: account.branchName || '',
    isDefault: account.isDefault
  })
  
  uni.$refs.accountPopup.open()
}

const deleteAccount = (account) => {
  if (account.isDefault) {
    uni.showToast({
      title: '默认账户不能删除',
      icon: 'none'
    })
    return
  }
  
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个提现账户吗？',
    success: (res) => {
      if (res.confirm) {
        const index = accounts.value.findIndex(item => item.id === account.id)
        if (index > -1) {
          accounts.value.splice(index, 1)
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    }
  })
}

const resetForm = () => {
  Object.assign(formData, {
    typeIndex: 0,
    type: 'wechat',
    name: '',
    number: '',
    bankIndex: -1,
    bankName: '',
    branchName: '',
    isDefault: false
  })
}

const onTypeChange = (e) => {
  const index = e.detail.value
  formData.typeIndex = index
  formData.type = accountTypes[index].value
  
  // 清空相关字段
  formData.bankIndex = -1
  formData.bankName = ''
  formData.branchName = ''
}

const onBankChange = (e) => {
  const index = e.detail.value
  formData.bankIndex = index
  formData.bankName = bankList[index].name
}

const onDefaultChange = (e) => {
  formData.isDefault = e.detail.value
}

const closeForm = () => {
  uni.$refs.accountPopup.close()
}

const validateForm = () => {
  if (!formData.name.trim()) {
    uni.showToast({
      title: getAccountLabel() + '不能为空',
      icon: 'none'
    })
    return false
  }
  
  if (!formData.number.trim()) {
    uni.showToast({
      title: getNumberLabel() + '不能为空',
      icon: 'none'
    })
    return false
  }
  
  if (formData.type === 'bank') {
    if (formData.bankIndex < 0) {
      uni.showToast({
        title: '请选择开户行',
        icon: 'none'
      })
      return false
    }
    
    if (!formData.branchName.trim()) {
      uni.showToast({
        title: '请输入开户支行',
        icon: 'none'
      })
      return false
    }
    
    // 验证银行卡号格式
    if (!/^\d{16,19}$/.test(formData.number)) {
      uni.showToast({
        title: '请输入正确的银行卡号',
        icon: 'none'
      })
      return false
    }
  } else {
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(formData.number)) {
      uni.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return false
    }
  }
  
  return true
}

const saveAccount = () => {
  if (!validateForm()) return
  
  uni.showLoading({
    title: '保存中...',
    mask: true
  })
  
  setTimeout(() => {
    uni.hideLoading()
    
    if (isEdit.value) {
      // 编辑账户
      const index = accounts.value.findIndex(item => item.id === currentAccount.value.id)
      if (index > -1) {
        const updatedAccount = {
          ...currentAccount.value,
          type: formData.type,
          name: formData.name,
          number: formData.type === 'wechat' ? `wx_${formData.number}` : 
                  formData.type === 'alipay' ? `alipay_${formData.number}` : formData.number,
          bankName: formData.bankName,
          branchName: formData.branchName,
          isDefault: formData.isDefault
        }
        
        // 如果设为默认，取消其他默认账户
        if (formData.isDefault) {
          accounts.value.forEach(account => {
            if (account.id !== currentAccount.value.id) {
              account.isDefault = false
            }
          })
        }
        
        accounts.value[index] = updatedAccount
      }
    } else {
      // 添加新账户
      const newAccount = {
        id: Date.now(),
        type: formData.type,
        name: formData.name,
        number: formData.type === 'wechat' ? `wx_${formData.number}` : 
                formData.type === 'alipay' ? `alipay_${formData.number}` : formData.number,
        bankName: formData.bankName,
        branchName: formData.branchName,
        isDefault: formData.isDefault
      }
      
      // 如果设为默认，取消其他默认账户
      if (formData.isDefault) {
        accounts.value.forEach(account => {
          account.isDefault = false
        })
      }
      
      accounts.value.push(newAccount)
    }
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    closeForm()
  }, 1000)
}

onMounted(() => {
  // 页面加载时获取账户列表
})
</script>

<style lang="scss" scoped>
.withdraw-accounts-page {
  min-height: 100vh;
  background: #f5f6fa;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  
  .nav-back, .nav-add {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .iconfont {
      font-size: 32rpx;
      color: #333;
    }
  }
  
  .nav-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
  
  .nav-add {
    .iconfont {
      color: #ff6b6b;
    }
  }
}

.account-list {
  padding: 30rpx;
}

.account-item {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
  
  .account-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    
    .iconfont {
      font-size: 36rpx;
      color: #fff;
    }
  }
  
  .account-info {
    flex: 1;
    
    .account-name {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .account-number {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 12rpx;
    }
    
    .default-badge {
      display: inline-block;
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      color: #fff;
      padding: 4rpx 16rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
    }
  }
  
  .account-actions {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
    
    .action-btn {
      font-size: 26rpx;
      color: #ff6b6b;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      background: rgba(255, 107, 107, 0.1);
      text-align: center;
      
      &.delete {
        color: #f44336;
        background: rgba(244, 67, 54, 0.1);
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 120rpx 30rpx;
  
  .empty-image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 40rpx;
  }
  
  .empty-text {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .empty-desc {
    display: block;
    font-size: 26rpx;
    color: #999;
    margin-bottom: 60rpx;
  }
  
  .add-btn {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: #fff;
    padding: 24rpx 80rpx;
    border-radius: 50rpx;
    font-size: 28rpx;
    border: none;
  }
}

.account-form {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .form-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
    
    .form-close {
      font-size: 48rpx;
      color: #999;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .form-content {
    padding: 40rpx 30rpx;
    max-height: 60vh;
    overflow-y: auto;
    
    .form-item {
      margin-bottom: 40rpx;
      
      .form-label {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
      }
      
      .form-input {
        width: 100%;
        padding: 24rpx 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: #333;
        border: 2rpx solid transparent;
        
        &:focus {
          border-color: #ff6b6b;
          background: #fff;
        }
      }
      
      .picker-value {
        display: flex;
        align-items: center;
        padding: 24rpx 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: #333;
        
        .type-icon {
          margin-right: 12rpx;
          font-size: 32rpx;
          color: #ff6b6b;
        }
        
        .icon-arrow-down {
          margin-left: auto;
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .checkbox-label {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #333;
        
        checkbox {
          margin-right: 20rpx;
          transform: scale(1.2);
        }
      }
    }
  }
  
  .form-actions {
    display: flex;
    padding: 30rpx;
    gap: 20rpx;
    border-top: 1rpx solid #f0f0f0;
    
    button {
      flex: 1;
      height: 88rpx;
      border-radius: 44rpx;
      font-size: 30rpx;
      border: none;
      
      &.btn-cancel {
        background: #f0f0f0;
        color: #666;
      }
      
      &.btn-confirm {
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        color: #fff;
      }
    }
  }
}
</style>