<template>
	<view class="container">
		<view class="header">
			<text class="title">商家页面跳转测试</text>
		</view>
		
		<view class="test-list">
			<view class="test-item" v-for="shop in shopList" :key="shop.id" @click="goToShopDetail(shop.id)">
				<text class="shop-name">{{shop.shopName}}</text>
				<text class="shop-desc">{{shop.category}} | {{shop.scale}}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			shopList: [
				{
					id: "1",
					shopName: "五分利电器",
					category: "数码电器",
					scale: "10-20人"
				},
				{
					id: "2",
					shopName: "金鼎家居",
					category: "家居家装",
					scale: "20-50人"
				},
				{
					id: "3",
					shopName: "鲜丰水果",
					category: "生鲜果蔬",
					scale: "5-10人"
				},
				{
					id: "4",
					shopName: "磁州书院",
					category: "文化教育",
					scale: "10-15人"
				}
			]
		}
	},
	methods: {
		goToShopDetail(id) {
			console.log('跳转到商家详情页，ID:', id);
			uni.navigateTo({
				url: `/pages/business/shop-detail?id=${id}`
			});
		}
	}
}
</script>

<style>
.container {
	padding: 40rpx;
}

.header {
	margin-bottom: 30rpx;
}

.title {
	font-size: 40rpx;
	font-weight: bold;
}

.test-list {
	background-color: #f5f7fa;
	border-radius: 16rpx;
	overflow: hidden;
}

.test-item {
	padding: 30rpx;
	background-color: #fff;
	margin-bottom: 20rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.shop-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	display: block;
}

.shop-desc {
	font-size: 24rpx;
	color: #999;
}
</style> 