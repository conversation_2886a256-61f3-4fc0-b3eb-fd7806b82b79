{"version": 3, "file": "homeApi.js", "sources": ["api/homeApi.js"], "sourcesContent": ["/**\r\n * 前台首页API模块 - 与后台管理系统数据互通\r\n * 用于获取后台管理系统配置的首页数据\r\n */\r\n\r\nimport request from '../utils/request.js';\r\n\r\n// 首页API接口\r\nexport const homeApi = {\r\n  // 获取轮播图列表 - 从后台管理系统获取\r\n  getBanners: async () => {\r\n    try {\r\n      const response = await request.get('/banners/public');\r\n      return response.data || [];\r\n    } catch (error) {\r\n      console.error('获取轮播图失败:', error);\r\n      // 返回默认轮播图\r\n      return [\r\n        { id: 1, image: '/static/images/banner/banner-1.png', url: '', title: '磁州生活网' },\r\n        { id: 2, image: '/static/images/banner/banner-2.png', url: '', title: '本地生活服务' }\r\n      ];\r\n    }\r\n  },\r\n\r\n  // 获取首页配置信息\r\n  getHomeConfig: async () => {\r\n    try {\r\n      const response = await request.get('/home/<USER>/public');\r\n      return response.data || {};\r\n    } catch (error) {\r\n      console.error('获取首页配置失败:', error);\r\n      return {\r\n        site_title: '磁州生活网',\r\n        site_description: '本地生活服务平台',\r\n        contact_phone: '',\r\n        contact_email: '',\r\n        home_notice: '',\r\n        features: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // 获取首页统计数据\r\n  getHomeStats: async () => {\r\n    try {\r\n      const response = await request.get('/home/<USER>/public');\r\n      return response.data || {};\r\n    } catch (error) {\r\n      console.error('获取首页统计失败:', error);\r\n      return {\r\n        banner_count: 0,\r\n        active_banner_count: 0,\r\n        total_views: 0,\r\n        today_views: 0\r\n      };\r\n    }\r\n  },\r\n\r\n  // 获取服务分类 - 从后台管理系统获取\r\n  getServiceCategories: async () => {\r\n    try {\r\n      const response = await request.get('/services/categories/public');\r\n      return response.data || [];\r\n    } catch (error) {\r\n      console.error('获取服务分类失败:', error);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // 获取商家推荐 - 从后台管理系统获取\r\n  getMerchantRecommend: async () => {\r\n    try {\r\n      const response = await request.get('/merchants/recommend/public');\r\n      return response.data || [];\r\n    } catch (error) {\r\n      console.error('获取商家推荐失败:', error);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // 获取同城资讯 - 从后台管理系统获取\r\n  getCityNews: async () => {\r\n    try {\r\n      const response = await request.get('/news/city/public');\r\n      return response.data || [];\r\n    } catch (error) {\r\n      console.error('获取同城资讯失败:', error);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // 获取特色功能配置\r\n  getFeatureConfig: async () => {\r\n    try {\r\n      const response = await request.get('/features/config/public');\r\n      return response.data || [];\r\n    } catch (error) {\r\n      console.error('获取特色功能配置失败:', error);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // 记录首页访问统计\r\n  recordPageView: async () => {\r\n    try {\r\n      await request.post('/home/<USER>', {\r\n        timestamp: Date.now(),\r\n        user_agent: navigator.userAgent,\r\n        referrer: document.referrer\r\n      });\r\n    } catch (error) {\r\n      console.error('记录页面访问失败:', error);\r\n    }\r\n  },\r\n\r\n  // 获取广告横幅\r\n  getAdBanner: async () => {\r\n    try {\r\n      const response = await request.get('/ads/banner/public');\r\n      return response.data || null;\r\n    } catch (error) {\r\n      console.error('获取广告横幅失败:', error);\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n// 导出默认对象\r\nexport default homeApi;"], "names": ["request", "uni"], "mappings": ";;;AAQY,MAAC,UAAU;AAAA;AAAA,EAErB,YAAY,YAAY;AACtB,QAAI;AACF,YAAM,WAAW,MAAMA,cAAAA,QAAQ,IAAI,iBAAiB;AACpD,aAAO,SAAS,QAAQ;IACzB,SAAQ,OAAO;AACdC,iEAAc,YAAY,KAAK;AAE/B,aAAO;AAAA,QACL,EAAE,IAAI,GAAG,OAAO,sCAAsC,KAAK,IAAI,OAAO,QAAS;AAAA,QAC/E,EAAE,IAAI,GAAG,OAAO,sCAAsC,KAAK,IAAI,OAAO,SAAU;AAAA,MACxF;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,eAAe,YAAY;AACzB,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,qBAAqB;AACxD,aAAO,SAAS,QAAQ;IACzB,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,wBAAA,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,eAAe;AAAA,QACf,aAAa;AAAA,QACb,UAAU,CAAE;AAAA,MACpB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,cAAc,YAAY;AACxB,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,oBAAoB;AACvD,aAAO,SAAS,QAAQ;IACzB,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,wBAAA,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,cAAc;AAAA,QACd,qBAAqB;AAAA,QACrB,aAAa;AAAA,QACb,aAAa;AAAA,MACrB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,sBAAsB,YAAY;AAChC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,6BAA6B;AAChE,aAAO,SAAS,QAAQ;IACzB,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,wBAAA,aAAa,KAAK;AAChC,aAAO;IACR;AAAA,EACF;AAAA;AAAA,EAGD,sBAAsB,YAAY;AAChC,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,6BAA6B;AAChE,aAAO,SAAS,QAAQ;IACzB,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,wBAAA,aAAa,KAAK;AAChC,aAAO;IACR;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,YAAY;AACvB,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,mBAAmB;AACtD,aAAO,SAAS,QAAQ;IACzB,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,wBAAA,aAAa,KAAK;AAChC,aAAO;IACR;AAAA,EACF;AAAA;AAAA,EAGD,kBAAkB,YAAY;AAC5B,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,yBAAyB;AAC5D,aAAO,SAAS,QAAQ;IACzB,SAAQ,OAAO;AACdC,oBAAA,MAAA,MAAA,SAAA,wBAAc,eAAe,KAAK;AAClC,aAAO;IACR;AAAA,EACF;AAAA;AAAA,EAGD,gBAAgB,YAAY;AAC1B,QAAI;AACF,YAAMD,cAAO,QAAC,KAAK,qBAAqB;AAAA,QACtC,WAAW,KAAK,IAAK;AAAA,QACrB,YAAY,UAAU;AAAA,QACtB,UAAU,SAAS;AAAA,MAC3B,CAAO;AAAA,IACF,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,yBAAA,aAAa,KAAK;AAAA,IACjC;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,YAAY;AACvB,QAAI;AACF,YAAM,WAAW,MAAMD,cAAAA,QAAQ,IAAI,oBAAoB;AACvD,aAAO,SAAS,QAAQ;AAAA,IACzB,SAAQ,OAAO;AACdC,oBAAc,MAAA,MAAA,SAAA,yBAAA,aAAa,KAAK;AAChC,aAAO;AAAA,IACR;AAAA,EACF;AACH;;"}