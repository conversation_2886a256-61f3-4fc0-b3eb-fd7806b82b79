"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_svg + _component_circle)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    common_vendor.ref("");
    const orderStats = common_vendor.ref({
      totalCount: 156,
      todayCount: 12,
      pendingCount: 8,
      totalCommission: 2456.78
    });
    const orderTabs = common_vendor.ref([
      { name: "全部", status: "all" },
      { name: "待付款", status: "pending_payment" },
      { name: "待发货", status: "pending_delivery" },
      { name: "已发货", status: "delivered" },
      { name: "已完成", status: "completed" },
      { name: "已失效", status: "invalid" }
    ]);
    const orderList = common_vendor.ref([
      {
        id: "O001",
        orderNumber: "20240101001",
        status: "completed",
        createTime: "2024-01-01 14:30",
        productName: "iPhone 15 Pro 深空黑 256GB",
        productImage: "https://via.placeholder.com/120",
        specification: "深空黑 256GB",
        productPrice: 8999,
        quantity: 1,
        customerName: "张三",
        commission: 449.95
      },
      {
        id: "O002",
        orderNumber: "20240102001",
        status: "pending_payment",
        createTime: "2024-01-02 10:15",
        productName: "华为Mate 60 Pro 雅川青 512GB",
        productImage: "https://via.placeholder.com/120",
        specification: "雅川青 512GB",
        productPrice: 6999,
        quantity: 1,
        customerName: "李四",
        commission: 349.95
      },
      {
        id: "O003",
        orderNumber: "20240103001",
        status: "pending_delivery",
        createTime: "2024-01-03 16:20",
        productName: "小米14 Ultra 白色 1TB",
        productImage: "https://via.placeholder.com/120",
        specification: "白色 1TB",
        productPrice: 5999,
        quantity: 2,
        customerName: "王五",
        commission: 599.9
      }
    ]);
    const filteredOrders = common_vendor.computed(() => {
      const status = orderTabs.value[currentTab.value].status;
      if (status === "all") {
        return orderList.value;
      }
      return orderList.value.filter((order) => order.status === status);
    });
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const showSearch = () => {
      common_vendor.index.showToast({
        title: "搜索功能开发中",
        icon: "none"
      });
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const getStatusColor = (status) => {
      switch (status) {
        case "pending_payment":
          return "#FF9500";
        case "pending_delivery":
          return "#5AC8FA";
        case "delivered":
          return "#34C759";
        case "completed":
          return "#AC39FF";
        case "invalid":
          return "#8E8E93";
        default:
          return "#333333";
      }
    };
    const getStatusBackground = (status) => {
      switch (status) {
        case "pending_payment":
          return "rgba(255,149,0,0.1)";
        case "pending_delivery":
          return "rgba(90,200,250,0.1)";
        case "delivered":
          return "rgba(52,199,89,0.1)";
        case "completed":
          return "rgba(172,57,255,0.1)";
        case "invalid":
          return "rgba(142,142,147,0.1)";
        default:
          return "rgba(51,51,51,0.1)";
      }
    };
    const getStatusText = (status) => {
      switch (status) {
        case "pending_payment":
          return "待付款";
        case "pending_delivery":
          return "待发货";
        case "delivered":
          return "已发货";
        case "completed":
          return "已完成";
        case "invalid":
          return "已失效";
        default:
          return "未知状态";
      }
    };
    const getOrderActions = (status) => {
      switch (status) {
        case "pending_payment":
          return [
            { key: "remind", text: "提醒付款", primary: false },
            { key: "cancel", text: "取消订单", primary: false }
          ];
        case "pending_delivery":
          return [
            { key: "track", text: "查看物流", primary: true }
          ];
        case "delivered":
          return [
            { key: "track", text: "查看物流", primary: true }
          ];
        default:
          return [];
      }
    };
    const handleAction = (action, order) => {
      switch (action) {
        case "remind":
          common_vendor.index.showToast({ title: "已提醒用户付款", icon: "success" });
          break;
        case "cancel":
          common_vendor.index.showModal({
            title: "确认取消",
            content: "确定要取消这个订单吗？",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.showToast({ title: "订单已取消", icon: "success" });
              }
            }
          });
          break;
        case "track":
          common_vendor.index.showToast({ title: "查看物流功能开发中", icon: "none" });
          break;
      }
    };
    const viewOrderDetail = (order) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/distribution/order-detail/index?id=${order.id}`
      });
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    common_vendor.onMounted(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(navigateBack),
        d: common_vendor.p({
          cx: "11",
          cy: "11",
          r: "8",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2"
        }),
        e: common_vendor.p({
          d: "m21 21-4.35-4.35",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        f: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        g: common_vendor.o(showSearch),
        h: common_vendor.t(orderStats.value.totalCount),
        i: common_vendor.t(orderStats.value.todayCount),
        j: common_vendor.t(orderStats.value.pendingCount),
        k: common_vendor.t(orderStats.value.totalCommission),
        l: common_vendor.f(orderTabs.value, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.name),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        m: common_vendor.f(filteredOrders.value, (order, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(order.orderNumber),
            b: common_vendor.t(order.createTime),
            c: common_vendor.t(getStatusText(order.status)),
            d: getStatusColor(order.status),
            e: getStatusBackground(order.status),
            f: order.productImage,
            g: common_vendor.t(order.productName),
            h: common_vendor.t(order.specification),
            i: common_vendor.t(order.productPrice),
            j: common_vendor.t(order.quantity),
            k: common_vendor.t(order.customerName),
            l: common_vendor.t(order.commission),
            m: getOrderActions(order.status).length > 0
          }, getOrderActions(order.status).length > 0 ? {
            n: common_vendor.f(getOrderActions(order.status), (action, k1, i1) => {
              return {
                a: common_vendor.t(action.text),
                b: action.primary ? "#FFFFFF" : "#AC39FF",
                c: action.key,
                d: action.primary ? "linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)" : "transparent",
                e: action.primary ? "none" : "2rpx solid #AC39FF",
                f: common_vendor.o(($event) => handleAction(action.key), action.key)
              };
            })
          } : {}, {
            o: order.id,
            p: common_vendor.o(($event) => viewOrderDetail(order), order.id)
          });
        }),
        n: filteredOrders.value.length === 0
      }, filteredOrders.value.length === 0 ? {
        o: common_vendor.p({
          d: "M9 17h6M9 13h6M9 9h6M5 21V5a2 2 0 012-2h10a2 2 0 012 2v16l-3-2-2 2-2-2-2 2-2-2-3 2z",
          stroke: "#C7C7CC",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "80",
          height: "80"
        }),
        q: common_vendor.t(orderTabs.value[currentTab.value].name),
        r: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/index"))
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8fc55181"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/distribution/orders/index.js.map
