"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const services_rewardedAdService = require("../../../../services/rewardedAdService.js");
if (!Array) {
  const _component_carpool_nav = common_vendor.resolveComponent("carpool-nav");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_carpool_nav + _component_path + _component_svg)();
}
const _sfc_main = {
  __name: "success",
  setup(__props, { expose: __expose }) {
    const publishId = common_vendor.ref("");
    const publishType = common_vendor.ref("people-to-car");
    const mode = common_vendor.ref("ad");
    const formData = common_vendor.ref({
      startPoint: "磁州城区",
      endPoint: "邯郸站",
      departureDate: "2023-10-15",
      departureTime: "14:30",
      passengers: "2",
      availableSeats: 3,
      contactName: "张先生",
      contactPhone: "138****5678",
      remark: "行李少，准时出发",
      viaPoints: ["磁县政府", "磁县一中"]
    });
    const typeText = common_vendor.computed(() => {
      const typeMap = {
        "people-to-car": "人找车",
        "car-to-people": "车找人",
        "goods-to-car": "货找车",
        "car-to-goods": "车找货"
      };
      return typeMap[publishType.value] || "人找车";
    });
    const typeClass = common_vendor.computed(() => {
      return publishType.value;
    });
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      if (options && options.type) {
        publishType.value = options.type;
      }
      if (options && options.mode) {
        mode.value = options.mode;
      }
      if (options && options.id) {
        publishId.value = options.id;
        getPublishDetail();
      }
      services_rewardedAdService.rewardedAdService.initRewardedVideoAd();
      common_vendor.index.$on("rewardGranted", handleRewardGranted);
      setTimeout(() => {
        showShareTips();
      }, 1e3);
    });
    common_vendor.onUnmounted(() => {
      common_vendor.index.$off("rewardGranted", handleRewardGranted);
      services_rewardedAdService.rewardedAdService.destroy();
    });
    const onShareAppMessage = () => {
      return {
        title: `${typeText.value}：${formData.value.startPoint} → ${formData.value.endPoint}`,
        path: `/carpool-package/pages/carpool/detail/index?id=${publishId.value}`
      };
    };
    const getPublishDetail = () => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/publish/success.vue:390", "获取发布ID:", publishId.value);
    };
    const handleRewardGranted = (rewardInfo) => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/publish/success.vue:395", "奖励发放成功", rewardInfo);
      switch (rewardInfo.type) {
        case "free_top":
          common_vendor.index.showModal({
            title: "置顶成功",
            content: "您的拼车信息已成功置顶2小时，将获得更多曝光机会！",
            showCancel: false
          });
          break;
        case "free_publish":
          common_vendor.index.showModal({
            title: "发布成功",
            content: "恭喜您获得免费发布机会！",
            showCancel: false
          });
          break;
        case "free_refresh":
          common_vendor.index.showModal({
            title: "刷新成功",
            content: "您的拼车信息已刷新，排名已更新！",
            showCancel: false
          });
          break;
      }
    };
    const handleAdTop = async () => {
      try {
        const checkResult = await services_rewardedAdService.rewardedAdService.checkTodayWatchCount("top");
        if (!checkResult.canWatch) {
          common_vendor.index.showToast({
            title: checkResult.message,
            icon: "none"
          });
          return;
        }
        const success = await services_rewardedAdService.rewardedAdService.showRewardedVideoAd("free_top", publishId.value);
        if (!success) {
          common_vendor.index.showModal({
            title: "广告暂不可用",
            content: "是否选择付费置顶？",
            success: (res) => {
              if (res.confirm) {
                handlePaidTop();
              }
            }
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at carpool-package/pages/carpool/publish/success.vue:473", "广告置顶失败", error);
        common_vendor.index.showToast({
          title: "操作失败，请重试",
          icon: "none"
        });
      }
    };
    const handlePaidTop = () => {
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/premium/top?id=${publishId.value}`
      });
    };
    const shareTipsVisible = common_vendor.ref(false);
    const qrcodeVisible = common_vendor.ref(false);
    const wechatId = common_vendor.ref("cishangtc");
    const beforeShare = () => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/publish/success.vue:499", "用户点击了分享按钮");
    };
    const showShareTips = () => {
      shareTipsVisible.value = true;
    };
    const hideShareTips = () => {
      shareTipsVisible.value = false;
    };
    const jumpToDetailAndShare = () => {
      hideShareTips();
      viewDetail();
      setTimeout(() => {
        common_vendor.index.showShareMenu({
          withShareTicket: true,
          menus: ["shareAppMessage", "shareTimeline"]
        });
      }, 500);
    };
    const contactService = () => {
      hideShareTips();
      showKefu();
    };
    const showKefu = () => {
      qrcodeVisible.value = true;
    };
    const hideQrcode = () => {
      qrcodeVisible.value = false;
    };
    const copyWechatId = () => {
      common_vendor.index.setClipboardData({
        data: wechatId.value,
        success: () => {
          common_vendor.index.showToast({
            title: "微信号已复制",
            icon: "success"
          });
        }
      });
    };
    const saveQrcode = () => {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: "/static/images/qrcode.png",
        success: () => {
          common_vendor.index.showToast({
            title: "二维码已保存",
            icon: "success"
          });
        },
        fail: (err) => {
          if (err.errMsg.indexOf("auth deny") >= 0) {
            common_vendor.index.showModal({
              title: "提示",
              content: "需要您授权保存图片到相册",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          }
        }
      });
    };
    const shareInfo = () => {
      common_vendor.index.showShareMenu({
        withShareTicket: true
      });
    };
    const viewDetail = () => {
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/detail/index?id=${publishId.value}`
      });
    };
    const goHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const publishAgain = () => {
      common_vendor.index.redirectTo({
        url: "/carpool-package/pages/carpool/publish/ad-publish"
      });
    };
    const goToMyPublish = () => {
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool/my/published-list"
      });
    };
    __expose({
      onShareAppMessage
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "发布成功"
        }),
        b: common_vendor.o(goHome),
        c: common_vendor.o(viewDetail),
        d: common_vendor.o(publishAgain),
        e: common_vendor.o(shareInfo),
        f: common_vendor.o(goToMyPublish),
        g: common_assets._imports_1$42,
        h: common_assets._imports_1$42,
        i: common_vendor.o(handleAdTop),
        j: common_assets._imports_1$42,
        k: common_vendor.o(handlePaidTop),
        l: common_vendor.t(typeText.value),
        m: common_vendor.n(typeClass.value),
        n: common_vendor.t(formData.value.startPoint),
        o: common_assets._imports_0$27,
        p: common_vendor.t(formData.value.endPoint),
        q: formData.value.viaPoints && formData.value.viaPoints.length > 0
      }, formData.value.viaPoints && formData.value.viaPoints.length > 0 ? {
        r: common_assets._imports_2$36,
        s: common_vendor.f(formData.value.viaPoints, (point, index, i0) => {
          return {
            a: common_vendor.t(point),
            b: index
          };
        })
      } : {}, {
        t: common_assets._imports_3$33,
        v: common_vendor.t(formData.value.departureDate),
        w: common_assets._imports_4$24,
        x: common_vendor.t(formData.value.departureTime),
        y: publishType.value === "people-to-car"
      }, publishType.value === "people-to-car" ? {
        z: common_assets._imports_5$12,
        A: common_vendor.t(formData.value.passengers)
      } : {}, {
        B: publishType.value === "car-to-people"
      }, publishType.value === "car-to-people" ? {
        C: common_assets._imports_6$14,
        D: common_vendor.t(formData.value.availableSeats)
      } : {}, {
        E: publishType.value === "goods-to-car" || publishType.value === "car-to-goods"
      }, publishType.value === "goods-to-car" || publishType.value === "car-to-goods" ? {
        F: common_assets._imports_7$9,
        G: common_vendor.t(formData.value.goodsType || "普通货物")
      } : {}, {
        H: mode.value === "premium"
      }, mode.value === "premium" ? {
        I: common_assets._imports_8$6
      } : {}, {
        J: common_assets._imports_9$8,
        K: common_vendor.t(formData.value.contactPhone),
        L: formData.value.contactName
      }, formData.value.contactName ? {
        M: common_assets._imports_10$5,
        N: common_vendor.t(formData.value.contactName)
      } : {}, {
        O: formData.value.remark
      }, formData.value.remark ? {
        P: common_vendor.t(formData.value.remark)
      } : {}, {
        Q: common_assets._imports_2$21,
        R: common_assets._imports_0$15,
        S: common_vendor.o(beforeShare),
        T: common_assets._imports_13$2,
        U: common_vendor.o(showKefu),
        V: shareTipsVisible.value
      }, shareTipsVisible.value ? {
        W: common_assets._imports_1$42,
        X: common_vendor.o(hideShareTips),
        Y: common_vendor.o(jumpToDetailAndShare),
        Z: common_vendor.o(contactService),
        aa: common_vendor.o(() => {
        })
      } : {}, {
        ab: qrcodeVisible.value
      }, qrcodeVisible.value ? {
        ac: common_vendor.p({
          d: "M572.16 512l183.466667-183.04a42.666667 42.666667 0 1 0-60.586667-60.586667L512 451.84 328.96 268.373333a42.666667 42.666667 0 0 0-60.586667 60.586667l183.04 183.04-183.04 183.466667a42.666667 42.666667 0 0 0 60.586667 60.586666L512 572.16l183.04 183.466667a42.666667 42.666667 0 0 0 60.586667-60.586667z",
          fill: "#999999"
        }),
        ad: common_vendor.p({
          t: "1692586074385",
          viewBox: "0 0 1024 1024",
          version: "1.1",
          xmlns: "http://www.w3.org/2000/svg",
          width: "20",
          height: "20"
        }),
        ae: common_vendor.o(hideQrcode),
        af: common_assets._imports_14$4,
        ag: common_assets._imports_15$3,
        ah: common_vendor.p({
          fill: "currentColor",
          d: "M9.5,6.5v3h-3v-3H9.5 M11,5H5v6h6V5L11,5z M9.5,14.5v3h-3v-3H9.5 M11,13H5v6h6V13z M17.5,6.5v3h-3v-3H17.5 M19,5h-6v6h6V5L19,5z M13,13h1.5v1.5H13V13z M14.5,14.5H16V16h-1.5V14.5z M16,13h1.5v1.5H16V13z M13,16h1.5v1.5H13V16z M14.5,17.5H16V19h-1.5V17.5z M16,16h1.5v1.5H16V16z M17.5,14.5H19V16h-1.5V14.5z M17.5,17.5H19V19h-1.5V17.5z M22,7h-2V4h-3V2h5V7z M22,22v-5h-2v3h-3v2H22z M2,22h5v-2H4v-3H2V22z M2,2v5h2V4h3V2H2z"
        }),
        ai: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        aj: common_vendor.p({
          fill: "currentColor",
          d: "M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,7H13V13H11V7M11,15H13V17H11V15Z"
        }),
        ak: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        al: common_vendor.p({
          fill: "currentColor",
          d: "M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,6A2,2 0 0,0 10,8A2,2 0 0,0 12,10A2,2 0 0,0 14,8A2,2 0 0,0 12,6M12,13C14.67,13 20,14.33 20,17V20H4V17C4,14.33 9.33,13 12,13M12,14.9C9.03,14.9 5.9,16.36 5.9,17V18.1H18.1V17C18.1,16.36 14.97,14.9 12,14.9Z"
        }),
        am: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        an: common_vendor.p({
          fill: "currentColor",
          d: "M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"
        }),
        ao: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        ap: common_vendor.o(copyWechatId),
        aq: common_vendor.p({
          fill: "currentColor",
          d: "M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"
        }),
        ar: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        as: common_vendor.o(saveQrcode),
        at: common_vendor.o(() => {
        }),
        av: common_vendor.o(hideQrcode)
      } : {});
    };
  }
};
_sfc_main.__runtimeHooks = 2;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/publish/success.js.map
