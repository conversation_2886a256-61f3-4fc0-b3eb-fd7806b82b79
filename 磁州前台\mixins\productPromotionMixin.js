/**
 * 产品推广混入
 * 为产品详情页提供推广能力
 */
import basePromotionMixin from './basePromotionMixin';

export default {
  mixins: [basePromotionMixin],

  data() {
    return {
      // 设置页面类型为产品
      pageType: 'product'
    };
  },

  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 获取当前用户ID
      const currentUserId = this.$store?.state?.user?.userId || '';
      // 获取产品发布者ID
      const publisherId = this.productInfo?.sellerId || this.productDetail?.sellerId || '';
      // 获取产品所属店铺ID
      const shopId = this.productInfo?.shopId || this.productDetail?.shopId || '';
      // 获取当前用户的店铺ID列表
      const userShopIds = this.$store?.state?.user?.shopIds || [];

      // 判断当前用户是否是产品发布者或产品所属店铺的所有者
      return (currentUserId && publisherId && currentUserId === publisherId) || 
             (shopId && userShopIds.includes(shopId));
    },

    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 产品通常支持佣金
      const canDistribute = this.productInfo?.canDistribute !== false || this.productDetail?.canDistribute !== false; // 默认为true
      // 是否设置了佣金比例
      const hasCommissionRate = !!(this.productInfo?.commissionRate || this.productDetail?.commissionRate);
      
      return canDistribute || hasCommissionRate;
    },

    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      // 获取产品数据
      const product = this.productInfo || this.productDetail || {};
      
      // 构建推广数据
      this.promotionData = {
        id: product.id || '',
        title: product.title || product.name || '产品详情',
        image: product.image || product.coverImage || product.images?.[0] || '/static/images/product-default.jpg',
        price: product.price || 0,
        originalPrice: product.originalPrice || product.marketPrice || 0,
        description: product.description || product.intro || '',
        shopName: product.shopName || '',
        shopId: product.shopId || '',
        // 如果有更多产品特定字段，可以在这里添加
      };
    },

    /**
     * 显示产品推广浮层
     */
    showProductPromotion() {
      // 如果没有推广权限，显示提示
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '暂无推广权限',
          icon: 'none'
        });
        return;
      }

      // 打开推广工具
      this.openPromotionTools();
    }
  }
}; 