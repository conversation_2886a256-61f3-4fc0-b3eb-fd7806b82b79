{"version": 3, "file": "detail.js", "sources": ["subPackages/activity-showcase/pages/discount/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzY291bnRcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"discount-detail-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"goBack\">\r\n          <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">满减详情</view>\r\n        <view class=\"navbar-right\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载状态 -->\r\n    <view class=\"loading-container\" v-if=\"loading\">\r\n      <view class=\"loading-spinner\"></view>\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view>\r\n\r\n    <block v-else>\r\n      <!-- 商家信息卡片 -->\r\n      <view class=\"merchant-card\" :style=\"{ marginTop: navbarHeight + 'px' }\">\r\n        <view class=\"merchant-header\">\r\n          <image :src=\"discount.merchantLogo\" class=\"merchant-logo\"></image>\r\n          <view class=\"merchant-info\">\r\n            <view class=\"merchant-name\">{{ discount.merchantName }}</view>\r\n            <view class=\"merchant-rating\">\r\n              <view class=\"rating-stars\">\r\n                <image v-for=\"i in 5\" :key=\"i\" \r\n                  :src=\"i <= Math.floor(discount.merchantRating) ? \r\n                    '/static/images/tabbar/星星-选中.png' : \r\n                    '/static/images/tabbar/星星-未选.png'\" \r\n                  class=\"star-icon\"></image>\r\n              </view>\r\n              <text class=\"rating-score\">{{ discount.merchantRating }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"merchant-action\" @click=\"goToShop\">\r\n            <text>进店</text>\r\n            <text class=\"arrow-icon\">›</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 满减规则卡片 -->\r\n      <view class=\"rules-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">满减规则</text>\r\n          <view class=\"activity-time\">{{ getTimeText(discount.startTime, discount.endTime) }}</view>\r\n        </view>\r\n        \r\n        <view class=\"rules-content\">\r\n          <view class=\"rule-item\" \r\n            v-for=\"(rule, index) in discount.rules\" \r\n            :key=\"index\"\r\n            :class=\"{ 'highlight': rule.highlight }\">\r\n            <view class=\"rule-tag\" v-if=\"rule.highlight\">推荐</view>\r\n            <text class=\"rule-text\">{{ rule.text }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"rules-notice\">\r\n          <text>* 同一订单不可叠加使用多个满减优惠</text>\r\n        </view>\r\n        \r\n        <!-- 分销组件 - 醒目位置 -->\r\n        <distribution-section \r\n          :itemId=\"id\"\r\n          itemType=\"discount\"\r\n          :itemTitle=\"discount.merchantName + '满减活动'\"\r\n          :itemPrice=\"getHighestDiscountAmount()\"\r\n          :commissionRate=\"discount.commissionRate || 8\"\r\n        />\r\n      </view>\r\n      \r\n      <!-- 活动说明卡片 -->\r\n      <view class=\"description-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">活动说明</text>\r\n        </view>\r\n        <view class=\"description-content\">\r\n          <view class=\"description-item\" v-for=\"(item, index) in discount.descriptions\" :key=\"index\">\r\n            <view class=\"item-dot\"></view>\r\n            <text class=\"item-text\">{{ item }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 推荐商品卡片 -->\r\n      <view class=\"products-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">推荐商品</text>\r\n          <view class=\"card-more\" @click=\"viewAllProducts\">\r\n            <text>查看全部</text>\r\n            <text class=\"arrow-icon\">›</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <scroll-view class=\"products-scroll\" scroll-x>\r\n          <view class=\"products-list\">\r\n            <view class=\"product-item\" \r\n              v-for=\"(item, index) in discount.recommendProducts\" \r\n              :key=\"index\"\r\n              @click=\"goToProductDetail(item.id)\">\r\n              <image :src=\"item.image\" mode=\"aspectFill\" class=\"product-image\"></image>\r\n              <view class=\"product-info\">\r\n                <text class=\"product-name\">{{ item.name }}</text>\r\n                <view class=\"product-price\">\r\n                  <text class=\"price-symbol\">¥</text>\r\n                  <text class=\"price-value\">{{ item.price }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n    </block>\r\n    \r\n    <!-- 底部按钮 -->\r\n    <view class=\"bottom-bar\">\r\n      <view class=\"action-btn\" @click=\"toggleFavorite\">\r\n        <image :src=\"isFavorite ? '/static/images/tabbar/收藏-选中.png' : '/static/images/tabbar/收藏.png'\" class=\"action-icon\"></image>\r\n        <text>收藏</text>\r\n      </view>\r\n      <view class=\"action-btn\" @click=\"contactService\">\r\n        <image src=\"/static/images/tabbar/客服.png\" class=\"action-icon\"></image>\r\n        <text>客服</text>\r\n      </view>\r\n      <view class=\"use-btn\" @click=\"useDiscount\">\r\n        <text>立即使用</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport DistributionSection from '@/components/distribution-section.vue';\r\n\r\nexport default {\r\n  components: {\r\n    DistributionSection\r\n  },\r\n  data() {\r\n    return {\r\n      id: null,\r\n      statusBarHeight: 20,\r\n      navbarHeight: 82,\r\n      loading: true,\r\n      discount: {},\r\n      isFavorite: false,\r\n      commissionAmount: '30'\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    if (options && options.id) {\r\n      this.id = options.id;\r\n    }\r\n    \r\n    // 获取状态栏高度\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = systemInfo.statusBarHeight;\r\n    this.navbarHeight = this.statusBarHeight + 62; // 状态栏 + 标题栏高度\r\n    \r\n    // 模拟加载数据\r\n    setTimeout(() => {\r\n      this.loadDiscountDetail();\r\n    }, 500);\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 加载满减详情\r\n    loadDiscountDetail() {\r\n      // 模拟API加载数据\r\n      this.loading = true;\r\n      \r\n      // 在实际应用中，这里应该是从API获取数据\r\n      setTimeout(() => {\r\n        // 模拟数据\r\n        this.discount = {\r\n          id: this.id || 1,\r\n          merchantName: '星巴克咖啡',\r\n          merchantLogo: '/static/demo/shop-logo.png',\r\n          merchantRating: 4.8,\r\n          merchantId: 101,\r\n          rules: [\r\n            { text: '满50减10', highlight: false },\r\n            { text: '满100减30', highlight: true },\r\n            { text: '满200减60', highlight: false }\r\n          ],\r\n          startTime: new Date(),\r\n          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),\r\n          descriptions: [\r\n            '活动时间：2023年10月1日至2023年10月31日',\r\n            '活动范围：全场通用，特价商品除外',\r\n            '使用方式：下单结算时自动抵扣',\r\n            '限制说明：每人每天最多使用3次',\r\n            '其他说明：最终解释权归商家所有'\r\n          ],\r\n          recommendProducts: [\r\n            {\r\n              id: 1,\r\n              name: '美式咖啡（大）',\r\n              price: '30',\r\n              image: '/static/demo/product1.jpg'\r\n            },\r\n            {\r\n              id: 2,\r\n              name: '拿铁咖啡（中）',\r\n              price: '32',\r\n              image: '/static/demo/product2.jpg'\r\n            },\r\n            {\r\n              id: 3,\r\n              name: '摩卡咖啡（大）',\r\n              price: '36',\r\n              image: '/static/demo/product3.jpg'\r\n            },\r\n            {\r\n              id: 4,\r\n              name: '焦糖玛奇朵',\r\n              price: '38',\r\n              image: '/static/demo/product1.jpg'\r\n            }\r\n          ]\r\n        };\r\n        \r\n        this.loading = false;\r\n      }, 1000);\r\n    },\r\n    \r\n    // 获取活动时间文本\r\n    getTimeText(startTime, endTime) {\r\n      const start = new Date(startTime);\r\n      const end = new Date(endTime);\r\n      \r\n      const startMonth = start.getMonth() + 1;\r\n      const startDay = start.getDate();\r\n      const endMonth = end.getMonth() + 1;\r\n      const endDay = end.getDate();\r\n      \r\n      return `${startMonth}.${startDay} - ${endMonth}.${endDay}`;\r\n    },\r\n    \r\n    // 进入店铺\r\n    goToShop() {\r\n      uni.navigateTo({\r\n        url: `/subPackages/shop/pages/detail?id=${this.discount.merchantId}`\r\n      });\r\n    },\r\n    \r\n    // 查看全部商品\r\n    viewAllProducts() {\r\n      uni.navigateTo({\r\n        url: `/subPackages/shop/pages/products?merchantId=${this.discount.merchantId}`\r\n      });\r\n    },\r\n    \r\n    // 跳转到商品详情\r\n    goToProductDetail(productId) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/product/pages/detail?id=${productId}`\r\n      });\r\n    },\r\n    \r\n    // 获取最高满减金额\r\n    getHighestDiscountAmount() {\r\n      const highestRule = this.discount.rules.find(rule => rule.highlight);\r\n      if (highestRule) {\r\n        return highestRule.text.split('减')[1];\r\n      }\r\n      return '0';\r\n    },\r\n    \r\n    // 切换收藏状态\r\n    toggleFavorite() {\r\n      this.isFavorite = !this.isFavorite;\r\n      uni.showToast({\r\n        title: this.isFavorite ? '已收藏' : '已取消收藏',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    // 联系客服\r\n    contactService() {\r\n      uni.showToast({\r\n        title: '正在连接客服...',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    // 使用满减\r\n    useDiscount() {\r\n      uni.navigateTo({\r\n        url: `/subPackages/shop/pages/detail?id=${this.discount.merchantId}&discountId=${this.id}`\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.discount-detail-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 120rpx; /* 为底部按钮留出空间 */\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    padding-left: 30rpx;\r\n    padding-right: 30rpx;\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .back-btn {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    margin-right: 10rpx;\r\n  }\r\n  \r\n  .back-icon {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  \r\n  .navbar-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    position: absolute;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n  }\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100vh;\r\n  \r\n  .loading-spinner {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    border: 6rpx solid #f3f3f3;\r\n    border-top: 6rpx solid #5856D6;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    color: #8E8E93;\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 商家信息卡片 */\r\n.merchant-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 35rpx;\r\n  padding: 30rpx;\r\n  margin: 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);\r\n  \r\n  .merchant-header {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .merchant-logo {\r\n      width: 100rpx;\r\n      height: 100rpx;\r\n      border-radius: 50%;\r\n      margin-right: 20rpx;\r\n      box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\r\n    }\r\n    \r\n    .merchant-info {\r\n      flex: 1;\r\n    }\r\n    \r\n    .merchant-name {\r\n      font-size: 32rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-bottom: 10rpx;\r\n    }\r\n    \r\n    .merchant-rating {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n    \r\n    .rating-stars {\r\n      display: flex;\r\n      margin-right: 10rpx;\r\n    }\r\n    \r\n    .star-icon {\r\n      width: 24rpx;\r\n      height: 24rpx;\r\n      margin-right: 4rpx;\r\n    }\r\n    \r\n    .rating-score {\r\n      font-size: 24rpx;\r\n      color: #FF9500;\r\n      font-weight: 600;\r\n    }\r\n    \r\n    .merchant-action {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 28rpx;\r\n      color: #5856D6;\r\n      font-weight: 500;\r\n      \r\n      .arrow-icon {\r\n        font-size: 32rpx;\r\n        margin-left: 5rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 满减规则卡片 */\r\n.rules-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 35rpx;\r\n  padding: 30rpx;\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);\r\n  \r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    border-bottom: 1rpx solid #EFEFEF;\r\n    padding-bottom: 20rpx;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333333;\r\n  }\r\n  \r\n  .activity-time {\r\n    font-size: 24rpx;\r\n    color: #8E8E93;\r\n  }\r\n  \r\n  .rules-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .rule-item {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx;\r\n    margin-bottom: 15rpx;\r\n    background-color: #F5F7FA;\r\n    border-radius: 16rpx;\r\n    \r\n    &.highlight {\r\n      background: linear-gradient(135deg, #F0EFFF 0%, #E6E6FF 100%);\r\n      border: 1rpx solid #D1D1FF;\r\n      \r\n      .rule-text {\r\n        color: #5856D6;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n    \r\n    .rule-tag {\r\n      position: absolute;\r\n      top: -10rpx;\r\n      right: 20rpx;\r\n      background-color: #5856D6;\r\n      color: #FFFFFF;\r\n      font-size: 20rpx;\r\n      padding: 4rpx 12rpx;\r\n      border-radius: 10rpx;\r\n    }\r\n    \r\n    .rule-text {\r\n      font-size: 28rpx;\r\n      color: #666666;\r\n    }\r\n  }\r\n  \r\n  .rules-notice {\r\n    font-size: 24rpx;\r\n    color: #8E8E93;\r\n    padding-top: 10rpx;\r\n  }\r\n}\r\n\r\n/* 分销入口 */\r\n.distribution-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  padding: 20rpx;\r\n  background: linear-gradient(135deg, #F0EFFF 0%, #E6E6FF 100%);\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(88, 86, 214, 0.1);\r\n  \r\n  .distribution-left {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  \r\n  .distribution-icon {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    margin-right: 15rpx;\r\n    \r\n    .icon-image {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  \r\n  .distribution-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .distribution-title {\r\n    font-size: 28rpx;\r\n    font-weight: 600;\r\n    color: #333333;\r\n    margin-bottom: 5rpx;\r\n  }\r\n  \r\n  .distribution-desc {\r\n    font-size: 24rpx;\r\n    color: #5856D6;\r\n    font-weight: 600;\r\n  }\r\n  \r\n  .distribution-right {\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  \r\n  .distribution-btn {\r\n    font-size: 26rpx;\r\n    color: #5856D6;\r\n    font-weight: 600;\r\n  }\r\n  \r\n  .arrow-icon {\r\n    font-size: 32rpx;\r\n    color: #5856D6;\r\n    margin-left: 5rpx;\r\n  }\r\n}\r\n\r\n/* 活动说明卡片 */\r\n.description-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 35rpx;\r\n  padding: 30rpx;\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);\r\n  \r\n  .card-header {\r\n    margin-bottom: 20rpx;\r\n    border-bottom: 1rpx solid #EFEFEF;\r\n    padding-bottom: 20rpx;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333333;\r\n  }\r\n  \r\n  .description-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .description-item {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    margin-bottom: 15rpx;\r\n    \r\n    .item-dot {\r\n      width: 12rpx;\r\n      height: 12rpx;\r\n      border-radius: 50%;\r\n      background-color: #5856D6;\r\n      margin-top: 12rpx;\r\n      margin-right: 15rpx;\r\n      flex-shrink: 0;\r\n    }\r\n    \r\n    .item-text {\r\n      font-size: 28rpx;\r\n      color: #666666;\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n}\r\n\r\n/* 推荐商品卡片 */\r\n.products-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 35rpx;\r\n  padding: 30rpx;\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);\r\n  \r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    border-bottom: 1rpx solid #EFEFEF;\r\n    padding-bottom: 20rpx;\r\n    \r\n    .card-more {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 26rpx;\r\n      color: #8E8E93;\r\n      \r\n      .arrow-icon {\r\n        font-size: 30rpx;\r\n        margin-left: 5rpx;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .products-scroll {\r\n    width: 100%;\r\n  }\r\n  \r\n  .products-list {\r\n    display: flex;\r\n    padding: 10rpx 0;\r\n  }\r\n  \r\n  .product-item {\r\n    width: 200rpx;\r\n    margin-right: 20rpx;\r\n    border-radius: 16rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);\r\n    background-color: #FFFFFF;\r\n    transition: transform 0.3s ease;\r\n    \r\n    &:active {\r\n      transform: scale(0.98);\r\n    }\r\n    \r\n    .product-image {\r\n      width: 200rpx;\r\n      height: 200rpx;\r\n      object-fit: cover;\r\n    }\r\n    \r\n    .product-info {\r\n      padding: 15rpx;\r\n    }\r\n    \r\n    .product-name {\r\n      font-size: 24rpx;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      height: 68rpx;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 2;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n    \r\n    .product-price {\r\n      display: flex;\r\n      align-items: baseline;\r\n      margin-top: 10rpx;\r\n      \r\n      .price-symbol {\r\n        font-size: 22rpx;\r\n        color: #5856D6;\r\n      }\r\n      \r\n      .price-value {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #5856D6;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 底部按钮 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  height: 100rpx;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  z-index: 99;\r\n  \r\n  .action-btn {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-right: 40rpx;\r\n    \r\n    .action-icon {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      margin-bottom: 5rpx;\r\n    }\r\n    \r\n    text {\r\n      font-size: 22rpx;\r\n      color: #8E8E93;\r\n    }\r\n  }\r\n  \r\n  .use-btn {\r\n    flex: 1;\r\n    height: 80rpx;\r\n    background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);\r\n    border-radius: 40rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 30rpx;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    box-shadow: 0 8rpx 16rpx rgba(88, 86, 214, 0.2);\r\n    transition: transform 0.3s ease;\r\n    \r\n    &:active {\r\n      transform: translateY(5rpx);\r\n      box-shadow: 0 4rpx 8rpx rgba(88, 86, 214, 0.15);\r\n    }\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/discount/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAyIA,MAAO,sBAAqB,MAAW;AAEvC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,IAAI;AAAA,MACJ,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,SAAS;AAAA,MACT,UAAU,CAAE;AAAA,MACZ,YAAY;AAAA,MACZ,kBAAkB;AAAA,IACpB;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,QAAI,WAAW,QAAQ,IAAI;AACzB,WAAK,KAAK,QAAQ;AAAA,IACpB;AAGA,UAAM,aAAaA,oBAAI;AACvB,SAAK,kBAAkB,WAAW;AAClC,SAAK,eAAe,KAAK,kBAAkB;AAG3C,eAAW,MAAM;AACf,WAAK,mBAAkB;AAAA,IACxB,GAAE,GAAG;AAAA,EACP;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,qBAAqB;AAEnB,WAAK,UAAU;AAGf,iBAAW,MAAM;AAEf,aAAK,WAAW;AAAA,UACd,IAAI,KAAK,MAAM;AAAA,UACf,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,OAAO;AAAA,YACL,EAAE,MAAM,UAAU,WAAW,MAAO;AAAA,YACpC,EAAE,MAAM,WAAW,WAAW,KAAM;AAAA,YACpC,EAAE,MAAM,WAAW,WAAW,MAAM;AAAA,UACrC;AAAA,UACD,WAAW,oBAAI,KAAM;AAAA,UACrB,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI;AAAA,UACvD,cAAc;AAAA,YACZ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,mBAAmB;AAAA,YACjB;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,OAAO;AAAA,YACR;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,OAAO;AAAA,YACR;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,OAAO;AAAA,YACR;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,OAAO;AAAA,YACT;AAAA,UACF;AAAA;AAGF,aAAK,UAAU;AAAA,MAChB,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,YAAY,WAAW,SAAS;AAC9B,YAAM,QAAQ,IAAI,KAAK,SAAS;AAChC,YAAM,MAAM,IAAI,KAAK,OAAO;AAE5B,YAAM,aAAa,MAAM,SAAQ,IAAK;AACtC,YAAM,WAAW,MAAM;AACvB,YAAM,WAAW,IAAI,SAAQ,IAAK;AAClC,YAAM,SAAS,IAAI;AAEnB,aAAO,GAAG,UAAU,IAAI,QAAQ,MAAM,QAAQ,IAAI,MAAM;AAAA,IACzD;AAAA;AAAA,IAGD,WAAW;AACTA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qCAAqC,KAAK,SAAS,UAAU;AAAA,MACpE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+CAA+C,KAAK,SAAS,UAAU;AAAA,MAC9E,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,WAAW;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wCAAwC,SAAS;AAAA,MACxD,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,2BAA2B;AACzB,YAAM,cAAc,KAAK,SAAS,MAAM,KAAK,UAAQ,KAAK,SAAS;AACnE,UAAI,aAAa;AACf,eAAO,YAAY,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,MACtC;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,aAAa,CAAC,KAAK;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,aAAa,QAAQ;AAAA,QACjC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qCAAqC,KAAK,SAAS,UAAU,eAAe,KAAK,EAAE;AAAA,MAC1F,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7SA,GAAG,WAAW,eAAe;"}