/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.message-center-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}
.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}
.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 24px;
  height: 24px;
}
.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}
.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.clear-text {
  color: #FFFFFF;
  font-size: 14px;
}

/* 消息类型Tab */
.message-tabs {
  position: fixed;
  top: calc(44px + var(--status-bar-height));
  left: 0;
  right: 0;
  height: 44px;
  background-color: #FFFFFF;
  display: flex;
  z-index: 99;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
.tab-item {
  flex: 1;
  height: 44px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.tab-text {
  font-size: 15px;
  color: #666666;
}
.tab-item.active .tab-text {
  color: #1677FF;
  font-weight: 500;
}
.active-line {
  position: absolute;
  bottom: 0;
  width: 20px;
  height: 3px;
  background-color: #1677FF;
  border-radius: 1.5px;
}

/* 内容区域 */
.scrollable-content {
  flex: 1;
  margin-top: calc(88px + var(--status-bar-height));
  padding: 8px 0;
  /* 减少左右内边距，确保卡片圆角完全显示 */
}

/* 消息列表 */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 14px;
  /* 增加卡片之间的间距 */
  padding: 6px 0;
  /* 添加上下内边距 */
}
.message-item {
  position: relative;
  background-color: #FFFFFF;
  border-radius: 16px;
  /* 增加圆角 */
  overflow: hidden;
  padding: 18px 20px;
  /* 调整内边距 */
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
  /* 增强阴影效果 */
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 80%;
  /* 再次缩短卡片宽度 */
  margin: 0 auto;
  /* 居中显示 */
}
.unread-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #FF5722;
}
.message-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: rgba(22, 119, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}
.message-item.system .message-icon-wrapper {
  background-color: rgba(22, 119, 255, 0.1);
}
.message-item.carpool .message-icon-wrapper {
  background-color: rgba(255, 87, 34, 0.1);
}
.message-icon {
  width: 24px;
  height: 24px;
}
.message-content {
  flex: 1;
}
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.message-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
.message-time {
  font-size: 12px;
  color: #999999;
}
.message-brief {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 拼车信息 */
.carpool-info {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #EEEEEE;
}
.route-brief {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.route-text {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}
.departure-time {
  font-size: 12px;
  color: #1677FF;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}
.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}
.empty-text {
  font-size: 16px;
  color: #999999;
}

/* 加载状态 */
.loading-state {
  padding: 16px 0;
  text-align: center;
}
.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 列表底部 */
.list-bottom {
  padding: 16px 0;
  text-align: center;
}
.bottom-text {
  font-size: 14px;
  color: #999999;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
.popup-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #EEEEEE;
}
.popup-title {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
}
.popup-close {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}
.popup-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}
.message-time-display {
  font-size: 12px;
  color: #999999;
  text-align: center;
  margin-bottom: 16px;
}
.message-full-content {
  padding-bottom: 16px;
}
.content-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 拼车详情 */
.carpool-detail {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #EEEEEE;
}
.detail-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12px;
}
.route-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #F8FAFB;
  padding: 12px;
  border-radius: 8px;
}
.route-points {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.start-point, .end-point {
  display: flex;
  align-items: center;
  gap: 10px;
}
.point-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.start {
  background-color: #1677FF;
}
.end {
  background-color: #FF5722;
}
.route-line {
  width: 2px;
  height: 20px;
  background-color: #DDDDDD;
  margin-left: 5px;
}
.point-text {
  font-size: 16px;
  color: #333333;
}
.trip-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}
.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}
.info-icon {
  width: 16px;
  height: 16px;
}
.info-text {
  font-size: 14px;
  color: #666666;
}
.price {
  color: #FF5722;
  font-weight: 500;
}
.popup-footer {
  display: flex;
  border-top: 1px solid #EEEEEE;
}
.popup-button {
  flex: 1;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 16px;
}
.popup-button.delete {
  color: #FF5722;
  border-right: 1px solid #EEEEEE;
}
.popup-button.confirm {
  color: #1677FF;
  font-weight: 500;
}

/* 底部悬浮清空按钮 */
.float-clear-btn {
  position: fixed;
  bottom: 30px;
  right: 20px;
  background: linear-gradient(135deg, #FF5722, #FF7043);
  height: 44px;
  padding: 0 20px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 87, 34, 0.3);
  z-index: 99;
}
.clear-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  filter: brightness(0) invert(1);
  /* 将图标变为白色 */
}
.float-clear-text {
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
}