/**
 * 安全审计日志工具
 */
import secureRequest from './secureRequest';
import { maskSensitiveInfo } from './securityUtils';

// 日志级别
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
  SECURITY: 'security'
};

// 安全事件类型
export const SECURITY_EVENTS = {
  LOGIN_SUCCESS: 'login_success',
  LOGIN_FAILURE: 'login_failure',
  LOGOUT: 'logout',
  PASSWORD_CHANGE: 'password_change',
  PERMISSION_CHANGE: 'permission_change',
  SENSITIVE_DATA_ACCESS: 'sensitive_data_access',
  PROMOTION_CREATED: 'promotion_created',
  PROMOTION_SHARED: 'promotion_shared',
  UNAUTHORIZED_ACCESS: 'unauthorized_access'
};

/**
 * 创建安全日志
 * @param {string} eventType 事件类型
 * @param {Object} eventData 事件数据
 * @param {string} level 日志级别
 * @returns {Promise} 日志记录结果
 */
export const createSecurityLog = async (eventType, eventData = {}, level = LOG_LEVELS.INFO) => {
  try {
    // 获取设备信息
    const systemInfo = uni.getSystemInfoSync();
    
    // 处理敏感数据
    const safeEventData = { ...eventData };
    
    // 掩码处理敏感字段
    if (safeEventData.userId) {
      safeEventData.userId = maskSensitiveInfo(safeEventData.userId);
    }
    if (safeEventData.phone) {
      safeEventData.phone = maskSensitiveInfo(safeEventData.phone);
    }
    if (safeEventData.email) {
      safeEventData.email = maskSensitiveInfo(safeEventData.email);
    }
    
    // 删除高度敏感字段
    delete safeEventData.password;
    delete safeEventData.token;
    delete safeEventData.creditCard;
    
    // 构建日志数据
    const logData = {
      eventType,
      level,
      timestamp: new Date().toISOString(),
      data: safeEventData,
      device: {
        platform: systemInfo.platform,
        model: systemInfo.model,
        system: systemInfo.system,
        appVersion: systemInfo.appVersion
      }
    };
    
    // 本地记录日志
    console.log(`[${level.toUpperCase()}] ${eventType}:`, safeEventData);
    
    // 将日志发送到服务器
    if (level === LOG_LEVELS.SECURITY || level === LOG_LEVELS.ERROR) {
      return await secureRequest.post('/api/security/logs', logData);
    }
    
    return true;
  } catch (error) {
    console.error('记录安全日志失败', error);
    return false;
  }
};

/**
 * 记录登录成功事件
 * @param {Object} userData 用户数据
 */
export const logLoginSuccess = (userData) => {
  createSecurityLog(SECURITY_EVENTS.LOGIN_SUCCESS, {
    userId: userData.userId,
    username: userData.username,
    loginTime: new Date().toISOString()
  }, LOG_LEVELS.SECURITY);
};

/**
 * 记录登录失败事件
 * @param {string} username 用户名
 * @param {string} reason 失败原因
 */
export const logLoginFailure = (username, reason) => {
  createSecurityLog(SECURITY_EVENTS.LOGIN_FAILURE, {
    username,
    reason,
    attemptTime: new Date().toISOString()
  }, LOG_LEVELS.SECURITY);
};

/**
 * 记录推广相关事件
 * @param {string} eventType 事件类型
 * @param {Object} promotionData 推广数据
 */
export const logPromotionEvent = (eventType, promotionData) => {
  createSecurityLog(eventType, {
    promotionId: promotionData.id,
    contentType: promotionData.contentType,
    contentId: promotionData.contentId,
    userId: promotionData.userId,
    timestamp: new Date().toISOString()
  }, LOG_LEVELS.INFO);
};

export default {
  LOG_LEVELS,
  SECURITY_EVENTS,
  createSecurityLog,
  logLoginSuccess,
  logLoginFailure,
  logPromotionEvent
}; 