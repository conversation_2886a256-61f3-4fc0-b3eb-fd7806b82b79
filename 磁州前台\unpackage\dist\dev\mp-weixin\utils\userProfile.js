"use strict";
const common_vendor = require("../common/vendor.js");
const DEFAULT_AVATAR = "/static/images/default-avatar.png";
const DEFAULT_NICKNAME = "同城用户";
const USER_INFO_KEY = "user_info";
const getLocalUserInfo = () => {
  try {
    return common_vendor.index.getStorageSync(USER_INFO_KEY) || null;
  } catch (e) {
    common_vendor.index.__f__("error", "at utils/userProfile.js:20", "获取用户信息失败", e);
    return null;
  }
};
const saveUserInfo = (userInfo) => {
  if (!userInfo)
    return;
  try {
    const savedInfo = {
      ...getLocalUserInfo(),
      ...userInfo,
      // 添加更新时间
      updateTime: (/* @__PURE__ */ new Date()).getTime()
    };
    common_vendor.index.setStorageSync(USER_INFO_KEY, savedInfo);
    return savedInfo;
  } catch (e) {
    common_vendor.index.__f__("error", "at utils/userProfile.js:44", "保存用户信息失败", e);
    return null;
  }
};
const updateUserProfile = async (profile) => {
  try {
    const currentUser = getLocalUserInfo();
    const updatedUser = {
      ...currentUser,
      avatar: profile.avatarUrl || (currentUser == null ? void 0 : currentUser.avatar) || DEFAULT_AVATAR,
      nickname: profile.nickName || (currentUser == null ? void 0 : currentUser.nickname) || DEFAULT_NICKNAME
    };
    saveUserInfo(updatedUser);
    return updatedUser;
  } catch (e) {
    common_vendor.index.__f__("error", "at utils/userProfile.js:111", "更新用户资料失败", e);
    return null;
  }
};
const hasUserInfo = () => {
  const userInfo = getLocalUserInfo();
  return !!(userInfo && (userInfo.nickname !== DEFAULT_NICKNAME || userInfo.avatar !== DEFAULT_AVATAR));
};
exports.getLocalUserInfo = getLocalUserInfo;
exports.hasUserInfo = hasUserInfo;
exports.updateUserProfile = updateUserProfile;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/userProfile.js.map
