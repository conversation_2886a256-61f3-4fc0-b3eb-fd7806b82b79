<template>
  <view class="order-detail-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">订单详情</text>
      <view class="navbar-right">
        <view class="more-icon">⋮</view>
      </view>
    </view>
    
    <!-- 订单状态卡片 -->
    <view class="status-card" :class="'status-' + orderData.status">
      <view class="status-header">
        <view class="status-icon-container">
          <text class="status-icon">{{getStatusIcon(orderData.status)}}</text>
        </view>
        <view class="status-info">
          <text class="status-text">{{getStatusText(orderData.status)}}</text>
          <text class="status-desc">{{getStatusDescription(orderData.status)}}</text>
        </view>
      </view>
      <view class="status-timeline">
        <view 
          v-for="(step, index) in orderTimeline" 
          :key="index" 
          class="timeline-item"
          :class="{'completed': step.completed, 'current': step.current}">
          <view class="timeline-dot"></view>
          <view class="timeline-content">
            <text class="timeline-title">{{step.title}}</text>
            <text class="timeline-time">{{step.time}}</text>
          </view>
          <view v-if="index < orderTimeline.length - 1" class="timeline-line"></view>
        </view>
      </view>
    </view>
    
    <!-- 订单基本信息 -->
    <view class="info-card">
      <view class="card-title">基本信息</view>
      <view class="info-item">
        <text class="info-label">订单编号</text>
        <text class="info-value">{{orderData.orderNo}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">下单时间</text>
        <text class="info-value">{{orderData.createTime}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">支付方式</text>
        <text class="info-value">{{orderData.paymentMethod}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">支付状态</text>
        <text class="info-value" :class="'payment-' + orderData.paymentStatus">{{orderData.paymentStatus === 'paid' ? '已支付' : '待支付'}}</text>
      </view>
    </view>
    
    <!-- 客户信息 -->
    <view class="info-card">
      <view class="card-title">客户信息</view>
      <view class="customer-profile">
        <image class="customer-avatar" :src="orderData.customer.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
        <view class="customer-info">
          <text class="customer-name">{{orderData.customer.name}}</text>
          <text class="customer-id">ID: {{orderData.customer.id}}</text>
        </view>
        <view class="contact-btn" @click="contactCustomer">联系客户</view>
      </view>
      <view v-if="orderData.deliveryType === 'delivery'" class="address-info">
        <view class="address-title">
          <text class="address-icon">📍</text>
          <text>收货地址</text>
        </view>
        <view class="address-content">
          <text class="address-name">{{orderData.address.name}} {{orderData.address.phone}}</text>
          <text class="address-detail">{{orderData.address.province}}{{orderData.address.city}}{{orderData.address.district}}{{orderData.address.detail}}</text>
        </view>
      </view>
    </view>
    
    <!-- 商品信息 -->
    <view class="info-card">
      <view class="card-title">商品信息</view>
      <view 
        v-for="(item, index) in orderData.items" 
        :key="index"
        class="product-item">
        <image class="product-image" :src="item.image" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-spec">{{item.spec}}</text>
          <view class="product-price-wrap">
            <text class="product-price">¥{{item.price}}</text>
            <text class="product-quantity">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
      
      <!-- 金额信息 -->
      <view class="amount-info">
        <view class="amount-item">
          <text class="amount-label">商品总价</text>
          <text class="amount-value">¥{{orderData.productTotal}}</text>
        </view>
        <view class="amount-item">
          <text class="amount-label">配送费</text>
          <text class="amount-value">¥{{orderData.deliveryFee}}</text>
        </view>
        <view v-if="orderData.discount > 0" class="amount-item discount">
          <text class="amount-label">优惠金额</text>
          <text class="amount-value">-¥{{orderData.discount}}</text>
        </view>
        <view class="amount-item total">
          <text class="amount-label">订单总价</text>
          <text class="amount-value">¥{{orderData.totalAmount}}</text>
        </view>
      </view>
    </view>
    
    <!-- 订单备注 -->
    <view v-if="orderData.remark" class="info-card">
      <view class="card-title">订单备注</view>
      <view class="remark-content">
        {{orderData.remark}}
      </view>
    </view>
    
    <!-- 订单日志 -->
    <view class="info-card">
      <view class="card-title">订单日志</view>
      <view 
        v-for="(log, index) in orderData.logs" 
        :key="index"
        class="log-item">
        <text class="log-time">{{log.time}}</text>
        <text class="log-content">{{log.content}}</text>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="action-bar">
      <view v-if="orderData.status === 'pending'" class="action-btn primary" @click="processOrder">接单处理</view>
      <view v-if="orderData.status === 'processing'" class="action-btn primary" @click="completeOrder">完成订单</view>
      <view v-if="['pending', 'processing'].includes(orderData.status)" class="action-btn" @click="cancelOrder">取消订单</view>
      <view class="action-btn" @click="printOrder">打印订单</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderId: '',
      orderData: {
        id: '1001',
        orderNo: 'CZ20230501001',
        status: 'pending', // pending, processing, completed, cancelled, refunding
        createTime: '2023-05-01 10:30:25',
        paymentMethod: '微信支付',
        paymentStatus: 'paid', // paid, unpaid
        deliveryType: 'delivery', // delivery, self-pickup
        productTotal: '118.00',
        deliveryFee: '10.00',
        discount: '0.00',
        totalAmount: '128.00',
        remark: '请尽快发货，谢谢！',
        customer: {
          id: '10086',
          name: '张三',
          avatar: '/static/images/default-avatar.png',
          phone: '13800138000'
        },
        address: {
          name: '张三',
          phone: '13800138000',
          province: '河北省',
          city: '邯郸市',
          district: '磁县',
          detail: '北辰小区3号楼2单元101'
        },
        items: [
          {
            id: '2001',
            name: '精品水果礼盒',
            spec: '3kg装',
            price: '88.00',
            quantity: 1,
            image: '/static/images/product-1.jpg'
          },
          {
            id: '2002',
            name: '有机蔬菜套餐',
            spec: '标准版',
            price: '30.00',
            quantity: 1,
            image: '/static/images/product-2.jpg'
          }
        ],
        logs: [
          {
            time: '2023-05-01 10:30:25',
            content: '用户下单成功'
          },
          {
            time: '2023-05-01 10:31:05',
            content: '用户完成支付'
          }
        ]
      },
      orderTimeline: [
        {
          title: '下单',
          time: '2023-05-01 10:30',
          completed: true,
          current: false
        },
        {
          title: '支付',
          time: '2023-05-01 10:31',
          completed: true,
          current: false
        },
        {
          title: '商家接单',
          time: '',
          completed: false,
          current: true
        },
        {
          title: '配送中',
          time: '',
          completed: false,
          current: false
        },
        {
          title: '已完成',
          time: '',
          completed: false,
          current: false
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    getStatusText(status) {
      const texts = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成',
        cancelled: '已取消',
        refunding: '退款中'
      };
      return texts[status] || '未知状态';
    },
    getStatusIcon(status) {
      const icons = {
        pending: '⏳',
        processing: '🔄',
        completed: '✅',
        cancelled: '❌',
        refunding: '↩️'
      };
      return icons[status] || '❓';
    },
    getStatusDescription(status) {
      const descriptions = {
        pending: '订单已提交，等待商家处理',
        processing: '商家正在处理您的订单',
        completed: '订单已完成，感谢您的惠顾',
        cancelled: '订单已取消',
        refunding: '订单正在退款中'
      };
      return descriptions[status] || '';
    },
    contactCustomer() {
      uni.showToast({
        title: '联系客户功能开发中',
        icon: 'none'
      });
    },
    processOrder() {
      uni.showModal({
        title: '确认接单',
        content: '您确定要接受并处理此订单吗？',
        success: (res) => {
          if (res.confirm) {
            // 这里应该调用API更新订单状态
            this.orderData.status = 'processing';
            uni.showToast({
              title: '已接单',
              icon: 'success'
            });
          }
        }
      });
    },
    completeOrder() {
      uni.showModal({
        title: '确认完成订单',
        content: '您确定该订单已完成处理吗？',
        success: (res) => {
          if (res.confirm) {
            // 这里应该调用API更新订单状态
            this.orderData.status = 'completed';
            uni.showToast({
              title: '订单已完成',
              icon: 'success'
            });
          }
        }
      });
    },
    cancelOrder() {
      uni.showModal({
        title: '取消订单',
        content: '确认取消该订单？取消后将无法恢复',
        success: (res) => {
          if (res.confirm) {
            // 更新订单状态
            this.orderData.status = 'cancelled';
            this.orderData.logs.push({
              time: this.getCurrentTime(),
              content: '商家取消订单'
            });
            
            uni.showToast({
              title: '订单已取消',
              icon: 'success'
            });
          }
        }
      });
    },
    printOrder() {
      uni.showToast({
        title: '打印功能开发中',
        icon: 'none'
      });
    },
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hour = String(now.getHours()).padStart(2, '0');
      const minute = String(now.getMinutes()).padStart(2, '0');
      const second = String(now.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    loadOrderData() {
      // 实际项目中，这里应该从服务器获取订单数据
      console.log('加载订单数据，ID:', this.orderId);
      // 模拟异步加载
      setTimeout(() => {
        // 数据已经在data中初始化了
      }, 500);
    }
  },
  onLoad(options) {
    this.orderId = options.id || '';
    this.loadOrderData();
  }
}
</script>

<style lang="scss">
.order-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 80px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  border-radius: 0;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: transparent;
  border-radius: 0;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.more-icon {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  transform: rotate(0);
}

.status-card {
  background: #fff;
  border-radius: 16px;
  margin: 90px 16px 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.status-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.status-pending .status-icon-container {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

.status-processing .status-icon-container {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.status-completed .status-icon-container {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
}

.status-cancelled .status-icon-container {
  background: linear-gradient(135deg, #9E9E9E, #616161);
}

.status-refunding .status-icon-container {
  background: linear-gradient(135deg, #F44336, #C62828);
}

.status-icon {
  font-size: 24px;
  color: #FFFFFF;
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.status-desc {
  font-size: 14px;
  color: #666;
}

.status-timeline {
  padding-top: 16px;
  padding-left: 8px;
}

.timeline-item {
  position: relative;
  padding-left: 24px;
  margin-bottom: 16px;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 6px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ddd;
}

.timeline-line {
  position: absolute;
  left: 6px;
  top: 18px;
  bottom: -16px;
  width: 1px;
  background-color: #ddd;
}

.timeline-item.completed .timeline-dot {
  background-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.timeline-item.current .timeline-dot {
  background-color: #1677FF;
  box-shadow: 0 0 0 3px rgba(22, 119, 255, 0.2);
}

.timeline-content {
  padding-bottom: 8px;
}

.timeline-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  display: block;
}

.timeline-time {
  font-size: 12px;
  color: #999;
}

.info-card {
  background: #fff;
  border-radius: 16px;
  margin: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  height: 16px;
  width: 3px;
  background: linear-gradient(to bottom, #1677FF, #065DD2);
  border-radius: 3px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
  text-align: right;
}

.payment-paid {
  color: #4CAF50;
}

.payment-unpaid {
  color: #FF9800;
}

.customer-profile {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.customer-avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-right: 12px;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.customer-id {
  font-size: 13px;
  color: #999;
}

.contact-btn {
  padding: 8px 16px;
  background: #1677FF;
  color: #fff;
  border-radius: 20px;
  font-size: 14px;
  box-shadow: 0 2px 6px rgba(22, 119, 255, 0.2);
}

.address-info {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 16px;
}

.address-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.address-icon {
  margin-right: 6px;
}

.address-content {
  padding-left: 26px;
}

.address-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 6px;
  display: block;
}

.address-detail {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.product-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.product-item:first-child {
  padding-top: 0;
}

.product-image {
  width: 70px;
  height: 70px;
  border-radius: 12px;
  margin-right: 12px;
  background-color: #f0f0f0;
  object-fit: cover;
}

.product-info {
  flex: 1;
  position: relative;
}

.product-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
  line-height: 1.4;
}

.product-spec {
  font-size: 13px;
  color: #999;
  margin-bottom: 8px;
  display: block;
}

.product-price-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 15px;
  color: #FF6A00;
  font-weight: 600;
}

.product-quantity {
  font-size: 13px;
  color: #999;
}

.amount-info {
  padding-top: 16px;
}

.amount-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.amount-label {
  font-size: 14px;
  color: #666;
}

.amount-value {
  font-size: 14px;
  color: #333;
}

.amount-item.discount .amount-value {
  color: #F44336;
}

.amount-item.total {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #eee;
}

.amount-item.total .amount-label {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.amount-item.total .amount-value {
  font-size: 18px;
  font-weight: 600;
  color: #FF6A00;
}

.remark-content {
  font-size: 14px;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 16px;
  line-height: 1.6;
}

.log-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.log-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
  display: block;
}

.log-content {
  font-size: 14px;
  color: #333;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  display: flex;
  padding: 12px 16px;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);
  z-index: 100;
  border-radius: 0;
  padding-bottom: env(safe-area-inset-bottom);
}

.action-btn {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border-radius: 4px;
  font-size: 15px;
  color: #333;
  margin: 0 6px;
  font-weight: 500;
}

.action-btn.primary {
  background-color: #1677FF;
  color: #fff;
}
</style> 