{"version": 3, "file": "partner-fans.js", "sources": ["subPackages/partner/pages/partner-fans.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGFydG5lclxwYWdlc1xwYXJ0bmVyLWZhbnMudnVl"], "sourcesContent": ["<template>\n  <view class=\"fans-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">我的粉丝</view>\n      <view class=\"navbar-right\">\n        <!-- 预留位置与发布页面保持一致 -->\n      </view>\n    </view>\n    \n    <!-- 添加顶部安全区域 -->\n    <view class=\"safe-area-top\"></view>\n\n    <!-- 数据统计卡片 -->\n    <view class=\"stats-card\">\n      <view class=\"stats-item\">\n        <text class=\"stats-num\">{{totalFans}}</text>\n        <text class=\"stats-label\">粉丝总数</text>\n      </view>\n      <view class=\"stats-divider\"></view>\n      <view class=\"stats-item\">\n        <text class=\"stats-num\">{{newFans}}</text>\n        <text class=\"stats-label\">今日新增</text>\n      </view>\n      <view class=\"stats-divider\"></view>\n      <view class=\"stats-item\">\n        <text class=\"stats-num\">{{activeFans}}</text>\n        <text class=\"stats-label\">活跃粉丝</text>\n      </view>\n    </view>\n\n    <!-- 筛选区域 -->\n    <view class=\"filter-section\">\n      <view class=\"filter-tabs\">\n        <view \n          class=\"tab-item\" \n          :class=\"{'active': currentTab === 'all'}\"\n          @tap=\"switchTab('all')\"\n        >全部粉丝</view>\n        <view \n          class=\"tab-item\" \n          :class=\"{'active': currentTab === 'new'}\"\n          @tap=\"switchTab('new')\"\n        >新增粉丝</view>\n        <view \n          class=\"tab-item\" \n          :class=\"{'active': currentTab === 'active'}\"\n          @tap=\"switchTab('active')\"\n        >活跃粉丝</view>\n        <view \n          class=\"tab-item\" \n          :class=\"{'active': currentTab === 'valuable'}\"\n          @tap=\"switchTab('valuable')\"\n        >消费粉丝</view>\n      </view>\n      <view class=\"search-box\">\n        <text class=\"cuIcon-search\"></text>\n        <input type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索粉丝昵称\" @confirm=\"searchFans\" />\n        <text class=\"cuIcon-close\" v-if=\"searchKeyword\" @tap=\"clearSearch\"></text>\n      </view>\n    </view>\n\n    <!-- 粉丝列表 -->\n    <view class=\"fans-list\" v-if=\"filteredFans.length > 0\">\n      <view class=\"fans-item\" v-for=\"(item, index) in filteredFans\" :key=\"index\">\n        <view class=\"fans-avatar\">\n          <image :src=\"item.avatar\" mode=\"aspectFill\"></image>\n          <view class=\"fans-badge\" v-if=\"item.isVip\">VIP</view>\n        </view>\n        <view class=\"fans-info\">\n          <view class=\"fans-name-row\">\n            <text class=\"fans-name\">{{item.nickname}}</text>\n            <text class=\"fans-tag\" v-if=\"isToday(item.joinTime)\">今日新增</text>\n          </view>\n          <view class=\"fans-data\">\n            <text class=\"join-time\">加入时间: {{formatDate(item.joinTime)}}</text>\n            <text class=\"contribution\">贡献: ¥{{item.contribution}}</text>\n          </view>\n        </view>\n        <view class=\"fans-action\">\n          <button class=\"message-btn\" @tap=\"sendMessage(item)\">私信</button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 空状态 -->\n    <view class=\"empty-state\" v-else>\n      <image src=\"/static/images/no-fans.png\" mode=\"aspectFit\"></image>\n      <text>{{emptyText}}</text>\n      <button class=\"share-btn\" @tap=\"shareToFriends\">去邀请好友</button>\n    </view>\n\n    <!-- 加载更多 -->\n    <view class=\"load-more\" v-if=\"filteredFans.length > 0 && hasMore\">\n      <text @tap=\"loadMore\" v-if=\"!isLoading\">加载更多</text>\n      <text v-else>加载中...</text>\n    </view>\n\n    <view class=\"load-end\" v-if=\"filteredFans.length > 0 && !hasMore\">\n      <text>- 已经到底了 -</text>\n    </view>\n\n    <!-- 分享弹窗 -->\n    <view class=\"share-modal\" v-if=\"showShareModal\">\n      <view class=\"modal-mask\" @tap=\"closeShareModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-title\">分享到</view>\n        <view class=\"share-options\">\n          <view class=\"share-option\" @tap=\"shareToWechat\">\n            <image src=\"/static/images/wechat.png\" mode=\"aspectFit\"></image>\n            <text>微信好友</text>\n          </view>\n          <view class=\"share-option\" @tap=\"shareToMoments\">\n            <image src=\"/static/images/moments.png\" mode=\"aspectFit\"></image>\n            <text>朋友圈</text>\n          </view>\n          <view class=\"share-option\" @tap=\"navigateToPoster\">\n            <image src=\"/static/images/poster.png\" mode=\"aspectFit\"></image>\n            <text>推广海报</text>\n          </view>\n          <view class=\"share-option\" @tap=\"copyLink\">\n            <image src=\"/static/images/copy-link.png\" mode=\"aspectFit\"></image>\n            <text>复制链接</text>\n          </view>\n        </view>\n        <button class=\"cancel-btn\" @tap=\"closeShareModal\">取消</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// Vue 3 Composition API 代码开始\n// 数据部分\nconst totalFans = ref(128); // 粉丝总数\nconst newFans = ref(5); // 今日新增\nconst activeFans = ref(42); // 活跃粉丝\nconst currentTab = ref('all'); // 当前选中的标签\nconst searchKeyword = ref(''); // 搜索关键词\nconst fansList = ref([]); // 粉丝列表\nconst page = ref(1); // 当前页码\nconst pageSize = ref(10); // 每页条数\nconst hasMore = ref(true); // 是否有更多数据\nconst isLoading = ref(false); // 是否正在加载\nconst showShareModal = ref(false); // 是否显示分享弹窗\n\n// 计算属性\n// 过滤后的粉丝列表\nconst filteredFans = computed(() => {\n  let result = [...fansList.value];\n  \n  // 按标签筛选\n  if (currentTab.value === 'new') {\n    result = result.filter(item => isToday(item.joinTime));\n  } else if (currentTab.value === 'active') {\n    result = result.filter(item => item.activeLevel >= 3);\n  } else if (currentTab.value === 'valuable') {\n    result = result.filter(item => item.contribution > 0);\n  }\n  \n  // 搜索筛选\n  if (searchKeyword.value) {\n    const keyword = searchKeyword.value.toLowerCase();\n    result = result.filter(item => \n      item.nickname.toLowerCase().includes(keyword)\n    );\n  }\n  \n  return result;\n});\n\n// 空状态文本\nconst emptyText = computed(() => {\n  if (searchKeyword.value) {\n    return '没有找到相关粉丝';\n  } else if (currentTab.value === 'new') {\n    return '今日暂无新增粉丝';\n  } else if (currentTab.value === 'active') {\n    return '暂无活跃粉丝';\n  } else if (currentTab.value === 'valuable') {\n    return '暂无消费粉丝';\n  } else {\n    return '暂无粉丝，去邀请好友吧';\n  }\n});\n\n// 生命周期钩子\nonMounted(() => {\n  // 初始化加载数据\n  loadData();\n});\n\n// 方法\n// 切换标签\nconst switchTab = (tab) => {\n  currentTab.value = tab;\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 搜索粉丝\nconst searchFans = () => {\n  // 重置页码，重新加载\n  page.value = 1;\n  hasMore.value = true;\n  loadData();\n};\n\n// 清除搜索\nconst clearSearch = () => {\n  searchKeyword.value = '';\n  searchFans();\n};\n\n// 加载数据\nconst loadData = () => {\n  isLoading.value = true;\n  \n  // 模拟API请求\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = generateMockFans();\n    \n    if (page.value === 1) {\n      fansList.value = mockData;\n    } else {\n      fansList.value = [...fansList.value, ...mockData];\n    }\n    \n    // 判断是否还有更多数据\n    hasMore.value = page.value < 3; // 模拟只有3页数据\n    \n    isLoading.value = false;\n  }, 500);\n};\n\n// 加载更多\nconst loadMore = () => {\n  if (isLoading.value || !hasMore.value) return;\n  \n  page.value++;\n  loadData();\n};\n\n// 发送私信\nconst sendMessage = (fan) => {\n  uni.navigateTo({\n    url: `/pages/message/chat?userId=${fan.id}&nickname=${fan.nickname}`\n  });\n};\n\n// 分享给朋友\nconst shareToFriends = () => {\n  showShareModal.value = true;\n};\n\n// 关闭分享弹窗\nconst closeShareModal = () => {\n  showShareModal.value = false;\n};\n\n// 分享到微信\nconst shareToWechat = () => {\n  uni.showToast({\n    title: '已分享到微信',\n    icon: 'success'\n  });\n  closeShareModal();\n};\n\n// 分享到朋友圈\nconst shareToMoments = () => {\n  uni.showToast({\n    title: '已分享到朋友圈',\n    icon: 'success'\n  });\n  closeShareModal();\n};\n\n// 跳转到海报页面\nconst navigateToPoster = () => {\n  uni.navigateTo({\n    url: '/pages/my/partner-poster'\n  });\n  closeShareModal();\n};\n\n// 复制链接\nconst copyLink = () => {\n  uni.setClipboardData({\n    data: 'https://example.com/register?ref=' + Math.random().toString(36).slice(2),\n    success: () => {\n      uni.showToast({\n        title: '链接已复制',\n        icon: 'success'\n      });\n      closeShareModal();\n    }\n  });\n};\n\n// 格式化日期\nconst formatDate = (dateStr) => {\n  const date = new Date(dateStr);\n  const year = date.getFullYear();\n  const month = (date.getMonth() + 1).toString().padStart(2, '0');\n  const day = date.getDate().toString().padStart(2, '0');\n  \n  return `${year}-${month}-${day}`;\n};\n\n// 判断是否为今天\nconst isToday = (dateStr) => {\n  const today = new Date();\n  const date = new Date(dateStr);\n  \n  return today.getFullYear() === date.getFullYear() \n    && today.getMonth() === date.getMonth() \n    && today.getDate() === date.getDate();\n};\n\n// 生成模拟粉丝数据\nconst generateMockFans = () => {\n  const mockFans = [];\n  const nicknames = ['奔跑的小猪', '小小科技迷', '梦想家', '爱笑的眼睛', '春风十里', '南方姑娘', '山川湖海', '时光漫步'];\n  const avatars = [\n    '/static/images/avatar-1.png',\n    '/static/images/avatar-2.png',\n    '/static/images/avatar-3.png',\n    '/static/images/avatar-4.png',\n    '/static/images/avatar-5.png',\n    '/static/images/avatar-6.png'\n  ];\n  \n  // 随机生成10条记录\n  for (let i = 0; i < 10; i++) {\n    const now = new Date();\n    // 随机日期，最近30天内\n    const randomDays = Math.floor(Math.random() * 30);\n    const joinTime = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);\n    \n    // 随机活跃度 1-5\n    const activeLevel = Math.floor(Math.random() * 5) + 1;\n    \n    // 随机贡献值\n    const contribution = randomDays === 0 ? 0 : Math.floor(Math.random() * 1000);\n    \n    // 随机VIP状态\n    const isVip = Math.random() > 0.7;\n    \n    mockFans.push({\n      id: 'user_' + Date.now() + i,\n      nickname: nicknames[Math.floor(Math.random() * nicknames.length)],\n      avatar: avatars[Math.floor(Math.random() * avatars.length)],\n      joinTime,\n      activeLevel,\n      contribution,\n      isVip\n    });\n  }\n  \n  return mockFans;\n};\n// Vue 3 Composition API 代码结束\n</script>\n\n<style lang=\"scss\" scoped>\n.fans-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 30rpx;\n  padding-top: calc(44px + var(--status-bar-height));\n}\n\n.stats-card {\n  margin: 30rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.stats-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stats-num {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 10rpx;\n}\n\n.stats-label {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.stats-divider {\n  width: 1px;\n  height: 60rpx;\n  background-color: #eeeeee;\n}\n\n.filter-section {\n  background-color: #ffffff;\n  padding: 20rpx 30rpx;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n  margin-bottom: 20rpx;\n}\n\n.filter-tabs {\n  display: flex;\n  overflow-x: auto;\n  margin-bottom: 20rpx;\n  \n  &::-webkit-scrollbar {\n    display: none;\n  }\n}\n\n.tab-item {\n  padding: 15rpx 30rpx;\n  font-size: 28rpx;\n  color: #666666;\n  position: relative;\n  white-space: nowrap;\n  \n  &.active {\n    color: #1677FF;\n    font-weight: 500;\n    \n    &::after {\n      content: '';\n      position: absolute;\n      left: 50%;\n      bottom: 0;\n      transform: translateX(-50%);\n      width: 40rpx;\n      height: 4rpx;\n      background-color: #1677FF;\n      border-radius: 2rpx;\n    }\n  }\n}\n\n.search-box {\n  height: 70rpx;\n  background-color: #f5f5f5;\n  border-radius: 35rpx;\n  display: flex;\n  align-items: center;\n  padding: 0 20rpx;\n  \n  .cuIcon-search {\n    font-size: 32rpx;\n    color: #999999;\n    margin-right: 10rpx;\n  }\n  \n  input {\n    flex: 1;\n    height: 100%;\n    font-size: 28rpx;\n  }\n  \n  .cuIcon-close {\n    font-size: 32rpx;\n    color: #999999;\n    padding: 10rpx;\n  }\n}\n\n.fans-list {\n  padding: 0 30rpx;\n}\n\n.fans-item {\n  display: flex;\n  align-items: center;\n  padding: 30rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.fans-avatar {\n  position: relative;\n  margin-right: 20rpx;\n  \n  image {\n    width: 100rpx;\n    height: 100rpx;\n    border-radius: 50%;\n  }\n  \n  .fans-badge {\n    position: absolute;\n    bottom: 0;\n    right: 0;\n    background: linear-gradient(135deg, #FF9500, #FF6000);\n    color: #ffffff;\n    font-size: 20rpx;\n    padding: 4rpx 8rpx;\n    border-radius: 10rpx;\n    transform: scale(0.8);\n    transform-origin: right bottom;\n  }\n}\n\n.fans-info {\n  flex: 1;\n}\n\n.fans-name-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.fans-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333333;\n  margin-right: 10rpx;\n}\n\n.fans-tag {\n  background-color: #e6f4ff;\n  color: #1677FF;\n  font-size: 20rpx;\n  padding: 4rpx 10rpx;\n  border-radius: 8rpx;\n}\n\n.fans-data {\n  font-size: 24rpx;\n  color: #999999;\n  \n  .join-time {\n    margin-right: 20rpx;\n  }\n  \n  .contribution {\n    color: #ff6000;\n  }\n}\n\n.fans-action {\n  .message-btn {\n    background: #f0f7ff;\n    color: #1677FF;\n    font-size: 26rpx;\n    min-width: 120rpx;\n    height: 60rpx;\n    line-height: 60rpx;\n    padding: 0 20rpx;\n    border-radius: 30rpx;\n    border: 1px solid #1677FF;\n  }\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 100rpx 0;\n  \n  image {\n    width: 240rpx;\n    height: 240rpx;\n    margin-bottom: 30rpx;\n  }\n  \n  text {\n    font-size: 28rpx;\n    color: #999999;\n    margin-bottom: 40rpx;\n  }\n  \n  .share-btn {\n    width: 300rpx;\n    height: 80rpx;\n    line-height: 80rpx;\n    background: linear-gradient(135deg, #1677FF, #0E5FD8);\n    color: #ffffff;\n    font-size: 28rpx;\n    border-radius: 40rpx;\n  }\n}\n\n.load-more, .load-end {\n  text-align: center;\n  font-size: 26rpx;\n  color: #999999;\n  padding: 30rpx 0;\n}\n\n.share-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 999;\n  display: flex;\n  justify-content: center;\n  align-items: flex-end;\n  \n  .modal-mask {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.6);\n  }\n  \n  .modal-content {\n    width: 100%;\n    background-color: #ffffff;\n    border-radius: 20rpx 20rpx 0 0;\n    padding-bottom: env(safe-area-inset-bottom);\n    position: relative;\n    z-index: 1000;\n  }\n  \n  .modal-title {\n    text-align: center;\n    font-size: 30rpx;\n    font-weight: 500;\n    color: #333333;\n    padding: 30rpx 0;\n    border-bottom: 1px solid #f0f0f0;\n  }\n  \n  .share-options {\n    display: flex;\n    flex-wrap: wrap;\n    padding: 30rpx;\n  }\n  \n  .share-option {\n    width: 25%;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    padding: 20rpx 0;\n    \n    image {\n      width: 100rpx;\n      height: 100rpx;\n      margin-bottom: 15rpx;\n    }\n    \n    text {\n      font-size: 26rpx;\n      color: #666666;\n    }\n  }\n  \n  .cancel-btn {\n    width: 100%;\n    height: 100rpx;\n    line-height: 100rpx;\n    text-align: center;\n    font-size: 32rpx;\n    color: #333333;\n    border-top: 10rpx solid #f5f5f5;\n  }\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 88rpx;\n  padding: 0 30rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-title {\n  position: absolute;\n  left: 0;\n  right: 0;\n  color: #ffffff;\n  font-size: 36rpx;\n  font-weight: 700;\n  font-family: 'AlimamaShuHeiTi', sans-serif;\n  text-align: center;\n}\n\n.navbar-right {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.navbar-left {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  z-index: 20; /* 确保在标题上层，可以被点击 */\n}\n\n.back-icon {\n  width: 100%;\n  height: 100%;\n}\n\n.safe-area-top {\n  height: var(--status-bar-height);\n  width: 100%;\n  background-image: linear-gradient(135deg, #0066FF, #0052CC);\n}\n</style> \n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/partner/pages/partner-fans.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni"], "mappings": ";;;;;;AA2IA,UAAM,YAAYA,cAAAA,IAAI,GAAG;AACzB,UAAM,UAAUA,cAAAA,IAAI,CAAC;AACrB,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,WAAWA,cAAAA,IAAI,CAAA,CAAE;AACvB,UAAM,OAAOA,cAAAA,IAAI,CAAC;AACDA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAIhC,UAAM,eAAeC,cAAQ,SAAC,MAAM;AAClC,UAAI,SAAS,CAAC,GAAG,SAAS,KAAK;AAG/B,UAAI,WAAW,UAAU,OAAO;AAC9B,iBAAS,OAAO,OAAO,UAAQ,QAAQ,KAAK,QAAQ,CAAC;AAAA,MACzD,WAAa,WAAW,UAAU,UAAU;AACxC,iBAAS,OAAO,OAAO,UAAQ,KAAK,eAAe,CAAC;AAAA,MACxD,WAAa,WAAW,UAAU,YAAY;AAC1C,iBAAS,OAAO,OAAO,UAAQ,KAAK,eAAe,CAAC;AAAA,MACrD;AAGD,UAAI,cAAc,OAAO;AACvB,cAAM,UAAU,cAAc,MAAM,YAAW;AAC/C,iBAAS,OAAO;AAAA,UAAO,UACrB,KAAK,SAAS,cAAc,SAAS,OAAO;AAAA,QAClD;AAAA,MACG;AAED,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,YAAYA,cAAQ,SAAC,MAAM;AAC/B,UAAI,cAAc,OAAO;AACvB,eAAO;AAAA,MACX,WAAa,WAAW,UAAU,OAAO;AACrC,eAAO;AAAA,MACX,WAAa,WAAW,UAAU,UAAU;AACxC,eAAO;AAAA,MACX,WAAa,WAAW,UAAU,YAAY;AAC1C,eAAO;AAAA,MACX,OAAS;AACL,eAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd;IACF,CAAC;AAID,UAAM,YAAY,CAAC,QAAQ;AACzB,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,aAAa,MAAM;AAEvB,WAAK,QAAQ;AACb,cAAQ,QAAQ;AAChB;IACF;AAGA,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AACtB;IACF;AAGA,UAAM,WAAW,MAAM;AACrB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAEf,cAAM,WAAW;AAEjB,YAAI,KAAK,UAAU,GAAG;AACpB,mBAAS,QAAQ;AAAA,QACvB,OAAW;AACL,mBAAS,QAAQ,CAAC,GAAG,SAAS,OAAO,GAAG,QAAQ;AAAA,QACjD;AAGD,gBAAQ,QAAQ,KAAK,QAAQ;AAE7B,kBAAU,QAAQ;AAAA,MACnB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,UAAU,SAAS,CAAC,QAAQ;AAAO;AAEvC,WAAK;AACL;IACF;AAGA,UAAM,cAAc,CAAC,QAAQ;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,8BAA8B,IAAI,EAAE,aAAa,IAAI,QAAQ;AAAA,MACtE,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AACD;IACF;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AACD;IACF;AAGA,UAAM,mBAAmB,MAAM;AAC7BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AACD;IACF;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,sCAAsC,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,MAAM,CAAC;AAAA,QAC9E,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AACD;QACD;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,YAAY;AAC9B,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAErD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC;AAGA,UAAM,UAAU,CAAC,YAAY;AAC3B,YAAM,QAAQ,oBAAI;AAClB,YAAM,OAAO,IAAI,KAAK,OAAO;AAE7B,aAAO,MAAM,kBAAkB,KAAK,YAAa,KAC5C,MAAM,SAAQ,MAAO,KAAK,SAAU,KACpC,MAAM,QAAO,MAAO,KAAK,QAAO;AAAA,IACvC;AAGA,UAAM,mBAAmB,MAAM;AAC7B,YAAM,WAAW,CAAA;AACjB,YAAM,YAAY,CAAC,SAAS,SAAS,OAAO,SAAS,QAAQ,QAAQ,QAAQ,MAAM;AACnF,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAGE,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAM,MAAM,oBAAI;AAEhB,cAAM,aAAa,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE;AAChD,cAAM,WAAW,IAAI,KAAK,IAAI,QAAO,IAAK,aAAa,KAAK,KAAK,KAAK,GAAI;AAG1E,cAAM,cAAc,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI;AAGpD,cAAM,eAAe,eAAe,IAAI,IAAI,KAAK,MAAM,KAAK,WAAW,GAAI;AAG3E,cAAM,QAAQ,KAAK,OAAM,IAAK;AAE9B,iBAAS,KAAK;AAAA,UACZ,IAAI,UAAU,KAAK,IAAK,IAAG;AAAA,UAC3B,UAAU,UAAU,KAAK,MAAM,KAAK,OAAQ,IAAG,UAAU,MAAM,CAAC;AAAA,UAChE,QAAQ,QAAQ,KAAK,MAAM,KAAK,OAAQ,IAAG,QAAQ,MAAM,CAAC;AAAA,UAC1D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACN,CAAK;AAAA,MACF;AAED,aAAO;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjXA,GAAG,WAAW,eAAe;"}