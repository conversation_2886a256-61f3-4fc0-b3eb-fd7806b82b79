/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-004fbe38, html.data-v-004fbe38, #app.data-v-004fbe38, .index-container.data-v-004fbe38 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 页面容器 */
.flash-create-container.data-v-004fbe38 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar.data-v-004fbe38 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}
.navbar-left.data-v-004fbe38 {
  width: 40px;
}
.back-button.data-v-004fbe38 {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title.data-v-004fbe38 {
  flex: 1;
  text-align: center;
}
.title-text.data-v-004fbe38 {
  font-size: 18px;
  font-weight: 600;
}
.navbar-right.data-v-004fbe38 {
  min-width: 40px;
}
.save-button.data-v-004fbe38 {
  font-size: 16px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

/* 内容区域 */
.content-area.data-v-004fbe38 {
  flex: 1;
  box-sizing: border-box;
  height: calc(100vh - 80px - 60px);
}

/* 表单区域 */
.form-section.data-v-004fbe38 {
  padding: 12px;
}
.form-group.data-v-004fbe38 {
  background: #FFFFFF;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}
.form-header.data-v-004fbe38 {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  border-bottom: 1px solid #F5F5F5;
}
.form-item.data-v-004fbe38 {
  padding: 12px 16px;
  border-bottom: 1px solid #F5F5F5;
  position: relative;
}
.form-item.data-v-004fbe38:last-child {
  border-bottom: none;
}
.form-label.data-v-004fbe38 {
  display: block;
  font-size: 14px;
  color: #333333;
  margin-bottom: 8px;
}
.required.data-v-004fbe38:after {
  content: "*";
  color: #FF3B30;
  margin-left: 4px;
}
.form-input.data-v-004fbe38 {
  width: 100%;
  height: 40px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
}
.form-textarea.data-v-004fbe38 {
  width: 100%;
  height: 100px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333333;
}
.form-counter.data-v-004fbe38 {
  position: absolute;
  right: 16px;
  bottom: 12px;
  font-size: 12px;
  color: #999999;
}
.form-tip.data-v-004fbe38 {
  font-size: 12px;
  color: #999999;
  margin-top: 8px;
  display: block;
}
.form-error.data-v-004fbe38 {
  font-size: 12px;
  color: #FF3B30;
  margin-top: 8px;
  display: block;
}
.discount-value.data-v-004fbe38 {
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  color: #FF3B30;
  font-weight: 600;
}
.picker-value.data-v-004fbe38 {
  width: 100%;
  height: 40px;
  background: #F9F9F9;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.arrow-icon.data-v-004fbe38 {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999999;
}

/* 图片上传 */
.image-uploader.data-v-004fbe38 {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4px;
}
.image-item.data-v-004fbe38 {
  width: calc(25% - 8px);
  margin: 4px;
  position: relative;
  aspect-ratio: 1;
}
.detail-image-item.data-v-004fbe38 {
  width: calc(33.33% - 8px);
}
.uploaded-image.data-v-004fbe38 {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
.delete-icon.data-v-004fbe38 {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  z-index: 10;
}
.upload-button.data-v-004fbe38 {
  width: calc(25% - 8px);
  margin: 4px;
  aspect-ratio: 1;
  background: #F9F9F9;
  border: 1px dashed #DDDDDD;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
}
.detail-upload.data-v-004fbe38 {
  width: calc(33.33% - 8px);
}

/* 活动提示 */
.tips-list.data-v-004fbe38 {
  padding: 0 16px;
}
.tip-item.data-v-004fbe38 {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}
.tip-item.data-v-004fbe38:last-child {
  border-bottom: none;
}
.tip-icon.data-v-004fbe38 {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  color: #FF7600;
}
.tip-content.data-v-004fbe38 {
  flex: 1;
}
.tip-title.data-v-004fbe38 {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}
.tip-desc.data-v-004fbe38 {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

/* 活动推广样式 */
.form-group .marketing-promotion-container.data-v-004fbe38 {
  margin-top: 10px;
}

/* 底部空间 */
.bottom-space.data-v-004fbe38 {
  height: 80px;
}

/* 底部操作栏 */
.bottom-bar.data-v-004fbe38 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 10px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
  display: flex;
}
.action-button.data-v-004fbe38 {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 6px;
}
.action-button.preview.data-v-004fbe38 {
  background: #F5F5F5;
  color: #666666;
}
.action-button.save.data-v-004fbe38 {
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
}
.button-text.data-v-004fbe38 {
  font-size: 16px;
  font-weight: 500;
}
@media screen and (min-width: 768px) {
.form-section.data-v-004fbe38 {
    max-width: 600px;
    margin: 0 auto;
}
.bottom-bar.data-v-004fbe38 {
    max-width: 600px;
    left: 50%;
    transform: translateX(-50%);
}
}