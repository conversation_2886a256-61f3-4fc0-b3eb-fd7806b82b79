{"version": 3, "file": "index.js", "sources": ["carpool-package/pages/carpool-main/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2wtbWFpblxpbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"carpool-container\">\n    <!-- 自定义标题栏模块 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">磁州拼车</text>\n        </view>\n        <view class=\"right-action\">\n          <image src=\"/static/images/tabbar/更多.png\" class=\"action-icon more-icon\"></image>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 圆弧背景模块 -->\n    <view class=\"arc-background\"></view>\n    \n    <!-- 背景装饰 -->\n    <view class=\"bg-decoration bg-circle-1\"></view>\n    <view class=\"bg-decoration bg-circle-2\"></view>\n    <view class=\"bg-decoration bg-circle-3\"></view>\n    \n    <!-- 轮播图 -->\n    <view class=\"swiper-section\">\n      <swiper class=\"swiper\" indicator-dots autoplay interval=\"3000\" duration=\"500\" circular>\n        <swiper-item v-for=\"(item, index) in banners\" :key=\"index\" @click=\"handleBannerClick(item)\">\n          <image :src=\"item.image\" mode=\"aspectFill\"></image>\n        </swiper-item>\n      </swiper>\n    </view>\n    \n    <!-- 搜索框 -->\n    <view class=\"search-section\">\n      <view class=\"search-box\">\n        <view class=\"search-input-wrapper\">\n          <input type=\"text\" class=\"search-input\" placeholder=\"请输入出发地\" v-model=\"startPoint\" />\n          <view class=\"search-exchange\">\n            <text class=\"exchange-icon\">⇄</text>\n      </view>\n          <input type=\"text\" class=\"search-input\" placeholder=\"请输入目的地\" v-model=\"endPoint\" />\n      </view>\n        <view class=\"search-button\" @click=\"searchRoutes\">\n          <text>搜索</text>\n      </view>\n      </view>\n    </view>\n    \n    <!-- 四宫格卡片 -->\n    <view class=\"grid-section\">\n      <view class=\"grid-row\">\n        <view class=\"grid-item people-to-car\" @click=\"navigateTo('people-to-car')\">\n          <view class=\"grid-content\">\n            <text class=\"grid-title\">人找车</text>\n            <text class=\"grid-subtitle\">快速出行</text>\n          </view>\n        </view>\n        <view class=\"grid-item car-to-people\" @click=\"navigateTo('car-to-people')\">\n          <view class=\"grid-content\">\n            <text class=\"grid-title\">车找人</text>\n            <text class=\"grid-subtitle\">拼车省钱</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"grid-row\">\n        <view class=\"grid-item goods-to-car\" @click=\"navigateTo('goods-to-car')\">\n          <view class=\"grid-content\">\n            <text class=\"grid-title\">货找车</text>\n            <text class=\"grid-subtitle\">安全送达</text>\n          </view>\n        </view>\n        <view class=\"grid-item car-to-goods\" @click=\"navigateTo('car-to-goods')\">\n          <view class=\"grid-content\">\n            <text class=\"grid-title\">车找货</text>\n            <text class=\"grid-subtitle\">满载而归</text>\n      </view>\n      </view>\n      </view>\n    </view>\n    \n    <!-- 热门路线 -->\n    <view class=\"section hot-routes\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">热门路线</text>\n        <text class=\"more-link\" @click=\"viewMoreRoutes\">更多</text>\n      </view>\n      <view class=\"route-list\">\n        <view class=\"route-item\" v-for=\"(route, index) in hotRoutes\" :key=\"index\" @click=\"selectRoute(route)\">\n          <view class=\"route-path\">\n            <text class=\"start-point\">{{route.start}}</text>\n            <view class=\"route-arrow-container\"></view>\n            <text class=\"end-point\">{{route.end}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 最新拼车信息 -->\n    <view class=\"section latest-carpools\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">最新拼车</text>\n        <view class=\"sort-dropdown\">\n          <view class=\"sort-label\" @click=\"toggleSortOptions\">\n            <text>{{sortTypeText}}</text>\n            <text class=\"sort-icon\">▼</text>\n          </view>\n          <view class=\"sort-options\" v-if=\"showSortOptions\">\n            <view class=\"sort-option\" @click=\"changeSortType('default')\">\n              <text>默认排序</text>\n            </view>\n            <view class=\"sort-option\" @click=\"changeSortType('departureTime')\">\n              <text>出发时间</text>\n            </view>\n            <view class=\"sort-option\" @click=\"changeSortType('publishTime')\">\n              <text>发布时间</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"carpool-list\">\n        <view class=\"carpool-card\" v-for=\"(item, index) in sortedCarpoolList\" :key=\"index\" @click=\"viewDetail(item)\" :class=\"item.type\">\n          <!-- 优化后的拼车卡片布局 -->\n          <view class=\"card-header\">\n            <view class=\"card-type-indicator\"></view>\n            <view class=\"route-direction\">\n              <view class=\"route-info-row\">\n                <text class=\"route-label\">出发:</text>\n                <text class=\"route-value\">{{item.startPoint}}</text>\n            </view>\n              <view class=\"route-info-row via-points\" v-if=\"item.viaPoints && item.viaPoints.length > 0\">\n                <text class=\"route-label\">途经:</text>\n                <view class=\"route-value-wrapper\">\n                  <text class=\"via-icon\">⟡</text>\n                  <text class=\"route-value\">{{item.viaPoints.join(' → ')}}</text>\n          </view>\n            </view>\n              <view class=\"route-info-row\">\n                <text class=\"route-label\">到达:</text>\n                <text class=\"route-value\">{{item.endPoint}}</text>\n          </view>\n            </view>\n            <!-- 类型标签 -->\n            <view class=\"card-type-tag\" :class=\"item.type + '-tag'\">\n              <text>{{item.typeText}}</text>\n            </view>\n            <!-- 发布方式标签 -->\n            <view class=\"publish-mode-tag\" :class=\"item.publishMode\">\n              <text>{{item.publishMode === 'premium' ? '置顶' : '广告'}}</text>\n            </view>\n          </view>\n          \n          <view class=\"card-footer\">\n            <view class=\"trip-info\">\n            <view class=\"info-item\">\n                <image src=\"/static/images/tabbar/时间.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\n                <text class=\"info-value\">{{item.departureTime}}</text>\n            </view>\n              \n            <view class=\"info-item\" v-if=\"item.price\">\n                <image src=\"/static/images/tabbar/价格.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\n                <text class=\"info-value\">{{item.price}}元</text>\n            </view>\n              \n            <view class=\"info-item\">\n                <image src=\"/static/images/tabbar/座位.png\" mode=\"aspectFit\" class=\"info-icon\"></image>\n                <text class=\"info-value\">{{item.seats}}座</text>\n              </view>\n            </view>\n            \n            <view class=\"user-actions\">\n              <view class=\"user-info\">\n                <image class=\"user-avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n                <view class=\"user-name-wrapper\">\n                  <text class=\"user-name\">{{item.username}}</text>\n                  <view class=\"verified-badge\" v-if=\"item.isVerified\">\n                    <view class=\"verified-icon-circle\">\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z\" fill=\"currentColor\" />\n                      </svg>\n                    </view>\n                    <text class=\"verified-text\">已认证</text>\n                  </view>\n            </view>\n          </view>\n          \n          <view class=\"card-actions\">\n                <button class=\"action-button phone\" @click.stop=\"callPhone(item.phone)\" data-label=\"拨打电话\">\n                  <image src=\"/static/images/tabbar/电话.png\" mode=\"aspectFit\"></image>\n            </button>\n                <button class=\"action-button chat\" @click.stop=\"chatWith(item.userId)\" data-label=\"发送私信\">\n                  <image src=\"/static/images/tabbar/消息.png\" mode=\"aspectFit\"></image>\n            </button>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 发布类型选择弹窗 -->\n    <view class=\"publish-popup\" v-if=\"showPublishPopup\" @click=\"closePublishPopup\">\n      <view class=\"publish-card\" @click.stop>\n        <view class=\"publish-header\">\n          <text class=\"publish-title\">选择发布类型</text>\n          <view class=\"close-btn\" @click=\"closePublishPopup\">\n            <text class=\"close-icon\">×</text>\n    </view>\n        </view>\n        <view class=\"publish-options\">\n          <view class=\"publish-option-row\">\n            <view class=\"publish-option\" @click=\"navigateToPublish('people-to-car')\">\n              <view class=\"option-icon-wrapper people-car\">\n                <image src=\"/static/images/tabbar/人找车.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n              </view>\n              <text class=\"option-text\">人找车</text>\n            </view>\n            <view class=\"publish-option\" @click=\"navigateToPublish('car-to-people')\">\n              <view class=\"option-icon-wrapper car-people\">\n                <image src=\"/static/images/tabbar/车找人.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n              </view>\n              <text class=\"option-text\">车找人</text>\n            </view>\n          </view>\n          <view class=\"publish-option-row\">\n            <view class=\"publish-option\" @click=\"navigateToPublish('goods-to-car')\">\n              <view class=\"option-icon-wrapper goods-car\">\n                <image src=\"/static/images/tabbar/货找车.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n              </view>\n              <text class=\"option-text\">货找车</text>\n            </view>\n            <view class=\"publish-option\" @click=\"navigateToPublish('car-to-goods')\">\n              <view class=\"option-icon-wrapper car-goods\">\n                <image src=\"/static/images/tabbar/车找货.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n              </view>\n              <text class=\"option-text\">车找货</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部导航栏 -->\n    <view class=\"tabbar\">\n      <view class=\"tabbar-item\" @click=\"navigateToPage('home')\">\n        <image src=\"/static/images/tabbar/p首页.png\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\">同城</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'carpool' }\">\n        <image :src=\"activeTab === 'carpool' ? '/static/images/tabbar/p拼车选中.png' : '/static/images/tabbar/p拼车.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'carpool' }\">拼车</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'publish' }\" @click=\"publishCarpool\">\n        <image :src=\"activeTab === 'publish' ? '/static/images/tabbar/p发布选中.png' : '/static/images/tabbar/p发布.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'publish' }\">发布</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'groups' }\" @click=\"navigateToPage('groups')\">\n        <image :src=\"activeTab === 'groups' ? '/static/images/tabbar/p拼车群选中.png' : '/static/images/tabbar/p拼车群.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'groups' }\">拼车群</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'my' }\" @click=\"navigateToPage('my')\">\n        <image :src=\"activeTab === 'my' ? '/static/images/tabbar/p我的选中.png' : '/static/images/tabbar/p我的.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'my' }\">我的</text>\n      </view>\n    </view>\n    \n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed } from 'vue';\nimport { onLoad, onReady, onShow, onHide, onUnload, onBackPress } from '@dcloudio/uni-app';\n\n// 响应式数据\nconst banners = reactive([\n  { id: 1, image: '/static/images/tabbar/carpool-banner1.jpg', url: '/pages/carpool/activity/discount' },\n  { id: 2, image: '/static/images/tabbar/carpool-banner2.jpg', url: '/pages/carpool/activity/newuser' }\n]);\n\nconst hotRoutes = reactive([\n  { id: 1, start: '磁县', end: '邯郸', count: 128 },\n  { id: 2, start: '邯郸', end: '磁县', count: 112 },\n  { id: 3, start: '邯郸', end: '北京', count: 86 },\n  { id: 4, start: '北京', end: '邯郸', count: 72 },\n  { id: 5, start: '邯郸', end: '石家庄', count: 65 },\n  { id: 6, start: '石家庄', end: '邯郸', count: 53 }\n]);\n\nconst activeTab = ref('carpool');\n\nconst latestCarpools = reactive([\n    {\n      id: 1,\n        type: 'people-to-car',\n        typeText: '人找车',\n        avatar: '/static/images/avatar/user1.png',\n        username: '张先生',\n        isVerified: true,\n        startPoint: '磁州城区-汽车站',\n        endPoint: '邯郸火车站',\n      departureTime: '今天 14:30',\n        seats: 1,\n      phone: '13812345678',\n        userId: 'user123',\n        publishMode: 'premium', // 付费置顶\n        viaPoints: ['磁州东区', '峰峰矿区']\n    },\n    {\n      id: 2,\n        type: 'car-to-people',\n        typeText: '车找人',\n        avatar: '/static/images/avatar/user2.png',\n        username: '李师傅',\n        isVerified: true,\n        startPoint: '邯郸机场',\n        endPoint: '磁州城区-人民医院',\n        departureTime: '今天 18:00',\n        price: 30,\n        seats: 3,\n      phone: '13987654321',\n        userId: 'user456',\n        publishMode: 'ad', // 广告发布\n        viaPoints: ['邯郸东站', '武安市']\n    },\n    {\n      id: 3,\n        type: 'goods-to-car',\n        typeText: '货找车',\n        avatar: '/static/images/avatar/user3.png',\n        username: '王经理',\n        isVerified: false,\n        startPoint: '磁州商贸城',\n        endPoint: '邯郸物流园',\n        departureTime: '明天 09:00',\n        phone: '13765432198',\n        userId: 'user789',\n        publishMode: 'premium', // 付费置顶\n        viaPoints: ['永年区', '丛台区']\n    },\n    {\n        id: 4,\n      type: 'car-to-goods',\n      typeText: '车找货',\n        avatar: '/static/images/avatar/user4.png',\n        username: '赵师傅',\n      isVerified: true,\n        startPoint: '邯郸市区',\n        endPoint: '磁县工业园',\n        departureTime: '明天 10:30',\n        seats: 0,\n        phone: '13598765432',\n        userId: 'user321',\n        publishMode: 'normal', // 普通发布\n        viaPoints: []\n    }\n]);\n\nconst statusBarHeight = ref(0);\nconst showAnimation = ref(false);\nconst startPoint = ref('');\nconst endPoint = ref('');\nconst showPublishPopup = ref(false);\nconst sortedCarpoolList = ref([]);\nconst sortType = ref('default');\nconst showSortOptions = ref(false);\nconst currentType = ref('');\nconst filterStartPoint = ref('');\nconst filterEndPoint = ref('');\n\nconst sortTypeText = computed(() => {\n    switch (sortType.value) {\n        case 'default': return '默认排序';\n        case 'departureTime': return '出发时间';\n        case 'publishTime': return '发布时间';\n        default: return '默认排序';\n    }\n});\n\n// 方法\nconst setStatusBarHeight = () => {\n    try {\n        const systemInfo = uni.getSystemInfoSync();\n        statusBarHeight.value = systemInfo.statusBarHeight || 20;\n\n        if (systemInfo.brand) {\n            const brand = systemInfo.brand.toLowerCase();\n            if (brand.includes('iphone')) {\n                if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.top > 20) {\n                    statusBarHeight.value = Math.max(statusBarHeight.value, systemInfo.safeAreaInsets.top);\n                }\n            }\n        }\n\n        if (typeof document !== 'undefined' && document.documentElement) {\n            document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight.value}px`);\n            document.documentElement.style.setProperty('--status-bar-color', '#1677FF');\n            document.documentElement.style.setProperty('--status-bar-bg-color', '#1677FF');\n        }\n        uni.setStorageSync('statusBarHeight', statusBarHeight.value);\n\n        // 设置状态栏颜色 - 确保是纯蓝色 #1677FF\n        uni.setNavigationBarColor({\n            frontColor: '#ffffff',\n            backgroundColor: '#1677FF',\n            animation: {\n                duration: 0,\n                timingFunc: 'easeIn'\n            }\n        });\n\n        // #ifdef APP-PLUS\n        // 在APP环境下，直接设置状态栏颜色和样式\n        plus.navigator.setStatusBarStyle('light');\n        plus.navigator.setStatusBarBackground('#1677FF');\n        // #endif\n\n        if (systemInfo.platform) {\n            uni.setStorageSync('platform', systemInfo.platform);\n            uni.setStorageSync('isIOS', systemInfo.platform === 'ios');\n        }\n    } catch (e) {\n        console.error('设置状态栏高度出错:', e);\n        statusBarHeight.value = 20;\n        if (typeof document !== 'undefined' && document.documentElement) {\n            document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight.value}px`);\n        }\n        uni.setStorageSync('statusBarHeight', statusBarHeight.value);\n    }\n};\n\nconst handleBannerClick = (banner) => {\n    uni.navigateTo({\n        url: '/carpool-package' + banner.url\n    });\n};\n\nconst exchangePoints = () => {\n    [startPoint.value, endPoint.value] = [endPoint.value, startPoint.value];\n};\n\nconst searchRoutes = () => {\n    if (!startPoint.value || !endPoint.value) {\n        uni.showToast({ title: '请输入出发地和目的地', icon: 'none' });\n        return;\n    }\n  uni.navigateTo({\n        url: `/carpool-package/pages/carpool/list/index?start=${startPoint.value}&end=${endPoint.value}`\n  });\n};\n\nconst getTypeText = (type) => {\n    switch(type) {\n        case 'people-to-car': return '人找车';\n        case 'car-to-people': return '车找人';\n        case 'goods-to-car': return '货找车';\n        case 'car-to-goods': return '车找货';\n        default: return '全部';\n    }\n};\n\nconst navigateTo = (type) => {\n    currentType.value = type;\n    sortCarpoolList();\n    uni.pageScrollTo({ selector: '.latest-carpools', duration: 300 });\n    uni.showToast({ title: `已筛选${getTypeText(type)}信息`, icon: 'none', duration: 1500 });\n};\n\nconst viewMoreRoutes = () => {\n    uni.navigateTo({ url: '/carpool-package/pages/carpool/routes/index' });\n};\n\nconst selectRoute = (route) => {\n    if (!route || !route.start || !route.end) {\n        uni.showToast({ title: '路线数据不完整', icon: 'none' });\n        return;\n    }\n    filterStartPoint.value = route.start;\n    filterEndPoint.value = route.end;\n    sortCarpoolList();\n    uni.pageScrollTo({ selector: '.latest-carpools', duration: 300 });\n    uni.showToast({ title: `已筛选 ${route.start}→${route.end} 路线`, icon: 'none', duration: 1500 });\n};\n\nconst viewAllCarpools = () => {\n    currentType.value = '';\n    filterStartPoint.value = '';\n    filterEndPoint.value = '';\n    sortCarpoolList();\n};\n\nconst viewDetail = (item) => {\n    uni.navigateTo({ url: `/carpool-package/pages/carpool/detail/index?id=${item.id}&type=${item.type}` });\n};\n\nconst callPhone = (phone) => {\n    uni.makePhoneCall({ phoneNumber: phone });\n};\n\nconst chatWith = (userId) => {\n    uni.navigateTo({ url: `/pages/message/chat?userId=${userId}` });\n};\n\nconst publishCarpool = () => {\n    activeTab.value = 'publish';\n  showPublishPopup.value = true;\n};\n\nconst getLatestCarpools = () => {\n    sortCarpoolList();\n};\n\nconst parseDepartureTime = (timeStr) => {\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n\n    if (timeStr.includes('今天')) {\n        const timeParts = timeStr.split(' ')[1].split(':');\n        return today.setHours(parseInt(timeParts[0]), parseInt(timeParts[1]));\n    } else if (timeStr.includes('明天')) {\n        const timeParts = timeStr.split(' ')[1].split(':');\n        return tomorrow.setHours(parseInt(timeParts[0]), parseInt(timeParts[1]));\n    } else {\n        return new Date(timeStr).getTime();\n    }\n};\n\nconst sortCarpoolList = () => {\n    let filteredList = latestCarpools.slice();\n    if (currentType.value) {\n        filteredList = filteredList.filter(item => item.type === currentType.value);\n    }\n    if (filterStartPoint.value && filterEndPoint.value) {\n        filteredList = filteredList.filter(item => \n            item.startPoint.includes(filterStartPoint.value) && \n            item.endPoint.includes(filterEndPoint.value)\n        );\n    }\n    sortedCarpoolList.value = filteredList.sort((a, b) => {\n        if (a.publishMode === 'premium' && b.publishMode !== 'premium') return -1;\n        if (a.publishMode !== 'premium' && b.publishMode === 'premium') return 1;\n        if (sortType.value === 'departureTime') {\n            return parseDepartureTime(a.departureTime) - parseDepartureTime(b.departureTime);\n        } else if (sortType.value === 'publishTime') {\n            return b.id - a.id;\n        }\n        return 0;\n    });\n\n    if (sortedCarpoolList.value.length === 0 && (currentType.value || filterStartPoint.value)) {\n        uni.showToast({ title: '没有找到符合条件的拼车信息', icon: 'none', duration: 2000 });\n    }\n};\n\nconst navigateToPage = (page) => {\n    activeTab.value = page;\n    switch(page) {\n        case 'home':\n            uni.showTabBar();\n            setTimeout(() => uni.switchTab({ url: '/pages/index/index' }), 50);\n            break;\n        case 'groups':\n            uni.hideTabBar();\n            uni.navigateTo({ url: '/carpool-package/pages/carpool/groups/index' });\n            break;\n        case 'my':\n            uni.hideTabBar();\n            uni.navigateTo({ url: '/carpool-package/pages/carpool/my/index' });\n            break;\n    }\n};\n\nconst closePublishPopup = () => {\n  showPublishPopup.value = false;\n    activeTab.value = 'carpool';\n};\n\nconst navigateToPublish = (type) => {\n  closePublishPopup();\n    let url = '';\n    switch(type) {\n        case 'people-to-car': url = '/carpool-package/pages/carpool/publish/people-to-car'; break;\n        case 'car-to-people': url = '/carpool-package/pages/carpool/publish/car-to-people'; break;\n        case 'goods-to-car': url = '/carpool-package/pages/carpool/publish/goods-to-car'; break;\n        case 'car-to-goods': url = '/carpool-package/pages/carpool/publish/car-to-goods'; break;\n        default: url = '/carpool-package/pages/carpool/publish/people-to-car';\n    }\n    uni.navigateTo({ url });\n};\n\nconst toggleSortOptions = () => {\n    showSortOptions.value = !showSortOptions.value;\n};\n\nconst changeSortType = (type) => {\n    sortType.value = type;\n    showSortOptions.value = false;\n    sortCarpoolList();\n};\n\nconst goBack = () => {\n    uni.navigateBack();\n};\n\n// 生命周期钩子\nonLoad((options) => {\n    setStatusBarHeight();\n    getLatestCarpools();\n    activeTab.value = 'carpool';\n    uni.hideTabBar();\n    setTimeout(() => { showAnimation.value = true; }, 300);\n    \n    // 获取当前页面对象\n    const currentPage = getCurrentPages().pop();\n    const eventChannel = currentPage.getOpenerEventChannel();\n    \n    if (eventChannel && eventChannel.on) {\n        eventChannel.on('showPublishPopup', () => {\n            showPublishPopup.value = true;\n        });\n    }\n\n    const showPopup = uni.getStorageSync('showPublishPopup');\n    if (showPopup) {\n        showPublishPopup.value = true;\n        uni.removeStorageSync('showPublishPopup');\n    }\n    sortCarpoolList();\n});\n\nonReady(() => {\n    setTimeout(() => {\n        const storedHeight = uni.getStorageSync('statusBarHeight');\n        if (storedHeight && storedHeight !== statusBarHeight.value) {\n            statusBarHeight.value = storedHeight;\n            if (typeof document !== 'undefined' && document.documentElement) {\n                document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight.value}px`);\n            }\n        }\n    }, 50);\n});\n\nonShow(() => {\n    uni.hideTabBar();\n});\n\nonHide(() => {\n    setTimeout(() => uni.showTabBar(), 100);\n});\n\nonUnload(() => {\n    uni.showTabBar();\n});\n\nonBackPress((event) => {\n    if (event.from === 'backbutton') {\n        uni.showTabBar();\n    uni.switchTab({ url: '/pages/index/index' });\n        return true;\n  }\n    return false;\n});\n</script>\n\n<style lang=\"scss\">\n/* 全局CSS变量定义 */\n:root {\n  --status-bar-color: #1677FF;\n  --status-bar-bg-color: #1677FF;\n  --navbar-bg-color: #1677FF;\n}\n\npage {\n  background-color: #F5F8FC;\n  --status-bar-color: #1677FF;\n}\n\n/* 平台特定覆盖 - 确保状态栏颜色正确 */\n/* #ifdef H5 || MP */\n:root, page {\n  --status-bar-color: #1677FF !important;\n}\n\n@media (prefers-color-scheme: light), (prefers-color-scheme: dark) {\n  uni-page-head, uni-page-head .uni-page-head {\n    background-color: #1677FF !important;\n  }\n}\n\n/* 强制覆盖小程序状态栏颜色 */\nuni-page-head {\n  background-color: #1677FF !important;\n}\n\nuni-page-head .uni-page-head {\n  background-color: #1677FF !important;\n}\n/* #endif */\n\n/* #ifdef APP-PLUS */\n/* APP平台特定样式 */\n:root, page {\n  --status-bar-color: #1677FF !important;\n  --status-bar-bg-color: #1677FF !important;\n  --navbar-bg-color: #1677FF !important;\n}\n/* #endif */\n\n.carpool-container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  padding-bottom: 140rpx;\n  padding-top: calc(var(--status-bar-height) + 90rpx); /* 只考虑标题栏高度 */\n  position: relative;\n  overflow-x: hidden;\n}\n\n/* 自定义标题栏模块 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  width: 100%;\n  background-color: #1677FF; /* 恢复为实色背景 */\n  z-index: 103;\n  box-shadow: none;\n}\n\n/* 标题栏内容 */\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 90rpx;\n  padding: 0 30rpx;\n  position: relative;\n  z-index: 102;\n}\n\n.left-action {\n  width: 70rpx;\n  height: 70rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.title-area {\n  flex: 1;\n  text-align: center;\n}\n\n.page-title {\n  color: #ffffff;\n  font-size: 36rpx;\n  font-weight: 600;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.right-action {\n  width: 70rpx;\n  height: 70rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 44rpx;\n  height: 44rpx;\n  filter: brightness(0) invert(1);\n}\n\n/* 轮播图区域 - 确保在背景之上 */\n.swiper-section {\n  padding: 0 32rpx;\n  position: relative;\n  z-index: 10; /* 确保内容显示在圆弧背景之上 */\n  margin-top: 50rpx; /* 从20rpx增加到50rpx，向下移动30rpx */\n  background-color: transparent;\n}\n\n.swiper {\n  height: 220rpx;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.15);\n  background-color: #ffffff;\n  position: relative;\n  z-index: 1;\n}\n\n.swiper image {\n  width: 100%;\n  height: 100%;\n  border-radius: 24rpx;\n  transition: transform 0.3s ease;\n}\n\n/* 搜索框 */\n.search-section {\n  padding: 0 32rpx;\n  position: relative;\n  z-index: 10; /* 确保内容显示在圆弧背景之上 */\n  margin-top: 15rpx; /* 与轮播图的间距 */\n  margin-bottom: 15rpx; /* 下方间距 */\n  background-color: transparent;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  background-color: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 8rpx;\n  box-shadow: 0 8rpx 20rpx rgba(10, 132, 255, 0.15), 0 2rpx 4rpx rgba(0, 0, 0, 0.05); /* 增强阴影 */\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.7);\n  position: relative; /* 用于伪元素定位 */\n  overflow: hidden; /* 确保内容不溢出 */\n  \n  /* 添加微妙的内部光效 */\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 50%;\n    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), transparent);\n    pointer-events: none;\n  }\n}\n\n.search-input-wrapper {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  background-color: #F2F7FD;\n  border-radius: 16rpx;\n  padding: 0 20rpx;\n  height: 80rpx;\n  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05); /* 内阴影 */\n}\n\n.search-input {\n  flex: 1;\n  height: 80rpx;\n  font-size: 28rpx;\n  color: #333;\n  padding: 0 10rpx;\n}\n\n.search-exchange {\n  padding: 0 12rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.exchange-icon {\n  font-size: 32rpx;\n  color: #0A84FF;\n  font-weight: bold;\n}\n\n.search-button {\n  padding: 0 30rpx;\n  height: 80rpx;\n  background: linear-gradient(to right, #0A84FF, #0040DD);\n  color: #ffffff;\n  border-radius: 16rpx;\n  margin-left: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  font-weight: 500;\n  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3), 0 2rpx 4rpx rgba(0, 0, 0, 0.1); /* 增强阴影 */\n  position: relative; /* 用于伪元素定位 */\n  overflow: hidden; /* 确保内容不溢出 */\n  \n  /* 添加微妙的内部光效 */\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 50%;\n    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);\n    pointer-events: none;\n  }\n  \n  /* 按下效果 */\n  &:active {\n    transform: translateY(1rpx) scale(0.98);\n    box-shadow: 0 2rpx 6rpx rgba(10, 132, 255, 0.2);\n  }\n}\n\n/* 四宫格卡片 */\n.grid-section {\n  padding: 10rpx;\n  background-color: transparent;\n  margin: 16rpx 32rpx; /* 减少上下边距 */\n  border-radius: 24rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.grid-row {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx; /* 减少行间距 */\n}\n\n.grid-row:last-child {\n  margin-bottom: 0;\n}\n\n.grid-item {\n  width: 48%; /* 固定宽度为48% */\n  margin: 1%; /* 使用margin替代gap */\n  padding: 20rpx; /* 调整内边距 */\n  background-color: #FFFFFF;\n  border-radius: 20rpx; /* 增大圆角从12rpx到20rpx */\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n  box-sizing: border-box;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05); /* 减轻阴影 */\n  position: relative;\n  overflow: hidden;\n  border: 1rpx solid #EEEEEE; /* 普通浅灰色边框 */\n  flex-shrink: 0;\n  flex-grow: 0;\n  \n  /* 简化光效 */\n  &::after {\n    display: none; /* 移除光效 */\n  }\n}\n\n.grid-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));\n  z-index: 1;\n  border-radius: 32rpx;\n}\n\n.grid-item::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.25), transparent 70%);\n  z-index: 1;\n  border-radius: 32rpx;\n}\n\n.grid-item:active {\n  transform: translateY(-3rpx) scale(0.98);\n  box-shadow: 0 5rpx 12rpx rgba(0, 0, 0, 0.08);\n}\n\n.grid-content {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  z-index: 2;\n  padding: 16rpx;\n}\n\n.grid-title {\n  font-size: 34rpx;\n  font-weight: 700;\n  color: #ffffff;\n  margin-bottom: 8rpx;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);\n  position: relative;\n  z-index: 3;\n  letter-spacing: 1rpx;\n}\n\n.grid-subtitle {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.95);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);\n  position: relative;\n  z-index: 3;\n}\n\n.people-to-car {\n  background: linear-gradient(135deg, #4F6EF7, #1E40AF);\n  box-shadow: 0 10rpx 25rpx rgba(79, 110, 247, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);\n}\n\n.car-to-people {\n  background: linear-gradient(135deg, #F43F5E, #BE123C);\n  box-shadow: 0 10rpx 25rpx rgba(244, 63, 94, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);\n}\n\n.goods-to-car {\n  background: linear-gradient(135deg, #10B981, #047857);\n  box-shadow: 0 10rpx 25rpx rgba(16, 185, 129, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);\n}\n\n.car-to-goods {\n  background: linear-gradient(135deg, #F59E0B, #B45309);\n  box-shadow: 0 10rpx 25rpx rgba(245, 158, 11, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);\n}\n\n/* 热门路线 */\n.section.hot-routes {\n  margin: 16rpx 32rpx; /* 减少上下边距 */\n  padding: 24rpx; /* 减少内边距 */\n  background-color: #FFFFFF;\n  border-radius: 24rpx;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);\n  position: relative;\n  z-index: 1;\n  border: 1rpx solid rgba(0, 0, 0, 0.02);\n  overflow: hidden;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24rpx;\n  position: relative;\n  padding-bottom: 0; /* 移除底部内边距 */\n}\n\n/* 移除下边框 */\n.section-header::after {\n  display: none;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  position: relative;\n  padding-left: 0; /* 移除左侧内边距 */\n}\n\n/* 移除左侧蓝色装饰条 */\n.section-title::before {\n  display: none;\n}\n\n.more-link {\n  font-size: 26rpx;\n  color: #0A84FF;\n  font-weight: 500;\n  padding: 6rpx 12rpx; /* 增加点击区域 */\n  border-radius: 12rpx; /* 圆角边框 */\n  transition: all 0.2s ease;\n  \n  /* 微妙的悬停效果 */\n  &:active {\n    background-color: rgba(10, 132, 255, 0.08);\n    transform: scale(0.95);\n  }\n}\n\n.route-list {\n  display: flex;\n  flex-flow: row wrap;\n  width: 100%;\n  box-sizing: border-box;\n  padding: 4rpx;\n  justify-content: space-between;\n}\n\n.route-item {\n  width: 48%;\n  margin-bottom: 16rpx;\n  padding: 20rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n  box-sizing: border-box;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: hidden;\n  border: 1rpx solid #EEEEEE;\n  flex: 0 0 auto;\n  \n  /* 简化光效 */\n  &::after {\n    display: none; /* 移除光效 */\n  }\n}\n\n/* iOS设备特殊处理 */\n@supports (-webkit-touch-callout: none) {\n  .route-list {\n    display: flex;\n    flex-flow: row wrap;\n    width: 100%;\n    box-sizing: border-box;\n    padding: 4rpx;\n  }\n  \n  .route-item {\n    width: 48%;\n    margin: 1%;\n    flex: 0 0 auto;\n    box-sizing: border-box;\n  }\n}\n\n.route-item:active {\n  transform: translateY(1rpx); /* 减轻按下效果 */\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);\n  background-color: #FAFAFA; /* 更淡的背景色 */\n}\n\n.route-path {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: relative;\n  z-index: 2;\n}\n\n.start-point, .end-point {\n  font-size: 28rpx;\n  color: #333333; /* 全部使用黑色 */\n  font-weight: 500;\n  max-width: 42%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 移除单独的颜色设置 */\n.start-point {\n  /* 移除蓝色 */\n}\n\n.end-point {\n  /* 保持黑色 */\n}\n\n/* 简化箭头设计 */\n.route-arrow-container {\n  position: relative;\n  width: 30rpx; /* 减小宽度从50rpx到30rpx */\n  height: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.route-arrow-container::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  right: 10rpx;\n  top: 50%;\n  height: 1rpx; /* 保持细线 */\n  background-color: #CCCCCC; /* 稍微亮一点的灰色 */\n  transform: translateY(-50%);\n}\n\n.route-arrow-container::after {\n  content: '';\n  position: absolute;\n  right: 0;\n  top: 50%;\n  width: 8rpx;\n  height: 8rpx;\n  border: 1rpx solid #CCCCCC; /* 与线条颜色匹配 */\n  border-left: none;\n  border-bottom: none;\n  transform: translateY(-50%) rotate(45deg);\n}\n\n/* 最新拼车信息 */\n.section.latest-carpools {\n  margin: 16rpx 32rpx; /* 减少上下边距 */\n  padding: 24rpx; /* 减少内边距 */\n  background-color: #FFFFFF;\n  border-radius: 24rpx;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);\n  position: relative;\n  z-index: 1;\n  border: 1rpx solid rgba(0, 0, 0, 0.02);\n  overflow: hidden;\n  margin-bottom: 50rpx; /* 减少底部边距 */\n}\n\n.sort-dropdown {\n  position: relative;\n}\n\n.sort-label {\n  display: flex;\n  align-items: center;\n  font-size: 26rpx;\n  color: #666666;\n  padding: 8rpx 16rpx;\n  background-color: #F2F7FD;\n  border-radius: 16rpx;\n}\n\n.sort-icon {\n  font-size: 20rpx;\n  margin-left: 6rpx;\n  color: #0A84FF;\n}\n\n.sort-options {\n  position: absolute;\n  right: 0;\n  top: 60rpx;\n  width: 200rpx;\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n  z-index: 10;\n  overflow: hidden;\n}\n\n.sort-option {\n  padding: 20rpx 24rpx;\n  font-size: 26rpx;\n  color: #333333;\n  transition: all 0.2s ease;\n}\n\n.sort-option:active {\n  background-color: #F2F7FD;\n}\n\n.carpool-list {\n  display: flex;\n  flex-direction: column;\n  gap: 24rpx;\n  padding: 4rpx; /* 添加内边距 */\n}\n\n.carpool-card {\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */\n  transition: all 0.3s ease;\n  position: relative;\n  border-left: 6rpx solid #0A84FF;\n  box-sizing: border-box;\n  \n  /* 添加顶部渐变边框 */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 6rpx; /* 留出左侧边框 */\n    right: 0;\n    height: 4rpx;\n    background: linear-gradient(to right, transparent, rgba(10, 132, 255, 0.3), transparent);\n    z-index: 2;\n  }\n  \n  /* 添加微妙的内部光效 */\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 6rpx; /* 留出左侧边框 */\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent 60%);\n    pointer-events: none;\n  }\n}\n\n.carpool-card.people-to-car {\n  border-left-color: #0A84FF;\n}\n\n.carpool-card.car-to-people {\n  border-left-color: #FF2D55;\n}\n\n.carpool-card.goods-to-car {\n  border-left-color: #30D158;\n}\n\n.carpool-card.car-to-goods {\n  border-left-color: #FF9F0A;\n}\n\n.carpool-card:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);\n}\n\n.card-header {\n  padding: 24rpx;\n  position: relative;\n  background-color: #FFFFFF;\n}\n\n.card-type-indicator {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 6rpx;\n  height: 100%;\n  background-color: inherit;\n}\n\n.route-direction {\n  margin-left: 12rpx;\n}\n\n.route-info-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.route-info-row:last-child {\n  margin-bottom: 0;\n}\n\n.route-label {\n  width: 80rpx;\n  font-size: 26rpx;\n  color: #999999;\n}\n\n.route-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n.via-points {\n  color: #666666;\n}\n\n.via-icon {\n  margin-right: 8rpx;\n  color: #0A84FF;\n}\n\n.route-value-wrapper {\n  display: flex;\n  align-items: center;\n}\n\n.card-type-tag {\n  position: absolute;\n  top: 24rpx;\n  right: 24rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 12rpx;\n  font-size: 22rpx;\n  font-weight: 500;\n  color: #FFFFFF;\n}\n\n.people-to-car-tag {\n  background-color: #0A84FF;\n}\n\n.car-to-people-tag {\n  background-color: #FF2D55;\n}\n\n.goods-to-car-tag {\n  background-color: #30D158;\n}\n\n.car-to-goods-tag {\n  background-color: #FF9F0A;\n}\n\n.publish-mode-tag {\n  position: absolute;\n  top: 70rpx;\n  right: 24rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 8rpx;\n  font-size: 20rpx;\n  color: #FFFFFF;\n}\n\n.premium {\n  background-color: #FF2D55;\n}\n\n.ad {\n  background-color: #FF9F0A;\n}\n\n.card-footer {\n  padding: 24rpx;\n  border-top: 1px solid #F2F2F7;\n  display: flex;\n  flex-direction: column;\n  background-color: #FFFFFF;\n}\n\n.trip-info {\n  display: flex;\n  gap: 24rpx;\n  flex-wrap: wrap;\n  margin-bottom: 16rpx;\n}\n\n.user-actions {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 12rpx;\n}\n\n.card-actions {\n  display: flex;\n  gap: 16rpx;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.info-icon {\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.info-value {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.user-avatar {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 24rpx;\n  border: 2rpx solid #FFFFFF;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.user-name-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 6rpx;\n}\n\n.user-name {\n  font-size: 26rpx;\n  color: #333333;\n  max-width: 120rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 认证标签样式 */\n.verified-badge {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, rgba(10, 132, 255, 0.15), rgba(10, 132, 255, 0.05));\n  padding: 4rpx 10rpx;\n  border-radius: 12rpx;\n  border: 1rpx solid rgba(10, 132, 255, 0.3);\n  box-shadow: 0 2rpx 6rpx rgba(10, 132, 255, 0.1);\n}\n\n.verified-icon-circle {\n  width: 24rpx;\n  height: 24rpx;\n  border-radius: 50%;\n  background-color: #0A84FF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 6rpx;\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);\n}\n\n.verified-icon-circle svg {\n  color: white;\n}\n\n.verified-text {\n  font-size: 20rpx;\n  color: #0A84FF;\n  font-weight: 500;\n}\n\n.action-button {\n  width: 88rpx;\n  height: 88rpx;\n  border-radius: 44rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.action-button.phone {\n  background-color: #34C759;\n  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.3);\n}\n\n.action-button.chat {\n  background-color: #0A84FF;\n  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3);\n}\n\n.action-button image {\n  width: 48rpx;\n  height: 48rpx;\n  filter: brightness(0) invert(1);\n}\n\n/* 发布类型选择弹窗 */\n.publish-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(8px);\n  -webkit-backdrop-filter: blur(8px);\n}\n\n.publish-card {\n  width: 80%;\n  background-color: #FFFFFF;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);\n  animation: card-pop 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;\n  transform: scale(0.95);\n  opacity: 0.8;\n}\n\n@keyframes card-pop {\n  0% {\n    transform: scale(0.95);\n    opacity: 0.8;\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n.publish-header {\n  padding: 24rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #F2F2F7;\n}\n\n.publish-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.close-btn {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 24rpx;\n  background-color: #F2F2F7;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.close-icon {\n  font-size: 32rpx;\n  color: #999999;\n}\n\n.publish-options {\n  padding: 32rpx;\n  display: flex;\n  flex-direction: column;\n}\n\n.publish-option-row {\n  display: flex;\n  justify-content: space-between;\n  width: 100%;\n  margin-bottom: 32rpx;\n}\n\n.publish-option-row:last-child {\n  margin-bottom: 0;\n}\n\n.publish-option {\n  width: 48%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 24rpx 0;\n  border-radius: 16rpx;\n  transition: all 0.3s ease;\n}\n\n.publish-option:active {\n  transform: scale(0.95);\n  background-color: #F5F8FC;\n}\n\n.option-text {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n.option-icon-wrapper {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 12rpx;\n  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.12);\n  border: 2rpx solid rgba(255, 255, 255, 0.7);\n  position: relative;\n  overflow: hidden;\n}\n\n.option-icon-wrapper::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.3), transparent 70%);\n  z-index: 1;\n}\n\n.option-icon {\n  width: 60rpx;\n  height: 60rpx;\n  filter: brightness(0) invert(1);\n  position: relative;\n  z-index: 2;\n}\n\n.people-car {\n  background: linear-gradient(135deg, #0A84FF, #5AC8FA);\n}\n\n.car-people {\n  background: linear-gradient(135deg, #FF2D55, #FF9500);\n}\n\n.goods-car {\n  background: linear-gradient(135deg, #30D158, #34C759);\n}\n\n.car-goods {\n  background: linear-gradient(135deg, #FF9F0A, #FFD60A);\n}\n\n/* 底部导航栏 */\n.tabbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 110rpx;\n  background-color: rgba(255, 255, 255, 0.95);\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\n  z-index: 1000;\n  padding-bottom: env(safe-area-inset-bottom);\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n}\n\n.tabbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 10rpx 0;\n  position: relative;\n  transition: all 0.2s ease;\n}\n\n.tabbar-item:active {\n  opacity: 0.7;\n}\n\n.tabbar-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-bottom: 6rpx;\n}\n\n.tabbar-text {\n  font-size: 22rpx;\n  color: #999999;\n  line-height: 1;\n}\n\n.active-text {\n  color: #0A84FF;\n  font-weight: 500;\n}\n\n.tabbar-item.active .tabbar-icon {\n  transform: scale(1.1);\n}\n\n/* 背景装饰 */\n.bg-decoration {\n  position: absolute;\n  border-radius: 50%;\n  z-index: 0;\n  opacity: 0.4;\n}\n\n.bg-circle-1 {\n  top: 260rpx; /* 调整位置 */\n  left: -150rpx;\n  width: 300rpx;\n  height: 300rpx;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent 70%);\n}\n\n.bg-circle-2 {\n  top: 80rpx; /* 调整位置 */\n  right: -100rpx;\n  width: 200rpx;\n  height: 200rpx;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.25), transparent 70%);\n}\n\n.bg-circle-3 {\n  bottom: 220rpx;\n  left: -120rpx;\n  width: 240rpx;\n  height: 240rpx;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.2), transparent 70%);\n}\n\n/* 过渡效果 */\n.bg-loaded {\n  animation: fadeIn 0.3s ease forwards;\n}\n\n.navbar-loaded {\n  animation: slideDown 0.3s ease forwards;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0.8; }\n  to { opacity: 1; }\n}\n\n@keyframes slideDown {\n  from { transform: translateY(-5px); opacity: 0.8; }\n  to { transform: translateY(0); opacity: 1; }\n}\n\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n  width: 100%;\n}\n\n/* 圆弧背景模块 */\n.arc-background {\n  position: fixed;\n  top: calc(var(--status-bar-height) + 90rpx); /* 从标题栏底部开始 */\n  left: 0;\n  right: 0;\n  height: 240rpx; /* 增加高度以覆盖更多内容区域 */\n  background-color: #1677FF;\n  border-bottom-left-radius: 32rpx;\n  border-bottom-right-radius: 32rpx;\n  z-index: 1;\n}\n\n/* 移除原来的伪元素背景 */\n.carpool-container::before {\n  content: none;\n}\n\n/* 内容区域布局调整 */\n.swiper-section, .search-section, .grid-section, .section {\n  position: relative;\n  z-index: 10; /* 确保内容显示在圆弧背景之上 */\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool-main/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "uni", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onBackPress", "MiniProgramPage"], "mappings": ";;;;;;;;;;;AAsRA,UAAM,UAAUA,cAAAA,SAAS;AAAA,MACvB,EAAE,IAAI,GAAG,OAAO,6CAA6C,KAAK,mCAAoC;AAAA,MACtG,EAAE,IAAI,GAAG,OAAO,6CAA6C,KAAK,kCAAmC;AAAA,IACvG,CAAC;AAED,UAAM,YAAYA,cAAAA,SAAS;AAAA,MACzB,EAAE,IAAI,GAAG,OAAO,MAAM,KAAK,MAAM,OAAO,IAAK;AAAA,MAC7C,EAAE,IAAI,GAAG,OAAO,MAAM,KAAK,MAAM,OAAO,IAAK;AAAA,MAC7C,EAAE,IAAI,GAAG,OAAO,MAAM,KAAK,MAAM,OAAO,GAAI;AAAA,MAC5C,EAAE,IAAI,GAAG,OAAO,MAAM,KAAK,MAAM,OAAO,GAAI;AAAA,MAC5C,EAAE,IAAI,GAAG,OAAO,MAAM,KAAK,OAAO,OAAO,GAAI;AAAA,MAC7C,EAAE,IAAI,GAAG,OAAO,OAAO,KAAK,MAAM,OAAO,GAAI;AAAA,IAC/C,CAAC;AAED,UAAM,YAAYC,cAAAA,IAAI,SAAS;AAE/B,UAAM,iBAAiBD,cAAAA,SAAS;AAAA,MAC5B;AAAA,QACE,IAAI;AAAA,QACF,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,UAAU;AAAA,QACZ,eAAe;AAAA,QACb,OAAO;AAAA,QACT,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,aAAa;AAAA;AAAA,QACb,WAAW,CAAC,QAAQ,MAAM;AAAA,MAC7B;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACF,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,eAAe;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACT,OAAO;AAAA,QACL,QAAQ;AAAA,QACR,aAAa;AAAA;AAAA,QACb,WAAW,CAAC,QAAQ,KAAK;AAAA,MAC5B;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACF,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,eAAe;AAAA,QACf,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,aAAa;AAAA;AAAA,QACb,WAAW,CAAC,OAAO,KAAK;AAAA,MAC3B;AAAA,MACD;AAAA,QACI,IAAI;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACZ,YAAY;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,eAAe;AAAA,QACf,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,aAAa;AAAA;AAAA,QACb,WAAW,CAAE;AAAA,MAChB;AAAA,IACL,CAAC;AAED,UAAM,kBAAkBC,cAAAA,IAAI,CAAC;AAC7B,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,WAAWA,cAAAA,IAAI,EAAE;AACvB,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAClC,UAAM,oBAAoBA,cAAAA,IAAI,CAAA,CAAE;AAChC,UAAM,WAAWA,cAAAA,IAAI,SAAS;AAC9B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,mBAAmBA,cAAAA,IAAI,EAAE;AAC/B,UAAM,iBAAiBA,cAAAA,IAAI,EAAE;AAE7B,UAAM,eAAeC,cAAQ,SAAC,MAAM;AAChC,cAAQ,SAAS,OAAK;AAAA,QAClB,KAAK;AAAW,iBAAO;AAAA,QACvB,KAAK;AAAiB,iBAAO;AAAA,QAC7B,KAAK;AAAe,iBAAO;AAAA,QAC3B;AAAS,iBAAO;AAAA,MACnB;AAAA,IACL,CAAC;AAGD,UAAM,qBAAqB,MAAM;AAC7B,UAAI;AACA,cAAM,aAAaC,oBAAI;AACvB,wBAAgB,QAAQ,WAAW,mBAAmB;AAEtD,YAAI,WAAW,OAAO;AAClB,gBAAM,QAAQ,WAAW,MAAM,YAAW;AAC1C,cAAI,MAAM,SAAS,QAAQ,GAAG;AAC1B,gBAAI,WAAW,kBAAkB,WAAW,eAAe,MAAM,IAAI;AACjE,8BAAgB,QAAQ,KAAK,IAAI,gBAAgB,OAAO,WAAW,eAAe,GAAG;AAAA,YACxF;AAAA,UACJ;AAAA,QACJ;AAED,YAAI,OAAO,aAAa,eAAe,SAAS,iBAAiB;AAC7D,mBAAS,gBAAgB,MAAM,YAAY,uBAAuB,GAAG,gBAAgB,KAAK,IAAI;AAC9F,mBAAS,gBAAgB,MAAM,YAAY,sBAAsB,SAAS;AAC1E,mBAAS,gBAAgB,MAAM,YAAY,yBAAyB,SAAS;AAAA,QAChF;AACDA,sBAAAA,MAAI,eAAe,mBAAmB,gBAAgB,KAAK;AAG3DA,sBAAAA,MAAI,sBAAsB;AAAA,UACtB,YAAY;AAAA,UACZ,iBAAiB;AAAA,UACjB,WAAW;AAAA,YACP,UAAU;AAAA,YACV,YAAY;AAAA,UACf;AAAA,QACb,CAAS;AAQD,YAAI,WAAW,UAAU;AACrBA,wBAAAA,MAAI,eAAe,YAAY,WAAW,QAAQ;AAClDA,wBAAG,MAAC,eAAe,SAAS,WAAW,aAAa,KAAK;AAAA,QAC5D;AAAA,MACJ,SAAQ,GAAG;AACRA,sBAAA,MAAA,MAAA,SAAA,uDAAc,cAAc,CAAC;AAC7B,wBAAgB,QAAQ;AACxB,YAAI,OAAO,aAAa,eAAe,SAAS,iBAAiB;AAC7D,mBAAS,gBAAgB,MAAM,YAAY,uBAAuB,GAAG,gBAAgB,KAAK,IAAI;AAAA,QACjG;AACDA,sBAAAA,MAAI,eAAe,mBAAmB,gBAAgB,KAAK;AAAA,MAC9D;AAAA,IACL;AAEA,UAAM,oBAAoB,CAAC,WAAW;AAClCA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,qBAAqB,OAAO;AAAA,MACzC,CAAK;AAAA,IACL;AAMA,UAAM,eAAe,MAAM;AACvB,UAAI,CAAC,WAAW,SAAS,CAAC,SAAS,OAAO;AACtCA,sBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAM,CAAE;AACnD;AAAA,MACH;AACHA,oBAAAA,MAAI,WAAW;AAAA,QACT,KAAK,mDAAmD,WAAW,KAAK,QAAQ,SAAS,KAAK;AAAA,MACtG,CAAG;AAAA,IACH;AAEA,UAAM,cAAc,CAAC,SAAS;AAC1B,cAAO,MAAI;AAAA,QACP,KAAK;AAAiB,iBAAO;AAAA,QAC7B,KAAK;AAAiB,iBAAO;AAAA,QAC7B,KAAK;AAAgB,iBAAO;AAAA,QAC5B,KAAK;AAAgB,iBAAO;AAAA,QAC5B;AAAS,iBAAO;AAAA,MACnB;AAAA,IACL;AAEA,UAAM,aAAa,CAAC,SAAS;AACzB,kBAAY,QAAQ;AACpB;AACAA,oBAAG,MAAC,aAAa,EAAE,UAAU,oBAAoB,UAAU,IAAG,CAAE;AAChEA,oBAAAA,MAAI,UAAU,EAAE,OAAO,MAAM,YAAY,IAAI,CAAC,MAAM,MAAM,QAAQ,UAAU,KAAM,CAAA;AAAA,IACtF;AAEA,UAAM,iBAAiB,MAAM;AACzBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,8CAA+C,CAAA;AAAA,IACzE;AAEA,UAAM,cAAc,CAAC,UAAU;AAC3B,UAAI,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC,MAAM,KAAK;AACtCA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAM,CAAE;AAChD;AAAA,MACH;AACD,uBAAiB,QAAQ,MAAM;AAC/B,qBAAe,QAAQ,MAAM;AAC7B;AACAA,oBAAG,MAAC,aAAa,EAAE,UAAU,oBAAoB,UAAU,IAAG,CAAE;AAChEA,oBAAG,MAAC,UAAU,EAAE,OAAO,OAAO,MAAM,KAAK,IAAI,MAAM,GAAG,OAAO,MAAM,QAAQ,UAAU,KAAI,CAAE;AAAA,IAC/F;AASA,UAAM,aAAa,CAAC,SAAS;AACzBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,kDAAkD,KAAK,EAAE,SAAS,KAAK,IAAI,GAAI,CAAA;AAAA,IACzG;AAEA,UAAM,YAAY,CAAC,UAAU;AACzBA,oBAAAA,MAAI,cAAc,EAAE,aAAa,MAAO,CAAA;AAAA,IAC5C;AAEA,UAAM,WAAW,CAAC,WAAW;AACzBA,oBAAG,MAAC,WAAW,EAAE,KAAK,8BAA8B,MAAM,GAAE,CAAE;AAAA,IAClE;AAEA,UAAM,iBAAiB,MAAM;AACzB,gBAAU,QAAQ;AACpB,uBAAiB,QAAQ;AAAA,IAC3B;AAEA,UAAM,oBAAoB,MAAM;AAC5B;IACJ;AAEA,UAAM,qBAAqB,CAAC,YAAY;AACpC,YAAM,MAAM,oBAAI;AAChB,YAAM,QAAQ,IAAI,KAAK,IAAI,YAAW,GAAI,IAAI,SAAU,GAAE,IAAI,QAAS,CAAA;AACvE,YAAM,WAAW,IAAI,KAAK,KAAK;AAC/B,eAAS,QAAQ,SAAS,QAAS,IAAG,CAAC;AAEvC,UAAI,QAAQ,SAAS,IAAI,GAAG;AACxB,cAAM,YAAY,QAAQ,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG;AACjD,eAAO,MAAM,SAAS,SAAS,UAAU,CAAC,CAAC,GAAG,SAAS,UAAU,CAAC,CAAC,CAAC;AAAA,MACvE,WAAU,QAAQ,SAAS,IAAI,GAAG;AAC/B,cAAM,YAAY,QAAQ,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG;AACjD,eAAO,SAAS,SAAS,SAAS,UAAU,CAAC,CAAC,GAAG,SAAS,UAAU,CAAC,CAAC,CAAC;AAAA,MAC/E,OAAW;AACH,eAAO,IAAI,KAAK,OAAO,EAAE,QAAO;AAAA,MACnC;AAAA,IACL;AAEA,UAAM,kBAAkB,MAAM;AAC1B,UAAI,eAAe,eAAe;AAClC,UAAI,YAAY,OAAO;AACnB,uBAAe,aAAa,OAAO,UAAQ,KAAK,SAAS,YAAY,KAAK;AAAA,MAC7E;AACD,UAAI,iBAAiB,SAAS,eAAe,OAAO;AAChD,uBAAe,aAAa;AAAA,UAAO,UAC/B,KAAK,WAAW,SAAS,iBAAiB,KAAK,KAC/C,KAAK,SAAS,SAAS,eAAe,KAAK;AAAA,QACvD;AAAA,MACK;AACD,wBAAkB,QAAQ,aAAa,KAAK,CAAC,GAAG,MAAM;AAClD,YAAI,EAAE,gBAAgB,aAAa,EAAE,gBAAgB;AAAW,iBAAO;AACvE,YAAI,EAAE,gBAAgB,aAAa,EAAE,gBAAgB;AAAW,iBAAO;AACvE,YAAI,SAAS,UAAU,iBAAiB;AACpC,iBAAO,mBAAmB,EAAE,aAAa,IAAI,mBAAmB,EAAE,aAAa;AAAA,QAC3F,WAAmB,SAAS,UAAU,eAAe;AACzC,iBAAO,EAAE,KAAK,EAAE;AAAA,QACnB;AACD,eAAO;AAAA,MACf,CAAK;AAED,UAAI,kBAAkB,MAAM,WAAW,MAAM,YAAY,SAAS,iBAAiB,QAAQ;AACvFA,4BAAI,UAAU,EAAE,OAAO,iBAAiB,MAAM,QAAQ,UAAU,IAAI,CAAE;AAAA,MACzE;AAAA,IACL;AAEA,UAAM,iBAAiB,CAAC,SAAS;AAC7B,gBAAU,QAAQ;AAClB,cAAO,MAAI;AAAA,QACP,KAAK;AACDA,wBAAG,MAAC,WAAU;AACd,qBAAW,MAAMA,cAAG,MAAC,UAAU,EAAE,KAAK,qBAAsB,CAAA,GAAG,EAAE;AACjE;AAAA,QACJ,KAAK;AACDA,wBAAG,MAAC,WAAU;AACdA,wBAAAA,MAAI,WAAW,EAAE,KAAK,8CAA+C,CAAA;AACrE;AAAA,QACJ,KAAK;AACDA,wBAAG,MAAC,WAAU;AACdA,wBAAAA,MAAI,WAAW,EAAE,KAAK,0CAA2C,CAAA;AACjE;AAAA,MACP;AAAA,IACL;AAEA,UAAM,oBAAoB,MAAM;AAC9B,uBAAiB,QAAQ;AACvB,gBAAU,QAAQ;AAAA,IACtB;AAEA,UAAM,oBAAoB,CAAC,SAAS;AAClC;AACE,UAAI,MAAM;AACV,cAAO,MAAI;AAAA,QACP,KAAK;AAAiB,gBAAM;AAAwD;AAAA,QACpF,KAAK;AAAiB,gBAAM;AAAwD;AAAA,QACpF,KAAK;AAAgB,gBAAM;AAAuD;AAAA,QAClF,KAAK;AAAgB,gBAAM;AAAuD;AAAA,QAClF;AAAS,gBAAM;AAAA,MAClB;AACDA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IAC1B;AAEA,UAAM,oBAAoB,MAAM;AAC5B,sBAAgB,QAAQ,CAAC,gBAAgB;AAAA,IAC7C;AAEA,UAAM,iBAAiB,CAAC,SAAS;AAC7B,eAAS,QAAQ;AACjB,sBAAgB,QAAQ;AACxB;IACJ;AAEA,UAAM,SAAS,MAAM;AACjBA,oBAAG,MAAC,aAAY;AAAA,IACpB;AAGAC,kBAAM,OAAC,CAAC,YAAY;AAChB;AACA;AACA,gBAAU,QAAQ;AAClBD,oBAAG,MAAC,WAAU;AACd,iBAAW,MAAM;AAAE,sBAAc,QAAQ;AAAA,MAAK,GAAI,GAAG;AAGrD,YAAM,cAAc,kBAAkB;AACtC,YAAM,eAAe,YAAY;AAEjC,UAAI,gBAAgB,aAAa,IAAI;AACjC,qBAAa,GAAG,oBAAoB,MAAM;AACtC,2BAAiB,QAAQ;AAAA,QACrC,CAAS;AAAA,MACJ;AAED,YAAM,YAAYA,cAAAA,MAAI,eAAe,kBAAkB;AACvD,UAAI,WAAW;AACX,yBAAiB,QAAQ;AACzBA,4BAAI,kBAAkB,kBAAkB;AAAA,MAC3C;AACD;IACJ,CAAC;AAEDE,kBAAAA,QAAQ,MAAM;AACV,iBAAW,MAAM;AACb,cAAM,eAAeF,cAAAA,MAAI,eAAe,iBAAiB;AACzD,YAAI,gBAAgB,iBAAiB,gBAAgB,OAAO;AACxD,0BAAgB,QAAQ;AACxB,cAAI,OAAO,aAAa,eAAe,SAAS,iBAAiB;AAC7D,qBAAS,gBAAgB,MAAM,YAAY,uBAAuB,GAAG,gBAAgB,KAAK,IAAI;AAAA,UACjG;AAAA,QACJ;AAAA,MACJ,GAAE,EAAE;AAAA,IACT,CAAC;AAEDG,kBAAAA,OAAO,MAAM;AACTH,oBAAG,MAAC,WAAU;AAAA,IAClB,CAAC;AAEDI,kBAAAA,OAAO,MAAM;AACT,iBAAW,MAAMJ,cAAG,MAAC,WAAY,GAAE,GAAG;AAAA,IAC1C,CAAC;AAEDK,kBAAAA,SAAS,MAAM;AACXL,oBAAG,MAAC,WAAU;AAAA,IAClB,CAAC;AAEDM,kBAAW,YAAC,CAAC,UAAU;AACnB,UAAI,MAAM,SAAS,cAAc;AAC7BN,sBAAG,MAAC,WAAU;AAClBA,sBAAAA,MAAI,UAAU,EAAE,KAAK,qBAAsB,CAAA;AACvC,eAAO;AAAA,MACZ;AACC,aAAO;AAAA,IACX,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzpBD,GAAG,WAAWO,SAAe;"}