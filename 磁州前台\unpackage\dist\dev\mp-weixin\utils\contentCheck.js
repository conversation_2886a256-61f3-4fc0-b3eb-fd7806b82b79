"use strict";
const common_vendor = require("../common/vendor.js");
const SENSITIVE_WORDS = [
  "赌博",
  "博彩",
  "色情",
  "暴力",
  "毒品",
  "违禁品",
  "诈骗",
  "传销",
  "非法",
  "黄色",
  "办证",
  "枪支",
  "走私",
  "病毒",
  "黑客",
  "私服",
  "外挂",
  "代考",
  "代写",
  "代办",
  "伪造",
  "假证",
  "黑产",
  "裸聊"
];
const VIOLATION_TYPES = {
  NORMAL: 0,
  // 正常内容
  SENSITIVE: 1,
  // 敏感内容
  POLITICAL: 2,
  // 政治敏感
  PORN: 3,
  // 色情
  ABUSE: 4,
  // 辱骂
  VIOLENCE: 5,
  // 暴力
  FRAUD: 6,
  // 诈骗
  ILLEGAL: 7,
  // 其他违法
  AD: 8,
  // 广告
  SPAM: 9
  // 垃圾信息
};
const checkText = async (text) => {
  if (!text || text.trim() === "") {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: "内容正常"
    };
  }
  try {
    if (true) {
      return localCheckText(text);
    }
    const result = await common_vendor.tr.callFunction({
      name: "contentCheck",
      data: {
        type: "text",
        content: text
      }
    });
    return result.result || {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: "内容正常"
    };
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/contentCheck.js:65", "文本内容审核异常:", error);
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: "内容审核失败，将进行人工复核",
      needReview: true
    };
  }
};
const checkImage = async (images) => {
  if (!images) {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: "内容正常"
    };
  }
  const imageArray = Array.isArray(images) ? images : [images];
  if (imageArray.length === 0) {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: "内容正常"
    };
  }
  try {
    if (true) {
      return {
        pass: true,
        type: VIOLATION_TYPES.NORMAL,
        message: "开发环境图片默认通过"
      };
    }
    const checkResults = await Promise.all(
      imageArray.map(async (imagePath) => {
        const result = await common_vendor.tr.callFunction({
          name: "contentCheck",
          data: {
            type: "image",
            url: imagePath
          }
        });
        return result.result || {
          pass: true,
          type: VIOLATION_TYPES.NORMAL,
          message: "内容正常",
          url: imagePath
        };
      })
    );
    const failedCheck = checkResults.find((item) => !item.pass);
    if (failedCheck) {
      return {
        pass: false,
        type: failedCheck.type,
        message: `图片内容违规: ${failedCheck.message}`,
        url: failedCheck.url
      };
    }
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: "所有图片内容正常"
    };
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/contentCheck.js:150", "图片内容审核异常:", error);
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: "图片审核失败，将进行人工复核",
      needReview: true
    };
  }
};
const localCheckText = (text) => {
  if (!text)
    return { pass: true, type: VIOLATION_TYPES.NORMAL, message: "内容正常" };
  const lowerText = text.toLowerCase();
  for (const word of SENSITIVE_WORDS) {
    if (lowerText.includes(word)) {
      return {
        pass: false,
        type: VIOLATION_TYPES.SENSITIVE,
        message: `内容包含违规词汇: ${word}`,
        word
      };
    }
  }
  const phoneRegex = /1[3-9]\d{9}/g;
  if (phoneRegex.test(text)) {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: "内容包含电话号码，请注意保护隐私",
      containsPhone: true
    };
  }
  if (lowerText.includes("优惠") && lowerText.includes("活动") || lowerText.includes("推广") && lowerText.includes("促销") || lowerText.includes("免费") && lowerText.includes("送") || lowerText.includes("点击链接")) {
    return {
      pass: false,
      type: VIOLATION_TYPES.AD,
      message: "内容疑似广告信息"
    };
  }
  return {
    pass: true,
    type: VIOLATION_TYPES.NORMAL,
    message: "内容正常"
  };
};
const checkContent = async (content) => {
  const { text, images } = content;
  const [textResult, imageResult] = await Promise.all([
    checkText(text),
    checkImage(images)
  ]);
  if (!textResult.pass || !imageResult.pass) {
    return {
      pass: false,
      textCheck: textResult,
      imageCheck: imageResult,
      message: textResult.pass ? imageResult.message : textResult.message
    };
  }
  const needReview = textResult.needReview || imageResult.needReview;
  return {
    pass: true,
    textCheck: textResult,
    imageCheck: imageResult,
    message: "内容审核通过",
    needReview
  };
};
exports.checkContent = checkContent;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/contentCheck.js.map
