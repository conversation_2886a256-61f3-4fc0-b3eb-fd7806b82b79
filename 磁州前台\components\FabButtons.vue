<template>
  <view class="fab-btns">
    <button class="fab-btn share-btn" @click="onShare" open-type="share">
      <image class="fab-icon" src="/static/images/tabbar/share.png" />
    </button>
    <button v-if="isHomePage || showBackToTop" class="fab-btn" @click="scrollToTop()">
      <image class="fab-icon" src="/static/images/tabbar/arrow-up.png" />
    </button>
    <button class="fab-btn" @click="onKefu">
      <image class="fab-icon" src="/static/images/tabbar/service.png" />
    </button>
    <view v-if="showQrcode" class="qrcode-popup-mask" @click.self="showQrcode = false">
      <view class="qrcode-popup">
        <image class="qrcode-img" src="/static/images/tabbar/wxacode.jpg" />
        <view class="qrcode-title">长按识别二维码，联系客服</view>
        <view class="qrcode-close" @click="showQrcode = false">×</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// --- Props ---
const props = defineProps({
    pageName: {
      type: String,
      default: ''
    },
    pageInfo: {
      type: Object,
      default: () => ({
        title: '磁州生活网',
        path: '/pages/index/index',
        imageUrl: ''
      })
    }
});

// --- 响应式状态 ---
const showQrcode = ref(false);
const showBackToTop = ref(false);
const scrollTop = ref(0);

// --- Computed ---
const isHomePage = computed(() => props.pageName === 'index');

// --- 方法 ---
const setScrollTop = (newScrollTop) => {
  scrollTop.value = newScrollTop;
  showBackToTop.value = newScrollTop > 200;
};

const scrollToTop = () => {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
};
      
const onShare = () => {
      // #ifndef MP-WEIXIN
  uni.showToast({ title: '请点击右上角"..."进行转发', icon: 'none' });
      // #endif
};

const onKefu = () => {
  showQrcode.value = true;
};

// --- Expose ---
// 暴露方法给父组件调用
defineExpose({
  setScrollTop
});

// --- 页面生命周期 (小程序分享) ---
// 注意：在Vue 3 <script setup>中，页面生命周期 onShareAppMessage 不能直接使用
// 需要在父页面中处理分享逻辑，或者使用其他方式。
// 为了保持功能一致，暂时保留此注释，父页面需要实现分享。
/*
onShareAppMessage(() => {
    return {
    title: props.pageInfo.title,
    path: props.pageInfo.path,
    imageUrl: props.pageInfo.imageUrl || '/static/images/share-default.png'
  };
});
*/
</script>

<style scoped>
.fab-btns {
  position: fixed;
  right: 32rpx;
  bottom: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  z-index: 9999;
}
.fab-btn {
  width: 66rpx;
  height: 66rpx;
  border-radius: 50%;
  background: rgba(240, 240, 240, 0.9);
  box-shadow: 0 3rpx 20rpx rgba(0,0,0,0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  border: none;
  padding: 0;
  outline: none;
  transition: box-shadow 0.2s, transform 0.2s;
  overflow: hidden;
  box-sizing: border-box;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}
.fab-btn:after {
  border: none;
}
.fab-btn:active {
  box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.12);
  transform: scale(0.96);
}
.share-btn {
  background: rgba(255, 255, 255, 0.95);
  width: 66rpx;
  height: 66rpx;
  box-shadow: 0 3rpx 20rpx rgba(0,0,0,0.15);
}
.share-btn .fab-icon {
  filter: hue-rotate(100deg) saturate(150%);
}
.fab-icon {
  width: 40rpx;
  height: 40rpx;
  display: block;
}
.qrcode-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.32);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qrcode-popup {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.13);
  padding: 38rpx 32rpx 28rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 420rpx;
  animation: fadeInUp 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}
.qrcode-img {
  width: 260rpx;
  height: 260rpx;
  border-radius: 18rpx;
  margin-bottom: 18rpx;
  box-shadow: 0 2rpx 12rpx rgba(64,158,255,0.10);
}
.qrcode-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}
.qrcode-close {
  position: absolute;
  top: 12rpx;
  right: 18rpx;
  font-size: 38rpx;
  color: #bbb;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.2s;
  z-index: 2;
}
.qrcode-close:active {
  color: #409eff;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
    filter: saturate(0.8) brightness(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: saturate(1) brightness(1);
  }
}
</style> 