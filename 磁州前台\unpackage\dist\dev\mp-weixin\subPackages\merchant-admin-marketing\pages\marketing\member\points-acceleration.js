"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      rules: [],
      // 积分加速规则列表
      ruleForm: {
        id: "",
        name: "",
        color: "#1E90FF",
        // 默认蓝色
        icon: "",
        multiplier: "",
        scope: "",
        applicableLevels: [],
        description: ""
      },
      isEditing: false,
      // 是否为编辑模式
      currentRuleId: null,
      // 当前编辑的规则ID
      colorOptions: [
        "#FF6B22",
        // 橙色
        "#8A2BE2",
        // 紫色
        "#1E90FF",
        // 道奇蓝
        "#32CD32",
        // 酸橙绿
        "#FFD700",
        // 金色
        "#FF69B4",
        // 热粉红
        "#20B2AA",
        // 浅海绿
        "#FF8C00"
        // 深橙色
      ],
      ruleScopes: [
        "全部商品",
        "指定分类商品",
        "指定商品",
        "会员专享商品"
      ],
      memberLevels: []
      // 会员等级列表
    };
  },
  onLoad() {
    this.fetchRules();
    this.fetchMemberLevels();
  },
  methods: {
    // 获取积分加速规则列表
    fetchRules() {
      this.rules = [
        {
          id: "1",
          name: "银卡会员积分加速",
          color: "#C0C0C0",
          icon: "/static/images/points-silver.svg",
          multiplier: "1.2",
          scope: "全部商品",
          applicableLevels: ["银卡会员"],
          description: "银卡会员购物可获得1.2倍积分奖励"
        },
        {
          id: "2",
          name: "金卡会员积分加速",
          color: "#FFD700",
          icon: "/static/images/points-gold.svg",
          multiplier: "1.5",
          scope: "全部商品",
          applicableLevels: ["金卡会员"],
          description: "金卡会员购物可获得1.5倍积分奖励"
        },
        {
          id: "3",
          name: "钻石会员积分加速",
          color: "#B9F2FF",
          icon: "/static/images/points-diamond.svg",
          multiplier: "2",
          scope: "全部商品",
          applicableLevels: ["钻石会员"],
          description: "钻石会员购物可获得2倍积分奖励"
        }
      ];
    },
    // 获取会员等级列表
    fetchMemberLevels() {
      this.memberLevels = [
        {
          id: "1",
          name: "普通会员"
        },
        {
          id: "2",
          name: "银卡会员"
        },
        {
          id: "3",
          name: "金卡会员"
        },
        {
          id: "4",
          name: "钻石会员"
        }
      ];
    },
    // 显示添加规则弹窗
    showAddRuleModal() {
      this.isEditing = false;
      this.ruleForm = {
        id: "",
        name: "",
        color: "#1E90FF",
        icon: "",
        multiplier: "",
        scope: "",
        applicableLevels: [],
        description: ""
      };
      this.$refs.ruleFormPopup.open();
    },
    // 编辑规则
    editRule(rule) {
      this.isEditing = true;
      this.currentRuleId = rule.id;
      this.ruleForm = JSON.parse(JSON.stringify(rule));
      this.$refs.ruleFormPopup.open();
    },
    // 关闭规则表单弹窗
    closeRuleModal() {
      this.$refs.ruleFormPopup.close();
    },
    // 保存规则表单
    saveRuleForm() {
      if (!this.ruleForm.name) {
        common_vendor.index.showToast({
          title: "请输入规则名称",
          icon: "none"
        });
        return;
      }
      if (!this.ruleForm.multiplier) {
        common_vendor.index.showToast({
          title: "请输入加速倍率",
          icon: "none"
        });
        return;
      }
      if (!this.ruleForm.scope) {
        common_vendor.index.showToast({
          title: "请选择适用范围",
          icon: "none"
        });
        return;
      }
      if (this.ruleForm.applicableLevels.length === 0) {
        common_vendor.index.showToast({
          title: "请选择适用等级",
          icon: "none"
        });
        return;
      }
      if (this.isEditing) {
        const index = this.rules.findIndex((item) => item.id === this.currentRuleId);
        if (index !== -1) {
          this.rules.splice(index, 1, JSON.parse(JSON.stringify(this.ruleForm)));
        }
      } else {
        this.ruleForm.id = Date.now().toString();
        this.rules.push(JSON.parse(JSON.stringify(this.ruleForm)));
      }
      this.closeRuleModal();
      common_vendor.index.showToast({
        title: this.isEditing ? "规则修改成功" : "规则添加成功"
      });
    },
    // 确认删除规则
    confirmDeleteRule(rule) {
      this.currentRuleId = rule.id;
      this.$refs.deleteConfirmPopup.open();
    },
    // 删除规则
    deleteRule() {
      const index = this.rules.findIndex((item) => item.id === this.currentRuleId);
      if (index !== -1) {
        this.rules.splice(index, 1);
      }
      this.$refs.deleteConfirmPopup.close();
      common_vendor.index.showToast({
        title: "规则删除成功"
      });
    },
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    // 选择图标
    chooseIcon() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          this.ruleForm.icon = res.tempFilePaths[0];
        }
      });
    },
    // 选择范围变更
    onScopeChange(e) {
      const index = e.detail.value;
      this.ruleForm.scope = this.ruleScopes[index];
    },
    // 判断等级是否被选中
    isLevelSelected(levelName) {
      return this.ruleForm.applicableLevels.includes(levelName);
    },
    // 切换等级选择
    toggleLevelSelection(levelName) {
      const index = this.ruleForm.applicableLevels.indexOf(levelName);
      if (index === -1) {
        this.ruleForm.applicableLevels.push(levelName);
      } else {
        this.ruleForm.applicableLevels.splice(index, 1);
      }
    }
  }
};
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  const _component_uni_popup_dialog = common_vendor.resolveComponent("uni-popup-dialog");
  (_component_uni_popup + _component_uni_popup_dialog)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.showAddRuleModal && $options.showAddRuleModal(...args)),
    b: $data.rules.length === 0
  }, $data.rules.length === 0 ? {
    c: common_assets._imports_0$46
  } : {
    d: common_vendor.f($data.rules, (rule, index, i0) => {
      return {
        a: common_vendor.t(rule.name),
        b: common_vendor.o(($event) => $options.editRule(rule), index),
        c: common_vendor.o(($event) => $options.confirmDeleteRule(rule), index),
        d: rule.color,
        e: rule.icon,
        f: common_vendor.f(rule.applicableLevels, (level, idx, i1) => {
          return {
            a: common_vendor.t(level),
            b: idx
          };
        }),
        g: common_vendor.t(rule.multiplier),
        h: common_vendor.t(rule.scope),
        i: common_vendor.t(rule.description || "暂无规则说明"),
        j: index
      };
    })
  }, {
    e: common_vendor.t($data.isEditing ? "编辑规则" : "添加规则"),
    f: common_vendor.o((...args) => $options.closeRuleModal && $options.closeRuleModal(...args)),
    g: $data.ruleForm.name,
    h: common_vendor.o(($event) => $data.ruleForm.name = $event.detail.value),
    i: common_vendor.f($data.colorOptions, (color, idx, i0) => {
      return {
        a: idx,
        b: $data.ruleForm.color === color ? 1 : "",
        c: color,
        d: common_vendor.o(($event) => $data.ruleForm.color = color, idx)
      };
    }),
    j: $data.ruleForm.icon
  }, $data.ruleForm.icon ? {
    k: $data.ruleForm.icon
  } : {
    l: common_vendor.o((...args) => $options.chooseIcon && $options.chooseIcon(...args))
  }, {
    m: $data.ruleForm.multiplier,
    n: common_vendor.o(($event) => $data.ruleForm.multiplier = $event.detail.value),
    o: common_vendor.t($data.ruleForm.scope || "请选择适用范围"),
    p: $data.ruleScopes,
    q: common_vendor.o((...args) => $options.onScopeChange && $options.onScopeChange(...args)),
    r: common_vendor.f($data.memberLevels, (level, idx, i0) => {
      return {
        a: common_vendor.t($options.isLevelSelected(level.name) ? "✓" : ""),
        b: common_vendor.t(level.name),
        c: idx,
        d: $options.isLevelSelected(level.name) ? 1 : "",
        e: common_vendor.o(($event) => $options.toggleLevelSelection(level.name), idx)
      };
    }),
    s: $data.ruleForm.description,
    t: common_vendor.o(($event) => $data.ruleForm.description = $event.detail.value),
    v: common_vendor.o((...args) => $options.closeRuleModal && $options.closeRuleModal(...args)),
    w: common_vendor.o((...args) => $options.saveRuleForm && $options.saveRuleForm(...args)),
    x: common_vendor.sr("ruleFormPopup", "390bbf3a-0"),
    y: common_vendor.p({
      type: "center"
    }),
    z: common_vendor.o($options.deleteRule),
    A: common_vendor.o($options.closeDeleteConfirm),
    B: common_vendor.p({
      type: "warning",
      title: "删除确认",
      content: "确定要删除该积分加速规则吗？删除后将无法恢复。",
      ["before-close"]: true
    }),
    C: common_vendor.sr("deleteConfirmPopup", "390bbf3a-1"),
    D: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-390bbf3a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/points-acceleration.js.map
