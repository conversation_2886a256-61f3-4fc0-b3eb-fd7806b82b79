{"version": 3, "file": "withdraw.js", "sources": ["subPackages/payment/pages/withdraw.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGF5bWVudFxwYWdlc1x3aXRoZHJhdy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"withdraw-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">提现</view>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 提现金额区域 -->\r\n    <view class=\"withdraw-section\" :style=\"{ marginTop: (navbarHeight + 10) + 'px' }\">\r\n      <view class=\"section-title\">提现金额</view>\r\n      <view class=\"amount-input-area\">\r\n        <text class=\"currency-symbol\">¥</text>\r\n        <input \r\n          class=\"amount-input\" \r\n          type=\"digit\" \r\n          v-model=\"amount\" \r\n          placeholder=\"0.00\"\r\n          @input=\"handleAmountInput\"\r\n          focus\r\n        />\r\n      </view>\r\n      <view class=\"balance-info\">\r\n        <text>可提现余额: ¥{{ balanceInfo.amount.toFixed(2) }}</text>\r\n        <text class=\"withdraw-all\" @click=\"withdrawAll\">全部提现</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提现方式 -->\r\n    <view class=\"withdraw-method\">\r\n      <view class=\"section-title\">提现方式</view>\r\n      <view class=\"bank-card-section\">\r\n        <view class=\"bank-card\" v-if=\"selectedCard\">\r\n          <view class=\"bank-logo\">\r\n            <image :src=\"selectedCard.bankLogo\" class=\"bank-logo-img\"></image>\r\n          </view>\r\n          <view class=\"bank-info\">\r\n            <view class=\"bank-name\">{{ selectedCard.bankName }}</view>\r\n            <view class=\"card-number\">**** **** **** {{ selectedCard.cardNumberLast4 }}</view>\r\n          </view>\r\n          <view class=\"bank-action\" @click=\"navigateTo('/pages/my/wallet-bank')\">\r\n            <text>更换</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"add-card\" v-else @click=\"navigateTo('/pages/my/wallet-bank')\">\r\n          <image src=\"/static/images/tabbar/添加.png\" class=\"add-icon\"></image>\r\n          <text>添加银行卡</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提现说明 -->\r\n    <view class=\"withdraw-notice\">\r\n      <view class=\"notice-title\">提现说明</view>\r\n      <view class=\"notice-item\">1. 提现金额最低1元，最高5000元</view>\r\n      <view class=\"notice-item\">2. 提现将在1-3个工作日内到账</view>\r\n      <view class=\"notice-item\">3. 提现手续费为金额的0.6%，最低1元</view>\r\n    </view>\r\n    \r\n    <!-- 提现按钮 -->\r\n    <view class=\"bottom-btn-area\">\r\n      <button \r\n        class=\"withdraw-btn\" \r\n        :disabled=\"!canWithdraw\" \r\n        :class=\"{'btn-disabled': !canWithdraw}\"\r\n        @click=\"submitWithdraw\"\r\n      >\r\n        确认提现\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { smartNavigate } from '@/utils/navigation.js';\r\n\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst navbarHeight = ref(64);\r\nconst amount = ref('');\r\nconst balanceInfo = ref({\r\n  amount: 158.50,\r\n  frozenAmount: 0.00\r\n});\r\nconst selectedCard = ref(null);\r\nconst bankCards = ref([]);\r\n\r\n// 计算属性\r\nconst canWithdraw = computed(() => {\r\n  const amountNum = parseFloat(amount.value);\r\n  return amountNum >= 1 && \r\n         amountNum <= 5000 && \r\n         amountNum <= balanceInfo.value.amount &&\r\n         selectedCard.value;\r\n});\r\n\r\n// 方法\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 页面跳转\r\nconst navigateTo = (url) => {\r\n  smartNavigate(url).catch(err => {\r\n    console.error('页面跳转失败:', err);\r\n  });\r\n};\r\n\r\n// 获取钱包余额\r\nconst getWalletBalance = () => {\r\n  // 这里应该是从API获取钱包余额\r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    balanceInfo.value = {\r\n      amount: 158.50,\r\n      frozenAmount: 0.00\r\n    };\r\n  }, 500);\r\n};\r\n\r\n// 获取银行卡列表\r\nconst getBankCards = () => {\r\n  // 模拟API请求获取银行卡列表\r\n  setTimeout(() => {\r\n    bankCards.value = [\r\n      {\r\n        id: 'card001',\r\n        bankName: '中国建设银行',\r\n        bankLogo: '/static/images/banks/ccb.png',\r\n        cardNumber: '6217 0012 3456 7890',\r\n        cardNumberLast4: '7890',\r\n        isDefault: true\r\n      }\r\n    ];\r\n    \r\n    // 设置默认选中的银行卡\r\n    if (bankCards.value.length > 0) {\r\n      selectedCard.value = bankCards.value.find(card => card.isDefault) || bankCards.value[0];\r\n    }\r\n  }, 500);\r\n};\r\n\r\n// 处理金额输入\r\nconst handleAmountInput = (e) => {\r\n  const value = e.detail.value;\r\n  // 限制只能输入两位小数\r\n  if (value.indexOf('.') !== -1) {\r\n    const decimal = value.split('.')[1];\r\n    if (decimal.length > 2) {\r\n      amount.value = parseFloat(value).toFixed(2);\r\n    }\r\n  }\r\n};\r\n\r\n// 全部提现\r\nconst withdrawAll = () => {\r\n  amount.value = balanceInfo.value.amount.toFixed(2);\r\n};\r\n\r\n// 提交提现申请\r\nconst submitWithdraw = () => {\r\n  if (!canWithdraw.value) {\r\n    return;\r\n  }\r\n  \r\n  uni.showLoading({\r\n    title: '提交中...'\r\n  });\r\n  \r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 提示用户提现申请已提交\r\n    uni.showModal({\r\n      title: '提示',\r\n      content: '提现申请已提交，将在1-3个工作日内到账',\r\n      showCancel: false,\r\n      success: () => {\r\n        // 返回钱包页面\r\n        uni.navigateBack();\r\n      }\r\n    });\r\n  }, 1500);\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取状态栏高度\r\n  const sysInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = sysInfo.statusBarHeight;\r\n  navbarHeight.value = statusBarHeight.value + 44;\r\n  \r\n  // 获取钱包余额\r\n  getWalletBalance();\r\n  \r\n  // 获取银行卡列表\r\n  getBankCards();\r\n});\r\n</script>\r\n\r\n<style>\r\n.withdraw-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  z-index: 999;\r\n}\r\n\r\n.navbar-left {\r\n  width: 80rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.navbar-right {\r\n  width: 80rpx;\r\n}\r\n\r\n/* 提现金额区域 */\r\n.withdraw-section {\r\n  background-color: #fff;\r\n  margin: 30rpx;\r\n  padding: 30rpx;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.amount-input-area {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.currency-symbol {\r\n  font-size: 50rpx;\r\n  font-weight: bold;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.amount-input {\r\n  font-size: 60rpx;\r\n  font-weight: bold;\r\n  flex: 1;\r\n}\r\n\r\n.balance-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.withdraw-all {\r\n  color: #0052CC;\r\n}\r\n\r\n/* 提现方式 */\r\n.withdraw-method {\r\n  background-color: #fff;\r\n  margin: 30rpx;\r\n  padding: 30rpx;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.bank-card {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx;\r\n  background-color: #f8f9fc;\r\n  border-radius: 10rpx;\r\n}\r\n\r\n.bank-logo {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  margin-right: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.bank-logo-img {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n}\r\n\r\n.bank-info {\r\n  flex: 1;\r\n}\r\n\r\n.bank-name {\r\n  font-size: 28rpx;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.card-number {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.bank-action {\r\n  font-size: 24rpx;\r\n  color: #0052CC;\r\n}\r\n\r\n.add-card {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 30rpx;\r\n  background-color: #f8f9fc;\r\n  border-radius: 10rpx;\r\n  color: #0052CC;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.add-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n/* 提现说明 */\r\n.withdraw-notice {\r\n  margin: 30rpx;\r\n  padding: 30rpx;\r\n  background-color: #fff;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.notice-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.notice-item {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-bottom: 10rpx;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 底部按钮区域 */\r\n.bottom-btn-area {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 30rpx;\r\n  background-color: #fff;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.withdraw-btn {\r\n  width: 100%;\r\n  height: 90rpx;\r\n  line-height: 90rpx;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n  border-radius: 45rpx;\r\n}\r\n\r\n.btn-disabled {\r\n  background-color: #cccccc;\r\n  color: #ffffff;\r\n}\r\n</style> \r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/payment/pages/withdraw.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "smartNavigate", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;AAiFA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,SAASA,cAAAA,IAAI,EAAE;AACrB,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,QAAQ;AAAA,MACR,cAAc;AAAA,IAChB,CAAC;AACD,UAAM,eAAeA,cAAAA,IAAI,IAAI;AAC7B,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AAGxB,UAAM,cAAcC,cAAQ,SAAC,MAAM;AACjC,YAAM,YAAY,WAAW,OAAO,KAAK;AACzC,aAAO,aAAa,KACb,aAAa,OACb,aAAa,YAAY,MAAM,UAC/B,aAAa;AAAA,IACtB,CAAC;AAID,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BC,uBAAAA,cAAc,GAAG,EAAE,MAAM,SAAO;AAC9BD,sBAAA,MAAA,MAAA,SAAA,iDAAc,WAAW,GAAG;AAAA,MAChC,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAG7B,iBAAW,MAAM;AACf,oBAAY,QAAQ;AAAA,UAClB,QAAQ;AAAA,UACR,cAAc;AAAA,QACpB;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,eAAe,MAAM;AAEzB,iBAAW,MAAM;AACf,kBAAU,QAAQ;AAAA,UAChB;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,iBAAiB;AAAA,YACjB,WAAW;AAAA,UACZ;AAAA,QACP;AAGI,YAAI,UAAU,MAAM,SAAS,GAAG;AAC9B,uBAAa,QAAQ,UAAU,MAAM,KAAK,UAAQ,KAAK,SAAS,KAAK,UAAU,MAAM,CAAC;AAAA,QACvF;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,YAAM,QAAQ,EAAE,OAAO;AAEvB,UAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,cAAM,UAAU,MAAM,MAAM,GAAG,EAAE,CAAC;AAClC,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,QAAQ,WAAW,KAAK,EAAE,QAAQ,CAAC;AAAA,QAC3C;AAAA,MACF;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,aAAO,QAAQ,YAAY,MAAM,OAAO,QAAQ,CAAC;AAAA,IACnD;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,YAAY,OAAO;AACtB;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS,MAAM;AAEbA,0BAAG,MAAC,aAAY;AAAA,UACjB;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGAE,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUF,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAG7C;AAGA;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1MD,GAAG,WAAWG,SAAe;"}