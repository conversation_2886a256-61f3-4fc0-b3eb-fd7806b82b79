<template>
  <view class="profile-container">
    <!-- 头部背景 -->
    <view class="profile-header" :style="{
      height: '300rpx',
      background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
      position: 'relative',
      overflow: 'hidden'
    }">
      <!-- 背景装饰 -->
      <view class="bg-decoration" :style="{
        position: 'absolute',
        top: '-50rpx',
        right: '-50rpx',
        width: '300rpx',
        height: '300rpx',
        borderRadius: '50%',
        background: 'rgba(255,255,255,0.1)',
        zIndex: '1'
      }"></view>
      <view class="bg-decoration" :style="{
        position: 'absolute',
        bottom: '-80rpx',
        left: '-80rpx',
        width: '250rpx',
        height: '250rpx',
        borderRadius: '50%',
        background: 'rgba(255,255,255,0.08)',
        zIndex: '1'
      }"></view>
      
      <!-- 导航栏 -->
      <view class="navbar" :style="{
        position: 'absolute',
        top: '70rpx', /* 从60rpx增加到70rpx，向下移动10单位 */
        left: '0',
        right: '0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 30rpx',
        zIndex: '10'
      }">
        <!-- 返回按钮 -->
        <view class="back-btn" @click="goBack" :style="{
          width: '70rpx',
          height: '70rpx',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }">
          <image src="/static/images/tabbar/最新返回键.png" mode="aspectFit" :style="{
            width: '40rpx',
            height: '40rpx'
          }"></image>
        </view>
        
        <!-- 标题 -->
        <text class="header-title" :style="{
          color: '#FFFFFF',
          fontSize: '36rpx',
          fontWeight: '600'
        }">个人资料</text>
        
        <!-- 保存按钮 -->
        <view class="save-btn" @click="saveProfile" :style="{
          padding: '10rpx 30rpx',
          borderRadius: '30rpx',
          background: 'rgba(255,255,255,0.2)',
          color: '#FFFFFF',
          fontSize: '28rpx',
          fontWeight: '500'
        }">
          保存
        </view>
      </view>
    </view>
    
    <!-- 头像区域 -->
    <view class="avatar-section" :style="{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      marginTop: '-100rpx',
      position: 'relative',
      zIndex: '20'
    }">
      <view class="avatar-wrapper" @click="changeAvatar" :style="{
        width: '180rpx',
        height: '180rpx',
        borderRadius: '50%',
        background: '#FFFFFF',
        padding: '6rpx',
        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
        position: 'relative'
      }">
        <image 
          :src="userInfo.avatar" 
          mode="aspectFill" 
          :style="{
            width: '100%',
            height: '100%',
            borderRadius: '50%'
          }"
        ></image>
        
        <view class="edit-icon" :style="{
          position: 'absolute',
          bottom: '10rpx',
          right: '10rpx',
          width: '50rpx',
          height: '50rpx',
          borderRadius: '50%',
          background: '#FF3B69',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 2px 5px rgba(255,59,105,0.3)',
          border: '2rpx solid #FFFFFF'
        }">
          <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
            <path d="M17 3a2.828 2.828 0 114 4L7.5 20.5 2 22l1.5-5.5L17 3z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <view class="vip-badge" v-if="userInfo.isVip" :style="{
        marginTop: '20rpx',
        padding: '6rpx 20rpx',
        background: 'linear-gradient(90deg, #FFD700 0%, #FFC107 100%)',
        borderRadius: '20rpx',
        color: '#8B4513',
        fontSize: '24rpx',
        fontWeight: 'bold',
        boxShadow: '0 2px 5px rgba(255,215,0,0.3)'
      }">
        VIP会员
      </view>
    </view>
    
    <!-- 表单内容 -->
    <view class="profile-form" :style="{
      padding: '30rpx',
      marginTop: '30rpx'
    }">
      <!-- 基本信息卡片 -->
      <view class="info-card" :style="{
        background: '#FFFFFF',
        borderRadius: '35px',
        padding: '30rpx',
        boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
        marginBottom: '30rpx'
      }">
        <view class="card-header" :style="{
          marginBottom: '30rpx'
        }">
          <text :style="{
            fontSize: '32rpx',
            fontWeight: '600',
            color: '#333333'
          }">基本信息</text>
        </view>
        
        <!-- 昵称 -->
        <view class="form-item" :style="{
          display: 'flex',
          alignItems: 'center',
          padding: '20rpx 0',
          borderBottom: '1rpx solid #EFEFEF'
        }">
          <text class="form-label" :style="{
            width: '160rpx',
            fontSize: '28rpx',
            color: '#666666'
          }">昵称</text>
          <input 
            type="text" 
            v-model="userInfo.nickname" 
            placeholder="请输入昵称" 
            :style="{
              flex: '1',
              fontSize: '28rpx',
              color: '#333333'
            }"
          />
        </view>
        
        <!-- 性别 -->
        <view class="form-item" :style="{
          display: 'flex',
          alignItems: 'center',
          padding: '20rpx 0',
          borderBottom: '1rpx solid #EFEFEF'
        }">
          <text class="form-label" :style="{
            width: '160rpx',
            fontSize: '28rpx',
            color: '#666666'
          }">性别</text>
          <view class="gender-options" :style="{
            flex: '1',
            display: 'flex'
          }">
            <view 
              class="gender-option" 
              :class="{ active: userInfo.gender === 1 }"
              @click="userInfo.gender = 1"
              :style="{
                padding: '10rpx 30rpx',
                borderRadius: '30rpx',
                marginRight: '20rpx',
                background: userInfo.gender === 1 ? 'rgba(255,59,105,0.1)' : '#F8F8F8',
                color: userInfo.gender === 1 ? '#FF3B69' : '#666666',
                fontSize: '26rpx',
                border: userInfo.gender === 1 ? '1rpx solid #FF3B69' : '1rpx solid transparent'
              }"
            >
              男
            </view>
            <view 
              class="gender-option" 
              :class="{ active: userInfo.gender === 2 }"
              @click="userInfo.gender = 2"
              :style="{
                padding: '10rpx 30rpx',
                borderRadius: '30rpx',
                background: userInfo.gender === 2 ? 'rgba(255,59,105,0.1)' : '#F8F8F8',
                color: userInfo.gender === 2 ? '#FF3B69' : '#666666',
                fontSize: '26rpx',
                border: userInfo.gender === 2 ? '1rpx solid #FF3B69' : '1rpx solid transparent'
              }"
            >
              女
            </view>
          </view>
        </view>
        
        <!-- 生日 -->
        <view class="form-item" :style="{
          display: 'flex',
          alignItems: 'center',
          padding: '20rpx 0',
          borderBottom: '1rpx solid #EFEFEF'
        }">
          <text class="form-label" :style="{
            width: '160rpx',
            fontSize: '28rpx',
            color: '#666666'
          }">生日</text>
          <view class="date-picker" @click="showDatePicker" :style="{
            flex: '1',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }">
            <text :style="{
              fontSize: '28rpx',
              color: userInfo.birthday ? '#333333' : '#999999'
            }">{{ userInfo.birthday || '请选择生日' }}</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M6 9l6 6 6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <!-- 手机号 -->
        <view class="form-item" :style="{
          display: 'flex',
          alignItems: 'center',
          padding: '20rpx 0',
          borderBottom: '1rpx solid #EFEFEF'
        }">
          <text class="form-label" :style="{
            width: '160rpx',
            fontSize: '28rpx',
            color: '#666666'
          }">手机号</text>
          <text v-if="userInfo.phone" :style="{
            flex: '1',
            fontSize: '28rpx',
            color: '#333333'
          }">{{ formatPhone(userInfo.phone) }}</text>
          <view 
            v-else 
            class="bind-btn" 
            @click="bindPhone"
            :style="{
              padding: '8rpx 20rpx',
              borderRadius: '30rpx',
              background: 'rgba(255,59,105,0.1)',
              color: '#FF3B69',
              fontSize: '24rpx',
              fontWeight: '500'
            }"
          >
            绑定手机号
          </view>
        </view>
        
        <!-- 所在地区 -->
        <view class="form-item" :style="{
          display: 'flex',
          alignItems: 'center',
          padding: '20rpx 0'
        }">
          <text class="form-label" :style="{
            width: '160rpx',
            fontSize: '28rpx',
            color: '#666666'
          }">所在地区</text>
          <view class="region-picker" @click="showRegionPicker" :style="{
            flex: '1',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }">
            <text :style="{
              fontSize: '28rpx',
              color: userInfo.region ? '#333333' : '#999999'
            }">{{ userInfo.region || '请选择地区' }}</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M6 9l6 6 6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
      
      <!-- 账号安全卡片 -->
      <view class="security-card" :style="{
        background: '#FFFFFF',
        borderRadius: '35px',
        padding: '30rpx',
        boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
        marginBottom: '30rpx'
      }">
        <view class="card-header" :style="{
          marginBottom: '30rpx'
        }">
          <text :style="{
            fontSize: '32rpx',
            fontWeight: '600',
            color: '#333333'
          }">账号安全</text>
        </view>
        
        <!-- 修改密码 -->
        <view class="security-item" @click="changePassword" :style="{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '20rpx 0',
          borderBottom: '1rpx solid #EFEFEF'
        }">
          <text :style="{
            fontSize: '28rpx',
            color: '#333333'
          }">修改密码</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        
        <!-- 实名认证 -->
        <view class="security-item" @click="verifyIdentity" :style="{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '20rpx 0',
          borderBottom: '1rpx solid #EFEFEF'
        }">
          <text :style="{
            fontSize: '28rpx',
            color: '#333333'
          }">实名认证</text>
          <view class="security-status" :style="{
            display: 'flex',
            alignItems: 'center'
          }">
            <text :style="{
              fontSize: '26rpx',
              color: userInfo.isVerified ? '#34C759' : '#999999',
              marginRight: '10rpx'
            }">{{ userInfo.isVerified ? '已认证' : '未认证' }}</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <!-- 绑定微信 -->
        <view class="security-item" @click="bindWeChat" :style="{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '20rpx 0',
          borderBottom: '1rpx solid #EFEFEF'
        }">
          <text :style="{
            fontSize: '28rpx',
            color: '#333333'
          }">绑定微信</text>
          <view class="security-status" :style="{
            display: 'flex',
            alignItems: 'center'
          }">
            <text :style="{
              fontSize: '26rpx',
              color: userInfo.isWeChatBound ? '#34C759' : '#999999',
              marginRight: '10rpx'
            }">{{ userInfo.isWeChatBound ? '已绑定' : '未绑定' }}</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <!-- 注销账号 -->
        <view class="security-item" @click="deleteAccount" :style="{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '20rpx 0'
        }">
          <text :style="{
            fontSize: '28rpx',
            color: '#FF3B30'
          }">注销账号</text>
          <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 18l6-6-6-6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <!-- 隐私设置卡片 -->
      <view class="privacy-card" :style="{
        background: '#FFFFFF',
        borderRadius: '35px',
        padding: '30rpx',
        boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
        marginBottom: '30rpx'
      }">
        <view class="card-header" :style="{
          marginBottom: '30rpx'
        }">
          <text :style="{
            fontSize: '32rpx',
            fontWeight: '600',
            color: '#333333'
          }">隐私设置</text>
        </view>
        
        <!-- 允许陌生人查看资料 -->
        <view class="privacy-item" :style="{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '20rpx 0',
          borderBottom: '1rpx solid #EFEFEF'
        }">
          <text :style="{
            fontSize: '28rpx',
            color: '#333333'
          }">允许陌生人查看资料</text>
          <switch 
            :checked="privacySettings.allowProfileView" 
            @change="e => privacySettings.allowProfileView = e.detail.value"
            color="#FF3B69"
          />
        </view>
        
        <!-- 显示我的活动记录 -->
        <view class="privacy-item" :style="{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '20rpx 0',
          borderBottom: '1rpx solid #EFEFEF'
        }">
          <text :style="{
            fontSize: '28rpx',
            color: '#333333'
          }">显示我的活动记录</text>
          <switch 
            :checked="privacySettings.showActivityHistory" 
            @change="e => privacySettings.showActivityHistory = e.detail.value"
            color="#FF3B69"
          />
        </view>
        
        <!-- 接收活动推送 -->
        <view class="privacy-item" :style="{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '20rpx 0'
        }">
          <text :style="{
            fontSize: '28rpx',
            color: '#333333'
          }">接收活动推送</text>
          <switch 
            :checked="privacySettings.receiveActivityPush" 
            @change="e => privacySettings.receiveActivityPush = e.detail.value"
            color="#FF3B69"
          />
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom" :style="{
      height: '100rpx',
      paddingBottom: 'env(safe-area-inset-bottom)'
    }"></view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

// 用户信息
const userInfo = ref({
  avatar: 'https://via.placeholder.com/180',
  nickname: '张三',
  gender: 1, // 1: 男, 2: 女
  birthday: '1990-01-01',
  phone: '13800138000',
  region: '北京市 海淀区',
  isVip: true,
  isVerified: false,
  isWeChatBound: true
});

// 隐私设置
const privacySettings = ref({
  allowProfileView: true,
  showActivityHistory: true,
  receiveActivityPush: true
});

// 返回上一页
function goBack() {
  uni.navigateBack();
}

// 保存个人资料
function saveProfile() {
  uni.showLoading({
    title: '保存中...'
  });
  
  // 模拟保存过程
  setTimeout(() => {
    uni.hideLoading();
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 保存成功后返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }, 1500);
}

// 更换头像
function changeAvatar() {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePaths = res.tempFilePaths;
      
      // 这里可以添加上传头像的逻辑
      // 模拟上传成功
      userInfo.value.avatar = tempFilePaths[0];
      
      uni.showToast({
        title: '头像已更新',
        icon: 'success'
      });
    }
  });
}

// 显示日期选择器
function showDatePicker() {
  uni.showToast({
    title: '日期选择功能开发中',
    icon: 'none'
  });
  
  // 实际开发中应该使用日期选择器组件
  // uni.showDatePicker({
  //   success: (res) => {
  //     userInfo.value.birthday = res.date;
  //   }
  // });
}

// 显示地区选择器
function showRegionPicker() {
  uni.showToast({
    title: '地区选择功能开发中',
    icon: 'none'
  });
  
  // 实际开发中应该使用地区选择器组件
  // uni.showRegionPicker({
  //   success: (res) => {
  //     userInfo.value.region = res.region;
  //   }
  // });
}

// 绑定手机号
function bindPhone() {
  uni.showToast({
    title: '绑定手机号功能开发中',
    icon: 'none'
  });
}

// 修改密码
function changePassword() {
  uni.showToast({
    title: '修改密码功能开发中',
    icon: 'none'
  });
}

// 实名认证
function verifyIdentity() {
  uni.showToast({
    title: '实名认证功能开发中',
    icon: 'none'
  });
}

// 绑定微信
function bindWeChat() {
  uni.showToast({
    title: '绑定微信功能开发中',
    icon: 'none'
  });
}

// 注销账号
function deleteAccount() {
  uni.showModal({
    title: '注销账号',
    content: '注销账号后，您的所有数据将被清除且无法恢复，确定要注销吗？',
    confirmColor: '#FF3B30',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '注销功能开发中',
          icon: 'none'
        });
      }
    }
  });
}

// 格式化手机号
function formatPhone(phone) {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #F8F8F8;
}

.back-btn:active, .save-btn:active, .gender-option:active, .security-item:active {
  opacity: 0.8;
}
</style> 