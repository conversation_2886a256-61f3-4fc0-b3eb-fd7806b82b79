{"version": 3, "file": "find-service-detail.js", "sources": ["pages/publish/find-service-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9maW5kLXNlcnZpY2UtZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"find-service-container\">\n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <!-- 隐藏的Canvas用于绘制海报 -->\n    <canvas canvas-id=\"posterCanvas\" class=\"poster-canvas\" style=\"width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;\"></canvas>\n    \n    <!-- 悬浮海报按钮 -->\n    <view class=\"float-poster-btn\" @click=\"generateShareImage\">\n      <image src=\"/static/images/tabbar/海报.png\" class=\"poster-icon\"></image>\n      <text class=\"poster-text\">海报</text>\n    </view>\n    \n    <view class=\"find-service-wrapper\">\n      <!-- 需求基本信息卡片 -->\n      <view class=\"content-card service-info-card\">\n        <view class=\"service-header\">\n          <view class=\"service-title-row\">\n            <text class=\"service-title\">{{serviceData.title}}</text>\n            <text class=\"service-budget\">{{serviceData.budget}}</text>\n          </view>\n          <view class=\"service-meta\">\n            <view class=\"service-tag-group\">\n              <view class=\"service-tag\" v-for=\"(tag, index) in serviceData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"service-publish-time\">发布于 {{formatTime(serviceData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <!-- 需求描述 -->\n        <view class=\"service-description\">\n          <text class=\"desc-text\">{{serviceData.description}}</text>\n        </view>\n        \n        <!-- 基本信息 -->\n        <view class=\"service-basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">服务类型</text>\n            <text class=\"info-value\">{{serviceData.type}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">服务区域</text>\n            <text class=\"info-value\">{{serviceData.area}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">服务时间</text>\n            <text class=\"info-value\">{{serviceData.time}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">服务方式</text>\n            <text class=\"info-value\">{{serviceData.method}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 需求详情 -->\n      <view class=\"content-card service-detail-card\">\n        <view class=\"section-title\">需求详情</view>\n        <view class=\"detail-list\">\n          <view class=\"detail-item\" v-for=\"(item, index) in serviceData.details\" :key=\"index\">\n            <text class=\"detail-label\">{{item.label}}</text>\n            <text class=\"detail-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 补充说明 -->\n      <view class=\"content-card service-notes-card\">\n        <view class=\"section-title\">补充说明</view>\n        <view class=\"notes-content\">\n          <rich-text :nodes=\"serviceData.notes\"></rich-text>\n        </view>\n      </view>\n      \n      <!-- 发布者信息 -->\n      <view class=\"content-card publisher-card\">\n        <view class=\"publisher-header\">\n          <view class=\"publisher-avatar\">\n            <image :src=\"serviceData.publisher.avatar\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"publisher-info\">\n            <text class=\"publisher-name\">{{serviceData.publisher.name}}</text>\n            <view class=\"publisher-meta\">\n              <text class=\"publisher-type\">{{serviceData.publisher.type}}</text>\n              <text class=\"publisher-rating\">信用等级 {{serviceData.publisher.rating}}</text>\n            </view>\n          </view>\n          <view class=\"publisher-auth\" v-if=\"serviceData.publisher.isVerified\">\n            <text class=\"iconfont icon-verified\"></text>\n            <text class=\"auth-text\">已认证</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"contact-header\">\n          <text class=\"card-title\">联系方式</text>\n        </view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{serviceData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{serviceData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 相似需求推荐 -->\n      <view class=\"content-card similar-needs-card\">\n        <view class=\"similar-header\">\n          <text class=\"card-title\">相似需求</text>\n        </view>\n        <view class=\"similar-list\">\n          <view class=\"similar-item\" v-for=\"(item, index) in similarServices\" :key=\"index\" @click=\"navigateToService(item.id)\">\n            <view class=\"similar-service-info\">\n              <text class=\"similar-service-title\">{{item.title}}</text>\n              <text class=\"similar-service-budget\">{{item.budget}}</text>\n              <text class=\"similar-service-meta\">{{item.type}} | {{item.area}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst serviceData = ref({\n  id: 'find12345',\n  title: '寻找专业水电维修师傅',\n  budget: '200-500元（视情况而定）',\n  tags: ['水电维修', '急聘', '可议价', '今日内', '有保修'],\n  publishTime: Date.now() - 86400000 * 0.5, // 12小时前\n  description: '家里厨房水管突然漏水，现已临时关闭总阀门。需要专业的水电维修师傅上门检查维修，要求经验丰富，能快速诊断问题并解决。因情况紧急，希望能今天下午或晚上来处理。',\n  type: '水电维修',\n  area: '磁县城区-县政府附近',\n  time: '今天（越快越好）',\n  method: '上门服务',\n  details: [\n    { label: '维修项目', value: '厨房水管漏水' },\n    { label: '具体位置', value: '厨房水槽下方管道' },\n    { label: '漏水情况', value: '渗水明显，已关闭总阀门' },\n    { label: '紧急程度', value: '非常紧急（影响正常用水）' },\n    { label: '服务要求', value: '持证上岗，经验丰富，维修彻底' },\n    { label: '预算说明', value: '基础检查+维修费用，耗材另计' }\n  ],\n  notes: '<p>补充说明：</p><ul><li>需要师傅自带齐全工具，我家没有维修工具</li><li>希望能提供材料发票和维修保修服务</li><li>最好能提前电话联系确认到达时间</li><li>我家是6楼，有电梯</li><li>预算可根据实际情况适当调整，主要求修复彻底不返工</li></ul>',\n  publisher: {\n    name: '张先生',\n    avatar: '/static/images/avatar.png',\n    type: '个人用户',\n    rating: 'A+（优质用户）',\n    isVerified: true\n  },\n  contact: {\n    name: '张先生',\n    phone: '13912345678'\n  }\n});\n\nconst similarServices = ref([\n  {\n    id: 'find001',\n    title: '急寻专业空调维修师傅上门检修',\n    budget: '100-300元',\n    type: '空调维修',\n    area: '磁县城区'\n  },\n  {\n    id: 'find002',\n    title: '找专业小时工打扫新房',\n    budget: '80元/小时',\n    type: '保洁服务',\n    area: '磁县城区'\n  },\n  {\n    id: 'find003',\n    title: '寻找搬家工人帮忙搬家',\n    budget: '300元',\n    type: '搬家服务',\n    area: '磁县城区'\n  },\n  {\n    id: 'find004',\n    title: '找厨师上门做一天席面',\n    budget: '800元/天',\n    type: '厨师服务',\n    area: '磁县城区'\n  }\n]);\n\n// 相关服务推荐数据\nconst relatedServices = ref([]);\n\n// 加载相关服务推荐\nconst loadRelatedServices = () => {\n  // 这里应该调用API获取数据\n  // 实际项目中应该根据当前服务需求的类型、标签等进行相关性匹配\n  \n  // 模拟数据\n  setTimeout(() => {\n    relatedServices.value = [\n      {\n        id: 'service001',\n        title: '专业水电维修师傅',\n        providerName: '老王维修店',\n        providerLogo: '/static/images/tabbar/公司.png',\n        tags: ['水电维修', '上门服务', '5年经验'],\n        price: '上门费50元起'\n      },\n      {\n        id: 'service002',\n        title: '水管安装疏通修理',\n        providerName: '王师傅维修',\n        providerLogo: '/static/images/tabbar/企业.png',\n        tags: ['管道疏通', '安装维修', '快速响应'],\n        price: '定价优惠'\n      },\n      {\n        id: 'service003',\n        title: '全能水电维修服务',\n        providerName: '张师傅维修',\n        providerLogo: '/static/images/tabbar/个人.png',\n        tags: ['专业水电', '随叫随到', '保修一年'],\n        price: '面议'\n      }\n    ];\n  }, 500);\n};\n\n// 跳转到服务详情页\nconst navigateToServiceDetail = (serviceId) => {\n  // 防止跳转到当前页面\n  if (serviceId === serviceData.value.id) {\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/pages/publish/home-service-detail?id=${serviceId}`\n  });\n};\n\n// 跳转到服务需求列表页\nconst navigateToServiceList = (e) => {\n  if (e) e.stopPropagation();\n  const serviceCategory = serviceData.value.type || '';\n  uni.navigateTo({\n    url: `/subPackages/service/pages/filter?type=find-service&title=${encodeURIComponent('服务需求')}&category=${encodeURIComponent(serviceCategory)}&active=find`\n  });\n};\n\n// 海报相关数据\nconst posterImagePath = ref('');\nconst showPosterFlag = ref(false);\n\n// 方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\nconst showShareOptions = () => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: serviceData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\nconst navigateToService = (id) => {\n  uni.navigateTo({\n    url: `/pages/publish/find-service-detail?id=${id}`\n  });\n};\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  if (!serviceData.value.publisher || !serviceData.value.publisher.id) {\n    uni.showToast({\n      title: '无法获取发布者信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/chat/index?userId=${serviceData.value.publisher.id}&username=${encodeURIComponent(serviceData.value.publisher.name || '发布者')}`\n  });\n};\n\n// 加载服务详情数据\nconst loadServiceData = (id) => {\n  // 判断是否是从发布页过来的临时数据\n  if (id && id.startsWith('temp_')) {\n    // 从本地缓存中获取数据\n    const publishDataList = uni.getStorageSync('publishDataList') || [];\n    const publishData = publishDataList.find(item => item.id === id);\n    \n    if (publishData) {\n      // 更新服务数据\n      updateServiceDataFromPublish(publishData);\n    } else {\n      uni.showToast({\n        title: '数据加载失败',\n        icon: 'none'\n      });\n    }\n  } else {\n    // 从服务器获取数据（这里使用模拟数据）\n    console.log('获取服务ID:', id);\n    // 保持默认的静态数据\n  }\n};\n\n// 根据发布数据更新服务详情\nconst updateServiceDataFromPublish = (publishData) => {\n  // 更新基本信息\n  serviceData.value = {\n    ...serviceData.value,\n    id: publishData.id,\n    title: publishData.title || '寻找服务',\n    budget: publishData.budget || '预算待定',\n    publishTime: publishData.publishTime || Date.now(),\n    description: publishData.demandDescription || publishData.description || serviceData.value.description,\n    type: publishData.serviceType || '服务类型',\n    area: publishData.serviceArea || '磁县城区',\n    time: publishData.urgency === '非常紧急' ? '今天（越快越好）' : '近期',\n    method: '上门服务'\n  };\n  \n  // 更新联系信息\n  if (publishData.contact && publishData.phone) {\n    serviceData.value.contact = {\n      name: publishData.contact,\n      phone: publishData.phone\n    };\n    \n    // 同步更新发布者信息\n    serviceData.value.publisher = {\n      name: publishData.contact,\n      avatar: '/static/images/avatar.png',\n      type: '个人用户',\n      rating: 'A（普通用户）',\n      isVerified: true\n    };\n  }\n  \n  // 更新标签\n  if (publishData.requirements && publishData.requirements.length > 0) {\n    serviceData.value.tags = publishData.requirements;\n  } else if (publishData.tags && publishData.tags.length > 0) {\n    serviceData.value.tags = publishData.tags;\n  }\n  \n  // 更新详情数据\n  const newDetails = [];\n  \n  // 根据表单数据构建详情项\n  if (publishData.serviceType) {\n    newDetails.push({ label: '服务类型', value: publishData.serviceType });\n  }\n  \n  if (publishData.serviceArea) {\n    newDetails.push({ label: '服务区域', value: publishData.serviceArea });\n  }\n  \n  if (publishData.urgency) {\n    newDetails.push({ label: '紧急程度', value: publishData.urgency });\n  }\n  \n  if (publishData.budget) {\n    newDetails.push({ label: '预算', value: publishData.budget });\n  }\n  \n  // 如果有图片，添加图片说明\n  if (publishData.images && publishData.images.length > 0) {\n    newDetails.push({ label: '相关图片', value: `已上传${publishData.images.length}张图片` });\n  }\n  \n  // 更新详情列表\n  if (newDetails.length > 0) {\n    serviceData.value.details = newDetails;\n  }\n  \n  // 构建补充说明HTML\n  if (publishData.demandDescription) {\n    serviceData.value.notes = `<p>需求详情：</p><p>${publishData.demandDescription}</p>`;\n  }\n};\n\n// 生成海报的方法\nconst generateShareImage = () => {\n  uni.showLoading({\n    title: '正在生成海报...',\n    mask: true\n  });\n  \n  // 创建海报数据对象\n  const posterData = {\n    title: serviceData.value.title,\n    budget: serviceData.value.budget,\n    type: serviceData.value.type,\n    address: serviceData.value.area,\n    phone: serviceData.value.publisher ? serviceData.value.publisher.phone : '',\n    description: serviceData.value.description ? serviceData.value.description.substring(0, 60) + '...' : '',\n    qrcode: '/static/images/tabbar/客服微信.png',\n    logo: '/static/images/tabbar/商家入驻.png',\n    bgImage: '/static/images/banner/banner-1.png'\n  };\n  \n  // #ifdef H5\n  // H5环境不支持canvas绘制图片保存，提示用户\n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showModal({\n      title: '提示',\n      content: 'H5环境暂不支持保存海报，请使用App或小程序',\n      showCancel: false\n    });\n  }, 1000);\n  return;\n  // #endif\n  \n  // 绘制海报\n  const ctx = uni.createCanvasContext('posterCanvas');\n  \n  // 绘制背景\n  ctx.save();\n  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);\n  // 添加半透明蒙层\n  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');\n  ctx.fillRect(0, 0, 600, 900);\n  ctx.restore();\n  \n  // 绘制白色卡片背景\n  ctx.save();\n  ctx.setFillStyle('#ffffff');\n  ctx.fillRect(30, 280, 540, 550);\n  ctx.restore();\n  \n  // 绘制Logo\n  ctx.save();\n  ctx.beginPath();\n  ctx.arc(300, 200, 80, 0, 2 * Math.PI);\n  ctx.setFillStyle('#ffffff');\n  ctx.fill();\n  // 在圆形内绘制Logo\n  ctx.clip();\n  ctx.drawImage(posterData.logo, 220, 120, 160, 160);\n  ctx.restore();\n  \n  // 绘制标题\n  ctx.setFillStyle('#333333');\n  ctx.setFontSize(32);\n  ctx.setTextAlign('center');\n  ctx.fillText(posterData.title, 300, 350);\n  \n  // 绘制预算\n  ctx.setFillStyle('#FF6B6B');\n  ctx.setFontSize(28);\n  ctx.fillText(posterData.budget, 300, 400);\n  \n  // 分割线\n  ctx.beginPath();\n  ctx.setStrokeStyle('#eeeeee');\n  ctx.setLineWidth(2);\n  ctx.moveTo(100, 430);\n  ctx.lineTo(500, 430);\n  ctx.stroke();\n  \n  // 绘制服务类型\n  ctx.setFillStyle('#666666');\n  ctx.setFontSize(24);\n  ctx.setTextAlign('left');\n  ctx.fillText('服务类型: ' + posterData.type, 80, 480);\n  \n  // 绘制服务区域\n  ctx.fillText('服务区域: ' + posterData.address, 80, 520);\n  \n  // A wrap text function\n  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {\n    if (text.length === 0) return;\n    \n    const words = text.split('');\n    let line = '';\n    let testLine = '';\n    let lineCount = 0;\n    \n    for (let n = 0; n < words.length; n++) {\n      testLine += words[n];\n      const metrics = ctx.measureText(testLine);\n      const testWidth = metrics.width;\n      \n      if (testWidth > maxWidth && n > 0) {\n        ctx.fillText(line, x, y + (lineCount * lineHeight));\n        line = words[n];\n        testLine = words[n];\n        lineCount++;\n        \n        if (lineCount >= 3) {\n          line += '...';\n          ctx.fillText(line, x, y + (lineCount * lineHeight));\n          break;\n        }\n      } else {\n        line = testLine;\n      }\n    }\n    \n    if (lineCount < 3) {\n      ctx.fillText(line, x, y + (lineCount * lineHeight));\n    }\n  };\n  \n  // 绘制简介\n  ctx.setFillStyle('#666666');\n  wrapText(ctx, posterData.description, 80, 560, 440, 35);\n  \n  // 绘制电话\n  if (posterData.phone) {\n    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);\n  }\n  \n  // 绘制小程序码\n  ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);\n  \n  // 提示文字\n  ctx.setFillStyle('#999999');\n  ctx.setFontSize(20);\n  ctx.setTextAlign('center');\n  ctx.fillText('长按识别二维码查看详情', 300, 880);\n  \n  // 应用平台Logo\n  ctx.setFillStyle('#333333');\n  ctx.setFontSize(24);\n  ctx.fillText('磁县同城 - 生活服务', 300, 840);\n  \n  // 绘制完成，输出图片\n  ctx.draw(false, () => {\n    setTimeout(() => {\n      // 延迟确保canvas已完成渲染\n      uni.canvasToTempFilePath({\n        canvasId: 'posterCanvas',\n        success: (res) => {\n          uni.hideLoading();\n          showPosterModal(res.tempFilePath);\n        },\n        fail: (err) => {\n          console.error('生成海报失败', err);\n          uni.hideLoading();\n          uni.showToast({\n            title: '生成海报失败',\n            icon: 'none'\n          });\n        }\n      });\n    }, 800);\n  });\n};\n\n// 显示海报预览和保存选项\nconst showPosterModal = (posterPath) => {\n  posterImagePath.value = posterPath;\n  showPosterFlag.value = true;\n  \n  uni.showModal({\n    title: '海报已生成',\n    content: '海报已生成，是否保存到相册？',\n    confirmText: '保存',\n    success: (res) => {\n      if (res.confirm) {\n        savePosterToAlbum(posterPath);\n      } else {\n        // 预览图片\n        uni.previewImage({\n          urls: [posterPath],\n          current: posterPath\n        });\n      }\n    }\n  });\n};\n\n// 保存海报到相册\nconst savePosterToAlbum = (posterPath) => {\n  uni.showLoading({\n    title: '正在保存...'\n  });\n  \n  uni.saveImageToPhotosAlbum({\n    filePath: posterPath,\n    success: () => {\n      uni.hideLoading();\n      uni.showToast({\n        title: '已保存到相册',\n        icon: 'success'\n      });\n    },\n    fail: (err) => {\n      uni.hideLoading();\n      console.error('保存失败', err);\n      \n      if (err.errMsg.indexOf('auth deny') > -1) {\n        uni.showModal({\n          title: '提示',\n          content: '保存失败，请授权相册权限后重试',\n          confirmText: '去设置',\n          success: (res) => {\n            if (res.confirm) {\n              uni.openSetting();\n            }\n          }\n        });\n      } else {\n        uni.showToast({\n          title: '保存失败',\n          icon: 'none'\n        });\n      }\n    }\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 修改页面标题\n  uni.setNavigationBarTitle({\n    title: '需求详情'\n  });\n  uni.setNavigationBarColor({\n    frontColor: '#000000',\n    backgroundColor: '#ffffff'\n  });\n  \n  // 获取路由参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 获取服务ID\n  if (options.id) {\n    loadServiceData(options.id);\n  }\n  \n  // 加载相关服务推荐\n  loadRelatedServices();\n  \n  // 如果是从发布页面跳转过来的，自动触发分享\n  if (options.fromPublish) {\n    setTimeout(() => {\n      // 自动触发分享按钮点击\n      const shareButton = document.getElementById('shareButton');\n      if (shareButton) {\n        shareButton.click();\n      }\n    }, 1500);\n  }\n});\n</script>\n\n<style>\n.find-service-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 110rpx;\n  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */\n}\n\n.find-service-wrapper {\n  padding: 24rpx;\n}\n\n.content-card {\n  margin-bottom: 24rpx;\n  border-radius: 24rpx;\n  background-color: #fff;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 需求基本信息卡片 */\n.service-title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.service-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.service-budget {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff4d4f;\n}\n\n.service-meta {\n  margin-bottom: 24rpx;\n}\n\n.service-tag-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 12rpx;\n}\n\n.service-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.service-publish-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 需求描述 */\n.service-description {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  margin-bottom: 24rpx;\n}\n\n.desc-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 基本信息 */\n.service-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.info-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 需求详情 */\n.detail-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.detail-item:last-child {\n  border-bottom: none;\n}\n\n.detail-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.detail-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 补充说明 */\n.notes-content {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  white-space: pre-wrap;\n}\n\n/* 发布者信息 */\n.publisher-header {\n  display: flex;\n  align-items: center;\n}\n\n.publisher-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.publisher-avatar image {\n  width: 100%;\n  height: 100%;\n}\n\n.publisher-info {\n  flex: 1;\n}\n\n.publisher-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.publisher-meta {\n  display: flex;\n  align-items: center;\n}\n\n.publisher-type, .publisher-rating {\n  font-size: 24rpx;\n  color: #666;\n  margin-right: 16rpx;\n}\n\n/* 相似需求 */\n.similar-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.similar-item {\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.similar-service-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.similar-service-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.similar-service-budget {\n  font-size: 28rpx;\n  color: #ff4d4f;\n  margin-bottom: 8rpx;\n}\n\n.similar-service-meta {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 底部互动工具栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  padding: 10rpx 10rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  height: 120rpx;\n  z-index: 100;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 0;\n  margin: 0 4rpx;\n}\n\n.toolbar-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.share-button {\n  background: transparent;\n  border: none;\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  border-radius: 0;\n  flex: 1;\n}\n\n.share-button::after {\n  display: none;\n}\n\n.call-button {\n  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */\n  background: linear-gradient(135deg, #0052CC, #0066FF);\n  height: 90rpx;\n  margin: 0 0 0 10rpx;\n  border-radius: 45rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.call-text {\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.call-subtitle {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 20rpx;\n  line-height: 1.2;\n}\n\n/* 隐藏原来的底部操作栏 */\n.action-bar {\n  display: none;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: absolute;\n  top: -9999rpx;\n  left: -9999rpx;\n  width: 0;\n  height: 0;\n  padding: 0;\n  margin: 0;\n  opacity: 0;\n}\n\n/* 悬浮海报按钮 */\n.float-poster-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 200rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: rgba(240, 240, 240, 0.9);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);\n  z-index: 90;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1rpx solid rgba(230, 230, 230, 0.6);\n  transition: all 0.2s ease;\n}\n\n.float-poster-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);\n}\n\n.poster-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 4rpx;\n}\n\n.poster-text {\n  font-size: 20rpx;\n  color: #444;\n  line-height: 1;\n}\n\n/* 相关服务推荐样式 */\n.related-services-card {\n  margin-top: 12px;\n  background-color: #fff;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n}\n\n.related-services-content {\n  padding: 0 16px 16px;\n  overflow: hidden;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 16px;\n  position: relative;\n  padding-left: 10px;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 4px;\n  width: 3px;\n  height: 16px;\n  background-color: #0052CC;\n  border-radius: 3px;\n}\n\n/* 相关服务列表样式 */\n.related-services-list {\n  margin-bottom: 12px;\n}\n\n.related-service-item {\n  padding: 12px 0;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.related-service-item:last-child {\n  border-bottom: none;\n}\n\n.service-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.service-item-left {\n  margin-right: 12px;\n}\n\n.provider-logo {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  background-color: #f5f7fa;\n}\n\n.service-item-middle {\n  flex: 1;\n}\n\n.service-item-title {\n  font-size: 15px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.service-item-provider {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.service-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.service-item-tag {\n  font-size: 12px;\n  color: #666;\n  background-color: #f5f7fa;\n  padding: 2px 6px;\n  border-radius: 4px;\n  margin-right: 6px;\n  margin-bottom: 4px;\n}\n\n.service-item-tag-more {\n  font-size: 12px;\n  color: #999;\n  padding: 2px 0;\n}\n\n.service-item-right {\n  text-align: right;\n}\n\n.service-item-price {\n  font-size: 15px;\n  color: #ff4d4f;\n  font-weight: 500;\n}\n\n/* 查看更多按钮 */\n.view-more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 44px;\n  background-color: #f7f9fc;\n  border-radius: 8px;\n  margin-top: 8px;\n}\n\n.view-more-text {\n  font-size: 14px;\n  color: #0052CC;\n}\n\n.view-more-icon {\n  margin-left: 4px;\n  font-size: 12px;\n  color: #0052CC;\n}\n\n/* 空数据提示 */\n.empty-related-services {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 24px 0;\n}\n\n.empty-image {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 12px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/find-service-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "ctx", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AAqKA,UAAM,aAAa,CAAC,cAAc;AAChC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IACjD;AAGA,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAC7B,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM,OAAO,OAAO,KAAK;AAAA,MACxC,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,MACrC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,QAClC,EAAE,OAAO,QAAQ,OAAO,WAAY;AAAA,QACpC,EAAE,OAAO,QAAQ,OAAO,cAAe;AAAA,QACvC,EAAE,OAAO,QAAQ,OAAO,eAAgB;AAAA,QACxC,EAAE,OAAO,QAAQ,OAAO,iBAAkB;AAAA,QAC1C,EAAE,OAAO,QAAQ,OAAO,iBAAkB;AAAA,MAC3C;AAAA,MACD,OAAO;AAAA,MACP,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,MACD,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAG9B,UAAM,sBAAsB,MAAM;AAKhC,iBAAW,MAAM;AACf,wBAAgB,QAAQ;AAAA,UACtB;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,cAAc;AAAA,YACd,cAAc;AAAA,YACd,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,YAC7B,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,cAAc;AAAA,YACd,cAAc;AAAA,YACd,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,YAC7B,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,cAAc;AAAA,YACd,cAAc;AAAA,YACd,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,YAC7B,OAAO;AAAA,UACR;AAAA,QACP;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAwBA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAGhC,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjC,UAAI,YAAY,OAAO;AACrBC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AASA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,YAAY,MAAM,QAAQ;AAAA,QACvC,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,oBAAoB,CAAC,OAAO;AAChCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yCAAyC,EAAE;AAAA,MACpD,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,YAAY,MAAM,aAAa,CAAC,YAAY,MAAM,UAAU,IAAI;AACnEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,YAAY,MAAM,UAAU,EAAE,aAAa,mBAAmB,YAAY,MAAM,UAAU,QAAQ,KAAK,CAAC;AAAA,MAC7I,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,OAAO;AAE9B,UAAI,MAAM,GAAG,WAAW,OAAO,GAAG;AAEhC,cAAM,kBAAkBA,cAAG,MAAC,eAAe,iBAAiB,KAAK,CAAA;AACjE,cAAM,cAAc,gBAAgB,KAAK,UAAQ,KAAK,OAAO,EAAE;AAE/D,YAAI,aAAa;AAEf,uCAA6B,WAAW;AAAA,QAC9C,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,OAAS;AAELA,yFAAY,WAAW,EAAE;AAAA,MAE1B;AAAA,IACH;AAGA,UAAM,+BAA+B,CAAC,gBAAgB;AAEpD,kBAAY,QAAQ;AAAA,QAClB,GAAG,YAAY;AAAA,QACf,IAAI,YAAY;AAAA,QAChB,OAAO,YAAY,SAAS;AAAA,QAC5B,QAAQ,YAAY,UAAU;AAAA,QAC9B,aAAa,YAAY,eAAe,KAAK,IAAK;AAAA,QAClD,aAAa,YAAY,qBAAqB,YAAY,eAAe,YAAY,MAAM;AAAA,QAC3F,MAAM,YAAY,eAAe;AAAA,QACjC,MAAM,YAAY,eAAe;AAAA,QACjC,MAAM,YAAY,YAAY,SAAS,aAAa;AAAA,QACpD,QAAQ;AAAA,MACZ;AAGE,UAAI,YAAY,WAAW,YAAY,OAAO;AAC5C,oBAAY,MAAM,UAAU;AAAA,UAC1B,MAAM,YAAY;AAAA,UAClB,OAAO,YAAY;AAAA,QACzB;AAGI,oBAAY,MAAM,YAAY;AAAA,UAC5B,MAAM,YAAY;AAAA,UAClB,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,QAClB;AAAA,MACG;AAGD,UAAI,YAAY,gBAAgB,YAAY,aAAa,SAAS,GAAG;AACnE,oBAAY,MAAM,OAAO,YAAY;AAAA,MACzC,WAAa,YAAY,QAAQ,YAAY,KAAK,SAAS,GAAG;AAC1D,oBAAY,MAAM,OAAO,YAAY;AAAA,MACtC;AAGD,YAAM,aAAa,CAAA;AAGnB,UAAI,YAAY,aAAa;AAC3B,mBAAW,KAAK,EAAE,OAAO,QAAQ,OAAO,YAAY,YAAW,CAAE;AAAA,MAClE;AAED,UAAI,YAAY,aAAa;AAC3B,mBAAW,KAAK,EAAE,OAAO,QAAQ,OAAO,YAAY,YAAW,CAAE;AAAA,MAClE;AAED,UAAI,YAAY,SAAS;AACvB,mBAAW,KAAK,EAAE,OAAO,QAAQ,OAAO,YAAY,QAAO,CAAE;AAAA,MAC9D;AAED,UAAI,YAAY,QAAQ;AACtB,mBAAW,KAAK,EAAE,OAAO,MAAM,OAAO,YAAY,OAAM,CAAE;AAAA,MAC3D;AAGD,UAAI,YAAY,UAAU,YAAY,OAAO,SAAS,GAAG;AACvD,mBAAW,KAAK,EAAE,OAAO,QAAQ,OAAO,MAAM,YAAY,OAAO,MAAM,MAAO,CAAA;AAAA,MAC/E;AAGD,UAAI,WAAW,SAAS,GAAG;AACzB,oBAAY,MAAM,UAAU;AAAA,MAC7B;AAGD,UAAI,YAAY,mBAAmB;AACjC,oBAAY,MAAM,QAAQ,kBAAkB,YAAY,iBAAiB;AAAA,MAC1E;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAGD,YAAM,aAAa;AAAA,QACjB,OAAO,YAAY,MAAM;AAAA,QACzB,QAAQ,YAAY,MAAM;AAAA,QAC1B,MAAM,YAAY,MAAM;AAAA,QACxB,SAAS,YAAY,MAAM;AAAA,QAC3B,OAAO,YAAY,MAAM,YAAY,YAAY,MAAM,UAAU,QAAQ;AAAA,QACzE,aAAa,YAAY,MAAM,cAAc,YAAY,MAAM,YAAY,UAAU,GAAG,EAAE,IAAI,QAAQ;AAAA,QACtG,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,MACb;AAgBE,YAAM,MAAMA,cAAAA,MAAI,oBAAoB,cAAc;AAGlD,UAAI,KAAI;AACR,UAAI,UAAU,WAAW,SAAS,GAAG,GAAG,KAAK,GAAG;AAEhD,UAAI,aAAa,qBAAqB;AACtC,UAAI,SAAS,GAAG,GAAG,KAAK,GAAG;AAC3B,UAAI,QAAO;AAGX,UAAI,KAAI;AACR,UAAI,aAAa,SAAS;AAC1B,UAAI,SAAS,IAAI,KAAK,KAAK,GAAG;AAC9B,UAAI,QAAO;AAGX,UAAI,KAAI;AACR,UAAI,UAAS;AACb,UAAI,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE;AACpC,UAAI,aAAa,SAAS;AAC1B,UAAI,KAAI;AAER,UAAI,KAAI;AACR,UAAI,UAAU,WAAW,MAAM,KAAK,KAAK,KAAK,GAAG;AACjD,UAAI,QAAO;AAGX,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,aAAa,QAAQ;AACzB,UAAI,SAAS,WAAW,OAAO,KAAK,GAAG;AAGvC,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,SAAS,WAAW,QAAQ,KAAK,GAAG;AAGxC,UAAI,UAAS;AACb,UAAI,eAAe,SAAS;AAC5B,UAAI,aAAa,CAAC;AAClB,UAAI,OAAO,KAAK,GAAG;AACnB,UAAI,OAAO,KAAK,GAAG;AACnB,UAAI,OAAM;AAGV,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,aAAa,MAAM;AACvB,UAAI,SAAS,WAAW,WAAW,MAAM,IAAI,GAAG;AAGhD,UAAI,SAAS,WAAW,WAAW,SAAS,IAAI,GAAG;AAGnD,YAAM,WAAW,CAACC,MAAK,MAAM,GAAG,GAAG,UAAU,eAAe;AAC1D,YAAI,KAAK,WAAW;AAAG;AAEvB,cAAM,QAAQ,KAAK,MAAM,EAAE;AAC3B,YAAI,OAAO;AACX,YAAI,WAAW;AACf,YAAI,YAAY;AAEhB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,MAAM,CAAC;AACnB,gBAAM,UAAUA,KAAI,YAAY,QAAQ;AACxC,gBAAM,YAAY,QAAQ;AAE1B,cAAI,YAAY,YAAY,IAAI,GAAG;AACjC,YAAAA,KAAI,SAAS,MAAM,GAAG,IAAK,YAAY,UAAW;AAClD,mBAAO,MAAM,CAAC;AACd,uBAAW,MAAM,CAAC;AAClB;AAEA,gBAAI,aAAa,GAAG;AAClB,sBAAQ;AACR,cAAAA,KAAI,SAAS,MAAM,GAAG,IAAK,YAAY,UAAW;AAClD;AAAA,YACD;AAAA,UACT,OAAa;AACL,mBAAO;AAAA,UACR;AAAA,QACF;AAED,YAAI,YAAY,GAAG;AACjB,UAAAA,KAAI,SAAS,MAAM,GAAG,IAAK,YAAY,UAAW;AAAA,QACnD;AAAA,MACL;AAGE,UAAI,aAAa,SAAS;AAC1B,eAAS,KAAK,WAAW,aAAa,IAAI,KAAK,KAAK,EAAE;AAGtD,UAAI,WAAW,OAAO;AACpB,YAAI,SAAS,WAAW,WAAW,OAAO,IAAI,GAAG;AAAA,MAClD;AAGD,UAAI,UAAU,WAAW,QAAQ,KAAK,KAAK,KAAK,GAAG;AAGnD,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,aAAa,QAAQ;AACzB,UAAI,SAAS,eAAe,KAAK,GAAG;AAGpC,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,SAAS,eAAe,KAAK,GAAG;AAGpC,UAAI,KAAK,OAAO,MAAM;AACpB,mBAAW,MAAM;AAEfD,wBAAAA,MAAI,qBAAqB;AAAA,YACvB,UAAU;AAAA,YACV,SAAS,CAAC,QAAQ;AAChBA,4BAAG,MAAC,YAAW;AACf,8BAAgB,IAAI,YAAY;AAAA,YACjC;AAAA,YACD,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,gDAAc,UAAU,GAAG;AAC3BA,4BAAG,MAAC,YAAW;AACfA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF,GAAE,GAAG;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,eAAe;AACtC,sBAAgB,QAAQ;AACxB,qBAAe,QAAQ;AAEvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,8BAAkB,UAAU;AAAA,UACpC,OAAa;AAELA,0BAAAA,MAAI,aAAa;AAAA,cACf,MAAM,CAAC,UAAU;AAAA,cACjB,SAAS;AAAA,YACnB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,eAAe;AACxCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAEDA,oBAAAA,MAAI,uBAAuB;AAAA,QACzB,UAAU;AAAA,QACV,SAAS,MAAM;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAA,MAAA,MAAA,SAAA,gDAAc,QAAQ,GAAG;AAEzB,cAAI,IAAI,OAAO,QAAQ,WAAW,IAAI,IAAI;AACxCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,aAAa;AAAA,cACb,SAAS,CAAC,QAAQ;AAChB,oBAAI,IAAI,SAAS;AACfA,gCAAG,MAAC,YAAW;AAAA,gBAChB;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACT,OAAa;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGAE,kBAAAA,UAAU,MAAM;AAEdF,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,OAAO;AAAA,MACX,CAAG;AACDA,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,YAAY;AAAA,QACZ,iBAAiB;AAAA,MACrB,CAAG;AAGD,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,UAAI,QAAQ,IAAI;AACd,wBAAgB,QAAQ,EAAE;AAAA,MAC3B;AAGD;AAGA,UAAI,QAAQ,aAAa;AACvB,mBAAW,MAAM;AAEf,gBAAM,cAAc,SAAS,eAAe,aAAa;AACzD,cAAI,aAAa;AACf,wBAAY,MAAK;AAAA,UAClB;AAAA,QACF,GAAE,IAAI;AAAA,MACR;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrtBD,GAAG,WAAWG,SAAe;"}