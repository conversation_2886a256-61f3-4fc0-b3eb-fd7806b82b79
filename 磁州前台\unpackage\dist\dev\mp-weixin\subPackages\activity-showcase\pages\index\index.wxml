<view class="activity-showcase-container data-v-91893c28"><view class="content-bg-scroll data-v-91893c28"></view><view class="custom-navbar data-v-91893c28"><view class="navbar-content data-v-91893c28"><view class="back-btn data-v-91893c28" bindtap="{{b}}"><image class="back-icon data-v-91893c28" src="{{a}}" mode="aspectFit"></image></view><view class="navbar-title data-v-91893c28">{{c}}</view><view class="navbar-right data-v-91893c28"><view class="close-btn data-v-91893c28" bindtap="{{d}}"><text class="close-icon data-v-91893c28"></text></view></view></view></view><scroll-view class="content-scroll data-v-91893c28" scroll-y bindscrolltolower="{{o}}" scroll-anchoring="{{true}}" enhanced="{{true}}" bounces="{{true}}" show-scrollbar="{{false}}"><view class="banner-outer data-v-91893c28"><swiper class="banner-swiper data-v-91893c28" indicator-dots autoplay="{{f}}" circular="{{g}}" indicator-color="{{h}}" indicator-active-color="{{i}}" interval="{{j}}" duration="{{k}}"><swiper-item wx:for="{{e}}" wx:for-item="banner" wx:key="d" class="data-v-91893c28"><view class="banner-item data-v-91893c28"><image class="banner-image data-v-91893c28" src="{{banner.a}}" mode="aspectFill" lazy-load="{{true}}"></image><view class="banner-info data-v-91893c28"><text class="banner-title data-v-91893c28">{{banner.b}}</text><text class="banner-desc data-v-91893c28">{{banner.c}}</text></view></view></swiper-item></swiper></view><view class="white-content data-v-91893c28"><view class="category-nav data-v-91893c28"><view wx:for="{{l}}" wx:for-item="category" wx:key="x" class="category-item data-v-91893c28" bindtap="{{category.y}}"><view class="{{['category-icon', 'data-v-91893c28', category.v]}}"><svg wx:if="{{category.a}}" u-s="{{['d']}}" class="icon data-v-91893c28" u-i="{{category.d}}" bind:__l="__l" u-p="{{category.e}}"><path wx:if="{{category.c}}" class="data-v-91893c28" u-i="{{category.b}}" bind:__l="__l" u-p="{{category.c}}"></path></svg><svg wx:elif="{{category.f}}" u-s="{{['d']}}" class="icon data-v-91893c28" u-i="{{category.i}}" bind:__l="__l" u-p="{{category.j}}"><path wx:if="{{category.h}}" class="data-v-91893c28" u-i="{{category.g}}" bind:__l="__l" u-p="{{category.h}}"></path></svg><svg wx:elif="{{category.k}}" u-s="{{['d']}}" class="icon data-v-91893c28" u-i="{{category.n}}" bind:__l="__l" u-p="{{category.o}}"><path wx:if="{{category.m}}" class="data-v-91893c28" u-i="{{category.l}}" bind:__l="__l" u-p="{{category.m}}"></path></svg><svg wx:elif="{{category.p}}" u-s="{{['d']}}" class="icon data-v-91893c28" u-i="{{category.s}}" bind:__l="__l" u-p="{{category.t}}"><path wx:if="{{category.r}}" class="data-v-91893c28" u-i="{{category.q}}" bind:__l="__l" u-p="{{category.r}}"></path></svg></view><text class="category-name data-v-91893c28">{{category.w}}</text></view></view><activity-center class="data-v-91893c28" u-i="91893c28-8" bind:__l="__l"/><view wx:if="{{m}}" class="loading-more data-v-91893c28"><text class="data-v-91893c28">加载中...</text></view><view wx:if="{{n}}" class="no-more data-v-91893c28"><text class="data-v-91893c28">已经到底啦~</text></view><view class="safe-area-bottom data-v-91893c28"></view></view></scroll-view><view class="tabbar data-v-91893c28"><view class="{{['tabbar-item', 'data-v-91893c28', p && 'active']}}" bindtap="{{q}}" data-tab="home"><view class="tab-icon home data-v-91893c28"></view><text class="tabbar-text data-v-91893c28">首页</text></view><view class="{{['tabbar-item', 'data-v-91893c28', r && 'active']}}" bindtap="{{s}}" data-tab="discover"><view class="tab-icon discover data-v-91893c28"></view><text class="tabbar-text data-v-91893c28">本地商城</text></view><view class="{{['tabbar-item', 'data-v-91893c28', t && 'active']}}" bindtap="{{v}}" data-tab="distribution"><view class="tab-icon distribution data-v-91893c28"></view><text class="tabbar-text data-v-91893c28">分销</text></view><view class="{{['tabbar-item', 'data-v-91893c28', y && 'active']}}" bindtap="{{z}}" data-tab="message"><view class="tab-icon message data-v-91893c28"><view wx:if="{{w}}" class="badge data-v-91893c28">{{x}}</view></view><text class="tabbar-text data-v-91893c28">消息</text></view><view class="{{['tabbar-item', 'data-v-91893c28', A && 'active']}}" bindtap="{{B}}" data-tab="my"><view class="tab-icon user data-v-91893c28"></view><text class="tabbar-text data-v-91893c28">我的</text></view></view></view>