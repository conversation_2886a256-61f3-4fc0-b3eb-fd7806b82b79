
.merchant-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 40rpx;
  height: 100vh;
  overflow-y: auto;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20px;
  z-index: 999;
  box-shadow: 0 2px 10px rgba(0, 122, 255, 0.2);
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}
.setting-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 商家信息卡片 */
.merchant-card {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  transform: translateZ(0);
}
.merchant-header {
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
}
.merchant-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  background-color: #f5f7fa;
  margin-right: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.merchant-info {
  flex: 1;
}
.merchant-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}
.merchant-status {
  display: flex;
  align-items: center;
}
.status-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.status-text {
  font-size: 24rpx;
  color: #ff9800;
}
.verified .status-text {
  color: #34C759;
}
.merchant-action {
  margin-left: 30rpx;
}
.verify-btn {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
  color: #fff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  height: 60rpx;
  line-height: 40rpx;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 94, 58, 0.2);
}
.edit-btn {
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: #fff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  height: 60rpx;
  line-height: 40rpx;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 商家数据统计 */
.merchant-stats {
  display: flex;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  background: rgba(248, 249, 250, 0.5);
}
.stat-item {
  flex: 1;
  text-align: center;
  padding: 10rpx 0;
}
.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}
.stat-label {
  font-size: 24rpx;
  color: #999;
}
.stat-divider {
  width: 2rpx;
  background-color: #f0f0f0;
}

/* 核心业务模块 */
.core-business-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}
.title-wrapper {
  display: flex;
  align-items: center;
}
.title-indicator {
  width: 6rpx;
  height: 30rpx;
  border-radius: 3rpx;
  margin-right: 15rpx;
}
.title-indicator-core {
  background: linear-gradient(to bottom, #FF9500, #FF5E3A);
}
.section-subtitle {
  font-size: 26rpx;
  color: #999;
  margin-top: 10rpx;
}
.core-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}
.core-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  transition: all 0.3s;
}
.core-item:active {
  transform: scale(0.95);
}
.core-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}
.bg-publish {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}
.bg-manage {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}
.bg-goods {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
}
.bg-reviews {
  background: linear-gradient(135deg, #5AC8FA, #34C759);
}
.core-icon {
  width: 50rpx;
  height: 50rpx;
}
.core-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 快捷工具 */
.tools-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}
.title-indicator-tools {
  background: linear-gradient(to bottom, #34C759, #30B3FF);
}
.tools-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}
.tool-item {
  position: relative;
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  transition: all 0.3s;
}
.tool-item:active {
  transform: scale(0.95);
}
.tool-card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}
.tool-card-icon image {
  width: 40rpx;
  height: 40rpx;
}
.bg-qrcode {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}
.bg-poster {
  background: linear-gradient(135deg, #FFCC00, #FF9500);
}
.bg-analysis {
  background: linear-gradient(135deg, #5856D6, #AF52DE);
}
.bg-staff {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}
.bg-settings {
  background: linear-gradient(135deg, #8E8E93, #636366);
}
.tool-card-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
}
.tool-card-tag {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 20rpx;
  color: #fff;
  padding: 2rpx 10rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
  z-index: 1;
}

/* 运营专区 */
.operation-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}
.section-title-large {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 24rpx;
}
.section-title-large::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 34rpx;
  background: linear-gradient(to bottom, #007AFF, #5856D6);
  border-radius: 4rpx;
}
.operation-cards {
  margin-top: 30rpx;
}
.operation-card {
  position: relative;
  width: 100%;
  height: 240rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  transform: translateZ(0);
  transition: all 0.3s;
}
.operation-card:active {
  transform: scale(0.98);
}
.operation-card-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.operation-card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
}
.operation-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
.operation-card-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
.operation-card-btn {
  display: inline-block;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.25);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s;
}

/* 行业专属功能 */
.industry-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}
.industry-features {
  margin-top: 30rpx;
}
.industry-card {
  position: relative;
  width: 100%;
  height: 240rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  transform: translateZ(0);
  transition: all 0.3s;
}
.industry-card:active {
  transform: scale(0.98);
}
.industry-card-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.industry-card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
}
.industry-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
.industry-card-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 新增背景色定义 */
.bg-publish {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}
.bg-manage {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}
.bg-visitor {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}
.bg-leads {
  background: linear-gradient(135deg, #5AC8FA, #34C759);
}
.bg-category {
  background: linear-gradient(135deg, #AF52DE, #5856D6);
}
.title-indicator-marketing {
  background: linear-gradient(to bottom, #FF9500, #FF5E3A);
}
.title-indicator-analysis {
  background: linear-gradient(to bottom, #5856D6, #AF52DE);
}

/* 营销推广样式 */
.marketing-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}
.marketing-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}
.marketing-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  transition: all 0.3s;
}
.marketing-item:active {
  transform: scale(0.95);
}
.marketing-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}
.marketing-icon {
  width: 50rpx;
  height: 50rpx;
}
.marketing-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 经营分析样式 */
.analysis-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}
.analysis-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}
.analysis-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  transition: all 0.3s;
}
.analysis-item:active {
  transform: scale(0.95);
}
.analysis-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}
.analysis-icon {
  width: 50rpx;
  height: 50rpx;
}
.analysis-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.bg-product {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
}
.bg-marketing {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}
.bg-finance {
  background: linear-gradient(135deg, #5856D6, #AF52DE);
}
.bg-coupon {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}
.bg-redpacket {
  background: linear-gradient(135deg, #FF3B30, #FF5E3A);
}
.bg-reviews {
  background: linear-gradient(135deg, #5AC8FA, #34C759);
}

/* 待处理事项 */
.pending-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.section-more {
  font-size: 26rpx;
  color: #007AFF;
}
.pending-list {
  background-color: #fff;
}
.pending-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.3s;
}
.pending-item:active {
  background-color: #f8f9fa;
  transform: scale(0.98);
}
.pending-item:last-child {
  border-bottom: none;
}
.pending-left {
  margin-right: 20rpx;
}
.pending-badge {
  display: inline-block;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  text-align: center;
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.badge-order {
  background: linear-gradient(135deg, #FF3B30, #FF9500);
}
.badge-comment {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}
.badge-finance {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}
.badge-approval {
  background: linear-gradient(135deg, #5856D6, #AF52DE);
}
.badge-service {
  background: linear-gradient(135deg, #34C759, #30B3FF);
}
.badge-info {
  background: linear-gradient(135deg, #FF9500, #FFCC00);
}
.pending-center {
  flex: 1;
}
.pending-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}
.pending-desc {
  font-size: 24rpx;
  color: #999;
}
.pending-right {
  margin-left: 20rpx;
}
.action-btn {
  display: inline-block;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #007AFF;
  border: 1rpx solid #007AFF;
  background-color: rgba(0, 122, 255, 0.05);
  transition: all 0.3s;
}
.action-btn:active {
  transform: scale(0.95);
  background-color: rgba(0, 122, 255, 0.1);
}

/* 空状态 */
.empty-view {
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}
.empty-text {
  margin-top: 10px;
  font-size: 14px;
  color: #999;
}

/* 推广模块 */
.promotion-section {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  border: 1px solid #F5F5F5;
}
.favorite-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 60rpx;
  padding: 0 20rpx;
  background-color: #FFFFFF;
  justify-content: flex-start;
}
.favorite-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}
.favorite-icon {
  width: 36rpx;
  height: 36rpx;
}
.favorite-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 400;
}
.bg-orders {
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
}
.bg-verification {
  background: linear-gradient(135deg, #9040FF, #6C2FF2);
}
.bg-customer {
  background: linear-gradient(135deg, #5AC8FA, #34C759);
}
