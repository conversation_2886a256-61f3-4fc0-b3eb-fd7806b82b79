# pseudomap

A thing that is a lot like ES6 `Map`, but without iterators, for use
in environments where `for..of` syntax and `Map` are not available.

If you need iterators, or just in general a more faithful polyfill to
ES6 Maps, check out [es6-map](http://npm.im/es6-map).

If you are in an environment where `Map` is supported, then that will
be returned instead, unless `process.env.TEST_PSEUDOMAP` is set.

You can use any value as keys, and any value as data.  Setting again
with the identical key will overwrite the previous value.

Internally, data is stored on an `Object.create(null)` style object.
The key is coerced to a string to generate the key on the internal
data-bag object.  The original key used is stored along with the data.

In the event of a stringified-key collision, a new key is generated by
appending an increasing number to the stringified-key until finding
either the intended key or an empty spot.

Note that because object traversal order of plain objects is not
guaranteed to be identical to insertion order, the insertion order
guarantee of `Map.prototype.forEach` is not guaranteed in this
implementation.  However, in all versions of Node.js and V8 where this
module works, `forEach` does traverse data in insertion order.

## API

Most of the [Map
API](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map),
with the following exceptions:

1. A `Map` object is not an iterator.
2. `values`, `keys`, and `entries` methods are not implemented,
   because they return iterators.
3. The argument to the constructor can be an Array of `[key, value]`
   pairs, or a `Map` or `PseudoMap` object.  But, since iterators
   aren't used, passing any plain-old iterator won't initialize the
   map properly.

## USAGE

Use just like a regular ES6 Map.

```javascript
var PseudoMap = require('pseudomap')

// optionally provide a pseudomap, or an array of [key,value] pairs
// as the argument to initialize the map with
var myMap = new PseudoMap()

myMap.set(1, 'number 1')
myMap.set('1', 'string 1')
var akey = {}
var bkey = {}
myMap.set(akey, { some: 'data' })
myMap.set(bkey, { some: 'other data' })
```
