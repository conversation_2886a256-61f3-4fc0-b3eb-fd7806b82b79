{"version": 3, "file": "verify-success.js", "sources": ["pages/business/verify-success.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYnVzaW5lc3MvdmVyaWZ5LXN1Y2Nlc3MudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"success-container\">\r\n    <!-- 顶部背景渐变 -->\r\n    <view class=\"top-gradient\"></view>\r\n    \r\n    <!-- 状态栏占位 -->\r\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n    \r\n    <!-- 导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">认证申请</view>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content\">\r\n      <!-- 成功动画 -->\r\n      <view class=\"success-animation\">\r\n        <view class=\"success-circle\">\r\n          <image src=\"/static/images/tabbar/成功.png\" class=\"success-icon\"></image>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 成功信息 -->\r\n      <view class=\"success-info\">\r\n        <view class=\"success-title\">认证申请已提交</view>\r\n        <view class=\"success-subtitle\">感谢您申请商家认证</view>\r\n      </view>\r\n      \r\n      <!-- 进度信息 -->\r\n      <view class=\"progress-card\">\r\n        <view class=\"progress-header\">\r\n          <text class=\"progress-title\">认证进度</text>\r\n          <text class=\"progress-status\">审核中</text>\r\n        </view>\r\n        \r\n        <view class=\"progress-steps\">\r\n          <view class=\"step-item completed\">\r\n            <view class=\"step-dot\"></view>\r\n            <view class=\"step-line\"></view>\r\n            <view class=\"step-content\">\r\n              <text class=\"step-name\">提交申请</text>\r\n              <text class=\"step-time\">{{applyTime}}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"step-item current\">\r\n            <view class=\"step-dot\"></view>\r\n            <view class=\"step-line\"></view>\r\n            <view class=\"step-content\">\r\n              <text class=\"step-name\">资料审核</text>\r\n              <text class=\"step-desc\">预计1-3个工作日</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"step-item\">\r\n            <view class=\"step-dot\"></view>\r\n            <view class=\"step-content\">\r\n              <text class=\"step-name\">认证完成</text>\r\n              <text class=\"step-desc\">审核通过后即可获得认证标识</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 提示信息 -->\r\n      <view class=\"tips-card\">\r\n        <view class=\"tips-header\">\r\n          <image src=\"/static/images/tabbar/提示.png\" class=\"tips-icon\"></image>\r\n          <text class=\"tips-title\">温馨提示</text>\r\n        </view>\r\n        <view class=\"tips-content\">\r\n          <text class=\"tips-text\">1. 您可以在\"我的-商家中心-认证管理\"查看认证进度</text>\r\n          <text class=\"tips-text\">2. 审核结果将通过短信通知您，请保持手机畅通</text>\r\n          <text class=\"tips-text\">3. 如有疑问，可联系平台客服咨询</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 操作按钮 -->\r\n      <view class=\"action-buttons\">\r\n        <button class=\"primary-btn\" @click=\"goToMerchantCenter\">查看认证进度</button>\r\n        <button class=\"secondary-btn\" @click=\"goToHome\">返回首页</button>\r\n      </view>\r\n      \r\n      <!-- 联系客服 -->\r\n      <view class=\"contact-section\">\r\n        <view class=\"contact-text\">遇到问题？联系客服获取帮助</view>\r\n        <button class=\"contact-btn\" @click=\"contactCustomerService\">联系客服</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\n\r\nconst statusBarHeight = ref(20);\r\nconst applyTime = ref('刚刚');\r\n\r\nonLoad(() => {\r\n  // 获取状态栏高度\r\n  const sysInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = sysInfo.statusBarHeight;\r\n  \r\n  // 获取认证申请数据\r\n  loadApplyData();\r\n});\r\n\r\nconst goBack = () => {\r\n  uni.switchTab({\r\n    url: '/pages/index/index'\r\n  });\r\n};\r\n\r\nconst loadApplyData = () => {\r\n  // 从本地获取认证申请数据\r\n  const verifyApplyData = uni.getStorageSync('verifyApplyData');\r\n  if (verifyApplyData && verifyApplyData.applyTime) {\r\n    applyTime.value = verifyApplyData.applyTime;\r\n  }\r\n};\r\n\r\nconst goToMerchantCenter = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/my/merchant?tab=verify'\r\n  });\r\n};\r\n\r\nconst goToHome = () => {\r\n  uni.switchTab({\r\n    url: '/pages/index/index'\r\n  });\r\n};\r\n\r\nconst contactCustomerService = () => {\r\n  uni.makePhoneCall({\r\n    phoneNumber: '************',\r\n    fail: () => {\r\n      uni.showToast({\r\n        title: '拨打客服电话失败',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  });\r\n};\r\n</script>\r\n\r\n<style>\r\n.success-container {\r\n  min-height: 100vh;\r\n  background-color: #f8f9fc;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n/* 顶部渐变背景 */\r\n.top-gradient {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 240px;\r\n  background: linear-gradient(135deg, #0046B3, #1677FF);\r\n  z-index: 0;\r\n}\r\n\r\n/* 导航栏 */\r\n.navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 20px;\r\n  position: relative;\r\n  z-index: 1;\r\n  width: 100%;\r\n}\r\n\r\n.navbar-left {\r\n  width: 44px;\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #fff;\r\n}\r\n\r\n.navbar-right {\r\n  width: 44px;\r\n}\r\n\r\n/* 内容区域 */\r\n.content {\r\n  position: relative;\r\n  z-index: 1;\r\n  padding: 20px;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n/* 成功动画 */\r\n.success-animation {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.success-circle {\r\n  width: 80px;\r\n  height: 80px;\r\n  background-color: #fff;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 10px 20px rgba(0, 82, 204, 0.1);\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(0, 82, 204, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 15px rgba(0, 82, 204, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(0, 82, 204, 0);\r\n  }\r\n}\r\n\r\n.success-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n}\r\n\r\n/* 成功信息 */\r\n.success-info {\r\n  margin-top: 16px;\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n  width: 100%;\r\n}\r\n\r\n.success-title {\r\n  font-size: 22px;\r\n  font-weight: bold;\r\n  color: #fff;\r\n  margin-bottom: 8px;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.success-subtitle {\r\n  font-size: 16px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* 进度卡片 */\r\n.progress-card {\r\n  background-color: #fff;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.progress-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.progress-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.progress-status {\r\n  font-size: 14px;\r\n  color: #1677FF;\r\n  background-color: rgba(22, 119, 255, 0.1);\r\n  padding: 4px 12px;\r\n  border-radius: 20px;\r\n}\r\n\r\n.progress-steps {\r\n  padding-left: 8px;\r\n}\r\n\r\n.step-item {\r\n  position: relative;\r\n  padding-left: 20px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.step-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.step-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 6px;\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  background-color: #e8e8e8;\r\n  z-index: 1;\r\n}\r\n\r\n.step-line {\r\n  position: absolute;\r\n  left: 5px;\r\n  top: 18px;\r\n  width: 2px;\r\n  height: calc(100% + 20px);\r\n  background-color: #e8e8e8;\r\n}\r\n\r\n.step-item:last-child .step-line {\r\n  display: none;\r\n}\r\n\r\n.step-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.step-name {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.step-time, .step-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 完成状态 */\r\n.step-item.completed .step-dot {\r\n  background-color: #52C41A;\r\n}\r\n\r\n.step-item.completed .step-line {\r\n  background-color: #52C41A;\r\n}\r\n\r\n.step-item.completed .step-name {\r\n  color: #52C41A;\r\n}\r\n\r\n/* 当前状态 */\r\n.step-item.current .step-dot {\r\n  background-color: #1677FF;\r\n  box-shadow: 0 0 0 4px rgba(22, 119, 255, 0.2);\r\n}\r\n\r\n.step-item.current .step-name {\r\n  color: #1677FF;\r\n}\r\n\r\n/* 提示卡片 */\r\n.tips-card {\r\n  background-color: #fff;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.tips-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.tips-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.tips-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.tips-content {\r\n  background-color: #f8f9fc;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.tips-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.8;\r\n  display: block;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  margin-top: 30px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n}\r\n\r\n.primary-btn {\r\n  height: 48px;\r\n  background: linear-gradient(135deg, #0052CC, #2684FF);\r\n  color: #fff;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  border-radius: 24px;\r\n  margin-bottom: 12px;\r\n  box-shadow: 0 6px 12px rgba(0, 82, 204, 0.2);\r\n}\r\n\r\n.secondary-btn {\r\n  height: 48px;\r\n  background-color: #fff;\r\n  color: #0052CC;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  border-radius: 24px;\r\n  border: 1px solid #0052CC;\r\n}\r\n\r\n/* 联系客服 */\r\n.contact-section {\r\n  margin-top: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.contact-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 12px;\r\n  text-align: center;\r\n}\r\n\r\n.contact-btn {\r\n  width: 140px;\r\n  height: 36px;\r\n  line-height: 36px;\r\n  background-color: #f5f5f5;\r\n  color: #666;\r\n  font-size: 14px;\r\n  border-radius: 18px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/business/verify-success.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAoGA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAE1BC,kBAAAA,OAAO,MAAM;AAEX,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAGhC;IACF,CAAC;AAED,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEA,UAAM,gBAAgB,MAAM;AAE1B,YAAM,kBAAkBA,cAAAA,MAAI,eAAe,iBAAiB;AAC5D,UAAI,mBAAmB,gBAAgB,WAAW;AAChD,kBAAU,QAAQ,gBAAgB;AAAA,MACnC;AAAA,IACH;AAEA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEA,UAAM,yBAAyB,MAAM;AACnCA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa;AAAA,QACb,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;ACnJA,GAAG,WAAWC,SAAe;"}