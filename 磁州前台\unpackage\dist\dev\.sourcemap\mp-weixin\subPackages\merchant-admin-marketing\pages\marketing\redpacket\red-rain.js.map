{"version": 3, "file": "red-rain.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/red-rain.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRccmVkLXJhaW4udnVl"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">红包雨</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 数据概览 -->\n    <view class=\"overview-section\">\n      <view class=\"overview-header\">\n        <text class=\"section-title\">红包雨数据概览</text>\n        <view class=\"date-picker\" @click=\"showDatePicker\">\n          <text class=\"date-text\">{{dateRange}}</text>\n          <view class=\"date-icon\"></view>\n        </view>\n      </view>\n      \n      <view class=\"stats-cards\">\n        <view class=\"stats-card\">\n          <text class=\"card-value\">{{rainData.totalCount}}</text>\n          <text class=\"card-label\">红包雨活动数</text>\n          <view class=\"card-trend\" :class=\"rainData.countTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text>{{rainData.countGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <text class=\"card-value\">{{rainData.totalUsers}}</text>\n          <text class=\"card-label\">参与用户数</text>\n          <view class=\"card-trend\" :class=\"rainData.usersTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text>{{rainData.usersGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <text class=\"card-value\">{{formatNumber(rainData.totalAmount)}}</text>\n          <text class=\"card-label\">发放金额(元)</text>\n          <view class=\"card-trend\" :class=\"rainData.amountTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text>{{rainData.amountGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <text class=\"card-value\">{{rainData.conversionRate}}%</text>\n          <text class=\"card-label\">领取转化率</text>\n          <view class=\"card-trend\" :class=\"rainData.conversionTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text>{{rainData.conversionGrowth}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 红包雨列表 -->\n    <view class=\"rain-list-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">红包雨活动</text>\n        <view class=\"add-btn\" @click=\"createRain\">\n          <text class=\"btn-text\">新建活动</text>\n          <view class=\"plus-icon-small\"></view>\n        </view>\n      </view>\n      \n      <view class=\"tab-header\">\n        <view class=\"tab-item\" \n              v-for=\"(tab, index) in tabList\" \n              :key=\"index\" \n              :class=\"{ active: currentTab === index }\" \n              @click=\"switchTab(index)\">\n          <text class=\"tab-text\">{{tab}}</text>\n        </view>\n      </view>\n      \n      <view class=\"rain-list\">\n        <view class=\"rain-item\" v-for=\"(item, index) in filteredRainList\" :key=\"index\">\n          <view class=\"rain-header\">\n            <text class=\"rain-title\">{{item.title}}</text>\n            <text class=\"rain-status\" :class=\"'status-' + item.status\">{{item.statusText}}</text>\n          </view>\n          \n          <view class=\"rain-content\">\n            <view class=\"rain-icon\"></view>\n            <view class=\"rain-info\">\n              <text class=\"rain-time\">{{item.timeRange}}</text>\n              <view class=\"rain-amount\">\n                <text class=\"amount-label\">红包金额: </text>\n                <text class=\"amount-value\">{{item.minAmount}}元 - {{item.maxAmount}}元</text>\n              </view>\n              <view class=\"rain-duration\">\n                <text class=\"duration-label\">持续时间: </text>\n                <text class=\"duration-value\">{{item.duration}}秒</text>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"rain-stats\">\n            <view class=\"stat-row\">\n              <text class=\"stat-label\">参与人数:</text>\n              <text class=\"stat-value\">{{item.participantCount}}人</text>\n            </view>\n            <view class=\"stat-row\">\n              <text class=\"stat-label\">发放红包:</text>\n              <text class=\"stat-value\">{{item.sentCount}}个</text>\n            </view>\n            <view class=\"stat-row\">\n              <text class=\"stat-label\">领取红包:</text>\n              <text class=\"stat-value\">{{item.receivedCount}}个</text>\n            </view>\n          </view>\n          \n          <view class=\"rain-actions\">\n            <view class=\"action-btn\" @click.stop=\"viewRainDetail(item)\">详情</view>\n            <view class=\"action-btn\" @click.stop=\"editRain(item)\" v-if=\"item.status === 'draft'\">编辑</view>\n            <view class=\"action-btn\" @click.stop=\"shareRain(item)\" v-if=\"item.status === 'active' || item.status === 'upcoming'\">分享</view>\n            <view class=\"action-btn delete\" @click.stop=\"deleteRain(item)\" v-if=\"item.status === 'draft' || item.status === 'ended'\">删除</view>\n          </view>\n        </view>\n        \n        <view class=\"empty-state\" v-if=\"filteredRainList.length === 0\">\n          <view class=\"empty-icon\"></view>\n          <text class=\"empty-text\">暂无{{tabList[currentTab]}}红包雨活动</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 红包雨设置指南 -->\n    <view class=\"guide-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">红包雨设置指南</text>\n      </view>\n      \n      <view class=\"guide-steps\">\n        <view class=\"guide-step\" v-for=\"(step, index) in guideSteps\" :key=\"index\">\n          <view class=\"step-number\">{{index + 1}}</view>\n          <view class=\"step-content\">\n            <text class=\"step-title\">{{step.title}}</text>\n            <text class=\"step-desc\">{{step.description}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 红包雨效果预览 -->\n    <view class=\"preview-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">效果预览</text>\n      </view>\n      \n      <view class=\"preview-container\">\n        <image class=\"preview-image\" src=\"/static/images/redpacket/rain-preview.png\" mode=\"widthFix\"></image>\n        <view class=\"preview-btn\" @click=\"showPreview\">查看动态效果</view>\n      </view>\n    </view>\n    \n    <!-- 浮动操作按钮 -->\n    <view class=\"floating-action-button\" @click=\"createRain\">\n      <view class=\"fab-icon\">+</view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      dateRange: '2023-04-01 ~ 2023-04-30',\n      currentTab: 0,\n      tabList: ['全部', '进行中', '未开始', '已结束', '草稿'],\n      \n      // 红包雨数据概览\n      rainData: {\n        totalCount: 18,\n        countTrend: 'up',\n        countGrowth: '22.5%',\n        \n        totalUsers: 8752,\n        usersTrend: 'up',\n        usersGrowth: '35.7%',\n        \n        totalAmount: 12680.50,\n        amountTrend: 'up',\n        amountGrowth: '28.2%',\n        \n        conversionRate: 45.6,\n        conversionTrend: 'up',\n        conversionGrowth: '7.8%'\n      },\n      \n      // 红包雨活动列表\n      rainList: [\n        {\n          id: 1,\n          title: '周年庆红包雨',\n          status: 'active',\n          statusText: '进行中',\n          timeRange: '2023-04-15 ~ 2023-04-20',\n          minAmount: 1.00,\n          maxAmount: 50.00,\n          duration: 60,\n          participantCount: 3562,\n          sentCount: 15820,\n          receivedCount: 12453\n        },\n        {\n          id: 2,\n          title: '五一节日红包雨',\n          status: 'upcoming',\n          statusText: '未开始',\n          timeRange: '2023-05-01 ~ 2023-05-07',\n          minAmount: 2.00,\n          maxAmount: 88.00,\n          duration: 90,\n          participantCount: 0,\n          sentCount: 0,\n          receivedCount: 0\n        },\n        {\n          id: 3,\n          title: '新品发布红包雨',\n          status: 'draft',\n          statusText: '草稿',\n          timeRange: '未设置',\n          minAmount: 1.00,\n          maxAmount: 20.00,\n          duration: 45,\n          participantCount: 0,\n          sentCount: 0,\n          receivedCount: 0\n        },\n        {\n          id: 4,\n          title: '春节红包雨',\n          status: 'ended',\n          statusText: '已结束',\n          timeRange: '2023-01-20 ~ 2023-02-05',\n          minAmount: 1.00,\n          maxAmount: 100.00,\n          duration: 120,\n          participantCount: 5189,\n          sentCount: 25680,\n          receivedCount: 22547\n        }\n      ],\n      \n      // 红包雨设置指南\n      guideSteps: [\n        {\n          title: '设置活动时间',\n          description: '选择红包雨活动的开始和结束时间，可设置多场次'\n        },\n        {\n          title: '设置红包参数',\n          description: '配置红包金额范围、数量、持续时间等参数'\n        },\n        {\n          title: '设置触发条件',\n          description: '可选择自动触发或用户手动触发红包雨'\n        },\n        {\n          title: '设置活动页面',\n          description: '自定义红包雨活动页面的背景、文案等元素'\n        },\n        {\n          title: '发布活动',\n          description: '预览效果无误后发布活动，可通过多种渠道分享'\n        }\n      ]\n    }\n  },\n  computed: {\n    filteredRainList() {\n      if (this.currentTab === 0) {\n        return this.rainList;\n      } else {\n        const statusMap = {\n          1: 'active',\n          2: 'upcoming',\n          3: 'ended',\n          4: 'draft'\n        };\n        return this.rainList.filter(item => item.status === statusMap[this.currentTab]);\n      }\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showHelp() {\n      uni.showModal({\n        title: '红包雨帮助',\n        content: '红包雨是一种互动营销活动，在指定时间内向用户随机发放红包，提高用户活跃度和参与感。',\n        showCancel: false\n      });\n    },\n    showDatePicker() {\n      // 显示日期选择器\n      uni.showToast({\n        title: '日期选择功能开发中',\n        icon: 'none'\n      });\n    },\n    formatNumber(num) {\n      return num.toFixed(2);\n    },\n    switchTab(index) {\n      this.currentTab = index;\n    },\n    createRain() {\n      uni.showToast({\n        title: '创建红包雨功能开发中',\n        icon: 'none'\n      });\n    },\n    viewRainDetail(item) {\n      uni.showToast({\n        title: '查看详情功能开发中',\n        icon: 'none'\n      });\n    },\n    editRain(item) {\n      uni.showToast({\n        title: '编辑功能开发中',\n        icon: 'none'\n      });\n    },\n    shareRain(item) {\n      uni.showToast({\n        title: '分享功能开发中',\n        icon: 'none'\n      });\n    },\n    deleteRain(item) {\n      uni.showModal({\n        title: '删除确认',\n        content: `确定要删除\"${item.title}\"吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            uni.showToast({\n              title: '删除成功',\n              icon: 'success'\n            });\n          }\n        }\n      });\n    },\n    showPreview() {\n      uni.showToast({\n        title: '预览功能开发中',\n        icon: 'none'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.page-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 数据概览样式 */\n.overview-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.overview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.date-picker {\n  display: flex;\n  align-items: center;\n  background-color: #F5F7FA;\n  padding: 5px 10px;\n  border-radius: 15px;\n}\n\n.date-text {\n  font-size: 12px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.date-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 1px solid #666;\n  border-bottom: 1px solid #666;\n  transform: rotate(-45deg);\n}\n\n.stats-cards {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n\n.stats-card {\n  width: 48%;\n  background: linear-gradient(135deg, #FFF, #F5F7FA);\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 10px;\n  position: relative;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n}\n\n.card-value {\n  font-size: 22px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.card-label {\n  font-size: 12px;\n  color: #666;\n}\n\n.card-trend {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  display: flex;\n  align-items: center;\n  font-size: 12px;\n}\n\n.card-trend.up {\n  color: #FF5858;\n}\n\n.card-trend.down {\n  color: #2ED573;\n}\n\n.trend-arrow {\n  width: 8px;\n  height: 8px;\n  border-left: 1px solid currentColor;\n  border-top: 1px solid currentColor;\n  margin-right: 2px;\n}\n\n.up .trend-arrow {\n  transform: rotate(45deg);\n}\n\n.down .trend-arrow {\n  transform: rotate(-135deg);\n}\n\n/* 红包雨列表样式 */\n.rain-list-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.add-btn {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  padding: 6px 12px;\n  border-radius: 15px;\n  color: #fff;\n}\n\n.btn-text {\n  font-size: 12px;\n  margin-right: 5px;\n}\n\n.plus-icon-small {\n  width: 12px;\n  height: 12px;\n  position: relative;\n}\n\n.plus-icon-small::before,\n.plus-icon-small::after {\n  content: '';\n  position: absolute;\n  background-color: #fff;\n}\n\n.plus-icon-small::before {\n  width: 12px;\n  height: 2px;\n  top: 5px;\n  left: 0;\n}\n\n.plus-icon-small::after {\n  width: 2px;\n  height: 12px;\n  top: 0;\n  left: 5px;\n}\n\n.tab-header {\n  display: flex;\n  border-bottom: 1px solid #eee;\n  margin-bottom: 15px;\n}\n\n.tab-item {\n  flex: 1;\n  text-align: center;\n  padding: 10px 0;\n  position: relative;\n}\n\n.tab-text {\n  font-size: 14px;\n  color: #666;\n}\n\n.tab-item.active .tab-text {\n  color: #FF5858;\n  font-weight: 600;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: -1px;\n  left: 25%;\n  width: 50%;\n  height: 2px;\n  background-color: #FF5858;\n  border-radius: 1px;\n}\n\n.rain-list {\n  margin-top: 10px;\n}\n\n.rain-item {\n  background-color: #fff;\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n  border: 1px solid #eee;\n}\n\n.rain-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.rain-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.rain-status {\n  font-size: 12px;\n  padding: 3px 8px;\n  border-radius: 10px;\n}\n\n.status-active {\n  background-color: #E8F5E9;\n  color: #388E3C;\n}\n\n.status-upcoming {\n  background-color: #E3F2FD;\n  color: #1976D2;\n}\n\n.status-ended {\n  background-color: #EEEEEE;\n  color: #757575;\n}\n\n.status-draft {\n  background-color: #FFF3E0;\n  color: #E65100;\n}\n\n.rain-content {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.rain-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  background: linear-gradient(135deg, #FFD166, #FF9A8B);\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.rain-icon::before {\n  content: '雨';\n  color: #fff;\n  font-weight: bold;\n  font-size: 16px;\n}\n\n.rain-info {\n  flex: 1;\n}\n\n.rain-time {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.rain-amount,\n.rain-duration {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 5px;\n  display: flex;\n}\n\n.amount-label,\n.duration-label {\n  color: #999;\n}\n\n.amount-value,\n.duration-value {\n  color: #333;\n}\n\n.rain-stats {\n  background-color: #F9F9F9;\n  border-radius: 8px;\n  padding: 10px;\n  margin-bottom: 10px;\n}\n\n.stat-row {\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n  margin-bottom: 5px;\n}\n\n.stat-row:last-child {\n  margin-bottom: 0;\n}\n\n.stat-label {\n  color: #666;\n}\n\n.stat-value {\n  color: #333;\n  font-weight: 500;\n}\n\n.rain-actions {\n  display: flex;\n  justify-content: flex-end;\n  border-top: 1px solid #eee;\n  padding-top: 10px;\n}\n\n.action-btn {\n  margin-left: 10px;\n  padding: 5px 12px;\n  border-radius: 15px;\n  font-size: 12px;\n  background-color: #F5F7FA;\n  color: #666;\n}\n\n.action-btn.delete {\n  background-color: #FEE8E8;\n  color: #FF5858;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 30px 0;\n}\n\n.empty-icon {\n  width: 60px;\n  height: 60px;\n  background-color: #F5F7FA;\n  border-radius: 30px;\n  margin-bottom: 10px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 指南样式 */\n.guide-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.guide-steps {\n  margin-top: 15px;\n}\n\n.guide-step {\n  display: flex;\n  margin-bottom: 15px;\n}\n\n.guide-step:last-child {\n  margin-bottom: 0;\n}\n\n.step-number {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background-color: #FF5858;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  margin-right: 10px;\n  flex-shrink: 0;\n}\n\n.step-content {\n  flex: 1;\n}\n\n.step-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.step-desc {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 预览样式 */\n.preview-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  margin-bottom: 80px;\n}\n\n.preview-container {\n  margin-top: 15px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.preview-image {\n  width: 100%;\n  height: auto;\n  border-radius: 8px;\n  margin-bottom: 15px;\n}\n\n.preview-btn {\n  padding: 8px 20px;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  color: #fff;\n  border-radius: 20px;\n  font-size: 14px;\n}\n\n/* 浮动操作按钮 */\n.floating-action-button {\n  position: fixed;\n  bottom: 30px;\n  right: 20px;\n  width: 56px;\n  height: 56px;\n  border-radius: 28px;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 3px 10px rgba(255, 88, 88, 0.3);\n  z-index: 100;\n}\n\n.fab-icon {\n  font-size: 28px;\n  color: #fff;\n  font-weight: 300;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/red-rain.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA2KA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS,CAAC,MAAM,OAAO,OAAO,OAAO,IAAI;AAAA;AAAA,MAGzC,UAAU;AAAA,QACR,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QAEd,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MACnB;AAAA;AAAA,MAGD,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,WAAW;AAAA,UACX,eAAe;AAAA,QAChB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,WAAW;AAAA,UACX,eAAe;AAAA,QAChB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,WAAW;AAAA,UACX,eAAe;AAAA,QAChB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,WAAW;AAAA,UACX,eAAe;AAAA,QACjB;AAAA,MACD;AAAA;AAAA,MAGD,YAAY;AAAA,QACV;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,mBAAmB;AACjB,UAAI,KAAK,eAAe,GAAG;AACzB,eAAO,KAAK;AAAA,aACP;AACL,cAAM,YAAY;AAAA,UAChB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA;AAEL,eAAO,KAAK,SAAS,OAAO,UAAQ,KAAK,WAAW,UAAU,KAAK,UAAU,CAAC;AAAA,MAChF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa,KAAK;AAChB,aAAO,IAAI,QAAQ,CAAC;AAAA,IACrB;AAAA,IACD,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,eAAe,MAAM;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,SAAS,MAAM;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,UAAU,MAAM;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,WAAW,MAAM;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,KAAK;AAAA,QAC5B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzWA,GAAG,WAAW,eAAe;"}