
.settings-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}
.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}
.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}
.settings-section {
  margin-bottom: 20px;
}
.section-header {
  padding: 16px 16px 8px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.settings-menu {
  background-color: #fff;
}
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}
.menu-item:last-child {
  border-bottom: none;
}
.item-left {
  display: flex;
  align-items: center;
}
.item-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}
.password-icon, .log-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}
.device-icon, .role-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}
.permission-icon, .user-icon {
  background-color: #f6ffed;
  color: #52c41a;
}
.channel-icon, .type-icon {
  background-color: #fcf4ff;
  color: #722ed1;
}
.template-icon, .printer-icon {
  background-color: #fff2e8;
  color: #fa541c;
}
.logistics-icon, .payment-icon {
  background-color: #f0f5ff;
  color: #2f54eb;
}
.app-icon {
  background-color: #fff0f6;
  color: #eb2f96;
}
.item-name {
  font-size: 16px;
  color: #333;
}
.item-right {
  display: flex;
  align-items: center;
}
.item-desc {
  font-size: 14px;
  color: #999;
  margin-right: 8px;
}
.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  transform: rotate(45deg);
}
