"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_polyline + _component_svg + _component_line + _component_circle)();
}
if (!Math) {
  BaseInfoCard();
}
const BaseInfoCard = () => "./BaseInfoCard.js";
const _sfc_main = {
  __name: "SecondHandCard",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const filteredTransactionMethods = common_vendor.computed(() => {
      if (!props.item.transactionMethods || !props.item.transactionMethods.length)
        return [];
      if (!props.item.tags || !props.item.tags.length)
        return props.item.transactionMethods;
      const tagsLowerCase = props.item.tags.map((tag) => tag.toLowerCase());
      return props.item.transactionMethods.filter(
        (method) => !tagsLowerCase.includes(method.toLowerCase())
      );
    });
    function getMethodIconClass(method) {
      const methodMap = {
        "自提": "pickup-icon",
        "邮寄": "delivery-icon",
        "同城配送": "local-icon",
        "见面交易": "face-icon",
        "可议价": "negotiate-icon"
      };
      return methodMap[method] || "default-icon";
    }
    function calculateDiscount(currentPrice, originalPrice) {
      if (!currentPrice || !originalPrice || originalPrice <= 0)
        return null;
      const discount = (currentPrice / originalPrice * 10).toFixed(1);
      return discount < 10 ? discount : null;
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.item.condition
      }, __props.item.condition ? {
        b: common_vendor.p({
          d: "M22 11.08V12a10 10 0 1 1-5.93-9.14"
        }),
        c: common_vendor.p({
          points: "22 4 12 14.01 9 11.01"
        }),
        d: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.t(__props.item.condition)
      } : {}, {
        f: __props.item.brand
      }, __props.item.brand ? {
        g: common_vendor.p({
          d: "M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"
        }),
        h: common_vendor.p({
          x1: "7",
          y1: "7",
          x2: "7.01",
          y2: "7"
        }),
        i: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        j: common_vendor.t(__props.item.brand)
      } : {}, {
        k: __props.item.age
      }, __props.item.age ? {
        l: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10"
        }),
        m: common_vendor.p({
          points: "12 6 12 12 16 14"
        }),
        n: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.t(__props.item.age)
      } : {}, {
        p: __props.item.transactionType
      }, __props.item.transactionType ? {
        q: common_vendor.p({
          x1: "12",
          y1: "1",
          x2: "12",
          y2: "23"
        }),
        r: common_vendor.p({
          d: "M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
        }),
        s: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        t: common_vendor.t(__props.item.transactionType)
      } : {}, {
        v: __props.item.originalPrice
      }, __props.item.originalPrice ? common_vendor.e({
        w: common_vendor.t(__props.item.originalPrice),
        x: common_vendor.t(__props.item.price),
        y: calculateDiscount(__props.item.price, __props.item.originalPrice)
      }, calculateDiscount(__props.item.price, __props.item.originalPrice) ? {
        z: common_vendor.t(calculateDiscount(__props.item.price, __props.item.originalPrice))
      } : {}) : {}, {
        A: __props.item.description
      }, __props.item.description ? {
        B: common_vendor.t(__props.item.description)
      } : {}, {
        C: __props.item.tags && __props.item.tags.length
      }, __props.item.tags && __props.item.tags.length ? {
        D: common_vendor.f(__props.item.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        })
      } : {}, {
        E: filteredTransactionMethods.value.length
      }, filteredTransactionMethods.value.length ? {
        F: common_vendor.f(filteredTransactionMethods.value, (method, index, i0) => {
          return {
            a: "a3a617cd-14-" + i0 + "," + ("a3a617cd-13-" + i0),
            b: "a3a617cd-13-" + i0 + ",a3a617cd-0",
            c: common_vendor.n(getMethodIconClass(method)),
            d: common_vendor.t(method),
            e: index
          };
        }),
        G: common_vendor.p({
          points: "20 6 9 17 4 12"
        }),
        H: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        })
      } : {}, {
        I: common_vendor.p({
          item: __props.item
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a3a617cd"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/cards/SecondHandCard.js.map
