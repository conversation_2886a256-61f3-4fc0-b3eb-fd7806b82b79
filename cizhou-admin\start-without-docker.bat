@echo off
chcp 65001 >nul

echo.
echo ========================================
echo   磁州生活网无Docker启动方案
echo ========================================
echo.

echo 此脚本将指导您在不使用Docker的情况下启动系统
echo.

echo 需要本地安装以下服务：
echo   1. MySQL 8.0+
echo   2. Redis 6.0+
echo   3. Node.js 18+
echo.

echo ========================================
echo 第一步：检查本地服务
echo ========================================
echo.

echo [1] 检查 MySQL 服务
sc query mysql >nul 2>&1
if errorlevel 1 (
    sc query mysql80 >nul 2>&1
    if errorlevel 1 (
        echo    X MySQL 服务未安装或未启动
        echo      请查看安装指南: install-mysql.md
        echo      下载地址: https://dev.mysql.com/downloads/mysql/
        set "mysql_ok=false"
    ) else (
        echo    √ MySQL80 服务运行中
        set "mysql_ok=true"
    )
) else (
    echo    √ MySQL 服务运行中
    set "mysql_ok=true"
)

echo.
echo [2] 检查 Redis 服务
sc query redis >nul 2>&1
if errorlevel 1 (
    echo    X Redis 服务未安装或未启动
    echo      请查看安装指南: install-redis.md
    echo      下载地址: https://github.com/tporadowski/redis/releases
    set "redis_ok=false"
) else (
    echo    √ Redis 服务运行中
    set "redis_ok=true"
)

echo.
echo [3] 测试 MySQL 连接
if "%mysql_ok%"=="true" (
    mysql -uroot -pcizhou123456 -e "SELECT 1;" >nul 2>&1
    if errorlevel 1 (
        echo    X MySQL 连接失败，请检查密码是否为: cizhou123456
        set "mysql_ok=false"
    ) else (
        echo    √ MySQL 连接成功
    )
)

echo.
echo [4] 测试 Redis 连接
if "%redis_ok%"=="true" (
    redis-cli -a cizhou123456 ping >nul 2>&1
    if errorlevel 1 (
        redis-cli ping >nul 2>&1
        if errorlevel 1 (
            echo    X Redis 连接失败
            set "redis_ok=false"
        ) else (
            echo    √ Redis 连接成功 (无密码)
        )
    ) else (
        echo    √ Redis 连接成功 (有密码)
    )
)

echo.
echo [5] 检查 Node.js
where node >nul 2>&1
if errorlevel 1 (
    echo    X Node.js 未安装
    echo      请安装 Node.js 18+
    echo      下载地址: https://nodejs.org/
    set "node_ok=false"
) else (
    echo    √ Node.js 已安装
    node --version
    set "node_ok=true"
)

echo.
echo ========================================
echo 环境检查结果
echo ========================================
echo.

if "%mysql_ok%"=="false" (
    echo X MySQL 未就绪
    echo   请按照 install-mysql.md 安装 MySQL
    echo.
)

if "%redis_ok%"=="false" (
    echo X Redis 未就绪
    echo   请按照 install-redis.md 安装 Redis
    echo.
)

if "%node_ok%"=="false" (
    echo X Node.js 未就绪
    echo   请访问 https://nodejs.org/ 安装 Node.js
    echo.
)

if "%mysql_ok%"=="false" (
    echo 是否现在查看 MySQL 安装指南？
    set /p view_mysql="输入 y 查看安装指南，其他键跳过: "
    if /i "%view_mysql%"=="y" (
        start install-mysql.md
    )
)

if "%redis_ok%"=="false" (
    echo 是否现在查看 Redis 安装指南？
    set /p view_redis="输入 y 查看安装指南，其他键跳过: "
    if /i "%view_redis%"=="y" (
        start install-redis.md
    )
)

if "%mysql_ok%"=="false" (
    echo.
    echo 请先安装并配置好 MySQL 和 Redis，然后重新运行此脚本
    goto :end
)

if "%redis_ok%"=="false" (
    echo.
    echo 请先安装并配置好 MySQL 和 Redis，然后重新运行此脚本
    goto :end
)

echo.
echo ========================================
echo 第二步：配置数据库
echo ========================================
echo.

echo 请确保 MySQL 配置如下：
echo   主机: localhost
echo   端口: 3306
echo   用户: root
echo   密码: cizhou123456
echo   数据库: cizhou_admin
echo.

echo 如果密码不同，请修改以下配置文件：
echo   - backend\cizhou-auth\src\main\resources\application.yml
echo   - backend\cizhou-gateway\src\main\resources\application.yml
echo.

set /p continue="是否继续初始化数据库？(y/N): "
if /i not "%continue%"=="y" (
    echo 已取消初始化
    goto :end
)

echo.
echo 正在初始化数据库...

REM 尝试连接MySQL并创建数据库
mysql -uroot -pcizhou123456 -e "CREATE DATABASE IF NOT EXISTS cizhou_admin DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>nul
if errorlevel 1 (
    echo X 数据库连接失败，请检查MySQL配置
    echo   1. 确保MySQL服务已启动
    echo   2. 确保用户名密码正确
    echo   3. 手动创建数据库: cizhou_admin
    goto :end
)

REM 导入数据库结构
mysql -uroot -pcizhou123456 cizhou_admin < backend\sql\init.sql 2>nul
if errorlevel 1 (
    echo X 数据库初始化失败
    echo   请手动执行: backend\sql\init.sql
) else (
    echo √ 数据库初始化成功
)

echo.
echo ========================================
echo 第三步：启动应用服务
echo ========================================
echo.

echo 请按以下顺序启动服务：
echo.

echo [1] 启动认证服务：
echo     cd backend\cizhou-auth
echo     mvn spring-boot:run
echo     或在IDE中运行 AuthApplication.java
echo.

echo [2] 启动网关服务：
echo     cd backend\cizhou-gateway  
echo     mvn spring-boot:run
echo     或在IDE中运行 GatewayApplication.java
echo.

echo [3] 启动前端服务：
echo     cd frontend
echo     npm install
echo     npm run dev
echo.

echo ========================================
echo 服务地址
echo ========================================
echo.

echo 启动完成后可访问：
echo   前端管理后台: http://localhost:3000
echo   后端API网关:  http://localhost:8080
echo   MySQL数据库:  localhost:3306
echo   Redis缓存:    localhost:6379
echo.

echo 默认管理员账号：
echo   用户名: admin
echo   密码:   admin123
echo.

:end
echo ========================================
echo 完成
echo ========================================
pause
