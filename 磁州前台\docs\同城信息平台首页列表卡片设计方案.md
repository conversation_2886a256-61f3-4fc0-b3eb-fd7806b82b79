# 同城信息平台首页列表卡片设计方案

## 核心设计原则
1. **简洁高效**：展示最核心信息，减少干扰元素
2. **信息密度适中**：避免过于拥挤或过于空白
3. **快速识别**：用户能在1-2秒内理解卡片内容
4. **统一风格**：不同类型信息保持视觉一致性
5. **性能优先**：确保列表快速加载和流畅滚动

## 通用卡片结构

```
┌─────────────────────────────────┐
│ [缩略图]  标题文字(单行/双行)   │
│           ¥价格/薪资 [标签]     │
│           [位置] · [时间]       │
└─────────────────────────────────┘
```

## 基础功能组件

### 1. 缩略图区域
- 固定宽高比(1:1或4:3)，确保视觉一致性
- 智能裁剪，确保主体内容居中
- 图片懒加载，提升列表性能
- 无图时显示默认分类图标
- 右上角可添加状态标签(已售/急租等)

### 2. 标题区域
- 限制1-2行，超出显示省略号
- 字体大小适中(14-16px)，确保可读性
- 关键词可高亮显示(搜索结果)

### 3. 核心数据区
- 价格/薪资等核心数字信息
- 使用醒目颜色(如红色)
- 大字号显示，提升视觉层级

### 4. 标签区域
- 每个卡片限制2-3个关键标签
- 使用不同颜色区分标签类型
- 标签文字简洁(2-4字为宜)

### 5. 位置与时间
- 使用小字号(12px)
- 位置精简到区域/商圈级别
- 时间显示为相对时间(如"3小时前")

## 不同类型信息的卡片特点

### 招聘信息卡片
```
┌─────────────────────────────────┐
│ [公司LOGO] 销售经理(急聘)       │
│            ¥8-12K·13薪 [全职]   │
│            [高新区]·[1小时前]   │
└─────────────────────────────────┘
```
- 核心展示：职位名称、薪资范围、工作性质
- 特色标签：学历要求、经验要求
- 缩略图使用：公司logo或行业图标

### 求职信息卡片
```
┌─────────────────────────────────┐
│ [头像]    会计文员/出纳         │
│           期望薪资:5-7K [2年经验]│
│           [南城区]·[今天]       │
└─────────────────────────────────┘
```
- 核心展示：求职意向、期望薪资、经验
- 特色标签：学历、年龄段
- 缩略图使用：求职者头像(可模糊处理)

### 门店转让卡片
```
┌─────────────────────────────────┐
│ [店铺图]  盈利中餐饮店急转      │
│           转让费:¥5.8万 [70㎡]  │
│           [商业街]·[2天前]      │
└─────────────────────────────────┘
```
- 核心展示：店铺类型、转让费、面积
- 特色标签：租金、剩余租期
- 缩略图使用：店铺外观或内部实景

### 二手交易卡片
```
┌─────────────────────────────────┐
│ [物品图]  iPhone 13 128G 全网通 │
│           ¥3800 [95新][保修内]  │
│           [西湖区]·[3小时前]    │
└─────────────────────────────────┘
```
- 核心展示：物品名称、价格、新旧程度
- 特色标签：品牌、保修情况
- 缩略图使用：物品主图(白底图优先)

### 房屋出租卡片
```
┌─────────────────────────────────┐
│ [房屋图]  南北通透两居室 精装修 │
│           ¥2800/月 [2室1厅][70㎡]│
│           [高新区]·[刚刚]       │
└─────────────────────────────────┘
```
- 核心展示：房屋类型、租金、户型、面积
- 特色标签：装修情况、朝向、电梯与否
- 缩略图使用：房屋实景(客厅或外观)

### 房屋出售卡片
```
┌─────────────────────────────────┐
│ [房屋图]  城南花园三居室 南北通透│
│           ¥138万 [3室2厅][110㎡] │
│           [城南区]·[昨天]       │
└─────────────────────────────────┘
```
- 核心展示：小区名称、总价、户型、面积
- 特色标签：单价、楼层、装修
- 缩略图使用：房屋实景或小区环境

### 二手车辆卡片
```
┌─────────────────────────────────┐
│ [车辆图]  奥迪A6L 2019款 自动档 │
│           ¥22.8万 [3.2万公里]   │
│           [城东区]·[5天前]      │
└─────────────────────────────────┘
```
- 核心展示：车型、年份、价格、里程
- 特色标签：排量、变速箱类型
- 缩略图使用：车辆侧45°角实拍图

## 技术实现方案

### 1. 组件设计
```vue
<template>
  <div class="info-card" :class="type">
    <div class="card-thumbnail">
      <img v-lazy="thumbnail" :alt="title">
      <span v-if="status" class="status-tag">{{ status }}</span>
    </div>
    <div class="card-content">
      <h3 class="card-title">{{ title }}</h3>
      <div class="card-price">
        <span class="price-symbol">¥</span>
        <span class="price-value">{{ price }}</span>
        <span v-if="priceUnit" class="price-unit">{{ priceUnit }}</span>
      </div>
      <div class="card-tags">
        <span v-for="tag in displayTags" :key="tag.id" class="tag" :class="tag.type">
          {{ tag.text }}
        </span>
      </div>
      <div class="card-footer">
        <span class="location">{{ location }}</span>
        <span class="dot-separator">·</span>
        <span class="time">{{ formatTime(publishTime) }}</span>
      </div>
    </div>
  </div>
</template>
```

### 2. 响应式设计
- 移动端：单列展示，图片宽度约30%
- 平板：双列展示，图片宽度约40%
- 桌面端：三列或四列，保持统一比例

### 3. 性能优化
- 虚拟列表：只渲染可视区域的卡片
- 图片优化：使用webp格式，CDN加速
- 骨架屏：加载过程中显示占位

### 4. 交互优化
- 点击整个卡片进入详情页
- 长按卡片显示快捷操作菜单(收藏/分享)
- 下拉刷新、滚动加载更多

## 数据结构设计

```javascript
// 首页列表卡片数据结构
const listCardData = {
  id: String,                // 信息ID
  type: String,              // 信息类型(招聘/二手/房屋等)
  title: String,             // 标题(限制50字符)
  thumbnail: String,         // 缩略图URL
  price: Number,             // 价格/薪资
  priceUnit: String,         // 价格单位(月/天/次)
  location: String,          // 位置信息(区域级别)
  publishTime: Date,         // 发布时间
  status: String,            // 状态(选填:急售/已租等)
  tags: [{                   // 标签(限制3个)
    id: String,
    text: String,
    type: String             // 标签类型(用于样式)
  }],
  priority: Number,          // 排序优先级(置顶/推广)
}
```

## 列表布局设计

### 1. 瀑布流布局
- 适合图片展示为主的信息(二手/房产)
- 图片高度不一，但宽度固定
- 更有视觉冲击力

### 2. 网格布局
- 适合信息密度较高的类型(招聘/服务)
- 规整的行列结构
- 便于快速浏览和比较

### 3. 列表布局
- 适合文字信息为主的类型(求职/资讯)
- 单列展示，图片较小或无图
- 信息条目紧凑，一屏可展示更多

## 实现建议

### 1. 组件封装
- 创建基础卡片组件`BaseInfoCard.vue`
- 根据不同业务类型扩展子组件
- 使用插槽设计，提高灵活性

### 2. 样式设计
- 使用CSS变量定义主题色
- 卡片阴影轻微，边框圆角适中
- 标签使用微胶囊形状，增强识别度

### 3. 列表加载策略
- 首屏快速加载(限制10-15条)
- 滚动加载更多(每次10条)
- 支持按类型/区域快速筛选

### 4. 用户体验优化
- 点击卡片有轻微反馈效果
- 新发布信息有"新"标识(24小时内)
- 已浏览过的卡片标题颜色变淡 