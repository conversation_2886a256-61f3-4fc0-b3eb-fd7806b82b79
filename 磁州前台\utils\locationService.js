/**
 * 位置服务管理
 */
import { checkLocationPermission, openSettings } from './permission.js';
import { getUserLocation, calculateDistance, formatDistance } from './location.js';

// 存储当前位置信息
let currentLocation = null;

// 默认位置（当用户拒绝位置权限或获取位置失败时使用）
const DEFAULT_LOCATION = {
  latitude: 36.313076,
  longitude: 114.347312,
  province: '河北省',
  city: '邯郸市',
  district: '磁县',
  address: '河北省邯郸市磁县',
  location: '河北省 邯郸市 磁县'
};

// 初始化位置服务
export const initLocationService = async () => {
  console.log('初始化位置服务');
  
  // 检查本地缓存是否有位置信息
  const cachedLocation = uni.getStorageSync('user_location');
  if (cachedLocation) {
    currentLocation = cachedLocation;
  } else {
    // 如果没有缓存的位置信息，使用默认位置
    currentLocation = DEFAULT_LOCATION;
    uni.setStorageSync('user_location', DEFAULT_LOCATION);
  }
  
  // 监听位置更新事件
  uni.$on('location_updated', (updatedLocation) => {
    if (updatedLocation && updatedLocation.latitude) {
      currentLocation = updatedLocation;
      uni.setStorageSync('user_location', updatedLocation);
    }
  });
  
  return currentLocation;
};

// 获取当前位置信息
// @deprecated 已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代
export const getCurrentLocation = () => {
  console.warn('getCurrentLocation 方法已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代');
  
  // 检查当前是否有位置信息
  if (currentLocation) {
    return currentLocation;
  }
  
  // 检查本地缓存是否有位置信息
  const cachedLocation = uni.getStorageSync('user_location');
  if (cachedLocation) {
    currentLocation = cachedLocation;
    return cachedLocation;
  }
  
  // 如果没有位置信息，返回默认位置
  return DEFAULT_LOCATION;
};

// 刷新位置信息
// @deprecated 已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代
export const refreshLocation = async () => {
  console.warn('refreshLocation 方法已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代');
  
  try {
    // 显示加载中提示
    uni.showToast({
      title: '更新位置...',
      icon: 'loading',
      duration: 1000,
      mask: false
    });
    
    // 尝试获取真实位置
    const permissionStatus = await checkLocationPermission();
    
    // 如果已授权位置权限
    if (permissionStatus === 'authorized') {
      try {
        // 获取位置信息
        const location = await getUserLocation({
          type: 'gcj02',
          timeout: 3000
        });
        
        // 构建位置对象
        const newLocation = {
          latitude: location.latitude,
          longitude: location.longitude,
          // 理想情况下应通过地图API获取以下信息
          // 这里简化处理，使用默认数据
          province: DEFAULT_LOCATION.province,
          city: DEFAULT_LOCATION.city,
          district: DEFAULT_LOCATION.district,
          address: DEFAULT_LOCATION.address,
          location: DEFAULT_LOCATION.location
        };
        
        // 更新当前位置
        currentLocation = newLocation;
        uni.setStorageSync('user_location', newLocation);
        
        // 发送位置更新事件
        uni.$emit('location_updated', newLocation);
        
        uni.showToast({
          title: '位置已更新',
          icon: 'success',
          duration: 1500
        });
        
        return newLocation;
      } catch (error) {
        console.error('获取位置失败:', error);
        
        // 如果获取位置失败，使用之前的位置或默认位置
        const fallbackLocation = currentLocation || DEFAULT_LOCATION;
        
        return fallbackLocation;
      }
    } else {
      // 如果未授权，询问用户是否打开设置
      uni.showModal({
        title: '位置权限未开启',
        content: '需要位置权限才能刷新位置，是否前往设置开启？',
        confirmText: '去设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 打开设置页面
            openSettings();
          }
        }
      });
      
      // 返回当前位置或默认位置
      return currentLocation || DEFAULT_LOCATION;
    }
  } catch (error) {
    console.error('刷新位置信息失败:', error);
    
    uni.showToast({
      title: '更新位置失败',
      icon: 'none',
      duration: 1500
    });
    
    return currentLocation || DEFAULT_LOCATION;
  }
};

// 计算与当前位置的距离
export const distanceFromCurrentLocation = (lat, lng) => {
  const current = getCurrentLocation();
  if (!current || !current.latitude || !current.longitude) {
    return '未知距离';
  }
  
  const distance = calculateDistance(
    current.latitude,
    current.longitude,
    lat,
    lng
  );
  
  return formatDistance(distance);
};

// 导出其他位置相关函数
export { calculateDistance, formatDistance }; 