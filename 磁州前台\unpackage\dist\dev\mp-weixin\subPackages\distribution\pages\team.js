"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_distributionService = require("../../../utils/distributionService.js");
const _sfc_main = {
  __name: "team",
  setup(__props) {
    const tabs = [
      { name: "全部成员", value: "all" },
      { name: "一级成员", value: 1 },
      { name: "二级成员", value: 2 }
    ];
    const activeTab = common_vendor.ref("all");
    const teamMembers = common_vendor.ref([]);
    const teamSummary = common_vendor.reactive({
      level1Count: 0,
      level2Count: 0,
      totalContribution: 0
    });
    const commissionRates = common_vendor.reactive({
      level1: 30,
      level2: 10
    });
    const pagination = common_vendor.reactive({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });
    const hasMoreData = common_vendor.ref(false);
    const loading = common_vendor.ref(false);
    common_vendor.onMounted(async () => {
      await getTeamMembers();
    });
    const getTeamMembers = async (loadMore2 = false) => {
      if (loading.value)
        return;
      try {
        loading.value = true;
        const page = loadMore2 ? pagination.page + 1 : 1;
        const level = activeTab.value === "all" ? void 0 : activeTab.value;
        const result = await utils_distributionService.distributionService.getTeamMembers({
          page,
          pageSize: pagination.pageSize,
          level
        });
        if (result) {
          if (loadMore2) {
            teamMembers.value = [...teamMembers.value, ...result.list];
          } else {
            teamMembers.value = result.list;
          }
          pagination.page = page;
          pagination.total = result.pagination.total;
          pagination.totalPages = result.pagination.totalPages;
          hasMoreData.value = pagination.page < pagination.totalPages;
          if (result.summary) {
            Object.assign(teamSummary, result.summary);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/team.vue:252", "获取团队成员失败", error);
        common_vendor.index.showToast({
          title: "获取团队成员失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    };
    const switchTab = (tab) => {
      if (activeTab.value === tab)
        return;
      activeTab.value = tab;
      getTeamMembers();
    };
    const loadMore = () => {
      if (hasMoreData.value && !loading.value) {
        getTeamMembers(true);
      }
    };
    const formatCommission = (amount) => {
      return utils_distributionService.distributionService.formatCommission(amount);
    };
    const formatTime = (time) => {
      if (!time)
        return "";
      const date = new Date(time);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "团队帮助",
        content: "团队页面显示您发展的分销员信息和业绩。一级成员是直接通过您的邀请加入的分销员，二级成员是您的一级成员邀请的分销员。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: common_vendor.t(teamSummary.level1Count + teamSummary.level2Count),
        d: common_vendor.t(formatCommission(teamSummary.totalContribution)),
        e: common_vendor.t(teamSummary.level1Count),
        f: common_vendor.t(teamSummary.level2Count),
        g: common_vendor.f(tabs, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: activeTab.value === tab.value ? 1 : "",
            d: common_vendor.o(($event) => switchTab(tab.value), index)
          };
        }),
        h: teamMembers.value.length > 0
      }, teamMembers.value.length > 0 ? {
        i: common_vendor.f(teamMembers.value, (member, index, i0) => {
          return {
            a: member.avatar,
            b: common_vendor.t(member.nickname),
            c: common_vendor.t(member.levelName),
            d: common_vendor.t(formatTime(member.joinTime)),
            e: common_vendor.t(formatCommission(member.contribution)),
            f: common_vendor.t(member.orderCount),
            g: index
          };
        })
      } : {
        j: common_assets._imports_0$43,
        k: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/promotion"))
      }, {
        l: hasMoreData.value && teamMembers.value.length > 0
      }, hasMoreData.value && teamMembers.value.length > 0 ? common_vendor.e({
        m: loading.value
      }, loading.value ? {} : {
        n: common_vendor.o(loadMore)
      }) : {}, {
        o: common_vendor.t(commissionRates.level1),
        p: common_vendor.t(commissionRates.level2)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/distribution/pages/team.js.map
