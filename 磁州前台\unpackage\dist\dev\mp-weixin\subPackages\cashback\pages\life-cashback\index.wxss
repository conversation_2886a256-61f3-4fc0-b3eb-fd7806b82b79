/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-ef32ba16, html.data-v-ef32ba16, #app.data-v-ef32ba16, .index-container.data-v-ef32ba16 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.life-cashback-container.data-v-ef32ba16 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content-container.data-v-ef32ba16 {
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 20px;
}
.category-section.data-v-ef32ba16 {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.category-grid.data-v-ef32ba16 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}
.category-item.data-v-ef32ba16 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.category-item .item-icon-container.data-v-ef32ba16 {
  width: 52px;
  height: 52px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.category-item .item-title.data-v-ef32ba16 {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
  text-align: center;
}
.category-item .item-desc.data-v-ef32ba16 {
  font-size: 12px;
  color: #9C27B0;
  text-align: center;
}
.section-header.data-v-ef32ba16 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-header .section-title.data-v-ef32ba16 {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}
.section-header .section-more.data-v-ef32ba16 {
  display: flex;
  align-items: center;
  color: #999999;
  font-size: 14px;
}
.coupon-section.data-v-ef32ba16 {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.coupons-scroll.data-v-ef32ba16 {
  width: 100%;
  white-space: nowrap;
}
.coupons-container.data-v-ef32ba16 {
  display: inline-flex;
  padding-bottom: 8px;
}
.coupon-item.data-v-ef32ba16 {
  width: 220px;
  height: 100px;
  background: linear-gradient(135deg, #F5F0FF 0%, #EDE7F6 100%);
  border-radius: 12px;
  padding: 16px;
  margin-right: 12px;
  display: flex;
  position: relative;
  overflow: hidden;
}
.coupon-item.data-v-ef32ba16:last-child {
  margin-right: 0;
}
.coupon-item.data-v-ef32ba16::after {
  content: "";
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #FFFFFF;
  box-shadow: inset 0 0 0 2px #F5F0FF;
}
.coupon-item.data-v-ef32ba16::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #FFFFFF;
  box-shadow: inset 0 0 0 2px #F5F0FF;
}
.coupon-item .coupon-content.data-v-ef32ba16 {
  flex: 1;
}
.coupon-item .coupon-content .coupon-platform.data-v-ef32ba16 {
  font-size: 14px;
  color: #333333;
  margin-bottom: 6px;
  display: block;
}
.coupon-item .coupon-content .coupon-desc.data-v-ef32ba16 {
  font-size: 16px;
  color: #9C27B0;
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
}
.coupon-item .coupon-content .coupon-subdesc.data-v-ef32ba16 {
  font-size: 12px;
  color: #666666;
  display: block;
}
.coupon-item .coupon-price.data-v-ef32ba16 {
  background-color: #FF5252;
  border-radius: 20px;
  padding: 4px 8px;
  height: -webkit-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
}
.coupon-item .coupon-price .price-value.data-v-ef32ba16 {
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
}
.coupon-item .coupon-price .price-tag.data-v-ef32ba16 {
  font-size: 12px;
  color: #FFFFFF;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 0 4px;
  margin-left: 2px;
}
.activity-section.data-v-ef32ba16 {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.activity-list.data-v-ef32ba16 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.activity-item.data-v-ef32ba16 {
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  background-color: #F9F9F9;
}
.activity-item .activity-image.data-v-ef32ba16 {
  width: 100px;
  height: 100px;
  object-fit: cover;
}
.activity-item .activity-info.data-v-ef32ba16 {
  flex: 1;
  padding: 12px;
}
.activity-item .activity-info .activity-title.data-v-ef32ba16 {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6px;
  display: block;
}
.activity-item .activity-info .activity-desc.data-v-ef32ba16 {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
  display: block;
}
.activity-item .activity-info .activity-tags.data-v-ef32ba16 {
  display: flex;
  gap: 8px;
}
.activity-item .activity-info .activity-tags .tag.data-v-ef32ba16 {
  font-size: 12px;
  color: #9C27B0;
  background-color: rgba(156, 39, 176, 0.1);
  border-radius: 10px;
  padding: 2px 8px;
}