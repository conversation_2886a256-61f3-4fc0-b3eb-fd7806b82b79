/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 表单容器 */
.form-container {
  padding: 15px;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.step-number {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  background-color: #eee;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-bottom: 5px;
}
.step-item.active .step-number {
  background-color: #FF5858;
  color: #fff;
}
.step-item.completed .step-number {
  background-color: #4ECDC4;
  color: #fff;
}
.step-text {
  font-size: 12px;
  color: #999;
}
.step-item.active .step-text,
.step-item.completed .step-text {
  color: #333;
  font-weight: 500;
}
.step-line {
  flex: 1;
  height: 1px;
  background-color: #eee;
  margin: 0 10px;
}
.step-line.active {
  background-color: #4ECDC4;
}

/* 表单步骤 */
.form-step {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;
}
.form-group {
  margin-bottom: 15px;
}
.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}
.form-input {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
}
.form-textarea {
  width: 100%;
  height: 100px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 14px;
  color: #333;
}

/* 日期范围选择器 */
.date-range-picker {
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.date-range-text {
  font-size: 14px;
  color: #333;
}
.picker-arrow {
  width: 10px;
  height: 10px;
  border-right: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(45deg);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
}
.radio-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
}
.radio-icon {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  border: 1px solid #ddd;
  margin-right: 6px;
  position: relative;
}
.radio-icon.active {
  border-color: #FF5858;
}
.radio-icon.active::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #FF5858;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.radio-text {
  font-size: 14px;
  color: #333;
}

/* 封面上传 */
.cover-uploader {
  width: 100%;
  height: 180px;
  background-color: #F5F7FA;
  border-radius: 8px;
  overflow: hidden;
}
.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon {
  font-size: 30px;
  color: #ccc;
  margin-bottom: 5px;
}
.upload-text {
  font-size: 14px;
  color: #999;
}
.cover-preview {
  width: 100%;
  height: 100%;
}

/* 有效期选择器 */
.validity-selector {
  display: flex;
  flex-wrap: wrap;
}
.validity-option {
  height: 36px;
  padding: 0 15px;
  background-color: #F5F7FA;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  margin-bottom: 10px;
}
.validity-option.active {
  background-color: #FF5858;
  color: #fff;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
}
.checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
}
.checkbox-icon {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin-right: 6px;
  position: relative;
}
.checkbox-icon.active {
  background-color: #FF5858;
  border-color: #FF5858;
}
.checkbox-icon.active::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 5px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.checkbox-text {
  font-size: 14px;
  color: #333;
}

/* 底部按钮 */
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  margin-bottom: 30px;
}
.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 500;
}
.action-btn.back {
  background-color: #F5F7FA;
  color: #666;
  margin-right: 10px;
}
.action-btn.next,
.action-btn.submit {
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  margin-left: 10px;
}
.action-btn.cancel {
  background-color: #F5F7FA;
  color: #666;
  margin-left: 10px;
}