"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  ActivityCard();
}
const ActivityCard = () => "./ActivityCard.js";
const maxDisplayMembers = 4;
const _sfc_main = {
  __name: "GroupBuyCard",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const discountPercent = common_vendor.computed(() => {
      if (!props.item.groupPrice || !props.item.originalPrice)
        return "";
      return Math.floor(props.item.groupPrice / props.item.originalPrice * 10);
    });
    const progressWidth = common_vendor.computed(() => {
      if (!props.item.currentGroupMembers || !props.item.groupSize)
        return 0;
      return props.item.currentGroupMembers / props.item.groupSize * 100;
    });
    const remainCount = common_vendor.computed(() => {
      if (!props.item.currentGroupMembers || !props.item.groupSize)
        return 0;
      return props.item.groupSize - props.item.currentGroupMembers;
    });
    const displayMembers = common_vendor.computed(() => {
      if (!props.item.participants)
        return [];
      return props.item.participants.slice(0, maxDisplayMembers);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(__props.item.groupPrice),
        b: common_vendor.t(__props.item.originalPrice),
        c: common_vendor.t(discountPercent.value),
        d: common_vendor.t(__props.item.currentGroupMembers),
        e: common_vendor.t(__props.item.groupSize),
        f: progressWidth.value + "%",
        g: __props.item.status === "ongoing"
      }, __props.item.status === "ongoing" ? {
        h: common_vendor.t(remainCount.value)
      } : {}, {
        i: __props.item.participants && __props.item.participants.length > 0
      }, __props.item.participants && __props.item.participants.length > 0 ? common_vendor.e({
        j: common_vendor.f(displayMembers.value, (member, index, i0) => {
          return common_vendor.e({
            a: member.avatar,
            b: index === 0
          }, index === 0 ? {} : {}, {
            c: index
          });
        }),
        k: __props.item.participants.length > maxDisplayMembers
      }, __props.item.participants.length > maxDisplayMembers ? {
        l: common_vendor.t(__props.item.participants.length - maxDisplayMembers)
      } : {}) : {}, {
        m: common_vendor.o(($event) => _ctx.$emit("favorite", __props.item.id)),
        n: common_vendor.o(($event) => _ctx.$emit("action", {
          id: __props.item.id,
          type: __props.item.type,
          status: __props.item.status
        })),
        o: common_vendor.p({
          item: __props.item
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3b272413"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/activity/GroupBuyCard.js.map
