/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.discount-detail-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px;
  /* 为底部操作栏留出空间 */
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #333;
  border-bottom: 2px solid #333;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-icon {
  width: 24px;
  height: 24px;
  color: #333;
}

/* 活动卡片样式 */
.discount-card {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}
.discount-card::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 4px;
  background: linear-gradient(90deg, #FDEB71, #F8D800);
}
.discount-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.discount-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.discount-status {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
}
.status-active {
  background: #34C759;
}
.status-expired {
  background: #8E8E93;
}
.status-upcoming {
  background: #FF9500;
}
.status-paused {
  background: #FF9500;
}
.discount-rules {
  margin-bottom: 15px;
}
.rule-item {
  margin-bottom: 8px;
}
.rule-content {
  background: rgba(248, 216, 0, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  display: inline-block;
}
.rule-text {
  font-size: 15px;
  font-weight: 500;
  color: #D4B100;
}
.validity-period {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.validity-label {
  font-size: 14px;
  color: #999;
  margin-right: 5px;
}
.validity-value {
  font-size: 14px;
  color: #333;
}
.discount-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #eee;
}
.qrcode-image {
  width: 120px;
  height: 120px;
  margin-bottom: 10px;
}
.qrcode-hint {
  font-size: 12px;
  color: #999;
}

/* 使用情况样式 */
.usage-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.stats-grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}
.stats-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-value {
  font-size: 18px;
  font-weight: bold;
  color: #F8D800;
  margin-bottom: 5px;
}
.stats-label {
  font-size: 12px;
  color: #999;
}
.chart-container {
  padding: 15px 0;
  border-top: 1px solid #f0f0f0;
}
.chart-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}
.chart-placeholder {
  height: 150px;
  background: #f9f9f9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.placeholder-text {
  font-size: 14px;
  color: #999;
}

/* 活动设置样式 */
.settings-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.settings-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}
.settings-item:last-child {
  border-bottom: none;
}
.item-label {
  font-size: 14px;
  color: #666;
}
.item-value {
  font-size: 14px;
  color: #333;
  max-width: 60%;
  text-align: right;
}

/* 订单记录样式 */
.records-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.view-all {
  font-size: 14px;
  color: #F8D800;
}
.record-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}
.record-item:last-child {
  border-bottom: none;
}
.record-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}
.order-number {
  font-size: 14px;
  color: #333;
}
.record-time {
  font-size: 12px;
  color: #999;
}
.record-details {
  display: flex;
  justify-content: space-between;
}
.user-name {
  font-size: 14px;
  color: #666;
}
.discount-amount {
  font-size: 14px;
  color: #F8D800;
  font-weight: 500;
}
.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 底部操作栏样式 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  padding: 10px 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}
.action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}
.button-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}
.button-text {
  font-size: 12px;
}
.action-button.edit {
  color: #007AFF;
}
.action-button.pause {
  color: #FF9500;
}
.action-button.activate {
  color: #34C759;
}
.action-button.share {
  color: #5856D6;
}
.action-button.delete {
  color: #FF3B30;
}

/* 分享弹窗样式 */
.share-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 15px;
  transform: translateY(0);
  transition: transform 0.3s;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.popup-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}
.share-options {
  display: flex;
  justify-content: space-around;
  padding: 10px 0 20px;
}
.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.option-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  color: #fff;
}
.option-icon.wechat {
  background: #07C160;
}
.option-icon.moments {
  background: #07C160;
}
.option-icon.link {
  background: #007AFF;
}
.option-icon.qrcode {
  background: #FF9500;
}
.option-name {
  font-size: 12px;
  color: #333;
}