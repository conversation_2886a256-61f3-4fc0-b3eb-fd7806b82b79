/**
 * 返利系统服务类
 */
import secureRequest from './secureRequest';

/**
 * 获取首页数据
 * @returns {Promise} 首页数据
 */
export const getHomeData = async () => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.get('/api/cashback/home');
    
    // 模拟数据
    return {
      banners: [
        { id: 1, image: 'https://via.placeholder.com/750x300', link: '' },
        { id: 2, image: 'https://via.placeholder.com/750x300', link: '' },
        { id: 3, image: 'https://via.placeholder.com/750x300', link: '' }
      ],
      platforms: [
        { id: 1, name: '淘宝', icon: 'https://via.placeholder.com/60x60', link: '' },
        { id: 2, name: '京东', icon: 'https://via.placeholder.com/60x60', link: '' },
        { id: 3, name: '拼多多', icon: 'https://via.placeholder.com/60x60', link: '' },
        { id: 4, name: '唯品会', icon: 'https://via.placeholder.com/60x60', link: '' },
        { id: 5, name: '苏宁', icon: 'https://via.placeholder.com/60x60', link: '' }
      ],
      highCashbackItems: [
        { id: 1, title: 'Apple iPhone 14 Pro Max 256GB', price: '8999.00', cashbackRate: '5%', image: 'https://via.placeholder.com/200x200' },
        { id: 2, title: 'Sony WH-1000XM5 无线降噪耳机', price: '2999.00', cashbackRate: '10%', image: 'https://via.placeholder.com/200x200' },
        { id: 3, title: 'Dyson V12 无绳吸尘器', price: '4590.00', cashbackRate: '8%', image: 'https://via.placeholder.com/200x200' },
        { id: 4, title: '华为 MateBook X Pro 2023', price: '9999.00', cashbackRate: '6%', image: 'https://via.placeholder.com/200x200' }
      ],
      hotProducts: [
        { id: 1, title: '小米13 Ultra 5G手机', price: '5999.00', cashbackRate: '4%', image: 'https://via.placeholder.com/200x200', platformLogo: 'https://via.placeholder.com/20x20' },
        { id: 2, title: 'Apple iPad Air 5 平板电脑', price: '4799.00', cashbackRate: '3%', image: 'https://via.placeholder.com/200x200', platformLogo: 'https://via.placeholder.com/20x20' },
        { id: 3, title: 'Nike Air Max 270 运动鞋', price: '999.00', cashbackRate: '15%', image: 'https://via.placeholder.com/200x200', platformLogo: 'https://via.placeholder.com/20x20' },
        { id: 4, title: '三星 Galaxy Watch 5 Pro', price: '2999.00', cashbackRate: '7%', image: 'https://via.placeholder.com/200x200', platformLogo: 'https://via.placeholder.com/20x20' }
      ]
    };
  } catch (error) {
    console.error('获取首页数据失败', error);
    throw error;
  }
};

/**
 * 获取商品详情
 * @param {string} id 商品ID
 * @returns {Promise} 商品详情
 */
export const getProductDetail = async (id) => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.get(`/api/cashback/product/${id}`);
    
    // 模拟数据
    return {
      id,
      title: 'Apple iPhone 14 Pro Max 256GB 暗紫色 移动联通电信5G双卡双待手机',
      price: '8999.00',
      originalPrice: '9999.00',
      cashbackRate: '5%',
      platform: '京东',
      platformLogo: 'https://via.placeholder.com/30x30',
      sales: '10000',
      images: [
        'https://via.placeholder.com/750x750',
        'https://via.placeholder.com/750x750',
        'https://via.placeholder.com/750x750'
      ],
      lowestPrice: '8799.00',
      highestPrice: '9999.00',
      averagePrice: '9299.00',
      similarProducts: [
        { id: 2, title: 'Apple iPhone 14 128GB', price: '6999.00', cashbackRate: '4%', image: 'https://via.placeholder.com/200x200' },
        { id: 3, title: 'Apple iPhone 14 Plus 128GB', price: '7999.00', cashbackRate: '4.5%', image: 'https://via.placeholder.com/200x200' },
        { id: 4, title: 'Apple iPhone 14 Pro 256GB', price: '8799.00', cashbackRate: '5%', image: 'https://via.placeholder.com/200x200' },
        { id: 5, title: 'Apple iPhone 13 Pro Max 256GB', price: '7999.00', cashbackRate: '6%', image: 'https://via.placeholder.com/200x200' }
      ]
    };
  } catch (error) {
    console.error('获取商品详情失败', error);
    throw error;
  }
};

/**
 * 搜索商品
 * @param {Object} params 搜索参数
 * @returns {Promise} 搜索结果
 */
export const searchProducts = async (params) => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.post('/api/cashback/search', params);
    
    // 模拟数据
    return {
      total: 100,
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      list: [
        { id: 1, title: 'Apple iPhone 14 Pro Max 256GB', price: '8999.00', cashbackRate: '5%', sales: '10000+', shop: '苹果官方旗舰店', image: 'https://via.placeholder.com/200x200', platformLogo: 'https://via.placeholder.com/20x20' },
        { id: 2, title: 'Apple iPhone 14 128GB', price: '6999.00', cashbackRate: '4%', sales: '8000+', shop: '苹果官方旗舰店', image: 'https://via.placeholder.com/200x200', platformLogo: 'https://via.placeholder.com/20x20' },
        { id: 3, title: 'Apple iPhone 14 Plus 128GB', price: '7999.00', cashbackRate: '4.5%', sales: '5000+', shop: '苹果官方旗舰店', image: 'https://via.placeholder.com/200x200', platformLogo: 'https://via.placeholder.com/20x20' },
        { id: 4, title: 'Apple iPhone 14 Pro 256GB', price: '8799.00', cashbackRate: '5%', sales: '9000+', shop: '苹果官方旗舰店', image: 'https://via.placeholder.com/200x200', platformLogo: 'https://via.placeholder.com/20x20' },
        { id: 5, title: 'Apple iPhone 13 Pro Max 256GB', price: '7999.00', cashbackRate: '6%', sales: '12000+', shop: '苹果官方旗舰店', image: 'https://via.placeholder.com/200x200', platformLogo: 'https://via.placeholder.com/20x20' }
      ]
    };
  } catch (error) {
    console.error('搜索商品失败', error);
    throw error;
  }
};

/**
 * 获取钱包信息
 * @returns {Promise} 钱包信息
 */
export const getWalletInfo = async () => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.get('/api/cashback/wallet');
    
    // 模拟数据
    return {
      balance: '258.75',
      pending: '125.60',
      total: '1258.35',
      withdrawn: '999.60',
      recentOrders: [
        { 
          id: 1, 
          title: 'Apple iPhone 14 Pro Max 256GB', 
          price: '8999.00', 
          cashback: '450.00', 
          status: 'pending', 
          statusText: '待结算', 
          statusClass: 'status-pending',
          time: '2023-06-15', 
          image: 'https://via.placeholder.com/100x100', 
          platformLogo: 'https://via.placeholder.com/20x20' 
        },
        { 
          id: 2, 
          title: 'Sony WH-1000XM5 无线降噪耳机', 
          price: '2999.00', 
          cashback: '299.90', 
          status: 'settled', 
          statusText: '已结算', 
          statusClass: 'status-settled',
          time: '2023-06-10', 
          image: 'https://via.placeholder.com/100x100', 
          platformLogo: 'https://via.placeholder.com/20x20' 
        },
        { 
          id: 3, 
          title: 'Dyson V12 无绳吸尘器', 
          price: '4590.00', 
          cashback: '367.20', 
          status: 'failed', 
          statusText: '未返利', 
          statusClass: 'status-failed',
          time: '2023-06-05', 
          image: 'https://via.placeholder.com/100x100', 
          platformLogo: 'https://via.placeholder.com/20x20' 
        }
      ]
    };
  } catch (error) {
    console.error('获取钱包信息失败', error);
    throw error;
  }
};

/**
 * 获取订单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 订单列表
 */
export const getOrderList = async (params) => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.post('/api/cashback/orders', params);
    
    // 模拟数据
    return {
      total: 50,
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      list: [
        { 
          id: 1, 
          title: 'Apple iPhone 14 Pro Max 256GB', 
          price: '8999.00', 
          cashback: '450.00', 
          status: 'pending', 
          statusText: '待结算', 
          statusClass: 'status-pending',
          time: '2023-06-15', 
          image: 'https://via.placeholder.com/100x100', 
          platformLogo: 'https://via.placeholder.com/20x20' 
        },
        { 
          id: 2, 
          title: 'Sony WH-1000XM5 无线降噪耳机', 
          price: '2999.00', 
          cashback: '299.90', 
          status: 'settled', 
          statusText: '已结算', 
          statusClass: 'status-settled',
          time: '2023-06-10', 
          image: 'https://via.placeholder.com/100x100', 
          platformLogo: 'https://via.placeholder.com/20x20' 
        },
        { 
          id: 3, 
          title: 'Dyson V12 无绳吸尘器', 
          price: '4590.00', 
          cashback: '367.20', 
          status: 'failed', 
          statusText: '未返利', 
          statusClass: 'status-failed',
          time: '2023-06-05', 
          image: 'https://via.placeholder.com/100x100', 
          platformLogo: 'https://via.placeholder.com/20x20' 
        }
      ]
    };
  } catch (error) {
    console.error('获取订单列表失败', error);
    throw error;
  }
};

/**
 * 获取订单详情
 * @param {string} id 订单ID
 * @returns {Promise} 订单详情
 */
export const getOrderDetail = async (id) => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.get(`/api/cashback/order/${id}`);
    
    // 模拟数据
    return {
      id,
      title: 'Apple iPhone 14 Pro Max 256GB 暗紫色 移动联通电信5G双卡双待手机',
      price: '8999.00',
      cashback: '450.00',
      status: 'pending',
      statusText: '待结算',
      statusClass: 'status-pending',
      time: '2023-06-15',
      image: 'https://via.placeholder.com/100x100',
      platformLogo: 'https://via.placeholder.com/20x20',
      platform: '京东',
      orderNumber: '2023061512345678',
      estimatedSettlementTime: '2023-07-15',
      statusHistory: [
        { status: 'created', statusText: '创建订单', time: '2023-06-15 10:30:00' },
        { status: 'paid', statusText: '支付成功', time: '2023-06-15 10:35:00' },
        { status: 'shipped', statusText: '商品发货', time: '2023-06-16 09:20:00' },
        { status: 'delivered', statusText: '确认收货', time: '2023-06-18 14:50:00' },
        { status: 'pending', statusText: '待结算', time: '2023-06-18 14:50:00' }
      ]
    };
  } catch (error) {
    console.error('获取订单详情失败', error);
    throw error;
  }
};

/**
 * 提现申请
 * @param {Object} params 提现参数
 * @returns {Promise} 提现结果
 */
export const withdrawCashback = async (params) => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.post('/api/cashback/withdraw', params);
    
    // 模拟数据
    return {
      success: true,
      message: '提现申请成功，预计1-3个工作日到账',
      withdrawId: 'W' + Date.now(),
      amount: params.amount,
      fee: params.amount * 0.02,
      actualAmount: params.amount * 0.98,
      withdrawTime: new Date().toISOString()
    };
  } catch (error) {
    console.error('提现申请失败', error);
    throw error;
  }
};

/**
 * 获取钱包明细
 * @param {Object} params 查询参数
 * @returns {Promise} 钱包明细
 */
export const getWalletDetail = async (params) => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.post('/api/cashback/wallet/detail', params);
    
    // 模拟数据
    return {
      total: 100,
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      list: [
        { id: 1, type: 'income', typeText: '收入', amount: '450.00', title: '订单返利', desc: 'Apple iPhone 14 Pro Max 256GB', time: '2023-06-18' },
        { id: 2, type: 'withdraw', typeText: '提现', amount: '-200.00', title: '提现到微信', desc: '手续费: ¥4.00', time: '2023-06-17' },
        { id: 3, type: 'income', typeText: '收入', amount: '299.90', title: '订单返利', desc: 'Sony WH-1000XM5 无线降噪耳机', time: '2023-06-15' },
        { id: 4, type: 'income', typeText: '收入', amount: '50.00', title: '邀请奖励', desc: '邀请用户: user123', time: '2023-06-10' },
        { id: 5, type: 'withdraw', typeText: '提现', amount: '-500.00', title: '提现到支付宝', desc: '手续费: ¥10.00', time: '2023-06-05' }
      ]
    };
  } catch (error) {
    console.error('获取钱包明细失败', error);
    throw error;
  }
};

/**
 * 生成分享链接
 * @param {Object} params 分享参数
 * @returns {Promise} 分享链接
 */
export const generateShareLink = async (params) => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.post('/api/cashback/share', params);
    
    // 模拟数据
    return {
      success: true,
      shareLink: `https://example.com/product/${params.productId}?ref=${params.userId}`,
      miniProgramPath: `pages/detail/index?id=${params.productId}&ref=${params.userId}`
    };
  } catch (error) {
    console.error('生成分享链接失败', error);
    throw error;
  }
};

/**
 * 获取邀请数据
 * @returns {Promise} 邀请数据
 */
export const getInviteData = async () => {
  try {
    // 实际应用中应该从API获取数据
    // return await secureRequest.get('/api/cashback/invite');
    
    // 模拟数据
    return {
      inviteCode: 'ABC123',
      inviteLink: 'https://example.com/register?ref=ABC123',
      inviteQrCode: 'https://via.placeholder.com/300x300',
      inviteCount: 15,
      inviteReward: '150.00',
      inviteRules: [
        '邀请好友注册成功即可获得10元奖励',
        '好友首次下单并确认收货后，您将获得额外5元奖励',
        '好友订单返利的5%将作为您的推广奖励',
        '邀请奖励无上限，多邀多得'
      ],
      invitedUsers: [
        { id: 1, nickname: '用户1', avatar: 'https://via.placeholder.com/50x50', time: '2023-06-15', reward: '15.00' },
        { id: 2, nickname: '用户2', avatar: 'https://via.placeholder.com/50x50', time: '2023-06-10', reward: '10.00' },
        { id: 3, nickname: '用户3', avatar: 'https://via.placeholder.com/50x50', time: '2023-06-05', reward: '25.00' }
      ]
    };
  } catch (error) {
    console.error('获取邀请数据失败', error);
    throw error;
  }
};

export default {
  getHomeData,
  getProductDetail,
  searchProducts,
  getWalletInfo,
  getOrderList,
  getOrderDetail,
  withdrawCashback,
  getWalletDetail,
  generateShareLink,
  getInviteData
}; 