<view class="red-packet-container"><view class="custom-navbar"><view class="navbar-left" bindtap="{{a}}"><text class="iconfont icon-back"></text></view><view class="navbar-title">抢红包</view><view class="navbar-right"><text class="iconfont icon-filter" bindtap="{{b}}"></text></view></view><view class="stats-section"><view class="stats-card"><view class="stats-value">{{c}}元</view><view class="stats-label">累计发放</view></view><view class="stats-card"><view class="stats-value">{{d}}元</view><view class="stats-label">我的收益</view></view><view class="stats-card"><view class="stats-value">{{e}}个</view><view class="stats-label">今日红包</view></view></view><view class="filter-tabs"><view class="{{['filter-tab', f && 'active']}}" bindtap="{{g}}"> 全部红包 </view><view class="{{['filter-tab', h && 'active']}}" bindtap="{{i}}"> 商家红包 </view><view class="{{['filter-tab', j && 'active']}}" bindtap="{{k}}"> 平台红包 </view><view class="{{['filter-tab', l && 'active']}}" bindtap="{{m}}"> 信息红包 </view><view class="{{['filter-tab', n && 'active']}}" bindtap="{{o}}"> 附近红包 </view></view><scroll-view scroll-y class="red-packet-list" bindscrolltolower="{{w}}" refresher-enabled="{{true}}" refresher-triggered="{{x}}" bindrefresherrefresh="{{y}}"><view wx:if="{{p}}" class="loading-placeholder"><uni-load-more wx:if="{{q}}" u-i="3e264358-0" bind:__l="__l" u-p="{{q}}"></uni-load-more></view><view wx:elif="{{r}}" class="empty-state"><image class="empty-image" src="{{s}}" mode="aspectFit"></image><text class="empty-text">暂无红包，下拉刷新试试</text></view><view wx:else><view wx:for="{{t}}" wx:for-item="item" wx:key="F" class="{{['red-packet-item', item.E && 'info-red-packet-item']}}" bindtap="{{item.G}}"><view class="merchant-info"><image class="merchant-avatar" src="{{item.a}}" mode="aspectFill"></image><view class="merchant-details"><text class="merchant-name">{{item.b}}</text><text class="publish-time">{{item.c}}</text></view><view wx:if="{{item.d}}" class="info-type-tag">{{item.e}}</view></view><view class="{{['content-section', item.w && 'info-content-section']}}"><block wx:if="{{item.f}}"><text class="info-title">{{item.g}}</text><view class="info-red-packet-bar"><view class="red-packet-icon-mini"><image src="{{item.h}}" mode="aspectFit"></image></view><text class="red-packet-amount-mini">¥{{item.i}} · 剩{{item.j}}个</text><view class="grab-button-mini">抢</view></view></block><block wx:else><view class="info-content"><view class="title-row"><text class="title">{{item.k}}</text></view><text class="description">{{item.l}}</text><view wx:if="{{item.m}}" class="image-list"><image wx:for="{{item.n}}" wx:for-item="img" wx:key="a" src="{{img.b}}" mode="aspectFill" class="content-image" catchtap="{{img.c}}"></image><view wx:if="{{item.o}}" class="image-count">+{{item.p}}</view></view></view><view class="red-packet-info"><view class="red-packet-icon"><image src="{{item.q}}" mode="aspectFit"></image></view><view class="red-packet-details"><text class="packet-amount">¥{{item.r}}</text><text class="packet-type">{{item.s}}</text></view><view class="{{['packet-status', item.v]}}"><text>{{item.t}}</text></view></view></block></view><view wx:if="{{item.x}}" class="action-bar"><view class="action-stats"><text class="stat-item"><text class="iconfont icon-view"></text> {{item.y}}</text><text class="stat-item"><text class="iconfont icon-comment"></text> {{item.z}}</text><text class="stat-item"><text class="iconfont icon-like"></text> {{item.A}}</text></view><view class="action-buttons"><button class="{{['grab-btn', item.C && 'disabled']}}" catchtap="{{item.D}}">{{item.B}}</button></view></view></view><uni-load-more wx:if="{{v}}" u-i="3e264358-1" bind:__l="__l" u-p="{{v}}"></uni-load-more></view></scroll-view><view class="publish-btn" bindtap="{{z}}"><text class="iconfont icon-add"></text><text>发布红包</text></view></view>