/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.my-container {
  min-height: 100vh;
  background-color: #F5F5F7;
  /* 更浅的苹果风格背景色 */
  padding-bottom: 30rpx;
  padding-top: 44px;
  /* 为状态栏预留空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #0A84FF, #0066FF);
  /* 更鲜明的苹果蓝渐变 */
  height: 88rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed;
  /* 固定定位 */
  top: 0;
  left: 0;
  right: 0;
  box-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.2);
  /* 更明显的阴影 */
  z-index: 100;
  backdrop-filter: blur(10px);
  /* 苹果风格模糊效果 */
  -webkit-backdrop-filter: blur(10px);
}
.navbar-title {
  flex: 1;
  text-align: center;
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1px;
  /* 增加字间距 */
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  /* 文字阴影 */
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #FFFFFF, #F8F8FA);
  /* 微妙的渐变背景 */
  margin: 150rpx 24rpx 30rpx;
  /* 增加顶部边距，为固定导航栏留出空间 */
  padding: 40rpx 36rpx;
  /* 增加内边距 */
  display: flex;
  align-items: center;
  border-radius: 32rpx;
  /* 更大圆角 */
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 2rpx 4rpx rgba(0, 0, 0, 0.03);
  /* 双层阴影增强立体感 */
  position: relative;
  overflow: hidden;
  border: 0.5px solid rgba(255, 255, 255, 0.8);
  /* 微妙的边框 */
}

/* 用户卡片装饰元素 */
.user-card::before {
  content: "";
  position: absolute;
  top: -80rpx;
  right: -80rpx;
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.05), rgba(10, 132, 255, 0.1));
  border-radius: 100rpx;
  z-index: 0;
}
.avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 64rpx;
  /* 完美圆形 */
  background-color: #f0f0f0;
  border: 3rpx solid #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
  /* 增强阴影 */
  position: relative;
  z-index: 1;
}
.user-info {
  margin-left: 28rpx;
  /* 增大间距 */
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}
.user-name {
  font-size: 36rpx;
  /* 增大字体 */
  font-weight: 600;
  /* 增强字重 */
  color: #1A1A1A;
  /* 苹果深灰色 */
  margin-bottom: 10rpx;
  letter-spacing: 0.5px;
}
.user-desc {
  font-size: 26rpx;
  color: #8E8E93;
  /* 苹果次要文本颜色 */
  letter-spacing: 0.3px;
}

/* 区域卡片样式 */
.section-card {
  background-color: #FFFFFF;
  border-radius: 32rpx;
  /* 更大圆角 */
  margin: 24rpx;
  padding: 28rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06), 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
  /* 双层阴影增强立体感 */
  position: relative;
  overflow: hidden;
  border: 0.5px solid rgba(255, 255, 255, 0.8);
  /* 微妙的边框 */
  transition: transform 0.2s, box-shadow 0.2s;
}
.section-card:active {
  transform: scale(0.99);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.04);
}
.section-header {
  padding: 10rpx 16rpx 20rpx;
  border-bottom: 0.5px solid rgba(60, 60, 67, 0.08);
  /* 更薄的分隔线 */
  margin-bottom: 16rpx;
  position: relative;
}
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  /* 苹果风格中等字重 */
  color: #1A1A1A;
  /* 苹果深灰色 */
  position: relative;
  padding-left: 20rpx;
  letter-spacing: 0.5px;
}
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 24rpx;
  background: linear-gradient(to bottom, #0A84FF, #0066FF);
  /* 渐变色装饰线 */
  border-radius: 2rpx;
}

/* 服务网格样式 */
.service-grid {
  display: flex;
  flex-direction: column;
  padding: 24rpx 10rpx 10rpx;
}
.service-row {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 36rpx;
  /* 增大行间距 */
}
.service-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15rpx;
}
.service-icon-wrap {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 12rpx;
}
.service-icon-wrap.has-badge .badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  transform: scale(0.8);
}
.service-icon-wrap.activity-icon-wrap .activity-icon-bg {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.3);
}
.service-icon-wrap.benefits-icon-wrap {
  background: linear-gradient(135deg, #FF9500 0%, #FF5E3A 100%);
  box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.3);
}
.service-icon-wrap.points-icon-wrap {
  background: linear-gradient(135deg, #4CD964 0%, #34C759 100%);
  box-shadow: 0 4rpx 8rpx rgba(76, 217, 100, 0.3);
}
.service-icon-wrap.cashback-icon-wrap {
  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
  box-shadow: 0 4rpx 8rpx rgba(0, 122, 255, 0.3);
}
.service-icon-wrap.distribution-icon-wrap {
  background: linear-gradient(135deg, #FF2D55 0%, #FF3B30 100%);
  box-shadow: 0 4rpx 8rpx rgba(255, 45, 85, 0.3);
}
.service-icon-wrap.partner-icon-wrap {
  background: linear-gradient(135deg, #5856D6 0%, #AF52DE 100%);
  box-shadow: 0 4rpx 8rpx rgba(88, 86, 214, 0.3);
}
.service-icon-wrap.franchise-icon-wrap {
  background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);
  box-shadow: 0 4rpx 8rpx rgba(255, 204, 0, 0.3);
}
.has-badge .badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 36rpx;
  height: 36rpx;
  padding: 0 8rpx;
  background-color: #FF3B30;
  /* 苹果红色 */
  color: #FFFFFF;
  font-size: 20rpx;
  font-weight: 600;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(255, 59, 48, 0.3);
  /* 徽章阴影 */
  border: 1px solid #FFFFFF;
  /* 白色边框 */
}
.service-icon {
  width: 52rpx;
  height: 52rpx;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
  /* SVG图标阴影 */
}
.service-name {
  font-size: 26rpx;
  color: #4D4D4D;
  margin-top: 4rpx;
  font-weight: 500;
  /* 稍微加粗 */
}

/* 工具项样式 */
.tool-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 28rpx;
  margin: 8rpx 16rpx;
  border-radius: 24rpx;
  /* 增大圆角 */
  transition: all 0.2s ease;
  position: relative;
}
.tool-item:active {
  background-color: rgba(0, 0, 0, 0.04);
  /* 点击时的背景变化 */
  transform: scale(0.99);
}
.tool-item:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 80rpx;
  right: 0;
  bottom: 0;
  height: 0.5px;
  background-color: rgba(60, 60, 67, 0.08);
  /* 苹果风格分隔线 */
}
.tool-left {
  display: flex;
  align-items: center;
}
.tool-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 24rpx;
  background: linear-gradient(135deg, #F0F6FF, #E6F0FF);
  /* 渐变背景 */
  border-radius: 16rpx;
  /* 增大圆角 */
  padding: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  /* 微妙的阴影 */
}
.tool-name {
  font-size: 30rpx;
  color: #1A1A1A;
  /* 苹果深灰色 */
  font-weight: 500;
  /* 稍微加粗 */
}
.tool-badge {
  padding: 6rpx 14rpx;
  background-color: #FF3B30;
  color: #fff;
  font-size: 20rpx;
  font-weight: 600;
  border-radius: 20rpx;
  margin-right: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 59, 48, 0.2);
  /* 徽章阴影 */
}
.arrow-icon {
  width: 28rpx;
  height: 28rpx;
  transform: rotate(90deg);
  opacity: 0.3;
}

/* 授权弹窗样式 */
.auth-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  /* 更深的半透明背景 */
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(10px);
  /* 苹果风格模糊背景 */
  -webkit-backdrop-filter: blur(10px);
}
.auth-container {
  width: 600rpx;
  background-color: rgba(255, 255, 255, 0.95);
  /* 半透明背景 */
  border-radius: 32rpx;
  /* 更大圆角 */
  overflow: hidden;
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.15);
  /* 明显的卡片阴影 */
  border: 0.5px solid rgba(255, 255, 255, 0.8);
  /* 微妙的边框 */
  animation: authPopIn 0.3s ease-out;
  /* 弹出动画 */
}
@keyframes authPopIn {
from {
    opacity: 0;
    transform: scale(0.9);
}
to {
    opacity: 1;
    transform: scale(1);
}
}
.auth-header {
  position: relative;
  padding: 32rpx;
  border-bottom: 0.5px solid rgba(60, 60, 67, 0.1);
  /* 苹果风格分隔线 */
  text-align: center;
}
.auth-title {
  font-size: 34rpx;
  font-weight: 600;
  /* 苹果字重 */
  color: #1A1A1A;
  /* 苹果深灰色 */
  letter-spacing: 0.5px;
}
.close-btn {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #8E8E93;
  /* 苹果次要文本颜色 */
  border-radius: 22rpx;
  background-color: rgba(60, 60, 67, 0.05);
  transition: all 0.2s ease;
}
.close-btn:active {
  background-color: rgba(60, 60, 67, 0.1);
  /* 点击效果 */
  transform: scale(0.95);
}
.auth-content {
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.auth-logo {
  width: 128rpx;
  height: 128rpx;
  margin-bottom: 36rpx;
  border-radius: 64rpx;
  background-color: #f0f0f0;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  /* 增强阴影 */
  border: 3rpx solid #FFFFFF;
}
.auth-desc {
  font-size: 30rpx;
  color: #666666;
  margin-bottom: 48rpx;
  text-align: center;
  line-height: 1.5;
}
.auth-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 24rpx;
}
.success-icon {
  font-size: 64rpx;
  color: #34C759;
  /* 苹果绿色 */
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 6rpx rgba(52, 199, 89, 0.2);
  /* 文字阴影 */
}
.success-text {
  font-size: 32rpx;
  color: #34C759;
  /* 苹果绿色 */
  font-weight: 600;
  /* 苹果字重 */
  letter-spacing: 0.5px;
}

/* 功能入口区样式 */
.function-section {
  background-color: #FFFFFF;
  border-radius: 32rpx;
  /* 更大圆角 */
  margin: 24rpx;
  padding: 28rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06), 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
  /* 双层阴影增强立体感 */
  position: relative;
  overflow: hidden;
  border: 0.5px solid rgba(255, 255, 255, 0.8);
  /* 微妙的边框 */
}
.function-row {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 24rpx;
}
.function-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}
.function-icon-wrap {
  position: relative;
  width: 96rpx;
  height: 96rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #F0F6FF, #E6F0FF);
  /* 渐变背景 */
  border-radius: 48rpx;
  /* 完全圆形 */
  transition: all 0.25s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05), 0 1rpx 3rpx rgba(0, 0, 0, 0.03);
  /* 双层阴影 */
  border: 0.5px solid rgba(255, 255, 255, 0.8);
  /* 微妙的边框 */
}
.function-icon-wrap:active {
  transform: scale(0.92);
  /* 点击时的缩放效果 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}
.function-icon {
  width: 52rpx;
  height: 52rpx;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
  /* SVG图标阴影 */
}
.function-name {
  font-size: 26rpx;
  color: #4D4D4D;
  font-weight: 500;
  /* 稍微加粗 */
}

/* 新商家后台入口样式 */
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 28rpx;
  margin: 8rpx 16rpx;
  border-radius: 24rpx;
  /* 增大圆角 */
  transition: all 0.2s ease;
  position: relative;
}
.menu-item:active {
  background-color: rgba(0, 0, 0, 0.04);
  /* 点击时的背景变化 */
  transform: scale(0.99);
}
.menu-item:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 80rpx;
  right: 0;
  bottom: 0;
  height: 0.5px;
  background-color: rgba(60, 60, 67, 0.08);
  /* 苹果风格分隔线 */
}
.menu-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 24rpx;
  background: linear-gradient(135deg, #F0F6FF, #E6F0FF);
  /* 渐变背景 */
  border-radius: 16rpx;
  /* 增大圆角 */
  padding: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  /* 微妙的阴影 */
}
.menu-text {
  font-size: 30rpx;
  color: #1A1A1A;
  /* 苹果深灰色 */
  font-weight: 500;
  /* 稍微加粗 */
}
.menu-badge {
  font-size: 20rpx;
  color: #FF3B30;
  /* 苹果红色 */
  font-weight: 600;
  margin-left: 8rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(255, 59, 48, 0.1);
  border-radius: 10rpx;
}
.menu-arrow {
  width: 28rpx;
  height: 28rpx;
  transform: rotate(90deg);
  opacity: 0.3;
}