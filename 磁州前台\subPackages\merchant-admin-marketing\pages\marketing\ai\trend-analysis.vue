<template>
  <view class="trend-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">消费趋势分析</text>
      <view class="navbar-right">
        <view class="refresh-icon" @click="refreshData">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#FFFFFF">
            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <!-- 数据概览卡片 -->
      <view class="overview-section">
        <view class="overview-header">
          <view class="header-left">
            <text class="header-title">数据概览</text>
            <text class="header-subtitle">最近30天消费趋势</text>
          </view>
          <view class="date-selector" @click="showDatePicker">
            <text class="date-text">{{dateRange}}</text>
            <view class="date-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="#666666">
                <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
              </svg>
            </view>
          </view>
        </view>
        
        <view class="metrics-grid">
          <view class="metric-card">
            <view class="metric-icon sales">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#6366F1">
                <path d="M7 18h2V6H7v12zm4 4h2V2h-2v20zm-8-8h2v-4H3v4zm12 4h2V6h-2v12zm4-8v4h2v-4h-2z"/>
              </svg>
            </view>
            <view class="metric-content">
              <text class="metric-label">总销售额</text>
              <view class="metric-value-container">
                <text class="metric-value">¥ {{formatNumber(overviewData.totalSales)}}</text>
                <view class="metric-trend up">
                  <view class="trend-icon"></view>
                  <text class="trend-value">{{overviewData.salesGrowth}}%</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="metric-card">
            <view class="metric-icon orders">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#FF9500">
                <path d="M18 17H6v-2h12v2zm0-4H6v-2h12v2zm0-4H6V7h12v2zM3 22l1.5-1.5L6 22l1.5-1.5L9 22l1.5-1.5L12 22l1.5-1.5L15 22l1.5-1.5L18 22l1.5-1.5L21 22V2l-1.5 1.5L18 2l-1.5 1.5L15 2l-1.5 1.5L12 2l-1.5 1.5L9 2 7.5 3.5 6 2 4.5 3.5 3 2v20z"/>
              </svg>
            </view>
            <view class="metric-content">
              <text class="metric-label">订单数量</text>
              <view class="metric-value-container">
                <text class="metric-value">{{formatNumber(overviewData.totalOrders)}}</text>
                <view class="metric-trend up">
                  <view class="trend-icon"></view>
                  <text class="trend-value">{{overviewData.ordersGrowth}}%</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="metric-card">
            <view class="metric-icon customers">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#34C759">
                <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
              </svg>
            </view>
            <view class="metric-content">
              <text class="metric-label">客户数量</text>
              <view class="metric-value-container">
                <text class="metric-value">{{formatNumber(overviewData.totalCustomers)}}</text>
                <view class="metric-trend up">
                  <view class="trend-icon"></view>
                  <text class="trend-value">{{overviewData.customersGrowth}}%</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="metric-card">
            <view class="metric-icon average">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#FF3B30">
                <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
              </svg>
            </view>
            <view class="metric-content">
              <text class="metric-label">客单价</text>
              <view class="metric-value-container">
                <text class="metric-value">¥ {{overviewData.averageOrderValue}}</text>
                <view class="metric-trend down">
                  <view class="trend-icon"></view>
                  <text class="trend-value">{{overviewData.aovGrowth}}%</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 分析概述 -->
      <view class="summary-section">
        <view class="summary-header">
          <text class="summary-title">趋势概述</text>
          <view class="date-range">
            <text class="date-text">{{dateRange}}</text>
            <view class="date-icon"></view>
          </view>
        </view>
        
        <view class="summary-content">
          <text class="summary-text">基于最近30天的消费数据分析，您的店铺顾客消费偏好正在发生变化。女性顾客对高端美妆产品的兴趣增加了15%，25-35岁男性对科技产品的购买频率提高了8%。建议调整商品结构，增加相关品类的库存和促销力度。</text>
        </view>
        
        <view class="ai-badge">
          <text class="badge-text">AI分析</text>
          <text class="badge-confidence">可信度: 98%</text>
        </view>
      </view>
      
      <!-- 销售趋势图表 -->
      <view class="chart-section">
        <view class="section-header">
          <view class="header-left">
            <text class="section-title">销售趋势</text>
            <text class="section-subtitle">过去30天销售与订单量变化</text>
          </view>
          <view class="chart-tabs">
            <view 
              v-for="(tab, index) in chartTabs" 
              :key="index" 
              :class="['chart-tab', activeChartTab === index ? 'active' : '']"
              @click="switchChartTab(index)"
            >
              {{tab}}
            </view>
          </view>
        </view>
        
        <view class="chart-container">
          <view class="chart-legend">
            <view class="legend-item">
              <view class="legend-color sales-line"></view>
              <text class="legend-text">销售额</text>
            </view>
            <view class="legend-item">
              <view class="legend-color orders-line"></view>
              <text class="legend-text">订单量</text>
            </view>
          </view>
          
          <view class="line-chart">
            <!-- 图表网格线 -->
            <view class="chart-grid">
              <view class="grid-line" v-for="i in 5" :key="i"></view>
            </view>
            
            <!-- 销售额曲线 -->
            <view class="chart-line sales-line">
              <svg :width="chartWidth" :height="chartHeight" viewBox="0 0 300 150">
                <path :d="salesPath" fill="none" stroke="#6366F1" stroke-width="2" />
                <circle v-for="(point, index) in salesPoints" :key="'sales-point-'+index"
                  :cx="point.x" :cy="point.y" r="4" fill="#6366F1"
                />
              </svg>
            </view>
            
            <!-- 订单量曲线 -->
            <view class="chart-line orders-line">
              <svg :width="chartWidth" :height="chartHeight" viewBox="0 0 300 150">
                <path :d="ordersPath" fill="none" stroke="#FF9500" stroke-width="2" />
                <circle v-for="(point, index) in ordersPoints" :key="'orders-point-'+index"
                  :cx="point.x" :cy="point.y" r="4" fill="#FF9500"
                />
              </svg>
            </view>
          </view>
          
          <!-- X轴标签 -->
          <view class="x-axis">
            <text v-for="(date, index) in chartData.dates" :key="index" class="x-axis-label">{{date}}</text>
          </view>
        </view>
        
        <!-- 数据统计 -->
        <view class="chart-stats">
          <view class="stat-item">
            <text class="stat-label">日均销售额</text>
            <text class="stat-value">¥ {{formatNumber(chartStats.avgDailySales)}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">最高销售日</text>
            <text class="stat-value">{{chartStats.peakSalesDay}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">销售增长率</text>
            <text class="stat-value">{{chartStats.salesGrowthRate}}%</text>
          </view>
        </view>
      </view>
      
      <!-- 消费趋势图表 -->
      <view class="chart-section">
        <view class="section-header">
          <text class="section-title">消费类别趋势</text>
          <view class="filter-dropdown" @click="showCategoryFilter">
            <text class="selected-value">全部类别</text>
            <view class="dropdown-arrow"></view>
          </view>
        </view>
        
        <view class="chart-container">
          <!-- 这里是图表，实际项目中可以使用ECharts等图表库 -->
          <view class="chart-placeholder">
            <view class="chart-bars">
              <view class="chart-bar" style="height: 60%;">
                <text class="bar-label">美妆</text>
              </view>
              <view class="chart-bar" style="height: 45%;">
                <text class="bar-label">服装</text>
              </view>
              <view class="chart-bar" style="height: 75%;">
                <text class="bar-label">科技</text>
              </view>
              <view class="chart-bar" style="height: 30%;">
                <text class="bar-label">家居</text>
              </view>
              <view class="chart-bar" style="height: 50%;">
                <text class="bar-label">食品</text>
              </view>
            </view>
            <view class="chart-legend">
              <view class="legend-item">
                <view class="legend-color"></view>
                <text class="legend-text">消费比例</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 消费者画像变化 -->
      <view class="profile-section">
        <view class="section-header">
          <text class="section-title">消费者画像变化</text>
        </view>
        
        <view class="profile-cards">
          <view class="profile-card">
            <view class="profile-icon gender-female"></view>
            <view class="profile-content">
              <text class="profile-title">女性顾客</text>
              <text class="profile-value">占比 65% <text class="trend-up">↑3%</text></text>
              <view class="profile-tags">
                <text class="profile-tag">25-35岁</text>
                <text class="profile-tag">高端美妆</text>
                <text class="profile-tag">护肤品</text>
              </view>
            </view>
          </view>
          
          <view class="profile-card">
            <view class="profile-icon gender-male"></view>
            <view class="profile-content">
              <text class="profile-title">男性顾客</text>
              <text class="profile-value">占比 35% <text class="trend-down">↓3%</text></text>
              <view class="profile-tags">
                <text class="profile-tag">25-35岁</text>
                <text class="profile-tag">科技产品</text>
                <text class="profile-tag">数码配件</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 智能建议 -->
      <view class="recommendations-section">
        <view class="section-header">
          <text class="section-title">智能建议</text>
        </view>
        
        <view class="recommendation-list">
          <view class="recommendation-item">
            <view class="recommendation-icon stock"></view>
            <view class="recommendation-content">
              <text class="recommendation-title">库存调整</text>
              <text class="recommendation-desc">建议增加高端美妆产品库存20%，减少男士服装库存10%</text>
              <view class="recommendation-actions">
                <button class="action-btn apply" @click="applyRecommendation(0)">应用</button>
                <button class="action-btn ignore" @click="ignoreRecommendation(0)">忽略</button>
              </view>
            </view>
          </view>
          
          <view class="recommendation-item">
            <view class="recommendation-icon promotion"></view>
            <view class="recommendation-content">
              <text class="recommendation-title">促销策略</text>
              <text class="recommendation-desc">针对25-35岁女性顾客推出美妆套装优惠活动，预计可提升销售额15%</text>
              <view class="recommendation-actions">
                <button class="action-btn apply" @click="applyRecommendation(1)">应用</button>
                <button class="action-btn ignore" @click="ignoreRecommendation(1)">忽略</button>
              </view>
            </view>
          </view>
          
          <view class="recommendation-item">
            <view class="recommendation-icon product"></view>
            <view class="recommendation-content">
              <text class="recommendation-title">商品结构</text>
              <text class="recommendation-desc">建议引入3个新的高端美妆品牌，扩充科技产品品类</text>
              <view class="recommendation-actions">
                <button class="action-btn apply" @click="applyRecommendation(2)">应用</button>
                <button class="action-btn ignore" @click="ignoreRecommendation(2)">忽略</button>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部空间 -->
      <view style="height: 20px;"></view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <tab-bar active-tab="marketing" @tab-change="handleTabChange"></tab-bar>
  </view>
</template>

<script>
import TabBar from '../../../../../components/TabBar.vue'

export default {
  components: {
    TabBar
  },
  data() {
    return {
      dateRange: '2023/10/01 - 2023/10/30',
      overviewData: {
        totalSales: 128650,
        salesGrowth: 15.8,
        totalOrders: 1256,
        ordersGrowth: 12.3,
        totalCustomers: 876,
        customersGrowth: 8.5,
        averageOrderValue: 102.43,
        aovGrowth: -2.1
      },
      isLoading: false,
      chartTabs: ['日', '周', '月'],
      activeChartTab: 0,
      chartWidth: 300,
      chartHeight: 150,
      salesPath: '',
      ordersPath: '',
      salesPoints: [],
      ordersPoints: [],
      chartData: {
        dates: ['10/01', '10/05', '10/10', '10/15', '10/20', '10/25', '10/30'],
        sales: [5000, 8000, 7500, 12000, 9000, 15000, 13000],
        orders: [50, 80, 75, 120, 90, 150, 130]
      },
      chartStats: {
        avgDailySales: 4288.33,
        peakSalesDay: '10月25日',
        salesGrowthRate: 15.8
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    refreshData() {
      this.isLoading = true;
      
      // 模拟数据刷新
      setTimeout(() => {
        this.isLoading = false;
        uni.showToast({
          title: '数据已更新',
          icon: 'success'
        });
      }, 1500);
    },
    showCategoryFilter() {
      uni.showActionSheet({
        itemList: ['全部类别', '美妆', '服装', '科技', '家居', '食品'],
        success: (res) => {
          // 处理选择结果
        }
      });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    switchChartTab(index) {
      this.activeChartTab = index;
      // 在实际项目中，这里应该根据选择的时间段重新加载图表数据
      uni.showToast({
        title: '切换到' + this.chartTabs[index] + '视图',
        icon: 'none'
      });
      // 重新计算路径
      this.calculateChartPaths();
    },
    // 计算销售额和订单量的SVG路径
    calculateChartPaths() {
      const salesData = this.chartData.sales;
      const ordersData = this.chartData.orders;
      const maxSales = Math.max(...salesData);
      const maxOrders = Math.max(...ordersData);
      
      // 计算销售额点位置
      this.salesPoints = salesData.map((value, index) => {
        const x = (index / (salesData.length - 1)) * this.chartWidth;
        const y = this.chartHeight - (value / maxSales) * this.chartHeight;
        return { x, y };
      });
      
      // 计算订单量点位置
      this.ordersPoints = ordersData.map((value, index) => {
        const x = (index / (ordersData.length - 1)) * this.chartWidth;
        const y = this.chartHeight - (value / maxOrders) * this.chartHeight;
        return { x, y };
      });
      
      // 生成销售额路径
      this.salesPath = this.generatePath(this.salesPoints);
      
      // 生成订单量路径
      this.ordersPath = this.generatePath(this.ordersPoints);
    },
    // 生成SVG路径
    generatePath(points) {
      if (points.length === 0) return '';
      
      let path = `M ${points[0].x} ${points[0].y}`;
      
      for (let i = 1; i < points.length; i++) {
        // 使用贝塞尔曲线使线条更平滑
        const cp1x = points[i-1].x + (points[i].x - points[i-1].x) / 3;
        const cp1y = points[i-1].y;
        const cp2x = points[i].x - (points[i].x - points[i-1].x) / 3;
        const cp2y = points[i].y;
        path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${points[i].x} ${points[i].y}`;
      }
      
      return path;
    },
    applyRecommendation(index) {
      uni.showToast({
        title: '已应用建议' + (index + 1),
        icon: 'success'
      })
    },
    
    ignoreRecommendation(index) {
      uni.showToast({
        title: '已忽略建议' + (index + 1),
        icon: 'none'
      })
    },
    
    handleTabChange(tabId) {
      // 处理底部标签页切换事件
      console.log('切换到标签:', tabId);
    }
  },
  mounted() {
    // 初始化图表
    this.calculateChartPaths();
  }
}
</script>

<style lang="scss">
.trend-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #6366F1, #4F46E5);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(99, 102, 241, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-content {
  height: calc(100vh - 77px);
  box-sizing: border-box;
  padding: 15px;
}

/* 数据概览部分样式 */
.overview-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.header-subtitle {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}

.date-selector {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  padding: 6px 12px;
  border-radius: 16px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  display: flex;
  align-items: center;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.metric-card {
  background-color: #F8FAFC;
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: flex-start;
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.metric-icon.sales {
  background-color: rgba(99, 102, 241, 0.1);
}

.metric-icon.orders {
  background-color: rgba(255, 149, 0, 0.1);
}

.metric-icon.customers {
  background-color: rgba(52, 199, 89, 0.1);
}

.metric-icon.average {
  background-color: rgba(255, 59, 48, 0.1);
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: block;
}

.metric-value-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.metric-trend {
  display: flex;
  align-items: center;
}

.trend-icon {
  width: 0;
  height: 0;
  margin-right: 2px;
}

.metric-trend.up .trend-icon {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.metric-trend.down .trend-icon {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

.trend-value {
  font-size: 12px;
}

.metric-trend.up .trend-value {
  color: #34C759;
}

.metric-trend.down .trend-value {
  color: #FF3B30;
}

.summary-section {
  margin: 15px;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.summary-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.date-range {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

.summary-content {
  margin-bottom: 20px;
}

.summary-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.ai-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #1989FA, #0D6EFD);
  border-radius: 15px;
  padding: 5px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.badge-text {
  font-size: 12px;
  font-weight: 600;
  color: #FFFFFF;
}

.badge-confidence {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
}

.chart-section, .profile-section, .recommendations-section {
  margin: 15px;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}

.filter-dropdown {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.selected-value {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.dropdown-arrow {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

.chart-container {
  height: 250px;
  position: relative;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  padding: 0 10px;
}

.chart-bar {
  width: 40px;
  background: linear-gradient(to top, #1989FA, #0D6EFD);
  border-radius: 5px 5px 0 0;
  position: relative;
  display: flex;
  justify-content: center;
}

.bar-label {
  position: absolute;
  bottom: -25px;
  font-size: 12px;
  color: #666;
}

.chart-legend {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.legend-item {
  display: flex;
  align-items: center;
}

.legend-color {
  width: 12px;
  height: 12px;
  background: linear-gradient(to right, #1989FA, #0D6EFD);
  border-radius: 2px;
  margin-right: 5px;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

.profile-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.profile-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
}

.profile-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
  position: relative;
}

.profile-icon.gender-female {
  background-color: rgba(255, 105, 180, 0.1);
}

.profile-icon.gender-female::before {
  content: '♀';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #FF69B4;
}

.profile-icon.gender-male {
  background-color: rgba(25, 137, 250, 0.1);
}

.profile-icon.gender-male::before {
  content: '♂';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #1989FA;
}

.profile-content {
  flex: 1;
}

.profile-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.profile-value {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.trend-up {
  color: #34C759;
}

.trend-down {
  color: #FF3B30;
}

.profile-tags {
  display: flex;
  flex-wrap: wrap;
}

.profile-tag {
  font-size: 10px;
  color: #666;
  background: #F5F7FA;
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.recommendation-list {
  margin-top: 10px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  margin-bottom: 15px;
  background: #F8FAFC;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.recommendation-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 15px;
  position: relative;
}

.recommendation-icon.stock {
  background-color: rgba(25, 137, 250, 0.1);
}

.recommendation-icon.stock::before {
  content: '📦';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}

.recommendation-icon.promotion {
  background-color: rgba(255, 149, 0, 0.1);
}

.recommendation-icon.promotion::before {
  content: '🏷️';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}

.recommendation-icon.product {
  background-color: rgba(52, 199, 89, 0.1);
}

.recommendation-icon.product::before {
  content: '🛍️';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.recommendation-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.recommendation-actions {
  display: flex;
}

.action-btn {
  padding: 5px 15px;
  border-radius: 15px;
  font-size: 12px;
  margin-right: 10px;
  border: none;
}

.action-btn.apply {
  background: #1989FA;
  color: white;
}

.action-btn.ignore {
  background: #F5F7FA;
  color: #666;
}

.chart-tabs {
  display: flex;
  align-items: center;
}

.chart-tab {
  padding: 5px 10px;
  border-radius: 15px;
  background: #F5F7FA;
  margin-left: 5px;
  cursor: pointer;
}

.chart-tab.active {
  background: #1989FA;
  color: white;
}

.line-chart {
  position: relative;
  height: 180px;
  margin: 20px 0;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.grid-line {
  position: absolute;
  width: 100%;
  height: 1px;
  background: #E0E0E0;
}

.grid-line:nth-child(1) { top: 0; }
.grid-line:nth-child(2) { top: 25%; }
.grid-line:nth-child(3) { top: 50%; }
.grid-line:nth-child(4) { top: 75%; }
.grid-line:nth-child(5) { top: 100%; }

.chart-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.legend-color.sales-line {
  background-color: #6366F1;
}

.legend-color.orders-line {
  background-color: #FF9500;
}

.x-axis {
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  margin-top: 10px;
}

.x-axis-label {
  font-size: 12px;
  color: #999;
  text-align: center;
  flex: 1;
}

.chart-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #EEEEEE;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
</style> 