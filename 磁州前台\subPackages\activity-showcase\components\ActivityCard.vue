<template>
  <view 
    class="activity-card" 
    :class="[`card-type-${activity.type}`]" 
    @tap="viewActivity"
  >
    <view class="card-type-indicator"></view>
    <view class="card-content">
      <view class="card-image-container">
        <image class="card-image" :src="activity.coverImage" mode="aspectFill"></image>
        
        <!-- 各种活动标签 -->
        <view class="card-tags">
          <!-- 秒杀标签 -->
          <view class="card-tag tag-flash" v-if="activity.type === 'flash'">
            <view class="tag-icon">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            </view>
            <text>秒杀</text>
          </view>
          
          <!-- 拼团标签 -->
          <view class="card-tag tag-group" v-if="activity.type === 'group'">
            <view class="tag-icon">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            </view>
            <text>拼团</text>
          </view>
          
          <!-- 优惠券标签 -->
          <view class="card-tag tag-coupon" v-if="activity.type === 'coupon'">
            <view class="tag-icon">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 12v5a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <polyline points="22 7 12 17 2 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polyline>
            </svg>
            </view>
            <text>优惠券</text>
          </view>
          
          <!-- 满减标签 -->
          <view class="card-tag tag-discount" v-if="activity.type === 'discount'">
            <view class="tag-icon">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <line x1="7" y1="7" x2="7.01" y2="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            </svg>
            </view>
            <text>满减</text>
          </view>
        </view>
        
        <!-- 价格标签 -->
        <view class="card-price" v-if="activity.currentPrice && activity.type !== 'coupon'">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{ activity.currentPrice }}</text>
          <text class="price-original" v-if="activity.originalPrice">¥{{ activity.originalPrice }}</text>
          <view class="price-discount" v-if="activity.originalPrice && activity.currentPrice">
            {{ Math.round((1 - activity.currentPrice / activity.originalPrice) * 100) }}% OFF
          </view>
        </view>
        
        <!-- 库存和销量信息 -->
        <view class="card-stock-info" v-if="activity.soldCount && activity.totalCount">
          <view class="stock-progress">
            <view class="progress-inner" :style="{width: `${Math.min(100, (activity.soldCount / activity.totalCount) * 100)}%`}"></view>
          </view>
          <view class="stock-text">
            <text>已售{{ activity.soldCount }}件</text>
            <text>剩余{{ activity.totalCount - activity.soldCount }}件</text>
          </view>
        </view>
      </view>
      
      <view class="card-info">
        <view class="card-title">{{ activity.title }}</view>
        
        <!-- 活动特定内容 -->
        <view class="card-special-content">
          <!-- 拼团特有内容 -->
          <view class="group-content" v-if="activity.type === 'group'">
            <view class="group-price-compare">
              <view class="group-price">
                <text class="price-now">¥{{ activity.currentPrice }}</text>
                <text class="price-was">¥{{ activity.originalPrice }}</text>
              </view>
              <view class="group-save">
                <text>立省{{ activity.originalPrice && activity.currentPrice ? (activity.originalPrice - activity.currentPrice).toFixed(2) : '0' }}元</text>
              </view>
            </view>
            <view class="group-status" v-if="activity.groupSize">
              <view class="group-users">
                <view class="user-avatars">
                  <view class="avatar-placeholder" v-for="i in Math.min(3, activity.soldCount || 0)" :key="i"></view>
                </view>
                <text>{{ activity.soldCount || 0 }}人已参团</text>
              </view>
              <view class="group-remaining">
                <text>还差{{ activity.groupSize - (activity.soldCount % activity.groupSize || activity.groupSize) }}人成团</text>
              </view>
            </view>
          </view>
          
          <!-- 秒杀特有内容 -->
          <view class="flash-content" v-if="activity.type === 'flash'">
            <view class="flash-countdown" v-if="activity.endTime">
              <text class="countdown-label">距结束</text>
              <view class="countdown-boxes">
                <view class="countdown-box">
                  <text>{{ getHours(activity.endTime) }}</text>
                </view>
                <text>:</text>
                <view class="countdown-box">
                  <text>{{ getMinutes(activity.endTime) }}</text>
                </view>
                <text>:</text>
                <view class="countdown-box">
                  <text>{{ getSeconds(activity.endTime) }}</text>
                </view>
              </view>
            </view>
            <view class="flash-limit" v-if="activity.userLimit">
              <text>限购{{ activity.userLimit }}件</text>
            </view>
          </view>
          
          <!-- 优惠券特有内容 -->
          <view class="coupon-content" v-if="activity.type === 'coupon'">
            <view class="coupon-shape">
              <view class="coupon-left">
                <view class="coupon-amount">
                  <text class="amount-symbol">¥</text>
                  <text class="amount-value">{{ activity.currentPrice || '0' }}</text>
                </view>
                <view class="coupon-condition" v-if="activity.useCondition">
                  <text>{{ activity.useCondition }}</text>
                </view>
              </view>
              <view class="coupon-divider">
                <view class="divider-circle divider-top"></view>
                <view class="divider-line"></view>
                <view class="divider-circle divider-bottom"></view>
              </view>
              <view class="coupon-right">
                <view class="coupon-desc">{{ activity.title }}</view>
                <view class="coupon-validity" v-if="activity.validityPeriod">
                  <text>{{ activity.validityPeriod }}</text>
                </view>
                <view class="coupon-btn">
                  <text>立即领取</text>
                </view>
              </view>
          </view>
        </view>
        
          <!-- 满减特有内容 -->
          <view class="discount-content" v-if="activity.type === 'discount'">
            <view class="discount-rules" v-if="activity.discountRules && activity.discountRules.length > 0">
              <view class="discount-rule" v-for="(rule, idx) in activity.discountRules" :key="idx">
                <text>满{{ rule.threshold }}减{{ rule.discount }}</text>
              </view>
            </view>
            <view class="discount-rule" v-else-if="activity.threshold && activity.discount">
              <text>满{{ activity.threshold }}减{{ activity.discount }}</text>
            </view>
            <view class="discount-validity" v-if="activity.validityPeriod">
              <text>{{ activity.validityPeriod }}</text>
            </view>
          </view>
        </view>
        
        <view class="card-meta">
          <view class="card-location" v-if="activity.location">
            <svg class="icon" viewBox="0 0 24 24" width="14" height="14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
            </svg>
            <text>{{ activity.location }}</text>
          </view>
          <view class="card-date" v-if="activity.date">
            <svg class="icon" viewBox="0 0 24 24" width="14" height="14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
              <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            </svg>
            <text>{{ activity.date }}</text>
          </view>
        </view>
      </view>
      
      <view class="card-action">
        <view class="action-button" :class="[`button-${activity.type}`]">
          <text v-if="activity.type === 'flash'">立即抢购</text>
          <text v-else-if="activity.type === 'group'">去拼团</text>
          <text v-else-if="activity.type === 'coupon'">领取优惠券</text>
          <text v-else-if="activity.type === 'discount'">立享优惠</text>
          <text v-else>查看详情</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ActivityCard',
  props: {
    activity: {
      type: Object,
      required: true
    }
  },
  methods: {
    viewActivity() {
      console.log('ActivityCard 被点击，准备发送点击事件', this.activity.id, this.activity.type);
      // 根据活动类型确定跳转路径
      let url = '';
      
      switch(this.activity.type) {
        case 'flash':
          url = `/subPackages/activity-showcase/pages/flash-sale/detail?id=${this.activity.id}`;
          break;
        case 'group':
          url = `/subPackages/activity-showcase/pages/group-buy/detail?id=${this.activity.id}`;
          break;
        case 'discount':
          url = `/subPackages/activity-showcase/pages/discount/detail?id=${this.activity.id}`;
          break;
        case 'coupon':
          url = `/subPackages/activity-showcase/pages/coupon/detail?id=${this.activity.id}`;
          break;
        default:
          url = `/subPackages/activity-showcase/pages/detail/index?id=${this.activity.id}&type=${this.activity.type}`;
      }
      
      // 导航到详情页
      uni.navigateTo({
        url: url,
        fail: (err) => {
          console.error('导航失败:', err);
          this.$emit('click', this.activity); // 如果导航失败，触发点击事件由父组件处理
        }
      });
    },
    
    // 倒计时辅助函数
    getHours(endTime) {
      const now = new Date();
      const end = new Date(endTime);
      const diff = end - now;
      if (diff <= 0) return '00';
      return Math.floor(diff / (1000 * 60 * 60)).toString().padStart(2, '0');
    },
    
    getMinutes(endTime) {
      const now = new Date();
      const end = new Date(endTime);
      const diff = end - now;
      if (diff <= 0) return '00';
      return Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60)).toString().padStart(2, '0');
    },
    
    getSeconds(endTime) {
      const now = new Date();
      const end = new Date(endTime);
      const diff = end - now;
      if (diff <= 0) return '00';
      return Math.floor((diff % (1000 * 60)) / 1000).toString().padStart(2, '0');
    }
  }
};
</script>

<style lang="scss" scoped>
.activity-card {
  position: relative;
  width: 100%;
  border-radius: 35rpx;
  background: #fff;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  transform: perspective(1000px) rotateX(0deg);
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:active {
    transform: perspective(1000px) rotateX(2deg) scale(0.98);
  }
  
  .card-type-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, #FF9500, #FF2D55);
    z-index: 1;
  }
  
  .card-content {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
    
    .card-image-container {
      position: relative;
      width: 100%;
      height: 350rpx;
      overflow: hidden;
      
      .card-image {
        width: 100%;
        height: 100%;
        transition: transform 0.3s ease;
      }
      
      .card-tags {
        position: absolute;
        top: 20rpx;
        left: 20rpx;
        display: flex;
        flex-wrap: wrap;
        gap: 10rpx;
        
        .card-tag {
          display: flex;
          align-items: center;
          padding: 6rpx 16rpx;
          border-radius: 20rpx;
          font-size: 22rpx;
          font-weight: 600;
          color: #fff;
          box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
          
          .tag-icon {
            margin-right: 5rpx;
            display: flex;
            align-items: center;
          }
          
          &.tag-flash {
            background: linear-gradient(135deg, #FF2D55, #FF3B30);
          }
          
          &.tag-group {
            background: linear-gradient(135deg, #34C759, #30B0C1);
          }
          
          &.tag-coupon {
            background: linear-gradient(135deg, #AF52DE, #5856D6);
          }
          
          &.tag-discount {
            background: linear-gradient(135deg, #FF9500, #FF6B22);
          }
        }
      }
      
      .card-price {
        position: absolute;
        bottom: 20rpx;
        left: 20rpx;
        display: flex;
        align-items: baseline;
        background: rgba(0, 0, 0, 0.6);
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
        
        .price-symbol {
          font-size: 24rpx;
          color: #fff;
          margin-right: 2rpx;
        }
        
        .price-value {
          font-size: 36rpx;
          font-weight: 700;
          color: #fff;
        }
        
        .price-original {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.7);
          text-decoration: line-through;
          margin-left: 10rpx;
        }
        
        .price-discount {
          margin-left: 10rpx;
          background: #FF3B30;
          padding: 4rpx 10rpx;
          border-radius: 10rpx;
          font-size: 20rpx;
          color: #fff;
          font-weight: 600;
        }
      }
      
      .card-stock-info {
        position: absolute;
        bottom: 20rpx;
        right: 20rpx;
        width: 200rpx;
        
        .stock-progress {
          height: 8rpx;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 4rpx;
          overflow: hidden;
          
          .progress-inner {
            height: 100%;
            background: #FF3B30;
            border-radius: 4rpx;
          }
        }
        
        .stock-text {
          display: flex;
          justify-content: space-between;
          margin-top: 6rpx;
          font-size: 18rpx;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
    
    .card-info {
      padding: 20rpx;
      
      .card-title {
        font-size: 32rpx;
    font-weight: 600;
    color: #333;
        margin-bottom: 15rpx;
        line-height: 1.3;
      }
      
      .card-special-content {
        margin-bottom: 20rpx;
        
        // 拼团内容样式
        .group-content {
          .group-price-compare {
    display: flex;
    align-items: center;
            margin-bottom: 15rpx;
  
            .group-price {
    display: flex;
    align-items: baseline;
    
              .price-now {
                font-size: 36rpx;
                font-weight: 700;
                color: #FF3B30;
              }
              
              .price-was {
                font-size: 24rpx;
                color: #999;
                text-decoration: line-through;
                margin-left: 10rpx;
              }
            }
            
            .group-save {
              margin-left: 15rpx;
              background: rgba(255, 59, 48, 0.1);
              padding: 4rpx 10rpx;
              border-radius: 10rpx;
      font-size: 22rpx;
              color: #FF3B30;
            }
          }
          
          .group-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .group-users {
              display: flex;
              align-items: center;
              
              .user-avatars {
                display: flex;
                margin-right: 10rpx;
                
                .avatar-placeholder {
                  width: 40rpx;
                  height: 40rpx;
                  border-radius: 50%;
                  background: linear-gradient(135deg, #34C759, #30B0C1);
                  margin-left: -10rpx;
                  border: 2rpx solid #fff;
                  
                  &:first-child {
                    margin-left: 0;
                  }
                }
              }
              
              text {
                font-size: 24rpx;
                color: #666;
              }
            }
            
            .group-remaining {
              font-size: 24rpx;
              color: #FF3B30;
              font-weight: 500;
            }
          }
        }
        
        // 秒杀内容样式
        .flash-content {
          .flash-countdown {
            display: flex;
            align-items: center;
            margin-bottom: 15rpx;
            
            .countdown-label {
              font-size: 24rpx;
              color: #666;
              margin-right: 10rpx;
            }
            
            .countdown-boxes {
    display: flex;
    align-items: center;
              
              .countdown-box {
                width: 50rpx;
    height: 50rpx;
                background: #333;
                border-radius: 8rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                
                text {
                  color: #fff;
                  font-size: 26rpx;
                  font-weight: 600;
                }
              }
              
              text {
                margin: 0 5rpx;
                color: #333;
                font-weight: 600;
              }
            }
          }
          
          .flash-limit {
    font-size: 24rpx;
            color: #FF3B30;
            background: rgba(255, 59, 48, 0.1);
            padding: 4rpx 10rpx;
            border-radius: 10rpx;
            display: inline-block;
          }
        }
        
        // 优惠券内容样式
        .coupon-content {
          .coupon-shape {
            display: flex;
            background: linear-gradient(135deg, #AF52DE, #5856D6);
            border-radius: 16rpx;
            overflow: hidden;
            height: 180rpx;
            
            .coupon-left {
              width: 35%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              padding: 20rpx;
              
              .coupon-amount {
                display: flex;
                align-items: baseline;
    color: #fff;
                
                .amount-symbol {
                  font-size: 28rpx;
                }
                
                .amount-value {
                  font-size: 60rpx;
                  font-weight: 700;
                  line-height: 1;
                }
              }
              
              .coupon-condition {
                font-size: 20rpx;
                color: rgba(255, 255, 255, 0.9);
                margin-top: 10rpx;
                text-align: center;
              }
            }
            
            .coupon-divider {
              position: relative;
              width: 20rpx;
              
              .divider-circle {
                position: absolute;
                width: 30rpx;
                height: 30rpx;
                background: #fff;
                border-radius: 50%;
                left: -15rpx;
              }
              
              .divider-top {
                top: -15rpx;
              }
              
              .divider-bottom {
                bottom: -15rpx;
              }
              
              .divider-line {
                position: absolute;
                top: 15rpx;
                bottom: 15rpx;
                left: 0;
                width: 0;
                border-left: 2rpx dashed rgba(255, 255, 255, 0.5);
              }
            }
            
            .coupon-right {
              flex: 1;
              padding: 20rpx;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              
              .coupon-desc {
                font-size: 28rpx;
                color: #fff;
                font-weight: 600;
              }
              
              .coupon-validity {
                font-size: 20rpx;
                color: rgba(255, 255, 255, 0.8);
              }
              
              .coupon-btn {
                align-self: flex-start;
                background: rgba(255, 255, 255, 0.2);
                border: 1rpx solid rgba(255, 255, 255, 0.5);
                border-radius: 30rpx;
                padding: 6rpx 20rpx;
                
                text {
                  color: #fff;
                  font-size: 24rpx;
                }
              }
            }
          }
        }
        
        // 满减内容样式
        .discount-content {
          .discount-rules {
            display: flex;
            flex-wrap: wrap;
            gap: 10rpx;
            margin-bottom: 10rpx;
          }
          
          .discount-rule {
            background: rgba(255, 149, 0, 0.1);
            color: #FF9500;
            padding: 6rpx 16rpx;
            border-radius: 20rpx;
            font-size: 24rpx;
            font-weight: 500;
            display: inline-block;
          }
          
          .discount-validity {
            font-size: 22rpx;
            color: #999;
            margin-top: 10rpx;
          }
        }
      }
      
      .card-meta {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        
        .card-location, .card-date {
          display: flex;
          align-items: center;
          font-size: 24rpx;
          color: #999;
          margin-right: 20rpx;
          
          .icon {
            margin-right: 5rpx;
          }
        }
      }
    }
    
    .card-action {
      padding: 0 20rpx 20rpx;
      
      .action-button {
        width: 100%;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #fff;
        box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        
        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
        }
        
        &.button-flash {
          background: linear-gradient(135deg, #FF2D55, #FF3B30);
        }
        
        &.button-group {
          background: linear-gradient(135deg, #34C759, #30B0C1);
        }
        
        &.button-coupon {
          background: linear-gradient(135deg, #AF52DE, #5856D6);
        }
        
        &.button-discount {
          background: linear-gradient(135deg, #FF9500, #FF6B22);
        }
        
        &.button-default {
          background: linear-gradient(135deg, #007AFF, #5AC8FA);
        }
      }
    }
  }
  
  // 不同类型卡片的特殊样式
  &.card-type-flash {
    .card-type-indicator {
      background: linear-gradient(90deg, #FF2D55, #FF3B30);
    }
  }
  
  &.card-type-group {
    .card-type-indicator {
      background: linear-gradient(90deg, #34C759, #30B0C1);
    }
  }
  
  &.card-type-coupon {
    .card-type-indicator {
      background: linear-gradient(90deg, #AF52DE, #5856D6);
    }
  }
  
  &.card-type-discount {
    .card-type-indicator {
      background: linear-gradient(90deg, #FF9500, #FF6B22);
    }
  }
}
</style> 