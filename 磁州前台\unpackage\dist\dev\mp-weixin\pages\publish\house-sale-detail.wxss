
.house-sale-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 150rpx;
}
.house-sale-wrapper {
  padding: 150rpx 20rpx 120rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-top: 20rpx; /* 添加与标题栏的间隙 */
}

/* 房屋基本信息卡片 */
.house-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.house-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.house-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}
.house-meta {
  margin-bottom: 24rpx;
}
.house-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}
.house-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}
.house-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.house-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}
.house-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.house-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}
.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 房屋配置 */
.config-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12rpx;
}
.config-item {
  width: 33.33%;
  padding: 12rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.config-icon {
  font-size: 32rpx;
  color: #1890ff;
  margin-right: 8rpx;
}
.config-text {
  font-size: 26rpx;
  color: #666;
}

/* 房屋详情 */
.detail-list {
  display: flex;
  flex-direction: column;
}
.detail-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.detail-item:last-child {
  border-bottom: none;
}
.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 位置信息 */
.location-content {
  display: flex;
  flex-direction: column;
}
.location-address {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.address-icon {
  font-size: 32rpx;
  color: #1890ff;
  margin-right: 8rpx;
}
.address-text {
  font-size: 28rpx;
  color: #333;
}
.location-map {
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}
.map {
  width: 100%;
  height: 100%;
}
.location-surroundings {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}
.surrounding-item {
  width: 50%;
  padding: 8rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.surrounding-icon {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 8rpx;
}
.surrounding-text {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}
.surrounding-distance {
  font-size: 24rpx;
  color: #999;
}

/* 交易信息 */
.transaction-content {
  display: flex;
  flex-direction: column;
}
.transaction-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.transaction-item:last-child {
  border-bottom: none;
}
.transaction-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.transaction-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 产权信息 */
.property-content {
  display: flex;
  flex-direction: column;
}
.property-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.property-item:last-child {
  border-bottom: none;
}
.property-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.property-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 房源描述 */
.description-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 业主信息 */
.owner-header {
  display: flex;
  align-items: center;
}
.owner-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}
.owner-avatar image {
  width: 100%;
  height: 100%;
}
.owner-info {
  flex: 1;
}
.owner-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.owner-meta {
  display: flex;
  align-items: center;
}
.owner-type, .owner-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 联系方式 */
.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.contact-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.contact-item {
  display: flex;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.contact-item:last-child {
  border-bottom: none;
}
.contact-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.contact-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.contact-phone {
  color: #1890ff;
}
.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}
.tips-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 8rpx;
}
.tips-text {
  font-size: 24rpx;
  color: #999;
}

/* 相关推荐模块 */
.related-houses-card {
  margin-top: 24rpx;
}
.related-houses-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.related-houses-list {
  margin-bottom: 24rpx;
}
.related-house-item {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.house-item-content {
  display: flex;
  align-items: center;
}
.house-item-left {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}
.house-item-middle {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.house-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.house-item-meta {
  font-size: 24rpx;
  color: #999;
}
.house-item-tags {
  display: flex;
  flex-wrap: wrap;
}
.house-item-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
}
.house-item-tag-more {
  font-size: 24rpx;
  color: #999;
}
.house-item-right {
  width: 160rpx;
  font-size: 28rpx;
  color: #ff4d4f;
  font-weight: 500;
}
.empty-related-houses {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: #fff;
  border-radius: 12rpx;
}
.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 16rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.view-more-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
}
.view-more-text {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 8rpx;
}
.view-more-icon {
  font-size: 28rpx;
  color: #1890ff;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}
.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}
.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.toolbar-text {
  font-size: 22rpx;
  color: #666;
}
.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}
.share-button::after {
  display: none;
}
.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}
.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 原来的section-title样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 20px;
  height: 20px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}
.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
