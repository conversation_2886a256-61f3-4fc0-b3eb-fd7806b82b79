<template>
  <view class="apply-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">申请成为分销员</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 申请条件卡片 -->
    <view class="conditions-card">
      <view class="card-header">
        <text class="card-title">申请条件</text>
      </view>
      
      <view class="conditions-list">
        <view class="condition-item" :class="{ 'satisfied': conditionStatus.purchase }">
          <view class="condition-icon" :class="{ 'satisfied': conditionStatus.purchase }"></view>
          <view class="condition-content">
            <text class="condition-title">{{conditions.requirePurchase ? '购买任意商品' : '无需购买商品'}}</text>
            <text class="condition-desc" v-if="conditions.requirePurchase">需购买满{{conditions.minimumPurchase}}元商品</text>
          </view>
          <view class="condition-status">
            <text v-if="conditionStatus.purchase">已满足</text>
            <button v-else class="action-btn" @click="navigateToShop">去购买</button>
          </view>
        </view>
        
        <view class="condition-item" :class="{ 'satisfied': conditionStatus.realName }">
          <view class="condition-icon" :class="{ 'satisfied': conditionStatus.realName }"></view>
          <view class="condition-content">
            <text class="condition-title">{{conditions.needRealName ? '实名认证' : '无需实名认证'}}</text>
            <text class="condition-desc" v-if="conditions.needRealName">需完成实名认证</text>
          </view>
          <view class="condition-status">
            <text v-if="conditionStatus.realName">已认证</text>
            <button v-else class="action-btn" @click="navigateToRealName">去认证</button>
          </view>
        </view>
        
        <view class="condition-item" :class="{ 'satisfied': conditionStatus.mobile }">
          <view class="condition-icon" :class="{ 'satisfied': conditionStatus.mobile }"></view>
          <view class="condition-content">
            <text class="condition-title">{{conditions.needMobile ? '绑定手机号' : '无需绑定手机号'}}</text>
            <text class="condition-desc" v-if="conditions.needMobile">需绑定手机号</text>
          </view>
          <view class="condition-status">
            <text v-if="conditionStatus.mobile">已绑定</text>
            <button v-else class="action-btn" @click="navigateToBindMobile">去绑定</button>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 申请表单 -->
    <view class="form-card">
      <view class="card-header">
        <text class="card-title">申请信息</text>
      </view>
      
      <view class="form-content">
        <view class="form-item">
          <text class="form-label">姓名</text>
          <input class="form-input" type="text" v-model="formData.name" placeholder="请输入您的真实姓名" />
        </view>
        
        <view class="form-item">
          <text class="form-label">手机号</text>
          <input class="form-input" type="number" v-model="formData.mobile" placeholder="请输入您的手机号" maxlength="11" />
        </view>
        
        <view class="form-item">
          <text class="form-label">微信号</text>
          <input class="form-input" type="text" v-model="formData.wechat" placeholder="请输入您的微信号" />
        </view>
        
        <view class="form-item">
          <text class="form-label">申请理由</text>
          <textarea class="form-textarea" v-model="formData.reason" placeholder="请简要描述您申请成为分销员的理由" maxlength="200" />
          <text class="textarea-counter">{{formData.reason.length}}/200</text>
        </view>
      </view>
    </view>
    
    <!-- 协议同意 -->
    <view class="agreement-section">
      <view class="agreement-checkbox" @click="toggleAgreement">
        <view class="checkbox" :class="{ 'checked': formData.agreeAgreement }"></view>
        <text class="agreement-text">我已阅读并同意</text>
      </view>
      <text class="agreement-link" @click="showAgreement">《分销员协议》</text>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" :disabled="!canSubmit" :class="{ 'disabled': !canSubmit }" @click="submitApplication">提交申请</button>
    </view>
    
    <!-- 申请说明 -->
    <view class="tips-section">
      <view class="tips-header">
        <text class="tips-title">申请说明</text>
      </view>
      
      <view class="tips-content">
        <text class="tips-text">1. 成为分销员后，可以获得推广商品的佣金收益。</text>
        <text class="tips-text">2. 分销员申请需要满足平台设置的条件。</text>
        <text class="tips-text">3. 申请提交后，{{conditions.autoApprove ? '系统将自动审核' : '平台将在1-3个工作日内审核'}}。</text>
        <text class="tips-text">4. 审核通过后，您将收到通知并可立即开始分销推广。</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 申请条件
const conditions = reactive({
  requirePurchase: true,
  minimumPurchase: 100,
  requireApproval: true,
  autoApprove: false,
  needRealName: true,
  needMobile: true,
  description: ''
});

// 条件满足状态
const conditionStatus = reactive({
  purchase: false,
  realName: false,
  mobile: true // 假设已绑定手机号
});

// 表单数据
const formData = reactive({
  name: '',
  mobile: '',
  wechat: '',
  reason: '',
  agreeAgreement: false
});

// 是否可以提交
const canSubmit = computed(() => {
  // 检查条件是否满足
  const conditionsMet = (!conditions.requirePurchase || conditionStatus.purchase) &&
                        (!conditions.needRealName || conditionStatus.realName) &&
                        (!conditions.needMobile || conditionStatus.mobile);
  
  // 检查表单是否填写完整
  const formValid = formData.name.trim() !== '' &&
                   formData.mobile.trim() !== '' &&
                   formData.mobile.length === 11 &&
                   formData.wechat.trim() !== '' &&
                   formData.reason.trim() !== '' &&
                   formData.agreeAgreement;
  
  return conditionsMet && formValid;
});

// 页面加载
onMounted(async () => {
  // 获取分销条件
  await getDistributionConditions();
  
  // 检查条件满足状态
  await checkConditionStatus();
});

// 获取分销条件
const getDistributionConditions = async () => {
  try {
    const result = await distributionService.getDistributionConditions();
    
    if (result) {
      Object.assign(conditions, result);
    }
  } catch (error) {
    console.error('获取分销条件失败', error);
    uni.showToast({
      title: '获取分销条件失败',
      icon: 'none'
    });
  }
};

// 检查条件满足状态
const checkConditionStatus = async () => {
  try {
    // 这里应该调用API检查用户是否满足条件
    // 暂时使用模拟数据
    conditionStatus.purchase = true; // 假设已满足购买条件
    conditionStatus.realName = false; // 假设未实名认证
  } catch (error) {
    console.error('检查条件状态失败', error);
  }
};

// 切换协议同意状态
const toggleAgreement = () => {
  formData.agreeAgreement = !formData.agreeAgreement;
};

// 显示分销协议
const showAgreement = async () => {
  try {
    const agreement = await distributionService.getDistributionAgreement();
    
    uni.showModal({
      title: '分销员协议',
      content: agreement.substring(0, 500) + '...',
      confirmText: '查看全文',
      success: (res) => {
        if (res.confirm) {
          // 跳转到协议详情页
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement'
          });
        }
      }
    });
  } catch (error) {
    console.error('获取分销协议失败', error);
  }
};

// 提交申请
const submitApplication = async () => {
  if (!canSubmit.value) {
    return;
  }
  
  try {
    uni.showLoading({
      title: '提交中...',
      mask: true
    });
    
    const result = await distributionService.applyDistributor({
      name: formData.name,
      mobile: formData.mobile,
      wechat: formData.wechat,
      reason: formData.reason
    });
    
    uni.hideLoading();
    
    if (result.success) {
      uni.showModal({
        title: '申请提交成功',
        content: conditions.autoApprove ? '您的分销员申请已自动通过，现在您可以开始分销推广了！' : '您的分销员申请已提交，请耐心等待审核。',
        showCancel: false,
        success: () => {
          // 返回我要赚钱
          uni.navigateBack();
        }
      });
    } else {
      uni.showModal({
        title: '申请提交失败',
        content: result.message || '请稍后再试',
        showCancel: false
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('提交申请失败', error);
    uni.showToast({
      title: '提交申请失败',
      icon: 'none'
    });
  }
};

// 导航到商城
const navigateToShop = () => {
  uni.switchTab({
    url: '/pages/business/business'
  });
};

// 导航到实名认证
const navigateToRealName = () => {
  uni.navigateTo({
    url: '/pages/my/real-name'
  });
};

// 导航到绑定手机号
const navigateToBindMobile = () => {
  uni.navigateTo({
    url: '/pages/my/bind-mobile'
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '申请帮助',
    content: '成为分销员需要满足平台设置的条件，并提交申请信息。审核通过后，您将成为平台分销员，可以通过分享商品获得佣金。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.apply-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 卡片通用样式 */
.conditions-card,
.form-card,
.tips-section {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 条件列表样式 */
.conditions-list {
  margin-bottom: 20rpx;
}

.condition-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.condition-item:last-child {
  border-bottom: none;
}

.condition-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ddd;
  margin-right: 20rpx;
  position: relative;
}

.condition-icon.satisfied {
  border-color: #6B0FBE;
  background-color: #6B0FBE;
}

.condition-icon.satisfied::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(-45deg);
  top: 10rpx;
  left: 8rpx;
}

.condition-content {
  flex: 1;
}

.condition-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.condition-desc {
  font-size: 24rpx;
  color: #999;
}

.condition-status {
  font-size: 26rpx;
  color: #6B0FBE;
}

.action-btn {
  background: #6B0FBE;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  line-height: 1.5;
  margin: 0;
}

/* 表单样式 */
.form-content {
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.textarea-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 协议同意 */
.agreement-section {
  margin: 30rpx;
  display: flex;
  align-items: center;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border-radius: 6rpx;
  border: 2rpx solid #ddd;
  margin-right: 16rpx;
  position: relative;
}

.checkbox.checked {
  background: #6B0FBE;
  border-color: #6B0FBE;
}

.checkbox.checked::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(-45deg);
  top: 8rpx;
  left: 6rpx;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
}

.agreement-link {
  font-size: 26rpx;
  color: #6B0FBE;
}

/* 提交按钮 */
.submit-section {
  margin: 40rpx 30rpx;
}

.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  line-height: 1.5;
  width: 100%;
}

.submit-btn.disabled {
  background: #cccccc;
  color: #ffffff;
}

/* 申请说明 */
.tips-header {
  margin-bottom: 20rpx;
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.tips-content {
  margin-bottom: 20rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  display: block;
}
</style> 