<template>
  <view class="form-group">
    <view class="form-label">
      <text class="label-text" :class="{ required: isRequired }">{{ label }}</text>
    </view>
    <view class="form-field">
      <slot></slot>
      <text class="form-tip" v-if="tip">{{ tip }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FormGroup',
  props: {
    label: {
      type: String,
      required: true
    },
    isRequired: {
      type: <PERSON>olean,
      default: false
    },
    tip: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss">
.form-group {
  margin-bottom: 20px;
}

.form-label {
  margin-bottom: 8px;
}

.label-text {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  
  &.required::after {
    content: ' *';
    color: #FF3B30;
  }
}

.form-field {
  position: relative;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

/* 基础表单输入框样式 */
.form-input {
  width: 100%;
  height: 44px;
  background-color: #F8FAFC;
  border-radius: 8px;
  padding: 0 15px;
  font-size: 15px;
  color: #333;
  box-sizing: border-box;
}

/* 价格输入框样式 */
.price-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #F8FAFC;
  border-radius: 8px;
  padding: 0 15px;
  height: 44px;
}

.price-symbol {
  font-size: 16px;
  color: #333;
  margin-right: 5px;
}

.price-input {
  flex: 1;
  height: 44px;
  font-size: 15px;
  background-color: transparent;
  padding: 0;
}

/* 数字选择器样式 */
.number-picker {
  display: flex;
  align-items: center;
  height: 44px;
}

.number-btn {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: #F0F0F0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: #666;
}

.number-input {
  width: 60px;
  height: 32px;
  text-align: center;
  margin: 0 8px;
  background-color: #F8FAFC;
  border-radius: 4px;
  font-size: 15px;
}

.unit-text {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

/* 单选按钮组样式 */
.radio-group {
  display: flex;
  margin-bottom: 15px;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 24px;
  
  &.active .radio-label {
    color: #0A84FF;
  }
}

.radio-dot {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 1px solid #D1D1D6;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .active & {
    border-color: #0A84FF;
  }
}

.radio-dot-inner {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #0A84FF;
}

.radio-label {
  font-size: 14px;
  color: #333;
}
</style> 