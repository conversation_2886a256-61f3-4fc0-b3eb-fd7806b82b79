"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      currentTab: 0,
      tabList: ["全部", "进行中", "未开始", "已结束", "草稿"],
      // 红包数据概览
      redpacketData: {
        totalCount: 5862,
        countTrend: "up",
        countGrowth: "12.5%",
        totalAmount: 58620.5,
        amountTrend: "up",
        amountGrowth: "15.2%",
        receiveRate: 76.8,
        receiveRateTrend: "up",
        receiveRateGrowth: "3.5%",
        conversionRate: 42.3,
        conversionTrend: "up",
        conversionGrowth: "5.2%"
      },
      // 红包活动列表
      redpacketList: [
        {
          id: 1,
          name: "新用户专享红包",
          type: "fixed",
          typeText: "固定金额",
          status: "active",
          statusText: "进行中",
          timeRange: "2023-04-01 ~ 2023-04-30",
          amount: 10,
          totalCount: 1e3,
          sentCount: 568,
          receivedCount: 452,
          usedCount: 326
        },
        {
          id: 2,
          name: "五一节日红包",
          type: "random",
          typeText: "随机金额",
          status: "upcoming",
          statusText: "未开始",
          timeRange: "2023-05-01 ~ 2023-05-07",
          minAmount: 5,
          maxAmount: 50,
          totalCount: 2e3,
          sentCount: 0,
          receivedCount: 0,
          usedCount: 0
        },
        {
          id: 3,
          name: "会员专享红包",
          type: "fixed",
          typeText: "固定金额",
          status: "active",
          statusText: "进行中",
          timeRange: "2023-04-15 ~ 2023-04-30",
          amount: 20,
          totalCount: 500,
          sentCount: 286,
          receivedCount: 215,
          usedCount: 158
        },
        {
          id: 4,
          name: "春季促销红包",
          type: "random",
          typeText: "随机金额",
          status: "ended",
          statusText: "已结束",
          timeRange: "2023-03-01 ~ 2023-03-31",
          minAmount: 10,
          maxAmount: 100,
          totalCount: 1e3,
          sentCount: 1e3,
          receivedCount: 876,
          usedCount: 654
        },
        {
          id: 5,
          name: "周末专享红包",
          type: "fixed",
          typeText: "固定金额",
          status: "draft",
          statusText: "草稿",
          timeRange: "未设置",
          amount: 15,
          totalCount: 800,
          sentCount: 0,
          receivedCount: 0,
          usedCount: 0
        }
      ],
      // 红包营销工具
      marketingTools: [
        {
          id: 1,
          name: "红包群发",
          description: "批量发送红包给用户",
          icon: "/static/images/send-icon.png",
          color: "linear-gradient(135deg, #FF9A8B, #FF6B6B)"
        },
        {
          id: 2,
          name: "红包雨",
          description: "创建红包雨活动",
          icon: "/static/images/rain-icon.png",
          color: "linear-gradient(135deg, #FFDB01, #FF9E01)"
        },
        {
          id: 3,
          name: "裂变红包",
          description: "创建裂变式红包",
          icon: "/static/images/share-icon.png",
          color: "linear-gradient(135deg, #FF5E62, #FF9966)"
        },
        {
          id: 4,
          name: "红包数据",
          description: "红包数据分析",
          icon: "/static/images/data-icon.png",
          color: "linear-gradient(135deg, #00F2FE, #4FACFE)"
        }
      ],
      // 红包模板
      redpacketTemplates: [
        {
          id: 1,
          name: "新年红包",
          description: "新年主题红包模板",
          icon: "/static/images/newyear-icon.png",
          color: "linear-gradient(135deg, #FF416C, #FF4B2B)"
        },
        {
          id: 2,
          name: "生日红包",
          description: "生日主题红包模板",
          icon: "/static/images/birthday-icon.png",
          color: "linear-gradient(135deg, #F6D365, #FDA085)"
        },
        {
          id: 3,
          name: "感恩红包",
          description: "感恩主题红包模板",
          icon: "/static/images/thanks-icon.png",
          color: "linear-gradient(135deg, #A18CD1, #FBC2EB)"
        },
        {
          id: 4,
          name: "节日红包",
          description: "节日主题红包模板",
          icon: "/static/images/festival-icon.png",
          color: "linear-gradient(135deg, #FF9A9E, #FECFEF)"
        }
      ],
      // 营销指南
      marketingGuides: [
        {
          id: 1,
          title: "红包营销最佳实践",
          description: "了解如何有效使用红包提高转化率",
          icon: "/static/images/guide-icon.png",
          color: "linear-gradient(135deg, #84FAB0, #8FD3F4)"
        },
        {
          id: 2,
          title: "节日红包攻略",
          description: "节日期间红包营销策略指南",
          icon: "/static/images/strategy-icon.png",
          color: "linear-gradient(135deg, #FCCF31, #F55555)"
        },
        {
          id: 3,
          title: "裂变红包详解",
          description: "如何利用裂变红包实现用户增长",
          icon: "/static/images/growth-icon.png",
          color: "linear-gradient(135deg, #43E97B, #38F9D7)"
        }
      ]
    };
  },
  computed: {
    filteredRedpackets() {
      if (this.currentTab === 0) {
        return this.redpacketList;
      } else {
        const statusMap = ["", "active", "upcoming", "ended", "draft"];
        return this.redpacketList.filter((item) => item.status === statusMap[this.currentTab]);
      }
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "红包营销帮助",
        content: "红包营销是通过发放现金红包的方式吸引用户，提高用户活跃度和转化率的营销方式。",
        showCancel: false
      });
    },
    formatNumber(number) {
      return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createRedpacket() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create"
      });
    },
    viewRedpacketDetail(redpacket) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail?id=${redpacket.id}`
      });
    },
    editRedpacket(redpacket) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/edit?id=${redpacket.id}`
      });
    },
    shareRedpacket(redpacket) {
      common_vendor.index.showLoading({
        title: "生成分享链接..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "分享红包",
          content: "红包分享链接已生成，是否复制链接？",
          confirmText: "复制",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.setClipboardData({
                data: `https://example.com/redpacket/${redpacket.id}`,
                success: () => {
                  common_vendor.index.showToast({
                    title: "链接已复制",
                    icon: "success"
                  });
                }
              });
            }
          }
        });
      }, 1e3);
    },
    deleteRedpacket(redpacket) {
      common_vendor.index.showModal({
        title: "删除红包",
        content: `确定要删除"${redpacket.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.redpacketList.findIndex((item) => item.id === redpacket.id);
            if (index !== -1) {
              this.redpacketList.splice(index, 1);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        }
      });
    },
    useTool(tool) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/tool?id=${tool.id}`
      });
    },
    createTemplate() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create-template"
      });
    },
    useTemplate(template) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create?template=${template.id}`
      });
    },
    viewGuide(guide) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide?id=${guide.id}`
      });
    },
    showActionMenu() {
      common_vendor.index.showActionSheet({
        itemList: ["创建红包", "红包数据分析", "导出红包数据", "红包营销设置"],
        success: (res) => {
          const actions = [
            () => this.createRedpacket(),
            () => this.analyzeData(),
            () => this.exportData(),
            () => this.manageSettings()
          ];
          actions[res.tapIndex]();
        }
      });
    },
    analyzeData() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis"
      });
    },
    exportData() {
      common_vendor.index.showLoading({
        title: "导出中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "数据导出成功",
          icon: "success"
        });
      }, 1500);
    },
    manageSettings() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.redpacketData.totalCount),
    f: common_vendor.t($data.redpacketData.countGrowth),
    g: common_vendor.n($data.redpacketData.countTrend),
    h: common_vendor.t($options.formatNumber($data.redpacketData.totalAmount)),
    i: common_vendor.t($data.redpacketData.amountGrowth),
    j: common_vendor.n($data.redpacketData.amountTrend),
    k: common_vendor.t($data.redpacketData.receiveRate),
    l: common_vendor.t($data.redpacketData.receiveRateGrowth),
    m: common_vendor.n($data.redpacketData.receiveRateTrend),
    n: common_vendor.t($data.redpacketData.conversionRate),
    o: common_vendor.t($data.redpacketData.conversionGrowth),
    p: common_vendor.n($data.redpacketData.conversionTrend),
    q: common_vendor.o((...args) => $options.createRedpacket && $options.createRedpacket(...args)),
    r: common_vendor.f($data.tabList, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    s: common_vendor.f($options.filteredRedpackets, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.typeText),
        b: common_vendor.n("type-" + item.type),
        c: common_vendor.t(item.statusText),
        d: common_vendor.n("status-" + item.status),
        e: common_vendor.n(item.type),
        f: common_vendor.t(item.name),
        g: common_vendor.t(item.timeRange),
        h: item.type === "fixed"
      }, item.type === "fixed" ? {
        i: common_vendor.t(item.amount)
      } : item.type === "random" ? {
        k: common_vendor.t(item.minAmount),
        l: common_vendor.t(item.maxAmount)
      } : {}, {
        j: item.type === "random",
        m: common_vendor.t(item.sentCount),
        n: common_vendor.t(item.totalCount),
        o: common_vendor.t(item.receivedCount),
        p: common_vendor.t(item.usedCount),
        q: common_vendor.o(($event) => $options.editRedpacket(item), index),
        r: item.status === "active"
      }, item.status === "active" ? {
        s: common_vendor.o(($event) => $options.shareRedpacket(item), index)
      } : {}, {
        t: item.status === "draft" || item.status === "ended"
      }, item.status === "draft" || item.status === "ended" ? {
        v: common_vendor.o(($event) => $options.deleteRedpacket(item), index)
      } : {}, {
        w: index,
        x: common_vendor.o(($event) => $options.viewRedpacketDetail(item), index)
      });
    }),
    t: $options.filteredRedpackets.length === 0
  }, $options.filteredRedpackets.length === 0 ? {
    v: common_vendor.t($data.tabList[$data.currentTab])
  } : {}, {
    w: common_vendor.f($data.marketingTools, (tool, index, i0) => {
      return {
        a: tool.icon,
        b: tool.color,
        c: common_vendor.t(tool.name),
        d: common_vendor.t(tool.description),
        e: index,
        f: common_vendor.o(($event) => $options.useTool(tool), index)
      };
    }),
    x: common_vendor.o((...args) => $options.createTemplate && $options.createTemplate(...args)),
    y: common_vendor.f($data.redpacketTemplates, (template, index, i0) => {
      return {
        a: template.icon,
        b: common_vendor.t(template.name),
        c: template.color,
        d: common_vendor.t(template.description),
        e: index,
        f: common_vendor.o(($event) => $options.useTemplate(template), index)
      };
    }),
    z: common_vendor.f($data.marketingGuides, (guide, index, i0) => {
      return {
        a: guide.icon,
        b: guide.color,
        c: common_vendor.t(guide.title),
        d: common_vendor.t(guide.description),
        e: index,
        f: common_vendor.o(($event) => $options.viewGuide(guide), index)
      };
    }),
    A: common_vendor.o((...args) => $options.showActionMenu && $options.showActionMenu(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/index.js.map
