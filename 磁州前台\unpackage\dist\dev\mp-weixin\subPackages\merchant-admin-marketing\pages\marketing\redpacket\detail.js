"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      showSharePopup: false,
      // 红包详情数据
      redpacketData: {
        id: 1,
        name: "新用户专享红包",
        coverUrl: "/static/images/redpacket/cover-placeholder.jpg",
        type: "normal",
        typeText: "普通红包",
        status: "active",
        statusText: "进行中",
        timeRange: "2023-04-15 ~ 2023-04-30",
        // 数据统计
        totalCount: 1e3,
        sentCount: 568,
        receivedCount: 452,
        usedCount: 326,
        // 红包设置
        amountType: "fixed",
        fixedAmount: "10.00",
        minAmount: "",
        maxAmount: "",
        target: "all",
        targetText: "所有用户",
        threshold: "none",
        thresholdText: "无门槛",
        validity: "7",
        userLimit: "1",
        // 使用说明
        description: "1. 每位用户限领1个红包\n2. 红包领取后7天内有效\n3. 红包可在下单时直接抵扣\n4. 不可与其他优惠同时使用\n5. 最终解释权归商家所有",
        // 领取记录
        records: [
          {
            userName: "用户1354***89",
            avatar: "/static/images/redpacket/avatar1.jpg",
            time: "2023-04-20 14:32:56",
            amount: "10.00",
            used: true
          },
          {
            userName: "用户1567***23",
            avatar: "/static/images/redpacket/avatar2.jpg",
            time: "2023-04-20 14:28:12",
            amount: "10.00",
            used: false
          },
          {
            userName: "用户1892***45",
            avatar: "/static/images/redpacket/avatar3.jpg",
            time: "2023-04-20 14:15:38",
            amount: "10.00",
            used: true
          }
        ]
      },
      // 分享选项
      shareOptions: [
        {
          name: "微信好友",
          type: "wechat",
          icon: "/static/images/redpacket/wechat-icon.png",
          color: "#07C160"
        },
        {
          name: "朋友圈",
          type: "moments",
          icon: "/static/images/redpacket/moments-icon.png",
          color: "#07C160"
        },
        {
          name: "微博",
          type: "weibo",
          icon: "/static/images/redpacket/weibo-icon.png",
          color: "#E6162D"
        },
        {
          name: "短信",
          type: "sms",
          icon: "/static/images/redpacket/sms-icon.png",
          color: "#FF9500"
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showMoreActions() {
      common_vendor.index.showActionSheet({
        itemList: ["导出数据", "复制链接", "设为模板"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.exportData();
              break;
            case 1:
              this.copyLink();
              break;
            case 2:
              this.saveAsTemplate();
              break;
          }
        }
      });
    },
    exportData() {
      common_vendor.index.showToast({
        title: "导出功能开发中",
        icon: "none"
      });
    },
    copyLink() {
      common_vendor.index.setClipboardData({
        data: "https://example.com/redpacket/" + this.redpacketData.id,
        success: () => {
          common_vendor.index.showToast({
            title: "链接已复制",
            icon: "success"
          });
        }
      });
    },
    saveAsTemplate() {
      common_vendor.index.showToast({
        title: "已保存为模板",
        icon: "success"
      });
    },
    viewFullStats() {
      common_vendor.index.showToast({
        title: "详细数据功能开发中",
        icon: "none"
      });
    },
    editRedpacket() {
      common_vendor.index.showToast({
        title: "编辑功能开发中",
        icon: "none"
      });
    },
    viewAllRecords() {
      common_vendor.index.showToast({
        title: "查看全部记录功能开发中",
        icon: "none"
      });
    },
    shareRedpacket() {
      this.showSharePopup = true;
    },
    closeSharePopup() {
      this.showSharePopup = false;
    },
    shareVia(type) {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
      this.closeSharePopup();
    },
    startRedpacket() {
      common_vendor.index.showModal({
        title: "确认开始",
        content: "确定要立即开始此红包活动吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "活动已开始",
              icon: "success"
            });
            this.redpacketData.status = "active";
            this.redpacketData.statusText = "进行中";
          }
        }
      });
    },
    stopRedpacket() {
      common_vendor.index.showModal({
        title: "确认结束",
        content: "确定要结束此红包活动吗？结束后不可恢复。",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "活动已结束",
              icon: "success"
            });
            this.redpacketData.status = "ended";
            this.redpacketData.statusText = "已结束";
          }
        }
      });
    },
    deleteRedpacket() {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除此红包活动吗？删除后不可恢复。",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          }
        }
      });
    },
    duplicateRedpacket() {
      common_vendor.index.showToast({
        title: "复制功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showMoreActions && $options.showMoreActions(...args)),
    c: $data.redpacketData.coverUrl,
    d: common_vendor.t($data.redpacketData.statusText),
    e: common_vendor.n("status-" + $data.redpacketData.status),
    f: common_vendor.t($data.redpacketData.name),
    g: common_vendor.t($data.redpacketData.typeText),
    h: common_vendor.n("type-" + $data.redpacketData.type),
    i: common_vendor.t($data.redpacketData.timeRange),
    j: common_vendor.o((...args) => $options.viewFullStats && $options.viewFullStats(...args)),
    k: common_vendor.t($data.redpacketData.totalCount),
    l: common_vendor.t($data.redpacketData.sentCount),
    m: common_vendor.t($data.redpacketData.receivedCount),
    n: common_vendor.t($data.redpacketData.usedCount),
    o: common_vendor.t($data.redpacketData.sentCount),
    p: common_vendor.t($data.redpacketData.totalCount),
    q: $data.redpacketData.sentCount / $data.redpacketData.totalCount * 100 + "%",
    r: $data.redpacketData.status === "draft"
  }, $data.redpacketData.status === "draft" ? {
    s: common_vendor.o((...args) => $options.editRedpacket && $options.editRedpacket(...args))
  } : {}, {
    t: $data.redpacketData.amountType === "fixed"
  }, $data.redpacketData.amountType === "fixed" ? {
    v: common_vendor.t($data.redpacketData.fixedAmount)
  } : $data.redpacketData.amountType === "random" ? {
    x: common_vendor.t($data.redpacketData.minAmount),
    y: common_vendor.t($data.redpacketData.maxAmount)
  } : {}, {
    w: $data.redpacketData.amountType === "random",
    z: common_vendor.t($data.redpacketData.targetText),
    A: common_vendor.t($data.redpacketData.thresholdText),
    B: common_vendor.t($data.redpacketData.validity),
    C: common_vendor.t($data.redpacketData.userLimit),
    D: common_vendor.t($data.redpacketData.description),
    E: common_vendor.o((...args) => $options.viewAllRecords && $options.viewAllRecords(...args)),
    F: common_vendor.f($data.redpacketData.records, (record, index, i0) => {
      return common_vendor.e({
        a: record.avatar,
        b: common_vendor.t(record.userName),
        c: common_vendor.t(record.time),
        d: common_vendor.t(record.amount),
        e: record.used
      }, record.used ? {} : {}, {
        f: index
      });
    }),
    G: $data.redpacketData.records.length === 0
  }, $data.redpacketData.records.length === 0 ? {} : {}, {
    H: $data.redpacketData.status === "active"
  }, $data.redpacketData.status === "active" ? {
    I: common_vendor.o((...args) => $options.shareRedpacket && $options.shareRedpacket(...args))
  } : {}, {
    J: $data.redpacketData.status === "upcoming"
  }, $data.redpacketData.status === "upcoming" ? {
    K: common_vendor.o((...args) => $options.startRedpacket && $options.startRedpacket(...args))
  } : {}, {
    L: $data.redpacketData.status === "active"
  }, $data.redpacketData.status === "active" ? {
    M: common_vendor.o((...args) => $options.stopRedpacket && $options.stopRedpacket(...args))
  } : {}, {
    N: $data.redpacketData.status === "draft" || $data.redpacketData.status === "ended"
  }, $data.redpacketData.status === "draft" || $data.redpacketData.status === "ended" ? {
    O: common_vendor.o((...args) => $options.deleteRedpacket && $options.deleteRedpacket(...args))
  } : {}, {
    P: common_vendor.o((...args) => $options.duplicateRedpacket && $options.duplicateRedpacket(...args)),
    Q: $data.showSharePopup
  }, $data.showSharePopup ? {
    R: common_vendor.o((...args) => $options.closeSharePopup && $options.closeSharePopup(...args)),
    S: common_vendor.o((...args) => $options.closeSharePopup && $options.closeSharePopup(...args)),
    T: common_vendor.f($data.shareOptions, (option, index, i0) => {
      return {
        a: option.icon,
        b: option.color,
        c: common_vendor.t(option.name),
        d: index,
        e: common_vendor.o(($event) => $options.shareVia(option.type), index)
      };
    }),
    U: common_assets._imports_0$35
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail.js.map
