<template>
  <view class="merchant-ad-buttons">
    <!-- 商家入驻推广操作 -->
    <ConfigurablePremiumActions
      v-if="showJoinButton"
      pageType="merchant_join"
      showMode="button"
      :itemData="joinData"
      @action-completed="handleJoinCompleted"
      @action-cancelled="handleJoinCancelled"
    />

    <!-- 商家续费推广操作 -->
    <ConfigurablePremiumActions
      v-if="showRenewButton"
      pageType="merchant_renew"
      showMode="button"
      :itemData="renewData"
      @action-completed="handleRenewCompleted"
      @action-cancelled="handleRenewCancelled"
    />

    <!-- 信息置顶推广操作 -->
    <ConfigurablePremiumActions
      v-if="showTopButton"
      pageType="merchant_top"
      showMode="button"
      :itemData="topData"
      :disabled="isTopDisabled"
      @action-completed="handleTopCompleted"
      @action-cancelled="handleTopCancelled"
    />

    <!-- 信息刷新推广操作 -->
    <ConfigurablePremiumActions
      v-if="showRefreshButton"
      pageType="merchant_refresh"
      showMode="button"
      :itemData="refreshData"
      :disabled="isRefreshDisabled"
      @action-completed="handleRefreshCompleted"
      @action-cancelled="handleRefreshCancelled"
    />
  </view>
</template>

<script setup>
import { computed, reactive } from 'vue';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// Props
const props = defineProps({
  // 按钮类型控制
  showJoinButton: {
    type: Boolean,
    default: false
  },
  showRenewButton: {
    type: Boolean,
    default: false
  },
  showTopButton: {
    type: Boolean,
    default: false
  },
  showRefreshButton: {
    type: Boolean,
    default: false
  },
  
  // 状态控制
  isTopDisabled: {
    type: Boolean,
    default: false
  },
  
  // 数据传递
  merchantId: {
    type: String,
    default: ''
  },
  infoId: {
    type: String,
    default: ''
  },
  carpoolId: {
    type: String,
    default: ''
  },
  
  // 剩余次数
  remainingAds: {
    type: Object,
    default: () => ({
      join: 1,
      renew: 2,
      top: 3,
      refresh: 5
    })
  },
  
  // 是否显示提示
  showTips: {
    type: Boolean,
    default: true
  }
});

// Emits
const emit = defineEmits([
  'adSuccess',
  'adFailed',
  'adCancelled',
  'actionCompleted',
  'actionCancelled'
]);

// 各种操作的数据对象
const joinData = reactive({
  id: props.merchantId || 'merchant_join',
  title: '商家入驻',
  description: '加入我们的商家平台'
});

const renewData = reactive({
  id: props.merchantId || 'merchant_renew',
  title: '商家续费',
  description: '续费商家会员服务'
});

const topData = reactive({
  id: props.infoId || props.carpoolId || 'info_top',
  title: '信息置顶',
  description: '置顶您的信息获得更多曝光'
});

const refreshData = reactive({
  id: props.infoId || props.carpoolId || 'info_refresh',
  title: '信息刷新',
  description: '刷新您的信息到最新'
});

// 添加刷新禁用状态
const isRefreshDisabled = computed(() => {
  return props.remainingAds.refresh <= 0;
});

// 计算属性
const tipsText = computed(() => {
  const tips = [];
  if (props.showJoinButton && props.remainingAds.join > 0) {
    tips.push(`入驻${props.remainingAds.join}次`);
  }
  if (props.showRenewButton && props.remainingAds.renew > 0) {
    tips.push(`续费${props.remainingAds.renew}次`);
  }
  if (props.showTopButton && props.remainingAds.top > 0) {
    tips.push(`置顶${props.remainingAds.top}次`);
  }
  if (props.showRefreshButton && props.remainingAds.refresh > 0) {
    tips.push(`刷新${props.remainingAds.refresh}次`);
  }
  
  return tips.length > 0 ? `今日剩余：${tips.join('，')}` : '今日广告次数已用完';
});

// 处理各种操作完成的方法
const handleJoinCompleted = (result) => {
  console.log('商家入驻完成:', result);
  emit('adSuccess', { type: 'join', data: result });
  emit('actionCompleted', { type: 'join', data: result });
};

const handleJoinCancelled = (result) => {
  console.log('商家入驻取消:', result);
  emit('adCancelled', 'join');
  emit('actionCancelled', { type: 'join', data: result });
};

const handleRenewCompleted = (result) => {
  console.log('商家续费完成:', result);
  emit('adSuccess', { type: 'renew', data: result });
  emit('actionCompleted', { type: 'renew', data: result });
};

const handleRenewCancelled = (result) => {
  console.log('商家续费取消:', result);
  emit('adCancelled', 'renew');
  emit('actionCancelled', { type: 'renew', data: result });
};

const handleTopCompleted = (result) => {
  console.log('信息置顶完成:', result);
  emit('adSuccess', { type: 'top', data: result });
  emit('actionCompleted', { type: 'top', data: result });
};

const handleTopCancelled = (result) => {
  console.log('信息置顶取消:', result);
  emit('adCancelled', 'top');
  emit('actionCancelled', { type: 'top', data: result });
};

const handleRefreshCompleted = (result) => {
  console.log('信息刷新完成:', result);
  emit('adSuccess', { type: 'refresh', data: result });
  emit('actionCompleted', { type: 'refresh', data: result });
};

const handleRefreshCancelled = (result) => {
  console.log('信息刷新取消:', result);
  emit('adCancelled', 'refresh');
  emit('actionCancelled', { type: 'refresh', data: result });
};

</script>

<style>
.merchant-ad-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.ad-button {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ad-button:active {
  transform: scale(0.98);
}

.ad-button.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.ad-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  border-radius: 16rpx;
}

.join-button::before {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.renew-button::before {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.top-button::before {
  background: linear-gradient(135deg, #ff9a00, #ff6a00);
}

.refresh-button::before {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.button-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
  position: relative;
  z-index: 1;
}

.button-icon-wrapper {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.button-icon {
  width: 40rpx;
  height: 40rpx;
}

.free-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: linear-gradient(135deg, #ff4757, #ff3742);
  color: #fff;
  font-size: 18rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
  z-index: 1;
}

.button-text {
  flex: 1;
}

.button-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.button-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.ad-tips {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 10rpx;
}

.tips-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.tips-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.tips-text {
  font-size: 22rpx;
  color: #666;
  flex: 1;
}
</style>
