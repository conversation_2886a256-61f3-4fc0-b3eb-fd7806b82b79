package com.cizhou.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分销员实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("distributors")
public class Distributor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分销员ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 分销员编号
     */
    @TableField("distributor_code")
    private String distributorCode;

    /**
     * 分销员等级：1-初级，2-中级，3-高级，4-金牌
     */
    @TableField("level")
    private Integer level;

    /**
     * 上级分销员ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 分销员状态：0-禁用，1-正常，2-冻结
     */
    @TableField("status")
    private Integer status;

    /**
     * 累计佣金
     */
    @TableField("total_commission")
    private BigDecimal totalCommission;

    /**
     * 可提现佣金
     */
    @TableField("available_commission")
    private BigDecimal availableCommission;

    /**
     * 冻结佣金
     */
    @TableField("frozen_commission")
    private BigDecimal frozenCommission;

    /**
     * 已提现佣金
     */
    @TableField("withdrawn_commission")
    private BigDecimal withdrawnCommission;

    /**
     * 直推人数
     */
    @TableField("direct_count")
    private Integer directCount;

    /**
     * 团队人数
     */
    @TableField("team_count")
    private Integer teamCount;

    /**
     * 本月订单数
     */
    @TableField("month_orders")
    private Integer monthOrders;

    /**
     * 本月销售额
     */
    @TableField("month_sales")
    private BigDecimal monthSales;

    /**
     * 累计订单数
     */
    @TableField("total_orders")
    private Integer totalOrders;

    /**
     * 累计销售额
     */
    @TableField("total_sales")
    private BigDecimal totalSales;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private LocalDateTime applyTime;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核人ID
     */
    @TableField("auditor_id")
    private Long auditorId;

    /**
     * 审核备注
     */
    @TableField("audit_remark")
    private String auditRemark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
