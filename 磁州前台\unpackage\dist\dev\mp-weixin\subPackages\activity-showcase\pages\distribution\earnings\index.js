"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const totalEarnings = common_vendor.ref("1,234.56");
    const waitingEarnings = common_vendor.ref("123.45");
    const settledEarnings = common_vendor.ref("1,111.11");
    const withdrawnEarnings = common_vendor.ref("800.00");
    const timePeriods = common_vendor.ref(["7天", "30天", "90天", "全部"]);
    const currentPeriod = common_vendor.ref(1);
    const incomeSources = common_vendor.ref([
      { name: "直接佣金", percent: 65, color: "#AC39FF" },
      { name: "团队佣金", percent: 25, color: "#FF3B69" },
      { name: "活动奖励", percent: 10, color: "#FF9500" }
    ]);
    const earningsRules = common_vendor.ref([
      {
        title: "佣金结算规则",
        description: "订单完成后，佣金将在7天后自动结算到您的账户，可随时申请提现。"
      },
      {
        title: "提现规则",
        description: "单笔提现金额不低于10元，每月可提现3次，超出次数将收取1%手续费。"
      },
      {
        title: "团队收益规则",
        description: "您可获得直接下级分销订单的10%佣金，二级下级的5%佣金作为团队收益。"
      }
    ]);
    function switchPeriod(index) {
      currentPeriod.value = index;
    }
    function navigateTo(url) {
      common_vendor.index.navigateTo({ url });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(totalEarnings.value),
        b: common_vendor.t(waitingEarnings.value),
        c: common_vendor.t(settledEarnings.value),
        d: common_vendor.t(withdrawnEarnings.value),
        e: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/withdraw/index")),
        f: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/records/index")),
        g: common_vendor.f(timePeriods.value, (period, index, i0) => {
          return {
            a: common_vendor.t(period),
            b: index,
            c: currentPeriod.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchPeriod(index), index),
            e: currentPeriod.value === index ? "#AC39FF" : "#666666",
            f: currentPeriod.value === index ? "rgba(172,57,255,0.1)" : "transparent"
          };
        }),
        h: common_vendor.f(incomeSources.value, (source, index, i0) => {
          return {
            a: source.color,
            b: common_vendor.t(source.name),
            c: common_vendor.t(source.percent),
            d: index
          };
        }),
        i: common_vendor.f(earningsRules.value, (rule, index, i0) => {
          return {
            a: common_vendor.t(index + 1),
            b: common_vendor.t(rule.title),
            c: common_vendor.t(rule.description),
            d: index,
            e: index < earningsRules.value.length - 1 ? "1rpx solid #F2F2F7" : "none"
          };
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-770c6e5d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/distribution/earnings/index.js.map
