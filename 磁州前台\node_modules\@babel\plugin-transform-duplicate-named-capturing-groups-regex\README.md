# @babel/plugin-transform-duplicate-named-capturing-groups-regex

> Compile regular expressions using duplicate named groups to index-based groups.

See our website [@babel/plugin-transform-duplicate-named-capturing-groups-regex](https://babeljs.io/docs/babel-plugin-transform-duplicate-named-capturing-groups-regex) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-duplicate-named-capturing-groups-regex
```

or using yarn:

```sh
yarn add @babel/plugin-transform-duplicate-named-capturing-groups-regex --dev
```
