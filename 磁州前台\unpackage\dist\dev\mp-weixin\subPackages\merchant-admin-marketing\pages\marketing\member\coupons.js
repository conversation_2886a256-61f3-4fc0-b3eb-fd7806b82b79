"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      currentTab: 0,
      // 卡券数据概览
      couponData: {
        totalCoupons: 16,
        issuedCoupons: 3568,
        issuedTrend: "up",
        issuedGrowth: "15.2%",
        usedCoupons: 1245,
        usedTrend: "up",
        usedGrowth: "8.7%",
        useRate: 34.9,
        useRateTrend: "down",
        useRateGrowth: "2.3%"
      },
      // 卡券分类标签
      tabs: ["全部卡券", "满减券", "折扣券", "代金券", "会员专享"],
      // 卡券列表
      coupons: [
        {
          id: 1,
          name: "新人专享券",
          description: "新会员专享优惠",
          type: "cash",
          value: 10,
          condition: "满100元可用",
          validPeriod: "领取后30天内有效",
          category: "代金券",
          memberLevels: ["全部会员"],
          status: "active",
          statusText: "进行中",
          enabled: true
        },
        {
          id: 2,
          name: "会员日特惠",
          description: "每月28日会员专享",
          type: "discount",
          value: 8.5,
          condition: "无门槛",
          validPeriod: "每月28日当天有效",
          category: "折扣券",
          memberLevels: ["银卡会员", "金卡会员", "钻石会员"],
          status: "active",
          statusText: "进行中",
          enabled: true
        },
        {
          id: 3,
          name: "生日礼券",
          description: "会员生日当月专享",
          type: "cash",
          value: 50,
          condition: "满200元可用",
          validPeriod: "生日当月有效",
          category: "会员专享",
          memberLevels: ["银卡会员", "金卡会员", "钻石会员"],
          status: "active",
          statusText: "进行中",
          enabled: true
        },
        {
          id: 4,
          name: "满减优惠券",
          description: "全场通用满减券",
          type: "cash",
          value: 20,
          condition: "满200元可用",
          validPeriod: "2023-04-01至2023-04-30",
          category: "满减券",
          memberLevels: ["全部会员"],
          status: "active",
          statusText: "进行中",
          enabled: true
        },
        {
          id: 5,
          name: "钻石会员专享",
          description: "钻石会员专享优惠",
          type: "discount",
          value: 7.5,
          condition: "无门槛",
          validPeriod: "长期有效",
          category: "会员专享",
          memberLevels: ["钻石会员"],
          status: "active",
          statusText: "进行中",
          enabled: true
        },
        {
          id: 6,
          name: "限时满减券",
          description: "周末限时优惠",
          type: "cash",
          value: 30,
          condition: "满300元可用",
          validPeriod: "每周六日有效",
          category: "满减券",
          memberLevels: ["全部会员"],
          status: "active",
          statusText: "进行中",
          enabled: true
        }
      ]
    };
  },
  computed: {
    filteredCoupons() {
      if (this.currentTab === 0) {
        return this.coupons;
      } else {
        const category = this.tabs[this.currentTab];
        return this.coupons.filter((coupon) => coupon.category === category);
      }
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "会员卡券帮助",
        content: "会员卡券是指为会员提供的各类优惠券，包括满减券、折扣券、代金券等，可以提高会员购买转化率和复购率。",
        showCancel: false
      });
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createCoupon() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/create-coupon?type=member"
      });
    },
    editCoupon(coupon) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/edit-coupon?id=${coupon.id}`
      });
    },
    issueCoupon(coupon) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/issue-coupon?id=${coupon.id}`
      });
    },
    viewStats(coupon) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon-stats?id=${coupon.id}`
      });
    },
    toggleCoupon(coupon, e) {
      const index = this.coupons.findIndex((item) => item.id === coupon.id);
      if (index !== -1) {
        this.coupons[index].enabled = e.detail.value;
        this.coupons[index].status = e.detail.value ? "active" : "inactive";
        this.coupons[index].statusText = e.detail.value ? "进行中" : "已暂停";
      }
      common_vendor.index.showToast({
        title: e.detail.value ? `${coupon.name}已启用` : `${coupon.name}已禁用`,
        icon: "none"
      });
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.couponData.totalCoupons),
    f: common_vendor.t($data.couponData.issuedCoupons),
    g: common_vendor.t($data.couponData.issuedGrowth),
    h: common_vendor.n($data.couponData.issuedTrend),
    i: common_vendor.t($data.couponData.usedCoupons),
    j: common_vendor.t($data.couponData.usedGrowth),
    k: common_vendor.n($data.couponData.usedTrend),
    l: common_vendor.t($data.couponData.useRate),
    m: common_vendor.t($data.couponData.useRateGrowth),
    n: common_vendor.n($data.couponData.useRateTrend),
    o: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    p: common_vendor.t($data.tabs[$data.currentTab]),
    q: common_vendor.o((...args) => $options.createCoupon && $options.createCoupon(...args)),
    r: common_vendor.f($options.filteredCoupons, (coupon, index, i0) => {
      return common_vendor.e({
        a: coupon.type === "discount"
      }, coupon.type === "discount" ? {
        b: common_vendor.t(coupon.value)
      } : {
        c: common_vendor.t(coupon.value)
      }, {
        d: common_vendor.t(coupon.condition),
        e: common_vendor.t(coupon.name),
        f: common_vendor.t(coupon.description),
        g: common_vendor.t(coupon.validPeriod),
        h: common_vendor.f(coupon.memberLevels, (level, levelIndex, i1) => {
          return {
            a: common_vendor.t(level),
            b: levelIndex
          };
        }),
        i: common_vendor.t(coupon.statusText),
        j: common_vendor.n(coupon.status),
        k: common_vendor.n(coupon.type),
        l: "67964e68-1-" + i0 + "," + ("67964e68-0-" + i0),
        m: "67964e68-0-" + i0,
        n: common_vendor.o(($event) => $options.issueCoupon(coupon), index),
        o: "67964e68-3-" + i0 + "," + ("67964e68-2-" + i0),
        p: "67964e68-2-" + i0,
        q: common_vendor.o(($event) => $options.viewStats(coupon), index),
        r: coupon.enabled,
        s: common_vendor.o((e) => $options.toggleCoupon(coupon, e), index),
        t: index,
        v: common_vendor.o(($event) => $options.editCoupon(coupon), index)
      });
    }),
    s: common_vendor.p({
      d: "M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"
    }),
    t: common_vendor.p({
      viewBox: "0 0 24 24",
      fill: "#FF6FD8"
    }),
    v: common_vendor.p({
      d: "M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"
    }),
    w: common_vendor.p({
      viewBox: "0 0 24 24",
      fill: "#3813C2"
    }),
    x: common_vendor.o((...args) => $options.createCoupon && $options.createCoupon(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/coupons.js.map
