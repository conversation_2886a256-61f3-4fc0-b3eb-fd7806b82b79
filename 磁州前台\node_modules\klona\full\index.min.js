!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.klona={})}(this,(function(e){function t(e,t,r){"object"==typeof r.value&&(r.value=o(r.value)),r.enumerable&&!r.get&&!r.set&&r.configurable&&r.writable&&"__proto__"!==t?e[t]=r.value:Object.defineProperty(e,t,r)}function o(e){if("object"!=typeof e)return e;var r,n,c,a=0,f=Object.prototype.toString.call(e);if("[object Object]"===f?c=Object.create(e.__proto__||null):"[object Array]"===f?c=Array(e.length):"[object Set]"===f?(c=new Set,e.forEach((function(e){c.add(o(e))}))):"[object Map]"===f?(c=new Map,e.forEach((function(e,t){c.set(o(t),o(e))}))):"[object Date]"===f?c=new Date(+e):"[object RegExp]"===f?c=new RegExp(e.source,e.flags):"[object DataView]"===f?c=new e.constructor(o(e.buffer)):"[object ArrayBuffer]"===f?c=e.slice(0):"Array]"===f.slice(-6)&&(c=new e.constructor(e)),c){for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)t(c,n[a],Object.getOwnPropertyDescriptor(e,n[a]));for(a=0,n=Object.getOwnPropertyNames(e);a<n.length;a++)Object.hasOwnProperty.call(c,r=n[a])&&c[r]===e[r]||t(c,r,Object.getOwnPropertyDescriptor(e,r))}return c||e}e.klona=o}));