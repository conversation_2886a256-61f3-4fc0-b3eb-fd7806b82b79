{"version": 3, "file": "list.js", "sources": ["subPackages/news/pages/list.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbmV3c1xwYWdlc1xsaXN0LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"news-container\">\r\n\t\t<!-- 资讯分类 -->\r\n\t\t<scroll-view class=\"category-scroll\" scroll-x>\r\n\t\t\t<view class=\"category-list\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tclass=\"category-item\" \r\n\t\t\t\t\t:class=\"{ active: currentCategory === item.id }\"\r\n\t\t\t\t\tv-for=\"item in categories\" \r\n\t\t\t\t\t:key=\"item.id\"\r\n\t\t\t\t\t@click=\"switchCategory(item.id)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{item.name}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t\t\r\n\t\t<!-- 资讯列表 -->\r\n\t\t<view class=\"news-list\">\r\n\t\t\t<view class=\"news-item card-section fade-in\" v-for=\"(item, index) in newsList\" :key=\"index\" @click=\"goToDetail(item.id)\">\r\n\t\t\t\t<view class=\"news-content\">\r\n\t\t\t\t\t<view class=\"news-info\">\r\n\t\t\t\t\t\t<text class=\"news-title\">{{item.title}}</text>\r\n\t\t\t\t\t\t<text class=\"news-desc\">{{item.description}}</text>\r\n\t\t\t\t\t\t<view class=\"news-meta\">\r\n\t\t\t\t\t\t\t<text class=\"news-time\">{{item.time}}</text>\r\n\t\t\t\t\t\t\t<text class=\"news-category\">{{item.category}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image v-if=\"item.image\" :src=\"item.image\" class=\"news-image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"news-stats\">\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/浏览.png\" class=\"stat-icon\"></image>\r\n\t\t\t\t\t\t<text class=\"stat-text\">{{item.views}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/点赞.png\" class=\"stat-icon\"></image>\r\n\t\t\t\t\t\t<text class=\"stat-text\">{{item.likes}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/评论.png\" class=\"stat-icon\"></image>\r\n\t\t\t\t\t\t<text class=\"stat-text\">{{item.comments}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 加载更多 -->\r\n\t\t\t<view class=\"load-more\" v-if=\"hasMore && !loading\" @click=\"loadMore\">\r\n\t\t\t\t加载更多\r\n\t\t\t</view>\r\n\t\t\t<view class=\"loading\" v-if=\"loading\">\r\n\t\t\t\t加载中...\r\n\t\t\t</view>\r\n\t\t\t<view class=\"no-more\" v-if=\"!hasMore && newsList.length > 0\">\r\n\t\t\t\t没有更多了\r\n\t\t\t</view>\r\n\t\t\t<view class=\"empty-list\" v-if=\"!loading && newsList.length === 0\">\r\n\t\t\t\t暂无资讯\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<fab-buttons />\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, watch } from 'vue';\r\nimport FabButtons from '@/components/FabButtons.vue';\r\nimport mockApi from '@/mock/api';\r\n\r\n// 响应式数据\r\nconst currentCategory = ref(0);\r\nconst categories = ref([]);\r\nconst newsList = ref([]);\r\nconst loading = ref(false);\r\nconst page = ref(1);\r\nconst hasMore = ref(true);\r\n\r\n// 方法\r\nconst switchCategory = (categoryId) => {\r\n\tif (currentCategory.value === categoryId) return;\r\n\t\r\n\tcurrentCategory.value = categoryId;\r\n\tpage.value = 1;\r\n\tnewsList.value = [];\r\n\thasMore.value = true;\r\n\tfetchNewsList();\r\n};\r\n\r\nconst goToDetail = (id) => {\r\n\tuni.navigateTo({\r\n\t\turl: `/subPackages/news/pages/detail?id=${id}`\r\n\t});\r\n};\r\n\r\nconst loadMore = () => {\r\n\tif (loading.value || !hasMore.value) return;\r\n\t\r\n\tpage.value++;\r\n\tfetchNewsList();\r\n};\r\n\r\nconst fetchNewsList = async () => {\r\n\tloading.value = true;\r\n\t\r\n\ttry {\r\n\t\tconst result = await mockApi.news.getList(currentCategory.value, page.value);\r\n\t\t\r\n\t\tif (page.value === 1) {\r\n\t\t\tnewsList.value = result.list;\r\n\t\t} else {\r\n\t\t\tnewsList.value = [...newsList.value, ...result.list];\r\n\t\t}\r\n\t\t\r\n\t\thasMore.value = result.hasMore;\r\n\t} catch (error) {\r\n\t\tconsole.error('获取新闻列表失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '获取资讯失败',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t} finally {\r\n\t\tloading.value = false;\r\n\t}\r\n};\r\n\r\nconst fetchCategories = async () => {\r\n\ttry {\r\n\t\tconst result = await mockApi.news.getCategories();\r\n\t\tcategories.value = result;\r\n\t} catch (error) {\r\n\t\tconsole.error('获取新闻分类失败:', error);\r\n\t}\r\n};\r\n\r\n// 监听分类变化\r\nwatch(currentCategory, () => {\r\n\t// 滚动到顶部\r\n\tuni.pageScrollTo({\r\n\t\tscrollTop: 0,\r\n\t\tduration: 300\r\n\t});\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\tfetchCategories();\r\n\tfetchNewsList();\r\n});\r\n</script>\r\n\r\n<style>\r\n\t.news-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n\t\r\n\t.category-scroll {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 20rpx 0;\r\n\t\tposition: sticky;\r\n\t\ttop: 0;\r\n\t\tz-index: 10;\r\n\t}\r\n\t\r\n\t.category-list {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 0 20rpx;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\t\r\n\t.category-item {\r\n\t\tdisplay: inline-block;\r\n\t\tpadding: 12rpx 30rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-radius: 30rpx;\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\t\r\n\t.category-item.active {\r\n\t\tcolor: #fff;\r\n\t\tbackground: linear-gradient(to right, #0052CC, #2196F3);\r\n\t}\r\n\t\r\n\t.news-list {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\t\r\n\t.news-item {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.news-content {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t\r\n\t.news-info {\r\n\t\tflex: 1;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\t\r\n\t.news-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t.news-desc {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\toverflow: hidden;\r\n\t}\r\n\t\r\n\t.news-image {\r\n\t\twidth: 200rpx;\r\n\t\theight: 150rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\t\r\n\t.news-meta {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t\r\n\t.news-time {\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\t\r\n\t.news-category {\r\n\t\tcolor: #0052CC;\r\n\t}\r\n\t\r\n\t.news-stats {\r\n\t\tdisplay: flex;\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t\tpadding-top: 16rpx;\r\n\t}\r\n\t\r\n\t.stat-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-right: 40rpx;\r\n\t}\r\n\t\r\n\t.stat-icon {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tmargin-right: 8rpx;\r\n\t}\r\n\t\r\n\t.stat-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t\r\n\t/* 继承首页的卡片和动画样式 */\r\n\t.card-section {\r\n\t\tbackground-color: #ffffff;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tpadding: 26rpx 22rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\t\r\n\t.fade-in {\r\n\t\tanimation: fadeIn 0.5s ease;\r\n\t}\r\n\t\r\n\t@keyframes fadeIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(20rpx);\r\n\t\t}\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* 加载更多样式 */\r\n\t.load-more, .no-more, .loading, .empty-list {\r\n\t\ttext-align: center;\r\n\t\tpadding: 30rpx 0;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t\r\n\t.load-more {\r\n\t\tcolor: #0052CC;\r\n\t}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/news/pages/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "mockApi", "watch", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;AAmEA,MAAM,aAAa,MAAW;;;;AAI9B,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAC7B,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AACzB,UAAM,WAAWA,cAAAA,IAAI,CAAA,CAAE;AACvB,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,OAAOA,cAAAA,IAAI,CAAC;AAClB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AAGxB,UAAM,iBAAiB,CAAC,eAAe;AACtC,UAAI,gBAAgB,UAAU;AAAY;AAE1C,sBAAgB,QAAQ;AACxB,WAAK,QAAQ;AACb,eAAS,QAAQ;AACjB,cAAQ,QAAQ;AAChB;IACD;AAEA,UAAM,aAAa,CAAC,OAAO;AAC1BC,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,qCAAqC,EAAE;AAAA,MAC9C,CAAE;AAAA,IACF;AAEA,UAAM,WAAW,MAAM;AACtB,UAAI,QAAQ,SAAS,CAAC,QAAQ;AAAO;AAErC,WAAK;AACL;IACD;AAEA,UAAM,gBAAgB,YAAY;AACjC,cAAQ,QAAQ;AAEhB,UAAI;AACH,cAAM,SAAS,MAAMC,SAAO,QAAC,KAAK,QAAQ,gBAAgB,OAAO,KAAK,KAAK;AAE3E,YAAI,KAAK,UAAU,GAAG;AACrB,mBAAS,QAAQ,OAAO;AAAA,QAC3B,OAAS;AACN,mBAAS,QAAQ,CAAC,GAAG,SAAS,OAAO,GAAG,OAAO,IAAI;AAAA,QACnD;AAED,gBAAQ,QAAQ,OAAO;AAAA,MACvB,SAAQ,OAAO;AACfD,qFAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,UAAW;AACT,gBAAQ,QAAQ;AAAA,MAChB;AAAA,IACF;AAEA,UAAM,kBAAkB,YAAY;AACnC,UAAI;AACH,cAAM,SAAS,MAAMC,SAAAA,QAAQ,KAAK,cAAa;AAC/C,mBAAW,QAAQ;AAAA,MACnB,SAAQ,OAAO;AACfD,qFAAc,aAAa,KAAK;AAAA,MAChC;AAAA,IACF;AAGAE,kBAAK,MAAC,iBAAiB,MAAM;AAE5BF,oBAAAA,MAAI,aAAa;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,MACZ,CAAE;AAAA,IACF,CAAC;AAGDG,kBAAAA,UAAU,MAAM;AACf;AACA;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnJD,GAAG,WAAWC,SAAe;"}