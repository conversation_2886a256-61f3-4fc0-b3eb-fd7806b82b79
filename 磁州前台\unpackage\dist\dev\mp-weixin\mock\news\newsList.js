"use strict";
const newsList = [
  {
    id: 1,
    title: "磁县城区道路改造工程开始，请注意绕行",
    description: "为提升城市道路品质，改善市民出行环境，磁县将对城区主要道路进行升级改造。",
    time: "2024-03-15 10:30",
    category: "政务资讯",
    image: "/static/images/banner/banner-1.png",
    views: 1234,
    likes: 88,
    comments: 32
  },
  {
    id: 2,
    title: "磁县第二届美食文化节将于下月举办",
    description: "为展示磁县特色美食文化，促进餐饮业发展，磁县将举办第二届美食文化节。",
    time: "2024-03-14 16:45",
    category: "活动通知",
    image: "/static/images/banner/banner-2.png",
    views: 956,
    likes: 76,
    comments: 28
  },
  {
    id: 3,
    title: "磁县新增三所幼儿园，解决入园难问题",
    description: "为解决适龄儿童入园难问题，磁县今年新建三所公办幼儿园，预计9月开始招生。",
    time: "2024-03-13 09:15",
    category: "便民信息",
    image: "/static/images/banner/banner-3.png",
    views: 845,
    likes: 62,
    comments: 19
  },
  {
    id: 4,
    title: "磁县多家企业联合招聘会本周六举行",
    description: "为促进就业，磁县将举办大型招聘会，多家知名企业参与，提供上千个就业岗位。",
    time: "2024-03-12 14:20",
    category: "招聘信息",
    image: "/static/images/banner/banner-4.png",
    views: 1567,
    likes: 45,
    comments: 37
  },
  {
    id: 5,
    title: "磁县房地产市场稳中有升，新项目陆续开工",
    description: "近期磁县房地产市场呈现稳中有升态势，多个新住宅项目陆续开工建设。",
    time: "2024-03-11 11:30",
    category: "房产资讯",
    image: "/static/images/banner/banner-5.png",
    views: 1023,
    likes: 39,
    comments: 26
  },
  {
    id: 6,
    title: '磁县实施"绿色家园"工程，城区将新增多处公园',
    description: '为改善城市生态环境，提升居民生活品质，磁县启动"绿色家园"工程。',
    time: "2024-03-10 15:40",
    category: "政务资讯",
    image: "/static/images/banner/banner-6.png",
    views: 876,
    likes: 71,
    comments: 23
  },
  {
    id: 7,
    title: "磁县文化馆免费开放系列活动即将启动",
    description: "磁县文化馆将举办一系列免费文化活动，包括书法展、摄影展和传统手工艺展示等。",
    time: "2024-03-09 13:25",
    category: "活动通知",
    image: "/static/images/banner/banner-7.png",
    views: 734,
    likes: 58,
    comments: 17
  },
  {
    id: 8,
    title: "磁县推出便民服务新举措，办事更加便捷",
    description: '磁县行政服务中心推出一系列便民新举措，实现"最多跑一次"和"一网通办"。',
    time: "2024-03-08 10:50",
    category: "便民信息",
    image: "/static/images/banner/banner-8.png",
    views: 892,
    likes: 67,
    comments: 21
  }
];
const fetchNewsList = (categoryId = 0, page = 1, pageSize = 10) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...newsList];
      if (categoryId !== 0) {
        const categoryMap = {
          1: "政务资讯",
          2: "便民信息",
          3: "活动通知",
          4: "招聘信息",
          5: "房产资讯"
        };
        result = result.filter((item) => item.category === categoryMap[categoryId]);
      }
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const data = result.slice(start, end);
      resolve({
        list: data,
        total: result.length,
        hasMore: end < result.length
      });
    }, 500);
  });
};
exports.fetchNewsList = fetchNewsList;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/news/newsList.js.map
