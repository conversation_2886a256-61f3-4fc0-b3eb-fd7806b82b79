"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "driver-ratings",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const ratings = common_vendor.ref([]);
    const activeFilter = common_vendor.ref("all");
    const averageScore = common_vendor.ref(4.8);
    const totalRatings = common_vendor.ref(0);
    const fiveStarCount = common_vendor.ref(0);
    const fourStarCount = common_vendor.ref(0);
    const threeStarCount = common_vendor.ref(0);
    const twoStarCount = common_vendor.ref(0);
    const oneStarCount = common_vendor.ref(0);
    const topTags = common_vendor.ref([]);
    const fiveStarPercent = common_vendor.computed(() => {
      return totalRatings.value > 0 ? fiveStarCount.value / totalRatings.value * 100 : 0;
    });
    const fourStarPercent = common_vendor.computed(() => {
      return totalRatings.value > 0 ? fourStarCount.value / totalRatings.value * 100 : 0;
    });
    const threeStarPercent = common_vendor.computed(() => {
      return totalRatings.value > 0 ? threeStarCount.value / totalRatings.value * 100 : 0;
    });
    const twoStarPercent = common_vendor.computed(() => {
      return totalRatings.value > 0 ? twoStarCount.value / totalRatings.value * 100 : 0;
    });
    const oneStarPercent = common_vendor.computed(() => {
      return totalRatings.value > 0 ? oneStarCount.value / totalRatings.value * 100 : 0;
    });
    const filteredRatings = common_vendor.computed(() => {
      if (activeFilter.value === "all") {
        return ratings.value;
      } else if (activeFilter.value === "good") {
        return ratings.value.filter((item) => item.rating >= 4);
      } else if (activeFilter.value === "medium") {
        return ratings.value.filter((item) => item.rating === 3);
      } else if (activeFilter.value === "bad") {
        return ratings.value.filter((item) => item.rating <= 2);
      }
      return ratings.value;
    });
    const filterText = common_vendor.computed(() => {
      const filterMap = {
        all: "",
        good: "好",
        medium: "中",
        bad: "差"
      };
      return filterMap[activeFilter.value];
    });
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 20;
      loadRatings();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const loadRatings = () => {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        ratings.value = [
          {
            id: "R001",
            userName: "李先生",
            userAvatar: "/static/images/avatar/user3.png",
            rating: 5,
            tags: ["准时出发", "路线合理", "态度友好"],
            comment: "司机师傅非常准时，提前到达上车地点等候，路线选择也很合理，避开了拥堵路段，全程行车平稳，服务态度也很好！",
            isAnonymous: false,
            createTime: "2023-06-18T09:30:00",
            startLocation: "磁县火车站",
            endLocation: "邯郸东站",
            departureDate: "2023-06-15",
            departureTime: "14:30"
          },
          {
            id: "R002",
            userName: "王女士",
            userAvatar: "/static/images/avatar/user4.png",
            rating: 4,
            tags: ["驾驶平稳", "车内整洁"],
            comment: "车内很干净，司机开车也很稳，就是上车地点稍微有点难找。",
            isAnonymous: false,
            createTime: "2023-06-16T15:45:00",
            startLocation: "磁县人民医院",
            endLocation: "邯郸市区",
            departureDate: "2023-06-16",
            departureTime: "10:00"
          },
          {
            id: "R003",
            userName: "",
            userAvatar: "",
            rating: 5,
            tags: ["价格合理", "态度友好", "准时出发"],
            comment: "价格很合理，比其他拼车便宜，司机人也很好，会主动帮忙搬行李。",
            isAnonymous: true,
            createTime: "2023-06-14T18:20:00",
            startLocation: "磁县商业街",
            endLocation: "邯郸高铁站",
            departureDate: "2023-06-14",
            departureTime: "16:30"
          },
          {
            id: "R004",
            userName: "张先生",
            userAvatar: "/static/images/avatar/user2.png",
            rating: 3,
            tags: ["价格合理"],
            comment: "一般，没什么特别的，价格还可以。",
            isAnonymous: false,
            createTime: "2023-06-12T11:05:00",
            startLocation: "磁县第一中学",
            endLocation: "邯郸火车站",
            departureDate: "2023-06-12",
            departureTime: "09:00"
          },
          {
            id: "R005",
            userName: "赵女士",
            userAvatar: "/static/images/avatar/user5.png",
            rating: 2,
            tags: [],
            comment: "迟到了15分钟，态度也不是很好，车内有异味。",
            isAnonymous: false,
            createTime: "2023-06-10T20:15:00",
            startLocation: "磁县政府",
            endLocation: "邯郸市区",
            departureDate: "2023-06-10",
            departureTime: "18:30"
          }
        ];
        calculateStatistics();
        common_vendor.index.hideLoading();
      }, 1e3);
    };
    const calculateStatistics = () => {
      totalRatings.value = ratings.value.length;
      fiveStarCount.value = ratings.value.filter((item) => item.rating === 5).length;
      fourStarCount.value = ratings.value.filter((item) => item.rating === 4).length;
      threeStarCount.value = ratings.value.filter((item) => item.rating === 3).length;
      twoStarCount.value = ratings.value.filter((item) => item.rating === 2).length;
      oneStarCount.value = ratings.value.filter((item) => item.rating === 1).length;
      if (totalRatings.value > 0) {
        const totalScore = ratings.value.reduce((sum, item) => sum + item.rating, 0);
        averageScore.value = (totalScore / totalRatings.value).toFixed(1);
      }
      const tagMap = {};
      ratings.value.forEach((item) => {
        if (item.tags && item.tags.length > 0) {
          item.tags.forEach((tag) => {
            if (tagMap[tag]) {
              tagMap[tag]++;
            } else {
              tagMap[tag] = 1;
            }
          });
        }
      });
      const tagArray = Object.keys(tagMap).map((key) => ({
        name: key,
        count: tagMap[key]
      }));
      tagArray.sort((a, b) => b.count - a.count);
      topTags.value = tagArray.slice(0, 6);
    };
    const setFilter = (filter) => {
      activeFilter.value = filter;
    };
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      const now = /* @__PURE__ */ new Date();
      const diffDays = Math.floor((now - date) / (1e3 * 60 * 60 * 24));
      if (diffDays === 0) {
        return "今天";
      } else if (diffDays === 1) {
        return "昨天";
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${year}-${month < 10 ? "0" + month : month}-${day < 10 ? "0" + day : day}`;
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(averageScore.value),
        e: common_vendor.t(totalRatings.value),
        f: fiveStarPercent.value + "%",
        g: common_vendor.t(fiveStarCount.value),
        h: fourStarPercent.value + "%",
        i: common_vendor.t(fourStarCount.value),
        j: threeStarPercent.value + "%",
        k: common_vendor.t(threeStarCount.value),
        l: twoStarPercent.value + "%",
        m: common_vendor.t(twoStarCount.value),
        n: oneStarPercent.value + "%",
        o: common_vendor.t(oneStarCount.value),
        p: statusBarHeight.value + 44 + "px",
        q: common_vendor.f(topTags.value, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag.name),
            b: common_vendor.t(tag.count),
            c: index
          };
        }),
        r: activeFilter.value === "all" ? 1 : "",
        s: common_vendor.o(($event) => setFilter("all")),
        t: activeFilter.value === "good" ? 1 : "",
        v: common_vendor.o(($event) => setFilter("good")),
        w: activeFilter.value === "medium" ? 1 : "",
        x: common_vendor.o(($event) => setFilter("medium")),
        y: activeFilter.value === "bad" ? 1 : "",
        z: common_vendor.o(($event) => setFilter("bad")),
        A: filteredRatings.value.length === 0
      }, filteredRatings.value.length === 0 ? {
        B: common_assets._imports_1$37,
        C: common_vendor.t(filterText.value)
      } : {}, {
        D: common_vendor.f(filteredRatings.value, (item, index, i0) => {
          return common_vendor.e({
            a: !item.isAnonymous
          }, !item.isAnonymous ? {
            b: item.userAvatar,
            c: common_vendor.t(item.userName)
          } : {
            d: common_assets._imports_2$33
          }, {
            e: common_vendor.f(5, (i, k1, i1) => {
              return {
                a: i,
                b: i <= item.rating ? "/static/images/icons/star-filled.png" : "/static/images/icons/star-empty.png"
              };
            }),
            f: common_vendor.t(formatDate(item.createTime)),
            g: item.tags && item.tags.length > 0
          }, item.tags && item.tags.length > 0 ? {
            h: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            })
          } : {}, {
            i: item.comment
          }, item.comment ? {
            j: common_vendor.t(item.comment)
          } : {}, {
            k: common_vendor.t(item.startLocation),
            l: common_vendor.t(item.endLocation),
            m: common_vendor.t(item.departureDate),
            n: common_vendor.t(item.departureTime),
            o: index
          });
        })
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/driver-ratings.js.map
