{"version": 3, "file": "agreement.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25cYWdyZWVtZW50LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"agreement-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">分销协议</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 协议开关卡片 -->\n    <view class=\"settings-card\">\n      <view class=\"form-item\">\n        <text class=\"form-label\">启用分销协议</text>\n        <view class=\"form-switch\">\n          <switch :checked=\"agreementEnabled\" @change=\"toggleAgreement\" color=\"#6B0FBE\" />\n        </view>\n      </view>\n      \n      <view class=\"form-tip\" v-if=\"agreementEnabled\">\n        <text class=\"tip-text\">启用后，用户申请成为分销员时需要同意该协议</text>\n      </view>\n    </view>\n    \n    <!-- 协议编辑卡片 -->\n    <view class=\"editor-card\" v-if=\"agreementEnabled\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">协议内容</text>\n      </view>\n      \n      <view class=\"editor-toolbar\">\n        <view class=\"toolbar-item\" @click=\"formatText('bold')\" :class=\"{ active: formats.bold }\">\n          <view class=\"toolbar-icon bold\">B</view>\n        </view>\n        <view class=\"toolbar-item\" @click=\"formatText('italic')\" :class=\"{ active: formats.italic }\">\n          <view class=\"toolbar-icon italic\">I</view>\n        </view>\n        <view class=\"toolbar-item\" @click=\"formatText('underline')\" :class=\"{ active: formats.underline }\">\n          <view class=\"toolbar-icon underline\">U</view>\n        </view>\n        <view class=\"toolbar-separator\"></view>\n        <view class=\"toolbar-item\" @click=\"formatText('align', 'left')\" :class=\"{ active: formats.align === 'left' }\">\n          <view class=\"toolbar-icon align-left\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M3 6H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M3 12H15\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M3 18H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n        </view>\n        <view class=\"toolbar-item\" @click=\"formatText('align', 'center')\" :class=\"{ active: formats.align === 'center' }\">\n          <view class=\"toolbar-icon align-center\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M3 6H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M6 12H18\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M3 18H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n        </view>\n        <view class=\"toolbar-item\" @click=\"formatText('align', 'right')\" :class=\"{ active: formats.align === 'right' }\">\n          <view class=\"toolbar-icon align-right\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M3 6H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M9 12H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M3 18H21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n        </view>\n        <view class=\"toolbar-separator\"></view>\n        <view class=\"toolbar-item\" @click=\"insertTemplate\">\n          <view class=\"toolbar-icon template\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M14 2V8H20\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M16 13H8\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M16 17H8\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M10 9H9H8\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"editor-content\">\n        <editor id=\"editor\" class=\"rich-editor\" :placeholder=\"'请输入分销协议内容...'\" @ready=\"onEditorReady\" @input=\"onEditorInput\"></editor>\n      </view>\n    </view>\n    \n    <!-- 协议预览卡片 -->\n    <view class=\"preview-card\" v-if=\"agreementEnabled\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">协议预览</text>\n        <view class=\"preview-button\" @click=\"previewAgreement\">预览</view>\n      </view>\n      \n      <view class=\"preview-content\">\n        <image class=\"preview-image\" src=\"/static/images/agreement-preview.png\" mode=\"widthFix\"></image>\n      </view>\n    </view>\n    \n    <!-- 保存按钮 -->\n    <view class=\"button-container\">\n      <button class=\"save-button\" @click=\"saveSettings\">保存设置</button>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue';\n\n// 是否启用分销协议\nconst agreementEnabled = ref(true);\n\n// 富文本编辑器\nconst editorCtx = ref(null);\n\n// 编辑器格式\nconst formats = reactive({\n  bold: false,\n  italic: false,\n  underline: false,\n  align: 'left'\n});\n\n// 默认协议内容\nconst defaultAgreement = `<h2 style=\"text-align: center; font-weight: bold; margin-bottom: 20px;\">分销员协议</h2>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">尊敬的用户，欢迎您申请成为我们的分销员。在您申请成为分销员之前，请您仔细阅读本协议的全部内容。</p>\n\n<h3 style=\"font-weight: bold; margin: 15px 0 10px 0;\">一、分销员资格</h3>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">1.1 您确认，您在申请成为分销员时，已经年满18周岁，具有完全民事行为能力。</p>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">1.2 您承诺，您提供的所有注册资料均真实、完整、准确，并承诺及时更新相关资料。</p>\n\n<h3 style=\"font-weight: bold; margin: 15px 0 10px 0;\">二、分销规则</h3>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">2.1 分销员可以获得的佣金比例、结算方式等具体规则，以平台当时公示的规则为准。</p>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">2.2 平台有权根据业务需要调整分销规则，调整后的规则将在平台公示，并自公示之日起生效。</p>\n\n<h3 style=\"font-weight: bold; margin: 15px 0 10px 0;\">三、分销员义务</h3>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">3.1 分销员应当遵守国家法律法规和平台规则，不得从事任何违法违规活动。</p>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">3.2 分销员在推广过程中，不得夸大宣传、虚假宣传，不得侵犯他人合法权益。</p>\n\n<h3 style=\"font-weight: bold; margin: 15px 0 10px 0;\">四、违约责任</h3>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">4.1 如分销员违反本协议约定，平台有权视情节轻重，采取警告、暂停分销资格、永久取消分销资格等措施。</p>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">4.2 如分销员的行为给平台或第三方造成损失，分销员应当承担赔偿责任。</p>\n\n<h3 style=\"font-weight: bold; margin: 15px 0 10px 0;\">五、协议修改</h3>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">5.1 平台有权根据业务需要修改本协议，修改后的协议将在平台公示，并自公示之日起生效。</p>\n<p style=\"text-indent: 2em; margin-bottom: 10px;\">5.2 如分销员不同意修改后的协议，可以申请终止分销员资格；如分销员继续使用分销功能，则视为同意修改后的协议。</p>\n\n<p style=\"text-align: right; margin-top: 20px;\">平台运营方：XXXX有限公司</p>\n<p style=\"text-align: right;\">日期：2023年XX月XX日</p>`;\n\n// 页面加载\nonMounted(() => {\n  // 获取分销协议设置\n  getAgreementSettings();\n});\n\n// 获取分销协议设置\nconst getAgreementSettings = () => {\n  // 这里应该从API获取设置\n  // 暂时使用模拟数据\n};\n\n// 切换分销协议\nconst toggleAgreement = (e) => {\n  agreementEnabled.value = e.detail.value;\n};\n\n// 编辑器准备完成\nconst onEditorReady = () => {\n  uni.createSelectorQuery()\n    .select('#editor')\n    .context((res) => {\n      editorCtx.value = res.context;\n      \n      // 设置初始内容\n      editorCtx.value.setContents({\n        html: defaultAgreement,\n        success: () => {\n          console.log('设置内容成功');\n        },\n        fail: (err) => {\n          console.error('设置内容失败:', err);\n        }\n      });\n    })\n    .exec();\n};\n\n// 编辑器内容变化\nconst onEditorInput = (e) => {\n  // 可以在这里处理内容变化\n  console.log('内容变化:', e.detail);\n};\n\n// 格式化文本\nconst formatText = (name, value) => {\n  editorCtx.value.format(name, value);\n  \n  // 更新格式状态\n  if (name === 'align') {\n    formats.align = value;\n  } else {\n    formats[name] = !formats[name];\n  }\n};\n\n// 插入模板\nconst insertTemplate = () => {\n  uni.showActionSheet({\n    itemList: ['插入分销规则模板', '插入佣金说明模板', '插入提现规则模板'],\n    success: (res) => {\n      let template = '';\n      \n      switch (res.tapIndex) {\n        case 0:\n          template = '<h3 style=\"font-weight: bold; margin: 15px 0 10px 0;\">分销规则</h3><p style=\"text-indent: 2em; margin-bottom: 10px;\">1. 分销员可获得一级佣金和二级佣金</p><p style=\"text-indent: 2em; margin-bottom: 10px;\">2. 佣金比例根据分销员等级不同而不同</p>';\n          break;\n        case 1:\n          template = '<h3 style=\"font-weight: bold; margin: 15px 0 10px 0;\">佣金说明</h3><p style=\"text-indent: 2em; margin-bottom: 10px;\">1. 佣金在订单完成后自动结算</p><p style=\"text-indent: 2em; margin-bottom: 10px;\">2. 佣金可在\"我的佣金\"页面查看</p>';\n          break;\n        case 2:\n          template = '<h3 style=\"font-weight: bold; margin: 15px 0 10px 0;\">提现规则</h3><p style=\"text-indent: 2em; margin-bottom: 10px;\">1. 最低提现金额为50元</p><p style=\"text-indent: 2em; margin-bottom: 10px;\">2. 提现申请将在1-3个工作日内处理</p>';\n          break;\n      }\n      \n      editorCtx.value.insertText({\n        text: template,\n        success: () => {\n          console.log('插入模板成功');\n        }\n      });\n    }\n  });\n};\n\n// 预览协议\nconst previewAgreement = () => {\n  editorCtx.value.getContents({\n    success: (res) => {\n      const html = res.html;\n      \n      // 这里可以实现预览功能，例如打开一个新页面显示协议内容\n      uni.showToast({\n        title: '预览功能开发中',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 保存设置\nconst saveSettings = () => {\n  if (agreementEnabled.value) {\n    editorCtx.value.getContents({\n      success: (res) => {\n        const html = res.html;\n        \n        // 这里应该调用API保存协议内容\n        saveAgreementContent(html);\n      }\n    });\n  } else {\n    // 直接保存设置\n    saveAgreementSetting();\n  }\n};\n\n// 保存协议内容\nconst saveAgreementContent = (html) => {\n  // 这里应该调用API保存协议内容\n  uni.showLoading({\n    title: '保存中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success'\n    });\n    \n    // 返回上一页\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 1500);\n  }, 1000);\n};\n\n// 保存协议设置\nconst saveAgreementSetting = () => {\n  // 这里应该调用API保存协议设置\n  uni.showLoading({\n    title: '保存中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success'\n    });\n    \n    // 返回上一页\n    setTimeout(() => {\n      uni.navigateBack();\n    }, 1500);\n  }, 1000);\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 显示帮助\nconst showHelp = () => {\n  uni.showModal({\n    title: '分销协议帮助',\n    content: '您可以设置分销员协议，用户申请成为分销员时需要同意该协议。您可以使用富文本编辑器编辑协议内容，也可以使用模板快速插入常用内容。',\n    showCancel: false\n  });\n};\n</script>\n\n<style lang=\"scss\">\n.agreement-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 卡片样式 */\n.settings-card, .editor-card, .preview-card {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n}\n\n.card-header {\n  padding: 16px;\n  border-bottom: 1px solid #F0F0F0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.preview-button {\n  font-size: 14px;\n  color: #6B0FBE;\n  padding: 6px 12px;\n  background-color: rgba(107, 15, 190, 0.1);\n  border-radius: 16px;\n}\n\n/* 表单项样式 */\n.form-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n}\n\n.form-label {\n  font-size: 15px;\n  color: #333;\n}\n\n.form-switch {\n  height: 36px;\n  display: flex;\n  align-items: center;\n}\n\n.form-tip {\n  padding: 0 16px 16px;\n  border-top: 1px solid #F0F0F0;\n}\n\n.tip-text {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 编辑器样式 */\n.editor-toolbar {\n  display: flex;\n  align-items: center;\n  padding: 8px 16px;\n  border-bottom: 1px solid #F0F0F0;\n  overflow-x: auto;\n}\n\n.toolbar-item {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 8px;\n  border-radius: 6px;\n  color: #666;\n}\n\n.toolbar-item.active {\n  background-color: rgba(107, 15, 190, 0.1);\n  color: #6B0FBE;\n}\n\n.toolbar-icon {\n  font-size: 14px;\n  font-weight: bold;\n}\n\n.toolbar-icon.bold {\n  font-weight: bold;\n}\n\n.toolbar-icon.italic {\n  font-style: italic;\n}\n\n.toolbar-icon.underline {\n  text-decoration: underline;\n}\n\n.toolbar-separator {\n  width: 1px;\n  height: 24px;\n  background-color: #EEEEEE;\n  margin: 0 8px;\n}\n\n.editor-content {\n  padding: 16px;\n  min-height: 300px;\n}\n\n.rich-editor {\n  width: 100%;\n  min-height: 300px;\n  font-size: 14px;\n  line-height: 1.5;\n  color: #333;\n}\n\n/* 预览样式 */\n.preview-content {\n  padding: 16px;\n}\n\n.preview-image {\n  width: 100%;\n  border-radius: 8px;\n}\n\n/* 按钮样式 */\n.button-container {\n  margin: 24px 16px;\n}\n\n.save-button {\n  height: 44px;\n  border-radius: 22px;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #FFFFFF;\n  font-size: 16px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;;;AA+HA,MAAM,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAdzB,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AAGjC,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAG1B,UAAM,UAAUC,cAAAA,SAAS;AAAA,MACvB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,IACT,CAAC;AA8BDC,kBAAAA,UAAU,MAAM;AAEd;IACF,CAAC;AAGD,UAAM,uBAAuB,MAAM;AAAA,IAGnC;AAGA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,uBAAiB,QAAQ,EAAE,OAAO;AAAA,IACpC;AAGA,UAAM,gBAAgB,MAAM;AAC1BC,oBAAAA,MAAI,oBAAqB,EACtB,OAAO,SAAS,EAChB,QAAQ,CAAC,QAAQ;AAChB,kBAAU,QAAQ,IAAI;AAGtB,kBAAU,MAAM,YAAY;AAAA,UAC1B,MAAM;AAAA,UACN,SAAS,MAAM;AACbA,0BAAAA,6GAAY,QAAQ;AAAA,UACrB;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAA,MAAA,MAAA,SAAA,0FAAc,WAAW,GAAG;AAAA,UAC7B;AAAA,QACT,CAAO;AAAA,MACP,CAAK,EACA;IACL;AAGA,UAAM,gBAAgB,CAAC,MAAM;AAE3BA,iIAAY,SAAS,EAAE,MAAM;AAAA,IAC/B;AAGA,UAAM,aAAa,CAAC,MAAM,UAAU;AAClC,gBAAU,MAAM,OAAO,MAAM,KAAK;AAGlC,UAAI,SAAS,SAAS;AACpB,gBAAQ,QAAQ;AAAA,MACpB,OAAS;AACL,gBAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI;AAAA,MAC9B;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,YAAY,YAAY,UAAU;AAAA,QAC7C,SAAS,CAAC,QAAQ;AAChB,cAAI,WAAW;AAEf,kBAAQ,IAAI,UAAQ;AAAA,YAClB,KAAK;AACH,yBAAW;AACX;AAAA,YACF,KAAK;AACH,yBAAW;AACX;AAAA,YACF,KAAK;AACH,yBAAW;AACX;AAAA,UACH;AAED,oBAAU,MAAM,WAAW;AAAA,YACzB,MAAM;AAAA,YACN,SAAS,MAAM;AACbA,4BAAAA,6GAAY,QAAQ;AAAA,YACrB;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAC7B,gBAAU,MAAM,YAAY;AAAA,QAC1B,SAAS,CAAC,QAAQ;AACH,cAAI;AAGjBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,iBAAiB,OAAO;AAC1B,kBAAU,MAAM,YAAY;AAAA,UAC1B,SAAS,CAAC,QAAQ;AACH,gBAAI;AAGjB,iCAAyB;AAAA,UAC1B;AAAA,QACP,CAAK;AAAA,MACL,OAAS;AAEL;MACD;AAAA,IACH;AAGA,UAAM,uBAAuB,CAAC,SAAS;AAErCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,uBAAuB,MAAM;AAEjCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnUA,GAAG,WAAWC,SAAe;"}