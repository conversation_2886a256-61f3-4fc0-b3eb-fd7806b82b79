// 服务发布模拟数据
export const publishList = [
  // 招聘信息数据 - 新增
  {
    id: 'job-001',
    content: '招聘销售人员，底薪3000+提成，有社保，包吃住，有销售经验者优先',
    category: '招聘信息',
    subcategory: '销售',
    area: '城区',
    time: '2025-06-29 10:30',
    views: 120,
    price: '3000-8000元/月',
    images: [
      '/static/images/job/sales1.jpg',
      '/static/images/job/sales2.jpg'
    ],
    contact: {
      name: '王经理',
      phone: '13888888888',
      wechat: 'wangjl123'
    }
  },
  {
    id: 'job-002',
    content: '餐厅招聘服务员数名，男女不限，有经验者优先，包吃住，月休4天',
    category: '招聘信息',
    subcategory: '服务员',
    area: '磁州镇',
    time: '2025-06-29 09:45',
    views: 85,
    price: '3500-4500元/月',
    images: [
      '/static/images/job/waiter1.jpg'
    ],
    contact: {
      name: '李店长',
      phone: '13777777777',
      wechat: 'lidz456'
    }
  },
  {
    id: 'job-003',
    content: '招聘电工2名，持证上岗，有3年以上工作经验，待遇从优',
    category: '招聘信息',
    subcategory: '技工',
    area: '岳城镇',
    time: '2025-06-29 08:20',
    views: 65,
    price: '5000-7000元/月',
    images: [
      '/static/images/job/electrician1.jpg',
      '/static/images/job/electrician2.jpg'
    ],
    contact: {
      name: '张总',
      phone: '13666666666',
      wechat: 'zhangzong789'
    }
  },
  {
    id: 'job-004',
    content: '物流公司招聘货车司机，C1以上驾照，熟悉本地路线，薪资高',
    category: '招聘信息',
    subcategory: '司机',
    area: '城区',
    time: '2025-06-28 21:15',
    views: 98,
    price: '6000-8000元/月',
    images: [
      '/static/images/job/driver1.jpg'
    ],
    contact: {
      name: '赵经理',
      phone: '13555555555',
      wechat: 'zhaojl135'
    }
  },
  {
    id: 'job-005',
    content: '餐厅招聘厨师，有经验，能独立操作，包吃住，待遇从优',
    category: '招聘信息',
    subcategory: '厨师',
    area: '讲武城镇',
    time: '2025-06-28 20:30',
    views: 72,
    price: '5000-8000元/月',
    images: [
      '/static/images/job/chef1.jpg',
      '/static/images/job/chef2.jpg'
    ],
    contact: {
      name: '陈师傅',
      phone: '13444444444',
      wechat: 'chensf246'
    }
  },
  {
    id: 'publish-001',
    content: '专业水电维修，经验丰富，价格合理，服务周到',
    category: '维修服务',
    subcategory: '水电维修',
    area: '城区',
    time: '2025-06-29 08:08',
    views: 75,
    price: '面议',
    images: [
      '/static/images/service/repair1.jpg',
      '/static/images/service/repair2.jpg'
    ],
    contact: {
      name: '刘师傅',
      phone: '13800138000',
      wechat: 'liushifu123'
    }
  },
  {
    id: 'publish-002',
    content: '专业瓷砖铺设，有经验者优先，价格面议',
    category: '装修服务',
    subcategory: '瓷砖铺设',
    area: '城区',
    time: '2025-06-29 03:08',
    views: 62,
    price: '面议',
    images: [
      '/static/images/service/tile1.jpg',
      '/static/images/service/tile2.jpg',
      '/static/images/service/tile3.jpg'
    ],
    contact: {
      name: '张师傅',
      phone: '13900139000',
      wechat: 'zhangshifu456'
    }
  },
  {
    id: 'publish-003',
    content: '专业家政保洁，开荒保洁，日常保洁，钟点工，擦玻璃',
    category: '家政服务',
    subcategory: '保洁服务',
    area: '磁州镇',
    time: '2025-06-28 15:30',
    views: 88,
    price: '50元/小时',
    images: [
      '/static/images/service/cleaning1.jpg',
      '/static/images/service/cleaning2.jpg'
    ],
    contact: {
      name: '李阿姨',
      phone: '13700137000',
      wechat: 'liayi789'
    }
  },
  {
    id: 'publish-004',
    content: '专业搬家服务，价格合理，服务周到，小型搬家，大型搬家均可',
    category: '搬家拉货',
    subcategory: '居民搬家',
    area: '讲武城镇',
    time: '2025-06-28 14:25',
    views: 56,
    price: '200元起',
    images: [
      '/static/images/service/moving1.jpg',
      '/static/images/service/moving2.jpg',
      '/static/images/service/moving3.jpg'
    ],
    contact: {
      name: '王师傅',
      phone: '13600136000',
      wechat: 'wangshifu321'
    }
  },
  {
    id: 'publish-005',
    content: '专业开锁换锁，汽车开锁，保险柜开锁，24小时服务',
    category: '开锁换锁',
    subcategory: '开锁服务',
    area: '城区',
    time: '2025-06-28 12:18',
    views: 102,
    price: '80元起',
    images: [
      '/static/images/service/lock1.jpg'
    ],
    contact: {
      name: '赵师傅',
      phone: '13500135000',
      wechat: 'zhaoshifu654'
    }
  },
  {
    id: 'publish-006',
    content: '专业上门按摩，推拿，足疗，中医理疗，舒缓疲劳',
    category: '上门美容',
    subcategory: '按摩推拿',
    area: '岳城镇',
    time: '2025-06-28 10:45',
    views: 135,
    price: '120元/小时',
    images: [
      '/static/images/service/massage1.jpg',
      '/static/images/service/massage2.jpg'
    ],
    contact: {
      name: '陈技师',
      phone: '13400134000',
      wechat: 'chenjishi987'
    }
  },
  {
    id: 'publish-007',
    content: '专业上门家教，小学初中高中辅导，提分快，有经验',
    category: '上门家教',
    subcategory: '学科辅导',
    area: '观台镇',
    time: '2025-06-28 09:30',
    views: 78,
    price: '100元/小时',
    images: [
      '/static/images/service/tutor1.jpg',
      '/static/images/service/tutor2.jpg'
    ],
    contact: {
      name: '孙老师',
      phone: '13300133000',
      wechat: 'sunlaoshi246'
    }
  },
  {
    id: 'publish-008',
    content: '专业宠物美容，洗澡，造型，修剪，疫苗接种，寄养',
    category: '宠物服务',
    subcategory: '宠物美容',
    area: '白土镇',
    time: '2025-06-28 08:15',
    views: 92,
    price: '80元起',
    images: [
      '/static/images/service/pet1.jpg',
      '/static/images/service/pet2.jpg',
      '/static/images/service/pet3.jpg'
    ],
    contact: {
      name: '钱师傅',
      phone: '13200132000',
      wechat: 'qianshifu135'
    }
  },
  {
    id: 'publish-009',
    content: '专业管道疏通，马桶疏通，下水道疏通，厨房疏通，快速上门',
    category: '上门疏通',
    subcategory: '管道疏通',
    area: '黄沙镇',
    time: '2025-06-28 07:20',
    views: 68,
    price: '60元起',
    images: [
      '/static/images/service/plumbing1.jpg',
      '/static/images/service/plumbing2.jpg'
    ],
    contact: {
      name: '周师傅',
      phone: '13100131000',
      wechat: 'zhoushifu579'
    }
  },
  {
    id: 'publish-010',
    content: '专业家电安装，空调安装，电视安装，洗衣机安装，热水器安装',
    category: '上门安装',
    subcategory: '家电安装',
    area: '城区',
    time: '2025-06-28 06:10',
    views: 110,
    price: '100元起',
    images: [
      '/static/images/service/installation1.jpg',
      '/static/images/service/installation2.jpg',
      '/static/images/service/installation3.jpg'
    ],
    contact: {
      name: '吴师傅',
      phone: '13000130000',
      wechat: 'wushifu468'
    }
  }
];

// 获取服务发布列表的API函数
export const fetchPublishList = (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...publishList];
      
      // 按类型筛选
      if (params.category && params.category !== '全部分类') {
        result = result.filter(item => item.category === params.category);
      }
      
      // 按子类型筛选
      if (params.subcategory) {
        result = result.filter(item => item.subcategory === params.subcategory);
      }
      
      // 按区域筛选
      if (params.area && params.area !== '全部区域') {
        result = result.filter(item => item.area === params.area);
      }
      
      // 按关键词搜索
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase();
        result = result.filter(item => 
          item.content.toLowerCase().includes(keyword) || 
          item.category.toLowerCase().includes(keyword) || 
          (item.subcategory && item.subcategory.toLowerCase().includes(keyword))
        );
      }
      
      // 排序
      if (params.sort) {
        switch (params.sort) {
          case '价格最低':
            result.sort((a, b) => {
              const priceA = parseFloat(a.price) || 999999;
              const priceB = parseFloat(b.price) || 999999;
              return priceA - priceB;
            });
            break;
          case '价格最高':
            result.sort((a, b) => {
              const priceA = parseFloat(a.price) || 0;
              const priceB = parseFloat(b.price) || 0;
              return priceB - priceA;
            });
            break;
          case '距离最近':
            // 模拟距离排序，实际应该根据用户位置计算
            result.sort((a, b) => {
              // 这里简单地按区域字母排序作为示例
              return a.area.localeCompare(b.area);
            });
            break;
          default: // 默认排序 - 按时间
            result.sort((a, b) => new Date(b.time) - new Date(a.time));
        }
      } else {
        // 默认按时间排序
        result.sort((a, b) => new Date(b.time) - new Date(a.time));
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const data = result.slice(start, end);
      
      // 返回数据和分页信息
      resolve({
        list: data,
        total: result.length,
        hasMore: end < result.length
      });
    }, 500);
  });
};

// 获取服务发布详情的API函数
export const fetchPublishDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const detail = publishList.find(item => item.id === id);
      
      if (detail) {
        // 增加浏览量
        detail.views += 1;
      }
      
      resolve(detail || null);
    }, 300);
  });
}; 