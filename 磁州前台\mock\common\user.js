// 用户信息模拟数据
export const userInfo = {
  id: 'user-001',
  nickname: '磁州居民',
  avatar: '/static/images/tabbar/user-blue.png',
  gender: 1, // 1-男，2-女，0-未设置
  phone: '139****5678',
  level: 2,
  points: 520,
  isVip: true,
  vipExpiry: '2024-12-31',
  registerTime: '2023-01-15',
  location: '磁县',
  signature: '热爱生活，热爱磁州',
  follows: 28,
  fans: 15,
  likes: 120,
  collections: 45,
  publishCount: 32
};

// 获取用户信息的API函数
export const fetchUserInfo = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(userInfo);
    }, 300);
  });
};

// 用户登录的API函数
export const login = (phone, code) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        token: 'mock-token-' + Date.now(),
        userInfo
      });
    }, 500);
  });
};

// 用户登出的API函数
export const logout = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 300);
  });
}; 