"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentStep: 1,
      formData: {
        id: 1,
        name: "新用户专享红包",
        startDate: "2023-05-01",
        endDate: "2023-05-31",
        type: "normal",
        coverUrl: "/static/images/redpacket/cover-placeholder.jpg",
        amountType: "fixed",
        fixedAmount: "10.00",
        minAmount: "",
        maxAmount: "",
        totalCount: "1000",
        userLimit: "1",
        validity: "7",
        target: "all",
        memberLevels: [],
        threshold: "none",
        minimumAmount: "",
        description: "1. 每位用户限领1个红包\n2. 红包领取后7天内有效\n3. 红包可在下单时直接抵扣\n4. 不可与其他优惠同时使用\n5. 最终解释权归商家所有"
      },
      // 红包类型选项
      redpacketTypes: [
        { label: "普通红包", value: "normal" },
        { label: "裂变红包", value: "fission" },
        { label: "群发红包", value: "mass" },
        { label: "红包雨", value: "rain" }
      ],
      // 金额类型选项
      amountTypes: [
        { label: "固定金额", value: "fixed" },
        { label: "随机金额", value: "random" }
      ],
      // 有效期选项
      validityOptions: [
        { label: "3天", value: "3" },
        { label: "7天", value: "7" },
        { label: "15天", value: "15" },
        { label: "30天", value: "30" }
      ],
      // 发放对象选项
      targetOptions: [
        { label: "所有用户", value: "all" },
        { label: "新用户", value: "new" },
        { label: "会员用户", value: "member" }
      ],
      // 会员等级选项
      memberLevels: [
        { label: "普通会员", value: "normal" },
        { label: "银卡会员", value: "silver" },
        { label: "金卡会员", value: "gold" },
        { label: "钻石会员", value: "diamond" }
      ],
      // 使用门槛选项
      thresholdOptions: [
        { label: "无门槛", value: "none" },
        { label: "满额可用", value: "minimum" }
      ]
    };
  },
  onLoad(options) {
    if (options && options.id) {
      this.loadRedpacketData(options.id);
    }
  },
  methods: {
    loadRedpacketData(id) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/redpacket/edit.vue:282", "加载红包ID:", id);
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "编辑红包帮助",
        content: "在此页面您可以修改红包活动的各项参数，包括基本信息、红包设置和发放规则。",
        showCancel: false
      });
    },
    showDateRangePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    selectRedpacketType(type) {
      this.formData.type = type;
    },
    uploadCover() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          this.formData.coverUrl = res.tempFilePaths[0];
        }
      });
    },
    selectAmountType(type) {
      this.formData.amountType = type;
    },
    selectValidity(validity) {
      this.formData.validity = validity;
    },
    selectTarget(target) {
      this.formData.target = target;
    },
    toggleMemberLevel(level) {
      const index = this.formData.memberLevels.indexOf(level);
      if (index === -1) {
        this.formData.memberLevels.push(level);
      } else {
        this.formData.memberLevels.splice(index, 1);
      }
    },
    selectThreshold(threshold) {
      this.formData.threshold = threshold;
    },
    nextStep() {
      if (this.currentStep < 3) {
        this.currentStep++;
      }
    },
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
      }
    },
    submitForm() {
      if (!this.validateForm()) {
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "保存成功",
          content: "红包活动已更新成功",
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateBack();
            }
          }
        });
      }, 1500);
    },
    cancelEdit() {
      common_vendor.index.showModal({
        title: "确认取消",
        content: "确定要取消编辑吗？未保存的修改将丢失。",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateBack();
          }
        }
      });
    },
    validateForm() {
      if (!this.formData.name) {
        common_vendor.index.showToast({
          title: "请输入红包名称",
          icon: "none"
        });
        return false;
      }
      if (this.formData.amountType === "fixed" && !this.formData.fixedAmount) {
        common_vendor.index.showToast({
          title: "请输入红包金额",
          icon: "none"
        });
        return false;
      }
      if (this.formData.amountType === "random") {
        if (!this.formData.minAmount || !this.formData.maxAmount) {
          common_vendor.index.showToast({
            title: "请输入红包金额范围",
            icon: "none"
          });
          return false;
        }
        if (parseFloat(this.formData.minAmount) >= parseFloat(this.formData.maxAmount)) {
          common_vendor.index.showToast({
            title: "最小金额必须小于最大金额",
            icon: "none"
          });
          return false;
        }
      }
      if (!this.formData.totalCount) {
        common_vendor.index.showToast({
          title: "请输入红包总数量",
          icon: "none"
        });
        return false;
      }
      if (this.formData.target === "member" && this.formData.memberLevels.length === 0) {
        common_vendor.index.showToast({
          title: "请选择会员等级",
          icon: "none"
        });
        return false;
      }
      if (this.formData.threshold === "minimum" && !this.formData.minimumAmount) {
        common_vendor.index.showToast({
          title: "请输入最低消费金额",
          icon: "none"
        });
        return false;
      }
      return true;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: $data.currentStep >= 1 ? 1 : "",
    d: $data.currentStep > 1 ? 1 : "",
    e: $data.currentStep > 1 ? 1 : "",
    f: $data.currentStep >= 2 ? 1 : "",
    g: $data.currentStep > 2 ? 1 : "",
    h: $data.currentStep > 2 ? 1 : "",
    i: $data.currentStep >= 3 ? 1 : "",
    j: $data.currentStep === 1
  }, $data.currentStep === 1 ? common_vendor.e({
    k: $data.formData.name,
    l: common_vendor.o(($event) => $data.formData.name = $event.detail.value),
    m: common_vendor.t($data.formData.startDate),
    n: common_vendor.t($data.formData.endDate),
    o: common_vendor.o((...args) => $options.showDateRangePicker && $options.showDateRangePicker(...args)),
    p: common_vendor.f($data.redpacketTypes, (type, index, i0) => {
      return {
        a: $data.formData.type === type.value ? 1 : "",
        b: common_vendor.t(type.label),
        c: index,
        d: $data.formData.type === type.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectRedpacketType(type.value), index)
      };
    }),
    q: $data.formData.coverUrl
  }, $data.formData.coverUrl ? {
    r: $data.formData.coverUrl
  } : {}, {
    s: common_vendor.o((...args) => $options.uploadCover && $options.uploadCover(...args))
  }) : {}, {
    t: $data.currentStep === 2
  }, $data.currentStep === 2 ? common_vendor.e({
    v: common_vendor.f($data.amountTypes, (type, index, i0) => {
      return {
        a: $data.formData.amountType === type.value ? 1 : "",
        b: common_vendor.t(type.label),
        c: index,
        d: $data.formData.amountType === type.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectAmountType(type.value), index)
      };
    }),
    w: $data.formData.amountType === "fixed"
  }, $data.formData.amountType === "fixed" ? {
    x: $data.formData.fixedAmount,
    y: common_vendor.o(($event) => $data.formData.fixedAmount = $event.detail.value)
  } : {}, {
    z: $data.formData.amountType === "random"
  }, $data.formData.amountType === "random" ? {
    A: $data.formData.minAmount,
    B: common_vendor.o(($event) => $data.formData.minAmount = $event.detail.value)
  } : {}, {
    C: $data.formData.amountType === "random"
  }, $data.formData.amountType === "random" ? {
    D: $data.formData.maxAmount,
    E: common_vendor.o(($event) => $data.formData.maxAmount = $event.detail.value)
  } : {}, {
    F: $data.formData.totalCount,
    G: common_vendor.o(($event) => $data.formData.totalCount = $event.detail.value),
    H: $data.formData.userLimit,
    I: common_vendor.o(($event) => $data.formData.userLimit = $event.detail.value),
    J: common_vendor.f($data.validityOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.formData.validity === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectValidity(option.value), index)
      };
    })
  }) : {}, {
    K: $data.currentStep === 3
  }, $data.currentStep === 3 ? common_vendor.e({
    L: common_vendor.f($data.targetOptions, (target, index, i0) => {
      return {
        a: $data.formData.target === target.value ? 1 : "",
        b: common_vendor.t(target.label),
        c: index,
        d: $data.formData.target === target.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectTarget(target.value), index)
      };
    }),
    M: $data.formData.target === "member"
  }, $data.formData.target === "member" ? {
    N: common_vendor.f($data.memberLevels, (level, index, i0) => {
      return {
        a: $data.formData.memberLevels.includes(level.value) ? 1 : "",
        b: common_vendor.t(level.label),
        c: index,
        d: $data.formData.memberLevels.includes(level.value) ? 1 : "",
        e: common_vendor.o(($event) => $options.toggleMemberLevel(level.value), index)
      };
    })
  } : {}, {
    O: common_vendor.f($data.thresholdOptions, (threshold, index, i0) => {
      return {
        a: $data.formData.threshold === threshold.value ? 1 : "",
        b: common_vendor.t(threshold.label),
        c: index,
        d: $data.formData.threshold === threshold.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectThreshold(threshold.value), index)
      };
    }),
    P: $data.formData.threshold === "minimum"
  }, $data.formData.threshold === "minimum" ? {
    Q: $data.formData.minimumAmount,
    R: common_vendor.o(($event) => $data.formData.minimumAmount = $event.detail.value)
  } : {}, {
    S: $data.formData.description,
    T: common_vendor.o(($event) => $data.formData.description = $event.detail.value)
  }) : {}, {
    U: $data.currentStep > 1
  }, $data.currentStep > 1 ? {
    V: common_vendor.o((...args) => $options.prevStep && $options.prevStep(...args))
  } : {}, {
    W: $data.currentStep < 3
  }, $data.currentStep < 3 ? {
    X: common_vendor.o((...args) => $options.nextStep && $options.nextStep(...args))
  } : {}, {
    Y: $data.currentStep === 3
  }, $data.currentStep === 3 ? {
    Z: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args))
  } : {}, {
    aa: common_vendor.o((...args) => $options.cancelEdit && $options.cancelEdit(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/edit.js.map
