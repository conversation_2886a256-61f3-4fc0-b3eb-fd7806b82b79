{"version": 3, "file": "array.js", "sourceRoot": "", "sources": ["../src/array.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACI,MAAM,aAAa,GAAa,CAAC,KAAY,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;IACnE,yEAAyE;IACzE,MAAM,MAAM,GAAG,KAAK;SACjB,GAAG,CAAC,UAAU,KAAK,EAAE,KAAK;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAElC,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;QAEhD,OAAO,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC;IACvD,CAAC,CAAC;SACD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAE7B,MAAM,GAAG,GAAG,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IACxC,OAAO,IAAI,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,CAAC;AACnC,CAAC,CAAC;AAdW,QAAA,aAAa,iBAcxB", "sourcesContent": ["import { ToString } from \"./types\";\n\n/**\n * Stringify an array of values.\n */\nexport const arrayToString: ToString = (array: any[], space, next) => {\n  // Map array values to their stringified values with correct indentation.\n  const values = array\n    .map(function (value, index) {\n      const result = next(value, index);\n\n      if (result === undefined) return String(result);\n\n      return space + result.split(\"\\n\").join(`\\n${space}`);\n    })\n    .join(space ? \",\\n\" : \",\");\n\n  const eol = space && values ? \"\\n\" : \"\";\n  return `[${eol}${values}${eol}]`;\n};\n"]}