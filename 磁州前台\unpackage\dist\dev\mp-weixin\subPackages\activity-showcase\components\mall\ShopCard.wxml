<view class="shop-card data-v-8e015a84" style="{{x}}" bindtap="{{y}}"><view class="shop-cover-container data-v-8e015a84"><image class="shop-cover data-v-8e015a84" src="{{a}}" mode="aspectFill"></image><view class="shop-logo-container data-v-8e015a84"><image class="shop-logo data-v-8e015a84" src="{{b}}" mode="aspectFill"></image></view></view><view class="shop-info data-v-8e015a84"><view class="shop-header data-v-8e015a84"><view class="shop-name data-v-8e015a84">{{c}}</view><view class="shop-rating data-v-8e015a84"><view class="rating-stars data-v-8e015a84"><view wx:for="{{d}}" wx:for-item="i" wx:key="c" class="{{['star-item', 'data-v-8e015a84', i.d && 'active']}}"><svg wx:if="{{f}}" u-s="{{['d']}}" class="star-icon data-v-8e015a84" u-i="{{i.b}}" bind:__l="__l" u-p="{{f}}"><path wx:if="{{e}}" class="data-v-8e015a84" u-i="{{i.a}}" bind:__l="__l" u-p="{{e}}"></path></svg></view></view><text class="rating-value data-v-8e015a84">{{g}}</text></view></view><view class="shop-meta data-v-8e015a84"><view class="meta-item data-v-8e015a84"><svg wx:if="{{j}}" u-s="{{['d']}}" class="meta-icon data-v-8e015a84" u-i="8e015a84-2" bind:__l="__l" u-p="{{j}}"><path wx:if="{{h}}" class="data-v-8e015a84" u-i="8e015a84-3,8e015a84-2" bind:__l="__l" u-p="{{h}}"></path><circle wx:if="{{i}}" class="data-v-8e015a84" u-i="8e015a84-4,8e015a84-2" bind:__l="__l" u-p="{{i}}"></circle></svg><text class="meta-text data-v-8e015a84">{{k}}</text></view><view class="meta-item data-v-8e015a84"><svg wx:if="{{n}}" u-s="{{['d']}}" class="meta-icon data-v-8e015a84" u-i="8e015a84-5" bind:__l="__l" u-p="{{n}}"><path wx:if="{{l}}" class="data-v-8e015a84" u-i="8e015a84-6,8e015a84-5" bind:__l="__l" u-p="{{l}}"></path><path wx:if="{{m}}" class="data-v-8e015a84" u-i="8e015a84-7,8e015a84-5" bind:__l="__l" u-p="{{m}}"></path></svg><text class="meta-text data-v-8e015a84">{{o}}+订单</text></view></view><view wx:if="{{p}}" class="shop-tags data-v-8e015a84"><view wx:for="{{q}}" wx:for-item="tag" wx:key="b" class="tag-item data-v-8e015a84">{{tag.a}}</view></view><view wx:if="{{r}}" class="shop-description data-v-8e015a84">{{s}}</view></view><view class="enter-shop-btn data-v-8e015a84" catchtap="{{w}}"><text class="data-v-8e015a84">进店</text><svg wx:if="{{v}}" u-s="{{['d']}}" class="enter-icon data-v-8e015a84" u-i="8e015a84-8" bind:__l="__l" u-p="{{v}}"><path wx:if="{{t}}" class="data-v-8e015a84" u-i="8e015a84-9,8e015a84-8" bind:__l="__l" u-p="{{t}}"></path></svg></view></view>