{"_from": "schema-utils@^2.6.1", "_id": "schema-utils@2.7.0", "_inBundle": false, "_integrity": "sha512-0ilKFI6QQF5nxDZLFn2dMjvc4hjg/Wkg7rHd3jK6/A4a1Hl9VFdQWvgB1UMGoU94pad1P/8N7fMcEnLnSiju8A==", "_location": "/sass-loader/schema-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "schema-utils@^2.6.1", "name": "schema-utils", "escapedName": "schema-utils", "rawSpec": "^2.6.1", "saveSpec": null, "fetchSpec": "^2.6.1"}, "_requiredBy": ["/sass-loader"], "_resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.0.tgz", "_shasum": "17151f76d8eae67fbbf77960c33c676ad9f4efc7", "_spec": "schema-utils@^2.6.1", "_where": "/Users/<USER>/Documents/DCloud/HbuilderX-plugins/alpha/uniapp-cli/node_modules/sass-loader", "author": {"name": "webpack Contrib", "url": "https://github.com/webpack-contrib"}, "bugs": {"url": "https://github.com/webpack/schema-utils/issues"}, "bundleDependencies": false, "dependencies": {"@types/json-schema": "^7.0.4", "ajv": "^6.12.2", "ajv-keywords": "^3.4.1"}, "deprecated": false, "description": "webpack Validation Utils", "devDependencies": {"@babel/cli": "^7.10.1", "@babel/core": "^7.10.1", "@babel/preset-env": "^7.10.1", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^25.5.1", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^6.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.20.2", "husky": "^4.2.5", "jest": "^25.5.4", "lint-staged": "^10.2.7", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^8.0.0", "typescript": "^3.9.3"}, "engines": {"node": ">= 8.9.0"}, "files": ["dist", "declarations"], "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack/schema-utils", "keywords": ["webpack"], "license": "MIT", "main": "dist/index.js", "name": "schema-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack/schema-utils.git"}, "scripts": {"build": "npm-run-all -p \"build:**\"", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build:types": "tsc --declaration --emitDeclarationOnly --outDir declarations && prettier \"declarations/**/*.ts\" --write", "clean": "del-cli dist declarations", "commitlint": "commitlint --from=master", "defaults": "webpack-defaults", "lint": "npm-run-all -l -p \"lint:**\"", "lint:js": "eslint --cache .", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:types": "tsc --pretty --noEmit", "prebuild": "npm run clean", "prepare": "npm run build", "pretest": "npm run lint", "release": "standard-version", "security": "npm audit", "start": "npm run build -- -w", "test": "npm run test:coverage", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch"}, "types": "declarations/index.d.ts", "version": "2.7.0"}