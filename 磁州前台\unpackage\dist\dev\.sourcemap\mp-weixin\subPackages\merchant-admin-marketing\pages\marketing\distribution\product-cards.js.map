{"version": 3, "file": "product-cards.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/product-cards.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25ccHJvZHVjdC1jYXJkcy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"product-cards-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">商品推广卡片</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"history-icon\" @click=\"showHistory\">\r\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            <polyline points=\"12 6 12 12 16 14\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品选择 -->\r\n    <view class=\"product-selection-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">选择推广商品</text>\r\n        <view class=\"search-btn\" @click=\"showSearch\">\r\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n            <path d=\"M21 21L16.65 16.65\" stroke=\"#6B0FBE\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          </svg>\r\n          <text>搜索商品</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <scroll-view class=\"product-scroll\" scroll-y>\r\n        <view class=\"product-list\">\r\n          <view \r\n            v-for=\"(product, index) in products\" \r\n            :key=\"index\" \r\n            class=\"product-item\"\r\n            :class=\"{ selected: selectedProduct === product }\"\r\n            @click=\"selectProduct(product)\"\r\n          >\r\n            <view class=\"select-indicator\" v-if=\"selectedProduct === product\">\r\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M20 6L9 17L4 12\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n              </svg>\r\n            </view>\r\n            <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\r\n            <view class=\"product-info\">\r\n              <text class=\"product-name\">{{product.name}}</text>\r\n              <view class=\"product-price-row\">\r\n                <text class=\"product-price\">¥{{product.price}}</text>\r\n                <text class=\"product-commission\">佣金: ¥{{product.commission}}</text>\r\n              </view>\r\n              <view class=\"product-sales\">已售 {{product.sold}} | 库存 {{product.stock}}</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 卡片预览 -->\r\n    <view class=\"card-preview-section\" v-if=\"selectedProduct\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">卡片预览</text>\r\n        <view class=\"template-selector\" @click=\"showTemplates\">\r\n          <text>{{currentTemplate.name}}</text>\r\n          <view class=\"selector-arrow\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"card-preview\" :class=\"currentTemplate.value\">\r\n        <view class=\"preview-card\">\r\n          <image class=\"card-product-image\" :src=\"selectedProduct.image\" mode=\"aspectFill\"></image>\r\n          \r\n          <view class=\"card-content\">\r\n            <text class=\"card-product-name\">{{selectedProduct.name}}</text>\r\n            \r\n            <view class=\"card-price-row\">\r\n              <text class=\"card-price\">¥{{selectedProduct.price}}</text>\r\n              <text class=\"card-original-price\" v-if=\"selectedProduct.originalPrice\">¥{{selectedProduct.originalPrice}}</text>\r\n            </view>\r\n            \r\n            <view class=\"card-footer\">\r\n              <view class=\"card-shop-info\">\r\n                <image class=\"card-shop-logo\" :src=\"shopInfo.logo\" mode=\"aspectFill\"></image>\r\n                <text class=\"card-shop-name\">{{shopInfo.name}}</text>\r\n              </view>\r\n              \r\n              <view class=\"card-qrcode-hint\">\r\n                <view class=\"card-qrcode-placeholder\"></view>\r\n                <text class=\"card-scan-text\">扫码购买</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 自定义选项 -->\r\n      <view class=\"customization-options\">\r\n        <view class=\"option-group\">\r\n          <text class=\"option-title\">卡片样式</text>\r\n          <view class=\"style-options\">\r\n            <view \r\n              v-for=\"(style, index) in cardStyles\" \r\n              :key=\"index\" \r\n              class=\"style-option\" \r\n              :class=\"{ active: currentStyle === style.value }\"\r\n              @click=\"selectStyle(style.value)\"\r\n            >\r\n              <view class=\"style-color\" :style=\"{ backgroundColor: style.color }\"></view>\r\n              <text class=\"style-name\">{{style.name}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"option-group\">\r\n          <view class=\"toggle-option\">\r\n            <text class=\"toggle-label\">显示原价</text>\r\n            <switch :checked=\"showOriginalPrice\" @change=\"toggleOriginalPrice\" color=\"#6B0FBE\" />\r\n          </view>\r\n          \r\n          <view class=\"toggle-option\">\r\n            <text class=\"toggle-label\">添加促销标签</text>\r\n            <switch :checked=\"showPromoTag\" @change=\"togglePromoTag\" color=\"#6B0FBE\" />\r\n          </view>\r\n          \r\n          <view class=\"toggle-option\" v-if=\"showPromoTag\">\r\n            <text class=\"toggle-label\">促销标签文字</text>\r\n            <input \r\n              class=\"tag-input\" \r\n              type=\"text\" \r\n              v-model=\"promoTagText\" \r\n              placeholder=\"限时优惠\" \r\n              maxlength=\"6\"\r\n            />\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 生成按钮 -->\r\n      <view class=\"generate-button-container\">\r\n        <button class=\"generate-button\" @click=\"generateCard\">\r\n          <text>生成推广卡片</text>\r\n        </button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 空状态提示 -->\r\n    <view class=\"empty-state\" v-if=\"!selectedProduct\">\r\n      <image class=\"empty-image\" src=\"/static/images/empty-state.png\" mode=\"aspectFit\"></image>\r\n      <text class=\"empty-text\">请选择一个商品来创建推广卡片</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive } from 'vue';\r\n\r\n// 店铺信息\r\nconst shopInfo = reactive({\r\n  name: '磁州同城生活',\r\n  logo: '/static/images/logo.png'\r\n});\r\n\r\n// 商品列表\r\nconst products = reactive([\r\n  {\r\n    id: 1,\r\n    name: 'Apple iPhone 14 Pro Max 256GB 暗夜紫',\r\n    image: '/static/images/products/iphone.jpg',\r\n    price: 8999.00,\r\n    originalPrice: 9599.00,\r\n    commission: 450.00,\r\n    sold: 86,\r\n    stock: 100\r\n  },\r\n  {\r\n    id: 2,\r\n    name: 'Apple Watch Series 8 GPS 45mm 午夜色',\r\n    image: '/static/images/products/watch.jpg',\r\n    price: 3299.00,\r\n    originalPrice: 3499.00,\r\n    commission: 165.00,\r\n    sold: 72,\r\n    stock: 100\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'Apple AirPods Pro 2 主动降噪无线耳机',\r\n    image: '/static/images/products/airpods.jpg',\r\n    price: 1999.00,\r\n    originalPrice: 2199.00,\r\n    commission: 100.00,\r\n    sold: 94,\r\n    stock: 100\r\n  },\r\n  {\r\n    id: 4,\r\n    name: 'Apple MacBook Air M2 芯片 13.6英寸',\r\n    image: '/static/images/products/macbook.jpg',\r\n    price: 7999.00,\r\n    originalPrice: 8499.00,\r\n    commission: 400.00,\r\n    sold: 58,\r\n    stock: 100\r\n  },\r\n  {\r\n    id: 5,\r\n    name: 'Apple iPad Air 5 10.9英寸 Wi-Fi版',\r\n    image: '/static/images/products/ipad.jpg',\r\n    price: 4799.00,\r\n    originalPrice: 4999.00,\r\n    commission: 240.00,\r\n    sold: 65,\r\n    stock: 100\r\n  }\r\n]);\r\n\r\n// 卡片模板\r\nconst templates = [\r\n  { name: '横版卡片', value: 'horizontal' },\r\n  { name: '竖版卡片', value: 'vertical' },\r\n  { name: '简约卡片', value: 'minimal' }\r\n];\r\n\r\n// 卡片样式\r\nconst cardStyles = [\r\n  { name: '紫色', value: 'purple', color: '#6B0FBE' },\r\n  { name: '蓝色', value: 'blue', color: '#007AFF' },\r\n  { name: '绿色', value: 'green', color: '#34C759' },\r\n  { name: '红色', value: 'red', color: '#FF3B30' },\r\n  { name: '橙色', value: 'orange', color: '#FF9500' }\r\n];\r\n\r\n// 状态变量\r\nconst selectedProduct = ref(null);\r\nconst currentTemplate = ref(templates[0]);\r\nconst currentStyle = ref('purple');\r\nconst showOriginalPrice = ref(true);\r\nconst showPromoTag = ref(false);\r\nconst promoTagText = ref('限时优惠');\r\n\r\n// 方法\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\nconst showHistory = () => {\r\n  uni.navigateTo({\r\n    url: '/subPackages/merchant-admin-marketing/pages/distribution/product-cards/history'\r\n  });\r\n};\r\n\r\nconst showSearch = () => {\r\n  uni.showToast({\r\n    title: '搜索功能开发中',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\nconst selectProduct = (product) => {\r\n  selectedProduct.value = product;\r\n};\r\n\r\nconst showTemplates = () => {\r\n  uni.showActionSheet({\r\n    itemList: templates.map(item => item.name),\r\n    success: (res) => {\r\n      currentTemplate.value = templates[res.tapIndex];\r\n    }\r\n  });\r\n};\r\n\r\nconst selectStyle = (style) => {\r\n  currentStyle.value = style;\r\n};\r\n\r\nconst toggleOriginalPrice = (e) => {\r\n  showOriginalPrice.value = e.detail.value;\r\n};\r\n\r\nconst togglePromoTag = (e) => {\r\n  showPromoTag.value = e.detail.value;\r\n};\r\n\r\nconst generateCard = () => {\r\n  uni.showLoading({\r\n    title: '生成中...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    uni.showModal({\r\n      title: '卡片生成成功',\r\n      content: '推广卡片已生成，您可以保存到相册或直接分享',\r\n      confirmText: '分享',\r\n      cancelText: '保存',\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          uni.showShareMenu({\r\n            withShareTicket: true,\r\n            menus: ['shareAppMessage', 'shareTimeline']\r\n          });\r\n        } else {\r\n          uni.showToast({\r\n            title: '已保存到相册',\r\n            icon: 'success'\r\n          });\r\n        }\r\n      }\r\n    });\r\n  }, 1500);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.product-cards-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 商品选择部分 */\r\n.product-selection-section {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 20px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.search-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: rgba(107, 15, 190, 0.05);\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.search-btn text {\r\n  font-size: 14px;\r\n  color: #6B0FBE;\r\n  margin-left: 6px;\r\n}\r\n\r\n.product-scroll {\r\n  max-height: 320px;\r\n}\r\n\r\n.product-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.product-item {\r\n  display: flex;\r\n  background-color: #F8F9FC;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.product-item.selected {\r\n  border-color: #6B0FBE;\r\n  background-color: rgba(107, 15, 190, 0.05);\r\n}\r\n\r\n.select-indicator {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background-color: #6B0FBE;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1;\r\n}\r\n\r\n.product-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  object-fit: cover;\r\n}\r\n\r\n.product-info {\r\n  flex: 1;\r\n  padding: 10px;\r\n}\r\n\r\n.product-name {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 6px;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 1;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.product-price-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.product-price {\r\n  font-size: 14px;\r\n  color: #FF3B30;\r\n  font-weight: 500;\r\n}\r\n\r\n.product-commission {\r\n  font-size: 12px;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.product-sales {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 卡片预览部分 */\r\n.card-preview-section {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 20px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.template-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: rgba(107, 15, 190, 0.05);\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.template-selector text {\r\n  font-size: 14px;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.selector-arrow {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-right: 2px solid #6B0FBE;\r\n  border-bottom: 2px solid #6B0FBE;\r\n  transform: rotate(45deg);\r\n  margin-left: 8px;\r\n}\r\n\r\n.card-preview {\r\n  margin: 20px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.preview-card {\r\n  width: 300px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.card-product-image {\r\n  width: 100%;\r\n  height: 180px;\r\n  object-fit: cover;\r\n}\r\n\r\n.card-content {\r\n  padding: 16px;\r\n}\r\n\r\n.card-product-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 10px;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.card-price-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.card-price {\r\n  font-size: 18px;\r\n  color: #FF3B30;\r\n  font-weight: 600;\r\n  margin-right: 8px;\r\n}\r\n\r\n.card-original-price {\r\n  font-size: 14px;\r\n  color: #999;\r\n  text-decoration: line-through;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-shop-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.card-shop-logo {\r\n  width: 20px;\r\n  height: 20px;\r\n  border-radius: 10px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.card-shop-name {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.card-qrcode-hint {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.card-qrcode-placeholder {\r\n  width: 40px;\r\n  height: 40px;\r\n  background-color: #F0F0F0;\r\n  border-radius: 4px;\r\n  margin-bottom: 4px;\r\n  position: relative;\r\n}\r\n\r\n.card-qrcode-placeholder::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  background-color: #CCCCCC;\r\n  border-radius: 2px;\r\n}\r\n\r\n.card-scan-text {\r\n  font-size: 10px;\r\n  color: #999;\r\n}\r\n\r\n/* 自定义选项 */\r\n.customization-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.option-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.option-title {\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 12px;\r\n  display: block;\r\n}\r\n\r\n.style-options {\r\n  display: flex;\r\n  gap: 12px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.style-option {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  width: 60px;\r\n}\r\n\r\n.style-color {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  margin-bottom: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.style-name {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.style-option.active .style-color {\r\n  transform: scale(1.2);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.style-option.active .style-name {\r\n  color: #6B0FBE;\r\n  font-weight: 500;\r\n}\r\n\r\n.toggle-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #F0F0F0;\r\n}\r\n\r\n.toggle-option:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.toggle-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.tag-input {\r\n  width: 120px;\r\n  height: 36px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  padding: 0 12px;\r\n  font-size: 14px;\r\n  color: #333;\r\n  text-align: right;\r\n}\r\n\r\n/* 生成按钮 */\r\n.generate-button-container {\r\n  padding: 0 20px;\r\n}\r\n\r\n.generate-button {\r\n  width: 100%;\r\n  height: 48px;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  border-radius: 24px;\r\n  color: #FFFFFF;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: none;\r\n  box-shadow: 0 4px 12px rgba(107, 15, 190, 0.2);\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 20px;\r\n}\r\n\r\n.empty-image {\r\n  width: 120px;\r\n  height: 120px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n\r\n/* 卡片模板样式 */\r\n.horizontal .preview-card {\r\n  display: flex;\r\n  width: 300px;\r\n  height: 120px;\r\n}\r\n\r\n.horizontal .card-product-image {\r\n  width: 120px;\r\n  height: 120px;\r\n}\r\n\r\n.horizontal .card-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.horizontal .card-product-name {\r\n  font-size: 14px;\r\n  margin-bottom: 6px;\r\n  -webkit-line-clamp: 1;\r\n}\r\n\r\n.horizontal .card-price-row {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.horizontal .card-price {\r\n  font-size: 16px;\r\n}\r\n\r\n.horizontal .card-footer {\r\n  margin-top: auto;\r\n}\r\n\r\n.vertical .preview-card {\r\n  width: 240px;\r\n}\r\n\r\n.minimal .preview-card {\r\n  width: 280px;\r\n  border: 1px solid #EEEEEE;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.minimal .card-product-image {\r\n  height: 160px;\r\n}\r\n\r\n.minimal .card-content {\r\n  padding: 12px;\r\n}\r\n\r\n.minimal .card-product-name {\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.minimal .card-price {\r\n  font-size: 16px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/product-cards.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "uni", "MiniProgramPage"], "mappings": ";;;;;;;;;;;;;AA8JA,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAGD,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,YAAY;AAAA,MAChB,EAAE,MAAM,QAAQ,OAAO,aAAc;AAAA,MACrC,EAAE,MAAM,QAAQ,OAAO,WAAY;AAAA,MACnC,EAAE,MAAM,QAAQ,OAAO,UAAW;AAAA,IACpC;AAGA,UAAM,aAAa;AAAA,MACjB,EAAE,MAAM,MAAM,OAAO,UAAU,OAAO,UAAW;AAAA,MACjD,EAAE,MAAM,MAAM,OAAO,QAAQ,OAAO,UAAW;AAAA,MAC/C,EAAE,MAAM,MAAM,OAAO,SAAS,OAAO,UAAW;AAAA,MAChD,EAAE,MAAM,MAAM,OAAO,OAAO,OAAO,UAAW;AAAA,MAC9C,EAAE,MAAM,MAAM,OAAO,UAAU,OAAO,UAAW;AAAA,IACnD;AAGA,UAAM,kBAAkBC,cAAAA,IAAI,IAAI;AAChC,UAAM,kBAAkBA,cAAG,IAAC,UAAU,CAAC,CAAC;AACxC,UAAM,eAAeA,cAAAA,IAAI,QAAQ;AACjC,UAAM,oBAAoBA,cAAAA,IAAI,IAAI;AAClC,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAG/B,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAEA,UAAM,gBAAgB,CAAC,YAAY;AACjC,sBAAgB,QAAQ;AAAA,IAC1B;AAEA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,UAAU,IAAI,UAAQ,KAAK,IAAI;AAAA,QACzC,SAAS,CAAC,QAAQ;AAChB,0BAAgB,QAAQ,UAAU,IAAI,QAAQ;AAAA,QAC/C;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,cAAc,CAAC,UAAU;AAC7B,mBAAa,QAAQ;AAAA,IACvB;AAEA,UAAM,sBAAsB,CAAC,MAAM;AACjC,wBAAkB,QAAQ,EAAE,OAAO;AAAA,IACrC;AAEA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,mBAAa,QAAQ,EAAE,OAAO;AAAA,IAChC;AAEA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfA,4BAAAA,MAAI,cAAc;AAAA,gBAChB,iBAAiB;AAAA,gBACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,cACtD,CAAW;AAAA,YACX,OAAe;AACLA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtTA,GAAG,WAAWC,SAAe;"}