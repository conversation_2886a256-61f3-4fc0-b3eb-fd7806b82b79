"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_svg + _component_circle)();
}
const _sfc_main = {
  __name: "ShopCard",
  props: {
    shop: {
      type: Object,
      required: true
    },
    cardStyle: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["click", "enter"],
  setup(__props) {
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.shop.coverImage,
        b: __props.shop.logo,
        c: common_vendor.t(__props.shop.name),
        d: common_vendor.f(5, (i, k0, i0) => {
          return {
            a: "8e015a84-1-" + i0 + "," + ("8e015a84-0-" + i0),
            b: "8e015a84-0-" + i0,
            c: i,
            d: i <= Math.floor(__props.shop.rating) ? 1 : ""
          };
        }),
        e: common_vendor.p({
          d: "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z",
          fill: "currentColor"
        }),
        f: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "12",
          height: "12"
        }),
        g: common_vendor.t(__props.shop.rating),
        h: common_vendor.p({
          d: "M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round",
          fill: "none"
        }),
        i: common_vendor.p({
          cx: "12",
          cy: "9",
          r: "2",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round",
          fill: "none"
        }),
        j: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "14",
          height: "14"
        }),
        k: common_vendor.t(__props.shop.distance),
        l: common_vendor.p({
          d: "M19 5H5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2z",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round",
          fill: "none"
        }),
        m: common_vendor.p({
          d: "M3 7l9 6 9-6",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round",
          fill: "none"
        }),
        n: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "14",
          height: "14"
        }),
        o: common_vendor.t(__props.shop.orderCount),
        p: __props.shop.tags && __props.shop.tags.length > 0
      }, __props.shop.tags && __props.shop.tags.length > 0 ? {
        q: common_vendor.f(__props.shop.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        })
      } : {}, {
        r: __props.shop.description
      }, __props.shop.description ? {
        s: common_vendor.t(__props.shop.description)
      } : {}, {
        t: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        w: common_vendor.o(($event) => _ctx.$emit("enter", __props.shop)),
        x: common_vendor.s(__props.cardStyle),
        y: common_vendor.o(($event) => _ctx.$emit("click", __props.shop))
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8e015a84"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/mall/ShopCard.js.map
