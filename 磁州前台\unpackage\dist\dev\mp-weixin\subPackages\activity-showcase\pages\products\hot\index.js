"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const product = common_vendor.ref({
      id: 1,
      title: "iPhone 14 Pro 深空黑 256G",
      price: 7999,
      originalPrice: 8999,
      stock: 986,
      soldCount: 235,
      coverImage: "https://via.placeholder.com/300x300",
      images: [
        "https://via.placeholder.com/750x750",
        "https://via.placeholder.com/750x750",
        "https://via.placeholder.com/750x750"
      ],
      detailImages: [
        "https://via.placeholder.com/750x1500",
        "https://via.placeholder.com/750x1200"
      ],
      shopName: "Apple授权专卖店",
      shopLogo: "https://via.placeholder.com/100",
      description: "<p>iPhone 14 Pro 搭载 A16 仿生芯片，超强性能与电池续航，支持 5G 网络。</p><p>4800万像素主摄 + 1200万像素超广角 + 1200万像素长焦，带来专业级摄影体验。</p>",
      labels: [
        { type: "discount", text: "满3000减300" },
        { type: "new", text: "新品" }
      ],
      promotions: [
        { type: "discount", text: "满3000减300" },
        { type: "coupon", text: "新用户立减200元" }
      ],
      specs: [
        {
          title: "颜色",
          options: ["深空黑", "银色", "金色", "暗紫色"]
        },
        {
          title: "容量",
          options: ["128GB", "256GB", "512GB", "1TB"]
        }
      ],
      skus: [
        { specs: { "颜色": "深空黑", "容量": "256GB" }, price: 7999, stock: 986 },
        { specs: { "颜色": "深空黑", "容量": "512GB" }, price: 9999, stock: 568 },
        { specs: { "颜色": "银色", "容量": "256GB" }, price: 7999, stock: 756 },
        { specs: { "颜色": "银色", "容量": "512GB" }, price: 9999, stock: 325 },
        { specs: { "颜色": "金色", "容量": "256GB" }, price: 7999, stock: 421 },
        { specs: { "颜色": "金色", "容量": "512GB" }, price: 9999, stock: 132 },
        { specs: { "颜色": "暗紫色", "容量": "256GB" }, price: 7999, stock: 675 },
        { specs: { "颜色": "暗紫色", "容量": "512GB" }, price: 9999, stock: 289 }
      ]
    });
    const shop = common_vendor.ref({
      id: 1,
      name: "Apple授权专卖店",
      logo: "https://via.placeholder.com/100",
      rating: 4.8,
      productCount: 128,
      followersCount: 5689
    });
    const reviews = common_vendor.ref([
      {
        id: 1,
        userName: "用户123456",
        userAvatar: "https://via.placeholder.com/50",
        rating: 5,
        content: "手机非常好用，外观漂亮，系统流畅，续航也很棒，相机拍照效果很赞，总体来说非常满意！",
        time: "2023-07-15",
        images: [
          "https://via.placeholder.com/200x200",
          "https://via.placeholder.com/200x200",
          "https://via.placeholder.com/200x200"
        ]
      },
      {
        id: 2,
        userName: "磁县用户",
        userAvatar: "https://via.placeholder.com/50",
        rating: 4,
        content: "手机整体不错，就是价格有点贵，但质量和体验确实很好，值得购买。",
        time: "2023-07-10",
        images: []
      }
    ]);
    const relatedProducts = common_vendor.ref([
      {
        id: 2,
        title: "iPhone 14 银色 128G",
        coverImage: "https://via.placeholder.com/300x300",
        price: 6999,
        originalPrice: 7599
      },
      {
        id: 3,
        title: "iPhone 14 Pro Max 暗紫色 256G",
        coverImage: "https://via.placeholder.com/300x300",
        price: 8999,
        originalPrice: 9999
      },
      {
        id: 4,
        title: "AirPods Pro 2代",
        coverImage: "https://via.placeholder.com/300x300",
        price: 1999,
        originalPrice: 2199
      },
      {
        id: 5,
        title: "Apple Watch Series 8",
        coverImage: "https://via.placeholder.com/300x300",
        price: 2999,
        originalPrice: 3299
      }
    ]);
    const isFavorite = common_vendor.ref(false);
    const cartCount = common_vendor.ref(5);
    const showSpecSelector = common_vendor.ref(false);
    const selectedSpecMap = common_vendor.ref({});
    const quantity = common_vendor.ref(1);
    const maxQuantity = common_vendor.ref(10);
    const discount = common_vendor.computed(() => {
      return Math.floor(product.value.price / product.value.originalPrice * 10);
    });
    const selectedSpec = common_vendor.computed(() => {
      const specs = Object.entries(selectedSpecMap.value).map(([key, value]) => `${key}:${value}`);
      return specs.length > 0 ? specs.join(", ") : "";
    });
    const selectedSku = common_vendor.computed(() => {
      const specs = selectedSpecMap.value;
      const specKeys = Object.keys(specs);
      if (specKeys.length !== product.value.specs.length) {
        return null;
      }
      return product.value.skus.find((sku) => {
        return specKeys.every((key) => sku.specs[key] === specs[key]);
      });
    });
    function selectSpec(title, option) {
      if (!isOptionAvailable(title, option))
        return;
      selectedSpecMap.value = {
        ...selectedSpecMap.value,
        [title]: option
      };
    }
    function isOptionAvailable(title, option) {
      const currentSpecs = { ...selectedSpecMap.value, [title]: option };
      const selectedKeys = Object.keys(currentSpecs);
      if (selectedKeys.length < product.value.specs.length) {
        return product.value.skus.some((sku) => {
          return selectedKeys.every((key) => sku.specs[key] === currentSpecs[key]);
        });
      }
      const matchedSku = product.value.skus.find((sku) => {
        return selectedKeys.every((key) => sku.specs[key] === currentSpecs[key]);
      });
      return matchedSku && matchedSku.stock > 0;
    }
    function getSelectedSpecText() {
      const specs = Object.entries(selectedSpecMap.value);
      if (specs.length === 0) {
        return "请选择规格";
      }
      return specs.map(([key, value]) => `已选: ${key} ${value}`).join(", ");
    }
    function increaseQuantity() {
      if (quantity.value < maxQuantity.value) {
        quantity.value++;
      }
    }
    function decreaseQuantity() {
      if (quantity.value > 1) {
        quantity.value--;
      }
    }
    function openSpecSelector() {
      showSpecSelector.value = true;
    }
    function closeSpecSelector() {
      showSpecSelector.value = false;
    }
    function addToCart() {
      openSpecSelector();
    }
    function confirmAddToCart() {
      if (!selectedSku.value) {
        common_vendor.index.showToast({
          title: "请选择完整规格",
          icon: "none"
        });
        return;
      }
      cartCount.value += quantity.value;
      common_vendor.index.showToast({
        title: "已添加到购物车",
        icon: "success"
      });
      closeSpecSelector();
    }
    function buyNow() {
      openSpecSelector();
    }
    function confirmBuyNow() {
      if (!selectedSku.value) {
        common_vendor.index.showToast({
          title: "请选择完整规格",
          icon: "none"
        });
        return;
      }
      navigateTo("/subPackages/activity-showcase/pages/orders/confirm?productId=" + product.value.id);
      closeSpecSelector();
    }
    function toggleFavorite() {
      isFavorite.value = !isFavorite.value;
      common_vendor.index.showToast({
        title: isFavorite.value ? "已收藏" : "已取消收藏",
        icon: "none"
      });
    }
    function shareProduct() {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        success() {
          common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/products/hot/index.vue:563", "打开分享菜单成功");
        },
        fail() {
          common_vendor.index.showToast({
            title: "分享功能开发中",
            icon: "none"
          });
        }
      });
    }
    function previewImage(images, current) {
      common_vendor.index.previewImage({
        urls: images,
        current
      });
    }
    function navigateToShop() {
      navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.value.id}`);
    }
    function navigateToCart() {
      navigateTo("/subPackages/activity-showcase/pages/cart/index");
    }
    function navigateToReviews() {
      navigateTo(`/subPackages/activity-showcase/pages/products/reviews?id=${product.value.id}`);
    }
    function viewProductDetail(product2) {
      navigateTo(`/subPackages/activity-showcase/pages/products/detail?id=${product2.id}`);
    }
    function navigateTo(url) {
      common_vendor.index.navigateTo({
        url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/activity-showcase/pages/products/hot/index.vue:607", "导航失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    }
    function navigateBack() {
      common_vendor.index.navigateBack();
    }
    common_vendor.onMounted(() => {
      const productId = Number(getApp().globalData.route.query.id) || 1;
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/products/hot/index.vue:625", "加载商品详情:", productId);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(navigateBack),
        d: common_vendor.p({
          d: "M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        f: common_vendor.o(shareProduct),
        g: common_vendor.f(product.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        h: common_vendor.t(product.value.price.toFixed(2)),
        i: common_vendor.t(product.value.originalPrice.toFixed(2)),
        j: common_vendor.t(discount.value),
        k: common_vendor.t(product.value.soldCount),
        l: common_vendor.t(product.value.stock),
        m: common_vendor.t(product.value.title),
        n: common_vendor.p({
          d: "M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z",
          fill: isFavorite.value ? "#FF3B69" : "none",
          stroke: isFavorite.value ? "#FF3B69" : "#8E8E93",
          ["stroke-width"]: "2"
        }),
        o: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "22",
          height: "22"
        }),
        p: isFavorite.value ? 1 : "",
        q: common_vendor.t(isFavorite.value ? "已收藏" : "收藏"),
        r: common_vendor.o(toggleFavorite),
        s: common_vendor.f(product.value.labels, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag.text),
            b: common_vendor.n(tag.type),
            c: index
          };
        }),
        t: product.value.promotions && product.value.promotions.length > 0
      }, product.value.promotions && product.value.promotions.length > 0 ? {
        v: common_vendor.f(product.value.promotions, (promo, index, i0) => {
          return {
            a: common_vendor.t(promo.type === "coupon" ? "券" : "减"),
            b: common_vendor.n(promo.type),
            c: common_vendor.t(promo.text),
            d: index
          };
        })
      } : {}, {
        w: common_vendor.t(selectedSpec.value || "请选择规格数量"),
        x: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        y: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        z: common_vendor.o(openSpecSelector),
        A: product.value.shopLogo,
        B: common_vendor.t(product.value.shopName),
        C: common_vendor.f(5, (i, k0, i0) => {
          return {
            a: "337cb590-9-" + i0 + "," + ("337cb590-8-" + i0),
            b: common_vendor.p({
              d: "M12 2l2.4 7.2H22l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z",
              fill: i <= shop.value.rating ? "#FF9500" : "#E0E0E0"
            }),
            c: "337cb590-8-" + i0,
            d: i
          };
        }),
        D: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "14",
          height: "14"
        }),
        E: common_vendor.t(shop.value.rating.toFixed(1)),
        F: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF3B69",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        G: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "14",
          height: "14"
        }),
        H: common_vendor.o(navigateToShop),
        I: product.value.description,
        J: common_vendor.f(product.value.detailImages, (image, index, i0) => {
          return {
            a: index,
            b: image
          };
        }),
        K: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        L: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "14",
          height: "14"
        }),
        M: common_vendor.o(navigateToReviews),
        N: reviews.value.length > 0
      }, reviews.value.length > 0 ? {
        O: common_vendor.f(reviews.value.slice(0, 2), (review, index, i0) => {
          return common_vendor.e({
            a: review.userAvatar,
            b: common_vendor.t(review.userName),
            c: common_vendor.f(5, (i, k1, i1) => {
              return {
                a: "337cb590-15-" + i0 + "-" + i1 + "," + ("337cb590-14-" + i0 + "-" + i1),
                b: common_vendor.p({
                  d: "M12 2l2.4 7.2H22l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z",
                  fill: i <= review.rating ? "#FF9500" : "#E0E0E0"
                }),
                c: "337cb590-14-" + i0 + "-" + i1,
                d: i
              };
            }),
            d: common_vendor.t(review.time),
            e: common_vendor.t(review.content),
            f: review.images && review.images.length > 0
          }, review.images && review.images.length > 0 ? {
            g: common_vendor.f(review.images, (img, imgIndex, i1) => {
              return {
                a: imgIndex,
                b: img,
                c: common_vendor.o(($event) => previewImage(review.images, imgIndex), imgIndex)
              };
            })
          } : {}, {
            h: index
          });
        }),
        P: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "12",
          height: "12"
        })
      } : {}, {
        Q: common_vendor.f(relatedProducts.value, (item, index, i0) => {
          return {
            a: item.coverImage,
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.price.toFixed(2)),
            d: index,
            e: common_vendor.o(($event) => viewProductDetail(item), index)
          };
        }),
        R: common_vendor.p({
          d: "M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round",
          fill: "none"
        }),
        S: common_vendor.p({
          d: "M9 22V12h6v10",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round",
          fill: "none"
        }),
        T: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        U: common_vendor.o(navigateToShop),
        V: common_vendor.p({
          d: "M9 22a1 1 0 100-2 1 1 0 000 2zM20 22a1 1 0 100-2 1 1 0 000 2zM1 1h4l2.68 13.39a2 2 0 002 1.61h9.72a2 2 0 002-1.61L23 6H6",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round",
          fill: "none"
        }),
        W: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        X: cartCount.value > 0
      }, cartCount.value > 0 ? {
        Y: common_vendor.t(cartCount.value)
      } : {}, {
        Z: common_vendor.o(navigateToCart),
        aa: common_vendor.o(addToCart),
        ab: common_vendor.o(buyNow),
        ac: showSpecSelector.value
      }, showSpecSelector.value ? {
        ad: product.value.coverImage,
        ae: common_vendor.t(selectedSku.value ? selectedSku.value.price.toFixed(2) : product.value.price.toFixed(2)),
        af: common_vendor.t(selectedSku.value ? selectedSku.value.stock : product.value.stock),
        ag: common_vendor.t(getSelectedSpecText()),
        ah: common_vendor.p({
          d: "M18 6L6 18M6 6l12 12",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ai: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        aj: common_vendor.o(closeSpecSelector),
        ak: common_vendor.f(product.value.specs, (group, groupIndex, i0) => {
          return {
            a: common_vendor.t(group.title),
            b: common_vendor.f(group.options, (option, optionIndex, i1) => {
              return {
                a: common_vendor.t(option),
                b: optionIndex,
                c: selectedSpecMap.value[group.title] === option ? 1 : "",
                d: !isOptionAvailable(group.title, option) ? 1 : "",
                e: common_vendor.o(($event) => selectSpec(group.title, option), optionIndex)
              };
            }),
            c: groupIndex
          };
        }),
        al: common_vendor.p({
          d: "M5 12h14",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        am: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        an: quantity.value <= 1 ? 1 : "",
        ao: common_vendor.o(decreaseQuantity),
        ap: quantity.value,
        aq: common_vendor.o(common_vendor.m(($event) => quantity.value = $event.detail.value, {
          number: true
        })),
        ar: common_vendor.p({
          d: "M12 5v14M5 12h14",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        as: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        at: quantity.value >= maxQuantity.value ? 1 : "",
        av: common_vendor.o(increaseQuantity),
        aw: common_vendor.o(confirmAddToCart),
        ax: common_vendor.o(confirmBuyNow),
        ay: common_vendor.o(() => {
        }),
        az: common_vendor.o(closeSpecSelector)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-337cb590"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/products/hot/index.js.map
