{"version": 3, "file": "cu-custom.js", "sources": ["components/cu-custom.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9jdS1jdXN0b20udnVl"], "sourcesContent": ["<template>\n  <view class=\"cu-custom\" :style=\"[{height: CustomBar + 'px'}]\">\n    <view class=\"cu-bar fixed\" :style=\"style\" :class=\"[bgImage!=''?'none-bg text-white bg-img':'',bgColor]\">\n      <view class=\"action back-action\" @tap=\"BackPage\" hover-class=\"back-hover\" v-if=\"isBack\">\n        <text class=\"cuIcon-back back-icon\"></text>\n        <slot name=\"backText\"></slot>\n      </view>\n      <view class=\"content\" :style=\"[{top:StatusBar + 'px'}]\">\n        <slot name=\"content\"></slot>\n      </view>\n      <slot name=\"right\"></slot>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'cu-custom',\n  data() {\n    return {\n      StatusBar: this.StatusBar,\n      CustomBar: this.CustomBar\n    };\n  },\n  computed: {\n    style() {\n      var StatusBar = this.StatusBar;\n      var CustomBar = this.CustomBar;\n      var bgImage = this.bgImage;\n      var style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;\n      if (this.bgImage) {\n        style = `${style}background-image:url(${bgImage});`;\n      }\n      return style;\n    }\n  },\n  props: {\n    bgColor: {\n      type: String,\n      default: ''\n    },\n    isBack: {\n      type: Boolean,\n      default: false\n    },\n    bgImage: {\n      type: String,\n      default: ''\n    }\n  },\n  created() {\n    this.StatusBar = uni.getSystemInfoSync().statusBarHeight;\n    this.CustomBar = this.StatusBar + 66;\n  },\n  methods: {\n    BackPage() {\n      console.log('返回按钮被点击');\n      \n      // 添加振动反馈\n      uni.vibrateShort({\n        success: function() {\n          console.log('振动成功');\n        }\n      });\n      \n      // 延迟100毫秒再返回，让用户感受到振动反馈\n      setTimeout(() => {\n        uni.navigateBack({\n          delta: 1,\n          fail: () => {\n            // 如果返回失败，跳转到我的页面\n            uni.switchTab({\n              url: '/pages/my/my'\n            });\n          }\n        });\n      }, 100);\n      \n      // 触发返回事件\n      this.$emit('back');\n    }\n  }\n}\n</script>\n\n<style>\n.cu-custom {\n  display: block;\n  position: relative;\n  width: 100%;\n  z-index: 9999;\n}\n\n.cu-bar {\n  display: flex;\n  position: relative;\n  align-items: center;\n  min-height: 30rpx;\n  justify-content: space-between;\n  padding: 0 15rpx;\n}\n\n.cu-bar.fixed {\n  position: fixed;\n  width: 100%;\n  top: 0;\n  z-index: 1024;\n  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.cu-bar .action {\n  display: flex;\n  align-items: center;\n  height: 30rpx;\n  justify-content: center;\n  max-width: 100%;\n  margin: 0;\n  position: relative;\n}\n\n.cu-bar .back-action {\n  margin-left: 15rpx;\n  font-size: 24rpx;\n  min-width: 30rpx;\n  height: 44rpx;\n  padding: 0 10rpx;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  border-radius: 16rpx;\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  z-index: 2;\n}\n\n.back-hover {\n  opacity: 0.8;\n  transform: scale(0.95);\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.cu-bar .content {\n  font-size: 32rpx;\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0px;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 44rpx;\n  transform: translateY(50%);\n  color: #ffffff;\n  font-weight: 600;\n  text-align: center;\n  width: 100%;\n  padding: 0 60rpx;\n}\n\n.back-icon {\n  width: 38rpx;\n  height: 38rpx;\n  margin-right: 0;\n}\n\n.cu-custom .cu-bar {\n  min-height: 0;\n  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);\n  padding-bottom: 0px;\n}\n\n.cu-custom .cu-bar .action {\n  display: flex;\n  align-items: center;\n  height: 30rpx;\n  justify-content: flex-start;\n  max-width: 60rpx;\n  margin-left: 15rpx;\n  font-size: 22rpx;\n}\n\n.cu-custom .cu-bar .action .back-icon {\n  width: 38rpx;\n  height: 38rpx;\n  margin-right: 0;\n}\n\n.text-white {\n  color: #ffffff;\n}\n\n/* 添加统一的蓝色系风格 */\n.bg-blue {\n  background-color: #007AFF;\n  color: #ffffff;\n}\n\n.bg-gradient-blue {\n  background-image: linear-gradient(135deg, #0066FF, #0052CC);\n  color: #ffffff;\n}\n\n.bg-white {\n  background-color: #FFFFFF;\n  color: #333333;\n}\n\n/* 增加点击区域和反馈 */\n.cu-bar .action.back-action {\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n/* 确保文本居中 */\n.text-center {\n  text-align: center;\n  width: 100%;\n  display: block;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/cu-custom.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAgBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,WAAW,KAAK;AAAA,MAChB,WAAW,KAAK;AAAA;EAEnB;AAAA,EACD,UAAU;AAAA,IACR,QAAQ;AACN,UAAI,YAAY,KAAK;AACrB,UAAI,YAAY,KAAK;AACrB,UAAI,UAAU,KAAK;AACnB,UAAI,QAAQ,UAAU,SAAS,kBAAkB,SAAS;AAC1D,UAAI,KAAK,SAAS;AAChB,gBAAQ,GAAG,KAAK,wBAAwB,OAAO;AAAA,MACjD;AACA,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,UAAU;AACR,SAAK,YAAYA,cAAAA,MAAI,kBAAiB,EAAG;AACzC,SAAK,YAAY,KAAK,YAAY;AAAA,EACnC;AAAA,EACD,SAAS;AAAA,IACP,WAAW;AACTA,oBAAAA,qDAAY,SAAS;AAGrBA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS,WAAW;AAClBA,wBAAAA,MAAA,MAAA,OAAA,kCAAY,MAAM;AAAA,QACpB;AAAA,MACF,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAAA,MAAI,aAAa;AAAA,UACf,OAAO;AAAA,UACP,MAAM,MAAM;AAEVA,0BAAAA,MAAI,UAAU;AAAA,cACZ,KAAK;AAAA,YACP,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACF,GAAE,GAAG;AAGN,WAAK,MAAM,MAAM;AAAA,IACnB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;ACjFA,GAAG,gBAAgB,SAAS;"}