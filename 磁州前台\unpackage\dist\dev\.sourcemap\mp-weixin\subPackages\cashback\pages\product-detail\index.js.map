{"version": 3, "file": "index.js", "sources": ["subPackages/cashback/pages/product-detail/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2FzaGJhY2tccGFnZXNccHJvZHVjdC1kZXRhaWxcaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"product-detail-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <custom-navbar title=\"商品详情\" :show-back=\"true\"></custom-navbar>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content-container\">\r\n      <!-- 商品信息 -->\r\n      <view class=\"product-info-section\">\r\n        <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\r\n        <view class=\"product-info\">\r\n          <text class=\"product-title\">{{ product.title }}</text>\r\n          <view class=\"product-price-row\">\r\n            <view class=\"price-container\">\r\n              <text class=\"price-symbol\">¥</text>\r\n              <text class=\"price-value\">{{ product.price }}</text>\r\n            </view>\r\n            <view class=\"cashback-tag\">\r\n              <text>返{{ product.cashback }}元</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"product-source\">\r\n            <image class=\"source-icon\" :src=\"product.platformIcon\" mode=\"aspectFit\"></image>\r\n            <text class=\"source-name\">{{ product.platform }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 比价功能 -->\r\n      <view class=\"price-compare-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">全网比价</text>\r\n          <view class=\"sort-options\">\r\n            <text \r\n              class=\"sort-option\" \r\n              :class=\"{'sort-option--active': sortBy === 'price'}\"\r\n              @tap=\"sortPrices('price')\"\r\n            >按价格</text>\r\n            <text \r\n              class=\"sort-option\" \r\n              :class=\"{'sort-option--active': sortBy === 'cashback'}\"\r\n              @tap=\"sortPrices('cashback')\"\r\n            >按返利</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"price-list\">\r\n          <view \r\n            class=\"price-item\" \r\n            v-for=\"(item, index) in sortedPriceList\" \r\n            :key=\"index\"\r\n            :class=\"{'price-item--best': item.isBest}\"\r\n            @tap=\"navigateToStore(item)\"\r\n          >\r\n            <view class=\"price-item-left\">\r\n              <image class=\"platform-icon\" :src=\"item.platformIcon\" mode=\"aspectFit\"></image>\r\n              <view class=\"price-item-info\">\r\n                <text class=\"platform-name\">{{ item.platform }}</text>\r\n                <view class=\"price-tag\" v-if=\"item.isBest\">\r\n                  <text>{{ item.bestTag }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            <view class=\"price-item-right\">\r\n              <view class=\"price-container\">\r\n                <text class=\"price-value\">¥{{ item.price }}</text>\r\n              </view>\r\n              <text class=\"cashback-value\">返¥{{ item.cashback }}</text>\r\n              <view class=\"go-button\">\r\n                <text>去购买</text>\r\n                <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                  <path fill=\"#FFFFFF\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n                </svg>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 商品详情 -->\r\n      <view class=\"product-detail-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">商品详情</text>\r\n        </view>\r\n        <rich-text :nodes=\"product.description\"></rich-text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport CustomNavbar from '../../components/CustomNavbar.vue';\r\n\r\nexport default {\r\n  components: {\r\n    CustomNavbar\r\n  },\r\n  data() {\r\n    return {\r\n      link: '',\r\n      product: {\r\n        id: 1,\r\n        title: 'Apple iPhone 15 Pro Max (A2850) 256GB 原色钛金属',\r\n        image: '/static/images/cashback/product-1.png',\r\n        price: '9999.00',\r\n        cashback: '300.00',\r\n        platform: '京东',\r\n        platformIcon: '/static/images/cashback/platform-jd.png',\r\n        description: '<div><p>iPhone 15 Pro Max采用航空级钛金属材质，搭载A17 Pro芯片，4nm工艺制程，全新USB-C接口，支持USB 3传输速度，最高10Gbps...</p></div>'\r\n      },\r\n      priceList: [\r\n        {\r\n          platform: '京东',\r\n          platformIcon: '/static/images/cashback/platform-jd.png',\r\n          price: '9999.00',\r\n          cashback: '300.00',\r\n          isBest: true,\r\n          bestTag: '返利最高',\r\n          url: 'https://item.jd.com/example'\r\n        },\r\n        {\r\n          platform: '天猫',\r\n          platformIcon: '/static/images/cashback/platform-tmall.png',\r\n          price: '9989.00',\r\n          cashback: '280.00',\r\n          isBest: true,\r\n          bestTag: '价格最低',\r\n          url: 'https://detail.tmall.com/example'\r\n        },\r\n        {\r\n          platform: '苏宁',\r\n          platformIcon: '/static/images/cashback/platform-suning.png',\r\n          price: '10099.00',\r\n          cashback: '290.00',\r\n          isBest: false,\r\n          bestTag: '',\r\n          url: 'https://product.suning.com/example'\r\n        },\r\n        {\r\n          platform: '拼多多',\r\n          platformIcon: '/static/images/cashback/platform-pdd.png',\r\n          price: '10199.00',\r\n          cashback: '250.00',\r\n          isBest: false,\r\n          bestTag: '',\r\n          url: 'https://mobile.yangkeduo.com/example'\r\n        },\r\n        {\r\n          platform: '抖音',\r\n          platformIcon: '/static/images/cashback/platform-douyin.png',\r\n          price: '10299.00',\r\n          cashback: '260.00',\r\n          isBest: false,\r\n          bestTag: '',\r\n          url: 'https://haohuo.douyin.com/example'\r\n        }\r\n      ],\r\n      sortBy: 'price' // 默认按价格排序\r\n    };\r\n  },\r\n  computed: {\r\n    sortedPriceList() {\r\n      if (this.sortBy === 'price') {\r\n        return [...this.priceList].sort((a, b) => parseFloat(a.price) - parseFloat(b.price));\r\n      } else {\r\n        return [...this.priceList].sort((a, b) => parseFloat(b.cashback) - parseFloat(a.cashback));\r\n      }\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 设置页面不显示系统导航栏\r\n    uni.setNavigationBarColor({\r\n      frontColor: '#ffffff',\r\n      backgroundColor: '#9C27B0'\r\n    });\r\n    \r\n    if (options.link) {\r\n      this.link = decodeURIComponent(options.link);\r\n      this.fetchProductInfo();\r\n    }\r\n    \r\n    if (options.id) {\r\n      this.fetchProductById(options.id);\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取商品信息\r\n    fetchProductInfo() {\r\n      // 这里应该发送请求到后端获取商品信息\r\n      // 使用this.link作为参数\r\n      uni.showLoading({\r\n        title: '获取商品信息...'\r\n      });\r\n      \r\n      // 模拟请求\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        // 实际项目中应该用真实数据替换\r\n        console.log('获取商品信息:', this.link);\r\n      }, 1000);\r\n    },\r\n    \r\n    // 根据ID获取商品\r\n    fetchProductById(id) {\r\n      // 这里应该发送请求到后端获取商品信息\r\n      // 使用id作为参数\r\n      uni.showLoading({\r\n        title: '获取商品信息...'\r\n      });\r\n      \r\n      // 模拟请求\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        // 实际项目中应该用真实数据替换\r\n        console.log('获取商品信息:', id);\r\n      }, 1000);\r\n    },\r\n    \r\n    // 排序价格列表\r\n    sortPrices(type) {\r\n      this.sortBy = type;\r\n    },\r\n    \r\n    // 跳转到商店\r\n    navigateToStore(item) {\r\n      // 这里可以跳转到对应的电商平台\r\n      // 实际项目中可能需要通过特殊方式打开\r\n      uni.showModal({\r\n        title: '跳转提示',\r\n        content: `即将跳转到${item.platform}购买，确认继续吗？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 可以使用plus.runtime.openURL(item.url)在APP中打开外部链接\r\n            // 或者使用其他方式处理\r\n            uni.showToast({\r\n              title: `正在跳转到${item.platform}`,\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.product-detail-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.content-container {\r\n  padding-top: calc(var(--status-bar-height) + 44px);\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.product-info-section {\r\n  background-color: #FFFFFF;\r\n  padding: 16px;\r\n  \r\n  .product-image {\r\n    width: 100%;\r\n    height: 300px;\r\n    border-radius: 12px;\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .product-info {\r\n    .product-title {\r\n      font-size: 16px;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      margin-bottom: 12px;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 2;\r\n      -webkit-box-orient: vertical;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n    }\r\n    \r\n    .product-price-row {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      margin-bottom: 12px;\r\n      \r\n      .price-container {\r\n        display: flex;\r\n        align-items: baseline;\r\n        \r\n        .price-symbol {\r\n          font-size: 14px;\r\n          color: #FF6B6B;\r\n          margin-right: 2px;\r\n        }\r\n        \r\n        .price-value {\r\n          font-size: 20px;\r\n          font-weight: 600;\r\n          color: #FF6B6B;\r\n        }\r\n      }\r\n      \r\n      .cashback-tag {\r\n        background-color: rgba(156, 39, 176, 0.1);\r\n        padding: 4px 8px;\r\n        border-radius: 4px;\r\n        \r\n        text {\r\n          font-size: 12px;\r\n          color: #9C27B0;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .product-source {\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .source-icon {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-right: 4px;\r\n      }\r\n      \r\n      .source-name {\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.price-compare-section {\r\n  margin-top: 12px;\r\n  background-color: #FFFFFF;\r\n  padding: 16px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  \r\n  .section-title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333333;\r\n  }\r\n  \r\n  .sort-options {\r\n    display: flex;\r\n    \r\n    .sort-option {\r\n      font-size: 14px;\r\n      color: #999999;\r\n      margin-left: 16px;\r\n      position: relative;\r\n      \r\n      &--active {\r\n        color: #9C27B0;\r\n        \r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          bottom: -4px;\r\n          left: 0;\r\n          right: 0;\r\n          height: 2px;\r\n          background-color: #9C27B0;\r\n          border-radius: 1px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.price-list {\r\n  .price-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px 0;\r\n    border-bottom: 1px solid #EEEEEE;\r\n    \r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    &--best {\r\n      background-color: rgba(156, 39, 176, 0.05);\r\n      border-radius: 8px;\r\n      padding: 16px 8px;\r\n      margin: 0 -8px;\r\n    }\r\n    \r\n    .price-item-left {\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .platform-icon {\r\n        width: 24px;\r\n        height: 24px;\r\n        margin-right: 8px;\r\n      }\r\n      \r\n      .price-item-info {\r\n        .platform-name {\r\n          font-size: 14px;\r\n          color: #333333;\r\n          margin-bottom: 4px;\r\n        }\r\n        \r\n        .price-tag {\r\n          display: inline-block;\r\n          background-color: #9C27B0;\r\n          border-radius: 2px;\r\n          padding: 2px 4px;\r\n          \r\n          text {\r\n            font-size: 10px;\r\n            color: #FFFFFF;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .price-item-right {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: flex-end;\r\n      \r\n      .price-container {\r\n        margin-bottom: 4px;\r\n        \r\n        .price-value {\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #FF6B6B;\r\n        }\r\n      }\r\n      \r\n      .cashback-value {\r\n        font-size: 12px;\r\n        color: #9C27B0;\r\n        margin-bottom: 8px;\r\n      }\r\n      \r\n      .go-button {\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: #9C27B0;\r\n        border-radius: 12px;\r\n        padding: 4px 10px;\r\n        \r\n        text {\r\n          font-size: 12px;\r\n          color: #FFFFFF;\r\n        }\r\n        \r\n        .arrow-icon {\r\n          margin-left: 2px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.product-detail-section {\r\n  margin-top: 12px;\r\n  background-color: #FFFFFF;\r\n  padding: 16px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/pages/product-detail/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2FA,MAAK,eAAgB,MAAW;AAEhC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,QACP,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,cAAc;AAAA,QACd,aAAa;AAAA,MACd;AAAA,MACD,WAAW;AAAA,QACT;AAAA,UACE,UAAU;AAAA,UACV,cAAc;AAAA,UACd,OAAO;AAAA,UACP,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,cAAc;AAAA,UACd,OAAO;AAAA,UACP,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,cAAc;AAAA,UACd,OAAO;AAAA,UACP,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,cAAc;AAAA,UACd,OAAO;AAAA,UACP,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,cAAc;AAAA,UACd,OAAO;AAAA,UACP,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,KAAK;AAAA,QACP;AAAA,MACD;AAAA,MACD,QAAQ;AAAA;AAAA;EAEX;AAAA,EACD,UAAU;AAAA,IACR,kBAAkB;AAChB,UAAI,KAAK,WAAW,SAAS;AAC3B,eAAO,CAAC,GAAG,KAAK,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,KAAK,IAAI,WAAW,EAAE,KAAK,CAAC;AAAA,aAC9E;AACL,eAAO,CAAC,GAAG,KAAK,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,QAAQ,IAAI,WAAW,EAAE,QAAQ,CAAC;AAAA,MAC3F;AAAA,IACF;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AAEdA,kBAAAA,MAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;AAED,QAAI,QAAQ,MAAM;AAChB,WAAK,OAAO,mBAAmB,QAAQ,IAAI;AAC3C,WAAK,iBAAgB;AAAA,IACvB;AAEA,QAAI,QAAQ,IAAI;AACd,WAAK,iBAAiB,QAAQ,EAAE;AAAA,IAClC;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,mBAAmB;AAGjBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAA,MAAA,MAAA,OAAA,8DAAY,WAAW,KAAK,IAAI;AAAA,MACjC,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB,IAAI;AAGnBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAY,MAAA,MAAA,OAAA,8DAAA,WAAW,EAAE;AAAA,MAC1B,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,WAAK,SAAS;AAAA,IACf;AAAA;AAAA,IAGD,gBAAgB,MAAM;AAGpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,KAAK,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAGfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,QAAQ,KAAK,QAAQ;AAAA,cAC5B,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClPA,GAAG,WAAW,eAAe;"}