/**
 * 积分服务
 * 管理积分规则和积分操作
 */

import { getToken } from './authService';
import { request } from './request';
import { showToast } from './uiHelper';

/**
 * 积分行为类型
 */
export const PointsActionType = {
  PURCHASE: 'purchase',     // 商品购买
  CHECKIN: 'checkin',       // 每日签到
  SHARE: 'share',           // 分享商品
  REVIEW: 'review',         // 评价商品
  REGISTER: 'register',     // 注册会员
  BIRTHDAY: 'birthday',     // 生日特权
  FIRST_ORDER: 'first_order', // 首单奖励
  INVITE: 'invite',         // 邀请新用户
  COMPLETE_PROFILE: 'complete_profile', // 完善资料
  FOLLOW_SHOP: 'follow_shop', // 关注店铺
};

/**
 * 获取积分规则列表
 * @returns {Promise<Array>} 积分规则列表
 */
export const getPointsRules = async () => {
  try {
    // 调用API获取积分规则
    const result = await request({
      url: '/api/points/rules',
      method: 'GET'
    });
    
    if (result && result.success) {
      return result.data.rules || [];
    }
    
    // 如果API调用失败，返回默认规则
    return getDefaultRules();
  } catch (error) {
    console.error('获取积分规则失败:', error);
    return getDefaultRules();
  }
};

/**
 * 获取默认积分规则
 * @returns {Array} 默认积分规则列表
 */
const getDefaultRules = () => {
  return [
    {
      id: 1,
      type: PointsActionType.PURCHASE,
      title: '商品购买',
      description: '每消费1元获得1积分',
      points: 1,
      isRatio: true, // 是否按比例计算
      ratio: 1, // 1元 = 1积分
      maxPoints: 0, // 0表示无上限
      dailyLimit: 0, // 0表示无每日限制
    },
    {
      id: 2,
      type: PointsActionType.CHECKIN,
      title: '每日签到',
      description: '每日签到获得积分奖励',
      points: 10,
      isRatio: false,
      maxPoints: 10,
      dailyLimit: 1, // 每日限制1次
    },
    {
      id: 3,
      type: PointsActionType.SHARE,
      title: '分享商品',
      description: '分享商品到社交媒体获得积分',
      points: 5,
      isRatio: false,
      maxPoints: 5,
      dailyLimit: 3, // 每日限制3次
    }
  ];
};

/**
 * 根据行为类型获取积分规则
 * @param {String} actionType 行为类型
 * @returns {Promise<Object>} 积分规则
 */
export const getPointsRuleByType = async (actionType) => {
  const rules = await getPointsRules();
  return rules.find(rule => rule.type === actionType) || null;
};

/**
 * 计算购买商品可获得的积分
 * @param {Number} amount 购买金额
 * @returns {Promise<Number>} 可获得的积分
 */
export const calculatePurchasePoints = async (amount) => {
  const rule = await getPointsRuleByType(PointsActionType.PURCHASE);
  if (!rule) return 0;
  
  if (rule.isRatio) {
    const points = Math.floor(amount * rule.ratio);
    return rule.maxPoints > 0 ? Math.min(points, rule.maxPoints) : points;
  }
  
  return rule.points;
};

/**
 * 获取用户积分余额
 * @returns {Promise<Number>} 积分余额
 */
export const getPointsBalance = async () => {
  try {
    const token = getToken();
    if (!token) {
      return 0;
    }
    
    const result = await request({
      url: '/api/points/balance',
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      return result.data.balance || 0;
    }
    
    return 0;
  } catch (error) {
    console.error('获取积分余额失败:', error);
    return 0;
  }
};

/**
 * 获取积分明细列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 积分明细列表和分页信息
 */
export const getPointsHistory = async (params = {}) => {
  try {
    const token = getToken();
    if (!token) {
      return { list: [], total: 0 };
    }
    
    const result = await request({
      url: '/api/points/history',
      method: 'GET',
      data: params,
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      return {
        list: result.data.list || [],
        total: result.data.total || 0
      };
    }
    
    return { list: [], total: 0 };
  } catch (error) {
    console.error('获取积分明细失败:', error);
    return { list: [], total: 0 };
  }
};

/**
 * 执行签到
 * @returns {Promise<Object>} 签到结果
 */
export const doCheckin = async () => {
  try {
    const token = getToken();
    if (!token) {
      showToast('请先登录');
      return { success: false, error: 'NOT_LOGGED_IN' };
    }
    
    // 检查今日是否已签到
    const checkedToday = await hasCheckedToday();
    if (checkedToday) {
      showToast('今日已签到');
      return { success: false, error: 'ALREADY_CHECKED' };
    }
    
    // 调用签到API
    const result = await request({
      url: '/api/points/checkin',
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      const points = result.data.points || 0;
      const continuousDays = result.data.continuousDays || 1;
      const bonusPoints = result.data.bonusPoints || 0;
      
      // 显示签到成功
      if (bonusPoints > 0) {
        showToast(`签到成功，获得${points}积分，连续签到奖励${bonusPoints}积分`);
      } else {
        showToast(`签到成功，获得${points}积分`);
      }
      
      return {
        success: true,
        points,
        continuousDays,
        bonusPoints,
        totalPoints: points + bonusPoints
      };
    }
    
    showToast('签到失败，请重试');
    return { success: false, error: 'API_ERROR' };
  } catch (error) {
    console.error('签到失败:', error);
    showToast('签到失败，请重试');
    return { success: false, error };
  }
};

/**
 * 检查今日是否已签到
 * @returns {Promise<Boolean>} 是否已签到
 */
export const hasCheckedToday = async () => {
  try {
    const token = getToken();
    if (!token) {
      return false;
    }
    
    const result = await request({
      url: '/api/points/checkin/status',
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      return result.data.checked || false;
    }
    
    return false;
  } catch (error) {
    console.error('检查签到状态失败:', error);
    return false;
  }
};

/**
 * 获取签到记录
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 签到记录和统计信息
 */
export const getCheckinHistory = async (params = {}) => {
  try {
    const token = getToken();
    if (!token) {
      return { list: [], continuousDays: 0, totalDays: 0 };
    }
    
    const result = await request({
      url: '/api/points/checkin/history',
      method: 'GET',
      data: params,
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      return {
        list: result.data.list || [],
        continuousDays: result.data.continuousDays || 0,
        totalDays: result.data.totalDays || 0
      };
    }
    
    return { list: [], continuousDays: 0, totalDays: 0 };
  } catch (error) {
    console.error('获取签到记录失败:', error);
    return { list: [], continuousDays: 0, totalDays: 0 };
  }
};

/**
 * 获取当月签到日历数据
 * @returns {Promise<Array>} 当月签到数据
 */
export const getMonthCheckinCalendar = async () => {
  try {
    const token = getToken();
    if (!token) {
      return [];
    }
    
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    
    const result = await request({
      url: '/api/points/checkin/calendar',
      method: 'GET',
      data: {
        year,
        month
      },
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      return result.data.days || [];
    }
    
    return [];
  } catch (error) {
    console.error('获取签到日历失败:', error);
    return [];
  }
};

/**
 * 记录分享行为并获取积分
 * @param {String} type 内容类型
 * @param {String|Number} contentId 内容ID
 * @param {String} channel 分享渠道
 * @returns {Promise<Object>} 分享结果
 */
export const recordShareAction = async (type, contentId, channel) => {
  try {
    const token = getToken();
    if (!token) {
      return { success: false, points: 0 };
    }
    
    // 检查今日分享次数是否达到上限
    const shareLimit = await checkShareLimit();
    if (shareLimit.reached) {
      // 已达到上限，但仍然记录分享行为，只是不给积分
      await recordShareWithoutPoints(type, contentId, channel);
      return { success: true, points: 0, reachedLimit: true };
    }
    
    // 调用API记录分享行为并获取积分
    const result = await request({
      url: '/api/points/share',
      method: 'POST',
      data: {
        contentType: type,
        contentId,
        channel
      },
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      const points = result.data.points || 0;
      return { success: true, points };
    }
    
    return { success: false, points: 0 };
  } catch (error) {
    console.error('记录分享行为失败:', error);
    return { success: false, points: 0, error };
  }
};

/**
 * 仅记录分享行为，不获取积分
 * @param {String} type 内容类型
 * @param {String|Number} contentId 内容ID
 * @param {String} channel 分享渠道
 * @returns {Promise<Object>} 记录结果
 */
const recordShareWithoutPoints = async (type, contentId, channel) => {
  try {
    const token = getToken();
    if (!token) {
      return { success: false };
    }
    
    const result = await request({
      url: '/api/share/record',
      method: 'POST',
      data: {
        contentType: type,
        contentId,
        channel,
        noPoints: true // 标记不获取积分
      },
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    return { success: result && result.success };
  } catch (error) {
    console.error('记录分享行为失败:', error);
    return { success: false };
  }
};

/**
 * 检查今日分享积分是否达到上限
 * @returns {Promise<Object>} 检查结果
 */
export const checkShareLimit = async () => {
  try {
    const token = getToken();
    if (!token) {
      return { reached: false, current: 0, limit: 0 };
    }
    
    const result = await request({
      url: '/api/points/share/limit',
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      return {
        reached: result.data.reached || false,
        current: result.data.current || 0,
        limit: result.data.limit || 0
      };
    }
    
    return { reached: false, current: 0, limit: 0 };
  } catch (error) {
    console.error('检查分享积分上限失败:', error);
    return { reached: false, current: 0, limit: 0 };
  }
};

/**
 * 使用积分兑换商品
 * @param {String|Number} productId 商品ID
 * @param {Number} points 所需积分
 * @returns {Promise<Object>} 兑换结果
 */
export const redeemProduct = async (productId, points) => {
  try {
    const token = getToken();
    if (!token) {
      showToast('请先登录');
      return { success: false, error: 'NOT_LOGGED_IN' };
    }
    
    // 检查积分余额
    const balance = await getPointsBalance();
    if (balance < points) {
      showToast('积分不足');
      return { success: false, error: 'INSUFFICIENT_POINTS' };
    }
    
    // 调用兑换API
    const result = await request({
      url: '/api/points/redeem',
      method: 'POST',
      data: {
        productId,
        points
      },
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      showToast('兑换成功');
      return {
        success: true,
        orderId: result.data.orderId,
        remainingPoints: result.data.remainingPoints
      };
    }
    
    showToast(result.message || '兑换失败');
    return { success: false, error: 'API_ERROR' };
  } catch (error) {
    console.error('兑换商品失败:', error);
    showToast('兑换失败，请重试');
    return { success: false, error };
  }
};

/**
 * 更新积分规则
 * @param {Array} rules 积分规则列表
 * @returns {Promise<Object>} 更新结果
 */
export const updatePointsRules = async (rules) => {
  try {
    const token = getToken();
    if (!token) {
      showToast('请先登录');
      return { success: false, error: 'NOT_LOGGED_IN' };
    }
    
    // 调用更新API
    const result = await request({
      url: '/api/admin/points/rules',
      method: 'PUT',
      data: {
        rules
      },
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      showToast('规则更新成功');
      return { success: true };
    }
    
    showToast(result.message || '更新失败');
    return { success: false, error: 'API_ERROR' };
  } catch (error) {
    console.error('更新积分规则失败:', error);
    showToast('更新失败，请重试');
    return { success: false, error };
  }
}; 