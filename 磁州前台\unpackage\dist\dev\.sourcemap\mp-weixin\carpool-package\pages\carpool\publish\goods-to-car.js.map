{"version": 3, "file": "goods-to-car.js", "sources": ["carpool-package/pages/carpool/publish/goods-to-car.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xccHVibGlzaFxnb29kcy10by1jYXIudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"publish-container\">\r\n    <!-- 导航栏 -->\r\n    <carpool-nav title=\"发布货找车信息\"></carpool-nav>\r\n    \r\n    <view class=\"form-container\">\r\n      <view class=\"form-group\">\r\n        <view class=\"form-title\">货物信息</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">货物名称</text>\r\n          <input type=\"text\" v-model=\"formData.goodsName\" placeholder=\"请输入货物名称\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">货物类型</text>\r\n          <picker mode=\"selector\" :range=\"goodsTypes\" :value=\"goodsTypeIndex\" @change=\"onGoodsTypeChange\">\r\n            <view class=\"picker-value\">\r\n              <text>{{formData.goodsType || '请选择货物类型'}}</text>\r\n              <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n          </picker>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">货物重量</text>\r\n          <view class=\"weight-input\">\r\n            <input type=\"digit\" v-model=\"formData.weight\" placeholder=\"请输入货物重量\" />\r\n            <text class=\"unit\">公斤</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">货物体积</text>\r\n          <view class=\"volume-input\">\r\n            <input type=\"digit\" v-model=\"formData.volume\" placeholder=\"请输入货物体积\" />\r\n            <text class=\"unit\">立方米</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"form-group\">\r\n        <view class=\"form-title\">运输信息</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">出发地</text>\r\n          <view class=\"input-wrapper\">\r\n            <input type=\"text\" v-model=\"formData.startPoint\" placeholder=\"请输入出发地\" />\r\n            <view class=\"location-btn\" @click=\"chooseLocation('start')\">\r\n              <image src=\"/static/images/tabbar/location.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">目的地</text>\r\n          <view class=\"input-wrapper\">\r\n            <input type=\"text\" v-model=\"formData.endPoint\" placeholder=\"请输入目的地\" />\r\n            <view class=\"location-btn\" @click=\"chooseLocation('end')\">\r\n              <image src=\"/static/images/tabbar/location.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">途径地点</text>\r\n          <view class=\"via-points-container\">\r\n            <view v-for=\"(point, index) in formData.viaPoints\" :key=\"index\" class=\"via-point-item\">\r\n              <view class=\"input-wrapper\">\r\n                <input type=\"text\" v-model=\"formData.viaPoints[index]\" placeholder=\"请输入途径地点\" />\r\n                <view class=\"location-btn\" @click=\"chooseLocation('via', index)\">\r\n                  <image src=\"/static/images/tabbar/location.png\" mode=\"aspectFit\"></image>\r\n                </view>\r\n              </view>\r\n              <view class=\"via-point-actions\">\r\n                <view class=\"via-point-delete\" @click=\"removeViaPoint(index)\">\r\n                  <text class=\"delete-icon\">×</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"add-via-point\" @click=\"addViaPoint\" v-if=\"formData.viaPoints.length < 3\">\r\n              <text class=\"add-icon\">+</text>\r\n              <text class=\"add-text\">添加途径地点</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">期望发货日期</text>\r\n          <picker mode=\"date\" :value=\"formData.departureDate\" start=\"2023-01-01\" end=\"2030-12-31\" @change=\"onDateChange\">\r\n            <view class=\"picker-value\">\r\n              <text>{{formData.departureDate || '请选择发货日期'}}</text>\r\n              <image src=\"/static/images/tabbar/arrow-right.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n          </picker>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">期望运费</text>\r\n          <view class=\"price-input\">\r\n            <input type=\"digit\" v-model=\"formData.price\" placeholder=\"请输入期望运费（选填）\" />\r\n            <text class=\"unit\">元</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"form-group\">\r\n        <view class=\"form-title\">联系方式</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">联系人</text>\r\n          <input type=\"text\" v-model=\"formData.contactName\" placeholder=\"请输入联系人姓名（选填）\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">手机号码</text>\r\n          <input type=\"number\" v-model=\"formData.contactPhone\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"form-group\">\r\n        <view class=\"form-title\">补充说明</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <textarea v-model=\"formData.remark\" placeholder=\"请输入补充说明（选填）\" maxlength=\"200\" />\r\n          <view class=\"word-count\">{{formData.remark.length}}/200</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"submit-section\">\r\n      <view class=\"agreement\">\r\n        <checkbox-group @change=\"onAgreementChange\">\r\n          <label>\r\n            <checkbox :checked=\"formData.agreement\" color=\"#0A84FF\" />\r\n            <text>我已阅读并同意</text>\r\n            <text class=\"link\" @click.stop=\"viewAgreement\">《拼车服务协议》</text>\r\n          </label>\r\n        </checkbox-group>\r\n      </view>\r\n      \r\n      <!-- 发布方式选择 -->\r\n      <view class=\"publish-options\">\r\n        <view class=\"options-title\">选择发布方式</view>\r\n        \r\n        <!-- 广告发布选项 -->\r\n        <view class=\"option-card\" :class=\"{'option-selected': publishMode === 'ad'}\" @click=\"selectPublishMode('ad')\">\r\n          <view class=\"option-icon ad-icon\">\r\n            <image src=\"/static/images/tabbar/ad-play.png\" mode=\"aspectFit\"></image>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <view class=\"option-name\">\r\n              <text>免费发布</text>\r\n              <view class=\"option-tag recommend\">推荐</view>\r\n            </view>\r\n            <view class=\"option-desc\">观看15秒广告免费发布</view>\r\n          </view>\r\n          <view class=\"option-checkbox\">\r\n            <view class=\"checkbox-inner\" v-if=\"publishMode === 'ad'\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 付费发布选项 -->\r\n        <view class=\"option-card\" :class=\"{'option-selected': publishMode === 'premium'}\" @click=\"selectPublishMode('premium')\">\r\n          <view class=\"option-icon premium-icon\">\r\n            <image src=\"/static/images/tabbar/vip-crown.png\" mode=\"aspectFit\"></image>\r\n          </view>\r\n          <view class=\"option-content\">\r\n            <view class=\"option-name\">\r\n              <text>置顶发布</text>\r\n              <view class=\"option-tag premium\">高级</view>\r\n            </view>\r\n            <view class=\"option-desc\">支付5元获得置顶特权</view>\r\n          </view>\r\n          <view class=\"option-checkbox\">\r\n            <view class=\"checkbox-inner\" v-if=\"publishMode === 'premium'\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <button class=\"submit-btn\" :disabled=\"!formData.agreement\" @click=\"submitForm\">\r\n        发布信息\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport CarpoolNav from '/components/carpool-nav.vue';\r\nimport { ref, onMounted } from 'vue';\r\n\r\n// 状态栏高度\r\nconst statusBarHeight = ref(20);\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n  goodsName: '',\r\n  goodsType: '',\r\n  weight: '',\r\n  volume: '',\r\n  startPoint: '',\r\n  endPoint: '',\r\n  departureDate: '',\r\n  price: '',\r\n  contactName: '',\r\n  contactPhone: '',\r\n  remark: '',\r\n  agreement: false,\r\n  viaPoints: []\r\n});\r\n\r\n// 货物类型选项\r\nconst goodsTypes = ref(['普通货物', '易碎品', '液体', '食品', '电子产品', '家具', '其他']);\r\nconst goodsTypeIndex = ref(0);\r\n\r\n// 发布模式\r\nconst publishMode = ref('ad');\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  setStatusBarHeight();\r\n});\r\n\r\n// 设置状态栏高度\r\nconst setStatusBarHeight = () => {\r\n  const systemInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = systemInfo.statusBarHeight;\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 选择位置\r\nconst chooseLocation = (type, index) => {\r\n  uni.chooseLocation({\r\n    success: (res) => {\r\n      if (type === 'start') {\r\n        formData.value.startPoint = res.name;\r\n      } else if (type === 'end') {\r\n        formData.value.endPoint = res.name;\r\n      } else if (type === 'via') {\r\n        if (index !== undefined && index !== null) {\r\n          formData.value.viaPoints[index] = res.name;\r\n        } else {\r\n          formData.value.viaPoints.push(res.name);\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 日期选择\r\nconst onDateChange = (e) => {\r\n  formData.value.departureDate = e.detail.value;\r\n};\r\n\r\n// 货物类型选择\r\nconst onGoodsTypeChange = (e) => {\r\n  goodsTypeIndex.value = e.detail.value;\r\n  formData.value.goodsType = goodsTypes.value[goodsTypeIndex.value];\r\n};\r\n\r\n// 协议同意状态变更\r\nconst onAgreementChange = (e) => {\r\n  formData.value.agreement = e.detail.value.length > 0;\r\n};\r\n\r\n// 查看协议\r\nconst viewAgreement = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/carpool/agreement'\r\n  });\r\n};\r\n\r\n// 提交表单\r\nconst submitForm = () => {\r\n  // 表单验证\r\n  if (!formData.value.goodsName) {\r\n    showToast('请输入货物名称');\r\n    return;\r\n  }\r\n  if (!formData.value.goodsType) {\r\n    showToast('请选择货物类型');\r\n    return;\r\n  }\r\n  if (!formData.value.weight) {\r\n    showToast('请输入货物重量');\r\n    return;\r\n  }\r\n  if (!formData.value.volume) {\r\n    showToast('请输入货物体积');\r\n    return;\r\n  }\r\n  if (!formData.value.startPoint) {\r\n    showToast('请输入出发地');\r\n    return;\r\n  }\r\n  if (!formData.value.endPoint) {\r\n    showToast('请输入目的地');\r\n    return;\r\n  }\r\n  if (!formData.value.departureDate) {\r\n    showToast('请选择发货日期');\r\n    return;\r\n  }\r\n  // 期望运费为选填，移除验证\r\n  // 联系人姓名为选填，移除验证\r\n  if (!formData.value.contactPhone) {\r\n    showToast('请输入手机号码');\r\n    return;\r\n  }\r\n  if (!/^1\\d{10}$/.test(formData.value.contactPhone)) {\r\n    showToast('手机号码格式不正确');\r\n    return;\r\n  }\r\n  \r\n  // 根据选择的模式进行处理\r\n  if (publishMode.value === 'ad') {\r\n    handleAdPublish();\r\n  } else if (publishMode.value === 'premium') {\r\n    handlePremiumPublish();\r\n  }\r\n};\r\n\r\n// 处理广告发布\r\nconst handleAdPublish = () => {\r\n  // 显示广告\r\n  uni.showLoading({\r\n    title: '正在加载广告...'\r\n  });\r\n  \r\n  // 模拟广告加载\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 模拟广告播放完成\r\n    uni.showModal({\r\n      title: '广告播放完成',\r\n      content: '感谢您观看广告，现在可以免费发布拼车信息',\r\n      showCancel: false,\r\n      success: () => {\r\n        submitToServer();\r\n      }\r\n    });\r\n  }, 1500);\r\n};\r\n\r\n// 处理付费发布\r\nconst handlePremiumPublish = () => {\r\n  uni.showModal({\r\n    title: '付费发布',\r\n    content: '您将支付5元获得置顶发布特权，是否继续？',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        // 模拟支付过程\r\n        uni.showLoading({\r\n          title: '正在支付...'\r\n        });\r\n        \r\n        setTimeout(() => {\r\n          uni.hideLoading();\r\n          uni.showToast({\r\n            title: '支付成功',\r\n            icon: 'success'\r\n          });\r\n          \r\n          // 提交到服务器\r\n          submitToServer();\r\n        }, 1500);\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 提交到服务器\r\nconst submitToServer = () => {\r\n  // 提交表单\r\n  uni.showLoading({\r\n    title: '提交中...'\r\n  });\r\n  \r\n  // 整理表单数据，包括途径地点\r\n  const formDataToSubmit = {\r\n    ...formData.value,\r\n    // 过滤掉空的途径地点\r\n    viaPoints: formData.value.viaPoints.filter(point => point.trim() !== '')\r\n  };\r\n  \r\n  console.log('提交的表单数据：', formDataToSubmit);\r\n  \r\n  // 模拟提交\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 生成一个模拟的发布ID\r\n    const publishId = Date.now().toString();\r\n    \r\n    // 跳转到成功页面\r\n    uni.navigateTo({\r\n      url: `/carpool-package/pages/carpool/publish/success?id=${publishId}&type=goods-to-car&mode=${publishMode.value}`\r\n    });\r\n  }, 1000);\r\n};\r\n\r\n// 显示提示\r\nconst showToast = (title) => {\r\n  uni.showToast({\r\n    title,\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 选择发布方式\r\nconst selectPublishMode = (mode) => {\r\n  publishMode.value = mode;\r\n};\r\n\r\n// 移除途径地点\r\nconst removeViaPoint = (index) => {\r\n  formData.value.viaPoints.splice(index, 1);\r\n};\r\n\r\n// 添加途径地点\r\nconst addViaPoint = () => {\r\n  formData.value.viaPoints.push('');\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.publish-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 40rpx;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 120rpx;\r\n  padding: 0 30rpx;\r\n  background-color: #0A84FF;\r\n  position: relative;\r\n}\r\n\r\n.navbar-left {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10;\r\n}\r\n\r\n.navbar-title {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 表单容器 */\r\n.form-container {\r\n  padding: 30rpx;\r\n}\r\n\r\n.form-group {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.form-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 20rpx;\r\n  padding-left: 20rpx;\r\n  border-left: 8rpx solid #0A84FF;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.label {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\ninput, .picker-value {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  background-color: #f8f8f8;\r\n  border-radius: 10rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.picker-value {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.picker-value image {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.input-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.location-btn {\r\n  position: absolute;\r\n  right: 20rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.location-btn image {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.weight-input, .volume-input, .price-input {\r\n  position: relative;\r\n}\r\n\r\n.unit {\r\n  position: absolute;\r\n  right: 20rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n}\r\n\r\ntextarea {\r\n  width: 100%;\r\n  height: 200rpx;\r\n  background-color: #f8f8f8;\r\n  border-radius: 10rpx;\r\n  padding: 20rpx;\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.word-count {\r\n  text-align: right;\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n/* 提交区域 */\r\n.submit-section {\r\n  padding: 0 30rpx;\r\n  margin-top: 40rpx;\r\n}\r\n\r\n.agreement {\r\n  margin-bottom: 30rpx;\r\n  font-size: 26rpx;\r\n  color: #666666;\r\n}\r\n\r\n.link {\r\n  color: #0A84FF;\r\n}\r\n\r\n/* 发布方式选择 */\r\n.publish-options {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.options-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.option-card {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.option-card:active {\r\n  transform: translateY(-2rpx);\r\n  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.option-selected {\r\n  border: 2rpx solid #0A84FF;\r\n  background-color: rgba(10, 132, 255, 0.05);\r\n}\r\n\r\n.option-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.option-icon image {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  filter: brightness(0) invert(1);\r\n}\r\n\r\n.ad-icon {\r\n  background: linear-gradient(135deg, #2c96ff, #5f65ff);\r\n}\r\n\r\n.premium-icon {\r\n  background: linear-gradient(135deg, #ff9500, #ff2d55);\r\n}\r\n\r\n.option-content {\r\n  flex: 1;\r\n}\r\n\r\n.option-name {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 6rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.option-tag {\r\n  font-size: 20rpx;\r\n  padding: 2rpx 10rpx;\r\n  border-radius: 10rpx;\r\n  margin-left: 10rpx;\r\n  color: #ffffff;\r\n}\r\n\r\n.recommend {\r\n  background-color: #1677FF;\r\n}\r\n\r\n.premium {\r\n  background-color: #ff9500;\r\n}\r\n\r\n.option-desc {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n}\r\n\r\n.option-checkbox {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border-radius: 50%;\r\n  border: 2rpx solid #dddddd;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 20rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.option-selected .option-checkbox {\r\n  border-color: #0A84FF;\r\n}\r\n\r\n.checkbox-inner {\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n  background-color: #0A84FF;\r\n  border-radius: 50%;\r\n}\r\n\r\n.submit-btn {\r\n  height: 90rpx;\r\n  border-radius: 45rpx;\r\n  background-image: linear-gradient(135deg, #0A84FF, #0055B8);\r\n  color: #ffffff;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  box-shadow: 0 8rpx 16rpx rgba(10, 132, 255, 0.3);\r\n}\r\n\r\n.submit-btn[disabled] {\r\n  background-image: linear-gradient(135deg, #cccccc, #999999);\r\n  box-shadow: none;\r\n  color: #ffffff;\r\n}\r\n\r\n.via-points-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.via-point-item {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.via-point-item:active {\r\n  transform: translateY(-2rpx);\r\n  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.via-point-delete {\r\n  position: absolute;\r\n  right: 20rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.via-point-delete text {\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n}\r\n\r\n.add-via-point {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.add-via-point:active {\r\n  transform: translateY(-2rpx);\r\n  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.add-icon {\r\n  font-size: 32rpx;\r\n  color: #0A84FF;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.add-text {\r\n  font-size: 28rpx;\r\n  color: #0A84FF;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/publish/goods-to-car.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;AA6LA,MAAM,aAAa,MAAW;;;;AAI9B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,eAAe;AAAA,MACf,OAAO;AAAA,MACP,aAAa;AAAA,MACb,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,WAAW,CAAE;AAAA,IACf,CAAC;AAGD,UAAM,aAAaA,cAAG,IAAC,CAAC,QAAQ,OAAO,MAAM,MAAM,QAAQ,MAAM,IAAI,CAAC;AACtE,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAG5B,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAG5BC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,UAAM,qBAAqB,MAAM;AAC/B,YAAM,aAAaC,oBAAI;AACvB,sBAAgB,QAAQ,WAAW;AAAA,IACrC;AAQA,UAAM,iBAAiB,CAAC,MAAM,UAAU;AACtCA,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,CAAC,QAAQ;AAChB,cAAI,SAAS,SAAS;AACpB,qBAAS,MAAM,aAAa,IAAI;AAAA,UACxC,WAAiB,SAAS,OAAO;AACzB,qBAAS,MAAM,WAAW,IAAI;AAAA,UACtC,WAAiB,SAAS,OAAO;AACzB,gBAAI,UAAU,UAAa,UAAU,MAAM;AACzC,uBAAS,MAAM,UAAU,KAAK,IAAI,IAAI;AAAA,YAChD,OAAe;AACL,uBAAS,MAAM,UAAU,KAAK,IAAI,IAAI;AAAA,YACvC;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,MAAM,gBAAgB,EAAE,OAAO;AAAA,IAC1C;AAGA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,qBAAe,QAAQ,EAAE,OAAO;AAChC,eAAS,MAAM,YAAY,WAAW,MAAM,eAAe,KAAK;AAAA,IAClE;AAGA,UAAM,oBAAoB,CAAC,MAAM;AAC/B,eAAS,MAAM,YAAY,EAAE,OAAO,MAAM,SAAS;AAAA,IACrD;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AAEvB,UAAI,CAAC,SAAS,MAAM,WAAW;AAC7B,kBAAU,SAAS;AACnB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,WAAW;AAC7B,kBAAU,SAAS;AACnB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,QAAQ;AAC1B,kBAAU,SAAS;AACnB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,QAAQ;AAC1B,kBAAU,SAAS;AACnB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,YAAY;AAC9B,kBAAU,QAAQ;AAClB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,UAAU;AAC5B,kBAAU,QAAQ;AAClB;AAAA,MACD;AACD,UAAI,CAAC,SAAS,MAAM,eAAe;AACjC,kBAAU,SAAS;AACnB;AAAA,MACD;AAGD,UAAI,CAAC,SAAS,MAAM,cAAc;AAChC,kBAAU,SAAS;AACnB;AAAA,MACD;AACD,UAAI,CAAC,YAAY,KAAK,SAAS,MAAM,YAAY,GAAG;AAClD,kBAAU,WAAW;AACrB;AAAA,MACD;AAGD,UAAI,YAAY,UAAU,MAAM;AAC9B;MACJ,WAAa,YAAY,UAAU,WAAW;AAC1C;MACD;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAE5BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS,MAAM;AACb;UACD;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,uBAAuB,MAAM;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,YACjB,CAAS;AAED,uBAAW,MAAM;AACfA,4BAAG,MAAC,YAAW;AACfA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAGD;YACD,GAAE,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAE3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,YAAM,mBAAmB;AAAA,QACvB,GAAG,SAAS;AAAA;AAAA,QAEZ,WAAW,SAAS,MAAM,UAAU,OAAO,WAAS,MAAM,KAAM,MAAK,EAAE;AAAA,MAC3E;AAEEA,oBAAA,MAAA,MAAA,OAAA,iEAAY,YAAY,gBAAgB;AAGxC,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,cAAM,YAAY,KAAK,IAAK,EAAC,SAAQ;AAGrCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,qDAAqD,SAAS,2BAA2B,YAAY,KAAK;AAAA,QACrH,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ;AAAA,QACA,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAClC,kBAAY,QAAQ;AAAA,IACtB;AAGA,UAAM,iBAAiB,CAAC,UAAU;AAChC,eAAS,MAAM,UAAU,OAAO,OAAO,CAAC;AAAA,IAC1C;AAGA,UAAM,cAAc,MAAM;AACxB,eAAS,MAAM,UAAU,KAAK,EAAE;AAAA,IAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3aA,GAAG,WAAWC,SAAe;"}