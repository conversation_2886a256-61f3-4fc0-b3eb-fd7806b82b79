import CustomNavbar from './CustomNavbar.vue';
import SectionHeader from './SectionHeader.vue';
import FormGroup from './FormGroup.vue';
import IconButton from './IconButton.vue';
import SvgIcon from './SvgIcon.vue';
import ProgressSteps from './ProgressSteps.vue';

// 统一注册组件
const components = {
  CustomNavbar,
  SectionHeader,
  FormGroup,
  IconButton,
  SvgIcon,
  ProgressSteps
};

// 批量注册组件的插件
const MerchantUI = {
  install(Vue) {
    Object.keys(components).forEach(key => {
      Vue.component(key, components[key]);
    });
  }
};

// 导出单个组件
export {
  CustomNavbar,
  SectionHeader,
  FormGroup,
  IconButton,
  SvgIcon,
  ProgressSteps
};

// 导出整个组件库
export default MerchantUI; 