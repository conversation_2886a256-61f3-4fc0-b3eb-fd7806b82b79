"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedType: "food",
      paymentType: "offline",
      // offline: 到店支付, online: 在线支付
      verifyType: "offline",
      // offline: 到店核销, online: 在线核销
      verifyCodeValidDays: 30,
      verifyTimes: 1
    };
  },
  onLoad() {
    try {
      const savedType = common_vendor.index.getStorageSync("packageType");
      const savedPaymentInfo = common_vendor.index.getStorageSync("packagePaymentInfo");
      if (savedType) {
        this.selectedType = savedType;
      }
      if (savedPaymentInfo) {
        const paymentInfo = JSON.parse(savedPaymentInfo);
        this.paymentType = paymentInfo.paymentType;
        this.verifyType = paymentInfo.verifyType;
        this.verifyCodeValidDays = paymentInfo.verifyCodeValidDays;
        this.verifyTimes = paymentInfo.verifyTimes;
      }
    } catch (e) {
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-type.vue:152", "读取本地存储失败:", e);
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    selectType(type) {
      this.selectedType = type;
    },
    setPaymentType(type) {
      this.paymentType = type;
    },
    setVerifyType(type) {
      this.verifyType = type;
    },
    nextStep() {
      try {
        common_vendor.index.setStorageSync("packageType", this.selectedType);
        const paymentInfo = {
          paymentType: this.paymentType,
          verifyType: this.verifyType,
          verifyCodeValidDays: this.verifyCodeValidDays,
          verifyTimes: this.verifyTimes
        };
        common_vendor.index.setStorageSync("packagePaymentInfo", JSON.stringify(paymentInfo));
      } catch (e) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-type.vue:182", "保存数据失败:", e);
      }
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info"
      });
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "帮助信息",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: $data.selectedType === "food"
  }, $data.selectedType === "food" ? {} : {}, {
    d: $data.selectedType === "food" ? 1 : "",
    e: common_vendor.o(($event) => $options.selectType("food")),
    f: $data.selectedType === "service"
  }, $data.selectedType === "service" ? {} : {}, {
    g: $data.selectedType === "service" ? 1 : "",
    h: common_vendor.o(($event) => $options.selectType("service")),
    i: $data.selectedType === "product"
  }, $data.selectedType === "product" ? {} : {}, {
    j: $data.selectedType === "product" ? 1 : "",
    k: common_vendor.o(($event) => $options.selectType("product")),
    l: $data.selectedType === "custom"
  }, $data.selectedType === "custom" ? {} : {}, {
    m: $data.selectedType === "custom" ? 1 : "",
    n: common_vendor.o(($event) => $options.selectType("custom")),
    o: $data.paymentType === "offline"
  }, $data.paymentType === "offline" ? {} : {}, {
    p: $data.paymentType === "offline" ? 1 : "",
    q: common_vendor.o(($event) => $options.setPaymentType("offline")),
    r: $data.paymentType === "online"
  }, $data.paymentType === "online" ? {} : {}, {
    s: $data.paymentType === "online" ? 1 : "",
    t: common_vendor.o(($event) => $options.setPaymentType("online")),
    v: $data.verifyType === "offline"
  }, $data.verifyType === "offline" ? {} : {}, {
    w: $data.verifyType === "offline" ? 1 : "",
    x: common_vendor.o(($event) => $options.setVerifyType("offline")),
    y: $data.verifyType === "online"
  }, $data.verifyType === "online" ? {} : {}, {
    z: $data.verifyType === "online" ? 1 : "",
    A: common_vendor.o(($event) => $options.setVerifyType("online")),
    B: $data.verifyType === "offline"
  }, $data.verifyType === "offline" ? {
    C: $data.verifyCodeValidDays,
    D: common_vendor.o(($event) => $data.verifyCodeValidDays = $event.detail.value)
  } : {}, {
    E: $data.verifyType === "offline"
  }, $data.verifyType === "offline" ? {
    F: $data.verifyTimes,
    G: common_vendor.o(($event) => $data.verifyTimes = $event.detail.value)
  } : {}, {
    H: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    I: common_vendor.o((...args) => $options.nextStep && $options.nextStep(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-type.js.map
