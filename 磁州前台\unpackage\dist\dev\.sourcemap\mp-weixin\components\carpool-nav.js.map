{"version": 3, "file": "carpool-nav.js", "sources": ["components/carpool-nav.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9jYXJwb29sLW5hdi52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"custom-navbar\" :style=\"{paddingTop: statusBarHeight + 'px'}\">\r\n    <view class=\"navbar-left\" @click=\"goBack\">\r\n      <image src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\" style=\"width: 48rpx; height: 48rpx; filter: brightness(0) invert(1);\"></image>\r\n    </view>\r\n    <view class=\"navbar-title\">{{ title }}</view>\r\n    <view class=\"navbar-right\">\r\n      <slot name=\"right\"></slot>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: '拼车'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20\r\n    }\r\n  },\r\n  created() {\r\n    this.setStatusBarHeight();\r\n  },\r\n  methods: {\r\n    // 设置状态栏高度\r\n    setStatusBarHeight() {\r\n      const systemInfo = uni.getSystemInfoSync();\r\n      this.statusBarHeight = systemInfo.statusBarHeight;\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack();\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 自定义导航栏样式 */\r\n.custom-navbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 120rpx;\r\n  padding: 0 30rpx;\r\n  background-color: #0A84FF;\r\n  position: relative;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 100;\r\n  border-bottom-left-radius: 40rpx;\r\n  border-bottom-right-radius: 40rpx;\r\n  backdrop-filter: blur(10px);\r\n  -webkit-backdrop-filter: blur(10px);\r\n}\r\n\r\n.navbar-left {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10;\r\n}\r\n\r\n.navbar-title {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.navbar-right {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/carpool-nav.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;AAaA,MAAK,YAAU;AAAA,EACb,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,EACD;AAAA,EACD,UAAU;AACR,SAAK,mBAAkB;AAAA,EACxB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,qBAAqB;AACnB,YAAM,aAAaA,oBAAI;AACvB,WAAK,kBAAkB,WAAW;AAAA,IACnC;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAAA,EACF;AACF;;;;;;;;;;ACvCA,GAAG,gBAAgB,SAAS;"}