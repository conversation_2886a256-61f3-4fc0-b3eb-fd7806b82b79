{"version": 3, "file": "ConfigurablePremiumActions.js", "sources": ["components/premium/ConfigurablePremiumActions.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9wcmVtaXVtL0NvbmZpZ3VyYWJsZVByZW1pdW1BY3Rpb25zLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"premium-actions-container\">\r\n    <!-- 直接显示模式 -->\r\n    <view class=\"direct-options\" v-if=\"showMode === 'direct'\">\r\n      <!-- 发布页面显示的选项 -->\r\n      <template v-if=\"pageType === 'publish'\">\r\n        <!-- 广告发布选项 -->\r\n        <view class=\"premium-option-card ad-option\" @click=\"selectDirectOption('publish', 'ad')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/ad-publish.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title\">看广告发布</text>\r\n                <text class=\"option-desc\" v-if=\"isCarpool\">看一个广告发布一条信息</text>\r\n                <text class=\"option-desc\" v-else>看一个广告免费发布一天</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag free-tag\">\r\n                <text>免费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 付费发布选项 -->\r\n        <view class=\"premium-option-card paid-option\" @click=\"selectDirectOption('publish', 'paid')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container paid-icon\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/paid-publish.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title\">付费发布</text>\r\n                <text class=\"option-desc\" v-if=\"isCarpool\">付费1元发布一条信息</text>\r\n                <text class=\"option-desc\" v-else>3天/1周/1个月任选</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag paid-tag\">\r\n                <text>付费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </template>\r\n\r\n      <!-- 置顶页面显示的选项 -->\r\n      <template v-else-if=\"pageType === 'top' || pageType === 'merchant_top' || pageType === 'carpool_top' || pageType === 'publish_top' || pageType.endsWith('_top')\">\r\n        <!-- 广告置顶选项 -->\r\n        <view class=\"premium-option-card ad-option\" @click=\"selectDirectOption('top', 'ad')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/ad-top.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title bold-title\">看广告置顶</text>\r\n                <text class=\"option-desc\" v-if=\"pageType.startsWith('merchant')\">免费置顶店铺2小时，排名次于付费</text>\r\n                <text class=\"option-desc\" v-else>免费置顶2小时，排名次于付费</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag free-tag\">\r\n                <text>免费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 付费置顶选项 -->\r\n        <view class=\"premium-option-card paid-option\" @click=\"selectDirectOption('top', 'paid')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container paid-icon\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/paid-top.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title bold-title\">付费置顶</text>\r\n                <text class=\"option-desc\" v-if=\"pageType.startsWith('merchant')\">获得店铺优先展示位置，排名最靠前</text>\r\n                <text class=\"option-desc\" v-else>获得优先展示位置，排名最靠前</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag paid-tag\">\r\n                <text>付费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </template>\r\n\r\n      <!-- 刷新页面显示的选项 -->\r\n      <template v-else-if=\"pageType === 'refresh' || pageType === 'merchant_refresh' || pageType === 'carpool_refresh' || pageType === 'publish_refresh' || pageType.endsWith('_refresh')\">\r\n        <!-- 广告刷新选项 -->\r\n        <view class=\"premium-option-card ad-option\" @click=\"selectDirectOption('refresh', 'ad')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/ad-refresh.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title\">看广告刷新</text>\r\n                <text class=\"option-desc\" v-if=\"pageType.startsWith('merchant')\">刷新店铺信息，提升排名到最新位置</text>\r\n                <text class=\"option-desc\" v-else>刷新后置顶信息排名立刻升到置顶第一位！未置顶的会升到未置顶第一位！</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag free-tag\">\r\n                <text>免费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 付费刷新选项 -->\r\n        <view class=\"premium-option-card paid-option\" @click=\"selectDirectOption('refresh', 'paid')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container paid-icon\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/paid-refresh.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title\">付费刷新</text>\r\n                <text class=\"option-desc\" v-if=\"pageType.startsWith('merchant')\">付费刷新店铺信息，提升排名</text>\r\n                <text class=\"option-desc\" v-else>刷新后置顶信息排名立刻升到置顶第一位！未置顶的会升到未置顶第一位！</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag paid-tag\">\r\n                <text>付费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </template>\r\n\r\n      <!-- 商家入驻页面显示的选项 -->\r\n      <template v-else-if=\"pageType === 'merchant_join'\">\r\n        <!-- 看广告入驻选项 -->\r\n        <view class=\"premium-option-card ad-option\" @click=\"selectDirectOption('join', 'ad')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/ad-join.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title bold-title\">看广告免费入驻</text>\r\n                <text class=\"option-desc\">观看广告获得1个月免费特权</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag free-tag\">\r\n                <text>免费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 付费入驻选项 -->\r\n        <view class=\"premium-option-card paid-option\" @click=\"selectDirectOption('join', 'paid')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container paid-icon\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/paid-join.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title bold-title\">付费入驻</text>\r\n                <text class=\"option-desc\">解锁全部功能，提升曝光量</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag paid-tag\">\r\n                <text>付费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </template>\r\n\r\n      <!-- 商家续费页面显示的选项 -->\r\n      <template v-else-if=\"pageType === 'merchant_renew'\">\r\n        <!-- 看广告续费选项 -->\r\n        <view class=\"premium-option-card ad-option\" @click=\"selectDirectOption('renew', 'ad')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/ad-renew.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title bold-title\">看广告续费</text>\r\n                <text class=\"option-desc\">观看广告延长7天会员</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag free-tag\">\r\n                <text>免费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 付费续费选项 -->\r\n        <view class=\"premium-option-card paid-option\" @click=\"selectDirectOption('renew', 'paid')\">\r\n          <view class=\"option-inner\">\r\n            <view class=\"option-left\">\r\n              <view class=\"option-icon-container paid-icon\">\r\n                <image class=\"option-icon\" src=\"/static/images/premium/paid-renew.png\" mode=\"aspectFit\"></image>\r\n              </view>\r\n              <view class=\"option-content\">\r\n                <text class=\"option-title bold-title\">付费续费</text>\r\n                <text class=\"option-desc\">选择续费时长，享受会员特权</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"option-right\">\r\n              <view class=\"option-tag paid-tag\">\r\n                <text>付费</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </template>\r\n    </view>\r\n\r\n    <!-- 付费选项模态框 -->\r\n    <view class=\"payment-modal\" v-if=\"showPaymentModal\" @click=\"closePaymentModal\">\r\n      <view class=\"payment-content\" @click.stop>\r\n        <view class=\"payment-header\">\r\n          <text class=\"payment-title\">{{ paymentModalConfig.title }}</text>\r\n          <view class=\"close-btn\" @click=\"closePaymentModal\">\r\n            <text>×</text>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"payment-body\">\r\n          <text class=\"payment-desc\">{{ paymentModalConfig.description }}</text>\r\n\r\n          <view class=\"duration-options\">\r\n            <view\r\n              class=\"duration-item\"\r\n              :class=\"{ active: selectedDuration === option.duration }\"\r\n              v-for=\"option in paymentModalConfig.options\"\r\n              :key=\"option.duration\"\r\n              @click=\"selectDuration(option)\"\r\n            >\r\n              <view class=\"duration-info\">\r\n                <text class=\"duration-text\">{{ option.duration }}</text>\r\n                <text class=\"duration-price\">¥{{ option.price }}</text>\r\n              </view>\r\n              <view class=\"duration-badge\" v-if=\"option.recommended\">\r\n                <text>推荐</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"payment-footer\">\r\n          <view class=\"payment-btn cancel-btn\" @click=\"closePaymentModal\">\r\n            <text>取消</text>\r\n          </view>\r\n          <view class=\"payment-btn confirm-btn\" @click=\"confirmPayment\" :class=\"{ disabled: !selectedDuration }\">\r\n            <text>确认支付</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport {\r\n  getPromotionConfig,\r\n  getButtonConfig,\r\n  getPricingConfig,\r\n  getAdRewardConfig,\r\n  isFeatureEnabled,\r\n  logUserAction\r\n} from '@/utils/promotion-config.js';\r\n\r\n// 组件属性\r\nconst props = defineProps({\r\n  pageType: {\r\n    type: String,\r\n    default: ''\r\n  },\r\n  showMode: {\r\n    type: String,\r\n    default: 'direct'\r\n  },\r\n  itemData: {\r\n    type: Object,\r\n    default: () => ({})\r\n  }\r\n});\r\n\r\n// 组件事件\r\nconst emit = defineEmits(['action-completed', 'action-cancelled']);\r\n\r\n// 配置数据\r\nconst promotionConfig = ref(null);\r\nconst configLoaded = ref(false);\r\n\r\n// 判断是否为拼车系统发布页面\r\nconst isCarpool = computed(() => {\r\n  const pages = getCurrentPages();\r\n  const currentPage = pages[pages.length - 1];\r\n  const route = currentPage ? currentPage.route || '' : '';\r\n  return route.includes('carpool');\r\n});\r\n\r\n// 付费模态框相关状态\r\nconst showPaymentModal = ref(false);\r\nconst selectedDuration = ref('');\r\nconst currentPaymentAction = ref('');\r\nconst paymentModalConfig = ref({\r\n  title: '',\r\n  description: '',\r\n  options: []\r\n});\r\n\r\n// 直接选择选项\r\nconst selectDirectOption = (action, optionType) => {\r\n  console.log('选择操作:', action, optionType);\r\n\r\n  // 创建选项对象\r\n  let option = null;\r\n\r\n  if (action === 'publish') {\r\n    if (optionType === 'ad') {\r\n      if (isCarpool.value) {\r\n        option = {\r\n          title: '免费发布',\r\n          subtitle: '观看15秒广告发布一条信息',\r\n          price: '免费',\r\n          icon: '/static/images/premium/ad-publish.png',\r\n          type: 'ad',\r\n          duration: '一条信息'\r\n        };\r\n      } else {\r\n        option = {\r\n          title: '免费发布',\r\n          subtitle: '观看15秒广告后发布',\r\n          price: '免费',\r\n          icon: '/static/images/premium/ad-publish.png',\r\n          type: 'ad',\r\n          duration: '1天'\r\n        };\r\n      }\r\n    }\r\n  } else if (action === 'top') {\r\n    if (optionType === 'ad') {\r\n      option = {\r\n        title: '广告置顶',\r\n        subtitle: '观看30秒广告获得2小时置顶',\r\n        price: '免费',\r\n        icon: '/static/images/premium/ad-top.png',\r\n        type: 'ad',\r\n        duration: '2小时'\r\n      };\r\n    }\r\n  } else if (action === 'refresh') {\r\n    if (optionType === 'ad') {\r\n      option = {\r\n        title: '广告刷新',\r\n        subtitle: '观看15秒广告刷新一次',\r\n        price: '免费',\r\n        icon: '/static/images/premium/ad-refresh.png',\r\n        type: 'ad'\r\n      };\r\n    }\r\n  } else if (action === 'join') {\r\n    if (optionType === 'ad') {\r\n      option = {\r\n        title: '看广告入驻',\r\n        subtitle: '观看30秒广告获得1个月免费特权',\r\n        price: '免费',\r\n        icon: '/static/images/premium/ad-join.png',\r\n        type: 'ad',\r\n        duration: '1个月'\r\n      };\r\n    }\r\n  } else if (action === 'renew') {\r\n    if (optionType === 'ad') {\r\n      option = {\r\n        title: '看广告续费',\r\n        subtitle: '观看30秒广告延长7天会员',\r\n        price: '免费',\r\n        icon: '/static/images/premium/ad-renew.png',\r\n        type: 'ad',\r\n        duration: '7天'\r\n      };\r\n    }\r\n  }\r\n\r\n  // 处理不同类型的操作\r\n  if (optionType === 'ad') {\r\n    if (option) {\r\n      showAd(option, action);\r\n    }\r\n  } else if (optionType === 'paid') {\r\n    // 付费选项直接显示模态框\r\n    openPaymentModal(action);\r\n  }\r\n};\r\n\r\n// 显示广告\r\nconst showAd = (option, action) => {\r\n  uni.showLoading({\r\n    title: '正在加载广告...'\r\n  });\r\n\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    uni.showToast({\r\n      title: '广告观看完成',\r\n      icon: 'success'\r\n    });\r\n\r\n    // 发送完成事件\r\n    emit('action-completed', {\r\n      action: action,\r\n      type: 'ad',\r\n      data: option\r\n    });\r\n  }, 2000);\r\n};\r\n\r\n// 显示付费模态框\r\nconst openPaymentModal = async (action) => {\r\n  currentPaymentAction.value = action;\r\n  selectedDuration.value = '';\r\n\r\n  try {\r\n    // 从后台获取价格配置\r\n    const pricingOptions = await getPricingConfig(action);\r\n\r\n    // 根据不同操作配置模态框\r\n    let title = '选择时长';\r\n    let description = '选择您需要的时长';\r\n\r\n    if (action === 'publish') {\r\n      title = '选择发布时长';\r\n      description = '选择您希望发布的时长';\r\n    } else if (action === 'top') {\r\n      title = '选择置顶时长';\r\n      description = '选择您希望置顶的时长';\r\n    } else if (action === 'refresh') {\r\n      title = '选择刷新套餐';\r\n      description = '选择您需要的刷新次数';\r\n    } else if (action === 'join') {\r\n      title = '选择入驻时长';\r\n      description = '选择您的商家会员时长';\r\n    } else if (action === 'renew') {\r\n      title = '选择续费时长';\r\n      description = '选择您要续费的时长';\r\n    }\r\n\r\n    paymentModalConfig.value = {\r\n      title,\r\n      description,\r\n      options: pricingOptions || []\r\n    };\r\n\r\n    showPaymentModal.value = true;\r\n\r\n    // 记录用户操作\r\n    logUserAction('open_payment_modal', {\r\n      action,\r\n      pageType: props.pageType,\r\n      itemId: props.itemData?.id\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('获取价格配置失败:', error);\r\n    uni.showToast({\r\n      title: '获取价格信息失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 选择时长\r\nconst selectDuration = (option) => {\r\n  selectedDuration.value = option.duration;\r\n};\r\n\r\n// 关闭付费模态框\r\nconst closePaymentModal = () => {\r\n  showPaymentModal.value = false;\r\n  selectedDuration.value = '';\r\n\r\n  // 发送取消事件\r\n  if (currentPaymentAction.value) {\r\n    emit('action-cancelled', {\r\n      action: currentPaymentAction.value,\r\n      type: 'payment',\r\n      reason: 'user_cancelled'\r\n    });\r\n  }\r\n\r\n  currentPaymentAction.value = '';\r\n};\r\n\r\n// 确认支付\r\nconst confirmPayment = () => {\r\n  if (!selectedDuration.value) {\r\n    uni.showToast({\r\n      title: '请选择时长',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n\r\n  // 找到选中的选项\r\n  const selectedOption = paymentModalConfig.value.options.find(\r\n    option => option.duration === selectedDuration.value\r\n  );\r\n\r\n  if (!selectedOption) return;\r\n\r\n  // 关闭模态框\r\n  showPaymentModal.value = false;\r\n\r\n  // 模拟支付\r\n  uni.showLoading({\r\n    title: '正在支付...'\r\n  });\r\n\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    uni.showToast({\r\n      title: '支付成功',\r\n      icon: 'success'\r\n    });\r\n\r\n    // 发送完成事件\r\n    emit('action-completed', {\r\n      action: currentPaymentAction.value,\r\n      type: 'payment',\r\n      data: {\r\n        title: `付费${currentPaymentAction.value === 'publish' ? '发布' :\r\n                     currentPaymentAction.value === 'top' ? '置顶' :\r\n                     currentPaymentAction.value === 'refresh' ? '刷新' :\r\n                     currentPaymentAction.value === 'join' ? '入驻' : '续费'}`,\r\n        duration: selectedOption.duration,\r\n        price: selectedOption.price,\r\n        type: 'payment'\r\n      }\r\n    });\r\n\r\n    // 重置状态\r\n    selectedDuration.value = '';\r\n    currentPaymentAction.value = '';\r\n  }, 1500);\r\n};\r\n\r\n// 初始化配置\r\nconst initConfig = async () => {\r\n  try {\r\n    // 检查功能是否启用\r\n    const enabled = await isFeatureEnabled(props.pageType);\r\n    if (!enabled) {\r\n      console.log('推广功能已禁用:', props.pageType);\r\n      return;\r\n    }\r\n\r\n    // 加载完整配置\r\n    promotionConfig.value = await getPromotionConfig();\r\n    configLoaded.value = true;\r\n\r\n    console.log('推广配置加载完成:', promotionConfig.value);\r\n  } catch (error) {\r\n    console.error('初始化推广配置失败:', error);\r\n    configLoaded.value = true; // 即使失败也标记为已加载，使用默认配置\r\n  }\r\n};\r\n\r\n// 组件挂载时初始化\r\nonMounted(() => {\r\n  initConfig();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.premium-actions-container {\r\n  width: 100%;\r\n  margin: 20rpx 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;\r\n\r\n  /* 添加容器动画 */\r\n  animation: fadeInUp 0.3s ease-out;\r\n}\r\n\r\n/* 容器动画 */\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20rpx);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.direct-options {\r\n  padding: 0;\r\n}\r\n\r\n.premium-option-card {\r\n  margin-bottom: 16px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: white;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  transform: translateZ(0); /* 启用硬件加速 */\r\n}\r\n\r\n/* 触摸反馈 */\r\n.premium-option-card:active {\r\n  transform: scale(0.98);\r\n  transition: transform 0.1s ease;\r\n}\r\n\r\n/* 悬停效果（适用于支持的设备） */\r\n@media (hover: hover) {\r\n  .premium-option-card:hover {\r\n    transform: translateY(-2rpx);\r\n    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);\r\n  }\r\n}\r\n\r\n.option-inner {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 20rpx;\r\n}\r\n\r\n.option-left {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.option-icon-container {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 12rpx;\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20rpx;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.option-icon-container.paid-icon {\r\n  background: linear-gradient(135deg, #ff9a9e, #fad0c4);\r\n}\r\n\r\n.option-icon-container:active {\r\n  transform: scale(0.9);\r\n}\r\n\r\n.option-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n}\r\n\r\n.option-content {\r\n  flex: 1;\r\n}\r\n\r\n.option-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  display: block;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.option-title.bold-title {\r\n  font-weight: 700;\r\n}\r\n\r\n.option-desc {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  display: block;\r\n  line-height: 1.4;\r\n}\r\n\r\n.option-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.option-tag {\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.free-tag {\r\n  background: linear-gradient(135deg, #4A90E2, #3B7DFC);\r\n  color: white;\r\n  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.3);\r\n}\r\n\r\n.paid-tag {\r\n  background: linear-gradient(135deg, #FF6B6B, #FF8E53);\r\n  color: white;\r\n  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);\r\n}\r\n\r\n.option-tag:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n/* 广告选项特殊样式 */\r\n.ad-option {\r\n  border-left: 4rpx solid #4A90E2;\r\n}\r\n\r\n.ad-option .option-icon-container {\r\n  background: linear-gradient(135deg, #4A90E2, #3B7DFC);\r\n}\r\n\r\n/* 付费选项特殊样式 */\r\n.paid-option {\r\n  border-left: 4rpx solid #FF6B6B;\r\n}\r\n\r\n.paid-option .option-icon-container {\r\n  background: linear-gradient(135deg, #FF6B6B, #FF8E53);\r\n}\r\n\r\n/* 付费模态框样式 */\r\n.payment-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(4px);\r\n  animation: fadeIn 0.3s ease-out;\r\n}\r\n\r\n.payment-content {\r\n  width: 90%;\r\n  max-width: 650rpx;\r\n  background: white;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.payment-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.payment-title {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.close-btn {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 50%;\r\n  background: #f5f5f5;\r\n  color: #666;\r\n  font-size: 40rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:active {\r\n  background: #e0e0e0;\r\n  transform: scale(0.95);\r\n}\r\n\r\n.payment-body {\r\n  padding: 30rpx;\r\n}\r\n\r\n.payment-desc {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  margin-bottom: 30rpx;\r\n  display: block;\r\n}\r\n\r\n.duration-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20rpx;\r\n}\r\n\r\n.duration-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 30rpx;\r\n  border: 2rpx solid #f0f0f0;\r\n  border-radius: 16rpx;\r\n  background: white;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.duration-item:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.duration-item.active {\r\n  border-color: #4f46e5;\r\n  background: linear-gradient(135deg, #f8faff, #eef2ff);\r\n  box-shadow: 0 4rpx 15rpx rgba(79, 70, 229, 0.15);\r\n}\r\n\r\n.duration-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8rpx;\r\n}\r\n\r\n.duration-text {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.duration-price {\r\n  font-size: 40rpx;\r\n  font-weight: 700;\r\n  color: #ff6b6b;\r\n}\r\n\r\n.duration-badge {\r\n  padding: 8rpx 16rpx;\r\n  background: linear-gradient(135deg, #ff6b6b, #ff8e53);\r\n  color: white;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 600;\r\n}\r\n\r\n.payment-footer {\r\n  display: flex;\r\n  gap: 20rpx;\r\n  padding: 30rpx;\r\n  border-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.payment-btn {\r\n  flex: 1;\r\n  height: 88rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 16rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cancel-btn {\r\n  background: #f5f5f5;\r\n  color: #666;\r\n}\r\n\r\n.cancel-btn:active {\r\n  background: #e0e0e0;\r\n  transform: scale(0.98);\r\n}\r\n\r\n.confirm-btn {\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  color: white;\r\n  box-shadow: 0 4rpx 15rpx rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.confirm-btn:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.confirm-btn.disabled {\r\n  background: #d1d5db;\r\n  color: #9ca3af;\r\n  box-shadow: none;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 模态框动画 */\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(100rpx) scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1);\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media screen and (max-width: 750rpx) {\r\n  .option-inner {\r\n    padding: 16rpx;\r\n  }\r\n\r\n  .option-icon-container {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    margin-right: 16rpx;\r\n  }\r\n\r\n  .option-icon {\r\n    width: 20rpx;\r\n    height: 20rpx;\r\n  }\r\n\r\n  .option-title {\r\n    font-size: 28rpx;\r\n  }\r\n\r\n  .option-desc {\r\n    font-size: 24rpx;\r\n  }\r\n\r\n  .option-tag {\r\n    padding: 6rpx 12rpx;\r\n    font-size: 22rpx;\r\n  }\r\n\r\n  .payment-content {\r\n    width: 95%;\r\n  }\r\n\r\n  .payment-header,\r\n  .payment-body,\r\n  .payment-footer {\r\n    padding: 20rpx;\r\n  }\r\n\r\n  .duration-item {\r\n    padding: 20rpx;\r\n  }\r\n\r\n  .duration-text {\r\n    font-size: 28rpx;\r\n  }\r\n\r\n  .duration-price {\r\n    font-size: 36rpx;\r\n  }\r\n\r\n  .payment-btn {\r\n    height: 80rpx;\r\n    font-size: 28rpx;\r\n  }\r\n}\r\n</style>", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/premium/ConfigurablePremiumActions.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "uni", "getPricingConfig", "logUserAction", "isFeatureEnabled", "getPromotionConfig", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA2RA,UAAM,QAAQ;AAgBd,UAAM,OAAO;AAGb,UAAM,kBAAkBA,cAAAA,IAAI,IAAI;AAChC,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAG9B,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC/B,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,QAAQ,cAAc,YAAY,SAAS,KAAK;AACtD,aAAO,MAAM,SAAS,SAAS;AAAA,IACjC,CAAC;AAGD,UAAM,mBAAmBD,cAAAA,IAAI,KAAK;AAClC,UAAM,mBAAmBA,cAAAA,IAAI,EAAE;AAC/B,UAAM,uBAAuBA,cAAAA,IAAI,EAAE;AACnC,UAAM,qBAAqBA,cAAAA,IAAI;AAAA,MAC7B,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS,CAAE;AAAA,IACb,CAAC;AAGD,UAAM,qBAAqB,CAAC,QAAQ,eAAe;AACjDE,oBAAA,MAAA,MAAA,OAAA,4DAAY,SAAS,QAAQ,UAAU;AAGvC,UAAI,SAAS;AAEb,UAAI,WAAW,WAAW;AACxB,YAAI,eAAe,MAAM;AACvB,cAAI,UAAU,OAAO;AACnB,qBAAS;AAAA,cACP,OAAO;AAAA,cACP,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA,YACpB;AAAA,UACA,OAAa;AACL,qBAAS;AAAA,cACP,OAAO;AAAA,cACP,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA,YACpB;AAAA,UACO;AAAA,QACF;AAAA,MACL,WAAa,WAAW,OAAO;AAC3B,YAAI,eAAe,MAAM;AACvB,mBAAS;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU;AAAA,UAClB;AAAA,QACK;AAAA,MACL,WAAa,WAAW,WAAW;AAC/B,YAAI,eAAe,MAAM;AACvB,mBAAS;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,UACd;AAAA,QACK;AAAA,MACL,WAAa,WAAW,QAAQ;AAC5B,YAAI,eAAe,MAAM;AACvB,mBAAS;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU;AAAA,UAClB;AAAA,QACK;AAAA,MACL,WAAa,WAAW,SAAS;AAC7B,YAAI,eAAe,MAAM;AACvB,mBAAS;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU;AAAA,UAClB;AAAA,QACK;AAAA,MACF;AAGD,UAAI,eAAe,MAAM;AACvB,YAAI,QAAQ;AACV,iBAAO,QAAQ,MAAM;AAAA,QACtB;AAAA,MACL,WAAa,eAAe,QAAQ;AAEhC,yBAAiB,MAAM;AAAA,MACxB;AAAA,IACH;AAGA,UAAM,SAAS,CAAC,QAAQ,WAAW;AACjCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,aAAK,oBAAoB;AAAA,UACvB;AAAA,UACA,MAAM;AAAA,UACN,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,mBAAmB,OAAO,WAAW;;AACzC,2BAAqB,QAAQ;AAC7B,uBAAiB,QAAQ;AAEzB,UAAI;AAEF,cAAM,iBAAiB,MAAMC,uCAAiB,MAAM;AAGpD,YAAI,QAAQ;AACZ,YAAI,cAAc;AAElB,YAAI,WAAW,WAAW;AACxB,kBAAQ;AACR,wBAAc;AAAA,QACpB,WAAe,WAAW,OAAO;AAC3B,kBAAQ;AACR,wBAAc;AAAA,QACpB,WAAe,WAAW,WAAW;AAC/B,kBAAQ;AACR,wBAAc;AAAA,QACpB,WAAe,WAAW,QAAQ;AAC5B,kBAAQ;AACR,wBAAc;AAAA,QACpB,WAAe,WAAW,SAAS;AAC7B,kBAAQ;AACR,wBAAc;AAAA,QACf;AAED,2BAAmB,QAAQ;AAAA,UACzB;AAAA,UACA;AAAA,UACA,SAAS,kBAAkB,CAAE;AAAA,QACnC;AAEI,yBAAiB,QAAQ;AAGzBC,8BAAAA,cAAc,sBAAsB;AAAA,UAClC;AAAA,UACA,UAAU,MAAM;AAAA,UAChB,SAAQ,WAAM,aAAN,mBAAgB;AAAA,QAC9B,CAAK;AAAA,MAEF,SAAQ,OAAO;AACdF,sBAAc,MAAA,MAAA,SAAA,4DAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,uBAAiB,QAAQ,OAAO;AAAA,IAClC;AAGA,UAAM,oBAAoB,MAAM;AAC9B,uBAAiB,QAAQ;AACzB,uBAAiB,QAAQ;AAGzB,UAAI,qBAAqB,OAAO;AAC9B,aAAK,oBAAoB;AAAA,UACvB,QAAQ,qBAAqB;AAAA,UAC7B,MAAM;AAAA,UACN,QAAQ;AAAA,QACd,CAAK;AAAA,MACF;AAED,2BAAqB,QAAQ;AAAA,IAC/B;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,iBAAiB,OAAO;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,YAAM,iBAAiB,mBAAmB,MAAM,QAAQ;AAAA,QACtD,YAAU,OAAO,aAAa,iBAAiB;AAAA,MACnD;AAEE,UAAI,CAAC;AAAgB;AAGrB,uBAAiB,QAAQ;AAGzBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,aAAK,oBAAoB;AAAA,UACvB,QAAQ,qBAAqB;AAAA,UAC7B,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,OAAO,KAAK,qBAAqB,UAAU,YAAY,OAC1C,qBAAqB,UAAU,QAAQ,OACvC,qBAAqB,UAAU,YAAY,OAC3C,qBAAqB,UAAU,SAAS,OAAO,IAAI;AAAA,YAChE,UAAU,eAAe;AAAA,YACzB,OAAO,eAAe;AAAA,YACtB,MAAM;AAAA,UACP;AAAA,QACP,CAAK;AAGD,yBAAiB,QAAQ;AACzB,6BAAqB,QAAQ;AAAA,MAC9B,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,aAAa,YAAY;AAC7B,UAAI;AAEF,cAAM,UAAU,MAAMG,sBAAAA,iBAAiB,MAAM,QAAQ;AACrD,YAAI,CAAC,SAAS;AACZH,wBAAY,MAAA,MAAA,OAAA,4DAAA,YAAY,MAAM,QAAQ;AACtC;AAAA,QACD;AAGD,wBAAgB,QAAQ,MAAMI,sBAAAA;AAC9B,qBAAa,QAAQ;AAErBJ,qGAAY,aAAa,gBAAgB,KAAK;AAAA,MAC/C,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,4DAAA,cAAc,KAAK;AACjC,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;AAGAK,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtkBD,GAAG,gBAAgB,SAAS;"}