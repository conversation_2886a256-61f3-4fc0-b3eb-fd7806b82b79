{"version": 3, "file": "merchantPromotionMixin.js", "sources": ["mixins/merchantPromotionMixin.js"], "sourcesContent": ["/**\r\n * 商家推广混入\r\n * 为商家详情页提供推广能力\r\n */\r\nimport basePromotionMixin from './basePromotionMixin';\r\n\r\nexport default {\r\n  mixins: [basePromotionMixin],\r\n\r\n  data() {\r\n    return {\r\n      // 设置页面类型为商家\r\n      pageType: 'merchant'\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    /**\r\n     * 重写：判断当前用户是否是内容所有者\r\n     */\r\n    isContentOwner() {\r\n      // 获取当前用户ID\r\n      const currentUserId = this.$store?.state?.user?.userId || '';\r\n      // 获取商家ID\r\n      const merchantId = this.shopData?.merchantId || this.shopData?.ownerId || '';\r\n\r\n      // 判断当前用户是否是商家所有者\r\n      return currentUserId && merchantId && currentUserId === merchantId;\r\n    },\r\n\r\n    /**\r\n     * 重写：判断当前内容是否支持佣金\r\n     */\r\n    isCommissionContent() {\r\n      // 商家通常支持佣金\r\n      const canDistribute = this.shopData?.canDistribute !== false; // 默认为true\r\n      \r\n      return canDistribute;\r\n    },\r\n\r\n    /**\r\n     * 重写：生成推广数据\r\n     */\r\n    generatePromotionData() {\r\n      // 获取商家数据\r\n      const shop = this.shopData || {};\r\n      \r\n      // 构建推广数据\r\n      this.promotionData = {\r\n        id: shop.id || '',\r\n        title: shop.shopName || '商家详情',\r\n        image: shop.logo || shop.images?.[0] || '/static/images/tabbar/商家入驻.png',\r\n        category: shop.category || '',\r\n        address: shop.address || '',\r\n        contactPhone: shop.contactPhone || '',\r\n        description: shop.description ? (shop.description.length > 50 ? shop.description.substring(0, 50) + '...' : shop.description) : '',\r\n        // 如果有更多商家特定字段，可以在这里添加\r\n      };\r\n    },\r\n\r\n    /**\r\n     * 显示商家推广浮层\r\n     */\r\n    showMerchantPromotion() {\r\n      // 如果没有推广权限，显示提示\r\n      if (!this.hasPromotionPermission) {\r\n        uni.showToast({\r\n          title: '暂无推广权限',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 打开推广工具\r\n      this.openPromotionTools();\r\n    }\r\n  }\r\n}; "], "names": ["basePromotionMixin", "uni"], "mappings": ";;;AAMA,MAAe,yBAAA;AAAA,EACb,QAAQ,CAACA,0BAAAA,kBAAkB;AAAA,EAE3B,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,UAAU;AAAA,IAChB;AAAA,EACG;AAAA,EAED,SAAS;AAAA;AAAA;AAAA;AAAA,IAIP,iBAAiB;;AAEf,YAAM,kBAAgB,sBAAK,WAAL,mBAAa,UAAb,mBAAoB,SAApB,mBAA0B,WAAU;AAE1D,YAAM,eAAa,UAAK,aAAL,mBAAe,iBAAc,UAAK,aAAL,mBAAe,YAAW;AAG1E,aAAO,iBAAiB,cAAc,kBAAkB;AAAA,IACzD;AAAA;AAAA;AAAA;AAAA,IAKD,sBAAsB;;AAEpB,YAAM,kBAAgB,UAAK,aAAL,mBAAe,mBAAkB;AAEvD,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA,IAKD,wBAAwB;;AAEtB,YAAM,OAAO,KAAK,YAAY;AAG9B,WAAK,gBAAgB;AAAA,QACnB,IAAI,KAAK,MAAM;AAAA,QACf,OAAO,KAAK,YAAY;AAAA,QACxB,OAAO,KAAK,UAAQ,UAAK,WAAL,mBAAc,OAAM;AAAA,QACxC,UAAU,KAAK,YAAY;AAAA,QAC3B,SAAS,KAAK,WAAW;AAAA,QACzB,cAAc,KAAK,gBAAgB;AAAA,QACnC,aAAa,KAAK,cAAe,KAAK,YAAY,SAAS,KAAK,KAAK,YAAY,UAAU,GAAG,EAAE,IAAI,QAAQ,KAAK,cAAe;AAAA;AAAA,MAExI;AAAA,IACK;AAAA;AAAA;AAAA;AAAA,IAKD,wBAAwB;AAEtB,UAAI,CAAC,KAAK,wBAAwB;AAChCC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AACD;AAAA,MACD;AAGD,WAAK,mBAAkB;AAAA,IACxB;AAAA,EACF;AACH;;"}