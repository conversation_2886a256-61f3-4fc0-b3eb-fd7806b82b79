{"version": 3, "file": "verify.js", "sources": ["pages/business/verify.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYnVzaW5lc3MvdmVyaWZ5LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"verify-container\">\r\n    <!-- 顶部背景渐变 -->\r\n    <view class=\"top-gradient\"></view>\r\n    \r\n    <!-- 状态栏占位 -->\r\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n    \r\n    <!-- 导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">商家认证</view>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <scroll-view scroll-y class=\"content\">\r\n      <!-- 认证说明卡片 -->\r\n      <view class=\"info-card\">\r\n        <view class=\"info-header\">\r\n          <image src=\"/static/images/tabbar/认证.png\" class=\"info-icon\"></image>\r\n          <text class=\"info-title\">商家认证说明</text>\r\n        </view>\r\n        <view class=\"info-content\">\r\n          <text class=\"info-text\">商家认证后，您的店铺将获得官方认证标识，提升用户信任度和转化率。认证商家在平台享有搜索排名优先、流量扶持等多重特权。</text>\r\n        </view>\r\n        <view class=\"info-tips\">\r\n          <text class=\"tip-title\">认证须知：</text>\r\n          <text class=\"tip-item\">· 认证过程大约需要1-3个工作日</text>\r\n          <text class=\"tip-item\">· 请确保上传资料真实有效</text>\r\n          <text class=\"tip-item\">· 认证成功后12个月内有效</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 认证表单 -->\r\n      <view class=\"form-section\">\r\n        <view class=\"section-title\">基本信息</view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">商家名称</text>\r\n          <input class=\"form-input\" v-model=\"formData.shopName\" placeholder=\"与营业执照一致\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">法定代表人</text>\r\n          <input class=\"form-input\" v-model=\"formData.legalPerson\" placeholder=\"营业执照上的法定代表人\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">经营地址</text>\r\n          <input class=\"form-input\" v-model=\"formData.address\" placeholder=\"详细经营地址\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">联系电话</text>\r\n          <input class=\"form-input\" v-model=\"formData.contactPhone\" placeholder=\"联系电话\" type=\"number\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">经营范围</text>\r\n          <textarea class=\"form-textarea\" v-model=\"formData.businessScope\" placeholder=\"与营业执照一致\"></textarea>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 证件上传 -->\r\n      <view class=\"form-section\">\r\n        <view class=\"section-title\">证件上传</view>\r\n        \r\n        <view class=\"watermark-notice\">\r\n          <text class=\"watermark-notice-text\">上传的图片将自动添加\"只用于磁州生活网认证\"水印，请确保图片清晰可见</text>\r\n        </view>\r\n        \r\n        <view class=\"upload-item\">\r\n          <text class=\"upload-label\">营业执照</text>\r\n          <view class=\"upload-desc\">请上传营业执照正本清晰照片</view>\r\n          <view class=\"upload-wrapper\" @click=\"chooseImage('license')\">\r\n            <view class=\"upload-placeholder\" v-if=\"!formData.licenseImage\">\r\n              <image src=\"/static/images/tabbar/上传.png\" class=\"upload-icon\"></image>\r\n              <text class=\"upload-text\">点击上传</text>\r\n            </view>\r\n            <image v-else :src=\"formData.licenseImage\" mode=\"aspectFit\" class=\"preview-image\"></image>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"upload-item\">\r\n          <text class=\"upload-label\">法人身份证正面</text>\r\n          <view class=\"upload-desc\">请上传法人身份证人像面</view>\r\n          <view class=\"upload-wrapper\" @click=\"chooseImage('idCardFront')\">\r\n            <view class=\"upload-placeholder\" v-if=\"!formData.idCardFrontImage\">\r\n              <image src=\"/static/images/tabbar/上传.png\" class=\"upload-icon\"></image>\r\n              <text class=\"upload-text\">点击上传</text>\r\n            </view>\r\n            <image v-else :src=\"formData.idCardFrontImage\" mode=\"aspectFit\" class=\"preview-image\"></image>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"upload-item\">\r\n          <text class=\"upload-label\">法人身份证背面</text>\r\n          <view class=\"upload-desc\">请上传法人身份证国徽面</view>\r\n          <view class=\"upload-wrapper\" @click=\"chooseImage('idCardBack')\">\r\n            <view class=\"upload-placeholder\" v-if=\"!formData.idCardBackImage\">\r\n              <image src=\"/static/images/tabbar/上传.png\" class=\"upload-icon\"></image>\r\n              <text class=\"upload-text\">点击上传</text>\r\n            </view>\r\n            <image v-else :src=\"formData.idCardBackImage\" mode=\"aspectFit\" class=\"preview-image\"></image>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"upload-item\">\r\n          <text class=\"upload-label\">店铺门头照</text>\r\n          <view class=\"upload-desc\">请上传清晰的店铺门头照片</view>\r\n          <view class=\"upload-wrapper\" @click=\"chooseImage('storefront')\">\r\n            <view class=\"upload-placeholder\" v-if=\"!formData.storefrontImage\">\r\n              <image src=\"/static/images/tabbar/上传.png\" class=\"upload-icon\"></image>\r\n              <text class=\"upload-text\">点击上传</text>\r\n            </view>\r\n            <image v-else :src=\"formData.storefrontImage\" mode=\"aspectFit\" class=\"preview-image\"></image>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 行业资质 -->\r\n      <view class=\"form-section\">\r\n        <view class=\"section-title\">行业资质（可选）</view>\r\n        <view class=\"form-tip\">根据您的经营类型，可上传相关行业资质证明，增加认证通过率</view>\r\n        \r\n        <view class=\"upload-item\">\r\n          <text class=\"upload-label\">行业资质证明</text>\r\n          <view class=\"upload-desc\">如：食品经营许可证、卫生许可证等</view>\r\n          <view class=\"upload-wrapper\" @click=\"chooseImage('qualification')\">\r\n            <view class=\"upload-placeholder\" v-if=\"!formData.qualificationImage\">\r\n              <image src=\"/static/images/tabbar/上传.png\" class=\"upload-icon\"></image>\r\n              <text class=\"upload-text\">点击上传</text>\r\n            </view>\r\n            <image v-else :src=\"formData.qualificationImage\" mode=\"aspectFit\" class=\"preview-image\"></image>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 认证协议 -->\r\n      <view class=\"agreement-section\">\r\n        <checkbox-group @change=\"checkboxChange\">\r\n          <label class=\"agreement-label\">\r\n            <checkbox value=\"agree\" :checked=\"isAgreed\" color=\"#1677FF\" />\r\n            <text class=\"agreement-text\">我已阅读并同意</text>\r\n            <text class=\"agreement-link\" @click=\"showAgreement\">《商家认证服务协议》</text>\r\n          </label>\r\n        </checkbox-group>\r\n      </view>\r\n      \r\n      <!-- 提交按钮 -->\r\n      <view class=\"submit-section\">\r\n        <button class=\"submit-btn\" :disabled=\"!isFormValid\" :class=\"{'submit-btn-disabled': !isFormValid}\" @click=\"submitVerify\">提交认证申请</button>\r\n        <view class=\"submit-tip\">提交后，平台将在1-3个工作日内完成审核</view>\r\n      </view>\r\n    </scroll-view>\r\n    \r\n    <!-- 隐藏的画布，用于生成水印图片 -->\r\n    <canvas canvas-id=\"watermarkCanvas\" style=\"position: absolute; left: -9999px; width: 300px; height: 300px;\"></canvas>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\n\r\nconst statusBarHeight = ref(20);\r\nconst isAgreed = ref(false);\r\nconst formData = reactive({\r\n  shopName: '',\r\n  legalPerson: '',\r\n  address: '',\r\n  contactPhone: '',\r\n  businessScope: '',\r\n  licenseImage: '',\r\n  idCardFrontImage: '',\r\n  idCardBackImage: '',\r\n  storefrontImage: '',\r\n  qualificationImage: ''\r\n});\r\n\r\nconst isFormValid = computed(() => {\r\n  return isAgreed.value &&\r\n    formData.shopName &&\r\n    formData.legalPerson &&\r\n    formData.address &&\r\n    formData.contactPhone &&\r\n    formData.businessScope &&\r\n    formData.licenseImage &&\r\n    formData.idCardFrontImage &&\r\n    formData.idCardBackImage &&\r\n    formData.storefrontImage;\r\n});\r\n\r\nonLoad(() => {\r\n  // 获取状态栏高度\r\n  const sysInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = sysInfo.statusBarHeight;\r\n\r\n  // 如果有商家信息，自动填充部分表单\r\n  loadMerchantInfo();\r\n});\r\n\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\nconst loadMerchantInfo = () => {\r\n  // 模拟从本地或API获取商家信息\r\n  const merchantInfo = uni.getStorageSync('lastMerchantData');\r\n  if (merchantInfo) {\r\n    formData.shopName = merchantInfo.shopName || '';\r\n    formData.address = merchantInfo.address || '';\r\n    formData.contactPhone = merchantInfo.contactPhone || '';\r\n  }\r\n};\r\n\r\nconst chooseImage = (type) => {\r\n  uni.chooseImage({\r\n    count: 1,\r\n    sizeType: ['compressed'],\r\n    sourceType: ['album', 'camera'],\r\n    success: (res) => {\r\n      const tempFilePath = res.tempFilePaths[0];\r\n\r\n      // 显示上传中提示\r\n      uni.showLoading({\r\n        title: '处理中...',\r\n        mask: true\r\n      });\r\n\r\n      // 添加水印\r\n      addWatermark(tempFilePath, type);\r\n    }\r\n  });\r\n};\r\n\r\nconst addWatermark = (imagePath, type) => {\r\n  const watermarkText = '只用于磁州生活网认证';\r\n\r\n  uni.getImageInfo({\r\n    src: imagePath,\r\n    success: (imageInfo) => {\r\n      const canvasWidth = imageInfo.width;\r\n      const canvasHeight = imageInfo.height;\r\n      const ctx = uni.createCanvasContext('watermarkCanvas');\r\n\r\n      // 调整canvas尺寸\r\n      const canvasElement = uni.createSelectorQuery().select('#watermarkCanvas');\r\n      // this is a bit of a hack as there is no direct way to set canvas size for this API\r\n      // so we rely on the style attribute, which we set before calling this function\r\n      // but to be safe, let's just assume the style was set correctly\r\n      \r\n      ctx.clearRect(0, 0, canvasWidth, canvasHeight);\r\n      ctx.drawImage(imagePath, 0, 0, canvasWidth, canvasHeight);\r\n\r\n      // 设置水印样式\r\n      ctx.setFontSize(20);\r\n      ctx.setFillStyle('rgba(0, 0, 0, 0.3)');\r\n      ctx.setGlobalAlpha(0.5);\r\n      ctx.rotate(-20 * Math.PI / 180);\r\n\r\n      // 平铺水印\r\n      const textWidth = ctx.measureText(watermarkText).width;\r\n      for (let y = -canvasHeight; y < canvasHeight * 2; y += 100) {\r\n        for (let x = -canvasWidth; x < canvasWidth * 2; x += textWidth + 80) {\r\n            ctx.fillText(watermarkText, x, y);\r\n        }\r\n      }\r\n      \r\n      ctx.draw(false, () => {\r\n        uni.canvasToTempFilePath({\r\n          canvasId: 'watermarkCanvas',\r\n          success: (res) => {\r\n            formData[type + 'Image'] = res.tempFilePath;\r\n            uni.hideLoading();\r\n          },\r\n          fail: (err) => {\r\n            console.error('Canvas to temp file path failed:', err);\r\n            uni.hideLoading();\r\n            uni.showToast({ title: '图片处理失败', icon: 'none' });\r\n          }\r\n        });\r\n      });\r\n    },\r\n    fail: (err) => {\r\n        console.error('Get image info failed:', err);\r\n        uni.hideLoading();\r\n        uni.showToast({ title: '无法获取图片信息', icon: 'none' });\r\n    }\r\n  });\r\n};\r\n\r\nconst checkboxChange = (e) => {\r\n  isAgreed.value = e.detail.value.includes('agree');\r\n};\r\n\r\nconst showAgreement = () => {\r\n  uni.showModal({\r\n    title: '商家认证服务协议',\r\n    content: '这里是详细的商家认证服务协议内容...（内容待填充）',\r\n    showCancel: false,\r\n    confirmText: '我已阅读'\r\n  });\r\n};\r\n\r\nconst submitVerify = () => {\r\n  if (!isFormValid.value) {\r\n    uni.showToast({\r\n      title: '请填写完整的必填信息并同意协议',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  uni.showLoading({\r\n    title: '提交中...',\r\n    mask: true\r\n  });\r\n  \r\n  // 模拟提交到后端\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    uni.showToast({\r\n      title: '提交成功！待审核',\r\n      icon: 'success'\r\n    });\r\n\r\n    // 可以在这里将formData保存到本地或上传\r\n    console.log('提交的表单数据:', formData);\r\n\r\n    // 跳转到成功页或状态页\r\n    setTimeout(() => {\r\n        uni.redirectTo({\r\n            url: '/pages/business/success?from=verify'\r\n        });\r\n    }, 1500);\r\n    \r\n  }, 2000);\r\n};\r\n\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.verify-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #f8f9fc;\r\n}\r\n\r\n/* 顶部渐变背景 */\r\n.top-gradient {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 25vh;\r\n  background: linear-gradient(135deg, #0052cc, #1677ff);\r\n  border-bottom-left-radius: 20px;\r\n  border-bottom-right-radius: 20px;\r\n  z-index: 0;\r\n}\r\n\r\n/* 导航栏 */\r\n.navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 20px;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.navbar-left {\r\n  width: 44px;\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #fff;\r\n}\r\n\r\n.navbar-right {\r\n  width: 44px;\r\n}\r\n\r\n/* 内容区域 */\r\n.content {\r\n  flex: 1;\r\n  position: relative;\r\n  z-index: 1;\r\n  padding: 0 20px 30px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 认证说明卡片 */\r\n.info-card {\r\n  background-color: #fff;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  margin-top: 20px;\r\n  box-shadow: 0 8px 25px rgba(0, 82, 204, 0.1);\r\n}\r\n\r\n.info-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.info-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.info-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #0052CC;\r\n}\r\n\r\n.info-content {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.info-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.6;\r\n}\r\n\r\n.info-tips {\r\n  background-color: #f0f8ff;\r\n  border-radius: 8px;\r\n  padding: 12px;\r\n}\r\n\r\n.tip-title {\r\n  font-size: 14px;\r\n  color: #0052CC;\r\n  font-weight: bold;\r\n  display: block;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.tip-item {\r\n  font-size: 12px;\r\n  color: #333;\r\n  line-height: 1.6;\r\n  display: block;\r\n}\r\n\r\n/* 表单区域 */\r\n.form-section {\r\n  background-color: #fff;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  margin-top: 20px;\r\n  box-shadow: 0 8px 25px rgba(0, 82, 204, 0.08);\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #333;\r\n  padding-bottom: 10px;\r\n  margin-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.form-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.form-input, .form-textarea {\r\n  font-size: 14px;\r\n  color: #333;\r\n  background-color: #f7f7f7;\r\n  border-radius: 8px;\r\n  padding: 12px;\r\n  border: 1px solid #eee;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.form-input:focus, .form-textarea:focus {\r\n  border-color: #1677FF;\r\n}\r\n\r\n.form-textarea {\r\n  height: 80px;\r\n  width: auto;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 证件上传 */\r\n.watermark-notice {\r\n  background-color: #e6f7ff;\r\n  border: 1px solid #91d5ff;\r\n  color: #0052cc;\r\n  padding: 8px 12px;\r\n  border-radius: 8px;\r\n  font-size: 12px;\r\n  line-height: 1.5;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.upload-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.upload-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 4px;\r\n  display: block;\r\n}\r\n\r\n.upload-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.upload-wrapper {\r\n  width: 100%;\r\n  height: 180px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 8px;\r\n  border: 1px dashed #ddd;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.upload-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  color: #999;\r\n}\r\n\r\n.upload-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 协议 */\r\n.agreement-section {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.agreement-label {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-left: 8px;\r\n}\r\n\r\n.agreement-link {\r\n  font-size: 14px;\r\n  color: #1677FF;\r\n  margin-left: 4px;\r\n}\r\n\r\n/* 提交按钮 */\r\n.submit-section {\r\n  margin: 30px 0;\r\n  text-align: center;\r\n}\r\n\r\n.submit-btn {\r\n  height: 50px;\r\n  background: linear-gradient(135deg, #1677FF, #0052CC);\r\n  color: #fff;\r\n  font-size: 16px;\r\n  border-radius: 25px;\r\n  box-shadow: 0 6px 12px rgba(0, 82, 204, 0.2);\r\n  line-height: 50px;\r\n}\r\n\r\n.submit-btn-disabled {\r\n  background: #ccc;\r\n  box-shadow: none;\r\n}\r\n\r\n.submit-tip {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 12px;\r\n}\r\n\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/business/verify.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "onLoad", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAwKA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAC1B,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,SAAS;AAAA,MACT,cAAc;AAAA,MACd,eAAe;AAAA,MACf,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,IACtB,CAAC;AAED,UAAM,cAAcC,cAAQ,SAAC,MAAM;AACjC,aAAO,SAAS,SACd,SAAS,YACT,SAAS,eACT,SAAS,WACT,SAAS,gBACT,SAAS,iBACT,SAAS,gBACT,SAAS,oBACT,SAAS,mBACT,SAAS;AAAA,IACb,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AAEX,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAGhC;IACF,CAAC;AAED,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,mBAAmB,MAAM;AAE7B,YAAM,eAAeA,cAAAA,MAAI,eAAe,kBAAkB;AAC1D,UAAI,cAAc;AAChB,iBAAS,WAAW,aAAa,YAAY;AAC7C,iBAAS,UAAU,aAAa,WAAW;AAC3C,iBAAS,eAAe,aAAa,gBAAgB;AAAA,MACtD;AAAA,IACH;AAEA,UAAM,cAAc,CAAC,SAAS;AAC5BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,gBAAM,eAAe,IAAI,cAAc,CAAC;AAGxCA,wBAAAA,MAAI,YAAY;AAAA,YACd,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAGD,uBAAa,cAAc,IAAI;AAAA,QAChC;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,eAAe,CAAC,WAAW,SAAS;AACxC,YAAM,gBAAgB;AAEtBA,oBAAAA,MAAI,aAAa;AAAA,QACf,KAAK;AAAA,QACL,SAAS,CAAC,cAAc;AACtB,gBAAM,cAAc,UAAU;AAC9B,gBAAM,eAAe,UAAU;AAC/B,gBAAM,MAAMA,cAAAA,MAAI,oBAAoB,iBAAiB;AAG/BA,wBAAAA,MAAI,oBAAmB,EAAG,OAAO,kBAAkB;AAKzE,cAAI,UAAU,GAAG,GAAG,aAAa,YAAY;AAC7C,cAAI,UAAU,WAAW,GAAG,GAAG,aAAa,YAAY;AAGxD,cAAI,YAAY,EAAE;AAClB,cAAI,aAAa,oBAAoB;AACrC,cAAI,eAAe,GAAG;AACtB,cAAI,OAAO,MAAM,KAAK,KAAK,GAAG;AAG9B,gBAAM,YAAY,IAAI,YAAY,aAAa,EAAE;AACjD,mBAAS,IAAI,CAAC,cAAc,IAAI,eAAe,GAAG,KAAK,KAAK;AAC1D,qBAAS,IAAI,CAAC,aAAa,IAAI,cAAc,GAAG,KAAK,YAAY,IAAI;AACjE,kBAAI,SAAS,eAAe,GAAG,CAAC;AAAA,YACnC;AAAA,UACF;AAED,cAAI,KAAK,OAAO,MAAM;AACpBA,0BAAAA,MAAI,qBAAqB;AAAA,cACvB,UAAU;AAAA,cACV,SAAS,CAAC,QAAQ;AAChB,yBAAS,OAAO,OAAO,IAAI,IAAI;AAC/BA,8BAAG,MAAC,YAAW;AAAA,cAChB;AAAA,cACD,MAAM,CAAC,QAAQ;AACbA,8BAAA,MAAA,MAAA,SAAA,oCAAc,oCAAoC,GAAG;AACrDA,8BAAG,MAAC,YAAW;AACfA,8BAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAM,CAAE;AAAA,cAChD;AAAA,YACX,CAAS;AAAA,UACT,CAAO;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,wBAAA,MAAA,MAAA,SAAA,oCAAc,0BAA0B,GAAG;AAC3CA,wBAAG,MAAC,YAAW;AACfA,wBAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAM,CAAE;AAAA,QACpD;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,eAAS,QAAQ,EAAE,OAAO,MAAM,SAAS,OAAO;AAAA,IAClD;AAEA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,MACjB,CAAG;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,YAAY,OAAO;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGDA,sBAAY,MAAA,MAAA,OAAA,oCAAA,YAAY,QAAQ;AAGhC,mBAAW,MAAM;AACbA,wBAAAA,MAAI,WAAW;AAAA,YACX,KAAK;AAAA,UACjB,CAAS;AAAA,QACJ,GAAE,IAAI;AAAA,MAER,GAAE,GAAI;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpVA,GAAG,WAAWC,SAAe;"}