{"version": 3, "file": "promotion-tool.js", "sources": ["subPackages/promotion/pages/promotion-tool.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccHJvbW90aW9uXHBhZ2VzXHByb21vdGlvbi10b29sLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"promotion-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">推广海报</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 海报预览 -->\n    <view class=\"poster-preview-section\">\n      <view class=\"preview-card\" :style=\"{ backgroundColor: selectedTheme.bgColor }\">\n        <image class=\"poster-image\" :src=\"posterPath || previewData.image || defaultPosterPath\" mode=\"aspectFit\"></image>\n        <view class=\"preview-footer\">\n          <text class=\"preview-text\" :style=\"{ color: selectedTheme.textColor }\">预览效果</text>\n        </view>\n      </view>\n      \n      <view class=\"action-buttons\">\n        <button class=\"action-button save\" @click=\"savePoster\">\n          <view class=\"button-icon\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M7 10L12 15L17 10\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M12 15V3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <text>保存到相册</text>\n        </button>\n        \n        <button class=\"action-button share\" @click=\"shareToWechat\">\n          <view class=\"button-icon\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <circle cx=\"18\" cy=\"5\" r=\"3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"6\" cy=\"12\" r=\"3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"18\" cy=\"19\" r=\"3\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </view>\n          <text>分享海报</text>\n        </button>\n      </view>\n    </view>\n    \n    <!-- 内容预览 -->\n    <view class=\"content-preview\">\n      <view class=\"preview-info\">\n        <text class=\"preview-title\">{{ previewData.title || '加载中...' }}</text>\n        <view class=\"preview-detail\" v-if=\"previewData.type === 'carpool'\">\n          <text class=\"detail-item\">{{ previewData.departure }} → {{ previewData.destination }}</text>\n          <text class=\"detail-item\">{{ previewData.departureTime }}</text>\n        </view>\n        <view class=\"preview-detail\" v-else-if=\"previewData.type === 'product'\">\n          <text class=\"detail-item price\">¥{{ previewData.price }}</text>\n          <text class=\"detail-item original-price\" v-if=\"previewData.originalPrice\">¥{{ previewData.originalPrice }}</text>\n        </view>\n        <view class=\"preview-detail\" v-else-if=\"previewData.type === 'house'\">\n          <text class=\"detail-item\">{{ previewData.roomType }} | {{ previewData.area }}㎡</text>\n          <text class=\"detail-item price\">{{ previewData.price }}{{ previewData.priceUnit }}</text>\n        </view>\n        <view class=\"preview-detail\" v-else-if=\"previewData.type === 'merchant'\">\n          <text class=\"detail-item\">{{ previewData.category }}</text>\n          <text class=\"detail-item\">{{ previewData.address }}</text>\n        </view>\n        <view class=\"preview-detail\" v-else>\n          <text class=\"detail-item\">{{ previewData.description }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 佣金信息 -->\n    <view class=\"commission-info\" v-if=\"hasCommission\">\n      <view class=\"commission-card\">\n        <view class=\"commission-header\">\n          <image class=\"commission-icon\" src=\"/static/images/commission-icon.png\" mode=\"aspectFit\"></image>\n          <text class=\"commission-title\">推广佣金</text>\n        </view>\n        <view class=\"commission-content\">\n          <text class=\"commission-rate\">佣金比例: {{ commissionRate }}%</text>\n          <text class=\"commission-estimate\">预计佣金: ¥{{ estimatedCommission }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 自定义选项 -->\n    <view class=\"customization-section\">\n      <!-- 海报模板选择 -->\n      <view class=\"option-group\">\n        <text class=\"option-title\">海报模板</text>\n        <scroll-view scroll-x class=\"template-scroll\">\n          <view class=\"template-list\">\n            <view \n              v-for=\"(poster, index) in posterTemplates\" \n              :key=\"index\" \n              class=\"template-item\"\n              :class=\"{ active: currentPosterIndex === index }\"\n              @click=\"selectPoster(index)\"\n            >\n              <image class=\"template-thumb\" :src=\"poster.thumb\" mode=\"aspectFill\"></image>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n      \n      <!-- 主题颜色选择 -->\n      <view class=\"option-group\">\n        <text class=\"option-title\">主题颜色</text>\n        <view class=\"theme-options\">\n          <view \n            v-for=\"(theme, index) in themeColors\" \n            :key=\"index\" \n            class=\"theme-option\" \n            :class=\"{ active: selectedTheme === theme }\"\n            :style=\"{ backgroundColor: theme.bgColor }\"\n            @click=\"selectTheme(theme)\"\n          >\n            <view class=\"theme-check\" v-if=\"selectedTheme === theme\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M20 6L9 17L4 12\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 其他选项 -->\n      <view class=\"option-group\">\n        <view class=\"toggle-option\">\n          <text class=\"toggle-label\">显示推广员ID</text>\n          <switch :checked=\"showId\" @change=\"toggleId\" color=\"#3846cd\" />\n        </view>\n        \n        <view class=\"toggle-option\">\n          <text class=\"toggle-label\">添加推广文案</text>\n          <switch :checked=\"showPromoText\" @change=\"togglePromoText\" color=\"#3846cd\" />\n        </view>\n      </view>\n      \n      <!-- 自定义文案 -->\n      <view class=\"option-group\" v-if=\"showPromoText\">\n        <text class=\"option-title\">自定义文案</text>\n        <input \n          class=\"custom-input\" \n          type=\"text\" \n          v-model=\"customText\" \n          placeholder=\"输入自定义文案（最多20字）\" \n          maxlength=\"20\"\n          @input=\"onCustomTextChange\"\n        />\n      </view>\n    </view>\n    \n    <!-- 用于生成海报的隐藏画布 -->\n    <canvas canvas-id=\"posterCanvas\" style=\"width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;\"></canvas>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport promotionService from '@/utils/promotionService';\n\n// 页面参数\nconst contentType = ref('');\nconst contentId = ref('');\n\n// 预览数据\nconst previewData = reactive({\n  type: '',\n  title: '',\n  image: '',\n  description: '',\n  // 拼车特有字段\n  departure: '',\n  destination: '',\n  departureTime: '',\n  // 商品特有字段\n  price: 0,\n  originalPrice: 0,\n  // 房产特有字段\n  roomType: '',\n  area: 0,\n  priceUnit: '',\n  // 商家特有字段\n  category: '',\n  address: ''\n});\n\n// 佣金相关\nconst hasCommission = ref(false);\nconst commissionRate = ref(0);\nconst estimatedCommission = ref('0.00');\n\n// 二维码和海报\nconst showPosterPopup = ref(false);\nconst posterPath = ref('');\n\n// 当前用户ID\nconst userId = ref('');\n\n// 默认海报路径\nconst defaultPosterPath = '/static/images/default-poster.png';\n\n// 海报模板\nconst posterTemplates = ref([\n  {\n    name: '商品推广',\n    thumb: '/static/images/distribution/poster-thumb-1.png',\n    url: '/static/images/distribution/poster-1.png',\n    type: 'product'\n  },\n  {\n    name: '店铺推广',\n    thumb: '/static/images/distribution/poster-thumb-2.png',\n    url: '/static/images/distribution/poster-2.png',\n    type: 'store'\n  },\n  {\n    name: '活动推广',\n    thumb: '/static/images/distribution/poster-thumb-3.png',\n    url: '/static/images/distribution/poster-3.png',\n    type: 'activity'\n  },\n  {\n    name: '会员推广',\n    thumb: '/static/images/distribution/poster-thumb-4.png',\n    url: '/static/images/distribution/poster-4.png',\n    type: 'member'\n  },\n  {\n    name: '节日主题',\n    thumb: '/static/images/distribution/poster-thumb-5.png',\n    url: '/static/images/distribution/poster-5.png',\n    type: 'general'\n  }\n]);\n\n// 特定类型的模板\nconst typeTemplates = {\n  'carpool': [\n    {\n      name: '拼车专用',\n      thumb: '/static/images/distribution/carpool-thumb-1.png',\n      url: '/static/images/distribution/carpool-1.png',\n      type: 'carpool'\n    }\n  ],\n  'secondhand': [\n    {\n      name: '二手商品专用',\n      thumb: '/static/images/distribution/secondhand-thumb-1.png',\n      url: '/static/images/distribution/secondhand-1.png',\n      type: 'secondhand'\n    }\n  ],\n  'house': [\n    {\n      name: '房屋租售专用',\n      thumb: '/static/images/distribution/house-thumb-1.png',\n      url: '/static/images/distribution/house-1.png',\n      type: 'house'\n    }\n  ]\n};\n\n// 主题颜色\nconst themeColors = [\n  { bgColor: '#FFFFFF', textColor: '#333333' },\n  { bgColor: '#3846CD', textColor: '#FFFFFF' },\n  { bgColor: '#2C3AA0', textColor: '#FFFFFF' },\n  { bgColor: '#F5F7FA', textColor: '#333333' },\n  { bgColor: '#FFE8F0', textColor: '#FF3B30' },\n  { bgColor: '#E8F8FF', textColor: '#007AFF' }\n];\n\n// 状态变量\nconst currentPosterIndex = ref(0);\nconst selectedTheme = ref(themeColors[0]);\nconst showId = ref(true);\nconst showPromoText = ref(false);\nconst customText = ref('');\n\n// 当前海报\nconst currentPoster = computed(() => {\n  if (posterTemplates.value.length === 0) return '';\n  return posterTemplates.value[currentPosterIndex.value].url;\n});\n\n// 页面加载\nonMounted(() => {\n  // 获取页面参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  contentType.value = options.type || '';\n  contentId.value = options.id || '';\n  \n  // 获取用户信息\n  const userInfo = uni.getStorageSync('userInfo');\n  if (userInfo && userInfo.userId) {\n    userId.value = userInfo.userId;\n  }\n  \n  // 加载内容数据\n  loadContentData();\n  \n  // 加载适合的海报模板\n  loadPosterTemplates();\n  \n  // 延迟生成海报，确保数据已加载\n  setTimeout(() => {\n    generatePoster();\n  }, 1500);\n});\n\n// 加载海报模板\nconst loadPosterTemplates = () => {\n  // 获取特定类型的模板\n  const typeSpecificTemplates = typeTemplates[contentType.value] || [];\n  \n  // 合并模板\n  posterTemplates.value = [\n    ...typeSpecificTemplates,\n    ...posterTemplates.value.filter(template => \n      template.type === 'general' || template.type === contentType.value\n    )\n  ];\n};\n\n// 选择海报模板\nconst selectPoster = (index) => {\n  currentPosterIndex.value = index;\n  // 延迟生成海报，避免频繁调用\n  setTimeout(() => {\n    generatePoster();\n  }, 100);\n};\n\n// 选择主题颜色\nconst selectTheme = (theme) => {\n  selectedTheme.value = theme;\n  // 延迟生成海报，避免频繁调用\n  setTimeout(() => {\n    generatePoster();\n  }, 100);\n};\n\n// 切换显示推广员ID\nconst toggleId = (e) => {\n  showId.value = e.detail.value;\n  // 延迟生成海报，避免频繁调用\n  setTimeout(() => {\n    generatePoster();\n  }, 100);\n};\n\n// 切换显示推广文案\nconst togglePromoText = (e) => {\n  showPromoText.value = e.detail.value;\n  if (showPromoText.value && !customText.value) {\n    generateDefaultText();\n  }\n  // 延迟生成海报，避免频繁调用\n  setTimeout(() => {\n    generatePoster();\n  }, 100);\n};\n\n// 处理自定义文案变化\nconst onCustomTextChange = () => {\n  // 使用防抖，避免频繁生成海报\n  if (window.customTextTimer) {\n    clearTimeout(window.customTextTimer);\n  }\n  window.customTextTimer = setTimeout(() => {\n    generatePoster();\n  }, 500);\n};\n\n// 生成默认文案\nconst generateDefaultText = () => {\n  switch(contentType.value) {\n    case 'carpool':\n      customText.value = `【${previewData.title}】${previewData.departure}→${previewData.destination}，出发时间：${previewData.departureTime}`;\n      break;\n    case 'product':\n      customText.value = `【${previewData.title}】价格：¥${previewData.price}`;\n      break;\n    case 'house':\n      customText.value = `【${previewData.title}】${previewData.roomType}，${previewData.area}㎡，${previewData.price}${previewData.priceUnit}`;\n      break;\n    default:\n      customText.value = `【${previewData.title}】`;\n  }\n};\n\n// 显示帮助\nconst showHelp = () => {\n  uni.showModal({\n    title: '推广海报使用帮助',\n    content: '选择喜欢的海报模板，自定义颜色和文案，生成精美海报进行分享推广。',\n    showCancel: false\n  });\n};\n\n// 加载内容数据\nconst loadContentData = () => {\n  if (!contentType.value || !contentId.value) {\n    uni.showToast({\n      title: '参数错误',\n      icon: 'none'\n    });\n    setTimeout(() => {\n      goBack();\n    }, 1500);\n    return;\n  }\n  \n  // 显示加载中\n  uni.showLoading({\n    title: '加载中...'\n  });\n  \n  // 根据不同内容类型获取数据\n  switch (contentType.value) {\n    case 'carpool':\n      loadCarpoolData();\n      break;\n    case 'product':\n      loadProductData();\n      break;\n    case 'merchant':\n      loadMerchantData();\n      break;\n    case 'house':\n      loadHouseData();\n      break;\n    case 'service':\n      loadServiceData();\n      break;\n    case 'community':\n      loadCommunityData();\n      break;\n    case 'activity':\n      loadActivityData();\n      break;\n    case 'content':\n      loadContentPageData();\n      break;\n    default:\n      // 通用加载逻辑\n      loadGenericData();\n  }\n};\n\n// 加载拼车数据\nconst loadCarpoolData = () => {\n  // 模拟API调用\n  setTimeout(() => {\n    previewData.type = 'carpool';\n    previewData.title = '磁县到邯郸拼车';\n    previewData.image = '/static/images/carpool-default.jpg';\n    previewData.departure = '磁县城区';\n    previewData.destination = '邯郸火车站';\n    previewData.departureTime = '2023-07-20 08:00';\n    previewData.price = 15;\n    \n    // 设置佣金信息\n    hasCommission.value = false;\n    commissionRate.value = 0;\n    estimatedCommission.value = '0.00';\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 加载商品数据\nconst loadProductData = () => {\n  // 模拟API调用\n  setTimeout(() => {\n    previewData.type = 'product';\n    previewData.title = '有机新鲜蔬菜礼盒';\n    previewData.image = '/static/images/product-default.jpg';\n    previewData.price = 99.8;\n    previewData.originalPrice = 128;\n    previewData.description = '本地新鲜蔬菜，无农药，当天采摘';\n    \n    // 设置佣金信息\n    hasCommission.value = true;\n    commissionRate.value = 10;\n    estimatedCommission.value = (previewData.price * commissionRate.value / 100).toFixed(2);\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 加载商家数据\nconst loadMerchantData = () => {\n  // 模拟API调用\n  setTimeout(() => {\n    previewData.type = 'merchant';\n    previewData.title = '磁州同城生活馆';\n    previewData.image = '/static/images/shop-default.jpg';\n    previewData.category = '生活服务';\n    previewData.address = '磁县城区中心广场东侧100米';\n    previewData.description = '提供本地生活服务、特产销售等';\n    \n    // 设置佣金信息\n    hasCommission.value = true;\n    commissionRate.value = 15;\n    estimatedCommission.value = '5.00';\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 加载房产数据\nconst loadHouseData = () => {\n  // 模拟API调用\n  setTimeout(() => {\n    previewData.type = 'house';\n    previewData.title = '城区精装两居室出租';\n    previewData.image = '/static/images/house-default.jpg';\n    previewData.roomType = '2室1厅1卫';\n    previewData.area = 89;\n    previewData.price = 1200;\n    previewData.priceUnit = '/月';\n    previewData.description = '城区中心位置，交通便利，拎包入住';\n    \n    // 设置佣金信息\n    hasCommission.value = true;\n    commissionRate.value = 30;\n    estimatedCommission.value = '360.00';\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 加载服务数据\nconst loadServiceData = () => {\n  // 模拟API调用\n  setTimeout(() => {\n    previewData.type = 'service';\n    previewData.title = '专业家电维修';\n    previewData.image = '/static/images/service-default.jpg';\n    previewData.price = 50;\n    previewData.description = '各类家电维修，上门服务，快速响应';\n    \n    // 设置佣金信息\n    hasCommission.value = true;\n    commissionRate.value = 20;\n    estimatedCommission.value = '10.00';\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 加载社区信息数据\nconst loadCommunityData = () => {\n  // 模拟API调用\n  setTimeout(() => {\n    previewData.type = 'community';\n    previewData.title = '社区便民信息';\n    previewData.image = '/static/images/community-default.jpg';\n    previewData.description = '社区便民服务信息，包括水电维修、家政服务等';\n    \n    // 设置佣金信息\n    hasCommission.value = false;\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 加载活动数据\nconst loadActivityData = () => {\n  // 模拟API调用\n  setTimeout(() => {\n    previewData.type = 'activity';\n    previewData.title = '周末农产品展销会';\n    previewData.image = '/static/images/activity-default.jpg';\n    previewData.description = '本周末在城区广场举办农产品展销会，欢迎参加';\n    \n    // 设置佣金信息\n    hasCommission.value = true;\n    commissionRate.value = 5;\n    estimatedCommission.value = '2.50';\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 加载内容页面数据\nconst loadContentPageData = () => {\n  // 模拟API调用\n  setTimeout(() => {\n    previewData.type = 'content';\n    previewData.title = '磁州特产推荐';\n    previewData.image = '/static/images/content-default.jpg';\n    previewData.description = '磁州地区特色农产品与手工艺品推荐';\n    \n    // 设置佣金信息\n    hasCommission.value = false;\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 加载通用数据\nconst loadGenericData = () => {\n  // 模拟API调用\n  setTimeout(() => {\n    previewData.type = contentType.value;\n    previewData.title = '磁州生活网内容';\n    previewData.image = '/static/images/default.jpg';\n    previewData.description = '磁州生活网为您提供本地生活服务';\n    \n    // 设置佣金信息\n    hasCommission.value = false;\n    \n    uni.hideLoading();\n  }, 500);\n};\n\n// 分享到微信好友\nconst shareToWechat = () => {\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 0,\n    title: previewData.title,\n    summary: customText.value || previewData.description || '分享来自磁州生活网',\n    imageUrl: posterPath.value || previewData.image,\n    href: promotionService.generateShareLink(contentType.value, contentId.value, userId.value),\n    success: function() {\n      console.log('分享成功');\n      // 记录分享事件\n      promotionService.recordPromotion(contentType.value, contentId.value, 'wechat');\n    },\n    fail: function() {\n      console.log('分享失败');\n      uni.showToast({\n        title: '分享失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 生成海报\nconst generatePoster = async () => {\n  try {\n    // 重置海报路径，强制重新生成\n    posterPath.value = '';\n    \n    uni.showLoading({\n      title: '生成中...'\n    });\n    \n    // 生成海报\n    posterPath.value = await promotionService.generatePoster(\n      contentType.value,\n      previewData,\n      userId.value,\n      {\n        template: currentPoster.value,\n        theme: selectedTheme.value,\n        showId: showId.value,\n        customText: showPromoText.value ? customText.value : ''\n      }\n    );\n    \n    uni.hideLoading();\n  } catch (err) {\n    console.error('生成海报失败', err);\n    uni.hideLoading();\n    \n    // 设置默认海报路径，确保UI不会空白\n    posterPath.value = previewData.image || defaultPosterPath;\n    \n    uni.showToast({\n      title: '生成海报失败',\n      icon: 'none'\n    });\n  }\n};\n\n// 保存海报\nconst savePoster = async () => {\n  if (!posterPath.value) {\n    // 先生成海报\n    await generatePoster();\n  }\n  \n  if (posterPath.value) {\n    try {\n      uni.showLoading({\n        title: '保存中...'\n      });\n      \n      await promotionService.savePosterToAlbum(posterPath.value);\n      \n      uni.hideLoading();\n      uni.showToast({\n        title: '保存成功',\n        icon: 'success'\n      });\n    } catch (err) {\n      console.error('保存海报失败', err);\n      uni.hideLoading();\n      uni.showToast({\n        title: '保存失败',\n        icon: 'none'\n      });\n    }\n  }\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.promotion-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #3846cd, #2c3aa0);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(56, 70, 205, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 海报预览部分 */\n.poster-preview-section {\n  margin: 16px;\n}\n\n.preview-card {\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n  overflow: hidden;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.poster-image {\n  width: 100%;\n  max-width: 300px;\n  height: 400px;\n  border-radius: 10px;\n  background-color: #f5f5f5;\n  object-fit: contain;\n}\n\n.preview-footer {\n  margin-top: 12px;\n  text-align: center;\n}\n\n.preview-text {\n  font-size: 14px;\n  color: #666;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 12px;\n}\n\n.action-button {\n  flex: 1;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: 500;\n  border: none;\n}\n\n.action-button.save {\n  background: linear-gradient(135deg, #34C759, #32D74B);\n  color: #FFFFFF;\n}\n\n.action-button.share {\n  background: linear-gradient(135deg, #3846cd, #2c3aa0);\n  color: #FFFFFF;\n}\n\n.button-icon {\n  margin-right: 6px;\n}\n\n/* 自定义选项部分 */\n.customization-section {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n}\n\n.option-group {\n  margin-bottom: 20px;\n}\n\n.option-group:last-child {\n  margin-bottom: 0;\n}\n\n.option-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n  display: block;\n}\n\n.template-scroll {\n  width: 100%;\n  white-space: nowrap;\n}\n\n.template-list {\n  display: flex;\n  padding-bottom: 8px;\n}\n\n.template-item {\n  width: 80px;\n  height: 120px;\n  margin-right: 12px;\n  border-radius: 10px;\n  overflow: hidden;\n  border: 2px solid transparent;\n  position: relative;\n  transition: all 0.3s ease;\n  flex-shrink: 0;\n}\n\n.template-item.active {\n  border-color: #3846cd;\n  transform: scale(1.05);\n}\n\n.template-thumb {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.theme-options {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.theme-option {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  border: 2px solid transparent;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.theme-option.active {\n  border-color: #3846cd;\n  transform: scale(1.05);\n  box-shadow: 0 2px 8px rgba(56, 70, 205, 0.2);\n}\n\n.theme-check {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.toggle-option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.toggle-option:last-child {\n  border-bottom: none;\n}\n\n.toggle-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.custom-input {\n  width: 100%;\n  height: 44px;\n  background-color: #F5F7FA;\n  border-radius: 12px;\n  padding: 0 16px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid transparent;\n}\n\n.custom-input:focus {\n  border-color: #3846cd;\n  background-color: #FFFFFF;\n}\n\n/* 内容预览 */\n.content-preview {\n  margin: 16px;\n  background-color: #FFFFFF;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);\n}\n\n.preview-info {\n  padding: 0;\n}\n\n.preview-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.preview-detail {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.detail-item {\n  font-size: 14px;\n  color: #666;\n  margin-right: 20px;\n  margin-bottom: 5px;\n}\n\n.price {\n  color: #ff4400;\n  font-weight: bold;\n}\n\n.original-price {\n  color: #999;\n  text-decoration: line-through;\n}\n\n/* 佣金信息 */\n.commission-info {\n  margin: 16px;\n}\n\n.commission-card {\n  background: linear-gradient(135deg, #fff8f0, #fff5e6);\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(255, 153, 0, 0.1);\n}\n\n.commission-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.commission-icon {\n  width: 24px;\n  height: 24px;\n  margin-right: 10px;\n}\n\n.commission-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #ff9900;\n}\n\n.commission-content {\n  display: flex;\n  justify-content: space-between;\n  padding: 5px 0;\n}\n\n.commission-rate, .commission-estimate {\n  font-size: 14px;\n  color: #ff6600;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/promotion/pages/promotion-tool.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "onMounted", "uni", "promotionService"], "mappings": ";;;;;;;;;;;AA6MA,MAAM,oBAAoB;;;;AAtC1B,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,YAAYA,cAAAA,IAAI,EAAE;AAGxB,UAAM,cAAcC,cAAAA,SAAS;AAAA,MAC3B,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA;AAAA,MAEb,WAAW;AAAA,MACX,aAAa;AAAA,MACb,eAAe;AAAA;AAAA,MAEf,OAAO;AAAA,MACP,eAAe;AAAA;AAAA,MAEf,UAAU;AAAA,MACV,MAAM;AAAA,MACN,WAAW;AAAA;AAAA,MAEX,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC;AAGD,UAAM,gBAAgBD,cAAAA,IAAI,KAAK;AAC/B,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAC5B,UAAM,sBAAsBA,cAAAA,IAAI,MAAM;AAGdA,kBAAG,IAAC,KAAK;AACjC,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,SAASA,cAAAA,IAAI,EAAE;AAMrB,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgB;AAAA,MACpB,WAAW;AAAA,QACT;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD,cAAc;AAAA,QACZ;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACF;AAAA,MACD,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACP;AAAA,MACF;AAAA,IACH;AAGA,UAAM,cAAc;AAAA,MAClB,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,MAC5C,EAAE,SAAS,WAAW,WAAW,UAAW;AAAA,IAC9C;AAGA,UAAM,qBAAqBA,cAAAA,IAAI,CAAC;AAChC,UAAM,gBAAgBA,cAAG,IAAC,YAAY,CAAC,CAAC;AACxC,UAAM,SAASA,cAAAA,IAAI,IAAI;AACvB,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,gBAAgBE,cAAQ,SAAC,MAAM;AACnC,UAAI,gBAAgB,MAAM,WAAW;AAAG,eAAO;AAC/C,aAAO,gBAAgB,MAAM,mBAAmB,KAAK,EAAE;AAAA,IACzD,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAEvC,kBAAY,QAAQ,QAAQ,QAAQ;AACpC,gBAAU,QAAQ,QAAQ,MAAM;AAGhC,YAAM,WAAWC,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,YAAY,SAAS,QAAQ;AAC/B,eAAO,QAAQ,SAAS;AAAA,MACzB;AAGD;AAGA;AAGA,iBAAW,MAAM;AACf;MACD,GAAE,IAAI;AAAA,IACT,CAAC;AAGD,UAAM,sBAAsB,MAAM;AAEhC,YAAM,wBAAwB,cAAc,YAAY,KAAK,KAAK,CAAA;AAGlE,sBAAgB,QAAQ;AAAA,QACtB,GAAG;AAAA,QACH,GAAG,gBAAgB,MAAM;AAAA,UAAO,cAC9B,SAAS,SAAS,aAAa,SAAS,SAAS,YAAY;AAAA,QAC9D;AAAA,MACL;AAAA,IACA;AAGA,UAAM,eAAe,CAAC,UAAU;AAC9B,yBAAmB,QAAQ;AAE3B,iBAAW,MAAM;AACf;MACD,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,cAAc,CAAC,UAAU;AAC7B,oBAAc,QAAQ;AAEtB,iBAAW,MAAM;AACf;MACD,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,WAAW,CAAC,MAAM;AACtB,aAAO,QAAQ,EAAE,OAAO;AAExB,iBAAW,MAAM;AACf;MACD,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,oBAAc,QAAQ,EAAE,OAAO;AAC/B,UAAI,cAAc,SAAS,CAAC,WAAW,OAAO;AAC5C;MACD;AAED,iBAAW,MAAM;AACf;MACD,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,qBAAqB,MAAM;AAE/B,UAAI,OAAO,iBAAiB;AAC1B,qBAAa,OAAO,eAAe;AAAA,MACpC;AACD,aAAO,kBAAkB,WAAW,MAAM;AACxC;MACD,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,sBAAsB,MAAM;AAChC,cAAO,YAAY,OAAK;AAAA,QACtB,KAAK;AACH,qBAAW,QAAQ,IAAI,YAAY,KAAK,IAAI,YAAY,SAAS,IAAI,YAAY,WAAW,SAAS,YAAY,aAAa;AAC9H;AAAA,QACF,KAAK;AACH,qBAAW,QAAQ,IAAI,YAAY,KAAK,QAAQ,YAAY,KAAK;AACjE;AAAA,QACF,KAAK;AACH,qBAAW,QAAQ,IAAI,YAAY,KAAK,IAAI,YAAY,QAAQ,IAAI,YAAY,IAAI,KAAK,YAAY,KAAK,GAAG,YAAY,SAAS;AAClI;AAAA,QACF;AACE,qBAAW,QAAQ,IAAI,YAAY,KAAK;AAAA,MAC3C;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,UAAI,CAAC,YAAY,SAAS,CAAC,UAAU,OAAO;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,mBAAW,MAAM;AACf;QACD,GAAE,IAAI;AACP;AAAA,MACD;AAGDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,cAAQ,YAAY,OAAK;AAAA,QACvB,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,QACF,KAAK;AACH;AACA;AAAA,QACF;AAEE;MACH;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAE5B,iBAAW,MAAM;AACf,oBAAY,OAAO;AACnB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,YAAY;AACxB,oBAAY,cAAc;AAC1B,oBAAY,gBAAgB;AAC5B,oBAAY,QAAQ;AAGpB,sBAAc,QAAQ;AACtB,uBAAe,QAAQ;AACvB,4BAAoB,QAAQ;AAE5BA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,kBAAkB,MAAM;AAE5B,iBAAW,MAAM;AACf,oBAAY,OAAO;AACnB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,gBAAgB;AAC5B,oBAAY,cAAc;AAG1B,sBAAc,QAAQ;AACtB,uBAAe,QAAQ;AACvB,4BAAoB,SAAS,YAAY,QAAQ,eAAe,QAAQ,KAAK,QAAQ,CAAC;AAEtFA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,mBAAmB,MAAM;AAE7B,iBAAW,MAAM;AACf,oBAAY,OAAO;AACnB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,WAAW;AACvB,oBAAY,UAAU;AACtB,oBAAY,cAAc;AAG1B,sBAAc,QAAQ;AACtB,uBAAe,QAAQ;AACvB,4BAAoB,QAAQ;AAE5BA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,gBAAgB,MAAM;AAE1B,iBAAW,MAAM;AACf,oBAAY,OAAO;AACnB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,WAAW;AACvB,oBAAY,OAAO;AACnB,oBAAY,QAAQ;AACpB,oBAAY,YAAY;AACxB,oBAAY,cAAc;AAG1B,sBAAc,QAAQ;AACtB,uBAAe,QAAQ;AACvB,4BAAoB,QAAQ;AAE5BA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,kBAAkB,MAAM;AAE5B,iBAAW,MAAM;AACf,oBAAY,OAAO;AACnB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,cAAc;AAG1B,sBAAc,QAAQ;AACtB,uBAAe,QAAQ;AACvB,4BAAoB,QAAQ;AAE5BA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,oBAAoB,MAAM;AAE9B,iBAAW,MAAM;AACf,oBAAY,OAAO;AACnB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,cAAc;AAG1B,sBAAc,QAAQ;AAEtBA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,mBAAmB,MAAM;AAE7B,iBAAW,MAAM;AACf,oBAAY,OAAO;AACnB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,cAAc;AAG1B,sBAAc,QAAQ;AACtB,uBAAe,QAAQ;AACvB,4BAAoB,QAAQ;AAE5BA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,sBAAsB,MAAM;AAEhC,iBAAW,MAAM;AACf,oBAAY,OAAO;AACnB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,cAAc;AAG1B,sBAAc,QAAQ;AAEtBA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,kBAAkB,MAAM;AAE5B,iBAAW,MAAM;AACf,oBAAY,OAAO,YAAY;AAC/B,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,cAAc;AAG1B,sBAAc,QAAQ;AAEtBA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO,YAAY;AAAA,QACnB,SAAS,WAAW,SAAS,YAAY,eAAe;AAAA,QACxD,UAAU,WAAW,SAAS,YAAY;AAAA,QAC1C,MAAMC,uBAAAA,iBAAiB,kBAAkB,YAAY,OAAO,UAAU,OAAO,OAAO,KAAK;AAAA,QACzF,SAAS,WAAW;AAClBD,wBAAAA,MAAA,MAAA,OAAA,yDAAY,MAAM;AAElBC,iCAAgB,iBAAC,gBAAgB,YAAY,OAAO,UAAU,OAAO,QAAQ;AAAA,QAC9E;AAAA,QACD,MAAM,WAAW;AACfD,wBAAAA,MAAA,MAAA,OAAA,yDAAY,MAAM;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,YAAY;AACjC,UAAI;AAEF,mBAAW,QAAQ;AAEnBA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,QACb,CAAK;AAGD,mBAAW,QAAQ,MAAMC,uBAAAA,iBAAiB;AAAA,UACxC,YAAY;AAAA,UACZ;AAAA,UACA,OAAO;AAAA,UACP;AAAA,YACE,UAAU,cAAc;AAAA,YACxB,OAAO,cAAc;AAAA,YACrB,QAAQ,OAAO;AAAA,YACf,YAAY,cAAc,QAAQ,WAAW,QAAQ;AAAA,UACtD;AAAA,QACP;AAEID,sBAAG,MAAC,YAAW;AAAA,MAChB,SAAQ,KAAK;AACZA,oGAAc,UAAU,GAAG;AAC3BA,sBAAG,MAAC,YAAW;AAGf,mBAAW,QAAQ,YAAY,SAAS;AAExCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,aAAa,YAAY;AAC7B,UAAI,CAAC,WAAW,OAAO;AAErB,cAAM,eAAc;AAAA,MACrB;AAED,UAAI,WAAW,OAAO;AACpB,YAAI;AACFA,wBAAAA,MAAI,YAAY;AAAA,YACd,OAAO;AAAA,UACf,CAAO;AAED,gBAAMC,wCAAiB,kBAAkB,WAAW,KAAK;AAEzDD,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF,SAAQ,KAAK;AACZA,wBAAc,MAAA,MAAA,SAAA,yDAAA,UAAU,GAAG;AAC3BA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACF;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACptBA,GAAG,WAAW,eAAe;"}