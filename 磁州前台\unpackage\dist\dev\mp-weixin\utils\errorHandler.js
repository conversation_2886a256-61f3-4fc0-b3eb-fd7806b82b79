"use strict";
const common_vendor = require("../common/vendor.js");
const setupGlobalErrorHandlers = () => {
  if (typeof window !== "undefined" && window.addEventListener) {
    window.addEventListener("unhandledrejection", (event) => {
      common_vendor.index.__f__("error", "at utils/errorHandler.js:202", "[全局未处理Promise错误]", event.reason);
      event.preventDefault();
    });
  }
  const reportError = (error) => {
    common_vendor.index.__f__("warn", "at utils/errorHandler.js:212", "[错误上报]", error);
  };
  return {
    reportError
  };
};
exports.setupGlobalErrorHandlers = setupGlobalErrorHandlers;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/errorHandler.js.map
