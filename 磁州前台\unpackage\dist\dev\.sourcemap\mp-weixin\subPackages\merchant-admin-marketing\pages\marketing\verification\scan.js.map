{"version": 3, "file": "scan.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/verification/scan.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1x2ZXJpZmljYXRpb25cc2Nhbi52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"scan-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">扫码核销</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 扫码区域 -->\r\n    <view class=\"scan-area\">\r\n      <view class=\"scan-box\">\r\n        <camera device-position=\"back\" flash=\"auto\" @error=\"handleCameraError\" class=\"scan-camera\"></camera>\r\n        <cover-image class=\"scan-frame\" src=\"/static/images/scan-frame.png\"></cover-image>\r\n        <cover-view class=\"scan-line\"></cover-view>\r\n      </view>\r\n      <text class=\"scan-tip\">请将核销码放入框内，即可自动扫描</text>\r\n    </view>\r\n    \r\n    <!-- 快捷操作 -->\r\n    <view class=\"quick-actions\">\r\n      <view class=\"action-btn\" @click=\"toggleFlash\">\r\n        <view class=\"action-icon\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n            <path d=\"M9 18h6M12 6V2M7.5 10.5L5 8M16.5 10.5L19 8M12 18a6 6 0 0 0 0-12 6 6 0 0 0 0 12z\"></path>\r\n          </svg>\r\n        </view>\r\n        <text class=\"action-text\">开启闪光灯</text>\r\n      </view>\r\n      <view class=\"action-btn\" @click=\"navigateToManual\">\r\n        <view class=\"action-icon\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n            <path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"></path>\r\n            <path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"></path>\r\n          </svg>\r\n        </view>\r\n        <text class=\"action-text\">手动输入</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 扫码成功弹窗 -->\r\n    <view class=\"verification-popup\" v-if=\"showVerificationPopup\">\r\n      <view class=\"popup-content\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">核销信息</text>\r\n          <view class=\"popup-close\" @click=\"closePopup\">×</view>\r\n        </view>\r\n        \r\n        <view class=\"verification-info\">\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">核销类型</text>\r\n            <text class=\"info-value\">{{verificationData.type}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">商品名称</text>\r\n            <text class=\"info-value\">{{verificationData.name}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">用户信息</text>\r\n            <text class=\"info-value\">{{verificationData.user}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">核销码</text>\r\n            <text class=\"info-value\">{{verificationData.code}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">有效期至</text>\r\n            <text class=\"info-value\">{{verificationData.expiry}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"verification-actions\">\r\n          <button class=\"btn-cancel\" @click=\"closePopup\">取消</button>\r\n          <button class=\"btn-confirm\" @click=\"confirmVerification\">确认核销</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      flashOn: false,\r\n      showVerificationPopup: false,\r\n      verificationData: {\r\n        type: '',\r\n        name: '',\r\n        user: '',\r\n        code: '',\r\n        expiry: ''\r\n      }\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 模拟扫码结果，实际项目中应该通过摄像头扫码获取\r\n    setTimeout(() => {\r\n      this.handleScanResult({\r\n        type: '拼团活动',\r\n        name: '双人下午茶套餐拼团',\r\n        user: '张三 (138****8888)',\r\n        code: 'GP20230618001',\r\n        expiry: '2023-06-25 23:59:59'\r\n      });\r\n    }, 3000);\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    showHelp() {\r\n      uni.showToast({\r\n        title: '扫码核销帮助',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    handleCameraError(e) {\r\n      uni.showToast({\r\n        title: '摄像头启动失败，请检查权限设置',\r\n        icon: 'none'\r\n      });\r\n      console.error('相机错误:', e);\r\n    },\r\n    toggleFlash() {\r\n      this.flashOn = !this.flashOn;\r\n      uni.showToast({\r\n        title: this.flashOn ? '闪光灯已开启' : '闪光灯已关闭',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    navigateToManual() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/verification/manual'\r\n      });\r\n    },\r\n    handleScanResult(result) {\r\n      this.verificationData = result;\r\n      this.showVerificationPopup = true;\r\n    },\r\n    closePopup() {\r\n      this.showVerificationPopup = false;\r\n    },\r\n    confirmVerification() {\r\n      uni.showLoading({\r\n        title: '核销中...'\r\n      });\r\n      \r\n      // 模拟核销请求\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '核销成功',\r\n          icon: 'success'\r\n        });\r\n        \r\n        // 关闭弹窗\r\n        this.closePopup();\r\n        \r\n        // 延迟返回上一页\r\n        setTimeout(() => {\r\n          uni.navigateBack();\r\n        }, 1500);\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.scan-container {\r\n  min-height: 100vh;\r\n  background-color: #000;\r\n  position: relative;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 扫码区域样式 */\r\n.scan-area {\r\n  padding: 30px 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.scan-box {\r\n  width: 280px;\r\n  height: 280px;\r\n  position: relative;\r\n  margin-bottom: 25px;\r\n  border-radius: 20px;\r\n  overflow: hidden;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.scan-camera {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.scan-frame {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.scan-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: linear-gradient(to right, transparent, #27ae60, transparent);\r\n  box-shadow: 0 0 10px #27ae60;\r\n  animation: scanAnimation 2s linear infinite;\r\n}\r\n\r\n@keyframes scanAnimation {\r\n  0% {\r\n    top: 0;\r\n  }\r\n  50% {\r\n    top: 100%;\r\n  }\r\n  100% {\r\n    top: 0;\r\n  }\r\n}\r\n\r\n.scan-tip {\r\n  color: #fff;\r\n  font-size: 15px;\r\n  margin-top: 25px;\r\n  text-align: center;\r\n  font-weight: 500;\r\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 快捷操作样式 */\r\n.quick-actions {\r\n  position: absolute;\r\n  bottom: 50px;\r\n  left: 0;\r\n  right: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 0 20px;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin: 0 35px;\r\n}\r\n\r\n.action-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 30px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  backdrop-filter: blur(10px);\r\n  -webkit-backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 10px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n  transition: transform 0.2s, box-shadow 0.2s;\r\n}\r\n\r\n.action-icon:active {\r\n  transform: scale(0.95);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.action-icon svg {\r\n  width: 28px;\r\n  height: 28px;\r\n  color: #fff;\r\n}\r\n\r\n.action-text {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 核销弹窗样式 */\r\n.verification-popup {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  backdrop-filter: blur(5px);\r\n  -webkit-backdrop-filter: blur(5px);\r\n  z-index: 999;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.popup-content {\r\n  width: 85%;\r\n  background: #fff;\r\n  border-radius: 20px;\r\n  overflow: hidden;\r\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 18px 20px;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n  background: #f8f8f8;\r\n}\r\n\r\n.popup-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.popup-close {\r\n  font-size: 22px;\r\n  color: #777;\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 18px;\r\n}\r\n\r\n.popup-close:active {\r\n  background: rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.verification-info {\r\n  padding: 20px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.info-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.info-label {\r\n  width: 90px;\r\n  font-size: 15px;\r\n  color: #777;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  font-size: 15px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.verification-actions {\r\n  display: flex;\r\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.btn-cancel, .btn-confirm {\r\n  flex: 1;\r\n  height: 56px;\r\n  line-height: 56px;\r\n  text-align: center;\r\n  font-size: 17px;\r\n  font-weight: 500;\r\n  border: none;\r\n  border-radius: 0;\r\n}\r\n\r\n.btn-cancel {\r\n  background: #f8f8f8;\r\n  color: #666;\r\n}\r\n\r\n.btn-confirm {\r\n  background: #27ae60;\r\n  color: #fff;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/verification/scan.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAqFA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,uBAAuB;AAAA,MACvB,kBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,eAAW,MAAM;AACf,WAAK,iBAAiB;AAAA,QACpB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACF,GAAE,GAAI;AAAA,EACR;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB,GAAG;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AACDA,oBAAA,MAAA,MAAA,SAAA,qFAAc,SAAS,CAAC;AAAA,IACzB;AAAA,IACD,cAAc;AACZ,WAAK,UAAU,CAAC,KAAK;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,UAAU,WAAW;AAAA,QACjC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,mBAAmB;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB,QAAQ;AACvB,WAAK,mBAAmB;AACxB,WAAK,wBAAwB;AAAA,IAC9B;AAAA,IACD,aAAa;AACX,WAAK,wBAAwB;AAAA,IAC9B;AAAA,IACD,sBAAsB;AACpBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAGD,aAAK,WAAU;AAGf,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzKA,GAAG,WAAW,eAAe;"}