<template>
  <view class="settings-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">设置中心</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 账户设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">账户设置</text>
        </view>
        
        <view class="settings-list">
          <view class="setting-item" @click="navigateToProfile">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </view>
            <text class="setting-title">个人资料</text>
            <view class="setting-value">
              <text class="value-text">完善个人信息</text>
            </view>
            <view class="setting-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>

          <view class="setting-item" @click="navigateToSecurity">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                <circle cx="12" cy="16" r="1"></circle>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
              </svg>
            </view>
            <text class="setting-title">账户安全</text>
            <view class="setting-value">
              <text class="value-text">密码、手机号</text>
            </view>
            <view class="setting-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>

          <view class="setting-item" @click="navigateToAddress">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
            </view>
            <text class="setting-title">收货地址</text>
            <view class="setting-value">
              <text class="value-text">管理收货地址</text>
            </view>
            <view class="setting-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>

      <!-- 通知设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">通知设置</text>
        </view>
        
        <view class="settings-list">
          <view class="setting-item">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
            </view>
            <text class="setting-title">推送通知</text>
            <view class="setting-value">
              <switch :checked="notificationSettings.push" @change="togglePushNotification" />
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
            </view>
            <text class="setting-title">邮件通知</text>
            <view class="setting-value">
              <switch :checked="notificationSettings.email" @change="toggleEmailNotification" />
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
              </svg>
            </view>
            <text class="setting-title">短信通知</text>
            <view class="setting-value">
              <switch :checked="notificationSettings.sms" @change="toggleSmsNotification" />
            </view>
          </view>
        </view>
      </view>

      <!-- 隐私设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">隐私设置</text>
        </view>
        
        <view class="settings-list">
          <view class="setting-item">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            </view>
            <text class="setting-title">个人信息可见性</text>
            <view class="setting-value">
              <switch :checked="privacySettings.profileVisible" @change="toggleProfileVisibility" />
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 12l2 2 4-4"></path>
                <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"></path>
              </svg>
            </view>
            <text class="setting-title">活动记录</text>
            <view class="setting-value">
              <switch :checked="privacySettings.activityVisible" @change="toggleActivityVisibility" />
            </view>
          </view>

          <view class="setting-item" @click="navigateToPrivacyPolicy">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14,2 14,8 20,8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10,9 9,9 8,9"></polyline>
              </svg>
            </view>
            <text class="setting-title">隐私政策</text>
            <view class="setting-value">
              <text class="value-text">查看详情</text>
            </view>
            <view class="setting-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">应用设置</text>
        </view>
        
        <view class="settings-list">
          <view class="setting-item" @click="showLanguageModal">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="2" y1="12" x2="22" y2="12"></line>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
              </svg>
            </view>
            <text class="setting-title">语言设置</text>
            <view class="setting-value">
              <text class="value-text">{{ currentLanguage }}</text>
            </view>
            <view class="setting-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="5"></circle>
                <line x1="12" y1="1" x2="12" y2="3"></line>
                <line x1="12" y1="21" x2="12" y2="23"></line>
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                <line x1="1" y1="12" x2="3" y2="12"></line>
                <line x1="21" y1="12" x2="23" y2="12"></line>
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
              </svg>
            </view>
            <text class="setting-title">深色模式</text>
            <view class="setting-value">
              <switch :checked="appSettings.darkMode" @change="toggleDarkMode" />
            </view>
          </view>

          <view class="setting-item" @click="clearCache">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="1 4 1 10 7 10"></polyline>
                <polyline points="23 20 23 14 17 14"></polyline>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
              </svg>
            </view>
            <text class="setting-title">清除缓存</text>
            <view class="setting-value">
              <text class="value-text">{{ cacheSize }}</text>
            </view>
            <view class="setting-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>

      <!-- 关于应用 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">关于应用</text>
        </view>
        
        <view class="settings-list">
          <view class="setting-item" @click="checkUpdate">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                <polyline points="12 22.81 12 17"></polyline>
              </svg>
            </view>
            <text class="setting-title">版本更新</text>
            <view class="setting-value">
              <text class="value-text">v{{ appVersion }}</text>
            </view>
            <view class="setting-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>

          <view class="setting-item" @click="navigateToAbout">
            <view class="setting-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
            </view>
            <text class="setting-title">关于我们</text>
            <view class="setting-value">
              <text class="value-text">了解更多</text>
            </view>
            <view class="setting-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="logout-section">
        <view class="logout-btn" @click="showLogoutModal">
          <text class="logout-text">退出登录</text>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 语言选择弹窗 -->
    <view class="language-modal" v-if="showLanguage" @click="hideLanguageModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择语言</text>
          <view class="close-btn" @click="hideLanguageModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        <view class="language-list">
          <view 
            class="language-item" 
            v-for="(lang, index) in languages" 
            :key="index"
            :class="{ active: currentLanguage === lang.name }"
            @click="selectLanguage(lang)"
          >
            <text class="language-name">{{ lang.name }}</text>
            <view class="language-check" v-if="currentLanguage === lang.name">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录确认弹窗 -->
    <view class="logout-modal" v-if="showLogout" @click="hideLogoutModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">确认退出</text>
        </view>
        <view class="modal-body">
          <text class="modal-text">确定要退出登录吗？</text>
        </view>
        <view class="modal-actions">
          <view class="action-btn cancel" @click="hideLogoutModal">
            <text class="btn-text">取消</text>
          </view>
          <view class="action-btn confirm" @click="confirmLogout">
            <text class="btn-text">确定</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const showLanguage = ref(false)
const showLogout = ref(false)
const currentLanguage = ref('简体中文')
const appVersion = ref('1.0.0')
const cacheSize = ref('25.6MB')

// 通知设置
const notificationSettings = ref({
  push: true,
  email: false,
  sms: true
})

// 隐私设置
const privacySettings = ref({
  profileVisible: true,
  activityVisible: false
})

// 应用设置
const appSettings = ref({
  darkMode: false
})

// 语言列表
const languages = ref([
  { name: '简体中文', code: 'zh-CN' },
  { name: '繁體中文', code: 'zh-TW' },
  { name: 'English', code: 'en' },
  { name: '日本語', code: 'ja' }
])

// 页面加载
onMounted(() => {
  console.log('设置中心页面加载')
  loadSettings()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function navigateToProfile() {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/user-profile/index'
  })
}

function navigateToSecurity() {
  uni.navigateTo({
    url: '/pages/settings/security'
  })
}

function navigateToAddress() {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/address/index'
  })
}

function navigateToPrivacyPolicy() {
  uni.navigateTo({
    url: '/pages/legal/privacy'
  })
}

function navigateToAbout() {
  uni.navigateTo({
    url: '/pages/about/index'
  })
}

function togglePushNotification(e) {
  notificationSettings.value.push = e.detail.value
  saveSettings()
}

function toggleEmailNotification(e) {
  notificationSettings.value.email = e.detail.value
  saveSettings()
}

function toggleSmsNotification(e) {
  notificationSettings.value.sms = e.detail.value
  saveSettings()
}

function toggleProfileVisibility(e) {
  privacySettings.value.profileVisible = e.detail.value
  saveSettings()
}

function toggleActivityVisibility(e) {
  privacySettings.value.activityVisible = e.detail.value
  saveSettings()
}

function toggleDarkMode(e) {
  appSettings.value.darkMode = e.detail.value
  saveSettings()
  
  // 这里可以实现深色模式切换逻辑
  if (e.detail.value) {
    uni.showToast({
      title: '深色模式已开启',
      icon: 'none'
    })
  } else {
    uni.showToast({
      title: '深色模式已关闭',
      icon: 'none'
    })
  }
}

function showLanguageModal() {
  showLanguage.value = true
}

function hideLanguageModal() {
  showLanguage.value = false
}

function selectLanguage(lang) {
  currentLanguage.value = lang.name
  hideLanguageModal()
  saveSettings()
  
  uni.showToast({
    title: `已切换到${lang.name}`,
    icon: 'success'
  })
}

function clearCache() {
  uni.showModal({
    title: '清除缓存',
    content: `确定要清除${cacheSize.value}的缓存数据吗？`,
    success: (res) => {
      if (res.confirm) {
        // 执行清除缓存逻辑
        cacheSize.value = '0MB'
        uni.showToast({
          title: '缓存已清除',
          icon: 'success'
        })
      }
    }
  })
}

function checkUpdate() {
  uni.showLoading({
    title: '检查更新中...'
  })
  
  // 模拟检查更新
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '已是最新版本',
      icon: 'success'
    })
  }, 2000)
}

function showLogoutModal() {
  showLogout.value = true
}

function hideLogoutModal() {
  showLogout.value = false
}

function confirmLogout() {
  hideLogoutModal()
  
  // 执行退出登录逻辑
  uni.showToast({
    title: '已退出登录',
    icon: 'success'
  })
  
  // 跳转到登录页面
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/login/index'
    })
  }, 1500)
}

function saveSettings() {
  // 保存设置到本地存储
  const settings = {
    notification: notificationSettings.value,
    privacy: privacySettings.value,
    app: appSettings.value,
    language: currentLanguage.value
  }
  
  uni.setStorageSync('userSettings', settings)
}

function loadSettings() {
  // 从本地存储加载设置
  try {
    const settings = uni.getStorageSync('userSettings')
    if (settings) {
      notificationSettings.value = settings.notification || notificationSettings.value
      privacySettings.value = settings.privacy || privacySettings.value
      appSettings.value = settings.app || appSettings.value
      currentLanguage.value = settings.language || currentLanguage.value
    }
  } catch (e) {
    console.error('加载设置失败:', e)
  }
}
</script>

<style scoped>
/* 设置中心样式开始 */
.settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #424242 0%, #212121 100%);
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(66, 66, 66, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
}

.back-btn svg {
  color: white;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
  height: 100vh;
}

/* 设置区块样式 */
.settings-section {
  margin: 20px 16px;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section-header {
  padding: 16px 20px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.settings-list {
  padding: 0;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f8f9fa;
  transition: all 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background: #f8f9fa;
}

.setting-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #424242;
  border-radius: 16px;
}

.setting-icon svg {
  color: white;
}

.setting-title {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.setting-value {
  display: flex;
  align-items: center;
}

.value-text {
  font-size: 14px;
  color: #666;
}

.setting-arrow svg {
  color: #999;
}

/* 退出登录区域样式 */
.logout-section {
  margin: 20px 16px;
}

.logout-btn {
  width: 100%;
  padding: 16px;
  background: #F44336;
  border-radius: 12px;
  text-align: center;
}

.logout-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

/* 弹窗样式 */
.language-modal, .logout-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 70vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.close-btn svg {
  color: #666;
}

/* 语言列表样式 */
.language-list {
  padding: 0 20px 20px;
  max-height: 50vh;
  overflow-y: auto;
}

.language-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.language-item:last-child {
  border-bottom: none;
}

.language-item.active {
  color: #424242;
}

.language-name {
  font-size: 16px;
  color: #333;
}

.language-item.active .language-name {
  color: #424242;
  font-weight: 500;
}

.language-check svg {
  color: #424242;
}

/* 退出登录弹窗样式 */
.modal-body {
  padding: 20px;
  text-align: center;
}

.modal-text {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
}

.action-btn.cancel {
  background: #f5f5f5;
}

.action-btn.confirm {
  background: #F44336;
}

.btn-text {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.action-btn.confirm .btn-text {
  color: white;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 设置中心样式结束 */
</style>
