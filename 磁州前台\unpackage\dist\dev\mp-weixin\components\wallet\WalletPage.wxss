
.wallet-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
  background: linear-gradient(135deg, #0066FF, #0052CC); /* 默认使用与"我的"页面一致的渐变色 */
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
}

/* 余额卡片 */
.balance-card {
  margin: 0 30rpx;
  padding: 40rpx;
  border-radius: 30rpx;
  color: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.15);
}
.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin: 0 0 20rpx 0;
}
.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin: 0 0 40rpx 0;
}
.balance-buttons {
  display: flex;
  justify-content: space-around;
  margin: 30rpx 0 0 0;
}
.balance-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0;
}
.withdraw-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.5);
}
.recharge-btn {
  background-color: #fff;
  color: #0052CC;
}

/* 钱包功能区 */
.wallet-functions {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.function-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}
.function-item:last-child {
  border-bottom: none;
}
.function-left {
  display: flex;
  align-items: center;
}
.function-icon-block {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}
.function-name {
  font-size: 28rpx;
  color: #333;
}
.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  transform: rotate(90deg);
}

/* 交易记录区域 */
.transaction-section {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding-bottom: 20rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}
.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}
.section-more {
  font-size: 24rpx;
  color: #666;
}
.transaction-list {
  padding: 0 30rpx;
}
.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.transaction-item:last-child {
  border-bottom: none;
}
.transaction-left {
  display: flex;
  flex-direction: column;
}
.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.transaction-time {
  font-size: 24rpx;
  color: #999;
}
.transaction-amount {
  font-size: 32rpx;
  font-weight: 500;
}
.income {
  color: #ff6b00;
}
.expense {
  color: #333;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
