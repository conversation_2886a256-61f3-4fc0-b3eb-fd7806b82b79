<template>
  <BaseInfoCard :item="item">
    <template #content>
      <view class="secondhand-details">
        <!-- 商品基本信息 -->
        <view class="product-basic-info">
          <view class="product-condition" v-if="item.condition">
            <view class="product-icon condition-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </view>
            <text class="product-text">{{item.condition}}</text>
          </view>
          
          <view class="product-brand" v-if="item.brand">
            <view class="product-icon brand-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                <line x1="7" y1="7" x2="7.01" y2="7"></line>
              </svg>
            </view>
            <text class="product-text">{{item.brand}}</text>
          </view>
          
          <view class="product-age" v-if="item.age">
            <view class="product-icon age-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
            </view>
            <text class="product-text">{{item.age}}</text>
          </view>
          
          <view class="product-transaction" v-if="item.transactionType">
            <view class="product-icon transaction-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </view>
            <text class="product-text">{{item.transactionType}}</text>
          </view>
        </view>
        
        <!-- 价格对比 -->
        <view class="price-comparison" v-if="item.originalPrice">
          <view class="price-tag original-price">
            <text class="price-label">原价</text>
            <text class="price-value original-value">¥{{item.originalPrice}}</text>
          </view>
          <view class="price-tag current-price">
            <text class="price-label">现价</text>
            <text class="price-value current-value">¥{{item.price}}</text>
          </view>
          <view class="price-tag discount-rate" v-if="calculateDiscount(item.price, item.originalPrice)">
            <text class="price-value discount-value">{{calculateDiscount(item.price, item.originalPrice)}}折</text>
          </view>
        </view>
        
        <!-- 商品描述 -->
        <view class="product-description" v-if="item.description">
          <text class="description-text">{{item.description}}</text>
        </view>
        
        <!-- 商品标签 -->
        <view class="product-tags" v-if="item.tags && item.tags.length">
          <view class="product-tag" v-for="(tag, index) in item.tags" :key="index">
            <text class="tag-text">{{tag}}</text>
          </view>
        </view>
        
        <!-- 交易方式 -->
        <view class="transaction-methods" v-if="filteredTransactionMethods.length">
          <view class="transaction-method" v-for="(method, index) in filteredTransactionMethods" :key="index">
            <view class="method-icon" :class="getMethodIconClass(method)">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </view>
            <text class="method-text">{{method}}</text>
          </view>
        </view>
      </view>
    </template>
  </BaseInfoCard>
</template>

<script setup>
import { computed } from 'vue';
import BaseInfoCard from './BaseInfoCard.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

// 过滤掉与tags重复的交易方式
const filteredTransactionMethods = computed(() => {
  if (!props.item.transactionMethods || !props.item.transactionMethods.length) return [];
  if (!props.item.tags || !props.item.tags.length) return props.item.transactionMethods;
  
  // 将tags转换为小写，用于不区分大小写的比较
  const tagsLowerCase = props.item.tags.map(tag => tag.toLowerCase());
  
  // 过滤transactionMethods，去除与tags重复的项
  return props.item.transactionMethods.filter(method => 
    !tagsLowerCase.includes(method.toLowerCase())
  );
});

function getMethodIconClass(method) {
  const methodMap = {
    '自提': 'pickup-icon',
    '邮寄': 'delivery-icon',
    '同城配送': 'local-icon',
    '见面交易': 'face-icon',
    '可议价': 'negotiate-icon'
  };
  
  return methodMap[method] || 'default-icon';
}

function calculateDiscount(currentPrice, originalPrice) {
  if (!currentPrice || !originalPrice || originalPrice <= 0) return null;
  const discount = (currentPrice / originalPrice * 10).toFixed(1);
  return discount < 10 ? discount : null; // 只显示打折的情况
}
</script>

<style scoped>
.secondhand-details {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

/* 商品基本信息 */
.product-basic-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.product-condition, .product-brand, .product-age, .product-transaction {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.product-icon {
  margin-right: 8rpx;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.condition-icon {
  color: #34C759;
}

.brand-icon {
  color: #FF9500;
}

.age-icon {
  color: #5856D6;
}

.transaction-icon {
  color: #FF3B30;
}

.product-text {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
}

/* 价格对比 */
.price-comparison {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
  background: rgba(0, 0, 0, 0.02);
  padding: 12rpx 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.price-tag {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.price-label {
  font-size: 20rpx;
  color: #8e8e93;
  margin-bottom: 4rpx;
}

.price-value {
  font-size: 26rpx;
  font-weight: 600;
}

.original-value {
  color: #8e8e93;
  text-decoration: line-through;
}

.current-value {
  color: #FF3B30;
  font-size: 30rpx;
}

.discount-rate {
  margin-left: auto;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
}

.discount-value {
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 700;
}

/* 商品描述 */
.product-description {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 16rpx;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.description-text {
  font-size: 26rpx;
  color: #636366;
  line-height: 1.5;
}

/* 商品标签 */
.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.product-tag {
  background: linear-gradient(135deg, rgba(88, 86, 214, 0.1), rgba(0, 122, 255, 0.1));
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid rgba(88, 86, 214, 0.2);
}

.tag-text {
  font-size: 22rpx;
  color: #5856D6;
  font-weight: 500;
}

/* 交易方式 */
.transaction-methods {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.transaction-method {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
}

.method-icon {
  margin-right: 6rpx;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pickup-icon {
  color: #34C759;
}

.delivery-icon {
  color: #007AFF;
}

.local-icon {
  color: #5856D6;
}

.face-icon {
  color: #FF9500;
}

.negotiate-icon {
  color: #FF3B30;
}

.method-text {
  font-size: 22rpx;
  color: #636366;
}

/* 车辆特殊信息 */
.vehicle-info {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.vehicle-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.vehicle-spec {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 12rpx 20rpx;
  border-radius: 16rpx;
  flex: 1;
  min-width: 120rpx;
}

.spec-label {
  font-size: 20rpx;
  color: #8e8e93;
  margin-bottom: 6rpx;
}

.spec-value {
  font-size: 26rpx;
  color: #1c1c1e;
  font-weight: 600;
}

.mileage-value {
  color: #FF9500;
}

.year-value {
  color: #5856D6;
}

.license-value {
  color: #007AFF;
}
</style> 