/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-db5f4e62, html.data-v-db5f4e62, #app.data-v-db5f4e62, .index-container.data-v-db5f4e62 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.franchise-container.data-v-db5f4e62 {
  padding-bottom: 120rpx;
  min-height: 100vh;
  background-color: #F5F7FA;
}
.safe-area.data-v-db5f4e62 {
  height: 20rpx;
  width: 100%;
}
.safe-area-top.data-v-db5f4e62 {
  height: 180rpx;
  width: 100%;
}
.franchise-card.data-v-db5f4e62 {
  margin: 30rpx;
  border-radius: 20rpx;
  background: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.intro-card.data-v-db5f4e62 {
  background: linear-gradient(135deg, #FFFFFF, #F9FCFF);
  border: 1px solid rgba(22, 119, 255, 0.1);
  margin-top: 30rpx;
  /* 调整顶部边距 */
}
.intro-header.data-v-db5f4e62 {
  margin-bottom: 30rpx;
}
.intro-title.data-v-db5f4e62 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.title-text.data-v-db5f4e62 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}
.title-tag.data-v-db5f4e62 {
  margin-left: 20rpx;
  font-size: 22rpx;
  color: #FF6B00;
  background-color: rgba(255, 107, 0, 0.1);
  padding: 4rpx 14rpx;
  border-radius: 4rpx;
}
.intro-subtitle.data-v-db5f4e62 {
  font-size: 26rpx;
  color: #666666;
}
.intro-content.data-v-db5f4e62 {
  display: flex;
  flex-direction: column;
}
.intro-item.data-v-db5f4e62 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #F5F5F5;
}
.intro-item.data-v-db5f4e62:last-child {
  border-bottom: none;
}
.intro-icon.data-v-db5f4e62 {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  background-color: rgba(22, 119, 255, 0.06);
  padding: 15rpx;
  border-radius: 50%;
}
.intro-info.data-v-db5f4e62 {
  flex: 1;
}
.intro-name.data-v-db5f4e62 {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 6rpx;
}
.intro-desc.data-v-db5f4e62 {
  font-size: 24rpx;
  color: #999999;
}
.region-header.data-v-db5f4e62 {
  margin-bottom: 30rpx;
}
.region-title.data-v-db5f4e62 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 10rpx;
}
.region-subtitle.data-v-db5f4e62 {
  font-size: 24rpx;
  color: #999999;
}
.region-selector.data-v-db5f4e62 {
  padding: 20rpx;
  background-color: #F8F9FC;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}
.selector-row.data-v-db5f4e62 {
  display: flex;
  margin-bottom: 20rpx;
}
.selector-item.data-v-db5f4e62 {
  flex: 1;
  margin-right: 20rpx;
}
.selector-item.data-v-db5f4e62:last-child {
  margin-right: 0;
}
.full-width.data-v-db5f4e62 {
  width: 100%;
}
.selector-label.data-v-db5f4e62 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-weight: 500;
}
.selector-picker.data-v-db5f4e62 {
  background-color: #fff;
  height: 80rpx;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  border: 1px solid #EAEDF2;
}
.picker-value.data-v-db5f4e62 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: #333;
  font-size: 28rpx;
}
.picker-value.disabled.data-v-db5f4e62 {
  color: #bbb;
}
.picker-value text.data-v-db5f4e62:last-child {
  color: #999;
  font-size: 24rpx;
}

/* 新增区域图示 */
.region-map.data-v-db5f4e62 {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}
.map-title.data-v-db5f4e62 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
}
.map-title-dot.data-v-db5f4e62 {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background-color: #0066FF;
  margin-right: 10rpx;
}
.map-image.data-v-db5f4e62 {
  width: 100%;
  height: 300rpx;
  background-color: #F8F9FC;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

/* 状态指示器样式 */
.status-indicators.data-v-db5f4e62 {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding: 0 20rpx;
}
.status-item.data-v-db5f4e62 {
  display: flex;
  align-items: center;
}
.status-dot.data-v-db5f4e62 {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}
.available-dot.data-v-db5f4e62 {
  background-color: #34C759;
}
.pending-dot.data-v-db5f4e62 {
  background-color: #FF9500;
}
.occupied-dot.data-v-db5f4e62 {
  background-color: #FF3B30;
}
.status-text.data-v-db5f4e62 {
  font-size: 24rpx;
  color: #666;
}
.selected-region.data-v-db5f4e62 {
  padding: 30rpx;
  background-color: #F8FCFF;
  border-radius: 12rpx;
  border: 1px solid rgba(0, 102, 255, 0.1);
  margin-top: 20rpx;
}
.region-info.data-v-db5f4e62 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.region-name.data-v-db5f4e62 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.region-status.data-v-db5f4e62 {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.available.data-v-db5f4e62 {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.pending.data-v-db5f4e62 {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
.occupied.data-v-db5f4e62 {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}
.region-data.data-v-db5f4e62 {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}
.data-box.data-v-db5f4e62 {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}
.data-value.data-v-db5f4e62 {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 4rpx;
}
.data-label.data-v-db5f4e62 {
  font-size: 24rpx;
  color: #999;
}
.card-header.data-v-db5f4e62 {
  margin-bottom: 20rpx;
}
.card-title.data-v-db5f4e62 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}
.card-title.data-v-db5f4e62::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background-color: #1677FF;
  border-radius: 3rpx;
}
.requirements-list.data-v-db5f4e62 {
  padding: 10rpx 0;
}
.requirement-item.data-v-db5f4e62 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.requirement-item.data-v-db5f4e62:last-child {
  margin-bottom: 0;
}
.requirement-dot.data-v-db5f4e62 {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #1677FF;
  margin-top: 10rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.requirement-text.data-v-db5f4e62 {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}
.process-steps.data-v-db5f4e62 {
  padding: 20rpx 0;
}
.process-step.data-v-db5f4e62 {
  display: flex;
  align-items: flex-start;
  position: relative;
  padding-bottom: 40rpx;
}
.process-step.data-v-db5f4e62:last-child {
  padding-bottom: 0;
}
.step-circle.data-v-db5f4e62 {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #1677FF;
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  position: relative;
  z-index: 2;
}
.step-line.data-v-db5f4e62 {
  position: absolute;
  left: 25rpx;
  top: 50rpx;
  width: 2rpx;
  height: calc(100% - 50rpx);
  background-color: #E5E5E5;
  z-index: 1;
}
.step-content.data-v-db5f4e62 {
  flex: 1;
}
.step-title.data-v-db5f4e62 {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 6rpx;
}
.step-desc.data-v-db5f4e62 {
  font-size: 24rpx;
  color: #999999;
}
.bottom-actions.data-v-db5f4e62 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.action-btn.data-v-db5f4e62 {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
}
.action-btn.consult-btn.data-v-db5f4e62 {
  flex: 1;
  background-color: #F5F7FA;
  color: #666666;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-btn.consult-btn .btn-icon.data-v-db5f4e62 {
  margin-right: 8rpx;
}
.action-btn.apply-btn.data-v-db5f4e62 {
  flex: 2;
  background: linear-gradient(90deg, #1677FF, #4F9DFF);
  color: #FFFFFF;
}
.action-btn.apply-btn[disabled].data-v-db5f4e62 {
  background: linear-gradient(90deg, #CCCCCC, #E5E5E5);
  color: #FFFFFF;
  opacity: 1;
}

/* 修改自定义导航栏样式，匹配发布页 */
.data-v-db5f4e62 .bg-gradient-blue {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.data-v-db5f4e62 .cu-custom .cu-bar {
  height: 88rpx !important;
  padding-top: 44px !important;
  /* 状态栏高度 */
}
.data-v-db5f4e62 .cu-custom .content {
  top: calc(44px + 40rpx) !important;
  /* 调整标题位置上移 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
}
.data-v-db5f4e62 .cu-custom .action {
  margin-top: 8rpx !important;
  /* 调整关闭键位置与标题对齐 */
}
.data-v-db5f4e62 .cu-bar .action:first-child {
  margin-left: 30rpx;
  font-size: 30rpx;
}

/* 导航栏样式 */
.navbar.data-v-db5f4e62 {
  position: relative;
  z-index: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义导航栏 */
.custom-navbar.data-v-db5f4e62 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-title.data-v-db5f4e62 {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: "AlimamaShuHeiTi", sans-serif;
  text-align: center;
}
.navbar-right.data-v-db5f4e62 {
  width: 40rpx;
  height: 40rpx;
}
.navbar-left.data-v-db5f4e62 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20;
  /* 确保在标题上层，可以被点击 */
}
.back-icon.data-v-db5f4e62 {
  width: 100%;
  height: 100%;
}