"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "近7天",
      stats: {
        totalSent: 1258,
        totalAmount: 3652.5,
        totalReceived: 986,
        conversionRate: 78.4
      },
      chartData: [
        { date: "周一", sent: 60, received: 45 },
        { date: "周二", sent: 70, received: 55 },
        { date: "周三", sent: 80, received: 65 },
        { date: "周四", sent: 90, received: 75 },
        { date: "周五", sent: 100, received: 85 },
        { date: "周六", sent: 85, received: 70 },
        { date: "周日", sent: 75, received: 60 }
      ],
      recentActivities: [
        {
          id: 1,
          name: "新人专享红包",
          status: "active",
          statusText: "进行中",
          time: "2023-06-01 ~ 2023-06-30",
          sentCount: 356,
          receivedCount: 289,
          icon: "新",
          iconBg: "rgba(255, 77, 79, 0.1)",
          iconColor: "#FF4D4F"
        },
        {
          id: 2,
          name: "618购物节红包雨",
          status: "active",
          statusText: "进行中",
          time: "2023-06-10 ~ 2023-06-18",
          sentCount: 520,
          receivedCount: 412,
          icon: "雨",
          iconBg: "rgba(24, 144, 255, 0.1)",
          iconColor: "#1890FF"
        },
        {
          id: 3,
          name: "五一劳动节红包",
          status: "ended",
          statusText: "已结束",
          time: "2023-05-01 ~ 2023-05-05",
          sentCount: 382,
          receivedCount: 285,
          icon: "节",
          iconBg: "rgba(82, 196, 26, 0.1)",
          iconColor: "#52C41A"
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showDatePicker() {
      common_vendor.index.showActionSheet({
        itemList: ["今日", "近7天", "近30天", "自定义"],
        success: (res) => {
          const options = ["今日", "近7天", "近30天", "自定义"];
          this.dateRange = options[res.tapIndex];
          if (res.tapIndex === 3) {
            common_vendor.index.showToast({
              title: "打开日期选择器",
              icon: "none"
            });
          } else {
            this.loadData(options[res.tapIndex]);
          }
        }
      });
    },
    loadData(timeRange) {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: `已加载${timeRange}数据`,
          icon: "success"
        });
      }, 500);
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
  }
};
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  (_component_circle + _component_path + _component_svg + _component_rect + _component_line + _component_polyline + _component_polygon)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "3"
    }),
    c: common_vendor.p({
      d: "M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
    }),
    d: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    e: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings")),
    f: common_vendor.t($data.dateRange),
    g: common_vendor.p({
      x: "3",
      y: "4",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    h: common_vendor.p({
      x1: "16",
      y1: "2",
      x2: "16",
      y2: "6"
    }),
    i: common_vendor.p({
      x1: "8",
      y1: "2",
      x2: "8",
      y2: "6"
    }),
    j: common_vendor.p({
      x1: "3",
      y1: "10",
      x2: "21",
      y2: "10"
    }),
    k: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "16",
      height: "16",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    l: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    m: common_vendor.t($data.stats.totalSent),
    n: common_vendor.t($data.stats.totalAmount),
    o: common_vendor.t($data.stats.totalReceived),
    p: common_vendor.t($data.stats.conversionRate),
    q: common_vendor.f($data.chartData, (item, index, i0) => {
      return {
        a: item.received + "%",
        b: item.sent + "%",
        c: common_vendor.t(item.date),
        d: index
      };
    }),
    r: common_vendor.p({
      x: "3",
      y: "3",
      width: "18",
      height: "18",
      rx: "2",
      ry: "2"
    }),
    s: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12",
      y2: "16"
    }),
    t: common_vendor.p({
      x1: "8",
      y1: "12",
      x2: "16",
      y2: "12"
    }),
    v: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    w: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create")),
    x: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
    }),
    y: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4"
    }),
    z: common_vendor.p({
      d: "M23 21v-2a4 4 0 0 0-3-3.87"
    }),
    A: common_vendor.p({
      d: "M16 3.13a4 4 0 0 1 0 7.75"
    }),
    B: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#1890FF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    C: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/mass-sending")),
    D: common_vendor.p({
      d: "M20 16.2A4.5 4.5 0 0 0 17.5 8h-1.8A7 7 0 1 0 4 14.9"
    }),
    E: common_vendor.p({
      d: "M16 14v6"
    }),
    F: common_vendor.p({
      d: "M8 14v6"
    }),
    G: common_vendor.p({
      d: "M12 16v6"
    }),
    H: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FAAD14",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    I: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/red-rain")),
    J: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    K: common_vendor.p({
      d: "M8 14s1.5 2 4 2 4-2 4-2"
    }),
    L: common_vendor.p({
      x1: "9",
      y1: "9",
      x2: "9.01",
      y2: "9"
    }),
    M: common_vendor.p({
      x1: "15",
      y1: "9",
      x2: "15.01",
      y2: "9"
    }),
    N: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#722ED1",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    O: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission")),
    P: common_vendor.p({
      d: "M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
    }),
    Q: common_vendor.p({
      points: "14 2 14 8 20 8"
    }),
    R: common_vendor.p({
      x1: "16",
      y1: "13",
      x2: "8",
      y2: "13"
    }),
    S: common_vendor.p({
      x1: "16",
      y1: "17",
      x2: "8",
      y2: "17"
    }),
    T: common_vendor.p({
      points: "10 9 9 9 8 9"
    }),
    U: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#52C41A",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    V: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/template")),
    W: common_vendor.p({
      x1: "18",
      y1: "20",
      x2: "18",
      y2: "10"
    }),
    X: common_vendor.p({
      x1: "12",
      y1: "20",
      x2: "12",
      y2: "4"
    }),
    Y: common_vendor.p({
      x1: "6",
      y1: "20",
      x2: "6",
      y2: "14"
    }),
    Z: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "24",
      height: "24",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#F5222D",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    aa: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis")),
    ab: common_vendor.p({
      points: "9 18 15 12 9 6"
    }),
    ac: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "14",
      height: "14",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    ad: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis")),
    ae: common_vendor.f($data.recentActivities, (item, index, i0) => {
      return {
        a: common_vendor.t(item.icon),
        b: item.iconColor,
        c: item.iconBg,
        d: common_vendor.t(item.name),
        e: common_vendor.t(item.statusText),
        f: common_vendor.n("status-" + item.status),
        g: common_vendor.t(item.time),
        h: common_vendor.t(item.sentCount),
        i: common_vendor.t(item.receivedCount),
        j: index,
        k: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail?id=" + item.id), index)
      };
    }),
    af: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    ag: common_vendor.p({
      x1: "12",
      y1: "16",
      x2: "12",
      y2: "12"
    }),
    ah: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12.01",
      y2: "8"
    }),
    ai: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#FF4D4F",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    aj: common_vendor.o(($event) => $options.navigateTo("/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide")),
    ak: common_vendor.p({
      points: "12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
    }),
    al: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#1890FF",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    am: common_vendor.p({
      d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"
    }),
    an: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4"
    }),
    ao: common_vendor.p({
      d: "M23 21v-2a4 4 0 0 0-3-3.87"
    }),
    ap: common_vendor.p({
      d: "M16 3.13a4 4 0 0 1 0 7.75"
    }),
    aq: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      width: "20",
      height: "20",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "#52C41A",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fda7de77"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/tool.js.map
