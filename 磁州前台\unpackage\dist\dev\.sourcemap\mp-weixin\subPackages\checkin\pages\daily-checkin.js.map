{"version": 3, "file": "daily-checkin.js", "sources": ["subPackages/checkin/pages/daily-checkin.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2hlY2tpblxwYWdlc1xkYWlseS1jaGVja2luLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"checkin-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-left\">\r\n        <view class=\"back-button\" @tap=\"goBack\">\r\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M15 18L9 12L15 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      <view class=\"navbar-title\">\r\n        <text class=\"title-text\">每日签到</text>\r\n      </view>\r\n      <view class=\"navbar-right\">\r\n        <!-- 占位 -->\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 页面内容区域 -->\r\n    <scroll-view scroll-y class=\"content-area\">\r\n      <!-- 签到卡片 -->\r\n      <view class=\"checkin-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">每日签到</text>\r\n          <text class=\"card-subtitle\">连续签到{{continuousDays}}天</text>\r\n        </view>\r\n        \r\n        <view class=\"calendar-section\">\r\n          <view class=\"month-header\">\r\n            <text class=\"month-text\">{{currentYear}}年{{currentMonth}}月</text>\r\n          </view>\r\n          <view class=\"weekdays\">\r\n            <text class=\"weekday\" v-for=\"day in ['日', '一', '二', '三', '四', '五', '六']\" :key=\"day\">{{day}}</text>\r\n          </view>\r\n          <view class=\"days-grid\">\r\n            <view class=\"day-item\" v-for=\"(day, index) in calendarDays\" :key=\"index\"\r\n                  :class=\"{\r\n                    'empty': !day.date,\r\n                    'checked': day.checked,\r\n                    'today': day.isToday,\r\n                    'disabled': day.disabled\r\n                  }\">\r\n              <text class=\"day-number\" v-if=\"day.date\">{{day.date}}</text>\r\n              <view class=\"checked-mark\" v-if=\"day.checked\"></view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"checkin-button-container\">\r\n          <view class=\"checkin-button\" :class=\"{'disabled': hasCheckedToday}\" @tap=\"doCheckin\">\r\n            <text>{{hasCheckedToday ? '今日已签到' : '立即签到'}}</text>\r\n            <text class=\"points-text\" v-if=\"!hasCheckedToday\">+{{checkinPoints}}积分</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 签到规则 -->\r\n      <view class=\"rules-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">签到规则</text>\r\n        </view>\r\n        <view class=\"rules-content\">\r\n          <view class=\"rule-item\">\r\n            <text class=\"rule-number\">1.</text>\r\n            <text class=\"rule-text\">每日签到可获得{{checkinPoints}}积分</text>\r\n          </view>\r\n          <view class=\"rule-item\">\r\n            <text class=\"rule-number\">2.</text>\r\n            <text class=\"rule-text\">连续签到7天可额外获得{{weeklyBonus}}积分</text>\r\n          </view>\r\n          <view class=\"rule-item\">\r\n            <text class=\"rule-number\">3.</text>\r\n            <text class=\"rule-text\">连续签到30天可额外获得{{monthlyBonus}}积分</text>\r\n          </view>\r\n          <view class=\"rule-item\">\r\n            <text class=\"rule-number\">4.</text>\r\n            <text class=\"rule-text\">每日签到时间为00:00-23:59</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 签到记录 -->\r\n      <view class=\"records-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">签到记录</text>\r\n        </view>\r\n        <view class=\"records-list\">\r\n          <view class=\"record-item\" v-for=\"(record, index) in checkinRecords\" :key=\"index\">\r\n            <view class=\"record-date\">\r\n              <text class=\"date-text\">{{record.date}}</text>\r\n              <text class=\"time-text\">{{record.time}}</text>\r\n            </view>\r\n            <view class=\"record-points\">\r\n              <text class=\"points-value\">+{{record.points}}</text>\r\n              <text class=\"points-label\">积分</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部空间 -->\r\n      <view class=\"bottom-space\"></view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\n\r\n// 签到相关数据\r\nconst checkinPoints = ref(10); // 每日签到积分\r\nconst weeklyBonus = ref(30);   // 连续签到7天奖励\r\nconst monthlyBonus = ref(100); // 连续签到30天奖励\r\nconst continuousDays = ref(3); // 连续签到天数\r\nconst hasCheckedToday = ref(false); // 今天是否已签到\r\n\r\n// 日历相关\r\nconst currentDate = new Date();\r\nconst currentYear = ref(currentDate.getFullYear());\r\nconst currentMonth = ref(currentDate.getMonth() + 1);\r\nconst calendarDays = ref([]);\r\n\r\n// 生成日历数据\r\nconst generateCalendar = () => {\r\n  const year = currentYear.value;\r\n  const month = currentMonth.value;\r\n  const firstDay = new Date(year, month - 1, 1).getDay(); // 本月第一天是周几\r\n  const lastDate = new Date(year, month, 0).getDate(); // 本月最后一天是几号\r\n  \r\n  const days = [];\r\n  \r\n  // 填充本月第一天之前的空白\r\n  for (let i = 0; i < firstDay; i++) {\r\n    days.push({ date: null });\r\n  }\r\n  \r\n  // 填充本月的日期\r\n  const today = new Date();\r\n  const isCurrentMonth = today.getFullYear() === year && today.getMonth() + 1 === month;\r\n  const todayDate = today.getDate();\r\n  \r\n  for (let i = 1; i <= lastDate; i++) {\r\n    const isToday = isCurrentMonth && i === todayDate;\r\n    const isPast = isCurrentMonth ? i < todayDate : new Date(year, month - 1, i) < today;\r\n    \r\n    days.push({\r\n      date: i,\r\n      isToday,\r\n      disabled: !isPast && !isToday,\r\n      checked: isPast || (isToday && hasCheckedToday.value)\r\n    });\r\n  }\r\n  \r\n  // 确保日历总是显示6行\r\n  const totalCells = 42;\r\n  const remainingCells = totalCells - days.length;\r\n  \r\n  for (let i = 0; i < remainingCells; i++) {\r\n    days.push({ date: null });\r\n  }\r\n  \r\n  calendarDays.value = days;\r\n};\r\n\r\n// 签到记录\r\nconst checkinRecords = ref([\r\n  {\r\n    date: '2023-12-20',\r\n    time: '08:15',\r\n    points: 10\r\n  },\r\n  {\r\n    date: '2023-12-19',\r\n    time: '09:23',\r\n    points: 10\r\n  },\r\n  {\r\n    date: '2023-12-18',\r\n    time: '07:45',\r\n    points: 10\r\n  },\r\n  {\r\n    date: '2023-12-15',\r\n    time: '12:30',\r\n    points: 10\r\n  },\r\n  {\r\n    date: '2023-12-14',\r\n    time: '08:05',\r\n    points: 10\r\n  }\r\n]);\r\n\r\n// 执行签到\r\nconst doCheckin = () => {\r\n  if (hasCheckedToday.value) {\r\n    uni.showToast({\r\n      title: '今日已签到',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  // 模拟签到请求\r\n  uni.showLoading({\r\n    title: '签到中...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 更新签到状态\r\n    hasCheckedToday.value = true;\r\n    continuousDays.value += 1;\r\n    \r\n    // 更新日历\r\n    const todayIndex = calendarDays.value.findIndex(day => day.isToday);\r\n    if (todayIndex !== -1) {\r\n      calendarDays.value[todayIndex].checked = true;\r\n    }\r\n    \r\n    // 添加签到记录\r\n    const now = new Date();\r\n    const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;\r\n    const timeStr = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;\r\n    \r\n    checkinRecords.value.unshift({\r\n      date: dateStr,\r\n      time: timeStr,\r\n      points: checkinPoints.value\r\n    });\r\n    \r\n    // 显示签到成功\r\n    uni.showToast({\r\n      title: `签到成功，获得${checkinPoints.value}积分`,\r\n      icon: 'success'\r\n    });\r\n    \r\n    // 检查是否有额外奖励\r\n    if (continuousDays.value === 7) {\r\n      setTimeout(() => {\r\n        uni.showToast({\r\n          title: `连续签到7天，额外获得${weeklyBonus.value}积分`,\r\n          icon: 'success'\r\n        });\r\n      }, 1500);\r\n    } else if (continuousDays.value === 30) {\r\n      setTimeout(() => {\r\n        uni.showToast({\r\n          title: `连续签到30天，额外获得${monthlyBonus.value}积分`,\r\n          icon: 'success'\r\n        });\r\n      }, 1500);\r\n    }\r\n    \r\n    // 发送签到数据到服务器\r\n    // 实际应用中应该调用API\r\n  }, 1000);\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 页面加载\r\nonMounted(() => {\r\n  generateCalendar();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 页面容器 */\r\n.checkin-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  position: relative;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\r\n  color: white;\r\n  padding: 48px 20px 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);\r\n}\r\n\r\n.navbar-left {\r\n  width: 40px;\r\n}\r\n\r\n.back-button {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.title-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-area {\r\n  flex: 1;\r\n  padding: 16px;\r\n  box-sizing: border-box;\r\n  height: calc(100vh - 80px);\r\n}\r\n\r\n/* 签到卡片 */\r\n.checkin-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-header {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.card-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.card-subtitle {\r\n  font-size: 14px;\r\n  color: #FF7600;\r\n  margin-top: 4px;\r\n  display: block;\r\n}\r\n\r\n/* 日历部分 */\r\n.calendar-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.month-header {\r\n  text-align: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.month-text {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n}\r\n\r\n.weekdays {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.weekday {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 14px;\r\n  color: #999999;\r\n}\r\n\r\n.days-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.day-item {\r\n  width: 14.28%;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.day-number {\r\n  font-size: 14px;\r\n  color: #333333;\r\n}\r\n\r\n.empty {\r\n  visibility: hidden;\r\n}\r\n\r\n.checked {\r\n  position: relative;\r\n}\r\n\r\n.checked .day-number {\r\n  color: #FFFFFF;\r\n}\r\n\r\n.checked::before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 15px;\r\n  background-color: #FF7600;\r\n  z-index: -1;\r\n}\r\n\r\n.today:not(.checked) {\r\n  position: relative;\r\n}\r\n\r\n.today:not(.checked) .day-number {\r\n  color: #FF7600;\r\n  font-weight: 600;\r\n}\r\n\r\n.today:not(.checked)::before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 15px;\r\n  border: 1px solid #FF7600;\r\n  z-index: -1;\r\n}\r\n\r\n.disabled .day-number {\r\n  color: #CCCCCC;\r\n}\r\n\r\n/* 签到按钮 */\r\n.checkin-button-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.checkin-button {\r\n  width: 200px;\r\n  height: 50px;\r\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\r\n  border-radius: 25px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  box-shadow: 0 4px 12px rgba(255, 120, 0, 0.3);\r\n}\r\n\r\n.checkin-button.disabled {\r\n  background: #CCCCCC;\r\n  box-shadow: none;\r\n}\r\n\r\n.points-text {\r\n  font-size: 12px;\r\n  margin-top: 2px;\r\n}\r\n\r\n/* 规则卡片 */\r\n.rules-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.rules-content {\r\n  margin-top: 12px;\r\n}\r\n\r\n.rule-item {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.rule-number {\r\n  color: #FF7600;\r\n  margin-right: 8px;\r\n}\r\n\r\n.rule-text {\r\n  color: #666666;\r\n  flex: 1;\r\n}\r\n\r\n/* 记录卡片 */\r\n.records-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.records-list {\r\n  margin-top: 12px;\r\n}\r\n\r\n.record-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #F0F0F0;\r\n}\r\n\r\n.record-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.record-date {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.date-text {\r\n  font-size: 14px;\r\n  color: #333333;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.time-text {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n.record-points {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.points-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #FF7600;\r\n  margin-right: 4px;\r\n}\r\n\r\n.points-label {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n/* 底部空间 */\r\n.bottom-space {\r\n  height: 20px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/checkin/pages/daily-checkin.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;;;;AA+GA,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,eAAeA,cAAAA,IAAI,GAAG;AAC5B,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAC5B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AAGjC,UAAM,cAAc,oBAAI;AACxB,UAAM,cAAcA,cAAG,IAAC,YAAY,YAAa,CAAA;AACjD,UAAM,eAAeA,cAAAA,IAAI,YAAY,SAAU,IAAG,CAAC;AACnD,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAG3B,UAAM,mBAAmB,MAAM;AAC7B,YAAM,OAAO,YAAY;AACzB,YAAM,QAAQ,aAAa;AAC3B,YAAM,WAAW,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC,EAAE;AAC9C,YAAM,WAAW,IAAI,KAAK,MAAM,OAAO,CAAC,EAAE;AAE1C,YAAM,OAAO,CAAA;AAGb,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,aAAK,KAAK,EAAE,MAAM,KAAM,CAAA;AAAA,MACzB;AAGD,YAAM,QAAQ,oBAAI;AAClB,YAAM,iBAAiB,MAAM,kBAAkB,QAAQ,MAAM,SAAQ,IAAK,MAAM;AAChF,YAAM,YAAY,MAAM;AAExB,eAAS,IAAI,GAAG,KAAK,UAAU,KAAK;AAClC,cAAM,UAAU,kBAAkB,MAAM;AACxC,cAAM,SAAS,iBAAiB,IAAI,YAAY,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC,IAAI;AAE/E,aAAK,KAAK;AAAA,UACR,MAAM;AAAA,UACN;AAAA,UACA,UAAU,CAAC,UAAU,CAAC;AAAA,UACtB,SAAS,UAAW,WAAW,gBAAgB;AAAA,QACrD,CAAK;AAAA,MACF;AAGD,YAAM,aAAa;AACnB,YAAM,iBAAiB,aAAa,KAAK;AAEzC,eAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,aAAK,KAAK,EAAE,MAAM,KAAM,CAAA;AAAA,MACzB;AAED,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AAGD,UAAM,YAAY,MAAM;AACtB,UAAI,gBAAgB,OAAO;AACzBC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,wBAAgB,QAAQ;AACxB,uBAAe,SAAS;AAGxB,cAAM,aAAa,aAAa,MAAM,UAAU,SAAO,IAAI,OAAO;AAClE,YAAI,eAAe,IAAI;AACrB,uBAAa,MAAM,UAAU,EAAE,UAAU;AAAA,QAC1C;AAGD,cAAM,MAAM,oBAAI;AAChB,cAAM,UAAU,GAAG,IAAI,YAAa,CAAA,IAAI,OAAO,IAAI,SAAU,IAAG,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAC7H,cAAM,UAAU,GAAG,OAAO,IAAI,UAAU,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC;AAEvG,uBAAe,MAAM,QAAQ;AAAA,UAC3B,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ,cAAc;AAAA,QAC5B,CAAK;AAGDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,UAAU,cAAc,KAAK;AAAA,UACpC,MAAM;AAAA,QACZ,CAAK;AAGD,YAAI,eAAe,UAAU,GAAG;AAC9B,qBAAW,MAAM;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,cAAc,YAAY,KAAK;AAAA,cACtC,MAAM;AAAA,YAChB,CAAS;AAAA,UACF,GAAE,IAAI;AAAA,QACb,WAAe,eAAe,UAAU,IAAI;AACtC,qBAAW,MAAM;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,eAAe,aAAa,KAAK;AAAA,cACxC,MAAM;AAAA,YAChB,CAAS;AAAA,UACF,GAAE,IAAI;AAAA,QACR;AAAA,MAIF,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5QD,GAAG,WAAWC,SAAe;"}