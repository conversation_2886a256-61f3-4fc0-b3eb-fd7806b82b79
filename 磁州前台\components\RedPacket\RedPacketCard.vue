<template>
  <view class="red-packet-card" :class="{ 'is-expired': isExpired }">
    <view class="card-content" @click="handleClick">
      <!-- 红包封面 -->
      <view class="card-cover">
        <image class="cover-bg" src="/static/images/red-packet-cover.png" mode="aspectFill"></image>
        <view class="cover-content">
          <view class="sender-info">
            <image class="avatar" :src="redPacket.sender.avatar" mode="aspectFill"></image>
            <text class="nickname">{{ redPacket.sender.nickname }}</text>
          </view>
          
          <view class="packet-info">
            <text class="title">{{ redPacket.title }}</text>
            <view class="amount" v-if="!isExpired">
              <text class="label">剩余</text>
              <text class="value">{{ formatAmount(redPacket.remainAmount) }}元</text>
            </view>
            <view class="amount expired" v-else>
              <text>已过期</text>
            </view>
          </view>
          
          <view class="progress-bar">
            <view 
              class="progress-inner"
              :style="{ width: (redPacket.receivedCount / redPacket.totalCount * 100) + '%' }"
            ></view>
            <text class="progress-text">{{ redPacket.receivedCount }}/{{ redPacket.totalCount }}</text>
          </view>
        </view>
      </view>
      
      <!-- 红包操作区 -->
      <view class="card-actions">
        <view class="action-info">
          <text class="time">{{ formatRemainTime(redPacket.expireTime) }}</text>
          <text class="type">{{ getTypeText(redPacket.type) }}</text>
        </view>
        
        <button 
          class="action-btn"
          :class="{ 'is-expired': isExpired }"
          @click.stop="handleGrab"
          :disabled="isExpired"
        >
          {{ isExpired ? '已过期' : '抢红包' }}
        </button>
      </view>
    </view>
    
    <!-- 红包动画效果 -->
    <view class="card-animation" v-if="showAnimation">
      <view class="animation-content">
        <image class="animation-icon" src="/static/images/red-packet-animation.png" mode="aspectFit"></image>
        <text class="animation-text">恭喜发财</text>
      </view>
    </view>
  </view>
</template>

<script>
import { formatAmount } from '@/utils/format.js';
import { RED_PACKET_TYPE } from '@/utils/redPacket.js';
import { showShareMenu } from '@/utils/share';

export default {
  name: 'RedPacketCard',
  
  props: {
    redPacket: {
      type: Object,
      required: true
    }
  },
  
  data() {
    return {
      showAnimation: false
    };
  },
  
  computed: {
    // 是否已过期
    isExpired() {
      return Date.now() > this.redPacket.expireTime;
    }
  },
  
  methods: {
    // 处理点击
    handleClick() {
      this.$emit('view', this.redPacket);
    },
    
    // 处理抢红包
    handleGrab() {
      if (this.isExpired) return;
      
      // 显示动画
      this.showAnimation = true;
      setTimeout(() => {
        this.showAnimation = false;
        this.$emit('grab', this.redPacket);
      }, 1000);
    },
    
    // 格式化金额
    formatAmount,
    
    // 格式化剩余时间
    formatRemainTime(expireTime) {
      const now = Date.now();
      const remain = expireTime - now;
      
      if (remain <= 0) return '已过期';
      
      const hours = Math.floor(remain / (60 * 60 * 1000));
      const minutes = Math.floor((remain % (60 * 60 * 1000)) / (60 * 1000));
      
      if (hours > 0) {
        return `剩余${hours}小时${minutes}分钟`;
      } else {
        return `剩余${minutes}分钟`;
      }
    },
    
    // 获取红包类型文本
    getTypeText(type) {
      const typeMap = {
        [RED_PACKET_TYPE.RANDOM]: '拼手气红包',
        [RED_PACKET_TYPE.FIXED]: '普通红包'
      };
      return typeMap[type] || '红包';
    },
    
    async handleShare() {
      try {
        await showShareMenu({
          type: 'redPacket',
          data: this.redPacket
        });
        uni.showToast({
          title: '分享成功',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '分享失败',
          icon: 'error'
        });
      }
    }
  }
}
</script>

<style lang="scss">
.red-packet-card {
  position: relative;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
  
  &.is-expired {
    opacity: 0.8;
  }
  
  .card-content {
    background-color: #fff;
    
    .card-cover {
      position: relative;
      height: 300rpx;
      
      .cover-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
      
      .cover-content {
        position: relative;
        z-index: 1;
        height: 100%;
        padding: 30rpx;
        display: flex;
        flex-direction: column;
        
        .sender-info {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          
          .avatar {
            width: 64rpx;
            height: 64rpx;
            border-radius: 50%;
            border: 2rpx solid #fff;
            margin-right: 16rpx;
          }
          
          .nickname {
            font-size: 28rpx;
            color: #fff;
            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
          }
        }
        
        .packet-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          
          .title {
            font-size: 36rpx;
            color: #fff;
            font-weight: 500;
            margin-bottom: 20rpx;
            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
          }
          
          .amount {
            display: flex;
            align-items: baseline;
            margin-bottom: 20rpx;
            
            .label {
              font-size: 28rpx;
              color: #fff;
              margin-right: 8rpx;
              text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
            }
            
            .value {
              font-size: 48rpx;
              color: #fff;
              font-weight: 500;
              text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
            }
            
            &.expired {
              font-size: 32rpx;
              color: #fff;
              text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
            }
          }
        }
        
        .progress-bar {
          height: 4rpx;
          background-color: rgba(255, 255, 255, 0.3);
          border-radius: 2rpx;
          position: relative;
          
          .progress-inner {
            height: 100%;
            background-color: #fff;
            border-radius: 2rpx;
            transition: width 0.3s ease;
          }
          
          .progress-text {
            position: absolute;
            right: 0;
            top: 8rpx;
            font-size: 20rpx;
            color: #fff;
            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
    
    .card-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
      background-color: #fff;
      
      .action-info {
        .time {
          font-size: 24rpx;
          color: #999;
          margin-right: 16rpx;
        }
        
        .type {
          font-size: 24rpx;
          color: #666;
        }
      }
      
      .action-btn {
        font-size: 28rpx;
        color: #fff;
        background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
        border-radius: 32rpx;
        padding: 12rpx 32rpx;
        line-height: 1.5;
        
        &.is-expired {
          background: #f5f5f5;
          color: #999;
        }
      }
    }
  }
  
  .card-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease;
    
    .animation-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      animation: scaleIn 0.3s ease;
      
      .animation-icon {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 20rpx;
        animation: rotate 1s linear infinite;
      }
      
      .animation-text {
        font-size: 36rpx;
        color: #fff;
        font-weight: 500;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
  }
  to {
    transform: scale(1);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.action-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.grab-btn, .share-btn {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}

.grab-btn {
  background: linear-gradient(45deg, #ff4d4f, #ff7875);
  color: #fff;
}

.grab-btn[disabled] {
  background: #ccc;
  color: #fff;
}

.share-btn {
  background: #f5f5f5;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-btn .iconfont {
  margin-right: 6rpx;
  font-size: 24rpx;
}
</style> 