{"version": 3, "file": "refresh-package.js", "sources": ["pages/my/refresh-package.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvcmVmcmVzaC1wYWNrYWdlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"refresh-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">刷新套餐</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 已有刷新次数 - 改为卡片设计 -->\n    <view class=\"refresh-card\" :style=\"{ marginTop: (navbarHeight + 20) + 'px' }\">\n      <view class=\"refresh-card-content\">\n        <view class=\"refresh-card-left\" style=\"background-color: #ffffff;\">\n          <image class=\"refresh-big-icon\" src=\"/static/images/tabbar/套餐.png\"></image>\n        </view>\n        <view class=\"refresh-card-center\">\n          <view class=\"refresh-card-title\">已有刷新次数</view>\n          <view class=\"refresh-card-count\">{{ userRefreshCount }}</view>\n          <view class=\"refresh-card-expire\">有效期至：{{ expiryDate }}</view>\n        </view>\n        <view class=\"refresh-card-right\">\n          <button class=\"use-now-btn\" @click=\"navigateToPublish\">立即使用</button>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 说明区域 -->\n    <view class=\"info-card\">\n      <view class=\"info-title\">什么是刷新套餐？</view>\n      <view class=\"info-desc\">\n        <text>刷新套餐用于提升您发布的信息的排名，使信息优先展示在列表前端，增加曝光度；</text>\n        <text>购买刷新套餐后可以手动刷新您的发布内容，也可以设置自动刷新。</text>\n      </view>\n    </view>\n    \n    <!-- 套餐列表 -->\n    <view class=\"package-list\">\n      <view \n        class=\"package-item\" \n        v-for=\"(item, index) in packageList\" \n        :key=\"index\"\n        :class=\"{ active: selectedPackage === index }\"\n        @click=\"selectPackage(index)\"\n      >\n        <view class=\"package-select\">\n          <view class=\"select-dot\" :class=\"{ selected: selectedPackage === index }\"></view>\n        </view>\n        <view class=\"package-content\">\n          <view class=\"package-name\">{{ item.name }}</view>\n          <view class=\"package-details\">\n            <text class=\"package-count\">{{ item.count }}次</text>\n            <text class=\"package-validity\">有效期1年</text>\n          </view>\n          <view class=\"package-feature\" v-if=\"item.feature\">{{ item.feature }}</view>\n        </view>\n        <view class=\"package-price\">\n          <text class=\"price-symbol\">¥</text>\n          <text class=\"price-value\">{{ item.price }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 常见问题 -->\n    <view class=\"faq-section\">\n      <view class=\"section-title\">常见问题</view>\n      <view class=\"faq-item\" v-for=\"(item, index) in faqList\" :key=\"index\" @click=\"toggleFaq(index)\">\n        <view class=\"faq-header\">\n          <text class=\"faq-question\">{{ item.question }}</text>\n          <image class=\"faq-icon\" :class=\"{ 'faq-icon-open': item.isOpen }\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n        </view>\n        <view class=\"faq-answer\" v-if=\"item.isOpen\">{{ item.answer }}</view>\n      </view>\n    </view>\n    \n    <!-- 底部购买按钮 -->\n    <view class=\"bottom-bar\">\n      <view class=\"total-price\">\n        <text>总计：</text>\n        <text class=\"price-symbol\">¥</text>\n        <text class=\"total-value\">{{ selectedPrice }}</text>\n      </view>\n      <button class=\"buy-button\" @click=\"handleBuy\">立即购买</button>\n    </view>\n    \n    <!-- 客服悬浮按钮 -->\n    <fab-buttons :pageName=\"'refresh'\"></fab-buttons>\n  </view>\n</template>\n\n<script>\nimport { smartNavigate } from '@/utils/navigation.js';\nimport FabButtons from '@/components/FabButtons.vue';\n\nexport default {\n  components: {\n    FabButtons\n  },\n  data() {\n    return {\n      statusBarHeight: 20,\n      navbarHeight: 64,\n      selectedPackage: 1, // 默认选中第二个套餐\n      userRefreshCount: 33,\n      expiryDate: '2025-05-20', // 默认有效期\n      packageList: [\n        {\n          id: 'p1',\n          name: '体验套餐',\n          count: 10,\n          validity: 365,\n          price: 15.00,\n          feature: '适合初次尝试'\n        },\n        {\n          id: 'p2',\n          name: '热门套餐',\n          count: 30,\n          validity: 365,\n          price: 39.00,\n          feature: '最受欢迎'\n        },\n        {\n          id: 'p3',\n          name: '尊享套餐',\n          count: 100,\n          validity: 365,\n          price: 99.00,\n          feature: '性价比最高'\n        }\n      ],\n      faqList: [\n        {\n          question: '刷新后排名会提升多少？',\n          answer: '刷新后您的发布信息将立即显示在同类信息的前列，具体排名受时间、区域以及其他用户刷新情况影响。',\n          isOpen: false\n        },\n        {\n          question: '刷新次数可以退款吗？',\n          answer: '已购买的刷新次数不支持退款，建议根据自己的需求购买合适的套餐。',\n          isOpen: false\n        },\n        {\n          question: '一天可以刷新几次？',\n          answer: '同一条信息每天最多可刷新3次，每次刷新间隔需大于4小时。',\n          isOpen: false\n        },\n        {\n          question: '刷新次数过期了怎么办？',\n          answer: '刷新次数有效期为1年，建议在有效期内使用完毕。过期后需要重新购买套餐。',\n          isOpen: false\n        }\n      ]\n    }\n  },\n  computed: {\n    selectedPrice() {\n      return this.packageList[this.selectedPackage]?.price.toFixed(2) || '0.00';\n    }\n  },\n  onLoad() {\n    // 获取状态栏高度\n    const sysInfo = uni.getSystemInfoSync();\n    this.statusBarHeight = sysInfo.statusBarHeight;\n    this.navbarHeight = this.statusBarHeight + 44;\n    \n    // 获取用户当前刷新次数和有效期\n    this.getUserRefreshInfo();\n  },\n  methods: {\n    // 返回上一页\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    // 选择套餐\n    selectPackage(index) {\n      this.selectedPackage = index;\n    },\n    \n    // 切换FAQ显示状态\n    toggleFaq(index) {\n      this.faqList[index].isOpen = !this.faqList[index].isOpen;\n    },\n    \n    // 获取用户当前刷新次数和有效期\n    getUserRefreshInfo() {\n      // 模拟API请求\n      setTimeout(() => {\n        // 保留33次刷新，展示图片中的数值\n        this.userRefreshCount = 33;\n        \n        // 计算有效期（当前日期加一年）\n        const now = new Date();\n        const nextYear = new Date(now.setFullYear(now.getFullYear() + 1));\n        this.expiryDate = this.formatDate(nextYear);\n      }, 500);\n    },\n    \n    // 格式化日期为 YYYY-MM-DD\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n    \n    // 跳转到我的发布页面\n    navigateToPublish() {\n      smartNavigate('/pages/my/publish').catch(err => {\n        console.error('跳转到我的发布页面失败:', err);\n        uni.showToast({\n          title: '跳转失败，请重试',\n          icon: 'none'\n        });\n      });\n    },\n    \n    // 处理购买按钮点击\n    handleBuy() {\n      const selectedPackage = this.packageList[this.selectedPackage];\n      \n      uni.showModal({\n        title: '确认购买',\n        content: `您确定要购买【${selectedPackage.name}】吗？价格：¥${selectedPackage.price}`,\n        success: (res) => {\n          if (res.confirm) {\n            // 调用支付接口\n            this.processPurchase(selectedPackage);\n          }\n        }\n      });\n    },\n    \n    // 处理购买流程\n    processPurchase(packageInfo) {\n      // 显示加载提示\n      uni.showLoading({\n        title: '处理中...'\n      });\n      \n      // 模拟支付请求\n      setTimeout(() => {\n        uni.hideLoading();\n        \n        // 模拟支付成功\n        uni.showToast({\n          title: '购买成功',\n          icon: 'success',\n          duration: 2000\n        });\n        \n        // 更新刷新次数\n        this.userRefreshCount += packageInfo.count;\n        \n        // 可以跳转到支付成功页面或者留在当前页面\n      }, 1500);\n    }\n  }\n}\n</script>\n\n<style>\n.refresh-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 120rpx; /* 为底部按钮留出空间 */\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 44px;\n  background-color: #0052CC;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  padding: 0 15px;\n  z-index: 999;\n}\n\n.navbar-left {\n  width: 80rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.navbar-right {\n  width: 80rpx;\n}\n\n/* 刷新卡片 - 新设计 */\n.refresh-card {\n  margin: 30rpx;\n  border-radius: 20rpx;\n  background: linear-gradient(135deg, #0069FF, #0052DD, #0045CC);\n  box-shadow: 0 16rpx 32rpx rgba(0, 102, 255, 0.25);\n  overflow: hidden;\n  position: relative;\n}\n\n.refresh-card::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 120rpx;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));\n  border-radius: 20rpx 20rpx 0 0;\n  z-index: 1;\n}\n\n.refresh-card-content {\n  display: flex;\n  align-items: center;\n  padding: 30rpx;\n  position: relative;\n  z-index: 2;\n}\n\n.refresh-card-left {\n  margin-right: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 90rpx;\n  height: 90rpx;\n  background-color: rgba(255, 255, 255, 0.2);\n  border-radius: 45rpx;\n}\n\n.refresh-big-icon {\n  width: 54rpx;\n  height: 54rpx;\n}\n\n.refresh-card-center {\n  flex: 1;\n}\n\n.refresh-card-title {\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 8rpx;\n}\n\n.refresh-card-count {\n  font-size: 60rpx;\n  font-weight: bold;\n  color: #ffffff;\n  line-height: 1.2;\n}\n\n.refresh-card-expire {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.7);\n  margin-top: 8rpx;\n}\n\n.refresh-card-right {\n  margin-left: 20rpx;\n}\n\n.use-now-btn {\n  background-color: #ffffff;\n  color: #0066FF;\n  font-size: 28rpx;\n  font-weight: bold;\n  height: 70rpx;\n  line-height: 70rpx;\n  padding: 0 30rpx;\n  border-radius: 35rpx;\n  border: none;\n  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);\n  margin: 0;\n  min-width: 170rpx;\n  text-align: center;\n  transition: all 0.2s ease;\n}\n\n.use-now-btn:active {\n  transform: translateY(3rpx);\n  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.12);\n}\n\n/* 信息卡片 */\n.info-card {\n  background-color: #fff;\n  margin: 30rpx;\n  padding: 30rpx;\n  border-radius: 20rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.info-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.info-desc {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n.info-desc text {\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n/* 套餐列表 */\n.package-list {\n  margin: 30rpx;\n}\n\n.package-item {\n  display: flex;\n  background-color: #fff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);\n  border: 2rpx solid transparent;\n  transition: all 0.3s;\n}\n\n.package-item.active {\n  border-color: #0066FF;\n  background-color: #f0f6ff;\n}\n\n.package-select {\n  display: flex;\n  align-items: center;\n  margin-right: 20rpx;\n}\n\n.select-dot {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 20rpx;\n  border: 2rpx solid #ccc;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.select-dot.selected {\n  border-color: #0066FF;\n}\n\n.select-dot.selected::after {\n  content: \"\";\n  width: 24rpx;\n  height: 24rpx;\n  border-radius: 12rpx;\n  background-color: #0066FF;\n}\n\n.package-content {\n  flex: 1;\n}\n\n.package-name {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.package-details {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.package-count {\n  font-size: 28rpx;\n  color: #666;\n  margin-right: 20rpx;\n}\n\n.package-validity {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.package-feature {\n  display: inline-block;\n  font-size: 24rpx;\n  color: #ff6600;\n  background-color: rgba(255, 102, 0, 0.1);\n  padding: 4rpx 12rpx;\n  border-radius: 10rpx;\n}\n\n.package-price {\n  display: flex;\n  align-items: center;\n  color: #ff6600;\n}\n\n.price-symbol {\n  font-size: 24rpx;\n  margin-right: 2rpx;\n}\n\n.price-value {\n  font-size: 36rpx;\n  font-weight: bold;\n}\n\n/* FAQ部分 */\n.faq-section {\n  margin: 30rpx;\n  background-color: #fff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 30rpx;\n}\n\n.faq-item {\n  margin-bottom: 20rpx;\n  border-bottom: 2rpx solid #f5f5f5;\n  padding-bottom: 20rpx;\n}\n\n.faq-item:last-child {\n  margin-bottom: 0;\n  border-bottom: none;\n  padding-bottom: 0;\n}\n\n.faq-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.faq-question {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.faq-icon {\n  width: 32rpx;\n  height: 32rpx;\n  transform: rotate(90deg);\n  transition: all 0.3s;\n}\n\n.faq-icon-open {\n  transform: rotate(270deg);\n}\n\n.faq-answer {\n  font-size: 26rpx;\n  color: #666;\n  margin-top: 20rpx;\n  line-height: 1.6;\n  padding-left: 20rpx;\n  border-left: 4rpx solid #f0f0f0;\n}\n\n/* 底部购买栏 */\n.bottom-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 30rpx;\n  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.total-price {\n  display: flex;\n  align-items: center;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.total-value {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #ff6600;\n}\n\n.buy-button {\n  background-color: #0066FF;\n  color: #fff;\n  font-size: 30rpx;\n  height: 70rpx;\n  line-height: 70rpx;\n  padding: 0 40rpx;\n  border-radius: 35rpx;\n  margin: 0;\n}\n\n.buy-button:active {\n  background-color: #0052CC;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/my/refresh-package.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "smartNavigate"], "mappings": ";;;;AA6FA,mBAAmB,MAAW;AAE9B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,iBAAiB;AAAA;AAAA,MACjB,kBAAkB;AAAA,MAClB,YAAY;AAAA;AAAA,MACZ,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,UAAU;AAAA,UACV,OAAO;AAAA,UACP,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,UAAU;AAAA,UACV,OAAO;AAAA,UACP,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,UAAU;AAAA,UACV,OAAO;AAAA,UACP,SAAS;AAAA,QACX;AAAA,MACD;AAAA,MACD,SAAS;AAAA,QACP;AAAA,UACE,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,gBAAgB;;AACd,eAAO,UAAK,YAAY,KAAK,eAAe,MAArC,mBAAwC,MAAM,QAAQ,OAAM;AAAA,IACrE;AAAA,EACD;AAAA,EACD,SAAS;AAEP,UAAM,UAAUA,oBAAI;AACpB,SAAK,kBAAkB,QAAQ;AAC/B,SAAK,eAAe,KAAK,kBAAkB;AAG3C,SAAK,mBAAkB;AAAA,EACxB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,WAAK,kBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,QAAQ,KAAK,EAAE,SAAS,CAAC,KAAK,QAAQ,KAAK,EAAE;AAAA,IACnD;AAAA;AAAA,IAGD,qBAAqB;AAEnB,iBAAW,MAAM;AAEf,aAAK,mBAAmB;AAGxB,cAAM,MAAM,oBAAI;AAChB,cAAM,WAAW,IAAI,KAAK,IAAI,YAAY,IAAI,YAAW,IAAK,CAAC,CAAC;AAChE,aAAK,aAAa,KAAK,WAAW,QAAQ;AAAA,MAC3C,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC/B;AAAA;AAAA,IAGD,oBAAoB;AAClBC,uBAAAA,cAAc,mBAAmB,EAAE,MAAM,SAAO;AAC9CD,sBAAA,MAAA,MAAA,SAAA,uCAAc,gBAAgB,GAAG;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY;AACV,YAAM,kBAAkB,KAAK,YAAY,KAAK,eAAe;AAE7DA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,UAAU,gBAAgB,IAAI,UAAU,gBAAgB,KAAK;AAAA,QACtE,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,iBAAK,gBAAgB,eAAe;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,aAAa;AAE3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAGD,aAAK,oBAAoB,YAAY;AAAA,MAGtC,GAAE,IAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnQA,GAAG,WAAW,eAAe;"}