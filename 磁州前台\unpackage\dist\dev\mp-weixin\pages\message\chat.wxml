<view class="{{['chat-container', 'data-v-013fa921', H && 'has-more-panel']}}"><view class="safe-area-top data-v-013fa921"></view><view class="custom-navbar data-v-013fa921"><view class="navbar-left data-v-013fa921" bindtap="{{b}}"><image src="{{a}}" class="back-icon data-v-013fa921"></image></view><view class="navbar-title data-v-013fa921">{{c}}</view><view class="navbar-right data-v-013fa921"></view></view><scroll-view class="chat-content data-v-013fa921" scroll-y scroll-into-view="{{g}}" bindscrolltoupper="{{h}}" upper-threshold="50" bindtap="{{i}}"><view wx:if="{{d}}" class="load-more data-v-013fa921"><text wx:if="{{e}}" class="data-v-013fa921">加载更多</text><text wx:else class="data-v-013fa921">加载中...</text></view><view class="message-list data-v-013fa921"><block wx:for="{{f}}" wx:for-item="item" wx:key="t"><view wx:if="{{item.a}}" class="time-divider data-v-013fa921"><text class="data-v-013fa921">{{item.b}}</text></view><view id="{{item.r}}" class="{{['message-item', 'data-v-013fa921', item.s && 'self']}}"><view wx:if="{{item.c}}" class="avatar-container data-v-013fa921"><image class="avatar data-v-013fa921" src="{{item.d}}" mode="aspectFill"></image></view><view class="{{['message-bubble', 'data-v-013fa921', item.o && 'self']}}"><text wx:if="{{item.e}}" class="message-text data-v-013fa921">{{item.f}}</text><view wx:elif="{{item.g}}" class="location-message data-v-013fa921" bindtap="{{item.k}}"><view class="location-icon data-v-013fa921"><image class="data-v-013fa921" src="{{item.h}}" mode="aspectFit"></image></view><view class="location-info data-v-013fa921"><text class="location-name data-v-013fa921">{{item.i}}</text><text class="location-address data-v-013fa921">{{item.j}}</text></view><view class="location-arrow data-v-013fa921"><text class="arrow-icon data-v-013fa921">></text></view></view><view wx:elif="{{item.l}}" class="image-message data-v-013fa921" bindtap="{{item.n}}"><image src="{{item.m}}" mode="widthFix" class="message-image data-v-013fa921"></image></view></view><view wx:if="{{item.p}}" class="avatar-container data-v-013fa921"><image class="avatar data-v-013fa921" src="{{item.q}}" mode="aspectFill"></image></view></view></block></view></scroll-view><view class="input-area data-v-013fa921" style="{{'bottom:' + t}}"><view class="input-box data-v-013fa921"><block wx:if="{{r0}}"><textarea class="input-field data-v-013fa921" placeholder="请输入消息..." adjust-position="{{true}}" show-confirm-bar="{{false}}" cursor-spacing="{{20}}" maxlength="{{j}}" bindfocus="{{k}}" bindblur="{{l}}" bindconfirm="{{m}}" bindinput="{{n}}" style="{{'max-height:' + '120rpx'}}" value="{{o}}"></textarea></block></view><view class="{{['action-btn', 'data-v-013fa921', r && 'has-text']}}" bindtap="{{s}}"><text wx:if="{{p}}" class="send-text data-v-013fa921">发送</text><image wx:else src="{{q}}" mode="aspectFit" class="add-icon data-v-013fa921"></image></view></view><view class="safe-area-bottom data-v-013fa921"></view><view wx:if="{{v}}" class="more-panel-mask data-v-013fa921" catchtap="{{w}}"></view><view class="{{['more-panel-drawer', 'data-v-013fa921', G && 'show']}}"><view class="more-panel-content data-v-013fa921"><view class="more-grid data-v-013fa921"><view class="more-item data-v-013fa921" catchtap="{{y}}"><view class="more-icon location-icon data-v-013fa921"><image src="{{x}}" mode="aspectFit" class="panel-icon-image data-v-013fa921"></image></view><text class="more-text data-v-013fa921">位置</text></view><view class="more-item data-v-013fa921" catchtap="{{A}}"><view class="more-icon image-icon data-v-013fa921"><image src="{{z}}" mode="aspectFit" class="panel-icon-image data-v-013fa921"></image></view><text class="more-text data-v-013fa921">图片</text></view><view class="more-item data-v-013fa921" catchtap="{{C}}"><view class="more-icon file-icon data-v-013fa921"><image src="{{B}}" mode="aspectFit" class="panel-icon-image data-v-013fa921"></image></view><text class="more-text data-v-013fa921">文件</text></view><view class="more-item data-v-013fa921" catchtap="{{E}}"><view class="more-icon contact-icon data-v-013fa921"><image src="{{D}}" mode="aspectFit" class="panel-icon-image data-v-013fa921"></image></view><text class="more-text data-v-013fa921">名片</text></view></view></view><view class="more-panel-handle data-v-013fa921" catchtap="{{F}}"><view class="handle-line data-v-013fa921"></view></view></view></view>