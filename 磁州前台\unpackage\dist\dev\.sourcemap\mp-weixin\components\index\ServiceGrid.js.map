{"version": 3, "file": "ServiceGrid.js", "sources": ["components/index/ServiceGrid.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9pbmRleC9TZXJ2aWNlR3JpZC52dWU"], "sourcesContent": ["<template>\n  <!-- 服务分类 -->\n  <view class=\"service-category card-section fade-in\">\n    <view class=\"category-header\">\n      <view class=\"section-title-wrap\">\n        <view class=\"section-bar\"></view>\n        <text class=\"category-title\">服务分类</text>\n      </view>\n      <view class=\"category-dots\">\n        <view class=\"category-dot\" :class=\"{active: categoryPage === 0}\" @click=\"changeCategoryPage(0)\"></view>\n        <view class=\"category-dot\" :class=\"{active: categoryPage === 1}\" @click=\"changeCategoryPage(1)\"></view>\n      </view>\n    </view>\n    <swiper class=\"category-swiper\" :current=\"categoryPage\" @change=\"onCategoryPageChange\" circular>\n      <swiper-item>\n        <view class=\"category-page\">\n          <view class=\"category-item\" v-for=\"(item, index) in serviceCategory.slice(0, 10)\" :key=\"item.name\" @click=\"handleCategoryClick(item, index)\">\n            <image :src=\"item.icon\" class=\"category-icon\"></image>\n            <text class=\"category-name\">{{item.name}}</text>\n          </view>\n        </view>\n      </swiper-item>\n      <swiper-item>\n        <view class=\"category-page category-page-second\">\n          <view class=\"category-item\" v-for=\"(item, index) in serviceCategory.slice(10)\" :key=\"item.name\" @click=\"handleCategoryClick(item, index + 10)\">\n            <image :src=\"item.icon\" class=\"category-icon\"></image>\n            <text class=\"category-name\">{{item.name}}</text>\n          </view>\n        </view>\n      </swiper-item>\n    </swiper>\n    \n    <!-- 到家服务全屏子分类弹出层 -->\n    <view class=\"service-category-popup\" v-if=\"showSubcategory\" @click.stop=\"closeSubcategory\">\n      <view class=\"service-category-container\" @click.stop>\n        <view class=\"service-category-header\">\n          <view class=\"category-header-left\">\n            <text class=\"category-title-main\">到家服务分类</text>\n            <text class=\"category-subtitle\">选择您需要的服务类型</text>\n          </view>\n          <view class=\"category-close\" @click.stop=\"closeSubcategory\">\n            <view class=\"close-icon\"></view>\n          </view>\n        </view>\n        \n        <view class=\"service-category-content\">\n          <view \n            class=\"service-type-item\" \n            v-for=\"(item, index) in homeServiceCategories\" \n            :key=\"index\"\n            @click=\"handleServiceItemClick(item)\"\n          >\n            <view class=\"service-type-icon-wrap\">\n              <image :src=\"item.icon\" class=\"service-type-icon\"></image>\n            </view>\n            <text class=\"service-type-name\">{{item.name}}</text>\n          </view>\n        </view>\n        \n        <view class=\"service-category-footer\">\n          <view class=\"view-all-btn\" @click=\"navigateTo('/subPackages/service/pages/list?type=home')\">\n            查看所有到家服务\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\n\nconst categoryPage = ref(0);\nconst showSubcategory = ref(false);\n\nconst serviceCategory = ref([\n        { name: '到家服务', icon: '/static/images/tabbar/到家服务.png', url: '/subPackages/service/pages/home-service-list' },\n        { name: '寻找服务', icon: '/static/images/tabbar/寻找服务.png', url: '/subPackages/service/pages/list?type=find' },\n        { name: '生意转让', icon: '/static/images/tabbar/生意转让.png', url: '/subPackages/service/pages/list?type=business' },\n        { name: '招聘信息', icon: '/static/images/tabbar/招聘信息.png', url: '/subPackages/service/pages/list?type=job' },\n        { name: '求职信息', icon: '/static/images/tabbar/求职信息.png', url: '/subPackages/service/pages/list?type=resume' },\n        { name: '房屋出租', icon: '/static/images/tabbar/出租.png', url: '/subPackages/service/pages/list?type=house_rent' },\n        { name: '房屋出售', icon: '/static/images/tabbar/出售.png', url: '/subPackages/service/pages/list?type=house_sell' },\n        { name: '二手车辆', icon: '/static/images/tabbar/二手车辆.png', url: '/subPackages/service/pages/list?type=second_car' },\n        { name: '宠物信息', icon: '/static/images/tabbar/宠物信息.png', url: '/subPackages/service/pages/list?type=pet' },\n        { name: '商家活动', icon: '/static/images/tabbar/商家活动.png', url: '/subPackages/service/pages/list?type=merchant_activity' },\n        { name: '婚恋交友', icon: '/static/images/tabbar/婚恋交友.png', url: '/subPackages/service/pages/list?type=dating' },\n        { name: '车辆服务', icon: '/static/images/tabbar/车辆服务.png', url: '/subPackages/service/pages/list?type=car' },\n        { name: '二手闲置', icon: '/static/images/tabbar/二手闲置.png', url: '/subPackages/service/pages/list?type=second_hand' },\n        { name: '磁州拼车', icon: '/static/images/tabbar/磁州拼车.png', url: '/subPackages/service/pages/list?type=carpool' },\n        { name: '教育培训', icon: '/static/images/tabbar/教育培训.png', url: '/subPackages/service/pages/list?type=education' },\n        { name: '其他服务', icon: '/static/images/tabbar/其他服务.png', url: '/subPackages/service/pages/list?type=other' }\n]);\n\nconst homeServiceCategories = ref([\n        { name: '家政服务', icon: '/static/images/tabbar/123/家政服务.png', url: '/subPackages/service/pages/list?type=home&subType=home_cleaning&name=家政服务' },\n        { name: '维修改造', icon: '/static/images/tabbar/123/维修改造.png', url: '/subPackages/service/pages/list?type=home&subType=repair&name=维修改造' },\n        { name: '上门安装', icon: '/static/images/tabbar/123/上门安装.png', url: '/subPackages/service/pages/list?type=home&subType=installation&name=上门安装' },\n        { name: '开锁换锁', icon: '/static/images/tabbar/123/开锁换锁.png', url: '/subPackages/service/pages/list?type=home&subType=locksmith&name=开锁换锁' },\n        { name: '搬家拉货', icon: '/static/images/tabbar/123/搬家拉货.png', url: '/subPackages/service/pages/list?type=home&subType=moving&name=搬家拉货' },\n        { name: '上门美容', icon: '/static/images/tabbar/123/上门美容.png', url: '/subPackages/service/pages/list?type=home&subType=beauty&name=上门美容' },\n        { name: '上门家教', icon: '/static/images/tabbar/123/上门家教.png', url: '/subPackages/service/pages/list?type=home&subType=tutor&name=上门家教' },\n        { name: '宠物服务', icon: '/static/images/tabbar/123/宠物服务.png', url: '/subPackages/service/pages/list?type=home&subType=pet_service&name=宠物服务' },\n        { name: '上门疏通', icon: '/static/images/tabbar/123/上门疏通.png', url: '/subPackages/service/pages/list?type=home&subType=plumbing&name=上门疏通' },\n        { name: '其他类型', icon: '/static/images/tabbar/123/其他类型.png', url: '/subPackages/service/pages/list?type=home&subType=other&name=其他类型' }\n]);\n\nfunction changeCategoryPage(page) {\n  categoryPage.value = page;\n}\n\nfunction onCategoryPageChange(e) {\n  categoryPage.value = e.detail.current;\n}\n\nfunction handleCategoryClick(item, index) {\n      if (index === 0 && item.name === '到家服务') {\n    showSubcategory.value = true;\n        uni.vibrateShort();\n      } else {\n        const typeMatch = item.url.match(/type=([^&]+)/);\n        const serviceType = typeMatch ? typeMatch[1] : '';\n        \n        if (serviceType) {\n          uni.navigateTo({\n            url: `/subPackages/service/pages/list?type=${serviceType}&title=${encodeURIComponent(item.name)}`,\n            success: () => {\n              console.log('成功跳转到服务列表页面:', item.name);\n              uni.vibrateShort();\n            },\n            fail: (err) => {\n              console.error('跳转到服务列表页面失败:', err);\n          navigateTo(item.url);\n            }\n          });\n        } else {\n      navigateTo(item.url);\n        }\n      }\n}\n\nfunction closeSubcategory() {\n  showSubcategory.value = false;\n}\n\nfunction navigateTo(url) {\n      if (!url) return;\n      \n      try {\n        uni.navigateTo({\n          url: url,\n          fail: (err) => {\n            console.error('页面跳转失败:', err);\n            uni.switchTab({\n              url: url,\n              fail: (err2) => {\n                console.error('switchTab跳转也失败:', err2);\n                uni.showToast({\n                  title: '页面跳转失败',\n                  icon: 'none'\n                });\n              }\n            });\n          }\n        });\n      } catch (error) {\n        console.error('导航出错:', error);\n      }\n}\n\nfunction handleServiceItemClick(item) {\n      console.log('点击了到家服务子分类:', item.name);\n  closeSubcategory();\n      uni.vibrateShort();\n      \n      const subTypeMatch = item.url.match(/subType=([^&]+)/);\n      const nameMatch = item.url.match(/name=([^&]+)/);\n      \n      const subType = subTypeMatch ? subTypeMatch[1] : '';\n      const name = nameMatch ? decodeURIComponent(nameMatch[1]) : item.name;\n      \n      try {\n        uni.navigateTo({\n          url: `/subPackages/service/pages/list?type=home&subType=${subType}&name=${encodeURIComponent(name)}`,\n          success: () => {\n            console.log('成功跳转到到家服务子分类:', name);\n          },\n          fail: (err) => {\n            console.error('跳转到到家服务子分类失败:', err);\n            uni.navigateTo({\n              url: '/subPackages/service/pages/list',\n              fail: (err2) => {\n                console.error('备用跳转也失败:', err2);\n                uni.showToast({\n                  title: '页面跳转失败',\n                  icon: 'none'\n                });\n              }\n            });\n          }\n        });\n      } catch (error) {\n        console.error('到家服务子分类跳转出错:', error);\n        uni.navigateTo({\n          url: '/subPackages/service/pages/list'\n        });\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n/* 服务分类 */\n.service-category {\n  background-color: #FFFFFF;\n  padding: 26rpx 20rpx;\n  margin: 0 25rpx 30rpx;\n  border-radius: 35rpx;\n  backdrop-filter: none;\n  -webkit-backdrop-filter: none;\n}\n\n.category-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 10rpx 20rpx 10rpx;\n  border-bottom: 1px solid rgba(235, 238, 245, 0.5);\n  margin-bottom: 20rpx;\n}\n\n.section-title-wrap {\n  display: flex;\n  align-items: center;\n}\n\n.section-bar {\n  width: 6rpx;\n  height: 28rpx;\n  background: linear-gradient(to bottom, #007AFF, #5AC8FA);\n  border-radius: 3rpx;\n  margin-right: 6rpx;\n}\n\n.category-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333;\n  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);\n}\n\n.category-dots {\n  display: flex;\n  align-items: center;\n}\n\n.category-dot {\n  width: 16rpx;\n  height: 16rpx;\n  border-radius: 50%;\n  background-color: #e0e0e0;\n  margin: 0 6rpx;\n  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);\n}\n\n.category-dot.active {\n  width: 24rpx;\n  height: 16rpx;\n  border-radius: 8rpx;\n  background: linear-gradient(90deg, #1677ff, #3a8eff);\n  box-shadow: 0 1px 3px rgba(22, 119, 255, 0.2);\n}\n\n.category-swiper {\n  width: 100%;\n  height: 360rpx;\n}\n\n.category-page {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-wrap: wrap;\n  padding: 5rpx 0;\n}\n\n/* 第二页服务分类样式 */\n.category-page-second {\n  align-items: flex-start;\n  justify-content: flex-start;\n  align-content: flex-start;\n}\n\n.category-item {\n  width: 20%;\n  display: inline-flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 18rpx 0;\n}\n\n.category-icon {\n  width: 80rpx;\n  height: 80rpx;\n  margin-bottom: 12rpx;\n  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));\n}\n\n.category-item:active .category-icon {\n  transform: none;\n}\n\n.category-name {\n  font-size: 24rpx;\n  color: #333;\n  font-weight: 400;\n  text-align: center;\n  width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-top: 4rpx;\n}\n\n/* 到家服务全屏子分类弹出层样式 */\n.service-category-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.4);\n  backdrop-filter: blur(5px);\n  -webkit-backdrop-filter: blur(5px);\n  z-index: 1001;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  animation: fadeIn 0.25s ease;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n.service-category-container {\n  width: 85%;\n  height: auto;\n  max-height: 72%;\n  max-width: 650rpx;\n  background-color: #fff;\n  border-radius: 26rpx;\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  animation: slideUp 0.3s ease;\n}\n\n@keyframes slideUp {\n  from { transform: translateY(50rpx); opacity: 0.8; }\n  to { transform: translateY(0); opacity: 1; }\n}\n\n.service-category-header {\n  padding: 26rpx 30rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #f0f5ff;\n  background: linear-gradient(to right, #f9fbff, #f0f5ff);\n}\n\n.category-header-left {\n  display: flex;\n  flex-direction: column;\n}\n\n.category-title-main {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 6rpx;\n}\n\n.category-subtitle {\n  font-size: 22rpx;\n  color: #777;\n}\n\n.category-close {\n  width: 50rpx;\n  height: 50rpx;\n  border-radius: 25rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0, 0, 0, 0.04);\n  transition: all 0.3s;\n}\n\n.category-close:active {\n  background-color: rgba(0, 0, 0, 0.1);\n  transform: scale(0.9);\n}\n\n.close-icon {\n  width: 20rpx;\n  height: 20rpx;\n  position: relative;\n}\n\n.close-icon:before, .close-icon:after {\n  content: '';\n  position: absolute;\n  width: 20rpx;\n  height: 2px;\n  background-color: #666;\n  top: 9rpx;\n  left: 0;\n}\n\n.close-icon:before {\n  transform: rotate(45deg);\n}\n\n.close-icon:after {\n  transform: rotate(-45deg);\n}\n\n.service-category-content {\n  padding: 25rpx 15rpx;\n  display: flex;\n  flex-wrap: wrap;\n  overflow-y: auto;\n  max-height: calc(80vh - 200rpx);\n}\n\n.service-type-item {\n  width: 25%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 10rpx 0;\n  margin-bottom: 12rpx;\n  transition: all 0.2s;\n}\n\n.service-type-item:active {\n  transform: scale(0.92);\n}\n\n.service-type-icon-wrap {\n  width: 90rpx;\n  height: 90rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 15rpx;\n}\n\n.service-type-icon {\n  width: 90rpx;\n  height: 90rpx;\n  position: relative;\n}\n\n.service-type-name {\n  font-size: 22rpx;\n  color: #333;\n  text-align: center;\n  padding: 0 6rpx;\n  width: 100%;\n  box-sizing: border-box;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.service-category-footer {\n  padding: 24rpx 0;\n  display: flex;\n  justify-content: center;\n  border-top: 1px solid #f0f5ff;\n  background-color: #f9fbff;\n}\n\n.view-all-btn {\n  background: linear-gradient(135deg, #1677FF, #0052CC);\n  color: #fff;\n  font-size: 26rpx;\n  padding: 14rpx 50rpx;\n  border-radius: 30rpx;\n  text-align: center;\n  box-shadow: 0 5rpx 12rpx rgba(22, 119, 255, 0.18);\n  position: relative;\n  overflow: hidden;\n}\n\n.view-all-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 3rpx 10rpx rgba(22, 119, 255, 0.15);\n}\n\n.view-all-btn:after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -50%;\n  width: 150%;\n  height: 100%;\n  background: linear-gradient(90deg, \n    rgba(255, 255, 255, 0) 0%, \n    rgba(255, 255, 255, 0.2) 50%,\n    rgba(255, 255, 255, 0) 100%\n  );\n  transform: skewX(-25deg);\n  animation: shine 3s infinite;\n}\n\n@keyframes shine {\n  0% { left: -150%; }\n  50%, 100% { left: 150%; }\n}\n\n.card-section {\n  background: #ffffff;\n  border-radius: 35rpx;\n  margin-bottom: 40rpx;\n  overflow: hidden;\n  border: none;\n  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.08);\n  padding: 26rpx 22rpx;\n}\n\n.fade-in {\n  animation: fadeIn 0.5s ease-in-out;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/index/ServiceGrid.vue'\nwx.createComponent(Component)"], "names": ["ref", "uni"], "mappings": ";;;;;AAwEA,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AAEjC,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MACpB,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,+CAAgD;AAAA,MAC7G,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,4CAA6C;AAAA,MAC1G,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,gDAAiD;AAAA,MAC9G,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,2CAA4C;AAAA,MACzG,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,8CAA+C;AAAA,MAC5G,EAAE,MAAM,QAAQ,MAAM,gCAAgC,KAAK,kDAAmD;AAAA,MAC9G,EAAE,MAAM,QAAQ,MAAM,gCAAgC,KAAK,kDAAmD;AAAA,MAC9G,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,kDAAmD;AAAA,MAChH,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,2CAA4C;AAAA,MACzG,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,yDAA0D;AAAA,MACvH,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,8CAA+C;AAAA,MAC5G,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,2CAA4C;AAAA,MACzG,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,mDAAoD;AAAA,MACjH,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,+CAAgD;AAAA,MAC7G,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,iDAAkD;AAAA,MAC/G,EAAE,MAAM,QAAQ,MAAM,kCAAkC,KAAK,6CAA8C;AAAA,IACnH,CAAC;AAED,UAAM,wBAAwBA,cAAAA,IAAI;AAAA,MAC1B,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,4EAA6E;AAAA,MAC9I,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,qEAAsE;AAAA,MACvI,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,2EAA4E;AAAA,MAC7I,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,wEAAyE;AAAA,MAC1I,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,qEAAsE;AAAA,MACvI,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,qEAAsE;AAAA,MACvI,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,oEAAqE;AAAA,MACtI,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,0EAA2E;AAAA,MAC5I,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,uEAAwE;AAAA,MACzI,EAAE,MAAM,QAAQ,MAAM,sCAAsC,KAAK,oEAAqE;AAAA,IAC9I,CAAC;AAED,aAAS,mBAAmB,MAAM;AAChC,mBAAa,QAAQ;AAAA,IACvB;AAEA,aAAS,qBAAqB,GAAG;AAC/B,mBAAa,QAAQ,EAAE,OAAO;AAAA,IAChC;AAEA,aAAS,oBAAoB,MAAM,OAAO;AACpC,UAAI,UAAU,KAAK,KAAK,SAAS,QAAQ;AAC3C,wBAAgB,QAAQ;AACpBC,sBAAG,MAAC,aAAY;AAAA,MACxB,OAAa;AACL,cAAM,YAAY,KAAK,IAAI,MAAM,cAAc;AAC/C,cAAM,cAAc,YAAY,UAAU,CAAC,IAAI;AAE/C,YAAI,aAAa;AACfA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,wCAAwC,WAAW,UAAU,mBAAmB,KAAK,IAAI,CAAC;AAAA,YAC/F,SAAS,MAAM;AACbA,4BAAA,MAAA,MAAA,OAAA,2CAAY,gBAAgB,KAAK,IAAI;AACrCA,4BAAG,MAAC,aAAY;AAAA,YACjB;AAAA,YACD,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,2CAAc,gBAAgB,GAAG;AACrC,yBAAW,KAAK,GAAG;AAAA,YAChB;AAAA,UACb,CAAW;AAAA,QACX,OAAe;AACT,qBAAW,KAAK,GAAG;AAAA,QAChB;AAAA,MACF;AAAA,IACP;AAEA,aAAS,mBAAmB;AAC1B,sBAAgB,QAAQ;AAAA,IAC1B;AAEA,aAAS,WAAW,KAAK;AACnB,UAAI,CAAC;AAAK;AAEV,UAAI;AACFA,sBAAAA,MAAI,WAAW;AAAA,UACb;AAAA,UACA,MAAM,CAAC,QAAQ;AACbA,0BAAc,MAAA,MAAA,SAAA,2CAAA,WAAW,GAAG;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACZ;AAAA,cACA,MAAM,CAAC,SAAS;AACdA,8BAAc,MAAA,MAAA,SAAA,2CAAA,mBAAmB,IAAI;AACrCA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACxB,CAAiB;AAAA,cACF;AAAA,YACf,CAAa;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,2CAAA,SAAS,KAAK;AAAA,MAC7B;AAAA,IACP;AAEA,aAAS,uBAAuB,MAAM;AAChCA,oBAAA,MAAA,MAAA,OAAA,2CAAY,eAAe,KAAK,IAAI;AACxC;AACIA,oBAAG,MAAC,aAAY;AAEhB,YAAM,eAAe,KAAK,IAAI,MAAM,iBAAiB;AACrD,YAAM,YAAY,KAAK,IAAI,MAAM,cAAc;AAE/C,YAAM,UAAU,eAAe,aAAa,CAAC,IAAI;AACjD,YAAM,OAAO,YAAY,mBAAmB,UAAU,CAAC,CAAC,IAAI,KAAK;AAEjE,UAAI;AACFA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,qDAAqD,OAAO,SAAS,mBAAmB,IAAI,CAAC;AAAA,UAClG,SAAS,MAAM;AACbA,0BAAA,MAAA,MAAA,OAAA,2CAAY,iBAAiB,IAAI;AAAA,UAClC;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAc,MAAA,MAAA,SAAA,2CAAA,iBAAiB,GAAG;AAClCA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,cACL,MAAM,CAAC,SAAS;AACdA,8BAAA,MAAA,MAAA,SAAA,2CAAc,YAAY,IAAI;AAC9BA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACxB,CAAiB;AAAA,cACF;AAAA,YACf,CAAa;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,2CAAc,gBAAgB,KAAK;AACnCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACf,CAAS;AAAA,MACN;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9MA,GAAG,gBAAgB,SAAS;"}