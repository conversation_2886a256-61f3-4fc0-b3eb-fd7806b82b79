<template>
  <view class="roles-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">角色设置</text>
      <view class="navbar-right">
        <view class="add-icon" @click="showAddRoleModal">
          <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 角色列表 -->
    <view class="roles-list">
      <view 
        v-for="(role, index) in roles" 
        :key="index"
        class="role-card"
        :class="{'active': selectedRoleId === role.id}"
        @click="selectRole(role.id)">
        <view class="role-header">
          <view class="role-icon" :style="{backgroundColor: role.color}">
            <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 00-3-3.87"></path>
              <path d="M16 3.13a4 4 0 010 7.75"></path>
            </svg>
          </view>
          <view class="role-info">
            <text class="role-name">{{role.name}}</text>
            <text class="role-count">{{role.userCount}}人</text>
          </view>
          <view class="role-actions">
            <view class="action-icon edit" @click.stop="editRole(role.id)">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"></path>
                <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"></path>
              </svg>
            </view>
            <view class="action-icon delete" @click.stop="confirmDeleteRole(role.id)" v-if="!role.isDefault">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="3 6 5 6 21 6"></polyline>
                <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
              </svg>
            </view>
          </view>
        </view>
        <view class="role-description">
          <text class="description-text">{{role.description}}</text>
        </view>
      </view>
    </view>
    
    <!-- 角色详情 -->
    <view class="role-detail" v-if="selectedRole">
      <view class="detail-header">
        <text class="detail-title">{{selectedRole.name}}权限配置</text>
        <view class="detail-actions">
          <button class="save-button" @click="saveRolePermissions">保存</button>
        </view>
      </view>
      
      <view class="permission-section">
        <view class="section-title">
          <text class="title-text">功能权限</text>
          <text class="select-all" @click="toggleSelectAll('function')">{{allFunctionSelected ? '取消全选' : '全选'}}</text>
        </view>
        
        <view class="permission-group" v-for="(group, index) in functionPermissions" :key="'func-'+index">
          <view class="group-header" @click="toggleGroup(group.id)">
            <view class="group-checkbox">
              <checkbox :checked="isGroupChecked(group)" color="#1677FF" @click.stop="toggleGroupCheck(group)" />
            </view>
            <text class="group-name">{{group.name}}</text>
            <view :class="['arrow-icon', {'expanded': expandedGroups.includes(group.id)}]"></view>
          </view>
          
          <view class="permission-items" v-if="expandedGroups.includes(group.id)">
            <view class="permission-item" v-for="(item, itemIndex) in group.items" :key="'item-'+itemIndex">
              <view class="item-checkbox">
                <checkbox :checked="selectedPermissions.includes(item.id)" color="#1677FF" @click="togglePermission(item.id)" />
              </view>
              <text class="item-name">{{item.name}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="permission-section">
        <view class="section-title">
          <text class="title-text">数据权限</text>
          <text class="select-all" @click="toggleSelectAll('data')">{{allDataSelected ? '取消全选' : '全选'}}</text>
        </view>
        
        <view class="permission-group" v-for="(group, index) in dataPermissions" :key="'data-'+index">
          <view class="group-header" @click="toggleGroup(group.id)">
            <view class="group-checkbox">
              <checkbox :checked="isGroupChecked(group)" color="#1677FF" @click.stop="toggleGroupCheck(group)" />
            </view>
            <text class="group-name">{{group.name}}</text>
            <view :class="['arrow-icon', {'expanded': expandedGroups.includes(group.id)}]"></view>
          </view>
          
          <view class="permission-items" v-if="expandedGroups.includes(group.id)">
            <view class="permission-item" v-for="(item, itemIndex) in group.items" :key="'item-'+itemIndex">
              <view class="item-checkbox">
                <checkbox :checked="selectedPermissions.includes(item.id)" color="#1677FF" @click="togglePermission(item.id)" />
              </view>
              <text class="item-name">{{item.name}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 添加/编辑角色弹窗 -->
    <uni-popup ref="rolePopup" type="dialog">
      <uni-popup-dialog 
        :title="isEditing ? '编辑角色' : '添加角色'" 
        :before-close="true" 
        @confirm="confirmRoleAction" 
        @close="closeRoleModal">
        <view class="role-form">
          <view class="form-item">
            <text class="form-label">角色名称</text>
            <input class="form-input" type="text" v-model="roleForm.name" placeholder="请输入角色名称" />
          </view>
          
          <view class="form-item">
            <text class="form-label">角色描述</text>
            <textarea class="form-textarea" v-model="roleForm.description" placeholder="请输入角色描述" />
          </view>
          
          <view class="form-item">
            <text class="form-label">角色颜色</text>
            <view class="color-selector">
              <view 
                v-for="(color, index) in colorOptions" 
                :key="index"
                :class="['color-option', {'selected': roleForm.color === color}]"
                :style="{backgroundColor: color}"
                @click="selectColor(color)">
              </view>
            </view>
          </view>
        </view>
      </uni-popup-dialog>
    </uni-popup>
    
    <!-- 删除确认弹窗 -->
    <uni-popup ref="deletePopup" type="dialog">
      <uni-popup-dialog 
        title="删除角色" 
        content="确定要删除该角色吗？删除后无法恢复，该角色下的用户将失去相应权限。" 
        :before-close="true" 
        @confirm="deleteRole" 
        @close="closeDeleteModal">
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      roles: [
        {
          id: 1,
          name: '超级管理员',
          description: '拥有所有权限，可以管理整个系统',
          userCount: 2,
          color: '#1677FF',
          isDefault: true
        },
        {
          id: 2,
          name: '运营管理员',
          description: '负责日常运营，包括订单管理、商品管理等',
          userCount: 5,
          color: '#52c41a',
          isDefault: false
        },
        {
          id: 3,
          name: '客服专员',
          description: '负责客户服务，处理客户咨询和投诉',
          userCount: 8,
          color: '#fa8c16',
          isDefault: false
        },
        {
          id: 4,
          name: '财务人员',
          description: '负责财务管理，包括对账、结算等',
          userCount: 3,
          color: '#eb2f96',
          isDefault: false
        }
      ],
      selectedRoleId: 1,
      expandedGroups: ['func-1', 'func-2'],
      selectedPermissions: ['func-1-1', 'func-1-2', 'func-2-1', 'func-2-3', 'data-1-1', 'data-2-2'],
      functionPermissions: [
        {
          id: 'func-1',
          name: '订单管理',
          items: [
            { id: 'func-1-1', name: '查看订单' },
            { id: 'func-1-2', name: '处理订单' },
            { id: 'func-1-3', name: '退款处理' }
          ]
        },
        {
          id: 'func-2',
          name: '商品管理',
          items: [
            { id: 'func-2-1', name: '查看商品' },
            { id: 'func-2-2', name: '添加商品' },
            { id: 'func-2-3', name: '编辑商品' },
            { id: 'func-2-4', name: '下架商品' }
          ]
        },
        {
          id: 'func-3',
          name: '营销管理',
          items: [
            { id: 'func-3-1', name: '查看活动' },
            { id: 'func-3-2', name: '创建活动' },
            { id: 'func-3-3', name: '编辑活动' },
            { id: 'func-3-4', name: '结束活动' }
          ]
        },
        {
          id: 'func-4',
          name: '数据分析',
          items: [
            { id: 'func-4-1', name: '查看销售数据' },
            { id: 'func-4-2', name: '查看客户数据' },
            { id: 'func-4-3', name: '导出报表' }
          ]
        }
      ],
      dataPermissions: [
        {
          id: 'data-1',
          name: '订单数据',
          items: [
            { id: 'data-1-1', name: '所有订单' },
            { id: 'data-1-2', name: '部门订单' },
            { id: 'data-1-3', name: '个人订单' }
          ]
        },
        {
          id: 'data-2',
          name: '客户数据',
          items: [
            { id: 'data-2-1', name: '所有客户' },
            { id: 'data-2-2', name: '部门客户' },
            { id: 'data-2-3', name: '个人客户' }
          ]
        }
      ],
      isEditing: false,
      editingRoleId: null,
      roleForm: {
        name: '',
        description: '',
        color: '#1677FF'
      },
      colorOptions: ['#1677FF', '#52c41a', '#fa8c16', '#eb2f96', '#722ed1', '#f5222d', '#faad14'],
      deleteRoleId: null
    }
  },
  computed: {
    selectedRole() {
      return this.roles.find(role => role.id === this.selectedRoleId);
    },
    allFunctionSelected() {
      const allFunctionIds = this.getAllPermissionIds(this.functionPermissions);
      return allFunctionIds.every(id => this.selectedPermissions.includes(id));
    },
    allDataSelected() {
      const allDataIds = this.getAllPermissionIds(this.dataPermissions);
      return allDataIds.every(id => this.selectedPermissions.includes(id));
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    selectRole(id) {
      this.selectedRoleId = id;
    },
    toggleGroup(groupId) {
      if (this.expandedGroups.includes(groupId)) {
        this.expandedGroups = this.expandedGroups.filter(id => id !== groupId);
      } else {
        this.expandedGroups.push(groupId);
      }
    },
    isGroupChecked(group) {
      return group.items.every(item => this.selectedPermissions.includes(item.id));
    },
    toggleGroupCheck(group) {
      const allChecked = this.isGroupChecked(group);
      if (allChecked) {
        // 取消选中该组所有权限
        this.selectedPermissions = this.selectedPermissions.filter(id => !group.items.some(item => item.id === id));
      } else {
        // 选中该组所有权限
        const groupItemIds = group.items.map(item => item.id);
        this.selectedPermissions = [...new Set([...this.selectedPermissions, ...groupItemIds])];
      }
    },
    togglePermission(permissionId) {
      if (this.selectedPermissions.includes(permissionId)) {
        this.selectedPermissions = this.selectedPermissions.filter(id => id !== permissionId);
      } else {
        this.selectedPermissions.push(permissionId);
      }
    },
    getAllPermissionIds(permissionGroups) {
      return permissionGroups.reduce((ids, group) => {
        return [...ids, ...group.items.map(item => item.id)];
      }, []);
    },
    toggleSelectAll(type) {
      const permissionGroups = type === 'function' ? this.functionPermissions : this.dataPermissions;
      const allIds = this.getAllPermissionIds(permissionGroups);
      const isAllSelected = type === 'function' ? this.allFunctionSelected : this.allDataSelected;
      
      if (isAllSelected) {
        // 取消全选
        this.selectedPermissions = this.selectedPermissions.filter(id => !allIds.includes(id));
      } else {
        // 全选
        this.selectedPermissions = [...new Set([...this.selectedPermissions, ...allIds])];
      }
    },
    saveRolePermissions() {
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '权限配置已保存',
          icon: 'success'
        });
      }, 1500);
    },
    showAddRoleModal() {
      this.isEditing = false;
      this.roleForm = {
        name: '',
        description: '',
        color: '#1677FF'
      };
      this.$refs.rolePopup.open();
    },
    editRole(roleId) {
      const role = this.roles.find(r => r.id === roleId);
      if (role) {
        this.isEditing = true;
        this.editingRoleId = roleId;
        this.roleForm = {
          name: role.name,
          description: role.description,
          color: role.color
        };
        this.$refs.rolePopup.open();
      }
    },
    confirmRoleAction() {
      if (!this.roleForm.name) {
        uni.showToast({
          title: '请输入角色名称',
          icon: 'none'
        });
        return;
      }
      
      if (this.isEditing) {
        // 编辑角色
        const index = this.roles.findIndex(r => r.id === this.editingRoleId);
        if (index !== -1) {
          this.roles[index] = {
            ...this.roles[index],
            name: this.roleForm.name,
            description: this.roleForm.description,
            color: this.roleForm.color
          };
          
          uni.showToast({
            title: '角色已更新',
            icon: 'success'
          });
        }
      } else {
        // 添加角色
        const newRole = {
          id: this.roles.length + 1,
          name: this.roleForm.name,
          description: this.roleForm.description,
          color: this.roleForm.color,
          userCount: 0,
          isDefault: false
        };
        
        this.roles.push(newRole);
        
        uni.showToast({
          title: '角色已添加',
          icon: 'success'
        });
      }
      
      this.$refs.rolePopup.close();
    },
    closeRoleModal() {
      this.$refs.rolePopup.close();
    },
    selectColor(color) {
      this.roleForm.color = color;
    },
    confirmDeleteRole(roleId) {
      this.deleteRoleId = roleId;
      this.$refs.deletePopup.open();
    },
    deleteRole() {
      if (this.deleteRoleId) {
        this.roles = this.roles.filter(role => role.id !== this.deleteRoleId);
        
        if (this.selectedRoleId === this.deleteRoleId) {
          this.selectedRoleId = this.roles[0].id;
        }
        
        uni.showToast({
          title: '角色已删除',
          icon: 'success'
        });
        
        this.deleteRoleId = null;
      }
      
      this.$refs.deletePopup.close();
    },
    closeDeleteModal() {
      this.deleteRoleId = null;
      this.$refs.deletePopup.close();
    }
  }
}
</script>

<style>
.roles-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  color: #fff;
}

.roles-list {
  padding: 16px;
}

.role-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid transparent;
}

.role-card.active {
  border-color: #1677FF;
}

.role-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.role-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #fff;
}

.role-info {
  flex: 1;
}

.role-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.role-count {
  font-size: 12px;
  color: #999;
}

.role-actions {
  display: flex;
}

.action-icon {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.action-icon.edit {
  color: #1677FF;
}

.action-icon.delete {
  color: #f5222d;
}

.role-description {
  
}

.description-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.role-detail {
  margin: 0 16px 16px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.detail-actions {
  
}

.save-button {
  height: 36px;
  padding: 0 16px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.permission-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.title-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.select-all {
  font-size: 12px;
  color: #1677FF;
}

.permission-group {
  margin-bottom: 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  overflow: hidden;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f5f7fa;
}

.group-checkbox {
  margin-right: 8px;
}

.group-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 1px solid #999;
  border-right: 1px solid #999;
  transform: rotate(45deg);
  transition: transform 0.3s;
}

.arrow-icon.expanded {
  transform: rotate(135deg);
}

.permission-items {
  padding: 0 12px;
}

.permission-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-top: 1px solid #f0f0f0;
}

.item-checkbox {
  margin-right: 8px;
}

.item-name {
  font-size: 14px;
  color: #666;
}

.role-form {
  padding: 0 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.form-input {
  width: 100%;
  height: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
}

.form-textarea {
  width: 100%;
  height: 80px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
}

.color-selector {
  display: flex;
  flex-wrap: wrap;
}

.color-option {
  width: 30px;
  height: 30px;
  border-radius: 15px;
  margin-right: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
}

.color-option.selected {
  border-color: #333;
}

.color-option.selected:after {
  content: "";
  position: absolute;
  width: 10px;
  height: 5px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
  top: 10px;
  left: 9px;
}
</style> 