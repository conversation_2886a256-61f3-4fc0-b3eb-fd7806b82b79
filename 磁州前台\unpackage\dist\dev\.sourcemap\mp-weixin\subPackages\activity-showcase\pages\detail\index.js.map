{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/detail/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGV0YWlsXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"detail-container\">\n    <!-- 自定义导航栏 - 蓝色渐变效果 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\n        </view>\n        <view class=\"navbar-title\">{{ activityTypeTitle }}</view>\n        <view class=\"navbar-right\"></view>\n      </view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view class=\"loading-container\" v-if=\"loading\">\n      <view class=\"loading-spinner\"></view>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n\n    <block v-else>\n      <!-- 商品轮播图 - 全屏设计 -->\n      <swiper class=\"product-swiper\" \n        :indicator-dots=\"true\" \n        indicator-color=\"rgba(255,255,255,0.4)\"\n        indicator-active-color=\"#FFFFFF\"\n        :autoplay=\"true\" \n        :interval=\"4000\" \n        :duration=\"400\"\n        @change=\"onSwiperChange\"\n        :style=\"{ marginTop: navbarHeight + 'px' }\">\n      <swiper-item v-for=\"(item, index) in groupbuy.images\" :key=\"index\">\n          <image :src=\"item\" mode=\"aspectFill\" class=\"swiper-image\"></image>\n      </swiper-item>\n    </swiper>\n\n      <!-- 商品基本信息卡片 -->\n      <view class=\"basic-info-card\">\n        <view class=\"price-section\">\n          <view class=\"price-main\">\n            <text class=\"price-symbol\">¥</text>\n            <text class=\"price-value\">{{formattedGroupPrice}}</text>\n            <view class=\"price-tag\">{{discountPercent}}%<text class=\"price-tag-text\">折扣</text></view>\n          </view>\n          <view class=\"price-compare\">\n            <view class=\"compare-item\">\n              <text class=\"compare-label\">市场价</text>\n              <text class=\"compare-price\">¥{{formattedMarketPrice}}</text>\n            </view>\n            <view class=\"compare-item\">\n              <text class=\"compare-label\">日常价</text>\n              <text class=\"compare-price\">¥{{formattedRegularPrice}}</text>\n            </view>\n          </view>\n    </view>\n    \n        <view class=\"save-info\">\n          <view class=\"save-tag\">\n            <text class=\"save-icon\">省</text>\n            <text class=\"save-text\">团购立省¥{{saveAmount}}</text>\n          </view>\n          <text class=\"limited-text\">限时特惠</text>\n        </view>\n        \n        <view class=\"product-title-row\">\n          <view class=\"group-tag\">拼团</view>\n        <text class=\"product-title\">{{groupbuy.title}}</text>\n      </view>\n      \n        <!-- 销量和倒计时 -->\n        <view class=\"sales-countdown\">\n          <view class=\"sales-info\">\n            <text class=\"sales-count\">已售{{groupbuy.soldCount}}件</text>\n            <text class=\"sales-divider\">|</text>\n            <text class=\"view-count\">{{groupbuy.viewCount || 1200}}人浏览</text>\n      </view>\n      \n          <view class=\"mini-countdown\">\n            <text class=\"countdown-text\">距结束</text>\n            <text class=\"time-block\">{{countdown.days}}</text>\n            <text class=\"time-colon\">:</text>\n            <text class=\"time-block\">{{countdown.hours}}</text>\n            <text class=\"time-colon\">:</text>\n            <text class=\"time-block\">{{countdown.minutes}}</text>\n            <text class=\"time-colon\">:</text>\n            <text class=\"time-block\">{{countdown.seconds}}</text>\n          </view>\n      </view>\n    </view>\n    \n      <!-- 分销组件 - 醒目位置 -->\n      <distribution-section \n        :itemId=\"id\"\n        itemType=\"group\"\n        :itemTitle=\"groupbuy.title\"\n        :itemPrice=\"groupbuy.groupPrice\"\n        :commissionRate=\"groupbuy.commissionRate || 20\"\n      />\n      \n      <!-- 适用门店列表 -->\n      <view class=\"store-list-card\">\n        <view class=\"card-header\">\n          <text class=\"card-title\">适用门店</text>\n          <view class=\"card-more\" @click=\"viewAllStores\">\n            <text>全部</text>\n            <text class=\"cuIcon-right\"></text>\n          </view>\n        </view>\n        <view class=\"store-list\">\n          <view class=\"store-item\" v-for=\"(store, index) in storeList\" :key=\"index\" @click=\"viewStoreDetail(store.id)\">\n            <image :src=\"store.logo\" class=\"store-logo\"></image>\n            <view class=\"store-info\">\n              <text class=\"store-name\">{{ store.name }}</text>\n              <text class=\"store-address\">{{ store.address }}</text>\n            </view>\n            <view class=\"store-distance\">\n              <text>{{ store.distance }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 本店其他拼团 -->\n      <view class=\"other-group-card\">\n        <view class=\"card-header\">\n          <text class=\"card-title\">本店其他拼团</text>\n          <view class=\"card-more\" @click=\"viewMoreGroups\">\n            <text>更多</text>\n            <text class=\"cuIcon-right\"></text>\n          </view>\n        </view>\n        <view class=\"other-group-list\">\n          <view class=\"other-group-item\" v-for=\"(item, index) in otherGroups\" :key=\"index\" @click=\"navigateToDetail(item.id)\">\n            <view class=\"sold-tag-corner\">已售{{ item.soldCount }}+</view>\n            <view class=\"item-image-container\">\n              <image :src=\"item.image\" class=\"other-group-image\"></image>\n            </view>\n            <view class=\"other-group-info\">\n              <view class=\"shop-location\">\n                <text class=\"shop-name\">{{ item.shopName || '磁州同城折扣店' }}</text>\n                <text class=\"location-info\">距你{{ item.distance || '1km' }} · 近磁州镇</text>\n              </view>\n              <text class=\"other-group-title\">{{ item.title }}</text>\n              <view class=\"other-group-price\">\n                <text class=\"group-price-value\">¥{{ item.groupPrice }}</text>\n                <text class=\"market-price-value\">¥{{ item.marketPrice }}</text>\n                <text class=\"discount-tag\">{{ item.discount || '6.2折热销中' }}</text>\n              </view>\n              <view class=\"other-group-bottom\">\n                <view class=\"sold-info\">\n                  <text class=\"sold-tag\">{{ item.soldTag || '新品' }}</text>\n                  <text class=\"sold-count\">{{ item.soldCount }}+人选择</text>\n                  <text class=\"usage-info\">{{ item.usageInfo || '到店使用' }}</text>\n                </view>\n                <view class=\"buy-now-btn\">\n                  <text>抢购</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 拼团信息卡片 -->\n      <view class=\"group-card\">\n        <view class=\"group-header\">\n          <view class=\"group-title\">\n            <image src=\"/static/demo/group-icon.png\" class=\"group-icon\"></image>\n            <text>{{groupbuy.minGroupSize || 3}}人团·已有{{groupbuy.soldCount}}人参与</text>\n          </view>\n          <view class=\"group-more\" @click=\"toggleGroupBuyMode\">\n            {{ groupbuy.enableGroupBuy ? '切换到直购模式' : '切换到拼团模式' }}\n          </view>\n        </view>\n        \n        <view class=\"group-location\">\n          <image src=\"/static/images/tabbar/位置.png\" class=\"location-icon\"></image>\n          <text class=\"location-text\">磁州市中心广场东路88号</text>\n        </view>\n        \n        <view class=\"group-avatars\">\n          <view class=\"avatar-item\" v-for=\"i in 5\" :key=\"i\">\n            <image :src=\"`/static/demo/avatar${i}.png`\" class=\"avatar-image\"></image>\n          </view>\n          <view class=\"avatar-more\">\n            <text>+{{groupbuy.soldCount - 5 > 0 ? groupbuy.soldCount - 5 : 0}}</text>\n          </view>\n          <view class=\"recent-join\">\n            <text class=\"recent-user\">张先生</text>\n            <text class=\"join-time\">刚刚参团</text>\n          </view>\n        </view>\n        \n        <view class=\"group-progress\">\n      <view class=\"progress-bar\">\n            <view class=\"progress-fill\" :style=\"{ width: progressWidth }\"></view>\n      </view>\n          <view class=\"progress-text\">\n            <text>已有{{groupbuy.soldCount}}人参团，目标{{groupbuy.targetCount}}人</text>\n      </view>\n    </view>\n    \n        <view class=\"group-tips\">\n          <text class=\"tip-icon cuIcon-info\"></text>\n          <text class=\"tip-text\">团购成功后，请到店核销，有效期7天</text>\n        </view>\n        \n        <view class=\"verification-info\">\n          <view class=\"verification-title\">\n            <text class=\"verification-icon cuIcon-location\"></text>\n            <text class=\"verification-text\">到店核销</text>\n          </view>\n          <view class=\"verification-address\">地址：磁州市中心广场东路88号</view>\n          <view class=\"verification-time\">营业时间：10:00-21:00</view>\n        </view>\n      </view>\n      \n      <view class=\"local-benefits\">\n        <view class=\"benefit-item\">\n          <image src=\"/static/images/tabbar/品质.png\" class=\"benefit-icon\"></image>\n          <text class=\"benefit-text\">本地商家</text>\n        </view>\n        <view class=\"benefit-item\">\n          <image src=\"/static/images/tabbar/准时.png\" class=\"benefit-icon\"></image>\n          <text class=\"benefit-text\">到店核销</text>\n        </view>\n        <view class=\"benefit-item\">\n          <image src=\"/static/images/tabbar/保障.png\" class=\"benefit-icon\"></image>\n          <text class=\"benefit-text\">品质保障</text>\n        </view>\n      </view>\n      \n      <!-- 优惠券卡片 -->\n      <view class=\"coupon-card\">\n        <view class=\"coupon-left\">\n          <text class=\"coupon-title\">新人专享券</text>\n          <text class=\"coupon-desc\">首单立减10元</text>\n        </view>\n        <view class=\"coupon-right\" @click=\"receiveCoupon\">\n          <text class=\"coupon-btn\">立即领取</text>\n        </view>\n      </view>\n      \n      <!-- 商家信息卡片 -->\n      <view class=\"shop-card\" @click=\"goToShop\">\n      <view class=\"shop-info\">\n          <image :src=\"groupbuy.shopLogo\" class=\"shop-logo\"></image>\n          <view class=\"shop-details\">\n        <text class=\"shop-name\">{{groupbuy.shopName}}</text>\n        <view class=\"shop-rating\">\n          <view class=\"rating-stars\">\n                <image v-for=\"i in 5\" :key=\"i\" \n                  :src=\"i <= Math.floor(groupbuy.shopRating) ? \n                    '/static/images/tabbar/星星-选中.png' : \n                    '/static/images/tabbar/星星-未选.png'\" \n                  class=\"star-icon\"></image>\n          </view>\n              <text class=\"rating-score\">{{groupbuy.shopRating}}</text>\n              <text class=\"rating-count\">({{groupbuy.ratingCount || 238}})</text>\n        </view>\n      </view>\n        </view>\n        <view class=\"shop-action\">\n        <text>进店</text>\n          <text class=\"cuIcon-right\"></text>\n      </view>\n    </view>\n    \n      <!-- 详情选项卡 -->\n      <view class=\"detail-tabs\">\n        <view class=\"tab-header\">\n          <view class=\"tab-item\" \n            v-for=\"(tab, index) in tabs\" \n            :key=\"index\"\n            :class=\"{ active: currentTab === index }\"\n            @click=\"switchTab(index)\">\n            <text>{{tab.name}}</text>\n            <view class=\"tab-line\" v-if=\"currentTab === index\"></view>\n      </view>\n    </view>\n    \n        <view class=\"tab-content\">\n    <!-- 商品详情 -->\n          <view v-if=\"currentTab === 0\" class=\"product-details\">\n        <view class=\"detail-desc\">{{groupbuy.description}}</view>\n        \n        <!-- 套餐内容 -->\n        <view class=\"package-content\">\n          <view class=\"package-title\">套餐内容</view>\n          <view class=\"package-items\">\n            <view class=\"package-item\" v-for=\"(item, index) in groupbuy.packageItems\" :key=\"index\">\n              <view class=\"package-item-header\">\n                <text class=\"package-item-name\">{{item.name}}</text>\n                <text class=\"package-item-quantity\">{{item.quantity}}{{item.unit}}</text>\n              </view>\n              <text class=\"package-item-desc\">{{item.desc}}</text>\n            </view>\n          </view>\n          <view class=\"package-notice\">\n            <text class=\"notice-title\">套餐须知：</text>\n            <text class=\"notice-text\">1. 套餐建议3人使用，可提前1小时预约</text>\n            <text class=\"notice-text\">2. 套餐内饮料可根据个人喜好替换</text>\n            <text class=\"notice-text\">3. 套餐内容不可拆分单独使用</text>\n          </view>\n        </view>\n        \n        <image v-for=\"(img, index) in groupbuy.detailImages\" \n          :key=\"index\" \n          :src=\"img\" \n          mode=\"widthFix\" \n          class=\"detail-image\"></image>\n      </view>\n          \n          <!-- 活动规则 -->\n          <view v-if=\"currentTab === 1\" class=\"product-rules\">\n            <view class=\"rule-item\" v-for=\"(rule, index) in groupbuy.rules\" :key=\"index\">\n              <text class=\"rule-number\">{{index + 1}}.</text>\n              <text class=\"rule-text\">{{rule}}</text>\n      </view>\n    </view>\n    \n    <!-- 用户评价 -->\n          <view v-if=\"currentTab === 2\" class=\"product-reviews\">\n      <view class=\"reviews-header\">\n              <text class=\"reviews-title\">用户评价({{groupbuy.reviews ? groupbuy.reviews.length : 0}})</text>\n              <text class=\"reviews-rate\">好评率 98%</text>\n            </view>\n            \n            <view class=\"review-tags\">\n              <view class=\"review-tag\" \n                v-for=\"(tag, index) in reviewTags\" \n                :key=\"index\" \n                :class=\"{ active: activeReviewTag === index }\"\n                @click=\"switchReviewTag(index)\">\n                {{tag}}\n        </view>\n      </view>\n      \n            <view class=\"review-item\" v-for=\"(review, index) in groupbuy.reviews\" :key=\"index\">\n          <view class=\"review-header\">\n            <image :src=\"review.avatar\" class=\"user-avatar\"></image>\n            <view class=\"review-user\">\n              <text class=\"user-name\">{{review.username}}</text>\n              <view class=\"review-rating\">\n                <image src=\"/static/images/tabbar/星星-选中.png\" class=\"star\" v-for=\"i in review.rating\" :key=\"i\"></image>\n                <image src=\"/static/images/tabbar/星星-未选.png\" class=\"star\" v-for=\"i in 5-review.rating\" :key=\"i+5\"></image>\n              </view>\n            </view>\n            <text class=\"review-time\">{{review.time}}</text>\n          </view>\n          <view class=\"review-content\">{{review.content}}</view>\n          <view class=\"review-images\" v-if=\"review.images && review.images.length > 0\">\n            <image v-for=\"(img, imgIndex) in review.images\" :key=\"imgIndex\" :src=\"img\" mode=\"aspectFill\" class=\"review-image\"></image>\n          </view>\n              \n              <view class=\"review-actions\">\n                <view class=\"review-action\" @click=\"likeReview(index)\">\n                  <text class=\"cuIcon-appreciate\"></text>\n                  <text>赞同(23)</text>\n                </view>\n                <view class=\"review-action\" @click=\"replyReview(index)\">\n                  <text class=\"cuIcon-comment\"></text>\n                  <text>回复(5)</text>\n                </view>\n        </view>\n      </view>\n      \n            <view class=\"empty-reviews\" v-if=\"!groupbuy.reviews || groupbuy.reviews.length === 0\">\n        <text>暂无评价</text>\n            </view>\n            \n            <view class=\"more-reviews\" v-if=\"groupbuy.reviews && groupbuy.reviews.length > 0\" @click=\"viewMoreReviews\">\n              <text>查看全部评价</text>\n              <text class=\"cuIcon-right\"></text>\n            </view>\n          </view>\n      </view>\n    </view>\n      \n      <!-- 推荐商品 -->\n      <view class=\"recommend-section\">\n        <view class=\"recommend-header\">\n          <text class=\"recommend-title\">猜你喜欢</text>\n        </view>\n        <view class=\"recommend-list\">\n          <view class=\"recommend-item\" v-for=\"item in recommendProducts\" :key=\"item.id\" @click=\"viewRecommendProduct(item.id)\">\n            <image :src=\"item.image\" class=\"recommend-image\"></image>\n            <view class=\"recommend-info\">\n              <text class=\"recommend-name\">{{item.name}}</text>\n              <view class=\"recommend-price\">\n                <text class=\"price-symbol\">¥</text>\n                <text class=\"price-value\">{{item.price}}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 底部安全区 -->\n      <view class=\"safe-area-inset-bottom\"></view>\n    \n    <!-- 底部购买栏 -->\n    <view class=\"bottom-bar\">\n      <view class=\"bottom-left\">\n        <view class=\"action-btn\" @click=\"contactService\">\n          <image src=\"/static/images/tabbar/客服.png\" class=\"action-icon\"></image>\n          <text>客服</text>\n        </view>\n        <view class=\"action-btn\" @click=\"goToShop\">\n          <image src=\"/static/images/tabbar/店铺.png\" class=\"action-icon\"></image>\n          <text>店铺</text>\n        </view>\n        <view class=\"action-btn\" @click=\"toggleFavorite\">\n          <image :src=\"isFavorite ? '/static/images/tabbar/收藏-选中.png' : '/static/images/tabbar/收藏.png'\" class=\"action-icon\"></image>\n          <text>收藏</text>\n        </view>\n        <view class=\"action-btn\" @click=\"share\">\n          <image src=\"/static/images/tabbar/分享.png\" class=\"action-icon\"></image>\n          <text>分享</text>\n        </view>\n      </view>\n      \n      <view class=\"buy-buttons\" v-if=\"groupbuy.enableGroupBuy\">\n        <view class=\"buy-btn normal-buy\" @click=\"buyNow\">\n          <view class=\"buy-price\">¥{{formattedRegularPrice}}</view>\n          <view class=\"buy-label\">单独购买</view>\n        </view>\n        <view class=\"buy-btn group-buy\" @click=\"groupBuy\">\n          <view class=\"buy-price-row\">\n            <text class=\"buy-price\">¥{{formattedGroupPrice}}</text>\n            <text class=\"original-tag\">¥{{formattedMarketPrice}}</text>\n          </view>\n          <view class=\"buy-label\">\n            <text>{{groupbuy.minGroupSize || 3}}人团</text>\n            <text class=\"save-amount\">省¥{{saveAmount}}</text>\n          </view>\n          <view class=\"verify-tag\">到店核销</view>\n        </view>\n      </view>\n      \n      <view class=\"buy-buttons\" v-else>\n        <view class=\"buy-btn group-buy full-width\" @click=\"buyNow\">\n          <view class=\"buy-price-row\">\n            <text class=\"buy-price\">¥{{formattedGroupPrice}}</text>\n            <text class=\"original-tag\">¥{{formattedMarketPrice}}</text>\n          </view>\n          <view class=\"buy-label\">\n            <text>立即购买</text>\n            <text class=\"save-amount\">省¥{{saveAmount}}</text>\n          </view>\n          <view class=\"verify-tag\">到店核销</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 购买弹窗 -->\n    <view class=\"buy-popup\" v-if=\"showBuyPopup\">\n      <view class=\"popup-mask\" @click=\"closeBuyPopup\"></view>\n      <view class=\"popup-content\">\n        <view class=\"popup-header\">\n          <image :src=\"groupbuy.images[0]\" class=\"popup-product-image\"></image>\n          <view class=\"popup-product-info\">\n            <text class=\"popup-price\">¥{{currentBuyType === 'group' ? formattedGroupPrice : formattedRegularPrice}}</text>\n            <text class=\"popup-stock\">库存: {{groupbuy.stock}}件</text>\n            <view class=\"popup-verify-tag\">到店核销</view>\n          </view>\n          <image src=\"/static/images/tabbar/关闭.png\" class=\"popup-close\" @click=\"closeBuyPopup\"></image>\n        </view>\n        \n          <view class=\"popup-section\">\n            <text class=\"section-title\">购买数量</text>\n            <view class=\"quantity-selector\">\n              <view class=\"quantity-btn\" @click=\"decreaseQuantity\">-</view>\n              <input type=\"number\" v-model=\"buyQuantity\" class=\"quantity-input\" />\n              <view class=\"quantity-btn\" @click=\"increaseQuantity\">+</view>\n            </view>\n          </view>\n        \n        <view class=\"popup-verify-info\">\n          <text class=\"verify-title\">使用须知</text>\n          <text class=\"verify-item\">· 购买后7天内有效</text>\n          <text class=\"verify-item\">· 到店出示券码核销</text>\n          <text class=\"verify-item\">· 每日10:00-21:00可用</text>\n        </view>\n        \n        <view class=\"popup-footer\">\n          <view class=\"popup-buy-btn\" @click=\"confirmBuy\">\n            立即购买\n          </view>\n        </view>\n      </view>\n    </view>\n      \n    <!-- 分享弹窗 -->\n    <view class=\"share-popup\" v-if=\"showSharePopup\">\n      <view class=\"popup-mask\" @click=\"closeSharePopup\"></view>\n      <view class=\"share-content\">\n        <view class=\"share-header\">\n          <text>分享到</text>\n          <image src=\"/static/images/tabbar/关闭.png\" class=\"popup-close\" @click=\"closeSharePopup\"></image>\n        </view>\n        <view class=\"share-options\">\n          <view class=\"share-option\" @click=\"share\">\n            <image src=\"/static/demo/wechat.png\" class=\"share-icon\"></image>\n            <text>微信</text>\n          </view>\n          <view class=\"share-option\" @click=\"share\">\n            <image src=\"/static/demo/moments.png\" class=\"share-icon\"></image>\n            <text>朋友圈</text>\n          </view>\n          <view class=\"share-option\" @click=\"share\">\n            <image src=\"/static/demo/qq.png\" class=\"share-icon\"></image>\n            <text>QQ</text>\n          </view>\n          <view class=\"share-option\" @click=\"share\">\n            <image src=\"/static/demo/weibo.png\" class=\"share-icon\"></image>\n            <text>微博</text>\n          </view>\n        </view>\n        <view class=\"share-poster\" @click=\"generatePoster\">\n          <text>生成海报</text>\n        </view>\n      </view>\n    </view>\n    </block>\n  </view>\n</template>\n\n<script>\nimport { smartNavigate } from '@/utils/navigation.js';\nimport DistributionSection from '@/components/distribution-section.vue';\n\nexport default {\n  components: {\n    DistributionSection\n  },\n  data() {\n    return {\n      id: null,\n      type: 'group', // 默认为拼团活动\n      groupbuy: {},\n      shop: {},\n      statusBarHeight: 20,\n      navbarHeight: 82,\n      tabs: [\n        { name: '商品详情' },\n        { name: '活动规则' },\n        { name: '用户评价' }\n      ],\n      currentTab: 0,\n      currentSwiperIndex: 0,\n      countdown: {\n        days: 0,\n        hours: 0,\n        minutes: 0,\n        seconds: 0\n      },\n      countdownTimer: null,\n      loading: true,\n      reviewTags: ['全部', '有图(12)', '好评(32)', '差评(1)'],\n      showBuyPopup: false,\n      buyQuantity: 1,\n      currentBuyType: 'normal',\n      showSharePopup: false,\n      activeReviewTag: 0,\n      isFavorite: false,\n      recommendProducts: [\n        {\n          id: 1,\n          name: '老磁州水饺',\n          price: 25.9,\n          image: '/static/demo/food2.jpg'\n        },\n        {\n          id: 2,\n          name: '特色烤肉套餐',\n          price: 59.9,\n          image: '/static/demo/food1.jpg'\n        },\n        {\n          id: 3,\n          name: '精品麻辣烫',\n          price: 19.9,\n          image: '/static/demo/food3.jpg'\n        },\n        {\n          id: 4,\n          name: '招牌炸鸡',\n          price: 39.9,\n          image: '/static/demo/food4.jpg'\n        }\n      ],\n      // 适用门店列表\n      storeList: [\n        {\n          id: 1,\n          name: '老磁州美食坊(中心广场店)',\n          logo: '/static/demo/shop-logo.png',\n          address: '磁州市中心广场东路88号',\n          distance: '1km'\n        },\n        {\n          id: 2,\n          name: '老磁州美食坊(西湖店)',\n          logo: '/static/demo/shop-logo.png',\n          address: '磁州市西湖路120号',\n          distance: '3.6km'\n        },\n        {\n          id: 3,\n          name: '老磁州美食坊(北城店)',\n          logo: '/static/demo/shop-logo.png',\n          address: '磁州市北城区商业街56号',\n          distance: '5.2km'\n        }\n      ],\n      // 本店其他拼团\n      otherGroups: [\n        {\n          id: 101,\n          title: '老磁州特色凉皮 2人套餐',\n          image: '/static/demo/food2.jpg',\n          groupPrice: '29.9',\n          marketPrice: '59.9',\n          groupSize: 2,\n          soldCount: 3000,\n          discount: '5.3折热销中',\n          distance: '1km',\n          soldTag: '新品',\n          usageInfo: '到店使用',\n          shopName: '磁州同城折扣店'\n        },\n        {\n          id: 102,\n          title: '老磁州肉夹馍 家庭套餐',\n          image: '/static/demo/food3.jpg',\n          groupPrice: '39.9',\n          marketPrice: '69.9',\n          groupSize: 3,\n          soldCount: 1000,\n          discount: '6.2折热销中',\n          distance: '1.2km',\n          soldTag: '',\n          usageInfo: '到店使用',\n          shopName: '磁州同城折扣店'\n        },\n        {\n          id: 103,\n          title: '老磁州特色小吃拼盘',\n          image: '/static/demo/food1.jpg',\n          groupPrice: '49.9',\n          marketPrice: '79.9',\n          groupSize: 4,\n          soldCount: 900,\n          discount: '5.8折热销中',\n          distance: '1.5km',\n          soldTag: '新品',\n          usageInfo: '免预约',\n          shopName: '磁州同城折扣店'\n        }\n      ]\n    };\n  },\n  computed: {\n    // 根据活动类型显示对应的标题\n    activityTypeTitle() {\n      switch(this.type) {\n        case 'flash':\n          return '秒杀详情';\n        case 'group':\n          return '拼团详情';\n        case 'discount':\n          return '满减详情';\n        case 'coupon':\n          return '优惠券详情';\n        default:\n          return '活动详情';\n      }\n    },\n    \n    // 计算折扣\n    discount() {\n      const originalPrice = parseFloat(this.groupbuy.originalPrice);\n      const groupPrice = parseFloat(this.groupbuy.groupPrice);\n      if (originalPrice <= 0) return 10;\n      const discount = Math.round((groupPrice / originalPrice) * 10);\n      return discount;\n    },\n    \n    // 计算折扣百分比\n    discountPercent() {\n      const marketPrice = parseFloat(this.groupbuy.marketPrice || this.groupbuy.originalPrice);\n      const groupPrice = parseFloat(this.groupbuy.groupPrice);\n      if (marketPrice <= 0) return 0;\n      const discountPercent = Math.round(100 - (groupPrice / marketPrice) * 100);\n      return discountPercent;\n    },\n    \n    // 计算节省金额\n    saveAmount() {\n      const marketPrice = parseFloat(this.groupbuy.marketPrice || this.groupbuy.originalPrice);\n      const groupPrice = parseFloat(this.groupbuy.groupPrice);\n      return (marketPrice - groupPrice).toFixed(2);\n    },\n    \n    // 计算进度条宽度\n    progressWidth() {\n      if (this.groupbuy.targetCount <= 0) return '0%';\n      const progress = (this.groupbuy.soldCount / this.groupbuy.targetCount) * 100;\n      return progress > 100 ? '100%' : `${progress}%`;\n    },\n    \n    // 格式化团购价格\n    formattedGroupPrice() {\n      // 如果是数字，转换为带两位小数的字符串\n      if (typeof this.groupbuy.groupPrice === 'number') {\n        return this.groupbuy.groupPrice.toFixed(2);\n      }\n      return this.groupbuy.groupPrice;\n    },\n    \n    // 格式化原价\n    formattedOriginalPrice() {\n      // 如果是数字，转换为带两位小数的字符串\n      if (typeof this.groupbuy.originalPrice === 'number') {\n        return this.groupbuy.originalPrice.toFixed(2);\n      }\n      return this.groupbuy.originalPrice;\n    },\n    \n    // 格式化市场价\n    formattedMarketPrice() {\n      if (typeof this.groupbuy.marketPrice === 'number') {\n        return this.groupbuy.marketPrice.toFixed(2);\n      }\n      return this.groupbuy.marketPrice || this.formattedOriginalPrice;\n    },\n    \n    // 格式化日常价\n    formattedRegularPrice() {\n      if (typeof this.groupbuy.regularPrice === 'number') {\n        return this.groupbuy.regularPrice.toFixed(2);\n      }\n      return this.groupbuy.regularPrice || this.formattedOriginalPrice;\n    },\n    \n    // 计算与市场价的差价\n    marketPriceDiff() {\n      const marketPrice = parseFloat(this.groupbuy.marketPrice || this.groupbuy.originalPrice);\n      const groupPrice = parseFloat(this.groupbuy.groupPrice);\n      return (marketPrice - groupPrice).toFixed(2);\n    },\n    \n    // 计算与日常价的差价\n    regularPriceDiff() {\n      const regularPrice = parseFloat(this.groupbuy.regularPrice || this.groupbuy.originalPrice);\n      const groupPrice = parseFloat(this.groupbuy.groupPrice);\n      return (regularPrice - groupPrice).toFixed(2);\n    },\n    \n    // 参与人数展示\n    participantCount() {\n      return this.groupbuy.soldCount > 0 ? this.groupbuy.soldCount : 0;\n    },\n    \n    // 剩余参与人数\n    remainingCount() {\n      const remaining = this.groupbuy.targetCount - this.groupbuy.soldCount;\n      return remaining > 0 ? remaining : 0;\n    },\n    \n    // 是否已达到目标人数\n    isTargetReached() {\n      return this.groupbuy.soldCount >= this.groupbuy.targetCount;\n    }\n  },\n  onLoad(options) {\n    if (options && options.id) {\n      this.id = options.id;\n    }\n    \n    // 获取活动类型\n    if (options && options.type) {\n      this.type = options.type;\n    }\n    \n    // 获取状态栏高度\n    const systemInfo = uni.getSystemInfoSync();\n    this.statusBarHeight = systemInfo.statusBarHeight;\n    this.navbarHeight = this.statusBarHeight + 62; // 状态栏 + 标题栏高度\n    \n    // 模拟加载数据\n    setTimeout(() => {\n      this.loadGroupbuyDetail();\n      this.startCountdown();\n    }, 500);\n  },\n  onUnload() {\n    // 清除倒计时定时器\n    if (this.countdownTimer) {\n      clearInterval(this.countdownTimer);\n      this.countdownTimer = null;\n    }\n  },\n  // 分享功能\n  onShareAppMessage() {\n    return {\n      title: this.groupbuy.title,\n      path: `/subPackages/activity-showcase/pages/detail/index?id=${this.id}`,\n      imageUrl: this.groupbuy.images[0]\n    };\n  },\n  methods: {\n    // 加载团购详情\n    loadGroupbuyDetail() {\n      // 模拟API加载数据\n      this.loading = true;\n      \n      // 在实际应用中，这里应该是从API获取数据\n      setTimeout(() => {\n        // 模拟数据\n        this.groupbuy = {\n          id: this.id || 1,\n          title: '【磁州特色】老磁州脆皮锅贴 正宗手工制作 20个/份',\n          images: [\n            '/static/demo/food1.jpg',\n            '/static/demo/food2.jpg',\n            '/static/demo/food3.jpg'\n          ],\n          groupPrice: '39.90',\n          originalPrice: '59.90',\n          marketPrice: '69.90',\n          regularPrice: '49.90',\n          soldCount: 126,\n          targetCount: 200,\n          startTime: new Date('2023-12-01'),\n          endTime: new Date('2023-12-31'),\n          tags: ['限时特惠', '热销爆款', '免预约'],\n          shopName: '老磁州美食坊',\n          shopLogo: '/static/demo/shop-logo.png',\n          shopRating: 4.8,\n          ratingCount: 238,\n          stock: 500,\n          description: '老磁州脆皮锅贴，采用传统工艺精心制作，外皮金黄酥脆，内馅鲜嫩多汁，是磁州地区特色美食。本店锅贴选用上等面粉和新鲜猪肉制作，不添加任何防腐剂和人工色素。',\n          detailImages: [\n            '/static/demo/detail1.jpg',\n            '/static/demo/detail2.jpg'\n          ],\n          rules: [\n            '团购券有效期为购买后30天内，请在有效期内使用',\n            '营业时间：周一至周日 10:00-22:00，法定节假日正常营业',\n            '每张团购券限1人使用，不再与店内其他优惠同享',\n            '团购券使用前请提前致电商家预约',\n            '如有特殊情况，请与商家协商解决'\n          ],\n          reviews: [\n            {\n              id: 1,\n              user: {\n                name: '张先生',\n                avatar: '/static/demo/avatar1.png'\n              },\n              rating: 5,\n              content: '味道很赞，皮薄馅大，一口下去超级满足！店家服务也很好，环境整洁，值得推荐！',\n              images: [\n                '/static/demo/review1.jpg',\n                '/static/demo/review2.jpg'\n              ],\n              time: '2023-11-28',\n              likes: 12\n            },\n            {\n              id: 2,\n              user: {\n                name: '李女士',\n                avatar: '/static/demo/avatar2.png'\n              },\n              rating: 4,\n              content: '第二次来购买了，锅贴依然那么好吃，就是今天人有点多，等了一会儿。',\n              images: [],\n              time: '2023-11-25',\n              likes: 5\n            }\n          ],\n          prices: [\n            {\n              id: 1,\n              name: '标准20个装',\n              price: 39.9\n            },\n            {\n              id: 2,\n              name: '超值30个装',\n              price: 59.9\n            },\n            {\n              id: 3,\n              name: '家庭40个装',\n              price: 75.9\n            }\n          ],\n          packageItems: [\n            {\n              name: '脆皮锅贴',\n              quantity: 20,\n              unit: '个',\n              desc: '传统手工制作，外皮酥脆，内馅多汁'\n            },\n            {\n              name: '特制蘸料',\n              quantity: 2,\n              unit: '份',\n              desc: '秘制配方，提味增香'\n            }\n          ],\n          enableGroupBuy: true,\n          minGroupSize: 3\n        };\n        \n        this.shop = {\n          id: 1,\n          name: this.groupbuy.shopName,\n          logo: this.groupbuy.shopLogo,\n          rating: this.groupbuy.shopRating,\n          isFollowed: false\n        };\n        \n        this.loading = false;\n      }, 1000);\n    },\n    \n    // 开始倒计时\n    startCountdown() {\n      // 先立即更新一次\n      this.updateCountdown();\n      \n      // 设置定时器，每秒更新一次\n      this.countdownTimer = setInterval(() => {\n        this.updateCountdown();\n      }, 1000);\n    },\n    \n    // 更新倒计时\n    updateCountdown() {\n      if (!this.groupbuy || !this.groupbuy.endTime) return;\n      \n      const now = new Date().getTime();\n      const endTime = new Date(this.groupbuy.endTime).getTime();\n      const diff = endTime - now;\n      \n      if (diff <= 0) {\n        // 活动已结束\n        this.countdown = { days: 0, hours: 0, minutes: 0, seconds: 0 };\n        if (this.countdownTimer) {\n          clearInterval(this.countdownTimer);\n          this.countdownTimer = null;\n        }\n        return;\n      }\n      \n      // 计算天、时、分、秒\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n      \n      // 更新倒计时数据\n      this.countdown = {\n        days: days < 10 ? '0' + days : days,\n        hours: hours < 10 ? '0' + hours : hours,\n        minutes: minutes < 10 ? '0' + minutes : minutes,\n        seconds: seconds < 10 ? '0' + seconds : seconds\n      };\n    },\n    \n    // 获取模拟数据\n    getMockData(id) {\n      // 转为数字\n      id = parseInt(id);\n      \n      // 通用模拟数据\n      const mockData = {\n        id: id,\n        title: '商户特惠 | 正宗磁州肉夹馍 3人套餐',\n        images: [\n          '/static/demo/food1.jpg',\n          '/static/demo/food2.jpg',\n          '/static/demo/food3.jpg'\n        ],\n        groupPrice: 59.90,\n        originalPrice: 99.00,\n        marketPrice: 99.00,\n        regularPrice: 79.00,\n        soldCount: 358,\n        targetCount: 500,\n        startTime: new Date('2023-08-15 10:00:00'),\n        endTime: new Date(Date.now() + 86400000 * 3), // 3天后结束\n        tags: ['本地美食', '限时特惠', '当日现做'],\n        shopName: '老磁州肉夹馍',\n        shopLogo: '/static/demo/shop-logo.png',\n        shopRating: 4.9,\n        ratingCount: 238,\n        stock: 200,\n        description: '精选五花肉，配以秘制调料，现烤现卖，外酥里嫩，肉香四溢。3人套餐包含肉夹馍3个、凉皮2份、特色饮料3杯，到店消费更有特色小吃免费品尝！',\n        detailImages: [\n          '/static/demo/food-detail1.jpg',\n          '/static/demo/food-detail2.jpg'\n        ],\n        packageItems: [\n          { name: '招牌肉夹馍', quantity: 3, unit: '个', desc: '精选五花肉，外酥里嫩' },\n          { name: '特色凉皮', quantity: 2, unit: '份', desc: '劲道爽滑，酸辣可口' },\n          { name: '饮料', quantity: 3, unit: '杯', desc: '可乐/雪碧/果茶任选' },\n          { name: '赠品小食', quantity: 1, unit: '份', desc: '薯条/鸡米花任选一份' }\n        ],\n        rules: [\n          '有效期：购买后7天内有效',\n          '使用方式：到店出示券码核销',\n          '营业时间：每日10:00-21:00',\n          '地址：磁州市中心广场东路88号',\n          '每人限购5份',\n          '支持随时退款',\n          '团购专享价格',\n          '到店消费可免费获赠热茶一杯'\n        ],\n        enableGroupBuy: true, // 是否开启拼团\n        minGroupSize: 3, // 最低成团人数\n        reviews: [\n          {\n            username: '张先生',\n            avatar: '/static/demo/avatar1.png',\n            rating: 5,\n            time: '2023-10-15',\n            content: '肉夹馍非常好吃，肉多且入味，饼也烤得恰到好处，酥脆可口。团购价格很实惠，强烈推荐！',\n            images: [\n              '/static/demo/review1.jpg',\n              '/static/demo/review2.jpg'\n            ]\n          },\n          {\n            username: '李女士',\n            avatar: '/static/demo/avatar2.png',\n            rating: 5,\n            time: '2023-10-12',\n            content: '配送速度很快，包装也很好，肉夹馍还是热乎的。味道很正宗，和老家的味道一样，很满意。',\n            images: [\n              '/static/demo/review3.jpg'\n            ]\n          }\n        ],\n        prices: [\n          { label: '市场价', value: '¥30' },\n          { label: '日常价', value: '¥25' }\n        ],\n        viewCount: 1268,\n      };\n      \n      // 根据ID返回不同数据\n      if (id === 102) {\n        mockData.title = '夏日清凉 | 网红冰淇淋双人套餐';\n        mockData.groupPrice = 39.9;\n        mockData.originalPrice = 68;\n        mockData.regularPrice = 58;\n        mockData.soldCount = 128;\n        mockData.shopName = '冰雪甜品屋';\n        mockData.description = '精选进口食材，纯手工制作，口感细腻，甜而不腻。套餐包含：招牌冰淇淋2份，水果沙拉1份，特调饮品2杯。';\n      } else if (id === 103) {\n        mockData.title = '周末特惠 | 精致下午茶套餐';\n        mockData.groupPrice = 88;\n        mockData.originalPrice = 138;\n        mockData.regularPrice = 118;\n        mockData.soldCount = 42;\n        mockData.shopName = '巴黎花园咖啡';\n        mockData.description = '法式精致下午茶，环境优雅，食材新鲜。套餐包含：精选茶饮2杯，马卡龙4个，水果塔2个，提拉米苏1份。';\n      } else if (id === 104) {\n        mockData.title = '磁州特色 | 手工水饺30个';\n        mockData.groupPrice = 29.9;\n        mockData.originalPrice = 45;\n        mockData.regularPrice = 39;\n        mockData.soldCount = 84;\n        mockData.shopName = '老街饺子馆';\n        mockData.description = '选用本地新鲜食材，现包现煮，皮薄馅大，鲜香可口。可选口味：猪肉白菜、韭菜鸡蛋、三鲜、牛肉。';\n      } else if (id === 105) {\n        mockData.title = '本地特色 | 正宗磁州烤肉套餐';\n        mockData.groupPrice = 99;\n        mockData.originalPrice = 168;\n        mockData.regularPrice = 138;\n        mockData.soldCount = 36;\n        mockData.shopName = '老街烤肉';\n        mockData.description = '传统炭火烤制，选用本地散养黑猪肉，搭配秘制调料，肉质鲜嫩多汁。套餐包含：烤肉拼盘、特色小菜4份、米饭2份。';\n      }\n      \n      return mockData;\n    },\n    \n    // 格式化日期\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = date.getMonth() + 1;\n      const day = date.getDate();\n      return `${year}年${month}月${day}日`;\n    },\n    \n    // 导航到商家页面\n    goToShop() {\n      smartNavigate(`/pages/business/shop-detail?id=${this.groupbuy.id}`);\n    },\n    \n    // 切换选项卡\n    switchTab(index) {\n      this.currentTab = index;\n    },\n    \n    // 联系客服\n    contactService() {\n      uni.showToast({\n        title: '正在连接客服...',\n        icon: 'none'\n      });\n    },\n    \n    // 切换收藏状态\n    toggleFavorite() {\n      this.isFavorite = !this.isFavorite;\n      uni.showToast({\n        title: this.isFavorite ? '收藏成功' : '已取消收藏',\n        icon: 'success'\n      });\n    },\n    \n    // 切换关注商家状态\n    toggleFollow() {\n      if (this.shop) {\n        this.shop.isFollowed = !this.shop.isFollowed;\n        uni.showToast({\n          title: this.shop.isFollowed ? '已关注' : '已取消关注',\n          icon: 'success'\n        });\n      }\n    },\n    \n    // 单独购买\n    buyNow() {\n      this.currentBuyType = 'normal';\n      this.showBuyPopup = true;\n    },\n    \n    // 团购购买\n    groupBuy() {\n      this.currentBuyType = 'group';\n      this.showBuyPopup = true;\n    },\n    \n    // 关闭购买弹窗\n    closeBuyPopup() {\n      this.showBuyPopup = false;\n    },\n    \n    // 减少购买数量\n    decreaseQuantity() {\n      if (this.buyQuantity > 1) {\n        this.buyQuantity--;\n      }\n    },\n    \n    // 增加购买数量\n    increaseQuantity() {\n      if (this.buyQuantity < this.groupbuy.stock) {\n        this.buyQuantity++;\n      } else {\n        uni.showToast({\n          title: '已达到最大库存',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 确认购买\n    confirmBuy() {\n      const price = this.currentBuyType === 'group' ? this.groupbuy.groupPrice : this.groupbuy.originalPrice;\n      const totalPrice = (parseFloat(price) * this.buyQuantity).toFixed(2);\n      \n      uni.showModal({\n        title: '确认购买',\n        content: `您将以¥${totalPrice}购买${this.buyQuantity}件商品，是否确认？`,\n        success: (res) => {\n          if (res.confirm) {\n            // 处理购买逻辑\n            uni.showLoading({\n              title: '正在下单'\n            });\n            \n            // 模拟API请求\n            setTimeout(() => {\n              uni.hideLoading();\n              this.showBuyPopup = false;\n              \n              // 跳转到支付页面\n              smartNavigate(`/pages/pay/index?amount=${totalPrice}&title=${encodeURIComponent(this.groupbuy.title)}`);\n            }, 800);\n          }\n        }\n      });\n    },\n    \n    // 轮播图切换\n    onSwiperChange(e) {\n      this.currentSwiperIndex = e.detail.current;\n    },\n    \n    // 分享功能\n    share() {\n      this.showSharePopup = true;\n    },\n    \n    // 关闭分享弹窗\n    closeSharePopup() {\n      this.showSharePopup = false;\n    },\n    \n    // 切换评价标签\n    switchReviewTag(index) {\n      this.activeReviewTag = index;\n    },\n    \n    // 点赞评价\n    likeReview(index) {\n      uni.showToast({\n        title: '点赞成功',\n        icon: 'success'\n      });\n    },\n    \n    // 回复评价\n    replyReview(index) {\n      uni.showToast({\n        title: '暂不支持回复',\n        icon: 'none'\n      });\n    },\n    \n    // 查看更多评价\n    viewMoreReviews() {\n      uni.showToast({\n        title: '查看更多评价',\n        icon: 'none'\n      });\n    },\n    \n    // 领取优惠券\n    receiveCoupon() {\n      uni.showToast({\n        title: '优惠券领取成功',\n        icon: 'success'\n      });\n    },\n    \n    // 切换拼团/直购模式（仅用于演示）\n    toggleGroupBuyMode() {\n      this.groupbuy.enableGroupBuy = !this.groupbuy.enableGroupBuy;\n      // 显示提示\n      uni.showToast({\n        title: this.groupbuy.enableGroupBuy ? '已切换到拼团模式' : '已切换到直购模式',\n        icon: 'none'\n      });\n    },\n    \n    // 格式化价格\n    formatPrice(price) {\n      // ... existing code ...\n    },\n    \n    // 生成分享海报\n    generatePoster() {\n      uni.showLoading({\n        title: '生成中...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: '海报已生成',\n          icon: 'success'\n        });\n        this.closeSharePopup();\n      }, 1500);\n    },\n    \n    // 返回上一页\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    // 查看推荐商品\n    viewRecommendProduct(productId) {\n      smartNavigate(`/subPackages/activity-showcase/pages/detail/index?id=${productId}`);\n    },\n    \n    // 查看所有适用门店\n    viewAllStores() {\n      uni.showToast({\n        title: '查看全部门店',\n        icon: 'none'\n      });\n    },\n    \n    // 查看门店详情\n    viewStoreDetail(storeId) {\n      smartNavigate(`/pages/business/store-detail?id=${storeId}`);\n    },\n    \n    // 查看更多拼团\n    viewMoreGroups() {\n      smartNavigate('/subPackages/activity-showcase/pages/group-buy/index');\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n/* 整体容器 */\n.detail-container {\n  background-color: #F5F7FA;\n  min-height: 100vh;\n  padding-bottom: 120rpx;\n}\n\n/* 导航栏 */\n.nav-bar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 100;\n}\n\n.nav-content {\n  display: flex;\n  align-items: center;\n  height: 80rpx;\n}\n\n.nav-icon {\n  width: 24rpx;\n  height: 24rpx;\n  padding: 0 10rpx;\n}\n\n.nav-title {\n  font-size: 24rpx;\n  font-weight: 500;\n}\n\n.back-button {\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 38rpx;\n  height: 38rpx;\n}\n\n/* 商品轮播图 */\n.product-swiper {\n  height: 520rpx; /* 从580rpx进一步减小到520rpx */\n  width: 100%;\n  border-radius: 0 0 24rpx 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n}\n\n.swiper-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 商品基本信息卡片 */\n.basic-info-card {\n  margin: -30rpx 24rpx 24rpx; /* 从-40rpx调整为-30rpx */\n  padding: 24rpx 30rpx; /* 从30rpx调整为24rpx 30rpx，减小上下内边距 */\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.05);\n  position: relative;\n  z-index: 10;\n}\n\n.price-section {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  margin-bottom: 12rpx; /* 从16rpx减小到12rpx */\n}\n\n.price-main {\n  display: flex;\n  align-items: baseline;\n}\n\n.price-symbol {\n  font-size: 36rpx;\n  font-weight: 500;\n  color: #FF3B30;\n}\n\n.price-value {\n  font-size: 54rpx; /* 从60rpx减小到54rpx */\n  font-weight: 700;\n  color: #FF3B30;\n  line-height: 1;\n}\n\n.price-tag {\n  background-color: #FF3B30;\n  color: #FFFFFF;\n  font-size: 24rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n  margin-left: 12rpx;\n}\n\n.price-tag-text {\n  font-size: 24rpx;\n  color: #FFFFFF;\n  font-weight: 500;\n}\n\n.price-compare {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n\n.compare-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 6rpx;\n}\n\n.compare-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-right: 8rpx;\n}\n\n.compare-price {\n  font-size: 26rpx;\n  color: #8E8E93;\n  text-decoration: line-through;\n}\n\n.save-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx; /* 从20rpx减小到16rpx */\n}\n\n.save-tag {\n  display: flex;\n  align-items: center;\n  background-color: rgba(255, 59, 48, 0.1);\n  padding: 6rpx 16rpx;\n  border-radius: 8rpx;\n}\n\n.save-icon {\n  width: 32rpx;\n  height: 32rpx;\n  background-color: #FF3B30;\n  color: #FFFFFF;\n  font-size: 20rpx;\n  text-align: center;\n  line-height: 32rpx;\n  border-radius: 4rpx;\n  margin-right: 8rpx;\n}\n\n.save-text {\n  font-size: 24rpx;\n  color: #FF3B30;\n  font-weight: 600;\n}\n\n.limited-text {\n  font-size: 24rpx;\n  color: #FF9500;\n  font-weight: 600;\n}\n\n.market-price {\n  font-size: 28rpx;\n  color: #8E8E93;\n  text-decoration: line-through;\n  margin-right: 12rpx;\n}\n\n.product-title-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx; /* 从24rpx减小到20rpx */\n}\n\n.group-tag {\n  background-color: #FF3B30;\n  color: #FFFFFF;\n  font-size: 24rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n}\n\n.product-title {\n  font-size: 32rpx; /* 从36rpx减小到32rpx */\n  font-weight: 700;\n  color: #000000;\n}\n\n/* 销量和倒计时 */\n.sales-countdown {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  background-color: rgba(255, 59, 48, 0.1);\n  padding: 16rpx;\n  border-radius: 12rpx;\n}\n\n.sales-info {\n  display: flex;\n  align-items: center;\n}\n\n.sales-count {\n  font-size: 28rpx;\n  color: #8E8E93;\n  margin-right: 16rpx;\n}\n\n.sales-divider {\n  color: #8E8E93;\n  margin: 0 16rpx;\n}\n\n.view-count {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n\n.mini-countdown {\n  display: flex;\n  align-items: center;\n}\n\n.countdown-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  margin-right: 16rpx;\n}\n\n.time-block {\n  background-color: #FF3B30;\n  color: #FFFFFF;\n  font-size: 28rpx;\n  font-weight: 600;\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8rpx;\n}\n\n.time-colon {\n  color: #FF3B30;\n  margin: 0 8rpx;\n  font-weight: 600;\n}\n\n/* 拼团信息卡片 */\n.group-card {\n  margin: 0 24rpx 24rpx;\n  padding: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n}\n\n.group-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.group-title {\n  display: flex;\n  align-items: center;\n}\n\n.group-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 12rpx;\n}\n\n.group-more {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.group-location {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n  padding: 10rpx 0;\n  border-bottom: 1rpx dashed #F2F2F7;\n}\n\n.location-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 8rpx;\n}\n\n.location-text {\n  font-size: 26rpx;\n  color: #333333;\n}\n\n.group-avatars {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24rpx;\n}\n\n.avatar-item {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin-right: 16rpx;\n  overflow: hidden;\n  border: 2rpx solid #FFFFFF;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n}\n\n.avatar-more {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #F2F2F7;\n  color: #8E8E93;\n  font-size: 24rpx;\n  margin-right: 16rpx;\n}\n\n.recent-join {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.recent-user {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 600;\n}\n\n.join-time {\n  font-size: 24rpx;\n  color: #FF3B30;\n}\n\n.group-progress {\n  margin-bottom: 24rpx;\n}\n\n.progress-bar {\n  height: 20rpx;\n  background-color: #F2F2F7;\n  border-radius: 10rpx;\n  overflow: hidden;\n  margin-bottom: 8rpx;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(to right, #FF3B30, #FF9500);\n  border-radius: 10rpx;\n}\n\n.progress-text {\n  font-size: 26rpx;\n  color: #8E8E93;\n  text-align: center;\n}\n\n.group-tips {\n  display: flex;\n  align-items: center;\n  background-color: rgba(255, 59, 48, 0.1);\n  padding: 16rpx;\n  border-radius: 12rpx;\n  margin-bottom: 20rpx;\n}\n\n.tip-icon {\n  margin-right: 8rpx;\n  color: #FF3B30;\n}\n\n.tip-text {\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.verification-info {\n  margin-bottom: 20rpx;\n}\n\n.verification-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.verification-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 8rpx;\n}\n\n.verification-text {\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.verification-address {\n  font-size: 26rpx;\n  color: #666666;\n}\n\n.verification-time {\n  font-size: 26rpx;\n  color: #8E8E93;\n}\n\n.local-benefits {\n  display: flex;\n  justify-content: space-around;\n  padding-top: 20rpx;\n  border-top: 1rpx dashed #F2F2F7;\n}\n\n.benefit-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.benefit-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-bottom: 8rpx;\n}\n\n.benefit-text {\n  font-size: 24rpx;\n  color: #333333;\n}\n\n/* 优惠券卡片 */\n.coupon-card {\n  margin: 0 24rpx 24rpx;\n  padding: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.coupon-left {\n  flex: 1;\n}\n\n.coupon-title {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #000000;\n  margin-bottom: 8rpx;\n}\n\n.coupon-desc {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n\n.coupon-right {\n  background-color: #FF3B30;\n  color: #FFFFFF;\n  font-size: 24rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 6rpx;\n}\n\n.coupon-btn {\n  font-size: 28rpx;\n  font-weight: 600;\n}\n\n/* 商家信息卡片 */\n.shop-card {\n  margin: 0 24rpx 24rpx;\n  padding: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n  display: flex;\n  align-items: center;\n}\n\n.shop-info {\n  flex: 1;\n  display: flex;\n  align-items: center;\n}\n\n.shop-logo {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n}\n\n.shop-details {\n  flex: 1;\n}\n\n.shop-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #000000;\n  margin-bottom: 8rpx;\n}\n\n.shop-rating {\n  display: flex;\n  align-items: center;\n}\n\n.rating-stars {\n  display: flex;\n  margin-right: 12rpx;\n}\n\n.star-icon {\n  width: 24rpx;\n  height: 24rpx;\n  margin-right: 4rpx;\n}\n\n.rating-score {\n  font-size: 26rpx;\n  color: #FF9500;\n}\n\n.rating-count {\n  font-size: 26rpx;\n  color: #8E8E93;\n  margin-left: 4rpx;\n}\n\n.shop-action {\n  background-color: #F2F2F7;\n  padding: 12rpx 24rpx;\n  border-radius: 30rpx;\n  display: flex;\n  align-items: center;\n  font-size: 26rpx;\n  color: #000000;\n}\n\n.chevron-icon {\n  width: 24rpx;\n  height: 24rpx;\n  margin-left: 8rpx;\n}\n\n/* 商品参数卡片 */\n.params-card {\n  margin: 0 24rpx 24rpx;\n  padding: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n}\n\n.params-header {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #000000;\n  margin-bottom: 24rpx;\n}\n\n.params-list {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.param-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 24rpx;\n}\n\n.param-name {\n  font-size: 28rpx;\n  color: #8E8E93;\n  margin-bottom: 4rpx;\n}\n\n.param-value {\n  font-size: 32rpx;\n  font-weight: 700;\n  color: #000000;\n}\n\n/* 详情选项卡 */\n.detail-tabs {\n  margin: 0 24rpx 24rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n  overflow: hidden;\n}\n\n.tab-header {\n  display: flex;\n  border-bottom: 1px solid #F2F2F7;\n}\n\n.tab-item {\n  flex: 1;\n  padding: 24rpx 0;\n  text-align: center;\n  font-size: 28rpx;\n  color: #8E8E93;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #000000;\n  font-weight: 500;\n}\n\n.tab-line {\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40rpx;\n  height: 4rpx;\n  background-color: #FF3B30;\n  border-radius: 2rpx;\n}\n\n.tab-content {\n  padding: 30rpx;\n}\n\n/* 商品详情 */\n.product-details {\n  \n}\n\n.detail-desc {\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.6;\n  margin-bottom: 24rpx;\n}\n\n.detail-image {\n  width: 100%;\n  margin-bottom: 16rpx;\n  border-radius: 12rpx;\n}\n\n/* 活动规则 */\n.product-rules {\n  \n}\n\n.rule-item {\n  display: flex;\n  margin-bottom: 20rpx;\n}\n\n.rule-number {\n  margin-right: 16rpx;\n  color: #FF3B30;\n  font-weight: 600;\n}\n\n.rule-text {\n  flex: 1;\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.6;\n}\n\n/* 用户评价 */\n.product-reviews {\n  \n}\n\n.reviews-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.reviews-title {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #000000;\n}\n\n.reviews-rate {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n\n.review-tags {\n  display: flex;\n  margin-bottom: 24rpx;\n}\n\n.review-tag {\n  background-color: #F2F2F7;\n  color: #8E8E93;\n  font-size: 24rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n}\n\n.review-tag.active {\n  background-color: #FF3B30;\n  color: #FFFFFF;\n}\n\n.review-item {\n  margin-bottom: 30rpx;\n  padding-bottom: 30rpx;\n  border-bottom: 1px solid #F2F2F7;\n}\n\n.review-item:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.review-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.user-avatar {\n  width: 64rpx;\n  height: 64rpx;\n  border-radius: 50%;\n  margin-right: 16rpx;\n}\n\n.review-user {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 28rpx;\n  color: #000000;\n  margin-bottom: 4rpx;\n}\n\n.review-rating {\n  display: flex;\n}\n\n.star {\n  width: 24rpx;\n  height: 24rpx;\n  margin-right: 4rpx;\n}\n\n.review-time {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.review-content {\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.6;\n  margin-bottom: 16rpx;\n}\n\n.review-images {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 16rpx;\n}\n\n.review-image {\n  width: 160rpx;\n  height: 160rpx;\n  margin-right: 16rpx;\n  margin-bottom: 16rpx;\n  border-radius: 8rpx;\n}\n\n.review-actions {\n  display: flex;\n}\n\n.review-action {\n  display: flex;\n  align-items: center;\n  margin-right: 24rpx;\n  color: #8E8E93;\n  font-size: 24rpx;\n}\n\n.empty-reviews {\n  text-align: center;\n  padding: 60rpx 0;\n  color: #8E8E93;\n  font-size: 28rpx;\n}\n\n.more-reviews {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 24rpx 0;\n  border-top: 1px solid #F2F2F7;\n  color: #8E8E93;\n  font-size: 28rpx;\n}\n\n.recommend-section {\n  margin: 0 24rpx 24rpx;\n  padding: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n}\n\n.recommend-header {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #000000;\n  margin-bottom: 24rpx;\n}\n\n.recommend-list {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -8rpx;\n}\n\n.recommend-item {\n  width: calc(50% - 16rpx);\n  margin: 0 8rpx 16rpx;\n}\n\n.recommend-image {\n  width: 100%;\n  height: 200rpx;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.recommend-info {\n  padding: 0 8rpx;\n}\n\n.recommend-name {\n  font-size: 28rpx;\n  color: #000000;\n  margin-bottom: 8rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.recommend-price {\n  font-size: 28rpx;\n  color: #FF3B30;\n  font-weight: 500;\n}\n\n/* 底部安全区 */\n.safe-area-inset-bottom {\n  height: 120rpx;\n}\n\n/* 底部购买栏 */\n.bottom-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 120rpx;\n  background-color: #FFFFFF;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.05);\n  z-index: 100;\n}\n\n.bottom-left {\n  display: flex;\n  align-items: center;\n  padding: 0 20rpx;\n}\n\n.action-btn {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 0 20rpx;\n}\n\n.action-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-bottom: 8rpx;\n}\n\n.action-btn text {\n  font-size: 20rpx;\n  color: #8E8E93;\n}\n\n.buy-buttons {\n  flex: 1;\n  display: flex;\n  height: 100%;\n}\n\n.buy-btn {\n  flex: 1;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.normal-buy {\n  background-color: #F2F2F7;\n}\n\n.group-buy {\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n}\n\n.buy-price {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #000000;\n}\n\n.group-buy .buy-price {\n  color: #FFFFFF;\n}\n\n.buy-price-row {\n  display: flex;\n  align-items: center;\n}\n\n.original-tag {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  text-decoration: line-through;\n  margin-left: 8rpx;\n}\n\n.buy-label {\n  font-size: 24rpx;\n  color: #8E8E93;\n  margin-top: 4rpx;\n  display: flex;\n  align-items: center;\n}\n\n.group-buy .buy-label {\n  color: #FFFFFF;\n}\n\n.save-amount {\n  background-color: #FFFFFF;\n  color: #FF3B30;\n  padding: 2rpx 8rpx;\n  border-radius: 8rpx;\n  margin-left: 8rpx;\n  font-weight: 600;\n}\n\n/* 购买弹窗 */\n.buy-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n}\n\n.popup-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n}\n\n.popup-content {\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #FFFFFF;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 30rpx;\n  transform: translateY(0);\n  animation: slideUp 0.3s ease;\n}\n\n@keyframes slideUp {\n  from {\n    transform: translateY(100%);\n  }\n  to {\n    transform: translateY(0);\n  }\n}\n\n.popup-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.popup-product-image {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 12rpx;\n  margin-right: 20rpx;\n}\n\n.popup-product-info {\n  flex: 1;\n}\n\n.popup-price {\n  font-size: 36rpx;\n  color: #FF3B30;\n  font-weight: 700;\n  margin-bottom: 8rpx;\n}\n\n.popup-stock {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.popup-verify-tag {\n  background-color: #FF9500;\n  color: #FFFFFF;\n  font-size: 20rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 10rpx;\n  display: inline-block;\n  margin-top: 8rpx;\n}\n\n.popup-close {\n  width: 48rpx;\n  height: 48rpx;\n}\n\n.popup-section {\n  margin-bottom: 30rpx;\n}\n\n.section-title {\n  font-size: 28rpx;\n  color: #8E8E93;\n  margin-bottom: 16rpx;\n}\n\n.quantity-selector {\n  display: flex;\n  align-items: center;\n  border: 1px solid #E5E5EA;\n  border-radius: 8rpx;\n  width: 240rpx;\n}\n\n.quantity-btn {\n  width: 80rpx;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 36rpx;\n  color: #8E8E93;\n}\n\n.quantity-input {\n  flex: 1;\n  height: 80rpx;\n  text-align: center;\n  font-size: 28rpx;\n  border-left: 1px solid #E5E5EA;\n  border-right: 1px solid #E5E5EA;\n}\n\n.popup-verify-info {\n  margin-top: 20rpx;\n  background-color: #F9F9F9;\n  padding: 16rpx;\n  border-radius: 8rpx;\n  margin-bottom: 30rpx;\n}\n\n.verify-title {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 600;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.verify-item {\n  font-size: 24rpx;\n  color: #666666;\n  line-height: 1.6;\n  display: block;\n}\n\n.popup-footer {\n  margin-top: 60rpx;\n}\n\n.popup-buy-btn {\n  height: 88rpx;\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n  border-radius: 44rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #FFFFFF;\n  font-size: 32rpx;\n  font-weight: 600;\n}\n\n/* 加载状态 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 300rpx;\n  width: 100%;\n}\n\n.loading-spinner {\n  width: 60rpx;\n  height: 60rpx;\n  border: 6rpx solid #F2F2F7;\n  border-top: 6rpx solid #FF3B30;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 分享弹窗 */\n.share-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n}\n\n.share-content {\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #FFFFFF;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 30rpx;\n  transform: translateY(0);\n  animation: slideUp 0.3s ease;\n}\n\n.share-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.share-options {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 24rpx;\n}\n\n.share-option {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.share-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-bottom: 8rpx;\n}\n\n.share-option text {\n  font-size: 24rpx;\n  color: #8E8E93;\n}\n\n.share-poster {\n  height: 88rpx;\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n  border-radius: 44rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #FFFFFF;\n  font-size: 32rpx;\n  font-weight: 600;\n}\n\n/* 底部购买栏的样式 */\n.verify-tag {\n  position: absolute;\n  top: -20rpx;\n  right: 20rpx;\n  background-color: #FF9500;\n  color: #FFFFFF;\n  font-size: 20rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n  transform: rotate(5deg);\n}\n\n/* 套餐内容样式 */\n.package-content {\n  background-color: #F9F9F9;\n  border-radius: 12rpx;\n  padding: 24rpx;\n  margin-bottom: 24rpx;\n}\n\n.package-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 16rpx;\n  position: relative;\n  padding-left: 20rpx;\n}\n\n.package-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 8rpx;\n  width: 8rpx;\n  height: 32rpx;\n  background-color: #FF3B30;\n  border-radius: 4rpx;\n}\n\n.package-items {\n  margin-bottom: 16rpx;\n}\n\n.package-item {\n  padding: 16rpx 0;\n  border-bottom: 1px dashed #E5E5EA;\n}\n\n.package-item:last-child {\n  border-bottom: none;\n}\n\n.package-item-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8rpx;\n}\n\n.package-item-name {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.package-item-quantity {\n  font-size: 28rpx;\n  color: #FF3B30;\n}\n\n.package-item-desc {\n  font-size: 24rpx;\n  color: #8E8E93;\n  line-height: 1.4;\n}\n\n.package-notice {\n  background-color: rgba(255, 149, 0, 0.1);\n  padding: 16rpx;\n  border-radius: 8rpx;\n}\n\n.notice-title {\n  font-size: 26rpx;\n  font-weight: 600;\n  color: #FF9500;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.notice-text {\n  font-size: 24rpx;\n  color: #666666;\n  line-height: 1.6;\n  display: block;\n}\n\n.buy-btn.group-buy {\n  background-color: #FF3B30;\n  flex: 2;\n}\n\n.buy-btn.full-width {\n  flex: 1;\n  width: 100%;\n}\n\n.buy-price {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #FFFFFF;\n}\n\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: calc(var(--status-bar-height, 25px) + 62px);\n  width: 100%;\n  z-index: 100;\n  \n  .navbar-bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\n  }\n  \n  .navbar-content {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    height: 100%;\n    padding-top: var(--status-bar-height, 25px);\n    padding-left: 30rpx;\n    padding-right: 30rpx;\n    box-sizing: border-box;\n    \n    .back-btn {\n      width: 40rpx;\n      height: 40rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n    \n    .back-icon {\n      width: 100%;\n      height: 100%;\n    }\n    \n    .navbar-title {\n      font-size: 18px;\n      font-weight: 600;\n      color: #FFFFFF;\n      position: absolute;\n      left: 50%;\n      transform: translateX(-50%);\n    }\n    \n    .navbar-right {\n      width: 40rpx;\n    }\n  }\n}\n\n/* 分享弹窗样式 */\n.share-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n}\n\n/* 分销弹窗样式 */\n.distribution-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n}\n\n.distribution-content {\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #FFFFFF;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 30rpx;\n  transform: translateY(0);\n  animation: slideUp 0.3s ease;\n}\n\n.distribution-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n  padding-bottom: 20rpx;\n  border-bottom: 1px solid #F2F2F7;\n}\n\n.distribution-header text {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.distribution-body {\n  padding: 20rpx 0;\n}\n\n.distribution-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.distribution-image {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  margin-right: 20rpx;\n}\n\n.distribution-text {\n  flex: 1;\n}\n\n.distribution-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.distribution-desc {\n  font-size: 26rpx;\n  color: #666666;\n  line-height: 1.4;\n  display: block;\n}\n\n.distribution-data {\n  display: flex;\n  justify-content: space-around;\n  margin-bottom: 30rpx;\n  padding: 20rpx;\n  background-color: #FFF9F9;\n  border-radius: 16rpx;\n}\n\n.data-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.data-value {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #FF3B30;\n  margin-bottom: 8rpx;\n}\n\n.data-label {\n  font-size: 24rpx;\n  color: #666666;\n}\n\n.distribution-btns {\n  display: flex;\n  justify-content: space-between;\n}\n\n.distribution-btn {\n  width: 48%;\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 44rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n}\n\n.apply-btn {\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n  color: #FFFFFF;\n}\n\n.poster-btn {\n  background-color: #F2F2F7;\n  color: #333333;\n  border: 1px solid #E5E5EA;\n}\n\n/* 适用门店列表卡片 */\n.store-list-card {\n  margin: 0 24rpx 24rpx;\n  padding: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  position: relative;\n  padding-left: 20rpx;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 50%;\n    transform: translateY(-50%);\n    width: 8rpx;\n    height: 32rpx;\n    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\n    border-radius: 4rpx;\n  }\n}\n\n.card-more {\n  display: flex;\n  align-items: center;\n  font-size: 26rpx;\n  color: #999999;\n}\n\n.store-list {\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.store-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1px solid #F5F5F7;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.store-logo {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 8rpx;\n  margin-right: 20rpx;\n}\n\n.store-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.store-name {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n}\n\n.store-address {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.store-distance {\n  font-size: 24rpx;\n  color: #999999;\n  padding: 0 10rpx;\n}\n\n/* 本店其他拼团卡片 */\n.other-group-card {\n  margin: 0 24rpx 24rpx;\n  padding: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n}\n\n.other-group-list {\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.other-group-item {\n  display: flex;\n  padding: 20rpx 0;\n  border-bottom: 1px solid #F5F5F7;\n  position: relative;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.sold-tag-corner {\n  position: absolute;\n  top: 10rpx;\n  right: 0;\n  background: #FF3B5C;\n  color: #FFFFFF;\n  font-size: 20rpx;\n  padding: 2rpx 12rpx;\n  border-radius: 12rpx 0 0 12rpx;\n  z-index: 1;\n}\n\n.item-image-container {\n  position: relative;\n  width: 160rpx;\n  height: 160rpx;\n  margin-right: 20rpx;\n}\n\n.other-group-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 12rpx;\n}\n\n.other-group-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.shop-location {\n  display: flex;\n  flex-direction: column;\n  margin-bottom: 6rpx;\n  \n  .shop-name {\n    font-size: 24rpx;\n    color: #333333;\n    font-weight: 500;\n  }\n  \n  .location-info {\n    font-size: 22rpx;\n    color: #999999;\n    margin-top: 4rpx;\n  }\n}\n\n.other-group-title {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-bottom: 8rpx;\n}\n\n.other-group-price {\n  display: flex;\n  align-items: center;\n  margin-bottom: 12rpx;\n}\n\n.group-price-value {\n  font-size: 32rpx;\n  color: #FF2C54;\n  font-weight: 600;\n  margin-right: 12rpx;\n}\n\n.market-price-value {\n  font-size: 24rpx;\n  color: #999999;\n  text-decoration: line-through;\n  margin-right: 12rpx;\n}\n\n.discount-tag {\n  font-size: 22rpx;\n  color: #FF2C54;\n  background-color: rgba(255, 44, 84, 0.1);\n  padding: 2rpx 8rpx;\n  border-radius: 4rpx;\n}\n\n.other-group-bottom {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.sold-info {\n  display: flex;\n  align-items: center;\n  font-size: 22rpx;\n  color: #999999;\n}\n\n.sold-tag {\n  background-color: #F5F5F7;\n  color: #666666;\n  padding: 2rpx 8rpx;\n  border-radius: 4rpx;\n  margin-right: 8rpx;\n}\n\n.sold-count {\n  margin-right: 8rpx;\n}\n\n.usage-info {\n  background-color: #F5F5F7;\n  color: #666666;\n  padding: 2rpx 8rpx;\n  border-radius: 4rpx;\n}\n\n.buy-now-btn {\n  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\n  color: #FFFFFF;\n  font-size: 24rpx;\n  padding: 8rpx 24rpx;\n  border-radius: 30rpx;\n  font-weight: 500;\n}\n\n/* 推荐商品部分样式调整 */\n.recommend-section {\n  margin: 0 24rpx 24rpx;\n  padding: 30rpx;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\n}\n\n.recommend-header {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 24rpx;\n  position: relative;\n  padding-left: 20rpx;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 50%;\n    transform: translateY(-50%);\n    width: 8rpx;\n    height: 32rpx;\n    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\n    border-radius: 4rpx;\n  }\n}\n\n.recommend-list {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -8rpx;\n}\n\n.recommend-item {\n  width: calc(50% - 16rpx);\n  margin: 0 8rpx 16rpx;\n}\n\n.recommend-image {\n  width: 100%;\n  height: 200rpx;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.recommend-info {\n  padding: 0 8rpx;\n}\n\n.recommend-name {\n  font-size: 28rpx;\n  color: #333333;\n  margin-bottom: 8rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.recommend-price {\n  font-size: 28rpx;\n  color: #FF2C54;\n  font-weight: 500;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/detail/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "smartNavigate"], "mappings": ";;;;AAkhBA,MAAA,sBAAA,MAAA;AAEA,MAAA,YAAA;AAAA;IAEI;AAAA;EAEF,OAAA;AACE,WAAA;AAAA,MACE,IAAA;AAAA,MACA,MAAA;AAAA;AAAA;MAEA,MAAA,CAAA;AAAA,MACA,iBAAA;AAAA,MACA,cAAA;AAAA;;;;;;MAOA,oBAAA;AAAA;QAEE,MAAA;AAAA,QACA,OAAA;AAAA,QACA,SAAA;AAAA,QACA,SAAA;AAAA;MAEF,gBAAA;AAAA;MAEA,YAAA,CAAA,MAAA,UAAA,UAAA,OAAA;AAAA,MACA,cAAA;AAAA;;MAGA,gBAAA;AAAA,MACA,iBAAA;AAAA,MACA,YAAA;AAAA,MACA,mBAAA;AAAA,QACE;AAAA;;;;;QAMA;AAAA;;;;;QAMA;AAAA;;;;;QAMA;AAAA;;;;QAKA;AAAA;;;QAIA;AAAA;UAEE,MAAA;AAAA,UACA,MAAA;AAAA;;;QAIF;AAAA;UAEE,MAAA;AAAA,UACA,MAAA;AAAA,UACA,SAAA;AAAA,UACA,UAAA;AAAA;QAEF;AAAA;UAEE,MAAA;AAAA,UACA,MAAA;AAAA;UAEA,UAAA;AAAA,QACF;AAAA;;;QAIA;AAAA,UACE,IAAA;AAAA,UACA,OAAA;AAAA,UACA,OAAA;AAAA,UACA,YAAA;AAAA,UACA,aAAA;AAAA;UAEA,WAAA;AAAA,UACA,UAAA;AAAA,UACA,UAAA;AAAA;UAEA,WAAA;AAAA,UACA,UAAA;AAAA;QAEF;AAAA,UACE,IAAA;AAAA,UACA,OAAA;AAAA,UACA,OAAA;AAAA,UACA,YAAA;AAAA,UACA,aAAA;AAAA;UAEA,WAAA;AAAA,UACA,UAAA;AAAA,UACA,UAAA;AAAA;UAEA,WAAA;AAAA,UACA,UAAA;AAAA;QAEF;AAAA,UACE,IAAA;AAAA,UACA,OAAA;AAAA,UACA,OAAA;AAAA,UACA,YAAA;AAAA,UACA,aAAA;AAAA;;UAGA,UAAA;AAAA,UACA,UAAA;AAAA;UAEA,WAAA;AAAA,UACA,UAAA;AAAA,QACF;AAAA,MACF;AAAA;;EAGJ,UAAA;AAAA;AAAA,IAEE,oBAAA;AACE,cAAA,KAAA,MAAA;AAAA;;;;QAKE,KAAA;;;;QAIA;;MAEF;AAAA;;;;;;;;AASA,aAAA;AAAA;;IAIF,kBAAA;AACE,YAAA,cAAA,WAAA,KAAA,SAAA,eAAA,KAAA,SAAA,aAAA;;;;AAGA,YAAA,kBAAA,KAAA,MAAA,MAAA,aAAA,cAAA,GAAA;AACA,aAAA;AAAA;;;AAKA,YAAA,cAAA,WAAA,KAAA,SAAA,eAAA,KAAA,SAAA,aAAA;;AAEA,cAAA,cAAA,YAAA,QAAA,CAAA;AAAA;;IAIF,gBAAA;AACE,UAAA,KAAA,SAAA,eAAA;AAAA,eAAA;AACA,YAAA,WAAA,KAAA,SAAA,YAAA,KAAA,SAAA,cAAA;AACA,aAAA,WAAA,MAAA,SAAA,GAAA,QAAA;AAAA;;IAIF,sBAAA;AAEE,UAAA,OAAA,KAAA,SAAA,eAAA,UAAA;AACE,eAAA,KAAA,SAAA,WAAA,QAAA,CAAA;AAAA,MACF;AACA,aAAA,KAAA,SAAA;AAAA;;;;AAOE,eAAA,KAAA,SAAA,cAAA,QAAA,CAAA;AAAA,MACF;AACA,aAAA,KAAA,SAAA;AAAA;;;;AAME,eAAA,KAAA,SAAA,YAAA,QAAA,CAAA;AAAA,MACF;;;;;;AAOE,eAAA,KAAA,SAAA,aAAA,QAAA,CAAA;AAAA,MACF;;;;IAKF,kBAAA;AACE,YAAA,cAAA,WAAA,KAAA,SAAA,eAAA,KAAA,SAAA,aAAA;;AAEA,cAAA,cAAA,YAAA,QAAA,CAAA;AAAA;;IAIF,mBAAA;AACE,YAAA,eAAA,WAAA,KAAA,SAAA,gBAAA,KAAA,SAAA,aAAA;;AAEA,cAAA,eAAA,YAAA,QAAA,CAAA;AAAA;;IAIF,mBAAA;;;;IAKA,iBAAA;AACE,YAAA,YAAA,KAAA,SAAA,cAAA,KAAA,SAAA;;;;IAKF,kBAAA;;IAEA;AAAA;EAEF,OAAA,SAAA;;AAEI,WAAA,KAAA,QAAA;AAAA,IACF;;;IAKA;AAGA,UAAA,aAAAA,oBAAA;;;AAKA,eAAA,MAAA;;AAEE,WAAA,eAAA;AAAA,IACF,GAAA,GAAA;AAAA;;;AAKE,oBAAA,KAAA,cAAA;;IAEF;AAAA;;EAGF,oBAAA;AACE,WAAA;AAAA;MAEE,MAAA,wDAAA,KAAA,EAAA;AAAA,MACA,UAAA,KAAA,SAAA,OAAA,CAAA;AAAA;;EAGJ,SAAA;AAAA;AAAA,IAEE,qBAAA;AAEE,WAAA,UAAA;AAGA,iBAAA,MAAA;AAEE,aAAA,WAAA;AAAA;UAEE,OAAA;AAAA,UACA,QAAA;AAAA;;;;UAKA,YAAA;AAAA,UACA,eAAA;AAAA,UACA,aAAA;AAAA,UACA,cAAA;AAAA;UAEA,aAAA;AAAA,UACA,WAAA,oBAAA,KAAA,YAAA;AAAA;;UAGA,UAAA;AAAA,UACA,UAAA;AAAA,UACA,YAAA;AAAA,UACA,aAAA;AAAA,UACA,OAAA;AAAA,UACA,aAAA;AAAA;;;;UAKA,OAAA;AAAA;YAEE;AAAA;YAEA;AAAA,YACA;AAAA;UAEF,SAAA;AAAA,YACE;AAAA;;;gBAII,QAAA;AAAA;cAEF,QAAA;AAAA;cAEA,QAAA;AAAA;;;cAIA,MAAA;AAAA,cACA,OAAA;AAAA;YAEF;AAAA;;;gBAII,QAAA;AAAA;cAEF,QAAA;AAAA,cACA,SAAA;AAAA,cACA,QAAA,CAAA;AAAA,cACA,MAAA;AAAA,cACA,OAAA;AAAA,YACF;AAAA;UAEF,QAAA;AAAA,YACE;AAAA;;cAGE,OAAA;AAAA;YAEF;AAAA;;cAGE,OAAA;AAAA;YAEF;AAAA;;cAGE,OAAA;AAAA,YACF;AAAA;;YAGA;AAAA;;cAGE,MAAA;AAAA;;YAGF;AAAA;;cAGE,MAAA;AAAA,cACA,MAAA;AAAA,YACF;AAAA;UAEF,gBAAA;AAAA;;;;;;UAQA,QAAA,KAAA,SAAA;AAAA,UACA,YAAA;AAAA;AAGF,aAAA,UAAA;AAAA,MACF,GAAA,GAAA;AAAA;;IAIF,iBAAA;;AAKE,WAAA,iBAAA,YAAA,MAAA;;MAEA,GAAA,GAAA;AAAA;;IAIF,kBAAA;;;;;AAKE,YAAA,OAAA,UAAA;;;;AAMI,wBAAA,KAAA,cAAA;;QAEF;AACA;AAAA,MACF;AAGA,YAAA,OAAA,KAAA,MAAA,QAAA,MAAA,KAAA,KAAA,GAAA;;;;AAMA,WAAA,YAAA;AAAA;QAEE,OAAA,QAAA,KAAA,MAAA,QAAA;AAAA,QACA,SAAA,UAAA,KAAA,MAAA,UAAA;AAAA,QACA,SAAA,UAAA,KAAA,MAAA,UAAA;AAAA;;;IAKJ,YAAA,IAAA;AAEE,WAAA,SAAA,EAAA;AAGA,YAAA,WAAA;AAAA;;QAGE,QAAA;AAAA;;;;QAKA,YAAA;AAAA,QACA,eAAA;AAAA,QACA,aAAA;AAAA,QACA,cAAA;AAAA;QAEA,aAAA;AAAA,QACA,WAAA,oBAAA,KAAA,qBAAA;AAAA,QACA,SAAA,IAAA,KAAA,KAAA,IAAA,IAAA,QAAA,CAAA;AAAA;AAAA;QAEA,UAAA;AAAA,QACA,UAAA;AAAA,QACA,YAAA;AAAA,QACA,aAAA;AAAA,QACA,OAAA;AAAA,QACA,aAAA;AAAA;UAEE;AAAA,UACA;AAAA;;;;;;;QAQF,OAAA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;;;;;QAIF,SAAA;AAAA,UACE;AAAA,YACE,UAAA;AAAA,YACA,QAAA;AAAA,YACA,QAAA;AAAA,YACA,MAAA;AAAA;YAEA,QAAA;AAAA;;YAGA;AAAA;UAEF;AAAA,YACE,UAAA;AAAA,YACA,QAAA;AAAA,YACA,QAAA;AAAA,YACA,MAAA;AAAA;YAEA,QAAA;AAAA;YAEA;AAAA,UACF;AAAA;QAEF,QAAA;AAAA;;;QAIA,WAAA;AAAA;;AAKA,iBAAA,QAAA;;;;;;AAMA,iBAAA,cAAA;AAAA,MACF,WAAA,OAAA,KAAA;;;;;AAKE,iBAAA,YAAA;;AAEA,iBAAA,cAAA;AAAA,MACF,WAAA,OAAA,KAAA;;;;;AAKE,iBAAA,YAAA;;AAEA,iBAAA,cAAA;AAAA,MACF,WAAA,OAAA,KAAA;AACE,iBAAA,QAAA;;;;AAIA,iBAAA,YAAA;;AAEA,iBAAA,cAAA;AAAA,MACF;AAEA,aAAA;AAAA;;IAIF,WAAA,MAAA;;;;AAIE,aAAA,GAAA,IAAA,IAAA,KAAA,IAAA,GAAA;AAAA;;;AAKAC,uBAAA,cAAA,kCAAA,KAAA,SAAA,EAAA,EAAA;AAAA;;IAIF,UAAA,OAAA;AACE,WAAA,aAAA;AAAA;;IAIF,iBAAA;AACED,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;MAEF,CAAA;AAAA;;IAIF,iBAAA;AACE,WAAA,aAAA,CAAA,KAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA,KAAA,aAAA,SAAA;AAAA;MAEF,CAAA;AAAA;;IAIF,eAAA;;AAEI,aAAA,KAAA,aAAA,CAAA,KAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA,KAAA,KAAA,aAAA,QAAA;AAAA;QAEF,CAAA;AAAA,MACF;AAAA;;IAIF,SAAA;;;;;;;;;;IAYA,gBAAA;;;;IAKA,mBAAA;;AAEI,aAAA;AAAA,MACF;AAAA;;IAIF,mBAAA;AACE,UAAA,KAAA,cAAA,KAAA,SAAA,OAAA;AACE,aAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AAAA,MACF;AAAA;;;AAKA,YAAA,QAAA,KAAA,mBAAA,UAAA,KAAA,SAAA,aAAA,KAAA,SAAA;AACA,YAAA,cAAA,WAAA,KAAA,IAAA,KAAA,aAAA,QAAA,CAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AAEEA,0BAAAA,MAAA,YAAA;AAAA;YAEA,CAAA;AAGA,uBAAA,MAAA;AACEA,4BAAA,MAAA,YAAA;;;YAKF,GAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,eAAA,GAAA;AACE,WAAA,qBAAA,EAAA,OAAA;AAAA;;IAIF,QAAA;;;;IAKA,kBAAA;;;;;;;;IAUA,WAAA,OAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;MAGA,CAAA;AAAA;;IAIF,YAAA,OAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;MAEF,CAAA;AAAA;;IAIF,kBAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;MAEF,CAAA;AAAA;;IAIF,gBAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;MAEF,CAAA;AAAA;;IAIF,qBAAA;;AAGEA,oBAAAA,MAAA,UAAA;AAAA;;MAGA,CAAA;AAAA;;IAIF,YAAA,OAAA;AAAA;;IAKA,iBAAA;AACEA,oBAAAA,MAAA,YAAA;AAAA;MAEA,CAAA;AAEA,iBAAA,MAAA;AACEA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;;QAGA,CAAA;;MAEF,GAAA,IAAA;AAAA;;IAIF,SAAA;AACEA,oBAAA,MAAA,aAAA;AAAA;;IAIF,qBAAA,WAAA;AACEC,uBAAAA,cAAA,wDAAA,SAAA,EAAA;AAAA;;IAIF,gBAAA;AACED,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;MAEF,CAAA;AAAA;;;;;;IASF,iBAAA;AACEC,uBAAA,cAAA,sDAAA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClyCA,GAAG,WAAW,eAAe;"}