{"version": 3, "file": "points-rule.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/points-rule.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJccG9pbnRzLXJ1bGUudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"points-rule-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">积分规则</text>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 积分规则内容 -->\r\n    <view class=\"rule-content\">\r\n      <!-- 积分获取规则 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">积分获取规则</view>\r\n        \r\n        <view class=\"rule-form\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">消费积分比例</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.consumptionRatio\" />\r\n              <text class=\"input-suffix\">积分/元</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">签到积分奖励</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.checkInPoints\" />\r\n              <text class=\"input-suffix\">积分/次</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">评价积分奖励</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.reviewPoints\" />\r\n              <text class=\"input-suffix\">积分/次</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">分享积分奖励</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.sharePoints\" />\r\n              <text class=\"input-suffix\">积分/次</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">新用户注册奖励</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.registerPoints\" />\r\n              <text class=\"input-suffix\">积分</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 积分使用规则 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">积分使用规则</view>\r\n        \r\n        <view class=\"rule-form\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">积分抵扣比例</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.redemptionRatio\" />\r\n              <text class=\"input-suffix\">积分/元</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">最低抵扣积分</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.minRedemption\" />\r\n              <text class=\"input-suffix\">积分</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">最高抵扣比例</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.maxRedemptionRatio\" />\r\n              <text class=\"input-suffix\">%</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 积分有效期规则 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">积分有效期规则</view>\r\n        \r\n        <view class=\"rule-form\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">积分有效期</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.validity\" />\r\n              <text class=\"input-suffix\">个月</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"form-item switch-item\">\r\n            <text class=\"form-label\">积分到期提醒</text>\r\n            <switch :checked=\"pointsRules.expirationReminder\" @change=\"toggleExpirationReminder\" color=\"#4A00E0\" />\r\n          </view>\r\n          \r\n          <view class=\"form-item\" v-if=\"pointsRules.expirationReminder\">\r\n            <text class=\"form-label\">提前提醒天数</text>\r\n            <view class=\"form-input-group\">\r\n              <input type=\"number\" class=\"form-input\" v-model=\"pointsRules.reminderDays\" />\r\n              <text class=\"input-suffix\">天</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 积分等级规则 -->\r\n      <view class=\"section-card\">\r\n        <view class=\"section-title\">积分等级规则</view>\r\n        \r\n        <view class=\"level-list\">\r\n          <view class=\"level-item\" v-for=\"(level, index) in pointsLevels\" :key=\"index\">\r\n            <view class=\"level-header\">\r\n              <text class=\"level-name\">{{level.name}}</text>\r\n              <text class=\"level-points\">{{level.minPoints}}-{{level.maxPoints}}积分</text>\r\n            </view>\r\n            <view class=\"level-benefits\">\r\n              <text class=\"benefit-item\" v-for=\"(benefit, bIndex) in level.benefits\" :key=\"bIndex\">{{benefit}}</text>\r\n            </view>\r\n            <view class=\"level-actions\">\r\n              <text class=\"action-btn edit\" @click=\"editLevel(level)\">编辑</text>\r\n              <text class=\"action-btn delete\" v-if=\"index > 0\" @click=\"deleteLevel(level)\">删除</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <button class=\"add-btn\" @click=\"addLevel\">添加积分等级</button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 保存按钮 -->\r\n    <view class=\"bottom-bar\">\r\n      <button class=\"save-btn\" @click=\"saveRules\">保存规则</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 积分规则\r\n      pointsRules: {\r\n        consumptionRatio: 10,\r\n        checkInPoints: 5,\r\n        reviewPoints: 10,\r\n        sharePoints: 5,\r\n        registerPoints: 100,\r\n        \r\n        redemptionRatio: 100,\r\n        minRedemption: 100,\r\n        maxRedemptionRatio: 30,\r\n        \r\n        validity: 12,\r\n        expirationReminder: true,\r\n        reminderDays: 30\r\n      },\r\n      \r\n      // 积分等级\r\n      pointsLevels: [\r\n        {\r\n          id: 1,\r\n          name: '普通会员',\r\n          minPoints: 0,\r\n          maxPoints: 999,\r\n          benefits: ['基础积分兑换']\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '银卡会员',\r\n          minPoints: 1000,\r\n          maxPoints: 4999,\r\n          benefits: ['积分兑换9.5折', '生日双倍积分']\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '金卡会员',\r\n          minPoints: 5000,\r\n          maxPoints: 19999,\r\n          benefits: ['积分兑换9折', '生日双倍积分', '专属积分活动']\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '钻石会员',\r\n          minPoints: 20000,\r\n          maxPoints: 99999,\r\n          benefits: ['积分兑换8.5折', '生日三倍积分', '专属积分活动', '积分不过期']\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    toggleExpirationReminder(e) {\r\n      this.pointsRules.expirationReminder = e.detail.value;\r\n    },\r\n    \r\n    editLevel(level) {\r\n      uni.showToast({\r\n        title: '编辑等级功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    deleteLevel(level) {\r\n      uni.showModal({\r\n        title: '删除确认',\r\n        content: `确定要删除\"${level.name}\"等级吗？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            const index = this.pointsLevels.findIndex(item => item.id === level.id);\r\n            if (index !== -1) {\r\n              this.pointsLevels.splice(index, 1);\r\n              uni.showToast({\r\n                title: '删除成功',\r\n                icon: 'success'\r\n              });\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    addLevel() {\r\n      uni.showToast({\r\n        title: '添加等级功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    saveRules() {\r\n      uni.showLoading({\r\n        title: '保存中...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '积分规则保存成功',\r\n          icon: 'success'\r\n        });\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 积分规则页面样式开始 */\r\n.points-rule-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 100rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #8E2DE2, #4A00E0);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n}\r\n\r\n/* 规则内容样式 */\r\n.rule-content {\r\n  padding: 20rpx;\r\n}\r\n\r\n.section-card {\r\n  background: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  padding-bottom: 15rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n/* 表单样式 */\r\n.form-item {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.form-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.form-input-group {\r\n  display: flex;\r\n  align-items: center;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.form-input {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.input-suffix {\r\n  padding: 0 20rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  background: #f5f5f5;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n}\r\n\r\n.switch-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n/* 积分等级样式 */\r\n.level-list {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.level-item {\r\n  background: #f9f9f9;\r\n  border-radius: 8rpx;\r\n  padding: 20rpx;\r\n  margin-bottom: 15rpx;\r\n}\r\n\r\n.level-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.level-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.level-points {\r\n  font-size: 24rpx;\r\n  color: #4A00E0;\r\n  background: rgba(74, 0, 224, 0.1);\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.level-benefits {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10rpx;\r\n  margin-bottom: 15rpx;\r\n}\r\n\r\n.benefit-item {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  background: #f0f0f0;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.level-actions {\r\n  display: flex;\r\n  gap: 15rpx;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 24rpx;\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 30rpx;\r\n}\r\n\r\n.action-btn.edit {\r\n  background: rgba(74, 0, 224, 0.1);\r\n  color: #4A00E0;\r\n}\r\n\r\n.action-btn.delete {\r\n  background: rgba(255, 59, 48, 0.1);\r\n  color: #FF3B30;\r\n}\r\n\r\n/* 添加按钮 */\r\n.add-btn {\r\n  background: #4A00E0;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 28rpx;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n/* 底部保存栏 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: #fff;\r\n  padding: 20rpx;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.save-btn {\r\n  background: #4A00E0;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 28rpx;\r\n  width: 100%;\r\n}\r\n/* 积分规则页面样式结束 */\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/points-rule.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAuJA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,aAAa;AAAA,QACX,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,cAAc;AAAA,QACd,aAAa;AAAA,QACb,gBAAgB;AAAA,QAEhB,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,oBAAoB;AAAA,QAEpB,UAAU;AAAA,QACV,oBAAoB;AAAA,QACpB,cAAc;AAAA,MACf;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU,CAAC,QAAQ;AAAA,QACpB;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU,CAAC,YAAY,QAAQ;AAAA,QAChC;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU,CAAC,UAAU,UAAU,QAAQ;AAAA,QACxC;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU,CAAC,YAAY,UAAU,UAAU,OAAO;AAAA,QACpD;AAAA,MACF;AAAA;EAEH;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,yBAAyB,GAAG;AAC1B,WAAK,YAAY,qBAAqB,EAAE,OAAO;AAAA,IAChD;AAAA,IAED,UAAU,OAAO;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,YAAY,OAAO;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,MAAM,IAAI;AAAA,QAC5B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,kBAAM,QAAQ,KAAK,aAAa,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACtE,gBAAI,UAAU,IAAI;AAChB,mBAAK,aAAa,OAAO,OAAO,CAAC;AACjCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,YAAY;AACVA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnQA,GAAG,WAAW,eAAe;"}