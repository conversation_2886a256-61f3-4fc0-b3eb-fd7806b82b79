
/* 添加会员特权页面样式开始 */
.add-privilege-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9500, #FF6B22);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 107, 34, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
}

/* 表单内容样式 */
.form-content {
  padding: 20rpx;
}
.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 20rpx;
}
.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}
.form-input-group .form-input {
  flex: 1;
  border: none;
}
.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}

/* 图标上传 */
.icon-upload {
  width: 120rpx;
  height: 120rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}
.preview-icon {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}
.upload-text {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 选择器样式 */
.picker-view {
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
}
.picker-text {
  font-size: 28rpx;
  color: #333;
}
.picker-arrow {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20rpx;
}
.radio-item {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}
.radio-item.active {
  background: rgba(255, 107, 34, 0.1);
  border-color: #FF6B22;
}
.radio-text {
  font-size: 26rpx;
  color: #666;
}
.radio-item.active .radio-text {
  color: #FF6B22;
  font-weight: 600;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 选择按钮 */
.select-btn {
  background: rgba(255, 107, 34, 0.1);
  color: #FF6B22;
  border: none;
  border-radius: 8rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

/* 会员等级列表 */
.level-list {
  margin-bottom: 20rpx;
}
.level-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.level-item:last-child {
  border-bottom: none;
}
.level-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.level-checkbox.checked {
  border-color: #FF6B22;
  background: #FF6B22;
}
.checkbox-inner {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background: #fff;
}
.level-content {
  flex: 1;
}
.level-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.level-desc {
  font-size: 24rpx;
  color: #999;
}

/* 底部按钮栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}
.save-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #FF6B22;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}
/* 添加会员特权页面样式结束 */
