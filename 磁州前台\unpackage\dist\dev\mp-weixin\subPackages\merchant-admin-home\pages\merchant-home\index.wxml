<view class="merchant-dashboard data-v-8ed27b9c"><view class="status-bar-bg data-v-8ed27b9c"></view><view class="navbar data-v-8ed27b9c"><view class="navbar-left data-v-8ed27b9c"><view class="back-icon data-v-8ed27b9c" bindtap="{{a}}"></view></view><text class="navbar-title data-v-8ed27b9c">商家中心</text><view class="navbar-right data-v-8ed27b9c"></view></view><view class="content-container data-v-8ed27b9c"><view class="performance-card data-v-8ed27b9c"><view class="performance-header data-v-8ed27b9c"><view class="performance-title data-v-8ed27b9c"><view class="greeting-container data-v-8ed27b9c"><text class="greeting data-v-8ed27b9c">您好，{{b}}</text><svg wx:if="{{e}}" u-s="{{['d']}}" class="greeting-icon data-v-8ed27b9c" u-i="8ed27b9c-0" bind:__l="__l" u-p="{{e}}"><path wx:if="{{c}}" class="data-v-8ed27b9c" u-i="8ed27b9c-1,8ed27b9c-0" bind:__l="__l" u-p="{{c}}"/><path wx:if="{{d}}" class="data-v-8ed27b9c" u-i="8ed27b9c-2,8ed27b9c-0" bind:__l="__l" u-p="{{d}}"/></svg></view><text class="date data-v-8ed27b9c">{{f}}</text></view><view class="merchant-badge data-v-8ed27b9c"><svg wx:if="{{j}}" u-s="{{['d']}}" class="merchant-badge-icon data-v-8ed27b9c" u-i="8ed27b9c-3" bind:__l="__l" u-p="{{j}}"><path wx:if="{{g}}" class="data-v-8ed27b9c" u-i="8ed27b9c-4,8ed27b9c-3" bind:__l="__l" u-p="{{g}}"/><path wx:if="{{h}}" class="data-v-8ed27b9c" u-i="8ed27b9c-5,8ed27b9c-3" bind:__l="__l" u-p="{{h}}"/><path wx:if="{{i}}" class="data-v-8ed27b9c" u-i="8ed27b9c-6,8ed27b9c-3" bind:__l="__l" u-p="{{i}}"/></svg><text class="data-v-8ed27b9c">{{k}}</text></view></view><view class="performance-metrics data-v-8ed27b9c"><view class="metric-item data-v-8ed27b9c"><text class="metric-value data-v-8ed27b9c">¥{{l}}</text><text class="metric-label data-v-8ed27b9c">今日收入</text></view><view class="metric-divider data-v-8ed27b9c"></view><view class="metric-item data-v-8ed27b9c"><text class="metric-value data-v-8ed27b9c">{{m}}</text><text class="metric-label data-v-8ed27b9c">今日订单</text></view><view class="metric-divider data-v-8ed27b9c"></view><view class="metric-item data-v-8ed27b9c"><text class="metric-value data-v-8ed27b9c">{{n}}</text><text class="metric-label data-v-8ed27b9c">待处理</text></view></view></view><view class="quick-actions-card data-v-8ed27b9c"><view class="section-header data-v-8ed27b9c"><view class="overview-title data-v-8ed27b9c"><svg wx:if="{{q}}" u-s="{{['d']}}" class="overview-icon data-v-8ed27b9c" u-i="8ed27b9c-7" bind:__l="__l" u-p="{{q}}"><path wx:if="{{o}}" class="data-v-8ed27b9c" u-i="8ed27b9c-8,8ed27b9c-7" bind:__l="__l" u-p="{{o}}"/><path wx:if="{{p}}" class="data-v-8ed27b9c" u-i="8ed27b9c-9,8ed27b9c-7" bind:__l="__l" u-p="{{p}}"/></svg><text class="data-v-8ed27b9c">快捷功能</text></view><view class="section-action data-v-8ed27b9c"><text class="action-text data-v-8ed27b9c">自定义</text><svg wx:if="{{s}}" class="data-v-8ed27b9c" u-s="{{['d']}}" u-i="8ed27b9c-10" bind:__l="__l" u-p="{{s}}"><path wx:if="{{r}}" class="data-v-8ed27b9c" u-i="8ed27b9c-11,8ed27b9c-10" bind:__l="__l" u-p="{{r}}"/></svg></view></view><view class="quick-actions data-v-8ed27b9c"><view wx:for="{{t}}" wx:for-item="action" wx:key="O" class="action-item data-v-8ed27b9c" catchtap="{{action.P}}"><view class="{{['action-icon-container', 'data-v-8ed27b9c', action.M]}}"><svg wx:if="{{action.a}}" u-s="{{['d']}}" class="action-svg data-v-8ed27b9c" u-i="{{action.d}}" bind:__l="__l" u-p="{{action.e}}"><path wx:if="{{action.c}}" class="data-v-8ed27b9c" u-i="{{action.b}}" bind:__l="__l" u-p="{{action.c}}"/></svg><svg wx:elif="{{action.f}}" u-s="{{['d']}}" class="action-svg data-v-8ed27b9c" u-i="{{action.i}}" bind:__l="__l" u-p="{{action.j}}"><path wx:if="{{action.h}}" class="data-v-8ed27b9c" u-i="{{action.g}}" bind:__l="__l" u-p="{{action.h}}"/></svg><svg wx:elif="{{action.k}}" u-s="{{['d']}}" class="action-svg data-v-8ed27b9c" u-i="{{action.n}}" bind:__l="__l" u-p="{{action.o}}"><path wx:if="{{action.m}}" class="data-v-8ed27b9c" u-i="{{action.l}}" bind:__l="__l" u-p="{{action.m}}"/></svg><svg wx:elif="{{action.p}}" u-s="{{['d']}}" class="action-svg data-v-8ed27b9c" u-i="{{action.s}}" bind:__l="__l" u-p="{{action.t}}"><path wx:if="{{action.r}}" class="data-v-8ed27b9c" u-i="{{action.q}}" bind:__l="__l" u-p="{{action.r}}"/></svg><svg wx:elif="{{action.v}}" u-s="{{['d']}}" class="action-svg data-v-8ed27b9c" u-i="{{action.y}}" bind:__l="__l" u-p="{{action.z}}"><path wx:if="{{action.x}}" class="data-v-8ed27b9c" u-i="{{action.w}}" bind:__l="__l" u-p="{{action.x}}"/></svg><svg wx:elif="{{action.A}}" u-s="{{['d']}}" class="action-svg data-v-8ed27b9c" u-i="{{action.D}}" bind:__l="__l" u-p="{{action.E}}"><path wx:if="{{action.C}}" class="data-v-8ed27b9c" u-i="{{action.B}}" bind:__l="__l" u-p="{{action.C}}"/></svg><svg wx:elif="{{action.F}}" u-s="{{['d']}}" class="action-svg data-v-8ed27b9c" u-i="{{action.K}}" bind:__l="__l" u-p="{{action.L}}"><path wx:if="{{action.H}}" class="data-v-8ed27b9c" u-i="{{action.G}}" bind:__l="__l" u-p="{{action.H}}"/><path wx:if="{{action.J}}" class="data-v-8ed27b9c" u-i="{{action.I}}" bind:__l="__l" u-p="{{action.J}}"/></svg></view><text class="action-text data-v-8ed27b9c">{{action.N}}</text></view></view></view><view class="business-overview data-v-8ed27b9c"><view class="overview-header data-v-8ed27b9c"><view class="overview-title data-v-8ed27b9c"><svg wx:if="{{w}}" u-s="{{['d']}}" class="overview-icon data-v-8ed27b9c" u-i="8ed27b9c-27" bind:__l="__l" u-p="{{w}}"><path wx:if="{{v}}" class="data-v-8ed27b9c" u-i="8ed27b9c-28,8ed27b9c-27" bind:__l="__l" u-p="{{v}}"/></svg><text class="data-v-8ed27b9c">业务概览</text></view><view class="tab-group data-v-8ed27b9c"><view class="{{['tab', 'data-v-8ed27b9c', x && 'active']}}" bindtap="{{y}}">今日</view><view class="{{['tab', 'data-v-8ed27b9c', z && 'active']}}" bindtap="{{A}}">本周</view><view class="{{['tab', 'data-v-8ed27b9c', B && 'active']}}" bindtap="{{C}}">本月</view></view></view><view class="overview-cards data-v-8ed27b9c"><view wx:for="{{D}}" wx:for-item="stat" wx:key="n" class="overview-card data-v-8ed27b9c" bindtap="{{stat.o}}"><text class="card-label data-v-8ed27b9c">{{stat.a}}</text><text class="card-value data-v-8ed27b9c">{{stat.b}}</text><view class="{{['card-trend', 'data-v-8ed27b9c', stat.m]}}"><svg wx:if="{{stat.c}}" class="data-v-8ed27b9c" u-s="{{['d']}}" u-i="{{stat.f}}" bind:__l="__l" u-p="{{stat.g}}"><path wx:if="{{stat.e}}" class="data-v-8ed27b9c" u-i="{{stat.d}}" bind:__l="__l" u-p="{{stat.e}}"/></svg><svg wx:else class="data-v-8ed27b9c" u-s="{{['d']}}" u-i="{{stat.j}}" bind:__l="__l" u-p="{{stat.k||''}}"><path wx:if="{{stat.i}}" class="data-v-8ed27b9c" u-i="{{stat.h}}" bind:__l="__l" u-p="{{stat.i}}"/></svg><text class="data-v-8ed27b9c">{{stat.l}}</text></view></view></view></view><view class="ai-assistant data-v-8ed27b9c"><view class="section-header data-v-8ed27b9c"><view class="overview-title data-v-8ed27b9c"><svg wx:if="{{I}}" u-s="{{['d']}}" class="overview-icon data-v-8ed27b9c" u-i="8ed27b9c-33" bind:__l="__l" u-p="{{I}}"><path wx:if="{{E}}" class="data-v-8ed27b9c" u-i="8ed27b9c-34,8ed27b9c-33" bind:__l="__l" u-p="{{E}}"/><path wx:if="{{F}}" class="data-v-8ed27b9c" u-i="8ed27b9c-35,8ed27b9c-33" bind:__l="__l" u-p="{{F}}"/><path wx:if="{{G}}" class="data-v-8ed27b9c" u-i="8ed27b9c-36,8ed27b9c-33" bind:__l="__l" u-p="{{G}}"/><path wx:if="{{H}}" class="data-v-8ed27b9c" u-i="8ed27b9c-37,8ed27b9c-33" bind:__l="__l" u-p="{{H}}"/></svg><text class="data-v-8ed27b9c">智能经营助手</text></view><view class="section-action data-v-8ed27b9c" bindtap="{{L}}"><text class="action-text data-v-8ed27b9c">更多</text><svg wx:if="{{K}}" class="data-v-8ed27b9c" u-s="{{['d']}}" u-i="8ed27b9c-38" bind:__l="__l" u-p="{{K}}"><path wx:if="{{J}}" class="data-v-8ed27b9c" u-i="8ed27b9c-39,8ed27b9c-38" bind:__l="__l" u-p="{{J}}"/></svg></view></view><view class="assistant-insights data-v-8ed27b9c"><view class="insight-card data-v-8ed27b9c" bindtap="{{Q}}"><view class="insight-icon-container data-v-8ed27b9c"><svg wx:if="{{N}}" u-s="{{['d']}}" class="insight-icon data-v-8ed27b9c" u-i="8ed27b9c-40" bind:__l="__l" u-p="{{N}}"><path wx:if="{{M}}" class="data-v-8ed27b9c" u-i="8ed27b9c-41,8ed27b9c-40" bind:__l="__l" u-p="{{M}}"/></svg></view><view class="insight-content data-v-8ed27b9c"><text class="insight-title data-v-8ed27b9c">消费趋势分析</text><text class="insight-desc data-v-8ed27b9c">近期顾客偏好变化，建议调整商品结构</text></view><view class="insight-action data-v-8ed27b9c"><svg wx:if="{{P}}" class="data-v-8ed27b9c" u-s="{{['d']}}" u-i="8ed27b9c-42" bind:__l="__l" u-p="{{P}}"><path wx:if="{{O}}" class="data-v-8ed27b9c" u-i="8ed27b9c-43,8ed27b9c-42" bind:__l="__l" u-p="{{O}}"/></svg></view></view><view class="insight-card warning data-v-8ed27b9c" bindtap="{{V}}"><view class="insight-icon-container warning data-v-8ed27b9c"><svg wx:if="{{S}}" u-s="{{['d']}}" class="insight-icon data-v-8ed27b9c" u-i="8ed27b9c-44" bind:__l="__l" u-p="{{S}}"><path wx:if="{{R}}" class="data-v-8ed27b9c" u-i="8ed27b9c-45,8ed27b9c-44" bind:__l="__l" u-p="{{R}}"/></svg></view><view class="insight-content data-v-8ed27b9c"><text class="insight-title data-v-8ed27b9c">竞品价格监测</text><text class="insight-desc data-v-8ed27b9c">同类商品市场价格下降5%，建议调整策略</text></view><view class="insight-action data-v-8ed27b9c"><svg wx:if="{{U}}" class="data-v-8ed27b9c" u-s="{{['d']}}" u-i="8ed27b9c-46" bind:__l="__l" u-p="{{U}}"><path wx:if="{{T}}" class="data-v-8ed27b9c" u-i="8ed27b9c-47,8ed27b9c-46" bind:__l="__l" u-p="{{T}}"/></svg></view></view><view class="insight-card opportunity data-v-8ed27b9c" bindtap="{{aa}}"><view class="insight-icon-container opportunity data-v-8ed27b9c"><svg wx:if="{{X}}" u-s="{{['d']}}" class="insight-icon data-v-8ed27b9c" u-i="8ed27b9c-48" bind:__l="__l" u-p="{{X}}"><path wx:if="{{W}}" class="data-v-8ed27b9c" u-i="8ed27b9c-49,8ed27b9c-48" bind:__l="__l" u-p="{{W}}"/></svg></view><view class="insight-content data-v-8ed27b9c"><text class="insight-title data-v-8ed27b9c">销售预测模型</text><text class="insight-desc data-v-8ed27b9c">根据历史数据，下周销售额预计增长12%</text></view><view class="insight-action data-v-8ed27b9c"><svg wx:if="{{Z}}" class="data-v-8ed27b9c" u-s="{{['d']}}" u-i="8ed27b9c-50" bind:__l="__l" u-p="{{Z}}"><path wx:if="{{Y}}" class="data-v-8ed27b9c" u-i="8ed27b9c-51,8ed27b9c-50" bind:__l="__l" u-p="{{Y}}"/></svg></view></view></view></view></view><view class="tab-bar data-v-8ed27b9c"><view wx:for="{{ab}}" wx:for-item="tab" wx:key="d" class="{{['tab-item', 'data-v-8ed27b9c', tab.e && 'active']}}" bindtap="{{tab.f}}"><view wx:if="{{tab.a}}" class="active-indicator data-v-8ed27b9c"></view><view class="{{['data-v-8ed27b9c', 'tab-icon', tab.b]}}"></view><text class="tab-text data-v-8ed27b9c">{{tab.c}}</text></view></view></view>