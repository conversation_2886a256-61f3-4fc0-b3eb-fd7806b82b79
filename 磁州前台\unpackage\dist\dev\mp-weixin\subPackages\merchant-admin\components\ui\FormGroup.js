"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  name: "FormGroup",
  props: {
    label: {
      type: String,
      required: true
    },
    isRequired: {
      type: Boolean,
      default: false
    },
    tip: {
      type: String,
      default: ""
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($props.label),
    b: $props.isRequired ? 1 : "",
    c: $props.tip
  }, $props.tip ? {
    d: common_vendor.t($props.tip)
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/components/ui/FormGroup.js.map
