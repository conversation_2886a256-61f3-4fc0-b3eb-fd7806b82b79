import Cookies from 'js-cookie'
import CryptoJS from 'crypto-js'

// Token相关常量
const TOKEN_KEY = 'cizhou_admin_token'
const REFRESH_TOKEN_KEY = 'cizhou_admin_refresh_token'
const USER_INFO_KEY = 'cizhou_admin_user_info'
const PERMISSIONS_KEY = 'cizhou_admin_permissions'

// 加密密钥
const SECRET_KEY = 'cizhou_admin_secret_2024'

// 加密函数
const encrypt = (data: string): string => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString()
}

// 解密函数
const decrypt = (encryptedData: string): string => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY)
    return bytes.toString(CryptoJS.enc.Utf8)
  } catch (error) {
    console.error('解密失败:', error)
    return ''
  }
}

// Token操作
export const getToken = (): string => {
  const encryptedToken = Cookies.get(TOKEN_KEY) || localStorage.getItem(TOKEN_KEY)
  if (!encryptedToken) return ''
  
  const token = decrypt(encryptedToken)
  return token
}

export const setToken = (token: string, rememberMe = false): void => {
  const encryptedToken = encrypt(token)
  
  if (rememberMe) {
    // 记住我：保存30天
    Cookies.set(TOKEN_KEY, encryptedToken, { expires: 30 })
    localStorage.setItem(TOKEN_KEY, encryptedToken)
  } else {
    // 不记住我：仅会话期间有效
    Cookies.set(TOKEN_KEY, encryptedToken)
    sessionStorage.setItem(TOKEN_KEY, encryptedToken)
  }
}

export const removeToken = (): void => {
  Cookies.remove(TOKEN_KEY)
  localStorage.removeItem(TOKEN_KEY)
  sessionStorage.removeItem(TOKEN_KEY)
}

// 刷新Token操作
export const getRefreshToken = (): string => {
  const encryptedToken = localStorage.getItem(REFRESH_TOKEN_KEY)
  if (!encryptedToken) return ''
  
  const token = decrypt(encryptedToken)
  return token
}

export const setRefreshToken = (token: string): void => {
  const encryptedToken = encrypt(token)
  localStorage.setItem(REFRESH_TOKEN_KEY, encryptedToken)
}

export const removeRefreshToken = (): void => {
  localStorage.removeItem(REFRESH_TOKEN_KEY)
}

// 用户信息操作
export const getUserInfo = (): any => {
  const encryptedUserInfo = localStorage.getItem(USER_INFO_KEY)
  if (!encryptedUserInfo) return null
  
  try {
    const userInfoStr = decrypt(encryptedUserInfo)
    return JSON.parse(userInfoStr)
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

export const setUserInfo = (userInfo: any): void => {
  const userInfoStr = JSON.stringify(userInfo)
  const encryptedUserInfo = encrypt(userInfoStr)
  localStorage.setItem(USER_INFO_KEY, encryptedUserInfo)
}

export const removeUserInfo = (): void => {
  localStorage.removeItem(USER_INFO_KEY)
}

// 权限信息操作
export const getPermissions = (): string[] => {
  const encryptedPermissions = localStorage.getItem(PERMISSIONS_KEY)
  if (!encryptedPermissions) return []
  
  try {
    const permissionsStr = decrypt(encryptedPermissions)
    return JSON.parse(permissionsStr)
  } catch (error) {
    console.error('获取权限信息失败:', error)
    return []
  }
}

export const setPermissions = (permissions: string[]): void => {
  const permissionsStr = JSON.stringify(permissions)
  const encryptedPermissions = encrypt(permissionsStr)
  localStorage.setItem(PERMISSIONS_KEY, encryptedPermissions)
}

export const removePermissions = (): void => {
  localStorage.removeItem(PERMISSIONS_KEY)
}

// 清除所有认证信息
export const clearAuth = (): void => {
  removeToken()
  removeRefreshToken()
  removeUserInfo()
  removePermissions()
}

// 检查Token是否过期
export const isTokenExpired = (token: string): boolean => {
  if (!token) return true
  
  try {
    // 解析JWT Token
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    
    // 检查是否过期（提前5分钟判断为过期）
    return payload.exp < currentTime + 300
  } catch (error) {
    console.error('Token解析失败:', error)
    return true
  }
}

// 获取Token剩余时间（秒）
export const getTokenRemainingTime = (token: string): number => {
  if (!token) return 0
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    
    return Math.max(0, payload.exp - currentTime)
  } catch (error) {
    console.error('Token解析失败:', error)
    return 0
  }
}

// 权限检查函数
export const hasPermission = (permission: string, userPermissions: string[] = []): boolean => {
  if (!permission) return true
  if (userPermissions.includes('*')) return true // 超级管理员
  return userPermissions.includes(permission)
}

export const hasAnyPermission = (permissions: string[], userPermissions: string[] = []): boolean => {
  if (!permissions || permissions.length === 0) return true
  if (userPermissions.includes('*')) return true // 超级管理员
  return permissions.some(permission => userPermissions.includes(permission))
}

export const hasAllPermissions = (permissions: string[], userPermissions: string[] = []): boolean => {
  if (!permissions || permissions.length === 0) return true
  if (userPermissions.includes('*')) return true // 超级管理员
  return permissions.every(permission => userPermissions.includes(permission))
}

// 角色检查函数
export const hasRole = (role: string, userRoles: string[] = []): boolean => {
  if (!role) return true
  if (userRoles.includes('admin')) return true // 管理员拥有所有角色
  return userRoles.includes(role)
}

export const hasAnyRole = (roles: string[], userRoles: string[] = []): boolean => {
  if (!roles || roles.length === 0) return true
  if (userRoles.includes('admin')) return true // 管理员拥有所有角色
  return roles.some(role => userRoles.includes(role))
}

// 生成随机字符串（用于状态参数等）
export const generateRandomString = (length = 32): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}
