<template>
  <!-- 活动卡片工厂组件 - 根据活动类型动态渲染不同卡片 -->
  <view class="activity-card-wrapper" @click="navigateToDetail">
    <!-- 使用条件渲染选择合适的卡片组件 -->
    <GroupBuyCard 
      v-if="cardType === 'GroupBuyCard'" 
      :item="item"
      @favorite="handleFavorite"
      @action="handleAction"
    />
    <FlashSaleCard 
      v-else-if="cardType === 'FlashSaleCard'" 
      :item="item"
      @favorite="handleFavorite"
      @action="handleAction"
    />
    <CouponCard 
      v-else-if="cardType === 'CouponCard'" 
      :item="item"
      @favorite="handleFavorite"
      @action="handleAction"
    />
    <DiscountCard 
      v-else-if="cardType === 'DiscountCard'" 
      :item="item"
      @favorite="handleFavorite"
      @action="handleAction"
    />
    <ActivityCard 
      v-else 
      :item="item"
      @favorite="handleFavorite"
      @action="handleAction"
    />
  </view>
</template>

<script setup>
import { computed } from 'vue';
import ActivityCard from './ActivityCard.vue';
import GroupBuyCard from './GroupBuyCard.vue';
import FlashSaleCard from './FlashSaleCard.vue';
import CouponCard from './CouponCard.vue';
import DiscountCard from './DiscountCard.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['navigate', 'favorite', 'action']);

// 根据活动类型确定使用哪种卡片组件
const cardType = computed(() => {
  const typeMap = {
    'groupBuy': 'GroupBuyCard',
    'flashSale': 'FlashSaleCard',
    'coupon': 'CouponCard',
    'discount': 'DiscountCard'
  };
  
  return typeMap[props.item.type] || 'ActivityCard';
});

// 跳转到活动详情页
function navigateToDetail() {
  emit('navigate', {
    id: props.item.id,
    type: props.item.type
  });
}

// 处理收藏事件
function handleFavorite(id) {
  emit('favorite', id);
}

// 处理操作按钮点击事件
function handleAction(data) {
  emit('action', data);
}
</script>

<style scoped>
.activity-card-wrapper {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.activity-card-wrapper:active {
  transform: scale(0.98);
}
</style> 