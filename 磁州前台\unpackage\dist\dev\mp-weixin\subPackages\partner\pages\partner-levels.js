"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "partner-levels",
  setup(__props) {
    const partnerInfo = common_vendor.reactive({
      level: 2,
      // 默认等级
      users: 28,
      orderAmount: 4200
    });
    const progressWidth = common_vendor.ref(65);
    const nextLevelRequirement = common_vendor.ref("2名用户或800元订单");
    const openedFaq = common_vendor.ref(-1);
    const showModal = common_vendor.ref(false);
    const canUpgrade = common_vendor.computed(() => {
      return partnerInfo.level < 4;
    });
    const getPartnerInfo = () => {
      setTimeout(() => {
        partnerInfo.level = 2;
        partnerInfo.users = 28;
        partnerInfo.orderAmount = 4200;
        calculateProgress();
      }, 500);
    };
    const calculateProgress = () => {
      if (partnerInfo.level === 1) {
        const userProgress = partnerInfo.users / 30 * 100;
        const amountProgress = partnerInfo.orderAmount / 5e3 * 100;
        progressWidth.value = Math.max(userProgress, amountProgress);
        const remainingUsers = Math.max(0, 30 - partnerInfo.users);
        const remainingAmount = Math.max(0, 5e3 - partnerInfo.orderAmount);
        nextLevelRequirement.value = `${remainingUsers}名用户或${remainingAmount}元订单`;
      } else if (partnerInfo.level === 2) {
        const userProgress = partnerInfo.users / 100 * 100;
        const amountProgress = partnerInfo.orderAmount / 2e4 * 100;
        progressWidth.value = Math.max(userProgress, amountProgress);
        const remainingUsers = Math.max(0, 100 - partnerInfo.users);
        const remainingAmount = Math.max(0, 2e4 - partnerInfo.orderAmount);
        nextLevelRequirement.value = `${remainingUsers}名用户或${remainingAmount}元订单`;
      } else if (partnerInfo.level === 3) {
        const userProgress = partnerInfo.users / 300 * 100;
        const amountProgress = partnerInfo.orderAmount / 5e4 * 100;
        progressWidth.value = Math.max(userProgress, amountProgress);
        const remainingUsers = Math.max(0, 300 - partnerInfo.users);
        const remainingAmount = Math.max(0, 5e4 - partnerInfo.orderAmount);
        nextLevelRequirement.value = `${remainingUsers}名用户或${remainingAmount}元订单`;
      } else {
        progressWidth.value = 100;
        nextLevelRequirement.value = "已达最高等级";
      }
      progressWidth.value = Math.min(progressWidth.value, 100);
    };
    const getLevelIcon = () => {
      const icons = [
        "/static/images/tabbar/partner-level-1.png",
        "/static/images/tabbar/partner-level-2.png",
        "/static/images/tabbar/partner-level-3.png",
        "/static/images/tabbar/partner-level-4.png"
      ];
      return icons[partnerInfo.level - 1] || icons[0];
    };
    const getLevelName = () => {
      const names = [
        "普通合伙人",
        "银牌合伙人",
        "金牌合伙人",
        "钻石合伙人"
      ];
      return names[partnerInfo.level - 1] || "未知等级";
    };
    const getNextLevelName = () => {
      const names = [
        "普通合伙人",
        "银牌合伙人",
        "金牌合伙人",
        "钻石合伙人"
      ];
      if (partnerInfo.level < 4) {
        return names[partnerInfo.level];
      }
      return "最高等级";
    };
    const getLevelDesc = () => {
      const descs = [
        "一级佣金5%，二级佣金2%",
        "一级佣金8%，二级佣金3%",
        "一级佣金12%，二级佣金5%",
        "一级佣金15%，二级佣金8%"
      ];
      return descs[partnerInfo.level - 1] || "";
    };
    const toggleFaq = (index) => {
      if (openedFaq.value === index) {
        openedFaq.value = -1;
      } else {
        openedFaq.value = index;
      }
    };
    const showUpgradeModal = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/partner/pages/partner-upgrade"
      });
    };
    const hideUpgradeModal = () => {
      showModal.value = false;
    };
    const getUpgradeRequirements = () => {
      if (partnerInfo.level === 1) {
        return "30名有效用户或5000元订单";
      } else if (partnerInfo.level === 2) {
        return "100名有效用户或20000元订单";
      } else if (partnerInfo.level === 3) {
        return "300名有效用户或50000元订单";
      }
      return "已达最高等级";
    };
    const getCurrentProgress = () => {
      return `${partnerInfo.users}名用户，${partnerInfo.orderAmount}元订单`;
    };
    const confirmUpgrade = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/partner/pages/partner-upgrade"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.navigateTo({
            url: "/subPackages/partner/pages/partner"
          });
        }
      });
    };
    common_vendor.onMounted(() => {
      getPartnerInfo();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: getLevelIcon(),
        d: common_vendor.t(getLevelName()),
        e: common_vendor.t(getLevelDesc()),
        f: canUpgrade.value
      }, canUpgrade.value ? {
        g: common_vendor.o(showUpgradeModal)
      } : {}, {
        h: common_vendor.t(nextLevelRequirement.value),
        i: progressWidth.value + "%",
        j: common_assets._imports_1$18,
        k: partnerInfo.level === 1 ? 1 : "",
        l: common_assets._imports_2$17,
        m: partnerInfo.level === 2 ? 1 : "",
        n: common_assets._imports_3$13,
        o: partnerInfo.level === 3 ? 1 : "",
        p: common_assets._imports_4$8,
        q: partnerInfo.level === 4 ? 1 : "",
        r: openedFaq.value === 0 ? 1 : "",
        s: common_assets._imports_5$8,
        t: openedFaq.value === 0
      }, openedFaq.value === 0 ? {} : {}, {
        v: common_vendor.o(($event) => toggleFaq(0)),
        w: openedFaq.value === 1 ? 1 : "",
        x: common_assets._imports_5$8,
        y: openedFaq.value === 1
      }, openedFaq.value === 1 ? {} : {}, {
        z: common_vendor.o(($event) => toggleFaq(1)),
        A: openedFaq.value === 2 ? 1 : "",
        B: common_assets._imports_5$8,
        C: openedFaq.value === 2
      }, openedFaq.value === 2 ? {} : {}, {
        D: common_vendor.o(($event) => toggleFaq(2)),
        E: openedFaq.value === 3 ? 1 : "",
        F: common_assets._imports_5$8,
        G: openedFaq.value === 3
      }, openedFaq.value === 3 ? {} : {}, {
        H: common_vendor.o(($event) => toggleFaq(3)),
        I: showModal.value
      }, showModal.value ? {
        J: common_vendor.t(getNextLevelName()),
        K: common_vendor.o(hideUpgradeModal),
        L: common_assets._imports_6$8,
        M: common_vendor.t(getLevelName()),
        N: common_vendor.t(getUpgradeRequirements()),
        O: common_vendor.t(getCurrentProgress()),
        P: common_vendor.o(confirmUpgrade),
        Q: common_vendor.o(() => {
        }),
        R: common_vendor.o(hideUpgradeModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-eeb58a13"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/partner/pages/partner-levels.js.map
