{"version": 3, "file": "partner-qrcode.js", "sources": ["subPackages/partner/pages/partner-qrcode.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGFydG5lclxwYWdlc1xwYXJ0bmVyLXFyY29kZS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"qrcode-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">我的二维码</view>\r\n      <view class=\"navbar-right\">\r\n        <!-- 预留位置与发布页面保持一致 -->\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 添加顶部安全区域 -->\r\n    <view class=\"safe-area-top\"></view>\r\n\r\n    <!-- 二维码卡片 -->\r\n    <view class=\"qrcode-card\">\r\n      <view class=\"user-info\">\r\n        <image class=\"avatar\" :src=\"userInfo.avatar || '/static/images/avatar/default.png'\" mode=\"aspectFill\"></image>\r\n        <view class=\"user-detail\">\r\n          <view class=\"nickname\">{{userInfo.nickname || '游客'}}</view>\r\n          <view class=\"level-tag\">{{getLevelName()}}</view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"qrcode-wrapper\">\r\n        <image class=\"qrcode-bg\" src=\"/static/images/tabbar/qrcode-bg.png\" mode=\"aspectFill\"></image>\r\n        <view class=\"qrcode-box\">\r\n          <image class=\"qrcode-image\" :src=\"qrCodeUrl\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"qrcode-tip\">扫描二维码加入我的团队</view>\r\n      </view>\r\n      \r\n      <view class=\"action-buttons\">\r\n        <button class=\"action-btn save-btn\" @tap=\"saveQrCode\">\r\n          <image class=\"btn-icon\" src=\"/static/images/tabbar/save.png\" mode=\"aspectFit\"></image>\r\n          <text>保存图片</text>\r\n        </button>\r\n        <button class=\"action-btn share-btn\" @tap=\"shareQrCode\">\r\n          <image class=\"btn-icon\" src=\"/static/images/tabbar/share.png\" mode=\"aspectFit\"></image>\r\n          <text>分享</text>\r\n        </button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 推广提示 -->\r\n    <view class=\"promotion-tips\">\r\n      <view class=\"tips-header\">\r\n        <text class=\"tips-title\">推广提示</text>\r\n      </view>\r\n      <view class=\"tips-content\">\r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-dot\"></view>\r\n          <text class=\"tip-text\">将二维码分享给好友，邀请他们扫码注册</text>\r\n        </view>\r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-dot\"></view>\r\n          <text class=\"tip-text\">好友通过您的二维码注册后，将自动成为您的团队成员</text>\r\n        </view>\r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-dot\"></view>\r\n          <text class=\"tip-text\">团队成员消费时，您将获得相应佣金奖励</text>\r\n        </view>\r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-dot\"></view>\r\n          <text class=\"tip-text\">佣金比例根据您的合伙人等级而定，等级越高佣金越多</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分享弹窗 -->\r\n    <view class=\"share-modal\" v-if=\"showShareModal\">\r\n      <view class=\"modal-mask\" @tap=\"closeShareModal\"></view>\r\n      <view class=\"modal-content\">\r\n        <view class=\"modal-title\">分享到</view>\r\n        <view class=\"share-options\">\r\n          <view class=\"share-option\" @tap=\"shareToWechat\">\r\n            <image src=\"/static/images/tabbar/wechat.png\" mode=\"aspectFit\"></image>\r\n            <text>微信好友</text>\r\n          </view>\r\n          <view class=\"share-option\" @tap=\"shareToMoments\">\r\n            <image src=\"/static/images/tabbar/moments.png\" mode=\"aspectFit\"></image>\r\n            <text>朋友圈</text>\r\n          </view>\r\n          <view class=\"share-option\" @tap=\"shareToQQ\">\r\n            <image src=\"/static/images/tabbar/qq.png\" mode=\"aspectFit\"></image>\r\n            <text>QQ好友</text>\r\n          </view>\r\n          <view class=\"share-option\" @tap=\"copyLink\">\r\n            <image src=\"/static/images/tabbar/copy-link.png\" mode=\"aspectFit\"></image>\r\n            <text>复制链接</text>\r\n          </view>\r\n        </view>\r\n        <button class=\"cancel-btn\" @tap=\"closeShareModal\">取消</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue';\r\nimport { getLocalUserInfo } from '@/utils/userProfile.js';\r\n\r\n// 响应式数据\r\nconst userInfo = reactive({\r\n  nickname: '',\r\n  avatar: '',\r\n  level: 1\r\n});\r\nconst qrCodeUrl = ref('/static/images/tabbar/qrcode-demo.png'); // 默认二维码图片\r\nconst showShareModal = ref(false);\r\n\r\n// 获取用户信息\r\nconst getUserInfo = () => {\r\n  const localUserInfo = getLocalUserInfo();\r\n  if (localUserInfo) {\r\n    userInfo.nickname = localUserInfo.nickname;\r\n    userInfo.avatar = localUserInfo.avatar;\r\n    userInfo.level = localUserInfo.level || 1;\r\n  }\r\n};\r\n\r\n// 获取等级名称\r\nconst getLevelName = () => {\r\n  const names = {\r\n    1: '普通合伙人',\r\n    2: '银牌合伙人',\r\n    3: '金牌合伙人',\r\n    4: '钻石合伙人'\r\n  };\r\n  return names[userInfo.level] || names[1];\r\n};\r\n\r\n// 生成二维码\r\nconst generateQrCode = () => {\r\n  // 这里应该调用API生成二维码，这里使用模拟数据\r\n  // 实际开发中，应该调用后端API获取二维码图片URL或使用前端库生成\r\n  \r\n  // 模拟生成二维码的过程\r\n  setTimeout(() => {\r\n    // 实际项目中应该替换为真实的二维码图片URL\r\n    qrCodeUrl.value = '/static/images/tabbar/qrcode-demo.png';\r\n  }, 500);\r\n};\r\n\r\n// 保存二维码到相册\r\nconst saveQrCode = () => {\r\n  uni.showLoading({\r\n    title: '保存中...'\r\n  });\r\n  \r\n  // 将二维码保存到相册\r\n  uni.saveImageToPhotosAlbum({\r\n    filePath: qrCodeUrl.value,\r\n    success: () => {\r\n      uni.hideLoading();\r\n      uni.showToast({\r\n        title: '保存成功',\r\n        icon: 'success'\r\n      });\r\n    },\r\n    fail: (err) => {\r\n      uni.hideLoading();\r\n      \r\n      // 如果是权限问题，提示用户授权\r\n      if (err.errMsg.indexOf('auth deny') !== -1) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '需要您授权保存图片到相册',\r\n          confirmText: '去授权',\r\n          success: (res) => {\r\n            if (res.confirm) {\r\n              uni.openSetting();\r\n            }\r\n          }\r\n        });\r\n      } else {\r\n        uni.showToast({\r\n          title: '保存失败',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 分享二维码\r\nconst shareQrCode = () => {\r\n  showShareModal.value = true;\r\n};\r\n\r\n// 关闭分享弹窗\r\nconst closeShareModal = () => {\r\n  showShareModal.value = false;\r\n};\r\n\r\n// 分享到微信\r\nconst shareToWechat = () => {\r\n  // 调用分享API\r\n  uni.share({\r\n    provider: 'weixin',\r\n    scene: 'WXSceneSession',\r\n    type: 2,\r\n    imageUrl: qrCodeUrl.value,\r\n    title: `${userInfo.nickname}邀请您加入同城`,\r\n    summary: '扫码注册，享受更多优惠',\r\n    success: () => {\r\n      closeShareModal();\r\n    },\r\n    fail: () => {\r\n      uni.showToast({\r\n        title: '分享失败',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  });\r\n};\r\n\r\n// 分享到朋友圈\r\nconst shareToMoments = () => {\r\n  // 调用分享API\r\n  uni.share({\r\n    provider: 'weixin',\r\n    scene: 'WXSceneTimeline',\r\n    type: 2,\r\n    imageUrl: qrCodeUrl.value,\r\n    title: `${userInfo.nickname}邀请您加入同城`,\r\n    summary: '扫码注册，享受更多优惠',\r\n    success: () => {\r\n      closeShareModal();\r\n    },\r\n    fail: () => {\r\n      uni.showToast({\r\n        title: '分享失败',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  });\r\n};\r\n\r\n// 分享到QQ\r\nconst shareToQQ = () => {\r\n  // 调用分享API\r\n  uni.share({\r\n    provider: 'qq',\r\n    type: 2,\r\n    imageUrl: qrCodeUrl.value,\r\n    title: `${userInfo.nickname}邀请您加入同城`,\r\n    summary: '扫码注册，享受更多优惠',\r\n    success: () => {\r\n      closeShareModal();\r\n    },\r\n    fail: () => {\r\n      uni.showToast({\r\n        title: '分享失败',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  });\r\n};\r\n\r\n// 复制链接\r\nconst copyLink = () => {\r\n  // 生成邀请链接\r\n  const inviteLink = `https://example.com/register?inviter=${userInfo.id}`;\r\n  \r\n  uni.setClipboardData({\r\n    data: inviteLink,\r\n    success: () => {\r\n      uni.showToast({\r\n        title: '链接已复制',\r\n        icon: 'success'\r\n      });\r\n      closeShareModal();\r\n    }\r\n  });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取用户信息\r\n  getUserInfo();\r\n  // 生成二维码\r\n  generateQrCode();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.qrcode-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30rpx;\r\n  padding-top: calc(44px + var(--status-bar-height));\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 88rpx;\r\n  padding: 0 30rpx;\r\n  padding-top: 44px; /* 状态栏高度 */\r\n  position: fixed; /* 改为固定定位 */\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-image: linear-gradient(135deg, #0066FF, #0052CC);\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);\r\n  z-index: 100; /* 提高z-index确保在最上层 */\r\n}\r\n\r\n.navbar-title {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  color: #ffffff;\r\n  font-size: 36rpx;\r\n  font-weight: 700;\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-left {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  z-index: 20; /* 确保在标题上层，可以被点击 */\r\n}\r\n\r\n.back-icon {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.safe-area-top {\r\n  height: var(--status-bar-height);\r\n  width: 100%;\r\n  background-image: linear-gradient(135deg, #0066FF, #0052CC);\r\n}\r\n\r\n/* 二维码卡片 */\r\n.qrcode-card {\r\n  margin: 30rpx;\r\n  padding: 40rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.avatar {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  border-radius: 50%;\r\n  margin-right: 20rpx;\r\n  border: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.user-detail {\r\n  flex: 1;\r\n}\r\n\r\n.nickname {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.level-tag {\r\n  display: inline-block;\r\n  font-size: 24rpx;\r\n  color: #0066FF;\r\n  background-color: rgba(0, 102, 255, 0.1);\r\n  padding: 4rpx 16rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.qrcode-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.qrcode-bg {\r\n  width: 400rpx;\r\n  height: 400rpx;\r\n  opacity: 0.05;\r\n  position: absolute;\r\n  z-index: 1;\r\n}\r\n\r\n.qrcode-box {\r\n  width: 400rpx;\r\n  height: 400rpx;\r\n  padding: 20rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.qrcode-image {\r\n  width: 360rpx;\r\n  height: 360rpx;\r\n}\r\n\r\n.qrcode-tip {\r\n  margin-top: 20rpx;\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: space-around;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  background-color: transparent;\r\n  border: none;\r\n  padding: 0;\r\n  width: 200rpx;\r\n  \r\n  &::after {\r\n    border: none;\r\n  }\r\n  \r\n  .btn-icon {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    margin-bottom: 10rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n  }\r\n}\r\n\r\n.save-btn {\r\n  color: #0066FF;\r\n}\r\n\r\n.share-btn {\r\n  color: #FF6B00;\r\n}\r\n\r\n/* 推广提示 */\r\n.promotion-tips {\r\n  margin: 30rpx;\r\n  padding: 30rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.tips-header {\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  \r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    left: 0;\r\n    bottom: -10rpx;\r\n    width: 60rpx;\r\n    height: 4rpx;\r\n    background: linear-gradient(to right, #0066FF, #36CBCB);\r\n    border-radius: 2rpx;\r\n  }\r\n}\r\n\r\n.tips-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.tips-content {\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.tip-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 20rpx;\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.tip-dot {\r\n  width: 12rpx;\r\n  height: 12rpx;\r\n  border-radius: 50%;\r\n  background-color: #0066FF;\r\n  margin-top: 12rpx;\r\n  margin-right: 15rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.tip-text {\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 分享弹窗 */\r\n.share-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 999;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-end;\r\n}\r\n\r\n.modal-mask {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.modal-content {\r\n  width: 100%;\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx 20rpx 0 0;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  position: relative;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-title {\r\n  text-align: center;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  padding: 30rpx 0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.share-options {\r\n  display: flex;\r\n  padding: 40rpx 30rpx;\r\n  justify-content: space-around;\r\n}\r\n\r\n.share-option {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  \r\n  image {\r\n    width: 100rpx;\r\n    height: 100rpx;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n  }\r\n}\r\n\r\n.cancel-btn {\r\n  height: 100rpx;\r\n  line-height: 100rpx;\r\n  text-align: center;\r\n  font-size: 32rpx;\r\n  color: #333333;\r\n  border-top: 10rpx solid #f5f5f5;\r\n  \r\n  &::after {\r\n    border: none;\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/partner/pages/partner-qrcode.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "getLocalUserInfo", "uni", "onMounted"], "mappings": ";;;;;;;AAyGA,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,IACT,CAAC;AACD,UAAM,YAAYC,cAAAA,IAAI,uCAAuC;AAC7D,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAGhC,UAAM,cAAc,MAAM;AACxB,YAAM,gBAAgBC,kBAAAA;AACtB,UAAI,eAAe;AACjB,iBAAS,WAAW,cAAc;AAClC,iBAAS,SAAS,cAAc;AAChC,iBAAS,QAAQ,cAAc,SAAS;AAAA,MACzC;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACP;AACE,aAAO,MAAM,SAAS,KAAK,KAAK,MAAM,CAAC;AAAA,IACzC;AAGA,UAAM,iBAAiB,MAAM;AAK3B,iBAAW,MAAM;AAEf,kBAAU,QAAQ;AAAA,MACnB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,aAAa,MAAM;AACvBC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGDA,oBAAAA,MAAI,uBAAuB;AAAA,QACzB,UAAU,UAAU;AAAA,QACpB,SAAS,MAAM;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAG,MAAC,YAAW;AAGf,cAAI,IAAI,OAAO,QAAQ,WAAW,MAAM,IAAI;AAC1CA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,aAAa;AAAA,cACb,SAAS,CAAC,QAAQ;AAChB,oBAAI,IAAI,SAAS;AACfA,gCAAG,MAAC,YAAW;AAAA,gBAChB;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACT,OAAa;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,gBAAgB,MAAM;AAE1BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU,UAAU;AAAA,QACpB,OAAO,GAAG,SAAS,QAAQ;AAAA,QAC3B,SAAS;AAAA,QACT,SAAS,MAAM;AACb;QACD;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAE3BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU,UAAU;AAAA,QACpB,OAAO,GAAG,SAAS,QAAQ;AAAA,QAC3B,SAAS;AAAA,QACT,SAAS,MAAM;AACb;QACD;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,YAAY,MAAM;AAEtBA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU,UAAU;AAAA,QACpB,OAAO,GAAG,SAAS,QAAQ;AAAA,QAC3B,SAAS;AAAA,QACT,SAAS,MAAM;AACb;QACD;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AAErB,YAAM,aAAa,wCAAwC,SAAS,EAAE;AAEtEA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AACD;QACD;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGAC,kBAAAA,UAAU,MAAM;AAEd;AAEA;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjSD,GAAG,WAAW,eAAe;"}