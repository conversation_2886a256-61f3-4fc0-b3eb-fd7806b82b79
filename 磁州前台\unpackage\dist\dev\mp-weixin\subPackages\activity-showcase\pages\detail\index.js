"use strict";
const common_vendor = require("../../../../common/vendor.js");
const utils_navigation = require("../../../../utils/navigation.js");
const common_assets = require("../../../../common/assets.js");
const DistributionSection = () => "../../../../components/distribution-section.js";
const _sfc_main = {
  components: {
    DistributionSection
  },
  data() {
    return {
      id: null,
      type: "group",
      // 默认为拼团活动
      groupbuy: {},
      shop: {},
      statusBarHeight: 20,
      navbarHeight: 82,
      tabs: [
        { name: "商品详情" },
        { name: "活动规则" },
        { name: "用户评价" }
      ],
      currentTab: 0,
      currentSwiperIndex: 0,
      countdown: {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0
      },
      countdownTimer: null,
      loading: true,
      reviewTags: ["全部", "有图(12)", "好评(32)", "差评(1)"],
      showBuyPopup: false,
      buyQuantity: 1,
      currentBuyType: "normal",
      showSharePopup: false,
      activeReviewTag: 0,
      isFavorite: false,
      recommendProducts: [
        {
          id: 1,
          name: "老磁州水饺",
          price: 25.9,
          image: "/static/demo/food2.jpg"
        },
        {
          id: 2,
          name: "特色烤肉套餐",
          price: 59.9,
          image: "/static/demo/food1.jpg"
        },
        {
          id: 3,
          name: "精品麻辣烫",
          price: 19.9,
          image: "/static/demo/food3.jpg"
        },
        {
          id: 4,
          name: "招牌炸鸡",
          price: 39.9,
          image: "/static/demo/food4.jpg"
        }
      ],
      // 适用门店列表
      storeList: [
        {
          id: 1,
          name: "老磁州美食坊(中心广场店)",
          logo: "/static/demo/shop-logo.png",
          address: "磁州市中心广场东路88号",
          distance: "1km"
        },
        {
          id: 2,
          name: "老磁州美食坊(西湖店)",
          logo: "/static/demo/shop-logo.png",
          address: "磁州市西湖路120号",
          distance: "3.6km"
        },
        {
          id: 3,
          name: "老磁州美食坊(北城店)",
          logo: "/static/demo/shop-logo.png",
          address: "磁州市北城区商业街56号",
          distance: "5.2km"
        }
      ],
      // 本店其他拼团
      otherGroups: [
        {
          id: 101,
          title: "老磁州特色凉皮 2人套餐",
          image: "/static/demo/food2.jpg",
          groupPrice: "29.9",
          marketPrice: "59.9",
          groupSize: 2,
          soldCount: 3e3,
          discount: "5.3折热销中",
          distance: "1km",
          soldTag: "新品",
          usageInfo: "到店使用",
          shopName: "磁州同城折扣店"
        },
        {
          id: 102,
          title: "老磁州肉夹馍 家庭套餐",
          image: "/static/demo/food3.jpg",
          groupPrice: "39.9",
          marketPrice: "69.9",
          groupSize: 3,
          soldCount: 1e3,
          discount: "6.2折热销中",
          distance: "1.2km",
          soldTag: "",
          usageInfo: "到店使用",
          shopName: "磁州同城折扣店"
        },
        {
          id: 103,
          title: "老磁州特色小吃拼盘",
          image: "/static/demo/food1.jpg",
          groupPrice: "49.9",
          marketPrice: "79.9",
          groupSize: 4,
          soldCount: 900,
          discount: "5.8折热销中",
          distance: "1.5km",
          soldTag: "新品",
          usageInfo: "免预约",
          shopName: "磁州同城折扣店"
        }
      ]
    };
  },
  computed: {
    // 根据活动类型显示对应的标题
    activityTypeTitle() {
      switch (this.type) {
        case "flash":
          return "秒杀详情";
        case "group":
          return "拼团详情";
        case "discount":
          return "满减详情";
        case "coupon":
          return "优惠券详情";
        default:
          return "活动详情";
      }
    },
    // 计算折扣
    discount() {
      const originalPrice = parseFloat(this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      if (originalPrice <= 0)
        return 10;
      const discount = Math.round(groupPrice / originalPrice * 10);
      return discount;
    },
    // 计算折扣百分比
    discountPercent() {
      const marketPrice = parseFloat(this.groupbuy.marketPrice || this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      if (marketPrice <= 0)
        return 0;
      const discountPercent = Math.round(100 - groupPrice / marketPrice * 100);
      return discountPercent;
    },
    // 计算节省金额
    saveAmount() {
      const marketPrice = parseFloat(this.groupbuy.marketPrice || this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      return (marketPrice - groupPrice).toFixed(2);
    },
    // 计算进度条宽度
    progressWidth() {
      if (this.groupbuy.targetCount <= 0)
        return "0%";
      const progress = this.groupbuy.soldCount / this.groupbuy.targetCount * 100;
      return progress > 100 ? "100%" : `${progress}%`;
    },
    // 格式化团购价格
    formattedGroupPrice() {
      if (typeof this.groupbuy.groupPrice === "number") {
        return this.groupbuy.groupPrice.toFixed(2);
      }
      return this.groupbuy.groupPrice;
    },
    // 格式化原价
    formattedOriginalPrice() {
      if (typeof this.groupbuy.originalPrice === "number") {
        return this.groupbuy.originalPrice.toFixed(2);
      }
      return this.groupbuy.originalPrice;
    },
    // 格式化市场价
    formattedMarketPrice() {
      if (typeof this.groupbuy.marketPrice === "number") {
        return this.groupbuy.marketPrice.toFixed(2);
      }
      return this.groupbuy.marketPrice || this.formattedOriginalPrice;
    },
    // 格式化日常价
    formattedRegularPrice() {
      if (typeof this.groupbuy.regularPrice === "number") {
        return this.groupbuy.regularPrice.toFixed(2);
      }
      return this.groupbuy.regularPrice || this.formattedOriginalPrice;
    },
    // 计算与市场价的差价
    marketPriceDiff() {
      const marketPrice = parseFloat(this.groupbuy.marketPrice || this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      return (marketPrice - groupPrice).toFixed(2);
    },
    // 计算与日常价的差价
    regularPriceDiff() {
      const regularPrice = parseFloat(this.groupbuy.regularPrice || this.groupbuy.originalPrice);
      const groupPrice = parseFloat(this.groupbuy.groupPrice);
      return (regularPrice - groupPrice).toFixed(2);
    },
    // 参与人数展示
    participantCount() {
      return this.groupbuy.soldCount > 0 ? this.groupbuy.soldCount : 0;
    },
    // 剩余参与人数
    remainingCount() {
      const remaining = this.groupbuy.targetCount - this.groupbuy.soldCount;
      return remaining > 0 ? remaining : 0;
    },
    // 是否已达到目标人数
    isTargetReached() {
      return this.groupbuy.soldCount >= this.groupbuy.targetCount;
    }
  },
  onLoad(options) {
    if (options && options.id) {
      this.id = options.id;
    }
    if (options && options.type) {
      this.type = options.type;
    }
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 62;
    setTimeout(() => {
      this.loadGroupbuyDetail();
      this.startCountdown();
    }, 500);
  },
  onUnload() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  },
  // 分享功能
  onShareAppMessage() {
    return {
      title: this.groupbuy.title,
      path: `/subPackages/activity-showcase/pages/detail/index?id=${this.id}`,
      imageUrl: this.groupbuy.images[0]
    };
  },
  methods: {
    // 加载团购详情
    loadGroupbuyDetail() {
      this.loading = true;
      setTimeout(() => {
        this.groupbuy = {
          id: this.id || 1,
          title: "【磁州特色】老磁州脆皮锅贴 正宗手工制作 20个/份",
          images: [
            "/static/demo/food1.jpg",
            "/static/demo/food2.jpg",
            "/static/demo/food3.jpg"
          ],
          groupPrice: "39.90",
          originalPrice: "59.90",
          marketPrice: "69.90",
          regularPrice: "49.90",
          soldCount: 126,
          targetCount: 200,
          startTime: /* @__PURE__ */ new Date("2023-12-01"),
          endTime: /* @__PURE__ */ new Date("2023-12-31"),
          tags: ["限时特惠", "热销爆款", "免预约"],
          shopName: "老磁州美食坊",
          shopLogo: "/static/demo/shop-logo.png",
          shopRating: 4.8,
          ratingCount: 238,
          stock: 500,
          description: "老磁州脆皮锅贴，采用传统工艺精心制作，外皮金黄酥脆，内馅鲜嫩多汁，是磁州地区特色美食。本店锅贴选用上等面粉和新鲜猪肉制作，不添加任何防腐剂和人工色素。",
          detailImages: [
            "/static/demo/detail1.jpg",
            "/static/demo/detail2.jpg"
          ],
          rules: [
            "团购券有效期为购买后30天内，请在有效期内使用",
            "营业时间：周一至周日 10:00-22:00，法定节假日正常营业",
            "每张团购券限1人使用，不再与店内其他优惠同享",
            "团购券使用前请提前致电商家预约",
            "如有特殊情况，请与商家协商解决"
          ],
          reviews: [
            {
              id: 1,
              user: {
                name: "张先生",
                avatar: "/static/demo/avatar1.png"
              },
              rating: 5,
              content: "味道很赞，皮薄馅大，一口下去超级满足！店家服务也很好，环境整洁，值得推荐！",
              images: [
                "/static/demo/review1.jpg",
                "/static/demo/review2.jpg"
              ],
              time: "2023-11-28",
              likes: 12
            },
            {
              id: 2,
              user: {
                name: "李女士",
                avatar: "/static/demo/avatar2.png"
              },
              rating: 4,
              content: "第二次来购买了，锅贴依然那么好吃，就是今天人有点多，等了一会儿。",
              images: [],
              time: "2023-11-25",
              likes: 5
            }
          ],
          prices: [
            {
              id: 1,
              name: "标准20个装",
              price: 39.9
            },
            {
              id: 2,
              name: "超值30个装",
              price: 59.9
            },
            {
              id: 3,
              name: "家庭40个装",
              price: 75.9
            }
          ],
          packageItems: [
            {
              name: "脆皮锅贴",
              quantity: 20,
              unit: "个",
              desc: "传统手工制作，外皮酥脆，内馅多汁"
            },
            {
              name: "特制蘸料",
              quantity: 2,
              unit: "份",
              desc: "秘制配方，提味增香"
            }
          ],
          enableGroupBuy: true,
          minGroupSize: 3
        };
        this.shop = {
          id: 1,
          name: this.groupbuy.shopName,
          logo: this.groupbuy.shopLogo,
          rating: this.groupbuy.shopRating,
          isFollowed: false
        };
        this.loading = false;
      }, 1e3);
    },
    // 开始倒计时
    startCountdown() {
      this.updateCountdown();
      this.countdownTimer = setInterval(() => {
        this.updateCountdown();
      }, 1e3);
    },
    // 更新倒计时
    updateCountdown() {
      if (!this.groupbuy || !this.groupbuy.endTime)
        return;
      const now = (/* @__PURE__ */ new Date()).getTime();
      const endTime = new Date(this.groupbuy.endTime).getTime();
      const diff = endTime - now;
      if (diff <= 0) {
        this.countdown = { days: 0, hours: 0, minutes: 0, seconds: 0 };
        if (this.countdownTimer) {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
        }
        return;
      }
      const days = Math.floor(diff / (1e3 * 60 * 60 * 24));
      const hours = Math.floor(diff % (1e3 * 60 * 60 * 24) / (1e3 * 60 * 60));
      const minutes = Math.floor(diff % (1e3 * 60 * 60) / (1e3 * 60));
      const seconds = Math.floor(diff % (1e3 * 60) / 1e3);
      this.countdown = {
        days: days < 10 ? "0" + days : days,
        hours: hours < 10 ? "0" + hours : hours,
        minutes: minutes < 10 ? "0" + minutes : minutes,
        seconds: seconds < 10 ? "0" + seconds : seconds
      };
    },
    // 获取模拟数据
    getMockData(id) {
      id = parseInt(id);
      const mockData = {
        id,
        title: "商户特惠 | 正宗磁州肉夹馍 3人套餐",
        images: [
          "/static/demo/food1.jpg",
          "/static/demo/food2.jpg",
          "/static/demo/food3.jpg"
        ],
        groupPrice: 59.9,
        originalPrice: 99,
        marketPrice: 99,
        regularPrice: 79,
        soldCount: 358,
        targetCount: 500,
        startTime: /* @__PURE__ */ new Date("2023-08-15 10:00:00"),
        endTime: new Date(Date.now() + 864e5 * 3),
        // 3天后结束
        tags: ["本地美食", "限时特惠", "当日现做"],
        shopName: "老磁州肉夹馍",
        shopLogo: "/static/demo/shop-logo.png",
        shopRating: 4.9,
        ratingCount: 238,
        stock: 200,
        description: "精选五花肉，配以秘制调料，现烤现卖，外酥里嫩，肉香四溢。3人套餐包含肉夹馍3个、凉皮2份、特色饮料3杯，到店消费更有特色小吃免费品尝！",
        detailImages: [
          "/static/demo/food-detail1.jpg",
          "/static/demo/food-detail2.jpg"
        ],
        packageItems: [
          { name: "招牌肉夹馍", quantity: 3, unit: "个", desc: "精选五花肉，外酥里嫩" },
          { name: "特色凉皮", quantity: 2, unit: "份", desc: "劲道爽滑，酸辣可口" },
          { name: "饮料", quantity: 3, unit: "杯", desc: "可乐/雪碧/果茶任选" },
          { name: "赠品小食", quantity: 1, unit: "份", desc: "薯条/鸡米花任选一份" }
        ],
        rules: [
          "有效期：购买后7天内有效",
          "使用方式：到店出示券码核销",
          "营业时间：每日10:00-21:00",
          "地址：磁州市中心广场东路88号",
          "每人限购5份",
          "支持随时退款",
          "团购专享价格",
          "到店消费可免费获赠热茶一杯"
        ],
        enableGroupBuy: true,
        // 是否开启拼团
        minGroupSize: 3,
        // 最低成团人数
        reviews: [
          {
            username: "张先生",
            avatar: "/static/demo/avatar1.png",
            rating: 5,
            time: "2023-10-15",
            content: "肉夹馍非常好吃，肉多且入味，饼也烤得恰到好处，酥脆可口。团购价格很实惠，强烈推荐！",
            images: [
              "/static/demo/review1.jpg",
              "/static/demo/review2.jpg"
            ]
          },
          {
            username: "李女士",
            avatar: "/static/demo/avatar2.png",
            rating: 5,
            time: "2023-10-12",
            content: "配送速度很快，包装也很好，肉夹馍还是热乎的。味道很正宗，和老家的味道一样，很满意。",
            images: [
              "/static/demo/review3.jpg"
            ]
          }
        ],
        prices: [
          { label: "市场价", value: "¥30" },
          { label: "日常价", value: "¥25" }
        ],
        viewCount: 1268
      };
      if (id === 102) {
        mockData.title = "夏日清凉 | 网红冰淇淋双人套餐";
        mockData.groupPrice = 39.9;
        mockData.originalPrice = 68;
        mockData.regularPrice = 58;
        mockData.soldCount = 128;
        mockData.shopName = "冰雪甜品屋";
        mockData.description = "精选进口食材，纯手工制作，口感细腻，甜而不腻。套餐包含：招牌冰淇淋2份，水果沙拉1份，特调饮品2杯。";
      } else if (id === 103) {
        mockData.title = "周末特惠 | 精致下午茶套餐";
        mockData.groupPrice = 88;
        mockData.originalPrice = 138;
        mockData.regularPrice = 118;
        mockData.soldCount = 42;
        mockData.shopName = "巴黎花园咖啡";
        mockData.description = "法式精致下午茶，环境优雅，食材新鲜。套餐包含：精选茶饮2杯，马卡龙4个，水果塔2个，提拉米苏1份。";
      } else if (id === 104) {
        mockData.title = "磁州特色 | 手工水饺30个";
        mockData.groupPrice = 29.9;
        mockData.originalPrice = 45;
        mockData.regularPrice = 39;
        mockData.soldCount = 84;
        mockData.shopName = "老街饺子馆";
        mockData.description = "选用本地新鲜食材，现包现煮，皮薄馅大，鲜香可口。可选口味：猪肉白菜、韭菜鸡蛋、三鲜、牛肉。";
      } else if (id === 105) {
        mockData.title = "本地特色 | 正宗磁州烤肉套餐";
        mockData.groupPrice = 99;
        mockData.originalPrice = 168;
        mockData.regularPrice = 138;
        mockData.soldCount = 36;
        mockData.shopName = "老街烤肉";
        mockData.description = "传统炭火烤制，选用本地散养黑猪肉，搭配秘制调料，肉质鲜嫩多汁。套餐包含：烤肉拼盘、特色小菜4份、米饭2份。";
      }
      return mockData;
    },
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    },
    // 导航到商家页面
    goToShop() {
      utils_navigation.smartNavigate(`/pages/business/shop-detail?id=${this.groupbuy.id}`);
    },
    // 切换选项卡
    switchTab(index) {
      this.currentTab = index;
    },
    // 联系客服
    contactService() {
      common_vendor.index.showToast({
        title: "正在连接客服...",
        icon: "none"
      });
    },
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      common_vendor.index.showToast({
        title: this.isFavorite ? "收藏成功" : "已取消收藏",
        icon: "success"
      });
    },
    // 切换关注商家状态
    toggleFollow() {
      if (this.shop) {
        this.shop.isFollowed = !this.shop.isFollowed;
        common_vendor.index.showToast({
          title: this.shop.isFollowed ? "已关注" : "已取消关注",
          icon: "success"
        });
      }
    },
    // 单独购买
    buyNow() {
      this.currentBuyType = "normal";
      this.showBuyPopup = true;
    },
    // 团购购买
    groupBuy() {
      this.currentBuyType = "group";
      this.showBuyPopup = true;
    },
    // 关闭购买弹窗
    closeBuyPopup() {
      this.showBuyPopup = false;
    },
    // 减少购买数量
    decreaseQuantity() {
      if (this.buyQuantity > 1) {
        this.buyQuantity--;
      }
    },
    // 增加购买数量
    increaseQuantity() {
      if (this.buyQuantity < this.groupbuy.stock) {
        this.buyQuantity++;
      } else {
        common_vendor.index.showToast({
          title: "已达到最大库存",
          icon: "none"
        });
      }
    },
    // 确认购买
    confirmBuy() {
      const price = this.currentBuyType === "group" ? this.groupbuy.groupPrice : this.groupbuy.originalPrice;
      const totalPrice = (parseFloat(price) * this.buyQuantity).toFixed(2);
      common_vendor.index.showModal({
        title: "确认购买",
        content: `您将以¥${totalPrice}购买${this.buyQuantity}件商品，是否确认？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "正在下单"
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              this.showBuyPopup = false;
              utils_navigation.smartNavigate(`/pages/pay/index?amount=${totalPrice}&title=${encodeURIComponent(this.groupbuy.title)}`);
            }, 800);
          }
        }
      });
    },
    // 轮播图切换
    onSwiperChange(e) {
      this.currentSwiperIndex = e.detail.current;
    },
    // 分享功能
    share() {
      this.showSharePopup = true;
    },
    // 关闭分享弹窗
    closeSharePopup() {
      this.showSharePopup = false;
    },
    // 切换评价标签
    switchReviewTag(index) {
      this.activeReviewTag = index;
    },
    // 点赞评价
    likeReview(index) {
      common_vendor.index.showToast({
        title: "点赞成功",
        icon: "success"
      });
    },
    // 回复评价
    replyReview(index) {
      common_vendor.index.showToast({
        title: "暂不支持回复",
        icon: "none"
      });
    },
    // 查看更多评价
    viewMoreReviews() {
      common_vendor.index.showToast({
        title: "查看更多评价",
        icon: "none"
      });
    },
    // 领取优惠券
    receiveCoupon() {
      common_vendor.index.showToast({
        title: "优惠券领取成功",
        icon: "success"
      });
    },
    // 切换拼团/直购模式（仅用于演示）
    toggleGroupBuyMode() {
      this.groupbuy.enableGroupBuy = !this.groupbuy.enableGroupBuy;
      common_vendor.index.showToast({
        title: this.groupbuy.enableGroupBuy ? "已切换到拼团模式" : "已切换到直购模式",
        icon: "none"
      });
    },
    // 格式化价格
    formatPrice(price) {
    },
    // 生成分享海报
    generatePoster() {
      common_vendor.index.showLoading({
        title: "生成中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "海报已生成",
          icon: "success"
        });
        this.closeSharePopup();
      }, 1500);
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 查看推荐商品
    viewRecommendProduct(productId) {
      utils_navigation.smartNavigate(`/subPackages/activity-showcase/pages/detail/index?id=${productId}`);
    },
    // 查看所有适用门店
    viewAllStores() {
      common_vendor.index.showToast({
        title: "查看全部门店",
        icon: "none"
      });
    },
    // 查看门店详情
    viewStoreDetail(storeId) {
      utils_navigation.smartNavigate(`/pages/business/store-detail?id=${storeId}`);
    },
    // 查看更多拼团
    viewMoreGroups() {
      utils_navigation.smartNavigate("/subPackages/activity-showcase/pages/group-buy/index");
    }
  }
};
if (!Array) {
  const _component_distribution_section = common_vendor.resolveComponent("distribution-section");
  _component_distribution_section();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.t($options.activityTypeTitle),
    d: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    e: common_vendor.f($data.groupbuy.images, (item, index, i0) => {
      return {
        a: item,
        b: index
      };
    }),
    f: common_vendor.o((...args) => $options.onSwiperChange && $options.onSwiperChange(...args)),
    g: $data.navbarHeight + "px",
    h: common_vendor.t($options.formattedGroupPrice),
    i: common_vendor.t($options.discountPercent),
    j: common_vendor.t($options.formattedMarketPrice),
    k: common_vendor.t($options.formattedRegularPrice),
    l: common_vendor.t($options.saveAmount),
    m: common_vendor.t($data.groupbuy.title),
    n: common_vendor.t($data.groupbuy.soldCount),
    o: common_vendor.t($data.groupbuy.viewCount || 1200),
    p: common_vendor.t($data.countdown.days),
    q: common_vendor.t($data.countdown.hours),
    r: common_vendor.t($data.countdown.minutes),
    s: common_vendor.t($data.countdown.seconds),
    t: common_vendor.p({
      itemId: $data.id,
      itemType: "group",
      itemTitle: $data.groupbuy.title,
      itemPrice: $data.groupbuy.groupPrice,
      commissionRate: $data.groupbuy.commissionRate || 20
    }),
    v: common_vendor.o((...args) => $options.viewAllStores && $options.viewAllStores(...args)),
    w: common_vendor.f($data.storeList, (store, index, i0) => {
      return {
        a: store.logo,
        b: common_vendor.t(store.name),
        c: common_vendor.t(store.address),
        d: common_vendor.t(store.distance),
        e: index,
        f: common_vendor.o(($event) => $options.viewStoreDetail(store.id), index)
      };
    }),
    x: common_vendor.o((...args) => $options.viewMoreGroups && $options.viewMoreGroups(...args)),
    y: common_vendor.f($data.otherGroups, (item, index, i0) => {
      return {
        a: common_vendor.t(item.soldCount),
        b: item.image,
        c: common_vendor.t(item.shopName || "磁州同城折扣店"),
        d: common_vendor.t(item.distance || "1km"),
        e: common_vendor.t(item.title),
        f: common_vendor.t(item.groupPrice),
        g: common_vendor.t(item.marketPrice),
        h: common_vendor.t(item.discount || "6.2折热销中"),
        i: common_vendor.t(item.soldTag || "新品"),
        j: common_vendor.t(item.soldCount),
        k: common_vendor.t(item.usageInfo || "到店使用"),
        l: index,
        m: common_vendor.o(($event) => _ctx.navigateToDetail(item.id), index)
      };
    }),
    z: common_assets._imports_1$54,
    A: common_vendor.t($data.groupbuy.minGroupSize || 3),
    B: common_vendor.t($data.groupbuy.soldCount),
    C: common_vendor.t($data.groupbuy.enableGroupBuy ? "切换到直购模式" : "切换到拼团模式"),
    D: common_vendor.o((...args) => $options.toggleGroupBuyMode && $options.toggleGroupBuyMode(...args)),
    E: common_assets._imports_2$24,
    F: common_vendor.f(5, (i, k0, i0) => {
      return {
        a: `/static/demo/avatar${i}.png`,
        b: i
      };
    }),
    G: common_vendor.t($data.groupbuy.soldCount - 5 > 0 ? $data.groupbuy.soldCount - 5 : 0),
    H: $options.progressWidth,
    I: common_vendor.t($data.groupbuy.soldCount),
    J: common_vendor.t($data.groupbuy.targetCount),
    K: common_assets._imports_3$39,
    L: common_assets._imports_4$29,
    M: common_assets._imports_5$26,
    N: common_vendor.o((...args) => $options.receiveCoupon && $options.receiveCoupon(...args)),
    O: $data.groupbuy.shopLogo,
    P: common_vendor.t($data.groupbuy.shopName),
    Q: common_vendor.f(5, (i, k0, i0) => {
      return {
        a: i,
        b: i <= Math.floor($data.groupbuy.shopRating) ? "/static/images/tabbar/星星-选中.png" : "/static/images/tabbar/星星-未选.png"
      };
    }),
    R: common_vendor.t($data.groupbuy.shopRating),
    S: common_vendor.t($data.groupbuy.ratingCount || 238),
    T: common_vendor.o((...args) => $options.goToShop && $options.goToShop(...args)),
    U: common_vendor.f($data.tabs, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.name),
        b: $data.currentTab === index
      }, $data.currentTab === index ? {} : {}, {
        c: index,
        d: $data.currentTab === index ? 1 : "",
        e: common_vendor.o(($event) => $options.switchTab(index), index)
      });
    }),
    V: $data.currentTab === 0
  }, $data.currentTab === 0 ? {
    W: common_vendor.t($data.groupbuy.description),
    X: common_vendor.f($data.groupbuy.packageItems, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.quantity),
        c: common_vendor.t(item.unit),
        d: common_vendor.t(item.desc),
        e: index
      };
    }),
    Y: common_vendor.f($data.groupbuy.detailImages, (img, index, i0) => {
      return {
        a: index,
        b: img
      };
    })
  } : {}, {
    Z: $data.currentTab === 1
  }, $data.currentTab === 1 ? {
    aa: common_vendor.f($data.groupbuy.rules, (rule, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(rule),
        c: index
      };
    })
  } : {}, {
    ab: $data.currentTab === 2
  }, $data.currentTab === 2 ? common_vendor.e({
    ac: common_vendor.t($data.groupbuy.reviews ? $data.groupbuy.reviews.length : 0),
    ad: common_vendor.f($data.reviewTags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index,
        c: $data.activeReviewTag === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchReviewTag(index), index)
      };
    }),
    ae: common_vendor.f($data.groupbuy.reviews, (review, index, i0) => {
      return common_vendor.e({
        a: review.avatar,
        b: common_vendor.t(review.username),
        c: common_vendor.f(review.rating, (i, k1, i1) => {
          return {
            a: i
          };
        }),
        d: common_vendor.f(5 - review.rating, (i, k1, i1) => {
          return {
            a: i + 5
          };
        }),
        e: common_vendor.t(review.time),
        f: common_vendor.t(review.content),
        g: review.images && review.images.length > 0
      }, review.images && review.images.length > 0 ? {
        h: common_vendor.f(review.images, (img, imgIndex, i1) => {
          return {
            a: imgIndex,
            b: img
          };
        })
      } : {}, {
        i: common_vendor.o(($event) => $options.likeReview(index), index),
        j: common_vendor.o(($event) => $options.replyReview(index), index),
        k: index
      });
    }),
    af: common_assets._imports_6$22,
    ag: common_assets._imports_7$13,
    ah: !$data.groupbuy.reviews || $data.groupbuy.reviews.length === 0
  }, !$data.groupbuy.reviews || $data.groupbuy.reviews.length === 0 ? {} : {}, {
    ai: $data.groupbuy.reviews && $data.groupbuy.reviews.length > 0
  }, $data.groupbuy.reviews && $data.groupbuy.reviews.length > 0 ? {
    aj: common_vendor.o((...args) => $options.viewMoreReviews && $options.viewMoreReviews(...args))
  } : {}) : {}, {
    ak: common_vendor.f($data.recommendProducts, (item, k0, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.price),
        d: item.id,
        e: common_vendor.o(($event) => $options.viewRecommendProduct(item.id), item.id)
      };
    }),
    al: common_assets._imports_1$55,
    am: common_vendor.o((...args) => $options.contactService && $options.contactService(...args)),
    an: common_assets._imports_9$12,
    ao: common_vendor.o((...args) => $options.goToShop && $options.goToShop(...args)),
    ap: $data.isFavorite ? "/static/images/tabbar/收藏-选中.png" : "/static/images/tabbar/收藏.png",
    aq: common_vendor.o((...args) => $options.toggleFavorite && $options.toggleFavorite(...args)),
    ar: common_assets._imports_2$1,
    as: common_vendor.o((...args) => $options.share && $options.share(...args)),
    at: $data.groupbuy.enableGroupBuy
  }, $data.groupbuy.enableGroupBuy ? {
    av: common_vendor.t($options.formattedRegularPrice),
    aw: common_vendor.o((...args) => $options.buyNow && $options.buyNow(...args)),
    ax: common_vendor.t($options.formattedGroupPrice),
    ay: common_vendor.t($options.formattedMarketPrice),
    az: common_vendor.t($data.groupbuy.minGroupSize || 3),
    aA: common_vendor.t($options.saveAmount),
    aB: common_vendor.o((...args) => $options.groupBuy && $options.groupBuy(...args))
  } : {
    aC: common_vendor.t($options.formattedGroupPrice),
    aD: common_vendor.t($options.formattedMarketPrice),
    aE: common_vendor.t($options.saveAmount),
    aF: common_vendor.o((...args) => $options.buyNow && $options.buyNow(...args))
  }, {
    aG: $data.showBuyPopup
  }, $data.showBuyPopup ? {
    aH: common_vendor.o((...args) => $options.closeBuyPopup && $options.closeBuyPopup(...args)),
    aI: $data.groupbuy.images[0],
    aJ: common_vendor.t($data.currentBuyType === "group" ? $options.formattedGroupPrice : $options.formattedRegularPrice),
    aK: common_vendor.t($data.groupbuy.stock),
    aL: common_assets._imports_11$2,
    aM: common_vendor.o((...args) => $options.closeBuyPopup && $options.closeBuyPopup(...args)),
    aN: common_vendor.o((...args) => $options.decreaseQuantity && $options.decreaseQuantity(...args)),
    aO: $data.buyQuantity,
    aP: common_vendor.o(($event) => $data.buyQuantity = $event.detail.value),
    aQ: common_vendor.o((...args) => $options.increaseQuantity && $options.increaseQuantity(...args)),
    aR: common_vendor.o((...args) => $options.confirmBuy && $options.confirmBuy(...args))
  } : {}, {
    aS: $data.showSharePopup
  }, $data.showSharePopup ? {
    aT: common_vendor.o((...args) => $options.closeSharePopup && $options.closeSharePopup(...args)),
    aU: common_assets._imports_11$2,
    aV: common_vendor.o((...args) => $options.closeSharePopup && $options.closeSharePopup(...args)),
    aW: common_assets._imports_12$7,
    aX: common_vendor.o((...args) => $options.share && $options.share(...args)),
    aY: common_assets._imports_13$5,
    aZ: common_vendor.o((...args) => $options.share && $options.share(...args)),
    ba: common_assets._imports_14$7,
    bb: common_vendor.o((...args) => $options.share && $options.share(...args)),
    bc: common_assets._imports_15$6,
    bd: common_vendor.o((...args) => $options.share && $options.share(...args)),
    be: common_vendor.o((...args) => $options.generatePoster && $options.generatePoster(...args))
  } : {}));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1470128c"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/detail/index.js.map
