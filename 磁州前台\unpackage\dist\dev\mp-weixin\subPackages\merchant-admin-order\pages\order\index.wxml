<view class="order-management-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">订单管理</text><view class="navbar-right"></view></view><view class="view-selector"><view wx:for="{{b}}" wx:for-item="item" wx:key="c" class="{{['view-option', item.d && 'active']}}" bindtap="{{item.e}}"><view class="{{['view-icon-container', item.a]}}"></view><text class="view-name">{{item.b}}</text></view></view><view class="filter-section"><scroll-view scroll-x="true" class="filter-scroll"><view wx:for="{{c}}" wx:for-item="status" wx:key="b" class="{{['status-tag', status.c]}}" bindtap="{{status.d}}">{{status.a}}</view></scroll-view></view><view class="content-main"><block wx:if="{{d}}"><view wx:if="{{e}}" class="order-list"><navigator wx:for="{{f}}" wx:for-item="order" wx:key="i" url="{{order.j}}" class="order-item"><view class="order-header"><text class="order-number">订单号: {{order.a}}</text><view class="{{['order-status-tag', order.c]}}">{{order.b}}</view></view><view class="order-info"><view class="customer-info"><text class="label">客户:</text><text class="value">{{order.d}}</text></view><view class="time-info"><text class="label">下单时间:</text><text class="value">{{order.e}}</text></view><view class="amount-info"><text class="label">订单金额:</text><text class="value price">¥{{order.f}}</text></view></view><view class="order-actions"><view class="action-btn primary" catchtap="{{order.g}}">处理订单</view><view class="action-btn" catchtap="{{order.h}}">联系客户</view></view></navigator></view><view wx:elif="{{g}}" class="order-detail-view"><text>详情视图正在开发中...</text><button class="nav-btn" bindtap="{{h}}">查看订单详情示例</button></view><view wx:elif="{{i}}" class="order-calendar-view"><text>日历视图正在开发中...</text><button class="nav-btn" bindtap="{{j}}">查看日历视图示例</button></view></block><view wx:else class="empty-state"><view class="empty-icon-container"><view class="empty-icon">📭</view></view><text class="empty-text">暂无订单数据</text><view class="refresh-btn" bindtap="{{k}}">刷新</view></view></view><view class="tab-bar"><view wx:for="{{l}}" wx:for-item="item" wx:key="d" class="{{['tab-item', item.e && 'active']}}" bindtap="{{item.f}}"><view wx:if="{{item.a}}" class="active-indicator"></view><view class="{{['tab-icon', item.b]}}"></view><text class="tab-text">{{item.c}}</text></view></view></view>