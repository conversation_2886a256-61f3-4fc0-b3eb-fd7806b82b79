"use strict";
const common_vendor = require("../common/vendor.js");
let configCache = null;
let lastUpdateTime = 0;
const CACHE_DURATION = 5 * 60 * 1e3;
const defaultConfig = {
  buttons: {
    publish: {
      ad: {
        enabled: true,
        title: "看广告发布",
        description: "看一个广告免费发布一天",
        carpoolDescription: "看一个广告发布一条信息",
        icon: "/static/images/premium/ad-publish.png",
        tag: "免费"
      },
      paid: {
        enabled: true,
        title: "付费发布",
        description: "3天/1周/1个月任选",
        carpoolDescription: "付费1元发布一条信息",
        icon: "/static/images/premium/paid-publish.png",
        tag: "付费"
      }
    },
    top: {
      ad: {
        enabled: true,
        title: "广告置顶",
        description: "观看30秒广告获得2小时置顶",
        duration: "2小时",
        icon: "/static/images/premium/ad-top.png",
        tag: "免费"
      },
      paid: {
        enabled: true,
        title: "付费置顶",
        description: "3天/1周/1个月任选",
        icon: "/static/images/premium/paid-top.png",
        tag: "付费"
      }
    },
    refresh: {
      ad: {
        enabled: true,
        title: "广告刷新",
        description: "观看15秒广告刷新一次",
        icon: "/static/images/premium/ad-refresh.png",
        tag: "免费"
      },
      paid: {
        enabled: true,
        title: "付费刷新",
        description: "5次/10次/20次任选",
        icon: "/static/images/premium/paid-refresh.png",
        tag: "付费"
      }
    },
    join: {
      ad: {
        enabled: true,
        title: "看广告入驻",
        description: "观看30秒广告获得1个月免费特权",
        icon: "/static/images/premium/ad-join.png",
        tag: "免费"
      },
      paid: {
        enabled: true,
        title: "付费入驻",
        description: "1个月/3个月/1年任选",
        icon: "/static/images/premium/paid-join.png",
        tag: "付费"
      }
    },
    renew: {
      ad: {
        enabled: true,
        title: "看广告续费",
        description: "观看30秒广告延长7天会员",
        icon: "/static/images/premium/ad-renew.png",
        tag: "免费"
      },
      paid: {
        enabled: true,
        title: "付费续费",
        description: "1个月/3个月/6个月任选",
        icon: "/static/images/premium/paid-renew.png",
        tag: "付费"
      }
    }
  },
  pricing: {
    publish: [
      { duration: "3天", price: "2.8", recommended: false },
      { duration: "1周", price: "5.8", recommended: true },
      { duration: "1个月", price: "19.8", recommended: false }
    ],
    top: [
      { duration: "3天", price: "2.8", recommended: false },
      { duration: "1周", price: "5.8", recommended: true },
      { duration: "1个月", price: "19.8", recommended: false }
    ],
    refresh: [
      { duration: "5次", price: "1.8", recommended: false },
      { duration: "10次", price: "2.8", recommended: true },
      { duration: "20次", price: "4.8", recommended: false }
    ],
    join: [
      { duration: "1个月", price: "99", recommended: false },
      { duration: "3个月", price: "199", recommended: true },
      { duration: "1年", price: "599", recommended: false }
    ],
    renew: [
      { duration: "1个月", price: "99", recommended: false },
      { duration: "3个月", price: "199", recommended: true },
      { duration: "6个月", price: "359", recommended: false }
    ]
  },
  toggles: {
    global: {
      enabled: true,
      defaultShowMode: "direct"
    },
    pages: {
      publish: { enabled: true, adEnabled: true, paidEnabled: true, carpoolSpecial: true, priority: "equal" },
      top: { enabled: true, adEnabled: true, paidEnabled: true, supportedTypes: ["merchant_top", "carpool_top", "publish_top"] },
      refresh: { enabled: true, adEnabled: true, paidEnabled: true },
      join: { enabled: true, adEnabled: true, paidEnabled: true },
      renew: { enabled: true, adEnabled: true, paidEnabled: true }
    },
    advanced: {
      debugMode: false,
      logging: true,
      cacheConfig: true,
      updateInterval: 300
    }
  },
  adReward: {
    adUnitId: "adunit-xxxxxxxxx",
    rewardRules: {
      publish: { type: "duration", value: 1, dailyLimit: 5 },
      top: { type: "duration", value: 2, dailyLimit: 3 },
      refresh: { type: "count", value: 1, dailyLimit: 10 },
      join: { type: "duration", value: 30, dailyLimit: 1 },
      renew: { type: "duration", value: 7, dailyLimit: 1 }
    }
  }
};
const fetchConfigFromBackend = async () => {
  try {
    const response = await common_vendor.index.request({
      url: "https://your-backend-api.com/api/promotion/complete-config",
      method: "GET",
      timeout: 5e3
    });
    if (response.statusCode === 200 && response.data) {
      return response.data;
    } else {
      throw new Error("获取配置失败");
    }
  } catch (error) {
    common_vendor.index.__f__("warn", "at utils/promotion-config.js:170", "从后台获取推广配置失败，使用默认配置:", error);
    return defaultConfig;
  }
};
const getPromotionConfig = async (forceRefresh = false) => {
  const now = Date.now();
  if (!forceRefresh && configCache && now - lastUpdateTime < CACHE_DURATION) {
    return configCache;
  }
  try {
    const config = await fetchConfigFromBackend();
    configCache = config;
    lastUpdateTime = now;
    try {
      common_vendor.index.setStorageSync("promotion_config", config);
      common_vendor.index.setStorageSync("promotion_config_time", now);
    } catch (e) {
      common_vendor.index.__f__("warn", "at utils/promotion-config.js:197", "保存配置到本地存储失败:", e);
    }
    return config;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/promotion-config.js:202", "获取推广配置失败:", error);
    try {
      const localConfig = common_vendor.index.getStorageSync("promotion_config");
      const localTime = common_vendor.index.getStorageSync("promotion_config_time");
      if (localConfig && localTime && now - localTime < 24 * 60 * 60 * 1e3) {
        configCache = localConfig;
        return localConfig;
      }
    } catch (e) {
      common_vendor.index.__f__("warn", "at utils/promotion-config.js:214", "从本地存储获取配置失败:", e);
    }
    return defaultConfig;
  }
};
const getPricingConfig = async (action) => {
  const config = await getPromotionConfig();
  let configKey = "publish";
  if (action === "top") {
    configKey = "top";
  } else if (action === "refresh") {
    configKey = "refresh";
  } else if (action === "join") {
    configKey = "join";
  } else if (action === "renew") {
    configKey = "renew";
  }
  return config.pricing[configKey] || [];
};
const isFeatureEnabled = async (pageType, optionType = null) => {
  const config = await getPromotionConfig();
  if (!config.toggles.global.enabled) {
    return false;
  }
  let configKey = "publish";
  if (pageType.includes("top")) {
    configKey = "top";
  } else if (pageType.includes("refresh")) {
    configKey = "refresh";
  } else if (pageType.includes("join")) {
    configKey = "join";
  } else if (pageType.includes("renew")) {
    configKey = "renew";
  }
  const pageToggle = config.toggles.pages[configKey];
  if (!pageToggle || !pageToggle.enabled) {
    return false;
  }
  if (optionType === "ad") {
    return pageToggle.adEnabled;
  }
  if (optionType === "paid") {
    return pageToggle.paidEnabled;
  }
  return true;
};
const logUserAction = async (action, data) => {
  const config = await getPromotionConfig();
  if (!config.toggles.advanced.logging) {
    return;
  }
  try {
    common_vendor.index.request({
      url: "https://your-backend-api.com/api/promotion/log",
      method: "POST",
      data: {
        action,
        data,
        timestamp: Date.now(),
        userAgent: navigator.userAgent || "unknown"
      }
    });
  } catch (error) {
    common_vendor.index.__f__("warn", "at utils/promotion-config.js:360", "发送操作日志失败:", error);
  }
};
exports.getPricingConfig = getPricingConfig;
exports.getPromotionConfig = getPromotionConfig;
exports.isFeatureEnabled = isFeatureEnabled;
exports.logUserAction = logUserAction;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/promotion-config.js.map
