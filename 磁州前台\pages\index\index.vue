<template>
	<view class="index-container">
		<!-- 新增的自定义导航栏 -->
		<view class="custom-nav-bar" :style="{ height: navBarFullHeight + 'px' }">
			<view class="nav-status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="nav-bar-content" :style="{ height: navBarHeight + 'px' }">
				<text class="nav-bar-title">磁州生活网</text>
			</view>
		</view>
		
		<!-- 可滚动的弧形背景 -->
		<view class="content-bg-scroll"></view>
		
		<!-- 页面内容区域 -->
		<view class="scrollable-content" :style="{ paddingTop: navBarFullHeight + 'px' }">
			<!-- 引导关注公众号模块 -->
			<view class="follow-guide-in-bg" v-if="showFollowTip">
				<view class="follow-guide-content">
					<image class="follow-avatar" src="/static/images/cizhou.png" mode="aspectFit"></image>
					<text class="follow-text">已有10万人关注我们，期待你的加入</text>
					<view class="follow-btn-wrap">
						<button class="follow-btn" @click="showQrCode = true">立即关注</button>
					</view>
					<view class="follow-close" @click="closeFollowTip">×</view>
				</view>
			</view>
			
			<!-- 轮播图 -->
			<view class="banner-outer">
				<swiper class="banner-swiper" 
					indicator-dots 
					indicator-color="rgba(255,255,255,0.5)" 
					indicator-active-color="#ffffff"
					autoplay 
					circular 
					interval="3000" 
					duration="500">
					<swiper-item v-for="(item, index) in bannerList" :key="index">
						<view class="banner-item">
							<image :src="item.image" class="banner-image" mode="scaleToFill" @click="navigateTo(item.url)"></image>
						</view>
					</swiper-item>
				</swiper>
			</view>
			
			<!-- 白色内容区域，包含所有剩余内容 -->
			<view class="white-content">
			<!-- 服务分类网格 -->
			<ServiceGrid :serviceList="serviceList" />
				
				<!-- 四宫格特色功能区 -->
				<FeatureGrid />
				
				<!-- 同城资讯 -->
				<CityNews />
			
			<!-- 商家推荐 -->
			<MerchantRecommend />
			
				<!-- 搜索框 -->
				<view class="search-container">
					<view class="search-box">
						<image src="/static/images/icons/search.png" class="search-icon"></image>
						<input type="text" class="search-input" placeholder="搜索信息" placeholder-style="color:#b8bdcc;" @input="onSearchInput" @confirm="doSearch" confirm-type="search"/>
						<view class="search-button" @click="doSearch"><text>搜索</text></view>
					</view>
				</view>
			
			<!-- 磁县同城信息 -->
			<InfoList ref="infoList" :allInfoList="allInfoList" :toppedInfoList="toppedInfoList" :adBanner="adBanner" @tab-change="handleTabChange" />
			</view>
		</view>
		
		<!-- 二维码弹出层 -->
		<view class="qrcode-popup" v-if="showQrCode" @click.stop="closeQrCode">
			<view class="qrcode-card" @click.stop>
				<view class="qrcode-header">
					<text class="qrcode-title">关注公众号</text>
					<view class="close-btn" @click="closeQrCode">×</view>
				</view>
				<view class="qrcode-content">
					<image src="/static/images/tabbar/wxacode.jpg" class="qrcode-image" mode="aspectFit"></image>
					<text class="qrcode-tips">扫描二维码，关注公众号</text>
					<text class="qrcode-desc">获取最新城市资讯、优惠活动</text>
				</view>
			</view>
		</view>
			
			<!-- 到家服务全屏子分类弹出层 -->
			<view class="service-popup" v-if="showServicePopup">
				<view class="service-popup-mask" @click="closeServicePopup"></view>
				<view class="service-popup-content">
					<view class="service-popup-header">
						<text class="service-popup-title">{{ currentServiceCategory.name }}</text>
						<view class="service-popup-close" @click="closeServicePopup">
							<text class="uni-icon-close"></text>
						</view>
					</view>
					<scroll-view class="service-popup-scroll" scroll-y="true">
						<view class="service-popup-grid">
							<view 
								class="service-popup-item" 
								v-for="(item, index) in currentServiceCategory.subItems" 
								:key="index"
								@click="navigateToSubService(item)">
								<image :src="item.icon" class="service-popup-icon" mode="aspectFit"></image>
								<text class="service-popup-name">{{ item.name }}</text>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
			
			<!-- 位置选择弹窗 -->
			<view class="location-popup" v-if="showLocationPicker">
				<view class="location-popup-mask" @click="closeLocationPicker"></view>
				<view class="location-popup-content">
					<view class="location-popup-header">
						<text class="location-popup-title">选择位置</text>
						<view class="location-popup-close" @click="closeLocationPicker">
							<text class="uni-icon-close"></text>
						</view>
					</view>
					<view class="location-list">
						<view 
							class="location-item" 
							v-for="(item, index) in locationList" 
							:key="index"
							@click="selectLocation(item)"
							:class="{'active': locationName === item.name}">
							<text class="location-item-name">{{ item.name }}</text>
							<text class="uni-icon-checkmarkempty" v-if="locationName === item.name"></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { onLoad, onShow, onReady, onPageScroll } from '@dcloudio/uni-app';
import ServiceGrid from '@/components/index/ServiceGrid.vue';
import MerchantRecommend from '@/components/index/MerchantRecommend.vue';
import InfoList from '@/components/index/InfoList.vue';
import FeatureGrid from '@/components/index/FeatureGrid.vue';
import CityNews from '@/components/index/CityNews.vue';
// 引入真实API替换Mock数据
import api from '@/api/index.js';
import { homeApi } from '@/api/homeApi.js';

// -- 自定义导航栏所需变量 --
const statusBarHeight = ref(0);
const navBarHeight = ref(0);
const navBarFullHeight = ref(0);
const navTitleWidth = ref(0);
// -- 自定义导航栏所需变量结束 --

// 滚动节流阀
const scrollTimer = ref(null);
			
			// 引导关注相关
const showFollowTip = ref(true);
const showQrCode = ref(false);
			
			// 位置信息
const locationName = ref('磁县');
const showLocationPicker = ref(false);
const locationList = ref([
				{ name: '磁县', id: 'cx' },
				{ name: '邯郸', id: 'hd' },
				{ name: '峰峰', id: 'ff' },
				{ name: '武安', id: 'wa' }
]);
			
			// 未读消息数
const unreadMessageCount = ref(5);
			
			// 轮播图数据 - 从后台管理系统获取
const bannerList = ref([]);
			
			// 服务分类数据 - 从后台管理系统获取
const serviceList = ref([]);
			
			// 服务分类弹窗
const showServicePopup = ref(false);
const currentServiceCategory = ref({
				name: '',
				subItems: []
});
			
			// 同城信息标签
const infoTabs = ref([
				{ id: 'all', name: '全部' },
				{ id: 'rent', name: '房屋租售' },
				{ id: 'job', name: '招聘求职' },
				{ id: 'service', name: '生活服务' },
				{ id: 'second', name: '二手交易' },
				{ id: 'car', name: '车辆信息' },
				{ id: 'other', name: '其他信息' }
]);
			
			// 磁县同城信息分类
const infoCategories = ref([
				'到家服务',
				'寻找服务',
				'生意转让',
				'招聘信息',
				'求职信息',
				'房屋出租',
				'房屋出售',
				'二手车辆',
				'宠物信息',
				'商家活动',
				'婚恋交友',
				'车辆服务',
				'二手闲置',
				'磁州拼车',
				'教育培训',
				'其他服务'
]);
			
			// 同城信息数据 - 普通信息
const allInfoList = ref([
				{ id: 'business-transfer-restaurant-1', category: '生意转让', content: '【红包】黄金地段餐饮店整体转让，地处商业中心，设备齐全可直接营业！', time: '2024-05-16 08:30', views: 186, pageType: 'business-transfer-detail', hasRedPacket: true, redPacketAmount: '15.88', redPacketType: 'fixed', redPacketCount: 30, redPacketRemain: 18 },
				{ id: 'business-transfer-no-red-1', category: '生意转让', content: '县城中心奶茶店转让，客流稳定，接手即可盈利，因个人原因急转', time: '2024-05-16 10:15', views: 135, pageType: 'business-transfer-detail', hasRedPacket: false },
				{ id: 'red-packet-service-1', category: '到家服务', content: '【红包】专业家庭保洁服务，首单立减20元，预约送10元现金红包！', time: '2024-05-15 09:30', views: 235, pageType: 'home-service-detail', hasRedPacket: true, redPacketAmount: '10.00', redPacketType: 'fixed', redPacketCount: 50, redPacketRemain: 32 },
				{ id: 'service-no-red-1', category: '到家服务', content: '专业上门维修空调、冰箱、洗衣机等家电，技术精湛，价格公道', time: '2024-05-15 11:30', views: 176, pageType: 'home-service-detail', hasRedPacket: false },
				{ id: 'red-packet-business-1', category: '生意转让', content: '【红包】黄金地段餐饮店整体转让，接手即可盈利，老板定制18.8元红包！', time: '2024-05-14 16:20', views: 328, pageType: 'business-transfer-detail', hasRedPacket: true, redPacketAmount: '18.88', redPacketType: 'fixed', redPacketCount: 100, redPacketRemain: 45 },
				{ id: 'red-packet-job-1', category: '招聘信息', content: '【红包】招聘销售经理5名，底薪5000+提成，咨询简历投递送随机红包！', time: '2024-05-14 15:15', views: 456, pageType: 'job-detail', hasRedPacket: true, redPacketAmount: '66.66', redPacketType: 'random', redPacketCount: 30, redPacketRemain: 12 },
				{ id: 'job-no-red-1', category: '招聘信息', content: '急招会计1名，五险一金，双休，2年以上工作经验，薪资4500-6000', time: '2024-05-15 14:30', views: 208, pageType: 'job-detail', hasRedPacket: false },
				{ id: 'job-seeking-no-red-1', category: '求职信息', content: '计算机专业应届毕业生求职，熟悉前端开发，有项目经验，可立即上岗', time: '2024-05-15 16:45', views: 125, pageType: 'job-seeking-detail', hasRedPacket: false },
				{ id: 'red-packet-house-1', category: '房屋出租', content: '【红包】市中心精装两室一厅出租，家电齐全，看房送15元现金红包！', time: '2024-05-13 14:30', views: 289, pageType: 'house-rent-detail', hasRedPacket: true, redPacketAmount: '15.00', redPacketType: 'fixed', redPacketCount: 20, redPacketRemain: 8 },
				{ id: 'house-rent-no-red-1', category: '房屋出租', content: '学区房两室一厅出租，家电家具齐全，拎包入住，交通便利', time: '2024-05-14 10:20', views: 197, pageType: 'house-rent-detail', hasRedPacket: false },
				{ id: 'house-sale-no-red-1', category: '房屋出售', content: '县城南区新房，三室两厅，120平米，毛坯房，采光好，有车位', time: '2024-05-14 09:15', views: 268, pageType: 'house-sale-detail', hasRedPacket: false },
				{ id: 'red-packet-car-1', category: '二手车辆', content: '【红包】2023款本田雅阁2.0L，准新车，行驶5000公里，查询车况送红包！', time: '2024-05-12 13:25', views: 376, pageType: 'car-detail', hasRedPacket: true, redPacketAmount: '20.00', redPacketType: 'fixed', redPacketCount: 25, redPacketRemain: 10 },
				{ id: 'car-no-red-1', category: '二手车辆', content: '2020款大众朗逸，1.5L自动挡，行驶3万公里，无事故，一手车', time: '2024-05-14 13:40', views: 189, pageType: 'car-detail', hasRedPacket: false },
				{ id: 'pet-no-red-1', category: '宠物信息', content: '家养蓝猫幼崽出售，2个月大，已驱虫，疫苗已做，可上门看猫', time: '2024-05-14 15:20', views: 162, pageType: 'pet-detail', hasRedPacket: false },
				{ id: 'merchant-activity-no-red-1', category: '商家活动', content: '新开张火锅店满减活动，满100减30，满200减80，还有精美礼品赠送', time: '2024-05-14 11:25', views: 248, pageType: 'merchant-activity-detail', hasRedPacket: false },
				{ id: 'red-packet-vehicle-1', category: '车辆服务', content: '【红包】专业汽车美容贴膜，隐形车衣，预约试用送50元红包！', time: '2024-05-11 11:45', views: 198, pageType: 'vehicle-service-detail', hasRedPacket: true, redPacketAmount: '50.00', redPacketType: 'fixed', redPacketCount: 10, redPacketRemain: 5 },
				{ id: 'vehicle-service-no-red-1', category: '车辆服务', content: '专业汽车保养，机油三滤更换，四轮定位，价格实惠，技术可靠', time: '2024-05-14 16:30', views: 156, pageType: 'vehicle-service-detail', hasRedPacket: false },
				{ id: 'red-packet-second-hand-1', category: '二手闲置', content: '【红包】全新iPhone 15 Pro Max，黑色256G，抽奖送华为手环！', time: '2024-05-10 10:20', views: 468, pageType: 'second-hand-detail', hasRedPacket: true, redPacketAmount: '88.88', redPacketType: 'random', redPacketCount: 5, redPacketRemain: 2 },
				{ id: 'second-hand-no-red-1', category: '二手闲置', content: '9成新MacBook Pro 2022款，M1芯片，16G内存，512G硬盘，原价12999', time: '2024-05-14 17:10', views: 215, pageType: 'second-hand-detail', hasRedPacket: false },
				{ id: 'ride-share-no-red-1', category: '磁州拼车', content: '每天早上7点县城到邯郸拼车，轿车舒适，准时发车，长期有效', time: '2024-05-14 18:20', views: 183, pageType: 'carpool-detail', hasRedPacket: false },
				{ id: 'education-no-red-1', category: '教育培训', content: '小学初中高中各科辅导，一对一定制教学计划，提分效果明显', time: '2024-05-14 19:10', views: 134, pageType: 'education-detail', hasRedPacket: false },
				{ id: 'dating-red-1', category: '婚恋交友', content: '【红包】28岁女士，本科学历，身高165cm，温柔大方，期待遇见有缘人', time: '2024-05-16 09:15', views: 228, pageType: 'dating-detail', hasRedPacket: true, redPacketAmount: '18.88', redPacketType: 'fixed', redPacketCount: 20, redPacketRemain: 12 },
				{ id: 'dating-no-red-1', category: '婚恋交友', content: '32岁男士，身高178cm，事业稳定，性格开朗，寻找志同道合的另一半', time: '2024-05-15 14:20', views: 176, pageType: 'dating-detail', hasRedPacket: false },
				{ id: 'merchant-activity-red-1', category: '商家活动', content: '【红包】新开业烤肉店满减活动，满200减100，关注公众号送20元无门槛代金券', time: '2024-05-16 10:30', views: 315, pageType: 'merchant-activity-detail', hasRedPacket: true, redPacketAmount: '20.00', redPacketType: 'fixed', redPacketCount: 40, redPacketRemain: 25 },
				{ id: 'merchant-activity-no-red-2', category: '商家活动', content: '周年庆典大促，全场化妆品8折起，部分商品买一送一，活动时间5月20-25日', time: '2024-05-15 16:40', views: 198, pageType: 'merchant-activity-detail', hasRedPacket: false },
				{ id: 'vehicle-service-no-red-2', category: '车辆服务', content: '专业汽车保养，机油三滤更换，四轮定位，价格实惠，技术可靠', time: '2024-05-14 16:30', views: 156, pageType: 'vehicle-service-detail', hasRedPacket: false },
				{ id: 'carpool-1', category: '磁州拼车', content: '磁县→邯郸，每天早8点发车，舒适型轿车，可带行李，剩余3个座位', time: '2024-05-16 07:30', views: 187, pageType: 'carpool-detail', hasRedPacket: false },
				{ id: 'carpool-2', category: '磁州拼车', content: '邯郸→磁县，下午5点发车，每天固定班次，可提前预约，支持改签', time: '2024-05-16 08:15', views: 145, pageType: 'carpool-detail', hasRedPacket: false },
				{ id: 'carpool-red-1', category: '磁州拼车', content: '【红包】磁县→石家庄，周六早7点发车，舒适商务车，咨询送10元红包！', time: '2024-05-15 16:40', views: 223, pageType: 'carpool-detail', hasRedPacket: true, redPacketAmount: '10.00', redPacketType: 'fixed', redPacketCount: 20, redPacketRemain: 15 },
				{ id: 'carpool-3', category: '磁州拼车', content: '磁县→北京，周五晚8点发车，7座商务车，舒适安全，提供夜间服务', time: '2024-05-14 14:20', views: 198, pageType: 'carpool-detail', hasRedPacket: false }
]);
			
			// 置顶信息列表
const toppedInfoList = ref([
				{ 
					id: 'topped-job-1', 
					category: '招聘信息', 
					content: '急招会计1名，五险一金，双休，2年以上工作经验，薪资4500-6000', 
					time: '2024-05-15 14:30', 
					views: 208, 
					pageType: 'job-detail', 
					isTopped: true,
					topType: 'paid',
					topExpiry: '2024-05-20'
				},
				{ 
					id: 'topped-house-1', 
					category: '房屋出租', 
					content: '市中心精装两室一厅出租，家电齐全，拎包入住，交通便利，周边配套设施完善', 
					time: '2024-05-14 10:20', 
					views: 197, 
					pageType: 'house-rent-detail', 
					isTopped: true,
					topType: 'ad',
					topExpiry: '2024-05-22',
					images: ['/static/images/tabbar/wxacode.jpg', '/static/images/tabbar/wxacode.jpg']
				}
]);
			
			// 广告横幅
const adBanner = ref({
				image: '/static/images/ad-banner.jpg',
				url: '/pages/activity/detail?id=3'
});
			
			// 二维码弹窗
const qrCodeData = ref({
				title: '关注公众号',
				image: '/static/images/qrcode.jpg',
				tips: '扫码关注磁州生活',
				desc: '获取更多本地信息和优惠'
});
			
			// 搜索框相关
const searchKeyword = ref('');
			
			// 同城信息标签相关
const currentInfoTab = ref(0);
const isTabsFixed = ref(false);
const visibleCategories = ref([
				'到家服务', '寻找服务', '生意转让', '招聘信息', '求职信息',
				'房屋出租', '房屋出售', '二手车辆', '宠物信息', '商家活动',
				'婚恋交友', '车辆服务', '二手闲置', '磁州拼车', '教育培训', '其他服务'
]);

// 信息数据
const infoData = ref({
  allInfoList: [],
  toppedInfoList: []
});

// 更新现有的广告横幅数据
adBanner.value = {
  image: '/static/images/banner/ad-banner.jpg',
  url: '/pages/ad/detail',
  title: '广告横幅'
};

// 生命周期钩子
onMounted(() => {
  // 加载数据
  loadHomeData(); // 从后台管理系统加载首页数据
  loadInfoData();
  loadNewsData();
  loadBusinessData();
});

// 从后台管理系统加载首页数据
async function loadHomeData() {
  try {
    console.log('开始从后台管理系统加载首页数据...');
    
    // 并行加载所有首页数据
    const [banners, services, config, stats, features, merchants, news] = await Promise.all([
      homeApi.getBanners(),
      homeApi.getServiceCategories(),
      homeApi.getHomeConfig(),
      homeApi.getHomeStats(),
      homeApi.getFeatureConfig(),
      homeApi.getMerchantRecommend(),
      homeApi.getCityNews()
    ]);
    
    // 更新轮播图数据
    if (banners && banners.length > 0) {
      bannerList.value = banners.map(banner => ({
        id: banner.id,
        image: banner.image,
        url: banner.url || '',
        title: banner.title || ''
      }));
      console.log('✅ 轮播图数据已更新:', bannerList.value.length, '张');
    }

    // 更新服务分类数据
    if (services && services.length > 0) {
      serviceList.value = services.map(service => ({
        icon: service.icon || '/static/images/service/default.png',
        name: service.name,
        url: service.url || `/pages/service/${service.id}`
      }));
      console.log('✅ 服务分类数据已更新:', serviceList.value.length, '个');
    }

    // 更新特色功能数据
    if (features && features.length > 0) {
      console.log('✅ 特色功能数据已获取:', features.length, '个功能');
      // 这里可以更新四宫格功能数据
    }

    // 更新商家推荐数据
    if (merchants && merchants.length > 0) {
      console.log('✅ 商家推荐数据已获取:', merchants.length, '个商家');
      // 这里可以更新商家推荐组件数据
    }

    // 更新同城资讯数据
    if (news && news.length > 0) {
      console.log('✅ 同城资讯数据已获取:', news.length, '条资讯');
      // 这里可以更新同城资讯组件数据
    }

    // 更新首页配置
    if (config && config.site_title) {
      uni.setNavigationBarTitle({
        title: config.site_title
      });
      console.log('✅ 页面标题已更新:', config.site_title);
    }
    
    // 记录页面访问统计
    homeApi.recordPageView();
    
    console.log('🎉 后台管理系统数据加载完成!');
    console.log('📊 数据统计:', {
      banners: bannerList.value.length,
      services: serviceList.value.length,
      features: features?.length || 0,
      merchants: merchants?.length || 0,
      news: news?.length || 0,
      config: config?.site_title || '未配置',
      stats: stats?.total_views || 0
    });
    
  } catch (error) {
    console.error('加载首页数据失败:', error);
    
    // 加载失败时使用默认数据
    bannerList.value = [
      { id: 1, image: '/static/images/banner/banner-1.png', url: '', title: '磁州生活网' },
      { id: 2, image: '/static/images/banner/banner-2.png', url: '', title: '本地生活服务' }
    ];
    
    serviceList.value = [
      { icon: '/static/images/service/food.png', name: '美食外卖', url: '/pages/service/food' },
      { icon: '/static/images/service/market.png', name: '超市便利', url: '/pages/service/market' },
      { icon: '/static/images/service/medicine.png', name: '医药健康', url: '/pages/service/medicine' },
      { icon: '/static/images/service/fresh.png', name: '生鲜果蔬', url: '/pages/service/fresh' },
      { icon: '/static/images/service/flower.png', name: '鲜花绿植', url: '/pages/service/flower' },
      { icon: '/static/images/service/clean.png', name: '家政保洁', url: '/pages/service/clean' },
      { icon: '/static/images/service/repair.png', name: '维修服务', url: '/pages/service/repair' },
      { icon: '/static/images/service/more.png', name: '更多服务', url: '/pages/service/more' }
    ];
  }
}

// 加载新闻数据
async function loadNewsData() {
  try {
    const result = await api.news.getList({ page: 1, limit: 10 });
    if (result.success) {
      // 更新新闻数据（如果有新闻组件的话）
      console.log('新闻数据加载成功:', result.data);
    }
  } catch (error) {
    console.error('加载新闻数据失败:', error);
  }
}

// 加载商家数据
async function loadBusinessData() {
  try {
    const result = await api.business.getList({ page: 1, limit: 10 });
    if (result.success) {
      // 更新商家数据（如果有商家组件的话）
      console.log('商家数据加载成功:', result.data);
    }
  } catch (error) {
    console.error('加载商家数据失败:', error);
  }
}

// 加载信息数据 - 使用真实API替换Mock数据
async function loadInfoData() {
  try {
    // 显示加载状态
    uni.showLoading({
      title: '加载中...'
    });

    // 并行加载置顶信息和普通信息
    const [toppedResult, allResult] = await Promise.all([
      api.info.getTopped({ page: 1, limit: 10 }),
      api.info.getAll({ page: 1, limit: 20 })
    ]);

    // 处理置顶信息数据
    if (toppedResult.success) {
      toppedInfoList.value = toppedResult.data.map(item => ({
        ...item,
        images: item.images || [],
        tags: item.tags || [],
        views: item.views || 0,
        likes: item.likes || 0,
        comments: item.comments || 0
      }));
    } else {
      console.error('获取置顶信息失败:', toppedResult.message);
    }

    // 处理普通信息数据
    if (allResult.success) {
      allInfoList.value = allResult.data.map(item => ({
        ...item,
        images: item.images || [],
        tags: item.tags || [],
        views: item.views || 0,
        likes: item.likes || 0,
        comments: item.comments || 0
      }));
    } else {
      console.error('获取信息列表失败:', allResult.message);
    }

  } catch (error) {
    console.error('加载信息数据失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });

    // 加载失败时使用空数据
    toppedInfoList.value = [];
    allInfoList.value = [];
  } finally {
    uni.hideLoading();
  }
}

// 处理标签切换事件
function handleTabChange(tabInfo) {
  console.log('标签切换:', tabInfo);
  // 可以根据标签切换加载不同类型的数据
  // 这里使用的是示例数据，实际应用中可能需要从服务器获取数据
}

// 组件引用
const infoList = ref(null);

		// 检查用户位置
function checkUserLocation() {
			// 这里可以获取用户位置，暂时使用默认值
			console.log('检查用户位置');
}
		
		// 加载轮播图数据
function loadBannerData() {
			// 这里可以从API获取数据，暂时使用默认值
			console.log('加载轮播图数据');
}
		
		// 加载服务分类数据
function loadServiceData() {
			// 这里可以从API获取数据，暂时使用默认值
			console.log('加载服务分类数据');
}
		
		// 加载商家推荐数据
function loadMerchantData() {
			// 这里可以从API获取数据，暂时使用默认值
			console.log('加载商家推荐数据');
}
		
		// 关闭关注提示
function closeFollowTip() {
	showFollowTip.value = false;
			// 可以存储状态，避免重复显示
			uni.setStorageSync('hideFollowTip', true);
}
		
		// 打开二维码
function openQrCode() {
	showQrCode.value = true;
}
		
		// 关闭二维码
function closeQrCode() {
	showQrCode.value = false;
}
		
		// 导航到指定页面
function navigateTo(url) {
			if (!url) return;
			
			uni.navigateTo({
				url: url,
				fail: (err) => {
					console.error('页面跳转失败:', err);
					// 尝试使用switchTab
					uni.switchTab({
						url: url,
						fail: (err2) => {
							console.error('switchTab也失败:', err2);
							uni.showToast({
								title: '页面跳转失败',
								icon: 'none'
							});
						}
					});
				}
			});
}
		
		// 打开位置选择器
function openLocationPicker() {
	showLocationPicker.value = true;
}
		
		// 关闭位置选择器
function closeLocationPicker() {
	showLocationPicker.value = false;
}
		
		// 选择位置
function selectLocation(item) {
	locationName.value = item.name;
	closeLocationPicker();
			
			// 根据选择的位置重新加载数据
	loadBannerData();
	loadServiceData();
	loadMerchantData();
	loadInfoData();
}
		
		// 打开服务分类弹窗
function openServicePopup(category) {
	currentServiceCategory.value = category;
	showServicePopup.value = true;
}
		
		// 关闭服务分类弹窗
function closeServicePopup() {
	showServicePopup.value = false;
}
		
		// 跳转到子服务页面
function navigateToSubService(item) {
			if (!item.url) return;
			
			uni.navigateTo({
				url: item.url,
				fail: () => {
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			});
			
	closeServicePopup();
}
		
		// 处理信息标签切换
function handleInfoTabChange(tab) {
			console.log('切换到标签:', tab.name);
	currentInfoTab.value = tab.index;
			// 根据选中的标签过滤信息数据
}
		
		// 搜索框相关方法
function onSearchInput(e) {
	searchKeyword.value = e.detail.value;
}
		
		// 执行搜索
function doSearch() {
	if (!searchKeyword.value.trim()) {
				uni.showToast({
					title: '请输入搜索关键词',
					icon: 'none'
				});
				return;
			}
			
			// 执行搜索逻辑
	performSimpleSearch();
}
		
		// 简单搜索功能
function performSimpleSearch() {
			// 显示加载中
			uni.showLoading({
				title: '搜索中...'
			});
			
			// 从现有信息列表中搜索
			const searchResults = [];
			
			// 遍历置顶信息
toppedInfoList.value.forEach(item => {
		if (item.content && item.content.includes(searchKeyword.value)) {
					searchResults.push(item);
				}
			});
			
			// 遍历普通信息
	allInfoList.value.forEach(item => {
		if (item.content && item.content.includes(searchKeyword.value)) {
					searchResults.push(item);
				}
			});
			
			// 隐藏加载
			setTimeout(() => {
				uni.hideLoading();
				
				// 显示搜索结果
				if (searchResults.length > 0) {
					uni.showModal({
						title: '搜索结果',
						content: `找到 ${searchResults.length} 条相关信息，点击确定查看详情`,
						success: (res) => {
							if (res.confirm && searchResults.length > 0) {
								// 查看第一条搜索结果详情
						navigateToInfoDetail(searchResults[0]);
							}
						}
					});
				} else {
					uni.showToast({
						title: '未找到相关信息',
						icon: 'none'
					});
				}
			}, 500);
}
		
		// 导航到信息详情页
function navigateToInfoDetail(item) {
			uni.navigateTo({
				url: `/pages/info/detail?id=${item.id}`,
				fail: (err) => {
					console.error('导航到信息详情页失败:', err);
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			});
}
		
		// 处理页面滚动，实现InfoList的吸顶效果
function handlePageScroll(scrollTop) {
			// 确保InfoList组件已经挂载
	if (infoList.value) {
		const query = uni.createSelectorQuery();
				// 使用 'all-info-title-row' 作为参照物，因为它位置稳定
				query.select('.scrollable-content >>> .all-info-title-row').boundingClientRect(data => {
					if (!data) {
						return;
					}
					
					const navBarHeight = 44; // 自定义导航栏高度
					// 减去1px以消除可能的缝隙
			const fixedHeaderHeight = statusBarHeight.value + navBarHeight - 1; 
					
					// 计算出参照物（标题栏）的底部位置
					const titleRowBottom = data.top + data.height;
					
					// 当参照物（标题栏）的底部滚动到导航栏下方时，触发吸顶
					if (titleRowBottom <= fixedHeaderHeight) {
				infoList.value.setSticky(true, fixedHeaderHeight);
					} else {
						// 否则，取消吸顶
				infoList.value.setSticky(false, 0);
					}
				}).exec();
			}
		}

onLoad(() => {
	// #ifdef MP-WEIXIN
	const systemInfo = uni.getSystemInfoSync();
	const menuButtonInfo = uni.getMenuButtonBoundingClientRect();

	statusBarHeight.value = systemInfo.statusBarHeight || 20;
	navBarHeight.value = (menuButtonInfo.top - statusBarHeight.value) * 2 + menuButtonInfo.height + 3;
	navBarFullHeight.value = statusBarHeight.value + navBarHeight.value;
	navTitleWidth.value = menuButtonInfo.left;
	// #endif

	// #ifndef MP-WEIXIN
	statusBarHeight.value = uni.getSystemInfoSync().statusBarHeight || 20;
	navBarHeight.value = 45 + 3;
	navBarFullHeight.value = statusBarHeight.value + navBarHeight.value;
	navTitleWidth.value = uni.getSystemInfoSync().windowWidth - 100; // 模拟一个宽度
	// #endif

	checkUserLocation();
	loadBannerData();
	loadServiceData();
	loadMerchantData();
	loadInfoData();
});

onShow(() => {
	const currentWindowInfo = uni.getWindowInfo();
	statusBarHeight.value = currentWindowInfo.statusBarHeight || 20;
});

onReady(() => {
	setTimeout(() => {
		const currentWindowInfo = uni.getWindowInfo();
		statusBarHeight.value = currentWindowInfo.statusBarHeight || 20;
	}, 50);
});

onPageScroll((e) => {
	if (scrollTimer.value) {
		return;
	}
	scrollTimer.value = setTimeout(() => {
		handlePageScroll(e.scrollTop);
		scrollTimer.value = null;
	}, 100);
});
</script>

<style lang="scss">
.index-container {
	position: relative;
	background-color: #F5F6FA;
	min-height: 100vh;
}

/* 导航栏图标样式 */
.navbar-icon {
	width: 44rpx;
	height: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(255, 255, 255, 0.1);
	border-radius: 50%;
	transition: all 0.3s;
}

.navbar-icon-hover {
	background-color: rgba(255, 255, 255, 0.2);
	transform: scale(0.95);
}

.icon-image {
	width: 28rpx;
	height: 28rpx;
	filter: brightness(0) invert(1);
}

/* 可滚动的弧形背景 */
.content-bg-scroll {
	position: absolute;
	top: 15rpx; /* 向下移动15rpx */
	left: 0;
	right: 0;
	height: 400rpx; /* 保持原始高度不变 */
	background-color: #1677FF;
	border-bottom-left-radius: 80rpx;
	border-bottom-right-radius: 80rpx;
	z-index: 1;
}

/* 页面内容区域 */
.scrollable-content {
	position: relative;
	z-index: 2;
	box-sizing: border-box;
	padding-top: 20rpx; /* 添加顶部内边距，与导航栏保持一定距离 */
}

/* 引导关注模块样式 */
.follow-guide-in-bg {
	position: relative;
	padding: 0 60rpx;
	margin: 15rpx 0 30rpx 0; /* 将顶部边距调整为15rpx，从原始10rpx增加5rpx */
	z-index: 10;
	width: 100%;
	box-sizing: border-box;
	display: flex;
	justify-content: center;
}

.follow-guide-content {
	background-color: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(15px);
	-webkit-backdrop-filter: blur(15px);
	border-radius: 25rpx;
	padding: 16rpx 26rpx 16rpx 75rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 12rpx 25rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.08), inset 0 0 0 1px rgba(255, 255, 255, 0.8);
	width: 100%;
	box-sizing: border-box;
	max-width: 650rpx;
	position: relative;
	/* overflow: hidden; */
}

.follow-guide-content::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(22, 119, 255, 0.05), rgba(0, 82, 204, 0.02));
	z-index: 0;
}

.follow-avatar {
	width: 55rpx;
	height: 55rpx;
	border-radius: 50%;
	box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.4), 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
	object-fit: contain;
	position: absolute;
	left: 10rpx;
	top: 50%;
	transform: translateY(-50%);
	border: 3rpx solid #fff;
	z-index: 5;
	background: #fff;
	padding: 0;
}

@keyframes gentle-pulse {
	0% { transform: translateY(-50%) scale(1); -webkit-transform: translateY(-50%) scale(1); }
	50% { transform: translateY(-50%) scale(1.03); -webkit-transform: translateY(-50%) scale(1.03); }
	100% { transform: translateY(-50%) scale(1); -webkit-transform: translateY(-50%) scale(1); }
}

.follow-guide-content:hover .follow-avatar {
	box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.45), 0 5rpx 10rpx rgba(0, 0, 0, 0.25);
	animation: none;
	transform: translateY(-50%) scale(1.06);
	-webkit-transform: translateY(-50%) scale(1.06);
}

.follow-text {
	flex: 1;
	font-size: 20rpx;
	color: #333;
	line-height: 1.3;
	font-weight: 500;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding-right: 4rpx;
	margin-left: 8rpx;
	position: relative;
	z-index: 1;
}

.follow-btn-wrap {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	position: relative;
	z-index: 1;
}

.follow-btn {
	background: linear-gradient(145deg, #394FC2, #4A67D7);
	color: #ffffff;
	font-size: 22rpx;
	padding: 0 22rpx;
	height: 54rpx;
	line-height: 54rpx;
	border-radius: 27rpx;
	margin: 0;
	position: relative;
	z-index: 2;
	box-shadow: 5rpx 5rpx 10rpx rgba(61, 86, 193, 0.3),
	            -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.3),
	            inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);
	font-weight: 600;
	border: none;
	transition: all 0.2s ease;
	text-align: center;
}

.follow-btn:active {
	transform: scale(0.96);
	box-shadow: 3rpx 3rpx 6rpx rgba(61, 86, 193, 0.3),
	            -1rpx -1rpx 3rpx rgba(255, 255, 255, 0.3),
	            inset 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.follow-close {
	position: absolute;
	top: -5rpx;
	right: -5rpx;
	width: 26rpx;
	height: 26rpx;
	background-color: rgba(0, 0, 0, 0.3);
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 18rpx;
	z-index: 10;
}

/* 轮播图 */
.banner-outer {
	position: relative;
	margin: 25rpx 30rpx 50rpx; /* 将顶部边距调整为25rpx，从原始20rpx增加5rpx */
	border-radius: 30rpx;
	overflow: hidden;
	box-shadow: 0 25rpx 45rpx rgba(0, 0, 0, 0.35), 0 15rpx 25rpx rgba(0, 0, 0, 0.2);
	border: 12rpx solid #ffffff;
	transform: translateZ(0);
	z-index: 3;
	animation: float 6s ease-in-out infinite;
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	padding: 2rpx;
	background-color: rgba(255, 255, 255, 0.8);
}

@keyframes float {
	0% { transform: translateY(0) translateZ(0); }
	50% { transform: translateY(-10rpx) translateZ(0); }
	100% { transform: translateY(0) translateZ(0); }
}

.banner-swiper {
	width: 100%;
	height: 300rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.banner-item {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #FFFFFF;
}

.banner-image {
	width: 100%;
	height: 100%;
	border-radius: 16rpx;
	box-shadow: inset 0 0 15rpx rgba(0, 0, 0, 0.15);
	transition: transform 0.3s ease;
}

/* 白色内容区域 */
.white-content {
	background: #f8f9fc;
	position: relative;
	padding-top: 40rpx;
	padding-bottom: 20rpx;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;
	box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.03);
	margin-top: -40rpx;
	z-index: 4;
}

/* 搜索框样式 */
.search-container {
	padding: 10rpx 30rpx 35rpx;
}

.search-box {
	display: flex;
	align-items: center;
	background-color: #ffffff;
	border-radius: 40rpx;
	padding: 0 20rpx 0 30rpx;
	height: 88rpx;
	border: 1rpx solid #eaedf2;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.search-icon {
	width: 36rpx;
	height: 36rpx;
	margin-right: 16rpx;
	opacity: 0.4;
}

.search-input {
	flex: 1;
	height: 88rpx;
	font-size: 28rpx;
	color: #333;
}

.search-placeholder {
	color: #b8bdcc;
	font-size: 28rpx;
}

.search-button {
	width: auto;
	min-width: 90rpx;
	height: 60rpx;
	line-height: 60rpx;
	background: #3A86FF;
	color: #fff;
	font-size: 28rpx;
	font-weight: 500;
	border-radius: 30rpx;
	padding: 0 20rpx;
	margin-right: 10rpx;
	letter-spacing: 1rpx;
	text-align: center;
}

/* 二维码弹窗 */
.qrcode-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.qrcode-card {
	width: 560rpx;
	background: #FFFFFF;
	border-radius: 20rpx;
	overflow: hidden;
}

.qrcode-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 20rpx 20rpx;
	border-bottom: 1px solid #F5F5F5;
}

.qrcode-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.close-btn {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999;
	font-size: 40rpx;
}

.qrcode-content {
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.qrcode-image {
	width: 320rpx;
	height: 320rpx;
	margin-bottom: 20rpx;
}

.qrcode-tips {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.qrcode-desc {
	font-size: 24rpx;
	color: #999;
}

/* 服务分类弹窗 */
.service-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	display: flex;
	flex-direction: column;
}

.service-popup-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
}

.service-popup-content {
	position: relative;
	background-color: #FFFFFF;
	border-radius: 30rpx 30rpx 0 0;
	padding: 30rpx;
	margin-top: auto;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
	animation: slideUp 0.3s ease;
}

@keyframes slideUp {
	from { transform: translateY(100%); }
	to { transform: translateY(0); }
}

.service-popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.service-popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.service-popup-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999;
	font-size: 40rpx;
}

.service-popup-scroll {
	flex: 1;
	max-height: 60vh;
}

.service-popup-grid {
	display: flex;
	flex-wrap: wrap;
	padding: 10rpx 0;
}

.service-popup-item {
	width: 25%;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 0;
}

.service-popup-icon {
	width: 80rpx;
	height: 80rpx;
	margin-bottom: 10rpx;
}

.service-popup-name {
	font-size: 24rpx;
	color: #333;
	text-align: center;
}

/* 位置选择弹窗 */
.location-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	display: flex;
	flex-direction: column;
}

.location-popup-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
}

.location-popup-content {
	position: relative;
	background-color: #FFFFFF;
	border-radius: 30rpx 30rpx 0 0;
	padding: 30rpx;
	margin-top: auto;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
	animation: slideUp 0.3s ease;
}

.location-popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.location-popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.location-popup-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999;
	font-size: 40rpx;
}

.location-list {
	max-height: 60vh;
	overflow-y: auto;
}

.location-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #F2F2F7;
}

.location-item:last-child {
	border-bottom: none;
}

.location-item.active {
	color: #1677FF;
}

.location-item-name {
	font-size: 30rpx;
}

/* 统一字体样式 */
text, input, button {
	font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 去除所有按钮的默认边框 */
button {
	border: none;
	outline: none;
	background-color: transparent;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

button::after {
	border: none;
	outline: none;
}

/* -- 自定义导航栏样式 -- */
.custom-nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background-color: #1677FF;
	z-index: 999;
	box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.2);
	display: flex;
	flex-direction: column;
}

.nav-status-bar {
	width: 100%;
}

.nav-bar-content {
	width: 100%;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-bar-title {
	color: #FFFFFF;
	font-size: 34rpx;
	font-weight: 600;
	text-align: center;
	margin-right: 24px; /* 右侧留出一定空间，平衡胶囊按钮 */
}
/* -- 自定义导航栏样式结束 -- */
</style>


