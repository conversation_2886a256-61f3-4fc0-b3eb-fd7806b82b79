{"version": 3, "file": "create-template.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/create-template.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcY3JlYXRlLXRlbXBsYXRlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">红包模板</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 模板列表 -->\n    <view class=\"template-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">我的模板</text>\n        <view class=\"add-btn\" @click=\"createTemplate\">\n          <text class=\"btn-text\">新建模板</text>\n          <view class=\"plus-icon-small\"></view>\n        </view>\n      </view>\n      \n      <view class=\"template-list\">\n        <view class=\"template-item\" v-for=\"(item, index) in myTemplates\" :key=\"index\" @click=\"viewTemplateDetail(item)\">\n          <view class=\"template-preview\" :style=\"{ background: item.color }\">\n            <view class=\"template-icon\">\n              <image class=\"icon-image\" :src=\"item.icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"template-name\">{{item.name}}</text>\n          </view>\n          <view class=\"template-info\">\n            <text class=\"template-desc\">{{item.description}}</text>\n            <view class=\"template-type\">{{item.typeText}}</view>\n          </view>\n          <view class=\"template-actions\">\n            <view class=\"action-btn\" @click.stop=\"useTemplate(item)\">使用</view>\n            <view class=\"action-btn edit\" @click.stop=\"editTemplate(item)\">编辑</view>\n            <view class=\"action-btn delete\" @click.stop=\"deleteTemplate(item)\">删除</view>\n          </view>\n        </view>\n        \n        <view class=\"empty-state\" v-if=\"myTemplates.length === 0\">\n          <view class=\"empty-icon\"></view>\n          <text class=\"empty-text\">暂无自定义模板</text>\n          <view class=\"empty-action\" @click=\"createTemplate\">新建模板</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 官方模板 -->\n    <view class=\"official-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">官方模板</text>\n      </view>\n      \n      <view class=\"template-grid\">\n        <view class=\"template-card\" v-for=\"(item, index) in officialTemplates\" :key=\"index\" @click=\"useTemplate(item)\">\n          <view class=\"template-preview\" :style=\"{ background: item.color }\">\n            <view class=\"template-icon\">\n              <image class=\"icon-image\" :src=\"item.icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"template-name\">{{item.name}}</text>\n          </view>\n          <view class=\"template-footer\">\n            <text class=\"template-desc\">{{item.description}}</text>\n            <view class=\"template-use-btn\">使用</view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 场景模板 -->\n    <view class=\"scenario-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">场景模板</text>\n      </view>\n      \n      <scroll-view scroll-x class=\"scenario-scroll\" show-scrollbar=\"false\">\n        <view class=\"scenario-container\">\n          <view class=\"scenario-card\" v-for=\"(item, index) in scenarioTemplates\" :key=\"index\" @click=\"useTemplate(item)\">\n            <view class=\"scenario-preview\" :style=\"{ background: item.color }\">\n              <view class=\"scenario-icon\">\n                <image class=\"icon-image\" :src=\"item.icon\" mode=\"aspectFit\"></image>\n              </view>\n              <text class=\"scenario-name\">{{item.name}}</text>\n              <view class=\"scenario-tag\">{{item.tagText}}</view>\n            </view>\n            <view class=\"scenario-footer\">\n              <text class=\"scenario-desc\">{{item.description}}</text>\n              <view class=\"scenario-use-btn\">使用</view>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 模板使用指南 -->\n    <view class=\"guide-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">模板使用指南</text>\n      </view>\n      \n      <view class=\"guide-steps\">\n        <view class=\"guide-step\" v-for=\"(step, index) in guideSteps\" :key=\"index\">\n          <view class=\"step-number\">{{index + 1}}</view>\n          <view class=\"step-content\">\n            <text class=\"step-title\">{{step.title}}</text>\n            <text class=\"step-desc\">{{step.description}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 浮动操作按钮 -->\n    <view class=\"floating-action-button\" @click=\"createTemplate\">\n      <view class=\"fab-icon\">+</view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      // 我的模板\n      myTemplates: [\n        {\n          id: 1,\n          name: '会员专享红包',\n          description: '针对会员用户的专属优惠',\n          icon: '/static/images/redpacket/template-icon-1.png',\n          color: '#FF5858',\n          type: 'normal',\n          typeText: '普通红包'\n        },\n        {\n          id: 2,\n          name: '节日特惠红包',\n          description: '节假日促销活动专用',\n          icon: '/static/images/redpacket/template-icon-2.png',\n          color: '#4ECDC4',\n          type: 'fission',\n          typeText: '裂变红包'\n        }\n      ],\n      \n      // 官方模板\n      officialTemplates: [\n        {\n          id: 101,\n          name: '新用户欢迎',\n          description: '吸引新用户注册和首次消费',\n          icon: '/static/images/redpacket/official-icon-1.png',\n          color: '#FF5858'\n        },\n        {\n          id: 102,\n          name: '老用户回馈',\n          description: '提高老用户复购率和忠诚度',\n          icon: '/static/images/redpacket/official-icon-2.png',\n          color: '#4ECDC4'\n        },\n        {\n          id: 103,\n          name: '生日特权',\n          description: '会员生日专属优惠和祝福',\n          icon: '/static/images/redpacket/official-icon-3.png',\n          color: '#FFD166'\n        },\n        {\n          id: 104,\n          name: '满额立减',\n          description: '刺激用户提高客单价',\n          icon: '/static/images/redpacket/official-icon-4.png',\n          color: '#6A0572'\n        }\n      ],\n      \n      // 场景模板\n      scenarioTemplates: [\n        {\n          id: 201,\n          name: '春节红包',\n          description: '新春佳节送福利',\n          icon: '/static/images/redpacket/scenario-icon-1.png',\n          color: '#FF5858',\n          tag: 'hot',\n          tagText: '热门'\n        },\n        {\n          id: 202,\n          name: '店庆活动',\n          description: '周年庆典专属优惠',\n          icon: '/static/images/redpacket/scenario-icon-2.png',\n          color: '#4ECDC4',\n          tag: 'new',\n          tagText: '新品'\n        },\n        {\n          id: 203,\n          name: '会员日特惠',\n          description: '每月会员专属福利',\n          icon: '/static/images/redpacket/scenario-icon-3.png',\n          color: '#FFD166',\n          tag: '',\n          tagText: ''\n        },\n        {\n          id: 204,\n          name: '限时秒杀',\n          description: '限时限量抢购活动',\n          icon: '/static/images/redpacket/scenario-icon-4.png',\n          color: '#6A0572',\n          tag: '',\n          tagText: ''\n        }\n      ],\n      \n      // 使用指南\n      guideSteps: [\n        {\n          title: '选择模板',\n          description: '从我的模板、官方模板或场景模板中选择适合的模板'\n        },\n        {\n          title: '自定义设置',\n          description: '根据实际需求修改红包金额、使用规则等参数'\n        },\n        {\n          title: '保存并发布',\n          description: '确认设置无误后保存并发布红包活动'\n        },\n        {\n          title: '分享推广',\n          description: '通过多种渠道分享红包活动，提高曝光度'\n        }\n      ]\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showHelp() {\n      uni.showModal({\n        title: '红包模板帮助',\n        content: '红包模板可以帮助您快速创建红包活动，节省设置时间。您可以使用官方模板，也可以创建和保存自己的模板。',\n        showCancel: false\n      });\n    },\n    createTemplate() {\n      uni.showToast({\n        title: '创建模板功能开发中',\n        icon: 'none'\n      });\n    },\n    viewTemplateDetail(item) {\n      uni.showToast({\n        title: '查看详情功能开发中',\n        icon: 'none'\n      });\n    },\n    useTemplate(item) {\n      uni.showModal({\n        title: '使用模板',\n        content: `确定要使用\"${item.name}\"模板创建红包活动吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            uni.navigateTo({\n              url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create'\n            });\n          }\n        }\n      });\n    },\n    editTemplate(item) {\n      uni.showToast({\n        title: '编辑模板功能开发中',\n        icon: 'none'\n      });\n    },\n    deleteTemplate(item) {\n      uni.showModal({\n        title: '删除确认',\n        content: `确定要删除\"${item.name}\"模板吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            // 模拟删除\n            const index = this.myTemplates.findIndex(t => t.id === item.id);\n            if (index !== -1) {\n              this.myTemplates.splice(index, 1);\n              uni.showToast({\n                title: '删除成功',\n                icon: 'success'\n              });\n            }\n          }\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.page-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 通用样式 */\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.add-btn {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  padding: 6px 12px;\n  border-radius: 15px;\n  color: #fff;\n}\n\n.btn-text {\n  font-size: 12px;\n  margin-right: 5px;\n}\n\n.plus-icon-small {\n  width: 12px;\n  height: 12px;\n  position: relative;\n}\n\n.plus-icon-small::before,\n.plus-icon-small::after {\n  content: '';\n  position: absolute;\n  background-color: #fff;\n}\n\n.plus-icon-small::before {\n  width: 12px;\n  height: 2px;\n  top: 5px;\n  left: 0;\n}\n\n.plus-icon-small::after {\n  width: 2px;\n  height: 12px;\n  top: 0;\n  left: 5px;\n}\n\n/* 我的模板区域 */\n.template-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.template-list {\n  margin-top: 10px;\n}\n\n.template-item {\n  background-color: #fff;\n  border-radius: 10px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n  border: 1px solid #eee;\n  overflow: hidden;\n}\n\n.template-preview {\n  height: 100px;\n  padding: 15px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.template-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  background: rgba(255, 255, 255, 0.2);\n  margin-bottom: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.icon-image {\n  width: 24px;\n  height: 24px;\n}\n\n.template-name {\n  color: #fff;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.template-info {\n  padding: 12px 15px;\n  border-bottom: 1px solid #f5f5f5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.template-desc {\n  font-size: 14px;\n  color: #666;\n  flex: 1;\n}\n\n.template-type {\n  font-size: 12px;\n  color: #999;\n  background-color: #F5F7FA;\n  padding: 3px 8px;\n  border-radius: 10px;\n}\n\n.template-actions {\n  padding: 10px 15px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.action-btn {\n  margin-left: 10px;\n  padding: 5px 12px;\n  border-radius: 15px;\n  font-size: 12px;\n  background-color: #F5F7FA;\n  color: #666;\n}\n\n.action-btn.edit {\n  background-color: #E3F2FD;\n  color: #1976D2;\n}\n\n.action-btn.delete {\n  background-color: #FEE8E8;\n  color: #FF5858;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 30px 0;\n}\n\n.empty-icon {\n  width: 60px;\n  height: 60px;\n  background-color: #F5F7FA;\n  border-radius: 30px;\n  margin-bottom: 10px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 15px;\n}\n\n.empty-action {\n  padding: 8px 20px;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  color: #fff;\n  border-radius: 20px;\n  font-size: 14px;\n}\n\n/* 官方模板区域 */\n.official-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.template-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -5px;\n}\n\n.template-card {\n  width: calc(50% - 10px);\n  margin: 5px;\n  border-radius: 10px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.template-footer {\n  background-color: #fff;\n  padding: 10px;\n}\n\n.template-use-btn {\n  background-color: #F5F7FA;\n  color: #FF5858;\n  font-size: 12px;\n  padding: 5px 0;\n  border-radius: 15px;\n  text-align: center;\n  margin-top: 8px;\n}\n\n/* 场景模板区域 */\n.scenario-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.scenario-scroll {\n  margin-top: 15px;\n  white-space: nowrap;\n}\n\n.scenario-container {\n  display: inline-flex;\n  padding: 5px 0;\n}\n\n.scenario-card {\n  width: 160px;\n  margin-right: 15px;\n  border-radius: 10px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.scenario-preview {\n  height: 120px;\n  padding: 15px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.scenario-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  background: rgba(255, 255, 255, 0.2);\n  margin-bottom: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.scenario-name {\n  color: #fff;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.scenario-tag {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 10px;\n  background-color: #FF3B30;\n  color: #fff;\n}\n\n.scenario-footer {\n  background-color: #fff;\n  padding: 10px;\n}\n\n.scenario-desc {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 8px;\n  display: block;\n  white-space: normal;\n  height: 32px;\n  overflow: hidden;\n}\n\n.scenario-use-btn {\n  background-color: #F5F7FA;\n  color: #FF5858;\n  font-size: 12px;\n  padding: 5px 0;\n  border-radius: 15px;\n  text-align: center;\n}\n\n/* 指南样式 */\n.guide-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  margin-bottom: 80px;\n}\n\n.guide-steps {\n  margin-top: 15px;\n}\n\n.guide-step {\n  display: flex;\n  margin-bottom: 15px;\n}\n\n.guide-step:last-child {\n  margin-bottom: 0;\n}\n\n.step-number {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background-color: #FF5858;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  margin-right: 10px;\n  flex-shrink: 0;\n}\n\n.step-content {\n  flex: 1;\n}\n\n.step-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.step-desc {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 浮动操作按钮 */\n.floating-action-button {\n  position: fixed;\n  bottom: 30px;\n  right: 20px;\n  width: 56px;\n  height: 56px;\n  border-radius: 28px;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 3px 10px rgba(255, 88, 88, 0.3);\n  z-index: 100;\n}\n\n.fab-icon {\n  font-size: 28px;\n  color: #fff;\n  font-weight: 300;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create-template.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA0HA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ;AAAA,MACD;AAAA;AAAA,MAGD,mBAAmB;AAAA,QACjB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACD;AAAA;AAAA,MAGD,mBAAmB;AAAA,QACjB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,SAAS;AAAA,QACX;AAAA,MACD;AAAA;AAAA,MAGD,YAAY;AAAA,QACV;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,mBAAmB,MAAM;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,YAAY,MAAM;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,IAAI;AAAA,QAC3B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,YACP,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa,MAAM;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,eAAe,MAAM;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,IAAI;AAAA,QAC3B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,kBAAM,QAAQ,KAAK,YAAY,UAAU,OAAK,EAAE,OAAO,KAAK,EAAE;AAC9D,gBAAI,UAAU,IAAI;AAChB,mBAAK,YAAY,OAAO,OAAO,CAAC;AAChCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7SA,GAAG,WAAW,eAAe;"}