"use strict";
const common_vendor = require("../../../common/vendor.js");
const subPackages_merchantAdminMarketing_services_distributionService = require("../services/distributionService.js");
const distributionMixin = {
  data() {
    return {
      hasMerchantDistribution: false,
      // 商家是否开通分销功能
      distributionSettings: {
        enabled: false,
        commissionMode: "percentage",
        // 'percentage'百分比 或 'fixed'固定金额
        commissions: {
          level1: "",
          level2: "",
          level3: ""
        },
        enableLevel3: false
      }
    };
  },
  methods: {
    /**
     * 检查商家是否开通分销功能
     */
    async checkMerchantDistribution() {
      try {
        const merchantInfo = this.$store.state.merchant.merchantInfo || {};
        this.hasMerchantDistribution = await subPackages_merchantAdminMarketing_services_distributionService.distributionService.checkMerchantDistribution(merchantInfo);
        if (this.hasMerchantDistribution) {
          const settings = await subPackages_merchantAdminMarketing_services_distributionService.distributionService.getMerchantDistributionSettings(merchantInfo);
          this.distributionSettings = settings;
        }
        return this.hasMerchantDistribution;
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/mixins/distributionMixin.js:44", "检查分销功能失败", error);
        this.hasMerchantDistribution = false;
        return false;
      }
    },
    /**
     * 更新分销设置
     * @param {Object} settings - 分销设置
     */
    updateDistributionSettings(settings) {
      this.distributionSettings = settings;
    },
    /**
     * 保存活动分销设置
     * @param {string} activityType - 活动类型
     * @param {string} activityId - 活动ID
     * @returns {Promise<boolean>} 是否保存成功
     */
    async saveActivityDistributionSettings(activityType, activityId) {
      if (!this.hasMerchantDistribution || !this.distributionSettings.enabled) {
        return true;
      }
      try {
        const { valid, errors } = subPackages_merchantAdminMarketing_services_distributionService.distributionService.validateDistributionSettings(this.distributionSettings);
        if (!valid) {
          common_vendor.index.showToast({
            title: errors[0],
            icon: "none"
          });
          return false;
        }
        const success = await subPackages_merchantAdminMarketing_services_distributionService.distributionService.saveActivityDistributionSettings(
          activityType,
          activityId,
          this.distributionSettings
        );
        if (!success) {
          common_vendor.index.showToast({
            title: "保存分销设置失败",
            icon: "none"
          });
          return false;
        }
        return true;
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/mixins/distributionMixin.js:97", "保存分销设置失败", error);
        common_vendor.index.showToast({
          title: "保存分销设置失败",
          icon: "none"
        });
        return false;
      }
    }
  },
  mounted() {
    this.checkMerchantDistribution();
  }
};
exports.distributionMixin = distributionMixin;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/mixins/distributionMixin.js.map
