{"version": 3, "file": "locationService.js", "sources": ["utils/locationService.js"], "sourcesContent": ["/**\n * 位置服务管理\n */\nimport { checkLocationPermission, openSettings } from './permission.js';\nimport { getUserLocation, calculateDistance, formatDistance } from './location.js';\n\n// 存储当前位置信息\nlet currentLocation = null;\n\n// 默认位置（当用户拒绝位置权限或获取位置失败时使用）\nconst DEFAULT_LOCATION = {\n  latitude: 36.313076,\n  longitude: 114.347312,\n  province: '河北省',\n  city: '邯郸市',\n  district: '磁县',\n  address: '河北省邯郸市磁县',\n  location: '河北省 邯郸市 磁县'\n};\n\n// 初始化位置服务\nexport const initLocationService = async () => {\n  console.log('初始化位置服务');\n  \n  // 检查本地缓存是否有位置信息\n  const cachedLocation = uni.getStorageSync('user_location');\n  if (cachedLocation) {\n    currentLocation = cachedLocation;\n  } else {\n    // 如果没有缓存的位置信息，使用默认位置\n    currentLocation = DEFAULT_LOCATION;\n    uni.setStorageSync('user_location', DEFAULT_LOCATION);\n  }\n  \n  // 监听位置更新事件\n  uni.$on('location_updated', (updatedLocation) => {\n    if (updatedLocation && updatedLocation.latitude) {\n      currentLocation = updatedLocation;\n      uni.setStorageSync('user_location', updatedLocation);\n    }\n  });\n  \n  return currentLocation;\n};\n\n// 获取当前位置信息\n// @deprecated 已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代\nexport const getCurrentLocation = () => {\n  console.warn('getCurrentLocation 方法已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代');\n  \n  // 检查当前是否有位置信息\n  if (currentLocation) {\n    return currentLocation;\n  }\n  \n  // 检查本地缓存是否有位置信息\n  const cachedLocation = uni.getStorageSync('user_location');\n  if (cachedLocation) {\n    currentLocation = cachedLocation;\n    return cachedLocation;\n  }\n  \n  // 如果没有位置信息，返回默认位置\n  return DEFAULT_LOCATION;\n};\n\n// 刷新位置信息\n// @deprecated 已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代\nexport const refreshLocation = async () => {\n  console.warn('refreshLocation 方法已弃用，请使用 utils/location.js 中的 getUserLocation 方法替代');\n  \n  try {\n    // 显示加载中提示\n    uni.showToast({\n      title: '更新位置...',\n      icon: 'loading',\n      duration: 1000,\n      mask: false\n    });\n    \n    // 尝试获取真实位置\n    const permissionStatus = await checkLocationPermission();\n    \n    // 如果已授权位置权限\n    if (permissionStatus === 'authorized') {\n      try {\n        // 获取位置信息\n        const location = await getUserLocation({\n          type: 'gcj02',\n          timeout: 3000\n        });\n        \n        // 构建位置对象\n        const newLocation = {\n          latitude: location.latitude,\n          longitude: location.longitude,\n          // 理想情况下应通过地图API获取以下信息\n          // 这里简化处理，使用默认数据\n          province: DEFAULT_LOCATION.province,\n          city: DEFAULT_LOCATION.city,\n          district: DEFAULT_LOCATION.district,\n          address: DEFAULT_LOCATION.address,\n          location: DEFAULT_LOCATION.location\n        };\n        \n        // 更新当前位置\n        currentLocation = newLocation;\n        uni.setStorageSync('user_location', newLocation);\n        \n        // 发送位置更新事件\n        uni.$emit('location_updated', newLocation);\n        \n        uni.showToast({\n          title: '位置已更新',\n          icon: 'success',\n          duration: 1500\n        });\n        \n        return newLocation;\n      } catch (error) {\n        console.error('获取位置失败:', error);\n        \n        // 如果获取位置失败，使用之前的位置或默认位置\n        const fallbackLocation = currentLocation || DEFAULT_LOCATION;\n        \n        return fallbackLocation;\n      }\n    } else {\n      // 如果未授权，询问用户是否打开设置\n      uni.showModal({\n        title: '位置权限未开启',\n        content: '需要位置权限才能刷新位置，是否前往设置开启？',\n        confirmText: '去设置',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            // 打开设置页面\n            openSettings();\n          }\n        }\n      });\n      \n      // 返回当前位置或默认位置\n      return currentLocation || DEFAULT_LOCATION;\n    }\n  } catch (error) {\n    console.error('刷新位置信息失败:', error);\n    \n    uni.showToast({\n      title: '更新位置失败',\n      icon: 'none',\n      duration: 1500\n    });\n    \n    return currentLocation || DEFAULT_LOCATION;\n  }\n};\n\n// 计算与当前位置的距离\nexport const distanceFromCurrentLocation = (lat, lng) => {\n  const current = getCurrentLocation();\n  if (!current || !current.latitude || !current.longitude) {\n    return '未知距离';\n  }\n  \n  const distance = calculateDistance(\n    current.latitude,\n    current.longitude,\n    lat,\n    lng\n  );\n  \n  return formatDistance(distance);\n};\n\n// 导出其他位置相关函数\nexport { calculateDistance, formatDistance }; "], "names": ["uni", "checkLocationPermission", "getUserLocation", "openSettings"], "mappings": ";;;;AAOA,IAAI,kBAAkB;AAGtB,MAAM,mBAAmB;AAAA,EACvB,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AACZ;AA6BY,MAAC,qBAAqB,MAAM;AACtCA,gBAAAA,MAAA,MAAA,QAAA,kCAAa,wEAAwE;AAGrF,MAAI,iBAAiB;AACnB,WAAO;AAAA,EACR;AAGD,QAAM,iBAAiBA,cAAAA,MAAI,eAAe,eAAe;AACzD,MAAI,gBAAgB;AAClB,sBAAkB;AAClB,WAAO;AAAA,EACR;AAGD,SAAO;AACT;AAIY,MAAC,kBAAkB,YAAY;AACzCA,gBAAAA,MAAA,MAAA,QAAA,kCAAa,qEAAqE;AAElF,MAAI;AAEFA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,MACV,MAAM;AAAA,IACZ,CAAK;AAGD,UAAM,mBAAmB,MAAMC,iBAAAA;AAG/B,QAAI,qBAAqB,cAAc;AACrC,UAAI;AAEF,cAAM,WAAW,MAAMC,+BAAgB;AAAA,UACrC,MAAM;AAAA,UACN,SAAS;AAAA,QACnB,CAAS;AAGD,cAAM,cAAc;AAAA,UAClB,UAAU,SAAS;AAAA,UACnB,WAAW,SAAS;AAAA;AAAA;AAAA,UAGpB,UAAU,iBAAiB;AAAA,UAC3B,MAAM,iBAAiB;AAAA,UACvB,UAAU,iBAAiB;AAAA,UAC3B,SAAS,iBAAiB;AAAA,UAC1B,UAAU,iBAAiB;AAAA,QACrC;AAGQ,0BAAkB;AAClBF,sBAAAA,MAAI,eAAe,iBAAiB,WAAW;AAG/CA,sBAAAA,MAAI,MAAM,oBAAoB,WAAW;AAEzCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACpB,CAAS;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,KAAK;AAG9B,cAAM,mBAAmB,mBAAmB;AAE5C,eAAO;AAAA,MACR;AAAA,IACP,OAAW;AAELA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfG,6BAAAA;UACD;AAAA,QACF;AAAA,MACT,CAAO;AAGD,aAAO,mBAAmB;AAAA,IAC3B;AAAA,EACF,SAAQ,OAAO;AACdH,kBAAA,MAAA,MAAA,SAAA,mCAAc,aAAa,KAAK;AAEhCA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAChB,CAAK;AAED,WAAO,mBAAmB;AAAA,EAC3B;AACH;;;"}