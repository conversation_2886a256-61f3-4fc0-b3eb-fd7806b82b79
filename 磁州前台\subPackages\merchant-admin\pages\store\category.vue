<template>
  <view class="category-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">分类管理</view>
        <view class="navbar-right">
          <!-- 占位元素保持导航栏平衡 -->
        </view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 分类列表 -->
      <view class="category-list">
        <view class="category-item" v-for="(item, index) in categories" :key="index">
          <view class="category-content">
            <view class="category-icon" :style="{ backgroundColor: item.color + '20' }">
              <view class="icon-inner" :style="{ backgroundColor: item.color }">
                <text class="icon-text">{{item.name.substring(0, 1)}}</text>
              </view>
            </view>
            <view class="category-info">
              <view class="category-name">{{item.name}}</view>
              <view class="category-count">{{item.count}}个商品</view>
            </view>
            <view class="category-actions">
              <view class="action-btn edit-btn" @click="editCategory(index)">
                <text class="action-icon">✎</text>
              </view>
              <view class="action-btn delete-btn" @click="deleteCategory(index)">
                <text class="action-icon">✕</text>
              </view>
            </view>
          </view>
          <view class="sort-handle">
            <view class="sort-icon">≡</view>
          </view>
        </view>
      </view>
      
      <!-- 添加分类按钮 -->
      <view class="add-category-btn" @click="showAddCategoryModal">
        <text class="add-icon">+</text>
        <text class="add-text">添加分类</text>
      </view>
      
      <!-- 提示说明 -->
      <view class="category-tips">
        <view class="tips-title">
          <text class="tips-icon">💡</text>
          <text class="tips-text">分类管理小贴士</text>
        </view>
        <view class="tips-content">
          <text class="tips-item">· 合理设置商品分类可以帮助顾客更快找到商品</text>
          <text class="tips-item">· 分类名称建议简短明了，不超过8个字符</text>
          <text class="tips-item">· 可以长按拖动分类来调整显示顺序</text>
          <text class="tips-item">· 最多可添加20个商品分类</text>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 添加/编辑分类弹窗 -->
    <view class="modal-overlay" v-if="showModal" @click="hideModal"></view>
    <view class="category-modal" v-if="showModal">
      <view class="modal-header">
        <text class="modal-title">{{isEdit ? '编辑分类' : '添加分类'}}</text>
      </view>
      <view class="modal-content">
        <view class="form-item">
          <text class="form-label required">分类名称</text>
          <input type="text" class="form-input" v-model="currentCategory.name" placeholder="请输入分类名称" maxlength="8" />
          <text class="input-counter">{{currentCategory.name.length}}/8</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">分类颜色</text>
          <view class="color-selector">
            <view 
              class="color-item" 
              v-for="(color, colorIndex) in colorOptions" 
              :key="colorIndex"
              :class="{active: currentCategory.color === color}"
              @click="selectColor(color)"
              :style="{ backgroundColor: color }"
            ></view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">排序权重</text>
          <input type="number" class="form-input" v-model="currentCategory.sort" placeholder="数字越小越靠前，默认为0" />
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="hideModal">取消</button>
        <button class="modal-btn confirm" @click="saveCategory" :disabled="!currentCategory.name.trim()">确认</button>
      </view>
    </view>
    
    <!-- 删除确认弹窗 -->
    <view class="modal-overlay" v-if="showDeleteModal"></view>
    <view class="delete-modal" v-if="showDeleteModal">
      <view class="modal-header">
        <text class="modal-title">删除分类</text>
      </view>
      <view class="modal-content">
        <view class="confirm-message">确定要删除"{{deleteCategory ? categories[deleteIndex].name : ''}}"分类吗？</view>
        <view class="confirm-warning">删除后，该分类下的商品将被移至默认分类</view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="cancelDelete">取消</button>
        <button class="modal-btn confirm delete" @click="confirmDelete">删除</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      categories: [
        { name: '热销推荐', count: 15, color: '#FF3B30', sort: 0 },
        { name: '主食', count: 8, color: '#FF9500', sort: 1 },
        { name: '小吃', count: 12, color: '#34C759', sort: 2 },
        { name: '饮品', count: 6, color: '#007AFF', sort: 3 },
        { name: '甜点', count: 5, color: '#AF52DE', sort: 4 }
      ],
      colorOptions: [
        '#FF3B30', '#FF9500', '#FFCC00', '#34C759', 
        '#5AC8FA', '#007AFF', '#5856D6', '#AF52DE',
        '#FF2D55', '#A2846E'
      ],
      showModal: false,
      isEdit: false,
      editIndex: -1,
      currentCategory: {
        name: '',
        count: 0,
        color: '#FF3B30',
        sort: 0
      },
      showDeleteModal: false,
      deleteIndex: -1,
      statusBarHeight: 20
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 显示添加分类弹窗
    showAddCategoryModal() {
      if (this.categories.length >= 20) {
        uni.showToast({
          title: '最多只能添加20个分类',
          icon: 'none'
        });
        return;
      }
      
      this.isEdit = false;
      this.currentCategory = {
        name: '',
        count: 0,
        color: '#FF3B30',
        sort: this.categories.length
      };
      this.showModal = true;
    },
    
    // 显示编辑分类弹窗
    editCategory(index) {
      this.isEdit = true;
      this.editIndex = index;
      this.currentCategory = JSON.parse(JSON.stringify(this.categories[index]));
      this.showModal = true;
    },
    
    // 隐藏弹窗
    hideModal() {
      this.showModal = false;
    },
    
    // 选择颜色
    selectColor(color) {
      this.currentCategory.color = color;
    },
    
    // 保存分类
    saveCategory() {
      if (!this.currentCategory.name.trim()) {
        uni.showToast({
          title: '请输入分类名称',
          icon: 'none'
        });
        return;
      }
      
      if (this.isEdit) {
        // 编辑现有分类
        this.categories.splice(this.editIndex, 1, JSON.parse(JSON.stringify(this.currentCategory)));
      } else {
        // 添加新分类
        this.categories.push(JSON.parse(JSON.stringify(this.currentCategory)));
      }
      
      // 排序
      this.categories.sort((a, b) => a.sort - b.sort);
      
      this.hideModal();
      
      uni.showToast({
        title: this.isEdit ? '分类已修改' : '分类已添加',
        icon: 'success'
      });
    },
    
    // 删除分类
    deleteCategory(index) {
      this.deleteIndex = index;
      this.showDeleteModal = true;
    },
    
    // 取消删除
    cancelDelete() {
      this.showDeleteModal = false;
      this.deleteIndex = -1;
    },
    
    // 确认删除
    confirmDelete() {
      if (this.deleteIndex > -1) {
        // 获取要删除的分类名称
        const categoryName = this.categories[this.deleteIndex].name;
        
        // 删除分类
        this.categories.splice(this.deleteIndex, 1);
        
        this.showDeleteModal = false;
        this.deleteIndex = -1;
        
        uni.showToast({
          title: `已删除"${categoryName}"分类`,
          icon: 'success'
        });
      }
    }
  }
}
</script>

<style lang="scss">
.category-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 内容区域 */
.content-scroll {
  padding: 30rpx;
}

/* 分类列表 */
.category-list {
  margin-bottom: 30rpx;
}

.category-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  display: flex;
  overflow: hidden;
}

.category-content {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 20rpx;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-inner {
  width: 50rpx;
  height: 50rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
}

.icon-text {
  font-size: 28rpx;
  font-weight: 600;
}

.category-info {
  flex: 1;
  padding: 0 20rpx;
}

.category-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.category-count {
  font-size: 24rpx;
  color: #999;
}

.category-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}

.edit-btn {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
}

.delete-btn {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.action-icon {
  font-size: 28rpx;
}

.sort-handle {
  width: 70rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F5F8FC;
  color: #999;
}

.sort-icon {
  font-size: 32rpx;
}

/* 添加分类按钮 */
.add-category-btn {
  background-color: #FFFFFF;
  height: 100rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.add-icon {
  font-size: 32rpx;
  color: #0A84FF;
  margin-right: 10rpx;
}

.add-text {
  font-size: 30rpx;
  color: #0A84FF;
  font-weight: 500;
}

/* 提示说明 */
.category-tips {
  padding: 24rpx;
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}

.tips-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tips-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.tips-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.tips-content {
  display: flex;
  flex-direction: column;
}

.tips-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.8;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.category-modal, .delete-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required::after {
  content: '*';
  color: #FF3B30;
  margin-left: 6rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.input-counter {
  position: absolute;
  right: 24rpx;
  bottom: 30rpx;
  font-size: 24rpx;
  color: #999;
}

/* 颜色选择器 */
.color-selector {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.color-item {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  margin: 10rpx;
  position: relative;
}

.color-item.active::after {
  content: '✓';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 32rpx;
}

/* 确认删除 */
.confirm-message {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.confirm-warning {
  font-size: 24rpx;
  color: #FF3B30;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background-color: transparent;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

.delete {
  color: #FF3B30;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .category-container {
    background-color: #1C1C1E;
  }
  
  .category-item,
  .add-category-btn {
    background-color: #2C2C2E;
  }
  
  .category-name {
    color: #FFFFFF;
  }
  
  .sort-handle {
    background-color: #3A3A3C;
  }
  
  .category-tips {
    background-color: rgba(10, 132, 255, 0.1);
  }
  
  .tips-text {
    color: #FFFFFF;
  }
  
  .tips-item {
    color: #8E8E93;
  }
  
  .category-modal,
  .delete-modal {
    background-color: #2C2C2E;
  }
  
  .modal-title {
    color: #FFFFFF;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #3A3A3C;
  }
  
  .form-label {
    color: #FFFFFF;
  }
  
  .form-input {
    background-color: #3A3A3C;
    color: #FFFFFF;
  }
  
  .confirm-message {
    color: #FFFFFF;
  }
}
</style> 