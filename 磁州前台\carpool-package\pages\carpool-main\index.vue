<template>
  <view class="carpool-container">
    <!-- 自定义标题栏模块 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">磁州拼车</text>
        </view>
        <view class="right-action">
          <image src="/static/images/tabbar/更多.png" class="action-icon more-icon"></image>
        </view>
      </view>
    </view>
    
    <!-- 圆弧背景模块 -->
    <view class="arc-background"></view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 轮播图 -->
    <view class="swiper-section">
      <swiper class="swiper" indicator-dots autoplay interval="3000" duration="500" circular>
        <swiper-item v-for="(item, index) in banners" :key="index" @click="handleBannerClick(item)">
          <image :src="item.image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>
    </view>
    
    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <view class="search-input-wrapper">
          <input type="text" class="search-input" placeholder="请输入出发地" v-model="startPoint" />
          <view class="search-exchange">
            <text class="exchange-icon">⇄</text>
      </view>
          <input type="text" class="search-input" placeholder="请输入目的地" v-model="endPoint" />
      </view>
        <view class="search-button" @click="searchRoutes">
          <text>搜索</text>
      </view>
      </view>
    </view>
    
    <!-- 四宫格卡片 -->
    <view class="grid-section">
      <view class="grid-row">
        <view class="grid-item people-to-car" @click="navigateTo('people-to-car')">
          <view class="grid-content">
            <text class="grid-title">人找车</text>
            <text class="grid-subtitle">快速出行</text>
          </view>
        </view>
        <view class="grid-item car-to-people" @click="navigateTo('car-to-people')">
          <view class="grid-content">
            <text class="grid-title">车找人</text>
            <text class="grid-subtitle">拼车省钱</text>
          </view>
        </view>
      </view>
      <view class="grid-row">
        <view class="grid-item goods-to-car" @click="navigateTo('goods-to-car')">
          <view class="grid-content">
            <text class="grid-title">货找车</text>
            <text class="grid-subtitle">安全送达</text>
          </view>
        </view>
        <view class="grid-item car-to-goods" @click="navigateTo('car-to-goods')">
          <view class="grid-content">
            <text class="grid-title">车找货</text>
            <text class="grid-subtitle">满载而归</text>
      </view>
      </view>
      </view>
    </view>
    
    <!-- 热门路线 -->
    <view class="section hot-routes">
      <view class="section-header">
        <text class="section-title">热门路线</text>
        <text class="more-link" @click="viewMoreRoutes">更多</text>
      </view>
      <view class="route-list">
        <view class="route-item" v-for="(route, index) in hotRoutes" :key="index" @click="selectRoute(route)">
          <view class="route-path">
            <text class="start-point">{{route.start}}</text>
            <view class="route-arrow-container"></view>
            <text class="end-point">{{route.end}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 最新拼车信息 -->
    <view class="section latest-carpools">
      <view class="section-header">
        <text class="section-title">最新拼车</text>
        <view class="sort-dropdown">
          <view class="sort-label" @click="toggleSortOptions">
            <text>{{sortTypeText}}</text>
            <text class="sort-icon">▼</text>
          </view>
          <view class="sort-options" v-if="showSortOptions">
            <view class="sort-option" @click="changeSortType('default')">
              <text>默认排序</text>
            </view>
            <view class="sort-option" @click="changeSortType('departureTime')">
              <text>出发时间</text>
            </view>
            <view class="sort-option" @click="changeSortType('publishTime')">
              <text>发布时间</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="carpool-list">
        <view class="carpool-card" v-for="(item, index) in sortedCarpoolList" :key="index" @click="viewDetail(item)" :class="item.type">
          <!-- 优化后的拼车卡片布局 -->
          <view class="card-header">
            <view class="card-type-indicator"></view>
            <view class="route-direction">
              <view class="route-info-row">
                <text class="route-label">出发:</text>
                <text class="route-value">{{item.startPoint}}</text>
            </view>
              <view class="route-info-row via-points" v-if="item.viaPoints && item.viaPoints.length > 0">
                <text class="route-label">途经:</text>
                <view class="route-value-wrapper">
                  <text class="via-icon">⟡</text>
                  <text class="route-value">{{item.viaPoints.join(' → ')}}</text>
          </view>
            </view>
              <view class="route-info-row">
                <text class="route-label">到达:</text>
                <text class="route-value">{{item.endPoint}}</text>
          </view>
            </view>
            <!-- 类型标签 -->
            <view class="card-type-tag" :class="item.type + '-tag'">
              <text>{{item.typeText}}</text>
            </view>
            <!-- 发布方式标签 -->
            <view class="publish-mode-tag" :class="item.publishMode">
              <text>{{item.publishMode === 'premium' ? '置顶' : '广告'}}</text>
            </view>
          </view>
          
          <view class="card-footer">
            <view class="trip-info">
            <view class="info-item">
                <image src="/static/images/tabbar/时间.png" mode="aspectFit" class="info-icon"></image>
                <text class="info-value">{{item.departureTime}}</text>
            </view>
              
            <view class="info-item" v-if="item.price">
                <image src="/static/images/tabbar/价格.png" mode="aspectFit" class="info-icon"></image>
                <text class="info-value">{{item.price}}元</text>
            </view>
              
            <view class="info-item">
                <image src="/static/images/tabbar/座位.png" mode="aspectFit" class="info-icon"></image>
                <text class="info-value">{{item.seats}}座</text>
              </view>
            </view>
            
            <view class="user-actions">
              <view class="user-info">
                <image class="user-avatar" :src="item.avatar" mode="aspectFill"></image>
                <view class="user-name-wrapper">
                  <text class="user-name">{{item.username}}</text>
                  <view class="verified-badge" v-if="item.isVerified">
                    <view class="verified-icon-circle">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" fill="currentColor" />
                      </svg>
                    </view>
                    <text class="verified-text">已认证</text>
                  </view>
            </view>
          </view>
          
          <view class="card-actions">
                <button class="action-button phone" @click.stop="callPhone(item.phone)" data-label="拨打电话">
                  <image src="/static/images/tabbar/电话.png" mode="aspectFit"></image>
            </button>
                <button class="action-button chat" @click.stop="chatWith(item.userId)" data-label="发送私信">
                  <image src="/static/images/tabbar/消息.png" mode="aspectFit"></image>
            </button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 发布类型选择弹窗 -->
    <view class="publish-popup" v-if="showPublishPopup" @click="closePublishPopup">
      <view class="publish-card" @click.stop>
        <view class="publish-header">
          <text class="publish-title">选择发布类型</text>
          <view class="close-btn" @click="closePublishPopup">
            <text class="close-icon">×</text>
    </view>
        </view>
        <view class="publish-options">
          <view class="publish-option-row">
            <view class="publish-option" @click="navigateToPublish('people-to-car')">
              <view class="option-icon-wrapper people-car">
                <image src="/static/images/tabbar/人找车.png" mode="aspectFit" class="option-icon"></image>
              </view>
              <text class="option-text">人找车</text>
            </view>
            <view class="publish-option" @click="navigateToPublish('car-to-people')">
              <view class="option-icon-wrapper car-people">
                <image src="/static/images/tabbar/车找人.png" mode="aspectFit" class="option-icon"></image>
              </view>
              <text class="option-text">车找人</text>
            </view>
          </view>
          <view class="publish-option-row">
            <view class="publish-option" @click="navigateToPublish('goods-to-car')">
              <view class="option-icon-wrapper goods-car">
                <image src="/static/images/tabbar/货找车.png" mode="aspectFit" class="option-icon"></image>
              </view>
              <text class="option-text">货找车</text>
            </view>
            <view class="publish-option" @click="navigateToPublish('car-to-goods')">
              <view class="option-icon-wrapper car-goods">
                <image src="/static/images/tabbar/车找货.png" mode="aspectFit" class="option-icon"></image>
              </view>
              <text class="option-text">车找货</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部导航栏 -->
    <view class="tabbar">
      <view class="tabbar-item" @click="navigateToPage('home')">
        <image src="/static/images/tabbar/p首页.png" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text">同城</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'carpool' }">
        <image :src="activeTab === 'carpool' ? '/static/images/tabbar/p拼车选中.png' : '/static/images/tabbar/p拼车.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'carpool' }">拼车</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'publish' }" @click="publishCarpool">
        <image :src="activeTab === 'publish' ? '/static/images/tabbar/p发布选中.png' : '/static/images/tabbar/p发布.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'publish' }">发布</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'groups' }" @click="navigateToPage('groups')">
        <image :src="activeTab === 'groups' ? '/static/images/tabbar/p拼车群选中.png' : '/static/images/tabbar/p拼车群.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'groups' }">拼车群</text>
      </view>
      <view class="tabbar-item" :class="{ active: activeTab === 'my' }" @click="navigateToPage('my')">
        <image :src="activeTab === 'my' ? '/static/images/tabbar/p我的选中.png' : '/static/images/tabbar/p我的.png'" mode="aspectFit" class="tabbar-icon"></image>
        <text class="tabbar-text" :class="{ 'active-text': activeTab === 'my' }">我的</text>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { onLoad, onReady, onShow, onHide, onUnload, onBackPress } from '@dcloudio/uni-app';

// 响应式数据
const banners = reactive([
  { id: 1, image: '/static/images/tabbar/carpool-banner1.jpg', url: '/pages/carpool/activity/discount' },
  { id: 2, image: '/static/images/tabbar/carpool-banner2.jpg', url: '/pages/carpool/activity/newuser' }
]);

const hotRoutes = reactive([
  { id: 1, start: '磁县', end: '邯郸', count: 128 },
  { id: 2, start: '邯郸', end: '磁县', count: 112 },
  { id: 3, start: '邯郸', end: '北京', count: 86 },
  { id: 4, start: '北京', end: '邯郸', count: 72 },
  { id: 5, start: '邯郸', end: '石家庄', count: 65 },
  { id: 6, start: '石家庄', end: '邯郸', count: 53 }
]);

const activeTab = ref('carpool');

const latestCarpools = reactive([
    {
      id: 1,
        type: 'people-to-car',
        typeText: '人找车',
        avatar: '/static/images/avatar/user1.png',
        username: '张先生',
        isVerified: true,
        startPoint: '磁州城区-汽车站',
        endPoint: '邯郸火车站',
      departureTime: '今天 14:30',
        seats: 1,
      phone: '13812345678',
        userId: 'user123',
        publishMode: 'premium', // 付费置顶
        viaPoints: ['磁州东区', '峰峰矿区']
    },
    {
      id: 2,
        type: 'car-to-people',
        typeText: '车找人',
        avatar: '/static/images/avatar/user2.png',
        username: '李师傅',
        isVerified: true,
        startPoint: '邯郸机场',
        endPoint: '磁州城区-人民医院',
        departureTime: '今天 18:00',
        price: 30,
        seats: 3,
      phone: '13987654321',
        userId: 'user456',
        publishMode: 'ad', // 广告发布
        viaPoints: ['邯郸东站', '武安市']
    },
    {
      id: 3,
        type: 'goods-to-car',
        typeText: '货找车',
        avatar: '/static/images/avatar/user3.png',
        username: '王经理',
        isVerified: false,
        startPoint: '磁州商贸城',
        endPoint: '邯郸物流园',
        departureTime: '明天 09:00',
        phone: '13765432198',
        userId: 'user789',
        publishMode: 'premium', // 付费置顶
        viaPoints: ['永年区', '丛台区']
    },
    {
        id: 4,
      type: 'car-to-goods',
      typeText: '车找货',
        avatar: '/static/images/avatar/user4.png',
        username: '赵师傅',
      isVerified: true,
        startPoint: '邯郸市区',
        endPoint: '磁县工业园',
        departureTime: '明天 10:30',
        seats: 0,
        phone: '13598765432',
        userId: 'user321',
        publishMode: 'normal', // 普通发布
        viaPoints: []
    }
]);

const statusBarHeight = ref(0);
const showAnimation = ref(false);
const startPoint = ref('');
const endPoint = ref('');
const showPublishPopup = ref(false);
const sortedCarpoolList = ref([]);
const sortType = ref('default');
const showSortOptions = ref(false);
const currentType = ref('');
const filterStartPoint = ref('');
const filterEndPoint = ref('');

const sortTypeText = computed(() => {
    switch (sortType.value) {
        case 'default': return '默认排序';
        case 'departureTime': return '出发时间';
        case 'publishTime': return '发布时间';
        default: return '默认排序';
    }
});

// 方法
const setStatusBarHeight = () => {
    try {
        const systemInfo = uni.getSystemInfoSync();
        statusBarHeight.value = systemInfo.statusBarHeight || 20;

        if (systemInfo.brand) {
            const brand = systemInfo.brand.toLowerCase();
            if (brand.includes('iphone')) {
                if (systemInfo.safeAreaInsets && systemInfo.safeAreaInsets.top > 20) {
                    statusBarHeight.value = Math.max(statusBarHeight.value, systemInfo.safeAreaInsets.top);
                }
            }
        }

        if (typeof document !== 'undefined' && document.documentElement) {
            document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight.value}px`);
            document.documentElement.style.setProperty('--status-bar-color', '#1677FF');
            document.documentElement.style.setProperty('--status-bar-bg-color', '#1677FF');
        }
        uni.setStorageSync('statusBarHeight', statusBarHeight.value);

        // 设置状态栏颜色 - 确保是纯蓝色 #1677FF
        uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: '#1677FF',
            animation: {
                duration: 0,
                timingFunc: 'easeIn'
            }
        });

        // #ifdef APP-PLUS
        // 在APP环境下，直接设置状态栏颜色和样式
        plus.navigator.setStatusBarStyle('light');
        plus.navigator.setStatusBarBackground('#1677FF');
        // #endif

        if (systemInfo.platform) {
            uni.setStorageSync('platform', systemInfo.platform);
            uni.setStorageSync('isIOS', systemInfo.platform === 'ios');
        }
    } catch (e) {
        console.error('设置状态栏高度出错:', e);
        statusBarHeight.value = 20;
        if (typeof document !== 'undefined' && document.documentElement) {
            document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight.value}px`);
        }
        uni.setStorageSync('statusBarHeight', statusBarHeight.value);
    }
};

const handleBannerClick = (banner) => {
    uni.navigateTo({
        url: '/carpool-package' + banner.url
    });
};

const exchangePoints = () => {
    [startPoint.value, endPoint.value] = [endPoint.value, startPoint.value];
};

const searchRoutes = () => {
    if (!startPoint.value || !endPoint.value) {
        uni.showToast({ title: '请输入出发地和目的地', icon: 'none' });
        return;
    }
  uni.navigateTo({
        url: `/carpool-package/pages/carpool/list/index?start=${startPoint.value}&end=${endPoint.value}`
  });
};

const getTypeText = (type) => {
    switch(type) {
        case 'people-to-car': return '人找车';
        case 'car-to-people': return '车找人';
        case 'goods-to-car': return '货找车';
        case 'car-to-goods': return '车找货';
        default: return '全部';
    }
};

const navigateTo = (type) => {
    currentType.value = type;
    sortCarpoolList();
    uni.pageScrollTo({ selector: '.latest-carpools', duration: 300 });
    uni.showToast({ title: `已筛选${getTypeText(type)}信息`, icon: 'none', duration: 1500 });
};

const viewMoreRoutes = () => {
    uni.navigateTo({ url: '/carpool-package/pages/carpool/routes/index' });
};

const selectRoute = (route) => {
    if (!route || !route.start || !route.end) {
        uni.showToast({ title: '路线数据不完整', icon: 'none' });
        return;
    }
    filterStartPoint.value = route.start;
    filterEndPoint.value = route.end;
    sortCarpoolList();
    uni.pageScrollTo({ selector: '.latest-carpools', duration: 300 });
    uni.showToast({ title: `已筛选 ${route.start}→${route.end} 路线`, icon: 'none', duration: 1500 });
};

const viewAllCarpools = () => {
    currentType.value = '';
    filterStartPoint.value = '';
    filterEndPoint.value = '';
    sortCarpoolList();
};

const viewDetail = (item) => {
    uni.navigateTo({ url: `/carpool-package/pages/carpool/detail/index?id=${item.id}&type=${item.type}` });
};

const callPhone = (phone) => {
    uni.makePhoneCall({ phoneNumber: phone });
};

const chatWith = (userId) => {
    uni.navigateTo({ url: `/pages/message/chat?userId=${userId}` });
};

const publishCarpool = () => {
    activeTab.value = 'publish';
  showPublishPopup.value = true;
};

const getLatestCarpools = () => {
    sortCarpoolList();
};

const parseDepartureTime = (timeStr) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (timeStr.includes('今天')) {
        const timeParts = timeStr.split(' ')[1].split(':');
        return today.setHours(parseInt(timeParts[0]), parseInt(timeParts[1]));
    } else if (timeStr.includes('明天')) {
        const timeParts = timeStr.split(' ')[1].split(':');
        return tomorrow.setHours(parseInt(timeParts[0]), parseInt(timeParts[1]));
    } else {
        return new Date(timeStr).getTime();
    }
};

const sortCarpoolList = () => {
    let filteredList = latestCarpools.slice();
    if (currentType.value) {
        filteredList = filteredList.filter(item => item.type === currentType.value);
    }
    if (filterStartPoint.value && filterEndPoint.value) {
        filteredList = filteredList.filter(item => 
            item.startPoint.includes(filterStartPoint.value) && 
            item.endPoint.includes(filterEndPoint.value)
        );
    }
    sortedCarpoolList.value = filteredList.sort((a, b) => {
        if (a.publishMode === 'premium' && b.publishMode !== 'premium') return -1;
        if (a.publishMode !== 'premium' && b.publishMode === 'premium') return 1;
        if (sortType.value === 'departureTime') {
            return parseDepartureTime(a.departureTime) - parseDepartureTime(b.departureTime);
        } else if (sortType.value === 'publishTime') {
            return b.id - a.id;
        }
        return 0;
    });

    if (sortedCarpoolList.value.length === 0 && (currentType.value || filterStartPoint.value)) {
        uni.showToast({ title: '没有找到符合条件的拼车信息', icon: 'none', duration: 2000 });
    }
};

const navigateToPage = (page) => {
    activeTab.value = page;
    switch(page) {
        case 'home':
            uni.showTabBar();
            setTimeout(() => uni.switchTab({ url: '/pages/index/index' }), 50);
            break;
        case 'groups':
            uni.hideTabBar();
            uni.navigateTo({ url: '/carpool-package/pages/carpool/groups/index' });
            break;
        case 'my':
            uni.hideTabBar();
            uni.navigateTo({ url: '/carpool-package/pages/carpool/my/index' });
            break;
    }
};

const closePublishPopup = () => {
  showPublishPopup.value = false;
    activeTab.value = 'carpool';
};

const navigateToPublish = (type) => {
  closePublishPopup();
    let url = '';
    switch(type) {
        case 'people-to-car': url = '/carpool-package/pages/carpool/publish/people-to-car'; break;
        case 'car-to-people': url = '/carpool-package/pages/carpool/publish/car-to-people'; break;
        case 'goods-to-car': url = '/carpool-package/pages/carpool/publish/goods-to-car'; break;
        case 'car-to-goods': url = '/carpool-package/pages/carpool/publish/car-to-goods'; break;
        default: url = '/carpool-package/pages/carpool/publish/people-to-car';
    }
    uni.navigateTo({ url });
};

const toggleSortOptions = () => {
    showSortOptions.value = !showSortOptions.value;
};

const changeSortType = (type) => {
    sortType.value = type;
    showSortOptions.value = false;
    sortCarpoolList();
};

const goBack = () => {
    uni.navigateBack();
};

// 生命周期钩子
onLoad((options) => {
    setStatusBarHeight();
    getLatestCarpools();
    activeTab.value = 'carpool';
    uni.hideTabBar();
    setTimeout(() => { showAnimation.value = true; }, 300);
    
    // 获取当前页面对象
    const currentPage = getCurrentPages().pop();
    const eventChannel = currentPage.getOpenerEventChannel();
    
    if (eventChannel && eventChannel.on) {
        eventChannel.on('showPublishPopup', () => {
            showPublishPopup.value = true;
        });
    }

    const showPopup = uni.getStorageSync('showPublishPopup');
    if (showPopup) {
        showPublishPopup.value = true;
        uni.removeStorageSync('showPublishPopup');
    }
    sortCarpoolList();
});

onReady(() => {
    setTimeout(() => {
        const storedHeight = uni.getStorageSync('statusBarHeight');
        if (storedHeight && storedHeight !== statusBarHeight.value) {
            statusBarHeight.value = storedHeight;
            if (typeof document !== 'undefined' && document.documentElement) {
                document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight.value}px`);
            }
        }
    }, 50);
});

onShow(() => {
    uni.hideTabBar();
});

onHide(() => {
    setTimeout(() => uni.showTabBar(), 100);
});

onUnload(() => {
    uni.showTabBar();
});

onBackPress((event) => {
    if (event.from === 'backbutton') {
        uni.showTabBar();
    uni.switchTab({ url: '/pages/index/index' });
        return true;
  }
    return false;
});
</script>

<style lang="scss">
/* 全局CSS变量定义 */
:root {
  --status-bar-color: #1677FF;
  --status-bar-bg-color: #1677FF;
  --navbar-bg-color: #1677FF;
}

page {
  background-color: #F5F8FC;
  --status-bar-color: #1677FF;
}

/* 平台特定覆盖 - 确保状态栏颜色正确 */
/* #ifdef H5 || MP */
:root, page {
  --status-bar-color: #1677FF !important;
}

@media (prefers-color-scheme: light), (prefers-color-scheme: dark) {
  uni-page-head, uni-page-head .uni-page-head {
    background-color: #1677FF !important;
  }
}

/* 强制覆盖小程序状态栏颜色 */
uni-page-head {
  background-color: #1677FF !important;
}

uni-page-head .uni-page-head {
  background-color: #1677FF !important;
}
/* #endif */

/* #ifdef APP-PLUS */
/* APP平台特定样式 */
:root, page {
  --status-bar-color: #1677FF !important;
  --status-bar-bg-color: #1677FF !important;
  --navbar-bg-color: #1677FF !important;
}
/* #endif */

.carpool-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  padding-bottom: 140rpx;
  padding-top: calc(var(--status-bar-height) + 90rpx); /* 只考虑标题栏高度 */
  position: relative;
  overflow-x: hidden;
}

/* 自定义标题栏模块 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #1677FF; /* 恢复为实色背景 */
  z-index: 103;
  box-shadow: none;
}

/* 标题栏内容 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  position: relative;
  z-index: 102;
}

.left-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-area {
  flex: 1;
  text-align: center;
}

.page-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.right-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

/* 轮播图区域 - 确保在背景之上 */
.swiper-section {
  padding: 0 32rpx;
  position: relative;
  z-index: 10; /* 确保内容显示在圆弧背景之上 */
  margin-top: 50rpx; /* 从20rpx增加到50rpx，向下移动30rpx */
  background-color: transparent;
}

.swiper {
  height: 220rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.15);
  background-color: #ffffff;
  position: relative;
  z-index: 1;
}

.swiper image {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
  transition: transform 0.3s ease;
}

/* 搜索框 */
.search-section {
  padding: 0 32rpx;
  position: relative;
  z-index: 10; /* 确保内容显示在圆弧背景之上 */
  margin-top: 15rpx; /* 与轮播图的间距 */
  margin-bottom: 15rpx; /* 下方间距 */
  background-color: transparent;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 8rpx;
  box-shadow: 0 8rpx 20rpx rgba(10, 132, 255, 0.15), 0 2rpx 4rpx rgba(0, 0, 0, 0.05); /* 增强阴影 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.7);
  position: relative; /* 用于伪元素定位 */
  overflow: hidden; /* 确保内容不溢出 */
  
  /* 添加微妙的内部光效 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), transparent);
    pointer-events: none;
  }
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #F2F7FD;
  border-radius: 16rpx;
  padding: 0 20rpx;
  height: 80rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05); /* 内阴影 */
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 10rpx;
}

.search-exchange {
  padding: 0 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.exchange-icon {
  font-size: 32rpx;
  color: #0A84FF;
  font-weight: bold;
}

.search-button {
  padding: 0 30rpx;
  height: 80rpx;
  background: linear-gradient(to right, #0A84FF, #0040DD);
  color: #ffffff;
  border-radius: 16rpx;
  margin-left: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3), 0 2rpx 4rpx rgba(0, 0, 0, 0.1); /* 增强阴影 */
  position: relative; /* 用于伪元素定位 */
  overflow: hidden; /* 确保内容不溢出 */
  
  /* 添加微妙的内部光效 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
    pointer-events: none;
  }
  
  /* 按下效果 */
  &:active {
    transform: translateY(1rpx) scale(0.98);
    box-shadow: 0 2rpx 6rpx rgba(10, 132, 255, 0.2);
  }
}

/* 四宫格卡片 */
.grid-section {
  padding: 10rpx;
  background-color: transparent;
  margin: 16rpx 32rpx; /* 减少上下边距 */
  border-radius: 24rpx;
  position: relative;
  z-index: 1;
}

.grid-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx; /* 减少行间距 */
}

.grid-row:last-child {
  margin-bottom: 0;
}

.grid-item {
  width: 48%; /* 固定宽度为48% */
  margin: 1%; /* 使用margin替代gap */
  padding: 20rpx; /* 调整内边距 */
  background-color: #FFFFFF;
  border-radius: 20rpx; /* 增大圆角从12rpx到20rpx */
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05); /* 减轻阴影 */
  position: relative;
  overflow: hidden;
  border: 1rpx solid #EEEEEE; /* 普通浅灰色边框 */
  flex-shrink: 0;
  flex-grow: 0;
  
  /* 简化光效 */
  &::after {
    display: none; /* 移除光效 */
  }
}

.grid-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  z-index: 1;
  border-radius: 32rpx;
}

.grid-item::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.25), transparent 70%);
  z-index: 1;
  border-radius: 32rpx;
}

.grid-item:active {
  transform: translateY(-3rpx) scale(0.98);
  box-shadow: 0 5rpx 12rpx rgba(0, 0, 0, 0.08);
}

.grid-content {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  padding: 16rpx;
}

.grid-title {
  font-size: 34rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 8rpx;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 3;
  letter-spacing: 1rpx;
}

.grid-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 3;
}

.people-to-car {
  background: linear-gradient(135deg, #4F6EF7, #1E40AF);
  box-shadow: 0 10rpx 25rpx rgba(79, 110, 247, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

.car-to-people {
  background: linear-gradient(135deg, #F43F5E, #BE123C);
  box-shadow: 0 10rpx 25rpx rgba(244, 63, 94, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

.goods-to-car {
  background: linear-gradient(135deg, #10B981, #047857);
  box-shadow: 0 10rpx 25rpx rgba(16, 185, 129, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

.car-to-goods {
  background: linear-gradient(135deg, #F59E0B, #B45309);
  box-shadow: 0 10rpx 25rpx rgba(245, 158, 11, 0.25), inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

/* 热门路线 */
.section.hot-routes {
  margin: 16rpx 32rpx; /* 减少上下边距 */
  padding: 24rpx; /* 减少内边距 */
  background-color: #FFFFFF;
  border-radius: 24rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
  padding-bottom: 0; /* 移除底部内边距 */
}

/* 移除下边框 */
.section-header::after {
  display: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 0; /* 移除左侧内边距 */
}

/* 移除左侧蓝色装饰条 */
.section-title::before {
  display: none;
}

.more-link {
  font-size: 26rpx;
  color: #0A84FF;
  font-weight: 500;
  padding: 6rpx 12rpx; /* 增加点击区域 */
  border-radius: 12rpx; /* 圆角边框 */
  transition: all 0.2s ease;
  
  /* 微妙的悬停效果 */
  &:active {
    background-color: rgba(10, 132, 255, 0.08);
    transform: scale(0.95);
  }
}

.route-list {
  display: flex;
  flex-flow: row wrap;
  width: 100%;
  box-sizing: border-box;
  padding: 4rpx;
  justify-content: space-between;
}

.route-item {
  width: 48%;
  margin-bottom: 16rpx;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  border: 1rpx solid #EEEEEE;
  flex: 0 0 auto;
  
  /* 简化光效 */
  &::after {
    display: none; /* 移除光效 */
  }
}

/* iOS设备特殊处理 */
@supports (-webkit-touch-callout: none) {
  .route-list {
    display: flex;
    flex-flow: row wrap;
    width: 100%;
    box-sizing: border-box;
    padding: 4rpx;
  }
  
  .route-item {
    width: 48%;
    margin: 1%;
    flex: 0 0 auto;
    box-sizing: border-box;
  }
}

.route-item:active {
  transform: translateY(1rpx); /* 减轻按下效果 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  background-color: #FAFAFA; /* 更淡的背景色 */
}

.route-path {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.start-point, .end-point {
  font-size: 28rpx;
  color: #333333; /* 全部使用黑色 */
  font-weight: 500;
  max-width: 42%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 移除单独的颜色设置 */
.start-point {
  /* 移除蓝色 */
}

.end-point {
  /* 保持黑色 */
}

/* 简化箭头设计 */
.route-arrow-container {
  position: relative;
  width: 30rpx; /* 减小宽度从50rpx到30rpx */
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.route-arrow-container::before {
  content: '';
  position: absolute;
  left: 0;
  right: 10rpx;
  top: 50%;
  height: 1rpx; /* 保持细线 */
  background-color: #CCCCCC; /* 稍微亮一点的灰色 */
  transform: translateY(-50%);
}

.route-arrow-container::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  width: 8rpx;
  height: 8rpx;
  border: 1rpx solid #CCCCCC; /* 与线条颜色匹配 */
  border-left: none;
  border-bottom: none;
  transform: translateY(-50%) rotate(45deg);
}

/* 最新拼车信息 */
.section.latest-carpools {
  margin: 16rpx 32rpx; /* 减少上下边距 */
  padding: 24rpx; /* 减少内边距 */
  background-color: #FFFFFF;
  border-radius: 24rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  overflow: hidden;
  margin-bottom: 50rpx; /* 减少底部边距 */
}

.sort-dropdown {
  position: relative;
}

.sort-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666666;
  padding: 8rpx 16rpx;
  background-color: #F2F7FD;
  border-radius: 16rpx;
}

.sort-icon {
  font-size: 20rpx;
  margin-left: 6rpx;
  color: #0A84FF;
}

.sort-options {
  position: absolute;
  right: 0;
  top: 60rpx;
  width: 200rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  z-index: 10;
  overflow: hidden;
}

.sort-option {
  padding: 20rpx 24rpx;
  font-size: 26rpx;
  color: #333333;
  transition: all 0.2s ease;
}

.sort-option:active {
  background-color: #F2F7FD;
}

.carpool-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 4rpx; /* 添加内边距 */
}

.carpool-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */
  transition: all 0.3s ease;
  position: relative;
  border-left: 6rpx solid #0A84FF;
  box-sizing: border-box;
  
  /* 添加顶部渐变边框 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 6rpx; /* 留出左侧边框 */
    right: 0;
    height: 4rpx;
    background: linear-gradient(to right, transparent, rgba(10, 132, 255, 0.3), transparent);
    z-index: 2;
  }
  
  /* 添加微妙的内部光效 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 6rpx; /* 留出左侧边框 */
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent 60%);
    pointer-events: none;
  }
}

.carpool-card.people-to-car {
  border-left-color: #0A84FF;
}

.carpool-card.car-to-people {
  border-left-color: #FF2D55;
}

.carpool-card.goods-to-car {
  border-left-color: #30D158;
}

.carpool-card.car-to-goods {
  border-left-color: #FF9F0A;
}

.carpool-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  padding: 24rpx;
  position: relative;
  background-color: #FFFFFF;
}

.card-type-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background-color: inherit;
}

.route-direction {
  margin-left: 12rpx;
}

.route-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.route-info-row:last-child {
  margin-bottom: 0;
}

.route-label {
  width: 80rpx;
  font-size: 26rpx;
  color: #999999;
}

.route-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.via-points {
  color: #666666;
}

.via-icon {
  margin-right: 8rpx;
  color: #0A84FF;
}

.route-value-wrapper {
  display: flex;
  align-items: center;
}

.card-type-tag {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: #FFFFFF;
}

.people-to-car-tag {
  background-color: #0A84FF;
}

.car-to-people-tag {
  background-color: #FF2D55;
}

.goods-to-car-tag {
  background-color: #30D158;
}

.car-to-goods-tag {
  background-color: #FF9F0A;
}

.publish-mode-tag {
  position: absolute;
  top: 70rpx;
  right: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: #FFFFFF;
}

.premium {
  background-color: #FF2D55;
}

.ad {
  background-color: #FF9F0A;
}

.card-footer {
  padding: 24rpx;
  border-top: 1px solid #F2F2F7;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
}

.trip-info {
  display: flex;
  gap: 24rpx;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.user-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-actions {
  display: flex;
  gap: 16rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-icon {
  width: 32rpx;
  height: 32rpx;
}

.info-value {
  font-size: 26rpx;
  color: #666666;
}

.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.user-name-wrapper {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.user-name {
  font-size: 26rpx;
  color: #333333;
  max-width: 120rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 认证标签样式 */
.verified-badge {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.15), rgba(10, 132, 255, 0.05));
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(10, 132, 255, 0.3);
  box-shadow: 0 2rpx 6rpx rgba(10, 132, 255, 0.1);
}

.verified-icon-circle {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #0A84FF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);
}

.verified-icon-circle svg {
  color: white;
}

.verified-text {
  font-size: 20rpx;
  color: #0A84FF;
  font-weight: 500;
}

.action-button {
  width: 88rpx;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.action-button.phone {
  background-color: #34C759;
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.3);
}

.action-button.chat {
  background-color: #0A84FF;
  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3);
}

.action-button image {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* 发布类型选择弹窗 */
.publish-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.publish-card {
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: card-pop 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  transform: scale(0.95);
  opacity: 0.8;
}

@keyframes card-pop {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.publish-header {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #F2F2F7;
}

.publish-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 32rpx;
  color: #999999;
}

.publish-options {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
}

.publish-option-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 32rpx;
}

.publish-option-row:last-child {
  margin-bottom: 0;
}

.publish-option {
  width: 48%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.publish-option:active {
  transform: scale(0.95);
  background-color: #F5F8FC;
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.option-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.12);
  border: 2rpx solid rgba(255, 255, 255, 0.7);
  position: relative;
  overflow: hidden;
}

.option-icon-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.3), transparent 70%);
  z-index: 1;
}

.option-icon {
  width: 60rpx;
  height: 60rpx;
  filter: brightness(0) invert(1);
  position: relative;
  z-index: 2;
}

.people-car {
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
}

.car-people {
  background: linear-gradient(135deg, #FF2D55, #FF9500);
}

.goods-car {
  background: linear-gradient(135deg, #30D158, #34C759);
}

.car-goods {
  background: linear-gradient(135deg, #FF9F0A, #FFD60A);
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 110rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  position: relative;
  transition: all 0.2s ease;
}

.tabbar-item:active {
  opacity: 0.7;
}

.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

.tabbar-text {
  font-size: 22rpx;
  color: #999999;
  line-height: 1;
}

.active-text {
  color: #0A84FF;
  font-weight: 500;
}

.tabbar-item.active .tabbar-icon {
  transform: scale(1.1);
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
  opacity: 0.4;
}

.bg-circle-1 {
  top: 260rpx; /* 调整位置 */
  left: -150rpx;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent 70%);
}

.bg-circle-2 {
  top: 80rpx; /* 调整位置 */
  right: -100rpx;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.25), transparent 70%);
}

.bg-circle-3 {
  bottom: 220rpx;
  left: -120rpx;
  width: 240rpx;
  height: 240rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2), transparent 70%);
}

/* 过渡效果 */
.bg-loaded {
  animation: fadeIn 0.3s ease forwards;
}

.navbar-loaded {
  animation: slideDown 0.3s ease forwards;
}

@keyframes fadeIn {
  from { opacity: 0.8; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-5px); opacity: 0.8; }
  to { transform: translateY(0); opacity: 1; }
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}

/* 圆弧背景模块 */
.arc-background {
  position: fixed;
  top: calc(var(--status-bar-height) + 90rpx); /* 从标题栏底部开始 */
  left: 0;
  right: 0;
  height: 240rpx; /* 增加高度以覆盖更多内容区域 */
  background-color: #1677FF;
  border-bottom-left-radius: 32rpx;
  border-bottom-right-radius: 32rpx;
  z-index: 1;
}

/* 移除原来的伪元素背景 */
.carpool-container::before {
  content: none;
}

/* 内容区域布局调整 */
.swiper-section, .search-section, .grid-section, .section {
  position: relative;
  z-index: 10; /* 确保内容显示在圆弧背景之上 */
}
</style>