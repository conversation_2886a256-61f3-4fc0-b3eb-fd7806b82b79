<template>
  <view class="invite-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">邀请好友</view>
        <view class="navbar-right">
          <view class="share-btn" @click="shareInvite">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="18" cy="5" r="3"></circle>
              <circle cx="6" cy="12" r="3"></circle>
              <circle cx="18" cy="19" r="3"></circle>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 邀请奖励卡片 -->
      <view class="invite-reward-card">
        <view class="reward-header">
          <view class="reward-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </view>
          <view class="reward-info">
            <text class="reward-title">邀请好友赚奖励</text>
            <text class="reward-desc">好友注册成功，双方都能获得丰厚奖励</text>
          </view>
        </view>

        <view class="reward-benefits">
          <view class="benefit-item">
            <view class="benefit-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
              </svg>
            </view>
            <view class="benefit-content">
              <text class="benefit-title">积分奖励</text>
              <text class="benefit-desc">每邀请1人获得100积分</text>
            </view>
          </view>
          <view class="benefit-item">
            <view class="benefit-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <line x1="8" y1="21" x2="16" y2="21"></line>
                <line x1="12" y1="17" x2="12" y2="21"></line>
              </svg>
            </view>
            <view class="benefit-content">
              <text class="benefit-title">优惠券</text>
              <text class="benefit-desc">好友注册送20元优惠券</text>
            </view>
          </view>
          <view class="benefit-item">
            <view class="benefit-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </view>
            <view class="benefit-content">
              <text class="benefit-title">现金奖励</text>
              <text class="benefit-desc">好友首单完成奖励5元</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 邀请统计 -->
      <view class="invite-stats">
        <view class="stats-header">
          <text class="stats-title">我的邀请</text>
        </view>
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">{{ inviteCount }}</text>
            <text class="stat-label">邀请人数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ successCount }}</text>
            <text class="stat-label">成功注册</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ totalReward }}</text>
            <text class="stat-label">累计奖励</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ todayInvite }}</text>
            <text class="stat-label">今日邀请</text>
          </view>
        </view>
      </view>

      <!-- 邀请方式 -->
      <view class="invite-methods">
        <view class="methods-header">
          <text class="methods-title">邀请方式</text>
        </view>
        <view class="methods-grid">
          <view class="method-item" @click="shareToWechat">
            <view class="method-icon wechat">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
              </svg>
            </view>
            <text class="method-name">微信好友</text>
          </view>
          <view class="method-item" @click="shareToMoments">
            <view class="method-icon moments">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <circle cx="12" cy="12" r="6"></circle>
                <circle cx="12" cy="12" r="2"></circle>
              </svg>
            </view>
            <text class="method-name">朋友圈</text>
          </view>
          <view class="method-item" @click="copyInviteCode">
            <view class="method-icon copy">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
            </view>
            <text class="method-name">复制链接</text>
          </view>
          <view class="method-item" @click="generatePoster">
            <view class="method-icon poster">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                <polyline points="21,15 16,10 5,21"></polyline>
              </svg>
            </view>
            <text class="method-name">生成海报</text>
          </view>
        </view>
      </view>

      <!-- 邀请码 -->
      <view class="invite-code-card">
        <view class="code-header">
          <text class="code-title">我的邀请码</text>
          <view class="code-copy" @click="copyInviteCode">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            <text class="copy-text">复制</text>
          </view>
        </view>
        <view class="code-content">
          <text class="code-value">{{ inviteCode }}</text>
        </view>
        <view class="code-desc">
          <text class="desc-text">好友输入邀请码注册，双方都能获得奖励</text>
        </view>
      </view>

      <!-- 邀请记录 -->
      <view class="invite-records">
        <view class="records-header">
          <text class="records-title">邀请记录</text>
          <view class="view-all" @click="viewAllRecords">
            <text class="view-all-text">查看全部</text>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </view>
        </view>
        <view class="records-list">
          <view class="record-item" v-for="(record, index) in recentRecords" :key="index">
            <view class="record-avatar">
              <image class="avatar-img" :src="record.avatar" mode="aspectFill"></image>
            </view>
            <view class="record-info">
              <text class="record-name">{{ record.name }}</text>
              <text class="record-time">{{ record.time }}</text>
            </view>
            <view class="record-status" :class="record.status">
              <text class="status-text">{{ getStatusText(record.status) }}</text>
            </view>
            <view class="record-reward" v-if="record.status === 'success'">
              <text class="reward-text">+{{ record.reward }}积分</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 邀请规则 -->
      <view class="invite-rules">
        <view class="rules-header">
          <text class="rules-title">邀请规则</text>
        </view>
        <view class="rules-content">
          <view class="rule-item">
            <text class="rule-text">• 好友通过您的邀请码注册成功，您可获得100积分奖励</text>
          </view>
          <view class="rule-item">
            <text class="rule-text">• 好友首次下单完成后，您可额外获得5元现金奖励</text>
          </view>
          <view class="rule-item">
            <text class="rule-text">• 被邀请的好友注册成功可获得20元优惠券</text>
          </view>
          <view class="rule-item">
            <text class="rule-text">• 奖励将在好友注册成功后24小时内发放</text>
          </view>
          <view class="rule-item">
            <text class="rule-text">• 每个用户只能被邀请一次，重复邀请无效</text>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const inviteCode = ref('ABC123')
const inviteCount = ref(15)
const successCount = ref(12)
const totalReward = ref(1200)
const todayInvite = ref(3)

// 邀请记录
const recentRecords = ref([
  {
    name: '张三',
    avatar: '/static/images/avatar/avatar1.jpg',
    time: '2024-01-20 14:30',
    status: 'success',
    reward: 100
  },
  {
    name: '李四',
    avatar: '/static/images/avatar/avatar2.jpg',
    time: '2024-01-20 10:15',
    status: 'pending',
    reward: 0
  },
  {
    name: '王五',
    avatar: '/static/images/avatar/avatar3.jpg',
    time: '2024-01-19 16:45',
    status: 'success',
    reward: 100
  }
])

// 页面加载
onMounted(() => {
  console.log('邀请好友页面加载')
  loadInviteData()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function shareInvite() {
  uni.showActionSheet({
    itemList: ['微信好友', '朋友圈', '复制链接', '生成海报'],
    success: (res) => {
      switch(res.tapIndex) {
        case 0:
          shareToWechat()
          break
        case 1:
          shareToMoments()
          break
        case 2:
          copyInviteCode()
          break
        case 3:
          generatePoster()
          break
      }
    }
  })
}

function shareToWechat() {
  // 分享到微信好友
  uni.showToast({
    title: '分享到微信好友',
    icon: 'none'
  })
}

function shareToMoments() {
  // 分享到朋友圈
  uni.showToast({
    title: '分享到朋友圈',
    icon: 'none'
  })
}

function copyInviteCode() {
  uni.setClipboardData({
    data: `我的邀请码：${inviteCode.value}，快来注册领取奖励吧！`,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
    }
  })
}

function generatePoster() {
  uni.showToast({
    title: '正在生成海报...',
    icon: 'loading'
  })
  
  // 模拟生成海报
  setTimeout(() => {
    uni.showToast({
      title: '海报生成成功',
      icon: 'success'
    })
  }, 2000)
}

function viewAllRecords() {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/invite/records'
  })
}

function getStatusText(status) {
  switch(status) {
    case 'success':
      return '已注册'
    case 'pending':
      return '待注册'
    case 'failed':
      return '注册失败'
    default:
      return '未知'
  }
}

function loadInviteData() {
  // 模拟加载邀请数据
  setTimeout(() => {
    console.log('邀请数据加载完成')
  }, 500)
}
</script>

<style scoped>
/* 邀请好友样式开始 */
.invite-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #E91E63 0%, #C2185B 100%);
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(233, 30, 99, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn, .share-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
}

.back-btn svg, .share-btn svg {
  color: white;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
  height: 100vh;
}

/* 邀请奖励卡片样式 */
.invite-reward-card {
  margin: 20px 16px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.reward-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.reward-icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #E91E63, #C2185B);
  border-radius: 32px;
}

.reward-icon svg {
  color: white;
}

.reward-info {
  flex: 1;
}

.reward-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.reward-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.reward-benefits {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}

.benefit-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #E91E63, #C2185B);
  border-radius: 20px;
}

.benefit-icon svg {
  color: white;
}

.benefit-content {
  flex: 1;
}

.benefit-title {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.benefit-desc {
  display: block;
  font-size: 12px;
  color: #666;
}

/* 邀请统计样式 */
.invite-stats {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.stats-header {
  margin-bottom: 16px;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  gap: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #E91E63;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 邀请方式样式 */
.invite-methods {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.methods-header {
  margin-bottom: 16px;
}

.methods-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.method-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  gap: 8px;
  transition: all 0.3s ease;
}

.method-item:active {
  transform: scale(0.95);
}

.method-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 28px;
}

.method-icon.wechat {
  background: #07C160;
}

.method-icon.moments {
  background: #1AAD19;
}

.method-icon.copy {
  background: #576B95;
}

.method-icon.poster {
  background: #FF6B35;
}

.method-icon svg {
  color: white;
}

.method-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 邀请码卡片样式 */
.invite-code-card {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.code-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.code-copy {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #E91E63;
  border-radius: 16px;
}

.code-copy svg {
  color: white;
}

.copy-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.code-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  text-align: center;
  margin-bottom: 12px;
}

.code-value {
  font-size: 24px;
  font-weight: 700;
  color: #E91E63;
  letter-spacing: 2px;
}

.code-desc {
  text-align: center;
}

.desc-text {
  font-size: 12px;
  color: #666;
}

/* 邀请记录样式 */
.invite-records {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.records-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.records-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.view-all {
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-all-text {
  font-size: 14px;
  color: #E91E63;
}

.view-all svg {
  color: #E91E63;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
}

.record-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.record-info {
  flex: 1;
}

.record-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.record-time {
  display: block;
  font-size: 12px;
  color: #666;
}

.record-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.record-status.success {
  background: #E8F5E8;
  color: #4CAF50;
}

.record-status.pending {
  background: #FFF3E0;
  color: #FF9800;
}

.record-status.failed {
  background: #FFEBEE;
  color: #F44336;
}

.record-reward {
  margin-left: 8px;
}

.reward-text {
  font-size: 12px;
  color: #E91E63;
  font-weight: 600;
}

/* 邀请规则样式 */
.invite-rules {
  margin: 0 16px 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
}

.rules-header {
  margin-bottom: 12px;
}

.rules-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.rules-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rule-item {
  padding: 4px 0;
}

.rule-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 邀请好友样式结束 */
</style>
