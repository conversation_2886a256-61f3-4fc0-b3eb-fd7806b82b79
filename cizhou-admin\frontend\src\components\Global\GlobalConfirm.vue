<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    align-center
    class="global-confirm-dialog"
  >
    <div class="confirm-content">
      <div class="confirm-icon">
        <el-icon :size="48" :color="iconColor">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      <div class="confirm-message">
        {{ message }}
      </div>
      <div v-if="detail" class="confirm-detail">
        {{ detail }}
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="showCancel"
          @click="handleCancel"
          :loading="loading"
        >
          {{ cancelText }}
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="loading"
        >
          {{ confirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  WarningFilled, 
  InfoFilled, 
  SuccessFilled, 
  CircleCloseFilled 
} from '@element-plus/icons-vue'

interface ConfirmOptions {
  title?: string
  message: string
  detail?: string
  type?: 'warning' | 'info' | 'success' | 'error'
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
  width?: string
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
}

// 状态
const visible = ref(false)
const loading = ref(false)
const title = ref('确认')
const message = ref('')
const detail = ref('')
const type = ref<'warning' | 'info' | 'success' | 'error'>('warning')
const confirmText = ref('确定')
const cancelText = ref('取消')
const showCancel = ref(true)
const width = ref('400px')

let onConfirm: (() => void | Promise<void>) | undefined
let onCancel: (() => void) | undefined

// 计算属性
const iconComponent = computed(() => {
  const iconMap = {
    warning: WarningFilled,
    info: InfoFilled,
    success: SuccessFilled,
    error: CircleCloseFilled
  }
  return iconMap[type.value]
})

const iconColor = computed(() => {
  const colorMap = {
    warning: 'var(--el-color-warning)',
    info: 'var(--el-color-info)',
    success: 'var(--el-color-success)',
    error: 'var(--el-color-error)'
  }
  return colorMap[type.value]
})

// 方法
const show = (options: ConfirmOptions) => {
  title.value = options.title || '确认'
  message.value = options.message
  detail.value = options.detail || ''
  type.value = options.type || 'warning'
  confirmText.value = options.confirmText || '确定'
  cancelText.value = options.cancelText || '取消'
  showCancel.value = options.showCancel !== false
  width.value = options.width || '400px'
  
  onConfirm = options.onConfirm
  onCancel = options.onCancel
  
  visible.value = true
}

const hide = () => {
  visible.value = false
  loading.value = false
}

const handleConfirm = async () => {
  if (onConfirm) {
    try {
      loading.value = true
      await onConfirm()
      hide()
    } catch (error) {
      console.error('确认操作失败:', error)
      loading.value = false
    }
  } else {
    hide()
  }
}

const handleCancel = () => {
  if (onCancel) {
    onCancel()
  }
  hide()
}

// 暴露方法给外部使用
defineExpose({
  show,
  hide
})

// 全局确认对话框实例
const globalConfirm = {
  show,
  hide,
  warning: (options: Omit<ConfirmOptions, 'type'>) => show({ ...options, type: 'warning' }),
  info: (options: Omit<ConfirmOptions, 'type'>) => show({ ...options, type: 'info' }),
  success: (options: Omit<ConfirmOptions, 'type'>) => show({ ...options, type: 'success' }),
  error: (options: Omit<ConfirmOptions, 'type'>) => show({ ...options, type: 'error' })
}

// 挂载到全局
if (typeof window !== 'undefined') {
  window.$confirm = globalConfirm
}
</script>

<style lang="scss" scoped>
.global-confirm-dialog {
  .confirm-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px 0;
  }

  .confirm-icon {
    margin-bottom: 16px;
  }

  .confirm-message {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
    line-height: 1.5;
  }

  .confirm-detail {
    font-size: 14px;
    color: var(--el-text-color-regular);
    line-height: 1.4;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: 0 auto;
  }
}
</style>
