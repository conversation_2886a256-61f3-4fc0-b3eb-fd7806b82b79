"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Math) {
  (GroupBuyCard + FlashSaleCard + CouponCard + DiscountCard + ActivityCard)();
}
const ActivityCard = () => "./ActivityCard.js";
const GroupBuyCard = () => "./GroupBuyCard.js";
const FlashSaleCard = () => "./FlashSaleCard/index.js";
const CouponCard = () => "./CouponCard/index.js";
const DiscountCard = () => "./DiscountCard/index.js";
const _sfc_main = {
  __name: "ActivityCardFactory",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  emits: ["navigate", "favorite", "action"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const cardType = common_vendor.computed(() => {
      const typeMap = {
        "groupBuy": "GroupBuyCard",
        "flashSale": "FlashSaleCard",
        "coupon": "CouponCard",
        "discount": "DiscountCard"
      };
      return typeMap[props.item.type] || "ActivityCard";
    });
    function navigateToDetail() {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/components/activity/ActivityCardFactory.vue:69", "点击活动卡片，准备跳转到详情页", props.item.id, props.item.type);
      emit("navigate", {
        id: props.item.id,
        type: props.item.type
      });
    }
    function handleFavorite(id) {
      emit("favorite", id);
    }
    function handleAction(data) {
      emit("action", data);
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: cardType.value === "GroupBuyCard"
      }, cardType.value === "GroupBuyCard" ? {
        b: common_vendor.o(handleFavorite),
        c: common_vendor.o(handleAction),
        d: common_vendor.p({
          item: __props.item
        })
      } : cardType.value === "FlashSaleCard" ? {
        f: common_vendor.o(handleFavorite),
        g: common_vendor.o(handleAction),
        h: common_vendor.p({
          item: __props.item
        })
      } : cardType.value === "CouponCard" ? {
        j: common_vendor.o(handleFavorite),
        k: common_vendor.o(handleAction),
        l: common_vendor.p({
          item: __props.item
        })
      } : cardType.value === "DiscountCard" ? {
        n: common_vendor.o(handleFavorite),
        o: common_vendor.o(handleAction),
        p: common_vendor.p({
          item: __props.item
        })
      } : {
        q: common_vendor.o(handleFavorite),
        r: common_vendor.o(handleAction),
        s: common_vendor.p({
          item: __props.item
        })
      }, {
        e: cardType.value === "FlashSaleCard",
        i: cardType.value === "CouponCard",
        m: cardType.value === "DiscountCard",
        t: common_vendor.o(navigateToDetail)
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-da1d1997"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/activity/ActivityCardFactory.js.map
