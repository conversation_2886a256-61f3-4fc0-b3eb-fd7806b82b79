// 用户认证模拟数据
export const authData = {
  token: 'mock-token-12345',
  refreshToken: 'mock-refresh-token-12345',
  expiry: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7天后过期
};

// 用户登录的API函数
export const login = (phone, code) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (phone && code) {
        resolve({
          success: true,
          token: 'mock-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
          expiry: Date.now() + 7 * 24 * 60 * 60 * 1000,
          userInfo: {
            id: 'user-001',
            nickname: '磁州居民',
            avatar: '/static/images/tabbar/user-blue.png',
            phone: phone
          }
        });
      } else {
        resolve({
          success: false,
          message: '手机号或验证码错误'
        });
      }
    }, 500);
  });
};

// 用户注册的API函数
export const register = (phone, code, password) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (phone && code) {
        resolve({
          success: true,
          token: 'mock-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
          expiry: Date.now() + 7 * 24 * 60 * 60 * 1000,
          userInfo: {
            id: 'user-' + Date.now(),
            nickname: '磁州新用户',
            avatar: '/static/images/tabbar/user-blue.png',
            phone: phone
          }
        });
      } else {
        resolve({
          success: false,
          message: '注册信息不完整'
        });
      }
    }, 700);
  });
};

// 获取验证码的API函数
export const getVerificationCode = (phone) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (phone && phone.length === 11) {
        resolve({
          success: true,
          message: '验证码已发送',
          code: '1234' // 模拟环境下固定验证码
        });
      } else {
        resolve({
          success: false,
          message: '手机号格式错误'
        });
      }
    }, 300);
  });
};

// 用户登出的API函数
export const logout = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ 
        success: true,
        message: '退出登录成功'
      });
    }, 300);
  });
};

// 刷新token的API函数
export const refreshAuthToken = (refreshToken) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      if (refreshToken) {
        resolve({
          success: true,
          token: 'mock-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
          expiry: Date.now() + 7 * 24 * 60 * 60 * 1000
        });
      } else {
        resolve({
          success: false,
          message: 'refresh token无效'
        });
      }
    }, 400);
  });
}; 