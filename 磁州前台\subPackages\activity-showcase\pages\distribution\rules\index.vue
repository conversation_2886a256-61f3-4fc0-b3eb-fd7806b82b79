<template>
  <view class="rules-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg" :style="{
        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
      }"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">分销规则</view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y="true"
      :scroll-anchoring="true"
      :enhanced="true"
      :bounces="true"
      :show-scrollbar="false"
    >
      <!-- 规则标签页 -->
      <view class="rules-tabs">
        <view 
          v-for="(tab, index) in ruleTabs" 
          :key="index"
          class="rule-tab"
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          <text>{{ tab.name }}</text>
          <view class="tab-indicator" v-if="currentTab === index"></view>
        </view>
      </view>
      
      <!-- 规则内容 -->
      <view class="rules-content" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <!-- 基本规则 -->
        <view class="rule-section" v-if="currentTab === 0">
          <view class="section-title">
            <text>什么是分销</text>
          </view>
          <view class="section-content">
            <text class="paragraph">分销是指通过推广活动或商品获取佣金的营销模式。您可以通过分享活动、邀请好友参与等方式获得相应的佣金奖励。</text>
          </view>
          
          <view class="section-title">
            <text>如何成为分销员</text>
          </view>
          <view class="section-content">
            <text class="paragraph">1. 在"我的"页面点击"申请成为分销员"</text>
            <text class="paragraph">2. 填写基本信息并提交审核</text>
            <text class="paragraph">3. 等待平台审核通过（一般1-2个工作日）</text>
            <text class="paragraph">4. 审核通过后即可开始分销推广</text>
          </view>
          
          <view class="section-title">
            <text>分销推广方式</text>
          </view>
          <view class="section-content">
            <text class="paragraph">1. 活动分享：分享活动链接或海报给好友</text>
            <text class="paragraph">2. 商品推广：推广平台上的商品获取佣金</text>
            <text class="paragraph">3. 邀请注册：邀请好友注册并成为分销员</text>
          </view>
          
          <view class="section-title">
            <text>佣金结算规则</text>
          </view>
          <view class="section-content">
            <text class="paragraph">1. 订单完成后，佣金将在7天后自动结算</text>
            <text class="paragraph">2. 已结算的佣金可在"分销中心-收益管理"中查看</text>
            <text class="paragraph">3. 当可提现金额达到10元以上时，可申请提现</text>
            <text class="paragraph">4. 提现申请提交后，一般1-3个工作日到账</text>
          </view>
        </view>
        
        <!-- 等级规则 -->
        <view class="rule-section" v-if="currentTab === 1">
          <view class="section-title">
            <text>分销等级体系</text>
          </view>
          <view class="section-content">
            <text class="paragraph">我们设有三个分销等级：初级分销商、中级分销商和高级分销商。不同等级享有不同的佣金比例和权益。</text>
          </view>
          
          <view class="level-cards">
            <view class="level-card" :style="{
              background: 'linear-gradient(135deg, #5AC8FA 0%, #1C84FF 100%)',
              borderRadius: '20rpx',
              padding: '20rpx',
              marginBottom: '20rpx'
            }">
              <view class="level-header">
                <text class="level-name">初级分销商</text>
              </view>
              <view class="level-content">
                <view class="level-item">
                  <text class="item-label">佣金比例</text>
                  <text class="item-value">5%</text>
                </view>
                <view class="level-item">
                  <text class="item-label">升级条件</text>
                  <text class="item-value">累计推广30个订单或累计佣金达500元</text>
                </view>
                <view class="level-item">
                  <text class="item-label">特殊权益</text>
                  <text class="item-value">基础推广工具</text>
                </view>
              </view>
            </view>
            
            <view class="level-card" :style="{
              background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',
              borderRadius: '20rpx',
              padding: '20rpx',
              marginBottom: '20rpx'
            }">
              <view class="level-header">
                <text class="level-name">中级分销商</text>
              </view>
              <view class="level-content">
                <view class="level-item">
                  <text class="item-label">佣金比例</text>
                  <text class="item-value">8%</text>
                </view>
                <view class="level-item">
                  <text class="item-label">升级条件</text>
                  <text class="item-value">累计推广100个订单或累计佣金达2000元</text>
                </view>
                <view class="level-item">
                  <text class="item-label">特殊权益</text>
                  <text class="item-value">高级推广工具、团队管理功能</text>
                </view>
              </view>
            </view>
            
            <view class="level-card" :style="{
              background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
              borderRadius: '20rpx',
              padding: '20rpx'
            }">
              <view class="level-header">
                <text class="level-name">高级分销商</text>
              </view>
              <view class="level-content">
                <view class="level-item">
                  <text class="item-label">佣金比例</text>
                  <text class="item-value">12%</text>
                </view>
                <view class="level-item">
                  <text class="item-label">升级条件</text>
                  <text class="item-value">累计推广300个订单或累计佣金达5000元</text>
                </view>
                <view class="level-item">
                  <text class="item-label">特殊权益</text>
                  <text class="item-value">专属推广工具、优先推广位、专属客服</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="section-title">
            <text>等级保持规则</text>
          </view>
          <view class="section-content">
            <text class="paragraph">1. 每个自然月需至少有1笔有效推广订单</text>
            <text class="paragraph">2. 连续3个月无推广订单，将自动降级一个等级</text>
            <text class="paragraph">3. 降级后需重新满足升级条件才能恢复等级</text>
          </view>
        </view>
        
        <!-- 佣金规则 -->
        <view class="rule-section" v-if="currentTab === 2">
          <view class="section-title">
            <text>佣金计算方式</text>
          </view>
          <view class="section-content">
            <text class="paragraph">佣金 = 商品实付金额 × 佣金比例</text>
            <text class="paragraph">注：不同等级的分销员享有不同的佣金比例，具体请参考等级规则。</text>
          </view>
          
          <view class="section-title">
            <text>佣金类型</text>
          </view>
          <view class="section-content">
            <view class="commission-type">
              <view class="type-header">
                <text class="type-name">一级佣金</text>
              </view>
              <text class="type-desc">直接推广获得的佣金，按照您的等级对应的佣金比例计算。</text>
            </view>
            
            <view class="commission-type">
              <view class="type-header">
                <text class="type-name">二级佣金</text>
              </view>
              <text class="type-desc">您直接发展的下级分销员推广获得的佣金的20%。</text>
            </view>
            
            <view class="commission-type">
              <view class="type-header">
                <text class="type-name">团队佣金</text>
              </view>
              <text class="type-desc">当您的团队规模达到一定程度时，可获得额外的团队佣金奖励。</text>
            </view>
          </view>
          
          <view class="section-title">
            <text>佣金结算周期</text>
          </view>
          <view class="section-content">
            <text class="paragraph">1. 订单完成后进入待结算状态</text>
            <text class="paragraph">2. 订单完成后7天无售后问题，佣金自动结算</text>
            <text class="paragraph">3. 已结算佣金可在"分销中心-收益管理"中查看</text>
          </view>
          
          <view class="section-title">
            <text>佣金提现</text>
          </view>
          <view class="section-content">
            <text class="paragraph">1. 最低提现金额：10元</text>
            <text class="paragraph">2. 提现手续费：1%（最低1元）</text>
            <text class="paragraph">3. 提现方式：微信零钱、支付宝、银行卡</text>
            <text class="paragraph">4. 提现到账时间：1-3个工作日</text>
          </view>
        </view>
        
        <!-- 违规规则 -->
        <view class="rule-section" v-if="currentTab === 3">
          <view class="section-title">
            <text>违规行为定义</text>
          </view>
          <view class="section-content">
            <text class="paragraph">以下行为将被视为违规：</text>
            <text class="paragraph">1. 虚假交易：自己购买自己推广的商品</text>
            <text class="paragraph">2. 恶意刷单：通过非正常渠道大量下单</text>
            <text class="paragraph">3. 虚假宣传：夸大或虚构商品功效、活动内容</text>
            <text class="paragraph">4. 侵权行为：未经授权使用他人图片、文字等</text>
            <text class="paragraph">5. 不当引流：在其他平台引流至本平台</text>
          </view>
          
          <view class="section-title">
            <text>违规处罚措施</text>
          </view>
          <view class="section-content">
            <view class="violation-level">
              <view class="level-header">
                <text class="level-name">轻度违规</text>
              </view>
              <text class="level-desc">首次违规，将收到警告并冻结相关订单佣金。</text>
            </view>
            
            <view class="violation-level">
              <view class="level-header">
                <text class="level-name">中度违规</text>
              </view>
              <text class="level-desc">再次违规或情节较重，将冻结账户7-15天，并扣除违规订单佣金。</text>
            </view>
            
            <view class="violation-level">
              <view class="level-header">
                <text class="level-name">严重违规</text>
              </view>
              <text class="level-desc">多次违规或情节严重，将永久封禁分销资格，并扣除全部未结算佣金。</text>
            </view>
          </view>
          
          <view class="section-title">
            <text>申诉流程</text>
          </view>
          <view class="section-content">
            <text class="paragraph">如您认为被误判为违规，可按以下流程申诉：</text>
            <text class="paragraph">1. 在"分销中心-帮助与客服"中提交申诉</text>
            <text class="paragraph">2. 提供相关证据材料</text>
            <text class="paragraph">3. 客服将在3个工作日内处理并回复</text>
            <text class="paragraph">4. 申诉成功后，将恢复相关权益和佣金</text>
          </view>
        </view>
      </view>
      
      <!-- 常见问题 -->
      <view class="faq-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-title">常见问题</view>
        
        <view class="faq-list">
          <view 
            v-for="(faq, index) in faqs" 
            :key="index"
            class="faq-item"
            :class="{ expanded: faq.expanded }"
            @click="toggleFaq(index)"
          >
            <view class="faq-question">
              <text>{{ faq.question }}</text>
              <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M6 9l6 6 6-6" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            <view class="faq-answer" v-if="faq.expanded">
              <text>{{ faq.answer }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 联系客服 -->
      <view class="contact-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }">
        <text class="contact-title">还有其他问题？</text>
        <view class="contact-btn" @click="contactCustomerService" :style="{
          background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)'
        }">
          <text>联系客服</text>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 规则标签页
const currentTab = ref(0);
const ruleTabs = ref([
  { name: '基本规则' },
  { name: '等级规则' },
  { name: '佣金规则' },
  { name: '违规规则' }
]);

// 常见问题
const faqs = ref([
  { 
    question: '如何查看我的分销收益？',
    answer: '您可以在"分销中心-收益管理"中查看您的分销收益，包括累计收益、待结算收益和可提现金额等信息。',
    expanded: false
  },
  { 
    question: '佣金什么时候可以提现？',
    answer: '订单完成后需要等待7天的售后期，无售后问题后佣金自动结算，结算后即可提现。提现金额需满足10元以上。',
    expanded: false
  },
  { 
    question: '如何提高我的分销等级？',
    answer: '您可以通过增加推广订单数量或累计佣金金额来提升分销等级。具体升级条件请参考"等级规则"。',
    expanded: false
  },
  { 
    question: '分销佣金会过期吗？',
    answer: '已结算的佣金不会过期，您可以随时申请提现。但如果您的账户被发现存在违规行为，平台有权冻结或扣除相关佣金。',
    expanded: false
  },
  { 
    question: '如何获取推广海报？',
    answer: '您可以在"分销中心-推广海报"中获取各种活动和商品的推广海报，选择您喜欢的样式后可直接分享给好友。',
    expanded: false
  }
]);

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 切换标签页
const switchTab = (index) => {
  currentTab.value = index;
};

// 展开/收起FAQ
const toggleFaq = (index) => {
  faqs.value[index].expanded = !faqs.value[index].expanded;
};

// 联系客服
const contactCustomerService = () => {
  uni.showToast({
    title: '正在连接客服...',
    icon: 'none'
  });
};

// 页面加载
onMounted(() => {
  // 初始化数据
});
</script>

<style lang="scss" scoped>
.rules-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F8F8FA;
  position: relative;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  
  .navbar-bg {
    height: 180rpx;
    width: 100%;
  }
  
  .navbar-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height);
    
    .back-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .navbar-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #FFFFFF;
    }
  }
}

.content-scroll {
  flex: 1;
  padding: 30rpx;
  padding-top: calc(180rpx + 30rpx);
}

.rules-tabs {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 35px;
  margin-bottom: 30rpx;
  box-shadow: 0 8px 20px rgba(172,57,255,0.15);
  overflow: hidden;
  
  .rule-tab {
    flex: 1;
    height: 80rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    
    text {
      font-size: 28rpx;
      color: #666666;
    }
    
    .tab-indicator {
      position: absolute;
      bottom: 0;
      width: 40rpx;
      height: 4rpx;
      background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);
      border-radius: 2rpx;
    }
    
    &.active {
      text {
        color: #AC39FF;
        font-weight: 600;
      }
    }
  }
}

.rule-section {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .section-title {
    margin-bottom: 20rpx;
    
    text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }
  
  .section-content {
    margin-bottom: 30rpx;
    
    .paragraph {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.6;
      margin-bottom: 16rpx;
      display: block;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.level-cards {
  .level-card {
    color: #FFFFFF;
    
    .level-header {
      margin-bottom: 20rpx;
      
      .level-name {
        font-size: 32rpx;
        font-weight: 600;
      }
    }
    
    .level-content {
      .level-item {
        margin-bottom: 10rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .item-label {
          font-size: 26rpx;
          opacity: 0.8;
          margin-right: 10rpx;
        }
        
        .item-value {
          font-size: 26rpx;
        }
      }
    }
  }
}

.commission-type, .violation-level {
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .type-header, .level-header {
    margin-bottom: 10rpx;
    
    .type-name, .level-name {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }
  }
  
  .type-desc, .level-desc {
    font-size: 26rpx;
    color: #666666;
    line-height: 1.5;
  }
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
}

.faq-list {
  .faq-item {
    border-bottom: 1px solid #EEEEEE;
    padding: 20rpx 0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .faq-question {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      text {
        font-size: 28rpx;
        color: #333333;
        font-weight: 500;
      }
      
      .arrow-icon {
        transition: transform 0.3s;
      }
    }
    
    .faq-answer {
      margin-top: 16rpx;
      padding: 10rpx 0;
      
      text {
        font-size: 26rpx;
        color: #666666;
        line-height: 1.5;
      }
    }
    
    &.expanded {
      .faq-question {
        .arrow-icon {
          transform: rotate(180deg);
        }
      }
    }
  }
}

.contact-card {
  .contact-title {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 20rpx;
  }
  
  .contact-btn {
    padding: 16rpx 60rpx;
    border-radius: 30rpx;
    
    text {
      font-size: 28rpx;
      color: #FFFFFF;
    }
  }
}

.safe-area-bottom {
  height: 50rpx;
}

/* 适配小屏幕手机 */
@media screen and (max-width: 375px) {
  .rules-tabs {
    .rule-tab {
      text {
        font-size: 24rpx;
      }
    }
  }
  
  .rule-section {
    .section-title {
      text {
        font-size: 28rpx;
      }
    }
    
    .section-content {
      .paragraph {
        font-size: 24rpx;
      }
    }
  }
}
</style> 