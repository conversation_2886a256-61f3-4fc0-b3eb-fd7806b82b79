/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 页面容器 */
.flash-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}
.navbar-left {
  width: 40px;
}
.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
}
.title-text {
  font-size: 18px;
  font-weight: 600;
}
.navbar-right {
  width: 40px;
  display: flex;
  justify-content: flex-end;
}
.more-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content-area {
  flex: 1;
  box-sizing: border-box;
  height: calc(100vh - 80px - 60px);
}

/* 商品信息区域 */
.product-section {
  background: #FFFFFF;
  margin-bottom: 12px;
}
.product-swiper {
  width: 100%;
  height: 300px;
}
.product-image {
  width: 100%;
  height: 100%;
}
.product-info {
  padding: 16px;
  position: relative;
}
.status-tag {
  position: absolute;
  top: -20px;
  right: 16px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  color: white;
  z-index: 10;
}
.status-active {
  background: #34C759;
}
.status-upcoming {
  background: #007AFF;
}
.status-ended {
  background: #8E8E93;
}
.product-name {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12px;
  line-height: 1.4;
}
.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.price-now {
  font-size: 24px;
  font-weight: 700;
  color: #FF3B30;
  margin-right: 8px;
}
.price-original {
  font-size: 16px;
  color: #999999;
  text-decoration: line-through;
  margin-right: 8px;
}
.discount-tag {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}
.countdown-section {
  background: #FFF8F0;
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
}
.countdown-label {
  font-size: 14px;
  color: #FF7600;
  margin-bottom: 8px;
}
.countdown-timer {
  display: flex;
  align-items: center;
  justify-content: center;
}
.time-block {
  background: #FF7600;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  min-width: 32px;
  text-align: center;
}
.time-separator {
  margin: 0 4px;
  color: #FF7600;
  font-weight: 600;
}

/* 销售数据区域 */
.sales-section {
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}
.sales-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-title {
  font-size: 17px;
  font-weight: 600;
  color: #333333;
}
.refresh-button {
  display: flex;
  align-items: center;
  color: #FF7600;
}
.refresh-text {
  font-size: 14px;
  margin-left: 4px;
}
.sales-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}
.stat-item {
  text-align: center;
  flex: 1;
}
.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
}
.stat-label {
  font-size: 12px;
  color: #999999;
}
.progress-bar {
  height: 8px;
  background: #F0F0F0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}
.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #FF7600, #FF3C00);
  border-radius: 4px;
}
.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666666;
}

/* 活动详情区域 */
.detail-section {
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}
.section-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.detail-items {
  border-radius: 8px;
  overflow: hidden;
}
.detail-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}
.detail-item:last-child {
  border-bottom: none;
}
.item-label {
  width: 80px;
  font-size: 14px;
  color: #666666;
}
.item-value {
  flex: 1;
  font-size: 14px;
  color: #333333;
  line-height: 1.5;
}

/* 商品详情区域 */
.product-detail-section {
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}
.product-description {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 16px;
}
.product-images {
  width: 100%;
}
.detail-image {
  width: 100%;
  margin-bottom: 12px;
}

/* 订单记录区域 */
.orders-section {
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}
.view-all {
  display: flex;
  align-items: center;
  color: #666666;
}
.view-all-text {
  font-size: 14px;
}
.arrow-icon {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #666666;
  transform: rotate(-90deg);
  margin-left: 4px;
}
.orders-list {
  margin-top: 8px;
}
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}
.order-item:last-child {
  border-bottom: none;
}
.order-user {
  display: flex;
  align-items: center;
}
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  margin-right: 8px;
}
.user-name {
  font-size: 14px;
  color: #333333;
}
.order-info {
  text-align: right;
}
.order-quantity {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
}
.order-time {
  font-size: 12px;
  color: #999999;
}
.empty-orders {
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}
.empty-text {
  font-size: 14px;
  color: #999999;
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 10px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}
.action-buttons {
  display: flex;
  justify-content: space-around;
}
.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}
.button-text {
  font-size: 12px;
  margin-top: 4px;
}
.action-button.edit {
  color: #5E5CE6;
}
.action-button.share {
  color: #34C759;
}
.action-button.pause {
  color: #FF9500;
}
.action-button.activate {
  color: #34C759;
}
.action-button.delete {
  color: #FF3B30;
}

/* 更多选项弹窗 */
.more-options-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.popup-content {
  position: absolute;
  top: 80px;
  right: 16px;
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  width: 160px;
}
.popup-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  color: #333333;
}
.popup-option:active {
  background: #F5F5F5;
}
.popup-option svg {
  margin-right: 8px;
}
@media screen and (min-width: 768px) {
.product-swiper {
    height: 400px;
}
.action-buttons {
    max-width: 600px;
    margin: 0 auto;
}
}