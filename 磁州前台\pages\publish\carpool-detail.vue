<template>
  <view class="detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
          </view>
      <view class="navbar-title">拼车详情</view>
      <view class="navbar-right" @click="showActionSheet">
        <image src="/static/images/tabbar/more-vertical.png" class="more-icon" style="filter: brightness(0) invert(1);"></image>
            </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view class="detail-content" scroll-y :style="{ paddingTop: navigationBarHeight + 'px' }">
      <!-- 标签 -->
      <view class="header-tag-container">
        <view class="header-tag" :class="tripData.type">
          <text>{{typeText}}</text>
          <view class="verified-tag" v-if="tripData.isVerified">已认证</view>
          </view>
        </view>
        
      <!-- 信息头部 -->
      <view class="detail-header">
        <!-- 行程信息 -->
        <view class="route-card">
          <view class="route-points">
            <view class="route-point">
              <view class="point-dot start"></view>
            <view class="point-info">
              <text class="point-name">{{tripData.startPoint}}</text>
                <text class="point-address" v-if="tripData.startAddress">（{{tripData.startAddress}}）</text>
            </view>
          </view>
            
            <view class="route-divider">
              <view class="divider-line"></view>
              <view class="divider-info">
                <text class="divider-text">约{{tripData.distance}}公里</text>
              </view>
            </view>
            
            <view class="route-point">
              <view class="point-dot end"></view>
            <view class="point-info">
              <text class="point-name">{{tripData.endPoint}}</text>
                <text class="point-address" v-if="tripData.endAddress">（{{tripData.endAddress}}）</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 行程详情 -->
        <view class="trip-details-card">
          <view class="trip-details-header">
            <text class="section-title">行程详情</text>
          </view>
          
          <view class="trip-details-content">
            <view class="trip-details-item">
              <text class="trip-details-label">上车地点</text>
              <text class="trip-details-value">{{tripData.startPoint}}</text>
          </view>
            <view class="trip-details-item">
              <text class="trip-details-label">下车地点</text>
              <text class="trip-details-value">{{tripData.endPoint}}</text>
          </view>
            <view class="trip-details-item">
              <text class="trip-details-label">行程距离</text>
              <text class="trip-details-value">{{tripData.distance}}公里</text>
          </view>
            <view class="trip-details-item">
              <text class="trip-details-label">预计用时</text>
              <text class="trip-details-value">约{{tripData.duration}}小时</text>
            </view>
            <view class="trip-details-item" v-if="tripData.tripNotes">
              <text class="trip-details-label">备注</text>
              <text class="trip-details-value">{{tripData.tripNotes}}</text>
            </view>
        </view>
      </view>
      
        <!-- 出发时间 -->
        <view class="info-card">
          <view class="info-item-new">
            <text class="info-label-new">出发时间</text>
            <text class="info-value-new">{{tripData.date}} {{tripData.startTime}}</text>
          </view>
          
          <view class="info-item-new" v-if="tripData.type === 'people-to-car'">
            <text class="info-label-new">乘车人数</text>
            <text class="info-value-new">{{tripData.passengers}}人</text>
        </view>
          
          <view class="info-item-new" v-if="tripData.type === 'car-to-people'">
            <text class="info-label-new">空余座位</text>
            <text class="info-value-new">{{tripData.remainingSeats}}个</text>
      </view>
      
          <view class="info-item-new" v-if="tripData.price">
            <text class="info-label-new">参考价格</text>
            <text class="info-value-new price-value">¥{{tripData.price}}</text>
            </view>
          </view>
        </view>
      
      <!-- 用户信息 -->
      <view class="user-card">
        <view class="user-header">
          <text class="section-title">发布人信息</text>
      </view>
      
        <view class="user-info">
          <image class="user-avatar" :src="tripData.driver.avatar" mode="aspectFill"></image>
          <view class="user-details">
            <view class="user-name-row">
              <text class="user-name">{{tripData.driver.name}}</text>
              <view class="user-badges">
                <view class="user-badge verified" v-if="tripData.driver.isVerified">已认证</view>
                <view class="user-badge premium" v-if="tripData.driver.isPremium">置顶</view>
          </view>
            </view>
            <text class="user-meta">{{formatTime(tripData.publishTime)}} 发布</text>
          </view>
          </view>
        
        <!-- 司机评分 - 仅在车找人类型显示 -->
        <view class="driver-rating" v-if="tripData.type === 'car-to-people'">
          <view class="rating-header">
            <text class="rating-title">司机评分</text>
            <text class="rating-count">{{tripData.driver.ratingCount || 0}}人评价</text>
        </view>
          <view class="rating-stars">
            <view class="star-container">
              <view class="star-fill" :style="{ width: (tripData.driver.rating || 0) * 20 + '%' }"></view>
              <view class="star-bg"></view>
          </view>
            <text class="rating-value">{{tripData.driver.rating || 0}}</text>
          </view>
          <view class="rating-tags" v-if="tripData.driver.ratingTags && tripData.driver.ratingTags.length > 0">
            <view class="rating-tag" v-for="(tag, index) in tripData.driver.ratingTags" :key="index">
              {{tag}}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 车辆信息 -->
      <view class="car-card" v-if="tripData.type && tripData.type.includes('car-to')">
        <view class="car-header">
          <text class="section-title">车辆信息</text>
        </view>
        
        <view class="car-info">
          <view class="car-item">
            <text class="car-label">车型</text>
            <text class="car-value">{{tripData.carType}}</text>
          </view>
          <view class="car-item">
            <text class="car-label">颜色</text>
            <text class="car-value">{{tripData.carColor || '未提供'}}</text>
          </view>
          <view class="car-item">
            <text class="car-label">车牌</text>
            <text class="car-value">{{tripData.carPlate || '未提供'}}</text>
          </view>
        </view>
      </view>
      
      <!-- 补充说明 -->
      <view class="remark-card" v-if="tripData.remark">
        <view class="remark-header">
          <text class="section-title">补充说明</text>
        </view>
        
        <view class="remark-content">
          <text>{{tripData.remark}}</text>
              </view>
              </view>
      
      <!-- 免责声明 -->
      <view class="disclaimer-card">
        <view class="disclaimer-header">
          <text class="section-title">免责声明</text>
            </view>
        <view class="disclaimer-content">
          <view class="disclaimer-icon">
            <image src="/static/images/tabbar/声明.png" mode="aspectFit"></image>
          </view>
          <text class="disclaimer-text">本平台仅提供信息对接服务，不对拼车双方的行为提供任何担保，不承担任何法律责任。请用户自行甄别信息真实性，注意出行安全。使用本平台即表示您已同意以上条款。</text>
          </view>
        </view>
        
      <!-- 添加举报卡片 -->
      <report-card :content-id="tripData.id" content-type="carpool"></report-card>
        
      <!-- 发布时间 -->
      <view class="publish-time-card">
        <view class="publish-time">
          <text>发布时间：{{formatTime(tripData.publishTime)}}</text>
          <text>信息有效期至：{{tripData.expiryTime || '未设置'}}</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-group">
        <button class="action-btn share" open-type="share">
          <image src="/static/images/tabbar/a分享.png" mode="aspectFit"></image>
          <text>转发</text>
        </button>
        <button class="action-btn favorite" @click="toggleCollect">
          <image :src="isCollected ? '/static/images/tabbar/a收藏选中.png' : '/static/images/tabbar/a收藏.png'" mode="aspectFit"></image>
          <text>{{isCollected ? '已收藏' : '收藏'}}</text>
        </button>
        <button class="action-btn rate" @click="rateDriver">
          <image src="/static/images/tabbar/a评价.png" mode="aspectFit"></image>
          <text>评价</text>
        </button>
        <button class="action-btn message" @click="openChat">
          <image src="/static/images/tabbar/a私信.png" mode="aspectFit"></image>
          <text>私信</text>
        </button>
      </view>
      <view class="call-btn-container">
        <button class="call-btn" @click="callPhone">
          <image src="/static/images/tabbar/电话.png" mode="aspectFit" style="filter: brightness(0) invert(1);"></image>
          <text>联系司机</text>
      </button>
      </view>
        </view>
    
    <!-- 安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import ReportCard from '@/components/ReportCard.vue'

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);
const statusBarHeight = ref(20);
const navigationBarHeight = ref(60);

// 计算状态栏和导航栏高度
const getSystemInfo = () => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 20;
      // 导航栏高度：状态栏 + 标题栏(44px)
      navigationBarHeight.value = statusBarHeight.value + 44;
    }
  });
};

const tripData = ref({
  id: 'trip12345',
  type: 'car-to-people', // 'car-to-people' 或 'people-to-car'
  title: '磁县→邯郸',
  price: '50',
  tags: ['准点发车', '舒适车型', '可带行李'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  startPoint: '磁县汽车站',
  startAddress: '河北省邯郸市磁县磁州镇汽车站',
  endPoint: '邯郸东站',
  endAddress: '河北省邯郸市丛台区邯郸东站',
  startTime: '08:00',
  endTime: '09:30',
  date: '2024-03-20',
  distance: '45',
  duration: '1.5',
  remainingSeats: 3,
  carType: '舒适型轿车',
  carColor: '白色',
  carPlate: '冀D·12345',
  tripNotes: '可提前预约，支持改签',
  isVerified: true,
  driver: {
    name: '王师傅',
    avatar: '/static/images/avatar.png',
    type: '专业司机',
    isVerified: true,
    isPremium: true,
    rating: 4.8,
    ratingCount: 56,
    ratingTags: ['准时', '礼貌', '车内整洁'],
    trips: 128,
    years: 5
  },
  contact: {
    name: '王师傅',
    phone: '138****1234'
  },
  expiryTime: '2024-03-25'
});

// 计算属性
const typeText = computed(() => {
  if (tripData.value.type === 'car-to-people') {
    return '车找人';
  } else if (tripData.value.type === 'people-to-car') {
    return '人找车';
  }
  return '拼车信息';
});

// 方法
const goBack = () => {
  uni.navigateBack();
};

const showActionSheet = () => {
  uni.showActionSheet({
    itemList: ['举报', '分享', '收藏'],
    success: function (res) {
      if (res.tapIndex === 0) {
        // 举报
    uni.showToast({
          title: '举报功能开发中',
          icon: 'none'
    });
      } else if (res.tapIndex === 1) {
        // 分享
  uni.showShareMenu({
          withShareTicket: true
        });
      } else if (res.tapIndex === 2) {
        // 收藏
        toggleCollect();
      }
    }
  });
};

const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  uni.showToast({
    title: isCollected.value ? '已收藏' : '已取消收藏',
    icon: 'none'
  });
};

const callPhone = () => {
  if (tripData.value.contact.phone) {
  uni.makePhoneCall({
      phoneNumber: tripData.value.contact.phone.replace(/\*+/g, '')
    });
  } else {
      uni.showToast({
      title: '电话号码不可用',
        icon: 'none'
      });
    }
};

const openChat = () => {
  uni.showToast({
    title: '私信功能开发中',
    icon: 'none'
  });
};

const rateDriver = () => {
  uni.showToast({
    title: '评价功能开发中',
    icon: 'none'
  });
};

// 页面加载
onMounted(() => {
  // 获取系统信息和状态栏高度
  getSystemInfo();
  
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  if (options.id) {
    // 这里应该根据ID从服务器获取拼车信息
    console.log('获取拼车信息，ID:', options.id);
    // fetchCarpoolInfo(options.id);
  }
});

// 分享功能
defineExpose({
  onShareAppMessage() {
    return {
      title: `${tripData.value.startPoint}→${tripData.value.endPoint} ${tripData.value.date} ${tripData.value.startTime}出发`,
      path: `/pages/publish/carpool-detail?id=${tripData.value.id}`
    };
  }
});
</script>

<style>
.detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left, .navbar-right {
  width: 32px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon, .more-icon {
  width: 24px;
  height: 24px;
}

.navbar-title {
  color: #FFFFFF;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 内容区域 */
.detail-content {
  flex: 1;
  box-sizing: border-box;
}

/* 标签 */
.header-tag-container {
  padding: 15rpx 30rpx 10rpx;
  margin-top: 0;
}

.header-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  color: #ffffff;
}

.car-to-people {
  background-color: #1989fa;
}

.people-to-car {
  background-color: #ff6b00;
}

.verified-tag {
  margin-left: 8px;
  padding: 2px 6px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  font-size: 12px;
}

/* 行程信息 */
.route-card {
  margin: 0 30rpx 30rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.route-points {
  position: relative;
}

.route-point {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.point-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
}

.start {
  background-color: #1989fa;
}

.end {
  background-color: #ff6b00;
}

.point-info {
  flex: 1;
}

.point-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.point-address {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.route-divider {
  padding-left: 6px;
  margin: 5px 0;
  height: 30px;
  position: relative;
}

.divider-line {
  position: absolute;
  left: 6px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #ddd;
}

.divider-info {
  margin-left: 20px;
}

.divider-text {
  font-size: 12px;
  color: #999;
}

/* 行程详情 */
.trip-details-card, .info-card, .user-card, .car-card, .remark-card, .disclaimer-card {
  margin: 0 30rpx 30rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.trip-details-content {
  margin-top: 10px;
}

.trip-details-item {
  display: flex;
  margin-bottom: 10px;
}

.trip-details-label {
  width: 80px;
  color: #666;
  font-size: 14px;
}

.trip-details-value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

/* 信息卡片 */
.info-item-new {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item-new:last-child {
  border-bottom: none;
}

.info-label-new {
  color: #666;
  font-size: 14px;
}

.info-value-new {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.price-value {
  color: #ff6b00;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
}

.user-details {
  flex: 1;
}

.user-name-row {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}

.user-badges {
  display: flex;
}

.user-badge {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  margin-right: 5px;
}

.verified {
  background-color: #1989fa;
  color: #fff;
}

.premium {
  background-color: #ff6b00;
  color: #fff;
}

.user-meta {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 司机评分 */
.driver-rating {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.rating-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.rating-count {
  font-size: 12px;
  color: #999;
}

.rating-stars {
  display: flex;
  align-items: center;
}

.star-container {
  position: relative;
  width: 100px;
  height: 20px;
  margin-right: 10px;
}

.star-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('../../../static/images/tabbar/star-bg.png') repeat-x;
  background-size: 20px 20px;
}

.star-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: url('../../../static/images/tabbar/star-fill.png') repeat-x;
  background-size: 20px 20px;
}

.rating-value {
  font-size: 16px;
  font-weight: 500;
  color: #ff6b00;
}

.rating-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.rating-tag {
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 车辆信息 */
.car-info {
  margin-top: 10px;
}

.car-item {
  display: flex;
  margin-bottom: 10px;
}

.car-label {
  width: 80px;
  color: #666;
  font-size: 14px;
}

.car-value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

/* 补充说明 */
.remark-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

/* 免责声明 */
.disclaimer-content {
  display: flex;
  align-items: flex-start;
}

.disclaimer-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.disclaimer-icon image {
  width: 100%;
  height: 100%;
}

.disclaimer-text {
  flex: 1;
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

/* 发布时间 */
.publish-time-card {
  margin: 0 30rpx 30rpx;
  padding: 10rpx 15rpx;
}

.publish-time {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.publish-time text {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  padding: 10px 15px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  z-index: 99;
}

.action-group {
  display: flex;
  flex: 1;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: transparent;
  flex: 1;
  height: 60px;
  line-height: 1;
}

.action-btn::after {
  border: none;
}

.action-btn image {
  width: 24px;
  height: 24px;
  margin-bottom: 5px;
}

.action-btn text {
  font-size: 12px;
  color: #666;
}

.call-btn-container {
  width: 120px;
  margin-left: 10px;
}

.call-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60px;
  background-color: #1989fa;
  color: #fff;
  border-radius: 4px;
  font-size: 16px;
  line-height: 1;
}

.call-btn::after {
  border: none;
}

.call-btn image {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom, 0);
  background-color: #fff;
}

.carpool-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}
</style> 