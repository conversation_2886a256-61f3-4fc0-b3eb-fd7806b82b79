{"version": 3, "file": "after-sale.js", "sources": ["subPackages/merchant-admin-order/pages/orders/after-sale.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tb3JkZXJccGFnZXNcb3JkZXJzXGFmdGVyLXNhbGUudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"after-sale-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">售后服务中心</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 售后服务Tab -->\r\n    <view class=\"tab-container\">\r\n      <view \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\"\r\n        class=\"tab-item\"\r\n        :class=\"{'active': currentTab === tab.value}\"\r\n        @click=\"switchTab(tab.value)\">\r\n        <text class=\"tab-name\">{{tab.name}}</text>\r\n        <text v-if=\"tab.count > 0\" class=\"tab-badge\">{{tab.count}}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 退款申请列表 -->\r\n    <view v-if=\"currentTab === 'refund'\" class=\"content-main\">\r\n      <view \r\n        v-for=\"(item, index) in refundList\" \r\n        :key=\"index\"\r\n        class=\"refund-item\"\r\n        @click=\"viewRefundDetail(item.id)\">\r\n        <view class=\"refund-header\">\r\n          <text class=\"refund-number\">退款编号: {{item.refundNo}}</text>\r\n          <text class=\"refund-status\" :class=\"'status-' + item.status\">{{getRefundStatusText(item.status)}}</text>\r\n        </view>\r\n        <view class=\"order-info\">\r\n          <text class=\"order-number\">订单号: {{item.orderNo}}</text>\r\n          <text class=\"refund-time\">申请时间: {{item.createTime}}</text>\r\n        </view>\r\n        <view class=\"refund-info\">\r\n          <view class=\"customer-info\">\r\n            <image class=\"customer-avatar\" :src=\"item.customerAvatar\" mode=\"aspectFill\"></image>\r\n            <text class=\"customer-name\">{{item.customerName}}</text>\r\n          </view>\r\n          <view class=\"refund-amount\">\r\n            <text class=\"label\">退款金额:</text>\r\n            <text class=\"amount\">¥{{item.amount}}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"refund-reason\">\r\n          <text class=\"label\">退款原因:</text>\r\n          <text class=\"reason\">{{item.reason}}</text>\r\n        </view>\r\n        <view class=\"action-buttons\">\r\n          <view v-if=\"item.status === 'pending'\" class=\"action-btn primary\" @click.stop=\"approveRefund(item.id)\">同意退款</view>\r\n          <view v-if=\"item.status === 'pending'\" class=\"action-btn reject\" @click.stop=\"rejectRefund(item.id)\">拒绝退款</view>\r\n          <view v-if=\"['approved', 'rejected'].includes(item.status)\" class=\"action-btn\" @click.stop=\"contactCustomer(item.customerId)\">联系客户</view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-if=\"refundList.length === 0\" class=\"empty-state\">\r\n        <view class=\"empty-icon\">📭</view>\r\n        <text class=\"empty-text\">暂无退款申请</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 投诉处理列表 -->\r\n    <view v-if=\"currentTab === 'complaint'\" class=\"content-main\">\r\n      <view \r\n        v-for=\"(item, index) in complaintList\" \r\n        :key=\"index\"\r\n        class=\"complaint-item\"\r\n        @click=\"viewComplaintDetail(item.id)\">\r\n        <view class=\"complaint-header\">\r\n          <text class=\"complaint-title\">{{item.title}}</text>\r\n          <text class=\"complaint-status\" :class=\"'status-' + item.status\">{{getComplaintStatusText(item.status)}}</text>\r\n        </view>\r\n        <view class=\"order-info\">\r\n          <text class=\"order-number\">订单号: {{item.orderNo}}</text>\r\n          <text class=\"complaint-time\">投诉时间: {{item.createTime}}</text>\r\n        </view>\r\n        <view class=\"complaint-content\">\r\n          <text class=\"complaint-text\">{{item.content}}</text>\r\n        </view>\r\n        <view class=\"action-buttons\">\r\n          <view v-if=\"item.status === 'pending'\" class=\"action-btn primary\" @click.stop=\"handleComplaint(item.id)\">处理投诉</view>\r\n          <view class=\"action-btn\" @click.stop=\"contactCustomer(item.customerId)\">联系客户</view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-if=\"complaintList.length === 0\" class=\"empty-state\">\r\n        <view class=\"empty-icon\">📭</view>\r\n        <text class=\"empty-text\">暂无客户投诉</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 评价管理列表 -->\r\n    <view v-if=\"currentTab === 'review'\" class=\"content-main\">\r\n      <view \r\n        v-for=\"(item, index) in reviewList\" \r\n        :key=\"index\"\r\n        class=\"review-item\">\r\n        <view class=\"review-header\">\r\n          <view class=\"customer-info\">\r\n            <image class=\"customer-avatar\" :src=\"item.customerAvatar\" mode=\"aspectFill\"></image>\r\n            <text class=\"customer-name\">{{item.customerName}}</text>\r\n          </view>\r\n          <view class=\"rating\">\r\n            <text v-for=\"i in 5\" :key=\"i\" class=\"star\" :class=\"{'filled': i <= item.rating}\">★</text>\r\n            <text class=\"rating-text\">{{item.rating}}.0</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"review-content\">\r\n          <text class=\"review-text\">{{item.content}}</text>\r\n          <view v-if=\"item.images && item.images.length > 0\" class=\"review-images\">\r\n            <image \r\n              v-for=\"(img, imgIndex) in item.images\" \r\n              :key=\"imgIndex\"\r\n              :src=\"img\"\r\n              mode=\"aspectFill\"\r\n              class=\"review-image\"\r\n              @click=\"previewImage(item.images, imgIndex)\"></image>\r\n          </view>\r\n        </view>\r\n        <view class=\"review-footer\">\r\n          <text class=\"review-time\">{{item.createTime}}</text>\r\n          <text class=\"order-number\">订单号: {{item.orderNo}}</text>\r\n        </view>\r\n        <view class=\"reply-section\">\r\n          <view v-if=\"item.reply\" class=\"merchant-reply\">\r\n            <text class=\"reply-label\">商家回复:</text>\r\n            <text class=\"reply-content\">{{item.reply}}</text>\r\n            <text class=\"reply-time\">{{item.replyTime}}</text>\r\n          </view>\r\n          <view v-else class=\"reply-form\">\r\n            <input \r\n              class=\"reply-input\" \r\n              placeholder=\"回复该评价...\" \r\n              v-model=\"replyContent[item.id]\"\r\n              @confirm=\"submitReply(item.id)\" />\r\n            <view class=\"reply-btn\" @click=\"submitReply(item.id)\">回复</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-if=\"reviewList.length === 0\" class=\"empty-state\">\r\n        <view class=\"empty-icon\">📭</view>\r\n        <text class=\"empty-text\">暂无客户评价</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      currentTab: 'refund', // refund, complaint, review\r\n      tabs: [\r\n        { name: '退款管理', value: 'refund', count: 2 },\r\n        { name: '投诉处理', value: 'complaint', count: 1 },\r\n        { name: '评价管理', value: 'review', count: 0 }\r\n      ],\r\n      refundList: [\r\n        {\r\n          id: '4001',\r\n          refundNo: 'RF20230510001',\r\n          orderNo: 'CZ20230501001',\r\n          status: 'pending', // pending, approved, rejected, completed\r\n          createTime: '2023-05-10 14:30',\r\n          customerName: '张三',\r\n          customerId: '10086',\r\n          customerAvatar: '/static/images/default-avatar.png',\r\n          amount: '128.00',\r\n          reason: '商品质量不符合预期，水果有部分已经变质。'\r\n        },\r\n        {\r\n          id: '4002',\r\n          refundNo: 'RF20230511002',\r\n          orderNo: 'CZ20230502003',\r\n          status: 'approved',\r\n          createTime: '2023-05-11 09:15',\r\n          customerName: '李四',\r\n          customerId: '10087',\r\n          customerAvatar: '/static/images/default-avatar.png',\r\n          amount: '89.90',\r\n          reason: '配送时间过长，蔬菜已经不新鲜。'\r\n        }\r\n      ],\r\n      complaintList: [\r\n        {\r\n          id: '5001',\r\n          title: '配送员服务态度差',\r\n          orderNo: 'CZ20230503005',\r\n          status: 'pending', // pending, processing, resolved\r\n          createTime: '2023-05-12 16:45',\r\n          customerId: '10088',\r\n          content: '配送员态度非常恶劣，送货上门后直接扔在门口就走了，敲门也不回应。'\r\n        }\r\n      ],\r\n      reviewList: [\r\n        {\r\n          id: '6001',\r\n          customerName: '王五',\r\n          customerAvatar: '/static/images/default-avatar.png',\r\n          rating: 4,\r\n          content: '水果非常新鲜，包装也很精美，就是价格稍微贵了点。下次还会购买。',\r\n          images: [\r\n            '/static/images/review-1.jpg',\r\n            '/static/images/review-2.jpg'\r\n          ],\r\n          createTime: '2023-05-13 10:20',\r\n          orderNo: 'CZ20230505008',\r\n          reply: null,\r\n          replyTime: null\r\n        },\r\n        {\r\n          id: '6002',\r\n          customerName: '赵六',\r\n          customerAvatar: '/static/images/default-avatar.png',\r\n          rating: 5,\r\n          content: '蔬菜很新鲜，配送速度也很快，非常满意！',\r\n          images: [],\r\n          createTime: '2023-05-14 15:30',\r\n          orderNo: 'CZ20230506010',\r\n          reply: '感谢您的支持，我们会继续努力提供优质服务！',\r\n          replyTime: '2023-05-14 16:05'\r\n        },\r\n        {\r\n          id: '6003',\r\n          customerName: '钱七',\r\n          customerAvatar: '/static/images/default-avatar.png',\r\n          rating: 2,\r\n          content: '送来的水果有几个已经坏了，很失望。',\r\n          images: [\r\n            '/static/images/review-3.jpg'\r\n          ],\r\n          createTime: '2023-05-15 09:40',\r\n          orderNo: 'CZ20230507012',\r\n          reply: null,\r\n          replyTime: null\r\n        }\r\n      ],\r\n      replyContent: {}\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    switchTab(tab) {\r\n      this.currentTab = tab;\r\n    },\r\n    viewRefundDetail(id) {\r\n      uni.showToast({\r\n        title: '查看退款详情功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    approveRefund(id) {\r\n      uni.showModal({\r\n        title: '确认退款',\r\n        content: '确认同意该退款申请？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 更新退款状态\r\n            const index = this.refundList.findIndex(item => item.id === id);\r\n            if (index !== -1) {\r\n              this.refundList[index].status = 'approved';\r\n              uni.showToast({\r\n                title: '已同意退款',\r\n                icon: 'success'\r\n              });\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    rejectRefund(id) {\r\n      uni.showModal({\r\n        title: '拒绝退款',\r\n        content: '确认拒绝该退款申请？请确保已与客户沟通。',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 更新退款状态\r\n            const index = this.refundList.findIndex(item => item.id === id);\r\n            if (index !== -1) {\r\n              this.refundList[index].status = 'rejected';\r\n              uni.showToast({\r\n                title: '已拒绝退款',\r\n                icon: 'success'\r\n              });\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    viewComplaintDetail(id) {\r\n      uni.showToast({\r\n        title: '查看投诉详情功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    handleComplaint(id) {\r\n      uni.showModal({\r\n        title: '处理投诉',\r\n        content: '确认开始处理该投诉？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 更新投诉状态\r\n            const index = this.complaintList.findIndex(item => item.id === id);\r\n            if (index !== -1) {\r\n              this.complaintList[index].status = 'processing';\r\n              uni.showToast({\r\n                title: '已开始处理投诉',\r\n                icon: 'success'\r\n              });\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    contactCustomer(customerId) {\r\n      uni.showActionSheet({\r\n        itemList: ['拨打电话', '发送消息'],\r\n        success: (res) => {\r\n          if (res.tapIndex === 0) {\r\n            // 拨打电话\r\n            uni.showToast({\r\n              title: '拨打电话功能开发中',\r\n              icon: 'none'\r\n            });\r\n          } else if (res.tapIndex === 1) {\r\n            // 发送消息\r\n            uni.showToast({\r\n              title: '消息功能开发中',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    previewImage(images, current) {\r\n      uni.previewImage({\r\n        urls: images,\r\n        current: images[current]\r\n      });\r\n    },\r\n    submitReply(reviewId) {\r\n      if (!this.replyContent[reviewId]) {\r\n        uni.showToast({\r\n          title: '请输入回复内容',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 更新评价回复\r\n      const index = this.reviewList.findIndex(item => item.id === reviewId);\r\n      if (index !== -1) {\r\n        this.reviewList[index].reply = this.replyContent[reviewId];\r\n        this.reviewList[index].replyTime = this.getCurrentTime();\r\n        this.replyContent[reviewId] = '';\r\n        \r\n        uni.showToast({\r\n          title: '回复成功',\r\n          icon: 'success'\r\n        });\r\n      }\r\n    },\r\n    getCurrentTime() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      const day = String(now.getDate()).padStart(2, '0');\r\n      const hour = String(now.getHours()).padStart(2, '0');\r\n      const minute = String(now.getMinutes()).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hour}:${minute}`;\r\n    },\r\n    getRefundStatusText(status) {\r\n      const texts = {\r\n        pending: '待处理',\r\n        approved: '已同意',\r\n        rejected: '已拒绝',\r\n        completed: '已完成'\r\n      };\r\n      return texts[status] || '未知状态';\r\n    },\r\n    getComplaintStatusText(status) {\r\n      const texts = {\r\n        pending: '待处理',\r\n        processing: '处理中',\r\n        resolved: '已解决'\r\n      };\r\n      return texts[status] || '未知状态';\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.after-sale-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 44px 16px 10px;\r\n  background: linear-gradient(135deg, #1677FF, #065DD2);\r\n  position: relative;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-back {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.tab-container {\r\n  display: flex;\r\n  background-color: #fff;\r\n  padding: 0 16px;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.05);\r\n}\r\n\r\n.tab-item {\r\n  padding: 16px 0;\r\n  margin-right: 24px;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #1677FF;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background-color: #1677FF;\r\n  border-radius: 3px 3px 0 0;\r\n}\r\n\r\n.tab-badge {\r\n  min-width: 16px;\r\n  height: 16px;\r\n  border-radius: 8px;\r\n  background-color: #ff4d4f;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 4px;\r\n  padding: 0 4px;\r\n}\r\n\r\n.content-main {\r\n  padding: 16px;\r\n}\r\n\r\n.refund-item, .complaint-item, .review-item {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 16px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.refund-header, .complaint-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.refund-number, .complaint-title {\r\n  font-size: 16px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.refund-status, .complaint-status {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-pending {\r\n  color: #FF9800;\r\n}\r\n\r\n.status-approved, .status-processing {\r\n  color: #2196F3;\r\n}\r\n\r\n.status-completed, .status-resolved {\r\n  color: #4CAF50;\r\n}\r\n\r\n.status-rejected {\r\n  color: #F44336;\r\n}\r\n\r\n.order-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 12px;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.refund-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.customer-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.customer-avatar {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 16px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.customer-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.refund-amount {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.label {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-right: 4px;\r\n}\r\n\r\n.amount {\r\n  font-size: 16px;\r\n  color: #ff6a00;\r\n  font-weight: 600;\r\n}\r\n\r\n.refund-reason {\r\n  background-color: #f9f9f9;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.reason {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.5;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-btn {\r\n  padding: 8px 16px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  margin-left: 12px;\r\n  background-color: #f0f0f0;\r\n  color: #333;\r\n}\r\n\r\n.action-btn.primary {\r\n  background-color: #1677FF;\r\n  color: #fff;\r\n}\r\n\r\n.action-btn.reject {\r\n  background-color: #ff4d4f;\r\n  color: #fff;\r\n}\r\n\r\n.complaint-content {\r\n  background-color: #f9f9f9;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.complaint-text {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.5;\r\n}\r\n\r\n.review-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.rating {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.star {\r\n  color: #ddd;\r\n  font-size: 16px;\r\n}\r\n\r\n.star.filled {\r\n  color: #FFAB00;\r\n}\r\n\r\n.rating-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-left: 4px;\r\n}\r\n\r\n.review-content {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.review-text {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.5;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.review-images {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.review-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  margin-right: 8px;\r\n  margin-bottom: 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.review-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.reply-section {\r\n  border-top: 1px solid #f0f0f0;\r\n  padding-top: 12px;\r\n}\r\n\r\n.merchant-reply {\r\n  background-color: #f9f9f9;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.reply-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n  display: block;\r\n}\r\n\r\n.reply-content {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.5;\r\n}\r\n\r\n.reply-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 4px;\r\n  display: block;\r\n  text-align: right;\r\n}\r\n\r\n.reply-form {\r\n  display: flex;\r\n}\r\n\r\n.reply-input {\r\n  flex: 1;\r\n  height: 36px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 18px;\r\n  padding: 0 12px;\r\n  font-size: 14px;\r\n}\r\n\r\n.reply-btn {\r\n  width: 60px;\r\n  height: 36px;\r\n  background-color: #1677FF;\r\n  color: #fff;\r\n  border-radius: 18px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60px 0;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 16px;\r\n  color: #999;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-order/pages/orders/after-sale.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA+JA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA;AAAA,MACZ,MAAM;AAAA,QACJ,EAAE,MAAM,QAAQ,OAAO,UAAU,OAAO,EAAG;AAAA,QAC3C,EAAE,MAAM,QAAQ,OAAO,aAAa,OAAO,EAAG;AAAA,QAC9C,EAAE,MAAM,QAAQ,OAAO,UAAU,OAAO,EAAE;AAAA,MAC3C;AAAA,MACD,YAAY;AAAA,QACV;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA;AAAA,UACR,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAAA,MACD;AAAA,MACD,eAAe;AAAA,QACb;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA;AAAA,UACR,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,SAAS;AAAA,QACX;AAAA,MACD;AAAA,MACD,YAAY;AAAA,QACV;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,UACD;AAAA,UACD,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,UACP,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,QAAQ,CAAE;AAAA,UACV,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,UACP,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,YACN;AAAA,UACD;AAAA,UACD,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,MACD;AAAA,MACD,cAAc,CAAC;AAAA,IACjB;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,UAAU,KAAK;AACb,WAAK,aAAa;AAAA,IACnB;AAAA,IACD,iBAAiB,IAAI;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,cAAc,IAAI;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,kBAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,EAAE;AAC9D,gBAAI,UAAU,IAAI;AAChB,mBAAK,WAAW,KAAK,EAAE,SAAS;AAChCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa,IAAI;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,kBAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,EAAE;AAC9D,gBAAI,UAAU,IAAI;AAChB,mBAAK,WAAW,KAAK,EAAE,SAAS;AAChCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,oBAAoB,IAAI;AACtBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB,IAAI;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,kBAAM,QAAQ,KAAK,cAAc,UAAU,UAAQ,KAAK,OAAO,EAAE;AACjE,gBAAI,UAAU,IAAI;AAChB,mBAAK,cAAc,KAAK,EAAE,SAAS;AACnCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB,YAAY;AAC1BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,MAAM;AAAA,QACzB,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,aAAa,GAAG;AAEtBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,qBACQ,IAAI,aAAa,GAAG;AAE7BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa,QAAQ,SAAS;AAC5BA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,SAAS,OAAO,OAAO;AAAA,MACzB,CAAC;AAAA,IACF;AAAA,IACD,YAAY,UAAU;AACpB,UAAI,CAAC,KAAK,aAAa,QAAQ,GAAG;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,YAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,QAAQ;AACpE,UAAI,UAAU,IAAI;AAChB,aAAK,WAAW,KAAK,EAAE,QAAQ,KAAK,aAAa,QAAQ;AACzD,aAAK,WAAW,KAAK,EAAE,YAAY,KAAK;AACxC,aAAK,aAAa,QAAQ,IAAI;AAE9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IACD,iBAAiB;AACf,YAAM,MAAM,oBAAI;AAChB,YAAM,OAAO,IAAI;AACjB,YAAM,QAAQ,OAAO,IAAI,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACxD,YAAM,MAAM,OAAO,IAAI,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACjD,YAAM,OAAO,OAAO,IAAI,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACnD,YAAM,SAAS,OAAO,IAAI,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAEvD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAAA,IACjD;AAAA,IACD,oBAAoB,QAAQ;AAC1B,YAAM,QAAQ;AAAA,QACZ,SAAS;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA;AAEb,aAAO,MAAM,MAAM,KAAK;AAAA,IACzB;AAAA,IACD,uBAAuB,QAAQ;AAC7B,YAAM,QAAQ;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,UAAU;AAAA;AAEZ,aAAO,MAAM,MAAM,KAAK;AAAA,IAC1B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClZA,GAAG,WAAW,eAAe;"}