/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.promotion-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 海报预览部分 */
.poster-preview-section {
  margin: 16px;
}
.preview-card {
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.poster-image {
  width: 100%;
  max-width: 300px;
  height: 400px;
  border-radius: 10px;
}
.preview-footer {
  margin-top: 12px;
  text-align: center;
}
.preview-text {
  font-size: 14px;
  color: #666;
}
.action-buttons {
  display: flex;
  gap: 12px;
}
.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  border: none;
}
.action-button.save {
  background: linear-gradient(135deg, #34C759, #32D74B);
  color: #FFFFFF;
}
.action-button.share {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
}
.button-icon {
  margin-right: 6px;
}

/* 自定义选项部分 */
.customization-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}
.option-group {
  margin-bottom: 20px;
}
.option-group:last-child {
  margin-bottom: 0;
}
.option-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
}
.template-scroll {
  width: 100%;
  white-space: nowrap;
}
.template-list {
  display: flex;
  padding-bottom: 8px;
}
.template-item {
  width: 80px;
  height: 120px;
  margin-right: 12px;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}
.template-item.active {
  border-color: #6B0FBE;
  transform: scale(1.05);
}
.template-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.type-options {
  display: flex;
  justify-content: space-between;
}
.type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 0;
  border-radius: 12px;
  transition: all 0.3s ease;
}
.type-option.active {
  background-color: rgba(107, 15, 190, 0.05);
}
.option-icon {
  margin-bottom: 8px;
}
.option-name {
  font-size: 12px;
  color: #666;
}
.type-option.active .option-name {
  color: #6B0FBE;
  font-weight: 500;
}
.theme-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.theme-option {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
}
.theme-option.active {
  border-color: #6B0FBE;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(107, 15, 190, 0.2);
}
.theme-check {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.toggle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}
.toggle-option:last-child {
  border-bottom: none;
}
.toggle-label {
  font-size: 14px;
  color: #333;
}
.custom-input {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 14px;
  color: #333;
  border: 1px solid transparent;
}
.custom-input:focus {
  border-color: #6B0FBE;
  background-color: #FFFFFF;
}

/* 历史记录部分 */
.history-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  margin-bottom: 32px;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}
.clear-history {
  font-size: 14px;
  color: #FF3B30;
}
.history-scroll {
  width: 100%;
}
.history-list {
  display: flex;
  padding-bottom: 8px;
}
.history-item {
  width: 80px;
  margin-right: 12px;
  flex-shrink: 0;
}
.history-item:last-child {
  margin-right: 0;
}
.history-image {
  width: 80px;
  height: 120px;
  border-radius: 10px;
  border: 1px solid #EEEEEE;
  margin-bottom: 6px;
}
.history-date {
  font-size: 10px;
  color: #999;
  text-align: center;
  display: block;
}