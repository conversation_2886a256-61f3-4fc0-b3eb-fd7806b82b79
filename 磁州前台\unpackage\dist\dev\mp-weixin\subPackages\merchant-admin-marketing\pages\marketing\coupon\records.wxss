/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.records-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-icon {
  width: 24px;
  height: 24px;
  color: #fff;
}

/* 筛选条件样式 */
.filter-section {
  background: #fff;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}
.filter-tabs {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
  margin-bottom: 15px;
}
.filter-tab {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #666;
  padding: 8px 0;
  position: relative;
}
.filter-tab.active {
  color: #FF5E62;
  font-weight: 600;
}
.filter-tab.active::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: #FF5E62;
  border-radius: 3px;
}
.search-filter {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
  margin-right: 10px;
}
.search-icon {
  font-size: 16px;
  color: #999;
  margin-right: 5px;
}
.search-input {
  flex: 1;
  font-size: 14px;
  color: #333;
  height: 20px;
  line-height: 20px;
}
.clear-icon {
  font-size: 18px;
  color: #999;
  padding: 0 5px;
}
.filter-button {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
}
.filter-text {
  font-size: 14px;
  color: #666;
}
.filter-icon {
  font-size: 14px;
  color: #666;
  margin-left: 3px;
}
.date-filter {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}
.date-range {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.date-input {
  flex: 1;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
}
.date-separator {
  margin: 0 10px;
  color: #999;
}
.date-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}
.date-value {
  font-size: 14px;
  color: #333;
}
.date-actions {
  display: flex;
  justify-content: flex-end;
}
.date-reset {
  background: #f5f5f5;
  border: none;
  border-radius: 6px;
  color: #666;
  font-size: 14px;
  padding: 6px 15px;
  margin-right: 10px;
}
.date-confirm {
  background: #FF5E62;
  border: none;
  border-radius: 6px;
  color: #fff;
  font-size: 14px;
  padding: 6px 15px;
}

/* 统计信息样式 */
.stats-section {
  display: flex;
  background: #fff;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}
.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-value {
  font-size: 18px;
  font-weight: bold;
  color: #FF5E62;
  margin-bottom: 5px;
}
.stats-label {
  font-size: 12px;
  color: #999;
}

/* 记录列表样式 */
.records-list {
  padding: 10px 15px;
}
.record-item {
  background: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.user-info {
  display: flex;
  align-items: center;
}
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
}
.user-details {
  display: flex;
  flex-direction: column;
}
.user-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}
.user-phone {
  font-size: 12px;
  color: #999;
}
.record-status {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
}
.status-used {
  background: #34C759;
}
.status-unused {
  background: #FF9500;
}
.status-expired {
  background: #8E8E93;
}
.record-body {
  border-top: 1px dashed #eee;
  border-bottom: 1px dashed #eee;
  padding: 15px 0;
  margin-bottom: 15px;
}
.coupon-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.coupon-title {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}
.coupon-value {
  display: flex;
  align-items: baseline;
}
.value-symbol {
  font-size: 14px;
  color: #FF5E62;
}
.value-amount {
  font-size: 20px;
  font-weight: bold;
  color: #FF5E62;
  margin: 0 2px;
}
.value-condition {
  font-size: 12px;
  color: #999;
}
.time-item {
  display: flex;
  margin-bottom: 8px;
}
.time-item:last-child {
  margin-bottom: 0;
}
.time-label {
  font-size: 13px;
  color: #999;
  width: 80px;
}
.time-value {
  font-size: 13px;
  color: #333;
  flex: 1;
}
.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.record-source {
  display: flex;
  align-items: center;
}
.source-label {
  font-size: 13px;
  color: #999;
}
.source-value {
  font-size: 13px;
  color: #333;
}
.record-actions {
  display: flex;
}
.action-button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 15px;
  font-size: 12px;
  padding: 4px 10px;
  margin-left: 10px;
}
.action-button.detail {
  color: #007AFF;
  border-color: #007AFF;
}
.action-button.message {
  color: #FF5E62;
  border-color: #FF5E62;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 15px 0;
}
.load-text {
  font-size: 14px;
  color: #007AFF;
}
.load-end {
  font-size: 14px;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}
.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 15px;
}
.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}
.empty-tip {
  font-size: 14px;
  color: #999;
}

/* 筛选弹窗样式 */
.filter-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 15px;
  max-height: 70vh;
  overflow-y: auto;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.popup-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}
.filter-options {
  margin-bottom: 20px;
}
.filter-group {
  margin-bottom: 20px;
}
.group-title {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  margin-bottom: 12px;
}
.time-options, .channel-options {
  display: flex;
  flex-wrap: wrap;
}
.time-option, .channel-option, .sort-option {
  background: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
  margin-right: 10px;
  margin-bottom: 10px;
  font-size: 13px;
  color: #666;
}
.time-option.active, .channel-option.active, .sort-option.active {
  background: rgba(255, 94, 98, 0.1);
  color: #FF5E62;
}
.sort-options {
  display: flex;
}
.filter-actions {
  display: flex;
  padding: 15px 0;
}
.action-reset {
  flex: 1;
  background: #f5f5f5;
  border: none;
  border-radius: 25px;
  color: #666;
  font-size: 15px;
  padding: 10px;
  margin-right: 15px;
}
.action-confirm {
  flex: 1;
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  border: none;
  border-radius: 25px;
  color: #fff;
  font-size: 15px;
  padding: 10px;
}