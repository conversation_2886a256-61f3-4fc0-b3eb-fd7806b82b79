{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/cart/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcY2FydFxpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"cart-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"navbar-left\" @click=\"goBack\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n        <view class=\"navbar-title\">购物车</view>\r\n        <view class=\"navbar-right\">\r\n          <view class=\"edit-btn\" @click=\"toggleEditMode\">\r\n            {{ isEditMode ? '完成' : '编辑' }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 内容区域 -->\r\n    <scroll-view \r\n      class=\"content-scroll\" \r\n      scroll-y \r\n      @scrolltolower=\"loadMore\"\r\n      v-if=\"cartItems.length > 0\"\r\n    >\r\n      <!-- 商家分组 -->\r\n      <view \r\n        class=\"shop-group\"\r\n        v-for=\"(shop, shopIndex) in cartShops\" \r\n        :key=\"shopIndex\"\r\n      >\r\n        <!-- 商家信息 -->\r\n        <view class=\"shop-header\">\r\n          <view class=\"checkbox-wrapper\" @click=\"toggleShopSelect(shop.id)\">\r\n            <view class=\"checkbox\" :class=\"{ checked: isShopSelected(shop.id) }\">\r\n              <svg v-if=\"isShopSelected(shop.id)\" class=\"check-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                <path d=\"M20 6L9 17l-5-5\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </view>\r\n          </view>\r\n          <view class=\"shop-info\" @click=\"viewShop(shop.id)\">\r\n            <image class=\"shop-logo\" :src=\"shop.logo\" mode=\"aspectFill\"></image>\r\n            <view class=\"shop-name\">{{ shop.name }}</view>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 商品列表 -->\r\n        <view class=\"cart-items\">\r\n          <view \r\n            class=\"cart-item\"\r\n            v-for=\"(item, itemIndex) in shop.items\" \r\n            :key=\"itemIndex\"\r\n          >\r\n            <view class=\"checkbox-wrapper\" @click=\"toggleItemSelect(item.id)\">\r\n              <view class=\"checkbox\" :class=\"{ checked: isItemSelected(item.id) }\">\r\n                <svg v-if=\"isItemSelected(item.id)\" class=\"check-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                  <path d=\"M20 6L9 17l-5-5\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                </svg>\r\n              </view>\r\n            </view>\r\n            \r\n            <image class=\"item-image\" :src=\"item.image\" mode=\"aspectFill\" @click=\"viewProduct(item.id)\"></image>\r\n            \r\n            <view class=\"item-info\" @click=\"viewProduct(item.id)\">\r\n              <view class=\"item-name\">{{ item.name }}</view>\r\n              <view class=\"item-specs\">{{ item.specs }}</view>\r\n              \r\n              <view class=\"item-bottom\">\r\n                <view class=\"item-price\">¥{{ item.price.toFixed(2) }}</view>\r\n                \r\n                <view class=\"quantity-control\">\r\n                  <view class=\"quantity-btn minus\" @click.stop=\"decreaseQuantity(item)\">\r\n                    <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                      <path d=\"M5 12h14\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                    </svg>\r\n                  </view>\r\n                  <view class=\"quantity-value\">{{ item.quantity }}</view>\r\n                  <view class=\"quantity-btn plus\" @click.stop=\"increaseQuantity(item)\">\r\n                    <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                      <path d=\"M12 5v14M5 12h14\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                    </svg>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 猜你喜欢 -->\r\n      <view class=\"recommend-section\">\r\n        <view class=\"section-title\">猜你喜欢</view>\r\n        <view class=\"product-grid\">\r\n          <view \r\n            class=\"product-item\"\r\n            v-for=\"(product, index) in recommendProducts\" \r\n            :key=\"index\"\r\n            @click=\"viewProduct(product.id)\"\r\n          >\r\n            <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\r\n            <view class=\"product-name\">{{ product.name }}</view>\r\n            <view class=\"product-price\">¥{{ product.price.toFixed(2) }}</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部安全区域 -->\r\n      <view class=\"safe-area-bottom\"></view>\r\n    </scroll-view>\r\n    \r\n    <!-- 空购物车 -->\r\n    <view class=\"empty-cart\" v-else>\r\n      <image class=\"empty-icon\" src=\"/static/images/empty-cart.png\" mode=\"aspectFit\"></image>\r\n      <view class=\"empty-text\">购物车空空如也</view>\r\n      <view class=\"go-shopping-btn\" @click=\"goShopping\">去逛逛</view>\r\n    </view>\r\n    \r\n    <!-- 底部结算栏 -->\r\n    <view class=\"checkout-bar\" v-if=\"cartItems.length > 0\">\r\n      <view class=\"select-all\" @click=\"toggleSelectAll\">\r\n        <view class=\"checkbox\" :class=\"{ checked: isAllSelected }\">\r\n          <svg v-if=\"isAllSelected\" class=\"check-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n            <path d=\"M20 6L9 17l-5-5\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n        <text>全选</text>\r\n      </view>\r\n      \r\n      <view class=\"total-section\" v-if=\"!isEditMode\">\r\n        <view class=\"total-price\">\r\n          <text>合计：</text>\r\n          <text class=\"price\">¥{{ totalPrice.toFixed(2) }}</text>\r\n        </view>\r\n        <view class=\"checkout-btn\" @click=\"checkout\">\r\n          结算({{ selectedCount }})\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"delete-section\" v-else>\r\n        <view class=\"delete-btn\" @click=\"deleteSelected\">\r\n          删除\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 编辑模式\r\nconst isEditMode = ref(false);\r\n\r\n// 购物车商品\r\nconst cartItems = ref([]);\r\n\r\n// 推荐商品\r\nconst recommendProducts = ref([\r\n  {\r\n    id: '101',\r\n    name: 'Apple AirPods Pro 2代',\r\n    image: 'https://via.placeholder.com/300x300',\r\n    price: 1999\r\n  },\r\n  {\r\n    id: '102',\r\n    name: '戴森吹风机 Supersonic HD08',\r\n    image: 'https://via.placeholder.com/300x300',\r\n    price: 3190\r\n  },\r\n  {\r\n    id: '103',\r\n    name: 'NIKE Air Jordan 1 高帮篮球鞋',\r\n    image: 'https://via.placeholder.com/300x300',\r\n    price: 1299\r\n  },\r\n  {\r\n    id: '104',\r\n    name: '三星Galaxy Watch 5 Pro',\r\n    image: 'https://via.placeholder.com/300x300',\r\n    price: 2999\r\n  }\r\n]);\r\n\r\n// 已选择的商品ID\r\nconst selectedItemIds = ref([]);\r\n\r\n// 按商家分组的购物车商品\r\nconst cartShops = computed(() => {\r\n  const shops = [];\r\n  const shopMap = {};\r\n  \r\n  cartItems.value.forEach(item => {\r\n    if (!shopMap[item.shopId]) {\r\n      shopMap[item.shopId] = {\r\n        id: item.shopId,\r\n        name: item.shopName,\r\n        logo: item.shopLogo,\r\n        items: []\r\n      };\r\n      shops.push(shopMap[item.shopId]);\r\n    }\r\n    shopMap[item.shopId].items.push(item);\r\n  });\r\n  \r\n  return shops;\r\n});\r\n\r\n// 是否全选\r\nconst isAllSelected = computed(() => {\r\n  return cartItems.value.length > 0 && selectedItemIds.value.length === cartItems.value.length;\r\n});\r\n\r\n// 已选择的商品数量\r\nconst selectedCount = computed(() => {\r\n  return selectedItemIds.value.length;\r\n});\r\n\r\n// 总价\r\nconst totalPrice = computed(() => {\r\n  let total = 0;\r\n  cartItems.value.forEach(item => {\r\n    if (selectedItemIds.value.includes(item.id)) {\r\n      total += item.price * item.quantity;\r\n    }\r\n  });\r\n  return total;\r\n});\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 切换编辑模式\r\nconst toggleEditMode = () => {\r\n  isEditMode.value = !isEditMode.value;\r\n};\r\n\r\n// 查看商品详情\r\nconst viewProduct = (productId) => {\r\n  uni.navigateTo({\r\n    url: `/subPackages/activity-showcase/pages/products/detail/index?id=${productId}`\r\n  });\r\n};\r\n\r\n// 查看店铺\r\nconst viewShop = (shopId) => {\r\n  uni.navigateTo({\r\n    url: `/subPackages/activity-showcase/pages/shops/detail?id=${shopId}`\r\n  });\r\n};\r\n\r\n// 去购物\r\nconst goShopping = () => {\r\n  uni.switchTab({\r\n    url: '/subPackages/activity-showcase/pages/discover/index'\r\n  });\r\n};\r\n\r\n// 增加商品数量\r\nconst increaseQuantity = (item) => {\r\n  if (item.quantity < 99) {\r\n    item.quantity++;\r\n  }\r\n};\r\n\r\n// 减少商品数量\r\nconst decreaseQuantity = (item) => {\r\n  if (item.quantity > 1) {\r\n    item.quantity--;\r\n  }\r\n};\r\n\r\n// 切换商品选择状态\r\nconst toggleItemSelect = (itemId) => {\r\n  const index = selectedItemIds.value.indexOf(itemId);\r\n  if (index === -1) {\r\n    selectedItemIds.value.push(itemId);\r\n  } else {\r\n    selectedItemIds.value.splice(index, 1);\r\n  }\r\n};\r\n\r\n// 切换店铺商品选择状态\r\nconst toggleShopSelect = (shopId) => {\r\n  const shop = cartShops.value.find(s => s.id === shopId);\r\n  if (!shop) return;\r\n  \r\n  const shopItemIds = shop.items.map(item => item.id);\r\n  const allSelected = shopItemIds.every(id => selectedItemIds.value.includes(id));\r\n  \r\n  if (allSelected) {\r\n    // 取消选择该店铺所有商品\r\n    selectedItemIds.value = selectedItemIds.value.filter(id => !shopItemIds.includes(id));\r\n  } else {\r\n    // 选择该店铺所有商品\r\n    shopItemIds.forEach(id => {\r\n      if (!selectedItemIds.value.includes(id)) {\r\n        selectedItemIds.value.push(id);\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\n// 切换全选状态\r\nconst toggleSelectAll = () => {\r\n  if (isAllSelected.value) {\r\n    selectedItemIds.value = [];\r\n  } else {\r\n    selectedItemIds.value = cartItems.value.map(item => item.id);\r\n  }\r\n};\r\n\r\n// 判断商品是否被选中\r\nconst isItemSelected = (itemId) => {\r\n  return selectedItemIds.value.includes(itemId);\r\n};\r\n\r\n// 判断店铺是否被选中\r\nconst isShopSelected = (shopId) => {\r\n  const shop = cartShops.value.find(s => s.id === shopId);\r\n  if (!shop) return false;\r\n  \r\n  return shop.items.every(item => selectedItemIds.value.includes(item.id));\r\n};\r\n\r\n// 删除选中的商品\r\nconst deleteSelected = () => {\r\n  if (selectedItemIds.value.length === 0) {\r\n    uni.showToast({\r\n      title: '请选择要删除的商品',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确定要删除选中的商品吗？',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        cartItems.value = cartItems.value.filter(item => !selectedItemIds.value.includes(item.id));\r\n        selectedItemIds.value = [];\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 结算\r\nconst checkout = () => {\r\n  if (selectedItemIds.value.length === 0) {\r\n    uni.showToast({\r\n      title: '请选择要结算的商品',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  const selectedItems = cartItems.value.filter(item => selectedItemIds.value.includes(item.id));\r\n  uni.navigateTo({\r\n    url: `/subPackages/activity-showcase/pages/orders/confirm?from=cart`\r\n  });\r\n};\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  // 模拟加载更多数据\r\n  uni.showToast({\r\n    title: '已加载全部数据',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 初始化购物车数据\r\nconst initCartData = () => {\r\n  // 模拟购物车数据\r\n  cartItems.value = [\r\n    {\r\n      id: '1',\r\n      shopId: 'shop1',\r\n      shopName: 'Apple授权专卖店',\r\n      shopLogo: 'https://via.placeholder.com/100',\r\n      name: 'iPhone 14 Pro 深空黑 256G',\r\n      specs: '颜色：深空黑；内存：256G',\r\n      price: 7999,\r\n      quantity: 1,\r\n      image: 'https://via.placeholder.com/300x300'\r\n    },\r\n    {\r\n      id: '2',\r\n      shopId: 'shop1',\r\n      shopName: 'Apple授权专卖店',\r\n      shopLogo: 'https://via.placeholder.com/100',\r\n      name: 'Apple Watch Series 8 GPS 45mm',\r\n      specs: '颜色：午夜色；尺寸：45mm',\r\n      price: 3499,\r\n      quantity: 1,\r\n      image: 'https://via.placeholder.com/300x300'\r\n    },\r\n    {\r\n      id: '3',\r\n      shopId: 'shop2',\r\n      shopName: '华为官方旗舰店',\r\n      shopLogo: 'https://via.placeholder.com/100',\r\n      name: '华为Mate 50 Pro 曜金黑 512G',\r\n      specs: '颜色：曜金黑；内存：512G',\r\n      price: 6999,\r\n      quantity: 1,\r\n      image: 'https://via.placeholder.com/300x300'\r\n    }\r\n  ];\r\n  \r\n  // 默认全选\r\n  selectedItemIds.value = cartItems.value.map(item => item.id);\r\n};\r\n\r\nonMounted(() => {\r\n  initCartData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cart-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #F2F2F7;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\r\n    backdrop-filter: blur(10px);\r\n    -webkit-backdrop-filter: blur(10px);\r\n    box-shadow: 0 4px 6px rgba(255,59,105,0.15);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding: 0 30rpx;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .navbar-left, .navbar-right {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n  }\r\n  \r\n  .navbar-title {\r\n    font-size: 36rpx;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    letter-spacing: 0.5px;\r\n  }\r\n  \r\n  .icon {\r\n    width: 48rpx;\r\n    height: 48rpx;\r\n  }\r\n  \r\n  .edit-btn {\r\n    font-size: 28rpx;\r\n    color: #FFFFFF;\r\n  }\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  flex: 1;\r\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\r\n  margin-bottom: 100rpx; /* 底部结算栏高度 */\r\n}\r\n\r\n/* 商家分组 */\r\n.shop-group {\r\n  margin-bottom: 20rpx;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n/* 商家信息 */\r\n.shop-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  border-bottom: 1rpx solid #F2F2F7;\r\n  \r\n  .checkbox-wrapper {\r\n    padding: 10rpx;\r\n  }\r\n  \r\n  .checkbox {\r\n    width: 36rpx;\r\n    height: 36rpx;\r\n    border-radius: 50%;\r\n    border: 2rpx solid #CCCCCC;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    &.checked {\r\n      background-color: #FF3B69;\r\n      border-color: #FF3B69;\r\n    }\r\n    \r\n    .check-icon {\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n  \r\n  .shop-info {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    margin-left: 10rpx;\r\n    \r\n    .shop-logo {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      border-radius: 50%;\r\n    }\r\n    \r\n    .shop-name {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n      margin-left: 10rpx;\r\n    }\r\n    \r\n    .arrow-icon {\r\n      width: 32rpx;\r\n      height: 32rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n\r\n/* 购物车商品 */\r\n.cart-items {\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.cart-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx 0;\r\n  border-bottom: 1rpx solid #F2F2F7;\r\n  \r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n  \r\n  .checkbox-wrapper {\r\n    padding: 10rpx;\r\n  }\r\n  \r\n  .checkbox {\r\n    width: 36rpx;\r\n    height: 36rpx;\r\n    border-radius: 50%;\r\n    border: 2rpx solid #CCCCCC;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    &.checked {\r\n      background-color: #FF3B69;\r\n      border-color: #FF3B69;\r\n    }\r\n    \r\n    .check-icon {\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n  \r\n  .item-image {\r\n    width: 160rpx;\r\n    height: 160rpx;\r\n    border-radius: 8rpx;\r\n    margin: 0 20rpx;\r\n  }\r\n  \r\n  .item-info {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 160rpx;\r\n    justify-content: space-between;\r\n    \r\n    .item-name {\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      display: -webkit-box;\r\n      -webkit-box-orient: vertical;\r\n      -webkit-line-clamp: 2;\r\n      overflow: hidden;\r\n    }\r\n    \r\n    .item-specs {\r\n      font-size: 24rpx;\r\n      color: #999999;\r\n      margin-top: 10rpx;\r\n    }\r\n    \r\n    .item-bottom {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-top: auto;\r\n      \r\n      .item-price {\r\n        font-size: 32rpx;\r\n        color: #FF3B69;\r\n        font-weight: 600;\r\n      }\r\n      \r\n      .quantity-control {\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        .quantity-btn {\r\n          width: 48rpx;\r\n          height: 48rpx;\r\n          border: 1rpx solid #EEEEEE;\r\n          border-radius: 50%;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n        \r\n        .quantity-value {\r\n          min-width: 60rpx;\r\n          text-align: center;\r\n          font-size: 28rpx;\r\n          color: #333333;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 猜你喜欢 */\r\n.recommend-section {\r\n  margin-top: 20rpx;\r\n  padding: 30rpx;\r\n  background-color: #FFFFFF;\r\n  \r\n  .section-title {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333333;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .product-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 20rpx;\r\n    \r\n    .product-item {\r\n      background-color: #FFFFFF;\r\n      border-radius: 8rpx;\r\n      overflow: hidden;\r\n      \r\n      .product-image {\r\n        width: 100%;\r\n        height: 320rpx;\r\n        border-radius: 8rpx;\r\n      }\r\n      \r\n      .product-name {\r\n        font-size: 28rpx;\r\n        color: #333333;\r\n        margin-top: 10rpx;\r\n        line-height: 1.4;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        overflow: hidden;\r\n      }\r\n      \r\n      .product-price {\r\n        font-size: 28rpx;\r\n        color: #FF3B69;\r\n        font-weight: 600;\r\n        margin-top: 10rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 空购物车 */\r\n.empty-cart {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding-top: calc(var(--status-bar-height, 25px) + 62px);\r\n  \r\n  .empty-icon {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n    margin-bottom: 40rpx;\r\n  }\r\n  \r\n  .go-shopping-btn {\r\n    padding: 20rpx 60rpx;\r\n    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\r\n    color: #FFFFFF;\r\n    font-size: 28rpx;\r\n    border-radius: 40rpx;\r\n  }\r\n}\r\n\r\n/* 底部结算栏 */\r\n.checkout-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 100rpx;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n  padding-bottom: env(safe-area-inset-bottom); /* 适配 iPhone X 以上的底部安全区域 */\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  z-index: 99;\r\n  \r\n  .select-all {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .checkbox {\r\n      width: 36rpx;\r\n      height: 36rpx;\r\n      border-radius: 50%;\r\n      border: 2rpx solid #CCCCCC;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 10rpx;\r\n      \r\n      &.checked {\r\n        background-color: #FF3B69;\r\n        border-color: #FF3B69;\r\n      }\r\n      \r\n      .check-icon {\r\n        color: #FFFFFF;\r\n      }\r\n    }\r\n    \r\n    text {\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n    }\r\n  }\r\n  \r\n  .total-section {\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    .total-price {\r\n      margin-right: 20rpx;\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n      \r\n      .price {\r\n        color: #FF3B69;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n    \r\n    .checkout-btn {\r\n      padding: 16rpx 40rpx;\r\n      background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\r\n      color: #FFFFFF;\r\n      font-size: 28rpx;\r\n      border-radius: 40rpx;\r\n    }\r\n  }\r\n  \r\n  .delete-section {\r\n    .delete-btn {\r\n      padding: 16rpx 40rpx;\r\n      background-color: #FF3B30;\r\n      color: #FFFFFF;\r\n      font-size: 28rpx;\r\n      border-radius: 40rpx;\r\n    }\r\n  }\r\n}\r\n\r\n/* 底部安全区域 */\r\n.safe-area-bottom {\r\n  height: 34px; /* iOS 安全区域高度 */\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/cart/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;;;;;;AA4JA,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAG5B,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AAGxB,UAAM,oBAAoBA,cAAAA,IAAI;AAAA,MAC5B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAG9B,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC/B,YAAM,QAAQ,CAAA;AACd,YAAM,UAAU,CAAA;AAEhB,gBAAU,MAAM,QAAQ,UAAQ;AAC9B,YAAI,CAAC,QAAQ,KAAK,MAAM,GAAG;AACzB,kBAAQ,KAAK,MAAM,IAAI;AAAA,YACrB,IAAI,KAAK;AAAA,YACT,MAAM,KAAK;AAAA,YACX,MAAM,KAAK;AAAA,YACX,OAAO,CAAE;AAAA,UACjB;AACM,gBAAM,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,QAChC;AACD,gBAAQ,KAAK,MAAM,EAAE,MAAM,KAAK,IAAI;AAAA,MACxC,CAAG;AAED,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,aAAO,UAAU,MAAM,SAAS,KAAK,gBAAgB,MAAM,WAAW,UAAU,MAAM;AAAA,IACxF,CAAC;AAGD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,aAAO,gBAAgB,MAAM;AAAA,IAC/B,CAAC;AAGD,UAAM,aAAaA,cAAQ,SAAC,MAAM;AAChC,UAAI,QAAQ;AACZ,gBAAU,MAAM,QAAQ,UAAQ;AAC9B,YAAI,gBAAgB,MAAM,SAAS,KAAK,EAAE,GAAG;AAC3C,mBAAS,KAAK,QAAQ,KAAK;AAAA,QAC5B;AAAA,MACL,CAAG;AACD,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,iBAAiB,MAAM;AAC3B,iBAAW,QAAQ,CAAC,WAAW;AAAA,IACjC;AAGA,UAAM,cAAc,CAAC,cAAc;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iEAAiE,SAAS;AAAA,MACnF,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,CAAC,WAAW;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wDAAwD,MAAM;AAAA,MACvE,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,SAAS;AACjC,UAAI,KAAK,WAAW,IAAI;AACtB,aAAK;AAAA,MACN;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,SAAS;AACjC,UAAI,KAAK,WAAW,GAAG;AACrB,aAAK;AAAA,MACN;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnC,YAAM,QAAQ,gBAAgB,MAAM,QAAQ,MAAM;AAClD,UAAI,UAAU,IAAI;AAChB,wBAAgB,MAAM,KAAK,MAAM;AAAA,MACrC,OAAS;AACL,wBAAgB,MAAM,OAAO,OAAO,CAAC;AAAA,MACtC;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnC,YAAM,OAAO,UAAU,MAAM,KAAK,OAAK,EAAE,OAAO,MAAM;AACtD,UAAI,CAAC;AAAM;AAEX,YAAM,cAAc,KAAK,MAAM,IAAI,UAAQ,KAAK,EAAE;AAClD,YAAM,cAAc,YAAY,MAAM,QAAM,gBAAgB,MAAM,SAAS,EAAE,CAAC;AAE9E,UAAI,aAAa;AAEf,wBAAgB,QAAQ,gBAAgB,MAAM,OAAO,QAAM,CAAC,YAAY,SAAS,EAAE,CAAC;AAAA,MACxF,OAAS;AAEL,oBAAY,QAAQ,QAAM;AACxB,cAAI,CAAC,gBAAgB,MAAM,SAAS,EAAE,GAAG;AACvC,4BAAgB,MAAM,KAAK,EAAE;AAAA,UAC9B;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,UAAI,cAAc,OAAO;AACvB,wBAAgB,QAAQ;MAC5B,OAAS;AACL,wBAAgB,QAAQ,UAAU,MAAM,IAAI,UAAQ,KAAK,EAAE;AAAA,MAC5D;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,aAAO,gBAAgB,MAAM,SAAS,MAAM;AAAA,IAC9C;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,YAAM,OAAO,UAAU,MAAM,KAAK,OAAK,EAAE,OAAO,MAAM;AACtD,UAAI,CAAC;AAAM,eAAO;AAElB,aAAO,KAAK,MAAM,MAAM,UAAQ,gBAAgB,MAAM,SAAS,KAAK,EAAE,CAAC;AAAA,IACzE;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,gBAAgB,MAAM,WAAW,GAAG;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,sBAAU,QAAQ,UAAU,MAAM,OAAO,UAAQ,CAAC,gBAAgB,MAAM,SAAS,KAAK,EAAE,CAAC;AACzF,4BAAgB,QAAQ;UACzB;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,gBAAgB,MAAM,WAAW,GAAG;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEqB,gBAAU,MAAM,OAAO,UAAQ,gBAAgB,MAAM,SAAS,KAAK,EAAE,CAAC;AAC5FA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AAErBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AAEzB,gBAAU,QAAQ;AAAA,QAChB;AAAA,UACE,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,UAAU;AAAA,UACV,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,UAAU;AAAA,UACV,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,UAAU;AAAA,UACV,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,UAAU;AAAA,UACV,OAAO;AAAA,QACR;AAAA,MACL;AAGE,sBAAgB,QAAQ,UAAU,MAAM,IAAI,UAAQ,KAAK,EAAE;AAAA,IAC7D;AAEAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvaD,GAAG,WAAW,eAAe;"}