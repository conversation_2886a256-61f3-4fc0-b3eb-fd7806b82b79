{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/message/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcbWVzc2FnZVxpbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"message-page\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"navbar-title\">消息</view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <scroll-view class=\"content-scroll\" scroll-y>\n      <!-- 消息分类 -->\n      <view class=\"message-tabs\">\n        <view class=\"tab-item\" :class=\"{ active: currentTab === 'all' }\" @click=\"switchMessageTab('all')\">\n          <text>全部</text>\n        </view>\n        <view class=\"tab-item\" :class=\"{ active: currentTab === 'system' }\" @click=\"switchMessageTab('system')\">\n          <text>系统通知</text>\n          <view class=\"tab-badge\" v-if=\"unreadCounts.system > 0\">{{ unreadCounts.system }}</view>\n        </view>\n        <view class=\"tab-item\" :class=\"{ active: currentTab === 'activity' }\" @click=\"switchMessageTab('activity')\">\n          <text>活动消息</text>\n          <view class=\"tab-badge\" v-if=\"unreadCounts.activity > 0\">{{ unreadCounts.activity }}</view>\n        </view>\n        <view class=\"tab-item\" :class=\"{ active: currentTab === 'interaction' }\" @click=\"switchMessageTab('interaction')\">\n          <text>互动消息</text>\n          <view class=\"tab-badge\" v-if=\"unreadCounts.interaction > 0\">{{ unreadCounts.interaction }}</view>\n        </view>\n      </view>\n      \n      <!-- 消息列表 -->\n      <view class=\"message-list\">\n        <view class=\"empty-tip\" v-if=\"filteredMessages.length === 0\">\n          <image class=\"empty-icon\" src=\"/static/images/message/empty-message.png\"></image>\n          <text class=\"empty-text\">暂无消息</text>\n        </view>\n        \n        <view class=\"message-item\" v-for=\"(message, index) in filteredMessages\" :key=\"index\" @click=\"readMessage(message)\">\n          <view class=\"message-avatar\">\n            <image :src=\"message.avatar\" mode=\"aspectFill\"></image>\n            <view class=\"message-dot\" v-if=\"!message.isRead\"></view>\n          </view>\n          <view class=\"message-content\">\n            <view class=\"message-header\">\n              <text class=\"message-title\">{{ message.title }}</text>\n              <text class=\"message-time\">{{ formatTime(message.time) }}</text>\n            </view>\n            <view class=\"message-body\">{{ message.content }}</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 底部安全区域 -->\n      <view class=\"safe-area-bottom\"></view>\n    </scroll-view>\n    \n    <!-- 底部导航栏 -->\n    <view class=\"tabbar\">\n      <view \n        class=\"tabbar-item\" \n        @click=\"switchTab('home')\"\n        data-tab=\"home\"\n      >\n        <view class=\"tab-icon home\"></view>\n        <text class=\"tabbar-text\">首页</text>\n      </view>\n      <view \n        class=\"tabbar-item\" \n        @click=\"switchTab('discover')\"\n        data-tab=\"discover\"\n      >\n        <view class=\"tab-icon discover\"></view>\n        <text class=\"tabbar-text\">本地商城</text>\n      </view>\n      <view \n        class=\"tabbar-item\" \n        @click=\"switchTab('distribution')\"\n        data-tab=\"distribution\"\n      >\n        <view class=\"tab-icon distribution\"></view>\n        <text class=\"tabbar-text\">分销</text>\n      </view>\n      <view \n        class=\"tabbar-item active\" \n        data-tab=\"message\"\n      >\n        <view class=\"tab-icon message\">\n          <view class=\"badge\" v-if=\"unreadCounts.system + unreadCounts.activity + unreadCounts.interaction > 0\">{{ unreadCounts.system + unreadCounts.activity + unreadCounts.interaction > 99 ? '99+' : unreadCounts.system + unreadCounts.activity + unreadCounts.interaction }}</view>\n        </view>\n        <text class=\"tabbar-text active-text\">消息</text>\n      </view>\n      <view \n        class=\"tabbar-item\" \n        @click=\"switchTab('my')\"\n        data-tab=\"my\"\n      >\n        <view class=\"tab-icon user\"></view>\n        <text class=\"tabbar-text\">我的</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      currentTab: 'all',\n      unreadCounts: {\n        system: 2,\n        activity: 1,\n        interaction: 3\n      },\n      messages: [\n        {\n          id: 1,\n          type: 'system',\n          title: '系统通知',\n          content: '您的账号已成功注册，欢迎使用我们的服务！',\n          avatar: 'https://via.placeholder.com/80x80',\n          time: '2023-12-01 10:30:00',\n          isRead: false\n        },\n        {\n          id: 2,\n          type: 'system',\n          title: '安全提醒',\n          content: '您的账号于今日10:30在新设备上登录，如非本人操作，请及时修改密码。',\n          avatar: 'https://via.placeholder.com/80x80',\n          time: '2023-12-02 15:20:00',\n          isRead: false\n        },\n        {\n          id: 3,\n          type: 'activity',\n          title: '拼团成功',\n          content: '恭喜您！您参与的\"iPhone 14 Pro\"拼团已成功，订单正在处理中。',\n          avatar: 'https://via.placeholder.com/80x80',\n          time: '2023-12-03 09:15:00',\n          isRead: true\n        },\n        {\n          id: 4,\n          type: 'activity',\n          title: '优惠券到账',\n          content: '您有一张满100减20的优惠券已到账，有效期至2023-12-31。',\n          avatar: 'https://via.placeholder.com/80x80',\n          time: '2023-12-04 14:05:00',\n          isRead: false\n        },\n        {\n          id: 5,\n          type: 'interaction',\n          title: '新的点赞',\n          content: '用户\"张三\"点赞了您的评论：\"这个产品真的很好用！\"',\n          avatar: 'https://via.placeholder.com/80x80',\n          time: '2023-12-05 11:30:00',\n          isRead: false\n        },\n        {\n          id: 6,\n          type: 'interaction',\n          title: '新的评论',\n          content: '用户\"李四\"回复了您的评论：\"谢谢分享，我也觉得不错。\"',\n          avatar: 'https://via.placeholder.com/80x80',\n          time: '2023-12-06 16:45:00',\n          isRead: false\n        },\n        {\n          id: 7,\n          type: 'interaction',\n          title: '新的关注',\n          content: '用户\"王五\"关注了您，去看看Ta的主页吧！',\n          avatar: 'https://via.placeholder.com/80x80',\n          time: '2023-12-07 08:20:00',\n          isRead: false\n        }\n      ]\n    }\n  },\n  computed: {\n    // 根据当前选中的标签过滤消息\n    filteredMessages() {\n      if (this.currentTab === 'all') {\n        return this.messages;\n      } else {\n        return this.messages.filter(message => message.type === this.currentTab);\n      }\n    },\n    \n    // 计算总未读消息数\n    totalUnreadCount() {\n      return this.unreadCounts.system + this.unreadCounts.activity + this.unreadCounts.interaction;\n    }\n  },\n  onLoad() {\n    // 页面加载时获取数据\n    this.fetchMessages();\n  },\n  methods: {\n    // 获取消息列表\n    fetchMessages() {\n      // 模拟从服务器获取消息\n      // 实际应用中应该调用API\n      // 示例数据已在data中定义\n      \n      // 计算未读消息数量\n      this.calculateUnreadCounts();\n    },\n    \n    // 计算各类型未读消息数量\n    calculateUnreadCounts() {\n      const counts = {\n        system: 0,\n        activity: 0,\n        interaction: 0\n      };\n      \n      this.messages.forEach(message => {\n        if (!message.isRead) {\n          counts[message.type]++;\n        }\n      });\n      \n      this.unreadCounts = counts;\n    },\n    \n    // 切换消息标签\n    switchMessageTab(tab) {\n      this.currentTab = tab;\n    },\n    \n    // 阅读消息\n    readMessage(message) {\n      // 标记消息为已读\n      if (!message.isRead) {\n        message.isRead = true;\n        this.calculateUnreadCounts();\n      }\n      \n      // 导航到消息详情页\n      uni.navigateTo({\n        url: `/subPackages/activity-showcase/pages/message/detail?id=${message.id}`\n      });\n    },\n    \n    // 格式化时间\n    formatTime(timestamp) {\n      const now = new Date();\n      const messageTime = new Date(timestamp);\n      const diff = now - messageTime;\n      \n      // 如果是今天的消息，只显示时间\n      if (diff < 24 * 60 * 60 * 1000 && \n          now.getDate() === messageTime.getDate() &&\n          now.getMonth() === messageTime.getMonth() &&\n          now.getFullYear() === messageTime.getFullYear()) {\n        return messageTime.toTimeString().slice(0, 5);\n      }\n      \n      // 如果是昨天的消息\n      const yesterday = new Date(now);\n      yesterday.setDate(now.getDate() - 1);\n      if (yesterday.getDate() === messageTime.getDate() &&\n          yesterday.getMonth() === messageTime.getMonth() &&\n          yesterday.getFullYear() === messageTime.getFullYear()) {\n        return '昨天 ' + messageTime.toTimeString().slice(0, 5);\n      }\n      \n      // 如果是今年的消息\n      if (now.getFullYear() === messageTime.getFullYear()) {\n        return `${messageTime.getMonth() + 1}月${messageTime.getDate()}日`;\n      }\n      \n      // 其他情况显示完整日期\n      return `${messageTime.getFullYear()}/${messageTime.getMonth() + 1}/${messageTime.getDate()}`;\n    },\n    \n    // 切换底部导航标签页\n    switchTab(tab) {\n      switch(tab) {\n        case 'home':\n          uni.redirectTo({\n            url: '/subPackages/activity-showcase/pages/index/index'\n          });\n          break;\n        case 'discover':\n          uni.redirectTo({\n            url: '/subPackages/activity-showcase/pages/discover/index'\n          });\n          break;\n        case 'distribution':\n          uni.redirectTo({\n            url: '/subPackages/activity-showcase/pages/distribution/index'\n          });\n          break;\n        case 'my':\n          uni.redirectTo({\n            url: '/subPackages/activity-showcase/pages/my/index'\n          });\n          break;\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.message-page {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #F2F2F7;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: calc(var(--status-bar-height, 25px) + 62px);\n  width: 100%;\n  z-index: 100;\n  \n  .navbar-bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\n  }\n  \n  .navbar-content {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    padding-top: var(--status-bar-height, 25px);\n    box-sizing: border-box;\n  }\n  \n  .navbar-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #FFFFFF;\n  }\n}\n\n/* 内容区域 */\n.content-scroll {\n  flex: 1;\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\n  padding-bottom: 15rpx;\n  margin-bottom: 100rpx; /* 为底部导航栏留出空间 */\n}\n\n/* 消息分类标签 */\n.message-tabs {\n  display: flex;\n  background-color: #FFFFFF;\n  padding: 20rpx 0;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);\n  \n  .tab-item {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n    padding: 10rpx 0;\n    \n    text {\n      font-size: 28rpx;\n      color: #666666;\n      transition: color 0.3s ease;\n    }\n    \n    &.active text {\n      color: #FF2C54;\n      font-weight: 600;\n    }\n    \n    &.active::after {\n      content: '';\n      position: absolute;\n      bottom: -10rpx;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40rpx;\n      height: 4rpx;\n      background-color: #FF2C54;\n      border-radius: 2rpx;\n    }\n    \n    .tab-badge {\n      position: absolute;\n      top: 0;\n      right: 50%;\n      transform: translateX(20rpx);\n      min-width: 32rpx;\n      height: 32rpx;\n      border-radius: 16rpx;\n      background-color: #FF3B30;\n      color: #FFFFFF;\n      font-size: 18rpx;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      padding: 0 6rpx;\n      box-sizing: border-box;\n      font-weight: 600;\n    }\n  }\n}\n\n/* 消息列表 */\n.message-list {\n  padding: 0 20rpx;\n}\n\n.message-item {\n  display: flex;\n  padding: 20rpx;\n  background-color: #FFFFFF;\n  border-radius: 15rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);\n}\n\n.message-avatar {\n  position: relative;\n  width: 80rpx;\n  height: 80rpx;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n  \n  image {\n    width: 100%;\n    height: 100%;\n    border-radius: 40rpx;\n  }\n  \n  .message-dot {\n    position: absolute;\n    top: 0;\n    right: 0;\n    width: 20rpx;\n    height: 20rpx;\n    border-radius: 10rpx;\n    background-color: #FF3B30;\n    border: 2rpx solid #FFFFFF;\n  }\n}\n\n.message-content {\n  flex: 1;\n}\n\n.message-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.message-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.message-time {\n  font-size: 22rpx;\n  color: #999999;\n}\n\n.message-body {\n  font-size: 26rpx;\n  color: #666666;\n  line-height: 1.4;\n}\n\n/* 空状态提示 */\n.empty-tip {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 0;\n}\n\n.empty-icon {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 20rpx;\n}\n\n.empty-text {\n  font-size: 26rpx;\n  color: #999999;\n}\n\n/* 底部安全区域 */\n.safe-area-bottom {\n  height: 34px; /* iOS 安全区域高度 */\n}\n\n/* 底部导航栏 */\n.tabbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background: #FFFFFF;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  padding-bottom: env(safe-area-inset-bottom);\n  z-index: 99;\n  border-top: 1rpx solid #EEEEEE;\n}\n  \n  .tabbar-item {\n  flex: 1;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n  padding: 6px 0;\n  box-sizing: border-box;\n  position: relative;\n}\n    \n.tabbar-item:active {\n  transform: scale(0.9);\n}\n    \n.tabbar-item.active .tab-icon {\n  transform: translateY(-5rpx);\n}\n      \n.tabbar-item.active .tabbar-text {\n  color: #FF3B69;\n  font-weight: 600;\n  transform: translateY(-2rpx);\n}\n    \n.tab-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 4px;\n  color: #999999;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n  background-size: contain;\n  background-position: center;\n  background-repeat: no-repeat;\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n}\n\n/* 首页图标 */\n.tab-icon.home {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"home\"] .tab-icon.home {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\n}\n\n/* 发现图标 */\n.tab-icon.discover {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"discover\"] .tab-icon.discover {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E\");\n}\n\n/* 分销图标 */\n.tab-icon.distribution {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"distribution\"] .tab-icon.distribution {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 消息图标 */\n.tab-icon.message {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"message\"] .tab-icon.message {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 我的图标 */\n.tab-icon.user {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"my\"] .tab-icon.user {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active .tab-icon {\n  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));\n}\n      \n      .badge {\n        position: absolute;\n  top: -8rpx;\n        right: -12rpx;\n        min-width: 32rpx;\n        height: 32rpx;\n        border-radius: 16rpx;\n  background: linear-gradient(135deg, #FF453A, #FF2D55);\n        color: #FFFFFF;\n        font-size: 18rpx;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        padding: 0 6rpx;\n        box-sizing: border-box;\n        font-weight: 600;\n  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  transform: scale(0.9);\n    }\n    \n    .tabbar-text {\n  font-size: 22rpx;\n      color: #8E8E93;\n  margin-top: 2rpx;\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n}\n    \n.tabbar-item::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%) scaleX(0);\n  width: 30rpx;\n  height: 4rpx;\n  background: #FF3B69;\n  border-radius: 2rpx;\n  transition: transform 0.3s ease;\n}\n    \n.tabbar-item.active::after {\n  transform: translateX(-50%) scaleX(1);\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/message/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAyGA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAc;AAAA,QACZ,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,MACd;AAAA,MACD,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,mBAAmB;AACjB,UAAI,KAAK,eAAe,OAAO;AAC7B,eAAO,KAAK;AAAA,aACP;AACL,eAAO,KAAK,SAAS,OAAO,aAAW,QAAQ,SAAS,KAAK,UAAU;AAAA,MACzE;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AACjB,aAAO,KAAK,aAAa,SAAS,KAAK,aAAa,WAAW,KAAK,aAAa;AAAA,IACnF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,cAAa;AAAA,EACnB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,gBAAgB;AAMd,WAAK,sBAAqB;AAAA,IAC3B;AAAA;AAAA,IAGD,wBAAwB;AACtB,YAAM,SAAS;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA;AAGf,WAAK,SAAS,QAAQ,aAAW;AAC/B,YAAI,CAAC,QAAQ,QAAQ;AACnB,iBAAO,QAAQ,IAAI;AAAA,QACrB;AAAA,MACF,CAAC;AAED,WAAK,eAAe;AAAA,IACrB;AAAA;AAAA,IAGD,iBAAiB,KAAK;AACpB,WAAK,aAAa;AAAA,IACnB;AAAA;AAAA,IAGD,YAAY,SAAS;AAEnB,UAAI,CAAC,QAAQ,QAAQ;AACnB,gBAAQ,SAAS;AACjB,aAAK,sBAAqB;AAAA,MAC5B;AAGAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0DAA0D,QAAQ,EAAE;AAAA,MAC3E,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,WAAW;AACpB,YAAM,MAAM,oBAAI;AAChB,YAAM,cAAc,IAAI,KAAK,SAAS;AACtC,YAAM,OAAO,MAAM;AAGnB,UAAI,OAAO,KAAK,KAAK,KAAK,OACtB,IAAI,QAAO,MAAO,YAAY,aAC9B,IAAI,SAAQ,MAAO,YAAY,cAC/B,IAAI,YAAW,MAAO,YAAY,YAAW,GAAI;AACnD,eAAO,YAAY,aAAc,EAAC,MAAM,GAAG,CAAC;AAAA,MAC9C;AAGA,YAAM,YAAY,IAAI,KAAK,GAAG;AAC9B,gBAAU,QAAQ,IAAI,QAAU,IAAE,CAAC;AACnC,UAAI,UAAU,cAAc,YAAY,QAAQ,KAC5C,UAAU,SAAQ,MAAO,YAAY,cACrC,UAAU,YAAW,MAAO,YAAY,YAAW,GAAI;AACzD,eAAO,QAAQ,YAAY,aAAY,EAAG,MAAM,GAAG,CAAC;AAAA,MACtD;AAGA,UAAI,IAAI,YAAW,MAAO,YAAY,YAAW,GAAI;AACnD,eAAO,GAAG,YAAY,aAAa,CAAC,IAAI,YAAY,SAAS;AAAA,MAC/D;AAGA,aAAO,GAAG,YAAY,YAAa,CAAA,IAAI,YAAY,SAAW,IAAE,CAAC,IAAI,YAAY,QAAO,CAAE;AAAA,IAC3F;AAAA;AAAA,IAGD,UAAU,KAAK;AACb,cAAO,KAAG;AAAA,QACR,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACP,CAAC;AACD;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChTA,GAAG,WAAW,eAAe;"}