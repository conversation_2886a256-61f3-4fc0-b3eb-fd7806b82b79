/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-cd88ae3b, html.data-v-cd88ae3b, #app.data-v-cd88ae3b, .index-container.data-v-cd88ae3b {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.flash-sale-page.data-v-cd88ae3b {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-cd88ae3b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-cd88ae3b {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
}
.custom-navbar .navbar-content.data-v-cd88ae3b {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding-top: var(--status-bar-height, 25px);
  padding-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
}
.custom-navbar .navbar-content .back-btn.data-v-cd88ae3b {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-navbar .navbar-content .back-icon.data-v-cd88ae3b {
  width: 100%;
  height: 100%;
}
.custom-navbar .navbar-content .navbar-title.data-v-cd88ae3b {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.custom-navbar .navbar-content .navbar-right.data-v-cd88ae3b {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.custom-navbar .navbar-content .navbar-right .close-btn.data-v-cd88ae3b {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 倒计时区域 */
.countdown-section.data-v-cd88ae3b {
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.countdown-section .countdown-header.data-v-cd88ae3b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.countdown-section .countdown-header .current-round .round-label.data-v-cd88ae3b {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 12rpx;
}
.countdown-section .countdown-header .current-round .round-status.data-v-cd88ae3b {
  font-size: 24rpx;
  color: #FF3B30;
  background-color: rgba(255, 59, 48, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.countdown-section .round-scroll.data-v-cd88ae3b {
  width: 100%;
  white-space: nowrap;
}
.countdown-section .round-scroll .round-list.data-v-cd88ae3b {
  display: inline-flex;
  padding: 10rpx 0;
}
.countdown-section .round-scroll .round-list .round-item.data-v-cd88ae3b {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 16rpx;
  background-color: #F2F2F7;
}
.countdown-section .round-scroll .round-list .round-item.active.data-v-cd88ae3b {
  background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);
}
.countdown-section .round-scroll .round-list .round-item.active .round-time.data-v-cd88ae3b, .countdown-section .round-scroll .round-list .round-item.active .round-status.data-v-cd88ae3b {
  color: #FFFFFF;
}
.countdown-section .round-scroll .round-list .round-item .round-time.data-v-cd88ae3b {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}
.countdown-section .round-scroll .round-list .round-item .round-status.data-v-cd88ae3b {
  font-size: 22rpx;
  color: #666666;
}

/* 商品列表 */
.content-scroll.data-v-cd88ae3b {
  flex: 1;
  width: 100%;
}
.products-list.data-v-cd88ae3b {
  padding: 20rpx;
}
.product-item.data-v-cd88ae3b {
  display: flex;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.product-item .product-image-container.data-v-cd88ae3b {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
}
.product-item .product-image-container .product-image.data-v-cd88ae3b {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.product-item .product-image-container .product-tag.data-v-cd88ae3b {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  border-radius: 10rpx;
  background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);
}
.product-item .product-info.data-v-cd88ae3b {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.product-item .product-info .product-title.data-v-cd88ae3b {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.product-item .product-info .product-desc.data-v-cd88ae3b {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-item .product-info .product-price-row.data-v-cd88ae3b {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}
.product-item .product-info .product-price-row .current-price.data-v-cd88ae3b {
  color: #FF3B30;
  font-weight: 600;
}
.product-item .product-info .product-price-row .current-price .price-symbol.data-v-cd88ae3b {
  font-size: 24rpx;
}
.product-item .product-info .product-price-row .current-price .price-value.data-v-cd88ae3b {
  font-size: 32rpx;
}
.product-item .product-info .product-price-row .original-price.data-v-cd88ae3b {
  margin-left: 12rpx;
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}
.product-item .product-info .product-progress.data-v-cd88ae3b {
  margin-bottom: 16rpx;
}
.product-item .product-info .product-progress .progress-text.data-v-cd88ae3b {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 8rpx;
}
.product-item .product-info .product-progress .progress-bar.data-v-cd88ae3b {
  height: 8rpx;
  background-color: #F2F2F7;
  border-radius: 4rpx;
  overflow: hidden;
}
.product-item .product-info .product-progress .progress-bar .progress-filled.data-v-cd88ae3b {
  height: 100%;
  background: linear-gradient(90deg, #FF9500 0%, #FF3B30 100%);
  border-radius: 4rpx;
}
.product-item .product-info .product-btn.data-v-cd88ae3b {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);
  font-size: 26rpx;
  font-weight: 500;
  color: #FFFFFF;
}

/* 加载更多和到底了提示 */
.loading-more.data-v-cd88ae3b, .no-more.data-v-cd88ae3b {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999999;
}

/* 空状态 */
.empty-state.data-v-cd88ae3b {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-state .empty-image.data-v-cd88ae3b {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-state .empty-text.data-v-cd88ae3b {
  font-size: 28rpx;
  color: #999999;
}