<template>
  <view class="demo-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: `${statusBarHeight}px` }">
      <view class="back-btn" @click="navigateBack">
        <text class="iconfont icon-back"></text>
      </view>
      <text class="page-title">增强版推广功能演示</text>
    </view>
    
    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 介绍卡片 -->
      <view class="intro-card">
        <view class="card-title">
          <text>增强版统一推广组件</text>
        </view>
        <view class="card-description">
          <text>这是增强版多功能推广组件，支持广告/付费发布、置顶和刷新功能，具有动画效果和VIP折扣。</text>
        </view>
      </view>
      
      <!-- 信息卡片 -->
      <view class="info-card">
        <view class="info-header">
          <text class="info-title">测试信息标题</text>
          <text class="info-category">分类: 寻找服务</text>
        </view>
        <view class="info-content">
          <text>这是一条测试信息内容，用于演示推广功能组件的使用方法。您可以通过下方的推广操作面板来体验各种推广功能。</text>
        </view>
        <view class="info-footer">
          <text class="info-time">发布时间: 2024-06-01 10:30</text>
          <text class="info-views">浏览: 128</text>
        </view>
      </view>
      
      <!-- 推广操作组件 -->
      <premium-actions-enhanced 
        title="推广操作" 
        info-id="demo-123456" 
        :show-account-info="true"
        @action-completed="handleActionCompleted"
      />
      
      <!-- 操作记录 -->
      <view class="action-history">
        <view class="history-title">
          <text>操作记录</text>
        </view>
        <view class="history-list">
          <view class="history-item" v-for="(item, index) in actionHistory" :key="index">
            <text class="history-time">{{ formatTime(item.timestamp) }}</text>
            <text class="history-action">{{ item.description }}</text>
          </view>
          <view class="empty-history" v-if="actionHistory.length === 0">
            <text>暂无操作记录</text>
          </view>
        </view>
      </view>
      
      <!-- 功能对比 -->
      <view class="compare-card">
        <view class="compare-title">
          <text>基础版与增强版对比</text>
        </view>
        <view class="compare-table">
          <view class="compare-header">
            <text class="compare-feature">功能</text>
            <text class="compare-basic">基础版</text>
            <text class="compare-enhanced">增强版</text>
          </view>
          <view class="compare-row">
            <text class="compare-feature">动画效果</text>
            <text class="compare-basic">✖</text>
            <text class="compare-enhanced">✓</text>
          </view>
          <view class="compare-row">
            <text class="compare-feature">VIP折扣</text>
            <text class="compare-basic">✖</text>
            <text class="compare-enhanced">✓</text>
          </view>
          <view class="compare-row">
            <text class="compare-feature">账户信息</text>
            <text class="compare-basic">✖</text>
            <text class="compare-enhanced">✓</text>
          </view>
          <view class="compare-row">
            <text class="compare-feature">多种置顶选项</text>
            <text class="compare-basic">✓</text>
            <text class="compare-enhanced">✓</text>
          </view>
          <view class="compare-row">
            <text class="compare-feature">广告/付费支持</text>
            <text class="compare-basic">✓</text>
            <text class="compare-enhanced">✓</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import PremiumActionsEnhanced from '@/components/premium/PremiumActionsEnhanced.vue';

// 响应式状态
const statusBarHeight = ref(20);
const actionHistory = ref([]);

// 页面加载
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
});

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 处理操作完成事件
const handleActionCompleted = (result) => {
  console.log('操作完成:', result);
  
  // 构建操作描述
  let description = '';
  switch(result.action) {
    case 'publish':
      description = `${result.type === 'ad' ? '通过广告' : '付费'}发布了信息`;
      break;
    case 'top':
      description = `${result.type === 'ad' ? '通过广告' : '付费'}置顶了信息，有效期${result.option.duration || ''}`;
      break;
    case 'refresh':
      description = `${result.type === 'ad' ? '通过广告' : '付费'}刷新了信息`;
      break;
  }
  
  // 添加到操作记录
  actionHistory.value.unshift({
    timestamp: Date.now(),
    description,
    result
  });
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
};
</script>

<style lang="scss" scoped>
.demo-container {
  min-height: 100vh;
  background-color: #f6f8fa;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background: linear-gradient(120deg, #007aff, #5856d6);
  padding: 0 16px;
  position: relative;
  z-index: 100;
  box-shadow: 0 1px 12px rgba(0, 122, 255, 0.18);
}

.back-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-back {
  color: #FFFFFF;
  font-size: 20px;
}

.page-title {
  flex: 1;
  text-align: center;
  color: #FFFFFF;
  font-size: 18px;
  font-weight: 500;
  margin-right: 36px; /* 为了标题居中 */
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 20rpx;
}

/* 介绍卡片 */
.intro-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.card-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 信息卡片 */
.info-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.info-header {
  margin-bottom: 16rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.info-category {
  font-size: 24rpx;
  color: #007aff;
  background: rgba(0, 122, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.info-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.info-footer {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

/* 操作记录 */
.action-history {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.history-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.history-list {
  max-height: 300rpx;
  overflow-y: auto;
}

.history-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.history-time {
  font-size: 24rpx;
  color: #999;
  width: 160rpx;
  flex-shrink: 0;
}

.history-action {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.empty-history {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 功能对比 */
.compare-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.compare-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.compare-table {
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.compare-header {
  display: flex;
  background: #f8f9fa;
  padding: 16rpx 0;
  font-weight: 600;
}

.compare-row {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding: 16rpx 0;
}

.compare-feature {
  flex: 2;
  padding: 0 20rpx;
  color: #333;
}

.compare-basic,
.compare-enhanced {
  flex: 1;
  text-align: center;
  color: #666;
}

.compare-enhanced {
  color: #007aff;
}
</style> 