{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/orders/confirm/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcb3JkZXJzXGNvbmZpcm1caW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"order-confirm-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"navbar-left\" @click=\"goBack\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n            <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n        <view class=\"navbar-title\">订单确认</view>\r\n        <view class=\"navbar-right\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 内容区域 -->\r\n    <scroll-view \r\n      class=\"content-scroll\" \r\n      scroll-y\r\n    >\r\n      <!-- 收货地址 -->\r\n      <view class=\"address-section\" @click=\"selectAddress\">\r\n        <view class=\"address-content\" v-if=\"address\">\r\n          <view class=\"address-info\">\r\n            <view class=\"user-info\">\r\n              <text class=\"user-name\">{{ address.name }}</text>\r\n              <text class=\"user-phone\">{{ address.phone }}</text>\r\n            </view>\r\n            <view class=\"address-detail\">{{ address.province }} {{ address.city }} {{ address.district }} {{ address.detail }}</view>\r\n          </view>\r\n          <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n        <view class=\"address-empty\" v-else>\r\n          <view class=\"add-address\">\r\n            <text>添加收货地址</text>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"address-divider\">\r\n          <image class=\"divider-image\" src=\"/static/images/address-divider.png\" mode=\"scaleToFill\"></image>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 商品列表 -->\r\n      <view class=\"products-section\">\r\n        <view class=\"shop-group\" v-for=\"(shop, shopIndex) in orderShops\" :key=\"shopIndex\">\r\n          <view class=\"shop-header\">\r\n            <image class=\"shop-logo\" :src=\"shop.logo\" mode=\"aspectFill\"></image>\r\n            <view class=\"shop-name\">{{ shop.name }}</view>\r\n          </view>\r\n          \r\n          <view class=\"product-list\">\r\n            <view class=\"product-item\" v-for=\"(item, itemIndex) in shop.items\" :key=\"itemIndex\">\r\n              <image class=\"product-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n              <view class=\"product-info\">\r\n                <view class=\"product-name\">{{ item.name }}</view>\r\n                <view class=\"product-specs\">{{ item.specs }}</view>\r\n                <view class=\"product-price-row\">\r\n                  <view class=\"product-price\">¥{{ item.price.toFixed(2) }}</view>\r\n                  <view class=\"product-quantity\">x{{ item.quantity }}</view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 配送方式 -->\r\n          <view class=\"delivery-section\">\r\n            <view class=\"section-title\">配送方式</view>\r\n            <view class=\"delivery-options\">\r\n              <view \r\n                class=\"delivery-option\" \r\n                v-for=\"(option, index) in deliveryOptions\" \r\n                :key=\"index\"\r\n                :class=\"{ active: shop.deliveryType === option.type }\"\r\n                @click=\"selectDelivery(shop, option.type)\"\r\n              >\r\n                {{ option.name }}\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 订单备注 -->\r\n          <view class=\"remark-section\">\r\n            <view class=\"section-title\">订单备注</view>\r\n            <input \r\n              class=\"remark-input\" \r\n              type=\"text\" \r\n              placeholder=\"选填，请先和商家协商一致\" \r\n              v-model=\"shop.remark\"\r\n              maxlength=\"50\"\r\n            />\r\n          </view>\r\n          \r\n          <!-- 优惠券 -->\r\n          <view class=\"coupon-section\" @click=\"selectCoupon(shop)\">\r\n            <view class=\"section-title\">优惠券</view>\r\n            <view class=\"coupon-info\">\r\n              <text>{{ shop.coupon ? `${shop.coupon.name}` : '暂无可用优惠券' }}</text>\r\n              <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 店铺小计 -->\r\n          <view class=\"shop-subtotal\">\r\n            <text>共{{ getTotalQuantity(shop) }}件商品</text>\r\n            <text>小计：¥{{ getShopTotal(shop).toFixed(2) }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 支付方式 -->\r\n      <view class=\"payment-section\">\r\n        <view class=\"section-title\">支付方式</view>\r\n        <view class=\"payment-options\">\r\n          <view \r\n            class=\"payment-option\" \r\n            v-for=\"(option, index) in paymentOptions\" \r\n            :key=\"index\"\r\n            :class=\"{ active: paymentType === option.type }\"\r\n            @click=\"selectPayment(option.type)\"\r\n          >\r\n            <image class=\"payment-icon\" :src=\"option.icon\" mode=\"aspectFit\"></image>\r\n            <text>{{ option.name }}</text>\r\n            <view class=\"checkbox\" v-if=\"paymentType === option.type\">\r\n              <svg class=\"check-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                <path d=\"M20 6L9 17l-5-5\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 订单金额 -->\r\n      <view class=\"amount-section\">\r\n        <view class=\"amount-row\">\r\n          <text>商品金额</text>\r\n          <text>¥{{ getProductTotal().toFixed(2) }}</text>\r\n        </view>\r\n        <view class=\"amount-row\">\r\n          <text>运费</text>\r\n          <text>¥{{ getDeliveryFee().toFixed(2) }}</text>\r\n        </view>\r\n        <view class=\"amount-row\">\r\n          <text>优惠金额</text>\r\n          <text>-¥{{ getDiscountAmount().toFixed(2) }}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部安全区域 -->\r\n      <view class=\"safe-area-bottom\"></view>\r\n    </scroll-view>\r\n    \r\n    <!-- 底部结算栏 -->\r\n    <view class=\"checkout-bar\">\r\n      <view class=\"total-amount\">\r\n        <text>实付款：</text>\r\n        <text class=\"price\">¥{{ getTotalAmount().toFixed(2) }}</text>\r\n      </view>\r\n      <view class=\"submit-btn\" @click=\"submitOrder\">提交订单</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 收货地址\r\nconst address = ref({\r\n  id: '1',\r\n  name: '张三',\r\n  phone: '138****1234',\r\n  province: '北京市',\r\n  city: '北京市',\r\n  district: '朝阳区',\r\n  detail: '三里屯SOHO 5号楼2单元801'\r\n});\r\n\r\n// 订单商品（按店铺分组）\r\nconst orderShops = ref([]);\r\n\r\n// 支付方式\r\nconst paymentType = ref('wechat');\r\n\r\n// 支付方式选项\r\nconst paymentOptions = ref([\r\n  {\r\n    type: 'wechat',\r\n    name: '微信支付',\r\n    icon: '/static/images/payment/wechat.png'\r\n  },\r\n  {\r\n    type: 'alipay',\r\n    name: '支付宝',\r\n    icon: '/static/images/payment/alipay.png'\r\n  },\r\n  {\r\n    type: 'balance',\r\n    name: '余额支付',\r\n    icon: '/static/images/payment/balance.png'\r\n  }\r\n]);\r\n\r\n// 配送方式选项\r\nconst deliveryOptions = ref([\r\n  {\r\n    type: 'express',\r\n    name: '快递配送'\r\n  },\r\n  {\r\n    type: 'self',\r\n    name: '到店自提'\r\n  }\r\n]);\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 选择收货地址\r\nconst selectAddress = () => {\r\n  uni.navigateTo({\r\n    url: '/subPackages/activity-showcase/pages/my/address'\r\n  });\r\n};\r\n\r\n// 选择配送方式\r\nconst selectDelivery = (shop, type) => {\r\n  shop.deliveryType = type;\r\n};\r\n\r\n// 选择优惠券\r\nconst selectCoupon = (shop) => {\r\n  uni.showToast({\r\n    title: '优惠券功能开发中',\r\n    icon: 'none'\r\n  });\r\n};\r\n\r\n// 选择支付方式\r\nconst selectPayment = (type) => {\r\n  paymentType.value = type;\r\n};\r\n\r\n// 获取店铺商品总数量\r\nconst getTotalQuantity = (shop) => {\r\n  return shop.items.reduce((total, item) => total + item.quantity, 0);\r\n};\r\n\r\n// 获取店铺商品总金额\r\nconst getShopTotal = (shop) => {\r\n  return shop.items.reduce((total, item) => total + item.price * item.quantity, 0);\r\n};\r\n\r\n// 获取所有商品总金额\r\nconst getProductTotal = () => {\r\n  return orderShops.value.reduce((total, shop) => {\r\n    return total + getShopTotal(shop);\r\n  }, 0);\r\n};\r\n\r\n// 获取运费\r\nconst getDeliveryFee = () => {\r\n  return orderShops.value.reduce((total, shop) => {\r\n    return total + (shop.deliveryFee || 0);\r\n  }, 0);\r\n};\r\n\r\n// 获取优惠金额\r\nconst getDiscountAmount = () => {\r\n  return orderShops.value.reduce((total, shop) => {\r\n    return total + (shop.coupon ? shop.coupon.amount : 0);\r\n  }, 0);\r\n};\r\n\r\n// 获取订单总金额\r\nconst getTotalAmount = () => {\r\n  return getProductTotal() + getDeliveryFee() - getDiscountAmount();\r\n};\r\n\r\n// 提交订单\r\nconst submitOrder = () => {\r\n  // 验证收货地址\r\n  if (!address.value) {\r\n    uni.showToast({\r\n      title: '请选择收货地址',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  // 模拟提交订单\r\n  uni.showLoading({\r\n    title: '提交订单中...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 模拟订单ID\r\n    const orderId = 'ORDER_' + Date.now();\r\n    \r\n    // 跳转到支付页面\r\n    uni.navigateTo({\r\n      url: `/subPackages/activity-showcase/pages/payment/index?orderId=${orderId}&amount=${getTotalAmount()}`\r\n    });\r\n  }, 1500);\r\n};\r\n\r\n// 初始化订单数据\r\nconst initOrderData = (fromCart) => {\r\n  // 模拟订单数据\r\n  orderShops.value = [\r\n    {\r\n      id: 'shop1',\r\n      name: 'Apple授权专卖店',\r\n      logo: 'https://via.placeholder.com/100',\r\n      deliveryType: 'express',\r\n      deliveryFee: 10,\r\n      remark: '',\r\n      coupon: {\r\n        id: 'coupon1',\r\n        name: '满5000减300',\r\n        amount: 300\r\n      },\r\n      items: [\r\n        {\r\n          id: '1',\r\n          name: 'iPhone 14 Pro 深空黑 256G',\r\n          specs: '颜色：深空黑；内存：256G',\r\n          price: 7999,\r\n          quantity: 1,\r\n          image: 'https://via.placeholder.com/300x300'\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      id: 'shop2',\r\n      name: '华为官方旗舰店',\r\n      logo: 'https://via.placeholder.com/100',\r\n      deliveryType: 'express',\r\n      deliveryFee: 0,\r\n      remark: '',\r\n      coupon: null,\r\n      items: [\r\n        {\r\n          id: '3',\r\n          name: '华为Mate 50 Pro 曜金黑 512G',\r\n          specs: '颜色：曜金黑；内存：512G',\r\n          price: 6999,\r\n          quantity: 1,\r\n          image: 'https://via.placeholder.com/300x300'\r\n        }\r\n      ]\r\n    }\r\n  ];\r\n};\r\n\r\nonMounted(() => {\r\n  const query = uni.getSystemInfoSync().platform === 'devtools' \r\n    ? { from: 'cart' } // 开发环境模拟数据\r\n    : uni.$u.route.query;\r\n    \r\n  // 从购物车进入或从商品详情进入\r\n  const fromCart = query.from === 'cart';\r\n  initOrderData(fromCart);\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.order-confirm-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #F2F2F7;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\r\n    backdrop-filter: blur(10px);\r\n    -webkit-backdrop-filter: blur(10px);\r\n    box-shadow: 0 4px 6px rgba(255,59,105,0.15);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding: 0 30rpx;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .navbar-left, .navbar-right {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n  }\r\n  \r\n  .navbar-title {\r\n    font-size: 36rpx;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    letter-spacing: 0.5px;\r\n  }\r\n  \r\n  .icon {\r\n    width: 48rpx;\r\n    height: 48rpx;\r\n  }\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  flex: 1;\r\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\r\n  margin-bottom: 100rpx; /* 底部结算栏高度 */\r\n}\r\n\r\n/* 收货地址 */\r\n.address-section {\r\n  background-color: #FFFFFF;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  \r\n  .address-content {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 30rpx;\r\n    \r\n    .address-info {\r\n      flex: 1;\r\n      \r\n      .user-info {\r\n        margin-bottom: 10rpx;\r\n        \r\n        .user-name {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #333333;\r\n          margin-right: 20rpx;\r\n        }\r\n        \r\n        .user-phone {\r\n          font-size: 28rpx;\r\n          color: #666666;\r\n        }\r\n      }\r\n      \r\n      .address-detail {\r\n        font-size: 28rpx;\r\n        color: #666666;\r\n        line-height: 1.4;\r\n      }\r\n    }\r\n    \r\n    .arrow-icon {\r\n      width: 32rpx;\r\n      height: 32rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n  \r\n  .address-empty {\r\n    padding: 30rpx;\r\n    \r\n    .add-address {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      \r\n      text {\r\n        font-size: 28rpx;\r\n        color: #666666;\r\n      }\r\n      \r\n      .arrow-icon {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .address-divider {\r\n    height: 10rpx;\r\n    width: 100%;\r\n    overflow: hidden;\r\n    \r\n    .divider-image {\r\n      width: 100%;\r\n      height: 10rpx;\r\n    }\r\n  }\r\n}\r\n\r\n/* 商品列表 */\r\n.products-section {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.shop-group {\r\n  background-color: #FFFFFF;\r\n  margin-bottom: 20rpx;\r\n  \r\n  .shop-header {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx 30rpx;\r\n    border-bottom: 1rpx solid #F2F2F7;\r\n    \r\n    .shop-logo {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      border-radius: 50%;\r\n    }\r\n    \r\n    .shop-name {\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n      margin-left: 10rpx;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n  \r\n  .product-list {\r\n    padding: 0 30rpx;\r\n  }\r\n  \r\n  .product-item {\r\n    display: flex;\r\n    padding: 30rpx 0;\r\n    border-bottom: 1rpx solid #F2F2F7;\r\n    \r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    .product-image {\r\n      width: 160rpx;\r\n      height: 160rpx;\r\n      border-radius: 8rpx;\r\n    }\r\n    \r\n    .product-info {\r\n      flex: 1;\r\n      margin-left: 20rpx;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      \r\n      .product-name {\r\n        font-size: 28rpx;\r\n        color: #333333;\r\n        line-height: 1.4;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        overflow: hidden;\r\n      }\r\n      \r\n      .product-specs {\r\n        font-size: 24rpx;\r\n        color: #999999;\r\n        margin-top: 10rpx;\r\n      }\r\n      \r\n      .product-price-row {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-top: auto;\r\n        \r\n        .product-price {\r\n          font-size: 28rpx;\r\n          color: #FF3B69;\r\n          font-weight: 600;\r\n        }\r\n        \r\n        .product-quantity {\r\n          font-size: 28rpx;\r\n          color: #999999;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .delivery-section, .remark-section, .coupon-section {\r\n    padding: 20rpx 30rpx;\r\n    border-top: 1rpx solid #F2F2F7;\r\n    \r\n    .section-title {\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n      margin-bottom: 20rpx;\r\n    }\r\n  }\r\n  \r\n  .delivery-options {\r\n    display: flex;\r\n    \r\n    .delivery-option {\r\n      padding: 10rpx 20rpx;\r\n      border-radius: 30rpx;\r\n      font-size: 24rpx;\r\n      color: #666666;\r\n      background-color: #F2F2F7;\r\n      margin-right: 20rpx;\r\n      \r\n      &.active {\r\n        color: #FFFFFF;\r\n        background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\r\n      }\r\n    }\r\n  }\r\n  \r\n  .remark-input {\r\n    height: 80rpx;\r\n    background-color: #F2F2F7;\r\n    border-radius: 8rpx;\r\n    padding: 0 20rpx;\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n  }\r\n  \r\n  .coupon-info {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    \r\n    text {\r\n      font-size: 28rpx;\r\n      color: #FF3B69;\r\n    }\r\n    \r\n    .arrow-icon {\r\n      width: 32rpx;\r\n      height: 32rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n  \r\n  .shop-subtotal {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20rpx 30rpx;\r\n    border-top: 1rpx solid #F2F2F7;\r\n    \r\n    text {\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n      \r\n      &:last-child {\r\n        font-weight: 600;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 支付方式 */\r\n.payment-section {\r\n  background-color: #FFFFFF;\r\n  padding: 20rpx 30rpx;\r\n  margin-bottom: 20rpx;\r\n  \r\n  .section-title {\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .payment-options {\r\n    .payment-option {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 20rpx 0;\r\n      border-bottom: 1rpx solid #F2F2F7;\r\n      \r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n      \r\n      .payment-icon {\r\n        width: 48rpx;\r\n        height: 48rpx;\r\n        margin-right: 20rpx;\r\n      }\r\n      \r\n      text {\r\n        flex: 1;\r\n        font-size: 28rpx;\r\n        color: #333333;\r\n      }\r\n      \r\n      .checkbox {\r\n        width: 36rpx;\r\n        height: 36rpx;\r\n        border-radius: 50%;\r\n        background-color: #FF3B69;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        \r\n        .check-icon {\r\n          color: #FFFFFF;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 订单金额 */\r\n.amount-section {\r\n  background-color: #FFFFFF;\r\n  padding: 20rpx 30rpx;\r\n  \r\n  .amount-row {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 10rpx 0;\r\n    \r\n    text {\r\n      font-size: 28rpx;\r\n      color: #333333;\r\n    }\r\n  }\r\n}\r\n\r\n/* 底部结算栏 */\r\n.checkout-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 100rpx;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n  padding-bottom: env(safe-area-inset-bottom); /* 适配 iPhone X 以上的底部安全区域 */\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  z-index: 99;\r\n  \r\n  .total-amount {\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n    \r\n    .price {\r\n      font-size: 32rpx;\r\n      color: #FF3B69;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n  \r\n  .submit-btn {\r\n    padding: 16rpx 40rpx;\r\n    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\r\n    color: #FFFFFF;\r\n    font-size: 28rpx;\r\n    border-radius: 40rpx;\r\n  }\r\n}\r\n\r\n/* 底部安全区域 */\r\n.safe-area-bottom {\r\n  height: 34px; /* iOS 安全区域高度 */\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/orders/confirm/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted"], "mappings": ";;;;;;;;;;;AA+KA,UAAM,UAAUA,cAAAA,IAAI;AAAA,MAClB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AAGzB,UAAM,cAAcA,cAAAA,IAAI,QAAQ;AAGhC,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,MAAM,SAAS;AACrC,WAAK,eAAe;AAAA,IACtB;AAGA,UAAM,eAAe,CAAC,SAAS;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAC9B,kBAAY,QAAQ;AAAA,IACtB;AAGA,UAAM,mBAAmB,CAAC,SAAS;AACjC,aAAO,KAAK,MAAM,OAAO,CAAC,OAAO,SAAS,QAAQ,KAAK,UAAU,CAAC;AAAA,IACpE;AAGA,UAAM,eAAe,CAAC,SAAS;AAC7B,aAAO,KAAK,MAAM,OAAO,CAAC,OAAO,SAAS,QAAQ,KAAK,QAAQ,KAAK,UAAU,CAAC;AAAA,IACjF;AAGA,UAAM,kBAAkB,MAAM;AAC5B,aAAO,WAAW,MAAM,OAAO,CAAC,OAAO,SAAS;AAC9C,eAAO,QAAQ,aAAa,IAAI;AAAA,MACjC,GAAE,CAAC;AAAA,IACN;AAGA,UAAM,iBAAiB,MAAM;AAC3B,aAAO,WAAW,MAAM,OAAO,CAAC,OAAO,SAAS;AAC9C,eAAO,SAAS,KAAK,eAAe;AAAA,MACrC,GAAE,CAAC;AAAA,IACN;AAGA,UAAM,oBAAoB,MAAM;AAC9B,aAAO,WAAW,MAAM,OAAO,CAAC,OAAO,SAAS;AAC9C,eAAO,SAAS,KAAK,SAAS,KAAK,OAAO,SAAS;AAAA,MACpD,GAAE,CAAC;AAAA,IACN;AAGA,UAAM,iBAAiB,MAAM;AAC3B,aAAO,gBAAiB,IAAG,eAAgB,IAAG,kBAAiB;AAAA,IACjE;AAGA,UAAM,cAAc,MAAM;AAExB,UAAI,CAAC,QAAQ,OAAO;AAClBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,cAAM,UAAU,WAAW,KAAK,IAAG;AAGnCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,8DAA8D,OAAO,WAAW,eAAgB,CAAA;AAAA,QAC3G,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,gBAAgB,CAAC,aAAa;AAElC,iBAAW,QAAQ;AAAA,QACjB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,cAAc;AAAA,UACd,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,QAAQ;AAAA,UACT;AAAA,UACD,OAAO;AAAA,YACL;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,OAAO;AAAA,cACP,UAAU;AAAA,cACV,OAAO;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,cAAc;AAAA,UACd,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,YACL;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,OAAO;AAAA,cACP,UAAU;AAAA,cACV,OAAO;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACL;AAAA,IACA;AAEAC,kBAAAA,UAAU,MAAM;AACd,YAAM,QAAQD,cAAG,MAAC,kBAAmB,EAAC,aAAa,aAC/C,EAAE,MAAM,OAAQ,IAChBA,oBAAI,GAAG,MAAM;AAGA,YAAM,SAAS;AAChC,oBAAsB;AAAA,IACxB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrXD,GAAG,WAAW,eAAe;"}