"use strict";
const activityList = [
  {
    id: "activity-001",
    title: "磁县第二届美食文化节",
    startTime: "2024-04-15 10:00",
    endTime: "2024-04-17 22:00",
    location: "磁县中央广场",
    category: "美食",
    status: "upcoming",
    // upcoming, ongoing, ended
    image: "/static/images/activity/activity-1.jpg",
    organizer: "磁县文化旅游局",
    participants: 256,
    maxParticipants: 500,
    isFree: true,
    isOfficial: true,
    tags: ["美食", "文化", "展览"]
  },
  {
    id: "activity-002",
    title: "磁州窑非遗文化体验活动",
    startTime: "2024-04-10 09:30",
    endTime: "2024-04-10 16:30",
    location: "磁州窑博物馆",
    category: "文化",
    status: "upcoming",
    image: "/static/images/activity/activity-2.jpg",
    organizer: "磁州窑博物馆",
    participants: 78,
    maxParticipants: 100,
    isFree: false,
    price: 20,
    isOfficial: true,
    tags: ["非遗", "文化", "体验"]
  },
  {
    id: "activity-003",
    title: "春季健步走活动",
    startTime: "2024-03-25 07:00",
    endTime: "2024-03-25 09:00",
    location: "磁县人民公园",
    category: "运动",
    status: "upcoming",
    image: "/static/images/activity/activity-3.jpg",
    organizer: "磁县全民健身协会",
    participants: 145,
    maxParticipants: 300,
    isFree: true,
    isOfficial: false,
    tags: ["健身", "户外", "公益"]
  },
  {
    id: "activity-004",
    title: "磁县青年创业分享会",
    startTime: "2024-03-20 14:00",
    endTime: "2024-03-20 17:00",
    location: "磁县青年创业中心",
    category: "创业",
    status: "ongoing",
    image: "/static/images/activity/activity-4.jpg",
    organizer: "磁县青年创业协会",
    participants: 68,
    maxParticipants: 100,
    isFree: true,
    isOfficial: false,
    tags: ["创业", "分享", "交流"]
  },
  {
    id: "activity-005",
    title: "磁县春季招聘会",
    startTime: "2024-03-18 09:00",
    endTime: "2024-03-18 16:00",
    location: "磁县体育馆",
    category: "招聘",
    status: "ended",
    image: "/static/images/activity/activity-5.jpg",
    organizer: "磁县人力资源和社会保障局",
    participants: 520,
    maxParticipants: 1e3,
    isFree: true,
    isOfficial: true,
    tags: ["招聘", "就业", "人才"]
  }
];
const activityCategories = [
  { id: 0, name: "全部" },
  { id: 1, name: "美食" },
  { id: 2, name: "文化" },
  { id: 3, name: "运动" },
  { id: 4, name: "创业" },
  { id: 5, name: "招聘" },
  { id: 6, name: "教育" },
  { id: 7, name: "公益" },
  { id: 8, name: "亲子" }
];
const fetchActivityList = (categoryId = 0, status = "all", page = 1, pageSize = 10) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...activityList];
      if (categoryId !== 0) {
        const categoryMap = {
          1: "美食",
          2: "文化",
          3: "运动",
          4: "创业",
          5: "招聘",
          6: "教育",
          7: "公益",
          8: "亲子"
        };
        result = result.filter((item) => item.category === categoryMap[categoryId]);
      }
      if (status !== "all") {
        result = result.filter((item) => item.status === status);
      }
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const data = result.slice(start, end);
      resolve({
        list: data,
        total: result.length,
        hasMore: end < result.length
      });
    }, 500);
  });
};
const fetchActivityCategories = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(activityCategories);
    }, 300);
  });
};
exports.fetchActivityCategories = fetchActivityCategories;
exports.fetchActivityList = fetchActivityList;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/activity/activityList.js.map
