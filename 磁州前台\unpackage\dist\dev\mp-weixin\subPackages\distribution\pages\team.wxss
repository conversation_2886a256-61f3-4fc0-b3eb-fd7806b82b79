/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.team-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 团队统计卡片 */
.stats-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.stats-header {
  margin-bottom: 30rpx;
}
.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.stats-content {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}
.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.item-value {
  font-size: 40rpx;
  font-weight: 600;
  color: #6B0FBE;
  margin-bottom: 8rpx;
}
.item-label {
  font-size: 24rpx;
  color: #666;
}
.level-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.level-item {
  display: flex;
  align-items: center;
}
.level-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  border-radius: 40rpx;
}
.level-icon.level1 {
  background-color: #6B0FBE;
}
.level-icon.level2 {
  background-color: #409EFF;
}
.level-info {
  display: flex;
  flex-direction: column;
}
.level-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}
.level-count {
  font-size: 24rpx;
  color: #666;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background: #FFFFFF;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.tab-item.active {
  color: #6B0FBE;
  font-weight: 600;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #6B0FBE;
  border-radius: 2rpx;
}

/* 团队成员列表 */
.member-list {
  margin: 0 30rpx;
}
.member-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.member-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}
.member-details {
  flex: 1;
}
.member-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.member-meta {
  display: flex;
  align-items: center;
}
.member-level {
  font-size: 24rpx;
  color: #6B0FBE;
  background-color: rgba(107, 15, 190, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}
.member-time {
  font-size: 24rpx;
  color: #999;
}
.member-stats {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}
.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}
.invite-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 12rpx 60rpx;
  line-height: 1.5;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #666;
}

/* 卡片通用样式 */
.rules-card,
.tips-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card-header {
  margin-bottom: 20rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 规则和提示 */
.rules-content,
.tips-content {
  margin-bottom: 20rpx;
}
.rule-item,
.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.rule-item:last-child,
.tip-item:last-child {
  margin-bottom: 0;
}
.rule-icon,
.tip-icon {
  width: 16rpx;
  height: 16rpx;
  border-radius: 40rpx;
  background-color: #6B0FBE;
  margin-right: 16rpx;
  margin-top: 10rpx;
  flex-shrink: 0;
}
.rule-text,
.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}