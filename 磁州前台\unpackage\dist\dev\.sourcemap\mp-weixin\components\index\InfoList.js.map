{"version": 3, "file": "InfoList.js", "sources": ["components/index/InfoList.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9pbmRleC9JbmZvTGlzdC52dWU"], "sourcesContent": ["<template>\n  <!-- 磁县同城信息模块 - 苹果风格设计 -->\n  <view class=\"info-module\">\n    <!-- 模块标题区 - 苹果风格设计 -->\n    <view class=\"info-header all-info-title-row\">\n      <view class=\"info-title-container\">\n        <view class=\"info-title-bar\"></view>\n        <text class=\"info-title\">磁县同城信息</text>\n        <view class=\"info-badge\">\n          <text class=\"info-badge-text\">本地精选</text>\n        </view>\n      </view>\n      <view class=\"info-publish-btn\" @click=\"navigateTo('/pages/publish/detail')\">\n        <view class=\"info-publish-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"></line>\n            <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\n          </svg>\n        </view>\n        <text class=\"info-publish-text\">发布</text>\n      </view>\n    </view>\n    \n    <!-- 标签导航栏 - 苹果风格设计 -->\n    <view class=\"info-tabs-container\" :class=\"{'sticky-tabs': isSticky}\" :style=\"stickyStyle\">\n      <scroll-view \n        class=\"info-tabs\" \n        scroll-x \n        show-scrollbar=\"false\"\n        :scroll-with-animation=\"true\"\n        :enhanced=\"true\"\n        :bounces=\"true\"\n        :scroll-left=\"tabsScrollLeft\"\n      >\n        <view \n          class=\"info-tab\" \n          :class=\"{active: currentInfoTab === 0}\" \n          @click=\"switchInfoTab(0)\"\n          :id=\"`tab-0`\"\n        >\n          <view class=\"tab-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <polyline points=\"22 12 18 12 15 21 9 3 6 12 2 12\"></polyline>\n            </svg>\n          </view>\n          <text class=\"tab-text\">最新发布</text>\n          <view class=\"tab-line\" v-if=\"currentInfoTab === 0\"></view>\n        </view>\n        <view \n          v-for=\"(cat, idx) in visibleCategories\" \n          :key=\"cat\" \n          class=\"info-tab\" \n          :class=\"{active: currentInfoTab === infoCategories.indexOf(cat)+1}\" \n          @click=\"switchInfoTab(infoCategories.indexOf(cat)+1)\"\n          :id=\"`tab-${infoCategories.indexOf(cat)+1}`\"\n        >\n          <view class=\"tab-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path v-if=\"cat === '招聘信息' || cat === '求职信息'\" d=\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n              <circle v-if=\"cat === '招聘信息' || cat === '求职信息'\" cx=\"8.5\" cy=\"7\" r=\"4\"></circle>\n              <path v-if=\"cat === '房屋出租' || cat === '房屋出售'\" d=\"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path>\n              <polyline v-if=\"cat === '房屋出租' || cat === '房屋出售'\" points=\"9 22 9 12 15 12 15 22\"></polyline>\n              <circle v-if=\"cat === '二手闲置' || cat === '二手车辆'\" cx=\"9\" cy=\"21\" r=\"1\"></circle>\n              <circle v-if=\"cat === '二手闲置' || cat === '二手车辆'\" cx=\"20\" cy=\"21\" r=\"1\"></circle>\n              <path v-if=\"cat === '二手闲置' || cat === '二手车辆'\" d=\"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6\"></path>\n              <path v-if=\"cat === '到家服务' || cat === '寻找服务'\" d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\"></path>\n              <circle v-if=\"cat === '宠物信息'\" cx=\"12\" cy=\"12\" r=\"10\"></circle>\n              <path v-if=\"cat === '宠物信息'\" d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\"></path>\n              <line v-if=\"cat === '宠物信息'\" x1=\"12\" y1=\"17\" x2=\"12.01\" y2=\"17\"></line>\n              <rect v-if=\"cat === '其他服务'\" x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n              <circle v-if=\"cat === '其他服务'\" cx=\"8.5\" cy=\"8.5\" r=\"1.5\"></circle>\n              <polyline v-if=\"cat === '其他服务'\" points=\"21 15 16 10 5 21\"></polyline>\n            </svg>\n          </view>\n          <text class=\"tab-text\">{{cat}}</text>\n          <view class=\"tab-line\" v-if=\"currentInfoTab === infoCategories.indexOf(cat)+1\"></view>\n        </view>\n        \n        <!-- 筛选按钮 -->\n        <view class=\"filter-tab\" @click=\"showFilterOptions\">\n          <view class=\"filter-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <line x1=\"4\" y1=\"21\" x2=\"4\" y2=\"14\"></line>\n              <line x1=\"4\" y1=\"10\" x2=\"4\" y2=\"3\"></line>\n              <line x1=\"12\" y1=\"21\" x2=\"12\" y2=\"12\"></line>\n              <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"3\"></line>\n              <line x1=\"20\" y1=\"21\" x2=\"20\" y2=\"16\"></line>\n              <line x1=\"20\" y1=\"12\" x2=\"20\" y2=\"3\"></line>\n              <line x1=\"1\" y1=\"14\" x2=\"7\" y2=\"14\"></line>\n              <line x1=\"9\" y1=\"8\" x2=\"15\" y2=\"8\"></line>\n              <line x1=\"17\" y1=\"16\" x2=\"23\" y2=\"16\"></line>\n            </svg>\n          </view>\n          <text class=\"filter-text\">筛选</text>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 占位元素，当标签栏吸顶时保持布局 -->\n    <view class=\"tabs-placeholder\" v-if=\"isSticky\" :style=\"{height: tabsHeight + 'rpx'}\"></view>\n    \n    <!-- 信息列表 - 苹果风格设计 -->\n    <view class=\"info-list\">\n      <!-- 置顶提示横幅 - 仅在有置顶信息时显示 -->\n      <view class=\"top-banner\" v-if=\"hasTopItems\">\n        <view class=\"top-banner-content\">\n          <view class=\"top-banner-icon\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <path d=\"M12 2L2 7l10 5 10-5-10-5z\"></path>\n              <path d=\"M2 17l10 5 10-5\"></path>\n              <path d=\"M2 12l10 5 10-5\"></path>\n            </svg>\n          </view>\n          <text class=\"top-banner-text\">置顶信息优先展示，获得更多曝光</text>\n          <view class=\"top-banner-btn\" @click=\"showTopOptions\">\n            <text>我要置顶</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 全部信息列表（包括置顶信息和普通信息） -->\n      <template v-for=\"(item, globalIndex) in combinedInfoList\" :key=\"item.key\">\n        <!-- 信息项内容 - 使用卡片工厂组件 -->\n        <info-card-factory \n          v-if=\"!item.isAd\" \n          :item=\"item\"\n          @click=\"navigateToInfoDetail(item)\"\n        />\n        \n        <!-- 广告位 - 苹果风格设计 -->\n        <view v-else class=\"ad-banner\">\n          <view class=\"ad-content\">\n            <view class=\"ad-image-container\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"ad-svg\">\n                <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\n                <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\n                <text x=\"12\" y=\"12\" text-anchor=\"middle\" font-size=\"6\" fill=\"currentColor\">广告</text>\n              </svg>\n            </view>\n            <view class=\"ad-label\">广告</view>\n          </view>\n        </view>\n      </template>\n      \n      <!-- 加载更多 -->\n      <view class=\"load-more\" v-if=\"hasMoreData\">\n        <view class=\"loading-indicator\">\n          <view class=\"loading-spinner-container\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"loading-spinner-svg\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n              <path d=\"M12 6v6l4 2\"></path>\n            </svg>\n          </view>\n          <text class=\"loading-text\">加载更多</text>\n        </view>\n      </view>\n      \n      <!-- 没有更多数据 -->\n      <view class=\"no-more\" v-else>\n        <text class=\"no-more-text\">已经到底啦</text>\n      </view>\n    </view>\n    \n    <!-- 置顶选项弹窗 -->\n    <view class=\"top-options-popup\" v-if=\"showTopOptionsModal\" @click=\"hideTopOptions\">\n      <view class=\"top-options-content\" @click.stop>\n        <view class=\"top-options-header\">\n          <text class=\"top-options-title\">选择置顶方式</text>\n          <view class=\"top-options-close\" @click=\"hideTopOptions\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n              <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n              <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"top-options-body\">\n          <view class=\"top-option\" @click=\"selectTopOption('paid')\">\n            <view class=\"top-option-icon paid-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"23\"></line>\n                <path d=\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"></path>\n              </svg>\n            </view>\n            <view class=\"top-option-info\">\n              <text class=\"top-option-title\">付费置顶</text>\n              <text class=\"top-option-desc\">使用余额支付，立即置顶信息</text>\n            </view>\n            <view class=\"top-option-action\">\n              <text class=\"top-action-text\">去支付</text>\n              <view class=\"top-action-arrow\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <polyline points=\"9 18 15 12 9 6\"></polyline>\n                </svg>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"top-option\" @click=\"selectTopOption('ad')\">\n            <view class=\"top-option-icon ad-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n                <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\n                <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\n              </svg>\n            </view>\n            <view class=\"top-option-info\">\n              <text class=\"top-option-title\">看广告置顶</text>\n              <text class=\"top-option-desc\">观看广告视频，免费获得置顶机会</text>\n            </view>\n            <view class=\"top-option-action\">\n              <text class=\"top-action-text\">去观看</text>\n              <view class=\"top-action-arrow\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <polyline points=\"9 18 15 12 9 6\"></polyline>\n                </svg>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"top-options-footer\">\n          <text class=\"top-options-tips\">置顶信息将获得更多曝光和更高的点击率</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, watch, nextTick, onMounted } from 'vue';\nimport { onPageScroll } from '@dcloudio/uni-app';\nimport InfoCardFactory from '../cards/InfoCardFactory.vue';\n\nconst props = defineProps({\n  allInfoList: {\n    type: Array,\n    default: () => []\n  },\n  toppedInfoList: {\n    type: Array,\n    default: () => []\n  },\n  adBanner: {\n    type: Object,\n    default: null\n  }\n});\n\nconst emit = defineEmits(['tab-change']);\n\n// 状态变量\nconst currentInfoTab = ref(0);\nconst visibleCategories = ref([\n  '到家服务', '寻找服务', '生意转让', '招聘信息', '求职信息',\n  '房屋出租', '房屋出售', '二手车辆', '宠物信息', '商家活动',\n  '婚恋交友', '车辆服务', '二手闲置', '磁州拼车', '教育培训', '其他服务'\n]);\nconst infoCategories = ref([\n  '到家服务', '寻找服务', '生意转让', '招聘信息', '求职信息',\n  '房屋出租', '房屋出售', '二手车辆', '宠物信息', '商家活动',\n  '婚恋交友', '车辆服务', '二手闲置', '磁州拼车', '教育培训', '其他服务'\n]);\n\n// 吸顶相关\nconst isSticky = ref(false);\nconst stickyTop = ref(0);\nconst tabsHeight = ref(88);\nconst tabsScrollLeft = ref(0);\n\n// 置顶选项弹窗\nconst showTopOptionsModal = ref(false);\n\n// 加载更多\nconst hasMoreData = ref(true);\n\n// 计算属性\nconst combinedInfoList = computed(() => {\n  let toppedItems = [];\n  let normalItems = [];\n  \n  if (currentInfoTab.value === 0) {\n    toppedItems = props.toppedInfoList || [];\n    normalItems = props.allInfoList || [];\n  } else {\n    const selectedCategory = infoCategories.value[currentInfoTab.value - 1];\n    toppedItems = (props.toppedInfoList || []).filter(item => item.category === selectedCategory);\n    normalItems = (props.allInfoList || []).filter(item => item.category === selectedCategory);\n  }\n\n  toppedItems = toppedItems.map((item, index) => ({\n    ...item,\n    isTopped: true,\n    key: `top-${item.id || index}-${Date.now()}`\n  }));\n  normalItems = normalItems.map((item, index) => ({\n    ...item,\n    isTopped: false,\n    key: `normal-${item.id || index}-${Date.now()}`\n  }));\n  \n  const allItems = [...toppedItems, ...normalItems];\n  const result = [];\n  let uniqueCounter = 0;\n  \n  const adImages = [\n    '/static/images/banner/ad-banner.jpg',\n    '/static/images/banner/banner-1.png',\n    '/static/images/banner/banner-2.png',\n    '/static/images/banner/banner-3.jpg'\n  ];\n  \n  if (props.adBanner && props.adBanner.image) {\n    adImages.unshift(props.adBanner.image);\n  }\n  \n  allItems.forEach((item, index) => {\n    result.push(item);\n    if ((index + 1) % 5 === 0) {\n      const ad = {\n        isAd: true,\n        image: adImages[uniqueCounter % adImages.length],\n        key: `ad-${uniqueCounter++}-${Date.now()}`\n      };\n      result.push(ad);\n    }\n  });\n  return result;\n});\n\nconst stickyStyle = computed(() => ({\n  top: `${stickyTop.value}px`\n}));\n\nconst hasTopItems = computed(() => {\n  return props.toppedInfoList && props.toppedInfoList.length > 0;\n});\n\n// 方法\nfunction setSticky(sticky, top = 0) {\n  isSticky.value = sticky;\n  stickyTop.value = top;\n}\n\nfunction switchInfoTab(index) {\n  if (currentInfoTab.value === index) return;\n  currentInfoTab.value = index;\n  emit('tab-change', {\n    index,\n    name: index === 0 ? '最新发布' : infoCategories.value[index - 1]\n  });\n\n  nextTick(() => {\n    const query = uni.createSelectorQuery();\n    query.select(`#tab-${index}`).boundingClientRect(data => {\n      if (data) {\n        const screenWidth = uni.getSystemInfoSync().windowWidth;\n        const scrollTarget = data.left - screenWidth / 2 + data.width / 2;\n        tabsScrollLeft.value = scrollTarget;\n      }\n    }).exec();\n  });\n}\n\nfunction navigateTo(url) {\n  if (!url) return;\n  \n  uni.navigateTo({\n    url: url,\n    fail: (err) => {\n      console.error('页面跳转失败:', err);\n      uni.showToast({\n        title: '页面开发中',\n        icon: 'none'\n      });\n    }\n  });\n}\n\nfunction navigateToInfoDetail(item) {\n  // 详情页路由映射\n  const detailPageMap = {\n    '招聘信息': 'job-detail',\n    '求职信息': 'job-seeking-detail',\n    '房屋出租': 'house-rent-detail',\n    '房屋出售': 'house-sale-detail',\n    '二手车辆': 'car-detail',\n    '宠物信息': 'pet-detail',\n    '二手闲置': 'second-hand-detail',\n    '婚恋交友': 'dating-detail',\n    '商家活动': 'merchant-activity-detail',\n    '磁州拼车': 'carpool-detail',\n    '教育培训': 'education-detail',\n    '到家服务': 'home-service-detail',\n    '寻找服务': 'find-service-detail',\n    '生意转让': 'business-transfer-detail',\n    // 默认跳转到通用信息详情页\n    'default': 'info-detail'\n  };\n\n  const pageType = detailPageMap[item.category] || detailPageMap['default'];\n\n  // 构建参数\n  let params = {\n    id: item.id,\n    category: encodeURIComponent(item.category || '')\n  };\n  \n  // 构建URL\n  const url = `/pages/publish/${pageType}?${Object.entries(params)\n    .map(([key, value]) => `${key}=${value}`)\n    .join('&')}`;\n  \n  console.log('跳转到详情页:', url);\n  navigateTo(url);\n}\n\nfunction showTopOptions() {\n  showTopOptionsModal.value = true;\n}\n\nfunction hideTopOptions() {\n  showTopOptionsModal.value = false;\n}\n\nfunction selectTopOption(type) {\n  if (type === 'paid') {\n    navigateTo('/pages/top/paid');\n  } else if (type === 'ad') {\n    // 显示广告\n    uni.showToast({\n      title: '正在加载广告...',\n      icon: 'loading'\n    });\n    \n    setTimeout(() => {\n      uni.hideToast();\n      uni.showModal({\n        title: '广告观看完成',\n        content: '恭喜您获得2小时置顶特权！',\n        showCancel: false,\n        success: () => {\n          hideTopOptions();\n        }\n      });\n    }, 1500);\n  }\n}\n\n// 点赞信息\nfunction likeInfo(item) {\n  console.log('点赞信息:', item.id);\n  // 实际应用中应该调用API进行点赞\n  uni.showToast({\n    title: '点赞成功',\n    icon: 'success'\n  });\n}\n\n// 评论信息\nfunction commentInfo(item) {\n  console.log('评论信息:', item.id);\n  navigateTo(`/pages/publish/comment?id=${item.id}&category=${encodeURIComponent(item.category || '')}`);\n}\n\n// 分享信息\nfunction shareInfo(item) {\n  console.log('分享信息:', item.id);\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 0,\n    title: item.content,\n    summary: `来自磁州生活网的${item.category}信息`,\n    imageUrl: item.images && item.images.length > 0 ? item.images[0] : '',\n    success: function() {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      });\n    },\n    fail: function() {\n      uni.showToast({\n        title: '分享失败',\n        icon: 'none'\n      });\n    }\n  });\n}\n\n// 添加方法\nfunction showFilterOptions() {\n  uni.showToast({\n    title: '筛选功能开发中',\n    icon: 'none'\n  });\n}\n\n// 优化吸顶效果\nconst tabsTop = ref(0);\nconst scrollTimer = ref(null);\n\nonMounted(() => {\n  // 设置初始值\n  isSticky.value = false;\n  tabsHeight.value = 80;\n  \n  // 获取标签栏高度\n  nextTick(() => {\n    const query = uni.createSelectorQuery();\n    query.select('.info-tabs-container').boundingClientRect(data => {\n      if (data) {\n        tabsHeight.value = data.height;\n        tabsTop.value = data.top;\n      }\n    }).exec();\n  });\n})\n\n// 注意：setSticky方法已在前面定义\n\n// 暴露方法给父组件\ndefineExpose({\n  setSticky\n});\n</script>\n\n<style lang=\"scss\" scoped>\n/* 磁县同城信息模块 - 苹果风格设计 */\n.info-module {\n  margin: 24rpx;\n  border-radius: 35rpx;\n  background-color: #FFFFFF;\n  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12), 0 6rpx 16rpx rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n  transform: translateZ(0);\n  backface-visibility: hidden;\n  will-change: transform;\n}\n\n/* 模块标题区 */\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx;\n  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);\n}\n\n.info-title-container {\n  display: flex;\n  align-items: center;\n}\n\n.info-title-bar {\n  width: 6rpx;\n  height: 32rpx;\n  background: linear-gradient(180deg, #007AFF, #5AC8FA);\n  border-radius: 3rpx;\n  margin-right: 16rpx;\n}\n\n.info-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #000000;\n  letter-spacing: 0.8rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.info-badge {\n  margin-left: 12rpx;\n  background-color: rgba(0, 122, 255, 0.1);\n  border-radius: 12rpx;\n  padding: 4rpx 12rpx;\n}\n\n.info-badge-text {\n  font-size: 20rpx;\n  color: #007AFF;\n}\n\n.info-publish-btn {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n  border-radius: 30rpx;\n  padding: 12rpx 24rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n.info-publish-icon {\n  color: #FFFFFF;\n  margin-right: 8rpx;\n}\n\n.info-publish-text {\n  font-size: 26rpx;\n  color: #FFFFFF;\n  font-weight: 500;\n}\n\n/* 标签导航栏 */\n.info-tabs-container {\n  width: 100%;\n  background-color: #FFFFFF;\n  padding: 10rpx 0;\n  z-index: 100;\n  transition: all 0.3s;\n}\n\n.sticky-tabs {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.info-tabs {\n  white-space: nowrap;\n  width: 100%;\n  height: 80rpx;\n}\n\n.info-tab {\n  display: inline-flex;\n  align-items: center;\n  padding: 12rpx 24rpx;\n  margin: 0 8rpx;\n  position: relative;\n  transition: all 0.3s;\n}\n\n.info-tab:first-child {\n  margin-left: 20rpx;\n}\n\n.tab-icon {\n  margin-right: 6rpx;\n  color: #8E8E93;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.tab-text {\n  font-size: 26rpx;\n  color: #636366;\n  font-weight: 500;\n}\n\n.info-tab.active .tab-text {\n  color: #007AFF;\n  font-weight: 600;\n}\n\n.info-tab.active .tab-icon {\n  color: #007AFF;\n}\n\n.tab-line {\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 30rpx;\n  height: 4rpx;\n  background: #007AFF;\n  border-radius: 2rpx;\n}\n\n.filter-tab {\n  display: inline-flex;\n  align-items: center;\n  padding: 12rpx 24rpx;\n  margin: 0 20rpx;\n  background: linear-gradient(135deg, #F2F2F7, #E5E5EA);\n  border-radius: 35rpx;\n  color: #636366;\n}\n\n.filter-icon {\n  margin-right: 6rpx;\n  color: #636366;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.filter-text {\n  font-size: 26rpx;\n  font-weight: 500;\n}\n\n/* 占位元素 */\n.tabs-placeholder {\n  width: 100%;\n}\n\n/* 置顶标识 - 重新设计 */\n.top-indicator {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 2;\n}\n\n.top-badge {\n  display: flex;\n  align-items: center;\n  padding: 10rpx 20rpx;\n  border-bottom-left-radius: 24rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\n  transform: translateZ(0);\n  backdrop-filter: blur(10rpx);\n  -webkit-backdrop-filter: blur(10rpx);\n}\n\n.paid-badge {\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n  box-shadow: 0 8rpx 20rpx rgba(255, 59, 48, 0.2), 0 4rpx 8rpx rgba(255, 59, 48, 0.1);\n}\n\n.ad-badge {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.2), 0 4rpx 8rpx rgba(0, 122, 255, 0.1);\n}\n\n.top-badge-icon {\n  color: #FFFFFF;\n  margin-right: 10rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.top-badge-icon svg {\n  width: 14px;\n  height: 14px;\n}\n\n.top-badge-text {\n  font-size: 24rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n  letter-spacing: 0.5rpx;\n  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 置顶提示横幅 */\n.top-banner {\n  margin: 20rpx 24rpx 0;\n  border-radius: 24rpx;\n  background: linear-gradient(135deg, rgba(255, 204, 0, 0.15), rgba(255, 149, 0, 0.15));\n  overflow: hidden;\n  box-shadow: 0 6rpx 16rpx rgba(255, 149, 0, 0.1);\n}\n\n.top-banner-content {\n  display: flex;\n  align-items: center;\n  padding: 16rpx 24rpx;\n}\n\n.top-banner-icon {\n  color: #FF9500;\n  margin-right: 12rpx;\n  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));\n}\n\n.top-banner-text {\n  flex: 1;\n  font-size: 26rpx;\n  color: #FF9500;\n  font-weight: 500;\n}\n\n.top-banner-btn {\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n  border-radius: 20rpx;\n  padding: 8rpx 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.2);\n}\n\n.top-banner-btn text {\n  font-size: 24rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n}\n\n/* 信息列表 */\n.info-list {\n  padding: 16rpx 24rpx 24rpx;\n}\n\n/* 信息项 - 高级感苹果风格重新设计 */\n.info-item {\n  margin-bottom: 24rpx;\n  border-radius: 35rpx;\n  background-color: #FFFFFF;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n  border: none;\n  overflow: hidden;\n  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;\n  transform: translateZ(0) perspective(1000px) rotateX(0deg);\n  backface-visibility: hidden;\n  padding: 28rpx 24rpx;\n}\n\n.info-item:active {\n  transform: translateY(2rpx) scale(0.98);\n  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\n}\n\n/* 置顶项特殊样式 */\n.topped-item {\n  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);\n  box-shadow: 0 14rpx 36rpx rgba(255, 149, 0, 0.16), 0 6rpx 16rpx rgba(255, 149, 0, 0.1);\n  border-left: 6rpx solid #FF9500;\n}\n\n.paid-top {\n  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);\n  border-left: 6rpx solid #FF9500;\n  box-shadow: 0 14rpx 36rpx rgba(255, 149, 0, 0.16), 0 6rpx 16rpx rgba(255, 149, 0, 0.1);\n}\n\n.ad-top {\n  background: linear-gradient(135deg, #FFFFFF, #F0F7FF);\n  border-left: 6rpx solid #007AFF;\n  box-shadow: 0 14rpx 36rpx rgba(0, 122, 255, 0.16), 0 6rpx 16rpx rgba(0, 122, 255, 0.1);\n}\n\n/* 内容区 - 高级感重新设计 */\n.info-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n}\n\n/* 删除重复的卡片样式 */\n/* 确保卡片大小不变 */\n/* .info-item {\n  padding: 24rpx 20rpx;\n  margin-bottom: 20rpx;\n  border-radius: 16rpx;\n  background-color: #ffffff;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n  position: relative;\n  overflow: hidden;\n  transition: all 0.2s ease;\n} */\n\n/* 头部信息 - 高级感重新设计 */\n.info-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.info-category {\n  background: linear-gradient(135deg, #0062CC, #0091E6);\n  border-radius: 10rpx;\n  padding: 4rpx 12rpx;\n  border: 0.5rpx solid rgba(0, 98, 204, 0.2);\n  box-shadow: 0 4rpx 8rpx rgba(0, 98, 204, 0.15);\n  transform: translateZ(0);\n  margin-right: 12rpx;\n  display: inline-flex;\n  align-items: center;\n}\n\n.topped-category {\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n  border: 0.5rpx solid rgba(255, 59, 48, 0.2);\n  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.15);\n}\n\n.category-text {\n  font-size: 22rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.topped-category .category-text {\n  color: #FFFFFF;\n}\n\n.info-time {\n  font-size: 22rpx;\n  color: #8E8E93;\n  font-weight: 400;\n  background-color: rgba(142, 142, 147, 0.08);\n  padding: 4rpx 12rpx;\n  border-radius: 10rpx;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n/* 主要内容 - 高级感重新设计 */\n.info-main {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-top: 12rpx;\n  margin-bottom: 20rpx;\n  gap: 20rpx;\n  width: 100%;\n}\n\n.info-content-wrapper {\n  flex: 1;\n  overflow: hidden;\n  min-width: 0; /* 确保文本可以正确换行 */\n}\n\n.info-text {\n  font-size: 30rpx;\n  color: #1C1C1E;\n  line-height: 1.5;\n  margin-bottom: 12rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  word-break: break-all; /* 确保长文本可以正确换行 */\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.bold-text {\n  font-weight: 600;\n  color: #000000;\n}\n\n/* 右侧图片样式 */\n.info-images-right {\n  width: 180rpx;\n  height: 180rpx;\n  flex-shrink: 0;\n  position: relative;\n  margin-left: 12rpx;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);\n}\n\n.info-image-container-right {\n  width: 100%;\n  height: 100%;\n  border-radius: 16rpx;\n  overflow: hidden;\n  background-color: #f2f2f7;\n  position: relative;\n}\n\n.info-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  border-radius: 16rpx;\n  transition: transform 0.3s ease;\n}\n\n.info-image:active {\n  transform: scale(1.05);\n}\n\n.image-count-right {\n  position: absolute;\n  bottom: 10rpx;\n  right: 10rpx;\n  background-color: rgba(0, 0, 0, 0.6);\n  color: white;\n  font-size: 20rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 10rpx;\n  z-index: 2;\n  backdrop-filter: blur(10rpx);\n  -webkit-backdrop-filter: blur(10rpx);\n}\n\n/* 隐藏旧的图片布局 */\n.info-images {\n  display: none;\n}\n\n/* 内容标签 - 新增设计元素 */\n.info-tags {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -4rpx 16rpx;\n}\n\n.info-tag {\n  background-color: rgba(0, 122, 255, 0.08);\n  border-radius: 20rpx;\n  padding: 6rpx 16rpx;\n  margin: 6rpx;\n  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05), 0 1rpx 2rpx rgba(255, 255, 255, 0.8);\n}\n\n.info-tag-text {\n  font-size: 22rpx;\n  color: #007AFF;\n  font-weight: 500;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n/* 图片预览 - 高级感重新设计 */\n.info-images {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 14rpx -4rpx 0; /* 从20rpx -6rpx 0减少到14rpx -4rpx 0 */\n  position: relative;\n}\n\n.info-image-container {\n  width: calc(33.33% - 8rpx); /* 从calc(33.33% - 12rpx)减少到calc(33.33% - 8rpx) */\n  height: 160rpx; /* 从200rpx减少到160rpx */\n  margin: 4rpx; /* 从6rpx减少到4rpx */\n  border-radius: 16rpx; /* 从24rpx减少到16rpx */\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08); /* 减轻阴影效果 */\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  border: 1rpx solid rgba(255, 255, 255, 0.8); /* 从2rpx减少到1rpx */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #f5f7fa, #e4e8f0);\n  overflow: hidden;\n  position: relative;\n}\n\n.info-image-container:active {\n  transform: scale(0.96);\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n}\n\n.info-image-svg {\n  width: 40%;\n  height: 40%;\n  color: #8E8E93;\n  opacity: 0.5;\n  position: absolute;\n  z-index: 1;\n}\n\n.single-image {\n  width: 70%; /* 从75%减少到70% */\n  height: 240rpx; /* 从320rpx减少到240rpx */\n}\n\n.double-image {\n  width: calc(50% - 8rpx); /* 从calc(50% - 12rpx)减少到calc(50% - 8rpx) */\n  height: 200rpx; /* 从260rpx减少到200rpx */\n}\n\n.image-count {\n  position: absolute;\n  right: 16rpx;\n  bottom: 16rpx;\n  background: rgba(0, 0, 0, 0.6);\n  backdrop-filter: blur(10rpx);\n  color: #FFFFFF;\n  font-size: 24rpx;\n  font-weight: 600;\n  padding: 6rpx 16rpx;\n  border-radius: 24rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 信息状态标签 - 新增设计元素 */\n.info-status {\n  position: absolute;\n  top: 24rpx;\n  left: 24rpx;\n  background: rgba(0, 0, 0, 0.7);\n  backdrop-filter: blur(10rpx);\n  -webkit-backdrop-filter: blur(10rpx);\n  border-radius: 20rpx;\n  padding: 6rpx 16rpx;\n  z-index: 2;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);\n}\n\n.info-status-text {\n  font-size: 22rpx;\n  color: #FFFFFF;\n  font-weight: 600;\n  letter-spacing: 0.5rpx;\n}\n\n/* 底部信息 - 高级感重新设计 */\n.info-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 16rpx;\n  border-top: 0.5rpx solid rgba(60, 60, 67, 0.1);\n  position: relative;\n  z-index: 1;\n  margin-top: 4rpx;\n}\n\n.info-user {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 8rpx 16rpx;\n  border-radius: 24rpx;\n  box-shadow: inset 0 0.5rpx 2rpx rgba(0, 0, 0, 0.05);\n  backdrop-filter: blur(5rpx);\n  -webkit-backdrop-filter: blur(5rpx);\n}\n\n.user-avatar-container {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 50%;\n  margin-right: 10rpx;\n  border: 0.5rpx solid #FFFFFF;\n  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0, 122, 255, 0.1);\n  overflow: hidden;\n}\n\n.user-avatar-svg {\n  width: 24px;\n  height: 24px;\n  color: #007AFF;\n}\n\n.user-name {\n  font-size: 24rpx;\n  color: #636366;\n  font-weight: 500;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.info-stats {\n  display: flex;\n  align-items: center;\n}\n\n.info-views {\n  display: flex;\n  align-items: center;\n  margin-right: 16rpx;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 8rpx 16rpx;\n  border-radius: 24rpx;\n  box-shadow: inset 0 0.5rpx 2rpx rgba(0, 0, 0, 0.05);\n  backdrop-filter: blur(5rpx);\n  -webkit-backdrop-filter: blur(5rpx);\n}\n\n.view-icon {\n  color: #636366;\n  margin-right: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.view-icon svg {\n  width: 14px;\n  height: 14px;\n}\n\n.view-count {\n  font-size: 24rpx;\n  color: #636366;\n  font-weight: 500;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n/* 红包信息 - 高级感重新设计 */\n.info-redpacket {\n  display: flex;\n  align-items: center;\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n  border-radius: 24rpx;\n  padding: 8rpx 16rpx;\n  box-shadow: 0 6rpx 16rpx rgba(255, 59, 48, 0.2), 0 2rpx 6rpx rgba(255, 59, 48, 0.1);\n  transform: translateZ(0);\n  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;\n}\n\n.info-redpacket:active {\n  transform: scale(0.96);\n  box-shadow: 0 3rpx 8rpx rgba(255, 59, 48, 0.15);\n}\n\n.redpacket-icon {\n  color: #FFFFFF;\n  margin-right: 10rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.redpacket-icon svg {\n  width: 14px;\n  height: 14px;\n}\n\n.redpacket-amount {\n  font-size: 26rpx;\n  color: #FFFFFF;\n  font-weight: 700;\n  margin-right: 10rpx;\n  letter-spacing: 1rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.redpacket-btn {\n  background-color: #FFFFFF;\n  border-radius: 50%;\n  width: 36rpx;\n  height: 36rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n}\n\n.redpacket-btn text {\n  font-size: 22rpx;\n  color: #FF3B30;\n  font-weight: 700;\n}\n\n/* 互动按钮组 - 新增设计元素 */\n.info-actions {\n  display: flex;\n  justify-content: space-around;\n  margin-top: 16rpx;\n  padding-top: 16rpx;\n  border-top: 0.5rpx solid rgba(60, 60, 67, 0.1);\n  position: relative;\n}\n\n.info-actions:before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 0.5rpx;\n  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));\n}\n\n.info-action {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8rpx 20rpx;\n  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);\n  background-color: rgba(0, 0, 0, 0.02);\n  border-radius: 24rpx;\n  box-shadow: inset 0 0.5rpx 2rpx rgba(0, 0, 0, 0.05);\n  margin: 0 6rpx;\n}\n\n.info-action:active {\n  transform: scale(0.92);\n  background-color: rgba(0, 0, 0, 0.04);\n}\n\n.info-action-icon {\n  color: #8E8E93;\n  margin-right: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.info-action-icon svg {\n  width: 16px;\n  height: 16px;\n}\n\n.info-action-text {\n  font-size: 24rpx;\n  color: #8E8E93;\n  font-weight: 500;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n/* 广告位 */\n.ad-banner {\n  margin-bottom: 24rpx;\n  border-radius: 35rpx;\n  overflow: hidden;\n  position: relative;\n  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.12), 0 6rpx 16rpx rgba(0, 0, 0, 0.08);\n  transform: translateZ(0);\n  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;\n}\n\n.ad-banner:active {\n  transform: translateY(2rpx) scale(0.98);\n  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);\n}\n\n.ad-content {\n  width: 100%;\n  position: relative;\n}\n\n.ad-image-container {\n  width: 100%;\n  height: 180rpx;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #f5f7fa, #e4e8f0);\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.ad-svg {\n  width: 60%;\n  height: 60%;\n  color: #8E8E93;\n  opacity: 0.7;\n  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));\n}\n\n.ad-label {\n  position: absolute;\n  right: 16rpx;\n  bottom: 16rpx;\n  background-color: rgba(0, 0, 0, 0.6);\n  color: #FFFFFF;\n  font-size: 22rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 10rpx;\n  backdrop-filter: blur(10rpx);\n  -webkit-backdrop-filter: blur(10rpx);\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 加载更多 */\n.load-more {\n  padding: 30rpx 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.loading-indicator {\n  display: flex;\n  align-items: center;\n  background-color: rgba(0, 0, 0, 0.03);\n  padding: 12rpx 24rpx;\n  border-radius: 30rpx;\n  box-shadow: inset 0 0.5rpx 3rpx rgba(0, 0, 0, 0.05), 0 1rpx 2rpx rgba(255, 255, 255, 0.8);\n}\n\n.loading-spinner-container {\n  width: 36rpx;\n  height: 36rpx;\n  margin-right: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.loading-spinner-svg {\n  width: 28rpx;\n  height: 28rpx;\n  color: #007AFF;\n  animation: spin 1s linear infinite;\n  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  font-weight: 500;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n/* 没有更多数据 */\n.no-more {\n  padding: 30rpx 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.no-more-text {\n  font-size: 28rpx;\n  color: #8E8E93;\n  font-weight: 500;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n  background-color: rgba(0, 0, 0, 0.03);\n  padding: 12rpx 24rpx;\n  border-radius: 30rpx;\n  box-shadow: inset 0 0.5rpx 3rpx rgba(0, 0, 0, 0.05), 0 1rpx 2rpx rgba(255, 255, 255, 0.8);\n}\n\n/* 置顶选项弹窗 */\n.top-options-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  backdrop-filter: blur(10rpx);\n  -webkit-backdrop-filter: blur(10rpx);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n}\n\n.top-options-content {\n  width: 650rpx;\n  background-color: #FFFFFF;\n  border-radius: 35rpx;\n  overflow: hidden;\n  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.2);\n  animation: popup 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);\n}\n\n@keyframes popup {\n  0% {\n    opacity: 0;\n    transform: scale(0.9) translateY(20rpx);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n.top-options-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 28rpx;\n  border-bottom: 0.5rpx solid rgba(60, 60, 67, 0.1);\n}\n\n.top-options-title {\n  font-size: 34rpx;\n  font-weight: 600;\n  color: #000000;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.top-options-close {\n  width: 52rpx;\n  height: 52rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #8E8E93;\n  background-color: rgba(0, 0, 0, 0.03);\n  border-radius: 50%;\n  transition: all 0.2s ease;\n}\n\n.top-options-close:active {\n  background-color: rgba(0, 0, 0, 0.08);\n  transform: scale(0.95);\n}\n\n.top-options-body {\n  padding: 28rpx;\n}\n\n.top-option {\n  display: flex;\n  align-items: center;\n  padding: 28rpx;\n  margin-bottom: 24rpx;\n  border-radius: 24rpx;\n  background-color: #F2F2F7;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;\n}\n\n.top-option:active {\n  transform: scale(0.98);\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);\n}\n\n.top-option:last-child {\n  margin-bottom: 0;\n}\n\n.top-option-icon {\n  width: 90rpx;\n  height: 90rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);\n}\n\n.paid-icon {\n  background: linear-gradient(135deg, #FF9500, #FF3B30);\n  color: #FFFFFF;\n}\n\n.ad-icon {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n  color: #FFFFFF;\n}\n\n.top-option-info {\n  flex: 1;\n}\n\n.top-option-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #000000;\n  margin-bottom: 6rpx;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.top-option-desc {\n  font-size: 26rpx;\n  color: #8E8E93;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.top-option-action {\n  display: flex;\n  align-items: center;\n  background-color: rgba(0, 122, 255, 0.1);\n  padding: 10rpx 20rpx;\n  border-radius: 24rpx;\n}\n\n.top-action-text {\n  font-size: 28rpx;\n  color: #007AFF;\n  font-weight: 500;\n  margin-right: 8rpx;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n.top-action-arrow {\n  color: #007AFF;\n}\n\n.top-options-footer {\n  padding: 28rpx;\n  border-top: 0.5rpx solid rgba(60, 60, 67, 0.1);\n  display: flex;\n  justify-content: center;\n  background-color: rgba(0, 0, 0, 0.01);\n}\n\n.top-options-tips {\n  font-size: 26rpx;\n  color: #8E8E93;\n  text-align: center;\n  letter-spacing: 0.5rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n}\n\n/* 房屋出租信息简洁展示 */\n.house-rent-info {\n  margin-top: 12rpx;\n  margin-bottom: 12rpx;\n  padding: 16rpx;\n  border-radius: 16rpx;\n  background-color: rgba(0, 0, 0, 0.03);\n  backdrop-filter: blur(5rpx);\n  -webkit-backdrop-filter: blur(5rpx);\n  box-shadow: inset 0 1rpx 5rpx rgba(0, 0, 0, 0.05);\n}\n\n.house-rent-detail {\n  display: flex;\n  justify-content: space-between;\n}\n\n.house-rent-item {\n  display: flex;\n  align-items: center;\n}\n\n.house-icon {\n  color: #636366;\n  margin-right: 8rpx;\n  display: flex;\n  align-items: center;\n}\n\n.house-text {\n  font-size: 22rpx;\n  color: #636366;\n  font-weight: 500;\n  letter-spacing: 0.5rpx;\n}\n</style>", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/index/InfoList.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "nextTick", "uni", "onMounted"], "mappings": ";;;;;;;;;;;;;;AAyOA,MAAM,kBAAkB,MAAW;;;;;;;;;;;;;;;;;;;AAEnC,UAAM,QAAQ;AAed,UAAM,OAAO;AAGb,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAC5B,UAAM,oBAAoBA,cAAAA,IAAI;AAAA,MAC5B;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAChC;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAChC;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,IAC1C,CAAC;AACD,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAChC;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAChC;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,IAC1C,CAAC;AAGD,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAC1B,UAAM,YAAYA,cAAAA,IAAI,CAAC;AACvB,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAG5B,UAAM,sBAAsBA,cAAAA,IAAI,KAAK;AAGrC,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAG5B,UAAM,mBAAmBC,cAAQ,SAAC,MAAM;AACtC,UAAI,cAAc,CAAA;AAClB,UAAI,cAAc,CAAA;AAElB,UAAI,eAAe,UAAU,GAAG;AAC9B,sBAAc,MAAM,kBAAkB;AACtC,sBAAc,MAAM,eAAe;MACvC,OAAS;AACL,cAAM,mBAAmB,eAAe,MAAM,eAAe,QAAQ,CAAC;AACtE,uBAAe,MAAM,kBAAkB,CAAE,GAAE,OAAO,UAAQ,KAAK,aAAa,gBAAgB;AAC5F,uBAAe,MAAM,eAAe,CAAE,GAAE,OAAO,UAAQ,KAAK,aAAa,gBAAgB;AAAA,MAC1F;AAED,oBAAc,YAAY,IAAI,CAAC,MAAM,WAAW;AAAA,QAC9C,GAAG;AAAA,QACH,UAAU;AAAA,QACV,KAAK,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAG,CAAE;AAAA,MAC3C,EAAC;AACF,oBAAc,YAAY,IAAI,CAAC,MAAM,WAAW;AAAA,QAC9C,GAAG;AAAA,QACH,UAAU;AAAA,QACV,KAAK,UAAU,KAAK,MAAM,KAAK,IAAI,KAAK,IAAG,CAAE;AAAA,MAC9C,EAAC;AAEF,YAAM,WAAW,CAAC,GAAG,aAAa,GAAG,WAAW;AAChD,YAAM,SAAS,CAAA;AACf,UAAI,gBAAgB;AAEpB,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAEE,UAAI,MAAM,YAAY,MAAM,SAAS,OAAO;AAC1C,iBAAS,QAAQ,MAAM,SAAS,KAAK;AAAA,MACtC;AAED,eAAS,QAAQ,CAAC,MAAM,UAAU;AAChC,eAAO,KAAK,IAAI;AAChB,aAAK,QAAQ,KAAK,MAAM,GAAG;AACzB,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,OAAO,SAAS,gBAAgB,SAAS,MAAM;AAAA,YAC/C,KAAK,MAAM,eAAe,IAAI,KAAK,IAAG,CAAE;AAAA,UAChD;AACM,iBAAO,KAAK,EAAE;AAAA,QACf;AAAA,MACL,CAAG;AACD,aAAO;AAAA,IACT,CAAC;AAED,UAAM,cAAcA,cAAQ,SAAC,OAAO;AAAA,MAClC,KAAK,GAAG,UAAU,KAAK;AAAA,IACzB,EAAE;AAEF,UAAM,cAAcA,cAAQ,SAAC,MAAM;AACjC,aAAO,MAAM,kBAAkB,MAAM,eAAe,SAAS;AAAA,IAC/D,CAAC;AAGD,aAAS,UAAU,QAAQ,MAAM,GAAG;AAClC,eAAS,QAAQ;AACjB,gBAAU,QAAQ;AAAA,IACpB;AAEA,aAAS,cAAc,OAAO;AAC5B,UAAI,eAAe,UAAU;AAAO;AACpC,qBAAe,QAAQ;AACvB,WAAK,cAAc;AAAA,QACjB;AAAA,QACA,MAAM,UAAU,IAAI,SAAS,eAAe,MAAM,QAAQ,CAAC;AAAA,MAC/D,CAAG;AAEDC,oBAAAA,WAAS,MAAM;AACb,cAAM,QAAQC,oBAAI;AAClB,cAAM,OAAO,QAAQ,KAAK,EAAE,EAAE,mBAAmB,UAAQ;AACvD,cAAI,MAAM;AACR,kBAAM,cAAcA,cAAAA,MAAI,kBAAiB,EAAG;AAC5C,kBAAM,eAAe,KAAK,OAAO,cAAc,IAAI,KAAK,QAAQ;AAChE,2BAAe,QAAQ;AAAA,UACxB;AAAA,QACP,CAAK,EAAE,KAAI;AAAA,MACX,CAAG;AAAA,IACH;AAEA,aAAS,WAAW,KAAK;AACvB,UAAI,CAAC;AAAK;AAEVA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,wCAAA,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,aAAS,qBAAqB,MAAM;AAElC,YAAM,gBAAgB;AAAA,QACpB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA;AAAA,QAER,WAAW;AAAA,MACf;AAEE,YAAM,WAAW,cAAc,KAAK,QAAQ,KAAK,cAAc,SAAS;AAGxE,UAAI,SAAS;AAAA,QACX,IAAI,KAAK;AAAA,QACT,UAAU,mBAAmB,KAAK,YAAY,EAAE;AAAA,MACpD;AAGE,YAAM,MAAM,kBAAkB,QAAQ,IAAI,OAAO,QAAQ,MAAM,EAC5D,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,EACvC,KAAK,GAAG,CAAC;AAEZA,oBAAY,MAAA,MAAA,OAAA,wCAAA,WAAW,GAAG;AAC1B,iBAAW,GAAG;AAAA,IAChB;AAEA,aAAS,iBAAiB;AACxB,0BAAoB,QAAQ;AAAA,IAC9B;AAEA,aAAS,iBAAiB;AACxB,0BAAoB,QAAQ;AAAA,IAC9B;AAEA,aAAS,gBAAgB,MAAM;AAC7B,UAAI,SAAS,QAAQ;AACnB,mBAAW,iBAAiB;AAAA,MAChC,WAAa,SAAS,MAAM;AAExBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,mBAAW,MAAM;AACfA,wBAAG,MAAC,UAAS;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS,MAAM;AACb;YACD;AAAA,UACT,CAAO;AAAA,QACF,GAAE,IAAI;AAAA,MACR;AAAA,IACH;AA4CA,aAAS,oBAAoB;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,UAAUH,cAAAA,IAAI,CAAC;AACDA,kBAAG,IAAC,IAAI;AAE5BI,kBAAAA,UAAU,MAAM;AAEd,eAAS,QAAQ;AACjB,iBAAW,QAAQ;AAGnBF,oBAAAA,WAAS,MAAM;AACb,cAAM,QAAQC,oBAAI;AAClB,cAAM,OAAO,sBAAsB,EAAE,mBAAmB,UAAQ;AAC9D,cAAI,MAAM;AACR,uBAAW,QAAQ,KAAK;AACxB,oBAAQ,QAAQ,KAAK;AAAA,UACtB;AAAA,QACP,CAAK,EAAE,KAAI;AAAA,MACX,CAAG;AAAA,IACH,CAAC;AAKD,aAAa;AAAA,MACX;AAAA,IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5gBD,GAAG,gBAAgB,SAAS;"}