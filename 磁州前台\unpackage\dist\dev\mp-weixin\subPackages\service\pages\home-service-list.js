"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "home-service-list",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.ref(0);
    common_vendor.ref("latest");
    const isRefreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const page = common_vendor.ref(1);
    const limit = common_vendor.ref(10);
    const serviceList = common_vendor.ref([]);
    common_vendor.ref([
      { name: "全部", type: "all" },
      { name: "家政服务", type: "home_cleaning" },
      { name: "维修改造", type: "repair" },
      { name: "上门安装", type: "installation" },
      { name: "开锁换锁", type: "locksmith" },
      { name: "搬家拉货", type: "moving" },
      { name: "上门美容", type: "beauty" },
      { name: "上门家教", type: "tutor" },
      { name: "宠物服务", type: "pet_service" },
      { name: "上门疏通", type: "plumbing" },
      { name: "其他类型", type: "other" }
    ]);
    const sampleServiceData = common_vendor.ref([]);
    const currentCategory = common_vendor.ref("全部");
    const categories = common_vendor.ref(["全部", "家政服务", "维修改造", "上门安装", "开锁换锁", "搬家拉货", "上门美容", "上门家教", "宠物服务", "上门疏通", "其他类型"]);
    const sortType = common_vendor.ref("time");
    common_vendor.ref(false);
    const loading = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const showAreaFilter = common_vendor.ref(false);
    const showSortFilter = common_vendor.ref(false);
    const selectedArea = common_vendor.ref("全部区域");
    const selectedSort = common_vendor.ref("默认排序");
    const areaDropdownTop = common_vendor.ref(120);
    const sortDropdownTop = common_vendor.ref(120);
    const areaList = common_vendor.ref(["全部区域", "城区", "磁州镇", "讲武城镇", "岳城镇", "观台镇", "白土镇", "黄沙镇"]);
    const sortList = common_vendor.ref(["默认排序", "离我最近", "最新发布", "热门推荐", "价格最低", "价格最高"]);
    const userLocation = common_vendor.ref(null);
    common_vendor.ref("上门安装");
    const activeTabId = common_vendor.ref("tab-0");
    const initSampleData = () => {
      sampleServiceData.value = [
        {
          id: "1",
          type: "home_service",
          serviceType: "home_cleaning",
          serviceTypeName: "家政服务",
          content: "专业保洁团队，提供家庭、办公室清洁服务，价格实惠",
          time: "2023-06-30 10:25",
          views: 125,
          images: ["/static/images/sample/cleaning1.jpg", "/static/images/sample/cleaning2.jpg"]
        },
        {
          id: "2",
          type: "home_service",
          serviceType: "repair",
          serviceTypeName: "维修改造",
          content: "水电维修，空调维修，灯具安装，墙面翻新等各类家庭维修服务",
          time: "2023-06-29 15:40",
          views: 98,
          images: ["/static/images/sample/repair1.jpg"]
        },
        {
          id: "3",
          type: "home_service",
          serviceType: "installation",
          serviceTypeName: "上门安装",
          content: "家具组装，家电安装，网络设备安装，提供专业上门服务",
          time: "2023-06-28 09:15",
          views: 76
        },
        {
          id: "4",
          type: "home_service",
          serviceType: "locksmith",
          serviceTypeName: "开锁换锁",
          content: "24小时开锁服务，换锁，修锁，保险柜开锁，汽车开锁",
          time: "2023-06-27 21:05",
          views: 112,
          images: ["/static/images/sample/lock1.jpg", "/static/images/sample/lock2.jpg"]
        },
        {
          id: "5",
          type: "home_service",
          serviceType: "moving",
          serviceTypeName: "搬家拉货",
          content: "专业搬家服务，小型搬家，大件运输，长途托运，价格合理",
          time: "2023-06-26 14:30",
          views: 88
        },
        {
          id: "6",
          type: "home_service",
          serviceType: "beauty",
          serviceTypeName: "上门美容",
          content: "专业美甲美睫，上门服务，使用进口产品，安全卫生",
          time: "2023-06-25 16:20",
          views: 143,
          images: ["/static/images/sample/beauty1.jpg"]
        },
        {
          id: "7",
          type: "home_service",
          serviceType: "tutor",
          serviceTypeName: "上门家教",
          content: "小学初中高中各科家教，有经验的老师，耐心负责",
          time: "2023-06-24 11:00",
          views: 67
        },
        {
          id: "8",
          type: "home_service",
          serviceType: "pet_service",
          serviceTypeName: "宠物服务",
          content: "宠物洗澡，美容，寄养，上门遛狗，专业服务",
          time: "2023-06-23 13:45",
          views: 124,
          images: ["/static/images/sample/pet1.jpg", "/static/images/sample/pet2.jpg"]
        },
        {
          id: "9",
          type: "home_service",
          serviceType: "plumbing",
          serviceTypeName: "上门疏通",
          content: "管道疏通，马桶疏通，下水道疏通，快速上门",
          time: "2023-06-22 08:30",
          views: 92
        },
        {
          id: "10",
          type: "home_service",
          serviceType: "other",
          serviceTypeName: "其他类型",
          content: "提供各类上门服务，有需要请联系，价格面议",
          time: "2023-06-21 17:10",
          views: 78,
          images: ["/static/images/sample/other1.jpg"]
        }
      ];
    };
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const loadServiceList = async () => {
      if (loading.value || !hasMore.value)
        return;
      loading.value = true;
      try {
        await new Promise((resolve) => setTimeout(resolve, 300));
        const newData = getMockServiceData();
        let filteredData = newData;
        if (currentCategory.value !== "全部") {
          filteredData = newData.filter((item) => item.category === currentCategory.value);
        }
        if (selectedArea.value !== "全部区域") {
          filteredData = filteredData.filter((item) => item.area === selectedArea.value || item.area && item.area.includes(selectedArea.value));
        }
        applySorting(filteredData);
        if (page.value === 1) {
          serviceList.value = filteredData;
        } else {
          serviceList.value = [...serviceList.value, ...filteredData];
        }
        hasMore.value = filteredData.length >= limit.value;
        page.value++;
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/service/pages/home-service-list.vue:387", "加载服务列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        loading.value = false;
        isRefreshing.value = false;
      }
    };
    const applySorting = (data) => {
      if (!data || data.length === 0)
        return;
      if (sortType.value === "popular") {
        data.sort((a, b) => b.views - a.views);
      } else if (sortType.value === "price_low") {
        data.sort((a, b) => {
          const priceA = parseFloat(a.price ? a.price.replace(/[^\d.]/g, "") : "99999");
          const priceB = parseFloat(b.price ? b.price.replace(/[^\d.]/g, "") : "99999");
          return priceA - priceB;
        });
      } else if (sortType.value === "price_high") {
        data.sort((a, b) => {
          const priceA = parseFloat(a.price ? a.price.replace(/[^\d.]/g, "") : "0");
          const priceB = parseFloat(b.price ? b.price.replace(/[^\d.]/g, "") : "0");
          return priceB - priceA;
        });
      } else if (sortType.value === "distance" && userLocation.value) {
        data.sort((a, b) => {
          const distanceA = calculateDistance(a.location || getLocationFromArea(a.area));
          const distanceB = calculateDistance(b.location || getLocationFromArea(b.area));
          return distanceA - distanceB;
        });
      }
      return data;
    };
    const getMockServiceData = () => {
      const mockServices = [
        {
          id: "101",
          category: "家政服务",
          title: "专业家庭保洁 深度清洁 开荒保洁 玻璃清洗",
          time: "2小时前",
          views: 128,
          area: "城区",
          price: "80元/小时",
          images: [
            "/static/images/service/cleaning1.jpg"
          ],
          location: { latitude: 36.358, longitude: 114.518 }
        },
        {
          id: "102",
          category: "维修改造",
          title: "专业水电维修安装 水管漏水 马桶疏通 灯具维修",
          time: "3小时前",
          views: 95,
          area: "高新区",
          price: "上门费 30元",
          images: [
            "/static/images/service/repair1.jpg"
          ],
          location: { latitude: 36.368, longitude: 114.528 }
        },
        {
          id: "103",
          category: "上门安装",
          title: "专业安装窗帘 晾衣架 各类家具 价格实惠 服务好",
          time: "昨天",
          views: 210,
          area: "全城",
          price: "面议",
          images: [
            "/static/images/service/install1.jpg"
          ],
          location: { latitude: 36.354, longitude: 114.514 }
        },
        {
          id: "104",
          category: "搬家拉货",
          title: "专业小型搬家 单身公寓 居民搬家 长短途运输",
          time: "3天前",
          views: 156,
          area: "磁州镇",
          price: "98元起",
          images: [
            "/static/images/service/moving1.jpg"
          ],
          location: { latitude: 36.378, longitude: 114.553 }
        },
        {
          id: "105",
          category: "开锁换锁",
          title: "专业开锁换锁 汽车锁 保险柜 指纹密码锁安装",
          time: "4天前",
          views: 86,
          area: "岳城镇",
          price: "开锁 50元起",
          images: [
            "/static/images/service/lock1.jpg"
          ],
          location: { latitude: 36.398, longitude: 114.493 }
        }
      ];
      let filteredData = mockServices;
      if (currentCategory.value !== "全部") {
        filteredData = mockServices.filter((item) => item.category === currentCategory.value);
      }
      const pageSize = Math.min(5, limit.value);
      return filteredData.slice(0, pageSize);
    };
    const loadMore = () => {
      loadServiceList();
    };
    const navigateToDetail = (id) => {
      const service = serviceList.value.find((item) => item.id === id);
      if (!service)
        return;
      const serviceCategory = service.category || currentCategory.value || "家政服务";
      common_vendor.index.__f__("log", "at subPackages/service/pages/home-service-list.vue:533", "跳转到详情页 - 服务ID:", id, "分类:", serviceCategory);
      let serviceType = service.serviceType || "";
      if (!serviceType) {
        switch (serviceCategory) {
          case "开锁换锁":
            serviceType = "locksmith";
            break;
          case "上门安装":
            serviceType = "installation";
            break;
          case "维修改造":
            serviceType = "repair";
            break;
          case "搬家拉货":
            serviceType = "moving";
            break;
          case "上门美容":
            serviceType = "beauty";
            break;
          case "上门家教":
            serviceType = "tutor";
            break;
          case "宠物服务":
            serviceType = "pet_service";
            break;
          case "上门疏通":
            serviceType = "plumbing";
            break;
          case "家政保洁":
          case "家政服务":
          default:
            serviceType = "home_cleaning";
            break;
        }
      }
      common_vendor.index.vibrateShort && common_vendor.index.vibrateShort();
      common_vendor.index.showLoading({
        title: "正在加载...",
        mask: true
      });
      const url = `/pages/publish/home-service-detail?id=${id}&category=${encodeURIComponent(serviceCategory)}&type=${serviceType}`;
      common_vendor.index.__f__("log", "at subPackages/service/pages/home-service-list.vue:582", "详情页跳转URL:", url);
      common_vendor.index.navigateTo({
        url,
        success: () => {
          common_vendor.index.__f__("log", "at subPackages/service/pages/home-service-list.vue:588", "跳转成功");
          common_vendor.index.hideLoading();
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/service/pages/home-service-list.vue:592", "跳转失败:", err);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "页面跳转失败，请重试",
            icon: "none"
          });
        }
      });
    };
    const navigateToPublish = () => {
      common_vendor.index.navigateTo({
        url: "/pages/publish/service-category"
      });
    };
    const switchCategory = (category) => {
      if (currentCategory.value !== category) {
        currentCategory.value = category;
        page.value = 1;
        serviceList.value = [];
        hasMore.value = true;
        loadServiceList();
        const index = categories.value.findIndex((item) => item === category);
        if (index >= 0) {
          activeTabId.value = "tab-" + index;
        }
      }
    };
    const contactService = (id) => {
      event.stopPropagation();
      const service = serviceList.value.find((item) => item.id === id);
      if (!service)
        return;
      common_vendor.index.showModal({
        title: "联系方式",
        content: "电话: 188****1234\n微信: same-as-phone",
        confirmText: "拨打电话",
        cancelText: "复制微信",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.makePhoneCall({
              phoneNumber: "18812341234"
            });
          } else if (res.cancel) {
            common_vendor.index.setClipboardData({
              data: "same-as-phone",
              success: () => {
                common_vendor.index.showToast({
                  title: "微信号已复制",
                  icon: "none"
                });
              }
            });
          }
        }
      });
    };
    const selectArea = (area) => {
      common_vendor.index.vibrateShort();
      selectedArea.value = area;
      showAreaFilter.value = false;
      resetListAndReload();
    };
    const selectSort = (sort) => {
      common_vendor.index.vibrateShort();
      selectedSort.value = sort;
      showSortFilter.value = false;
      resetListAndReload();
      if (sort === "最新发布") {
        sortType.value = "time";
      } else if (sort === "热门推荐") {
        sortType.value = "popular";
      } else if (sort === "价格最低") {
        sortType.value = "price_low";
      } else if (sort === "价格最高") {
        sortType.value = "price_high";
      } else if (sort === "离我最近") {
        sortType.value = "distance";
        sortByDistance();
      } else {
        sortType.value = "default";
      }
    };
    const sortByDistance = () => {
      common_vendor.index.getLocation({
        type: "gcj02",
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          userLocation.value = location;
          resetListAndReload();
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/service/pages/home-service-list.vue:743", "获取位置失败", err);
          common_vendor.index.showToast({
            title: "无法获取位置信息，请检查定位权限",
            icon: "none"
          });
          selectedSort.value = "默认排序";
          sortType.value = "default";
        }
      });
    };
    const closeAllFilters = () => {
      showAreaFilter.value = false;
      showSortFilter.value = false;
    };
    const resetListAndReload = () => {
      page.value = 1;
      serviceList.value = [];
      hasMore.value = true;
      loadServiceList();
    };
    const getLocationFromArea = (area) => {
      const locationMap = {
        "城区": { latitude: 36.354, longitude: 114.511 },
        "磁州镇": { latitude: 36.374, longitude: 114.551 },
        "讲武城镇": { latitude: 36.334, longitude: 114.471 },
        "岳城镇": { latitude: 36.394, longitude: 114.491 },
        "观台镇": { latitude: 36.314, longitude: 114.531 },
        "白土镇": { latitude: 36.284, longitude: 114.501 },
        "黄沙镇": { latitude: 36.404, longitude: 114.461 },
        "全城": { latitude: 36.354, longitude: 114.511 },
        "高新区": { latitude: 36.368, longitude: 114.528 }
      };
      return locationMap[area] || { latitude: 36.354, longitude: 114.511 };
    };
    const calculateDistance = (serviceLocation) => {
      if (!userLocation.value || !serviceLocation) {
        return Number.MAX_VALUE;
      }
      const R = 6371;
      const lat1 = userLocation.value.latitude * Math.PI / 180;
      const lat2 = serviceLocation.latitude * Math.PI / 180;
      const dLat = (serviceLocation.latitude - userLocation.value.latitude) * Math.PI / 180;
      const dLon = (serviceLocation.longitude - userLocation.value.longitude) * Math.PI / 180;
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;
      return distance;
    };
    const scrollToCategory = () => {
      try {
        const index = categories.value.findIndex((item) => item === currentCategory.value);
        if (index >= 0) {
          activeTabId.value = "tab-" + index;
          common_vendor.index.__f__("log", "at subPackages/service/pages/home-service-list.vue:821", "设置滚动到:", activeTabId.value);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at subPackages/service/pages/home-service-list.vue:824", "自动滚动失败:", e);
      }
    };
    const toggleAreaFilter = () => {
      showAreaFilter.value = !showAreaFilter.value;
      showSortFilter.value = false;
      if (showAreaFilter.value) {
        common_vendor.nextTick$1(() => {
          const query = common_vendor.index.createSelectorQuery();
          query.select(".filter-item[ref=areaBtn]").boundingClientRect((rect) => {
            if (rect) {
              areaDropdownTop.value = rect.bottom;
            }
          }).exec();
        });
      }
    };
    const toggleSortFilter = () => {
      showSortFilter.value = !showSortFilter.value;
      showAreaFilter.value = false;
      if (showSortFilter.value) {
        common_vendor.nextTick$1(() => {
          const query = common_vendor.index.createSelectorQuery();
          query.select(".filter-item[ref=sortBtn]").boundingClientRect((rect) => {
            if (rect) {
              sortDropdownTop.value = rect.bottom;
            }
          }).exec();
        });
      }
    };
    common_vendor.onMounted(() => {
      var _a;
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      const pages = getCurrentPages();
      const page2 = pages[pages.length - 1];
      const options = ((_a = page2.$page) == null ? void 0 : _a.options) || {};
      setTimeout(() => {
        initSampleData();
      }, 100);
      if (options.subType && options.subName) {
        currentCategory.value = decodeURIComponent(options.subName);
        page2.value = 1;
        serviceList.value = [];
        hasMore.value = true;
        const index = categories.value.findIndex((item) => item === currentCategory.value);
        if (index >= 0) {
          activeTabId.value = "tab-" + index;
        }
      } else if (options.category) {
        const categoryName = decodeURIComponent(options.category);
        if (categories.value.includes(categoryName)) {
          currentCategory.value = categoryName;
          page2.value = 1;
          serviceList.value = [];
          hasMore.value = true;
          const index = categories.value.findIndex((item) => item === currentCategory.value);
          if (index >= 0) {
            activeTabId.value = "tab-" + index;
          }
        }
      }
      setTimeout(() => {
        loadServiceList();
        common_vendor.nextTick$1(() => {
          scrollToCategory();
        });
      }, 200);
    });
    common_vendor.index.onPullDownRefresh(() => {
      page.value = 1;
      serviceList.value = [];
      hasMore.value = true;
      loadServiceList();
      setTimeout(() => {
        common_vendor.index.stopPullDownRefresh();
      }, 1e3);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(navigateBack),
        b: common_vendor.t(currentCategory.value || "家政服务"),
        c: statusBarHeight.value + "px",
        d: common_vendor.f(categories.value, (category, index, i0) => {
          return {
            a: common_vendor.t(category),
            b: index,
            c: "tab-" + index,
            d: currentCategory.value === category ? 1 : "",
            e: common_vendor.o(($event) => switchCategory(category), index)
          };
        }),
        e: activeTabId.value,
        f: common_vendor.t(selectedArea.value),
        g: selectedArea.value !== "全部区域" ? 1 : "",
        h: showAreaFilter.value ? 1 : "",
        i: common_vendor.o(toggleAreaFilter),
        j: common_vendor.t(selectedSort.value),
        k: selectedSort.value !== "默认排序" ? 1 : "",
        l: showSortFilter.value ? 1 : "",
        m: common_vendor.o(toggleSortFilter),
        n: showAreaFilter.value
      }, showAreaFilter.value ? {
        o: common_vendor.f(areaList.value, (area, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(area),
            b: area === selectedArea.value
          }, area === selectedArea.value ? {} : {}, {
            c: index,
            d: area === selectedArea.value ? 1 : "",
            e: common_vendor.o(($event) => selectArea(area), index)
          });
        }),
        p: areaDropdownTop.value + "px"
      } : {}, {
        q: showSortFilter.value
      }, showSortFilter.value ? {
        r: common_vendor.f(sortList.value, (sort, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(sort),
            b: sort === selectedSort.value
          }, sort === selectedSort.value ? {} : {}, {
            c: index,
            d: sort === selectedSort.value ? 1 : "",
            e: common_vendor.o(($event) => selectSort(sort), index)
          });
        }),
        s: sortDropdownTop.value + "px"
      } : {}, {
        t: showAreaFilter.value || showSortFilter.value
      }, showAreaFilter.value || showSortFilter.value ? {
        v: common_vendor.o(closeAllFilters)
      } : {}, {
        w: serviceList.value.length === 0 && !isLoading.value
      }, serviceList.value.length === 0 && !isLoading.value ? {
        x: common_assets._imports_1$3
      } : {}, {
        y: common_vendor.f(serviceList.value, (service, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(service.category),
            b: common_vendor.t(service.time),
            c: common_vendor.t(service.title),
            d: service.images && service.images.length > 0
          }, service.images && service.images.length > 0 ? {
            e: common_vendor.f(service.images.slice(0, 3), (img, imgIndex, i1) => {
              return {
                a: imgIndex,
                b: img
              };
            })
          } : {}, {
            f: common_vendor.t(service.area || "全城"),
            g: common_vendor.t(service.price || "面议"),
            h: common_vendor.t(service.views),
            i: common_vendor.o(($event) => contactService(service.id), index),
            j: index,
            k: common_vendor.o(($event) => navigateToDetail(service.id), index)
          });
        }),
        z: loading.value && serviceList.value.length > 0
      }, loading.value && serviceList.value.length > 0 ? {} : {}, {
        A: !hasMore.value && serviceList.value.length > 0
      }, !hasMore.value && serviceList.value.length > 0 ? {} : {}, {
        B: common_vendor.o(loadMore),
        C: common_vendor.o(navigateToPublish)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-59785f67"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/service/pages/home-service-list.js.map
