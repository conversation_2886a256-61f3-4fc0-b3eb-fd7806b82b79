
.success-container {
  min-height: 100vh;
  background-color: #f8f9fc;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 顶部渐变背景 */
.top-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(135deg, #0046B3, #1677FF);
  z-index: 0;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  position: relative;
  z-index: 1;
  width: 100%;
}
.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24px;
  height: 24px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}
.navbar-right {
  width: 44px;
}

/* 内容区域 */
.content {
  position: relative;
  z-index: 1;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 成功动画 */
.success-animation {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  width: 100%;
}
.success-circle {
  width: 80px;
  height: 80px;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 20px rgba(0, 82, 204, 0.1);
  animation: pulse 2s infinite;
}
@keyframes pulse {
0% {
    box-shadow: 0 0 0 0 rgba(0, 82, 204, 0.4);
}
70% {
    box-shadow: 0 0 0 15px rgba(0, 82, 204, 0);
}
100% {
    box-shadow: 0 0 0 0 rgba(0, 82, 204, 0);
}
}
.success-icon {
  width: 48px;
  height: 48px;
}

/* 成功信息 */
.success-info {
  margin-top: 16px;
  text-align: center;
  margin-bottom: 30px;
  width: 100%;
}
.success-title {
  font-size: 22px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.success-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 进度卡片 */
.progress-card {
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}
.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}
.progress-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.progress-status {
  font-size: 14px;
  color: #1677FF;
  background-color: rgba(22, 119, 255, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
}
.progress-steps {
  padding-left: 8px;
}
.step-item {
  position: relative;
  padding-left: 20px;
  margin-bottom: 30px;
}
.step-item:last-child {
  margin-bottom: 0;
}
.step-dot {
  position: absolute;
  left: 0;
  top: 6px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #e8e8e8;
  z-index: 1;
}
.step-line {
  position: absolute;
  left: 5px;
  top: 18px;
  width: 2px;
  height: calc(100% + 20px);
  background-color: #e8e8e8;
}
.step-item:last-child .step-line {
  display: none;
}
.step-content {
  display: flex;
  flex-direction: column;
}
.step-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}
.step-time, .step-desc {
  font-size: 12px;
  color: #999;
}

/* 完成状态 */
.step-item.completed .step-dot {
  background-color: #52C41A;
}
.step-item.completed .step-line {
  background-color: #52C41A;
}
.step-item.completed .step-name {
  color: #52C41A;
}

/* 当前状态 */
.step-item.current .step-dot {
  background-color: #1677FF;
  box-shadow: 0 0 0 4px rgba(22, 119, 255, 0.2);
}
.step-item.current .step-name {
  color: #1677FF;
}

/* 提示卡片 */
.tips-card {
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}
.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.tips-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.tips-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.tips-content {
  background-color: #f8f9fc;
  padding: 16px;
  border-radius: 8px;
}
.tips-text {
  font-size: 14px;
  color: #666;
  line-height: 1.8;
  display: block;
}

/* 操作按钮 */
.action-buttons {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.primary-btn {
  height: 48px;
  background: linear-gradient(135deg, #0052CC, #2684FF);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 24px;
  margin-bottom: 12px;
  box-shadow: 0 6px 12px rgba(0, 82, 204, 0.2);
}
.secondary-btn {
  height: 48px;
  background-color: #fff;
  color: #0052CC;
  font-size: 16px;
  font-weight: 500;
  border-radius: 24px;
  border: 1px solid #0052CC;
}

/* 联系客服 */
.contact-section {
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}
.contact-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  text-align: center;
}
.contact-btn {
  width: 140px;
  height: 36px;
  line-height: 36px;
  background-color: #f5f5f5;
  color: #666;
  font-size: 14px;
  border-radius: 18px;
}
