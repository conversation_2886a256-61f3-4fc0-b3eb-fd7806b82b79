<template>
  <view class="activity-rewards-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">活动奖励</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 顶部选项卡 -->
    <view class="tabs-container" :style="{ top: navbarHeight + 'px' }">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
      </view>
      <view class="tab-line" :style="tabLineStyle"></view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-area" :style="{ paddingTop: (navbarHeight + tabsHeight) + 'px' }">
      <swiper class="content-swiper" :current="currentTab" @change="onSwiperChange">
        <!-- 全部奖励 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(0)" refresher-enabled :refresher-triggered="refreshing[0]" @refresherrefresh="onRefresh(0)">
            <view v-if="allRewards.length > 0" class="rewards-list">
              <view class="reward-item" v-for="(item, index) in allRewards" :key="index" @click="viewRewardDetail(item)">
                <view class="reward-header">
                  <image class="reward-icon" :src="getRewardTypeIcon(item.type)"></image>
                  <view class="reward-title-wrap">
                    <text class="reward-title">{{item.title}}</text>
                    <text class="reward-time">{{item.time}}</text>
                  </view>
                  <view class="reward-status" :class="{'status-used': item.status === 'used', 'status-expired': item.status === 'expired'}">
                    {{getStatusText(item.status)}}
                  </view>
                </view>
                <view class="reward-content">
                  <view class="reward-info">
                    <text class="reward-name">{{item.name}}</text>
                    <text class="reward-desc">{{item.description}}</text>
                  </view>
                  <view class="reward-value">
                    <text class="reward-amount" v-if="item.type === 'coupon'">¥{{item.amount}}</text>
                    <text class="reward-amount" v-else-if="item.type === 'redPacket'">¥{{item.amount}}</text>
                    <text class="reward-amount" v-else-if="item.type === 'points'">+{{item.amount}}</text>
                    <text class="reward-amount" v-else>{{item.amount}}</text>
                  </view>
                </view>
                <view class="reward-footer">
                  <text class="activity-name">来自活动：{{item.activityName}}</text>
                  <view class="reward-action" v-if="item.status === 'available'">
                    <text class="action-btn" @click.stop="useReward(item)">立即使用</text>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无活动奖励</view>
            </view>
            <view v-if="allRewards.length > 0 && !hasMore[0]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
        
        <!-- 可使用 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(1)" refresher-enabled :refresher-triggered="refreshing[1]" @refresherrefresh="onRefresh(1)">
            <view v-if="availableRewards.length > 0" class="rewards-list">
              <view class="reward-item" v-for="(item, index) in availableRewards" :key="index" @click="viewRewardDetail(item)">
                <view class="reward-header">
                  <image class="reward-icon" :src="getRewardTypeIcon(item.type)"></image>
                  <view class="reward-title-wrap">
                    <text class="reward-title">{{item.title}}</text>
                    <text class="reward-time">{{item.time}}</text>
                  </view>
                  <view class="reward-status">
                    {{getStatusText(item.status)}}
                  </view>
                </view>
                <view class="reward-content">
                  <view class="reward-info">
                    <text class="reward-name">{{item.name}}</text>
                    <text class="reward-desc">{{item.description}}</text>
                  </view>
                  <view class="reward-value">
                    <text class="reward-amount" v-if="item.type === 'coupon'">¥{{item.amount}}</text>
                    <text class="reward-amount" v-else-if="item.type === 'redPacket'">¥{{item.amount}}</text>
                    <text class="reward-amount" v-else-if="item.type === 'points'">+{{item.amount}}</text>
                    <text class="reward-amount" v-else>{{item.amount}}</text>
                  </view>
                </view>
                <view class="reward-footer">
                  <text class="activity-name">来自活动：{{item.activityName}}</text>
                  <view class="reward-action">
                    <text class="action-btn" @click.stop="useReward(item)">立即使用</text>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无可用奖励</view>
            </view>
            <view v-if="availableRewards.length > 0 && !hasMore[1]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
        
        <!-- 已使用/过期 -->
        <swiper-item>
          <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(2)" refresher-enabled :refresher-triggered="refreshing[2]" @refresherrefresh="onRefresh(2)">
            <view v-if="historyRewards.length > 0" class="rewards-list">
              <view class="reward-item" v-for="(item, index) in historyRewards" :key="index" @click="viewRewardDetail(item)">
                <view class="reward-header">
                  <image class="reward-icon" :src="getRewardTypeIcon(item.type)"></image>
                  <view class="reward-title-wrap">
                    <text class="reward-title">{{item.title}}</text>
                    <text class="reward-time">{{item.time}}</text>
                  </view>
                  <view class="reward-status" :class="{'status-used': item.status === 'used', 'status-expired': item.status === 'expired'}">
                    {{getStatusText(item.status)}}
                  </view>
                </view>
                <view class="reward-content">
                  <view class="reward-info">
                    <text class="reward-name">{{item.name}}</text>
                    <text class="reward-desc">{{item.description}}</text>
                  </view>
                  <view class="reward-value">
                    <text class="reward-amount" v-if="item.type === 'coupon'">¥{{item.amount}}</text>
                    <text class="reward-amount" v-else-if="item.type === 'redPacket'">¥{{item.amount}}</text>
                    <text class="reward-amount" v-else-if="item.type === 'points'">+{{item.amount}}</text>
                    <text class="reward-amount" v-else>{{item.amount}}</text>
                  </view>
                </view>
                <view class="reward-footer">
                  <text class="activity-name">来自活动：{{item.activityName}}</text>
                </view>
              </view>
            </view>
            <view v-else class="empty-view">
              <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
              <view class="empty-text">暂无历史奖励</view>
            </view>
            <view v-if="historyRewards.length > 0 && !hasMore[2]" class="list-bottom">没有更多了</view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64, // 导航栏高度
      tabsHeight: 44, // 选项卡高度
      tabs: [
        { name: '全部' },
        { name: '可使用' },
        { name: '历史记录' }
      ],
      currentTab: 0,
      allRewards: [],
      availableRewards: [],
      historyRewards: [],
      page: [1, 1, 1], // 当前页码
      pageSize: 10, // 每页显示数量
      hasMore: [true, true, true], // 是否有更多数据
      refreshing: [false, false, false] // 刷新状态
    }
  },
  computed: {
    tabLineStyle() {
      return {
        transform: `translateX(${this.currentTab * (100 / this.tabs.length)}%)`,
        width: `${100 / this.tabs.length}%`
      }
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 加载初始数据
    this.loadAllRewards();
    this.loadAvailableRewards();
    this.loadHistoryRewards();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 切换选项卡
    switchTab(index) {
      this.currentTab = index;
    },
    
    // 轮播图切换事件
    onSwiperChange(e) {
      this.currentTab = e.detail.current;
    },
    
    // 加载全部奖励
    loadAllRewards() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `all_${this.page[0]}_${i}`,
          type: ['coupon', 'redPacket', 'points', 'gift'][i % 4],
          title: `活动奖励 ${this.page[0]}_${i}`,
          name: `${['优惠券', '红包', '积分', '实物礼品'][i % 4]}`,
          description: `${['满100减20', '现金红包', '签到奖励', '精美礼品'][i % 4]}`,
          amount: [20, 5, 10, ''][i % 4],
          time: `2023-05-${15 - i} 获得`,
          status: ['available', 'available', 'used', 'expired'][i % 4],
          activityName: `${['夏日购物节', '新店开业', '每日签到', '周年庆'][i % 4]}`
        }));
        
        if (this.page[0] === 1) {
          this.allRewards = mockData;
        } else {
          this.allRewards = [...this.allRewards, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[0] = this.page[0] < 3;
        
        // 关闭刷新状态
        this.refreshing[0] = false;
      }, 500);
    },
    
    // 加载可用奖励
    loadAvailableRewards() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        const mockData = Array.from({ length: 3 }, (_, i) => ({
          id: `available_${this.page[1]}_${i}`,
          type: ['coupon', 'redPacket', 'points'][i % 3],
          title: `可用奖励 ${this.page[1]}_${i}`,
          name: `${['优惠券', '红包', '积分'][i % 3]}`,
          description: `${['满100减20', '现金红包', '签到奖励'][i % 3]}`,
          amount: [20, 5, 10][i % 3],
          time: `2023-05-${15 - i} 获得`,
          status: 'available',
          activityName: `${['夏日购物节', '新店开业', '每日签到'][i % 3]}`
        }));
        
        if (this.page[1] === 1) {
          this.availableRewards = mockData;
        } else {
          this.availableRewards = [...this.availableRewards, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[1] = this.page[1] < 2;
        
        // 关闭刷新状态
        this.refreshing[1] = false;
      }, 500);
    },
    
    // 加载历史奖励
    loadHistoryRewards() {
      // 模拟请求延迟
      setTimeout(() => {
        // 模拟数据
        const mockData = Array.from({ length: 4 }, (_, i) => ({
          id: `history_${this.page[2]}_${i}`,
          type: ['coupon', 'redPacket', 'points', 'gift'][i % 4],
          title: `历史奖励 ${this.page[2]}_${i}`,
          name: `${['优惠券', '红包', '积分', '实物礼品'][i % 4]}`,
          description: `${['满100减20', '现金红包', '签到奖励', '精美礼品'][i % 4]}`,
          amount: [20, 5, 10, ''][i % 4],
          time: `2023-05-${10 - i} 获得`,
          status: i % 2 === 0 ? 'used' : 'expired',
          activityName: `${['夏日购物节', '新店开业', '每日签到', '周年庆'][i % 4]}`
        }));
        
        if (this.page[2] === 1) {
          this.historyRewards = mockData;
        } else {
          this.historyRewards = [...this.historyRewards, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[2] = this.page[2] < 2;
        
        // 关闭刷新状态
        this.refreshing[2] = false;
      }, 500);
    },
    
    // 加载更多
    loadMore(tabIndex) {
      if (!this.hasMore[tabIndex]) return;
      
      this.page[tabIndex]++;
      
      switch (tabIndex) {
        case 0:
          this.loadAllRewards();
          break;
        case 1:
          this.loadAvailableRewards();
          break;
        case 2:
          this.loadHistoryRewards();
          break;
      }
    },
    
    // 下拉刷新
    onRefresh(tabIndex) {
      this.refreshing[tabIndex] = true;
      this.page[tabIndex] = 1;
      
      switch (tabIndex) {
        case 0:
          this.loadAllRewards();
          break;
        case 1:
          this.loadAvailableRewards();
          break;
        case 2:
          this.loadHistoryRewards();
          break;
      }
    },
    
    // 查看奖励详情
    viewRewardDetail(item) {
      uni.showToast({
        title: '查看详情: ' + item.name,
        icon: 'none'
      });
    },
    
    // 使用奖励
    useReward(item) {
      switch (item.type) {
        case 'coupon':
          uni.navigateTo({
            url: '/pages/services/coupon'
          });
          break;
        case 'redPacket':
          uni.navigateTo({
            url: '/pages/user/my-red-packets'
          });
          break;
        case 'points':
          uni.navigateTo({
            url: '/subPackages/checkin/pages/points'
          });
          break;
        default:
          uni.showToast({
            title: '使用奖励: ' + item.name,
            icon: 'none'
          });
      }
    },
    
    // 获取奖励类型图标
    getRewardTypeIcon(type) {
      switch (type) {
        case 'coupon':
          return '/static/images/tabbar/卡券.png';
        case 'redPacket':
          return '/static/images/tabbar/我的红包.png';
        case 'points':
          return '/static/images/tabbar/每日签到.png';
        case 'gift':
          return '/static/images/tabbar/礼品.png';
        default:
          return '/static/images/tabbar/活动.png';
      }
    },
    
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'available':
          return '可使用';
        case 'used':
          return '已使用';
        case 'expired':
          return '已过期';
        default:
          return '未知';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-rewards-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background: linear-gradient(135deg, #3a7afe, #6ca6ff);
  color: #fff;
  z-index: 100;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.navbar-title {
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 60rpx;
}

/* 选项卡样式 */
.tabs-container {
  position: fixed;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  background-color: #fff;
  z-index: 99;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tab-text {
  font-size: 14px;
  color: #666;
}

.tab-item.active .tab-text {
  color: #3a7afe;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  height: 3px;
  background-color: #3a7afe;
  border-radius: 3px;
  transition: transform 0.3s;
}

/* 内容区域样式 */
.content-area {
  width: 100%;
  height: 100vh;
}

.content-swiper {
  width: 100%;
  height: 100%;
}

.tab-scroll {
  height: 100%;
}

/* 奖励列表样式 */
.rewards-list {
  padding: 20rpx;
}

.reward-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.reward-header {
  display: flex;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.reward-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
}

.reward-title-wrap {
  flex: 1;
}

.reward-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.reward-time {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}

.reward-status {
  font-size: 24rpx;
  color: #3a7afe;
  padding: 4rpx 12rpx;
  background-color: #f0f5ff;
  border-radius: 6rpx;
}

.status-used {
  color: #52c41a;
  background-color: #f6ffed;
}

.status-expired {
  color: #999;
  background-color: #f5f5f5;
}

.reward-content {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.reward-info {
  flex: 1;
}

.reward-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.reward-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.reward-value {
  text-align: right;
}

.reward-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4d4f;
}

.reward-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
}

.activity-name {
  font-size: 24rpx;
  color: #999;
}

.reward-action {
  display: flex;
  align-items: center;
}

.action-btn {
  font-size: 24rpx;
  color: #fff;
  background-color: #3a7afe;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}
</style> 