{"version": 3, "file": "data-analysis.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcZGF0YS1hbmFseXNpcy52dWU"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">红包数据</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 数据筛选 -->\n    <view class=\"filter-section\">\n      <view class=\"filter-header\">\n        <view class=\"date-picker\" @click=\"showDatePicker\">\n          <text class=\"date-text\">{{dateRange}}</text>\n          <view class=\"date-icon\"></view>\n        </view>\n        <view class=\"type-filter\" @click=\"showTypeFilter\">\n          <text class=\"filter-text\">{{currentType}}</text>\n          <view class=\"filter-icon\"></view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 数据概览 -->\n    <view class=\"overview-section\">\n      <view class=\"overview-header\">\n        <text class=\"section-title\">数据概览</text>\n      </view>\n      \n      <view class=\"stats-cards\">\n        <view class=\"stats-card\">\n          <view class=\"card-value\">{{redpacketData.totalCount}}</view>\n          <view class=\"card-label\">发放总数</view>\n          <view class=\"card-trend\" :class=\"redpacketData.countTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{redpacketData.countGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-value\">¥{{formatNumber(redpacketData.totalAmount)}}</view>\n          <view class=\"card-label\">红包总额</view>\n          <view class=\"card-trend\" :class=\"redpacketData.amountTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{redpacketData.amountGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-value\">{{redpacketData.receiveRate}}%</view>\n          <view class=\"card-label\">领取率</view>\n          <view class=\"card-trend\" :class=\"redpacketData.receiveRateTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{redpacketData.receiveRateGrowth}}</text>\n          </view>\n        </view>\n        \n        <view class=\"stats-card\">\n          <view class=\"card-value\">{{redpacketData.conversionRate}}%</view>\n          <view class=\"card-label\">转化率</view>\n          <view class=\"card-trend\" :class=\"redpacketData.conversionTrend\">\n            <view class=\"trend-arrow\"></view>\n            <text class=\"trend-value\">{{redpacketData.conversionGrowth}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 趋势图表 -->\n    <view class=\"chart-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">趋势分析</text>\n        <view class=\"chart-tabs\">\n          <view \n            class=\"chart-tab\" \n            v-for=\"(tab, index) in chartTabs\" \n            :key=\"index\"\n            :class=\"{ active: currentChartTab === index }\"\n            @tap=\"switchChartTab(index)\">\n            <text class=\"tab-text\">{{tab}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"chart-container\">\n        <image class=\"chart-placeholder\" src=\"/static/images/redpacket/chart-placeholder.png\" mode=\"widthFix\"></image>\n      </view>\n    </view>\n    \n    <!-- 用户分析 -->\n    <view class=\"user-analysis-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">用户分析</text>\n      </view>\n      \n      <view class=\"user-stats\">\n        <view class=\"user-stat-item\">\n          <view class=\"stat-circle\" :style=\"{ background: 'conic-gradient(#FF5858 ' + (userData.newUserRate * 3.6) + 'deg, #F5F7FA 0)' }\">\n            <view class=\"inner-circle\">\n              <text class=\"circle-value\">{{userData.newUserRate}}%</text>\n            </view>\n          </view>\n          <text class=\"stat-label\">新用户占比</text>\n        </view>\n        \n        <view class=\"user-stat-item\">\n          <view class=\"stat-circle\" :style=\"{ background: 'conic-gradient(#4ECDC4 ' + (userData.activeUserRate * 3.6) + 'deg, #F5F7FA 0)' }\">\n            <view class=\"inner-circle\">\n              <text class=\"circle-value\">{{userData.activeUserRate}}%</text>\n            </view>\n          </view>\n          <text class=\"stat-label\">活跃用户</text>\n        </view>\n        \n        <view class=\"user-stat-item\">\n          <view class=\"stat-circle\" :style=\"{ background: 'conic-gradient(#FFD166 ' + (userData.repurchaseRate * 3.6) + 'deg, #F5F7FA 0)' }\">\n            <view class=\"inner-circle\">\n              <text class=\"circle-value\">{{userData.repurchaseRate}}%</text>\n            </view>\n          </view>\n          <text class=\"stat-label\">复购率</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 红包类型分析 -->\n    <view class=\"type-analysis-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">红包类型分析</text>\n      </view>\n      \n      <view class=\"type-stats\">\n        <view class=\"type-stat-item\" v-for=\"(item, index) in typeData\" :key=\"index\">\n          <view class=\"type-bar-container\">\n            <view class=\"type-bar\" :style=\"{ width: item.percentage + '%', background: item.color }\"></view>\n          </view>\n          <view class=\"type-info\">\n            <view class=\"type-name\">{{item.name}}</view>\n            <view class=\"type-value\">{{item.count}}个 ({{item.percentage}}%)</view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 红包效果排行 -->\n    <view class=\"ranking-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">红包效果排行</text>\n      </view>\n      \n      <view class=\"ranking-tabs\">\n        <view \n          class=\"ranking-tab\" \n          v-for=\"(tab, index) in rankingTabs\" \n          :key=\"index\"\n          :class=\"{ active: currentRankingTab === index }\"\n          @tap=\"switchRankingTab(index)\">\n          <text class=\"tab-text\">{{tab}}</text>\n        </view>\n      </view>\n      \n      <view class=\"ranking-list\">\n        <view class=\"ranking-item\" v-for=\"(item, index) in rankingList\" :key=\"index\">\n          <view class=\"ranking-number\" :class=\"{ 'top-three': index < 3 }\">{{index + 1}}</view>\n          <view class=\"ranking-content\">\n            <text class=\"ranking-title\">{{item.title}}</text>\n            <text class=\"ranking-time\">{{item.time}}</text>\n          </view>\n          <view class=\"ranking-value\">\n            <text class=\"value-text\">{{item.value}}</text>\n            <text class=\"value-unit\">{{rankingValueUnit}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 导出报表按钮 -->\n    <view class=\"export-button\" @click=\"exportReport\">\n      <text class=\"export-text\">导出数据报表</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      dateRange: '2023-04-01 ~ 2023-04-30',\n      currentType: '全部类型',\n      \n      // 红包数据概览\n      redpacketData: {\n        totalCount: 5862,\n        countTrend: 'up',\n        countGrowth: '12.5%',\n        \n        totalAmount: 58620.50,\n        amountTrend: 'up',\n        amountGrowth: '15.2%',\n        \n        receiveRate: 76.8,\n        receiveRateTrend: 'up',\n        receiveRateGrowth: '3.5%',\n        \n        conversionRate: 42.3,\n        conversionTrend: 'up',\n        conversionGrowth: '5.2%'\n      },\n      \n      // 图表选项\n      chartTabs: ['发放数量', '领取金额', '转化率'],\n      currentChartTab: 0,\n      \n      // 用户数据\n      userData: {\n        newUserRate: 35,\n        activeUserRate: 68,\n        repurchaseRate: 42\n      },\n      \n      // 红包类型数据\n      typeData: [\n        {\n          name: '普通红包',\n          count: 3256,\n          percentage: 55,\n          color: '#FF5858'\n        },\n        {\n          name: '裂变红包',\n          count: 1528,\n          percentage: 26,\n          color: '#4ECDC4'\n        },\n        {\n          name: '群发红包',\n          count: 782,\n          percentage: 13,\n          color: '#FFD166'\n        },\n        {\n          name: '红包雨',\n          count: 296,\n          percentage: 6,\n          color: '#6A0572'\n        }\n      ],\n      \n      // 排行榜选项\n      rankingTabs: ['转化率', '领取率', '分享率'],\n      currentRankingTab: 0,\n      \n      // 排行榜数据\n      rankingList: [\n        {\n          title: '新用户专享红包',\n          time: '2023-04-15 ~ 2023-04-20',\n          value: 68.5\n        },\n        {\n          title: '五一节日红包',\n          time: '2023-05-01 ~ 2023-05-07',\n          value: 62.3\n        },\n        {\n          title: '周末限时红包',\n          time: '2023-04-22 ~ 2023-04-23',\n          value: 58.9\n        },\n        {\n          title: '会员生日红包',\n          time: '2023-04-01 ~ 2023-04-30',\n          value: 52.4\n        },\n        {\n          title: '满减活动红包',\n          time: '2023-04-10 ~ 2023-04-15',\n          value: 48.7\n        }\n      ]\n    }\n  },\n  computed: {\n    rankingValueUnit() {\n      const units = ['%', '%', '%'];\n      return units[this.currentRankingTab];\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showHelp() {\n      uni.showModal({\n        title: '红包数据帮助',\n        content: '在此页面您可以查看红包活动的各项数据指标和分析报告，帮助您优化红包营销策略。',\n        showCancel: false\n      });\n    },\n    showDatePicker() {\n      // 显示日期选择器\n      uni.showToast({\n        title: '日期选择功能开发中',\n        icon: 'none'\n      });\n    },\n    showTypeFilter() {\n      // 显示类型筛选\n      uni.showToast({\n        title: '类型筛选功能开发中',\n        icon: 'none'\n      });\n    },\n    formatNumber(num) {\n      return num.toFixed(2);\n    },\n    switchChartTab(index) {\n      this.currentChartTab = index;\n    },\n    switchRankingTab(index) {\n      this.currentRankingTab = index;\n    },\n    exportReport() {\n      uni.showToast({\n        title: '数据报表导出功能开发中',\n        icon: 'none'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.page-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF5858, #FF0000);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 筛选样式 */\n.filter-section {\n  background-color: #fff;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.date-picker,\n.type-filter {\n  display: flex;\n  align-items: center;\n  background-color: #F5F7FA;\n  padding: 8px 12px;\n  border-radius: 15px;\n}\n\n.date-text,\n.filter-text {\n  font-size: 12px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.date-icon,\n.filter-icon {\n  width: 10px;\n  height: 10px;\n  border-left: 1px solid #666;\n  border-bottom: 1px solid #666;\n  transform: rotate(-45deg);\n}\n\n/* 数据概览样式 */\n.overview-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.overview-header {\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.stats-cards {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n\n.stats-card {\n  width: 48%;\n  background: linear-gradient(135deg, #FFF, #F5F7FA);\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 10px;\n  position: relative;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\n}\n\n.card-value {\n  font-size: 22px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.card-label {\n  font-size: 12px;\n  color: #666;\n}\n\n.card-trend {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  display: flex;\n  align-items: center;\n  font-size: 12px;\n}\n\n.card-trend.up {\n  color: #FF5858;\n}\n\n.card-trend.down {\n  color: #2ED573;\n}\n\n.trend-arrow {\n  width: 8px;\n  height: 8px;\n  border-left: 1px solid currentColor;\n  border-top: 1px solid currentColor;\n  margin-right: 2px;\n}\n\n.up .trend-arrow {\n  transform: rotate(45deg);\n}\n\n.down .trend-arrow {\n  transform: rotate(-135deg);\n}\n\n/* 图表样式 */\n.chart-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.chart-tabs {\n  display: flex;\n}\n\n.chart-tab {\n  padding: 5px 10px;\n  margin-left: 5px;\n  border-radius: 15px;\n  background-color: #F5F7FA;\n}\n\n.chart-tab.active {\n  background-color: #FF5858;\n  color: #fff;\n}\n\n.tab-text {\n  font-size: 12px;\n}\n\n.chart-container {\n  height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.chart-placeholder {\n  width: 100%;\n  height: auto;\n}\n\n/* 用户分析样式 */\n.user-analysis-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.user-stats {\n  display: flex;\n  justify-content: space-around;\n  margin-top: 15px;\n}\n\n.user-stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-circle {\n  width: 80px;\n  height: 80px;\n  border-radius: 40px;\n  position: relative;\n  margin-bottom: 10px;\n}\n\n.inner-circle {\n  position: absolute;\n  top: 10px;\n  left: 10px;\n  width: 60px;\n  height: 60px;\n  border-radius: 30px;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.circle-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 类型分析样式 */\n.type-analysis-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.type-stats {\n  margin-top: 15px;\n}\n\n.type-stat-item {\n  margin-bottom: 15px;\n}\n\n.type-bar-container {\n  height: 10px;\n  background-color: #F5F7FA;\n  border-radius: 5px;\n  overflow: hidden;\n  margin-bottom: 5px;\n}\n\n.type-bar {\n  height: 100%;\n  border-radius: 5px;\n}\n\n.type-info {\n  display: flex;\n  justify-content: space-between;\n  font-size: 12px;\n}\n\n.type-name {\n  color: #666;\n}\n\n.type-value {\n  color: #333;\n  font-weight: 500;\n}\n\n/* 排行榜样式 */\n.ranking-section {\n  background-color: #fff;\n  margin: 15px;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.ranking-tabs {\n  display: flex;\n  margin-bottom: 15px;\n}\n\n.ranking-tab {\n  padding: 5px 15px;\n  margin-right: 10px;\n  border-radius: 15px;\n  background-color: #F5F7FA;\n}\n\n.ranking-tab.active {\n  background-color: #FF5858;\n  color: #fff;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.ranking-item:last-child {\n  border-bottom: none;\n}\n\n.ranking-number {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background-color: #F5F7FA;\n  color: #666;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  margin-right: 10px;\n}\n\n.ranking-number.top-three {\n  background-color: #FF5858;\n  color: #fff;\n}\n\n.ranking-content {\n  flex: 1;\n}\n\n.ranking-title {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.ranking-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.ranking-value {\n  text-align: right;\n}\n\n.value-text {\n  font-size: 16px;\n  font-weight: bold;\n  color: #FF5858;\n}\n\n.value-unit {\n  font-size: 12px;\n  color: #666;\n  margin-left: 2px;\n}\n\n/* 导出按钮 */\n.export-button {\n  margin: 15px;\n  padding: 12px 0;\n  background-color: #fff;\n  border-radius: 25px;\n  text-align: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  margin-bottom: 30px;\n}\n\n.export-text {\n  font-size: 14px;\n  color: #FF5858;\n  font-weight: 500;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA4LA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,aAAa;AAAA;AAAA,MAGb,eAAe;AAAA,QACb,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QAEd,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QAEnB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MACnB;AAAA;AAAA,MAGD,WAAW,CAAC,QAAQ,QAAQ,KAAK;AAAA,MACjC,iBAAiB;AAAA;AAAA,MAGjB,UAAU;AAAA,QACR,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MACjB;AAAA;AAAA,MAGD,UAAU;AAAA,QACR;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACD;AAAA;AAAA,MAGD,aAAa,CAAC,OAAO,OAAO,KAAK;AAAA,MACjC,mBAAmB;AAAA;AAAA,MAGnB,aAAa;AAAA,QACX;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,mBAAmB;AACjB,YAAM,QAAQ,CAAC,KAAK,KAAK,GAAG;AAC5B,aAAO,MAAM,KAAK,iBAAiB;AAAA,IACrC;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa,KAAK;AAChB,aAAO,IAAI,QAAQ,CAAC;AAAA,IACrB;AAAA,IACD,eAAe,OAAO;AACpB,WAAK,kBAAkB;AAAA,IACxB;AAAA,IACD,iBAAiB,OAAO;AACtB,WAAK,oBAAoB;AAAA,IAC1B;AAAA,IACD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5UA,GAAG,WAAW,eAAe;"}