<template>
  <view class="service-list-container premium-style" :data-service-type="serviceType">
    <!-- 高级自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="back-btn" @click="navigateBack">
        <view class="back-icon"></view>
      </view>
      <view class="navbar-title">{{serviceTitle}}</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 高级搜索框 -->
    <view class="search-container" v-if="showSearchBox">
      <view class="search-box">
        <image class="search-icon" src="/static/images/tabbar/放大镜.png"></image>
        <input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索职位、公司或关键词" confirm-type="search" @confirm="applyKeywordSearch" />
        <view class="search-cancel" v-if="searchKeyword" @click="searchKeyword = ''">×</view>
      </view>
    </view>
    
    <!-- 高级一级分类标签栏 -->
    <scroll-view class="category-tabs" scroll-x show-scrollbar="false" v-if="showSubCategories" enhanced :bounces="true">
        <view 
          v-for="(tab, index) in subCategories" 
          :key="index" 
          class="tab-item" 
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          {{tab.name}}
        </view>
      </scroll-view>
    
    <!-- 高级子分类标签栏 -->
    <scroll-view class="subcategory-tabs" scroll-x show-scrollbar="false" v-if="showSubSubCategories && subSubCategories.length > 0" enhanced :bounces="true">
      <view 
        v-for="(subTab, index) in subSubCategories" 
        :key="index" 
        class="subtab-item" 
        :class="{ active: currentSubTab === index }"
        @click="switchSubTab(index)"
      >
        {{subTab.name}}
    </view>
    </scroll-view>
    
    <!-- 高级筛选栏 -->
    <view class="filter-container">
      <view class="filter-wrapper">
        <view class="filter-item" ref="areaBtn" @click="toggleAreaFilter">
          <text :class="{ 'active-filter': selectedArea !== '全部区域' }">{{selectedArea}}</text>
          <view class="filter-arrow" :class="{ 'arrow-up': showAreaFilter }"></view>
      </view>
        <view class="filter-item" ref="sortBtn" @click="toggleSort('default')">
          <text :class="{ 'active-filter': sortBy !== 'default' }">{{sortBy === 'default' ? '最新' : sortBy}}</text>
          <view class="filter-arrow" :class="{ 'arrow-up': showSortFilter }"></view>
      </view>
      </view>
    </view>
    
    <!-- 高级区域筛选弹出层 -->
    <view class="filter-dropdown area-dropdown" v-if="showAreaFilter" :style="{ top: areaDropdownTop + 'px' }">
      <scroll-view scroll-y class="dropdown-scroll">
        <view class="dropdown-item" 
          v-for="(area, index) in areaList" 
          :key="index"
          :class="{ 'active-item': area === selectedArea }"
          @click="selectArea(area)">
          <text class="dropdown-item-text">{{area}}</text>
          <text class="dropdown-item-check" v-if="area === selectedArea">✓</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 高级排序筛选弹出层 -->
    <view class="filter-dropdown sort-dropdown" v-if="showSortFilter" :style="{ top: sortDropdownTop + 'px' }">
      <view class="dropdown-item" 
        v-for="(sort, index) in sortList" 
        :key="index"
        :class="{ 'active-item': (sortBy === 'default' && sort === '最新') || sort === sortBy }"
        @click="selectSort(sort)">
        <text class="dropdown-item-text">{{sort}}</text>
        <text class="dropdown-item-check" v-if="(sortBy === 'default' && sort === '最新') || sort === sortBy">✓</text>
      </view>
    </view>
    
    <!-- 遮罩层 -->
    <view class="filter-mask" v-if="showAreaFilter || showSortFilter" @click="closeAllFilters"></view>
    
    <!-- 高级列表内容 -->
    <scroll-view 
      scroll-y 
      class="service-scroll" 
      @scrolltolower="loadMore" 
      refresher-enabled 
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      enhanced
      :bounces="true"
      :show-scrollbar="false"
    >
      <view v-if="serviceList.length > 0" class="service-list">
        <!-- 高级职位卡片 -->
        <view 
          v-for="(item, index) in serviceList" 
          :key="index" 
          class="service-item"
          hover-class="service-item-hover"
          @click="navigateToDetail(item)"
        >
          <view class="service-content">
            <view class="service-header">
              <view class="service-header-left">
              <text class="service-tag">{{item.category}}</text>
                <text class="service-subcategory" v-if="item.subcategory">{{item.subcategory}}</text>
            </view>
              <view class="service-meta-right">
                <text class="service-area">{{item.area || '全城'}}</text>
              </view>
            </view>
            
            <view class="service-main">
              <view class="service-title-wrapper">
            <text class="service-title">{{item.content}}</text>
                <text class="service-price" v-if="item.price">¥{{item.price}}</text>
              </view>
            
            <!-- 图片区域，如果有图片则显示 -->
            <view class="service-images" v-if="item.images && item.images.length > 0">
              <image 
                v-for="(img, imgIndex) in item.images.slice(0, 3)" 
                :key="imgIndex" 
                :src="img" 
                mode="aspectFill" 
                class="service-image"
                  :class="{'single-image': item.images.length === 1}"
              ></image>
            </view>
            
            <view class="service-footer">
              <view class="service-meta">
                  <view class="meta-tag">
                    <text class="meta-views">{{item.views || 0}}人浏览</text>
                  </view>
                  <view class="meta-time">{{item.time}}</view>
              </view>
              <view class="service-actions">
                  <view class="action-btn contact-btn">
                  <text class="action-text">联系</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 高级空状态 -->
      <view v-else class="empty-state">
        <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
        <text class="empty-text">暂无相关职位信息</text>
        <text class="empty-subtext">换个筛选条件试试吧</text>
        <view class="empty-btn" @click="resetFilters">重置筛选</view>
      </view>
      
      <!-- 加载更多状态 -->
      <view class="loading-more" v-if="serviceList.length > 0 && hasMore">
        <view class="loading-indicator"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 加载完成提示 -->
      <view class="loading-done" v-if="serviceList.length > 0 && !hasMore">
        <text class="loading-done-text">— 已经到底啦 —</text>
      </view>
    </scroll-view>
    
    <!-- 高级悬浮发布按钮 -->
    <view class="publish-btn" hover-class="publish-btn-hover" @click="navigateToPublish">
      <text class="publish-icon">+</text>
      <text class="publish-text">发布</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';

// 响应式状态
const statusBarHeight = ref(20);
const serviceType = ref('');
const serviceTitle = ref('服务列表');
const currentTab = ref(0);
const currentSubTab = ref(0);
const sortBy = ref('default');
const isRefreshing = ref(false);
const hasMore = ref(true);
const page = ref(1);
const limit = ref(10);
const serviceList = ref([]);
const showSubCategories = ref(false);
const subCategories = ref([]);
const showSubSubCategories = ref(false);
const subSubCategories = ref([]);
const showSearchBox = ref(false);
const searchKeyword = ref('');
const selectedArea = ref('全部区域');
const areaList = ref(['全部区域', '城区', '磁州镇', '讲武城镇', '岳城镇', '观台镇', '白土镇', '黄沙镇']);
const showAreaFilter = ref(false);
const showSortFilter = ref(false);
const sortList = ref(['最新', '最热', '附近']);
const areaDropdownTop = ref(44);
const sortDropdownTop = ref(44);
const areaBtn = ref(null);
const sortBtn = ref(null);

// 设置服务标题
const setServiceTitle = (type) => {
  const titleMap = {
    'home': '到家服务',
    'find': '寻找服务',
    'business': '生意转让',
    'job': '招聘信息',
    'resume': '求职信息',
    'house_rent': '房屋出租',
    'house_sell': '房屋出售',
    'second_car': '二手车辆',
    'pet': '宠物信息',
    'dating': '婚恋交友',
    'merchant_activity': '商家活动',
    'car': '车辆服务',
    'second_hand': '二手闲置',
    'carpool': '磁州拼车',
    'education': '教育培训',
    'other': '其他服务'
  };
  
  serviceTitle.value = titleMap[type] || '服务列表';
};

// 切换标签
const switchTab = (index) => {
  if (currentTab.value !== index) {
    currentTab.value = index;
    
    // 获取选中的服务类型
    const selectedType = subCategories.value[index].type;
    
    // 更新当前服务类型
    serviceType.value = selectedType;
    
    // 更新服务标题
    setServiceTitle(selectedType);
    
    // 加载对应的二级分类
    loadSecondLevelCategories(selectedType);
    
    // 重置列表数据
    page.value = 1;
    serviceList.value = [];
    hasMore.value = true;
    loadServiceList();
  }
};

// 切换子分类的标签
const switchSubTab = (index) => {
  if (currentSubTab.value !== index) {
    currentSubTab.value = index;
    
    // 重置列表数据
    page.value = 1;
    serviceList.value = [];
    hasMore.value = true;
    loadServiceList();
  }
};

// 切换排序方式
const toggleSort = (sort) => {
  if (sort === 'default') {
    showSortFilter.value = !showSortFilter.value;
    // 关闭其他筛选
    showAreaFilter.value = false;
    
    if (showSortFilter.value) {
      // 动态获取筛选栏底部位置
      nextTick(() => {
        const query = uni.createSelectorQuery();
        query.select('.filter-container').boundingClientRect(container => {
          if (container) {
            // 下拉菜单应该紧贴筛选栏底部
            sortDropdownTop.value = container.height + container.top;
          }
        }).exec();
      });
    }
  } else {
    sortBy.value = sort;
    page.value = 1;
    serviceList.value = [];
    hasMore.value = true;
    loadServiceList();
  }
};

// 选择排序
const selectSort = (sort) => {
  // 判断是否修改了排序方式
  const previousSort = sortBy.value;
  
  // 更新排序方式
  if (sort === '最新') {
    sortBy.value = 'default';
  } else {
    sortBy.value = sort; // 直接使用选择的排序名称
  }
  
  // 隐藏排序筛选
  showSortFilter.value = false;
  
  // 如果排序方式变化，重新加载数据
  if (previousSort !== sortBy.value) {
    page.value = 1;
    serviceList.value = [];
    hasMore.value = true;
    loadServiceList();
  }
};

// 加载服务列表数据
const loadServiceList = () => {
  // 模拟请求延迟
  uni.showLoading({
    title: '加载中'
  });
  
  // 获取当前二级分类类型
  const subCategoryType = showSubSubCategories.value && currentSubTab.value > 0
    ? subSubCategories.value[currentSubTab.value].type
    : 'all';
  
  // 模拟异步加载数据
  setTimeout(() => {
    // 这里模拟从本地存储获取发布的信息
    try {
      // 获取所有发布信息
      const allPublishedInfo = uni.getStorageSync('publishedInfo') || [];
      
      // 根据当前页面类型过滤
      let filteredList = [];
      
      // 根据当前选中的服务类型进行过滤
      switch (serviceType.value) {
        case 'home':
          // 到家服务
        if (subCategoryType === 'all') {
          filteredList = allPublishedInfo.filter(item => 
            item.type === 'home_service'
          );
        } else {
          filteredList = allPublishedInfo.filter(item => 
            item.type === 'home_service' && item.serviceType === subCategoryType
            );
          }
          break;
          
        case 'find':
          // 寻找服务
          if (subCategoryType === 'all') {
            filteredList = allPublishedInfo.filter(item => 
              item.type === 'find_service'
            );
          } else {
            filteredList = allPublishedInfo.filter(item => 
              item.type === 'find_service' && item.findCategory === subCategoryType
            );
          }
          break;
          
        case 'business':
          // 生意转让
  if (subCategoryType === 'all') {
    filteredList = allPublishedInfo.filter(item => 
              item.type === 'business_transfer'
    );
  } else {
    filteredList = allPublishedInfo.filter(item => 
              item.type === 'business_transfer' && item.businessCategory === subCategoryType
    );
  }
  break;
  
case 'job':
  // 招聘信息
  if (subCategoryType === 'all') {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'hire'
    );
  } else {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'hire' && item.jobCategory === subCategoryType
    );
  }
  break;
  
case 'resume':
  // 求职信息
  if (subCategoryType === 'all') {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'job_wanted'
    );
  } else {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'job_wanted' && item.resumeCategory === subCategoryType
    );
  }
  break;
  
case 'car':
  // 车辆服务
  if (subCategoryType === 'all') {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'car_service'
    );
  } else {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'car_service' && item.carServiceType === subCategoryType
    );
  }
  break;
  
case 'second_hand':
  // 二手闲置
  if (subCategoryType === 'all') {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'second_hand'
    );
  } else {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'second_hand' && item.secondHandCategory === subCategoryType
    );
  }
  break;
  
case 'carpool':
  // 磁州拼车
  if (subCategoryType === 'all') {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'carpool'
    );
  } else {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'carpool' && item.carpoolType === subCategoryType
    );
  }
  break;
  
case 'education':
  // 教育培训
  if (subCategoryType === 'all') {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'education'
    );
  } else {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'education' && item.educationType === subCategoryType
    );
  }
  break;
  
        case 'dating':
          // 婚恋交友
          if (subCategoryType === 'all') {
            filteredList = allPublishedInfo.filter(item => 
              item.type === 'dating'
            );
          } else {
            filteredList = allPublishedInfo.filter(item => 
              item.type === 'dating' && item.datingCategory === subCategoryType
            );
          }
          break;
          
        case 'merchant_activity':
          // 商家活动
          if (subCategoryType === 'all') {
            filteredList = allPublishedInfo.filter(item => 
              item.type === 'merchant_activity'
            );
          } else {
            filteredList = allPublishedInfo.filter(item => 
              item.type === 'merchant_activity' && item.activityCategory === subCategoryType
    );
  }
  break;
  
case 'other':
  // 其他服务
  if (subCategoryType === 'all') {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'other_service'
    );
  } else {
    filteredList = allPublishedInfo.filter(item => 
      item.type === 'other_service' && item.otherServiceType === subCategoryType
    );
  }
  break;
  
        case 'house_rent':
          // 房屋出租
  filteredList = allPublishedInfo.filter(item => 
            item.type === 'house_rent'
          );
          break;
          
        case 'house_sell':
          // 房屋出售
          filteredList = allPublishedInfo.filter(item => 
            item.type === 'house_sell'
          );
          break;
          
        case 'second_car':
          // 二手车辆
          filteredList = allPublishedInfo.filter(item => 
            item.type === 'used_car'
          );
          break;
          
        case 'pet':
          // 宠物信息
          filteredList = allPublishedInfo.filter(item => 
            item.type === 'pet'
          );
          break;
          
        default:
          // 默认显示所有信息
          filteredList = allPublishedInfo;
}

// 按排序方式排序
if (sortBy.value === 'default') {
  // 最新：按发布时间排序
  filteredList.sort((a, b) => new Date(b.publishTime || 0) - new Date(a.publishTime || 0));
} else if (sortBy.value === '最热') {
  // 最热：按浏览量排序
  filteredList.sort((a, b) => (b.views || 0) - (a.views || 0));
} else if (sortBy.value === '附近') {
  // 附近：按距离排序，有距离信息的显示在前面
  filteredList.sort((a, b) => {
    // 如果有距离信息，按距离排序
    if (a.distance && b.distance) {
      return (a.distance || 999999) - (b.distance || 999999);
    }
    // 如果只有a有距离信息，a排前面
    if (a.distance) return -1;
    // 如果只有b有距离信息，b排前面
    if (b.distance) return 1;
    // 都没有距离信息，保持原顺序
    return 0;
  });
}

// 分页处理
const start = (page.value - 1) * limit.value;
const end = page.value * limit.value;
const pageData = filteredList.slice(start, end);

// 更新数据和状态
if (pageData.length > 0) {
  serviceList.value = [...serviceList.value, ...pageData];
  hasMore.value = filteredList.length > serviceList.value.length;
} else {
  hasMore.value = false;
}

// 如果没有数据，添加一些示例数据
if (serviceList.value.length === 0) {
  addSampleData();
}
    } catch (e) {
      console.error('加载服务列表失败', e);
      addSampleData();
    }
    
    uni.hideLoading();
    
    // 如果是下拉刷新，结束刷新状态
    if (isRefreshing.value) {
      isRefreshing.value = false;
    }
  }, 800);
};

// 添加示例数据
const addSampleData = () => {
  // 根据当前选中的服务类型添加示例数据
  switch (serviceType.value) {
    case 'home':
      // 到家服务示例数据
        serviceList.value.push({
          id: 'sample-home-1',
          category: '家政服务',
          content: '专业家政保洁，开荒保洁，日常保洁，家电清洗，擦玻璃等服务',
          time: '2024-05-01 10:30',
          views: 128,
          images: ['/static/images/banner/banner-1.png'],
          type: 'home_service',
          pageType: 'home-service-detail'
        });
      
        serviceList.value.push({
          id: 'sample-home-2',
          category: '维修改造',
          content: '专业水电安装维修，管道疏通，灯具安装，墙面翻新',
          time: '2024-05-01 09:45',
          views: 156,
          images: ['/static/images/banner/banner-2.png'],
          type: 'home_service',
          pageType: 'home-service-detail'
        });
      
        serviceList.value.push({
          id: 'sample-home-3',
          category: '开锁换锁',
          content: '专业开锁换锁，汽车开锁，保险柜开锁，安装智能锁',
          time: '2024-05-01 08:20',
          views: 89,
          type: 'home_service',
          pageType: 'home-service-detail'
        });
      break;
      
    case 'business':
      // 生意转让示例数据
      serviceList.value.push({
        id: 'sample-business-1',
        category: '餐饮店铺',
        subcategory: '中餐店',
        content: '黄金地段餐饮店整体转让，客流稳定，接手即可盈利',
        time: '2024-05-01 14:30',
        views: 198,
        images: ['/static/images/banner/banner-3.png'],
        type: 'business_transfer',
        pageType: 'business-transfer-detail'
      });
      
      serviceList.value.push({
        id: 'sample-business-2',
        category: '零售店铺',
        subcategory: '服装店',
        content: '县城中心奶茶店转让，设备齐全，接手即可营业',
        time: '2024-05-01 15:20',
        views: 156,
        type: 'business_transfer',
        pageType: 'business-transfer-detail'
      });
      
      serviceList.value.push({
        id: 'sample-business-3',
        category: '美容美发',
        content: '美容美发店转让，位置好，客源稳定，接手即可营业',
        time: '2024-05-01 16:10',
        views: 142,
        type: 'business_transfer',
        pageType: 'business-transfer-detail'
      });
      break;
      
    case 'resume':
      // 求职信息示例数据
      serviceList.value.push({
        id: 'sample-resume-1',
        category: '求职信息',
        content: '会计专业毕业，有初级会计证书，两年工作经验，求职会计相关工作',
        time: '2024-05-01 15:30',
        views: 95,
        type: 'job_wanted',
        pageType: 'job-seeking-detail'
      });
      
      serviceList.value.push({
        id: 'sample-resume-2',
        category: '求职信息',
        content: '有多年销售经验，善于沟通，能吃苦耐劳，求职销售相关工作',
        time: '2024-05-01 14:45',
        views: 112,
        type: 'job_wanted',
        pageType: 'job-seeking-detail'
      });
      break;
      
    case 'find':
      // 寻找服务示例数据
      serviceList.value.push({
        id: 'sample-find-1',
        category: '维修服务',
        subcategory: '水电维修',
        content: '找水电工维修厨房水管漏水，今天急修',
        time: '2024-05-01 11:30',
        views: 75,
        type: 'find_service',
        pageType: 'find-service-detail'
      });
      
      serviceList.value.push({
        id: 'sample-find-2',
        category: '家政服务',
        subcategory: '保洁服务',
        content: '寻找家政阿姨，每周定期打扫家庭卫生',
        time: '2024-05-01 10:45',
        views: 86,
        type: 'find_service',
        pageType: 'find-service-detail'
      });
      
      serviceList.value.push({
        id: 'sample-find-3',
        category: '安装服务',
        subcategory: '家具安装',
        content: '寻找搬家服务，三室两厅搬家，本周六上午',
        time: '2024-05-01 09:15',
        views: 92,
        type: 'find_service',
        pageType: 'find-service-detail'
      });
      
      serviceList.value.push({
        id: 'sample-find-4',
        category: '教育服务',
        content: '寻找高中数学家教，高三学生，一对一辅导',
        time: '2024-05-01 08:45',
        views: 68,
        type: 'find_service',
        pageType: 'find-service-detail'
      });
      break;
      
    case 'dating':
      // 婚恋交友示例数据
        serviceList.value.push({
          id: 'sample-dating-1',
          category: '男士征婚',
          content: '35岁，身高178cm，本科学历，公务员，诚心寻找一位温柔贤惠的女士共度余生',
          time: '2024-05-01 09:30',
          views: 156,
          type: 'dating',
          pageType: 'dating-detail'
        });
      
        serviceList.value.push({
          id: 'sample-dating-2',
          category: '女士征婚',
          content: '32岁，身高165cm，硕士学历，教师，希望找一位成熟稳重、有责任心的男士',
          time: '2024-05-01 10:15',
          views: 188,
          type: 'dating',
          pageType: 'dating-detail'
        });
      
        serviceList.value.push({
          id: 'sample-dating-3',
          category: '相亲活动',
          content: '本周六下午2点，城区文化广场举办大型相亲交友活动，单身男女免费参加',
          time: '2024-05-01 11:20',
          views: 235,
          images: ['/static/images/banner/banner-3.jpg'],
          type: 'dating',
          pageType: 'dating-detail'
        });
      break;
      
    case 'merchant_activity':
      // 商家活动示例数据
        serviceList.value.push({
          id: 'sample-merchant-1',
          category: '促销活动',
          content: '五一大促销，全场商品5折起，部分商品买一送一，活动时间5月1日至5月5日',
          time: '2024-05-01 08:30',
          views: 245,
          images: ['/static/images/banner/banner-1.png'],
          type: 'merchant_activity',
          pageType: 'merchant-activity-detail'
        });
      
        serviceList.value.push({
          id: 'sample-merchant-2',
          category: '新店开业',
          content: '新店开业大酬宾，前100名顾客免费赠送精美礼品一份，消费满100元送50元代金券',
          time: '2024-05-01 09:45',
          views: 198,
          images: ['/static/images/banner/banner-2.png'],
          type: 'merchant_activity',
          pageType: 'merchant-activity-detail'
        });
      
        serviceList.value.push({
          id: 'sample-merchant-3',
          category: '满减活动',
          content: '本周末满减活动，满100减30，满200减80，满300减150，多买多省',
          time: '2024-05-01 10:30',
          views: 176,
          type: 'merchant_activity',
          pageType: 'merchant-activity-detail'
        });
      break;
      
    case 'job':
      // 招聘信息示例数据
        serviceList.value.push({
          id: 'sample-job-1',
          category: '销售',
          content: '招聘销售人员3名，底薪3000+提成，要求有销售经验',
          time: '2024-05-01 09:15',
          views: 210,
          type: 'hire',
          pageType: 'job-detail'
        });
      
        serviceList.value.push({
          id: 'sample-job-2',
          category: '服务员',
          content: '餐厅招聘服务员2名，有经验优先，包吃住',
          time: '2024-05-01 11:30',
          views: 150,
          type: 'hire',
          pageType: 'job-detail'
        });
      
        serviceList.value.push({
          id: 'sample-job-3',
          category: '司机',
          content: '招聘专职司机，C1驾照，有3年以上驾龄，底薪4500',
          time: '2024-05-01 08:45',
          views: 180,
          type: 'hire',
          pageType: 'job-detail'
        });
      break;
      
    case 'car':
      // 车辆服务示例数据
        serviceList.value.push({
          id: 'sample-car-1',
          category: '汽车维修',
          content: '专业汽车维修，各种故障检测维修，价格合理',
          time: '2024-05-01 10:15',
          views: 95,
          type: 'car_service',
        pageType: 'car-service-detail'
        });
      
        serviceList.value.push({
          id: 'sample-car-2',
          category: '汽车保养',
          content: '汽车保养套餐，包含机油机滤更换，全车检查等服务',
          time: '2024-05-01 09:30',
          views: 120,
          type: 'car_service',
        pageType: 'car-service-detail'
        });
      break;
      
    case 'second_hand':
      // 二手闲置示例数据
        serviceList.value.push({
          id: 'sample-second-hand-1',
          category: '手机数码',
          content: '出售二手iPhone 13，128G，成色95新，有发票保修',
          time: '2024-05-01 14:20',
          views: 230,
          images: ['/static/images/banner/banner-3.png'],
          type: 'second_hand',
          pageType: 'info-detail'
        });
      
        serviceList.value.push({
          id: 'sample-second-hand-2',
          category: '家具家居',
          content: '搬家出售九成新沙发一套，茶几，餐桌等家具',
          time: '2024-05-01 13:15',
          views: 185,
          type: 'second_hand',
          pageType: 'info-detail'
        });
      break;
      
    case 'education':
      // 教育培训示例数据
        serviceList.value.push({
          id: 'sample-education-1',
          category: '小学辅导',
          content: '小学一对一辅导，语数英全科，有丰富教学经验',
          time: '2024-05-01 16:30',
          views: 145,
          type: 'education',
          pageType: 'info-detail'
        });
      
        serviceList.value.push({
          id: 'sample-education-2',
          category: '英语培训',
          content: '英语口语培训，外教一对一，提高口语能力',
          time: '2024-05-01 15:20',
          views: 168,
          type: 'education',
          pageType: 'info-detail'
        });
      break;
      
    case 'carpool':
      // 磁州拼车示例数据
        serviceList.value.push({
          id: 'sample-carpool-1',
          category: '上下班拼车',
          content: '工作日上下班拼车，磁州到市区，早7点发车，晚6点返回',
          time: '2024-05-01 18:30',
          views: 112,
          type: 'carpool',
          pageType: 'info-detail'
        });
      
        serviceList.value.push({
          id: 'sample-carpool-2',
          category: '城际拼车',
          content: '周末磁州到石家庄拼车，周六早出发，周日晚返回',
          time: '2024-05-01 17:15',
          views: 135,
          type: 'carpool',
          pageType: 'info-detail'
        });
      break;
      
    case 'house_rent':
      // 房屋出租示例数据
      serviceList.value.push({
        id: 'sample-house-rent-1',
        category: '整租',
        content: '城区两室一厅出租，精装修，家电齐全，拎包入住',
        time: '2024-05-01 14:30',
        views: 220,
        images: ['/static/images/banner/banner-2.png'],
        type: 'house_rent',
        pageType: 'info-detail'
      });
      
      serviceList.value.push({
        id: 'sample-house-rent-2',
        category: '合租',
        content: '城区三室一厅合租，主卧带阳台，家电齐全，拎包入住',
        time: '2024-05-01 13:20',
        views: 175,
        type: 'house_rent',
        pageType: 'info-detail'
      });
      break;
      
    case 'house_sell':
      // 房屋出售示例数据
      serviceList.value.push({
        id: 'sample-house-sell-1',
        category: '商品房',
        content: '城区新小区三室两厅出售，110平米，精装修，南北通透',
        time: '2024-05-01 15:30',
        views: 245,
        images: ['/static/images/banner/banner-3.png'],
        type: 'house_sell',
        pageType: 'info-detail'
      });
      
      serviceList.value.push({
        id: 'sample-house-sell-2',
        category: '二手房',
        content: '老城区两室一厅出售，70平米，简装修，交通便利',
        time: '2024-05-01 14:15',
        views: 198,
        type: 'house_sell',
        pageType: 'info-detail'
      });
      break;
      
    case 'second_car':
      // 二手车辆示例数据
      serviceList.value.push({
        id: 'sample-second-car-1',
        category: '轿车',
        content: '2020年大众朗逸，1.5L自动挡，行驶3万公里，车况良好',
        time: '2024-05-01 11:30',
        views: 210,
        images: ['/static/images/banner/banner-1.png'],
        type: 'used_car',
        pageType: 'info-detail'
      });
      
      serviceList.value.push({
        id: 'sample-second-car-2',
        category: 'SUV',
        content: '2019年本田CR-V，2.0L自动挡，行驶5万公里，车况良好',
        time: '2024-05-01 10:15',
        views: 185,
        type: 'used_car',
        pageType: 'info-detail'
      });
      break;
      
    case 'pet':
      // 宠物信息示例数据
      serviceList.value.push({
        id: 'sample-pet-1',
        category: '狗狗',
        content: '出售纯种金毛幼犬，2个月大，已打疫苗，健康活泼',
        time: '2024-05-01 13:30',
        views: 165,
        images: ['/static/images/banner/banner-3.png'],
        type: 'pet',
        pageType: 'info-detail'
      });
      
      serviceList.value.push({
        id: 'sample-pet-2',
        category: '猫咪',
        content: '出售英短蓝猫，3个月大，已打疫苗，粘人可爱',
        time: '2024-05-01 12:15',
        views: 145,
        type: 'pet',
        pageType: 'info-detail'
      });
      break;
      
    case 'other':
      // 其他服务示例数据
        serviceList.value.push({
          id: 'sample-other-1',
          category: '法律服务',
          content: '专业律师咨询服务，合同审核，法律纠纷解决',
          time: '2024-05-01 11:45',
          views: 78,
          type: 'other_service',
          pageType: 'info-detail'
        });
      
        serviceList.value.push({
          id: 'sample-other-2',
          category: '设计服务',
          content: '平面设计，logo设计，海报设计，价格合理',
          time: '2024-05-01 10:50',
          views: 92,
          type: 'other_service',
          pageType: 'info-detail'
      });
      break;
      
    default:
      // 默认示例数据
      serviceList.value.push({
        id: 'sample-default-1',
        category: '服务信息',
        content: '这是一条示例服务信息',
        time: '2024-05-01 12:00',
        views: 50,
        type: serviceType.value,
        pageType: 'info-detail'
      });
  }
  
  // 随机添加更多示例数据
  hasMore.value = serviceList.value.length >= 5;
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  page.value = 1;
  serviceList.value = [];
  hasMore.value = true;
  loadServiceList();
};

// 加载更多
const loadMore = () => {
  if (hasMore.value) {
    page.value++;
    loadServiceList();
  }
};

// 导航到详情页
const navigateToDetail = (item) => {
  let url = '';
  
  // 根据不同的类型跳转到不同的详情页
  if (item.pageType) {
    // 如果是到家服务，确保传递正确的服务类型参数
    if (item.type === 'home_service' && item.serviceType) {
      url = `/pages/publish/${item.pageType}?id=${item.id}&type=${item.serviceType}`;
    } else {
      url = `/pages/publish/${item.pageType}?id=${item.id}`;
    }
  } else {
    // 根据当前服务类型确定详情页
    switch (serviceType.value) {
      case 'home':
        // 获取当前选中的子分类
    let subType = 'home_cleaning'; // 默认为家政服务
    if (currentTab.value > 0 && subCategories.value[currentTab.value]) {
      subType = subCategories.value[currentTab.value].type;
    }
    url = `/pages/publish/home-service-detail?id=${item.id}&type=${item.serviceType || subType}`;
        break;
        
      case 'find':
    url = `/pages/publish/find-service-detail?id=${item.id}`;
        break;
        
      case 'business':
        url = `/pages/publish/business-transfer-detail?id=${item.id}`;
        break;
        
      case 'job':
    url = `/pages/publish/job-detail?id=${item.id}`;
        break;
        
      case 'resume':
        url = `/pages/publish/job-seeking-detail?id=${item.id}`;
        break;
        
      case 'dating':
    url = `/pages/publish/dating-detail?id=${item.id}`;
        break;
        
      case 'merchant_activity':
    url = `/pages/publish/merchant-activity-detail?id=${item.id}`;
        break;
        
      case 'car':
        url = `/pages/publish/car-service-detail?id=${item.id}`;
        break;
        
      case 'second_hand':
        url = `/pages/publish/second-hand-detail?id=${item.id}`;
        break;
        
      case 'carpool':
    url = `/pages/publish/carpool-detail?id=${item.id}`;
        break;
        
      case 'education':
    url = `/pages/publish/education-detail?id=${item.id}`;
        break;
        
      default:
        url = `/pages/publish/info-detail?id=${item.id}&type=${serviceType.value}`;
    }
  }
  
  console.log('跳转到详情页：', url);
  
  uni.navigateTo({
    url
  });
};

// 导航到发布页面
const navigateToPublish = () => {
  if (serviceType.value === 'home') {
    // 到家服务特殊处理，先选择子分类
    uni.navigateTo({
      url: '/pages/publish/service-category'
    });
  } else {
    // 其他服务直接跳转到对应发布页
    const typeMap = {
      'find': 'find_service',
      'business': 'business_transfer',
      'job': 'hire',
      'resume': 'job_wanted',
      'house_rent': 'house_rent',
      'house_sell': 'house_sell',
      'second_car': 'used_car',
      'pet': 'pet',
      'dating': 'dating',
      'merchant_activity': 'merchant_activity',
      'car': 'car_service',
      'second_hand': 'second_hand',
      'carpool': 'carpool',
      'education': 'education',
      'other': 'other_service'
    };
    
    const publishType = typeMap[serviceType.value] || 'other_service';
    const publishName = serviceTitle.value;
    
    uni.navigateTo({
      url: `/pages/publish/detail?type=${publishType}&name=${encodeURIComponent(publishName)}&categoryType=${publishType}&categoryName=${encodeURIComponent(publishName)}`
    });
  }
};

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 打开筛选页面
const openFilter = () => {
  // 添加触感反馈
  uni.vibrateShort();
  
  // 打开筛选页面
  uni.navigateTo({
    url: `/subPackages/service/pages/filter?type=${serviceType.value}&title=${encodeURIComponent(serviceTitle.value)}&active=${serviceType.value}`
  });
};

// 切换区域筛选
const toggleAreaFilter = () => {
  showAreaFilter.value = !showAreaFilter.value;
  // 关闭其他筛选
  showSortFilter.value = false;
  
  if (showAreaFilter.value) {
    // 动态获取按钮底部位置
    nextTick(() => {
      const query = uni.createSelectorQuery();
      query.select('.filter-container').boundingClientRect(container => {
        if (container) {
          // 下拉菜单应该紧贴筛选栏底部
          areaDropdownTop.value = container.height + container.top;
        }
      }).exec();
    });
  }
};

// 选择区域
const selectArea = (area) => {
  if (selectedArea.value !== area) {
    selectedArea.value = area;
    // 重置列表数据
    page.value = 1;
    serviceList.value = [];
    hasMore.value = true;
    loadServiceList();
  }
  showAreaFilter.value = false;
};

// 关闭所有筛选
const closeAllFilters = () => {
  showAreaFilter.value = false;
  showSortFilter.value = false;
};

// 应用关键词搜索
const applyKeywordSearch = () => {
  // 实现关键词搜索逻辑
  console.log('应用关键词搜索：', searchKeyword.value);
};

// 重置筛选
const resetFilters = () => {
  showAreaFilter.value = false;
  showSortFilter.value = false;
  page.value = 1;
  serviceList.value = [];
  hasMore.value = true;
  loadServiceList();
};

// 页面加载
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 获取服务类型
  if (options.type) {
    serviceType.value = options.type;
    setServiceTitle(options.type);
    
    // 一级分类显示
    showSubCategories.value = true;
    
    // 初始化一级分类列表 - 这里是所有可用的服务类型
    subCategories.value = [
      { name: '到家服务', type: 'home' },
      { name: '寻找服务', type: 'find' },
      { name: '生意转让', type: 'business' },
      { name: '招聘信息', type: 'job' },
      { name: '求职信息', type: 'resume' },
      { name: '房屋出租', type: 'house_rent' },
      { name: '房屋出售', type: 'house_sell' },
      { name: '二手车辆', type: 'second_car' },
      { name: '宠物信息', type: 'pet' },
      { name: '商家活动', type: 'merchant_activity' },
      { name: '婚恋交友', type: 'dating' },
      { name: '车辆服务', type: 'car' },
      { name: '二手闲置', type: 'second_hand' },
      { name: '磁州拼车', type: 'carpool' },
      { name: '教育培训', type: 'education' },
      { name: '其他服务', type: 'other' }
    ];
    
    // 设置当前选中的一级分类
    const currentTypeIndex = subCategories.value.findIndex(item => item.type === options.type);
    if (currentTypeIndex > -1) {
      currentTab.value = currentTypeIndex;
    }
    
    // 根据当前选中的一级分类加载对应的二级分类
    loadSecondLevelCategories(options.type);
    
    // 根据服务类型设置搜索框显示
    switch(options.type) {
      case 'home':
      case 'find':
      case 'business':
      case 'job':
      case 'resume':
      case 'dating':
      case 'merchant_activity':
        showSearchBox.value = true;
        break;
      default:
        showSearchBox.value = false;
    }
  }

  // 加载服务列表数据
  loadServiceList();
});

// 加载二级分类
const loadSecondLevelCategories = (type) => {
  // 根据一级分类类型加载对应的二级分类
  switch(type) {
    case 'home':
      // 到家服务二级分类
      subSubCategories.value = [
        { name: '全部', type: 'all' },
        { name: '家政服务', type: 'home_cleaning' },
        { name: '维修改造', type: 'repair' },
        { name: '上门安装', type: 'installation' },
        { name: '开锁换锁', type: 'locksmith' },
        { name: '搬家拉货', type: 'moving' },
        { name: '上门美容', type: 'beauty' },
        { name: '上门家教', type: 'tutor' },
        { name: '宠物服务', type: 'pet_service' },
        { name: '上门疏通', type: 'plumbing' },
        { name: '其他类型', type: 'other' }
      ];
      showSubSubCategories.value = true;
      break;
      
    case 'find':
      // 寻找服务二级分类
      subSubCategories.value = [
        { name: '全部服务', type: 'all' },
        { name: '家政服务', type: 'home_service' },
        { name: '维修服务', type: 'repair' },
        { name: '安装服务', type: 'installation' },
        { name: '搬家服务', type: 'moving' },
        { name: '美容服务', type: 'beauty' },
        { name: '教育服务', type: 'education' },
        { name: '其他服务', type: 'other' }
      ];
      showSubSubCategories.value = true;
      break;
      
    case 'business':
      // 生意转让二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '餐饮店铺', type: 'restaurant' },
        { name: '零售店铺', type: 'retail' },
        { name: '美容美发', type: 'beauty_salon' },
        { name: '服装店铺', type: 'clothing' },
        { name: '便利超市', type: 'convenience' },
        { name: '其他店铺', type: 'other' }
      ];
      showSubSubCategories.value = true;
      break;
      
      case 'job':
      // 招聘信息二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '销售', type: 'sales' },
        { name: '服务员', type: 'waiter' },
        { name: '技工', type: 'technician' },
        { name: '司机', type: 'driver' },
        { name: '厨师', type: 'chef' },
        { name: '会计', type: 'accountant' },
        { name: '文员', type: 'clerk' },
        { name: '保安', type: 'security' },
        { name: '其他', type: 'other' }
      ];
      showSubSubCategories.value = true;
        break;
      
      case 'resume':
      // 求职信息二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '销售类', type: 'sales' },
        { name: '技术类', type: 'tech' },
        { name: '服务类', type: 'service' },
        { name: '行政类', type: 'admin' },
        { name: '教育类', type: 'education' },
        { name: '其他类', type: 'other' }
      ];
      showSubSubCategories.value = true;
        break;
      
      case 'dating':
      // 婚恋交友二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '男士征婚', type: 'male_dating' },
        { name: '女士征婚', type: 'female_dating' },
        { name: '恋爱交友', type: 'friendship' },
        { name: '相亲活动', type: 'matchmaking' },
        { name: '婚恋平台', type: 'dating_platform' },
        { name: '其他', type: 'other' }
      ];
      showSubSubCategories.value = true;
        break;
      
      case 'merchant_activity':
      // 商家活动二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '促销活动', type: 'promotion' },
        { name: '新店开业', type: 'opening' },
        { name: '优惠券', type: 'coupon' },
        { name: '满减活动', type: 'discount' },
        { name: '限时特价', type: 'special_price' },
        { name: '积分活动', type: 'points' },
        { name: '其他活动', type: 'other' }
      ];
      showSubSubCategories.value = true;
        break;
      
      case 'car':
      // 车辆服务二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '汽车维修', type: 'repair' },
        { name: '汽车保养', type: 'maintenance' },
        { name: '汽车美容', type: 'beauty' },
        { name: '汽车改装', type: 'modification' },
        { name: '汽车救援', type: 'rescue' },
        { name: '其他', type: 'other' }
      ];
      showSubSubCategories.value = true;
        break;
      
      case 'second_hand':
      // 二手闲置二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '手机数码', type: 'digital' },
        { name: '家用电器', type: 'appliance' },
        { name: '家具家居', type: 'furniture' },
        { name: '服装鞋帽', type: 'clothing' },
        { name: '母婴用品', type: 'baby' },
        { name: '运动户外', type: 'sports' },
        { name: '图书音像', type: 'books' },
        { name: '其他', type: 'other' }
      ];
      showSubSubCategories.value = true;
        break;
      
      case 'carpool':
      // 磁州拼车二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '上下班拼车', type: 'commute' },
        { name: '城际拼车', type: 'intercity' },
        { name: '回乡拼车', type: 'hometown' },
        { name: '其他', type: 'other' }
      ];
      showSubSubCategories.value = true;
        break;
      
      case 'education':
      // 教育培训二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '学前教育', type: 'preschool' },
        { name: '小学辅导', type: 'primary' },
        { name: '中学辅导', type: 'secondary' },
        { name: '高考辅导', type: 'college_entrance' },
        { name: '英语培训', type: 'english' },
        { name: '音乐培训', type: 'music' },
        { name: '美术培训', type: 'art' },
        { name: '体育培训', type: 'sports' },
        { name: '职业技能', type: 'vocational' },
        { name: '其他', type: 'other' }
      ];
      showSubSubCategories.value = true;
        break;
      
      case 'other':
      // 其他服务二级分类
      subSubCategories.value = [
        { name: '全部分类', type: 'all' },
        { name: '法律服务', type: 'legal' },
        { name: '金融服务', type: 'financial' },
        { name: '设计服务', type: 'design' },
        { name: '网络服务', type: 'internet' },
        { name: '翻译服务', type: 'translation' },
        { name: '婚庆服务', type: 'wedding' },
        { name: '摄影服务', type: 'photography' },
        { name: '其他', type: 'other' }
      ];
      showSubSubCategories.value = true;
        break;
      
      default:
      // 默认不显示二级分类
      subSubCategories.value = [];
      showSubSubCategories.value = false;
  }
  
  // 重置二级分类选中状态
  currentSubTab.value = 0;
};
</script>

<style lang="scss" scoped>
/* 全局样式 */
.service-list-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f6f8fa;
  
  &.premium-style {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }
}

/* 固定头部区域 */
.custom-navbar,
.search-container,
.category-tabs,
.subcategory-tabs,
.filter-container {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

/* 自定义导航栏 - 最顶层 */
.custom-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background: linear-gradient(120deg, #0070f3, #00a1ff);
  padding: 0 16px;
  position: sticky;
  top: 0;
  z-index: 105;
  box-shadow: 0 1px 12px rgba(0, 112, 243, 0.18);
}

/* 搜索框 - 第二层 */
.search-container {
  padding: 12px 16px 16px;
  background: #fff;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  margin-bottom: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 44px;
  z-index: 104;
}

/* 一级分类标签栏 - 第三层 */
.category-tabs {
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
  padding: 14px 0 12px;
  white-space: nowrap;
  border-radius: 16px;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  position: sticky;
  top: calc(44px + 68px); /* 导航栏高度 + 搜索框高度 */
  z-index: 103;
}

/* 二级分类标签栏 - 第四层 */
.subcategory-tabs {
  background: rgba(255, 255, 255, 0.95);
  padding: 10px 0;
  white-space: nowrap;
  border-radius: 16px;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  position: sticky;
  top: calc(44px + 68px + 60px); /* 导航栏高度 + 搜索框高度 + 一级分类高度 */
  z-index: 102;
}

/* 筛选栏 - 第五层 */
.filter-container {
  display: flex;
  height: 54px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  position: sticky;
  top: calc(44px + 68px + 60px + 54px); /* 导航栏高度 + 搜索框高度 + 一级分类高度 + 二级分类高度 */
  justify-content: center;
  padding: 0;
  z-index: 101;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

/* 列表内容区域 - 需要添加足够的上边距以避免被固定头部遮挡 */
.service-scroll {
  flex: 1;
  box-sizing: border-box;
  padding: 0 12px;
  margin-top: 12px; /* 添加一些顶部边距 */
}

.back-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 2;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
  transition: transform 0.2s ease;
}

.back-btn:active .back-icon {
  transform: rotate(-45deg) scale(0.9);
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: -0.2px;
}

.navbar-right {
  width: 36px;
  position: relative;
  z-index: 2;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 12px;
  padding: 0 12px;
  height: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}

.search-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 40px;
  font-size: 15px;
  color: #333;
  background: transparent;
}

.search-cancel {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #999;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.tab-item {
  display: inline-block;
  padding: 8px 20px;
  margin: 0 5px;
  font-size: 14px;
  color: #555;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  position: relative;
}

.tab-item:first-child {
  margin-left: 16px;
}

.tab-item:last-child {
  margin-right: 16px;
}

.tab-item.active {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}

.subtab-item {
  display: inline-block;
  padding: 6px 16px;
  margin: 0 5px;
  font-size: 13px;
  color: #666;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  background-color: #f1f2f6;
}

.subtab-item:first-child {
  margin-left: 16px;
}

.subtab-item:last-child {
  margin-right: 16px;
}

.subtab-item.active {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}

.filter-wrapper {
  display: flex;
  width: 100%;
  height: 54px;
  border-radius: 16px;
  overflow: hidden;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
  font-size: 15px;
  color: #333;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.filter-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.filter-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.05);
}

.active-filter {
  color: #0070f3;
  font-weight: 600;
}

.filter-arrow {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999;
  margin-left: 8px;
  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.arrow-up {
  transform: rotate(180deg);
  border-top-color: #0070f3;
}

/* 高级筛选下拉菜单 */
.filter-dropdown {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  z-index: 99;
  max-height: 60vh;
  animation: dropDown 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  border-radius: 16px;
  overflow: hidden;
  margin-top: 8px;
}

@keyframes dropDown {
  from { opacity: 0; transform: translateY(-12px); }
  to { opacity: 1; transform: translateY(0); }
}

.area-dropdown {
  left: 16px;
  width: calc(50% - 24px);
}

.sort-dropdown {
  right: 16px;
  width: calc(50% - 24px);
}

.dropdown-scroll {
  max-height: 50vh;
}

.dropdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  font-size: 15px;
  color: #333;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.2s;
}

.dropdown-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.dropdown-item.active-item {
  color: #0070f3;
  font-weight: 600;
  background-color: rgba(0, 112, 243, 0.05);
}

.dropdown-item-text {
  flex: 1;
}

.dropdown-item-check {
  color: #0070f3;
  font-weight: bold;
  margin-left: 8px;
}

/* 遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 90;
  backdrop-filter: blur(2px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.service-list {
  padding-bottom: 16px;
}

.service-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
  border-radius: 18px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.service-item-hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.service-content {
  padding: 18px;
}

.service-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.service-header-left {
  display: flex;
  align-items: center;
}

.service-meta-right {
  display: flex;
  align-items: center;
}

.service-tag {
  background: linear-gradient(135deg, rgba(0, 112, 243, 0.1), rgba(0, 161, 255, 0.15));
  color: #0070f3;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 8px;
  font-weight: 600;
  letter-spacing: -0.2px;
}

.service-subcategory {
  background-color: rgba(0, 112, 243, 0.06);
  color: #0070f3;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 8px;
  margin-left: 8px;
  letter-spacing: -0.2px;
}

.service-area {
  background-color: #f1f2f6;
  color: #666;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 8px;
}

.service-main {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.service-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.service-title {
  font-size: 17px;
  color: #222;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: -0.2px;
  flex: 1;
}

.service-price {
  color: #ff3b30;
  font-weight: 600;
  font-size: 17px;
  margin-left: 12px;
}

.service-images {
  display: flex;
  gap: 8px;
  margin: 4px 0;
}

.service-image {
  width: 32%;
  height: 100px;
  border-radius: 12px;
  background-color: #f5f5f5;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.service-image.single-image {
  width: 100%;
  height: 160px;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.service-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.meta-tag {
  background-color: rgba(0, 0, 0, 0.04);
  padding: 4px 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
}

.meta-views {
  font-size: 13px;
  color: #666;
}

.meta-time {
  font-size: 13px;
  color: #999;
}

.service-actions {
  display: flex;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 20px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  border-radius: 24px;
  font-size: 14px;
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.96);
  box-shadow: 0 2px 8px rgba(0, 112, 243, 0.2);
}

.contact-btn {
  letter-spacing: 1px;
}

/* 高级空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
}

.empty-image {
  width: 140px;
  height: 140px;
  margin-bottom: 24px;
  opacity: 0.8;
}

.empty-text {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: -0.3px;
}

.empty-subtext {
  color: #999;
  font-size: 15px;
  margin-bottom: 24px;
}

.empty-btn {
  padding: 10px 24px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  border-radius: 24px;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}

/* 加载更多状态 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  gap: 8px;
}

.loading-indicator {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 112, 243, 0.3);
  border-top: 2px solid #0070f3;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #777;
  font-size: 14px;
}

.loading-done {
  text-align: center;
  padding: 20px 0;
}

.loading-done-text {
  color: #999;
  font-size: 14px;
}

/* 高级悬浮发布按钮 */
.publish-btn {
  position: fixed;
  right: 20px;
  bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px 0 22px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  box-shadow: 0 6px 16px rgba(0, 112, 243, 0.3);
  z-index: 90;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.publish-btn-hover {
  transform: scale(0.95);
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}

.publish-icon {
  color: #fff;
  font-size: 24px;
  font-weight: 300;
  line-height: 1;
  margin-right: 6px;
}

.publish-text {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}
</style>