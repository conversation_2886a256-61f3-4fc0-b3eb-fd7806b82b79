{"version": 3, "file": "SearchAndCategory.js", "sources": ["subPackages/activity-showcase/components/mall/SearchAndCategory.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9tYWxsL1NlYXJjaEFuZENhdGVnb3J5LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"search-category-container\">\n    <!-- 搜索框 -->\n    <view class=\"search-box\" @click=\"$emit('search')\">\n      <view class=\"search-input\">\n        <svg class=\"search-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n          <path d=\"M11 17.25a6.25 6.25 0 110-12.5 6.25 6.25 0 010 12.5zm0 0L21 21\" stroke=\"#8E8E93\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n        </svg>\n        <text class=\"placeholder\">搜索磁州商品、店铺</text>\n      </view>\n      <view class=\"scan-btn\" @click.stop=\"$emit('scan')\">\n        <svg class=\"scan-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\n          <path d=\"M7 3H5a2 2 0 0 0-2 2v2M17 3h2a2 2 0 0 1 2 2v2M7 21H5a2 2 0 0 1-2-2v-2M17 21h2a2 2 0 0 0 2-2v-2\" stroke=\"#FF3B69\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          <rect x=\"9\" y=\"9\" width=\"6\" height=\"6\" rx=\"1\" stroke=\"#FF3B69\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></rect>\n        </svg>\n      </view>\n    </view>\n\n    <!-- 分类导航 -->\n    <view class=\"category-grid\">\n        <view \n          v-for=\"(category, index) in categories\" \n          :key=\"index\" \n          class=\"category-item\"\n          :class=\"{ active: currentCategory === index }\"\n          @click=\"selectCategory(index)\"\n        >\n        <view class=\"category-icon-wrapper\">\n          <view class=\"category-icon\" :style=\"{ background: getGradientBackground(category.bgColor) }\">\n            <image class=\"icon-image\" :src=\"category.icon\" mode=\"aspectFit\"></image>\n          </view>\n          <view class=\"category-indicator\" v-if=\"currentCategory === index\"></view>\n        </view>\n        <text class=\"category-name\">{{ category.name }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\n\n// 组件属性定义\nconst props = defineProps({\n  categories: {\n    type: Array,\n    default: () => []\n  }\n});\n\n// 定义事件\nconst emit = defineEmits(['search', 'scan', 'categoryChange']);\n\n// 当前选中的分类\nconst currentCategory = ref(0);\n\n// 选择分类\nconst selectCategory = (index) => {\n  currentCategory.value = index;\n  // 触发分类变更事件\n  emit('categoryChange', index);\n};\n\n// 生成渐变背景\nconst getGradientBackground = (baseColor) => {\n  // 根据基础颜色生成渐变\n  return `linear-gradient(135deg, ${baseColor} 0%, ${lightenColor(baseColor, 20)} 100%)`;\n};\n\n// 颜色变亮函数\nconst lightenColor = (hex, percent) => {\n  // 将颜色转换为RGB\n  let r = parseInt(hex.substring(1, 3), 16);\n  let g = parseInt(hex.substring(3, 5), 16);\n  let b = parseInt(hex.substring(5, 7), 16);\n  \n  // 增加亮度\n  r = Math.min(255, Math.floor(r * (1 + percent / 100)));\n  g = Math.min(255, Math.floor(g * (1 + percent / 100)));\n  b = Math.min(255, Math.floor(b * (1 + percent / 100)));\n  \n  // 转换回HEX格式\n  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.search-category-container {\n  padding: 20rpx 30rpx 30rpx;\n  background: #FFFFFF;\n  border-radius: 0 0 30rpx 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  margin-bottom: 20rpx;\n}\n\n/* 搜索框 */\n.search-box {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24rpx;\n  \n  .search-input {\n    flex: 1;\n    height: 80rpx;\n    background-color: #F2F2F7;\n    border-radius: 40rpx;\n    display: flex;\n    align-items: center;\n    padding: 0 30rpx;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n    \n    .search-icon {\n      margin-right: 10rpx;\n      color: #8E8E93;\n    }\n    \n    .placeholder {\n      font-size: 28rpx;\n      color: #8E8E93;\n    }\n  }\n  \n  .scan-btn {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-left: 20rpx;\n    background-color: #F2F2F7;\n    border-radius: 50%;\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n    \n    .scan-icon {\n      color: #FF3B69;\n    }\n  }\n}\n\n/* 分类导航 - 网格布局 */\n.category-grid {\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  gap: 16rpx 10rpx;\n  \n  .category-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    \n    .category-icon-wrapper {\n      position: relative;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      margin-bottom: 8rpx;\n    }\n      \n      .category-icon {\n      width: 90rpx;\n      height: 90rpx;\n      border-radius: 24rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      transition: all 0.3s ease;\n      box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);\n        \n        .icon-image {\n        width: 50rpx;\n        height: 50rpx;\n        }\n    }\n    \n    .category-indicator {\n      position: absolute;\n      bottom: -8rpx;\n      width: 16rpx;\n      height: 4rpx;\n      background-color: #FF3B69;\n      border-radius: 2rpx;\n      transition: all 0.3s ease;\n      }\n      \n      .category-name {\n        font-size: 24rpx;\n        color: #333333;\n      transition: all 0.3s ease;\n      width: 100%;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      }\n      \n      &.active {\n        .category-icon {\n        transform: translateY(-4rpx);\n        box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);\n      }\n      \n      .category-indicator {\n        width: 32rpx;\n        }\n        \n        .category-name {\n          color: #FF3B69;\n        font-weight: 600;\n      }\n    }\n  }\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/mall/SearchAndCategory.vue'\nwx.createComponent(Component)"], "names": ["ref"], "mappings": ";;;;;;;;;;;;;;;;;;AAmDA,UAAM,OAAO;AAGb,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAG7B,UAAM,iBAAiB,CAAC,UAAU;AAChC,sBAAgB,QAAQ;AAExB,WAAK,kBAAkB,KAAK;AAAA,IAC9B;AAGA,UAAM,wBAAwB,CAAC,cAAc;AAE3C,aAAO,2BAA2B,SAAS,QAAQ,aAAa,WAAW,EAAE,CAAC;AAAA,IAChF;AAGA,UAAM,eAAe,CAAC,KAAK,YAAY;AAErC,UAAI,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AACxC,UAAI,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AACxC,UAAI,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AAGxC,UAAI,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,IAAI,UAAU,IAAI,CAAC;AACrD,UAAI,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,IAAI,UAAU,IAAI,CAAC;AACrD,UAAI,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,IAAI,UAAU,IAAI,CAAC;AAGrD,aAAO,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IAChH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClFA,GAAG,gBAAgB,SAAS;"}