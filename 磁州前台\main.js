import App from './App'
import { createSSRApp } from 'vue'
import { setupGlobalErrorHandlers } from './utils/errorHandler'
import RedPacketComponents from './components/RedPacket'
import cuCustom from './components/cu-custom.vue'
// 移除直接导入商家后台组件库的代码
// 改为使用条件导入或延迟导入

// 创建一个简单的商家工具对象，不从外部导入
const merchantPlugin = {
  getMerchantInfo: function(id) {
    return { id, name: '测试商家' };
  }
};

// 初始化全局错误处理
const { reportError } = setupGlobalErrorHandlers()

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 引入全局样式
import './common/css/global.css'
import './static/styles/partner-styles.css'

// 引入用户信息管理模块
import { getLocalUserInfo } from './utils/userProfile.js'

// 引入推广工具组件
import PromotionToolButton from '@/components/PromotionToolButton.vue'
import FloatPromotionButton from '@/components/FloatPromotionButton.vue'
import basePromotionMixin from '@/mixins/basePromotionMixin'

Vue.config.productionTip = false

// 注册全局组件
Vue.component('cu-custom', cuCustom)

// 注册全局混入
Vue.mixin(basePromotionMixin)

// 注册全局推广组件
Vue.component('promotion-tool-button', PromotionToolButton)
Vue.component('float-promotion-button', FloatPromotionButton)

// 移除直接导入商家组件UI的代码
// Vue.use(MerchantUI)

App.mpType = 'app'

// 全局错误处理
Vue.config.errorHandler = function(err, vm, info) {
  console.error('全局错误:', err)
  
  // 可以上报到服务端或者做其他处理
  if (process.env.NODE_ENV !== 'production') {
    uni.showModal({
      title: '应用错误',
      content: err.message || '未知错误',
      showCancel: false
    })
  } else {
    // 生产环境下，可以给用户一个友好的提示
    uni.showToast({
      title: '应用发生异常，请稍后再试',
      icon: 'none'
    })
  }
}

// 拦截页面跳转，处理权限问题
const needAuthPages = [
  '/pages/my/publish',
  '/pages/my/wallet',
  '/pages/my/profile',
  '/pages/my/messages',
  '/pages/my/merchant'
]

uni.addInterceptor('navigateTo', {
  invoke(params) {
    // 判断是否需要登录权限
    if (needAuthPages.some(page => params.url.startsWith(page))) {
      const userInfo = getLocalUserInfo()
      if (!userInfo || !userInfo.userId) {
        // 未登录，跳转到登录页或显示登录弹窗
        uni.showModal({
          title: '提示',
          content: '请先登录后再操作',
          success: (res) => {
            if (res.confirm) {
              // 跳转到个人中心页面
              uni.switchTab({
                url: '/pages/my/my'
              })
            }
          }
        })
        return false // 拦截原有的跳转
      }
    }
    return params
  },
  fail(err) {
    console.log('页面跳转失败:', err)
    return false
  }
})

// 修复图片加载错误问题
Vue.prototype.imageError = function(e) {
  const currentTarget = e.currentTarget
  const index = currentTarget.dataset.index
  const type = currentTarget.dataset.type || 'default'
  
  // 设置默认图片
  const defaultImages = {
    avatar: '/static/images/default-avatar.png',
    product: '/static/images/default-product.png',
    default: '/static/images/default-image.png'
  }
  
  // 更新图片路径
  if (typeof index !== 'undefined') {
    // 列表中的图片
    const key = `${type}List[${index}].image`
    this.$set(this, key, defaultImages[type] || defaultImages.default)
  } else {
    // 单个图片
    const key = `${type}Image`
    this.$set(this, key, defaultImages[type] || defaultImages.default)
  }
}

// 注册商家插件
Vue.prototype.$merchant = merchantPlugin;

// 根据页面类型动态注入对应的推广能力
Vue.prototype.$injectPromotionMixin = function(pageType) {
  // 动态导入对应的推广混入
  let mixinModule = null;
  
  switch(pageType) {
    case 'product':
      import('@/mixins/productPromotionMixin').then(module => {
        mixinModule = module.default;
      });
      break;
    // 可以添加更多类型
    default:
      // 默认使用基础混入
      break;
  }
  
  return mixinModule || {};
};

// 添加全局推广服务
import platformPromotionService from '@/services/platformPromotionService';
Vue.prototype.$promotionService = platformPromotionService;

// 初始化应用
const app = new Vue({
  ...App,
  // 全局共享数据
  globalData: {
    userInfo: getLocalUserInfo(),
    isCheckingSession: false,
    version: '1.0.0'
  }
})
app.$mount()
// #endif

export function createApp() {
  const app = createSSRApp(App)
  
  // 注册红包组件
  app.use(RedPacketComponents)
  
  // 注册商家管理插件
  app.config.globalProperties.$merchant = merchantPlugin;
  
  // 注册全局组件
  app.component('cu-custom', cuCustom)
  
  // 移除直接导入商家后台组件库的代码
  // app.use(MerchantAdminComponents)
  
  // 全局错误处理
  app.config.errorHandler = (err, instance, info) => {
    console.error('[Vue错误]', err, info)
    reportError({
      error: err,
      info: info,
      component: instance ? instance.$options.name || 'anonymous' : null,
      timestamp: Date.now()
    })
  }
  
  return {
    app
  }
}