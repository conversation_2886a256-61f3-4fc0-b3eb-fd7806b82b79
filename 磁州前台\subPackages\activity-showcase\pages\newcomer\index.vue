<template>
  <view class="newcomer-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">新人专区</view>
        <view class="navbar-right">
          <view class="help-btn" @click="showHelp">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 新人欢迎卡片 -->
      <view class="welcome-card">
        <view class="welcome-header">
          <view class="welcome-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 12l2 2 4-4M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"></path>
            </svg>
          </view>
          <view class="welcome-info">
            <text class="welcome-title">欢迎新用户！</text>
            <text class="welcome-desc">专属福利等你来领取</text>
          </view>
        </view>
        
        <view class="welcome-benefits">
          <view class="benefit-item">
            <text class="benefit-title">新人大礼包</text>
            <text class="benefit-desc">价值100元优惠券</text>
          </view>
          <view class="benefit-item">
            <text class="benefit-title">首单立减</text>
            <text class="benefit-desc">满50减20元</text>
          </view>
          <view class="benefit-item">
            <text class="benefit-title">免费试用</text>
            <text class="benefit-desc">精选商品0元试用</text>
          </view>
        </view>

        <view class="welcome-progress">
          <text class="progress-title">新人任务进度</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: `${taskProgress}%` }"></view>
          </view>
          <text class="progress-text">{{ completedTasks }}/{{ totalTasks }} 已完成</text>
        </view>
      </view>

      <!-- 新人任务 -->
      <view class="newcomer-tasks">
        <view class="section-header">
          <text class="section-title">新人任务</text>
          <text class="section-desc">完成任务获得丰厚奖励</text>
        </view>

        <view class="tasks-list">
          <view 
            class="task-item" 
            v-for="(task, index) in tasks" 
            :key="task.id"
            :class="{ completed: task.completed, current: task.current }"
            @click="handleTask(task)"
          >
            <view class="task-icon">
              <svg v-if="task.completed" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
              <svg v-else-if="task.current" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
              </svg>
            </view>
            <view class="task-content">
              <text class="task-title">{{ task.title }}</text>
              <text class="task-desc">{{ task.description }}</text>
              <view class="task-progress" v-if="task.progress !== undefined">
                <text class="progress-text">{{ task.progress }}/{{ task.target }}</text>
              </view>
            </view>
            <view class="task-reward">
              <text class="reward-text">{{ task.reward }}</text>
            </view>
            <view class="task-action">
              <view class="action-btn" v-if="!task.completed" :class="{ current: task.current }">
                <text class="btn-text">{{ task.current ? '进行中' : '去完成' }}</text>
              </view>
              <view class="completed-badge" v-else>
                <text class="badge-text">已完成</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 新人专享商品 -->
      <view class="newcomer-products">
        <view class="section-header">
          <text class="section-title">新人专享</text>
          <text class="section-desc">限时特价，仅限新用户</text>
        </view>

        <view class="products-grid">
          <view 
            class="product-item" 
            v-for="(product, index) in newcomerProducts" 
            :key="product.id"
            @click="navigateToProduct(product)"
          >
            <view class="product-image-container">
              <image class="product-image" :src="product.image" mode="aspectFill" :lazy-load="true"></image>
              <view class="product-badge">
                <text class="badge-text">新人专享</text>
              </view>
              <view class="product-discount">
                <text class="discount-text">{{ product.discount }}折</text>
              </view>
            </view>
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <text class="product-desc">{{ product.description }}</text>
              <view class="product-price">
                <text class="current-price">¥{{ product.price }}</text>
                <text class="original-price">¥{{ product.originalPrice }}</text>
              </view>
              <view class="product-stats">
                <text class="limit-text">限购{{ product.limit }}件</text>
                <text class="stock-text">剩余{{ product.stock }}件</text>
              </view>
            </view>
            <view class="buy-btn">
              <text class="buy-text">立即购买</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 新人优惠券 -->
      <view class="newcomer-coupons">
        <view class="section-header">
          <text class="section-title">新人优惠券</text>
          <text class="section-desc">专属优惠，限时领取</text>
        </view>

        <view class="coupons-list">
          <view 
            class="coupon-item" 
            v-for="(coupon, index) in coupons" 
            :key="coupon.id"
            :class="{ received: coupon.received }"
            @click="receiveCoupon(coupon)"
          >
            <view class="coupon-left">
              <text class="coupon-amount">{{ coupon.amount }}</text>
              <text class="coupon-unit">元</text>
            </view>
            <view class="coupon-content">
              <text class="coupon-title">{{ coupon.title }}</text>
              <text class="coupon-desc">{{ coupon.description }}</text>
              <text class="coupon-expire">有效期：{{ coupon.expireDate }}</text>
            </view>
            <view class="coupon-action">
              <view class="receive-btn" v-if="!coupon.received">
                <text class="btn-text">立即领取</text>
              </view>
              <view class="received-badge" v-else>
                <text class="badge-text">已领取</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 新人指南 -->
      <view class="newcomer-guide">
        <view class="section-header">
          <text class="section-title">新人指南</text>
          <text class="section-desc">快速了解平台功能</text>
        </view>

        <view class="guide-list">
          <view 
            class="guide-item" 
            v-for="(guide, index) in guides" 
            :key="index"
            @click="navigateToGuide(guide)"
          >
            <view class="guide-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path v-if="guide.type === 'shopping'" d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z"></path>
                <path v-else-if="guide.type === 'payment'" d="M21 4H3C2.45 4 2 4.45 2 5V19C2 19.55 2.45 20 3 20H21C21.55 20 22 19.55 22 19V5C22 4.45 21.55 4 21 4Z"></path>
                <path v-else-if="guide.type === 'service'" d="M22 16.92V18C22 18.55 21.55 19 21 19C9.4 19 0 9.6 0 -2C0 -2.55 0.45 -3 1 -3H2.08C2.6 -3 3.02 -2.64 3.07 -2.12L3.4 0.88C3.44 1.35 3.23 1.8 2.84 2.07L1.37 3.17C2.93 6.34 5.66 9.07 8.83 10.63L9.93 9.16C10.2 8.77 10.65 8.56 11.12 8.6L14.12 8.93C14.64 8.98 15 9.4 15 9.92V11C15 11.55 14.55 12 14 12C12.4 12 11 10.6 11 9"></path>
                <circle v-else cx="12" cy="12" r="10"></circle>
              </svg>
            </view>
            <view class="guide-content">
              <text class="guide-title">{{ guide.title }}</text>
              <text class="guide-desc">{{ guide.description }}</text>
            </view>
            <view class="guide-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const completedTasks = ref(2)
const totalTasks = ref(5)

// 计算任务进度
const taskProgress = computed(() => {
  return (completedTasks.value / totalTasks.value) * 100
})

// 新人任务
const tasks = ref([
  {
    id: 1,
    title: '完善个人信息',
    description: '填写头像、昵称等基本信息',
    reward: '+50积分',
    completed: true,
    current: false
  },
  {
    id: 2,
    title: '首次浏览商品',
    description: '浏览任意商品详情页',
    reward: '+20积分',
    completed: true,
    current: false
  },
  {
    id: 3,
    title: '添加商品到购物车',
    description: '将喜欢的商品加入购物车',
    reward: '+30积分',
    completed: false,
    current: true,
    progress: 0,
    target: 1
  },
  {
    id: 4,
    title: '完成首单购买',
    description: '成功下单并完成支付',
    reward: '+100积分+优惠券',
    completed: false,
    current: false
  },
  {
    id: 5,
    title: '邀请好友注册',
    description: '邀请1位好友成功注册',
    reward: '+200积分',
    completed: false,
    current: false,
    progress: 0,
    target: 1
  }
])

// 新人专享商品
const newcomerProducts = ref([
  {
    id: 1,
    name: '新人专享礼盒',
    description: '精选好物，超值优惠',
    image: '/static/images/newcomer/product1.jpg',
    price: 19.9,
    originalPrice: 59.9,
    discount: 3.3,
    limit: 1,
    stock: 98
  },
  {
    id: 2,
    name: '限时特价商品',
    description: '仅限新用户购买',
    image: '/static/images/newcomer/product2.jpg',
    price: 29.9,
    originalPrice: 79.9,
    discount: 3.7,
    limit: 2,
    stock: 156
  }
])

// 新人优惠券
const coupons = ref([
  {
    id: 1,
    amount: 20,
    title: '新人专享券',
    description: '满50可用',
    expireDate: '2024-02-20',
    received: false
  },
  {
    id: 2,
    amount: 50,
    title: '首单立减券',
    description: '满100可用',
    expireDate: '2024-02-25',
    received: false
  },
  {
    id: 3,
    amount: 10,
    title: '通用优惠券',
    description: '无门槛使用',
    expireDate: '2024-02-15',
    received: true
  }
])

// 新人指南
const guides = ref([
  {
    title: '如何购买商品',
    description: '详细的购物流程指导',
    type: 'shopping',
    url: '/pages/help/shopping'
  },
  {
    title: '支付方式说明',
    description: '支持的支付方式介绍',
    type: 'payment',
    url: '/pages/help/payment'
  },
  {
    title: '售后服务政策',
    description: '退换货及售后服务说明',
    type: 'service',
    url: '/pages/help/service'
  },
  {
    title: '常见问题解答',
    description: '新用户常见问题及解答',
    type: 'faq',
    url: '/pages/help/faq'
  }
])

// 页面加载
onMounted(() => {
  console.log('新人专区页面加载')
  loadNewcomerData()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function showHelp() {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/help/index'
  })
}

function handleTask(task) {
  if (task.completed) return
  
  // 根据任务类型执行相应操作
  switch(task.id) {
    case 1:
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/user-profile/index'
      })
      break
    case 2:
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/products/detail/index?id=1'
      })
      break
    case 3:
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/categories/index'
      })
      break
    case 4:
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/cart/index'
      })
      break
    case 5:
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/invite/index'
      })
      break
  }
}

function navigateToProduct(product) {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/products/detail/index?id=${product.id}&type=newcomer`
  })
}

function receiveCoupon(coupon) {
  if (coupon.received) return
  
  coupon.received = true
  uni.showToast({
    title: '领取成功',
    icon: 'success'
  })
}

function navigateToGuide(guide) {
  uni.navigateTo({
    url: guide.url
  })
}

function loadNewcomerData() {
  // 模拟加载新人数据
  setTimeout(() => {
    console.log('新人数据加载完成')
  }, 500)
}
</script>

<style scoped>
/* 新人专区样式开始 */
.newcomer-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFC107 0%, #FF9800 100%);
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 193, 7, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn, .help-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.1);
}

.back-btn svg, .help-btn svg {
  color: #333;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
  height: 100vh;
}

/* 欢迎卡片样式 */
.welcome-card {
  margin: 20px 16px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.welcome-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.welcome-icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4CAF50, #45A049);
  border-radius: 32px;
}

.welcome-icon svg {
  color: white;
}

.welcome-info {
  flex: 1;
}

.welcome-title {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.welcome-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.welcome-benefits {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.benefit-item {
  text-align: center;
  padding: 16px 8px;
  background: #f8f9fa;
  border-radius: 12px;
}

.benefit-title {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.benefit-desc {
  display: block;
  font-size: 12px;
  color: #666;
}

.welcome-progress {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.progress-title {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.progress-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFC107, #FF9800);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #666;
}

/* 新人任务样式 */
.newcomer-tasks {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.section-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.task-item.completed {
  background: #E8F5E8;
  border-color: #4CAF50;
}

.task-item.current {
  background: #FFF3E0;
  border-color: #FF9800;
}

.task-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  background: #e0e0e0;
}

.task-item.completed .task-icon {
  background: #4CAF50;
}

.task-item.current .task-icon {
  background: #FF9800;
}

.task-icon svg {
  color: #666;
}

.task-item.completed .task-icon svg,
.task-item.current .task-icon svg {
  color: white;
}

.task-content {
  flex: 1;
}

.task-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.task-desc {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.task-progress .progress-text {
  font-size: 12px;
  color: #FF9800;
  font-weight: 500;
}

.task-reward {
  margin-right: 12px;
}

.reward-text {
  font-size: 14px;
  color: #FFC107;
  font-weight: 600;
}

.task-action {
  min-width: 80px;
}

.action-btn {
  padding: 8px 16px;
  background: #e0e0e0;
  border-radius: 16px;
  text-align: center;
}

.action-btn.current {
  background: #FF9800;
}

.btn-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.action-btn.current .btn-text {
  color: white;
}

.completed-badge {
  padding: 6px 12px;
  background: #4CAF50;
  border-radius: 12px;
}

.completed-badge .badge-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

/* 新人专享商品样式 */
.newcomer-products {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.product-item {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 120px;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-badge {
  position: absolute;
  top: 6px;
  left: 6px;
  padding: 2px 8px;
  background: #FFC107;
  border-radius: 8px;
}

.product-badge .badge-text {
  font-size: 10px;
  color: #333;
  font-weight: 500;
}

.product-discount {
  position: absolute;
  top: 6px;
  right: 6px;
  padding: 2px 8px;
  background: #E91E63;
  border-radius: 8px;
}

.discount-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

.product-info {
  padding: 12px;
}

.product-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #FFC107;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.limit-text, .stock-text {
  font-size: 12px;
  color: #666;
}

.buy-btn {
  width: 100%;
  padding: 8px;
  background: linear-gradient(135deg, #FFC107, #FF9800);
  border-radius: 8px;
  text-align: center;
}

.buy-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 新人优惠券样式 */
.newcomer-coupons {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.coupons-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.coupon-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #FFC107, #FF9800);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.coupon-item.received {
  background: #f5f5f5;
}

.coupon-item::before {
  content: '';
  position: absolute;
  right: 80px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(255, 255, 255, 0.3);
  border-left: 2px dashed rgba(255, 255, 255, 0.5);
}

.coupon-left {
  display: flex;
  align-items: baseline;
  gap: 2px;
  margin-right: 16px;
  min-width: 60px;
}

.coupon-amount {
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.coupon-item.received .coupon-amount {
  color: #999;
}

.coupon-unit {
  font-size: 14px;
  color: #333;
}

.coupon-item.received .coupon-unit {
  color: #999;
}

.coupon-content {
  flex: 1;
}

.coupon-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.coupon-item.received .coupon-title {
  color: #999;
}

.coupon-desc {
  display: block;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  margin-bottom: 4px;
}

.coupon-item.received .coupon-desc {
  color: #999;
}

.coupon-expire {
  display: block;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.5);
}

.coupon-item.received .coupon-expire {
  color: #999;
}

.coupon-action {
  min-width: 80px;
  text-align: center;
}

.receive-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
}

.receive-btn .btn-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.received-badge {
  padding: 6px 12px;
  background: #e0e0e0;
  border-radius: 12px;
}

.received-badge .badge-text {
  font-size: 12px;
  color: #999;
  font-weight: 500;
}

/* 新人指南样式 */
.newcomer-guide {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.guide-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.guide-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.guide-item:active {
  background: #e9ecef;
}

.guide-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFC107;
  border-radius: 20px;
}

.guide-icon svg {
  color: #333;
}

.guide-content {
  flex: 1;
}

.guide-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.guide-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.guide-arrow svg {
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 新人专区样式结束 */
</style>
