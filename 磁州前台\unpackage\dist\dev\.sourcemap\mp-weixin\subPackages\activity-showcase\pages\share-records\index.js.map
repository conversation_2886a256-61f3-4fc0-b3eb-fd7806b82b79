{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/share-records/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcc2hhcmUtcmVjb3Jkc1xpbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"share-records-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\" class=\"back-icon\"></image>\n        </view>\n        <view class=\"navbar-title\">分享记录</view>\n        <view class=\"navbar-right\">\n          <view class=\"filter-btn\" @click=\"showFilter\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <path d=\"M22 3H2l8 9.46V19l4 2v-8.54L22 3z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 分享类型标签栏 -->\n    <view class=\"share-tabs\">\n      <view \n        v-for=\"(tab, index) in shareTabs\" \n        :key=\"index\"\n        class=\"tab-item\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.name }}</text>\n        <view class=\"tab-indicator\" v-if=\"currentTab === index\" :style=\"{\n          background: 'linear-gradient(90deg, #5856D6 0%, #A09CFF 100%)'\n        }\"></view>\n      </view>\n    </view>\n\n    <!-- 分享统计卡片 -->\n    <view class=\"stats-card\">\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ shareStats.totalShares }}</text>\n        <text class=\"stat-label\">总分享</text>\n      </view>\n      <view class=\"stat-divider\"></view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ shareStats.totalViews }}</text>\n        <text class=\"stat-label\">总浏览</text>\n      </view>\n      <view class=\"stat-divider\"></view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ shareStats.totalRegistrations }}</text>\n        <text class=\"stat-label\">报名转化</text>\n      </view>\n      <view class=\"stat-divider\"></view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ shareStats.totalCommission }}</text>\n        <text class=\"stat-label\">累计佣金</text>\n      </view>\n    </view>\n\n    <!-- 分享记录列表区域 -->\n    <swiper class=\"records-swiper\" :current=\"currentTab\" @change=\"onSwiperChange\">\n      <swiper-item v-for=\"(tab, tabIndex) in shareTabs\" :key=\"tabIndex\">\n        <scroll-view \n          class=\"tab-content\" \n          scroll-y \n          refresher-enabled\n          :refresher-triggered=\"isRefreshing\"\n          @refresherrefresh=\"onRefresh\"\n          @scrolltolower=\"loadMore\"\n        >\n          <view class=\"records-list\">\n            <view \n              v-for=\"record in getRecordsByType(tab.type)\" \n              :key=\"record.id\"\n              class=\"record-card\"\n              @click=\"viewRecordDetail(record)\"\n            >\n              <!-- 分享内容预览 -->\n              <view class=\"record-preview\">\n                <image :src=\"record.image\" class=\"record-image\" mode=\"aspectFill\"></image>\n                <view class=\"record-type-tag\" :style=\"{\n                  background: getTypeTagBackground(record.contentType)\n                }\">\n                  {{ getTypeTagText(record.contentType) }}\n                </view>\n              </view>\n              \n              <!-- 分享记录信息 -->\n              <view class=\"record-info\">\n                <text class=\"record-title\">{{ record.title }}</text>\n                \n                <view class=\"record-meta\">\n                  <view class=\"meta-item\">\n                    <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                      <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></rect>\n                      <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                      <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                      <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                    </svg>\n                    <text class=\"meta-text\">{{ record.date }}</text>\n                  </view>\n                  \n                  <view class=\"meta-item\">\n                    <svg class=\"meta-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                      <path d=\"M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                      <circle cx=\"9\" cy=\"7\" r=\"4\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                      <path d=\"M23 21v-2a4 4 0 00-3-3.87m-4-12a4 4 0 010 7.75\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                    </svg>\n                    <text class=\"meta-text\">{{ record.platform }}</text>\n                  </view>\n                </view>\n                \n                <view class=\"record-stats\">\n                  <view class=\"stat-row\">\n                    <view class=\"mini-stat\">\n                      <svg class=\"stat-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                        <path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                      </svg>\n                      <text class=\"stat-text\">{{ record.views }} 浏览</text>\n                    </view>\n                    \n                    <view class=\"mini-stat\">\n                      <svg class=\"stat-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                        <path d=\"M20.84 4.61a5.5 5.5 0 00-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 00-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 000-7.78z\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                      </svg>\n                      <text class=\"stat-text\">{{ record.likes }} 点赞</text>\n                    </view>\n                    \n                    <view class=\"mini-stat\">\n                      <svg class=\"stat-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                        <path d=\"M21 11.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                      </svg>\n                      <text class=\"stat-text\">{{ record.comments }} 评论</text>\n                    </view>\n                  </view>\n                  \n                  <view class=\"stat-row\">\n                    <view class=\"mini-stat highlight\">\n                      <svg class=\"stat-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                        <path d=\"M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2\" stroke=\"#5856D6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                        <circle cx=\"8.5\" cy=\"7\" r=\"4\" stroke=\"#5856D6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                        <line x1=\"20\" y1=\"8\" x2=\"20\" y2=\"14\" stroke=\"#5856D6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                        <line x1=\"23\" y1=\"11\" x2=\"17\" y2=\"11\" stroke=\"#5856D6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                      </svg>\n                      <text class=\"stat-text\">{{ record.registrations }} 报名</text>\n                    </view>\n                    \n                    <view class=\"mini-stat highlight\">\n                      <svg class=\"stat-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                        <line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"23\" stroke=\"#5856D6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                        <path d=\"M17 5H9.5a3.5 3.5 0 000 7h5a3.5 3.5 0 010 7H6\" stroke=\"#5856D6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                      </svg>\n                      <text class=\"stat-text\">¥{{ record.commission.toFixed(2) }} 佣金</text>\n                    </view>\n                  </view>\n                </view>\n                \n                <view class=\"record-actions\">\n                  <view \n                    class=\"action-btn\"\n                    @click.stop=\"shareAgain(record)\"\n                  >\n                    <svg class=\"action-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                      <circle cx=\"18\" cy=\"5\" r=\"3\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                      <circle cx=\"6\" cy=\"12\" r=\"3\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                      <circle cx=\"18\" cy=\"19\" r=\"3\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n                      <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                      <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\" stroke=\"#666666\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                    </svg>\n                    <text>再次分享</text>\n                  </view>\n                  \n                  <view \n                    class=\"action-btn primary\"\n                    :style=\"{\n                      background: 'linear-gradient(135deg, #5856D6 0%, #A09CFF 100%)',\n                      color: '#FFFFFF'\n                    }\"\n                    @click.stop=\"viewAnalytics(record)\"\n                  >\n                    <svg class=\"action-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                      <line x1=\"18\" y1=\"20\" x2=\"18\" y2=\"10\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                      <line x1=\"12\" y1=\"20\" x2=\"12\" y2=\"4\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                      <line x1=\"6\" y1=\"20\" x2=\"6\" y2=\"14\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n                    </svg>\n                    <text>查看数据</text>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 空状态 -->\n          <view class=\"empty-state\" v-if=\"getRecordsByType(tab.type).length === 0\">\n            <image class=\"empty-image\" :src=\"tab.emptyImage || '/static/images/empty-shares.png'\"></image>\n            <text class=\"empty-text\">{{ tab.emptyText || '暂无分享记录' }}</text>\n            <view class=\"action-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/list/index')\" :style=\"{\n              background: 'linear-gradient(135deg, #5856D6 0%, #A09CFF 100%)',\n              borderRadius: '35px',\n              boxShadow: '0 5px 15px rgba(88,86,214,0.3)'\n            }\">\n              <text>去分享活动</text>\n            </view>\n          </view>\n        </scroll-view>\n      </swiper-item>\n    </swiper>\n\n    <!-- 筛选弹窗 -->\n    <uni-popup ref=\"filterPopup\" type=\"bottom\">\n      <view class=\"filter-popup\">\n        <view class=\"filter-header\">\n          <text class=\"filter-title\">筛选</text>\n          <view class=\"filter-close\" @click=\"closeFilter\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n              <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"filter-content\">\n          <!-- 时间筛选 -->\n          <view class=\"filter-section\">\n            <text class=\"section-title\">时间范围</text>\n            <view class=\"filter-options\">\n              <view \n                v-for=\"(option, index) in timeOptions\" \n                :key=\"index\"\n                class=\"filter-option\"\n                :class=\"{ active: selectedTimeOption === index }\"\n                @click=\"selectTimeOption(index)\"\n              >\n                {{ option.label }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 分享平台筛选 -->\n          <view class=\"filter-section\">\n            <text class=\"section-title\">分享平台</text>\n            <view class=\"filter-options\">\n              <view \n                v-for=\"(option, index) in platformOptions\" \n                :key=\"index\"\n                class=\"filter-option\"\n                :class=\"{ active: selectedPlatformOptions.includes(index) }\"\n                @click=\"togglePlatformOption(index)\"\n              >\n                {{ option.label }}\n              </view>\n            </view>\n          </view>\n          \n          <!-- 内容类型筛选 -->\n          <view class=\"filter-section\">\n            <text class=\"section-title\">内容类型</text>\n            <view class=\"filter-options\">\n              <view \n                v-for=\"(option, index) in contentTypeOptions\" \n                :key=\"index\"\n                class=\"filter-option\"\n                :class=\"{ active: selectedContentTypeOptions.includes(index) }\"\n                @click=\"toggleContentTypeOption(index)\"\n              >\n                {{ option.label }}\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"filter-footer\">\n          <view class=\"filter-reset\" @click=\"resetFilter\">\n            <text>重置</text>\n          </view>\n          <view class=\"filter-apply\" @click=\"applyFilter\">\n            <text>确定</text>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 页面状态\nconst currentTab = ref(0);\nconst isRefreshing = ref(false);\nconst recordsList = ref([]);\nconst filterPopup = ref(null);\n\n// 分享统计数据\nconst shareStats = ref({\n  totalShares: 56,\n  totalViews: 2348,\n  totalRegistrations: 32,\n  totalCommission: '¥328.50'\n});\n\n// 筛选选项\nconst selectedTimeOption = ref(0);\nconst selectedPlatformOptions = ref([0]);\nconst selectedContentTypeOptions = ref([0]);\n\n// 筛选选项数据\nconst timeOptions = [\n  { label: '全部时间', value: 'all' },\n  { label: '最近一周', value: 'week' },\n  { label: '最近一月', value: 'month' },\n  { label: '最近三月', value: 'three_months' },\n  { label: '自定义', value: 'custom' }\n];\n\nconst platformOptions = [\n  { label: '全部平台', value: 'all' },\n  { label: '微信', value: 'wechat' },\n  { label: '朋友圈', value: 'moments' },\n  { label: '微博', value: 'weibo' },\n  { label: '抖音', value: 'douyin' },\n  { label: '其他', value: 'other' }\n];\n\nconst contentTypeOptions = [\n  { label: '全部类型', value: 'all' },\n  { label: '活动', value: 'activity' },\n  { label: '产品', value: 'product' },\n  { label: '优惠券', value: 'coupon' },\n  { label: '海报', value: 'poster' }\n];\n\n// 分享标签页\nconst shareTabs = [\n  { name: '全部', type: 'all', emptyText: '暂无分享记录', emptyImage: '/static/images/empty-shares.png' },\n  { name: '活动', type: 'activity', emptyText: '暂无活动分享', emptyImage: '/static/images/empty-activity-shares.png' },\n  { name: '产品', type: 'product', emptyText: '暂无产品分享', emptyImage: '/static/images/empty-product-shares.png' },\n  { name: '优惠券', type: 'coupon', emptyText: '暂无优惠券分享', emptyImage: '/static/images/empty-coupon-shares.png' }\n];\n\n// 模拟数据\nconst mockRecords = [\n  {\n    id: '1001',\n    title: '磁州文化节',\n    contentType: 'activity',\n    platform: '微信群',\n    date: '2024-05-15',\n    image: '/static/demo/activity1.jpg',\n    views: 245,\n    likes: 32,\n    comments: 18,\n    registrations: 12,\n    commission: 120.00\n  },\n  {\n    id: '1002',\n    title: '亲子户外拓展活动',\n    contentType: 'activity',\n    platform: '朋友圈',\n    date: '2024-05-12',\n    image: '/static/demo/activity2.jpg',\n    views: 189,\n    likes: 24,\n    comments: 9,\n    registrations: 8,\n    commission: 80.00\n  },\n  {\n    id: '1003',\n    title: '磁州特产礼盒',\n    contentType: 'product',\n    platform: '微信群',\n    date: '2024-05-10',\n    image: '/static/demo/product1.jpg',\n    views: 156,\n    likes: 18,\n    comments: 7,\n    registrations: 5,\n    commission: 50.00\n  },\n  {\n    id: '1004',\n    title: '新人专享优惠券',\n    contentType: 'coupon',\n    platform: '抖音',\n    date: '2024-05-08',\n    image: '/static/demo/coupon1.jpg',\n    views: 324,\n    likes: 45,\n    comments: 12,\n    registrations: 7,\n    commission: 35.00\n  },\n  {\n    id: '1005',\n    title: '磁州文化体验套餐',\n    contentType: 'product',\n    platform: '微博',\n    date: '2024-05-05',\n    image: '/static/demo/product2.jpg',\n    views: 132,\n    likes: 16,\n    comments: 5,\n    registrations: 0,\n    commission: 0.00\n  }\n];\n\n// 生命周期\nonMounted(() => {\n  loadRecords();\n});\n\n// 方法\nconst loadRecords = () => {\n  // 模拟加载数据\n  recordsList.value = mockRecords;\n};\n\nconst getRecordsByType = (type) => {\n  if (type === 'all') {\n    return recordsList.value;\n  }\n  return recordsList.value.filter(record => record.contentType === type);\n};\n\nconst switchTab = (index) => {\n  currentTab.value = index;\n};\n\nconst onSwiperChange = (e) => {\n  currentTab.value = e.detail.current;\n};\n\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  setTimeout(() => {\n    loadRecords();\n    isRefreshing.value = false;\n  }, 1000);\n};\n\nconst loadMore = () => {\n  // 模拟加载更多\n  console.log('加载更多记录');\n};\n\nconst getTypeTagText = (type) => {\n  const typeMap = {\n    'activity': '活动',\n    'product': '产品',\n    'coupon': '优惠券',\n    'poster': '海报'\n  };\n  return typeMap[type] || '未知类型';\n};\n\nconst getTypeTagBackground = (type) => {\n  const bgMap = {\n    'activity': 'rgba(255, 59, 105, 0.8)',\n    'product': 'rgba(90, 200, 250, 0.8)',\n    'coupon': 'rgba(255, 149, 0, 0.8)',\n    'poster': 'rgba(52, 199, 89, 0.8)'\n  };\n  return bgMap[type] || 'rgba(142, 142, 147, 0.8)';\n};\n\nconst viewRecordDetail = (record) => {\n  switch (record.contentType) {\n    case 'activity':\n      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);\n      break;\n    case 'product':\n      navigateTo(`/subPackages/activity-showcase/pages/products/detail?id=${record.id}`);\n      break;\n    case 'coupon':\n      navigateTo(`/subPackages/activity-showcase/pages/coupon/detail?id=${record.id}`);\n      break;\n    default:\n      navigateTo(`/subPackages/activity-showcase/pages/distribution/poster/index?id=${record.id}`);\n  }\n};\n\nconst shareAgain = (record) => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\nconst viewAnalytics = (record) => {\n  navigateTo(`/subPackages/activity-showcase/pages/share-analytics/index?id=${record.id}`);\n};\n\nconst showFilter = () => {\n  filterPopup.value.open();\n};\n\nconst closeFilter = () => {\n  filterPopup.value.close();\n};\n\nconst selectTimeOption = (index) => {\n  selectedTimeOption.value = index;\n};\n\nconst togglePlatformOption = (index) => {\n  const position = selectedPlatformOptions.value.indexOf(index);\n  if (index === 0) {\n    // 如果选择\"全部\"，清除其他选项\n    selectedPlatformOptions.value = [0];\n  } else {\n    // 如果选择其他选项，移除\"全部\"选项\n    if (selectedPlatformOptions.value.includes(0)) {\n      selectedPlatformOptions.value = selectedPlatformOptions.value.filter(item => item !== 0);\n    }\n    \n    // 切换选中状态\n    if (position !== -1) {\n      selectedPlatformOptions.value.splice(position, 1);\n      // 如果没有选项，默认选中\"全部\"\n      if (selectedPlatformOptions.value.length === 0) {\n        selectedPlatformOptions.value = [0];\n      }\n    } else {\n      selectedPlatformOptions.value.push(index);\n    }\n  }\n};\n\nconst toggleContentTypeOption = (index) => {\n  const position = selectedContentTypeOptions.value.indexOf(index);\n  if (index === 0) {\n    // 如果选择\"全部\"，清除其他选项\n    selectedContentTypeOptions.value = [0];\n  } else {\n    // 如果选择其他选项，移除\"全部\"选项\n    if (selectedContentTypeOptions.value.includes(0)) {\n      selectedContentTypeOptions.value = selectedContentTypeOptions.value.filter(item => item !== 0);\n    }\n    \n    // 切换选中状态\n    if (position !== -1) {\n      selectedContentTypeOptions.value.splice(position, 1);\n      // 如果没有选项，默认选中\"全部\"\n      if (selectedContentTypeOptions.value.length === 0) {\n        selectedContentTypeOptions.value = [0];\n      }\n    } else {\n      selectedContentTypeOptions.value.push(index);\n    }\n  }\n};\n\nconst resetFilter = () => {\n  selectedTimeOption.value = 0;\n  selectedPlatformOptions.value = [0];\n  selectedContentTypeOptions.value = [0];\n};\n\nconst applyFilter = () => {\n  // 应用筛选\n  console.log('应用筛选', {\n    time: timeOptions[selectedTimeOption.value].value,\n    platforms: selectedPlatformOptions.value.map(index => platformOptions[index].value),\n    contentTypes: selectedContentTypeOptions.value.map(index => contentTypeOptions[index].value)\n  });\n  \n  // 模拟筛选结果\n  uni.showToast({\n    title: '筛选已应用',\n    icon: 'success'\n  });\n  \n  closeFilter();\n};\n\nconst goBack = () => {\n  uni.navigateBack();\n};\n\nconst navigateTo = (url) => {\n  uni.navigateTo({ url });\n};\n</script>\n\n<style scoped>\n.share-records-container {\n  min-height: 100vh;\n  background-color: #F5F5F5;\n  padding-bottom: 30rpx;\n}\n\n/* 导航栏样式 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 100;\n}\n\n.navbar-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #5856D6 0%, #A09CFF 100%);\n}\n\n.navbar-content {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 113rpx; /* 原来是110rpx，增加3rpx */\n  padding: var(--status-bar-height) 30rpx 0;\n}\n\n.back-btn, .filter-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 36rpx;\n  height: 36rpx;\n}\n\n.navbar-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #FFFFFF;\n}\n\n.navbar-right {\n  display: flex;\n  align-items: center;\n}\n\n/* 标签栏样式 */\n.share-tabs {\n  display: flex;\n  background: #FFFFFF;\n  padding: 0 20rpx;\n  margin-top: calc(var(--status-bar-height) + 113rpx); /* 原来是110rpx，增加3rpx */\n  border-bottom: 1rpx solid #EEEEEE;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20rpx 0;\n  position: relative;\n}\n\n.tab-text {\n  font-size: 28rpx;\n  color: #333333;\n  padding: 0 10rpx;\n}\n\n.tab-item.active .tab-text {\n  color: #5856D6;\n  font-weight: 500;\n}\n\n.tab-indicator {\n  position: absolute;\n  bottom: 0;\n  width: 40rpx;\n  height: 6rpx;\n  border-radius: 3rpx;\n}\n\n/* 统计卡片样式 */\n.stats-card {\n  display: flex;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  margin: 20rpx;\n  padding: 30rpx 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.stat-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-value {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 10rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.stat-divider {\n  width: 1rpx;\n  height: 60rpx;\n  background: #EEEEEE;\n  margin: 0 10rpx;\n}\n\n/* 分享记录列表样式 */\n.records-swiper {\n  height: calc(100vh - var(--status-bar-height) - 113rpx - 70rpx - 130rpx); /* 原来是110rpx，增加3rpx */\n}\n\n.tab-content {\n  height: 100%;\n  padding: 0 20rpx 20rpx;\n}\n\n.records-list {\n  padding-bottom: 30rpx;\n}\n\n.record-card {\n  display: flex;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.record-preview {\n  width: 200rpx;\n  height: 200rpx;\n  position: relative;\n}\n\n.record-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.record-type-tag {\n  position: absolute;\n  top: 10rpx;\n  right: 10rpx;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n  font-size: 20rpx;\n  color: #FFFFFF;\n}\n\n.record-info {\n  flex: 1;\n  padding: 20rpx;\n  display: flex;\n  flex-direction: column;\n}\n\n.record-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 10rpx;\n  display: -webkit-box;\n  -webkit-line-clamp: 1;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.record-meta {\n  display: flex;\n  margin-bottom: 10rpx;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  margin-right: 20rpx;\n}\n\n.meta-icon {\n  margin-right: 6rpx;\n}\n\n.meta-text {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.record-stats {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.stat-row {\n  display: flex;\n  margin-bottom: 10rpx;\n}\n\n.mini-stat {\n  display: flex;\n  align-items: center;\n  margin-right: 20rpx;\n}\n\n.stat-icon {\n  margin-right: 6rpx;\n}\n\n.stat-text {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.mini-stat.highlight .stat-text {\n  color: #5856D6;\n  font-weight: 500;\n}\n\n.record-actions {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  margin-top: 10rpx;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 10rpx 20rpx;\n  border-radius: 30rpx;\n  font-size: 24rpx;\n  margin-left: 15rpx;\n  border: 1rpx solid #DDDDDD;\n  color: #666666;\n}\n\n.action-icon {\n  margin-right: 6rpx;\n}\n\n.action-btn.primary {\n  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n  border: none;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999999;\n  margin-bottom: 30rpx;\n}\n\n.empty-state .action-btn {\n  padding: 15rpx 60rpx;\n  font-size: 28rpx;\n  color: #FFFFFF;\n  border: none;\n}\n\n/* 筛选弹窗样式 */\n.filter-popup {\n  background: #FFFFFF;\n  border-top-left-radius: 30rpx;\n  border-top-right-radius: 30rpx;\n  padding: 30rpx;\n  max-height: 70vh;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.filter-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n}\n\n.filter-close {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.filter-content {\n  max-height: calc(70vh - 180rpx);\n  overflow-y: auto;\n}\n\n.filter-section {\n  margin-bottom: 30rpx;\n}\n\n.section-title {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n  margin-bottom: 20rpx;\n}\n\n.filter-options {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.filter-option {\n  padding: 10rpx 30rpx;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  color: #666666;\n  background: #F5F5F5;\n  margin-right: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.filter-option.active {\n  background: rgba(88, 86, 214, 0.1);\n  color: #5856D6;\n  border: 1rpx solid rgba(88, 86, 214, 0.3);\n}\n\n.filter-footer {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 30rpx;\n  padding-top: 20rpx;\n  border-top: 1rpx solid #F0F0F0;\n}\n\n.filter-reset, .filter-apply {\n  flex: 1;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n}\n\n.filter-reset {\n  background: #F5F5F5;\n  color: #666666;\n  margin-right: 20rpx;\n}\n\n.filter-apply {\n  background: linear-gradient(135deg, #5856D6 0%, #A09CFF 100%);\n  color: #FFFFFF;\n  box-shadow: 0 4rpx 8rpx rgba(88, 86, 214, 0.2);\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/share-records/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;;;AAiSA,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAG5B,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,IACnB,CAAC;AAGD,UAAM,qBAAqBA,cAAAA,IAAI,CAAC;AAChC,UAAM,0BAA0BA,cAAG,IAAC,CAAC,CAAC,CAAC;AACvC,UAAM,6BAA6BA,cAAG,IAAC,CAAC,CAAC,CAAC;AAG1C,UAAM,cAAc;AAAA,MAClB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,MAC/B,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,MAChC,EAAE,OAAO,QAAQ,OAAO,QAAS;AAAA,MACjC,EAAE,OAAO,QAAQ,OAAO,eAAgB;AAAA,MACxC,EAAE,OAAO,OAAO,OAAO,SAAU;AAAA,IACnC;AAEA,UAAM,kBAAkB;AAAA,MACtB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,MAC/B,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,MAChC,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,MAClC,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,MAC/B,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,MAChC,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,IACjC;AAEA,UAAM,qBAAqB;AAAA,MACzB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,MAC/B,EAAE,OAAO,MAAM,OAAO,WAAY;AAAA,MAClC,EAAE,OAAO,MAAM,OAAO,UAAW;AAAA,MACjC,EAAE,OAAO,OAAO,OAAO,SAAU;AAAA,MACjC,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,IAClC;AAGA,UAAM,YAAY;AAAA,MAChB,EAAE,MAAM,MAAM,MAAM,OAAO,WAAW,UAAU,YAAY,kCAAmC;AAAA,MAC/F,EAAE,MAAM,MAAM,MAAM,YAAY,WAAW,UAAU,YAAY,2CAA4C;AAAA,MAC7G,EAAE,MAAM,MAAM,MAAM,WAAW,WAAW,UAAU,YAAY,0CAA2C;AAAA,MAC3G,EAAE,MAAM,OAAO,MAAM,UAAU,WAAW,WAAW,YAAY,yCAA0C;AAAA,IAC7G;AAGA,UAAM,cAAc;AAAA,MAClB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,eAAe;AAAA,QACf,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,eAAe;AAAA,QACf,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,eAAe;AAAA,QACf,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,eAAe;AAAA,QACf,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,QACV,eAAe;AAAA,QACf,YAAY;AAAA,MACb;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,UAAM,cAAc,MAAM;AAExB,kBAAY,QAAQ;AAAA,IACtB;AAEA,UAAM,mBAAmB,CAAC,SAAS;AACjC,UAAI,SAAS,OAAO;AAClB,eAAO,YAAY;AAAA,MACpB;AACD,aAAO,YAAY,MAAM,OAAO,YAAU,OAAO,gBAAgB,IAAI;AAAA,IACvE;AAEA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAEA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,iBAAW,QAAQ,EAAE,OAAO;AAAA,IAC9B;AAEA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,iBAAW,MAAM;AACf;AACA,qBAAa,QAAQ;AAAA,MACtB,GAAE,GAAI;AAAA,IACT;AAEA,UAAM,WAAW,MAAM;AAErBC,oBAAAA,MAAY,MAAA,OAAA,sEAAA,QAAQ;AAAA,IACtB;AAEA,UAAM,iBAAiB,CAAC,SAAS;AAC/B,YAAM,UAAU;AAAA,QACd,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,MACd;AACE,aAAO,QAAQ,IAAI,KAAK;AAAA,IAC1B;AAEA,UAAM,uBAAuB,CAAC,SAAS;AACrC,YAAM,QAAQ;AAAA,QACZ,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,MACd;AACE,aAAO,MAAM,IAAI,KAAK;AAAA,IACxB;AAEA,UAAM,mBAAmB,CAAC,WAAW;AACnC,cAAQ,OAAO,aAAW;AAAA,QACxB,KAAK;AACH,qBAAW,iEAAiE,OAAO,EAAE,EAAE;AACvF;AAAA,QACF,KAAK;AACH,qBAAW,2DAA2D,OAAO,EAAE,EAAE;AACjF;AAAA,QACF,KAAK;AACH,qBAAW,yDAAyD,OAAO,EAAE,EAAE;AAC/E;AAAA,QACF;AACE,qBAAW,qEAAqE,OAAO,EAAE,EAAE;AAAA,MAC9F;AAAA,IACH;AAEA,UAAM,aAAa,CAAC,WAAW;AAC7BA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,MAC9C,CAAG;AAAA,IACH;AAEA,UAAM,gBAAgB,CAAC,WAAW;AAChC,iBAAW,iEAAiE,OAAO,EAAE,EAAE;AAAA,IACzF;AAEA,UAAM,aAAa,MAAM;AACvB,kBAAY,MAAM;IACpB;AAEA,UAAM,cAAc,MAAM;AACxB,kBAAY,MAAM;IACpB;AAEA,UAAM,mBAAmB,CAAC,UAAU;AAClC,yBAAmB,QAAQ;AAAA,IAC7B;AAEA,UAAM,uBAAuB,CAAC,UAAU;AACtC,YAAM,WAAW,wBAAwB,MAAM,QAAQ,KAAK;AAC5D,UAAI,UAAU,GAAG;AAEf,gCAAwB,QAAQ,CAAC,CAAC;AAAA,MACtC,OAAS;AAEL,YAAI,wBAAwB,MAAM,SAAS,CAAC,GAAG;AAC7C,kCAAwB,QAAQ,wBAAwB,MAAM,OAAO,UAAQ,SAAS,CAAC;AAAA,QACxF;AAGD,YAAI,aAAa,IAAI;AACnB,kCAAwB,MAAM,OAAO,UAAU,CAAC;AAEhD,cAAI,wBAAwB,MAAM,WAAW,GAAG;AAC9C,oCAAwB,QAAQ,CAAC,CAAC;AAAA,UACnC;AAAA,QACP,OAAW;AACL,kCAAwB,MAAM,KAAK,KAAK;AAAA,QACzC;AAAA,MACF;AAAA,IACH;AAEA,UAAM,0BAA0B,CAAC,UAAU;AACzC,YAAM,WAAW,2BAA2B,MAAM,QAAQ,KAAK;AAC/D,UAAI,UAAU,GAAG;AAEf,mCAA2B,QAAQ,CAAC,CAAC;AAAA,MACzC,OAAS;AAEL,YAAI,2BAA2B,MAAM,SAAS,CAAC,GAAG;AAChD,qCAA2B,QAAQ,2BAA2B,MAAM,OAAO,UAAQ,SAAS,CAAC;AAAA,QAC9F;AAGD,YAAI,aAAa,IAAI;AACnB,qCAA2B,MAAM,OAAO,UAAU,CAAC;AAEnD,cAAI,2BAA2B,MAAM,WAAW,GAAG;AACjD,uCAA2B,QAAQ,CAAC,CAAC;AAAA,UACtC;AAAA,QACP,OAAW;AACL,qCAA2B,MAAM,KAAK,KAAK;AAAA,QAC5C;AAAA,MACF;AAAA,IACH;AAEA,UAAM,cAAc,MAAM;AACxB,yBAAmB,QAAQ;AAC3B,8BAAwB,QAAQ,CAAC,CAAC;AAClC,iCAA2B,QAAQ,CAAC,CAAC;AAAA,IACvC;AAEA,UAAM,cAAc,MAAM;AAExBA,oBAAAA,MAAY,MAAA,OAAA,sEAAA,QAAQ;AAAA,QAClB,MAAM,YAAY,mBAAmB,KAAK,EAAE;AAAA,QAC5C,WAAW,wBAAwB,MAAM,IAAI,WAAS,gBAAgB,KAAK,EAAE,KAAK;AAAA,QAClF,cAAc,2BAA2B,MAAM,IAAI,WAAS,mBAAmB,KAAK,EAAE,KAAK;AAAA,MAC/F,CAAG;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAED;IACF;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxkBA,GAAG,WAAW,eAAe;"}