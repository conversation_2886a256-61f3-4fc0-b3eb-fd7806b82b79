"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_distributionService = require("../../../utils/distributionService.js");
const _sfc_main = {
  __name: "merchant-apply",
  setup(__props) {
    const searchKeyword = common_vendor.ref("");
    const merchants = common_vendor.ref([]);
    const pagination = common_vendor.reactive({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });
    const hasMoreData = common_vendor.ref(false);
    const loading = common_vendor.ref(false);
    const showApplyModal = common_vendor.ref(false);
    const selectedMerchant = common_vendor.ref({});
    const formData = common_vendor.reactive({
      reason: "",
      experience: "",
      contact: ""
    });
    const canSubmit = common_vendor.computed(() => {
      return formData.reason.trim() !== "" && formData.experience.trim() !== "" && formData.contact.trim() !== "" && formData.contact.length === 11;
    });
    common_vendor.onMounted(async () => {
      await getMerchants();
    });
    const getMerchants = async (loadMore2 = false) => {
      if (loading.value)
        return;
      try {
        loading.value = true;
        const page = loadMore2 ? pagination.page + 1 : 1;
        const result = await utils_distributionService.distributionService.getMerchantsWithDistribution({
          page,
          pageSize: pagination.pageSize,
          keyword: searchKeyword.value
        });
        if (result) {
          if (loadMore2) {
            merchants.value = [...merchants.value, ...result.list];
          } else {
            merchants.value = result.list;
          }
          pagination.page = page;
          pagination.total = result.pagination.total;
          pagination.totalPages = result.pagination.totalPages;
          hasMoreData.value = pagination.page < pagination.totalPages;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/merchant-apply.vue:230", "获取商家列表失败", error);
        common_vendor.index.showToast({
          title: "获取商家列表失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    };
    const searchMerchants = () => {
      getMerchants();
    };
    const clearSearch = () => {
      searchKeyword.value = "";
      getMerchants();
    };
    const loadMore = () => {
      if (hasMoreData.value && !loading.value) {
        getMerchants(true);
      }
    };
    const selectMerchant = (merchant) => {
      if (merchant.applied) {
        common_vendor.index.showToast({
          title: "您已申请该商家",
          icon: "none"
        });
        return;
      }
      selectedMerchant.value = merchant;
      showApplyModal.value = true;
    };
    const closeApplyModal = () => {
      showApplyModal.value = false;
    };
    const submitApplication = async () => {
      if (!canSubmit.value)
        return;
      try {
        common_vendor.index.showLoading({
          title: "提交中...",
          mask: true
        });
        const result = await utils_distributionService.distributionService.applyMerchantDistributor({
          merchantId: selectedMerchant.value.id,
          reason: formData.reason,
          experience: formData.experience,
          contact: formData.contact
        });
        common_vendor.index.hideLoading();
        if (result.success) {
          common_vendor.index.showModal({
            title: "申请提交成功",
            content: "您的专属分销员申请已提交，请耐心等待商家审核。",
            showCancel: false,
            success: () => {
              const index = merchants.value.findIndex((m) => m.id === selectedMerchant.value.id);
              if (index !== -1) {
                merchants.value[index].applied = true;
              }
              closeApplyModal();
              formData.reason = "";
              formData.experience = "";
              formData.contact = "";
            }
          });
        } else {
          common_vendor.index.showModal({
            title: "申请提交失败",
            content: result.message || "请稍后再试",
            showCancel: false
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/merchant-apply.vue:326", "提交申请失败", error);
        common_vendor.index.showToast({
          title: "提交申请失败",
          icon: "none"
        });
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "商家专属分销帮助",
        content: "成为商家专属分销员后，您可以获得更高的佣金比例和更多专属特权。选择您感兴趣的商家，提交申请后等待商家审核。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: common_vendor.o(searchMerchants),
        d: searchKeyword.value,
        e: common_vendor.o(($event) => searchKeyword.value = $event.detail.value),
        f: searchKeyword.value
      }, searchKeyword.value ? {
        g: common_vendor.o(clearSearch)
      } : {}, {
        h: common_vendor.o(searchMerchants),
        i: merchants.value.length > 0
      }, merchants.value.length > 0 ? {
        j: common_vendor.f(merchants.value, (merchant, index, i0) => {
          return {
            a: merchant.logo,
            b: common_vendor.t(merchant.name),
            c: common_vendor.t(merchant.category),
            d: common_vendor.t(merchant.commissionRate),
            e: common_vendor.t(merchant.applied ? "已申请" : "申请"),
            f: merchant.applied ? 1 : "",
            g: index,
            h: common_vendor.o(($event) => selectMerchant(merchant), index)
          };
        })
      } : {
        k: common_assets._imports_0$51
      }, {
        l: hasMoreData.value && merchants.value.length > 0
      }, hasMoreData.value && merchants.value.length > 0 ? common_vendor.e({
        m: loading.value
      }, loading.value ? {} : {
        n: common_vendor.o(loadMore)
      }) : {}, {
        o: showApplyModal.value
      }, showApplyModal.value ? {
        p: common_vendor.o(closeApplyModal),
        q: common_vendor.o(closeApplyModal),
        r: selectedMerchant.value.logo,
        s: common_vendor.t(selectedMerchant.value.name),
        t: formData.reason,
        v: common_vendor.o(($event) => formData.reason = $event.detail.value),
        w: common_vendor.t(formData.reason.length),
        x: formData.experience,
        y: common_vendor.o(($event) => formData.experience = $event.detail.value),
        z: common_vendor.t(formData.experience.length),
        A: formData.contact,
        B: common_vendor.o(($event) => formData.contact = $event.detail.value),
        C: common_vendor.o(closeApplyModal),
        D: !canSubmit.value,
        E: !canSubmit.value ? 1 : "",
        F: common_vendor.o(submitApplication)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/distribution/pages/merchant-apply.js.map
