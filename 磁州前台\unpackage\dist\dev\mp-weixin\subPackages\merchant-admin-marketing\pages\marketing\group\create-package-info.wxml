<view class="create-package-info-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">拼团活动</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="step-indicator"><view class="step-progress"><view class="step-progress-bar" style="width:40%"></view></view><view class="step-text">步骤 2/5</view></view><scroll-view scroll-y class="page-content"><view class="page-title">填写套餐基本信息</view><view class="page-subtitle">请填写团购套餐的基本信息</view><view class="form-section"><view class="form-item"><text class="form-label">套餐名称 <text class="required">*</text></text><input class="form-input" type="text" placeholder="请输入套餐名称，如：四菜一汤家庭套餐" maxlength="30" value="{{c}}" bindinput="{{d}}"/><text class="input-counter">{{e}}/30</text></view><view class="form-item"><text class="form-label">套餐描述</text><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入套餐描述信息，如：适合3-4人用餐，荤素搭配，营养均衡" maxlength="100" value="{{f}}" bindinput="{{g}}"></textarea></block><text class="input-counter">{{h}}/100</text></view><view class="form-item"><text class="form-label">套餐分类 <text class="required">*</text></text><picker mode="selector" range="{{k}}" bindchange="{{l}}" class="form-picker"><view class="picker-value"><text wx:if="{{i}}">{{j}}</text><text wx:else class="placeholder">请选择套餐分类</text><view class="picker-arrow"></view></view></picker></view><view class="form-item"><text class="form-label">成团人数 <text class="required">*</text></text><view class="number-picker"><view class="number-btn minus" bindtap="{{m}}">-</view><input class="number-input" type="number" value="{{n}}" bindinput="{{o}}"/><view class="number-btn plus" bindtap="{{p}}">+</view><text class="unit-text">人</text></view></view><view class="form-item"><text class="form-label">活动有效期 <text class="required">*</text></text><view class="date-range-picker"><picker mode="date" value="{{s}}" bindchange="{{t}}" class="date-picker"><view class="date-picker-value"><text wx:if="{{q}}">{{r}}</text><text wx:else class="placeholder">开始日期</text></view></picker><text class="date-separator">至</text><picker mode="date" value="{{x}}" bindchange="{{y}}" class="date-picker"><view class="date-picker-value"><text wx:if="{{v}}">{{w}}</text><text wx:else class="placeholder">结束日期</text></view></picker></view></view></view></scroll-view><view class="footer-buttons"><button class="btn btn-secondary" bindtap="{{z}}">上一步</button><button class="btn btn-primary" bindtap="{{A}}">下一步</button></view></view>