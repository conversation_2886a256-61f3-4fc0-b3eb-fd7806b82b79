{"version": 3, "file": "settings.js", "sources": ["mock/user/settings.js"], "sourcesContent": ["// 用户设置模拟数据\r\nexport const userSettings = {\r\n  notifications: {\r\n    pushEnabled: true,\r\n    commentNotify: true,\r\n    likeNotify: true,\r\n    followNotify: true,\r\n    systemNotify: true,\r\n    activityNotify: true,\r\n    messageNotify: true\r\n  },\r\n  privacy: {\r\n    profileVisible: 'public', // public, friends, private\r\n    phoneVisible: 'friends', // public, friends, private\r\n    locationVisible: 'friends', // public, friends, private\r\n    allowStrangerMessage: true\r\n  },\r\n  theme: {\r\n    darkMode: false,\r\n    fontScale: 1,\r\n    colorScheme: 'blue' // blue, green, orange, purple\r\n  },\r\n  language: 'zh-CN',\r\n  autoPlay: true,\r\n  dataUsage: {\r\n    saveData: false,\r\n    autoDownload: true\r\n  }\r\n};\r\n\r\n// 获取用户设置的API函数\r\nexport const fetchUserSettings = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(userSettings);\r\n    }, 300);\r\n  });\r\n};\r\n\r\n// 更新用户设置的API函数\r\nexport const updateUserSettings = (data) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      // 模拟更新成功\r\n      const updatedSettings = {\r\n        ...userSettings,\r\n        ...data\r\n      };\r\n      resolve({\r\n        success: true,\r\n        data: updatedSettings\r\n      });\r\n    }, 500);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,eAAe;AAAA,EAC1B,eAAe;AAAA,IACb,aAAa;AAAA,IACb,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,EAChB;AAAA,EACD,SAAS;AAAA,IACP,gBAAgB;AAAA;AAAA,IAChB,cAAc;AAAA;AAAA,IACd,iBAAiB;AAAA;AAAA,IACjB,sBAAsB;AAAA,EACvB;AAAA,EACD,OAAO;AAAA,IACL,UAAU;AAAA,IACV,WAAW;AAAA,IACX,aAAa;AAAA;AAAA,EACd;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,IACT,UAAU;AAAA,IACV,cAAc;AAAA,EACf;AACH;AAGY,MAAC,oBAAoB,MAAM;AACrC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,YAAY;AAAA,IACrB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,qBAAqB,CAAC,SAAS;AAC1C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AAEf,YAAM,kBAAkB;AAAA,QACtB,GAAG;AAAA,QACH,GAAG;AAAA,MACX;AACM,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,MACd,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;"}