<template>
  <view class="profile-page">
    <!-- 其他内容 -->
    <view class="menu-list">
      <!-- 其他菜单项 -->
      <view class="menu-item" @click="navigateToMyRedPackets">
        <view class="menu-icon">
          <image src="/static/images/red-packet-icon.png" mode="aspectFit"></image>
        </view>
        <view class="menu-text">我的红包</view>
        <view class="menu-arrow">›</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    navigateToMyRedPackets() {
      uni.navigateTo({
        url: '/pages/user/my-red-packets'
      });
    }
  }
}
</script>

<style lang="scss">
.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
  
  .menu-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 20rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .menu-text {
    flex: 1;
    font-size: 28rpx;
    color: #333;
  }
  
  .menu-arrow {
    font-size: 32rpx;
    color: #999;
  }
}
</style> 