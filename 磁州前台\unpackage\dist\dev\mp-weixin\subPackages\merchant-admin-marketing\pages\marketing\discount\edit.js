"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  setup() {
    const discountId = common_vendor.ref("");
    const discountForm = common_vendor.reactive({
      title: "",
      startDate: "",
      endDate: "",
      status: "active",
      rules: [],
      applicableProducts: "全部商品",
      canStack: false,
      perPersonLimit: "3",
      instructions: ""
    });
    const showDatePickerDialog = common_vendor.ref(false);
    const currentDatePicker = common_vendor.ref("");
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function showDatePicker(type) {
      currentDatePicker.value = type;
      showDatePickerDialog.value = true;
    }
    function closeDatePicker() {
      showDatePickerDialog.value = false;
    }
    function onDateSelect(e) {
      const selectedDate = e.fulldate;
      if (currentDatePicker.value === "start") {
        discountForm.startDate = selectedDate;
      } else {
        discountForm.endDate = selectedDate;
      }
      closeDatePicker();
    }
    function onStatusChange(e) {
      discountForm.status = e.detail.value ? "active" : "paused";
    }
    function onStackChange(e) {
      discountForm.canStack = e.detail.value;
    }
    function addRule() {
      discountForm.rules.push({
        minAmount: "",
        discountAmount: ""
      });
    }
    function deleteRule(index) {
      discountForm.rules.splice(index, 1);
    }
    function selectProducts() {
      common_vendor.index.showActionSheet({
        itemList: ["全部商品", "指定商品", "指定分类"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              discountForm.applicableProducts = "全部商品";
              break;
            case 1:
              discountForm.applicableProducts = "指定商品";
              break;
            case 2:
              discountForm.applicableProducts = "指定分类";
              break;
          }
        }
      });
    }
    function validateForm() {
      if (!discountForm.title.trim()) {
        common_vendor.index.showToast({
          title: "请输入活动名称",
          icon: "none"
        });
        return false;
      }
      if (!discountForm.startDate || !discountForm.endDate) {
        common_vendor.index.showToast({
          title: "请选择活动时间",
          icon: "none"
        });
        return false;
      }
      if (discountForm.rules.length === 0) {
        common_vendor.index.showToast({
          title: "请添加至少一条满减规则",
          icon: "none"
        });
        return false;
      }
      for (const rule of discountForm.rules) {
        if (!rule.minAmount || !rule.discountAmount) {
          common_vendor.index.showToast({
            title: "请完善满减规则",
            icon: "none"
          });
          return false;
        }
        if (parseFloat(rule.discountAmount) >= parseFloat(rule.minAmount)) {
          common_vendor.index.showToast({
            title: "优惠金额不能大于等于满减金额",
            icon: "none"
          });
          return false;
        }
      }
      return true;
    }
    function saveDiscount() {
      if (!validateForm()) {
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    }
    function loadDiscountData(id) {
      discountId.value = id;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        Object.assign(discountForm, {
          title: "春季促销活动",
          startDate: "2023-04-01",
          endDate: "2023-04-30",
          status: "active",
          rules: [
            { minAmount: "100", discountAmount: "10" },
            { minAmount: "200", discountAmount: "25" },
            { minAmount: "300", discountAmount: "50" }
          ],
          applicableProducts: "全部商品",
          canStack: false,
          perPersonLimit: "3",
          instructions: "活动期间，每位用户最多可使用3次满减优惠，不可与其他优惠同时使用"
        });
        common_vendor.index.hideLoading();
      }, 500);
    }
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (options.id) {
        loadDiscountData(options.id);
      } else {
        addRule();
      }
    });
    return {
      discountForm,
      showDatePickerDialog,
      currentDatePicker,
      goBack,
      showDatePicker,
      closeDatePicker,
      onDateSelect,
      onStatusChange,
      onStackChange,
      addRule,
      deleteRule,
      selectProducts,
      saveDiscount
    };
  }
};
if (!Array) {
  const _component_uni_calendar = common_vendor.resolveComponent("uni-calendar");
  _component_uni_calendar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    b: common_vendor.o((...args) => $setup.saveDiscount && $setup.saveDiscount(...args)),
    c: $setup.discountForm.title,
    d: common_vendor.o(($event) => $setup.discountForm.title = $event.detail.value),
    e: common_vendor.t($setup.discountForm.title.length),
    f: common_vendor.t($setup.discountForm.startDate || "开始日期"),
    g: common_vendor.o(($event) => $setup.showDatePicker("start")),
    h: common_vendor.t($setup.discountForm.endDate || "结束日期"),
    i: common_vendor.o(($event) => $setup.showDatePicker("end")),
    j: common_vendor.t($setup.discountForm.status === "active" ? "启用" : "暂停"),
    k: $setup.discountForm.status === "active",
    l: common_vendor.o((...args) => $setup.onStatusChange && $setup.onStatusChange(...args)),
    m: common_vendor.o((...args) => $setup.addRule && $setup.addRule(...args)),
    n: common_vendor.f($setup.discountForm.rules, (rule, index, i0) => {
      return {
        a: rule.minAmount,
        b: common_vendor.o(($event) => rule.minAmount = $event.detail.value, index),
        c: rule.discountAmount,
        d: common_vendor.o(($event) => rule.discountAmount = $event.detail.value, index),
        e: common_vendor.o(($event) => $setup.deleteRule(index), index),
        f: index
      };
    }),
    o: $setup.discountForm.rules.length === 0
  }, $setup.discountForm.rules.length === 0 ? {} : {}, {
    p: common_vendor.t($setup.discountForm.applicableProducts || "全部商品"),
    q: common_vendor.o((...args) => $setup.selectProducts && $setup.selectProducts(...args)),
    r: $setup.discountForm.canStack,
    s: common_vendor.o((...args) => $setup.onStackChange && $setup.onStackChange(...args)),
    t: $setup.discountForm.perPersonLimit,
    v: common_vendor.o(($event) => $setup.discountForm.perPersonLimit = $event.detail.value),
    w: $setup.discountForm.instructions,
    x: common_vendor.o(($event) => $setup.discountForm.instructions = $event.detail.value),
    y: common_vendor.t($setup.discountForm.instructions.length),
    z: common_vendor.o((...args) => $setup.saveDiscount && $setup.saveDiscount(...args)),
    A: $setup.showDatePickerDialog
  }, $setup.showDatePickerDialog ? {
    B: common_vendor.o($setup.onDateSelect),
    C: common_vendor.o($setup.closeDatePicker),
    D: common_vendor.p({
      insert: false,
      ["start-date"]: "2020-01-01",
      ["end-date"]: "2030-12-31"
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/discount/edit.js.map
