<!-- 创建中 -->
<template>
  <view class="create-package-price-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">拼团活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 步骤指示器 -->
    <view class="step-indicator">
      <view class="step-progress">
        <view class="step-progress-bar" style="width: 60%"></view>
      </view>
      <view class="step-text">步骤 3/5</view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <view class="page-title">设置团购套餐价格</view>
      <view class="page-subtitle">请设置团购套餐的价格信息</view>
      
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">市场价 <text class="required">*</text></text>
          <view class="price-input-wrapper">
            <text class="price-symbol">¥</text>
            <input class="form-input" type="digit" v-model="priceInfo.marketPrice" placeholder="请输入套餐市场价" />
          </view>
          <text class="form-tip">市场价是指套餐内所有商品/服务的原价总和</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">日常价 <text class="required">*</text></text>
          <view class="price-input-wrapper">
            <text class="price-symbol">¥</text>
            <input class="form-input" type="digit" v-model="priceInfo.regularPrice" placeholder="请输入套餐日常价" />
          </view>
          <text class="form-tip">日常价是指套餐平时销售的价格</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">拼团价 <text class="required">*</text></text>
          <view class="price-input-wrapper">
            <text class="price-symbol">¥</text>
            <input class="form-input" type="digit" v-model="priceInfo.groupPrice" placeholder="请输入套餐拼团价" />
          </view>
          <text class="form-tip">拼团价是指用户参与拼团时的优惠价格</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">单人限购</text>
          <view class="number-picker">
            <view class="number-btn minus" @tap="decrementLimitPerUser">-</view>
            <input class="number-input" type="number" v-model="priceInfo.limitPerUser" />
            <view class="number-btn plus" @tap="incrementLimitPerUser">+</view>
            <text class="unit-text">件</text>
          </view>
          <text class="form-tip">0表示不限购，最多可设置为99</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">库存数量 <text class="required">*</text></text>
          <view class="number-picker">
            <view class="number-btn minus" @tap="decrementStock">-</view>
            <input class="number-input" type="number" v-model="priceInfo.stock" />
            <view class="number-btn plus" @tap="incrementStock">+</view>
            <text class="unit-text">件</text>
          </view>
        </view>
      </view>
      
      <!-- 价格预览 -->
      <view class="price-preview">
        <view class="preview-header">价格预览</view>
        
        <view class="preview-content">
          <view class="preview-item">
            <text class="preview-label">市场价</text>
            <text class="preview-value original">¥{{formatPrice(priceInfo.marketPrice)}}</text>
          </view>
          
          <view class="preview-item">
            <text class="preview-label">日常价</text>
            <text class="preview-value regular">¥{{formatPrice(priceInfo.regularPrice)}}</text>
          </view>
          
          <view class="preview-item">
            <text class="preview-label">拼团价</text>
            <text class="preview-value group">¥{{formatPrice(priceInfo.groupPrice)}}</text>
          </view>
          
          <view class="preview-item">
            <text class="preview-label">节省金额</text>
            <text class="preview-value save">¥{{calculateSavings()}}</text>
          </view>
          
          <view class="preview-item">
            <text class="preview-label">折扣率</text>
            <text class="preview-value discount">{{calculateDiscount()}}折</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="btn btn-secondary" @click="goBack">上一步</button>
      <button class="btn btn-primary" @click="nextStep">下一步</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      priceInfo: {
        marketPrice: '',
        regularPrice: '',
        groupPrice: '',
        limitPerUser: 1,
        stock: 100
      }
    }
  },
  onLoad() {
    // 尝试从本地存储获取之前保存的数据
    try {
      const savedPriceInfo = uni.getStorageSync('packagePriceInfo');
      if (savedPriceInfo) {
        this.priceInfo = JSON.parse(savedPriceInfo);
      }
    } catch (e) {
      console.error('读取本地存储失败:', e);
    }
  },
  methods: {
    goBack() {
      // 保存当前页面数据
      this.saveData();
      uni.navigateBack();
    },
    nextStep() {
      // 表单验证
      if (!this.priceInfo.marketPrice) {
        uni.showToast({
          title: '请输入市场价',
          icon: 'none'
        });
        return;
      }
      
      if (!this.priceInfo.regularPrice) {
        uni.showToast({
          title: '请输入日常价',
          icon: 'none'
        });
        return;
      }
      
      if (!this.priceInfo.groupPrice) {
        uni.showToast({
          title: '请输入拼团价',
          icon: 'none'
        });
        return;
      }
      
      // 检查价格大小关系
      const marketPrice = parseFloat(this.priceInfo.marketPrice);
      const regularPrice = parseFloat(this.priceInfo.regularPrice);
      const groupPrice = parseFloat(this.priceInfo.groupPrice);
      
      if (marketPrice <= regularPrice) {
        uni.showToast({
          title: '市场价应高于日常价',
          icon: 'none'
        });
        return;
      }
      
      if (regularPrice <= groupPrice) {
        uni.showToast({
          title: '日常价应高于拼团价',
          icon: 'none'
        });
        return;
      }
      
      // 保存数据并跳转到下一步
      this.saveData();
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items'
      });
    },
    saveData() {
      // 保存当前页面数据到本地存储
      try {
        uni.setStorageSync('packagePriceInfo', JSON.stringify(this.priceInfo));
      } catch (e) {
        console.error('保存数据失败:', e);
      }
    },
    formatPrice(price) {
      if (!price) return '0.00';
      return parseFloat(price).toFixed(2);
    },
    calculateSavings() {
      const marketPrice = parseFloat(this.priceInfo.marketPrice) || 0;
      const groupPrice = parseFloat(this.priceInfo.groupPrice) || 0;
      const savings = marketPrice - groupPrice;
      return savings > 0 ? savings.toFixed(2) : '0.00';
    },
    calculateDiscount() {
      const marketPrice = parseFloat(this.priceInfo.marketPrice) || 1;
      const groupPrice = parseFloat(this.priceInfo.groupPrice) || 0;
      const discount = (groupPrice / marketPrice) * 10;
      return discount.toFixed(1);
    },
    decrementLimitPerUser() {
      if (this.priceInfo.limitPerUser > 0) {
        this.priceInfo.limitPerUser--;
      }
    },
    incrementLimitPerUser() {
      if (this.priceInfo.limitPerUser < 99) {
        this.priceInfo.limitPerUser++;
      }
    },
    decrementStock() {
      if (this.priceInfo.stock > 1) {
        this.priceInfo.stock--;
      }
    },
    incrementStock() {
      if (this.priceInfo.stock < 9999) {
        this.priceInfo.stock++;
      }
    },
    showHelp() {
      uni.showToast({
        title: '帮助信息',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.create-package-price-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
}

/* 步骤指示器 */
.step-indicator {
  padding: 15px;
  background: #FFFFFF;
}

.step-progress {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 5px;
  position: relative;
}

.step-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}

.step-text {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 20px 15px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.page-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

/* 表单样式 */
.form-section {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.required {
  color: #FF3B30;
}

.price-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 8px;
  border: 1px solid #EBEDF5;
  padding: 0 12px;
}

.price-symbol {
  font-size: 14px;
  color: #333;
  margin-right: 5px;
}

.form-input {
  flex: 1;
  height: 45px;
  font-size: 14px;
  color: #333;
  background: transparent;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.number-picker {
  display: flex;
  align-items: center;
  height: 45px;
}

.number-btn {
  width: 45px;
  height: 45px;
  background: #F5F7FA;
  border: 1px solid #EBEDF5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #333;
}

.number-btn.minus {
  border-radius: 8px 0 0 8px;
}

.number-btn.plus {
  border-radius: 0 8px 8px 0;
}

.number-input {
  width: 60px;
  height: 45px;
  background: #F5F7FA;
  border-top: 1px solid #EBEDF5;
  border-bottom: 1px solid #EBEDF5;
  text-align: center;
  font-size: 14px;
  color: #333;
}

.unit-text {
  margin-left: 10px;
  font-size: 14px;
  color: #666;
}

/* 价格预览 */
.price-preview {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.preview-header {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #EBEDF5;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-label {
  font-size: 14px;
  color: #666;
}

.preview-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.preview-value.original {
  text-decoration: line-through;
  color: #999;
  font-weight: normal;
}

.preview-value.regular {
  color: #666;
}

.preview-value.group {
  color: #FF3B30;
}

.preview-value.save {
  color: #FF9500;
}

.preview-value.discount {
  color: #34C759;
}

/* 底部按钮 */
.footer-buttons {
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}

.btn-secondary {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}
</style>
