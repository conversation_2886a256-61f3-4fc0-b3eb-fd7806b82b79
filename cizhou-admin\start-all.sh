#!/bin/bash

echo "🚀 一键启动磁州生活网后台管理系统（前端+后端）"

# 检查当前目录
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ 请在项目根目录（cizhou-admin）下运行此脚本"
    exit 1
fi

# 检查必要的工具
echo "🔍 检查环境依赖..."

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 18+"
    exit 1
fi

# 检查Java
if ! command -v java &> /dev/null; then
    echo "❌ Java未安装，请先安装JDK 17+"
    exit 1
fi

# 检查Maven
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven未安装，请先安装Maven 3.8+"
    exit 1
fi

echo "✅ 环境检查通过"

# 启动后端基础服务
echo "📦 启动后端基础服务..."
cd backend
chmod +x start-dev.sh
./start-dev.sh

if [ $? -ne 0 ]; then
    echo "❌ 后端服务启动失败"
    exit 1
fi

# 等待后端服务完全启动
echo "⏳ 等待后端服务完全启动..."
sleep 10

# 检查后端服务状态
echo "🔍 检查后端服务状态..."
for i in {1..30}; do
    if curl -s http://localhost:8080/actuator/health > /dev/null; then
        echo "✅ 后端服务启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 后端服务启动超时"
        exit 1
    fi
    echo "等待后端服务启动... ($i/30)"
    sleep 2
done

# 启动前端服务
echo "🎨 启动前端服务..."
cd ../frontend

# 检查前端依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm config set registry https://registry.npmmirror.com/
    npm install
    
    if [ $? -ne 0 ]; then
        echo "❌ 前端依赖安装失败"
        exit 1
    fi
fi

# 启动前端开发服务器
echo "🚀 启动前端开发服务器..."
echo ""
echo "🎉 系统启动完成！"
echo ""
echo "📋 访问地址："
echo "   前端管理后台: http://localhost:3000"
echo "   后端API网关:  http://localhost:8080"
echo "   Nacos控制台:  http://localhost:8848/nacos (nacos/nacos)"
echo ""
echo "🔑 默认管理员账号："
echo "   用户名: admin"
echo "   密码:   admin123"
echo ""
echo "💡 提示："
echo "   - 按 Ctrl+C 停止前端服务"
echo "   - 后端服务将继续在Docker中运行"
echo "   - 如需停止后端服务，请运行: docker-compose -f backend/docker-compose.dev.yml down"
echo ""

# 启动前端（这会阻塞直到用户停止）
npm run dev

echo "👋 前端服务已停止"
echo "💡 后端服务仍在运行，如需停止请手动执行："
echo "   cd backend && docker-compose -f docker-compose.dev.yml down"
