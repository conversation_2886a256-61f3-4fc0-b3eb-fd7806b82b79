/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.add-btn {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  padding: 6px 12px;
  border-radius: 15px;
  color: #fff;
}
.btn-text {
  font-size: 12px;
  margin-right: 5px;
}
.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}
.plus-icon-small::before,
.plus-icon-small::after {
  content: "";
  position: absolute;
  background-color: #fff;
}
.plus-icon-small::before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}
.plus-icon-small::after {
  width: 2px;
  height: 12px;
  top: 0;
  left: 5px;
}

/* 我的模板区域 */
.template-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.template-list {
  margin-top: 10px;
}
.template-item {
  background-color: #fff;
  border-radius: 10px;
  margin-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  border: 1px solid #eee;
  overflow: hidden;
}
.template-preview {
  height: 100px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-image {
  width: 24px;
  height: 24px;
}
.template-name {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}
.template-info {
  padding: 12px 15px;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.template-desc {
  font-size: 14px;
  color: #666;
  flex: 1;
}
.template-type {
  font-size: 12px;
  color: #999;
  background-color: #F5F7FA;
  padding: 3px 8px;
  border-radius: 10px;
}
.template-actions {
  padding: 10px 15px;
  display: flex;
  justify-content: flex-end;
}
.action-btn {
  margin-left: 10px;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  background-color: #F5F7FA;
  color: #666;
}
.action-btn.edit {
  background-color: #E3F2FD;
  color: #1976D2;
}
.action-btn.delete {
  background-color: #FEE8E8;
  color: #FF5858;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}
.empty-icon {
  width: 60px;
  height: 60px;
  background-color: #F5F7FA;
  border-radius: 30px;
  margin-bottom: 10px;
}
.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 15px;
}
.empty-action {
  padding: 8px 20px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  border-radius: 20px;
  font-size: 14px;
}

/* 官方模板区域 */
.official-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.template-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}
.template-card {
  width: calc(50% - 10px);
  margin: 5px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.template-footer {
  background-color: #fff;
  padding: 10px;
}
.template-use-btn {
  background-color: #F5F7FA;
  color: #FF5858;
  font-size: 12px;
  padding: 5px 0;
  border-radius: 15px;
  text-align: center;
  margin-top: 8px;
}

/* 场景模板区域 */
.scenario-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.scenario-scroll {
  margin-top: 15px;
  white-space: nowrap;
}
.scenario-container {
  display: inline-flex;
  padding: 5px 0;
}
.scenario-card {
  width: 160px;
  margin-right: 15px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.scenario-preview {
  height: 120px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.scenario-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.scenario-name {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}
.scenario-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  background-color: #FF3B30;
  color: #fff;
}
.scenario-footer {
  background-color: #fff;
  padding: 10px;
}
.scenario-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  display: block;
  white-space: normal;
  height: 32px;
  overflow: hidden;
}
.scenario-use-btn {
  background-color: #F5F7FA;
  color: #FF5858;
  font-size: 12px;
  padding: 5px 0;
  border-radius: 15px;
  text-align: center;
}

/* 指南样式 */
.guide-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 80px;
}
.guide-steps {
  margin-top: 15px;
}
.guide-step {
  display: flex;
  margin-bottom: 15px;
}
.guide-step:last-child {
  margin-bottom: 0;
}
.step-number {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #FF5858;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
  flex-shrink: 0;
}
.step-content {
  flex: 1;
}
.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}
.step-desc {
  font-size: 12px;
  color: #666;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(255, 88, 88, 0.3);
  z-index: 100;
}
.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
}