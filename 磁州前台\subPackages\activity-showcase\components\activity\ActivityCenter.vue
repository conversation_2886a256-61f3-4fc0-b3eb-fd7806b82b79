<template>
  <!-- 同城活动中心模块 - 苹果风格设计 -->
  <view class="activity-center">
    <!-- 模块标题区 -->
    <view class="activity-header">
      <view class="title-container">
        <view class="title-bar"></view>
        <text class="activity-title">同城活动中心</text>
        <view class="activity-badge">
          <text class="badge-text">精选推荐</text>
        </view>
      </view>
      <view class="more-btn" @click="navigateTo('/subPackages/activity-showcase/pages/list')">
        <text class="more-text">更多</text>
        <view class="more-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 标签导航栏 -->
    <view class="activity-tabs-container">
      <scroll-view 
        class="activity-tabs" 
        scroll-x 
        show-scrollbar="false"
        :scroll-with-animation="true"
        :enhanced="true"
        :bounces="true"
      >
        <view 
          v-for="(tab, index) in activityTabs" 
          :key="index"
          class="activity-tab" 
          :class="{active: currentTab === index}" 
          @click="switchTab(index)"
        >
          <view class="tab-content">
            <view class="tab-icon" v-if="tab.icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path v-if="tab.name === '全部活动'" d="M3 3h18v18H3zM12 8v8M8 12h8"></path>
                <path v-else-if="tab.name === '拼团活动'" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75"></path>
                <path v-else-if="tab.name === '秒杀活动'" d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                <path v-else-if="tab.name === '优惠券'" d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <path v-else-if="tab.name === '满减活动'" d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                <circle v-else cx="12" cy="12" r="10"></circle>
              </svg>
            </view>
            <text class="tab-text">{{tab.name}}</text>
          </view>
          <view class="tab-line" v-if="currentTab === index"></view>
          <view class="tab-glow" v-if="currentTab === index"></view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 活动列表 - 水平滚动 -->
    <view class="activity-scroll-container">
      <view class="carousel-controls" v-if="filteredActivities.length > 1">
        <view class="control-btn prev-btn" @click="scrollToPrev" :class="{ disabled: currentScrollIndex === 0 && !isLooping }">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="control-btn next-btn" @click="scrollToNext">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </view>
      </view>
      
      <scroll-view 
        class="activity-scroll" 
        scroll-x 
        show-scrollbar="false"
        :scroll-with-animation="true"
        :enhanced="true"
        :bounces="true"
        :scroll-left="scrollLeft"
        ref="activityScrollRef"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
        @scroll="handleScroll"
      >
        <view class="activity-list" :style="{ paddingLeft: `${centerOffsetRpx}rpx`, paddingRight: `${centerOffsetRpx}rpx` }">
          <view 
            class="activity-item" 
            v-for="(item, index) in filteredActivities" 
            :key="item.id"
            :style="{ 
              transform: `scale(${currentScrollIndex === index ? 1 : 0.95})`, 
              opacity: currentScrollIndex === index ? 1 : 0.8 
            }"
          >
            <activity-card-factory 
              :item="item"
              @navigate="navigateToDetail"
              @favorite="toggleFavorite"
              @action="handleAction"
            />
          </view>
        </view>
      </scroll-view>
      
      <!-- 轮播指示器 -->
      <view class="carousel-indicators" v-if="filteredActivities.length > 1">
        <view 
          v-for="(_, index) in filteredActivities" 
          :key="index"
          class="indicator-dot"
          :class="{ active: currentScrollIndex === index }"
          @click="scrollToIndex(index)"
        ></view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import ActivityCardFactory from './ActivityCardFactory.vue';

// 活动标签数据
const activityTabs = ref([
  { name: '全部活动', icon: true },
  { name: '拼团活动', icon: true },
  { name: '秒杀活动', icon: true },
  { name: '优惠券', icon: true },
  { name: '满减活动', icon: true }
]);

// 当前选中的标签
const currentTab = ref(0);

// 模拟活动数据
const activities = ref([
  {
    id: '1',
    type: 'groupBuy',
    title: '5人拼团 | 磁县特产礼盒',
    status: 'ongoing',
    startTime: '2024-07-10',
    endTime: '2024-07-20',
    location: '磁县特产展销中心',
    coverImage: '/static/images/activity/group-buy-1.jpg',
    isFavorite: false,
    hot: true,
    isPaidTop: true, // 付费置顶
    groupPrice: 99,
    originalPrice: 199,
    groupSize: 5,
    currentGroupMembers: 3,
    participants: [
      { avatar: '/static/images/avatar/avatar-1.jpg' },
      { avatar: '/static/images/avatar/avatar-2.jpg' },
      { avatar: '/static/images/avatar/avatar-3.jpg' },
      { avatar: '/static/images/avatar/avatar-4.jpg' }
    ]
  },
  {
    id: '2',
    type: 'flashSale',
    title: '限时秒杀 | 本地农产品特惠',
    status: 'ongoing',
    startTime: '2024-07-12',
    endTime: '2024-07-13',
    location: '磁县农贸市场',
    coverImage: '/static/images/activity/flash-sale-1.jpg',
    isFavorite: true,
    isAdTop: true, // 广告置顶
    salePrice: 29.9,
    originalPrice: 59.9,
    totalStock: 100,
    soldCount: 78,
    participants: [
      { avatar: '/static/images/avatar/avatar-5.jpg' },
      { avatar: '/static/images/avatar/avatar-6.jpg' },
      { avatar: '/static/images/avatar/avatar-7.jpg' },
      { avatar: '/static/images/avatar/avatar-8.jpg' },
      { avatar: '/static/images/avatar/avatar-9.jpg' }
    ]
  },
  {
    id: '3',
    type: 'coupon',
    title: '周末专享 | 餐饮优惠券',
    status: 'upcoming',
    startTime: '2024-07-15',
    endTime: '2024-07-17',
    location: '磁县商业街',
    coverImage: '/static/images/activity/coupon-1.jpg',
    isFavorite: false,
    couponType: 'cash',
    couponValue: 50,
    couponCondition: '满200元可用',
    couponValidity: '2024-07-31',
    participants: [
      { avatar: '/static/images/avatar/avatar-10.jpg' },
      { avatar: '/static/images/avatar/avatar-11.jpg' }
    ]
  },
  {
    id: '4',
    type: 'discount',
    title: '商圈联合满减 | 暑期狂欢',
    status: 'ongoing',
    startTime: '2024-07-01',
    endTime: '2024-07-31',
    location: '磁县中心商场',
    coverImage: '/static/images/activity/discount-1.jpg',
    isFavorite: false,
    hot: true,
    isPaidTop: true, // 付费置顶
    discountRules: [
      { threshold: 100, discount: 20 },
      { threshold: 200, discount: 50 },
      { threshold: 300, discount: 100 }
    ],
    merchantCount: 28,
    participants: [
      { avatar: '/static/images/avatar/avatar-12.jpg' },
      { avatar: '/static/images/avatar/avatar-13.jpg' },
      { avatar: '/static/images/avatar/avatar-14.jpg' },
      { avatar: '/static/images/avatar/avatar-15.jpg' },
      { avatar: '/static/images/avatar/avatar-16.jpg' },
      { avatar: '/static/images/avatar/avatar-17.jpg' }
    ]
  },
  {
    id: '5',
    type: 'groupBuy',
    title: '亲子团购 | 磁县水上乐园门票',
    status: 'ongoing',
    startTime: '2024-07-05',
    endTime: '2024-08-05',
    location: '磁县水上乐园',
    coverImage: '/static/images/activity/group-buy-2.jpg',
    isFavorite: false,
    isAdTop: true, // 广告置顶
    groupPrice: 79,
    originalPrice: 158,
    groupSize: 3,
    currentGroupMembers: 1,
    participants: [
      { avatar: '/static/images/avatar/avatar-18.jpg' }
    ]
  }
]);

// 根据当前标签过滤活动
const filteredActivities = computed(() => {
  if (currentTab.value === 0) {
    return activities.value;
  }
  
  const tabTypeMap = {
    1: 'groupBuy',
    2: 'flashSale',
    3: 'coupon',
    4: 'discount'
  };
  
  const selectedType = tabTypeMap[currentTab.value];
  return activities.value.filter(activity => activity.type === selectedType);
});

// 自动轮播相关变量
const scrollLeft = ref(0);
const cardWidth = ref(620); // 卡片宽度 + 右边距 (rpx)
const cardWidthPx = ref(310); // 卡片宽度像素值
const scrollInterval = ref(null);
const activityScrollRef = ref(null);
const scrollSpeed = 3000; // 轮播间隔时间，单位毫秒
const scrollAnimationDuration = 500; // 滚动动画持续时间，单位毫秒
const currentScrollIndex = ref(0);
const lastScrollLeft = ref(0); // 记录上次滚动位置
const isScrolling = ref(false); // 是否正在滚动中
const isLooping = ref(true); // 是否循环轮播
const centerOffset = ref(0); // 居中偏移量（像素）
const centerOffsetRpx = ref(0); // 居中偏移量（rpx）
const screenWidth = ref(375); // 屏幕宽度（像素）

// 开始自动轮播
function startAutoScroll() {
  if (scrollInterval.value || filteredActivities.value.length <= 1) return;
  
  scrollInterval.value = setInterval(() => {
    if (isUserInteracting.value || isScrolling.value) return;
    
    // 如果已经到最后一个，则回到第一个
    if (currentScrollIndex.value >= filteredActivities.value.length - 1) {
      // 无动画快速回到第一个
      isScrolling.value = true;
      currentScrollIndex.value = 0;
      scrollToIndex(0, true);
      
      setTimeout(() => {
        isScrolling.value = false;
      }, 50);
    } else {
      // 正常向左滚动到下一个
      currentScrollIndex.value++;
      isScrolling.value = true;
      scrollToIndex(currentScrollIndex.value, true);
      
      setTimeout(() => {
        isScrolling.value = false;
      }, scrollAnimationDuration);
    }
  }, scrollSpeed);
}

// 停止自动轮播
function stopAutoScroll() {
  if (scrollInterval.value) {
    clearInterval(scrollInterval.value);
    scrollInterval.value = null;
  }
}

// 处理滚动事件
function handleScroll(e) {
  if (isUserInteracting.value) {
    // 用户正在交互，记录当前滚动位置
    lastScrollLeft.value = e.detail.scrollLeft;
  }
}

// 组件挂载时启动自动轮播
onMounted(() => {
  // 获取设备信息，根据屏幕宽度调整卡片宽度和居中偏移量
  uni.getSystemInfo({
    success: (res) => {
      // 保存屏幕宽度
      screenWidth.value = res.windowWidth;
      
      // 根据屏幕宽度动态调整卡片宽度
      let actualCardWidthRpx = 600; // 默认卡片宽度 (rpx)
      
      if (res.windowWidth <= 375) {
        actualCardWidthRpx = 550;
        cardWidth.value = 570; // 550 + 20 (卡片宽度 + 右边距)
      } else if (res.windowWidth >= 768) {
        actualCardWidthRpx = 650;
        cardWidth.value = 670; // 650 + 20 (卡片宽度 + 右边距)
      } else {
        actualCardWidthRpx = 600;
        cardWidth.value = 620; // 600 + 20 (卡片宽度 + 右边距)
      }
      
      // 计算rpx到px的转换比例
      const rpxRatio = res.windowWidth / 750;
      
      // 计算卡片宽度的像素值
      cardWidthPx.value = cardWidth.value * rpxRatio;
      
      // 计算居中偏移量（像素）= (屏幕宽度 - 卡片宽度) / 2
      centerOffset.value = (res.windowWidth - (actualCardWidthRpx * rpxRatio)) / 2;
      
      // 计算居中偏移量（rpx）
      centerOffsetRpx.value = centerOffset.value / rpxRatio;
      
      console.log('屏幕宽度:', res.windowWidth, 'px');
      console.log('卡片宽度:', actualCardWidthRpx, 'rpx =', actualCardWidthRpx * rpxRatio, 'px');
      console.log('居中偏移量:', centerOffset.value, 'px =', centerOffsetRpx.value, 'rpx');
      
      // 初始化滚动位置，使第一个卡片居中
      setTimeout(() => {
        scrollToIndex(0, false);
      }, 100);
    }
  });
  
  // 添加监听器，检测页面显示/隐藏状态
  uni.onAppShow(() => {
    if (!isUserInteracting.value) {
      startAutoScroll();
    }
  });
  
  uni.onAppHide(() => {
    stopAutoScroll();
  });
  
  // 延迟启动轮播，确保组件完全渲染
  setTimeout(() => {
    startAutoScroll();
  }, 1000);
});

// 组件销毁前停止自动轮播
onBeforeUnmount(() => {
  stopAutoScroll();
});

// 切换标签时重置轮播
function switchTab(index) {
  currentTab.value = index;
  currentScrollIndex.value = 0;
  
  // 重置滚动位置
  setTimeout(() => {
    scrollToIndex(0, false);
  }, 100);
  
  // 重新启动轮播
  stopAutoScroll();
  setTimeout(() => {
    startAutoScroll();
  }, 500);
}

// 导航到详情页
function navigateToDetail(data) {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/detail/index?id=${data.id}&type=${data.type}`,
    fail: (err) => {
      console.error('导航失败:', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
}

// 切换收藏状态
function toggleFavorite(id) {
  const activity = activities.value.find(item => item.id === id);
  if (activity) {
    activity.isFavorite = !activity.isFavorite;
    
    // 显示提示
    uni.showToast({
      title: activity.isFavorite ? '已收藏' : '已取消收藏',
      icon: 'none'
    });
  }
}

// 处理操作按钮点击
function handleAction(data) {
  console.log('操作按钮点击:', data);
  
  switch(data.type) {
    case 'groupBuy':
      if (data.status === 'ongoing') {
        uni.navigateTo({
          url: `/subPackages/activity-showcase/pages/group-buy?id=${data.id}`
        });
      } else if (data.status === 'upcoming') {
        uni.showToast({
          title: '已预约拼团提醒',
          icon: 'success'
        });
      }
      break;
      
    case 'flashSale':
      if (data.status === 'ongoing') {
        uni.navigateTo({
          url: `/subPackages/activity-showcase/pages/flash-sale?id=${data.id}`
        });
      } else if (data.status === 'upcoming') {
        uni.showToast({
          title: '已设置开始提醒',
          icon: 'success'
        });
      }
      break;
      
    case 'coupon':
      uni.showToast({
        title: '优惠券已领取',
        icon: 'success'
      });
      break;
      
    case 'discount':
      uni.navigateTo({
        url: `/subPackages/activity-showcase/pages/discount?id=${data.id}`
      });
      break;
      
    default:
      navigateToDetail(data);
  }
}

// 导航到指定页面
function navigateTo(url) {
  uni.navigateTo({
    url: url,
    fail: (err) => {
      console.error('导航失败:', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
}

// 用户交互相关
const isUserInteracting = ref(false);

// 处理触摸开始事件
function handleTouchStart() {
  isUserInteracting.value = true;
  stopAutoScroll();
}

// 滚动到上一个
function scrollToPrev() {
  if (currentScrollIndex.value <= 0) {
    if (isLooping.value) {
      // 循环到最后一个
      currentScrollIndex.value = filteredActivities.value.length - 1;
    } else {
      return;
    }
  } else {
    currentScrollIndex.value--;
  }
  
  // 临时停止自动轮播
  stopAutoScroll();
  
  // 滚动到目标位置
  scrollToIndex(currentScrollIndex.value);
  
  // 延迟恢复自动轮播
  setTimeout(() => {
    if (!isUserInteracting.value) {
      startAutoScroll();
    }
  }, 1500);
}

// 滚动到下一个
function scrollToNext() {
  if (currentScrollIndex.value >= filteredActivities.value.length - 1) {
    if (isLooping.value) {
      // 循环到第一个
      currentScrollIndex.value = 0;
    } else {
      return;
    }
  } else {
    currentScrollIndex.value++;
  }
  
  // 临时停止自动轮播
  stopAutoScroll();
  
  // 滚动到目标位置
  scrollToIndex(currentScrollIndex.value);
  
  // 延迟恢复自动轮播
  setTimeout(() => {
    if (!isUserInteracting.value) {
      startAutoScroll();
    }
  }, 1500);
}

// 滚动到指定索引
function scrollToIndex(index, animate = true) {
  currentScrollIndex.value = index;
  
  // 计算滚动位置：卡片宽度（像素）* 索引
  const targetScrollLeft = index * cardWidthPx.value;
  
  if (!animate) {
    // 无动画直接设置
    scrollLeft.value = targetScrollLeft;
  } else {
    // 有动画过渡
    isScrolling.value = true;
    scrollLeft.value = targetScrollLeft;
    
    setTimeout(() => {
      isScrolling.value = false;
    }, scrollAnimationDuration);
  }
  
  return targetScrollLeft;
}

// 处理触摸结束事件
function handleTouchEnd() {
  isUserInteracting.value = false;
  
  // 根据滚动位置确定当前应该显示的卡片索引
  const estimatedIndex = Math.round(lastScrollLeft.value / cardWidthPx.value);
  const maxIndex = filteredActivities.value.length - 1;
  
  // 边界检查
  currentScrollIndex.value = Math.max(0, Math.min(estimatedIndex, maxIndex));
  
  // 平滑滚动到正确位置
  scrollToIndex(currentScrollIndex.value);
  
  // 延迟恢复自动轮播
  setTimeout(() => {
    if (!isUserInteracting.value) {
      startAutoScroll();
    }
  }, 1500);
}
</script>

<style scoped>
/* 活动中心模块基础样式 */
.activity-center {
  margin: 30rpx 0;
  padding-bottom: 20rpx;
}

/* 模块标题区 */
.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.title-container {
  display: flex;
  align-items: center;
}

.title-bar {
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(to bottom, #007aff, #5ac8fa);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.activity-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.activity-badge {
  margin-left: 16rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 20rpx;
}

.badge-text {
  font-size: 22rpx;
  color: #007aff;
}

.more-btn {
  display: flex;
  align-items: center;
  padding: 10rpx;
}

.more-text {
  font-size: 28rpx;
  color: #8e8e93;
}

.more-icon {
  margin-left: 4rpx;
  color: #8e8e93;
}

/* 标签导航栏 */
.activity-tabs-container {
  margin-bottom: 20rpx;
}

.activity-tabs {
  white-space: nowrap;
  padding: 0 20rpx;
}

.activity-tab {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 6rpx;
}

.activity-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: -1;
  transition: all 0.3s ease;
  opacity: 0;
  transform: scale(0.9);
}

.activity-tab.active::before {
  background: linear-gradient(35deg, rgba(0, 122, 255, 0.12), rgba(90, 200, 250, 0.08));
  opacity: 1;
  transform: scale(1);
}

.tab-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.tab-icon {
  margin-bottom: 8rpx;
  color: #8e8e93;
  transition: all 0.3s ease;
  transform: translateY(0);
}

.activity-tab.active .tab-icon {
  color: #007aff;
  transform: translateY(-2rpx);
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 122, 255, 0.3));
}

.tab-text {
  font-size: 28rpx;
  color: #8e8e93;
  transition: all 0.3s ease;
  position: relative;
}

.activity-tab.active .tab-text {
  color: #007aff;
  font-weight: 600;
  text-shadow: 0 0 0.5px rgba(0, 122, 255, 0.3);
}

.tab-line {
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #007aff, #5ac8fa);
  border-radius: 4rpx;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 1rpx 3rpx rgba(0, 122, 255, 0.3);
}

.activity-tab.active .tab-line {
  width: 60rpx;
}

.tab-glow {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 40rpx;
  background: radial-gradient(ellipse at center, rgba(0, 122, 255, 0.2) 0%, rgba(0, 122, 255, 0) 70%);
  border-radius: 50%;
  z-index: 1;
  opacity: 0.7;
  filter: blur(4rpx);
}

/* 活动列表 */
.activity-scroll-container {
  position: relative;
  width: 100%;
  padding-bottom: 30rpx;
}

.activity-scroll {
  width: 100%;
  overflow: hidden;
}

.activity-list {
  display: flex;
  padding: 10rpx 0;
  box-sizing: border-box;
}

.activity-item-placeholder {
  flex-shrink: 0;
  height: 1px;
}

.activity-item {
  width: 600rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
  transition: all 0.3s ease;
  will-change: transform, opacity;
}

.activity-item:last-child {
  margin-right: 20rpx; /* 确保最后一个卡片也有右边距 */
}

/* 轮播控制按钮 */
.carousel-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 10rpx;
  transform: translateY(-50%);
  z-index: 10;
  pointer-events: none;
}

.control-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  color: #333333;
  transition: all 0.3s ease;
  pointer-events: auto;
  opacity: 0.7;
}

.control-btn:active {
  transform: scale(0.9);
  background-color: rgba(255, 255, 255, 0.9);
}

.control-btn.disabled {
  opacity: 0.3;
  pointer-events: none;
}

.control-btn:hover {
  opacity: 1;
}

/* 轮播指示器 */
.carousel-indicators {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
}

.indicator-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: rgba(142, 142, 147, 0.3);
  margin: 0 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.indicator-dot.active {
  width: 24rpx;
  background-color: #007aff;
}

/* 媒体查询 - 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
  .activity-item {
    width: 550rpx;
  }
}

@media screen and (min-width: 768px) {
  .activity-item {
    width: 650rpx;
  }
}
</style> 