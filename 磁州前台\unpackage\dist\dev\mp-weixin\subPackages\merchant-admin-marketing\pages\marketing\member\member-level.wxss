
/* 会员等级页面样式开始 */
.member-level-container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.title-section {
  display: flex;
  flex-direction: column;
}
.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.page-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}
.add-level-btn {
  display: flex;
  align-items: center;
  background-color: #6200ee;
  color: #fff;
  padding: 16rpx 30rpx;
  border-radius: 50rpx;
}
.btn-text {
  font-size: 28rpx;
}
.btn-icon {
  font-size: 32rpx;
  margin-left: 8rpx;
}
.level-list {
  margin-top: 20rpx;
}
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.level-cards {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.level-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.level-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  color: #fff;
}
.level-name {
  font-size: 32rpx;
  font-weight: bold;
}
.level-actions {
  display: flex;
  gap: 20rpx;
}
.action-btn {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
}
.level-card-body {
  padding: 20rpx 30rpx;
}
.level-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.info-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.level-icon {
  width: 60rpx;
  height: 60rpx;
}

/* 弹窗样式 */
.level-form-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}
.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.popup-close {
  font-size: 40rpx;
  color: #999;
}
.popup-body {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}
.form-item {
  margin-bottom: 24rpx;
}
.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}
.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid transparent;
}
.color-option.active {
  border-color: #333;
  transform: scale(1.1);
}
.icon-upload {
  display: flex;
  justify-content: center;
  align-items: center;
}
.preview-icon {
  width: 100rpx;
  height: 100rpx;
}
.upload-btn {
  width: 100rpx;
  height: 100rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.upload-icon {
  font-size: 40rpx;
  color: #999;
}
.upload-text {
  font-size: 20rpx;
  color: #999;
  margin-top: 6rpx;
}
.popup-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}
.cancel-btn, .confirm-btn {
  flex: 1;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}
.cancel-btn {
  color: #666;
  background-color: #f5f5f5;
}
.confirm-btn {
  color: #fff;
  background-color: #6200ee;
}
/* 会员等级页面样式结束 */
