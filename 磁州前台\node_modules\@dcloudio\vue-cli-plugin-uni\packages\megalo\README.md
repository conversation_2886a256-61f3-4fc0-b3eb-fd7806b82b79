<p align="center">
  <a href="https://codecov.io/gh/kaola-fed/megalo">
    <img src="https://img.shields.io/npm/v/megalo.svg?style=for-the-badge" />
  </a>

  <a href="https://travis-ci.org/kaola-fed/megalo">
    <img src="https://img.shields.io/travis-ci/kaola-fed/megalo.svg?branch=feature_megalo&style=for-the-badge">
  </a>

  <a href="https://codecov.io/gh/kaola-fed/megalo">
    <img src="https://img.shields.io/codecov/c/github/kaola-fed/megalo.svg?style=for-the-badge" />
  </a>
</p>

## Megalo

**Megalo** 是基于 Vue（`Vue@2.5.16`） 的小程序开发框架，让开发者可以用 Vue 的开发方式开发小程序应用。**Megalo** 是为了跨 H5 和小程序两端的应用提供一个高效的解决方案，只需要少量改动即可完成 H5 和小程序之间的代码迁移。

**Megalo** 目前支持**微信小程序**，**支付宝小程序**，**百度智能小程序**。

## 配套设施

- [文档](https://megalojs.org)
- [magalo-aot](https://github.com/kaola-fed/megalo-aot)
- [demo](https://github.com/kaola-fed/megalo-demo)
- [工程实例](https://github.com/kaola-fed/megalo-examples)

## 支持
  - megalo 经公司内部大项目验证，可在你的公司或个人项目中使用
  - 如果你觉得还不错，请点下「star」以表支持
  - 任何技术问题均可在交流群内讨论
  
  <img alt="Join the chat at dingtalk" src="https://user-images.githubusercontent.com/20720117/47690767-450cbd00-dc2a-11e8-9c59-2547341e0add.jpeg" width="240"/> <img alt="Join the chat at wechat" src="https://user-images.githubusercontent.com/20720117/47761677-4c989880-dcf4-11e8-8586-bcc79e134e51.png" width="240"/>

## 灵感来源

名字来源于动画 `Megalo Box`。项目启发自 `mpvue`。

<p align="center"><img src="https://haitao.nos.netease.com/222d2a49-b9fe-4d95-aa61-074d910f0087.jpg"></p>
