"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "category",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const selectServiceType = (name, type) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/home-service-list?subType=${type}&subName=${encodeURIComponent(name)}`
      });
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(navigateBack),
        b: statusBarHeight.value + "px",
        c: common_assets._imports_0$26,
        d: common_vendor.o(($event) => selectServiceType("家政服务", "home_cleaning")),
        e: common_assets._imports_1$29,
        f: common_vendor.o(($event) => selectServiceType("维修改造", "repair")),
        g: common_assets._imports_2$25,
        h: common_vendor.o(($event) => selectServiceType("上门安装", "installation")),
        i: common_assets._imports_3$22,
        j: common_vendor.o(($event) => selectServiceType("开锁换锁", "locksmith")),
        k: common_assets._imports_4$17,
        l: common_vendor.o(($event) => selectServiceType("搬家拉货", "moving")),
        m: common_assets._imports_5$14,
        n: common_vendor.o(($event) => selectServiceType("上门美容", "beauty")),
        o: common_assets._imports_6$12,
        p: common_vendor.o(($event) => selectServiceType("上门家教", "tutor")),
        q: common_assets._imports_7$7,
        r: common_vendor.o(($event) => selectServiceType("宠物服务", "pet_service")),
        s: common_assets._imports_8$3,
        t: common_vendor.o(($event) => selectServiceType("上门疏通", "plumbing")),
        v: common_assets._imports_9$5,
        w: common_vendor.o(($event) => selectServiceType("其他类型", "other"))
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/service/pages/category.js.map
