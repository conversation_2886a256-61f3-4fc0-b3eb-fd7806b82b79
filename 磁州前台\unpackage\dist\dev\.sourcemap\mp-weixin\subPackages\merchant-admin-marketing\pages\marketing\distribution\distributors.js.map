{"version": 3, "file": "distributors.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25cZGlzdHJpYnV0b3JzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"distributors-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">分销员管理</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 搜索筛选区 -->\n    <view class=\"search-filter\">\n      <view class=\"search-bar\">\n        <view class=\"search-input-wrap\">\n          <view class=\"search-icon\"></view>\n          <input \n            class=\"search-input\" \n            type=\"text\" \n            v-model=\"searchParams.keyword\" \n            placeholder=\"搜索分销员姓名/手机号\" \n            confirm-type=\"search\"\n            @confirm=\"searchDistributors\"\n          />\n          <view class=\"clear-icon\" v-if=\"searchParams.keyword\" @click=\"clearSearch\"></view>\n        </view>\n        <view class=\"search-btn\" @click=\"searchDistributors\">搜索</view>\n      </view>\n      \n      <view class=\"filter-options\">\n        <view class=\"filter-item\">\n          <picker \n            mode=\"selector\" \n            :range=\"statusOptions\" \n            range-key=\"name\"\n            :value=\"statusIndex\"\n            @change=\"onStatusChange\"\n          >\n            <view class=\"picker-value\">\n              <text>{{statusOptions[statusIndex].name}}</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </picker>\n        </view>\n        \n        <view class=\"filter-item\">\n          <picker \n            mode=\"selector\" \n            :range=\"levelOptions\" \n            range-key=\"name\"\n            :value=\"levelIndex\"\n            @change=\"onLevelChange\"\n          >\n            <view class=\"picker-value\">\n              <text>{{levelOptions[levelIndex].name}}</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </picker>\n        </view>\n        \n        <view class=\"filter-item\">\n          <picker \n            mode=\"selector\" \n            :range=\"sortOptions\" \n            range-key=\"name\"\n            :value=\"sortIndex\"\n            @change=\"onSortChange\"\n          >\n            <view class=\"picker-value\">\n              <text>{{sortOptions[sortIndex].name}}</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </picker>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-area\">\n      <!-- 分销员列表 -->\n      <view class=\"distributors-list\" v-if=\"distributors.length > 0\">\n        <view \n          v-for=\"(item, index) in distributors\" \n          :key=\"index\" \n          class=\"distributor-card\"\n        >\n          <view class=\"distributor-header\">\n            <view class=\"distributor-info\">\n              <image class=\"avatar\" :src=\"item.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\n              <view class=\"info-content\">\n                <view class=\"name-wrap\">\n                  <text class=\"name\">{{item.name}}</text>\n                  <text class=\"level-tag\" :style=\"{ backgroundColor: item.levelColor || '#67C23A' }\">{{item.levelName}}</text>\n                </view>\n                <text class=\"phone\">{{item.phone}}</text>\n              </view>\n            </view>\n            <view class=\"status-tag\" :class=\"getStatusClass(item.status)\">\n              {{getStatusText(item.status)}}\n            </view>\n          </view>\n          \n          <view class=\"distributor-stats\">\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">{{item.orderCount || 0}}</text>\n              <text class=\"stat-label\">推广订单</text>\n            </view>\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">¥{{formatAmount(item.commissionTotal)}}</text>\n              <text class=\"stat-label\">累计佣金</text>\n            </view>\n            <view class=\"stat-item\">\n              <text class=\"stat-value\">{{item.teamCount || 0}}</text>\n              <text class=\"stat-label\">团队人数</text>\n            </view>\n          </view>\n          \n          <view class=\"distributor-footer\">\n            <view class=\"time\">注册时间：{{formatDate(item.createdAt)}}</view>\n            <view class=\"actions\">\n              <view class=\"action-btn detail\" @click=\"viewDetail(item)\">详情</view>\n              \n              <block v-if=\"item.status === 'pending'\">\n                <view class=\"action-btn approve\" @click=\"approveDistributor(item)\">通过</view>\n                <view class=\"action-btn reject\" @click=\"rejectDistributor(item)\">拒绝</view>\n              </block>\n              \n              <block v-else-if=\"item.status === 'active'\">\n                <view class=\"action-btn disable\" @click=\"disableDistributor(item)\">禁用</view>\n                <view class=\"action-btn set-level\" @click=\"setLevel(item)\">设置等级</view>\n              </block>\n              \n              <block v-else-if=\"item.status === 'disabled'\">\n                <view class=\"action-btn enable\" @click=\"enableDistributor(item)\">启用</view>\n              </block>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-else-if=\"!loading\">\n        <image class=\"empty-image\" src=\"/static/images/empty-data.png\" mode=\"aspectFit\"></image>\n        <text class=\"empty-text\">暂无分销员数据</text>\n      </view>\n      \n      <!-- 加载中 -->\n      <view class=\"loading-state\" v-if=\"loading\">\n        <view class=\"loading-icon\"></view>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n      \n      <!-- 分页 -->\n      <view class=\"pagination\" v-if=\"distributors.length > 0 && !loading\">\n        <view class=\"page-info\">\n          <text>共 {{pagination.total}} 条记录，当前 {{pagination.current}}/{{pagination.totalPages}} 页</text>\n        </view>\n        <view class=\"page-actions\">\n          <view \n            class=\"page-btn prev\" \n            :class=\"{ 'disabled': pagination.current <= 1 }\"\n            @click=\"prevPage\"\n          >上一页</view>\n          <view \n            class=\"page-btn next\" \n            :class=\"{ 'disabled': pagination.current >= pagination.totalPages }\"\n            @click=\"nextPage\"\n          >下一页</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 设置等级弹窗 -->\n    <view class=\"level-modal\" v-if=\"showLevelModal\">\n      <view class=\"modal-mask\" @click=\"closeLevelModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">设置分销员等级</text>\n          <view class=\"close-icon\" @click=\"closeLevelModal\"></view>\n        </view>\n        \n        <view class=\"modal-distributor\">\n          <image class=\"avatar\" :src=\"selectedDistributor.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\n          <text class=\"name\">{{selectedDistributor.name}}</text>\n        </view>\n        \n        <view class=\"level-list\">\n          <view \n            v-for=\"(level, idx) in levelOptions.slice(1)\" \n            :key=\"idx\" \n            class=\"level-item-select\"\n            :class=\"{ 'active': selectedLevel === level.value }\"\n            @click=\"selectedLevel = level.value\"\n          >\n            <view class=\"level-radio\">\n              <view class=\"radio-inner\" v-if=\"selectedLevel === level.value\"></view>\n            </view>\n            <text class=\"level-name\">{{level.name}}</text>\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <button class=\"cancel-btn\" @click=\"closeLevelModal\">取消</button>\n          <button class=\"submit-btn\" @click=\"confirmSetLevel\">确定</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport distributionService from '@/utils/distributionService';\n\n// 搜索参数\nconst searchParams = reactive({\n  keyword: '',\n  status: 'all',\n  level: 'all',\n  sortBy: 'created_at',\n  sortOrder: 'desc',\n  page: 1,\n  pageSize: 10\n});\n\n// 状态选项\nconst statusOptions = [\n  { value: 'all', name: '全部状态' },\n  { value: 'active', name: '已启用' },\n  { value: 'disabled', name: '已禁用' },\n  { value: 'pending', name: '待审核' },\n  { value: 'rejected', name: '已拒绝' }\n];\n\n// 等级选项\nconst levelOptions = [\n  { value: 'all', name: '全部等级' },\n  { value: '1', name: '普通分销员' },\n  { value: '2', name: '高级分销员' },\n  { value: '3', name: '金牌分销员' }\n];\n\n// 排序选项\nconst sortOptions = [\n  { value: { field: 'created_at', order: 'desc' }, name: '注册时间降序' },\n  { value: { field: 'created_at', order: 'asc' }, name: '注册时间升序' },\n  { value: { field: 'commission_total', order: 'desc' }, name: '佣金总额降序' },\n  { value: { field: 'commission_total', order: 'asc' }, name: '佣金总额升序' },\n  { value: { field: 'order_count', order: 'desc' }, name: '订单数量降序' },\n  { value: { field: 'order_count', order: 'asc' }, name: '订单数量升序' }\n];\n\n// 当前选中的状态索引\nconst statusIndex = ref(0);\n\n// 当前选中的等级索引\nconst levelIndex = ref(0);\n\n// 当前选中的排序索引\nconst sortIndex = ref(0);\n\n// 分销员列表\nconst distributors = ref([]);\n\n// 分页信息\nconst pagination = reactive({\n  current: 1,\n  pageSize: 10,\n  total: 0,\n  totalPages: 0\n});\n\n// 加载状态\nconst loading = ref(false);\n\n// 等级设置弹窗\nconst showLevelModal = ref(false);\n\n// 选中的分销员\nconst selectedDistributor = ref({});\n\n// 选中的等级\nconst selectedLevel = ref('');\n\n// 页面加载\nonMounted(async () => {\n  // 获取分销等级列表\n  await getDistributionLevels();\n  \n  // 获取分销员列表\n  await getDistributorsList();\n});\n\n// 获取分销等级列表\nconst getDistributionLevels = async () => {\n  try {\n    const result = await distributionService.getDistributionLevels();\n    \n    if (result && result.levels && result.levels.length > 0) {\n      // 更新等级选项\n      levelOptions.length = 1; // 保留\"全部等级\"选项\n      \n      result.levels.forEach(level => {\n        levelOptions.push({\n          value: level.id.toString(),\n          name: level.name\n        });\n      });\n    }\n  } catch (error) {\n    console.error('获取分销等级列表失败', error);\n  }\n};\n\n// 获取分销员列表\nconst getDistributorsList = async (loadMore = false) => {\n  try {\n    loading.value = true;\n    \n    const params = {\n      ...searchParams,\n      page: searchParams.page,\n      pageSize: searchParams.pageSize\n    };\n    \n    const result = await distributionService.getDistributorsList(params);\n    \n    if (result) {\n      distributors.value = result.list || [];\n      \n      // 更新分页信息\n      pagination.current = result.pagination.current;\n      pagination.total = result.pagination.total;\n      pagination.totalPages = result.pagination.totalPages;\n    }\n  } catch (error) {\n    console.error('获取分销员列表失败', error);\n    uni.showToast({\n      title: '获取分销员列表失败',\n      icon: 'none'\n    });\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 搜索分销员\nconst searchDistributors = () => {\n  searchParams.page = 1;\n  getDistributorsList();\n};\n\n// 清除搜索\nconst clearSearch = () => {\n  searchParams.keyword = '';\n  searchDistributors();\n};\n\n// 状态变化\nconst onStatusChange = (e) => {\n  const index = e.detail.value;\n  statusIndex.value = index;\n  searchParams.status = statusOptions[index].value;\n  searchDistributors();\n};\n\n// 等级变化\nconst onLevelChange = (e) => {\n  const index = e.detail.value;\n  levelIndex.value = index;\n  searchParams.level = levelOptions[index].value;\n  searchDistributors();\n};\n\n// 排序变化\nconst onSortChange = (e) => {\n  const index = e.detail.value;\n  sortIndex.value = index;\n  const sortOption = sortOptions[index].value;\n  searchParams.sortBy = sortOption.field;\n  searchParams.sortOrder = sortOption.order;\n  searchDistributors();\n};\n\n// 返回上一页\nconst goBack = () => {\n      uni.navigateBack();\n};\n\n// 显示帮助\nconst showHelp = () => {\n  uni.showModal({\n    title: '分销员管理帮助',\n    content: '在此页面您可以查看和管理所有分销员，包括审核申请、设置等级、启用或禁用分销员等操作。',\n    showCancel: false\n  });\n};\n\n// 格式化金额\nconst formatAmount = (amount) => {\n  return (amount || 0).toFixed(2);\n};\n\n// 格式化日期\nconst formatDate = (dateString) => {\n  if (!dateString) return '';\n  \n  const date = new Date(dateString);\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  \n  return `${year}-${month}-${day}`;\n};\n\n// 获取状态样式类\nconst getStatusClass = (status) => {\n  switch (status) {\n    case 'active':\n      return 'status-active';\n    case 'disabled':\n      return 'status-disabled';\n    case 'pending':\n      return 'status-pending';\n    case 'rejected':\n      return 'status-rejected';\n    default:\n      return '';\n  }\n};\n\n// 获取状态文本\nconst getStatusText = (status) => {\n  switch (status) {\n    case 'active':\n      return '已启用';\n    case 'disabled':\n      return '已禁用';\n    case 'pending':\n      return '待审核';\n    case 'rejected':\n      return '已拒绝';\n    default:\n      return '未知';\n  }\n};\n\n// 查看详情\nconst viewDetail = (item) => {\n  uni.navigateTo({\n    url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${item.id}`\n  });\n};\n\n// 审核通过\nconst approveDistributor = (item) => {\n  uni.showModal({\n    title: '审核通过',\n    content: `确定通过 ${item.name} 的分销员申请吗？`,\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.reviewDistributorApplication({\n            id: item.id,\n            status: 'approved'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '审核通过成功',\n              icon: 'success'\n            });\n            \n            // 刷新列表\n            getDistributorsList();\n          } else {\n            uni.showModal({\n              title: '审核失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('审核失败', error);\n          uni.showToast({\n            title: '审核失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 拒绝申请\nconst rejectDistributor = (item) => {\n  uni.showModal({\n    title: '拒绝申请',\n    content: `确定拒绝 ${item.name} 的分销员申请吗？`,\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.reviewDistributorApplication({\n            id: item.id,\n            status: 'rejected'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '已拒绝申请',\n              icon: 'success'\n            });\n            \n            // 刷新列表\n            getDistributorsList();\n          } else {\n            uni.showModal({\n              title: '操作失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('操作失败', error);\n          uni.showToast({\n            title: '操作失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 禁用分销员\nconst disableDistributor = (item) => {\n  uni.showModal({\n    title: '禁用分销员',\n    content: `确定禁用 ${item.name} 的分销员资格吗？禁用后该用户将无法进行分销活动。`,\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.toggleDistributorStatus({\n            id: item.id,\n            status: 'disabled'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '禁用成功',\n              icon: 'success'\n            });\n            \n            // 刷新列表\n            getDistributorsList();\n          } else {\n            uni.showModal({\n              title: '操作失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('操作失败', error);\n          uni.showToast({\n            title: '操作失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 启用分销员\nconst enableDistributor = (item) => {\n  uni.showModal({\n    title: '启用分销员',\n    content: `确定启用 ${item.name} 的分销员资格吗？`,\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.toggleDistributorStatus({\n            id: item.id,\n            status: 'active'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '启用成功',\n              icon: 'success'\n            });\n            \n            // 刷新列表\n            getDistributorsList();\n          } else {\n            uni.showModal({\n              title: '操作失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('操作失败', error);\n          uni.showToast({\n            title: '操作失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 设置等级\nconst setLevel = (item) => {\n  selectedDistributor.value = item;\n  selectedLevel.value = item.levelId ? item.levelId.toString() : '1';\n  showLevelModal.value = true;\n};\n\n// 关闭等级弹窗\nconst closeLevelModal = () => {\n  showLevelModal.value = false;\n};\n\n// 确认设置等级\nconst confirmSetLevel = async () => {\n  try {\n    uni.showLoading({\n      title: '设置中...',\n      mask: true\n    });\n    \n    const result = await distributionService.setDistributorLevel({\n      id: selectedDistributor.value.id,\n      levelId: selectedLevel.value\n    });\n    \n    uni.hideLoading();\n    \n    if (result.success) {\n      uni.showToast({\n        title: '设置成功',\n        icon: 'success'\n      });\n      \n      closeLevelModal();\n      \n      // 刷新列表\n      getDistributorsList();\n    } else {\n      uni.showModal({\n        title: '设置失败',\n        content: result.message || '请稍后再试',\n        showCancel: false\n      });\n    }\n  } catch (error) {\n    uni.hideLoading();\n    console.error('设置等级失败', error);\n    uni.showToast({\n      title: '设置失败',\n      icon: 'none'\n    });\n  }\n};\n\n// 上一页\nconst prevPage = () => {\n  if (pagination.current > 1) {\n    searchParams.page = pagination.current - 1;\n    getDistributorsList();\n  }\n};\n\n// 下一页\nconst nextPage = () => {\n  if (pagination.current < pagination.totalPages) {\n    searchParams.page = pagination.current + 1;\n    getDistributorsList();\n  }\n};\n</script>\n\n<style lang=\"scss\">\n.distributors-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 88rpx 32rpx 30rpx;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 24rpx;\n  height: 24rpx;\n  border-left: 4rpx solid #fff;\n  border-bottom: 4rpx solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 36rpx;\n  font-weight: 600;\n  letter-spacing: 1rpx;\n}\n\n.navbar-right {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 24rpx;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  font-weight: bold;\n}\n\n/* 搜索筛选区 */\n.search-filter {\n  background: #FFFFFF;\n  padding: 20rpx 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.search-bar {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.search-input-wrap {\n  flex: 1;\n  height: 72rpx;\n  background: #F5F7FA;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  padding: 0 20rpx;\n  margin-right: 20rpx;\n}\n\n.search-icon {\n  width: 32rpx;\n  height: 32rpx;\n  background-color: #6B0FBE;\n  border-radius: 50%;\n  position: relative;\n  margin-right: 10rpx;\n}\n\n.search-icon::before {\n  content: '';\n  position: absolute;\n  width: 12rpx;\n  height: 12rpx;\n  border: 2rpx solid white;\n  border-radius: 50%;\n  top: 6rpx;\n  left: 6rpx;\n}\n\n.search-icon::after {\n  content: '';\n  position: absolute;\n  width: 8rpx;\n  height: 2rpx;\n  background-color: white;\n  transform: rotate(45deg);\n  bottom: 8rpx;\n  right: 6rpx;\n}\n\n.search-input {\n  flex: 1;\n  height: 100%;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.clear-icon {\n  width: 32rpx;\n  height: 32rpx;\n  background-color: #909399;\n  border-radius: 50%;\n  position: relative;\n}\n\n.clear-icon::before {\n  content: '';\n  position: absolute;\n  width: 16rpx;\n  height: 2rpx;\n  background-color: white;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%) rotate(45deg);\n}\n\n.clear-icon::after {\n  content: '';\n  position: absolute;\n  width: 16rpx;\n  height: 2rpx;\n  background-color: white;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%) rotate(-45deg);\n}\n\n.search-btn {\n  font-size: 28rpx;\n  color: #6B0FBE;\n}\n\n.filter-options {\n  display: flex;\n  justify-content: space-between;\n}\n\n.filter-item {\n  flex: 1;\n  margin: 0 10rpx;\n}\n\n.filter-item:first-child {\n  margin-left: 0;\n}\n\n.filter-item:last-child {\n  margin-right: 0;\n}\n\n.picker-value {\n  height: 72rpx;\n  background: #F5F7FA;\n  border-radius: 10rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 26rpx;\n  color: #333;\n  padding: 0 20rpx;\n}\n\n.arrow-icon {\n  width: 16rpx;\n  height: 16rpx;\n  border-right: 2rpx solid #999;\n  border-bottom: 2rpx solid #999;\n  transform: rotate(45deg);\n  margin-left: 10rpx;\n}\n\n/* 内容区域 */\n.content-area {\n  padding: 20rpx 30rpx;\n}\n\n/* 分销员列表 */\n.distributors-list {\n  margin-bottom: 30rpx;\n}\n\n.distributor-card {\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.distributor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.distributor-info {\n  display: flex;\n  align-items: center;\n}\n\n.avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 40rpx;\n  margin-right: 20rpx;\n  background-color: #f5f5f5;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.name-wrap {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.name {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-right: 16rpx;\n}\n\n.level-tag {\n  font-size: 22rpx;\n  color: #fff;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.phone {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.status-tag {\n  font-size: 24rpx;\n  color: #fff;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n}\n\n.status-active {\n  background-color: #67C23A;\n}\n\n.status-disabled {\n  background-color: #909399;\n}\n\n.status-pending {\n  background-color: #E6A23C;\n}\n\n.status-rejected {\n  background-color: #F56C6C;\n}\n\n.distributor-stats {\n  display: flex;\n  justify-content: space-between;\n  padding: 20rpx 0;\n  border-top: 1rpx solid #f0f0f0;\n  border-bottom: 1rpx solid #f0f0f0;\n  margin-bottom: 20rpx;\n}\n\n.stat-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.stat-value {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.distributor-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.actions {\n  display: flex;\n}\n\n.action-btn {\n  font-size: 24rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  margin-left: 16rpx;\n}\n\n.action-btn.detail {\n  background-color: #F5F7FA;\n  color: #333;\n}\n\n.action-btn.approve {\n  background-color: #67C23A;\n  color: #fff;\n}\n\n.action-btn.reject {\n  background-color: #F56C6C;\n  color: #fff;\n}\n\n.action-btn.disable {\n  background-color: #909399;\n  color: #fff;\n}\n\n.action-btn.enable {\n  background-color: #409EFF;\n  color: #fff;\n}\n\n.action-btn.set-level {\n  background-color: #6B0FBE;\n  color: #fff;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 加载中 */\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 50rpx 0;\n}\n\n.loading-icon {\n  width: 60rpx;\n  height: 60rpx;\n  margin-bottom: 20rpx;\n  border: 4rpx solid #f3f3f3;\n  border-top: 4rpx solid #6B0FBE;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 分页 */\n.pagination {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 30rpx;\n  padding: 20rpx;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.page-info {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.page-actions {\n  display: flex;\n}\n\n.page-btn {\n  font-size: 24rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  margin-left: 16rpx;\n  background-color: #6B0FBE;\n  color: #fff;\n}\n\n.page-btn.disabled {\n  background-color: #f5f5f5;\n  color: #999;\n}\n\n/* 等级设置弹窗 */\n.level-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n}\n\n.modal-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n}\n\n.modal-content {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 80%;\n  background-color: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.modal-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.close-icon {\n  width: 40rpx;\n  height: 40rpx;\n  position: relative;\n}\n\n.close-icon::before,\n.close-icon::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 32rpx;\n  height: 2rpx;\n  background-color: #999;\n}\n\n.close-icon::before {\n  transform: translate(-50%, -50%) rotate(45deg);\n}\n\n.close-icon::after {\n  transform: translate(-50%, -50%) rotate(-45deg);\n}\n\n.modal-distributor {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.modal-distributor .avatar {\n  width: 100rpx;\n  height: 100rpx;\n  margin-bottom: 16rpx;\n  margin-right: 0;\n}\n\n.modal-distributor .name {\n  margin-right: 0;\n}\n\n.level-list {\n  margin-bottom: 30rpx;\n}\n\n.level-item-select {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.level-item-select:last-child {\n  border-bottom: none;\n}\n\n.level-radio {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 20rpx;\n  border: 2rpx solid #ddd;\n  margin-right: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.level-item-select.active .level-radio {\n  border-color: #6B0FBE;\n}\n\n.radio-inner {\n  width: 24rpx;\n  height: 24rpx;\n  border-radius: 12rpx;\n  background-color: #6B0FBE;\n}\n\n.level-item-select .level-name {\n  font-size: 28rpx;\n  color: #333;\n  margin-right: 0;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: space-between;\n}\n\n.cancel-btn,\n.submit-btn {\n  width: 48%;\n  height: 80rpx;\n  border-radius: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n}\n\n.cancel-btn {\n  background: #F5F7FA;\n  color: #666;\n}\n\n.submit-btn {\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "onMounted", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;;AAyNA,UAAM,eAAeA,cAAAA,SAAS;AAAA,MAC5B,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AAGD,UAAM,gBAAgB;AAAA,MACpB,EAAE,OAAO,OAAO,MAAM,OAAQ;AAAA,MAC9B,EAAE,OAAO,UAAU,MAAM,MAAO;AAAA,MAChC,EAAE,OAAO,YAAY,MAAM,MAAO;AAAA,MAClC,EAAE,OAAO,WAAW,MAAM,MAAO;AAAA,MACjC,EAAE,OAAO,YAAY,MAAM,MAAO;AAAA,IACpC;AAGA,UAAM,eAAe;AAAA,MACnB,EAAE,OAAO,OAAO,MAAM,OAAQ;AAAA,MAC9B,EAAE,OAAO,KAAK,MAAM,QAAS;AAAA,MAC7B,EAAE,OAAO,KAAK,MAAM,QAAS;AAAA,MAC7B,EAAE,OAAO,KAAK,MAAM,QAAS;AAAA,IAC/B;AAGA,UAAM,cAAc;AAAA,MAClB,EAAE,OAAO,EAAE,OAAO,cAAc,OAAO,OAAQ,GAAE,MAAM,SAAU;AAAA,MACjE,EAAE,OAAO,EAAE,OAAO,cAAc,OAAO,MAAO,GAAE,MAAM,SAAU;AAAA,MAChE,EAAE,OAAO,EAAE,OAAO,oBAAoB,OAAO,OAAQ,GAAE,MAAM,SAAU;AAAA,MACvE,EAAE,OAAO,EAAE,OAAO,oBAAoB,OAAO,MAAO,GAAE,MAAM,SAAU;AAAA,MACtE,EAAE,OAAO,EAAE,OAAO,eAAe,OAAO,OAAQ,GAAE,MAAM,SAAU;AAAA,MAClE,EAAE,OAAO,EAAE,OAAO,eAAe,OAAO,MAAO,GAAE,MAAM,SAAU;AAAA,IACnE;AAGA,UAAM,cAAcC,cAAAA,IAAI,CAAC;AAGzB,UAAM,aAAaA,cAAAA,IAAI,CAAC;AAGxB,UAAM,YAAYA,cAAAA,IAAI,CAAC;AAGvB,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAG3B,UAAM,aAAaD,cAAAA,SAAS;AAAA,MAC1B,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,UAAUC,cAAAA,IAAI,KAAK;AAGzB,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAGhC,UAAM,sBAAsBA,cAAAA,IAAI,CAAA,CAAE;AAGlC,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAG5BC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,sBAAqB;AAG3B,YAAM,oBAAmB;AAAA,IAC3B,CAAC;AAGD,UAAM,wBAAwB,YAAY;AACxC,UAAI;AACF,cAAM,SAAS,MAAMC,8CAAoB;AAEzC,YAAI,UAAU,OAAO,UAAU,OAAO,OAAO,SAAS,GAAG;AAEvD,uBAAa,SAAS;AAEtB,iBAAO,OAAO,QAAQ,WAAS;AAC7B,yBAAa,KAAK;AAAA,cAChB,OAAO,MAAM,GAAG,SAAU;AAAA,cAC1B,MAAM,MAAM;AAAA,YACtB,CAAS;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,6FAAA,cAAc,KAAK;AAAA,MAClC;AAAA,IACH;AAGA,UAAM,sBAAsB,OAAO,WAAW,UAAU;AACtD,UAAI;AACF,gBAAQ,QAAQ;AAEhB,cAAM,SAAS;AAAA,UACb,GAAG;AAAA,UACH,MAAM,aAAa;AAAA,UACnB,UAAU,aAAa;AAAA,QAC7B;AAEI,cAAM,SAAS,MAAMD,0BAAAA,oBAAoB,oBAAoB,MAAM;AAEnE,YAAI,QAAQ;AACV,uBAAa,QAAQ,OAAO,QAAQ,CAAA;AAGpC,qBAAW,UAAU,OAAO,WAAW;AACvC,qBAAW,QAAQ,OAAO,WAAW;AACrC,qBAAW,aAAa,OAAO,WAAW;AAAA,QAC3C;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAA,MAAA,MAAA,SAAA,6FAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/B,mBAAa,OAAO;AACpB;IACF;AAGA,UAAM,cAAc,MAAM;AACxB,mBAAa,UAAU;AACvB;IACF;AAGA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,YAAM,QAAQ,EAAE,OAAO;AACvB,kBAAY,QAAQ;AACpB,mBAAa,SAAS,cAAc,KAAK,EAAE;AAC3C;IACF;AAGA,UAAM,gBAAgB,CAAC,MAAM;AAC3B,YAAM,QAAQ,EAAE,OAAO;AACvB,iBAAW,QAAQ;AACnB,mBAAa,QAAQ,aAAa,KAAK,EAAE;AACzC;IACF;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,YAAM,QAAQ,EAAE,OAAO;AACvB,gBAAU,QAAQ;AAClB,YAAM,aAAa,YAAY,KAAK,EAAE;AACtC,mBAAa,SAAS,WAAW;AACjC,mBAAa,YAAY,WAAW;AACpC;IACF;AAGA,UAAM,SAAS,MAAM;AACfA,oBAAG,MAAC,aAAY;AAAA,IACtB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,WAAW;AAC/B,cAAQ,UAAU,GAAG,QAAQ,CAAC;AAAA,IAChC;AAGA,UAAM,aAAa,CAAC,eAAe;AACjC,UAAI,CAAC;AAAY,eAAO;AAExB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAElD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4FAA4F,KAAK,EAAE;AAAA,MAC5G,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,CAAC,SAAS;AACnCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,KAAK,IAAI;AAAA,QAC1B,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMD,0BAAmB,oBAAC,6BAA6B;AAAA,gBACpE,IAAI,KAAK;AAAA,gBACT,QAAQ;AAAA,cACpB,CAAW;AAEDC,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,6FAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,KAAK,IAAI;AAAA,QAC1B,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMD,0BAAmB,oBAAC,6BAA6B;AAAA,gBACpE,IAAI,KAAK;AAAA,gBACT,QAAQ;AAAA,cACpB,CAAW;AAEDC,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,6FAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,CAAC,SAAS;AACnCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,KAAK,IAAI;AAAA,QAC1B,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMD,0BAAmB,oBAAC,wBAAwB;AAAA,gBAC/D,IAAI,KAAK;AAAA,gBACT,QAAQ;AAAA,cACpB,CAAW;AAEDC,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,6FAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,KAAK,IAAI;AAAA,QAC1B,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMD,0BAAmB,oBAAC,wBAAwB;AAAA,gBAC/D,IAAI,KAAK;AAAA,gBACT,QAAQ;AAAA,cACpB,CAAW;AAEDC,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,6FAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,CAAC,SAAS;AACzB,0BAAoB,QAAQ;AAC5B,oBAAc,QAAQ,KAAK,UAAU,KAAK,QAAQ,SAAU,IAAG;AAC/D,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,kBAAkB,YAAY;AAClC,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,SAAS,MAAMD,0BAAmB,oBAAC,oBAAoB;AAAA,UAC3D,IAAI,oBAAoB,MAAM;AAAA,UAC9B,SAAS,cAAc;AAAA,QAC7B,CAAK;AAEDC,sBAAG,MAAC,YAAW;AAEf,YAAI,OAAO,SAAS;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAED;AAGA;QACN,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,OAAO,WAAW;AAAA,YAC3B,YAAY;AAAA,UACpB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,6FAAc,UAAU,KAAK;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,WAAW,UAAU,GAAG;AAC1B,qBAAa,OAAO,WAAW,UAAU;AACzC;MACD;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,WAAW,UAAU,WAAW,YAAY;AAC9C,qBAAa,OAAO,WAAW,UAAU;AACzC;MACD;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3sBA,GAAG,WAAWC,SAAe;"}