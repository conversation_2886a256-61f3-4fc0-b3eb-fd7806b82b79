/**
 * 阿里云短信服务API封装
 */
import { uniCloud } from '@dcloudio/uni-app';

// 阿里云短信配置信息 - 实际项目中应将这些配置存储在云函数环境变量中而非前端代码中
const SMS_CONFIG = {
  accessKeyId: 'YOUR_ACCESS_KEY_ID', // 替换为实际的accessKeyId
  accessKeySecret: 'YOUR_ACCESS_KEY_SECRET', // 替换为实际的accessKeySecret
  endpoint: 'https://dysmsapi.aliyuncs.com',
  apiVersion: '2017-05-25',
  signName: '同城生活网', // 替换为您的签名
  templateCode: 'SMS_123456789', // 替换为您的模板CODE
  regionId: 'cn-hangzhou'
};

// 生成6位随机验证码
export const generateVerifyCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 验证码缓存，实际项目中应该存储在数据库或Redis中
const codeCache = new Map();

/**
 * 通过云函数发送短信验证码
 * @param {String} phone 手机号
 * @returns {Promise} 发送结果
 */
export const sendSmsCode = async (phone) => {
  if (!phone) {
    return Promise.reject(new Error('手机号不能为空'));
  }
  
  // 生成验证码
  const code = generateVerifyCode();
  
  try {
    // 调用云函数发送短信，避免将密钥暴露在前端
    const result = await uniCloud.callFunction({
      name: 'sendSms',
      data: {
        phone,
        code,
        templateParam: JSON.stringify({ code })
      }
    });
    
    if (result.result && result.result.success) {
      // 缓存验证码，设置5分钟过期
      codeCache.set(phone, {
        code,
        expireTime: Date.now() + 5 * 60 * 1000 // 5分钟过期
      });
      
      console.log('验证码发送成功:', phone, code);
      return Promise.resolve({
        success: true,
        message: '验证码发送成功'
      });
    } else {
      return Promise.reject(new Error(result.result?.message || '发送失败'));
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    return Promise.reject(new Error('验证码发送失败，请稍后重试'));
  }
};

/**
 * 验证用户输入的验证码是否正确
 * @param {String} phone 手机号
 * @param {String} code 用户输入的验证码
 * @returns {Boolean} 验证结果
 */
export const verifyCode = (phone, code) => {
  // 获取缓存中的验证码信息
  const cacheInfo = codeCache.get(phone);
  
  // 如果没有缓存或已过期，返回false
  if (!cacheInfo || Date.now() > cacheInfo.expireTime) {
    return false;
  }
  
  // 比较用户输入的验证码和缓存中的验证码
  return cacheInfo.code === code;
};

/**
 * 模拟短信发送过程，用于开发阶段测试
 * @param {String} phone 手机号
 * @returns {Promise} 发送结果
 */
export const mockSendSmsCode = (phone) => {
  return new Promise((resolve) => {
    // 生成验证码
    const code = generateVerifyCode();
    
    // 缓存验证码，设置5分钟过期
    codeCache.set(phone, {
      code,
      expireTime: Date.now() + 5 * 60 * 1000 // 5分钟过期
    });
    
    console.log('模拟发送验证码成功:', phone, code);
    setTimeout(() => {
      resolve({
        success: true,
        message: '验证码发送成功',
        code // 仅用于测试，实际环境不应返回验证码
      });
    }, 500);
  });
};

/**
 * 判断是否处于开发环境，决定使用模拟还是真实API
 */
export const sendVerifyCode = async (phone) => {
  // 判断是否是开发环境
  const isDev = process.env.NODE_ENV === 'development';
  
  if (isDev) {
    // 开发环境使用模拟发送
    return mockSendSmsCode(phone);
  } else {
    // 生产环境使用真实API
    return sendSmsCode(phone);
  }
};

export default {
  sendVerifyCode,
  verifyCode,
  generateVerifyCode
}; 