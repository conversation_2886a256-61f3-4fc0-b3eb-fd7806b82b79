{"version": 3, "file": "redirect-fix.js", "sources": ["pages/redirect-fix.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcmVkaXJlY3QtZml4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"redirect-container\">\n    <text>加载中...</text>\n  </view>\n</template>\n\n<script>\nexport default {\n  onLoad(options) {\n    // 获取所有传递过来的参数\n    console.log('接收到的跳转参数:', JSON.stringify(options));\n    \n    const params = Object.keys(options)\n      .map(key => `${key}=${encodeURIComponent(options[key])}`)\n      .join('&');\n    \n    // 获取页面类型\n    const pageType = options.pageType || 'info-detail';\n    \n    // 直接跳转，不使用setTimeout\n    // 尝试使用原始的详情页路径\n    let url = `/pages/publish/${pageType}?${params}`;\n    console.log('尝试跳转到原始页面:', url);\n    \n    uni.navigateTo({\n      url: url,\n      success: () => {\n        console.log('跳转成功');\n      },\n      fail: (err) => {\n        console.error('跳转原始页面失败', err);\n        \n        // 返回首页并显示错误提示\n        uni.showToast({\n          title: '页面跳转失败，请稍后重试',\n          icon: 'none',\n          duration: 1000\n        });\n      }\n    });\n  }\n}\n</script>\n\n<style>\n.redirect-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/redirect-fix.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAOA,MAAK,YAAU;AAAA,EACb,OAAO,SAAS;AAEdA,qEAAY,aAAa,KAAK,UAAU,OAAO,CAAC;AAEhD,UAAM,SAAS,OAAO,KAAK,OAAO,EAC/B,IAAI,SAAO,GAAG,GAAG,IAAI,mBAAmB,QAAQ,GAAG,CAAC,CAAC,EAAE,EACvD,KAAK,GAAG;AAGX,UAAM,WAAW,QAAQ,YAAY;AAIrC,QAAI,MAAM,kBAAkB,QAAQ,IAAI,MAAM;AAC9CA,kBAAA,MAAA,MAAA,OAAA,gCAAY,cAAc,GAAG;AAE7BA,kBAAAA,MAAI,WAAW;AAAA,MACb;AAAA,MACA,SAAS,MAAM;AACbA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,MAAM;AAAA,MACnB;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAA,MAAA,MAAA,SAAA,gCAAc,YAAY,GAAG;AAG7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;;;ACxCA,GAAG,WAAW,eAAe;"}