// SCSS 变量定义

// 颜色变量
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 文本颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #c0c4cc;

// 边框颜色
$border-color-base: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// 背景颜色
$bg-color-base: #f5f7fa;
$bg-color-light: #fafafa;
$bg-color-lighter: #ffffff;

// 字体大小
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 字体粗细
$font-weight-primary: 500;
$font-weight-secondary: 400;

// 行高
$line-height-primary: 24px;
$line-height-base: 1.5;

// 边框圆角
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-round: 20px;
$border-radius-circle: 100%;

// 盒子阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 间距
$spacing-base: 16px;
$spacing-large: 24px;
$spacing-medium: 16px;
$spacing-small: 12px;
$spacing-mini: 8px;

// 布局
$header-height: 60px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;
$footer-height: 50px;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;

// z-index
$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;
$z-index-message: 3000;
$z-index-loading: 4000;

// 过渡动画
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-md-fade: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-border: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-color: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

// 表格
$table-border-color: #ebeef5;
$table-header-bg-color: #fafafa;
$table-row-hover-bg-color: #f5f7fa;

// 表单
$form-label-color: #606266;
$form-border-color: #dcdfe6;
$form-border-color-hover: #c0c4cc;
$form-border-color-focus: #409eff;

// 按钮
$button-border-radius: 4px;
$button-font-weight: 500;

// 输入框
$input-border-radius: 4px;
$input-border-color: #dcdfe6;
$input-border-color-hover: #c0c4cc;
$input-border-color-focus: #409eff;

// 卡片
$card-border-radius: 8px;
$card-border-color: #ebeef5;
$card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

// 对话框
$dialog-border-radius: 8px;
$dialog-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

// 标签
$tag-border-radius: 4px;

// 分页
$pagination-button-color: #606266;
$pagination-button-disabled-color: #c0c4cc;
$pagination-hover-color: #409eff;

// 菜单
$menu-item-height: 56px;
$menu-item-font-size: 14px;
$menu-bg-color: #ffffff;
$menu-text-color: #303133;
$menu-active-color: #409eff;
$menu-hover-bg-color: #f5f7fa;

// 侧边栏
$sidebar-bg-color: #ffffff;
$sidebar-text-color: #303133;
$sidebar-active-color: #409eff;
$sidebar-hover-bg-color: #f5f7fa;

// 头部
$header-bg-color: #ffffff;
$header-text-color: #303133;
$header-border-color: #ebeef5;

// 面包屑
$breadcrumb-font-size: 14px;
$breadcrumb-separator-color: #c0c4cc;

// 标签页
$tabs-header-height: 40px;
$tabs-item-height: 40px;

// 加载
$loading-spinner-size: 42px;
$loading-text-color: #409eff;

// 空状态
$empty-padding: 40px 0;
$empty-image-width: 160px;
$empty-description-margin-top: 16px;

// 结果页
$result-padding: 48px 32px;
$result-icon-font-size: 72px;
$result-title-font-size: 24px;
$result-subtitle-margin-top: 8px;
$result-extra-margin-top: 24px;

// 统计数值
$statistic-title-font-size: 14px;
$statistic-content-font-size: 24px;
$statistic-content-font-weight: 600;
