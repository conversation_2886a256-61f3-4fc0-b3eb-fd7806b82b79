/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.coupon-create-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px;
  /* 为底部操作栏留出空间 */
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 表单容器样式 */
.form-container {
  height: calc(100vh - 80px - 44px - 15px - 15px);
}

/* 优惠券类型选择样式 */
.type-selection-section {
  margin: 15px;
  display: flex;
  flex-wrap: wrap;
}
.type-option {
  width: calc(33.33% - 10px);
  margin: 0 5px 10px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transition: all 0.3s;
}
.type-option.active {
  border: 2px solid #FF5E62;
  padding: 13px;
}
.type-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  color: #fff;
}
.bg-discount {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}
.bg-percent {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}
.bg-free {
  background: linear-gradient(135deg, #11998e, #38ef7d);
}
.type-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.type-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
  line-height: 1.3;
}
.type-check {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  color: #FF5E62;
}

/* 表单部分样式 */
.form-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.section-header {
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.section-subtitle {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
.form-item {
  margin-bottom: 15px;
}
.form-item:last-child {
  margin-bottom: 0;
}
.item-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}
.item-input {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}
.amount-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 44px;
}
.amount-symbol {
  font-size: 14px;
  color: #333;
  margin-right: 5px;
}
.amount-input {
  flex: 1;
  height: 44px;
  font-size: 14px;
  color: #333;
  background: transparent;
}
.amount-unit {
  font-size: 14px;
  color: #666;
}
.threshold-text {
  font-size: 14px;
  color: #666;
  margin: 0 5px;
}
.validity-type-selector {
  display: flex;
}
.type-option {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
.option-radio {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  border: 1px solid #ddd;
  margin-right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.radio-inner {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background: #FF5E62;
}
.option-text {
  font-size: 14px;
  color: #333;
}
.type-option.active .option-text {
  color: #FF5E62;
}
.date-selector {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.date-value {
  font-size: 14px;
  color: #333;
}
.date-icon {
  width: 20px;
  height: 20px;
  color: #999;
}
.days-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 44px;
}
.days-input {
  flex: 1;
  height: 44px;
  font-size: 14px;
  color: #333;
  background: transparent;
}
.days-text {
  font-size: 14px;
  color: #666;
}
.product-selector {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.selector-value {
  font-size: 14px;
  color: #333;
}
.selector-arrow {
  width: 10px;
  height: 10px;
  border-right: 2px solid #999;
  border-bottom: 2px solid #999;
  transform: rotate(45deg);
}
.item-textarea {
  width: 100%;
  height: 100px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

/* 底部操作栏样式 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  padding: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}
.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
}
.action-button.preview {
  background: #F5F7FA;
  border: 1px solid #FF5E62;
}
.action-button.create {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}
.button-text {
  font-size: 16px;
  font-weight: 500;
}
.action-button.preview .button-text {
  color: #FF5E62;
}
.action-button.create .button-text {
  color: #fff;
}