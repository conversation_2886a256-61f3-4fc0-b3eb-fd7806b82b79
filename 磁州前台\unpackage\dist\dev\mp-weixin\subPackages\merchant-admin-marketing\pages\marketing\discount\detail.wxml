<view class="discount-detail-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">满减活动详情</text><view class="navbar-right"><view class="more-icon" bindtap="{{f}}"><svg wx:if="{{e}}" u-s="{{['d']}}" u-i="2d62efdb-0" bind:__l="__l" u-p="{{e}}"><circle wx:if="{{b}}" u-i="2d62efdb-1,2d62efdb-0" bind:__l="__l" u-p="{{b}}"></circle><circle wx:if="{{c}}" u-i="2d62efdb-2,2d62efdb-0" bind:__l="__l" u-p="{{c}}"></circle><circle wx:if="{{d}}" u-i="2d62efdb-3,2d62efdb-0" bind:__l="__l" u-p="{{d}}"></circle></svg></view></view></view><view class="discount-card"><view class="discount-header"><text class="discount-title">{{g}}</text><view class="{{['discount-status', i]}}">{{h}}</view></view><view class="discount-rules"><view wx:for="{{j}}" wx:for-item="rule" wx:key="c" class="rule-item"><view class="rule-content"><text class="rule-text">满 {{rule.a}} 元减 {{rule.b}} 元</text></view></view></view><view class="validity-period"><text class="validity-label">活动时间:</text><text class="validity-value">{{k}}</text></view><view wx:if="{{l}}" class="discount-qrcode"><image class="qrcode-image" src="{{m}}" mode="aspectFit"></image><text class="qrcode-hint">扫描二维码分享活动</text></view></view><view class="usage-section"><view class="section-header"><text class="section-title">使用情况</text></view><view class="stats-grid"><view class="stats-item"><text class="stats-value">{{n}}</text><text class="stats-label">参与订单</text></view><view class="stats-item"><text class="stats-value">{{o}}</text><text class="stats-label">参与用户</text></view><view class="stats-item"><text class="stats-value">¥{{p}}</text><text class="stats-label">优惠金额</text></view><view class="stats-item"><text class="stats-value">{{q}}%</text><text class="stats-label">转化率</text></view></view><view class="chart-container"><text class="chart-title">优惠金额趋势</text><view class="chart-placeholder"><text class="placeholder-text">图表加载中...</text></view></view></view><view class="settings-section"><view class="section-header"><text class="section-title">活动设置</text></view><view class="settings-list"><view class="settings-item"><text class="item-label">活动类型</text><text class="item-value">{{r}}</text></view><view class="settings-item"><text class="item-label">适用商品</text><text class="item-value">{{s}}</text></view><view class="settings-item"><text class="item-label">叠加使用</text><text class="item-value">{{t}}</text></view><view class="settings-item"><text class="item-label">每人限用</text><text class="item-value">{{v}}次</text></view><view class="settings-item"><text class="item-label">活动说明</text><text class="item-value">{{w}}</text></view></view></view><view class="records-section"><view class="section-header"><text class="section-title">订单记录</text><text class="view-all" bindtap="{{x}}">查看全部</text></view><view class="records-list"><view wx:for="{{y}}" wx:for-item="record" wx:key="e" class="record-item"><view class="record-info"><text class="order-number">订单号: {{record.a}}</text><text class="record-time">{{record.b}}</text></view><view class="record-details"><text class="user-name">{{record.c}}</text><text class="discount-amount">-¥{{record.d}}</text></view></view></view><view wx:if="{{z}}" class="empty-records"><text class="empty-text">暂无订单记录</text></view></view><view class="bottom-action-bar"><view class="action-button edit" bindtap="{{C}}"><view class="button-icon"><svg wx:if="{{B}}" u-s="{{['d']}}" u-i="2d62efdb-4" bind:__l="__l" u-p="{{B}}"><path wx:if="{{A}}" u-i="2d62efdb-5,2d62efdb-4" bind:__l="__l" u-p="{{A}}"></path></svg></view><text class="button-text">编辑</text></view><view class="{{['action-button', K]}}" bindtap="{{L}}"><view class="button-icon"><svg wx:if="{{D}}" u-s="{{['d']}}" u-i="2d62efdb-6" bind:__l="__l" u-p="{{G}}"><rect wx:if="{{E}}" u-i="2d62efdb-7,2d62efdb-6" bind:__l="__l" u-p="{{E}}"></rect><rect wx:if="{{F}}" u-i="2d62efdb-8,2d62efdb-6" bind:__l="__l" u-p="{{F}}"></rect></svg><svg wx:else u-s="{{['d']}}" u-i="2d62efdb-9" bind:__l="__l" u-p="{{I||''}}"><polygon wx:if="{{H}}" u-i="2d62efdb-10,2d62efdb-9" bind:__l="__l" u-p="{{H}}"></polygon></svg></view><text class="button-text">{{J}}</text></view><view class="action-button share" bindtap="{{S}}"><view class="button-icon"><svg wx:if="{{R}}" u-s="{{['d']}}" u-i="2d62efdb-11" bind:__l="__l" u-p="{{R}}"><circle wx:if="{{M}}" u-i="2d62efdb-12,2d62efdb-11" bind:__l="__l" u-p="{{M}}"></circle><circle wx:if="{{N}}" u-i="2d62efdb-13,2d62efdb-11" bind:__l="__l" u-p="{{N}}"></circle><circle wx:if="{{O}}" u-i="2d62efdb-14,2d62efdb-11" bind:__l="__l" u-p="{{O}}"></circle><line wx:if="{{P}}" u-i="2d62efdb-15,2d62efdb-11" bind:__l="__l" u-p="{{P}}"></line><line wx:if="{{Q}}" u-i="2d62efdb-16,2d62efdb-11" bind:__l="__l" u-p="{{Q}}"></line></svg></view><text class="button-text">分享</text></view><view class="action-button delete" bindtap="{{Y}}"><view class="button-icon"><svg wx:if="{{X}}" u-s="{{['d']}}" u-i="2d62efdb-17" bind:__l="__l" u-p="{{X}}"><polyline wx:if="{{T}}" u-i="2d62efdb-18,2d62efdb-17" bind:__l="__l" u-p="{{T}}"></polyline><path wx:if="{{U}}" u-i="2d62efdb-19,2d62efdb-17" bind:__l="__l" u-p="{{U}}"></path><line wx:if="{{V}}" u-i="2d62efdb-20,2d62efdb-17" bind:__l="__l" u-p="{{V}}"></line><line wx:if="{{W}}" u-i="2d62efdb-21,2d62efdb-17" bind:__l="__l" u-p="{{W}}"></line></svg></view><text class="button-text">删除</text></view></view><view wx:if="{{Z}}" class="share-popup"><view class="popup-mask" bindtap="{{aa}}"></view><view class="popup-content"><view class="popup-header"><text class="popup-title">分享活动</text><view class="popup-close" bindtap="{{ab}}">×</view></view><view class="share-options"><view class="share-option" bindtap="{{af}}"><view class="option-icon wechat"><svg wx:if="{{ae}}" u-s="{{['d']}}" u-i="2d62efdb-22" bind:__l="__l" u-p="{{ae}}"><path wx:if="{{ac}}" u-i="2d62efdb-23,2d62efdb-22" bind:__l="__l" u-p="{{ac}}"></path><path wx:if="{{ad}}" u-i="2d62efdb-24,2d62efdb-22" bind:__l="__l" u-p="{{ad}}"></path></svg></view><text class="option-name">微信</text></view><view class="share-option" bindtap="{{ak}}"><view class="option-icon moments"><svg wx:if="{{aj}}" u-s="{{['d']}}" u-i="2d62efdb-25" bind:__l="__l" u-p="{{aj}}"><circle wx:if="{{ag}}" u-i="2d62efdb-26,2d62efdb-25" bind:__l="__l" u-p="{{ag}}"></circle><path wx:if="{{ah}}" u-i="2d62efdb-27,2d62efdb-25" bind:__l="__l" u-p="{{ah}}"></path><path wx:if="{{ai}}" u-i="2d62efdb-28,2d62efdb-25" bind:__l="__l" u-p="{{ai}}"></path></svg></view><text class="option-name">朋友圈</text></view><view class="share-option" bindtap="{{ao}}"><view class="option-icon link"><svg wx:if="{{an}}" u-s="{{['d']}}" u-i="2d62efdb-29" bind:__l="__l" u-p="{{an}}"><path wx:if="{{al}}" u-i="2d62efdb-30,2d62efdb-29" bind:__l="__l" u-p="{{al}}"></path><path wx:if="{{am}}" u-i="2d62efdb-31,2d62efdb-29" bind:__l="__l" u-p="{{am}}"></path></svg></view><text class="option-name">复制链接</text></view><view class="share-option" bindtap="{{av}}"><view class="option-icon qrcode"><svg wx:if="{{at}}" u-s="{{['d']}}" u-i="2d62efdb-32" bind:__l="__l" u-p="{{at}}"><rect wx:if="{{ap}}" u-i="2d62efdb-33,2d62efdb-32" bind:__l="__l" u-p="{{ap}}"></rect><rect wx:if="{{aq}}" u-i="2d62efdb-34,2d62efdb-32" bind:__l="__l" u-p="{{aq}}"></rect><rect wx:if="{{ar}}" u-i="2d62efdb-35,2d62efdb-32" bind:__l="__l" u-p="{{ar}}"></rect><rect wx:if="{{as}}" u-i="2d62efdb-36,2d62efdb-32" bind:__l="__l" u-p="{{as}}"></rect></svg></view><text class="option-name">二维码</text></view></view></view></view></view>