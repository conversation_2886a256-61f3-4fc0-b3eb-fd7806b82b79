{"version": 3, "file": "mass-sending.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/mass-sending.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcbWFzcy1zZW5kaW5nLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">红包群发</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 红包群发数据概览 -->\r\n    <view class=\"overview-section\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"section-title\">群发数据概览</text>\r\n        <view class=\"date-picker\" @click=\"showDatePicker\">\r\n          <text class=\"date-text\">{{dateRange}}</text>\r\n          <view class=\"date-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-cards\">\r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{massData.totalCount}}</view>\r\n          <view class=\"card-label\">群发总次数</view>\r\n          <view class=\"card-trend\" :class=\"massData.countTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{massData.countGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{massData.totalUsers}}</view>\r\n          <view class=\"card-label\">覆盖用户</view>\r\n          <view class=\"card-trend\" :class=\"massData.usersTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{massData.usersGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">¥{{formatNumber(massData.totalAmount)}}</view>\r\n          <view class=\"card-label\">发放总额</view>\r\n          <view class=\"card-trend\" :class=\"massData.amountTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{massData.amountGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{massData.conversionRate}}%</view>\r\n          <view class=\"card-label\">转化率</view>\r\n          <view class=\"card-trend\" :class=\"massData.conversionTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{massData.conversionGrowth}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 群发记录 -->\r\n    <view class=\"mass-history-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">群发记录</text>\r\n        <view class=\"add-btn\" @click=\"createMassSending\">\r\n          <text class=\"btn-text\">新建群发</text>\r\n          <view class=\"plus-icon-small\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"tab-header\">\r\n        <view \r\n          class=\"tab-item\" \r\n          v-for=\"(tab, index) in tabList\" \r\n          :key=\"index\"\r\n          :class=\"{ active: currentTab === index }\"\r\n          @tap=\"switchTab(index)\">\r\n          <text class=\"tab-text\">{{tab}}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"mass-list\">\r\n        <view class=\"mass-item\" v-for=\"(item, index) in filteredMassList\" :key=\"index\" @click=\"viewMassDetail(item)\">\r\n          <view class=\"mass-header\">\r\n            <view class=\"mass-title\">{{item.title}}</view>\r\n            <view class=\"mass-status\" :class=\"'status-'+item.status\">{{item.statusText}}</view>\r\n          </view>\r\n          \r\n          <view class=\"mass-content\">\r\n            <view class=\"mass-icon\" :class=\"item.type\"></view>\r\n            <view class=\"mass-info\">\r\n              <text class=\"mass-time\">发送时间: {{item.sendTime}}</text>\r\n              <view class=\"mass-amount\">\r\n                <text class=\"amount-label\">红包金额: </text>\r\n                <text class=\"amount-value\" v-if=\"item.type === 'fixed'\">¥{{item.amount}}/个</text>\r\n                <text class=\"amount-value\" v-else-if=\"item.type === 'random'\">¥{{item.minAmount}}-{{item.maxAmount}}/个</text>\r\n              </view>\r\n              <view class=\"mass-target\">\r\n                <text class=\"target-label\">发送对象: </text>\r\n                <text class=\"target-value\">{{item.targetText}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"mass-stats\">\r\n            <view class=\"stat-row\">\r\n              <text class=\"stat-label\">发送人数:</text>\r\n              <text class=\"stat-value\">{{item.sentCount}}人</text>\r\n            </view>\r\n            <view class=\"stat-row\">\r\n              <text class=\"stat-label\">领取人数:</text>\r\n              <text class=\"stat-value\">{{item.receivedCount}}人</text>\r\n            </view>\r\n            <view class=\"stat-row\">\r\n              <text class=\"stat-label\">使用人数:</text>\r\n              <text class=\"stat-value\">{{item.usedCount}}人</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"mass-actions\">\r\n            <view class=\"action-btn\" @click.stop=\"viewMassDetail(item)\">详情</view>\r\n            <view class=\"action-btn\" @click.stop=\"repeatMass(item)\" v-if=\"item.status === 'completed'\">再次发送</view>\r\n            <view class=\"action-btn delete\" @click.stop=\"deleteMass(item)\" v-if=\"item.status === 'draft'\">删除</view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"empty-state\" v-if=\"filteredMassList.length === 0\">\r\n          <view class=\"empty-icon\"></view>\r\n          <text class=\"empty-text\">暂无{{tabList[currentTab]}}群发记录</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 群发攻略 -->\r\n    <view class=\"strategy-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">群发攻略</text>\r\n      </view>\r\n      \r\n      <view class=\"strategy-list\">\r\n        <view class=\"strategy-item\" v-for=\"(strategy, index) in strategies\" :key=\"index\" @click=\"viewStrategy(strategy)\">\r\n          <view class=\"strategy-icon\" :style=\"{ background: strategy.color }\">\r\n            <image class=\"icon-image\" :src=\"strategy.icon\" mode=\"aspectFit\"></image>\r\n          </view>\r\n          <view class=\"strategy-content\">\r\n            <text class=\"strategy-title\">{{strategy.title}}</text>\r\n            <text class=\"strategy-desc\">{{strategy.description}}</text>\r\n          </view>\r\n          <view class=\"strategy-arrow\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 浮动操作按钮 -->\r\n    <view class=\"floating-action-button\" @click=\"createMassSending\">\r\n      <view class=\"fab-icon\">+</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dateRange: '2023-04-01 ~ 2023-04-30',\r\n      currentTab: 0,\r\n      tabList: ['全部', '进行中', '已完成', '草稿'],\r\n      \r\n      // 群发数据概览\r\n      massData: {\r\n        totalCount: 42,\r\n        countTrend: 'up',\r\n        countGrowth: '15.3%',\r\n        \r\n        totalUsers: 3568,\r\n        usersTrend: 'up',\r\n        usersGrowth: '23.7%',\r\n        \r\n        totalAmount: 8765.50,\r\n        amountTrend: 'up',\r\n        amountGrowth: '18.2%',\r\n        \r\n        conversionRate: 32.6,\r\n        conversionTrend: 'up',\r\n        conversionGrowth: '5.8%'\r\n      },\r\n      \r\n      // 群发记录列表\r\n      massList: [\r\n        {\r\n          id: 1,\r\n          title: '新用户欢迎红包',\r\n          type: 'fixed',\r\n          status: 'completed',\r\n          statusText: '已完成',\r\n          sendTime: '2023-04-15 14:30',\r\n          amount: 10.00,\r\n          targetText: '新注册用户',\r\n          sentCount: 568,\r\n          receivedCount: 452,\r\n          usedCount: 326\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '五一节日红包',\r\n          type: 'random',\r\n          status: 'processing',\r\n          statusText: '进行中',\r\n          sendTime: '2023-05-01 10:00',\r\n          minAmount: 5.00,\r\n          maxAmount: 50.00,\r\n          targetText: '全部会员',\r\n          sentCount: 1200,\r\n          receivedCount: 876,\r\n          usedCount: 543\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '老用户回馈红包',\r\n          type: 'fixed',\r\n          status: 'draft',\r\n          statusText: '草稿',\r\n          sendTime: '未发送',\r\n          amount: 15.00,\r\n          targetText: '三个月未消费用户',\r\n          sentCount: 0,\r\n          receivedCount: 0,\r\n          usedCount: 0\r\n        }\r\n      ],\r\n      \r\n      // 群发攻略\r\n      strategies: [\r\n        {\r\n          id: 1,\r\n          title: '如何提高红包领取率',\r\n          description: '了解用户行为，精准设置红包金额和发放时间',\r\n          color: '#FF6B6B',\r\n          icon: '/static/images/redpacket/strategy-icon-1.png'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '会员分层群发策略',\r\n          description: '根据会员等级和消费习惯制定差异化红包策略',\r\n          color: '#4ECDC4',\r\n          icon: '/static/images/redpacket/strategy-icon-2.png'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '节假日红包营销攻略',\r\n          description: '把握节假日营销节点，提升红包营销效果',\r\n          color: '#FFD166',\r\n          icon: '/static/images/redpacket/strategy-icon-3.png'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredMassList() {\r\n      if (this.currentTab === 0) {\r\n        return this.massList;\r\n      } else {\r\n        const statusMap = {\r\n          1: 'processing',\r\n          2: 'completed',\r\n          3: 'draft'\r\n        };\r\n        return this.massList.filter(item => item.status === statusMap[this.currentTab]);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    showHelp() {\r\n      uni.showModal({\r\n        title: '红包群发帮助',\r\n        content: '红包群发功能可以帮助您向指定用户群体批量发送红包，提升用户活跃度和转化率。',\r\n        showCancel: false\r\n      });\r\n    },\r\n    showDatePicker() {\r\n      // 显示日期选择器\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    formatNumber(num) {\r\n      return num.toFixed(2);\r\n    },\r\n    switchTab(index) {\r\n      this.currentTab = index;\r\n    },\r\n    createMassSending() {\r\n      uni.showToast({\r\n        title: '创建群发功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    viewMassDetail(item) {\r\n      uni.showToast({\r\n        title: '查看详情功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    repeatMass(item) {\r\n      uni.showToast({\r\n        title: '再次发送功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    deleteMass(item) {\r\n      uni.showModal({\r\n        title: '删除确认',\r\n        content: `确定要删除\"${item.title}\"吗？`,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '删除成功',\r\n              icon: 'success'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    viewStrategy(strategy) {\r\n      uni.showToast({\r\n        title: '攻略详情功能开发中',\r\n        icon: 'none'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.page-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 数据概览样式 */\r\n.overview-section {\r\n  background-color: #fff;\r\n  margin: 15px;\r\n  border-radius: 12px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #F5F7FA;\r\n  padding: 5px 10px;\r\n  border-radius: 15px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.date-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 1px solid #666;\r\n  border-bottom: 1px solid #666;\r\n  transform: rotate(-45deg);\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n}\r\n\r\n.stats-card {\r\n  width: 48%;\r\n  background: linear-gradient(135deg, #FFF, #F5F7FA);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.card-value {\r\n  font-size: 22px;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.card-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.card-trend {\r\n  position: absolute;\r\n  top: 15px;\r\n  right: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-trend.up {\r\n  color: #FF5858;\r\n}\r\n\r\n.card-trend.down {\r\n  color: #2ED573;\r\n}\r\n\r\n.trend-arrow {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-left: 1px solid currentColor;\r\n  border-top: 1px solid currentColor;\r\n  margin-right: 2px;\r\n}\r\n\r\n.up .trend-arrow {\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.down .trend-arrow {\r\n  transform: rotate(-135deg);\r\n}\r\n\r\n/* 群发记录样式 */\r\n.mass-history-section {\r\n  background-color: #fff;\r\n  margin: 15px;\r\n  border-radius: 12px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n  padding: 6px 12px;\r\n  border-radius: 15px;\r\n  color: #fff;\r\n}\r\n\r\n.btn-text {\r\n  font-size: 12px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.plus-icon-small {\r\n  width: 12px;\r\n  height: 12px;\r\n  position: relative;\r\n}\r\n\r\n.plus-icon-small::before,\r\n.plus-icon-small::after {\r\n  content: '';\r\n  position: absolute;\r\n  background-color: #fff;\r\n}\r\n\r\n.plus-icon-small::before {\r\n  width: 12px;\r\n  height: 2px;\r\n  top: 5px;\r\n  left: 0;\r\n}\r\n\r\n.plus-icon-small::after {\r\n  width: 2px;\r\n  height: 12px;\r\n  top: 0;\r\n  left: 5px;\r\n}\r\n\r\n.tab-header {\r\n  display: flex;\r\n  border-bottom: 1px solid #eee;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 10px 0;\r\n  position: relative;\r\n}\r\n\r\n.tab-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.tab-item.active .tab-text {\r\n  color: #FF5858;\r\n  font-weight: 600;\r\n}\r\n\r\n.tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -1px;\r\n  left: 25%;\r\n  width: 50%;\r\n  height: 2px;\r\n  background-color: #FF5858;\r\n  border-radius: 1px;\r\n}\r\n\r\n.mass-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.mass-item {\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\r\n  border: 1px solid #eee;\r\n}\r\n\r\n.mass-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.mass-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.mass-status {\r\n  font-size: 12px;\r\n  padding: 3px 8px;\r\n  border-radius: 10px;\r\n}\r\n\r\n.status-completed {\r\n  background-color: #E1F5FE;\r\n  color: #0288D1;\r\n}\r\n\r\n.status-processing {\r\n  background-color: #E8F5E9;\r\n  color: #388E3C;\r\n}\r\n\r\n.status-draft {\r\n  background-color: #EEEEEE;\r\n  color: #757575;\r\n}\r\n\r\n.mass-content {\r\n  display: flex;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.mass-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  background-color: #FFE0E0;\r\n  margin-right: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.mass-icon.fixed::before {\r\n  content: '固';\r\n  color: #FF5858;\r\n  font-weight: bold;\r\n}\r\n\r\n.mass-icon.random::before {\r\n  content: '随';\r\n  color: #FF5858;\r\n  font-weight: bold;\r\n}\r\n\r\n.mass-info {\r\n  flex: 1;\r\n}\r\n\r\n.mass-time {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-bottom: 5px;\r\n  display: block;\r\n}\r\n\r\n.mass-amount,\r\n.mass-target {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-bottom: 5px;\r\n  display: flex;\r\n}\r\n\r\n.amount-label,\r\n.target-label {\r\n  color: #999;\r\n}\r\n\r\n.amount-value,\r\n.target-value {\r\n  color: #333;\r\n}\r\n\r\n.mass-stats {\r\n  background-color: #F9F9F9;\r\n  border-radius: 8px;\r\n  padding: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.stat-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 12px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.stat-label {\r\n  color: #666;\r\n}\r\n\r\n.stat-value {\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.mass-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  border-top: 1px solid #eee;\r\n  padding-top: 10px;\r\n}\r\n\r\n.action-btn {\r\n  margin-left: 10px;\r\n  padding: 5px 12px;\r\n  border-radius: 15px;\r\n  font-size: 12px;\r\n  background-color: #F5F7FA;\r\n  color: #666;\r\n}\r\n\r\n.action-btn.delete {\r\n  background-color: #FEE8E8;\r\n  color: #FF5858;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 30px 0;\r\n}\r\n\r\n.empty-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 30px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n/* 群发攻略样式 */\r\n.strategy-section {\r\n  background-color: #fff;\r\n  margin: 15px;\r\n  border-radius: 12px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 80px;\r\n}\r\n\r\n.strategy-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.strategy-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.strategy-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.strategy-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  margin-right: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.icon-image {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.strategy-content {\r\n  flex: 1;\r\n}\r\n\r\n.strategy-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.strategy-desc {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.strategy-arrow {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-top: 1px solid #ccc;\r\n  border-right: 1px solid #ccc;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n/* 浮动操作按钮 */\r\n.floating-action-button {\r\n  position: fixed;\r\n  bottom: 30px;\r\n  right: 20px;\r\n  width: 56px;\r\n  height: 56px;\r\n  border-radius: 28px;\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 3px 10px rgba(255, 88, 88, 0.3);\r\n  z-index: 100;\r\n}\r\n\r\n.fab-icon {\r\n  font-size: 28px;\r\n  color: #fff;\r\n  font-weight: 300;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/mass-sending.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAmKA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS,CAAC,MAAM,OAAO,OAAO,IAAI;AAAA;AAAA,MAGlC,UAAU;AAAA,QACR,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QAEd,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MACnB;AAAA;AAAA,MAGD,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,WAAW;AAAA,QACZ;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,WAAW;AAAA,QACb;AAAA,MACD;AAAA;AAAA,MAGD,YAAY;AAAA,QACV;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,mBAAmB;AACjB,UAAI,KAAK,eAAe,GAAG;AACzB,eAAO,KAAK;AAAA,aACP;AACL,cAAM,YAAY;AAAA,UAChB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA;AAEL,eAAO,KAAK,SAAS,OAAO,UAAQ,KAAK,WAAW,UAAU,KAAK,UAAU,CAAC;AAAA,MAChF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa,KAAK;AAChB,aAAO,IAAI,QAAQ,CAAC;AAAA,IACrB;AAAA,IACD,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA,IACD,oBAAoB;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,eAAe,MAAM;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,WAAW,MAAM;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,WAAW,MAAM;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,KAAK;AAAA,QAC5B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa,UAAU;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/UA,GAAG,WAAW,eAAe;"}