<template>
  <view class="carpool-list-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
        </view>
        <view class="navbar-title">拼车列表</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <view class="filter-bar">
        <view class="filter-item" :class="{ active: type === 'people-to-car' }" @click="setType('people-to-car')">人找车</view>
        <view class="filter-item" :class="{ active: type === 'car-to-people' }" @click="setType('car-to-people')">车找人</view>
        <view class="filter-item" :class="{ active: type === 'goods-to-car' }" @click="setType('goods-to-car')">货找车</view>
        <view class="filter-item" :class="{ active: type === 'car-to-goods' }" @click="setType('car-to-goods')">车找货</view>
      </view>

      <!-- 路线信息 -->
      <view class="route-info" v-if="startPoint && endPoint">
        <view class="route-title">路线信息</view>
        <view class="route-detail">
          <text class="route-point">{{ startPoint }}</text>
          <text class="route-arrow">→</text>
          <text class="route-point">{{ endPoint }}</text>
        </view>
      </view>

      <!-- 暂无数据提示 -->
      <view class="empty-data" v-if="carpoolList.length === 0">
        <image src="/static/images/tabbar/empty.png" class="empty-icon"></image>
        <text class="empty-text">暂无拼车信息</text>
      </view>

      <!-- 拼车列表 -->
      <view class="list-container" v-else>
        <view class="carpool-item" v-for="(item, index) in carpoolList" :key="index" @click="viewDetail(item)">
          <view class="item-header">
            <view class="item-type" :class="item.type">{{ getTypeText(item.type) }}</view>
            <view class="item-time">{{ item.departureTime }}</view>
          </view>
          <view class="item-route">
            <view class="route-start">
              <text class="route-label">出发:</text>
              <text class="route-value">{{ item.startPoint }}</text>
            </view>
            <view class="route-end">
              <text class="route-label">到达:</text>
              <text class="route-value">{{ item.endPoint }}</text>
            </view>
          </view>
          <view class="item-footer">
            <view class="user-info">
              <image :src="item.avatar" class="user-avatar"></image>
              <view class="user-info-details">
              <text class="user-name">{{ item.username }}</text>
                <view class="verified-badge" v-if="item.isVerified">
                  <image src="/static/images/tabbar/verified.png" mode="aspectFit" class="verified-icon"></image>
                  <text class="verified-text">已认证</text>
                </view>
              </view>
            </view>
            <view class="item-actions">
              <button class="action-btn call" @click.stop="callPhone(item.phone)">拨打</button>
              <button class="action-btn chat" @click.stop="chatWith(item.userId)">私信</button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 状态栏高度
const statusBarHeight = ref(20);
// 拼车类型
const type = ref('');
// 起点和终点
const startPoint = ref('');
const endPoint = ref('');
// 拼车列表数据
const carpoolList = ref([]);

// 页面加载
onMounted((options) => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;
  
  // 获取参数
  if (options && options.type) {
    type.value = options.type;
  }
  
  if (options && options.start) {
    startPoint.value = decodeURIComponent(options.start);
  }
  
  if (options && options.end) {
    endPoint.value = decodeURIComponent(options.end);
  }
  
  // 加载拼车列表数据
  loadCarpoolList();
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 设置类型
const setType = (newType) => {
  type.value = newType;
  loadCarpoolList();
};

// 加载拼车列表数据
const loadCarpoolList = () => {
  // 这里应该是实际的API调用
  // 目前使用模拟数据
  setTimeout(() => {
    carpoolList.value = [
      {
        id: 1,
        type: 'people-to-car',
        avatar: '/static/images/avatar/user1.png',
        username: '张先生',
        startPoint: '磁州城区-汽车站',
        endPoint: '邯郸火车站',
        departureTime: '今天 14:30',
        seats: 1,
        phone: '13812345678',
        userId: 'user123'
      },
      {
        id: 2,
        type: 'car-to-people',
        avatar: '/static/images/avatar/user2.png',
        username: '李师傅',
        startPoint: '邯郸机场',
        endPoint: '磁州城区-人民医院',
        departureTime: '今天 18:00',
        price: 30,
        seats: 3,
        phone: '13987654321',
        userId: 'user456'
      }
    ];
    
    // 根据类型筛选
    if (type.value) {
      carpoolList.value = carpoolList.value.filter(item => item.type === type.value);
    }
    
    // 根据路线筛选
    if (startPoint.value && endPoint.value) {
      carpoolList.value = carpoolList.value.filter(item => 
        item.startPoint.includes(startPoint.value) && 
        item.endPoint.includes(endPoint.value)
      );
    }
  }, 500);
};

// 获取类型文本
const getTypeText = (typeValue) => {
  switch(typeValue) {
    case 'people-to-car': return '人找车';
    case 'car-to-people': return '车找人';
    case 'goods-to-car': return '货找车';
    case 'car-to-goods': return '车找货';
    default: return '';
  }
};

// 查看详情
const viewDetail = (item) => {
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/detail/index?id=${item.id}&type=${item.type}`
  });
};

// 拨打电话
const callPhone = (phone) => {
  uni.makePhoneCall({
    phoneNumber: phone
  });
};

// 发送私信
const chatWith = (userId) => {
  uni.navigateTo({
    url: `/pages/message/chat?userId=${userId}`
  });
};
</script>

<style>
.carpool-list-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #0A84FF;
  color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.navbar {
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}

.navbar-left {
  width: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}

.navbar-right {
  width: 40px;
}

.content {
  margin-top: 64px;
  padding: 15px;
  flex: 1;
}

.filter-bar {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.filter-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: #333;
}

.filter-item.active {
  background-color: #0A84FF;
  color: #fff;
}

.route-info {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.route-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.route-detail {
  display: flex;
  align-items: center;
}

.route-point {
  flex: 1;
  font-size: 14px;
}

.route-arrow {
  margin: 0 10px;
  color: #0A84FF;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

.list-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.carpool-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.item-type {
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 4px;
  color: #fff;
}

.item-type.people-to-car {
  background-color: #0A84FF;
}

.item-type.car-to-people {
  background-color: #FF2D55;
}

.item-type.goods-to-car {
  background-color: #30D158;
}

.item-type.car-to-goods {
  background-color: #FF9F0A;
}

.item-time {
  font-size: 14px;
  color: #666;
}

.item-route {
  margin-bottom: 10px;
}

.route-start, .route-end {
  display: flex;
  margin-bottom: 5px;
}

.route-label {
  width: 40px;
  color: #999;
  font-size: 14px;
}

.route-value {
  flex: 1;
  font-size: 14px;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 30px;
  height: 30px;
  border-radius: 15px;
  margin-right: 8px;
}

.user-info-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 14px;
  color: #333;
}

.verified-badge {
  display: flex;
  align-items: center;
  margin-top: 4rpx;
  background-color: rgba(10, 132, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(10, 132, 255, 0.3);
}

.verified-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.verified-text {
  font-size: 20rpx;
  color: #0A84FF;
  font-weight: 500;
}

.item-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
  border: none;
}

.action-btn.call {
  background-color: #34C759;
  color: #fff;
}

.action-btn.chat {
  background-color: #0A84FF;
  color: #fff;
}
</style> 