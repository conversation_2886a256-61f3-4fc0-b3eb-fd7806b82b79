"use strict";
const common_vendor = require("../../../../common/vendor.js");
const CustomNavbar = () => "../../components/CustomNavbar.js";
const PlatformCard = () => "../../components/PlatformCard.js";
const _sfc_main = {
  components: {
    CustomNavbar,
    PlatformCard
  },
  data() {
    return {
      platforms: [
        { id: 1, name: "淘宝", icon: "/static/images/cashback/platform-taobao.png", desc: "返利比例 0.5%-20%", products: 1e6 },
        { id: 2, name: "京东", icon: "/static/images/cashback/platform-jd.png", desc: "返利比例 0.5%-15%", products: 8e5 },
        { id: 3, name: "拼多多", icon: "/static/images/cashback/platform-pdd.png", desc: "返利比例 1%-30%", products: 6e5 },
        { id: 4, name: "唯品会", icon: "/static/images/cashback/platform-vip.png", desc: "返利比例 1%-10%", products: 4e5 },
        { id: 5, name: "抖音", icon: "/static/images/cashback/platform-douyin.png", desc: "返利比例 1%-25%", products: 5e5 },
        { id: 6, name: "天猫", icon: "/static/images/cashback/platform-tmall.png", desc: "返利比例 0.5%-18%", products: 9e5 },
        { id: 7, name: "苏宁", icon: "/static/images/cashback/platform-suning.png", desc: "返利比例 1%-12%", products: 3e5 },
        { id: 8, name: "小红书", icon: "/static/images/cashback/platform-xiaohongshu.png", desc: "返利比例 2%-20%", products: 2e5 },
        { id: 9, name: "考拉海购", icon: "/static/images/cashback/platform-kaola.png", desc: "返利比例 1%-15%", products: 15e4 },
        { id: 10, name: "网易严选", icon: "/static/images/cashback/platform-yanxuan.png", desc: "返利比例 2%-10%", products: 1e5 },
        { id: 11, name: "亚马逊", icon: "/static/images/cashback/platform-amazon.png", desc: "返利比例 0.5%-8%", products: 5e5 },
        { id: 12, name: "当当", icon: "/static/images/cashback/platform-dangdang.png", desc: "返利比例 1%-12%", products: 2e5 }
      ]
    };
  },
  onLoad() {
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#9C27B0"
    });
  },
  methods: {
    navigateToSearch() {
      common_vendor.index.navigateTo({
        url: "/subPackages/cashback/pages/search/index"
      });
    },
    navigateToPlatformDetail(platform) {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/platform-detail/index?id=${platform.id}&name=${platform.name}`
      });
    }
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_platform_card = common_vendor.resolveComponent("platform-card");
  (_component_custom_navbar + _component_path + _component_svg + _component_platform_card)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "热门平台",
      ["show-back"]: true
    }),
    b: common_vendor.p({
      fill: "#999999",
      d: "M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"
    }),
    c: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "20",
      height: "20"
    }),
    d: common_vendor.o((...args) => $options.navigateToSearch && $options.navigateToSearch(...args)),
    e: common_vendor.f($data.platforms, (platform, index, i0) => {
      return {
        a: index,
        b: common_vendor.o(($event) => $options.navigateToPlatformDetail(platform), index),
        c: "fd261dea-3-" + i0,
        d: common_vendor.p({
          platform
        })
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fd261dea"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/cashback/pages/platforms/index.js.map
