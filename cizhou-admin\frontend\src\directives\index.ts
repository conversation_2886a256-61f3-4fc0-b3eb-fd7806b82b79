import type { App } from 'vue'
import { useUserStore } from '@/stores/user'

// 权限指令
const permission = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding
    const userStore = useUserStore()
    
    if (value && !userStore.checkPermission(value)) {
      el.style.display = 'none'
    }
  },
  updated(el: HTMLElement, binding: any) {
    const { value } = binding
    const userStore = useUserStore()
    
    if (value && !userStore.checkPermission(value)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// 角色指令
const role = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding
    const userStore = useUserStore()
    
    if (value && !userStore.checkRole(value)) {
      el.style.display = 'none'
    }
  },
  updated(el: HTMLElement, binding: any) {
    const { value } = binding
    const userStore = useUserStore()
    
    if (value && !userStore.checkRole(value)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// 防抖指令
const debounce = {
  mounted(el: HTMLElement, binding: any) {
    let timer: NodeJS.Timeout
    el.addEventListener('click', () => {
      if (timer) clearTimeout(timer)
      timer = setTimeout(() => {
        binding.value()
      }, binding.arg || 300)
    })
  }
}

// 节流指令
const throttle = {
  mounted(el: HTMLElement, binding: any) {
    let timer: NodeJS.Timeout | null = null
    el.addEventListener('click', () => {
      if (timer) return
      timer = setTimeout(() => {
        binding.value()
        timer = null
      }, binding.arg || 300)
    })
  }
}

// 复制指令
const copy = {
  mounted(el: HTMLElement, binding: any) {
    el.addEventListener('click', () => {
      const text = binding.value
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          ElMessage.success('复制成功')
        }).catch(() => {
          ElMessage.error('复制失败')
        })
      } else {
        // 兼容旧浏览器
        const textarea = document.createElement('textarea')
        textarea.value = text
        document.body.appendChild(textarea)
        textarea.select()
        document.execCommand('copy')
        document.body.removeChild(textarea)
        ElMessage.success('复制成功')
      }
    })
  }
}

// 长按指令
const longpress = {
  mounted(el: HTMLElement, binding: any) {
    let timer: NodeJS.Timeout
    
    const start = () => {
      timer = setTimeout(() => {
        binding.value()
      }, binding.arg || 1000)
    }
    
    const cancel = () => {
      if (timer) clearTimeout(timer)
    }
    
    el.addEventListener('mousedown', start)
    el.addEventListener('mouseup', cancel)
    el.addEventListener('mouseleave', cancel)
    el.addEventListener('touchstart', start)
    el.addEventListener('touchend', cancel)
    el.addEventListener('touchcancel', cancel)
  }
}

// 拖拽指令
const draggable = {
  mounted(el: HTMLElement, binding: any) {
    el.style.cursor = 'move'
    el.style.position = 'relative'
    
    let isDragging = false
    let startX = 0
    let startY = 0
    let initialX = 0
    let initialY = 0
    
    const handleMouseDown = (e: MouseEvent) => {
      isDragging = true
      startX = e.clientX
      startY = e.clientY
      initialX = parseInt(el.style.left) || 0
      initialY = parseInt(el.style.top) || 0
      
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return
      
      const deltaX = e.clientX - startX
      const deltaY = e.clientY - startY
      
      el.style.left = `${initialX + deltaX}px`
      el.style.top = `${initialY + deltaY}px`
    }
    
    const handleMouseUp = () => {
      isDragging = false
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      
      if (binding.value && typeof binding.value === 'function') {
        binding.value({
          x: parseInt(el.style.left) || 0,
          y: parseInt(el.style.top) || 0
        })
      }
    }
    
    el.addEventListener('mousedown', handleMouseDown)
  }
}

// 懒加载指令
const lazy = {
  mounted(el: HTMLImageElement, binding: any) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          el.src = binding.value
          observer.unobserve(el)
        }
      })
    })
    
    observer.observe(el)
  }
}

// 水印指令
const watermark = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = 200
    canvas.height = 100
    
    ctx.rotate(-20 * Math.PI / 180)
    ctx.font = '14px Arial'
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'
    ctx.fillText(value || '磁州生活网', 10, 50)
    
    const dataURL = canvas.toDataURL()
    el.style.backgroundImage = `url(${dataURL})`
    el.style.backgroundRepeat = 'repeat'
  }
}

export const setupDirectives = (app: App) => {
  app.directive('permission', permission)
  app.directive('role', role)
  app.directive('debounce', debounce)
  app.directive('throttle', throttle)
  app.directive('copy', copy)
  app.directive('longpress', longpress)
  app.directive('draggable', draggable)
  app.directive('lazy', lazy)
  app.directive('watermark', watermark)
}
