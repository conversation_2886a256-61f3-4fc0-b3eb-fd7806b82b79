{"version": 3, "file": "index.js", "sources": ["subPackages/cashback/pages/platforms/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2FzaGJhY2tccGFnZXNccGxhdGZvcm1zXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"platforms-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <custom-navbar title=\"热门平台\" :show-back=\"true\"></custom-navbar>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content-container\">\r\n      <!-- 搜索框 -->\r\n      <view class=\"search-section\">\r\n        <view class=\"search-bar\" @tap=\"navigateToSearch\">\r\n          <svg class=\"search-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n            <path fill=\"#999999\" d=\"M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z\" />\r\n          </svg>\r\n          <text class=\"search-placeholder\">搜索平台名称</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 平台列表 -->\r\n      <view class=\"platforms-grid\">\r\n        <platform-card\r\n          v-for=\"(platform, index) in platforms\"\r\n          :key=\"index\"\r\n          :platform=\"platform\"\r\n          @tap=\"navigateToPlatformDetail(platform)\"\r\n        ></platform-card>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport CustomNavbar from '../../components/CustomNavbar.vue';\r\nimport PlatformCard from '../../components/PlatformCard.vue';\r\n\r\nexport default {\r\n  components: {\r\n    CustomNavbar,\r\n    PlatformCard\r\n  },\r\n  data() {\r\n    return {\r\n      platforms: [\r\n        { id: 1, name: '淘宝', icon: '/static/images/cashback/platform-taobao.png', desc: '返利比例 0.5%-20%', products: 1000000 },\r\n        { id: 2, name: '京东', icon: '/static/images/cashback/platform-jd.png', desc: '返利比例 0.5%-15%', products: 800000 },\r\n        { id: 3, name: '拼多多', icon: '/static/images/cashback/platform-pdd.png', desc: '返利比例 1%-30%', products: 600000 },\r\n        { id: 4, name: '唯品会', icon: '/static/images/cashback/platform-vip.png', desc: '返利比例 1%-10%', products: 400000 },\r\n        { id: 5, name: '抖音', icon: '/static/images/cashback/platform-douyin.png', desc: '返利比例 1%-25%', products: 500000 },\r\n        { id: 6, name: '天猫', icon: '/static/images/cashback/platform-tmall.png', desc: '返利比例 0.5%-18%', products: 900000 },\r\n        { id: 7, name: '苏宁', icon: '/static/images/cashback/platform-suning.png', desc: '返利比例 1%-12%', products: 300000 },\r\n        { id: 8, name: '小红书', icon: '/static/images/cashback/platform-xiaohongshu.png', desc: '返利比例 2%-20%', products: 200000 },\r\n        { id: 9, name: '考拉海购', icon: '/static/images/cashback/platform-kaola.png', desc: '返利比例 1%-15%', products: 150000 },\r\n        { id: 10, name: '网易严选', icon: '/static/images/cashback/platform-yanxuan.png', desc: '返利比例 2%-10%', products: 100000 },\r\n        { id: 11, name: '亚马逊', icon: '/static/images/cashback/platform-amazon.png', desc: '返利比例 0.5%-8%', products: 500000 },\r\n        { id: 12, name: '当当', icon: '/static/images/cashback/platform-dangdang.png', desc: '返利比例 1%-12%', products: 200000 }\r\n      ]\r\n    };\r\n  },\r\n  onLoad() {\r\n    // 设置页面不显示系统导航栏\r\n    uni.setNavigationBarColor({\r\n      frontColor: '#ffffff',\r\n      backgroundColor: '#9C27B0'\r\n    });\r\n  },\r\n  methods: {\r\n    navigateToSearch() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/cashback/pages/search/index'\r\n      });\r\n    },\r\n    navigateToPlatformDetail(platform) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/cashback/pages/platform-detail/index?id=${platform.id}&name=${platform.name}`\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.platforms-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.content-container {\r\n  padding-top: calc(var(--status-bar-height) + 44px);\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.search-section {\r\n  padding: 16px;\r\n  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);\r\n  border-bottom-left-radius: 20px;\r\n  border-bottom-right-radius: 20px;\r\n  padding-top: 24px;\r\n}\r\n\r\n.search-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #FFFFFF;\r\n  border-radius: 20px;\r\n  padding: 10px 16px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  \r\n  .search-icon {\r\n    margin-right: 8px;\r\n  }\r\n  \r\n  .search-placeholder {\r\n    color: #999999;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.platforms-grid {\r\n  padding: 16px;\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 16px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/pages/platforms/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA+BA,MAAK,eAAgB,MAAW;AAChC,MAAK,eAAgB,MAAW;AAEhC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,QACT,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,+CAA+C,MAAM,iBAAiB,UAAU,IAAS;AAAA,QACpH,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,2CAA2C,MAAM,iBAAiB,UAAU,IAAQ;AAAA,QAC/G,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,4CAA4C,MAAM,eAAe,UAAU,IAAQ;AAAA,QAC/G,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,4CAA4C,MAAM,eAAe,UAAU,IAAQ;AAAA,QAC/G,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,+CAA+C,MAAM,eAAe,UAAU,IAAQ;AAAA,QACjH,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,8CAA8C,MAAM,iBAAiB,UAAU,IAAQ;AAAA,QAClH,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,+CAA+C,MAAM,eAAe,UAAU,IAAQ;AAAA,QACjH,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,oDAAoD,MAAM,eAAe,UAAU,IAAQ;AAAA,QACvH,EAAE,IAAI,GAAG,MAAM,QAAQ,MAAM,8CAA8C,MAAM,eAAe,UAAU,KAAQ;AAAA,QAClH,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,gDAAgD,MAAM,eAAe,UAAU,IAAQ;AAAA,QACrH,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,+CAA+C,MAAM,gBAAgB,UAAU,IAAQ;AAAA,QACpH,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,iDAAiD,MAAM,eAAe,UAAU,IAAO;AAAA,MACrH;AAAA;EAEH;AAAA,EACD,SAAS;AAEPA,kBAAAA,MAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,mBAAmB;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,yBAAyB,UAAU;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wDAAwD,SAAS,EAAE,SAAS,SAAS,IAAI;AAAA,MAChG,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3EA,GAAG,WAAW,eAAe;"}