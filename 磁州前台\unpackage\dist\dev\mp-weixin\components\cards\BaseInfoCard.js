"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_circle + _component_path + _component_svg + _component_polygon + _component_line + _component_rect + _component_polyline)();
}
const _sfc_main = {
  __name: "BaseInfoCard",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  emits: ["like", "comment", "share"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const hasKeyData = common_vendor.computed(() => {
      const item = props.item;
      return item.price || item.area || (item.minSalary || item.maxSalary) || item.location;
    });
    function formatSalary(min, max, unit = "月") {
      if (!min && !max)
        return "面议";
      if (!max)
        return `${min}K/${unit}以上`;
      if (!min)
        return `${max}K/${unit}以内`;
      return `${min}-${max}K/${unit}`;
    }
    function getCategoryClass(category) {
      const categoryMap = {
        "招聘信息": "category-job",
        "求职信息": "category-resume",
        "房屋出租": "category-house-rent",
        "房屋出售": "category-house-sale",
        "二手闲置": "category-secondhand",
        "二手车辆": "category-used-car",
        "到家服务": "category-home-service",
        "寻找服务": "category-find-service",
        "生意转让": "category-business",
        "宠物信息": "category-pet",
        "商家活动": "category-merchant",
        "婚恋交友": "category-dating",
        "车辆服务": "category-car-service",
        "磁州拼车": "category-carpool",
        "教育培训": "category-education",
        "其他服务": "category-other"
      };
      return categoryMap[category] || "category-other";
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.item.isTopped
      }, __props.item.isTopped ? common_vendor.e({
        b: __props.item.topType === "paid"
      }, __props.item.topType === "paid" ? {
        c: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10"
        }),
        d: common_vendor.p({
          d: "M16 8l-8 8"
        }),
        e: common_vendor.p({
          d: "M8 8l8 8"
        }),
        f: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        })
      } : {
        g: common_vendor.p({
          points: "12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
        }),
        h: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        })
      }, {
        i: common_vendor.t(__props.item.topType === "paid" ? "付费置顶" : "广告置顶"),
        j: __props.item.topType === "paid" ? 1 : "",
        k: __props.item.topType === "ad" ? 1 : ""
      }) : {}, {
        l: __props.item.status
      }, __props.item.status ? {
        m: common_vendor.t(__props.item.status)
      } : {}, {
        n: common_vendor.t(__props.item.category),
        o: common_vendor.n(__props.item.isTopped ? "topped-category" : ""),
        p: common_vendor.n(getCategoryClass(__props.item.category)),
        q: common_vendor.t(__props.item.time),
        r: common_vendor.t(__props.item.content),
        s: __props.item.isTopped ? 1 : "",
        t: __props.item.tags && __props.item.tags.length
      }, __props.item.tags && __props.item.tags.length ? {
        v: common_vendor.f(__props.item.tags, (tag, tagIndex, i0) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        })
      } : {}, {
        w: __props.item.images && __props.item.images.length > 0
      }, __props.item.images && __props.item.images.length > 0 ? common_vendor.e({
        x: __props.item.images[0],
        y: __props.item.images.length > 1
      }, __props.item.images.length > 1 ? {
        z: common_vendor.t(__props.item.images.length - 1)
      } : {}) : {}, {
        A: hasKeyData.value
      }, hasKeyData.value ? common_vendor.e({
        B: __props.item.price
      }, __props.item.price ? common_vendor.e({
        C: common_vendor.p({
          x1: "12",
          y1: "1",
          x2: "12",
          y2: "23"
        }),
        D: common_vendor.p({
          d: "M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
        }),
        E: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        F: common_vendor.t(__props.item.price),
        G: __props.item.priceUnit
      }, __props.item.priceUnit ? {
        H: common_vendor.t(__props.item.priceUnit)
      } : {}) : {}, {
        I: __props.item.area
      }, __props.item.area ? {
        J: common_vendor.p({
          x: "3",
          y: "3",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2"
        }),
        K: common_vendor.p({
          cx: "8.5",
          cy: "8.5",
          r: "1.5"
        }),
        L: common_vendor.p({
          points: "21 15 16 10 5 21"
        }),
        M: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        N: common_vendor.t(__props.item.area)
      } : {}, {
        O: __props.item.minSalary || __props.item.maxSalary
      }, __props.item.minSalary || __props.item.maxSalary ? {
        P: common_vendor.p({
          x: "2",
          y: "7",
          width: "20",
          height: "14",
          rx: "2",
          ry: "2"
        }),
        Q: common_vendor.p({
          d: "M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"
        }),
        R: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        S: common_vendor.t(formatSalary(__props.item.minSalary, __props.item.maxSalary, __props.item.salaryUnit))
      } : {}, {
        T: __props.item.location
      }, __props.item.location ? {
        U: common_vendor.p({
          d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
        }),
        V: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3"
        }),
        W: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        X: common_vendor.t(__props.item.location)
      } : {}) : {}, {
        Y: common_vendor.p({
          d: "M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
        }),
        Z: common_vendor.p({
          cx: "12",
          cy: "7",
          r: "4"
        }),
        aa: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ab: common_vendor.t(__props.item.author || "匿名用户"),
        ac: common_vendor.p({
          d: "M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
        }),
        ad: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "3"
        }),
        ae: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        af: common_vendor.t(__props.item.views || 0),
        ag: __props.item.hasRedPacket
      }, __props.item.hasRedPacket ? {
        ah: common_vendor.p({
          x: "3",
          y: "4",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2"
        }),
        ai: common_vendor.p({
          x1: "16",
          y1: "2",
          x2: "16",
          y2: "6"
        }),
        aj: common_vendor.p({
          x1: "8",
          y1: "2",
          x2: "8",
          y2: "6"
        }),
        ak: common_vendor.p({
          x1: "3",
          y1: "10",
          x2: "21",
          y2: "10"
        }),
        al: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "14",
          height: "14",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        am: common_vendor.t(__props.item.redPacketAmount)
      } : {}, {
        an: common_vendor.p({
          d: "M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
        }),
        ao: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ap: common_vendor.t(__props.item.likes || 0),
        aq: common_vendor.p({
          d: "M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"
        }),
        ar: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        as: common_vendor.t(__props.item.comments || 0),
        at: common_vendor.p({
          cx: "18",
          cy: "5",
          r: "3"
        }),
        av: common_vendor.p({
          cx: "6",
          cy: "12",
          r: "3"
        }),
        aw: common_vendor.p({
          cx: "18",
          cy: "19",
          r: "3"
        }),
        ax: common_vendor.p({
          x1: "8.59",
          y1: "13.51",
          x2: "15.42",
          y2: "17.49"
        }),
        ay: common_vendor.p({
          x1: "15.41",
          y1: "6.51",
          x2: "8.59",
          y2: "10.49"
        }),
        az: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        aA: __props.item.isTopped ? 1 : "",
        aB: __props.item.topType === "paid" ? 1 : "",
        aC: __props.item.topType === "ad" ? 1 : ""
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/cards/BaseInfoCard.js.map
