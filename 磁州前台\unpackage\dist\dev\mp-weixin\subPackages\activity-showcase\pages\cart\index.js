"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const isEditMode = common_vendor.ref(false);
    const cartItems = common_vendor.ref([]);
    const recommendProducts = common_vendor.ref([
      {
        id: "101",
        name: "Apple AirPods Pro 2代",
        image: "https://via.placeholder.com/300x300",
        price: 1999
      },
      {
        id: "102",
        name: "戴森吹风机 Supersonic HD08",
        image: "https://via.placeholder.com/300x300",
        price: 3190
      },
      {
        id: "103",
        name: "NIKE Air Jordan 1 高帮篮球鞋",
        image: "https://via.placeholder.com/300x300",
        price: 1299
      },
      {
        id: "104",
        name: "三星Galaxy Watch 5 Pro",
        image: "https://via.placeholder.com/300x300",
        price: 2999
      }
    ]);
    const selectedItemIds = common_vendor.ref([]);
    const cartShops = common_vendor.computed(() => {
      const shops = [];
      const shopMap = {};
      cartItems.value.forEach((item) => {
        if (!shopMap[item.shopId]) {
          shopMap[item.shopId] = {
            id: item.shopId,
            name: item.shopName,
            logo: item.shopLogo,
            items: []
          };
          shops.push(shopMap[item.shopId]);
        }
        shopMap[item.shopId].items.push(item);
      });
      return shops;
    });
    const isAllSelected = common_vendor.computed(() => {
      return cartItems.value.length > 0 && selectedItemIds.value.length === cartItems.value.length;
    });
    const selectedCount = common_vendor.computed(() => {
      return selectedItemIds.value.length;
    });
    const totalPrice = common_vendor.computed(() => {
      let total = 0;
      cartItems.value.forEach((item) => {
        if (selectedItemIds.value.includes(item.id)) {
          total += item.price * item.quantity;
        }
      });
      return total;
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const toggleEditMode = () => {
      isEditMode.value = !isEditMode.value;
    };
    const viewProduct = (productId) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/products/detail/index?id=${productId}`
      });
    };
    const viewShop = (shopId) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/shops/detail?id=${shopId}`
      });
    };
    const goShopping = () => {
      common_vendor.index.switchTab({
        url: "/subPackages/activity-showcase/pages/discover/index"
      });
    };
    const increaseQuantity = (item) => {
      if (item.quantity < 99) {
        item.quantity++;
      }
    };
    const decreaseQuantity = (item) => {
      if (item.quantity > 1) {
        item.quantity--;
      }
    };
    const toggleItemSelect = (itemId) => {
      const index = selectedItemIds.value.indexOf(itemId);
      if (index === -1) {
        selectedItemIds.value.push(itemId);
      } else {
        selectedItemIds.value.splice(index, 1);
      }
    };
    const toggleShopSelect = (shopId) => {
      const shop = cartShops.value.find((s) => s.id === shopId);
      if (!shop)
        return;
      const shopItemIds = shop.items.map((item) => item.id);
      const allSelected = shopItemIds.every((id) => selectedItemIds.value.includes(id));
      if (allSelected) {
        selectedItemIds.value = selectedItemIds.value.filter((id) => !shopItemIds.includes(id));
      } else {
        shopItemIds.forEach((id) => {
          if (!selectedItemIds.value.includes(id)) {
            selectedItemIds.value.push(id);
          }
        });
      }
    };
    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedItemIds.value = [];
      } else {
        selectedItemIds.value = cartItems.value.map((item) => item.id);
      }
    };
    const isItemSelected = (itemId) => {
      return selectedItemIds.value.includes(itemId);
    };
    const isShopSelected = (shopId) => {
      const shop = cartShops.value.find((s) => s.id === shopId);
      if (!shop)
        return false;
      return shop.items.every((item) => selectedItemIds.value.includes(item.id));
    };
    const deleteSelected = () => {
      if (selectedItemIds.value.length === 0) {
        common_vendor.index.showToast({
          title: "请选择要删除的商品",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除选中的商品吗？",
        success: (res) => {
          if (res.confirm) {
            cartItems.value = cartItems.value.filter((item) => !selectedItemIds.value.includes(item.id));
            selectedItemIds.value = [];
          }
        }
      });
    };
    const checkout = () => {
      if (selectedItemIds.value.length === 0) {
        common_vendor.index.showToast({
          title: "请选择要结算的商品",
          icon: "none"
        });
        return;
      }
      cartItems.value.filter((item) => selectedItemIds.value.includes(item.id));
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/orders/confirm?from=cart`
      });
    };
    const loadMore = () => {
      common_vendor.index.showToast({
        title: "已加载全部数据",
        icon: "none"
      });
    };
    const initCartData = () => {
      cartItems.value = [
        {
          id: "1",
          shopId: "shop1",
          shopName: "Apple授权专卖店",
          shopLogo: "https://via.placeholder.com/100",
          name: "iPhone 14 Pro 深空黑 256G",
          specs: "颜色：深空黑；内存：256G",
          price: 7999,
          quantity: 1,
          image: "https://via.placeholder.com/300x300"
        },
        {
          id: "2",
          shopId: "shop1",
          shopName: "Apple授权专卖店",
          shopLogo: "https://via.placeholder.com/100",
          name: "Apple Watch Series 8 GPS 45mm",
          specs: "颜色：午夜色；尺寸：45mm",
          price: 3499,
          quantity: 1,
          image: "https://via.placeholder.com/300x300"
        },
        {
          id: "3",
          shopId: "shop2",
          shopName: "华为官方旗舰店",
          shopLogo: "https://via.placeholder.com/100",
          name: "华为Mate 50 Pro 曜金黑 512G",
          specs: "颜色：曜金黑；内存：512G",
          price: 6999,
          quantity: 1,
          image: "https://via.placeholder.com/300x300"
        }
      ];
      selectedItemIds.value = cartItems.value.map((item) => item.id);
    };
    common_vendor.onMounted(() => {
      initCartData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(goBack),
        d: common_vendor.t(isEditMode.value ? "完成" : "编辑"),
        e: common_vendor.o(toggleEditMode),
        f: cartItems.value.length > 0
      }, cartItems.value.length > 0 ? {
        g: common_vendor.f(cartShops.value, (shop, shopIndex, i0) => {
          return common_vendor.e({
            a: isShopSelected(shop.id)
          }, isShopSelected(shop.id) ? {
            b: "a2ece00a-3-" + i0 + "," + ("a2ece00a-2-" + i0),
            c: common_vendor.p({
              d: "M20 6L9 17l-5-5",
              stroke: "#FFFFFF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            d: "a2ece00a-2-" + i0,
            e: common_vendor.p({
              viewBox: "0 0 24 24",
              width: "16",
              height: "16"
            })
          } : {}, {
            f: isShopSelected(shop.id) ? 1 : "",
            g: common_vendor.o(($event) => toggleShopSelect(shop.id), shopIndex),
            h: shop.logo,
            i: common_vendor.t(shop.name),
            j: "a2ece00a-5-" + i0 + "," + ("a2ece00a-4-" + i0),
            k: "a2ece00a-4-" + i0,
            l: common_vendor.o(($event) => viewShop(shop.id), shopIndex),
            m: common_vendor.f(shop.items, (item, itemIndex, i1) => {
              return common_vendor.e({
                a: isItemSelected(item.id)
              }, isItemSelected(item.id) ? {
                b: "a2ece00a-7-" + i0 + "-" + i1 + "," + ("a2ece00a-6-" + i0 + "-" + i1),
                c: common_vendor.p({
                  d: "M20 6L9 17l-5-5",
                  stroke: "#FFFFFF",
                  ["stroke-width"]: "2",
                  ["stroke-linecap"]: "round",
                  ["stroke-linejoin"]: "round"
                }),
                d: "a2ece00a-6-" + i0 + "-" + i1,
                e: common_vendor.p({
                  viewBox: "0 0 24 24",
                  width: "16",
                  height: "16"
                })
              } : {}, {
                f: isItemSelected(item.id) ? 1 : "",
                g: common_vendor.o(($event) => toggleItemSelect(item.id), itemIndex),
                h: item.image,
                i: common_vendor.o(($event) => viewProduct(item.id), itemIndex),
                j: common_vendor.t(item.name),
                k: common_vendor.t(item.specs),
                l: common_vendor.t(item.price.toFixed(2)),
                m: "a2ece00a-9-" + i0 + "-" + i1 + "," + ("a2ece00a-8-" + i0 + "-" + i1),
                n: "a2ece00a-8-" + i0 + "-" + i1,
                o: common_vendor.o(($event) => decreaseQuantity(item), itemIndex),
                p: common_vendor.t(item.quantity),
                q: "a2ece00a-11-" + i0 + "-" + i1 + "," + ("a2ece00a-10-" + i0 + "-" + i1),
                r: "a2ece00a-10-" + i0 + "-" + i1,
                s: common_vendor.o(($event) => increaseQuantity(item), itemIndex),
                t: common_vendor.o(($event) => viewProduct(item.id), itemIndex),
                v: itemIndex
              });
            }),
            n: shopIndex
          });
        }),
        h: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        i: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        j: common_vendor.p({
          d: "M5 12h14",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        k: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        l: common_vendor.p({
          d: "M12 5v14M5 12h14",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        n: common_vendor.f(recommendProducts.value, (product, index, i0) => {
          return {
            a: product.image,
            b: common_vendor.t(product.name),
            c: common_vendor.t(product.price.toFixed(2)),
            d: index,
            e: common_vendor.o(($event) => viewProduct(product.id), index)
          };
        }),
        o: common_vendor.o(loadMore)
      } : {
        p: common_assets._imports_0$63,
        q: common_vendor.o(goShopping)
      }, {
        r: cartItems.value.length > 0
      }, cartItems.value.length > 0 ? common_vendor.e({
        s: isAllSelected.value
      }, isAllSelected.value ? {
        t: common_vendor.p({
          d: "M20 6L9 17l-5-5",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        })
      } : {}, {
        w: isAllSelected.value ? 1 : "",
        x: common_vendor.o(toggleSelectAll),
        y: !isEditMode.value
      }, !isEditMode.value ? {
        z: common_vendor.t(totalPrice.value.toFixed(2)),
        A: common_vendor.t(selectedCount.value),
        B: common_vendor.o(checkout)
      } : {
        C: common_vendor.o(deleteSelected)
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a2ece00a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/cart/index.js.map
