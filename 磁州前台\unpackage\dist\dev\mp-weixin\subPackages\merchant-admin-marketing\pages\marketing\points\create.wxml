<view class="points-create-container"><view class="navbar"><view class="navbar-left"><view class="back-button" bindtap="{{c}}"><svg wx:if="{{b}}" u-s="{{['d']}}" u-i="785a3324-0" bind:__l="__l" u-p="{{b}}"><path wx:if="{{a}}" u-i="785a3324-1,785a3324-0" bind:__l="__l" u-p="{{a}}"/></svg></view></view><view class="navbar-title"><text class="title-text">创建积分商品</text></view><view class="navbar-right"></view></view><scroll-view scroll-y class="content-area"><view class="form-section"><view class="form-group"><view class="form-title">基本信息</view><view class="form-item"><text class="form-label">商品名称</text><input class="form-input" placeholder="请输入积分商品名称" value="{{d}}" bindinput="{{e}}"/></view><view class="form-item"><text class="form-label">所需积分</text><input class="form-input" type="number" placeholder="请输入兑换所需积分" value="{{f}}" bindinput="{{g}}"/></view><view class="form-item"><text class="form-label">库存数量</text><input class="form-input" type="number" placeholder="请输入商品库存" value="{{h}}" bindinput="{{i}}"/></view></view><view class="form-group"><view class="form-title">商品图片</view><view class="image-uploader"><view wx:if="{{j}}" class="upload-box" bindtap="{{m}}"><svg wx:if="{{l}}" u-s="{{['d']}}" u-i="785a3324-2" bind:__l="__l" u-p="{{l}}"><path wx:if="{{k}}" u-i="785a3324-3,785a3324-2" bind:__l="__l" u-p="{{k}}"/></svg><text class="upload-text">上传图片</text></view><view wx:else class="image-preview"><image class="preview-image" src="{{n}}" mode="aspectFill"></image><view class="delete-image" bindtap="{{q}}"><svg wx:if="{{p}}" u-s="{{['d']}}" u-i="785a3324-4" bind:__l="__l" u-p="{{p}}"><path wx:if="{{o}}" u-i="785a3324-5,785a3324-4" bind:__l="__l" u-p="{{o}}"/></svg></view></view></view></view><view class="form-group"><view class="form-title">有效期</view><view class="form-item"><text class="form-label">开始时间</text><picker mode="date" value="{{s}}" bindchange="{{t}}" class="date-picker"><view class="picker-value">{{r}}</view></picker></view><view class="form-item"><text class="form-label">结束时间</text><picker mode="date" value="{{w}}" bindchange="{{x}}" class="date-picker"><view class="picker-value">{{v}}</view></picker></view></view><view class="form-group"><view class="form-title">商品描述</view><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入商品描述信息" value="{{y}}" bindinput="{{z}}"/></block></view><view class="form-group"><view class="form-title">兑换规则</view><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入兑换规则" value="{{A}}" bindinput="{{B}}"/></block></view></view><view class="bottom-space"></view></scroll-view><view class="bottom-bar"><view class="action-button save-draft" bindtap="{{C}}">保存草稿</view><view class="action-button publish" bindtap="{{D}}">立即发布</view></view></view>