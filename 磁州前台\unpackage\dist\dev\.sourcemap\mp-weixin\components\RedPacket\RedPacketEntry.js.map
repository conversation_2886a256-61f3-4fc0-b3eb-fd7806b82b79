{"version": 3, "file": "RedPacketEntry.js", "sources": ["components/RedPacket/RedPacketEntry.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9SZWRQYWNrZXQvUmVkUGFja2V0RW50cnkudnVl"], "sourcesContent": ["<template>\n  <view>\n    <!-- 红包入口卡片 -->\n    <view class=\"red-packet-card\" @click=\"showDialog = true\">\n      <image class=\"icon\" src=\"/static/images/red-packet-apple.svg\" />\n      <view class=\"info\">\n        <view class=\"title\">任务红包</view>\n        <view class=\"desc\">设置任务红包，用户转发/助力后可抢</view>\n      </view>\n      <view class=\"status\" :class=\"{ set: isSet }\">{{ isSet ? '已设置' : '未设置' }}</view>\n      <image class=\"arrow\" src=\"/static/images/arrow-right.svg\" />\n    </view>\n\n    <!-- 红包设置弹窗 -->\n    <uni-popup v-model=\"showDialog\" type=\"bottom\">\n      <view class=\"red-packet-setup-popup\">\n        <view class=\"popup-title\">设置任务红包</view>\n        <view class=\"form-row\">\n          <text class=\"label\">红包总金额</text>\n          <input v-model=\"form.amount\" type=\"number\" placeholder=\"请输入金额\" />\n          <text class=\"unit\">元</text>\n        </view>\n        <view class=\"form-row\">\n          <text class=\"label\">红包个数</text>\n          <input v-model=\"form.count\" type=\"number\" placeholder=\"请输入个数\" />\n          <text class=\"unit\">个</text>\n        </view>\n        <view class=\"form-row\">\n          <text class=\"label\">任务要求</text>\n          <picker :range=\"taskOptions\" :value=\"form.taskType\" @change=\"onTaskTypeChange\">\n            <view class=\"picker-value\">{{ taskOptions[form.taskType] }}</view>\n          </picker>\n        </view>\n        <button class=\"primary-btn\" @click=\"confirmRedPacket\">确定</button>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script setup>\nimport { ref, watch, computed } from 'vue'\nconst props = defineProps({ modelValue: Object })\nconst emit = defineEmits(['update:modelValue'])\n\nconst showDialog = ref(false)\nconst form = ref({\n  amount: '',\n  count: '',\n  taskType: 0\n})\nconst taskOptions = ['转发到群', '邀请助力', '完成浏览', '自定义']\n\nconst isSet = computed(() => !!form.value.amount && !!form.value.count)\n\nwatch(\n  () => props.modelValue,\n  (val) => {\n    if (val) Object.assign(form.value, val)\n  },\n  { immediate: true }\n)\n\nfunction confirmRedPacket() {\n  if (!form.value.amount || !form.value.count) {\n    uni.showToast({ title: '请填写完整', icon: 'none' })\n    return\n  }\n  emit('update:modelValue', { ...form.value })\n  showDialog.value = false\n}\n\nfunction onTaskTypeChange(e) {\n  form.value.taskType = e.detail.value\n}\n</script>\n\n<style scoped>\n.red-packet-card {\n  display: flex;\n  align-items: center;\n  background: #fff;\n  border-radius: 18rpx;\n  box-shadow: 0 4rpx 16rpx rgba(22,119,255,0.08);\n  padding: 24rpx 30rpx;\n  margin: 32rpx 0;\n  cursor: pointer;\n}\n.icon {\n  width: 60rpx;\n  height: 60rpx;\n  margin-right: 20rpx;\n}\n.info {\n  flex: 1;\n}\n.title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #1677FF;\n}\n.desc {\n  font-size: 24rpx;\n  color: #888;\n  margin-top: 4rpx;\n}\n.status {\n  font-size: 24rpx;\n  color: #bbb;\n  margin-right: 16rpx;\n}\n.status.set {\n  color: #1677FF;\n}\n.arrow {\n  width: 32rpx;\n  height: 32rpx;\n}\n.red-packet-setup-popup {\n  background: #fff;\n  border-radius: 24rpx 24rpx 0 0;\n  padding: 40rpx 30rpx 30rpx 30rpx;\n}\n.popup-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #222;\n  text-align: center;\n  margin-bottom: 30rpx;\n}\n.form-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 28rpx;\n}\n.label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #333;\n}\ninput {\n  flex: 1;\n  font-size: 28rpx;\n  border: none;\n  border-bottom: 1rpx solid #eee;\n  background: transparent;\n  padding: 10rpx 0;\n  margin: 0 10rpx;\n}\n.unit {\n  font-size: 26rpx;\n  color: #888;\n}\n.picker-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #1677FF;\n  padding: 10rpx 0;\n}\n.primary-btn {\n  width: 100%;\n  height: 80rpx;\n  background: linear-gradient(90deg, #1677FF 60%, #50aaff 100%);\n  color: #fff;\n  font-size: 30rpx;\n  border-radius: 40rpx;\n  margin-top: 30rpx;\n  font-weight: 600;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/RedPacket/RedPacketEntry.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "watch", "uni"], "mappings": ";;;;;;;;;;;;AAyCA,UAAM,QAAQ;AACd,UAAM,OAAO;AAEb,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,cAAc,CAAC,QAAQ,QAAQ,QAAQ,KAAK;AAElD,UAAM,QAAQC,cAAQ,SAAC,MAAM,CAAC,CAAC,KAAK,MAAM,UAAU,CAAC,CAAC,KAAK,MAAM,KAAK;AAEtEC,kBAAK;AAAA,MACH,MAAM,MAAM;AAAA,MACZ,CAAC,QAAQ;AACP,YAAI;AAAK,iBAAO,OAAO,KAAK,OAAO,GAAG;AAAA,MACvC;AAAA,MACD,EAAE,WAAW,KAAM;AAAA,IACrB;AAEA,aAAS,mBAAmB;AAC1B,UAAI,CAAC,KAAK,MAAM,UAAU,CAAC,KAAK,MAAM,OAAO;AAC3CC,sBAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,QAAQ;AAC9C;AAAA,MACD;AACD,WAAK,qBAAqB,EAAE,GAAG,KAAK,MAAK,CAAE;AAC3C,iBAAW,QAAQ;AAAA,IACrB;AAEA,aAAS,iBAAiB,GAAG;AAC3B,WAAK,MAAM,WAAW,EAAE,OAAO;AAAA,IACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxEA,GAAG,gBAAgB,SAAS;"}