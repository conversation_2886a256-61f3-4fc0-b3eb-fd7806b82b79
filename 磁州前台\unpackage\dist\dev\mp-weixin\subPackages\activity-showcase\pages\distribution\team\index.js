"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const showInviteModal = common_vendor.ref(false);
    const teamStats = common_vendor.ref({
      totalMembers: 28,
      directMembers: 12,
      indirectMembers: 16,
      newMembers: 5
    });
    const teamTabs = common_vendor.ref([
      { name: "全部成员", type: "all" },
      { name: "直属成员", type: "direct" },
      { name: "间接成员", type: "indirect" },
      { name: "本月新增", type: "new" }
    ]);
    const memberList = common_vendor.ref([
      {
        id: "M001",
        name: "李小明",
        avatar: "https://via.placeholder.com/80",
        level: "高级分销商",
        joinTime: "2023-12-15",
        type: "direct",
        isOnline: true,
        orderCount: 45,
        contribution: 1234.56,
        teamSize: 8,
        monthlyOrders: 12,
        isNew: false
      },
      {
        id: "M002",
        name: "王小红",
        avatar: "https://via.placeholder.com/80",
        level: "中级分销商",
        joinTime: "2024-01-08",
        type: "direct",
        isOnline: false,
        orderCount: 28,
        contribution: 856.78,
        teamSize: 3,
        monthlyOrders: 8,
        isNew: true
      },
      {
        id: "M003",
        name: "张小华",
        avatar: "https://via.placeholder.com/80",
        level: "初级分销商",
        joinTime: "2024-01-12",
        type: "indirect",
        isOnline: true,
        orderCount: 15,
        contribution: 345.67,
        teamSize: 0,
        monthlyOrders: 6,
        isNew: true
      },
      {
        id: "M004",
        name: "刘小强",
        avatar: "https://via.placeholder.com/80",
        level: "中级分销商",
        joinTime: "2023-11-20",
        type: "direct",
        isOnline: false,
        orderCount: 32,
        contribution: 967.89,
        teamSize: 5,
        monthlyOrders: 10,
        isNew: false
      }
    ]);
    const filteredMembers = common_vendor.computed(() => {
      const type = teamTabs.value[currentTab.value].type;
      switch (type) {
        case "all":
          return memberList.value;
        case "direct":
          return memberList.value.filter((member) => member.type === "direct");
        case "indirect":
          return memberList.value.filter((member) => member.type === "indirect");
        case "new":
          return memberList.value.filter((member) => member.isNew);
        default:
          return memberList.value;
      }
    });
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const getLevelColor = (level) => {
      switch (level) {
        case "高级分销商":
          return "linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)";
        case "中级分销商":
          return "linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%)";
        case "初级分销商":
          return "linear-gradient(135deg, #45B7D1 0%, #96C93D 100%)";
        default:
          return "linear-gradient(135deg, #8E8E93 0%, #A8A8A8 100%)";
      }
    };
    const viewMemberDetail = (member) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/distribution/member-detail/index?id=${member.id}`
      });
    };
    const sendMessage = (member) => {
      common_vendor.index.showToast({
        title: `向${member.name}发送私信`,
        icon: "none"
      });
    };
    const viewTeamDetail = (member) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/distribution/team/detail/index?id=${member.id}`
      });
    };
    const showInviteQrcode = () => {
      showInviteModal.value = true;
    };
    const hideInviteQrcode = () => {
      showInviteModal.value = false;
    };
    const shareToWechat = () => {
      common_vendor.index.showToast({
        title: "分享到微信功能开发中",
        icon: "none"
      });
    };
    const copyInviteLink = () => {
      common_vendor.index.setClipboardData({
        data: "https://example.com/invite?code=ABC123",
        success: () => {
          common_vendor.index.showToast({
            title: "邀请链接已复制",
            icon: "success"
          });
        }
      });
    };
    common_vendor.onMounted(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(navigateBack),
        d: common_vendor.p({
          d: "M12 5v14M5 12h14",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        f: common_vendor.o(showInviteQrcode),
        g: common_vendor.t(teamStats.value.totalMembers),
        h: common_vendor.t(teamStats.value.directMembers),
        i: common_vendor.t(teamStats.value.indirectMembers),
        j: common_vendor.t(teamStats.value.newMembers),
        k: common_vendor.f(teamTabs.value, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.name),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        l: common_vendor.f(filteredMembers.value, (member, k0, i0) => {
          return common_vendor.e({
            a: member.avatar,
            b: member.isOnline
          }, member.isOnline ? {} : {}, {
            c: common_vendor.t(member.name),
            d: common_vendor.t(member.level),
            e: getLevelColor(member.level),
            f: common_vendor.t(member.joinTime),
            g: common_vendor.t(member.id),
            h: "c16fb8e4-5-" + i0 + "," + ("c16fb8e4-4-" + i0),
            i: "c16fb8e4-4-" + i0,
            j: common_vendor.t(member.orderCount),
            k: common_vendor.t(member.contribution),
            l: common_vendor.t(member.teamSize),
            m: common_vendor.t(member.monthlyOrders),
            n: member.type === "direct"
          }, member.type === "direct" ? {
            o: "c16fb8e4-7-" + i0 + "," + ("c16fb8e4-6-" + i0),
            p: common_vendor.p({
              d: "M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z",
              stroke: "#AC39FF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            q: "c16fb8e4-6-" + i0,
            r: common_vendor.p({
              viewBox: "0 0 24 24",
              width: "16",
              height: "16"
            }),
            s: common_vendor.o(($event) => sendMessage(member), member.id),
            t: "c16fb8e4-9-" + i0 + "," + ("c16fb8e4-8-" + i0),
            v: common_vendor.p({
              d: "M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75",
              stroke: "#FFFFFF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            w: "c16fb8e4-8-" + i0,
            x: common_vendor.p({
              viewBox: "0 0 24 24",
              width: "16",
              height: "16"
            }),
            y: common_vendor.o(($event) => viewTeamDetail(member), member.id)
          } : {}, {
            z: member.id,
            A: common_vendor.o(($event) => viewMemberDetail(member), member.id)
          });
        }),
        m: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#C7C7CC",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        o: filteredMembers.value.length === 0
      }, filteredMembers.value.length === 0 ? {
        p: common_vendor.p({
          d: "M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75",
          stroke: "#C7C7CC",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        q: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "80",
          height: "80"
        }),
        r: common_vendor.t(teamTabs.value[currentTab.value].name),
        s: common_vendor.o(showInviteQrcode)
      } : {}, {
        t: showInviteModal.value
      }, showInviteModal.value ? {
        v: common_vendor.p({
          d: "M18 6L6 18M6 6l12 12",
          stroke: "#8E8E93",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        x: common_vendor.o(hideInviteQrcode),
        y: common_vendor.o(shareToWechat),
        z: common_vendor.o(copyInviteLink),
        A: common_vendor.o(() => {
        }),
        B: common_vendor.o(hideInviteQrcode)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c16fb8e4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/distribution/team/index.js.map
