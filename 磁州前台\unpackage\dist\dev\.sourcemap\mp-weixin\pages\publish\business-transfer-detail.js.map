{"version": 3, "file": "business-transfer-detail.js", "sources": ["pages/publish/business-transfer-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9idXNpbmVzcy10cmFuc2Zlci1kZXRhaWwudnVl"], "sourcesContent": ["<template>\n  <view class=\"detail-container business-transfer-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">店铺转让详情</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <!-- 隐藏的Canvas用于绘制海报 -->\n    <canvas canvas-id=\"posterCanvas\" class=\"poster-canvas\" style=\"width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;\"></canvas>\n    \n    <!-- 悬浮海报按钮 -->\n    <view class=\"float-poster-btn\" @click=\"generateShareImage\">\n      <image src=\"/static/images/tabbar/海报.png\" class=\"poster-icon\"></image>\n      <text class=\"poster-text\">海报</text>\n    </view>\n    \n    <!-- 举报按钮 -->\n    <!-- 悬浮举报按钮已删除 -->\n    \n    <view class=\"detail-wrapper business-transfer-wrapper\">\n      <!-- 转让基本信息卡片 -->\n      <view class=\"content-card transfer-info-card\">\n        <view class=\"transfer-header\">\n          <view class=\"title-row\" style=\"flex-direction: column; align-items: flex-start;\">\n            <text class=\"main-title\">{{transferData.title}}</text>\n            <text class=\"price-text\">{{transferData.price}}</text>\n          </view>\n          <view class=\"meta-info\">\n            <view class=\"tag-group\">\n              <view class=\"info-tag\" v-for=\"(tag, index) in transferData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"publish-time\">发布于 {{formatTime(transferData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <!-- 店铺图片轮播 -->\n        <swiper class=\"detail-swiper\" :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\">\n          <swiper-item v-for=\"(image, index) in transferData.images\" :key=\"index\">\n            <image :src=\"image\" mode=\"aspectFill\" class=\"swiper-image\"></image>\n          </swiper-item>\n        </swiper>\n        \n        <!-- 基本信息 -->\n        <view class=\"basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">店铺类型</text>\n            <text class=\"info-value\">{{transferData.type}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">所在区域</text>\n            <text class=\"info-value\">{{transferData.area}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">经营年限</text>\n            <text class=\"info-value\">{{transferData.years}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">店铺面积</text>\n            <text class=\"info-value\">{{transferData.size}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 店铺详情 -->\n      <view class=\"content-card shop-detail-card\">\n        <view class=\"section-title\">店铺详情</view>\n        <view class=\"detail-list\">\n          <view class=\"detail-item\" v-for=\"(item, index) in transferData.details\" :key=\"index\">\n            <text class=\"detail-label\">{{item.label}}</text>\n            <text class=\"detail-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 经营状况 -->\n      <view class=\"content-card business-status-card\">\n        <view class=\"section-title\">经营状况</view>\n        <view class=\"detail-list\">\n          <view class=\"detail-item\">\n            <text class=\"detail-label\">月营业额</text>\n            <text class=\"detail-value\">{{transferData.monthlyRevenue}}</text>\n          </view>\n          <view class=\"detail-item\">\n            <text class=\"detail-label\">月利润</text>\n            <text class=\"detail-value\">{{transferData.monthlyProfit}}</text>\n          </view>\n          <view class=\"detail-item\">\n            <text class=\"detail-label\">客流量</text>\n            <text class=\"detail-value\">{{transferData.customerFlow}}</text>\n          </view>\n          <view class=\"detail-item\">\n            <text class=\"detail-label\">员工数量</text>\n            <text class=\"detail-value\">{{transferData.employeeCount}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 转让说明 -->\n      <view class=\"content-card transfer-notes-card\">\n        <view class=\"section-title\">转让说明</view>\n        <view class=\"description-content\">\n          <rich-text :nodes=\"transferData.notes\" class=\"description-text\"></rich-text>\n        </view>\n      </view>\n      \n      <!-- 转让原因 -->\n      <view class=\"content-card transfer-reason-card\">\n        <view class=\"section-title\">转让原因</view>\n        <view class=\"description-content\">\n          <text class=\"description-text\">{{transferData.reason}}</text>\n        </view>\n      </view>\n      \n      <!-- 发布者信息 -->\n      <view class=\"content-card publisher-card\">\n        <view class=\"section-title\">发布者信息</view>\n        <view class=\"publisher-header\">\n          <view class=\"avatar-container\">\n            <image :src=\"transferData.publisher.avatar\" mode=\"aspectFill\" class=\"avatar-image\"></image>\n          </view>\n          <view class=\"publisher-info\">\n            <text class=\"publisher-name\">{{transferData.publisher.name}}</text>\n            <view class=\"publisher-meta\">\n              <text class=\"meta-text\">{{transferData.publisher.type}}</text>\n              <text class=\"meta-text\">信用等级 {{transferData.publisher.rating}}</text>\n              <view class=\"meta-text\" v-if=\"transferData.publisher.isVerified\">\n                <text class=\"iconfont icon-verified\"></text>\n                <text>已认证</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"section-title\">联系方式</view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{transferData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{transferData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 举报卡片 -->\n      <report-card :content-id=\"transferData.id\" content-type=\"business-transfer\"></report-card>\n      \n      <!-- 相关店铺推荐 -->\n      <view class=\"content-card related-shops-card\">\n        <view class=\"section-title\">相关店铺推荐</view>\n        <view class=\"related-shops-content\">\n          <!-- 简洁的店铺列表 -->\n          <view class=\"related-shops-list\">\n            <view class=\"related-shop-item\" \n                 v-for=\"(shop, index) in relatedShops.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToShopDetail(shop.id)\">\n              <view class=\"shop-item-content\">\n                <view class=\"shop-item-left\">\n                  <image class=\"shop-logo\" :src=\"shop.image\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"shop-item-middle\">\n                  <text class=\"shop-item-title\">{{shop.title}}</text>\n                  <view class=\"shop-item-area\">{{shop.area}}</view>\n                  <view class=\"shop-item-tags\">\n                    <text class=\"shop-item-tag\" v-for=\"(tag, tagIndex) in shop.tags.slice(0, 2)\" :key=\"tagIndex\">{{tag}}</text>\n                    <text class=\"shop-item-tag-more\" v-if=\"shop.tags.length > 2\">+{{shop.tags.length - 2}}</text>\n                  </view>\n                </view>\n                <view class=\"shop-item-right\">\n                  <text class=\"shop-item-price\">{{shop.price}}</text>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 暂无数据提示 -->\n            <view class=\"empty-related-shops\" v-if=\"relatedShops.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关店铺</text>\n            </view>\n          </view>\n          \n          <!-- 查看更多按钮 -->\n          <view class=\"view-more-btn\" v-if=\"relatedShops.length > 0\" @click.stop=\"navigateToTransferList\">\n            <text class=\"view-more-text\">查看更多店铺信息</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n        </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport ReportCard from '@/components/ReportCard.vue'\nimport mockApi from '@/mock/api'\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 获取状态栏高度\nconst statusBarHeight = ref(20); // 默认值\n\n// 响应式数据\nconst isCollected = ref(false);\nconst transferData = ref({});\nconst relatedShops = ref([]);\n\n// 加载生意转让详情数据\nconst loadTransferDetail = (id) => {\n  uni.showLoading({\n    title: '加载中'\n  });\n  \n  // 使用模拟API获取生意转让详情\n  mockApi.publish.getBusinessTransferDetail(id).then(data => {\n    transferData.value = data;\n    uni.hideLoading();\n  }).catch(err => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '获取详情失败',\n      icon: 'none'\n    });\n    console.error('获取详情失败:', err);\n  });\n};\n\n// 加载相关店铺数据\nconst loadRelatedShops = () => {\n  // 使用模拟API获取相关店铺数据\n  mockApi.publish.getRelatedShops().then(data => {\n    relatedShops.value = data;\n  }).catch(err => {\n    console.error('获取相关店铺失败:', err);\n  });\n};\n\n// 跳转到店铺详情页\nconst navigateToShopDetail = (id) => {\n  // 避免重复跳转当前页面\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.$page?.options || {};\n  \n  if (id === options.id) {\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/pages/business/shop-detail?id=${id}`\n  });\n};\n\n// 跳转到生意转让列表页\nconst navigateToTransferList = (e) => {\n  if (e) e.stopPropagation();\n  // 跳转到生意转让信息列表页\n  const transferCategory = transferData.value.tags?.[0] || '';\n  uni.navigateTo({ \n    url: `/subPackages/service/pages/filter?type=business-transfer&title=${encodeURIComponent('生意转让')}&category=${encodeURIComponent(transferCategory)}&active=business` \n  });\n};\n\n// 方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: transferData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  if (!transferData.value.publisher || !transferData.value.publisher.id) {\n    uni.showToast({\n      title: '无法获取发布者信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/pages/message/chat?userId=${transferData.value.publisher.id}&userName=${transferData.value.publisher.name}`\n  });\n};\n\n// 生成分享海报\nconst generateShareImage = () => {\n  uni.showLoading({\n    title: '正在生成海报...',\n    mask: true\n  });\n  \n  // 创建海报数据对象\n  const posterData = {\n    title: transferData.value.title,\n    price: transferData.value.price,\n    type: transferData.value.type,\n    area: transferData.value.area,\n    address: transferData.value.address,\n    phone: transferData.value.contact.phone,\n    description: transferData.value.description ? transferData.value.description.substring(0, 60) + '...' : '',\n    qrcode: '/static/images/tabbar/客服微信.png',\n    logo: '/static/images/tabbar/生意转让.png',\n    bgImage: transferData.value.images[0] || '/static/images/banner/banner-1.png'\n  };\n  \n  // #ifdef H5\n  // H5环境不支持canvas绘制图片保存，提示用户\n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showModal({\n      title: '提示',\n      content: 'H5环境暂不支持保存海报，请使用App或小程序',\n      showCancel: false\n    });\n  }, 1000);\n  return;\n  // #endif\n  \n  // 绘制海报\n  const ctx = uni.createCanvasContext('posterCanvas');\n  \n  // 绘制背景\n  ctx.save();\n  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);\n  // 添加半透明蒙层\n  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');\n  ctx.fillRect(0, 0, 600, 900);\n  ctx.restore();\n  \n  // 绘制白色卡片背景\n  ctx.save();\n  ctx.setFillStyle('#ffffff');\n  ctx.fillRect(30, 280, 540, 550);\n  ctx.restore();\n  \n  // 绘制Logo\n  ctx.save();\n  ctx.beginPath();\n  ctx.arc(300, 200, 80, 0, 2 * Math.PI);\n  ctx.setFillStyle('#ffffff');\n  ctx.fill();\n  // 在圆形内绘制Logo\n  ctx.clip();\n  ctx.drawImage(posterData.logo, 220, 120, 160, 160);\n  ctx.restore();\n  \n  // 绘制标题\n  ctx.setFillStyle('#333333');\n  ctx.setFontSize(32);\n  ctx.setTextAlign('center');\n  ctx.fillText(posterData.title, 300, 350);\n  \n  // 绘制价格\n  ctx.setFillStyle('#FF6B6B');\n  ctx.setFontSize(28);\n  ctx.fillText(posterData.price, 300, 400);\n  \n  // 分割线\n  ctx.beginPath();\n  ctx.setStrokeStyle('#eeeeee');\n  ctx.setLineWidth(2);\n  ctx.moveTo(100, 430);\n  ctx.lineTo(500, 430);\n  ctx.stroke();\n  \n  // 绘制商铺类型\n  ctx.setFillStyle('#666666');\n  ctx.setFontSize(24);\n  ctx.setTextAlign('left');\n  ctx.fillText('商铺类型: ' + posterData.type, 80, 480);\n  \n  // 绘制面积\n  ctx.fillText('面积: ' + posterData.area, 80, 520);\n  \n  // 绘制位置\n  ctx.fillText('位置: ' + posterData.address, 80, 560);\n  \n  // A wrap text function\n  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {\n    if (text.length === 0) return;\n    \n    const words = text.split('');\n    let line = '';\n    let testLine = '';\n    let lineCount = 0;\n    \n    for (let n = 0; n < words.length; n++) {\n      testLine += words[n];\n      const metrics = ctx.measureText(testLine);\n      const testWidth = metrics.width;\n      \n      if (testWidth > maxWidth && n > 0) {\n        ctx.fillText(line, x, y + (lineCount * lineHeight));\n        line = words[n];\n        testLine = words[n];\n        lineCount++;\n        \n        if (lineCount >= 2) {\n          line += '...';\n          ctx.fillText(line, x, y + (lineCount * lineHeight));\n          break;\n        }\n      } else {\n        line = testLine;\n      }\n    }\n    \n    if (lineCount < 2) {\n      ctx.fillText(line, x, y + (lineCount * lineHeight));\n    }\n  };\n  \n  // 绘制描述\n  ctx.setFillStyle('#666666');\n  ctx.fillText('描述:', 80, 600);\n  wrapText(ctx, posterData.description, 80, 630, 440, 35);\n  \n  // 绘制电话\n  if (posterData.phone) {\n    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);\n  }\n  \n  // 绘制小程序码\n  ctx.drawImage(posterData.qrcode, 230, 700, 140, 140);\n  \n  // 绘制小程序码提示文字\n  ctx.setFillStyle('#999999');\n  ctx.setFontSize(20);\n  ctx.setTextAlign('center');\n  ctx.fillText('长按识别小程序码查看详情', 300, 870);\n  \n  // 执行绘制\n  ctx.draw(false, () => {\n    setTimeout(() => {\n      uni.canvasToTempFilePath({\n        canvasId: 'posterCanvas',\n        success: (res) => {\n          posterImagePath.value = res.tempFilePath;\n          showPosterFlag.value = true;\n          uni.hideLoading();\n          \n          uni.showModal({\n            title: '提示',\n            content: '海报已生成，是否保存到相册？',\n            confirmText: '保存',\n            success: (res) => {\n              if (res.confirm) {\n                saveImageToAlbum();\n              }\n            }\n          });\n        },\n        fail: (err) => {\n          console.error('生成海报失败:', err);\n          uni.hideLoading();\n          uni.showToast({\n            title: '生成海报失败',\n            icon: 'none'\n          });\n        }\n      });\n    }, 500);\n  });\n};\n\n// 保存图片到相册\nconst saveImageToAlbum = () => {\n  if (!posterImagePath.value) {\n    uni.showToast({\n      title: '海报未生成',\n      icon: 'none'\n    });\n    return;\n      }\n  \n  uni.saveImageToPhotosAlbum({\n    filePath: posterImagePath.value,\n    success: () => {\n      uni.showToast({\n        title: '保存成功',\n        icon: 'success'\n      });\n    },\n    fail: () => {\n        uni.showToast({\n          title: '保存失败',\n          icon: 'none'\n        });\n      }\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  uni.getSystemInfo({\n    success: (res) => {\n      statusBarHeight.value = res.statusBarHeight;\n    }\n  });\n  \n  // 获取页面参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.$page?.options || {};\n  \n  // 加载详情数据\n  if (options.id) {\n    loadTransferDetail(options.id);\n  } else {\n    // 如果没有ID参数，加载默认数据\n    loadTransferDetail('transfer12345');\n  }\n  \n  // 加载相关店铺数据\n  loadRelatedShops();\n});\n\n// 分享\nconst onShareAppMessage = () => {\n  return {\n    title: transferData.value.title,\n    path: `/pages/publish/business-transfer-detail?id=${transferData.value.id}`\n  };\n};\n\n// 海报相关数据\nconst posterImagePath = ref('');\nconst showPosterFlag = ref(false);\n\n// 举报相关\nconst showReportOptions = () => {\n  uni.showActionSheet({\n    itemList: ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'],\n    success: (res) => {\n      const reportReasonIndex = res.tapIndex;\n      const reasons = ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'];\n      showReportInputDialog(reasons[reportReasonIndex]);\n    }\n  });\n};\n\nconst showReportInputDialog = (reason) => {\n  // 检查是否登录\n  const hasLogin = uni.getStorageSync('token') || false;\n  if (!hasLogin) {\n    uni.showModal({\n      title: '提示',\n      content: '请先登录后再进行举报',\n      confirmText: '去登录',\n      success: (res) => {\n        if (res.confirm) {\n          uni.navigateTo({\n            url: '/pages/login/login'\n          });\n        }\n      }\n    });\n    return;\n  }\n  \n  uni.showModal({\n    title: '举报内容',\n    content: `您选择的举报原因是: ${reason}，请确认是否提交举报？`,\n    confirmText: '确认举报',\n    success: (res) => {\n      if (res.confirm) {\n        submitReport(reason, '');\n      }\n    }\n  });\n};\n\nconst submitReport = (reason, content) => {\n  uni.showLoading({\n    title: '提交中...'\n  });\n  \n  // 这里模拟提交举报信息到服务器\n  setTimeout(() => {\n    uni.hideLoading();\n    \n    uni.showToast({\n      title: '举报成功',\n      icon: 'success'\n    });\n    \n    // 实际开发中，这里应该调用API提交举报信息\n    // const reportData = {\n    //   type: 'businessTransfer',\n    //   id: businessData.value.id,\n    //   reason: reason,\n    //   content: content\n    // };\n    // submitReportAPI(reportData).then(() => {\n    //   uni.showToast({\n    //     title: '举报成功',\n    //     icon: 'success'\n    //   });\n    // });\n  }, 1500);\n};\n</script>\n\n<style scoped>\n.detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 110rpx;\n  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  width: 60rpx;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: transparent;\n  border-radius: 50%;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 16px;\n  font-weight: 500;\n  color: #FFFFFF; /* 修改为白色字体 */\n}\n\n.navbar-right {\n  width: 50px;\n  text-align: right;\n}\n\n.back-icon {\n  width: 36rpx;\n  height: 36rpx;\n  display: block;\n}\n\n.detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n/* 店铺转让页面的特殊样式 */\n.business-transfer-container {\n  /* 可以添加特定于店铺转让页面的样式 */\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: fixed;\n  width: 2rpx;\n  height: 2rpx;\n  opacity: 0;\n  top: -999rpx;\n  left: -999rpx;\n  z-index: -1;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  border: none;\n}\n\n.hidden-share-btn::after {\n  display: none;\n}\n\n/* 内容卡片通用样式 */\n.content-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\n}\n\n/* 标题区样式优化 */\n.title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 16rpx;\n}\n\n.main-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  line-height: 1.4;\n  margin-bottom: 12rpx;\n}\n\n.price-text {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff4d4f;\n  margin-bottom: 12rpx;\n}\n\n/* 元数据样式 */\n.meta-info {\n  margin-bottom: 24rpx;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.tag-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: 20rpx;\n  margin-bottom: 8rpx;\n}\n\n.info-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.08);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 12rpx;\n  margin-bottom: 8rpx;\n}\n\n.publish-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 轮播图优化 */\n.detail-swiper {\n  height: 420rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  margin-bottom: 24rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.swiper-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 基本信息卡片内部布局 */\n.basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  padding: 16rpx 0;\n  margin-top: 20rpx;\n}\n\n.info-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 详情信息列表 */\n.detail-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.detail-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.detail-item:last-child {\n  border-bottom: none;\n}\n\n.detail-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #888;\n}\n\n.detail-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.5;\n}\n\n/* 区块标题优化 */\n.section-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 20rpx;\n  position: relative;\n  padding-left: 16rpx;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 6rpx;\n  width: 6rpx;\n  height: 28rpx;\n  background-color: #1890ff;\n  border-radius: 3rpx;\n}\n\n/* 描述内容样式 */\n.description-content {\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  line-height: 1.6;\n}\n\n.description-text {\n  font-size: 28rpx;\n  color: #555;\n  line-height: 1.6;\n}\n\n/* 联系人信息样式 */\n.contact-content {\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.contact-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.contact-label {\n  width: 120rpx;\n  font-size: 28rpx;\n  color: #888;\n}\n\n.contact-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.contact-phone {\n  color: #1890ff;\n}\n\n.contact-tips {\n  display: flex;\n  align-items: center;\n  margin-top: 16rpx;\n}\n\n.tips-icon {\n  font-size: 24rpx;\n  color: #ff9800;\n  margin-right: 8rpx;\n}\n\n.tips-text {\n  font-size: 24rpx;\n  color: #ff9800;\n}\n\n/* 发布者信息 */\n.publisher-header {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.avatar-container {\n  width: 88rpx;\n  height: 88rpx;\n  border-radius: 44rpx;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n}\n\n.publisher-info {\n  flex: 1;\n}\n\n.publisher-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.publisher-meta {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.meta-text {\n  font-size: 24rpx;\n  color: #888;\n  margin-right: 16rpx;\n  display: flex;\n  align-items: center;\n}\n\n/* 底部工具栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  padding: 10rpx 20rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1rpx solid #f0f0f0;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 100;\n  height: 100rpx;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 6rpx 0;\n}\n\n.toolbar-icon {\n  width: 44rpx;\n  height: 44rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 22rpx;\n  color: #666;\n}\n\n.share-button {\n  background: transparent;\n  border: none;\n  margin: 0;\n  padding: 0;\n  line-height: normal;\n  border-radius: 0;\n  flex: 1;\n}\n\n.share-button::after {\n  display: none;\n}\n\n.call-button {\n  flex: 3;\n  background: linear-gradient(135deg, #0052CC, #0066FF);\n  height: 80rpx;\n  margin: 0 0 0 10rpx;\n  border-radius: 40rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.call-text {\n  color: #fff;\n  font-size: 30rpx;\n  font-weight: bold;\n  line-height: 1.2;\n}\n\n.call-subtitle {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 20rpx;\n  line-height: 1.2;\n}\n\n/* 悬浮海报按钮 */\n.float-poster-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 200rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: rgba(240, 240, 240, 0.9);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);\n  z-index: 90;\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  border: 1rpx solid rgba(230, 230, 230, 0.6);\n  transition: all 0.2s ease;\n}\n\n.float-poster-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);\n}\n\n.poster-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 4rpx;\n}\n\n.poster-text {\n  font-size: 20rpx;\n  color: #444;\n  line-height: 1;\n}\n\n/* 举报按钮样式 */\n/* 悬浮举报按钮已删除\n.report-btn {\n  position: fixed;\n  right: 30rpx;\n  top: 120rpx;\n  z-index: 90;\n  background-color: rgba(240, 240, 240, 0.9);\n  border-radius: 30rpx;\n  padding: 10rpx 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.report-text {\n  font-size: 24rpx;\n  color: #666;\n}\n*/\n\n/* 相似转让 */\n.similar-transfers {\n  margin-bottom: 20rpx;\n}\n.similar-list {\n  display: flex;\n  flex-direction: column;\n}\n.similar-item {\n  display: flex;\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n}\n.similar-image {\n  width: 160rpx;\n  height: 120rpx;\n  border-radius: 8rpx;\n  margin-right: 20rpx;\n}\n.similar-info {\n  flex: 1;\n}\n.similar-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 1;\n  -webkit-box-orient: vertical;\n}\n.similar-price {\n  font-size: 28rpx;\n  color: #ff4d4f;\n  margin-bottom: 8rpx;\n}\n.similar-meta {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 相关店铺推荐 */\n.related-shops-card {\n  margin-top: 20rpx;\n  padding: 20rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n}\n\n.related-shops-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.related-shops-list {\n  display: flex;\n  flex-direction: column;\n  margin-bottom: 20rpx;\n}\n\n.related-shop-item {\n  display: flex;\n  padding: 20rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.shop-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.shop-item-left {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.shop-item-middle {\n  flex: 1;\n}\n\n.shop-item-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.shop-item-area {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.shop-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.shop-item-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.08);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 12rpx;\n  margin-bottom: 8rpx;\n}\n\n.shop-item-tag-more {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.shop-item-right {\n  width: 120rpx;\n  text-align: right;\n}\n\n.shop-item-price {\n  font-size: 28rpx;\n  color: #ff4d4f;\n}\n\n.empty-related-shops {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 40rpx;\n}\n\n.empty-image {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 20rpx;\n}\n\n.empty-text {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.view-more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 10rpx 20rpx;\n  background-color: #fff;\n  border-radius: 12rpx;\n  margin-top: 20rpx;\n}\n\n.view-more-text {\n  font-size: 24rpx;\n  color: #1890ff;\n  margin-right: 8rpx;\n}\n\n.view-more-icon {\n  font-size: 24rpx;\n  color: #1890ff;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/business-transfer-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "mockApi", "ctx", "res", "onMounted"], "mappings": ";;;;;;;AA6OA,MAAA,aAAA,MAAA;;;;AAIA,UAAA,aAAA,CAAA,cAAA;AACA,YAAA,OAAA,IAAA,KAAA,SAAA;AACA,aAAA,GAAA,KAAA,aAAA,CAAA,IAAA,KAAA,SAAA;AAAA,IACA;AAGA,UAAA,kBAAAA,cAAAA,IAAA,EAAA;AAGA,UAAA,cAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA,CAAA;AAGA,UAAA,qBAAA,CAAA,OAAA;AACAC,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAGAC,eAAA,QAAA,QAAA,0BAAA,EAAA,EAAA,KAAA,UAAA;AACA,qBAAA,QAAA;AACAD,sBAAA,MAAA,YAAA;AAAA,MACA,CAAA,EAAA,MAAA,SAAA;AACAA,sBAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,qDAAA,WAAA,GAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,mBAAA,MAAA;AAEAC,eAAAA,QAAA,QAAA,kBAAA,KAAA,UAAA;AACA,qBAAA,QAAA;AAAA,MACA,CAAA,EAAA,MAAA,SAAA;AACAD,sBAAA,MAAA,MAAA,SAAA,qDAAA,aAAA,GAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,uBAAA,CAAA,OAAA;;AAEA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,YAAA,iBAAA,UAAA,mBAAA,YAAA,CAAA;AAEA,UAAA,OAAA,QAAA,IAAA;AACA;AAAA,MACA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,kCAAA,EAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,yBAAA,CAAA,MAAA;;AACA,UAAA;AAAA,UAAA;AAEA,YAAA,qBAAA,kBAAA,MAAA,SAAA,mBAAA,OAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,kEAAA,mBAAA,MAAA,CAAA,aAAA,mBAAA,gBAAA,CAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACA,kBAAA,QAAA,CAAA,YAAA;AACA,UAAA,YAAA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,YAAA,MAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,aAAA,aAAA,MAAA,QAAA;AAAA,QACA,MAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,WAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,SAAA,MAAA;AACAA,oBAAA,MAAA,aAAA;AAAA,IACA;AAGA,UAAA,WAAA,MAAA;AACA,UAAA,CAAA,aAAA,MAAA,aAAA,CAAA,aAAA,MAAA,UAAA,IAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,8BAAA,aAAA,MAAA,UAAA,EAAA,aAAA,aAAA,MAAA,UAAA,IAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,qBAAA,MAAA;AACAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAGA,YAAA,aAAA;AAAA,QACA,OAAA,aAAA,MAAA;AAAA,QACA,OAAA,aAAA,MAAA;AAAA,QACA,MAAA,aAAA,MAAA;AAAA,QACA,MAAA,aAAA,MAAA;AAAA,QACA,SAAA,aAAA,MAAA;AAAA,QACA,OAAA,aAAA,MAAA,QAAA;AAAA,QACA,aAAA,aAAA,MAAA,cAAA,aAAA,MAAA,YAAA,UAAA,GAAA,EAAA,IAAA,QAAA;AAAA,QACA,QAAA;AAAA,QACA,MAAA;AAAA,QACA,SAAA,aAAA,MAAA,OAAA,CAAA,KAAA;AAAA,MACA;AAgBA,YAAA,MAAAA,cAAAA,MAAA,oBAAA,cAAA;AAGA,UAAA,KAAA;AACA,UAAA,UAAA,WAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAEA,UAAA,aAAA,qBAAA;AACA,UAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AACA,UAAA,QAAA;AAGA,UAAA,KAAA;AACA,UAAA,aAAA,SAAA;AACA,UAAA,SAAA,IAAA,KAAA,KAAA,GAAA;AACA,UAAA,QAAA;AAGA,UAAA,KAAA;AACA,UAAA,UAAA;AACA,UAAA,IAAA,KAAA,KAAA,IAAA,GAAA,IAAA,KAAA,EAAA;AACA,UAAA,aAAA,SAAA;AACA,UAAA,KAAA;AAEA,UAAA,KAAA;AACA,UAAA,UAAA,WAAA,MAAA,KAAA,KAAA,KAAA,GAAA;AACA,UAAA,QAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,aAAA,QAAA;AACA,UAAA,SAAA,WAAA,OAAA,KAAA,GAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,SAAA,WAAA,OAAA,KAAA,GAAA;AAGA,UAAA,UAAA;AACA,UAAA,eAAA,SAAA;AACA,UAAA,aAAA,CAAA;AACA,UAAA,OAAA,KAAA,GAAA;AACA,UAAA,OAAA,KAAA,GAAA;AACA,UAAA,OAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,aAAA,MAAA;AACA,UAAA,SAAA,WAAA,WAAA,MAAA,IAAA,GAAA;AAGA,UAAA,SAAA,SAAA,WAAA,MAAA,IAAA,GAAA;AAGA,UAAA,SAAA,SAAA,WAAA,SAAA,IAAA,GAAA;AAGA,YAAA,WAAA,CAAAE,MAAA,MAAA,GAAA,GAAA,UAAA,eAAA;AACA,YAAA,KAAA,WAAA;AAAA;AAEA,cAAA,QAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA;AACA,YAAA,WAAA;AACA,YAAA,YAAA;AAEA,iBAAA,IAAA,GAAA,IAAA,MAAA,QAAA,KAAA;AACA,sBAAA,MAAA,CAAA;AACA,gBAAA,UAAAA,KAAA,YAAA,QAAA;AACA,gBAAA,YAAA,QAAA;AAEA,cAAA,YAAA,YAAA,IAAA,GAAA;AACA,YAAAA,KAAA,SAAA,MAAA,GAAA,IAAA,YAAA,UAAA;AACA,mBAAA,MAAA,CAAA;AACA,uBAAA,MAAA,CAAA;AACA;AAEA,gBAAA,aAAA,GAAA;AACA,sBAAA;AACA,cAAAA,KAAA,SAAA,MAAA,GAAA,IAAA,YAAA,UAAA;AACA;AAAA,YACA;AAAA,UACA,OAAA;AACA,mBAAA;AAAA,UACA;AAAA,QACA;AAEA,YAAA,YAAA,GAAA;AACA,UAAAA,KAAA,SAAA,MAAA,GAAA,IAAA,YAAA,UAAA;AAAA,QACA;AAAA,MACA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,SAAA,OAAA,IAAA,GAAA;AACA,eAAA,KAAA,WAAA,aAAA,IAAA,KAAA,KAAA,EAAA;AAGA,UAAA,WAAA,OAAA;AACA,YAAA,SAAA,WAAA,WAAA,OAAA,IAAA,GAAA;AAAA,MACA;AAGA,UAAA,UAAA,WAAA,QAAA,KAAA,KAAA,KAAA,GAAA;AAGA,UAAA,aAAA,SAAA;AACA,UAAA,YAAA,EAAA;AACA,UAAA,aAAA,QAAA;AACA,UAAA,SAAA,gBAAA,KAAA,GAAA;AAGA,UAAA,KAAA,OAAA,MAAA;AACA,mBAAA,MAAA;AACAF,wBAAAA,MAAA,qBAAA;AAAA,YACA,UAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACA,8BAAA,QAAA,IAAA;AACA,6BAAA,QAAA;AACAA,4BAAA,MAAA,YAAA;AAEAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,SAAA;AAAA,gBACA,aAAA;AAAA,gBACA,SAAA,CAAAG,SAAA;AACA,sBAAAA,KAAA,SAAA;AACA;kBACA;AAAA,gBACA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAH,4BAAA,MAAA,MAAA,SAAA,qDAAA,WAAA,GAAA;AACAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA,GAAA,GAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,mBAAA,MAAA;AACA,UAAA,CAAA,gBAAA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAEAA,oBAAAA,MAAA,uBAAA;AAAA,QACA,UAAA,gBAAA;AAAA,QACA,SAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,MAAA,MAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGAI,kBAAAA,UAAA,MAAA;;AAEAJ,oBAAAA,MAAA,cAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,0BAAA,QAAA,IAAA;AAAA,QACA;AAAA,MACA,CAAA;AAGA,YAAA,QAAA;AACA,YAAA,cAAA,MAAA,MAAA,SAAA,CAAA;AACA,YAAA,YAAA,iBAAA,UAAA,mBAAA,YAAA,CAAA;AAGA,UAAA,QAAA,IAAA;AACA,2BAAA,QAAA,EAAA;AAAA,MACA,OAAA;AAEA,2BAAA,eAAA;AAAA,MACA;AAGA;IACA,CAAA;AAWA,UAAA,kBAAAD,cAAAA,IAAA,EAAA;AACA,UAAA,iBAAAA,cAAAA,IAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3lBA,GAAG,WAAW,eAAe;"}