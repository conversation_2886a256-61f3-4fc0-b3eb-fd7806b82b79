<view class="service-filter-container premium-style data-v-bd48d0a8"><view class="custom-navbar data-v-bd48d0a8" style="{{'padding-top:' + c}}"><view class="back-btn data-v-bd48d0a8" bindtap="{{a}}"><view class="back-icon data-v-bd48d0a8"></view></view><view class="navbar-title data-v-bd48d0a8">{{b}}</view><view class="navbar-right data-v-bd48d0a8"></view></view><view wx:if="{{d}}" class="search-container data-v-bd48d0a8"><view class="search-box data-v-bd48d0a8"><image class="search-icon data-v-bd48d0a8" src="{{e}}"></image><input class="search-input data-v-bd48d0a8" type="text" placeholder="搜索职位、公司或关键词" confirm-type="search" bindconfirm="{{f}}" value="{{g}}" bindinput="{{h}}"/><view class="search-btn data-v-bd48d0a8" bindtap="{{i}}">搜索</view></view></view><scroll-view class="top-category-tabs data-v-bd48d0a8" scroll-x show-scrollbar="{{false}}" enhanced bounces="{{true}}"><view wx:for="{{j}}" wx:for-item="item" wx:key="b" class="{{['top-category-item', 'data-v-bd48d0a8', item.c && 'active-top-category']}}" bindtap="{{item.d}}">{{item.a}}</view></scroll-view><scroll-view wx:if="{{k}}" class="subcategory-tabs data-v-bd48d0a8" scroll-x show-scrollbar="{{false}}" enhanced bounces="{{true}}"><view wx:for="{{l}}" wx:for-item="category" wx:key="b" class="{{['subcategory-item', 'data-v-bd48d0a8', category.c && 'active-subcategory']}}" bindtap="{{category.d}}">{{category.a}}</view></scroll-view><view class="filter-section data-v-bd48d0a8" ref="filterSection"><view class="filter-item data-v-bd48d0a8" bindtap="{{p}}" ref="areaFilterItem"><text class="{{['filter-text', 'data-v-bd48d0a8', n && 'active-filter']}}">{{m}}</text><view class="{{['filter-arrow', 'data-v-bd48d0a8', o && 'arrow-up']}}"></view></view><view class="filter-item data-v-bd48d0a8" bindtap="{{t}}" ref="sortFilterItem"><text class="{{['filter-text', 'data-v-bd48d0a8', r && 'active-filter']}}">{{q}}</text><view class="{{['filter-arrow', 'data-v-bd48d0a8', s && 'arrow-up']}}"></view></view></view><view wx:if="{{v}}" class="selected-filters data-v-bd48d0a8"><scroll-view scroll-x class="filter-tags-scroll data-v-bd48d0a8" show-scrollbar="false" enhanced bounces="{{true}}"><view class="filter-tags data-v-bd48d0a8"><view wx:if="{{w}}" class="filter-tag data-v-bd48d0a8">{{x}} <text class="tag-close data-v-bd48d0a8" bindtap="{{y}}">×</text></view><view wx:if="{{z}}" class="filter-tag data-v-bd48d0a8">{{A}} <text class="tag-close data-v-bd48d0a8" bindtap="{{B}}">×</text></view><view wx:if="{{C}}" class="filter-tag data-v-bd48d0a8">{{D}} <text class="tag-close data-v-bd48d0a8" bindtap="{{E}}">×</text></view><view wx:if="{{F}}" class="filter-tag data-v-bd48d0a8">{{G}} <text class="tag-close data-v-bd48d0a8" bindtap="{{H}}">×</text></view><view wx:if="{{I}}" class="filter-tag data-v-bd48d0a8">{{J}} <text class="tag-close data-v-bd48d0a8" bindtap="{{K}}">×</text></view><view class="reset-all data-v-bd48d0a8" bindtap="{{L}}">清除全部</view></view></scroll-view></view><view wx:if="{{M}}" class="filter-dropdown area-dropdown data-v-bd48d0a8" style="{{'top:' + O + ';' + ('left:' + P) + ';' + ('width:' + Q)}}"><view class="dropdown-header data-v-bd48d0a8"><text class="dropdown-title data-v-bd48d0a8">选择区域</text></view><scroll-view scroll-y class="dropdown-scroll data-v-bd48d0a8"><view wx:for="{{N}}" wx:for-item="area" wx:key="c" class="{{['dropdown-item', 'data-v-bd48d0a8', area.d && 'active-item']}}" bindtap="{{area.e}}"><text class="dropdown-item-text data-v-bd48d0a8">{{area.a}}</text><text wx:if="{{area.b}}" class="dropdown-item-check data-v-bd48d0a8">✓</text></view></scroll-view></view><view wx:if="{{R}}" class="filter-dropdown subcategory-dropdown data-v-bd48d0a8" style="{{'top:' + U}}"><view class="dropdown-header data-v-bd48d0a8"><text class="dropdown-title data-v-bd48d0a8">{{S}}细分类别</text></view><scroll-view scroll-y class="dropdown-scroll data-v-bd48d0a8"><view wx:for="{{T}}" wx:for-item="subcat" wx:key="c" class="{{['dropdown-item', 'data-v-bd48d0a8', subcat.d && 'active-item']}}" bindtap="{{subcat.e}}"><text class="dropdown-item-text data-v-bd48d0a8">{{subcat.a}}</text><text wx:if="{{subcat.b}}" class="dropdown-item-check data-v-bd48d0a8">✓</text></view></scroll-view></view><view wx:if="{{V}}" class="filter-dropdown sort-dropdown data-v-bd48d0a8" style="{{'top:' + X + ';' + ('left:' + Y) + ';' + ('width:' + Z)}}"><view class="dropdown-header data-v-bd48d0a8"><text class="dropdown-title data-v-bd48d0a8">排序方式</text></view><view wx:for="{{W}}" wx:for-item="sort" wx:key="c" class="{{['dropdown-item', 'data-v-bd48d0a8', sort.d && 'active-item']}}" bindtap="{{sort.e}}"><text class="dropdown-item-text data-v-bd48d0a8">{{sort.a}}</text><text wx:if="{{sort.b}}" class="dropdown-item-check data-v-bd48d0a8">✓</text></view></view><view wx:if="{{aa}}" class="filter-mask data-v-bd48d0a8" bindtap="{{ab}}"></view><scroll-view scroll-y class="service-list data-v-bd48d0a8" bindscrolltolower="{{aj}}" refresher-enabled refresher-triggered="{{ak}}" bindrefresherrefresh="{{al}}" enhanced bounces="{{true}}" show-scrollbar="{{false}}"><view wx:if="{{ac}}" class="result-stats data-v-bd48d0a8"> 共找到 <text class="stats-number data-v-bd48d0a8">{{ad}}</text> 条相关信息 </view><view wx:if="{{ae}}" class="service-items-wrapper data-v-bd48d0a8"><view wx:for="{{af}}" wx:for-item="item" wx:key="o" class="service-item data-v-bd48d0a8" hover-class="service-item-hover" bindtap="{{item.p}}"><view class="service-content data-v-bd48d0a8"><view class="service-header data-v-bd48d0a8"><view class="service-tags data-v-bd48d0a8"><text class="service-tag data-v-bd48d0a8">{{item.a}}</text><text wx:if="{{item.b}}" class="service-subcategory data-v-bd48d0a8">{{item.c}}</text></view><view class="service-meta data-v-bd48d0a8"><text class="service-area data-v-bd48d0a8">{{item.d}}</text><text class="service-time data-v-bd48d0a8">{{item.e}}</text></view></view><view class="service-body data-v-bd48d0a8"><text class="service-title data-v-bd48d0a8">{{item.f}}</text><view wx:if="{{item.g}}" class="service-images data-v-bd48d0a8"><image wx:for="{{item.h}}" wx:for-item="img" wx:key="a" src="{{img.b}}" mode="aspectFill" class="{{['service-image', 'data-v-bd48d0a8', item.i && 'single-image']}}"></image><view wx:if="{{item.j}}" class="image-count data-v-bd48d0a8">+{{item.k}}</view></view></view><view class="service-footer data-v-bd48d0a8"><view class="service-stats data-v-bd48d0a8"><text class="service-views data-v-bd48d0a8">浏览: {{item.l}}</text><text wx:if="{{item.m}}" class="service-price data-v-bd48d0a8">￥{{item.n}}</text></view><view class="service-actions data-v-bd48d0a8"><view class="action-btn contact-btn data-v-bd48d0a8"><text class="action-icon data-v-bd48d0a8"></text><text class="action-text data-v-bd48d0a8">联系</text></view></view></view></view></view><view wx:if="{{ag}}" class="loading-more data-v-bd48d0a8"><view class="loading-indicator data-v-bd48d0a8"></view><text class="loading-text data-v-bd48d0a8">加载中...</text></view><view wx:else class="loading-done data-v-bd48d0a8"><text class="loading-done-text data-v-bd48d0a8">— 已经到底啦 —</text></view></view><view wx:else class="empty-state data-v-bd48d0a8"><image src="{{ah}}" mode="aspectFit" class="empty-image data-v-bd48d0a8"></image><text class="empty-text data-v-bd48d0a8">暂无相关职位信息</text><view class="empty-tips data-v-bd48d0a8"> 换个筛选条件试试吧 </view><view class="empty-btn data-v-bd48d0a8" bindtap="{{ai}}">重置筛选</view></view></scroll-view><view class="publish-btn data-v-bd48d0a8" hover-class="publish-btn-hover" bindtap="{{am}}"><text class="publish-icon data-v-bd48d0a8">+</text><text class="publish-text data-v-bd48d0a8">发布</text></view></view>