<template>
  <view class="commission-rules-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">佣金规则设置</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 佣金模式设置 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">佣金模式</text>
      </view>
      
      <view class="mode-options">
        <view 
          class="mode-option" 
          :class="{ 'active': settings.commissionMode === 'percentage' }"
          @click="settings.commissionMode = 'percentage'"
        >
          <view class="option-icon percentage"></view>
          <view class="option-content">
            <text class="option-title">按比例计算</text>
            <text class="option-desc">按商品售价的百分比计算佣金</text>
          </view>
        </view>
        
        <view 
          class="mode-option" 
          :class="{ 'active': settings.commissionMode === 'fixed' }"
          @click="settings.commissionMode = 'fixed'"
        >
          <view class="option-icon fixed"></view>
          <view class="option-content">
            <text class="option-title">固定金额</text>
            <text class="option-desc">每件商品固定金额佣金</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 默认佣金设置 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">默认佣金设置</text>
        <text class="section-desc">设置全平台通用的默认佣金比例，可在商品详情单独设置</text>
      </view>
      
      <view class="commission-levels">
        <view class="level-item">
          <view class="level-header">
            <view class="level-icon level1"></view>
            <text class="level-name">一级分销</text>
          </view>
          <view class="level-input-wrap">
            <input 
              class="level-input" 
              type="digit" 
              v-model="settings.defaultCommission.level1" 
              :placeholder="settings.commissionMode === 'percentage' ? '佣金比例' : '固定金额'"
            />
            <text class="input-unit">{{settings.commissionMode === 'percentage' ? '%' : '元'}}</text>
          </view>
        </view>
        
        <view class="level-item">
          <view class="level-header">
            <view class="level-icon level2"></view>
            <text class="level-name">二级分销</text>
          </view>
          <view class="level-input-wrap">
            <input 
              class="level-input" 
              type="digit" 
              v-model="settings.defaultCommission.level2" 
              :placeholder="settings.commissionMode === 'percentage' ? '佣金比例' : '固定金额'"
            />
            <text class="input-unit">{{settings.commissionMode === 'percentage' ? '%' : '元'}}</text>
          </view>
        </view>
        
        <view class="level-item" v-if="settings.enableLevel3">
          <view class="level-header">
            <view class="level-icon level3"></view>
            <text class="level-name">三级分销</text>
          </view>
          <view class="level-input-wrap">
            <input 
              class="level-input" 
              type="digit" 
              v-model="settings.defaultCommission.level3" 
              :placeholder="settings.commissionMode === 'percentage' ? '佣金比例' : '固定金额'"
            />
            <text class="input-unit">{{settings.commissionMode === 'percentage' ? '%' : '元'}}</text>
          </view>
        </view>
      </view>
      
      <view class="switch-item">
        <view class="switch-label">
          <text class="label-text">启用三级分销</text>
        </view>
        <switch 
          :checked="settings.enableLevel3" 
          color="#6B0FBE" 
          @change="toggleLevel3"
        />
      </view>
    </view>
    
    <!-- 佣金计算规则 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">佣金计算规则</text>
      </view>
      
      <view class="rule-item">
        <view class="rule-label">
          <text class="label-text">计算基数</text>
        </view>
        <view class="rule-value">
          <picker 
            mode="selector" 
            :range="baseOptions" 
            range-key="name"
            :value="baseIndex"
            @change="onBaseChange"
          >
            <view class="picker-value">
              <text>{{baseOptions[baseIndex].name}}</text>
              <view class="arrow-icon"></view>
            </view>
          </picker>
        </view>
      </view>
      
      <view class="rule-item">
        <view class="rule-label">
          <text class="label-text">结算时机</text>
        </view>
        <view class="rule-value">
          <picker 
            mode="selector" 
            :range="settlementOptions" 
            range-key="name"
            :value="settlementIndex"
            @change="onSettlementChange"
          >
            <view class="picker-value">
              <text>{{settlementOptions[settlementIndex].name}}</text>
              <view class="arrow-icon"></view>
            </view>
          </picker>
        </view>
      </view>
      
      <view class="rule-item">
        <view class="rule-label">
          <text class="label-text">冻结天数</text>
          <text class="label-desc">订单完成后需等待的天数</text>
        </view>
        <view class="rule-value">
          <input 
            class="rule-input" 
            type="number" 
            v-model="settings.freezeDays" 
            placeholder="天数"
          />
        </view>
      </view>
    </view>
    
    <!-- 佣金限制 -->
    <view class="section-card">
      <view class="section-header">
        <text class="section-title">佣金限制</text>
      </view>
      
      <view class="switch-item">
        <view class="switch-label">
          <text class="label-text">设置佣金上限</text>
          <text class="label-desc">启用后，单笔订单佣金不超过设定值</text>
        </view>
        <switch 
          :checked="settings.enableMaxCommission" 
          color="#6B0FBE" 
          @change="toggleMaxCommission"
        />
      </view>
      
      <view class="limit-input" v-if="settings.enableMaxCommission">
        <text class="limit-label">最高佣金金额</text>
        <view class="input-wrap">
          <text class="currency">¥</text>
          <input 
            class="limit-value" 
            type="digit" 
            v-model="settings.maxCommission" 
            placeholder="请输入金额"
          />
        </view>
      </view>
      
      <view class="switch-item">
        <view class="switch-label">
          <text class="label-text">自购返佣</text>
          <text class="label-desc">分销员购买自己分销的商品是否获得佣金</text>
        </view>
        <switch 
          :checked="settings.selfPurchaseCommission" 
          color="#6B0FBE" 
          @change="toggleSelfPurchase"
        />
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="save-section">
      <button class="save-btn" @click="saveSettings">保存设置</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 佣金规则设置
const settings = reactive({
  commissionMode: 'percentage', // 佣金模式: percentage(按比例), fixed(固定金额)
  defaultCommission: {
    level1: 10, // 一级佣金比例
    level2: 5,  // 二级佣金比例
    level3: 3   // 三级佣金比例
  },
  enableLevel3: false, // 是否启用三级分销
  calculationBase: 'total', // 计算基数: total(订单总额), profit(利润)
  settlementTiming: 'completed', // 结算时机: paid(支付后), completed(完成后), confirmed(确认收货后)
  freezeDays: 7, // 冻结天数
  enableMaxCommission: false, // 是否启用最高佣金限制
  maxCommission: 100, // 最高佣金金额
  selfPurchaseCommission: true // 自购是否返佣
});

// 计算基数选项
const baseOptions = [
  { value: 'total', name: '订单总额' },
  { value: 'profit', name: '商品利润' }
];

// 结算时机选项
const settlementOptions = [
  { value: 'paid', name: '支付后' },
  { value: 'completed', name: '完成后' },
  { value: 'confirmed', name: '确认收货后' }
];

// 当前选中的计算基数索引
const baseIndex = computed(() => {
  return baseOptions.findIndex(option => option.value === settings.calculationBase);
});

// 当前选中的结算时机索引
const settlementIndex = computed(() => {
  return settlementOptions.findIndex(option => option.value === settings.settlementTiming);
});

// 页面加载
onMounted(async () => {
  // 获取佣金规则设置
  await getCommissionRules();
});

// 获取佣金规则设置
const getCommissionRules = async () => {
  try {
    const result = await distributionService.getCommissionRules();
    
    if (result) {
      Object.assign(settings, result);
    }
  } catch (error) {
    console.error('获取佣金规则设置失败', error);
    uni.showToast({
      title: '获取佣金规则设置失败',
      icon: 'none'
    });
  }
};

// 切换三级分销
const toggleLevel3 = (e) => {
  settings.enableLevel3 = e.detail.value;
};

// 切换最高佣金限制
const toggleMaxCommission = (e) => {
  settings.enableMaxCommission = e.detail.value;
};

// 切换自购返佣
const toggleSelfPurchase = (e) => {
  settings.selfPurchaseCommission = e.detail.value;
};

// 计算基数变化
const onBaseChange = (e) => {
  const index = e.detail.value;
  settings.calculationBase = baseOptions[index].value;
};

// 结算时机变化
const onSettlementChange = (e) => {
  const index = e.detail.value;
  settings.settlementTiming = settlementOptions[index].value;
};

// 保存设置
const saveSettings = async () => {
  try {
    uni.showLoading({
      title: '保存中...',
      mask: true
    });
    
    const result = await distributionService.saveCommissionRules(settings);
    
    uni.hideLoading();
    
    if (result.success) {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
    } else {
      uni.showModal({
        title: '保存失败',
        content: result.message || '请稍后再试',
        showCancel: false
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('保存佣金规则设置失败', error);
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '佣金规则帮助',
    content: '佣金规则设置用于配置分销佣金的计算方式、结算时机和限制条件。您可以设置按比例或固定金额计算佣金，并配置多级分销的佣金分配。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.commission-rules-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 卡片通用样式 */
.section-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

/* 佣金模式 */
.mode-options {
  display: flex;
  margin: 0 -10rpx;
}

.mode-option {
  flex: 1;
  margin: 0 10rpx;
  background: #F5F7FA;
  border-radius: 20rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  border: 2rpx solid transparent;
}

.mode-option.active {
  border-color: #6B0FBE;
  background: rgba(107, 15, 190, 0.05);
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.option-icon.percentage {
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
}

.option-icon.percentage::after {
  content: '%';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.option-icon.fixed {
  background-color: #FF9500;
  border-radius: 50%;
  position: relative;
}

.option-icon.fixed::after {
  content: '¥';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.option-desc {
  font-size: 24rpx;
  color: #999;
}

/* 佣金等级 */
.commission-levels {
  margin-bottom: 30rpx;
}

.level-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.level-header {
  display: flex;
  align-items: center;
}

.level-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.level-icon.level1 {
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
}

.level-icon.level1::after {
  content: '1';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.level-icon.level2 {
  background-color: #409EFF;
  border-radius: 50%;
  position: relative;
}

.level-icon.level2::after {
  content: '2';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.level-icon.level3 {
  background-color: #67C23A;
  border-radius: 50%;
  position: relative;
}

.level-icon.level3::after {
  content: '3';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.level-name {
  font-size: 28rpx;
  color: #333;
}

.level-input-wrap {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  height: 80rpx;
  width: 200rpx;
}

.level-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
  text-align: right;
}

.input-unit {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.switch-item:last-child {
  margin-bottom: 0;
}

.switch-label {
  flex: 1;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
  display: block;
}

.label-desc {
  font-size: 24rpx;
  color: #999;
}

/* 规则项 */
.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.rule-label {
  flex: 1;
}

.rule-value {
  width: 200rpx;
}

.picker-value {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 10rpx;
}

.rule-input {
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

/* 限制输入 */
.limit-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.limit-label {
  font-size: 28rpx;
  color: #333;
}

.input-wrap {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  height: 80rpx;
  width: 200rpx;
}

.currency {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.limit-value {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

/* 保存按钮 */
.save-section {
  margin: 30rpx;
}

.save-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  line-height: 1.5;
  width: 100%;
}
</style>