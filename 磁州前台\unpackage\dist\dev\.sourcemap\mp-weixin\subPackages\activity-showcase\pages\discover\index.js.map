{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/discover/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzY292ZXJcaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"local-mall-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"navbar-title\">磁州商城</view>\n        <view class=\"navbar-right\">\n          <view class=\"search-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/search/index')\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <path d=\"M11 17.25a6.25 6.25 0 110-12.5 6.25 6.25 0 010 12.5zm0 0L21 21\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n          <view class=\"notification-btn\" @click=\"showNotifications\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <path d=\"M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9M13.73 21a2 2 0 01-3.46 0\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n            <view class=\"badge\" v-if=\"unreadNotifications > 0\">{{ unreadNotifications > 99 ? '99+' : unreadNotifications }}</view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 内容区域 -->\n    <scroll-view \n      class=\"content-scroll\" \n      scroll-y \n      @scrolltolower=\"loadMore\"\n    >\n      <!-- 搜索框和分类导航 -->\n      <SearchAndCategory \n        :categories=\"categories\" \n        @search=\"navigateTo('/subPackages/activity-showcase/pages/search/index')\"\n        @scan=\"showScanCode\"\n        @categoryChange=\"onCategoryChange\"\n      />\n      \n      <!-- 限时特惠 -->\n      <view class=\"section-card\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">今日特惠</text>\n          <view class=\"section-more\" @click=\"navigateTo('/subPackages/activity-showcase/pages/flash-sale/index')\">\n            <text>查看更多</text>\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#FF3B69\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <scroll-view class=\"product-scroll\" scroll-x show-scrollbar=\"false\">\n          <view class=\"product-scroll-list\">\n            <view \n              class=\"product-scroll-item\" \n              v-for=\"(product, index) in flashSaleProducts\" \n              :key=\"index\"\n            >\n              <ProductCard \n                :product=\"product\"\n                :showShop=\"false\"\n                :cardStyle=\"{\n                  width: '280rpx',\n                  marginRight: index === flashSaleProducts.length - 1 ? '0' : '20rpx'\n                }\"\n                @click=\"viewProductDetail(product)\"\n                @addToCart=\"addToCart(product)\"\n              />\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n      \n      <!-- 附近好店 -->\n      <view class=\"section-card\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">磁州好店</text>\n          <view class=\"section-more\" @click=\"navigateTo('/subPackages/activity-showcase/pages/shops/index')\">\n            <text>查看更多</text>\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#FF3B69\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"shops-list\">\n          <ShopCard \n            v-for=\"(shop, index) in nearbyShops\" \n            :key=\"index\"\n            :shop=\"shop\"\n            @click=\"viewShopDetail(shop)\"\n            @enter=\"viewShopDetail(shop)\"\n          />\n        </view>\n      </view>\n      \n      <!-- 热门商品 -->\n      <view class=\"section-card\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">精选商品</text>\n          <view class=\"section-more\" @click=\"navigateTo('/subPackages/activity-showcase/pages/products/hot')\">\n            <text>查看更多</text>\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#FF3B69\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"products-grid\">\n          <view \n            class=\"product-grid-item\" \n            v-for=\"(product, index) in hotProducts\" \n            :key=\"index\"\n          >\n            <ProductCard \n              :product=\"product\"\n              @click=\"viewProductDetail(product)\"\n              @addToCart=\"addToCart(product)\"\n            />\n          </view>\n        </view>\n      </view>\n      \n      <!-- 底部安全区域 -->\n      <view class=\"safe-area-bottom\"></view>\n    </scroll-view>\n    \n    <!-- 底部导航栏 -->\n    <view class=\"tabbar\">\n      <view \n        class=\"tabbar-item\" \n        @click=\"switchTab('home')\"\n        data-tab=\"home\"\n      >\n        <view class=\"tab-icon home\"></view>\n        <text class=\"tabbar-text\">首页</text>\n      </view>\n      <view \n        class=\"tabbar-item active\" \n        data-tab=\"discover\"\n      >\n        <view class=\"tab-icon discover\"></view>\n        <text class=\"tabbar-text\">商城</text>\n      </view>\n      <view \n        class=\"tabbar-item\" \n        @click=\"switchTab('distribution')\"\n        data-tab=\"distribution\"\n      >\n        <view class=\"tab-icon distribution\"></view>\n        <text class=\"tabbar-text\">分销</text>\n      </view>\n      <view \n        class=\"tabbar-item\" \n        @click=\"switchTab('message')\"\n        data-tab=\"message\"\n      >\n        <view class=\"tab-icon message\">\n          <view class=\"badge\" v-if=\"unreadMessageCount > 0\">{{ unreadMessageCount > 99 ? '99+' : unreadMessageCount }}</view>\n        </view>\n        <text class=\"tabbar-text\">消息</text>\n      </view>\n      <view \n        class=\"tabbar-item\" \n        @click=\"switchTab('my')\"\n        data-tab=\"my\"\n      >\n        <view class=\"tab-icon user\"></view>\n        <text class=\"tabbar-text\">我的</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\nimport SearchAndCategory from '../../components/mall/SearchAndCategory.vue';\nimport ProductCard from '../../components/mall/ProductCard.vue';\nimport ShopCard from '../../components/mall/ShopCard.vue';\n\n// 页面状态\nconst unreadNotifications = ref(5);\nconst unreadMessageCount = ref(3);\n\n// 分类数据\nconst categories = ref([\n  { name: '全部', icon: '/static/images/category/all.png', bgColor: '#FFF0F5' },\n  { name: '美食', icon: '/static/images/category/food.png', bgColor: '#FFF4E8' },\n  { name: '服饰', icon: '/static/images/category/clothing.png', bgColor: '#E8F5FF' },\n  { name: '美妆', icon: '/static/images/category/beauty.png', bgColor: '#F0FFF0' },\n  { name: '电子', icon: '/static/images/category/electronics.png', bgColor: '#F5E8FF' },\n  { name: '家居', icon: '/static/images/category/home.png', bgColor: '#E8FFFF' },\n  { name: '母婴', icon: '/static/images/category/baby.png', bgColor: '#FFF0E8' },\n  { name: '更多', icon: '/static/images/category/more.png', bgColor: '#F2F2F7' },\n  { name: '生鲜', icon: '/static/images/category/fresh.png', bgColor: '#E8FFF0' },\n  { name: '书籍', icon: '/static/images/category/books.png', bgColor: '#FFE8F5' }\n]);\n\n// 限时特惠商品\nconst flashSaleProducts = ref([\n  {\n    id: 1,\n    title: 'iPhone 14 Pro 深空黑 256G',\n    coverImage: 'https://via.placeholder.com/300x300',\n    shopName: 'Apple授权专卖店',\n    shopLogo: 'https://via.placeholder.com/100',\n    price: 7999,\n    originalPrice: 8999,\n    soldCount: 235,\n    tag: '限时秒杀',\n    labels: [\n      { type: 'discount', text: '满3000减300' },\n      { type: 'new', text: '新品' }\n    ]\n  },\n  {\n    id: 2,\n    title: '华为Mate 50 Pro 曜金黑 512G',\n    coverImage: 'https://via.placeholder.com/300x300',\n    shopName: '华为官方旗舰店',\n    shopLogo: 'https://via.placeholder.com/100',\n    price: 6999,\n    originalPrice: 7999,\n    soldCount: 189,\n    tag: '限时特惠',\n    labels: [\n      { type: 'discount', text: '满5000减500' },\n      { type: 'hot', text: '热卖' }\n    ]\n  },\n  {\n    id: 3,\n    title: '小米12S Ultra 陶瓷白 256G',\n    coverImage: 'https://via.placeholder.com/300x300',\n    shopName: '小米官方旗舰店',\n    shopLogo: 'https://via.placeholder.com/100',\n    price: 5999,\n    originalPrice: 6999,\n    soldCount: 156,\n    tag: '限时秒杀',\n    labels: [\n      { type: 'coupon', text: '满3000减400' }\n    ]\n  },\n  {\n    id: 4,\n    title: 'OPPO Find X5 Pro 陶瓷白 256G',\n    coverImage: 'https://via.placeholder.com/300x300',\n    shopName: 'OPPO官方旗舰店',\n    shopLogo: 'https://via.placeholder.com/100',\n    price: 4999,\n    originalPrice: 5999,\n    soldCount: 132,\n    tag: '限时特惠',\n    labels: [\n      { type: 'new', text: '新品' }\n    ]\n  }\n]);\n\n// 附近好店\nconst nearbyShops = ref([\n  {\n    id: 1,\n    name: '星巴克咖啡(万达广场店)',\n    logo: 'https://via.placeholder.com/100',\n    coverImage: 'https://via.placeholder.com/750x300',\n    rating: 4.8,\n    distance: '500m',\n    orderCount: 5689,\n    tags: ['咖啡', '甜点', '下午茶'],\n    description: '提供优质咖啡和舒适环境的星巴克门店，欢迎品尝我们的季节限定饮品。'\n  },\n  {\n    id: 2,\n    name: '海底捞火锅(环球中心店)',\n    logo: 'https://via.placeholder.com/100',\n    coverImage: 'https://via.placeholder.com/750x300',\n    rating: 4.9,\n    distance: '1.2km',\n    orderCount: 8976,\n    tags: ['火锅', '川菜', '聚餐'],\n    description: '提供优质服务和美味火锅的海底捞门店，欢迎您的光临。'\n  }\n]);\n\n// 热门商品\nconst hotProducts = ref([\n  {\n    id: 5,\n    title: 'Apple AirPods Pro 2代',\n    coverImage: 'https://via.placeholder.com/300x300',\n    shopName: 'Apple授权专卖店',\n    shopLogo: 'https://via.placeholder.com/100',\n    price: 1999,\n    originalPrice: 2299,\n    soldCount: 456,\n    distance: '2.5km',\n    labels: [\n      { type: 'hot', text: '热卖' }\n    ]\n  },\n  {\n    id: 6,\n    title: '戴森吹风机 Supersonic HD08',\n    coverImage: 'https://via.placeholder.com/300x300',\n    shopName: '戴森官方旗舰店',\n    shopLogo: 'https://via.placeholder.com/100',\n    price: 3190,\n    originalPrice: 3690,\n    soldCount: 325,\n    distance: '3.1km',\n    labels: [\n      { type: 'discount', text: '满3000减300' }\n    ]\n  },\n  {\n    id: 7,\n    title: 'NIKE Air Jordan 1 高帮篮球鞋',\n    coverImage: 'https://via.placeholder.com/300x300',\n    shopName: 'NIKE官方旗舰店',\n    shopLogo: 'https://via.placeholder.com/100',\n    price: 1299,\n    originalPrice: 1499,\n    soldCount: 567,\n    distance: '4.2km',\n    labels: [\n      { type: 'new', text: '新品' }\n    ]\n  },\n  {\n    id: 8,\n    title: '三星Galaxy Watch 5 Pro',\n    coverImage: 'https://via.placeholder.com/300x300',\n    shopName: '三星电子旗舰店',\n    shopLogo: 'https://via.placeholder.com/100',\n    price: 2999,\n    originalPrice: 3299,\n    soldCount: 234,\n    distance: '1.8km',\n    labels: [\n      { type: 'coupon', text: '满2000减200' }\n    ]\n  }\n]);\n\n// 显示通知\nconst showNotifications = () => {\n  uni.showToast({\n    title: '通知功能开发中',\n    icon: 'none'\n  });\n};\n\n// 显示扫码\nconst showScanCode = () => {\n  uni.scanCode({\n    success: (res) => {\n      uni.showToast({\n        title: '扫码成功: ' + res.result,\n        icon: 'none'\n      });\n    },\n    fail: () => {\n      uni.showToast({\n        title: '扫码失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 分类切换\nconst onCategoryChange = (index) => {\n  uni.showToast({\n    title: `已切换到${categories.value[index].name}分类`,\n    icon: 'none'\n  });\n};\n\n// 查看商品详情\nconst viewProductDetail = (product) => {\n  navigateTo(`/subPackages/activity-showcase/pages/products/detail?id=${product.id}`);\n};\n\n// 查看店铺详情\nconst viewShopDetail = (shop) => {\n  navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.id}`);\n};\n\n// 添加到购物车\nconst addToCart = (product) => {\n  uni.showToast({\n    title: '已添加到购物车',\n    icon: 'success'\n  });\n};\n\n// 切换底部导航标签页\nconst switchTab = (tab) => {\n  switch(tab) {\n    case 'home':\n      uni.redirectTo({\n        url: '/subPackages/activity-showcase/pages/index/index'\n      });\n      break;\n    case 'distribution':\n      uni.redirectTo({\n        url: '/subPackages/activity-showcase/pages/distribution/index'\n      });\n      break;\n    case 'message':\n      uni.redirectTo({\n        url: '/subPackages/activity-showcase/pages/message/index'\n      });\n      break;\n    case 'my':\n      uni.redirectTo({\n        url: '/subPackages/activity-showcase/pages/my/index'\n      });\n      break;\n  }\n};\n\n// 页面导航\nconst navigateTo = (url) => {\n  uni.navigateTo({ url });\n};\n\n// 加载更多\nconst loadMore = () => {\n  // 加载更多数据...\n  uni.showToast({\n    title: '已加载全部数据',\n    icon: 'none'\n  });\n};\n\n// 页面加载\nonMounted(() => {\n  // 初始化数据\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.local-mall-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #F2F2F7;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: calc(var(--status-bar-height, 25px) + 62px);\n  width: 100%;\n  z-index: 100;\n  \n  .navbar-bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\n    backdrop-filter: blur(10px);\n    -webkit-backdrop-filter: blur(10px);\n    box-shadow: 0 4px 6px rgba(255,59,105,0.15);\n  }\n  \n  .navbar-content {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    padding: 0 30rpx;\n    padding-top: var(--status-bar-height, 25px);\n    box-sizing: border-box;\n  }\n  \n  .navbar-title {\n    font-size: 36rpx;\n    font-weight: 600;\n    color: #FFFFFF;\n    letter-spacing: 0.5px;\n    position: absolute;\n    left: 50%;\n    transform: translateX(-50%);\n    text-shadow: 0 1px 2px rgba(0,0,0,0.1);\n  }\n  \n  .navbar-right {\n    display: flex;\n    align-items: center;\n    position: absolute;\n    right: 30rpx;\n  }\n  \n  .search-btn, .notification-btn {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n    \n    .icon {\n      width: 48rpx;\n      height: 48rpx;\n    }\n    \n    .badge {\n      position: absolute;\n      top: 10rpx;\n      right: 10rpx;\n      min-width: 32rpx;\n      height: 32rpx;\n      border-radius: 16rpx;\n      background: #FF3B30;\n      color: #FFFFFF;\n      font-size: 20rpx;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      padding: 0 6rpx;\n      box-sizing: border-box;\n      font-weight: 600;\n      box-shadow: 0 2rpx 6rpx rgba(255,59,48,0.3);\n      border: 1rpx solid rgba(255,255,255,0.8);\n    }\n  }\n}\n\n/* 内容区域 */\n.content-scroll {\n  flex: 1;\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\n  margin-bottom: 100rpx; /* 底部导航栏高度 */\n  box-sizing: border-box;\n}\n\n/* 内容区域样式 */\n.content-scroll {\n  padding: 0;\n}\n\n/* 区块卡片 */\n.section-card {\n  background-color: #FFFFFF;\n  margin: 30rpx;\n  border-radius: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.05);\n  position: relative;\n  z-index: 1;\n}\n\n.section-card:first-of-type {\n  margin-top: 20rpx;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333333;\n  }\n  \n  .section-more {\n    display: flex;\n    align-items: center;\n    font-size: 24rpx;\n    color: #FF3B69;\n    \n    .icon {\n      margin-left: 4rpx;\n    }\n  }\n}\n\n/* 商品横向滚动 */\n.product-scroll {\n  white-space: nowrap;\n  \n  .product-scroll-list {\n    display: inline-flex;\n    padding: 10rpx 0;\n  }\n}\n\n/* 店铺列表 */\n.shops-list {\n  margin-top: 20rpx;\n}\n\n/* 商品网格 */\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n  margin-top: 20rpx;\n}\n\n/* 底部安全区域 */\n.safe-area-bottom {\n  height: 34px; /* iOS 安全区域高度 */\n}\n\n/* 底部导航栏 */\n.tabbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background-color: #FFFFFF;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  padding-bottom: env(safe-area-inset-bottom); /* 适配 iPhone X 以上的底部安全区域 */\n  z-index: 99;\n  border-top: 1rpx solid #EEEEEE;\n}\n  \n.tabbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 6px 0;\n  box-sizing: border-box;\n  position: relative;\n}\n\n.tabbar-item:active {\n  transform: scale(0.9);\n}\n\n.tabbar-item.active .tab-icon {\n  transform: translateY(-5rpx);\n}\n\n.tabbar-item.active .tabbar-text {\n  color: #FF3B69;\n  font-weight: 600;\n  transform: translateY(-2rpx);\n}\n\n.tab-icon {\n  width: 24px;\n  height: 24px;\n  margin-bottom: 4px;\n  color: #999999;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-size: contain;\n  background-position: center;\n  background-repeat: no-repeat;\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n}\n\n/* 首页图标 */\n.tab-icon.home {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"home\"] .tab-icon.home {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E\");\n}\n\n/* 发现图标（本地商城） */\n.tab-icon.discover {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 3c0 .55.45 1 1 1h1l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h11c.55 0 1-.45 1-1s-.45-1-1-1H7l1.1-2h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.67-1.43c-.16-.35-.52-.57-.9-.57H2c-.55 0-1 .45-1 1zm16 15c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"discover\"] .tab-icon.discover {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 3c0 .55.45 1 1 1h1l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h11c.55 0 1-.45 1-1s-.45-1-1-1H7l1.1-2h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.67-1.43c-.16-.35-.52-.57-.9-.57H2c-.55 0-1 .45-1 1zm16 15c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 分销图标 */\n.tab-icon.distribution {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"distribution\"] .tab-icon.distribution {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 消息图标 */\n.tab-icon.message {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"message\"] .tab-icon.message {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E\");\n}\n\n/* 我的图标 */\n.tab-icon.user {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active[data-tab=\"my\"] .tab-icon.user {\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E\");\n}\n\n.tabbar-item.active .tab-icon {\n  filter: drop-shadow(0 1px 2px rgba(255,59,105,0.3));\n}\n\n.badge {\n  position: absolute;\n  top: -8rpx;\n  right: -12rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  border-radius: 16rpx;\n  background: linear-gradient(135deg, #FF453A, #FF2D55);\n  color: #FFFFFF;\n  font-size: 18rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0 6rpx;\n  box-sizing: border-box;\n  font-weight: 600;\n  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);\n  border: 1rpx solid rgba(255, 255, 255, 0.8);\n  transform: scale(0.9);\n}\n\n.tabbar-text {\n  font-size: 22rpx;\n  color: #8E8E93;\n  margin-top: 2rpx;\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n}\n\n.tabbar-item::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%) scaleX(0);\n  width: 30rpx;\n  height: 4rpx;\n  background: #FF3B69;\n  border-radius: 2rpx;\n  transition: transform 0.3s ease;\n}\n\n.tabbar-item.active::after {\n  transform: translateX(-50%) scaleX(1);\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/discover/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted"], "mappings": ";;;;;;;;;;AA8KA,MAAM,oBAAoB,MAAW;AACrC,MAAM,cAAc,MAAW;AAC/B,MAAM,WAAW,MAAW;;;;AAG5B,UAAM,sBAAsBA,cAAAA,IAAI,CAAC;AACjC,UAAM,qBAAqBA,cAAAA,IAAI,CAAC;AAGhC,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,EAAE,MAAM,MAAM,MAAM,mCAAmC,SAAS,UAAW;AAAA,MAC3E,EAAE,MAAM,MAAM,MAAM,oCAAoC,SAAS,UAAW;AAAA,MAC5E,EAAE,MAAM,MAAM,MAAM,wCAAwC,SAAS,UAAW;AAAA,MAChF,EAAE,MAAM,MAAM,MAAM,sCAAsC,SAAS,UAAW;AAAA,MAC9E,EAAE,MAAM,MAAM,MAAM,2CAA2C,SAAS,UAAW;AAAA,MACnF,EAAE,MAAM,MAAM,MAAM,oCAAoC,SAAS,UAAW;AAAA,MAC5E,EAAE,MAAM,MAAM,MAAM,oCAAoC,SAAS,UAAW;AAAA,MAC5E,EAAE,MAAM,MAAM,MAAM,oCAAoC,SAAS,UAAW;AAAA,MAC5E,EAAE,MAAM,MAAM,MAAM,qCAAqC,SAAS,UAAW;AAAA,MAC7E,EAAE,MAAM,MAAM,MAAM,qCAAqC,SAAS,UAAW;AAAA,IAC/E,CAAC;AAGD,UAAM,oBAAoBA,cAAAA,IAAI;AAAA,MAC5B;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,EAAE,MAAM,YAAY,MAAM,YAAa;AAAA,UACvC,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,EAAE,MAAM,YAAY,MAAM,YAAa;AAAA,UACvC,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,EAAE,MAAM,UAAU,MAAM,YAAa;AAAA,QACtC;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,QAAQ;AAAA,UACN,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,QAC5B;AAAA,MACF;AAAA,IACH,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,MAAM,CAAC,MAAM,MAAM,KAAK;AAAA,QACxB,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,aAAa;AAAA,MACd;AAAA,IACH,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,EAAE,MAAM,YAAY,MAAM,YAAa;AAAA,QACxC;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,EAAE,MAAM,OAAO,MAAM,KAAM;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,EAAE,MAAM,UAAU,MAAM,YAAa;AAAA,QACtC;AAAA,MACF;AAAA,IACH,CAAC;AAGD,UAAM,oBAAoB,MAAM;AAC9BC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,SAAS;AAAA,QACX,SAAS,CAAC,QAAQ;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,WAAW,IAAI;AAAA,YACtB,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,UAAU;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,OAAO,WAAW,MAAM,KAAK,EAAE,IAAI;AAAA,QAC1C,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,YAAY;AACrC,iBAAW,2DAA2D,QAAQ,EAAE,EAAE;AAAA,IACpF;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAC/B,iBAAW,wDAAwD,KAAK,EAAE,EAAE;AAAA,IAC9E;AAGA,UAAM,YAAY,CAAC,YAAY;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,QAAQ;AACzB,cAAO,KAAG;AAAA,QACR,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,MACH;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,WAAW,MAAM;AAErBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AAAA,IAEhB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtbD,GAAG,WAAW,eAAe;"}