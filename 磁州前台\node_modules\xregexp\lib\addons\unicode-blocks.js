'use strict';

Object.defineProperty(exports, "__esModule", {
    value: true
});

/*!
 * XRegExp Unicode Blocks 4.0.0
 * <xregexp.com>
 * <PERSON> (c) 2010-2017 MIT License
 * Unicode data by <PERSON> <mathiasbynens.be>
 */

exports.default = function (XRegExp) {

    /**
     * Adds support for all Unicode blocks. Block names use the prefix 'In'. E.g.,
     * `\p{InBasicLatin}`. Token names are case insensitive, and any spaces, hyphens, and
     * underscores are ignored.
     *
     * Uses Unicode 9.0.0.
     *
     * @requires XRegExp, Unicode Base
     */

    if (!XRegExp.addUnicodeData) {
        throw new ReferenceError('Unicode Base must be loaded before Unicode Blocks');
    }

    XRegExp.addUnicodeData([{
        name: 'InAdlam',
        astral: '\uD83A[\uDD00-\uDD5F]'
    }, {
        name: 'InAegean_Numbers',
        astral: '\uD800[\uDD00-\uDD3F]'
    }, {
        name: 'InAhom',
        astral: '\uD805[\uDF00-\uDF3F]'
    }, {
        name: 'InAlchemical_Symbols',
        astral: '\uD83D[\uDF00-\uDF7F]'
    }, {
        name: 'InAlphabetic_Presentation_Forms',
        bmp: '\uFB00-\uFB4F'
    }, {
        name: 'InAnatolian_Hieroglyphs',
        astral: '\uD811[\uDC00-\uDE7F]'
    }, {
        name: 'InAncient_Greek_Musical_Notation',
        astral: '\uD834[\uDE00-\uDE4F]'
    }, {
        name: 'InAncient_Greek_Numbers',
        astral: '\uD800[\uDD40-\uDD8F]'
    }, {
        name: 'InAncient_Symbols',
        astral: '\uD800[\uDD90-\uDDCF]'
    }, {
        name: 'InArabic',
        bmp: '\u0600-\u06FF'
    }, {
        name: 'InArabic_Extended_A',
        bmp: '\u08A0-\u08FF'
    }, {
        name: 'InArabic_Mathematical_Alphabetic_Symbols',
        astral: '\uD83B[\uDE00-\uDEFF]'
    }, {
        name: 'InArabic_Presentation_Forms_A',
        bmp: '\uFB50-\uFDFF'
    }, {
        name: 'InArabic_Presentation_Forms_B',
        bmp: '\uFE70-\uFEFF'
    }, {
        name: 'InArabic_Supplement',
        bmp: '\u0750-\u077F'
    }, {
        name: 'InArmenian',
        bmp: '\u0530-\u058F'
    }, {
        name: 'InArrows',
        bmp: '\u2190-\u21FF'
    }, {
        name: 'InAvestan',
        astral: '\uD802[\uDF00-\uDF3F]'
    }, {
        name: 'InBalinese',
        bmp: '\u1B00-\u1B7F'
    }, {
        name: 'InBamum',
        bmp: '\uA6A0-\uA6FF'
    }, {
        name: 'InBamum_Supplement',
        astral: '\uD81A[\uDC00-\uDE3F]'
    }, {
        name: 'InBasic_Latin',
        bmp: '\0-\x7F'
    }, {
        name: 'InBassa_Vah',
        astral: '\uD81A[\uDED0-\uDEFF]'
    }, {
        name: 'InBatak',
        bmp: '\u1BC0-\u1BFF'
    }, {
        name: 'InBengali',
        bmp: '\u0980-\u09FF'
    }, {
        name: 'InBhaiksuki',
        astral: '\uD807[\uDC00-\uDC6F]'
    }, {
        name: 'InBlock_Elements',
        bmp: '\u2580-\u259F'
    }, {
        name: 'InBopomofo',
        bmp: '\u3100-\u312F'
    }, {
        name: 'InBopomofo_Extended',
        bmp: '\u31A0-\u31BF'
    }, {
        name: 'InBox_Drawing',
        bmp: '\u2500-\u257F'
    }, {
        name: 'InBrahmi',
        astral: '\uD804[\uDC00-\uDC7F]'
    }, {
        name: 'InBraille_Patterns',
        bmp: '\u2800-\u28FF'
    }, {
        name: 'InBuginese',
        bmp: '\u1A00-\u1A1F'
    }, {
        name: 'InBuhid',
        bmp: '\u1740-\u175F'
    }, {
        name: 'InByzantine_Musical_Symbols',
        astral: '\uD834[\uDC00-\uDCFF]'
    }, {
        name: 'InCJK_Compatibility',
        bmp: '\u3300-\u33FF'
    }, {
        name: 'InCJK_Compatibility_Forms',
        bmp: '\uFE30-\uFE4F'
    }, {
        name: 'InCJK_Compatibility_Ideographs',
        bmp: '\uF900-\uFAFF'
    }, {
        name: 'InCJK_Compatibility_Ideographs_Supplement',
        astral: '\uD87E[\uDC00-\uDE1F]'
    }, {
        name: 'InCJK_Radicals_Supplement',
        bmp: '\u2E80-\u2EFF'
    }, {
        name: 'InCJK_Strokes',
        bmp: '\u31C0-\u31EF'
    }, {
        name: 'InCJK_Symbols_and_Punctuation',
        bmp: '\u3000-\u303F'
    }, {
        name: 'InCJK_Unified_Ideographs',
        bmp: '\u4E00-\u9FFF'
    }, {
        name: 'InCJK_Unified_Ideographs_Extension_A',
        bmp: '\u3400-\u4DBF'
    }, {
        name: 'InCJK_Unified_Ideographs_Extension_B',
        astral: '[\uD840-\uD868][\uDC00-\uDFFF]|\uD869[\uDC00-\uDEDF]'
    }, {
        name: 'InCJK_Unified_Ideographs_Extension_C',
        astral: '\uD869[\uDF00-\uDFFF]|[\uD86A-\uD86C][\uDC00-\uDFFF]|\uD86D[\uDC00-\uDF3F]'
    }, {
        name: 'InCJK_Unified_Ideographs_Extension_D',
        astral: '\uD86D[\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1F]'
    }, {
        name: 'InCJK_Unified_Ideographs_Extension_E',
        astral: '\uD86E[\uDC20-\uDFFF]|[\uD86F-\uD872][\uDC00-\uDFFF]|\uD873[\uDC00-\uDEAF]'
    }, {
        name: 'InCarian',
        astral: '\uD800[\uDEA0-\uDEDF]'
    }, {
        name: 'InCaucasian_Albanian',
        astral: '\uD801[\uDD30-\uDD6F]'
    }, {
        name: 'InChakma',
        astral: '\uD804[\uDD00-\uDD4F]'
    }, {
        name: 'InCham',
        bmp: '\uAA00-\uAA5F'
    }, {
        name: 'InCherokee',
        bmp: '\u13A0-\u13FF'
    }, {
        name: 'InCherokee_Supplement',
        bmp: '\uAB70-\uABBF'
    }, {
        name: 'InCombining_Diacritical_Marks',
        bmp: '\u0300-\u036F'
    }, {
        name: 'InCombining_Diacritical_Marks_Extended',
        bmp: '\u1AB0-\u1AFF'
    }, {
        name: 'InCombining_Diacritical_Marks_Supplement',
        bmp: '\u1DC0-\u1DFF'
    }, {
        name: 'InCombining_Diacritical_Marks_for_Symbols',
        bmp: '\u20D0-\u20FF'
    }, {
        name: 'InCombining_Half_Marks',
        bmp: '\uFE20-\uFE2F'
    }, {
        name: 'InCommon_Indic_Number_Forms',
        bmp: '\uA830-\uA83F'
    }, {
        name: 'InControl_Pictures',
        bmp: '\u2400-\u243F'
    }, {
        name: 'InCoptic',
        bmp: '\u2C80-\u2CFF'
    }, {
        name: 'InCoptic_Epact_Numbers',
        astral: '\uD800[\uDEE0-\uDEFF]'
    }, {
        name: 'InCounting_Rod_Numerals',
        astral: '\uD834[\uDF60-\uDF7F]'
    }, {
        name: 'InCuneiform',
        astral: '\uD808[\uDC00-\uDFFF]'
    }, {
        name: 'InCuneiform_Numbers_and_Punctuation',
        astral: '\uD809[\uDC00-\uDC7F]'
    }, {
        name: 'InCurrency_Symbols',
        bmp: '\u20A0-\u20CF'
    }, {
        name: 'InCypriot_Syllabary',
        astral: '\uD802[\uDC00-\uDC3F]'
    }, {
        name: 'InCyrillic',
        bmp: '\u0400-\u04FF'
    }, {
        name: 'InCyrillic_Extended_A',
        bmp: '\u2DE0-\u2DFF'
    }, {
        name: 'InCyrillic_Extended_B',
        bmp: '\uA640-\uA69F'
    }, {
        name: 'InCyrillic_Extended_C',
        bmp: '\u1C80-\u1C8F'
    }, {
        name: 'InCyrillic_Supplement',
        bmp: '\u0500-\u052F'
    }, {
        name: 'InDeseret',
        astral: '\uD801[\uDC00-\uDC4F]'
    }, {
        name: 'InDevanagari',
        bmp: '\u0900-\u097F'
    }, {
        name: 'InDevanagari_Extended',
        bmp: '\uA8E0-\uA8FF'
    }, {
        name: 'InDingbats',
        bmp: '\u2700-\u27BF'
    }, {
        name: 'InDomino_Tiles',
        astral: '\uD83C[\uDC30-\uDC9F]'
    }, {
        name: 'InDuployan',
        astral: '\uD82F[\uDC00-\uDC9F]'
    }, {
        name: 'InEarly_Dynastic_Cuneiform',
        astral: '\uD809[\uDC80-\uDD4F]'
    }, {
        name: 'InEgyptian_Hieroglyphs',
        astral: '\uD80C[\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F]'
    }, {
        name: 'InElbasan',
        astral: '\uD801[\uDD00-\uDD2F]'
    }, {
        name: 'InEmoticons',
        astral: '\uD83D[\uDE00-\uDE4F]'
    }, {
        name: 'InEnclosed_Alphanumeric_Supplement',
        astral: '\uD83C[\uDD00-\uDDFF]'
    }, {
        name: 'InEnclosed_Alphanumerics',
        bmp: '\u2460-\u24FF'
    }, {
        name: 'InEnclosed_CJK_Letters_and_Months',
        bmp: '\u3200-\u32FF'
    }, {
        name: 'InEnclosed_Ideographic_Supplement',
        astral: '\uD83C[\uDE00-\uDEFF]'
    }, {
        name: 'InEthiopic',
        bmp: '\u1200-\u137F'
    }, {
        name: 'InEthiopic_Extended',
        bmp: '\u2D80-\u2DDF'
    }, {
        name: 'InEthiopic_Extended_A',
        bmp: '\uAB00-\uAB2F'
    }, {
        name: 'InEthiopic_Supplement',
        bmp: '\u1380-\u139F'
    }, {
        name: 'InGeneral_Punctuation',
        bmp: '\u2000-\u206F'
    }, {
        name: 'InGeometric_Shapes',
        bmp: '\u25A0-\u25FF'
    }, {
        name: 'InGeometric_Shapes_Extended',
        astral: '\uD83D[\uDF80-\uDFFF]'
    }, {
        name: 'InGeorgian',
        bmp: '\u10A0-\u10FF'
    }, {
        name: 'InGeorgian_Supplement',
        bmp: '\u2D00-\u2D2F'
    }, {
        name: 'InGlagolitic',
        bmp: '\u2C00-\u2C5F'
    }, {
        name: 'InGlagolitic_Supplement',
        astral: '\uD838[\uDC00-\uDC2F]'
    }, {
        name: 'InGothic',
        astral: '\uD800[\uDF30-\uDF4F]'
    }, {
        name: 'InGrantha',
        astral: '\uD804[\uDF00-\uDF7F]'
    }, {
        name: 'InGreek_Extended',
        bmp: '\u1F00-\u1FFF'
    }, {
        name: 'InGreek_and_Coptic',
        bmp: '\u0370-\u03FF'
    }, {
        name: 'InGujarati',
        bmp: '\u0A80-\u0AFF'
    }, {
        name: 'InGurmukhi',
        bmp: '\u0A00-\u0A7F'
    }, {
        name: 'InHalfwidth_and_Fullwidth_Forms',
        bmp: '\uFF00-\uFFEF'
    }, {
        name: 'InHangul_Compatibility_Jamo',
        bmp: '\u3130-\u318F'
    }, {
        name: 'InHangul_Jamo',
        bmp: '\u1100-\u11FF'
    }, {
        name: 'InHangul_Jamo_Extended_A',
        bmp: '\uA960-\uA97F'
    }, {
        name: 'InHangul_Jamo_Extended_B',
        bmp: '\uD7B0-\uD7FF'
    }, {
        name: 'InHangul_Syllables',
        bmp: '\uAC00-\uD7AF'
    }, {
        name: 'InHanunoo',
        bmp: '\u1720-\u173F'
    }, {
        name: 'InHatran',
        astral: '\uD802[\uDCE0-\uDCFF]'
    }, {
        name: 'InHebrew',
        bmp: '\u0590-\u05FF'
    }, {
        name: 'InHigh_Private_Use_Surrogates',
        bmp: '\uDB80-\uDBFF'
    }, {
        name: 'InHigh_Surrogates',
        bmp: '\uD800-\uDB7F'
    }, {
        name: 'InHiragana',
        bmp: '\u3040-\u309F'
    }, {
        name: 'InIPA_Extensions',
        bmp: '\u0250-\u02AF'
    }, {
        name: 'InIdeographic_Description_Characters',
        bmp: '\u2FF0-\u2FFF'
    }, {
        name: 'InIdeographic_Symbols_and_Punctuation',
        astral: '\uD81B[\uDFE0-\uDFFF]'
    }, {
        name: 'InImperial_Aramaic',
        astral: '\uD802[\uDC40-\uDC5F]'
    }, {
        name: 'InInscriptional_Pahlavi',
        astral: '\uD802[\uDF60-\uDF7F]'
    }, {
        name: 'InInscriptional_Parthian',
        astral: '\uD802[\uDF40-\uDF5F]'
    }, {
        name: 'InJavanese',
        bmp: '\uA980-\uA9DF'
    }, {
        name: 'InKaithi',
        astral: '\uD804[\uDC80-\uDCCF]'
    }, {
        name: 'InKana_Supplement',
        astral: '\uD82C[\uDC00-\uDCFF]'
    }, {
        name: 'InKanbun',
        bmp: '\u3190-\u319F'
    }, {
        name: 'InKangxi_Radicals',
        bmp: '\u2F00-\u2FDF'
    }, {
        name: 'InKannada',
        bmp: '\u0C80-\u0CFF'
    }, {
        name: 'InKatakana',
        bmp: '\u30A0-\u30FF'
    }, {
        name: 'InKatakana_Phonetic_Extensions',
        bmp: '\u31F0-\u31FF'
    }, {
        name: 'InKayah_Li',
        bmp: '\uA900-\uA92F'
    }, {
        name: 'InKharoshthi',
        astral: '\uD802[\uDE00-\uDE5F]'
    }, {
        name: 'InKhmer',
        bmp: '\u1780-\u17FF'
    }, {
        name: 'InKhmer_Symbols',
        bmp: '\u19E0-\u19FF'
    }, {
        name: 'InKhojki',
        astral: '\uD804[\uDE00-\uDE4F]'
    }, {
        name: 'InKhudawadi',
        astral: '\uD804[\uDEB0-\uDEFF]'
    }, {
        name: 'InLao',
        bmp: '\u0E80-\u0EFF'
    }, {
        name: 'InLatin_Extended_Additional',
        bmp: '\u1E00-\u1EFF'
    }, {
        name: 'InLatin_Extended_A',
        bmp: '\u0100-\u017F'
    }, {
        name: 'InLatin_Extended_B',
        bmp: '\u0180-\u024F'
    }, {
        name: 'InLatin_Extended_C',
        bmp: '\u2C60-\u2C7F'
    }, {
        name: 'InLatin_Extended_D',
        bmp: '\uA720-\uA7FF'
    }, {
        name: 'InLatin_Extended_E',
        bmp: '\uAB30-\uAB6F'
    }, {
        name: 'InLatin_1_Supplement',
        bmp: '\x80-\xFF'
    }, {
        name: 'InLepcha',
        bmp: '\u1C00-\u1C4F'
    }, {
        name: 'InLetterlike_Symbols',
        bmp: '\u2100-\u214F'
    }, {
        name: 'InLimbu',
        bmp: '\u1900-\u194F'
    }, {
        name: 'InLinear_A',
        astral: '\uD801[\uDE00-\uDF7F]'
    }, {
        name: 'InLinear_B_Ideograms',
        astral: '\uD800[\uDC80-\uDCFF]'
    }, {
        name: 'InLinear_B_Syllabary',
        astral: '\uD800[\uDC00-\uDC7F]'
    }, {
        name: 'InLisu',
        bmp: '\uA4D0-\uA4FF'
    }, {
        name: 'InLow_Surrogates',
        bmp: '\uDC00-\uDFFF'
    }, {
        name: 'InLycian',
        astral: '\uD800[\uDE80-\uDE9F]'
    }, {
        name: 'InLydian',
        astral: '\uD802[\uDD20-\uDD3F]'
    }, {
        name: 'InMahajani',
        astral: '\uD804[\uDD50-\uDD7F]'
    }, {
        name: 'InMahjong_Tiles',
        astral: '\uD83C[\uDC00-\uDC2F]'
    }, {
        name: 'InMalayalam',
        bmp: '\u0D00-\u0D7F'
    }, {
        name: 'InMandaic',
        bmp: '\u0840-\u085F'
    }, {
        name: 'InManichaean',
        astral: '\uD802[\uDEC0-\uDEFF]'
    }, {
        name: 'InMarchen',
        astral: '\uD807[\uDC70-\uDCBF]'
    }, {
        name: 'InMathematical_Alphanumeric_Symbols',
        astral: '\uD835[\uDC00-\uDFFF]'
    }, {
        name: 'InMathematical_Operators',
        bmp: '\u2200-\u22FF'
    }, {
        name: 'InMeetei_Mayek',
        bmp: '\uABC0-\uABFF'
    }, {
        name: 'InMeetei_Mayek_Extensions',
        bmp: '\uAAE0-\uAAFF'
    }, {
        name: 'InMende_Kikakui',
        astral: '\uD83A[\uDC00-\uDCDF]'
    }, {
        name: 'InMeroitic_Cursive',
        astral: '\uD802[\uDDA0-\uDDFF]'
    }, {
        name: 'InMeroitic_Hieroglyphs',
        astral: '\uD802[\uDD80-\uDD9F]'
    }, {
        name: 'InMiao',
        astral: '\uD81B[\uDF00-\uDF9F]'
    }, {
        name: 'InMiscellaneous_Mathematical_Symbols_A',
        bmp: '\u27C0-\u27EF'
    }, {
        name: 'InMiscellaneous_Mathematical_Symbols_B',
        bmp: '\u2980-\u29FF'
    }, {
        name: 'InMiscellaneous_Symbols',
        bmp: '\u2600-\u26FF'
    }, {
        name: 'InMiscellaneous_Symbols_and_Arrows',
        bmp: '\u2B00-\u2BFF'
    }, {
        name: 'InMiscellaneous_Symbols_and_Pictographs',
        astral: '\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDDFF]'
    }, {
        name: 'InMiscellaneous_Technical',
        bmp: '\u2300-\u23FF'
    }, {
        name: 'InModi',
        astral: '\uD805[\uDE00-\uDE5F]'
    }, {
        name: 'InModifier_Tone_Letters',
        bmp: '\uA700-\uA71F'
    }, {
        name: 'InMongolian',
        bmp: '\u1800-\u18AF'
    }, {
        name: 'InMongolian_Supplement',
        astral: '\uD805[\uDE60-\uDE7F]'
    }, {
        name: 'InMro',
        astral: '\uD81A[\uDE40-\uDE6F]'
    }, {
        name: 'InMultani',
        astral: '\uD804[\uDE80-\uDEAF]'
    }, {
        name: 'InMusical_Symbols',
        astral: '\uD834[\uDD00-\uDDFF]'
    }, {
        name: 'InMyanmar',
        bmp: '\u1000-\u109F'
    }, {
        name: 'InMyanmar_Extended_A',
        bmp: '\uAA60-\uAA7F'
    }, {
        name: 'InMyanmar_Extended_B',
        bmp: '\uA9E0-\uA9FF'
    }, {
        name: 'InNKo',
        bmp: '\u07C0-\u07FF'
    }, {
        name: 'InNabataean',
        astral: '\uD802[\uDC80-\uDCAF]'
    }, {
        name: 'InNew_Tai_Lue',
        bmp: '\u1980-\u19DF'
    }, {
        name: 'InNewa',
        astral: '\uD805[\uDC00-\uDC7F]'
    }, {
        name: 'InNumber_Forms',
        bmp: '\u2150-\u218F'
    }, {
        name: 'InOgham',
        bmp: '\u1680-\u169F'
    }, {
        name: 'InOl_Chiki',
        bmp: '\u1C50-\u1C7F'
    }, {
        name: 'InOld_Hungarian',
        astral: '\uD803[\uDC80-\uDCFF]'
    }, {
        name: 'InOld_Italic',
        astral: '\uD800[\uDF00-\uDF2F]'
    }, {
        name: 'InOld_North_Arabian',
        astral: '\uD802[\uDE80-\uDE9F]'
    }, {
        name: 'InOld_Permic',
        astral: '\uD800[\uDF50-\uDF7F]'
    }, {
        name: 'InOld_Persian',
        astral: '\uD800[\uDFA0-\uDFDF]'
    }, {
        name: 'InOld_South_Arabian',
        astral: '\uD802[\uDE60-\uDE7F]'
    }, {
        name: 'InOld_Turkic',
        astral: '\uD803[\uDC00-\uDC4F]'
    }, {
        name: 'InOptical_Character_Recognition',
        bmp: '\u2440-\u245F'
    }, {
        name: 'InOriya',
        bmp: '\u0B00-\u0B7F'
    }, {
        name: 'InOrnamental_Dingbats',
        astral: '\uD83D[\uDE50-\uDE7F]'
    }, {
        name: 'InOsage',
        astral: '\uD801[\uDCB0-\uDCFF]'
    }, {
        name: 'InOsmanya',
        astral: '\uD801[\uDC80-\uDCAF]'
    }, {
        name: 'InPahawh_Hmong',
        astral: '\uD81A[\uDF00-\uDF8F]'
    }, {
        name: 'InPalmyrene',
        astral: '\uD802[\uDC60-\uDC7F]'
    }, {
        name: 'InPau_Cin_Hau',
        astral: '\uD806[\uDEC0-\uDEFF]'
    }, {
        name: 'InPhags_pa',
        bmp: '\uA840-\uA87F'
    }, {
        name: 'InPhaistos_Disc',
        astral: '\uD800[\uDDD0-\uDDFF]'
    }, {
        name: 'InPhoenician',
        astral: '\uD802[\uDD00-\uDD1F]'
    }, {
        name: 'InPhonetic_Extensions',
        bmp: '\u1D00-\u1D7F'
    }, {
        name: 'InPhonetic_Extensions_Supplement',
        bmp: '\u1D80-\u1DBF'
    }, {
        name: 'InPlaying_Cards',
        astral: '\uD83C[\uDCA0-\uDCFF]'
    }, {
        name: 'InPrivate_Use_Area',
        bmp: '\uE000-\uF8FF'
    }, {
        name: 'InPsalter_Pahlavi',
        astral: '\uD802[\uDF80-\uDFAF]'
    }, {
        name: 'InRejang',
        bmp: '\uA930-\uA95F'
    }, {
        name: 'InRumi_Numeral_Symbols',
        astral: '\uD803[\uDE60-\uDE7F]'
    }, {
        name: 'InRunic',
        bmp: '\u16A0-\u16FF'
    }, {
        name: 'InSamaritan',
        bmp: '\u0800-\u083F'
    }, {
        name: 'InSaurashtra',
        bmp: '\uA880-\uA8DF'
    }, {
        name: 'InSharada',
        astral: '\uD804[\uDD80-\uDDDF]'
    }, {
        name: 'InShavian',
        astral: '\uD801[\uDC50-\uDC7F]'
    }, {
        name: 'InShorthand_Format_Controls',
        astral: '\uD82F[\uDCA0-\uDCAF]'
    }, {
        name: 'InSiddham',
        astral: '\uD805[\uDD80-\uDDFF]'
    }, {
        name: 'InSinhala',
        bmp: '\u0D80-\u0DFF'
    }, {
        name: 'InSinhala_Archaic_Numbers',
        astral: '\uD804[\uDDE0-\uDDFF]'
    }, {
        name: 'InSmall_Form_Variants',
        bmp: '\uFE50-\uFE6F'
    }, {
        name: 'InSora_Sompeng',
        astral: '\uD804[\uDCD0-\uDCFF]'
    }, {
        name: 'InSpacing_Modifier_Letters',
        bmp: '\u02B0-\u02FF'
    }, {
        name: 'InSpecials',
        bmp: '\uFFF0-\uFFFF'
    }, {
        name: 'InSundanese',
        bmp: '\u1B80-\u1BBF'
    }, {
        name: 'InSundanese_Supplement',
        bmp: '\u1CC0-\u1CCF'
    }, {
        name: 'InSuperscripts_and_Subscripts',
        bmp: '\u2070-\u209F'
    }, {
        name: 'InSupplemental_Arrows_A',
        bmp: '\u27F0-\u27FF'
    }, {
        name: 'InSupplemental_Arrows_B',
        bmp: '\u2900-\u297F'
    }, {
        name: 'InSupplemental_Arrows_C',
        astral: '\uD83E[\uDC00-\uDCFF]'
    }, {
        name: 'InSupplemental_Mathematical_Operators',
        bmp: '\u2A00-\u2AFF'
    }, {
        name: 'InSupplemental_Punctuation',
        bmp: '\u2E00-\u2E7F'
    }, {
        name: 'InSupplemental_Symbols_and_Pictographs',
        astral: '\uD83E[\uDD00-\uDDFF]'
    }, {
        name: 'InSupplementary_Private_Use_Area_A',
        astral: '[\uDB80-\uDBBF][\uDC00-\uDFFF]'
    }, {
        name: 'InSupplementary_Private_Use_Area_B',
        astral: '[\uDBC0-\uDBFF][\uDC00-\uDFFF]'
    }, {
        name: 'InSutton_SignWriting',
        astral: '\uD836[\uDC00-\uDEAF]'
    }, {
        name: 'InSyloti_Nagri',
        bmp: '\uA800-\uA82F'
    }, {
        name: 'InSyriac',
        bmp: '\u0700-\u074F'
    }, {
        name: 'InTagalog',
        bmp: '\u1700-\u171F'
    }, {
        name: 'InTagbanwa',
        bmp: '\u1760-\u177F'
    }, {
        name: 'InTags',
        astral: '\uDB40[\uDC00-\uDC7F]'
    }, {
        name: 'InTai_Le',
        bmp: '\u1950-\u197F'
    }, {
        name: 'InTai_Tham',
        bmp: '\u1A20-\u1AAF'
    }, {
        name: 'InTai_Viet',
        bmp: '\uAA80-\uAADF'
    }, {
        name: 'InTai_Xuan_Jing_Symbols',
        astral: '\uD834[\uDF00-\uDF5F]'
    }, {
        name: 'InTakri',
        astral: '\uD805[\uDE80-\uDECF]'
    }, {
        name: 'InTamil',
        bmp: '\u0B80-\u0BFF'
    }, {
        name: 'InTangut',
        astral: '[\uD81C-\uD821][\uDC00-\uDFFF]'
    }, {
        name: 'InTangut_Components',
        astral: '\uD822[\uDC00-\uDEFF]'
    }, {
        name: 'InTelugu',
        bmp: '\u0C00-\u0C7F'
    }, {
        name: 'InThaana',
        bmp: '\u0780-\u07BF'
    }, {
        name: 'InThai',
        bmp: '\u0E00-\u0E7F'
    }, {
        name: 'InTibetan',
        bmp: '\u0F00-\u0FFF'
    }, {
        name: 'InTifinagh',
        bmp: '\u2D30-\u2D7F'
    }, {
        name: 'InTirhuta',
        astral: '\uD805[\uDC80-\uDCDF]'
    }, {
        name: 'InTransport_and_Map_Symbols',
        astral: '\uD83D[\uDE80-\uDEFF]'
    }, {
        name: 'InUgaritic',
        astral: '\uD800[\uDF80-\uDF9F]'
    }, {
        name: 'InUnified_Canadian_Aboriginal_Syllabics',
        bmp: '\u1400-\u167F'
    }, {
        name: 'InUnified_Canadian_Aboriginal_Syllabics_Extended',
        bmp: '\u18B0-\u18FF'
    }, {
        name: 'InVai',
        bmp: '\uA500-\uA63F'
    }, {
        name: 'InVariation_Selectors',
        bmp: '\uFE00-\uFE0F'
    }, {
        name: 'InVariation_Selectors_Supplement',
        astral: '\uDB40[\uDD00-\uDDEF]'
    }, {
        name: 'InVedic_Extensions',
        bmp: '\u1CD0-\u1CFF'
    }, {
        name: 'InVertical_Forms',
        bmp: '\uFE10-\uFE1F'
    }, {
        name: 'InWarang_Citi',
        astral: '\uD806[\uDCA0-\uDCFF]'
    }, {
        name: 'InYi_Radicals',
        bmp: '\uA490-\uA4CF'
    }, {
        name: 'InYi_Syllables',
        bmp: '\uA000-\uA48F'
    }, {
        name: 'InYijing_Hexagram_Symbols',
        bmp: '\u4DC0-\u4DFF'
    }]);
};

module.exports = exports['default'];