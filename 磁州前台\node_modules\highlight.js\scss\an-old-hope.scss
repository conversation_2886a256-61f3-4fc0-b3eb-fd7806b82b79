/* 

An Old Hope – Star Wars Syntax (c) <PERSON> <gusbe<PERSON><PERSON><PERSON>@gmail.com>
Original theme - Ocean Dark Theme – by https://github.com/gavsiu
Based on <PERSON>'s Atom syntax theme 'An Old Hope' – https://github.com/JesseLeite/an-old-hope-syntax-atom

*/

/* Death Star Comment */
.hljs-comment,
.hljs-quote 
{
  color: #B6B18B;
}

/* Darth Vader */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion 
{
  color: #EB3C54;
}

/* Threepio */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link 
{
  color: #E7CE56;
}

/* <PERSON> */
.hljs-attribute 
{
  color: #EE7C2B;
}

/* <PERSON><PERSON> */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition 
{
  color: #4FB4D7;
}

/* Yoda */
.hljs-title,
.hljs-section 
{
  color: #78BB65;
}

/* Mace Windu */
.hljs-keyword,
.hljs-selector-tag 
{
  color: #B45EA4;
}

/* Millenium Falcon */
.hljs 
{
  display: block;
  overflow-x: auto;
  background: #1C1D21;
  color: #c0c5ce;
  padding: 0.5em;
}

.hljs-emphasis 
{
  font-style: italic;
}

.hljs-strong 
{
  font-weight: bold;
}
