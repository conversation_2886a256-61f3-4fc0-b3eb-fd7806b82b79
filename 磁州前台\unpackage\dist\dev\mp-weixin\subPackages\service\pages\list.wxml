<view class="service-list-container premium-style data-v-09770677" data-service-type="{{P}}"><view class="custom-navbar data-v-09770677" style="{{'padding-top:' + c}}"><view class="back-btn data-v-09770677" bindtap="{{a}}"><view class="back-icon data-v-09770677"></view></view><view class="navbar-title data-v-09770677">{{b}}</view><view class="navbar-right data-v-09770677"></view></view><view wx:if="{{d}}" class="search-container data-v-09770677"><view class="search-box data-v-09770677"><image class="search-icon data-v-09770677" src="{{e}}"></image><input class="search-input data-v-09770677" type="text" placeholder="搜索职位、公司或关键词" confirm-type="search" bindconfirm="{{f}}" value="{{g}}" bindinput="{{h}}"/><view wx:if="{{i}}" class="search-cancel data-v-09770677" bindtap="{{j}}">×</view></view></view><scroll-view wx:if="{{k}}" class="category-tabs data-v-09770677" scroll-x show-scrollbar="false" enhanced bounces="{{true}}"><view wx:for="{{l}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', 'data-v-09770677', tab.c && 'active']}}" bindtap="{{tab.d}}">{{tab.a}}</view></scroll-view><scroll-view wx:if="{{m}}" class="subcategory-tabs data-v-09770677" scroll-x show-scrollbar="false" enhanced bounces="{{true}}"><view wx:for="{{n}}" wx:for-item="subTab" wx:key="b" class="{{['subtab-item', 'data-v-09770677', subTab.c && 'active']}}" bindtap="{{subTab.d}}">{{subTab.a}}</view></scroll-view><view class="filter-container data-v-09770677"><view class="filter-wrapper data-v-09770677"><view class="filter-item data-v-09770677" ref="areaBtn" bindtap="{{r}}"><text class="{{['data-v-09770677', p && 'active-filter']}}">{{o}}</text><view class="{{['filter-arrow', 'data-v-09770677', q && 'arrow-up']}}"></view></view><view class="filter-item data-v-09770677" ref="sortBtn" bindtap="{{w}}"><text class="{{['data-v-09770677', t && 'active-filter']}}">{{s}}</text><view class="{{['filter-arrow', 'data-v-09770677', v && 'arrow-up']}}"></view></view></view></view><view wx:if="{{x}}" class="filter-dropdown area-dropdown data-v-09770677" style="{{'top:' + z}}"><scroll-view scroll-y class="dropdown-scroll data-v-09770677"><view wx:for="{{y}}" wx:for-item="area" wx:key="c" class="{{['dropdown-item', 'data-v-09770677', area.d && 'active-item']}}" bindtap="{{area.e}}"><text class="dropdown-item-text data-v-09770677">{{area.a}}</text><text wx:if="{{area.b}}" class="dropdown-item-check data-v-09770677">✓</text></view></scroll-view></view><view wx:if="{{A}}" class="filter-dropdown sort-dropdown data-v-09770677" style="{{'top:' + C}}"><view wx:for="{{B}}" wx:for-item="sort" wx:key="c" class="{{['dropdown-item', 'data-v-09770677', sort.d && 'active-item']}}" bindtap="{{sort.e}}"><text class="dropdown-item-text data-v-09770677">{{sort.a}}</text><text wx:if="{{sort.b}}" class="dropdown-item-check data-v-09770677">✓</text></view></view><view wx:if="{{D}}" class="filter-mask data-v-09770677" bindtap="{{E}}"></view><scroll-view scroll-y class="service-scroll data-v-09770677" bindscrolltolower="{{L}}" refresher-enabled refresher-triggered="{{M}}" bindrefresherrefresh="{{N}}" enhanced bounces="{{true}}" show-scrollbar="{{false}}"><view wx:if="{{F}}" class="service-list data-v-09770677"><view wx:for="{{G}}" wx:for-item="item" wx:key="m" class="service-item data-v-09770677" hover-class="service-item-hover" bindtap="{{item.n}}"><view class="service-content data-v-09770677"><view class="service-header data-v-09770677"><view class="service-header-left data-v-09770677"><text class="service-tag data-v-09770677">{{item.a}}</text><text wx:if="{{item.b}}" class="service-subcategory data-v-09770677">{{item.c}}</text></view><view class="service-meta-right data-v-09770677"><text class="service-area data-v-09770677">{{item.d}}</text></view></view><view class="service-main data-v-09770677"><view class="service-title-wrapper data-v-09770677"><text class="service-title data-v-09770677">{{item.e}}</text><text wx:if="{{item.f}}" class="service-price data-v-09770677">¥{{item.g}}</text></view><view wx:if="{{item.h}}" class="service-images data-v-09770677"><image wx:for="{{item.i}}" wx:for-item="img" wx:key="a" src="{{img.b}}" mode="aspectFill" class="{{['service-image', 'data-v-09770677', item.j && 'single-image']}}"></image></view><view class="service-footer data-v-09770677"><view class="service-meta data-v-09770677"><view class="meta-tag data-v-09770677"><text class="meta-views data-v-09770677">{{item.k}}人浏览</text></view><view class="meta-time data-v-09770677">{{item.l}}</view></view><view class="service-actions data-v-09770677"><view class="action-btn contact-btn data-v-09770677"><text class="action-text data-v-09770677">联系</text></view></view></view></view></view></view></view><view wx:else class="empty-state data-v-09770677"><image src="{{H}}" mode="aspectFit" class="empty-image data-v-09770677"></image><text class="empty-text data-v-09770677">暂无相关职位信息</text><text class="empty-subtext data-v-09770677">换个筛选条件试试吧</text><view class="empty-btn data-v-09770677" bindtap="{{I}}">重置筛选</view></view><view wx:if="{{J}}" class="loading-more data-v-09770677"><view class="loading-indicator data-v-09770677"></view><text class="loading-text data-v-09770677">加载中...</text></view><view wx:if="{{K}}" class="loading-done data-v-09770677"><text class="loading-done-text data-v-09770677">— 已经到底啦 —</text></view></scroll-view><view class="publish-btn data-v-09770677" hover-class="publish-btn-hover" bindtap="{{O}}"><text class="publish-icon data-v-09770677">+</text><text class="publish-text data-v-09770677">发布</text></view></view>