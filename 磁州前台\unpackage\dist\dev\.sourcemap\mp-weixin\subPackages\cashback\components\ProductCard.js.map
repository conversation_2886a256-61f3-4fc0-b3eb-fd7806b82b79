{"version": 3, "file": "ProductCard.js", "sources": ["subPackages/cashback/components/ProductCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvY2FzaGJhY2svY29tcG9uZW50cy9Qcm9kdWN0Q2FyZC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"product-card\" @tap=\"navigateToDetail\">\r\n    <!-- 商品图片 -->\r\n    <view class=\"product-image-container\">\r\n      <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\" />\r\n      <view v-if=\"product.discount\" class=\"discount-tag\">\r\n        <text>{{ product.discount }}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品信息 -->\r\n    <view class=\"product-info\">\r\n      <!-- 商品平台 -->\r\n      <view class=\"product-platform\">\r\n        <image v-if=\"product.platformIcon\" class=\"platform-icon\" :src=\"product.platformIcon\" mode=\"aspectFit\" />\r\n        <text class=\"platform-name\">{{ product.platformName }}</text>\r\n      </view>\r\n      \r\n      <!-- 商品标题 -->\r\n      <view class=\"product-title\">\r\n        <text>{{ product.title }}</text>\r\n      </view>\r\n      \r\n      <!-- 价格和返利 -->\r\n      <view class=\"product-price-row\">\r\n        <view class=\"product-price\">\r\n          <text class=\"price-symbol\">¥</text>\r\n          <text class=\"price-value\">{{ formatPrice(product.price) }}</text>\r\n        </view>\r\n        <view class=\"product-cashback\">\r\n          <text>返{{ product.cashbackRate }}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 销量和收藏 -->\r\n      <view class=\"product-meta\">\r\n        <view class=\"product-sales\">\r\n          <text>已售{{ formatSales(product.sales) }}</text>\r\n        </view>\r\n        <view class=\"product-favorite\" @tap.stop=\"toggleFavorite\">\r\n          <svg class=\"favorite-icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n            <path v-if=\"isFavorite\" fill=\"#FF6B6B\" d=\"M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z\" />\r\n            <path v-else fill=\"#999999\" d=\"M12.1,18.55L12,18.65L11.89,18.55C7.14,14.24 4,11.39 4,8.5C4,6.5 5.5,5 7.5,5C9.04,5 10.54,6 11.07,7.36H12.93C13.46,6 14.96,5 16.5,5C18.5,5 20,6.5 20,8.5C20,11.39 16.86,14.24 12.1,18.55M16.5,3C14.76,3 13.09,3.81 12,5.08C10.91,3.81 9.24,3 7.5,3C4.42,3 2,5.41 2,8.5C2,12.27 5.4,15.36 10.55,20.03L12,21.35L13.45,20.03C18.6,15.36 22,12.27 22,8.5C22,5.41 19.58,3 16.5,3Z\" />\r\n          </svg>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ProductCard',\r\n  props: {\r\n    product: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isFavorite: false\r\n    };\r\n  },\r\n  created() {\r\n    // 初始化收藏状态\r\n    this.isFavorite = this.product.isFavorite || false;\r\n  },\r\n  methods: {\r\n    // 导航到商品详情\r\n    navigateToDetail() {\r\n      uni.navigateTo({\r\n        url: `/subPackages/cashback/pages/detail/index?id=${this.product.id}`\r\n      });\r\n    },\r\n    \r\n    // 切换收藏状态\r\n    toggleFavorite() {\r\n      this.isFavorite = !this.isFavorite;\r\n      this.$emit('favorite', {\r\n        id: this.product.id,\r\n        isFavorite: this.isFavorite\r\n      });\r\n    },\r\n    \r\n    // 格式化价格\r\n    formatPrice(price) {\r\n      return parseFloat(price).toFixed(2);\r\n    },\r\n    \r\n    // 格式化销量\r\n    formatSales(sales) {\r\n      if (sales >= 10000) {\r\n        return (sales / 10000).toFixed(1) + '万';\r\n      }\r\n      return sales;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.product-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 12px;\r\n  \r\n  .product-image-container {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 0;\r\n    padding-bottom: 100%;\r\n    overflow: hidden;\r\n    \r\n    .product-image {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n    }\r\n    \r\n    .discount-tag {\r\n      position: absolute;\r\n      top: 8px;\r\n      left: 8px;\r\n      padding: 4px 8px;\r\n      background: linear-gradient(135deg, #FF6B6B 0%, #FF4F4F 100%);\r\n      border-radius: 12px;\r\n      \r\n      text {\r\n        color: #FFFFFF;\r\n        font-size: 12px;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .product-info {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .product-platform {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 6px;\r\n    \r\n    .platform-icon {\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-right: 4px;\r\n    }\r\n    \r\n    .platform-name {\r\n      font-size: 12px;\r\n      color: #999999;\r\n    }\r\n  }\r\n  \r\n  .product-title {\r\n    margin-bottom: 8px;\r\n    \r\n    text {\r\n      font-size: 14px;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 2;\r\n      -webkit-box-orient: vertical;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n    }\r\n  }\r\n  \r\n  .product-price-row {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 8px;\r\n    \r\n    .product-price {\r\n      display: flex;\r\n      align-items: baseline;\r\n      \r\n      .price-symbol {\r\n        font-size: 12px;\r\n        color: #FF6B6B;\r\n        margin-right: 2px;\r\n      }\r\n      \r\n      .price-value {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #FF6B6B;\r\n      }\r\n    }\r\n    \r\n    .product-cashback {\r\n      padding: 2px 6px;\r\n      background-color: rgba(156, 39, 176, 0.1);\r\n      border-radius: 10px;\r\n      \r\n      text {\r\n        font-size: 12px;\r\n        color: #9C27B0;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .product-meta {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    \r\n    .product-sales {\r\n      text {\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n    }\r\n    \r\n    .product-favorite {\r\n      padding: 6px;\r\n      margin-right: -6px;\r\n    }\r\n  }\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/components/ProductCard.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAmDA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,SAAS;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA;EAEf;AAAA,EACD,UAAU;AAER,SAAK,aAAa,KAAK,QAAQ,cAAc;AAAA,EAC9C;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,mBAAmB;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+CAA+C,KAAK,QAAQ,EAAE;AAAA,MACrE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,aAAa,CAAC,KAAK;AACxB,WAAK,MAAM,YAAY;AAAA,QACrB,IAAI,KAAK,QAAQ;AAAA,QACjB,YAAY,KAAK;AAAA,MACnB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,aAAO,WAAW,KAAK,EAAE,QAAQ,CAAC;AAAA,IACnC;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,UAAI,SAAS,KAAO;AAClB,gBAAQ,QAAQ,KAAO,QAAQ,CAAC,IAAI;AAAA,MACtC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjGA,GAAG,gBAAgB,SAAS;"}