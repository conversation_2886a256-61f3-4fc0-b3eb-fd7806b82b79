<template>
  <view class="recharge-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">充值</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 充值金额区域 -->
    <view class="recharge-section" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <view class="section-title">充值金额</view>
      <view class="amount-input-area">
        <text class="currency-symbol">¥</text>
        <input 
          class="amount-input" 
          type="digit" 
          v-model="amount" 
          placeholder="0.00"
          @input="handleAmountInput"
          focus
        />
      </view>
      <view class="amount-quick-select">
        <view 
          class="quick-amount-item" 
          v-for="(item, index) in quickAmounts" 
          :key="index"
          :class="{'selected': amount === item.toString()}"
          @click="selectAmount(item)"
        >
          {{item}}元
        </view>
      </view>
    </view>
    
    <!-- 充值方式 -->
    <view class="payment-method">
      <view class="section-title">充值方式</view>
      <view class="payment-options">
        <view 
          class="payment-option" 
          v-for="(item, index) in paymentMethods" 
          :key="index"
          :class="{'selected': selectedPayment === item.id}"
          @click="selectPayment(item.id)"
        >
          <view class="payment-left">
            <image :src="item.icon" class="payment-icon"></image>
            <text class="payment-name">{{ item.name }}</text>
          </view>
          <view class="payment-right">
            <view class="payment-check" v-if="selectedPayment === item.id">
              <image src="/static/images/tabbar/选中.png" class="check-icon"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 充值说明 -->
    <view class="recharge-notice">
      <view class="notice-title">充值说明</view>
      <view class="notice-item">1. 充值金额最低1元</view>
      <view class="notice-item">2. 充值成功后，余额将立即到账</view>
      <view class="notice-item">3. 如遇到充值问题，请联系客服</view>
    </view>
    
    <!-- 充值按钮 -->
    <view class="bottom-btn-area">
      <button 
        class="recharge-btn" 
        :disabled="!canRecharge" 
        :class="{'btn-disabled': !canRecharge}"
        @click="submitRecharge"
      >
        确认充值
      </button>
    </view>
  </view>
</template>

<script>
import { smartNavigate } from '@/utils/navigation.js';

export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      amount: '',
      selectedPayment: 'wxpay',
      quickAmounts: [10, 50, 100, 200, 500],
      paymentMethods: [
        {
          id: 'wxpay',
          name: '微信支付',
          icon: '/static/images/payment/wxpay.png'
        },
        {
          id: 'alipay',
          name: '支付宝支付',
          icon: '/static/images/payment/alipay.png'
        }
      ]
    }
  },
  computed: {
    canRecharge() {
      const amountNum = parseFloat(this.amount);
      return amountNum >= 1 && !isNaN(amountNum);
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 页面跳转
    navigateTo(url) {
      smartNavigate(url).catch(err => {
        console.error('页面跳转失败:', err);
      });
    },
    
    // 处理金额输入
    handleAmountInput(e) {
      const value = e.detail.value;
      // 限制只能输入两位小数
      if (value.indexOf('.') !== -1) {
        const decimal = value.split('.')[1];
        if (decimal.length > 2) {
          this.amount = parseFloat(value).toFixed(2);
        }
      }
    },
    
    // 选择快捷金额
    selectAmount(amount) {
      this.amount = amount.toString();
    },
    
    // 选择支付方式
    selectPayment(paymentId) {
      this.selectedPayment = paymentId;
    },
    
    // 提交充值
    submitRecharge() {
      if (!this.canRecharge) {
        return;
      }
      
      uni.showLoading({
        title: '请求中...'
      });
      
      // 模拟API请求
      setTimeout(() => {
        uni.hideLoading();
        
        // 根据不同支付方式处理
        if (this.selectedPayment === 'wxpay') {
          // 调用微信支付
          uni.showModal({
            title: '微信支付',
            content: `确认支付¥${this.amount}？`,
            success: (res) => {
              if (res.confirm) {
                this.processPayment();
              }
            }
          });
        } else if (this.selectedPayment === 'alipay') {
          // 调用支付宝支付
          uni.showModal({
            title: '支付宝支付',
            content: `确认支付¥${this.amount}？`,
            success: (res) => {
              if (res.confirm) {
                this.processPayment();
              }
            }
          });
        }
      }, 500);
    },
    
    // 处理支付结果
    processPayment() {
      uni.showLoading({
        title: '支付中...'
      });
      
      // 模拟支付过程
      setTimeout(() => {
        uni.hideLoading();
        
        // 支付成功后
        uni.showToast({
          title: '充值成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            setTimeout(() => {
              // 返回钱包页面
              uni.navigateBack();
            }, 2000);
          }
        });
      }, 1500);
    }
  }
}
</script>

<style>
.recharge-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 充值金额区域 */
.recharge-section {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.amount-input-area {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.currency-symbol {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.amount-input {
  font-size: 60rpx;
  font-weight: bold;
  flex: 1;
}

.amount-quick-select {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.quick-amount-item {
  width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #f8f9fc;
  border-radius: 35rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #333;
  border: 1px solid transparent;
}

.quick-amount-item.selected {
  background-color: #e6f0ff;
  color: #0052CC;
  border: 1px solid #0052CC;
}

/* 支付方式 */
.payment-method {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.payment-options {
  margin-top: 20rpx;
}

.payment-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.payment-option:last-child {
  border-bottom: none;
}

.payment-left {
  display: flex;
  align-items: center;
}

.payment-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.payment-name {
  font-size: 28rpx;
}

.payment-check {
  width: 40rpx;
  height: 40rpx;
}

.check-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 充值说明 */
.recharge-notice {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.notice-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.notice-item {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 底部按钮区域 */
.bottom-btn-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.recharge-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #0052CC;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}

.btn-disabled {
  background-color: #cccccc;
  color: #ffffff;
}
</style> 