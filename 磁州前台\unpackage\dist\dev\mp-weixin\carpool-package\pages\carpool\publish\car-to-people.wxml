<view class="publish-container"><carpool-nav wx:if="{{a}}" u-i="20a5db6e-0" bind:__l="__l" u-p="{{a}}"></carpool-nav><view class="form-container"><view class="form-group"><view class="form-title">行程信息</view><view class="form-item"><text class="label">出发地</text><view class="input-wrapper"><input type="text" placeholder="请输入出发地" value="{{b}}" bindinput="{{c}}"/><view class="location-btn" bindtap="{{e}}"><image src="{{d}}" mode="aspectFit"></image></view></view></view><view class="form-item"><text class="label">目的地</text><view class="input-wrapper"><input type="text" placeholder="请输入目的地" value="{{f}}" bindinput="{{g}}"/><view class="location-btn" bindtap="{{i}}"><image src="{{h}}" mode="aspectFit"></image></view></view></view><view class="form-item"><text class="label">途径地点</text><view class="via-points-container"><view wx:for="{{j}}" wx:for-item="point" wx:key="e" class="via-point-item"><view class="input-wrapper"><input type="text" placeholder="请输入途径地点" value="{{point.a}}" bindinput="{{point.b}}"/><view class="location-btn" bindtap="{{point.c}}"><image src="{{k}}" mode="aspectFit"></image></view></view><view class="via-point-actions"><view class="via-point-delete" bindtap="{{point.d}}"><text class="delete-icon">×</text></view></view></view><view wx:if="{{l}}" class="add-via-point" bindtap="{{m}}"><text class="add-icon">+</text><text class="add-text">添加途径地点</text></view></view></view><view class="form-item"><text class="label">出发日期</text><picker mode="date" value="{{p}}" start="2023-01-01" end="2030-12-31" bindchange="{{q}}"><view class="picker-value"><text>{{n}}</text><image src="{{o}}" mode="aspectFit"></image></view></picker></view><view class="form-item"><text class="label">出发时间</text><picker mode="time" value="{{t}}" bindchange="{{v}}"><view class="picker-value"><text>{{r}}</text><image src="{{s}}" mode="aspectFit"></image></view></picker></view><view class="form-item"><text class="label">车型</text><input type="text" placeholder="请输入车型，如：大众朗逸" value="{{w}}" bindinput="{{x}}"/></view><view class="form-item"><text class="label">空座数量</text><picker mode="selector" range="{{A}}" value="{{B}}" bindchange="{{C}}"><view class="picker-value"><text>{{y}}</text><image src="{{z}}" mode="aspectFit"></image></view></picker></view><view class="form-item"><text class="label">每人价格</text><view class="price-input"><input type="digit" placeholder="请输入每人价格（选填）" value="{{D}}" bindinput="{{E}}"/><text class="unit">元</text></view></view></view><view class="form-group"><view class="form-title">联系方式</view><view class="form-item"><text class="label">联系人</text><input type="text" placeholder="请输入联系人姓名（选填）" value="{{F}}" bindinput="{{G}}"/></view><view class="form-item"><text class="label">手机号码</text><input type="number" placeholder="请输入手机号码" maxlength="11" value="{{H}}" bindinput="{{I}}"/></view></view><view class="form-group"><view class="form-title">补充说明</view><view class="form-item"><block wx:if="{{r0}}"><textarea placeholder="请输入补充说明（选填）" maxlength="200" value="{{J}}" bindinput="{{K}}"/></block><view class="word-count">{{L}}/200</view></view></view></view><view class="submit-section"><view class="agreement"><checkbox-group bindchange="{{O}}"><label><checkbox checked="{{M}}" color="#0A84FF"/><text>我已阅读并同意</text><text class="link" catchtap="{{N}}">《拼车服务协议》</text></label></checkbox-group></view><view class="publish-options"><view class="options-title">选择发布方式</view><configurable-premium-actions wx:if="{{Q}}" bindactionCompleted="{{P}}" u-i="20a5db6e-1" bind:__l="__l" u-p="{{Q}}"/></view><button class="submit-btn" disabled="{{R}}" bindtap="{{S}}"> 发布信息 </button></view></view>