<template>
  <view class="poster-records-page">
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">分享记录</view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-overview">
      <view class="stat-item">
        <view class="stat-number">{{ stats.totalPosters }}</view>
        <view class="stat-label">生成海报</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ stats.totalShares }}</view>
        <view class="stat-label">分享次数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ stats.totalViews }}</view>
        <view class="stat-label">浏览量</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ stats.totalOrders }}</view>
        <view class="stat-label">转化订单</view>
      </view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <picker :value="filterType" :range="filterOptions" range-key="label" @change="onFilterChange">
        <view class="filter-item">
          <text>{{ filterOptions[filterType].label }}</text>
          <text class="iconfont icon-arrow-down"></text>
        </view>
      </picker>
      
      <picker mode="date" @change="onDateChange">
        <view class="filter-item">
          <text>{{ selectedDate || '选择日期' }}</text>
          <text class="iconfont icon-calendar"></text>
        </view>
      </picker>
    </view>

    <!-- 记录列表 -->
    <view class="records-list">
      <view v-for="record in filteredRecords" :key="record.id" class="record-item">
        <view class="record-poster">
          <image :src="record.posterUrl" mode="aspectFill" @click="previewPoster(record)" />
        </view>
        
        <view class="record-info">
          <view class="record-title">{{ record.productTitle }}</view>
          <view class="record-time">{{ record.createTime }}</view>
          <view class="record-stats">
            <text class="stat-text">分享{{ record.shareCount }}次</text>
            <text class="stat-text">浏览{{ record.viewCount }}次</text>
            <text class="stat-text">转化{{ record.orderCount }}单</text>
          </view>
        </view>
        
        <view class="record-actions">
          <view class="action-btn" @click="sharePoster(record)">
            <text class="iconfont icon-share"></text>
            <text>分享</text>
          </view>
          <view class="action-btn" @click="downloadPoster(record)">
            <text class="iconfont icon-download"></text>
            <text>下载</text>
          </view>
          <view class="action-btn delete" @click="deletePoster(record)">
            <text class="iconfont icon-delete"></text>
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="filteredRecords.length === 0" class="empty-state">
      <image src="/static/images/empty-poster.png" class="empty-image" />
      <text class="empty-text">暂无海报记录</text>
      <button class="create-btn" @click="createPoster">立即创建</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const filterType = ref(0)
const selectedDate = ref('')

const filterOptions = [
  { label: '全部', value: 'all' },
  { label: '今日', value: 'today' },
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' }
]

const stats = ref({
  totalPosters: 0,
  totalShares: 0,
  totalViews: 0,
  totalOrders: 0
})

const records = ref([])

const filteredRecords = computed(() => {
  let filtered = records.value
  
  // 按类型筛选
  const filter = filterOptions[filterType.value]
  if (filter.value !== 'all') {
    const now = new Date()
    filtered = filtered.filter(record => {
      const recordDate = new Date(record.createTime)
      switch (filter.value) {
        case 'today':
          return recordDate.toDateString() === now.toDateString()
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          return recordDate >= weekAgo
        case 'month':
          return recordDate.getMonth() === now.getMonth()
        default:
          return true
      }
    })
  }
  
  // 按日期筛选
  if (selectedDate.value) {
    filtered = filtered.filter(record => 
      record.createTime.includes(selectedDate.value)
    )
  }
  
  return filtered
})

const previewPoster = (record) => {
  uni.previewImage({
    urls: [record.posterUrl]
  })
}

const sharePoster = (record) => {
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
    success: (res) => {
      // 分享逻辑
    }
  })
}

const downloadPoster = (record) => {
  uni.downloadFile({
    url: record.posterUrl,
    success: () => {
      uni.showToast({ title: '下载成功' })
    }
  })
}

const deletePoster = (record) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这张海报吗？',
    success: (res) => {
      if (res.confirm) {
        // 删除逻辑
        uni.showToast({ title: '删除成功' })
      }
    }
  })
}

onMounted(() => {
  loadRecords()
})
</script>

<style lang="scss" scoped>
.stats-overview {
  display: flex;
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .stat-item {
    flex: 1;
    text-align: center;
    
    .stat-number {
      font-size: 32rpx;
      font-weight: bold;
      color: #ff6b6b;
      margin-bottom: 10rpx;
    }
    
    .stat-label {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.filter-bar {
  display: flex;
  background: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  gap: 20rpx;
  
  .filter-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15rpx 20rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    font-size: 26rpx;
    color: #333;
  }
}

.record-item {
  display: flex;
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  
  .record-poster {
    margin-right: 20rpx;
    
    image {
      width: 120rpx;
      height: 160rpx;
      border-radius: 8rpx;
    }
  }
  
  .record-info {
    flex: 1;
    
    .record-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .record-time {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 15rpx;
    }
    
    .record-stats {
      display: flex;
      gap: 20rpx;
      
      .stat-text {
        font-size: 22rpx;
        color: #666;
      }
    }
  }
  
  .record-actions {
    display: flex;
    flex-direction: column;
    gap: 15rpx;
    
    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 20rpx;
      color: #666;
      
      .iconfont {
        font-size: 28rpx;
        margin-bottom: 5rpx;
      }
      
      &.delete {
        color: #f44336;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    display: block;
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
  
  .create-btn {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: #fff;
    padding: 20rpx 60rpx;
    border-radius: 40rpx;
  }
}
</style>