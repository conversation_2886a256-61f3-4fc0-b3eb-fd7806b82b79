<template>
	<view class="partner-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-left" @click="goBack">
				<image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
			</view>
			<view class="navbar-title">合伙人中心</view>
			<view class="navbar-right">
				<!-- 预留位置与发布页面保持一致 -->
			</view>
		</view>
		
		<!-- 添加顶部安全区域 -->
		<view class="safe-area-top"></view>
		
		<!-- 用户卡片 -->
		<view class="user-card">
			<image class="partner-avatar" :src="partnerInfo.avatar || '/static/images/avatar/default.png'" mode="aspectFill"></image>
			<view class="user-info">
				<view class="nickname">{{partnerInfo.nickname || '游客'}}</view>
				<view class="level-info">
					<image class="level-icon" :src="getLevelIcon()" mode="aspectFit"></image>
					<text class="level-name">{{getLevelName()}}</text>
					</view>
				<view class="progress-bar">
					<view class="progress" :style="{width: progressWidth + '%'}"></view>
				</view>
			</view>
			<view class="partner-upgrade" @click="navigateTo('/subPackages/partner/pages/partner-upgrade')" v-if="canUpgrade">
				<text class="upgrade-text">升级</text>
				</view>
			</view>
			
		<!-- 收益数据卡片 -->
		<view class="partner-card income-card">
			<view class="income-header">
				<view class="title">收益统计</view>
				<view class="total-income">总收益 <text>¥{{partnerInfo.totalIncome}}</text></view>
			</view>
			<view class="data-row">
				<view class="data-item">
					<view class="data-value">¥{{incomeData.today}}</view>
					<view class="data-label">今日收益</view>
				</view>
				<view class="data-item">
					<view class="data-value">¥{{incomeData.yesterday}}</view>
					<view class="data-label">昨日收益</view>
				</view>
				<view class="data-item">
					<view class="data-value">¥{{incomeData.thisWeek}}</view>
					<view class="data-label">本周收益</view>
				</view>
			</view>
			<view class="chart-container">
				<view class="chart-bars">
					<view class="chart-bar" v-for="(item, index) in chartData" :key="index">
						<view class="bar-value">{{item.value}}元</view>
						<view class="bar-item" :style="{height: item.height + 'rpx'}"></view>
						<text class="bar-date">{{item.date}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 功能卡片 -->
		<view class="partner-card function-card">
			<view class="partner-header">
				<view class="title">功能区</view>
			</view>
			<view class="function-list">
				<view class="function-item" @click="navigateTo('/subPackages/partner/pages/partner-income')">
					<view class="icon-block" style="background-color: #6A9DFF;"></view>
					<view class="name">收益明细</view>
					</view>
				<view class="function-item" @click="navigateTo('/subPackages/partner/pages/partner-fans')">
					<view class="icon-block" style="background-color: #FF7D54;"></view>
					<view class="name">我的粉丝</view>
				</view>
				<view class="function-item" @click="navigateTo('/subPackages/partner/pages/partner-withdraw')">
					<view class="icon-block" style="background-color: #5ACB95;"></view>
					<view class="name">提现</view>
					</view>
				<view class="function-item" @click="navigateTo('/subPackages/partner/pages/partner-poster')">
					<view class="icon-block" style="background-color: #FFA940;"></view>
					<view class="name">推广海报</view>
				</view>
				<view class="function-item" @click="navigateTo('/subPackages/partner/pages/partner-qrcode')">
					<view class="icon-block" style="background-color: #9254DE;"></view>
					<view class="name">我的二维码</view>
				</view>
				<view class="function-item" @click="navigateTo('/subPackages/partner/pages/partner-rules')">
					<view class="icon-block" style="background-color: #36CFC9;"></view>
					<view class="name">规则说明</view>
				</view>
			</view>
		</view>
		
		<!-- 等级权益卡片 -->
		<view class="partner-card level-card">
			<view class="partner-header">
				<view class="title">等级权益</view>
				<view class="more" @click="navigateTo('/subPackages/partner/pages/partner-levels')">
					查看详情 <text class="cuIcon-right"></text>
				</view>
			</view>
			<view class="level-benefits">
				<view class="benefit-item">
					<image class="icon" src="/static/images/tabbar/commission.png" mode="aspectFit"></image>
					<view class="info">
						<view class="name">一级佣金比例</view>
						<view class="desc">直接推广用户下单获得佣金</view>
					</view>
					<view class="rate">{{getCommissionRate(1)}}%</view>
				</view>
				<view class="benefit-item">
					<image class="icon" src="/static/images/tabbar/commission2.png" mode="aspectFit"></image>
					<view class="info">
						<view class="name">二级佣金比例</view>
						<view class="desc">间接推广用户下单获得佣金</view>
					</view>
					<view class="rate">{{getCommissionRate(2)}}%</view>
				</view>
			</view>
		</view>
		
		<!-- 推广数据卡片 -->
		<view class="partner-card promotion-card">
			<view class="partner-header">
				<view class="title">推广数据</view>
			</view>
			<view class="promotion-data">
				<view class="data-item">
					<view class="data-value">{{promotionData.viewCount}}</view>
					<view class="data-label">访问人数 <text class="cuIcon-info"></text></view>
				</view>
				<view class="data-item">
					<view class="data-value">{{promotionData.registerCount}}</view>
					<view class="data-label">注册人数 <text class="cuIcon-info"></text></view>
				</view>
				<view class="data-item">
					<view class="data-value">{{promotionData.orderCount}}</view>
					<view class="data-label">下单人数 <text class="cuIcon-info"></text></view>
				</view>
				<view class="data-item">
					<view class="data-value">{{promotionData.conversionRate}}</view>
					<view class="data-label">转化率 <text class="cuIcon-info"></text></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { getLocalUserInfo } from '@/utils/userProfile.js';
import { smartNavigate } from '@/utils/navigation.js';

// 响应式状态
const partnerInfo = reactive({
	nickname: '',
	avatar: '',
	level: 1, // 1: 普通合伙人, 2: 银牌合伙人, 3: 金牌合伙人, 4: 钻石合伙人
	totalIncome: '0.00',
	thisMonthIncome: '0.00',
	fansCount: '0'
});

const incomeData = reactive({
	today: '0.00',
	yesterday: '0.00',
	thisWeek: '0.00',
	chartData: [] // 最近7天的收益数据
});

const promotionData = reactive({
	viewCount: '0',
	registerCount: '0',
	orderCount: '0',
	conversionRate: '0%'
});

const progressWidth = ref(25); // 等级进度条宽度百分比

// 计算属性
const canUpgrade = computed(() => {
	return partnerInfo.level < 4; // 钻石合伙人是最高级别
});

const chartData = computed(() => {
	// 转换收益数据为图表数据
	const data = incomeData.chartData || [];
	// 找出最大值，用于计算高度比例
	const maxValue = Math.max(...data, 10); // 至少为10，避免全部为0的情况
	
	// 计算每天的数据
	return [
		{ date: '周一', value: data[0] || 0, height: data[0] ? (data[0] / maxValue) * 150 : 0 },
		{ date: '周二', value: data[1] || 0, height: data[1] ? (data[1] / maxValue) * 150 : 0 },
		{ date: '周三', value: data[2] || 0, height: data[2] ? (data[2] / maxValue) * 150 : 0 },
		{ date: '周四', value: data[3] || 0, height: data[3] ? (data[3] / maxValue) * 150 : 0 },
		{ date: '周五', value: data[4] || 0, height: data[4] ? (data[4] / maxValue) * 150 : 0 },
		{ date: '周六', value: data[5] || 0, height: data[5] ? (data[5] / maxValue) * 150 : 0 },
		{ date: '周日', value: data[6] || 0, height: data[6] ? (data[6] / maxValue) * 150 : 0 }
	];
});

// 生命周期钩子
onMounted(() => {
	// 获取用户信息
	getUserInfo();
	// 获取合伙人信息
	getPartnerInfo();
	// 获取收益数据
	getIncomeData();
	// 获取推广数据
	getPromotionData();
});

// 方法
// 获取用户信息
const getUserInfo = () => {
	const userInfo = getLocalUserInfo();
	if (userInfo) {
		partnerInfo.nickname = userInfo.nickname;
		partnerInfo.avatar = userInfo.avatar;
	}
};

// 获取合伙人信息
const getPartnerInfo = () => {
	// 模拟数据，实际应从API获取
	Object.assign(partnerInfo, {
		level: 2,
		totalIncome: '1280.50',
		thisMonthIncome: '320.75',
		fansCount: '28'
	});
	
	// 计算进度条宽度
	progressWidth.value = (partnerInfo.level / 4) * 100;
};

// 获取收益数据
const getIncomeData = () => {
	// 模拟数据，实际应从API获取
	Object.assign(incomeData, {
		today: '35.60',
		yesterday: '42.80',
		thisWeek: '198.30',
		chartData: [12.5, 18.3, 25.6, 42.8, 35.6, 10.5, 8.2]
	});
};

// 获取推广数据
const getPromotionData = () => {
	// 模拟数据，实际应从API获取
	Object.assign(promotionData, {
		viewCount: '156',
		registerCount: '32',
		orderCount: '18',
		conversionRate: '11.5%'
	});
};

// 获取等级图标
const getLevelIcon = () => {
	const icons = {
		1: '/static/images/tabbar/partner-level-1.png',
		2: '/static/images/tabbar/partner-level-2.png',
		3: '/static/images/tabbar/partner-level-3.png',
		4: '/static/images/tabbar/partner-level-4.png'
	};
	return icons[partnerInfo.level] || icons[1];
};

// 获取等级名称
const getLevelName = () => {
	const names = {
		1: '普通合伙人',
		2: '银牌合伙人',
		3: '金牌合伙人',
		4: '钻石合伙人'
	};
	return names[partnerInfo.level] || names[1];
};

// 获取佣金比例
const getCommissionRate = (level) => {
	// 根据合伙人等级返回不同的佣金比例
	const commissionRates = {
		1: { 1: 5, 2: 2 },    // 普通合伙人：一级5%，二级2%
		2: { 1: 8, 2: 3 },    // 银牌合伙人：一级8%，二级3%
		3: { 1: 12, 2: 5 },   // 金牌合伙人：一级12%，二级5%
		4: { 1: 15, 2: 8 }    // 钻石合伙人：一级15%，二级8%
	};
	
	return commissionRates[partnerInfo.level]?.[level] || 0;
};

// 页面跳转
const navigateTo = (url) => {
	smartNavigate(url).catch(err => {
		console.error('智能导航失败:', err);
	});
};

// 返回键
const goBack = () => {
	uni.navigateBack({
		fail: () => {
			// 如果返回失败（无页面可返回），则跳转到首页
			uni.switchTab({
				url: '/pages/my/my'
			});
		}
	});
};
</script>

<style lang="scss" scoped>
	.partner-container {
		padding-bottom: 30rpx;
		min-height: 100vh;
		background-color: #F6F8FB;
	}
	
	.safe-area {
		height: 120rpx;
		width: 100%;
	}
	
	.safe-area-top {
		height: 180rpx;
		width: 100%;
	}
	
	.partner-card {
		margin: 30rpx;
		border-radius: 20rpx;
		background: #FFFFFF;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 20rpx rgba(22, 119, 255, 0.08);
		border: 1px solid rgba(22, 119, 255, 0.1);
		transition: all 0.3s ease;
		
		&:hover, &:active {
			transform: translateY(-4rpx);
			box-shadow: 0 12rpx 24rpx rgba(22, 119, 255, 0.12);
		}
	}
	
	.partner-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30rpx;
		position: relative;
		
		&::after {
			content: '';
			position: absolute;
			bottom: -15rpx;
			left: 0;
			width: 60rpx;
			height: 4rpx;
			background: #1677FF;
			border-radius: 2rpx;
		}
		
		.title {
			font-size: 34rpx;
			font-weight: 700;
			color: #333333;
		}
		
		.more {
			font-size: 26rpx;
			color: #1677FF;
			display: flex;
		align-items: center;
			
			.cuIcon-right {
				font-size: 24rpx;
				margin-left: 4rpx;
			}
		}
	}
	
	.user-card {
		display: flex;
		align-items: center;
		padding: 40rpx 30rpx;
		background: linear-gradient(135deg, #64B5FF, #A0D2FF);
		margin: 30rpx 30rpx 30rpx; /* 调整顶部边距 */
		border-radius: 20rpx;
		color: #FFFFFF;
		box-shadow: 0 10rpx 30rpx rgba(22, 119, 255, 0.3);
	
	.partner-avatar {
			width: 120rpx;
			height: 120rpx;
			border-radius: 60rpx;
			border: 4rpx solid #FFFFFF;
			margin-right: 20rpx;
			box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
			background-color: #FFFFFF;
		}
		
		.user-info {
			flex: 1;
			
			.nickname {
				font-size: 38rpx;
		font-weight: 600;
		margin-bottom: 12rpx;
				text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}
	
			.level-info {
		display: flex;
		align-items: center;
				margin-bottom: 20rpx;
				
				.level-icon {
					width: 40rpx;
					height: 40rpx;
		margin-right: 10rpx;
				}
				
				.level-name {
					font-size: 28rpx;
				}
			}
			
			.progress-bar {
				height: 12rpx;
				background-color: rgba(255, 255, 255, 0.3);
				border-radius: 6rpx;
				overflow: hidden;
				
				.progress {
					height: 100%;
					background-color: #FFFFFF;
					border-radius: 6rpx;
					transition: width 0.8s ease;
				}
			}
	}
	
	.partner-upgrade {
		display: flex;
		align-items: center;
			justify-content: center;
			padding: 14rpx 28rpx;
			background: linear-gradient(90deg, #1677FF, #4F9DFF);
		border-radius: 30rpx;
			margin-left: 20rpx;
			box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
			transition: all 0.3s ease;
			
			&:active {
				transform: scale(0.95);
			}
			
			.upgrade-text {
		font-size: 26rpx;
		color: #FFFFFF;
				text-align: center;
				font-weight: 700;
			}
		}
	}
	
	.income-card {
		margin-bottom: 30rpx;
		
		.income-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			
			.title {
				font-size: 34rpx;
				font-weight: 700;
				color: #333333;
			}
			
			.total-income {
				font-size: 28rpx;
				color: #666666;
				
				text {
					color: #FF6B00;
					font-weight: 600;
					margin-left: 10rpx;
					font-size: 32rpx;
				}
			}
		}
		
		.data-row {
		display: flex;
			justify-content: space-between;
			margin-bottom: 30rpx;
	
	.data-item {
		flex: 1;
		text-align: center;
		position: relative;
				
				&::after {
					content: '';
					position: absolute;
					bottom: -15rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 60%;
					height: 3rpx;
					background: linear-gradient(90deg, transparent, #1677FF, transparent);
				}
			}
		}
		
		.chart-container {
			height: 260rpx;
			width: 100%;
			position: relative;
			margin-top: 20rpx;
		background-color: #FFFFFF;
			padding: 20rpx 10rpx 0;
		}
		
		.chart-bars {
		display: flex;
		justify-content: space-between;
			align-items: flex-end;
			width: 100%;
			height: 200rpx;
		}
		
		.chart-bar {
		display: flex;
			flex-direction: column;
		align-items: center;
			justify-content: flex-end;
			flex: 1;
		height: 100%;
	}
	
		.bar-value {
			font-size: 20rpx;
			color: #666666;
			margin-bottom: 4rpx;
		}
		
		.bar-item {
			width: 30rpx;
			background: linear-gradient(180deg, #1677FF, #64B5FF);
			border-radius: 15rpx 15rpx 0 0;
		}
		
		.bar-date {
			margin-top: 10rpx;
			font-size: 22rpx;
			color: #999999;
		}
	}
	
	.data-value {
		font-size: 42rpx;
		font-weight: 700 !important;
		color: #1677FF !important;
		margin-bottom: 10rpx;
		letter-spacing: 1rpx;
	}
	
	.data-label {
		font-size: 24rpx;
		color: #76ABFF !important;
		font-weight: 400 !important;
	}
	
	.function-card {
		.function-list {
		display: flex;
		flex-wrap: wrap;
	
	.function-item {
				width: 33.33%;
				text-align: center;
				padding: 30rpx 0;
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.95);
					background-color: rgba(22, 119, 255, 0.05);
					border-radius: 10rpx;
				}
				
				.icon-block {
					width: 90rpx;
					height: 90rpx;
					margin-bottom: 16rpx;
					transition: transform 0.3s ease;
					border-radius: 24rpx;
					margin: 0 auto 16rpx;
				}
	
				&:active .icon-block {
					transform: scale(1.1);
				}
				
				.name {
					font-size: 26rpx;
					color: #333333;
					font-weight: 500;
				}
			}
		}
	}
	
	.level-card {
		.level-benefits {
			margin-top: 20rpx;
			
			.benefit-item {
		display: flex;
		align-items: center;
				padding: 24rpx 0;
				border-bottom: 1px solid #F5F5F5;
				
				&:last-child {
					border-bottom: none;
				}
				
				.icon {
					width: 70rpx;
					height: 70rpx;
					margin-right: 20rpx;
					background-color: rgba(22, 119, 255, 0.1);
					padding: 12rpx;
		border-radius: 50%;
				}
				
				.info {
					flex: 1;
					
					.name {
						font-size: 30rpx;
						color: #333333;
						margin-bottom: 8rpx;
		font-weight: 500;
	}
	
					.desc {
						font-size: 24rpx;
						color: #999999;
					}
				}
				
				.rate {
					font-size: 36rpx;
					font-weight: 700;
					color: #FF6B00;
					background-color: rgba(255, 107, 0, 0.1);
					padding: 8rpx 20rpx;
					border-radius: 30rpx;
				}
			}
		}
	}
	
	.promotion-card {
	.promotion-data {
		display: flex;
		flex-wrap: wrap;
	
			.data-item {
		width: 50%;
				padding: 24rpx 0;
				position: relative;
				
				&::after {
		content: '';
		position: absolute;
					bottom: 10rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 60%;
					height: 3rpx;
					background: linear-gradient(90deg, transparent, #1677FF, transparent);
				}
				
				.cuIcon-info {
					margin-left: 6rpx;
					color: #76ABFF;
				}
			}
		}
	}
	
	.summary-items {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -10rpx;
		
		.summary-item {
			width: calc(50% - 20rpx);
			margin: 10rpx;
			background-color: #FFFFFF;
			border-radius: 16rpx;
			padding: 24rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
			
			.item-title {
				font-size: 26rpx;
				color: #999999;
				margin-bottom: 10rpx;
			}
			
			.item-value {
				font-size: 38rpx;
				font-weight: 700;
				color: #1677FF;
			}
		}
	}
	
	/* 增强文本样式 */
	.text-bold {
		font-weight: 700 !important;
	}
	
	/* 导航栏样式 */
	.navbar {
		position: relative;
		z-index: 1;
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* 自定义导航栏 */
	.custom-navbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 88rpx;
		padding: 0 30rpx;
		padding-top: 44px; /* 状态栏高度 */
		position: fixed; /* 改为固定定位 */
		top: 0;
		left: 0;
		right: 0;
		background-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */
		box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
		z-index: 100; /* 提高z-index确保在最上层 */
	}
	
	.navbar-title {
		position: absolute;
		left: 0;
		right: 0;
		color: #ffffff;
		font-size: 36rpx;
		font-weight: 700;
		font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
		text-align: center;
	}
	
	.navbar-right {
		width: 40rpx;
		height: 40rpx;
	}
	
	.navbar-left {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		z-index: 20; /* 确保在标题上层，可以被点击 */
	}
	
	.back-icon {
		width: 100%;
		height: 100%;
	}
</style>