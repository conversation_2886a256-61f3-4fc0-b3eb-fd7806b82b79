<template>
	<view class="levels-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-left" @click="goBack">
				<image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
			</view>
			<view class="navbar-title">等级说明</view>
			<view class="navbar-right">
				<!-- 预留位置与发布页面保持一致 -->
			</view>
		</view>
		
		<!-- 添加顶部安全区域 -->
		<view class="safe-area-top"></view>
		
		<!-- 当前等级信息 -->
		<view class="current-level-card">
			<view class="level-header">
				<view class="level-info">
					<image class="level-badge" :src="getLevelIcon()"></image>
					<view class="level-detail">
						<text class="level-name">{{ getLevelName() }}</text>
						<text class="level-desc">{{ getLevelDesc() }}</text>
					</view>
				</view>
				<view class="upgrade-btn" v-if="canUpgrade" @click="showUpgradeModal">
					<text>立即升级</text>
				</view>
			</view>
			
			<view class="level-progress">
				<view class="progress-info">
					<text class="progress-text">距离下一等级还需</text>
					<text class="progress-value">{{ nextLevelRequirement }}</text>
				</view>
				<view class="progress-track">
					<view class="progress-bar" :style="{ width: progressWidth + '%' }"></view>
				</view>
			</view>
		</view>
		
		<!-- 等级特权 -->
		<view class="privileges-card">
			<view class="card-title">等级特权</view>
			
			<view class="privileges-table">
				<view class="table-header">
					<view class="header-cell level-cell">等级</view>
					<view class="header-cell">一级佣金</view>
					<view class="header-cell">二级佣金</view>
					<view class="header-cell">特权</view>
				</view>
				
				<view class="table-row" :class="{ active: partnerInfo.level === 1 }">
					<view class="table-cell level-cell">
						<view class="level-tag level-1">
							<image class="mini-badge" src="/static/images/tabbar/partner-level-1.png"></image>
							<text>普通合伙人</text>
						</view>
					</view>
					<view class="table-cell">5%</view>
					<view class="table-cell">2%</view>
					<view class="table-cell">基础推广权益</view>
				</view>
				
				<view class="table-row" :class="{ active: partnerInfo.level === 2 }">
					<view class="table-cell level-cell">
						<view class="level-tag level-2">
							<image class="mini-badge" src="/static/images/tabbar/partner-level-2.png"></image>
							<text>银牌合伙人</text>
						</view>
					</view>
					<view class="table-cell">8%</view>
					<view class="table-cell">3%</view>
					<view class="table-cell">专属推广海报</view>
				</view>
				
				<view class="table-row" :class="{ active: partnerInfo.level === 3 }">
					<view class="table-cell level-cell">
						<view class="level-tag level-3">
							<image class="mini-badge" src="/static/images/tabbar/partner-level-3.png"></image>
							<text>金牌合伙人</text>
						</view>
					</view>
					<view class="table-cell">12%</view>
					<view class="table-cell">5%</view>
					<view class="table-cell">专属客服</view>
				</view>
				
				<view class="table-row" :class="{ active: partnerInfo.level === 4 }">
					<view class="table-cell level-cell">
						<view class="level-tag level-4">
							<image class="mini-badge" src="/static/images/tabbar/partner-level-4.png"></image>
							<text>钻石合伙人</text>
						</view>
					</view>
					<view class="table-cell">15%</view>
					<view class="table-cell">8%</view>
					<view class="table-cell">专属活动特权</view>
				</view>
			</view>
		</view>
		
		<!-- 升级规则 -->
		<view class="rules-card">
			<view class="card-title">升级规则</view>
			
			<view class="rule-item">
				<view class="rule-title">
					<view class="rule-dot"></view>
					<text>普通合伙人</text>
				</view>
				<view class="rule-content">
					<text>注册成为合伙人即可获得普通合伙人资格，享受一级佣金5%，二级佣金2%的分润。</text>
				</view>
			</view>
			
			<view class="rule-item">
				<view class="rule-title">
					<view class="rule-dot"></view>
					<text>银牌合伙人</text>
				</view>
				<view class="rule-content">
					<text>累计推广30名有效用户，或累计推广订单金额达到5000元，即可升级为银牌合伙人。</text>
				</view>
			</view>
			
			<view class="rule-item">
				<view class="rule-title">
					<view class="rule-dot"></view>
					<text>金牌合伙人</text>
				</view>
				<view class="rule-content">
					<text>累计推广100名有效用户，或累计推广订单金额达到20000元，即可升级为金牌合伙人。</text>
				</view>
			</view>
			
			<view class="rule-item">
				<view class="rule-title">
					<view class="rule-dot"></view>
					<text>钻石合伙人</text>
				</view>
				<view class="rule-content">
					<text>累计推广300名有效用户，或累计推广订单金额达到50000元，即可升级为钻石合伙人。</text>
				</view>
			</view>
		</view>
		
		<!-- 常见问题 -->
		<view class="faq-card">
			<view class="card-title">常见问题</view>
			
			<view class="faq-item" @click="toggleFaq(0)">
				<view class="faq-question">
					<text>什么是合伙人系统？</text>
					<image class="arrow-icon" :class="{ rotated: openedFaq === 0 }" src="/static/images/tabbar/arrow-down.png"></image>
				</view>
				<view class="faq-answer" v-if="openedFaq === 0">
					<text>合伙人系统是磁州同城为用户提供的推广分销系统，通过分享平台内容，邀请好友注册并消费，合伙人可以获得相应的佣金奖励。</text>
				</view>
			</view>
			
			<view class="faq-item" @click="toggleFaq(1)">
				<view class="faq-question">
					<text>如何成为合伙人？</text>
					<image class="arrow-icon" :class="{ rotated: openedFaq === 1 }" src="/static/images/tabbar/arrow-down.png"></image>
				</view>
				<view class="faq-answer" v-if="openedFaq === 1">
					<text>在"我的"页面点击"合伙人"，根据页面提示完成实名认证并同意合伙人协议即可成为普通合伙人。</text>
				</view>
			</view>
			
			<view class="faq-item" @click="toggleFaq(2)">
				<view class="faq-question">
					<text>佣金如何结算？</text>
					<image class="arrow-icon" :class="{ rotated: openedFaq === 2 }" src="/static/images/tabbar/arrow-down.png"></image>
				</view>
				<view class="faq-answer" v-if="openedFaq === 2">
					<text>佣金将在订单完成后自动计入您的合伙人账户，可在"收益提现"中申请提现至微信钱包或银行卡，T+1个工作日到账。</text>
				</view>
			</view>
			
			<view class="faq-item" @click="toggleFaq(3)">
				<view class="faq-question">
					<text>如何提高我的佣金收益？</text>
					<image class="arrow-icon" :class="{ rotated: openedFaq === 3 }" src="/static/images/tabbar/arrow-down.png"></image>
				</view>
				<view class="faq-answer" v-if="openedFaq === 3">
					<text>1. 提升合伙人等级，获得更高的佣金比例；\n2. 扩大您的推广范围，邀请更多用户；\n3. 关注平台活动，参与高佣金的特殊推广活动。</text>
				</view>
			</view>
		</view>
		
		<!-- 升级弹窗 -->
		<view class="upgrade-modal" v-if="showModal" @click="hideUpgradeModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">升级{{ getNextLevelName() }}</text>
					<view class="close-btn" @click="hideUpgradeModal">×</view>
				</view>
				
				<view class="modal-body">
					<view class="upgrade-info">
						<image class="upgrade-icon" src="/static/images/tabbar/upgrade.png"></image>
						<view class="upgrade-text">
							<text class="upgrade-title">当前等级：{{ getLevelName() }}</text>
							<text class="upgrade-desc">升级后将享受更高佣金比例和更多特权</text>
						</view>
					</view>
					
					<view class="upgrade-requirements">
						<view class="requirement-item">
							<text class="requirement-label">升级条件：</text>
							<text class="requirement-value">{{ getUpgradeRequirements() }}</text>
						</view>
						<view class="requirement-item">
							<text class="requirement-label">当前进度：</text>
							<text class="requirement-value">{{ getCurrentProgress() }}</text>
						</view>
					</view>
					
					<button class="upgrade-confirm-btn" @click="confirmUpgrade">立即升级</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { getLocalUserInfo } from '@/utils/userProfile.js';
import { smartNavigate } from '@/utils/navigation.js';

// 响应式数据
const partnerInfo = reactive({
	level: 2, // 默认等级
	users: 28,
	orderAmount: 4200
});
const progressWidth = ref(65); // 升级进度百分比
const nextLevelRequirement = ref('2名用户或800元订单');
const openedFaq = ref(-1); // 当前打开的FAQ项，-1表示都关闭
const showModal = ref(false); // 升级弹窗显示状态

// 计算属性
const canUpgrade = computed(() => {
	return partnerInfo.level < 4; // 钻石合伙人是最高级别
});

// 获取合伙人信息
const getPartnerInfo = () => {
	// 模拟数据，实际应从API获取
	setTimeout(() => {
		// 模拟请求返回数据
		partnerInfo.level = 2;
		partnerInfo.users = 28;
		partnerInfo.orderAmount = 4200;
		
		// 计算升级进度
		calculateProgress();
	}, 500);
};

// 计算升级进度
const calculateProgress = () => {
	if (partnerInfo.level === 1) {
		// 普通合伙人升级到银牌：需要30名用户或5000元订单
		const userProgress = (partnerInfo.users / 30) * 100;
		const amountProgress = (partnerInfo.orderAmount / 5000) * 100;
		progressWidth.value = Math.max(userProgress, amountProgress);
		
		const remainingUsers = Math.max(0, 30 - partnerInfo.users);
		const remainingAmount = Math.max(0, 5000 - partnerInfo.orderAmount);
		nextLevelRequirement.value = `${remainingUsers}名用户或${remainingAmount}元订单`;
	} else if (partnerInfo.level === 2) {
		// 银牌合伙人升级到金牌：需要100名用户或20000元订单
		const userProgress = (partnerInfo.users / 100) * 100;
		const amountProgress = (partnerInfo.orderAmount / 20000) * 100;
		progressWidth.value = Math.max(userProgress, amountProgress);
		
		const remainingUsers = Math.max(0, 100 - partnerInfo.users);
		const remainingAmount = Math.max(0, 20000 - partnerInfo.orderAmount);
		nextLevelRequirement.value = `${remainingUsers}名用户或${remainingAmount}元订单`;
	} else if (partnerInfo.level === 3) {
		// 金牌合伙人升级到钻石：需要300名用户或50000元订单
		const userProgress = (partnerInfo.users / 300) * 100;
		const amountProgress = (partnerInfo.orderAmount / 50000) * 100;
		progressWidth.value = Math.max(userProgress, amountProgress);
		
		const remainingUsers = Math.max(0, 300 - partnerInfo.users);
		const remainingAmount = Math.max(0, 50000 - partnerInfo.orderAmount);
		nextLevelRequirement.value = `${remainingUsers}名用户或${remainingAmount}元订单`;
	} else {
		// 钻石合伙人是最高级别
		progressWidth.value = 100;
		nextLevelRequirement.value = '已达最高等级';
	}
	
	// 限制进度条最大值为100%
	progressWidth.value = Math.min(progressWidth.value, 100);
};

// 获取等级图标
const getLevelIcon = () => {
	const icons = [
		'/static/images/tabbar/partner-level-1.png',
		'/static/images/tabbar/partner-level-2.png',
		'/static/images/tabbar/partner-level-3.png',
		'/static/images/tabbar/partner-level-4.png'
	];
	return icons[partnerInfo.level - 1] || icons[0];
};

// 获取等级名称
const getLevelName = () => {
	const names = [
		'普通合伙人',
		'银牌合伙人',
		'金牌合伙人',
		'钻石合伙人'
	];
	return names[partnerInfo.level - 1] || '未知等级';
};

// 获取下一等级名称
const getNextLevelName = () => {
	const names = [
		'普通合伙人',
		'银牌合伙人',
		'金牌合伙人',
		'钻石合伙人'
	];
	if (partnerInfo.level < 4) {
		return names[partnerInfo.level];
	}
	return '最高等级';
};

// 获取等级描述
const getLevelDesc = () => {
	const descs = [
		'一级佣金5%，二级佣金2%',
		'一级佣金8%，二级佣金3%',
		'一级佣金12%，二级佣金5%',
		'一级佣金15%，二级佣金8%'
	];
	return descs[partnerInfo.level - 1] || '';
};

// 切换FAQ展开状态
const toggleFaq = (index) => {
	if (openedFaq.value === index) {
		openedFaq.value = -1; // 关闭当前展开的FAQ
	} else {
		openedFaq.value = index; // 展开所点击的FAQ
	}
};

// 显示升级弹窗
const showUpgradeModal = () => {
	uni.navigateTo({
		url: '/subPackages/partner/pages/partner-upgrade'
	});
};

// 隐藏升级弹窗
const hideUpgradeModal = () => {
	showModal.value = false;
};

// 获取升级条件
const getUpgradeRequirements = () => {
	if (partnerInfo.level === 1) {
		return '30名有效用户或5000元订单';
	} else if (partnerInfo.level === 2) {
		return '100名有效用户或20000元订单';
	} else if (partnerInfo.level === 3) {
		return '300名有效用户或50000元订单';
	}
	return '已达最高等级';
};

// 获取当前进度
const getCurrentProgress = () => {
	return `${partnerInfo.users}名用户，${partnerInfo.orderAmount}元订单`;
};

// 确认升级
const confirmUpgrade = () => {
	// 跳转到升级页面
	uni.navigateTo({
		url: '/subPackages/partner/pages/partner-upgrade'
	});
};

// 返回上一页
const goBack = () => {
	uni.navigateBack({
		fail: () => {
			// 如果返回失败（无页面可返回），则跳转到合伙人首页
			uni.navigateTo({
				url: '/subPackages/partner/pages/partner'
			});
		}
	});
};

// 生命周期钩子
onMounted(() => {
	// 获取合伙人信息
	getPartnerInfo();
});
</script>

<style lang="scss" scoped>
.levels-container {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 88rpx;
	padding: 0 30rpx;
	padding-top: 44px; /* 状态栏高度 */
	position: fixed; /* 改为固定定位 */
	top: 0;
	left: 0;
	right: 0;
	background-image: linear-gradient(135deg, #0066FF, #0052CC);
	box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
	z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-title {
	position: absolute;
	left: 0;
	right: 0;
	color: #ffffff;
	font-size: 36rpx;
	font-weight: 700;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	text-align: center;
}

.navbar-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 20; /* 确保在标题上层，可以被点击 */
}

.back-icon {
	width: 100%;
	height: 100%;
}

.safe-area-top {
	height: 180rpx;
	width: 100%;
}

/* 当前等级卡片 */
.current-level-card {
	margin: 30rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.level-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.level-info {
	display: flex;
	align-items: center;
}

.level-badge {
	width: 80rpx;
	height: 80rpx;
	margin-right: 20rpx;
}

.level-detail {
	display: flex;
	flex-direction: column;
}

.level-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 6rpx;
}

.level-desc {
	font-size: 24rpx;
	color: #666666;
}

.upgrade-btn {
	background: linear-gradient(135deg, #0066FF, #0052CC);
	padding: 12rpx 30rpx;
	border-radius: 30rpx;
	color: #ffffff;
	font-size: 26rpx;
	font-weight: 500;
	box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.level-progress {
	margin-top: 20rpx;
}

.progress-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.progress-text {
	font-size: 26rpx;
	color: #666666;
}

.progress-value {
	font-size: 26rpx;
	color: #FF6B00;
	font-weight: 500;
}

.progress-track {
	height: 16rpx;
	background-color: #f0f0f0;
	border-radius: 8rpx;
	overflow: hidden;
}

.progress-bar {
	height: 100%;
	background: linear-gradient(to right, #0066FF, #36CBCB);
	border-radius: 8rpx;
	transition: width 0.5s ease;
}

/* 等级特权卡片 */
.privileges-card {
	margin: 30rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 30rpx;
	position: relative;
	
	&::after {
		content: '';
		position: absolute;
		bottom: -10rpx;
		left: 0;
		width: 60rpx;
		height: 4rpx;
		background: linear-gradient(to right, #0066FF, #36CBCB);
		border-radius: 2rpx;
	}
}

.privileges-table {
	width: 100%;
	border-radius: 12rpx;
	overflow: hidden;
	border: 1px solid #e0e0e0;
}

.table-header {
	display: flex;
	background-color: #f5f7fa;
}

.header-cell {
	flex: 1;
	padding: 20rpx 10rpx;
	text-align: center;
	font-size: 26rpx;
	font-weight: 600;
	color: #333333;
	border-right: 1px solid #e0e0e0;
	
	&:last-child {
		border-right: none;
	}
}

.level-cell {
	flex: 1.5;
}

.table-row {
	display: flex;
	border-top: 1px solid #e0e0e0;
	
	&.active {
		background-color: rgba(0, 102, 255, 0.05);
	}
}

.table-cell {
	flex: 1;
	padding: 20rpx 10rpx;
	text-align: center;
	font-size: 26rpx;
	color: #666666;
	border-right: 1px solid #e0e0e0;
	
	&:last-child {
		border-right: none;
	}
}

.level-tag {
	display: flex;
	align-items: center;
	justify-content: center;
	
	.mini-badge {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	
	text {
		font-size: 24rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}

/* 升级规则卡片 */
.rules-card {
	margin: 30rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.rule-item {
	margin-bottom: 20rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.rule-title {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.rule-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background-color: #0066FF;
	margin-right: 10rpx;
}

.rule-title text {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.rule-content {
	padding-left: 22rpx;
}

.rule-content text {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.6;
}

/* FAQ卡片 */
.faq-card {
	margin: 30rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.faq-item {
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
	
	&:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
	}
}

.faq-question {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10rpx 0;
}

.faq-question text {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.arrow-icon {
	width: 32rpx;
	height: 32rpx;
	transition: transform 0.3s ease;
	
	&.rotated {
		transform: rotate(180deg);
	}
}

.faq-answer {
	margin-top: 15rpx;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
}

.faq-answer text {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.6;
}

/* 升级弹窗 */
.upgrade-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

.modal-content {
	width: 80%;
	background-color: #ffffff;
	border-radius: 16rpx;
	overflow: hidden;
}

.modal-header {
	padding: 30rpx;
	background: linear-gradient(135deg, #0066FF, #0052CC);
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.modal-title {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
}

.close-btn {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #ffffff;
	font-size: 40rpx;
	font-weight: 300;
}

.modal-body {
	padding: 30rpx;
}

.upgrade-info {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}
</style>