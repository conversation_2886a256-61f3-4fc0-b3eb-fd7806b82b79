"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const DistributionSection = () => "../../../../components/distribution-section.js";
const _sfc_main = {
  components: {
    DistributionSection
  },
  data() {
    return {
      id: null,
      statusBarHeight: 20,
      navbarHeight: 82,
      loading: true,
      product: {},
      countdown: {
        hours: "00",
        minutes: "00",
        seconds: "00"
      },
      progressWidth: "0%",
      isFavorite: false,
      timer: null
      // 用于存储定时器ID
    };
  },
  computed: {
    discountPercent() {
      if (!this.product.flashPrice || !this.product.originalPrice)
        return "";
      const discount = parseFloat(this.product.flashPrice) / parseFloat(this.product.originalPrice) * 10;
      return discount.toFixed(1);
    }
  },
  onLoad(options) {
    if (options && options.id) {
      this.id = options.id;
    }
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 62;
    setTimeout(() => {
      this.loadProductDetail();
    }, 500);
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载秒杀商品详情
    loadProductDetail() {
      this.loading = true;
      setTimeout(() => {
        this.product = {
          id: this.id || 1,
          title: "iPhone 13 Pro Max限时秒杀",
          images: [
            "/static/demo/product1.jpg",
            "/static/demo/product2.jpg",
            "/static/demo/product3.jpg"
          ],
          detailImages: [
            "/static/demo/detail1.jpg",
            "/static/demo/detail2.jpg"
          ],
          flashPrice: "7999",
          originalPrice: "8999",
          soldCount: 156,
          totalCount: 200,
          startTime: /* @__PURE__ */ new Date(),
          endTime: new Date(Date.now() + 24 * 60 * 60 * 1e3),
          description: "苹果最新旗舰手机限时特惠，A15仿生芯片，超视网膜XDR显示屏，专业级摄像系统，超瓷晶面板，IP68级防水，5G网络。",
          shopName: "官方旗舰店",
          shopLogo: "/static/demo/shop-logo.png",
          shopRating: 4.9,
          stock: 44,
          commissionRate: 15
        };
        this.loading = false;
        this.startCountdown();
        this.calculateProgress();
      }, 1e3);
    },
    // 开始倒计时
    startCountdown() {
      const endTime = this.product.endTime;
      const updateCountdown = () => {
        const now = /* @__PURE__ */ new Date();
        const diff = endTime - now;
        if (diff <= 0) {
          this.countdown = {
            hours: "00",
            minutes: "00",
            seconds: "00"
          };
          clearInterval(this.timer);
          return;
        }
        const hours = Math.floor(diff / (1e3 * 60 * 60));
        const minutes = Math.floor(diff % (1e3 * 60 * 60) / (1e3 * 60));
        const seconds = Math.floor(diff % (1e3 * 60) / 1e3);
        this.countdown = {
          hours: hours.toString().padStart(2, "0"),
          minutes: minutes.toString().padStart(2, "0"),
          seconds: seconds.toString().padStart(2, "0")
        };
      };
      updateCountdown();
      this.timer = setInterval(updateCountdown, 1e3);
    },
    // 计算进度条宽度
    calculateProgress() {
      const soldPercent = this.product.soldCount / this.product.totalCount * 100;
      this.progressWidth = soldPercent + "%";
    },
    // 跳转到店铺
    goToShop() {
      common_vendor.index.navigateTo({
        url: `/pages/shop/detail?id=${this.product.shopId || 1}`
      });
    },
    // 联系客服
    contactService() {
      common_vendor.index.showToast({
        title: "正在连接客服...",
        icon: "none"
      });
    },
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      common_vendor.index.showToast({
        title: this.isFavorite ? "收藏成功" : "已取消收藏",
        icon: "success"
      });
    },
    // 立即购买
    buyNow() {
      common_vendor.index.showLoading({
        title: "正在下单"
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.navigateTo({
          url: `/pages/pay/index?amount=${this.product.flashPrice}&title=${encodeURIComponent(this.product.title)}`
        });
      }, 800);
    },
    // 分享
    share() {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    }
  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
};
if (!Array) {
  const _component_distribution_section = common_vendor.resolveComponent("distribution-section");
  _component_distribution_section();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: $data.loading
  }, $data.loading ? {} : {
    d: common_vendor.f($data.product.images, (item, index, i0) => {
      return {
        a: item,
        b: index
      };
    }),
    e: $data.navbarHeight + "px",
    f: common_vendor.t($data.countdown.hours),
    g: common_vendor.t($data.countdown.minutes),
    h: common_vendor.t($data.countdown.seconds),
    i: common_vendor.t($data.product.flashPrice),
    j: common_vendor.t($options.discountPercent),
    k: common_vendor.t($data.product.originalPrice),
    l: common_vendor.t($data.product.title),
    m: common_vendor.t($data.product.soldCount),
    n: common_vendor.t($data.product.stock),
    o: $data.progressWidth,
    p: common_vendor.t($data.product.soldCount),
    q: common_vendor.t($data.product.totalCount),
    r: common_vendor.p({
      itemId: $data.id,
      itemType: "flash",
      itemTitle: $data.product.title,
      itemPrice: $data.product.flashPrice,
      commissionRate: $data.product.commissionRate || 15
    }),
    s: $data.product.shopLogo,
    t: common_vendor.t($data.product.shopName),
    v: common_vendor.f(5, (i, k0, i0) => {
      return {
        a: i,
        b: i <= Math.floor($data.product.shopRating) ? "/static/images/tabbar/星星-选中.png" : "/static/images/tabbar/星星-未选.png"
      };
    }),
    w: common_vendor.t($data.product.shopRating),
    x: common_vendor.o((...args) => $options.goToShop && $options.goToShop(...args)),
    y: common_vendor.t($data.product.description),
    z: common_vendor.f($data.product.detailImages, (img, index, i0) => {
      return {
        a: index,
        b: img
      };
    }),
    A: $data.isFavorite ? "/static/images/tabbar/收藏-选中.png" : "/static/images/tabbar/收藏.png",
    B: common_vendor.o((...args) => $options.toggleFavorite && $options.toggleFavorite(...args)),
    C: common_assets._imports_1$55,
    D: common_vendor.o((...args) => $options.contactService && $options.contactService(...args)),
    E: common_assets._imports_2$1,
    F: common_vendor.o((...args) => $options.share && $options.share(...args)),
    G: common_vendor.t($data.product.flashPrice),
    H: common_vendor.o((...args) => $options.buyNow && $options.buyNow(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c0c46a90"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/flash-sale/detail.js.map
