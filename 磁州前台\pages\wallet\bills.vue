<template>
  <view class="bills-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">收支记录</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 筛选区域 -->
    <view class="filter-section" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <view class="filter-tabs">
        <view 
          class="filter-tab" 
          v-for="(tab, index) in tabs" 
          :key="index"
          :class="{'active': activeTab === index}"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </view>
      </view>
      
      <view class="filter-date">
        <picker 
          mode="date" 
          fields="month" 
          :value="selectedDate" 
          @change="onDateChange"
        >
          <view class="date-picker">
            <text>{{ formatDate(selectedDate) }}</text>
            <image src="/static/images/tabbar/下拉.png" class="date-icon"></image>
          </view>
        </picker>
      </view>
    </view>
    
    <!-- 收支统计 -->
    <view class="statistics-section">
      <view class="statistics-item">
        <view class="statistics-label">收入</view>
        <view class="statistics-value income">+{{ statistics.income.toFixed(2) }}</view>
      </view>
      <view class="statistics-divider"></view>
      <view class="statistics-item">
        <view class="statistics-label">支出</view>
        <view class="statistics-value expense">-{{ statistics.expense.toFixed(2) }}</view>
      </view>
    </view>
    
    <!-- 交易记录列表 -->
    <view class="transaction-list">
      <view v-if="filteredTransactions.length > 0">
        <!-- 按日期分组的交易记录 -->
        <block v-for="(group, date) in groupedTransactions" :key="date">
          <view class="date-header">
            <view class="date-text">{{ formatDayDate(date) }}</view>
            <view class="date-summary">
              <text class="date-income" v-if="getDateIncome(group) > 0">收入: {{ getDateIncome(group).toFixed(2) }}</text>
              <text class="date-expense" v-if="getDateExpense(group) > 0">支出: {{ getDateExpense(group).toFixed(2) }}</text>
            </view>
          </view>
          
          <view class="transaction-item" v-for="(item, index) in group" :key="index">
            <view class="transaction-left">
              <view class="transaction-icon" :class="getTransactionTypeClass(item.type)">
                <image :src="getTransactionTypeIcon(item.type)" class="type-icon"></image>
              </view>
            </view>
            <view class="transaction-center">
              <view class="transaction-title">{{ item.title }}</view>
              <view class="transaction-time">{{ item.time }}</view>
            </view>
            <view class="transaction-right">
              <view class="transaction-amount" :class="{'income': item.type === 'income', 'expense': item.type === 'expense'}">
                {{ item.type === 'income' ? '+' : '-' }}{{ item.amount.toFixed(2) }}
              </view>
              <view class="transaction-status">{{ item.status }}</view>
            </view>
          </view>
        </block>
        
        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMoreData" @click="loadMoreData">
          <text>加载更多</text>
        </view>
        <view class="no-more" v-else>
          <text>没有更多数据了</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-view">
        <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
        <view class="empty-text">暂无交易记录</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      activeTab: 0,
      tabs: [
        { name: '全部', type: 'all' },
        { name: '收入', type: 'income' },
        { name: '支出', type: 'expense' }
      ],
      selectedDate: new Date().toISOString().split('T')[0].substr(0, 7),
      statistics: {
        income: 0,
        expense: 0
      },
      transactions: [
        {
          id: 'tx001',
          title: '充值',
          date: '2023-11-05',
          time: '14:30',
          fullTime: '2023-11-05 14:30',
          amount: 100.00,
          type: 'income',
          status: '已完成'
        },
        {
          id: 'tx002',
          title: '服务支付',
          date: '2023-11-03',
          time: '09:15',
          fullTime: '2023-11-03 09:15',
          amount: 35.00,
          type: 'expense',
          status: '已完成'
        },
        {
          id: 'tx003',
          title: '提现',
          date: '2023-11-03',
          time: '16:22',
          fullTime: '2023-11-03 16:22',
          amount: 50.00,
          type: 'expense',
          status: '已完成'
        },
        {
          id: 'tx004',
          title: '充值',
          date: '2023-11-01',
          time: '11:05',
          fullTime: '2023-11-01 11:05',
          amount: 100.00,
          type: 'income',
          status: '已完成'
        },
        {
          id: 'tx005',
          title: '任务收入',
          date: '2023-10-30',
          time: '18:45',
          fullTime: '2023-10-30 18:45',
          amount: 88.00,
          type: 'income',
          status: '已完成'
        }
      ],
      page: 1,
      pageSize: 10,
      hasMoreData: true
    }
  },
  computed: {
    // 根据当前选中的标签和日期筛选交易记录
    filteredTransactions() {
      const yearMonth = this.selectedDate;
      let result = this.transactions.filter(item => item.date.startsWith(yearMonth));
      
      if (this.activeTab !== 0) {
        const type = this.tabs[this.activeTab].type;
        result = result.filter(item => item.type === type);
      }
      
      return result;
    },
    
    // 按日期分组交易记录
    groupedTransactions() {
      const groups = {};
      
      this.filteredTransactions.forEach(item => {
        if (!groups[item.date]) {
          groups[item.date] = [];
        }
        groups[item.date].push(item);
      });
      
      // 按日期降序排序
      const sortedGroups = {};
      Object.keys(groups).sort((a, b) => new Date(b) - new Date(a)).forEach(key => {
        sortedGroups[key] = groups[key];
      });
      
      return sortedGroups;
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 获取交易记录
    this.getTransactions();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 切换标签
    switchTab(index) {
      this.activeTab = index;
      this.calculateStatistics();
    },
    
    // 日期选择变化
    onDateChange(e) {
      this.selectedDate = e.detail.value;
      this.page = 1;
      this.getTransactions();
    },
    
    // 格式化日期显示
    formatDate(dateStr) {
      if (!dateStr) return '';
      const [year, month] = dateStr.split('-');
      return `${year}年${month}月`;
    },
    
    // 格式化日期显示(日)
    formatDayDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const weekDay = weekDays[date.getDay()];
      return `${month}月${day}日 ${weekDay}`;
    },
    
    // 获取交易记录
    getTransactions() {
      // 模拟API请求获取交易记录
      // 实际应用中应该从服务器获取数据
      setTimeout(() => {
        // 如果是第一页，直接替换数据
        if (this.page === 1) {
          // 数据已经在data中初始化
          this.calculateStatistics();
        } else {
          // 如果是加载更多，追加数据
          if (this.page >= 3) {
            this.hasMoreData = false;
          } else {
            // 模拟加载更多数据
            const moreData = [
              {
                id: 'tx006',
                title: '服务支付',
                date: '2023-10-22',
                time: '10:30',
                fullTime: '2023-10-22 10:30',
                amount: 25.00,
                type: 'expense',
                status: '已完成'
              },
              {
                id: 'tx007',
                title: '充值',
                date: '2023-10-15',
                time: '16:40',
                fullTime: '2023-10-15 16:40',
                amount: 50.00,
                type: 'income',
                status: '已完成'
              }
            ];
            this.transactions = [...this.transactions, ...moreData];
            this.calculateStatistics();
          }
        }
      }, 500);
    },
    
    // 计算统计数据
    calculateStatistics() {
      let income = 0;
      let expense = 0;
      
      this.filteredTransactions.forEach(item => {
        if (item.type === 'income') {
          income += item.amount;
        } else if (item.type === 'expense') {
          expense += item.amount;
        }
      });
      
      this.statistics = { income, expense };
    },
    
    // 获取某天的收入总额
    getDateIncome(transactions) {
      return transactions.reduce((sum, item) => {
        return item.type === 'income' ? sum + item.amount : sum;
      }, 0);
    },
    
    // 获取某天的支出总额
    getDateExpense(transactions) {
      return transactions.reduce((sum, item) => {
        return item.type === 'expense' ? sum + item.amount : sum;
      }, 0);
    },
    
    // 加载更多数据
    loadMoreData() {
      if (!this.hasMoreData) return;
      
      this.page++;
      this.getTransactions();
    },
    
    // 获取交易类型对应的图标
    getTransactionTypeIcon(type) {
      const icons = {
        'income': '/static/images/tabbar/收入.png',
        'expense': '/static/images/tabbar/支出.png'
      };
      return icons[type] || icons['income'];
    },
    
    // 获取交易类型对应的样式类
    getTransactionTypeClass(type) {
      return {
        'income-icon': type === 'income',
        'expense-icon': type === 'expense'
      };
    }
  }
}
</script>

<style>
.bills-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 筛选区域 */
.filter-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
}

.filter-tabs {
  display: flex;
}

.filter-tab {
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  margin-right: 20rpx;
}

.filter-tab.active {
  color: #0052CC;
  font-weight: 500;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #0052CC;
  border-radius: 2rpx;
}

.filter-date {
  font-size: 28rpx;
  color: #333;
}

.date-picker {
  display: flex;
  align-items: center;
}

.date-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}

/* 统计区域 */
.statistics-section {
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 20rpx;
}

.statistics-item {
  text-align: center;
}

.statistics-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.statistics-value {
  font-size: 36rpx;
  font-weight: 500;
}

.income {
  color: #07c160;
}

.expense {
  color: #f56c6c;
}

.statistics-divider {
  width: 1px;
  height: 60rpx;
  background-color: #f5f5f5;
}

/* 交易记录列表 */
.transaction-list {
  padding: 0 30rpx;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.date-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.date-summary {
  font-size: 24rpx;
}

.date-income {
  color: #07c160;
  margin-right: 20rpx;
}

.date-expense {
  color: #f56c6c;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.transaction-left {
  margin-right: 20rpx;
}

.transaction-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.income-icon {
  background-color: rgba(7, 193, 96, 0.1);
}

.expense-icon {
  background-color: rgba(245, 108, 108, 0.1);
}

.type-icon {
  width: 40rpx;
  height: 40rpx;
}

.transaction-center {
  flex: 1;
}

.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.transaction-time {
  font-size: 24rpx;
  color: #999;
}

.transaction-right {
  text-align: right;
}

.transaction-amount {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.transaction-status {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999;
}

.load-more {
  color: #0052CC;
}

/* 空状态 */
.empty-view {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 