
.activity-my-container.data-v-d62e35d3 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-d62e35d3 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.navbar-bg.data-v-d62e35d3 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(255,59,105,0.15);
}
.navbar-content.data-v-d62e35d3 {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
}
.navbar-title.data-v-d62e35d3 {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
}
.back-btn.data-v-d62e35d3, .filter-btn.data-v-d62e35d3 {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}
.back-btn .icon.data-v-d62e35d3, .filter-btn .icon.data-v-d62e35d3 {
      width: 48rpx;
      height: 48rpx;
}

/* 内容区域 */
.content-scroll.data-v-d62e35d3 {
  flex: 1;
  height: calc(100vh - var(--status-bar-height, 25px) - 62px);
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  padding: 30rpx;
  box-sizing: border-box;
}

/* 底部安全区域 */
.safe-area-bottom.data-v-d62e35d3 {
  height: 100rpx; /* 底部安全区域高度，包括导航栏高度 */
  padding-bottom: env(safe-area-inset-bottom);
}

/* 标签页容器 */
.tabs-container.data-v-d62e35d3 {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-radius: 35px 35px 0 0;
}
.tab-item.data-v-d62e35d3 {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding: 10rpx 0;
}
.tab-text.data-v-d62e35d3 {
      font-size: 28rpx;
      color: #8E8E93;
      transition: color 0.3s ease;
}
.tab-indicator.data-v-d62e35d3 {
      position: absolute;
      bottom: -10rpx;
      width: 40rpx;
      height: 3px;
      border-radius: 1.5px;
      transition: all 0.3s ease;
}
.tab-item.active .tab-text.data-v-d62e35d3 {
        color: #FF3B69;
        font-weight: 500;
}

/* 活动容器 */
.activities-container.data-v-d62e35d3 {
  width: 100%;
  position: relative;
  margin-bottom: 30rpx;
}

/* 活动轮播区域 */
.activities-swiper.data-v-d62e35d3 {
  width: 100%;
  margin-bottom: 30rpx;
  height: 600px; /* 默认高度 */
}
.tab-content.data-v-d62e35d3 {
    height: 100%;
    overflow: visible;
    padding-bottom: 20rpx;
}
.activity-list.data-v-d62e35d3 {
      padding: 10rpx 0;
      display: flex;
      flex-direction: column;
      width: 100%;
}

/* 活动状态卡片 */
.activity-status-card.data-v-d62e35d3 {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx;
}
.card-header.data-v-d62e35d3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.card-title.data-v-d62e35d3 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.header-right.data-v-d62e35d3 {
  display: flex;
  align-items: center;
}
.view-all.data-v-d62e35d3 {
  font-size: 28rpx;
  color: #FF3B69;
  margin-right: 5rpx;
}
.icon.data-v-d62e35d3 {
  width: 16rpx;
  height: 16rpx;
}

/* 空状态 */
.empty-state.data-v-d62e35d3 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}
.empty-image.data-v-d62e35d3 {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
}
.empty-text.data-v-d62e35d3 {
    font-size: 28rpx;
    color: #8E8E93;
    margin-bottom: 30rpx;
}
.action-btn.data-v-d62e35d3 {
    padding: 16rpx 40rpx;
    border-radius: 35px;
    color: #FFFFFF;
    font-size: 28rpx;
}
.action-btn.data-v-d62e35d3:active {
      opacity: 0.9;
      transform: scale(0.98);
}

/* 订单状态网格 */
.order-status-grid.data-v-d62e35d3 {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}
.status-item.data-v-d62e35d3 {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
    margin-bottom: 20rpx;
}
.status-icon.data-v-d62e35d3 {
  width: 80rpx;
  height: 80rpx;
      display: flex;
      align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}
.status-name.data-v-d62e35d3 {
  font-size: 24rpx;
        color: #333333;
}
.status-badge.data-v-d62e35d3 {
  position: absolute;
  top: -10rpx;
  right: 10rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
        display: flex;
        justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
}

/* 优惠券列表 */
.coupon-list.data-v-d62e35d3 {
  margin-top: 20rpx;
}

/* 主标签容器 */
.main-tabs-container.data-v-d62e35d3 {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.main-tab-item.data-v-d62e35d3 {
  flex: 1;
        display: flex;
  flex-direction: column;
        align-items: center;
  position: relative;
  padding: 15rpx 0;
}
.main-tab-item .tab-text.data-v-d62e35d3 {
  font-size: 28rpx;
  color: #8E8E93;
  transition: color 0.3s ease;
}
.main-tab-item.active .tab-text.data-v-d62e35d3 {
  color: #FF3B69;
  font-weight: 600;
}

/* 子标签容器 */
.sub-tabs-container.data-v-d62e35d3 {
    display: flex;
  justify-content: flex-start;
    margin-bottom: 20rpx;
  overflow-x: auto;
  white-space: nowrap;
}
.sub-tab-item.data-v-d62e35d3 {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
      position: relative;
  padding: 10rpx 30rpx;
}
.sub-tab-item .tab-text.data-v-d62e35d3 {
  font-size: 26rpx;
  color: #8E8E93;
  transition: color 0.3s ease;
}
.sub-tab-item.active .tab-text.data-v-d62e35d3 {
  color: #FF3B69;
  font-weight: 500;
}

/* 底部导航栏 */
.tabbar.data-v-d62e35d3 {
  position: fixed;
        bottom: 0;
  left: 0;
        right: 0;
  height: 100rpx;
  background: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
        display: flex;
  justify-content: space-around;
        align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 99;
  border-top: 1rpx solid #EEEEEE;
}
.tabbar-item.data-v-d62e35d3 {
  flex: 1;
      display: flex;
      flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}
.tabbar-item.data-v-d62e35d3:active {
  transform: scale(0.9);
}
.tabbar-item.active .tab-icon.data-v-d62e35d3 {
  transform: translateY(-5rpx);
}
.tabbar-item.active .tabbar-text.data-v-d62e35d3 {
  color: #FF3B69;
  font-weight: 600;
  transform: translateY(-2rpx);
}
.tab-icon.data-v-d62e35d3 {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
    display: flex;
  justify-content: center;
      align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 首页图标 */
.tab-icon.home.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="home"] .tab-icon.home.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

/* 发现图标 */
.tab-icon.discover.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="discover"] .tab-icon.discover.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

/* 消息图标 */
.tab-icon.message.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="message"] .tab-icon.message.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 分销图标 */
.tab-icon.distribution.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="distribution"] .tab-icon.distribution.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

/* 我的图标 */
.tab-icon.user.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab="my"] .tab-icon.user.data-v-d62e35d3 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.tabbar-item.active .tab-icon.data-v-d62e35d3 {
  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));
}
.badge.data-v-d62e35d3 {
  position: absolute;
  top: -8rpx;
  right: -12rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #FF453A, #FF2D55);
  color: #FFFFFF;
  font-size: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  font-weight: 600;
  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transform: scale(0.9);
}
.tabbar-text.data-v-d62e35d3 {
  font-size: 22rpx;
      color: #8E8E93;
  margin-top: 2rpx;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.tabbar-item.data-v-d62e35d3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 30rpx;
  height: 4rpx;
  background: #FF3B69;
  border-radius: 2rpx;
  transition: transform 0.3s ease;
}
.tabbar-item.active.data-v-d62e35d3::after {
  transform: translateX(-50%) scaleX(1);
}

/* 卡片通用样式 */
.card-header.data-v-d62e35d3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 工具箱样式 */
.tools-card.data-v-d62e35d3 {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(88,86,214,0.15);
  background: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.toolbox-header.data-v-d62e35d3 {
    margin-bottom: 20rpx;
}
.toolbox-title.data-v-d62e35d3 {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
}
.tool-grid.data-v-d62e35d3 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 20rpx;
  margin-top: 20rpx;
}
.tool-item.data-v-d62e35d3 {
      display: flex;
      flex-direction: column;
      align-items: center;
  margin-bottom: 10rpx;
}
.tool-icon.data-v-d62e35d3 {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10rpx;
  border-radius: 50%;
}
.tool-name.data-v-d62e35d3 {
        font-size: 24rpx;
        color: #333333;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 优惠券专区样式 */
.coupon-card.data-v-d62e35d3 {
  border-radius: 35px;
  box-shadow: 0 10px 25px rgba(255,149,0,0.12);
  background: linear-gradient(to bottom, #FFFFFF, #FFFAF2);
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid rgba(255,149,0,0.1);
}
.coupon-status-tabs.data-v-d62e35d3 {
    display: flex;
  justify-content: space-around;
    margin-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(242,242,247,0.8);
  padding-bottom: 5rpx;
}
.coupon-status-tab.data-v-d62e35d3 {
  flex: 1;
      display: flex;
  flex-direction: column;
      align-items: center;
  position: relative;
  padding: 15rpx 0;
}
.coupon-status-tab text.data-v-d62e35d3 {
        font-size: 28rpx;
  color: #8E8E93;
  transition: color 0.3s ease;
}
.coupon-status-tab.active text.data-v-d62e35d3 {
  color: #FF9500;
  font-weight: 600;
}
.tab-indicator.data-v-d62e35d3 {
  position: absolute;
  bottom: -5rpx;
  width: 40rpx;
  height: 4rpx;
  border-radius: 2rpx;
}
.coupons-list.data-v-d62e35d3 {
    margin-top: 20rpx;
}
.coupon-item.data-v-d62e35d3 {
  position: relative;
  display: flex;
  margin-bottom: 25rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}
.coupon-item.data-v-d62e35d3:active {
  transform: scale(0.98);
}
.coupon-left.data-v-d62e35d3 {
  width: 200rpx;
  padding: 25rpx 20rpx;
      display: flex;
      flex-direction: column;
  justify-content: center;
      align-items: center;
      position: relative;
  overflow: hidden;
}
.coupon-left.data-v-d62e35d3::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
  z-index: 1;
}
.coupon-value.data-v-d62e35d3 {
        display: flex;
  align-items: baseline;
  position: relative;
  z-index: 2;
}
.currency.data-v-d62e35d3 {
  font-size: 26rpx;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}
.amount.data-v-d62e35d3 {
  font-size: 52rpx;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}
.coupon-condition.data-v-d62e35d3 {
  font-size: 22rpx;
  color: #FFFFFF;
  margin-top: 10rpx;
  position: relative;
  z-index: 2;
  text-shadow: 0 1rpx 3rpx rgba(0,0,0,0.2);
  background-color: rgba(0,0,0,0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
}
.coupon-divider.data-v-d62e35d3 {
        position: absolute;
  left: 200rpx;
  top: 0;
  bottom: 0;
  width: 0;
  border-left: 1px dashed rgba(255,255,255,0.5);
  z-index: 2;
}
.coupon-divider.data-v-d62e35d3::before, .coupon-divider.data-v-d62e35d3::after {
  content: '';
  position: absolute;
  left: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #F8F8F8;
  box-shadow: inset 0 0 5rpx rgba(0,0,0,0.05);
  z-index: 2;
}
.coupon-divider.data-v-d62e35d3::before {
  top: -10rpx;
}
.coupon-divider.data-v-d62e35d3::after {
  bottom: -10rpx;
}
.coupon-right.data-v-d62e35d3 {
  flex: 1;
  padding: 25rpx;
  display: flex;
  flex-direction: column;
    justify-content: space-between;
  background-color: #FFFFFF;
}
.coupon-name.data-v-d62e35d3 {
  font-size: 28rpx;
      font-weight: 600;
      color: #333333;
  margin-bottom: 10rpx;
}
.coupon-shop.data-v-d62e35d3 {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 15rpx;
}
.coupon-validity.data-v-d62e35d3 {
  font-size: 22rpx;
  color: #999999;
}
.coupon-use-btn.data-v-d62e35d3 {
  align-self: flex-end;
      display: flex;
  justify-content: center;
      align-items: center;
  height: 60rpx;
  width: 160rpx;
  border-radius: 30rpx;
  background: rgba(255,149,0,0.9);
  font-size: 26rpx;
  font-weight: 500;
  color: #FFFFFF;
  border: 1rpx solid rgba(255,149,0,0.3);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}
.coupon-use-btn.data-v-d62e35d3:active {
  transform: scale(0.95);
  background: rgba(255,149,0,1);
}

/* 活动日历样式 */
.calendar-card.data-v-d62e35d3 {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(52,199,89,0.15);
  background: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.calendar-grid.data-v-d62e35d3 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 20rpx;
}
.calendar-item.data-v-d62e35d3 {
  width: 31%;
  padding: 15rpx;
  border-radius: 16rpx;
  margin-bottom: 15rpx;
  box-sizing: border-box;
}
.calendar-date.data-v-d62e35d3 {
    display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10rpx;
}
.date-day.data-v-d62e35d3 {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}
.date-month.data-v-d62e35d3 {
  font-size: 20rpx;
  color: #666666;
}
.calendar-event.data-v-d62e35d3 {
      display: flex;
      flex-direction: column;
      align-items: center;
}
.event-title.data-v-d62e35d3 {
  font-size: 22rpx;
  font-weight: 500;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
}
.event-type.data-v-d62e35d3 {
  font-size: 20rpx;
  color: #666666;
  margin-top: 5rpx;
}

/* 分销中心样式 */
.distribution-card.data-v-d62e35d3 {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(172,57,255,0.15);
  background: linear-gradient(135deg, #FFFFFF 0%, #F9F5FF 100%);
  padding: 30rpx;
  margin-bottom: 30rpx;
  transform: perspective(1000px) rotateX(-2deg);
}
.header-left.data-v-d62e35d3 {
  display: flex;
  align-items: center;
}
.distributor-level.data-v-d62e35d3 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40rpx;
  padding: 0 15rpx;
  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
  border-radius: 20rpx;
  margin-left: 15rpx;
}
.level-text.data-v-d62e35d3 {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 500;
}
.view-more-btn.data-v-d62e35d3 {
    display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
}
.view-more-btn text.data-v-d62e35d3 {
  font-size: 24rpx;
  color: #FFFFFF;
  margin-right: 5rpx;
}
.distributor-info.data-v-d62e35d3 {
      display: flex;
  align-items: center;
  margin: 20rpx 0;
}
.distributor-avatar.data-v-d62e35d3 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
      overflow: hidden;
      position: relative;
  margin-right: 20rpx;
}
.distributor-avatar image.data-v-d62e35d3 {
  width: 100%;
  height: 100%;
}
.distributor-badge.data-v-d62e35d3 {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #FFFFFF;
}
.distributor-details.data-v-d62e35d3 {
        display: flex;
        flex-direction: column;
}
.distributor-name.data-v-d62e35d3 {
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 5rpx;
}
.distributor-id.data-v-d62e35d3 {
          font-size: 24rpx;
  color: #666666;
}
.earnings-overview.data-v-d62e35d3 {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}
.earnings-item.data-v-d62e35d3 {
  display: flex;
  flex-direction: column;
    align-items: center;
}
.earnings-value.data-v-d62e35d3 {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
  margin-bottom: 5rpx;
}
.earnings-label.data-v-d62e35d3 {
  font-size: 24rpx;
  color: #666666;
}
.progress-section.data-v-d62e35d3 {
  margin-top: 20rpx;
}
.progress-header.data-v-d62e35d3 {
      display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.progress-title.data-v-d62e35d3 {
  font-size: 26rpx;
  color: #333333;
}
.progress-value.data-v-d62e35d3 {
  font-size: 26rpx;
  font-weight: 600;
  color: #AC39FF;
}
.progress-bar-bg.data-v-d62e35d3 {
  width: 100%;
  height: 10rpx;
  background: rgba(172,57,255,0.2);
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}
.progress-bar-fill.data-v-d62e35d3 {
  height: 100%;
  background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);
  border-radius: 10rpx;
}
.progress-hint.data-v-d62e35d3 {
  font-size: 22rpx;
  color: #666666;
}

/* 用户信息模块 */
.user-profile-card.data-v-d62e35d3 {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(0,0,0,0.08);
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  padding: 30rpx;
  margin-bottom: 30rpx;
      position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.profile-bg-decoration.data-v-d62e35d3 {
        position: absolute;
  top: -50rpx;
  right: -50rpx;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.1);
  z-index: 1;
}
.profile-bg-decoration.data-v-d62e35d3:last-child {
  position: absolute;
  bottom: -80rpx;
  left: -80rpx;
  width: 250rpx;
  height: 250rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.08);
  z-index: 1;
}

/* 用户基本信息 */
.user-basic-info.data-v-d62e35d3 {
    display: flex;
  align-items: center;
      position: relative;
  z-index: 2;
}
.avatar-container.data-v-d62e35d3 {
        position: relative;
  margin-right: 20rpx;
}
.user-avatar.data-v-d62e35d3 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255,255,255,0.8);
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
}
.vip-badge.data-v-d62e35d3 {
          position: absolute;
          bottom: 0;
  right: 0;
  background: #FFD700;
  color: #8B4513;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  transform: translateY(50%);
  box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.2);
}
.user-info.data-v-d62e35d3 {
  flex: 1;
}
.user-name-row.data-v-d62e35d3 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.user-name.data-v-d62e35d3 {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-right: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}
.level-tag.data-v-d62e35d3 {
  font-size: 22rpx;
  color: #FF3B69;
  background: rgba(255,255,255,0.9);
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  font-weight: 500;
}
.user-id.data-v-d62e35d3 {
          font-size: 24rpx;
  color: rgba(255,255,255,0.8);
  margin-bottom: 10rpx;
  display: block;
}
.user-points.data-v-d62e35d3 {
        display: flex;
  align-items: center;
}
.points-label.data-v-d62e35d3 {
          font-size: 24rpx;
  color: rgba(255,255,255,0.9);
}
.points-value.data-v-d62e35d3 {
  font-size: 28rpx;
  font-weight: 600;
          color: #FFFFFF;
  margin-left: 8rpx;
}
.profile-action.data-v-d62e35d3 {
  padding: 10rpx 30rpx;
  background: rgba(255,255,255,0.2);
          border-radius: 30rpx;
  font-size: 26rpx;
  color: #FFFFFF;
  font-weight: 500;
  border: 1rpx solid rgba(255,255,255,0.3);
}

/* 用户数据统计 */
.user-stats.data-v-d62e35d3 {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  position: relative;
  z-index: 2;
  background: rgba(255,255,255,0.1);
  border-radius: 20rpx;
  padding: 20rpx 0;
}
.stat-item.data-v-d62e35d3 {
  text-align: center;
}
.stat-value.data-v-d62e35d3 {
  font-size: 32rpx;
  font-weight: bold;
    color: #FFFFFF;
  display: block;
}
.stat-label.data-v-d62e35d3 {
  font-size: 24rpx;
  color: rgba(255,255,255,0.8);
  display: block;
}

/* 推荐活动模块样式 */
.recommended-card.data-v-d62e35d3 {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(90,200,250,0.15);
  background: linear-gradient(to bottom, #FFFFFF, #F0F8FF);
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid rgba(90,200,250,0.1);
}
.recommendation-info.data-v-d62e35d3 {
  margin-bottom: 20rpx;
}
.recommendation-subtitle.data-v-d62e35d3 {
  font-size: 26rpx;
  color: #666666;
}
.recommendation-scroll.data-v-d62e35d3 {
  width: 100%;
  white-space: nowrap;
}
.recommendation-list.data-v-d62e35d3 {
  display: flex;
  padding: 10rpx 0;
}
.recommendation-item.data-v-d62e35d3 {
  display: inline-flex;
  flex-direction: column;
  width: 300rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}
.recommendation-item.data-v-d62e35d3:active {
  transform: scale(0.98);
}
.recommendation-image.data-v-d62e35d3 {
  width: 100%;
  height: 180rpx;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}
.recommendation-content.data-v-d62e35d3 {
  padding: 15rpx;
  position: relative;
}
.recommendation-tag.data-v-d62e35d3 {
  position: absolute;
  top: -15rpx;
  left: 15rpx;
  padding: 5rpx 15rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 500;
  box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);
}
.recommendation-title.data-v-d62e35d3 {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
  white-space: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 72rpx;
}
.recommendation-shop.data-v-d62e35d3 {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.recommendation-price-row.data-v-d62e35d3 {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}
.current-price.data-v-d62e35d3 {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF3B69;
  margin-right: 10rpx;
}
.original-price.data-v-d62e35d3 {
  font-size: 22rpx;
  color: #999999;
  text-decoration: line-through;
}
.recommendation-stats.data-v-d62e35d3 {
  display: flex;
  justify-content: space-between;
  font-size: 20rpx;
  color: #666666;
}
.match-rate.data-v-d62e35d3 {
  color: #5AC8FA;
  font-weight: 500;
}
.participation-count.data-v-d62e35d3 {
  color: #999999;
}
