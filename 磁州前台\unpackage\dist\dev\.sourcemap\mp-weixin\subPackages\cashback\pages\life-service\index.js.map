{"version": 3, "file": "index.js", "sources": ["subPackages/cashback/pages/life-service/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2FzaGJhY2tccGFnZXNcbGlmZS1zZXJ2aWNlXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"life-service-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <custom-navbar :title=\"pageTitle\" :show-back=\"true\"></custom-navbar>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content-container\">\r\n      <!-- 外卖红包 -->\r\n      <block v-if=\"serviceType === 'takeout'\">\r\n        <view class=\"service-header\">\r\n          <view class=\"service-icon-container\" style=\"background-color: #FFE8E0;\">\r\n            <svg class=\"service-icon\" viewBox=\"0 0 24 24\" width=\"32\" height=\"32\">\r\n              <path fill=\"#FF6B6B\" d=\"M15.5,21L14,8H16.23L15.1,3.46L16.84,3L18.09,8H22L20.5,21H15.5M5,11H10A3,3 0 0,1 13,14H2A3,3 0 0,1 5,11M13,18A3,3 0 0,1 10,21H5A3,3 0 0,1 2,18H13M3,15H8L9.5,16.5L11,15H12A1,1 0 0,1 13,16A1,1 0 0,1 12,17H3A1,1 0 0,1 2,16A1,1 0 0,1 3,15Z\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"service-info\">\r\n            <text class=\"service-title\">外卖红包</text>\r\n            <text class=\"service-desc\">最高返2%-5%</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"platform-list\">\r\n          <view class=\"platform-item\" @tap=\"goToPlatformService('meituan')\">\r\n            <image class=\"platform-logo\" src=\"/static/images/cashback/platform-meituan.png\" mode=\"aspectFit\"></image>\r\n            <view class=\"platform-info\">\r\n              <text class=\"platform-name\">美团外卖</text>\r\n              <text class=\"platform-desc\">最高返5%</text>\r\n            </view>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path fill=\"#CCCCCC\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n            </svg>\r\n          </view>\r\n          \r\n          <view class=\"platform-item\" @tap=\"goToPlatformService('eleme')\">\r\n            <image class=\"platform-logo\" src=\"/static/images/cashback/platform-eleme.png\" mode=\"aspectFit\"></image>\r\n            <view class=\"platform-info\">\r\n              <text class=\"platform-name\">饿了么</text>\r\n              <text class=\"platform-desc\">最高返3%</text>\r\n            </view>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path fill=\"#CCCCCC\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      \r\n      <!-- 打车红包 -->\r\n      <block v-if=\"serviceType === 'taxi'\">\r\n        <view class=\"service-header\">\r\n          <view class=\"service-icon-container\" style=\"background-color: #FFF2D6;\">\r\n            <svg class=\"service-icon\" viewBox=\"0 0 24 24\" width=\"32\" height=\"32\">\r\n              <path fill=\"#FFA726\" d=\"M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"service-info\">\r\n            <text class=\"service-title\">打车红包</text>\r\n            <text class=\"service-desc\">最高返2.4%-5%</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"platform-list\">\r\n          <view class=\"platform-item\" @tap=\"goToPlatformService('didi')\">\r\n            <image class=\"platform-logo\" src=\"/static/images/cashback/platform-didi.png\" mode=\"aspectFit\"></image>\r\n            <view class=\"platform-info\">\r\n              <text class=\"platform-name\">滴滴出行</text>\r\n              <text class=\"platform-desc\">最高返5%</text>\r\n            </view>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path fill=\"#CCCCCC\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n            </svg>\r\n          </view>\r\n          \r\n          <view class=\"platform-item\" @tap=\"goToPlatformService('caocao')\">\r\n            <image class=\"platform-logo\" src=\"/static/images/cashback/platform-caocao.png\" mode=\"aspectFit\"></image>\r\n            <view class=\"platform-info\">\r\n              <text class=\"platform-name\">曹操出行</text>\r\n              <text class=\"platform-desc\">最高返2.4%</text>\r\n            </view>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path fill=\"#CCCCCC\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      \r\n      <!-- 电影票 -->\r\n      <block v-if=\"serviceType === 'movie'\">\r\n        <view class=\"service-header\">\r\n          <view class=\"service-icon-container\" style=\"background-color: #FFE0EC;\">\r\n            <svg class=\"service-icon\" viewBox=\"0 0 24 24\" width=\"32\" height=\"32\">\r\n              <path fill=\"#E91E63\" d=\"M18,9H16V7H18M18,13H16V11H18M18,17H16V15H18M8,9H6V7H8M8,13H6V11H8M8,17H6V15H8M18,3V5H16V3H8V5H6V3H4V21H6V19H8V21H16V19H18V21H20V3H18Z\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"service-info\">\r\n            <text class=\"service-title\">电影票8折起</text>\r\n            <text class=\"service-desc\">返10%</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"platform-list\">\r\n          <view class=\"platform-item\" @tap=\"goToPlatformService('maoyan')\">\r\n            <image class=\"platform-logo\" src=\"/static/images/cashback/platform-maoyan.png\" mode=\"aspectFit\"></image>\r\n            <view class=\"platform-info\">\r\n              <text class=\"platform-name\">猫眼电影</text>\r\n              <text class=\"platform-desc\">最高返10%</text>\r\n            </view>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path fill=\"#CCCCCC\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n            </svg>\r\n          </view>\r\n          \r\n          <view class=\"platform-item\" @tap=\"goToPlatformService('taopiaopiao')\">\r\n            <image class=\"platform-logo\" src=\"/static/images/cashback/platform-taopiaopiao.png\" mode=\"aspectFit\"></image>\r\n            <view class=\"platform-info\">\r\n              <text class=\"platform-name\">淘票票</text>\r\n              <text class=\"platform-desc\">最高返8%</text>\r\n            </view>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path fill=\"#CCCCCC\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      \r\n      <!-- 其他服务类型... -->\r\n      <block v-if=\"serviceType === 'express'\">\r\n        <view class=\"service-header\">\r\n          <view class=\"service-icon-container\" style=\"background-color: #E3F1FF;\">\r\n            <svg class=\"service-icon\" viewBox=\"0 0 24 24\" width=\"32\" height=\"32\">\r\n              <path fill=\"#2196F3\" d=\"M3,14H5V20H19V14H21V21A1,1 0 0,1 20,22H4A1,1 0 0,1 3,21V14M17,4H7V2H17V4M17.5,5L12,10.5L6.5,5H17.5M20,6.4L17.9,8.5L15.5,6.1L16.9,4.7L20,7.8V6.4M5.93,4.7L7.33,6.1L4.93,8.5L2.83,6.4V7.8L5.93,4.7Z\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"service-info\">\r\n            <text class=\"service-title\">寄快递返现</text>\r\n            <text class=\"service-desc\">返15%</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"platform-list\">\r\n          <view class=\"platform-item\" @tap=\"goToPlatformService('sf')\">\r\n            <image class=\"platform-logo\" src=\"/static/images/cashback/platform-sf.png\" mode=\"aspectFit\"></image>\r\n            <view class=\"platform-info\">\r\n              <text class=\"platform-name\">顺丰速运</text>\r\n              <text class=\"platform-desc\">最高返15%</text>\r\n            </view>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path fill=\"#CCCCCC\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n            </svg>\r\n          </view>\r\n          \r\n          <view class=\"platform-item\" @tap=\"goToPlatformService('jd-express')\">\r\n            <image class=\"platform-logo\" src=\"/static/images/cashback/platform-jd-express.png\" mode=\"aspectFit\"></image>\r\n            <view class=\"platform-info\">\r\n              <text class=\"platform-name\">京东物流</text>\r\n              <text class=\"platform-desc\">最高返12%</text>\r\n            </view>\r\n            <svg class=\"arrow-icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path fill=\"#CCCCCC\" d=\"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\" />\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      \r\n      <!-- 优惠券 -->\r\n      <block v-if=\"serviceType === 'coupon'\">\r\n        <view class=\"service-header special-header\">\r\n          <view class=\"special-info\">\r\n            <text class=\"special-title\">淘宝-搜了么</text>\r\n            <text class=\"special-desc\">免费奶茶喝到爽</text>\r\n            <text class=\"special-subdesc\">下单再返3元</text>\r\n          </view>\r\n          <view class=\"special-price\">\r\n            <text class=\"price-value\">15元</text>\r\n            <text class=\"price-tag\">券</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"coupon-detail\">\r\n          <image class=\"coupon-image\" src=\"/static/images/cashback/coupon-detail.png\" mode=\"widthFix\"></image>\r\n          <view class=\"coupon-info\">\r\n            <text class=\"coupon-title\">搜了么奶茶专享券</text>\r\n            <text class=\"coupon-value\">15元</text>\r\n            <text class=\"coupon-condition\">无门槛</text>\r\n            <text class=\"coupon-valid\">有效期：2023.6.1-2023.12.31</text>\r\n            <button class=\"get-coupon-btn\" @tap=\"getCoupon\">立即领取</button>\r\n          </view>\r\n          <view class=\"coupon-desc\">\r\n            <text class=\"desc-title\">使用说明</text>\r\n            <text class=\"desc-item\">1. 打开淘宝APP，搜索\"搜了么\"</text>\r\n            <text class=\"desc-item\">2. 进入搜了么小程序</text>\r\n            <text class=\"desc-item\">3. 选择奶茶商品下单</text>\r\n            <text class=\"desc-item\">4. 结算时选择使用优惠券</text>\r\n            <text class=\"desc-item\">5. 下单后返利将在确认收货后到账</text>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      \r\n      <!-- 会员充值 -->\r\n      <block v-if=\"serviceType === 'vip'\">\r\n        <view class=\"service-header\">\r\n          <view class=\"service-icon-container\" style=\"background-color: #E8F5E9;\">\r\n            <svg class=\"service-icon\" viewBox=\"0 0 24 24\" width=\"32\" height=\"32\">\r\n              <path fill=\"#4CAF50\" d=\"M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z\" />\r\n            </svg>\r\n          </view>\r\n          <view class=\"service-info\">\r\n            <text class=\"service-title\">会员充值</text>\r\n            <text class=\"service-desc\">3.6元起</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"vip-list\">\r\n          <view class=\"vip-item\" v-for=\"(item, index) in vipList\" :key=\"index\" @tap=\"goToVipDetail(item)\">\r\n            <image class=\"vip-logo\" :src=\"item.logo\" mode=\"aspectFit\"></image>\r\n            <view class=\"vip-info\">\r\n              <text class=\"vip-name\">{{ item.name }}</text>\r\n              <text class=\"vip-desc\">{{ item.desc }}</text>\r\n            </view>\r\n            <view class=\"vip-price\">\r\n              <text class=\"price-value\">{{ item.price }}元起</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </block>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport CustomNavbar from '../../components/CustomNavbar.vue';\r\n\r\nexport default {\r\n  components: {\r\n    CustomNavbar\r\n  },\r\n  data() {\r\n    return {\r\n      serviceType: '',\r\n      pageTitle: '',\r\n      vipList: [\r\n        {\r\n          id: 1,\r\n          name: '腾讯视频VIP',\r\n          logo: '/static/images/cashback/vip-tencent.png',\r\n          desc: '月卡/季卡/年卡',\r\n          price: '19.8'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '爱奇艺VIP',\r\n          logo: '/static/images/cashback/vip-iqiyi.png',\r\n          desc: '月卡/季卡/年卡',\r\n          price: '19.0'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '优酷VIP',\r\n          logo: '/static/images/cashback/vip-youku.png',\r\n          desc: '月卡/季卡/年卡',\r\n          price: '18.8'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '芒果TV VIP',\r\n          logo: '/static/images/cashback/vip-mgtv.png',\r\n          desc: '月卡/季卡/年卡',\r\n          price: '15.0'\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '网易云音乐VIP',\r\n          logo: '/static/images/cashback/vip-netease.png',\r\n          desc: '月卡/季卡/年卡',\r\n          price: '8.0'\r\n        },\r\n        {\r\n          id: 6,\r\n          name: 'QQ音乐VIP',\r\n          logo: '/static/images/cashback/vip-qqmusic.png',\r\n          desc: '月卡/季卡/年卡',\r\n          price: '15.0'\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  onLoad(options) {\r\n    this.serviceType = options.type || 'takeout';\r\n    this.setPageTitle();\r\n  },\r\n  methods: {\r\n    setPageTitle() {\r\n      switch(this.serviceType) {\r\n        case 'takeout':\r\n          this.pageTitle = '外卖红包';\r\n          break;\r\n        case 'taxi':\r\n          this.pageTitle = '打车红包';\r\n          break;\r\n        case 'movie':\r\n          this.pageTitle = '电影票优惠';\r\n          break;\r\n        case 'express':\r\n          this.pageTitle = '快递返现';\r\n          break;\r\n        case 'coupon':\r\n          this.pageTitle = '优惠券';\r\n          break;\r\n        case 'vip':\r\n          this.pageTitle = '会员充值';\r\n          break;\r\n        default:\r\n          this.pageTitle = '生活服务';\r\n      }\r\n    },\r\n    goToPlatformService(platform) {\r\n      uni.showToast({\r\n        title: '平台服务功能正在开发中',\r\n        icon: 'none',\r\n        duration: 2000\r\n      });\r\n    },\r\n    goToVipDetail(vip) {\r\n      uni.showToast({\r\n        title: '会员充值功能正在开发中',\r\n        icon: 'none',\r\n        duration: 2000\r\n      });\r\n    },\r\n    getCoupon() {\r\n      uni.showLoading({\r\n        title: '领取中...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '领取成功',\r\n          icon: 'success'\r\n        });\r\n      }, 1000);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.life-service-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.content-container {\r\n  padding-top: calc(var(--status-bar-height) + 44px);\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.service-header {\r\n  margin: 16px;\r\n  padding: 20px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  \r\n  &.special-header {\r\n    background: linear-gradient(135deg, #F5F0FF 0%, #EDE7F6 100%);\r\n    padding: 24px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    \r\n    .special-info {\r\n      .special-title {\r\n        font-size: 16px;\r\n        color: #333333;\r\n        margin-bottom: 6px;\r\n        display: block;\r\n      }\r\n      \r\n      .special-desc {\r\n        font-size: 20px;\r\n        color: #9C27B0;\r\n        font-weight: 600;\r\n        margin-bottom: 4px;\r\n        display: block;\r\n      }\r\n      \r\n      .special-subdesc {\r\n        font-size: 14px;\r\n        color: #666666;\r\n        display: block;\r\n      }\r\n    }\r\n    \r\n    .special-price {\r\n      background-color: #FF5252;\r\n      border-radius: 24px;\r\n      padding: 8px 16px;\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      .price-value {\r\n        font-size: 24px;\r\n        font-weight: 600;\r\n        color: #FFFFFF;\r\n      }\r\n      \r\n      .price-tag {\r\n        font-size: 14px;\r\n        color: #FFFFFF;\r\n        background-color: rgba(255, 255, 255, 0.3);\r\n        border-radius: 12px;\r\n        padding: 2px 6px;\r\n        margin-left: 4px;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .service-icon-container {\r\n    width: 56px;\r\n    height: 56px;\r\n    border-radius: 12px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 16px;\r\n  }\r\n  \r\n  .service-info {\r\n    .service-title {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-bottom: 6px;\r\n      display: block;\r\n    }\r\n    \r\n    .service-desc {\r\n      font-size: 14px;\r\n      color: #9C27B0;\r\n      display: block;\r\n    }\r\n  }\r\n}\r\n\r\n.platform-list {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  padding: 8px 0;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  \r\n  .platform-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 16px;\r\n    border-bottom: 1px solid #F0F0F0;\r\n    \r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    .platform-logo {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 8px;\r\n      margin-right: 12px;\r\n    }\r\n    \r\n    .platform-info {\r\n      flex: 1;\r\n      \r\n      .platform-name {\r\n        font-size: 16px;\r\n        color: #333333;\r\n        margin-bottom: 4px;\r\n        display: block;\r\n      }\r\n      \r\n      .platform-desc {\r\n        font-size: 14px;\r\n        color: #9C27B0;\r\n        display: block;\r\n      }\r\n    }\r\n    \r\n    .arrow-icon {\r\n      margin-left: 8px;\r\n    }\r\n  }\r\n}\r\n\r\n.coupon-detail {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  \r\n  .coupon-image {\r\n    width: 100%;\r\n    border-radius: 12px;\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .coupon-info {\r\n    padding: 16px 0;\r\n    border-bottom: 1px dashed #EEEEEE;\r\n    margin-bottom: 16px;\r\n    \r\n    .coupon-title {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-bottom: 12px;\r\n      display: block;\r\n    }\r\n    \r\n    .coupon-value {\r\n      font-size: 24px;\r\n      font-weight: 700;\r\n      color: #FF5252;\r\n      margin-bottom: 6px;\r\n      display: block;\r\n    }\r\n    \r\n    .coupon-condition {\r\n      font-size: 14px;\r\n      color: #666666;\r\n      margin-bottom: 6px;\r\n      display: block;\r\n    }\r\n    \r\n    .coupon-valid {\r\n      font-size: 12px;\r\n      color: #999999;\r\n      margin-bottom: 16px;\r\n      display: block;\r\n    }\r\n    \r\n    .get-coupon-btn {\r\n      width: 100%;\r\n      height: 44px;\r\n      background-color: #9C27B0;\r\n      color: #FFFFFF;\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n      border-radius: 22px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border: none;\r\n    }\r\n  }\r\n  \r\n  .coupon-desc {\r\n    .desc-title {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-bottom: 12px;\r\n      display: block;\r\n    }\r\n    \r\n    .desc-item {\r\n      font-size: 14px;\r\n      color: #666666;\r\n      margin-bottom: 8px;\r\n      display: block;\r\n      line-height: 1.5;\r\n    }\r\n  }\r\n}\r\n\r\n.vip-list {\r\n  margin: 16px;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16px;\r\n  padding: 8px 0;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  \r\n  .vip-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 16px;\r\n    border-bottom: 1px solid #F0F0F0;\r\n    \r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    .vip-logo {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 8px;\r\n      margin-right: 12px;\r\n    }\r\n    \r\n    .vip-info {\r\n      flex: 1;\r\n      \r\n      .vip-name {\r\n        font-size: 16px;\r\n        color: #333333;\r\n        margin-bottom: 4px;\r\n        display: block;\r\n      }\r\n      \r\n      .vip-desc {\r\n        font-size: 14px;\r\n        color: #666666;\r\n        display: block;\r\n      }\r\n    }\r\n    \r\n    .vip-price {\r\n      .price-value {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #FF5252;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/pages/life-service/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAqOA,MAAK,eAAgB,MAAW;AAEhC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,WAAW;AAAA,MACX,SAAS;AAAA,QACP;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAAA;EAEH;AAAA,EACD,OAAO,SAAS;AACd,SAAK,cAAc,QAAQ,QAAQ;AACnC,SAAK,aAAY;AAAA,EAClB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACb,cAAO,KAAK,aAAW;AAAA,QACrB,KAAK;AACH,eAAK,YAAY;AACjB;AAAA,QACF,KAAK;AACH,eAAK,YAAY;AACjB;AAAA,QACF,KAAK;AACH,eAAK,YAAY;AACjB;AAAA,QACF,KAAK;AACH,eAAK,YAAY;AACjB;AAAA,QACF,KAAK;AACH,eAAK,YAAY;AACjB;AAAA,QACF,KAAK;AACH,eAAK,YAAY;AACjB;AAAA,QACF;AACE,eAAK,YAAY;AAAA,MACrB;AAAA,IACD;AAAA,IACD,oBAAoB,UAAU;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACF;AAAA,IACD,cAAc,KAAK;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACF;AAAA,IACD,YAAY;AACVA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrVA,GAAG,WAAW,eAAe;"}