"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const mixins_merchantPromotionMixin = require("../../mixins/merchantPromotionMixin.js");
if (!Array) {
  const _component_FloatPromotionButton = common_vendor.resolveComponent("FloatPromotionButton");
  _component_FloatPromotionButton();
}
if (!Math) {
  (FabButtons + PromotionToolButton + SimplePopup)();
}
const FabButtons = () => "../../components/FabButtons.js";
const SimplePopup = () => "../../components/SimplePopup.js";
const PromotionToolButton = () => "../../components/PromotionToolButton.js";
const _sfc_main = {
  __name: "shop-detail",
  setup(__props) {
    const shopId = common_vendor.ref(null);
    const isFollowing = common_vendor.ref(false);
    common_vendor.ref(false);
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.ref(44);
    const safeAreaInsetTop = common_vendor.ref(0);
    const isIOS = common_vendor.ref(false);
    common_vendor.ref(false);
    common_vendor.ref("");
    const redPacketPopup = common_vendor.ref(null);
    const shopList = common_vendor.ref([
      {
        id: "1",
        shopName: "五分利电器",
        category: "数码电器",
        scale: "10-20人",
        address: "河北省邯郸市磁县祥和路",
        contactPhone: "188-8888-8888",
        businessTime: "09:00-21:00",
        description: "五分利电器是本地知名的电器销售与维修服务商。我们提供各类家用电器，包括冰箱、洗衣机、空调、电视等。全场特价，送货上门，并提供专业安装和维修服务。我们的技术人员经验丰富，能够高效解决各类电器故障问题。\n\n本店支持分期付款，以旧换新，并提供长期的售后保障。欢迎新老顾客前来选购！",
        viewCount: 2345,
        favoriteCount: 128,
        logo: "/static/images/tabbar/商家入驻.png",
        qrcode: "/static/images/tabbar/二维码示例.png",
        supportMessage: true,
        images: [
          "/static/images/banner/banner-1.png",
          "/static/images/banner/banner-2.png",
          "/static/images/banner/banner-3.jpg"
        ],
        albumImages: [
          "/static/images/banner/banner-1.png",
          "/static/images/banner/banner-2.png",
          "/static/images/banner/banner-3.jpg",
          "/static/images/banner/banner-1.png",
          "/static/images/banner/banner-2.png",
          "/static/images/banner/banner-3.jpg"
        ]
      }
      // ... 其他商家数据
    ]);
    const shopData = common_vendor.ref({});
    const isDrawing = common_vendor.ref(false);
    const hasDrawn = common_vendor.ref(false);
    const drawResult = common_vendor.ref({
      amount: 0,
      message: ""
    });
    const promotionMixin = mixins_merchantPromotionMixin.merchantPromotionMixin;
    const hasPromotionPermission = common_vendor.ref(false);
    const promotionData = common_vendor.ref({});
    common_vendor.onLoad((options) => {
      if (options.id) {
        shopId.value = options.id;
        const shop = shopList.value.find((item) => item.id === options.id);
        if (shop) {
          shopData.value = shop;
          shopData.value.canDistribute = true;
          shopData.value.ownerId = "123456";
        }
      }
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight;
      isIOS.value = systemInfo.platform === "ios";
      safeAreaInsetTop.value = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.top : 0;
      initPromotion();
    });
    const initPromotion = () => {
      hasPromotionPermission.value = promotionMixin.methods.isContentOwner.call({
        $store: {
          state: {
            user: {
              userId: "123456"
              // 假设当前用户ID
            }
          }
        },
        shopData: shopData.value
      }) || promotionMixin.methods.isCommissionContent.call({
        shopData: shopData.value
      });
      if (hasPromotionPermission.value) {
        promotionData.value = promotionMixin.methods.generatePromotionData.call({
          shopData: shopData.value,
          promotionData: {}
        }).promotionData;
      }
    };
    const showMerchantPromotion = () => {
      promotionMixin.methods.showMerchantPromotion.call({
        hasPromotionPermission: hasPromotionPermission.value,
        openPromotionTools: () => {
          common_vendor.index.navigateTo({
            url: "/subPackages/promotion/pages/promotion-tool?type=merchant&id=" + shopData.value.id
          });
        }
      });
    };
    common_vendor.onReady(() => {
    });
    common_vendor.onShareAppMessage(() => {
      return {
        title: shopData.value.shopName + " - 磁州生活网推荐商家",
        path: "/pages/business/shop-detail?id=" + shopData.value.id,
        imageUrl: shopData.value.images && shopData.value.images.length > 0 ? shopData.value.images[0] : ""
      };
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const viewAllPhotos = () => {
      common_vendor.index.previewImage({
        urls: shopData.value.albumImages,
        current: 0
      });
    };
    const previewImage = (index) => {
      common_vendor.index.previewImage({
        urls: shopData.value.albumImages,
        current: index
      });
    };
    const previewQRCode = () => {
      common_vendor.index.previewImage({
        urls: [shopData.value.qrcode],
        current: 0
      });
    };
    const openLocation = () => {
      common_vendor.index.openLocation({
        latitude: 36.3427,
        longitude: 114.7582,
        name: shopData.value.shopName,
        address: shopData.value.address,
        scale: 18
      });
    };
    const makePhoneCall = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: shopData.value.contactPhone
      });
    };
    const toggleFollow = () => {
      isFollowing.value = !isFollowing.value;
      common_vendor.index.showToast({
        title: isFollowing.value ? "已关注" : "已取消关注",
        icon: "none"
      });
    };
    const contactShop = () => {
      if (!shopData.value.supportMessage) {
        makePhoneCall();
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages/message/chat?id=" + shopData.value.id + "&name=" + encodeURIComponent(shopData.value.shopName)
      });
    };
    const callShop = () => {
      makePhoneCall();
    };
    const backToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const shareShop = () => {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    };
    const generateShareImage = () => {
      common_vendor.index.showLoading({
        title: "生成中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "海报生成成功",
          icon: "success"
        });
        common_vendor.index.saveImageToPhotosAlbum({
          filePath: shopData.value.images[0],
          success: () => {
            common_vendor.index.showToast({
              title: "已保存到相册",
              icon: "success"
            });
          },
          fail: () => {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        });
      }, 1500);
    };
    const showReportOptions = () => {
      common_vendor.index.showActionSheet({
        itemList: ["虚假信息", "诈骗信息", "违法信息", "侵权信息", "其他问题"],
        success: (res) => {
          const selectedType = ["虚假信息", "诈骗信息", "违法信息", "侵权信息", "其他问题"][res.tapIndex];
          const userInfo = common_vendor.index.getStorageSync("userInfo");
          if (!userInfo) {
            common_vendor.index.showModal({
              title: "提示",
              content: "举报需要先登录，是否前往登录？",
              success: (res2) => {
                if (res2.confirm) {
                  common_vendor.index.navigateTo({
                    url: "/pages/login/login?redirect=" + encodeURIComponent("/pages/business/shop-detail?id=" + shopData.value.id)
                  });
                }
              }
            });
            return;
          }
          if (selectedType === "其他问题") {
            showReportInputDialog();
          } else {
            submitReport();
          }
        }
      });
    };
    const showReportInputDialog = () => {
      common_vendor.index.showModal({
        title: "请描述问题",
        placeholderText: "请详细描述您遇到的问题",
        editable: true,
        success: (res) => {
          if (res.confirm && res.content) {
            submitReport("其他问题: " + res.content);
          }
        }
      });
    };
    const submitReport = (reportReason) => {
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "举报成功",
          icon: "success"
        });
      }, 1e3);
    };
    const handleUseRedPacket = () => {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!userInfo) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/login/login?redirect=" + encodeURIComponent("/pages/business/shop-detail?id=" + shopData.value.id)
          });
        }, 1500);
        return;
      }
      isDrawing.value = true;
      hasDrawn.value = false;
      redPacketPopup.value.open();
      setTimeout(() => {
        try {
          const amount = (Math.random() * 87 + 1).toFixed(2);
          drawResult.value = {
            amount,
            message: amount >= 20 ? "恭喜您，手气不错！" : "谢谢参与，下次再来！"
          };
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/business/shop-detail.vue:648", "抽红包失败", error);
          drawResult.value = {
            amount: 0,
            message: "网络异常，请重试"
          };
        } finally {
          isDrawing.value = false;
          hasDrawn.value = true;
        }
      }, 2e3);
    };
    const useDrawnRedPacket = () => {
      common_vendor.index.showToast({
        title: `红包已添加到您的账户`,
        icon: "success"
      });
      closeRedPacketPopup();
    };
    const closeRedPacketPopup = () => {
      redPacketPopup.value.close();
      setTimeout(() => {
        isDrawing.value = false;
        hasDrawn.value = false;
      }, 300);
    };
    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$13,
        b: common_vendor.o(goBack),
        c: isIOS.value ? safeAreaInsetTop.value + "px" : statusBarHeight.value + "px",
        d: common_vendor.p({
          pageName: "shop-detail",
          pageInfo: {
            title: shopData.value.shopName + " - 商家详情，点击查看更多信息",
            path: "/pages/business/shop-detail?id=" + shopData.value.id,
            imageUrl: shopData.value.images && shopData.value.images.length > 0 ? shopData.value.images[0] : ""
          }
        }),
        e: common_vendor.o(showMerchantPromotion),
        f: common_assets._imports_10,
        g: common_vendor.o(generateShareImage),
        h: common_vendor.f(shopData.value.images, (img, index, i0) => {
          return {
            a: img,
            b: index
          };
        }),
        i: shopData.value.logo || "/static/images/tabbar/商家入驻.png",
        j: common_vendor.t(shopData.value.shopName),
        k: common_vendor.t(shopData.value.category),
        l: common_vendor.t(shopData.value.scale),
        m: common_vendor.t(shopData.value.viewCount),
        n: common_vendor.t(shopData.value.favoriteCount),
        o: isFollowing.value ? "/static/images/tabbar/已关注.png" : "/static/images/tabbar/关注.png",
        p: common_vendor.t(isFollowing.value ? "已关注" : "关注"),
        q: isFollowing.value ? 1 : "",
        r: common_vendor.o(toggleFollow),
        s: common_assets._imports_6,
        t: common_vendor.o(contactShop),
        v: hasPromotionPermission.value
      }, hasPromotionPermission.value ? {
        w: common_vendor.o(showMerchantPromotion),
        x: common_vendor.p({
          buttonText: "推广"
        })
      } : {}, {
        y: common_assets._imports_2$8,
        z: common_assets._imports_4$4,
        A: common_vendor.t(shopData.value.address),
        B: common_vendor.o(openLocation),
        C: common_assets._imports_6,
        D: common_vendor.t(shopData.value.contactPhone),
        E: common_assets._imports_1$5,
        F: common_vendor.o(makePhoneCall),
        G: common_assets._imports_6$3,
        H: common_vendor.t(shopData.value.businessTime),
        I: common_assets._imports_0$6,
        J: common_assets._imports_1$5,
        K: common_vendor.o(showReportOptions),
        L: common_assets._imports_8$2,
        M: common_vendor.t(shopData.value.description),
        N: shopData.value.hasConsumeRedPacket
      }, shopData.value.hasConsumeRedPacket ? common_vendor.e({
        O: common_assets._imports_0$11,
        P: shopData.value.hasConsumeRedPacket
      }, shopData.value.hasConsumeRedPacket ? {} : {}, {
        Q: shopData.value.merchantLogo || shopData.value.logo,
        R: common_vendor.t(formatDate((/* @__PURE__ */ new Date()).getTime() + 30 * 24 * 60 * 60 * 1e3)),
        S: common_vendor.o(handleUseRedPacket),
        T: common_vendor.t(Math.floor(Math.random() * 200 + 50))
      }) : {}, {
        U: common_assets._imports_10$2,
        V: common_assets._imports_1$5,
        W: common_vendor.o(viewAllPhotos),
        X: common_vendor.f(shopData.value.albumImages.slice(0, 6), (photo, index, i0) => {
          return {
            a: photo,
            b: index,
            c: common_vendor.o(($event) => previewImage(index), index)
          };
        }),
        Y: common_assets._imports_11$4,
        Z: shopData.value.qrcode,
        aa: common_vendor.o(previewQRCode),
        ab: isDrawing.value
      }, isDrawing.value ? {
        ac: common_assets._imports_0$11
      } : {}, {
        ad: hasDrawn.value
      }, hasDrawn.value ? {
        ae: common_assets._imports_0$11,
        af: common_vendor.t(drawResult.value.amount),
        ag: common_vendor.t(drawResult.value.message || "红包已发放到您的账户"),
        ah: common_vendor.o(useDrawnRedPacket),
        ai: common_vendor.o(closeRedPacketPopup)
      } : {}, {
        aj: common_vendor.sr(redPacketPopup, "7c1f337e-3", {
          "k": "redPacketPopup"
        }),
        ak: common_vendor.p({
          type: "center"
        }),
        al: common_assets._imports_12,
        am: common_vendor.o(backToHome),
        an: common_assets._imports_3$3,
        ao: common_vendor.o(shareShop),
        ap: common_assets._imports_14,
        aq: common_vendor.o(contactShop),
        ar: common_vendor.o(callShop)
      });
    };
  }
};
_sfc_main.__runtimeHooks = 2;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/business/shop-detail.js.map
