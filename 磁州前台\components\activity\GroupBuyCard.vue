<template>
  <!-- 拼团活动卡片 - 苹果风格设计 -->
  <view class="group-buy-card">
    <!-- 使用基础活动卡片 -->
    <ActivityCard 
      :item="item" 
      @favorite="$emit('favorite', item.id)"
      @action="$emit('action', { id: item.id, type: item.type, status: item.status })"
    >
      <!-- 拼团特有信息插槽 -->
      <template #special-info>
        <view class="group-buy-special">
          <!-- 价格区域 -->
          <view class="price-section">
            <view class="current-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.groupPrice}}</text>
            </view>
            <view class="original-price">
              <text class="price-label">原价</text>
              <text class="price-through">¥{{item.originalPrice}}</text>
            </view>
            <view class="discount-tag">
              <text class="discount-value">{{discountPercent}}折</text>
            </view>
          </view>
          
          <!-- 拼团进度 -->
          <view class="group-progress">
            <view class="progress-header">
              <text class="progress-title">拼团进度</text>
              <text class="progress-status">{{item.currentGroupMembers}}/{{item.groupSize}}人</text>
            </view>
            <view class="progress-bar">
              <view class="progress-inner" :style="{width: progressWidth + '%'}"></view>
            </view>
            <view class="progress-tip" v-if="item.status === 'ongoing'">
              <text class="tip-text">还差{{remainCount}}人成团，快邀请好友吧！</text>
            </view>
          </view>
          
          <!-- 拼团成员头像 -->
          <view class="group-members" v-if="item.participants && item.participants.length > 0">
            <view class="members-title">参团好友</view>
            <view class="members-avatars">
              <view 
                class="member-avatar-wrapper" 
                v-for="(member, index) in displayMembers" 
                :key="index"
              >
                <image :src="member.avatar" class="member-avatar" mode="aspectFill"></image>
                <view class="team-leader-badge" v-if="index === 0">团长</view>
              </view>
              <view class="more-members" v-if="item.participants.length > maxDisplayMembers">
                <text>+{{item.participants.length - maxDisplayMembers}}</text>
              </view>
            </view>
          </view>
        </view>
      </template>
    </ActivityCard>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import ActivityCard from './ActivityCard.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

// 最大显示成员数
const maxDisplayMembers = 4;

// 计算折扣百分比
const discountPercent = computed(() => {
  if (!props.item.groupPrice || !props.item.originalPrice) return '';
  return Math.floor((props.item.groupPrice / props.item.originalPrice) * 10);
});

// 计算进度条宽度
const progressWidth = computed(() => {
  if (!props.item.currentGroupMembers || !props.item.groupSize) return 0;
  return (props.item.currentGroupMembers / props.item.groupSize) * 100;
});

// 计算剩余所需人数
const remainCount = computed(() => {
  if (!props.item.currentGroupMembers || !props.item.groupSize) return 0;
  return props.item.groupSize - props.item.currentGroupMembers;
});

// 显示的成员头像
const displayMembers = computed(() => {
  if (!props.item.participants) return [];
  return props.item.participants.slice(0, maxDisplayMembers);
});
</script>

<style scoped>
/* 拼团活动卡片特有样式 */
.group-buy-card {
  /* 继承基础卡片样式 */
}

/* 拼团特有信息区域 */
.group-buy-special {
  padding: 20rpx;
  background-color: rgba(52, 199, 89, 0.05);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 价格区域 */
.price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.current-price {
  display: flex;
  align-items: baseline;
  color: #34c759;
  margin-right: 16rpx;
}

.price-symbol {
  font-size: 24rpx;
  font-weight: 500;
}

.price-value {
  font-size: 40rpx;
  font-weight: 700;
}

.original-price {
  display: flex;
  align-items: baseline;
  margin-right: 16rpx;
}

.price-label {
  font-size: 22rpx;
  color: #8e8e93;
  margin-right: 4rpx;
}

.price-through {
  font-size: 24rpx;
  color: #8e8e93;
  text-decoration: line-through;
}

.discount-tag {
  padding: 4rpx 10rpx;
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 10rpx;
}

.discount-value {
  font-size: 22rpx;
  color: #34c759;
  font-weight: 500;
}

/* 拼团进度 */
.group-progress {
  margin-bottom: 20rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.progress-title {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
}

.progress-status {
  font-size: 24rpx;
  color: #34c759;
  font-weight: 500;
}

.progress-bar {
  height: 10rpx;
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-inner {
  height: 100%;
  background-color: #34c759;
  border-radius: 5rpx;
  transition: width 0.3s ease;
}

.progress-tip {
  margin-top: 8rpx;
}

.tip-text {
  font-size: 22rpx;
  color: #ff9500;
}

/* 拼团成员 */
.group-members {
  margin-top: 16rpx;
}

.members-title {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.members-avatars {
  display: flex;
  align-items: center;
}

.member-avatar-wrapper {
  position: relative;
  margin-right: 20rpx;
}

.member-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.team-leader-badge {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ff9500;
  color: #ffffff;
  font-size: 18rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  white-space: nowrap;
}

.more-members {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f2f2f7;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.more-members text {
  font-size: 20rpx;
  color: #8e8e93;
}
</style> 