<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">裂变红包详解</text>
      <view class="navbar-right">
        <view class="share-icon" @click="shareGuide">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
<circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="content-scroll">
      <!-- 页面头部 -->
      <view class="page-header">
        <view class="header-bg" style="background: linear-gradient(135deg, #43CBFF, #9708CC);">
          <text class="header-title">如何利用裂变红包实现用户增长</text>
          <text class="header-subtitle">低成本高效率的用户获取策略</text>
        </view>
      </view>
      
      <!-- 内容部分 -->
      <view class="content-section">
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(151, 8, 204, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </view>
          <text class="title-text">什么是裂变红包</text>
        </view>
        
        <view class="content-text">
          <text>裂变红包是一种基于社交传播的营销工具，通过激励用户分享红包给好友，从而实现用户快速增长的营销方式。裂变红包利用了用户社交网络的传播效应，每个用户都成为品牌的自发推广者，形成"1传10、10传100"的指数级增长。</text>
        </view>
        
        <view class="content-image">
          <image src="/static/images/fission-redpacket.png" mode="widthFix"></image>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(151, 8, 204, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </view>
          <text class="title-text">裂变红包的核心原理</text>
        </view>
        
        <view class="principle-diagram">
          <view class="diagram-step">
            <view class="step-circle">1</view>
            <view class="step-content">
              <text class="step-title">用户领取红包</text>
              <text class="step-desc">用户在平台领取初始红包</text>
            </view>
          </view>
          <view class="step-arrow"></view>
          <view class="diagram-step">
            <view class="step-circle">2</view>
            <view class="step-content">
              <text class="step-title">分享给好友</text>
              <text class="step-desc">用户将红包链接分享给好友</text>
            </view>
          </view>
          <view class="step-arrow"></view>
          <view class="diagram-step">
            <view class="step-circle">3</view>
            <view class="step-content">
              <text class="step-title">好友注册/领取</text>
              <text class="step-desc">好友注册并领取红包</text>
            </view>
          </view>
          <view class="step-arrow"></view>
          <view class="diagram-step">
            <view class="step-circle">4</view>
            <view class="step-content">
              <text class="step-title">双方获得奖励</text>
              <text class="step-desc">邀请者和被邀请者均获得奖励</text>
            </view>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(151, 8, 204, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
          </view>
          <text class="title-text">常见裂变红包模式</text>
        </view>
        
        <view class="model-list">
          <view class="model-item">
            <view class="model-header">
              <view class="model-icon" style="background-color: rgba(151, 8, 204, 0.1);">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path>
                  <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path>
                  <path d="M4 22h16"></path>
                  <path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path>
                  <path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"></path>
                  <path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path>
                </svg>
              </view>
              <text class="model-title">拆红包模式</text>
            </view>
            <view class="model-content">
              <text class="model-desc">用户需邀请一定数量的好友助力，才能拆开获得红包。每位助力好友也可获得小额红包，形成链式传播。</text>
              <view class="model-advantages">
                <text class="advantages-title">优势：</text>
                <view class="advantage-point">
                  <text class="point-dot"></text>
                  <text class="point-text">用户有强烈的邀请动机</text>
                </view>
                <view class="advantage-point">
                  <text class="point-dot"></text>
                  <text class="point-text">助力门槛低，易于传播</text>
                </view>
                <view class="advantage-point">
                  <text class="point-dot"></text>
                  <text class="point-text">适合新用户拉新场景</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="model-item">
            <view class="model-header">
              <view class="model-icon" style="background-color: rgba(151, 8, 204, 0.1);">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
              </view>
              <text class="model-title">瓜分红包模式</text>
            </view>
            <view class="model-content">
              <text class="model-desc">设置一个大额红包池，邀请好友共同瓜分。参与人数越多，每人分得的金额可能越小，但也可设置最低保障金额。</text>
              <view class="model-advantages">
                <text class="advantages-title">优势：</text>
                <view class="advantage-point">
                  <text class="point-dot"></text>
                  <text class="point-text">有趣的博弈心理，增加参与感</text>
                </view>
                <view class="advantage-point">
                  <text class="point-dot"></text>
                  <text class="point-text">适合大型活动和节日营销</text>
                </view>
                <view class="advantage-point">
                  <text class="point-dot"></text>
                  <text class="point-text">可控制总成本</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="model-item">
            <view class="model-header">
              <view class="model-icon" style="background-color: rgba(151, 8, 204, 0.1);">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M8 3H5a2 2 0 0 0-2 2v3"></path>
                  <path d="M21 8V5a2 2 0 0 0-2-2h-3"></path>
                  <path d="M3 16v3a2 2 0 0 0 2 2h3"></path>
                  <path d="M16 21h3a2 2 0 0 0 2-2v-3"></path>
                </svg>
              </view>
              <text class="model-title">阶梯奖励模式</text>
            </view>
            <view class="model-content">
              <text class="model-desc">用户邀请的好友数量越多，获得的奖励越高。设置不同的邀请人数门槛，对应不同等级的奖励。</text>
              <view class="model-advantages">
                <text class="advantages-title">优势：</text>
                <view class="advantage-point">
                  <text class="point-dot"></text>
                  <text class="point-text">激励用户持续邀请</text>
                </view>
                <view class="advantage-point">
                  <text class="point-dot"></text>
                  <text class="point-text">提高用户参与度和粘性</text>
                </view>
                <view class="advantage-point">
                  <text class="point-dot"></text>
                  <text class="point-text">适合长期运营的裂变活动</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(151, 8, 204, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 20h9"></path>
              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
            </svg>
          </view>
          <text class="title-text">裂变红包设计要点</text>
        </view>
        
        <view class="design-points">
          <view class="design-point">
            <view class="point-header">
              <view class="point-icon" style="background-color: rgba(151, 8, 204, 0.1);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 8v4"></path>
                  <path d="M12 16h.01"></path>
                </svg>
              </view>
              <text class="point-title">合理的奖励机制</text>
            </view>
            <text class="point-desc">设计既能激励用户分享，又能保证商业可持续的奖励机制。奖励金额要有吸引力，但不宜过高导致亏损。</text>
          </view>
          
          <view class="design-point">
            <view class="point-header">
              <view class="point-icon" style="background-color: rgba(151, 8, 204, 0.1);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </view>
              <text class="point-title">简单的参与流程</text>
            </view>
            <text class="point-desc">裂变过程要简单易懂，减少参与门槛。分享、注册、领取等步骤越简单，转化率越高。</text>
          </view>
          
          <view class="design-point">
            <view class="point-header">
              <view class="point-icon" style="background-color: rgba(151, 8, 204, 0.1);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="8.5" cy="8.5" r="1.5"></circle>
                  <polyline points="21 15 16 10 5 21"></polyline>
                </svg>
              </view>
              <text class="point-title">吸引人的视觉设计</text>
            </view>
            <text class="point-desc">红包的视觉设计要吸引人，包括封面图、文案和动效等，增强用户分享的意愿。</text>
          </view>
          
          <view class="design-point">
            <view class="point-header">
              <view class="point-icon" style="background-color: rgba(151, 8, 204, 0.1);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </view>
              <text class="point-title">有效的防刷机制</text>
            </view>
            <text class="point-desc">设置合理的防刷机制，如手机号验证、实名认证、IP限制等，防止恶意刷取红包。</text>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(151, 8, 204, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
          </view>
          <text class="title-text">裂变红包效果分析</text>
        </view>
        
        <view class="metrics-cards">
          <view class="metrics-card">
            <text class="metrics-title">裂变系数</text>
            <text class="metrics-value">1.8-3.5</text>
            <text class="metrics-desc">每个用户平均能带来的新用户数量</text>
          </view>
          <view class="metrics-card">
            <text class="metrics-title">获客成本</text>
            <text class="metrics-value">3-15元</text>
            <text class="metrics-desc">获取一个新用户的平均成本</text>
          </view>
          <view class="metrics-card">
            <text class="metrics-title">转化率</text>
            <text class="metrics-value">15%-30%</text>
            <text class="metrics-desc">被邀请用户的注册转化率</text>
          </view>
          <view class="metrics-card">
            <text class="metrics-title">活跃度</text>
            <text class="metrics-value">提升40%</text>
            <text class="metrics-desc">用户活跃度平均提升幅度</text>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(151, 8, 204, 0.1);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9708CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
          </view>
          <text class="title-text">成功案例分析</text>
        </view>
        
        <view class="case-study">
          <view class="case-header">
            <text class="case-title">某电商平台新用户裂变案例</text>
          </view>
          <view class="case-content">
            <text class="case-desc">某电商平台推出"邀请好友，双方各得50元优惠券"的裂变活动。用户通过分享专属链接邀请好友注册并完成首单，邀请者和被邀请者均可获得50元优惠券。活动期间，平台新增用户20万，获客成本降至8元/人，是传统广告获客成本的1/5，且新用户30天留存率提升了25%。</text>
          </view>
          <view class="case-results">
            <view class="result-item">
              <text class="result-label">新增用户</text>
              <text class="result-value">20万</text>
            </view>
            <view class="result-item">
              <text class="result-label">获客成本降低</text>
              <text class="result-value">80%</text>
            </view>
            <view class="result-item">
              <text class="result-label">留存率提升</text>
              <text class="result-value">25%</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 联系我们 -->
      <view class="contact-section">
        <text class="contact-title">需要定制裂变红包方案？</text>
        <text class="contact-desc">我们的专业团队可为您量身定制高效的裂变营销方案</text>
        <button class="contact-btn" @click="contactService">获取方案</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    shareGuide() {
      uni.showActionSheet({
        itemList: ['分享给好友', '分享到朋友圈', '复制链接'],
        success: function(res) {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        }
      });
    },
    contactService() {
      uni.makePhoneCall({
        phoneNumber: '************'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}

.navbar-back {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-icon {
  color: #333;
}

/* 内容滚动区 */
.content-scroll {
  flex: 1;
}

/* 页面头部 */
.page-header {
  height: 180px;
  position: relative;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 10px;
}

.header-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

/* 内容部分 */
.content-section {
  padding: 20px 15px;
  background-color: #fff;
  border-radius: 15px 15px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 1;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  margin-top: 25px;
}

.section-title:first-child {
  margin-top: 0;
}

.title-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.content-text {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.content-image {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.content-image image {
  width: 100%;
}

/* 原理图解 */
.principle-diagram {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px 15px;
  margin-bottom: 20px;
}

.diagram-step {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.step-circle {
  width: 30px;
  height: 30px;
  border-radius: 15px;
  background-color: #9708CC;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 10px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 3px;
}

.step-desc {
  font-size: 13px;
  color: #666;
}

.step-arrow {
  width: 12px;
  height: 12px;
  border-right: 2px solid #9708CC;
  border-bottom: 2px solid #9708CC;
  transform: rotate(45deg);
  margin: 0 auto 15px;
}

/* 模式列表 */
.model-list {
  margin-bottom: 20px;
}

.model-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.model-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.model-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.model-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.model-content {
  padding-left: 46px;
}

.model-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.model-advantages {
  margin-top: 10px;
}

.advantages-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.advantage-point {
  display: flex;
  margin-bottom: 5px;
}

.advantage-point:last-child {
  margin-bottom: 0;
}

.point-dot {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  background-color: #9708CC;
  margin-top: 6px;
  margin-right: 8px;
  flex-shrink: 0;
}

.point-text {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

/* 设计要点 */
.design-points {
  margin-bottom: 20px;
}

.design-point {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.point-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.point-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.point-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.point-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 效果指标 */
.metrics-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px 20px;
}

.metrics-card {
  width: calc(50% - 10px);
  margin: 5px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.metrics-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.metrics-value {
  font-size: 20px;
  font-weight: 700;
  color: #9708CC;
  display: block;
  margin-bottom: 5px;
}

.metrics-desc {
  font-size: 12px;
  color: #666;
}

/* 案例分析 */
.case-study {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.case-header {
  margin-bottom: 10px;
}

.case-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.case-content {
  margin-bottom: 15px;
}

.case-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.case-results {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.result-item {
  text-align: center;
  flex: 1;
}

.result-label {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 5px;
}

.result-value {
  font-size: 18px;
  font-weight: 600;
  color: #9708CC;
}

/* 联系我们 */
.contact-section {
  margin: 0 15px 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.contact-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.contact-desc {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 15px;
}

.contact-btn {
  background: linear-gradient(135deg, #43CBFF, #9708CC);
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
}
</style>