/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.red-packet-container {
  min-height: 100vh;
  background-color: #f7f7f7;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  height: 90rpx;
  padding: var(--status-bar-height) 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: #fff;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.custom-navbar .navbar-left, .custom-navbar .navbar-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-navbar .navbar-left .iconfont, .custom-navbar .navbar-right .iconfont {
  font-size: 40rpx;
}
.custom-navbar .navbar-title {
  font-size: 36rpx;
  font-weight: 500;
}

/* 统计信息区域 */
.stats-section {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: #fff;
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 107, 107, 0.2);
}
.stats-section .stats-card {
  text-align: center;
  flex: 1;
}
.stats-section .stats-card .stats-value {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.stats-section .stats-card .stats-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.filter-tabs .filter-tab {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 16rpx 0;
  position: relative;
  transition: all 0.3s;
}
.filter-tabs .filter-tab.active {
  color: #FF6B6B;
  font-weight: 500;
}
.filter-tabs .filter-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #FF6B6B;
  border-radius: 3rpx;
}

/* 红包列表 */
.red-packet-list {
  height: calc(100vh - 90rpx - var(--status-bar-height) - 170rpx - 100rpx);
  padding: 0 20rpx;
}
.loading-placeholder, .empty-state {
  padding: 100rpx 0;
  text-align: center;
}
.loading-placeholder .empty-image, .empty-state .empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.loading-placeholder .empty-text, .empty-state .empty-text {
  color: #999;
  font-size: 28rpx;
}
.red-packet-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.red-packet-item .merchant-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.red-packet-item .merchant-info .merchant-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.red-packet-item .merchant-info .merchant-details {
  flex: 1;
}
.red-packet-item .merchant-info .merchant-details .merchant-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}
.red-packet-item .merchant-info .merchant-details .publish-time {
  font-size: 24rpx;
  color: #999;
}
.red-packet-item .merchant-info .info-type-tag {
  font-size: 22rpx;
  color: #fff;
  background-color: #1677FF;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}
.red-packet-item .content-section {
  margin-bottom: 20rpx;
}
.red-packet-item .content-section .info-content {
  margin-bottom: 30rpx;
}
.red-packet-item .content-section .info-content .title-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.red-packet-item .content-section .info-content .title-row .title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  flex: 1;
}
.red-packet-item .content-section .info-content .description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}
.red-packet-item .content-section .info-content .image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5rpx;
}
.red-packet-item .content-section .info-content .image-list .content-image {
  width: calc(33.33% - 10rpx);
  height: 200rpx;
  margin: 5rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}
.red-packet-item .content-section .info-content .image-list .image-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
}
.red-packet-item .content-section .red-packet-info {
  display: flex;
  align-items: center;
  background-color: #FFF7F7;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1rpx solid #FFE0E0;
}
.red-packet-item .content-section .red-packet-info .red-packet-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}
.red-packet-item .content-section .red-packet-info .red-packet-icon image {
  width: 100%;
  height: 100%;
}
.red-packet-item .content-section .red-packet-info .red-packet-details {
  flex: 1;
}
.red-packet-item .content-section .red-packet-info .red-packet-details .packet-amount {
  font-size: 36rpx;
  color: #FF4D4F;
  font-weight: bold;
  margin-bottom: 6rpx;
}
.red-packet-item .content-section .red-packet-info .red-packet-details .packet-type {
  font-size: 24rpx;
  color: #FF7875;
}
.red-packet-item .content-section .red-packet-info .packet-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.red-packet-item .content-section .red-packet-info .packet-status.status-active {
  background-color: #FF4D4F;
  color: #fff;
}
.red-packet-item .content-section .red-packet-info .packet-status.status-finished {
  background-color: #999;
  color: #fff;
}
.red-packet-item .content-section .red-packet-info .packet-status.status-expired {
  background-color: #ccc;
  color: #fff;
}
.red-packet-item .action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}
.red-packet-item .action-bar .action-stats {
  display: flex;
}
.red-packet-item .action-bar .action-stats .stat-item {
  margin-right: 30rpx;
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}
.red-packet-item .action-bar .action-stats .stat-item .iconfont {
  margin-right: 6rpx;
  font-size: 28rpx;
}
.red-packet-item .action-bar .action-buttons .grab-btn {
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  color: #fff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  border: none;
  box-shadow: 0 4rpx 8rpx rgba(255, 77, 79, 0.2);
}
.red-packet-item .action-bar .action-buttons .grab-btn.disabled {
  background: #ccc;
  box-shadow: none;
}

/* 发布按钮 */
.publish-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 180rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 77, 79, 0.3);
  z-index: 99;
}
.publish-btn .iconfont {
  margin-right: 10rpx;
  font-size: 32rpx;
}

/* 信息红包特有样式 - 精简版 */
.info-red-packet-item {
  border-left: 4rpx solid #FF4D4F;
  padding: 20rpx;
}
.info-content-section {
  margin-bottom: 0;
}
.info-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.info-type-tag {
  font-size: 22rpx;
  color: #fff;
  background-color: #1677FF;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}
.info-red-packet-bar {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}
.info-red-packet-bar .red-packet-icon-mini {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.info-red-packet-bar .red-packet-icon-mini image {
  width: 100%;
  height: 100%;
}
.info-red-packet-bar .red-packet-amount-mini {
  flex: 1;
  font-size: 26rpx;
  color: #FF4D4F;
}
.info-red-packet-bar .grab-button-mini {
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  color: #fff;
  font-size: 24rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(255, 77, 79, 0.2);
}