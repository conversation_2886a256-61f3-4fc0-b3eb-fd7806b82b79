<view class="product-publish-container"><view class="status-bar-placeholder"></view><view class="custom-navbar"><view class="navbar-left" bindtap="{{a}}"><view class="back-button"><text class="icon-back">←</text></view></view><view class="navbar-title"><text class="title-text">发布商品</text></view><view class="navbar-right"><view class="help-button" bindtap="{{b}}"><text class="icon-help">?</text></view></view></view><scroll-view class="content-scroll" scroll-y><view class="form-section"><view class="section-header"><text class="section-title">基本信息</text><text class="section-subtitle">填写商品的基本信息</text></view><view class="image-upload-area"><view class="upload-title"><text class="required">*</text><text>商品图片</text><text class="upload-desc">最多9张，建议尺寸800x800</text></view><view class="image-grid"><view wx:for="{{c}}" wx:for-item="image" wx:key="c" class="image-item"><image src="{{image.a}}" mode="aspectFill" class="preview-image"></image><view class="delete-btn" bindtap="{{image.b}}">×</view></view><view wx:if="{{d}}" class="upload-item" bindtap="{{e}}"><text class="upload-icon">+</text><text class="upload-text">上传图片</text></view></view></view><view class="form-item"><view class="form-label"><text class="required">*</text><text>商品名称</text></view><view class="form-input-wrap"><input class="form-input" type="text" placeholder="请输入商品名称（2-40字）" maxlength="40" value="{{f}}" bindinput="{{g}}"/><text class="input-counter">{{h}}/40</text></view></view><view class="form-item"><view class="form-label"><text class="required">*</text><text>商品分类</text></view><view class="form-input-wrap selector" bindtap="{{k}}"><text class="{{['selector-text', j && 'placeholder']}}">{{i}}</text><text class="selector-arrow">›</text></view></view><view class="price-row"><view class="form-item price-item"><view class="form-label"><text class="required">*</text><text>售价</text></view><view class="form-input-wrap price-input"><text class="price-symbol">¥</text><input class="form-input" type="digit" placeholder="0.00" value="{{l}}" bindinput="{{m}}"/></view></view><view class="form-item price-item"><view class="form-label"><text>原价</text><text class="label-tip">(选填)</text></view><view class="form-input-wrap price-input"><text class="price-symbol">¥</text><input class="form-input" type="digit" placeholder="0.00" value="{{n}}" bindinput="{{o}}"/></view></view></view><view class="form-item"><view class="form-label"><text class="required">*</text><text>库存数量</text></view><view class="form-input-wrap stock-input"><view bindtap="{{p}}" class="{{['stock-btn', 'minus', q && 'disabled']}}">-</view><input class="form-input stock-value" type="number" value="{{r}}" bindinput="{{s}}"/><view class="stock-btn plus" bindtap="{{t}}">+</view></view></view></view><view class="form-section"><view class="section-header"><text class="section-title">商品详情</text><text class="section-subtitle">详细描述您的商品特点和卖点</text></view><view class="form-item"><view class="form-label"><text class="required">*</text><text>商品描述</text></view><view class="form-input-wrap textarea-wrap"><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请详细描述商品的特点、用途、材质等信息" maxlength="500" value="{{v}}" bindinput="{{w}}"></textarea></block><text class="input-counter textarea-counter">{{x}}/500</text></view></view><view class="form-item"><view class="form-label specs-header"><view class="specs-title"><text>规格参数</text><text class="label-tip">(选填)</text></view><view class="add-spec-btn" bindtap="{{y}}"><text class="add-spec-icon">+</text><text class="add-spec-text">添加规格</text></view></view><view class="specs-list"><view wx:for="{{z}}" wx:for-item="spec" wx:key="f" class="spec-item"><view class="spec-inputs"><input class="spec-key" type="text" placeholder="规格名称" value="{{spec.a}}" bindinput="{{spec.b}}"/><text class="spec-separator">:</text><input class="spec-value" type="text" placeholder="规格值" value="{{spec.c}}" bindinput="{{spec.d}}"/></view><view class="delete-spec-btn" bindtap="{{spec.e}}">×</view></view><view wx:if="{{A}}" class="empty-specs"><text>点击"添加规格"按钮添加商品规格参数</text></view></view></view></view><view class="form-section"><view class="section-header"><text class="section-title">营销设置</text><text class="section-subtitle">设置商品的营销相关信息</text></view><view class="form-item"><view class="form-label"><text>营销标签</text><text class="label-tip">(选填)</text></view><view class="tags-container"><view wx:for="{{B}}" wx:for-item="tag" wx:key="b" class="{{['tag-item', tag.c && 'active']}}" bindtap="{{tag.d}}"><text>{{tag.a}}</text></view></view></view><view class="form-item"><view class="form-label"><text>限购数量</text><text class="label-tip">(选填，0表示不限购)</text></view><view class="form-input-wrap"><input class="form-input" type="number" placeholder="0" value="{{C}}" bindinput="{{D}}"/></view></view></view></scroll-view><view class="bottom-action-bar"><button class="action-button save-draft" bindtap="{{E}}">保存草稿</button><button class="action-button publish" bindtap="{{F}}">立即发布</button></view></view>