@echo off
chcp 65001 >nul

echo.
echo ========================================
echo   磁州生活网 - 超简单启动（小白专用）
echo ========================================
echo.

echo 🔍 正在检查环境...

REM 检查 Node.js
where node >nul 2>&1
if errorlevel 1 (
    echo ❌ 还没有安装 Node.js
    echo.
    echo 🔧 请先运行：超简单安装.bat
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
node --version

echo.
echo 🚀 正在启动系统...
echo.

REM 进入前端目录
cd frontend

REM 检查是否需要安装依赖
if not exist "node_modules" (
    echo 📦 首次运行，正在安装必要文件...
    echo    这可能需要几分钟，请耐心等待...
    echo.
    
    REM 设置国内镜像源，提高下载速度
    npm config set registry https://registry.npmmirror.com/
    
    echo 🔄 正在下载文件...
    npm install
    
    if errorlevel 1 (
        echo.
        echo ❌ 文件下载失败
        echo 🔧 尝试解决方案：
        echo    1. 检查网络连接
        echo    2. 重新运行这个脚本
        echo    3. 或者重启电脑后重试
        echo.
        pause
        exit /b 1
    )
    
    echo ✅ 文件下载完成
)

echo.
echo ========================================
echo 🎉 启动成功！
echo ========================================
echo.

echo 📱 网址：http://localhost:3000
echo 🔑 账号：admin
echo 🔑 密码：admin123
echo.

echo 💡 使用说明：
echo    1. 系统启动后会自动打开浏览器
echo    2. 如果没有自动打开，请手动访问：http://localhost:3000
echo    3. 使用上面的账号密码登录
echo    4. 这是演示版本，可以查看所有界面功能
echo.

echo 🛑 停止系统：按 Ctrl+C
echo.

echo ========================================
echo 正在启动...
echo ========================================
echo.

REM 启动开发服务器
npm run dev

echo.
echo 👋 系统已停止运行
pause
