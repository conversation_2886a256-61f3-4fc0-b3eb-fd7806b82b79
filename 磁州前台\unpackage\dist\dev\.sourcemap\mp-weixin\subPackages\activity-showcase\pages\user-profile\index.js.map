{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/user-profile/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcdXNlci1wcm9maWxlXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"profile-container\">\r\n    <!-- 头部背景 -->\r\n    <view class=\"profile-header\" :style=\"{\r\n      height: '300rpx',\r\n      background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\r\n      position: 'relative',\r\n      overflow: 'hidden'\r\n    }\">\r\n      <!-- 背景装饰 -->\r\n      <view class=\"bg-decoration\" :style=\"{\r\n        position: 'absolute',\r\n        top: '-50rpx',\r\n        right: '-50rpx',\r\n        width: '300rpx',\r\n        height: '300rpx',\r\n        borderRadius: '50%',\r\n        background: 'rgba(255,255,255,0.1)',\r\n        zIndex: '1'\r\n      }\"></view>\r\n      <view class=\"bg-decoration\" :style=\"{\r\n        position: 'absolute',\r\n        bottom: '-80rpx',\r\n        left: '-80rpx',\r\n        width: '250rpx',\r\n        height: '250rpx',\r\n        borderRadius: '50%',\r\n        background: 'rgba(255,255,255,0.08)',\r\n        zIndex: '1'\r\n      }\"></view>\r\n      \r\n      <!-- 导航栏 -->\r\n      <view class=\"navbar\" :style=\"{\r\n        position: 'absolute',\r\n        top: '70rpx', /* 从60rpx增加到70rpx，向下移动10单位 */\r\n        left: '0',\r\n        right: '0',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'space-between',\r\n        padding: '0 30rpx',\r\n        zIndex: '10'\r\n      }\">\r\n        <!-- 返回按钮 -->\r\n        <view class=\"back-btn\" @click=\"goBack\" :style=\"{\r\n          width: '70rpx',\r\n          height: '70rpx',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center'\r\n        }\">\r\n          <image src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\" :style=\"{\r\n            width: '40rpx',\r\n            height: '40rpx'\r\n          }\"></image>\r\n        </view>\r\n        \r\n        <!-- 标题 -->\r\n        <text class=\"header-title\" :style=\"{\r\n          color: '#FFFFFF',\r\n          fontSize: '36rpx',\r\n          fontWeight: '600'\r\n        }\">个人资料</text>\r\n        \r\n        <!-- 保存按钮 -->\r\n        <view class=\"save-btn\" @click=\"saveProfile\" :style=\"{\r\n          padding: '10rpx 30rpx',\r\n          borderRadius: '30rpx',\r\n          background: 'rgba(255,255,255,0.2)',\r\n          color: '#FFFFFF',\r\n          fontSize: '28rpx',\r\n          fontWeight: '500'\r\n        }\">\r\n          保存\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 头像区域 -->\r\n    <view class=\"avatar-section\" :style=\"{\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      alignItems: 'center',\r\n      marginTop: '-100rpx',\r\n      position: 'relative',\r\n      zIndex: '20'\r\n    }\">\r\n      <view class=\"avatar-wrapper\" @click=\"changeAvatar\" :style=\"{\r\n        width: '180rpx',\r\n        height: '180rpx',\r\n        borderRadius: '50%',\r\n        background: '#FFFFFF',\r\n        padding: '6rpx',\r\n        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',\r\n        position: 'relative'\r\n      }\">\r\n        <image \r\n          :src=\"userInfo.avatar\" \r\n          mode=\"aspectFill\" \r\n          :style=\"{\r\n            width: '100%',\r\n            height: '100%',\r\n            borderRadius: '50%'\r\n          }\"\r\n        ></image>\r\n        \r\n        <view class=\"edit-icon\" :style=\"{\r\n          position: 'absolute',\r\n          bottom: '10rpx',\r\n          right: '10rpx',\r\n          width: '50rpx',\r\n          height: '50rpx',\r\n          borderRadius: '50%',\r\n          background: '#FF3B69',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          boxShadow: '0 2px 5px rgba(255,59,105,0.3)',\r\n          border: '2rpx solid #FFFFFF'\r\n        }\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n            <path d=\"M17 3a2.828 2.828 0 114 4L7.5 20.5 2 22l1.5-5.5L17 3z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"vip-badge\" v-if=\"userInfo.isVip\" :style=\"{\r\n        marginTop: '20rpx',\r\n        padding: '6rpx 20rpx',\r\n        background: 'linear-gradient(90deg, #FFD700 0%, #FFC107 100%)',\r\n        borderRadius: '20rpx',\r\n        color: '#8B4513',\r\n        fontSize: '24rpx',\r\n        fontWeight: 'bold',\r\n        boxShadow: '0 2px 5px rgba(255,215,0,0.3)'\r\n      }\">\r\n        VIP会员\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 表单内容 -->\r\n    <view class=\"profile-form\" :style=\"{\r\n      padding: '30rpx',\r\n      marginTop: '30rpx'\r\n    }\">\r\n      <!-- 基本信息卡片 -->\r\n      <view class=\"info-card\" :style=\"{\r\n        background: '#FFFFFF',\r\n        borderRadius: '35px',\r\n        padding: '30rpx',\r\n        boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"card-header\" :style=\"{\r\n          marginBottom: '30rpx'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '32rpx',\r\n            fontWeight: '600',\r\n            color: '#333333'\r\n          }\">基本信息</text>\r\n        </view>\r\n        \r\n        <!-- 昵称 -->\r\n        <view class=\"form-item\" :style=\"{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0',\r\n          borderBottom: '1rpx solid #EFEFEF'\r\n        }\">\r\n          <text class=\"form-label\" :style=\"{\r\n            width: '160rpx',\r\n            fontSize: '28rpx',\r\n            color: '#666666'\r\n          }\">昵称</text>\r\n          <input \r\n            type=\"text\" \r\n            v-model=\"userInfo.nickname\" \r\n            placeholder=\"请输入昵称\" \r\n            :style=\"{\r\n              flex: '1',\r\n              fontSize: '28rpx',\r\n              color: '#333333'\r\n            }\"\r\n          />\r\n        </view>\r\n        \r\n        <!-- 性别 -->\r\n        <view class=\"form-item\" :style=\"{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0',\r\n          borderBottom: '1rpx solid #EFEFEF'\r\n        }\">\r\n          <text class=\"form-label\" :style=\"{\r\n            width: '160rpx',\r\n            fontSize: '28rpx',\r\n            color: '#666666'\r\n          }\">性别</text>\r\n          <view class=\"gender-options\" :style=\"{\r\n            flex: '1',\r\n            display: 'flex'\r\n          }\">\r\n            <view \r\n              class=\"gender-option\" \r\n              :class=\"{ active: userInfo.gender === 1 }\"\r\n              @click=\"userInfo.gender = 1\"\r\n              :style=\"{\r\n                padding: '10rpx 30rpx',\r\n                borderRadius: '30rpx',\r\n                marginRight: '20rpx',\r\n                background: userInfo.gender === 1 ? 'rgba(255,59,105,0.1)' : '#F8F8F8',\r\n                color: userInfo.gender === 1 ? '#FF3B69' : '#666666',\r\n                fontSize: '26rpx',\r\n                border: userInfo.gender === 1 ? '1rpx solid #FF3B69' : '1rpx solid transparent'\r\n              }\"\r\n            >\r\n              男\r\n            </view>\r\n            <view \r\n              class=\"gender-option\" \r\n              :class=\"{ active: userInfo.gender === 2 }\"\r\n              @click=\"userInfo.gender = 2\"\r\n              :style=\"{\r\n                padding: '10rpx 30rpx',\r\n                borderRadius: '30rpx',\r\n                background: userInfo.gender === 2 ? 'rgba(255,59,105,0.1)' : '#F8F8F8',\r\n                color: userInfo.gender === 2 ? '#FF3B69' : '#666666',\r\n                fontSize: '26rpx',\r\n                border: userInfo.gender === 2 ? '1rpx solid #FF3B69' : '1rpx solid transparent'\r\n              }\"\r\n            >\r\n              女\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 生日 -->\r\n        <view class=\"form-item\" :style=\"{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0',\r\n          borderBottom: '1rpx solid #EFEFEF'\r\n        }\">\r\n          <text class=\"form-label\" :style=\"{\r\n            width: '160rpx',\r\n            fontSize: '28rpx',\r\n            color: '#666666'\r\n          }\">生日</text>\r\n          <view class=\"date-picker\" @click=\"showDatePicker\" :style=\"{\r\n            flex: '1',\r\n            display: 'flex',\r\n            justifyContent: 'space-between',\r\n            alignItems: 'center'\r\n          }\">\r\n            <text :style=\"{\r\n              fontSize: '28rpx',\r\n              color: userInfo.birthday ? '#333333' : '#999999'\r\n            }\">{{ userInfo.birthday || '请选择生日' }}</text>\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path d=\"M6 9l6 6 6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 手机号 -->\r\n        <view class=\"form-item\" :style=\"{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0',\r\n          borderBottom: '1rpx solid #EFEFEF'\r\n        }\">\r\n          <text class=\"form-label\" :style=\"{\r\n            width: '160rpx',\r\n            fontSize: '28rpx',\r\n            color: '#666666'\r\n          }\">手机号</text>\r\n          <text v-if=\"userInfo.phone\" :style=\"{\r\n            flex: '1',\r\n            fontSize: '28rpx',\r\n            color: '#333333'\r\n          }\">{{ formatPhone(userInfo.phone) }}</text>\r\n          <view \r\n            v-else \r\n            class=\"bind-btn\" \r\n            @click=\"bindPhone\"\r\n            :style=\"{\r\n              padding: '8rpx 20rpx',\r\n              borderRadius: '30rpx',\r\n              background: 'rgba(255,59,105,0.1)',\r\n              color: '#FF3B69',\r\n              fontSize: '24rpx',\r\n              fontWeight: '500'\r\n            }\"\r\n          >\r\n            绑定手机号\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 所在地区 -->\r\n        <view class=\"form-item\" :style=\"{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0'\r\n        }\">\r\n          <text class=\"form-label\" :style=\"{\r\n            width: '160rpx',\r\n            fontSize: '28rpx',\r\n            color: '#666666'\r\n          }\">所在地区</text>\r\n          <view class=\"region-picker\" @click=\"showRegionPicker\" :style=\"{\r\n            flex: '1',\r\n            display: 'flex',\r\n            justifyContent: 'space-between',\r\n            alignItems: 'center'\r\n          }\">\r\n            <text :style=\"{\r\n              fontSize: '28rpx',\r\n              color: userInfo.region ? '#333333' : '#999999'\r\n            }\">{{ userInfo.region || '请选择地区' }}</text>\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path d=\"M6 9l6 6 6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 账号安全卡片 -->\r\n      <view class=\"security-card\" :style=\"{\r\n        background: '#FFFFFF',\r\n        borderRadius: '35px',\r\n        padding: '30rpx',\r\n        boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"card-header\" :style=\"{\r\n          marginBottom: '30rpx'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '32rpx',\r\n            fontWeight: '600',\r\n            color: '#333333'\r\n          }\">账号安全</text>\r\n        </view>\r\n        \r\n        <!-- 修改密码 -->\r\n        <view class=\"security-item\" @click=\"changePassword\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0',\r\n          borderBottom: '1rpx solid #EFEFEF'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '28rpx',\r\n            color: '#333333'\r\n          }\">修改密码</text>\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n        \r\n        <!-- 实名认证 -->\r\n        <view class=\"security-item\" @click=\"verifyIdentity\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0',\r\n          borderBottom: '1rpx solid #EFEFEF'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '28rpx',\r\n            color: '#333333'\r\n          }\">实名认证</text>\r\n          <view class=\"security-status\" :style=\"{\r\n            display: 'flex',\r\n            alignItems: 'center'\r\n          }\">\r\n            <text :style=\"{\r\n              fontSize: '26rpx',\r\n              color: userInfo.isVerified ? '#34C759' : '#999999',\r\n              marginRight: '10rpx'\r\n            }\">{{ userInfo.isVerified ? '已认证' : '未认证' }}</text>\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 绑定微信 -->\r\n        <view class=\"security-item\" @click=\"bindWeChat\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0',\r\n          borderBottom: '1rpx solid #EFEFEF'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '28rpx',\r\n            color: '#333333'\r\n          }\">绑定微信</text>\r\n          <view class=\"security-status\" :style=\"{\r\n            display: 'flex',\r\n            alignItems: 'center'\r\n          }\">\r\n            <text :style=\"{\r\n              fontSize: '26rpx',\r\n              color: userInfo.isWeChatBound ? '#34C759' : '#999999',\r\n              marginRight: '10rpx'\r\n            }\">{{ userInfo.isWeChatBound ? '已绑定' : '未绑定' }}</text>\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n              <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 注销账号 -->\r\n        <view class=\"security-item\" @click=\"deleteAccount\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '28rpx',\r\n            color: '#FF3B30'\r\n          }\">注销账号</text>\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 隐私设置卡片 -->\r\n      <view class=\"privacy-card\" :style=\"{\r\n        background: '#FFFFFF',\r\n        borderRadius: '35px',\r\n        padding: '30rpx',\r\n        boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"card-header\" :style=\"{\r\n          marginBottom: '30rpx'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '32rpx',\r\n            fontWeight: '600',\r\n            color: '#333333'\r\n          }\">隐私设置</text>\r\n        </view>\r\n        \r\n        <!-- 允许陌生人查看资料 -->\r\n        <view class=\"privacy-item\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0',\r\n          borderBottom: '1rpx solid #EFEFEF'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '28rpx',\r\n            color: '#333333'\r\n          }\">允许陌生人查看资料</text>\r\n          <switch \r\n            :checked=\"privacySettings.allowProfileView\" \r\n            @change=\"e => privacySettings.allowProfileView = e.detail.value\"\r\n            color=\"#FF3B69\"\r\n          />\r\n        </view>\r\n        \r\n        <!-- 显示我的活动记录 -->\r\n        <view class=\"privacy-item\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0',\r\n          borderBottom: '1rpx solid #EFEFEF'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '28rpx',\r\n            color: '#333333'\r\n          }\">显示我的活动记录</text>\r\n          <switch \r\n            :checked=\"privacySettings.showActivityHistory\" \r\n            @change=\"e => privacySettings.showActivityHistory = e.detail.value\"\r\n            color=\"#FF3B69\"\r\n          />\r\n        </view>\r\n        \r\n        <!-- 接收活动推送 -->\r\n        <view class=\"privacy-item\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          padding: '20rpx 0'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '28rpx',\r\n            color: '#333333'\r\n          }\">接收活动推送</text>\r\n          <switch \r\n            :checked=\"privacySettings.receiveActivityPush\" \r\n            @change=\"e => privacySettings.receiveActivityPush = e.detail.value\"\r\n            color=\"#FF3B69\"\r\n          />\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部安全区域 -->\r\n    <view class=\"safe-area-bottom\" :style=\"{\r\n      height: '100rpx',\r\n      paddingBottom: 'env(safe-area-inset-bottom)'\r\n    }\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\n\r\n// 用户信息\r\nconst userInfo = ref({\r\n  avatar: 'https://via.placeholder.com/180',\r\n  nickname: '张三',\r\n  gender: 1, // 1: 男, 2: 女\r\n  birthday: '1990-01-01',\r\n  phone: '13800138000',\r\n  region: '北京市 海淀区',\r\n  isVip: true,\r\n  isVerified: false,\r\n  isWeChatBound: true\r\n});\r\n\r\n// 隐私设置\r\nconst privacySettings = ref({\r\n  allowProfileView: true,\r\n  showActivityHistory: true,\r\n  receiveActivityPush: true\r\n});\r\n\r\n// 返回上一页\r\nfunction goBack() {\r\n  uni.navigateBack();\r\n}\r\n\r\n// 保存个人资料\r\nfunction saveProfile() {\r\n  uni.showLoading({\r\n    title: '保存中...'\r\n  });\r\n  \r\n  // 模拟保存过程\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    uni.showToast({\r\n      title: '保存成功',\r\n      icon: 'success'\r\n    });\r\n    \r\n    // 保存成功后返回上一页\r\n    setTimeout(() => {\r\n      uni.navigateBack();\r\n    }, 1500);\r\n  }, 1500);\r\n}\r\n\r\n// 更换头像\r\nfunction changeAvatar() {\r\n  uni.chooseImage({\r\n    count: 1,\r\n    sizeType: ['compressed'],\r\n    sourceType: ['album', 'camera'],\r\n    success: (res) => {\r\n      const tempFilePaths = res.tempFilePaths;\r\n      \r\n      // 这里可以添加上传头像的逻辑\r\n      // 模拟上传成功\r\n      userInfo.value.avatar = tempFilePaths[0];\r\n      \r\n      uni.showToast({\r\n        title: '头像已更新',\r\n        icon: 'success'\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n// 显示日期选择器\r\nfunction showDatePicker() {\r\n  uni.showToast({\r\n    title: '日期选择功能开发中',\r\n    icon: 'none'\r\n  });\r\n  \r\n  // 实际开发中应该使用日期选择器组件\r\n  // uni.showDatePicker({\r\n  //   success: (res) => {\r\n  //     userInfo.value.birthday = res.date;\r\n  //   }\r\n  // });\r\n}\r\n\r\n// 显示地区选择器\r\nfunction showRegionPicker() {\r\n  uni.showToast({\r\n    title: '地区选择功能开发中',\r\n    icon: 'none'\r\n  });\r\n  \r\n  // 实际开发中应该使用地区选择器组件\r\n  // uni.showRegionPicker({\r\n  //   success: (res) => {\r\n  //     userInfo.value.region = res.region;\r\n  //   }\r\n  // });\r\n}\r\n\r\n// 绑定手机号\r\nfunction bindPhone() {\r\n  uni.showToast({\r\n    title: '绑定手机号功能开发中',\r\n    icon: 'none'\r\n  });\r\n}\r\n\r\n// 修改密码\r\nfunction changePassword() {\r\n  uni.showToast({\r\n    title: '修改密码功能开发中',\r\n    icon: 'none'\r\n  });\r\n}\r\n\r\n// 实名认证\r\nfunction verifyIdentity() {\r\n  uni.showToast({\r\n    title: '实名认证功能开发中',\r\n    icon: 'none'\r\n  });\r\n}\r\n\r\n// 绑定微信\r\nfunction bindWeChat() {\r\n  uni.showToast({\r\n    title: '绑定微信功能开发中',\r\n    icon: 'none'\r\n  });\r\n}\r\n\r\n// 注销账号\r\nfunction deleteAccount() {\r\n  uni.showModal({\r\n    title: '注销账号',\r\n    content: '注销账号后，您的所有数据将被清除且无法恢复，确定要注销吗？',\r\n    confirmColor: '#FF3B30',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        uni.showToast({\r\n          title: '注销功能开发中',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\n// 格式化手机号\r\nfunction formatPhone(phone) {\r\n  if (!phone) return '';\r\n  return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.profile-container {\r\n  min-height: 100vh;\r\n  background-color: #F8F8F8;\r\n}\r\n\r\n.back-btn:active, .save-btn:active, .gender-option:active, .security-item:active {\r\n  opacity: 0.8;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/user-profile/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni"], "mappings": ";;;;;;;;;;;AAygBA,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,IACvB,CAAC;AAGD,aAAS,SAAS;AAChBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,aAAS,cAAc;AACrBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,IAAI;AAAA,IACT;AAGA,aAAS,eAAe;AACtBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,gBAAM,gBAAgB,IAAI;AAI1B,mBAAS,MAAM,SAAS,cAAc,CAAC;AAEvCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAS,iBAAiB;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IAQH;AAGA,aAAS,mBAAmB;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IAQH;AAGA,aAAS,YAAY;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,iBAAiB;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,iBAAiB;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,aAAa;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,gBAAgB;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAS,YAAY,OAAO;AAC1B,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,MAAM,QAAQ,uBAAuB,UAAU;AAAA,IACxD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9pBA,GAAG,WAAW,eAAe;"}