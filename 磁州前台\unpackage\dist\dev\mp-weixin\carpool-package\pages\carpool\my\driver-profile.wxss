/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 容器样式 */
.driver-profile-container {
  background-color: #F5F5F5;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
}
.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}
.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 24px;
  height: 24px;
}
.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}
.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 司机卡片 */
.driver-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.driver-info-section {
  display: flex;
  margin-bottom: 24rpx;
}
.driver-avatar-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
}
.driver-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2rpx solid #F2F2F7;
}
.verified-badge {
  position: absolute;
  right: -6rpx;
  bottom: -6rpx;
  width: 36rpx;
  height: 36rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
.verified-icon {
  width: 28rpx;
  height: 28rpx;
}
.driver-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.driver-name-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.driver-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 12rpx;
}
.driver-level {
  display: flex;
  align-items: center;
  background-color: #FFF9E6;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.level-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}
.level-text {
  font-size: 22rpx;
  color: #FF9F0A;
}
.driver-id {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}
.driver-stats {
  display: flex;
  align-items: center;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1;
}
.stat-label {
  font-size: 22rpx;
  color: #8E8E93;
  margin-top: 4rpx;
}
.stat-divider {
  width: 1px;
  height: 30rpx;
  background-color: #DDDDDD;
  margin: 0 24rpx;
}
.driver-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}
.tag-item {
  background-color: #F2F2F7;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.tag-text {
  font-size: 24rpx;
  color: #666666;
}
.driver-intro {
  border-top: 1px solid #F2F2F7;
  padding-top: 20rpx;
}
.intro-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}
.intro-content {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
}

/* 通用卡片样式 */
.section-card {
  margin: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-title-wrap {
  display: flex;
  align-items: center;
}
.section-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}
.section-more {
  display: flex;
  align-items: center;
}
.more-text {
  font-size: 26rpx;
  color: #8E8E93;
  margin-right: 4rpx;
}
.more-icon {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 车辆信息样式 */
.vehicle-info {
  display: flex;
  align-items: center;
}
.vehicle-image-container {
  width: 180rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}
.vehicle-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.vehicle-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.vehicle-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6rpx;
}
.vehicle-plate {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 6rpx;
}
.vehicle-desc {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 服务统计样式 */
.service-stats {
  display: flex;
  flex-direction: column;
}
.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #F2F2F7;
}
.stat-row:last-child {
  border-bottom: none;
}
.stat-label {
  font-size: 28rpx;
  color: #666666;
}
.stat-value-large {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

/* 服务区域样式 */
.service-areas {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.area-item {
  display: flex;
  align-items: center;
  background-color: #F2F2F7;
  padding: 10rpx 16rpx;
  border-radius: 20rpx;
}
.area-icon-wrap {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.area-icon {
  width: 100%;
  height: 100%;
}
.area-name {
  font-size: 26rpx;
  color: #666666;
}

/* 底部联系按钮 */
.contact-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.contact-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
}
.primary {
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  color: #FFFFFF;
}
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}