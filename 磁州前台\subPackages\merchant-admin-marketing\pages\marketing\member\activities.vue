<template>
  <view class="activities-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">会员活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 活动概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">活动概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{activityData.totalActivities}}</view>
          <view class="card-label">总活动数</view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{activityData.runningActivities}}</view>
          <view class="card-label">进行中活动</view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{activityData.participationCount}}</view>
          <view class="card-label">参与人次</view>
          <view class="card-trend" :class="activityData.participationTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{activityData.participationGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">¥{{activityData.revenue}}</view>
          <view class="card-label">活动收入</view>
          <view class="card-trend" :class="activityData.revenueTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{activityData.revenueGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 活动分类标签 -->
    <view class="tabs-section">
      <scroll-view scroll-x class="tabs-scroll" show-scrollbar="false">
        <view class="tabs">
          <view 
            class="tab-item" 
            v-for="(tab, index) in tabs" 
            :key="index"
            :class="{ active: currentTab === index }"
            @click="switchTab(index)"
          >
            {{tab}}
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 活动列表 -->
    <view class="activities-section">
      <view class="section-header">
        <text class="section-title">{{tabs[currentTab]}}</text>
        <view class="add-btn" @click="createActivity">
          <text class="btn-text">添加活动</text>
          <view class="plus-icon"></view>
        </view>
      </view>
      
      <view class="activities-list">
        <view class="activity-item" v-for="(activity, index) in filteredActivities" :key="index" @click="viewActivityDetail(activity)">
          <image class="activity-image" :src="activity.image" mode="aspectFill"></image>
          <view class="activity-overlay" :class="activity.status"></view>
          <view class="activity-status-badge" :class="activity.status">{{activity.statusText}}</view>
          <view class="activity-content">
            <view class="activity-name">{{activity.name}}</view>
            <view class="activity-period">{{activity.period}}</view>
            <view class="activity-meta">
              <view class="meta-item">
                <svg class="svg-icon" viewBox="0 0 24 24" fill="#43E97B">
                  <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                </svg>
                <text>{{activity.participantCount}}人参与</text>
              </view>
              <view class="meta-item">
                <svg class="svg-icon" viewBox="0 0 24 24" fill="#38F9D7">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"/>
                </svg>
                <text>{{activity.memberLevelText}}</text>
              </view>
            </view>
          </view>
          <view class="activity-actions">
            <view class="action-btn stats" @click.stop="viewStats(activity)">
              <svg class="svg-icon" viewBox="0 0 24 24" fill="#43E97B">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
              </svg>
              <text>统计</text>
            </view>
            <view class="action-btn share" @click.stop="shareActivity(activity)">
              <svg class="svg-icon" viewBox="0 0 24 24" fill="#38F9D7">
                <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/>
              </svg>
              <text>分享</text>
            </view>
            <view class="action-btn toggle">
              <switch :checked="activity.enabled" @change="(e) => toggleActivity(activity, e)" color="#43E97B" />
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="createActivity">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      currentTab: 0,
      
      // 活动数据概览
      activityData: {
        totalActivities: 8,
        runningActivities: 5,
        participationCount: 1256,
        participationTrend: 'up',
        participationGrowth: '18.5%',
        revenue: '28560.50',
        revenueTrend: 'up',
        revenueGrowth: '12.3%'
      },
      
      // 活动分类标签
      tabs: ['全部活动', '会员专享', '积分活动', '抽奖活动', '限时活动'],
      
      // 活动列表
      activities: [
        {
          id: 1,
          name: '会员专享品鉴会',
          period: '2023-04-15 ~ 2023-04-20',
          image: '/static/images/activity-1.jpg',
          participantCount: 128,
          memberLevel: ['silver', 'gold', 'diamond'],
          memberLevelText: '银卡及以上',
          category: '会员专享',
          status: 'active',
          statusText: '进行中',
          enabled: true
        },
        {
          id: 2,
          name: '积分翻倍周',
          period: '2023-04-10 ~ 2023-04-17',
          image: '/static/images/activity-2.jpg',
          participantCount: 356,
          memberLevel: ['all'],
          memberLevelText: '全部会员',
          category: '积分活动',
          status: 'active',
          statusText: '进行中',
          enabled: true
        },
        {
          id: 3,
          name: '幸运大抽奖',
          period: '2023-04-20 ~ 2023-04-30',
          image: '/static/images/activity-3.jpg',
          participantCount: 215,
          memberLevel: ['all'],
          memberLevelText: '全部会员',
          category: '抽奖活动',
          status: 'upcoming',
          statusText: '即将开始',
          enabled: true
        },
        {
          id: 4,
          name: '限时秒杀专场',
          period: '2023-04-05 ~ 2023-04-08',
          image: '/static/images/activity-4.jpg',
          participantCount: 432,
          memberLevel: ['all'],
          memberLevelText: '全部会员',
          category: '限时活动',
          status: 'ended',
          statusText: '已结束',
          enabled: false
        },
        {
          id: 5,
          name: '钻石会员专享日',
          period: '2023-04-28',
          image: '/static/images/activity-5.jpg',
          participantCount: 68,
          memberLevel: ['diamond'],
          memberLevelText: '钻石会员',
          category: '会员专享',
          status: 'upcoming',
          statusText: '即将开始',
          enabled: true
        },
        {
          id: 6,
          name: '积分兑换特惠',
          period: '2023-04-01 ~ 2023-04-07',
          image: '/static/images/activity-6.jpg',
          participantCount: 187,
          memberLevel: ['all'],
          memberLevelText: '全部会员',
          category: '积分活动',
          status: 'ended',
          statusText: '已结束',
          enabled: false
        }
      ]
    }
  },
  computed: {
    filteredActivities() {
      if (this.currentTab === 0) {
        return this.activities;
      } else {
        const category = this.tabs[this.currentTab];
        return this.activities.filter(activity => activity.category === category);
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showHelp() {
      uni.showModal({
        title: '会员活动帮助',
        content: '会员活动是指针对会员开展的各类营销活动，包括会员专享活动、积分活动、抽奖活动等，可以提高会员活跃度和忠诚度。',
        showCancel: false
      });
    },
    
    showDatePicker() {
      // 实现日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    
    switchTab(index) {
      this.currentTab = index;
    },
    
    createActivity() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/member/create-activity'
      });
    },
    
    viewActivityDetail(activity) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/activity-detail?id=${activity.id}`
      });
    },
    
    viewStats(activity) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/activity-stats?id=${activity.id}`
      });
    },
    
    shareActivity(activity) {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    },
    
    toggleActivity(activity, e) {
      // 更新活动状态
      const index = this.activities.findIndex(item => item.id === activity.id);
      if (index !== -1) {
        this.activities[index].enabled = e.detail.value;
      }
      
      uni.showToast({
        title: e.detail.value ? `${activity.name}已启用` : `${activity.name}已禁用`,
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.activities-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #43E97B, #38F9D7);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(67, 233, 123, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  background: #43E97B;
  border-radius: 15px;
  padding: 5px 12px;
  color: white;
}

.btn-text {
  font-size: 13px;
  margin-right: 5px;
}

.plus-icon {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon:before,
.plus-icon:after {
  content: '';
  position: absolute;
  background: white;
}

.plus-icon:before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon:after {
  height: 12px;
  width: 2px;
  left: 5px;
  top: 0;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #43E97B;
}

.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}

.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}

.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 标签页样式 */
.tabs-section {
  margin: 15px 15px 0;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs {
  display: inline-flex;
  padding: 5px 0;
}

.tab-item {
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.tab-item.active {
  background: #43E97B;
  color: white;
  box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);
}

/* 活动列表样式 */
.activities-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.activities-list {
  margin-top: 10px;
}

.activity-item {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.activity-image {
  width: 100%;
  height: 150px;
  position: relative;
}

.activity-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 150px;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));
}

.activity-overlay.ended {
  background: linear-gradient(to bottom, rgba(0,0,0,0.4), rgba(0,0,0,0.7));
}

.activity-status-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.activity-status-badge.active {
  background: #43E97B;
}

.activity-status-badge.upcoming {
  background: #007AFF;
}

.activity-status-badge.ended {
  background: #8E8E93;
}

.activity-content {
  padding: 15px;
  background: white;
}

.activity-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.activity-period {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-item text {
  font-size: 12px;
  color: #666;
  margin-left: 5px;
}

.svg-icon {
  width: 16px;
  height: 16px;
}

.activity-actions {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  background: #F8FAFC;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-btn text {
  font-size: 12px;
  color: #666;
  margin-top: 3px;
}

.action-btn.toggle {
  display: flex;
  align-items: center;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #43E97B, #38F9D7);
  box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .stats-card {
    width: 100%;
  }
}
</style> 