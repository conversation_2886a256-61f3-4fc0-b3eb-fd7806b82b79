<template>
  <view v-if="visible" class="simple-popup-mask" @click="close">
    <view class="simple-popup-content" :class="type" @click.stop>
      <slot></slot>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

// --- Props ---
const props = defineProps({
  type: {
    type: String,
    default: 'center'
  }
});

// --- 响应式状态 ---
const visible = ref(false);

// --- 方法 ---
const open = () => {
  visible.value = true;
};

const close = () => {
  visible.value = false;
};

// --- Expose ---
// 暴露方法给父组件调用
defineExpose({
  open,
  close
});
</script>

<style>
.simple-popup-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.simple-popup-content {
  background-color: transparent;
  max-width: 80%;
  max-height: 80%;
  overflow: hidden;
}

.simple-popup-content.bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 100%;
}

.simple-popup-content.center {
  border-radius: 12rpx;
}
</style> 