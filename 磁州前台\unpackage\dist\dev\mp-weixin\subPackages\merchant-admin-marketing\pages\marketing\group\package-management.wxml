<view class="package-management-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">团购套餐管理</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><scroll-view scroll-y class="page-content"><view class="overview-section"><view class="section-header"><text class="section-title">套餐数据概览</text><view class="date-picker" bindtap="{{d}}"><text class="date-text">{{c}}</text><view class="date-icon"></view></view></view><view class="stats-cards"><view class="stats-card"><view class="card-value">{{e}}</view><view class="card-label">套餐总数</view><view class="{{['card-trend', g]}}"><view class="trend-arrow"></view><text class="trend-value">{{f}}</text></view></view><view class="stats-card"><view class="card-value">{{h}}</view><view class="card-label">销售数量</view><view class="{{['card-trend', j]}}"><view class="trend-arrow"></view><text class="trend-value">{{i}}</text></view></view><view class="stats-card"><view class="card-value">¥{{k}}</view><view class="card-label">套餐收入</view><view class="{{['card-trend', m]}}"><view class="trend-arrow"></view><text class="trend-value">{{l}}</text></view></view><view class="stats-card"><view class="card-value">{{n}}%</view><view class="card-label">转化率</view><view class="{{['card-trend', p]}}"><view class="trend-arrow"></view><text class="trend-value">{{o}}</text></view></view></view></view><view class="packages-section"><view class="section-header"><text class="section-title">套餐列表</text><view class="filter-dropdown" bindtap="{{r}}"><text class="filter-text">{{q}}</text><view class="filter-icon"></view></view></view><view class="packages-list"><view wx:for="{{s}}" wx:for-item="packageItem" wx:key="o" class="package-item" bindtap="{{packageItem.p}}"><view class="package-header"><view class="package-title-row"><text class="package-name">{{packageItem.a}}</text><view class="{{['package-status', packageItem.c]}}">{{packageItem.b}}</view></view><text class="package-desc">{{packageItem.d}}</text></view><view class="package-content"><view class="package-items"><text class="package-items-text">{{packageItem.e}}</text></view><view class="package-price-info"><view class="price-row"><text class="price-label">原价:</text><text class="price-original">¥{{packageItem.f}}</text></view><view class="price-row"><text class="price-label">拼团价:</text><text class="price-group">¥{{packageItem.g}}</text></view><view class="price-row"><text class="price-label">节省:</text><text class="price-save">¥{{packageItem.h}}</text></view></view></view><view class="package-footer"><view class="package-stats"><view class="stat-item"><text class="stat-value">{{packageItem.i}}</text><text class="stat-label">销量</text></view><view class="stat-item"><text class="stat-value">{{packageItem.j}}</text><text class="stat-label">浏览</text></view><view class="stat-item"><text class="stat-value">{{packageItem.k}}%</text><text class="stat-label">转化率</text></view></view><view class="package-actions"><view class="action-button edit" catchtap="{{packageItem.l}}"><view class="action-icon edit-icon"></view><text class="action-text">编辑</text></view><view class="action-button share" catchtap="{{packageItem.m}}"><view class="action-icon share-icon"></view><text class="action-text">分享</text></view><view class="action-button more" catchtap="{{packageItem.n}}"><view class="action-icon more-icon"></view><text class="action-text">更多</text></view></view></view></view></view><view wx:if="{{t}}" class="empty-state"><image class="empty-image" src="{{v}}" mode="aspectFit"></image><text class="empty-text">暂无团购套餐</text><text class="empty-subtext">点击下方按钮创建您的第一个团购套餐</text></view></view></scroll-view><view class="floating-action-button" bindtap="{{w}}"><view class="fab-content"><view class="fab-icon">+</view><view class="fab-text">创建套餐</view></view></view><view wx:if="{{x}}" class="step-wizard"><view class="wizard-overlay" bindtap="{{y}}"></view><view class="wizard-content"><view class="wizard-header"><text class="wizard-title">创建团购套餐</text><view class="close-icon" bindtap="{{z}}">×</view></view><view class="wizard-body"><view wx:for="{{A}}" wx:for-item="step" wx:key="d" class="wizard-step" bindtap="{{step.e}}"><view class="step-number">{{step.a}}</view><view class="step-info"><text class="step-title">{{step.b}}</text><text class="step-desc">{{step.c}}</text></view><view class="step-arrow"></view></view></view></view></view></view>