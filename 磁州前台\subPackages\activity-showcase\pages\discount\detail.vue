<template>
  <view class="discount-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
        </view>
        <view class="navbar-title">满减详情</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <block v-else>
      <!-- 商家信息卡片 -->
      <view class="merchant-card" :style="{ marginTop: navbarHeight + 'px' }">
        <view class="merchant-header">
          <image :src="discount.merchantLogo" class="merchant-logo"></image>
          <view class="merchant-info">
            <view class="merchant-name">{{ discount.merchantName }}</view>
            <view class="merchant-rating">
              <view class="rating-stars">
                <image v-for="i in 5" :key="i" 
                  :src="i <= Math.floor(discount.merchantRating) ? 
                    '/static/images/tabbar/星星-选中.png' : 
                    '/static/images/tabbar/星星-未选.png'" 
                  class="star-icon"></image>
              </view>
              <text class="rating-score">{{ discount.merchantRating }}</text>
            </view>
          </view>
          <view class="merchant-action" @click="goToShop">
            <text>进店</text>
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>
      
      <!-- 满减规则卡片 -->
      <view class="rules-card">
        <view class="card-header">
          <text class="card-title">满减规则</text>
          <view class="activity-time">{{ getTimeText(discount.startTime, discount.endTime) }}</view>
        </view>
        
        <view class="rules-content">
          <view class="rule-item" 
            v-for="(rule, index) in discount.rules" 
            :key="index"
            :class="{ 'highlight': rule.highlight }">
            <view class="rule-tag" v-if="rule.highlight">推荐</view>
            <text class="rule-text">{{ rule.text }}</text>
          </view>
        </view>
        
        <view class="rules-notice">
          <text>* 同一订单不可叠加使用多个满减优惠</text>
        </view>
        
        <!-- 分销组件 - 醒目位置 -->
        <distribution-section 
          :itemId="id"
          itemType="discount"
          :itemTitle="discount.merchantName + '满减活动'"
          :itemPrice="getHighestDiscountAmount()"
          :commissionRate="discount.commissionRate || 8"
        />
      </view>
      
      <!-- 活动说明卡片 -->
      <view class="description-card">
        <view class="card-header">
          <text class="card-title">活动说明</text>
        </view>
        <view class="description-content">
          <view class="description-item" v-for="(item, index) in discount.descriptions" :key="index">
            <view class="item-dot"></view>
            <text class="item-text">{{ item }}</text>
          </view>
        </view>
      </view>
      
      <!-- 推荐商品卡片 -->
      <view class="products-card">
        <view class="card-header">
          <text class="card-title">推荐商品</text>
          <view class="card-more" @click="viewAllProducts">
            <text>查看全部</text>
            <text class="arrow-icon">›</text>
          </view>
        </view>
        
        <scroll-view class="products-scroll" scroll-x>
          <view class="products-list">
            <view class="product-item" 
              v-for="(item, index) in discount.recommendProducts" 
              :key="index"
              @click="goToProductDetail(item.id)">
              <image :src="item.image" mode="aspectFill" class="product-image"></image>
              <view class="product-info">
                <text class="product-name">{{ item.name }}</text>
                <view class="product-price">
                  <text class="price-symbol">¥</text>
                  <text class="price-value">{{ item.price }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </block>
    
    <!-- 底部按钮 -->
    <view class="bottom-bar">
      <view class="action-btn" @click="toggleFavorite">
        <image :src="isFavorite ? '/static/images/tabbar/收藏-选中.png' : '/static/images/tabbar/收藏.png'" class="action-icon"></image>
        <text>收藏</text>
      </view>
      <view class="action-btn" @click="contactService">
        <image src="/static/images/tabbar/客服.png" class="action-icon"></image>
        <text>客服</text>
      </view>
      <view class="use-btn" @click="useDiscount">
        <text>立即使用</text>
      </view>
    </view>
  </view>
</template>

<script>
import DistributionSection from '@/components/distribution-section.vue';

export default {
  components: {
    DistributionSection
  },
  data() {
    return {
      id: null,
      statusBarHeight: 20,
      navbarHeight: 82,
      loading: true,
      discount: {},
      isFavorite: false,
      commissionAmount: '30'
    }
  },
  onLoad(options) {
    if (options && options.id) {
      this.id = options.id;
    }
    
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 62; // 状态栏 + 标题栏高度
    
    // 模拟加载数据
    setTimeout(() => {
      this.loadDiscountDetail();
    }, 500);
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 加载满减详情
    loadDiscountDetail() {
      // 模拟API加载数据
      this.loading = true;
      
      // 在实际应用中，这里应该是从API获取数据
      setTimeout(() => {
        // 模拟数据
        this.discount = {
          id: this.id || 1,
          merchantName: '星巴克咖啡',
          merchantLogo: '/static/demo/shop-logo.png',
          merchantRating: 4.8,
          merchantId: 101,
          rules: [
            { text: '满50减10', highlight: false },
            { text: '满100减30', highlight: true },
            { text: '满200减60', highlight: false }
          ],
          startTime: new Date(),
          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          descriptions: [
            '活动时间：2023年10月1日至2023年10月31日',
            '活动范围：全场通用，特价商品除外',
            '使用方式：下单结算时自动抵扣',
            '限制说明：每人每天最多使用3次',
            '其他说明：最终解释权归商家所有'
          ],
          recommendProducts: [
            {
              id: 1,
              name: '美式咖啡（大）',
              price: '30',
              image: '/static/demo/product1.jpg'
            },
            {
              id: 2,
              name: '拿铁咖啡（中）',
              price: '32',
              image: '/static/demo/product2.jpg'
            },
            {
              id: 3,
              name: '摩卡咖啡（大）',
              price: '36',
              image: '/static/demo/product3.jpg'
            },
            {
              id: 4,
              name: '焦糖玛奇朵',
              price: '38',
              image: '/static/demo/product1.jpg'
            }
          ]
        };
        
        this.loading = false;
      }, 1000);
    },
    
    // 获取活动时间文本
    getTimeText(startTime, endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      
      const startMonth = start.getMonth() + 1;
      const startDay = start.getDate();
      const endMonth = end.getMonth() + 1;
      const endDay = end.getDate();
      
      return `${startMonth}.${startDay} - ${endMonth}.${endDay}`;
    },
    
    // 进入店铺
    goToShop() {
      uni.navigateTo({
        url: `/subPackages/shop/pages/detail?id=${this.discount.merchantId}`
      });
    },
    
    // 查看全部商品
    viewAllProducts() {
      uni.navigateTo({
        url: `/subPackages/shop/pages/products?merchantId=${this.discount.merchantId}`
      });
    },
    
    // 跳转到商品详情
    goToProductDetail(productId) {
      uni.navigateTo({
        url: `/subPackages/product/pages/detail?id=${productId}`
      });
    },
    
    // 获取最高满减金额
    getHighestDiscountAmount() {
      const highestRule = this.discount.rules.find(rule => rule.highlight);
      if (highestRule) {
        return highestRule.text.split('减')[1];
      }
      return '0';
    },
    
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      uni.showToast({
        title: this.isFavorite ? '已收藏' : '已取消收藏',
        icon: 'none'
      });
    },
    
    // 联系客服
    contactService() {
      uni.showToast({
        title: '正在连接客服...',
        icon: 'none'
      });
    },
    
    // 使用满减
    useDiscount() {
      uni.navigateTo({
        url: `/subPackages/shop/pages/detail?id=${this.discount.merchantId}&discountId=${this.id}`
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.discount-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding-top: var(--status-bar-height, 25px);
    padding-left: 30rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
  }
  
  .back-btn {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
  }
  
  .back-icon {
    width: 100%;
    height: 100%;
  }
  
  .navbar-title {
    font-size: 18px;
    font-weight: 600;
    color: #FFFFFF;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  
  .loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #5856D6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  
  .loading-text {
    font-size: 28rpx;
    color: #8E8E93;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 商家信息卡片 */
.merchant-card {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  
  .merchant-header {
    display: flex;
    align-items: center;
    
    .merchant-logo {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      margin-right: 20rpx;
      box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    }
    
    .merchant-info {
      flex: 1;
    }
    
    .merchant-name {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 10rpx;
    }
    
    .merchant-rating {
      display: flex;
      align-items: center;
    }
    
    .rating-stars {
      display: flex;
      margin-right: 10rpx;
    }
    
    .star-icon {
      width: 24rpx;
      height: 24rpx;
      margin-right: 4rpx;
    }
    
    .rating-score {
      font-size: 24rpx;
      color: #FF9500;
      font-weight: 600;
    }
    
    .merchant-action {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: #5856D6;
      font-weight: 500;
      
      .arrow-icon {
        font-size: 32rpx;
        margin-left: 5rpx;
      }
    }
  }
}

/* 满减规则卡片 */
.rules-card {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid #EFEFEF;
    padding-bottom: 20rpx;
  }
  
  .card-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .activity-time {
    font-size: 24rpx;
    color: #8E8E93;
  }
  
  .rules-content {
    display: flex;
    flex-direction: column;
    margin-bottom: 20rpx;
  }
  
  .rule-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 20rpx;
    margin-bottom: 15rpx;
    background-color: #F5F7FA;
    border-radius: 16rpx;
    
    &.highlight {
      background: linear-gradient(135deg, #F0EFFF 0%, #E6E6FF 100%);
      border: 1rpx solid #D1D1FF;
      
      .rule-text {
        color: #5856D6;
        font-weight: 600;
      }
    }
    
    .rule-tag {
      position: absolute;
      top: -10rpx;
      right: 20rpx;
      background-color: #5856D6;
      color: #FFFFFF;
      font-size: 20rpx;
      padding: 4rpx 12rpx;
      border-radius: 10rpx;
    }
    
    .rule-text {
      font-size: 28rpx;
      color: #666666;
    }
  }
  
  .rules-notice {
    font-size: 24rpx;
    color: #8E8E93;
    padding-top: 10rpx;
  }
}

/* 分销入口 */
.distribution-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 30rpx 30rpx 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #F0EFFF 0%, #E6E6FF 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(88, 86, 214, 0.1);
  
  .distribution-left {
    display: flex;
    align-items: center;
  }
  
  .distribution-icon {
    width: 60rpx;
    height: 60rpx;
    margin-right: 15rpx;
    
    .icon-image {
      width: 100%;
      height: 100%;
    }
  }
  
  .distribution-info {
    display: flex;
    flex-direction: column;
  }
  
  .distribution-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 5rpx;
  }
  
  .distribution-desc {
    font-size: 24rpx;
    color: #5856D6;
    font-weight: 600;
  }
  
  .distribution-right {
    display: flex;
    align-items: center;
  }
  
  .distribution-btn {
    font-size: 26rpx;
    color: #5856D6;
    font-weight: 600;
  }
  
  .arrow-icon {
    font-size: 32rpx;
    color: #5856D6;
    margin-left: 5rpx;
  }
}

/* 活动说明卡片 */
.description-card {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  
  .card-header {
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid #EFEFEF;
    padding-bottom: 20rpx;
  }
  
  .card-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .description-content {
    display: flex;
    flex-direction: column;
  }
  
  .description-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15rpx;
    
    .item-dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background-color: #5856D6;
      margin-top: 12rpx;
      margin-right: 15rpx;
      flex-shrink: 0;
    }
    
    .item-text {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.6;
    }
  }
}

/* 推荐商品卡片 */
.products-card {
  background-color: #FFFFFF;
  border-radius: 35rpx;
  padding: 30rpx;
  margin: 0 30rpx 30rpx 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid #EFEFEF;
    padding-bottom: 20rpx;
    
    .card-more {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #8E8E93;
      
      .arrow-icon {
        font-size: 30rpx;
        margin-left: 5rpx;
      }
    }
  }
  
  .products-scroll {
    width: 100%;
  }
  
  .products-list {
    display: flex;
    padding: 10rpx 0;
  }
  
  .product-item {
    width: 200rpx;
    margin-right: 20rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);
    background-color: #FFFFFF;
    transition: transform 0.3s ease;
    
    &:active {
      transform: scale(0.98);
    }
    
    .product-image {
      width: 200rpx;
      height: 200rpx;
      object-fit: cover;
    }
    
    .product-info {
      padding: 15rpx;
    }
    
    .product-name {
      font-size: 24rpx;
      color: #333333;
      line-height: 1.4;
      height: 68rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    .product-price {
      display: flex;
      align-items: baseline;
      margin-top: 10rpx;
      
      .price-symbol {
        font-size: 22rpx;
        color: #5856D6;
      }
      
      .price-value {
        font-size: 28rpx;
        font-weight: 600;
        color: #5856D6;
      }
    }
  }
}

/* 底部按钮 */
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
  
  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 40rpx;
    
    .action-icon {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 5rpx;
    }
    
    text {
      font-size: 22rpx;
      color: #8E8E93;
    }
  }
  
  .use-btn {
    flex: 1;
    height: 80rpx;
    background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 600;
    color: #FFFFFF;
    box-shadow: 0 8rpx 16rpx rgba(88, 86, 214, 0.2);
    transition: transform 0.3s ease;
    
    &:active {
      transform: translateY(5rpx);
      box-shadow: 0 4rpx 8rpx rgba(88, 86, 214, 0.15);
    }
  }
}
</style> 