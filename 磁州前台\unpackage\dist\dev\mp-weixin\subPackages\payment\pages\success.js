"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "success",
  setup(__props) {
    const orderInfo = common_vendor.ref({
      orderType: "",
      days: 0,
      amount: 0,
      orderNo: ""
    });
    const getOrderTypeText = (type) => {
      const typeMap = {
        "top": "信息置顶",
        "publish": "信息发布",
        "vip": "会员开通"
      };
      return typeMap[type] || type;
    };
    const viewDetail = () => {
      const infoId = common_vendor.index.getStorageSync("lastPublishId");
      common_vendor.index.navigateTo({
        url: `/pages/publish/detail?id=${infoId}`
      });
    };
    const goBack = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      if (options.orderInfo) {
        orderInfo.value = JSON.parse(decodeURIComponent(options.orderInfo));
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$24,
        b: common_vendor.t(getOrderTypeText(orderInfo.value.orderType)),
        c: orderInfo.value.days
      }, orderInfo.value.days ? {
        d: common_vendor.t(orderInfo.value.days)
      } : {}, {
        e: common_vendor.t(orderInfo.value.amount),
        f: common_vendor.t(orderInfo.value.orderNo),
        g: common_vendor.o(viewDetail),
        h: common_vendor.o(goBack)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/payment/pages/success.js.map
