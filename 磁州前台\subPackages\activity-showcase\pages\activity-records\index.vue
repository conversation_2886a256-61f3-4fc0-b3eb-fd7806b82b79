<template>
  <view class="activity-records-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" mode="aspectFit" class="back-icon"></image>
        </view>
        <view class="navbar-title">活动记录</view>
        <view class="navbar-right">
          <view class="filter-btn" @click="showFilter">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 活动类型标签栏 -->
    <view class="activity-tabs">
      <view 
        v-for="(tab, index) in activityTabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <view class="tab-indicator" v-if="currentTab === index" :style="{
          background: 'linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%)'
        }"></view>
      </view>
    </view>

    <!-- 活动记录列表区域 -->
    <swiper class="records-swiper" :current="currentTab" @change="onSwiperChange">
      <swiper-item v-for="(tab, tabIndex) in activityTabs" :key="tabIndex">
        <scroll-view 
          class="tab-content" 
          scroll-y 
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          @scrolltolower="loadMore"
        >
          <view class="records-list">
            <view 
              v-for="record in getRecordsByType(tab.type)" 
              :key="record.id"
              class="record-card"
              @click="viewActivityDetail(record)"
            >
              <!-- 活动图片 -->
              <image :src="record.image" class="record-image" mode="aspectFill"></image>
              
              <!-- 活动状态标签 -->
              <view class="status-tag" :style="{
                background: getStatusBackground(record.status),
                color: '#FFFFFF'
              }">
                {{ getStatusText(record.status) }}
              </view>
              
              <!-- 活动信息 -->
              <view class="record-info">
                <text class="record-title">{{ record.title }}</text>
                
                <view class="record-meta">
                  <view class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                      <line x1="16" y1="2" x2="16" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                      <line x1="8" y1="2" x2="8" y2="6" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                      <line x1="3" y1="10" x2="21" y2="10" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                    </svg>
                    <text class="meta-text">{{ record.date }}</text>
                  </view>
                  
                  <view class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                      <circle cx="12" cy="12" r="10" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                      <polyline points="12 6 12 12 16 14" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polyline>
                    </svg>
                    <text class="meta-text">{{ record.time }}</text>
                  </view>
                  
                  <view class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                      <circle cx="12" cy="10" r="3" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                    </svg>
                    <text class="meta-text">{{ record.location }}</text>
                  </view>
                </view>
                
                <view class="record-bottom">
                  <view class="participants">
                    <view class="avatar-group">
                      <image 
                        v-for="(avatar, avatarIndex) in record.participants.slice(0, 3)" 
                        :key="avatarIndex"
                        :src="avatar"
                        class="participant-avatar"
                      ></image>
                      <view class="avatar-more" v-if="record.participants.length > 3">
                        +{{ record.participants.length - 3 }}
                      </view>
                    </view>
                    <text class="participant-count">{{ record.participants.length }}人参与</text>
                  </view>
                  
                  <view class="record-actions">
                    <view 
                      class="action-btn share"
                      @click.stop="shareActivity(record)"
                    >
                      <svg class="action-icon" viewBox="0 0 24 24" width="16" height="16">
                        <circle cx="18" cy="5" r="3" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                        <circle cx="6" cy="12" r="3" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                        <circle cx="18" cy="19" r="3" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                      </svg>
                      <text>分享</text>
                    </view>
                    
                    <view 
                      class="action-btn primary"
                      :style="{
                        background: getPrimaryActionBgColor(record.status),
                        color: '#FFFFFF'
                      }"
                      @click.stop="handlePrimaryAction(record)"
                    >
                      {{ getPrimaryActionText(record.status) }}
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-if="getRecordsByType(tab.type).length === 0">
            <image class="empty-image" :src="tab.emptyImage || '/static/images/empty-records.png'"></image>
            <text class="empty-text">{{ tab.emptyText || '暂无相关活动记录' }}</text>
            <view class="action-btn" @click="navigateTo('/subPackages/activity-showcase/pages/list/index')" :style="{
              background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
              borderRadius: '35px',
              boxShadow: '0 5px 15px rgba(255,59,105,0.3)'
            }">
              <text>去参与活动</text>
            </view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-popup">
        <view class="filter-header">
          <text class="filter-title">筛选</text>
          <view class="filter-close" @click="closeFilter">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <line x1="18" y1="6" x2="6" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              <line x1="6" y1="6" x2="18" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            </svg>
          </view>
        </view>
        
        <view class="filter-content">
          <!-- 时间筛选 -->
          <view class="filter-section">
            <text class="section-title">时间范围</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in timeOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedTimeOption === index }"
                @click="selectTimeOption(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
          
          <!-- 活动状态筛选 -->
          <view class="filter-section">
            <text class="section-title">活动状态</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in statusOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedStatusOptions.includes(index) }"
                @click="toggleStatusOption(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
          
          <!-- 活动类型筛选 -->
          <view class="filter-section">
            <text class="section-title">活动类型</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in typeOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedTypeOptions.includes(index) }"
                @click="toggleTypeOption(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="filter-footer">
          <view class="filter-reset" @click="resetFilter">
            <text>重置</text>
          </view>
          <view class="filter-apply" @click="applyFilter">
            <text>确定</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 页面状态
const currentTab = ref(0);
const isRefreshing = ref(false);
const recordsList = ref([]);
const filterPopup = ref(null);

// 筛选选项
const selectedTimeOption = ref(0);
const selectedStatusOptions = ref([0]);
const selectedTypeOptions = ref([0]);

// 筛选选项数据
const timeOptions = [
  { label: '全部时间', value: 'all' },
  { label: '最近一周', value: 'week' },
  { label: '最近一月', value: 'month' },
  { label: '最近三月', value: 'three_months' },
  { label: '自定义', value: 'custom' }
];

const statusOptions = [
  { label: '全部状态', value: 'all' },
  { label: '未开始', value: 'upcoming' },
  { label: '进行中', value: 'ongoing' },
  { label: '已结束', value: 'ended' },
  { label: '已取消', value: 'cancelled' }
];

const typeOptions = [
  { label: '全部类型', value: 'all' },
  { label: '文化活动', value: 'culture' },
  { label: '体育赛事', value: 'sports' },
  { label: '亲子活动', value: 'family' },
  { label: '公益活动', value: 'charity' },
  { label: '户外拓展', value: 'outdoor' }
];

// 活动标签页
const activityTabs = [
  { name: '全部', type: 'all', emptyText: '暂无活动记录', emptyImage: '/static/images/empty-activities.png' },
  { name: '我参与的', type: 'participated', emptyText: '暂无参与活动', emptyImage: '/static/images/empty-participated.png' },
  { name: '我发起的', type: 'created', emptyText: '暂无发起活动', emptyImage: '/static/images/empty-created.png' },
  { name: '已收藏', type: 'favorite', emptyText: '暂无收藏活动', emptyImage: '/static/images/empty-favorites.png' }
];

// 模拟数据
const mockRecords = [
  {
    id: '1001',
    title: '磁州文化节',
    type: 'culture',
    status: 'upcoming',
    date: '2024-06-15',
    time: '09:00-18:00',
    location: '磁州文化广场',
    image: '/static/demo/activity1.jpg',
    participants: [
      '/static/demo/avatar1.png',
      '/static/demo/avatar2.png',
      '/static/demo/avatar3.png',
      '/static/demo/avatar4.png',
      '/static/demo/avatar5.png'
    ],
    recordType: 'participated'
  },
  {
    id: '1002',
    title: '亲子户外拓展活动',
    type: 'family',
    status: 'ongoing',
    date: '2024-05-28',
    time: '14:00-17:00',
    location: '磁州森林公园',
    image: '/static/demo/activity2.jpg',
    participants: [
      '/static/demo/avatar1.png',
      '/static/demo/avatar3.png',
      '/static/demo/avatar5.png'
    ],
    recordType: 'participated'
  },
  {
    id: '1003',
    title: '社区篮球赛',
    type: 'sports',
    status: 'ended',
    date: '2024-05-20',
    time: '10:00-12:00',
    location: '磁州体育中心',
    image: '/static/demo/activity3.jpg',
    participants: [
      '/static/demo/avatar2.png',
      '/static/demo/avatar4.png',
      '/static/demo/avatar5.png',
      '/static/demo/avatar1.png'
    ],
    recordType: 'created'
  },
  {
    id: '1004',
    title: '传统文化体验课',
    type: 'culture',
    status: 'cancelled',
    date: '2024-05-15',
    time: '15:00-17:00',
    location: '磁州文化馆',
    image: '/static/demo/activity4.jpg',
    participants: [
      '/static/demo/avatar3.png',
      '/static/demo/avatar1.png'
    ],
    recordType: 'favorite'
  },
  {
    id: '1005',
    title: '环保公益行动',
    type: 'charity',
    status: 'upcoming',
    date: '2024-06-05',
    time: '09:00-12:00',
    location: '磁州河畔',
    image: '/static/demo/activity5.jpg',
    participants: [
      '/static/demo/avatar1.png',
      '/static/demo/avatar2.png',
      '/static/demo/avatar3.png',
      '/static/demo/avatar4.png',
      '/static/demo/avatar5.png',
      '/static/demo/avatar1.png',
      '/static/demo/avatar2.png'
    ],
    recordType: 'created'
  }
];

// 生命周期
onMounted(() => {
  loadRecords();
});

// 方法
const loadRecords = () => {
  // 模拟加载数据
  recordsList.value = mockRecords;
};

const getRecordsByType = (type) => {
  if (type === 'all') {
    return recordsList.value;
  }
  return recordsList.value.filter(record => record.recordType === type);
};

const switchTab = (index) => {
  currentTab.value = index;
};

const onSwiperChange = (e) => {
  currentTab.value = e.detail.current;
};

const onRefresh = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    loadRecords();
    isRefreshing.value = false;
  }, 1000);
};

const loadMore = () => {
  // 模拟加载更多
  console.log('加载更多记录');
};

const getStatusText = (status) => {
  const statusMap = {
    'upcoming': '未开始',
    'ongoing': '进行中',
    'ended': '已结束',
    'cancelled': '已取消'
  };
  return statusMap[status] || '未知状态';
};

const getStatusBackground = (status) => {
  const bgMap = {
    'upcoming': 'rgba(255, 149, 0, 0.8)',
    'ongoing': 'rgba(52, 199, 89, 0.8)',
    'ended': 'rgba(142, 142, 147, 0.8)',
    'cancelled': 'rgba(255, 59, 48, 0.8)'
  };
  return bgMap[status] || 'rgba(142, 142, 147, 0.8)';
};

const getPrimaryActionText = (status) => {
  const actionMap = {
    'upcoming': '立即报名',
    'ongoing': '查看详情',
    'ended': '活动回顾',
    'cancelled': '查看详情'
  };
  return actionMap[status] || '查看详情';
};

const getPrimaryActionBgColor = (status) => {
  const bgColorMap = {
    'upcoming': 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
    'ongoing': 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',
    'ended': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)',
    'cancelled': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)'
  };
  return bgColorMap[status] || 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)';
};

const handlePrimaryAction = (record) => {
  switch (record.status) {
    case 'upcoming':
      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}&action=register`);
      break;
    case 'ongoing':
      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);
      break;
    case 'ended':
      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}&tab=review`);
      break;
    default:
      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);
  }
};

const viewActivityDetail = (record) => {
  navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);
};

const shareActivity = (record) => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const showFilter = () => {
  filterPopup.value.open();
};

const closeFilter = () => {
  filterPopup.value.close();
};

const selectTimeOption = (index) => {
  selectedTimeOption.value = index;
};

const toggleStatusOption = (index) => {
  const position = selectedStatusOptions.value.indexOf(index);
  if (index === 0) {
    // 如果选择"全部"，清除其他选项
    selectedStatusOptions.value = [0];
  } else {
    // 如果选择其他选项，移除"全部"选项
    if (selectedStatusOptions.value.includes(0)) {
      selectedStatusOptions.value = selectedStatusOptions.value.filter(item => item !== 0);
    }
    
    // 切换选中状态
    if (position !== -1) {
      selectedStatusOptions.value.splice(position, 1);
      // 如果没有选项，默认选中"全部"
      if (selectedStatusOptions.value.length === 0) {
        selectedStatusOptions.value = [0];
      }
    } else {
      selectedStatusOptions.value.push(index);
    }
  }
};

const toggleTypeOption = (index) => {
  const position = selectedTypeOptions.value.indexOf(index);
  if (index === 0) {
    // 如果选择"全部"，清除其他选项
    selectedTypeOptions.value = [0];
  } else {
    // 如果选择其他选项，移除"全部"选项
    if (selectedTypeOptions.value.includes(0)) {
      selectedTypeOptions.value = selectedTypeOptions.value.filter(item => item !== 0);
    }
    
    // 切换选中状态
    if (position !== -1) {
      selectedTypeOptions.value.splice(position, 1);
      // 如果没有选项，默认选中"全部"
      if (selectedTypeOptions.value.length === 0) {
        selectedTypeOptions.value = [0];
      }
    } else {
      selectedTypeOptions.value.push(index);
    }
  }
};

const resetFilter = () => {
  selectedTimeOption.value = 0;
  selectedStatusOptions.value = [0];
  selectedTypeOptions.value = [0];
};

const applyFilter = () => {
  // 应用筛选
  console.log('应用筛选', {
    time: timeOptions[selectedTimeOption.value].value,
    status: selectedStatusOptions.value.map(index => statusOptions[index].value),
    type: selectedTypeOptions.value.map(index => typeOptions[index].value)
  });
  
  // 模拟筛选结果
  uni.showToast({
    title: '筛选已应用',
    icon: 'success'
  });
  
  closeFilter();
};

const goBack = () => {
  uni.navigateBack();
};

const navigateTo = (url) => {
  uni.navigateTo({ url });
};
</script>

<style scoped>
.activity-records-container {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.navbar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 107rpx; /* 原来是102rpx，增加5rpx */
  padding: var(--status-bar-height) 30rpx 0;
}

.back-btn, .filter-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* 标签栏样式 */
.activity-tabs {
  display: flex;
  background: #FFFFFF;
  padding: 0 20rpx;
  margin-top: calc(var(--status-bar-height) + 107rpx); /* 原来是102rpx，增加5rpx */
  border-bottom: 1rpx solid #EEEEEE;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}

.tab-text {
  font-size: 28rpx;
  color: #333333;
  padding: 0 10rpx;
}

.tab-item.active .tab-text {
  color: #FF3B69;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 6rpx;
  border-radius: 3rpx;
}

/* 活动记录列表样式 */
.records-swiper {
  height: calc(100vh - var(--status-bar-height) - 107rpx - 70rpx); /* 原来是102rpx，增加5rpx */
}

.tab-content {
  height: 100%;
  padding: 20rpx;
}

.records-list {
  padding-bottom: 30rpx;
}

.record-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  position: relative;
}

.record-image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.status-tag {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.record-info {
  padding: 20rpx 30rpx;
}

.record-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.record-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
}

.meta-icon {
  margin-right: 6rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #999999;
}

.record-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

.participants {
  display: flex;
  align-items: center;
}

.avatar-group {
  display: flex;
  margin-right: 10rpx;
}

.participant-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
  margin-left: -10rpx;
}

.participant-avatar:first-child {
  margin-left: 0;
}

.avatar-more {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: #F0F0F0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666666;
  margin-left: -10rpx;
  border: 2rpx solid #FFFFFF;
}

.participant-count {
  font-size: 24rpx;
  color: #666666;
}

.record-actions {
  display: flex;
  align-items: center;
}

.action-btn {
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  padding: 10rpx 20rpx;
	  border-radius: 30rpx;
	  font-size: 24rpx;
	  margin-left: 15rpx;
	}
	
	.action-btn.share {
	  border: 1rpx solid #DDDDDD;
	  color: #666666;
	}
	
	.action-icon {
	  margin-right: 6rpx;
	}
	
	.action-btn.primary {
	  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
	}
	
	/* 空状态样式 */
	.empty-state {
	  display: flex;
	  flex-direction: column;
	  align-items: center;
	  justify-content: center;
	  padding: 100rpx 0;
	}
	
	.empty-image {
	  width: 200rpx;
	  height: 200rpx;
	  margin-bottom: 30rpx;
	}
	
	.empty-text {
	  font-size: 28rpx;
	  color: #999999;
	  margin-bottom: 30rpx;
	}
	
	.empty-state .action-btn {
	  padding: 15rpx 60rpx;
	  font-size: 28rpx;
	  color: #FFFFFF;
	}
	
	/* 筛选弹窗样式 */
	.filter-popup {
	  background: #FFFFFF;
	  border-top-left-radius: 30rpx;
	  border-top-right-radius: 30rpx;
	  padding: 30rpx;
	  max-height: 70vh;
	}
	
	.filter-header {
	  display: flex;
	  justify-content: space-between;
	  align-items: center;
	  margin-bottom: 30rpx;
	}
	
	.filter-title {
	  font-size: 32rpx;
	  font-weight: bold;
	  color: #333333;
	}
	
	.filter-close {
	  width: 60rpx;
	  height: 60rpx;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	}
	
	.filter-content {
	  max-height: calc(70vh - 180rpx);
	  overflow-y: auto;
	}
	
	.filter-section {
	  margin-bottom: 30rpx;
	}
	
	.section-title {
	  font-size: 28rpx;
	  color: #333333;
	  font-weight: 500;
	  margin-bottom: 20rpx;
	}
	
	.filter-options {
	  display: flex;
	  flex-wrap: wrap;
	}
	
	.filter-option {
	  padding: 10rpx 30rpx;
	  border-radius: 30rpx;
	  font-size: 26rpx;
	  color: #666666;
	  background: #F5F5F5;
	  margin-right: 20rpx;
	  margin-bottom: 20rpx;
	}
	
	.filter-option.active {
	  background: rgba(255, 59, 105, 0.1);
	  color: #FF3B69;
	  border: 1rpx solid rgba(255, 59, 105, 0.3);
	}
	
	.filter-footer {
	  display: flex;
	  justify-content: space-between;
	  margin-top: 30rpx;
	  padding-top: 20rpx;
	  border-top: 1rpx solid #F0F0F0;
	}
	
	.filter-reset, .filter-apply {
	  flex: 1;
	  height: 80rpx;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  border-radius: 40rpx;
	  font-size: 28rpx;
	}
	
	.filter-reset {
	  background: #F5F5F5;
	  color: #666666;
	  margin-right: 20rpx;
	}
	
	.filter-apply {
	  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
	  color: #FFFFFF;
	  box-shadow: 0 4rpx 8rpx rgba(255, 59, 105, 0.2);
	}
	</style>