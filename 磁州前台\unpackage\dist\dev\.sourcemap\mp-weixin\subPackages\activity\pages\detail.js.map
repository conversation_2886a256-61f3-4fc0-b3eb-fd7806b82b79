{"version": 3, "file": "detail.js", "sources": ["subPackages/activity/pages/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHlccGFnZXNcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"detail-container\">\r\n\t\t<image :src=\"activityInfo.image\" class=\"detail-banner\" mode=\"aspectFill\"></image>\r\n\t\t\r\n\t\t<view class=\"detail-content card-section\">\r\n\t\t\t<view class=\"detail-header\">\r\n\t\t\t\t<text class=\"detail-title\">{{activityInfo.title}}</text>\r\n\t\t\t\t<view class=\"detail-status\" :class=\"activityInfo.status === '进行中' ? 'status-active' : 'status-ended'\">\r\n\t\t\t\t\t{{activityInfo.status}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"detail-meta\">\r\n\t\t\t\t<view class=\"meta-item\">\r\n\t\t\t\t\t<image src=\"/static/images/tabbar/时间.png\" class=\"meta-icon\"></image>\r\n\t\t\t\t\t<text class=\"meta-text\">{{activityInfo.time}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"meta-item\">\r\n\t\t\t\t\t<image src=\"/static/images/tabbar/位置.png\" class=\"meta-icon\"></image>\r\n\t\t\t\t\t<text class=\"meta-text\">{{activityInfo.location}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"detail-section\">\r\n\t\t\t\t<text class=\"section-title\">活动详情</text>\r\n\t\t\t\t<text class=\"section-content\">{{activityInfo.description}}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"detail-section\">\r\n\t\t\t\t<text class=\"section-title\">活动规则</text>\r\n\t\t\t\t<view class=\"rule-list\">\r\n\t\t\t\t\t<text class=\"rule-item\" v-for=\"(rule, index) in activityInfo.rules\" :key=\"index\">{{index + 1}}. {{rule}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"detail-section\">\r\n\t\t\t\t<text class=\"section-title\">联系方式</text>\r\n\t\t\t\t<view class=\"contact-info\">\r\n\t\t\t\t\t<view class=\"contact-item\">\r\n\t\t\t\t\t\t<text class=\"contact-label\">主办方：</text>\r\n\t\t\t\t\t\t<text class=\"contact-value\">{{activityInfo.organizer}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"contact-item\">\r\n\t\t\t\t\t\t<text class=\"contact-label\">电话：</text>\r\n\t\t\t\t\t\t<text class=\"contact-value\">{{activityInfo.phone}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"action-bar\">\r\n\t\t\t<button class=\"share-btn\" @click=\"shareActivity\">\r\n\t\t\t\t<image src=\"/static/images/tabbar/分享.png\" class=\"btn-icon\"></image>\r\n\t\t\t\t<text>分享活动</text>\r\n\t\t\t</button>\r\n\t\t\t<button class=\"join-btn\" @click=\"joinActivity\">\r\n\t\t\t\t<text>我要参加</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t\t<fab-buttons />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport FabButtons from '@/components/FabButtons.vue'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tactivityInfo: {\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\ttitle: '磁州美食节',\r\n\t\t\t\t\timage: '/static/images/banner/banner-1.png',\r\n\t\t\t\t\ttime: '2024-03-20 至 2024-03-25',\r\n\t\t\t\t\tlocation: '磁县人民广场',\r\n\t\t\t\t\tdescription: '汇聚磁州各地特色美食，带您品尝地道磁州味道。活动期间设有美食品鉴、厨艺比拼、文化展示等多个环节，欢迎广大市民朋友参与。',\r\n\t\t\t\t\tstatus: '进行中',\r\n\t\t\t\t\trules: [\r\n\t\t\t\t\t\t'活动时间：每日10:00-22:00',\r\n\t\t\t\t\t\t'需要提前在线报名或现场登记',\r\n\t\t\t\t\t\t'请遵守场地秩序，文明参与活动',\r\n\t\t\t\t\t\t'主办方保留活动最终解释权'\r\n\t\t\t\t\t],\r\n\t\t\t\t\torganizer: '磁县文化旅游局',\r\n\t\t\t\t\tphone: '0310-12345678'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// 实际应用中，这里应该根据options.id从服务器获取活动详情\r\n\t\t\tconsole.log('活动ID:', options.id)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tshareActivity() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '分享功能开发中',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tjoinActivity() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '报名成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomponents: { FabButtons }\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.detail-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding-bottom: 120rpx;\r\n\t}\r\n\t\r\n\t.detail-banner {\r\n\t\twidth: 100%;\r\n\t\theight: 400rpx;\r\n\t}\r\n\t\r\n\t.detail-content {\r\n\t\tmargin: -50rpx 20rpx 0;\r\n\t\tpadding: 30rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t}\r\n\t\r\n\t.detail-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.detail-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tflex: 1;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\t\r\n\t.detail-status {\r\n\t\tpadding: 4rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\t\r\n\t.status-active {\r\n\t\tbackground-color: #e6f7ff;\r\n\t\tcolor: #0052CC;\r\n\t}\r\n\t\r\n\t.status-ended {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tcolor: #999;\r\n\t}\r\n\t\r\n\t.detail-meta {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 16rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.meta-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.meta-icon {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tmargin-right: 12rpx;\r\n\t}\r\n\t\r\n\t.meta-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\t\r\n\t.detail-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.section-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 16rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t.section-content {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.6;\r\n\t}\r\n\t\r\n\t.rule-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\t\r\n\t.rule-item {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\t\r\n\t.contact-info {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\t\r\n\t.contact-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.contact-label {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\twidth: 140rpx;\r\n\t}\r\n\t\r\n\t.contact-value {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\t\r\n\t.action-bar {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\theight: 100rpx;\r\n\t\tbackground: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 30rpx;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\t\r\n\t.share-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tbackground: #f5f5f5;\r\n\t\tborder: none;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\t\r\n\t.btn-icon {\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tmargin-right: 0;\r\n\t}\r\n\t\r\n\t.join-btn {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tbackground: linear-gradient(to right, #0052CC, #2196F3);\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t\r\n\t/* 继承首页的卡片样式 */\r\n\t.card-section {\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0,82,204,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);\r\n\t}\r\n</style> \r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity/pages/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAgEC,mBAAmB,MAAW;AAC9B,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA,QACb,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACA;AAAA,QACD,WAAW;AAAA,QACX,OAAO;AAAA,MACR;AAAA,IACD;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEfA,kBAAY,MAAA,MAAA,OAAA,+CAAA,SAAS,QAAQ,EAAE;AAAA,EAC/B;AAAA,EACD,SAAS;AAAA,IACR,gBAAgB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AAAA,IACD;AAAA,IACD,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AAAA,IACF;AAAA,EACA;AAAA,EACD,YAAY,EAAE,WAAW;AAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzGD,GAAG,WAAW,eAAe;"}