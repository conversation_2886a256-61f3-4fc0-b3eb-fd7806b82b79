
/* 全局样式 */
.product-publish-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 状态栏占位 */
.status-bar-placeholder {
  height: var(--status-bar-height, 20px);
  width: 100%;
  background: linear-gradient(135deg, #3A86FF, #5E60CE);
}

/* 导航栏样式 */
.custom-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  background: linear-gradient(135deg, #3A86FF, #5E60CE);
  position: relative;
}
.navbar-left, .navbar-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
}
.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
}
.back-button, .help-button {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-back, .icon-help {
  color: #FFFFFF;
  font-size: 18px;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  padding: 16px;
}

/* 表单区域 */
.form-section {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}
.section-header {
  margin-bottom: 20px;
}
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  display: block;
}
.section-subtitle {
  font-size: 14px;
  color: #999;
}

/* 图片上传区域 */
.image-upload-area {
  margin-bottom: 20px;
}
.upload-title {
  margin-bottom: 12px;
  font-size: 15px;
  color: #333;
}
.required {
  color: #FF3B30;
  margin-right: 4px;
}
.upload-desc {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}
.image-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -6px;
}
.image-item, .upload-item {
  width: calc(33.33% - 12px);
  margin: 6px;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}
.image-item::before, .upload-item::before {
  content: "";
  display: block;
  padding-top: 100%;
}
.preview-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.delete-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  z-index: 1;
}
.upload-item {
  background-color: #F5F7FA;
  border: 1px dashed #DDD;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon {
  font-size: 24px;
  color: #999;
  margin-bottom: 4px;
}
.upload-text {
  font-size: 12px;
  color: #999;
}

/* 表单项 */
.form-item {
  margin-bottom: 20px;
}
.form-label {
  font-size: 15px;
  color: #333;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.label-tip {
  font-size: 12px;
  color: #999;
  margin-left: 4px;
}
.form-input-wrap {
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 0 16px;
  height: 48px;
  display: flex;
  align-items: center;
  position: relative;
}
.form-input {
  flex: 1;
  height: 100%;
  font-size: 15px;
}
.input-counter {
  font-size: 12px;
  color: #999;
}

/* 选择器 */
.selector {
  justify-content: space-between;
}
.selector-text {
  font-size: 15px;
  color: #333;
}
.placeholder {
  color: #999;
}
.selector-arrow {
  font-size: 18px;
  color: #999;
  transform: rotate(90deg);
}

/* 价格区域 */
.price-row {
  display: flex;
  margin: 0 -8px;
  margin-bottom: 20px;
}
.price-item {
  flex: 1;
  padding: 0 8px;
  margin-bottom: 0;
}
.price-input {
  position: relative;
}
.price-symbol {
  font-size: 15px;
  color: #333;
  margin-right: 4px;
}

/* 库存控制 */
.stock-input {
  display: flex;
  align-items: center;
  padding: 0;
}
.stock-btn {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #3A86FF;
  background-color: rgba(58, 134, 255, 0.1);
}
.stock-btn.disabled {
  color: #CCC;
}
.stock-value {
  flex: 1;
  text-align: center;
  font-size: 15px;
}

/* 底部操作栏 */
.bottom-action-bar {
  display: flex;
  padding: 16px;
  background-color: #FFFFFF;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  z-index: 10;
}
.action-button {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  border: none;
}
.save-draft {
  background-color: #E0E7FF;
  color: #4F46E5;
}
.publish {
  background: linear-gradient(135deg, #3A86FF, #5E60CE);
  color: #FFFFFF;
}

/* 文本域样式 */
.textarea-wrap {
  height: auto;
  padding: 12px 16px;
  min-height: 120px;
}
.form-textarea {
  width: 100%;
  height: 100px;
  font-size: 15px;
  line-height: 1.5;
}
.textarea-counter {
  position: absolute;
  bottom: 12px;
  right: 16px;
}

/* 规格参数样式 */
.specs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.specs-title {
  display: flex;
  align-items: center;
}
.add-spec-btn {
  display: flex;
  align-items: center;
  background-color: rgba(58, 134, 255, 0.1);
  padding: 6px 12px;
  border-radius: 16px;
}
.add-spec-icon {
  font-size: 16px;
  color: #3A86FF;
  margin-right: 4px;
}
.add-spec-text {
  font-size: 14px;
  color: #3A86FF;
}
.specs-list {
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 8px;
}
.spec-item {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}
.spec-item:last-child {
  margin-bottom: 0;
}
.spec-inputs {
  flex: 1;
  display: flex;
  align-items: center;
}
.spec-key {
  width: 40%;
  font-size: 14px;
  padding: 6px 0;
}
.spec-separator {
  margin: 0 8px;
  color: #999;
}
.spec-value {
  flex: 1;
  font-size: 14px;
  padding: 6px 0;
}
.delete-spec-btn {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}
.empty-specs {
  padding: 16px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -6px;
}
.tag-item {
  margin: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  background-color: #F5F7FA;
  color: #666;
  transition: all 0.2s;
}
.tag-item:nth-child(1) {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.tag-item:nth-child(2) {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
.tag-item:nth-child(3) {
  background-color: rgba(255, 45, 85, 0.1);
  color: #FF2D55;
}
.tag-item:nth-child(4) {
  background-color: rgba(175, 82, 222, 0.1);
  color: #AF52DE;
}
.tag-item:nth-child(5) {
  background-color: rgba(90, 200, 250, 0.1);
  color: #5AC8FA;
}
.tag-item:nth-child(6) {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}
.tag-item.active {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.tag-item.active:nth-child(1) {
  background-color: #34C759;
  color: #FFFFFF;
}
.tag-item.active:nth-child(2) {
  background-color: #FF9500;
  color: #FFFFFF;
}
.tag-item.active:nth-child(3) {
  background-color: #FF2D55;
  color: #FFFFFF;
}
.tag-item.active:nth-child(4) {
  background-color: #AF52DE;
  color: #FFFFFF;
}
.tag-item.active:nth-child(5) {
  background-color: #5AC8FA;
  color: #FFFFFF;
}
.tag-item.active:nth-child(6) {
  background-color: #007AFF;
  color: #FFFFFF;
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
.product-publish-container {
    background-color: #1C1C1E;
}
.form-section {
    background-color: #2C2C2E;
}
.section-title {
    color: #FFFFFF;
}
.form-label {
    color: #FFFFFF;
}
.form-input-wrap {
    background-color: #3A3A3C;
}
.form-input, .form-textarea {
    color: #FFFFFF;
}
.upload-item {
    background-color: #3A3A3C;
    border-color: #666;
}
.spec-item {
    background-color: #3A3A3C;
}
.specs-list {
    background-color: #2C2C2E;
}
.bottom-action-bar {
    background-color: #2C2C2E;
}
}
