<view class="page-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">红包模板</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="template-section"><view class="section-header"><text class="section-title">我的模板</text><view class="add-btn" bindtap="{{c}}"><text class="btn-text">新建模板</text><view class="plus-icon-small"></view></view></view><view class="template-list"><view wx:for="{{d}}" wx:for-item="item" wx:key="i" class="template-item" bindtap="{{item.j}}"><view class="template-preview" style="{{'background:' + item.c}}"><view class="template-icon"><image class="icon-image" src="{{item.a}}" mode="aspectFit"></image></view><text class="template-name">{{item.b}}</text></view><view class="template-info"><text class="template-desc">{{item.d}}</text><view class="template-type">{{item.e}}</view></view><view class="template-actions"><view class="action-btn" catchtap="{{item.f}}">使用</view><view class="action-btn edit" catchtap="{{item.g}}">编辑</view><view class="action-btn delete" catchtap="{{item.h}}">删除</view></view></view><view wx:if="{{e}}" class="empty-state"><view class="empty-icon"></view><text class="empty-text">暂无自定义模板</text><view class="empty-action" bindtap="{{f}}">新建模板</view></view></view></view><view class="official-section"><view class="section-header"><text class="section-title">官方模板</text></view><view class="template-grid"><view wx:for="{{g}}" wx:for-item="item" wx:key="e" class="template-card" bindtap="{{item.f}}"><view class="template-preview" style="{{'background:' + item.c}}"><view class="template-icon"><image class="icon-image" src="{{item.a}}" mode="aspectFit"></image></view><text class="template-name">{{item.b}}</text></view><view class="template-footer"><text class="template-desc">{{item.d}}</text><view class="template-use-btn">使用</view></view></view></view></view><view class="scenario-section"><view class="section-header"><text class="section-title">场景模板</text></view><scroll-view scroll-x class="scenario-scroll" show-scrollbar="false"><view class="scenario-container"><view wx:for="{{h}}" wx:for-item="item" wx:key="f" class="scenario-card" bindtap="{{item.g}}"><view class="scenario-preview" style="{{'background:' + item.d}}"><view class="scenario-icon"><image class="icon-image" src="{{item.a}}" mode="aspectFit"></image></view><text class="scenario-name">{{item.b}}</text><view class="scenario-tag">{{item.c}}</view></view><view class="scenario-footer"><text class="scenario-desc">{{item.e}}</text><view class="scenario-use-btn">使用</view></view></view></view></scroll-view></view><view class="guide-section"><view class="section-header"><text class="section-title">模板使用指南</text></view><view class="guide-steps"><view wx:for="{{i}}" wx:for-item="step" wx:key="d" class="guide-step"><view class="step-number">{{step.a}}</view><view class="step-content"><text class="step-title">{{step.b}}</text><text class="step-desc">{{step.c}}</text></view></view></view></view><view class="floating-action-button" bindtap="{{j}}"><view class="fab-icon">+</view></view></view>