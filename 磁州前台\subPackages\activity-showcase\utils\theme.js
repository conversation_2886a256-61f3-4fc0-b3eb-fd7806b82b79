/**
 * 磁州前台主题管理工具
 * 提供主题切换、颜色管理、样式动态应用等功能
 */

// 主题配置
const THEMES = {
  // 默认主题
  default: {
    name: '默认主题',
    colors: {
      primary: '#795548',
      primaryLight: '#A1887F',
      primaryDark: '#5D4037',
      secondary: '#FF9800',
      secondaryLight: '#FFB74D',
      secondaryDark: '#F57C00',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3',
      textPrimary: '#333333',
      textSecondary: '#666666',
      textTertiary: '#999999',
      bgPrimary: '#FFFFFF',
      bgSecondary: '#F8F9FA',
      bgTertiary: '#F5F5F5'
    }
  },
  
  // 磁州瓷器主题
  cizhou: {
    name: '磁州瓷器',
    colors: {
      primary: '#8D6E63',
      primaryLight: '#BCAAA4',
      primaryDark: '#5D4037',
      secondary: '#FF8A65',
      secondaryLight: '#FFAB91',
      secondaryDark: '#FF5722',
      success: '#66BB6A',
      warning: '#FFA726',
      error: '#EF5350',
      info: '#42A5F5',
      textPrimary: '#3E2723',
      textSecondary: '#5D4037',
      textTertiary: '#8D6E63',
      bgPrimary: '#FFF8E1',
      bgSecondary: '#F3E5F5',
      bgTertiary: '#EFEBE9'
    }
  },
  
  // 深色主题
  dark: {
    name: '深色主题',
    colors: {
      primary: '#6D4C41',
      primaryLight: '#8D6E63',
      primaryDark: '#3E2723',
      secondary: '#FF7043',
      secondaryLight: '#FF8A65',
      secondaryDark: '#FF5722',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3',
      textPrimary: '#FFFFFF',
      textSecondary: '#CCCCCC',
      textTertiary: '#999999',
      bgPrimary: '#2C2C2C',
      bgSecondary: '#1E1E1E',
      bgTertiary: '#121212'
    }
  },
  
  // 春季主题
  spring: {
    name: '春季主题',
    colors: {
      primary: '#4CAF50',
      primaryLight: '#81C784',
      primaryDark: '#388E3C',
      secondary: '#8BC34A',
      secondaryLight: '#AED581',
      secondaryDark: '#689F38',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3',
      textPrimary: '#1B5E20',
      textSecondary: '#2E7D32',
      textTertiary: '#4CAF50',
      bgPrimary: '#F1F8E9',
      bgSecondary: '#E8F5E8',
      bgTertiary: '#C8E6C9'
    }
  },
  
  // 夏季主题
  summer: {
    name: '夏季主题',
    colors: {
      primary: '#2196F3',
      primaryLight: '#64B5F6',
      primaryDark: '#1976D2',
      secondary: '#00BCD4',
      secondaryLight: '#4DD0E1',
      secondaryDark: '#0097A7',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3',
      textPrimary: '#0D47A1',
      textSecondary: '#1565C0',
      textTertiary: '#1976D2',
      bgPrimary: '#E3F2FD',
      bgSecondary: '#BBDEFB',
      bgTertiary: '#90CAF9'
    }
  },
  
  // 秋季主题
  autumn: {
    name: '秋季主题',
    colors: {
      primary: '#FF9800',
      primaryLight: '#FFB74D',
      primaryDark: '#F57C00',
      secondary: '#FF5722',
      secondaryLight: '#FF8A65',
      secondaryDark: '#D84315',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
      info: '#2196F3',
      textPrimary: '#BF360C',
      textSecondary: '#D84315',
      textTertiary: '#FF5722',
      bgPrimary: '#FFF3E0',
      bgSecondary: '#FFE0B2',
      bgTertiary: '#FFCC80'
    }
  }
}

// 当前主题
let currentTheme = 'default'

// 主题管理类
class ThemeManager {
  constructor() {
    this.currentTheme = 'default'
    this.listeners = []
    this.init()
  }
  
  // 初始化主题管理器
  init() {
    // 从本地存储加载主题设置
    try {
      const savedTheme = uni.getStorageSync('app_theme')
      if (savedTheme && THEMES[savedTheme]) {
        this.currentTheme = savedTheme
      }
    } catch (e) {
      console.warn('加载主题设置失败:', e)
    }
    
    // 应用当前主题
    this.applyTheme(this.currentTheme)
  }
  
  // 获取所有可用主题
  getAvailableThemes() {
    return Object.keys(THEMES).map(key => ({
      key,
      name: THEMES[key].name,
      colors: THEMES[key].colors
    }))
  }
  
  // 获取当前主题
  getCurrentTheme() {
    return this.currentTheme
  }
  
  // 获取主题配置
  getThemeConfig(themeName = null) {
    const theme = themeName || this.currentTheme
    return THEMES[theme] || THEMES.default
  }
  
  // 获取主题颜色
  getThemeColor(colorName, themeName = null) {
    const config = this.getThemeConfig(themeName)
    return config.colors[colorName] || config.colors.primary
  }
  
  // 切换主题
  setTheme(themeName) {
    if (!THEMES[themeName]) {
      console.warn(`主题 ${themeName} 不存在`)
      return false
    }
    
    const oldTheme = this.currentTheme
    this.currentTheme = themeName
    
    // 应用新主题
    this.applyTheme(themeName)
    
    // 保存到本地存储
    try {
      uni.setStorageSync('app_theme', themeName)
    } catch (e) {
      console.warn('保存主题设置失败:', e)
    }
    
    // 通知监听器
    this.notifyListeners(themeName, oldTheme)
    
    return true
  }
  
  // 应用主题
  applyTheme(themeName) {
    const config = this.getThemeConfig(themeName)
    const colors = config.colors
    
    // 更新CSS变量
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      Object.keys(colors).forEach(key => {
        const cssVar = this.camelToKebab(key)
        root.style.setProperty(`--${cssVar}`, colors[key])
      })
      
      // 添加主题类名
      document.body.className = document.body.className.replace(/theme-\w+/g, '')
      document.body.classList.add(`theme-${themeName}`)
    }
  }
  
  // 驼峰转短横线
  camelToKebab(str) {
    return str.replace(/([A-Z])/g, '-$1').toLowerCase()
  }
  
  // 添加主题变化监听器
  addListener(callback) {
    if (typeof callback === 'function') {
      this.listeners.push(callback)
    }
  }
  
  // 移除监听器
  removeListener(callback) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }
  
  // 通知所有监听器
  notifyListeners(newTheme, oldTheme) {
    this.listeners.forEach(callback => {
      try {
        callback(newTheme, oldTheme)
      } catch (e) {
        console.error('主题监听器执行失败:', e)
      }
    })
  }
  
  // 根据时间自动切换主题
  autoSwitchByTime() {
    const hour = new Date().getHours()
    let targetTheme = 'default'
    
    if (hour >= 6 && hour < 12) {
      targetTheme = 'spring' // 早晨
    } else if (hour >= 12 && hour < 18) {
      targetTheme = 'summer' // 下午
    } else if (hour >= 18 && hour < 22) {
      targetTheme = 'autumn' // 傍晚
    } else {
      targetTheme = 'dark' // 夜晚
    }
    
    this.setTheme(targetTheme)
  }
  
  // 根据系统主题切换
  autoSwitchBySystem() {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const targetTheme = darkModeQuery.matches ? 'dark' : 'default'
      this.setTheme(targetTheme)
      
      // 监听系统主题变化
      darkModeQuery.addListener((e) => {
        const newTheme = e.matches ? 'dark' : 'default'
        this.setTheme(newTheme)
      })
    }
  }
  
  // 生成主题预览
  generateThemePreview(themeName) {
    const config = this.getThemeConfig(themeName)
    const colors = config.colors
    
    return {
      name: config.name,
      primary: colors.primary,
      secondary: colors.secondary,
      background: colors.bgPrimary,
      text: colors.textPrimary,
      preview: `
        <div style="
          background: ${colors.bgPrimary};
          color: ${colors.textPrimary};
          padding: 16px;
          border-radius: 8px;
          border: 2px solid ${colors.primary};
        ">
          <div style="
            background: ${colors.primary};
            color: white;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 8px;
          ">${config.name}</div>
          <div style="color: ${colors.textSecondary};">示例文本</div>
          <div style="
            background: ${colors.secondary};
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
            margin-top: 8px;
          ">按钮</div>
        </div>
      `
    }
  }
  
  // 创建自定义主题
  createCustomTheme(name, colors) {
    const customKey = `custom_${Date.now()}`
    THEMES[customKey] = {
      name,
      colors: {
        ...THEMES.default.colors,
        ...colors
      }
    }
    return customKey
  }
  
  // 导出主题配置
  exportTheme(themeName = null) {
    const theme = themeName || this.currentTheme
    const config = this.getThemeConfig(theme)
    return JSON.stringify(config, null, 2)
  }
  
  // 导入主题配置
  importTheme(themeData, name) {
    try {
      const config = typeof themeData === 'string' ? JSON.parse(themeData) : themeData
      const customKey = `imported_${Date.now()}`
      THEMES[customKey] = {
        name: name || '导入的主题',
        colors: config.colors || config
      }
      return customKey
    } catch (e) {
      console.error('导入主题失败:', e)
      return null
    }
  }
}

// 创建全局主题管理器实例
const themeManager = new ThemeManager()

// 导出工具函数
export {
  themeManager,
  THEMES
}

// 便捷方法
export const getCurrentTheme = () => themeManager.getCurrentTheme()
export const setTheme = (themeName) => themeManager.setTheme(themeName)
export const getThemeColor = (colorName, themeName) => themeManager.getThemeColor(colorName, themeName)
export const getAvailableThemes = () => themeManager.getAvailableThemes()
export const addThemeListener = (callback) => themeManager.addListener(callback)
export const removeThemeListener = (callback) => themeManager.removeListener(callback)

// 默认导出
export default themeManager
