"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      shopList: [
        {
          id: "1",
          shopName: "五分利电器",
          category: "数码电器",
          scale: "10-20人"
        },
        {
          id: "2",
          shopName: "金鼎家居",
          category: "家居家装",
          scale: "20-50人"
        },
        {
          id: "3",
          shopName: "鲜丰水果",
          category: "生鲜果蔬",
          scale: "5-10人"
        },
        {
          id: "4",
          shopName: "磁州书院",
          category: "文化教育",
          scale: "10-15人"
        }
      ]
    };
  },
  methods: {
    goToShopDetail(id) {
      common_vendor.index.__f__("log", "at pages/test/shop-test.vue:50", "跳转到商家详情页，ID:", id);
      common_vendor.index.navigateTo({
        url: `/pages/business/shop-detail?id=${id}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.shopList, (shop, k0, i0) => {
      return {
        a: common_vendor.t(shop.shopName),
        b: common_vendor.t(shop.category),
        c: common_vendor.t(shop.scale),
        d: shop.id,
        e: common_vendor.o(($event) => $options.goToShopDetail(shop.id), shop.id)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/shop-test.js.map
