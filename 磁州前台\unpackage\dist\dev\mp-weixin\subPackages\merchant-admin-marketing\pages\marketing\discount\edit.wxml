<view class="discount-edit-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">编辑满减活动</text><view class="navbar-right"><text class="save-btn" bindtap="{{b}}">保存</text></view></view><view class="edit-form"><view class="form-section"><view class="section-header"><text class="section-title">基本信息</text></view><view class="form-item"><text class="item-label">活动名称</text><input class="item-input" placeholder="请输入活动名称" maxlength="20" value="{{c}}" bindinput="{{d}}"/><text class="input-count">{{e}}/20</text></view><view class="form-item"><text class="item-label">活动时间</text><view class="date-picker" bindtap="{{g}}"><text class="date-text">{{f}}</text></view><text class="date-separator">至</text><view class="date-picker" bindtap="{{i}}"><text class="date-text">{{h}}</text></view></view><view class="form-item"><text class="item-label">活动状态</text><view class="status-switch"><text class="status-text">{{j}}</text><switch checked="{{k}}" bindchange="{{l}}" color="#F8D800"/></view></view></view><view class="form-section"><view class="section-header"><text class="section-title">满减规则</text><text class="add-rule" bindtap="{{m}}">+ 添加规则</text></view><view class="rules-list"><view wx:for="{{n}}" wx:for-item="rule" wx:key="f" class="rule-item"><view class="rule-inputs"><view class="rule-input-group"><text class="input-prefix">满</text><input class="rule-input" type="digit" placeholder="0.00" value="{{rule.a}}" bindinput="{{rule.b}}"/><text class="input-suffix">元</text></view><text class="rule-separator">减</text><view class="rule-input-group"><input class="rule-input" type="digit" placeholder="0.00" value="{{rule.c}}" bindinput="{{rule.d}}"/><text class="input-suffix">元</text></view></view><view class="rule-delete" bindtap="{{rule.e}}"><text class="delete-icon">×</text></view></view><view wx:if="{{o}}" class="empty-rules"><text class="empty-text">请添加满减规则</text></view></view></view><view class="form-section"><view class="section-header"><text class="section-title">使用设置</text></view><view class="form-item"><text class="item-label">适用商品</text><view class="item-right" bindtap="{{q}}"><text class="item-value">{{p}}</text><text class="item-arrow">></text></view></view><view class="form-item"><text class="item-label">叠加使用</text><switch checked="{{r}}" bindchange="{{s}}" color="#F8D800"/></view><view class="form-item"><text class="item-label">每人限用</text><view class="limit-input-group"><input class="limit-input" type="number" placeholder="不限制" value="{{t}}" bindinput="{{v}}"/><text class="input-suffix">次</text></view></view><view class="form-item textarea-item"><text class="item-label">活动说明</text><block wx:if="{{r0}}"><textarea class="item-textarea" placeholder="请输入活动说明" maxlength="200" value="{{w}}" bindinput="{{x}}"></textarea></block><text class="textarea-count">{{y}}/200</text></view></view></view><view class="bottom-save-bar"><button class="save-button" bindtap="{{z}}">保存</button></view><uni-calendar wx:if="{{A}}" bindconfirm="{{B}}" bindclose="{{C}}" u-i="37d77594-0" bind:__l="__l" u-p="{{D}}"/></view>