/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.create-group-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  padding-bottom: 40rpx;
}
.form-container {
  padding: 30rpx;
}
.form-item {
  margin-bottom: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.form-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}
.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
}
.form-textarea {
  width: 100%;
  height: 180rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
}
.tags-container {
  display: flex;
  flex-wrap: wrap;
}
.tag-item {
  padding: 10rpx 20rpx;
  background-color: #F2F2F2;
  border-radius: 30rpx;
  margin: 10rpx;
  font-size: 24rpx;
  color: #666666;
}
.tag-item.selected {
  background-color: #E6F2FF;
  color: #0A84FF;
  border: 1px solid #0A84FF;
}
.location-selectors {
  background-color: #F8F8F8;
  border-radius: 8rpx;
  overflow: hidden;
}
.location-input {
  display: flex;
  align-items: center;
  height: 80rpx;
  padding: 0 20rpx;
}
.location-icon {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}
.location-icon.start {
  background-color: #34C759;
}
.location-icon.end {
  background-color: #FF3B30;
}
.location-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}
.location-divider {
  height: 1px;
  background-color: #E5E5E5;
  margin-left: 40rpx;
}
.qrcode-uploader {
  width: 200rpx;
  height: 200rpx;
  border: 1px dashed #CCCCCC;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-placeholder text {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}
.qrcode-preview {
  width: 100%;
  height: 100%;
}
.privacy-section {
  background-color: transparent;
  box-shadow: none;
  padding: 0;
}
.privacy-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666666;
}
.privacy-link {
  color: #0A84FF;
  margin-left: 4rpx;
}
.submit-button-area {
  padding: 30rpx;
  margin-top: 40rpx;
}
.submit-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #0A84FF, #0066CC);
  border-radius: 45rpx;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3);
}
.submit-btn[disabled] {
  background: linear-gradient(135deg, #B8B8B8, #999999);
  color: #FFFFFF;
  box-shadow: none;
}
.empty-tip {
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
}