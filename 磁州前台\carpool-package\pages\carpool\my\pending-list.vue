<template>
  <view class="pending-list-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/icons/back-white.png" mode="aspectFit" class="back-icon"></image>
        </view>
        <view class="navbar-title">待审核</view>
        <view class="navbar-right">
          <!-- 右侧占位 -->
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view class="scrollable-content" scroll-y @scrolltolower="loadMore" refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="isRefreshing">
      <!-- 审核中内容列表 -->
      <view class="card-list">
        <view class="card-item" v-for="(item, index) in pendingList" :key="item.id">
          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="header-left">
              <text class="card-type">{{item.type}}</text>
              <text class="publish-time">{{item.publishTime}}</text>
            </view>
            <view class="header-right">
              <text class="status-tag status-pending">审核中</text>
            </view>
          </view>
          
          <!-- 卡片内容 -->
          <view class="card-content">
            <view class="route-info">
              <view class="route-points">
                <view class="start-point">
                  <view class="point-marker start"></view>
                  <text class="point-text">{{item.startPoint}}</text>
                </view>
                <view class="route-line"></view>
                <view class="end-point">
                  <view class="point-marker end"></view>
                  <text class="point-text">{{item.endPoint}}</text>
                </view>
              </view>
              <view class="trip-info">
                <view class="info-item">
                  <image src="/static/images/icons/calendar.png" mode="aspectFit" class="info-icon"></image>
                  <text class="info-text">{{item.departureTime}}</text>
                </view>
                <view class="info-item">
                  <image src="/static/images/icons/people.png" mode="aspectFit" class="info-icon"></image>
                  <text class="info-text">{{item.seatCount}}个座位</text>
                </view>
                <view class="info-item" v-if="item.price">
                  <image src="/static/images/icons/price.png" mode="aspectFit" class="info-icon"></image>
                  <text class="info-text price">¥{{item.price}}/人</text>
                </view>
              </view>
            </view>
            
            <!-- 审核状态 -->
            <view class="audit-status">
              <view class="audit-progress">
                <view class="progress-bar">
                  <view class="progress-inner" :style="{width: item.auditProgress + '%'}"></view>
                </view>
                <text class="progress-text">预计{{item.estimatedTime}}完成审核</text>
              </view>
            </view>
          </view>
          
          <!-- 卡片底部按钮 -->
          <view class="card-actions">
            <button class="action-button outline" @click="deleteItem(item)">删除</button>
            <button class="action-button outline" @click="editItem(item)">编辑</button>
            <button class="action-button primary" @click="accelerateAudit(item)">加速审核</button>
          </view>
        </view>
      </view>
      
      <!-- 无数据提示 -->
      <view class="empty-state" v-if="pendingList.length === 0 && !isLoading">
        <image src="/static/images/empty/no-pending.png" mode="aspectFit" class="empty-image"></image>
        <text class="empty-text">暂无待审核信息</text>
        <button class="publish-button" @click="goPublish">去发布</button>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="isLoading && !isRefreshing">
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 到底提示 -->
      <view class="list-bottom" v-if="pendingList.length > 0 && !hasMore">
        <text class="bottom-text">— 已经到底啦 —</text>
      </view>
    </scroll-view>

    <!-- 加速审核弹窗 -->
    <view class="popup-mask" v-if="showAcceleratePopup" @click="closePopup"></view>
    <view class="popup-container" v-if="showAcceleratePopup">
      <view class="popup-header">
        <text class="popup-title">加速审核</text>
        <view class="popup-close" @click="closePopup">×</view>
      </view>
      <view class="popup-content">
        <view class="accelerate-info">
          <text class="accelerate-title">加速说明</text>
          <text class="accelerate-desc">通过付费或观看广告可以将您的审核优先处理，大幅缩短审核时间。</text>
        </view>
        
        <view class="topup-options">
          <view class="topup-option" @click="selectAccelerateOption('pay')">
            <view class="option-icon-wrap payment">
              <image src="/static/images/icons/payment.png" mode="aspectFit" class="option-icon"></image>
            </view>
            <view class="option-info">
              <text class="option-title">付费加速</text>
              <text class="option-desc">最快5分钟内完成审核</text>
              <text class="option-price">¥3.00</text>
            </view>
            <radio class="option-radio" :checked="accelerateOption === 'pay'" color="#1677FF"></radio>
          </view>
          
          <view class="topup-option" @click="selectAccelerateOption('ad')">
            <view class="option-icon-wrap ad">
              <image src="/static/images/icons/video-ad.png" mode="aspectFit" class="option-icon"></image>
            </view>
            <view class="option-info">
              <text class="option-title">看广告加速</text>
              <text class="option-desc">观看15秒广告后优先审核</text>
              <text class="option-free">免费</text>
            </view>
            <radio class="option-radio" :checked="accelerateOption === 'ad'" color="#1677FF"></radio>
          </view>
        </view>
      </view>
      <view class="popup-footer">
        <button class="popup-button cancel" @click="closePopup">取消</button>
        <button class="popup-button confirm" @click="confirmAccelerate">确认</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 数据定义
const pendingList = ref([])
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const isLoading = ref(false)
const isRefreshing = ref(false)
const showAcceleratePopup = ref(false)
const accelerateOption = ref('pay') // 默认选择付费加速
const currentItem = ref(null) // 当前操作的信息项

// 加载数据
const loadData = () => {
  if (isLoading.value) return
  isLoading.value = true
  
  // 模拟数据加载
  setTimeout(() => {
    // 模拟数据
    const mockData = [
      {
        id: '2001',
        type: '长途拼车',
        publishTime: '2023-10-15 16:30',
        startPoint: '磁县政府',
        endPoint: '石家庄火车站',
        departureTime: '明天 10:30',
        seatCount: 3,
        price: 50,
        auditProgress: 45,
        estimatedTime: '30分钟'
      },
      {
        id: '2002',
        type: '上下班拼车',
        publishTime: '2023-10-15 14:15',
        startPoint: '磁县老城区',
        endPoint: '邯郸科技学院',
        departureTime: '工作日 07:30',
        seatCount: 4,
        price: 12,
        auditProgress: 25,
        estimatedTime: '1小时'
      },
      {
        id: '2003',
        type: '短途拼车',
        publishTime: '2023-10-15 11:40',
        startPoint: '磁县体育场',
        endPoint: '磁县汽车站',
        departureTime: '今天 16:00',
        seatCount: 2,
        price: 5,
        auditProgress: 75,
        estimatedTime: '10分钟'
      }
    ]
    
    if (page.value === 1) {
      pendingList.value = mockData
    } else {
      pendingList.value = [...pendingList.value, ...mockData]
    }
    
    // 模拟没有更多数据
    if (page.value >= 2) {
      hasMore.value = false
    }
    
    isLoading.value = false
    isRefreshing.value = false
  }, 1000)
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || isLoading.value) return
  page.value++
  loadData()
}

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true
  page.value = 1
  hasMore.value = true
  loadData()
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 前往发布页面
const goPublish = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/publish/index'
  })
}

// 删除信息
const deleteItem = (item) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除此条信息吗？',
    success: (res) => {
      if (res.confirm) {
        // 模拟删除操作
        pendingList.value = pendingList.value.filter(i => i.id !== item.id)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}

// 编辑信息
const editItem = (item) => {
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/publish/index?id=${item.id}&type=edit`
  })
}

// 加速审核
const accelerateAudit = (item) => {
  currentItem.value = item
  showAcceleratePopup.value = true
}

// 关闭弹窗
const closePopup = () => {
  showAcceleratePopup.value = false
}

// 选择加速选项
const selectAccelerateOption = (option) => {
  accelerateOption.value = option
}

// 确认加速
const confirmAccelerate = () => {
  if (accelerateOption.value === 'pay') {
    // 调用支付接口
    uni.showLoading({
      title: '支付中...'
    })
    
    setTimeout(() => {
      uni.hideLoading()
      // 模拟支付成功
      handleAccelerateSuccess()
    }, 1500)
  } else {
    // 调用广告SDK
    uni.showLoading({
      title: '加载广告中...'
    })
    
    setTimeout(() => {
      uni.hideLoading()
      // 模拟广告播放完成
      uni.showModal({
        title: '广告观看完成',
        content: '感谢您观看广告，您的审核已加速处理！',
        showCancel: false,
        success: () => {
          handleAccelerateSuccess()
        }
      })
    }, 1500)
  }
}

// 处理加速成功
const handleAccelerateSuccess = () => {
  if (!currentItem.value) return
  
  // 更新数据
  pendingList.value = pendingList.value.map(item => {
    if (item.id === currentItem.value.id) {
      return {
        ...item,
        auditProgress: 90,
        estimatedTime: '5分钟'
      }
    }
    return item
  })
  
  uni.showToast({
    title: '加速成功',
    icon: 'success'
  })
  
  closePopup()
}

// 生命周期钩子
onMounted(() => {
  loadData()
})

// 暴露方法给外部访问
defineExpose({
  loadData,
  goPublish
})
</script>

<style scoped>
.pending-list-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(140rpx + var(--status-bar-height, 44px));
  background: linear-gradient(135deg, #3a7be8, #4f8cff);
  z-index: 100;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 16rpx rgba(79,140,255,0.15);
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 140rpx;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 44px);
}

.navbar-left, .navbar-right {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.navbar-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 2rpx;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 内容区域 */
.scrollable-content {
  flex: 1;
  margin-top: calc(44px + var(--status-bar-height));
  padding: 12px;
}

/* 卡片列表 */
.card-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-item {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #F8FAFB;
  border-bottom: 1px solid #EEEEEE;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-type {
  font-size: 15px;
  font-weight: 500;
  color: #333333;
}

.publish-time {
  font-size: 12px;
  color: #999999;
}

.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.status-pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

/* 卡片内容 */
.card-content {
  padding: 16px;
}

.route-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.route-points {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.start-point, .end-point {
  display: flex;
  align-items: center;
  gap: 10px;
}

.point-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.start {
  background-color: #1677FF;
}

.end {
  background-color: #FF5722;
}

.route-line {
  width: 2px;
  height: 20px;
  background-color: #DDDDDD;
  margin-left: 5px;
}

.point-text {
  font-size: 16px;
  color: #333333;
}

.trip-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-icon {
  width: 24px;
  height: 24px;
}

.info-text {
  font-size: 14px;
  color: #666666;
}

.price {
  color: #FF5722;
  font-weight: 500;
}

/* 审核状态 */
.audit-status {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #EEEEEE;
}

.audit-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar {
  height: 6px;
  background-color: #F5F5F5;
  border-radius: 3px;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background-color: #FF9800;
  border-radius: 3px;
}

.progress-text {
  font-size: 12px;
  color: #999999;
  text-align: right;
}

/* 卡片底部按钮 */
.card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20px 24px;
  gap: 20px;
  border-top: 1px solid #EEEEEE;
}

.action-button {
  padding: 10px 20px;
  font-size: 16px;
  height: 44px;
  min-width: 90px;
  border-radius: 6px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button.outline {
  color: #666666;
  border: 1px solid #DDDDDD;
}

.action-button.primary {
  color: #FFFFFF;
  background-color: #FF9800;
  border: 1px solid #FF9800;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999999;
  margin-bottom: 20px;
}

.publish-button {
  background-color: #FF9800;
  color: #FFFFFF;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
}

/* 加载状态 */
.loading-state {
  padding: 16px 0;
  text-align: center;
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 列表底部 */
.list-bottom {
  padding: 16px 0;
  text-align: center;
}

.bottom-text {
  font-size: 14px;
  color: #999999;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.popup-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #EEEEEE;
}

.popup-title {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
}

.popup-close {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}

.popup-content {
  padding: 16px;
}

.accelerate-info {
  margin-bottom: 16px;
}

.accelerate-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8px;
  display: block;
}

.accelerate-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.topup-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.topup-option {
  display: flex;
  align-items: center;
  padding: 14px;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  gap: 12px;
}

.option-icon-wrap {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment {
  background-color: rgba(22, 119, 255, 0.1);
}

.ad {
  background-color: rgba(255, 152, 0, 0.1);
}

.option-icon {
  width: 24px;
  height: 24px;
}

.option-info {
  flex: 1;
}

.option-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

.option-desc {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
}

.option-price, .option-free {
  font-size: 14px;
  margin-top: 4px;
}

.option-price {
  color: #FF5722;
  font-weight: 500;
}

.option-free {
  color: #52C41A;
  font-weight: 500;
}

.popup-footer {
  display: flex;
  border-top: 1px solid #EEEEEE;
}

.popup-button {
  flex: 1;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 16px;
}

.popup-button.cancel {
  color: #666666;
  border-right: 1px solid #EEEEEE;
}

.popup-button.confirm {
  color: #FF9800;
  font-weight: 500;
}
</style> 