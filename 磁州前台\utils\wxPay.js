/**
 * 微信支付工具函数
 */

// 统一下单接口
export const createOrder = (params) => {
  return new Promise((resolve, reject) => {
    uni.showLoading({
      title: '订单创建中...'
    });
    
    // 调用后端统一下单接口
    uni.request({
      url: 'https://your-api-domain/api/pay/create',
      method: 'POST',
      data: {
        ...params,
        // 可以添加其他必要参数，如用户ID、商品信息等
      },
      success: (res) => {
        uni.hideLoading();
        if (res.data && res.data.code === 0) {
          resolve(res.data.data);
        } else {
          uni.showToast({
            title: res.data.message || '创建订单失败',
            icon: 'none'
          });
          reject(res.data);
        }
      },
      fail: (err) => {
        uni.hideLoading();
        uni.showToast({
          title: '网络异常，请稍后再试',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

// 发起微信支付
export const requestPayment = (payParams) => {
  return new Promise((resolve, reject) => {
    // 判断环境
    // #ifdef MP-WEIXIN
    uni.requestPayment({
      ...payParams,
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        if (err.errMsg === 'requestPayment:fail cancel') {
          uni.showToast({
            title: '支付已取消',
            icon: 'none'
          });
        } else {
          uni.showToast({
            title: '支付失败，请重试',
            icon: 'none'
          });
        }
        reject(err);
      }
    });
    // #endif
    
    // #ifdef H5
    if (typeof WeixinJSBridge !== 'undefined') {
      WeixinJSBridge.invoke('getBrandWCPayRequest', payParams, (res) => {
        if (res.err_msg === 'get_brand_wcpay_request:ok') {
          resolve(res);
        } else if (res.err_msg === 'get_brand_wcpay_request:cancel') {
          uni.showToast({
            title: '支付已取消',
            icon: 'none'
          });
          reject(res);
        } else {
          uni.showToast({
            title: '支付失败，请重试',
            icon: 'none'
          });
          reject(res);
        }
      });
    } else {
      uni.showToast({
        title: '请在微信环境中支付',
        icon: 'none'
      });
      reject(new Error('非微信环境'));
    }
    // #endif
    
    // #ifdef APP-PLUS
    uni.requestPayment({
      provider: 'wxpay',
      ...payParams,
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        if (err.errMsg.indexOf('cancel') !== -1) {
          uni.showToast({
            title: '支付已取消',
            icon: 'none'
          });
        } else {
          uni.showToast({
            title: '支付失败，请重试',
            icon: 'none'
          });
        }
        reject(err);
      }
    });
    // #endif
  });
};

// 查询支付结果
export const queryPayResult = (orderId) => {
  return new Promise((resolve, reject) => {
    uni.showLoading({
      title: '查询支付结果...'
    });
    
    uni.request({
      url: 'https://your-api-domain/api/pay/query',
      method: 'GET',
      data: { orderId },
      success: (res) => {
        uni.hideLoading();
        if (res.data && res.data.code === 0) {
          resolve(res.data.data);
        } else {
          uni.showToast({
            title: res.data.message || '查询支付结果失败',
            icon: 'none'
          });
          reject(res.data);
        }
      },
      fail: (err) => {
        uni.hideLoading();
        uni.showToast({
          title: '网络异常，请稍后再试',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

// 完整支付流程
export const doPay = async (orderInfo) => {
  try {
    // 1. 创建订单
    const payParams = await createOrder(orderInfo);
    
    // 2. 发起支付
    const payResult = await requestPayment(payParams);
    
    // 3. 查询支付结果
    const result = await queryPayResult(payParams.orderId || orderInfo.orderId);
    
    // 4. 返回最终结果
    return {
      success: true,
      payResult: result
    };
  } catch (error) {
    return {
      success: false,
      error
    };
  }
}; 