/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-f47550b4, html.data-v-f47550b4, #app.data-v-f47550b4, .index-container.data-v-f47550b4 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 服务分类 */
.service-category.data-v-f47550b4 {
  background-color: #FFFFFF;
  padding: 26rpx 20rpx;
  margin: 0 25rpx 30rpx;
  border-radius: 35rpx;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}
.category-header.data-v-f47550b4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10rpx 20rpx 10rpx;
  border-bottom: 1px solid rgba(235, 238, 245, 0.5);
  margin-bottom: 20rpx;
}
.section-title-wrap.data-v-f47550b4 {
  display: flex;
  align-items: center;
}
.section-bar.data-v-f47550b4 {
  width: 6rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #007AFF, #5AC8FA);
  border-radius: 3rpx;
  margin-right: 6rpx;
}
.category-title.data-v-f47550b4 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}
.category-dots.data-v-f47550b4 {
  display: flex;
  align-items: center;
}
.category-dot.data-v-f47550b4 {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  margin: 0 6rpx;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}
.category-dot.active.data-v-f47550b4 {
  width: 24rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #1677ff, #3a8eff);
  box-shadow: 0 1px 3px rgba(22, 119, 255, 0.2);
}
.category-swiper.data-v-f47550b4 {
  width: 100%;
  height: 360rpx;
}
.category-page.data-v-f47550b4 {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 5rpx 0;
}

/* 第二页服务分类样式 */
.category-page-second.data-v-f47550b4 {
  align-items: flex-start;
  justify-content: flex-start;
  align-content: flex-start;
}
.category-item.data-v-f47550b4 {
  width: 20%;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 18rpx 0;
}
.category-icon.data-v-f47550b4 {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}
.category-item:active .category-icon.data-v-f47550b4 {
  transform: none;
}
.category-name.data-v-f47550b4 {
  font-size: 24rpx;
  color: #333;
  font-weight: 400;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 4rpx;
}

/* 到家服务全屏子分类弹出层样式 */
.service-category-popup.data-v-f47550b4 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn-f47550b4 0.25s ease;
}
@keyframes fadeIn-f47550b4 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.service-category-container.data-v-f47550b4 {
  width: 85%;
  height: auto;
  max-height: 72%;
  max-width: 650rpx;
  background-color: #fff;
  border-radius: 26rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp-f47550b4 0.3s ease;
}
@keyframes slideUp-f47550b4 {
from {
    transform: translateY(50rpx);
    opacity: 0.8;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
.service-category-header.data-v-f47550b4 {
  padding: 26rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f5ff;
  background: linear-gradient(to right, #f9fbff, #f0f5ff);
}
.category-header-left.data-v-f47550b4 {
  display: flex;
  flex-direction: column;
}
.category-title-main.data-v-f47550b4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}
.category-subtitle.data-v-f47550b4 {
  font-size: 22rpx;
  color: #777;
}
.category-close.data-v-f47550b4 {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.04);
  transition: all 0.3s;
}
.category-close.data-v-f47550b4:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
}
.close-icon.data-v-f47550b4 {
  width: 20rpx;
  height: 20rpx;
  position: relative;
}
.close-icon.data-v-f47550b4:before, .close-icon.data-v-f47550b4:after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 2px;
  background-color: #666;
  top: 9rpx;
  left: 0;
}
.close-icon.data-v-f47550b4:before {
  transform: rotate(45deg);
}
.close-icon.data-v-f47550b4:after {
  transform: rotate(-45deg);
}
.service-category-content.data-v-f47550b4 {
  padding: 25rpx 15rpx;
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  max-height: calc(80vh - 200rpx);
}
.service-type-item.data-v-f47550b4 {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
  margin-bottom: 12rpx;
  transition: all 0.2s;
}
.service-type-item.data-v-f47550b4:active {
  transform: scale(0.92);
}
.service-type-icon-wrap.data-v-f47550b4 {
  width: 90rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
}
.service-type-icon.data-v-f47550b4 {
  width: 90rpx;
  height: 90rpx;
  position: relative;
}
.service-type-name.data-v-f47550b4 {
  font-size: 22rpx;
  color: #333;
  text-align: center;
  padding: 0 6rpx;
  width: 100%;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.service-category-footer.data-v-f47550b4 {
  padding: 24rpx 0;
  display: flex;
  justify-content: center;
  border-top: 1px solid #f0f5ff;
  background-color: #f9fbff;
}
.view-all-btn.data-v-f47550b4 {
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  font-size: 26rpx;
  padding: 14rpx 50rpx;
  border-radius: 30rpx;
  text-align: center;
  box-shadow: 0 5rpx 12rpx rgba(22, 119, 255, 0.18);
  position: relative;
  overflow: hidden;
}
.view-all-btn.data-v-f47550b4:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(22, 119, 255, 0.15);
}
.view-all-btn.data-v-f47550b4:after {
  content: "";
  position: absolute;
  top: 0;
  left: -50%;
  width: 150%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  transform: skewX(-25deg);
  animation: shine-f47550b4 3s infinite;
}
@keyframes shine-f47550b4 {
0% {
    left: -150%;
}
50%, 100% {
    left: 150%;
}
}
.card-section.data-v-f47550b4 {
  background: #ffffff;
  border-radius: 35rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
  border: none;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 26rpx 22rpx;
}
.fade-in.data-v-f47550b4 {
  animation: fadeIn-f47550b4 0.5s ease-in-out;
}