"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  methods: {
    navigateToMyRedPackets() {
      common_vendor.index.navigateTo({
        url: "/subPackages/user/pages/my-red-packets"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$11,
    b: common_vendor.o((...args) => $options.navigateToMyRedPackets && $options.navigateToMyRedPackets(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/user/pages/profile.js.map
