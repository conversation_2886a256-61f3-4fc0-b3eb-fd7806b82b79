{"version": 3, "file": "distribution-section.js", "sources": ["components/distribution-section.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9kaXN0cmlidXRpb24tc2VjdGlvbi52dWU"], "sourcesContent": ["<!-- 分销推广组件 -->\n<template>\n  <view class=\"distribution-section\" @click=\"handleClick\">\n    <view class=\"distribution-card\">\n      <view class=\"distribution-left\">\n        <view class=\"distribution-icon\" :class=\"itemTypeClass\">\n          <image src=\"/static/images/tabbar/分销.png\" mode=\"aspectFit\" class=\"icon-image\"></image>\n        </view>\n        <view class=\"distribution-info\">\n          <text class=\"distribution-title\">{{ title }}</text>\n          <text class=\"distribution-desc\">最高可赚¥{{ commissionAmount }}</text>\n        </view>\n      </view>\n      <view class=\"distribution-right\" :class=\"itemTypeClass\">\n        <text class=\"distribution-btn\">去推广</text>\n        <view class=\"arrow-icon\">\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M9 6L15 12L9 18\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport distributionService from '@/utils/distributionService';\n\nexport default {\n  name: 'DistributionSection',\n  props: {\n    // 商品/活动ID\n    itemId: {\n      type: [String, Number],\n      required: true\n    },\n    // 商品/活动类型: product, coupon, discount, flash, group\n    itemType: {\n      type: String,\n      default: 'product'\n    },\n    // 商品/活动标题\n    itemTitle: {\n      type: String,\n      default: ''\n    },\n    // 商品/活动价格\n    itemPrice: {\n      type: [String, Number],\n      default: 0\n    },\n    // 佣金比例\n    commissionRate: {\n      type: [String, Number],\n      default: 20\n    }\n  },\n  computed: {\n    // 计算佣金金额\n    commissionAmount() {\n      const price = parseFloat(this.itemPrice);\n      const rate = parseFloat(this.commissionRate);\n      if (isNaN(price) || isNaN(rate)) return '0';\n      return (price * rate / 100).toFixed(2);\n    },\n    // 分销标题\n    title() {\n      const typeMap = {\n        'product': '分销此商品',\n        'coupon': '分销此优惠券',\n        'discount': '分销此活动',\n        'flash': '分销此秒杀',\n        'group': '分销此拼团'\n      };\n      return typeMap[this.itemType] || '分销此商品';\n    },\n    // 根据活动类型设置对应的样式类名\n    itemTypeClass() {\n      return `type-${this.itemType}`;\n    }\n  },\n  methods: {\n    // 处理点击事件\n    async handleClick() {\n      // 检查用户是否是分销员\n      const isDistributor = uni.getStorageSync('isDistributor') || false;\n      \n      if (isDistributor) {\n        // 如果是分销员，生成分销链接或海报\n        this.generateDistributionContent();\n      } else {\n        // 如果不是分销员，显示分销申请弹窗\n        this.showDistributionPopup();\n      }\n    },\n    \n    // 生成分销内容\n    async generateDistributionContent() {\n      uni.showActionSheet({\n        itemList: ['生成分销海报', '复制分销链接', '生成小程序码'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.generatePoster();\n              break;\n            case 1:\n              this.copyLink();\n              break;\n            case 2:\n              this.generateQrCode();\n              break;\n          }\n        }\n      });\n    },\n    \n    // 生成分销海报\n    async generatePoster() {\n      uni.showLoading({ title: '生成中...' });\n      try {\n        const result = await distributionService.getDistributionPoster({\n          type: this.itemType,\n          id: this.itemId,\n          title: this.itemTitle,\n          price: this.itemPrice\n        });\n        \n        uni.hideLoading();\n        if (result && result.posterUrl) {\n          uni.previewImage({\n            urls: [result.posterUrl],\n            current: result.posterUrl,\n            success: () => {\n              uni.showToast({\n                title: '长按图片保存',\n                icon: 'none'\n              });\n            }\n          });\n        } else {\n          uni.showToast({\n            title: '生成海报失败',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        uni.hideLoading();\n        uni.showToast({\n          title: '生成海报失败',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 复制分销链接\n    async copyLink() {\n      uni.showLoading({ title: '生成中...' });\n      try {\n        const result = await distributionService.generatePromotionLink({\n          type: this.itemType,\n          id: this.itemId,\n          distributorId: uni.getStorageSync('userId') || '0'\n        });\n        \n        uni.hideLoading();\n        if (result && result.url) {\n          uni.setClipboardData({\n            data: result.url,\n            success: () => {\n              uni.showToast({\n                title: '链接已复制',\n                icon: 'success'\n              });\n            }\n          });\n        } else {\n          uni.showToast({\n            title: '生成链接失败',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        uni.hideLoading();\n        uni.showToast({\n          title: '生成链接失败',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 生成小程序码\n    async generateQrCode() {\n      uni.showLoading({ title: '生成中...' });\n      try {\n        const result = await distributionService.generateProductCode({\n          type: this.itemType,\n          id: this.itemId\n        });\n        \n        uni.hideLoading();\n        if (result && result.qrCodeUrl) {\n          uni.previewImage({\n            urls: [result.qrCodeUrl],\n            current: result.qrCodeUrl,\n            success: () => {\n              uni.showToast({\n                title: '长按图片保存',\n                icon: 'none'\n              });\n            }\n          });\n        } else {\n          uni.showToast({\n            title: '生成小程序码失败',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        uni.hideLoading();\n        uni.showToast({\n          title: '生成小程序码失败',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 显示分销申请弹窗\n    showDistributionPopup() {\n      uni.navigateTo({\n        url: '/subPackages/distribution/pages/apply'\n      });\n    }\n  }\n}\n</script>\n\n<style>\n.distribution-section {\n  padding: 0 24rpx;\n  margin: 30rpx 0;\n  position: relative;\n  z-index: 10;\n}\n\n.distribution-card {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(245,245,247,0.9) 100%);\n  padding: 28rpx 30rpx;\n  border-radius: 35rpx;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), \n              0 2rpx 4rpx rgba(0, 0, 0, 0.05),\n              0 0 1rpx rgba(0, 0, 0, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1rpx solid rgba(255, 255, 255, 0.3);\n  position: relative;\n  overflow: hidden;\n}\n\n.distribution-card::before {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);\n  opacity: 0.5;\n}\n\n.distribution-left {\n  display: flex;\n  align-items: center;\n}\n\n.distribution-icon {\n  width: 70rpx;\n  height: 70rpx;\n  border-radius: 22rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-right: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 默认样式 - 紫色 */\n.distribution-icon {\n  background: linear-gradient(135deg, #6B0FBE 0%, #9013FE 100%);\n}\n\n/* 拼团活动 - 红色 */\n.distribution-icon.type-group {\n  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\n  box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.2);\n}\n\n/* 秒杀活动 - 橙色 */\n.distribution-icon.type-flash {\n  background: linear-gradient(135deg, #FF6B00 0%, #FF9500 100%);\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 0, 0.2);\n}\n\n/* 优惠券 - 蓝色 */\n.distribution-icon.type-coupon {\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n/* 满减活动 - 绿色 */\n.distribution-icon.type-discount {\n  background: linear-gradient(135deg, #34C759 0%, #30DB5B 100%);\n  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.2);\n}\n\n.icon-image {\n  width: 40rpx;\n  height: 40rpx;\n  filter: brightness(0) invert(1);\n}\n\n.distribution-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.distribution-title {\n  font-size: 30rpx;\n  font-weight: 600;\n  color: #1D1D1F;\n  margin-bottom: 8rpx;\n  letter-spacing: 0.5rpx;\n}\n\n.distribution-desc {\n  font-size: 26rpx;\n  color: #FF6B00;\n  font-weight: 500;\n}\n\n.distribution-right {\n  display: flex;\n  align-items: center;\n  padding: 16rpx 24rpx;\n  border-radius: 30rpx;\n}\n\n/* 默认样式 - 紫色 */\n.distribution-right {\n  background: linear-gradient(135deg, #6B0FBE 0%, #9013FE 100%);\n  box-shadow: 0 4rpx 12rpx rgba(107, 15, 190, 0.2);\n}\n\n/* 拼团活动 - 红色 */\n.distribution-right.type-group {\n  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);\n  box-shadow: 0 4rpx 12rpx rgba(255, 44, 84, 0.2);\n}\n\n/* 秒杀活动 - 橙色 */\n.distribution-right.type-flash {\n  background: linear-gradient(135deg, #FF6B00 0%, #FF9500 100%);\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 0, 0.2);\n}\n\n/* 优惠券 - 蓝色 */\n.distribution-right.type-coupon {\n  background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n/* 满减活动 - 绿色 */\n.distribution-right.type-discount {\n  background: linear-gradient(135deg, #34C759 0%, #30DB5B 100%);\n  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.2);\n}\n\n.distribution-btn {\n  font-size: 28rpx;\n  color: #FFFFFF;\n  font-weight: 500;\n}\n\n.arrow-icon {\n  display: flex;\n  align-items: center;\n  margin-left: 8rpx;\n  color: #FFFFFF;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/distribution-section.vue'\nwx.createComponent(Component)"], "names": ["uni", "distributionService"], "mappings": ";;;;AA4BA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,QAAQ;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,UAAU;AAAA,IACX;AAAA;AAAA,IAED,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,WAAW;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACV;AAAA;AAAA,IAED,gBAAgB;AAAA,MACd,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,mBAAmB;AACjB,YAAM,QAAQ,WAAW,KAAK,SAAS;AACvC,YAAM,OAAO,WAAW,KAAK,cAAc;AAC3C,UAAI,MAAM,KAAK,KAAK,MAAM,IAAI;AAAG,eAAO;AACxC,cAAQ,QAAQ,OAAO,KAAK,QAAQ,CAAC;AAAA,IACtC;AAAA;AAAA,IAED,QAAQ;AACN,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA;AAEX,aAAO,QAAQ,KAAK,QAAQ,KAAK;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AACd,aAAO,QAAQ,KAAK,QAAQ;AAAA,IAC9B;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,MAAM,cAAc;AAElB,YAAM,gBAAgBA,cAAG,MAAC,eAAe,eAAe,KAAK;AAE7D,UAAI,eAAe;AAEjB,aAAK,4BAA2B;AAAA,aAC3B;AAEL,aAAK,sBAAqB;AAAA,MAC5B;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,8BAA8B;AAClCA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,UAAU,UAAU,QAAQ;AAAA,QACvC,SAAS,CAAC,QAAQ;AAChB,kBAAQ,IAAI,UAAQ;AAAA,YAClB,KAAK;AACH,mBAAK,eAAc;AACnB;AAAA,YACF,KAAK;AACH,mBAAK,SAAQ;AACb;AAAA,YACF,KAAK;AACH,mBAAK,eAAc;AACnB;AAAA,UACJ;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrBA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,UAAI;AACF,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,sBAAsB;AAAA,UAC7D,MAAM,KAAK;AAAA,UACX,IAAI,KAAK;AAAA,UACT,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK;AAAA,QACd,CAAC;AAEDD,sBAAG,MAAC,YAAW;AACf,YAAI,UAAU,OAAO,WAAW;AAC9BA,wBAAAA,MAAI,aAAa;AAAA,YACf,MAAM,CAAC,OAAO,SAAS;AAAA,YACvB,SAAS,OAAO;AAAA,YAChB,SAAS,MAAM;AACbA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,WAAW;AACfA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,UAAI;AACF,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,sBAAsB;AAAA,UAC7D,MAAM,KAAK;AAAA,UACX,IAAI,KAAK;AAAA,UACT,eAAeD,cAAG,MAAC,eAAe,QAAQ,KAAK;AAAA,QACjD,CAAC;AAEDA,sBAAG,MAAC,YAAW;AACf,YAAI,UAAU,OAAO,KAAK;AACxBA,wBAAAA,MAAI,iBAAiB;AAAA,YACnB,MAAM,OAAO;AAAA,YACb,SAAS,MAAM;AACbA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrBA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnC,UAAI;AACF,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,oBAAoB;AAAA,UAC3D,MAAM,KAAK;AAAA,UACX,IAAI,KAAK;AAAA,QACX,CAAC;AAEDD,sBAAG,MAAC,YAAW;AACf,YAAI,UAAU,OAAO,WAAW;AAC9BA,wBAAAA,MAAI,aAAa;AAAA,YACf,MAAM,CAAC,OAAO,SAAS;AAAA,YACvB,SAAS,OAAO;AAAA,YAChB,SAAS,MAAM;AACbA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxOA,GAAG,gBAAgB,SAAS;"}