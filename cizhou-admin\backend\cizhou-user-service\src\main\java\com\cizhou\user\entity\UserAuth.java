package com.cizhou.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户认证实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_auth")
public class UserAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 认证ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 认证类型：1-身份证认证，2-营业执照认证，3-驾驶证认证
     */
    @TableField("auth_type")
    private Integer authType;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 身份证号
     */
    @TableField("id_card")
    private String idCard;

    /**
     * 身份证正面照
     */
    @TableField("id_card_front")
    private String idCardFront;

    /**
     * 身份证反面照
     */
    @TableField("id_card_back")
    private String idCardBack;

    /**
     * 营业执照号
     */
    @TableField("business_license")
    private String businessLicense;

    /**
     * 营业执照照片
     */
    @TableField("business_license_img")
    private String businessLicenseImg;

    /**
     * 驾驶证号
     */
    @TableField("driving_license")
    private String drivingLicense;

    /**
     * 驾驶证照片
     */
    @TableField("driving_license_img")
    private String drivingLicenseImg;

    /**
     * 认证状态：0-待审核，1-审核通过，2-审核拒绝
     */
    @TableField("status")
    private Integer status;

    /**
     * 审核人ID
     */
    @TableField("auditor_id")
    private Long auditorId;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核备注
     */
    @TableField("audit_remark")
    private String auditRemark;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private LocalDateTime applyTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
