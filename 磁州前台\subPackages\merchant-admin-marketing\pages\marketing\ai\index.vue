<!-- 智能营销助手页面开始 -->
<template>
  <view class="ai-marketing-page">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">智能营销助手</text>
      <view class="navbar-right">
        <!-- 已移除帮助按钮 -->
      </view>
    </view>
    
    <view class="content">
      <view class="header-card">
        <view class="title">AI驱动的营销策划工具</view>
        <view class="description">基于历史数据和行业洞察，智能生成最优营销策略与执行方案</view>
      </view>
      
      <view class="tool-section">
        <view class="section-title">智能分析工具</view>
        
        <view class="tool-grid">
          <!-- 消费趋势分析 -->
          <view class="tool-card" @tap="navigateTo('/subPackages/merchant-admin/pages/marketing/ai/trend-analysis')">
            <view class="tool-icon blue">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"/>
              </svg>
            </view>
            <view class="tool-content">
              <text class="tool-title">消费趋势分析</text>
              <text class="tool-desc">分析顾客消费习惯变化</text>
              <view class="tag-list">
                <view class="tag">AI预测</view>
                <view class="tag">数据分析</view>
              </view>
            </view>
          </view>
          
          <!-- 竞品价格监测 -->
          <view class="tool-card" @tap="navigateTo('/subPackages/merchant-admin/pages/marketing/ai/price-monitor')">
            <view class="tool-icon orange">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
              </svg>
            </view>
            <view class="tool-content">
              <text class="tool-title">竞品价格监测</text>
              <text class="tool-desc">实时监控市场价格变化</text>
              <view class="tag-list">
                <view class="tag">市场分析</view>
                <view class="tag">价格策略</view>
              </view>
            </view>
          </view>
          
          <!-- 销售预测模型 -->
          <view class="tool-card" @tap="navigateTo('/subPackages/merchant-admin/pages/marketing/ai/sales-forecast')">
            <view class="tool-icon green">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
              </svg>
            </view>
            <view class="tool-content">
              <text class="tool-title">销售预测模型</text>
              <text class="tool-desc">预测未来销售趋势</text>
              <view class="tag-list">
                <view class="tag">AI预测</view>
                <view class="tag">库存管理</view>
              </view>
            </view>
          </view>
          
          <!-- 客户画像分析 -->
          <view class="tool-card" @tap="navigateTo('/subPackages/merchant-admin/pages/marketing/ai/customer-profile')">
            <view class="tool-icon purple">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </view>
            <view class="tool-content">
              <text class="tool-title">客户画像分析</text>
              <text class="tool-desc">深入了解客户特征</text>
              <view class="tag-list">
                <view class="tag">用户分析</view>
                <view class="tag">精准营销</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="insights-section">
        <view class="section-title">智能营销洞察</view>
        
        <view class="insights-list">
          <view class="insight-card">
            <view class="insight-icon insight">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="16"></line>
                <line x1="12" y1="16" x2="12" y2="16"></line>
              </svg>
            </view>
            <view class="insight-content">
              <text class="insight-title">客群扩展机会</text>
              <text class="insight-desc">您当前的25-35岁女性客户转化率较高，建议针对此群体增加营销预算，预计可提高20%销售额</text>
            </view>
            <view class="insight-actions">
              <view class="action-btn apply">应用</view>
              <view class="action-btn ignore">忽略</view>
            </view>
          </view>
          
          <view class="insight-card">
            <view class="insight-icon warning">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
            </view>
            <view class="insight-content">
              <text class="insight-title">活动优化建议</text>
              <text class="insight-desc">满300减50活动的转化率低于平均水平，建议调整为满200减40，预计可提高15%的转化</text>
            </view>
            <view class="insight-actions">
              <view class="action-btn apply">应用</view>
              <view class="action-btn ignore">忽略</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    navigateTo(url) {
      uni.navigateTo({
        url: url,
        fail: (err) => {
          console.error('导航失败:', err);
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      });
    },
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '智能营销助手帮助',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-marketing-page {
  min-height: 100vh;
  background-color: #f5f6fa;
  
  .navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    height: 180rpx; /* 固定高度包含状态栏和关闭按钮, +20rpx (10px) */
    padding-top: var(--status-bar-height);
    background: #6366F1;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    z-index: 999;
    
    .navbar-back {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      
      .back-icon {
        width: 20rpx;
        height: 20rpx;
        border-top: 3rpx solid #ffffff;
        border-left: 3rpx solid #ffffff;
        transform: rotate(-45deg);
      }
    }
    
    .navbar-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #ffffff;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, 30%); /* 向下调整以对齐关闭键, 适应更高的导航栏 */
      white-space: nowrap;
    }
    
    .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: flex-end;
      
      .help-icon {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        border: 2rpx solid #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 28rpx;
      }
    }
  }
  
  .content {
    padding: 30rpx;
    padding-top: 15rpx;
  }
  
  .header-card {
    background: linear-gradient(135deg, #6366F1 0%, #818CF8 100%);
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    color: #ffffff;
    margin-bottom: 30rpx;
    box-shadow: 0 10rpx 20rpx rgba(99, 102, 241, 0.1);
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
    }
    
    .description {
      font-size: 26rpx;
      opacity: 0.8;
    }
  }
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    color: #333333;
  }
  
  .tool-section {
    margin-bottom: 30rpx;
  }
  
  .tool-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
  }
  
  .tool-card {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    
    .tool-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15rpx;
      
      svg {
        color: #ffffff;
      }
      
      &.blue {
        background-color: #1989FA;
      }
      
      &.orange {
        background-color: #FF9500;
      }
      
      &.green {
        background-color: #34C759;
      }
      
      &.purple {
        background-color: #A855F7;
      }
    }
    
    .tool-content {
      .tool-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
        margin-bottom: 6rpx;
        display: block;
      }
      
      .tool-desc {
        font-size: 24rpx;
        color: #999999;
        margin-bottom: 10rpx;
        display: block;
      }
      
      .tag-list {
        display: flex;
        flex-wrap: wrap;
        
        .tag {
          font-size: 20rpx;
          color: #6366F1;
          background-color: rgba(99, 102, 241, 0.1);
          padding: 4rpx 12rpx;
          border-radius: 10rpx;
          margin-right: 10rpx;
          margin-bottom: 6rpx;
        }
      }
    }
  }
  
  .insights-section {
    margin-bottom: 30rpx;
  }
  
  .insights-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
  
  .insight-card {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    display: flex;
    
    .insight-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15rpx;
      flex-shrink: 0;
      
      svg {
        color: #ffffff;
        width: 24rpx;
        height: 24rpx;
      }
      
      &.insight {
        background-color: #1989FA;
      }
      
      &.warning {
        background-color: #FF9500;
      }
      
      &.opportunity {
        background-color: #34C759;
      }
    }
    
    .insight-content {
      flex: 1;
      
      .insight-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
        margin-bottom: 6rpx;
        display: block;
      }
      
      .insight-desc {
        font-size: 24rpx;
        color: #666666;
        display: block;
      }
    }
    
    .insight-actions {
      display: flex;
      flex-direction: column;
      gap: 10rpx;
      margin-left: 15rpx;
      
      .action-btn {
        font-size: 24rpx;
        padding: 6rpx 16rpx;
        border-radius: 10rpx;
        text-align: center;
        
        &.apply {
          background-color: #6366F1;
          color: #ffffff;
        }
        
        &.ignore {
          border: 1rpx solid #E5E7EB;
          color: #666666;
        }
      }
    }
  }
}
</style>
<!-- 智能营销助手页面结束 -->
