"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Math) {
  ActivityCard();
}
const ActivityCard = () => "../ActivityCard.js";
const _sfc_main = {
  __name: "index",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const discountPercent = common_vendor.computed(() => {
      if (!props.item.salePrice || !props.item.originalPrice)
        return "";
      return Math.floor(props.item.salePrice / props.item.originalPrice * 10);
    });
    const progressWidth = common_vendor.computed(() => {
      if (!props.item.soldCount || !props.item.totalStock)
        return 0;
      return props.item.soldCount / props.item.totalStock * 100;
    });
    const remainStock = common_vendor.computed(() => {
      if (!props.item.soldCount || !props.item.totalStock)
        return 0;
      return props.item.totalStock - props.item.soldCount;
    });
    const countdownHours = common_vendor.ref("00");
    const countdownMinutes = common_vendor.ref("00");
    const countdownSeconds = common_vendor.ref("00");
    const timeStatus = common_vendor.ref("火热抢购中");
    let countdownTimer = null;
    const updateCountdown = () => {
      if (!props.item.endTime)
        return;
      const now = /* @__PURE__ */ new Date();
      const targetTime = props.item.status === "ongoing" ? new Date(props.item.endTime) : new Date(props.item.startTime);
      const timeDiff = targetTime - now;
      if (timeDiff <= 0) {
        countdownHours.value = "00";
        countdownMinutes.value = "00";
        countdownSeconds.value = "00";
        if (props.item.status === "ongoing") {
          timeStatus.value = "已结束";
        } else {
          timeStatus.value = "已开始";
        }
        if (countdownTimer) {
          clearInterval(countdownTimer);
        }
        return;
      }
      const hours = Math.floor(timeDiff / (1e3 * 60 * 60));
      const minutes = Math.floor(timeDiff % (1e3 * 60 * 60) / (1e3 * 60));
      const seconds = Math.floor(timeDiff % (1e3 * 60) / 1e3);
      countdownHours.value = hours.toString().padStart(2, "0");
      countdownMinutes.value = minutes.toString().padStart(2, "0");
      countdownSeconds.value = seconds.toString().padStart(2, "0");
      if (props.item.status === "ongoing") {
        if (hours > 0) {
          timeStatus.value = "火热抢购中";
        } else if (minutes > 30) {
          timeStatus.value = "火热抢购中";
        } else if (minutes > 10) {
          timeStatus.value = "即将结束";
        } else {
          timeStatus.value = "最后机会";
        }
      } else {
        if (hours > 24) {
          timeStatus.value = "即将开始";
        } else if (hours > 0) {
          timeStatus.value = "即将开始";
        } else if (minutes > 30) {
          timeStatus.value = "即将开始";
        } else {
          timeStatus.value = "马上开始";
        }
      }
    };
    common_vendor.onMounted(() => {
      updateCountdown();
      countdownTimer = setInterval(updateCountdown, 1e3);
    });
    common_vendor.onUnmounted(() => {
      if (countdownTimer) {
        clearInterval(countdownTimer);
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(__props.item.salePrice),
        b: common_vendor.t(__props.item.originalPrice),
        c: common_vendor.t(discountPercent.value),
        d: __props.item.status === "ongoing" || __props.item.status === "upcoming"
      }, __props.item.status === "ongoing" || __props.item.status === "upcoming" ? {
        e: common_vendor.t(__props.item.status === "ongoing" ? "距结束" : "距开始"),
        f: common_vendor.t(timeStatus.value),
        g: common_vendor.t(countdownHours.value),
        h: common_vendor.t(countdownMinutes.value),
        i: common_vendor.t(countdownSeconds.value)
      } : {}, {
        j: common_vendor.t(__props.item.soldCount),
        k: common_vendor.t(__props.item.totalStock),
        l: progressWidth.value + "%",
        m: __props.item.status === "ongoing" && remainStock.value > 0
      }, __props.item.status === "ongoing" && remainStock.value > 0 ? {
        n: common_vendor.t(remainStock.value)
      } : __props.item.status === "ongoing" && remainStock.value <= 0 ? {} : {}, {
        o: __props.item.status === "ongoing" && remainStock.value <= 0,
        p: common_vendor.o(($event) => _ctx.$emit("favorite", __props.item.id)),
        q: common_vendor.o(($event) => _ctx.$emit("action", {
          id: __props.item.id,
          type: __props.item.type,
          status: __props.item.status
        })),
        r: common_vendor.p({
          item: __props.item
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0b7110be"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/activity/FlashSaleCard/index.js.map
