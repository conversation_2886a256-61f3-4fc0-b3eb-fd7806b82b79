<template>
  <view>
    <!-- 悬浮推广按钮 -->
    <view
      class="float-promotion-button"
      :style="{
        right: position.right,
        bottom: position.bottom,
        width: size,
        height: size
      }"
      @click="showPromotionModal = true"
    >
      <image class="button-icon" :src="icon" mode="aspectFit"></image>
    </view>

    <!-- 推广操作弹窗 -->
    <view v-if="showPromotionModal" class="promotion-modal-mask" @click="showPromotionModal = false">
      <view class="promotion-modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">推广工具</text>
          <view class="modal-close" @click="showPromotionModal = false">
            <text>×</text>
          </view>
        </view>
        <ConfigurablePremiumActions
          :pageType="pageType"
          :showMode="showMode"
          :itemData="itemData"
          @action-completed="handleActionCompleted"
          @action-cancelled="handleActionCancelled"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// 定义组件属性
const props = defineProps({
  // 按钮位置
  position: {
    type: Object,
    default: () => ({
      right: '30rpx',
      bottom: '180rpx'
    })
  },
  // 按钮大小
  size: {
    type: String,
    default: '80rpx'
  },
  // 按钮图标
  icon: {
    type: String,
    default: '/static/images/promotion-icon.svg'
  },
  // 页面类型
  pageType: {
    type: String,
    default: 'publish'
  },
  // 显示模式
  showMode: {
    type: String,
    default: 'direct'
  },
  // 项目数据
  itemData: {
    type: Object,
    default: () => ({
      id: '',
      title: '推广',
      description: '提升曝光度'
    })
  }
});

// 定义事件
const emit = defineEmits(['action-completed', 'action-cancelled']);

// 控制弹窗显示
const showPromotionModal = ref(false);

// 处理操作完成
const handleActionCompleted = (result) => {
  console.log('推广操作完成:', result);
  showPromotionModal.value = false;
  emit('action-completed', result);
};

// 处理操作取消
const handleActionCancelled = (result) => {
  console.log('推广操作取消:', result);
  showPromotionModal.value = false;
  emit('action-cancelled', result);
};
</script>

<style lang="scss" scoped>
.float-promotion-button {
  position: fixed;
  z-index: 999;
  border-radius: 50%;
  background: linear-gradient(135deg, #3846cd, #2c3aa0);
  box-shadow: 0 4rpx 10rpx rgba(56, 70, 205, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.button-icon {
  width: 50%;
  height: 50%;
}

/* 推广弹窗样式 */
.promotion-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.promotion-modal-content {
  background: #fff;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  border-radius: 50%;
  background: #f5f5f5;
}
</style> 