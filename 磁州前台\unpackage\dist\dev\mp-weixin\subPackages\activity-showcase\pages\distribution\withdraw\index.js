"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const availableBalance = common_vendor.ref("311.11");
    const withdrawLimit = common_vendor.ref("10.00");
    const withdrawCount = common_vendor.ref(1);
    const maxWithdrawCount = common_vendor.ref(3);
    const processingDays = common_vendor.ref("1-3天");
    const withdrawAmount = common_vendor.ref("");
    const withdrawMethods = common_vendor.ref([
      {
        name: "微信零钱",
        description: "提现到微信零钱，实时到账",
        icon: "M6 9h12v12H6z M6 5V3h12v2 M16 11v8 M12 11v8 M8 11v8",
        iconColor: "#07C160",
        iconBg: "rgba(7,193,96,0.1)"
      },
      {
        name: "支付宝",
        description: "提现到支付宝账户，实时到账",
        icon: "M22 8v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z M6 12l4 4 8-8",
        iconColor: "#1677FF",
        iconBg: "rgba(22,119,255,0.1)"
      },
      {
        name: "银行卡",
        description: "提现到银行卡，1-3个工作日到账",
        icon: "M3 10h18M7 15h1m4 0h1m4 0h1M21 4v16a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4",
        iconColor: "#FF9500",
        iconBg: "rgba(255,149,0,0.1)"
      }
    ]);
    const currentMethod = common_vendor.ref(0);
    const accountInfo = common_vendor.ref({
      avatar: "https://via.placeholder.com/80",
      name: "张三",
      number: "微信账号已绑定"
    });
    const withdrawNotice = common_vendor.ref("1. 提现金额不低于10元，单日最高提现金额为5000元；\n2. 每月可免费提现3次，超出次数将收取1%手续费；\n3. 提现申请提交后，将在1-3个工作日内处理完成；\n4. 如有问题，请联系客服。");
    const withdrawRecords = common_vendor.ref([
      {
        method: "微信零钱",
        amount: "100.00",
        time: "2023-05-15 14:30:25",
        status: "success"
      },
      {
        method: "支付宝",
        amount: "200.00",
        time: "2023-05-10 09:15:36",
        status: "processing"
      },
      {
        method: "银行卡",
        amount: "500.00",
        time: "2023-05-01 16:42:18",
        status: "failed"
      }
    ]);
    const isFormValid = common_vendor.computed(() => {
      const amount = parseFloat(withdrawAmount.value);
      const limit = parseFloat(withdrawLimit.value);
      return amount && amount >= limit && amount <= parseFloat(availableBalance.value);
    });
    function selectMethod(index) {
      currentMethod.value = index;
      if (index === 0) {
        accountInfo.value.name = "张三";
        accountInfo.value.number = "微信账号已绑定";
      } else if (index === 1) {
        accountInfo.value.name = "张三";
        accountInfo.value.number = "支付宝账号已绑定";
      } else {
        accountInfo.value.name = "张三";
        accountInfo.value.number = "工商银行 (1234)";
      }
    }
    function setMaxAmount() {
      withdrawAmount.value = availableBalance.value;
    }
    function changeAccount() {
      common_vendor.index.showToast({
        title: "功能开发中",
        icon: "none"
      });
    }
    function submitWithdraw() {
      if (!isFormValid.value) {
        common_vendor.index.showToast({
          title: "请检查提现金额",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "提现申请已提交",
          icon: "success"
        });
        withdrawAmount.value = "";
        const newRecord = {
          method: withdrawMethods.value[currentMethod.value].name,
          amount: withdrawAmount.value,
          time: (/* @__PURE__ */ new Date()).toLocaleString(),
          status: "processing"
        };
        withdrawRecords.value.unshift(newRecord);
      }, 1500);
    }
    function getStatusColor(status, alpha = 1) {
      switch (status) {
        case "success":
          return alpha === 1 ? "#34C759" : "rgba(52,199,89," + alpha + ")";
        case "processing":
          return alpha === 1 ? "#007AFF" : "rgba(0,122,255," + alpha + ")";
        case "failed":
          return alpha === 1 ? "#FF3B30" : "rgba(255,59,48," + alpha + ")";
        default:
          return alpha === 1 ? "#8E8E93" : "rgba(142,142,147," + alpha + ")";
      }
    }
    function getStatusIcon(status) {
      switch (status) {
        case "success":
          return "M22 11.08V12a10 10 0 1 1-5.93-9.14";
        case "processing":
          return "M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z";
        case "failed":
          return "M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z";
        default:
          return "M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z";
      }
    }
    function getStatusText(status) {
      switch (status) {
        case "success":
          return "提现成功";
        case "processing":
          return "处理中";
        case "failed":
          return "提现失败";
        default:
          return "未知状态";
      }
    }
    function navigateTo(url) {
      common_vendor.index.navigateTo({ url });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(availableBalance.value),
        b: common_vendor.t(withdrawLimit.value),
        c: common_vendor.t(withdrawCount.value),
        d: common_vendor.t(maxWithdrawCount.value),
        e: common_vendor.t(processingDays.value),
        f: withdrawAmount.value,
        g: common_vendor.o(($event) => withdrawAmount.value = $event.detail.value),
        h: common_vendor.t(withdrawLimit.value),
        i: common_vendor.o(setMaxAmount),
        j: common_vendor.f(withdrawMethods.value, (method, index, i0) => {
          return common_vendor.e({
            a: "13649c72-1-" + i0 + "," + ("13649c72-0-" + i0),
            b: common_vendor.p({
              d: method.icon,
              stroke: method.iconColor,
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            c: "13649c72-0-" + i0,
            d: method.iconBg,
            e: common_vendor.t(method.name),
            f: common_vendor.t(method.description),
            g: currentMethod.value === index
          }, currentMethod.value === index ? {
            h: "13649c72-3-" + i0 + "," + ("13649c72-2-" + i0),
            i: common_vendor.p({
              d: "M5 12l5 5L20 7",
              stroke: "#FFFFFF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            j: "13649c72-2-" + i0,
            k: common_vendor.p({
              viewBox: "0 0 24 24",
              width: "16",
              height: "16"
            })
          } : {}, {
            l: currentMethod.value === index ? "0" : "1rpx solid #CCCCCC",
            m: currentMethod.value === index ? "#AC39FF" : "transparent",
            n: index,
            o: currentMethod.value === index ? 1 : "",
            p: common_vendor.o(($event) => selectMethod(index), index),
            q: currentMethod.value === index ? "rgba(172,57,255,0.1)" : "#F8F8F8",
            r: currentMethod.value === index ? "1rpx solid #AC39FF" : "1rpx solid transparent"
          });
        }),
        k: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        l: accountInfo.value.avatar,
        m: common_vendor.t(accountInfo.value.name),
        n: common_vendor.t(accountInfo.value.number),
        o: common_vendor.o(changeAccount),
        p: common_vendor.p({
          d: "M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        q: common_vendor.p({
          d: "M12 8v4M12 16h.01",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        r: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "20",
          height: "20"
        }),
        s: common_vendor.t(withdrawNotice.value),
        t: common_vendor.o(submitWithdraw),
        v: !isFormValid.value,
        w: isFormValid.value ? "linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)" : "#CCCCCC",
        x: isFormValid.value ? "0 5px 15px rgba(172,57,255,0.3)" : "none",
        y: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#AC39FF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        z: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        A: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/distribution/records/index")),
        B: common_vendor.f(withdrawRecords.value, (record, index, i0) => {
          return {
            a: "13649c72-10-" + i0 + "," + ("13649c72-9-" + i0),
            b: common_vendor.p({
              d: getStatusIcon(record.status),
              stroke: getStatusColor(record.status),
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            c: "13649c72-9-" + i0,
            d: getStatusColor(record.status, 0.1),
            e: common_vendor.t(record.method),
            f: common_vendor.t(record.amount),
            g: common_vendor.t(record.time),
            h: common_vendor.t(getStatusText(record.status)),
            i: getStatusColor(record.status),
            j: index,
            k: index < withdrawRecords.value.length - 1 ? "1rpx solid #F2F2F7" : "none"
          };
        }),
        C: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        D: withdrawRecords.value.length === 0
      }, withdrawRecords.value.length === 0 ? {
        E: common_assets._imports_0$59
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-13649c72"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/distribution/withdraw/index.js.map
