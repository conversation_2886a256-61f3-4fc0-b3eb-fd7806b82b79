{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/history/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcaGlzdG9yeVxpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"history-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{\r\n      position: 'fixed',\r\n      top: 0,\r\n      left: 0,\r\n      right: 0,\r\n      height: 'calc(var(--status-bar-height, 25px) + 62px)',\r\n      width: '100%',\r\n      zIndex: 100\r\n    }\">\r\n      <view class=\"navbar-bg\" :style=\"{\r\n        position: 'absolute',\r\n        top: 0,\r\n        left: 0,\r\n        width: '100%',\r\n        height: '100%',\r\n        background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\r\n        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'\r\n      }\"></view>\r\n      \r\n      <view class=\"navbar-content\" :style=\"{\r\n        position: 'relative',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'space-between',\r\n        height: '100%',\r\n        padding: '0 30rpx',\r\n        paddingTop: 'var(--status-bar-height, 25px)',\r\n        boxSizing: 'border-box'\r\n      }\">\r\n        <view class=\"back-btn\" @click=\"goBack\" :style=\"{\r\n          width: '80rpx',\r\n          height: '80rpx',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center'\r\n        }\">\r\n          <image src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\" :style=\"{\r\n            width: '40rpx',\r\n            height: '40rpx'\r\n          }\"></image>\r\n        </view>\r\n        \r\n        <text class=\"navbar-title\" :style=\"{\r\n          fontSize: '36rpx',\r\n          fontWeight: '600',\r\n          color: '#FFFFFF'\r\n        }\">浏览记录</text>\r\n        \r\n        <!-- 占位元素，保持布局平衡 -->\r\n        <view style=\"width: 80rpx;\"></view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content-area\" :style=\"{\r\n      paddingTop: 'calc(var(--status-bar-height, 25px) + 62px)',\r\n      minHeight: '100vh',\r\n      boxSizing: 'border-box'\r\n    }\">\r\n      <!-- 分类标签栏和清空按钮 -->\r\n      <view :style=\"{\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'space-between',\r\n        padding: '20rpx 30rpx',\r\n        background: '#FFFFFF',\r\n        marginBottom: '20rpx',\r\n        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'\r\n      }\">\r\n        <scroll-view \r\n          scroll-x \r\n          class=\"category-tabs\" \r\n          :style=\"{\r\n            whiteSpace: 'nowrap',\r\n            flex: 1,\r\n            overflow: 'hidden'\r\n          }\"\r\n        >\r\n          <view \r\n            v-for=\"(category, index) in categories\" \r\n            :key=\"index\"\r\n            class=\"category-item\"\r\n            :class=\"{ active: currentCategory === index }\"\r\n            @click=\"switchCategory(index)\"\r\n            :style=\"{\r\n              display: 'inline-block',\r\n              padding: '10rpx 30rpx',\r\n              marginRight: '20rpx',\r\n              borderRadius: '30rpx',\r\n              fontSize: '28rpx',\r\n              color: currentCategory === index ? '#FFFFFF' : '#666666',\r\n              background: currentCategory === index ? 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)' : '#F2F2F7',\r\n              boxShadow: currentCategory === index ? '0 4px 10px rgba(255,59,105,0.2)' : 'none'\r\n            }\"\r\n          >\r\n            {{ category.name }}\r\n          </view>\r\n        </scroll-view>\r\n        \r\n        <!-- 清空按钮移到这里 -->\r\n        <view class=\"clear-btn\" @click=\"showClearConfirm\" :style=\"{\r\n          marginLeft: '20rpx',\r\n          padding: '10rpx 30rpx',\r\n          borderRadius: '30rpx',\r\n          fontSize: '28rpx',\r\n          fontWeight: '500',\r\n          background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\r\n          color: '#FFFFFF',\r\n          boxShadow: '0 4px 10px rgba(255,59,105,0.2)'\r\n        }\">\r\n          清空记录\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 日期分组 -->\r\n      <view class=\"history-list\" :style=\"{\r\n        padding: '0 20rpx'\r\n      }\">\r\n        <view \r\n          v-for=\"(group, groupIndex) in groupedHistory\" \r\n          :key=\"groupIndex\"\r\n          class=\"history-group\"\r\n          :style=\"{\r\n            marginBottom: '30rpx'\r\n          }\"\r\n        >\r\n          <!-- 日期标题 -->\r\n          <view class=\"date-header\" :style=\"{\r\n            padding: '10rpx 20rpx',\r\n            fontSize: '28rpx',\r\n            fontWeight: '500',\r\n            color: '#333333',\r\n            marginBottom: '10rpx'\r\n          }\">\r\n            {{ group.date }}\r\n          </view>\r\n          \r\n          <!-- 浏览记录项 -->\r\n          <view \r\n            v-for=\"(item, itemIndex) in group.items\" \r\n            :key=\"itemIndex\"\r\n            class=\"history-item\"\r\n            :style=\"{\r\n              background: '#FFFFFF',\r\n              borderRadius: '20rpx',\r\n              marginBottom: '20rpx',\r\n              overflow: 'hidden',\r\n              boxShadow: '0 4px 10px rgba(0,0,0,0.05)'\r\n            }\"\r\n          >\r\n            <!-- 内容区域 -->\r\n            <view class=\"item-content\" @click=\"viewDetail(item)\" :style=\"{\r\n              display: 'flex',\r\n              padding: '20rpx'\r\n            }\">\r\n              <image \r\n                :src=\"item.image\" \r\n                mode=\"aspectFill\" \r\n                :style=\"{\r\n                  width: '200rpx',\r\n                  height: '200rpx',\r\n                  borderRadius: '10rpx',\r\n                  marginRight: '20rpx'\r\n                }\"\r\n              ></image>\r\n              \r\n              <view class=\"item-info\" :style=\"{\r\n                flex: '1',\r\n                display: 'flex',\r\n                flexDirection: 'column',\r\n                justifyContent: 'space-between'\r\n              }\">\r\n                <view class=\"item-top\">\r\n                  <text class=\"item-title\" :style=\"{\r\n                    fontSize: '28rpx',\r\n                    fontWeight: '500',\r\n                    color: '#333333',\r\n                    marginBottom: '10rpx',\r\n                    display: 'block',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis',\r\n                    display: '-webkit-box',\r\n                    '-webkit-line-clamp': '2',\r\n                    '-webkit-box-orient': 'vertical',\r\n                    lineHeight: '1.4'\r\n                  }\">{{ item.title }}</text>\r\n                  \r\n                  <text class=\"item-shop\" :style=\"{\r\n                    fontSize: '24rpx',\r\n                    color: '#999999',\r\n                    marginBottom: '10rpx',\r\n                    display: 'block'\r\n                  }\">{{ item.shop }}</text>\r\n                </view>\r\n                \r\n                <view class=\"item-bottom\" :style=\"{\r\n                  display: 'flex',\r\n                  justifyContent: 'space-between',\r\n                  alignItems: 'flex-end'\r\n                }\">\r\n                  <view class=\"price-info\">\r\n                    <view class=\"current-price\" :style=\"{\r\n                      display: 'flex',\r\n                      alignItems: 'baseline'\r\n                    }\">\r\n                      <text :style=\"{\r\n                        fontSize: '24rpx',\r\n                        color: '#FF3B69',\r\n                        marginRight: '5rpx'\r\n                      }\">¥</text>\r\n                      <text :style=\"{\r\n                        fontSize: '32rpx',\r\n                        fontWeight: '600',\r\n                        color: '#FF3B69'\r\n                      }\">{{ item.price }}</text>\r\n                    </view>\r\n                    \r\n                    <text v-if=\"item.originalPrice\" class=\"original-price\" :style=\"{\r\n                      fontSize: '24rpx',\r\n                      color: '#999999',\r\n                      textDecoration: 'line-through'\r\n                    }\">¥{{ item.originalPrice }}</text>\r\n                  </view>\r\n                  \r\n                  <view class=\"item-tag\" :style=\"{\r\n                    padding: '6rpx 16rpx',\r\n                    borderRadius: '20rpx',\r\n                    background: getTagBackground(item.type),\r\n                    color: '#FFFFFF',\r\n                    fontSize: '22rpx',\r\n                    fontWeight: '500'\r\n                  }\">\r\n                    {{ getTagText(item.type) }}\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 浏览时间和操作 -->\r\n            <view class=\"item-footer\" :style=\"{\r\n              padding: '10rpx 20rpx',\r\n              borderTop: '1rpx solid #F2F2F7',\r\n              display: 'flex',\r\n              justifyContent: 'space-between',\r\n              alignItems: 'center'\r\n            }\">\r\n              <text :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#999999'\r\n              }\">{{ item.viewTime }}</text>\r\n              \r\n              <view class=\"action-btns\" :style=\"{\r\n                display: 'flex',\r\n                alignItems: 'center'\r\n              }\">\r\n                <view \r\n                  class=\"action-btn favorite\" \r\n                  @click.stop=\"toggleFavorite(item)\"\r\n                  :style=\"{\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    marginRight: '20rpx'\r\n                  }\"\r\n                >\r\n                  <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" :style=\"{ marginRight: '5rpx' }\">\r\n                    <path \r\n                      d=\"M19 21l-7-5-7 5V5a2 2 0 012-2h10a2 2 0 012 2v16z\" \r\n                      :fill=\"item.isFavorite ? '#FF3B69' : 'none'\"\r\n                      :stroke=\"item.isFavorite ? '#FF3B69' : '#999999'\" \r\n                      stroke-width=\"2\" \r\n                      stroke-linecap=\"round\" \r\n                      stroke-linejoin=\"round\"\r\n                    ></path>\r\n                  </svg>\r\n                  <text :style=\"{\r\n                    fontSize: '24rpx',\r\n                    color: item.isFavorite ? '#FF3B69' : '#999999'\r\n                  }\">{{ item.isFavorite ? '已收藏' : '收藏' }}</text>\r\n                </view>\r\n                \r\n                <view \r\n                  class=\"action-btn delete\" \r\n                  @click.stop=\"deleteHistory(item)\"\r\n                  :style=\"{\r\n                    display: 'flex',\r\n                    alignItems: 'center'\r\n                  }\"\r\n                >\r\n                  <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" :style=\"{ marginRight: '5rpx' }\">\r\n                    <path d=\"M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2M10 11v6M14 11v6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                  </svg>\r\n                  <text :style=\"{\r\n                    fontSize: '24rpx',\r\n                    color: '#999999'\r\n                  }\">删除</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 空状态 -->\r\n        <view v-if=\"filteredHistory.length === 0\" class=\"empty-state\" :style=\"{\r\n          padding: '100rpx 0',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          alignItems: 'center'\r\n        }\">\r\n          <image src=\"/static/images/empty/empty-history.png\" mode=\"aspectFit\" :style=\"{\r\n            width: '200rpx',\r\n            height: '200rpx',\r\n            marginBottom: '20rpx'\r\n          }\"></image>\r\n          <text :style=\"{\r\n            fontSize: '28rpx',\r\n            color: '#999999',\r\n            marginBottom: '30rpx'\r\n          }\">暂无浏览记录</text>\r\n          \r\n          <view class=\"action-btn\" @click=\"goExplore\" :style=\"{\r\n            padding: '16rpx 40rpx',\r\n            borderRadius: '35px',\r\n            background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',\r\n            color: '#FFFFFF',\r\n            fontSize: '28rpx',\r\n            boxShadow: '0 5px 15px rgba(255,59,105,0.3)'\r\n          }\">\r\n            去探索\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 加载更多 -->\r\n        <view v-if=\"filteredHistory.length > 0 && !noMore\" class=\"load-more\" :style=\"{\r\n          textAlign: 'center',\r\n          padding: '30rpx 0'\r\n        }\">\r\n          <text v-if=\"loading\" :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#999999'\r\n          }\">加载中...</text>\r\n          <text v-else @click=\"loadMore\" :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#FF3B69'\r\n          }\">加载更多</text>\r\n        </view>\r\n        \r\n        <!-- 没有更多数据 -->\r\n        <view v-if=\"filteredHistory.length > 0 && noMore\" class=\"no-more\" :style=\"{\r\n          textAlign: 'center',\r\n          padding: '30rpx 0'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#999999'\r\n          }\">没有更多记录了</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\n\r\n// 分类数据\r\nconst categories = ref([\r\n  { id: 0, name: '全部' },\r\n  { id: 1, name: '活动' },\r\n  { id: 2, name: '商品' },\r\n  { id: 3, name: '优惠券' },\r\n  { id: 4, name: '拼团' },\r\n  { id: 5, name: '秒杀' }\r\n]);\r\nconst currentCategory = ref(0);\r\n\r\n// 加载状态\r\nconst loading = ref(false);\r\nconst noMore = ref(false);\r\n\r\n// 浏览记录数据\r\nconst historyList = ref([\r\n  {\r\n    id: 1,\r\n    title: 'Apple iPhone 14 Pro Max 256GB 暗夜紫 移动联通电信5G双卡双待手机',\r\n    image: 'https://via.placeholder.com/200',\r\n    shop: 'Apple官方旗舰店',\r\n    price: '8999.00',\r\n    originalPrice: '9999.00',\r\n    type: 'product',\r\n    viewTime: '14:30',\r\n    date: '2023-05-15',\r\n    isFavorite: true\r\n  },\r\n  {\r\n    id: 2,\r\n    title: '618年中大促全场低至5折起',\r\n    image: 'https://via.placeholder.com/200',\r\n    shop: '京东自营',\r\n    price: '0.00',\r\n    originalPrice: '',\r\n    type: 'activity',\r\n    viewTime: '09:15',\r\n    date: '2023-05-15',\r\n    isFavorite: false\r\n  },\r\n  {\r\n    id: 3,\r\n    title: '满300减50全场优惠券',\r\n    image: 'https://via.placeholder.com/200',\r\n    shop: '京东自营',\r\n    price: '50.00',\r\n    originalPrice: '',\r\n    type: 'coupon',\r\n    viewTime: '16:42',\r\n    date: '2023-05-14',\r\n    isFavorite: false\r\n  },\r\n  {\r\n    id: 4,\r\n    title: '小米12S Ultra 12GB+256GB 丹青黑 骁龙8+旗舰处理器 徕卡专业光学镜头',\r\n    image: 'https://via.placeholder.com/200',\r\n    shop: '小米官方旗舰店',\r\n    price: '5999.00',\r\n    originalPrice: '6999.00',\r\n    type: 'product',\r\n    viewTime: '11:23',\r\n    date: '2023-05-14',\r\n    isFavorite: true\r\n  },\r\n  {\r\n    id: 5,\r\n    title: '3人团：小米空气净化器',\r\n    image: 'https://via.placeholder.com/200',\r\n    shop: '小米官方旗舰店',\r\n    price: '699.00',\r\n    originalPrice: '999.00',\r\n    type: 'group',\r\n    viewTime: '18:05',\r\n    date: '2023-05-13',\r\n    isFavorite: false\r\n  },\r\n  {\r\n    id: 6,\r\n    title: '限时秒杀：iPhone 14 Pro',\r\n    image: 'https://via.placeholder.com/200',\r\n    shop: 'Apple授权专卖店',\r\n    price: '6999.00',\r\n    originalPrice: '8999.00',\r\n    type: 'flash',\r\n    viewTime: '10:30',\r\n    date: '2023-05-12',\r\n    isFavorite: false\r\n  }\r\n]);\r\n\r\n// 过滤后的浏览记录\r\nconst filteredHistory = computed(() => {\r\n  if (currentCategory.value === 0) {\r\n    return historyList.value;\r\n  } else {\r\n    const categoryMap = {\r\n      1: 'activity',\r\n      2: 'product',\r\n      3: 'coupon',\r\n      4: 'group',\r\n      5: 'flash'\r\n    };\r\n    return historyList.value.filter(item => item.type === categoryMap[currentCategory.value]);\r\n  }\r\n});\r\n\r\n// 按日期分组的浏览记录\r\nconst groupedHistory = computed(() => {\r\n  const groups = {};\r\n  \r\n  filteredHistory.value.forEach(item => {\r\n    if (!groups[item.date]) {\r\n      groups[item.date] = {\r\n        date: formatDate(item.date),\r\n        items: []\r\n      };\r\n    }\r\n    \r\n    groups[item.date].items.push(item);\r\n  });\r\n  \r\n  // 转换为数组并按日期排序\r\n  return Object.values(groups).sort((a, b) => {\r\n    const dateA = new Date(a.date.replace('今天', new Date().toISOString().split('T')[0])\r\n                                 .replace('昨天', new Date(Date.now() - 86400000).toISOString().split('T')[0])\r\n                                 .replace('前天', new Date(Date.now() - 2 * 86400000).toISOString().split('T')[0]));\r\n    const dateB = new Date(b.date.replace('今天', new Date().toISOString().split('T')[0])\r\n                                 .replace('昨天', new Date(Date.now() - 86400000).toISOString().split('T')[0])\r\n                                 .replace('前天', new Date(Date.now() - 2 * 86400000).toISOString().split('T')[0]));\r\n    return dateB - dateA;\r\n  });\r\n});\r\n\r\n// 返回上一页\r\nfunction goBack() {\r\n  uni.navigateBack();\r\n}\r\n\r\n// 切换分类\r\nfunction switchCategory(index) {\r\n  currentCategory.value = index;\r\n}\r\n\r\n// 查看详情\r\nfunction viewDetail(item) {\r\n  // 根据不同类型跳转到不同页面\r\n  let url = '';\r\n  \r\n  switch(item.type) {\r\n    case 'product':\r\n      url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=product`;\r\n      break;\r\n    case 'activity':\r\n      url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}&type=activity`;\r\n      break;\r\n    case 'coupon':\r\n      url = `/subPackages/activity-showcase/pages/coupon/detail?id=${item.id}`;\r\n      break;\r\n    case 'group':\r\n      url = `/subPackages/activity-showcase/pages/group-buy/detail?id=${item.id}`;\r\n      break;\r\n    case 'flash':\r\n      url = `/subPackages/activity-showcase/pages/flash-sale/detail?id=${item.id}`;\r\n      break;\r\n    default:\r\n      url = `/subPackages/activity-showcase/pages/detail/index?id=${item.id}`;\r\n  }\r\n  \r\n  uni.navigateTo({ url });\r\n}\r\n\r\n// 切换收藏状态\r\nfunction toggleFavorite(item) {\r\n  item.isFavorite = !item.isFavorite;\r\n  \r\n  uni.showToast({\r\n    title: item.isFavorite ? '已收藏' : '已取消收藏',\r\n    icon: 'success'\r\n  });\r\n}\r\n\r\n// 删除单条浏览记录\r\nfunction deleteHistory(item) {\r\n  uni.showModal({\r\n    title: '删除记录',\r\n    content: '确定要删除该浏览记录吗？',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        // 从浏览记录中移除\r\n        const index = historyList.value.findIndex(record => record.id === item.id);\r\n        if (index !== -1) {\r\n          historyList.value.splice(index, 1);\r\n        }\r\n        \r\n        uni.showToast({\r\n          title: '已删除',\r\n          icon: 'success'\r\n        });\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\n// 清空所有浏览记录\r\nfunction showClearConfirm() {\r\n  uni.showModal({\r\n    title: '清空记录',\r\n    content: '确定要清空所有浏览记录吗？',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        // 清空浏览记录\r\n        historyList.value = [];\r\n        \r\n        uni.showToast({\r\n          title: '已清空',\r\n          icon: 'success'\r\n        });\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\n// 前往探索\r\nfunction goExplore() {\r\n  uni.switchTab({\r\n    url: '/pages/index/index'\r\n  });\r\n}\r\n\r\n// 加载更多\r\nfunction loadMore() {\r\n  if (loading.value || noMore.value) return;\r\n  \r\n  loading.value = true;\r\n  \r\n  // 模拟加载更多数据\r\n  setTimeout(() => {\r\n    // 这里应该调用API获取更多数据\r\n    // 模拟没有更多数据\r\n    noMore.value = true;\r\n    loading.value = false;\r\n  }, 1500);\r\n}\r\n\r\n// 获取标签背景色\r\nfunction getTagBackground(type) {\r\n  switch (type) {\r\n    case 'product':\r\n      return 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)';\r\n    case 'activity':\r\n      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';\r\n    case 'coupon':\r\n      return 'linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%)';\r\n    case 'group':\r\n      return 'linear-gradient(135deg, #34C759 0%, #30D158 100%)';\r\n    case 'flash':\r\n      return 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)';\r\n    default:\r\n      return 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)';\r\n  }\r\n}\r\n\r\n// 获取标签文本\r\nfunction getTagText(type) {\r\n  switch (type) {\r\n    case 'product':\r\n      return '商品';\r\n    case 'activity':\r\n      return '活动';\r\n    case 'coupon':\r\n      return '优惠券';\r\n    case 'group':\r\n      return '拼团';\r\n    case 'flash':\r\n      return '秒杀';\r\n    default:\r\n      return '其他';\r\n  }\r\n}\r\n\r\n// 格式化日期\r\nfunction formatDate(dateStr) {\r\n  const today = new Date().toISOString().split('T')[0];\r\n  const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];\r\n  const beforeYesterday = new Date(Date.now() - 2 * 86400000).toISOString().split('T')[0];\r\n  \r\n  if (dateStr === today) {\r\n    return '今天';\r\n  } else if (dateStr === yesterday) {\r\n    return '昨天';\r\n  } else if (dateStr === beforeYesterday) {\r\n    return '前天';\r\n  } else {\r\n    return dateStr;\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.history-container {\r\n  min-height: 100vh;\r\n  background-color: #F8F8F8;\r\n}\r\n\r\n.back-btn:active, .clear-btn:active, .category-item:active, .action-btn:active {\r\n  opacity: 0.8;\r\n}\r\n\r\n.history-item {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.history-item:active {\r\n  transform: scale(0.98);\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/history/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni"], "mappings": ";;;;;;;;;;;AAgXA,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,MAAO;AAAA,MACtB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,IACvB,CAAC;AACD,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAG7B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,SAASA,cAAAA,IAAI,KAAK;AAGxB,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,eAAe;AAAA,QACf,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,YAAY;AAAA,MACb;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBC,cAAQ,SAAC,MAAM;AACrC,UAAI,gBAAgB,UAAU,GAAG;AAC/B,eAAO,YAAY;AAAA,MACvB,OAAS;AACL,cAAM,cAAc;AAAA,UAClB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACT;AACI,eAAO,YAAY,MAAM,OAAO,UAAQ,KAAK,SAAS,YAAY,gBAAgB,KAAK,CAAC;AAAA,MACzF;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AACpC,YAAM,SAAS,CAAA;AAEf,sBAAgB,MAAM,QAAQ,UAAQ;AACpC,YAAI,CAAC,OAAO,KAAK,IAAI,GAAG;AACtB,iBAAO,KAAK,IAAI,IAAI;AAAA,YAClB,MAAM,WAAW,KAAK,IAAI;AAAA,YAC1B,OAAO,CAAE;AAAA,UACjB;AAAA,QACK;AAED,eAAO,KAAK,IAAI,EAAE,MAAM,KAAK,IAAI;AAAA,MACrC,CAAG;AAGD,aAAO,OAAO,OAAO,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM;AAC1C,cAAM,QAAQ,IAAI,KAAK,EAAE,KAAK,QAAQ,OAAM,oBAAI,KAAM,GAAC,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,CAAC,EACpD,QAAQ,MAAM,IAAI,KAAK,KAAK,IAAK,IAAG,KAAQ,EAAE,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,CAAC,EACzE,QAAQ,MAAM,IAAI,KAAK,KAAK,IAAK,IAAG,IAAI,KAAQ,EAAE,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AAC5G,cAAM,QAAQ,IAAI,KAAK,EAAE,KAAK,QAAQ,OAAM,oBAAI,KAAM,GAAC,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,CAAC,EACpD,QAAQ,MAAM,IAAI,KAAK,KAAK,IAAK,IAAG,KAAQ,EAAE,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,CAAC,EACzE,QAAQ,MAAM,IAAI,KAAK,KAAK,IAAK,IAAG,IAAI,KAAQ,EAAE,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AAC5G,eAAO,QAAQ;AAAA,MACnB,CAAG;AAAA,IACH,CAAC;AAGD,aAAS,SAAS;AAChBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,aAAS,eAAe,OAAO;AAC7B,sBAAgB,QAAQ;AAAA,IAC1B;AAGA,aAAS,WAAW,MAAM;AAExB,UAAI,MAAM;AAEV,cAAO,KAAK,MAAI;AAAA,QACd,KAAK;AACH,gBAAM,wDAAwD,KAAK,EAAE;AACrE;AAAA,QACF,KAAK;AACH,gBAAM,wDAAwD,KAAK,EAAE;AACrE;AAAA,QACF,KAAK;AACH,gBAAM,yDAAyD,KAAK,EAAE;AACtE;AAAA,QACF,KAAK;AACH,gBAAM,4DAA4D,KAAK,EAAE;AACzE;AAAA,QACF,KAAK;AACH,gBAAM,6DAA6D,KAAK,EAAE;AAC1E;AAAA,QACF;AACE,gBAAM,wDAAwD,KAAK,EAAE;AAAA,MACxE;AAEDA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,aAAS,eAAe,MAAM;AAC5B,WAAK,aAAa,CAAC,KAAK;AAExBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,aAAa,QAAQ;AAAA,QACjC,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,cAAc,MAAM;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,kBAAM,QAAQ,YAAY,MAAM,UAAU,YAAU,OAAO,OAAO,KAAK,EAAE;AACzE,gBAAI,UAAU,IAAI;AAChB,0BAAY,MAAM,OAAO,OAAO,CAAC;AAAA,YAClC;AAEDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAS,mBAAmB;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,wBAAY,QAAQ;AAEpBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,aAAS,YAAY;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,aAAS,WAAW;AAClB,UAAI,QAAQ,SAAS,OAAO;AAAO;AAEnC,cAAQ,QAAQ;AAGhB,iBAAW,MAAM;AAGf,eAAO,QAAQ;AACf,gBAAQ,QAAQ;AAAA,MACjB,GAAE,IAAI;AAAA,IACT;AAGA,aAAS,iBAAiB,MAAM;AAC9B,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,aAAS,WAAW,MAAM;AACxB,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,aAAS,WAAW,SAAS;AAC3B,YAAM,SAAQ,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AACnD,YAAM,YAAY,IAAI,KAAK,KAAK,IAAG,IAAK,KAAQ,EAAE,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAC5E,YAAM,kBAAkB,IAAI,KAAK,KAAK,IAAK,IAAG,IAAI,KAAQ,EAAE,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC;AAEtF,UAAI,YAAY,OAAO;AACrB,eAAO;AAAA,MACX,WAAa,YAAY,WAAW;AAChC,eAAO;AAAA,MACX,WAAa,YAAY,iBAAiB;AACtC,eAAO;AAAA,MACX,OAAS;AACL,eAAO;AAAA,MACR;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtpBA,GAAG,WAAW,eAAe;"}