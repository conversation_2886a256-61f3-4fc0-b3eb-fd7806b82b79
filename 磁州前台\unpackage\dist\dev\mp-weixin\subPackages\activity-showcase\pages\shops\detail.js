"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_svg + _component_circle)();
}
const _sfc_main = {
  __name: "detail",
  setup(__props) {
    const product = common_vendor.ref({
      id: "1",
      name: "磁州特产手工小酥肉",
      price: 68,
      originalPrice: 88,
      sold: 1024,
      tags: ["限时特惠", "包邮", "48小时发货"],
      delivery: "免运费",
      services: ["7天无理由退货", "正品保证", "极速退款"],
      description: "<p>磁州特产手工小酥肉，传统工艺制作，选用上等五花肉，经过腌制、裹粉、油炸等多道工序精心制作而成。肉质酥脆，口感香浓，回味无穷。</p>",
      detailImages: [
        "/static/images/products/detail1.jpg",
        "/static/images/products/detail2.jpg",
        "/static/images/products/detail3.jpg"
      ],
      images: [
        "/static/images/products/product1-1.jpg",
        "/static/images/products/product1-2.jpg",
        "/static/images/products/product1-3.jpg"
      ],
      specs: [
        {
          name: "规格",
          options: ["小份(250g)", "中份(500g)", "大份(1000g)"]
        },
        {
          name: "口味",
          options: ["原味", "微辣", "麻辣"]
        }
      ],
      reviewCount: 238,
      reviews: [
        {
          name: "张先生",
          avatar: "/static/images/avatars/user1.jpg",
          rating: 5,
          date: "2023-06-15",
          content: "味道非常好，包装也很精美，送货速度快，下次还会再买！",
          specs: "规格：中份(500g) 口味：原味",
          images: [
            "/static/images/reviews/review1-1.jpg",
            "/static/images/reviews/review1-2.jpg"
          ]
        },
        {
          name: "李女士",
          avatar: "/static/images/avatars/user2.jpg",
          rating: 4,
          date: "2023-06-10",
          content: "肉质很嫩，味道不错，就是有点咸，下次尝试微辣口味。",
          specs: "规格：小份(250g) 口味：原味",
          images: []
        }
      ],
      shop: {
        id: "101",
        name: "磁州好味食品专营店",
        avatar: "/static/images/shops/shop1.jpg",
        rating: 4.8
      }
    });
    const recommendProducts = common_vendor.ref([
      {
        id: "2",
        name: "磁州传统手工豆腐脑",
        price: 12.8,
        sold: 3256,
        image: "/static/images/products/product2.jpg"
      },
      {
        id: "3",
        name: "磁州特色糖醋里脊",
        price: 48,
        sold: 1856,
        image: "/static/images/products/product3.jpg"
      },
      {
        id: "4",
        name: "磁州农家小炒肉",
        price: 38,
        sold: 2048,
        image: "/static/images/products/product4.jpg"
      }
    ]);
    const isFavorite = common_vendor.ref(false);
    const selectedSpecs = common_vendor.ref("");
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const shareProduct = () => {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    };
    const previewImage = (images, current) => {
      common_vendor.index.previewImage({
        urls: images,
        current: images[current]
      });
    };
    const viewShopDetail = () => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/shops/detail/index?id=${product.value.shop.id}`
      });
    };
    const viewProductDetail = (id) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/products/detail/index?id=${id}`
      });
    };
    const viewAllReviews = () => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/products/reviews?id=${product.value.id}`
      });
    };
    const showSpecsSelector = () => {
      common_vendor.index.showToast({
        title: "正在加载规格选择器...",
        icon: "none"
      });
    };
    const showDeliveryInfo = () => {
      common_vendor.index.showModal({
        title: "配送信息",
        content: "本商品支持全国配送，订单满99元免运费，不满99元收取10元运费。预计3-5天送达。",
        showCancel: false
      });
    };
    const showServiceInfo = () => {
      common_vendor.index.showModal({
        title: "服务说明",
        content: "7天无理由退货：商品签收后7天内，在不影响二次销售的情况下可申请无理由退货。\n正品保证：所有商品均为正品，假一赔十。\n极速退款：审核通过后24小时内退款到账。",
        showCancel: false
      });
    };
    const toggleFavorite = () => {
      isFavorite.value = !isFavorite.value;
      common_vendor.index.showToast({
        title: isFavorite.value ? "已收藏" : "已取消收藏",
        icon: "none"
      });
    };
    const contactService = () => {
      common_vendor.index.showToast({
        title: "正在接入客服系统...",
        icon: "none"
      });
    };
    const navigateToShop = () => {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/shops/detail/index?id=${product.value.shop.id}`
      });
    };
    const addToCart = () => {
      if (!selectedSpecs.value) {
        showSpecsSelector();
        return;
      }
      common_vendor.index.showToast({
        title: "已加入购物车",
        icon: "success"
      });
    };
    const buyNow = () => {
      if (!selectedSpecs.value) {
        showSpecsSelector();
        return;
      }
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/order/confirm?productId=${product.value.id}&specs=${encodeURIComponent(selectedSpecs.value)}`
      });
    };
    common_vendor.onMounted(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(navigateBack),
        d: common_vendor.p({
          d: "M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        f: common_vendor.o(shareProduct),
        g: common_vendor.f(product.value.images, (img, index, i0) => {
          return {
            a: img,
            b: common_vendor.o(($event) => previewImage(product.value.images, index), index),
            c: index
          };
        }),
        h: common_vendor.t(product.value.price),
        i: product.value.originalPrice
      }, product.value.originalPrice ? {
        j: common_vendor.t(product.value.originalPrice)
      } : {}, {
        k: common_vendor.t(product.value.sold),
        l: common_vendor.t(product.value.name),
        m: product.value.tags && product.value.tags.length
      }, product.value.tags && product.value.tags.length ? {
        n: common_vendor.f(product.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        })
      } : {}, {
        o: product.value.shop.avatar,
        p: common_vendor.t(product.value.shop.name),
        q: common_vendor.f(5, (i, k0, i0) => {
          return {
            a: "83d25058-5-" + i0 + "," + ("83d25058-4-" + i0),
            b: "83d25058-4-" + i0,
            c: i,
            d: i <= product.value.shop.rating ? 1 : ""
          };
        }),
        r: common_vendor.p({
          d: "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z",
          fill: "currentColor",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "12",
          height: "12"
        }),
        t: common_vendor.t(product.value.shop.rating),
        v: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        x: common_vendor.o(viewShopDetail),
        y: common_vendor.t(selectedSpecs.value || "请选择规格"),
        z: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        A: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        B: common_vendor.o(showSpecsSelector),
        C: common_vendor.t(product.value.delivery),
        D: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        E: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        F: common_vendor.o(showDeliveryInfo),
        G: common_vendor.f(product.value.services, (service, index, i0) => {
          return {
            a: "83d25058-13-" + i0 + "," + ("83d25058-12-" + i0),
            b: "83d25058-12-" + i0,
            c: common_vendor.t(service),
            d: index
          };
        }),
        H: common_vendor.p({
          d: "M20 6L9 17l-5-5",
          stroke: "#07C160",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        I: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "14",
          height: "14"
        }),
        J: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        K: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        L: common_vendor.o(showServiceInfo),
        M: product.value.description,
        N: common_vendor.f(product.value.detailImages, (img, index, i0) => {
          return {
            a: index,
            b: img
          };
        }),
        O: common_vendor.t(product.value.reviewCount),
        P: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Q: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        R: common_vendor.o(viewAllReviews),
        S: common_vendor.f(product.value.reviews, (review, index, i0) => {
          return common_vendor.e({
            a: review.avatar,
            b: common_vendor.t(review.name),
            c: common_vendor.f(5, (i, k1, i1) => {
              return {
                a: "83d25058-19-" + i0 + "-" + i1 + "," + ("83d25058-18-" + i0 + "-" + i1),
                b: "83d25058-18-" + i0 + "-" + i1,
                c: i,
                d: i <= review.rating ? 1 : ""
              };
            }),
            d: common_vendor.t(review.date),
            e: common_vendor.t(review.content),
            f: review.images && review.images.length
          }, review.images && review.images.length ? {
            g: common_vendor.f(review.images, (img, imgIndex, i1) => {
              return {
                a: imgIndex,
                b: img,
                c: common_vendor.o(($event) => previewImage(review.images, imgIndex), imgIndex)
              };
            })
          } : {}, {
            h: review.specs
          }, review.specs ? {
            i: common_vendor.t(review.specs)
          } : {}, {
            j: index
          });
        }),
        T: common_vendor.p({
          d: "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z",
          fill: "currentColor",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        U: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "12",
          height: "12"
        }),
        V: common_vendor.f(recommendProducts.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.price),
            d: common_vendor.t(item.sold),
            e: index,
            f: common_vendor.o(($event) => viewProductDetail(item.id), index)
          };
        }),
        W: common_vendor.p({
          d: "M20 7h-7.05a5 5 0 00-9.9 0H3a1 1 0 00-1 1v1a1 1 0 001 1v8a3 3 0 003 3h8a3 3 0 003-3v-8a1 1 0 001-1V8a1 1 0 00-1-1zM10 20H6a1 1 0 01-1-1v-8h5v9zm8-1a1 1 0 01-1 1h-4v-9h5v8z",
          stroke: "currentColor",
          fill: "none",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        X: common_vendor.p({
          cx: "10",
          cy: "5",
          r: "2",
          stroke: "currentColor",
          fill: "none",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Y: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        Z: common_vendor.o(navigateToShop),
        aa: common_vendor.p({
          d: "M20.84 4.61a5.5 5.5 0 00-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 00-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 000-7.78z",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        ab: isFavorite.value ? 1 : "",
        ac: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        ad: common_vendor.o(toggleFavorite),
        ae: common_vendor.p({
          d: "M21 11.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        af: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        ag: common_vendor.o(contactService),
        ah: common_vendor.o(addToCart),
        ai: common_vendor.o(buyNow)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-83d25058"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/shops/detail.js.map
