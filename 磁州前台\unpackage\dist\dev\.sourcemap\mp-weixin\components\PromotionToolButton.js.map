{"version": 3, "file": "PromotionToolButton.js", "sources": ["components/PromotionToolButton.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9Qcm9tb3Rpb25Ub29sQnV0dG9uLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"promotion-tool-wrapper\">\r\n    <ConfigurablePremiumActions\r\n      :pageType=\"pageType\"\r\n      :showMode=\"showMode\"\r\n      :itemData=\"itemData\"\r\n      @action-completed=\"handleActionCompleted\"\r\n      @action-cancelled=\"handleActionCancelled\"\r\n    />\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { computed } from 'vue';\r\nimport ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';\r\n\r\n// 定义组件属性\r\nconst props = defineProps({\r\n  // 页面类型\r\n  pageType: {\r\n    type: String,\r\n    default: 'publish',\r\n    validator: (value) => [\r\n      'publish', 'merchant_join', 'merchant_renew', 'merchant_top', 'merchant_refresh',\r\n      'carpool_top', 'carpool_refresh', 'product_top', 'product_refresh'\r\n    ].includes(value)\r\n  },\r\n  // 显示模式\r\n  showMode: {\r\n    type: String,\r\n    default: 'button',\r\n    validator: (value) => ['button', 'direct', 'selection'].includes(value)\r\n  },\r\n  // 项目数据\r\n  itemData: {\r\n    type: Object,\r\n    default: () => ({\r\n      id: '',\r\n      title: '推广',\r\n      description: '提升曝光度'\r\n    })\r\n  }\r\n});\r\n\r\n// 定义事件\r\nconst emit = defineEmits(['action-completed', 'action-cancelled']);\r\n\r\n// 处理操作完成\r\nconst handleActionCompleted = (result) => {\r\n  console.log('推广操作完成:', result);\r\n  emit('action-completed', result);\r\n};\r\n\r\n// 处理操作取消\r\nconst handleActionCancelled = (result) => {\r\n  console.log('推广操作取消:', result);\r\n  emit('action-cancelled', result);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.promotion-tool-wrapper {\r\n  width: 100%;\r\n}\r\n\r\n\r\n\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/PromotionToolButton.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;;;AAcA,MAAM,6BAA6B,MAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+B9C,UAAM,OAAO;AAGb,UAAM,wBAAwB,CAAC,WAAW;AACxCA,mFAAY,WAAW,MAAM;AAC7B,WAAK,oBAAoB,MAAM;AAAA,IACjC;AAGA,UAAM,wBAAwB,CAAC,WAAW;AACxCA,mFAAY,WAAW,MAAM;AAC7B,WAAK,oBAAoB,MAAM;AAAA,IACjC;;;;;;;;;;;;;;;ACxDA,GAAG,gBAAgB,SAAS;"}