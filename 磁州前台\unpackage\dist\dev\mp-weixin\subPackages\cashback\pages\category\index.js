"use strict";
const common_vendor = require("../../../../common/vendor.js");
const CustomNavbar = () => "../../components/CustomNavbar.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      activeIndex: 0,
      categories: [
        {
          name: "女装",
          subcategories: [
            {
              name: "当季热卖",
              items: [
                { id: 1, name: "连衣裙", icon: "/static/images/cashback/category-dress.png" },
                { id: 2, name: "T恤", icon: "/static/images/cashback/category-tshirt.png" },
                { id: 3, name: "衬衫", icon: "/static/images/cashback/category-shirt.png" },
                { id: 4, name: "裤子", icon: "/static/images/cashback/category-pants.png" },
                { id: 5, name: "外套", icon: "/static/images/cashback/category-coat.png" },
                { id: 6, name: "半身裙", icon: "/static/images/cashback/category-skirt.png" }
              ]
            },
            {
              name: "流行款式",
              items: [
                { id: 7, name: "牛仔裤", icon: "/static/images/cashback/category-jeans.png" },
                { id: 8, name: "休闲裤", icon: "/static/images/cashback/category-casual.png" },
                { id: 9, name: "卫衣", icon: "/static/images/cashback/category-hoodie.png" },
                { id: 10, name: "针织衫", icon: "/static/images/cashback/category-knitwear.png" }
              ]
            }
          ]
        },
        {
          name: "男装",
          subcategories: [
            {
              name: "当季热卖",
              items: [
                { id: 11, name: "T恤", icon: "/static/images/cashback/category-mtshirt.png" },
                { id: 12, name: "衬衫", icon: "/static/images/cashback/category-mshirt.png" },
                { id: 13, name: "裤子", icon: "/static/images/cashback/category-mpants.png" },
                { id: 14, name: "外套", icon: "/static/images/cashback/category-mcoat.png" }
              ]
            },
            {
              name: "流行款式",
              items: [
                { id: 15, name: "牛仔裤", icon: "/static/images/cashback/category-mjeans.png" },
                { id: 16, name: "休闲裤", icon: "/static/images/cashback/category-mcasual.png" },
                { id: 17, name: "卫衣", icon: "/static/images/cashback/category-mhoodie.png" },
                { id: 18, name: "西装", icon: "/static/images/cashback/category-suit.png" }
              ]
            }
          ]
        },
        {
          name: "美妆",
          subcategories: [
            {
              name: "热门品类",
              items: [
                { id: 19, name: "面膜", icon: "/static/images/cashback/category-mask.png" },
                { id: 20, name: "口红", icon: "/static/images/cashback/category-lipstick.png" },
                { id: 21, name: "精华", icon: "/static/images/cashback/category-essence.png" },
                { id: 22, name: "眼影", icon: "/static/images/cashback/category-eyeshadow.png" },
                { id: 23, name: "粉底", icon: "/static/images/cashback/category-foundation.png" },
                { id: 24, name: "洁面", icon: "/static/images/cashback/category-cleanser.png" }
              ]
            }
          ]
        },
        {
          name: "数码",
          subcategories: [
            {
              name: "热门设备",
              items: [
                { id: 25, name: "手机", icon: "/static/images/cashback/category-phone.png" },
                { id: 26, name: "笔记本", icon: "/static/images/cashback/category-laptop.png" },
                { id: 27, name: "平板", icon: "/static/images/cashback/category-tablet.png" },
                { id: 28, name: "耳机", icon: "/static/images/cashback/category-headphone.png" },
                { id: 29, name: "相机", icon: "/static/images/cashback/category-camera.png" },
                { id: 30, name: "手表", icon: "/static/images/cashback/category-watch.png" }
              ]
            }
          ]
        },
        {
          name: "家电",
          subcategories: [
            {
              name: "大家电",
              items: [
                { id: 31, name: "电视", icon: "/static/images/cashback/category-tv.png" },
                { id: 32, name: "冰箱", icon: "/static/images/cashback/category-fridge.png" },
                { id: 33, name: "洗衣机", icon: "/static/images/cashback/category-washer.png" },
                { id: 34, name: "空调", icon: "/static/images/cashback/category-ac.png" }
              ]
            },
            {
              name: "小家电",
              items: [
                { id: 35, name: "吸尘器", icon: "/static/images/cashback/category-vacuum.png" },
                { id: 36, name: "电饭煲", icon: "/static/images/cashback/category-cooker.png" },
                { id: 37, name: "加湿器", icon: "/static/images/cashback/category-humidifier.png" },
                { id: 38, name: "电热水壶", icon: "/static/images/cashback/category-kettle.png" }
              ]
            }
          ]
        },
        {
          name: "食品",
          subcategories: [
            {
              name: "休闲零食",
              items: [
                { id: 39, name: "坚果", icon: "/static/images/cashback/category-nuts.png" },
                { id: 40, name: "饼干", icon: "/static/images/cashback/category-biscuit.png" },
                { id: 41, name: "糖果", icon: "/static/images/cashback/category-candy.png" },
                { id: 42, name: "巧克力", icon: "/static/images/cashback/category-chocolate.png" }
              ]
            },
            {
              name: "粮油调味",
              items: [
                { id: 43, name: "大米", icon: "/static/images/cashback/category-rice.png" },
                { id: 44, name: "食用油", icon: "/static/images/cashback/category-oil.png" },
                { id: 45, name: "调味料", icon: "/static/images/cashback/category-spice.png" },
                { id: 46, name: "面条", icon: "/static/images/cashback/category-noodle.png" }
              ]
            }
          ]
        }
      ]
    };
  },
  computed: {
    currentSubcategories() {
      return this.categories[this.activeIndex].subcategories || [];
    }
  },
  onLoad() {
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#9C27B0"
    });
  },
  methods: {
    switchCategory(index) {
      this.activeIndex = index;
    },
    navigateToProductList(item) {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/product-list/index?id=${item.id}&name=${item.name}`
      });
    }
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  _component_custom_navbar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "商品分类",
      ["show-back"]: true
    }),
    b: common_vendor.f($data.categories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: index,
        c: $data.activeIndex === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchCategory(index), index)
      };
    }),
    c: common_vendor.f($options.currentSubcategories, (subcategory, subIndex, i0) => {
      return {
        a: common_vendor.t(subcategory.name),
        b: common_vendor.f(subcategory.items, (item, itemIndex, i1) => {
          return {
            a: item.icon,
            b: common_vendor.t(item.name),
            c: itemIndex,
            d: common_vendor.o(($event) => $options.navigateToProductList(item), itemIndex)
          };
        }),
        c: subIndex
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8d06c5ee"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/cashback/pages/category/index.js.map
