"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Math) {
  CarpoolNav();
}
const CarpoolNav = () => "../../../../components/carpool-nav.js";
const _sfc_main = {
  __name: "car-to-goods",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const formData = common_vendor.ref({
      carType: "",
      maxWeight: "",
      carSize: "",
      startPoint: "",
      endPoint: "",
      departureDate: "",
      departureTime: "",
      price: "",
      goodsRequirement: "",
      contactName: "",
      contactPhone: "",
      remark: "",
      agreement: false,
      viaPoints: []
    });
    const carTypes = common_vendor.ref(["小面包车", "中面包车", "小货车", "中型货车", "大型货车", "平板车", "厢式货车", "冷藏车", "其他"]);
    const carTypeIndex = common_vendor.ref(0);
    const publishMode = common_vendor.ref("ad");
    common_vendor.onMounted(() => {
      setStatusBarHeight();
    });
    const setStatusBarHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight;
    };
    const chooseLocation = (type, index) => {
      common_vendor.index.chooseLocation({
        success: (res) => {
          if (type === "start") {
            formData.value.startPoint = res.name;
          } else if (type === "end") {
            formData.value.endPoint = res.name;
          } else if (type === "via") {
            if (index !== void 0 && index !== null) {
              formData.value.viaPoints[index] = res.name;
            } else {
              formData.value.viaPoints.push(res.name);
            }
          }
        }
      });
    };
    const onDateChange = (e) => {
      formData.value.departureDate = e.detail.value;
    };
    const onTimeChange = (e) => {
      formData.value.departureTime = e.detail.value;
    };
    const onCarTypeChange = (e) => {
      carTypeIndex.value = e.detail.value;
      formData.value.carType = carTypes.value[carTypeIndex.value];
    };
    const onAgreementChange = (e) => {
      formData.value.agreement = e.detail.value.length > 0;
    };
    const viewAgreement = () => {
      common_vendor.index.navigateTo({
        url: "/pages/carpool/agreement"
      });
    };
    const selectPublishMode = (mode) => {
      publishMode.value = mode;
    };
    const submitForm = () => {
      if (!formData.value.carType) {
        showToast("请选择车辆类型");
        return;
      }
      if (!formData.value.maxWeight) {
        showToast("请输入最大载重");
        return;
      }
      if (!formData.value.carSize) {
        showToast("请输入车厢尺寸");
        return;
      }
      if (!formData.value.startPoint) {
        showToast("请输入出发地");
        return;
      }
      if (!formData.value.endPoint) {
        showToast("请输入目的地");
        return;
      }
      if (!formData.value.departureDate) {
        showToast("请选择出发日期");
        return;
      }
      if (!formData.value.departureTime) {
        showToast("请选择出发时间");
        return;
      }
      if (!formData.value.contactPhone) {
        showToast("请输入手机号码");
        return;
      }
      if (!/^1\d{10}$/.test(formData.value.contactPhone)) {
        showToast("手机号码格式不正确");
        return;
      }
      if (publishMode.value === "ad") {
        handleAdPublish();
      } else if (publishMode.value === "premium") {
        handlePremiumPublish();
      }
    };
    const handleAdPublish = () => {
      common_vendor.index.showLoading({
        title: "正在加载广告..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "广告播放完成",
          content: "感谢您观看广告，现在可以免费发布拼车信息",
          showCancel: false,
          success: () => {
            submitToServer();
          }
        });
      }, 1500);
    };
    const handlePremiumPublish = () => {
      common_vendor.index.showModal({
        title: "付费发布",
        content: "您将支付5元获得置顶发布特权，是否继续？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "正在支付..."
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "支付成功",
                icon: "success"
              });
              submitToServer();
            }, 1500);
          }
        }
      });
    };
    const submitToServer = () => {
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const formDataToSubmit = {
        ...formData.value,
        // 过滤掉空的途径地点
        viaPoints: formData.value.viaPoints.filter((point) => point.trim() !== "")
      };
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/publish/car-to-goods.vue:412", "提交的表单数据：", formDataToSubmit);
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const publishId = Date.now().toString();
        common_vendor.index.navigateTo({
          url: `/carpool-package/pages/carpool/publish/success?id=${publishId}&type=car-to-goods&mode=${publishMode.value}`
        });
      }, 1e3);
    };
    const showToast = (title) => {
      common_vendor.index.showToast({
        title,
        icon: "none"
      });
    };
    const removeViaPoint = (index) => {
      formData.value.viaPoints.splice(index, 1);
    };
    const addViaPoint = () => {
      formData.value.viaPoints.push("");
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "发布车找货信息"
        }),
        b: common_vendor.t(formData.value.carType || "请选择车辆类型"),
        c: common_assets._imports_0$27,
        d: carTypes.value,
        e: carTypeIndex.value,
        f: common_vendor.o(onCarTypeChange),
        g: formData.value.maxWeight,
        h: common_vendor.o(($event) => formData.value.maxWeight = $event.detail.value),
        i: formData.value.carSize,
        j: common_vendor.o(($event) => formData.value.carSize = $event.detail.value),
        k: formData.value.startPoint,
        l: common_vendor.o(($event) => formData.value.startPoint = $event.detail.value),
        m: common_assets._imports_2$36,
        n: common_vendor.o(($event) => chooseLocation("start")),
        o: formData.value.endPoint,
        p: common_vendor.o(($event) => formData.value.endPoint = $event.detail.value),
        q: common_assets._imports_2$36,
        r: common_vendor.o(($event) => chooseLocation("end")),
        s: common_vendor.f(formData.value.viaPoints, (point, index, i0) => {
          return {
            a: formData.value.viaPoints[index],
            b: common_vendor.o(($event) => formData.value.viaPoints[index] = $event.detail.value, index),
            c: common_vendor.o(($event) => chooseLocation("via", index), index),
            d: common_vendor.o(($event) => removeViaPoint(index), index),
            e: index
          };
        }),
        t: common_assets._imports_2$36,
        v: formData.value.viaPoints.length < 3
      }, formData.value.viaPoints.length < 3 ? {
        w: common_vendor.o(addViaPoint)
      } : {}, {
        x: common_vendor.t(formData.value.departureDate || "请选择出发日期"),
        y: common_assets._imports_0$27,
        z: formData.value.departureDate,
        A: common_vendor.o(onDateChange),
        B: common_vendor.t(formData.value.departureTime || "请选择出发时间"),
        C: common_assets._imports_0$27,
        D: formData.value.departureTime,
        E: common_vendor.o(onTimeChange),
        F: formData.value.price,
        G: common_vendor.o(($event) => formData.value.price = $event.detail.value),
        H: formData.value.goodsRequirement,
        I: common_vendor.o(($event) => formData.value.goodsRequirement = $event.detail.value),
        J: common_vendor.t(formData.value.goodsRequirement.length),
        K: formData.value.contactName,
        L: common_vendor.o(($event) => formData.value.contactName = $event.detail.value),
        M: formData.value.contactPhone,
        N: common_vendor.o(($event) => formData.value.contactPhone = $event.detail.value),
        O: formData.value.remark,
        P: common_vendor.o(($event) => formData.value.remark = $event.detail.value),
        Q: common_vendor.t(formData.value.remark.length),
        R: formData.value.agreement,
        S: common_vendor.o(viewAgreement),
        T: common_vendor.o(onAgreementChange),
        U: common_assets._imports_2$37,
        V: publishMode.value === "ad"
      }, publishMode.value === "ad" ? {} : {}, {
        W: publishMode.value === "ad" ? 1 : "",
        X: common_vendor.o(($event) => selectPublishMode("ad")),
        Y: common_assets._imports_8$6,
        Z: publishMode.value === "premium"
      }, publishMode.value === "premium" ? {} : {}, {
        aa: publishMode.value === "premium" ? 1 : "",
        ab: common_vendor.o(($event) => selectPublishMode("premium")),
        ac: !formData.value.agreement,
        ad: common_vendor.o(submitForm)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/publish/car-to-goods.js.map
