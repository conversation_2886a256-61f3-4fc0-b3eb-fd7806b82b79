"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_request = require("../../../utils/request.js");
const utils_uniapi = require("../../../utils/uniapi.js");
const { request } = utils_request.request;
class DistributionService {
  /**
   * 检查商家是否开通分销功能
   * @param {Object} merchantInfo 商家信息
   * @returns {Promise<boolean>} 是否开通分销功能
   */
  async checkMerchantDistribution(merchantInfo) {
    try {
      const { data } = await request({
        url: "/merchant/distribution/status",
        method: "GET",
        data: {
          merchantId: merchantInfo.id
        }
      });
      return data.enabled || false;
    } catch (error) {
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/services/distributionService.js:33", "检查分销状态失败", error);
      return false;
    }
  }
  /**
   * 获取商家分销默认设置
   * @param {Object} merchantInfo 商家信息
   * @returns {Promise<Object>} 分销默认设置
   */
  async getMerchantDistributionSettings(merchantInfo) {
    var _a, _b, _c;
    try {
      const { data } = await request({
        url: "/merchant/distribution/settings",
        method: "GET",
        data: {
          merchantId: merchantInfo.id
        }
      });
      return {
        enabled: false,
        // 默认不启用
        commissionMode: data.commissionMode || "percentage",
        commissions: {
          level1: ((_a = data.defaultCommission) == null ? void 0 : _a.level1) || "",
          level2: ((_b = data.defaultCommission) == null ? void 0 : _b.level2) || "",
          level3: ((_c = data.defaultCommission) == null ? void 0 : _c.level3) || ""
        },
        enableLevel3: data.enableLevel3 || false
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/services/distributionService.js:64", "获取分销设置失败", error);
      return {
        enabled: false,
        commissionMode: "percentage",
        commissions: {
          level1: "",
          level2: "",
          level3: ""
        },
        enableLevel3: false
      };
    }
  }
  /**
   * 保存活动分销设置
   * @param {string} activityType 活动类型：coupon-优惠券, discount-满减, flash-秒杀, group-拼团
   * @param {string} activityId 活动ID
   * @param {Object} distributionSettings 分销设置
   * @returns {Promise<boolean>} 是否保存成功
   */
  async saveActivityDistributionSettings(activityType, activityId, distributionSettings) {
    try {
      const { data } = await request({
        url: "/merchant/distribution/activity/settings",
        method: "POST",
        data: {
          activityType,
          activityId,
          settings: distributionSettings
        }
      });
      return data.success || false;
    } catch (error) {
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/services/distributionService.js:99", "保存活动分销设置失败", error);
      utils_uniapi.showToast("保存分销设置失败，请重试");
      return false;
    }
  }
  /**
   * 获取活动分销设置
   * @param {string} activityType 活动类型：coupon-优惠券, discount-满减, flash-秒杀, group-拼团
   * @param {string} activityId 活动ID
   * @returns {Promise<Object>} 分销设置
   */
  async getActivityDistributionSettings(activityType, activityId) {
    try {
      const { data } = await request({
        url: "/merchant/distribution/activity/settings",
        method: "GET",
        data: {
          activityType,
          activityId
        }
      });
      return data.settings || {
        enabled: false,
        commissionMode: "percentage",
        commissions: {
          level1: "",
          level2: "",
          level3: ""
        },
        enableLevel3: false
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/services/distributionService.js:133", "获取活动分销设置失败", error);
      return {
        enabled: false,
        commissionMode: "percentage",
        commissions: {
          level1: "",
          level2: "",
          level3: ""
        },
        enableLevel3: false
      };
    }
  }
  /**
   * 计算预估佣金
   * @param {Object} settings 分销设置
   * @param {number} price 商品价格
   * @returns {Object} 预估佣金
   */
  calculateEstimatedCommission(settings, price) {
    if (!settings || !settings.enabled || !price) {
      return {
        level1: 0,
        level2: 0,
        level3: 0,
        total: 0
      };
    }
    const { commissionMode, commissions, enableLevel3 } = settings;
    let level1 = 0, level2 = 0, level3 = 0;
    if (commissionMode === "percentage") {
      level1 = price * (parseFloat(commissions.level1 || 0) / 100);
      level2 = price * (parseFloat(commissions.level2 || 0) / 100);
      if (enableLevel3) {
        level3 = price * (parseFloat(commissions.level3 || 0) / 100);
      }
    } else {
      level1 = parseFloat(commissions.level1 || 0);
      level2 = parseFloat(commissions.level2 || 0);
      if (enableLevel3) {
        level3 = parseFloat(commissions.level3 || 0);
      }
    }
    const total = level1 + level2 + level3;
    return {
      level1: parseFloat(level1.toFixed(2)),
      level2: parseFloat(level2.toFixed(2)),
      level3: parseFloat(level3.toFixed(2)),
      total: parseFloat(total.toFixed(2))
    };
  }
  /**
   * 验证分销设置是否有效
   * @param {Object} settings 分销设置
   * @returns {Object} 验证结果
   */
  validateDistributionSettings(settings) {
    if (!settings.enabled) {
      return { valid: true };
    }
    const { commissionMode, commissions, enableLevel3 } = settings;
    const errors = [];
    if (!commissions.level1) {
      errors.push("请设置一级分销佣金");
    } else if (commissionMode === "percentage" && (parseFloat(commissions.level1) <= 0 || parseFloat(commissions.level1) > 100)) {
      errors.push("一级分销比例应在0-100%之间");
    } else if (commissionMode === "fixed" && parseFloat(commissions.level1) < 0) {
      errors.push("一级分销金额不能为负数");
    }
    if (!commissions.level2) {
      errors.push("请设置二级分销佣金");
    } else if (commissionMode === "percentage" && (parseFloat(commissions.level2) < 0 || parseFloat(commissions.level2) > 100)) {
      errors.push("二级分销比例应在0-100%之间");
    } else if (commissionMode === "fixed" && parseFloat(commissions.level2) < 0) {
      errors.push("二级分销金额不能为负数");
    }
    if (enableLevel3) {
      if (!commissions.level3) {
        errors.push("请设置三级分销佣金");
      } else if (commissionMode === "percentage" && (parseFloat(commissions.level3) < 0 || parseFloat(commissions.level3) > 100)) {
        errors.push("三级分销比例应在0-100%之间");
      } else if (commissionMode === "fixed" && parseFloat(commissions.level3) < 0) {
        errors.push("三级分销金额不能为负数");
      }
    }
    if (commissionMode === "percentage") {
      const totalPercentage = parseFloat(commissions.level1 || 0) + parseFloat(commissions.level2 || 0) + (enableLevel3 ? parseFloat(commissions.level3 || 0) : 0);
      if (totalPercentage > 100) {
        errors.push("分销佣金总比例不能超过100%");
      }
    }
    return {
      valid: errors.length === 0,
      errors
    };
  }
}
const distributionService = new DistributionService();
exports.distributionService = distributionService;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/services/distributionService.js.map
