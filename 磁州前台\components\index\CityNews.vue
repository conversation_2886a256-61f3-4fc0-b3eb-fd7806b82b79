<template>
  <!-- 同城资讯 -->
  <view class="city-news card-section fade-in">
    <view class="section-header">
      <view class="section-title-wrap">
        <!-- 删除蓝色竖道 -->
        <text class="section-title blue-title">同城资讯</text>
      </view>
      <text class="more-link blue-link" @click="navigateToMore">查看更多</text>
    </view>
    <view class="notice-bar">
      <image src="/static/images/tabbar/喇叭.png" class="notice-icon"></image>
      <swiper class="notice-swiper" vertical autoplay circular interval="3000" duration="500">
        <swiper-item v-for="(item, index) in noticeList" :key="index" @click="navigateToDetail(index)">
          <text class="notice-text">{{item}}</text>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

const noticeList = ref([
  '磁县人民政府关于加强城市管理的通知',
  '2023年磁县春节期间活动安排',
  '磁县城区道路施工公告',
  '关于开展全民健康体检的通知',
  '磁县文化中心活动预告'
]);

function navigateToMore() {
  uni.navigateTo({
    url: '/subPackages/news/pages/list',
    fail: () => {
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
}

function navigateToDetail(index) {
  uni.navigateTo({
    url: `/subPackages/news/pages/detail?id=${index + 1}`,
    fail: () => {
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
}
</script>

<style lang="scss" scoped>
.city-news {
  margin: 24rpx 30rpx 30rpx;
  position: relative;
  z-index: 2;
  background: #ffffff;
  border-radius: 35rpx;
  padding: 24rpx 20rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title-wrap {
  display: flex;
  align-items: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.blue-title {
  color: #007AFF;
}

.more-link {
  font-size: 26rpx;
  color: #007AFF;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.blue-link {
  color: #007AFF;
}

.more-link:active {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(0.96);
}

.notice-bar {
  display: flex;
  align-items: center;
  background-color: #F2F2F7;
  border-radius: 35rpx;
  padding: 16rpx 20rpx;
}

.notice-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.notice-swiper {
  flex: 1;
  height: 66rpx;
}

.notice-text {
  font-size: 28rpx;
  color: #333;
  line-height: 66rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-section {
  margin-bottom: 20rpx;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}
</style> 