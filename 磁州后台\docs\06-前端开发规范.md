# 磁州生活网后台管理系统 - 前端开发规范

## 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025-01-04
- **文档类型**: 前端开发规范
- **目标读者**: 前端开发工程师、UI设计师

## 1. 项目结构规范

### 1.1 目录结构
```
src/
├── api/                    # API接口
│   ├── modules/           # 按模块分组的API
│   │   ├── user.ts       # 用户相关API
│   │   ├── content.ts    # 内容相关API
│   │   └── merchant.ts   # 商家相关API
│   ├── types/            # API类型定义
│   └── request.ts        # 请求封装
├── assets/               # 静态资源
│   ├── images/          # 图片资源
│   ├── icons/           # 图标资源
│   └── styles/          # 全局样式
├── components/          # 公共组件
│   ├── common/         # 通用组件
│   ├── business/       # 业务组件
│   └── layout/         # 布局组件
├── composables/        # 组合式函数
│   ├── useAuth.ts     # 认证相关
│   ├── useTable.ts    # 表格相关
│   └── useForm.ts     # 表单相关
├── layouts/            # 布局组件
│   ├── default.vue    # 默认布局
│   └── blank.vue      # 空白布局
├── router/             # 路由配置
│   ├── modules/       # 路由模块
│   └── index.ts       # 路由入口
├── stores/             # 状态管理
│   ├── modules/       # 状态模块
│   └── index.ts       # 状态入口
├── types/              # TypeScript类型
│   ├── api.ts         # API类型
│   ├── common.ts      # 通用类型
│   └── business.ts    # 业务类型
├── utils/              # 工具函数
│   ├── auth.ts        # 认证工具
│   ├── format.ts      # 格式化工具
│   └── validate.ts    # 验证工具
├── views/              # 页面组件
│   ├── dashboard/     # 仪表盘
│   ├── user/          # 用户管理
│   ├── content/       # 内容管理
│   └── system/        # 系统管理
├── App.vue             # 根组件
└── main.ts             # 入口文件
```

### 1.2 文件命名规范
- **组件文件**: PascalCase (UserList.vue)
- **页面文件**: kebab-case (user-list.vue)
- **工具文件**: camelCase (formatDate.ts)
- **常量文件**: UPPER_SNAKE_CASE (API_ENDPOINTS.ts)

## 2. 编码规范

### 2.1 Vue 3 组合式API规范

#### 2.1.1 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 2. 类型定义
interface UserInfo {
  id: number
  name: string
  email: string
}

// 3. Props定义
interface Props {
  userId?: number
  readonly?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// 4. Emits定义
interface Emits {
  (e: 'update', user: UserInfo): void
  (e: 'delete', id: number): void
}
const emit = defineEmits<Emits>()

// 5. 响应式数据
const loading = ref(false)
const userInfo = reactive<UserInfo>({
  id: 0,
  name: '',
  email: ''
})

// 6. 计算属性
const displayName = computed(() => {
  return userInfo.name || '未命名用户'
})

// 7. 方法定义
const handleSubmit = async () => {
  loading.value = true
  try {
    // 处理逻辑
    emit('update', userInfo)
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 8. 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 9. 暴露给模板的内容
defineExpose({
  handleSubmit
})
</script>

<style scoped>
/* 组件样式 */
</style>
```

#### 2.1.2 组合式函数规范
```typescript
// composables/useTable.ts
import { ref, reactive } from 'vue'

export interface TableOptions {
  page: number
  size: number
  total: number
}

export interface UseTableReturn<T> {
  loading: Ref<boolean>
  data: Ref<T[]>
  pagination: Reactive<TableOptions>
  refresh: () => Promise<void>
  handlePageChange: (page: number) => void
  handleSizeChange: (size: number) => void
}

export function useTable<T>(
  fetchFn: (params: any) => Promise<{ data: T[]; total: number }>
): UseTableReturn<T> {
  const loading = ref(false)
  const data = ref<T[]>([])
  const pagination = reactive<TableOptions>({
    page: 1,
    size: 20,
    total: 0
  })

  const refresh = async () => {
    loading.value = true
    try {
      const result = await fetchFn({
        page: pagination.page,
        size: pagination.size
      })
      data.value = result.data
      pagination.total = result.total
    } catch (error) {
      console.error('获取数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  const handlePageChange = (page: number) => {
    pagination.page = page
    refresh()
  }

  const handleSizeChange = (size: number) => {
    pagination.size = size
    pagination.page = 1
    refresh()
  }

  return {
    loading,
    data,
    pagination,
    refresh,
    handlePageChange,
    handleSizeChange
  }
}
```

### 2.2 TypeScript规范

#### 2.2.1 类型定义
```typescript
// types/api.ts

// 基础响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
  traceId: string
}

// 分页响应类型
export interface PageResponse<T> {
  total: number
  page: number
  size: number
  pages: number
  records: T[]
}

// 分页请求类型
export interface PageRequest {
  page: number
  size: number
  keyword?: string
  startDate?: string
  endDate?: string
}

// 用户类型
export interface User {
  id: number
  username: string
  nickname: string
  phone: string
  email: string
  avatar: string
  status: UserStatus
  userType: UserType
  createdAt: string
  updatedAt: string
}

// 枚举类型
export enum UserStatus {
  DISABLED = 0,
  ENABLED = 1,
  PENDING = 2
}

export enum UserType {
  NORMAL = 1,
  MERCHANT = 2,
  DISTRIBUTOR = 3,
  PARTNER = 4
}
```

#### 2.2.2 API接口类型
```typescript
// api/types/user.ts

export interface UserListRequest extends PageRequest {
  status?: UserStatus
  userType?: UserType
}

export interface UserCreateRequest {
  username: string
  password: string
  nickname: string
  phone: string
  email: string
  userType: UserType
}

export interface UserUpdateRequest {
  nickname?: string
  phone?: string
  email?: string
  status?: UserStatus
}

export interface UserBatchRequest {
  action: 'enable' | 'disable' | 'delete'
  userIds: number[]
}
```

### 2.3 组件开发规范

#### 2.3.1 通用组件规范
```vue
<!-- components/common/SearchForm.vue -->
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :inline="inline"
    class="search-form"
    @submit.prevent="handleSearch"
  >
    <slot :form-data="formData" :loading="loading" />
    
    <el-form-item>
      <el-button
        type="primary"
        :loading="loading"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button @click="handleReset">
        重置
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
interface Props {
  modelValue: Record<string, any>
  inline?: boolean
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'search', value: Record<string, any>): void
  (e: 'reset'): void
}

const props = withDefaults(defineProps<Props>(), {
  inline: true,
  loading: false
})

const emit = defineEmits<Emits>()

const formRef = ref()
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleSearch = () => {
  emit('search', formData.value)
}

const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}
</script>
```

#### 2.3.2 业务组件规范
```vue
<!-- components/business/UserTable.vue -->
<template>
  <div class="user-table">
    <el-table
      v-loading="loading"
      :data="data"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="nickname" label="昵称" />
      <el-table-column prop="phone" label="手机号" />
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup lang="ts">
import type { User, UserStatus } from '@/types/api'

interface Props {
  data: User[]
  loading?: boolean
  pagination: {
    page: number
    size: number
    total: number
  }
}

interface Emits {
  (e: 'edit', user: User): void
  (e: 'delete', user: User): void
  (e: 'selection-change', users: User[]): void
  (e: 'page-change', page: number): void
  (e: 'size-change', size: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const getStatusType = (status: UserStatus) => {
  const typeMap = {
    [UserStatus.ENABLED]: 'success',
    [UserStatus.DISABLED]: 'danger',
    [UserStatus.PENDING]: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: UserStatus) => {
  const textMap = {
    [UserStatus.ENABLED]: '正常',
    [UserStatus.DISABLED]: '禁用',
    [UserStatus.PENDING]: '待审核'
  }
  return textMap[status] || '未知'
}

const handleEdit = (user: User) => {
  emit('edit', user)
}

const handleDelete = (user: User) => {
  emit('delete', user)
}

const handleSelectionChange = (users: User[]) => {
  emit('selection-change', users)
}

const handlePageChange = (page: number) => {
  emit('page-change', page)
}

const handleSizeChange = (size: number) => {
  emit('size-change', size)
}
</script>
```

## 3. 样式规范

### 3.1 CSS命名规范
采用BEM命名规范：
```scss
// 块(Block)
.user-list {
  // 元素(Element)
  &__header {
    display: flex;
    justify-content: space-between;
    
    // 修饰符(Modifier)
    &--sticky {
      position: sticky;
      top: 0;
    }
  }
  
  &__content {
    padding: 20px;
  }
  
  &__footer {
    text-align: center;
  }
}
```

### 3.2 SCSS变量规范
```scss
// styles/variables.scss

// 颜色变量
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 尺寸变量
$header-height: 60px;
$sidebar-width: 200px;
$border-radius: 4px;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体变量
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
```

### 3.3 响应式设计规范
```scss
// styles/mixins.scss

// 断点变量
$breakpoints: (
  xs: 480px,
  sm: 768px,
  md: 992px,
  lg: 1200px,
  xl: 1920px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 使用示例
.user-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  
  @include respond-to(md) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include respond-to(lg) {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 4. 状态管理规范

### 4.1 Pinia Store规范
```typescript
// stores/modules/user.ts
import { defineStore } from 'pinia'
import type { User } from '@/types/api'
import { userApi } from '@/api/modules/user'

interface UserState {
  currentUser: User | null
  token: string | null
  permissions: string[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    currentUser: null,
    token: localStorage.getItem('token'),
    permissions: []
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    hasPermission: (state) => (permission: string) => {
      return state.permissions.includes(permission)
    }
  },

  actions: {
    async login(credentials: LoginRequest) {
      try {
        const response = await userApi.login(credentials)
        this.token = response.data.token
        this.currentUser = response.data.userInfo
        this.permissions = response.data.permissions
        
        localStorage.setItem('token', this.token)
        return response
      } catch (error) {
        throw error
      }
    },

    async logout() {
      try {
        await userApi.logout()
      } finally {
        this.token = null
        this.currentUser = null
        this.permissions = []
        localStorage.removeItem('token')
      }
    },

    async getCurrentUser() {
      if (!this.token) return null
      
      try {
        const response = await userApi.getCurrentUser()
        this.currentUser = response.data
        return response.data
      } catch (error) {
        this.logout()
        throw error
      }
    }
  }
})
```

## 5. 路由规范

### 5.1 路由配置规范
```typescript
// router/modules/user.ts
import type { RouteRecordRaw } from 'vue-router'

const userRoutes: RouteRecordRaw[] = [
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/layouts/default.vue'),
    meta: {
      title: '用户管理',
      icon: 'user',
      requiresAuth: true,
      permissions: ['user:read']
    },
    children: [
      {
        path: '',
        name: 'UserList',
        component: () => import('@/views/user/user-list.vue'),
        meta: {
          title: '用户列表',
          keepAlive: true
        }
      },
      {
        path: 'create',
        name: 'UserCreate',
        component: () => import('@/views/user/user-form.vue'),
        meta: {
          title: '创建用户',
          permissions: ['user:create']
        }
      },
      {
        path: ':id/edit',
        name: 'UserEdit',
        component: () => import('@/views/user/user-form.vue'),
        meta: {
          title: '编辑用户',
          permissions: ['user:update']
        }
      }
    ]
  }
]

export default userRoutes
```

### 5.2 路由守卫规范
```typescript
// router/guards.ts
import type { Router } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'

export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore()
    
    // 检查登录状态
    if (to.meta.requiresAuth && !userStore.isLoggedIn) {
      next('/login')
      return
    }
    
    // 检查权限
    if (to.meta.permissions) {
      const hasPermission = to.meta.permissions.some(permission =>
        userStore.hasPermission(permission)
      )
      
      if (!hasPermission) {
        next('/403')
        return
      }
    }
    
    next()
  })
  
  // 全局后置守卫
  router.afterEach((to) => {
    // 设置页面标题
    document.title = to.meta.title 
      ? `${to.meta.title} - 磁州生活网后台管理系统`
      : '磁州生活网后台管理系统'
  })
}
```

## 6. 工具函数规范

### 6.1 格式化工具
```typescript
// utils/format.ts

/**
 * 格式化日期
 */
export function formatDate(
  date: string | number | Date,
  format = 'YYYY-MM-DD HH:mm:ss'
): string {
  // 实现日期格式化逻辑
  return dayjs(date).format(format)
}

/**
 * 格式化金额
 */
export function formatMoney(
  amount: number,
  currency = '¥',
  decimals = 2
): string {
  return `${currency}${amount.toFixed(decimals)}`
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 B'
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
}
```

### 6.2 验证工具
```typescript
// utils/validate.ts

/**
 * 验证手机号
 */
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证密码强度
 */
export function validatePassword(password: string): {
  valid: boolean
  strength: 'weak' | 'medium' | 'strong'
  message: string
} {
  if (password.length < 8) {
    return {
      valid: false,
      strength: 'weak',
      message: '密码长度至少8位'
    }
  }
  
  const hasLower = /[a-z]/.test(password)
  const hasUpper = /[A-Z]/.test(password)
  const hasNumber = /\d/.test(password)
  const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password)
  
  const score = [hasLower, hasUpper, hasNumber, hasSpecial].filter(Boolean).length
  
  if (score < 3) {
    return {
      valid: false,
      strength: 'weak',
      message: '密码强度太弱，请包含大小写字母、数字和特殊字符'
    }
  }
  
  return {
    valid: true,
    strength: score === 3 ? 'medium' : 'strong',
    message: '密码强度符合要求'
  }
}
```

---

**文档状态**: 前端开发规范完成，待团队评审
**下一步**: 后端开发规范文档
