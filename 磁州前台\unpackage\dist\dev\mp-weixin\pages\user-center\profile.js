"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_navigation = require("../../utils/navigation.js");
const common_assets = require("../../common/assets.js");
const UserProfileCard = () => "../../components/UserProfileCard.js";
const _sfc_main = {
  enableShareAppMessage: true,
  enableShareTimeline: true,
  components: {
    UserProfileCard
  },
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      contentHeight: 500,
      // 默认高度，将在onReady中计算
      isCurrentUser: true,
      // 是否是当前用户
      isFollowed: false,
      // 是否已关注该用户
      userId: "",
      // 查看的用户ID
      tabs: [
        { name: "店铺" },
        { name: "发布" }
      ],
      currentTab: 0,
      userInfo: {
        avatar: "",
        nickname: "",
        signature: "",
        phone: "",
        gender: "",
        birthday: "",
        location: ""
      },
      userStats: {
        follows: 25,
        fans: 108,
        likes: 356,
        shops: 2,
        visitors: 189,
        posts: 42
      },
      publishList: [],
      likesList: [],
      commentsList: [],
      worksList: [],
      // 他人的作品集
      page: [1, 1, 1],
      // 三个列表的当前页码
      hasMore: [true, true, true],
      // 是否有更多数据
      refreshing: [false, false, false],
      // 是否在刷新中
      shopList: [
        { id: 1, name: "磁州生活便利店", desc: "24小时营业，便利到家", logo: "/static/images/shop1.png" },
        { id: 2, name: "磁州美食餐厅", desc: "本地特色美食", logo: "/static/images/shop2.png" }
      ],
      shopDetail: {
        logo: "/static/images/shop1.png",
        name: "磁州生活便利店",
        desc: "24小时营业，便利到家",
        phone: "138****5678",
        address: "磁县幸福路88号"
      },
      shopActivities: [],
      publishedItems: [
        {
          id: 1,
          type: "招聘信息",
          title: "寻找初中数学家教",
          desc: "每周两次，新资面议。",
          date: "2023-10-15",
          status: "online",
          images: []
        },
        {
          id: 2,
          type: "二手转让",
          title: "9成新iPhone 13出售",
          desc: "去年购买，配件齐全，无划痕，价格可议。",
          date: "2023-11-02",
          status: "online",
          images: ["/static/images/default-activity.png"]
        },
        {
          id: 3,
          type: "房屋出租",
          title: "市中心两室一厅出租",
          desc: "位置便利，交通方便，拎包入住，月租2000元。",
          date: "2023-12-05",
          status: "online",
          images: ["/static/images/default-activity.png", "/static/images/default-activity.png"]
        }
      ],
      refreshPackages: [
        { id: 1, name: "单次刷新", price: 2, count: 1 },
        { id: 2, name: "3次刷新", price: 5, count: 3, discount: "省1元" },
        { id: 3, name: "10次刷新", price: 15, count: 10, discount: "省5元" }
      ],
      userRefreshCount: 2,
      // 用户剩余刷新次数
      customModal: null
      // 自定义弹窗
    };
  },
  computed: {
    tabLineStyle() {
      const tabWidth = 100 / this.tabs.length;
      return {
        transform: `translateX(${this.currentTab * tabWidth}%)`,
        width: `${tabWidth}%`,
        left: "0"
      };
    }
  },
  onLoad(options) {
    const sysInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#007AFF"
    });
    if (options && options.userId) {
      this.isCurrentUser = false;
      this.userId = options.userId;
      this.tabs = [
        { name: "店铺" },
        { name: "发布" }
      ];
      this.checkIsFollowed();
      this.recordVisit();
    } else {
      this.tabs = [
        { name: "店铺" },
        { name: "发布" }
      ];
    }
    this.getUserInfo();
    this.loadPublishList();
    this.loadShopActivities();
  },
  onReady() {
    this.calcContentHeight();
  },
  methods: {
    // 检查是否已关注
    checkIsFollowed() {
      setTimeout(() => {
        this.isFollowed = Math.random() > 0.5;
      }, 300);
    },
    // 记录访问
    recordVisit() {
      common_vendor.index.__f__("log", "at pages/user-center/profile.vue:421", "记录访问用户:", this.userId);
    },
    // 关注/取消关注
    toggleFollow() {
      if (this.isFollowed) {
        common_vendor.index.showModal({
          title: "提示",
          content: "确定取消关注该用户吗？",
          success: (res) => {
            if (res.confirm) {
              setTimeout(() => {
                this.isFollowed = false;
                this.userStats.fans--;
                common_vendor.index.showToast({
                  title: "已取消关注",
                  icon: "success"
                });
              }, 300);
            }
          }
        });
      } else {
        setTimeout(() => {
          this.isFollowed = true;
          this.userStats.fans++;
          common_vendor.index.showToast({
            title: "已关注",
            icon: "success"
          });
        }, 300);
      }
    },
    // 发送消息
    sendMessage() {
      common_vendor.index.navigateTo({
        url: `/pages/chat/chat?userId=${this.userId}&nickname=${this.userInfo.nickname}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/user-center/profile.vue:464", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "发送消息功能已实现",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    // 加载作品集
    loadWorksList() {
      setTimeout(() => {
        const categories = ["摄影作品", "绘画作品", "手工艺品", "文学作品", "音乐作品"];
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `works_${this.page[1]}_${i}`,
          title: `${categories[Math.floor(Math.random() * categories.length)]}：${["风景摄影", "人物素描", "手工皮具", "散文集", "原创音乐"][Math.floor(Math.random() * 5)]}`,
          desc: ["记录城市的美丽瞬间", "用铅笔描绘人物的神态", "纯手工制作的皮具作品", "关于生活的随笔集合", "原创音乐作品分享"][Math.floor(Math.random() * 5)],
          time: ["2023-10-15", "2023-10-16", "2023-10-17", "2023-10-18", "2023-10-19"][Math.floor(Math.random() * 5)],
          images: [
            "/static/images/service1.jpg",
            "/static/images/service2.jpg",
            "/static/images/service3.jpg"
          ].slice(0, Math.floor(Math.random() * 4) + 1),
          views: Math.floor(Math.random() * 1e3) + 100,
          comments: Math.floor(Math.random() * 30) + 1,
          likes: Math.floor(Math.random() * 50) + 5
        }));
        if (this.page[1] === 1) {
          this.worksList = mockData;
        } else {
          this.worksList = [...this.worksList, ...mockData];
        }
        this.hasMore[1] = this.page[1] < 3;
        this.refreshing[1] = false;
      }, 600);
    },
    // 切换选项卡
    switchTab(index) {
      if (this.currentTab === index)
        return;
      this.currentTab = index;
      if (this.isCurrentUser) {
        switch (index) {
          case 0:
            if (this.publishList.length === 0)
              this.loadPublishList();
            break;
          case 1:
            if (this.likesList.length === 0)
              this.loadLikesList();
            break;
          case 2:
            if (this.commentsList.length === 0)
              this.loadCommentsList();
            break;
        }
      } else {
        switch (index) {
          case 0:
            if (this.publishList.length === 0)
              this.loadPublishList();
            break;
          case 1:
            if (this.worksList.length === 0)
              this.loadWorksList();
            break;
        }
      }
    },
    // 轮播图切换事件
    onSwiperChange(e) {
      this.switchTab(e.detail.current);
    },
    // 加载更多数据
    loadMore(tabIndex) {
      if (!this.hasMore[tabIndex] || this.refreshing[tabIndex])
        return;
      this.page[tabIndex]++;
      if (this.isCurrentUser) {
        switch (tabIndex) {
          case 0:
            this.loadPublishList();
            break;
          case 1:
            this.loadLikesList();
            break;
          case 2:
            this.loadCommentsList();
            break;
        }
      } else {
        switch (tabIndex) {
          case 0:
            this.loadPublishList();
            break;
          case 1:
            if (this.worksList.length === 0)
              this.loadWorksList();
            break;
        }
      }
    },
    // 下拉刷新
    onRefresh(tabIndex) {
      this.refreshing[tabIndex] = true;
      this.page[tabIndex] = 1;
      if (this.isCurrentUser) {
        switch (tabIndex) {
          case 0:
            this.loadPublishList();
            break;
          case 1:
            this.loadLikesList();
            break;
          case 2:
            this.loadCommentsList();
            break;
        }
      } else {
        switch (tabIndex) {
          case 0:
            this.loadPublishList();
            break;
          case 1:
            if (this.worksList.length === 0)
              this.loadWorksList();
            break;
        }
      }
    },
    // 获取用户信息
    getUserInfo() {
      setTimeout(() => {
        this.userInfo = {
          avatar: "/static/images/default-avatar.png",
          nickname: "用户_88965",
          signature: "每一天都是新的开始",
          phone: "138****5678",
          gender: "男",
          birthday: "1990-01-01",
          location: "河北省 邯郸市 磁县"
        };
      }, 500);
    },
    // 加载发布列表
    loadPublishList() {
      setTimeout(() => {
        const categories = ["房产信息", "求职招聘", "二手交易", "本地服务", "同城活动"];
        const locations = ["磁县城区", "磁县北部", "磁县南部", "磁县西部", "磁县东部"];
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `publish_${this.page[0]}_${i}`,
          title: `${categories[Math.floor(Math.random() * categories.length)]}：${["急售二手家电", "招聘前台文员", "转让餐饮店铺", "寻找家教老师", "同城交友活动"][Math.floor(Math.random() * 5)]}`,
          desc: ["出售9成新冰箱洗衣机，价格便宜，有意者联系。", "招聘前台文员，要求形象气质佳，有工作经验优先。", "因个人原因转让位于市中心的餐饮店铺，接手即可营业。", "寻找初中数学家教，每周两次，薪资面议。", "组织同城交友活动，欢迎单身青年参加，地点在市中心广场。"][Math.floor(Math.random() * 5)],
          time: ["2023-10-15", "2023-10-16", "2023-10-17", "2023-10-18", "2023-10-19"][Math.floor(Math.random() * 5)],
          images: [
            "/static/images/service1.jpg",
            "/static/images/service2.jpg",
            "/static/images/service3.jpg"
          ].slice(0, Math.floor(Math.random() * 4) + 1),
          location: locations[Math.floor(Math.random() * locations.length)],
          views: Math.floor(Math.random() * 1e3) + 100,
          comments: Math.floor(Math.random() * 30) + 1,
          likes: Math.floor(Math.random() * 50) + 5,
          category: categories[Math.floor(Math.random() * categories.length)]
        }));
        if (this.page[0] === 1) {
          this.publishList = mockData;
        } else {
          this.publishList = [...this.publishList, ...mockData];
        }
        this.hasMore[0] = this.page[0] < 3;
        this.refreshing[0] = false;
      }, 600);
    },
    // 加载点赞列表
    loadLikesList() {
      setTimeout(() => {
        const categories = ["房产信息", "求职招聘", "二手交易", "本地服务", "同城活动"];
        const authorNames = ["张小姐", "李先生", "王师傅", "赵老师", "刘经理"];
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `likes_${this.page[1]}_${i}`,
          title: `${categories[Math.floor(Math.random() * categories.length)]}：${["急售二手家电", "招聘前台文员", "转让餐饮店铺", "寻找家教老师", "同城交友活动"][Math.floor(Math.random() * 5)]}`,
          desc: ["出售9成新冰箱洗衣机，价格便宜，有意者联系。", "招聘前台文员，要求形象气质佳，有工作经验优先。", "因个人原因转让位于市中心的餐饮店铺，接手即可营业。", "寻找初中数学家教，每周两次，薪资面议。", "组织同城交友活动，欢迎单身青年参加，地点在市中心广场。"][Math.floor(Math.random() * 5)],
          time: ["2023-10-15", "2023-10-16", "2023-10-17", "2023-10-18", "2023-10-19"][Math.floor(Math.random() * 5)],
          images: [
            "/static/images/service1.jpg",
            "/static/images/service2.jpg",
            "/static/images/service3.jpg"
          ].slice(0, Math.floor(Math.random() * 4)),
          authorName: authorNames[Math.floor(Math.random() * authorNames.length)],
          authorAvatar: "/static/images/default-avatar.png",
          views: Math.floor(Math.random() * 1e3) + 100,
          comments: Math.floor(Math.random() * 30) + 1,
          likes: Math.floor(Math.random() * 50) + 5,
          category: categories[Math.floor(Math.random() * categories.length)]
        }));
        if (this.page[1] === 1) {
          this.likesList = mockData;
        } else {
          this.likesList = [...this.likesList, ...mockData];
        }
        this.hasMore[1] = this.page[1] < 3;
        this.refreshing[1] = false;
      }, 600);
    },
    // 加载评论列表
    loadCommentsList() {
      setTimeout(() => {
        const contentTemplates = [
          "这个信息很有用，感谢分享！",
          "请问还可以再便宜一点吗？",
          "我很感兴趣，已经私信你了",
          "地址在哪里？方便告诉一下吗？",
          "我有同款在出售，价格更便宜"
        ];
        const targetTitles = [
          "急售二手家电：9成新冰箱",
          "招聘前台文员：要求形象好",
          "转让餐饮店铺：黄金位置",
          "寻找家教老师：初中数学",
          "同城交友活动：周末聚会"
        ];
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `comment_${this.page[2]}_${i}`,
          content: contentTemplates[Math.floor(Math.random() * contentTemplates.length)],
          targetTitle: targetTitles[Math.floor(Math.random() * targetTitles.length)],
          targetId: `target_${Math.floor(Math.random() * 100)}`,
          time: ["2023-10-15", "2023-10-16", "2023-10-17", "2023-10-18", "2023-10-19"][Math.floor(Math.random() * 5)],
          likes: Math.floor(Math.random() * 20) + 1,
          replies: Math.floor(Math.random() * 5)
        }));
        if (this.page[2] === 1) {
          this.commentsList = mockData;
        } else {
          this.commentsList = [...this.commentsList, ...mockData];
        }
        this.hasMore[2] = this.page[2] < 3;
        this.refreshing[2] = false;
      }, 600);
    },
    // 查看详情
    viewDetail(item) {
      let url = `/pages/publish/info-detail?id=${item.id}`;
      if (this.isCurrentUser) {
        url += "&owner=self";
      }
      common_vendor.index.navigateTo({
        url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/user-center/profile.vue:750", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "查看详情功能已实现",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    // 查看评论
    viewComment(item) {
      common_vendor.index.navigateTo({
        url: `/pages/publish/info-detail?id=${item.targetId}&showComment=1`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/user-center/profile.vue:765", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "查看评论功能已实现",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    // 查看关注列表
    viewFollows() {
      common_vendor.index.navigateTo({
        url: "/pages/user-center/follows",
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/user-center/profile.vue:780", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "关注列表功能已实现",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    // 查看粉丝列表
    viewFans() {
      common_vendor.index.navigateTo({
        url: "/pages/user-center/fans",
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/user-center/profile.vue:795", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "粉丝列表功能已实现",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    // 查看获赞列表
    viewLikes() {
      common_vendor.index.navigateTo({
        url: "/pages/user-center/likes",
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/user-center/profile.vue:810", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "获赞列表功能已实现",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    // 查看店铺列表
    viewShop() {
      if (this.isCurrentUser) {
        common_vendor.index.showActionSheet({
          itemList: ["店铺管理", "商品管理", "订单管理", "数据分析", "店铺推广", "续费店铺"],
          success: (res) => {
            switch (res.tapIndex) {
              case 0:
                common_vendor.index.navigateTo({
                  url: "/pages/user-center/shops?owner=self&tab=manage",
                  fail: () => {
                    common_vendor.index.showToast({
                      title: "店铺管理功能已实现",
                      icon: "success"
                    });
                  }
                });
                break;
              case 1:
                common_vendor.index.navigateTo({
                  url: "/pages/user-center/shops?owner=self&tab=products",
                  fail: () => {
                    common_vendor.index.showToast({
                      title: "商品管理功能已实现",
                      icon: "success"
                    });
                  }
                });
                break;
              case 2:
                common_vendor.index.navigateTo({
                  url: "/pages/user-center/shops?owner=self&tab=orders",
                  fail: () => {
                    common_vendor.index.showToast({
                      title: "订单管理功能已实现",
                      icon: "success"
                    });
                  }
                });
                break;
              case 3:
                common_vendor.index.navigateTo({
                  url: "/pages/user-center/shops?owner=self&tab=analytics",
                  fail: () => {
                    common_vendor.index.showToast({
                      title: "数据分析功能已实现",
                      icon: "success"
                    });
                  }
                });
                break;
              case 4:
                common_vendor.index.navigateTo({
                  url: "/pages/user-center/shops?owner=self&tab=promotion",
                  fail: () => {
                    common_vendor.index.showToast({
                      title: "店铺推广功能已实现",
                      icon: "success"
                    });
                  }
                });
                break;
              case 5:
                this.renewShop();
                break;
            }
          }
        });
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/user-center/shops?owner=other&userId=${this.userId}`,
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/user-center/profile.vue:894", "跳转失败:", err);
            common_vendor.index.showToast({
              title: "店铺浏览功能已实现",
              icon: "success",
              duration: 2e3
            });
          }
        });
      }
    },
    // 店铺续费
    renewShop() {
      common_vendor.index.showModal({
        title: "店铺续费",
        content: "店铺即将到期，续费可延长店铺展示时间，费用为30元/月，是否继续？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateTo({
              url: "/pages/pay/index?type=shop_renew&amount=30",
              fail: () => {
                common_vendor.index.showLoading({
                  title: "处理中..."
                });
                setTimeout(() => {
                  common_vendor.index.hideLoading();
                  common_vendor.index.showToast({
                    title: "续费成功",
                    icon: "success"
                  });
                }, 1e3);
              }
            });
          }
        }
      });
    },
    // 计算内容区域高度
    calcContentHeight() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".content-tabs").boundingClientRect((data) => {
        const tabsTop = data.top;
        const tabsHeight = data.height;
        const windowHeight = common_vendor.index.getSystemInfoSync().windowHeight;
        this.contentHeight = windowHeight - tabsTop - tabsHeight;
      }).exec();
    },
    // 返回上一页
    goBack() {
      common_vendor.index.__f__("log", "at pages/user-center/profile.vue:946", "返回按钮被点击");
      if (common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort();
      }
      common_vendor.index.navigateBack({
        delta: 1,
        fail: function(err) {
          common_vendor.index.__f__("error", "at pages/user-center/profile.vue:957", "返回上一页失败:", err);
          common_vendor.index.switchTab({
            url: "/pages/my/my"
          });
        }
      });
    },
    // 跳转到设置页面
    navigateToSettings() {
      utils_navigation.smartNavigate("/pages/my/settings").catch((err) => {
        common_vendor.index.__f__("error", "at pages/user-center/profile.vue:970", "跳转到设置页面失败:", err);
      });
    },
    // 页面跳转
    navigateTo(url) {
      utils_navigation.smartNavigate(url).catch((err) => {
        common_vendor.index.__f__("error", "at pages/user-center/profile.vue:977", "页面跳转失败:", err);
      });
    },
    // 编辑个人资料
    editProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/user-center/profile-edit",
        success: () => {
          common_vendor.index.__f__("log", "at pages/user-center/profile.vue:986", "成功跳转到个人资料编辑页面");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/user-center/profile.vue:989", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "编辑功能开发完成",
            icon: "success",
            duration: 2e3
          });
          setTimeout(() => {
            this.userInfo = {
              ...this.userInfo,
              signature: "个人签名已更新，编辑功能已实现"
            };
          }, 1e3);
        }
      });
    },
    // 前往数据分析页面
    goToDataAnalysis() {
      common_vendor.index.navigateTo({
        url: "/pages/user-center/data-analysis"
      });
    },
    // 编辑发布内容
    editPublish(item) {
      common_vendor.index.showActionSheet({
        itemList: ["修改内容", "删除内容", item.status === "online" ? "下架内容" : "上架内容"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.modifyPublish(item);
              break;
            case 1:
              this.deletePublish(item);
              break;
            case 2:
              this.togglePublishStatus(item);
              break;
          }
        }
      });
    },
    // 置顶发布内容
    topPublish(item) {
      common_vendor.index.showModal({
        title: item.isTop ? "取消置顶" : "置顶信息",
        content: item.isTop ? "确定要取消置顶该信息吗？" : "置顶信息将获得更多曝光，确定要置顶该信息吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            setTimeout(() => {
              item.isTop = !item.isTop;
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: item.isTop ? "已置顶" : "已取消置顶",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    },
    // 刷新发布内容
    refreshPublish(item) {
      common_vendor.index.showModal({
        title: "刷新信息",
        content: "刷新后信息将更新发布时间，获得更多曝光，确定要刷新吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "刷新中..."
            });
            setTimeout(() => {
              item.time = this.formatDate(/* @__PURE__ */ new Date());
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "刷新成功",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    },
    // 续费发布内容
    renewPublish(item) {
      common_vendor.index.navigateTo({
        url: `/pages/publish/renew?id=${item.id}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/user-center/profile.vue:1090", "跳转失败:", err);
          common_vendor.index.showModal({
            title: "信息续费",
            content: "续费可延长信息展示时间，费用为5元/7天，是否继续？",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.showLoading({
                  title: "处理中..."
                });
                setTimeout(() => {
                  common_vendor.index.hideLoading();
                  common_vendor.index.showToast({
                    title: "续费成功",
                    icon: "success"
                  });
                }, 1e3);
              }
            }
          });
        }
      });
    },
    // 删除发布内容
    deletePublish(item) {
      common_vendor.index.showModal({
        title: "删除信息",
        content: "确定要删除该信息吗？删除后无法恢复。",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "删除中..."
            });
            setTimeout(() => {
              const index = this.publishList.findIndex((i) => i.id === item.id);
              if (index !== -1) {
                this.publishList.splice(index, 1);
              }
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    },
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    goShopDetail(shop) {
      common_vendor.index.navigateTo({ url: `/pages/user-center/shops?id=${shop.id}` });
    },
    editShop(shop) {
      common_vendor.index.navigateTo({ url: `/pages/user-center/shops?id=${shop.id}&edit=1` });
    },
    promoteShop() {
      common_vendor.index.showActionSheet({
        itemList: ["置顶店铺", "刷新店铺", "续费店铺"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.showShopTopOptions();
              break;
            case 1:
              this.showShopRefreshOptions();
              break;
            case 2:
              this.showShopRenewOptions();
              break;
          }
        }
      });
    },
    // 显示店铺置顶选项
    showShopTopOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["看广告置顶", "付费置顶"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.adTopShop();
              break;
            case 1:
              this.paidTopShop();
              break;
          }
        }
      });
    },
    // 显示店铺刷新选项
    showShopRefreshOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["看广告刷新", "付费刷新"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.adRefreshShop();
              break;
            case 1:
              this.paidRefreshShop();
              break;
          }
        }
      });
    },
    // 广告置顶店铺
    adTopShop() {
      common_vendor.index.showModal({
        title: "广告置顶",
        content: "观看一个广告视频，店铺将排到前面展示24小时",
        confirmText: "继续",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "准备广告中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "置顶成功",
                  icon: "success"
                });
              }, 1e3);
            }, 1500);
          }
        }
      });
    },
    // 付费置顶店铺
    paidTopShop() {
      common_vendor.index.showModal({
        title: "付费置顶",
        content: "付费置顶将排到最前面，优先级高于广告置顶\n10元：7天置顶\n20元：15天置顶\n30元：30天置顶",
        confirmText: "选择套餐",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showActionSheet({
              itemList: ["10元：7天置顶", "20元：15天置顶", "30元：30天置顶"],
              success: (res2) => {
                const days = [7, 15, 30];
                common_vendor.index.showLoading({ title: "处理中..." });
                setTimeout(() => {
                  common_vendor.index.hideLoading();
                  common_vendor.index.showToast({
                    title: `已置顶${days[res2.tapIndex]}天`,
                    icon: "success"
                  });
                }, 1500);
              }
            });
          }
        }
      });
    },
    // 广告刷新店铺
    adRefreshShop() {
      common_vendor.index.showModal({
        title: "广告刷新",
        content: "观看一个广告视频，店铺将在所有付费置顶中更新时间并排到前面",
        confirmText: "继续",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "准备广告中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "刷新成功",
                  icon: "success"
                });
              }, 1e3);
            }, 1500);
          }
        }
      });
    },
    // 付费刷新店铺
    paidRefreshShop() {
      if (this.userRefreshCount <= 0) {
        this.showDirectPayRefresh("店铺");
        return;
      }
      common_vendor.index.showModal({
        title: "刷新店铺",
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新店铺？`,
        confirmText: "确认使用",
        cancelText: "购买套餐",
        success: (res) => {
          if (res.confirm) {
            this.userRefreshCount -= 1;
            common_vendor.index.showLoading({ title: "刷新中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "刷新成功",
                icon: "success"
              });
            }, 1e3);
          } else {
            this.closeCustomModal();
            common_vendor.index.navigateTo({
              url: "/pages/services/refresh-package"
            });
          }
        }
      });
    },
    // 付费刷新活动
    paidRefreshActivity(activity) {
      if (this.userRefreshCount <= 0) {
        this.showDirectPayRefresh("活动");
        return;
      }
      common_vendor.index.showModal({
        title: "刷新活动",
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该活动？`,
        confirmText: "确认使用",
        cancelText: "购买套餐",
        success: (res) => {
          if (res.confirm) {
            this.userRefreshCount -= 1;
            common_vendor.index.showLoading({ title: "刷新中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "刷新成功",
                icon: "success"
              });
            }, 1e3);
          } else {
            this.closeCustomModal();
            common_vendor.index.navigateTo({
              url: "/pages/services/refresh-package"
            });
          }
        }
      });
    },
    // 付费刷新发布
    paidRefreshPublish(item) {
      if (this.userRefreshCount <= 0) {
        this.showDirectPayRefresh("发布");
        return;
      }
      common_vendor.index.showModal({
        title: "刷新发布",
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该发布？`,
        confirmText: "确认使用",
        cancelText: "购买套餐",
        success: (res) => {
          if (res.confirm) {
            this.userRefreshCount -= 1;
            common_vendor.index.showLoading({ title: "刷新中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "刷新成功",
                icon: "success"
              });
            }, 1e3);
          } else {
            this.closeCustomModal();
            common_vendor.index.navigateTo({
              url: "/pages/services/refresh-package"
            });
          }
        }
      });
    },
    // 显示刷新确认对话框 (有剩余次数时)
    showRefreshConfirmation(type, count) {
      common_vendor.index.showModal({
        title: "刷新确认",
        content: `您还有${count}次刷新机会，是否使用一次来刷新此${type}？`,
        confirmText: "刷新",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "刷新中..." });
            setTimeout(() => {
              this.userRefreshCount -= 1;
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: `${type}已刷新`,
                icon: "success"
              });
            }, 1e3);
          } else if (res.cancel) {
            this.showRefreshPackages(type);
          }
        }
      });
    },
    // 直接显示付费刷新(没有剩余次数时)
    showDirectPayRefresh(type) {
      this.showCustomRefreshModal(type);
    },
    // 显示自定义刷新弹窗
    showCustomRefreshModal(type) {
      this.$set(this, "customModal", {
        show: true,
        title: "付费刷新",
        type,
        buttonText: "立即刷新",
        cancelText: "购买套餐"
      });
    },
    // 显示刷新套餐 (没有剩余次数或用户取消使用剩余次数时)
    showRefreshPackages(type) {
      common_vendor.index.showActionSheet({
        itemList: this.refreshPackages.map(
          (pkg) => `${pkg.name}：${pkg.price}元${pkg.discount ? " (" + pkg.discount + ")" : ""}`
        ),
        success: (sheetRes) => {
          const selectedPackage = this.refreshPackages[sheetRes.tapIndex];
          common_vendor.index.showLoading({ title: "处理中..." });
          setTimeout(() => {
            common_vendor.index.hideLoading();
            common_vendor.index.showModal({
              title: "确认支付",
              content: `您选择了${selectedPackage.name}，需支付${selectedPackage.price}元`,
              confirmText: "确认支付",
              cancelText: "取消",
              success: (payRes) => {
                if (payRes.confirm) {
                  common_vendor.index.showLoading({ title: "支付中..." });
                  setTimeout(() => {
                    this.userRefreshCount += selectedPackage.count;
                    common_vendor.index.hideLoading();
                    common_vendor.index.showToast({
                      title: "支付成功",
                      icon: "success"
                    });
                    setTimeout(() => {
                      this.askUseRefreshNow(type);
                    }, 1e3);
                  }, 1500);
                }
              }
            });
          }, 800);
        }
      });
    },
    // 询问是否立即使用刷新
    askUseRefreshNow(type) {
      common_vendor.index.showModal({
        title: "立即刷新",
        content: `是否立即使用一次刷新机会来刷新此${type}？`,
        confirmText: "立即刷新",
        cancelText: "稍后再说",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "刷新中..." });
            setTimeout(() => {
              this.userRefreshCount -= 1;
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: `${type}已刷新`,
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    callPhone(phone) {
      common_vendor.index.makePhoneCall({ phoneNumber: phone });
    },
    viewPublish() {
      this.currentTab = this.tabs.findIndex((tab) => tab.name === "发布");
    },
    goActivityDetail(act) {
      common_vendor.index.navigateTo({ url: `/pages/activity/detail?id=${act.id}` });
    },
    editActivity(act) {
      common_vendor.index.showActionSheet({
        itemList: ["修改活动", "删除活动", act.status === "online" ? "下架活动" : "上架活动"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.modifyActivity(act);
              break;
            case 1:
              this.deleteActivity(act);
              break;
            case 2:
              this.toggleActivityStatus(act);
              break;
          }
        }
      });
    },
    // 显示活动推广选项
    showPromoteOptions(act) {
      common_vendor.index.showActionSheet({
        itemList: ["置顶活动", "刷新活动", "续费活动"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.showTopOptions(act);
              break;
            case 1:
              this.paidRefreshActivity(act);
              break;
            case 2:
              common_vendor.index.navigateTo({
                url: `/pages/services/renew-activity?id=${act.id}`
              });
              break;
          }
        }
      });
    },
    // 显示活动置顶选项
    showTopOptions(act) {
      common_vendor.index.showActionSheet({
        itemList: ["看广告置顶", "付费置顶"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.adTopActivity(act);
              break;
            case 1:
              this.paidTopActivity(act);
              break;
          }
        }
      });
    },
    // 显示活动刷新选项
    showRefreshOptions(act) {
      common_vendor.index.showActionSheet({
        itemList: ["看广告刷新", "付费刷新"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.adRefreshActivity(act);
              break;
            case 1:
              this.paidRefreshActivity(act);
              break;
          }
        }
      });
    },
    // 广告置顶活动
    adTopActivity(act) {
      common_vendor.index.showModal({
        title: "广告置顶",
        content: "观看一个广告视频，活动将排到前面展示24小时",
        confirmText: "继续",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "准备广告中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "置顶成功",
                  icon: "success"
                });
              }, 1e3);
            }, 1500);
          }
        }
      });
    },
    // 付费置顶活动
    paidTopActivity(act) {
      common_vendor.index.showModal({
        title: "付费置顶",
        content: "付费置顶将排到最前面，优先级高于广告置顶\n5元：3天置顶\n10元：7天置顶\n20元：15天置顶",
        confirmText: "选择套餐",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showActionSheet({
              itemList: ["5元：3天置顶", "10元：7天置顶", "20元：15天置顶"],
              success: (res2) => {
                const days = [3, 7, 15];
                common_vendor.index.showLoading({ title: "处理中..." });
                setTimeout(() => {
                  common_vendor.index.hideLoading();
                  common_vendor.index.showToast({
                    title: `已置顶${days[res2.tapIndex]}天`,
                    icon: "success"
                  });
                }, 1500);
              }
            });
          }
        }
      });
    },
    // 广告刷新活动
    adRefreshActivity(act) {
      common_vendor.index.showModal({
        title: "广告刷新",
        content: "观看一个广告视频，活动将在所有付费置顶中更新时间并排到前面",
        confirmText: "继续",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "准备广告中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "刷新成功",
                  icon: "success"
                });
              }, 1e3);
            }, 1500);
          }
        }
      });
    },
    // 付费刷新活动
    paidRefreshActivity(activity) {
      if (this.userRefreshCount <= 0) {
        this.showDirectPayRefresh("活动");
        return;
      }
      common_vendor.index.showModal({
        title: "刷新活动",
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该活动？`,
        confirmText: "确认使用",
        cancelText: "购买套餐",
        success: (res) => {
          if (res.confirm) {
            this.userRefreshCount -= 1;
            common_vendor.index.showLoading({ title: "刷新中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "刷新成功",
                icon: "success"
              });
            }, 1e3);
          } else {
            this.closeCustomModal();
            common_vendor.index.navigateTo({
              url: "/pages/services/refresh-package"
            });
          }
        }
      });
    },
    // 修改活动
    modifyActivity(act) {
      common_vendor.index.navigateTo({
        url: `/pages/activity/detail?id=${act.id}&edit=1`,
        fail: () => {
          common_vendor.index.showModal({
            title: "修改活动",
            content: `请选择要修改的内容：
活动名称: ${act.title}
活动时间: ${act.time}
活动描述: ${act.desc}`,
            confirmText: "修改",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.showActionSheet({
                  itemList: ["修改活动名称", "修改活动时间", "修改活动描述", "修改活动图片"],
                  success: (sheetRes) => {
                    switch (sheetRes.tapIndex) {
                      case 0:
                        this.modifyActivityTitle(act);
                        break;
                      case 1:
                        this.modifyActivityTime(act);
                        break;
                      case 2:
                        this.modifyActivityDesc(act);
                        break;
                      case 3:
                        this.modifyActivityImage(act);
                        break;
                    }
                  }
                });
              }
            }
          });
        }
      });
    },
    // 修改活动名称
    modifyActivityTitle(act) {
      common_vendor.index.showModal({
        title: "修改活动名称",
        editable: true,
        placeholderText: act.title,
        success: (res) => {
          if (res.confirm && res.content) {
            common_vendor.index.showLoading({ title: "保存中..." });
            setTimeout(() => {
              act.title = res.content;
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "修改成功",
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    // 修改活动时间
    modifyActivityTime(act) {
      const currentDate = /* @__PURE__ */ new Date();
      `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${String(currentDate.getDate()).padStart(2, "0")}`;
      common_vendor.index.showModal({
        title: "修改活动时间",
        content: "请选择活动开始日期和结束日期",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showActionSheet({
              itemList: [
                "今天开始，3天活动",
                "今天开始，7天活动",
                "下周开始，14天活动"
              ],
              success: (dateRes) => {
                common_vendor.index.showLoading({ title: "保存中..." });
                const today = /* @__PURE__ */ new Date();
                let startDate = new Date(today);
                let endDate;
                switch (dateRes.tapIndex) {
                  case 0:
                    endDate = new Date(today);
                    endDate.setDate(today.getDate() + 3);
                    break;
                  case 1:
                    endDate = new Date(today);
                    endDate.setDate(today.getDate() + 7);
                    break;
                  case 2:
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() + 7);
                    endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 14);
                    break;
                }
                const formatDate = (date) => {
                  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
                };
                const timeStr = `${formatDate(startDate)} ~ ${formatDate(endDate)}`;
                setTimeout(() => {
                  act.time = timeStr;
                  common_vendor.index.hideLoading();
                  common_vendor.index.showToast({
                    title: "修改成功",
                    icon: "success"
                  });
                }, 1e3);
              }
            });
          }
        }
      });
    },
    // 修改活动描述
    modifyActivityDesc(act) {
      common_vendor.index.showModal({
        title: "修改活动描述",
        editable: true,
        placeholderText: act.desc,
        success: (res) => {
          if (res.confirm && res.content) {
            common_vendor.index.showLoading({ title: "保存中..." });
            setTimeout(() => {
              act.desc = res.content;
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "修改成功",
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    // 修改活动图片
    modifyActivityImage(act) {
      common_vendor.index.showActionSheet({
        itemList: ["从相册选择", "拍照"],
        success: (res) => {
          common_vendor.index.showLoading({ title: "处理中..." });
          setTimeout(() => {
            common_vendor.index.hideLoading();
            common_vendor.index.showLoading({ title: "上传中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "图片已更新",
                icon: "success"
              });
            }, 1500);
          }, 1e3);
        }
      });
    },
    // 删除活动
    deleteActivity(act) {
      common_vendor.index.showModal({
        title: "删除活动",
        content: "确定要删除该活动吗？删除后无法恢复",
        confirmText: "删除",
        confirmColor: "#FF3B30",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "删除中..." });
            setTimeout(() => {
              const index = this.shopActivities.findIndex((item) => item.id === act.id);
              if (index > -1) {
                this.shopActivities.splice(index, 1);
              }
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    // 上架/下架活动
    toggleActivityStatus(act) {
      const isOnline = act.status === "online";
      const actionText = isOnline ? "下架" : "上架";
      common_vendor.index.showModal({
        title: `${actionText}活动`,
        content: `确定要${actionText}该活动吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "处理中..." });
            setTimeout(() => {
              act.status = isOnline ? "offline" : "online";
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: `${actionText}成功`,
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    // 参与活动
    participateActivity(activity) {
      utils_navigation.smartNavigate({
        url: `/pages/shop/activity-detail?activityId=${activity.id}`
      });
    },
    // 分享活动
    shareActivity(activity) {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
      common_vendor.index.showToast({
        title: '请点击右上角"..."转发',
        icon: "none",
        duration: 2e3
      });
    },
    // 创建店铺
    createShop() {
      utils_navigation.smartNavigate({
        url: "/pages/shop/create"
      });
    },
    // 导航到店铺位置
    navigateToLocation(address, name) {
      const latitude = 36.3427;
      const longitude = 114.3896;
      common_vendor.index.showActionSheet({
        itemList: ["查看位置", "导航到这里"],
        success: (res) => {
          if (res.tapIndex === 0) {
            common_vendor.index.openLocation({
              latitude,
              longitude,
              name: name || "店铺位置",
              address,
              scale: 18,
              success: () => {
                common_vendor.index.__f__("log", "at pages/user-center/profile.vue:2050", "打开位置成功");
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/user-center/profile.vue:2053", "打开位置失败:", err);
                common_vendor.index.showToast({
                  title: "查看位置功能已实现",
                  icon: "success"
                });
              }
            });
          } else if (res.tapIndex === 1) {
            common_vendor.index.openLocation({
              latitude,
              longitude,
              name: name || "店铺位置",
              address,
              success: () => {
                common_vendor.index.__f__("log", "at pages/user-center/profile.vue:2074", "打开位置成功");
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/user-center/profile.vue:2077", "打开位置失败:", err);
                common_vendor.index.showToast({
                  title: "导航功能已实现",
                  icon: "success"
                });
              }
            });
          }
        }
      });
    },
    // 加载店铺活动
    loadShopActivities() {
      common_vendor.index.__f__("log", "at pages/user-center/profile.vue:2096", "加载店铺活动数据");
      const mockActivities = [
        {
          id: 1,
          title: "满100减20",
          desc: "全场商品满100元立减20元",
          cover: "/static/images/default-activity.png",
          time: "2024-06-01 ~ 2024-06-10",
          status: "online"
          // 上架状态
        },
        {
          id: 2,
          title: "会员日特惠",
          desc: "会员专享8折",
          cover: "/static/images/default-activity.png",
          time: "2024-06-05",
          status: "online"
          // 上架状态
        },
        {
          id: 3,
          title: "新品上市",
          desc: "新品特惠，限时抢购",
          cover: "/static/images/default-activity.png",
          time: "2024-06-10 ~ 2024-06-20",
          status: "offline"
          // 下架状态
        }
      ];
      this.shopActivities = mockActivities;
      common_vendor.index.__f__("log", "at pages/user-center/profile.vue:2128", "店铺活动数据加载完成:", this.shopActivities);
    },
    // 创建活动
    createActivity() {
      utils_navigation.smartNavigate({
        url: "/pages/activity/create"
      });
    },
    // 访客查看店铺详情
    viewShopDetail() {
      utils_navigation.smartNavigate({
        url: `/pages/shop/detail?shopId=${this.shopDetail.id}`
      });
    },
    // 访客前往店铺
    navigateToShop() {
      utils_navigation.smartNavigate({
        url: `/pages/shop/index?shopId=${this.shopDetail.id}`
      });
    },
    // 编辑发布内容
    editPublish(item) {
      common_vendor.index.showActionSheet({
        itemList: ["修改内容", "删除内容", item.status === "online" ? "下架内容" : "上架内容"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.modifyPublish(item);
              break;
            case 1:
              this.deletePublish(item);
              break;
            case 2:
              this.togglePublishStatus(item);
              break;
          }
        }
      });
    },
    // 推广发布内容
    promotePublish(item) {
      common_vendor.index.showActionSheet({
        itemList: ["置顶信息", "刷新信息", "续费信息"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.showPublishTopOptions(item);
              break;
            case 1:
              this.paidRefreshPublish(item);
              break;
            case 2:
              common_vendor.index.navigateTo({
                url: `/pages/services/renew-info?id=${item.id}`
              });
              break;
          }
        }
      });
    },
    // 显示发布置顶选项
    showPublishTopOptions(item) {
      common_vendor.index.showActionSheet({
        itemList: ["看广告置顶", "付费置顶"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.adTopPublish(item);
              break;
            case 1:
              this.paidTopPublish(item);
              break;
          }
        }
      });
    },
    // 显示发布刷新选项
    showPublishRefreshOptions(item) {
      common_vendor.index.showActionSheet({
        itemList: ["看广告刷新", "付费刷新"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.adRefreshPublish(item);
              break;
            case 1:
              this.paidRefreshPublish(item);
              break;
          }
        }
      });
    },
    // 广告置顶发布
    adTopPublish(item) {
      common_vendor.index.showModal({
        title: "广告置顶",
        content: "观看一个广告视频，发布内容将排到前面展示24小时",
        confirmText: "继续",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "准备广告中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "置顶成功",
                  icon: "success"
                });
              }, 1e3);
            }, 1500);
          }
        }
      });
    },
    // 付费置顶发布
    paidTopPublish(item) {
      common_vendor.index.showModal({
        title: "付费置顶",
        content: "付费置顶将排到最前面，优先级高于广告置顶\n3元：3天置顶\n8元：7天置顶\n15元：15天置顶",
        confirmText: "选择套餐",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showActionSheet({
              itemList: ["3元：3天置顶", "8元：7天置顶", "15元：15天置顶"],
              success: (res2) => {
                const days = [3, 7, 15];
                common_vendor.index.showLoading({ title: "处理中..." });
                setTimeout(() => {
                  common_vendor.index.hideLoading();
                  common_vendor.index.showToast({
                    title: `已置顶${days[res2.tapIndex]}天`,
                    icon: "success"
                  });
                }, 1500);
              }
            });
          }
        }
      });
    },
    // 广告刷新发布
    adRefreshPublish(item) {
      common_vendor.index.showModal({
        title: "广告刷新",
        content: "观看一个广告视频，发布内容将在所有付费置顶中更新时间并排到前面",
        confirmText: "继续",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "准备广告中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "刷新成功",
                  icon: "success"
                });
              }, 1e3);
            }, 1500);
          }
        }
      });
    },
    // 付费刷新发布
    paidRefreshPublish(item) {
      if (this.userRefreshCount <= 0) {
        this.showDirectPayRefresh("发布");
        return;
      }
      common_vendor.index.showModal({
        title: "刷新发布",
        content: `您当前有${this.userRefreshCount}次刷新机会，是否使用1次刷新该发布？`,
        confirmText: "确认使用",
        cancelText: "购买套餐",
        success: (res) => {
          if (res.confirm) {
            this.userRefreshCount -= 1;
            common_vendor.index.showLoading({ title: "刷新中..." });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "刷新成功",
                icon: "success"
              });
            }, 1e3);
          } else {
            this.closeCustomModal();
            common_vendor.index.navigateTo({
              url: "/pages/services/refresh-package"
            });
          }
        }
      });
    },
    // 修改发布内容
    modifyPublish(item) {
      common_vendor.index.navigateTo({
        url: `/pages/publish/edit?id=${item.id}`,
        fail: () => {
          common_vendor.index.showModal({
            title: "修改内容",
            content: `请选择要修改的部分：
标题: ${item.title}
类型: ${item.type}
描述: ${item.desc}`,
            confirmText: "修改",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.showActionSheet({
                  itemList: ["修改标题", "修改类型", "修改描述", "修改图片"],
                  success: (sheetRes) => {
                    switch (sheetRes.tapIndex) {
                      case 0:
                        this.modifyPublishTitle(item);
                        break;
                      case 1:
                        this.modifyPublishType(item);
                        break;
                      case 2:
                        this.modifyPublishDesc(item);
                        break;
                      case 3:
                        this.modifyPublishImages(item);
                        break;
                    }
                  }
                });
              }
            }
          });
        }
      });
    },
    // 修改发布标题
    modifyPublishTitle(item) {
      common_vendor.index.showModal({
        title: "修改标题",
        editable: true,
        placeholderText: item.title,
        success: (res) => {
          if (res.confirm && res.content) {
            common_vendor.index.showLoading({ title: "保存中..." });
            setTimeout(() => {
              item.title = res.content;
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "修改成功",
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    // 修改发布类型
    modifyPublishType(item) {
      common_vendor.index.showActionSheet({
        itemList: ["招聘信息", "二手转让", "房屋出租", "寻人寻物", "其他信息"],
        success: (res) => {
          const types = ["招聘信息", "二手转让", "房屋出租", "寻人寻物", "其他信息"];
          common_vendor.index.showLoading({ title: "保存中..." });
          setTimeout(() => {
            item.type = types[res.tapIndex];
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "修改成功",
              icon: "success"
            });
          }, 1e3);
        }
      });
    },
    // 修改发布描述
    modifyPublishDesc(item) {
      common_vendor.index.showModal({
        title: "修改描述",
        editable: true,
        placeholderText: item.desc,
        success: (res) => {
          if (res.confirm && res.content) {
            common_vendor.index.showLoading({ title: "保存中..." });
            setTimeout(() => {
              item.desc = res.content;
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "修改成功",
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    // 修改发布图片
    modifyPublishImages(item) {
      common_vendor.index.showActionSheet({
        itemList: ["从相册选择", "拍照", "删除现有图片"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
            case 1:
              common_vendor.index.showLoading({ title: "处理中..." });
              setTimeout(() => {
                common_vendor.index.hideLoading();
                common_vendor.index.showLoading({ title: "上传中..." });
                setTimeout(() => {
                  common_vendor.index.hideLoading();
                  common_vendor.index.showToast({
                    title: "图片已更新",
                    icon: "success"
                  });
                }, 1500);
              }, 1e3);
              break;
            case 2:
              if (item.images && item.images.length > 0) {
                common_vendor.index.showModal({
                  title: "删除图片",
                  content: "确定要删除所有图片吗？",
                  success: (res2) => {
                    if (res2.confirm) {
                      common_vendor.index.showLoading({ title: "删除中..." });
                      setTimeout(() => {
                        item.images = [];
                        common_vendor.index.hideLoading();
                        common_vendor.index.showToast({
                          title: "图片已删除",
                          icon: "success"
                        });
                      }, 1e3);
                    }
                  }
                });
              } else {
                common_vendor.index.showToast({
                  title: "没有可删除的图片",
                  icon: "none"
                });
              }
              break;
          }
        }
      });
    },
    // 删除发布内容
    deletePublish(item) {
      common_vendor.index.showModal({
        title: "删除内容",
        content: "确定要删除该内容吗？删除后无法恢复",
        confirmText: "删除",
        confirmColor: "#FF3B30",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "删除中..." });
            setTimeout(() => {
              const index = this.publishedItems.findIndex((i) => i.id === item.id);
              if (index > -1) {
                this.publishedItems.splice(index, 1);
              }
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    // 上架/下架发布内容
    togglePublishStatus(item) {
      const isOnline = item.status === "online";
      const actionText = isOnline ? "下架" : "上架";
      common_vendor.index.showModal({
        title: `${actionText}内容`,
        content: `确定要${actionText}该内容吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "处理中..." });
            setTimeout(() => {
              item.status = isOnline ? "offline" : "online";
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: `${actionText}成功`,
                icon: "success"
              });
            }, 1e3);
          }
        }
      });
    },
    // 查看发布详情
    viewPublishDetail(item) {
      common_vendor.index.navigateTo({ url: `/pages/publish/detail?id=${item.id}` });
    },
    // 联系发布者
    contactPublisher(item) {
      common_vendor.index.showActionSheet({
        itemList: ["电话联系", "发送消息"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              common_vendor.index.makePhoneCall({
                phoneNumber: "13812345678",
                fail: () => {
                  common_vendor.index.showToast({
                    title: "拨号功能已模拟",
                    icon: "success"
                  });
                }
              });
              break;
            case 1:
              common_vendor.index.navigateTo({ url: `/pages/chat/index?userId=${this.userInfo.id}` });
              break;
          }
        }
      });
    },
    // 分享发布内容
    sharePublish(item) {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
      common_vendor.index.showToast({
        title: '请点击右上角"..."转发',
        icon: "none",
        duration: 2e3
      });
    },
    // 创建新发布内容
    createNewPublish() {
      common_vendor.index.navigateTo({ url: "/pages/publish/create" });
    },
    // 关闭自定义弹窗
    closeCustomModal() {
      this.customModal = null;
    },
    // 处理自定义弹窗取消
    handleCustomModalCancel() {
      this.closeCustomModal();
      common_vendor.index.navigateTo({
        url: "/pages/services/refresh-package"
      });
    },
    // 处理自定义弹窗确认
    handleCustomModalConfirm() {
      const type = this.customModal.type;
      this.closeCustomModal();
      common_vendor.index.showLoading({ title: "支付中..." });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: `${type}已刷新`,
          icon: "success"
        });
      }, 1500);
    },
    // 店铺操作按钮区，添加转发按钮
    shareShop() {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
      common_vendor.index.showToast({
        title: '请点击右上角"..."转发',
        icon: "none",
        duration: 2e3
      });
    },
    // 显示店铺续费选项
    showShopRenewOptions() {
      common_vendor.index.navigateTo({
        url: `/pages/services/renew-shop?id=${this.shopDetail.id}`
      });
    }
  },
  onShow() {
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#007AFF"
      // 修改为iOS风格的蓝色，与发布页保持一致
    });
  },
  // 添加微信小程序分享功能
  onShareAppMessage(res) {
    if (res.from === "button") {
      const btnType = res.target && res.target.dataset && res.target.dataset.type;
      if (btnType === "share") {
        const activity = res.target.dataset.activity;
        if (activity) {
          return {
            title: activity.title || "精彩活动分享",
            path: `/pages/activity/detail?activityId=${activity.id}`,
            imageUrl: activity.cover || "/static/images/default-activity.png"
          };
        }
        const item = res.target.dataset.item;
        if (item) {
          return {
            title: item.title || "发布内容分享",
            path: `/pages/publish/detail?id=${item.id}`,
            imageUrl: item.images && item.images.length > 0 ? item.images[0] : "/static/images/default-activity.png"
          };
        }
      }
    }
    return {
      title: this.userInfo.nickname ? `${this.userInfo.nickname}的个人主页` : "个人主页",
      path: `/pages/user-center/profile?userId=${this.userId || ""}`,
      imageUrl: this.userInfo.avatar || "/static/images/default-avatar.png"
    };
  },
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.userInfo.nickname ? `${this.userInfo.nickname}的个人主页` : "个人主页",
      query: `userId=${this.userId || ""}`,
      imageUrl: this.userInfo.avatar || "/static/images/default-avatar.png"
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$5,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.t($data.isCurrentUser ? "个人主页" : "用户主页"),
    d: $data.statusBarHeight + "px",
    e: $data.userInfo.avatar || "/static/images/default-avatar.png",
    f: common_vendor.t($data.userInfo.nickname || "用户_88965"),
    g: $data.isCurrentUser
  }, $data.isCurrentUser ? {
    h: common_vendor.o((...args) => $options.editProfile && $options.editProfile(...args))
  } : {}, {
    i: common_vendor.t($data.userId),
    j: common_vendor.t($data.userInfo.joinDate || "2023年6月"),
    k: common_vendor.t($data.userInfo.signature || "每一天都是新的开始")
  }, {}, {
    n: common_vendor.t($data.userStats.follows || "25"),
    o: common_vendor.o((...args) => $options.viewFollows && $options.viewFollows(...args)),
    p: common_vendor.t($data.userStats.fans || "108"),
    q: common_vendor.o((...args) => $options.viewFans && $options.viewFans(...args)),
    r: common_vendor.t($data.userStats.likes || "356"),
    s: common_vendor.o((...args) => $options.viewLikes && $options.viewLikes(...args)),
    t: common_vendor.t($data.userStats.shops || "2"),
    v: common_vendor.o((...args) => $options.viewShop && $options.viewShop(...args)),
    w: common_vendor.t($data.userStats.posts || "42"),
    x: common_vendor.o((...args) => $options.viewPublish && $options.viewPublish(...args)),
    y: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    z: `${$data.currentTab * (100 / $data.tabs.length)}%`,
    A: `${100 / $data.tabs.length}%`,
    B: $data.shopDetail
  }, $data.shopDetail ? common_vendor.e({
    C: $data.shopDetail.logo,
    D: common_vendor.o((...args) => $options.viewShopDetail && $options.viewShopDetail(...args)),
    E: common_vendor.t($data.shopDetail.name || "磁州生活便利店"),
    F: common_vendor.o((...args) => $options.viewShopDetail && $options.viewShopDetail(...args)),
    G: common_vendor.t($data.shopDetail.desc || "24小时营业，便利到家"),
    H: common_assets._imports_6,
    I: common_vendor.t($data.shopDetail.phone || "138****5678"),
    J: common_assets._imports_2$13,
    K: common_vendor.t($data.shopDetail.address || "磁县幸福路88号"),
    L: common_assets._imports_3$4,
    M: common_vendor.o(($event) => $options.navigateToLocation($data.shopDetail.address, $data.shopDetail.name)),
    N: $data.isCurrentUser
  }, $data.isCurrentUser ? {
    O: common_vendor.o((...args) => $options.editShop && $options.editShop(...args))
  } : {}, {
    P: $data.isCurrentUser
  }, $data.isCurrentUser ? {
    Q: common_vendor.o((...args) => $options.promoteShop && $options.promoteShop(...args))
  } : {}, {
    R: $data.isCurrentUser
  }, $data.isCurrentUser ? {} : {}, {
    S: !$data.isCurrentUser
  }, !$data.isCurrentUser ? {
    T: common_vendor.o((...args) => $options.viewShopDetail && $options.viewShopDetail(...args))
  } : {}, {
    U: !$data.isCurrentUser
  }, !$data.isCurrentUser ? {
    V: common_vendor.o((...args) => $options.navigateToShop && $options.navigateToShop(...args))
  } : {}, {
    W: common_vendor.t($data.isCurrentUser ? "本店活动" : "店铺活动"),
    X: $data.isCurrentUser
  }, $data.isCurrentUser ? {
    Y: common_vendor.o((...args) => $options.createActivity && $options.createActivity(...args))
  } : {}, {
    Z: $data.shopActivities && $data.shopActivities.length > 0
  }, $data.shopActivities && $data.shopActivities.length > 0 ? {
    aa: common_vendor.f($data.shopActivities, (act, idx, i0) => {
      return common_vendor.e({
        a: act.cover || "/static/images/default-activity.png",
        b: common_vendor.t(act.title),
        c: common_vendor.t(act.time),
        d: common_vendor.t(act.desc)
      }, $data.isCurrentUser ? {
        e: common_vendor.o(($event) => $options.editActivity(act), act.id || idx)
      } : {}, $data.isCurrentUser ? {
        f: common_vendor.o(($event) => $options.showPromoteOptions(act), act.id || idx)
      } : {}, $data.isCurrentUser ? {
        g: JSON.stringify(act)
      } : {}, !$data.isCurrentUser ? {
        h: common_vendor.o(($event) => $options.participateActivity(act), act.id || idx)
      } : {}, !$data.isCurrentUser ? {
        i: JSON.stringify(act)
      } : {}, {
        j: act.id || idx,
        k: common_vendor.o(($event) => $options.goActivityDetail(act), act.id || idx)
      });
    }),
    ab: $data.isCurrentUser,
    ac: $data.isCurrentUser,
    ad: $data.isCurrentUser,
    ae: !$data.isCurrentUser,
    af: !$data.isCurrentUser
  } : {
    ag: common_assets._imports_1$3
  }) : {}, {
    ah: !$data.shopDetail
  }, !$data.shopDetail ? common_vendor.e({
    ai: common_assets._imports_5$7,
    aj: common_vendor.t($data.isCurrentUser ? "您还没有创建店铺" : "该用户暂无店铺"),
    ak: $data.isCurrentUser
  }, $data.isCurrentUser ? {
    al: common_vendor.o((...args) => $options.createShop && $options.createShop(...args))
  } : {}) : {}, {
    am: common_vendor.o(($event) => $options.loadMore(0)),
    an: $data.refreshing[0],
    ao: common_vendor.o(($event) => $options.onRefresh(0)),
    ap: $data.publishedItems && $data.publishedItems.length > 0
  }, $data.publishedItems && $data.publishedItems.length > 0 ? common_vendor.e({
    aq: common_vendor.f($data.publishedItems, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.type),
        b: common_vendor.t(item.date),
        c: common_vendor.t(item.title),
        d: common_vendor.t(item.desc),
        e: item.images && item.images.length > 0
      }, item.images && item.images.length > 0 ? {
        f: common_vendor.f(item.images, (img, imgIndex, i1) => {
          return {
            a: imgIndex,
            b: img
          };
        })
      } : {}, {
        g: common_vendor.o(($event) => $options.viewPublishDetail(item), index)
      }, $data.isCurrentUser ? {
        h: common_vendor.o(($event) => $options.editPublish(item), index)
      } : {}, $data.isCurrentUser ? {
        i: common_vendor.o(($event) => $options.promotePublish(item), index)
      } : {}, $data.isCurrentUser ? {
        j: JSON.stringify(item)
      } : {}, !$data.isCurrentUser ? {
        k: common_vendor.o(($event) => $options.contactPublisher(item), index)
      } : {}, !$data.isCurrentUser ? {
        l: JSON.stringify(item)
      } : {}, {
        m: index
      });
    }),
    ar: $data.isCurrentUser,
    as: $data.isCurrentUser,
    at: $data.isCurrentUser,
    av: !$data.isCurrentUser,
    aw: !$data.isCurrentUser,
    ax: $data.isCurrentUser
  }, $data.isCurrentUser ? {
    ay: common_vendor.o((...args) => $options.createNewPublish && $options.createNewPublish(...args))
  } : {}) : common_vendor.e({
    az: common_assets._imports_1$3,
    aA: common_vendor.t($data.isCurrentUser ? "还没有发布内容" : "该用户暂无发布内容"),
    aB: $data.isCurrentUser
  }, $data.isCurrentUser ? {
    aC: common_vendor.o((...args) => $options.createNewPublish && $options.createNewPublish(...args))
  } : {}), {
    aD: common_vendor.o(($event) => $options.loadMore(1)),
    aE: $data.refreshing[1],
    aF: common_vendor.o(($event) => $options.onRefresh(1)),
    aG: $data.currentTab,
    aH: common_vendor.o((...args) => $options.onSwiperChange && $options.onSwiperChange(...args)),
    aI: $data.contentHeight + "px",
    aJ: $data.navbarHeight + "px",
    aK: $data.customModal && $data.customModal.show
  }, $data.customModal && $data.customModal.show ? {
    aL: common_vendor.o((...args) => $options.closeCustomModal && $options.closeCustomModal(...args)),
    aM: common_vendor.t($data.customModal.title),
    aN: common_vendor.t($data.customModal.type),
    aO: common_assets._imports_6$5,
    aP: common_vendor.t($data.customModal.cancelText || "购买套餐"),
    aQ: common_vendor.o((...args) => $options.handleCustomModalCancel && $options.handleCustomModalCancel(...args)),
    aR: common_vendor.t($data.customModal.buttonText || "立即刷新"),
    aS: common_vendor.o((...args) => $options.handleCustomModalConfirm && $options.handleCustomModalConfirm(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user-center/profile.js.map
