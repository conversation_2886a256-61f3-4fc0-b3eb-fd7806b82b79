/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-4f90d1d5, html.data-v-4f90d1d5, #app.data-v-4f90d1d5, .index-container.data-v-4f90d1d5 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.member-discount-container.data-v-4f90d1d5 {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.page-header.data-v-4f90d1d5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.title-section .page-title.data-v-4f90d1d5 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.title-section .page-subtitle.data-v-4f90d1d5 {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.add-discount-btn.data-v-4f90d1d5 {
  display: flex;
  align-items: center;
  background-color: #4A00E0;
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
}
.add-discount-btn .btn-text.data-v-4f90d1d5 {
  font-size: 28rpx;
}
.add-discount-btn .btn-icon.data-v-4f90d1d5 {
  font-size: 32rpx;
  margin-left: 8rpx;
}
.discount-list .empty-tip.data-v-4f90d1d5 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: #fff;
  border-radius: 12rpx;
}
.discount-list .empty-tip .empty-icon.data-v-4f90d1d5 {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}
.discount-list .empty-tip .empty-text.data-v-4f90d1d5 {
  font-size: 28rpx;
  color: #999;
}
.discount-cards .discount-card.data-v-4f90d1d5 {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.discount-cards .discount-card .discount-card-header.data-v-4f90d1d5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  color: #fff;
}
.discount-cards .discount-card .discount-card-header .discount-name.data-v-4f90d1d5 {
  font-size: 32rpx;
  font-weight: bold;
}
.discount-cards .discount-card .discount-card-header .discount-actions.data-v-4f90d1d5 {
  display: flex;
}
.discount-cards .discount-card .discount-card-header .discount-actions .action-btn.data-v-4f90d1d5 {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  margin-left: 16rpx;
}
.discount-cards .discount-card .discount-card-header .discount-actions .action-btn.edit.data-v-4f90d1d5 {
  background-color: rgba(255, 255, 255, 0.3);
}
.discount-cards .discount-card .discount-card-header .discount-actions .action-btn.delete.data-v-4f90d1d5 {
  background-color: rgba(255, 255, 255, 0.3);
}
.discount-cards .discount-card .discount-card-body.data-v-4f90d1d5 {
  padding: 24rpx;
}
.discount-cards .discount-card .discount-card-body .discount-info-item.data-v-4f90d1d5 {
  display: flex;
  margin-bottom: 16rpx;
}
.discount-cards .discount-card .discount-card-body .discount-info-item .info-label.data-v-4f90d1d5 {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}
.discount-cards .discount-card .discount-card-body .discount-info-item .info-value.data-v-4f90d1d5 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.discount-cards .discount-card .discount-card-body .discount-info-item .discount-icon.data-v-4f90d1d5 {
  width: 60rpx;
  height: 60rpx;
}
.discount-cards .discount-card .discount-card-body .discount-info-item .level-tags.data-v-4f90d1d5 {
  display: flex;
  flex-wrap: wrap;
}
.discount-cards .discount-card .discount-card-body .discount-info-item .level-tags .level-tag.data-v-4f90d1d5 {
  font-size: 24rpx;
  color: #4A00E0;
  background-color: rgba(74, 0, 224, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
}
.discount-form-popup.data-v-4f90d1d5 {
  width: 650rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}
.discount-form-popup .popup-header.data-v-4f90d1d5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
}
.discount-form-popup .popup-header .popup-title.data-v-4f90d1d5 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.discount-form-popup .popup-header .popup-close.data-v-4f90d1d5 {
  font-size: 40rpx;
  color: #999;
}
.discount-form-popup .popup-body.data-v-4f90d1d5 {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}
.discount-form-popup .popup-body .form-item.data-v-4f90d1d5 {
  margin-bottom: 24rpx;
}
.discount-form-popup .popup-body .form-item .form-label.data-v-4f90d1d5 {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}
.discount-form-popup .popup-body .form-item .form-input.data-v-4f90d1d5 {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.discount-form-popup .popup-body .form-item .form-textarea.data-v-4f90d1d5 {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.discount-form-popup .popup-body .form-item .form-picker.data-v-4f90d1d5 {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.discount-form-popup .popup-body .form-item .form-picker .picker-value.data-v-4f90d1d5 {
  color: #333;
}
.discount-form-popup .popup-body .form-item .color-picker.data-v-4f90d1d5 {
  display: flex;
  flex-wrap: wrap;
}
.discount-form-popup .popup-body .form-item .color-picker .color-option.data-v-4f90d1d5 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.discount-form-popup .popup-body .form-item .color-picker .color-option.active.data-v-4f90d1d5::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 32rpx;
}
.discount-form-popup .popup-body .form-item .icon-upload .preview-icon.data-v-4f90d1d5 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
}
.discount-form-popup .popup-body .form-item .icon-upload .upload-btn.data-v-4f90d1d5 {
  width: 100rpx;
  height: 100rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.discount-form-popup .popup-body .form-item .icon-upload .upload-btn .upload-icon.data-v-4f90d1d5 {
  font-size: 40rpx;
  color: #999;
  margin-bottom: 4rpx;
}
.discount-form-popup .popup-body .form-item .icon-upload .upload-btn .upload-text.data-v-4f90d1d5 {
  font-size: 20rpx;
  color: #999;
}
.discount-form-popup .popup-body .form-item .level-checkboxes.data-v-4f90d1d5 {
  display: flex;
  flex-wrap: wrap;
}
.discount-form-popup .popup-body .form-item .level-checkboxes .level-checkbox.data-v-4f90d1d5 {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 20rpx;
}
.discount-form-popup .popup-body .form-item .level-checkboxes .level-checkbox .checkbox-icon.data-v-4f90d1d5 {
  width: 40rpx;
  height: 40rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  color: #fff;
  font-size: 24rpx;
}
.discount-form-popup .popup-body .form-item .level-checkboxes .level-checkbox.active .checkbox-icon.data-v-4f90d1d5 {
  background-color: #4A00E0;
  border-color: #4A00E0;
}
.discount-form-popup .popup-body .form-item .level-checkboxes .level-checkbox .checkbox-label.data-v-4f90d1d5 {
  font-size: 28rpx;
  color: #333;
}
.discount-form-popup .popup-footer.data-v-4f90d1d5 {
  display: flex;
  border-top: 1rpx solid #eee;
}
.discount-form-popup .popup-footer button.data-v-4f90d1d5 {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border-radius: 0;
}
.discount-form-popup .popup-footer button.cancel-btn.data-v-4f90d1d5 {
  background-color: #f5f5f5;
  color: #666;
}
.discount-form-popup .popup-footer button.confirm-btn.data-v-4f90d1d5 {
  background-color: #4A00E0;
  color: #fff;
}