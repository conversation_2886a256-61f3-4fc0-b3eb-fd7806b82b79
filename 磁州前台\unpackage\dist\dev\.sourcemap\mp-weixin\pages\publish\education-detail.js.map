{"version": 3, "file": "education-detail.js", "sources": ["pages/publish/education-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9lZHVjYXRpb24tZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"education-detail-container\">\n    <!-- 添加自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">教育培训详情</view>\n      <view class=\"navbar-right\">\n        <!-- 占位 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <view class=\"education-detail-wrapper\">\n      <!-- 课程基本信息卡片 -->\n      <view class=\"content-card course-info-card\">\n        <view class=\"course-header\">\n          <view class=\"course-title-row\">\n            <text class=\"course-title\">{{courseData.title}}</text>\n            <text class=\"course-price\">{{courseData.price}}</text>\n          </view>\n          <view class=\"course-meta\">\n            <view class=\"course-tag-group\">\n              <view class=\"course-tag\" v-for=\"(tag, index) in courseData.tags\" :key=\"index\">{{tag}}</view>\n            </view>\n            <text class=\"course-publish-time\">发布于 {{formatTime(courseData.publishTime)}}</text>\n          </view>\n        </view>\n        \n        <!-- 课程图片轮播 -->\n        <swiper class=\"course-swiper\" :indicator-dots=\"true\" :autoplay=\"true\" :interval=\"3000\" :duration=\"500\">\n          <swiper-item v-for=\"(image, index) in courseData.images\" :key=\"index\">\n            <image :src=\"image\" mode=\"aspectFill\" class=\"course-image\"></image>\n          </swiper-item>\n        </swiper>\n        \n        <!-- 基本信息 -->\n        <view class=\"course-basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">课程类型</text>\n            <text class=\"info-value\">{{courseData.type}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">适合人群</text>\n            <text class=\"info-value\">{{courseData.target}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">开课时间</text>\n            <text class=\"info-value\">{{courseData.startTime}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">课程周期</text>\n            <text class=\"info-value\">{{courseData.duration}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"contact-header\">\n          <text class=\"card-title\">联系方式</text>\n        </view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{courseData.contact.name}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{courseData.contact.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 课程内容 -->\n      <view class=\"content-card course-content-card\">\n        <view class=\"section-title\">课程内容</view>\n        <view class=\"content-list\">\n          <view class=\"content-item\" v-for=\"(item, index) in courseData.contents\" :key=\"index\">\n            <text class=\"content-label\">{{item.label}}</text>\n            <text class=\"content-value\">{{item.value}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 课程安排 -->\n      <view class=\"content-card schedule-card\">\n        <view class=\"section-title\">课程安排</view>\n        <view class=\"schedule-list\">\n          <view class=\"schedule-item\" v-for=\"(item, index) in courseData.schedule\" :key=\"index\">\n            <view class=\"schedule-time\">{{item.time}}</view>\n            <view class=\"schedule-info\">\n              <text class=\"schedule-title\">{{item.title}}</text>\n              <text class=\"schedule-desc\">{{item.description}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 教学特色 -->\n      <view class=\"content-card features-card\">\n        <view class=\"section-title\">教学特色</view>\n        <view class=\"features-list\">\n          <view class=\"feature-item\" v-for=\"(item, index) in courseData.features\" :key=\"index\">\n            <text class=\"feature-icon iconfont\" :class=\"item.icon\"></text>\n            <view class=\"feature-info\">\n              <text class=\"feature-title\">{{item.title}}</text>\n              <text class=\"feature-desc\">{{item.description}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 教师信息 -->\n      <view class=\"content-card teacher-card\">\n        <view class=\"teacher-header\">\n          <view class=\"teacher-avatar\">\n            <image :src=\"courseData.teacher.avatar\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"teacher-info\">\n            <text class=\"teacher-name\">{{courseData.teacher.name}}</text>\n            <view class=\"teacher-meta\">\n              <text class=\"teacher-title\">{{courseData.teacher.title}}</text>\n              <text class=\"teacher-rating\">好评率 {{courseData.teacher.rating}}</text>\n            </view>\n          </view>\n          <view class=\"teacher-auth\" v-if=\"courseData.teacher.isVerified\">\n            <text class=\"iconfont icon-verified\"></text>\n            <text class=\"auth-text\">已认证</text>\n          </view>\n        </view>\n        <view class=\"teacher-intro\">\n          <text class=\"intro-text\">{{courseData.teacher.introduction}}</text>\n        </view>\n      </view>\n      \n      <!-- 相似课程推荐 -->\n      <view class=\"content-card similar-courses-card\">\n        <view class=\"section-title\">相关课程推荐</view>\n        <view class=\"related-courses-content\">\n          <!-- 简洁的课程列表 -->\n          <view class=\"related-courses-list\">\n            <view class=\"related-course-item\" \n                 v-for=\"(course, index) in relatedCourses.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToCourseDetail(course.id)\">\n              <view class=\"course-item-content\">\n                <view class=\"course-item-left\">\n                  <image class=\"course-image\" :src=\"course.image\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"course-item-middle\">\n                  <text class=\"course-item-title\">{{course.title}}</text>\n                  <view class=\"course-item-target\">{{course.type}} · {{course.target}}</view>\n                  <view class=\"course-item-tags\">\n                    <text class=\"course-item-tag\" v-for=\"(tag, tagIndex) in course.tags.slice(0, 2)\" :key=\"tagIndex\">{{tag}}</text>\n                    <text class=\"course-item-tag-more\" v-if=\"course.tags && course.tags.length > 2\">+{{course.tags.length - 2}}</text>\n                  </view>\n                </view>\n                <view class=\"course-item-right\">\n                  <text class=\"course-item-price\">{{course.price}}</text>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 暂无数据提示 -->\n            <view class=\"empty-related-courses\" v-if=\"relatedCourses.length === 0\">\n              <image src=\"/static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n              <text class=\"empty-text\">暂无相关课程</text>\n            </view>\n          </view>\n          \n          <!-- 查看更多按钮 -->\n          <view class=\"view-more-btn\" v-if=\"relatedCourses.length > 0\" @click.stop=\"navigateToEducationList\">\n            <text class=\"view-more-text\">查看更多相关课程</text>\n            <text class=\"view-more-icon iconfont icon-right\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"interaction-toolbar\">\n      <view class=\"toolbar-item\" @click=\"goToHome\">\n        <image src=\"/static/images/tabbar/a首页.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">首页</text>\n      </view>\n      <view class=\"toolbar-item\" @click=\"toggleCollect\">\n        <image src=\"/static/images/tabbar/a收藏.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">收藏</text>\n      </view>\n      <button class=\"share-button toolbar-item\" open-type=\"share\">\n        <image src=\"/static/images/tabbar/a分享.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">分享</text>\n      </button>\n      <view class=\"toolbar-item\" @click=\"openChat\">\n        <image src=\"/static/images/tabbar/a消息.png\" class=\"toolbar-icon\"></image>\n        <text class=\"toolbar-text\">私信</text>\n      </view>\n      <view class=\"toolbar-item call-button\" @click=\"callPhone\">\n        <view class=\"call-button-content\">\n          <text class=\"call-text\">打电话</text>\n          <text class=\"call-subtitle\">请说在磁州生活网看到的</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 获取状态栏高度\nconst getStatusBarHeight = () => {\n  uni.getSystemInfo({\n    success: (res) => {\n      statusBarHeight.value = res.statusBarHeight || 20;\n    }\n  });\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 格式化时间\nconst formatTime = (timestamp) => {\n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日`;\n};\n\n// 响应式数据\nconst isCollected = ref(false);\nconst courseData = ref({\n  id: 'course12345',\n  title: '少儿英语启蒙课程',\n  price: '2999元/期',\n  tags: ['小班教学', '外教授课', '免费试听', '趣味教学'],\n  publishTime: Date.now() - 86400000 * 2, // 2天前\n  images: [\n    '/static/images/course1.jpg',\n    '/static/images/course2.jpg',\n    '/static/images/course3.jpg',\n    '/static/images/course4.jpg'\n  ],\n  type: '少儿英语',\n  target: '4-12岁儿童',\n  startTime: '2024-04-01',\n  duration: '3个月',\n  contents: [\n    { label: '课程目标', value: '培养英语兴趣，建立语言基础，提升口语表达能力' },\n    { label: '课程特色', value: '互动教学，寓教于乐，情景对话，角色扮演' },\n    { label: '教学方式', value: '小班教学（每班8人），外教授课，中教辅助' },\n    { label: '课程安排', value: '每周2次课，每次90分钟，课后作业辅导' },\n    { label: '教材使用', value: '牛津少儿英语教材，配套练习册和多媒体资源' },\n    { label: '学习效果', value: '掌握300个基础词汇，能进行简单日常对话' }\n  ],\n  schedule: [\n    {\n      time: '周一 16:00-17:30',\n      title: '英语启蒙',\n      description: '基础词汇、简单对话、字母发音'\n    },\n    {\n      time: '周三 16:00-17:30',\n      title: '趣味英语',\n      description: '英语游戏、歌曲学习、情景对话'\n    },\n    {\n      time: '周六 10:00-11:30',\n      title: '英语实践',\n      description: '角色扮演、故事阅读、口语练习'\n    },\n    {\n      time: '周日 10:00-11:30',\n      title: '英语活动',\n      description: '英语角、文化体验、成果展示'\n    }\n  ],\n  features: [\n    {\n      icon: 'icon-teacher',\n      title: '专业外教',\n      description: '来自英语母语国家的外教，均持有TESOL/TEFL证书'\n    },\n    {\n      icon: 'icon-class',\n      title: '小班教学',\n      description: '每班不超过8人，确保每个孩子都能得到充分关注'\n    },\n    {\n      icon: 'icon-material',\n      title: '优质教材',\n      description: '使用国际知名教材，配套丰富的多媒体教学资源'\n    },\n    {\n      icon: 'icon-method',\n      title: '趣味教学',\n      description: '通过游戏、歌曲、故事等多种方式激发学习兴趣'\n    },\n    {\n      icon: 'icon-progress',\n      title: '进度跟踪',\n      description: '定期评估学习效果，及时调整教学计划'\n    }\n  ],\n  teacher: {\n    name: 'Sarah Johnson',\n    avatar: '/static/images/avatar.png',\n    title: '资深外教',\n    rating: '98%',\n    isVerified: true,\n    introduction: '来自英国伦敦，拥有剑桥大学教育学硕士学位，TESOL证书持有者。5年少儿英语教学经验，擅长互动教学和趣味教学法。曾在新加坡国际学校任教，深受学生喜爱。教学风格活泼有趣，善于激发孩子的学习兴趣。'\n  },\n  contact: {\n    name: '王老师',\n    phone: '13912345678'\n  }\n});\n\nconst similarCourses = ref([\n  {\n    id: 'course001',\n    title: '少儿英语进阶课程',\n    price: '3999元/期',\n    type: '少儿英语',\n    target: '6-12岁儿童',\n    image: '/static/images/course-similar1.jpg'\n  },\n  {\n    id: 'course002',\n    title: '少儿英语口语课程',\n    price: '2599元/期',\n    type: '少儿英语',\n    target: '4-12岁儿童',\n    image: '/static/images/course-similar2.jpg'\n  },\n  {\n    id: 'course003',\n    title: '少儿英语阅读写作',\n    price: '3299元/期',\n    type: '少儿英语',\n    target: '8-12岁儿童',\n    image: '/static/images/course-similar3.jpg'\n  },\n  {\n    id: 'course004',\n    title: '少儿英语自然拼读',\n    price: '2899元/期',\n    type: '少儿英语',\n    target: '5-10岁儿童',\n    image: '/static/images/course-similar4.jpg'\n  }\n]);\n\n// 相关课程推荐数据\nconst relatedCourses = ref([]);\n\n// 加载相关课程推荐\nconst loadRelatedCourses = () => {\n  // 这里应该调用API获取数据\n  // 实际项目中应该根据当前课程的类型、标签等进行相关性匹配\n  \n  // 使用现有数据作为模拟数据\n  setTimeout(() => {\n    relatedCourses.value = similarCourses.value.map(course => ({\n      ...course,\n      tags: ['专业师资', '小班教学', '趣味教学']\n    }));\n  }, 500);\n};\n\n// 跳转到课程详情页\nconst navigateToCourseDetail = (courseId) => {\n  // 防止跳转到当前页面\n  if (courseId === courseData.value.id) {\n    return;\n  }\n  \n  uni.navigateTo({\n    url: `/pages/publish/education-detail?id=${courseId}`\n  });\n};\n\n// 跳转到教育培训列表页\nconst navigateToEducationList = (e) => {\n  if (e) e.stopPropagation();\n  const educationCategory = courseData.value.tags?.[0] || '';\n  uni.navigateTo({ \n    url: `/subPackages/service/pages/filter?type=education&title=${encodeURIComponent('教育培训')}&category=${encodeURIComponent(educationCategory)}&active=education` \n  });\n};\n\n// 方法\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  if (isCollected.value) {\n    uni.showToast({\n      title: '收藏成功',\n      icon: 'success'\n    });\n  }\n};\n\nconst showShareOptions = () => {\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline']\n  });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({\n    phoneNumber: courseData.value.contact.phone,\n    fail: () => {\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\nconst navigateToCourse = (id) => {\n  uni.navigateTo({\n    url: `/pages/publish/education-detail?id=${id}`\n  });\n};\n\n// 跳转到首页\nconst goToHome = () => {\n  uni.switchTab({\n    url: '/pages/index/index'\n  });\n};\n\n// 打开私信聊天\nconst openChat = () => {\n  if (!courseData.value.contact || !courseData.value.contact.id) {\n    uni.showToast({\n      title: '无法获取教育机构信息',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 跳转到聊天页面\n  uni.navigateTo({\n    url: `/pages/chat/index?userId=${courseData.value.contact.id}&username=${encodeURIComponent(courseData.value.contact.name || '教育机构')}`\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  getStatusBarHeight();\n  \n  // 修改页面标题\n  uni.setNavigationBarTitle({\n    title: '课程详情'\n  });\n  \n  // 设置导航栏颜色\n  uni.setNavigationBarColor({\n    frontColor: '#000000',\n    backgroundColor: '#ffffff'\n  });\n  \n  // 获取路由参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 获取课程ID\n  const id = options.id || '';\n  console.log('课程详情页ID:', id);\n  \n  // 加载相关课程推荐\n  loadRelatedCourses();\n});\n</script>\n\n<style>\n/* 自定义导航栏样式 */\n.custom-navbar {\n  background: linear-gradient(135deg, #0066FF, #0052CC);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  position: fixed; /* 改为固定定位 */\n  top: 0;\n  left: 0;\n  right: 0;\n  padding-bottom: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n  z-index: 100; /* 提高z-index确保在最上层 */\n}\n\n.navbar-left {\n  width: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 500;\n  color: #FFFFFF; /* 修改为白色文字 */\n}\n\n.navbar-right {\n  width: 60px;\n}\n\n.education-detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 110rpx;\n}\n\n.education-detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n.content-card {\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 课程基本信息卡片 */\n.course-title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.course-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.course-price {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #ff4d4f;\n}\n\n.course-meta {\n  margin-bottom: 24rpx;\n}\n\n.course-tag-group {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 12rpx;\n}\n\n.course-tag {\n  font-size: 24rpx;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 4rpx 16rpx;\n  border-radius: 6rpx;\n  margin-right: 16rpx;\n  margin-bottom: 12rpx;\n}\n\n.course-publish-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 轮播图 */\n.course-swiper {\n  height: 400rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n  margin-bottom: 24rpx;\n}\n\n.course-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 基本信息 */\n.course-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.info-item {\n  width: 50%;\n  padding: 12rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.info-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.info-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 课程内容 */\n.content-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.content-item {\n  display: flex;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.content-item:last-child {\n  border-bottom: none;\n}\n\n.content-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #999;\n}\n\n.content-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 课程安排 */\n.schedule-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.schedule-item {\n  display: flex;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.schedule-item:last-child {\n  border-bottom: none;\n}\n\n.schedule-time {\n  width: 200rpx;\n  font-size: 28rpx;\n  color: #1890ff;\n  font-weight: 500;\n}\n\n.schedule-info {\n  flex: 1;\n}\n\n.schedule-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.schedule-desc {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 教学特色 */\n.features-list {\n  display: flex;\n  flex-direction: column;\n}\n\n.feature-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.feature-item:last-child {\n  border-bottom: none;\n}\n\n.feature-icon {\n  font-size: 40rpx;\n  color: #1890ff;\n  margin-right: 20rpx;\n}\n\n.feature-info {\n  flex: 1;\n}\n\n.feature-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.feature-desc {\n  font-size: 26rpx;\n  color: #666;\n}\n\n/* 教师信息 */\n.teacher-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24rpx;\n}\n\n.teacher-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.teacher-avatar image {\n  width: 100%;\n  height: 100%;\n}\n\n.teacher-info {\n  flex: 1;\n}\n\n.teacher-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.teacher-meta {\n  display: flex;\n  align-items: center;\n}\n\n.teacher-title, .teacher-rating {\n  font-size: 24rpx;\n  color: #666;\n  margin-right: 16rpx;\n}\n\n.teacher-intro {\n  padding: 24rpx;\n  background-color: #f9fafc;\n  border-radius: 12rpx;\n}\n\n.intro-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 相似课程推荐样式优化 */\n.similar-courses-card {\n  margin-top: 12px;\n  background-color: #fff;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n}\n\n.related-courses-content {\n  padding: 0 16px 16px;\n  overflow: hidden;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 16px;\n  position: relative;\n  padding-left: 10px;\n}\n\n.section-title::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 4px;\n  width: 3px;\n  height: 16px;\n  background-color: #0052CC;\n  border-radius: 3px;\n}\n\n/* 相关课程列表样式 */\n.related-courses-list {\n  margin-bottom: 12px;\n}\n\n.related-course-item {\n  padding: 12px 0;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.related-course-item:last-child {\n  border-bottom: none;\n}\n\n.course-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.course-item-left {\n  margin-right: 12px;\n}\n\n.course-image {\n  width: 80px;\n  height: 60px;\n  border-radius: 8px;\n  background-color: #f5f7fa;\n  object-fit: cover;\n}\n\n.course-item-middle {\n  flex: 1;\n}\n\n.course-item-title {\n  font-size: 15px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.course-item-target {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.course-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.course-item-tag {\n  font-size: 12px;\n  color: #1890ff;\n  background-color: rgba(24, 144, 255, 0.1);\n  padding: 2px 8px;\n  border-radius: 4px;\n  margin-right: 8px;\n  margin-bottom: 4px;\n}\n\n.course-item-tag-more {\n  font-size: 12px;\n  color: #999;\n}\n\n.course-item-right {\n  text-align: right;\n  margin-left: 10px;\n}\n\n.course-item-price {\n  font-size: 16px;\n  font-weight: 500;\n  color: #ff4d4f;\n}\n\n/* 查看更多按钮 */\n.view-more-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 12px 0;\n  border-top: 1px solid #f5f5f5;\n}\n\n.view-more-text {\n  font-size: 14px;\n  color: #1890ff;\n  margin-right: 4px;\n}\n\n.view-more-icon {\n  font-size: 12px;\n  color: #1890ff;\n}\n\n/* 空状态 */\n.empty-related-courses {\n  padding: 30px 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.empty-image {\n  width: 100px;\n  height: 100px;\n  margin-bottom: 10px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 底部操作栏 */\n.interaction-toolbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 100;\n}\n\n.toolbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.toolbar-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 6rpx;\n}\n\n.toolbar-text {\n  font-size: 20rpx;\n  color: #666;\n}\n\n.call-button {\n  flex: 2;\n  background-color: #1890ff;\n  color: #fff;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.call-button-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.call-text {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #fff;\n}\n\n.call-subtitle {\n  font-size: 20rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 4rpx;\n}\n\n.share-button {\n  background-color: transparent;\n  padding: 0;\n  margin: 0;\n  line-height: normal;\n  border: none;\n}\n\n.share-button::after {\n  border: none;\n}\n\n.hidden-share-btn {\n  position: absolute;\n  top: -999px;\n  left: -999px;\n  width: 1px;\n  height: 1px;\n  opacity: 0;\n  pointer-events: none;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/education-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AA2NA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,qBAAqB,MAAM;AAC/BC,oBAAAA,MAAI,cAAc;AAAA,QAChB,SAAS,CAAC,QAAQ;AAChB,0BAAgB,QAAQ,IAAI,mBAAmB;AAAA,QAChD;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,aAAa,CAAC,cAAc;AAChC,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IACjD;AAGA,UAAM,cAAcD,cAAAA,IAAI,KAAK;AAC7B,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MACrC,aAAa,KAAK,IAAK,IAAG,QAAW;AAAA;AAAA,MACrC,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,QACR,EAAE,OAAO,QAAQ,OAAO,yBAA0B;AAAA,QAClD,EAAE,OAAO,QAAQ,OAAO,sBAAuB;AAAA,QAC/C,EAAE,OAAO,QAAQ,OAAO,uBAAwB;AAAA,QAChD,EAAE,OAAO,QAAQ,OAAO,sBAAuB;AAAA,QAC/C,EAAE,OAAO,QAAQ,OAAO,uBAAwB;AAAA,QAChD,EAAE,OAAO,QAAQ,OAAO,uBAAwB;AAAA,MACjD;AAAA,MACD,UAAU;AAAA,QACR;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,MACF;AAAA,MACD,UAAU;AAAA,QACR;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,QACd;AAAA,MACF;AAAA,MACD,SAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MACf;AAAA,MACD,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACzB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiBA,cAAAA,IAAI,CAAA,CAAE;AAG7B,UAAM,qBAAqB,MAAM;AAK/B,iBAAW,MAAM;AACf,uBAAe,QAAQ,eAAe,MAAM,IAAI,aAAW;AAAA,UACzD,GAAG;AAAA,UACH,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,QAC9B,EAAC;AAAA,MACH,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,yBAAyB,CAAC,aAAa;AAE3C,UAAI,aAAa,WAAW,MAAM,IAAI;AACpC;AAAA,MACD;AAEDC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,sCAAsC,QAAQ;AAAA,MACvD,CAAG;AAAA,IACH;AAGA,UAAM,0BAA0B,CAAC,MAAM;;AACrC,UAAI;AAAG,UAAE;AACT,YAAM,sBAAoB,gBAAW,MAAM,SAAjB,mBAAwB,OAAM;AACxDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0DAA0D,mBAAmB,MAAM,CAAC,aAAa,mBAAmB,iBAAiB,CAAC;AAAA,MAC/I,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjC,UAAI,YAAY,OAAO;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AASA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,WAAW,MAAM,QAAQ;AAAA,QACtC,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AASA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,WAAW,MAAM,WAAW,CAAC,WAAW,MAAM,QAAQ,IAAI;AAC7DA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,WAAW,MAAM,QAAQ,EAAE,aAAa,mBAAmB,WAAW,MAAM,QAAQ,QAAQ,MAAM,CAAC;AAAA,MACxI,CAAG;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AAEd;AAGAD,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,OAAO;AAAA,MACX,CAAG;AAGDA,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,YAAY;AAAA,QACZ,iBAAiB;AAAA,MACrB,CAAG;AAGD,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,YAAM,KAAK,QAAQ,MAAM;AACzBA,oBAAY,MAAA,MAAA,OAAA,6CAAA,YAAY,EAAE;AAG1B;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxeD,GAAG,WAAWE,SAAe;"}