/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.distribution-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.view-all {
  font-size: 14px;
  color: #6B0FBE;
}
.edit-btn {
  background: rgba(107, 15, 190, 0.1);
  color: #6B0FBE;
  font-size: 14px;
  padding: 5px 12px;
  border-radius: 15px;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}
.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}
.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #6B0FBE;
}
.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}
.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}
.card-trend.up {
  color: #34C759;
}
.card-trend.down {
  color: #FF3B30;
}
.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}
.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}
.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 排行榜样式 */
.leaderboard-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.leaderboard-list {
  margin-top: 10px;
}
.leaderboard-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}
.leaderboard-item:last-child {
  border-bottom: none;
}
.rank-badge {
  width: 24px;
  height: 24px;
  background: #F5F7FA;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: #999;
  margin-right: 10px;
}
.rank-badge.top-rank {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
}
.distributor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
  background-color: #f5f5f5;
}
.distributor-info {
  flex: 1;
}
.distributor-name {
  font-size: 15px;
  color: #333;
  margin-bottom: 3px;
}
.distributor-level {
  font-size: 12px;
  color: #6B0FBE;
  background: rgba(107, 15, 190, 0.1);
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
}
.distributor-stats {
  display: flex;
  margin-left: 10px;
}
.stat-item {
  margin-left: 15px;
  text-align: right;
}
.stat-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}
.stat-label {
  font-size: 12px;
  color: #999;
}

/* 设置部分样式 */
.settings-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.switch-container {
  display: flex;
  align-items: center;
}
.switch-label {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}
.settings-list {
  margin-top: 10px;
}
.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}
.settings-item:last-child {
  border-bottom: none;
}
.item-left {
  display: flex;
  align-items: center;
}
.item-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.item-icon.conditions {
  background: rgba(255, 149, 0, 0.1);
}
.item-icon.conditions::before {
  content: "";
  width: 16px;
  height: 16px;
  border: 2px solid #FF9500;
  border-radius: 10px;
  position: absolute;
}
.item-icon.levels {
  background: rgba(0, 122, 255, 0.1);
}
.item-icon.levels::before {
  content: "";
  width: 18px;
  height: 4px;
  background: #007AFF;
  position: absolute;
  top: 12px;
}
.item-icon.levels::after {
  content: "";
  width: 18px;
  height: 4px;
  background: #007AFF;
  position: absolute;
  top: 20px;
}
.item-icon.withdrawal {
  background: rgba(52, 199, 89, 0.1);
}
.item-icon.withdrawal::before {
  content: "";
  width: 16px;
  height: 10px;
  border: 2px solid #34C759;
  border-radius: 2px;
  position: absolute;
}
.item-icon.agreement {
  background: rgba(88, 86, 214, 0.1);
}
.item-icon.agreement::before {
  content: "";
  width: 14px;
  height: 18px;
  border: 2px solid #5856D6;
  border-radius: 2px;
  position: absolute;
}
.item-icon.agreement::after {
  content: "";
  width: 10px;
  height: 2px;
  background: #5856D6;
  position: absolute;
  top: 14px;
}
.item-title {
  font-size: 15px;
  color: #333;
}
.item-right {
  display: flex;
  align-items: center;
}
.item-value {
  font-size: 14px;
  color: #999;
  margin-right: 10px;
}
.item-arrow {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #999;
  border-right: 1.5px solid #999;
  transform: rotate(45deg);
}

/* 佣金规则样式 */
.commission-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.commission-rules {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.rule-card {
  width: 33.3333333333%;
  padding: 7.5px;
  box-sizing: border-box;
}
.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}
.rule-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
.rule-value {
  font-size: 16px;
  font-weight: 600;
  color: #6B0FBE;
}
.rule-desc {
  font-size: 12px;
  color: #999;
  height: 32px;
}

/* 推广工具样式 */
.promotion-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.promotion-tools {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.tool-card {
  width: 25%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.tool-icon {
  width: 50px;
  height: 50px;
  margin-bottom: 8px;
}
.tool-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 3px;
  text-align: center;
}
.tool-desc {
  font-size: 10px;
  color: #999;
  text-align: center;
  height: 28px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  box-shadow: 0 4px 15px rgba(107, 15, 190, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}
.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
.rule-card {
    width: 50%;
}
.tool-card {
    width: 33.33%;
}
}
@media screen and (max-width: 320px) {
.stats-card {
    width: 100%;
}
.tool-card {
    width: 50%;
}
}