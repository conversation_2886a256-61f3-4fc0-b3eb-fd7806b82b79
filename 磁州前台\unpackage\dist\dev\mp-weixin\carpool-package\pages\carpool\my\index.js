"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const userInfo = common_vendor.ref({
      id: "10086",
      nickname: "磁州用户",
      avatar: "/static/images/avatar/user1.png",
      creditScore: 98,
      isVerified: true,
      isVIP: false,
      isDriverVerified: true
    });
    const stats = common_vendor.ref({
      publishCount: 12,
      completedCount: 8,
      favorCount: 5,
      tripDistance: 320,
      newRatingCount: 2,
      contactHistoryCount: 3,
      unreadMessageCount: 5
    });
    const activeTab = common_vendor.ref("my");
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.onMounted(() => {
      common_vendor.index.hideTabBar();
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight;
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
    });
    common_vendor.onLoad(() => {
      getUserInfo();
      getStatistics();
      common_vendor.index.hideTabBar();
    });
    common_vendor.onHide(() => {
    });
    common_vendor.onUnload(() => {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (!prevPage || prevPage.route !== "carpool-package/pages/carpool-main/index") {
        common_vendor.index.showTabBar();
      }
    });
    common_vendor.onBackPress((event) => {
      if (event.from === "backbutton") {
        common_vendor.index.navigateTo({
          url: "/carpool-package/pages/carpool-main/index"
        });
        return true;
      }
      return false;
    });
    const getUserInfo = () => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/index.vue:293", "获取用户信息");
    };
    const getStatistics = () => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/index.vue:300", "获取统计数据");
    };
    const goSettings = () => {
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool/my/settings"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const goVerification = () => {
      if (userInfo.value.isVerified) {
        common_vendor.index.showToast({
          title: "您已通过认证",
          icon: "success"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool/my/verification"
      });
    };
    const goVIP = () => {
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool/my/vip"
      });
    };
    const navigateToList = (type) => {
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/my/${type === "published" ? "published-list" : type === "pending" ? "pending-list" : type === "expired" ? "expired-list" : "favorites"}`
      });
    };
    const navigateToPage = (page) => {
      switch (page) {
        case "home":
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
          return;
        case "carpool-main":
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool-main/index"
          });
          return;
        case "groups":
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool/groups/index"
          });
          return;
        case "message":
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool/my/message-center"
          });
          return;
        case "wallet":
          common_vendor.index.navigateTo({
            url: "/subPackages/payment/pages/wallet"
          });
          return;
        case "driver-verification":
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool/my/driver-verification"
          });
          return;
        case "feedback":
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool/my/feedback"
          });
          return;
        case "trip-records":
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool/my/trip-records"
          });
          return;
        case "driver-ratings":
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool/my/driver-ratings"
          });
          return;
        case "driver-profile":
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool/my/driver-profile"
          });
          return;
        case "contact-history":
          common_vendor.index.navigateTo({
            url: "/carpool-package/pages/carpool/my/contact-history"
          });
          return;
      }
    };
    const publishNew = () => {
      activeTab.value = "publish";
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool-main/index"
      });
      common_vendor.index.setStorageSync("showPublishPopup", true);
    };
    const goToDriverRatings = () => {
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool/my/driver-ratings"
      });
    };
    const goToTripRecords = () => {
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool/my/trip-records"
      });
    };
    const goToDriverProfile = () => {
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool/my/driver-profile"
      });
    };
    const goToSuccessPage = () => {
      const publishId = Date.now().toString();
      common_vendor.index.navigateTo({
        url: `/carpool-package/pages/carpool/publish/success?id=${publishId}&type=car-to-people&mode=ad`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_assets._imports_1$32,
        d: common_vendor.o(goSettings),
        e: statusBarHeight.value + "px",
        f: userInfo.value.avatar,
        g: common_vendor.t(userInfo.value.nickname),
        h: common_vendor.t(userInfo.value.id),
        i: common_vendor.t(userInfo.value.creditScore),
        j: common_vendor.t(userInfo.value.isVerified ? "已认证" : "去认证"),
        k: common_vendor.o(goVerification),
        l: common_vendor.t(userInfo.value.isVIP ? "VIP会员" : "开通VIP"),
        m: common_vendor.o(goVIP),
        n: common_vendor.o(goToSuccessPage),
        o: common_vendor.t(stats.value.publishCount),
        p: common_vendor.t(stats.value.completedCount),
        q: common_vendor.t(stats.value.favorCount),
        r: common_vendor.t(stats.value.tripDistance),
        s: common_assets._imports_2$28,
        t: common_vendor.o(($event) => navigateToList("published")),
        v: common_assets._imports_3$26,
        w: common_vendor.o(goToTripRecords),
        x: common_assets._imports_4$18,
        y: stats.value.newRatingCount > 0
      }, stats.value.newRatingCount > 0 ? {
        z: common_vendor.t(stats.value.newRatingCount)
      } : {}, {
        A: common_vendor.o(goToDriverRatings),
        B: common_assets._imports_5$17,
        C: common_vendor.o(goToDriverProfile),
        D: common_assets._imports_6$15,
        E: common_assets._imports_0$27,
        F: common_vendor.o(($event) => navigateToList("favorites")),
        G: common_assets._imports_3$26,
        H: stats.value.contactHistoryCount > 0
      }, stats.value.contactHistoryCount > 0 ? {
        I: common_vendor.t(stats.value.contactHistoryCount)
      } : {}, {
        J: common_assets._imports_0$27,
        K: common_vendor.o(($event) => navigateToPage("contact-history")),
        L: common_assets._imports_8$5,
        M: stats.value.unreadMessageCount > 0
      }, stats.value.unreadMessageCount > 0 ? {
        N: common_vendor.t(stats.value.unreadMessageCount)
      } : {}, {
        O: common_assets._imports_0$27,
        P: common_vendor.o(($event) => navigateToPage("message")),
        Q: common_assets._imports_9$7,
        R: common_assets._imports_0$27,
        S: common_vendor.o(($event) => navigateToPage("wallet")),
        T: common_assets._imports_10$4,
        U: userInfo.value.isDriverVerified
      }, userInfo.value.isDriverVerified ? {} : {}, {
        V: common_assets._imports_0$27,
        W: common_vendor.o(($event) => navigateToPage("driver-verification")),
        X: common_assets._imports_11$9,
        Y: common_assets._imports_0$27,
        Z: common_vendor.o(($event) => navigateToPage("feedback")),
        aa: activeTab.value === "home" ? "/static/images/tabbar/p首页选中.png" : "/static/images/tabbar/p首页.png",
        ab: activeTab.value === "home" ? 1 : "",
        ac: activeTab.value === "home" ? 1 : "",
        ad: common_vendor.o(($event) => navigateToPage("home")),
        ae: activeTab.value === "carpool-main" ? "/static/images/tabbar/p拼车选中.png" : "/static/images/tabbar/p拼车.png",
        af: activeTab.value === "carpool-main" ? 1 : "",
        ag: activeTab.value === "carpool-main" ? 1 : "",
        ah: common_vendor.o(($event) => navigateToPage("carpool-main")),
        ai: activeTab.value === "publish" ? "/static/images/tabbar/p发布选中.png" : "/static/images/tabbar/p发布.png",
        aj: activeTab.value === "publish" ? 1 : "",
        ak: activeTab.value === "publish" ? 1 : "",
        al: common_vendor.o(publishNew),
        am: activeTab.value === "groups" ? "/static/images/tabbar/p拼车群选中.png" : "/static/images/tabbar/p拼车群.png",
        an: activeTab.value === "groups" ? 1 : "",
        ao: activeTab.value === "groups" ? 1 : "",
        ap: common_vendor.o(($event) => navigateToPage("groups")),
        aq: common_assets._imports_12$3
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/index.js.map
