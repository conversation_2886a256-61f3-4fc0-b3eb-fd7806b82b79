{"version": 3, "file": "apply.js", "sources": ["subPackages/distribution/pages/apply.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZGlzdHJpYnV0aW9uXHBhZ2VzXGFwcGx5LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"apply-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">申请成为分销员</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 申请条件卡片 -->\r\n    <view class=\"conditions-card\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">申请条件</text>\r\n      </view>\r\n      \r\n      <view class=\"conditions-list\">\r\n        <view class=\"condition-item\" :class=\"{ 'satisfied': conditionStatus.purchase }\">\r\n          <view class=\"condition-icon\" :class=\"{ 'satisfied': conditionStatus.purchase }\"></view>\r\n          <view class=\"condition-content\">\r\n            <text class=\"condition-title\">{{conditions.requirePurchase ? '购买任意商品' : '无需购买商品'}}</text>\r\n            <text class=\"condition-desc\" v-if=\"conditions.requirePurchase\">需购买满{{conditions.minimumPurchase}}元商品</text>\r\n          </view>\r\n          <view class=\"condition-status\">\r\n            <text v-if=\"conditionStatus.purchase\">已满足</text>\r\n            <button v-else class=\"action-btn\" @click=\"navigateToShop\">去购买</button>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"condition-item\" :class=\"{ 'satisfied': conditionStatus.realName }\">\r\n          <view class=\"condition-icon\" :class=\"{ 'satisfied': conditionStatus.realName }\"></view>\r\n          <view class=\"condition-content\">\r\n            <text class=\"condition-title\">{{conditions.needRealName ? '实名认证' : '无需实名认证'}}</text>\r\n            <text class=\"condition-desc\" v-if=\"conditions.needRealName\">需完成实名认证</text>\r\n          </view>\r\n          <view class=\"condition-status\">\r\n            <text v-if=\"conditionStatus.realName\">已认证</text>\r\n            <button v-else class=\"action-btn\" @click=\"navigateToRealName\">去认证</button>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"condition-item\" :class=\"{ 'satisfied': conditionStatus.mobile }\">\r\n          <view class=\"condition-icon\" :class=\"{ 'satisfied': conditionStatus.mobile }\"></view>\r\n          <view class=\"condition-content\">\r\n            <text class=\"condition-title\">{{conditions.needMobile ? '绑定手机号' : '无需绑定手机号'}}</text>\r\n            <text class=\"condition-desc\" v-if=\"conditions.needMobile\">需绑定手机号</text>\r\n          </view>\r\n          <view class=\"condition-status\">\r\n            <text v-if=\"conditionStatus.mobile\">已绑定</text>\r\n            <button v-else class=\"action-btn\" @click=\"navigateToBindMobile\">去绑定</button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 申请表单 -->\r\n    <view class=\"form-card\">\r\n      <view class=\"card-header\">\r\n        <text class=\"card-title\">申请信息</text>\r\n      </view>\r\n      \r\n      <view class=\"form-content\">\r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">姓名</text>\r\n          <input class=\"form-input\" type=\"text\" v-model=\"formData.name\" placeholder=\"请输入您的真实姓名\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">手机号</text>\r\n          <input class=\"form-input\" type=\"number\" v-model=\"formData.mobile\" placeholder=\"请输入您的手机号\" maxlength=\"11\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">微信号</text>\r\n          <input class=\"form-input\" type=\"text\" v-model=\"formData.wechat\" placeholder=\"请输入您的微信号\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">申请理由</text>\r\n          <textarea class=\"form-textarea\" v-model=\"formData.reason\" placeholder=\"请简要描述您申请成为分销员的理由\" maxlength=\"200\" />\r\n          <text class=\"textarea-counter\">{{formData.reason.length}}/200</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 协议同意 -->\r\n    <view class=\"agreement-section\">\r\n      <view class=\"agreement-checkbox\" @click=\"toggleAgreement\">\r\n        <view class=\"checkbox\" :class=\"{ 'checked': formData.agreeAgreement }\"></view>\r\n        <text class=\"agreement-text\">我已阅读并同意</text>\r\n      </view>\r\n      <text class=\"agreement-link\" @click=\"showAgreement\">《分销员协议》</text>\r\n    </view>\r\n    \r\n    <!-- 提交按钮 -->\r\n    <view class=\"submit-section\">\r\n      <button class=\"submit-btn\" :disabled=\"!canSubmit\" :class=\"{ 'disabled': !canSubmit }\" @click=\"submitApplication\">提交申请</button>\r\n    </view>\r\n    \r\n    <!-- 申请说明 -->\r\n    <view class=\"tips-section\">\r\n      <view class=\"tips-header\">\r\n        <text class=\"tips-title\">申请说明</text>\r\n      </view>\r\n      \r\n      <view class=\"tips-content\">\r\n        <text class=\"tips-text\">1. 成为分销员后，可以获得推广商品的佣金收益。</text>\r\n        <text class=\"tips-text\">2. 分销员申请需要满足平台设置的条件。</text>\r\n        <text class=\"tips-text\">3. 申请提交后，{{conditions.autoApprove ? '系统将自动审核' : '平台将在1-3个工作日内审核'}}。</text>\r\n        <text class=\"tips-text\">4. 审核通过后，您将收到通知并可立即开始分销推广。</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\nimport distributionService from '@/utils/distributionService';\r\n\r\n// 申请条件\r\nconst conditions = reactive({\r\n  requirePurchase: true,\r\n  minimumPurchase: 100,\r\n  requireApproval: true,\r\n  autoApprove: false,\r\n  needRealName: true,\r\n  needMobile: true,\r\n  description: ''\r\n});\r\n\r\n// 条件满足状态\r\nconst conditionStatus = reactive({\r\n  purchase: false,\r\n  realName: false,\r\n  mobile: true // 假设已绑定手机号\r\n});\r\n\r\n// 表单数据\r\nconst formData = reactive({\r\n  name: '',\r\n  mobile: '',\r\n  wechat: '',\r\n  reason: '',\r\n  agreeAgreement: false\r\n});\r\n\r\n// 是否可以提交\r\nconst canSubmit = computed(() => {\r\n  // 检查条件是否满足\r\n  const conditionsMet = (!conditions.requirePurchase || conditionStatus.purchase) &&\r\n                        (!conditions.needRealName || conditionStatus.realName) &&\r\n                        (!conditions.needMobile || conditionStatus.mobile);\r\n  \r\n  // 检查表单是否填写完整\r\n  const formValid = formData.name.trim() !== '' &&\r\n                   formData.mobile.trim() !== '' &&\r\n                   formData.mobile.length === 11 &&\r\n                   formData.wechat.trim() !== '' &&\r\n                   formData.reason.trim() !== '' &&\r\n                   formData.agreeAgreement;\r\n  \r\n  return conditionsMet && formValid;\r\n});\r\n\r\n// 页面加载\r\nonMounted(async () => {\r\n  // 获取分销条件\r\n  await getDistributionConditions();\r\n  \r\n  // 检查条件满足状态\r\n  await checkConditionStatus();\r\n});\r\n\r\n// 获取分销条件\r\nconst getDistributionConditions = async () => {\r\n  try {\r\n    const result = await distributionService.getDistributionConditions();\r\n    \r\n    if (result) {\r\n      Object.assign(conditions, result);\r\n    }\r\n  } catch (error) {\r\n    console.error('获取分销条件失败', error);\r\n    uni.showToast({\r\n      title: '获取分销条件失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 检查条件满足状态\r\nconst checkConditionStatus = async () => {\r\n  try {\r\n    // 这里应该调用API检查用户是否满足条件\r\n    // 暂时使用模拟数据\r\n    conditionStatus.purchase = true; // 假设已满足购买条件\r\n    conditionStatus.realName = false; // 假设未实名认证\r\n  } catch (error) {\r\n    console.error('检查条件状态失败', error);\r\n  }\r\n};\r\n\r\n// 切换协议同意状态\r\nconst toggleAgreement = () => {\r\n  formData.agreeAgreement = !formData.agreeAgreement;\r\n};\r\n\r\n// 显示分销协议\r\nconst showAgreement = async () => {\r\n  try {\r\n    const agreement = await distributionService.getDistributionAgreement();\r\n    \r\n    uni.showModal({\r\n      title: '分销员协议',\r\n      content: agreement.substring(0, 500) + '...',\r\n      confirmText: '查看全文',\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          // 跳转到协议详情页\r\n          uni.navigateTo({\r\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement'\r\n          });\r\n        }\r\n      }\r\n    });\r\n  } catch (error) {\r\n    console.error('获取分销协议失败', error);\r\n  }\r\n};\r\n\r\n// 提交申请\r\nconst submitApplication = async () => {\r\n  if (!canSubmit.value) {\r\n    return;\r\n  }\r\n  \r\n  try {\r\n    uni.showLoading({\r\n      title: '提交中...',\r\n      mask: true\r\n    });\r\n    \r\n    const result = await distributionService.applyDistributor({\r\n      name: formData.name,\r\n      mobile: formData.mobile,\r\n      wechat: formData.wechat,\r\n      reason: formData.reason\r\n    });\r\n    \r\n    uni.hideLoading();\r\n    \r\n    if (result.success) {\r\n      uni.showModal({\r\n        title: '申请提交成功',\r\n        content: conditions.autoApprove ? '您的分销员申请已自动通过，现在您可以开始分销推广了！' : '您的分销员申请已提交，请耐心等待审核。',\r\n        showCancel: false,\r\n        success: () => {\r\n          // 返回我要赚钱\r\n          uni.navigateBack();\r\n        }\r\n      });\r\n    } else {\r\n      uni.showModal({\r\n        title: '申请提交失败',\r\n        content: result.message || '请稍后再试',\r\n        showCancel: false\r\n      });\r\n    }\r\n  } catch (error) {\r\n    uni.hideLoading();\r\n    console.error('提交申请失败', error);\r\n    uni.showToast({\r\n      title: '提交申请失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 导航到商城\r\nconst navigateToShop = () => {\r\n  uni.switchTab({\r\n    url: '/pages/business/business'\r\n  });\r\n};\r\n\r\n// 导航到实名认证\r\nconst navigateToRealName = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/my/real-name'\r\n  });\r\n};\r\n\r\n// 导航到绑定手机号\r\nconst navigateToBindMobile = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/my/bind-mobile'\r\n  });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 显示帮助\r\nconst showHelp = () => {\r\n  uni.showModal({\r\n    title: '申请帮助',\r\n    content: '成为分销员需要满足平台设置的条件，并提交申请信息。审核通过后，您将成为平台分销员，可以通过分享商品获得佣金。',\r\n    showCancel: false\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.apply-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 88rpx 32rpx 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.navbar-right {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 24rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 卡片通用样式 */\r\n.conditions-card,\r\n.form-card,\r\n.tips-section {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-header {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.card-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n/* 条件列表样式 */\r\n.conditions-list {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.condition-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.condition-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.condition-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border-radius: 20rpx;\r\n  border: 2rpx solid #ddd;\r\n  margin-right: 20rpx;\r\n  position: relative;\r\n}\r\n\r\n.condition-icon.satisfied {\r\n  border-color: #6B0FBE;\r\n  background-color: #6B0FBE;\r\n}\r\n\r\n.condition-icon.satisfied::after {\r\n  content: '';\r\n  position: absolute;\r\n  width: 20rpx;\r\n  height: 10rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(-45deg);\r\n  top: 10rpx;\r\n  left: 8rpx;\r\n}\r\n\r\n.condition-content {\r\n  flex: 1;\r\n}\r\n\r\n.condition-title {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.condition-desc {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.condition-status {\r\n  font-size: 26rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.action-btn {\r\n  background: #6B0FBE;\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  padding: 6rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  line-height: 1.5;\r\n  margin: 0;\r\n}\r\n\r\n/* 表单样式 */\r\n.form-content {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 30rpx;\r\n  position: relative;\r\n}\r\n\r\n.form-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n  display: block;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  background: #F5F7FA;\r\n  border-radius: 10rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 200rpx;\r\n  background: #F5F7FA;\r\n  border-radius: 10rpx;\r\n  padding: 20rpx;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.textarea-counter {\r\n  position: absolute;\r\n  right: 20rpx;\r\n  bottom: 20rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 协议同意 */\r\n.agreement-section {\r\n  margin: 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.agreement-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.checkbox {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  border-radius: 6rpx;\r\n  border: 2rpx solid #ddd;\r\n  margin-right: 16rpx;\r\n  position: relative;\r\n}\r\n\r\n.checkbox.checked {\r\n  background: #6B0FBE;\r\n  border-color: #6B0FBE;\r\n}\r\n\r\n.checkbox.checked::after {\r\n  content: '';\r\n  position: absolute;\r\n  width: 20rpx;\r\n  height: 10rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(-45deg);\r\n  top: 8rpx;\r\n  left: 6rpx;\r\n}\r\n\r\n.agreement-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.agreement-link {\r\n  font-size: 26rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n/* 提交按钮 */\r\n.submit-section {\r\n  margin: 40rpx 30rpx;\r\n}\r\n\r\n.submit-btn {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 32rpx;\r\n  padding: 20rpx 0;\r\n  line-height: 1.5;\r\n  width: 100%;\r\n}\r\n\r\n.submit-btn.disabled {\r\n  background: #cccccc;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 申请说明 */\r\n.tips-header {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.tips-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.tips-content {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.tips-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  line-height: 1.8;\r\n  display: block;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/distribution/pages/apply.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "computed", "onMounted", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;AA2HA,UAAM,aAAaA,cAAAA,SAAS;AAAA,MAC1B,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,aAAa;AAAA,IACf,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,SAAS;AAAA,MAC/B,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA;AAAA,IACV,CAAC;AAGD,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,gBAAgB;AAAA,IAClB,CAAC;AAGD,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAE/B,YAAM,iBAAiB,CAAC,WAAW,mBAAmB,gBAAgB,cAC/C,CAAC,WAAW,gBAAgB,gBAAgB,cAC5C,CAAC,WAAW,cAAc,gBAAgB;AAGjE,YAAM,YAAY,SAAS,KAAK,KAAM,MAAK,MAC1B,SAAS,OAAO,KAAI,MAAO,MAC3B,SAAS,OAAO,WAAW,MAC3B,SAAS,OAAO,KAAI,MAAO,MAC3B,SAAS,OAAO,KAAI,MAAO,MAC3B,SAAS;AAE1B,aAAO,iBAAiB;AAAA,IAC1B,CAAC;AAGDC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,0BAAyB;AAG/B,YAAM,qBAAoB;AAAA,IAC5B,CAAC;AAGD,UAAM,4BAA4B,YAAY;AAC5C,UAAI;AACF,cAAM,SAAS,MAAMC,8CAAoB;AAEzC,YAAI,QAAQ;AACV,iBAAO,OAAO,YAAY,MAAM;AAAA,QACjC;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAA,MAAA,MAAA,SAAA,mDAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,uBAAuB,YAAY;AACvC,UAAI;AAGF,wBAAgB,WAAW;AAC3B,wBAAgB,WAAW;AAAA,MAC5B,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,mDAAc,YAAY,KAAK;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,eAAS,iBAAiB,CAAC,SAAS;AAAA,IACtC;AAGA,UAAM,gBAAgB,YAAY;AAChC,UAAI;AACF,cAAM,YAAY,MAAMD,8CAAoB;AAE5CC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS,UAAU,UAAU,GAAG,GAAG,IAAI;AAAA,UACvC,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AAEfA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK;AAAA,cACjB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,mDAAc,YAAY,KAAK;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,oBAAoB,YAAY;AACpC,UAAI,CAAC,UAAU,OAAO;AACpB;AAAA,MACD;AAED,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,SAAS,MAAMD,0BAAmB,oBAAC,iBAAiB;AAAA,UACxD,MAAM,SAAS;AAAA,UACf,QAAQ,SAAS;AAAA,UACjB,QAAQ,SAAS;AAAA,UACjB,QAAQ,SAAS;AAAA,QACvB,CAAK;AAEDC,sBAAG,MAAC,YAAW;AAEf,YAAI,OAAO,SAAS;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,WAAW,cAAc,+BAA+B;AAAA,YACjE,YAAY;AAAA,YACZ,SAAS,MAAM;AAEbA,4BAAG,MAAC,aAAY;AAAA,YACjB;AAAA,UACT,CAAO;AAAA,QACP,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,OAAO,WAAW;AAAA,YAC3B,YAAY;AAAA,UACpB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,mDAAc,UAAU,KAAK;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,uBAAuB,MAAM;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzTA,GAAG,WAAWC,SAAe;"}