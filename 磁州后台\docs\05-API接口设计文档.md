# 磁州生活网后台管理系统 - API接口设计文档

## 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025-01-04
- **文档类型**: API接口设计文档
- **目标读者**: 前端开发工程师、后端开发工程师、测试工程师

## 1. API设计规范

### 1.1 RESTful API设计原则
- **资源导向**: URL表示资源，HTTP方法表示操作
- **无状态**: 每个请求包含完整的信息
- **统一接口**: 统一的请求/响应格式
- **分层系统**: 支持缓存、负载均衡等中间层

### 1.2 URL设计规范
```
基础URL: https://api.cizhou.com/admin/v1
资源URL: /api/admin/v1/{module}/{resource}
示例: /api/admin/v1/users/123
```

### 1.3 HTTP方法使用规范
- **GET**: 查询资源
- **POST**: 创建资源
- **PUT**: 更新资源(全量)
- **PATCH**: 更新资源(部分)
- **DELETE**: 删除资源

### 1.4 状态码规范
```
200 OK - 请求成功
201 Created - 创建成功
204 No Content - 删除成功
400 Bad Request - 请求参数错误
401 Unauthorized - 未授权
403 Forbidden - 禁止访问
404 Not Found - 资源不存在
409 Conflict - 资源冲突
422 Unprocessable Entity - 参数验证失败
500 Internal Server Error - 服务器内部错误
```

### 1.5 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-01-04T10:30:00Z",
  "traceId": "abc123def456"
}
```

## 2. 认证授权API

### 2.1 管理员登录
```http
POST /api/admin/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password123",
  "captcha": "1234",
  "captchaKey": "uuid-key"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_string",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "avatar": "https://example.com/avatar.jpg",
      "roles": ["ADMIN"],
      "permissions": ["user:read", "user:write"]
    }
  }
}
```

### 2.2 刷新Token
```http
POST /api/admin/v1/auth/refresh
Content-Type: application/json
Authorization: Bearer {refresh_token}

{
  "refreshToken": "refresh_token_string"
}
```

### 2.3 退出登录
```http
POST /api/admin/v1/auth/logout
Authorization: Bearer {access_token}
```

### 2.4 获取当前用户信息
```http
GET /api/admin/v1/auth/profile
Authorization: Bearer {access_token}
```

## 3. 用户管理API

### 3.1 用户列表查询
```http
GET /api/admin/v1/users?page=1&size=20&keyword=张三&status=1&userType=1&startDate=2025-01-01&endDate=2025-01-31
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 1000,
    "page": 1,
    "size": 20,
    "pages": 50,
    "records": [
      {
        "id": 1,
        "username": "user001",
        "nickname": "张三",
        "phone": "138****1234",
        "email": "<EMAIL>",
        "avatar": "https://example.com/avatar.jpg",
        "status": 1,
        "userType": 1,
        "level": "VIP",
        "points": 1000,
        "balance": 100.50,
        "lastLoginTime": "2025-01-04T10:30:00Z",
        "createdAt": "2025-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 3.2 创建用户
```http
POST /api/admin/v1/users
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "username": "newuser",
  "password": "password123",
  "nickname": "新用户",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "userType": 1,
  "status": 1
}
```

### 3.3 更新用户信息
```http
PUT /api/admin/v1/users/{id}
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "nickname": "更新昵称",
  "phone": "13800138001",
  "email": "<EMAIL>",
  "status": 1
}
```

### 3.4 删除用户
```http
DELETE /api/admin/v1/users/{id}
Authorization: Bearer {access_token}
```

### 3.5 批量操作用户
```http
POST /api/admin/v1/users/batch
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "action": "enable", // enable, disable, delete
  "userIds": [1, 2, 3, 4, 5]
}
```

### 3.6 用户详情
```http
GET /api/admin/v1/users/{id}
Authorization: Bearer {access_token}
```

### 3.7 重置用户密码
```http
POST /api/admin/v1/users/{id}/reset-password
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "newPassword": "newpassword123"
}
```

## 4. 内容管理API

### 4.1 内容分类管理

#### 4.1.1 分类列表
```http
GET /api/admin/v1/content/categories?parentId=0&status=1
Authorization: Bearer {access_token}
```

#### 4.1.2 创建分类
```http
POST /api/admin/v1/content/categories
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "name": "招聘求职",
  "code": "job",
  "parentId": 0,
  "icon": "https://example.com/icon.png",
  "description": "招聘求职信息",
  "publishFee": 10.00,
  "topFee": 50.00,
  "sortOrder": 1,
  "status": 1
}
```

#### 4.1.3 更新分类
```http
PUT /api/admin/v1/content/categories/{id}
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "name": "更新分类名",
  "publishFee": 15.00,
  "sortOrder": 2
}
```

#### 4.1.4 删除分类
```http
DELETE /api/admin/v1/content/categories/{id}
Authorization: Bearer {access_token}
```

### 4.2 内容信息管理

#### 4.2.1 内容列表
```http
GET /api/admin/v1/content/posts?page=1&size=20&categoryId=1&status=1&keyword=招聘&city=北京&startDate=2025-01-01&endDate=2025-01-31
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 500,
    "page": 1,
    "size": 20,
    "records": [
      {
        "id": 1,
        "title": "招聘前端开发工程师",
        "summary": "负责前端页面开发...",
        "categoryName": "招聘求职",
        "userName": "张三",
        "contactPhone": "138****1234",
        "locationCity": "北京",
        "price": 8000.00,
        "isTop": 1,
        "viewCount": 100,
        "status": 1,
        "createdAt": "2025-01-04T10:00:00Z"
      }
    ]
  }
}
```

#### 4.2.2 内容详情
```http
GET /api/admin/v1/content/posts/{id}
Authorization: Bearer {access_token}
```

#### 4.2.3 审核内容
```http
POST /api/admin/v1/content/posts/{id}/audit
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "status": 1, // 1-通过, 3-拒绝
  "remark": "审核通过"
}
```

#### 4.2.4 批量审核
```http
POST /api/admin/v1/content/posts/batch-audit
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "postIds": [1, 2, 3],
  "status": 1,
  "remark": "批量审核通过"
}
```

#### 4.2.5 删除内容
```http
DELETE /api/admin/v1/content/posts/{id}
Authorization: Bearer {access_token}
```

#### 4.2.6 设置推荐
```http
POST /api/admin/v1/content/posts/{id}/recommend
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "isRecommend": true,
  "recommendDays": 7
}
```

## 5. 商家管理API

### 5.1 商家入驻管理

#### 5.1.1 入驻申请列表
```http
GET /api/admin/v1/merchants/applications?page=1&size=20&status=0&city=北京&startDate=2025-01-01
Authorization: Bearer {access_token}
```

#### 5.1.2 入驻申请详情
```http
GET /api/admin/v1/merchants/applications/{id}
Authorization: Bearer {access_token}
```

#### 5.1.3 审核入驻申请
```http
POST /api/admin/v1/merchants/applications/{id}/audit
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "status": 1, // 1-通过, 3-拒绝
  "remark": "审核通过，欢迎入驻"
}
```

### 5.2 商家信息管理

#### 5.2.1 商家列表
```http
GET /api/admin/v1/merchants?page=1&size=20&keyword=商家名&status=1&level=1&city=北京
Authorization: Bearer {access_token}
```

#### 5.2.2 商家详情
```http
GET /api/admin/v1/merchants/{id}
Authorization: Bearer {access_token}
```

#### 5.2.3 更新商家信息
```http
PUT /api/admin/v1/merchants/{id}
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "merchantName": "更新商家名",
  "level": 2,
  "status": 1
}
```

#### 5.2.4 商家状态管理
```http
POST /api/admin/v1/merchants/{id}/status
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "status": 2, // 1-正常, 2-暂停, 0-禁用
  "reason": "违规操作"
}
```

### 5.3 商家商品管理

#### 5.3.1 商品列表
```http
GET /api/admin/v1/merchants/{merchantId}/products?page=1&size=20&status=1&keyword=商品名
Authorization: Bearer {access_token}
```

#### 5.3.2 商品详情
```http
GET /api/admin/v1/products/{id}
Authorization: Bearer {access_token}
```

#### 5.3.3 商品状态管理
```http
POST /api/admin/v1/products/{id}/status
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "status": 1, // 0-下架, 1-上架
  "reason": "商品违规"
}
```

## 6. 拼车管理API

### 6.1 拼车信息管理

#### 6.1.1 拼车列表
```http
GET /api/admin/v1/carpool/posts?page=1&size=20&type=1&status=1&departureCity=北京&destinationCity=上海&startDate=2025-01-01
Authorization: Bearer {access_token}
```

#### 6.1.2 拼车详情
```http
GET /api/admin/v1/carpool/posts/{id}
Authorization: Bearer {access_token}
```

#### 6.1.3 审核拼车信息
```http
POST /api/admin/v1/carpool/posts/{id}/audit
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "status": 1, // 1-通过, 4-拒绝
  "remark": "审核通过"
}
```

### 6.2 拼车订单管理

#### 6.2.1 订单列表
```http
GET /api/admin/v1/carpool/orders?page=1&size=20&status=1&orderNo=CP202501040001&startDate=2025-01-01
Authorization: Bearer {access_token}
```

#### 6.2.2 订单详情
```http
GET /api/admin/v1/carpool/orders/{id}
Authorization: Bearer {access_token}
```

#### 6.2.3 订单状态管理
```http
POST /api/admin/v1/carpool/orders/{id}/status
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "status": 5, // 订单状态
  "reason": "处理原因"
}
```

### 6.3 路线管理

#### 6.3.1 热门路线统计
```http
GET /api/admin/v1/carpool/routes/hot?limit=20&days=30
Authorization: Bearer {access_token}
```

#### 6.3.2 创建推荐路线
```http
POST /api/admin/v1/carpool/routes
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "departureCity": "北京",
  "destinationCity": "上海",
  "recommendPrice": 200.00,
  "isHot": true,
  "sortOrder": 1
}
```

## 7. 返利系统API

### 7.1 返利平台管理

#### 7.1.1 平台列表
```http
GET /api/admin/v1/cashback/platforms?status=1
Authorization: Bearer {access_token}
```

#### 7.1.2 创建平台
```http
POST /api/admin/v1/cashback/platforms
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "name": "淘宝",
  "code": "taobao",
  "logo": "https://example.com/taobao-logo.png",
  "apiUrl": "https://api.taobao.com",
  "appKey": "app_key",
  "appSecret": "app_secret",
  "commissionRate": 0.0500,
  "settlementCycle": 30,
  "minWithdrawAmount": 10.00,
  "status": 1
}
```

#### 7.1.3 更新平台配置
```http
PUT /api/admin/v1/cashback/platforms/{id}
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "commissionRate": 0.0600,
  "settlementCycle": 15
}
```

#### 7.1.4 同步平台数据
```http
POST /api/admin/v1/cashback/platforms/{id}/sync
Authorization: Bearer {access_token}
```

### 7.2 返利订单管理

#### 7.2.1 订单列表
```http
GET /api/admin/v1/cashback/orders?page=1&size=20&platformId=1&orderStatus=1&cashbackStatus=1&startDate=2025-01-01
Authorization: Bearer {access_token}
```

#### 7.2.2 订单详情
```http
GET /api/admin/v1/cashback/orders/{id}
Authorization: Bearer {access_token}
```

#### 7.2.3 订单状态管理
```http
POST /api/admin/v1/cashback/orders/{id}/status
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "orderStatus": 3, // 订单状态
  "cashbackStatus": 1, // 返利状态
  "reason": "处理原因"
}
```

## 8. 数据统计API

### 8.1 概览统计
```http
GET /api/admin/v1/statistics/overview?date=2025-01-04
Authorization: Bearer {access_token}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userStats": {
      "totalUsers": 10000,
      "todayNewUsers": 50,
      "activeUsers": 2000
    },
    "contentStats": {
      "totalPosts": 5000,
      "todayPosts": 20,
      "pendingAudit": 10
    },
    "merchantStats": {
      "totalMerchants": 500,
      "todayNewMerchants": 3,
      "pendingAudit": 5
    },
    "revenueStats": {
      "todayRevenue": 1000.00,
      "monthRevenue": 30000.00,
      "totalRevenue": 500000.00
    }
  }
}
```

### 8.2 用户统计
```http
GET /api/admin/v1/statistics/users?startDate=2025-01-01&endDate=2025-01-31&groupBy=day
Authorization: Bearer {access_token}
```

### 8.3 业务统计
```http
GET /api/admin/v1/statistics/business?module=content&startDate=2025-01-01&endDate=2025-01-31
Authorization: Bearer {access_token}
```

### 8.4 收入统计
```http
GET /api/admin/v1/statistics/revenue?startDate=2025-01-01&endDate=2025-01-31&groupBy=day
Authorization: Bearer {access_token}
```

## 9. 系统配置API

### 9.1 系统配置管理

#### 9.1.1 获取配置列表
```http
GET /api/admin/v1/system/configs?group=basic
Authorization: Bearer {access_token}
```

#### 9.1.2 更新系统配置
```http
PUT /api/admin/v1/system/configs
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "configs": [
    {
      "key": "site.name",
      "value": "磁州生活网"
    },
    {
      "key": "site.logo",
      "value": "https://example.com/logo.png"
    }
  ]
}
```

### 9.2 文件上传API

#### 9.2.1 单文件上传
```http
POST /api/admin/v1/files/upload
Content-Type: multipart/form-data
Authorization: Bearer {access_token}

file: [binary data]
type: image // image, document, video
```

#### 9.2.2 批量文件上传
```http
POST /api/admin/v1/files/batch-upload
Content-Type: multipart/form-data
Authorization: Bearer {access_token}

files: [binary data array]
type: image
```

---

**文档状态**: API接口设计完成，待开发实施
**下一步**: 前端开发规范文档
