<template>
  <view class="test-page">
    <view class="page-header">
      <text class="page-title">ConfigurablePremiumActions 组件测试</text>
      <text class="page-desc">测试所有页面类型和显示模式</text>
    </view>
    
    <!-- 测试商家入驻 -->
    <view class="test-section">
      <view class="section-title">商家入驻测试</view>
      <ConfigurablePremiumActions
        pageType="merchant_join"
        showMode="selection"
        :itemData="merchantJoinData"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <!-- 测试商家置顶 -->
    <view class="test-section">
      <view class="section-title">商家置顶测试</view>
      <ConfigurablePremiumActions
        pageType="merchant_top"
        showMode="direct"
        :itemData="merchantTopData"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <!-- 测试拼车置顶 -->
    <view class="test-section">
      <view class="section-title">拼车置顶测试</view>
      <ConfigurablePremiumActions
        pageType="carpool_top"
        showMode="button"
        :itemData="carpoolTopData"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <!-- 测试发布推广 -->
    <view class="test-section">
      <view class="section-title">发布推广测试</view>
      <ConfigurablePremiumActions
        pageType="publish"
        showMode="standard"
        :itemData="publishData"
        @action-completed="handleActionCompleted"
        @action-cancelled="handleActionCancelled"
      />
    </view>
    
    <!-- 测试结果显示 -->
    <view class="test-results" v-if="testResults.length > 0">
      <view class="results-title">测试结果</view>
      <view class="result-item" v-for="(result, index) in testResults" :key="index">
        <view class="result-time">{{ result.time }}</view>
        <view class="result-type">{{ result.type }}</view>
        <view class="result-data">{{ JSON.stringify(result.data) }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// 测试数据
const merchantJoinData = reactive({
  id: 'merchant_join_test',
  title: '商家入驻测试',
  description: '测试商家入驻功能'
});

const merchantTopData = reactive({
  id: 'merchant_top_test',
  title: '商家置顶测试',
  description: '测试商家置顶功能'
});

const carpoolTopData = reactive({
  id: 'carpool_top_test',
  title: '拼车置顶测试',
  description: '测试拼车置顶功能'
});

const publishData = reactive({
  id: 'publish_test',
  title: '发布推广测试',
  description: '测试发布推广功能'
});

// 测试结果
const testResults = ref([]);

// 处理操作完成
const handleActionCompleted = (result) => {
  console.log('操作完成:', result);
  
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    type: '操作完成',
    data: result
  });
  
  uni.showToast({
    title: '操作完成',
    icon: 'success'
  });
};

// 处理操作取消
const handleActionCancelled = (result) => {
  console.log('操作取消:', result);
  
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    type: '操作取消',
    data: result
  });
  
  uni.showToast({
    title: '操作取消',
    icon: 'none'
  });
};
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.test-section {
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.test-results {
  margin-top: 40rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.result-item {
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #4f46e5;
}

.result-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.result-type {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.result-data {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
  background: white;
  padding: 10rpx;
  border-radius: 5rpx;
}
</style>
