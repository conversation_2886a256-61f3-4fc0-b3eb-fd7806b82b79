{"version": 3, "file": "newsDetail.js", "sources": ["mock/news/newsDetail.js"], "sourcesContent": ["// 新闻详情模拟数据\r\nexport const newsDetail = {\r\n  id: 1,\r\n  title: '磁县城区道路改造工程开始，请注意绕行',\r\n  time: '2024-03-15 10:30',\r\n  source: '磁州生活网',\r\n  category: '政务资讯',\r\n  image: '/static/images/banner/banner-1.png',\r\n  imageCaption: '磁县城区道路改造工程效果图',\r\n  isCollected: false,\r\n  content: '为提升城市道路品质，改善市民出行环境，磁县将对城区主要道路进行升级改造。\\n\\n改造范围：\\n1. 磁州大道（北段）：从人民路口至北环路段\\n2. 建设路：全段\\n3. 振兴路：全段\\n\\n施工时间：2024年3月20日至6月30日\\n\\n绕行建议：\\n1. 南北方向可选择：和平路、幸福路\\n2. 东西方向可选择：繁荣路、永安路\\n\\n请广大市民提前规划出行路线，遵守交通标识，确保安全。',\r\n  views: 1234,\r\n  likes: 88,\r\n  isLiked: false,\r\n  comments: 32,\r\n  commentList: [\r\n    {\r\n      avatar: '/static/images/tabbar/user-blue.png',\r\n      name: '磁州居民',\r\n      content: '终于要修路了，期待改造后的道路',\r\n      time: '10分钟前',\r\n      likes: 12,\r\n      isLiked: false,\r\n      isOfficial: false,\r\n      showAllReplies: false,\r\n      replies: [\r\n        {\r\n          name: '城管小李',\r\n          content: '是啊，早就该修了',\r\n          time: '8分钟前',\r\n          replyTo: '磁州居民',\r\n          isOfficial: true\r\n        },\r\n        {\r\n          name: '路人甲',\r\n          content: '希望能按时完工',\r\n          time: '5分钟前',\r\n          replyTo: '城管小李'\r\n        },\r\n        {\r\n          name: '市民小张',\r\n          content: '希望能够解决拥堵问题',\r\n          time: '3分钟前',\r\n          replyTo: '磁州居民'\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      avatar: '/static/images/tabbar/user-blue.png',\r\n      name: '老磁州',\r\n      content: '建议增加非机动车道的宽度，方便市民骑行',\r\n      time: '20分钟前',\r\n      likes: 8,\r\n      isLiked: false,\r\n      isOfficial: false,\r\n      showAllReplies: false,\r\n      replies: []\r\n    },\r\n    {\r\n      avatar: '/static/images/tabbar/城管小李.png',\r\n      name: '磁县交通局',\r\n      content: '本次改造将重点解决非机动车道和人行道问题，提升市民出行体验，感谢大家关注',\r\n      time: '1小时前',\r\n      likes: 45,\r\n      isLiked: false,\r\n      isOfficial: true,\r\n      showAllReplies: false,\r\n      replies: []\r\n    }\r\n  ]\r\n};\r\n\r\n// 模拟加载更多评论的数据\r\nexport const moreComments = [\r\n  {\r\n    avatar: '/static/images/tabbar/user-blue.png',\r\n    name: '关注市政',\r\n    content: '希望施工期间注意保持周边环境卫生，减少灰尘污染',\r\n    time: '2小时前',\r\n    likes: 5,\r\n    isLiked: false,\r\n    isOfficial: false,\r\n    showAllReplies: false,\r\n    replies: []\r\n  },\r\n  {\r\n    avatar: '/static/images/tabbar/user-blue.png',\r\n    name: '城市规划爱好者',\r\n    content: '建议同步规划自行车道，增加绿化带，打造更宜居的城市环境',\r\n    time: '3小时前',\r\n    likes: 7,\r\n    isLiked: false,\r\n    isOfficial: false,\r\n    showAllReplies: false,\r\n    replies: []\r\n  }\r\n];\r\n\r\n// 模拟获取新闻详情的API函数\r\nexport const fetchNewsDetail = (id) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(newsDetail);\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 模拟获取更多评论的API函数\r\nexport const fetchMoreComments = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(moreComments);\r\n    }, 800);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,aAAa;AAAA,EACxB,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO;AAAA,EACP,cAAc;AAAA,EACd,aAAa;AAAA,EACb,SAAS;AAAA,EACT,OAAO;AAAA,EACP,OAAO;AAAA,EACP,SAAS;AAAA,EACT,UAAU;AAAA,EACV,aAAa;AAAA,IACX;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,SAAS;AAAA,QACP;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,IACD;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,SAAS,CAAE;AAAA,IACZ;AAAA,IACD;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,SAAS,CAAE;AAAA,IACZ;AAAA,EACF;AACH;AAGO,MAAM,eAAe;AAAA,EAC1B;AAAA,IACE,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,SAAS,CAAE;AAAA,EACZ;AAAA,EACD;AAAA,IACE,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,SAAS,CAAE;AAAA,EACZ;AACH;AAGY,MAAC,kBAAkB,CAAC,OAAO;AACrC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,UAAU;AAAA,IACnB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,oBAAoB,MAAM;AACrC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,YAAY;AAAA,IACrB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;"}