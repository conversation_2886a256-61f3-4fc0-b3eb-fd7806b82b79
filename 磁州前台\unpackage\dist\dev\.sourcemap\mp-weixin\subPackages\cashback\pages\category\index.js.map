{"version": 3, "file": "index.js", "sources": ["subPackages/cashback/pages/category/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcY2FzaGJhY2tccGFnZXNcY2F0ZWdvcnlcaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"category-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <custom-navbar title=\"商品分类\" :show-back=\"true\"></custom-navbar>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content-container\">\r\n      <!-- 分类导航 -->\r\n      <scroll-view class=\"category-sidebar\" scroll-y>\r\n        <view \r\n          class=\"category-item\" \r\n          v-for=\"(category, index) in categories\" \r\n          :key=\"index\"\r\n          :class=\"{'category-item--active': activeIndex === index}\"\r\n          @tap=\"switchCategory(index)\"\r\n        >\r\n          <text>{{ category.name }}</text>\r\n        </view>\r\n      </scroll-view>\r\n      \r\n      <!-- 分类内容 -->\r\n      <scroll-view class=\"category-content\" scroll-y>\r\n        <view class=\"subcategory-grid\">\r\n          <view class=\"subcategory-section\" v-for=\"(subcategory, subIndex) in currentSubcategories\" :key=\"subIndex\">\r\n            <view class=\"subcategory-title\">\r\n              <text>{{ subcategory.name }}</text>\r\n            </view>\r\n            <view class=\"subcategory-items\">\r\n              <view \r\n                class=\"subcategory-item\" \r\n                v-for=\"(item, itemIndex) in subcategory.items\" \r\n                :key=\"itemIndex\"\r\n                @tap=\"navigateToProductList(item)\"\r\n              >\r\n                <image class=\"subcategory-icon\" :src=\"item.icon\" mode=\"aspectFill\"></image>\r\n                <text class=\"subcategory-name\">{{ item.name }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport CustomNavbar from '../../components/CustomNavbar.vue';\r\n\r\nexport default {\r\n  components: {\r\n    CustomNavbar\r\n  },\r\n  data() {\r\n    return {\r\n      activeIndex: 0,\r\n      categories: [\r\n        {\r\n          name: '女装',\r\n          subcategories: [\r\n            {\r\n              name: '当季热卖',\r\n              items: [\r\n                { id: 1, name: '连衣裙', icon: '/static/images/cashback/category-dress.png' },\r\n                { id: 2, name: 'T恤', icon: '/static/images/cashback/category-tshirt.png' },\r\n                { id: 3, name: '衬衫', icon: '/static/images/cashback/category-shirt.png' },\r\n                { id: 4, name: '裤子', icon: '/static/images/cashback/category-pants.png' },\r\n                { id: 5, name: '外套', icon: '/static/images/cashback/category-coat.png' },\r\n                { id: 6, name: '半身裙', icon: '/static/images/cashback/category-skirt.png' }\r\n              ]\r\n            },\r\n            {\r\n              name: '流行款式',\r\n              items: [\r\n                { id: 7, name: '牛仔裤', icon: '/static/images/cashback/category-jeans.png' },\r\n                { id: 8, name: '休闲裤', icon: '/static/images/cashback/category-casual.png' },\r\n                { id: 9, name: '卫衣', icon: '/static/images/cashback/category-hoodie.png' },\r\n                { id: 10, name: '针织衫', icon: '/static/images/cashback/category-knitwear.png' }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '男装',\r\n          subcategories: [\r\n            {\r\n              name: '当季热卖',\r\n              items: [\r\n                { id: 11, name: 'T恤', icon: '/static/images/cashback/category-mtshirt.png' },\r\n                { id: 12, name: '衬衫', icon: '/static/images/cashback/category-mshirt.png' },\r\n                { id: 13, name: '裤子', icon: '/static/images/cashback/category-mpants.png' },\r\n                { id: 14, name: '外套', icon: '/static/images/cashback/category-mcoat.png' }\r\n              ]\r\n            },\r\n            {\r\n              name: '流行款式',\r\n              items: [\r\n                { id: 15, name: '牛仔裤', icon: '/static/images/cashback/category-mjeans.png' },\r\n                { id: 16, name: '休闲裤', icon: '/static/images/cashback/category-mcasual.png' },\r\n                { id: 17, name: '卫衣', icon: '/static/images/cashback/category-mhoodie.png' },\r\n                { id: 18, name: '西装', icon: '/static/images/cashback/category-suit.png' }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '美妆',\r\n          subcategories: [\r\n            {\r\n              name: '热门品类',\r\n              items: [\r\n                { id: 19, name: '面膜', icon: '/static/images/cashback/category-mask.png' },\r\n                { id: 20, name: '口红', icon: '/static/images/cashback/category-lipstick.png' },\r\n                { id: 21, name: '精华', icon: '/static/images/cashback/category-essence.png' },\r\n                { id: 22, name: '眼影', icon: '/static/images/cashback/category-eyeshadow.png' },\r\n                { id: 23, name: '粉底', icon: '/static/images/cashback/category-foundation.png' },\r\n                { id: 24, name: '洁面', icon: '/static/images/cashback/category-cleanser.png' }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '数码',\r\n          subcategories: [\r\n            {\r\n              name: '热门设备',\r\n              items: [\r\n                { id: 25, name: '手机', icon: '/static/images/cashback/category-phone.png' },\r\n                { id: 26, name: '笔记本', icon: '/static/images/cashback/category-laptop.png' },\r\n                { id: 27, name: '平板', icon: '/static/images/cashback/category-tablet.png' },\r\n                { id: 28, name: '耳机', icon: '/static/images/cashback/category-headphone.png' },\r\n                { id: 29, name: '相机', icon: '/static/images/cashback/category-camera.png' },\r\n                { id: 30, name: '手表', icon: '/static/images/cashback/category-watch.png' }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '家电',\r\n          subcategories: [\r\n            {\r\n              name: '大家电',\r\n              items: [\r\n                { id: 31, name: '电视', icon: '/static/images/cashback/category-tv.png' },\r\n                { id: 32, name: '冰箱', icon: '/static/images/cashback/category-fridge.png' },\r\n                { id: 33, name: '洗衣机', icon: '/static/images/cashback/category-washer.png' },\r\n                { id: 34, name: '空调', icon: '/static/images/cashback/category-ac.png' }\r\n              ]\r\n            },\r\n            {\r\n              name: '小家电',\r\n              items: [\r\n                { id: 35, name: '吸尘器', icon: '/static/images/cashback/category-vacuum.png' },\r\n                { id: 36, name: '电饭煲', icon: '/static/images/cashback/category-cooker.png' },\r\n                { id: 37, name: '加湿器', icon: '/static/images/cashback/category-humidifier.png' },\r\n                { id: 38, name: '电热水壶', icon: '/static/images/cashback/category-kettle.png' }\r\n              ]\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '食品',\r\n          subcategories: [\r\n            {\r\n              name: '休闲零食',\r\n              items: [\r\n                { id: 39, name: '坚果', icon: '/static/images/cashback/category-nuts.png' },\r\n                { id: 40, name: '饼干', icon: '/static/images/cashback/category-biscuit.png' },\r\n                { id: 41, name: '糖果', icon: '/static/images/cashback/category-candy.png' },\r\n                { id: 42, name: '巧克力', icon: '/static/images/cashback/category-chocolate.png' }\r\n              ]\r\n            },\r\n            {\r\n              name: '粮油调味',\r\n              items: [\r\n                { id: 43, name: '大米', icon: '/static/images/cashback/category-rice.png' },\r\n                { id: 44, name: '食用油', icon: '/static/images/cashback/category-oil.png' },\r\n                { id: 45, name: '调味料', icon: '/static/images/cashback/category-spice.png' },\r\n                { id: 46, name: '面条', icon: '/static/images/cashback/category-noodle.png' }\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    currentSubcategories() {\r\n      return this.categories[this.activeIndex].subcategories || [];\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 设置页面不显示系统导航栏\r\n    uni.setNavigationBarColor({\r\n      frontColor: '#ffffff',\r\n      backgroundColor: '#9C27B0'\r\n    });\r\n  },\r\n  methods: {\r\n    switchCategory(index) {\r\n      this.activeIndex = index;\r\n    },\r\n    navigateToProductList(item) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/cashback/pages/product-list/index?id=${item.id}&name=${item.name}`\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.category-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.content-container {\r\n  display: flex;\r\n  height: 100vh;\r\n  padding-top: calc(var(--status-bar-height) + 44px);\r\n}\r\n\r\n.category-sidebar {\r\n  width: 80px;\r\n  height: 100%;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.category-item {\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  \r\n  text {\r\n    font-size: 14px;\r\n    color: #666666;\r\n  }\r\n  \r\n  &--active {\r\n    background-color: #FFFFFF;\r\n    \r\n    text {\r\n      color: #9C27B0;\r\n      font-weight: 500;\r\n    }\r\n    \r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      left: 0;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      width: 4px;\r\n      height: 16px;\r\n      background-color: #9C27B0;\r\n      border-radius: 0 2px 2px 0;\r\n    }\r\n  }\r\n}\r\n\r\n.category-content {\r\n  flex: 1;\r\n  height: 100%;\r\n  background-color: #FFFFFF;\r\n}\r\n\r\n.subcategory-grid {\r\n  padding: 16px;\r\n}\r\n\r\n.subcategory-section {\r\n  margin-bottom: 24px;\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.subcategory-title {\r\n  margin-bottom: 16px;\r\n  \r\n  text {\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n  }\r\n}\r\n\r\n.subcategory-items {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 16px;\r\n}\r\n\r\n.subcategory-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  \r\n  .subcategory-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 12px;\r\n    margin-bottom: 8px;\r\n  }\r\n  \r\n  .subcategory-name {\r\n    font-size: 12px;\r\n    color: #666666;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/pages/category/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA8CA,MAAK,eAAgB,MAAW;AAEhC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,YAAY;AAAA,QACV;AAAA,UACE,MAAM;AAAA,UACN,eAAe;AAAA,YACb;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,6CAA8C;AAAA,gBAC1E,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,8CAA+C;AAAA,gBAC1E,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,6CAA8C;AAAA,gBACzE,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,6CAA8C;AAAA,gBACzE,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,4CAA6C;AAAA,gBACxE,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,6CAA6C;AAAA,cAC3E;AAAA,YACD;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,6CAA8C;AAAA,gBAC1E,EAAE,IAAI,GAAG,MAAM,OAAO,MAAM,8CAA+C;AAAA,gBAC3E,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,8CAA+C;AAAA,gBAC1E,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,gDAAgD;AAAA,cAC/E;AAAA,YACF;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,eAAe;AAAA,YACb;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,+CAAgD;AAAA,gBAC5E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,8CAA+C;AAAA,gBAC3E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,8CAA+C;AAAA,gBAC3E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,6CAA6C;AAAA,cAC3E;AAAA,YACD;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,8CAA+C;AAAA,gBAC5E,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,+CAAgD;AAAA,gBAC7E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,+CAAgD;AAAA,gBAC5E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,4CAA4C;AAAA,cAC1E;AAAA,YACF;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,eAAe;AAAA,YACb;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,4CAA6C;AAAA,gBACzE,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,gDAAiD;AAAA,gBAC7E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,+CAAgD;AAAA,gBAC5E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,iDAAkD;AAAA,gBAC9E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,kDAAmD;AAAA,gBAC/E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,gDAAgD;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,eAAe;AAAA,YACb;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,6CAA8C;AAAA,gBAC1E,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,8CAA+C;AAAA,gBAC5E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,8CAA+C;AAAA,gBAC3E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,iDAAkD;AAAA,gBAC9E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,8CAA+C;AAAA,gBAC3E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,6CAA6C;AAAA,cAC3E;AAAA,YACF;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,eAAe;AAAA,YACb;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,0CAA2C;AAAA,gBACvE,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,8CAA+C;AAAA,gBAC3E,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,8CAA+C;AAAA,gBAC5E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,0CAA0C;AAAA,cACxE;AAAA,YACD;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,8CAA+C;AAAA,gBAC5E,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,8CAA+C;AAAA,gBAC5E,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,kDAAmD;AAAA,gBAChF,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,8CAA8C;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,eAAe;AAAA,YACb;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,4CAA6C;AAAA,gBACzE,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,+CAAgD;AAAA,gBAC5E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,6CAA8C;AAAA,gBAC1E,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,iDAAiD;AAAA,cAChF;AAAA,YACD;AAAA,YACD;AAAA,cACE,MAAM;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,4CAA6C;AAAA,gBACzE,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,2CAA4C;AAAA,gBACzE,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,6CAA8C;AAAA,gBAC3E,EAAE,IAAI,IAAI,MAAM,MAAM,MAAM,8CAA8C;AAAA,cAC5E;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;EAEH;AAAA,EACD,UAAU;AAAA,IACR,uBAAuB;AACrB,aAAO,KAAK,WAAW,KAAK,WAAW,EAAE,iBAAiB;IAC5D;AAAA,EACD;AAAA,EACD,SAAS;AAEPA,kBAAAA,MAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,eAAe,OAAO;AACpB,WAAK,cAAc;AAAA,IACpB;AAAA,IACD,sBAAsB,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qDAAqD,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,MACrF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9MA,GAAG,WAAW,eAAe;"}