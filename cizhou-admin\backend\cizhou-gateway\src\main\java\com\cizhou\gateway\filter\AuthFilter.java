package com.cizhou.gateway.filter;

import com.alibaba.fastjson2.JSON;
import com.cizhou.common.core.result.Result;
import com.cizhou.common.core.result.ResultCode;
import com.cizhou.common.security.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 认证过滤器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class AuthFilter extends AbstractGatewayFilterFactory<AuthFilter.Config> {

    @Autowired
    private JwtUtil jwtUtil;

    @Value("${gateway.whitelist}")
    private List<String> whitelist;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    public AuthFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            ServerHttpResponse response = exchange.getResponse();
            
            String path = request.getURI().getPath();
            log.debug("请求路径: {}", path);

            // 检查是否在白名单中
            if (isWhitelisted(path)) {
                log.debug("路径在白名单中，跳过认证: {}", path);
                return chain.filter(exchange);
            }

            // 获取Token
            String token = getTokenFromRequest(request);
            if (!StringUtils.hasText(token)) {
                log.warn("Token缺失，路径: {}", path);
                return handleUnauthorized(response, "Token缺失");
            }

            // 验证Token
            if (!jwtUtil.validateToken(token)) {
                log.warn("Token无效，路径: {}", path);
                return handleUnauthorized(response, "Token无效");
            }

            // 检查Token是否过期
            if (jwtUtil.isTokenExpired(token)) {
                log.warn("Token已过期，路径: {}", path);
                return handleUnauthorized(response, "Token已过期");
            }

            // 获取用户信息并添加到请求头
            String username = jwtUtil.getUsernameFromToken(token);
            if (StringUtils.hasText(username)) {
                ServerHttpRequest mutatedRequest = request.mutate()
                        .header("X-User-Id", username)
                        .header("X-User-Name", username)
                        .build();
                exchange = exchange.mutate().request(mutatedRequest).build();
                log.debug("用户认证成功: {}", username);
            }

            return chain.filter(exchange);
        };
    }

    /**
     * 检查路径是否在白名单中
     */
    private boolean isWhitelisted(String path) {
        return whitelist.stream().anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(ServerHttpRequest request) {
        // 从Authorization头获取
        String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        // 从查询参数获取
        String tokenParam = request.getQueryParams().getFirst("token");
        if (StringUtils.hasText(tokenParam)) {
            return tokenParam;
        }

        return null;
    }

    /**
     * 处理未认证响应
     */
    private Mono<Void> handleUnauthorized(ServerHttpResponse response, String message) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        Result<Object> result = Result.error(ResultCode.UNAUTHORIZED.getCode(), message);
        String body = JSON.toJSONString(result);
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));

        return response.writeWith(Mono.just(buffer));
    }

    public static class Config {
        // 配置类，可以添加过滤器配置参数
    }
}
