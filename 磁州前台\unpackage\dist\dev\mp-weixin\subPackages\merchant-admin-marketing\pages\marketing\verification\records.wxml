<view class="records-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">核销记录</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><view class="filter-area"><view class="filter-tabs"><view wx:for="{{c}}" wx:for-item="tab" wx:key="b" class="{{['filter-tab', tab.c && 'active']}}" bindtap="{{tab.d}}"><text>{{tab.a}}</text></view></view><view class="filter-actions"><view class="date-filter" bindtap="{{e}}"><text class="date-text">{{d}}</text><view class="date-arrow"></view></view><view class="search-btn" bindtap="{{i}}"><svg wx:if="{{h}}" u-s="{{['d']}}" u-i="60361482-0" bind:__l="__l" u-p="{{h}}"><circle wx:if="{{f}}" u-i="60361482-1,60361482-0" bind:__l="__l" u-p="{{f}}"></circle><line wx:if="{{g}}" u-i="60361482-2,60361482-0" bind:__l="__l" u-p="{{g}}"></line></svg></view></view></view><view class="records-list"><view wx:if="{{j}}" class="empty-records"><text class="empty-text">暂无核销记录</text></view><block wx:else><view wx:for="{{k}}" wx:for-item="record" wx:key="i" class="record-item" bindtap="{{record.j}}"><view class="{{['record-type', record.b]}}">{{record.a}}</view><view class="record-content"><view class="record-main"><text class="record-title">{{record.c}}</text><text class="record-code">{{record.d}}</text></view><view class="record-info"><text class="record-user">用户：{{record.e}}</text><text class="record-time">{{record.f}}</text></view></view><view class="{{['record-status', record.h]}}">{{record.g}}</view></view></block></view><view wx:if="{{l}}" class="load-more"><text class="load-text" bindtap="{{m}}">加载更多</text></view><view wx:if="{{n}}" class="search-popup"><view class="search-header"><view class="search-input-box"><input class="search-input" type="text" placeholder="搜索核销码/商品名称/用户" focus bindconfirm="{{o}}" value="{{p}}" bindinput="{{q}}"/><view wx:if="{{r}}" class="clear-btn" bindtap="{{s}}">×</view></view><view class="cancel-btn" bindtap="{{t}}">取消</view></view></view><view wx:if="{{v}}" class="detail-popup"><view class="popup-content"><view class="popup-header"><text class="popup-title">核销详情</text><view class="popup-close" bindtap="{{w}}">×</view></view><view class="detail-info"><view class="{{['detail-status', y]}}">{{x}}</view><view class="detail-item"><text class="detail-label">核销类型</text><text class="detail-value">{{z}}</text></view><view class="detail-item"><text class="detail-label">商品名称</text><text class="detail-value">{{A}}</text></view><view class="detail-item"><text class="detail-label">核销码</text><text class="detail-value">{{B}}</text></view><view class="detail-item"><text class="detail-label">用户信息</text><text class="detail-value">{{C}}</text></view><view class="detail-item"><text class="detail-label">核销时间</text><text class="detail-value">{{D}}</text></view><view class="detail-item"><text class="detail-label">核销人员</text><text class="detail-value">{{E}}</text></view></view><button class="close-btn" bindtap="{{F}}">关闭</button></view></view></view>