{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/distribution/withdraw/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcZGlzdHJpYnV0aW9uXHdpdGhkcmF3XGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"withdraw-container\">\r\n    <!-- 可提现金额卡片 -->\r\n    <view class=\"balance-card\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(172,57,255,0.15)',\r\n      background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx',\r\n      position: 'relative',\r\n      overflow: 'hidden'\r\n    }\">\r\n      <!-- 背景装饰 -->\r\n      <view class=\"bg-decoration\" :style=\"{\r\n        position: 'absolute',\r\n        top: '-50rpx',\r\n        right: '-50rpx',\r\n        width: '300rpx',\r\n        height: '300rpx',\r\n        borderRadius: '50%',\r\n        background: 'rgba(255,255,255,0.1)',\r\n        zIndex: '1'\r\n      }\"></view>\r\n      <view class=\"bg-decoration\" :style=\"{\r\n        position: 'absolute',\r\n        bottom: '-80rpx',\r\n        left: '-80rpx',\r\n        width: '250rpx',\r\n        height: '250rpx',\r\n        borderRadius: '50%',\r\n        background: 'rgba(255,255,255,0.08)',\r\n        zIndex: '1'\r\n      }\"></view>\r\n      \r\n      <!-- 可提现金额 -->\r\n      <view class=\"available-balance\" :style=\"{\r\n        position: 'relative',\r\n        zIndex: '2',\r\n        textAlign: 'center'\r\n      }\">\r\n        <text class=\"label\" :style=\"{\r\n          fontSize: '28rpx',\r\n          color: 'rgba(255,255,255,0.9)',\r\n          marginBottom: '10rpx',\r\n          display: 'block'\r\n        }\">可提现金额(元)</text>\r\n        <text class=\"value\" :style=\"{\r\n          fontSize: '60rpx',\r\n          fontWeight: 'bold',\r\n          color: '#FFFFFF',\r\n          display: 'block',\r\n          textShadow: '0 2rpx 4rpx rgba(0,0,0,0.1)'\r\n        }\">{{ availableBalance }}</text>\r\n      </view>\r\n      \r\n      <!-- 提现信息 -->\r\n      <view class=\"withdraw-info\" :style=\"{\r\n        display: 'flex',\r\n        justifyContent: 'space-around',\r\n        marginTop: '30rpx',\r\n        position: 'relative',\r\n        zIndex: '2',\r\n        background: 'rgba(255,255,255,0.1)',\r\n        borderRadius: '20rpx',\r\n        padding: '20rpx 0'\r\n      }\">\r\n        <view class=\"info-item\">\r\n          <text class=\"info-value\" :style=\"{\r\n            fontSize: '32rpx',\r\n            fontWeight: 'bold',\r\n            color: '#FFFFFF',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">{{ withdrawLimit }}</text>\r\n          <text class=\"info-label\" :style=\"{\r\n            fontSize: '24rpx',\r\n            color: 'rgba(255,255,255,0.8)',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">提现门槛</text>\r\n        </view>\r\n        \r\n        <view class=\"info-item\">\r\n          <text class=\"info-value\" :style=\"{\r\n            fontSize: '32rpx',\r\n            fontWeight: 'bold',\r\n            color: '#FFFFFF',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">{{ withdrawCount }}/{{ maxWithdrawCount }}</text>\r\n          <text class=\"info-label\" :style=\"{\r\n            fontSize: '24rpx',\r\n            color: 'rgba(255,255,255,0.8)',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">本月提现次数</text>\r\n        </view>\r\n        \r\n        <view class=\"info-item\">\r\n          <text class=\"info-value\" :style=\"{\r\n            fontSize: '32rpx',\r\n            fontWeight: 'bold',\r\n            color: '#FFFFFF',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">{{ processingDays }}</text>\r\n          <text class=\"info-label\" :style=\"{\r\n            fontSize: '24rpx',\r\n            color: 'rgba(255,255,255,0.8)',\r\n            display: 'block',\r\n            textAlign: 'center'\r\n          }\">处理天数</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提现表单 -->\r\n    <view class=\"withdraw-form\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n      background: '#FFFFFF',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx'\r\n    }\">\r\n      <view class=\"form-header\" :style=\"{\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <text class=\"form-title\" :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">提现申请</text>\r\n      </view>\r\n      \r\n      <!-- 提现金额 -->\r\n      <view class=\"form-item\" :style=\"{\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <text class=\"form-label\" :style=\"{\r\n          fontSize: '28rpx',\r\n          color: '#333333',\r\n          marginBottom: '15rpx',\r\n          display: 'block'\r\n        }\">提现金额</text>\r\n        \r\n        <view class=\"amount-input-wrapper\" :style=\"{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          borderBottom: '1rpx solid #E5E5EA',\r\n          paddingBottom: '15rpx'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '36rpx',\r\n            fontWeight: '600',\r\n            color: '#333333',\r\n            marginRight: '10rpx'\r\n          }\">¥</text>\r\n          <input \r\n            type=\"digit\" \r\n            v-model=\"withdrawAmount\" \r\n            placeholder=\"请输入提现金额\" \r\n            :style=\"{\r\n              flex: '1',\r\n              fontSize: '36rpx',\r\n              fontWeight: '500'\r\n            }\"\r\n          />\r\n        </view>\r\n        \r\n        <view class=\"amount-tips\" :style=\"{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          marginTop: '15rpx'\r\n        }\">\r\n          <text :style=\"{\r\n            fontSize: '24rpx',\r\n            color: '#999999'\r\n          }\">提现金额不低于{{ withdrawLimit }}元</text>\r\n          \r\n          <text \r\n            @click=\"setMaxAmount\" \r\n            :style=\"{\r\n              fontSize: '24rpx',\r\n              color: '#AC39FF',\r\n              fontWeight: '500'\r\n            }\"\r\n          >全部提现</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 提现方式 -->\r\n      <view class=\"form-item\" :style=\"{\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <text class=\"form-label\" :style=\"{\r\n          fontSize: '28rpx',\r\n          color: '#333333',\r\n          marginBottom: '15rpx',\r\n          display: 'block'\r\n        }\">提现方式</text>\r\n        \r\n        <view class=\"withdraw-methods\">\r\n          <view \r\n            v-for=\"(method, index) in withdrawMethods\" \r\n            :key=\"index\"\r\n            class=\"method-item\"\r\n            :class=\"{ active: currentMethod === index }\"\r\n            @click=\"selectMethod(index)\"\r\n            :style=\"{\r\n              padding: '20rpx',\r\n              borderRadius: '20rpx',\r\n              marginBottom: '15rpx',\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              background: currentMethod === index ? 'rgba(172,57,255,0.1)' : '#F8F8F8',\r\n              border: currentMethod === index ? '1rpx solid #AC39FF' : '1rpx solid transparent'\r\n            }\"\r\n          >\r\n            <view class=\"method-icon\" :style=\"{\r\n              width: '60rpx',\r\n              height: '60rpx',\r\n              borderRadius: '50%',\r\n              background: method.iconBg,\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              marginRight: '20rpx'\r\n            }\">\r\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n                <path :d=\"method.icon\" :stroke=\"method.iconColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </view>\r\n            \r\n            <view class=\"method-info\" :style=\"{ flex: '1' }\">\r\n              <text class=\"method-name\" :style=\"{\r\n                fontSize: '28rpx',\r\n                fontWeight: '500',\r\n                color: '#333333',\r\n                display: 'block',\r\n                marginBottom: '5rpx'\r\n              }\">{{ method.name }}</text>\r\n              \r\n              <text class=\"method-desc\" :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#999999',\r\n                display: 'block'\r\n              }\">{{ method.description }}</text>\r\n            </view>\r\n            \r\n            <view class=\"method-check\" :style=\"{\r\n              width: '40rpx',\r\n              height: '40rpx',\r\n              borderRadius: '50%',\r\n              border: currentMethod === index ? '0' : '1rpx solid #CCCCCC',\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              background: currentMethod === index ? '#AC39FF' : 'transparent'\r\n            }\">\r\n              <svg v-if=\"currentMethod === index\" class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n                <path d=\"M5 12l5 5L20 7\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 提现账户 -->\r\n      <view class=\"form-item\" :style=\"{\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <text class=\"form-label\" :style=\"{\r\n          fontSize: '28rpx',\r\n          color: '#333333',\r\n          marginBottom: '15rpx',\r\n          display: 'block'\r\n        }\">提现账户</text>\r\n        \r\n        <view class=\"account-info\" :style=\"{\r\n          padding: '20rpx',\r\n          borderRadius: '20rpx',\r\n          background: '#F8F8F8',\r\n          display: 'flex',\r\n          alignItems: 'center'\r\n        }\">\r\n          <view class=\"account-avatar\" :style=\"{\r\n            width: '80rpx',\r\n            height: '80rpx',\r\n            borderRadius: '50%',\r\n            overflow: 'hidden',\r\n            marginRight: '20rpx'\r\n          }\">\r\n            <image :src=\"accountInfo.avatar\" mode=\"aspectFill\" :style=\"{\r\n              width: '100%',\r\n              height: '100%'\r\n            }\"></image>\r\n          </view>\r\n          \r\n          <view class=\"account-details\" :style=\"{ flex: '1' }\">\r\n            <text class=\"account-name\" :style=\"{\r\n              fontSize: '28rpx',\r\n              fontWeight: '500',\r\n              color: '#333333',\r\n              display: 'block',\r\n              marginBottom: '5rpx'\r\n            }\">{{ accountInfo.name }}</text>\r\n            \r\n            <text class=\"account-number\" :style=\"{\r\n              fontSize: '24rpx',\r\n              color: '#999999',\r\n              display: 'block'\r\n            }\">{{ accountInfo.number }}</text>\r\n          </view>\r\n          \r\n          <view class=\"change-account\" @click=\"changeAccount\" :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#AC39FF',\r\n            padding: '10rpx 20rpx',\r\n            borderRadius: '30rpx',\r\n            background: 'rgba(172,57,255,0.1)'\r\n          }\">\r\n            更换\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 提现须知 -->\r\n      <view class=\"withdraw-notice\" :style=\"{\r\n        padding: '20rpx',\r\n        borderRadius: '20rpx',\r\n        background: 'rgba(255,149,0,0.1)',\r\n        marginBottom: '30rpx'\r\n      }\">\r\n        <view class=\"notice-header\" :style=\"{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          marginBottom: '10rpx'\r\n        }\">\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n            <path d=\"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            <path d=\"M12 8v4M12 16h.01\" stroke=\"#FF9500\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n          <text :style=\"{\r\n            fontSize: '26rpx',\r\n            fontWeight: '500',\r\n            color: '#FF9500',\r\n            marginLeft: '10rpx'\r\n          }\">提现须知</text>\r\n        </view>\r\n        \r\n        <text :style=\"{\r\n          fontSize: '24rpx',\r\n          color: '#FF9500',\r\n          lineHeight: '1.6'\r\n        }\">{{ withdrawNotice }}</text>\r\n      </view>\r\n      \r\n      <!-- 提交按钮 -->\r\n      <button \r\n        class=\"submit-btn\" \r\n        @click=\"submitWithdraw\"\r\n        :disabled=\"!isFormValid\"\r\n        :style=\"{\r\n          width: '100%',\r\n          height: '90rpx',\r\n          borderRadius: '45rpx',\r\n          background: isFormValid ? 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)' : '#CCCCCC',\r\n          color: '#FFFFFF',\r\n          fontSize: '32rpx',\r\n          fontWeight: '500',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          boxShadow: isFormValid ? '0 5px 15px rgba(172,57,255,0.3)' : 'none'\r\n        }\"\r\n      >\r\n        提交申请\r\n      </button>\r\n    </view>\r\n    \r\n    <!-- 提现记录 -->\r\n    <view class=\"withdraw-records\" :style=\"{\r\n      borderRadius: '35px',\r\n      boxShadow: '0 8px 20px rgba(0,0,0,0.08)',\r\n      background: '#FFFFFF',\r\n      padding: '30rpx',\r\n      marginBottom: '30rpx'\r\n    }\">\r\n      <view class=\"records-header\" :style=\"{\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center',\r\n        marginBottom: '20rpx'\r\n      }\">\r\n        <text class=\"records-title\" :style=\"{\r\n          fontSize: '32rpx',\r\n          fontWeight: '600',\r\n          color: '#333333'\r\n        }\">提现记录</text>\r\n        \r\n        <view class=\"view-all\" @click=\"navigateTo('/subPackages/activity-showcase/pages/distribution/records/index')\" :style=\"{\r\n          fontSize: '26rpx',\r\n          color: '#AC39FF',\r\n          display: 'flex',\r\n          alignItems: 'center'\r\n        }\">\r\n          <text>查看全部</text>\r\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\r\n            <path d=\"M9 18l6-6-6-6\" stroke=\"#AC39FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 记录列表 -->\r\n      <view class=\"records-list\">\r\n        <view \r\n          v-for=\"(record, index) in withdrawRecords\" \r\n          :key=\"index\"\r\n          class=\"record-item\"\r\n          :style=\"{\r\n            padding: '20rpx 0',\r\n            borderBottom: index < withdrawRecords.length - 1 ? '1rpx solid #F2F2F7' : 'none',\r\n            display: 'flex',\r\n            alignItems: 'center'\r\n          }\"\r\n        >\r\n          <view class=\"record-icon\" :style=\"{\r\n            width: '80rpx',\r\n            height: '80rpx',\r\n            borderRadius: '50%',\r\n            background: getStatusColor(record.status, 0.1),\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'center',\r\n            marginRight: '20rpx'\r\n          }\">\r\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\r\n              <path :d=\"getStatusIcon(record.status)\" :stroke=\"getStatusColor(record.status)\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n            </svg>\r\n          </view>\r\n          \r\n          <view class=\"record-info\" :style=\"{ flex: '1' }\">\r\n            <view class=\"record-top\" :style=\"{\r\n              display: 'flex',\r\n              justifyContent: 'space-between',\r\n              marginBottom: '5rpx'\r\n            }\">\r\n              <text class=\"record-title\" :style=\"{\r\n                fontSize: '28rpx',\r\n                fontWeight: '500',\r\n                color: '#333333'\r\n              }\">提现到{{ record.method }}</text>\r\n              \r\n              <text class=\"record-amount\" :style=\"{\r\n                fontSize: '28rpx',\r\n                fontWeight: '600',\r\n                color: '#333333'\r\n              }\">¥{{ record.amount }}</text>\r\n            </view>\r\n            \r\n            <view class=\"record-bottom\" :style=\"{\r\n              display: 'flex',\r\n              justifyContent: 'space-between'\r\n            }\">\r\n              <text class=\"record-time\" :style=\"{\r\n                fontSize: '24rpx',\r\n                color: '#999999'\r\n              }\">{{ record.time }}</text>\r\n              \r\n              <text class=\"record-status\" :style=\"{\r\n                fontSize: '24rpx',\r\n                color: getStatusColor(record.status)\r\n              }\">{{ getStatusText(record.status) }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 空状态 -->\r\n        <view v-if=\"withdrawRecords.length === 0\" class=\"empty-state\" :style=\"{\r\n          padding: '50rpx 0',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          alignItems: 'center'\r\n        }\">\r\n          <image src=\"/static/images/empty/empty-records.png\" mode=\"aspectFit\" :style=\"{\r\n            width: '200rpx',\r\n            height: '200rpx',\r\n            marginBottom: '20rpx'\r\n          }\"></image>\r\n          <text :style=\"{\r\n            fontSize: '26rpx',\r\n            color: '#999999'\r\n          }\">暂无提现记录</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部安全区域 -->\r\n    <view class=\"safe-area-bottom\" :style=\"{\r\n      height: '100rpx',\r\n      paddingBottom: 'env(safe-area-inset-bottom)'\r\n    }\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\n\r\n// 提现相关数据\r\nconst availableBalance = ref('311.11');\r\nconst withdrawLimit = ref('10.00');\r\nconst withdrawCount = ref(1);\r\nconst maxWithdrawCount = ref(3);\r\nconst processingDays = ref('1-3天');\r\nconst withdrawAmount = ref('');\r\n\r\n// 提现方式\r\nconst withdrawMethods = ref([\r\n  {\r\n    name: '微信零钱',\r\n    description: '提现到微信零钱，实时到账',\r\n    icon: 'M6 9h12v12H6z M6 5V3h12v2 M16 11v8 M12 11v8 M8 11v8',\r\n    iconColor: '#07C160',\r\n    iconBg: 'rgba(7,193,96,0.1)'\r\n  },\r\n  {\r\n    name: '支付宝',\r\n    description: '提现到支付宝账户，实时到账',\r\n    icon: 'M22 8v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z M6 12l4 4 8-8',\r\n    iconColor: '#1677FF',\r\n    iconBg: 'rgba(22,119,255,0.1)'\r\n  },\r\n  {\r\n    name: '银行卡',\r\n    description: '提现到银行卡，1-3个工作日到账',\r\n    icon: 'M3 10h18M7 15h1m4 0h1m4 0h1M21 4v16a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4',\r\n    iconColor: '#FF9500',\r\n    iconBg: 'rgba(255,149,0,0.1)'\r\n  }\r\n]);\r\nconst currentMethod = ref(0);\r\n\r\n// 账户信息\r\nconst accountInfo = ref({\r\n  avatar: 'https://via.placeholder.com/80',\r\n  name: '张三',\r\n  number: '微信账号已绑定'\r\n});\r\n\r\n// 提现须知\r\nconst withdrawNotice = ref('1. 提现金额不低于10元，单日最高提现金额为5000元；\\n2. 每月可免费提现3次，超出次数将收取1%手续费；\\n3. 提现申请提交后，将在1-3个工作日内处理完成；\\n4. 如有问题，请联系客服。');\r\n\r\n// 提现记录\r\nconst withdrawRecords = ref([\r\n  {\r\n    method: '微信零钱',\r\n    amount: '100.00',\r\n    time: '2023-05-15 14:30:25',\r\n    status: 'success'\r\n  },\r\n  {\r\n    method: '支付宝',\r\n    amount: '200.00',\r\n    time: '2023-05-10 09:15:36',\r\n    status: 'processing'\r\n  },\r\n  {\r\n    method: '银行卡',\r\n    amount: '500.00',\r\n    time: '2023-05-01 16:42:18',\r\n    status: 'failed'\r\n  }\r\n]);\r\n\r\n// 表单验证\r\nconst isFormValid = computed(() => {\r\n  const amount = parseFloat(withdrawAmount.value);\r\n  const limit = parseFloat(withdrawLimit.value);\r\n  return amount && amount >= limit && amount <= parseFloat(availableBalance.value);\r\n});\r\n\r\n// 选择提现方式\r\nfunction selectMethod(index) {\r\n  currentMethod.value = index;\r\n  \r\n  // 更新账户信息\r\n  if (index === 0) {\r\n    accountInfo.value.name = '张三';\r\n    accountInfo.value.number = '微信账号已绑定';\r\n  } else if (index === 1) {\r\n    accountInfo.value.name = '张三';\r\n    accountInfo.value.number = '支付宝账号已绑定';\r\n  } else {\r\n    accountInfo.value.name = '张三';\r\n    accountInfo.value.number = '工商银行 (1234)';\r\n  }\r\n}\r\n\r\n// 设置最大提现金额\r\nfunction setMaxAmount() {\r\n  withdrawAmount.value = availableBalance.value;\r\n}\r\n\r\n// 更换账户\r\nfunction changeAccount() {\r\n  uni.showToast({\r\n    title: '功能开发中',\r\n    icon: 'none'\r\n  });\r\n}\r\n\r\n// 提交提现申请\r\nfunction submitWithdraw() {\r\n  if (!isFormValid.value) {\r\n    uni.showToast({\r\n      title: '请检查提现金额',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  uni.showLoading({\r\n    title: '提交中...'\r\n  });\r\n  \r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    uni.showToast({\r\n      title: '提现申请已提交',\r\n      icon: 'success'\r\n    });\r\n    \r\n    // 模拟提交成功后重置表单\r\n    withdrawAmount.value = '';\r\n    \r\n    // 模拟添加新记录\r\n    const newRecord = {\r\n      method: withdrawMethods.value[currentMethod.value].name,\r\n      amount: withdrawAmount.value,\r\n      time: new Date().toLocaleString(),\r\n      status: 'processing'\r\n    };\r\n    withdrawRecords.value.unshift(newRecord);\r\n  }, 1500);\r\n}\r\n\r\n// 获取状态颜色\r\nfunction getStatusColor(status, alpha = 1) {\r\n  switch (status) {\r\n    case 'success':\r\n      return alpha === 1 ? '#34C759' : 'rgba(52,199,89,' + alpha + ')';\r\n    case 'processing':\r\n      return alpha === 1 ? '#007AFF' : 'rgba(0,122,255,' + alpha + ')';\r\n    case 'failed':\r\n      return alpha === 1 ? '#FF3B30' : 'rgba(255,59,48,' + alpha + ')';\r\n    default:\r\n      return alpha === 1 ? '#8E8E93' : 'rgba(142,142,147,' + alpha + ')';\r\n  }\r\n}\r\n\r\n// 获取状态图标\r\nfunction getStatusIcon(status) {\r\n  switch (status) {\r\n    case 'success':\r\n      return 'M22 11.08V12a10 10 0 1 1-5.93-9.14';\r\n    case 'processing':\r\n      return 'M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z';\r\n    case 'failed':\r\n      return 'M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z';\r\n    default:\r\n      return 'M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z';\r\n  }\r\n}\r\n\r\n// 获取状态文本\r\nfunction getStatusText(status) {\r\n  switch (status) {\r\n    case 'success':\r\n      return '提现成功';\r\n    case 'processing':\r\n      return '处理中';\r\n    case 'failed':\r\n      return '提现失败';\r\n    default:\r\n      return '未知状态';\r\n  }\r\n}\r\n\r\n// 页面导航\r\nfunction navigateTo(url) {\r\n  uni.navigateTo({ url });\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.withdraw-container {\r\n  padding: 30rpx;\r\n  background-color: #F2F2F7;\r\n  min-height: 100vh;\r\n}\r\n\r\n.submit-btn:active {\r\n  opacity: 0.9;\r\n  transform: scale(0.98);\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/distribution/withdraw/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni"], "mappings": ";;;;;;;;;;;AA6fA,UAAM,mBAAmBA,cAAAA,IAAI,QAAQ;AACrC,UAAM,gBAAgBA,cAAAA,IAAI,OAAO;AACjC,UAAM,gBAAgBA,cAAAA,IAAI,CAAC;AAC3B,UAAM,mBAAmBA,cAAAA,IAAI,CAAC;AAC9B,UAAM,iBAAiBA,cAAAA,IAAI,MAAM;AACjC,UAAM,iBAAiBA,cAAAA,IAAI,EAAE;AAG7B,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,WAAW;AAAA,QACX,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,WAAW;AAAA,QACX,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,QACN,WAAW;AAAA,QACX,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AACD,UAAM,gBAAgBA,cAAAA,IAAI,CAAC;AAG3B,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,iBAAiBA,cAAAA,IAAI,uGAAuG;AAGlI,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AAGD,UAAM,cAAcC,cAAQ,SAAC,MAAM;AACjC,YAAM,SAAS,WAAW,eAAe,KAAK;AAC9C,YAAM,QAAQ,WAAW,cAAc,KAAK;AAC5C,aAAO,UAAU,UAAU,SAAS,UAAU,WAAW,iBAAiB,KAAK;AAAA,IACjF,CAAC;AAGD,aAAS,aAAa,OAAO;AAC3B,oBAAc,QAAQ;AAGtB,UAAI,UAAU,GAAG;AACf,oBAAY,MAAM,OAAO;AACzB,oBAAY,MAAM,SAAS;AAAA,MAC/B,WAAa,UAAU,GAAG;AACtB,oBAAY,MAAM,OAAO;AACzB,oBAAY,MAAM,SAAS;AAAA,MAC/B,OAAS;AACL,oBAAY,MAAM,OAAO;AACzB,oBAAY,MAAM,SAAS;AAAA,MAC5B;AAAA,IACH;AAGA,aAAS,eAAe;AACtB,qBAAe,QAAQ,iBAAiB;AAAA,IAC1C;AAGA,aAAS,gBAAgB;AACvBC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,aAAS,iBAAiB;AACxB,UAAI,CAAC,YAAY,OAAO;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,uBAAe,QAAQ;AAGvB,cAAM,YAAY;AAAA,UAChB,QAAQ,gBAAgB,MAAM,cAAc,KAAK,EAAE;AAAA,UACnD,QAAQ,eAAe;AAAA,UACvB,OAAM,oBAAI,KAAM,GAAC,eAAgB;AAAA,UACjC,QAAQ;AAAA,QACd;AACI,wBAAgB,MAAM,QAAQ,SAAS;AAAA,MACxC,GAAE,IAAI;AAAA,IACT;AAGA,aAAS,eAAe,QAAQ,QAAQ,GAAG;AACzC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO,UAAU,IAAI,YAAY,oBAAoB,QAAQ;AAAA,QAC/D,KAAK;AACH,iBAAO,UAAU,IAAI,YAAY,oBAAoB,QAAQ;AAAA,QAC/D,KAAK;AACH,iBAAO,UAAU,IAAI,YAAY,oBAAoB,QAAQ;AAAA,QAC/D;AACE,iBAAO,UAAU,IAAI,YAAY,sBAAsB,QAAQ;AAAA,MAClE;AAAA,IACH;AAGA,aAAS,cAAc,QAAQ;AAC7B,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,aAAS,cAAc,QAAQ;AAC7B,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,aAAS,WAAW,KAAK;AACvBA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClrBA,GAAG,WAAW,eAAe;"}