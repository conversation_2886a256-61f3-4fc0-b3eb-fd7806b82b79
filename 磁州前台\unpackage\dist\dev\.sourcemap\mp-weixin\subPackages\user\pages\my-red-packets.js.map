{"version": 3, "file": "my-red-packets.js", "sources": ["subPackages/user/pages/my-red-packets.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcdXNlclxwYWdlc1xteS1yZWQtcGFja2V0cy52dWU"], "sourcesContent": ["<template>\n  <view class=\"my-red-packets\">\n    <!-- 顶部标签页 -->\n    <view class=\"tabs\">\n      <view \n        v-for=\"(tab, index) in tabs\" \n        :key=\"index\"\n        class=\"tab-item\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        {{ tab.name }}\n      </view>\n    </view>\n    \n    <!-- 红包列表 -->\n    <scroll-view \n      class=\"red-packet-list\"\n      scroll-y\n      @scrolltolower=\"loadMore\"\n      :refresher-enabled=\"true\"\n      @refresherrefresh=\"onRefresh\"\n    >\n      <view \n        v-for=\"(item, index) in redPacketList\" \n        :key=\"index\"\n        class=\"red-packet-item\"\n        @click=\"viewRedPacketDetail(item)\"\n      >\n        <view class=\"red-packet-info\">\n          <view class=\"title\">{{ item.title }}</view>\n          <view class=\"time\">{{ formatTime(item.createTime) }}</view>\n          <view class=\"amount\" v-if=\"item.type === 'received'\">\n            获得 {{ formatAmount(item.amount) }}元\n          </view>\n          <view class=\"amount\" v-else>\n            发出 {{ formatAmount(item.totalAmount) }}元\n          </view>\n        </view>\n        <view class=\"status\" :class=\"item.status\">\n          {{ getStatusText(item.status) }}\n        </view>\n      </view>\n      \n      <!-- 加载更多 -->\n      <view class=\"loading\" v-if=\"loading\">加载中...</view>\n      <view class=\"no-more\" v-if=\"!hasMore && redPacketList.length > 0\">没有更多了</view>\n      <view class=\"empty\" v-if=\"!loading && redPacketList.length === 0\">\n        <image src=\"/static/images/empty-red-packet.png\" mode=\"aspectFit\"></image>\n        <text>暂无红包记录</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue';\nimport { getMyRedPackets } from '@/utils/redPacket.js';\nimport { formatTime, formatAmount } from '@/utils/format.js';\n\n// Vue 3 Composition API 代码开始\n// 标签页配置\nconst tabs = ref([\n  { name: '收到的红包', type: 'received' },\n  { name: '发出的红包', type: 'sent' }\n]);\n\n// 当前选中的标签页\nconst currentTab = ref(0);\n\n// 红包列表数据\nconst redPacketList = ref([]);\n\n// 分页和加载状态\nconst page = ref(1);\nconst pageSize = ref(20);\nconst loading = ref(false);\nconst hasMore = ref(true);\n\n// 缓存机制\nconst cache = reactive(new Map());\n\n// 生命周期钩子\nonMounted(() => {\n  loadData();\n});\n\n// 切换标签页\nconst switchTab = (index) => {\n  if (currentTab.value === index) return;\n  currentTab.value = index;\n  page.value = 1;\n  redPacketList.value = [];\n  hasMore.value = true;\n  loadData();\n};\n\n// 加载数据\nconst loadData = async () => {\n  if (loading.value || !hasMore.value) return;\n  \n  loading.value = true;\n  const type = tabs.value[currentTab.value].type;\n  const cacheKey = `${type}_${page.value}`;\n  \n  try {\n    // 检查缓存\n    if (cache.has(cacheKey)) {\n      const cachedData = cache.get(cacheKey);\n      redPacketList.value = [...redPacketList.value, ...cachedData.list];\n      hasMore.value = cachedData.hasMore;\n    } else {\n      const res = await getMyRedPackets({\n        type,\n        page: page.value,\n        pageSize: pageSize.value\n      });\n      \n      redPacketList.value = [...redPacketList.value, ...res.list];\n      hasMore.value = res.hasMore;\n      \n      // 设置缓存\n      cache.set(cacheKey, {\n        list: res.list,\n        hasMore: res.hasMore\n      });\n    }\n    \n    page.value++;\n  } catch (err) {\n    uni.showToast({\n      title: '加载失败',\n      icon: 'none'\n    });\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 下拉刷新\nconst onRefresh = async () => {\n  page.value = 1;\n  redPacketList.value = [];\n  hasMore.value = true;\n  cache.clear(); // 清除缓存\n  await loadData();\n  uni.stopPullDownRefresh();\n};\n\n// 加载更多\nconst loadMore = () => {\n  loadData();\n};\n\n// 查看红包详情\nconst viewRedPacketDetail = (item) => {\n  uni.navigateTo({\n    url: `/pages/red-packet/detail?id=${item.id}`\n  });\n};\n\n// 获取状态文本\nconst getStatusText = (status) => {\n  const statusMap = {\n    pending: '待领取',\n    received: '已领取',\n    expired: '已过期',\n    completed: '已领完'\n  };\n  return statusMap[status] || status;\n};\n// Vue 3 Composition API 代码结束\n</script>\n\n<style lang=\"scss\">\n.my-red-packets {\n  min-height: 100vh;\n  background-color: #f8f8f8;\n  \n  .tabs {\n    display: flex;\n    background-color: #fff;\n    padding: 20rpx 0;\n    position: sticky;\n    top: 0;\n    z-index: 1;\n    \n    .tab-item {\n      flex: 1;\n      text-align: center;\n      font-size: 28rpx;\n      color: #666;\n      position: relative;\n      padding: 20rpx 0;\n      \n      &.active {\n        color: #FF6347;\n        font-weight: 500;\n        \n        &::after {\n          content: '';\n          position: absolute;\n          bottom: 0;\n          left: 50%;\n          transform: translateX(-50%);\n          width: 40rpx;\n          height: 4rpx;\n          background-color: #FF6347;\n          border-radius: 2rpx;\n        }\n      }\n    }\n  }\n  \n  .red-packet-list {\n    height: calc(100vh - 100rpx);\n    \n    .red-packet-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 30rpx;\n      background-color: #fff;\n      margin-bottom: 2rpx;\n      \n      .red-packet-info {\n        flex: 1;\n        \n        .title {\n          font-size: 28rpx;\n          color: #333;\n          margin-bottom: 10rpx;\n        }\n        \n        .time {\n          font-size: 24rpx;\n          color: #999;\n          margin-bottom: 10rpx;\n        }\n        \n        .amount {\n          font-size: 32rpx;\n          color: #FF6347;\n          font-weight: 500;\n        }\n      }\n      \n      .status {\n        font-size: 24rpx;\n        padding: 4rpx 12rpx;\n        border-radius: 4rpx;\n        \n        &.pending {\n          color: #FF6347;\n          background-color: #FFF0F5;\n        }\n        \n        &.received {\n          color: #52c41a;\n          background-color: #f6ffed;\n        }\n        \n        &.expired {\n          color: #999;\n          background-color: #f5f5f5;\n        }\n        \n        &.completed {\n          color: #1890ff;\n          background-color: #e6f7ff;\n        }\n      }\n    }\n    \n    .loading, .no-more {\n      text-align: center;\n      padding: 30rpx;\n      color: #999;\n      font-size: 24rpx;\n    }\n    \n    .empty {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      padding: 100rpx 0;\n      \n      image {\n        width: 200rpx;\n        height: 200rpx;\n        margin-bottom: 20rpx;\n      }\n      \n      text {\n        font-size: 28rpx;\n        color: #999;\n      }\n    }\n  }\n}\n</style> \n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/user/pages/my-red-packets.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onMounted", "getMyRedPackets", "uni", "MiniProgramPage"], "mappings": ";;;;;;;;AA8DA,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,EAAE,MAAM,SAAS,MAAM,WAAY;AAAA,MACnC,EAAE,MAAM,SAAS,MAAM,OAAQ;AAAA,IACjC,CAAC;AAGD,UAAM,aAAaA,cAAAA,IAAI,CAAC;AAGxB,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAG5B,UAAM,OAAOA,cAAAA,IAAI,CAAC;AAClB,UAAM,WAAWA,cAAAA,IAAI,EAAE;AACvB,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AAGxB,UAAM,QAAQC,cAAQ,SAAC,oBAAI,IAAG,CAAE;AAGhCC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,UAAM,YAAY,CAAC,UAAU;AAC3B,UAAI,WAAW,UAAU;AAAO;AAChC,iBAAW,QAAQ;AACnB,WAAK,QAAQ;AACb,oBAAc,QAAQ;AACtB,cAAQ,QAAQ;AAChB;IACF;AAGA,UAAM,WAAW,YAAY;AAC3B,UAAI,QAAQ,SAAS,CAAC,QAAQ;AAAO;AAErC,cAAQ,QAAQ;AAChB,YAAM,OAAO,KAAK,MAAM,WAAW,KAAK,EAAE;AAC1C,YAAM,WAAW,GAAG,IAAI,IAAI,KAAK,KAAK;AAEtC,UAAI;AAEF,YAAI,MAAM,IAAI,QAAQ,GAAG;AACvB,gBAAM,aAAa,MAAM,IAAI,QAAQ;AACrC,wBAAc,QAAQ,CAAC,GAAG,cAAc,OAAO,GAAG,WAAW,IAAI;AACjE,kBAAQ,QAAQ,WAAW;AAAA,QACjC,OAAW;AACL,gBAAM,MAAM,MAAMC,gCAAgB;AAAA,YAChC;AAAA,YACA,MAAM,KAAK;AAAA,YACX,UAAU,SAAS;AAAA,UAC3B,CAAO;AAED,wBAAc,QAAQ,CAAC,GAAG,cAAc,OAAO,GAAG,IAAI,IAAI;AAC1D,kBAAQ,QAAQ,IAAI;AAGpB,gBAAM,IAAI,UAAU;AAAA,YAClB,MAAM,IAAI;AAAA,YACV,SAAS,IAAI;AAAA,UACrB,CAAO;AAAA,QACF;AAED,aAAK;AAAA,MACN,SAAQ,KAAK;AACZC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,YAAY,YAAY;AAC5B,WAAK,QAAQ;AACb,oBAAc,QAAQ;AACtB,cAAQ,QAAQ;AAChB,YAAM,MAAK;AACX,YAAM,SAAQ;AACdA,oBAAG,MAAC,oBAAmB;AAAA,IACzB;AAGA,UAAM,WAAW,MAAM;AACrB;IACF;AAGA,UAAM,sBAAsB,CAAC,SAAS;AACpCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+BAA+B,KAAK,EAAE;AAAA,MAC/C,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA,QAChB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,WAAW;AAAA,MACf;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzKA,GAAG,WAAWC,SAAe;"}