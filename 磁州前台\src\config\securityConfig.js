/**
 * 安全配置文件
 */

// 密码策略配置
export const PASSWORD_POLICY = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  maxAge: 90, // 密码有效期（天）
  historyCount: 5 // 记住多少个历史密码（防止重复使用）
};

// 会话配置
export const SESSION_CONFIG = {
  timeout: 30, // 会话超时时间（分钟）
  renewThreshold: 5, // 自动续期阈值（分钟）
  maxConcurrentSessions: 3 // 最大并发会话数
};

// 推广工具安全配置
export const PROMOTION_SECURITY = {
  maxDailyPromotions: 100, // 每日最大推广次数
  maxDailyShares: 50, // 每日最大分享次数
  requireVerification: true, // 是否需要身份验证
  sensitiveContentCheck: true, // 是否检查敏感内容
  allowedPromotionTypes: ['merchant', 'product', 'carpool', 'house', 'service', 'community', 'activity', 'content']
};

// 数据脱敏配置
export const DATA_MASKING = {
  userIdVisible: 3, // 用户ID可见前几位
  phoneVisible: 3, // 手机号可见前几位
  emailNameRatio: 0.3, // 邮箱名称可见比例
  addressVisible: 5 // 地址可见前几位
};

// 安全头配置
export const SECURITY_HEADERS = {
  'Content-Security-Policy': "default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self' data:;",
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Referrer-Policy': 'no-referrer-when-downgrade'
};

// 安全常量
export const SECURITY_CONSTANTS = {
  TOKEN_KEY: 'auth_token',
  REFRESH_TOKEN_KEY: 'refresh_token',
  USER_INFO_KEY: 'user_info',
  MAX_LOGIN_ATTEMPTS: 5,
  LOGIN_LOCKOUT_TIME: 30, // 分钟
  SENSITIVE_ROUTES: [
    '/pages/user/profile',
    '/pages/payment/',
    '/pages/admin/',
    '/subPackages/merchant-admin/'
  ]
};

export default {
  PASSWORD_POLICY,
  SESSION_CONFIG,
  PROMOTION_SECURITY,
  DATA_MASKING,
  SECURITY_HEADERS,
  SECURITY_CONSTANTS
}; 