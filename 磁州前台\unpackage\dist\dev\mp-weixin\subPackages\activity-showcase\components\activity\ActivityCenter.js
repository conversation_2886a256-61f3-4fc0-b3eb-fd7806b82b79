"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_polyline + _component_svg + _component_path + _component_circle)();
}
if (!Math) {
  ActivityCardFactory();
}
const ActivityCardFactory = () => "./ActivityCardFactory.js";
const scrollSpeed = 3e3;
const scrollAnimationDuration = 500;
const _sfc_main = {
  __name: "ActivityCenter",
  setup(__props) {
    const activityTabs = common_vendor.ref([
      { name: "全部活动", icon: true },
      { name: "拼团活动", icon: true },
      { name: "秒杀活动", icon: true },
      { name: "优惠券", icon: true },
      { name: "满减活动", icon: true }
    ]);
    const currentTab = common_vendor.ref(0);
    const activities = common_vendor.ref([
      {
        id: "1",
        type: "groupBuy",
        title: "5人拼团 | 磁县特产礼盒",
        status: "ongoing",
        startTime: "2024-07-10",
        endTime: "2024-07-20",
        location: "磁县特产展销中心",
        coverImage: "/static/images/activity/group-buy-1.jpg",
        isFavorite: false,
        hot: true,
        isPaidTop: true,
        // 付费置顶
        groupPrice: 99,
        originalPrice: 199,
        groupSize: 5,
        currentGroupMembers: 3,
        participants: [
          { avatar: "/static/images/avatar/avatar-1.jpg" },
          { avatar: "/static/images/avatar/avatar-2.jpg" },
          { avatar: "/static/images/avatar/avatar-3.jpg" },
          { avatar: "/static/images/avatar/avatar-4.jpg" }
        ]
      },
      {
        id: "2",
        type: "flashSale",
        title: "限时秒杀 | 本地农产品特惠",
        status: "ongoing",
        startTime: "2024-07-12",
        endTime: "2024-07-13",
        location: "磁县农贸市场",
        coverImage: "/static/images/activity/flash-sale-1.jpg",
        isFavorite: true,
        isAdTop: true,
        // 广告置顶
        salePrice: 29.9,
        originalPrice: 59.9,
        totalStock: 100,
        soldCount: 78,
        participants: [
          { avatar: "/static/images/avatar/avatar-5.jpg" },
          { avatar: "/static/images/avatar/avatar-6.jpg" },
          { avatar: "/static/images/avatar/avatar-7.jpg" },
          { avatar: "/static/images/avatar/avatar-8.jpg" },
          { avatar: "/static/images/avatar/avatar-9.jpg" }
        ]
      },
      {
        id: "3",
        type: "coupon",
        title: "周末专享 | 餐饮优惠券",
        status: "upcoming",
        startTime: "2024-07-15",
        endTime: "2024-07-17",
        location: "磁县商业街",
        coverImage: "/static/images/activity/coupon-1.jpg",
        isFavorite: false,
        couponType: "cash",
        couponValue: 50,
        couponCondition: "满200元可用",
        couponValidity: "2024-07-31",
        participants: [
          { avatar: "/static/images/avatar/avatar-10.jpg" },
          { avatar: "/static/images/avatar/avatar-11.jpg" }
        ]
      },
      {
        id: "4",
        type: "discount",
        title: "商圈联合满减 | 暑期狂欢",
        status: "ongoing",
        startTime: "2024-07-01",
        endTime: "2024-07-31",
        location: "磁县中心商场",
        coverImage: "/static/images/activity/discount-1.jpg",
        isFavorite: false,
        hot: true,
        isPaidTop: true,
        // 付费置顶
        discountRules: [
          { threshold: 100, discount: 20 },
          { threshold: 200, discount: 50 },
          { threshold: 300, discount: 100 }
        ],
        merchantCount: 28,
        participants: [
          { avatar: "/static/images/avatar/avatar-12.jpg" },
          { avatar: "/static/images/avatar/avatar-13.jpg" },
          { avatar: "/static/images/avatar/avatar-14.jpg" },
          { avatar: "/static/images/avatar/avatar-15.jpg" },
          { avatar: "/static/images/avatar/avatar-16.jpg" },
          { avatar: "/static/images/avatar/avatar-17.jpg" }
        ]
      },
      {
        id: "5",
        type: "groupBuy",
        title: "亲子团购 | 磁县水上乐园门票",
        status: "ongoing",
        startTime: "2024-07-05",
        endTime: "2024-08-05",
        location: "磁县水上乐园",
        coverImage: "/static/images/activity/group-buy-2.jpg",
        isFavorite: false,
        isAdTop: true,
        // 广告置顶
        groupPrice: 79,
        originalPrice: 158,
        groupSize: 3,
        currentGroupMembers: 1,
        participants: [
          { avatar: "/static/images/avatar/avatar-18.jpg" }
        ]
      }
    ]);
    const filteredActivities = common_vendor.computed(() => {
      if (currentTab.value === 0) {
        return activities.value;
      }
      const tabTypeMap = {
        1: "groupBuy",
        2: "flashSale",
        3: "coupon",
        4: "discount"
      };
      const selectedType = tabTypeMap[currentTab.value];
      return activities.value.filter((activity) => activity.type === selectedType);
    });
    const scrollLeft = common_vendor.ref(0);
    const cardWidth = common_vendor.ref(620);
    const cardWidthPx = common_vendor.ref(310);
    const scrollInterval = common_vendor.ref(null);
    common_vendor.ref(null);
    const currentScrollIndex = common_vendor.ref(0);
    const lastScrollLeft = common_vendor.ref(0);
    const isScrolling = common_vendor.ref(false);
    const isLooping = common_vendor.ref(true);
    const centerOffset = common_vendor.ref(0);
    const centerOffsetRpx = common_vendor.ref(0);
    const screenWidth = common_vendor.ref(375);
    function startAutoScroll() {
      if (scrollInterval.value || filteredActivities.value.length <= 1)
        return;
      scrollInterval.value = setInterval(() => {
        if (isUserInteracting.value || isScrolling.value)
          return;
        if (currentScrollIndex.value >= filteredActivities.value.length - 1) {
          isScrolling.value = true;
          currentScrollIndex.value = 0;
          scrollToIndex(0, true);
          setTimeout(() => {
            isScrolling.value = false;
          }, 50);
        } else {
          currentScrollIndex.value++;
          isScrolling.value = true;
          scrollToIndex(currentScrollIndex.value, true);
          setTimeout(() => {
            isScrolling.value = false;
          }, scrollAnimationDuration);
        }
      }, scrollSpeed);
    }
    function stopAutoScroll() {
      if (scrollInterval.value) {
        clearInterval(scrollInterval.value);
        scrollInterval.value = null;
      }
    }
    function handleScroll(e) {
      if (isUserInteracting.value) {
        lastScrollLeft.value = e.detail.scrollLeft;
      }
    }
    common_vendor.onMounted(() => {
      common_vendor.index.getSystemInfo({
        success: (res) => {
          screenWidth.value = res.windowWidth;
          let actualCardWidthRpx = 600;
          if (res.windowWidth <= 375) {
            actualCardWidthRpx = 550;
            cardWidth.value = 570;
          } else if (res.windowWidth >= 768) {
            actualCardWidthRpx = 650;
            cardWidth.value = 670;
          } else {
            actualCardWidthRpx = 600;
            cardWidth.value = 620;
          }
          const rpxRatio = res.windowWidth / 750;
          cardWidthPx.value = cardWidth.value * rpxRatio;
          centerOffset.value = (res.windowWidth - actualCardWidthRpx * rpxRatio) / 2;
          centerOffsetRpx.value = centerOffset.value / rpxRatio;
          common_vendor.index.__f__("log", "at subPackages/activity-showcase/components/activity/ActivityCenter.vue:365", "屏幕宽度:", res.windowWidth, "px");
          common_vendor.index.__f__("log", "at subPackages/activity-showcase/components/activity/ActivityCenter.vue:366", "卡片宽度:", actualCardWidthRpx, "rpx =", actualCardWidthRpx * rpxRatio, "px");
          common_vendor.index.__f__("log", "at subPackages/activity-showcase/components/activity/ActivityCenter.vue:367", "居中偏移量:", centerOffset.value, "px =", centerOffsetRpx.value, "rpx");
          setTimeout(() => {
            scrollToIndex(0, false);
          }, 100);
        }
      });
      common_vendor.index.onAppShow(() => {
        if (!isUserInteracting.value) {
          startAutoScroll();
        }
      });
      common_vendor.index.onAppHide(() => {
        stopAutoScroll();
      });
      setTimeout(() => {
        startAutoScroll();
      }, 1e3);
    });
    common_vendor.onBeforeUnmount(() => {
      stopAutoScroll();
    });
    function switchTab(index) {
      currentTab.value = index;
      currentScrollIndex.value = 0;
      setTimeout(() => {
        scrollToIndex(0, false);
      }, 100);
      stopAutoScroll();
      setTimeout(() => {
        startAutoScroll();
      }, 500);
    }
    function navigateToDetail(data) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/detail/index?id=${data.id}&type=${data.type}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/activity-showcase/components/activity/ActivityCenter.vue:420", "导航失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    }
    function toggleFavorite(id) {
      const activity = activities.value.find((item) => item.id === id);
      if (activity) {
        activity.isFavorite = !activity.isFavorite;
        common_vendor.index.showToast({
          title: activity.isFavorite ? "已收藏" : "已取消收藏",
          icon: "none"
        });
      }
    }
    function handleAction(data) {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/components/activity/ActivityCenter.vue:445", "操作按钮点击:", data);
      switch (data.type) {
        case "groupBuy":
          if (data.status === "ongoing") {
            common_vendor.index.navigateTo({
              url: `/subPackages/activity-showcase/pages/group-buy?id=${data.id}`
            });
          } else if (data.status === "upcoming") {
            common_vendor.index.showToast({
              title: "已预约拼团提醒",
              icon: "success"
            });
          }
          break;
        case "flashSale":
          if (data.status === "ongoing") {
            common_vendor.index.navigateTo({
              url: `/subPackages/activity-showcase/pages/flash-sale?id=${data.id}`
            });
          } else if (data.status === "upcoming") {
            common_vendor.index.showToast({
              title: "已设置开始提醒",
              icon: "success"
            });
          }
          break;
        case "coupon":
          common_vendor.index.showToast({
            title: "优惠券已领取",
            icon: "success"
          });
          break;
        case "discount":
          common_vendor.index.navigateTo({
            url: `/subPackages/activity-showcase/pages/discount?id=${data.id}`
          });
          break;
        default:
          navigateToDetail(data);
      }
    }
    function navigateTo(url) {
      common_vendor.index.navigateTo({
        url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/activity-showcase/components/activity/ActivityCenter.vue:497", "导航失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    }
    const isUserInteracting = common_vendor.ref(false);
    function handleTouchStart() {
      isUserInteracting.value = true;
      stopAutoScroll();
    }
    function scrollToPrev() {
      if (currentScrollIndex.value <= 0) {
        if (isLooping.value) {
          currentScrollIndex.value = filteredActivities.value.length - 1;
        } else {
          return;
        }
      } else {
        currentScrollIndex.value--;
      }
      stopAutoScroll();
      scrollToIndex(currentScrollIndex.value);
      setTimeout(() => {
        if (!isUserInteracting.value) {
          startAutoScroll();
        }
      }, 1500);
    }
    function scrollToNext() {
      if (currentScrollIndex.value >= filteredActivities.value.length - 1) {
        if (isLooping.value) {
          currentScrollIndex.value = 0;
        } else {
          return;
        }
      } else {
        currentScrollIndex.value++;
      }
      stopAutoScroll();
      scrollToIndex(currentScrollIndex.value);
      setTimeout(() => {
        if (!isUserInteracting.value) {
          startAutoScroll();
        }
      }, 1500);
    }
    function scrollToIndex(index, animate = true) {
      currentScrollIndex.value = index;
      const targetScrollLeft = index * cardWidthPx.value;
      if (!animate) {
        scrollLeft.value = targetScrollLeft;
      } else {
        isScrolling.value = true;
        scrollLeft.value = targetScrollLeft;
        setTimeout(() => {
          isScrolling.value = false;
        }, scrollAnimationDuration);
      }
      return targetScrollLeft;
    }
    function handleTouchEnd() {
      isUserInteracting.value = false;
      const estimatedIndex = Math.round(lastScrollLeft.value / cardWidthPx.value);
      const maxIndex = filteredActivities.value.length - 1;
      currentScrollIndex.value = Math.max(0, Math.min(estimatedIndex, maxIndex));
      scrollToIndex(currentScrollIndex.value);
      setTimeout(() => {
        if (!isUserInteracting.value) {
          startAutoScroll();
        }
      }, 1500);
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          points: "9 18 15 12 9 6"
        }),
        b: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        c: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/list")),
        d: common_vendor.f(activityTabs.value, (tab, index, i0) => {
          return common_vendor.e({
            a: tab.icon
          }, tab.icon ? common_vendor.e({
            b: tab.name === "全部活动"
          }, tab.name === "全部活动" ? {
            c: "26a06813-3-" + i0 + "," + ("26a06813-2-" + i0),
            d: common_vendor.p({
              d: "M3 3h18v18H3zM12 8v8M8 12h8"
            })
          } : tab.name === "拼团活动" ? {
            f: "26a06813-4-" + i0 + "," + ("26a06813-2-" + i0),
            g: common_vendor.p({
              d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75"
            })
          } : tab.name === "秒杀活动" ? {
            i: "26a06813-5-" + i0 + "," + ("26a06813-2-" + i0),
            j: common_vendor.p({
              d: "M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"
            })
          } : tab.name === "优惠券" ? {
            l: "26a06813-6-" + i0 + "," + ("26a06813-2-" + i0),
            m: common_vendor.p({
              d: "M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"
            })
          } : tab.name === "满减活动" ? {
            o: "26a06813-7-" + i0 + "," + ("26a06813-2-" + i0),
            p: common_vendor.p({
              d: "M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
            })
          } : {
            q: "26a06813-8-" + i0 + "," + ("26a06813-2-" + i0),
            r: common_vendor.p({
              cx: "12",
              cy: "12",
              r: "10"
            })
          }, {
            e: tab.name === "拼团活动",
            h: tab.name === "秒杀活动",
            k: tab.name === "优惠券",
            n: tab.name === "满减活动",
            s: "26a06813-2-" + i0,
            t: common_vendor.p({
              xmlns: "http://www.w3.org/2000/svg",
              width: "18",
              height: "18",
              viewBox: "0 0 24 24",
              fill: "none",
              stroke: "currentColor",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            })
          }) : {}, {
            v: common_vendor.t(tab.name),
            w: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            x: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            y: index,
            z: currentTab.value === index ? 1 : "",
            A: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        e: filteredActivities.value.length > 1
      }, filteredActivities.value.length > 1 ? {
        f: common_vendor.p({
          points: "15 18 9 12 15 6"
        }),
        g: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        h: common_vendor.o(scrollToPrev),
        i: currentScrollIndex.value === 0 && !isLooping.value ? 1 : "",
        j: common_vendor.p({
          points: "9 18 15 12 9 6"
        }),
        k: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "20",
          height: "20",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        l: common_vendor.o(scrollToNext)
      } : {}, {
        m: common_vendor.f(filteredActivities.value, (item, index, i0) => {
          return {
            a: common_vendor.o(navigateToDetail, item.id),
            b: common_vendor.o(toggleFavorite, item.id),
            c: common_vendor.o(handleAction, item.id),
            d: "26a06813-13-" + i0,
            e: common_vendor.p({
              item
            }),
            f: item.id,
            g: `scale(${currentScrollIndex.value === index ? 1 : 0.95})`,
            h: currentScrollIndex.value === index ? 1 : 0.8
          };
        }),
        n: `${centerOffsetRpx.value}rpx`,
        o: `${centerOffsetRpx.value}rpx`,
        p: scrollLeft.value,
        q: common_vendor.o(handleTouchStart),
        r: common_vendor.o(handleTouchEnd),
        s: common_vendor.o(handleScroll),
        t: filteredActivities.value.length > 1
      }, filteredActivities.value.length > 1 ? {
        v: common_vendor.f(filteredActivities.value, (_, index, i0) => {
          return {
            a: index,
            b: currentScrollIndex.value === index ? 1 : "",
            c: common_vendor.o(($event) => scrollToIndex(index), index)
          };
        })
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-26a06813"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/activity/ActivityCenter.js.map
