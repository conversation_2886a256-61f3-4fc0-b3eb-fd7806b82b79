"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const utils_distributionService = require("../../../../../utils/distributionService.js");
const _sfc_main = {
  __name: "commission-rules",
  setup(__props) {
    const settings = common_vendor.reactive({
      commissionMode: "percentage",
      // 佣金模式: percentage(按比例), fixed(固定金额)
      defaultCommission: {
        level1: 10,
        // 一级佣金比例
        level2: 5,
        // 二级佣金比例
        level3: 3
        // 三级佣金比例
      },
      enableLevel3: false,
      // 是否启用三级分销
      calculationBase: "total",
      // 计算基数: total(订单总额), profit(利润)
      settlementTiming: "completed",
      // 结算时机: paid(支付后), completed(完成后), confirmed(确认收货后)
      freezeDays: 7,
      // 冻结天数
      enableMaxCommission: false,
      // 是否启用最高佣金限制
      maxCommission: 100,
      // 最高佣金金额
      selfPurchaseCommission: true
      // 自购是否返佣
    });
    const baseOptions = [
      { value: "total", name: "订单总额" },
      { value: "profit", name: "商品利润" }
    ];
    const settlementOptions = [
      { value: "paid", name: "支付后" },
      { value: "completed", name: "完成后" },
      { value: "confirmed", name: "确认收货后" }
    ];
    const baseIndex = common_vendor.computed(() => {
      return baseOptions.findIndex((option) => option.value === settings.calculationBase);
    });
    const settlementIndex = common_vendor.computed(() => {
      return settlementOptions.findIndex((option) => option.value === settings.settlementTiming);
    });
    common_vendor.onMounted(async () => {
      await getCommissionRules();
    });
    const getCommissionRules = async () => {
      try {
        const result = await utils_distributionService.distributionService.getCommissionRules();
        if (result) {
          Object.assign(settings, result);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules.vue:288", "获取佣金规则设置失败", error);
        common_vendor.index.showToast({
          title: "获取佣金规则设置失败",
          icon: "none"
        });
      }
    };
    const toggleLevel3 = (e) => {
      settings.enableLevel3 = e.detail.value;
    };
    const toggleMaxCommission = (e) => {
      settings.enableMaxCommission = e.detail.value;
    };
    const toggleSelfPurchase = (e) => {
      settings.selfPurchaseCommission = e.detail.value;
    };
    const onBaseChange = (e) => {
      const index = e.detail.value;
      settings.calculationBase = baseOptions[index].value;
    };
    const onSettlementChange = (e) => {
      const index = e.detail.value;
      settings.settlementTiming = settlementOptions[index].value;
    };
    const saveSettings = async () => {
      try {
        common_vendor.index.showLoading({
          title: "保存中...",
          mask: true
        });
        const result = await utils_distributionService.distributionService.saveCommissionRules(settings);
        common_vendor.index.hideLoading();
        if (result.success) {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        } else {
          common_vendor.index.showModal({
            title: "保存失败",
            content: result.message || "请稍后再试",
            showCancel: false
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules.vue:349", "保存佣金规则设置失败", error);
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "none"
        });
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "佣金规则帮助",
        content: "佣金规则设置用于配置分销佣金的计算方式、结算时机和限制条件。您可以设置按比例或固定金额计算佣金，并配置多级分销的佣金分配。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: settings.commissionMode === "percentage" ? 1 : "",
        d: common_vendor.o(($event) => settings.commissionMode = "percentage"),
        e: settings.commissionMode === "fixed" ? 1 : "",
        f: common_vendor.o(($event) => settings.commissionMode = "fixed"),
        g: settings.commissionMode === "percentage" ? "佣金比例" : "固定金额",
        h: settings.defaultCommission.level1,
        i: common_vendor.o(($event) => settings.defaultCommission.level1 = $event.detail.value),
        j: common_vendor.t(settings.commissionMode === "percentage" ? "%" : "元"),
        k: settings.commissionMode === "percentage" ? "佣金比例" : "固定金额",
        l: settings.defaultCommission.level2,
        m: common_vendor.o(($event) => settings.defaultCommission.level2 = $event.detail.value),
        n: common_vendor.t(settings.commissionMode === "percentage" ? "%" : "元"),
        o: settings.enableLevel3
      }, settings.enableLevel3 ? {
        p: settings.commissionMode === "percentage" ? "佣金比例" : "固定金额",
        q: settings.defaultCommission.level3,
        r: common_vendor.o(($event) => settings.defaultCommission.level3 = $event.detail.value),
        s: common_vendor.t(settings.commissionMode === "percentage" ? "%" : "元")
      } : {}, {
        t: settings.enableLevel3,
        v: common_vendor.o(toggleLevel3),
        w: common_vendor.t(baseOptions[baseIndex.value].name),
        x: baseOptions,
        y: baseIndex.value,
        z: common_vendor.o(onBaseChange),
        A: common_vendor.t(settlementOptions[settlementIndex.value].name),
        B: settlementOptions,
        C: settlementIndex.value,
        D: common_vendor.o(onSettlementChange),
        E: settings.freezeDays,
        F: common_vendor.o(($event) => settings.freezeDays = $event.detail.value),
        G: settings.enableMaxCommission,
        H: common_vendor.o(toggleMaxCommission),
        I: settings.enableMaxCommission
      }, settings.enableMaxCommission ? {
        J: settings.maxCommission,
        K: common_vendor.o(($event) => settings.maxCommission = $event.detail.value)
      } : {}, {
        L: settings.selfPurchaseCommission,
        M: common_vendor.o(toggleSelfPurchase),
        N: common_vendor.o(saveSettings)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules.js.map
