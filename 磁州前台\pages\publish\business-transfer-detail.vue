<template>
  <view class="detail-container business-transfer-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">店铺转让详情</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <!-- 隐藏的Canvas用于绘制海报 -->
    <canvas canvas-id="posterCanvas" class="poster-canvas" style="width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;"></canvas>
    
    <!-- 悬浮海报按钮 -->
    <view class="float-poster-btn" @click="generateShareImage">
      <image src="/static/images/tabbar/海报.png" class="poster-icon"></image>
      <text class="poster-text">海报</text>
    </view>
    
    <!-- 举报按钮 -->
    <!-- 悬浮举报按钮已删除 -->
    
    <view class="detail-wrapper business-transfer-wrapper">
      <!-- 转让基本信息卡片 -->
      <view class="content-card transfer-info-card">
        <view class="transfer-header">
          <view class="title-row" style="flex-direction: column; align-items: flex-start;">
            <text class="main-title">{{transferData.title}}</text>
            <text class="price-text">{{transferData.price}}</text>
          </view>
          <view class="meta-info">
            <view class="tag-group">
              <view class="info-tag" v-for="(tag, index) in transferData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="publish-time">发布于 {{formatTime(transferData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 店铺图片轮播 -->
        <swiper class="detail-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
          <swiper-item v-for="(image, index) in transferData.images" :key="index">
            <image :src="image" mode="aspectFill" class="swiper-image"></image>
          </swiper-item>
        </swiper>
        
        <!-- 基本信息 -->
        <view class="basic-info">
          <view class="info-item">
            <text class="info-label">店铺类型</text>
            <text class="info-value">{{transferData.type}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">所在区域</text>
            <text class="info-value">{{transferData.area}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">经营年限</text>
            <text class="info-value">{{transferData.years}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">店铺面积</text>
            <text class="info-value">{{transferData.size}}</text>
          </view>
        </view>
      </view>
      
      <!-- 店铺详情 -->
      <view class="content-card shop-detail-card">
        <view class="section-title">店铺详情</view>
        <view class="detail-list">
          <view class="detail-item" v-for="(item, index) in transferData.details" :key="index">
            <text class="detail-label">{{item.label}}</text>
            <text class="detail-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 经营状况 -->
      <view class="content-card business-status-card">
        <view class="section-title">经营状况</view>
        <view class="detail-list">
          <view class="detail-item">
            <text class="detail-label">月营业额</text>
            <text class="detail-value">{{transferData.monthlyRevenue}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">月利润</text>
            <text class="detail-value">{{transferData.monthlyProfit}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">客流量</text>
            <text class="detail-value">{{transferData.customerFlow}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">员工数量</text>
            <text class="detail-value">{{transferData.employeeCount}}</text>
          </view>
        </view>
      </view>
      
      <!-- 转让说明 -->
      <view class="content-card transfer-notes-card">
        <view class="section-title">转让说明</view>
        <view class="description-content">
          <rich-text :nodes="transferData.notes" class="description-text"></rich-text>
        </view>
      </view>
      
      <!-- 转让原因 -->
      <view class="content-card transfer-reason-card">
        <view class="section-title">转让原因</view>
        <view class="description-content">
          <text class="description-text">{{transferData.reason}}</text>
        </view>
      </view>
      
      <!-- 发布者信息 -->
      <view class="content-card publisher-card">
        <view class="section-title">发布者信息</view>
        <view class="publisher-header">
          <view class="avatar-container">
            <image :src="transferData.publisher.avatar" mode="aspectFill" class="avatar-image"></image>
          </view>
          <view class="publisher-info">
            <text class="publisher-name">{{transferData.publisher.name}}</text>
            <view class="publisher-meta">
              <text class="meta-text">{{transferData.publisher.type}}</text>
              <text class="meta-text">信用等级 {{transferData.publisher.rating}}</text>
              <view class="meta-text" v-if="transferData.publisher.isVerified">
                <text class="iconfont icon-verified"></text>
                <text>已认证</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="section-title">联系方式</view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{transferData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{transferData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 举报卡片 -->
      <report-card :content-id="transferData.id" content-type="business-transfer"></report-card>
      
      <!-- 相关店铺推荐 -->
      <view class="content-card related-shops-card">
        <view class="section-title">相关店铺推荐</view>
        <view class="related-shops-content">
          <!-- 简洁的店铺列表 -->
          <view class="related-shops-list">
            <view class="related-shop-item" 
                 v-for="(shop, index) in relatedShops.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToShopDetail(shop.id)">
              <view class="shop-item-content">
                <view class="shop-item-left">
                  <image class="shop-logo" :src="shop.image" mode="aspectFill"></image>
                </view>
                <view class="shop-item-middle">
                  <text class="shop-item-title">{{shop.title}}</text>
                  <view class="shop-item-area">{{shop.area}}</view>
                  <view class="shop-item-tags">
                    <text class="shop-item-tag" v-for="(tag, tagIndex) in shop.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="shop-item-tag-more" v-if="shop.tags.length > 2">+{{shop.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="shop-item-right">
                  <text class="shop-item-price">{{shop.price}}</text>
                </view>
              </view>
            </view>
            
            <!-- 暂无数据提示 -->
            <view class="empty-related-shops" v-if="relatedShops.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关店铺</text>
            </view>
          </view>
          
          <!-- 查看更多按钮 -->
          <view class="view-more-btn" v-if="relatedShops.length > 0" @click.stop="navigateToTransferList">
            <text class="view-more-text">查看更多店铺信息</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
        </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ReportCard from '@/components/ReportCard.vue'
import mockApi from '@/mock/api'

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 获取状态栏高度
const statusBarHeight = ref(20); // 默认值

// 响应式数据
const isCollected = ref(false);
const transferData = ref({});
const relatedShops = ref([]);

// 加载生意转让详情数据
const loadTransferDetail = (id) => {
  uni.showLoading({
    title: '加载中'
  });
  
  // 使用模拟API获取生意转让详情
  mockApi.publish.getBusinessTransferDetail(id).then(data => {
    transferData.value = data;
    uni.hideLoading();
  }).catch(err => {
    uni.hideLoading();
    uni.showToast({
      title: '获取详情失败',
      icon: 'none'
    });
    console.error('获取详情失败:', err);
  });
};

// 加载相关店铺数据
const loadRelatedShops = () => {
  // 使用模拟API获取相关店铺数据
  mockApi.publish.getRelatedShops().then(data => {
    relatedShops.value = data;
  }).catch(err => {
    console.error('获取相关店铺失败:', err);
  });
};

// 跳转到店铺详情页
const navigateToShopDetail = (id) => {
  // 避免重复跳转当前页面
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  if (id === options.id) {
    return;
  }
  
  uni.navigateTo({
    url: `/pages/business/shop-detail?id=${id}`
  });
};

// 跳转到生意转让列表页
const navigateToTransferList = (e) => {
  if (e) e.stopPropagation();
  // 跳转到生意转让信息列表页
  const transferCategory = transferData.value.tags?.[0] || '';
  uni.navigateTo({ 
    url: `/subPackages/service/pages/filter?type=business-transfer&title=${encodeURIComponent('生意转让')}&category=${encodeURIComponent(transferCategory)}&active=business` 
  });
};

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: transferData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 打开私信聊天
const openChat = () => {
  if (!transferData.value.publisher || !transferData.value.publisher.id) {
    uni.showToast({
      title: '无法获取发布者信息',
      icon: 'none'
    });
    return;
  }
  
  uni.navigateTo({
    url: `/pages/message/chat?userId=${transferData.value.publisher.id}&userName=${transferData.value.publisher.name}`
  });
};

// 生成分享海报
const generateShareImage = () => {
  uni.showLoading({
    title: '正在生成海报...',
    mask: true
  });
  
  // 创建海报数据对象
  const posterData = {
    title: transferData.value.title,
    price: transferData.value.price,
    type: transferData.value.type,
    area: transferData.value.area,
    address: transferData.value.address,
    phone: transferData.value.contact.phone,
    description: transferData.value.description ? transferData.value.description.substring(0, 60) + '...' : '',
    qrcode: '/static/images/tabbar/客服微信.png',
    logo: '/static/images/tabbar/生意转让.png',
    bgImage: transferData.value.images[0] || '/static/images/banner/banner-1.png'
  };
  
  // #ifdef H5
  // H5环境不支持canvas绘制图片保存，提示用户
  setTimeout(() => {
    uni.hideLoading();
    uni.showModal({
      title: '提示',
      content: 'H5环境暂不支持保存海报，请使用App或小程序',
      showCancel: false
    });
  }, 1000);
  return;
  // #endif
  
  // 绘制海报
  const ctx = uni.createCanvasContext('posterCanvas');
  
  // 绘制背景
  ctx.save();
  ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
  // 添加半透明蒙层
  ctx.setFillStyle('rgba(0, 0, 0, 0.35)');
  ctx.fillRect(0, 0, 600, 900);
  ctx.restore();
  
  // 绘制白色卡片背景
  ctx.save();
  ctx.setFillStyle('#ffffff');
  ctx.fillRect(30, 280, 540, 550);
  ctx.restore();
  
  // 绘制Logo
  ctx.save();
  ctx.beginPath();
  ctx.arc(300, 200, 80, 0, 2 * Math.PI);
  ctx.setFillStyle('#ffffff');
  ctx.fill();
  // 在圆形内绘制Logo
  ctx.clip();
  ctx.drawImage(posterData.logo, 220, 120, 160, 160);
  ctx.restore();
  
  // 绘制标题
  ctx.setFillStyle('#333333');
  ctx.setFontSize(32);
  ctx.setTextAlign('center');
  ctx.fillText(posterData.title, 300, 350);
  
  // 绘制价格
  ctx.setFillStyle('#FF6B6B');
  ctx.setFontSize(28);
  ctx.fillText(posterData.price, 300, 400);
  
  // 分割线
  ctx.beginPath();
  ctx.setStrokeStyle('#eeeeee');
  ctx.setLineWidth(2);
  ctx.moveTo(100, 430);
  ctx.lineTo(500, 430);
  ctx.stroke();
  
  // 绘制商铺类型
  ctx.setFillStyle('#666666');
  ctx.setFontSize(24);
  ctx.setTextAlign('left');
  ctx.fillText('商铺类型: ' + posterData.type, 80, 480);
  
  // 绘制面积
  ctx.fillText('面积: ' + posterData.area, 80, 520);
  
  // 绘制位置
  ctx.fillText('位置: ' + posterData.address, 80, 560);
  
  // A wrap text function
  const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {
    if (text.length === 0) return;
    
    const words = text.split('');
    let line = '';
    let testLine = '';
    let lineCount = 0;
    
    for (let n = 0; n < words.length; n++) {
      testLine += words[n];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > maxWidth && n > 0) {
        ctx.fillText(line, x, y + (lineCount * lineHeight));
        line = words[n];
        testLine = words[n];
        lineCount++;
        
        if (lineCount >= 2) {
          line += '...';
          ctx.fillText(line, x, y + (lineCount * lineHeight));
          break;
        }
      } else {
        line = testLine;
      }
    }
    
    if (lineCount < 2) {
      ctx.fillText(line, x, y + (lineCount * lineHeight));
    }
  };
  
  // 绘制描述
  ctx.setFillStyle('#666666');
  ctx.fillText('描述:', 80, 600);
  wrapText(ctx, posterData.description, 80, 630, 440, 35);
  
  // 绘制电话
  if (posterData.phone) {
    ctx.fillText('联系电话: ' + posterData.phone, 80, 680);
  }
  
  // 绘制小程序码
  ctx.drawImage(posterData.qrcode, 230, 700, 140, 140);
  
  // 绘制小程序码提示文字
  ctx.setFillStyle('#999999');
  ctx.setFontSize(20);
  ctx.setTextAlign('center');
  ctx.fillText('长按识别小程序码查看详情', 300, 870);
  
  // 执行绘制
  ctx.draw(false, () => {
    setTimeout(() => {
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: (res) => {
          posterImagePath.value = res.tempFilePath;
          showPosterFlag.value = true;
          uni.hideLoading();
          
          uni.showModal({
            title: '提示',
            content: '海报已生成，是否保存到相册？',
            confirmText: '保存',
            success: (res) => {
              if (res.confirm) {
                saveImageToAlbum();
              }
            }
          });
        },
        fail: (err) => {
          console.error('生成海报失败:', err);
          uni.hideLoading();
          uni.showToast({
            title: '生成海报失败',
            icon: 'none'
          });
        }
      });
    }, 500);
  });
};

// 保存图片到相册
const saveImageToAlbum = () => {
  if (!posterImagePath.value) {
    uni.showToast({
      title: '海报未生成',
      icon: 'none'
    });
    return;
      }
  
  uni.saveImageToPhotosAlbum({
    filePath: posterImagePath.value,
    success: () => {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
    },
    fail: () => {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
  });
};

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight;
    }
  });
  
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  // 加载详情数据
  if (options.id) {
    loadTransferDetail(options.id);
  } else {
    // 如果没有ID参数，加载默认数据
    loadTransferDetail('transfer12345');
  }
  
  // 加载相关店铺数据
  loadRelatedShops();
});

// 分享
const onShareAppMessage = () => {
  return {
    title: transferData.value.title,
    path: `/pages/publish/business-transfer-detail?id=${transferData.value.id}`
  };
};

// 海报相关数据
const posterImagePath = ref('');
const showPosterFlag = ref(false);

// 举报相关
const showReportOptions = () => {
  uni.showActionSheet({
    itemList: ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'],
    success: (res) => {
      const reportReasonIndex = res.tapIndex;
      const reasons = ['虚假信息', '违法内容', '色情内容', '侵权投诉', '诱导欺骗', '其他问题'];
      showReportInputDialog(reasons[reportReasonIndex]);
    }
  });
};

const showReportInputDialog = (reason) => {
  // 检查是否登录
  const hasLogin = uni.getStorageSync('token') || false;
  if (!hasLogin) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再进行举报',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }
    });
    return;
  }
  
  uni.showModal({
    title: '举报内容',
    content: `您选择的举报原因是: ${reason}，请确认是否提交举报？`,
    confirmText: '确认举报',
    success: (res) => {
      if (res.confirm) {
        submitReport(reason, '');
      }
    }
  });
};

const submitReport = (reason, content) => {
  uni.showLoading({
    title: '提交中...'
  });
  
  // 这里模拟提交举报信息到服务器
  setTimeout(() => {
    uni.hideLoading();
    
    uni.showToast({
      title: '举报成功',
      icon: 'success'
    });
    
    // 实际开发中，这里应该调用API提交举报信息
    // const reportData = {
    //   type: 'businessTransfer',
    //   id: businessData.value.id,
    //   reason: reason,
    //   content: content
    // };
    // submitReportAPI(reportData).then(() => {
    //   uni.showToast({
    //     title: '举报成功',
    //     icon: 'success'
    //   });
    // });
  }, 1500);
};
</script>

<style scoped>
.detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
  padding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF; /* 修改为白色字体 */
}

.navbar-right {
  width: 50px;
  text-align: right;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
  display: block;
}

.detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 店铺转让页面的特殊样式 */
.business-transfer-container {
  /* 可以添加特定于店铺转让页面的样式 */
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: fixed;
  width: 2rpx;
  height: 2rpx;
  opacity: 0;
  top: -999rpx;
  left: -999rpx;
  z-index: -1;
  overflow: hidden;
  padding: 0;
  margin: 0;
  border: none;
}

.hidden-share-btn::after {
  display: none;
}

/* 内容卡片通用样式 */
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 标题区样式优化 */
.title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.main-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.price-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
  margin-bottom: 12rpx;
}

/* 元数据样式 */
.meta-info {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-right: 20rpx;
  margin-bottom: 8rpx;
}

.info-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.08);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图优化 */
.detail-swiper {
  height: 420rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.swiper-image {
  width: 100%;
  height: 100%;
}

/* 基本信息卡片内部布局 */
.basic-info {
  display: flex;
  flex-wrap: wrap;
  background-color: #f9fafc;
  border-radius: 12rpx;
  padding: 16rpx 0;
  margin-top: 20rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 详情信息列表 */
.detail-list {
  display: flex;
  flex-direction: column;
}

.detail-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #888;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 区块标题优化 */
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}

/* 描述内容样式 */
.description-content {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  line-height: 1.6;
}

.description-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
}

/* 联系人信息样式 */
.contact-content {
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #888;
}

.contact-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.contact-phone {
  color: #1890ff;
}

.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}

.tips-icon {
  font-size: 24rpx;
  color: #ff9800;
  margin-right: 8rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #ff9800;
}

/* 发布者信息 */
.publisher-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.avatar-container {
  width: 88rpx;
  height: 88rpx;
  border-radius: 44rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.publisher-info {
  flex: 1;
}

.publisher-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.publisher-meta {
  display: flex;
  flex-wrap: wrap;
}

.meta-text {
  font-size: 24rpx;
  color: #888;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
}

/* 底部工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  height: 100rpx;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3;
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 80rpx;
  margin: 0 0 0 10rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(240, 240, 240, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  z-index: 90;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(230, 230, 230, 0.6);
  transition: all 0.2s ease;
}

.float-poster-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}

.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.poster-text {
  font-size: 20rpx;
  color: #444;
  line-height: 1;
}

/* 举报按钮样式 */
/* 悬浮举报按钮已删除
.report-btn {
  position: fixed;
  right: 30rpx;
  top: 120rpx;
  z-index: 90;
  background-color: rgba(240, 240, 240, 0.9);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.report-text {
  font-size: 24rpx;
  color: #666;
}
*/

/* 相似转让 */
.similar-transfers {
  margin-bottom: 20rpx;
}
.similar-list {
  display: flex;
  flex-direction: column;
}
.similar-item {
  display: flex;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.similar-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.similar-info {
  flex: 1;
}
.similar-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.similar-price {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 8rpx;
}
.similar-meta {
  font-size: 24rpx;
  color: #999;
}

/* 相关店铺推荐 */
.related-shops-card {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.related-shops-content {
  display: flex;
  flex-direction: column;
}

.related-shops-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.related-shop-item {
  display: flex;
  padding: 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.shop-item-content {
  display: flex;
  align-items: center;
}

.shop-item-left {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.shop-item-middle {
  flex: 1;
}

.shop-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.shop-item-area {
  font-size: 24rpx;
  color: #999;
}

.shop-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.shop-item-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.08);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.shop-item-tag-more {
  font-size: 24rpx;
  color: #999;
}

.shop-item-right {
  width: 120rpx;
  text-align: right;
}

.shop-item-price {
  font-size: 28rpx;
  color: #ff4d4f;
}

.empty-related-shops {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 24rpx;
  color: #999;
}

.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-top: 20rpx;
}

.view-more-text {
  font-size: 24rpx;
  color: #1890ff;
  margin-right: 8rpx;
}

.view-more-icon {
  font-size: 24rpx;
  color: #1890ff;
}
</style> 