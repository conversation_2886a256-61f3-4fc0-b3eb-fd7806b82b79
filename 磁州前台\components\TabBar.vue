<template>
  <view class="tab-bar">
    <view 
      class="tab-item" 
      v-for="(tab, index) in tabs" 
      :key="index"
      :class="{ active: activeTab === tab.id }"
      :data-tab="tab.id"
      @tap="switchTab(tab.id)">
      <view class="tab-icon" v-html="tab.icon"></view>
      <text class="tab-text">{{tab.name}}</text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    activeTab: {
      type: String,
      default: 'marketing'
    }
  },
  data() {
    return {
      tabs: [
        {
          id: 'dashboard',
          name: '数据概览',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>',
          path: ''
        },
        {
          id: 'store',
          name: '店铺管理',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor"><path d="M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z"/></svg>',
          path: '/subPackages/merchant-admin/pages/store/index'
        },
        {
          id: 'marketing',
          name: '营销中心',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>',
          path: '/subPackages/merchant-admin-marketing/pages/marketing/index'
        },
        {
          id: 'order',
          name: '订单管理',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
          path: '/subPackages/merchant-admin-order/pages/order/index'
        },
        {
          id: 'more',
          name: '更多',
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor"><path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/></svg>',
          path: '/subPackages/merchant-admin-home/pages/merchant-home/index'
        }
      ]
    }
  },
  methods: {
    switchTab(tabId) {
      // 如果点击的不是当前选中的标签，则进行跳转
      if (tabId !== this.activeTab) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (tab) {
          // 优先使用redirectTo，避免页面堆栈过多
          uni.redirectTo({
            url: tab.path,
            fail: (err) => {
              // 如果redirectTo失败，尝试使用switchTab
              uni.switchTab({
                url: tab.path,
                fail: (switchErr) => {
                  console.error('页面跳转失败:', switchErr);
                  uni.showToast({
                    title: '页面跳转失败，请稍后再试',
                    icon: 'none'
                  });
                }
              });
            }
          });
        }
      }
      
      // 触发事件通知父组件
      this.$emit('tab-change', tabId);
    }
  }
}
</script>

<style lang="scss">
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 56px;
  background-color: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #F0F0F0;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 100;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
}

/* 强制SVG图标显示 */
.tab-icon svg {
  width: 22px !important;
  height: 22px !important;
  min-width: 22px !important;
  min-height: 22px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  fill: currentColor !important;
}

.tab-text {
  font-size: 10px;
  color: #999999;
}

.tab-item.active .tab-icon {
  color: #1989FA;
}

.tab-item.active .tab-text {
  color: #1989FA;
}

/* 营销中心用橙色 */
.tab-item.active[data-tab="marketing"] .tab-icon {
  color: #FF7600;
}

.tab-item.active[data-tab="marketing"] .tab-text {
  color: #FF7600;
}

/* 数据概览用蓝色 */
.tab-item.active[data-tab="dashboard"] .tab-icon {
  color: #1989FA;
}

.tab-item.active[data-tab="dashboard"] .tab-text {
  color: #1989FA;
}

/* 添加底部空间 */
.bottom-space {
  height: 60px;
}
</style> 