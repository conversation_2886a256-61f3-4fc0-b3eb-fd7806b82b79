<template>
  <view class="publish-detail-container">
    <!-- 导航栏 -->
    <carpool-nav title="发布详情" :showBack="true"></carpool-nav>
    
    <!-- 发布信息摘要 -->
    <view class="summary-card">
      <view class="card-header">
        <view class="card-title">拼车信息摘要</view>
        <view class="card-tag" :class="typeClass">{{typeText}}</view>
      </view>
      
      <!-- 路线信息 -->
      <view class="route-info">
        <view class="route-item">
          <view class="route-dot start"></view>
          <text class="route-text">{{formData.startPoint}}</text>
        </view>
        <view class="route-arrow"></view>
        <view class="route-item">
          <view class="route-dot end"></view>
          <text class="route-text">{{formData.endPoint}}</text>
        </view>
      </view>
      
      <!-- 详细信息 -->
      <view class="info-details">
        <view class="detail-item">
          <image src="/static/images/tabbar/calendar.png" mode="aspectFit" class="detail-icon"></image>
          <text class="detail-text">{{formData.departureDate}} {{formData.departureTime}}</text>
        </view>
        
        <view class="detail-item" v-if="publishType === 'people-to-car'">
          <image src="/static/images/tabbar/user.png" mode="aspectFit" class="detail-icon"></image>
          <text class="detail-text">{{formData.passengers}}人</text>
        </view>
        
        <view class="detail-item" v-if="publishType === 'car-to-people'">
          <image src="/static/images/tabbar/seat.png" mode="aspectFit" class="detail-icon"></image>
          <text class="detail-text">空余座位 {{formData.availableSeats}}</text>
        </view>
      </view>
    </view>
    
    <!-- 推广选项区域 -->
    <view class="promotion-section">
      <view class="section-title">选择发布方式</view>
      
      <!-- 使用ConfigurablePremiumActions组件 -->
      <ConfigurablePremiumActions 
        :pageType="'publish'"
        :showMode="'direct'" 
        @action-completed="handleActionCompleted"
      />
    </view>
    
    <!-- 底部说明 -->
    <view class="publish-tips">
      <view class="tips-icon">
        <image src="/static/images/tabbar/info.png" mode="aspectFit"></image>
      </view>
      <view class="tips-content">
        <text class="tips-title">温馨提示</text>
        <text class="tips-text">付费发布将获得更高展示位置，增加曝光率，提高匹配成功率。</text>
      </view>
    </view>
    
    <!-- 按钮区域 -->
    <view class="button-group">
      <button class="cancel-button" @click="goBack">返回修改</button>
      <button class="confirm-button" @click="confirmPublish">确认发布</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import CarpoolNav from '/components/carpool-nav.vue';
import ConfigurablePremiumActions from '/components/premium/ConfigurablePremiumActions.vue';

// 发布类型
const publishType = ref('people-to-car');
// 发布ID
const publishId = ref('');
// 发布模式（普通/置顶）
const publishMode = ref('');
// 选择的选项
const selectedOption = ref(null);

// 表单数据 - 这里模拟从上一页传递过来的数据
const formData = ref({
  startPoint: '磁州城区-汽车站',
  endPoint: '邯郸火车站',
  departureDate: '2023-11-15',
  departureTime: '09:30',
  passengers: '2',
  availableSeats: '3',
  contactName: '张先生',
  contactPhone: '13800138000',
  remark: '希望准时出发，不要迟到。'
});

// 类型文本
const typeText = computed(() => {
  const typeMap = {
    'people-to-car': '人找车',
    'car-to-people': '车找人',
    'goods-to-car': '货找车',
    'car-to-goods': '车找货'
  };
  return typeMap[publishType.value] || '人找车';
});

// 类型样式类
const typeClass = computed(() => {
  const classMap = {
    'people-to-car': 'tag-blue',
    'car-to-people': 'tag-green',
    'goods-to-car': 'tag-orange',
    'car-to-goods': 'tag-purple'
  };
  return classMap[publishType.value] || 'tag-blue';
});

// 处理页面参数
onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  // 获取发布类型
  if (options.type) {
    publishType.value = options.type;
  }
  
  // 其他初始化逻辑
  console.log('发布详情页加载, 类型:', publishType.value);
});

// 处理推广操作完成
const handleActionCompleted = (actionType, result) => {
  console.log('推广操作完成:', actionType, result);
  selectedOption.value = result;
  
  if (actionType === 'ad') {
    publishMode.value = 'ad';
  } else if (actionType === 'paid') {
    publishMode.value = 'premium';
  }
};

// 返回修改
const goBack = () => {
  uni.navigateBack();
};

// 确认发布
const confirmPublish = () => {
  if (!selectedOption.value) {
    uni.showToast({
      title: '请选择发布方式',
      icon: 'none'
    });
    return;
  }
  
  // 这里应该有一个实际的发布API调用
  // 目前使用模拟数据
  uni.showLoading({
    title: '发布中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 生成一个模拟的发布ID
    publishId.value = 'CP' + Date.now().toString().slice(-8);
    
    // 跳转到发布成功页面
    uni.redirectTo({
      url: `/carpool-package/pages/carpool/publish/success?id=${publishId.value}&type=${publishType.value}&mode=${publishMode.value}`
    });
  }, 1500);
};
</script>

<style lang="scss">
.publish-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 120rpx 30rpx 40rpx;
  display: flex;
  flex-direction: column;
}

/* 摘要卡片 */
.summary-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.card-tag {
  font-size: 24rpx;
  color: #ffffff;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.tag-blue {
  background-color: #0A84FF;
}

.tag-green {
  background-color: #52C41A;
}

.tag-orange {
  background-color: #FF9500;
}

.tag-purple {
  background-color: #722ED1;
}

/* 路线信息 */
.route-info {
  margin-bottom: 20rpx;
}

.route-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.route-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.start {
  background-color: #0A84FF;
}

.end {
  background-color: #FF453A;
}

.route-text {
  font-size: 30rpx;
  color: #333333;
  flex: 1;
}

.route-arrow {
  width: 20rpx;
  height: 30rpx;
  margin-left: 5rpx;
  margin-bottom: 10rpx;
  border-left: 2rpx dashed #cccccc;
  margin-left: 5rpx;
}

/* 详细信息 */
.info-details {
  display: flex;
  flex-wrap: wrap;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
}

.detail-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
  opacity: 0.6;
}

.detail-text {
  font-size: 24rpx;
  color: #666666;
}

/* 推广部分 */
.promotion-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

/* 提示信息 */
.publish-tips {
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.tips-icon {
  margin-right: 15rpx;
}

.tips-icon image {
  width: 36rpx;
  height: 36rpx;
  opacity: 0.6;
}

.tips-content {
  flex: 1;
}

.tips-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 5rpx;
  display: block;
}

.tips-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 按钮区域 */
.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  padding-bottom: env(safe-area-inset-bottom, 20rpx);
}

.cancel-button, .confirm-button {
  width: 48%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #666666;
  border: 1rpx solid #e5e5e5;
}

.confirm-button {
  background: linear-gradient(135deg, #0A84FF, #0066CC);
  color: #FFFFFF;
  box-shadow: 0 6rpx 12rpx rgba(10, 132, 255, 0.2);
}
</style> 