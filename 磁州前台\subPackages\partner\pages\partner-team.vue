<template>
  <view class="team-container">
    <!-- 自定义导航栏 -->
    <cu-custom bgColor="bg-gradient-blue" isBack>
      <block slot="backText"></block>
      <block slot="content">我的团队</block>
    </cu-custom>

    <!-- 数据统计卡片 -->
    <view class="stats-card">
      <view class="stats-header">
        <text class="stats-title">团队概览</text>
        <text class="stats-time">{{currentMonth}}月数据</text>
      </view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-num">{{teamTotalNum}}</text>
          <text class="stats-label">团队总人数</text>
        </view>
        <view class="stats-item">
          <text class="stats-num">{{teamNewNum}}</text>
          <text class="stats-label">本月新增</text>
        </view>
        <view class="stats-item">
          <text class="stats-num">{{teamActiveNum}}</text>
          <text class="stats-label">活跃成员</text>
        </view>
        <view class="stats-item">
          <text class="stats-num">¥ {{teamIncome}}</text>
          <text class="stats-label">团队收益</text>
        </view>
      </view>
    </view>

    <!-- 级别切换 -->
    <view class="tab-section">
      <view class="tab-header">
        <view 
          class="tab-item" 
          :class="{'active': currentLevel === 1}"
          @tap="switchLevel(1)"
        >一级团队</view>
        <view 
          class="tab-item" 
          :class="{'active': currentLevel === 2}"
          @tap="switchLevel(2)"
        >二级团队</view>
      </view>
      <view class="tab-info">
        <text>{{levelInfo}}</text>
      </view>
    </view>

    <!-- 成员列表 -->
    <view class="team-list" v-if="teamMembers.length > 0">
      <view class="team-item" v-for="(item, index) in teamMembers" :key="index">
        <view class="member-avatar">
          <image :src="item.avatar" mode="aspectFill"></image>
        </view>
        <view class="member-info">
          <view class="member-name-row">
            <text class="member-name">{{item.nickname}}</text>
            <text class="member-level">{{item.partnerLevel}}</text>
          </view>
          <view class="member-data">
            <text>加入时间: {{formatDate(item.joinTime)}}</text>
          </view>
        </view>
        <view class="member-right">
          <view class="member-contribution">贡献: ¥{{item.contribution}}</view>
          <view class="member-fans">{{item.fansNum}}个粉丝</view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image src="/static/images/no-team.png" mode="aspectFit"></image>
      <text>{{emptyText}}</text>
      <button class="share-btn" @tap="shareToFriends">邀请好友加入</button>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="teamMembers.length > 0 && hasMore">
      <text @tap="loadMore" v-if="!isLoading">加载更多</text>
      <text v-else>加载中...</text>
    </view>

    <view class="load-end" v-if="teamMembers.length > 0 && !hasMore">
      <text>- 已经到底了 -</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 响应式数据
const currentMonth = ref(new Date().getMonth() + 1);
const teamTotalNum = ref(86);
const teamNewNum = ref(12);
const teamActiveNum = ref(35);
const teamIncome = ref('1286.50');
const currentLevel = ref(1);
const teamMembers = ref([]);
const page = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const isLoading = ref(false);

// 计算属性
// 级别描述信息
const levelInfo = computed(() => {
  return currentLevel.value === 1 
    ? '直接关注您的成员' 
    : '您的一级团队发展的成员';
});

// 空状态文本
const emptyText = computed(() => {
  return currentLevel.value === 1 
    ? '暂无一级团队成员' 
    : '暂无二级团队成员';
});

// 切换级别
const switchLevel = (level) => {
  if (currentLevel.value === level) return;
  
  currentLevel.value = level;
  page.value = 1;
  hasMore.value = true;
  teamMembers.value = [];
  loadData();
};

// 加载数据
const loadData = () => {
  isLoading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    // 模拟数据
    const mockData = generateMockTeam();
    
    if (page.value === 1) {
      teamMembers.value = mockData;
    } else {
      teamMembers.value = [...teamMembers.value, ...mockData];
    }
    
    // 判断是否还有更多数据
    hasMore.value = page.value < 3; // 模拟只有3页数据
    
    isLoading.value = false;
  }, 500);
};

// 加载更多
const loadMore = () => {
  if (isLoading.value || !hasMore.value) return;
  
  page.value++;
  loadData();
};

// 分享给朋友
const shareToFriends = () => {
  uni.navigateTo({
    url: '/pages/my/partner-poster'
  });
};

// 格式化日期
const formatDate = (dateStr) => {
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 生成模拟团队数据
const generateMockTeam = () => {
  const mockTeam = [];
  const nicknames = ['城市猎人', '科技达人', '生活家', '微笑向暖', '光影世界', '千里之行', '星空漫步', '纸短情长'];
  const avatars = [
    '/static/images/avatar-1.png',
    '/static/images/avatar-2.png',
    '/static/images/avatar-3.png',
    '/static/images/avatar-4.png',
    '/static/images/avatar-5.png',
    '/static/images/avatar-6.png'
  ];
  const levels = ['普通合伙人', '银牌合伙人', '金牌合伙人', '钻石合伙人'];
  
  // 随机生成10条记录
  for (let i = 0; i < 10; i++) {
    const now = new Date();
    // 随机日期，最近60天内
    const randomDays = Math.floor(Math.random() * 60);
    const joinTime = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);
    
    // 随机贡献值
    const contribution = Math.floor(Math.random() * 2000);
    
    // 随机粉丝数
    const fansNum = Math.floor(Math.random() * 50);
    
    // 随机等级，二级团队较少高级合伙人
    let levelIndex;
    if (currentLevel.value === 1) {
      levelIndex = Math.floor(Math.random() * levels.length);
    } else {
      // 二级团队中高级合伙人较少
      levelIndex = Math.floor(Math.random() * 2); // 只在前两级中选择
    }
    
    mockTeam.push({
      id: 'member_' + Date.now() + i,
      nickname: nicknames[Math.floor(Math.random() * nicknames.length)],
      avatar: avatars[Math.floor(Math.random() * avatars.length)],
      joinTime,
      partnerLevel: levels[levelIndex],
      contribution,
      fansNum
    });
  }
  
  return mockTeam;
};

// 生命周期钩子
onMounted(() => {
  // 初始化加载数据
  loadData();
});
</script>

<style lang="scss" scoped>
.team-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
}

.stats-card {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.stats-time {
  font-size: 24rpx;
  color: #999999;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 30rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f8f8f8;
  padding: 20rpx 0;
  border-radius: 12rpx;
}

.stats-num {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #999999;
}

.tab-section {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  
  &.active {
    color: #1677FF;
    font-weight: 500;
    
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: #1677FF;
      border-radius: 2rpx;
    }
  }
}

.tab-info {
  padding: 20rpx;
  font-size: 24rpx;
  color: #999999;
  text-align: center;
}

.team-list {
  padding: 0 30rpx;
}

.team-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.member-avatar {
  margin-right: 20rpx;
  
  image {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
  }
}

.member-info {
  flex: 1;
}

.member-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.member-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-right: 15rpx;
}

.member-level {
  font-size: 20rpx;
  color: #ff9500;
  background-color: #fff7e6;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
}

.member-data {
  font-size: 24rpx;
  color: #999999;
}

.member-right {
  text-align: right;
}

.member-contribution {
  font-size: 26rpx;
  color: #ff6000;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.member-fans {
  font-size: 22rpx;
  color: #999999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  
  image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 30rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 40rpx;
  }
  
  .share-btn {
    width: 300rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: linear-gradient(135deg, #1677FF, #0E5FD8);
    color: #ffffff;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}

.load-more, .load-end {
  text-align: center;
  font-size: 26rpx;
  color: #999999;
  padding: 30rpx 0;
}
</style>