<template>
  <view class="order-management-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">订单管理</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 订单管理视图选择 -->
    <view class="view-selector">
      <view 
        v-for="(item, index) in viewOptions" 
        :key="index" 
        class="view-option"
        :class="{'active': currentView === item.value}"
        @click="switchView(item.value)">
        <view class="view-icon-container" :class="'bg-' + item.value">
        </view>
        <text class="view-name">{{item.name}}</text>
      </view>
    </view>
    
    <!-- 订单筛选条件 -->
    <view class="filter-section">
      <scroll-view scroll-x="true" class="filter-scroll">
        <view 
          v-for="(status, index) in orderStatuses" 
          :key="index"
          :class="['status-tag', {'active': currentStatus === status.value}]"
          @click="filterByStatus(status.value)">
          {{status.name}}
        </view>
      </scroll-view>
    </view>
    
    <!-- 主要内容区域 -->
    <view class="content-main">
      <block v-if="orders.length > 0">
        <!-- 列表视图 -->
        <view v-if="currentView === 'list'" class="order-list">
          <navigator 
            v-for="(order, index) in orders" 
            :key="index"
            :url="`./detail?id=${order.id}`"
            class="order-item">
            <view class="order-header">
              <text class="order-number">订单号: {{order.orderNo}}</text>
              <view class="order-status-tag" :class="'status-' + order.status">
                {{getStatusText(order.status)}}
              </view>
            </view>
            <view class="order-info">
              <view class="customer-info">
                <text class="label">客户:</text>
                <text class="value">{{order.customerName}}</text>
              </view>
              <view class="time-info">
                <text class="label">下单时间:</text>
                <text class="value">{{order.createTime}}</text>
              </view>
              <view class="amount-info">
                <text class="label">订单金额:</text>
                <text class="value price">¥{{order.totalAmount}}</text>
              </view>
            </view>
            <view class="order-actions">
              <view class="action-btn primary" @click.stop="handleOrder(order.id)">处理订单</view>
              <view class="action-btn" @click.stop="contactCustomer(order.id)">联系客户</view>
            </view>
          </navigator>
        </view>
        
        <!-- 详情视图占位 -->
        <view v-else-if="currentView === 'detail'" class="order-detail-view">
          <text>详情视图正在开发中...</text>
          <button class="nav-btn" @click="navigateTo('./detail')">查看订单详情示例</button>
        </view>
        
        <!-- 日历视图占位 -->
        <view v-else-if="currentView === 'calendar'" class="order-calendar-view">
          <text>日历视图正在开发中...</text>
          <button class="nav-btn" @click="navigateTo('./calendar')">查看日历视图示例</button>
        </view>
      </block>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <view class="empty-icon-container">
        <view class="empty-icon">📭</view>
        </view>
        <text class="empty-text">暂无订单数据</text>
        <view class="refresh-btn" @click="loadOrders">刷新</view>
      </view>
    </view>
    
    <!-- 底部导航 -->
    <view class="tab-bar">
      <view 
        class="tab-item" 
        v-for="(item, index) in tabItems" 
        :key="index"
        :class="{ active: currentTab === item.id }"
        @tap="switchTab(item.id)">
        <view class="active-indicator" v-if="currentTab === item.id"></view>
        <view class="tab-icon" :class="item.icon"></view>
        <text class="tab-text">{{item.text}}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentView: 'list', // list, detail, calendar
      currentStatus: 'all',
      viewOptions: [
        { name: '列表视图', value: 'list', icon: '📋' },
        { name: '详情视图', value: 'detail', icon: '📝' },
        { name: '日历视图', value: 'calendar', icon: '📅' }
      ],
      orderStatuses: [
        { name: '全部', value: 'all' },
        { name: '待处理', value: 'pending' },
        { name: '处理中', value: 'processing' },
        { name: '已完成', value: 'completed' },
        { name: '已取消', value: 'cancelled' },
        { name: '退款中', value: 'refunding' }
      ],
      orders: [
        {
          id: '1001',
          orderNo: 'CZ20230501001',
          status: 'pending',
          customerName: '张三',
          createTime: '2023-05-01 10:30',
          totalAmount: '128.00'
        },
        {
          id: '1002',
          orderNo: 'CZ20230501002',
          status: 'processing',
          customerName: '李四',
          createTime: '2023-05-01 11:45',
          totalAmount: '256.50'
        },
        {
          id: '1003',
          orderNo: 'CZ20230502003',
          status: 'completed',
          customerName: '王五',
          createTime: '2023-05-02 09:15',
          totalAmount: '89.90'
        }
      ],
      currentTab: 3, // 当前是订单管理
      tabItems: [
        {
          id: 0,
          icon: 'dashboard',
          text: '商家中心',
          url: '/subPackages/merchant-admin-home/pages/merchant-home/index'
        },
        {
          id: 1,
          icon: 'store',
          text: '店铺管理',
          url: '/subPackages/merchant-admin/pages/store/index'
        },
        {
          id: 2,
          icon: 'marketing',
          text: '营销中心',
          url: '/subPackages/merchant-admin-marketing/pages/marketing/index'
        },
        {
          id: 3,
          icon: 'orders',
          text: '订单管理',
          url: '/subPackages/merchant-admin-order/pages/order/index'
        },
        {
          id: 'more',
          icon: 'more',
          text: '更多',
          url: ''
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    switchView(view) {
      this.currentView = view;
    },
    filterByStatus(status) {
      this.currentStatus = status;
      // 这里应该根据状态筛选订单
      this.loadOrders();
    },
    showFilterOptions() {
      uni.showToast({
        title: '筛选功能开发中',
        icon: 'none'
      });
    },
    loadOrders() {
      // 这里应该从服务器加载订单数据
      console.log('加载订单数据，状态:', this.currentStatus);
    },
    handleOrder(orderId) {
      uni.navigateTo({
        url: `./detail?id=${orderId}&action=process`
      });
    },
    contactCustomer(orderId) {
      uni.showToast({
        title: '联系客户功能开发中',
        icon: 'none'
      });
    },
    // 切换底部导航栏
    switchTab(tabId) {
      // 处理"更多"选项
      if (tabId === 'more') {
        this.showMoreOptions();
        return;
      }
      
      if (tabId === this.currentTab) return;
      
      this.currentTab = tabId;
      
      // 特殊处理订单管理标签
      if (tabId === 3) {
        // 当前已经在订单管理页面，不需要跳转
        return;
      }
      
      // 使用redirectTo而不是navigateTo，避免堆栈过多
      uni.redirectTo({
        url: this.tabItems[tabId].url,
        fail: (err) => {
          console.error('redirectTo失败:', err);
          // 如果redirectTo失败，尝试使用switchTab
          uni.switchTab({
            url: this.tabItems[tabId].url,
            fail: (switchErr) => {
              console.error('switchTab也失败:', switchErr);
              uni.showToast({
                title: '页面跳转失败，请稍后再试',
                icon: 'none'
              });
            }
          });
        }
      });
    },
    
    // 显示更多选项弹出菜单
    showMoreOptions() {
      // 准备更多菜单中的选项
      const moreOptions = ['客户运营', '分析洞察', '系统设置'];
      
      uni.showActionSheet({
        itemList: moreOptions,
        success: (res) => {
          // 根据选择的选项进行跳转
          const routes = [
            '/subPackages/merchant-admin-customer/pages/customer/index',
            '/subPackages/merchant-admin/pages/settings/index'
          ];
          uni.navigateTo({
            url: routes[res.tapIndex]
          });
        }
      });
    },
    getStatusColor(status) {
      const colors = {
        pending: '#FF9800',
        processing: '#2196F3',
        completed: '#4CAF50',
        cancelled: '#9E9E9E',
        refunding: '#F44336'
      };
      return colors[status] || '#333333';
    },
    getStatusText(status) {
      const texts = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成',
        cancelled: '已取消',
        refunding: '退款中'
      };
      return texts[status] || '未知状态';
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    }
  },
  onLoad() {
    this.loadOrders();
  }
}
</script>

<style lang="scss">
.order-management-container {
  min-height: 100vh;
  position: relative;
  background-color: #f5f8fc;
  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 80px); /* 增加底部间距，避免被底部导航栏遮挡 */
  display: flex;
  flex-direction: column;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  border-radius: 0;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: transparent;
  border-radius: 0;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
  margin-left: 16px;
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 700; /* 增加字体粗细 */
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.view-selector {
  display: flex;
  background-color: #fff;
  padding: 16px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border-radius: 20px;
  margin: 90px 16px 0;
  z-index: 10;
  position: relative;
}

.view-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
  position: relative;
}

.view-option.active {
  color: #1677FF;
}

.view-option.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #1677FF;
  border-radius: 3px;
}

.view-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.bg-list {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
}

.bg-detail {
  background: linear-gradient(135deg, #2196F3, #0D47A1);
}

.bg-calendar {
  background: linear-gradient(135deg, #9C27B0, #6A1B9A);
}

.view-name {
  font-size: 12px;
  margin-top: 4px;
}

.filter-section {
  display: flex;
  padding: 16px;
  background-color: #fff;
  margin: 16px 16px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border-radius: 16px;
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
}

.status-tag {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 8px;
  background-color: #f0f0f0;
  border-radius: 24px;
  font-size: 12px;
  color: #666;
  transition: all 0.3s ease;
}

.status-tag.active {
  background-color: #e6f7ff;
  color: #1677FF;
  border: 1px solid #91caff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
}

.filter-button {
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-left: 8px;
  border-left: 1px solid #eee;
}

.filter-icon-container {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: linear-gradient(135deg, #FF9800, #F57C00);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.filter-icon {
  font-size: 16px;
}

.content-main {
  flex: 1;
  padding: 16px;
  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 40px); /* 增加底部内边距，避免被底部导航栏遮挡 */
  overflow-y: auto;
}

.order-list {
  display: flex;
  flex-direction: column;
}

.order-item {
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.order-item:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.order-number {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.order-status-tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
}

.status-pending {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

.status-processing {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.status-completed {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
}

.status-cancelled {
  background: linear-gradient(135deg, #9E9E9E, #616161);
}

.status-refunding {
  background: linear-gradient(135deg, #F44336, #C62828);
}

.order-info {
  margin-bottom: 16px;
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 12px;
}

.customer-info, .time-info, .amount-info {
  display: flex;
  margin-bottom: 8px;
}

.amount-info {
  margin-bottom: 0;
}

.label {
  width: 70px;
  color: #666;
  font-size: 13px;
}

.value {
  flex: 1;
  font-size: 13px;
  color: #333;
}

.price {
  font-weight: 700;
  color: #ff6a00;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 8px 16px;
  border-radius: 24px;
  font-size: 13px;
  margin-left: 12px;
  background-color: #f0f0f0;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.action-btn.primary {
  background: linear-gradient(135deg, #1677FF, #0D47A1);
  color: #fff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 0;
}

.empty-icon-container {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #E0E0E0, #BDBDBD);
  border-radius: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.empty-icon {
  font-size: 32px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 24px;
}

.refresh-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #1677FF, #0D47A1);
  color: #fff;
  border-radius: 24px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(22, 119, 255, 0.2);
  transition: all 0.3s ease;
}

.refresh-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
}

.order-detail-view, .order-calendar-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.nav-btn {
  margin-top: 20px;
  background: linear-gradient(135deg, #1677FF, #0D47A1);
  color: #fff;
  font-size: 14px;
  padding: 10px 24px;
  border-radius: 24px;
  border: none;
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(22, 119, 255, 0.2);
  transition: all 0.3s ease;
}

.tab-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  background-color: #FFFFFF;
  border-top: 1px solid var(--border-light);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 0 10px;
  padding-bottom: env(safe-area-inset-bottom);
  flex-shrink: 0;
  border-radius: 0;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  transition: all 0.2s ease;
  padding: 8px 0;
  margin: 0 8px;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 3px;
  transition: all 0.25s cubic-bezier(0.3, 0.7, 0.4, 1.5);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 22px;
  opacity: 0.7;
}

.tab-text {
  font-size: 10px;
  color: var(--text-tertiary);
  transition: color 0.2s ease, transform 0.25s ease;
  transform: scale(0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 0 2px;
}

.active-indicator {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 16px;
  height: 3px;
  border-radius: 0 0 4px 4px;
  background: linear-gradient(90deg, var(--brand-primary), var(--accent-purple));
}

.tab-item.active .tab-icon {
  transform: translateY(-2px);
  opacity: 1;
}

.tab-item.active .tab-text {
  color: var(--brand-primary);
  font-weight: 500;
  transform: scale(1);
}

/* 导航图标样式 */
.tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}

.tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}

.tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'/%3E%3C/svg%3E");
}

.tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}

.tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

@media screen and (max-width: 375px) {
  /* 在较小屏幕上优化导航栏 */
  .tab-text {
    font-size: 9px;
  }
  
  .tab-icon {
    margin-bottom: 2px;
    background-size: 20px;
  }
}

/* 在较宽屏幕上优化导航栏，确保元素不会挤压 */
@media screen and (min-width: 400px) {
  .tab-bar {
    padding: 0 10px;
  }
  
  .tab-item {
    margin: 0 5px;
  }
}

/* 添加底部安全区域 */
.safe-area-bottom {
  height: calc(60px + env(safe-area-inset-bottom));
}
</style> 