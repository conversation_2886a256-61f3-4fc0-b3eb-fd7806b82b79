/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-f925b123, html.data-v-f925b123, #app.data-v-f925b123, .index-container.data-v-f925b123 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.merchant-recommend-section.data-v-f925b123 {
  margin: 24rpx 30rpx 30rpx;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 35rpx;
  /* 35度圆角 */
  padding: 30rpx 24rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08), 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}
.section-header.data-v-f925b123 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 10rpx;
}
.section-title-wrap.data-v-f925b123 {
  display: flex;
  align-items: center;
}
.section-title.data-v-f925b123 {
  font-size: 34rpx;
  font-weight: 700;
  color: #1c1c1e;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", sans-serif;
  letter-spacing: 0.5rpx;
  background: linear-gradient(135deg, #333, #666);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.section-more.data-v-f925b123 {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  transition: all 0.2s ease;
  background: rgba(0, 122, 255, 0.1);
  border: 1rpx solid rgba(0, 122, 255, 0.2);
}
.section-more.data-v-f925b123:active {
  background: rgba(0, 122, 255, 0.2);
  transform: scale(0.96);
}
.more-text.data-v-f925b123 {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 600;
}
.more-icon.data-v-f925b123 {
  font-size: 24rpx;
  color: #007AFF;
  margin-left: 4rpx;
}

/* 轮播区域 */
.merchant-swiper.data-v-f925b123 {
  width: 100%;
  height: 380rpx;
  margin-top: 20rpx;
}
.merchant-swiper-page.data-v-f925b123 {
  height: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 15rpx;
  gap: 25rpx;
}
.merchant-card.data-v-f925b123 {
  width: 310rpx;
  height: 100%;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.08), 0 5rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  transform: perspective(800px) rotateX(5deg);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  position: relative;
}
.merchant-card.data-v-f925b123:active {
  transform: perspective(800px) rotateX(5deg) scale(0.98);
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
}

/* 商家图片区域 */
.merchant-image.data-v-f925b123 {
  width: 100%;
  height: 180rpx;
  position: relative;
  overflow: hidden;
}
.merchant-image.data-v-f925b123::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.2), transparent);
  z-index: 1;
}
.merchant-image image.data-v-f925b123 {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}
.merchant-card:hover .merchant-image image.data-v-f925b123 {
  transform: scale(1.05);
}
.tag-overlay.data-v-f925b123 {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  z-index: 2;
}
.merchant-tag.data-v-f925b123 {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  color: white;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  letter-spacing: 0.5rpx;
}
.new-tag.data-v-f925b123 {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.9), rgba(48, 209, 88, 0.9));
}
.hot-tag.data-v-f925b123 {
  background: linear-gradient(135deg, rgba(255, 69, 58, 0.9), rgba(255, 59, 48, 0.9));
}

/* 商家信息区域 */
.merchant-info.data-v-f925b123 {
  flex: 1;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: linear-gradient(to bottom, #ffffff, #f9f9f9);
}
.merchant-name-row.data-v-f925b123 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.merchant-name.data-v-f925b123 {
  font-size: 26rpx;
  color: #1c1c1e;
  font-weight: 700;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
  letter-spacing: 0.5rpx;
}
.merchant-rating.data-v-f925b123 {
  display: flex;
  align-items: center;
  background: rgba(255, 149, 0, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
}
.rating-score.data-v-f925b123 {
  font-size: 20rpx;
  color: #1c1c1e;
  font-weight: 600;
  margin-right: 4rpx;
}
.rating-icon.data-v-f925b123 {
  font-size: 20rpx;
  color: #FF9500;
}
.merchant-desc.data-v-f925b123 {
  font-size: 22rpx;
  color: #8a8a8e;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 12rpx;
  line-height: 1.3;
}
.merchant-bottom.data-v-f925b123 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}
.merchant-category.data-v-f925b123 {
  font-size: 20rpx;
  color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 600;
  border: 1rpx solid rgba(0, 122, 255, 0.2);
}
.merchant-distance.data-v-f925b123 {
  display: flex;
  align-items: center;
  font-size: 20rpx;
  color: #8a8a8e;
  background: rgba(142, 142, 147, 0.08);
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
}
.distance-icon.data-v-f925b123 {
  font-size: 20rpx;
  margin-right: 4rpx;
}

/* 轮播指示器 */
.merchant-indicators.data-v-f925b123 {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}
.merchant-dot.data-v-f925b123 {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0 6rpx;
  transition: all 0.3s ease;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}
.merchant-dot.active.data-v-f925b123 {
  width: 28rpx;
  background: linear-gradient(90deg, #007AFF, #5AC8FA);
  box-shadow: 0 1rpx 3rpx rgba(0, 122, 255, 0.3);
}
.card-section.data-v-f925b123 {
  margin-bottom: 20rpx;
}
.fade-in.data-v-f925b123 {
  animation: fadeIn-f925b123 0.5s ease-in-out;
}
@keyframes fadeIn-f925b123 {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}