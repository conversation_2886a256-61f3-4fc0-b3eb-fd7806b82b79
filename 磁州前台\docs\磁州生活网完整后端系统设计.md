# 磁州生活网完整后端系统设计与实现

## 目录
- [1. 项目概述](#1-项目概述)
- [2. 系统架构](#2-系统架构)
- [3. 数据库设计](#3-数据库设计)
- [4. 用户管理模块](#4-用户管理模块)
- [5. 商家管理模块](#5-商家管理模块)
- [6. 商品管理模块](#6-商品管理模块)
- [7. 订单管理模块](#7-订单管理模块)
- [8. 支付管理模块](#8-支付管理模块)
- [9. 拼车系统模块](#9-拼车系统模块)
- [10. 房产系统模块](#10-房产系统模块)
- [11. 二手交易模块](#11-二手交易模块)
- [12. 本地服务模块](#12-本地服务模块)
- [13. 社区信息模块](#13-社区信息模块)
- [14. 推广工具模块](#14-推广工具模块)
- [15. 分销系统模块](#15-分销系统模块)
- [16. 返利系统模块](#16-返利系统模块)
- [17. 营销活动模块](#17-营销活动模块)
- [18. 会员系统模块](#18-会员系统模块)
- [19. 积分系统模块](#19-积分系统模块)
- [20. 优惠券模块](#20-优惠券模块)
- [21. 消息通知模块](#21-消息通知模块)
- [22. 客服系统模块](#22-客服系统模块)
- [23. 评价系统模块](#23-评价系统模块)
- [24. 搜索系统模块](#24-搜索系统模块)
- [25. 地理位置模块](#25-地理位置模块)
- [26. 文件管理模块](#26-文件管理模块)
- [27. 数据统计模块](#27-数据统计模块)
- [28. 系统配置模块](#28-系统配置模块)
- [29. 日志审计模块](#29-日志审计模块)
- [30. 安全防护模块](#30-安全防护模块)

## 1. 项目概述

磁州生活网是一个综合性的本地生活服务平台，基于现有的uni-app前端架构，集成了商家服务、商品销售、拼车出行、房产租售、二手交易、社区信息、推广工具、分销系统、返利系统等多个功能模块。

### 1.1 技术栈

- **核心框架**: Spring Boot 3.x + Spring Cloud Alibaba
- **数据访问**: MyBatis Plus + JPA
- **缓存**: Redis 7.x + Caffeine
- **搜索**: Elasticsearch 8.x
- **消息队列**: RabbitMQ + Kafka
- **任务调度**: XXL-Job
- **数据库**: MySQL 8.0 (主从复制)
- **容器化**: Docker + Kubernetes
- **监控**: Prometheus + Grafana + Jaeger
- **配置中心**: Nacos
- **网关**: Spring Cloud Gateway

### 1.2 项目结构

```
cizhou-life-backend/
├── cizhou-common/              # 公共模块
│   ├── common-core/            # 核心工具类
│   ├── common-security/        # 安全组件
│   ├── common-redis/           # Redis组件
│   ├── common-swagger/         # API文档组件
│   └── common-log/             # 日志组件
├── cizhou-gateway/             # API网关
├── cizhou-auth/                # 认证授权服务
├── cizhou-user/                # 用户服务
├── cizhou-merchant/            # 商家服务
├── cizhou-product/             # 商品服务
├── cizhou-order/               # 订单服务
├── cizhou-payment/             # 支付服务
├── cizhou-carpool/             # 拼车服务
├── cizhou-house/               # 房产服务
├── cizhou-secondhand/          # 二手交易服务
├── cizhou-service/             # 本地服务
├── cizhou-community/           # 社区信息服务
├── cizhou-promotion/           # 推广工具服务
├── cizhou-distribution/        # 分销系统服务
├── cizhou-cashback/            # 返利系统服务
├── cizhou-marketing/           # 营销活动服务
├── cizhou-member/              # 会员系统服务
├── cizhou-points/              # 积分系统服务
├── cizhou-coupon/              # 优惠券服务
├── cizhou-notification/        # 消息通知服务
├── cizhou-customer-service/    # 客服系统服务
├── cizhou-review/              # 评价系统服务
├── cizhou-search/              # 搜索系统服务
├── cizhou-location/            # 地理位置服务
├── cizhou-file/                # 文件管理服务
├── cizhou-statistics/          # 数据统计服务
├── cizhou-config/              # 系统配置服务
├── cizhou-audit/               # 日志审计服务
└── cizhou-security/            # 安全防护服务
```

## 2. 系统架构

### 2.1 微服务架构图

```mermaid
graph TB
    A[API网关] --> B[用户服务]
    A --> C[商家服务]
    A --> D[商品服务]
    A --> E[订单服务]
    A --> F[支付服务]
    A --> G[拼车服务]
    A --> H[房产服务]
    A --> I[二手交易服务]
    A --> J[本地服务]
    A --> K[社区信息服务]
    A --> L[推广工具服务]
    A --> M[分销系统服务]
    A --> N[返利系统服务]
    A --> O[营销活动服务]
    A --> P[会员系统服务]
    A --> Q[积分系统服务]
    A --> R[优惠券服务]
    A --> S[消息通知服务]
    A --> T[客服系统服务]
    A --> U[评价系统服务]
    A --> V[搜索系统服务]
    A --> W[地理位置服务]
    A --> X[文件管理服务]
    A --> Y[数据统计服务]
    A --> Z[系统配置服务]
    A --> AA[日志审计服务]
    A --> BB[安全防护服务]
    
    CC[Redis缓存] --> A
    DD[Elasticsearch] --> A
    EE[消息队列] --> A
    FF[MySQL集群] --> B
    FF --> C
    FF --> D
    FF --> E
    FF --> F
    FF --> G
    FF --> H
    FF --> I
    FF --> J
    FF --> K
    FF --> L
    FF --> M
    FF --> N
    FF --> O
    FF --> P
    FF --> Q
    FF --> R
    FF --> S
    FF --> T
    FF --> U
    FF --> V
    FF --> W
    FF --> X
    FF --> Y
    FF --> Z
    FF --> AA
    FF --> BB
```

### 2.2 技术架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层                                │
│  uni-app + Vue3 + TypeScript + Pinia + uView UI            │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      API网关层                               │
│  Spring Cloud Gateway + 限流 + 熔断 + 认证                  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      微服务层                                │
│  Spring Boot + Spring Cloud Alibaba + Nacos + Feign       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      数据访问层                              │
│  MyBatis Plus + JPA + Redis + Elasticsearch                │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      数据存储层                              │
│  MySQL + Redis + Elasticsearch + MinIO                     │
└─────────────────────────────────────────────────────────────┘
```

## 3. 数据库设计

### 3.1 用户相关表

#### 3.1.1 用户基础信息表 (users)

```sql
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(11) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `gender` tinyint DEFAULT 0 COMMENT '性别 0-未知 1-男 2-女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `location` varchar(100) DEFAULT NULL COMMENT '所在地',
  `level` int DEFAULT 1 COMMENT '用户等级',
  `balance` decimal(10,2) DEFAULT 0.00 COMMENT '余额',
  `commission` decimal(10,2) DEFAULT 0.00 COMMENT '佣金',
  `points` int DEFAULT 0 COMMENT '积分',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-正常 2-禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `register_source` varchar(20) DEFAULT NULL COMMENT '注册来源',
  `invite_code` varchar(20) DEFAULT NULL COMMENT '邀请码',
  `inviter_id` bigint DEFAULT NULL COMMENT '邀请人ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';
```

#### 3.1.2 用户认证表 (user_auth)

```sql
CREATE TABLE `user_auth` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '认证ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型 password/wechat/qq/alipay',
  `auth_key` varchar(100) NOT NULL COMMENT '认证标识',
  `auth_secret` varchar(255) NOT NULL COMMENT '认证密钥',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-正常 2-禁用',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_auth_type_key` (`auth_type`, `auth_key`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户认证表';
```

#### 3.1.3 用户地址表 (user_addresses)

```sql
CREATE TABLE `user_addresses` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `phone` varchar(11) NOT NULL COMMENT '收货人电话',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `address` varchar(200) NOT NULL COMMENT '详细地址',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `is_default` tinyint DEFAULT 0 COMMENT '是否默认地址 0-否 1-是',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-正常 2-删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';
```

#### 3.1.4 用户钱包表 (user_wallets)

```sql
CREATE TABLE `user_wallets` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '钱包ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `balance` decimal(10,2) DEFAULT 0.00 COMMENT '余额',
  `frozen_balance` decimal(10,2) DEFAULT 0.00 COMMENT '冻结余额',
  `commission` decimal(10,2) DEFAULT 0.00 COMMENT '佣金',
  `frozen_commission` decimal(10,2) DEFAULT 0.00 COMMENT '冻结佣金',
  `total_income` decimal(10,2) DEFAULT 0.00 COMMENT '总收入',
  `total_expense` decimal(10,2) DEFAULT 0.00 COMMENT '总支出',
  `withdraw_password` varchar(255) DEFAULT NULL COMMENT '提现密码',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-正常 2-冻结',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包表';
```

#### 3.1.5 钱包流水表 (wallet_transactions)

```sql
CREATE TABLE `wallet_transactions` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '流水ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `transaction_no` varchar(32) NOT NULL COMMENT '交易流水号',
  `type` varchar(20) NOT NULL COMMENT '交易类型 recharge/withdraw/consume/refund/commission',
  `amount` decimal(10,2) NOT NULL COMMENT '交易金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '交易前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '交易后余额',
  `description` varchar(200) DEFAULT NULL COMMENT '交易描述',
  `related_id` bigint DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(20) DEFAULT NULL COMMENT '关联类型',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-成功 2-失败 3-处理中',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transaction_no` (`transaction_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包流水表';
```

### 3.2 商家相关表

#### 3.2.1 商家基础信息表 (merchants)

```sql
CREATE TABLE `merchants` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商家ID',
  `user_id` bigint NOT NULL COMMENT '关联用户ID',
  `merchant_no` varchar(32) NOT NULL COMMENT '商家编号',
  `name` varchar(100) NOT NULL COMMENT '商家名称',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `logo` varchar(255) DEFAULT NULL COMMENT '商家logo',
  `images` json DEFAULT NULL COMMENT '商家图片',
  `description` text COMMENT '商家描述',
  `contact_name` varchar(50) NOT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(11) NOT NULL COMMENT '联系电话',
  `business_license` varchar(255) DEFAULT NULL COMMENT '营业执照',
  `id_card_front` varchar(255) DEFAULT NULL COMMENT '身份证正面',
  `id_card_back` varchar(255) DEFAULT NULL COMMENT '身份证背面',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `address` varchar(200) NOT NULL COMMENT '详细地址',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `business_hours` json DEFAULT NULL COMMENT '营业时间',
  `facilities` json DEFAULT NULL COMMENT '设施服务',
  `payment_methods` json DEFAULT NULL COMMENT '支付方式',
  `delivery_service` tinyint DEFAULT 0 COMMENT '是否支持配送 0-否 1-是',
  `takeout_service` tinyint DEFAULT 0 COMMENT '是否支持外卖 0-否 1-是',
  `reservation_service` tinyint DEFAULT 0 COMMENT '是否支持预约 0-否 1-是',
  `min_order_amount` decimal(8,2) DEFAULT 0.00 COMMENT '最低起送金额',
  `delivery_fee` decimal(8,2) DEFAULT 0.00 COMMENT '配送费',
  `delivery_range` decimal(5,2) DEFAULT 0.00 COMMENT '配送范围(公里)',
  `rating` decimal(3,2) DEFAULT 5.00 COMMENT '评分',
  `review_count` int DEFAULT 0 COMMENT '评价数量',
  `order_count` int DEFAULT 0 COMMENT '订单数量',
  `status` tinyint DEFAULT 0 COMMENT '状态 0-待审核 1-正常 2-暂停 3-拒绝',
  `audit_reason` varchar(200) DEFAULT NULL COMMENT '审核原因',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_merchant_no` (`merchant_no`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_location` (`latitude`, `longitude`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家基础信息表';
```

#### 3.2.2 商家分类表 (merchant_categories)

```sql
CREATE TABLE `merchant_categories` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint DEFAULT 0 COMMENT '父分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-正常 2-禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家分类表';
```

#### 3.2.3 商家银行账户表 (merchant_bank_accounts)

```sql
CREATE TABLE `merchant_bank_accounts` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `merchant_id` bigint NOT NULL COMMENT '商家ID',
  `account_name` varchar(100) NOT NULL COMMENT '账户名称',
  `account_number` varchar(50) NOT NULL COMMENT '账户号码',
  `bank_name` varchar(100) NOT NULL COMMENT '银行名称',
  `bank_branch` varchar(100) DEFAULT NULL COMMENT '开户行',
  `is_default` tinyint DEFAULT 0 COMMENT '是否默认账户 0-否 1-是',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-正常 2-禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家银行账户表';
```

### 3.3 商品相关表

#### 3.3.1 商品分类表 (product_categories)

```sql
CREATE TABLE `product_categories` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint DEFAULT 0 COMMENT '父分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) DEFAULT NULL COMMENT '分类图片',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-正常 2-禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';
```

#### 3.3.2 商品基础信息表 (products)

```sql
CREATE TABLE `products` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `merchant_id` bigint NOT NULL COMMENT '商家ID',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `product_no` varchar(32) NOT NULL COMMENT '商品编号',
  `name` varchar(200) NOT NULL COMMENT '商品名称',
  `subtitle` varchar(500) DEFAULT NULL COMMENT '商品副标题',
  `images` json NOT NULL COMMENT '商品图片',
  `video` varchar(255) DEFAULT NULL COMMENT '商品视频',
  `description` text COMMENT '商品描述',
  `specifications` json DEFAULT NULL COMMENT '商品规格',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价',
  `stock` int DEFAULT 0 COMMENT '库存数量',
  `min_stock` int DEFAULT 0 COMMENT '最低库存',
  `sales_count` int DEFAULT 0 COMMENT '销量',
  `view_count` int DEFAULT 0 COMMENT '浏览量',
  `weight` decimal(8,3) DEFAULT NULL COMMENT '重量(kg)',
  `volume` decimal(8,3) DEFAULT NULL COMMENT '体积(m³)',
  `tags` json DEFAULT NULL COMMENT '商品标签',
  `attributes` json DEFAULT NULL COMMENT '商品属性',
  `is_virtual` tinyint DEFAULT 0 COMMENT '是否虚拟商品 0-否 1-是',
  `is_featured` tinyint DEFAULT 0 COMMENT '是否精选 0-否 1-是',
  `is_hot` tinyint DEFAULT 0 COMMENT '是否热销 0-否 1-是',
  `is_new` tinyint DEFAULT 0 COMMENT '是否新品 0-否 1-是',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-上架 2-下架 3-删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_no` (`product_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_price` (`price`),
  KEY `idx_sales_count` (`sales_count`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品基础信息表';
```

#### 3.3.3 商品SKU表 (product_skus)

```sql
CREATE TABLE `product_skus` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'SKU ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `sku_no` varchar(32) NOT NULL COMMENT 'SKU编号',
  `name` varchar(200) NOT NULL COMMENT 'SKU名称',
  `specifications` json NOT NULL COMMENT 'SKU规格',
  `price` decimal(10,2) NOT NULL COMMENT 'SKU价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价',
  `stock` int DEFAULT 0 COMMENT '库存数量',
  `sales_count` int DEFAULT 0 COMMENT '销量',
  `image` varchar(255) DEFAULT NULL COMMENT 'SKU图片',
  `barcode` varchar(50) DEFAULT NULL COMMENT '条形码',
  `weight` decimal(8,3) DEFAULT NULL COMMENT '重量(kg)',
  `volume` decimal(8,3) DEFAULT NULL COMMENT '体积(m³)',
  `status` tinyint DEFAULT 1 COMMENT '状态 1-正常 2-禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_no` (`sku_no`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品SKU表';
```

### 3.4 订单相关表

#### 3.4.1 订单主表 (orders)

```sql
CREATE TABLE `orders` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `merchant_id` bigint NOT NULL COMMENT '商家ID',
  `type` varchar(20) NOT NULL COMMENT '订单类型 product/service/carpool/house/secondhand',
  `status` varchar(20) NOT NULL COMMENT '订单状态',
  `payment_status` varchar(20) DEFAULT 'unpaid' COMMENT '支付状态',
  `delivery_status` varchar(20) DEFAULT 'undelivered' COMMENT '配送状态',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `product_amount` decimal(10,2) NOT NULL COMMENT '商品金额',
  `delivery_fee` decimal(10,2) DEFAULT 0.00 COMMENT '配送费',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '优惠金额',
  `coupon_amount` decimal(10,2) DEFAULT 0.00 COMMENT '优惠券金额',
  `points_amount` decimal(10,2) DEFAULT 0.00 COMMENT '积分抵扣金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `refund_amount` decimal(10,2) DEFAULT 0.00 COMMENT '退款金额',
  `commission_amount` decimal(10,2) DEFAULT 0.00 COMMENT '佣金金额',
  `delivery_type` varchar(20) DEFAULT 'delivery' COMMENT '配送方式 delivery/pickup/dine_in',
  `delivery_time` datetime DEFAULT NULL COMMENT '配送时间',
  `delivery_address` json DEFAULT NULL COMMENT '配送地址',
  `remark` varchar(500) DEFAULT NULL COMMENT '订单备注',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '取消原因',
  `refund_reason` varchar(200) DEFAULT NULL COMMENT '退款原因',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `delivery_time_actual` datetime DEFAULT NULL COMMENT '实际配送时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';
```

#### 3.4.2 订单商品表 (order_items)

```sql
CREATE TABLE `order_items` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单商品ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `sku_id` bigint DEFAULT NULL COMMENT 'SKU ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `sku_name` varchar(200) DEFAULT NULL COMMENT 'SKU名称',
  `product_image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `specifications` json DEFAULT NULL COMMENT '商品规格',
  `price` decimal(10,2) NOT NULL COMMENT '商品单价',
  `quantity` int NOT NULL COMMENT '购买数量',
  `total_amount` decimal(10,2) NOT NULL COMMENT '商品总金额',
  `refund_quantity` int DEFAULT 0 COMMENT '退款数量',
  `refund_amount` decimal(10,2) DEFAULT 0.00 COMMENT '退款金额',
  `commission_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '佣金比例',
  `commission_amount` decimal(10,2) DEFAULT 0.00 COMMENT '佣金金额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sku_id` (`sku_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表';
```

#### 3.4.3 订单状态变更记录表 (order_status_logs)

```sql
CREATE TABLE `order_status_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `from_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `to_status` varchar(20) NOT NULL COMMENT '新状态',
  `operator_type` varchar(20) NOT NULL COMMENT '操作者类型 user/merchant/system/admin',
  `operator_id` bigint DEFAULT NULL COMMENT '操作者ID',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单状态变更记录表';
```

### 3.5 支付相关表

#### 3.5.1 支付记录表 (payments)

```sql
CREATE TABLE `payments` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '支付ID',
  `payment_no` varchar(32) NOT NULL COMMENT '支付流水号',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式 wechat/alipay/balance/bank',
  `payment_channel` varchar(20) NOT NULL COMMENT '支付渠道 app/h5/jsapi/native',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `currency` varchar(10) DEFAULT 'CNY' COMMENT '货币类型',
  `status` varchar(20) DEFAULT 'pending' COMMENT '支付状态 pending/success/failed/cancelled',
  `third_party_no` varchar(64) DEFAULT NULL COMMENT '第三方支付流水号',
  `third_party_response` json DEFAULT NULL COMMENT '第三方响应数据',
  `notify_url` varchar(255) DEFAULT NULL COMMENT '异步通知地址',
  `return_url` varchar(255) DEFAULT NULL COMMENT '同步返回地址',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `expired_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';
```

#### 3.5.2 退款记录表 (refunds)

```sql
CREATE TABLE `refunds` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '退款ID',
  `refund_no` varchar(32) NOT NULL COMMENT '退款流水号',
  `payment_id` bigint NOT NULL COMMENT '支付ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `refund_reason` varchar(200) DEFAULT NULL COMMENT '退款原因',
  `status` varchar(20) DEFAULT 'pending' COMMENT '退款状态 pending/success/failed',
  `third_party_no` varchar(64) DEFAULT NULL COMMENT '第三方退款流水号',
  `third_party_response` json DEFAULT NULL COMMENT '第三方响应数据',
  `processed_at` datetime DEFAULT NULL COMMENT '处理时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `idx_payment_id` (`payment_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';
```

## 4. 用户管理模块

### 4.1 用户注册与认证

#### 4.1.1 用户注册API

```java
@RestController
@RequestMapping("/api/user")
@Api(tags = "用户管理")
public class UserController {

    @Autowired
    private UserService userService;

    @PostMapping("/register")
    @ApiOperation("用户注册")
    public Result<UserVO> register(@RequestBody @Valid UserRegisterDTO registerDTO) {
        UserVO user = userService.register(registerDTO);
        return Result.success(user);
    }

    @PostMapping("/login")
    @ApiOperation("用户登录")
    public Result<LoginVO> login(@RequestBody @Valid UserLoginDTO loginDTO) {
        LoginVO loginVO = userService.login(loginDTO);
        return Result.success(loginVO);
    }

    @PostMapping("/logout")
    @ApiOperation("用户登出")
    public Result<Void> logout(@RequestHeader("Authorization") String token) {
        userService.logout(token);
        return Result.success();
    }

    @PostMapping("/send-sms")
    @ApiOperation("发送短信验证码")
    public Result<Void> sendSms(@RequestBody @Valid SendSmsDTO sendSmsDTO) {
        userService.sendSms(sendSmsDTO);
        return Result.success();
    }

    @PostMapping("/verify-sms")
    @ApiOperation("验证短信验证码")
    public Result<Void> verifySms(@RequestBody @Valid VerifySmsDTO verifySmsDTO) {
        userService.verifySms(verifySmsDTO);
        return Result.success();
    }
}
```

#### 4.1.2 用户信息管理API

```java
@RestController
@RequestMapping("/api/user/profile")
@Api(tags = "用户信息管理")
public class UserProfileController {

    @Autowired
    private UserService userService;

    @GetMapping("/info")
    @ApiOperation("获取用户信息")
    public Result<UserVO> getUserInfo() {
        Long userId = SecurityUtils.getCurrentUserId();
        UserVO user = userService.getUserById(userId);
        return Result.success(user);
    }

    @PutMapping("/info")
    @ApiOperation("更新用户信息")
    public Result<UserVO> updateUserInfo(@RequestBody @Valid UserUpdateDTO updateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        UserVO user = userService.updateUser(userId, updateDTO);
        return Result.success(user);
    }

    @PostMapping("/avatar")
    @ApiOperation("上传头像")
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file) {
        Long userId = SecurityUtils.getCurrentUserId();
        String avatarUrl = userService.uploadAvatar(userId, file);
        return Result.success(avatarUrl);
    }

    @PostMapping("/change-password")
    @ApiOperation("修改密码")
    public Result<Void> changePassword(@RequestBody @Valid ChangePasswordDTO changePasswordDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        userService.changePassword(userId, changePasswordDTO);
        return Result.success();
    }

    @PostMapping("/bind-phone")
    @ApiOperation("绑定手机号")
    public Result<Void> bindPhone(@RequestBody @Valid BindPhoneDTO bindPhoneDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        userService.bindPhone(userId, bindPhoneDTO);
        return Result.success();
    }

    @PostMapping("/bind-email")
    @ApiOperation("绑定邮箱")
    public Result<Void> bindEmail(@RequestBody @Valid BindEmailDTO bindEmailDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        userService.bindEmail(userId, bindEmailDTO);
        return Result.success();
    }
}
```

#### 4.1.3 用户地址管理API

```java
@RestController
@RequestMapping("/api/user/address")
@Api(tags = "用户地址管理")
public class UserAddressController {

    @Autowired
    private UserAddressService userAddressService;

    @GetMapping("/list")
    @ApiOperation("获取用户地址列表")
    public Result<List<UserAddressVO>> getAddressList() {
        Long userId = SecurityUtils.getCurrentUserId();
        List<UserAddressVO> addresses = userAddressService.getAddressList(userId);
        return Result.success(addresses);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取地址详情")
    public Result<UserAddressVO> getAddress(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        UserAddressVO address = userAddressService.getAddress(userId, id);
        return Result.success(address);
    }

    @PostMapping
    @ApiOperation("添加地址")
    public Result<UserAddressVO> addAddress(@RequestBody @Valid UserAddressDTO addressDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        UserAddressVO address = userAddressService.addAddress(userId, addressDTO);
        return Result.success(address);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新地址")
    public Result<UserAddressVO> updateAddress(@PathVariable Long id, 
                                              @RequestBody @Valid UserAddressDTO addressDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        UserAddressVO address = userAddressService.updateAddress(userId, id, addressDTO);
        return Result.success(address);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除地址")
    public Result<Void> deleteAddress(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        userAddressService.deleteAddress(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/set-default")
    @ApiOperation("设置默认地址")
    public Result<Void> setDefaultAddress(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        userAddressService.setDefaultAddress(userId, id);
        return Result.success();
    }
}
```

#### 4.1.4 用户钱包管理API

```java
@RestController
@RequestMapping("/api/user/wallet")
@Api(tags = "用户钱包管理")
public class UserWalletController {

    @Autowired
    private UserWalletService userWalletService;

    @GetMapping("/info")
    @ApiOperation("获取钱包信息")
    public Result<UserWalletVO> getWalletInfo() {
        Long userId = SecurityUtils.getCurrentUserId();
        UserWalletVO wallet = userWalletService.getWalletInfo(userId);
        return Result.success(wallet);
    }

    @GetMapping("/transactions")
    @ApiOperation("获取钱包流水")
    public Result<PageResult<WalletTransactionVO>> getTransactions(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String type) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<WalletTransactionVO> transactions = 
            userWalletService.getTransactions(userId, page, size, type);
        return Result.success(transactions);
    }

    @PostMapping("/recharge")
    @ApiOperation("钱包充值")
    public Result<PaymentVO> recharge(@RequestBody @Valid WalletRechargeDTO rechargeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PaymentVO payment = userWalletService.recharge(userId, rechargeDTO);
        return Result.success(payment);
    }

    @PostMapping("/withdraw")
    @ApiOperation("钱包提现")
    public Result<WithdrawVO> withdraw(@RequestBody @Valid WalletWithdrawDTO withdrawDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        WithdrawVO withdraw = userWalletService.withdraw(userId, withdrawDTO);
        return Result.success(withdraw);
    }

    @PostMapping("/set-withdraw-password")
    @ApiOperation("设置提现密码")
    public Result<Void> setWithdrawPassword(@RequestBody @Valid SetWithdrawPasswordDTO passwordDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        userWalletService.setWithdrawPassword(userId, passwordDTO);
        return Result.success();
    }

    @PostMapping("/change-withdraw-password")
    @ApiOperation("修改提现密码")
    public Result<Void> changeWithdrawPassword(@RequestBody @Valid ChangeWithdrawPasswordDTO passwordDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        userWalletService.changeWithdrawPassword(userId, passwordDTO);
        return Result.success();
    }
}
```

### 4.2 用户服务实现

#### 4.2.1 用户服务接口

```java
@Service
@Transactional
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private UserAuthMapper userAuthMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private SmsService smsService;
    
    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Override
    public UserVO register(UserRegisterDTO registerDTO) {
        // 1. 验证手机号是否已注册
        if (userMapper.existsByPhone(registerDTO.getPhone())) {
            throw new BusinessException("手机号已注册");
        }

        // 2. 验证短信验证码
        String cacheKey = "sms:register:" + registerDTO.getPhone();
        String cachedCode = (String) redisTemplate.opsForValue().get(cacheKey);
        if (!registerDTO.getSmsCode().equals(cachedCode)) {
            throw new BusinessException("验证码错误或已过期");
        }

        // 3. 创建用户
        User user = new User();
        user.setPhone(registerDTO.getPhone());
        user.setNickname(generateNickname());
        user.setInviteCode(generateInviteCode());
        if (StringUtils.hasText(registerDTO.getInviteCode())) {
            User inviter = userMapper.findByInviteCode(registerDTO.getInviteCode());
            if (inviter != null) {
                user.setInviterId(inviter.getId());
            }
        }
        userMapper.insert(user);

        // 4. 创建认证信息
        UserAuth userAuth = new UserAuth();
        userAuth.setUserId(user.getId());
        userAuth.setAuthType("password");
        userAuth.setAuthKey(registerDTO.getPhone());
        userAuth.setAuthSecret(passwordEncoder.encode(registerDTO.getPassword()));
        userAuthMapper.insert(userAuth);

        // 5. 创建钱包
        createUserWallet(user.getId());

        // 6. 删除验证码缓存
        redisTemplate.delete(cacheKey);

        return convertToVO(user);
    }

    @Override
    public LoginVO login(UserLoginDTO loginDTO) {
        // 1. 查找用户
        User user = userMapper.findByPhone(loginDTO.getPhone());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 2. 验证密码
        UserAuth userAuth = userAuthMapper.findByUserIdAndAuthType(user.getId(), "password");
        if (userAuth == null || !passwordEncoder.matches(loginDTO.getPassword(), userAuth.getAuthSecret())) {
            throw new BusinessException("密码错误");
        }

        // 3. 检查用户状态
        if (user.getStatus() != 1) {
            throw new BusinessException("用户已被禁用");
        }

        // 4. 生成token
        String token = jwtTokenUtil.generateToken(user.getId());

        // 5. 更新登录信息
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(getClientIp());
        userMapper.updateById(user);

        // 6. 构建返回结果
        LoginVO loginVO = new LoginVO();
        loginVO.setToken(token);
        loginVO.setUser(convertToVO(user));
        return loginVO;
    }

    @Override
    public void logout(String token) {
        // 将token加入黑名单
        String tokenKey = "token:blacklist:" + token;
        redisTemplate.opsForValue().set(tokenKey, "1", Duration.ofDays(7));
    }

    @Override
    public void sendSms(SendSmsDTO sendSmsDTO) {
        String phone = sendSmsDTO.getPhone();
        String type = sendSmsDTO.getType();

        // 1. 验证发送频率
        String rateLimitKey = "sms:rate:" + phone;
        String lastSendTime = (String) redisTemplate.opsForValue().get(rateLimitKey);
        if (lastSendTime != null) {
            throw new BusinessException("发送过于频繁，请稍后再试");
        }

        // 2. 生成验证码
        String code = generateSmsCode();

        // 3. 发送短信
        smsService.sendVerificationCode(phone, code);

        // 4. 缓存验证码
        String cacheKey = "sms:" + type + ":" + phone;
        redisTemplate.opsForValue().set(cacheKey, code, Duration.ofMinutes(5));

        // 5. 设置发送频率限制
        redisTemplate.opsForValue().set(rateLimitKey, String.valueOf(System.currentTimeMillis()), Duration.ofMinutes(1));
    }

    @Override
    public UserVO updateUser(Long userId, UserUpdateDTO updateDTO) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 更新用户信息
        BeanUtils.copyProperties(updateDTO, user);
        user.setUpdatedAt(LocalDateTime.now());
        userMapper.updateById(user);

        return convertToVO(user);
    }

    @Override
    public String uploadAvatar(Long userId, MultipartFile file) {
        // 1. 验证文件
        if (file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        String contentType = file.getContentType();
        if (!contentType.startsWith("image/")) {
            throw new BusinessException("只能上传图片文件");
        }

        // 2. 上传文件
        String avatarUrl = fileService.uploadImage(file, "avatar");

        // 3. 更新用户头像
        User user = new User();
        user.setId(userId);
        user.setAvatar(avatarUrl);
        user.setUpdatedAt(LocalDateTime.now());
        userMapper.updateById(user);

        return avatarUrl;
    }

    private String generateNickname() {
        return "用户" + System.currentTimeMillis();
    }

    private String generateInviteCode() {
        return RandomStringUtils.randomAlphanumeric(8).toUpperCase();
    }

    private String generateSmsCode() {
        return RandomStringUtils.randomNumeric(6);
    }

    private void createUserWallet(Long userId) {
        UserWallet wallet = new UserWallet();
        wallet.setUserId(userId);
        wallet.setBalance(BigDecimal.ZERO);
        wallet.setFrozenBalance(BigDecimal.ZERO);
        wallet.setCommission(BigDecimal.ZERO);
        wallet.setFrozenCommission(BigDecimal.ZERO);
        wallet.setTotalIncome(BigDecimal.ZERO);
        wallet.setTotalExpense(BigDecimal.ZERO);
        userWalletMapper.insert(wallet);
    }
}
```

## 5. 商家管理模块

### 5.1 商家入驻与认证

#### 5.1.1 商家入驻API

```java
@RestController
@RequestMapping("/api/merchant")
@Api(tags = "商家管理")
public class MerchantController {

    @Autowired
    private MerchantService merchantService;

    @PostMapping("/apply")
    @ApiOperation("商家入驻申请")
    public Result<MerchantVO> apply(@RequestBody @Valid MerchantApplyDTO applyDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantVO merchant = merchantService.apply(userId, applyDTO);
        return Result.success(merchant);
    }

    @GetMapping("/info")
    @ApiOperation("获取商家信息")
    public Result<MerchantVO> getMerchantInfo() {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantVO merchant = merchantService.getMerchantByUserId(userId);
        return Result.success(merchant);
    }

    @PutMapping("/info")
    @ApiOperation("更新商家信息")
    public Result<MerchantVO> updateMerchantInfo(@RequestBody @Valid MerchantUpdateDTO updateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantVO merchant = merchantService.updateMerchant(userId, updateDTO);
        return Result.success(merchant);
    }

    @PostMapping("/upload-license")
    @ApiOperation("上传营业执照")
    public Result<String> uploadLicense(@RequestParam("file") MultipartFile file) {
        Long userId = SecurityUtils.getCurrentUserId();
        String licenseUrl = merchantService.uploadLicense(userId, file);
        return Result.success(licenseUrl);
    }

    @PostMapping("/upload-id-card")
    @ApiOperation("上传身份证")
    public Result<Map<String, String>> uploadIdCard(@RequestParam("front") MultipartFile front,
                                                    @RequestParam("back") MultipartFile back) {
        Long userId = SecurityUtils.getCurrentUserId();
        Map<String, String> idCardUrls = merchantService.uploadIdCard(userId, front, back);
        return Result.success(idCardUrls);
    }

    @PostMapping("/submit-audit")
    @ApiOperation("提交审核")
    public Result<Void> submitAudit() {
        Long userId = SecurityUtils.getCurrentUserId();
        merchantService.submitAudit(userId);
        return Result.success();
    }

    @GetMapping("/categories")
    @ApiOperation("获取商家分类列表")
    public Result<List<MerchantCategoryVO>> getCategories() {
        List<MerchantCategoryVO> categories = merchantService.getCategories();
        return Result.success(categories);
    }
}
```

#### 5.1.2 商家分类管理API

```java
@RestController
@RequestMapping("/api/merchant/category")
@Api(tags = "商家分类管理")
public class MerchantCategoryController {

    @Autowired
    private MerchantCategoryService merchantCategoryService;

    @GetMapping("/list")
    @ApiOperation("获取分类列表")
    public Result<List<MerchantCategoryVO>> getCategoryList(
            @RequestParam(required = false) Long parentId) {
        List<MerchantCategoryVO> categories = merchantCategoryService.getCategoryList(parentId);
        return Result.success(categories);
    }

    @GetMapping("/tree")
    @ApiOperation("获取分类树")
    public Result<List<MerchantCategoryTreeVO>> getCategoryTree() {
        List<MerchantCategoryTreeVO> categoryTree = merchantCategoryService.getCategoryTree();
        return Result.success(categoryTree);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取分类详情")
    public Result<MerchantCategoryVO> getCategory(@PathVariable Long id) {
        MerchantCategoryVO category = merchantCategoryService.getCategoryById(id);
        return Result.success(category);
    }

    @PostMapping
    @ApiOperation("添加分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<MerchantCategoryVO> addCategory(@RequestBody @Valid MerchantCategoryDTO categoryDTO) {
        MerchantCategoryVO category = merchantCategoryService.addCategory(categoryDTO);
        return Result.success(category);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<MerchantCategoryVO> updateCategory(@PathVariable Long id,
                                                    @RequestBody @Valid MerchantCategoryDTO categoryDTO) {
        MerchantCategoryVO category = merchantCategoryService.updateCategory(id, categoryDTO);
        return Result.success(category);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> deleteCategory(@PathVariable Long id) {
        merchantCategoryService.deleteCategory(id);
        return Result.success();
    }

    @PostMapping("/{id}/enable")
    @ApiOperation("启用分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> enableCategory(@PathVariable Long id) {
        merchantCategoryService.enableCategory(id);
        return Result.success();
    }

    @PostMapping("/{id}/disable")
    @ApiOperation("禁用分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> disableCategory(@PathVariable Long id) {
        merchantCategoryService.disableCategory(id);
        return Result.success();
    }
}
```

#### 5.1.3 商家银行账户管理API

```java
@RestController
@RequestMapping("/api/merchant/bank-account")
@Api(tags = "商家银行账户管理")
public class MerchantBankAccountController {

    @Autowired
    private MerchantBankAccountService bankAccountService;
````markdown path=docs/磁州生活网完整后端系统设计.md mode=EDIT
    @GetMapping("/list")
    @ApiOperation("获取银行账户列表")
    public Result<List<MerchantBankAccountVO>> getBankAccountList() {
        Long userId = SecurityUtils.getCurrentUserId();
        List<MerchantBankAccountVO> accounts = bankAccountService.getBankAccountList(userId);
        return Result.success(accounts);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取银行账户详情")
    public Result<MerchantBankAccountVO> getBankAccount(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantBankAccountVO account = bankAccountService.getBankAccount(userId, id);
        return Result.success(account);
    }

    @PostMapping
    @ApiOperation("添加银行账户")
    public Result<MerchantBankAccountVO> addBankAccount(@RequestBody @Valid MerchantBankAccountDTO accountDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantBankAccountVO account = bankAccountService.addBankAccount(userId, accountDTO);
        return Result.success(account);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新银行账户")
    public Result<MerchantBankAccountVO> updateBankAccount(@PathVariable Long id,
                                                          @RequestBody @Valid MerchantBankAccountDTO accountDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantBankAccountVO account = bankAccountService.updateBankAccount(userId, id, accountDTO);
        return Result.success(account);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除银行账户")
    public Result<Void> deleteBankAccount(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        bankAccountService.deleteBankAccount(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/set-default")
    @ApiOperation("设置默认银行账户")
    public Result<Void> setDefaultBankAccount(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        bankAccountService.setDefaultBankAccount(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/verify")
    @ApiOperation("验证银行账户")
    public Result<Void> verifyBankAccount(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        bankAccountService.verifyBankAccount(userId, id);
        return Result.success();
    }
}
````

### 5.2 商家店铺管理

#### 5.2.1 店铺基础信息管理API

```java
@RestController
@RequestMapping("/api/merchant/shop")
@Api(tags = "商家店铺管理")
public class MerchantShopController {

    @Autowired
    private MerchantShopService shopService;

    @GetMapping("/info")
    @ApiOperation("获取店铺信息")
    public Result<MerchantShopVO> getShopInfo() {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantShopVO shop = shopService.getShopByUserId(userId);
        return Result.success(shop);
    }

    @PutMapping("/info")
    @ApiOperation("更新店铺信息")
    public Result<MerchantShopVO> updateShopInfo(@RequestBody @Valid MerchantShopUpdateDTO updateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantShopVO shop = shopService.updateShop(userId, updateDTO);
        return Result.success(shop);
    }

    @PostMapping("/upload-logo")
    @ApiOperation("上传店铺Logo")
    public Result<String> uploadLogo(@RequestParam("file") MultipartFile file) {
        Long userId = SecurityUtils.getCurrentUserId();
        String logoUrl = shopService.uploadLogo(userId, file);
        return Result.success(logoUrl);
    }

    @PostMapping("/upload-banner")
    @ApiOperation("上传店铺横幅")
    public Result<String> uploadBanner(@RequestParam("file") MultipartFile file) {
        Long userId = SecurityUtils.getCurrentUserId();
        String bannerUrl = shopService.uploadBanner(userId, file);
        return Result.success(bannerUrl);
    }

    @PostMapping("/upload-images")
    @ApiOperation("上传店铺图片")
    public Result<List<String>> uploadImages(@RequestParam("files") MultipartFile[] files) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<String> imageUrls = shopService.uploadImages(userId, files);
        return Result.success(imageUrls);
    }

    @GetMapping("/business-hours")
    @ApiOperation("获取营业时间")
    public Result<List<BusinessHoursVO>> getBusinessHours() {
        Long userId = SecurityUtils.getCurrentUserId();
        List<BusinessHoursVO> businessHours = shopService.getBusinessHours(userId);
        return Result.success(businessHours);
    }

    @PutMapping("/business-hours")
    @ApiOperation("设置营业时间")
    public Result<Void> setBusinessHours(@RequestBody @Valid List<BusinessHoursDTO> businessHoursDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        shopService.setBusinessHours(userId, businessHoursDTO);
        return Result.success();
    }

    @PostMapping("/open")
    @ApiOperation("开启店铺")
    public Result<Void> openShop() {
        Long userId = SecurityUtils.getCurrentUserId();
        shopService.openShop(userId);
        return Result.success();
    }

    @PostMapping("/close")
    @ApiOperation("关闭店铺")
    public Result<Void> closeShop(@RequestBody @Valid ShopCloseDTO closeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        shopService.closeShop(userId, closeDTO);
        return Result.success();
    }
}
```

#### 5.2.2 店铺装修管理API

```java
@RestController
@RequestMapping("/api/merchant/shop/decoration")
@Api(tags = "店铺装修管理")
public class ShopDecorationController {

    @Autowired
    private ShopDecorationService decorationService;

    @GetMapping("/template/list")
    @ApiOperation("获取装修模板列表")
    public Result<List<DecorationTemplateVO>> getTemplateList() {
        List<DecorationTemplateVO> templates = decorationService.getTemplateList();
        return Result.success(templates);
    }

    @GetMapping("/template/{id}")
    @ApiOperation("获取装修模板详情")
    public Result<DecorationTemplateVO> getTemplate(@PathVariable Long id) {
        DecorationTemplateVO template = decorationService.getTemplate(id);
        return Result.success(template);
    }

    @GetMapping("/config")
    @ApiOperation("获取店铺装修配置")
    public Result<ShopDecorationVO> getDecorationConfig() {
        Long userId = SecurityUtils.getCurrentUserId();
        ShopDecorationVO decoration = decorationService.getDecorationConfig(userId);
        return Result.success(decoration);
    }

    @PutMapping("/config")
    @ApiOperation("保存店铺装修配置")
    public Result<Void> saveDecorationConfig(@RequestBody @Valid ShopDecorationDTO decorationDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        decorationService.saveDecorationConfig(userId, decorationDTO);
        return Result.success();
    }

    @PostMapping("/apply-template/{templateId}")
    @ApiOperation("应用装修模板")
    public Result<Void> applyTemplate(@PathVariable Long templateId) {
        Long userId = SecurityUtils.getCurrentUserId();
        decorationService.applyTemplate(userId, templateId);
        return Result.success();
    }

    @PostMapping("/preview")
    @ApiOperation("预览装修效果")
    public Result<String> previewDecoration(@RequestBody @Valid ShopDecorationDTO decorationDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        String previewUrl = decorationService.previewDecoration(userId, decorationDTO);
        return Result.success(previewUrl);
    }

    @PostMapping("/publish")
    @ApiOperation("发布装修")
    public Result<Void> publishDecoration() {
        Long userId = SecurityUtils.getCurrentUserId();
        decorationService.publishDecoration(userId);
        return Result.success();
    }
}
```

### 5.3 商家员工管理

#### 5.3.1 员工管理API

```java
@RestController
@RequestMapping("/api/merchant/staff")
@Api(tags = "商家员工管理")
public class MerchantStaffController {

    @Autowired
    private MerchantStaffService staffService;

    @GetMapping("/list")
    @ApiOperation("获取员工列表")
    public Result<PageResult<MerchantStaffVO>> getStaffList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<MerchantStaffVO> staffList = staffService.getStaffList(userId, page, size, keyword, status);
        return Result.success(staffList);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取员工详情")
    public Result<MerchantStaffVO> getStaff(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantStaffVO staff = staffService.getStaff(userId, id);
        return Result.success(staff);
    }

    @PostMapping
    @ApiOperation("添加员工")
    public Result<MerchantStaffVO> addStaff(@RequestBody @Valid MerchantStaffDTO staffDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantStaffVO staff = staffService.addStaff(userId, staffDTO);
        return Result.success(staff);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新员工信息")
    public Result<MerchantStaffVO> updateStaff(@PathVariable Long id,
                                              @RequestBody @Valid MerchantStaffDTO staffDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantStaffVO staff = staffService.updateStaff(userId, id, staffDTO);
        return Result.success(staff);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除员工")
    public Result<Void> deleteStaff(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        staffService.deleteStaff(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/enable")
    @ApiOperation("启用员工")
    public Result<Void> enableStaff(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        staffService.enableStaff(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/disable")
    @ApiOperation("禁用员工")
    public Result<Void> disableStaff(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        staffService.disableStaff(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/reset-password")
    @ApiOperation("重置员工密码")
    public Result<String> resetPassword(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        String newPassword = staffService.resetPassword(userId, id);
        return Result.success(newPassword);
    }

    @GetMapping("/roles")
    @ApiOperation("获取员工角色列表")
    public Result<List<StaffRoleVO>> getStaffRoles() {
        List<StaffRoleVO> roles = staffService.getStaffRoles();
        return Result.success(roles);
    }

    @PostMapping("/{id}/assign-role")
    @ApiOperation("分配员工角色")
    public Result<Void> assignRole(@PathVariable Long id, @RequestBody @Valid AssignRoleDTO assignRoleDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        staffService.assignRole(userId, id, assignRoleDTO);
        return Result.success();
    }
}
```

#### 5.3.2 员工权限管理API

```java
@RestController
@RequestMapping("/api/merchant/staff/permission")
@Api(tags = "员工权限管理")
public class StaffPermissionController {

    @Autowired
    private StaffPermissionService permissionService;

    @GetMapping("/list")
    @ApiOperation("获取权限列表")
    public Result<List<PermissionVO>> getPermissionList() {
        List<PermissionVO> permissions = permissionService.getPermissionList();
        return Result.success(permissions);
    }

    @GetMapping("/tree")
    @ApiOperation("获取权限树")
    public Result<List<PermissionTreeVO>> getPermissionTree() {
        List<PermissionTreeVO> permissionTree = permissionService.getPermissionTree();
        return Result.success(permissionTree);
    }

    @GetMapping("/staff/{staffId}")
    @ApiOperation("获取员工权限")
    public Result<List<PermissionVO>> getStaffPermissions(@PathVariable Long staffId) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<PermissionVO> permissions = permissionService.getStaffPermissions(userId, staffId);
        return Result.success(permissions);
    }

    @PostMapping("/staff/{staffId}/assign")
    @ApiOperation("分配员工权限")
    public Result<Void> assignPermissions(@PathVariable Long staffId,
                                         @RequestBody @Valid AssignPermissionsDTO assignDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        permissionService.assignPermissions(userId, staffId, assignDTO);
        return Result.success();
    }

    @DeleteMapping("/staff/{staffId}/revoke")
    @ApiOperation("撤销员工权限")
    public Result<Void> revokePermissions(@PathVariable Long staffId,
                                         @RequestBody @Valid RevokePermissionsDTO revokeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        permissionService.revokePermissions(userId, staffId, revokeDTO);
        return Result.success();
    }

    @GetMapping("/role/{roleId}")
    @ApiOperation("获取角色权限")
    public Result<List<PermissionVO>> getRolePermissions(@PathVariable Long roleId) {
        List<PermissionVO> permissions = permissionService.getRolePermissions(roleId);
        return Result.success(permissions);
    }

    @PostMapping("/role/{roleId}/assign")
    @ApiOperation("分配角色权限")
    public Result<Void> assignRolePermissions(@PathVariable Long roleId,
                                             @RequestBody @Valid AssignPermissionsDTO assignDTO) {
        permissionService.assignRolePermissions(roleId, assignDTO);
        return Result.success();
    }
}
```

## 6. 商品管理模块

### 6.1 商品分类管理

#### 6.1.1 商品分类API

```java
@RestController
@RequestMapping("/api/product/category")
@Api(tags = "商品分类管理")
public class ProductCategoryController {

    @Autowired
    private ProductCategoryService categoryService;

    @GetMapping("/list")
    @ApiOperation("获取分类列表")
    public Result<List<ProductCategoryVO>> getCategoryList(
            @RequestParam(required = false) Long parentId,
            @RequestParam(required = false) Integer status) {
        List<ProductCategoryVO> categories = categoryService.getCategoryList(parentId, status);
        return Result.success(categories);
    }

    @GetMapping("/tree")
    @ApiOperation("获取分类树")
    public Result<List<ProductCategoryTreeVO>> getCategoryTree() {
        List<ProductCategoryTreeVO> categoryTree = categoryService.getCategoryTree();
        return Result.success(categoryTree);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取分类详情")
    public Result<ProductCategoryVO> getCategory(@PathVariable Long id) {
        ProductCategoryVO category = categoryService.getCategoryById(id);
        return Result.success(category);
    }

    @PostMapping
    @ApiOperation("添加分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<ProductCategoryVO> addCategory(@RequestBody @Valid ProductCategoryDTO categoryDTO) {
        ProductCategoryVO category = categoryService.addCategory(categoryDTO);
        return Result.success(category);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<ProductCategoryVO> updateCategory(@PathVariable Long id,
                                                   @RequestBody @Valid ProductCategoryDTO categoryDTO) {
        ProductCategoryVO category = categoryService.updateCategory(id, categoryDTO);
        return Result.success(category);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> deleteCategory(@PathVariable Long id) {
        categoryService.deleteCategory(id);
        return Result.success();
    }

    @PostMapping("/{id}/enable")
    @ApiOperation("启用分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> enableCategory(@PathVariable Long id) {
        categoryService.enableCategory(id);
        return Result.success();
    }

    @PostMapping("/{id}/disable")
    @ApiOperation("禁用分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> disableCategory(@PathVariable Long id) {
        categoryService.disableCategory(id);
        return Result.success();
    }

    @PostMapping("/batch-sort")
    @ApiOperation("批量排序")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> batchSort(@RequestBody @Valid List<CategorySortDTO> sortList) {
        categoryService.batchSort(sortList);
        return Result.success();
    }
}
```

### 6.2 商品基础管理

#### 6.2.1 商品管理API

```java
@RestController
@RequestMapping("/api/product")
@Api(tags = "商品管理")
public class ProductController {

    @Autowired
    private ProductService productService;

    @GetMapping("/list")
    @ApiOperation("获取商品列表")
    public Result<PageResult<ProductVO>> getProductList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortOrder) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<ProductVO> products = productService.getProductList(
            userId, page, size, categoryId, keyword, status, sortBy, sortOrder);
        return Result.success(products);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取商品详情")
    public Result<ProductDetailVO> getProduct(@PathVariable Long id) {
        ProductDetailVO product = productService.getProductById(id);
        return Result.success(product);
    }

    @PostMapping
    @ApiOperation("添加商品")
    public Result<ProductVO> addProduct(@RequestBody @Valid ProductDTO productDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductVO product = productService.addProduct(userId, productDTO);
        return Result.success(product);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新商品")
    public Result<ProductVO> updateProduct(@PathVariable Long id,
                                          @RequestBody @Valid ProductDTO productDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductVO product = productService.updateProduct(userId, id, productDTO);
        return Result.success(product);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除商品")
    public Result<Void> deleteProduct(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.deleteProduct(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/publish")
    @ApiOperation("上架商品")
    public Result<Void> publishProduct(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.publishProduct(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/unpublish")
    @ApiOperation("下架商品")
    public Result<Void> unpublishProduct(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.unpublishProduct(userId, id);
        return Result.success();
    }

    @PostMapping("/batch-publish")
    @ApiOperation("批量上架")
    public Result<Void> batchPublish(@RequestBody @Valid BatchOperationDTO batchDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.batchPublish(userId, batchDTO.getIds());
        return Result.success();
    }

    @PostMapping("/batch-unpublish")
    @ApiOperation("批量下架")
    public Result<Void> batchUnpublish(@RequestBody @Valid BatchOperationDTO batchDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.batchUnpublish(userId, batchDTO.getIds());
        return Result.success();
    }

    @PostMapping("/batch-delete")
    @ApiOperation("批量删除")
    public Result<Void> batchDelete(@RequestBody @Valid BatchOperationDTO batchDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.batchDelete(userId, batchDTO.getIds());
        return Result.success();
    }

    @PostMapping("/upload-images")
    @ApiOperation("上传商品图片")
    public Result<List<String>> uploadImages(@RequestParam("files") MultipartFile[] files) {
        List<String> imageUrls = productService.uploadImages(files);
        return Result.success(imageUrls);
    }

    @PostMapping("/upload-video")
    @ApiOperation("上传商品视频")
    public Result<String> uploadVideo(@RequestParam("file") MultipartFile file) {
        String videoUrl = productService.uploadVideo(file);
        return Result.success(videoUrl);
    }
}
```

#### 6.2.2 商品SKU管理API

```java
@RestController
@RequestMapping("/api/product/sku")
@Api(tags = "商品SKU管理")
public class ProductSkuController {

    @Autowired
    private ProductSkuService skuService;

    @GetMapping("/list/{productId}")
    @ApiOperation("获取商品SKU列表")
    public Result<List<ProductSkuVO>> getSkuList(@PathVariable Long productId) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<ProductSkuVO> skus = skuService.getSkuList(userId, productId);
        return Result.success(skus);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取SKU详情")
    public Result<ProductSkuVO> getSku(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductSkuVO sku = skuService.getSkuById(userId, id);
        return Result.success(sku);
    }

    @PostMapping
    @ApiOperation("添加SKU")
    public Result<ProductSkuVO> addSku(@RequestBody @Valid ProductSkuDTO skuDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductSkuVO sku = skuService.addSku(userId, skuDTO);
        return Result.success(sku);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新SKU")
    public Result<ProductSkuVO> updateSku(@PathVariable Long id,
                                         @RequestBody @Valid ProductSkuDTO skuDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductSkuVO sku = skuService.updateSku(userId, id, skuDTO);
        return Result.success(sku);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除SKU")
    public Result<Void> deleteSku(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.deleteSku(userId, id);
        return Result.success();
    }

    @PostMapping("/batch-update-stock")
    @ApiOperation("批量更新库存")
    public Result<Void> batchUpdateStock(@RequestBody @Valid List<SkuStockUpdateDTO> stockUpdates) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.batchUpdateStock(userId, stockUpdates);
        return Result.success();
    }

    @PostMapping("/batch-update-price")
    @ApiOperation("批量更新价格")
    public Result<Void> batchUpdatePrice(@RequestBody @Valid List<SkuPriceUpdateDTO> priceUpdates) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.batchUpdatePrice(userId, priceUpdates);
        return Result.success();
    }

    @PostMapping("/{id}/enable")
    @ApiOperation("启用SKU")
    public Result<Void> enableSku(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.enableSku(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/disable")
    @ApiOperation("禁用SKU")
    public Result<Void> disableSku(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.disableSku(userId, id);
        return Result.success();
    }
}
```

### 6.3 商品库存管理

#### 6.3.1 库存管理API

```java
@RestController
@RequestMapping("/api/product/stock")
@Api(tags = "商品库存管理")
public class ProductStockController {

    @Autowired
    private ProductStockService stockService;

    @GetMapping("/list")
    @ApiOperation("获取库存列表")
    public Result<PageResult<ProductStockVO>> getStockList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer stockStatus) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<ProductStockVO> stocks = stockService.getStockList(userId, page, size, keyword, stockStatus);
        return Result.success(stocks);
    }

    @GetMapping("/{productId}")
    @ApiOperation("获取商品库存详情")
    public Result<ProductStockDetailVO> getProductStock(@PathVariable Long productId) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductStockDetailVO stock = stockService.getProductStock(userId, productId);
        return Result.success(stock);
    }

    @PostMapping("/adjust")
    @ApiOperation("库存调整")
    public Result<Void> adjustStock(@RequestBody @Valid StockAdjustDTO adjustDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        stockService.adjustStock(userId, adjustDTO);
        return Result.success();
    }

    @PostMapping("/batch-adjust")
    @ApiOperation("批量库存调整")
    public Result<Void> batchAdjustStock(@RequestBody @Valid List<StockAdjustDTO> adjustList) {
        Long userId = SecurityUtils.getCurrentUserId();
        stockService.batchAdjustStock(userId, adjustList);
        return Result.success();
    }

    @GetMapping("/logs")
    @ApiOperation("获取库存变动日志")
    public Result<PageResult<StockLogVO>> getStockLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long productId,
            @RequestParam(required = false) Long skuId,
            @RequestParam(required = false) String operationType) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<StockLogVO> logs = stockService.getStockLogs(userId, page, size, productId, skuId, operationType);
        return Result.success(logs);
    }

    @PostMapping("/alert/settings")
    @ApiOperation("设置库存预警")
    public Result<Void> setStockAlert(@RequestBody @Valid StockAlertSettingsDTO alertSettings) {
        Long userId = SecurityUtils.getCurrentUserId();
        stockService.setStockAlert(userId, alertSettings);
        return Result.success();
    }

    @GetMapping("/alert/list")
    @ApiOperation("获取库存预警列表")
    public Result<PageResult<StockAlertVO>> getStockAlerts(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<StockAlertVO> alerts = stockService.getStockAlerts(userId, page, size);
        return Result.success(alerts);
    }

    @PostMapping("/import")
    @ApiOperation("导入库存")
    public Result<ImportResultVO> importStock(@RequestParam("file") MultipartFile file) {
        Long userId = SecurityUtils.getCurrentUserId();
        ImportResultVO result = stockService.importStock(userId, file);
        return Result.success(result);
    }

    @GetMapping("/export")
    @ApiOperation("导出库存")
    public void exportStock(HttpServletResponse response,
                           @RequestParam(required = false) String keyword,
                           @RequestParam(required = false) Integer stockStatus) {
        Long userId = SecurityUtils.getCurrentUserId();
        stockService.exportStock(response, userId, keyword, stockStatus);
    }
}
```

## 7. 订单管理模块

### 7.1 订单基础管理

#### 7.1.1 订单管理API

```java
@RestController
@RequestMapping("/api/order")
@Api(tags = "订单管理")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @GetMapping("/list")
    @ApiOperation("获取订单列表")
    public Result<PageResult<OrderVO>> getOrderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String paymentStatus,
            @RequestParam(required = false) String deliveryStatus,
            @Request
````markdown path=docs/磁州生活网完整后端系统设计.md mode=EDIT
    @PostMapping("/apply-template/{templateId}")
    @ApiOperation("应用装修模板")
    public Result<Void> applyTemplate(@PathVariable Long templateId) {
        Long userId = SecurityUtils.getCurrentUserId();
        decorationService.applyTemplate(userId, templateId);
        return Result.success();
    }

    @PostMapping("/preview")
    @ApiOperation("预览装修效果")
    public Result<String> previewDecoration(@RequestBody @Valid ShopDecorationDTO decorationDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        String previewUrl = decorationService.previewDecoration(userId, decorationDTO);
        return Result.success(previewUrl);
    }

    @PostMapping("/publish")
    @ApiOperation("发布装修")
    public Result<Void> publishDecoration() {
        Long userId = SecurityUtils.getCurrentUserId();
        decorationService.publishDecoration(userId);
        return Result.success();
    }
}
```

### 5.3 商家员工管理

#### 5.3.1 员工管理API

```java
@RestController
@RequestMapping("/api/merchant/staff")
@Api(tags = "商家员工管理")
public class MerchantStaffController {

    @Autowired
    private MerchantStaffService staffService;

    @GetMapping("/list")
    @ApiOperation("获取员工列表")
    public Result<PageResult<MerchantStaffVO>> getStaffList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<MerchantStaffVO> staffList = staffService.getStaffList(userId, page, size, keyword, status);
        return Result.success(staffList);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取员工详情")
    public Result<MerchantStaffVO> getStaff(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantStaffVO staff = staffService.getStaff(userId, id);
        return Result.success(staff);
    }

    @PostMapping
    @ApiOperation("添加员工")
    public Result<MerchantStaffVO> addStaff(@RequestBody @Valid MerchantStaffDTO staffDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantStaffVO staff = staffService.addStaff(userId, staffDTO);
        return Result.success(staff);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新员工信息")
    public Result<MerchantStaffVO> updateStaff(@PathVariable Long id,
                                              @RequestBody @Valid MerchantStaffDTO staffDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MerchantStaffVO staff = staffService.updateStaff(userId, id, staffDTO);
        return Result.success(staff);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除员工")
    public Result<Void> deleteStaff(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        staffService.deleteStaff(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/enable")
    @ApiOperation("启用员工")
    public Result<Void> enableStaff(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        staffService.enableStaff(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/disable")
    @ApiOperation("禁用员工")
    public Result<Void> disableStaff(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        staffService.disableStaff(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/reset-password")
    @ApiOperation("重置员工密码")
    public Result<String> resetPassword(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        String newPassword = staffService.resetPassword(userId, id);
        return Result.success(newPassword);
    }

    @GetMapping("/roles")
    @ApiOperation("获取员工角色列表")
    public Result<List<StaffRoleVO>> getStaffRoles() {
        List<StaffRoleVO> roles = staffService.getStaffRoles();
        return Result.success(roles);
    }

    @PostMapping("/{id}/assign-role")
    @ApiOperation("分配员工角色")
    public Result<Void> assignRole(@PathVariable Long id, @RequestBody @Valid AssignRoleDTO assignRoleDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        staffService.assignRole(userId, id, assignRoleDTO);
        return Result.success();
    }
}
```

#### 5.3.2 员工权限管理API

```java
@RestController
@RequestMapping("/api/merchant/staff/permission")
@Api(tags = "员工权限管理")
public class StaffPermissionController {

    @Autowired
    private StaffPermissionService permissionService;

    @GetMapping("/list")
    @ApiOperation("获取权限列表")
    public Result<List<PermissionVO>> getPermissionList() {
        List<PermissionVO> permissions = permissionService.getPermissionList();
        return Result.success(permissions);
    }

    @GetMapping("/tree")
    @ApiOperation("获取权限树")
    public Result<List<PermissionTreeVO>> getPermissionTree() {
        List<PermissionTreeVO> permissionTree = permissionService.getPermissionTree();
        return Result.success(permissionTree);
    }

    @GetMapping("/staff/{staffId}")
    @ApiOperation("获取员工权限")
    public Result<List<PermissionVO>> getStaffPermissions(@PathVariable Long staffId) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<PermissionVO> permissions = permissionService.getStaffPermissions(userId, staffId);
        return Result.success(permissions);
    }

    @PostMapping("/staff/{staffId}/assign")
    @ApiOperation("分配员工权限")
    public Result<Void> assignPermissions(@PathVariable Long staffId,
                                         @RequestBody @Valid AssignPermissionsDTO assignDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        permissionService.assignPermissions(userId, staffId, assignDTO);
        return Result.success();
    }

    @DeleteMapping("/staff/{staffId}/revoke")
    @ApiOperation("撤销员工权限")
    public Result<Void> revokePermissions(@PathVariable Long staffId,
                                         @RequestBody @Valid RevokePermissionsDTO revokeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        permissionService.revokePermissions(userId, staffId, revokeDTO);
        return Result.success();
    }

    @GetMapping("/role/{roleId}")
    @ApiOperation("获取角色权限")
    public Result<List<PermissionVO>> getRolePermissions(@PathVariable Long roleId) {
        List<PermissionVO> permissions = permissionService.getRolePermissions(roleId);
        return Result.success(permissions);
    }

    @PostMapping("/role/{roleId}/assign")
    @ApiOperation("分配角色权限")
    public Result<Void> assignRolePermissions(@PathVariable Long roleId,
                                             @RequestBody @Valid AssignPermissionsDTO assignDTO) {
        permissionService.assignRolePermissions(roleId, assignDTO);
        return Result.success();
    }
}
```

## 6. 商品管理模块

### 6.1 商品分类管理

#### 6.1.1 商品分类API

```java
@RestController
@RequestMapping("/api/product/category")
@Api(tags = "商品分类管理")
public class ProductCategoryController {

    @Autowired
    private ProductCategoryService categoryService;

    @GetMapping("/list")
    @ApiOperation("获取分类列表")
    public Result<List<ProductCategoryVO>> getCategoryList(
            @RequestParam(required = false) Long parentId,
            @RequestParam(required = false) Integer status) {
        List<ProductCategoryVO> categories = categoryService.getCategoryList(parentId, status);
        return Result.success(categories);
    }

    @GetMapping("/tree")
    @ApiOperation("获取分类树")
    public Result<List<ProductCategoryTreeVO>> getCategoryTree() {
        List<ProductCategoryTreeVO> categoryTree = categoryService.getCategoryTree();
        return Result.success(categoryTree);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取分类详情")
    public Result<ProductCategoryVO> getCategory(@PathVariable Long id) {
        ProductCategoryVO category = categoryService.getCategoryById(id);
        return Result.success(category);
    }

    @PostMapping
    @ApiOperation("添加分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<ProductCategoryVO> addCategory(@RequestBody @Valid ProductCategoryDTO categoryDTO) {
        ProductCategoryVO category = categoryService.addCategory(categoryDTO);
        return Result.success(category);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<ProductCategoryVO> updateCategory(@PathVariable Long id,
                                                   @RequestBody @Valid ProductCategoryDTO categoryDTO) {
        ProductCategoryVO category = categoryService.updateCategory(id, categoryDTO);
        return Result.success(category);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> deleteCategory(@PathVariable Long id) {
        categoryService.deleteCategory(id);
        return Result.success();
    }

    @PostMapping("/{id}/enable")
    @ApiOperation("启用分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> enableCategory(@PathVariable Long id) {
        categoryService.enableCategory(id);
        return Result.success();
    }

    @PostMapping("/{id}/disable")
    @ApiOperation("禁用分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> disableCategory(@PathVariable Long id) {
        categoryService.disableCategory(id);
        return Result.success();
    }

    @PostMapping("/batch-sort")
    @ApiOperation("批量排序")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> batchSort(@RequestBody @Valid List<CategorySortDTO> sortList) {
        categoryService.batchSort(sortList);
        return Result.success();
    }
}
```

### 6.2 商品基础管理

#### 6.2.1 商品管理API

```java
@RestController
@RequestMapping("/api/product")
@Api(tags = "商品管理")
public class ProductController {

    @Autowired
    private ProductService productService;

    @GetMapping("/list")
    @ApiOperation("获取商品列表")
    public Result<PageResult<ProductVO>> getProductList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortOrder) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<ProductVO> products = productService.getProductList(
            userId, page, size, categoryId, keyword, status, sortBy, sortOrder);
        return Result.success(products);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取商品详情")
    public Result<ProductDetailVO> getProduct(@PathVariable Long id) {
        ProductDetailVO product = productService.getProductById(id);
        return Result.success(product);
    }

    @PostMapping
    @ApiOperation("添加商品")
    public Result<ProductVO> addProduct(@RequestBody @Valid ProductDTO productDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductVO product = productService.addProduct(userId, productDTO);
        return Result.success(product);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新商品")
    public Result<ProductVO> updateProduct(@PathVariable Long id,
                                          @RequestBody @Valid ProductDTO productDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductVO product = productService.updateProduct(userId, id, productDTO);
        return Result.success(product);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除商品")
    public Result<Void> deleteProduct(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.deleteProduct(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/publish")
    @ApiOperation("上架商品")
    public Result<Void> publishProduct(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.publishProduct(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/unpublish")
    @ApiOperation("下架商品")
    public Result<Void> unpublishProduct(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.unpublishProduct(userId, id);
        return Result.success();
    }

    @PostMapping("/batch-publish")
    @ApiOperation("批量上架")
    public Result<Void> batchPublish(@RequestBody @Valid BatchOperationDTO batchDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.batchPublish(userId, batchDTO.getIds());
        return Result.success();
    }

    @PostMapping("/batch-unpublish")
    @ApiOperation("批量下架")
    public Result<Void> batchUnpublish(@RequestBody @Valid BatchOperationDTO batchDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.batchUnpublish(userId, batchDTO.getIds());
        return Result.success();
    }

    @PostMapping("/batch-delete")
    @ApiOperation("批量删除")
    public Result<Void> batchDelete(@RequestBody @Valid BatchOperationDTO batchDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.batchDelete(userId, batchDTO.getIds());
        return Result.success();
    }

    @PostMapping("/upload-images")
    @ApiOperation("上传商品图片")
    public Result<List<String>> uploadImages(@RequestParam("files") MultipartFile[] files) {
        List<String> imageUrls = productService.uploadImages(files);
        return Result.success(imageUrls);
    }

    @PostMapping("/upload-video")
    @ApiOperation("上传商品视频")
    public Result<String> uploadVideo(@RequestParam("file") MultipartFile file) {
        String videoUrl = productService.uploadVideo(file);
        return Result.success(videoUrl);
    }
}
```

#### 6.2.2 商品SKU管理API

```java
@RestController
@RequestMapping("/api/product/sku")
@Api(tags = "商品SKU管理")
public class ProductSkuController {

    @Autowired
    private ProductSkuService skuService;

    @GetMapping("/list/{productId}")
    @ApiOperation("获取商品SKU列表")
    public Result<List<ProductSkuVO>> getSkuList(@PathVariable Long productId) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<ProductSkuVO> skus = skuService.getSkuList(userId, productId);
        return Result.success(skus);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取SKU详情")
    public Result<ProductSkuVO> getSku(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductSkuVO sku = skuService.getSkuById(userId, id);
        return Result.success(sku);
    }

    @PostMapping
    @ApiOperation("添加SKU")
    public Result<ProductSkuVO> addSku(@RequestBody @Valid ProductSkuDTO skuDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductSkuVO sku = skuService.addSku(userId, skuDTO);
        return Result.success(sku);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新SKU")
    public Result<ProductSkuVO> updateSku(@PathVariable Long id,
                                         @RequestBody @Valid ProductSkuDTO skuDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductSkuVO sku = skuService.updateSku(userId, id, skuDTO);
        return Result.success(sku);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除SKU")
    public Result<Void> deleteSku(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.deleteSku(userId, id);
        return Result.success();
    }

    @PostMapping("/batch-update-stock")
    @ApiOperation("批量更新库存")
    public Result<Void> batchUpdateStock(@RequestBody @Valid List<SkuStockUpdateDTO> stockUpdates) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.batchUpdateStock(userId, stockUpdates);
        return Result.success();
    }

    @PostMapping("/batch-update-price")
    @ApiOperation("批量更新价格")
    public Result<Void> batchUpdatePrice(@RequestBody @Valid List<SkuPriceUpdateDTO> priceUpdates) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.batchUpdatePrice(userId, priceUpdates);
        return Result.success();
    }

    @PostMapping("/{id}/enable")
    @ApiOperation("启用SKU")
    public Result<Void> enableSku(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.enableSku(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/disable")
    @ApiOperation("禁用SKU")
    public Result<Void> disableSku(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        skuService.disableSku(userId, id);
        return Result.success();
    }
}
```

### 6.3 商品库存管理

#### 6.3.1 库存管理API

```java
@RestController
@RequestMapping("/api/product/stock")
@Api(tags = "商品库存管理")
public class ProductStockController {

    @Autowired
    private ProductStockService stockService;

    @GetMapping("/list")
    @ApiOperation("获取库存列表")
    public Result<PageResult<ProductStockVO>> getStockList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer stockStatus) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<ProductStockVO> stocks = stockService.getStockList(userId, page, size, keyword, stockStatus);
        return Result.success(stocks);
    }

    @GetMapping("/{productId}")
    @ApiOperation("获取商品库存详情")
    public Result<ProductStockDetailVO> getProductStock(@PathVariable Long productId) {
        Long userId = SecurityUtils.getCurrentUserId();
        ProductStockDetailVO stock = stockService.getProductStock(userId, productId);
        return Result.success(stock);
    }

    @PostMapping("/adjust")
    @ApiOperation("库存调整")
    public Result<Void> adjustStock(@RequestBody @Valid StockAdjustDTO adjustDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        stockService.adjustStock(userId, adjustDTO);
        return Result.success();
    }

    @PostMapping("/batch-adjust")
    @ApiOperation("批量库存调整")
    public Result<Void> batchAdjustStock(@RequestBody @Valid List<StockAdjustDTO> adjustList) {
        Long userId = SecurityUtils.getCurrentUserId();
        stockService.batchAdjustStock(userId, adjustList);
        return Result.success();
    }

    @GetMapping("/logs")
    @ApiOperation("获取库存变动日志")
    public Result<PageResult<StockLogVO>> getStockLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long productId,
            @RequestParam(required = false) Long skuId,
            @RequestParam(required = false) String operationType) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<StockLogVO> logs = stockService.getStockLogs(userId, page, size, productId, skuId, operationType);
        return Result.success(logs);
    }

    @PostMapping("/alert/settings")
    @ApiOperation("设置库存预警")
    public Result<Void> setStockAlert(@RequestBody @Valid StockAlertSettingsDTO alertSettings) {
        Long userId = SecurityUtils.getCurrentUserId();
        stockService.setStockAlert(userId, alertSettings);
        return Result.success();
    }

    @GetMapping("/alert/list")
    @ApiOperation("获取库存预警列表")
    public Result<PageResult<StockAlertVO>> getStockAlerts(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<StockAlertVO> alerts = stockService.getStockAlerts(userId, page, size);
        return Result.success(alerts);
    }

    @PostMapping("/import")
    @ApiOperation("导入库存")
    public Result<ImportResultVO> importStock(@RequestParam("file") MultipartFile file) {
        Long userId = SecurityUtils.getCurrentUserId();
        ImportResultVO result = stockService.importStock(userId, file);
        return Result.success(result);
    }

    @GetMapping("/export")
    @ApiOperation("导出库存")
    public void exportStock(HttpServletResponse response,
                           @RequestParam(required = false) String keyword,
                           @RequestParam(required = false) Integer stockStatus) {
        Long userId = SecurityUtils.getCurrentUserId();
        stockService.exportStock(response, userId, keyword, stockStatus);
    }
}
```

## 7. 订单管理模块

### 7.1 订单基础管理

#### 7.1.1 订单管理API

```java
@RestController
@RequestMapping("/api/order")
@Api(tags = "订单管理")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @GetMapping("/list")
    @ApiOperation("获取订单列表")
    public Result<PageResult<OrderVO>> getOrderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String paymentStatus,
            @RequestParam(required = false) String deliveryStatus,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<OrderVO> orders = orderService.getOrderList(
            userId, page, size, status, paymentStatus, deliveryStatus, keyword, startDate, endDate);
        return Result.success(orders);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取订单详情")
    public Result<OrderDetailVO> getOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        OrderDetailVO order = orderService.getOrderById(userId, id);
        return Result.success(order);
    }

    @PostMapping("/create")
    @ApiOperation("创建订单")
    public Result<OrderVO> createOrder(@RequestBody @Valid OrderCreateDTO orderCreateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        OrderVO order = orderService.createOrder(userId, orderCreateDTO);
        return Result.success(order);
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation("取消订单")
    public Result<Void> cancelOrder(@PathVariable Long id, @RequestBody @Valid OrderCancelDTO cancelDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.cancelOrder(userId, id, cancelDTO);
        return Result.success();
    }

    @PostMapping("/{id}/confirm")
    @ApiOperation("确认订单")
    public Result<Void> confirmOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.confirmOrder(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/ship")
    @ApiOperation("发货")
    public Result<Void> shipOrder(@PathVariable Long id, @RequestBody @Valid OrderShipDTO shipDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.shipOrder(userId, id, shipDTO);
        return Result.success();
    }

    @PostMapping("/{id}/deliver")
    @ApiOperation("确认收货")
    public Result<Void> deliverOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.deliverOrder(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成订单")
    public Result<Void> completeOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.completeOrder(userId, id);
        return Result.success();
    }

    @PostMapping("/batch-ship")
    @ApiOperation("批量发货")
    public Result<BatchOperationResultVO> batchShip(@RequestBody @Valid BatchShipDTO batchShipDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        BatchOperationResultVO result = orderService.batchShip(userId, batchShipDTO);
        return Result.success(result);
    }

    @PostMapping("/batch-cancel")
    @ApiOperation("批量取消")
    public Result<BatchOperationResultVO> batchCancel(@RequestBody @Valid BatchCancelDTO batchCancelDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        BatchOperationResultVO result = orderService.batchCancel(userId, batchCancelDTO);
        return Result.success(result);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取订单统计")
    public Result<OrderStatisticsVO> getOrderStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        OrderStatisticsVO statistics = orderService.getOrderStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }

    @GetMapping("/export")
    @ApiOperation("导出订单")
    public void exportOrders(HttpServletResponse response,
                            @RequestParam(required = false) String status,
                            @RequestParam(required = false) String startDate,
                            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.exportOrders(response, userId, status, startDate, endDate);
    }
}
```

#### 7.1.2 订单物流管理API

```java
@RestController
@RequestMapping("/api/order/logistics")
@Api(tags = "订单物流管理")
public class OrderLogisticsController {

    @Autowired
    private OrderLogisticsService logisticsService;

    @GetMapping("/{orderId}/track")
    @ApiOperation("查询物流信息")
    public Result<LogisticsTrackVO> trackLogistics(@PathVariable Long orderId) {
        Long userId = SecurityUtils.getCurrentUserId();
        LogisticsTrackVO track = logisticsService.trackLogistics(userId, orderId);
        return Result.success(track);
    }

    @PostMapping("/{orderId}/update")
    @ApiOperation("更新物流信息")
    public Result<Void> updateLogistics(@PathVariable Long orderId,
                                       @RequestBody @Valid LogisticsUpdateDTO updateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        logisticsService.updateLogistics(userId, orderId, updateDTO);
        return Result.success();
    }

    @GetMapping("/companies")
    @ApiOperation("获取物流公司列表")
    public Result<List<LogisticsCompanyVO>> getLogisticsCompanies() {
        List<LogisticsCompanyVO> companies = logisticsService.getLogisticsCompanies();
        return Result.success(companies);
    }

    @PostMapping("/{orderId}/change-company")
    @ApiOperation("更换物流公司")
    public Result<Void> changeLogisticsCompany(@PathVariable Long orderId,
                                              @RequestBody @Valid ChangeLogisticsCompanyDTO changeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        logisticsService.changeLogisticsCompany(userId, orderId, changeDTO);
        return Result.success();
    }

    @PostMapping("/batch-update")
    @ApiOperation("批量更新物流")
    public Result<BatchOperationResultVO> batchUpdateLogistics(
            @RequestBody @Valid BatchLogisticsUpdateDTO batchUpdateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        BatchOperationResultVO result = logisticsService.batchUpdateLogistics(userId, batchUpdateDTO);
        return Result.success(result);
    }

    @PostMapping("/import")
    @ApiOperation("导入物流信息")
    public Result<ImportResultVO> importLogistics(@RequestParam("file") MultipartFile file) {
        Long userId = SecurityUtils.getCurrentUserId();
        ImportResultVO result = logisticsService.importLogistics(userId, file);
        return Result.success(result);
    }
}
```

### 7.2 售后管理

#### 7.2.1 退款管理API

```java
@RestController
@RequestMapping("/api/order/refund")
@Api(tags = "退款管理")
public class RefundController {

    @Autowired
    private RefundService refundService;

    @GetMapping("/list")
    @ApiOperation("获取退款列表")
    public Result<PageResult<RefundVO>> getRefundList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<RefundVO> refunds = refundService.getRefundList(
            userId, page, size, status, keyword, startDate, endDate);
        return Result.success(refunds);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取退款详情")
    public Result<RefundDetailVO> getRefund(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        RefundDetailVO refund = refundService.getRefundById(userId, id);
        return Result.success(refund);
    }

    @PostMapping("/apply")
    @ApiOperation("申请退款")
    public Result<RefundVO> applyRefund(@RequestBody @Valid RefundApplyDTO applyDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        RefundVO refund = refundService.applyRefund(userId, applyDTO);
        return Result.success(refund);
    }

    @PostMapping("/{id}/approve")
    @ApiOperation("同意退款")
    public Result<Void> approveRefund(@PathVariable Long id, @RequestBody @Valid RefundApproveDTO approveDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        refundService.approveRefund(userId, id, approveDTO);
        return Result.success();
    }

    @PostMapping("/{id}/reject")
    @ApiOperation("拒绝退款")
    public Result<Void> rejectRefund(@PathVariable Long id, @RequestBody @Valid RefundRejectDTO rejectDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        refundService.rejectRefund(userId, id, rejectDTO);
        return Result.success();
    }

    @PostMapping("/{id}/process")
    @ApiOperation("处理退款")
    public Result<Void> processRefund(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        refundService.processRefund(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成退款")
    public Result<Void> completeRefund(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        refundService.completeRefund(userId, id);
        return Result.success();
    }

    @GetMapping("/statistics")
    @ApiOperation("获取退款统计")
    public Result<RefundStatisticsVO> getRefundStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        RefundStatisticsVO statistics = refundService.getRefundStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }
}
````

#### 7.2.2 退货管理API

```java
@RestController
@RequestMapping("/api/order/return")
@Api(tags = "退货管理")
public class ReturnController {

    @Autowired
    private ReturnService returnService;

    @GetMapping("/list")
    @ApiOperation("获取退货列表")
    public Result<PageResult<ReturnVO>> getReturnList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<ReturnVO> returns = returnService.getReturnList(
            userId, page, size, status, keyword, startDate, endDate);
        return Result.success(returns);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取退货详情")
    public Result<ReturnDetailVO> getReturn(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        ReturnDetailVO returnDetail = returnService.getReturnById(userId, id);
        return Result.success(returnDetail);
    }

    @PostMapping("/apply")
    @ApiOperation("申请退货")
    public Result<ReturnVO> applyReturn(@RequestBody @Valid ReturnApplyDTO applyDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ReturnVO returnVO = returnService.applyReturn(userId, applyDTO);
        return Result.success(returnVO);
    }

    @PostMapping("/{id}/approve")
    @ApiOperation("同意退货")
    public Result<Void> approveReturn(@PathVariable Long id, @RequestBody @Valid ReturnApproveDTO approveDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        returnService.approveReturn(userId, id, approveDTO);
        return Result.success();
    }

    @PostMapping("/{id}/reject")
    @ApiOperation("拒绝退货")
    public Result<Void> rejectReturn(@PathVariable Long id, @RequestBody @Valid ReturnRejectDTO rejectDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        returnService.rejectReturn(userId, id, rejectDTO);
        return Result.success();
    }

    @PostMapping("/{id}/ship")
    @ApiOperation("退货发货")
    public Result<Void> shipReturn(@PathVariable Long id, @RequestBody @Valid ReturnShipDTO shipDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        returnService.shipReturn(userId, id, shipDTO);
        return Result.success();
    }

    @PostMapping("/{id}/receive")
    @ApiOperation("确认收货")
    public Result<Void> receiveReturn(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        returnService.receiveReturn(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成退货")
    public Result<Void> completeReturn(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        returnService.completeReturn(userId, id);
        return Result.success();
    }
}
```

## 8. 支付管理模块

### 8.1 支付接口管理

#### 8.1.1 支付管理API

```java
@RestController
@RequestMapping("/api/payment")
@Api(tags = "支付管理")
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    @PostMapping("/create")
    @ApiOperation("创建支付订单")
    public Result<PaymentVO> createPayment(@RequestBody @Valid PaymentCreateDTO paymentCreateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PaymentVO payment = paymentService.createPayment(userId, paymentCreateDTO);
        return Result.success(payment);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取支付详情")
    public Result<PaymentDetailVO> getPayment(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        PaymentDetailVO payment = paymentService.getPaymentById(userId, id);
        return Result.success(payment);
    }

    @GetMapping("/list")
    @ApiOperation("获取支付列表")
    public Result<PageResult<PaymentVO>> getPaymentList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String paymentMethod,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<PaymentVO> payments = paymentService.getPaymentList(
            userId, page, size, status, paymentMethod, startDate, endDate);
        return Result.success(payments);
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation("取消支付")
    public Result<Void> cancelPayment(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        paymentService.cancelPayment(userId, id);
        return Result.success();
    }

    @PostMapping("/notify/wechat")
    @ApiOperation("微信支付回调")
    public String wechatPayNotify(@RequestBody String notifyData, HttpServletRequest request) {
        return paymentService.handleWechatPayNotify(notifyData, request);
    }

    @PostMapping("/notify/alipay")
    @ApiOperation("支付宝支付回调")
    public String alipayNotify(@RequestParam Map<String, String> params) {
        return paymentService.handleAlipayNotify(params);
    }

    @GetMapping("/methods")
    @ApiOperation("获取支付方式列表")
    public Result<List<PaymentMethodVO>> getPaymentMethods() {
        List<PaymentMethodVO> methods = paymentService.getPaymentMethods();
        return Result.success(methods);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取支付统计")
    public Result<PaymentStatisticsVO> getPaymentStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PaymentStatisticsVO statistics = paymentService.getPaymentStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }
}
```

### 8.2 资金管理

#### 8.2.1 钱包管理API

```java
@RestController
@RequestMapping("/api/wallet")
@Api(tags = "钱包管理")
public class WalletController {

    @Autowired
    private WalletService walletService;

    @GetMapping("/balance")
    @ApiOperation("获取钱包余额")
    public Result<WalletBalanceVO> getWalletBalance() {
        Long userId = SecurityUtils.getCurrentUserId();
        WalletBalanceVO balance = walletService.getWalletBalance(userId);
        return Result.success(balance);
    }

    @GetMapping("/transactions")
    @ApiOperation("获取交易记录")
    public Result<PageResult<WalletTransactionVO>> getTransactions(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<WalletTransactionVO> transactions = walletService.getTransactions(
            userId, page, size, type, startDate, endDate);
        return Result.success(transactions);
    }

    @PostMapping("/recharge")
    @ApiOperation("钱包充值")
    public Result<PaymentVO> recharge(@RequestBody @Valid WalletRechargeDTO rechargeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PaymentVO payment = walletService.recharge(userId, rechargeDTO);
        return Result.success(payment);
    }

    @PostMapping("/withdraw")
    @ApiOperation("申请提现")
    public Result<WithdrawVO> withdraw(@RequestBody @Valid WithdrawApplyDTO withdrawDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        WithdrawVO withdraw = walletService.applyWithdraw(userId, withdrawDTO);
        return Result.success(withdraw);
    }

    @GetMapping("/withdraw/list")
    @ApiOperation("获取提现记录")
    public Result<PageResult<WithdrawVO>> getWithdrawList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<WithdrawVO> withdraws = walletService.getWithdrawList(userId, page, size, status);
        return Result.success(withdraws);
    }

    @PostMapping("/withdraw/{id}/cancel")
    @ApiOperation("取消提现")
    public Result<Void> cancelWithdraw(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        walletService.cancelWithdraw(userId, id);
        return Result.success();
    }

    @GetMapping("/bank-cards")
    @ApiOperation("获取银行卡列表")
    public Result<List<BankCardVO>> getBankCards() {
        Long userId = SecurityUtils.getCurrentUserId();
        List<BankCardVO> bankCards = walletService.getBankCards(userId);
        return Result.success(bankCards);
    }

    @PostMapping("/bank-cards")
    @ApiOperation("添加银行卡")
    public Result<BankCardVO> addBankCard(@RequestBody @Valid BankCardDTO bankCardDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        BankCardVO bankCard = walletService.addBankCard(userId, bankCardDTO);
        return Result.success(bankCard);
    }

    @DeleteMapping("/bank-cards/{id}")
    @ApiOperation("删除银行卡")
    public Result<Void> deleteBankCard(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        walletService.deleteBankCard(userId, id);
        return Result.success();
    }
}
```

## 9. 拼车服务模块

### 9.1 拼车行程管理

#### 9.1.1 拼车行程API

```java
@RestController
@RequestMapping("/api/carpool/trip")
@Api(tags = "拼车行程管理")
public class CarpoolTripController {

    @Autowired
    private CarpoolTripService tripService;

    @GetMapping("/list")
    @ApiOperation("获取拼车行程列表")
    public Result<PageResult<CarpoolTripVO>> getTripList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String departure,
            @RequestParam(required = false) String destination,
            @RequestParam(required = false) String departureDate,
            @RequestParam(required = false) String tripType) {
        PageResult<CarpoolTripVO> trips = tripService.getTripList(
            page, size, departure, destination, departureDate, tripType);
        return Result.success(trips);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取拼车行程详情")
    public Result<CarpoolTripDetailVO> getTrip(@PathVariable Long id) {
        CarpoolTripDetailVO trip = tripService.getTripById(id);
        return Result.success(trip);
    }

    @PostMapping
    @ApiOperation("发布拼车行程")
    public Result<CarpoolTripVO> createTrip(@RequestBody @Valid CarpoolTripDTO tripDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        CarpoolTripVO trip = tripService.createTrip(userId, tripDTO);
        return Result.success(trip);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新拼车行程")
    public Result<CarpoolTripVO> updateTrip(@PathVariable Long id,
                                           @RequestBody @Valid CarpoolTripDTO tripDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        CarpoolTripVO trip = tripService.updateTrip(userId, id, tripDTO);
        return Result.success(trip);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除拼车行程")
    public Result<Void> deleteTrip(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        tripService.deleteTrip(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation("取消拼车行程")
    public Result<Void> cancelTrip(@PathVariable Long id, @RequestBody @Valid TripCancelDTO cancelDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        tripService.cancelTrip(userId, id, cancelDTO);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成拼车行程")
    public Result<Void> completeTrip(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        tripService.completeTrip(userId, id);
        return Result.success();
    }

    @GetMapping("/my-trips")
    @ApiOperation("获取我的拼车行程")
    public Result<PageResult<CarpoolTripVO>> getMyTrips(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<CarpoolTripVO> trips = tripService.getMyTrips(userId, page, size, status);
        return Result.success(trips);
    }

    @GetMapping("/search")
    @ApiOperation("搜索拼车行程")
    public Result<PageResult<CarpoolTripVO>> searchTrips(
            @RequestParam String departure,
            @RequestParam String destination,
            @RequestParam String departureDate,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        PageResult<CarpoolTripVO> trips = tripService.searchTrips(
            departure, destination, departureDate, page, size);
        return Result.success(trips);
    }
}
```

### 9.2 拼车订单管理

#### 9.2.1 拼车订单API

```java
@RestController
@RequestMapping("/api/carpool/order")
@Api(tags = "拼车订单管理")
public class CarpoolOrderController {

    @Autowired
    private CarpoolOrderService orderService;

    @GetMapping("/list")
    @ApiOperation("获取拼车订单列表")
    public Result<PageResult<CarpoolOrderVO>> getOrderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<CarpoolOrderVO> orders = orderService.getOrderList(
            userId, page, size, status, startDate, endDate);
        return Result.success(orders);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取拼车订单详情")
    public Result<CarpoolOrderDetailVO> getOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        CarpoolOrderDetailVO order = orderService.getOrderById(userId, id);
        return Result.success(order);
    }

    @PostMapping("/book")
    @ApiOperation("预订拼车")
    public Result<CarpoolOrderVO> bookTrip(@RequestBody @Valid CarpoolBookDTO bookDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        CarpoolOrderVO order = orderService.bookTrip(userId, bookDTO);
        return Result.success(order);
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation("取消拼车订单")
    public Result<Void> cancelOrder(@PathVariable Long id, @RequestBody @Valid OrderCancelDTO cancelDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.cancelOrder(userId, id, cancelDTO);
        return Result.success();
    }

    @PostMapping("/{id}/confirm")
    @ApiOperation("确认拼车订单")
    public Result<Void> confirmOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.confirmOrder(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成拼车订单")
    public Result<Void> completeOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.completeOrder(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/evaluate")
    @ApiOperation("评价拼车订单")
    public Result<Void> evaluateOrder(@PathVariable Long id, @RequestBody @Valid OrderEvaluateDTO evaluateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.evaluateOrder(userId, id, evaluateDTO);
        return Result.success();
    }

    @GetMapping("/statistics")
    @ApiOperation("获取拼车订单统计")
    public Result<CarpoolOrderStatisticsVO> getOrderStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        CarpoolOrderStatisticsVO statistics = orderService.getOrderStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }
}
```

## 10. 房产服务模块

### 10.1 房产信息管理

#### 10.1.1 房产信息API

```java
@RestController
@RequestMapping("/api/property")
@Api(tags = "房产信息管理")
public class PropertyController {

    @Autowired
    private PropertyService propertyService;

    @GetMapping("/list")
    @ApiOperation("获取房产列表")
    public Result<PageResult<PropertyVO>> getPropertyList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String propertyType,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String district,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) String keyword) {
        PageResult<PropertyVO> properties = propertyService.getPropertyList(
            page, size, propertyType, transactionType, district, minPrice, maxPrice, keyword);
        return Result.success(properties);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取房产详情")
    public Result<PropertyDetailVO> getProperty(@PathVariable Long id) {
        PropertyDetailVO property = propertyService.getPropertyById(id);
        return Result.success(property);
    }

    @PostMapping
    @ApiOperation("发布房产信息")
    public Result<PropertyVO> createProperty(@RequestBody @Valid PropertyDTO propertyDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PropertyVO property = propertyService.createProperty(userId, propertyDTO);
        return Result.success(property);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新房产信息")
    public Result<PropertyVO> updateProperty(@PathVariable Long id,
                                            @RequestBody @Valid PropertyDTO propertyDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PropertyVO property = propertyService.updateProperty(userId, id, propertyDTO);
        return Result.success(property);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除房产信息")
    public Result<Void> deleteProperty(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        propertyService.deleteProperty(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/publish")
    @ApiOperation("发布房产")
    public Result<Void> publishProperty(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        propertyService.publishProperty(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/unpublish")
    @ApiOperation("下架房产")
    public Result<Void> unpublishProperty(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        propertyService.unpublishProperty(userId, id);
        return Result.success();
    }

    @GetMapping("/my-properties")
    @ApiOperation("获取我的房产")
    public Result<PageResult<PropertyVO>> getMyProperties(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<PropertyVO> properties = propertyService.getMyProperties(userId, page, size, status);
        return Result.success(properties);
    }

    @GetMapping("/search")
    @ApiOperation("搜索房产")
    public Result<PageResult<PropertyVO>> searchProperties(
            @RequestParam String keyword,
            @RequestParam(required = false) String propertyType,
            @RequestParam(required = false) String transactionType,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        PageResult<PropertyVO> properties = propertyService.searchProperties(
            keyword, propertyType, transactionType, page, size);
        return Result.success(properties);
    }

    @PostMapping("/{id}/favorite")
    @ApiOperation("收藏房产")
    public Result<Void> favoriteProperty(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        propertyService.favoriteProperty(userId, id);
        return Result.success();
    }

    @DeleteMapping("/{id}/favorite")
    @ApiOperation("取消收藏房产")
    public Result<Void> unfavoriteProperty(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        propertyService.unfavoriteProperty(userId, id);
        return Result.success();
    }
}
```

### 10.2 房产预约管理

#### 10.2.1 看房预约API

```java
@RestController
@RequestMapping("/api/property/appointment")
@Api(tags = "看房预约管理")
public class PropertyAppointmentController {

    @Autowired
    private PropertyAppointmentService appointmentService;

    @GetMapping("/list")
    @ApiOperation("获取预约列表")
    public Result<PageResult<PropertyAppointmentVO>> getAppointmentList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<PropertyAppointmentVO> appointments = appointmentService.getAppointmentList(
            userId, page, size, status, startDate, endDate);
        return Result.success(appointments);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取预约详情")
    public Result<PropertyAppointmentDetailVO> getAppointment(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        PropertyAppointmentDetailVO appointment = appointmentService.getAppointmentById(userId, id);
        return Result.success(appointment);
    }

    @PostMapping
    @ApiOperation("创建看房预约")
    public Result<PropertyAppointmentVO> createAppointment(@RequestBody @Valid PropertyAppointmentDTO appointmentDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PropertyAppointmentVO appointment = appointmentService.createAppointment(userId, appointmentDTO);
        return Result.success(appointment);
    }

    @PostMapping("/{id}/confirm")
    @ApiOperation("确认预约")
    public Result<Void> confirmAppointment(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        appointmentService.confirmAppointment(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation("取消预约")
    public Result<Void> cancelAppointment(@PathVariable Long id, @RequestBody @Valid AppointmentCancelDTO cancelDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        appointmentService.cancelAppointment(userId, id, cancelDTO);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成预约")
    public Result<Void> completeAppointment(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        appointmentService.completeAppointment(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/reschedule")
    @ApiOperation("重新安排预约")
    public Result<Void> rescheduleAppointment(@PathVariable Long id,
                                             @RequestBody @Valid AppointmentRescheduleDTO rescheduleDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        appointmentService.rescheduleAppointment(userId, id, rescheduleDTO);
        return Result.success();
    }

    @GetMapping("/available-times")
    @ApiOperation("获取可预约时间")
    public Result<List<AvailableTimeVO>> getAvailableTimes(
            @RequestParam Long propertyId,
            @RequestParam String date) {
        List<AvailableTimeVO> availableTimes = appointmentService.getAvailableTimes(propertyId, date);
        return Result.success(availableTimes);
    }
}
```

## 11. 二手交易模块

### 11.1 二手商品管理

#### 11.1.1 二手商品API

```java
@RestController
@RequestMapping("/api/secondhand")
@Api(tags = "二手商品管理")
public class SecondhandController {

    @Autowired
    private SecondhandService secondhandService;

    @GetMapping("/list")
    @ApiOperation("获取二手商品列表")
    public Result<PageResult<SecondhandVO>> getSecondhandList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String condition,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) String keyword) {
        PageResult<SecondhandVO> secondhands = secondhandService.getSecondhandList(
            page, size, categoryId, condition, minPrice, maxPrice, keyword);
        return Result.success(secondhands);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取二手商品详情")
    public Result<SecondhandDetailVO> getSecondhand(@PathVariable Long id) {
        SecondhandDetailVO secondhand = secondhandService.getSecondhandById(id);
        return Result.success(secondhand);
    }

    @PostMapping
    @ApiOperation("发布二手商品")
    public Result<SecondhandVO> createSecondhand(@RequestBody @Valid SecondhandDTO secondhandDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        SecondhandVO secondhand = secondhandService.createSecondhand(userId, secondhandDTO);
        return Result.success(secondhand);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新二手商品")
    public Result<SecondhandVO> updateSecondhand(@PathVariable Long id,
                                                @RequestBody @Valid SecondhandDTO secondhandDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        SecondhandVO secondhand = secondhandService.updateSecondhand(userId, id, secondhandDTO);
        return Result.success(secondhand);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除二手商品")
    public Result<Void> deleteSecondhand(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        secondhandService.deleteSecondhand(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/publish")
    @ApiOperation("发布二手商品")
    public Result<Void> publishSecondhand(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        secondhandService.publishSecondhand(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/unpublish")
    @ApiOperation("下架二手商品")
    public Result<Void> unpublishSecondhand(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        secondhandService.unpublishSecondhand(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/sold")
    @ApiOperation("标记为已售出")
    public Result<Void> markAsSold(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        secondhandService.markAsSold(userId, id);
        return Result.success();
    }

    @GetMapping("/my-items")
    @ApiOperation("获取我的二手商品")
    public Result<PageResult<SecondhandVO>> getMySecondhandItems(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<SecondhandVO> items = secondhandService.getMySecondhandItems(userId, page, size, status);
        return Result.success(items);
    }

    @GetMapping("/categories")
    @ApiOperation("获取二手商品分类")
    public Result<List<SecondhandCategoryVO>> getCategories() {
        List<SecondhandCategoryVO> categories = secondhandService.getCategories();
        return Result.success(categories);
    }

    @PostMapping("/{id}/favorite")
    @ApiOperation("收藏二手商品")
    public Result<Void> favoriteSecondhand(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        secondhandService.favoriteSecondhand(userId, id);
        return Result.success();
    }

    @DeleteMapping("/{id}/favorite")
    @ApiOperation("取消收藏二手商品")
    public Result<Void> unfavoriteSecondhand(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        secondhandService.unfavoriteSecondhand(userId, id);
        return Result.success();
    }

    @GetMapping("/search")
    @ApiOperation("搜索二手商品")
    public Result<PageResult<SecondhandVO>> searchSecondhand(
            @RequestParam String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String condition,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        PageResult<SecondhandVO> items = secondhandService.searchSecondhand(
            keyword, categoryId, condition, page, size);
        return Result.success(items);
    }
}
````

### 11.2 二手交易订单管理

#### 11.2.1 二手交易订单API

```java
@RestController
@RequestMapping("/api/secondhand/order")
@Api(tags = "二手交易订单管理")
public class SecondhandOrderController {

    @Autowired
    private SecondhandOrderService orderService;

    @GetMapping("/list")
    @ApiOperation("获取二手交易订单列表")
    public Result<PageResult<SecondhandOrderVO>> getOrderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<SecondhandOrderVO> orders = orderService.getOrderList(
            userId, page, size, status, role, startDate, endDate);
        return Result.success(orders);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取二手交易订单详情")
    public Result<SecondhandOrderDetailVO> getOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        SecondhandOrderDetailVO order = orderService.getOrderById(userId, id);
        return Result.success(order);
    }

    @PostMapping("/create")
    @ApiOperation("创建二手交易订单")
    public Result<SecondhandOrderVO> createOrder(@RequestBody @Valid SecondhandOrderDTO orderDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        SecondhandOrderVO order = orderService.createOrder(userId, orderDTO);
        return Result.success(order);
    }

    @PostMapping("/{id}/pay")
    @ApiOperation("支付订单")
    public Result<PaymentVO> payOrder(@PathVariable Long id, @RequestBody @Valid OrderPayDTO payDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PaymentVO payment = orderService.payOrder(userId, id, payDTO);
        return Result.success(payment);
    }

    @PostMapping("/{id}/confirm")
    @ApiOperation("确认订单")
    public Result<Void> confirmOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.confirmOrder(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation("取消订单")
    public Result<Void> cancelOrder(@PathVariable Long id, @RequestBody @Valid OrderCancelDTO cancelDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.cancelOrder(userId, id, cancelDTO);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成订单")
    public Result<Void> completeOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.completeOrder(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/evaluate")
    @ApiOperation("评价订单")
    public Result<Void> evaluateOrder(@PathVariable Long id, @RequestBody @Valid OrderEvaluateDTO evaluateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.evaluateOrder(userId, id, evaluateDTO);
        return Result.success();
    }

    @GetMapping("/statistics")
    @ApiOperation("获取二手交易统计")
    public Result<SecondhandOrderStatisticsVO> getOrderStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        SecondhandOrderStatisticsVO statistics = orderService.getOrderStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }
}
```

## 12. 本地服务模块

### 12.1 服务提供商管理

#### 12.1.1 服务提供商API

```java
@RestController
@RequestMapping("/api/local-service/provider")
@Api(tags = "服务提供商管理")
public class ServiceProviderController {

    @Autowired
    private ServiceProviderService providerService;

    @GetMapping("/list")
    @ApiOperation("获取服务提供商列表")
    public Result<PageResult<ServiceProviderVO>> getProviderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String district,
            @RequestParam(required = false) String keyword) {
        PageResult<ServiceProviderVO> providers = providerService.getProviderList(
            page, size, categoryId, district, keyword);
        return Result.success(providers);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取服务提供商详情")
    public Result<ServiceProviderDetailVO> getProvider(@PathVariable Long id) {
        ServiceProviderDetailVO provider = providerService.getProviderById(id);
        return Result.success(provider);
    }

    @PostMapping("/register")
    @ApiOperation("注册服务提供商")
    public Result<ServiceProviderVO> registerProvider(@RequestBody @Valid ServiceProviderDTO providerDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ServiceProviderVO provider = providerService.registerProvider(userId, providerDTO);
        return Result.success(provider);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新服务提供商信息")
    public Result<ServiceProviderVO> updateProvider(@PathVariable Long id,
                                                   @RequestBody @Valid ServiceProviderDTO providerDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ServiceProviderVO provider = providerService.updateProvider(userId, id, providerDTO);
        return Result.success(provider);
    }

    @PostMapping("/{id}/verify")
    @ApiOperation("认证服务提供商")
    public Result<Void> verifyProvider(@PathVariable Long id, @RequestBody @Valid ProviderVerifyDTO verifyDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        providerService.verifyProvider(userId, id, verifyDTO);
        return Result.success();
    }

    @PostMapping("/{id}/suspend")
    @ApiOperation("暂停服务提供商")
    public Result<Void> suspendProvider(@PathVariable Long id, @RequestBody @Valid ProviderSuspendDTO suspendDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        providerService.suspendProvider(userId, id, suspendDTO);
        return Result.success();
    }

    @PostMapping("/{id}/activate")
    @ApiOperation("激活服务提供商")
    public Result<Void> activateProvider(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        providerService.activateProvider(userId, id);
        return Result.success();
    }

    @GetMapping("/categories")
    @ApiOperation("获取服务分类")
    public Result<List<ServiceCategoryVO>> getServiceCategories() {
        List<ServiceCategoryVO> categories = providerService.getServiceCategories();
        return Result.success(categories);
    }

    @GetMapping("/my-provider")
    @ApiOperation("获取我的服务提供商信息")
    public Result<ServiceProviderDetailVO> getMyProvider() {
        Long userId = SecurityUtils.getCurrentUserId();
        ServiceProviderDetailVO provider = providerService.getMyProvider(userId);
        return Result.success(provider);
    }
}
```

### 12.2 服务订单管理

#### 12.2.1 服务订单API

```java
@RestController
@RequestMapping("/api/local-service/order")
@Api(tags = "服务订单管理")
public class ServiceOrderController {

    @Autowired
    private ServiceOrderService orderService;

    @GetMapping("/list")
    @ApiOperation("获取服务订单列表")
    public Result<PageResult<ServiceOrderVO>> getOrderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<ServiceOrderVO> orders = orderService.getOrderList(
            userId, page, size, status, role, startDate, endDate);
        return Result.success(orders);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取服务订单详情")
    public Result<ServiceOrderDetailVO> getOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        ServiceOrderDetailVO order = orderService.getOrderById(userId, id);
        return Result.success(order);
    }

    @PostMapping("/create")
    @ApiOperation("创建服务订单")
    public Result<ServiceOrderVO> createOrder(@RequestBody @Valid ServiceOrderDTO orderDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ServiceOrderVO order = orderService.createOrder(userId, orderDTO);
        return Result.success(order);
    }

    @PostMapping("/{id}/accept")
    @ApiOperation("接受服务订单")
    public Result<Void> acceptOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.acceptOrder(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/reject")
    @ApiOperation("拒绝服务订单")
    public Result<Void> rejectOrder(@PathVariable Long id, @RequestBody @Valid OrderRejectDTO rejectDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.rejectOrder(userId, id, rejectDTO);
        return Result.success();
    }

    @PostMapping("/{id}/start")
    @ApiOperation("开始服务")
    public Result<Void> startService(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.startService(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/complete")
    @ApiOperation("完成服务")
    public Result<Void> completeService(@PathVariable Long id, @RequestBody @Valid ServiceCompleteDTO completeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.completeService(userId, id, completeDTO);
        return Result.success();
    }

    @PostMapping("/{id}/cancel")
    @ApiOperation("取消服务订单")
    public Result<Void> cancelOrder(@PathVariable Long id, @RequestBody @Valid OrderCancelDTO cancelDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.cancelOrder(userId, id, cancelDTO);
        return Result.success();
    }

    @PostMapping("/{id}/pay")
    @ApiOperation("支付服务订单")
    public Result<PaymentVO> payOrder(@PathVariable Long id, @RequestBody @Valid OrderPayDTO payDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PaymentVO payment = orderService.payOrder(userId, id, payDTO);
        return Result.success(payment);
    }

    @PostMapping("/{id}/evaluate")
    @ApiOperation("评价服务订单")
    public Result<Void> evaluateOrder(@PathVariable Long id, @RequestBody @Valid OrderEvaluateDTO evaluateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.evaluateOrder(userId, id, evaluateDTO);
        return Result.success();
    }

    @GetMapping("/statistics")
    @ApiOperation("获取服务订单统计")
    public Result<ServiceOrderStatisticsVO> getOrderStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        ServiceOrderStatisticsVO statistics = orderService.getOrderStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }
}
```

## 13. 社区信息模块

### 13.1 社区动态管理

#### 13.1.1 社区动态API

```java
@RestController
@RequestMapping("/api/community/post")
@Api(tags = "社区动态管理")
public class CommunityPostController {

    @Autowired
    private CommunityPostService postService;

    @GetMapping("/list")
    @ApiOperation("获取社区动态列表")
    public Result<PageResult<CommunityPostVO>> getPostList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String postType,
            @RequestParam(required = false) String sortBy) {
        PageResult<CommunityPostVO> posts = postService.getPostList(
            page, size, categoryId, postType, sortBy);
        return Result.success(posts);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取社区动态详情")
    public Result<CommunityPostDetailVO> getPost(@PathVariable Long id) {
        CommunityPostDetailVO post = postService.getPostById(id);
        return Result.success(post);
    }

    @PostMapping
    @ApiOperation("发布社区动态")
    public Result<CommunityPostVO> createPost(@RequestBody @Valid CommunityPostDTO postDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        CommunityPostVO post = postService.createPost(userId, postDTO);
        return Result.success(post);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新社区动态")
    public Result<CommunityPostVO> updatePost(@PathVariable Long id,
                                             @RequestBody @Valid CommunityPostDTO postDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        CommunityPostVO post = postService.updatePost(userId, id, postDTO);
        return Result.success(post);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除社区动态")
    public Result<Void> deletePost(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        postService.deletePost(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/like")
    @ApiOperation("点赞社区动态")
    public Result<Void> likePost(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        postService.likePost(userId, id);
        return Result.success();
    }

    @DeleteMapping("/{id}/like")
    @ApiOperation("取消点赞社区动态")
    public Result<Void> unlikePost(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        postService.unlikePost(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/collect")
    @ApiOperation("收藏社区动态")
    public Result<Void> collectPost(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        postService.collectPost(userId, id);
        return Result.success();
    }

    @DeleteMapping("/{id}/collect")
    @ApiOperation("取消收藏社区动态")
    public Result<Void> uncollectPost(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        postService.uncollectPost(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/report")
    @ApiOperation("举报社区动态")
    public Result<Void> reportPost(@PathVariable Long id, @RequestBody @Valid PostReportDTO reportDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        postService.reportPost(userId, id, reportDTO);
        return Result.success();
    }

    @GetMapping("/my-posts")
    @ApiOperation("获取我的社区动态")
    public Result<PageResult<CommunityPostVO>> getMyPosts(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<CommunityPostVO> posts = postService.getMyPosts(userId, page, size, status);
        return Result.success(posts);
    }

    @GetMapping("/categories")
    @ApiOperation("获取社区分类")
    public Result<List<CommunityCategoryVO>> getCategories() {
        List<CommunityCategoryVO> categories = postService.getCategories();
        return Result.success(categories);
    }

    @GetMapping("/hot")
    @ApiOperation("获取热门动态")
    public Result<List<CommunityPostVO>> getHotPosts(@RequestParam(defaultValue = "10") Integer limit) {
        List<CommunityPostVO> posts = postService.getHotPosts(limit);
        return Result.success(posts);
    }

    @GetMapping("/search")
    @ApiOperation("搜索社区动态")
    public Result<PageResult<CommunityPostVO>> searchPosts(
            @RequestParam String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        PageResult<CommunityPostVO> posts = postService.searchPosts(keyword, categoryId, page, size);
        return Result.success(posts);
    }
}
```

### 13.2 社区评论管理

#### 13.2.1 社区评论API

```java
@RestController
@RequestMapping("/api/community/comment")
@Api(tags = "社区评论管理")
public class CommunityCommentController {

    @Autowired
    private CommunityCommentService commentService;

    @GetMapping("/list")
    @ApiOperation("获取评论列表")
    public Result<PageResult<CommunityCommentVO>> getCommentList(
            @RequestParam Long postId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String sortBy) {
        PageResult<CommunityCommentVO> comments = commentService.getCommentList(
            postId, page, size, sortBy);
        return Result.success(comments);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取评论详情")
    public Result<CommunityCommentDetailVO> getComment(@PathVariable Long id) {
        CommunityCommentDetailVO comment = commentService.getCommentById(id);
        return Result.success(comment);
    }

    @PostMapping
    @ApiOperation("发表评论")
    public Result<CommunityCommentVO> createComment(@RequestBody @Valid CommunityCommentDTO commentDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        CommunityCommentVO comment = commentService.createComment(userId, commentDTO);
        return Result.success(comment);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新评论")
    public Result<CommunityCommentVO> updateComment(@PathVariable Long id,
                                                   @RequestBody @Valid CommunityCommentDTO commentDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        CommunityCommentVO comment = commentService.updateComment(userId, id, commentDTO);
        return Result.success(comment);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除评论")
    public Result<Void> deleteComment(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        commentService.deleteComment(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/like")
    @ApiOperation("点赞评论")
    public Result<Void> likeComment(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        commentService.likeComment(userId, id);
        return Result.success();
    }

    @DeleteMapping("/{id}/like")
    @ApiOperation("取消点赞评论")
    public Result<Void> unlikeComment(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        commentService.unlikeComment(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/report")
    @ApiOperation("举报评论")
    public Result<Void> reportComment(@PathVariable Long id, @RequestBody @Valid CommentReportDTO reportDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        commentService.reportComment(userId, id, reportDTO);
        return Result.success();
    }

    @GetMapping("/replies/{commentId}")
    @ApiOperation("获取评论回复")
    public Result<PageResult<CommunityCommentVO>> getCommentReplies(
            @PathVariable Long commentId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        PageResult<CommunityCommentVO> replies = commentService.getCommentReplies(commentId, page, size);
        return Result.success(replies);
    }

    @GetMapping("/my-comments")
    @ApiOperation("获取我的评论")
    public Result<PageResult<CommunityCommentVO>> getMyComments(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<CommunityCommentVO> comments = commentService.getMyComments(userId, page, size);
        return Result.success(comments);
    }
}
```

## 14. 推广工具模块

### 14.1 推广内容管理

#### 14.1.1 推广内容API

```java
@RestController
@RequestMapping("/api/promotion/content")
@Api(tags = "推广内容管理")
public class PromotionContentController {

    @Autowired
    private PromotionContentService contentService;

    @GetMapping("/list")
    @ApiOperation("获取推广内容列表")
    public Result<PageResult<PromotionContentVO>> getContentList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String contentType,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<PromotionContentVO> contents = contentService.getContentList(
            userId, page, size, contentType, status, keyword);
        return Result.success(contents);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取推广内容详情")
    public Result<PromotionContentDetailVO> getContent(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        PromotionContentDetailVO content = contentService.getContentById(userId, id);
        return Result.success(content);
    }

    @PostMapping("/generate-link")
    @ApiOperation("生成推广链接")
    public Result<PromotionLinkVO> generatePromotionLink(@RequestBody @Valid PromotionLinkDTO linkDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PromotionLinkVO link = contentService.generatePromotionLink(userId, linkDTO);
        return Result.success(link);
    }

    @PostMapping("/generate-qrcode")
    @ApiOperation("生成推广二维码")
    public Result<PromotionQRCodeVO> generateQRCode(@RequestBody @Valid PromotionQRCodeDTO qrCodeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PromotionQRCodeVO qrCode = contentService.generateQRCode(userId, qrCodeDTO);
        return Result.success(qrCode);
    }

    @PostMapping("/generate-poster")
    @ApiOperation("生成推广海报")
    public Result<PromotionPosterVO> generatePoster(@RequestBody @Valid PromotionPosterDTO posterDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PromotionPosterVO poster = contentService.generatePoster(userId, posterDTO);
        return Result.success(poster);
    }

    @GetMapping("/templates")
    @ApiOperation("获取推广模板")
    public Result<List<PromotionTemplateVO>> getPromotionTemplates(
            @RequestParam(required = false) String contentType,
            @RequestParam(required = false) String templateType) {
        List<PromotionTemplateVO> templates = contentService.getPromotionTemplates(contentType, templateType);
        return Result.success(templates);
    }

    @PostMapping("/share")
    @ApiOperation("记录分享行为")
    public Result<Void> recordShare(@RequestBody @Valid PromotionShareDTO shareDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        contentService.recordShare(userId, shareDTO);
        return Result.success();
    }

    @GetMapping("/statistics")
    @ApiOperation("获取推广统计")
    public Result<PromotionStatisticsVO> getPromotionStatistics(
            @RequestParam(required = false) String contentType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PromotionStatisticsVO statistics = contentService.getPromotionStatistics(
            userId, contentType, startDate, endDate);
        return Result.success(statistics);
    }

    @GetMapping("/my-promotions")
    @ApiOperation("获取我的推广记录")
    public Result<PageResult<PromotionRecordVO>> getMyPromotions(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String contentType,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<PromotionRecordVO> records = contentService.getMyPromotions(
            userId, page, size, contentType, status);
        return Result.success(records);
    }
}
```

### 14.2 推广效果分析

#### 14.2.1 推广效果分析API

```java
@RestController
@RequestMapping("/api/promotion/analytics")
@Api(tags = "推广效果分析")
public class PromotionAnalyticsController {

    @Autowired
    private PromotionAnalyticsService analyticsService;

    @GetMapping("/overview")
    @ApiOperation("获取推广概览")
    public Result<PromotionOverviewVO> getPromotionOverview(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PromotionOverviewVO overview = analyticsService.getPromotionOverview(userId, startDate, endDate);
        return Result.success(overview);
    }

    @GetMapping("/trend")
    @ApiOperation("获取推广趋势")
    public Result<List<PromotionTrendVO>> getPromotionTrend(
            @RequestParam String period,
            @RequestParam(required = false) String contentType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<PromotionTrendVO> trend = analyticsService.getPromotionTrend(
            userId, period, contentType, startDate, endDate);
        return Result.success(trend);
    }

    @GetMapping("/conversion")
    @ApiOperation("获取转化率分析")
    public Result<PromotionConversionVO> getConversionAnalysis(
            @RequestParam(required = false) String contentType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PromotionConversionVO conversion = analyticsService.getConversionAnalysis(
            userId, contentType, startDate, endDate);
        return Result.success(conversion);
    }

    @GetMapping("/channel")
    @ApiOperation("获取渠道分析")
    public Result<List<PromotionChannelVO>> getChannelAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<PromotionChannelVO> channels = analyticsService.getChannelAnalysis(userId, startDate, endDate);
        return Result.success(channels);
    }

    @GetMapping("/top-content")
    @ApiOperation("获取热门推广内容")
    public Result<List<PromotionTopContentVO>> getTopContent(
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) String contentType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<PromotionTopContentVO> topContent = analyticsService.getTopContent(
            userId, limit, contentType, startDate, endDate);
        return Result.success(topContent);
    }

    @GetMapping("/earnings")
    @ApiOperation("获取推广收益分析")
    public Result<PromotionEarningsVO> getEarningsAnalysis(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PromotionEarningsVO earnings = analyticsService.getEarningsAnalysis(userId, startDate, endDate);
        return Result.success(earnings);
    }

    @GetMapping("/report")
    @ApiOperation("生成推广报告")
    public Result<PromotionReportVO> generatePromotionReport(
            @RequestParam String reportType,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) String contentType) {
        Long userId = SecurityUtils.getCurrentUserId();
        PromotionReportVO report = analyticsService.generatePromotionReport(
            userId, reportType, startDate, endDate, contentType);
        return Result.success(report);
    }
}
````

## 15. 分销系统模块

### 15.1 分销员管理

#### 15.1.1 分销员API

```java
@RestController
@RequestMapping("/api/distribution/distributor")
@Api(tags = "分销员管理")
public class DistributorController {

    @Autowired
    private DistributorService distributorService;

    @GetMapping("/list")
    @ApiOperation("获取分销员列表")
    public Result<PageResult<DistributorVO>> getDistributorList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String keyword) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<DistributorVO> distributors = distributorService.getDistributorList(
            userId, page, size, status, level, keyword);
        return Result.success(distributors);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取分销员详情")
    public Result<DistributorDetailVO> getDistributor(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        DistributorDetailVO distributor = distributorService.getDistributorById(userId, id);
        return Result.success(distributor);
    }

    @PostMapping("/apply")
    @ApiOperation("申请成为分销员")
    public Result<DistributorVO> applyDistributor(@RequestBody @Valid DistributorApplyDTO applyDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        DistributorVO distributor = distributorService.applyDistributor(userId, applyDTO);
        return Result.success(distributor);
    }

    @PostMapping("/{id}/approve")
    @ApiOperation("审核分销员申请")
    public Result<Void> approveDistributor(@PathVariable Long id, 
                                          @RequestBody @Valid DistributorApproveDTO approveDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        distributorService.approveDistributor(userId, id, approveDTO);
        return Result.success();
    }

    @PostMapping("/{id}/reject")
    @ApiOperation("拒绝分销员申请")
    public Result<Void> rejectDistributor(@PathVariable Long id, 
                                         @RequestBody @Valid DistributorRejectDTO rejectDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        distributorService.rejectDistributor(userId, id, rejectDTO);
        return Result.success();
    }

    @PostMapping("/{id}/suspend")
    @ApiOperation("暂停分销员")
    public Result<Void> suspendDistributor(@PathVariable Long id, 
                                          @RequestBody @Valid DistributorSuspendDTO suspendDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        distributorService.suspendDistributor(userId, id, suspendDTO);
        return Result.success();
    }

    @PostMapping("/{id}/activate")
    @ApiOperation("激活分销员")
    public Result<Void> activateDistributor(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        distributorService.activateDistributor(userId, id);
        return Result.success();
    }

    @GetMapping("/my-info")
    @ApiOperation("获取我的分销员信息")
    public Result<DistributorDetailVO> getMyDistributorInfo() {
        Long userId = SecurityUtils.getCurrentUserId();
        DistributorDetailVO distributor = distributorService.getMyDistributorInfo(userId);
        return Result.success(distributor);
    }

    @PutMapping("/my-info")
    @ApiOperation("更新我的分销员信息")
    public Result<DistributorVO> updateMyDistributorInfo(@RequestBody @Valid DistributorUpdateDTO updateDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        DistributorVO distributor = distributorService.updateMyDistributorInfo(userId, updateDTO);
        return Result.success(distributor);
    }

    @GetMapping("/team")
    @ApiOperation("获取我的团队")
    public Result<PageResult<DistributorTeamVO>> getMyTeam(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Integer level) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<DistributorTeamVO> team = distributorService.getMyTeam(userId, page, size, level);
        return Result.success(team);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取分销员统计")
    public Result<DistributorStatisticsVO> getDistributorStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        DistributorStatisticsVO statistics = distributorService.getDistributorStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }

    @GetMapping("/levels")
    @ApiOperation("获取分销员等级配置")
    public Result<List<DistributorLevelVO>> getDistributorLevels() {
        List<DistributorLevelVO> levels = distributorService.getDistributorLevels();
        return Result.success(levels);
    }
}
```

### 15.2 佣金管理

#### 15.2.1 佣金API

```java
@RestController
@RequestMapping("/api/distribution/commission")
@Api(tags = "佣金管理")
public class CommissionController {

    @Autowired
    private CommissionService commissionService;

    @GetMapping("/list")
    @ApiOperation("获取佣金记录列表")
    public Result<PageResult<CommissionRecordVO>> getCommissionList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<CommissionRecordVO> commissions = commissionService.getCommissionList(
            userId, page, size, status, type, startDate, endDate);
        return Result.success(commissions);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取佣金记录详情")
    public Result<CommissionRecordDetailVO> getCommission(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        CommissionRecordDetailVO commission = commissionService.getCommissionById(userId, id);
        return Result.success(commission);
    }

    @GetMapping("/summary")
    @ApiOperation("获取佣金汇总")
    public Result<CommissionSummaryVO> getCommissionSummary(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        CommissionSummaryVO summary = commissionService.getCommissionSummary(userId, startDate, endDate);
        return Result.success(summary);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取佣金统计")
    public Result<CommissionStatisticsVO> getCommissionStatistics(
            @RequestParam String period,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        CommissionStatisticsVO statistics = commissionService.getCommissionStatistics(
            userId, period, startDate, endDate);
        return Result.success(statistics);
    }

    @GetMapping("/trend")
    @ApiOperation("获取佣金趋势")
    public Result<List<CommissionTrendVO>> getCommissionTrend(
            @RequestParam String period,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<CommissionTrendVO> trend = commissionService.getCommissionTrend(userId, period, startDate, endDate);
        return Result.success(trend);
    }

    @PostMapping("/withdraw")
    @ApiOperation("申请提现")
    public Result<WithdrawRecordVO> applyWithdraw(@RequestBody @Valid WithdrawApplyDTO withdrawDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        WithdrawRecordVO withdraw = commissionService.applyWithdraw(userId, withdrawDTO);
        return Result.success(withdraw);
    }

    @GetMapping("/withdraw/list")
    @ApiOperation("获取提现记录")
    public Result<PageResult<WithdrawRecordVO>> getWithdrawList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<WithdrawRecordVO> withdraws = commissionService.getWithdrawList(userId, page, size, status);
        return Result.success(withdraws);
    }

    @GetMapping("/withdraw/{id}")
    @ApiOperation("获取提现记录详情")
    public Result<WithdrawRecordDetailVO> getWithdraw(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        WithdrawRecordDetailVO withdraw = commissionService.getWithdrawById(userId, id);
        return Result.success(withdraw);
    }

    @PostMapping("/withdraw/{id}/cancel")
    @ApiOperation("取消提现申请")
    public Result<Void> cancelWithdraw(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        commissionService.cancelWithdraw(userId, id);
        return Result.success();
    }

    @GetMapping("/rules")
    @ApiOperation("获取佣金规则")
    public Result<List<CommissionRuleVO>> getCommissionRules(
            @RequestParam(required = false) String contentType) {
        List<CommissionRuleVO> rules = commissionService.getCommissionRules(contentType);
        return Result.success(rules);
    }

    @GetMapping("/account")
    @ApiOperation("获取佣金账户信息")
    public Result<CommissionAccountVO> getCommissionAccount() {
        Long userId = SecurityUtils.getCurrentUserId();
        CommissionAccountVO account = commissionService.getCommissionAccount(userId);
        return Result.success(account);
    }
}
```

## 16. 返利系统模块

### 16.1 返利商品管理

#### 16.1.1 返利商品API

```java
@RestController
@RequestMapping("/api/cashback/product")
@Api(tags = "返利商品管理")
public class CashbackProductController {

    @Autowired
    private CashbackProductService productService;

    @GetMapping("/list")
    @ApiOperation("获取返利商品列表")
    public Result<PageResult<CashbackProductVO>> getProductList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String platform,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String sortBy) {
        PageResult<CashbackProductVO> products = productService.getProductList(
            page, size, platform, categoryId, keyword, sortBy);
        return Result.success(products);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取返利商品详情")
    public Result<CashbackProductDetailVO> getProduct(@PathVariable Long id) {
        CashbackProductDetailVO product = productService.getProductById(id);
        return Result.success(product);
    }

    @PostMapping("/search")
    @ApiOperation("搜索返利商品")
    public Result<PageResult<CashbackProductVO>> searchProducts(@RequestBody @Valid ProductSearchDTO searchDTO) {
        PageResult<CashbackProductVO> products = productService.searchProducts(searchDTO);
        return Result.success(products);
    }

    @PostMapping("/{id}/generate-link")
    @ApiOperation("生成返利链接")
    public Result<CashbackLinkVO> generateCashbackLink(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        CashbackLinkVO link = productService.generateCashbackLink(userId, id);
        return Result.success(link);
    }

    @PostMapping("/{id}/favorite")
    @ApiOperation("收藏返利商品")
    public Result<Void> favoriteProduct(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.favoriteProduct(userId, id);
        return Result.success();
    }

    @DeleteMapping("/{id}/favorite")
    @ApiOperation("取消收藏返利商品")
    public Result<Void> unfavoriteProduct(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.unfavoriteProduct(userId, id);
        return Result.success();
    }

    @GetMapping("/favorites")
    @ApiOperation("获取收藏的返利商品")
    public Result<PageResult<CashbackProductVO>> getFavoriteProducts(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<CashbackProductVO> products = productService.getFavoriteProducts(userId, page, size);
        return Result.success(products);
    }

    @GetMapping("/categories")
    @ApiOperation("获取返利商品分类")
    public Result<List<CashbackCategoryVO>> getCategories(@RequestParam(required = false) String platform) {
        List<CashbackCategoryVO> categories = productService.getCategories(platform);
        return Result.success(categories);
    }

    @GetMapping("/platforms")
    @ApiOperation("获取支持的平台列表")
    public Result<List<CashbackPlatformVO>> getPlatforms() {
        List<CashbackPlatformVO> platforms = productService.getPlatforms();
        return Result.success(platforms);
    }

    @GetMapping("/hot")
    @ApiOperation("获取热门返利商品")
    public Result<List<CashbackProductVO>> getHotProducts(
            @RequestParam(defaultValue = "20") Integer limit,
            @RequestParam(required = false) String platform) {
        List<CashbackProductVO> products = productService.getHotProducts(limit, platform);
        return Result.success(products);
    }

    @GetMapping("/recommendations")
    @ApiOperation("获取推荐返利商品")
    public Result<List<CashbackProductVO>> getRecommendations(
            @RequestParam(defaultValue = "20") Integer limit) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<CashbackProductVO> products = productService.getRecommendations(userId, limit);
        return Result.success(products);
    }

    @PostMapping("/sync")
    @ApiOperation("同步平台商品数据")
    public Result<Void> syncPlatformProducts(@RequestBody @Valid ProductSyncDTO syncDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        productService.syncPlatformProducts(userId, syncDTO);
        return Result.success();
    }
}
```

### 16.2 返利订单管理

#### 16.2.1 返利订单API

```java
@RestController
@RequestMapping("/api/cashback/order")
@Api(tags = "返利订单管理")
public class CashbackOrderController {

    @Autowired
    private CashbackOrderService orderService;

    @GetMapping("/list")
    @ApiOperation("获取返利订单列表")
    public Result<PageResult<CashbackOrderVO>> getOrderList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String platform,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<CashbackOrderVO> orders = orderService.getOrderList(
            userId, page, size, platform, status, startDate, endDate);
        return Result.success(orders);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取返利订单详情")
    public Result<CashbackOrderDetailVO> getOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        CashbackOrderDetailVO order = orderService.getOrderById(userId, id);
        return Result.success(order);
    }

    @PostMapping("/sync")
    @ApiOperation("同步平台订单")
    public Result<Void> syncPlatformOrders(@RequestBody @Valid OrderSyncDTO syncDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.syncPlatformOrders(userId, syncDTO);
        return Result.success();
    }

    @PostMapping("/{id}/claim")
    @ApiOperation("申请返利")
    public Result<Void> claimCashback(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.claimCashback(userId, id);
        return Result.success();
    }

    @GetMapping("/statistics")
    @ApiOperation("获取返利订单统计")
    public Result<CashbackOrderStatisticsVO> getOrderStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        CashbackOrderStatisticsVO statistics = orderService.getOrderStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }

    @GetMapping("/trend")
    @ApiOperation("获取返利订单趋势")
    public Result<List<CashbackOrderTrendVO>> getOrderTrend(
            @RequestParam String period,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<CashbackOrderTrendVO> trend = orderService.getOrderTrend(userId, period, startDate, endDate);
        return Result.success(trend);
    }

    @PostMapping("/batch-sync")
    @ApiOperation("批量同步订单")
    public Result<BatchSyncResultVO> batchSyncOrders(@RequestBody @Valid BatchSyncDTO batchSyncDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        BatchSyncResultVO result = orderService.batchSyncOrders(userId, batchSyncDTO);
        return Result.success(result);
    }

    @GetMapping("/pending")
    @ApiOperation("获取待处理订单")
    public Result<PageResult<CashbackOrderVO>> getPendingOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<CashbackOrderVO> orders = orderService.getPendingOrders(userId, page, size);
        return Result.success(orders);
    }

    @GetMapping("/failed")
    @ApiOperation("获取失败订单")
    public Result<PageResult<CashbackOrderVO>> getFailedOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<CashbackOrderVO> orders = orderService.getFailedOrders(userId, page, size);
        return Result.success(orders);
    }

    @PostMapping("/{id}/retry")
    @ApiOperation("重试处理订单")
    public Result<Void> retryOrder(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        orderService.retryOrder(userId, id);
        return Result.success();
    }
}
```

### 16.3 返利钱包管理

#### 16.3.1 返利钱包API

```java
@RestController
@RequestMapping("/api/cashback/wallet")
@Api(tags = "返利钱包管理")
public class CashbackWalletController {

    @Autowired
    private CashbackWalletService walletService;

    @GetMapping("/balance")
    @ApiOperation("获取钱包余额")
    public Result<CashbackWalletVO> getWalletBalance() {
        Long userId = SecurityUtils.getCurrentUserId();
        CashbackWalletVO wallet = walletService.getWalletBalance(userId);
        return Result.success(wallet);
    }

    @GetMapping("/transactions")
    @ApiOperation("获取钱包交易记录")
    public Result<PageResult<WalletTransactionVO>> getTransactions(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<WalletTransactionVO> transactions = walletService.getTransactions(
            userId, page, size, type, startDate, endDate);
        return Result.success(transactions);
    }

    @GetMapping("/transaction/{id}")
    @ApiOperation("获取交易记录详情")
    public Result<WalletTransactionDetailVO> getTransaction(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        WalletTransactionDetailVO transaction = walletService.getTransactionById(userId, id);
        return Result.success(transaction);
    }

    @PostMapping("/withdraw")
    @ApiOperation("申请提现")
    public Result<WithdrawRecordVO> applyWithdraw(@RequestBody @Valid CashbackWithdrawDTO withdrawDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        WithdrawRecordVO withdraw = walletService.applyWithdraw(userId, withdrawDTO);
        return Result.success(withdraw);
    }

    @GetMapping("/withdraw/list")
    @ApiOperation("获取提现记录")
    public Result<PageResult<WithdrawRecordVO>> getWithdrawList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<WithdrawRecordVO> withdraws = walletService.getWithdrawList(userId, page, size, status);
        return Result.success(withdraws);
    }

    @PostMapping("/withdraw/{id}/cancel")
    @ApiOperation("取消提现申请")
    public Result<Void> cancelWithdraw(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        walletService.cancelWithdraw(userId, id);
        return Result.success();
    }

    @GetMapping("/statistics")
    @ApiOperation("获取钱包统计")
    public Result<WalletStatisticsVO> getWalletStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        WalletStatisticsVO statistics = walletService.getWalletStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }

    @GetMapping("/income-trend")
    @ApiOperation("获取收入趋势")
    public Result<List<WalletIncomeTrendVO>> getIncomeTrend(
            @RequestParam String period,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<WalletIncomeTrendVO> trend = walletService.getIncomeTrend(userId, period, startDate, endDate);
        return Result.success(trend);
    }

    @GetMapping("/withdraw-config")
    @ApiOperation("获取提现配置")
    public Result<WithdrawConfigVO> getWithdrawConfig() {
        WithdrawConfigVO config = walletService.getWithdrawConfig();
        return Result.success(config);
    }

    @PostMapping("/bind-account")
    @ApiOperation("绑定提现账户")
    public Result<Void> bindWithdrawAccount(@RequestBody @Valid WithdrawAccountDTO accountDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        walletService.bindWithdrawAccount(userId, accountDTO);
        return Result.success();
    }

    @GetMapping("/withdraw-accounts")
    @ApiOperation("获取提现账户列表")
    public Result<List<WithdrawAccountVO>> getWithdrawAccounts() {
        Long userId = SecurityUtils.getCurrentUserId();
        List<WithdrawAccountVO> accounts = walletService.getWithdrawAccounts(userId);
        return Result.success(accounts);
    }
}
```

## 17. 营销活动模块

### 17.1 活动管理

#### 17.1.1 活动API

```java
@RestController
@RequestMapping("/api/marketing/activity")
@Api(tags = "营销活动管理")
public class MarketingActivityController {

    @Autowired
    private MarketingActivityService activityService;

    @GetMapping("/list")
    @ApiOperation("获取营销活动列表")
    public Result<PageResult<MarketingActivityVO>> getActivityList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String keyword) {
        PageResult<MarketingActivityVO> activities = activityService.getActivityList(
            page, size, status, type, keyword);
        return Result.success(activities);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取营销活动详情")
    public Result<MarketingActivityDetailVO> getActivity(@PathVariable Long id) {
        MarketingActivityDetailVO activity = activityService.getActivityById(id);
        return Result.success(activity);
    }

    @PostMapping
    @ApiOperation("创建营销活动")
    public Result<MarketingActivityVO> createActivity(@RequestBody @Valid MarketingActivityDTO activityDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MarketingActivityVO activity = activityService.createActivity(userId, activityDTO);
        return Result.success(activity);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新营销活动")
    public Result<MarketingActivityVO> updateActivity(@PathVariable Long id,
                                                     @RequestBody @Valid MarketingActivityDTO activityDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MarketingActivityVO activity = activityService.updateActivity(userId, id, activityDTO);
        return Result.success(activity);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除营销活动")
    public Result<Void> deleteActivity(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        activityService.deleteActivity(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/publish")
    @ApiOperation("发布营销活动")
    public Result<Void> publishActivity(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        activityService.publishActivity(userId, id);
        return Result.success();
    }
    @PostMapping("/{id}/unpublish")
    @ApiOperation("下架营销活动")
    public Result<Void> unpublishActivity(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        activityService.unpublishActivity(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/participate")
    @ApiOperation("参与营销活动")
    public Result<ActivityParticipationVO> participateActivity(@PathVariable Long id,
                                                              @RequestBody @Valid ActivityParticipationDTO participationDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ActivityParticipationVO participation = activityService.participateActivity(userId, id, participationDTO);
        return Result.success(participation);
    }

    @GetMapping("/{id}/participants")
    @ApiOperation("获取活动参与者列表")
    public Result<PageResult<ActivityParticipantVO>> getActivityParticipants(
            @PathVariable Long id,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        PageResult<ActivityParticipantVO> participants = activityService.getActivityParticipants(id, page, size);
        return Result.success(participants);
    }

    @GetMapping("/{id}/statistics")
    @ApiOperation("获取活动统计")
    public Result<ActivityStatisticsVO> getActivityStatistics(@PathVariable Long id) {
        ActivityStatisticsVO statistics = activityService.getActivityStatistics(id);
        return Result.success(statistics);
    }

    @GetMapping("/my-activities")
    @ApiOperation("获取我参与的活动")
    public Result<PageResult<ActivityParticipationVO>> getMyActivities(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<ActivityParticipationVO> activities = activityService.getMyActivities(userId, page, size, status);
        return Result.success(activities);
    }

    @GetMapping("/types")
    @ApiOperation("获取活动类型列表")
    public Result<List<ActivityTypeVO>> getActivityTypes() {
        List<ActivityTypeVO> types = activityService.getActivityTypes();
        return Result.success(types);
    }

    @PostMapping("/{id}/share")
    @ApiOperation("分享活动")
    public Result<ActivityShareVO> shareActivity(@PathVariable Long id,
                                                @RequestBody @Valid ActivityShareDTO shareDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ActivityShareVO share = activityService.shareActivity(userId, id, shareDTO);
        return Result.success(share);
    }
}
````

### 17.2 优惠券管理

#### 17.2.1 优惠券API

```java
@RestController
@RequestMapping("/api/marketing/coupon")
@Api(tags = "优惠券管理")
public class CouponController {

    @Autowired
    private CouponService couponService;

    @GetMapping("/list")
    @ApiOperation("获取优惠券列表")
    public Result<PageResult<CouponVO>> getCouponList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Long merchantId) {
        PageResult<CouponVO> coupons = couponService.getCouponList(page, size, status, type, merchantId);
        return Result.success(coupons);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取优惠券详情")
    public Result<CouponDetailVO> getCoupon(@PathVariable Long id) {
        CouponDetailVO coupon = couponService.getCouponById(id);
        return Result.success(coupon);
    }

    @PostMapping
    @ApiOperation("创建优惠券")
    public Result<CouponVO> createCoupon(@RequestBody @Valid CouponDTO couponDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        CouponVO coupon = couponService.createCoupon(userId, couponDTO);
        return Result.success(coupon);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新优惠券")
    public Result<CouponVO> updateCoupon(@PathVariable Long id, @RequestBody @Valid CouponDTO couponDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        CouponVO coupon = couponService.updateCoupon(userId, id, couponDTO);
        return Result.success(coupon);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除优惠券")
    public Result<Void> deleteCoupon(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        couponService.deleteCoupon(userId, id);
        return Result.success();
    }

    @PostMapping("/{id}/receive")
    @ApiOperation("领取优惠券")
    public Result<UserCouponVO> receiveCoupon(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        UserCouponVO userCoupon = couponService.receiveCoupon(userId, id);
        return Result.success(userCoupon);
    }

    @GetMapping("/my-coupons")
    @ApiOperation("获取我的优惠券")
    public Result<PageResult<UserCouponVO>> getMyCoupons(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<UserCouponVO> coupons = couponService.getMyCoupons(userId, page, size, status);
        return Result.success(coupons);
    }

    @PostMapping("/my-coupons/{id}/use")
    @ApiOperation("使用优惠券")
    public Result<Void> useCoupon(@PathVariable Long id, @RequestBody @Valid CouponUseDTO useDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        couponService.useCoupon(userId, id, useDTO);
        return Result.success();
    }

    @GetMapping("/available")
    @ApiOperation("获取可用优惠券")
    public Result<List<CouponVO>> getAvailableCoupons(
            @RequestParam(required = false) Long merchantId,
            @RequestParam(required = false) Long productId,
            @RequestParam(required = false) BigDecimal orderAmount) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<CouponVO> coupons = couponService.getAvailableCoupons(userId, merchantId, productId, orderAmount);
        return Result.success(coupons);
    }

    @GetMapping("/{id}/statistics")
    @ApiOperation("获取优惠券统计")
    public Result<CouponStatisticsVO> getCouponStatistics(@PathVariable Long id) {
        CouponStatisticsVO statistics = couponService.getCouponStatistics(id);
        return Result.success(statistics);
    }

    @PostMapping("/batch-create")
    @ApiOperation("批量创建优惠券")
    public Result<List<CouponVO>> batchCreateCoupons(@RequestBody @Valid BatchCouponDTO batchCouponDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<CouponVO> coupons = couponService.batchCreateCoupons(userId, batchCouponDTO);
        return Result.success(coupons);
    }

    @PostMapping("/{id}/send")
    @ApiOperation("发送优惠券")
    public Result<Void> sendCoupon(@PathVariable Long id, @RequestBody @Valid CouponSendDTO sendDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        couponService.sendCoupon(userId, id, sendDTO);
        return Result.success();
    }
}
```

## 18. 会员系统模块

### 18.1 会员管理

#### 18.1.1 会员API

```java
@RestController
@RequestMapping("/api/member")
@Api(tags = "会员管理")
public class MemberController {

    @Autowired
    private MemberService memberService;

    @GetMapping("/info")
    @ApiOperation("获取会员信息")
    public Result<MemberInfoVO> getMemberInfo() {
        Long userId = SecurityUtils.getCurrentUserId();
        MemberInfoVO memberInfo = memberService.getMemberInfo(userId);
        return Result.success(memberInfo);
    }

    @PostMapping("/upgrade")
    @ApiOperation("升级会员")
    public Result<MemberInfoVO> upgradeMember(@RequestBody @Valid MemberUpgradeDTO upgradeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MemberInfoVO memberInfo = memberService.upgradeMember(userId, upgradeDTO);
        return Result.success(memberInfo);
    }

    @GetMapping("/levels")
    @ApiOperation("获取会员等级列表")
    public Result<List<MemberLevelVO>> getMemberLevels() {
        List<MemberLevelVO> levels = memberService.getMemberLevels();
        return Result.success(levels);
    }

    @GetMapping("/benefits")
    @ApiOperation("获取会员权益")
    public Result<List<MemberBenefitVO>> getMemberBenefits(@RequestParam(required = false) Integer levelId) {
        List<MemberBenefitVO> benefits = memberService.getMemberBenefits(levelId);
        return Result.success(benefits);
    }

    @GetMapping("/growth-records")
    @ApiOperation("获取成长值记录")
    public Result<PageResult<MemberGrowthRecordVO>> getGrowthRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<MemberGrowthRecordVO> records = memberService.getGrowthRecords(userId, page, size);
        return Result.success(records);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取会员统计")
    public Result<MemberStatisticsVO> getMemberStatistics() {
        Long userId = SecurityUtils.getCurrentUserId();
        MemberStatisticsVO statistics = memberService.getMemberStatistics(userId);
        return Result.success(statistics);
    }

    @PostMapping("/sign-in")
    @ApiOperation("会员签到")
    public Result<MemberSignInVO> signIn() {
        Long userId = SecurityUtils.getCurrentUserId();
        MemberSignInVO signIn = memberService.signIn(userId);
        return Result.success(signIn);
    }

    @GetMapping("/sign-in-records")
    @ApiOperation("获取签到记录")
    public Result<List<MemberSignInRecordVO>> getSignInRecords(
            @RequestParam(required = false) String month) {
        Long userId = SecurityUtils.getCurrentUserId();
        List<MemberSignInRecordVO> records = memberService.getSignInRecords(userId, month);
        return Result.success(records);
    }

    @GetMapping("/tasks")
    @ApiOperation("获取会员任务")
    public Result<List<MemberTaskVO>> getMemberTasks() {
        Long userId = SecurityUtils.getCurrentUserId();
        List<MemberTaskVO> tasks = memberService.getMemberTasks(userId);
        return Result.success(tasks);
    }

    @PostMapping("/tasks/{id}/complete")
    @ApiOperation("完成会员任务")
    public Result<MemberTaskCompletionVO> completeTask(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        MemberTaskCompletionVO completion = memberService.completeTask(userId, id);
        return Result.success(completion);
    }

    @GetMapping("/privileges")
    @ApiOperation("获取会员特权")
    public Result<List<MemberPrivilegeVO>> getMemberPrivileges() {
        Long userId = SecurityUtils.getCurrentUserId();
        List<MemberPrivilegeVO> privileges = memberService.getMemberPrivileges(userId);
        return Result.success(privileges);
    }

    @PostMapping("/privileges/{id}/use")
    @ApiOperation("使用会员特权")
    public Result<Void> usePrivilege(@PathVariable Long id, @RequestBody @Valid PrivilegeUseDTO useDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        memberService.usePrivilege(userId, id, useDTO);
        return Result.success();
    }
}
```

## 19. 积分系统模块

### 19.1 积分管理

#### 19.1.1 积分API

```java
@RestController
@RequestMapping("/api/points")
@Api(tags = "积分管理")
public class PointsController {

    @Autowired
    private PointsService pointsService;

    @GetMapping("/balance")
    @ApiOperation("获取积分余额")
    public Result<PointsBalanceVO> getPointsBalance() {
        Long userId = SecurityUtils.getCurrentUserId();
        PointsBalanceVO balance = pointsService.getPointsBalance(userId);
        return Result.success(balance);
    }

    @GetMapping("/records")
    @ApiOperation("获取积分记录")
    public Result<PageResult<PointsRecordVO>> getPointsRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<PointsRecordVO> records = pointsService.getPointsRecords(
            userId, page, size, type, startDate, endDate);
        return Result.success(records);
    }

    @PostMapping("/earn")
    @ApiOperation("获得积分")
    public Result<PointsEarnVO> earnPoints(@RequestBody @Valid PointsEarnDTO earnDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PointsEarnVO earn = pointsService.earnPoints(userId, earnDTO);
        return Result.success(earn);
    }

    @PostMapping("/spend")
    @ApiOperation("消费积分")
    public Result<PointsSpendVO> spendPoints(@RequestBody @Valid PointsSpendDTO spendDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PointsSpendVO spend = pointsService.spendPoints(userId, spendDTO);
        return Result.success(spend);
    }

    @GetMapping("/rules")
    @ApiOperation("获取积分规则")
    public Result<List<PointsRuleVO>> getPointsRules() {
        List<PointsRuleVO> rules = pointsService.getPointsRules();
        return Result.success(rules);
    }

    @GetMapping("/mall")
    @ApiOperation("获取积分商城商品")
    public Result<PageResult<PointsMallItemVO>> getPointsMallItems(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String keyword) {
        PageResult<PointsMallItemVO> items = pointsService.getPointsMallItems(page, size, categoryId, keyword);
        return Result.success(items);
    }

    @PostMapping("/mall/{id}/exchange")
    @ApiOperation("积分兑换")
    public Result<PointsExchangeVO> exchangeItem(@PathVariable Long id,
                                                @RequestBody @Valid PointsExchangeDTO exchangeDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PointsExchangeVO exchange = pointsService.exchangeItem(userId, id, exchangeDTO);
        return Result.success(exchange);
    }

    @GetMapping("/exchanges")
    @ApiOperation("获取兑换记录")
    public Result<PageResult<PointsExchangeRecordVO>> getExchangeRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<PointsExchangeRecordVO> records = pointsService.getExchangeRecords(userId, page, size, status);
        return Result.success(records);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取积分统计")
    public Result<PointsStatisticsVO> getPointsStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PointsStatisticsVO statistics = pointsService.getPointsStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }

    @GetMapping("/expiring")
    @ApiOperation("获取即将过期的积分")
    public Result<List<PointsExpiringVO>> getExpiringPoints() {
        Long userId = SecurityUtils.getCurrentUserId();
        List<PointsExpiringVO> expiring = pointsService.getExpiringPoints(userId);
        return Result.success(expiring);
    }

    @PostMapping("/transfer")
    @ApiOperation("积分转赠")
    public Result<PointsTransferVO> transferPoints(@RequestBody @Valid PointsTransferDTO transferDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PointsTransferVO transfer = pointsService.transferPoints(userId, transferDTO);
        return Result.success(transfer);
    }

    @GetMapping("/transfer-records")
    @ApiOperation("获取转赠记录")
    public Result<PageResult<PointsTransferRecordVO>> getTransferRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String type) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<PointsTransferRecordVO> records = pointsService.getTransferRecords(userId, page, size, type);
        return Result.success(records);
    }
}
```

## 20. 消息通知模块

### 20.1 消息管理

#### 20.1.1 消息API

```java
@RestController
@RequestMapping("/api/message")
@Api(tags = "消息管理")
public class MessageController {

    @Autowired
    private MessageService messageService;

    @GetMapping("/list")
    @ApiOperation("获取消息列表")
    public Result<PageResult<MessageVO>> getMessageList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<MessageVO> messages = messageService.getMessageList(userId, page, size, type, status);
        return Result.success(messages);
    }

    @GetMapping("/{id}")
    @ApiOperation("获取消息详情")
    public Result<MessageDetailVO> getMessage(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        MessageDetailVO message = messageService.getMessageById(userId, id);
        return Result.success(message);
    }

    @PostMapping("/{id}/read")
    @ApiOperation("标记消息为已读")
    public Result<Void> markAsRead(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        messageService.markAsRead(userId, id);
        return Result.success();
    }

    @PostMapping("/batch-read")
    @ApiOperation("批量标记消息为已读")
    public Result<Void> batchMarkAsRead(@RequestBody @Valid BatchReadDTO batchReadDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        messageService.batchMarkAsRead(userId, batchReadDTO);
        return Result.success();
    }

    @PostMapping("/read-all")
    @ApiOperation("标记所有消息为已读")
    public Result<Void> markAllAsRead() {
        Long userId = SecurityUtils.getCurrentUserId();
        messageService.markAllAsRead(userId);
        return Result.success();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除消息")
    public Result<Void> deleteMessage(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        messageService.deleteMessage(userId, id);
        return Result.success();
    }

    @PostMapping("/batch-delete")
    @ApiOperation("批量删除消息")
    public Result<Void> batchDeleteMessages(@RequestBody @Valid BatchDeleteDTO batchDeleteDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        messageService.batchDeleteMessages(userId, batchDeleteDTO);
        return Result.success();
    }

    @GetMapping("/unread-count")
    @ApiOperation("获取未读消息数量")
    public Result<MessageUnreadCountVO> getUnreadCount() {
        Long userId = SecurityUtils.getCurrentUserId();
        MessageUnreadCountVO count = messageService.getUnreadCount(userId);
        return Result.success(count);
    }

    @GetMapping("/types")
    @ApiOperation("获取消息类型列表")
    public Result<List<MessageTypeVO>> getMessageTypes() {
        List<MessageTypeVO> types = messageService.getMessageTypes();
        return Result.success(types);
    }

    @PostMapping("/send")
    @ApiOperation("发送消息")
    public Result<MessageVO> sendMessage(@RequestBody @Valid MessageSendDTO sendDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MessageVO message = messageService.sendMessage(userId, sendDTO);
        return Result.success(message);
    }

    @GetMapping("/settings")
    @ApiOperation("获取消息设置")
    public Result<MessageSettingsVO> getMessageSettings() {
        Long userId = SecurityUtils.getCurrentUserId();
        MessageSettingsVO settings = messageService.getMessageSettings(userId);
        return Result.success(settings);
    }

    @PutMapping("/settings")
    @ApiOperation("更新消息设置")
    public Result<MessageSettingsVO> updateMessageSettings(@RequestBody @Valid MessageSettingsDTO settingsDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        MessageSettingsVO settings = messageService.updateMessageSettings(userId, settingsDTO);
        return Result.success(settings);
    }
}
```

### 20.2 推送通知

#### 20.2.1 推送API

```java
@RestController
@RequestMapping("/api/push")
@Api(tags = "推送通知")
public class PushController {

    @Autowired
    private PushService pushService;

    @PostMapping("/register")
    @ApiOperation("注册推送设备")
    public Result<Void> registerDevice(@RequestBody @Valid DeviceRegisterDTO registerDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        pushService.registerDevice(userId, registerDTO);
        return Result.success();
    }

    @PostMapping("/unregister")
    @ApiOperation("注销推送设备")
    public Result<Void> unregisterDevice(@RequestBody @Valid DeviceUnregisterDTO unregisterDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        pushService.unregisterDevice(userId, unregisterDTO);
        return Result.success();
    }

    @PostMapping("/send")
    @ApiOperation("发送推送")
    public Result<PushResultVO> sendPush(@RequestBody @Valid PushSendDTO sendDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PushResultVO result = pushService.sendPush(userId, sendDTO);
        return Result.success(result);
    }

    @GetMapping("/history")
    @ApiOperation("获取推送历史")
    public Result<PageResult<PushHistoryVO>> getPushHistory(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<PushHistoryVO> history = pushService.getPushHistory(userId, page, size, status);
        return Result.success(history);
    }

    @GetMapping("/settings")
    @ApiOperation("获取推送设置")
    public Result<PushSettingsVO> getPushSettings() {
        Long userId = SecurityUtils.getCurrentUserId();
        PushSettingsVO settings = pushService.getPushSettings(userId);
        return Result.success(settings);
    }

    @PutMapping("/settings")
    @ApiOperation("更新推送设置")
    public Result<PushSettingsVO> updatePushSettings(@RequestBody @Valid PushSettingsDTO settingsDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        PushSettingsVO settings = pushService.updatePushSettings(userId, settingsDTO);
        return Result.success(settings);
    }

    @GetMapping("/statistics")
    @ApiOperation("获取推送统计")
    public Result<PushStatisticsVO> getPushStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Long userId = SecurityUtils.getCurrentUserId();
        PushStatisticsVO statistics = pushService.getPushStatistics(userId, startDate, endDate);
        return Result.success(statistics);
    }

    @PostMapping("/test")
    @ApiOperation("测试推送")
    public Result<Void> testPush(@RequestBody @Valid PushTestDTO testDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        pushService.testPush(userId, testDTO);
        return Result.success();
    }
}
```

## 21. 客服系统模块

### 21.1 客服管理

#### 21.1.1 客服API

```java
@RestController
@RequestMapping("/api/customer-service")
@Api(tags = "客服管理")
public class CustomerServiceController {

    @Autowired
    private CustomerServiceService customerServiceService;

    @PostMapping("/conversation/start")
    @ApiOperation("开始客服对话")
    public Result<ConversationVO> startConversation(@RequestBody @Valid ConversationStartDTO startDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ConversationVO conversation = customerServiceService.startConversation(userId, startDTO);
        return Result.success(conversation);
    }

    @GetMapping("/conversation/{id}")
    @ApiOperation("获取对话详情")
    public Result<ConversationDetailVO> getConversation(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        ConversationDetailVO conversation = customerServiceService.getConversationById(userId, id);
        return Result.success(conversation);
    }

    @PostMapping("/conversation/{id}/message")
    @ApiOperation("发送消息")
    public Result<ServiceMessageVO> sendMessage(@PathVariable Long id,
                                               @RequestBody @Valid ServiceMessageDTO messageDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        ServiceMessageVO message = customerServiceService.sendMessage(userId, id, messageDTO);
        return Result.success(message);
    }

    @GetMapping("/conversation/{id}/messages")
    @ApiOperation("获取对话消息")
    public Result<PageResult<ServiceMessageVO>> getMessages(
            @PathVariable Long id,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<ServiceMessageVO> messages = customerServiceService.getMessages(userId, id, page, size);
        return Result.success(messages);
    }

    @PostMapping("/conversation/{id}/close")
    @ApiOperation("结束对话")
    public Result<Void> closeConversation(@PathVariable Long id) {
        Long userId = SecurityUtils.getCurrentUserId();
        customerServiceService.closeConversation(userId, id);
        return Result.success();
    }

    @PostMapping("/conversation/{id}/evaluate")
    @ApiOperation("评价客服")
    public Result<Void> evaluateService(@PathVariable Long id,
                                       @RequestBody @Valid ServiceEvaluationDTO evaluationDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        customerServiceService.evaluateService(userId, id, evaluationDTO);
        return Result.success();
    }

    @GetMapping("/conversations")
    @ApiOperation("获取我的对话列表")
    public Result<PageResult<ConversationVO>> getMyConversations(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<ConversationVO> conversations = customerServiceService.getMyConversations(userId, page, size, status);
        return Result.success(conversations);
    }

    @GetMapping("/faq")
    @ApiOperation("获取常见问题")
    public Result<List<FaqVO>> getFaq(@RequestParam(required = false) Long categoryId) {
        List<FaqVO> faq = customerServiceService.getFaq(categoryId);
        return Result.success(faq);
    }

    @GetMapping("/faq/categories")
    @ApiOperation("获取FAQ分类")
    public Result<List<FaqCategoryVO>> getFaqCategories() {
        List<FaqCategoryVO> categories = customerServiceService.getFaqCategories();
        return Result.success(categories);
    }

    @PostMapping("/feedback")
    @ApiOperation("提交反馈")
    public Result<FeedbackVO> submitFeedback(@RequestBody @Valid FeedbackDTO feedbackDTO) {
        Long userId = SecurityUtils.getCurrentUserId();
        FeedbackVO feedback = customerServiceService.submitFeedback(userId, feedbackDTO);
        return Result.success(feedback);
    }

    @GetMapping("/feedback/list")
    @ApiOperation("获取我的反馈列表")
    public Result<PageResult<FeedbackVO>> getMyFeedback(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getCurrentUserId();
        PageResult<FeedbackVO> feedback = customerServiceService.getMyFeedback(userId, page, size, status);
        return Result.success(feedback);
    }

    @GetMapping("/online-status")
    @ApiOperation("获取客服在线状态")
    public Result<ServiceOnlineStatusVO> getOnlineStatus() {
        ServiceOnlineStatusVO status = customerServiceService.getOnlineStatus();
        return Result.success(status);
    }
}
```

## 22. 评价系统模块

### 22.1 评价管理

#### 22.1.1 评价API

```java
@Rest
