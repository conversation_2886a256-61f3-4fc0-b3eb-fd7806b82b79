<template>
  <view class="agreement-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">分销协议</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 协议开关卡片 -->
    <view class="settings-card">
      <view class="form-item">
        <text class="form-label">启用分销协议</text>
        <view class="form-switch">
          <switch :checked="agreementEnabled" @change="toggleAgreement" color="#6B0FBE" />
        </view>
      </view>
      
      <view class="form-tip" v-if="agreementEnabled">
        <text class="tip-text">启用后，用户申请成为分销员时需要同意该协议</text>
      </view>
    </view>
    
    <!-- 协议编辑卡片 -->
    <view class="editor-card" v-if="agreementEnabled">
      <view class="card-header">
        <text class="card-title">协议内容</text>
      </view>
      
      <view class="editor-toolbar">
        <view class="toolbar-item" @click="formatText('bold')" :class="{ active: formats.bold }">
          <view class="toolbar-icon bold">B</view>
        </view>
        <view class="toolbar-item" @click="formatText('italic')" :class="{ active: formats.italic }">
          <view class="toolbar-icon italic">I</view>
        </view>
        <view class="toolbar-item" @click="formatText('underline')" :class="{ active: formats.underline }">
          <view class="toolbar-icon underline">U</view>
        </view>
        <view class="toolbar-separator"></view>
        <view class="toolbar-item" @click="formatText('align', 'left')" :class="{ active: formats.align === 'left' }">
          <view class="toolbar-icon align-left">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 6H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M3 12H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        <view class="toolbar-item" @click="formatText('align', 'center')" :class="{ active: formats.align === 'center' }">
          <view class="toolbar-icon align-center">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 6H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M6 12H18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        <view class="toolbar-item" @click="formatText('align', 'right')" :class="{ active: formats.align === 'right' }">
          <view class="toolbar-icon align-right">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 6H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 12H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        <view class="toolbar-separator"></view>
        <view class="toolbar-item" @click="insertTemplate">
          <view class="toolbar-icon template">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M10 9H9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
      </view>
      
      <view class="editor-content">
        <editor id="editor" class="rich-editor" :placeholder="'请输入分销协议内容...'" @ready="onEditorReady" @input="onEditorInput"></editor>
      </view>
    </view>
    
    <!-- 协议预览卡片 -->
    <view class="preview-card" v-if="agreementEnabled">
      <view class="card-header">
        <text class="card-title">协议预览</text>
        <view class="preview-button" @click="previewAgreement">预览</view>
      </view>
      
      <view class="preview-content">
        <image class="preview-image" src="/static/images/agreement-preview.png" mode="widthFix"></image>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="button-container">
      <button class="save-button" @click="saveSettings">保存设置</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';

// 是否启用分销协议
const agreementEnabled = ref(true);

// 富文本编辑器
const editorCtx = ref(null);

// 编辑器格式
const formats = reactive({
  bold: false,
  italic: false,
  underline: false,
  align: 'left'
});

// 默认协议内容
const defaultAgreement = `<h2 style="text-align: center; font-weight: bold; margin-bottom: 20px;">分销员协议</h2>
<p style="text-indent: 2em; margin-bottom: 10px;">尊敬的用户，欢迎您申请成为我们的分销员。在您申请成为分销员之前，请您仔细阅读本协议的全部内容。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">一、分销员资格</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">1.1 您确认，您在申请成为分销员时，已经年满18周岁，具有完全民事行为能力。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">1.2 您承诺，您提供的所有注册资料均真实、完整、准确，并承诺及时更新相关资料。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">二、分销规则</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">2.1 分销员可以获得的佣金比例、结算方式等具体规则，以平台当时公示的规则为准。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">2.2 平台有权根据业务需要调整分销规则，调整后的规则将在平台公示，并自公示之日起生效。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">三、分销员义务</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">3.1 分销员应当遵守国家法律法规和平台规则，不得从事任何违法违规活动。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">3.2 分销员在推广过程中，不得夸大宣传、虚假宣传，不得侵犯他人合法权益。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">四、违约责任</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">4.1 如分销员违反本协议约定，平台有权视情节轻重，采取警告、暂停分销资格、永久取消分销资格等措施。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">4.2 如分销员的行为给平台或第三方造成损失，分销员应当承担赔偿责任。</p>

<h3 style="font-weight: bold; margin: 15px 0 10px 0;">五、协议修改</h3>
<p style="text-indent: 2em; margin-bottom: 10px;">5.1 平台有权根据业务需要修改本协议，修改后的协议将在平台公示，并自公示之日起生效。</p>
<p style="text-indent: 2em; margin-bottom: 10px;">5.2 如分销员不同意修改后的协议，可以申请终止分销员资格；如分销员继续使用分销功能，则视为同意修改后的协议。</p>

<p style="text-align: right; margin-top: 20px;">平台运营方：XXXX有限公司</p>
<p style="text-align: right;">日期：2023年XX月XX日</p>`;

// 页面加载
onMounted(() => {
  // 获取分销协议设置
  getAgreementSettings();
});

// 获取分销协议设置
const getAgreementSettings = () => {
  // 这里应该从API获取设置
  // 暂时使用模拟数据
};

// 切换分销协议
const toggleAgreement = (e) => {
  agreementEnabled.value = e.detail.value;
};

// 编辑器准备完成
const onEditorReady = () => {
  uni.createSelectorQuery()
    .select('#editor')
    .context((res) => {
      editorCtx.value = res.context;
      
      // 设置初始内容
      editorCtx.value.setContents({
        html: defaultAgreement,
        success: () => {
          console.log('设置内容成功');
        },
        fail: (err) => {
          console.error('设置内容失败:', err);
        }
      });
    })
    .exec();
};

// 编辑器内容变化
const onEditorInput = (e) => {
  // 可以在这里处理内容变化
  console.log('内容变化:', e.detail);
};

// 格式化文本
const formatText = (name, value) => {
  editorCtx.value.format(name, value);
  
  // 更新格式状态
  if (name === 'align') {
    formats.align = value;
  } else {
    formats[name] = !formats[name];
  }
};

// 插入模板
const insertTemplate = () => {
  uni.showActionSheet({
    itemList: ['插入分销规则模板', '插入佣金说明模板', '插入提现规则模板'],
    success: (res) => {
      let template = '';
      
      switch (res.tapIndex) {
        case 0:
          template = '<h3 style="font-weight: bold; margin: 15px 0 10px 0;">分销规则</h3><p style="text-indent: 2em; margin-bottom: 10px;">1. 分销员可获得一级佣金和二级佣金</p><p style="text-indent: 2em; margin-bottom: 10px;">2. 佣金比例根据分销员等级不同而不同</p>';
          break;
        case 1:
          template = '<h3 style="font-weight: bold; margin: 15px 0 10px 0;">佣金说明</h3><p style="text-indent: 2em; margin-bottom: 10px;">1. 佣金在订单完成后自动结算</p><p style="text-indent: 2em; margin-bottom: 10px;">2. 佣金可在"我的佣金"页面查看</p>';
          break;
        case 2:
          template = '<h3 style="font-weight: bold; margin: 15px 0 10px 0;">提现规则</h3><p style="text-indent: 2em; margin-bottom: 10px;">1. 最低提现金额为50元</p><p style="text-indent: 2em; margin-bottom: 10px;">2. 提现申请将在1-3个工作日内处理</p>';
          break;
      }
      
      editorCtx.value.insertText({
        text: template,
        success: () => {
          console.log('插入模板成功');
        }
      });
    }
  });
};

// 预览协议
const previewAgreement = () => {
  editorCtx.value.getContents({
    success: (res) => {
      const html = res.html;
      
      // 这里可以实现预览功能，例如打开一个新页面显示协议内容
      uni.showToast({
        title: '预览功能开发中',
        icon: 'none'
      });
    }
  });
};

// 保存设置
const saveSettings = () => {
  if (agreementEnabled.value) {
    editorCtx.value.getContents({
      success: (res) => {
        const html = res.html;
        
        // 这里应该调用API保存协议内容
        saveAgreementContent(html);
      }
    });
  } else {
    // 直接保存设置
    saveAgreementSetting();
  }
};

// 保存协议内容
const saveAgreementContent = (html) => {
  // 这里应该调用API保存协议内容
  uni.showLoading({
    title: '保存中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }, 1000);
};

// 保存协议设置
const saveAgreementSetting = () => {
  // 这里应该调用API保存协议设置
  uni.showLoading({
    title: '保存中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }, 1000);
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '分销协议帮助',
    content: '您可以设置分销员协议，用户申请成为分销员时需要同意该协议。您可以使用富文本编辑器编辑协议内容，也可以使用模板快速插入常用内容。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.agreement-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 卡片样式 */
.settings-card, .editor-card, .preview-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.preview-button {
  font-size: 14px;
  color: #6B0FBE;
  padding: 6px 12px;
  background-color: rgba(107, 15, 190, 0.1);
  border-radius: 16px;
}

/* 表单项样式 */
.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.form-label {
  font-size: 15px;
  color: #333;
}

.form-switch {
  height: 36px;
  display: flex;
  align-items: center;
}

.form-tip {
  padding: 0 16px 16px;
  border-top: 1px solid #F0F0F0;
}

.tip-text {
  font-size: 12px;
  color: #999;
}

/* 编辑器样式 */
.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #F0F0F0;
  overflow-x: auto;
}

.toolbar-item {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  border-radius: 6px;
  color: #666;
}

.toolbar-item.active {
  background-color: rgba(107, 15, 190, 0.1);
  color: #6B0FBE;
}

.toolbar-icon {
  font-size: 14px;
  font-weight: bold;
}

.toolbar-icon.bold {
  font-weight: bold;
}

.toolbar-icon.italic {
  font-style: italic;
}

.toolbar-icon.underline {
  text-decoration: underline;
}

.toolbar-separator {
  width: 1px;
  height: 24px;
  background-color: #EEEEEE;
  margin: 0 8px;
}

.editor-content {
  padding: 16px;
  min-height: 300px;
}

.rich-editor {
  width: 100%;
  min-height: 300px;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

/* 预览样式 */
.preview-content {
  padding: 16px;
}

.preview-image {
  width: 100%;
  border-radius: 8px;
}

/* 按钮样式 */
.button-container {
  margin: 24px 16px;
}

.save-button {
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
</style>