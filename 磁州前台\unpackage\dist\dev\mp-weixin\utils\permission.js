"use strict";
const common_vendor = require("../common/vendor.js");
const checkLocationPermission = () => {
  return new Promise((resolve) => {
    common_vendor.index.getSetting({
      success: (res) => {
        if (res.authSetting["scope.userLocation"] === true) {
          resolve("authorized");
        } else if (res.authSetting["scope.userLocation"] === false) {
          resolve("denied");
        } else {
          resolve("not_determined");
        }
      },
      fail: () => {
        resolve("not_determined");
      }
    });
  });
};
const openSettings = () => {
  return new Promise((resolve, reject) => {
    common_vendor.index.openSetting({
      success: (res) => {
        resolve(res.authSetting);
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};
exports.checkLocationPermission = checkLocationPermission;
exports.openSettings = openSettings;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/permission.js.map
