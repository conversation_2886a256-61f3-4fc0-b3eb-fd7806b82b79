/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.verify-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fc;
}

/* 顶部渐变背景 */
.top-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 25vh;
  background: linear-gradient(135deg, #0052cc, #1677ff);
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  z-index: 0;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  position: relative;
  z-index: 1;
}
.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24px;
  height: 24px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}
.navbar-right {
  width: 44px;
}

/* 内容区域 */
.content {
  flex: 1;
  position: relative;
  z-index: 1;
  padding: 0 20px 30px;
  box-sizing: border-box;
}

/* 认证说明卡片 */
.info-card {
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 8px 25px rgba(0, 82, 204, 0.1);
}
.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.info-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}
.info-title {
  font-size: 18px;
  font-weight: bold;
  color: #0052CC;
}
.info-content {
  margin-bottom: 12px;
}
.info-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}
.info-tips {
  background-color: #f0f8ff;
  border-radius: 8px;
  padding: 12px;
}
.tip-title {
  font-size: 14px;
  color: #0052CC;
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
}
.tip-item {
  font-size: 12px;
  color: #333;
  line-height: 1.6;
  display: block;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 8px 25px rgba(0, 82, 204, 0.08);
}
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  padding-bottom: 10px;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}
.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}
.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}
.form-input, .form-textarea {
  font-size: 14px;
  color: #333;
  background-color: #f7f7f7;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #eee;
  transition: border-color 0.3s;
}
.form-input:focus, .form-textarea:focus {
  border-color: #1677FF;
}
.form-textarea {
  height: 80px;
  width: auto;
}
.form-tip {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
}

/* 证件上传 */
.watermark-notice {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #0052cc;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  line-height: 1.5;
  margin-bottom: 16px;
}
.upload-item {
  margin-bottom: 20px;
}
.upload-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  display: block;
}
.upload-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}
.upload-wrapper {
  width: 100%;
  height: 180px;
  background-color: #f7f7f7;
  border-radius: 8px;
  border: 1px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}
.upload-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 8px;
}
.upload-text {
  font-size: 14px;
}
.preview-image {
  width: 100%;
  height: 100%;
}

/* 协议 */
.agreement-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
.agreement-label {
  display: flex;
  align-items: center;
}
.agreement-text {
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}
.agreement-link {
  font-size: 14px;
  color: #1677FF;
  margin-left: 4px;
}

/* 提交按钮 */
.submit-section {
  margin: 30px 0;
  text-align: center;
}
.submit-btn {
  height: 50px;
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  font-size: 16px;
  border-radius: 25px;
  box-shadow: 0 6px 12px rgba(0, 82, 204, 0.2);
  line-height: 50px;
}
.submit-btn-disabled {
  background: #ccc;
  box-shadow: none;
}
.submit-tip {
  font-size: 12px;
  color: #999;
  margin-top: 12px;
}