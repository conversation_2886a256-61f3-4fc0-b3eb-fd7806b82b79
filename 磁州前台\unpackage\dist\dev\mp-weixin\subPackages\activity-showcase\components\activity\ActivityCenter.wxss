
/* 活动中心模块基础样式 */
.activity-center.data-v-26a06813 {
  margin: 30rpx 0;
  padding-bottom: 20rpx;
}

/* 模块标题区 */
.activity-header.data-v-26a06813 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}
.title-container.data-v-26a06813 {
  display: flex;
  align-items: center;
}
.title-bar.data-v-26a06813 {
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(to bottom, #007aff, #5ac8fa);
  border-radius: 4rpx;
  margin-right: 16rpx;
}
.activity-title.data-v-26a06813 {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}
.activity-badge.data-v-26a06813 {
  margin-left: 16rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 20rpx;
}
.badge-text.data-v-26a06813 {
  font-size: 22rpx;
  color: #007aff;
}
.more-btn.data-v-26a06813 {
  display: flex;
  align-items: center;
  padding: 10rpx;
}
.more-text.data-v-26a06813 {
  font-size: 28rpx;
  color: #8e8e93;
}
.more-icon.data-v-26a06813 {
  margin-left: 4rpx;
  color: #8e8e93;
}

/* 标签导航栏 */
.activity-tabs-container.data-v-26a06813 {
  margin-bottom: 20rpx;
}
.activity-tabs.data-v-26a06813 {
  white-space: nowrap;
  padding: 0 20rpx;
}
.activity-tab.data-v-26a06813 {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 6rpx;
}
.activity-tab.data-v-26a06813::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: -1;
  transition: all 0.3s ease;
  opacity: 0;
  transform: scale(0.9);
}
.activity-tab.active.data-v-26a06813::before {
  background: linear-gradient(35deg, rgba(0, 122, 255, 0.12), rgba(90, 200, 250, 0.08));
  opacity: 1;
  transform: scale(1);
}
.tab-content.data-v-26a06813 {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}
.tab-icon.data-v-26a06813 {
  margin-bottom: 8rpx;
  color: #8e8e93;
  transition: all 0.3s ease;
  transform: translateY(0);
}
.activity-tab.active .tab-icon.data-v-26a06813 {
  color: #007aff;
  transform: translateY(-2rpx);
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 122, 255, 0.3));
}
.tab-text.data-v-26a06813 {
  font-size: 28rpx;
  color: #8e8e93;
  transition: all 0.3s ease;
  position: relative;
}
.activity-tab.active .tab-text.data-v-26a06813 {
  color: #007aff;
  font-weight: 600;
  text-shadow: 0 0 0.5px rgba(0, 122, 255, 0.3);
}
.tab-line.data-v-26a06813 {
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #007aff, #5ac8fa);
  border-radius: 4rpx;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 1rpx 3rpx rgba(0, 122, 255, 0.3);
}
.activity-tab.active .tab-line.data-v-26a06813 {
  width: 60rpx;
}
.tab-glow.data-v-26a06813 {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 40rpx;
  background: radial-gradient(ellipse at center, rgba(0, 122, 255, 0.2) 0%, rgba(0, 122, 255, 0) 70%);
  border-radius: 50%;
  z-index: 1;
  opacity: 0.7;
  filter: blur(4rpx);
}

/* 活动列表 */
.activity-scroll-container.data-v-26a06813 {
  position: relative;
  width: 100%;
  padding-bottom: 30rpx;
}
.activity-scroll.data-v-26a06813 {
  width: 100%;
  overflow: hidden;
}
.activity-list.data-v-26a06813 {
  display: flex;
  padding: 10rpx 0;
  box-sizing: border-box;
}
.activity-item-placeholder.data-v-26a06813 {
  flex-shrink: 0;
  height: 1px;
}
.activity-item.data-v-26a06813 {
  width: 600rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
  transition: all 0.3s ease;
  will-change: transform, opacity;
}
.activity-item.data-v-26a06813:last-child {
  margin-right: 20rpx; /* 确保最后一个卡片也有右边距 */
}

/* 轮播控制按钮 */
.carousel-controls.data-v-26a06813 {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 10rpx;
  transform: translateY(-50%);
  z-index: 10;
  pointer-events: none;
}
.control-btn.data-v-26a06813 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  color: #333333;
  transition: all 0.3s ease;
  pointer-events: auto;
  opacity: 0.7;
}
.control-btn.data-v-26a06813:active {
  transform: scale(0.9);
  background-color: rgba(255, 255, 255, 0.9);
}
.control-btn.disabled.data-v-26a06813 {
  opacity: 0.3;
  pointer-events: none;
}
.control-btn.data-v-26a06813:hover {
  opacity: 1;
}

/* 轮播指示器 */
.carousel-indicators.data-v-26a06813 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
}
.indicator-dot.data-v-26a06813 {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: rgba(142, 142, 147, 0.3);
  margin: 0 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}
.indicator-dot.active.data-v-26a06813 {
  width: 24rpx;
  background-color: #007aff;
}

/* 媒体查询 - 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
.activity-item.data-v-26a06813 {
    width: 550rpx;
}
}
@media screen and (min-width: 768px) {
.activity-item.data-v-26a06813 {
    width: 650rpx;
}
}
