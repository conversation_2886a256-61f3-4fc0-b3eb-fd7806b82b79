"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Math) {
  (CarpoolNav + ConfigurablePremiumActions)();
}
const CarpoolNav = () => "../../../../components/carpool-nav.js";
const ConfigurablePremiumActions = () => "../../../../components/premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "car-to-people",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const formData = common_vendor.ref({
      startPoint: "",
      endPoint: "",
      departureDate: "",
      departureTime: "",
      carType: "",
      availableSeats: "",
      price: "",
      contactName: "",
      contactPhone: "",
      remark: "",
      agreement: false,
      viaPoints: []
    });
    const seatsOptions = common_vendor.ref(["1个座位", "2个座位", "3个座位", "4个座位", "5个座位", "6个座位"]);
    const seatsIndex = common_vendor.ref(0);
    const publishMode = common_vendor.ref("ad");
    common_vendor.onMounted(() => {
      setStatusBarHeight();
    });
    const setStatusBarHeight = () => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight;
    };
    const chooseLocation = (type, index) => {
      common_vendor.index.chooseLocation({
        success: (res) => {
          if (type === "start") {
            formData.value.startPoint = res.name;
          } else if (type === "end") {
            formData.value.endPoint = res.name;
          } else if (type === "via" && typeof index === "number") {
            formData.value.viaPoints[index] = res.name;
          }
        }
      });
    };
    const onDateChange = (e) => {
      formData.value.departureDate = e.detail.value;
    };
    const onTimeChange = (e) => {
      formData.value.departureTime = e.detail.value;
    };
    const onSeatsChange = (e) => {
      seatsIndex.value = e.detail.value;
      formData.value.availableSeats = seatsOptions.value[seatsIndex.value];
    };
    const onAgreementChange = (e) => {
      formData.value.agreement = e.detail.value.length > 0;
    };
    const viewAgreement = () => {
      common_vendor.index.navigateTo({
        url: "/pages/carpool/agreement"
      });
    };
    const submitForm = () => {
      if (!formData.value.startPoint) {
        showToast("请输入出发地");
        return;
      }
      if (!formData.value.endPoint) {
        showToast("请输入目的地");
        return;
      }
      if (!formData.value.departureDate) {
        showToast("请选择出发日期");
        return;
      }
      if (!formData.value.departureTime) {
        showToast("请选择出发时间");
        return;
      }
      if (!formData.value.carType) {
        showToast("请输入车型");
        return;
      }
      if (!formData.value.availableSeats) {
        showToast("请选择空座数量");
        return;
      }
      if (!formData.value.contactPhone) {
        showToast("请输入手机号码");
        return;
      }
      if (!/^1\d{10}$/.test(formData.value.contactPhone)) {
        showToast("手机号码格式不正确");
        return;
      }
      if (!selectedOption.value) {
        showToast("请选择发布方式");
        return;
      }
      submitToServer();
    };
    const submitToServer = () => {
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const formDataToSubmit = {
        ...formData.value,
        // 过滤掉空的途径地点
        viaPoints: formData.value.viaPoints.filter((point) => point.trim() !== "")
      };
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/publish/car-to-people.vue:354", "提交的表单数据：", formDataToSubmit);
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const publishId = Date.now().toString();
        common_vendor.index.navigateTo({
          url: `/carpool-package/pages/carpool/publish/success?id=${publishId}&type=car-to-people&mode=${publishMode.value}`
        });
      }, 1e3);
    };
    const showToast = (title) => {
      common_vendor.index.showToast({
        title,
        icon: "none"
      });
    };
    const addViaPoint = () => {
      formData.value.viaPoints.push("");
    };
    const removeViaPoint = (index) => {
      formData.value.viaPoints.splice(index, 1);
    };
    const selectedOption = common_vendor.ref(null);
    const handleActionCompleted = (actionType, result) => {
      selectedOption.value = result;
      if (actionType === "ad") {
        publishMode.value = "ad";
      } else if (actionType === "paid") {
        publishMode.value = "premium";
      }
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/publish/car-to-people.vue:407", "选择的推广选项:", actionType, result);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "发布车找人信息"
        }),
        b: formData.value.startPoint,
        c: common_vendor.o(($event) => formData.value.startPoint = $event.detail.value),
        d: common_assets._imports_2$36,
        e: common_vendor.o(($event) => chooseLocation("start")),
        f: formData.value.endPoint,
        g: common_vendor.o(($event) => formData.value.endPoint = $event.detail.value),
        h: common_assets._imports_2$36,
        i: common_vendor.o(($event) => chooseLocation("end")),
        j: common_vendor.f(formData.value.viaPoints, (point, index, i0) => {
          return {
            a: formData.value.viaPoints[index],
            b: common_vendor.o(($event) => formData.value.viaPoints[index] = $event.detail.value, index),
            c: common_vendor.o(($event) => chooseLocation("via", index), index),
            d: common_vendor.o(($event) => removeViaPoint(index), index),
            e: index
          };
        }),
        k: common_assets._imports_2$36,
        l: formData.value.viaPoints.length < 3
      }, formData.value.viaPoints.length < 3 ? {
        m: common_vendor.o(addViaPoint)
      } : {}, {
        n: common_vendor.t(formData.value.departureDate || "请选择出发日期"),
        o: common_assets._imports_0$27,
        p: formData.value.departureDate,
        q: common_vendor.o(onDateChange),
        r: common_vendor.t(formData.value.departureTime || "请选择出发时间"),
        s: common_assets._imports_0$27,
        t: formData.value.departureTime,
        v: common_vendor.o(onTimeChange),
        w: formData.value.carType,
        x: common_vendor.o(($event) => formData.value.carType = $event.detail.value),
        y: common_vendor.t(formData.value.availableSeats || "请选择空座数量"),
        z: common_assets._imports_0$27,
        A: seatsOptions.value,
        B: seatsIndex.value,
        C: common_vendor.o(onSeatsChange),
        D: formData.value.price,
        E: common_vendor.o(($event) => formData.value.price = $event.detail.value),
        F: formData.value.contactName,
        G: common_vendor.o(($event) => formData.value.contactName = $event.detail.value),
        H: formData.value.contactPhone,
        I: common_vendor.o(($event) => formData.value.contactPhone = $event.detail.value),
        J: formData.value.remark,
        K: common_vendor.o(($event) => formData.value.remark = $event.detail.value),
        L: common_vendor.t(formData.value.remark.length),
        M: formData.value.agreement,
        N: common_vendor.o(viewAgreement),
        O: common_vendor.o(onAgreementChange),
        P: common_vendor.o(handleActionCompleted),
        Q: common_vendor.p({
          pageType: "publish",
          showMode: "direct"
        }),
        R: !formData.value.agreement,
        S: common_vendor.o(submitForm)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/publish/car-to-people.js.map
