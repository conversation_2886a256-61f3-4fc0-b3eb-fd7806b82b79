/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-1c538d68, html.data-v-1c538d68, #app.data-v-1c538d68, .index-container.data-v-1c538d68 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.city-news.data-v-1c538d68 {
  margin: 24rpx 30rpx 30rpx;
  position: relative;
  z-index: 2;
  background: #ffffff;
  border-radius: 35rpx;
  padding: 24rpx 20rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.section-header.data-v-1c538d68 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-title-wrap.data-v-1c538d68 {
  display: flex;
  align-items: center;
}
.section-title.data-v-1c538d68 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", sans-serif;
}
.blue-title.data-v-1c538d68 {
  color: #007AFF;
}
.more-link.data-v-1c538d68 {
  font-size: 26rpx;
  color: #007AFF;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}
.blue-link.data-v-1c538d68 {
  color: #007AFF;
}
.more-link.data-v-1c538d68:active {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(0.96);
}
.notice-bar.data-v-1c538d68 {
  display: flex;
  align-items: center;
  background-color: #F2F2F7;
  border-radius: 35rpx;
  padding: 16rpx 20rpx;
}
.notice-icon.data-v-1c538d68 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.notice-swiper.data-v-1c538d68 {
  flex: 1;
  height: 66rpx;
}
.notice-text.data-v-1c538d68 {
  font-size: 28rpx;
  color: #333;
  line-height: 66rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card-section.data-v-1c538d68 {
  margin-bottom: 20rpx;
}
.fade-in.data-v-1c538d68 {
  animation: fadeIn-1c538d68 0.5s ease-in-out;
}
@keyframes fadeIn-1c538d68 {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}