<template>
  <view class="custom-navbar" :style="navbarStyle">
    <view class="navbar-left" @tap="onBack">
      <view class="back-arrow"></view>
    </view>
    <text class="navbar-title">{{ title }}</text>
    <view class="navbar-right">
      <slot name="right"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomNavbar',
  props: {
    title: {
      type: String,
      default: ''
    },
    bgColor: {
      type: String,
      default: 'linear-gradient(135deg, #9040FF, #5E35B1)'
    }
  },
  computed: {
    navbarStyle() {
      return {
        background: this.bgColor
      };
    }
  },
  methods: {
    onBack() {
      this.$emit('back');
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss">
.custom-navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-left {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-arrow {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 