{"version": 3, "file": "create.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/flash/create.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xmbGFzaFxjcmVhdGUudnVl"], "sourcesContent": ["<!-- 秒杀活动创建页面 (create.vue) -->\n<template>\n  <view class=\"flash-create-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-left\">\n        <view class=\"back-button\" @tap=\"goBack\">\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M15 18L9 12L15 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </view>\n      </view>\n      <view class=\"navbar-title\">\n        <text class=\"title-text\">创建秒杀活动</text>\n      </view>\n      <view class=\"navbar-right\">\n        <view class=\"save-button\" @tap=\"saveFlashSale\">创建</view>\n      </view>\n    </view>\n    \n    <!-- 页面内容区域 -->\n    <scroll-view scroll-y class=\"content-area\">\n      <!-- 表单区域 -->\n      <view class=\"form-section\">\n        <!-- 基本信息 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">基本信息</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">活动名称</text>\n            <input class=\"form-input\" v-model=\"formData.name\" placeholder=\"请输入秒杀活动名称\" maxlength=\"30\" />\n            <text class=\"form-counter\">{{formData.name.length}}/30</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">商品图片</text>\n            <view class=\"image-uploader\">\n              <view \n                class=\"image-item\" \n                v-for=\"(image, index) in formData.images\" \n                :key=\"index\"\n              >\n                <image class=\"uploaded-image\" :src=\"image\" mode=\"aspectFill\"></image>\n                <view class=\"delete-icon\" @tap=\"removeImage(index)\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"rgba(0,0,0,0.5)\"/>\n                    <path d=\"M15 9L9 15M9 9L15 15\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n                  </svg>\n                </view>\n              </view>\n              <view class=\"upload-button\" @tap=\"addImage\" v-if=\"formData.images.length < 5\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M12 5V19M5 12H19\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n                </svg>\n              </view>\n            </view>\n            <text class=\"form-tip\">最多上传5张图片，建议尺寸750x750像素</text>\n          </view>\n        </view>\n        \n        <!-- 价格设置 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">价格设置</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">原价 (元)</text>\n            <input class=\"form-input\" type=\"digit\" v-model=\"formData.originalPrice\" placeholder=\"请输入商品原价\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">秒杀价 (元)</text>\n            <input class=\"form-input\" type=\"digit\" v-model=\"formData.flashPrice\" placeholder=\"请输入秒杀价格\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">折扣</text>\n            <text class=\"discount-value\">{{discountValue}}</text>\n          </view>\n        </view>\n        \n        <!-- 库存设置 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">库存设置</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">活动库存</text>\n            <input class=\"form-input\" type=\"number\" v-model=\"formData.stockTotal\" placeholder=\"请输入活动库存\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">每人限购</text>\n            <input class=\"form-input\" type=\"number\" v-model=\"formData.purchaseLimit\" placeholder=\"0表示不限购\" />\n            <text class=\"form-tip\">设置为0表示不限购</text>\n          </view>\n        </view>\n        \n        <!-- 时间设置 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">时间设置</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">开始时间</text>\n            <picker \n              mode=\"dateTime\" \n              :value=\"formData.startTime\" \n              start=\"2023-01-01 00:00\" \n              end=\"2030-12-31 23:59\" \n              @change=\"startTimeChange\"\n            >\n              <view class=\"picker-value\">\n                <text>{{formData.startTime || '请选择开始时间'}}</text>\n                <view class=\"arrow-icon\"></view>\n              </view>\n            </picker>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">结束时间</text>\n            <picker \n              mode=\"dateTime\" \n              :value=\"formData.endTime\" \n              start=\"2023-01-01 00:00\" \n              end=\"2030-12-31 23:59\" \n              @change=\"endTimeChange\"\n            >\n              <view class=\"picker-value\">\n                <text>{{formData.endTime || '请选择结束时间'}}</text>\n                <view class=\"arrow-icon\"></view>\n              </view>\n            </picker>\n            <text class=\"form-error\" v-if=\"timeError\">{{timeError}}</text>\n          </view>\n        </view>\n        \n        <!-- 活动规则 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">活动规则</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">活动规则</text>\n            <textarea class=\"form-textarea\" v-model=\"formData.rules\" placeholder=\"请输入活动规则说明\" maxlength=\"200\" />\n            <text class=\"form-counter\">{{formData.rules.length}}/200</text>\n          </view>\n        </view>\n        \n        <!-- 分销设置 -->\n        <view class=\"form-group\" v-if=\"hasMerchantDistribution\">\n          <view class=\"form-header\">分销设置</view>\n          \n          <distribution-setting \n            :initial-settings=\"formData.distributionSettings\"\n            @update=\"updateDistributionSettings\"\n          />\n        </view>\n        \n        <!-- 商品详情 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">商品详情</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">商品描述</text>\n            <textarea class=\"form-textarea\" v-model=\"formData.description\" placeholder=\"请输入商品描述\" maxlength=\"500\" />\n            <text class=\"form-counter\">{{formData.description.length}}/500</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">详情图片</text>\n            <view class=\"image-uploader\">\n              <view \n                class=\"image-item detail-image-item\" \n                v-for=\"(image, index) in formData.detailImages\" \n                :key=\"index\"\n              >\n                <image class=\"uploaded-image\" :src=\"image\" mode=\"aspectFill\"></image>\n                <view class=\"delete-icon\" @tap=\"removeDetailImage(index)\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"rgba(0,0,0,0.5)\"/>\n                    <path d=\"M15 9L9 15M9 9L15 15\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n                  </svg>\n                </view>\n              </view>\n              <view class=\"upload-button detail-upload\" @tap=\"addDetailImage\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M12 5V19M5 12H19\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n                </svg>\n              </view>\n            </view>\n            <text class=\"form-tip\">建议上传清晰的商品细节图</text>\n          </view>\n        </view>\n        \n        <!-- 活动提示 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">活动提示</view>\n          \n          <view class=\"tips-list\">\n            <view class=\"tip-item\">\n              <view class=\"tip-icon\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                  <path d=\"M12 16V12\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                  <path d=\"M12 8H12.01\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                </svg>\n              </view>\n              <view class=\"tip-content\">\n                <view class=\"tip-title\">选择热门时段</view>\n                <view class=\"tip-desc\">选择用户活跃度高的时段发布秒杀活动，如12:00-13:00或18:00-20:00</view>\n              </view>\n            </view>\n            <view class=\"tip-item\">\n              <view class=\"tip-icon\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                </svg>\n              </view>\n              <view class=\"tip-content\">\n                <view class=\"tip-title\">明确价格优势</view>\n                <view class=\"tip-desc\">秒杀商品价格应明显低于原价，建议达到5-7折的优惠幅度</view>\n              </view>\n            </view>\n            <view class=\"tip-item\">\n              <view class=\"tip-icon\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                </svg>\n              </view>\n              <view class=\"tip-content\">\n                <view class=\"tip-title\">提前预热宣传</view>\n                <view class=\"tip-desc\">秒杀活动发布前1-3天进行预热宣传，增加用户关注度</view>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 活动推广 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">\n            <text class=\"form-title\">活动发布</text>\n            <text class=\"form-subtitle\">选择活动发布方式</text>\n          </view>\n          \n          <MarketingPromotionActions \n            :activityId=\"formData.id || ''\" \n            activityType=\"flash\"\n            :showActions=\"['publish']\"\n            :publishModeOnly=\"true\"\n            @action-completed=\"handleActionCompleted\"\n          />\n        </view>\n      </view>\n      \n      <!-- 底部空间 -->\n      <view class=\"bottom-space\"></view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport MarketingPromotionActions from '../../../components/MarketingPromotionActions.vue'\nimport { ActivityService, ActivityType } from '@/common/services/ActivityService.js'\n\nexport default {\n  components: {\n    MarketingPromotionActions\n  },\n  data() {\n    return {\n      formData: {\n  name: '',\n  images: [],\n  originalPrice: '',\n  flashPrice: '',\n  stockTotal: '',\n  purchaseLimit: '0',\n  startTime: '',\n  endTime: '',\n        rules: '',\n  description: '',\n        detailImages: [],\n  distributionSettings: {\n    enabled: false,\n          commission: 5,\n          secondLevel: false,\n          secondCommission: 2\n        },\n        type: ActivityType.FLASH // 设置活动类型为秒杀\n      },\n      timeError: '',\n      hasMerchantDistribution: false\n  }\n  },\n  computed: {\n    // 计算折扣值\n    discountValue() {\n      if (!this.formData.originalPrice || !this.formData.flashPrice) {\n        return '-';\n  }\n  \n      const originalPrice = parseFloat(this.formData.originalPrice);\n      const flashPrice = parseFloat(this.formData.flashPrice);\n      \n      if (isNaN(originalPrice) || isNaN(flashPrice) || originalPrice <= 0) {\n        return '-';\n      }\n      \n      const discount = (flashPrice / originalPrice * 10).toFixed(1);\n      return `${discount}折`;\n    }\n  },\n  onLoad() {\n    // 检查商家是否开通了分销功能\n    this.checkMerchantDistribution();\n  },\n  methods: {\n// 返回上一页\n    goBack() {\n  uni.navigateBack();\n    },\n\n// 添加商品图片\n    addImage() {\n  uni.chooseImage({\n        count: 5 - this.formData.images.length,\n    sizeType: ['compressed'],\n    sourceType: ['album', 'camera'],\n    success: (res) => {\n          // 这里应该上传图片到服务器，获取URL\n          // 示例中直接使用本地临时路径\n          this.formData.images = [...this.formData.images, ...res.tempFilePaths];\n    }\n  });\n    },\n\n    // 删除商品图片\n    removeImage(index) {\n      this.formData.images.splice(index, 1);\n    },\n\n// 添加详情图片\n    addDetailImage() {\n  uni.chooseImage({\n    count: 9,\n    sizeType: ['compressed'],\n    sourceType: ['album', 'camera'],\n    success: (res) => {\n          // 这里应该上传图片到服务器，获取URL\n          // 示例中直接使用本地临时路径\n          this.formData.detailImages = [...this.formData.detailImages, ...res.tempFilePaths];\n    }\n  });\n    },\n\n    // 删除详情图片\n    removeDetailImage(index) {\n      this.formData.detailImages.splice(index, 1);\n    },\n\n// 开始时间变化\n    startTimeChange(e) {\n      this.formData.startTime = e.detail.value;\n      this.validateTime();\n    },\n\n// 结束时间变化\n    endTimeChange(e) {\n      this.formData.endTime = e.detail.value;\n      this.validateTime();\n    },\n\n// 验证时间\n    validateTime() {\n      if (this.formData.startTime && this.formData.endTime) {\n        const startTime = new Date(this.formData.startTime.replace(/-/g, '/'));\n        const endTime = new Date(this.formData.endTime.replace(/-/g, '/'));\n  \n        if (endTime <= startTime) {\n          this.timeError = '结束时间必须晚于开始时间';\n          return false;\n  } else {\n          this.timeError = '';\n          return true;\n        }\n      }\n      return true;\n    },\n    \n    // 检查表单是否有效\n    validateForm() {\n      if (!this.formData.name) {\n    uni.showToast({\n      title: '请输入活动名称',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n      if (this.formData.images.length === 0) {\n    uni.showToast({\n      title: '请上传至少一张商品图片',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n      if (!this.formData.originalPrice) {\n    uni.showToast({\n          title: '请输入商品原价',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n      if (!this.formData.flashPrice) {\n    uni.showToast({\n          title: '请输入秒杀价格',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n      if (!this.formData.stockTotal) {\n    uni.showToast({\n          title: '请输入活动库存',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n      if (!this.formData.startTime) {\n    uni.showToast({\n      title: '请选择开始时间',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n      if (!this.formData.endTime) {\n    uni.showToast({\n      title: '请选择结束时间',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n      return this.validateTime();\n    },\n    \n    // 保存秒杀活动\n    saveFlashSale() {\n      if (!this.validateForm()) {\n        return;\n  }\n  \n      // 显示加载提示\n      uni.showLoading({\n        title: '保存中...'\n      });\n\n      // 准备提交的数据\n      const activityData = {\n        ...this.formData,\n        coverImage: this.formData.images[0], // 设置封面图为第一张图片\n        soldCount: 0, // 初始销量为0\n        tag: '新品', // 默认标签\n        merchantId: this.getMerchantId() // 获取商家ID\n      };\n      \n      try {\n        // 使用ActivityService创建活动\n        const createdActivity = ActivityService.createActivity(activityData);\n        \n        // 隐藏加载提示\n        uni.hideLoading();\n        \n        // 显示成功提示\n    uni.showToast({\n          title: '活动创建成功',\n      icon: 'success'\n    });\n        \n        // 保存活动ID到表单数据中，用于后续发布\n        this.formData.id = createdActivity.id;\n        \n        // 显示发布提示\n        setTimeout(() => {\n          uni.showModal({\n            title: '活动已创建',\n            content: '您可以选择发布方式将活动展示给用户',\n            confirmText: '立即发布',\n            cancelText: '稍后发布',\n            success: (res) => {\n              if (res.confirm) {\n                // 用户选择立即发布，可以滚动到发布区域\n                // 或者直接触发发布操作\n              } else {\n                // 用户选择稍后发布，返回上一页\n                setTimeout(() => {\n                  uni.navigateBack();\n                }, 500);\n              }\n            }\n          });\n        }, 1000);\n      } catch (error) {\n        console.error('创建活动失败:', error);\n        uni.hideLoading();\n    uni.showToast({\n          title: '创建失败，请重试',\n          icon: 'none'\n    });\n  }\n    },\n    \n    // 获取商家ID\n    getMerchantId() {\n      // 实际项目中应该从用户信息或全局状态获取\n      return 'merchant_' + Math.floor(Math.random() * 1000);\n    },\n    \n    // 检查商家是否开通了分销功能\n    checkMerchantDistribution() {\n      // 实际项目中应该从API获取\n      // 这里模拟一个异步操作\n      setTimeout(() => {\n        this.hasMerchantDistribution = true;\n      }, 500);\n    },\n    \n    // 更新分销设置\n    updateDistributionSettings(settings) {\n      this.formData.distributionSettings = settings;\n    },\n    \n    // 处理活动发布操作完成\n    handleActionCompleted(eventType, data) {\n      console.log('活动发布操作完成', eventType, data);\n      \n      // 根据不同的发布方式处理\n      if (eventType === 'adPublish' || eventType === 'paidPublish') {\n        // 使用ActivityService发布活动\n        const publishOptions = {\n          publishType: eventType === 'adPublish' ? 'ad' : 'paid',\n          publishDuration: data.option?.duration || '1天',\n          publishAmount: data.amount || '0'\n        };\n        \n        try {\n          ActivityService.publishActivity(this.formData.id, publishOptions);\n          \n          uni.showToast({\n            title: '发布设置已保存',\n            icon: 'success'\n          });\n          \n          // 发布成功后延迟返回上一页\n          setTimeout(() => {\n            uni.navigateBack();\n          }, 1500);\n        } catch (error) {\n          console.error('发布活动失败:', error);\n          uni.showToast({\n            title: '发布失败，请重试',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n/* 页面容器 */\n.flash-create-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  position: relative;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\n  color: white;\n  padding: 48px 20px 16px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);\n}\n\n.navbar-left {\n  width: 40px;\n}\n\n.back-button {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n}\n\n.title-text {\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.navbar-right {\n  min-width: 40px;\n}\n\n.save-button {\n  font-size: 16px;\n  padding: 6px 12px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n}\n\n/* 内容区域 */\n.content-area {\n  flex: 1;\n  box-sizing: border-box;\n  height: calc(100vh - 80px - 60px);\n}\n\n/* 表单区域 */\n.form-section {\n  padding: 12px;\n}\n\n.form-group {\n  background: #FFFFFF;\n  border-radius: 8px;\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.form-header {\n  padding: 12px 16px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #333333;\n  border-bottom: 1px solid #F5F5F5;\n}\n\n.form-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #F5F5F5;\n  position: relative;\n}\n\n.form-item:last-child {\n  border-bottom: none;\n}\n\n.form-label {\n  display: block;\n  font-size: 14px;\n  color: #333333;\n  margin-bottom: 8px;\n}\n\n.required:after {\n  content: '*';\n  color: #FF3B30;\n  margin-left: 4px;\n}\n\n.form-input {\n  width: 100%;\n  height: 40px;\n  background: #F9F9F9;\n  border: 1px solid #EEEEEE;\n  border-radius: 4px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333333;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 100px;\n  background: #F9F9F9;\n  border: 1px solid #EEEEEE;\n  border-radius: 4px;\n  padding: 8px 12px;\n  font-size: 14px;\n  color: #333333;\n}\n\n.form-counter {\n  position: absolute;\n  right: 16px;\n  bottom: 12px;\n  font-size: 12px;\n  color: #999999;\n}\n\n.form-tip {\n  font-size: 12px;\n  color: #999999;\n  margin-top: 8px;\n  display: block;\n}\n\n.form-error {\n  font-size: 12px;\n  color: #FF3B30;\n  margin-top: 8px;\n  display: block;\n}\n\n.discount-value {\n  height: 40px;\n  line-height: 40px;\n  font-size: 16px;\n  color: #FF3B30;\n  font-weight: 600;\n}\n\n.picker-value {\n  width: 100%;\n  height: 40px;\n  background: #F9F9F9;\n  border: 1px solid #EEEEEE;\n  border-radius: 4px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333333;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.arrow-icon {\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-top: 5px solid #999999;\n}\n\n/* 图片上传 */\n.image-uploader {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -4px;\n}\n\n.image-item {\n  width: calc(25% - 8px);\n  margin: 4px;\n  position: relative;\n  aspect-ratio: 1;\n}\n\n.detail-image-item {\n  width: calc(33.33% - 8px);\n}\n\n.uploaded-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 4px;\n}\n\n.delete-icon {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  width: 20px;\n  height: 20px;\n  z-index: 10;\n}\n\n.upload-button {\n  width: calc(25% - 8px);\n  margin: 4px;\n  aspect-ratio: 1;\n  background: #F9F9F9;\n  border: 1px dashed #DDDDDD;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #999999;\n}\n\n.detail-upload {\n  width: calc(33.33% - 8px);\n}\n\n/* 活动提示 */\n.tips-list {\n  padding: 0 16px;\n}\n\n.tip-item {\n  display: flex;\n  padding: 12px 0;\n  border-bottom: 1px solid #F5F5F5;\n}\n\n.tip-item:last-child {\n  border-bottom: none;\n}\n\n.tip-icon {\n  width: 36px;\n  height: 36px;\n  margin-right: 12px;\n  color: #FF7600;\n}\n\n.tip-content {\n  flex: 1;\n}\n\n.tip-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 4px;\n}\n\n.tip-desc {\n  font-size: 14px;\n  color: #666666;\n  line-height: 1.5;\n}\n\n/* 活动推广样式 */\n.form-group .marketing-promotion-container {\n  margin-top: 10px;\n}\n\n/* 底部空间 */\n.bottom-space {\n  height: 80px;\n}\n\n/* 底部操作栏 */\n.bottom-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #FFFFFF;\n  padding: 10px 16px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  z-index: 90;\n  display: flex;\n}\n\n.action-button {\n  flex: 1;\n  height: 40px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 6px;\n}\n\n.action-button.preview {\n  background: #F5F5F5;\n  color: #666666;\n}\n\n.action-button.save {\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\n  color: white;\n}\n\n.button-text {\n  font-size: 16px;\n  font-weight: 500;\n}\n\n@media screen and (min-width: 768px) {\n  .form-section {\n    max-width: 600px;\n    margin: 0 auto;\n  }\n  \n  .bottom-bar {\n    max-width: 600px;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/flash/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ActivityType", "uni", "ActivityService"], "mappings": ";;;AAkQA,MAAK,4BAA6B,MAAW;AAG7C,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,QACd,MAAM;AAAA,QACN,QAAQ,CAAE;AAAA,QACV,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,WAAW;AAAA,QACX,SAAS;AAAA,QACH,OAAO;AAAA,QACb,aAAa;AAAA,QACP,cAAc,CAAE;AAAA,QACtB,sBAAsB;AAAA,UACpB,SAAS;AAAA,UACH,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,kBAAkB;AAAA,QACnB;AAAA,QACD,MAAMA,gCAAY,aAAC;AAAA;AAAA,MACpB;AAAA,MACD,WAAW;AAAA,MACX,yBAAyB;AAAA,IAC7B;AAAA,EACC;AAAA,EACD,UAAU;AAAA;AAAA,IAER,gBAAgB;AACd,UAAI,CAAC,KAAK,SAAS,iBAAiB,CAAC,KAAK,SAAS,YAAY;AAC7D,eAAO;AAAA,MACb;AAEI,YAAM,gBAAgB,WAAW,KAAK,SAAS,aAAa;AAC5D,YAAM,aAAa,WAAW,KAAK,SAAS,UAAU;AAEtD,UAAI,MAAM,aAAa,KAAK,MAAM,UAAU,KAAK,iBAAiB,GAAG;AACnE,eAAO;AAAA,MACT;AAEA,YAAM,YAAY,aAAa,gBAAgB,IAAI,QAAQ,CAAC;AAC5D,aAAO,GAAG,QAAQ;AAAA,IACpB;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,0BAAyB;AAAA,EAC/B;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACXC,oBAAG,MAAC,aAAY;AAAA,IACb;AAAA;AAAA,IAGD,WAAW;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACV,OAAO,IAAI,KAAK,SAAS,OAAO;AAAA,QACpC,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAGZ,eAAK,SAAS,SAAS,CAAC,GAAG,KAAK,SAAS,QAAQ,GAAG,IAAI,aAAa;AAAA,QAC3E;AAAA,MACF,CAAC;AAAA,IACE;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,WAAK,SAAS,OAAO,OAAO,OAAO,CAAC;AAAA,IACrC;AAAA;AAAA,IAGD,iBAAiB;AACnBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAGZ,eAAK,SAAS,eAAe,CAAC,GAAG,KAAK,SAAS,cAAc,GAAG,IAAI,aAAa;AAAA,QACvF;AAAA,MACF,CAAC;AAAA,IACE;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACvB,WAAK,SAAS,aAAa,OAAO,OAAO,CAAC;AAAA,IAC3C;AAAA;AAAA,IAGD,gBAAgB,GAAG;AACjB,WAAK,SAAS,YAAY,EAAE,OAAO;AACnC,WAAK,aAAY;AAAA,IAClB;AAAA;AAAA,IAGD,cAAc,GAAG;AACf,WAAK,SAAS,UAAU,EAAE,OAAO;AACjC,WAAK,aAAY;AAAA,IAClB;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,KAAK,SAAS,aAAa,KAAK,SAAS,SAAS;AACpD,cAAM,YAAY,IAAI,KAAK,KAAK,SAAS,UAAU,QAAQ,MAAM,GAAG,CAAC;AACrE,cAAM,UAAU,IAAI,KAAK,KAAK,SAAS,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAEjE,YAAI,WAAW,WAAW;AACxB,eAAK,YAAY;AACjB,iBAAO;AAAA,eACR;AACC,eAAK,YAAY;AACjB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,CAAC,KAAK,SAAS,MAAM;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEI,UAAI,KAAK,SAAS,OAAO,WAAW,GAAG;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEI,UAAI,CAAC,KAAK,SAAS,eAAe;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACR,OAAO;AAAA,UACX,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEI,UAAI,CAAC,KAAK,SAAS,YAAY;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACR,OAAO;AAAA,UACX,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEI,UAAI,CAAC,KAAK,SAAS,YAAY;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACR,OAAO;AAAA,UACX,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEI,UAAI,CAAC,KAAK,SAAS,WAAW;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEI,UAAI,CAAC,KAAK,SAAS,SAAS;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEI,aAAO,KAAK;IACb;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,MACN;AAGIA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,YAAM,eAAe;AAAA,QACnB,GAAG,KAAK;AAAA,QACR,YAAY,KAAK,SAAS,OAAO,CAAC;AAAA;AAAA,QAClC,WAAW;AAAA;AAAA,QACX,KAAK;AAAA;AAAA,QACL,YAAY,KAAK,cAAc;AAAA;AAAA;AAGjC,UAAI;AAEF,cAAM,kBAAkBC,gCAAAA,gBAAgB,eAAe,YAAY;AAGnED,sBAAG,MAAC,YAAW;AAGnBA,sBAAAA,MAAI,UAAU;AAAA,UACR,OAAO;AAAA,UACX,MAAM;AAAA,QACR,CAAC;AAGG,aAAK,SAAS,KAAK,gBAAgB;AAGnC,mBAAW,MAAM;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AAAA,qBAGV;AAEL,2BAAW,MAAM;AACfA,gCAAG,MAAC,aAAY;AAAA,gBACjB,GAAE,GAAG;AAAA,cACR;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACF,GAAE,GAAI;AAAA,MACP,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,gFAAc,WAAW,KAAK;AAC9BA,sBAAG,MAAC,YAAW;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACG;AAAA;AAAA,IAGD,gBAAgB;AAEd,aAAO,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI;AAAA,IACrD;AAAA;AAAA,IAGD,4BAA4B;AAG1B,iBAAW,MAAM;AACf,aAAK,0BAA0B;AAAA,MAChC,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,2BAA2B,UAAU;AACnC,WAAK,SAAS,uBAAuB;AAAA,IACtC;AAAA;AAAA,IAGD,sBAAsB,WAAW,MAAM;;AACrCA,oBAAY,MAAA,MAAA,OAAA,gFAAA,YAAY,WAAW,IAAI;AAGvC,UAAI,cAAc,eAAe,cAAc,eAAe;AAE5D,cAAM,iBAAiB;AAAA,UACrB,aAAa,cAAc,cAAc,OAAO;AAAA,UAChD,mBAAiB,UAAK,WAAL,mBAAa,aAAY;AAAA,UAC1C,eAAe,KAAK,UAAU;AAAA;AAGhC,YAAI;AACFC,0CAAe,gBAAC,gBAAgB,KAAK,SAAS,IAAI,cAAc;AAEhED,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAGD,qBAAW,MAAM;AACfA,0BAAG,MAAC,aAAY;AAAA,UACjB,GAAE,IAAI;AAAA,QACP,SAAO,OAAO;AACdA,wBAAc,MAAA,MAAA,SAAA,gFAAA,WAAW,KAAK;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvjBA,GAAG,WAAW,eAAe;"}