### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [v6.5.1](https://github.com/neutrinojs/webpack-chain/compare/v6.5.0...v6.5.1)

> 25 July 2020

- Improve error message when .tap() used on an undefined plugin [`#271`](https://github.com/neutrinojs/webpack-chain/pull/271)
- Improve error message when .use() not called for a Plugin [`#270`](https://github.com/neutrinojs/webpack-chain/pull/270)
- Clean up linting configuration [`#268`](https://github.com/neutrinojs/webpack-chain/pull/268)
- Remove unused dev-dependency @types/node [`#269`](https://github.com/neutrinojs/webpack-chain/pull/269)
- Lock file maintenance [`#259`](https://github.com/neutrinojs/webpack-chain/pull/259)
- Travis: Test against Node 14 [`#267`](https://github.com/neutrinojs/webpack-chain/pull/267)
- Improve error message when .tap() used on an undefined plugin (#271) [`#125`](https://github.com/neutrinojs/webpack-chain/issues/125)

#### [v6.5.0](https://github.com/neutrinojs/webpack-chain/compare/v6.4.0...v6.5.0)

> 1 July 2020

- Add rule.resolve [`#265`](https://github.com/neutrinojs/webpack-chain/pull/265)
- Update dependency eslint to v7 [`#263`](https://github.com/neutrinojs/webpack-chain/pull/263)
- Switch from Ava to Jest [`#258`](https://github.com/neutrinojs/webpack-chain/pull/258)
- Update dependency prettier to v2 [`#250`](https://github.com/neutrinojs/webpack-chain/pull/250)
- Update dependency auto-changelog to v2 [`#257`](https://github.com/neutrinojs/webpack-chain/pull/257)
- Lock file maintenance [`#244`](https://github.com/neutrinojs/webpack-chain/pull/244)
- Test against Node 13 [`#254`](https://github.com/neutrinojs/webpack-chain/pull/254)
- Switch to a newer Travis base image [`#253`](https://github.com/neutrinojs/webpack-chain/pull/253)

#### [v6.4.0](https://github.com/neutrinojs/webpack-chain/compare/v6.3.1...v6.4.0)

> 3 February 2020

- Fix Rule.merge() when include or exclude are strings [`#243`](https://github.com/neutrinojs/webpack-chain/pull/243)
- Add Types to Plugin Arguments [`#241`](https://github.com/neutrinojs/webpack-chain/pull/241)
- Add devServer shorthands for sockHost, sockPort, sockPath [`#238`](https://github.com/neutrinojs/webpack-chain/pull/238)
- Add `devtoolNamespace` TypeScript declaration [`#232`](https://github.com/neutrinojs/webpack-chain/pull/232)
- Fix Rule.merge() when include or exclude are strings (#243) [`#228`](https://github.com/neutrinojs/webpack-chain/issues/228)
- Add devServer shorthands for sockHost, sockPort, sockPath (#238) [`#231`](https://github.com/neutrinojs/webpack-chain/issues/231)

#### [v6.3.1](https://github.com/neutrinojs/webpack-chain/compare/v6.3.0...v6.3.1)

> 28 January 2020

- Lock file maintenance [`#217`](https://github.com/neutrinojs/webpack-chain/pull/217)
- docs: Emphasise that merge() doesn't accept webpack config objects [`#225`](https://github.com/neutrinojs/webpack-chain/pull/225)
- Update types and documentation for DevServer [`#233`](https://github.com/neutrinojs/webpack-chain/pull/233)
- Validate that Plugin 'args' is an array [`#229`](https://github.com/neutrinojs/webpack-chain/pull/229)
- Improve error message when legacy minimizer syntax used [`#226`](https://github.com/neutrinojs/webpack-chain/pull/226)
- Add missing engines definition to package.json [`#227`](https://github.com/neutrinojs/webpack-chain/pull/227)
- docs: Correct the .merge() example for optimization.minimizer [`#224`](https://github.com/neutrinojs/webpack-chain/pull/224)
- docs: Fix formatting typos in README [`#223`](https://github.com/neutrinojs/webpack-chain/pull/223)
- Validate that Plugin 'args' is an array (#229) [`#121`](https://github.com/neutrinojs/webpack-chain/issues/121)

#### [v6.3.0](https://github.com/neutrinojs/webpack-chain/compare/v6.2.0...v6.3.0)

> 22 December 2019

- Add support for nested rules (rule.rules) [`#220`](https://github.com/neutrinojs/webpack-chain/pull/220)
- Fix missing type TypedChainedMap.getOrCompute [`#221`](https://github.com/neutrinojs/webpack-chain/pull/221)
- fix: fix type definition for Rule#oneOf [`#218`](https://github.com/neutrinojs/webpack-chain/pull/218)
- fix: fix type definition for Rule#oneOf (#218) [`#216`](https://github.com/neutrinojs/webpack-chain/issues/216)

#### [v6.2.0](https://github.com/neutrinojs/webpack-chain/compare/v6.1.0...v6.2.0)

> 22 December 2019

- Add support for module.strictExportPresence and output.futureEmitAssets [`#207`](https://github.com/neutrinojs/webpack-chain/pull/207)
- Add support for module.strictExportPresence and output.futureEmitAssets (#207) [`#205`](https://github.com/neutrinojs/webpack-chain/issues/205) [`#206`](https://github.com/neutrinojs/webpack-chain/issues/206)

#### [v6.1.0](https://github.com/neutrinojs/webpack-chain/compare/v6.0.0...v6.1.0)

> 13 December 2019

- README: Update Travis badge to point to travis-ci.com [`#215`](https://github.com/neutrinojs/webpack-chain/pull/215)
- Lock file maintenance [`#199`](https://github.com/neutrinojs/webpack-chain/pull/199)
- Fix types for Config.resolve plugins and improve Plugin types [`#213`](https://github.com/neutrinojs/webpack-chain/pull/213)
- Update dependency eslint-plugin-ava to v9 [`#203`](https://github.com/neutrinojs/webpack-chain/pull/203)
- Add Chinese docs link to README [`#136`](https://github.com/neutrinojs/webpack-chain/pull/136)
- Lock file maintenance [`#196`](https://github.com/neutrinojs/webpack-chain/pull/196)
- Update dependency eslint to v6 [`#186`](https://github.com/neutrinojs/webpack-chain/pull/186)
- Update dependency eslint-config-airbnb-base to v14 [`#192`](https://github.com/neutrinojs/webpack-chain/pull/192)
- Lock file maintenance [`#184`](https://github.com/neutrinojs/webpack-chain/pull/184)
- Update dependency eslint-config-prettier to v6 [`#187`](https://github.com/neutrinojs/webpack-chain/pull/187)
- Lock file maintenance [`#180`](https://github.com/neutrinojs/webpack-chain/pull/180)
- Update dependency eslint-plugin-ava to v7 [`#178`](https://github.com/neutrinojs/webpack-chain/pull/178)
- Lock file maintenance [`#177`](https://github.com/neutrinojs/webpack-chain/pull/177)
- Lock file maintenance [`#176`](https://github.com/neutrinojs/webpack-chain/pull/176)
- Lock file maintenance [`#174`](https://github.com/neutrinojs/webpack-chain/pull/174)
- feat: rule test supports function [`#172`](https://github.com/neutrinojs/webpack-chain/pull/172)
- Lock file maintenance [`#170`](https://github.com/neutrinojs/webpack-chain/pull/170)

### [v6.0.0](https://github.com/neutrinojs/webpack-chain/compare/v5.2.4...v6.0.0)

> 3 May 2019

- Lock file maintenance [`#169`](https://github.com/neutrinojs/webpack-chain/pull/169)
- Update linting configuration, support Node.js 12 in CI [`#168`](https://github.com/neutrinojs/webpack-chain/pull/168)
- Extended DevServer method [`#167`](https://github.com/neutrinojs/webpack-chain/pull/167)
- Lock file maintenance [`#165`](https://github.com/neutrinojs/webpack-chain/pull/165)
- Update dependency eslint-plugin-ava to v6 [`#161`](https://github.com/neutrinojs/webpack-chain/pull/161)
- Point docs to v6 [`37201a2`](https://github.com/neutrinojs/webpack-chain/commit/37201a2974c078c3494243ff03342ba16910db21)

#### [v5.2.4](https://github.com/neutrinojs/webpack-chain/compare/v5.2.3...v5.2.4)

> 25 March 2019

- fix Use#end return type in OneOf [`#158`](https://github.com/neutrinojs/webpack-chain/pull/158)
- make __expression property works in any object [`#157`](https://github.com/neutrinojs/webpack-chain/pull/157)
- Lock file maintenance [`#160`](https://github.com/neutrinojs/webpack-chain/pull/160)
- docs: Fix typo in config.optimization example [`#159`](https://github.com/neutrinojs/webpack-chain/pull/159)

#### [v5.2.3](https://github.com/neutrinojs/webpack-chain/compare/v5.2.2...v5.2.3)

> 22 March 2019

- optimize type definitions [`#156`](https://github.com/neutrinojs/webpack-chain/pull/156)
- Lock file maintenance [`#155`](https://github.com/neutrinojs/webpack-chain/pull/155)

#### [v5.2.2](https://github.com/neutrinojs/webpack-chain/compare/v5.2.1...v5.2.2)

> 12 March 2019

- Fix README comment rendering [`#154`](https://github.com/neutrinojs/webpack-chain/pull/154)
- Lock file maintenance [`#153`](https://github.com/neutrinojs/webpack-chain/pull/153)
- Update dependency javascript-stringify to v2 [`#151`](https://github.com/neutrinojs/webpack-chain/pull/151)
- Fix stringify master bustage [`55f6a5d`](https://github.com/neutrinojs/webpack-chain/commit/55f6a5d25b58d03fbc57a17d8c2e09de310fa068)

#### [v5.2.1](https://github.com/neutrinojs/webpack-chain/compare/v5.2.0...v5.2.1)

> 7 March 2019

- Lock file maintenance [`#145`](https://github.com/neutrinojs/webpack-chain/pull/145)
- Add `config.output.globalObject` type [`#147`](https://github.com/neutrinojs/webpack-chain/pull/147)
- add module-rule-type [`#148`](https://github.com/neutrinojs/webpack-chain/pull/148)
- Update `config.mode` type [`#146`](https://github.com/neutrinojs/webpack-chain/pull/146)
- Update dependency eslint-config-prettier to v4 [`75aa60b`](https://github.com/neutrinojs/webpack-chain/commit/75aa60b760947cd8eb438691158dd9db52f4f9f5)

#### [v5.2.0](https://github.com/neutrinojs/webpack-chain/compare/v5.1.0...v5.2.0)

> 23 January 2019

- Add `config.name` type [`#143`](https://github.com/neutrinojs/webpack-chain/pull/143)
- Add TypeScript type definitions [`#132`](https://github.com/neutrinojs/webpack-chain/pull/132)
- docs: Fix typo of 'optimization' [`#139`](https://github.com/neutrinojs/webpack-chain/pull/139)
- Add TypeScript type definitions (#132) [`#62`](https://github.com/neutrinojs/webpack-chain/issues/62)

#### [v5.1.0](https://github.com/neutrinojs/webpack-chain/compare/v5.0.1...v5.1.0)

> 16 January 2019

- Support config.name() setter [`#131`](https://github.com/neutrinojs/webpack-chain/pull/131)
- Allow use of before() and after() with oneOf rules [`#133`](https://github.com/neutrinojs/webpack-chain/pull/133)
- Travis: Test against Node 11 [`#118`](https://github.com/neutrinojs/webpack-chain/pull/118)
- docs: Fix typo in devServer options [`#117`](https://github.com/neutrinojs/webpack-chain/pull/117)
- Allow use of before() and after() with oneOf rules (#133) [`#119`](https://github.com/neutrinojs/webpack-chain/issues/119)
- Update dependency ava to v1 [`ce9e884`](https://github.com/neutrinojs/webpack-chain/commit/ce9e884f988a01ac6297d167dfd013cab8d8c24a)
- Lock file maintenance [`d124b5d`](https://github.com/neutrinojs/webpack-chain/commit/d124b5df6648257092becae81ceece9bef5485b8)

#### [v5.0.1](https://github.com/neutrinojs/webpack-chain/compare/v5.0.0...v5.0.1)

> 22 October 2018

- Fix toString() output for alternative types of plugin [`#116`](https://github.com/neutrinojs/webpack-chain/pull/116)
- Fix toString() output for alternative types of plugin (#116) [`#115`](https://github.com/neutrinojs/webpack-chain/issues/115)

### [v5.0.0](https://github.com/neutrinojs/webpack-chain/compare/v4.12.1...v5.0.0)

> 8 October 2018

- README: Add NPM/Travis badges [`#112`](https://github.com/neutrinojs/webpack-chain/pull/112)
- Provide the same API for config.optimization.minimizer as config.plugins [`#84`](https://github.com/neutrinojs/webpack-chain/pull/84)
- README: Add NPM/Travis badges (#112) [`#110`](https://github.com/neutrinojs/webpack-chain/issues/110)
- Provide the same API for config.optimization.minimizer as config.plugins (#84) [`#95`](https://github.com/neutrinojs/webpack-chain/issues/95)

#### [v4.12.1](https://github.com/neutrinojs/webpack-chain/compare/v4.12.0...v4.12.1)

> 3 October 2018

- Switch from changelog to auto-changelog [`#109`](https://github.com/neutrinojs/webpack-chain/pull/109)
- Allow passing entry as a string to config.merge() [`#107`](https://github.com/neutrinojs/webpack-chain/pull/107)
- Lock file maintenance [`#101`](https://github.com/neutrinojs/webpack-chain/pull/101)
- Update dependency eslint-plugin-prettier to v3 [`e42d8bd`](https://github.com/neutrinojs/webpack-chain/commit/e42d8bd2f6f70841c4d1ab7a2926c26d3ec828ed)

#### [v4.12.0](https://github.com/neutrinojs/webpack-chain/compare/v4.11.0...v4.12.0)

> 3 October 2018

- merge resolve plugins just like config [`c47ee2d`](https://github.com/neutrinojs/webpack-chain/commit/c47ee2d52c1be4fcdbe10adef865e45d6fd729ef)
- linted [`dff82f8`](https://github.com/neutrinojs/webpack-chain/commit/dff82f8494394dcf8c97bff324f877513f44fa56)
- Revert changes to gitignore [`c1250a0`](https://github.com/neutrinojs/webpack-chain/commit/c1250a0fe7ffafa82f529ac8e7262e2c3cdd4729)

#### [v4.11.0](https://github.com/neutrinojs/webpack-chain/compare/v4.10.0...v4.11.0)

> 13 September 2018

- Support specifying plugins by path [`#102`](https://github.com/neutrinojs/webpack-chain/pull/102)
- Lock file maintenance [`#100`](https://github.com/neutrinojs/webpack-chain/pull/100)
- Lock file maintenance [`#96`](https://github.com/neutrinojs/webpack-chain/pull/96)

#### [v4.10.0](https://github.com/neutrinojs/webpack-chain/compare/v4.9.0...v4.10.0)

> 6 September 2018

- Use the Resolve API to define ResolveLoader according to webpack [`#99`](https://github.com/neutrinojs/webpack-chain/pull/99)
- Migrate to new org [`#92`](https://github.com/neutrinojs/webpack-chain/pull/92)
- test: 'clean' in 'ChainedMap' [`#93`](https://github.com/neutrinojs/webpack-chain/pull/93)
- Lock file maintenance [`3a4b3e1`](https://github.com/neutrinojs/webpack-chain/commit/3a4b3e10032856ab7f01afa67a23dd9e4e68161a)
- Lock file maintenance [`815bfd1`](https://github.com/neutrinojs/webpack-chain/commit/815bfd173a2dc6f802b66a48cdb2c4d2ff47df9f)

#### [v4.9.0](https://github.com/neutrinojs/webpack-chain/compare/v4.8.0...v4.9.0)

> 15 August 2018

- Update to ESLint 5 [`#89`](https://github.com/neutrinojs/webpack-chain/pull/89)
- Lock file maintenance [`#85`](https://github.com/neutrinojs/webpack-chain/pull/85)
- Implement ChainedMap.getOrCompute [`#63`](https://github.com/neutrinojs/webpack-chain/pull/63)
- Support Object literal plugin usage [`#86`](https://github.com/neutrinojs/webpack-chain/pull/86)
- Lock file maintenance [`#61`](https://github.com/neutrinojs/webpack-chain/pull/61)
- Lock file maintenance [`#60`](https://github.com/neutrinojs/webpack-chain/pull/60)
- Update to ESLint 5 (#89) [`#69`](https://github.com/neutrinojs/webpack-chain/issues/69) [`#77`](https://github.com/neutrinojs/webpack-chain/issues/77) [`#87`](https://github.com/neutrinojs/webpack-chain/issues/87) [`#88`](https://github.com/neutrinojs/webpack-chain/issues/88)
- Update dependency eslint-config-airbnb-base to v13 [`7370962`](https://github.com/neutrinojs/webpack-chain/commit/73709628a6ff6661e478c652d0ff03b99b6c2abb)
- Fix linting :/ [`30cc11d`](https://github.com/neutrinojs/webpack-chain/commit/30cc11d0d35a5676069a623b180c8e7b00e099e4)
- Fix README bug, test in Node.js v6 [`4a37c74`](https://github.com/neutrinojs/webpack-chain/commit/4a37c74e1f790e118034154da9c32d0e36164f74)
- Run yarn lint --fix [`9384537`](https://github.com/neutrinojs/webpack-chain/commit/9384537269d60bb80b3330cf44ddbdd9d528c454)

#### [v4.8.0](https://github.com/neutrinojs/webpack-chain/compare/v4.7.0...v4.8.0)

> 16 May 2018

- Expose toString as a static method on Config [`#57`](https://github.com/neutrinojs/webpack-chain/pull/57)
- Add test for Config.toString, add README note [`0107aef`](https://github.com/neutrinojs/webpack-chain/commit/0107aef203202aef069190723b04ec4f6ac80b9f)

#### [v4.7.0](https://github.com/neutrinojs/webpack-chain/compare/v4.6.0...v4.7.0)

> 15 May 2018

- Lint with eslint, prettier, airbnb [`#52`](https://github.com/neutrinojs/webpack-chain/pull/52)
- Support Config.toString() with name hints [`#53`](https://github.com/neutrinojs/webpack-chain/pull/53)
- Configure Renovate [`#54`](https://github.com/neutrinojs/webpack-chain/pull/54)
- Lock file maintenance [`50d4db8`](https://github.com/neutrinojs/webpack-chain/commit/50d4db81ab5fe62a55435720f5c78ddc40309a88)

#### [v4.6.0](https://github.com/neutrinojs/webpack-chain/compare/v4.5.0...v4.6.0)

> 16 April 2018

- Support Webpack 4.x [`#51`](https://github.com/neutrinojs/webpack-chain/pull/51)
- Update devDependencies [`#50`](https://github.com/neutrinojs/webpack-chain/pull/50)

#### [v4.5.0](https://github.com/neutrinojs/webpack-chain/compare/v4.4.2...v4.5.0)

> 22 November 2017

- Introduce method for performing a batch of operations against a context [`#43`](https://github.com/neutrinojs/webpack-chain/pull/43)

#### [v4.4.2](https://github.com/neutrinojs/webpack-chain/compare/v4.4.1...v4.4.2)

> 10 October 2017

- Update changelog [`1bb3da1`](https://github.com/neutrinojs/webpack-chain/commit/1bb3da1fec04a158f68762e57aff33a0172a298f)
- Hotfix - guard against non-defined entries when ordering chainedmap [`76be81f`](https://github.com/neutrinojs/webpack-chain/commit/76be81f4509b9652bef25cc55747df87850b858e)
- Updating changelog [`a71fc4b`](https://github.com/neutrinojs/webpack-chain/commit/a71fc4b70ccce358aacc29ed7dc5d8cdacdd4cc1)

#### [v4.4.1](https://github.com/neutrinojs/webpack-chain/compare/v4.4.0...v4.4.1)

> 6 October 2017

- Updating changelog [`97a2fab`](https://github.com/neutrinojs/webpack-chain/commit/97a2fabf6e51f1b03cacb0991ba02e236be983fa)
- Missing schema before/after [`8d8f26d`](https://github.com/neutrinojs/webpack-chain/commit/8d8f26dd0e6db375dbabb8e8dfe784e6c50408d5)

#### [v4.4.0](https://github.com/neutrinojs/webpack-chain/compare/v4.3.0...v4.4.0)

> 6 October 2017

- Feature: allow specifying to use before or after other use [`#42`](https://github.com/neutrinojs/webpack-chain/pull/42)
- Docs: Upstream fixes made to Neutrino's webpack-chain docs [`#41`](https://github.com/neutrinojs/webpack-chain/pull/41)
- Improve documentation for plugin configuration [`#40`](https://github.com/neutrinojs/webpack-chain/pull/40)
- Allow omitting keys from source merge object [`fb6ea2f`](https://github.com/neutrinojs/webpack-chain/commit/fb6ea2fad931c13e7516a3e9354215a78cb5c4ff)
- Feature: allow specifying .before or .after to order plugins and uses [`b0040bf`](https://github.com/neutrinojs/webpack-chain/commit/b0040bff73b3b9e55d53192ac4a447a2ac8c02d1)
- Rename when arguments to be clearer [`d15e895`](https://github.com/neutrinojs/webpack-chain/commit/d15e895669ce0a44c704755af39290700e73e85f)
- Bumping deps [`c15be4a`](https://github.com/neutrinojs/webpack-chain/commit/c15be4ab99d232126bbf18666f4f20f80df21f90)
- Update changelog [`5aec63a`](https://github.com/neutrinojs/webpack-chain/commit/5aec63a424c71a9a603540b33e576999c839f074)

#### [v4.3.0](https://github.com/neutrinojs/webpack-chain/compare/v4.2.0...v4.3.0)

> 13 September 2017

- Update API for base config, dev server, and output [`#38`](https://github.com/neutrinojs/webpack-chain/pull/38)
- Update changelog [`6260f49`](https://github.com/neutrinojs/webpack-chain/commit/6260f49edbcab301988b7b2c6c8a77e07707c010)

#### [v4.2.0](https://github.com/neutrinojs/webpack-chain/compare/v4.1.0...v4.2.0)

> 13 September 2017

- Add new shorthands from resolve and output [`#37`](https://github.com/neutrinojs/webpack-chain/pull/37)
- changelog [`0374e51`](https://github.com/neutrinojs/webpack-chain/commit/0374e518a4b465c73b5097eff6e4c77768319e4f)
- Updating README with shorthands [`ae5e75a`](https://github.com/neutrinojs/webpack-chain/commit/ae5e75ae619d0399bcbf8e588a48759b0e590b6e)

#### [v4.1.0](https://github.com/neutrinojs/webpack-chain/compare/v4.0.0...v4.1.0)

> 12 September 2017

- Updating rule definition shortcuts, adding oneOf [`#36`](https://github.com/neutrinojs/webpack-chain/pull/36)

### [v4.0.0](https://github.com/neutrinojs/webpack-chain/compare/v3.3.0...v4.0.0)

> 3 October 2018

- Switch noParse to getter/setter to allow webpack v3 function argument [`#32`](https://github.com/neutrinojs/webpack-chain/pull/32)
- Serialize performance into config output [`#31`](https://github.com/neutrinojs/webpack-chain/pull/31)
- Release v4.0.0 [`e84b002`](https://github.com/neutrinojs/webpack-chain/commit/e84b00207f6d4f4dc37c43ffffc65d8a34f63a75)

#### [v3.3.0](https://github.com/neutrinojs/webpack-chain/compare/v3.2.0...v3.3.0)

> 3 October 2018

- Adding noParse on module [`#27`](https://github.com/neutrinojs/webpack-chain/pull/27)
- Releasing v3.3.0 [`4a59bef`](https://github.com/neutrinojs/webpack-chain/commit/4a59bef687f273503945e638fefe1f6ab29857d1)

#### [v3.2.0](https://github.com/neutrinojs/webpack-chain/compare/v3.1.0...v3.2.0)

> 3 October 2018

- Adding updated shorthand methods for devServer [`#23`](https://github.com/neutrinojs/webpack-chain/pull/23)

#### [v3.1.0](https://github.com/neutrinojs/webpack-chain/compare/v3.0.0...v3.1.0)

> 3 October 2018

- Allow conditional configuration via when [`#22`](https://github.com/neutrinojs/webpack-chain/pull/22)
- Update README with links to previous docs versions [`0dc3984`](https://github.com/neutrinojs/webpack-chain/commit/0dc39841b76c5e4d9493fa86d7f65e66145a6964)
- Update README with links to previous docs versions [`bcc2362`](https://github.com/neutrinojs/webpack-chain/commit/bcc2362d396cee736feaa5e4537150b4a1fa2d4a)

### [v3.0.0](https://github.com/neutrinojs/webpack-chain/compare/v2.0.1...v3.0.0)

> 3 October 2018

- Make rule.include, rule.exclude, loaders and plugins more extensible [`#16`](https://github.com/neutrinojs/webpack-chain/pull/16)

#### [v2.0.1](https://github.com/neutrinojs/webpack-chain/compare/v2.0.0...v2.0.1)

> 3 October 2018

- undefined plugin [`#17`](https://github.com/neutrinojs/webpack-chain/pull/17)

### [v2.0.0](https://github.com/neutrinojs/webpack-chain/compare/v1.4.3...v2.0.0)

> 3 October 2018

- Adding testing, which informed v2 API, updated docs to reflect [`#14`](https://github.com/neutrinojs/webpack-chain/pull/14)
- Make Plugin API consistent with Loader API [`#13`](https://github.com/neutrinojs/webpack-chain/pull/13)
- MPL license, moving to mozilla-neutrino [`f122edd`](https://github.com/neutrinojs/webpack-chain/commit/f122eddccb9f7af9742f5c447c651172700b4c50)

#### [v1.4.3](https://github.com/neutrinojs/webpack-chain/compare/v1.4.2...v1.4.3)

> 6 March 2017

- Adding ChainedMap and ChainedSet documentation [`b071f82`](https://github.com/neutrinojs/webpack-chain/commit/b071f82042c7806f6d2df412a0154c1b985c4763)
- Removing empty entities from cluttering configuration object [`b428e55`](https://github.com/neutrinojs/webpack-chain/commit/b428e55a671a033c133ba2e225796845307dee12)
- Docs: getConfig -&gt; toConfig [`2468eaa`](https://github.com/neutrinojs/webpack-chain/commit/2468eaac7e4c2f74cae244a4af6d2a517483db7b)

#### [v1.4.2](https://github.com/neutrinojs/webpack-chain/compare/v1.4.1...v1.4.2)

> 3 October 2018

- Fix bug where `exclude` doesn't return `this` [`#7`](https://github.com/neutrinojs/webpack-chain/pull/7)
- Bumping to v1.4.2 [`38d1412`](https://github.com/neutrinojs/webpack-chain/commit/38d1412037780b815c537de2abd5f02443b80502)

#### [v1.4.1](https://github.com/neutrinojs/webpack-chain/compare/v1.4.0...v1.4.1)

> 3 October 2018

- Allowing config merge to append to existing rule loaders [`#3`](https://github.com/neutrinojs/webpack-chain/pull/3)
- docs(readme): fix typo in devtool option [`#1`](https://github.com/neutrinojs/webpack-chain/pull/1)

#### [v1.4.0](https://github.com/neutrinojs/webpack-chain/compare/v1.3.0...v1.4.0)

> 3 October 2018

- Adds rule test merge via string to regex, fixes externals not chainable [`a15b49e`](https://github.com/neutrinojs/webpack-chain/commit/a15b49ec28903708c04665d0c6cac3a956558a99)

#### [v1.3.0](https://github.com/neutrinojs/webpack-chain/compare/v1.2.0...v1.3.0)

> 3 October 2018

- Adding functionality for merging and object into a Config instance [`5f0b0c6`](https://github.com/neutrinojs/webpack-chain/commit/5f0b0c670e6ad946e0232a208abb667f749aeba4)

#### [v1.2.0](https://github.com/neutrinojs/webpack-chain/compare/v1.1.0...v1.2.0)

> 3 October 2018

- Adds hot flag for Config.DevServer [`c64a155`](https://github.com/neutrinojs/webpack-chain/commit/c64a1558188ab2a4982a7b3f2aba95ced50c9756)

#### [v1.1.0](https://github.com/neutrinojs/webpack-chain/compare/v1.0.3...v1.1.0)

> 3 October 2018

- Adding ChainedSet#prepend functionality [`cc86e7b`](https://github.com/neutrinojs/webpack-chain/commit/cc86e7bdbdcaca7610255039dcb31adfacb4952b)

#### [v1.0.3](https://github.com/neutrinojs/webpack-chain/compare/v1.0.2...v1.0.3)

> 3 October 2018

- Fixes exception with empty rule entries with loader only [`7964b34`](https://github.com/neutrinojs/webpack-chain/commit/7964b347ed613c7dc18e54f912467abad2956b8b)

#### [v1.0.2](https://github.com/neutrinojs/webpack-chain/compare/v1.0.1...v1.0.2)

> 3 October 2018

- Fixes plugin methods not chaining [`7cc56ed`](https://github.com/neutrinojs/webpack-chain/commit/7cc56ed5e331c7cc0ccd58f25feb3b1b0398829a)

#### [v1.0.1](https://github.com/neutrinojs/webpack-chain/compare/v1.0.0...v1.0.1)

> 3 October 2018

- Shared configuration documentation [`5c6a65b`](https://github.com/neutrinojs/webpack-chain/commit/5c6a65b344113ff3522d5f7d66dfacbc0ad7fa69)
- Avoid exceptions in empty config [`ab46ee0`](https://github.com/neutrinojs/webpack-chain/commit/ab46ee0234a04eb2b89190df32ea4287c499dd39)

#### v1.0.0

> 3 October 2018

- initial commit [`9e2a87c`](https://github.com/neutrinojs/webpack-chain/commit/9e2a87c5f6a1f1aac3eedbd4102e40dc47a8f7f4)
