<!-- 积分加速页面开始 -->
<template>
  <view class="points-acceleration-container">
    <!-- 页面标题区域 -->
    <view class="page-header">
      <view class="title-section">
        <text class="page-title">积分加速</text>
        <text class="page-subtitle">管理会员购物积分加速规则</text>
      </view>
      
      <!-- 添加规则按钮 -->
      <view class="add-rule-btn" @click="showAddRuleModal">
        <text class="btn-text">添加规则</text>
        <text class="btn-icon">+</text>
      </view>
    </view>
    
    <!-- 积分加速说明 -->
    <view class="info-card">
      <view class="info-header">
        <text class="info-title">什么是积分加速？</text>
      </view>
      <view class="info-body">
        <text class="info-text">积分加速是指会员在购物时可以获得比普通用户更多的积分奖励。您可以为不同等级的会员设置不同的积分加速倍率，提高会员的购物积极性和忠诚度。</text>
      </view>
    </view>
    
    <!-- 积分加速规则列表 -->
    <view class="rules-list">
      <view v-if="rules.length === 0" class="empty-tip">
        <image class="empty-icon" src="/static/images/empty-data.svg"></image>
        <text class="empty-text">暂无积分加速规则，点击"添加规则"创建</text>
      </view>
      
      <view v-else class="rule-cards">
        <view v-for="(rule, index) in rules" :key="index" class="rule-card">
          <view class="rule-card-header" :style="{ backgroundColor: rule.color }">
            <view class="rule-name">{{ rule.name }}</view>
            <view class="rule-actions">
              <text class="action-btn edit" @click="editRule(rule)">编辑</text>
              <text class="action-btn delete" @click="confirmDeleteRule(rule)">删除</text>
            </view>
          </view>
          <view class="rule-card-body">
            <view class="rule-info-item">
              <text class="info-label">规则图标：</text>
              <image class="rule-icon" :src="rule.icon" mode="aspectFit"></image>
            </view>
            <view class="rule-info-item">
              <text class="info-label">适用等级：</text>
              <view class="level-tags">
                <text 
                  v-for="(level, idx) in rule.applicableLevels" 
                  :key="idx" 
                  class="level-tag"
                >{{ level }}</text>
              </view>
            </view>
            <view class="rule-info-item">
              <text class="info-label">加速倍率：</text>
              <text class="info-value">{{ rule.multiplier }}倍</text>
            </view>
            <view class="rule-info-item">
              <text class="info-label">适用范围：</text>
              <text class="info-value">{{ rule.scope }}</text>
            </view>
            <view class="rule-info-item">
              <text class="info-label">规则说明：</text>
              <text class="info-value">{{ rule.description || '暂无规则说明' }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 添加/编辑规则弹窗 -->
    <uni-popup ref="ruleFormPopup" type="center">
      <view class="rule-form-popup">
        <view class="popup-header">
          <text class="popup-title">{{ isEditing ? '编辑规则' : '添加规则' }}</text>
          <text class="popup-close" @click="closeRuleModal">×</text>
        </view>
        <view class="popup-body">
          <view class="form-item">
            <text class="form-label">规则名称</text>
            <input class="form-input" v-model="ruleForm.name" placeholder="请输入规则名称" />
          </view>
          <view class="form-item">
            <text class="form-label">规则颜色</text>
            <view class="color-picker">
              <view 
                v-for="(color, idx) in colorOptions" 
                :key="idx" 
                class="color-option"
                :class="{ active: ruleForm.color === color }"
                :style="{ backgroundColor: color }"
                @click="ruleForm.color = color"
              ></view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">规则图标</text>
            <view class="icon-upload">
              <image v-if="ruleForm.icon" class="preview-icon" :src="ruleForm.icon" mode="aspectFit"></image>
              <view v-else class="upload-btn" @click="chooseIcon">
                <text class="upload-icon">+</text>
                <text class="upload-text">上传图标</text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">加速倍率</text>
            <view class="form-input-group">
              <input class="form-input" type="digit" v-model="ruleForm.multiplier" placeholder="请输入加速倍率" />
              <text class="input-suffix">倍</text>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">适用范围</text>
            <picker class="form-picker" :range="ruleScopes" @change="onScopeChange">
              <view class="picker-value">{{ ruleForm.scope || '请选择适用范围' }}</view>
            </picker>
          </view>
          <view class="form-item">
            <text class="form-label">适用等级</text>
            <view class="level-checkboxes">
              <view 
                v-for="(level, idx) in memberLevels" 
                :key="idx" 
                class="level-checkbox"
                :class="{ active: isLevelSelected(level.name) }"
                @click="toggleLevelSelection(level.name)"
              >
                <text class="checkbox-icon">{{ isLevelSelected(level.name) ? '✓' : '' }}</text>
                <text class="checkbox-label">{{ level.name }}</text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">规则说明</text>
            <textarea class="form-textarea" v-model="ruleForm.description" placeholder="请输入规则说明"></textarea>
          </view>
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @click="closeRuleModal">取消</button>
          <button class="confirm-btn" @click="saveRuleForm">确认</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 删除确认弹窗 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog
        type="warning"
        title="删除确认"
        content="确定要删除该积分加速规则吗？删除后将无法恢复。"
        :before-close="true"
        @confirm="deleteRule"
        @close="closeDeleteConfirm"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      rules: [], // 积分加速规则列表
      ruleForm: {
        id: '',
        name: '',
        color: '#1E90FF', // 默认蓝色
        icon: '',
        multiplier: '',
        scope: '',
        applicableLevels: [],
        description: ''
      },
      isEditing: false, // 是否为编辑模式
      currentRuleId: null, // 当前编辑的规则ID
      colorOptions: [
        '#FF6B22', // 橙色
        '#8A2BE2', // 紫色
        '#1E90FF', // 道奇蓝
        '#32CD32', // 酸橙绿
        '#FFD700', // 金色
        '#FF69B4', // 热粉红
        '#20B2AA', // 浅海绿
        '#FF8C00'  // 深橙色
      ],
      ruleScopes: [
        '全部商品',
        '指定分类商品',
        '指定商品',
        '会员专享商品'
      ],
      memberLevels: [] // 会员等级列表
    };
  },
  onLoad() {
    this.fetchRules();
    this.fetchMemberLevels();
  },
  methods: {
    // 获取积分加速规则列表
    fetchRules() {
      // 模拟数据，实际项目中应从API获取
      this.rules = [
        {
          id: '1',
          name: '银卡会员积分加速',
          color: '#C0C0C0',
          icon: '/static/images/points-silver.svg',
          multiplier: '1.2',
          scope: '全部商品',
          applicableLevels: ['银卡会员'],
          description: '银卡会员购物可获得1.2倍积分奖励'
        },
        {
          id: '2',
          name: '金卡会员积分加速',
          color: '#FFD700',
          icon: '/static/images/points-gold.svg',
          multiplier: '1.5',
          scope: '全部商品',
          applicableLevels: ['金卡会员'],
          description: '金卡会员购物可获得1.5倍积分奖励'
        },
        {
          id: '3',
          name: '钻石会员积分加速',
          color: '#B9F2FF',
          icon: '/static/images/points-diamond.svg',
          multiplier: '2',
          scope: '全部商品',
          applicableLevels: ['钻石会员'],
          description: '钻石会员购物可获得2倍积分奖励'
        }
      ];
    },
    
    // 获取会员等级列表
    fetchMemberLevels() {
      // 模拟数据，实际项目中应从API获取
      this.memberLevels = [
        {
          id: '1',
          name: '普通会员'
        },
        {
          id: '2',
          name: '银卡会员'
        },
        {
          id: '3',
          name: '金卡会员'
        },
        {
          id: '4',
          name: '钻石会员'
        }
      ];
    },
    
    // 显示添加规则弹窗
    showAddRuleModal() {
      this.isEditing = false;
      this.ruleForm = {
        id: '',
        name: '',
        color: '#1E90FF',
        icon: '',
        multiplier: '',
        scope: '',
        applicableLevels: [],
        description: ''
      };
      this.$refs.ruleFormPopup.open();
    },
    
    // 编辑规则
    editRule(rule) {
      this.isEditing = true;
      this.currentRuleId = rule.id;
      this.ruleForm = JSON.parse(JSON.stringify(rule)); // 深拷贝
      this.$refs.ruleFormPopup.open();
    },
    
    // 关闭规则表单弹窗
    closeRuleModal() {
      this.$refs.ruleFormPopup.close();
    },
    
    // 保存规则表单
    saveRuleForm() {
      // 表单验证
      if (!this.ruleForm.name) {
        uni.showToast({
          title: '请输入规则名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.ruleForm.multiplier) {
        uni.showToast({
          title: '请输入加速倍率',
          icon: 'none'
        });
        return;
      }
      
      if (!this.ruleForm.scope) {
        uni.showToast({
          title: '请选择适用范围',
          icon: 'none'
        });
        return;
      }
      
      if (this.ruleForm.applicableLevels.length === 0) {
        uni.showToast({
          title: '请选择适用等级',
          icon: 'none'
        });
        return;
      }
      
      // 保存数据
      if (this.isEditing) {
        // 编辑现有规则
        const index = this.rules.findIndex(item => item.id === this.currentRuleId);
        if (index !== -1) {
          this.rules.splice(index, 1, JSON.parse(JSON.stringify(this.ruleForm)));
        }
      } else {
        // 添加新规则
        this.ruleForm.id = Date.now().toString(); // 生成临时ID
        this.rules.push(JSON.parse(JSON.stringify(this.ruleForm)));
      }
      
      // 关闭弹窗
      this.closeRuleModal();
      
      // 提示保存成功
      uni.showToast({
        title: this.isEditing ? '规则修改成功' : '规则添加成功'
      });
    },
    
    // 确认删除规则
    confirmDeleteRule(rule) {
      this.currentRuleId = rule.id;
      this.$refs.deleteConfirmPopup.open();
    },
    
    // 删除规则
    deleteRule() {
      const index = this.rules.findIndex(item => item.id === this.currentRuleId);
      if (index !== -1) {
        this.rules.splice(index, 1);
      }
      
      this.$refs.deleteConfirmPopup.close();
      
      uni.showToast({
        title: '规则删除成功'
      });
    },
    
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    
    // 选择图标
    chooseIcon() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.ruleForm.icon = res.tempFilePaths[0];
        }
      });
    },
    
    // 选择范围变更
    onScopeChange(e) {
      const index = e.detail.value;
      this.ruleForm.scope = this.ruleScopes[index];
    },
    
    // 判断等级是否被选中
    isLevelSelected(levelName) {
      return this.ruleForm.applicableLevels.includes(levelName);
    },
    
    // 切换等级选择
    toggleLevelSelection(levelName) {
      const index = this.ruleForm.applicableLevels.indexOf(levelName);
      if (index === -1) {
        this.ruleForm.applicableLevels.push(levelName);
      } else {
        this.ruleForm.applicableLevels.splice(index, 1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.points-acceleration-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-section {
  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .page-subtitle {
    font-size: 24rpx;
    color: #666;
    margin-top: 6rpx;
  }
}

.add-rule-btn {
  display: flex;
  align-items: center;
  background-color: #4A00E0;
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  
  .btn-text {
    font-size: 28rpx;
  }
  
  .btn-icon {
    font-size: 32rpx;
    margin-left: 8rpx;
  }
}

.info-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .info-header {
    padding: 20rpx 24rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .info-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .info-body {
    padding: 20rpx 24rpx;
    
    .info-text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
    }
  }
}

.rules-list {
  .empty-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    background-color: #fff;
    border-radius: 12rpx;
    
    .empty-icon {
      width: 160rpx;
      height: 160rpx;
      margin-bottom: 20rpx;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.rule-cards {
  .rule-card {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .rule-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 24rpx;
      color: #fff;
      
      .rule-name {
        font-size: 32rpx;
        font-weight: bold;
      }
      
      .rule-actions {
        display: flex;
        
        .action-btn {
          font-size: 24rpx;
          padding: 6rpx 16rpx;
          border-radius: 30rpx;
          margin-left: 16rpx;
          
          &.edit {
            background-color: rgba(255, 255, 255, 0.3);
          }
          
          &.delete {
            background-color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
    
    .rule-card-body {
      padding: 24rpx;
      
      .rule-info-item {
        display: flex;
        margin-bottom: 16rpx;
        
        .info-label {
          width: 160rpx;
          font-size: 28rpx;
          color: #666;
        }
        
        .info-value {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
        
        .rule-icon {
          width: 60rpx;
          height: 60rpx;
        }
        
        .level-tags {
          display: flex;
          flex-wrap: wrap;
          
          .level-tag {
            font-size: 24rpx;
            color: #4A00E0;
            background-color: rgba(74, 0, 224, 0.1);
            padding: 6rpx 16rpx;
            border-radius: 6rpx;
            margin-right: 12rpx;
            margin-bottom: 12rpx;
          }
        }
      }
    }
  }
}

.rule-form-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    border-bottom: 1rpx solid #eee;
    
    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .popup-close {
      font-size: 40rpx;
      color: #999;
    }
  }
  
  .popup-body {
    padding: 24rpx;
    max-height: 60vh;
    overflow-y: auto;
    
    .form-item {
      margin-bottom: 24rpx;
      
      .form-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 12rpx;
      }
      
      .form-input {
        width: 100%;
        height: 80rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
      
      .form-input-group {
        display: flex;
        align-items: center;
        
        .form-input {
          flex: 1;
        }
        
        .input-suffix {
          margin-left: 10rpx;
          font-size: 28rpx;
          color: #333;
        }
      }
      
      .form-textarea {
        width: 100%;
        height: 160rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        padding: 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
      
      .form-picker {
        width: 100%;
        height: 80rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        
        .picker-value {
          color: #333;
        }
      }
      
      .color-picker {
        display: flex;
        flex-wrap: wrap;
        
        .color-option {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          position: relative;
          
          &.active::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 32rpx;
          }
        }
      }
      
      .icon-upload {
        .preview-icon {
          width: 100rpx;
          height: 100rpx;
          border-radius: 8rpx;
        }
        
        .upload-btn {
          width: 100rpx;
          height: 100rpx;
          border: 1rpx dashed #ddd;
          border-radius: 8rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          
          .upload-icon {
            font-size: 40rpx;
            color: #999;
            margin-bottom: 4rpx;
          }
          
          .upload-text {
            font-size: 20rpx;
            color: #999;
          }
        }
      }
      
      .level-checkboxes {
        display: flex;
        flex-wrap: wrap;
        
        .level-checkbox {
          display: flex;
          align-items: center;
          margin-right: 30rpx;
          margin-bottom: 20rpx;
          
          .checkbox-icon {
            width: 40rpx;
            height: 40rpx;
            border: 1rpx solid #ddd;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10rpx;
            color: #fff;
            font-size: 24rpx;
          }
          
          &.active .checkbox-icon {
            background-color: #4A00E0;
            border-color: #4A00E0;
          }
          
          .checkbox-label {
            font-size: 28rpx;
            color: #333;
          }
        }
      }
    }
  }
  
  .popup-footer {
    display: flex;
    border-top: 1rpx solid #eee;
    
    button {
      flex: 1;
      height: 90rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      border-radius: 0;
      
      &.cancel-btn {
        background-color: #f5f5f5;
        color: #666;
      }
      
      &.confirm-btn {
        background-color: #4A00E0;
        color: #fff;
      }
    }
  }
}
</style>
<!-- 积分加速页面结束 --> 