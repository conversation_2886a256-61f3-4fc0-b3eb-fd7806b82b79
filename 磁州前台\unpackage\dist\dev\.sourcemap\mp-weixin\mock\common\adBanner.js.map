{"version": 3, "file": "adBanner.js", "sources": ["mock/common/adBanner.js"], "sourcesContent": ["// 广告横幅模拟数据\r\nexport const adBanner = {\r\n  image: '/static/images/ad-banner.jpg',\r\n  url: '/pages/activity/detail?id=3'\r\n};\r\n\r\n// 模拟获取广告横幅的API函数\r\nexport const fetchAdBanner = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(adBanner);\r\n    }, 200);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,WAAW;AAAA,EACtB,OAAO;AAAA,EACP,KAAK;AACP;AAGY,MAAC,gBAAgB,MAAM;AACjC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,QAAQ;AAAA,IACjB,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;"}