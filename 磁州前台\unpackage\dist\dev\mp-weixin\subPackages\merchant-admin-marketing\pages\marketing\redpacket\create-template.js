"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 我的模板
      myTemplates: [
        {
          id: 1,
          name: "会员专享红包",
          description: "针对会员用户的专属优惠",
          icon: "/static/images/redpacket/template-icon-1.png",
          color: "#FF5858",
          type: "normal",
          typeText: "普通红包"
        },
        {
          id: 2,
          name: "节日特惠红包",
          description: "节假日促销活动专用",
          icon: "/static/images/redpacket/template-icon-2.png",
          color: "#4ECDC4",
          type: "fission",
          typeText: "裂变红包"
        }
      ],
      // 官方模板
      officialTemplates: [
        {
          id: 101,
          name: "新用户欢迎",
          description: "吸引新用户注册和首次消费",
          icon: "/static/images/redpacket/official-icon-1.png",
          color: "#FF5858"
        },
        {
          id: 102,
          name: "老用户回馈",
          description: "提高老用户复购率和忠诚度",
          icon: "/static/images/redpacket/official-icon-2.png",
          color: "#4ECDC4"
        },
        {
          id: 103,
          name: "生日特权",
          description: "会员生日专属优惠和祝福",
          icon: "/static/images/redpacket/official-icon-3.png",
          color: "#FFD166"
        },
        {
          id: 104,
          name: "满额立减",
          description: "刺激用户提高客单价",
          icon: "/static/images/redpacket/official-icon-4.png",
          color: "#6A0572"
        }
      ],
      // 场景模板
      scenarioTemplates: [
        {
          id: 201,
          name: "春节红包",
          description: "新春佳节送福利",
          icon: "/static/images/redpacket/scenario-icon-1.png",
          color: "#FF5858",
          tag: "hot",
          tagText: "热门"
        },
        {
          id: 202,
          name: "店庆活动",
          description: "周年庆典专属优惠",
          icon: "/static/images/redpacket/scenario-icon-2.png",
          color: "#4ECDC4",
          tag: "new",
          tagText: "新品"
        },
        {
          id: 203,
          name: "会员日特惠",
          description: "每月会员专属福利",
          icon: "/static/images/redpacket/scenario-icon-3.png",
          color: "#FFD166",
          tag: "",
          tagText: ""
        },
        {
          id: 204,
          name: "限时秒杀",
          description: "限时限量抢购活动",
          icon: "/static/images/redpacket/scenario-icon-4.png",
          color: "#6A0572",
          tag: "",
          tagText: ""
        }
      ],
      // 使用指南
      guideSteps: [
        {
          title: "选择模板",
          description: "从我的模板、官方模板或场景模板中选择适合的模板"
        },
        {
          title: "自定义设置",
          description: "根据实际需求修改红包金额、使用规则等参数"
        },
        {
          title: "保存并发布",
          description: "确认设置无误后保存并发布红包活动"
        },
        {
          title: "分享推广",
          description: "通过多种渠道分享红包活动，提高曝光度"
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "红包模板帮助",
        content: "红包模板可以帮助您快速创建红包活动，节省设置时间。您可以使用官方模板，也可以创建和保存自己的模板。",
        showCancel: false
      });
    },
    createTemplate() {
      common_vendor.index.showToast({
        title: "创建模板功能开发中",
        icon: "none"
      });
    },
    viewTemplateDetail(item) {
      common_vendor.index.showToast({
        title: "查看详情功能开发中",
        icon: "none"
      });
    },
    useTemplate(item) {
      common_vendor.index.showModal({
        title: "使用模板",
        content: `确定要使用"${item.name}"模板创建红包活动吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateTo({
              url: "/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create"
            });
          }
        }
      });
    },
    editTemplate(item) {
      common_vendor.index.showToast({
        title: "编辑模板功能开发中",
        icon: "none"
      });
    },
    deleteTemplate(item) {
      common_vendor.index.showModal({
        title: "删除确认",
        content: `确定要删除"${item.name}"模板吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.myTemplates.findIndex((t) => t.id === item.id);
            if (index !== -1) {
              this.myTemplates.splice(index, 1);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.o((...args) => $options.createTemplate && $options.createTemplate(...args)),
    d: common_vendor.f($data.myTemplates, (item, index, i0) => {
      return {
        a: item.icon,
        b: common_vendor.t(item.name),
        c: item.color,
        d: common_vendor.t(item.description),
        e: common_vendor.t(item.typeText),
        f: common_vendor.o(($event) => $options.useTemplate(item), index),
        g: common_vendor.o(($event) => $options.editTemplate(item), index),
        h: common_vendor.o(($event) => $options.deleteTemplate(item), index),
        i: index,
        j: common_vendor.o(($event) => $options.viewTemplateDetail(item), index)
      };
    }),
    e: $data.myTemplates.length === 0
  }, $data.myTemplates.length === 0 ? {
    f: common_vendor.o((...args) => $options.createTemplate && $options.createTemplate(...args))
  } : {}, {
    g: common_vendor.f($data.officialTemplates, (item, index, i0) => {
      return {
        a: item.icon,
        b: common_vendor.t(item.name),
        c: item.color,
        d: common_vendor.t(item.description),
        e: index,
        f: common_vendor.o(($event) => $options.useTemplate(item), index)
      };
    }),
    h: common_vendor.f($data.scenarioTemplates, (item, index, i0) => {
      return {
        a: item.icon,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.tagText),
        d: item.color,
        e: common_vendor.t(item.description),
        f: index,
        g: common_vendor.o(($event) => $options.useTemplate(item), index)
      };
    }),
    i: common_vendor.f($data.guideSteps, (step, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(step.title),
        c: common_vendor.t(step.description),
        d: index
      };
    }),
    j: common_vendor.o((...args) => $options.createTemplate && $options.createTemplate(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/redpacket/create-template.js.map
