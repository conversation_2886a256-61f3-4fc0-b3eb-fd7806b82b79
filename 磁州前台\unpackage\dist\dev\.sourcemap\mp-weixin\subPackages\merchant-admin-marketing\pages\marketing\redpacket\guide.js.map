{"version": 3, "file": "guide.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcZ3VpZGUudnVl"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">红包营销指南</text>\n      <view class=\"navbar-right\">\n        <view class=\"share-icon\" @click=\"shareGuide\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <circle cx=\"18\" cy=\"5\" r=\"3\"></circle>\n            <circle cx=\"6\" cy=\"12\" r=\"3\"></circle>\n            <circle cx=\"18\" cy=\"19\" r=\"3\"></circle>\n            <line x1=\"8.59\" y1=\"13.51\" x2=\"15.42\" y2=\"17.49\"></line>\n            <line x1=\"15.41\" y1=\"6.51\" x2=\"8.59\" y2=\"10.49\"></line>\n          </svg>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 指南内容 -->\n    <scroll-view scroll-y class=\"guide-content\">\n      <!-- 指南头部 -->\n      <view class=\"guide-header\">\n        <image class=\"guide-banner\" src=\"/static/images/redpacket-guide-banner.png\" mode=\"aspectFill\"></image>\n        <view class=\"guide-intro\">\n          <text class=\"guide-title\">红包营销全攻略</text>\n          <text class=\"guide-subtitle\">提升用户活跃度与转化率的必备工具</text>\n        </view>\n      </view>\n      \n      <!-- 指南章节 -->\n      <view class=\"guide-sections\">\n        <!-- 章节1 -->\n        <view class=\"guide-section\">\n          <view class=\"section-header\">\n            <view class=\"section-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M12 2L2 7l10 5 10-5-10-5z\"></path>\n                <path d=\"M2 17l10 5 10-5\"></path>\n                <path d=\"M2 12l10 5 10-5\"></path>\n              </svg>\n            </view>\n            <text class=\"section-title\">什么是红包营销？</text>\n          </view>\n          <view class=\"section-content\">\n            <text class=\"section-text\">红包营销是一种基于用户心理的营销方式，通过发放红包激励用户参与活动，提高用户粘性，促进交易转化。在商家营销中，红包已成为不可或缺的工具。</text>\n            <view class=\"section-image-container\">\n              <image class=\"section-image\" src=\"/static/images/redpacket-intro.png\" mode=\"aspectFill\"></image>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 章节2 -->\n        <view class=\"guide-section\">\n          <view class=\"section-header\">\n            <view class=\"section-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"16\"></line>\n                <line x1=\"8\" y1=\"12\" x2=\"16\" y2=\"12\"></line>\n              </svg>\n            </view>\n            <text class=\"section-title\">红包营销的优势</text>\n          </view>\n          <view class=\"section-content\">\n            <view class=\"advantage-item\">\n              <view class=\"advantage-icon\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <polyline points=\"20 6 9 17 4 12\"></polyline>\n                </svg>\n              </view>\n              <text class=\"advantage-text\">提高用户活跃度与参与感</text>\n            </view>\n            <view class=\"advantage-item\">\n              <view class=\"advantage-icon\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <polyline points=\"20 6 9 17 4 12\"></polyline>\n                </svg>\n              </view>\n              <text class=\"advantage-text\">促进用户分享，实现裂变增长</text>\n            </view>\n            <view class=\"advantage-item\">\n              <view class=\"advantage-icon\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <polyline points=\"20 6 9 17 4 12\"></polyline>\n                </svg>\n              </view>\n              <text class=\"advantage-text\">提升品牌曝光度与美誉度</text>\n            </view>\n            <view class=\"advantage-item\">\n              <view class=\"advantage-icon\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                  <polyline points=\"20 6 9 17 4 12\"></polyline>\n                </svg>\n              </view>\n              <text class=\"advantage-text\">刺激消费，提高转化率</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 章节3 -->\n        <view class=\"guide-section\">\n          <view class=\"section-header\">\n            <view class=\"section-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"></path>\n                <circle cx=\"9\" cy=\"7\" r=\"4\"></circle>\n                <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\"></path>\n                <path d=\"M16 3.13a4 4 0 0 1 0 7.75\"></path>\n              </svg>\n            </view>\n            <text class=\"section-title\">红包营销的场景应用</text>\n          </view>\n          <view class=\"section-content\">\n            <view class=\"scenario-item\">\n              <text class=\"scenario-title\">新客获取</text>\n              <text class=\"scenario-desc\">通过发放红包吸引新用户注册，降低获客成本</text>\n            </view>\n            <view class=\"scenario-item\">\n              <text class=\"scenario-title\">节日营销</text>\n              <text class=\"scenario-desc\">在重要节日发放红包，提升用户好感度</text>\n            </view>\n            <view class=\"scenario-item\">\n              <text class=\"scenario-title\">社交裂变</text>\n              <text class=\"scenario-desc\">鼓励用户分享红包给好友，实现低成本获客</text>\n            </view>\n            <view class=\"scenario-item\">\n              <text class=\"scenario-title\">会员福利</text>\n              <text class=\"scenario-desc\">为会员提供专属红包，提升会员忠诚度</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 章节4 -->\n        <view class=\"guide-section\">\n          <view class=\"section-header\">\n            <view class=\"section-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n                <polyline points=\"14 2 14 8 20 8\"></polyline>\n                <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n                <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n                <polyline points=\"10 9 9 9 8 9\"></polyline>\n              </svg>\n            </view>\n            <text class=\"section-title\">红包营销最佳实践</text>\n          </view>\n          <view class=\"section-content\">\n            <view class=\"practice-item\">\n              <text class=\"practice-number\">01</text>\n              <view class=\"practice-detail\">\n                <text class=\"practice-title\">设定明确的营销目标</text>\n                <text class=\"practice-desc\">在开始红包活动前，明确活动目标是获客、促活还是转化</text>\n              </view>\n            </view>\n            <view class=\"practice-item\">\n              <text class=\"practice-number\">02</text>\n              <view class=\"practice-detail\">\n                <text class=\"practice-title\">精准的用户定向</text>\n                <text class=\"practice-desc\">根据用户画像和行为数据，向最有价值的用户群体投放红包</text>\n              </view>\n            </view>\n            <view class=\"practice-item\">\n              <text class=\"practice-number\">03</text>\n              <view class=\"practice-detail\">\n                <text class=\"practice-title\">设计合理的红包金额</text>\n                <text class=\"practice-desc\">根据商品价格和预期转化率，设计具有吸引力的红包金额</text>\n              </view>\n            </view>\n            <view class=\"practice-item\">\n              <text class=\"practice-number\">04</text>\n              <view class=\"practice-detail\">\n                <text class=\"practice-title\">数据追踪与优化</text>\n                <text class=\"practice-desc\">实时监控红包活动数据，根据效果及时调整策略</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 章节5 -->\n        <view class=\"guide-section\">\n          <view class=\"section-header\">\n            <view class=\"section-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#FF4D4F\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n                <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\n              </svg>\n            </view>\n            <text class=\"section-title\">常见问题解答</text>\n          </view>\n          <view class=\"section-content\">\n            <view class=\"faq-item\" @click=\"toggleFaq(0)\">\n              <view class=\"faq-question\">\n                <text class=\"question-text\">如何设置红包金额才最有效？</text>\n                <view class=\"arrow-icon\" :class=\"{ 'arrow-down': openFaq === 0 }\"></view>\n              </view>\n              <view class=\"faq-answer\" v-if=\"openFaq === 0\">\n                <text class=\"answer-text\">红包金额应根据商品价格、目标转化率和ROI来设定。一般建议设置为商品价格的5%-20%，既能吸引用户又能保证营销效益。</text>\n              </view>\n            </view>\n            <view class=\"faq-item\" @click=\"toggleFaq(1)\">\n              <view class=\"faq-question\">\n                <text class=\"question-text\">红包活动多久举办一次比较合适？</text>\n                <view class=\"arrow-icon\" :class=\"{ 'arrow-down': openFaq === 1 }\"></view>\n              </view>\n              <view class=\"faq-answer\" v-if=\"openFaq === 1\">\n                <text class=\"answer-text\">红包活动频率应根据业务周期和用户行为来决定。一般建议每月1-2次大型红包活动，结合节假日和促销季，避免过于频繁导致用户疲劳。</text>\n              </view>\n            </view>\n            <view class=\"faq-item\" @click=\"toggleFaq(2)\">\n              <view class=\"faq-question\">\n                <text class=\"question-text\">如何防止红包被恶意领取？</text>\n                <view class=\"arrow-icon\" :class=\"{ 'arrow-down': openFaq === 2 }\"></view>\n              </view>\n              <view class=\"faq-answer\" v-if=\"openFaq === 2\">\n                <text class=\"answer-text\">可以设置领取条件和限制，如实名认证、手机号验证、每人限领一次等。同时设置红包使用门槛和有效期，减少恶意领取的可能性。</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系我们 -->\n      <view class=\"contact-section\">\n        <text class=\"contact-title\">需要更多帮助？</text>\n        <text class=\"contact-desc\">如果您对红包营销有任何疑问，请联系我们的客服团队</text>\n        <button class=\"contact-btn\" @click=\"contactService\">联系客服</button>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      openFaq: -1,\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    shareGuide() {\n      uni.showActionSheet({\n        itemList: ['分享给好友', '分享到朋友圈', '复制链接'],\n        success: function(res) {\n          uni.showToast({\n            title: '分享成功',\n            icon: 'success'\n          });\n        }\n      });\n    },\n    toggleFaq(index) {\n      this.openFaq = this.openFaq === index ? -1 : index;\n    },\n    contactService() {\n      uni.makePhoneCall({\n        phoneNumber: '************'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* 导航栏样式 */\n.navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background-color: #fff;\n  padding: 0 15px;\n  position: relative;\n}\n\n.navbar-back {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #333;\n  border-left: 2px solid #333;\n  transform: rotate(-45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 600;\n  color: #333;\n}\n\n.navbar-right {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.share-icon {\n  color: #333;\n}\n\n/* 指南内容样式 */\n.guide-content {\n  flex: 1;\n  padding-bottom: 20px;\n}\n\n.guide-header {\n  position: relative;\n  height: 180px;\n  overflow: hidden;\n}\n\n.guide-banner {\n  width: 100%;\n  height: 100%;\n}\n\n.guide-intro {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 15px;\n  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);\n}\n\n.guide-title {\n  font-size: 22px;\n  font-weight: 700;\n  color: #fff;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.guide-subtitle {\n  font-size: 14px;\n  color: rgba(255,255,255,0.9);\n  display: block;\n}\n\n.guide-sections {\n  padding: 15px;\n}\n\n.guide-section {\n  background-color: #fff;\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n}\n\n.section-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.section-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  background-color: rgba(255, 77, 79, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.section-content {\n  padding-left: 46px;\n}\n\n.section-text {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n.section-image-container {\n  margin-top: 15px;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.section-image {\n  width: 100%;\n  height: 150px;\n}\n\n/* 优势项样式 */\n.advantage-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.advantage-icon {\n  margin-right: 10px;\n  color: #FF4D4F;\n}\n\n.advantage-text {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 场景项样式 */\n.scenario-item {\n  margin-bottom: 12px;\n}\n\n.scenario-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.scenario-desc {\n  font-size: 13px;\n  color: #666;\n  display: block;\n}\n\n/* 最佳实践样式 */\n.practice-item {\n  display: flex;\n  margin-bottom: 15px;\n}\n\n.practice-number {\n  font-size: 18px;\n  font-weight: 700;\n  color: #FF4D4F;\n  margin-right: 12px;\n}\n\n.practice-detail {\n  flex: 1;\n}\n\n.practice-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.practice-desc {\n  font-size: 13px;\n  color: #666;\n  display: block;\n}\n\n/* FAQ样式 */\n.faq-item {\n  margin-bottom: 10px;\n  border-bottom: 1px solid #eee;\n  padding-bottom: 10px;\n}\n\n.faq-question {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.question-text {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n}\n\n.arrow-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 1px solid #999;\n  border-right: 1px solid #999;\n  transform: rotate(45deg);\n  transition: transform 0.3s;\n}\n\n.arrow-down {\n  transform: rotate(135deg);\n}\n\n.faq-answer {\n  margin-top: 10px;\n  padding: 10px;\n  background-color: #f9f9f9;\n  border-radius: 6px;\n}\n\n.answer-text {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.5;\n}\n\n/* 联系我们样式 */\n.contact-section {\n  margin: 0 15px 20px;\n  padding: 20px;\n  background-color: #fff;\n  border-radius: 10px;\n  text-align: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n}\n\n.contact-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 10px;\n}\n\n.contact-desc {\n  font-size: 14px;\n  color: #666;\n  display: block;\n  margin-bottom: 15px;\n}\n\n.contact-btn {\n  background-color: #FF4D4F;\n  color: #fff;\n  border: none;\n  border-radius: 20px;\n  padding: 8px 20px;\n  font-size: 14px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA4OA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,UAAU,MAAM;AAAA,QACpC,SAAS,SAAS,KAAK;AACrBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,UAAU,OAAO;AACf,WAAK,UAAU,KAAK,YAAY,QAAQ,KAAK;AAAA,IAC9C;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzQA,GAAG,WAAW,eAAe;"}