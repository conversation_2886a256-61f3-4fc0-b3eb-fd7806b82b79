"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      // 选中的日期范围
      selectedDateRange: "过去30天",
      // 客户数据概览
      customerMetrics: {
        totalCustomers: "3,216",
        customerGrowth: "+8.3%",
        newCustomers: "238",
        newCustomerGrowth: "+12.7%",
        activeRate: "42.8",
        activeRateChange: "-2.5%",
        repurchaseRate: "31.5",
        repurchaseRateChange: "+5.4%"
      },
      // 客户分群标签页
      segmentTabs: [
        { id: 1, name: "高价值客户" },
        { id: 2, name: "活跃消费者" },
        { id: 3, name: "新客户" },
        { id: 4, name: "流失风险" },
        { id: 5, name: "低频消费" }
      ],
      currentSegment: 1,
      // 分群数据
      segmentData: [
        {
          id: 1,
          name: "高价值客户",
          tagType: "high-value",
          tag: "高价值",
          customerCount: "325",
          avgValue: "4,625",
          purchaseFrequency: "3.7次/月",
          traits: ["高频消费者", "忠诚会员", "品质敏感", "夜间购物", "追求体验"],
          chartData: [8500, 9200, 9800, 10200, 11500, 12300]
        },
        {
          id: 2,
          name: "活跃消费者",
          tagType: "active",
          tag: "活跃",
          customerCount: "462",
          avgValue: "1,850",
          purchaseFrequency: "2.8次/月",
          traits: ["周末消费者", "热衷促销", "追求性价比", "多频次", "购物篮多样"],
          chartData: [3600, 3900, 4200, 4500, 4800, 5100]
        },
        {
          id: 3,
          name: "新客户",
          tagType: "new",
          tag: "新客",
          customerCount: "538",
          avgValue: "960",
          purchaseFrequency: "1.2次/月",
          traits: ["价格敏感", "尝试购买", "小额消费", "移动端", "社交媒体引导"],
          chartData: [0, 0, 0, 0, 350, 950]
        },
        {
          id: 4,
          name: "流失风险",
          tagType: "risk",
          tag: "风险",
          customerCount: "186",
          avgValue: "2,340",
          purchaseFrequency: "0.5次/月",
          traits: ["消费频次下降", "曾为高价值", "购买周期拉长", "有投诉历史", "客服互动"],
          chartData: [5200, 4800, 4200, 3600, 3200, 2800]
        },
        {
          id: 5,
          name: "低频消费",
          tagType: "low",
          tag: "低频",
          customerCount: "1,705",
          avgValue: "780",
          purchaseFrequency: "0.7次/月",
          traits: ["季节性消费", "特定品类", "低参与度", "价格敏感", "偶发性购买"],
          chartData: [1200, 1050, 1350, 1100, 950, 1300]
        }
      ],
      // 客户生命周期数据
      lifecycle: {
        newCustomers: "538",
        newTrend: "up",
        newGrowth: "+16.7%",
        growingCustomers: "782",
        growingTrend: "up",
        growingGrowth: "+8.3%",
        matureCustomers: "1,246",
        matureTrend: "down",
        matureGrowth: "-2.1%",
        riskCustomers: "650",
        riskTrend: "down",
        riskGrowth: "-5.3%",
        insight: "新客户增长强劲，但成熟客户群体略有下降，建议关注成熟客户的留存策略。"
      },
      // RFM价值矩阵
      rfmMatrix: [
        [
          { name: "重要价值", value: 12, type: "high" },
          { name: "重要保持", value: 8, type: "high" },
          { name: "重要发展", value: 6, type: "medium" }
        ],
        [
          { name: "一般价值", value: 10, type: "medium" },
          { name: "一般保持", value: 14, type: "medium" },
          { name: "一般发展", value: 11, type: "low" }
        ],
        [
          { name: "潜力客户", value: 7, type: "potential" },
          { name: "新客发展", value: 18, type: "potential" },
          { name: "低价值", value: 14, type: "low" }
        ]
      ],
      // 客户标签分类
      tagCategories: [
        { id: 1, name: "消费习惯" },
        { id: 2, name: "生活方式" },
        { id: 3, name: "人口特征" },
        { id: 4, name: "行为特征" },
        { id: 5, name: "偏好喜好" }
      ],
      currentTagCategory: 1,
      // 标签数据
      tagData: {
        1: [
          // 消费习惯标签
          { name: "夜间购物", count: 625, size: "large" },
          { name: "周末消费", count: 842, size: "large" },
          { name: "促销敏感", count: 1236, size: "xlarge" },
          { name: "高频次", count: 465, size: "medium" },
          { name: "大额消费", count: 284, size: "medium" },
          { name: "理性决策", count: 356, size: "medium" },
          { name: "冲动购买", count: 478, size: "medium" },
          { name: "季节性", count: 592, size: "large" },
          { name: "特殊节日", count: 734, size: "large" },
          { name: "长期积累", count: 185, size: "small" },
          { name: "预算型", count: 605, size: "large" },
          { name: "奢侈型", count: 146, size: "small" }
        ],
        2: [
          // 生活方式标签
          { name: "重视家庭", count: 865, size: "large" },
          { name: "现代简约", count: 742, size: "large" },
          { name: "工作繁忙", count: 638, size: "large" },
          { name: "喜爱旅行", count: 425, size: "medium" },
          { name: "健康生活", count: 528, size: "medium" },
          { name: "社交活跃", count: 356, size: "medium" },
          { name: "宅家爱好者", count: 492, size: "medium" },
          { name: "典雅风格", count: 168, size: "small" },
          { name: "环保意识", count: 274, size: "medium" },
          { name: "科技发烧友", count: 183, size: "small" },
          { name: "追求品质", count: 427, size: "medium" }
        ],
        3: [
          // 人口特征标签
          { name: "25-34岁", count: 782, size: "large" },
          { name: "35-44岁", count: 864, size: "large" },
          { name: "城市居民", count: 1568, size: "xlarge" },
          { name: "高收入", count: 485, size: "medium" },
          { name: "中等收入", count: 1246, size: "xlarge" },
          { name: "已婚", count: 956, size: "large" },
          { name: "有子女", count: 842, size: "large" },
          { name: "高学历", count: 685, size: "large" },
          { name: "女性", count: 1842, size: "xlarge" },
          { name: "男性", count: 1374, size: "xlarge" }
        ],
        4: [
          // 行为特征标签
          { name: "多渠道", count: 562, size: "large" },
          { name: "移动端", count: 1675, size: "xlarge" },
          { name: "评论积极", count: 246, size: "medium" },
          { name: "经常退货", count: 128, size: "small" },
          { name: "分享推荐", count: 184, size: "small" },
          { name: "货比三家", count: 567, size: "large" },
          { name: "忠诚会员", count: 425, size: "medium" },
          { name: "投诉历史", count: 78, size: "xsmall" },
          { name: "客服咨询", count: 265, size: "medium" },
          { name: "自助服务", count: 684, size: "large" },
          { name: "快速决策", count: 347, size: "medium" },
          { name: "深度研究", count: 428, size: "medium" }
        ],
        5: [
          // 偏好喜好标签
          { name: "北欧风格", count: 456, size: "medium" },
          { name: "简约设计", count: 687, size: "large" },
          { name: "环保材质", count: 345, size: "medium" },
          { name: "明亮色调", count: 426, size: "medium" },
          { name: "温暖色系", count: 372, size: "medium" },
          { name: "多功能", count: 582, size: "large" },
          { name: "极简主义", count: 428, size: "medium" },
          { name: "复古风格", count: 216, size: "small" },
          { name: "高科技", count: 284, size: "medium" },
          { name: "实用主义", count: 745, size: "large" },
          { name: "精致装饰", count: 367, size: "medium" },
          { name: "舒适体验", count: 648, size: "large" }
        ]
      },
      // 客户互动数据
      messageStats: {
        unread: 16,
        today: 28,
        responseRate: 93.5,
        responseTime: "8分钟"
      },
      // 最近消息
      recentMessages: [
        {
          id: 1,
          customerName: "张先生",
          customerAvatar: "/static/images/avatar-1.png",
          time: "10分钟前",
          content: "请问我订购的沙发什么时候能到货？",
          unreadCount: 1
        },
        {
          id: 2,
          customerName: "王女士",
          customerAvatar: "/static/images/avatar-2.png",
          time: "28分钟前",
          content: "收到的床垫有点问题，可以联系我处理一下吗？",
          unreadCount: 2
        },
        {
          id: 3,
          customerName: "李先生",
          customerAvatar: "/static/images/avatar-3.png",
          time: "1小时前",
          content: "请问你们有没有提供上门测量服务？",
          unreadCount: 0
        },
        {
          id: 4,
          customerName: "赵女士",
          customerAvatar: "/static/images/avatar-4.png",
          time: "2小时前",
          content: "窗帘的安装视频我看了，但还是有些不明白...",
          unreadCount: 0
        }
      ]
    };
  },
  computed: {
    // 获取当前选中的分群数据
    currentSegmentData() {
      return this.segmentData.find((segment) => segment.id === this.currentSegment) || this.segmentData[0];
    },
    // 获取当前标签分类的标签
    currentCategoryTags() {
      return this.tagData[this.currentTagCategory] || [];
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.drawSegmentChart();
    });
  },
  methods: {
    // 基础导航
    goBack() {
      common_vendor.index.navigateBack();
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 切换客户分群
    switchSegment(segmentId) {
      this.currentSegment = segmentId;
      this.$nextTick(() => {
        this.drawSegmentChart();
      });
    },
    // 定向营销客群
    targetSegment(segment) {
      common_vendor.index.navigateTo({
        url: `/pages/customer/target?id=${segment.id}&name=${segment.name}`
      });
    },
    // 查看分群详情
    viewSegmentDetail(segment) {
      common_vendor.index.navigateTo({
        url: `/pages/customer/segment-detail?id=${segment.id}`
      });
    },
    // 导出分群数据
    exportSegmentData(segment) {
      common_vendor.index.showToast({
        title: `正在导出${segment.name}数据`,
        icon: "none"
      });
    },
    // 绘制分群趋势图表
    drawSegmentChart() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-customer/pages/customer/index.vue:685", "绘制图表，数据：", this.currentSegmentData.chartData);
    },
    // 查看RFM客群详情
    viewRfmSegment(cell) {
      common_vendor.index.navigateTo({
        url: `/pages/customer/rfm-detail?name=${cell.name}&type=${cell.type}`
      });
    },
    // 切换标签分类
    switchTagCategory(categoryId) {
      this.currentTagCategory = categoryId;
    },
    // 查看标签客户列表
    viewTagCustomers(tag) {
      common_vendor.index.navigateTo({
        url: `/pages/customer/tag-customers?tag=${tag.name}&count=${tag.count}`
      });
    },
    // 创建新标签
    createNewTag() {
      common_vendor.index.navigateTo({
        url: "/pages/customer/create-tag"
      });
    },
    // 查看聊天记录
    viewConversation(message) {
      common_vendor.index.navigateTo({
        url: `/pages/customer/conversation?id=${message.id}&name=${message.customerName}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($data.selectedDateRange),
    c: common_vendor.t($data.customerMetrics.totalCustomers),
    d: common_vendor.t($data.customerMetrics.customerGrowth),
    e: common_vendor.t($data.customerMetrics.newCustomers),
    f: common_vendor.t($data.customerMetrics.newCustomerGrowth),
    g: common_vendor.t($data.customerMetrics.activeRate),
    h: common_vendor.t($data.customerMetrics.activeRateChange),
    i: common_vendor.t($data.customerMetrics.repurchaseRate),
    j: common_vendor.t($data.customerMetrics.repurchaseRateChange),
    k: common_vendor.o(($event) => $options.navigateTo("/pages/customer/segments")),
    l: common_vendor.f($data.segmentTabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentSegment === tab.id ? 1 : "",
        d: common_vendor.o(($event) => $options.switchSegment(tab.id), index)
      };
    }),
    m: common_vendor.t($options.currentSegmentData.name),
    n: common_vendor.t($options.currentSegmentData.tag),
    o: common_vendor.n($options.currentSegmentData.tagType),
    p: common_vendor.o(($event) => $options.targetSegment($options.currentSegmentData)),
    q: common_vendor.t($options.currentSegmentData.customerCount),
    r: common_vendor.t($options.currentSegmentData.avgValue),
    s: common_vendor.t($options.currentSegmentData.purchaseFrequency),
    t: common_vendor.f($options.currentSegmentData.traits, (trait, index, i0) => {
      return {
        a: common_vendor.t(trait),
        b: index
      };
    }),
    v: common_vendor.o(($event) => $options.viewSegmentDetail($options.currentSegmentData)),
    w: common_vendor.o(($event) => $options.exportSegmentData($options.currentSegmentData)),
    x: common_vendor.o(($event) => $options.navigateTo("/pages/customer/lifecycle")),
    y: common_assets._imports_0$48,
    z: common_vendor.t($data.lifecycle.newCustomers),
    A: common_vendor.t($data.lifecycle.newGrowth),
    B: common_vendor.n($data.lifecycle.newTrend),
    C: common_vendor.t($data.lifecycle.growingCustomers),
    D: common_vendor.t($data.lifecycle.growingGrowth),
    E: common_vendor.n($data.lifecycle.growingTrend),
    F: common_vendor.t($data.lifecycle.matureCustomers),
    G: common_vendor.t($data.lifecycle.matureGrowth),
    H: common_vendor.n($data.lifecycle.matureTrend),
    I: common_vendor.t($data.lifecycle.riskCustomers),
    J: common_vendor.t($data.lifecycle.riskGrowth),
    K: common_vendor.n($data.lifecycle.riskTrend),
    L: common_vendor.t($data.lifecycle.insight),
    M: common_vendor.o(($event) => $options.navigateTo("/pages/customer/rfm")),
    N: common_vendor.f($data.rfmMatrix, (row, rowIndex, i0) => {
      return {
        a: common_vendor.f(row, (cell, cellIndex, i1) => {
          return {
            a: common_vendor.t(cell.name),
            b: common_vendor.t(cell.value),
            c: "cell-" + cellIndex,
            d: common_vendor.n(cell.type),
            e: common_vendor.o(($event) => $options.viewRfmSegment(cell), "cell-" + cellIndex)
          };
        }),
        b: "row-" + rowIndex
      };
    }),
    O: common_vendor.o(($event) => $options.navigateTo("/pages/customer/tags")),
    P: common_vendor.f($data.tagCategories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: index,
        c: $data.currentTagCategory === category.id ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTagCategory(category.id), index)
      };
    }),
    Q: common_vendor.f($options.currentCategoryTags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag.name),
        b: common_vendor.t(tag.count),
        c: index,
        d: common_vendor.n(tag.size),
        e: common_vendor.o(($event) => $options.viewTagCustomers(tag), index)
      };
    }),
    R: common_vendor.o((...args) => $options.createNewTag && $options.createNewTag(...args)),
    S: common_vendor.o(($event) => $options.navigateTo("/pages/customer/interaction")),
    T: common_vendor.t($data.messageStats.unread),
    U: common_vendor.t($data.messageStats.today),
    V: common_vendor.t($data.messageStats.responseRate),
    W: common_vendor.t($data.messageStats.responseTime),
    X: common_vendor.f($data.recentMessages, (message, index, i0) => {
      return common_vendor.e({
        a: message.customerAvatar,
        b: common_vendor.t(message.customerName),
        c: common_vendor.t(message.time),
        d: common_vendor.t(message.content),
        e: message.unreadCount > 0
      }, message.unreadCount > 0 ? {
        f: common_vendor.t(message.unreadCount)
      } : {}, {
        g: index,
        h: common_vendor.o(($event) => $options.viewConversation(message), index)
      });
    }),
    Y: common_vendor.o(($event) => $options.navigateTo("/pages/customer/message")),
    Z: common_vendor.o(($event) => $options.navigateTo("/pages/customer/notification")),
    aa: common_vendor.o(($event) => $options.navigateTo("/pages/customer/survey"))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-customer/pages/customer/index.js.map
