{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAC,WAAW,EAAE,eAAe,EAAC,MAAM,uBAAuB,CAAC;AACnE,OAAO,EAAC,iBAAiB,EAAC,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAC,aAAa,EAAE,YAAY,EAAC,MAAM,sBAAsB,CAAC;AAEjE,IAAM,kBAAkB,yBACjB,eAAe,KAClB,GAAG,EAAE,eAAe,CAAC,KAAK,GAC7B,CAAC;AAqBF,IAAM,aAAa,GAA+B;IAC9C,YAAY,EAAE,UAAU;IACxB,QAAQ,EAAE,iFAAiF;IAC3F,iBAAiB,EAAE,0GAA0G;IAC7H,qBAAqB,EAAE,qGAAqG;IAC5H,SAAS,EAAE,yIAAyI;CACvJ,CAAC;AAEF,IAAM,oBAAoB,GAAkB;IACxC,IAAI,EAAE,cAAc;IACpB,KAAK,EAAE,KAAK;IACZ,OAAO,EAAE,SAAS;CACrB,CAAC;AAEF,8EAA8E;AAC9E,MAAM,UAAU,MAAM,CAClB,IAA+B,EAC/B,EAAiG;QAAjG,qBAA6E,oBAAoB,KAAA,EAAhG,YAAqB,EAArB,IAAI,mBAAG,cAAc,KAAA,EAAE,eAAmB,EAAnB,OAAO,mBAAG,SAAS,KAAA,EAAE,aAAa,EAAb,KAAK,mBAAG,KAAK,KAAA;IAE1D,IAAI,CAAC,IAAI,EAAE,CAAC;QACR,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IACzC,IAAM,UAAU,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC;IACxD,IAAM,KAAK,GAAG,OAAO,KAAK,aAAa,CAAC;IAExC,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,UAAC,KAAK;QAC3D,IAAI,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC9E,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACrE,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC;AAED,IAAM,oBAAoB,GAAkB;IACxC,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,KAAK;CACf,CAAC;AAEF,IAAM,MAAM,GAAG,2CAA2C,CAAC;AAC3D,IAAM,SAAS,GAAG,+CAA+C,CAAC;AAElE,IAAM,iBAAiB,GAA+D;IAClF,GAAG,EAAE;QACD,MAAM,QAAA;QACN,SAAS,WAAA;QACT,IAAI,EAAE,WAAW,CAAC,GAAG;KACxB;IACD,KAAK,EAAE;QACH,MAAM,QAAA;QACN,SAAS,WAAA;QACT,IAAI,EAAE,WAAW,CAAC,KAAK;KAC1B;IACD,KAAK,EAAE;QACH,MAAM,QAAA;QACN,SAAS,WAAA;QACT,IAAI,EAAE,WAAW,CAAC,KAAK;KAC1B;CACJ,CAAC;AAEF,IAAM,aAAa,yBACZ,iBAAiB,KACpB,GAAG,EAAE,iBAAiB,CAAC,KAAK,GAC/B,CAAC;AAEF,IAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACzC,IAAM,eAAe,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AAE5C,IAAM,0BAA0B,GAAkB;IAC9C,KAAK,EAAE,KAAK;CACf,CAAC;AAEF,SAAS,gBAAgB,CACrB,MAAc,EACd,UAAkC,EAClC,WAAoB,EACpB,QAAiB;IAEjB,IAAI,YAAY,GAAG,MAAM,CAAC;IAC1B,IAAM,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvD,IAAI,WAAW,IAAI,oBAAoB,KAAK,GAAG,EAAE,CAAC;QAC9C,YAAY,GAAG,MAAM,CAAC;IAC1B,CAAC;SAAM,IAAI,QAAQ,IAAI,oBAAoB,KAAK,GAAG,EAAE,CAAC;QAClD,YAAY,GAAG,MAAM,CAAC;IAC1B,CAAC;SAAM,CAAC;QACJ,IAAM,uBAAuB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,uBAAuB,EAAE,CAAC;YAC1B,YAAY,GAAG,uBAAuB,CAAC;QAC3C,CAAC;aAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAChD,IAAM,gBAAgB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACnC,IAAM,UAAU,GACZ,gBAAgB,IAAI,GAAG,IAAI,gBAAgB,IAAI,GAAG;gBAC9C,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAChC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAErC,YAAY;gBACR,UAAU,IAAI,QAAQ;oBAClB,CAAC,CAAC,eAAe;oBACjB,CAAC,CAAC,UAAU,GAAG,KAAK;wBACpB,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC;wBAC3B,CAAC,CAAC,YAAY,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC;QACxE,CAAC;IACL,CAAC;IACD,OAAO,YAAY,CAAC;AACxB,CAAC;AAED,8BAA8B;AAC9B,MAAM,UAAU,YAAY,CACxB,MAAiC,EACjC,EAA2D;QAA3D,qBAAiC,0BAA0B,KAAA,EAA1D,aAAa,EAAb,KAAK,mBAAG,KAAK,KAAA;IAEd,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;IACd,CAAC;IACD,OAAO,gBAAgB,CAAC,MAAM,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACtF,CAAC;AAED,uCAAuC;AACvC,MAAM,UAAU,MAAM,CAClB,IAA+B,EAC/B,EAAkG;QAAlG,qBAA8E,oBAAoB,KAAA,EAAjG,aAAa,EAAb,KAAK,mBAAG,KAAK,KAAA,EAAE,aAA2C,EAA3C,KAAK,mBAAG,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,KAAA;IAE3D,IAAI,CAAC,IAAI,EAAE,CAAC;QACR,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACjD,IAAM,UAAU,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;IACtD,IAAM,WAAW,GAAG,KAAK,KAAK,WAAW,CAAC;IAC1C,IAAM,QAAQ,GAAG,KAAK,KAAK,QAAQ,CAAC;IAEpC,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,UAAC,MAAM,IAAK,OAAA,gBAAgB,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC,EAA3D,CAA2D,CAAC,CAAC;AAC/G,CAAC"}