"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      refreshing: false,
      loading: false,
      noMore: false,
      currentTabIndex: 0,
      currentStatusIndex: 0,
      tabs: [
        { name: "可领取" },
        { name: "我的券" }
      ],
      couponStatus: [
        { name: "未使用" },
        { name: "已使用" },
        { name: "已过期" }
      ],
      availableCoupons: [
        {
          id: 1,
          title: "新人专享券",
          description: "仅限新用户使用",
          type: "cash",
          value: "30",
          minAmount: 0,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3).toISOString(),
          tag: "新人专享"
        },
        {
          id: 2,
          title: "满减优惠券",
          description: "全场通用",
          type: "cash",
          value: "10",
          minAmount: 99,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1e3).toISOString(),
          tag: "热门"
        },
        {
          id: 3,
          title: "满减优惠券",
          description: "全场通用",
          type: "cash",
          value: "50",
          minAmount: 299,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 20 * 24 * 60 * 60 * 1e3).toISOString(),
          tag: "限量"
        },
        {
          id: 4,
          title: "满减优惠券",
          description: "仅限食品类",
          type: "cash",
          value: "20",
          minAmount: 129,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1e3).toISOString()
        },
        {
          id: 5,
          title: "满减优惠券",
          description: "仅限电器类",
          type: "cash",
          value: "100",
          minAmount: 1e3,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 25 * 24 * 60 * 60 * 1e3).toISOString(),
          tag: "特惠"
        }
      ],
      myCoupons: [
        {
          id: 6,
          title: "满减优惠券",
          description: "全场通用",
          type: "cash",
          value: "15",
          minAmount: 99,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1e3).toISOString(),
          status: "valid",
          tag: "热门"
        },
        {
          id: 7,
          title: "满减优惠券",
          description: "仅限服装类",
          type: "cash",
          value: "30",
          minAmount: 199,
          startTime: new Date(Date.now()).toISOString(),
          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1e3).toISOString(),
          status: "valid"
        },
        {
          id: 8,
          title: "满减优惠券",
          description: "全场通用",
          type: "cash",
          value: "50",
          minAmount: 299,
          startTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1e3).toISOString(),
          endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1e3).toISOString(),
          status: "expired",
          tag: "限量"
        },
        {
          id: 9,
          title: "满减优惠券",
          description: "仅限食品类",
          type: "cash",
          value: "20",
          minAmount: 129,
          startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1e3).toISOString(),
          endTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1e3).toISOString(),
          status: "used"
        }
      ]
    };
  },
  computed: {
    tabLineStyle() {
      const width = 100 / this.tabs.length;
      const left = this.currentTabIndex * width;
      return {
        width: width + "%",
        transform: `translateX(${left * 100}%)`
      };
    }
  },
  onLoad() {
    this.fetchData();
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 切换选项卡
    switchTab(index) {
      this.currentTabIndex = index;
      this.fetchData();
    },
    // 切换优惠券状态
    switchStatus(index) {
      this.currentStatusIndex = index;
    },
    // 获取我的优惠券列表
    getMyCoupons() {
      const statusMap = ["valid", "used", "expired"];
      const status = statusMap[this.currentStatusIndex];
      return this.myCoupons.filter((item) => item.status === status);
    },
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore)
        return;
      this.loading = true;
      setTimeout(() => {
        if (this.currentTabIndex === 0) {
          const moreCoupons = [
            {
              id: 10,
              title: "满减优惠券",
              description: "仅限母婴类",
              type: "cash",
              value: "25",
              minAmount: 199,
              startTime: new Date(Date.now()).toISOString(),
              endTime: new Date(Date.now() + 18 * 24 * 60 * 60 * 1e3).toISOString()
            },
            {
              id: 11,
              title: "满减优惠券",
              description: "仅限美妆类",
              type: "cash",
              value: "40",
              minAmount: 299,
              startTime: new Date(Date.now()).toISOString(),
              endTime: new Date(Date.now() + 12 * 24 * 60 * 60 * 1e3).toISOString(),
              tag: "爆款"
            }
          ];
          this.availableCoupons = [...this.availableCoupons, ...moreCoupons];
        }
        this.noMore = true;
        this.loading = false;
      }, 1500);
    },
    // 获取数据
    fetchData() {
    },
    // 领取优惠券
    receiveCoupon(id) {
      common_vendor.index.showLoading({
        title: "领取中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const coupon = this.availableCoupons.find((item) => item.id === id);
        if (coupon) {
          const myCoupon = {
            ...coupon,
            id: Date.now(),
            // 生成新ID
            status: "valid"
          };
          this.myCoupons.push(myCoupon);
          common_vendor.index.showToast({
            title: "领取成功",
            icon: "success"
          });
        }
      }, 1e3);
    },
    // 使用优惠券
    useCoupon(id) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/coupon/detail?id=${id}`
      });
    },
    // 查看优惠券详情
    viewCouponDetail(id) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity-showcase/pages/coupon/detail?id=${id}`
      });
    },
    // 获取活动时间文本
    getTimeText(startTime, endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      const startMonth = start.getMonth() + 1;
      const startDay = start.getDate();
      const endMonth = end.getMonth() + 1;
      const endDay = end.getDate();
      return `${startMonth}.${startDay}-${endMonth}.${endDay}`;
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "valid": "未使用",
        "used": "已使用",
        "expired": "已过期"
      };
      return statusMap[status] || "";
    },
    // 获取空状态提示文本
    getEmptyText() {
      const textMap = [
        "您还没有未使用的优惠券",
        "您还没有已使用的优惠券",
        "您还没有已过期的优惠券"
      ];
      return textMap[this.currentStatusIndex] || "暂无数据";
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.p({
      d: "M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z",
      fill: "#FFFFFF"
    }),
    d: common_vendor.p({
      viewBox: "0 0 1024 1024",
      xmlns: "http://www.w3.org/2000/svg",
      width: "22",
      height: "22"
    }),
    e: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    f: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTabIndex === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    g: common_vendor.s($options.tabLineStyle),
    h: $data.currentTabIndex === 0
  }, $data.currentTabIndex === 0 ? {
    i: common_vendor.f($data.availableCoupons, (item, index, i0) => {
      return common_vendor.e({
        a: item.type === "cash"
      }, item.type === "cash" ? {} : {}, {
        b: common_vendor.t(item.value),
        c: item.minAmount > 0
      }, item.minAmount > 0 ? {
        d: common_vendor.t(item.minAmount)
      } : {}, {
        e: common_vendor.t(item.title),
        f: common_vendor.t(item.description),
        g: common_vendor.t($options.getTimeText(item.startTime, item.endTime)),
        h: common_vendor.o(($event) => $options.viewCouponDetail(item.id), index),
        i: common_vendor.o(($event) => $options.receiveCoupon(item.id), index),
        j: item.tag
      }, item.tag ? {
        k: common_vendor.t(item.tag)
      } : {}, {
        l: index
      });
    })
  } : common_vendor.e({
    j: common_vendor.f($data.couponStatus, (status, index, i0) => {
      return {
        a: common_vendor.t(status.name),
        b: index,
        c: $data.currentStatusIndex === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchStatus(index), index)
      };
    }),
    k: $options.getMyCoupons().length > 0
  }, $options.getMyCoupons().length > 0 ? {
    l: common_vendor.f($options.getMyCoupons(), (item, index, i0) => {
      return common_vendor.e({
        a: item.type === "cash"
      }, item.type === "cash" ? {} : {}, {
        b: common_vendor.t(item.value),
        c: item.minAmount > 0
      }, item.minAmount > 0 ? {
        d: common_vendor.t(item.minAmount)
      } : {}, {
        e: common_vendor.t(item.title),
        f: common_vendor.t(item.description),
        g: common_vendor.t($options.getTimeText(item.startTime, item.endTime)),
        h: item.status === "valid"
      }, item.status === "valid" ? {
        i: common_vendor.o(($event) => $options.useCoupon(item.id), index)
      } : {
        j: common_vendor.t($options.getStatusText(item.status))
      }, {
        k: item.tag
      }, item.tag ? {
        l: common_vendor.t(item.tag)
      } : {}, {
        m: item.status !== "valid"
      }, item.status !== "valid" ? {} : {}, {
        n: item.status === "used"
      }, item.status === "used" ? {} : item.status === "expired" ? {} : {}, {
        o: item.status === "expired",
        p: index,
        q: item.status === "used" ? 1 : "",
        r: item.status === "expired" ? 1 : ""
      });
    })
  } : common_vendor.e({
    m: common_assets._imports_1$53,
    n: common_vendor.t($options.getEmptyText()),
    o: $data.currentStatusIndex === 0
  }, $data.currentStatusIndex === 0 ? {
    p: common_vendor.o(($event) => $options.switchTab(0))
  } : {})), {
    q: $data.loading
  }, $data.loading ? {} : {}, {
    r: $data.noMore
  }, $data.noMore ? {} : {}, {
    s: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-976a0f82"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/coupon/index.js.map
