<template>
  <view class="consume-red-packet">
    <!-- 红包活动展示区 -->
    <view class="red-packet-banner" v-if="redPacketInfo && !isDrawing && !hasDrawn">
      <view class="banner-content">
        <view class="merchant-info">
          <image class="merchant-logo" :src="redPacketInfo.merchantLogo" mode="aspectFill"></image>
          <text class="merchant-name">{{redPacketInfo.merchantName}}</text>
        </view>
        <view class="activity-info">
          <text class="activity-title">{{redPacketInfo.title}}</text>
          <text class="activity-desc">{{redPacketInfo.description}}</text>
          <text class="consume-tip">到店消费满{{consumeThreshold}}元即可参与抽红包</text>
          <text class="time-limit" v-if="redPacketInfo.endTime">活动截止: {{formatDate(redPacketInfo.endTime)}}</text>
        </view>
        <view class="action-area">
          <button 
            class="draw-btn" 
            :class="{'disabled': !canDraw}"
            @click="handleDrawRedPacket">
            {{canDraw ? '立即抽红包' : '消费满'+consumeThreshold+'元可抽'}}
          </button>
          <text class="consume-amount" v-if="consumeAmount > 0">已消费: ¥{{consumeAmount}}</text>
        </view>
      </view>
    </view>
    
    <!-- 抽奖动画区 -->
    <view class="drawing-area" v-if="isDrawing">
      <view class="red-packet-animation">
        <image class="red-packet-img" src="/static/images/red-packet-open.gif" mode="aspectFit"></image>
        <text class="drawing-text">正在抽取红包...</text>
      </view>
    </view>
    
    <!-- 抽奖结果区 -->
    <view class="result-area" v-if="hasDrawn">
      <view class="result-content">
        <image class="result-icon" src="/static/images/red-packet-success.png" mode="aspectFit"></image>
        <text class="result-title">恭喜获得红包</text>
        <text class="result-amount">¥{{drawResult.amount}}</text>
        <text class="result-desc">{{drawResult.message || '红包已发放到您的账户'}}</text>
        <view class="result-buttons">
          <button class="use-now-btn" @click="useRedPacket">立即使用</button>
          <button class="close-btn" @click="closeResult">关闭</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { 
  CONSUME_RED_PACKET_CONFIG, 
  RED_PACKET_TYPE,
  checkConsumeRedPacketEligibility,
  getRandomRedPacketAmount,
  hasGrabbedRedPacket,
  saveRedPacketRecord
} from '@/utils/redPacket';
import { formatTime } from '@/utils/format';
import { getLocalUserInfo } from '@/utils/userProfile';

export default {
  props: {
    merchantId: {
      type: String,
      required: true
    },
    redPacketId: {
      type: String,
      required: true
    }
  },
  
  data() {
    return {
      redPacketInfo: null,
      consumeAmount: 0,
      consumeThreshold: CONSUME_RED_PACKET_CONFIG.THRESHOLD,
      canDraw: false,
      isDrawing: false,
      hasDrawn: false,
      drawResult: {
        amount: 0,
        message: ''
      },
      userInfo: null
    };
  },
  
  created() {
    // 获取用户信息
    this.userInfo = getLocalUserInfo();
    if (!this.userInfo || !this.userInfo.userId) {
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 加载红包活动信息和消费记录
    this.loadRedPacketInfo();
    this.loadConsumeRecord();
  },
  
  methods: {
    // 加载红包活动信息
    async loadRedPacketInfo() {
      try {
        // 模拟API调用，添加示例数据
        // 实际项目中应该使用真实API
        setTimeout(() => {
          this.redPacketInfo = {
            merchantId: this.merchantId,
            merchantName: '示例商家',
            merchantLogo: '/static/images/tabbar/商家入驻.png',
            title: '消费满额抽红包',
            description: '到店消费满100元，最高可得88元红包',
            endTime: new Date().getTime() + 30 * 24 * 60 * 60 * 1000, // 30天后过期
            minAmount: CONSUME_RED_PACKET_CONFIG.MIN_AMOUNT,
            maxAmount: CONSUME_RED_PACKET_CONFIG.MAX_AMOUNT
          };
          
          console.log('红包信息加载成功:', this.redPacketInfo);
        }, 500);
      } catch (error) {
        console.error('加载红包活动失败', error);
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      }
    },
    
    // 加载消费记录
    async loadConsumeRecord() {
      if (!this.userInfo || !this.userInfo.userId) {
        // 模拟用户信息
        this.userInfo = { userId: 'demo_user_001' };
      }
      
      try {
        // 模拟API调用，添加示例数据
        // 实际项目中应该使用真实API
        setTimeout(() => {
          // 随机消费金额，有50%的概率超过门槛
          const randomAmount = Math.random() > 0.5 ? 
            this.consumeThreshold + Math.floor(Math.random() * 200) : 
            Math.floor(Math.random() * this.consumeThreshold);
          
          this.consumeAmount = randomAmount;
          this.canDraw = this.consumeAmount >= this.consumeThreshold;
          
          console.log('消费记录加载成功:', this.consumeAmount, '是否可抽:', this.canDraw);
        }, 800);
      } catch (error) {
        console.error('加载消费记录失败', error);
      }
    },
    
    // 处理抽红包
    async handleDrawRedPacket() {
      if (!this.canDraw) {
        uni.showToast({
          title: `需要消费满${this.consumeThreshold}元才能参与`,
          icon: 'none'
        });
        return;
      }
      
      // 检查资格
      const isEligible = await checkConsumeRedPacketEligibility(this.userInfo.userId, this.merchantId);
      if (!isEligible) {
        uni.showToast({
          title: `消费记录验证失败，请联系商家`,
          icon: 'none'
        });
        return;
      }
      
      // 检查是否已抽过
      const hasGrabbed = await hasGrabbedRedPacket(this.userInfo.userId, this.redPacketId);
      if (hasGrabbed) {
        uni.showToast({
          title: '您已参与过此红包活动',
          icon: 'none'
        });
        return;
      }
      
      // 开始抽奖动画
      this.isDrawing = true;
      
      // 模拟抽奖过程
      setTimeout(() => {
        try {
          // 随机红包金额
          const min = CONSUME_RED_PACKET_CONFIG.MIN_AMOUNT;
          const max = CONSUME_RED_PACKET_CONFIG.MAX_AMOUNT;
          const amount = (Math.random() * (max - min) + min).toFixed(2);
          
          // 显示结果
          this.drawResult = {
            amount: amount,
            message: amount >= 10 ? '恭喜您，手气不错！' : '谢谢参与，下次再来！'
          };
          
          console.log('抽红包结果:', this.drawResult);
        } catch (error) {
          console.error('抽红包失败', error);
          this.drawResult = {
            amount: 0,
            message: '网络异常，请重试'
          };
        } finally {
          this.isDrawing = false;
          this.hasDrawn = true;
        }
      }, 2000);
    },
    
    // 使用红包
    useRedPacket() {
      this.$emit('use-red-packet', {
        amount: this.drawResult.amount,
        redPacketId: this.redPacketId,
        merchantId: this.merchantId
      });
      
      // 关闭结果页
      this.closeResult();
      
      // 跳转到商家详情页
      uni.navigateTo({
        url: `/pages/business/shop-detail?id=${this.merchantId}&showCoupon=true`
      });
    },
    
    // 关闭结果
    closeResult() {
      this.hasDrawn = false;
      this.$emit('close');
    },
    
    // 格式化日期
    formatDate(timestamp) {
      return formatTime(timestamp, 'YYYY-MM-DD');
    }
  }
};
</script>

<style lang="scss">
.consume-red-packet {
  width: 100%;
  
  .red-packet-banner {
    background: linear-gradient(135deg, #FF9B9B, #FF4D4F);
    border-radius: 16rpx;
    padding: 30rpx;
    color: #fff;
    box-shadow: 0 8rpx 16rpx rgba(255, 77, 79, 0.2);
    
    .merchant-info {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      .merchant-logo {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        border: 2rpx solid rgba(255, 255, 255, 0.6);
        margin-right: 16rpx;
      }
      
      .merchant-name {
        font-size: 32rpx;
        font-weight: bold;
      }
    }
    
    .activity-info {
      margin-bottom: 30rpx;
      
      .activity-title {
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 12rpx;
      }
      
      .activity-desc {
        font-size: 28rpx;
        opacity: 0.9;
        margin-bottom: 16rpx;
      }
      
      .consume-tip {
        font-size: 30rpx;
        font-weight: bold;
        color: #FFFF00;
        margin-bottom: 12rpx;
      }
      
      .time-limit {
        font-size: 24rpx;
        opacity: 0.8;
      }
    }
    
    .action-area {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .draw-btn {
        background-color: #FFFF00;
        color: #FF4D4F;
        font-size: 32rpx;
        font-weight: bold;
        padding: 20rpx 40rpx;
        border-radius: 40rpx;
        box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
        margin-bottom: 16rpx;
        
        &.disabled {
          background-color: rgba(255, 255, 255, 0.5);
          color: rgba(255, 255, 255, 0.8);
        }
      }
      
      .consume-amount {
        font-size: 26rpx;
        opacity: 0.9;
      }
    }
  }
  
  .drawing-area {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60rpx 0;
    
    .red-packet-animation {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .red-packet-img {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 30rpx;
      }
      
      .drawing-text {
        font-size: 32rpx;
        color: #FF4D4F;
        font-weight: bold;
      }
    }
  }
  
  .result-area {
    padding: 40rpx;
    
    .result-content {
      background: linear-gradient(135deg, #FFEBEB, #FFF9F9);
      border-radius: 16rpx;
      padding: 40rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      box-shadow: 0 8rpx 16rpx rgba(255, 77, 79, 0.1);
      
      .result-icon {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 20rpx;
      }
      
      .result-title {
        font-size: 32rpx;
        color: #FF4D4F;
        margin-bottom: 16rpx;
      }
      
      .result-amount {
        font-size: 60rpx;
        font-weight: bold;
        color: #FF4D4F;
        margin-bottom: 20rpx;
      }
      
      .result-desc {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 30rpx;
      }
      
      .result-buttons {
        display: flex;
        width: 100%;
        
        .use-now-btn {
          flex: 2;
          background: linear-gradient(135deg, #FF4D4F, #FF7875);
          color: #fff;
          font-size: 30rpx;
          padding: 16rpx 0;
          border-radius: 40rpx;
          margin-right: 20rpx;
        }
        
        .close-btn {
          flex: 1;
          background-color: #F5F5F5;
          color: #666;
          font-size: 30rpx;
          padding: 16rpx 0;
          border-radius: 40rpx;
        }
      }
    }
  }
}
</style> 