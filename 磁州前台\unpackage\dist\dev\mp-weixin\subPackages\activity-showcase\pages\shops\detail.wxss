/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-83d25058, html.data-v-83d25058, #app.data-v-83d25058, .index-container.data-v-83d25058 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.product-detail-container.data-v-83d25058 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-83d25058 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-83d25058 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, #FF3B69, #FF7A9E);
}
.custom-navbar .navbar-content.data-v-83d25058 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
}
.custom-navbar .navbar-title.data-v-83d25058 {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.custom-navbar .back-btn.data-v-83d25058, .custom-navbar .share-btn.data-v-83d25058 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-navbar .back-btn.data-v-83d25058 {
  position: absolute;
  left: 30rpx;
}
.custom-navbar .navbar-right.data-v-83d25058 {
  position: absolute;
  right: 30rpx;
}

/* 内容区域 */
.content-scroll.data-v-83d25058 {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 120rpx;
  /* 底部操作栏高度 */
}

/* 商品轮播图 */
.product-swiper.data-v-83d25058 {
  width: 100%;
  height: 750rpx;
}
.product-swiper .swiper-image.data-v-83d25058 {
  width: 100%;
  height: 100%;
}

/* 商品信息卡片 */
.product-info-card.data-v-83d25058 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.product-info-card .price-row.data-v-83d25058 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.product-info-card .price-row .price-box.data-v-83d25058 {
  display: flex;
  align-items: baseline;
}
.product-info-card .price-row .price-box .price-symbol.data-v-83d25058 {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF3B69;
}
.product-info-card .price-row .price-box .price-value.data-v-83d25058 {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF3B69;
  margin-right: 16rpx;
}
.product-info-card .price-row .price-box .original-price.data-v-83d25058 {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}
.product-info-card .price-row .sold-count.data-v-83d25058 {
  font-size: 24rpx;
  color: #999999;
}
.product-info-card .product-name.data-v-83d25058 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}
.product-info-card .tags-row.data-v-83d25058 {
  display: flex;
  flex-wrap: wrap;
}
.product-info-card .tags-row .tag.data-v-83d25058 {
  font-size: 24rpx;
  color: #FF3B69;
  background-color: rgba(255, 59, 105, 0.1);
  border-radius: 6rpx;
  padding: 4rpx 12rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
}

/* 商家信息卡片 */
.shop-info-card.data-v-83d25058 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.shop-info-card .shop-header.data-v-83d25058 {
  display: flex;
  align-items: center;
}
.shop-info-card .shop-header .shop-avatar.data-v-83d25058 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.shop-info-card .shop-header .shop-detail.data-v-83d25058 {
  flex: 1;
}
.shop-info-card .shop-header .shop-detail .shop-name.data-v-83d25058 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6rpx;
}
.shop-info-card .shop-header .shop-detail .shop-rating.data-v-83d25058 {
  display: flex;
  align-items: center;
}
.shop-info-card .shop-header .shop-detail .shop-rating .star.data-v-83d25058 {
  color: #CCCCCC;
  margin-right: 4rpx;
}
.shop-info-card .shop-header .shop-detail .shop-rating .star.active.data-v-83d25058 {
  color: #FFCC00;
}
.shop-info-card .shop-header .shop-detail .shop-rating .rating-value.data-v-83d25058 {
  font-size: 24rpx;
  color: #999999;
  margin-left: 6rpx;
}
.shop-info-card .shop-header .view-shop-btn.data-v-83d25058 {
  font-size: 28rpx;
  color: #666666;
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border: 1px solid #DDDDDD;
  border-radius: 30rpx;
}

/* 规格卡片 */
.specs-card.data-v-83d25058 {
  background-color: #FFFFFF;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}
.specs-card .specs-item.data-v-83d25058 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #F2F2F7;
}
.specs-card .specs-item.data-v-83d25058:last-child {
  border-bottom: none;
}
.specs-card .specs-item .specs-label.data-v-83d25058 {
  font-size: 28rpx;
  color: #999999;
}
.specs-card .specs-item .specs-value.data-v-83d25058 {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 28rpx;
  color: #333333;
}
.specs-card .specs-item .specs-value .service-tags.data-v-83d25058 {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  max-width: 400rpx;
}
.specs-card .specs-item .specs-value .service-tags .service-tag.data-v-83d25058 {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
  margin-bottom: 6rpx;
}
.specs-card .specs-item .specs-value .service-tags .service-tag svg.data-v-83d25058 {
  margin-right: 4rpx;
}
.specs-card .specs-item .specs-value .service-tags .service-tag text.data-v-83d25058 {
  font-size: 24rpx;
  color: #666666;
}
.specs-card .specs-item .specs-value svg.data-v-83d25058 {
  margin-left: 10rpx;
}

/* 商品详情卡片 */
.detail-card.data-v-83d25058 {
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
}
.detail-card .detail-header.data-v-83d25058 {
  padding: 30rpx;
  border-bottom: 1px solid #F2F2F7;
}
.detail-card .detail-header .detail-title.data-v-83d25058 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.detail-card .detail-content.data-v-83d25058 {
  padding: 30rpx;
}
.detail-card .detail-content .detail-images.data-v-83d25058 {
  margin-top: 20rpx;
}
.detail-card .detail-content .detail-images .detail-image.data-v-83d25058 {
  width: 100%;
  margin-bottom: 20rpx;
}
.detail-card .detail-content .detail-images .detail-image.data-v-83d25058:last-child {
  margin-bottom: 0;
}

/* 评价卡片 */
.reviews-card.data-v-83d25058 {
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
}
.reviews-card .reviews-header.data-v-83d25058 {
  padding: 30rpx;
  border-bottom: 1px solid #F2F2F7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.reviews-card .reviews-header .reviews-title.data-v-83d25058 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.reviews-card .reviews-header .view-all.data-v-83d25058 {
  font-size: 28rpx;
  color: #999999;
  display: flex;
  align-items: center;
}
.reviews-card .reviews-content.data-v-83d25058 {
  padding: 0 30rpx;
}
.reviews-card .reviews-content .review-item.data-v-83d25058 {
  padding: 30rpx 0;
  border-bottom: 1px solid #F2F2F7;
}
.reviews-card .reviews-content .review-item.data-v-83d25058:last-child {
  border-bottom: none;
}
.reviews-card .reviews-content .review-item .review-header.data-v-83d25058 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.reviews-card .reviews-content .review-item .review-header .reviewer-avatar.data-v-83d25058 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.reviews-card .reviews-content .review-item .review-header .reviewer-info.data-v-83d25058 {
  flex: 1;
}
.reviews-card .reviews-content .review-item .review-header .reviewer-info .reviewer-name.data-v-83d25058 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6rpx;
}
.reviews-card .reviews-content .review-item .review-header .reviewer-info .review-rating.data-v-83d25058 {
  display: flex;
  align-items: center;
}
.reviews-card .reviews-content .review-item .review-header .reviewer-info .review-rating .star.data-v-83d25058 {
  color: #CCCCCC;
  margin-right: 4rpx;
}
.reviews-card .reviews-content .review-item .review-header .reviewer-info .review-rating .star.active.data-v-83d25058 {
  color: #FFCC00;
}
.reviews-card .reviews-content .review-item .review-header .reviewer-info .review-rating .review-date.data-v-83d25058 {
  font-size: 24rpx;
  color: #999999;
  margin-left: 10rpx;
}
.reviews-card .reviews-content .review-item .review-content.data-v-83d25058 {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 16rpx;
}
.reviews-card .reviews-content .review-item .review-images.data-v-83d25058 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.reviews-card .reviews-content .review-item .review-images .review-image.data-v-83d25058 {
  width: 160rpx;
  height: 160rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}
.reviews-card .reviews-content .review-item .specs-info.data-v-83d25058 {
  font-size: 24rpx;
  color: #999999;
}

/* 推荐商品卡片 */
.recommend-card.data-v-83d25058 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.recommend-card .recommend-header.data-v-83d25058 {
  margin-bottom: 20rpx;
}
.recommend-card .recommend-header .recommend-title.data-v-83d25058 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.recommend-card .recommend-list.data-v-83d25058 {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
}
.recommend-card .recommend-list .recommend-item.data-v-83d25058 {
  width: 240rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.recommend-card .recommend-list .recommend-item.data-v-83d25058:last-child {
  margin-right: 0;
}
.recommend-card .recommend-list .recommend-item .recommend-image.data-v-83d25058 {
  width: 240rpx;
  height: 240rpx;
  border-radius: 16rpx;
  margin-bottom: 10rpx;
}
.recommend-card .recommend-list .recommend-item .recommend-info .recommend-name.data-v-83d25058 {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.recommend-card .recommend-list .recommend-item .recommend-info .recommend-price-row.data-v-83d25058 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.recommend-card .recommend-list .recommend-item .recommend-info .recommend-price-row .recommend-price.data-v-83d25058 {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF3B69;
}
.recommend-card .recommend-list .recommend-item .recommend-info .recommend-price-row .recommend-sold.data-v-83d25058 {
  font-size: 24rpx;
  color: #999999;
}

/* 底部操作栏 */
.footer-bar.data-v-83d25058 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.footer-bar .footer-left.data-v-83d25058 {
  display: flex;
}
.footer-bar .footer-left .action-btn.data-v-83d25058 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
}
.footer-bar .footer-left .action-btn .icon.data-v-83d25058 {
  color: #999999;
  margin-bottom: 6rpx;
}
.footer-bar .footer-left .action-btn .icon.active.data-v-83d25058 {
  color: #FF3B69;
}
.footer-bar .footer-left .action-btn text.data-v-83d25058 {
  font-size: 24rpx;
  color: #999999;
}
.footer-bar .footer-right.data-v-83d25058 {
  display: flex;
}
.footer-bar .footer-right .cart-btn.data-v-83d25058, .footer-bar .footer-right .buy-btn.data-v-83d25058 {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  padding: 0 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}
.footer-bar .footer-right .cart-btn.data-v-83d25058 {
  background-color: #FFF0F5;
  color: #FF3B69;
  margin-right: 20rpx;
}
.footer-bar .footer-right .buy-btn.data-v-83d25058 {
  background: linear-gradient(to right, #FF3B69, #FF7A9E);
  color: #FFFFFF;
}