#!/bin/bash

echo "🎨 启动磁州生活网后台管理系统前端"

# 检查Node.js版本
NODE_VERSION=$(node -v 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ Node.js未安装，请先安装Node.js 18+"
    echo "   下载地址: https://nodejs.org/"
    exit 1
fi

echo "📋 当前Node.js版本: $NODE_VERSION"

# 检查Node.js版本是否满足要求
NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
if [ "$NODE_MAJOR_VERSION" -lt 18 ]; then
    echo "❌ Node.js版本过低，需要18+版本"
    echo "   当前版本: $NODE_VERSION"
    echo "   请升级Node.js: https://nodejs.org/"
    exit 1
fi

# 检查npm版本
NPM_VERSION=$(npm -v 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ npm未安装"
    exit 1
fi

echo "📋 当前npm版本: $NPM_VERSION"

# 检查是否存在node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 首次运行，正在安装依赖..."
    echo "⏳ 这可能需要几分钟时间，请耐心等待..."
    
    # 设置npm镜像源（可选，提高下载速度）
    echo "🔧 配置npm镜像源..."
    npm config set registry https://registry.npmmirror.com/
    
    # 安装依赖
    npm install
    
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        echo "🔧 尝试清理缓存后重新安装..."
        npm cache clean --force
        rm -rf node_modules package-lock.json
        npm install
        
        if [ $? -ne 0 ]; then
            echo "❌ 依赖安装仍然失败，请检查网络连接或手动安装"
            exit 1
        fi
    fi
    
    echo "✅ 依赖安装完成"
else
    echo "📦 检查依赖更新..."
    npm outdated
fi

# 检查后端服务是否启动
echo "🔍 检查后端服务状态..."
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/actuator/health 2>/dev/null)

if [ "$BACKEND_STATUS" != "200" ]; then
    echo "⚠️  后端服务未启动或不可访问"
    echo "🔧 请确保后端服务已启动："
    echo "   1. cd ../backend"
    echo "   2. ./start-dev.sh"
    echo ""
    echo "📋 或者手动启动后端服务："
    echo "   - 网关服务: http://localhost:8080"
    echo "   - 认证服务: http://localhost:8081"
    echo ""
    read -p "是否继续启动前端服务？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 已取消启动"
        exit 1
    fi
else
    echo "✅ 后端服务运行正常"
fi

# 启动开发服务器
echo "🚀 启动前端开发服务器..."
echo "📱 前端地址: http://localhost:3000"
echo "🔑 默认账号: admin / admin123"
echo ""
echo "💡 提示："
echo "   - 按 Ctrl+C 停止服务"
echo "   - 修改代码会自动热重载"
echo "   - 如遇到问题，请检查控制台错误信息"
echo ""

# 启动开发服务器
npm run dev

echo "👋 前端服务已停止"
