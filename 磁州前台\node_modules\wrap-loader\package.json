{"name": "wrap-loader", "description": "Add custom content before and after the loaded source.", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://unindented.org/"}, "repository": {"type": "git", "url": "git://github.com/unindented/wrap-loader.git"}, "keywords": ["wrap", "webpack"], "scripts": {"test:lint": "eslint .", "test:unit": "nodeunit test/loader.js", "test": "run-s test:*"}, "dependencies": {"loader-utils": "^1.1.0"}, "devDependencies": {"eslint": "^3.19.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.3.0", "eslint-plugin-node": "^5.0.0", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "nodeunit": "^0.11.1", "npm-run-all": "^4.0.2"}}