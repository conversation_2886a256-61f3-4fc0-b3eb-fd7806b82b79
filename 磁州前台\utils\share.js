/**
 * 分享工具函数
 */

// 分享配置
const shareConfig = {
  // 分享标题
  getTitle(type, data) {
    const titles = {
      redPacket: `${data.sender.nickname}的红包`,
      post: data.title || '分享一个有趣的动态',
      promotion: data.title || '分享一个优惠活动'
    };
    return titles[type] || '分享';
  },
  
  // 分享描述
  getDesc(type, data) {
    const descs = {
      redPacket: '快来抢红包啦！',
      post: data.content || '快来看看吧',
      promotion: data.description || '优惠活动等你来'
    };
    return descs[type] || '';
  },
  
  // 分享图片
  getImage(type, data) {
    const images = {
      redPacket: '/static/images/share-red-packet.png',
      post: data.images?.[0] || '/static/images/share-post.png',
      promotion: data.image || '/static/images/share-promotion.png'
    };
    return images[type] || '/static/images/share-default.png';
  },
  
  // 分享链接
  getPath(type, data) {
    const paths = {
      redPacket: `/pages/red-packet/grab?id=${data.id}`,
      post: `/pages/post/detail?id=${data.id}`,
      promotion: `/pages/promotion/detail?id=${data.id}`
    };
    return paths[type] || '/pages/index/index';
  }
};

/**
 * 分享到微信好友
 * @param {Object} options 分享选项
 * @param {string} options.type 分享类型
 * @param {Object} options.data 分享数据
 * @returns {Promise}
 */
export function shareToWechat(options) {
  return new Promise((resolve, reject) => {
    uni.share({
      provider: 'weixin',
      scene: 'WXSceneSession',
      type: 0,
      title: shareConfig.getTitle(options.type, options.data),
      summary: shareConfig.getDesc(options.type, options.data),
      imageUrl: shareConfig.getImage(options.type, options.data),
      href: shareConfig.getPath(options.type, options.data),
      success: function (res) {
        resolve(res);
      },
      fail: function (err) {
        reject(err);
      }
    });
  });
}

/**
 * 分享到朋友圈
 * @param {Object} options 分享选项
 * @param {string} options.type 分享类型
 * @param {Object} options.data 分享数据
 * @returns {Promise}
 */
export function shareToTimeline(options) {
  return new Promise((resolve, reject) => {
    uni.share({
      provider: 'weixin',
      scene: 'WXSceneTimeline',
      type: 0,
      title: shareConfig.getTitle(options.type, options.data),
      summary: shareConfig.getDesc(options.type, options.data),
      imageUrl: shareConfig.getImage(options.type, options.data),
      href: shareConfig.getPath(options.type, options.data),
      success: function (res) {
        resolve(res);
      },
      fail: function (err) {
        reject(err);
      }
    });
  });
}

/**
 * 显示分享菜单
 * @param {Object} options 分享选项
 * @param {string} options.type 分享类型
 * @param {Object} options.data 分享数据
 * @returns {Promise}
 */
export function showShareMenu(options) {
  return new Promise((resolve, reject) => {
    uni.showActionSheet({
      itemList: ['分享到微信好友', '分享到朋友圈'],
      success: function (res) {
        if (res.tapIndex === 0) {
          shareToWechat(options).then(resolve).catch(reject);
        } else if (res.tapIndex === 1) {
          shareToTimeline(options).then(resolve).catch(reject);
        }
      },
      fail: function (err) {
        reject(err);
      }
    });
  });
}

export default {
  shareToWechat,
  shareToTimeline,
  showShareMenu
}; 