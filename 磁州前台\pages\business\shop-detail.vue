<template>
	<view class="shop-detail-page">
		<!-- 隐藏的Canvas用于绘制海报 -->
		<canvas canvas-id="posterCanvas" class="poster-canvas" style="width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;"></canvas>
		
		<!-- 状态栏占位和导航栏 -->
		<view class="blue-header-container">
			<view class="blue-header" :style="{'padding-top': isIOS ? (safeAreaInsetTop + 'px') : (statusBarHeight + 'px')}">
		<view class="navbar">
			<view class="navbar-left" @click="goBack">
				<view class="back-icon-wrap">
					<image src="/static/images/tabbar/返回.png" class="back-icon"></image>
				</view>
			</view>
					<view class="navbar-title">商家详情</view>
					<view class="navbar-right"></view>
				</view>
			</view>
		</view>
		
		<!-- 添加悬浮转发按钮组件 -->
		<FabButtons 
			pageName="shop-detail" 
			:pageInfo="{
				title: shopData.shopName + ' - 商家详情，点击查看更多信息',
				path: '/pages/business/shop-detail?id=' + shopData.id,
				imageUrl: shopData.images && shopData.images.length > 0 ? shopData.images[0] : ''
			}" 
		/>
		
		<!-- 添加悬浮推广按钮 -->
		<FloatPromotionButton @click="showMerchantPromotion" />
		
		<!-- 悬浮海报按钮 -->
		<view class="float-poster-btn" @click="generateShareImage">
			<image src="/static/images/tabbar/海报.png" class="poster-icon"></image>
			<text class="poster-text">海报</text>
		</view>
		
		<!-- 顶部图片区域 -->
		<view class="shop-gallery">
			<swiper class="gallery-swiper" circular :indicator-dots="true" :autoplay="true" :interval="4000" 
				indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#ffffff">
				<swiper-item v-for="(img, index) in shopData.images" :key="index">
					<image class="gallery-image" :src="img" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 商家基本信息 -->
		<view class="shop-info-card">
			<view class="shop-basic-info">
				<view class="shop-logo-container">
					<image :src="shopData.logo || '/static/images/tabbar/商家入驻.png'" class="shop-logo"></image>
				</view>
				<view class="shop-title-container">
					<text class="shop-name">{{shopData.shopName}}</text>
					<view class="shop-category">
						<text class="category-tag">{{shopData.category}}</text>
						<text class="shop-scale">{{shopData.scale}}</text>
					</view>
					<view class="shop-stats">
						<text class="stat-item">浏览 {{shopData.viewCount}}</text>
						<text class="stat-divider">|</text>
						<text class="stat-item">收藏 {{shopData.favoriteCount}}</text>
					</view>
				</view>
			</view>
			
			<view class="action-buttons">
				<button class="action-btn follow-btn" :class="{'following': isFollowing}" @click="toggleFollow">
					<view class="btn-content">
					<image :src="isFollowing ? '/static/images/tabbar/已关注.png' : '/static/images/tabbar/关注.png'" class="btn-icon"></image>
						<text class="btn-text">{{isFollowing ? '已关注' : '关注'}}</text>
					</view>
				</button>
				<button class="action-btn contact-btn" @click="contactShop">
					<view class="btn-content">
						<image src="/static/images/tabbar/电话.png" class="btn-icon phone-icon"></image>
						<text class="btn-text">联系商家</text>
					</view>
				</button>
				<!-- 添加推广按钮 -->
				<PromotionToolButton 
					v-if="hasPromotionPermission" 
					@click="showMerchantPromotion" 
					buttonText="推广" 
				/>
			</view>
		</view>
		
		<!-- 商家信息卡片 -->
		<view class="detail-section">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/tabbar/商家信息.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">商家信息</view>
				</view>
			</view>
			
			<view class="info-list">
				<view class="info-item location-item" @click="openLocation">
					<view class="info-icon-wrap">
						<image src="/static/images/tabbar/商家地址.png" class="info-icon"></image>
					</view>
					<view class="info-content">
						<text class="info-label">商家地址</text>
						<text class="info-value">{{shopData.address}}</text>
					</view>
					<view class="navigate-btn">
						<text>导航</text>
					</view>
				</view>
				
				<view class="info-item" @click="makePhoneCall">
					<view class="info-icon-wrap">
						<image src="/static/images/tabbar/电话.png" class="info-icon"></image>
					</view>
					<view class="info-content">
						<text class="info-label">联系电话</text>
						<text class="info-value">{{shopData.contactPhone}}</text>
						<text class="info-tip">打电话时请说在磁州生活网看到的</text>
					</view>
					<view class="action-icon-wrap">
						<image src="/static/images/tabbar/箭头.png" class="action-icon"></image>
					</view>
				</view>
				
				<view class="info-item">
					<view class="info-icon-wrap">
						<image src="/static/images/tabbar/营业时间.png" class="info-icon"></image>
					</view>
					<view class="info-content">
						<text class="info-label">营业时间</text>
						<text class="info-value">{{shopData.businessTime}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 举报提示卡片 -->
		<view class="report-tip-section" @click="showReportOptions">
			<view class="report-tip-content">
				<image src="/static/images/tabbar/举报.png" class="report-tip-icon"></image>
				<text class="report-tip-text">如遇无效、虚假、诈骗信息，请立即举报</text>
				<image src="/static/images/tabbar/箭头.png" class="report-arrow-icon"></image>
			</view>
		</view>
		
		<!-- 商家介绍卡片 -->
		<view class="detail-section">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/tabbar/商家介绍.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">商家介绍</view>
				</view>
			</view>
			<view class="description-content">
				<text class="description-text">{{shopData.description}}</text>
			</view>
		</view>
		
		<!-- 消费满额红包卡片 -->
		<view class="detail-section" v-if="shopData.hasConsumeRedPacket">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/red-packet-icon.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">消费红包</view>
				</view>
				<view class="header-right" v-if="shopData.hasConsumeRedPacket">
					<text class="more-text">活动中</text>
				</view>
			</view>
			<view class="consume-red-packet-container">
				<view class="consume-activity-card">
					<view class="activity-header">
						<image class="merchant-logo" :src="shopData.merchantLogo || shopData.logo" mode="aspectFill"></image>
						<view class="activity-info">
							<text class="activity-title">消费满额抽红包</text>
							<text class="activity-desc">到店消费满100元，最高可得88元红包</text>
						</view>
					</view>
					<view class="activity-details">
						<text class="detail-item">活动时间：{{formatDate(new Date().getTime() + 30*24*60*60*1000)}}</text>
						<view class="button-area">
							<button class="draw-button" @click="handleUseRedPacket">立即抽红包</button>
							<text class="detail-text">已有{{Math.floor(Math.random()*200+50)}}人参与</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 商家相册 -->
		<view class="detail-section">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/tabbar/商家相册.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">商家相册</view>
				</view>
				<view class="view-all" @click="viewAllPhotos">
					<text>查看全部</text>
					<image src="/static/images/tabbar/箭头.png" class="arrow-icon"></image>
				</view>
			</view>
			<view class="photos-grid">
				<view class="photo-item" v-for="(photo, index) in shopData.albumImages.slice(0, 6)" :key="index" @click="previewImage(index)">
					<image :src="photo" class="photo-image" mode="aspectFill"></image>
				</view>
			</view>
		</view>
		
		<!-- 客服微信二维码 -->
		<view class="detail-section">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/tabbar/客服微信.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">客服微信</view>
				</view>
			</view>
			<view class="qrcode-container">
				<image :src="shopData.qrcode" class="qrcode-image" @click="previewQRCode"></image>
				<text class="qrcode-tip">点击保存图片，微信扫码添加</text>
			</view>
		</view>
		
		<!-- 抽奖动画弹窗 -->
		<SimplePopup ref="redPacketPopup" type="center">
			<view class="red-packet-popup">
				<view class="drawing-area" v-if="isDrawing">
					<view class="red-packet-animation">
						<image class="red-packet-img" src="/static/images/red-packet-icon.png" mode="aspectFit"></image>
						<text class="drawing-text">正在抽取红包...</text>
					</view>
				</view>
				
				<view class="result-area" v-if="hasDrawn">
					<view class="result-content">
						<image class="result-icon" src="/static/images/red-packet-icon.png" mode="aspectFit"></image>
						<text class="result-title">恭喜获得红包</text>
						<text class="result-amount">¥{{drawResult.amount}}</text>
						<text class="result-desc">{{drawResult.message || '红包已发放到您的账户'}}</text>
						<view class="result-buttons">
							<button class="use-now-btn" @click="useDrawnRedPacket">立即使用</button>
							<button class="close-btn" @click="closeRedPacketPopup">关闭</button>
						</view>
					</view>
				</view>
			</view>
		</SimplePopup>
		
		<!-- 底部功能区 -->
		<view class="bottom-toolbar">
			<view class="toolbar-btn" @click="backToHome">
				<image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
					<text class="toolbar-text">首页</text>
				</view>
			<view class="toolbar-btn" @click="shareShop">
				<image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
				<text class="toolbar-text">分享</text>
				</view>
			<view class="toolbar-btn message-btn" @click="contactShop">
				<image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
				<text class="toolbar-text">私信</text>
			</view>
			<view class="toolbar-call-btn" @click="callShop">
				<text class="call-text">打电话</text>
				<text class="call-subtext">请说在磁州生活网看到的</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onLoad, onShareAppMessage, onReady, onMounted } from '@dcloudio/uni-app';

// 导入FabButtons组件
import FabButtons from '@/components/FabButtons.vue';
// 导入自定义弹窗组件
import SimplePopup from '@/components/SimplePopup.vue';
// 导入推广按钮组件
import PromotionToolButton from '@/components/PromotionToolButton.vue';
// 导入商家推广混入
import merchantPromotionMixin from '@/mixins/merchantPromotionMixin';

// 页面数据
const shopId = ref(null);
const isFollowing = ref(false);
const isFavorite = ref(false);
const statusBarHeight = ref(20);
const navbarHeight = ref(44);
const safeAreaInsetTop = ref(0);
const isIOS = ref(false);
const showPoster = ref(false); // 是否显示海报
const posterPath = ref(''); // 海报图片路径

// 弹窗ref
const redPacketPopup = ref(null);

// 商家列表数据
const shopList = ref([
	{
		id: "1",
		shopName: "五分利电器",
		category: "数码电器",
		scale: "10-20人",
		address: "河北省邯郸市磁县祥和路",
		contactPhone: "188-8888-8888",
		businessTime: "09:00-21:00",
		description: '五分利电器是本地知名的电器销售与维修服务商。我们提供各类家用电器，包括冰箱、洗衣机、空调、电视等。全场特价，送货上门，并提供专业安装和维修服务。我们的技术人员经验丰富，能够高效解决各类电器故障问题。\n\n本店支持分期付款，以旧换新，并提供长期的售后保障。欢迎新老顾客前来选购！',
		viewCount: 2345,
		favoriteCount: 128,
		logo: "/static/images/tabbar/商家入驻.png",
		qrcode: "/static/images/tabbar/二维码示例.png",
		supportMessage: true,
		images: [
			"/static/images/banner/banner-1.png",
			"/static/images/banner/banner-2.png",
			"/static/images/banner/banner-3.jpg"
		],
		albumImages: [
			"/static/images/banner/banner-1.png",
			"/static/images/banner/banner-2.png",
			"/static/images/banner/banner-3.jpg",
			"/static/images/banner/banner-1.png",
			"/static/images/banner/banner-2.png",
			"/static/images/banner/banner-3.jpg",
		]
	},
	// ... 其他商家数据
]);

// 当前商家数据
const shopData = ref({});

// 抽奖相关
const isDrawing = ref(false);
const hasDrawn = ref(false);
const drawResult = ref({
	amount: 0,
	message: ''
});

// 使用商家推广混入
const promotionMixin = merchantPromotionMixin;
const hasPromotionPermission = ref(false);
const promotionData = ref({});

// 页面加载
onLoad((options) => {
	// 获取商家ID
	if (options.id) {
		shopId.value = options.id;
		// 根据ID查找商家数据
		const shop = shopList.value.find(item => item.id === options.id);
	if (shop) {
		shopData.value = shop;
			// 为演示目的，添加商家推广相关字段
			shopData.value.canDistribute = true; // 允许推广
			shopData.value.ownerId = '123456'; // 假设当前用户ID为123456
		}
	}
	
	// 获取系统信息
	const systemInfo = uni.getSystemInfoSync();
	statusBarHeight.value = systemInfo.statusBarHeight;
	isIOS.value = systemInfo.platform === 'ios';
	safeAreaInsetTop.value = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.top : 0;
	
	// 初始化推广功能
	initPromotion();
});

// 初始化推广功能
const initPromotion = () => {
	// 检查推广权限
	hasPromotionPermission.value = promotionMixin.methods.isContentOwner.call({
		$store: {
			state: {
				user: {
					userId: '123456' // 假设当前用户ID
				}
			}
		},
		shopData: shopData.value
	}) || promotionMixin.methods.isCommissionContent.call({
		shopData: shopData.value
	});
	
	// 生成推广数据
	if (hasPromotionPermission.value) {
		promotionData.value = promotionMixin.methods.generatePromotionData.call({
			shopData: shopData.value,
			promotionData: {}
		}).promotionData;
	}
};

// 显示商家推广
const showMerchantPromotion = () => {
	promotionMixin.methods.showMerchantPromotion.call({
		hasPromotionPermission: hasPromotionPermission.value,
		openPromotionTools: () => {
			uni.navigateTo({
				url: '/subPackages/promotion/pages/promotion-tool?type=merchant&id=' + shopData.value.id
						});
					}
				});
};

// 页面准备完成
onReady(() => {
	// 页面准备完成后的逻辑
});

// 分享功能
onShareAppMessage(() => {
	return {
		title: shopData.value.shopName + ' - 磁州生活网推荐商家',
		path: '/pages/business/shop-detail?id=' + shopData.value.id,
		imageUrl: shopData.value.images && shopData.value.images.length > 0 ? shopData.value.images[0] : ''
	};
});

// 返回上一页
const goBack = () => {
	uni.navigateBack();
};

// 查看全部照片
const viewAllPhotos = () => {
	uni.previewImage({
		urls: shopData.value.albumImages,
		current: 0
	});
};

// 预览图片
const previewImage = (index) => {
				uni.previewImage({
		urls: shopData.value.albumImages,
		current: index
	});
};

// 预览二维码
const previewQRCode = () => {
	uni.previewImage({
		urls: [shopData.value.qrcode],
		current: 0
	});
};

// 打开地图导航
const openLocation = () => {
	// 这里应该使用真实的经纬度，为了演示使用固定值
	uni.openLocation({
		latitude: 36.3427,
		longitude: 114.7582,
		name: shopData.value.shopName,
		address: shopData.value.address,
		scale: 18
	});
};

// 拨打电话
const makePhoneCall = () => {
	uni.makePhoneCall({
		phoneNumber: shopData.value.contactPhone
	});
};

// 切换关注状态
const toggleFollow = () => {
	isFollowing.value = !isFollowing.value;
	uni.showToast({
		title: isFollowing.value ? '已关注' : '已取消关注',
		icon: 'none'
	});
};

// 联系商家
const contactShop = () => {
	if (!shopData.value.supportMessage) {
		makePhoneCall();
		return;
	}
	
	uni.navigateTo({
		url: '/pages/message/chat?id=' + shopData.value.id + '&name=' + encodeURIComponent(shopData.value.shopName)
	});
};

// 拨打电话
const callShop = () => {
	makePhoneCall();
};

// 返回首页
const backToHome = () => {
	uni.switchTab({
		url: '/pages/index/index'
	});
};

// 分享商家
const shareShop = () => {
	uni.showShareMenu({
		withShareTicket: true,
		menus: ['shareAppMessage', 'shareTimeline']
	});
};

// 生成分享图片
const generateShareImage = () => {
	uni.showLoading({
		title: '生成中...'
	});
	
	// 这里应该是真实的海报生成逻辑
	// 为了演示，使用setTimeout模拟
	setTimeout(() => {
		uni.hideLoading();
		uni.showToast({
			title: '海报生成成功',
			icon: 'success'
		});
		
		// 模拟保存图片
		uni.saveImageToPhotosAlbum({
			filePath: shopData.value.images[0],
			success: () => {
				uni.showToast({
					title: '已保存到相册',
					icon: 'success'
				});
			},
			fail: () => {
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				});
			}
		});
	}, 1500);
};

// 显示举报选项
const showReportOptions = () => {
	uni.showActionSheet({
		itemList: ['虚假信息', '诈骗信息', '违法信息', '侵权信息', '其他问题'],
		success: (res) => {
			const selectedType = ['虚假信息', '诈骗信息', '违法信息', '侵权信息', '其他问题'][res.tapIndex];
			
			// 检查登录状态
			const userInfo = uni.getStorageSync('userInfo');
			if (!userInfo) {
				uni.showModal({
					title: '提示',
					content: '举报需要先登录，是否前往登录？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login/login?redirect=' + encodeURIComponent('/pages/business/shop-detail?id=' + shopData.value.id)
							});
						}
					}
				});
				return;
			}
			
			if (selectedType === '其他问题') {
				showReportInputDialog();
			} else {
				submitReport(selectedType);
			}
		}
	});
};

const showReportInputDialog = () => {
	uni.showModal({
		title: '请描述问题',
		placeholderText: '请详细描述您遇到的问题',
		editable: true,
		success: (res) => {
			if (res.confirm && res.content) {
				submitReport('其他问题: ' + res.content);
			}
		}
	});
};

const submitReport = (reportReason) => {
	uni.showLoading({
		title: '提交中...'
	});
	
	setTimeout(() => {
		uni.hideLoading();
		uni.showToast({
			title: '举报成功',
			icon: 'success'
		});
	}, 1000);
};

const handleUseRedPacket = () => {
	const userInfo = uni.getStorageSync('userInfo');
	if (!userInfo) {
		uni.showToast({
			title: '请先登录',
			icon: 'none'
		});
		
		setTimeout(() => {
			uni.navigateTo({
				url: '/pages/login/login?redirect=' + encodeURIComponent('/pages/business/shop-detail?id=' + shopData.value.id)
			});
		}, 1500);
		return;
	}
	
	isDrawing.value = true;
	hasDrawn.value = false;
	redPacketPopup.value.open();
	
	setTimeout(() => {
		try {
			const amount = (Math.random() * 87 + 1).toFixed(2);
			
			drawResult.value = {
				amount: amount,
				message: amount >= 20 ? '恭喜您，手气不错！' : '谢谢参与，下次再来！'
			};
		} catch (error) {
			console.error('抽红包失败', error);
			drawResult.value = {
				amount: 0,
				message: '网络异常，请重试'
			};
		} finally {
			isDrawing.value = false;
			hasDrawn.value = true;
		}
	}, 2000);
};

const useDrawnRedPacket = () => {
	uni.showToast({
		title: `红包已添加到您的账户`,
		icon: 'success'
	});
	closeRedPacketPopup();
};

const closeRedPacketPopup = () => {
	redPacketPopup.value.close();
	setTimeout(() => {
		isDrawing.value = false;
		hasDrawn.value = false;
	}, 300);
};

const formatDate = (timestamp) => {
	const date = new Date(timestamp);
	const year = date.getFullYear();
	const month = (date.getMonth() + 1).toString().padStart(2, '0');
	const day = date.getDate().toString().padStart(2, '0');
	return `${year}-${month}-${day}`;
};
</script>

<style lang="scss">
/* 覆盖整个顶部区域的蓝色容器 */
.blue-header-container {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	width: 100%;
	z-index: 100;
	pointer-events: auto;
}

/* 蓝色头部区域 */
.blue-header {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	z-index: 10;
	background-color: #3846cd;
	width: 100%;
	/* 这里的padding-top会被动态设置 */
	pointer-events: auto;
}

.safe-area-inset-top {
	display: none; /* 不再需要这个元素 */
}

/* 页面整体样式 */
.shop-detail-page {
	min-height: 100vh;
	width: 100vw;
	background-color: #f7f8fc;
	position: relative;
	padding-bottom: 120rpx;
	overflow-x: hidden;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 系统标题栏 */
.navbar {
	height: 90rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx 0 20rpx;
	width: 100%;
	box-sizing: border-box;
}

.navbar-left, .navbar-right {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon-wrap {
	width: 72rpx;
	height: 72rpx;
	border-radius: 50%;
	background-color: transparent;
	backdrop-filter: none;
	-webkit-backdrop-filter: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.back-icon-wrap:active {
	transform: scale(0.92);
	background-color: transparent;
}

.back-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 1;
	filter: brightness(0) invert(1);
}

.navbar-title {
	flex: 1;
	text-align: center;
	font-size: 34rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 顶部图片轮播 */
.shop-gallery {
	position: relative;
	width: 100%;
	height: 520rpx;
	margin-top: calc(var(--status-bar-height, 44px) + 90rpx);
	border-radius: 0 0 40rpx 40rpx;
	overflow: hidden;
	box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.08);
}

.gallery-swiper {
	width: 100%;
	height: 100%;
}

.gallery-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* 商家基本信息卡片 */
.shop-info-card {
	margin: -70rpx 30rpx 30rpx;
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 32rpx;
	padding: 36rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);
	position: relative;
	z-index: 2;
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.shop-basic-info {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.shop-logo-container {
	width: 130rpx;
	height: 130rpx;
	border-radius: 24rpx;
	background-color: #f5f7fa;
	overflow: hidden;
	margin-right: 24rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
	border: 2rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
}

.shop-logo-container::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 24rpx;
	box-shadow: inset 0 0 0 1rpx rgba(255, 255, 255, 0.4);
	pointer-events: none;
}

.shop-logo {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.shop-title-container {
	flex: 1;
}

.shop-name {
	font-size: 40rpx;
	font-weight: 600;
	color: #222;
	margin-bottom: 16rpx;
	display: block;
	letter-spacing: -0.5rpx;
}

.shop-category {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.category-tag {
	font-size: 24rpx;
	color: #2738C0;
	background-color: rgba(39, 56, 192, 0.08);
	padding: 6rpx 20rpx;
	border-radius: 20rpx;
	margin-right: 16rpx;
	font-weight: 500;
}

.shop-scale {
	font-size: 24rpx;
	color: #666;
	background-color: #f0f2f5;
	padding: 6rpx 20rpx;
	border-radius: 20rpx;
}

.shop-stats {
	display: flex;
	align-items: center;
}

.stat-item {
	font-size: 24rpx;
	color: #888;
}

.stat-divider {
	margin: 0 16rpx;
	color: #ddd;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	justify-content: space-between;
	margin-top: 36rpx;
}

.action-btn {
	flex: 1;
	height: 70rpx;
	line-height: 70rpx;
	border-radius: 35rpx;
	text-align: center;
	margin: 0 10rpx;
	font-size: 28rpx;
	color: #FFFFFF;
	background: linear-gradient(to right, #007AFF, #5AC8FA);
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.follow-btn {
	background: linear-gradient(to right, #007AFF, #5AC8FA);
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.following {
	background: linear-gradient(to right, #8E8E93, #C7C7CC);
	box-shadow: 0 4rpx 12rpx rgba(142, 142, 147, 0.2);
}

.contact-btn {
	background: linear-gradient(to right, #007AFF, #5AC8FA);
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.btn-content {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
	position: relative;
}

.btn-icon {
	width: 28rpx;
	height: 28rpx;
	margin-right: 8rpx;
	flex-shrink: 0;
}

.phone-icon {
	filter: brightness(0) invert(1);
}

.btn-text {
	color: inherit;
	font-family: 'AlimamaShuHeiTi', sans-serif;
	font-size: 28rpx;
	line-height: 28rpx;
	padding-top: 2rpx;
}

/* 详情卡片通用样式 */
.detail-section {
	margin: 30rpx 30rpx 0;
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 32rpx;
	padding: 36rpx;
	box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.06);
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	transition: all 0.3s ease;
}

.section-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.header-left {
	display: flex;
	align-items: center;
}

.section-icon-wrap {
	width: 56rpx;
	height: 56rpx;
	border-radius: 16rpx;
	background-color: rgba(39, 56, 192, 0.08);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
}

.section-icon {
	width: 32rpx;
	height: 32rpx;
	display: block;
}

.section-title {
	display: none !important;
}

.section-title-text {
	font-size: 34rpx;
	font-weight: 600;
	color: #222;
	letter-spacing: -0.5rpx;
	line-height: 1.2;
	padding: 0 4rpx;
	display: inline-block;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.view-all {
	display: flex;
	align-items: center;
	font-size: 26rpx;
	color: #2738C0;
	background-color: rgba(39, 56, 192, 0.08);
	padding: 8rpx 20rpx;
	border-radius: 28rpx;
}

.arrow-icon {
	width: 24rpx;
	height: 24rpx;
	margin-left: 8rpx;
}

/* 信息列表 */
.info-list {
	background-color: rgba(249, 250, 252, 0.7);
	border-radius: 28rpx;
	overflow: hidden;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.info-item {
	display: flex;
	align-items: center;
	padding: 28rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.info-item:last-child {
	border-bottom: none;
}

.info-icon-wrap {
	width: 68rpx;
	height: 68rpx;
	border-radius: 20rpx;
	background-color: rgba(39, 56, 192, 0.08);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.info-icon {
	width: 36rpx;
	height: 36rpx;
}

.info-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.info-label {
	font-size: 26rpx;
	color: #888;
	margin-bottom: 8rpx;
}

.info-value {
	font-size: 30rpx;
	color: #222;
	font-weight: 500;
}

.navigate-btn {
	padding: 6rpx 24rpx;
	background-color: rgba(39, 56, 192, 0.08);
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #2738C0;
	margin-left: 20rpx;
	font-weight: 500;
}

.action-icon-wrap {
	margin-left: 20rpx;
}

.action-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 0.3;
}

.location-item {
	align-items: flex-start;
}

/* 商家介绍 */
.description-content {
	background-color: rgba(249, 250, 252, 0.7);
	border-radius: 28rpx;
	padding: 28rpx;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.description-text {
	font-size: 30rpx;
	color: #444;
	line-height: 1.7;
	white-space: pre-wrap;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 商家相册 */
.photos-grid {
	display: flex;
	flex-wrap: wrap;
	margin: 0 -8rpx;
}

.photo-item {
	width: calc(33.33% - 16rpx);
	aspect-ratio: 1;
	margin: 8rpx;
	border-radius: 24rpx;
	overflow: hidden;
	background-color: #f5f7fa;
	box-shadow: 0 8rpx 15rpx rgba(0, 0, 0, 0.05);
	position: relative;
	transform: translateZ(0);
	transition: transform 0.2s ease;
}

.photo-item:active {
	transform: scale(0.97);
}

.photo-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* 二维码 */
.qrcode-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 0;
}

.qrcode-image {
	width: 320rpx;
	height: 320rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1);
	border-radius: 28rpx;
	background-color: white;
	padding: 20rpx;
}

.qrcode-tip {
	font-size: 26rpx;
	color: #888;
	background-color: rgba(39, 56, 192, 0.08);
	padding: 12rpx 30rpx;
	border-radius: 30rpx;
}

/* 底部工具栏 */
.bottom-toolbar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	height: 110rpx;
	background-color: rgba(255, 255, 255, 0.95);
	box-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.05);
	display: flex;
	align-items: center;
	padding: 0;
	z-index: 100;
	border-top: 1rpx solid rgba(0, 0, 0, 0.03);
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
}

.toolbar-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	flex: 1;
	position: relative;
	transition: opacity 0.2s ease;
}

.toolbar-btn:active {
	opacity: 0.7;
}

.toolbar-call-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 85%;
	flex: 2.5;
	background-color: #2738C0;
	border-radius: 36rpx;
	margin: 12rpx 15rpx;
	box-shadow: 0 6rpx 15rpx rgba(39, 56, 192, 0.2);
	transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.toolbar-call-btn:active {
	transform: scale(0.97);
	box-shadow: 0 4rpx 10rpx rgba(39, 56, 192, 0.2);
}

.call-text {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 600;
}

.call-subtext {
	font-size: 18rpx;
	color: rgba(255, 255, 255, 0.85);
	margin-top: 2rpx;
	white-space: nowrap;
}

.toolbar-icon {
	width: 44rpx;
	height: 44rpx;
	margin-bottom: 6rpx;
}

.toolbar-text {
	font-size: 22rpx;
	color: #444;
}

/* 添加联系电话下方的提示样式 */
.info-tip {
	font-size: 22rpx;
	color: #3846cd;
	margin-top: 6rpx;
}

/* 悬浮海报按钮 */
.float-poster-btn {
	position: fixed;
	right: 30rpx;
	bottom: 200rpx;
	width: 100rpx;
	height: 100rpx;
	background: rgba(240, 240, 240, 0.9);
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
	z-index: 90;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1rpx solid rgba(230, 230, 230, 0.6);
	transition: all 0.2s ease;
}

.float-poster-btn:active {
	transform: scale(0.95);
	box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}

.poster-icon {
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 4rpx;
}

.poster-text {
	font-size: 20rpx;
	color: #444;
	line-height: 1;
}

/* 添加举报图标样式 */
.report-icon-wrap {
	width: 72rpx;
	height: 72rpx;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.report-icon-wrap:active {
	transform: scale(0.92);
	background-color: rgba(255, 255, 255, 0.25);
}

.report-icon {
	width: 36rpx;
	height: 36rpx;
	opacity: 1;
	filter: brightness(0) invert(1);
}

/* 举报提示卡片样式 */
.report-tip-section {
	margin: 30rpx 30rpx 0;
	background-color: #f9fafe;
	border-radius: 32rpx;
	padding: 20rpx 24rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.report-tip-content {
	display: flex;
	align-items: center;
}

.report-tip-icon {
	width: 28rpx;
	height: 28rpx;
	margin-right: 12rpx;
}

.report-tip-text {
	flex: 1;
	font-size: 24rpx;
	color: #666;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.report-arrow-icon {
	width: 24rpx;
	height: 24rpx;
	opacity: 0.3;
}

/* 确保所有文本在真机上正确显示的全局设置 */
text, view {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 消费红包相关样式 */
.consume-red-packet-container {
	padding: 20rpx;
}

.consume-activity-card {
	background: linear-gradient(135deg, #fff5f5, #fff1f0);
	border-radius: 28rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(255, 69, 58, 0.1);
	border: 1px solid rgba(255, 69, 58, 0.15);
}

.activity-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.merchant-logo {
	width: 80rpx;
	height: 80rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
	border: 1px solid rgba(255, 255, 255, 0.6);
}

.activity-info {
	flex: 1;
}

.activity-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff4538;
	margin-bottom: 10rpx;
	display: block;
}

.activity-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.activity-details {
	margin-top: 20rpx;
}

.detail-item {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 20rpx;
	display: block;
}

.button-area {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20rpx;
}

.draw-button {
	background: linear-gradient(135deg, #ff4538, #ff7b6c);
	color: #fff;
	font-size: 28rpx;
	padding: 10rpx 40rpx;
	border-radius: 30rpx;
	border: none;
	box-shadow: 0 4rpx 8rpx rgba(255, 69, 58, 0.3);
}

.detail-text {
	font-size: 24rpx;
	color: #999;
}

.more-text {
	font-size: 24rpx;
	color: #ff4538;
	padding: 4rpx 12rpx;
	background: rgba(255, 69, 58, 0.1);
	border-radius: 20rpx;
}

/* 红包弹窗样式 */
.red-packet-popup {
	background-color: transparent;
	width: 600rpx;
}

.drawing-area {
	background: rgba(0, 0, 0, 0.6);
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.red-packet-animation {
	text-align: center;
}

.red-packet-img {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.drawing-text {
	font-size: 32rpx;
	color: #fff;
	margin-top: 20rpx;
}

.result-area {
	background: linear-gradient(135deg, #FA2A2D, #FF4E4E);
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 16rpx rgba(255, 0, 0, 0.2);
}

.result-content {
	text-align: center;
}

.result-icon {
	width: 160rpx;
	height: 160rpx;
	margin-bottom: 20rpx;
}

.result-title {
	font-size: 36rpx;
	color: #FFE4B5;
	margin-bottom: 20rpx;
	display: block;
}

.result-amount {
	font-size: 80rpx;
	font-weight: bold;
	color: #FFFFFF;
	margin: 20rpx 0;
	display: block;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.result-desc {
	font-size: 28rpx;
	color: #FFE4B5;
	margin-bottom: 40rpx;
	display: block;
}

.result-buttons {
	display: flex;
	justify-content: space-between;
	margin-top: 30rpx;
}

.use-now-btn, .close-btn {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	margin: 0 10rpx;
}

.use-now-btn {
	background-color: #FFFFFF;
	color: #FA2A2D;
	border: none;
}

.close-btn {
	background-color: rgba(255, 255, 255, 0.2);
	color: #FFFFFF;
	border: 1rpx solid rgba(255, 255, 255, 0.5);
}
</style>