/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-a2ece00a, html.data-v-a2ece00a, #app.data-v-a2ece00a, .index-container.data-v-a2ece00a {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.cart-container.data-v-a2ece00a {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-a2ece00a {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-a2ece00a {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(255, 59, 105, 0.15);
}
.custom-navbar .navbar-content.data-v-a2ece00a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
}
.custom-navbar .navbar-left.data-v-a2ece00a, .custom-navbar .navbar-right.data-v-a2ece00a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
}
.custom-navbar .navbar-title.data-v-a2ece00a {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
}
.custom-navbar .icon.data-v-a2ece00a {
  width: 48rpx;
  height: 48rpx;
}
.custom-navbar .edit-btn.data-v-a2ece00a {
  font-size: 28rpx;
  color: #FFFFFF;
}

/* 内容区域 */
.content-scroll.data-v-a2ece00a {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 100rpx;
  /* 底部结算栏高度 */
}

/* 商家分组 */
.shop-group.data-v-a2ece00a {
  margin-bottom: 20rpx;
  background-color: #FFFFFF;
}

/* 商家信息 */
.shop-header.data-v-a2ece00a {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #F2F2F7;
}
.shop-header .checkbox-wrapper.data-v-a2ece00a {
  padding: 10rpx;
}
.shop-header .checkbox.data-v-a2ece00a {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
}
.shop-header .checkbox.checked.data-v-a2ece00a {
  background-color: #FF3B69;
  border-color: #FF3B69;
}
.shop-header .checkbox .check-icon.data-v-a2ece00a {
  color: #FFFFFF;
}
.shop-header .shop-info.data-v-a2ece00a {
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: 10rpx;
}
.shop-header .shop-info .shop-logo.data-v-a2ece00a {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
}
.shop-header .shop-info .shop-name.data-v-a2ece00a {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  margin-left: 10rpx;
}
.shop-header .shop-info .arrow-icon.data-v-a2ece00a {
  width: 32rpx;
  height: 32rpx;
  color: #999999;
}

/* 购物车商品 */
.cart-items.data-v-a2ece00a {
  padding: 0 30rpx;
}
.cart-item.data-v-a2ece00a {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F2F2F7;
}
.cart-item.data-v-a2ece00a:last-child {
  border-bottom: none;
}
.cart-item .checkbox-wrapper.data-v-a2ece00a {
  padding: 10rpx;
}
.cart-item .checkbox.data-v-a2ece00a {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cart-item .checkbox.checked.data-v-a2ece00a {
  background-color: #FF3B69;
  border-color: #FF3B69;
}
.cart-item .checkbox .check-icon.data-v-a2ece00a {
  color: #FFFFFF;
}
.cart-item .item-image.data-v-a2ece00a {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin: 0 20rpx;
}
.cart-item .item-info.data-v-a2ece00a {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 160rpx;
  justify-content: space-between;
}
.cart-item .item-info .item-name.data-v-a2ece00a {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.cart-item .item-info .item-specs.data-v-a2ece00a {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}
.cart-item .item-info .item-bottom.data-v-a2ece00a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}
.cart-item .item-info .item-bottom .item-price.data-v-a2ece00a {
  font-size: 32rpx;
  color: #FF3B69;
  font-weight: 600;
}
.cart-item .item-info .item-bottom .quantity-control.data-v-a2ece00a {
  display: flex;
  align-items: center;
}
.cart-item .item-info .item-bottom .quantity-control .quantity-btn.data-v-a2ece00a {
  width: 48rpx;
  height: 48rpx;
  border: 1rpx solid #EEEEEE;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cart-item .item-info .item-bottom .quantity-control .quantity-value.data-v-a2ece00a {
  min-width: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
}

/* 猜你喜欢 */
.recommend-section.data-v-a2ece00a {
  margin-top: 20rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
}
.recommend-section .section-title.data-v-a2ece00a {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}
.recommend-section .product-grid.data-v-a2ece00a {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.recommend-section .product-grid .product-item.data-v-a2ece00a {
  background-color: #FFFFFF;
  border-radius: 8rpx;
  overflow: hidden;
}
.recommend-section .product-grid .product-item .product-image.data-v-a2ece00a {
  width: 100%;
  height: 320rpx;
  border-radius: 8rpx;
}
.recommend-section .product-grid .product-item .product-name.data-v-a2ece00a {
  font-size: 28rpx;
  color: #333333;
  margin-top: 10rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.recommend-section .product-grid .product-item .product-price.data-v-a2ece00a {
  font-size: 28rpx;
  color: #FF3B69;
  font-weight: 600;
  margin-top: 10rpx;
}

/* 空购物车 */
.empty-cart.data-v-a2ece00a {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: calc(var(--status-bar-height, 25px) + 62px);
}
.empty-cart .empty-icon.data-v-a2ece00a {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-cart .empty-text.data-v-a2ece00a {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 40rpx;
}
.empty-cart .go-shopping-btn.data-v-a2ece00a {
  padding: 20rpx 60rpx;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  color: #FFFFFF;
  font-size: 28rpx;
  border-radius: 40rpx;
}

/* 底部结算栏 */
.checkout-bar.data-v-a2ece00a {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  padding-bottom: env(safe-area-inset-bottom);
  /* 适配 iPhone X 以上的底部安全区域 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.checkout-bar .select-all.data-v-a2ece00a {
  display: flex;
  align-items: center;
}
.checkout-bar .select-all .checkbox.data-v-a2ece00a {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}
.checkout-bar .select-all .checkbox.checked.data-v-a2ece00a {
  background-color: #FF3B69;
  border-color: #FF3B69;
}
.checkout-bar .select-all .checkbox .check-icon.data-v-a2ece00a {
  color: #FFFFFF;
}
.checkout-bar .select-all text.data-v-a2ece00a {
  font-size: 28rpx;
  color: #333333;
}
.checkout-bar .total-section.data-v-a2ece00a {
  display: flex;
  align-items: center;
}
.checkout-bar .total-section .total-price.data-v-a2ece00a {
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #333333;
}
.checkout-bar .total-section .total-price .price.data-v-a2ece00a {
  color: #FF3B69;
  font-weight: 600;
}
.checkout-bar .total-section .checkout-btn.data-v-a2ece00a {
  padding: 16rpx 40rpx;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  color: #FFFFFF;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.checkout-bar .delete-section .delete-btn.data-v-a2ece00a {
  padding: 16rpx 40rpx;
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 28rpx;
  border-radius: 40rpx;
}

/* 底部安全区域 */
.safe-area-bottom.data-v-a2ece00a {
  height: 34px;
  /* iOS 安全区域高度 */
}