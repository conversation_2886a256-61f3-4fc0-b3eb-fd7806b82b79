"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      errorCode: "",
      errorMessage: "抱歉，应用遇到了一些问题",
      errorDetails: "",
      fromPage: "",
      showDetails: false,
      errorType: "general"
      // general, network, permission, auth
    };
  },
  computed: {
    errorTitle() {
      const titles = {
        general: "应用错误",
        network: "网络错误",
        permission: "权限错误",
        auth: "授权错误"
      };
      return titles[this.errorType] || "应用错误";
    }
  },
  onLoad(options) {
    if (options.error) {
      this.errorMessage = decodeURIComponent(options.error) || this.errorMessage;
    }
    if (options.details) {
      this.errorDetails = decodeURIComponent(options.details);
    }
    if (options.from) {
      this.fromPage = decodeURIComponent(options.from);
    }
    if (options.type) {
      this.errorType = options.type;
    }
    if (options.code) {
      this.errorCode = options.code;
    }
    common_vendor.index.__f__("error", "at pages/error/index.vue:75", "Error page loaded:", {
      message: this.errorMessage,
      details: this.errorDetails,
      from: this.fromPage,
      type: this.errorType,
      code: this.errorCode
    });
  },
  methods: {
    // 返回首页
    goHome() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    },
    // 返回上一页
    goBack() {
      if (this.fromPage && this.fromPage !== "pages/error/index") {
        try {
          if (this.fromPage.startsWith("/pages/")) {
            common_vendor.index.navigateTo({
              url: this.fromPage,
              fail: () => {
                common_vendor.index.navigateBack({
                  delta: 1
                });
              }
            });
          } else {
            common_vendor.index.navigateBack({
              delta: 1
            });
          }
        } catch (e) {
          this.goHome();
        }
      } else {
        common_vendor.index.navigateBack({
          delta: 1,
          fail: () => {
            this.goHome();
          }
        });
      }
    },
    // 切换显示详情
    toggleDetails() {
      this.showDetails = !this.showDetails;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$20,
    b: common_vendor.t($options.errorTitle),
    c: common_vendor.t($data.errorMessage),
    d: $data.showDetails
  }, $data.showDetails ? {
    e: common_vendor.t($data.errorDetails)
  } : {}, {
    f: common_vendor.o((...args) => $options.goHome && $options.goHome(...args)),
    g: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    h: common_vendor.t($data.showDetails ? "隐藏" : "显示"),
    i: common_vendor.o((...args) => $options.toggleDetails && $options.toggleDetails(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/error/index.js.map
