# Vue2迁移到Vue3指南

## 迁移原则

在将磁州生活网项目从Vue2迁移到Vue3的过程中，我们必须遵循以下核心原则：

1. **功能一致性**：迁移后的代码必须保持与原Vue2版本完全相同的功能
2. **样式一致性**：所有UI元素的样式、布局和响应式行为必须保持一致
3. **性能维持或提升**：迁移不应导致性能下降
4. **代码质量提升**：利用Vue3新特性提高代码可维护性

## 迁移步骤

### 1. 环境与依赖升级

```bash
# 更新Vue核心依赖
npm install vue@3.x vuex@4.x vue-router@4.x

# 更新uni-app相关依赖
npm install @dcloudio/uni-app@3.x
```

### 2. 入口文件改造

将`main.js`从Vue2写法改为Vue3写法：

```js
// Vue2写法
import Vue from 'vue'
import App from './App'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()

// Vue3写法
import App from './App'
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
```

### 3. 组件API迁移

#### Options API到Composition API的转换

虽然Vue3支持Options API，但建议逐步迁移到Composition API以获得更好的代码组织和类型支持。

**Vue2 Options API示例：**
```vue
<script>
export default {
  data() {
    return {
      message: 'Hello',
      count: 0
    }
  },
  computed: {
    doubleCount() {
      return this.count * 2
    }
  },
  methods: {
    increment() {
      this.count++
    }
  },
  mounted() {
    console.log('组件已挂载')
  }
}
</script>
```

**Vue3 Composition API等效实现：**
```vue
<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式状态
const message = ref('Hello')
const count = ref(0)

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 方法
function increment() {
  count.value++
}

// 生命周期钩子
onMounted(() => {
  console.log('组件已挂载')
})
</script>
```

### 4. 常见API变更对照

| Vue2 | Vue3 | 迁移说明 |
|------|------|---------|
| `this.$set(obj, key, value)` | 直接赋值 | Vue3的响应式系统支持直接赋值 |
| `this.$destroy()` | 不再需要 | Vue3组件实例会自动清理 |
| `this.$on`, `this.$off`, `this.$once` | 使用外部事件库 | 移除了实例事件方法，使用独立事件库如mitt |
| `Vue.observable()` | `reactive()` | 使用reactive创建响应式对象 |
| `filters` | 使用方法或计算属性 | Vue3移除了过滤器特性 |

### 5. 模板语法变更

大多数Vue2模板语法在Vue3中仍然有效，但有一些细微变化：

- **多根节点**：Vue3组件模板支持多个根节点
- **v-model变更**：Vue3中v-model默认prop为`modelValue`，默认事件为`update:modelValue`
- **v-if与v-for优先级**：Vue3中v-if总是优先于v-for执行

### 6. 生命周期钩子对应关系

| Vue2 | Vue3 Composition API |
|------|---------------------|
| beforeCreate | setup() |
| created | setup() |
| beforeMount | onBeforeMount |
| mounted | onMounted |
| beforeUpdate | onBeforeUpdate |
| updated | onUpdated |
| beforeDestroy | onBeforeUnmount |
| destroyed | onUnmounted |
| errorCaptured | onErrorCaptured |

### 7. 样式保持一致性

- 确保所有CSS类名保持不变
- 检查scoped样式是否正常工作
- 验证动态样式绑定是否正确转换

### 8. 特殊情况处理

#### 8.1 全局API调用

Vue2中的全局API调用需要修改：

```js
// Vue2
Vue.prototype.$http = axios

// Vue3
app.config.globalProperties.$http = axios
```

#### 8.2 Mixins转换

考虑将mixins转换为可复用的组合式函数：

```js
// Vue2 mixin
export const myMixin = {
  data() {
    return { count: 0 }
  },
  methods: {
    increment() {
      this.count++
    }
  }
}

// Vue3 组合式函数
import { ref } from 'vue'
export function useCounter() {
  const count = ref(0)
  function increment() {
    count.value++
  }
  return { count, increment }
}
```

### 9. 测试与验证

每完成一个组件的迁移，必须进行以下验证：

1. 功能测试：确保所有功能按预期工作
2. UI对比：对比迁移前后的UI，确保完全一致
3. 交互测试：验证所有用户交互行为一致
4. 响应式测试：在不同屏幕尺寸下测试布局

## 常见问题与解决方案

### 响应式丢失问题

**问题**：在Composition API中，响应式数据需要使用`.value`访问，忘记添加可能导致响应式丢失。

**解决方案**：
- 使用ESLint插件检测缺失的`.value`
- 对复杂对象使用`reactive`而非`ref`
- 使用`toRefs`将响应式对象转换为独立的ref

### 生命周期执行时机差异

**问题**：Vue3中的某些生命周期钩子执行时机与Vue2略有不同。

**解决方案**：
- 避免在生命周期钩子中依赖精确的执行顺序
- 使用`nextTick`确保DOM更新后再执行相关操作

### 事件处理变更

**问题**：Vue3移除了组件实例的事件系统（$on，$off，$once）。

**解决方案**：
- 使用props和emit进行父子组件通信
- 使用provide/inject进行深层组件通信
- 引入外部事件库如mitt处理跨组件通信

## 迁移检查清单

- [ ] 更新Vue核心依赖和相关库
- [ ] 修改入口文件创建应用的方式
- [ ] 更新组件选项和API调用
- [ ] 检查并更新模板语法
- [ ] 验证样式应用正确
- [ ] 测试所有功能和交互
- [ ] 确认响应式数据正常工作
- [ ] 验证生命周期钩子正确执行