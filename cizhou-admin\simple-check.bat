@echo off

echo.
echo ========================================
echo   磁州生活网环境检查
echo ========================================
echo.

echo 正在检查环境...
echo.

echo [1] 检查 Node.js
where node
if errorlevel 1 (
    echo    未找到 Node.js
    echo    请访问：https://nodejs.org/ 下载安装
) else (
    echo    找到 Node.js，版本：
    node --version
)

echo.
echo [2] 检查 npm
where npm
if errorlevel 1 (
    echo    未找到 npm
) else (
    echo    找到 npm，版本：
    npm --version
)

echo.
echo [3] 检查 MySQL
where mysql
if errorlevel 1 (
    echo    未找到 MySQL
    echo    请访问：https://dev.mysql.com/downloads/mysql/ 下载安装
) else (
    echo    找到 MySQL，尝试连接：
    mysql -uroot -pcizhou123456 -e "SELECT 'MySQL连接成功' as status;"
)

echo.
echo [4] 检查 Redis
where redis-cli
if errorlevel 1 (
    echo    未找到 Redis
    echo    请访问：https://github.com/tporadowski/redis/releases 下载安装
) else (
    echo    找到 Redis，尝试连接：
    redis-cli -a cizhou123456 ping
)

echo.
echo ========================================
echo 检查完成
echo ========================================
echo.

echo 如果上述工具缺失，请安装：
echo.
echo Node.js: https://nodejs.org/
echo Docker:  https://www.docker.com/products/docker-desktop/
echo Java:    https://adoptium.net/
echo.

pause
