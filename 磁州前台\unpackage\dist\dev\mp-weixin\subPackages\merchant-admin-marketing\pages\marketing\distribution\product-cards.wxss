/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.product-cards-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.history-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 商品选择部分 */
.product-selection-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.search-btn {
  display: flex;
  align-items: center;
  background-color: rgba(107, 15, 190, 0.05);
  border-radius: 16px;
  padding: 6px 12px;
}
.search-btn text {
  font-size: 14px;
  color: #6B0FBE;
  margin-left: 6px;
}
.product-scroll {
  max-height: 320px;
}
.product-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.product-item {
  display: flex;
  background-color: #F8F9FC;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}
.product-item.selected {
  border-color: #6B0FBE;
  background-color: rgba(107, 15, 190, 0.05);
}
.select-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #6B0FBE;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
.product-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
}
.product-info {
  flex: 1;
  padding: 10px;
}
.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.product-price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}
.product-price {
  font-size: 14px;
  color: #FF3B30;
  font-weight: 500;
}
.product-commission {
  font-size: 12px;
  color: #6B0FBE;
}
.product-sales {
  font-size: 12px;
  color: #999;
}

/* 卡片预览部分 */
.card-preview-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}
.template-selector {
  display: flex;
  align-items: center;
  background-color: rgba(107, 15, 190, 0.05);
  border-radius: 16px;
  padding: 6px 12px;
}
.template-selector text {
  font-size: 14px;
  color: #6B0FBE;
}
.selector-arrow {
  width: 8px;
  height: 8px;
  border-right: 2px solid #6B0FBE;
  border-bottom: 2px solid #6B0FBE;
  transform: rotate(45deg);
  margin-left: 8px;
}
.card-preview {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}
.preview-card {
  width: 300px;
  background-color: #FFFFFF;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}
.card-product-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
}
.card-content {
  padding: 16px;
}
.card-product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.card-price-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.card-price {
  font-size: 18px;
  color: #FF3B30;
  font-weight: 600;
  margin-right: 8px;
}
.card-original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-shop-info {
  display: flex;
  align-items: center;
}
.card-shop-logo {
  width: 20px;
  height: 20px;
  border-radius: 10px;
  margin-right: 8px;
}
.card-shop-name {
  font-size: 12px;
  color: #666;
}
.card-qrcode-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.card-qrcode-placeholder {
  width: 40px;
  height: 40px;
  background-color: #F0F0F0;
  border-radius: 4px;
  margin-bottom: 4px;
  position: relative;
}
.card-qrcode-placeholder::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background-color: #CCCCCC;
  border-radius: 2px;
}
.card-scan-text {
  font-size: 10px;
  color: #999;
}

/* 自定义选项 */
.customization-options {
  margin-bottom: 20px;
}
.option-group {
  margin-bottom: 20px;
}
.option-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
}
.style-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
.style-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
}
.style-color {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  margin-bottom: 6px;
  transition: all 0.3s ease;
}
.style-name {
  font-size: 12px;
  color: #666;
}
.style-option.active .style-color {
  transform: scale(1.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
.style-option.active .style-name {
  color: #6B0FBE;
  font-weight: 500;
}
.toggle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}
.toggle-option:last-child {
  border-bottom: none;
}
.toggle-label {
  font-size: 14px;
  color: #333;
}
.tag-input {
  width: 120px;
  height: 36px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  text-align: right;
}

/* 生成按钮 */
.generate-button-container {
  padding: 0 20px;
}
.generate-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  border-radius: 24px;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 4px 12px rgba(107, 15, 190, 0.2);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}
.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
}
.empty-text {
  font-size: 14px;
  color: #999;
  text-align: center;
}

/* 卡片模板样式 */
.horizontal .preview-card {
  display: flex;
  width: 300px;
  height: 120px;
}
.horizontal .card-product-image {
  width: 120px;
  height: 120px;
}
.horizontal .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.horizontal .card-product-name {
  font-size: 14px;
  margin-bottom: 6px;
  -webkit-line-clamp: 1;
}
.horizontal .card-price-row {
  margin-bottom: 8px;
}
.horizontal .card-price {
  font-size: 16px;
}
.horizontal .card-footer {
  margin-top: auto;
}
.vertical .preview-card {
  width: 240px;
}
.minimal .preview-card {
  width: 280px;
  border: 1px solid #EEEEEE;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}
.minimal .card-product-image {
  height: 160px;
}
.minimal .card-content {
  padding: 12px;
}
.minimal .card-product-name {
  font-size: 14px;
  margin-bottom: 8px;
}
.minimal .card-price {
  font-size: 16px;
}