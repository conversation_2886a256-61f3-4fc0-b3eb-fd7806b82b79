<template>
  <view class="test-container">
    <view class="header">
      <text class="title">API对接测试页面</text>
      <text class="subtitle">测试前台和后台的API连通性</text>
    </view>

    <view class="test-section">
      <view class="section-title">🔗 连接状态</view>
      <view class="status-item">
        <text class="status-label">后台服务状态：</text>
        <text :class="['status-value', backendStatus.connected ? 'success' : 'error']">
          {{ backendStatus.connected ? '已连接' : '未连接' }}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">API基础地址：</text>
        <text class="status-value">{{ apiBaseUrl }}</text>
      </view>
      <button class="test-btn" @click="testConnection">测试连接</button>
    </view>

    <view class="test-section">
      <view class="section-title">👤 用户API测试</view>
      <view class="test-item">
        <button class="test-btn" @click="testUserLogin">测试用户登录</button>
        <text class="test-result">{{ userTestResult }}</text>
      </view>
      <view class="test-item">
        <button class="test-btn" @click="testGetUserInfo">获取用户信息</button>
        <text class="test-result">{{ userInfoResult }}</text>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">📰 内容API测试</view>
      <view class="test-item">
        <button class="test-btn" @click="testGetNews">获取新闻列表</button>
        <text class="test-result">{{ newsResult }}</text>
      </view>
      <view class="test-item">
        <button class="test-btn" @click="testGetInfo">获取信息列表</button>
        <text class="test-result">{{ infoResult }}</text>
      </view>
      <view class="test-item">
        <button class="test-btn" @click="testGetBusiness">获取商家列表</button>
        <text class="test-result">{{ businessResult }}</text>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">🚗 拼车API测试</view>
      <view class="test-item">
        <button class="test-btn" @click="testGetCarpool">获取拼车列表</button>
        <text class="test-result">{{ carpoolResult }}</text>
      </view>
      <view class="test-item">
        <button class="test-btn" @click="testPublishCarpool">发布拼车信息</button>
        <text class="test-result">{{ publishResult }}</text>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">📊 测试结果汇总</view>
      <view class="summary">
        <view class="summary-item">
          <text class="summary-label">总测试数：</text>
          <text class="summary-value">{{ testSummary.total }}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">成功数：</text>
          <text class="summary-value success">{{ testSummary.success }}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">失败数：</text>
          <text class="summary-value error">{{ testSummary.failed }}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">成功率：</text>
          <text class="summary-value">{{ testSummary.successRate }}%</text>
        </view>
      </view>
      <button class="test-btn primary" @click="runAllTests">运行所有测试</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import api from '@/api/index.js';

// 后台服务状态
const backendStatus = ref({
  connected: false,
  message: ''
});

// API基础地址
const apiBaseUrl = ref('http://localhost:8080/api');

// 测试结果
const userTestResult = ref('');
const userInfoResult = ref('');
const newsResult = ref('');
const infoResult = ref('');
const businessResult = ref('');
const carpoolResult = ref('');
const publishResult = ref('');

// 测试统计
const testResults = ref([]);

// 测试汇总
const testSummary = computed(() => {
  const total = testResults.value.length;
  const success = testResults.value.filter(r => r.success).length;
  const failed = total - success;
  const successRate = total > 0 ? Math.round((success / total) * 100) : 0;
  
  return { total, success, failed, successRate };
});

// 页面加载时测试连接
onMounted(() => {
  testConnection();
});

// 测试连接
const testConnection = async () => {
  try {
    uni.showLoading({ title: '测试连接中...' });
    
    // 尝试调用健康检查API
    const response = await uni.request({
      url: `${apiBaseUrl.value}/health`,
      method: 'GET',
      timeout: 5000
    });
    
    if (response.statusCode === 200) {
      backendStatus.value = {
        connected: true,
        message: '后台服务连接正常'
      };
    } else {
      backendStatus.value = {
        connected: false,
        message: `连接失败，状态码：${response.statusCode}`
      };
    }
  } catch (error) {
    console.error('连接测试失败:', error);
    backendStatus.value = {
      connected: false,
      message: `连接失败：${error.message || '网络错误'}`
    };
  } finally {
    uni.hideLoading();
  }
};

// 测试用户登录
const testUserLogin = async () => {
  try {
    userTestResult.value = '测试中...';
    
    const result = await api.user.login({
      phone: '13800138000',
      password: '123456'
    });
    
    if (result.success) {
      userTestResult.value = '✅ 登录成功';
      addTestResult('用户登录', true, '登录API正常工作');
    } else {
      userTestResult.value = `❌ 登录失败：${result.message}`;
      addTestResult('用户登录', false, result.message);
    }
  } catch (error) {
    userTestResult.value = `❌ 登录错误：${error.message}`;
    addTestResult('用户登录', false, error.message);
  }
};

// 测试获取用户信息
const testGetUserInfo = async () => {
  try {
    userInfoResult.value = '测试中...';
    
    const result = await api.user.getUserInfo();
    
    if (result.success) {
      userInfoResult.value = '✅ 获取用户信息成功';
      addTestResult('获取用户信息', true, '用户信息API正常工作');
    } else {
      userInfoResult.value = `❌ 获取失败：${result.message}`;
      addTestResult('获取用户信息', false, result.message);
    }
  } catch (error) {
    userInfoResult.value = `❌ 获取错误：${error.message}`;
    addTestResult('获取用户信息', false, error.message);
  }
};

// 测试获取新闻
const testGetNews = async () => {
  try {
    newsResult.value = '测试中...';
    
    const result = await api.news.getList({ page: 1, limit: 5 });
    
    if (result.success) {
      newsResult.value = `✅ 获取新闻成功，共${result.data.length}条`;
      addTestResult('获取新闻', true, `获取到${result.data.length}条新闻`);
    } else {
      newsResult.value = `❌ 获取失败：${result.message}`;
      addTestResult('获取新闻', false, result.message);
    }
  } catch (error) {
    newsResult.value = `❌ 获取错误：${error.message}`;
    addTestResult('获取新闻', false, error.message);
  }
};

// 测试获取信息
const testGetInfo = async () => {
  try {
    infoResult.value = '测试中...';
    
    const result = await api.info.getAll({ page: 1, limit: 5 });
    
    if (result.success) {
      infoResult.value = `✅ 获取信息成功，共${result.data.length}条`;
      addTestResult('获取信息', true, `获取到${result.data.length}条信息`);
    } else {
      infoResult.value = `❌ 获取失败：${result.message}`;
      addTestResult('获取信息', false, result.message);
    }
  } catch (error) {
    infoResult.value = `❌ 获取错误：${error.message}`;
    addTestResult('获取信息', false, error.message);
  }
};

// 测试获取商家
const testGetBusiness = async () => {
  try {
    businessResult.value = '测试中...';
    
    const result = await api.business.getList({ page: 1, limit: 5 });
    
    if (result.success) {
      businessResult.value = `✅ 获取商家成功，共${result.data.length}条`;
      addTestResult('获取商家', true, `获取到${result.data.length}条商家信息`);
    } else {
      businessResult.value = `❌ 获取失败：${result.message}`;
      addTestResult('获取商家', false, result.message);
    }
  } catch (error) {
    businessResult.value = `❌ 获取错误：${error.message}`;
    addTestResult('获取商家', false, error.message);
  }
};

// 测试获取拼车
const testGetCarpool = async () => {
  try {
    carpoolResult.value = '测试中...';
    
    const result = await api.carpool.getList({ page: 1, limit: 5 });
    
    if (result.success) {
      carpoolResult.value = `✅ 获取拼车成功，共${result.data.length}条`;
      addTestResult('获取拼车', true, `获取到${result.data.length}条拼车信息`);
    } else {
      carpoolResult.value = `❌ 获取失败：${result.message}`;
      addTestResult('获取拼车', false, result.message);
    }
  } catch (error) {
    carpoolResult.value = `❌ 获取错误：${error.message}`;
    addTestResult('获取拼车', false, error.message);
  }
};

// 测试发布拼车
const testPublishCarpool = async () => {
  try {
    publishResult.value = '测试中...';
    
    const testData = {
      type: 'people-to-car',
      departure: '磁县',
      destination: '邯郸',
      departure_time: '2024-12-24 08:00',
      passenger_count: 2,
      price: 20,
      contact: '13800138000',
      description: '测试发布拼车信息'
    };
    
    const result = await api.carpool.publish(testData);
    
    if (result.success) {
      publishResult.value = '✅ 发布拼车成功';
      addTestResult('发布拼车', true, '拼车发布API正常工作');
    } else {
      publishResult.value = `❌ 发布失败：${result.message}`;
      addTestResult('发布拼车', false, result.message);
    }
  } catch (error) {
    publishResult.value = `❌ 发布错误：${error.message}`;
    addTestResult('发布拼车', false, error.message);
  }
};

// 运行所有测试
const runAllTests = async () => {
  // 清空之前的测试结果
  testResults.value = [];
  
  uni.showLoading({ title: '运行测试中...' });
  
  try {
    // 按顺序运行所有测试
    await testConnection();
    await testUserLogin();
    await testGetUserInfo();
    await testGetNews();
    await testGetInfo();
    await testGetBusiness();
    await testGetCarpool();
    await testPublishCarpool();
    
    // 显示测试完成提示
    uni.showModal({
      title: '测试完成',
      content: `总共运行${testSummary.value.total}个测试，成功${testSummary.value.success}个，失败${testSummary.value.failed}个，成功率${testSummary.value.successRate}%`,
      showCancel: false
    });
  } catch (error) {
    console.error('运行测试失败:', error);
  } finally {
    uni.hideLoading();
  }
};

// 添加测试结果
const addTestResult = (testName, success, message) => {
  testResults.value.push({
    name: testName,
    success,
    message,
    timestamp: new Date().toLocaleTimeString()
  });
};
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.test-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.status-item, .test-item, .summary-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.status-label, .summary-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.status-value, .summary-value {
  font-size: 28rpx;
  color: #333;
  
  &.success {
    color: #52c41a;
  }
  
  &.error {
    color: #ff4d4f;
  }
}

.test-btn {
  background: #1677ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
  
  &.primary {
    background: #52c41a;
    width: 100%;
    margin-right: 0;
  }
}

.test-result {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.summary {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
</style>
