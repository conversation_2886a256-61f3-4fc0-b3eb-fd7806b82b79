{"version": 3, "file": "configManager.js", "sources": ["subPackages/activity-showcase/utils/configManager.js"], "sourcesContent": ["/**\n * 活动展示配置管理器\n * 负责从后台管理系统获取配置并应用到前台\n */\n\n// 默认配置\nconst DEFAULT_CONFIG = {\n  structure: {\n    navbar: {\n      enabled: true,\n      title: '活动中心',\n      backgroundColor: '#ffffff',\n      textColor: '#333333',\n      showBackButton: true,\n      showCloseButton: false,\n      showSearchButton: true,\n      showShareButton: true,\n      style: 'default',\n      height: 60,\n      borderRadius: 0,\n      shadow: 'light',\n      position: 'fixed',\n      zIndex: 1000,\n      animation: 'fadeIn'\n    },\n    banner: {\n      enabled: true,\n      autoplay: true,\n      interval: 5000,\n      duration: 500,\n      circular: true,\n      showIndicators: true,\n      indicatorColor: 'rgba(255, 255, 255, 0.6)',\n      indicatorActiveColor: '#ffffff',\n      indicatorPosition: 'bottom-center',\n      height: 200,\n      borderRadius: 8,\n      margin: 16,\n      padding: 0,\n      aspectRatio: '16:9',\n      objectFit: 'cover',\n      lazyLoad: true,\n      preloadImages: 2,\n      touchable: true\n    }\n  },\n  display: {\n    cardStyle: {\n      layout: 'card',\n      width: 'auto',\n      height: 'auto',\n      aspectRatio: '16:9',\n      backgroundColor: '#ffffff',\n      borderColor: '#e5e7eb',\n      borderWidth: 1,\n      borderRadius: 8,\n      shadow: 'light',\n      padding: 16,\n      margin: 8,\n      overflow: 'hidden',\n      hoverEffect: true,\n      hoverScale: 1.05,\n      clickEffect: true,\n      animationDuration: 300\n    },\n    imageDisplay: {\n      enabled: true,\n      width: '100%',\n      height: 'auto',\n      aspectRatio: '16:9',\n      objectFit: 'cover',\n      borderRadius: 8,\n      border: 'none',\n      opacity: 1,\n      brightness: 1,\n      contrast: 1,\n      saturation: 1,\n      blur: 0,\n      quality: 0.8,\n      lazyLoad: true,\n      placeholder: '',\n      errorImage: '',\n      loadingImage: '',\n      preload: false,\n      cache: true\n    }\n  },\n  interaction: {},\n  theme: {},\n  data: {},\n  system: {}\n}\n\nclass ConfigManager {\n  constructor() {\n    this.config = DEFAULT_CONFIG\n    this.isLoaded = false\n    this.listeners = []\n  }\n\n  /**\n   * 初始化配置管理器\n   */\n  async init() {\n    try {\n      await this.loadConfig()\n      this.applyConfig()\n      this.isLoaded = true\n      console.log('活动展示配置加载成功')\n    } catch (error) {\n      console.warn('配置加载失败，使用默认配置:', error)\n      this.config = DEFAULT_CONFIG\n      this.applyConfig()\n      this.isLoaded = true\n    }\n  }\n\n  /**\n   * 从本地文件加载配置\n   */\n  async loadConfig() {\n    try {\n      // 尝试从本地配置文件加载\n      const response = await uni.request({\n        url: '/subPackages/activity-showcase/config/display-config.json',\n        method: 'GET'\n      })\n      \n      if (response.statusCode === 200 && response.data) {\n        this.config = { ...DEFAULT_CONFIG, ...response.data }\n        return\n      }\n    } catch (error) {\n      console.log('本地配置文件不存在，尝试从后台API获取')\n    }\n\n    // 如果本地文件不存在，尝试从后台API获取\n    try {\n      const response = await uni.request({\n        url: 'http://localhost:8080/api/v1/activity-display/config',\n        method: 'GET',\n        header: {\n          'Content-Type': 'application/json'\n        }\n      })\n      \n      if (response.statusCode === 200 && response.data && response.data.data) {\n        this.config = { ...DEFAULT_CONFIG, ...response.data.data }\n      }\n    } catch (error) {\n      console.warn('从后台API获取配置失败:', error)\n      throw error\n    }\n  }\n\n  /**\n   * 应用配置到页面\n   */\n  applyConfig() {\n    // 应用导航栏配置\n    this.applyNavbarConfig()\n    \n    // 应用轮播图配置\n    this.applyBannerConfig()\n    \n    // 应用卡片样式配置\n    this.applyCardStyleConfig()\n    \n    // 应用图片显示配置\n    this.applyImageDisplayConfig()\n    \n    // 通知监听器配置已更新\n    this.notifyListeners()\n  }\n\n  /**\n   * 应用导航栏配置\n   */\n  applyNavbarConfig() {\n    const navbarConfig = this.config.structure?.navbar\n    if (!navbarConfig || !navbarConfig.enabled) return\n\n    // 设置CSS变量\n    const root = document.documentElement\n    if (root) {\n      root.style.setProperty('--navbar-bg-color', navbarConfig.backgroundColor)\n      root.style.setProperty('--navbar-text-color', navbarConfig.textColor)\n      root.style.setProperty('--navbar-height', `${navbarConfig.height}px`)\n      root.style.setProperty('--navbar-border-radius', `${navbarConfig.borderRadius}px`)\n      root.style.setProperty('--navbar-z-index', navbarConfig.zIndex)\n    }\n  }\n\n  /**\n   * 应用轮播图配置\n   */\n  applyBannerConfig() {\n    const bannerConfig = this.config.structure?.banner\n    if (!bannerConfig || !bannerConfig.enabled) return\n\n    const root = document.documentElement\n    if (root) {\n      root.style.setProperty('--banner-height', `${bannerConfig.height}px`)\n      root.style.setProperty('--banner-border-radius', `${bannerConfig.borderRadius}px`)\n      root.style.setProperty('--banner-margin', `${bannerConfig.margin}px`)\n      root.style.setProperty('--banner-padding', `${bannerConfig.padding}px`)\n      root.style.setProperty('--banner-indicator-color', bannerConfig.indicatorColor)\n      root.style.setProperty('--banner-indicator-active-color', bannerConfig.indicatorActiveColor)\n    }\n  }\n\n  /**\n   * 应用卡片样式配置\n   */\n  applyCardStyleConfig() {\n    const cardConfig = this.config.display?.cardStyle\n    if (!cardConfig) return\n\n    const root = document.documentElement\n    if (root) {\n      root.style.setProperty('--card-bg-color', cardConfig.backgroundColor)\n      root.style.setProperty('--card-border-color', cardConfig.borderColor)\n      root.style.setProperty('--card-border-width', `${cardConfig.borderWidth}px`)\n      root.style.setProperty('--card-border-radius', `${cardConfig.borderRadius}px`)\n      root.style.setProperty('--card-padding', `${cardConfig.padding}px`)\n      root.style.setProperty('--card-margin', `${cardConfig.margin}px`)\n      root.style.setProperty('--card-hover-scale', cardConfig.hoverScale)\n      root.style.setProperty('--card-animation-duration', `${cardConfig.animationDuration}ms`)\n    }\n  }\n\n  /**\n   * 应用图片显示配置\n   */\n  applyImageDisplayConfig() {\n    const imageConfig = this.config.display?.imageDisplay\n    if (!imageConfig || !imageConfig.enabled) return\n\n    const root = document.documentElement\n    if (root) {\n      root.style.setProperty('--image-border-radius', `${imageConfig.borderRadius}px`)\n      root.style.setProperty('--image-opacity', imageConfig.opacity)\n      root.style.setProperty('--image-brightness', imageConfig.brightness)\n      root.style.setProperty('--image-contrast', imageConfig.contrast)\n      root.style.setProperty('--image-saturation', imageConfig.saturation)\n      root.style.setProperty('--image-blur', `${imageConfig.blur}px`)\n    }\n  }\n\n  /**\n   * 获取配置值\n   */\n  getConfig(path) {\n    const keys = path.split('.')\n    let value = this.config\n    \n    for (const key of keys) {\n      if (value && typeof value === 'object' && key in value) {\n        value = value[key]\n      } else {\n        return undefined\n      }\n    }\n    \n    return value\n  }\n\n  /**\n   * 检查功能是否启用\n   */\n  isEnabled(path) {\n    const config = this.getConfig(path)\n    return config && config.enabled === true\n  }\n\n  /**\n   * 添加配置变更监听器\n   */\n  addListener(callback) {\n    this.listeners.push(callback)\n  }\n\n  /**\n   * 移除配置变更监听器\n   */\n  removeListener(callback) {\n    const index = this.listeners.indexOf(callback)\n    if (index > -1) {\n      this.listeners.splice(index, 1)\n    }\n  }\n\n  /**\n   * 通知所有监听器\n   */\n  notifyListeners() {\n    this.listeners.forEach(callback => {\n      try {\n        callback(this.config)\n      } catch (error) {\n        console.error('配置监听器执行失败:', error)\n      }\n    })\n  }\n\n  /**\n   * 重新加载配置\n   */\n  async reload() {\n    await this.loadConfig()\n    this.applyConfig()\n  }\n\n  /**\n   * 获取完整配置\n   */\n  getAllConfig() {\n    return this.config\n  }\n\n  /**\n   * 检查配置是否已加载\n   */\n  isConfigLoaded() {\n    return this.isLoaded\n  }\n}\n\n// 创建全局配置管理器实例\nconst configManager = new ConfigManager()\n\n// 导出配置管理器\nexport default configManager\n\n// 也可以通过全局变量访问\nif (typeof getApp === 'function') {\n  const app = getApp()\n  app.globalData = app.globalData || {}\n  app.globalData.configManager = configManager\n}\n"], "names": ["uni"], "mappings": ";;AAMA,MAAM,iBAAiB;AAAA,EACrB,WAAW;AAAA,IACT,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ;AAAA,IACD,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,sBAAsB;AAAA,MACtB,mBAAmB;AAAA,MACnB,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,aAAa;AAAA,MACb,WAAW;AAAA,MACX,UAAU;AAAA,MACV,eAAe;AAAA,MACf,WAAW;AAAA,IACZ;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,WAAW;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,mBAAmB;AAAA,IACpB;AAAA,IACD,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,WAAW;AAAA,MACX,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,EACF;AAAA,EACD,aAAa,CAAE;AAAA,EACf,OAAO,CAAE;AAAA,EACT,MAAM,CAAE;AAAA,EACR,QAAQ,CAAE;AACZ;AAEA,MAAM,cAAc;AAAA,EAClB,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,YAAY,CAAE;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,OAAO;AACX,QAAI;AACF,YAAM,KAAK,WAAY;AACvB,WAAK,YAAa;AAClB,WAAK,WAAW;AAChBA,oBAAAA,MAAA,MAAA,OAAA,+DAAY,YAAY;AAAA,IACzB,SAAQ,OAAO;AACdA,oBAAAA,MAAA,MAAA,QAAA,+DAAa,kBAAkB,KAAK;AACpC,WAAK,SAAS;AACd,WAAK,YAAa;AAClB,WAAK,WAAW;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,aAAa;AACjB,QAAI;AAEF,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK;AAAA,QACL,QAAQ;AAAA,MAChB,CAAO;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,MAAM;AAChD,aAAK,SAAS,EAAE,GAAG,gBAAgB,GAAG,SAAS,KAAM;AACrD;AAAA,MACD;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAA,MAAA,OAAA,+DAAY,sBAAsB;AAAA,IACnC;AAGD,QAAI;AACF,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,MACT,CAAO;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,QAAQ,SAAS,KAAK,MAAM;AACtE,aAAK,SAAS,EAAE,GAAG,gBAAgB,GAAG,SAAS,KAAK,KAAM;AAAA,MAC3D;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAA,MAAA,QAAA,+DAAa,iBAAiB,KAAK;AACnC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc;AAEZ,SAAK,kBAAmB;AAGxB,SAAK,kBAAmB;AAGxB,SAAK,qBAAsB;AAG3B,SAAK,wBAAyB;AAG9B,SAAK,gBAAiB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;;AAClB,UAAM,gBAAe,UAAK,OAAO,cAAZ,mBAAuB;AAC5C,QAAI,CAAC,gBAAgB,CAAC,aAAa;AAAS;AAG5C,UAAM,OAAO,SAAS;AACtB,QAAI,MAAM;AACR,WAAK,MAAM,YAAY,qBAAqB,aAAa,eAAe;AACxE,WAAK,MAAM,YAAY,uBAAuB,aAAa,SAAS;AACpE,WAAK,MAAM,YAAY,mBAAmB,GAAG,aAAa,MAAM,IAAI;AACpE,WAAK,MAAM,YAAY,0BAA0B,GAAG,aAAa,YAAY,IAAI;AACjF,WAAK,MAAM,YAAY,oBAAoB,aAAa,MAAM;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;;AAClB,UAAM,gBAAe,UAAK,OAAO,cAAZ,mBAAuB;AAC5C,QAAI,CAAC,gBAAgB,CAAC,aAAa;AAAS;AAE5C,UAAM,OAAO,SAAS;AACtB,QAAI,MAAM;AACR,WAAK,MAAM,YAAY,mBAAmB,GAAG,aAAa,MAAM,IAAI;AACpE,WAAK,MAAM,YAAY,0BAA0B,GAAG,aAAa,YAAY,IAAI;AACjF,WAAK,MAAM,YAAY,mBAAmB,GAAG,aAAa,MAAM,IAAI;AACpE,WAAK,MAAM,YAAY,oBAAoB,GAAG,aAAa,OAAO,IAAI;AACtE,WAAK,MAAM,YAAY,4BAA4B,aAAa,cAAc;AAC9E,WAAK,MAAM,YAAY,mCAAmC,aAAa,oBAAoB;AAAA,IAC5F;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,uBAAuB;;AACrB,UAAM,cAAa,UAAK,OAAO,YAAZ,mBAAqB;AACxC,QAAI,CAAC;AAAY;AAEjB,UAAM,OAAO,SAAS;AACtB,QAAI,MAAM;AACR,WAAK,MAAM,YAAY,mBAAmB,WAAW,eAAe;AACpE,WAAK,MAAM,YAAY,uBAAuB,WAAW,WAAW;AACpE,WAAK,MAAM,YAAY,uBAAuB,GAAG,WAAW,WAAW,IAAI;AAC3E,WAAK,MAAM,YAAY,wBAAwB,GAAG,WAAW,YAAY,IAAI;AAC7E,WAAK,MAAM,YAAY,kBAAkB,GAAG,WAAW,OAAO,IAAI;AAClE,WAAK,MAAM,YAAY,iBAAiB,GAAG,WAAW,MAAM,IAAI;AAChE,WAAK,MAAM,YAAY,sBAAsB,WAAW,UAAU;AAClE,WAAK,MAAM,YAAY,6BAA6B,GAAG,WAAW,iBAAiB,IAAI;AAAA,IACxF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,0BAA0B;;AACxB,UAAM,eAAc,UAAK,OAAO,YAAZ,mBAAqB;AACzC,QAAI,CAAC,eAAe,CAAC,YAAY;AAAS;AAE1C,UAAM,OAAO,SAAS;AACtB,QAAI,MAAM;AACR,WAAK,MAAM,YAAY,yBAAyB,GAAG,YAAY,YAAY,IAAI;AAC/E,WAAK,MAAM,YAAY,mBAAmB,YAAY,OAAO;AAC7D,WAAK,MAAM,YAAY,sBAAsB,YAAY,UAAU;AACnE,WAAK,MAAM,YAAY,oBAAoB,YAAY,QAAQ;AAC/D,WAAK,MAAM,YAAY,sBAAsB,YAAY,UAAU;AACnE,WAAK,MAAM,YAAY,gBAAgB,GAAG,YAAY,IAAI,IAAI;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,MAAM;AACd,UAAM,OAAO,KAAK,MAAM,GAAG;AAC3B,QAAI,QAAQ,KAAK;AAEjB,eAAW,OAAO,MAAM;AACtB,UAAI,SAAS,OAAO,UAAU,YAAY,OAAO,OAAO;AACtD,gBAAQ,MAAM,GAAG;AAAA,MACzB,OAAa;AACL,eAAO;AAAA,MACR;AAAA,IACF;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,MAAM;AACd,UAAM,SAAS,KAAK,UAAU,IAAI;AAClC,WAAO,UAAU,OAAO,YAAY;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY,UAAU;AACpB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,UAAU;AACvB,UAAM,QAAQ,KAAK,UAAU,QAAQ,QAAQ;AAC7C,QAAI,QAAQ,IAAI;AACd,WAAK,UAAU,OAAO,OAAO,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB;AAChB,SAAK,UAAU,QAAQ,cAAY;AACjC,UAAI;AACF,iBAAS,KAAK,MAAM;AAAA,MACrB,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,+DAAA,cAAc,KAAK;AAAA,MAClC;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,SAAS;AACb,UAAM,KAAK,WAAY;AACvB,SAAK,YAAa;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACb,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AACf,WAAO,KAAK;AAAA,EACb;AACH;AAGK,MAAC,gBAAgB,IAAI,cAAe;AAMzC,IAAI,OAAO,WAAW,YAAY;AAChC,QAAM,MAAM,OAAQ;AACpB,MAAI,aAAa,IAAI,cAAc,CAAE;AACrC,MAAI,WAAW,gBAAgB;AACjC;;"}