<view class="profile-container data-v-c64ed3ce"><view class="profile-header data-v-c64ed3ce" style="{{'height:' + '300rpx' + ';' + ('background:' + 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)') + ';' + ('position:' + 'relative') + ';' + ('overflow:' + 'hidden')}}"><view class="bg-decoration data-v-c64ed3ce" style="{{'position:' + 'absolute' + ';' + ('top:' + '-50rpx') + ';' + ('right:' + '-50rpx') + ';' + ('width:' + '300rpx') + ';' + ('height:' + '300rpx') + ';' + ('border-radius:' + '50%') + ';' + ('background:' + 'rgba(255,255,255,0.1)') + ';' + ('z-index:' + '1')}}"></view><view class="bg-decoration data-v-c64ed3ce" style="{{'position:' + 'absolute' + ';' + ('bottom:' + '-80rpx') + ';' + ('left:' + '-80rpx') + ';' + ('width:' + '250rpx') + ';' + ('height:' + '250rpx') + ';' + ('border-radius:' + '50%') + ';' + ('background:' + 'rgba(255,255,255,0.08)') + ';' + ('z-index:' + '1')}}"></view><view class="navbar data-v-c64ed3ce" style="{{'position:' + 'absolute' + ';' + ('top:' + '70rpx') + ';' + ('left:' + '0') + ';' + ('right:' + '0') + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'space-between') + ';' + ('padding:' + '0 30rpx') + ';' + ('z-index:' + '10')}}"><view class="back-btn data-v-c64ed3ce" bindtap="{{b}}" style="{{'width:' + '70rpx' + ';' + ('height:' + '70rpx') + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'center')}}"><image class="data-v-c64ed3ce" src="{{a}}" mode="aspectFit" style="{{'width:' + '40rpx' + ';' + ('height:' + '40rpx')}}"></image></view><text class="header-title data-v-c64ed3ce" style="{{'color:' + '#FFFFFF' + ';' + ('font-size:' + '36rpx') + ';' + ('font-weight:' + '600')}}">个人资料</text><view class="save-btn data-v-c64ed3ce" bindtap="{{c}}" style="{{'padding:' + '10rpx 30rpx' + ';' + ('border-radius:' + '30rpx') + ';' + ('background:' + 'rgba(255,255,255,0.2)') + ';' + ('color:' + '#FFFFFF') + ';' + ('font-size:' + '28rpx') + ';' + ('font-weight:' + '500')}}"> 保存 </view></view></view><view class="avatar-section data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('flex-direction:' + 'column') + ';' + ('align-items:' + 'center') + ';' + ('margin-top:' + '-100rpx') + ';' + ('position:' + 'relative') + ';' + ('z-index:' + '20')}}"><view class="avatar-wrapper data-v-c64ed3ce" bindtap="{{g}}" style="{{'width:' + '180rpx' + ';' + ('height:' + '180rpx') + ';' + ('border-radius:' + '50%') + ';' + ('background:' + '#FFFFFF') + ';' + ('padding:' + '6rpx') + ';' + ('box-shadow:' + '0 5px 15px rgba(0,0,0,0.1)') + ';' + ('position:' + 'relative')}}"><image class="data-v-c64ed3ce" src="{{d}}" mode="aspectFill" style="{{'width:' + '100%' + ';' + ('height:' + '100%') + ';' + ('border-radius:' + '50%')}}"></image><view class="edit-icon data-v-c64ed3ce" style="{{'position:' + 'absolute' + ';' + ('bottom:' + '10rpx') + ';' + ('right:' + '10rpx') + ';' + ('width:' + '50rpx') + ';' + ('height:' + '50rpx') + ';' + ('border-radius:' + '50%') + ';' + ('background:' + '#FF3B69') + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'center') + ';' + ('box-shadow:' + '0 2px 5px rgba(255,59,105,0.3)') + ';' + ('border:' + '2rpx solid #FFFFFF')}}"><svg wx:if="{{f}}" u-s="{{['d']}}" class="icon data-v-c64ed3ce" u-i="c64ed3ce-0" bind:__l="__l" u-p="{{f}}"><path wx:if="{{e}}" class="data-v-c64ed3ce" u-i="c64ed3ce-1,c64ed3ce-0" bind:__l="__l" u-p="{{e}}"></path></svg></view></view><view wx:if="{{h}}" class="vip-badge data-v-c64ed3ce" style="{{'margin-top:' + '20rpx' + ';' + ('padding:' + '6rpx 20rpx') + ';' + ('background:' + 'linear-gradient(90deg, #FFD700 0%, #FFC107 100%)') + ';' + ('border-radius:' + '20rpx') + ';' + ('color:' + '#8B4513') + ';' + ('font-size:' + '24rpx') + ';' + ('font-weight:' + 'bold') + ';' + ('box-shadow:' + '0 2px 5px rgba(255,215,0,0.3)')}}"> VIP会员 </view></view><view class="profile-form data-v-c64ed3ce" style="{{'padding:' + '30rpx' + ';' + ('margin-top:' + '30rpx')}}"><view class="info-card data-v-c64ed3ce" style="{{'background:' + '#FFFFFF' + ';' + ('border-radius:' + '35px') + ';' + ('padding:' + '30rpx') + ';' + ('box-shadow:' + '0 8px 20px rgba(0,0,0,0.08)') + ';' + ('margin-bottom:' + '30rpx')}}"><view class="card-header data-v-c64ed3ce" style="{{'margin-bottom:' + '30rpx'}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '32rpx' + ';' + ('font-weight:' + '600') + ';' + ('color:' + '#333333')}}">基本信息</text></view><view class="form-item data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0') + ';' + ('border-bottom:' + '1rpx solid #EFEFEF')}}"><text class="form-label data-v-c64ed3ce" style="{{'width:' + '160rpx' + ';' + ('font-size:' + '28rpx') + ';' + ('color:' + '#666666')}}">昵称</text><input class="data-v-c64ed3ce" type="text" placeholder="请输入昵称" style="{{'flex:' + '1' + ';' + ('font-size:' + '28rpx') + ';' + ('color:' + '#333333')}}" value="{{i}}" bindinput="{{j}}"/></view><view class="form-item data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0') + ';' + ('border-bottom:' + '1rpx solid #EFEFEF')}}"><text class="form-label data-v-c64ed3ce" style="{{'width:' + '160rpx' + ';' + ('font-size:' + '28rpx') + ';' + ('color:' + '#666666')}}">性别</text><view class="gender-options data-v-c64ed3ce" style="{{'flex:' + '1' + ';' + ('display:' + 'flex')}}"><view class="{{['gender-option', 'data-v-c64ed3ce', k && 'active']}}" bindtap="{{l}}" style="{{'padding:' + '10rpx 30rpx' + ';' + ('border-radius:' + '30rpx') + ';' + ('margin-right:' + '20rpx') + ';' + ('background:' + m) + ';' + ('color:' + n) + ';' + ('font-size:' + '26rpx') + ';' + ('border:' + o)}}"> 男 </view><view class="{{['gender-option', 'data-v-c64ed3ce', p && 'active']}}" bindtap="{{q}}" style="{{'padding:' + '10rpx 30rpx' + ';' + ('border-radius:' + '30rpx') + ';' + ('background:' + r) + ';' + ('color:' + s) + ';' + ('font-size:' + '26rpx') + ';' + ('border:' + t)}}"> 女 </view></view></view><view class="form-item data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0') + ';' + ('border-bottom:' + '1rpx solid #EFEFEF')}}"><text class="form-label data-v-c64ed3ce" style="{{'width:' + '160rpx' + ';' + ('font-size:' + '28rpx') + ';' + ('color:' + '#666666')}}">生日</text><view class="date-picker data-v-c64ed3ce" bindtap="{{z}}" style="{{'flex:' + '1' + ';' + ('display:' + 'flex') + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '28rpx' + ';' + ('color:' + w)}}">{{v}}</text><svg wx:if="{{y}}" u-s="{{['d']}}" class="icon data-v-c64ed3ce" u-i="c64ed3ce-2" bind:__l="__l" u-p="{{y}}"><path wx:if="{{x}}" class="data-v-c64ed3ce" u-i="c64ed3ce-3,c64ed3ce-2" bind:__l="__l" u-p="{{x}}"></path></svg></view></view><view class="form-item data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0') + ';' + ('border-bottom:' + '1rpx solid #EFEFEF')}}"><text class="form-label data-v-c64ed3ce" style="{{'width:' + '160rpx' + ';' + ('font-size:' + '28rpx') + ';' + ('color:' + '#666666')}}">手机号</text><text wx:if="{{A}}" class="data-v-c64ed3ce" style="{{'flex:' + '1' + ';' + ('font-size:' + '28rpx') + ';' + ('color:' + '#333333')}}">{{B}}</text><view wx:else class="bind-btn data-v-c64ed3ce" bindtap="{{C}}" style="{{'padding:' + '8rpx 20rpx' + ';' + ('border-radius:' + '30rpx') + ';' + ('background:' + 'rgba(255,59,105,0.1)') + ';' + ('color:' + '#FF3B69') + ';' + ('font-size:' + '24rpx') + ';' + ('font-weight:' + '500')}}"> 绑定手机号 </view></view><view class="form-item data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0')}}"><text class="form-label data-v-c64ed3ce" style="{{'width:' + '160rpx' + ';' + ('font-size:' + '28rpx') + ';' + ('color:' + '#666666')}}">所在地区</text><view class="region-picker data-v-c64ed3ce" bindtap="{{H}}" style="{{'flex:' + '1' + ';' + ('display:' + 'flex') + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '28rpx' + ';' + ('color:' + E)}}">{{D}}</text><svg wx:if="{{G}}" u-s="{{['d']}}" class="icon data-v-c64ed3ce" u-i="c64ed3ce-4" bind:__l="__l" u-p="{{G}}"><path wx:if="{{F}}" class="data-v-c64ed3ce" u-i="c64ed3ce-5,c64ed3ce-4" bind:__l="__l" u-p="{{F}}"></path></svg></view></view></view><view class="security-card data-v-c64ed3ce" style="{{'background:' + '#FFFFFF' + ';' + ('border-radius:' + '35px') + ';' + ('padding:' + '30rpx') + ';' + ('box-shadow:' + '0 8px 20px rgba(0,0,0,0.08)') + ';' + ('margin-bottom:' + '30rpx')}}"><view class="card-header data-v-c64ed3ce" style="{{'margin-bottom:' + '30rpx'}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '32rpx' + ';' + ('font-weight:' + '600') + ';' + ('color:' + '#333333')}}">账号安全</text></view><view class="security-item data-v-c64ed3ce" bindtap="{{K}}" style="{{'display:' + 'flex' + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0') + ';' + ('border-bottom:' + '1rpx solid #EFEFEF')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '28rpx' + ';' + ('color:' + '#333333')}}">修改密码</text><svg wx:if="{{J}}" u-s="{{['d']}}" class="icon data-v-c64ed3ce" u-i="c64ed3ce-6" bind:__l="__l" u-p="{{J}}"><path wx:if="{{I}}" class="data-v-c64ed3ce" u-i="c64ed3ce-7,c64ed3ce-6" bind:__l="__l" u-p="{{I}}"></path></svg></view><view class="security-item data-v-c64ed3ce" bindtap="{{P}}" style="{{'display:' + 'flex' + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0') + ';' + ('border-bottom:' + '1rpx solid #EFEFEF')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '28rpx' + ';' + ('color:' + '#333333')}}">实名认证</text><view class="security-status data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '26rpx' + ';' + ('color:' + M) + ';' + ('margin-right:' + '10rpx')}}">{{L}}</text><svg wx:if="{{O}}" u-s="{{['d']}}" class="icon data-v-c64ed3ce" u-i="c64ed3ce-8" bind:__l="__l" u-p="{{O}}"><path wx:if="{{N}}" class="data-v-c64ed3ce" u-i="c64ed3ce-9,c64ed3ce-8" bind:__l="__l" u-p="{{N}}"></path></svg></view></view><view class="security-item data-v-c64ed3ce" bindtap="{{U}}" style="{{'display:' + 'flex' + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0') + ';' + ('border-bottom:' + '1rpx solid #EFEFEF')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '28rpx' + ';' + ('color:' + '#333333')}}">绑定微信</text><view class="security-status data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('align-items:' + 'center')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '26rpx' + ';' + ('color:' + R) + ';' + ('margin-right:' + '10rpx')}}">{{Q}}</text><svg wx:if="{{T}}" u-s="{{['d']}}" class="icon data-v-c64ed3ce" u-i="c64ed3ce-10" bind:__l="__l" u-p="{{T}}"><path wx:if="{{S}}" class="data-v-c64ed3ce" u-i="c64ed3ce-11,c64ed3ce-10" bind:__l="__l" u-p="{{S}}"></path></svg></view></view><view class="security-item data-v-c64ed3ce" bindtap="{{X}}" style="{{'display:' + 'flex' + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '28rpx' + ';' + ('color:' + '#FF3B30')}}">注销账号</text><svg wx:if="{{W}}" u-s="{{['d']}}" class="icon data-v-c64ed3ce" u-i="c64ed3ce-12" bind:__l="__l" u-p="{{W}}"><path wx:if="{{V}}" class="data-v-c64ed3ce" u-i="c64ed3ce-13,c64ed3ce-12" bind:__l="__l" u-p="{{V}}"></path></svg></view></view><view class="privacy-card data-v-c64ed3ce" style="{{'background:' + '#FFFFFF' + ';' + ('border-radius:' + '35px') + ';' + ('padding:' + '30rpx') + ';' + ('box-shadow:' + '0 8px 20px rgba(0,0,0,0.08)') + ';' + ('margin-bottom:' + '30rpx')}}"><view class="card-header data-v-c64ed3ce" style="{{'margin-bottom:' + '30rpx'}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '32rpx' + ';' + ('font-weight:' + '600') + ';' + ('color:' + '#333333')}}">隐私设置</text></view><view class="privacy-item data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0') + ';' + ('border-bottom:' + '1rpx solid #EFEFEF')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '28rpx' + ';' + ('color:' + '#333333')}}">允许陌生人查看资料</text><switch class="data-v-c64ed3ce" checked="{{Y}}" bindchange="{{Z}}" color="#FF3B69"/></view><view class="privacy-item data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0') + ';' + ('border-bottom:' + '1rpx solid #EFEFEF')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '28rpx' + ';' + ('color:' + '#333333')}}">显示我的活动记录</text><switch class="data-v-c64ed3ce" checked="{{aa}}" bindchange="{{ab}}" color="#FF3B69"/></view><view class="privacy-item data-v-c64ed3ce" style="{{'display:' + 'flex' + ';' + ('justify-content:' + 'space-between') + ';' + ('align-items:' + 'center') + ';' + ('padding:' + '20rpx 0')}}"><text class="data-v-c64ed3ce" style="{{'font-size:' + '28rpx' + ';' + ('color:' + '#333333')}}">接收活动推送</text><switch class="data-v-c64ed3ce" checked="{{ac}}" bindchange="{{ad}}" color="#FF3B69"/></view></view></view><view class="safe-area-bottom data-v-c64ed3ce" style="{{'height:' + '100rpx' + ';' + ('padding-bottom:' + 'env(safe-area-inset-bottom)')}}"></view></view>