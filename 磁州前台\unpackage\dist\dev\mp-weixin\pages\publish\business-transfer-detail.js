"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const mock_api = require("../../mock/api.js");
if (!Math) {
  ReportCard();
}
const ReportCard = () => "../../components/ReportCard.js";
const _sfc_main = {
  __name: "business-transfer-detail",
  setup(__props) {
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const statusBarHeight = common_vendor.ref(20);
    const isCollected = common_vendor.ref(false);
    const transferData = common_vendor.ref({});
    const relatedShops = common_vendor.ref([]);
    const loadTransferDetail = (id) => {
      common_vendor.index.showLoading({
        title: "加载中"
      });
      mock_api.default.publish.getBusinessTransferDetail(id).then((data) => {
        transferData.value = data;
        common_vendor.index.hideLoading();
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "获取详情失败",
          icon: "none"
        });
        common_vendor.index.__f__("error", "at pages/publish/business-transfer-detail.vue:271", "获取详情失败:", err);
      });
    };
    const loadRelatedShops = () => {
      mock_api.default.publish.getRelatedShops().then((data) => {
        relatedShops.value = data;
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/publish/business-transfer-detail.vue:281", "获取相关店铺失败:", err);
      });
    };
    const navigateToShopDetail = (id) => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (id === options.id) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/business/shop-detail?id=${id}`
      });
    };
    const navigateToTransferList = (e) => {
      var _a;
      if (e)
        e.stopPropagation();
      const transferCategory = ((_a = transferData.value.tags) == null ? void 0 : _a[0]) || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=business-transfer&title=${encodeURIComponent("生意转让")}&category=${encodeURIComponent(transferCategory)}&active=business`
      });
    };
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: transferData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const openChat = () => {
      if (!transferData.value.publisher || !transferData.value.publisher.id) {
        common_vendor.index.showToast({
          title: "无法获取发布者信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/message/chat?userId=${transferData.value.publisher.id}&userName=${transferData.value.publisher.name}`
      });
    };
    const generateShareImage = () => {
      common_vendor.index.showLoading({
        title: "正在生成海报...",
        mask: true
      });
      const posterData = {
        title: transferData.value.title,
        price: transferData.value.price,
        type: transferData.value.type,
        area: transferData.value.area,
        address: transferData.value.address,
        phone: transferData.value.contact.phone,
        description: transferData.value.description ? transferData.value.description.substring(0, 60) + "..." : "",
        qrcode: "/static/images/tabbar/客服微信.png",
        logo: "/static/images/tabbar/生意转让.png",
        bgImage: transferData.value.images[0] || "/static/images/banner/banner-1.png"
      };
      const ctx = common_vendor.index.createCanvasContext("posterCanvas");
      ctx.save();
      ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
      ctx.setFillStyle("rgba(0, 0, 0, 0.35)");
      ctx.fillRect(0, 0, 600, 900);
      ctx.restore();
      ctx.save();
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(30, 280, 540, 550);
      ctx.restore();
      ctx.save();
      ctx.beginPath();
      ctx.arc(300, 200, 80, 0, 2 * Math.PI);
      ctx.setFillStyle("#ffffff");
      ctx.fill();
      ctx.clip();
      ctx.drawImage(posterData.logo, 220, 120, 160, 160);
      ctx.restore();
      ctx.setFillStyle("#333333");
      ctx.setFontSize(32);
      ctx.setTextAlign("center");
      ctx.fillText(posterData.title, 300, 350);
      ctx.setFillStyle("#FF6B6B");
      ctx.setFontSize(28);
      ctx.fillText(posterData.price, 300, 400);
      ctx.beginPath();
      ctx.setStrokeStyle("#eeeeee");
      ctx.setLineWidth(2);
      ctx.moveTo(100, 430);
      ctx.lineTo(500, 430);
      ctx.stroke();
      ctx.setFillStyle("#666666");
      ctx.setFontSize(24);
      ctx.setTextAlign("left");
      ctx.fillText("商铺类型: " + posterData.type, 80, 480);
      ctx.fillText("面积: " + posterData.area, 80, 520);
      ctx.fillText("位置: " + posterData.address, 80, 560);
      const wrapText = (ctx2, text, x, y, maxWidth, lineHeight) => {
        if (text.length === 0)
          return;
        const words = text.split("");
        let line = "";
        let testLine = "";
        let lineCount = 0;
        for (let n = 0; n < words.length; n++) {
          testLine += words[n];
          const metrics = ctx2.measureText(testLine);
          const testWidth = metrics.width;
          if (testWidth > maxWidth && n > 0) {
            ctx2.fillText(line, x, y + lineCount * lineHeight);
            line = words[n];
            testLine = words[n];
            lineCount++;
            if (lineCount >= 2) {
              line += "...";
              ctx2.fillText(line, x, y + lineCount * lineHeight);
              break;
            }
          } else {
            line = testLine;
          }
        }
        if (lineCount < 2) {
          ctx2.fillText(line, x, y + lineCount * lineHeight);
        }
      };
      ctx.setFillStyle("#666666");
      ctx.fillText("描述:", 80, 600);
      wrapText(ctx, posterData.description, 80, 630, 440, 35);
      if (posterData.phone) {
        ctx.fillText("联系电话: " + posterData.phone, 80, 680);
      }
      ctx.drawImage(posterData.qrcode, 230, 700, 140, 140);
      ctx.setFillStyle("#999999");
      ctx.setFontSize(20);
      ctx.setTextAlign("center");
      ctx.fillText("长按识别小程序码查看详情", 300, 870);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "posterCanvas",
            success: (res) => {
              posterImagePath.value = res.tempFilePath;
              showPosterFlag.value = true;
              common_vendor.index.hideLoading();
              common_vendor.index.showModal({
                title: "提示",
                content: "海报已生成，是否保存到相册？",
                confirmText: "保存",
                success: (res2) => {
                  if (res2.confirm) {
                    saveImageToAlbum();
                  }
                }
              });
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/publish/business-transfer-detail.vue:530", "生成海报失败:", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "生成海报失败",
                icon: "none"
              });
            }
          });
        }, 500);
      });
    };
    const saveImageToAlbum = () => {
      if (!posterImagePath.value) {
        common_vendor.index.showToast({
          title: "海报未生成",
          icon: "none"
        });
        return;
      }
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: posterImagePath.value,
        success: () => {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "none"
          });
        }
      });
    };
    common_vendor.onMounted(() => {
      var _a;
      common_vendor.index.getSystemInfo({
        success: (res) => {
          statusBarHeight.value = res.statusBarHeight;
        }
      });
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (options.id) {
        loadTransferDetail(options.id);
      } else {
        loadTransferDetail("transfer12345");
      }
      loadRelatedShops();
    });
    const posterImagePath = common_vendor.ref("");
    const showPosterFlag = common_vendor.ref(false);
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_assets._imports_10,
        e: common_vendor.o(generateShareImage),
        f: common_vendor.t(transferData.value.title),
        g: common_vendor.t(transferData.value.price),
        h: common_vendor.f(transferData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        i: common_vendor.t(formatTime(transferData.value.publishTime)),
        j: common_vendor.f(transferData.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        k: common_vendor.t(transferData.value.type),
        l: common_vendor.t(transferData.value.area),
        m: common_vendor.t(transferData.value.years),
        n: common_vendor.t(transferData.value.size),
        o: common_vendor.f(transferData.value.details, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        p: common_vendor.t(transferData.value.monthlyRevenue),
        q: common_vendor.t(transferData.value.monthlyProfit),
        r: common_vendor.t(transferData.value.customerFlow),
        s: common_vendor.t(transferData.value.employeeCount),
        t: transferData.value.notes,
        v: common_vendor.t(transferData.value.reason),
        w: transferData.value.publisher.avatar,
        x: common_vendor.t(transferData.value.publisher.name),
        y: common_vendor.t(transferData.value.publisher.type),
        z: common_vendor.t(transferData.value.publisher.rating),
        A: transferData.value.publisher.isVerified
      }, transferData.value.publisher.isVerified ? {} : {}, {
        B: common_vendor.t(transferData.value.contact.name),
        C: common_vendor.t(transferData.value.contact.phone),
        D: common_vendor.o(callPhone),
        E: common_vendor.p({
          ["content-id"]: transferData.value.id,
          ["content-type"]: "business-transfer"
        }),
        F: common_vendor.f(relatedShops.value.slice(0, 3), (shop, index, i0) => {
          return common_vendor.e({
            a: shop.image,
            b: common_vendor.t(shop.title),
            c: common_vendor.t(shop.area),
            d: common_vendor.f(shop.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            e: shop.tags.length > 2
          }, shop.tags.length > 2 ? {
            f: common_vendor.t(shop.tags.length - 2)
          } : {}, {
            g: common_vendor.t(shop.price),
            h: index,
            i: common_vendor.o(($event) => navigateToShopDetail(shop.id), index)
          });
        }),
        G: relatedShops.value.length === 0
      }, relatedShops.value.length === 0 ? {
        H: common_assets._imports_1$3
      } : {}, {
        I: relatedShops.value.length > 0
      }, relatedShops.value.length > 0 ? {
        J: common_vendor.o(navigateToTransferList)
      } : {}, {
        K: common_assets._imports_12,
        L: common_vendor.o(goToHome),
        M: common_assets._imports_3$2,
        N: common_vendor.o(toggleCollect),
        O: common_assets._imports_3$3,
        P: common_assets._imports_14,
        Q: common_vendor.o(openChat),
        R: common_vendor.o(callPhone)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d3c0eed0"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/business-transfer-detail.js.map
