{"version": 3, "file": "rewardedAdService.js", "sources": ["services/rewardedAdService.js"], "sourcesContent": ["/**\n * 微信小程序激励广告服务\n * 处理激励视频广告的展示和奖励发放\n */\n\nimport request from '@/utils/request';\n\nclass RewardedAdService {\n  constructor() {\n    this.videoAd = null;\n    this.currentRewardType = null;\n    this.currentCarpoolId = null;\n    this.currentMerchantId = null;\n    this.currentInfoId = null;\n    this.adUnitId = 'adunit-9699c56c26082b54'; // 您的真实广告单元ID\n  }\n\n  /**\n   * 初始化激励视频广告\n   */\n  initRewardedVideoAd() {\n    // 检查微信小程序环境\n    if (typeof wx === 'undefined' || !wx.createRewardedVideoAd) {\n      console.warn('当前环境不支持激励视频广告');\n      return false;\n    }\n\n    try {\n      this.videoAd = wx.createRewardedVideoAd({\n        adUnitId: this.adUnitId\n      });\n\n      // 监听广告加载成功\n      this.videoAd.onLoad(() => {\n        console.log('激励视频广告加载成功');\n      });\n\n      // 监听广告加载失败\n      this.videoAd.onError((err) => {\n        console.error('激励视频广告加载失败', err);\n        uni.showToast({\n          title: '广告加载失败',\n          icon: 'none'\n        });\n      });\n\n      // 监听广告关闭\n      this.videoAd.onClose((res) => {\n        if (res && res.isEnded || res === undefined) {\n          // 广告播放完成，发放奖励\n          console.log('激励视频广告完成，发放奖励');\n          this.grantReward();\n        } else {\n          // 播放中途退出，不发放奖励\n          console.log('激励视频广告未完成');\n          uni.showToast({\n            title: '请观看完整广告',\n            icon: 'none'\n          });\n        }\n      });\n\n      return true;\n    } catch (error) {\n      console.error('初始化激励视频广告失败', error);\n      return false;\n    }\n  }\n\n  /**\n   * 显示激励视频广告\n   * @param {string} rewardType - 奖励类型: 'free_publish', 'free_top', 'free_refresh', 'merchant_renew', 'merchant_join', 'merchant_top', 'merchant_refresh'\n   * @param {number} carpoolId - 拼车信息ID（置顶和刷新时需要）\n   * @param {number} merchantId - 商家ID（商家相关功能时需要）\n   * @param {number} infoId - 信息ID（商家信息置顶和刷新时需要）\n   */\n  async showRewardedVideoAd(rewardType, carpoolId = null, merchantId = null, infoId = null) {\n    this.currentRewardType = rewardType;\n    this.currentCarpoolId = carpoolId;\n    this.currentMerchantId = merchantId;\n    this.currentInfoId = infoId;\n\n    // 检查是否已初始化\n    if (!this.videoAd) {\n      const initSuccess = this.initRewardedVideoAd();\n      if (!initSuccess) {\n        uni.showToast({\n          title: '广告功能不可用',\n          icon: 'none'\n        });\n        return false;\n      }\n    }\n\n    try {\n      // 显示广告\n      await this.videoAd.show();\n      return true;\n    } catch (error) {\n      console.error('显示激励视频广告失败', error);\n      \n      // 失败重试\n      try {\n        await this.videoAd.load();\n        await this.videoAd.show();\n        return true;\n      } catch (retryError) {\n        console.error('重试显示激励视频广告失败', retryError);\n        uni.showToast({\n          title: '广告显示失败',\n          icon: 'none'\n        });\n        return false;\n      }\n    }\n  }\n\n  /**\n   * 发放奖励\n   */\n  async grantReward() {\n    try {\n      // 获取用户信息\n      const userInfo = uni.getStorageSync('userInfo');\n      if (!userInfo || !userInfo.id) {\n        uni.showToast({\n          title: '请先登录',\n          icon: 'none'\n        });\n        return;\n      }\n\n      // 构建请求数据\n      const requestData = {\n        reward_type: this.currentRewardType,\n        user_id: userInfo.id,\n        ad_unit_id: this.adUnitId,\n        timestamp: Date.now()\n      };\n\n      // 如果是置顶或刷新，需要传递拼车信息ID\n      if (this.currentCarpoolId) {\n        requestData.carpool_id = this.currentCarpoolId;\n      }\n\n      // 如果是商家相关功能，需要传递商家ID\n      if (this.currentMerchantId) {\n        requestData.merchant_id = this.currentMerchantId;\n      }\n\n      // 如果是商家信息置顶或刷新，需要传递信息ID\n      if (this.currentInfoId) {\n        requestData.info_id = this.currentInfoId;\n      }\n\n      // 根据奖励类型调用不同的API\n      let apiUrl = '/api/carpool/ads/reward';\n      if (this.currentRewardType === 'merchant_renew') {\n        apiUrl = '/api/merchant/ads/renew';\n      } else if (this.currentRewardType === 'merchant_join') {\n        apiUrl = '/api/merchant/ads/join';\n      } else if (this.currentRewardType === 'merchant_top') {\n        apiUrl = '/api/merchant/ads/top';\n      } else if (this.currentRewardType === 'merchant_refresh') {\n        apiUrl = '/api/merchant/ads/refresh';\n      }\n\n      // 调用后台API发放奖励\n      const response = await request.post(apiUrl, requestData);\n\n      if (response.data && response.data.success) {\n        // 奖励发放成功\n        const rewardData = response.data.data;\n        this.showRewardSuccess(rewardData);\n      } else {\n        // 奖励发放失败\n        uni.showToast({\n          title: response.data?.message || '奖励发放失败',\n          icon: 'none'\n        });\n      }\n\n    } catch (error) {\n      console.error('发放奖励失败', error);\n      uni.showToast({\n        title: '网络错误，请重试',\n        icon: 'none'\n      });\n    }\n  }\n\n  /**\n   * 显示奖励成功提示\n   * @param {Object} rewardData - 奖励数据\n   */\n  showRewardSuccess(rewardData) {\n    let message = '奖励发放成功';\n    \n    switch (rewardData.type) {\n      case 'free_publish':\n        message = '获得1次免费发布机会！';\n        break;\n      case 'free_top':\n        message = '置顶2小时成功！';\n        break;\n      case 'free_refresh':\n        message = '刷新成功！';\n        break;\n      case 'merchant_renew':\n        message = `商家入驻续费成功！延长${rewardData.days || 7}天`;\n        break;\n      case 'merchant_join':\n        message = '商家入驻成功！获得1个月免费特权';\n        break;\n      case 'merchant_top':\n        message = '商家信息置顶成功！置顶2小时';\n        break;\n      case 'merchant_refresh':\n        message = '商家信息刷新成功！';\n        break;\n    }\n\n    uni.showToast({\n      title: message,\n      icon: 'success',\n      duration: 2000\n    });\n\n    // 触发页面刷新事件\n    uni.$emit('rewardGranted', {\n      type: rewardData.type,\n      data: rewardData\n    });\n  }\n\n  /**\n   * 检查今日观看次数\n   * @param {string} adType - 广告类型\n   */\n  async checkTodayWatchCount(adType) {\n    try {\n      const userInfo = uni.getStorageSync('userInfo');\n      if (!userInfo || !userInfo.id) {\n        return { canWatch: false, message: '请先登录' };\n      }\n\n      const response = await request.get('/api/carpool/ads/stats', {\n        params: {\n          user_id: userInfo.id,\n          ad_type: adType,\n          date: new Date().toISOString().split('T')[0]\n        }\n      });\n\n      const stats = response.data;\n      const dailyLimit = this.getDailyLimit(adType);\n\n      if (stats.today_views >= dailyLimit) {\n        return {\n          canWatch: false,\n          message: `今日观看次数已达上限（${dailyLimit}次）`\n        };\n      }\n\n      return {\n        canWatch: true,\n        remaining: dailyLimit - stats.today_views\n      };\n\n    } catch (error) {\n      console.error('检查观看次数失败', error);\n      return { canWatch: true }; // 出错时允许观看\n    }\n  }\n\n  /**\n   * 获取每日观看限制\n   * @param {string} adType - 广告类型\n   */\n  getDailyLimit(adType) {\n    const limits = {\n      'publish': 5,\n      'top': 3,\n      'refresh': 5,\n      'merchant_renew': 2,    // 商家续费每月限制2次\n      'merchant_join': 1,     // 商家入驻每月限制1次\n      'merchant_top': 3,      // 商家置顶每日限制3次\n      'merchant_refresh': 5   // 商家刷新每日限制5次\n    };\n    return limits[adType] || 3;\n  }\n\n  /**\n   * 销毁广告实例\n   */\n  destroy() {\n    if (this.videoAd) {\n      this.videoAd.destroy();\n      this.videoAd = null;\n    }\n  }\n}\n\n// 创建单例实例\nconst rewardedAdService = new RewardedAdService();\n\nexport default rewardedAdService;\n"], "names": ["wx", "uni", "request"], "mappings": ";;;AAOA,MAAM,kBAAkB;AAAA,EACtB,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKD,sBAAsB;AAEpB,QAAI,OAAOA,cAAAA,SAAO,eAAe,CAACA,cAAAA,KAAG,uBAAuB;AAC1DC,oBAAAA,MAAa,MAAA,QAAA,uCAAA,eAAe;AAC5B,aAAO;AAAA,IACR;AAED,QAAI;AACF,WAAK,UAAUD,cAAE,KAAC,sBAAsB;AAAA,QACtC,UAAU,KAAK;AAAA,MACvB,CAAO;AAGD,WAAK,QAAQ,OAAO,MAAM;AACxBC,sBAAAA,0DAAY,YAAY;AAAA,MAChC,CAAO;AAGD,WAAK,QAAQ,QAAQ,CAAC,QAAQ;AAC5BA,sBAAc,MAAA,MAAA,SAAA,uCAAA,cAAc,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AAAA,MACT,CAAO;AAGD,WAAK,QAAQ,QAAQ,CAAC,QAAQ;AAC5B,YAAI,OAAO,IAAI,WAAW,QAAQ,QAAW;AAE3CA,wBAAAA,MAAY,MAAA,OAAA,uCAAA,eAAe;AAC3B,eAAK,YAAW;AAAA,QAC1B,OAAe;AAELA,wBAAAA,MAAY,MAAA,OAAA,uCAAA,WAAW;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AAAA,QACF;AAAA,MACT,CAAO;AAED,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,uCAAA,eAAe,KAAK;AAClC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,MAAM,oBAAoB,YAAY,YAAY,MAAM,aAAa,MAAM,SAAS,MAAM;AACxF,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AAGrB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,cAAc,KAAK;AACzB,UAAI,CAAC,aAAa;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AACD,eAAO;AAAA,MACR;AAAA,IACF;AAED,QAAI;AAEF,YAAM,KAAK,QAAQ;AACnB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,wCAAA,cAAc,KAAK;AAGjC,UAAI;AACF,cAAM,KAAK,QAAQ;AACnB,cAAM,KAAK,QAAQ;AACnB,eAAO;AAAA,MACR,SAAQ,YAAY;AACnBA,sBAAc,MAAA,MAAA,SAAA,wCAAA,gBAAgB,UAAU;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AACD,eAAO;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,cAAc;;AAClB,QAAI;AAEF,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,CAAC,YAAY,CAAC,SAAS,IAAI;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AACD;AAAA,MACD;AAGD,YAAM,cAAc;AAAA,QAClB,aAAa,KAAK;AAAA,QAClB,SAAS,SAAS;AAAA,QAClB,YAAY,KAAK;AAAA,QACjB,WAAW,KAAK,IAAK;AAAA,MAC7B;AAGM,UAAI,KAAK,kBAAkB;AACzB,oBAAY,aAAa,KAAK;AAAA,MAC/B;AAGD,UAAI,KAAK,mBAAmB;AAC1B,oBAAY,cAAc,KAAK;AAAA,MAChC;AAGD,UAAI,KAAK,eAAe;AACtB,oBAAY,UAAU,KAAK;AAAA,MAC5B;AAGD,UAAI,SAAS;AACb,UAAI,KAAK,sBAAsB,kBAAkB;AAC/C,iBAAS;AAAA,MACjB,WAAiB,KAAK,sBAAsB,iBAAiB;AACrD,iBAAS;AAAA,MACjB,WAAiB,KAAK,sBAAsB,gBAAgB;AACpD,iBAAS;AAAA,MACjB,WAAiB,KAAK,sBAAsB,oBAAoB;AACxD,iBAAS;AAAA,MACV;AAGD,YAAM,WAAW,MAAMC,cAAO,QAAC,KAAK,QAAQ,WAAW;AAEvD,UAAI,SAAS,QAAQ,SAAS,KAAK,SAAS;AAE1C,cAAM,aAAa,SAAS,KAAK;AACjC,aAAK,kBAAkB,UAAU;AAAA,MACzC,OAAa;AAELD,sBAAAA,MAAI,UAAU;AAAA,UACZ,SAAO,cAAS,SAAT,mBAAe,YAAW;AAAA,UACjC,MAAM;AAAA,QAChB,CAAS;AAAA,MACF;AAAA,IAEF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,wCAAA,UAAU,KAAK;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAO;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,kBAAkB,YAAY;AAC5B,QAAI,UAAU;AAEd,YAAQ,WAAW,MAAI;AAAA,MACrB,KAAK;AACH,kBAAU;AACV;AAAA,MACF,KAAK;AACH,kBAAU;AACV;AAAA,MACF,KAAK;AACH,kBAAU;AACV;AAAA,MACF,KAAK;AACH,kBAAU,cAAc,WAAW,QAAQ,CAAC;AAC5C;AAAA,MACF,KAAK;AACH,kBAAU;AACV;AAAA,MACF,KAAK;AACH,kBAAU;AACV;AAAA,MACF,KAAK;AACH,kBAAU;AACV;AAAA,IACH;AAEDA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAChB,CAAK;AAGDA,kBAAG,MAAC,MAAM,iBAAiB;AAAA,MACzB,MAAM,WAAW;AAAA,MACjB,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,qBAAqB,QAAQ;AACjC,QAAI;AACF,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,CAAC,YAAY,CAAC,SAAS,IAAI;AAC7B,eAAO,EAAE,UAAU,OAAO,SAAS,OAAM;AAAA,MAC1C;AAED,YAAM,WAAW,MAAMC,sBAAQ,IAAI,0BAA0B;AAAA,QAC3D,QAAQ;AAAA,UACN,SAAS,SAAS;AAAA,UAClB,SAAS;AAAA,UACT,OAAM,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAAA,QAC5C;AAAA,MACT,CAAO;AAED,YAAM,QAAQ,SAAS;AACvB,YAAM,aAAa,KAAK,cAAc,MAAM;AAE5C,UAAI,MAAM,eAAe,YAAY;AACnC,eAAO;AAAA,UACL,UAAU;AAAA,UACV,SAAS,cAAc,UAAU;AAAA,QAC3C;AAAA,MACO;AAED,aAAO;AAAA,QACL,UAAU;AAAA,QACV,WAAW,aAAa,MAAM;AAAA,MACtC;AAAA,IAEK,SAAQ,OAAO;AACdD,iFAAc,YAAY,KAAK;AAC/B,aAAO,EAAE,UAAU;IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,cAAc,QAAQ;AACpB,UAAM,SAAS;AAAA,MACb,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,MACX,kBAAkB;AAAA;AAAA,MAClB,iBAAiB;AAAA;AAAA,MACjB,gBAAgB;AAAA;AAAA,MAChB,oBAAoB;AAAA;AAAA,IAC1B;AACI,WAAO,OAAO,MAAM,KAAK;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU;AACR,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ;AACb,WAAK,UAAU;AAAA,IAChB;AAAA,EACF;AACH;AAGK,MAAC,oBAAoB,IAAI,kBAAiB;;"}