<template>
  <view class="commission-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">佣金明细</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 佣金统计卡片 -->
    <view class="stats-card">
      <view class="stats-item main">
        <text class="item-value">¥{{formatCommission(commissionSummary.total)}}</text>
        <text class="item-label">累计佣金</text>
      </view>
      
      <view class="stats-row">
        <view class="stats-item">
          <text class="item-value">¥{{formatCommission(commissionSummary.pending)}}</text>
          <text class="item-label">待结算</text>
        </view>
        
        <view class="stats-item">
          <text class="item-value">¥{{formatCommission(commissionSummary.settled)}}</text>
          <text class="item-label">可提现</text>
        </view>
        
        <view class="stats-item">
          <text class="item-value">¥{{formatCommission(commissionSummary.withdrawn)}}</text>
          <text class="item-label">已提现</text>
        </view>
      </view>
      
      <view class="stats-footer">
        <button class="withdraw-btn" @click="navigateTo('/subPackages/distribution/pages/withdraw')">立即提现</button>
      </view>
    </view>
    
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="tab-item" 
        :class="{ 'active': activeTab === tab.value }"
        @click="switchTab(tab.value)"
      >
        <text>{{tab.name}}</text>
      </view>
    </view>
    
    <!-- 佣金列表 -->
    <view class="commission-list" v-if="commissionRecords.length > 0">
      <view 
        v-for="(record, index) in commissionRecords" 
        :key="index" 
        class="commission-item"
      >
        <view class="commission-info">
          <view class="commission-header">
            <text class="commission-title">{{record.productName}}</text>
            <text class="commission-amount" :class="getStatusClass(record.status)">{{getStatusPrefix(record.status)}}¥{{formatCommission(record.amount)}}</text>
          </view>
          
          <view class="commission-detail">
            <text class="commission-order">订单号: {{record.orderId}}</text>
            <text class="commission-level">{{getLevelText(record.level)}}佣金</text>
          </view>
          
          <view class="commission-footer">
            <text class="commission-time">{{formatTime(record.createdAt)}}</text>
            <text class="commission-status">{{getStatusText(record.status)}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-image" src="/static/images/empty-data.png" mode="aspectFit"></image>
      <text class="empty-text">暂无佣金记录</text>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMoreData && commissionRecords.length > 0">
      <text v-if="loading">加载中...</text>
      <text v-else @click="loadMore">点击加载更多</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 选项卡
const tabs = [
  { name: '全部', value: 'all' },
  { name: '待结算', value: 'pending' },
  { name: '可提现', value: 'settled' },
  { name: '已提现', value: 'withdrawn' }
];

// 当前选中的选项卡
const activeTab = ref('all');

// 佣金记录
const commissionRecords = ref([]);

// 佣金统计
const commissionSummary = reactive({
  total: 0,
  pending: 0,
  settled: 0,
  withdrawn: 0
});

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 是否有更多数据
const hasMoreData = ref(false);

// 是否正在加载
const loading = ref(false);

// 页面加载
onMounted(async () => {
  // 获取佣金记录
  await getCommissionRecords();
});

// 获取佣金记录
const getCommissionRecords = async (loadMore = false) => {
  if (loading.value) return;
  
  try {
    loading.value = true;
    
    const page = loadMore ? pagination.page + 1 : 1;
    
    const result = await distributionService.getCommissionRecords({
      page,
      pageSize: pagination.pageSize,
      status: activeTab.value
    });
    
    if (result) {
      // 更新佣金记录
      if (loadMore) {
        commissionRecords.value = [...commissionRecords.value, ...result.list];
      } else {
        commissionRecords.value = result.list;
      }
      
      // 更新分页信息
      pagination.page = page;
      pagination.total = result.pagination.total;
      pagination.totalPages = result.pagination.totalPages;
      
      // 更新是否有更多数据
      hasMoreData.value = pagination.page < pagination.totalPages;
      
      // 更新佣金统计
      if (result.summary) {
        Object.assign(commissionSummary, result.summary);
      }
    }
  } catch (error) {
    console.error('获取佣金记录失败', error);
    uni.showToast({
      title: '获取佣金记录失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 切换选项卡
const switchTab = (tab) => {
  if (activeTab.value === tab) return;
  
  activeTab.value = tab;
  getCommissionRecords();
};

// 加载更多
const loadMore = () => {
  if (hasMoreData.value && !loading.value) {
    getCommissionRecords(true);
  }
};

// 格式化佣金
const formatCommission = (amount) => {
  return distributionService.formatCommission(amount);
};

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  
  const date = new Date(time);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'pending':
      return '待结算';
    case 'settled':
      return '可提现';
    case 'withdrawn':
      return '已提现';
    default:
      return '未知状态';
  }
};

// 获取状态前缀
const getStatusPrefix = (status) => {
  return status === 'pending' ? '+预计 ' : '+';
};

// 获取状态样式类
const getStatusClass = (status) => {
  return {
    'pending': 'status-pending',
    'settled': 'status-settled',
    'withdrawn': 'status-withdrawn'
  }[status] || '';
};

// 获取等级文本
const getLevelText = (level) => {
  switch (level) {
    case 1:
      return '一级';
    case 2:
      return '二级';
    case 3:
      return '三级';
    default:
      return '';
  }
};

// 页面导航
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '佣金明细帮助',
    content: '佣金明细页面显示您的所有佣金记录，包括待结算、可提现和已提现的佣金。待结算佣金需要等待订单完成后才能提现。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.commission-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 佣金统计卡片 */
.stats-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.stats-item.main {
  margin-bottom: 30rpx;
}

.stats-row {
  display: flex;
  justify-content: space-between;
}

.item-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-item.main .item-value {
  font-size: 48rpx;
  color: #6B0FBE;
}

.item-label {
  font-size: 24rpx;
  color: #666;
}

.stats-footer {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}

.withdraw-btn {
  background: linear-gradient(135deg, #FF9500, #FF5722);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 12rpx 60rpx;
  line-height: 1.5;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background: #FFFFFF;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #6B0FBE;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #6B0FBE;
  border-radius: 2rpx;
}

/* 佣金列表 */
.commission-list {
  margin: 0 30rpx;
}

.commission-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.commission-info {
  padding: 30rpx;
}

.commission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.commission-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.commission-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF5722;
}

.commission-amount.status-pending {
  color: #FF9500;
}

.commission-amount.status-settled {
  color: #6B0FBE;
}

.commission-amount.status-withdrawn {
  color: #999;
}

.commission-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.commission-order {
  font-size: 24rpx;
  color: #666;
}

.commission-level {
  font-size: 24rpx;
  color: #6B0FBE;
  background-color: rgba(107, 15, 190, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.commission-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.commission-time {
  font-size: 24rpx;
  color: #999;
}

.commission-status {
  font-size: 24rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #666;
}
</style> 