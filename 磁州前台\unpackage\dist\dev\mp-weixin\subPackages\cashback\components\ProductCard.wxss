/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-be4dc338, html.data-v-be4dc338, #app.data-v-be4dc338, .index-container.data-v-be4dc338 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.product-card.data-v-be4dc338 {
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 12px;
}
.product-card .product-image-container.data-v-be4dc338 {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
}
.product-card .product-image-container .product-image.data-v-be4dc338 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.product-card .product-image-container .discount-tag.data-v-be4dc338 {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF4F4F 100%);
  border-radius: 12px;
}
.product-card .product-image-container .discount-tag text.data-v-be4dc338 {
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 500;
}
.product-card .product-info.data-v-be4dc338 {
  padding: 12px;
}
.product-card .product-platform.data-v-be4dc338 {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}
.product-card .product-platform .platform-icon.data-v-be4dc338 {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.product-card .product-platform .platform-name.data-v-be4dc338 {
  font-size: 12px;
  color: #999999;
}
.product-card .product-title.data-v-be4dc338 {
  margin-bottom: 8px;
}
.product-card .product-title text.data-v-be4dc338 {
  font-size: 14px;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-card .product-price-row.data-v-be4dc338 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.product-card .product-price-row .product-price.data-v-be4dc338 {
  display: flex;
  align-items: baseline;
}
.product-card .product-price-row .product-price .price-symbol.data-v-be4dc338 {
  font-size: 12px;
  color: #FF6B6B;
  margin-right: 2px;
}
.product-card .product-price-row .product-price .price-value.data-v-be4dc338 {
  font-size: 18px;
  font-weight: 600;
  color: #FF6B6B;
}
.product-card .product-price-row .product-cashback.data-v-be4dc338 {
  padding: 2px 6px;
  background-color: rgba(156, 39, 176, 0.1);
  border-radius: 10px;
}
.product-card .product-price-row .product-cashback text.data-v-be4dc338 {
  font-size: 12px;
  color: #9C27B0;
}
.product-card .product-meta.data-v-be4dc338 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.product-card .product-meta .product-sales text.data-v-be4dc338 {
  font-size: 12px;
  color: #999999;
}
.product-card .product-meta .product-favorite.data-v-be4dc338 {
  padding: 6px;
  margin-right: -6px;
}