/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-1a83d72f, html.data-v-1a83d72f, #app.data-v-1a83d72f, .index-container.data-v-1a83d72f {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.countdown-container.data-v-1a83d72f {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16rpx;
}
.countdown-container .countdown-label.data-v-1a83d72f {
  font-size: 24rpx;
  margin-bottom: 8rpx;
}
.countdown-container .countdown-timer.data-v-1a83d72f {
  display: flex;
  align-items: center;
}
.countdown-container .countdown-timer .time-block.data-v-1a83d72f {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.countdown-container .countdown-timer .time-block .time-value.data-v-1a83d72f {
  min-width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 35rpx;
  padding: 0 8rpx;
  transition: transform 0.3s ease;
}
.countdown-container .countdown-timer .time-block .time-value.time-pulse.data-v-1a83d72f {
  animation: pulse-1a83d72f 0.5s ease-in-out;
}
.countdown-container .countdown-timer .time-block .time-unit.data-v-1a83d72f {
  font-size: 20rpx;
  color: #666;
  margin-top: 4rpx;
}
.countdown-container .countdown-timer .time-separator.data-v-1a83d72f {
  margin: 0 4rpx;
  color: #666;
  font-weight: 600;
  font-size: 28rpx;
  padding-bottom: 24rpx;
}
.countdown-flash .countdown-label.data-v-1a83d72f {
  color: #FF3B30;
}
.countdown-flash .time-value.data-v-1a83d72f {
  background: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%) !important;
}
.countdown-group .countdown-label.data-v-1a83d72f {
  color: #34C759;
}
.countdown-group .time-value.data-v-1a83d72f {
  background: linear-gradient(135deg, #4CD964 0%, #34C759 100%) !important;
}
.countdown-discount .countdown-label.data-v-1a83d72f {
  color: #5856D6;
}
.countdown-discount .time-value.data-v-1a83d72f {
  background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%) !important;
}
.countdown-coupon .countdown-label.data-v-1a83d72f {
  color: #FF9500;
}
.countdown-coupon .time-value.data-v-1a83d72f {
  background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%) !important;
}
.countdown-default .countdown-label.data-v-1a83d72f {
  color: #007AFF;
}
.countdown-default .time-value.data-v-1a83d72f {
  background: linear-gradient(135deg, #007AFF 0%, #0A84FF 100%) !important;
}
@keyframes pulse-1a83d72f {
0% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
100% {
    transform: scale(1);
}
}