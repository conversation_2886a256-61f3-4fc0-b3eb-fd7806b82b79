"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 表单数据
      privilegeForm: {
        name: "",
        description: "",
        icon: "",
        type: "discount",
        enabled: true,
        // 折扣特权
        discountValue: "9.5",
        discountScope: "all",
        // 积分特权
        pointsRatio: "1.5",
        // 配送特权
        freeShippingCondition: "none",
        freeShippingAmount: "99",
        // 礼包特权
        giftType: "coupon",
        giftPoints: "100",
        // 客服特权
        serviceType: "priority",
        // 有效期
        validityType: "permanent",
        startDate: "",
        endDate: ""
      },
      // 特权类型
      privilegeTypes: [
        { id: "discount", name: "会员折扣" },
        { id: "points", name: "积分加速" },
        { id: "delivery", name: "免费配送" },
        { id: "gift", name: "礼品赠送" },
        { id: "service", name: "专属客服" }
      ],
      // 会员等级
      memberLevels: [
        {
          id: 1,
          name: "普通会员",
          memberCount: 2156,
          selected: false
        },
        {
          id: 2,
          name: "银卡会员",
          memberCount: 864,
          selected: true
        },
        {
          id: 3,
          name: "金卡会员",
          memberCount: 426,
          selected: true
        },
        {
          id: 4,
          name: "钻石会员",
          memberCount: 116,
          selected: true
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    getTypeName(typeId) {
      const type = this.privilegeTypes.find((item) => item.id === typeId);
      return type ? type.name : "请选择特权类型";
    },
    onTypeChange(e) {
      const index = e.detail.value;
      this.privilegeForm.type = this.privilegeTypes[index].id;
    },
    chooseIcon() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.privilegeForm.icon = res.tempFilePaths[0];
        }
      });
    },
    toggleEnabled(e) {
      this.privilegeForm.enabled = e.detail.value;
    },
    setDiscountScope(scope) {
      this.privilegeForm.discountScope = scope;
    },
    setShippingCondition(condition) {
      this.privilegeForm.freeShippingCondition = condition;
    },
    setGiftType(type) {
      this.privilegeForm.giftType = type;
    },
    setServiceType(type) {
      this.privilegeForm.serviceType = type;
    },
    toggleLevel(level) {
      const index = this.memberLevels.findIndex((item) => item.id === level.id);
      if (index !== -1) {
        this.memberLevels[index].selected = !this.memberLevels[index].selected;
      }
    },
    setValidityType(type) {
      this.privilegeForm.validityType = type;
    },
    onStartDateChange(e) {
      this.privilegeForm.startDate = e.detail.value;
    },
    onEndDateChange(e) {
      this.privilegeForm.endDate = e.detail.value;
    },
    selectItems() {
      common_vendor.index.showToast({
        title: `选择${this.privilegeForm.discountScope === "category" ? "分类" : "商品"}功能开发中`,
        icon: "none"
      });
    },
    selectCoupons() {
      common_vendor.index.showToast({
        title: "选择优惠券功能开发中",
        icon: "none"
      });
    },
    selectProducts() {
      common_vendor.index.showToast({
        title: "选择礼品功能开发中",
        icon: "none"
      });
    },
    validateForm() {
      if (!this.privilegeForm.name) {
        common_vendor.index.showToast({
          title: "请输入特权名称",
          icon: "none"
        });
        return false;
      }
      if (!this.privilegeForm.description) {
        common_vendor.index.showToast({
          title: "请输入特权描述",
          icon: "none"
        });
        return false;
      }
      if (!this.privilegeForm.icon) {
        common_vendor.index.showToast({
          title: "请上传特权图标",
          icon: "none"
        });
        return false;
      }
      const hasSelectedLevel = this.memberLevels.some((level) => level.selected);
      if (!hasSelectedLevel) {
        common_vendor.index.showToast({
          title: "请选择适用会员等级",
          icon: "none"
        });
        return false;
      }
      if (this.privilegeForm.validityType === "fixed") {
        if (!this.privilegeForm.startDate) {
          common_vendor.index.showToast({
            title: "请选择开始日期",
            icon: "none"
          });
          return false;
        }
        if (!this.privilegeForm.endDate) {
          common_vendor.index.showToast({
            title: "请选择结束日期",
            icon: "none"
          });
          return false;
        }
      }
      return true;
    },
    savePrivilege() {
      if (!this.validateForm())
        return;
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "会员特权添加成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.privilegeForm.name,
    c: common_vendor.o(($event) => $data.privilegeForm.name = $event.detail.value),
    d: $data.privilegeForm.description,
    e: common_vendor.o(($event) => $data.privilegeForm.description = $event.detail.value),
    f: $data.privilegeForm.icon
  }, $data.privilegeForm.icon ? {
    g: $data.privilegeForm.icon
  } : {}, {
    h: common_vendor.o((...args) => $options.chooseIcon && $options.chooseIcon(...args)),
    i: common_vendor.t($options.getTypeName($data.privilegeForm.type)),
    j: $data.privilegeTypes,
    k: common_vendor.o((...args) => $options.onTypeChange && $options.onTypeChange(...args)),
    l: $data.privilegeForm.enabled,
    m: common_vendor.o((...args) => $options.toggleEnabled && $options.toggleEnabled(...args)),
    n: $data.privilegeForm.type === "discount"
  }, $data.privilegeForm.type === "discount" ? common_vendor.e({
    o: $data.privilegeForm.discountValue,
    p: common_vendor.o(($event) => $data.privilegeForm.discountValue = $event.detail.value),
    q: $data.privilegeForm.discountScope === "all" ? 1 : "",
    r: common_vendor.o(($event) => $options.setDiscountScope("all")),
    s: $data.privilegeForm.discountScope === "category" ? 1 : "",
    t: common_vendor.o(($event) => $options.setDiscountScope("category")),
    v: $data.privilegeForm.discountScope === "product" ? 1 : "",
    w: common_vendor.o(($event) => $options.setDiscountScope("product")),
    x: $data.privilegeForm.discountScope !== "all"
  }, $data.privilegeForm.discountScope !== "all" ? {
    y: common_vendor.t($data.privilegeForm.discountScope === "category" ? "分类" : "商品"),
    z: common_vendor.o((...args) => $options.selectItems && $options.selectItems(...args))
  } : {}) : {}, {
    A: $data.privilegeForm.type === "points"
  }, $data.privilegeForm.type === "points" ? {
    B: $data.privilegeForm.pointsRatio,
    C: common_vendor.o(($event) => $data.privilegeForm.pointsRatio = $event.detail.value)
  } : {}, {
    D: $data.privilegeForm.type === "delivery"
  }, $data.privilegeForm.type === "delivery" ? common_vendor.e({
    E: $data.privilegeForm.freeShippingCondition === "none" ? 1 : "",
    F: common_vendor.o(($event) => $options.setShippingCondition("none")),
    G: $data.privilegeForm.freeShippingCondition === "amount" ? 1 : "",
    H: common_vendor.o(($event) => $options.setShippingCondition("amount")),
    I: $data.privilegeForm.freeShippingCondition === "amount"
  }, $data.privilegeForm.freeShippingCondition === "amount" ? {
    J: $data.privilegeForm.freeShippingAmount,
    K: common_vendor.o(($event) => $data.privilegeForm.freeShippingAmount = $event.detail.value)
  } : {}) : {}, {
    L: $data.privilegeForm.type === "gift"
  }, $data.privilegeForm.type === "gift" ? common_vendor.e({
    M: $data.privilegeForm.giftType === "coupon" ? 1 : "",
    N: common_vendor.o(($event) => $options.setGiftType("coupon")),
    O: $data.privilegeForm.giftType === "points" ? 1 : "",
    P: common_vendor.o(($event) => $options.setGiftType("points")),
    Q: $data.privilegeForm.giftType === "product" ? 1 : "",
    R: common_vendor.o(($event) => $options.setGiftType("product")),
    S: $data.privilegeForm.giftType === "coupon"
  }, $data.privilegeForm.giftType === "coupon" ? {
    T: common_vendor.o((...args) => $options.selectCoupons && $options.selectCoupons(...args))
  } : {}, {
    U: $data.privilegeForm.giftType === "points"
  }, $data.privilegeForm.giftType === "points" ? {
    V: $data.privilegeForm.giftPoints,
    W: common_vendor.o(($event) => $data.privilegeForm.giftPoints = $event.detail.value)
  } : {}, {
    X: $data.privilegeForm.giftType === "product"
  }, $data.privilegeForm.giftType === "product" ? {
    Y: common_vendor.o((...args) => $options.selectProducts && $options.selectProducts(...args))
  } : {}) : {}, {
    Z: $data.privilegeForm.type === "service"
  }, $data.privilegeForm.type === "service" ? {
    aa: $data.privilegeForm.serviceType === "priority" ? 1 : "",
    ab: common_vendor.o(($event) => $options.setServiceType("priority")),
    ac: $data.privilegeForm.serviceType === "exclusive" ? 1 : "",
    ad: common_vendor.o(($event) => $options.setServiceType("exclusive"))
  } : {}, {
    ae: common_vendor.f($data.memberLevels, (level, index, i0) => {
      return common_vendor.e({
        a: level.selected
      }, level.selected ? {} : {}, {
        b: level.selected ? 1 : "",
        c: common_vendor.o(($event) => $options.toggleLevel(level), index),
        d: common_vendor.t(level.name),
        e: common_vendor.t(level.memberCount),
        f: index
      });
    }),
    af: $data.privilegeForm.validityType === "permanent" ? 1 : "",
    ag: common_vendor.o(($event) => $options.setValidityType("permanent")),
    ah: $data.privilegeForm.validityType === "fixed" ? 1 : "",
    ai: common_vendor.o(($event) => $options.setValidityType("fixed")),
    aj: $data.privilegeForm.validityType === "fixed"
  }, $data.privilegeForm.validityType === "fixed" ? {
    ak: common_vendor.t($data.privilegeForm.startDate || "请选择开始日期"),
    al: $data.privilegeForm.startDate,
    am: common_vendor.o((...args) => $options.onStartDateChange && $options.onStartDateChange(...args)),
    an: common_vendor.t($data.privilegeForm.endDate || "请选择结束日期"),
    ao: $data.privilegeForm.endDate,
    ap: common_vendor.o((...args) => $options.onEndDateChange && $options.onEndDateChange(...args))
  } : {}, {
    aq: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    ar: common_vendor.o((...args) => $options.savePrivilege && $options.savePrivilege(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/add-privilege.js.map
