"use strict";
const common_vendor = require("../common/vendor.js");
function showToast(options) {
  return new Promise((resolve) => {
    if (typeof options === "string") {
      common_vendor.index.showToast({
        title: options,
        icon: "none",
        duration: 2e3,
        success: resolve,
        fail: resolve
      });
    } else {
      common_vendor.index.showToast({
        title: options.title || "",
        icon: options.icon || "none",
        duration: options.duration || 2e3,
        mask: options.mask || false,
        success: resolve,
        fail: resolve
      });
    }
  });
}
exports.showToast = showToast;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/uniapi.js.map
