{"version": 3, "file": "edit.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/flash/edit.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xmbGFzaFxlZGl0LnZ1ZQ"], "sourcesContent": ["<!-- 秒杀活动编辑页面 (edit.vue) -->\n<template>\n  <view class=\"flash-edit-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-left\">\n        <view class=\"back-button\" @tap=\"goBack\">\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M15 18L9 12L15 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </view>\n      </view>\n      <view class=\"navbar-title\">\n        <text class=\"title-text\">编辑秒杀活动</text>\n      </view>\n      <view class=\"navbar-right\">\n        <view class=\"save-button\" @tap=\"saveFlashSale\">保存</view>\n      </view>\n    </view>\n    \n    <!-- 页面内容区域 -->\n    <scroll-view scroll-y class=\"content-area\">\n      <!-- 表单区域 -->\n      <view class=\"form-section\">\n        <!-- 基本信息 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">基本信息</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">活动名称</text>\n            <input class=\"form-input\" v-model=\"formData.name\" placeholder=\"请输入秒杀活动名称\" maxlength=\"30\" />\n            <text class=\"form-counter\">{{formData.name.length}}/30</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">商品图片</text>\n            <view class=\"image-uploader\">\n              <view \n                class=\"image-item\" \n                v-for=\"(image, index) in formData.images\" \n                :key=\"index\"\n              >\n                <image class=\"uploaded-image\" :src=\"image\" mode=\"aspectFill\"></image>\n                <view class=\"delete-icon\" @tap=\"removeImage(index)\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"rgba(0,0,0,0.5)\"/>\n                    <path d=\"M15 9L9 15M9 9L15 15\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n                  </svg>\n                </view>\n              </view>\n              <view class=\"upload-button\" @tap=\"addImage\" v-if=\"formData.images.length < 5\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M12 5V19M5 12H19\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n                </svg>\n              </view>\n            </view>\n            <text class=\"form-tip\">最多上传5张图片，建议尺寸750x750像素</text>\n          </view>\n        </view>\n        \n        <!-- 价格设置 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">价格设置</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">原价 (元)</text>\n            <input class=\"form-input\" type=\"digit\" v-model=\"formData.originalPrice\" placeholder=\"请输入商品原价\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">秒杀价 (元)</text>\n            <input class=\"form-input\" type=\"digit\" v-model=\"formData.flashPrice\" placeholder=\"请输入秒杀价格\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">折扣</text>\n            <text class=\"discount-value\">{{discountValue}}</text>\n          </view>\n        </view>\n        \n        <!-- 库存设置 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">库存设置</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">活动库存</text>\n            <input class=\"form-input\" type=\"number\" v-model=\"formData.stockTotal\" placeholder=\"请输入活动库存\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">每人限购</text>\n            <input class=\"form-input\" type=\"number\" v-model=\"formData.purchaseLimit\" placeholder=\"0表示不限购\" />\n            <text class=\"form-tip\">设置为0表示不限购</text>\n          </view>\n        </view>\n        \n        <!-- 时间设置 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">时间设置</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">开始时间</text>\n            <picker \n              mode=\"dateTime\" \n              :value=\"formData.startTime\" \n              start=\"2023-01-01 00:00\" \n              end=\"2030-12-31 23:59\" \n              @change=\"startTimeChange\"\n            >\n              <view class=\"picker-value\">\n                <text>{{formData.startTime || '请选择开始时间'}}</text>\n                <view class=\"arrow-icon\"></view>\n              </view>\n            </picker>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label required\">结束时间</text>\n            <picker \n              mode=\"dateTime\" \n              :value=\"formData.endTime\" \n              start=\"2023-01-01 00:00\" \n              end=\"2030-12-31 23:59\" \n              @change=\"endTimeChange\"\n            >\n              <view class=\"picker-value\">\n                <text>{{formData.endTime || '请选择结束时间'}}</text>\n                <view class=\"arrow-icon\"></view>\n              </view>\n            </picker>\n            <text class=\"form-error\" v-if=\"timeError\">{{timeError}}</text>\n          </view>\n        </view>\n        \n        <!-- 活动规则 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">活动规则</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">活动规则</text>\n            <textarea class=\"form-textarea\" v-model=\"formData.rules\" placeholder=\"请输入活动规则说明\" maxlength=\"200\" />\n            <text class=\"form-counter\">{{formData.rules.length}}/200</text>\n          </view>\n        </view>\n        \n        <!-- 商品详情 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">商品详情</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">商品描述</text>\n            <textarea class=\"form-textarea\" v-model=\"formData.description\" placeholder=\"请输入商品描述\" maxlength=\"500\" />\n            <text class=\"form-counter\">{{formData.description.length}}/500</text>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">详情图片</text>\n            <view class=\"image-uploader\">\n              <view \n                class=\"image-item detail-image-item\" \n                v-for=\"(image, index) in formData.detailImages\" \n                :key=\"index\"\n              >\n                <image class=\"uploaded-image\" :src=\"image\" mode=\"aspectFill\"></image>\n                <view class=\"delete-icon\" @tap=\"removeDetailImage(index)\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"rgba(0,0,0,0.5)\"/>\n                    <path d=\"M15 9L9 15M9 9L15 15\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n                  </svg>\n                </view>\n              </view>\n              <view class=\"upload-button detail-upload\" @tap=\"addDetailImage\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M12 5V19M5 12H19\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n                </svg>\n              </view>\n            </view>\n            <text class=\"form-tip\">建议上传清晰的商品细节图</text>\n          </view>\n        </view>\n        \n        <!-- 高级设置 -->\n        <view class=\"form-group\">\n          <view class=\"form-header\">高级设置</view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">活动状态</text>\n            <switch \n              :checked=\"formData.status === 'active'\" \n              @change=\"statusChange\" \n              color=\"#FF7600\"\n            />\n            <text class=\"switch-label\">{{formData.status === 'active' ? '上架中' : '已下架'}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 底部空间 -->\n      <view class=\"bottom-space\"></view>\n    </scroll-view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-bar\">\n      <view class=\"action-button preview\" @tap=\"previewFlashSale\">\n        <text class=\"button-text\">预览</text>\n      </view>\n      <view class=\"action-button save\" @tap=\"saveFlashSale\">\n        <text class=\"button-text\">保存</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\n\n// 页面参数\nconst flashId = ref(null);\n\n// 表单数据\nconst formData = reactive({\n  name: '夏季清凉风扇特惠',\n  images: [\n    '/static/images/flash-item1.jpg',\n    '/static/images/flash-item1-2.jpg',\n    '/static/images/flash-item1-3.jpg'\n  ],\n  detailImages: [\n    '/static/images/flash-detail1.jpg',\n    '/static/images/flash-detail2.jpg',\n    '/static/images/flash-detail3.jpg'\n  ],\n  originalPrice: '129.9',\n  flashPrice: '59.9',\n  stockTotal: '100',\n  stockSold: '0',\n  purchaseLimit: '2',\n  startTime: '2023-07-01 12:00',\n  endTime: '2023-07-01 14:00',\n  rules: '活动期间每人限购2件，秒杀商品不支持退换货，数量有限，先到先得',\n  description: '这是一款高品质的夏季清凉风扇，采用先进技术，风力强劲，静音设计，让您的夏天更加舒适。\\n\\n规格参数：\\n- 功率：30W\\n- 风速：3档可调\\n- 电池容量：4000mAh\\n- 续航时间：4-8小时\\n- 材质：环保ABS',\n  status: 'active'\n});\n\n// 时间错误提示\nconst timeError = ref('');\n\n// 计算折扣\nconst discountValue = computed(() => {\n  if (!formData.originalPrice || !formData.flashPrice || formData.originalPrice <= 0) {\n    return '- 折';\n  }\n  \n  const discount = (formData.flashPrice / formData.originalPrice * 10).toFixed(1);\n  return `${discount} 折`;\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 添加商品图片\nconst addImage = () => {\n  uni.chooseImage({\n    count: 5 - formData.images.length,\n    sizeType: ['compressed'],\n    sourceType: ['album', 'camera'],\n    success: (res) => {\n      formData.images = [...formData.images, ...res.tempFilePaths];\n    }\n  });\n};\n\n// 移除商品图片\nconst removeImage = (index) => {\n  formData.images.splice(index, 1);\n};\n\n// 添加详情图片\nconst addDetailImage = () => {\n  uni.chooseImage({\n    count: 9,\n    sizeType: ['compressed'],\n    sourceType: ['album', 'camera'],\n    success: (res) => {\n      formData.detailImages = [...formData.detailImages, ...res.tempFilePaths];\n    }\n  });\n};\n\n// 移除详情图片\nconst removeDetailImage = (index) => {\n  formData.detailImages.splice(index, 1);\n};\n\n// 开始时间变化\nconst startTimeChange = (e) => {\n  formData.startTime = e.detail.value;\n  validateTime();\n};\n\n// 结束时间变化\nconst endTimeChange = (e) => {\n  formData.endTime = e.detail.value;\n  validateTime();\n};\n\n// 验证时间\nconst validateTime = () => {\n  if (!formData.startTime || !formData.endTime) {\n    timeError.value = '';\n    return;\n  }\n  \n  const start = new Date(formData.startTime.replace(/-/g, '/'));\n  const end = new Date(formData.endTime.replace(/-/g, '/'));\n  \n  if (end <= start) {\n    timeError.value = '结束时间必须晚于开始时间';\n  } else {\n    timeError.value = '';\n  }\n};\n\n// 状态变化\nconst statusChange = (e) => {\n  formData.status = e.detail.value ? 'active' : 'inactive';\n};\n\n// 预览秒杀活动\nconst previewFlashSale = () => {\n  if (!validateForm(true)) return;\n  \n  uni.showToast({\n    title: '预览功能开发中',\n    icon: 'none'\n  });\n};\n\n// 保存秒杀活动\nconst saveFlashSale = () => {\n  if (!validateForm()) return;\n  \n  uni.showLoading({\n    title: '保存中...'\n  });\n  \n  // 模拟API请求\n  setTimeout(() => {\n    uni.hideLoading();\n    uni.showToast({\n      title: '保存成功',\n      icon: 'success',\n      duration: 1500,\n      success: () => {\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      }\n    });\n  }, 1500);\n};\n\n// 表单验证\nconst validateForm = (isPreview = false) => {\n  if (!formData.name.trim()) {\n    uni.showToast({\n      title: '请输入活动名称',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n  if (formData.images.length === 0) {\n    uni.showToast({\n      title: '请上传至少一张商品图片',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n  if (!formData.originalPrice || parseFloat(formData.originalPrice) <= 0) {\n    uni.showToast({\n      title: '请输入有效的原价',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n  if (!formData.flashPrice || parseFloat(formData.flashPrice) <= 0) {\n    uni.showToast({\n      title: '请输入有效的秒杀价',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n  if (parseFloat(formData.flashPrice) >= parseFloat(formData.originalPrice)) {\n    uni.showToast({\n      title: '秒杀价必须低于原价',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n  if (!formData.stockTotal || parseInt(formData.stockTotal) <= 0) {\n    uni.showToast({\n      title: '请输入有效的活动库存',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n  if (!formData.startTime) {\n    uni.showToast({\n      title: '请选择开始时间',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n  if (!formData.endTime) {\n    uni.showToast({\n      title: '请选择结束时间',\n      icon: 'none'\n    });\n    return false;\n  }\n  \n  if (timeError.value) {\n    uni.showToast({\n      title: timeError.value,\n      icon: 'none'\n    });\n    return false;\n  }\n  \n  return true;\n};\n\n// 页面加载\nonMounted(() => {\n  // 获取页面参数\n  const eventChannel = getOpenerEventChannel();\n  const query = uni.getSystemInfoSync().platform === 'h5' ? location.href.split('?')[1] : '';\n  \n  if (query) {\n    const params = query.split('&').reduce((res, item) => {\n      const [key, value] = item.split('=');\n      res[key] = value;\n      return res;\n    }, {});\n    \n    flashId.value = params.id;\n  } else {\n    const pages = getCurrentPages();\n    const currentPage = pages[pages.length - 1];\n    \n    if (currentPage && currentPage.options) {\n      flashId.value = currentPage.options.id;\n    }\n  }\n  \n  // 加载秒杀活动数据\n  loadFlashSaleData();\n});\n\n// 加载秒杀活动数据\nconst loadFlashSaleData = () => {\n  if (!flashId.value) return;\n  \n  // 实际应用中应该从API获取数据\n  console.log('加载秒杀活动数据, ID:', flashId.value);\n  \n  // 模拟API请求\n  uni.showLoading({\n    title: '加载中...'\n  });\n  \n  setTimeout(() => {\n    uni.hideLoading();\n    // 实际应用中这里应该使用API返回的数据更新formData\n  }, 500);\n};\n</script>\n\n<style lang=\"scss\">\n/* 页面容器 */\n.flash-edit-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  position: relative;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\n  color: white;\n  padding: 48px 20px 16px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);\n}\n\n.navbar-left {\n  width: 40px;\n}\n\n.back-button {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n}\n\n.title-text {\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.navbar-right {\n  min-width: 40px;\n}\n\n.save-button {\n  font-size: 16px;\n  padding: 6px 12px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n}\n\n/* 内容区域 */\n.content-area {\n  flex: 1;\n  box-sizing: border-box;\n  height: calc(100vh - 80px - 60px);\n}\n\n/* 表单区域 */\n.form-section {\n  padding: 12px;\n}\n\n.form-group {\n  background: #FFFFFF;\n  border-radius: 8px;\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.form-header {\n  padding: 12px 16px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #333333;\n  border-bottom: 1px solid #F5F5F5;\n}\n\n.form-item {\n  padding: 12px 16px;\n  border-bottom: 1px solid #F5F5F5;\n  position: relative;\n}\n\n.form-item:last-child {\n  border-bottom: none;\n}\n\n.form-label {\n  display: block;\n  font-size: 14px;\n  color: #333333;\n  margin-bottom: 8px;\n}\n\n.required:after {\n  content: '*';\n  color: #FF3B30;\n  margin-left: 4px;\n}\n\n.form-input {\n  width: 100%;\n  height: 40px;\n  background: #F9F9F9;\n  border: 1px solid #EEEEEE;\n  border-radius: 4px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333333;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 100px;\n  background: #F9F9F9;\n  border: 1px solid #EEEEEE;\n  border-radius: 4px;\n  padding: 8px 12px;\n  font-size: 14px;\n  color: #333333;\n}\n\n.form-counter {\n  position: absolute;\n  right: 16px;\n  bottom: 12px;\n  font-size: 12px;\n  color: #999999;\n}\n\n.form-tip {\n  font-size: 12px;\n  color: #999999;\n  margin-top: 8px;\n  display: block;\n}\n\n.form-error {\n  font-size: 12px;\n  color: #FF3B30;\n  margin-top: 8px;\n  display: block;\n}\n\n.discount-value {\n  height: 40px;\n  line-height: 40px;\n  font-size: 16px;\n  color: #FF3B30;\n  font-weight: 600;\n}\n\n.picker-value {\n  width: 100%;\n  height: 40px;\n  background: #F9F9F9;\n  border: 1px solid #EEEEEE;\n  border-radius: 4px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333333;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.arrow-icon {\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-top: 5px solid #999999;\n}\n\n.switch-label {\n  font-size: 14px;\n  color: #666666;\n  margin-left: 8px;\n}\n\n/* 图片上传 */\n.image-uploader {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -4px;\n}\n\n.image-item {\n  width: calc(25% - 8px);\n  margin: 4px;\n  position: relative;\n  aspect-ratio: 1;\n}\n\n.detail-image-item {\n  width: calc(33.33% - 8px);\n}\n\n.uploaded-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 4px;\n}\n\n.delete-icon {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  width: 20px;\n  height: 20px;\n  z-index: 10;\n}\n\n.upload-button {\n  width: calc(25% - 8px);\n  margin: 4px;\n  aspect-ratio: 1;\n  background: #F9F9F9;\n  border: 1px dashed #DDDDDD;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #999999;\n}\n\n.detail-upload {\n  width: calc(33.33% - 8px);\n}\n\n/* 底部空间 */\n.bottom-space {\n  height: 80px;\n}\n\n/* 底部操作栏 */\n.bottom-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #FFFFFF;\n  padding: 10px 16px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  z-index: 90;\n  display: flex;\n}\n\n.action-button {\n  flex: 1;\n  height: 40px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 6px;\n}\n\n.action-button.preview {\n  background: #F5F5F5;\n  color: #666666;\n}\n\n.action-button.save {\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\n  color: white;\n}\n\n.button-text {\n  font-size: 16px;\n  font-weight: 500;\n}\n\n@media screen and (min-width: 768px) {\n  .form-section {\n    max-width: 600px;\n    margin: 0 auto;\n  }\n  \n  .bottom-bar {\n    max-width: 600px;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/flash/edit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;;;;;AAyNA,UAAM,UAAUA,cAAAA,IAAI,IAAI;AAGxB,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,MAAM;AAAA,MACN,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,eAAe;AAAA,MACf,WAAW;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,aAAa;AAAA,MACb,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,YAAYD,cAAAA,IAAI,EAAE;AAGxB,UAAM,gBAAgBE,cAAQ,SAAC,MAAM;AACnC,UAAI,CAAC,SAAS,iBAAiB,CAAC,SAAS,cAAc,SAAS,iBAAiB,GAAG;AAClF,eAAO;AAAA,MACR;AAED,YAAM,YAAY,SAAS,aAAa,SAAS,gBAAgB,IAAI,QAAQ,CAAC;AAC9E,aAAO,GAAG,QAAQ;AAAA,IACpB,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO,IAAI,SAAS,OAAO;AAAA,QAC3B,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,mBAAS,SAAS,CAAC,GAAG,SAAS,QAAQ,GAAG,IAAI,aAAa;AAAA,QAC5D;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,CAAC,UAAU;AAC7B,eAAS,OAAO,OAAO,OAAO,CAAC;AAAA,IACjC;AAGA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,mBAAS,eAAe,CAAC,GAAG,SAAS,cAAc,GAAG,IAAI,aAAa;AAAA,QACxE;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,UAAU;AACnC,eAAS,aAAa,OAAO,OAAO,CAAC;AAAA,IACvC;AAGA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,eAAS,YAAY,EAAE,OAAO;AAC9B;IACF;AAGA,UAAM,gBAAgB,CAAC,MAAM;AAC3B,eAAS,UAAU,EAAE,OAAO;AAC5B;IACF;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,SAAS,aAAa,CAAC,SAAS,SAAS;AAC5C,kBAAU,QAAQ;AAClB;AAAA,MACD;AAED,YAAM,QAAQ,IAAI,KAAK,SAAS,UAAU,QAAQ,MAAM,GAAG,CAAC;AAC5D,YAAM,MAAM,IAAI,KAAK,SAAS,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAExD,UAAI,OAAO,OAAO;AAChB,kBAAU,QAAQ;AAAA,MACtB,OAAS;AACL,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,SAAS,EAAE,OAAO,QAAQ,WAAW;AAAA,IAChD;AAGA,UAAM,mBAAmB,MAAM;AAC7B,UAAI,CAAC,aAAa,IAAI;AAAG;AAEzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,UAAI,CAAC,aAAY;AAAI;AAErBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,MAAM;AACb,uBAAW,MAAM;AACfA,4BAAG,MAAC,aAAY;AAAA,YACjB,GAAE,IAAI;AAAA,UACR;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,eAAe,CAAC,YAAY,UAAU;AAC1C,UAAI,CAAC,SAAS,KAAK,QAAQ;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO;AAAA,MACR;AAED,UAAI,SAAS,OAAO,WAAW,GAAG;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO;AAAA,MACR;AAED,UAAI,CAAC,SAAS,iBAAiB,WAAW,SAAS,aAAa,KAAK,GAAG;AACtEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO;AAAA,MACR;AAED,UAAI,CAAC,SAAS,cAAc,WAAW,SAAS,UAAU,KAAK,GAAG;AAChEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO;AAAA,MACR;AAED,UAAI,WAAW,SAAS,UAAU,KAAK,WAAW,SAAS,aAAa,GAAG;AACzEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO;AAAA,MACR;AAED,UAAI,CAAC,SAAS,cAAc,SAAS,SAAS,UAAU,KAAK,GAAG;AAC9DA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO;AAAA,MACR;AAED,UAAI,CAAC,SAAS,WAAW;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO;AAAA,MACR;AAED,UAAI,CAAC,SAAS,SAAS;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO;AAAA,MACR;AAED,UAAI,UAAU,OAAO;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,UAAU;AAAA,UACjB,MAAM;AAAA,QACZ,CAAK;AACD,eAAO;AAAA,MACR;AAED,aAAO;AAAA,IACT;AAGAC,kBAAAA,UAAU,MAAM;AAEO,4BAAwB;AAC7C,YAAM,QAAQD,cAAG,MAAC,kBAAmB,EAAC,aAAa,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI;AAExF,UAAI,OAAO;AACT,cAAM,SAAS,MAAM,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,SAAS;AACpD,gBAAM,CAAC,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG;AACnC,cAAI,GAAG,IAAI;AACX,iBAAO;AAAA,QACR,GAAE,CAAE,CAAA;AAEL,gBAAQ,QAAQ,OAAO;AAAA,MAC3B,OAAS;AACL,cAAM,QAAQ;AACd,cAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAE1C,YAAI,eAAe,YAAY,SAAS;AACtC,kBAAQ,QAAQ,YAAY,QAAQ;AAAA,QACrC;AAAA,MACF;AAGD;IACF,CAAC;AAGD,UAAM,oBAAoB,MAAM;AAC9B,UAAI,CAAC,QAAQ;AAAO;AAGpBA,oBAAA,MAAA,MAAA,OAAA,8EAAY,iBAAiB,QAAQ,KAAK;AAG1CA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAAA,MAEhB,GAAE,GAAG;AAAA,IACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACneA,GAAG,WAAWE,SAAe;"}