
.fab-btns.data-v-dd5b99c1 {
  position: fixed;
  right: 32rpx;
  bottom: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  z-index: 9999;
}
.fab-btn.data-v-dd5b99c1 {
  width: 66rpx;
  height: 66rpx;
  border-radius: 50%;
  background: rgba(240, 240, 240, 0.9);
  box-shadow: 0 3rpx 20rpx rgba(0,0,0,0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  border: none;
  padding: 0;
  outline: none;
  transition: box-shadow 0.2s, transform 0.2s;
  overflow: hidden;
  box-sizing: border-box;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}
.fab-btn.data-v-dd5b99c1:after {
  border: none;
}
.fab-btn.data-v-dd5b99c1:active {
  box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.12);
  transform: scale(0.96);
}
.share-btn.data-v-dd5b99c1 {
  background: rgba(255, 255, 255, 0.95);
  width: 66rpx;
  height: 66rpx;
  box-shadow: 0 3rpx 20rpx rgba(0,0,0,0.15);
}
.share-btn .fab-icon.data-v-dd5b99c1 {
  filter: hue-rotate(100deg) saturate(150%);
}
.fab-icon.data-v-dd5b99c1 {
  width: 40rpx;
  height: 40rpx;
  display: block;
}
.qrcode-popup-mask.data-v-dd5b99c1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.32);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qrcode-popup.data-v-dd5b99c1 {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.13);
  padding: 38rpx 32rpx 28rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 420rpx;
  animation: fadeInUp-dd5b99c1 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}
.qrcode-img.data-v-dd5b99c1 {
  width: 260rpx;
  height: 260rpx;
  border-radius: 18rpx;
  margin-bottom: 18rpx;
  box-shadow: 0 2rpx 12rpx rgba(64,158,255,0.10);
}
.qrcode-title.data-v-dd5b99c1 {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}
.qrcode-close.data-v-dd5b99c1 {
  position: absolute;
  top: 12rpx;
  right: 18rpx;
  font-size: 38rpx;
  color: #bbb;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.2s;
  z-index: 2;
}
.qrcode-close.data-v-dd5b99c1:active {
  color: #409eff;
}
@keyframes fadeInUp-dd5b99c1 {
from {
    opacity: 0;
    transform: translateY(20rpx);
    filter: saturate(0.8) brightness(0.95);
}
to {
    opacity: 1;
    transform: translateY(0);
    filter: saturate(1) brightness(1);
}
}
