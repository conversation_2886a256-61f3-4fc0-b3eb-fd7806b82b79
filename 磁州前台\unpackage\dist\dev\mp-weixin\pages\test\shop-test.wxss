
.container {
	padding: 40rpx;
}
.header {
	margin-bottom: 30rpx;
}
.title {
	font-size: 40rpx;
	font-weight: bold;
}
.test-list {
	background-color: #f5f7fa;
	border-radius: 16rpx;
	overflow: hidden;
}
.test-item {
	padding: 30rpx;
	background-color: #fff;
	margin-bottom: 20rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.shop-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	display: block;
}
.shop-desc {
	font-size: 24rpx;
	color: #999;
}
