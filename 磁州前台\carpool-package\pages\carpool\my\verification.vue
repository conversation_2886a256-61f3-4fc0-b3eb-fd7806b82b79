<template>
  <view class="verification-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">用户认证</view>
      <view class="navbar-right">
        <!-- 预留位置 -->
      </view>
    </view>
    
    <!-- 页面内容 -->
    <view class="page-content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <!-- 认证状态 -->
      <view class="verification-status" :class="{ 'verified': isVerified, 'pending': isPending }">
        <image :src="statusIcon" class="status-icon"></image>
        <text class="status-text">{{ statusText }}</text>
        <text class="status-desc">{{ statusDesc }}</text>
      </view>
      
      <!-- 认证表单 -->
      <view class="verification-form" v-if="!isVerified && !isPending">
        <view class="form-title">
          <text>请填写认证信息</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">真实姓名</text>
          <input type="text" class="form-input" v-model="formData.realName" placeholder="请输入您的真实姓名" />
        </view>
        
        <view class="form-item">
          <text class="form-label">身份证号</text>
          <input type="idcard" class="form-input" v-model="formData.idCardNo" placeholder="请输入您的身份证号码" maxlength="18" />
        </view>
        
        <view class="form-item">
          <text class="form-label">手机号码</text>
          <input type="number" class="form-input" v-model="formData.phone" placeholder="请输入您的手机号码" maxlength="11" />
        </view>
        
        <view class="upload-section">
          <text class="upload-title">身份证照片</text>
          <view class="upload-tips">
            <text>请上传清晰的身份证正反面照片</text>
          </view>
          <view class="upload-area">
            <view class="upload-item">
              <view class="upload-box" @click="chooseImage('idCardFront')" v-if="!formData.idCardFront">
                <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
                <text class="upload-text">身份证正面</text>
              </view>
              <view class="image-preview" v-else>
                <image :src="formData.idCardFront" mode="aspectFill" class="preview-image"></image>
                <view class="delete-btn" @click.stop="deleteImage('idCardFront')">
                  <image src="/static/images/tabbar/删除.png" class="delete-icon"></image>
                </view>
              </view>
            </view>
            
            <view class="upload-item">
              <view class="upload-box" @click="chooseImage('idCardBack')" v-if="!formData.idCardBack">
                <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
                <text class="upload-text">身份证背面</text>
              </view>
              <view class="image-preview" v-else>
                <image :src="formData.idCardBack" mode="aspectFill" class="preview-image"></image>
                <view class="delete-btn" @click.stop="deleteImage('idCardBack')">
                  <image src="/static/images/tabbar/删除.png" class="delete-icon"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="upload-section" v-if="needDriverLicense">
          <text class="upload-title">驾驶证照片</text>
          <view class="upload-tips">
            <text>请上传清晰的驾驶证照片</text>
          </view>
          <view class="upload-area">
            <view class="upload-item">
              <view class="upload-box" @click="chooseImage('driverLicense')" v-if="!formData.driverLicense">
                <image src="/static/images/tabbar/上传.png" class="upload-icon"></image>
                <text class="upload-text">驾驶证</text>
              </view>
              <view class="image-preview" v-else>
                <image :src="formData.driverLicense" mode="aspectFill" class="preview-image"></image>
                <view class="delete-btn" @click.stop="deleteImage('driverLicense')">
                  <image src="/static/images/tabbar/删除.png" class="delete-icon"></image>
                </view>
              </view>
            </view>
          </view>
          
          <view class="checkbox-item">
            <checkbox :checked="needDriverLicense" @click="toggleDriverLicense" color="#0A84FF" />
            <text class="checkbox-label" @click="toggleDriverLicense">我需要车主认证（发布车找人信息）</text>
          </view>
        </view>
        
        <view class="agreement-section">
          <view class="checkbox-item">
            <checkbox :checked="agreeTerms" @click="toggleAgreement" color="#0A84FF" />
            <text class="checkbox-label" @click="toggleAgreement">我已阅读并同意</text>
            <text class="link-text" @click="viewTerms">《用户认证服务协议》</text>
          </view>
        </view>
        
        <button class="submit-btn" :disabled="!canSubmit" @click="submitVerification">
          提交认证
        </button>
      </view>
      
      <!-- 认证中状态 -->
      <view class="verification-pending" v-if="isPending">
        <view class="pending-info">
          <text class="pending-title">认证信息已提交</text>
          <text class="pending-desc">我们将在1-2个工作日内完成审核，请耐心等待</text>
          <text class="pending-time">提交时间：{{ submitTime }}</text>
        </view>
        
        <button class="cancel-btn" @click="cancelVerification">
          取消认证申请
        </button>
      </view>
      
      <!-- 认证成功状态 -->
      <view class="verification-success" v-if="isVerified">
        <view class="success-info">
          <text class="success-title">恭喜您，认证成功！</text>
          <text class="success-desc">您现在可以使用拼车的全部功能</text>
          <text class="success-time">认证时间：{{ verifiedTime }}</text>
        </view>
        
        <view class="user-verified-info">
          <view class="info-item">
            <text class="info-label">认证姓名</text>
            <text class="info-value">{{ userInfo.realName }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">认证类型</text>
            <text class="info-value">{{ userInfo.hasDriverLicense ? '乘客 + 车主' : '乘客' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">认证时间</text>
            <text class="info-value">{{ verifiedTime }}</text>
          </view>
        </view>
        
        <button class="back-btn" @click="goBack">
          返回
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 响应式数据
const statusBarHeight = ref(20);
const isVerified = ref(false);
const isPending = ref(false);
const needDriverLicense = ref(false);
const agreeTerms = ref(false);
const submitTime = ref('');
const verifiedTime = ref('');
const formData = ref({
  realName: '',
  idCardNo: '',
  phone: '',
  idCardFront: '',
  idCardBack: '',
  driverLicense: ''
});
const userInfo = ref({
  realName: '张三',
  hasDriverLicense: true
});

// 计算属性
const statusIcon = computed(() => {
  if (isVerified.value) {
    return '/static/images/tabbar/verified.png'
  } else if (isPending.value) {
    return '/static/images/tabbar/pending.png'
  } else {
    return '/static/images/tabbar/unverified.png'
  }
});

const statusText = computed(() => {
  if (isVerified.value) {
    return '已认证'
  } else if (isPending.value) {
    return '审核中'
  } else {
    return '未认证'
  }
});

const statusDesc = computed(() => {
  if (isVerified.value) {
    return '您已通过实名认证'
  } else if (isPending.value) {
    return '您的认证信息正在审核中'
  } else {
    return '请完成实名认证以使用全部功能'
  }
});

const canSubmit = computed(() => {
  const { realName, idCardNo, phone, idCardFront, idCardBack } = formData.value;
  const basicInfoComplete = realName && idCardNo && phone && idCardFront && idCardBack;
  
  if (needDriverLicense.value) {
    return basicInfoComplete && formData.value.driverLicense && agreeTerms.value;
  }
  
  return basicInfoComplete && agreeTerms.value;
});

// 生命周期钩子
onMounted(() => {
  getStatusBarHeight();
  checkVerificationStatus();
});

// 方法
const getStatusBarHeight = () => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight;
};

const checkVerificationStatus = () => {
  // 这里应该是实际的API调用
  // 目前使用模拟数据
  const status = uni.getStorageSync('verificationStatus') || 'unverified';
  
  if (status === 'verified') {
    isVerified.value = true;
    isPending.value = false;
    verifiedTime.value = uni.getStorageSync('verifiedTime') || '2023-06-01 15:30:25';
  } else if (status === 'pending') {
    isVerified.value = false;
    isPending.value = true;
    submitTime.value = uni.getStorageSync('submitTime') || '2023-06-01 10:15:36';
  } else {
    isVerified.value = false;
    isPending.value = false;
  }
};

const goBack = () => {
  uni.navigateBack();
};

const toggleDriverLicense = () => {
  needDriverLicense.value = !needDriverLicense.value;
};

const toggleAgreement = () => {
  agreeTerms.value = !agreeTerms.value;
};

const viewTerms = () => {
  uni.navigateTo({
    url: '/pages/common/terms?type=verification'
  });
};

const chooseImage = (field) => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      formData.value[field] = res.tempFilePaths[0];
    }
  });
};

const deleteImage = (field) => {
  formData.value[field] = '';
};

const submitVerification = () => {
  if (!canSubmit.value) {
    return;
  }
  
  uni.showLoading({
    title: '提交中...'
  });
  
  // 这里应该是实际的API调用
  // 目前使用模拟数据
  setTimeout(() => {
    uni.hideLoading();
    
    // 保存认证状态
    const now = new Date();
    const submitTimeStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    uni.setStorageSync('verificationStatus', 'pending');
    uni.setStorageSync('submitTime', submitTimeStr);
    
    isPending.value = true;
    submitTime.value = submitTimeStr;
    
    uni.showToast({
      title: '提交成功',
      icon: 'success'
    });
  }, 1500);
};

const cancelVerification = () => {
  uni.showModal({
    title: '取消认证',
    content: '确定要取消认证申请吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '处理中...'
        });
        
        // 这里应该是实际的API调用
        // 目前使用模拟数据
        setTimeout(() => {
          uni.hideLoading();
          
          // 清除认证状态
          uni.removeStorageSync('verificationStatus');
          uni.removeStorageSync('submitTime');
          
          isPending.value = false;
          
          uni.showToast({
            title: '已取消认证',
            icon: 'success'
          });
        }, 1000);
      }
    }
  });
};
</script>

<style>
.verification-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  z-index: 100;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.navbar-left {
  position: absolute;
  left: 24rpx;
  bottom: 8rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.navbar-title {
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 18px;
  color: #FFFFFF;
  font-weight: 500;
}

.navbar-right {
  position: absolute;
  right: 24rpx;
  bottom: 8rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

/* 页面内容 */
.page-content {
  padding: 32rpx;
}

/* 认证状态 */
.verification-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #666666;
}

.verification-status.verified .status-text {
  color: #34C759;
}

.verification-status.pending .status-text {
  color: #FF9500;
}

/* 认证表单 */
.verification-form {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 32rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
}

/* 上传区域 */
.upload-section {
  margin-top: 40rpx;
  margin-bottom: 32rpx;
}

.upload-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.upload-tips {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 24rpx;
}

.upload-area {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.upload-item {
  width: 48%;
  margin-bottom: 16rpx;
}

.upload-box {
  width: 100%;
  height: 200rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #CCCCCC;
}

.upload-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 24rpx;
  color: #999999;
}

.image-preview {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  position: relative;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  width: 28rpx;
  height: 28rpx;
  filter: brightness(0) invert(1);
}

/* 复选框 */
.checkbox-item {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
}

.checkbox-label {
  font-size: 26rpx;
  color: #666666;
  margin-left: 8rpx;
}

.link-text {
  font-size: 26rpx;
  color: #0A84FF;
}

/* 协议区域 */
.agreement-section {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

/* 按钮 */
.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
}

.submit-btn[disabled] {
  background: linear-gradient(135deg, #CCCCCC, #999999);
  opacity: 0.7;
}

.cancel-btn {
  width: 100%;
  height: 88rpx;
  background-color: #FFFFFF;
  color: #FF3B30;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  border: 1px solid #FF3B30;
}

.back-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
}

/* 认证中状态 */
.verification-pending {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-top: 32rpx;
}

.pending-info {
  text-align: center;
  padding: 32rpx 0;
}

.pending-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF9500;
  margin-bottom: 16rpx;
  display: block;
}

.pending-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 24rpx;
  display: block;
}

.pending-time {
  font-size: 24rpx;
  color: #999999;
}

/* 认证成功状态 */
.verification-success {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-top: 32rpx;
}

.success-info {
  text-align: center;
  padding: 32rpx 0;
}

.success-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #34C759;
  margin-bottom: 16rpx;
  display: block;
}

.success-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 24rpx;
  display: block;
}

.success-time {
  font-size: 24rpx;
  color: #999999;
}

.user-verified-info {
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 32rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
</style> 