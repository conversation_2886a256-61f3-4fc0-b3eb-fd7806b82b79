<template>
  <view class="vehicles-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/back-white.png" class="back-icon"></image>
        </view>
        <view class="navbar-title">我的车辆</view>
        <view class="navbar-right">
          <!-- 预留位置 -->
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="isRefreshing">
      <!-- 添加车辆卡片 -->
      <view class="add-vehicle-card" @click="addVehicle" v-if="!isVerified">
        <view class="add-vehicle-content">
          <image src="/static/images/tabbar/id-card.png" class="verify-icon"></image>
          <text class="verify-text">请先完成实名认证</text>
          <text class="verify-desc">认证后才能添加车辆信息</text>
          <button class="verify-btn">立即认证</button>
        </view>
      </view>
      
      <!-- 添加车辆卡片 -->
      <view class="add-vehicle-card" @click="addVehicle" v-else-if="vehicleList.length === 0">
        <view class="add-vehicle-content">
          <image src="/static/images/tabbar/add-car.png" class="add-icon"></image>
          <text class="add-text">添加车辆信息</text>
          <text class="add-desc">添加车辆信息后可发布车主拼车信息</text>
        </view>
      </view>
      
      <!-- 车辆列表 -->
      <view class="vehicle-list" v-if="vehicleList.length > 0">
        <view class="vehicle-item" v-for="(item, index) in vehicleList" :key="item.id">
          <!-- 车辆卡片 -->
          <view class="vehicle-card">
            <view class="card-header">
              <view class="header-left">
                <text class="vehicle-name">{{item.brand}} {{item.model}}</text>
                <text class="vehicle-tag" v-if="item.isDefault">默认</text>
              </view>
              <view class="header-right">
                <text class="status-tag" :class="'status-' + item.status">{{getStatusText(item.status)}}</text>
              </view>
            </view>
            
            <view class="card-content">
              <view class="vehicle-info">
                <view class="info-row">
                  <view class="info-item">
                    <text class="info-label">车牌号码</text>
                    <text class="info-value">{{item.plateNumber}}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">车辆颜色</text>
                    <text class="info-value">{{item.color}}</text>
                  </view>
                </view>
                
                <view class="info-row">
                  <view class="info-item">
                    <text class="info-label">座位数</text>
                    <text class="info-value">{{item.seats}}座</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">车辆类型</text>
                    <text class="info-value">{{item.type}}</text>
                  </view>
                </view>
              </view>
              
              <view class="vehicle-image-wrap">
                <image :src="item.image" mode="aspectFill" class="vehicle-image"></image>
              </view>
            </view>
            
            <view class="card-actions">
              <button class="action-button outline" @click="editVehicle(item)">编辑</button>
              <button class="action-button outline" @click="setDefault(item)" v-if="!item.isDefault">设为默认</button>
              <button class="action-button outline danger" @click="deleteVehicle(item)">删除</button>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 添加更多车辆的按钮 -->
      <view class="add-more" v-if="vehicleList.length > 0 && vehicleList.length < 3">
        <button class="add-more-btn" @click="addVehicle">
          <image src="/static/images/tabbar/add-circle.png" class="add-more-icon"></image>
          <text class="add-more-text">添加更多车辆</text>
        </button>
      </view>
      
      <!-- 车辆管理提示 -->
      <view class="tips-card" v-if="vehicleList.length > 0">
        <view class="tips-header">
          <image src="/static/images/tabbar/info.png" class="tips-icon"></image>
          <text class="tips-title">车辆管理须知</text>
        </view>
        <view class="tips-content">
          <text class="tips-text">1. 最多可添加3辆车辆信息</text>
          <text class="tips-text">2. 车辆信息需要审核通过后才能使用</text>
          <text class="tips-text">3. 请确保车辆信息真实有效，否则可能影响账号信用</text>
        </view>
      </view>
      
      <!-- 无数据提示 -->
      <view class="empty-state" v-if="vehicleList.length === 0 && isVerified && !isLoading">
        <image src="/static/images/empty/no-car.png" mode="aspectFit" class="empty-image"></image>
        <text class="empty-text">暂无车辆信息</text>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="isLoading && !isRefreshing">
        <text class="loading-text">加载中...</text>
      </view>
    </scroll-view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
    
    <!-- 浮动添加按钮 -->
    <view class="float-add-btn" @click="addVehicle" v-if="isVerified && vehicleList.length > 0 && vehicleList.length < 3">
      <image src="/static/images/tabbar/add-white.png" class="float-add-icon"></image>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 响应式数据
const isVerified = ref(true); // 是否已实名认证
const vehicleList = ref([]);
const isLoading = ref(false);
const isRefreshing = ref(false);

// 生命周期钩子
onMounted(() => {
  loadData();
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 加载数据
const loadData = () => {
  isLoading.value = true;
  
  // 模拟数据加载
  setTimeout(() => {
    // 模拟数据
    vehicleList.value = [
      {
        id: '1001',
        brand: '大众',
        model: '帕萨特',
        plateNumber: '冀E·12345',
        color: '白色',
        seats: 5,
        type: '轿车',
        status: 'verified',
        isDefault: true,
        image: '/static/images/vehicles/car1.jpg'
      },
      {
        id: '1002',
        brand: '本田',
        model: 'CR-V',
        plateNumber: '冀E·67890',
        color: '黑色',
        seats: 5,
        type: 'SUV',
        status: 'pending',
        isDefault: false,
        image: '/static/images/vehicles/car2.jpg'
      }
    ];
    
    isLoading.value = false;
    isRefreshing.value = false;
  }, 1000);
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  loadData();
};

// 添加车辆
const addVehicle = () => {
  if (!isVerified.value) {
    uni.navigateTo({
      url: '/carpool-package/pages/carpool/my/verification'
    });
    return;
  }
  
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/my/add-vehicle'
  });
};

// 编辑车辆
const editVehicle = (item) => {
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/my/edit-vehicle?id=${item.id}`
  });
};

// 设为默认车辆
const setDefault = (item) => {
  uni.showLoading({
    title: '设置中...',
    mask: true
  });
  
  // 模拟API调用
  setTimeout(() => {
    vehicleList.value.forEach(vehicle => {
      vehicle.isDefault = vehicle.id === item.id;
    });
    
    uni.hideLoading();
    uni.showToast({
      title: '设置成功',
      icon: 'success'
    });
  }, 1000);
};

// 删除车辆
const deleteVehicle = (item) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除该车辆信息吗？',
    success: res => {
      if (res.confirm) {
        uni.showLoading({
          title: '删除中...',
          mask: true
        });
        
        // 模拟API调用
        setTimeout(() => {
          vehicleList.value = vehicleList.value.filter(vehicle => vehicle.id !== item.id);
          
          uni.hideLoading();
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }, 1000);
      }
    }
  });
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    verified: "已认证",
    pending: "审核中",
    rejected: "已驳回"
  };
  return statusMap[status] || status;
};
</script>

<style lang="scss">
.vehicles-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(90rpx + var(--status-bar-height, 40px));
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(140rpx + var(--status-bar-height, 44px));
  background: linear-gradient(135deg, #3a7be8, #4f8cff);
  z-index: 100;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 16rpx rgba(79,140,255,0.15);
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 140rpx;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 44px);
}

.navbar-left, .navbar-right {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.navbar-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 2rpx;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 内容区域 */
.content-scroll {
  height: calc(100vh - 90rpx - var(--status-bar-height, 40px));
  padding: 20rpx;
}

/* 添加车辆卡片 */
.add-vehicle-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  padding: 40rpx 0;
}

.add-vehicle-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.add-icon, .verify-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.add-text, .verify-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}

.add-desc, .verify-desc {
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 30rpx;
}

.verify-btn {
  width: 240rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  color: #FFFFFF;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 车辆列表 */
.vehicle-list {
  margin-bottom: 24rpx;
}

.vehicle-item {
  margin-bottom: 24rpx;
}

.vehicle-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 1px solid #F2F2F7;
}

.header-left {
  display: flex;
  align-items: center;
}

.vehicle-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}

.vehicle-tag {
  font-size: 22rpx;
  color: #FFFFFF;
  background-color: #0A84FF;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-verified {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.status-pending {
  background-color: rgba(255, 159, 10, 0.1);
  color: #FF9F0A;
}

.status-rejected {
  background-color: rgba(255, 69, 58, 0.1);
  color: #FF453A;
}

/* 卡片内容 */
.card-content {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
}

.vehicle-info {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 20rpx;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 6rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
}

.vehicle-image-wrap {
  width: 180rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-left: 20rpx;
}

.vehicle-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 卡片底部按钮 */
.card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 24rpx;
  gap: 20rpx;
  border-top: 1px solid #EEEEEE;
}

.action-button {
  min-width: 160rpx;
  height: 64rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  padding: 0 30rpx;
}

.outline {
  background-color: #FFFFFF;
  color: #666666;
  border: 1px solid #DDDDDD;
}

.danger {
  color: #FF453A;
  border-color: #FF453A;
}

/* 添加更多车辆 */
.add-more {
  margin-bottom: 24rpx;
}

.add-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  height: 88rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.add-more-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.add-more-text {
  font-size: 28rpx;
  color: #0A84FF;
}

/* 提示卡片 */
.tips-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tips-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.tips-content {
  display: flex;
  flex-direction: column;
}

.tips-text {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 浮动添加按钮 */
.float-add-btn {
  position: fixed;
  right: 40rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(10, 132, 255, 0.3);
  z-index: 99;
}

.float-add-icon {
  width: 40rpx;
  height: 40rpx;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}
</style> 