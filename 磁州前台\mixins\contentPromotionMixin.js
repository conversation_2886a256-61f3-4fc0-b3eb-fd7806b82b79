/**
 * 内容推广混入
 * 为资讯、文章等内容详情页提供推广能力
 */
import basePromotionMixin from './basePromotionMixin';

export default {
  mixins: [basePromotionMixin],

  data() {
    return {
      // 设置页面类型为内容
      pageType: 'content'
    };
  },

  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 获取当前用户ID
      const currentUserId = this.$store?.state?.user?.userId || '';
      // 获取内容发布者ID
      const authorId = this.articleDetail?.authorId || this.article?.authorId || 
                       this.newsDetail?.authorId || this.news?.authorId || '';
      // 获取内容运营权限
      const isOperator = this.$store?.state?.user?.permissions?.includes('CONTENT_OPERATION') || false;

      // 判断当前用户是否是内容作者或有运营权限
      return (currentUserId && authorId && currentUserId === authorId) || isOperator;
    },

    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 判断内容是否可分销
      const canDistribute = this.articleDetail?.canDistribute || this.article?.canDistribute || 
                           this.newsDetail?.canDistribute || this.news?.canDistribute || false;
      
      return canDistribute;
    },

    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      // 获取内容数据
      const content = this.articleDetail || this.article || this.newsDetail || this.news || {};
      
      // 构建推广数据
      this.promotionData = {
        id: content.id || '',
        title: content.title || '',
        image: content.coverImage || content.cover || content.image || '',
        summary: content.summary || content.description || this.getContentSummary(content),
        author: content.author || content.authorName || '',
        publishTime: content.publishTime || content.createTime || '',
        // 如果有更多内容特定字段，可以在这里添加
      };
    },

    /**
     * 获取内容摘要
     */
    getContentSummary(content) {
      // 如果有内容，截取前100个字符作为摘要
      if (content.content) {
        // 移除HTML标签
        const plainText = content.content.replace(/<[^>]+>/g, '');
        return plainText.substring(0, 100) + (plainText.length > 100 ? '...' : '');
      }
      return '';
    },

    /**
     * 显示内容推广浮层
     */
    showContentPromotion() {
      // 如果没有推广权限，显示提示
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '暂无推广权限',
          icon: 'none'
        });
        return;
      }

      // 打开推广工具
      this.openPromotionTools();
    }
  }
}; 