/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.group-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30px;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 顶部操作区样式 */
.top-actions {
  position: -webkit-sticky;
  position: sticky;
  top: 100px;
  /* 确保在导航栏下方 */
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 15px 16px;
  display: flex;
  justify-content: flex-end;
  z-index: 90;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 系统开关样式 */
.system-switch {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.switch-content {
  flex: 1;
}
.switch-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.switch-desc {
  font-size: 12px;
  color: #999;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.03);
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 8px;
}
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background: linear-gradient(to bottom, #9040FF, #5E35B1);
  border-radius: 3px;
}
.date-picker {
  display: flex;
  align-items: center;
  background: rgba(144, 64, 255, 0.08);
  border-radius: 20px;
  padding: 6px 12px;
  transition: all 0.3s ease;
}
.date-picker:hover {
  background: rgba(144, 64, 255, 0.12);
}
.date-text {
  font-size: 12px;
  color: #9040FF;
  margin-right: 6px;
  font-weight: 500;
}
.date-icon {
  width: 6px;
  height: 6px;
  border-top: 1.5px solid #9040FF;
  border-right: 1.5px solid #9040FF;
  transform: rotate(135deg);
}

/* 数据卡片样式 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-top: 10px;
}
.stats-card {
  background: #F8FAFC;
  border-radius: 12px;
  padding: 12px;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}
.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}
.card-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}
.card-trend {
  display: flex;
  align-items: center;
  font-size: 11px;
  margin-top: auto;
}
.card-trend.up {
  color: #34C759;
}
.card-trend.down {
  color: #FF3B30;
}
.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 4px;
}
.up .trend-arrow {
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-bottom: 5px solid #34C759;
}
.down .trend-arrow {
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 5px solid #FF3B30;
}
.card-icon {
  width: 28px;
  height: 28px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.card-icon.groups {
  background-color: rgba(144, 64, 255, 0.1);
}
.card-icon.rate {
  background-color: rgba(52, 199, 89, 0.1);
}
.card-icon.revenue {
  background-color: rgba(255, 149, 0, 0.1);
}
.card-icon.participants {
  background-color: rgba(0, 122, 255, 0.1);
}

/* 进行中的拼团活动样式 */
.active-groups-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.view-all {
  font-size: 14px;
  color: #9040FF;
}
.group-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.group-item {
  display: flex;
  background: #F8FAFC;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}
.group-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
}
.group-content {
  flex: 1;
  padding: 10px;
}
.group-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 5px;
}
.group-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-right: 60px;
}
.group-status {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.group-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.group-status.ending {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
.group-status.package {
  background: rgba(144, 64, 255, 0.1);
  color: #9040FF;
}
.group-info {
  margin-bottom: 10px;
}
.info-item {
  display: flex;
  font-size: 12px;
  margin-bottom: 3px;
}
.info-label {
  color: #999;
  width: 65px;
}
.info-value {
  color: #666;
}
.original-price {
  text-decoration: line-through;
  color: #999;
}
.group-price {
  color: #FF3B30;
  font-weight: 600;
}
.time-left {
  color: #FF9500;
}
.group-progress {
  margin-top: 5px;
}
.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}
.progress-bar {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  overflow: hidden;
}
.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}

/* 拼团设置样式 */
.settings-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.section-header {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}
.settings-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}
.settings-item {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 7.5px;
}
.item-left {
  display: flex;
  align-items: center;
}
.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.item-icon.rules {
  background-color: rgba(126, 48, 225, 0.1);
  position: relative;
}
.item-icon.rules::before {
  content: "";
  width: 12px;
  height: 12px;
  border: 2px solid #7E30E1;
  border-radius: 2px;
}
.item-icon.time {
  background-color: rgba(52, 199, 89, 0.1);
  position: relative;
}
.item-icon.time::before {
  content: "";
  width: 12px;
  height: 12px;
  border: 2px solid #34C759;
  border-radius: 6px;
}
.item-icon.discount {
  background-color: rgba(255, 59, 48, 0.1);
  position: relative;
}
.item-icon.discount::before {
  content: "%";
  color: #FF3B30;
  font-size: 14px;
  font-weight: bold;
}
.item-icon.notification {
  background-color: rgba(0, 122, 255, 0.1);
  position: relative;
}
.item-icon.notification::before {
  content: "";
  width: 12px;
  height: 12px;
  border: 2px solid #007AFF;
  border-radius: 6px;
  position: relative;
}
.item-icon.notification::after {
  content: "";
  position: absolute;
  width: 6px;
  height: 6px;
  background: #007AFF;
  border-radius: 3px;
  top: 9px;
  left: 9px;
}
.item-title {
  font-size: 12px;
  color: #333;
}
.item-right {
  display: flex;
  align-items: center;
}
.item-value {
  font-size: 12px;
  color: #333;
  margin-right: 10px;
}
.item-arrow {
  width: 8px;
  height: 8px;
  border-top: 2px solid #333;
  border-right: 2px solid #333;
  transform: rotate(45deg);
}

/* 拼团工具样式 */
.tools-section {
  margin: 15px;
  padding: 15px 12px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.tools-grid {
  display: flex;
  justify-content: space-between;
  margin: 0 -2px;
}
.tool-card {
  flex: 1;
  padding: 4px 2px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0;
}
.tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background-color: #7E30E1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.tool-icon-svg {
  width: 20px;
  height: 20px;
}
.tool-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  margin-bottom: 4px;
}
.tool-desc {
  font-size: 10px;
  color: #999;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-width: 90px;
}