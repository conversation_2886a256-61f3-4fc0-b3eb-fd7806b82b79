{"version": 3, "file": "settings.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcc2V0dGluZ3MudnVl"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">红包营销设置</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 设置内容 -->\n    <scroll-view scroll-y class=\"settings-content\">\n      <!-- 基础设置 -->\n      <view class=\"settings-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">基础设置</text>\n        </view>\n        \n        <view class=\"settings-group\">\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">自动审核红包</text>\n            </view>\n            <view class=\"item-right\">\n              <switch :checked=\"settings.autoApprove\" @change=\"toggleAutoApprove\" color=\"#FF4D4F\" />\n            </view>\n          </view>\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">红包领取提醒</text>\n              <text class=\"item-desc\">用户领取红包时通知商家</text>\n            </view>\n            <view class=\"item-right\">\n              <switch :checked=\"settings.receiveNotification\" @change=\"toggleReceiveNotification\" color=\"#FF4D4F\" />\n            </view>\n          </view>\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">红包到期提醒</text>\n              <text class=\"item-desc\">红包即将过期时提醒用户</text>\n            </view>\n            <view class=\"item-right\">\n              <switch :checked=\"settings.expiryNotification\" @change=\"toggleExpiryNotification\" color=\"#FF4D4F\" />\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 红包限制 -->\n      <view class=\"settings-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">红包限制</text>\n        </view>\n        \n        <view class=\"settings-group\">\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">单个红包最高金额</text>\n            </view>\n            <view class=\"item-right\">\n              <text class=\"item-value\">¥{{settings.maxAmount}}</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n          <view class=\"settings-item\" @click=\"showLimitPicker('dailyLimit')\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">每日发放上限</text>\n              <text class=\"item-desc\">每日最多发放红包数量</text>\n            </view>\n            <view class=\"item-right\">\n              <text class=\"item-value\">{{settings.dailyLimit}}个</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n          <view class=\"settings-item\" @click=\"showLimitPicker('userLimit')\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">用户领取限制</text>\n              <text class=\"item-desc\">单个用户每日最多领取次数</text>\n            </view>\n            <view class=\"item-right\">\n              <text class=\"item-value\">{{settings.userLimit}}次</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 红包模板设置 -->\n      <view class=\"settings-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">红包模板设置</text>\n        </view>\n        \n        <view class=\"settings-group\">\n          <view class=\"settings-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/redpacket/template')\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">红包模板管理</text>\n              <text class=\"item-desc\">创建和管理红包模板</text>\n            </view>\n            <view class=\"item-right\">\n              <text class=\"item-value\">{{settings.templateCount}}个</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">默认红包样式</text>\n            </view>\n            <view class=\"item-right\">\n              <view class=\"template-preview\" :style=\"{ backgroundImage: 'url(' + settings.defaultTemplate.preview + ')' }\"></view>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 高级设置 -->\n      <view class=\"settings-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">高级设置</text>\n        </view>\n        \n        <view class=\"settings-group\">\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">风控设置</text>\n              <text class=\"item-desc\">设置风险控制规则</text>\n            </view>\n            <view class=\"item-right\">\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">用户黑名单</text>\n              <text class=\"item-desc\">管理不可领取红包的用户</text>\n            </view>\n            <view class=\"item-right\">\n              <text class=\"item-value\">{{settings.blacklistCount}}人</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">数据导出</text>\n              <text class=\"item-desc\">导出红包营销数据</text>\n            </view>\n            <view class=\"item-right\">\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 账户与安全 -->\n      <view class=\"settings-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">账户与安全</text>\n        </view>\n        \n        <view class=\"settings-group\">\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">支付密码</text>\n              <text class=\"item-desc\">设置发放红包时的支付密码</text>\n            </view>\n            <view class=\"item-right\">\n              <text class=\"item-value\">已设置</text>\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n          <view class=\"settings-item\">\n            <view class=\"item-left\">\n              <text class=\"item-label\">操作日志</text>\n              <text class=\"item-desc\">查看红包操作记录</text>\n            </view>\n            <view class=\"item-right\">\n              <view class=\"arrow-icon\"></view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 清除缓存按钮 -->\n      <view class=\"clear-cache-btn\" @click=\"clearCache\">\n        <text class=\"btn-text\">清除缓存</text>\n      </view>\n    </scroll-view>\n    \n    <!-- 数量选择弹窗 -->\n    <view class=\"popup-mask\" v-if=\"showLimitPopup\" @click=\"cancelLimitPicker\"></view>\n    <view class=\"popup-content\" v-if=\"showLimitPopup\">\n      <view class=\"popup-header\">\n        <text class=\"popup-title\">{{currentLimitTitle}}</text>\n        <view class=\"popup-close\" @click=\"cancelLimitPicker\">×</view>\n      </view>\n      <view class=\"limit-picker\">\n        <view class=\"limit-control\" @click=\"decreaseLimit\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\n          </svg>\n        </view>\n        <text class=\"limit-value\">{{tempLimitValue}}</text>\n        <view class=\"limit-control\" @click=\"increaseLimit\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"></line>\n            <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\n          </svg>\n        </view>\n      </view>\n      <view class=\"popup-buttons\">\n        <button class=\"cancel-btn\" @click=\"cancelLimitPicker\">取消</button>\n        <button class=\"confirm-btn\" @click=\"confirmLimitPicker\">确定</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      settings: {\n        autoApprove: true,\n        receiveNotification: true,\n        expiryNotification: false,\n        maxAmount: 200,\n        dailyLimit: 100,\n        userLimit: 3,\n        templateCount: 5,\n        defaultTemplate: {\n          id: 1,\n          name: '默认红包',\n          preview: '/static/images/redpacket-template-default.png'\n        },\n        blacklistCount: 7\n      },\n      showLimitPopup: false,\n      currentLimit: '',\n      currentLimitTitle: '',\n      tempLimitValue: 0\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showHelp() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide'\n      });\n    },\n    toggleAutoApprove(e) {\n      this.settings.autoApprove = e.detail.value;\n      this.saveSettings();\n    },\n    toggleReceiveNotification(e) {\n      this.settings.receiveNotification = e.detail.value;\n      this.saveSettings();\n    },\n    toggleExpiryNotification(e) {\n      this.settings.expiryNotification = e.detail.value;\n      this.saveSettings();\n    },\n    showLimitPicker(limitType) {\n      this.currentLimit = limitType;\n      this.tempLimitValue = this.settings[limitType];\n      \n      if (limitType === 'dailyLimit') {\n        this.currentLimitTitle = '每日发放上限';\n      } else if (limitType === 'userLimit') {\n        this.currentLimitTitle = '用户领取限制';\n      }\n      \n      this.showLimitPopup = true;\n    },\n    cancelLimitPicker() {\n      this.showLimitPopup = false;\n    },\n    confirmLimitPicker() {\n      this.settings[this.currentLimit] = this.tempLimitValue;\n      this.saveSettings();\n      this.showLimitPopup = false;\n    },\n    decreaseLimit() {\n      if (this.tempLimitValue > 1) {\n        this.tempLimitValue--;\n      }\n    },\n    increaseLimit() {\n      this.tempLimitValue++;\n    },\n    navigateTo(url) {\n      uni.navigateTo({\n        url: url\n      });\n    },\n    clearCache() {\n      uni.showLoading({\n        title: '清除中...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: '缓存已清除',\n          icon: 'success'\n        });\n      }, 1000);\n    },\n    saveSettings() {\n      // 模拟保存设置\n      console.log('保存设置', this.settings);\n      uni.showToast({\n        title: '设置已保存',\n        icon: 'success',\n        duration: 1000\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* 导航栏样式 */\n.navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background-color: #fff;\n  padding: 0 15px;\n  position: relative;\n}\n\n.navbar-back {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #333;\n  border-left: 2px solid #333;\n  transform: rotate(-45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 17px;\n  font-weight: 600;\n  color: #333;\n}\n\n.navbar-right {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  border: 1px solid #999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #999;\n}\n\n/* 设置内容样式 */\n.settings-content {\n  flex: 1;\n  padding: 15px;\n}\n\n.settings-section {\n  margin-bottom: 15px;\n}\n\n.section-header {\n  margin-bottom: 10px;\n}\n\n.section-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #666;\n}\n\n.settings-group {\n  background-color: #fff;\n  border-radius: 10px;\n  overflow: hidden;\n}\n\n.settings-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.settings-item:last-child {\n  border-bottom: none;\n}\n\n.item-left {\n  flex: 1;\n}\n\n.item-label {\n  font-size: 15px;\n  color: #333;\n  display: block;\n}\n\n.item-desc {\n  font-size: 12px;\n  color: #999;\n  display: block;\n  margin-top: 4px;\n}\n\n.item-right {\n  display: flex;\n  align-items: center;\n}\n\n.item-value {\n  font-size: 14px;\n  color: #999;\n  margin-right: 5px;\n}\n\n.arrow-icon {\n  width: 8px;\n  height: 8px;\n  border-top: 1px solid #ccc;\n  border-right: 1px solid #ccc;\n  transform: rotate(45deg);\n}\n\n.template-preview {\n  width: 40px;\n  height: 60px;\n  background-size: cover;\n  background-position: center;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n/* 清除缓存按钮 */\n.clear-cache-btn {\n  background-color: #fff;\n  border-radius: 10px;\n  padding: 15px;\n  text-align: center;\n  margin-top: 20px;\n  margin-bottom: 30px;\n}\n\n.btn-text {\n  font-size: 15px;\n  color: #FF4D4F;\n}\n\n/* 弹窗样式 */\n.popup-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  z-index: 100;\n}\n\n.popup-content {\n  position: fixed;\n  left: 15px;\n  right: 15px;\n  bottom: 30px;\n  background-color: #fff;\n  border-radius: 10px;\n  padding: 20px;\n  z-index: 101;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.popup-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.popup-close {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  color: #999;\n}\n\n.limit-picker {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20px;\n}\n\n.limit-control {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  border: 1px solid #ddd;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #666;\n}\n\n.limit-value {\n  font-size: 24px;\n  font-weight: 600;\n  color: #333;\n  margin: 0 30px;\n  min-width: 40px;\n  text-align: center;\n}\n\n.popup-buttons {\n  display: flex;\n  justify-content: space-between;\n}\n\n.cancel-btn, .confirm-btn {\n  flex: 1;\n  height: 40px;\n  border-radius: 20px;\n  font-size: 15px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.cancel-btn {\n  background-color: #f5f5f5;\n  color: #666;\n  margin-right: 10px;\n}\n\n.confirm-btn {\n  background-color: #FF4D4F;\n  color: #fff;\n  margin-left: 10px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA8NA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,QACR,aAAa;AAAA,QACb,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,eAAe;AAAA,QACf,iBAAiB;AAAA,UACf,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD,gBAAgB;AAAA,MACjB;AAAA,MACD,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,IAClB;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,kBAAkB,GAAG;AACnB,WAAK,SAAS,cAAc,EAAE,OAAO;AACrC,WAAK,aAAY;AAAA,IAClB;AAAA,IACD,0BAA0B,GAAG;AAC3B,WAAK,SAAS,sBAAsB,EAAE,OAAO;AAC7C,WAAK,aAAY;AAAA,IAClB;AAAA,IACD,yBAAyB,GAAG;AAC1B,WAAK,SAAS,qBAAqB,EAAE,OAAO;AAC5C,WAAK,aAAY;AAAA,IAClB;AAAA,IACD,gBAAgB,WAAW;AACzB,WAAK,eAAe;AACpB,WAAK,iBAAiB,KAAK,SAAS,SAAS;AAE7C,UAAI,cAAc,cAAc;AAC9B,aAAK,oBAAoB;AAAA,MAC3B,WAAW,cAAc,aAAa;AACpC,aAAK,oBAAoB;AAAA,MAC3B;AAEA,WAAK,iBAAiB;AAAA,IACvB;AAAA,IACD,oBAAoB;AAClB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IACD,qBAAqB;AACnB,WAAK,SAAS,KAAK,YAAY,IAAI,KAAK;AACxC,WAAK,aAAY;AACjB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IACD,gBAAgB;AACd,UAAI,KAAK,iBAAiB,GAAG;AAC3B,aAAK;AAAA,MACP;AAAA,IACD;AAAA,IACD,gBAAgB;AACd,WAAK;AAAA,IACN;AAAA,IACD,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACR;AAAA,IACD,eAAe;AAEbA,oBAAA,MAAA,MAAA,OAAA,sFAAY,QAAQ,KAAK,QAAQ;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClUA,GAAG,WAAW,eAAe;"}