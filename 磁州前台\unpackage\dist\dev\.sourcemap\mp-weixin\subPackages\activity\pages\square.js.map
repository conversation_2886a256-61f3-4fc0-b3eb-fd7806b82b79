{"version": 3, "file": "square.js", "sources": ["subPackages/activity/pages/square.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHlccGFnZXNcc3F1YXJlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"store-management-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">店铺管理</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\">?</view>\n      </view>\n    </view>\n    \n    <!-- 头部店铺信息卡片 -->\n    <view class=\"store-header-card\">\n      <view class=\"store-header-content\">\n        <view class=\"store-avatar-container\">\n          <image class=\"store-avatar\" :src=\"storeInfo.avatar || '/static/images/default-store.png'\" mode=\"aspectFill\"></image>\n          <view class=\"avatar-edit-btn\" @tap=\"uploadStoreAvatar\">\n            <view class=\"edit-icon\"></view>\n        </view>\n      </view>\n      \n        <view class=\"store-header-info\">\n          <view class=\"store-name-container\">\n            <text class=\"store-name\">{{storeInfo.name}}</text>\n            <view class=\"store-badges\">\n              <view class=\"badge verified\" v-if=\"storeInfo.isVerified\">\n                <view class=\"badge-icon verified\"></view>\n                <text class=\"badge-text\">已认证</text>\n          </view>\n              <view class=\"badge premium\" v-if=\"storeInfo.isPremium\">\n                <view class=\"badge-icon premium\"></view>\n                <text class=\"badge-text\">优选商家</text>\n              </view>\n            </view>\n          </view>\n          <text class=\"store-desc\">{{storeInfo.description || '暂无店铺介绍'}}</text>\n          <view class=\"store-metrics\">\n            <view class=\"metric-item\">\n              <text class=\"metric-value\">{{storeInfo.rating}}</text>\n              <text class=\"metric-label\">评分</text>\n            </view>\n            <view class=\"metric-divider\"></view>\n            <view class=\"metric-item\">\n              <text class=\"metric-value\">{{storeInfo.orderCount}}</text>\n              <text class=\"metric-label\">总订单</text>\n            </view>\n            <view class=\"metric-divider\"></view>\n            <view class=\"metric-item\">\n              <text class=\"metric-value\">{{storeInfo.followers}}</text>\n              <text class=\"metric-label\">粉丝数</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"store-preview-btn\" @tap=\"previewStore\">\n          <view class=\"preview-icon\"></view>\n          <text class=\"preview-text\">预览店铺</text>\n          </view>\n      </view>\n      \n      <view class=\"store-status-bar\">\n        <view class=\"status-info\">\n          <view class=\"status-dot\" :class=\"{active: storeInfo.status === 'open'}\"></view>\n          <text class=\"status-text\">{{storeInfo.status === 'open' ? '营业中' : '休息中'}}</text>\n        </view>\n        <view class=\"operation-hours\">\n          <text class=\"hours-label\">营业时间:</text>\n          <text class=\"hours-value\">{{storeInfo.operationHours}}</text>\n          <view class=\"edit-hours-btn\" @tap=\"editOperationHours\">\n            <view class=\"edit-icon small\"></view>\n          </view>\n        </view>\n          </view>\n        </view>\n        \n    <!-- 店铺管理导航 -->\n    <scroll-view class=\"store-nav\" scroll-x show-scrollbar=\"false\">\n      <view \n        class=\"nav-item\" \n        v-for=\"(item, index) in navItems\" \n        :key=\"index\"\n        :class=\"{ active: currentNav === index }\"\n        @tap=\"switchNav(index)\">\n        <text class=\"nav-text\">{{item}}</text>\n      </view>\n    </scroll-view>\n    \n    <!-- 内容区域容器 -->\n    <scroll-view class=\"content-scroll\" scroll-y refresher-enabled @refresherrefresh=\"refreshData\" :refresher-triggered=\"refreshing\">\n      \n      <!-- 商品服务管理区块 -->\n      <view class=\"content-section\" v-show=\"currentNav === 0\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">商品与服务</text>\n          <text class=\"header-subtitle\">管理您店铺提供的商品与服务</text>\n        </view>\n        \n        <view class=\"data-overview-cards\">\n        <view class=\"data-card\">\n            <text class=\"data-value\">{{productStats.total}}</text>\n            <text class=\"data-label\">商品总数</text>\n          </view>\n          <view class=\"data-card\">\n            <text class=\"data-value\">{{productStats.onSale}}</text>\n            <text class=\"data-label\">在售商品</text>\n          </view>\n          <view class=\"data-card\">\n            <text class=\"data-value\">{{productStats.outOfStock}}</text>\n            <text class=\"data-label\">缺货商品</text>\n        </view>\n        <view class=\"data-card\">\n            <text class=\"data-value\">{{productStats.services}}</text>\n            <text class=\"data-label\">服务项目</text>\n          </view>\n          </view>\n        \n        <view class=\"action-buttons\">\n          <view class=\"action-button\" @tap=\"navigateTo('/pages/store/product/list')\">\n            <view class=\"button-icon product\"></view>\n            <text class=\"button-text\">商品管理</text>\n        </view>\n          <view class=\"action-button\" @tap=\"navigateTo('/pages/store/category/list')\">\n            <view class=\"button-icon category\"></view>\n            <text class=\"button-text\">分类管理</text>\n          </view>\n          <view class=\"action-button\" @tap=\"navigateTo('/pages/store/service/list')\">\n            <view class=\"button-icon service\"></view>\n            <text class=\"button-text\">服务项目</text>\n          </view>\n          <view class=\"action-button\" @tap=\"navigateTo('/pages/store/stock/manage')\">\n            <view class=\"button-icon stock\"></view>\n            <text class=\"button-text\">库存管理</text>\n      </view>\n    </view>\n    \n        <view class=\"hot-products-section\">\n          <view class=\"sub-section-header\">\n            <text class=\"sub-section-title\">热门商品</text>\n            <text class=\"view-more\" @tap=\"navigateTo('/pages/store/product/hot')\">查看全部</text>\n          </view>\n          \n          <scroll-view class=\"product-scroll\" scroll-x show-scrollbar=\"false\">\n            <view class=\"product-card\" v-for=\"(product, index) in hotProducts\" :key=\"index\">\n              <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\n              <view class=\"product-info\">\n                <text class=\"product-name\">{{product.name}}</text>\n                <text class=\"product-price\">¥{{product.price}}</text>\n                <view class=\"product-stats\">\n                  <text class=\"stats-item\">销量: {{product.sales}}</text>\n                  <text class=\"stats-item\">好评率: {{product.rating}}%</text>\n                </view>\n              </view>\n        </view>\n      </scroll-view>\n    </view>\n    \n        <view class=\"quick-actions\">\n          <view class=\"action-card\" @tap=\"navigateTo('/pages/store/product/add')\">\n            <view class=\"action-icon add\"></view>\n            <text class=\"action-title\">添加商品</text>\n            <text class=\"action-desc\">快速添加新商品</text>\n          </view>\n          <view class=\"action-card\" @tap=\"navigateTo('/pages/store/product/batch')\">\n            <view class=\"action-icon batch\"></view>\n            <text class=\"action-title\">批量管理</text>\n            <text class=\"action-desc\">批量编辑商品信息</text>\n          </view>\n          <view class=\"action-card\" @tap=\"navigateTo('/pages/store/product/import')\">\n            <view class=\"action-icon import\"></view>\n            <text class=\"action-title\">导入导出</text>\n            <text class=\"action-desc\">表格导入导出商品</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 店铺形象区块 -->\n      <view class=\"content-section\" v-show=\"currentNav === 1\">\n      <view class=\"section-header\">\n          <text class=\"section-title\">店铺形象</text>\n          <text class=\"header-subtitle\">提升店铺形象，吸引更多顾客</text>\n        </view>\n        \n        <!-- 店铺封面管理 -->\n        <view class=\"cover-section\">\n          <text class=\"sub-section-title\">店铺封面</text>\n          <view class=\"cover-preview\">\n            <image class=\"cover-image\" :src=\"storeInfo.coverImage || '/static/images/default-cover.jpg'\" mode=\"aspectFill\"></image>\n            <view class=\"cover-edit-btn\" @tap=\"uploadCoverImage\">\n              <view class=\"edit-cover-icon\"></view>\n              <text class=\"edit-text\">更换封面</text>\n            </view>\n        </view>\n      </view>\n      \n        <!-- 店铺风格设置 -->\n        <view class=\"style-section\">\n          <text class=\"sub-section-title\">店铺风格</text>\n          <view class=\"style-cards\">\n            <view \n              class=\"style-card\" \n              v-for=\"(style, index) in storeStyles\" \n              :key=\"index\"\n              :class=\"{ active: storeInfo.styleId === style.id }\"\n              @tap=\"selectStoreStyle(style.id)\">\n              <image class=\"style-preview\" :src=\"style.preview\" mode=\"aspectFill\"></image>\n              <view class=\"style-info\">\n                <text class=\"style-name\">{{style.name}}</text>\n                <view class=\"style-check\" v-if=\"storeInfo.styleId === style.id\">\n                  <view class=\"check-icon\"></view>\n                </view>\n              </view>\n            </view>\n          </view>\n      </view>\n      \n        <!-- 店铺相册 -->\n        <view class=\"gallery-section\">\n          <view class=\"sub-section-header\">\n            <text class=\"sub-section-title\">店铺相册</text>\n            <text class=\"view-more\" @tap=\"navigateTo('/pages/store/gallery')\">管理相册</text>\n        </view>\n          \n          <text class=\"gallery-hint\">向顾客展示您的店铺环境、商品展示、团队风采等</text>\n          \n          <view class=\"gallery-grid\">\n            <view class=\"gallery-item\" v-for=\"(image, index) in storeGallery\" :key=\"index\">\n              <image class=\"gallery-image\" :src=\"image.url\" mode=\"aspectFill\"></image>\n              <text class=\"gallery-tag\" v-if=\"image.tag\">{{image.tag}}</text>\n            </view>\n            <view class=\"gallery-add\" @tap=\"addGalleryImage\">\n              <view class=\"add-icon\"></view>\n              <text class=\"add-text\">添加图片</text>\n        </view>\n      </view>\n    </view>\n    \n        <!-- 宣传视频 -->\n        <view class=\"video-section\">\n          <text class=\"sub-section-title\">宣传视频</text>\n          <view class=\"video-container\" v-if=\"storeInfo.promotionVideo\">\n            <video class=\"store-video\" :src=\"storeInfo.promotionVideo\" object-fit=\"cover\" poster=\"/static/images/video-poster.jpg\"></video>\n          </view>\n          <view class=\"video-upload\" v-else @tap=\"uploadVideo\">\n            <view class=\"upload-icon\"></view>\n            <text class=\"upload-text\">上传宣传视频</text>\n            <text class=\"upload-hint\">支持MP4格式，大小不超过50MB，时长建议30秒-2分钟</text>\n          </view>\n        </view>\n        \n        <!-- 商家故事 -->\n        <view class=\"story-section\">\n          <text class=\"sub-section-title\">商家故事</text>\n          <view class=\"story-editor\">\n            <textarea class=\"story-textarea\" v-model=\"storeInfo.story\" placeholder=\"讲述您的品牌故事，与顾客建立情感连接...\" maxlength=\"500\"></textarea>\n            <text class=\"text-counter\">{{(storeInfo.story || '').length}}/500</text>\n          </view>\n          <view class=\"save-story-btn\" @tap=\"saveStoryContent\">\n            <text class=\"save-text\">保存内容</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 位置与配送区块 -->\n      <view class=\"content-section\" v-show=\"currentNav === 2\">\n      <view class=\"section-header\">\n          <text class=\"section-title\">位置与配送</text>\n          <text class=\"header-subtitle\">管理店铺位置和配送范围</text>\n      </view>\n      \n        <!-- 店铺地址 -->\n        <view class=\"location-card\">\n          <text class=\"location-title\">店铺地址</text>\n          \n          <view class=\"address-container\">\n            <view class=\"address-info\">\n              <view class=\"address-detail\">\n                <view class=\"location-icon\"></view>\n                <text class=\"address-text\">{{storeInfo.address || '点击设置店铺地址'}}</text>\n          </view>\n              <view class=\"address-actions\">\n                <view class=\"action-btn edit\" @tap=\"editAddress\">\n                  <view class=\"btn-icon edit\"></view>\n                  <text class=\"btn-text\">编辑</text>\n            </view>\n                <view class=\"action-btn navigate\" @tap=\"navigateToStore\">\n                  <view class=\"btn-icon navigate\"></view>\n                  <text class=\"btn-text\">导航</text>\n            </view>\n            </view>\n          </view>\n        </view>\n          \n          <!-- 地图预览 -->\n          <view class=\"map-container\">\n            <image class=\"map-preview\" src=\"/static/images/store-map.jpg\" mode=\"aspectFill\"></image>\n            <view class=\"map-marker\">\n              <view class=\"marker-icon\"></view>\n            </view>\n            <view class=\"map-tools\">\n              <view class=\"map-tool-btn\" @tap=\"openMapSelection\">\n                <view class=\"tool-icon map\"></view>\n                <text class=\"tool-text\">选择位置</text>\n              </view>\n              <view class=\"map-tool-btn\" @tap=\"previewMapInApp\">\n                <view class=\"tool-icon preview\"></view>\n                <text class=\"tool-text\">查看大图</text>\n              </view>\n            </view>\n      </view>\n    </view>\n    \n        <!-- 配送范围 -->\n        <view class=\"delivery-section\">\n          <text class=\"section-subtitle\">配送范围</text>\n          \n          <view class=\"delivery-toggle\">\n            <text class=\"toggle-label\">提供配送服务</text>\n            <switch :checked=\"storeInfo.providesDelivery\" color=\"#1677FF\" @change=\"toggleDelivery\" />\n      </view>\n      \n          <view class=\"delivery-settings\" v-if=\"storeInfo.providesDelivery\">\n            <view class=\"radius-setting\">\n              <text class=\"setting-label\">配送半径</text>\n              <view class=\"radius-slider-container\">\n                <slider \n                  class=\"radius-slider\" \n                  :min=\"1\" \n                  :max=\"20\" \n                  :value=\"storeInfo.deliveryRadius\" \n                  show-value \n                  activeColor=\"#1677FF\"\n                  backgroundColor=\"#E1E8F0\"\n                  block-color=\"#1677FF\"\n                  @change=\"updateDeliveryRadius\" />\n                <text class=\"radius-value\">{{storeInfo.deliveryRadius}}公里</text>\n          </view>\n        </view>\n            \n            <view class=\"delivery-fee-settings\">\n              <text class=\"setting-label\">配送费设置</text>\n              \n              <view class=\"fee-type-toggle\">\n                <view \n                  class=\"toggle-option\" \n                  :class=\"{active: storeInfo.deliveryFeeType === 'fixed'}\" \n                  @tap=\"storeInfo.deliveryFeeType = 'fixed'\">\n                  <text class=\"option-text\">固定费用</text>\n                </view>\n                <view \n                  class=\"toggle-option\" \n                  :class=\"{active: storeInfo.deliveryFeeType === 'distance'}\" \n                  @tap=\"storeInfo.deliveryFeeType = 'distance'\">\n                  <text class=\"option-text\">按距离收费</text>\n      </view>\n    </view>\n    \n              <!-- 固定费用设置 -->\n              <view class=\"fee-input-container\" v-if=\"storeInfo.deliveryFeeType === 'fixed'\">\n                <text class=\"fee-input-label\">配送费</text>\n                <view class=\"fee-input-wrap\">\n                  <input type=\"digit\" class=\"fee-input\" v-model=\"storeInfo.fixedDeliveryFee\" placeholder=\"请输入配送费\" />\n                  <text class=\"fee-unit\">元</text>\n                </view>\n      </view>\n      \n              <!-- 按距离收费设置 -->\n              <view class=\"distance-fee-table\" v-if=\"storeInfo.deliveryFeeType === 'distance'\">\n                <view class=\"table-header\">\n                  <text class=\"header-cell\">距离范围</text>\n                  <text class=\"header-cell\">配送费(元)</text>\n                  <text class=\"header-cell\">操作</text>\n            </view>\n                \n                <view class=\"table-row\" v-for=\"(fee, index) in distanceFeeTiers\" :key=\"index\">\n                  <view class=\"row-cell distance\">\n                    <text v-if=\"index === 0\">0 - {{fee.distance}}公里</text>\n                    <text v-else>{{distanceFeeTiers[index-1].distance}} - {{fee.distance}}公里</text>\n            </view>\n                  <view class=\"row-cell fee\">\n                    <input type=\"digit\" class=\"tier-fee-input\" v-model=\"fee.price\" />\n          </view>\n                  <view class=\"row-cell actions\">\n                    <view class=\"row-action delete\" @tap=\"removeFeeTier(index)\" v-if=\"distanceFeeTiers.length > 1\">\n                      <view class=\"delete-icon\"></view>\n            </view>\n          </view>\n        </view>\n                \n                <view class=\"add-tier-btn\" @tap=\"addFeeTier\">\n                  <view class=\"add-icon\"></view>\n                  <text class=\"add-text\">添加梯度</text>\n      </view>\n    </view>\n    \n              <!-- 起送价设置 -->\n              <view class=\"minimum-order\">\n                <text class=\"setting-label\">起送价</text>\n                <view class=\"minimum-input-wrap\">\n                  <input type=\"digit\" class=\"minimum-input\" v-model=\"storeInfo.minimumOrder\" placeholder=\"请输入起送价\" />\n                  <text class=\"minimum-unit\">元</text>\n                </view>\n                <text class=\"minimum-hint\">设置为0表示无起送价限制</text>\n              </view>\n            </view>\n          </view>\n          \n          <view class=\"empty-delivery\" v-else>\n            <image class=\"empty-icon\" src=\"/static/images/delivery-inactive.png\"></image>\n            <text class=\"empty-text\">您当前未开启配送服务</text>\n          </view>\n        </view>\n        \n        <!-- 自提点设置 -->\n        <view class=\"pickup-section\">\n          <view class=\"section-header-with-toggle\">\n            <text class=\"section-subtitle\">自提点设置</text>\n            <view class=\"toggle-container\">\n              <text class=\"toggle-label\">支持自提</text>\n              <switch :checked=\"storeInfo.supportsPickup\" color=\"#1677FF\" @change=\"togglePickup\" />\n            </view>\n          </view>\n          \n          <view class=\"pickup-settings\" v-if=\"storeInfo.supportsPickup\">\n            <view class=\"pickup-instructions\">\n              <text class=\"setting-label\">自提说明</text>\n              <textarea class=\"pickup-textarea\" v-model=\"storeInfo.pickupInstructions\" placeholder=\"请输入自提说明，如：请到前台出示订单号领取...\" maxlength=\"200\"></textarea>\n              <text class=\"text-counter\">{{(storeInfo.pickupInstructions || '').length}}/200</text>\n            </view>\n          </view>\n          \n          <view class=\"empty-pickup\" v-else>\n            <image class=\"empty-icon\" src=\"/static/images/pickup-inactive.png\"></image>\n            <text class=\"empty-text\">您当前未开启自提服务</text>\n          </view>\n        </view>\n        \n        <!-- 保存按钮 -->\n        <view class=\"save-btn\" @tap=\"saveLocationAndDelivery\">\n          <text class=\"save-text\">保存设置</text>\n        </view>\n      </view>\n      \n      <!-- 认证与资质区块 -->\n      <view class=\"content-section\" v-show=\"currentNav === 3\">\n      <view class=\"section-header\">\n          <text class=\"section-title\">认证与资质</text>\n          <text class=\"header-subtitle\">提高店铺可信度，增加顾客信任感</text>\n      </view>\n      \n        <!-- 商家认证状态 -->\n        <view class=\"certification-card\">\n          <view class=\"certification-header\">\n            <text class=\"card-title\">商家认证</text>\n            <view class=\"cert-status\" :class=\"storeInfo.certificationStatus\">\n              <text class=\"status-text\">\n                {{certStatusText[storeInfo.certificationStatus] || '未认证'}}\n              </text>\n          </view>\n            </view>\n          \n          <view class=\"certification-content\">\n            <!-- 未认证状态 -->\n            <view class=\"uncertified\" v-if=\"storeInfo.certificationStatus === 'uncertified'\">\n              <view class=\"uncert-icon\"></view>\n              <text class=\"uncert-title\">您尚未完成商家认证</text>\n              <text class=\"uncert-desc\">完成认证后，将获得\"已认证\"标识，提高店铺可信度</text>\n              <view class=\"start-cert-btn\" @tap=\"startCertification\">\n                <text class=\"btn-text\">开始认证</text>\n          </view>\n          </view>\n            \n            <!-- 审核中状态 -->\n            <view class=\"in-progress\" v-else-if=\"storeInfo.certificationStatus === 'pending'\">\n              <view class=\"progress-icon\"></view>\n              <text class=\"progress-title\">认证审核中</text>\n              <text class=\"progress-desc\">预计{{storeInfo.certificationETA || '3个工作日'}}内完成审核，请耐心等待</text>\n              <view class=\"cert-progress\">\n                <view class=\"progress-bar\">\n                  <view class=\"progress-fill\" style=\"width: 50%;\"></view>\n                </view>\n                <text class=\"progress-text\">审核进行中</text>\n        </view>\n      </view>\n      \n            <!-- 已认证状态 -->\n            <view class=\"certified\" v-else-if=\"storeInfo.certificationStatus === 'certified'\">\n              <view class=\"cert-info\">\n                <view class=\"cert-icon\"></view>\n                <view class=\"cert-details\">\n                  <text class=\"cert-name\">{{storeInfo.certType === 'individual' ? '个体工商户认证' : '企业认证'}}</text>\n                  <text class=\"cert-time\">认证时间：{{storeInfo.certTime || '2023-04-01'}}</text>\n                  <text class=\"cert-number\">{{storeInfo.certType === 'individual' ? '营业执照' : '统一社会信用代码'}}：{{storeInfo.certNumber}}</text>\n                </view>\n              </view>\n              <view class=\"renewal-info\" v-if=\"storeInfo.certExpiringSoon\">\n                <view class=\"alert-icon\"></view>\n                <text class=\"renewal-text\">认证即将到期，请及时更新资质</text>\n                <view class=\"renew-btn\" @tap=\"renewCertification\">更新</view>\n      </view>\n    </view>\n    \n            <!-- 认证失败状态 -->\n            <view class=\"cert-failed\" v-else-if=\"storeInfo.certificationStatus === 'failed'\">\n              <view class=\"failed-icon\"></view>\n              <text class=\"failed-title\">认证未通过</text>\n              <text class=\"failed-reason\">原因：{{storeInfo.failReason || '资料不完整，请补充完整的营业执照信息'}}</text>\n              <view class=\"retry-cert-btn\" @tap=\"retryCertification\">\n                <text class=\"btn-text\">重新提交</text>\n              </view>\n            </view>\n          </view>\n      </view>\n      \n        <!-- 行业资质 -->\n        <view class=\"qualification-section\">\n          <text class=\"section-subtitle\">行业资质</text>\n          \n          <view class=\"quals-container\">\n            <!-- 未添加资质时的提示 -->\n            <view class=\"no-quals\" v-if=\"qualifications.length === 0\">\n              <image class=\"no-quals-img\" src=\"/static/images/no-qualification.png\"></image>\n              <text class=\"no-quals-text\">您尚未添加行业资质</text>\n              <text class=\"no-quals-desc\">添加相关行业资质，提高顾客信任度</text>\n          </view>\n            \n            <!-- 已添加的资质列表 -->\n            <view class=\"qual-list\" v-else>\n              <view class=\"qual-item\" v-for=\"(qual, index) in qualifications\" :key=\"index\">\n                <view class=\"qual-icon\" :class=\"qual.type\"></view>\n                <view class=\"qual-info\">\n                  <text class=\"qual-name\">{{qual.name}}</text>\n                  <text class=\"qual-desc\">{{qual.validUntil ? '有效期至：' + qual.validUntil : '长期有效'}}</text>\n          </view>\n                <view class=\"qual-status\" :class=\"qual.status\">\n                  <text class=\"status-text\">{{qualStatusText[qual.status] || '待审核'}}</text>\n        </view>\n                <view class=\"qual-actions\">\n                  <view class=\"action-btn view\" @tap=\"viewQualification(qual)\">\n                    <view class=\"btn-icon view\"></view>\n                  </view>\n                  <view class=\"action-btn edit\" @tap=\"editQualification(qual)\">\n                    <view class=\"btn-icon edit\"></view>\n                  </view>\n                  <view class=\"action-btn delete\" @tap=\"deleteQualification(qual)\">\n                    <view class=\"btn-icon delete\"></view>\n                  </view>\n                </view>\n      </view>\n    </view>\n    \n            <view class=\"add-qual-btn\" @tap=\"addQualification\">\n              <view class=\"add-icon\"></view>\n              <text class=\"add-text\">添加资质</text>\n    </view>\n          </view>\n        </view>\n        \n        <!-- 到期提醒设置 -->\n        <view class=\"expiration-reminder\">\n          <text class=\"section-subtitle\">到期提醒设置</text>\n          \n          <view class=\"reminder-card\">\n            <view class=\"reminder-item\">\n              <text class=\"reminder-label\">提前提醒天数</text>\n              <view class=\"reminder-input-wrap\">\n                <input type=\"number\" class=\"reminder-input\" v-model=\"storeInfo.expirationReminder\" />\n                <text class=\"reminder-unit\">天</text>\n              </view>\n            </view>\n            \n            <view class=\"reminder-item\">\n              <text class=\"reminder-label\">提醒方式</text>\n              <view class=\"reminder-checkboxes\">\n                <view class=\"checkbox-item\" @tap=\"toggleReminderMethod('app')\">\n                  <view class=\"checkbox\" :class=\"{checked: reminderMethods.includes('app')}\">\n                    <view class=\"check-mark\" v-if=\"reminderMethods.includes('app')\"></view>\n                  </view>\n                  <text class=\"checkbox-label\">应用通知</text>\n                </view>\n                \n                <view class=\"checkbox-item\" @tap=\"toggleReminderMethod('sms')\">\n                  <view class=\"checkbox\" :class=\"{checked: reminderMethods.includes('sms')}\">\n                    <view class=\"check-mark\" v-if=\"reminderMethods.includes('sms')\"></view>\n                  </view>\n                  <text class=\"checkbox-label\">短信通知</text>\n                </view>\n                \n                <view class=\"checkbox-item\" @tap=\"toggleReminderMethod('email')\">\n                  <view class=\"checkbox\" :class=\"{checked: reminderMethods.includes('email')}\">\n                    <view class=\"check-mark\" v-if=\"reminderMethods.includes('email')\"></view>\n                  </view>\n                  <text class=\"checkbox-label\">邮件通知</text>\n                </view>\n              </view>\n            </view>\n            \n            <view class=\"save-reminder-btn\" @tap=\"saveReminderSettings\">\n              <text class=\"save-text\">保存设置</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      currentNav: 0,\n      refreshing: false,\n      navItems: ['商品服务', '店铺形象', '位置配送', '认证资质', '基本信息'],\n      \n      storeInfo: {\n        id: '12345678',\n        name: '磁州生活家居旗舰店',\n        avatar: '/static/images/store-logo.png',\n        coverImage: '/static/images/store-cover.jpg',\n        description: '为您提供高品质家居产品，打造舒适生活空间',\n        rating: 4.8,\n        orderCount: 1286,\n        followers: 568,\n        status: 'open',\n        operationHours: '09:00 - 22:00',\n        isVerified: true,\n        isPremium: true,\n        styleId: 2,\n        story: '我们是一家专注于家居产品的本地商户，创立于2018年。始终坚持\"品质优先，用户至上\"的原则，精选每一件商品，只为给客户带来舒适的居家体验。',\n        address: '河北省邯郸市磁县磁州生活广场A座1202室',\n        providesDelivery: true,\n        deliveryRadius: 5,\n        deliveryFeeType: 'fixed',\n        fixedDeliveryFee: '5',\n        minimumOrder: '20',\n        supportsPickup: true,\n        pickupInstructions: '请到店铺前台出示订单号领取商品，工作时间9:00-22:00。',\n        certificationStatus: 'certified', // uncertified, pending, certified, failed\n        certType: 'enterprise',\n        certTime: '2023-01-15',\n        certNumber: '91130427MA0CXY2R5B',\n        certExpiringSoon: false,\n        expirationReminder: 30,\n        promotionVideo: ''\n      },\n      \n      productStats: {\n        total: 128,\n        onSale: 96,\n        outOfStock: 8,\n        services: 12\n      },\n      \n      // 热门商品数据\n      hotProducts: [\n        {\n          id: 1,\n          name: '北欧风格实木沙发',\n          price: '1299.00',\n          image: '/static/images/product-1.jpg',\n          sales: 56,\n          rating: 98\n        },\n        {\n          id: 2,\n          name: '天然乳胶床垫',\n          price: '2399.00',\n          image: '/static/images/product-2.jpg',\n          sales: 42,\n          rating: 95\n        },\n        {\n          id: 3,\n          name: '日式简约茶几',\n          price: '499.00',\n          image: '/static/images/product-3.jpg',\n          sales: 38,\n          rating: 92\n        },\n        {\n          id: 4,\n          name: '环保棉麻窗帘',\n          price: '298.00',\n          image: '/static/images/product-4.jpg',\n          sales: 127,\n          rating: 97\n        }\n      ],\n      \n      // 店铺风格数据\n      storeStyles: [\n        {\n          id: 1,\n          name: '简约现代',\n          preview: '/static/images/style-modern.jpg'\n        },\n        {\n          id: 2,\n          name: '轻奢风格',\n          preview: '/static/images/style-luxury.jpg'\n        },\n        {\n          id: 3,\n          name: '田园清新',\n          preview: '/static/images/style-country.jpg'\n        },\n        {\n          id: 4,\n          name: '工业风',\n          preview: '/static/images/style-industrial.jpg'\n        }\n      ],\n      \n      // 店铺相册数据\n      storeGallery: [\n        {\n          id: 1,\n          url: '/static/images/gallery-1.jpg',\n          tag: '店铺环境'\n        },\n        {\n          id: 2,\n          url: '/static/images/gallery-2.jpg',\n          tag: '产品展示'\n        },\n        {\n          id: 3,\n          url: '/static/images/gallery-3.jpg',\n          tag: '团队风采'\n        },\n        {\n          id: 4,\n          url: '/static/images/gallery-4.jpg',\n          tag: '活动现场'\n        },\n        {\n          id: 5,\n          url: '/static/images/gallery-5.jpg',\n          tag: '顾客好评'\n        }\n      ],\n      \n      // 配送费梯度\n      distanceFeeTiers: [\n        { distance: 3, price: '3' },\n        { distance: 5, price: '5' },\n        { distance: 10, price: '10' }\n      ],\n      \n      // 行业资质数据\n      qualifications: [\n        {\n          id: 1,\n          name: '食品经营许可证',\n          type: 'food',\n          validUntil: '2025-12-31',\n          status: 'approved',\n          image: '/static/images/qual-food.jpg'\n        },\n        {\n          id: 2,\n          name: '卫生许可证',\n          type: 'health',\n          validUntil: '2024-10-15',\n          status: 'approved',\n          image: '/static/images/qual-health.jpg'\n        }\n      ],\n      \n      // 认证状态文本\n      certStatusText: {\n        'uncertified': '未认证',\n        'pending': '审核中',\n        'certified': '已认证',\n        'failed': '未通过'\n      },\n      \n      // 资质状态文本\n      qualStatusText: {\n        'pending': '审核中',\n        'approved': '已通过',\n        'rejected': '未通过',\n        'expired': '已过期'\n      },\n      \n      // 提醒方式\n      reminderMethods: ['app', 'sms']\n    }\n  },\n  \n  methods: {\n    // 通用导航方法\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    navigateTo(url) {\n      uni.navigateTo({\n        url: url\n      });\n    },\n    \n    // 切换导航选项卡\n    switchNav(index) {\n      this.currentNav = index;\n    },\n    \n    // 下拉刷新\n    refreshData(e) {\n      this.refreshing = true;\n      setTimeout(() => {\n        // 模拟数据刷新\n        this.refreshing = false;\n      }, 1500);\n    },\n    \n    // 商品服务相关方法\n    previewStore() {\n      uni.showToast({\n        title: '预览功能开发中',\n        icon: 'none'\n      });\n    },\n    \n    uploadStoreAvatar() {\n      uni.chooseImage({\n        count: 1,\n        success: (res) => {\n          // 模拟上传\n          setTimeout(() => {\n            this.storeInfo.avatar = res.tempFilePaths[0];\n            uni.showToast({\n              title: '上传成功',\n              icon: 'success'\n            });\n          }, 1000);\n        }\n      });\n    },\n    \n    editOperationHours() {\n      uni.showToast({\n        title: '编辑营业时间功能开发中',\n        icon: 'none'\n      });\n    },\n    \n    // 店铺形象相关方法\n    uploadCoverImage() {\n      uni.chooseImage({\n        count: 1,\n        success: (res) => {\n          // 模拟上传\n          setTimeout(() => {\n            this.storeInfo.coverImage = res.tempFilePaths[0];\n            uni.showToast({\n              title: '上传成功',\n              icon: 'success'\n            });\n          }, 1000);\n        }\n      });\n    },\n    \n    selectStoreStyle(styleId) {\n      this.storeInfo.styleId = styleId;\n    },\n    \n    addGalleryImage() {\n      uni.chooseImage({\n        count: 1,\n        success: (res) => {\n          // 模拟上传\n          setTimeout(() => {\n            this.storeGallery.push({\n              id: this.storeGallery.length + 1,\n              url: res.tempFilePaths[0],\n              tag: '新增图片'\n            });\n            uni.showToast({\n              title: '上传成功',\n              icon: 'success'\n            });\n          }, 1000);\n        }\n      });\n    },\n    \n    uploadVideo() {\n      uni.chooseVideo({\n        sourceType: ['album', 'camera'],\n        maxDuration: 120,\n        camera: 'back',\n        success: (res) => {\n          // 模拟上传\n          setTimeout(() => {\n            this.storeInfo.promotionVideo = res.tempFilePath;\n            uni.showToast({\n              title: '上传成功',\n              icon: 'success'\n            });\n          }, 1500);\n        }\n      });\n    },\n    \n    saveStoryContent() {\n      uni.showToast({\n        title: '保存成功',\n        icon: 'success'\n      });\n    },\n    \n    // 位置配送相关方法\n    editAddress() {\n      uni.showToast({\n        title: '编辑地址功能开发中',\n        icon: 'none'\n      });\n    },\n    \n    navigateToStore() {\n      uni.showToast({\n        title: '导航功能开发中',\n        icon: 'none'\n      });\n    },\n    \n    openMapSelection() {\n      uni.showToast({\n        title: '地图选点功能开发中',\n        icon: 'none'\n      });\n    },\n    \n    previewMapInApp() {\n      uni.showToast({\n        title: '地图预览功能开发中',\n        icon: 'none'\n      });\n    },\n    \n    toggleDelivery(e) {\n      this.storeInfo.providesDelivery = e.detail.value;\n    },\n    \n    updateDeliveryRadius(e) {\n      this.storeInfo.deliveryRadius = e.detail.value;\n    },\n    \n    addFeeTier() {\n      if (this.distanceFeeTiers.length >= 5) {\n        uni.showToast({\n          title: '最多添加5个梯度',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      const lastTier = this.distanceFeeTiers[this.distanceFeeTiers.length - 1];\n      this.distanceFeeTiers.push({\n        distance: lastTier.distance + 5,\n        price: (parseFloat(lastTier.price) + 5).toString()\n      });\n    },\n    \n    removeFeeTier(index) {\n      this.distanceFeeTiers.splice(index, 1);\n    },\n    \n    togglePickup(e) {\n      this.storeInfo.supportsPickup = e.detail.value;\n    },\n    \n    saveLocationAndDelivery() {\n      uni.showLoading({\n        title: '保存中...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: '保存成功',\n          icon: 'success'\n        });\n      }, 1500);\n    },\n    \n    // 认证资质相关方法\n    startCertification() {\n      uni.navigateTo({\n        url: '/pages/store/certification/apply'\n      });\n    },\n    \n    retryCertification() {\n      uni.navigateTo({\n        url: '/pages/store/certification/retry'\n      });\n    },\n    \n    renewCertification() {\n      uni.navigateTo({\n        url: '/pages/store/certification/renew'\n      });\n    },\n    \n    viewQualification(qual) {\n      uni.previewImage({\n        urls: [qual.image],\n        current: qual.image\n      });\n    },\n    \n    editQualification(qual) {\n      uni.navigateTo({\n        url: `/pages/store/qualification/edit?id=${qual.id}`\n      });\n    },\n    \n    deleteQualification(qual) {\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除\"${qual.name}\"资质吗？`,\n        success: (res) => {\n          if (res.confirm) {\n            this.qualifications = this.qualifications.filter(q => q.id !== qual.id);\n            uni.showToast({\n              title: '删除成功',\n              icon: 'success'\n            });\n          }\n        }\n      });\n    },\n    \n    addQualification() {\n          uni.navigateTo({\n        url: '/pages/store/qualification/add'\n      });\n    },\n    \n    toggleReminderMethod(method) {\n      const index = this.reminderMethods.indexOf(method);\n      if (index > -1) {\n        this.reminderMethods.splice(index, 1);\n      } else {\n        this.reminderMethods.push(method);\n      }\n    },\n    \n    saveReminderSettings() {\n      uni.showToast({\n        title: '设置已保存',\n        icon: 'success'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/* 页面基础样式 */\n.store-management-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 导航栏样式 */\n.navbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 44px 16px 10px;\n  background: linear-gradient(135deg, #1677FF, #065DD2);\n  position: relative;\n  z-index: 100;\n}\n\n.navbar-back {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  color: #fff;\n  font-size: 18px;\n  font-weight: 600;\n  flex: 1;\n  text-align: center;\n}\n\n.navbar-right {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n/* 店铺头部卡片样式 */\n.store-header-card {\n  background: #FFFFFF;\n  border-radius: 0 0 20px 20px;\n  padding: 20px;\n  margin-bottom: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n}\n\n.store-header-content {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 15px;\n}\n\n.store-avatar-container {\n  position: relative;\n  margin-right: 15px;\n}\n\n.store-avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 8px;\n  background: #f0f0f0;\n  border: 2px solid #fff;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n}\n\n.avatar-edit-btn {\n  position: absolute;\n  right: -6px;\n  bottom: -6px;\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: #1677FF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);\n}\n\n.edit-icon {\n  width: 14px;\n  height: 14px;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"white\"><path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.edit-icon.small {\n  width: 12px;\n  height: 12px;\n}\n\n.store-header-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.store-name-container {\n  display: flex;\n  align-items: center;\n  margin-bottom: 6px;\n}\n\n.store-name {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-right: 10px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.store-badges {\n  display: flex;\n  align-items: center;\n}\n\n.badge {\n  display: flex;\n  align-items: center;\n  background: rgba(22, 119, 255, 0.1);\n  border-radius: 4px;\n  padding: 2px 6px;\n  margin-left: 6px;\n}\n\n.badge.verified {\n  background: rgba(22, 119, 255, 0.1);\n}\n\n.badge.premium {\n  background: rgba(255, 164, 28, 0.1);\n}\n\n.badge-icon {\n  width: 12px;\n  height: 12px;\n  margin-right: 3px;\n}\n\n.badge-icon.verified {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm-1.06 13.54L7.4 12l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41-5.64 5.66z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.badge-icon.premium {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23FFA41C\"><path d=\"M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.badge-text {\n  font-size: 10px;\n  font-weight: 500;\n  color: #1677FF;\n}\n\n.badge.premium .badge-text {\n  color: #FFA41C;\n}\n\n.store-desc {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 10px;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.store-metrics {\n  display: flex;\n  align-items: center;\n}\n\n.metric-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.metric-value {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n}\n\n.metric-label {\n  font-size: 12px;\n  color: #999;\n}\n\n.metric-divider {\n  width: 1px;\n  height: 20px;\n  background: #E8E8E8;\n  margin: 0 15px;\n}\n\n.store-preview-btn {\n  display: flex;\n  align-items: center;\n  padding: 6px 12px;\n  background: rgba(22, 119, 255, 0.1);\n  border-radius: 16px;\n  margin-left: 10px;\n}\n\n.preview-icon {\n  width: 14px;\n  height: 14px;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n  margin-right: 4px;\n}\n\n.preview-text {\n  font-size: 12px;\n  color: #1677FF;\n}\n\n.store-status-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 0 0;\n  border-top: 1px solid #f0f0f0;\n}\n\n.status-info {\n  display: flex;\n  align-items: center;\n}\n\n.status-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 4px;\n  background: #ccc;\n  margin-right: 5px;\n}\n\n.status-dot.active {\n  background: #52C41A;\n}\n\n.status-text {\n  font-size: 14px;\n  color: #333;\n}\n\n.operation-hours {\n  display: flex;\n  align-items: center;\n}\n\n.hours-label {\n  font-size: 12px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.hours-value {\n  font-size: 14px;\n  color: #333;\n}\n\n.edit-hours-btn {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 5px;\n}\n\n/* 店铺管理导航样式 */\n.store-nav {\n  background: #fff;\n  padding: 10px 0;\n  white-space: nowrap;\n  margin-bottom: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.nav-item {\n  display: inline-block;\n  padding: 8px 20px;\n  margin: 0 5px;\n  font-size: 14px;\n  color: #666;\n  border-radius: 16px;\n  position: relative;\n}\n\n.nav-item.active {\n  color: #1677FF;\n  background: rgba(22, 119, 255, 0.1);\n  font-weight: 500;\n}\n\n.nav-item:first-child {\n  margin-left: 15px;\n}\n\n.nav-item:last-child {\n  margin-right: 15px;\n}\n\n/* 内容区域容器样式 */\n.content-scroll {\n  flex: 1;\n  height: calc(100vh - 220px); /* 减去导航栏、店铺卡片和导航选项卡的高度 */\n}\n\n.content-section {\n  padding: 15px;\n}\n\n.section-header {\n  margin-bottom: 15px;\n}\n\n.section-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.header-subtitle {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 通用数据卡片样式 */\n.data-overview-cards {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  margin-bottom: 20px;\n}\n\n.data-card {\n  width: 48%;\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 10px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.data-value {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.data-label {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 商品服务区块样式 */\n.action-buttons {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n}\n\n.action-button {\n  width: 23%;\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.button-icon {\n  width: 28px;\n  height: 28px;\n  margin-bottom: 8px;\n}\n\n.button-icon.product {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M19 6H5c-1.1 0-2 .9-2 2v9c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 11H5V8h14v9zm-7-5c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.button-icon.category {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.button-icon.service {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M19 14V6c0-1.1-.9-2-2-2H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zm-2 0H3V6h14v8zm-7-1l-5-3v6z\"/><path d=\"M23 15v-1c0-1.1-.9-2-2-2s-2 .9-2 2v1c-.55 0-1 .45-1 1v3c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-3c0-.55-.45-1-1-1zm-3 0v-1c0-.55.45-1 1-1s1 .45 1 1v1h-2z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.button-icon.stock {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.button-text {\n  font-size: 12px;\n  color: #333;\n}\n\n.hot-products-section {\n  margin-bottom: 20px;\n}\n\n.sub-section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.sub-section-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.view-more {\n  font-size: 12px;\n  color: #1677FF;\n}\n\n.product-scroll {\n  white-space: nowrap;\n  padding-bottom: 10px;\n}\n\n.product-card {\n  display: inline-block;\n  width: 140px;\n  background: #fff;\n  border-radius: 12px;\n  margin-right: 10px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.product-image {\n  width: 140px;\n  height: 140px;\n  background: #f5f5f5;\n}\n\n.product-info {\n  padding: 10px;\n}\n\n.product-name {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 6px;\n  white-space: normal;\n  height: 40px;\n  line-height: 20px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.product-price {\n  font-size: 16px;\n  font-weight: 600;\n  color: #FF5000;\n  margin-bottom: 8px;\n}\n\n.product-stats {\n  display: flex;\n  justify-content: space-between;\n  font-size: 10px;\n  color: #999;\n}\n\n.stats-item {\n  white-space: nowrap;\n}\n\n.quick-actions {\n  display: flex;\n  justify-content: space-between;\n}\n\n.action-card {\n  width: 32%;\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.action-icon {\n  width: 32px;\n  height: 32px;\n  margin-bottom: 10px;\n}\n\n.action-icon.add {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.action-icon.batch {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M20 2H8c-1.1 0-1.99.9-1.99 2L6 20c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4c0-.55.45-1 1-1h1l3.6 3.6L7 10.1l4.9 4.9 3.6-3.6L20 16l0 4L6 4z\"/><path d=\"M15.1 7.9l2 2 3-4-4 3z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.action-icon.import {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.action-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.action-desc {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 店铺形象区块样式 */\n.cover-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.sub-section-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 15px;\n}\n\n.cover-preview {\n  position: relative;\n  width: 100%;\n  height: 150px;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.cover-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.cover-edit-btn {\n  position: absolute;\n  right: 15px;\n  bottom: 15px;\n  height: 32px;\n  border-radius: 16px;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  padding: 0 12px;\n}\n\n.edit-cover-icon {\n  width: 16px;\n  height: 16px;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"white\"><path d=\"M19 7v2.99s-1.99.01-2 0V7h-3s.01-1.99 0-2h3V2h2v3h3v2h-3zm-3 4V8h-3V5H5c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-8h-3zM5 19l3-4 2 3 3-4 4 5H5z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n  margin-right: 6px;\n}\n\n.edit-text {\n  font-size: 12px;\n  color: #fff;\n}\n\n.style-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.style-cards {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n\n.style-card {\n  width: 48%;\n  margin-bottom: 15px;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.style-card.active {\n  box-shadow: 0 0 0 2px #1677FF;\n}\n\n.style-preview {\n  width: 100%;\n  height: 90px;\n  object-fit: cover;\n}\n\n.style-info {\n  padding: 10px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: #fff;\n}\n\n.style-name {\n  font-size: 14px;\n  color: #333;\n}\n\n.style-check {\n  width: 18px;\n  height: 18px;\n  border-radius: 9px;\n  background: #1677FF;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.check-icon {\n  width: 10px;\n  height: 6px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(-45deg);\n  margin-top: -2px;\n}\n\n.gallery-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.gallery-hint {\n  font-size: 12px;\n  color: #666;\n  margin: -10px 0 15px;\n  display: block;\n}\n\n.gallery-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: -5px;\n}\n\n.gallery-item {\n  width: calc(33.33% - 10px);\n  margin: 5px;\n  height: 100px;\n  border-radius: 8px;\n  overflow: hidden;\n  position: relative;\n}\n\n.gallery-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.gallery-tag {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  background: rgba(0, 0, 0, 0.5);\n  color: #fff;\n  font-size: 10px;\n  padding: 4px 8px;\n  text-align: center;\n}\n\n.gallery-add {\n  width: calc(33.33% - 10px);\n  margin: 5px;\n  height: 100px;\n  border-radius: 8px;\n  border: 1px dashed #ddd;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: #fafafa;\n}\n\n.add-icon {\n  width: 24px;\n  height: 24px;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23999999\"><path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n  margin-bottom: 6px;\n}\n\n.add-text {\n  font-size: 12px;\n  color: #999;\n}\n\n.video-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.video-container {\n  width: 100%;\n  height: 200px;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.store-video {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.video-upload {\n  width: 100%;\n  height: 150px;\n  border-radius: 8px;\n  border: 1px dashed #ddd;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: #fafafa;\n  padding: 15px;\n}\n\n.upload-icon {\n  width: 32px;\n  height: 32px;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23999999\"><path d=\"M18 4l2 4h-3l-2-4h-2l2 4h-3l-2-4H8l2 4H7L5 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4h-4z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n  margin-bottom: 10px;\n}\n\n.upload-text {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 6px;\n}\n\n.upload-hint {\n  font-size: 12px;\n  color: #999;\n  text-align: center;\n}\n\n.story-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.story-editor {\n  position: relative;\n  margin-bottom: 15px;\n}\n\n.story-textarea {\n  width: 100%;\n  height: 150px;\n  background: #F8FAFC;\n  border: 1px solid #EAEAEA;\n  border-radius: 8px;\n  padding: 12px;\n  font-size: 14px;\n  color: #333;\n}\n\n.text-counter {\n  position: absolute;\n  right: 10px;\n  bottom: 10px;\n  font-size: 12px;\n  color: #999;\n}\n\n.save-story-btn {\n  width: 100%;\n  height: 44px;\n  background: #1677FF;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.save-text {\n  font-size: 16px;\n  font-weight: 500;\n  color: #fff;\n}\n\n/* 位置与配送区块样式 */\n.location-card {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.location-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 15px;\n  display: block;\n}\n\n.address-container {\n  margin-bottom: 15px;\n}\n\n.address-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.address-detail {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.location-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  flex-shrink: 0;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.address-text {\n  font-size: 14px;\n  color: #333;\n  line-height: 20px;\n}\n\n.address-actions {\n  display: flex;\n}\n\n.action-btn {\n  height: 32px;\n  padding: 0 12px;\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  margin-right: 10px;\n}\n\n.action-btn.edit {\n  background: #F0F7FF;\n}\n\n.action-btn.navigate {\n  background: #E6F7FF;\n}\n\n.btn-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 4px;\n}\n\n.btn-icon.edit {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.btn-icon.navigate {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M12 2L4.5 20.29l.71.71L12 18l6.79 3 .71-.71L12 2z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.btn-text {\n  font-size: 12px;\n  color: #1677FF;\n}\n\n.map-container {\n  position: relative;\n  height: 150px;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.map-preview {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.map-marker {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.marker-icon {\n  width: 32px;\n  height: 32px;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23D83232\"><path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.map-tools {\n  position: absolute;\n  right: 10px;\n  bottom: 10px;\n  display: flex;\n}\n\n.map-tool-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 16px;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 8px;\n}\n\n.tool-icon {\n  width: 20px;\n  height: 20px;\n}\n\n.tool-icon.map {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"white\"><path d=\"M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.tool-icon.preview {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"white\"><path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.tool-text {\n  display: none;\n}\n\n.delivery-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.section-subtitle {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 15px;\n  display: block;\n}\n\n.delivery-toggle {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.toggle-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.delivery-settings {\n  margin-top: 15px;\n}\n\n.radius-setting {\n  margin-bottom: 20px;\n}\n\n.setting-label {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.radius-slider-container {\n  padding: 0 10px;\n  position: relative;\n}\n\n.radius-slider {\n  margin: 10px 0;\n}\n\n.radius-value {\n  position: absolute;\n  right: 10px;\n  top: -5px;\n  font-size: 12px;\n  color: #666;\n}\n\n.delivery-fee-settings {\n  margin-bottom: 20px;\n}\n\n.fee-type-toggle {\n  display: flex;\n  background: #F5F7FA;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 15px;\n}\n\n.toggle-option {\n  flex: 1;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #666;\n  position: relative;\n}\n\n.toggle-option.active {\n  color: #fff;\n  background: #1677FF;\n}\n\n.option-text {\n  position: relative;\n  z-index: 1;\n}\n\n.fee-input-container {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.fee-input-label {\n  font-size: 14px;\n  color: #333;\n  margin-right: 10px;\n}\n\n.fee-input-wrap {\n  flex: 1;\n  position: relative;\n}\n\n.fee-input {\n  width: 100%;\n  height: 40px;\n  background: #F8FAFC;\n  border: 1px solid #EAEAEA;\n  border-radius: 8px;\n  padding: 0 40px 0 15px;\n  font-size: 14px;\n  color: #333;\n}\n\n.fee-unit {\n  position: absolute;\n  right: 15px;\n  top: 50%;\n  transform: translateY(-50%);\n  font-size: 14px;\n  color: #999;\n}\n\n.distance-fee-table {\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #EAEAEA;\n  margin-bottom: 15px;\n}\n\n.table-header {\n  display: flex;\n  background: #F8FAFC;\n  height: 40px;\n  border-bottom: 1px solid #EAEAEA;\n}\n\n.header-cell {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #666;\n  text-align: center;\n}\n\n.header-cell:not(:last-child) {\n  border-right: 1px solid #EAEAEA;\n}\n\n.table-row {\n  display: flex;\n  height: 50px;\n  background: #fff;\n}\n\n.table-row:not(:last-child) {\n  border-bottom: 1px solid #EAEAEA;\n}\n\n.row-cell {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #333;\n}\n\n.row-cell:not(:last-child) {\n  border-right: 1px solid #EAEAEA;\n}\n\n.row-cell.distance {\n  justify-content: flex-start;\n  padding-left: 15px;\n}\n\n.tier-fee-input {\n  width: 80%;\n  height: 36px;\n  background: #F8FAFC;\n  border: 1px solid #EAEAEA;\n  border-radius: 4px;\n  padding: 0 10px;\n  text-align: center;\n}\n\n.row-action {\n  width: 28px;\n  height: 28px;\n  border-radius: 14px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.row-action.delete {\n  background: #FFF0F0;\n}\n\n.delete-icon {\n  width: 16px;\n  height: 16px;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23FF4D4F\"><path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.add-tier-btn {\n  height: 40px;\n  background: #F0F7FF;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-text {\n  font-size: 14px;\n  color: #1677FF;\n  margin-left: 5px;\n}\n\n.minimum-order {\n  margin-top: 20px;\n}\n\n.minimum-input-wrap {\n  position: relative;\n  margin-bottom: 8px;\n}\n\n.minimum-input {\n  width: 100%;\n  height: 40px;\n  background: #F8FAFC;\n  border: 1px solid #EAEAEA;\n  border-radius: 8px;\n  padding: 0 40px 0 15px;\n  font-size: 14px;\n  color: #333;\n}\n\n.minimum-unit {\n  position: absolute;\n  right: 15px;\n  top: 50%;\n  transform: translateY(-50%);\n  font-size: 14px;\n  color: #999;\n}\n\n.minimum-hint {\n  font-size: 12px;\n  color: #999;\n}\n\n.empty-delivery, .empty-pickup {\n  padding: 30px 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.empty-icon {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 15px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n.pickup-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.section-header-with-toggle {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.toggle-container {\n  display: flex;\n  align-items: center;\n}\n\n.pickup-instructions {\n  margin-bottom: 15px;\n}\n\n.pickup-textarea {\n  width: 100%;\n  height: 100px;\n  background: #F8FAFC;\n  border: 1px solid #EAEAEA;\n  border-radius: 8px;\n  padding: 12px;\n  font-size: 14px;\n  color: #333;\n}\n\n/* 认证与资质区块样式 */\n.certification-card {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.certification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.card-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.cert-status {\n  padding: 4px 10px;\n  border-radius: 12px;\n  font-size: 12px;\n}\n\n.cert-status.uncertified {\n  background: #F5F5F5;\n}\n\n.cert-status.pending {\n  background: #FCF5EB;\n}\n\n.cert-status.certified {\n  background: #F6FFED;\n}\n\n.cert-status.failed {\n  background: #FFF2F0;\n}\n\n.cert-status.uncertified .status-text {\n  color: #999;\n}\n\n.cert-status.pending .status-text {\n  color: #FFA41C;\n}\n\n.cert-status.certified .status-text {\n  color: #52C41A;\n}\n\n.cert-status.failed .status-text {\n  color: #FF4D4F;\n}\n\n.certification-content {\n  padding: 15px 0;\n}\n\n.uncertified, .in-progress, .cert-failed {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px 0;\n}\n\n.uncert-icon, .progress-icon, .failed-icon {\n  width: 60px;\n  height: 60px;\n  margin-bottom: 15px;\n}\n\n.uncert-icon {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23CCCCCC\"><path d=\"M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm-1.06 13.54L7.4 12l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41-5.64 5.66z\" opacity=\"0.3\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.progress-icon {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23FFA41C\"><path d=\"M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.failed-icon {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23FF4D4F\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.uncert-title, .progress-title, .failed-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.uncert-desc, .progress-desc, .failed-reason {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 20px;\n  text-align: center;\n  padding: 0 20px;\n}\n\n.start-cert-btn, .retry-cert-btn {\n  width: 140px;\n  height: 40px;\n  background: #1677FF;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #fff;\n}\n\n.cert-progress {\n  width: 80%;\n  margin-top: 10px;\n}\n\n.progress-bar {\n  height: 8px;\n  background: #F0F0F0;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-bottom: 8px;\n}\n\n.progress-fill {\n  height: 100%;\n  background: #FFA41C;\n  border-radius: 4px;\n}\n\n.progress-text {\n  font-size: 12px;\n  color: #FFA41C;\n  text-align: center;\n}\n\n.certified {\n  padding: 10px 0;\n}\n\n.cert-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.cert-icon {\n  width: 40px;\n  height: 40px;\n  margin-right: 15px;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%2352C41A\"><path d=\"M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm-1.06 13.54L7.4 12l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41-5.64 5.66z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.cert-details {\n  flex: 1;\n}\n\n.cert-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.cert-time, .cert-number {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.renewal-info {\n  display: flex;\n  align-items: center;\n  padding: 10px 15px;\n  background: #FFF7E6;\n  border-radius: 8px;\n}\n\n.alert-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 10px;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23FFA41C\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.renewal-text {\n  flex: 1;\n  font-size: 12px;\n  color: #FA8C16;\n}\n\n.renew-btn {\n  width: 60px;\n  height: 30px;\n  background: #FFA41C;\n  border-radius: 15px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  color: #fff;\n}\n\n.qualification-section {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.no-quals {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 30px 0;\n}\n\n.no-quals-img {\n  width: 100px;\n  height: 100px;\n  margin-bottom: 15px;\n}\n\n.no-quals-text {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.no-quals-desc {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 20px;\n}\n\n.qual-list {\n  margin-bottom: 15px;\n}\n\n.qual-item {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  border-bottom: 1px solid #F0F0F0;\n}\n\n.qual-item:last-child {\n  border-bottom: none;\n}\n\n.qual-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  margin-right: 15px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.qual-icon.food {\n  background: linear-gradient(135deg, #FFA62E, #EA4D2C);\n}\n\n.qual-icon.health {\n  background: linear-gradient(135deg, #36D1DC, #5B86E5);\n}\n\n.qual-info {\n  flex: 1;\n}\n\n.qual-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.qual-desc {\n  font-size: 12px;\n  color: #999;\n}\n\n.qual-status {\n  padding: 4px 8px;\n  border-radius: 10px;\n  font-size: 10px;\n  margin: 0 10px;\n}\n\n.qual-status.approved {\n  background: #F6FFED;\n  color: #52C41A;\n}\n\n.qual-status.pending {\n  background: #FCF5EB;\n  color: #FFA41C;\n}\n\n.qual-status.rejected {\n  background: #FFF2F0;\n  color: #FF4D4F;\n}\n\n.qual-status.expired {\n  background: #F5F5F5;\n  color: #999;\n}\n\n.qual-actions {\n  display: flex;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 16px;\n  margin-left: 5px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-btn.view {\n  background: #E6F7FF;\n}\n\n.action-btn.edit {\n  background: #F0F7FF;\n}\n\n.action-btn.delete {\n  background: #FFF0F0;\n}\n\n.btn-icon.view {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.btn-icon.edit {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%231677FF\"><path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.btn-icon.delete {\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23FF4D4F\"><path d=\"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z\"/></svg>');\n  background-repeat: no-repeat;\n  background-size: cover;\n}\n\n.add-qual-btn {\n  height: 44px;\n  background: #F0F7FF;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.expiration-reminder {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);\n}\n\n.reminder-card {\n  background: #FAFAFA;\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.reminder-item {\n  margin-bottom: 15px;\n}\n\n.reminder-label {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.reminder-input-wrap {\n  position: relative;\n}\n\n.reminder-input {\n  width: 100%;\n  height: 40px;\n  background: #fff;\n  border: 1px solid #EAEAEA;\n  border-radius: 8px;\n  padding: 0 40px 0 15px;\n  font-size: 14px;\n  color: #333;\n}\n\n.reminder-unit {\n  position: absolute;\n  right: 15px;\n  top: 50%;\n  transform: translateY(-50%);\n  font-size: 14px;\n  color: #999;\n}\n\n.reminder-checkboxes {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.checkbox-item {\n  display: flex;\n  align-items: center;\n  margin-right: 20px;\n  margin-bottom: 10px;\n}\n\n.checkbox {\n  width: 18px;\n  height: 18px;\n  border-radius: 4px;\n  border: 1px solid #D9D9D9;\n  margin-right: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #fff;\n}\n\n.checkbox.checked {\n  background: #1677FF;\n  border-color: #1677FF;\n}\n\n.check-mark {\n  width: 10px;\n  height: 5px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(-45deg);\n  margin-top: -2px;\n}\n\n.checkbox-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.save-reminder-btn {\n  width: 100%;\n  height: 40px;\n  background: #1677FF;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 15px;\n}\n\n/* 底部保存按钮 */\n.save-btn {\n  width: calc(100% - 30px);\n  height: 44px;\n  background: #1677FF;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto 20px;\n  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.2);\n}\n\n.save-text {\n  font-size: 16px;\n  font-weight: 500;\n  color: #fff;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity/pages/square.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAkmBA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MAEjD,WAAW;AAAA,QACT,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,QACP,SAAS;AAAA,QACT,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,QACpB,qBAAqB;AAAA;AAAA,QACrB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,MACjB;AAAA,MAED,cAAc;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,UAAU;AAAA,MACX;AAAA;AAAA,MAGD,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,MACD;AAAA;AAAA,MAGD,aAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACD;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACD;AAAA;AAAA,MAGD,kBAAkB;AAAA,QAChB,EAAE,UAAU,GAAG,OAAO,IAAK;AAAA,QAC3B,EAAE,UAAU,GAAG,OAAO,IAAK;AAAA,QAC3B,EAAE,UAAU,IAAI,OAAO,KAAK;AAAA,MAC7B;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,MACD;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,WAAW;AAAA,QACX,aAAa;AAAA,QACb,UAAU;AAAA,MACX;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,WAAW;AAAA,MACZ;AAAA;AAAA,MAGD,iBAAiB,CAAC,OAAO,KAAK;AAAA,IAChC;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA;AAAA,IAGD,YAAY,GAAG;AACb,WAAK,aAAa;AAClB,iBAAW,MAAM;AAEf,aAAK,aAAa;AAAA,MACnB,GAAE,IAAI;AAAA,IACR;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,oBAAoB;AAClBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAEhB,qBAAW,MAAM;AACf,iBAAK,UAAU,SAAS,IAAI,cAAc,CAAC;AAC3CA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACF,GAAE,GAAI;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,qBAAqB;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AACjBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAEhB,qBAAW,MAAM;AACf,iBAAK,UAAU,aAAa,IAAI,cAAc,CAAC;AAC/CA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACF,GAAE,GAAI;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB,SAAS;AACxB,WAAK,UAAU,UAAU;AAAA,IAC1B;AAAA,IAED,kBAAkB;AAChBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAEhB,qBAAW,MAAM;AACf,iBAAK,aAAa,KAAK;AAAA,cACrB,IAAI,KAAK,aAAa,SAAS;AAAA,cAC/B,KAAK,IAAI,cAAc,CAAC;AAAA,cACxB,KAAK;AAAA,YACP,CAAC;AACDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACF,GAAE,GAAI;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,cAAc;AACZA,oBAAAA,MAAI,YAAY;AAAA,QACd,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS,CAAC,QAAQ;AAEhB,qBAAW,MAAM;AACf,iBAAK,UAAU,iBAAiB,IAAI;AACpCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACF,GAAE,IAAI;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,mBAAmB;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,mBAAmB;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,eAAe,GAAG;AAChB,WAAK,UAAU,mBAAmB,EAAE,OAAO;AAAA,IAC5C;AAAA,IAED,qBAAqB,GAAG;AACtB,WAAK,UAAU,iBAAiB,EAAE,OAAO;AAAA,IAC1C;AAAA,IAED,aAAa;AACX,UAAI,KAAK,iBAAiB,UAAU,GAAG;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,YAAM,WAAW,KAAK,iBAAiB,KAAK,iBAAiB,SAAS,CAAC;AACvE,WAAK,iBAAiB,KAAK;AAAA,QACzB,UAAU,SAAS,WAAW;AAAA,QAC9B,QAAQ,WAAW,SAAS,KAAK,IAAI,GAAG,SAAS;AAAA,MACnD,CAAC;AAAA,IACF;AAAA,IAED,cAAc,OAAO;AACnB,WAAK,iBAAiB,OAAO,OAAO,CAAC;AAAA,IACtC;AAAA,IAED,aAAa,GAAG;AACd,WAAK,UAAU,iBAAiB,EAAE,OAAO;AAAA,IAC1C;AAAA,IAED,0BAA0B;AACxBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AAAA;AAAA,IAGD,qBAAqB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,qBAAqB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,qBAAqB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB,MAAM;AACtBA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM,CAAC,KAAK,KAAK;AAAA,QACjB,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB,MAAM;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,sCAAsC,KAAK,EAAE;AAAA,MACpD,CAAC;AAAA,IACF;AAAA,IAED,oBAAoB,MAAM;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,IAAI;AAAA,QAC3B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,iBAAiB,KAAK,eAAe,OAAO,OAAK,EAAE,OAAO,KAAK,EAAE;AACtEA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,mBAAmB;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACjB,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,qBAAqB,QAAQ;AAC3B,YAAM,QAAQ,KAAK,gBAAgB,QAAQ,MAAM;AACjD,UAAI,QAAQ,IAAI;AACd,aAAK,gBAAgB,OAAO,OAAO,CAAC;AAAA,aAC/B;AACL,aAAK,gBAAgB,KAAK,MAAM;AAAA,MAClC;AAAA,IACD;AAAA,IAED,uBAAuB;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpiCA,GAAG,WAAW,eAAe;"}