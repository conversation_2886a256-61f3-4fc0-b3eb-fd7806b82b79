<template>
  <view class="location-bar" @click="refreshLocationHandler">
    <view class="location-icon">
      <text class="iconfont icon-location"></text>
    </view>
    <view class="location-info">
      <text v-if="locationLoading" class="location-text">定位中...</text>
      <text v-else-if="currentLocation" class="location-text">
        {{ currentLocation.address || currentLocation.location || '未知位置' }}
      </text>
      <text v-else class="location-text">点击获取位置</text>
    </view>
    <view class="location-refresh">
      <text class="iconfont icon-refresh" :class="{ 'refreshing': locationLoading }"></text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { getCurrentLocation, refreshLocation } from '../utils/locationService.js';

// 定位状态
const locationLoading = ref(false);
// 当前位置
const currentLocation = ref(null);

// 判断是否为默认位置
const isDefaultLocation = computed(() => {
  if (!currentLocation.value) return false;
  // 检查是否是默认经纬度
  return currentLocation.value.latitude === 36.313076 && 
         currentLocation.value.longitude === 114.347312;
});

// 获取位置信息
const getLocationInfo = async () => {
  currentLocation.value = getCurrentLocation();
};

// 刷新位置
const refreshLocationHandler = async () => {
  if (locationLoading.value) return;
  
  locationLoading.value = true;
  try {
    const location = await refreshLocation();
    currentLocation.value = location;
  } catch (error) {
    console.error('刷新位置失败:', error);
  } finally {
    locationLoading.value = false;
  }
};

// 监听位置更新事件
const locationUpdateHandler = (updatedLocation) => {
  currentLocation.value = updatedLocation;
};

onMounted(() => {
  getLocationInfo();
  // 注册位置更新事件监听
  uni.$on('location_updated', locationUpdateHandler);
});

onUnmounted(() => {
  // 移除位置更新事件监听
  uni.$off('location_updated', locationUpdateHandler);
});
</script>

<style lang="scss">
.location-bar {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 28rpx;
  height: 56rpx;
  padding: 0 20rpx;
}

.location-icon {
  font-size: 32rpx;
  color: #ffffff;
  margin-right: 10rpx;
}

.location-info {
  flex: 1;
  overflow: hidden;
}

.location-text {
  font-size: 26rpx;
  color: #ffffff;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.default-label {
  font-size: 22rpx;
  color: #ffff99;
  margin-left: 6rpx;
}

.location-refresh {
  padding-left: 16rpx;
  font-size: 28rpx;
  color: #ffffff;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refreshing {
  display: inline-block;
  animation: rotating 1.5s linear infinite;
}
</style> 