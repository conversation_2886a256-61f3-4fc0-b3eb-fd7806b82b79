/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-80f2f579, html.data-v-80f2f579, #app.data-v-80f2f579, .index-container.data-v-80f2f579 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 磁县同城信息模块 - 苹果风格设计 */
.info-module.data-v-80f2f579 {
  margin: 24rpx;
  border-radius: 35rpx;
  background-color: #FFFFFF;
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12), 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  will-change: transform;
}

/* 模块标题区 */
.info-header.data-v-80f2f579 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}
.info-title-container.data-v-80f2f579 {
  display: flex;
  align-items: center;
}
.info-title-bar.data-v-80f2f579 {
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(180deg, #007AFF, #5AC8FA);
  border-radius: 3rpx;
  margin-right: 16rpx;
}
.info-title.data-v-80f2f579 {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  letter-spacing: 0.8rpx;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.info-badge.data-v-80f2f579 {
  margin-left: 12rpx;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
}
.info-badge-text.data-v-80f2f579 {
  font-size: 20rpx;
  color: #007AFF;
}
.info-publish-btn.data-v-80f2f579 {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}
.info-publish-icon.data-v-80f2f579 {
  color: #FFFFFF;
  margin-right: 8rpx;
}
.info-publish-text.data-v-80f2f579 {
  font-size: 26rpx;
  color: #FFFFFF;
  font-weight: 500;
}

/* 标签导航栏 */
.info-tabs-container.data-v-80f2f579 {
  width: 100%;
  background-color: #FFFFFF;
  padding: 10rpx 0;
  z-index: 100;
  transition: all 0.3s;
}
.sticky-tabs.data-v-80f2f579 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.info-tabs.data-v-80f2f579 {
  white-space: nowrap;
  width: 100%;
  height: 80rpx;
}
.info-tab.data-v-80f2f579 {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin: 0 8rpx;
  position: relative;
  transition: all 0.3s;
}
.info-tab.data-v-80f2f579:first-child {
  margin-left: 20rpx;
}
.tab-icon.data-v-80f2f579 {
  margin-right: 6rpx;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tab-text.data-v-80f2f579 {
  font-size: 26rpx;
  color: #636366;
  font-weight: 500;
}
.info-tab.active .tab-text.data-v-80f2f579 {
  color: #007AFF;
  font-weight: 600;
}
.info-tab.active .tab-icon.data-v-80f2f579 {
  color: #007AFF;
}
.tab-line.data-v-80f2f579 {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 4rpx;
  background: #007AFF;
  border-radius: 2rpx;
}
.filter-tab.data-v-80f2f579 {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin: 0 20rpx;
  background: linear-gradient(135deg, #F2F2F7, #E5E5EA);
  border-radius: 35rpx;
  color: #636366;
}
.filter-icon.data-v-80f2f579 {
  margin-right: 6rpx;
  color: #636366;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-text.data-v-80f2f579 {
  font-size: 26rpx;
  font-weight: 500;
}

/* 占位元素 */
.tabs-placeholder.data-v-80f2f579 {
  width: 100%;
}

/* 置顶标识 - 重新设计 */
.top-indicator.data-v-80f2f579 {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
}
.top-badge.data-v-80f2f579 {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-bottom-left-radius: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}
.paid-badge.data-v-80f2f579 {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  box-shadow: 0 8rpx 20rpx rgba(255, 59, 48, 0.2), 0 4rpx 8rpx rgba(255, 59, 48, 0.1);
}
.ad-badge.data-v-80f2f579 {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.2), 0 4rpx 8rpx rgba(0, 122, 255, 0.1);
}
.top-badge-icon.data-v-80f2f579 {
  color: #FFFFFF;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.top-badge-icon svg.data-v-80f2f579 {
  width: 14px;
  height: 14px;
}
.top-badge-text.data-v-80f2f579 {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 置顶提示横幅 */
.top-banner.data-v-80f2f579 {
  margin: 20rpx 24rpx 0;
  border-radius: 24rpx;
  background: linear-gradient(135deg, rgba(255, 204, 0, 0.15), rgba(255, 149, 0, 0.15));
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(255, 149, 0, 0.1);
}
.top-banner-content.data-v-80f2f579 {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
}
.top-banner-icon.data-v-80f2f579 {
  color: #FF9500;
  margin-right: 12rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.top-banner-text.data-v-80f2f579 {
  flex: 1;
  font-size: 26rpx;
  color: #FF9500;
  font-weight: 500;
}
.top-banner-btn.data-v-80f2f579 {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.2);
}
.top-banner-btn text.data-v-80f2f579 {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 600;
}

/* 信息列表 */
.info-list.data-v-80f2f579 {
  padding: 16rpx 24rpx 24rpx;
}

/* 信息项 - 高级感苹果风格重新设计 */
.info-item.data-v-80f2f579 {
  margin-bottom: 24rpx;
  border-radius: 35rpx;
  background-color: #FFFFFF;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  border: none;
  overflow: hidden;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
  transform: translateZ(0) perspective(1000px) rotateX(0deg);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  padding: 28rpx 24rpx;
}
.info-item.data-v-80f2f579:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 置顶项特殊样式 */
.topped-item.data-v-80f2f579 {
  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);
  box-shadow: 0 14rpx 36rpx rgba(255, 149, 0, 0.16), 0 6rpx 16rpx rgba(255, 149, 0, 0.1);
  border-left: 6rpx solid #FF9500;
}
.paid-top.data-v-80f2f579 {
  background: linear-gradient(135deg, #FFFFFF, #FFF9F0);
  border-left: 6rpx solid #FF9500;
  box-shadow: 0 14rpx 36rpx rgba(255, 149, 0, 0.16), 0 6rpx 16rpx rgba(255, 149, 0, 0.1);
}
.ad-top.data-v-80f2f579 {
  background: linear-gradient(135deg, #FFFFFF, #F0F7FF);
  border-left: 6rpx solid #007AFF;
  box-shadow: 0 14rpx 36rpx rgba(0, 122, 255, 0.16), 0 6rpx 16rpx rgba(0, 122, 255, 0.1);
}

/* 内容区 - 高级感重新设计 */
.info-content.data-v-80f2f579 {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 删除重复的卡片样式 */
/* 确保卡片大小不变 */
/* .info-item {
  padding: 24rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
} */
/* 头部信息 - 高级感重新设计 */
.info-header.data-v-80f2f579 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.info-category.data-v-80f2f579 {
  background: linear-gradient(135deg, #0062CC, #0091E6);
  border-radius: 10rpx;
  padding: 4rpx 12rpx;
  border: 0.5rpx solid rgba(0, 98, 204, 0.2);
  box-shadow: 0 4rpx 8rpx rgba(0, 98, 204, 0.15);
  transform: translateZ(0);
  margin-right: 12rpx;
  display: inline-flex;
  align-items: center;
}
.topped-category.data-v-80f2f579 {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  border: 0.5rpx solid rgba(255, 59, 48, 0.2);
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.15);
}
.category-text.data-v-80f2f579 {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.topped-category .category-text.data-v-80f2f579 {
  color: #FFFFFF;
}
.info-time.data-v-80f2f579 {
  font-size: 22rpx;
  color: #8E8E93;
  font-weight: 400;
  background-color: rgba(142, 142, 147, 0.08);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 主要内容 - 高级感重新设计 */
.info-main.data-v-80f2f579 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 12rpx;
  margin-bottom: 20rpx;
  gap: 20rpx;
  width: 100%;
}
.info-content-wrapper.data-v-80f2f579 {
  flex: 1;
  overflow: hidden;
  min-width: 0;
  /* 确保文本可以正确换行 */
}
.info-text.data-v-80f2f579 {
  font-size: 30rpx;
  color: #1C1C1E;
  line-height: 1.5;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
  /* 确保长文本可以正确换行 */
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.bold-text.data-v-80f2f579 {
  font-weight: 600;
  color: #000000;
}

/* 右侧图片样式 */
.info-images-right.data-v-80f2f579 {
  width: 180rpx;
  height: 180rpx;
  flex-shrink: 0;
  position: relative;
  margin-left: 12rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}
.info-image-container-right.data-v-80f2f579 {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #f2f2f7;
  position: relative;
}
.info-image.data-v-80f2f579 {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16rpx;
  transition: transform 0.3s ease;
}
.info-image.data-v-80f2f579:active {
  transform: scale(1.05);
}
.image-count-right.data-v-80f2f579 {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  z-index: 2;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

/* 隐藏旧的图片布局 */
.info-images.data-v-80f2f579 {
  display: none;
}

/* 内容标签 - 新增设计元素 */
.info-tags.data-v-80f2f579 {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4rpx 16rpx;
}
.info-tag.data-v-80f2f579 {
  background-color: rgba(0, 122, 255, 0.08);
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  margin: 6rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05), 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}
.info-tag-text.data-v-80f2f579 {
  font-size: 22rpx;
  color: #007AFF;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 图片预览 - 高级感重新设计 */
.info-images.data-v-80f2f579 {
  display: flex;
  flex-wrap: wrap;
  margin: 14rpx -4rpx 0;
  /* 从20rpx -6rpx 0减少到14rpx -4rpx 0 */
  position: relative;
}
.info-image-container.data-v-80f2f579 {
  width: calc(33.33% - 8rpx);
  /* 从calc(33.33% - 12rpx)减少到calc(33.33% - 8rpx) */
  height: 160rpx;
  /* 从200rpx减少到160rpx */
  margin: 4rpx;
  /* 从6rpx减少到4rpx */
  border-radius: 16rpx;
  /* 从24rpx减少到16rpx */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  /* 减轻阴影效果 */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  /* 从2rpx减少到1rpx */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa, #e4e8f0);
  overflow: hidden;
  position: relative;
}
.info-image-container.data-v-80f2f579:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.info-image-svg.data-v-80f2f579 {
  width: 40%;
  height: 40%;
  color: #8E8E93;
  opacity: 0.5;
  position: absolute;
  z-index: 1;
}
.single-image.data-v-80f2f579 {
  width: 70%;
  /* 从75%减少到70% */
  height: 240rpx;
  /* 从320rpx减少到240rpx */
}
.double-image.data-v-80f2f579 {
  width: calc(50% - 8rpx);
  /* 从calc(50% - 12rpx)减少到calc(50% - 8rpx) */
  height: 200rpx;
  /* 从260rpx减少到200rpx */
}
.image-count.data-v-80f2f579 {
  position: absolute;
  right: 16rpx;
  bottom: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 600;
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 信息状态标签 - 新增设计元素 */
.info-status.data-v-80f2f579 {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  z-index: 2;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}
.info-status-text.data-v-80f2f579 {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

/* 底部信息 - 高级感重新设计 */
.info-footer.data-v-80f2f579 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 0.5rpx solid rgba(60, 60, 67, 0.1);
  position: relative;
  z-index: 1;
  margin-top: 4rpx;
}
.info-user.data-v-80f2f579 {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: inset 0 0.5rpx 2rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5rpx);
  -webkit-backdrop-filter: blur(5rpx);
}
.user-avatar-container.data-v-80f2f579 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 10rpx;
  border: 0.5rpx solid #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 122, 255, 0.1);
  overflow: hidden;
}
.user-avatar-svg.data-v-80f2f579 {
  width: 24px;
  height: 24px;
  color: #007AFF;
}
.user-name.data-v-80f2f579 {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.info-stats.data-v-80f2f579 {
  display: flex;
  align-items: center;
}
.info-views.data-v-80f2f579 {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: inset 0 0.5rpx 2rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5rpx);
  -webkit-backdrop-filter: blur(5rpx);
}
.view-icon.data-v-80f2f579 {
  color: #636366;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.view-icon svg.data-v-80f2f579 {
  width: 14px;
  height: 14px;
}
.view-count.data-v-80f2f579 {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 红包信息 - 高级感重新设计 */
.info-redpacket.data-v-80f2f579 {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 24rpx;
  padding: 8rpx 16rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 59, 48, 0.2), 0 2rpx 6rpx rgba(255, 59, 48, 0.1);
  transform: translateZ(0);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
}
.info-redpacket.data-v-80f2f579:active {
  transform: scale(0.96);
  box-shadow: 0 3rpx 8rpx rgba(255, 59, 48, 0.15);
}
.redpacket-icon.data-v-80f2f579 {
  color: #FFFFFF;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.redpacket-icon svg.data-v-80f2f579 {
  width: 14px;
  height: 14px;
}
.redpacket-amount.data-v-80f2f579 {
  font-size: 26rpx;
  color: #FFFFFF;
  font-weight: 700;
  margin-right: 10rpx;
  letter-spacing: 1rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.redpacket-btn.data-v-80f2f579 {
  background-color: #FFFFFF;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
.redpacket-btn text.data-v-80f2f579 {
  font-size: 22rpx;
  color: #FF3B30;
  font-weight: 700;
}

/* 互动按钮组 - 新增设计元素 */
.info-actions.data-v-80f2f579 {
  display: flex;
  justify-content: space-around;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 0.5rpx solid rgba(60, 60, 67, 0.1);
  position: relative;
}
.info-actions.data-v-80f2f579:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.5rpx;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
}
.info-action.data-v-80f2f579 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 20rpx;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 24rpx;
  box-shadow: inset 0 0.5rpx 2rpx rgba(0, 0, 0, 0.05);
  margin: 0 6rpx;
}
.info-action.data-v-80f2f579:active {
  transform: scale(0.92);
  background-color: rgba(0, 0, 0, 0.04);
}
.info-action-icon.data-v-80f2f579 {
  color: #8E8E93;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.info-action-icon svg.data-v-80f2f579 {
  width: 16px;
  height: 16px;
}
.info-action-text.data-v-80f2f579 {
  font-size: 24rpx;
  color: #8E8E93;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 广告位 */
.ad-banner.data-v-80f2f579 {
  margin-bottom: 24rpx;
  border-radius: 35rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.12), 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateZ(0);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
}
.ad-banner.data-v-80f2f579:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}
.ad-content.data-v-80f2f579 {
  width: 100%;
  position: relative;
}
.ad-image-container.data-v-80f2f579 {
  width: 100%;
  height: 180rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa, #e4e8f0);
  border-radius: 16rpx;
  overflow: hidden;
}
.ad-svg.data-v-80f2f579 {
  width: 60%;
  height: 60%;
  color: #8E8E93;
  opacity: 0.7;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.ad-label.data-v-80f2f579 {
  position: absolute;
  right: 16rpx;
  bottom: 16rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

/* 加载更多 */
.load-more.data-v-80f2f579 {
  padding: 30rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading-indicator.data-v-80f2f579 {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  box-shadow: inset 0 0.5rpx 3rpx rgba(0, 0, 0, 0.05), 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}
.loading-spinner-container.data-v-80f2f579 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.loading-spinner-svg.data-v-80f2f579 {
  width: 28rpx;
  height: 28rpx;
  color: #007AFF;
  animation: spin-80f2f579 1s linear infinite;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
@keyframes spin-80f2f579 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text.data-v-80f2f579 {
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 没有更多数据 */
.no-more.data-v-80f2f579 {
  padding: 30rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.no-more-text.data-v-80f2f579 {
  font-size: 28rpx;
  color: #8E8E93;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  box-shadow: inset 0 0.5rpx 3rpx rgba(0, 0, 0, 0.05), 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 置顶选项弹窗 */
.top-options-popup.data-v-80f2f579 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.top-options-content.data-v-80f2f579 {
  width: 650rpx;
  background-color: #FFFFFF;
  border-radius: 35rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: popup-80f2f579 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}
@keyframes popup-80f2f579 {
0% {
    opacity: 0;
    transform: scale(0.9) translateY(20rpx);
}
100% {
    opacity: 1;
    transform: scale(1) translateY(0);
}
}
.top-options-header.data-v-80f2f579 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx;
  border-bottom: 0.5rpx solid rgba(60, 60, 67, 0.1);
}
.top-options-title.data-v-80f2f579 {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.top-options-close.data-v-80f2f579 {
  width: 52rpx;
  height: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8E8E93;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 50%;
  transition: all 0.2s ease;
}
.top-options-close.data-v-80f2f579:active {
  background-color: rgba(0, 0, 0, 0.08);
  transform: scale(0.95);
}
.top-options-body.data-v-80f2f579 {
  padding: 28rpx;
}
.top-option.data-v-80f2f579 {
  display: flex;
  align-items: center;
  padding: 28rpx;
  margin-bottom: 24rpx;
  border-radius: 24rpx;
  background-color: #F2F2F7;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
}
.top-option.data-v-80f2f579:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}
.top-option.data-v-80f2f579:last-child {
  margin-bottom: 0;
}
.top-option-icon.data-v-80f2f579 {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}
.paid-icon.data-v-80f2f579 {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  color: #FFFFFF;
}
.ad-icon.data-v-80f2f579 {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #FFFFFF;
}
.top-option-info.data-v-80f2f579 {
  flex: 1;
}
.top-option-title.data-v-80f2f579 {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 6rpx;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.top-option-desc.data-v-80f2f579 {
  font-size: 26rpx;
  color: #8E8E93;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.top-option-action.data-v-80f2f579 {
  display: flex;
  align-items: center;
  background-color: rgba(0, 122, 255, 0.1);
  padding: 10rpx 20rpx;
  border-radius: 24rpx;
}
.top-action-text.data-v-80f2f579 {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: 500;
  margin-right: 8rpx;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.top-action-arrow.data-v-80f2f579 {
  color: #007AFF;
}
.top-options-footer.data-v-80f2f579 {
  padding: 28rpx;
  border-top: 0.5rpx solid rgba(60, 60, 67, 0.1);
  display: flex;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.01);
}
.top-options-tips.data-v-80f2f579 {
  font-size: 26rpx;
  color: #8E8E93;
  text-align: center;
  letter-spacing: 0.5rpx;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 房屋出租信息简洁展示 */
.house-rent-info.data-v-80f2f579 {
  margin-top: 12rpx;
  margin-bottom: 12rpx;
  padding: 16rpx;
  border-radius: 16rpx;
  background-color: rgba(0, 0, 0, 0.03);
  backdrop-filter: blur(5rpx);
  -webkit-backdrop-filter: blur(5rpx);
  box-shadow: inset 0 1rpx 5rpx rgba(0, 0, 0, 0.05);
}
.house-rent-detail.data-v-80f2f579 {
  display: flex;
  justify-content: space-between;
}
.house-rent-item.data-v-80f2f579 {
  display: flex;
  align-items: center;
}
.house-icon.data-v-80f2f579 {
  color: #636366;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
}
.house-text.data-v-80f2f579 {
  font-size: 22rpx;
  color: #636366;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}