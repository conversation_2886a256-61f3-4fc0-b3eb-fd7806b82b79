
.group-page {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f7f7f7;
		position: relative;
}
	
	/* 自定义导航栏 */
.custom-navbar {
		background-image: linear-gradient(135deg, #0052CC, #0066FF);
		height: 88rpx;
		padding-top: 44px; /* 状态栏高度 */
		display: flex;
		align-items: center;
		position: relative;
		box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
		z-index: 10;
}
.navbar-title {
		flex: 1;
		text-align: center;
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: 500;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
	
	/* 搜索框 */
.search-box {
		background-color: #ffffff;
		margin: 20rpx;
		height: 72rpx;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid rgba(235, 238, 245, 0.8);
}
.search-icon-wrap {
		display: flex;
		align-items: center;
		padding: 0 10rpx;
}
.search-icon {
		width: 32rpx;
		height: 32rpx;
		opacity: 0.5;
}
.search-input {
		flex: 1;
		height: 72rpx;
		font-size: 28rpx;
		color: #333;
}
.voice-icon-wrap {
		padding: 0 10rpx;
		display: flex;
		align-items: center;
}
.voice-icon {
		width: 36rpx;
		height: 36rpx;
}
.search-btn {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
}
.search-btn-icon {
		width: 44rpx;
		height: 44rpx;
}
	
	/* 内容区 */
.content-container {
		display: flex;
		flex: 1;
		overflow: hidden;
		height: calc(100vh - 112rpx);
}
	
	/* 左侧类别栏 */
.category-list {
		width: 180rpx;
		background-color: #f2f2f2;
		height: calc(100vh - 112rpx);
}
.category-item {
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
}
.category-item.active {
		background-color: #ffffff;
}
.category-item.active::before {
		content: '';
		position: absolute;
		left: 0;
		top: 34rpx;
		height: 32rpx;
		width: 6rpx;
		background-color: #0052CC;
		border-radius: 0 3rpx 3rpx 0;
}
.category-text {
		font-size: 28rpx;
		color: #666;
		text-align: center;
}
.category-item.active .category-text {
		color: #0052CC;
		font-weight: 500;
}
	
	/* 右侧群组内容 */
.group-content {
		flex: 1;
		background-color: #ffffff;
		height: calc(100vh - 112rpx);
		padding: 0 20rpx;
}
.group-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f2f2f2;
}
.group-avatar {
		width: 90rpx;
		height: 90rpx;
		border-radius: 12rpx;
		background-color: #f0f0f0;
		margin-right: 20rpx;
}
.group-info {
		flex: 1;
		display: flex;
		flex-direction: column;
}
.group-name {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 8rpx;
}
.group-desc {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 8rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 380rpx;
}
.group-count {
		font-size: 22rpx;
		color: #aaa;
}
.join-button {
		min-width: 120rpx;
		height: 60rpx;
		background-color: #0052CC;
		color: #ffffff;
		font-size: 26rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0;
		padding: 0 20rpx;
}
.no-data {
		padding: 60rpx 0;
		display: flex;
		align-items: center;
		justify-content: center;
}
.no-data-text {
		font-size: 28rpx;
		color: #999;
}
	
	/* 悬浮分享按钮 */
.share-button {
		position: fixed;
		right: 32rpx;
		bottom: 180rpx;
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background-color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
		z-index: 9;
}
.share-icon {
		width: 36rpx;
		height: 36rpx;
}
