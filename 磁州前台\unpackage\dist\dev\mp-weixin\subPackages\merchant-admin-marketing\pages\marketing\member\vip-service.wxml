<view class="vip-service-container data-v-a6b86a0e"><view class="page-header data-v-a6b86a0e"><view class="title-section data-v-a6b86a0e"><text class="page-title data-v-a6b86a0e">专属客服</text><text class="page-subtitle data-v-a6b86a0e">管理会员专属客服服务</text></view></view><view class="section-card data-v-a6b86a0e"><view class="switch-item data-v-a6b86a0e"><view class="switch-content data-v-a6b86a0e"><text class="switch-title data-v-a6b86a0e">专属客服服务</text><text class="switch-desc data-v-a6b86a0e">开启后，指定等级会员可享受专属客服服务</text></view><switch class="data-v-a6b86a0e" checked="{{a}}" bindchange="{{b}}" color="#4A00E0"/></view></view><block wx:if="{{c}}"><view class="section-card data-v-a6b86a0e"><view class="section-title data-v-a6b86a0e">服务设置</view><view class="form-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">服务类型</text><view class="checkbox-group data-v-a6b86a0e"><view wx:for="{{d}}" wx:for-item="type" wx:key="c" class="{{['checkbox-item', 'data-v-a6b86a0e', type.d && 'active']}}" bindtap="{{type.e}}"><view class="checkbox-icon data-v-a6b86a0e"><view wx:if="{{type.a}}" class="checkbox-inner data-v-a6b86a0e"></view></view><text class="checkbox-text data-v-a6b86a0e">{{type.b}}</text></view></view></view><view class="form-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">优先级</text><view class="radio-group data-v-a6b86a0e"><view wx:for="{{e}}" wx:for-item="priority" wx:key="b" class="{{['radio-item', 'data-v-a6b86a0e', priority.c && 'active']}}" bindtap="{{priority.d}}"><text class="radio-text data-v-a6b86a0e">{{priority.a}}</text></view></view></view><view class="form-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">响应时间</text><view class="form-input-group data-v-a6b86a0e"><input type="number" class="form-input data-v-a6b86a0e" value="{{f}}" bindinput="{{g}}"/><text class="input-suffix data-v-a6b86a0e">分钟内</text></view></view><view class="form-item switch-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">7×24小时服务</text><switch class="data-v-a6b86a0e" checked="{{h}}" bindchange="{{i}}" color="#4A00E0"/></view></view><view class="section-card data-v-a6b86a0e"><view class="section-title data-v-a6b86a0e">客服人员</view><view class="staff-list data-v-a6b86a0e"><view wx:for="{{j}}" wx:for-item="staff" wx:key="h" class="staff-item data-v-a6b86a0e"><view class="staff-info data-v-a6b86a0e"><image class="staff-avatar data-v-a6b86a0e" src="{{staff.a}}" mode="aspectFill"></image><view class="staff-detail data-v-a6b86a0e"><view class="staff-name data-v-a6b86a0e">{{staff.b}}</view><view class="staff-position data-v-a6b86a0e">{{staff.c}}</view></view></view><view class="staff-actions data-v-a6b86a0e"><view class="{{['staff-status', 'data-v-a6b86a0e', staff.e && 'active']}}">{{staff.d}}</view><view class="action-btn edit data-v-a6b86a0e" bindtap="{{staff.f}}">编辑</view><view class="action-btn delete data-v-a6b86a0e" bindtap="{{staff.g}}">删除</view></view></view></view><button class="add-btn data-v-a6b86a0e" bindtap="{{k}}">添加客服人员</button></view><view class="section-card data-v-a6b86a0e"><view class="section-title data-v-a6b86a0e">适用会员等级</view><view class="level-list data-v-a6b86a0e"><view wx:for="{{l}}" wx:for-item="level" wx:key="f" class="level-item data-v-a6b86a0e"><view class="{{['level-checkbox', 'data-v-a6b86a0e', level.b && 'checked']}}" bindtap="{{level.c}}"><view wx:if="{{level.a}}" class="checkbox-inner data-v-a6b86a0e"></view></view><view class="level-content data-v-a6b86a0e"><text class="level-name data-v-a6b86a0e">{{level.d}}</text><text class="level-desc data-v-a6b86a0e">{{level.e}}名会员</text></view></view></view></view><view class="section-card data-v-a6b86a0e"><view class="section-title data-v-a6b86a0e">服务说明</view><view class="form-item data-v-a6b86a0e"><block wx:if="{{r0}}"><textarea class="form-textarea data-v-a6b86a0e" placeholder="请输入专属客服服务说明" value="{{m}}" bindinput="{{n}}"/></block></view></view><view class="bottom-bar data-v-a6b86a0e"><button class="save-btn data-v-a6b86a0e" bindtap="{{o}}">保存设置</button></view></block><uni-popup wx:if="{{I}}" class="r data-v-a6b86a0e" u-s="{{['d']}}" u-r="staffFormPopup" u-i="a6b86a0e-0" bind:__l="__l" u-p="{{I}}"><view class="staff-form-popup data-v-a6b86a0e"><view class="popup-header data-v-a6b86a0e"><text class="popup-title data-v-a6b86a0e">{{p}}</text><text class="popup-close data-v-a6b86a0e" bindtap="{{q}}">×</text></view><view class="popup-body data-v-a6b86a0e"><view class="form-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">客服姓名</text><input class="form-input data-v-a6b86a0e" placeholder="请输入客服姓名" value="{{r}}" bindinput="{{s}}"/></view><view class="form-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">客服头像</text><view class="avatar-upload data-v-a6b86a0e"><image wx:if="{{t}}" class="preview-avatar data-v-a6b86a0e" src="{{v}}" mode="aspectFill"></image><view wx:else class="upload-btn data-v-a6b86a0e" bindtap="{{w}}"><text class="upload-icon data-v-a6b86a0e">+</text><text class="upload-text data-v-a6b86a0e">上传头像</text></view></view></view><view class="form-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">职位</text><input class="form-input data-v-a6b86a0e" placeholder="请输入客服职位" value="{{x}}" bindinput="{{y}}"/></view><view class="form-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">手机号码</text><input class="form-input data-v-a6b86a0e" placeholder="请输入客服手机号码" value="{{z}}" bindinput="{{A}}"/></view><view class="form-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">微信号</text><input class="form-input data-v-a6b86a0e" placeholder="请输入客服微信号" value="{{B}}" bindinput="{{C}}"/></view><view class="form-item switch-item data-v-a6b86a0e"><text class="form-label data-v-a6b86a0e">在线状态</text><switch class="data-v-a6b86a0e" checked="{{D}}" bindchange="{{E}}" color="#4A00E0"/></view></view><view class="popup-footer data-v-a6b86a0e"><button class="cancel-btn data-v-a6b86a0e" bindtap="{{F}}">取消</button><button class="confirm-btn data-v-a6b86a0e" bindtap="{{G}}">确认</button></view></view></uni-popup><uni-popup wx:if="{{N}}" class="r data-v-a6b86a0e" u-s="{{['d']}}" u-r="deleteConfirmPopup" u-i="a6b86a0e-1" bind:__l="__l" u-p="{{N}}"><uni-popup-dialog wx:if="{{L}}" class="data-v-a6b86a0e" bindconfirm="{{J}}" bindclose="{{K}}" u-i="a6b86a0e-2,a6b86a0e-1" bind:__l="__l" u-p="{{L}}"></uni-popup-dialog></uni-popup></view>