{"version": 3, "file": "member-level.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/member-level.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcbWVtYmVyLWxldmVsLnZ1ZQ"], "sourcesContent": ["<!-- 会员等级页面开始 -->\r\n<template>\r\n  <view class=\"member-level-container\">\r\n    <!-- 页面标题区域 -->\r\n    <view class=\"page-header\">\r\n      <view class=\"title-section\">\r\n        <text class=\"page-title\">会员等级</text>\r\n        <text class=\"page-subtitle\">管理店铺会员等级体系</text>\r\n      </view>\r\n      \r\n      <!-- 添加等级按钮 -->\r\n      <view class=\"add-level-btn\" @click=\"showAddLevelModal\">\r\n        <text class=\"btn-text\">添加等级</text>\r\n        <text class=\"btn-icon\">+</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 会员等级列表 -->\r\n    <view class=\"level-list\">\r\n      <view v-if=\"levels.length === 0\" class=\"empty-tip\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty-data.svg\"></image>\r\n        <text class=\"empty-text\">暂无会员等级，点击\"添加等级\"创建</text>\r\n      </view>\r\n      \r\n      <view v-else class=\"level-cards\">\r\n        <view v-for=\"(level, index) in levels\" :key=\"index\" class=\"level-card\">\r\n          <view class=\"level-card-header\" :style=\"{ backgroundColor: level.color }\">\r\n            <view class=\"level-name\">{{ level.name }}</view>\r\n            <view class=\"level-actions\">\r\n              <text class=\"action-btn edit\" @click=\"editLevel(level)\">编辑</text>\r\n              <text class=\"action-btn delete\" @click=\"confirmDeleteLevel(level)\">删除</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"level-card-body\">\r\n            <view class=\"level-info-item\">\r\n              <text class=\"info-label\">等级图标：</text>\r\n              <image class=\"level-icon\" :src=\"level.icon\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <view class=\"level-info-item\">\r\n              <text class=\"info-label\">所需成长值：</text>\r\n              <text class=\"info-value\">{{ level.growthRequired }}</text>\r\n            </view>\r\n            <view class=\"level-info-item\">\r\n              <text class=\"info-label\">折扣权益：</text>\r\n              <text class=\"info-value\">{{ level.discount }}折</text>\r\n            </view>\r\n            <view class=\"level-info-item\">\r\n              <text class=\"info-label\">积分倍率：</text>\r\n              <text class=\"info-value\">{{ level.pointsMultiplier }}倍</text>\r\n            </view>\r\n            <view class=\"level-info-item\">\r\n              <text class=\"info-label\">特权说明：</text>\r\n              <text class=\"info-value\">{{ level.description || '暂无特权说明' }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 添加/编辑等级弹窗 -->\r\n    <uni-popup ref=\"levelFormPopup\" type=\"center\">\r\n      <view class=\"level-form-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">{{ isEditing ? '编辑等级' : '添加等级' }}</text>\r\n          <text class=\"popup-close\" @click=\"closeLevelModal\">×</text>\r\n        </view>\r\n        <view class=\"popup-body\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">等级名称</text>\r\n            <input class=\"form-input\" v-model=\"levelForm.name\" placeholder=\"请输入等级名称\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">等级颜色</text>\r\n            <view class=\"color-picker\">\r\n              <view \r\n                v-for=\"(color, idx) in colorOptions\" \r\n                :key=\"idx\" \r\n                class=\"color-option\"\r\n                :class=\"{ active: levelForm.color === color }\"\r\n                :style=\"{ backgroundColor: color }\"\r\n                @click=\"levelForm.color = color\"\r\n              ></view>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">等级图标</text>\r\n            <view class=\"icon-upload\">\r\n              <image v-if=\"levelForm.icon\" class=\"preview-icon\" :src=\"levelForm.icon\" mode=\"aspectFit\"></image>\r\n              <view v-else class=\"upload-btn\" @click=\"chooseIcon\">\r\n                <text class=\"upload-icon\">+</text>\r\n                <text class=\"upload-text\">上传图标</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">所需成长值</text>\r\n            <input class=\"form-input\" type=\"number\" v-model=\"levelForm.growthRequired\" placeholder=\"请输入所需成长值\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">折扣权益</text>\r\n            <input class=\"form-input\" type=\"digit\" v-model=\"levelForm.discount\" placeholder=\"请输入折扣（如9.5代表9.5折）\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">积分倍率</text>\r\n            <input class=\"form-input\" type=\"digit\" v-model=\"levelForm.pointsMultiplier\" placeholder=\"请输入积分倍率\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">特权说明</text>\r\n            <textarea class=\"form-textarea\" v-model=\"levelForm.description\" placeholder=\"请输入特权说明\"></textarea>\r\n          </view>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <button class=\"cancel-btn\" @click=\"closeLevelModal\">取消</button>\r\n          <button class=\"confirm-btn\" @click=\"saveLevelForm\">确认</button>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 删除确认弹窗 -->\r\n    <uni-popup ref=\"deleteConfirmPopup\" type=\"dialog\">\r\n      <uni-popup-dialog\r\n        type=\"warning\"\r\n        title=\"删除确认\"\r\n        content=\"确定要删除该会员等级吗？删除后将无法恢复。\"\r\n        :before-close=\"true\"\r\n        @confirm=\"deleteLevel\"\r\n        @close=\"closeDeleteConfirm\"\r\n      ></uni-popup-dialog>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      levels: [], // 会员等级列表\r\n      levelForm: {\r\n        id: '',\r\n        name: '',\r\n        color: '#8A2BE2', // 默认紫色\r\n        icon: '',\r\n        growthRequired: '',\r\n        discount: '',\r\n        pointsMultiplier: '',\r\n        description: ''\r\n      },\r\n      isEditing: false, // 是否为编辑模式\r\n      currentLevelId: null, // 当前编辑的等级ID\r\n      colorOptions: [\r\n        '#8A2BE2', // 紫色\r\n        '#FF4500', // 橙红色\r\n        '#1E90FF', // 道奇蓝\r\n        '#32CD32', // 酸橙绿\r\n        '#FFD700', // 金色\r\n        '#FF69B4', // 热粉红\r\n        '#20B2AA', // 浅海绿\r\n        '#FF8C00'  // 深橙色\r\n      ]\r\n    };\r\n  },\r\n  onLoad() {\r\n    this.fetchLevels();\r\n  },\r\n  methods: {\r\n    // 获取会员等级列表\r\n    fetchLevels() {\r\n      // 模拟数据，实际项目中应从API获取\r\n      this.levels = [\r\n        {\r\n          id: '1',\r\n          name: '普通会员',\r\n          color: '#8A2BE2',\r\n          icon: '/static/images/level-normal.svg',\r\n          growthRequired: 0,\r\n          discount: 10,\r\n          pointsMultiplier: 1,\r\n          description: '基础会员权益'\r\n        },\r\n        {\r\n          id: '2',\r\n          name: '银卡会员',\r\n          color: '#1E90FF',\r\n          icon: '/static/images/level-silver.svg',\r\n          growthRequired: 1000,\r\n          discount: 9.5,\r\n          pointsMultiplier: 1.2,\r\n          description: '享受9.5折优惠，积分1.2倍'\r\n        },\r\n        {\r\n          id: '3',\r\n          name: '金卡会员',\r\n          color: '#FFD700',\r\n          icon: '/static/images/level-gold.svg',\r\n          growthRequired: 3000,\r\n          discount: 9,\r\n          pointsMultiplier: 1.5,\r\n          description: '享受9折优惠，积分1.5倍，生日礼包'\r\n        }\r\n      ];\r\n    },\r\n    \r\n    // 显示添加等级弹窗\r\n    showAddLevelModal() {\r\n      this.isEditing = false;\r\n      this.resetLevelForm();\r\n      this.$refs.levelFormPopup.open();\r\n    },\r\n    \r\n    // 编辑等级\r\n    editLevel(level) {\r\n      this.isEditing = true;\r\n      this.currentLevelId = level.id;\r\n      this.levelForm = { ...level };\r\n      this.$refs.levelFormPopup.open();\r\n    },\r\n    \r\n    // 关闭等级表单弹窗\r\n    closeLevelModal() {\r\n      this.$refs.levelFormPopup.close();\r\n      this.resetLevelForm();\r\n    },\r\n    \r\n    // 重置表单\r\n    resetLevelForm() {\r\n      this.levelForm = {\r\n        id: '',\r\n        name: '',\r\n        color: '#8A2BE2',\r\n        icon: '',\r\n        growthRequired: '',\r\n        discount: '',\r\n        pointsMultiplier: '',\r\n        description: ''\r\n      };\r\n      this.currentLevelId = null;\r\n    },\r\n    \r\n    // 选择图标\r\n    chooseIcon() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        success: (res) => {\r\n          this.levelForm.icon = res.tempFilePaths[0];\r\n          // 实际项目中应上传图片到服务器并获取URL\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 保存等级表单\r\n    saveLevelForm() {\r\n      // 表单验证\r\n      if (!this.levelForm.name) {\r\n        uni.showToast({\r\n          title: '请输入等级名称',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.levelForm.growthRequired && this.levelForm.growthRequired !== 0) {\r\n        uni.showToast({\r\n          title: '请输入所需成长值',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.levelForm.discount) {\r\n        uni.showToast({\r\n          title: '请输入折扣权益',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.levelForm.pointsMultiplier) {\r\n        uni.showToast({\r\n          title: '请输入积分倍率',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (this.isEditing) {\r\n        // 编辑现有等级\r\n        const index = this.levels.findIndex(item => item.id === this.currentLevelId);\r\n        if (index !== -1) {\r\n          this.levels[index] = { ...this.levelForm, id: this.currentLevelId };\r\n        }\r\n      } else {\r\n        // 添加新等级\r\n        const newLevel = {\r\n          ...this.levelForm,\r\n          id: Date.now().toString() // 生成临时ID，实际项目中应由后端生成\r\n        };\r\n        this.levels.push(newLevel);\r\n      }\r\n      \r\n      // 关闭弹窗\r\n      this.$refs.levelFormPopup.close();\r\n      this.resetLevelForm();\r\n      \r\n      // 提示成功\r\n      uni.showToast({\r\n        title: this.isEditing ? '编辑成功' : '添加成功',\r\n        icon: 'success'\r\n      });\r\n    },\r\n    \r\n    // 确认删除等级\r\n    confirmDeleteLevel(level) {\r\n      this.currentLevelId = level.id;\r\n      this.$refs.deleteConfirmPopup.open();\r\n    },\r\n    \r\n    // 关闭删除确认弹窗\r\n    closeDeleteConfirm() {\r\n      this.$refs.deleteConfirmPopup.close();\r\n    },\r\n    \r\n    // 删除等级\r\n    deleteLevel() {\r\n      const index = this.levels.findIndex(item => item.id === this.currentLevelId);\r\n      if (index !== -1) {\r\n        this.levels.splice(index, 1);\r\n        uni.showToast({\r\n          title: '删除成功',\r\n          icon: 'success'\r\n        });\r\n      }\r\n      this.currentLevelId = null;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n/* 会员等级页面样式开始 */\r\n.member-level-container {\r\n  padding: 30rpx;\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.title-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  margin-top: 8rpx;\r\n}\r\n\r\n.add-level-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #6200ee;\r\n  color: #fff;\r\n  padding: 16rpx 30rpx;\r\n  border-radius: 50rpx;\r\n}\r\n\r\n.btn-text {\r\n  font-size: 28rpx;\r\n}\r\n\r\n.btn-icon {\r\n  font-size: 32rpx;\r\n  margin-left: 8rpx;\r\n}\r\n\r\n.level-list {\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.empty-tip {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-icon {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.level-cards {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30rpx;\r\n}\r\n\r\n.level-card {\r\n  background-color: #fff;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.level-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  color: #fff;\r\n}\r\n\r\n.level-name {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n.level-actions {\r\n  display: flex;\r\n  gap: 20rpx;\r\n}\r\n\r\n.action-btn {\r\n  font-size: 24rpx;\r\n  padding: 6rpx 16rpx;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border-radius: 30rpx;\r\n}\r\n\r\n.level-card-body {\r\n  padding: 20rpx 30rpx;\r\n}\r\n\r\n.level-info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.info-label {\r\n  width: 180rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n.info-value {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  flex: 1;\r\n}\r\n\r\n.level-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.level-form-popup {\r\n  width: 650rpx;\r\n  background-color: #fff;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.popup-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.popup-close {\r\n  font-size: 40rpx;\r\n  color: #999;\r\n}\r\n\r\n.popup-body {\r\n  padding: 30rpx;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 160rpx;\r\n  border: 1rpx solid #ddd;\r\n  border-radius: 8rpx;\r\n  padding: 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.color-picker {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20rpx;\r\n}\r\n\r\n.color-option {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  border: 2rpx solid transparent;\r\n}\r\n\r\n.color-option.active {\r\n  border-color: #333;\r\n  transform: scale(1.1);\r\n}\r\n\r\n.icon-upload {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.preview-icon {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n}\r\n\r\n.upload-btn {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  border: 1rpx dashed #ddd;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 40rpx;\r\n  color: #999;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 20rpx;\r\n  color: #999;\r\n  margin-top: 6rpx;\r\n}\r\n\r\n.popup-footer {\r\n  display: flex;\r\n  border-top: 1rpx solid #eee;\r\n}\r\n\r\n.cancel-btn, .confirm-btn {\r\n  flex: 1;\r\n  height: 90rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 32rpx;\r\n}\r\n\r\n.cancel-btn {\r\n  color: #666;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.confirm-btn {\r\n  color: #fff;\r\n  background-color: #6200ee;\r\n}\r\n/* 会员等级页面样式结束 */\r\n</style>\r\n<!-- 会员等级页面结束 --> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/member-level.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAqIA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,QAAQ,CAAE;AAAA;AAAA,MACV,WAAW;AAAA,QACT,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA;AAAA,QACP,MAAM;AAAA,QACN,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,aAAa;AAAA,MACd;AAAA,MACD,WAAW;AAAA;AAAA,MACX,gBAAgB;AAAA;AAAA,MAChB,cAAc;AAAA,QACZ;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA;EAEH;AAAA,EACD,SAAS;AACP,SAAK,YAAW;AAAA,EACjB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc;AAEZ,WAAK,SAAS;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,aAAa;AAAA,QACf;AAAA;IAEH;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,YAAY;AACjB,WAAK,eAAc;AACnB,WAAK,MAAM,eAAe;IAC3B;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,YAAY;AACjB,WAAK,iBAAiB,MAAM;AAC5B,WAAK,YAAY,EAAE,GAAG;AACtB,WAAK,MAAM,eAAe;IAC3B;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,MAAM,eAAe;AAC1B,WAAK,eAAc;AAAA,IACpB;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,YAAY;AAAA,QACf,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,aAAa;AAAA;AAEf,WAAK,iBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAChB,eAAK,UAAU,OAAO,IAAI,cAAc,CAAC;AAAA,QAE3C;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AAEd,UAAI,CAAC,KAAK,UAAU,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,UAAU,kBAAkB,KAAK,UAAU,mBAAmB,GAAG;AACzEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,UAAU,UAAU;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,UAAU,kBAAkB;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,KAAK,WAAW;AAElB,cAAM,QAAQ,KAAK,OAAO,UAAU,UAAQ,KAAK,OAAO,KAAK,cAAc;AAC3E,YAAI,UAAU,IAAI;AAChB,eAAK,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,WAAW,IAAI,KAAK;QACrD;AAAA,aACK;AAEL,cAAM,WAAW;AAAA,UACf,GAAG,KAAK;AAAA,UACR,IAAI,KAAK,IAAK,EAAC;;;AAEjB,aAAK,OAAO,KAAK,QAAQ;AAAA,MAC3B;AAGA,WAAK,MAAM,eAAe;AAC1B,WAAK,eAAc;AAGnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,YAAY,SAAS;AAAA,QACjC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB,OAAO;AACxB,WAAK,iBAAiB,MAAM;AAC5B,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,cAAc;AACZ,YAAM,QAAQ,KAAK,OAAO,UAAU,UAAQ,KAAK,OAAO,KAAK,cAAc;AAC3E,UAAI,UAAU,IAAI;AAChB,aAAK,OAAO,OAAO,OAAO,CAAC;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7UA,GAAG,WAAW,eAAe;"}