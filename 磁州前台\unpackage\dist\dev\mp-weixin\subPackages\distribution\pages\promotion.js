"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_distributionService = require("../../../utils/distributionService.js");
const _sfc_main = {
  __name: "promotion",
  setup(__props) {
    const promotionLink = common_vendor.reactive({
      url: "",
      shortUrl: "",
      qrCodeUrl: ""
    });
    const promotionData = common_vendor.reactive({
      viewCount: 0,
      orderCount: 0,
      conversionRate: 0,
      commission: 0
    });
    const userInfo = common_vendor.reactive({
      userId: "1001",
      // 模拟数据，实际应从用户系统获取
      distributorId: "DIS1001"
    });
    common_vendor.onMounted(async () => {
      await generateLink();
      await getPromotionData();
    });
    const generateLink = async () => {
      try {
        const result = await utils_distributionService.distributionService.generatePromotionLink({
          type: "platform",
          id: "home",
          distributorId: userInfo.distributorId
        });
        if (result) {
          Object.assign(promotionLink, result);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/promotion.vue:205", "生成推广链接失败", error);
        common_vendor.index.showToast({
          title: "生成推广链接失败",
          icon: "none"
        });
      }
    };
    const getPromotionData = async () => {
      try {
        promotionData.viewCount = 1256;
        promotionData.orderCount = 45;
        promotionData.conversionRate = 3.58;
        promotionData.commission = 1256.8;
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/promotion.vue:223", "获取推广数据失败", error);
      }
    };
    const copyLink = () => {
      if (!promotionLink.url) {
        common_vendor.index.showToast({
          title: "链接生成中，请稍后再试",
          icon: "none"
        });
        return;
      }
      common_vendor.index.setClipboardData({
        data: promotionLink.url,
        success: () => {
          common_vendor.index.showToast({
            title: "链接已复制",
            icon: "success"
          });
        }
      });
    };
    const shareLink = () => {
      if (!promotionLink.url) {
        common_vendor.index.showToast({
          title: "链接生成中，请稍后再试",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    };
    const formatCommission = (amount) => {
      return utils_distributionService.distributionService.formatCommission(amount);
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    const navigateToMerchant = (url) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "此功能需要商家权限，是否申请成为商家专属分销员？",
        confirmText: "去申请",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateTo({
              url: "/subPackages/distribution/pages/merchant-apply"
            });
          }
        }
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "推广工具帮助",
        content: "推广工具页面提供了多种推广方式，包括海报、二维码、文案等。您可以选择适合的工具进行推广，获取佣金。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: common_vendor.o(($event) => navigateTo("/subPackages/promotion/pages/promotion-tool")),
        d: common_vendor.o(($event) => navigateTo("/subPackages/promotion/pages/qrcode")),
        e: common_vendor.o(($event) => navigateToMerchant()),
        f: common_vendor.o(($event) => navigateToMerchant()),
        g: common_vendor.o(generateLink),
        h: common_vendor.t(promotionLink.url || "生成中..."),
        i: common_vendor.o(copyLink),
        j: common_vendor.o(shareLink),
        k: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/team")),
        l: common_vendor.t(promotionData.viewCount),
        m: common_vendor.t(promotionData.orderCount),
        n: common_vendor.t(promotionData.conversionRate),
        o: common_vendor.t(formatCommission(promotionData.commission))
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/distribution/pages/promotion.js.map
