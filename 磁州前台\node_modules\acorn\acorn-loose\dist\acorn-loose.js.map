{"version": 3, "file": "acorn-loose.js", "sources": ["../src/state.js", "../src/tokenize.js", "../src/parseutil.js", "../src/statement.js", "../src/expression.js", "../src/index.js"], "sourcesContent": ["import {Parser, SourceLocation, tokTypes as tt, Node, lineBreak, isNewLine} from \"acorn\"\n\nfunction noop() {}\n\nexport class LooseParser {\n  constructor(input, options = {}) {\n    this.toks = this.constructor.BaseParser.tokenizer(input, options)\n    this.options = this.toks.options\n    this.input = this.toks.input\n    this.tok = this.last = {type: tt.eof, start: 0, end: 0}\n    this.tok.validateRegExpFlags = noop\n    this.tok.validateRegExpPattern = noop\n    if (this.options.locations) {\n      let here = this.toks.curPosition()\n      this.tok.loc = new SourceLocation(this.toks, here, here)\n    }\n    this.ahead = [] // Tokens ahead\n    this.context = [] // Indentation contexted\n    this.curIndent = 0\n    this.curLineStart = 0\n    this.nextLineStart = this.lineEnd(this.curLineStart) + 1\n    this.inAsync = false\n    this.inFunction = false\n  }\n\n  startNode() {\n    return new Node(this.toks, this.tok.start, this.options.locations ? this.tok.loc.start : null)\n  }\n\n  storeCurrentPos() {\n    return this.options.locations ? [this.tok.start, this.tok.loc.start] : this.tok.start\n  }\n\n  startNodeAt(pos) {\n    if (this.options.locations) {\n      return new Node(this.toks, pos[0], pos[1])\n    } else {\n      return new Node(this.toks, pos)\n    }\n  }\n\n  finishNode(node, type) {\n    node.type = type\n    node.end = this.last.end\n    if (this.options.locations)\n      node.loc.end = this.last.loc.end\n    if (this.options.ranges)\n      node.range[1] = this.last.end\n    return node\n  }\n\n  dummyNode(type) {\n    let dummy = this.startNode()\n    dummy.type = type\n    dummy.end = dummy.start\n    if (this.options.locations)\n      dummy.loc.end = dummy.loc.start\n    if (this.options.ranges)\n      dummy.range[1] = dummy.start\n    this.last = {type: tt.name, start: dummy.start, end: dummy.start, loc: dummy.loc}\n    return dummy\n  }\n\n  dummyIdent() {\n    let dummy = this.dummyNode(\"Identifier\")\n    dummy.name = \"✖\"\n    return dummy\n  }\n\n  dummyString() {\n    let dummy = this.dummyNode(\"Literal\")\n    dummy.value = dummy.raw = \"✖\"\n    return dummy\n  }\n\n  eat(type) {\n    if (this.tok.type === type) {\n      this.next()\n      return true\n    } else {\n      return false\n    }\n  }\n\n  isContextual(name) {\n    return this.tok.type === tt.name && this.tok.value === name\n  }\n\n  eatContextual(name) {\n    return this.tok.value === name && this.eat(tt.name)\n  }\n\n  canInsertSemicolon() {\n    return this.tok.type === tt.eof || this.tok.type === tt.braceR ||\n      lineBreak.test(this.input.slice(this.last.end, this.tok.start))\n  }\n\n  semicolon() {\n    return this.eat(tt.semi)\n  }\n\n  expect(type) {\n    if (this.eat(type)) return true\n    for (let i = 1; i <= 2; i++) {\n      if (this.lookAhead(i).type === type) {\n        for (let j = 0; j < i; j++) this.next()\n        return true\n      }\n    }\n  }\n\n  pushCx() {\n    this.context.push(this.curIndent)\n  }\n\n  popCx() {\n    this.curIndent = this.context.pop()\n  }\n\n  lineEnd(pos) {\n    while (pos < this.input.length && !isNewLine(this.input.charCodeAt(pos))) ++pos\n    return pos\n  }\n\n  indentationAfter(pos) {\n    for (let count = 0;; ++pos) {\n      let ch = this.input.charCodeAt(pos)\n      if (ch === 32) ++count\n      else if (ch === 9) count += this.options.tabSize\n      else return count\n    }\n  }\n\n  closes(closeTok, indent, line, blockHeuristic) {\n    if (this.tok.type === closeTok || this.tok.type === tt.eof) return true\n    return line !== this.curLineStart && this.curIndent < indent && this.tokenStartsLine() &&\n      (!blockHeuristic || this.nextLineStart >= this.input.length ||\n       this.indentationAfter(this.nextLineStart) < indent)\n  }\n\n  tokenStartsLine() {\n    for (let p = this.tok.start - 1; p >= this.curLineStart; --p) {\n      let ch = this.input.charCodeAt(p)\n      if (ch !== 9 && ch !== 32) return false\n    }\n    return true\n  }\n\n  extend(name, f) {\n    this[name] = f(this[name])\n  }\n\n  parse() {\n    this.next()\n    return this.parseTopLevel()\n  }\n\n  static extend(...plugins) {\n    let cls = this\n    for (let i = 0; i < plugins.length; i++) cls = plugins[i](cls)\n    return cls\n  }\n\n  static parse(input, options) {\n    return new this(input, options).parse()\n  }\n}\n\n// Allows plugins to extend the base parser / tokenizer used\nLooseParser.BaseParser = Parser\n", "import {tokTypes as tt, Token, isNewLine, SourceLocation, getLineInfo, lineBreakG} from \"acorn\"\nimport {LooseParser} from \"./state\"\n\nconst lp = LooseParser.prototype\n\nfunction isSpace(ch) {\n  return (ch < 14 && ch > 8) || ch === 32 || ch === 160 || isNewLine(ch)\n}\n\nlp.next = function() {\n  this.last = this.tok\n  if (this.ahead.length)\n    this.tok = this.ahead.shift()\n  else\n    this.tok = this.readToken()\n\n  if (this.tok.start >= this.nextLineStart) {\n    while (this.tok.start >= this.nextLineStart) {\n      this.curLineStart = this.nextLineStart\n      this.nextLineStart = this.lineEnd(this.curLineStart) + 1\n    }\n    this.curIndent = this.indentationAfter(this.curLineStart)\n  }\n}\n\nlp.readToken = function() {\n  for (;;) {\n    try {\n      this.toks.next()\n      if (this.toks.type === tt.dot &&\n          this.input.substr(this.toks.end, 1) === \".\" &&\n          this.options.ecmaVersion >= 6) {\n        this.toks.end++\n        this.toks.type = tt.ellipsis\n      }\n      return new Token(this.toks)\n    } catch (e) {\n      if (!(e instanceof SyntaxError)) throw e\n\n      // Try to skip some text, based on the error message, and then continue\n      let msg = e.message, pos = e.raisedAt, replace = true\n      if (/unterminated/i.test(msg)) {\n        pos = this.lineEnd(e.pos + 1)\n        if (/string/.test(msg)) {\n          replace = {start: e.pos, end: pos, type: tt.string, value: this.input.slice(e.pos + 1, pos)}\n        } else if (/regular expr/i.test(msg)) {\n          let re = this.input.slice(e.pos, pos)\n          try { re = new RegExp(re) } catch (e) { /* ignore compilation error due to new syntax */ }\n          replace = {start: e.pos, end: pos, type: tt.regexp, value: re}\n        } else if (/template/.test(msg)) {\n          replace = {\n            start: e.pos,\n            end: pos,\n            type: tt.template,\n            value: this.input.slice(e.pos, pos)\n          }\n        } else {\n          replace = false\n        }\n      } else if (/invalid (unicode|regexp|number)|expecting unicode|octal literal|is reserved|directly after number|expected number in radix/i.test(msg)) {\n        while (pos < this.input.length && !isSpace(this.input.charCodeAt(pos))) ++pos\n      } else if (/character escape|expected hexadecimal/i.test(msg)) {\n        while (pos < this.input.length) {\n          let ch = this.input.charCodeAt(pos++)\n          if (ch === 34 || ch === 39 || isNewLine(ch)) break\n        }\n      } else if (/unexpected character/i.test(msg)) {\n        pos++\n        replace = false\n      } else if (/regular expression/i.test(msg)) {\n        replace = true\n      } else {\n        throw e\n      }\n      this.resetTo(pos)\n      if (replace === true) replace = {start: pos, end: pos, type: tt.name, value: \"✖\"}\n      if (replace) {\n        if (this.options.locations)\n          replace.loc = new SourceLocation(\n            this.toks,\n            getLineInfo(this.input, replace.start),\n            getLineInfo(this.input, replace.end))\n        return replace\n      }\n    }\n  }\n}\n\nlp.resetTo = function(pos) {\n  this.toks.pos = pos\n  let ch = this.input.charAt(pos - 1)\n  this.toks.exprAllowed = !ch || /[[{(,;:?/*=+\\-~!|&%^<>]/.test(ch) ||\n    /[enwfd]/.test(ch) &&\n    /\\b(case|else|return|throw|new|in|(instance|type)?of|delete|void)$/.test(this.input.slice(pos - 10, pos))\n\n  if (this.options.locations) {\n    this.toks.curLine = 1\n    this.toks.lineStart = lineBreakG.lastIndex = 0\n    let match\n    while ((match = lineBreakG.exec(this.input)) && match.index < pos) {\n      ++this.toks.curLine\n      this.toks.lineStart = match.index + match[0].length\n    }\n  }\n}\n\nlp.lookAhead = function(n) {\n  while (n > this.ahead.length)\n    this.ahead.push(this.readToken())\n  return this.ahead[n - 1]\n}\n", "export function isDummy(node) { return node.name === \"✖\" }\n", "import {<PERSON><PERSON><PERSON><PERSON><PERSON>} from \"./state\"\nimport {isDummy} from \"./parseutil\"\nimport {getLineInfo, tokTypes as tt} from \"acorn\"\n\nconst lp = LooseParser.prototype\n\nlp.parseTopLevel = function() {\n  let node = this.startNodeAt(this.options.locations ? [0, getLineInfo(this.input, 0)] : 0)\n  node.body = []\n  while (this.tok.type !== tt.eof) node.body.push(this.parseStatement())\n  this.toks.adaptDirectivePrologue(node.body)\n  this.last = this.tok\n  if (this.options.ecmaVersion >= 6) {\n    node.sourceType = this.options.sourceType\n  }\n  return this.finishNode(node, \"Program\")\n}\n\nlp.parseStatement = function() {\n  let starttype = this.tok.type, node = this.startNode(), kind\n\n  if (this.toks.isLet()) {\n    starttype = tt._var\n    kind = \"let\"\n  }\n\n  switch (starttype) {\n  case tt._break: case tt._continue:\n    this.next()\n    let isBreak = starttype === tt._break\n    if (this.semicolon() || this.canInsertSemicolon()) {\n      node.label = null\n    } else {\n      node.label = this.tok.type === tt.name ? this.parseIdent() : null\n      this.semicolon()\n    }\n    return this.finishNode(node, isBreak ? \"BreakStatement\" : \"ContinueStatement\")\n\n  case tt._debugger:\n    this.next()\n    this.semicolon()\n    return this.finishNode(node, \"DebuggerStatement\")\n\n  case tt._do:\n    this.next()\n    node.body = this.parseStatement()\n    node.test = this.eat(tt._while) ? this.parseParenExpression() : this.dummyIdent()\n    this.semicolon()\n    return this.finishNode(node, \"DoWhileStatement\")\n\n  case tt._for:\n    this.next() // `for` keyword\n    let isAwait = this.options.ecmaVersion >= 9 && this.inAsync && this.eatContextual(\"await\")\n\n    this.pushCx()\n    this.expect(tt.parenL)\n    if (this.tok.type === tt.semi) return this.parseFor(node, null)\n    let isLet = this.toks.isLet()\n    if (isLet || this.tok.type === tt._var || this.tok.type === tt._const) {\n      let init = this.parseVar(this.startNode(), true, isLet ? \"let\" : this.tok.value)\n      if (init.declarations.length === 1 && (this.tok.type === tt._in || this.isContextual(\"of\"))) {\n        if (this.options.ecmaVersion >= 9 && this.tok.type !== tt._in) {\n          node.await = isAwait\n        }\n        return this.parseForIn(node, init)\n      }\n      return this.parseFor(node, init)\n    }\n    let init = this.parseExpression(true)\n    if (this.tok.type === tt._in || this.isContextual(\"of\")) {\n      if (this.options.ecmaVersion >= 9 && this.tok.type !== tt._in) {\n        node.await = isAwait\n      }\n      return this.parseForIn(node, this.toAssignable(init))\n    }\n    return this.parseFor(node, init)\n\n  case tt._function:\n    this.next()\n    return this.parseFunction(node, true)\n\n  case tt._if:\n    this.next()\n    node.test = this.parseParenExpression()\n    node.consequent = this.parseStatement()\n    node.alternate = this.eat(tt._else) ? this.parseStatement() : null\n    return this.finishNode(node, \"IfStatement\")\n\n  case tt._return:\n    this.next()\n    if (this.eat(tt.semi) || this.canInsertSemicolon()) node.argument = null\n    else { node.argument = this.parseExpression(); this.semicolon() }\n    return this.finishNode(node, \"ReturnStatement\")\n\n  case tt._switch:\n    let blockIndent = this.curIndent, line = this.curLineStart\n    this.next()\n    node.discriminant = this.parseParenExpression()\n    node.cases = []\n    this.pushCx()\n    this.expect(tt.braceL)\n\n    let cur\n    while (!this.closes(tt.braceR, blockIndent, line, true)) {\n      if (this.tok.type === tt._case || this.tok.type === tt._default) {\n        let isCase = this.tok.type === tt._case\n        if (cur) this.finishNode(cur, \"SwitchCase\")\n        node.cases.push(cur = this.startNode())\n        cur.consequent = []\n        this.next()\n        if (isCase) cur.test = this.parseExpression()\n        else cur.test = null\n        this.expect(tt.colon)\n      } else {\n        if (!cur) {\n          node.cases.push(cur = this.startNode())\n          cur.consequent = []\n          cur.test = null\n        }\n        cur.consequent.push(this.parseStatement())\n      }\n    }\n    if (cur) this.finishNode(cur, \"SwitchCase\")\n    this.popCx()\n    this.eat(tt.braceR)\n    return this.finishNode(node, \"SwitchStatement\")\n\n  case tt._throw:\n    this.next()\n    node.argument = this.parseExpression()\n    this.semicolon()\n    return this.finishNode(node, \"ThrowStatement\")\n\n  case tt._try:\n    this.next()\n    node.block = this.parseBlock()\n    node.handler = null\n    if (this.tok.type === tt._catch) {\n      let clause = this.startNode()\n      this.next()\n      if (this.eat(tt.parenL)) {\n        clause.param = this.toAssignable(this.parseExprAtom(), true)\n        this.expect(tt.parenR)\n      } else {\n        clause.param = null\n      }\n      clause.body = this.parseBlock()\n      node.handler = this.finishNode(clause, \"CatchClause\")\n    }\n    node.finalizer = this.eat(tt._finally) ? this.parseBlock() : null\n    if (!node.handler && !node.finalizer) return node.block\n    return this.finishNode(node, \"TryStatement\")\n\n  case tt._var:\n  case tt._const:\n    return this.parseVar(node, false, kind || this.tok.value)\n\n  case tt._while:\n    this.next()\n    node.test = this.parseParenExpression()\n    node.body = this.parseStatement()\n    return this.finishNode(node, \"WhileStatement\")\n\n  case tt._with:\n    this.next()\n    node.object = this.parseParenExpression()\n    node.body = this.parseStatement()\n    return this.finishNode(node, \"WithStatement\")\n\n  case tt.braceL:\n    return this.parseBlock()\n\n  case tt.semi:\n    this.next()\n    return this.finishNode(node, \"EmptyStatement\")\n\n  case tt._class:\n    return this.parseClass(true)\n\n  case tt._import:\n    return this.parseImport()\n\n  case tt._export:\n    return this.parseExport()\n\n  default:\n    if (this.toks.isAsyncFunction()) {\n      this.next()\n      this.next()\n      return this.parseFunction(node, true, true)\n    }\n    let expr = this.parseExpression()\n    if (isDummy(expr)) {\n      this.next()\n      if (this.tok.type === tt.eof) return this.finishNode(node, \"EmptyStatement\")\n      return this.parseStatement()\n    } else if (starttype === tt.name && expr.type === \"Identifier\" && this.eat(tt.colon)) {\n      node.body = this.parseStatement()\n      node.label = expr\n      return this.finishNode(node, \"LabeledStatement\")\n    } else {\n      node.expression = expr\n      this.semicolon()\n      return this.finishNode(node, \"ExpressionStatement\")\n    }\n  }\n}\n\nlp.parseBlock = function() {\n  let node = this.startNode()\n  this.pushCx()\n  this.expect(tt.braceL)\n  let blockIndent = this.curIndent, line = this.curLineStart\n  node.body = []\n  while (!this.closes(tt.braceR, blockIndent, line, true))\n    node.body.push(this.parseStatement())\n  this.popCx()\n  this.eat(tt.braceR)\n  return this.finishNode(node, \"BlockStatement\")\n}\n\nlp.parseFor = function(node, init) {\n  node.init = init\n  node.test = node.update = null\n  if (this.eat(tt.semi) && this.tok.type !== tt.semi) node.test = this.parseExpression()\n  if (this.eat(tt.semi) && this.tok.type !== tt.parenR) node.update = this.parseExpression()\n  this.popCx()\n  this.expect(tt.parenR)\n  node.body = this.parseStatement()\n  return this.finishNode(node, \"ForStatement\")\n}\n\nlp.parseForIn = function(node, init) {\n  let type = this.tok.type === tt._in ? \"ForInStatement\" : \"ForOfStatement\"\n  this.next()\n  node.left = init\n  node.right = this.parseExpression()\n  this.popCx()\n  this.expect(tt.parenR)\n  node.body = this.parseStatement()\n  return this.finishNode(node, type)\n}\n\nlp.parseVar = function(node, noIn, kind) {\n  node.kind = kind\n  this.next()\n  node.declarations = []\n  do {\n    let decl = this.startNode()\n    decl.id = this.options.ecmaVersion >= 6 ? this.toAssignable(this.parseExprAtom(), true) : this.parseIdent()\n    decl.init = this.eat(tt.eq) ? this.parseMaybeAssign(noIn) : null\n    node.declarations.push(this.finishNode(decl, \"VariableDeclarator\"))\n  } while (this.eat(tt.comma))\n  if (!node.declarations.length) {\n    let decl = this.startNode()\n    decl.id = this.dummyIdent()\n    node.declarations.push(this.finishNode(decl, \"VariableDeclarator\"))\n  }\n  if (!noIn) this.semicolon()\n  return this.finishNode(node, \"VariableDeclaration\")\n}\n\nlp.parseClass = function(isStatement) {\n  let node = this.startNode()\n  this.next()\n  if (this.tok.type === tt.name) node.id = this.parseIdent()\n  else if (isStatement === true) node.id = this.dummyIdent()\n  else node.id = null\n  node.superClass = this.eat(tt._extends) ? this.parseExpression() : null\n  node.body = this.startNode()\n  node.body.body = []\n  this.pushCx()\n  let indent = this.curIndent + 1, line = this.curLineStart\n  this.eat(tt.braceL)\n  if (this.curIndent + 1 < indent) { indent = this.curIndent; line = this.curLineStart }\n  while (!this.closes(tt.braceR, indent, line)) {\n    if (this.semicolon()) continue\n    let method = this.startNode(), isGenerator, isAsync\n    if (this.options.ecmaVersion >= 6) {\n      method.static = false\n      isGenerator = this.eat(tt.star)\n    }\n    this.parsePropertyName(method)\n    if (isDummy(method.key)) { if (isDummy(this.parseMaybeAssign())) this.next(); this.eat(tt.comma); continue }\n    if (method.key.type === \"Identifier\" && !method.computed && method.key.name === \"static\" &&\n        (this.tok.type !== tt.parenL && this.tok.type !== tt.braceL)) {\n      method.static = true\n      isGenerator = this.eat(tt.star)\n      this.parsePropertyName(method)\n    } else {\n      method.static = false\n    }\n    if (!method.computed &&\n        method.key.type === \"Identifier\" && method.key.name === \"async\" && this.tok.type !== tt.parenL &&\n        !this.canInsertSemicolon()) {\n      isAsync = true\n      isGenerator = this.options.ecmaVersion >= 9 && this.eat(tt.star)\n      this.parsePropertyName(method)\n    } else {\n      isAsync = false\n    }\n    if (this.options.ecmaVersion >= 5 && method.key.type === \"Identifier\" &&\n        !method.computed && (method.key.name === \"get\" || method.key.name === \"set\") &&\n        this.tok.type !== tt.parenL && this.tok.type !== tt.braceL) {\n      method.kind = method.key.name\n      this.parsePropertyName(method)\n      method.value = this.parseMethod(false)\n    } else {\n      if (!method.computed && !method.static && !isGenerator && !isAsync && (\n        method.key.type === \"Identifier\" && method.key.name === \"constructor\" ||\n          method.key.type === \"Literal\" && method.key.value === \"constructor\")) {\n        method.kind = \"constructor\"\n      } else {\n        method.kind = \"method\"\n      }\n      method.value = this.parseMethod(isGenerator, isAsync)\n    }\n    node.body.body.push(this.finishNode(method, \"MethodDefinition\"))\n  }\n  this.popCx()\n  if (!this.eat(tt.braceR)) {\n    // If there is no closing brace, make the node span to the start\n    // of the next token (this is useful for Tern)\n    this.last.end = this.tok.start\n    if (this.options.locations) this.last.loc.end = this.tok.loc.start\n  }\n  this.semicolon()\n  this.finishNode(node.body, \"ClassBody\")\n  return this.finishNode(node, isStatement ? \"ClassDeclaration\" : \"ClassExpression\")\n}\n\nlp.parseFunction = function(node, isStatement, isAsync) {\n  let oldInAsync = this.inAsync, oldInFunction = this.inFunction\n  this.initFunction(node)\n  if (this.options.ecmaVersion >= 6) {\n    node.generator = this.eat(tt.star)\n  }\n  if (this.options.ecmaVersion >= 8) {\n    node.async = !!isAsync\n  }\n  if (this.tok.type === tt.name) node.id = this.parseIdent()\n  else if (isStatement === true) node.id = this.dummyIdent()\n  this.inAsync = node.async\n  this.inFunction = true\n  node.params = this.parseFunctionParams()\n  node.body = this.parseBlock()\n  this.toks.adaptDirectivePrologue(node.body.body)\n  this.inAsync = oldInAsync\n  this.inFunction = oldInFunction\n  return this.finishNode(node, isStatement ? \"FunctionDeclaration\" : \"FunctionExpression\")\n}\n\nlp.parseExport = function() {\n  let node = this.startNode()\n  this.next()\n  if (this.eat(tt.star)) {\n    node.source = this.eatContextual(\"from\") ? this.parseExprAtom() : this.dummyString()\n    return this.finishNode(node, \"ExportAllDeclaration\")\n  }\n  if (this.eat(tt._default)) {\n    // export default (function foo() {}) // This is FunctionExpression.\n    let isAsync\n    if (this.tok.type === tt._function || (isAsync = this.toks.isAsyncFunction())) {\n      let fNode = this.startNode()\n      this.next()\n      if (isAsync) this.next()\n      node.declaration = this.parseFunction(fNode, \"nullableID\", isAsync)\n    } else if (this.tok.type === tt._class) {\n      node.declaration = this.parseClass(\"nullableID\")\n    } else {\n      node.declaration = this.parseMaybeAssign()\n      this.semicolon()\n    }\n    return this.finishNode(node, \"ExportDefaultDeclaration\")\n  }\n  if (this.tok.type.keyword || this.toks.isLet() || this.toks.isAsyncFunction()) {\n    node.declaration = this.parseStatement()\n    node.specifiers = []\n    node.source = null\n  } else {\n    node.declaration = null\n    node.specifiers = this.parseExportSpecifierList()\n    node.source = this.eatContextual(\"from\") ? this.parseExprAtom() : null\n    this.semicolon()\n  }\n  return this.finishNode(node, \"ExportNamedDeclaration\")\n}\n\nlp.parseImport = function() {\n  let node = this.startNode()\n  this.next()\n  if (this.tok.type === tt.string) {\n    node.specifiers = []\n    node.source = this.parseExprAtom()\n  } else {\n    let elt\n    if (this.tok.type === tt.name && this.tok.value !== \"from\") {\n      elt = this.startNode()\n      elt.local = this.parseIdent()\n      this.finishNode(elt, \"ImportDefaultSpecifier\")\n      this.eat(tt.comma)\n    }\n    node.specifiers = this.parseImportSpecifiers()\n    node.source = this.eatContextual(\"from\") && this.tok.type === tt.string ? this.parseExprAtom() : this.dummyString()\n    if (elt) node.specifiers.unshift(elt)\n  }\n  this.semicolon()\n  return this.finishNode(node, \"ImportDeclaration\")\n}\n\nlp.parseImportSpecifiers = function() {\n  let elts = []\n  if (this.tok.type === tt.star) {\n    let elt = this.startNode()\n    this.next()\n    elt.local = this.eatContextual(\"as\") ? this.parseIdent() : this.dummyIdent()\n    elts.push(this.finishNode(elt, \"ImportNamespaceSpecifier\"))\n  } else {\n    let indent = this.curIndent, line = this.curLineStart, continuedLine = this.nextLineStart\n    this.pushCx()\n    this.eat(tt.braceL)\n    if (this.curLineStart > continuedLine) continuedLine = this.curLineStart\n    while (!this.closes(tt.braceR, indent + (this.curLineStart <= continuedLine ? 1 : 0), line)) {\n      let elt = this.startNode()\n      if (this.eat(tt.star)) {\n        elt.local = this.eatContextual(\"as\") ? this.parseIdent() : this.dummyIdent()\n        this.finishNode(elt, \"ImportNamespaceSpecifier\")\n      } else {\n        if (this.isContextual(\"from\")) break\n        elt.imported = this.parseIdent()\n        if (isDummy(elt.imported)) break\n        elt.local = this.eatContextual(\"as\") ? this.parseIdent() : elt.imported\n        this.finishNode(elt, \"ImportSpecifier\")\n      }\n      elts.push(elt)\n      this.eat(tt.comma)\n    }\n    this.eat(tt.braceR)\n    this.popCx()\n  }\n  return elts\n}\n\nlp.parseExportSpecifierList = function() {\n  let elts = []\n  let indent = this.curIndent, line = this.curLineStart, continuedLine = this.nextLineStart\n  this.pushCx()\n  this.eat(tt.braceL)\n  if (this.curLineStart > continuedLine) continuedLine = this.curLineStart\n  while (!this.closes(tt.braceR, indent + (this.curLineStart <= continuedLine ? 1 : 0), line)) {\n    if (this.isContextual(\"from\")) break\n    let elt = this.startNode()\n    elt.local = this.parseIdent()\n    if (isDummy(elt.local)) break\n    elt.exported = this.eatContextual(\"as\") ? this.parseIdent() : elt.local\n    this.finishNode(elt, \"ExportSpecifier\")\n    elts.push(elt)\n    this.eat(tt.comma)\n  }\n  this.eat(tt.braceR)\n  this.popCx()\n  return elts\n}\n", "import {<PERSON><PERSON><PERSON><PERSON><PERSON>} from \"./state\"\nimport {isDummy} from \"./parseutil\"\nimport {tokTypes as tt} from \"acorn\"\n\nconst lp = LooseParser.prototype\n\nlp.checkLVal = function(expr) {\n  if (!expr) return expr\n  switch (expr.type) {\n  case \"Identifier\":\n  case \"MemberExpression\":\n    return expr\n\n  case \"ParenthesizedExpression\":\n    expr.expression = this.checkLVal(expr.expression)\n    return expr\n\n  default:\n    return this.dummyIdent()\n  }\n}\n\nlp.parseExpression = function(noIn) {\n  let start = this.storeCurrentPos()\n  let expr = this.parseMaybeAssign(noIn)\n  if (this.tok.type === tt.comma) {\n    let node = this.startNodeAt(start)\n    node.expressions = [expr]\n    while (this.eat(tt.comma)) node.expressions.push(this.parseMaybeAssign(noIn))\n    return this.finishNode(node, \"SequenceExpression\")\n  }\n  return expr\n}\n\nlp.parseParenExpression = function() {\n  this.pushCx()\n  this.expect(tt.parenL)\n  let val = this.parseExpression()\n  this.popCx()\n  this.expect(tt.parenR)\n  return val\n}\n\nlp.parseMaybeAssign = function(noIn) {\n  if (this.toks.isContextual(\"yield\")) {\n    let node = this.startNode()\n    this.next()\n    if (this.semicolon() || this.canInsertSemicolon() || (this.tok.type !== tt.star && !this.tok.type.startsExpr)) {\n      node.delegate = false\n      node.argument = null\n    } else {\n      node.delegate = this.eat(tt.star)\n      node.argument = this.parseMaybeAssign()\n    }\n    return this.finishNode(node, \"YieldExpression\")\n  }\n\n  let start = this.storeCurrentPos()\n  let left = this.parseMaybeConditional(noIn)\n  if (this.tok.type.isAssign) {\n    let node = this.startNodeAt(start)\n    node.operator = this.tok.value\n    node.left = this.tok.type === tt.eq ? this.toAssignable(left) : this.checkLVal(left)\n    this.next()\n    node.right = this.parseMaybeAssign(noIn)\n    return this.finishNode(node, \"AssignmentExpression\")\n  }\n  return left\n}\n\nlp.parseMaybeConditional = function(noIn) {\n  let start = this.storeCurrentPos()\n  let expr = this.parseExprOps(noIn)\n  if (this.eat(tt.question)) {\n    let node = this.startNodeAt(start)\n    node.test = expr\n    node.consequent = this.parseMaybeAssign()\n    node.alternate = this.expect(tt.colon) ? this.parseMaybeAssign(noIn) : this.dummyIdent()\n    return this.finishNode(node, \"ConditionalExpression\")\n  }\n  return expr\n}\n\nlp.parseExprOps = function(noIn) {\n  let start = this.storeCurrentPos()\n  let indent = this.curIndent, line = this.curLineStart\n  return this.parseExprOp(this.parseMaybeUnary(false), start, -1, noIn, indent, line)\n}\n\nlp.parseExprOp = function(left, start, minPrec, noIn, indent, line) {\n  if (this.curLineStart !== line && this.curIndent < indent && this.tokenStartsLine()) return left\n  let prec = this.tok.type.binop\n  if (prec != null && (!noIn || this.tok.type !== tt._in)) {\n    if (prec > minPrec) {\n      let node = this.startNodeAt(start)\n      node.left = left\n      node.operator = this.tok.value\n      this.next()\n      if (this.curLineStart !== line && this.curIndent < indent && this.tokenStartsLine()) {\n        node.right = this.dummyIdent()\n      } else {\n        let rightStart = this.storeCurrentPos()\n        node.right = this.parseExprOp(this.parseMaybeUnary(false), rightStart, prec, noIn, indent, line)\n      }\n      this.finishNode(node, /&&|\\|\\|/.test(node.operator) ? \"LogicalExpression\" : \"BinaryExpression\")\n      return this.parseExprOp(node, start, minPrec, noIn, indent, line)\n    }\n  }\n  return left\n}\n\nlp.parseMaybeUnary = function(sawUnary) {\n  let start = this.storeCurrentPos(), expr\n  if (this.options.ecmaVersion >= 8 && this.toks.isContextual(\"await\") &&\n    (this.inAsync || (!this.inFunction && this.options.allowAwaitOutsideFunction))\n  ) {\n    expr = this.parseAwait()\n    sawUnary = true\n  } else if (this.tok.type.prefix) {\n    let node = this.startNode(), update = this.tok.type === tt.incDec\n    if (!update) sawUnary = true\n    node.operator = this.tok.value\n    node.prefix = true\n    this.next()\n    node.argument = this.parseMaybeUnary(true)\n    if (update) node.argument = this.checkLVal(node.argument)\n    expr = this.finishNode(node, update ? \"UpdateExpression\" : \"UnaryExpression\")\n  } else if (this.tok.type === tt.ellipsis) {\n    let node = this.startNode()\n    this.next()\n    node.argument = this.parseMaybeUnary(sawUnary)\n    expr = this.finishNode(node, \"SpreadElement\")\n  } else {\n    expr = this.parseExprSubscripts()\n    while (this.tok.type.postfix && !this.canInsertSemicolon()) {\n      let node = this.startNodeAt(start)\n      node.operator = this.tok.value\n      node.prefix = false\n      node.argument = this.checkLVal(expr)\n      this.next()\n      expr = this.finishNode(node, \"UpdateExpression\")\n    }\n  }\n\n  if (!sawUnary && this.eat(tt.starstar)) {\n    let node = this.startNodeAt(start)\n    node.operator = \"**\"\n    node.left = expr\n    node.right = this.parseMaybeUnary(false)\n    return this.finishNode(node, \"BinaryExpression\")\n  }\n\n  return expr\n}\n\nlp.parseExprSubscripts = function() {\n  let start = this.storeCurrentPos()\n  return this.parseSubscripts(this.parseExprAtom(), start, false, this.curIndent, this.curLineStart)\n}\n\nlp.parseSubscripts = function(base, start, noCalls, startIndent, line) {\n  for (;;) {\n    if (this.curLineStart !== line && this.curIndent <= startIndent && this.tokenStartsLine()) {\n      if (this.tok.type === tt.dot && this.curIndent === startIndent)\n        --startIndent\n      else\n        return base\n    }\n\n    let maybeAsyncArrow = base.type === \"Identifier\" && base.name === \"async\" && !this.canInsertSemicolon()\n\n    if (this.eat(tt.dot)) {\n      let node = this.startNodeAt(start)\n      node.object = base\n      if (this.curLineStart !== line && this.curIndent <= startIndent && this.tokenStartsLine())\n        node.property = this.dummyIdent()\n      else\n        node.property = this.parsePropertyAccessor() || this.dummyIdent()\n      node.computed = false\n      base = this.finishNode(node, \"MemberExpression\")\n    } else if (this.tok.type === tt.bracketL) {\n      this.pushCx()\n      this.next()\n      let node = this.startNodeAt(start)\n      node.object = base\n      node.property = this.parseExpression()\n      node.computed = true\n      this.popCx()\n      this.expect(tt.bracketR)\n      base = this.finishNode(node, \"MemberExpression\")\n    } else if (!noCalls && this.tok.type === tt.parenL) {\n      let exprList = this.parseExprList(tt.parenR)\n      if (maybeAsyncArrow && this.eat(tt.arrow))\n        return this.parseArrowExpression(this.startNodeAt(start), exprList, true)\n      let node = this.startNodeAt(start)\n      node.callee = base\n      node.arguments = exprList\n      base = this.finishNode(node, \"CallExpression\")\n    } else if (this.tok.type === tt.backQuote) {\n      let node = this.startNodeAt(start)\n      node.tag = base\n      node.quasi = this.parseTemplate()\n      base = this.finishNode(node, \"TaggedTemplateExpression\")\n    } else {\n      return base\n    }\n  }\n}\n\nlp.parseExprAtom = function() {\n  let node\n  switch (this.tok.type) {\n  case tt._this:\n  case tt._super:\n    let type = this.tok.type === tt._this ? \"ThisExpression\" : \"Super\"\n    node = this.startNode()\n    this.next()\n    return this.finishNode(node, type)\n\n  case tt.name:\n    let start = this.storeCurrentPos()\n    let id = this.parseIdent()\n    let isAsync = false\n    if (id.name === \"async\" && !this.canInsertSemicolon()) {\n      if (this.eat(tt._function))\n        return this.parseFunction(this.startNodeAt(start), false, true)\n      if (this.tok.type === tt.name) {\n        id = this.parseIdent()\n        isAsync = true\n      }\n    }\n    return this.eat(tt.arrow) ? this.parseArrowExpression(this.startNodeAt(start), [id], isAsync) : id\n\n  case tt.regexp:\n    node = this.startNode()\n    let val = this.tok.value\n    node.regex = {pattern: val.pattern, flags: val.flags}\n    node.value = val.value\n    node.raw = this.input.slice(this.tok.start, this.tok.end)\n    this.next()\n    return this.finishNode(node, \"Literal\")\n\n  case tt.num: case tt.string:\n    node = this.startNode()\n    node.value = this.tok.value\n    node.raw = this.input.slice(this.tok.start, this.tok.end)\n    this.next()\n    return this.finishNode(node, \"Literal\")\n\n  case tt._null: case tt._true: case tt._false:\n    node = this.startNode()\n    node.value = this.tok.type === tt._null ? null : this.tok.type === tt._true\n    node.raw = this.tok.type.keyword\n    this.next()\n    return this.finishNode(node, \"Literal\")\n\n  case tt.parenL:\n    let parenStart = this.storeCurrentPos()\n    this.next()\n    let inner = this.parseExpression()\n    this.expect(tt.parenR)\n    if (this.eat(tt.arrow)) {\n      // (a,)=>a // SequenceExpression makes dummy in the last hole. Drop the dummy.\n      let params = inner.expressions || [inner]\n      if (params.length && isDummy(params[params.length - 1]))\n        params.pop()\n      return this.parseArrowExpression(this.startNodeAt(parenStart), params)\n    }\n    if (this.options.preserveParens) {\n      let par = this.startNodeAt(parenStart)\n      par.expression = inner\n      inner = this.finishNode(par, \"ParenthesizedExpression\")\n    }\n    return inner\n\n  case tt.bracketL:\n    node = this.startNode()\n    node.elements = this.parseExprList(tt.bracketR, true)\n    return this.finishNode(node, \"ArrayExpression\")\n\n  case tt.braceL:\n    return this.parseObj()\n\n  case tt._class:\n    return this.parseClass(false)\n\n  case tt._function:\n    node = this.startNode()\n    this.next()\n    return this.parseFunction(node, false)\n\n  case tt._new:\n    return this.parseNew()\n\n  case tt.backQuote:\n    return this.parseTemplate()\n\n  default:\n    return this.dummyIdent()\n  }\n}\n\nlp.parseNew = function() {\n  let node = this.startNode(), startIndent = this.curIndent, line = this.curLineStart\n  let meta = this.parseIdent(true)\n  if (this.options.ecmaVersion >= 6 && this.eat(tt.dot)) {\n    node.meta = meta\n    node.property = this.parseIdent(true)\n    return this.finishNode(node, \"MetaProperty\")\n  }\n  let start = this.storeCurrentPos()\n  node.callee = this.parseSubscripts(this.parseExprAtom(), start, true, startIndent, line)\n  if (this.tok.type === tt.parenL) {\n    node.arguments = this.parseExprList(tt.parenR)\n  } else {\n    node.arguments = []\n  }\n  return this.finishNode(node, \"NewExpression\")\n}\n\nlp.parseTemplateElement = function() {\n  let elem = this.startNode()\n\n  // The loose parser accepts invalid unicode escapes even in untagged templates.\n  if (this.tok.type === tt.invalidTemplate) {\n    elem.value = {\n      raw: this.tok.value,\n      cooked: null\n    }\n  } else {\n    elem.value = {\n      raw: this.input.slice(this.tok.start, this.tok.end).replace(/\\r\\n?/g, \"\\n\"),\n      cooked: this.tok.value\n    }\n  }\n  this.next()\n  elem.tail = this.tok.type === tt.backQuote\n  return this.finishNode(elem, \"TemplateElement\")\n}\n\nlp.parseTemplate = function() {\n  let node = this.startNode()\n  this.next()\n  node.expressions = []\n  let curElt = this.parseTemplateElement()\n  node.quasis = [curElt]\n  while (!curElt.tail) {\n    this.next()\n    node.expressions.push(this.parseExpression())\n    if (this.expect(tt.braceR)) {\n      curElt = this.parseTemplateElement()\n    } else {\n      curElt = this.startNode()\n      curElt.value = {cooked: \"\", raw: \"\"}\n      curElt.tail = true\n      this.finishNode(curElt, \"TemplateElement\")\n    }\n    node.quasis.push(curElt)\n  }\n  this.expect(tt.backQuote)\n  return this.finishNode(node, \"TemplateLiteral\")\n}\n\nlp.parseObj = function() {\n  let node = this.startNode()\n  node.properties = []\n  this.pushCx()\n  let indent = this.curIndent + 1, line = this.curLineStart\n  this.eat(tt.braceL)\n  if (this.curIndent + 1 < indent) { indent = this.curIndent; line = this.curLineStart }\n  while (!this.closes(tt.braceR, indent, line)) {\n    let prop = this.startNode(), isGenerator, isAsync, start\n    if (this.options.ecmaVersion >= 9 && this.eat(tt.ellipsis)) {\n      prop.argument = this.parseMaybeAssign()\n      node.properties.push(this.finishNode(prop, \"SpreadElement\"))\n      this.eat(tt.comma)\n      continue\n    }\n    if (this.options.ecmaVersion >= 6) {\n      start = this.storeCurrentPos()\n      prop.method = false\n      prop.shorthand = false\n      isGenerator = this.eat(tt.star)\n    }\n    this.parsePropertyName(prop)\n    if (this.toks.isAsyncProp(prop)) {\n      isAsync = true\n      isGenerator = this.options.ecmaVersion >= 9 && this.eat(tt.star)\n      this.parsePropertyName(prop)\n    } else {\n      isAsync = false\n    }\n    if (isDummy(prop.key)) { if (isDummy(this.parseMaybeAssign())) this.next(); this.eat(tt.comma); continue }\n    if (this.eat(tt.colon)) {\n      prop.kind = \"init\"\n      prop.value = this.parseMaybeAssign()\n    } else if (this.options.ecmaVersion >= 6 && (this.tok.type === tt.parenL || this.tok.type === tt.braceL)) {\n      prop.kind = \"init\"\n      prop.method = true\n      prop.value = this.parseMethod(isGenerator, isAsync)\n    } else if (this.options.ecmaVersion >= 5 && prop.key.type === \"Identifier\" &&\n               !prop.computed && (prop.key.name === \"get\" || prop.key.name === \"set\") &&\n               (this.tok.type !== tt.comma && this.tok.type !== tt.braceR && this.tok.type !== tt.eq)) {\n      prop.kind = prop.key.name\n      this.parsePropertyName(prop)\n      prop.value = this.parseMethod(false)\n    } else {\n      prop.kind = \"init\"\n      if (this.options.ecmaVersion >= 6) {\n        if (this.eat(tt.eq)) {\n          let assign = this.startNodeAt(start)\n          assign.operator = \"=\"\n          assign.left = prop.key\n          assign.right = this.parseMaybeAssign()\n          prop.value = this.finishNode(assign, \"AssignmentExpression\")\n        } else {\n          prop.value = prop.key\n        }\n      } else {\n        prop.value = this.dummyIdent()\n      }\n      prop.shorthand = true\n    }\n    node.properties.push(this.finishNode(prop, \"Property\"))\n    this.eat(tt.comma)\n  }\n  this.popCx()\n  if (!this.eat(tt.braceR)) {\n    // If there is no closing brace, make the node span to the start\n    // of the next token (this is useful for Tern)\n    this.last.end = this.tok.start\n    if (this.options.locations) this.last.loc.end = this.tok.loc.start\n  }\n  return this.finishNode(node, \"ObjectExpression\")\n}\n\nlp.parsePropertyName = function(prop) {\n  if (this.options.ecmaVersion >= 6) {\n    if (this.eat(tt.bracketL)) {\n      prop.computed = true\n      prop.key = this.parseExpression()\n      this.expect(tt.bracketR)\n      return\n    } else {\n      prop.computed = false\n    }\n  }\n  let key = (this.tok.type === tt.num || this.tok.type === tt.string) ? this.parseExprAtom() : this.parseIdent()\n  prop.key = key || this.dummyIdent()\n}\n\nlp.parsePropertyAccessor = function() {\n  if (this.tok.type === tt.name || this.tok.type.keyword) return this.parseIdent()\n}\n\nlp.parseIdent = function() {\n  let name = this.tok.type === tt.name ? this.tok.value : this.tok.type.keyword\n  if (!name) return this.dummyIdent()\n  let node = this.startNode()\n  this.next()\n  node.name = name\n  return this.finishNode(node, \"Identifier\")\n}\n\nlp.initFunction = function(node) {\n  node.id = null\n  node.params = []\n  if (this.options.ecmaVersion >= 6) {\n    node.generator = false\n    node.expression = false\n  }\n  if (this.options.ecmaVersion >= 8)\n    node.async = false\n}\n\n// Convert existing expression atom to assignable pattern\n// if possible.\n\nlp.toAssignable = function(node, binding) {\n  if (!node || node.type === \"Identifier\" || (node.type === \"MemberExpression\" && !binding)) {\n    // Okay\n  } else if (node.type === \"ParenthesizedExpression\") {\n    this.toAssignable(node.expression, binding)\n  } else if (this.options.ecmaVersion < 6) {\n    return this.dummyIdent()\n  } else if (node.type === \"ObjectExpression\") {\n    node.type = \"ObjectPattern\"\n    for (let prop of node.properties)\n      this.toAssignable(prop, binding)\n  } else if (node.type === \"ArrayExpression\") {\n    node.type = \"ArrayPattern\"\n    this.toAssignableList(node.elements, binding)\n  } else if (node.type === \"Property\") {\n    this.toAssignable(node.value, binding)\n  } else if (node.type === \"SpreadElement\") {\n    node.type = \"RestElement\"\n    this.toAssignable(node.argument, binding)\n  } else if (node.type === \"AssignmentExpression\") {\n    node.type = \"AssignmentPattern\"\n    delete node.operator\n  } else {\n    return this.dummyIdent()\n  }\n  return node\n}\n\nlp.toAssignableList = function(exprList, binding) {\n  for (let expr of exprList)\n    this.toAssignable(expr, binding)\n  return exprList\n}\n\nlp.parseFunctionParams = function(params) {\n  params = this.parseExprList(tt.parenR)\n  return this.toAssignableList(params, true)\n}\n\nlp.parseMethod = function(isGenerator, isAsync) {\n  let node = this.startNode(), oldInAsync = this.inAsync, oldInFunction = this.inFunction\n  this.initFunction(node)\n  if (this.options.ecmaVersion >= 6)\n    node.generator = !!isGenerator\n  if (this.options.ecmaVersion >= 8)\n    node.async = !!isAsync\n  this.inAsync = node.async\n  this.inFunction = true\n  node.params = this.parseFunctionParams()\n  node.body = this.parseBlock()\n  this.toks.adaptDirectivePrologue(node.body.body)\n  this.inAsync = oldInAsync\n  this.inFunction = oldInFunction\n  return this.finishNode(node, \"FunctionExpression\")\n}\n\nlp.parseArrowExpression = function(node, params, isAsync) {\n  let oldInAsync = this.inAsync, oldInFunction = this.inFunction\n  this.initFunction(node)\n  if (this.options.ecmaVersion >= 8)\n    node.async = !!isAsync\n  this.inAsync = node.async\n  this.inFunction = true\n  node.params = this.toAssignableList(params, true)\n  node.expression = this.tok.type !== tt.braceL\n  if (node.expression) {\n    node.body = this.parseMaybeAssign()\n  } else {\n    node.body = this.parseBlock()\n    this.toks.adaptDirectivePrologue(node.body.body)\n  }\n  this.inAsync = oldInAsync\n  this.inFunction = oldInFunction\n  return this.finishNode(node, \"ArrowFunctionExpression\")\n}\n\nlp.parseExprList = function(close, allowEmpty) {\n  this.pushCx()\n  let indent = this.curIndent, line = this.curLineStart, elts = []\n  this.next() // Opening bracket\n  while (!this.closes(close, indent + 1, line)) {\n    if (this.eat(tt.comma)) {\n      elts.push(allowEmpty ? null : this.dummyIdent())\n      continue\n    }\n    let elt = this.parseMaybeAssign()\n    if (isDummy(elt)) {\n      if (this.closes(close, indent, line)) break\n      this.next()\n    } else {\n      elts.push(elt)\n    }\n    this.eat(tt.comma)\n  }\n  this.popCx()\n  if (!this.eat(close)) {\n    // If there is no closing brace, make the node span to the start\n    // of the next token (this is useful for Tern)\n    this.last.end = this.tok.start\n    if (this.options.locations) this.last.loc.end = this.tok.loc.start\n  }\n  return elts\n}\n\nlp.parseAwait = function() {\n  let node = this.startNode()\n  this.next()\n  node.argument = this.parseMaybeUnary()\n  return this.finishNode(node, \"AwaitExpression\")\n}\n", "// Acorn: Loose parser\n//\n// This module provides an alternative parser that exposes that same\n// interface as the main module's `parse` function, but will try to\n// parse anything as JavaScript, repairing syntax error the best it\n// can. There are circumstances in which it will raise an error and\n// give up, but they are very rare. The resulting AST will be a mostly\n// valid JavaScript AST (as per the [Mozilla parser API][api], except\n// that:\n//\n// - Return outside functions is allowed\n//\n// - Label consistency (no conflicts, break only to existing labels)\n//   is not enforced.\n//\n// - Bogus Identifier nodes with a name of `\"✖\"` are inserted whenever\n//   the parser got too confused to return anything meaningful.\n//\n// [api]: https://developer.mozilla.org/en-US/docs/SpiderMonkey/Parser_API\n//\n// The expected use for this is to *first* try `acorn.parse`, and only\n// if that fails switch to the loose parser. The loose parser might\n// parse badly indented code incorrectly, so **don't** use it as your\n// default parser.\n//\n// Quite a lot of acorn.js is duplicated here. The alternative was to\n// add a *lot* of extra cruft to that file, making it less readable\n// and slower. Copying and editing the code allowed me to make\n// invasive changes and simplifications without creating a complicated\n// tangle.\n\nimport {defaultOptions} from \"acorn\"\nimport {LooseParser} from \"./state\"\nimport \"./tokenize\"\nimport \"./statement\"\nimport \"./expression\"\n\nexport {LooseParser} from \"./state\"\n\ndefaultOptions.tabSize = 4\n\nexport function parse(input, options) {\n  return LooseParser.parse(input, options)\n}\n"], "names": ["tt", "SourceLocation", "Node", "lineBreak", "let", "this", "isNewLine", "<PERSON><PERSON><PERSON>", "const", "Token", "getLineInfo", "lineBreakG", "lp", "init", "decl", "elt", "node", "defaultOptions"], "mappings": ";;;;;;AAEA,SAAS,IAAI,GAAG,EAAE;;AAElB,AAAO,IAAM,WAAW,GAAC,oBACZ,CAAC,KAAK,EAAE,OAAY,EAAE;mCAAP,GAAG,EAAE;;EAC/B,IAAM,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,EAAC;EACnE,IAAM,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAO;EAClC,IAAM,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAK;EAC9B,IAAM,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,EAAEA,cAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAC;EACzD,IAAM,CAAC,GAAG,CAAC,mBAAmB,GAAG,KAAI;EACrC,IAAM,CAAC,GAAG,CAAC,qBAAqB,GAAG,KAAI;EACvC,IAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;IAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAE;IACpC,IAAM,CAAC,GAAG,CAAC,GAAG,GAAG,IAAIC,oBAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC;GACzD;EACH,IAAM,CAAC,KAAK,GAAG,GAAE;EACjB,IAAM,CAAC,OAAO,GAAG,GAAE;EACnB,IAAM,CAAC,SAAS,GAAG,EAAC;EACpB,IAAM,CAAC,YAAY,GAAG,EAAC;EACvB,IAAM,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAC;EAC1D,IAAM,CAAC,OAAO,GAAG,MAAK;EACtB,IAAM,CAAC,UAAU,GAAG,MAAK;CACxB,CAAA;;AAEH,sBAAE,SAAS,yBAAG;EACZ,OAAS,IAAIC,UAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC;CAC/F,CAAA;;AAEH,sBAAE,eAAe,+BAAG;EAClB,OAAS,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK;CACtF,CAAA;;AAEH,sBAAE,WAAW,yBAAC,GAAG,EAAE;EACjB,IAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;IAC5B,OAAS,IAAIA,UAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;GAC3C,MAAM;IACP,OAAS,IAAIA,UAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;GAChC;CACF,CAAA;;AAEH,sBAAE,UAAU,wBAAC,IAAI,EAAE,IAAI,EAAE;EACvB,IAAM,CAAC,IAAI,GAAG,KAAI;EAClB,IAAM,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAG;EAC1B,IAAM,IAAI,CAAC,OAAO,CAAC,SAAS;IAC1B,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAG,EAAA;EACpC,IAAM,IAAI,CAAC,OAAO,CAAC,MAAM;IACvB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAG,EAAA;EACjC,OAAS,IAAI;CACZ,CAAA;;AAEH,sBAAE,SAAS,uBAAC,IAAI,EAAE;EAChB,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,GAAE;EAC9B,KAAO,CAAC,IAAI,GAAG,KAAI;EACnB,KAAO,CAAC,GAAG,GAAG,KAAK,CAAC,MAAK;EACzB,IAAM,IAAI,CAAC,OAAO,CAAC,SAAS;IAC1B,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,MAAK,EAAA;EACnC,IAAM,IAAI,CAAC,OAAO,CAAC,MAAM;IACvB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAK,EAAA;EAChC,IAAM,CAAC,IAAI,GAAG,CAAC,IAAI,EAAEF,cAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAC;EACnF,OAAS,KAAK;CACb,CAAA;;AAEH,sBAAE,UAAU,0BAAG;EACb,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAC;EAC1C,KAAO,CAAC,IAAI,GAAG,IAAG;EAClB,OAAS,KAAK;CACb,CAAA;;AAEH,sBAAE,WAAW,2BAAG;EACd,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAC;EACvC,KAAO,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,IAAG;EAC/B,OAAS,KAAK;CACb,CAAA;;AAEH,sBAAE,GAAG,iBAAC,IAAI,EAAE;EACV,IAAM,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE;IAC5B,IAAM,CAAC,IAAI,GAAE;IACb,OAAS,IAAI;GACZ,MAAM;IACP,OAAS,KAAK;GACb;CACF,CAAA;;AAEH,sBAAE,YAAY,0BAAC,IAAI,EAAE;EACnB,OAAS,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,IAAI;CAC5D,CAAA;;AAEH,sBAAE,aAAa,2BAAC,IAAI,EAAE;EACpB,OAAS,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,IAAI,CAAC;CACpD,CAAA;;AAEH,sBAAE,kBAAkB,kCAAG;EACrB,OAAS,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,MAAM;IAC9DG,eAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAClE,CAAA;;AAEH,sBAAE,SAAS,yBAAG;EACZ,OAAS,IAAI,CAAC,GAAG,CAACH,cAAE,CAAC,IAAI,CAAC;CACzB,CAAA;;AAEH,sBAAE,MAAM,oBAAC,IAAI,EAAE;;;EACb,IAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAA,OAAO,IAAI,EAAA;EACjC,KAAOI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7B,IAAMC,MAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;MACrC,KAAOD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAAC,MAAI,CAAC,IAAI,GAAE,EAAA;MACzC,OAAS,IAAI;KACZ;GACF;CACF,CAAA;;AAEH,sBAAE,MAAM,sBAAG;EACT,IAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAC;CAClC,CAAA;;AAEH,sBAAE,KAAK,qBAAG;EACR,IAAM,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAE;CACpC,CAAA;;AAEH,sBAAE,OAAO,qBAAC,GAAG,EAAE;EACb,OAAS,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAACC,eAAS,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA,EAAE,IAAG,EAAA;EACjF,OAAS,GAAG;CACX,CAAA;;AAEH,sBAAE,gBAAgB,8BAAC,GAAG,EAAE;;;EACtB,KAAOF,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE;IAC5B,IAAM,EAAE,GAAGC,MAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAC;IACrC,IAAM,EAAE,KAAK,EAAE,EAAE,EAAA,EAAE,MAAK,EAAA;SACjB,IAAI,EAAE,KAAK,CAAC,EAAE,EAAA,KAAK,IAAIA,MAAI,CAAC,OAAO,CAAC,QAAO,EAAA;SAC3C,EAAA,OAAO,KAAK,EAAA;GAClB;CACF,CAAA;;AAEH,sBAAE,MAAM,oBAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE;EAC/C,IAAM,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,GAAG,EAAE,EAAA,OAAO,IAAI,EAAA;EACzE,OAAS,IAAI,KAAK,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE;KACnF,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;KAC5D,IAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC;CACvD,CAAA;;AAEH,sBAAE,eAAe,+BAAG;;;EAClB,KAAOI,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE;IAC9D,IAAM,EAAE,GAAGC,MAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAC;IACnC,IAAM,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAA,OAAO,KAAK,EAAA;GACxC;EACH,OAAS,IAAI;CACZ,CAAA;;AAEH,sBAAE,MAAM,oBAAC,IAAI,EAAE,CAAC,EAAE;EAChB,IAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC;CAC3B,CAAA;;AAEH,sBAAE,KAAK,qBAAG;EACR,IAAM,CAAC,IAAI,GAAE;EACb,OAAS,IAAI,CAAC,aAAa,EAAE;CAC5B,CAAA;;AAEH,YAAE,MAAa,sBAAa;;;;EAC1B,IAAM,GAAG,GAAG,KAAI;EAChB,KAAOD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAA,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAC,EAAA;EAChE,OAAS,GAAG;CACX,CAAA;;AAEH,YAAE,KAAY,mBAAC,KAAK,EAAE,OAAO,EAAE;EAC7B,OAAS,IAAI,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE;CACxC,CAAA;;;AAIH,WAAW,CAAC,UAAU,GAAGG;;ACtKzBC,IAAM,EAAE,GAAG,WAAW,CAAC,UAAS;;AAEhC,SAAS,OAAO,CAAC,EAAE,EAAE;EACnB,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,IAAIF,eAAS,CAAC,EAAE,CAAC;CACvE;;AAED,EAAE,CAAC,IAAI,GAAG,WAAW;;;EACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAG;EACpB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;IACnB,EAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAE,EAAA;;IAE7B,EAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAE,EAAA;;EAE7B,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE;IACxC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE;MAC3CD,MAAI,CAAC,YAAY,GAAGA,MAAI,CAAC,cAAa;MACtCA,MAAI,CAAC,aAAa,GAAGA,MAAI,CAAC,OAAO,CAACA,MAAI,CAAC,YAAY,CAAC,GAAG,EAAC;KACzD;IACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAC;GAC1D;EACF;;AAED,EAAE,CAAC,SAAS,GAAG,WAAW;;;EACxB,SAAS;IACP,IAAI;MACFA,MAAI,CAAC,IAAI,CAAC,IAAI,GAAE;MAChB,IAAIA,MAAI,CAAC,IAAI,CAAC,IAAI,KAAKL,cAAE,CAAC,GAAG;UACzBK,MAAI,CAAC,KAAK,CAAC,MAAM,CAACA,MAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG;UAC3CA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;QACjCA,MAAI,CAAC,IAAI,CAAC,GAAG,GAAE;QACfA,MAAI,CAAC,IAAI,CAAC,IAAI,GAAGL,cAAE,CAAC,SAAQ;OAC7B;MACD,OAAO,IAAIS,WAAK,CAACJ,MAAI,CAAC,IAAI,CAAC;KAC5B,CAAC,OAAO,CAAC,EAAE;MACV,IAAI,EAAE,CAAC,YAAY,WAAW,CAAC,EAAE,EAAA,MAAM,CAAC,EAAA;;;MAGxCD,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,GAAG,KAAI;MACrD,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC7B,GAAG,GAAGC,MAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAC;QAC7B,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;UACtB,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAEL,cAAE,CAAC,MAAM,EAAE,KAAK,EAAEK,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,EAAC;SAC7F,MAAM,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;UACpCD,IAAI,EAAE,GAAGC,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAC;UACrC,IAAI,EAAE,EAAE,GAAG,IAAI,MAAM,CAAC,EAAE,EAAC,EAAE,CAAC,OAAO,CAAC,EAAE,oDAAoD;UAC1F,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAEL,cAAE,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAC;SAC/D,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;UAC/B,OAAO,GAAG;YACR,KAAK,EAAE,CAAC,CAAC,GAAG;YACZ,GAAG,EAAE,GAAG;YACR,IAAI,EAAEA,cAAE,CAAC,QAAQ;YACjB,KAAK,EAAEK,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;YACpC;SACF,MAAM;UACL,OAAO,GAAG,MAAK;SAChB;OACF,MAAM,IAAI,6HAA6H,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClJ,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,EAAA,EAAE,IAAG,EAAA;OAC9E,MAAM,IAAI,wCAAwC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC7D,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;UAC9BD,IAAI,EAAE,GAAGC,MAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,EAAC;UACrC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAIC,eAAS,CAAC,EAAE,CAAC,EAAE,EAAA,KAAK,EAAA;SACnD;OACF,MAAM,IAAI,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC5C,GAAG,GAAE;QACL,OAAO,GAAG,MAAK;OAChB,MAAM,IAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC1C,OAAO,GAAG,KAAI;OACf,MAAM;QACL,MAAM,CAAC;OACR;MACDD,MAAI,CAAC,OAAO,CAAC,GAAG,EAAC;MACjB,IAAI,OAAO,KAAK,IAAI,EAAE,EAAA,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAEL,cAAE,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAC,EAAA;MACjF,IAAI,OAAO,EAAE;QACX,IAAIK,MAAI,CAAC,OAAO,CAAC,SAAS;UACxB,EAAA,OAAO,CAAC,GAAG,GAAG,IAAIJ,oBAAc;YAC9BI,MAAI,CAAC,IAAI;YACTK,iBAAW,CAACL,MAAI,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC;YACtCK,iBAAW,CAACL,MAAI,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,EAAC,EAAA;QACzC,OAAO,OAAO;OACf;KACF;GACF;EACF;;AAED,EAAE,CAAC,OAAO,GAAG,SAAS,GAAG,EAAE;;;EACzB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAG;EACnBD,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,EAAC;EACnC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC;IAC/D,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;IAClB,mEAAmE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,EAAE,GAAG,CAAC,EAAC;;EAE3G,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;IAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,EAAC;IACrB,IAAI,CAAC,IAAI,CAAC,SAAS,GAAGO,gBAAU,CAAC,SAAS,GAAG,EAAC;IAC9CP,IAAI,MAAK;IACT,OAAO,CAAC,KAAK,GAAGO,gBAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,GAAG,GAAG,EAAE;MACjE,EAAEN,MAAI,CAAC,IAAI,CAAC,QAAO;MACnBA,MAAI,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;KACpD;GACF;EACF;;AAED,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC,EAAE;;;EACzB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B,EAAAA,MAAI,CAAC,KAAK,CAAC,IAAI,CAACA,MAAI,CAAC,SAAS,EAAE,EAAC,EAAA;EACnC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;CACzB;;AC9GM,SAAS,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE;;ACI1DG,IAAMI,IAAE,GAAG,WAAW,CAAC,UAAS;;AAEhCA,IAAE,CAAC,aAAa,GAAG,WAAW;;;EAC5BR,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,EAAEM,iBAAW,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC;EACzF,IAAI,CAAC,IAAI,GAAG,GAAE;EACd,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKV,cAAE,CAAC,GAAG,EAAE,EAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAACK,MAAI,CAAC,cAAc,EAAE,EAAC,EAAA;EACtE,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAC;EAC3C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAG;EACpB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAU;GAC1C;EACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC;EACxC;;AAEDO,IAAE,CAAC,cAAc,GAAG,WAAW;;;EAC7BR,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,KAAI;;EAE5D,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;IACrB,SAAS,GAAGJ,cAAE,CAAC,KAAI;IACnB,IAAI,GAAG,MAAK;GACb;;EAED,QAAQ,SAAS;EACjB,KAAKA,cAAE,CAAC,MAAM,CAAC,CAAC,KAAKA,cAAE,CAAC,SAAS;IAC/B,IAAI,CAAC,IAAI,GAAE;IACXI,IAAI,OAAO,GAAG,SAAS,KAAKJ,cAAE,CAAC,OAAM;IACrC,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;MACjD,IAAI,CAAC,KAAK,GAAG,KAAI;KAClB,MAAM;MACL,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,KAAI;MACjE,IAAI,CAAC,SAAS,GAAE;KACjB;IACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,GAAG,gBAAgB,GAAG,mBAAmB,CAAC;;EAEhF,KAAKA,cAAE,CAAC,SAAS;IACf,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,SAAS,GAAE;IAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,mBAAmB,CAAC;;EAEnD,KAAKA,cAAE,CAAC,GAAG;IACT,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,GAAE;IACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE;IACjF,IAAI,CAAC,SAAS,GAAE;IAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,kBAAkB,CAAC;;EAElD,KAAKA,cAAE,CAAC,IAAI;IACV,IAAI,CAAC,IAAI,GAAE;IACXI,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAC;;IAE1F,IAAI,CAAC,MAAM,GAAE;IACb,IAAI,CAAC,MAAM,CAACJ,cAAE,CAAC,MAAM,EAAC;IACtB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,IAAI,EAAE,EAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,EAAA;IAC/DI,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAE;IAC7B,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,MAAM,EAAE;MACrEI,IAAIS,MAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAC;MAChF,IAAIA,MAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKb,cAAE,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE;QAC3F,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,GAAG,EAAE;UAC7D,IAAI,CAAC,KAAK,GAAG,QAAO;SACrB;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAEa,MAAI,CAAC;OACnC;MACD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAEA,MAAI,CAAC;KACjC;IACDT,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAC;IACrC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;MACvD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,GAAG,EAAE;QAC7D,IAAI,CAAC,KAAK,GAAG,QAAO;OACrB;MACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KACtD;IACD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;;EAElC,KAAKA,cAAE,CAAC,SAAS;IACf,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC;;EAEvC,KAAKA,cAAE,CAAC,GAAG;IACT,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,oBAAoB,GAAE;IACvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,GAAE;IACvC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,KAAI;IAClE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,aAAa,CAAC;;EAE7C,KAAKA,cAAE,CAAC,OAAO;IACb,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAA,IAAI,CAAC,QAAQ,GAAG,KAAI,EAAA;SACnE,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,GAAE,EAAE;IACjE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;;EAEjD,KAAKA,cAAE,CAAC,OAAO;IACbI,IAAI,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,aAAY;IAC1D,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,GAAE;IAC/C,IAAI,CAAC,KAAK,GAAG,GAAE;IACf,IAAI,CAAC,MAAM,GAAE;IACb,IAAI,CAAC,MAAM,CAACJ,cAAE,CAAC,MAAM,EAAC;;IAEtBI,IAAI,IAAG;IACP,OAAO,CAAC,IAAI,CAAC,MAAM,CAACJ,cAAE,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE;MACvD,IAAIK,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,KAAK,IAAIK,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,QAAQ,EAAE;QAC/DI,IAAI,MAAM,GAAGC,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAK;QACvC,IAAI,GAAG,EAAE,EAAAK,MAAI,CAAC,UAAU,CAAC,GAAG,EAAE,YAAY,EAAC,EAAA;QAC3C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAGA,MAAI,CAAC,SAAS,EAAE,EAAC;QACvC,GAAG,CAAC,UAAU,GAAG,GAAE;QACnBA,MAAI,CAAC,IAAI,GAAE;QACX,IAAI,MAAM,EAAE,EAAA,GAAG,CAAC,IAAI,GAAGA,MAAI,CAAC,eAAe,GAAE,EAAA;aACxC,EAAA,GAAG,CAAC,IAAI,GAAG,KAAI,EAAA;QACpBA,MAAI,CAAC,MAAM,CAACL,cAAE,CAAC,KAAK,EAAC;OACtB,MAAM;QACL,IAAI,CAAC,GAAG,EAAE;UACR,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAGK,MAAI,CAAC,SAAS,EAAE,EAAC;UACvC,GAAG,CAAC,UAAU,GAAG,GAAE;UACnB,GAAG,CAAC,IAAI,GAAG,KAAI;SAChB;QACD,GAAG,CAAC,UAAU,CAAC,IAAI,CAACA,MAAI,CAAC,cAAc,EAAE,EAAC;OAC3C;KACF;IACD,IAAI,GAAG,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,YAAY,EAAC,EAAA;IAC3C,IAAI,CAAC,KAAK,GAAE;IACZ,IAAI,CAAC,GAAG,CAACL,cAAE,CAAC,MAAM,EAAC;IACnB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;;EAEjD,KAAKA,cAAE,CAAC,MAAM;IACZ,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAE;IACtC,IAAI,CAAC,SAAS,GAAE;IAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC;;EAEhD,KAAKA,cAAE,CAAC,IAAI;IACV,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAE;IAC9B,IAAI,CAAC,OAAO,GAAG,KAAI;IACnB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,MAAM,EAAE;MAC/BI,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,GAAE;MAC7B,IAAI,CAAC,IAAI,GAAE;MACX,IAAI,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,MAAM,CAAC,EAAE;QACvB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,EAAC;QAC5D,IAAI,CAAC,MAAM,CAACA,cAAE,CAAC,MAAM,EAAC;OACvB,MAAM;QACL,MAAM,CAAC,KAAK,GAAG,KAAI;OACpB;MACD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,GAAE;MAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,EAAC;KACtD;IACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,KAAI;IACjE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAA,OAAO,IAAI,CAAC,KAAK,EAAA;IACvD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC;;EAE9C,KAAKA,cAAE,CAAC,IAAI,CAAC;EACb,KAAKA,cAAE,CAAC,MAAM;IACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;;EAE3D,KAAKA,cAAE,CAAC,MAAM;IACZ,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,oBAAoB,GAAE;IACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,GAAE;IACjC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC;;EAEhD,KAAKA,cAAE,CAAC,KAAK;IACX,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,GAAE;IACzC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,GAAE;IACjC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC;;EAE/C,KAAKA,cAAE,CAAC,MAAM;IACZ,OAAO,IAAI,CAAC,UAAU,EAAE;;EAE1B,KAAKA,cAAE,CAAC,IAAI;IACV,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC;;EAEhD,KAAKA,cAAE,CAAC,MAAM;IACZ,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;;EAE9B,KAAKA,cAAE,CAAC,OAAO;IACb,OAAO,IAAI,CAAC,WAAW,EAAE;;EAE3B,KAAKA,cAAE,CAAC,OAAO;IACb,OAAO,IAAI,CAAC,WAAW,EAAE;;EAE3B;IACE,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;MAC/B,IAAI,CAAC,IAAI,GAAE;MACX,IAAI,CAAC,IAAI,GAAE;MACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;KAC5C;IACDI,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,GAAE;IACjC,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;MACjB,IAAI,CAAC,IAAI,GAAE;MACX,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,GAAG,EAAE,EAAA,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAA;MAC5E,OAAO,IAAI,CAAC,cAAc,EAAE;KAC7B,MAAM,IAAI,SAAS,KAAKA,cAAE,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,KAAK,CAAC,EAAE;MACpF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,GAAE;MACjC,IAAI,CAAC,KAAK,GAAG,KAAI;MACjB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,kBAAkB,CAAC;KACjD,MAAM;MACL,IAAI,CAAC,UAAU,GAAG,KAAI;MACtB,IAAI,CAAC,SAAS,GAAE;MAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,qBAAqB,CAAC;KACpD;GACF;EACF;;AAEDY,IAAE,CAAC,UAAU,GAAG,WAAW;;;EACzBR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,MAAM,GAAE;EACb,IAAI,CAAC,MAAM,CAACJ,cAAE,CAAC,MAAM,EAAC;EACtBI,IAAI,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,aAAY;EAC1D,IAAI,CAAC,IAAI,GAAG,GAAE;EACd,OAAO,CAAC,IAAI,CAAC,MAAM,CAACJ,cAAE,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC;IACrD,EAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAACK,MAAI,CAAC,cAAc,EAAE,EAAC,EAAA;EACvC,IAAI,CAAC,KAAK,GAAE;EACZ,IAAI,CAAC,GAAG,CAACL,cAAE,CAAC,MAAM,EAAC;EACnB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC;EAC/C;;AAEDY,IAAE,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;EACjC,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,KAAI;EAC9B,IAAI,IAAI,CAAC,GAAG,CAACZ,cAAE,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,IAAI,EAAE,EAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,GAAE,EAAA;EACtF,IAAI,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,MAAM,EAAE,EAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,GAAE,EAAA;EAC1F,IAAI,CAAC,KAAK,GAAE;EACZ,IAAI,CAAC,MAAM,CAACA,cAAE,CAAC,MAAM,EAAC;EACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,GAAE;EACjC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC;EAC7C;;AAEDY,IAAE,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;EACnCR,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,GAAG,GAAG,gBAAgB,GAAG,iBAAgB;EACzE,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,GAAE;EACnC,IAAI,CAAC,KAAK,GAAE;EACZ,IAAI,CAAC,MAAM,CAACA,cAAE,CAAC,MAAM,EAAC;EACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,GAAE;EACjC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;EACnC;;AAEDY,IAAE,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;;;EACvC,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,YAAY,GAAG,GAAE;EACtB,GAAG;IACDR,IAAI,IAAI,GAAGC,MAAI,CAAC,SAAS,GAAE;IAC3B,IAAI,CAAC,EAAE,GAAGA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,GAAGA,MAAI,CAAC,YAAY,CAACA,MAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,GAAGA,MAAI,CAAC,UAAU,GAAE;IAC3G,IAAI,CAAC,IAAI,GAAGA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,EAAE,CAAC,GAAGK,MAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,KAAI;IAChE,IAAI,CAAC,YAAY,CAAC,IAAI,CAACA,MAAI,CAAC,UAAU,CAAC,IAAI,EAAE,oBAAoB,CAAC,EAAC;GACpE,QAAQ,IAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,CAAC,CAAC;EAC5B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;IAC7BI,IAAIU,MAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IAC3BA,MAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE;IAC3B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAACA,MAAI,EAAE,oBAAoB,CAAC,EAAC;GACpE;EACD,IAAI,CAAC,IAAI,EAAE,EAAA,IAAI,CAAC,SAAS,GAAE,EAAA;EAC3B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,qBAAqB,CAAC;EACpD;;AAEDF,IAAE,CAAC,UAAU,GAAG,SAAS,WAAW,EAAE;;;EACpCR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,IAAI,EAAE,EAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE,EAAA;OACrD,IAAI,WAAW,KAAK,IAAI,EAAE,EAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE,EAAA;OACrD,EAAA,IAAI,CAAC,EAAE,GAAG,KAAI,EAAA;EACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,KAAI;EACvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,GAAE;EACnB,IAAI,CAAC,MAAM,GAAE;EACbI,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,aAAY;EACzD,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,MAAM,EAAC;EACnB,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,aAAY,EAAE;EACtF,OAAO,CAAC,IAAI,CAAC,MAAM,CAACA,cAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;IAC5C,IAAIK,MAAI,CAAC,SAAS,EAAE,EAAE,EAAA,QAAQ,EAAA;IAC9BD,IAAI,MAAM,GAAGC,MAAI,CAAC,SAAS,EAAE,EAAE,WAAW,WAAA,EAAE,OAAO,YAAA;IACnD,IAAIA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;MACjC,MAAM,CAAC,MAAM,GAAG,MAAK;MACrB,WAAW,GAAGA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,IAAI,EAAC;KAChC;IACDK,MAAI,CAAC,iBAAiB,CAAC,MAAM,EAAC;IAC9B,IAAI,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,OAAO,CAACA,MAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAAA,MAAI,CAAC,IAAI,EAAE,CAAC,EAAA,CAACA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC5G,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ;SACnFK,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAM,IAAIK,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAM,CAAC,EAAE;MAChE,MAAM,CAAC,MAAM,GAAG,KAAI;MACpB,WAAW,GAAGK,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,IAAI,EAAC;MAC/BK,MAAI,CAAC,iBAAiB,CAAC,MAAM,EAAC;KAC/B,MAAM;MACL,MAAM,CAAC,MAAM,GAAG,MAAK;KACtB;IACD,IAAI,CAAC,MAAM,CAAC,QAAQ;QAChB,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO,IAAIA,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAM;QAC9F,CAACK,MAAI,CAAC,kBAAkB,EAAE,EAAE;MAC9B,OAAO,GAAG,KAAI;MACd,WAAW,GAAGA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAIA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,IAAI,EAAC;MAChEK,MAAI,CAAC,iBAAiB,CAAC,MAAM,EAAC;KAC/B,MAAM;MACL,OAAO,GAAG,MAAK;KAChB;IACD,IAAIA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY;QACjE,CAAC,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC;QAC5EA,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAM,IAAIK,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAM,EAAE;MAC9D,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,KAAI;MAC7BK,MAAI,CAAC,iBAAiB,CAAC,MAAM,EAAC;MAC9B,MAAM,CAAC,KAAK,GAAGA,MAAI,CAAC,WAAW,CAAC,KAAK,EAAC;KACvC,MAAM;MACL,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,WAAW,IAAI,CAAC,OAAO;QAChE,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,aAAa;UACnE,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,aAAa,CAAC,EAAE;QACxE,MAAM,CAAC,IAAI,GAAG,cAAa;OAC5B,MAAM;QACL,MAAM,CAAC,IAAI,GAAG,SAAQ;OACvB;MACD,MAAM,CAAC,KAAK,GAAGA,MAAI,CAAC,WAAW,CAAC,WAAW,EAAE,OAAO,EAAC;KACtD;IACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAACA,MAAI,CAAC,UAAU,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAAC;GACjE;EACD,IAAI,CAAC,KAAK,GAAE;EACZ,IAAI,CAAC,IAAI,CAAC,GAAG,CAACL,cAAE,CAAC,MAAM,CAAC,EAAE;;;IAGxB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;IAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAK,EAAA;GACnE;EACD,IAAI,CAAC,SAAS,GAAE;EAChB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAC;EACvC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,GAAG,kBAAkB,GAAG,iBAAiB,CAAC;EACnF;;AAEDY,IAAE,CAAC,aAAa,GAAG,SAAS,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE;EACtDR,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,CAAC,WAAU;EAC9D,IAAI,CAAC,YAAY,CAAC,IAAI,EAAC;EACvB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,IAAI,EAAC;GACnC;EACD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,QAAO;GACvB;EACD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,IAAI,EAAE,EAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE,EAAA;OACrD,IAAI,WAAW,KAAK,IAAI,EAAE,EAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE,EAAA;EAC1D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAK;EACzB,IAAI,CAAC,UAAU,GAAG,KAAI;EACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,GAAE;EACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,GAAE;EAC7B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;EAChD,IAAI,CAAC,OAAO,GAAG,WAAU;EACzB,IAAI,CAAC,UAAU,GAAG,cAAa;EAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,GAAG,qBAAqB,GAAG,oBAAoB,CAAC;EACzF;;AAEDY,IAAE,CAAC,WAAW,GAAG,WAAW;EAC1BR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,IAAI,CAAC,EAAE;IACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,WAAW,GAAE;IACpF,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,sBAAsB,CAAC;GACrD;EACD,IAAI,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,QAAQ,CAAC,EAAE;;IAEzBI,IAAI,QAAO;IACX,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,SAAS,KAAK,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;MAC7EI,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,GAAE;MAC5B,IAAI,CAAC,IAAI,GAAE;MACX,IAAI,OAAO,EAAE,EAAA,IAAI,CAAC,IAAI,GAAE,EAAA;MACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,EAAC;KACpE,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,MAAM,EAAE;MACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAC;KACjD,MAAM;MACL,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,GAAE;MAC1C,IAAI,CAAC,SAAS,GAAE;KACjB;IACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,0BAA0B,CAAC;GACzD;EACD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;IAC7E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,GAAE;IACxC,IAAI,CAAC,UAAU,GAAG,GAAE;IACpB,IAAI,CAAC,MAAM,GAAG,KAAI;GACnB,MAAM;IACL,IAAI,CAAC,WAAW,GAAG,KAAI;IACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,wBAAwB,GAAE;IACjD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,KAAI;IACtE,IAAI,CAAC,SAAS,GAAE;GACjB;EACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,wBAAwB,CAAC;EACvD;;AAEDY,IAAE,CAAC,WAAW,GAAG,WAAW;EAC1BR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,MAAM,EAAE;IAC/B,IAAI,CAAC,UAAU,GAAG,GAAE;IACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,GAAE;GACnC,MAAM;IACLI,IAAI,IAAG;IACP,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,MAAM,EAAE;MAC1D,GAAG,GAAG,IAAI,CAAC,SAAS,GAAE;MACtB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAE;MAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,wBAAwB,EAAC;MAC9C,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,KAAK,EAAC;KACnB;IACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,GAAE;IAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,WAAW,GAAE;IACnH,IAAI,GAAG,EAAE,EAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,EAAC,EAAA;GACtC;EACD,IAAI,CAAC,SAAS,GAAE;EAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,mBAAmB,CAAC;EAClD;;AAEDY,IAAE,CAAC,qBAAqB,GAAG,WAAW;;;EACpCR,IAAI,IAAI,GAAG,GAAE;EACb,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,IAAI,EAAE;IAC7BI,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,GAAE;IAC1B,IAAI,CAAC,IAAI,GAAE;IACX,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE;IAC5E,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,0BAA0B,CAAC,EAAC;GAC5D,MAAM;IACLA,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,aAAa,GAAG,IAAI,CAAC,cAAa;IACzF,IAAI,CAAC,MAAM,GAAE;IACb,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,MAAM,EAAC;IACnB,IAAI,IAAI,CAAC,YAAY,GAAG,aAAa,EAAE,EAAA,aAAa,GAAG,IAAI,CAAC,aAAY,EAAA;IACxE,OAAO,CAAC,IAAI,CAAC,MAAM,CAACA,cAAE,CAAC,MAAM,EAAE,MAAM,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;MAC3FI,IAAIW,KAAG,GAAGV,MAAI,CAAC,SAAS,GAAE;MAC1B,IAAIA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,IAAI,CAAC,EAAE;QACrBe,KAAG,CAAC,KAAK,GAAGV,MAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAGA,MAAI,CAAC,UAAU,EAAE,GAAGA,MAAI,CAAC,UAAU,GAAE;QAC5EA,MAAI,CAAC,UAAU,CAACU,KAAG,EAAE,0BAA0B,EAAC;OACjD,MAAM;QACL,IAAIV,MAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,EAAA,KAAK,EAAA;QACpCU,KAAG,CAAC,QAAQ,GAAGV,MAAI,CAAC,UAAU,GAAE;QAChC,IAAI,OAAO,CAACU,KAAG,CAAC,QAAQ,CAAC,EAAE,EAAA,KAAK,EAAA;QAChCA,KAAG,CAAC,KAAK,GAAGV,MAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAGA,MAAI,CAAC,UAAU,EAAE,GAAGU,KAAG,CAAC,SAAQ;QACvEV,MAAI,CAAC,UAAU,CAACU,KAAG,EAAE,iBAAiB,EAAC;OACxC;MACD,IAAI,CAAC,IAAI,CAACA,KAAG,EAAC;MACdV,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,EAAC;KACnB;IACD,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,MAAM,EAAC;IACnB,IAAI,CAAC,KAAK,GAAE;GACb;EACD,OAAO,IAAI;EACZ;;AAEDY,IAAE,CAAC,wBAAwB,GAAG,WAAW;;;EACvCR,IAAI,IAAI,GAAG,GAAE;EACbA,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,aAAa,GAAG,IAAI,CAAC,cAAa;EACzF,IAAI,CAAC,MAAM,GAAE;EACb,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,MAAM,EAAC;EACnB,IAAI,IAAI,CAAC,YAAY,GAAG,aAAa,EAAE,EAAA,aAAa,GAAG,IAAI,CAAC,aAAY,EAAA;EACxE,OAAO,CAAC,IAAI,CAAC,MAAM,CAACA,cAAE,CAAC,MAAM,EAAE,MAAM,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;IAC3F,IAAIK,MAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,EAAA,KAAK,EAAA;IACpCD,IAAI,GAAG,GAAGC,MAAI,CAAC,SAAS,GAAE;IAC1B,GAAG,CAAC,KAAK,GAAGA,MAAI,CAAC,UAAU,GAAE;IAC7B,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAA,KAAK,EAAA;IAC7B,GAAG,CAAC,QAAQ,GAAGA,MAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAGA,MAAI,CAAC,UAAU,EAAE,GAAG,GAAG,CAAC,MAAK;IACvEA,MAAI,CAAC,UAAU,CAAC,GAAG,EAAE,iBAAiB,EAAC;IACvC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAC;IACdA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,EAAC;GACnB;EACD,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,MAAM,EAAC;EACnB,IAAI,CAAC,KAAK,GAAE;EACZ,OAAO,IAAI;CACZ;;AC1cDQ,IAAMI,IAAE,GAAG,WAAW,CAAC,UAAS;;AAEhCA,IAAE,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE;EAC5B,IAAI,CAAC,IAAI,EAAE,EAAA,OAAO,IAAI,EAAA;EACtB,QAAQ,IAAI,CAAC,IAAI;EACjB,KAAK,YAAY,CAAC;EAClB,KAAK,kBAAkB;IACrB,OAAO,IAAI;;EAEb,KAAK,yBAAyB;IAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAC;IACjD,OAAO,IAAI;;EAEb;IACE,OAAO,IAAI,CAAC,UAAU,EAAE;GACzB;EACF;;AAEDA,IAAE,CAAC,eAAe,GAAG,SAAS,IAAI,EAAE;;;EAClCR,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,GAAE;EAClCA,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAC;EACtC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,KAAK,EAAE;IAC9BI,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAC;IAClC,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,EAAC;IACzB,OAAO,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,KAAK,CAAC,EAAE,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAACK,MAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAC,EAAA;IAC7E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,oBAAoB,CAAC;GACnD;EACD,OAAO,IAAI;EACZ;;AAEDO,IAAE,CAAC,oBAAoB,GAAG,WAAW;EACnC,IAAI,CAAC,MAAM,GAAE;EACb,IAAI,CAAC,MAAM,CAACZ,cAAE,CAAC,MAAM,EAAC;EACtBI,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,GAAE;EAChC,IAAI,CAAC,KAAK,GAAE;EACZ,IAAI,CAAC,MAAM,CAACJ,cAAE,CAAC,MAAM,EAAC;EACtB,OAAO,GAAG;EACX;;AAEDY,IAAE,CAAC,gBAAgB,GAAG,SAAS,IAAI,EAAE;EACnC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;IACnCR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IAC3B,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;MAC7G,IAAI,CAAC,QAAQ,GAAG,MAAK;MACrB,IAAI,CAAC,QAAQ,GAAG,KAAI;KACrB,MAAM;MACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,IAAI,EAAC;MACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,GAAE;KACxC;IACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;GAChD;;EAEDI,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,GAAE;EAClCA,IAAI,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAC;EAC3C,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE;IAC1BA,IAAIY,MAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAC;IAClCA,MAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;IAC9BA,MAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKhB,cAAE,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAC;IACpF,IAAI,CAAC,IAAI,GAAE;IACXgB,MAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAC;IACxC,OAAO,IAAI,CAAC,UAAU,CAACA,MAAI,EAAE,sBAAsB,CAAC;GACrD;EACD,OAAO,IAAI;EACZ;;AAEDJ,IAAE,CAAC,qBAAqB,GAAG,SAAS,IAAI,EAAE;EACxCR,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,GAAE;EAClCA,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAC;EAClC,IAAI,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,QAAQ,CAAC,EAAE;IACzBI,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAC;IAClC,IAAI,CAAC,IAAI,GAAG,KAAI;IAChB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,GAAE;IACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAACJ,cAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,GAAE;IACxF,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,uBAAuB,CAAC;GACtD;EACD,OAAO,IAAI;EACZ;;AAEDY,IAAE,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE;EAC/BR,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,GAAE;EAClCA,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,aAAY;EACrD,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;EACpF;;AAEDQ,IAAE,CAAC,WAAW,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;EAClE,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,EAAA,OAAO,IAAI,EAAA;EAChGR,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAK;EAC9B,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,GAAG,CAAC,EAAE;IACvD,IAAI,IAAI,GAAG,OAAO,EAAE;MAClBI,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAC;MAClC,IAAI,CAAC,IAAI,GAAG,KAAI;MAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;MAC9B,IAAI,CAAC,IAAI,GAAE;MACX,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;QACnF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,GAAE;OAC/B,MAAM;QACLA,IAAI,UAAU,GAAG,IAAI,CAAC,eAAe,GAAE;QACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAC;OACjG;MACD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,mBAAmB,GAAG,kBAAkB,EAAC;MAC/F,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;KAClE;GACF;EACD,OAAO,IAAI;EACZ;;AAEDQ,IAAE,CAAC,eAAe,GAAG,SAAS,QAAQ,EAAE;;;EACtCR,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,EAAE,KAAI;EACxC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;KACjE,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;IAC9E;IACA,IAAI,GAAG,IAAI,CAAC,UAAU,GAAE;IACxB,QAAQ,GAAG,KAAI;GAChB,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE;IAC/BA,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,OAAM;IACjE,IAAI,CAAC,MAAM,EAAE,EAAA,QAAQ,GAAG,KAAI,EAAA;IAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;IAC9B,IAAI,CAAC,MAAM,GAAG,KAAI;IAClB,IAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAC;IAC1C,IAAI,MAAM,EAAE,EAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAC,EAAA;IACzD,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,kBAAkB,GAAG,iBAAiB,EAAC;GAC9E,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,QAAQ,EAAE;IACxCI,IAAIY,MAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IAC3B,IAAI,CAAC,IAAI,GAAE;IACXA,MAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAC;IAC9C,IAAI,GAAG,IAAI,CAAC,UAAU,CAACA,MAAI,EAAE,eAAe,EAAC;GAC9C,MAAM;IACL,IAAI,GAAG,IAAI,CAAC,mBAAmB,GAAE;IACjC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;MAC1DZ,IAAIY,MAAI,GAAGX,MAAI,CAAC,WAAW,CAAC,KAAK,EAAC;MAClCW,MAAI,CAAC,QAAQ,GAAGX,MAAI,CAAC,GAAG,CAAC,MAAK;MAC9BW,MAAI,CAAC,MAAM,GAAG,MAAK;MACnBA,MAAI,CAAC,QAAQ,GAAGX,MAAI,CAAC,SAAS,CAAC,IAAI,EAAC;MACpCA,MAAI,CAAC,IAAI,GAAE;MACX,IAAI,GAAGA,MAAI,CAAC,UAAU,CAACW,MAAI,EAAE,kBAAkB,EAAC;KACjD;GACF;;EAED,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAChB,cAAE,CAAC,QAAQ,CAAC,EAAE;IACtCI,IAAIY,MAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAC;IAClCA,MAAI,CAAC,QAAQ,GAAG,KAAI;IACpBA,MAAI,CAAC,IAAI,GAAG,KAAI;IAChBA,MAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAC;IACxC,OAAO,IAAI,CAAC,UAAU,CAACA,MAAI,EAAE,kBAAkB,CAAC;GACjD;;EAED,OAAO,IAAI;EACZ;;AAEDJ,IAAE,CAAC,mBAAmB,GAAG,WAAW;EAClCR,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,GAAE;EAClC,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC;EACnG;;AAEDQ,IAAE,CAAC,eAAe,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE;;;EACrE,SAAS;IACP,IAAIP,MAAI,CAAC,YAAY,KAAK,IAAI,IAAIA,MAAI,CAAC,SAAS,IAAI,WAAW,IAAIA,MAAI,CAAC,eAAe,EAAE,EAAE;MACzF,IAAIA,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,GAAG,IAAIK,MAAI,CAAC,SAAS,KAAK,WAAW;QAC5D,EAAA,EAAE,YAAW,EAAA;;QAEb,EAAA,OAAO,IAAI,EAAA;KACd;;IAEDD,IAAI,eAAe,GAAG,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,CAACC,MAAI,CAAC,kBAAkB,GAAE;;IAEvG,IAAIA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,GAAG,CAAC,EAAE;MACpBI,IAAI,IAAI,GAAGC,MAAI,CAAC,WAAW,CAAC,KAAK,EAAC;MAClC,IAAI,CAAC,MAAM,GAAG,KAAI;MAClB,IAAIA,MAAI,CAAC,YAAY,KAAK,IAAI,IAAIA,MAAI,CAAC,SAAS,IAAI,WAAW,IAAIA,MAAI,CAAC,eAAe,EAAE;QACvF,EAAA,IAAI,CAAC,QAAQ,GAAGA,MAAI,CAAC,UAAU,GAAE,EAAA;;QAEjC,EAAA,IAAI,CAAC,QAAQ,GAAGA,MAAI,CAAC,qBAAqB,EAAE,IAAIA,MAAI,CAAC,UAAU,GAAE,EAAA;MACnE,IAAI,CAAC,QAAQ,GAAG,MAAK;MACrB,IAAI,GAAGA,MAAI,CAAC,UAAU,CAAC,IAAI,EAAE,kBAAkB,EAAC;KACjD,MAAM,IAAIA,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,QAAQ,EAAE;MACxCK,MAAI,CAAC,MAAM,GAAE;MACbA,MAAI,CAAC,IAAI,GAAE;MACXD,IAAIY,MAAI,GAAGX,MAAI,CAAC,WAAW,CAAC,KAAK,EAAC;MAClCW,MAAI,CAAC,MAAM,GAAG,KAAI;MAClBA,MAAI,CAAC,QAAQ,GAAGX,MAAI,CAAC,eAAe,GAAE;MACtCW,MAAI,CAAC,QAAQ,GAAG,KAAI;MACpBX,MAAI,CAAC,KAAK,GAAE;MACZA,MAAI,CAAC,MAAM,CAACL,cAAE,CAAC,QAAQ,EAAC;MACxB,IAAI,GAAGK,MAAI,CAAC,UAAU,CAACW,MAAI,EAAE,kBAAkB,EAAC;KACjD,MAAM,IAAI,CAAC,OAAO,IAAIX,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAM,EAAE;MAClDI,IAAI,QAAQ,GAAGC,MAAI,CAAC,aAAa,CAACL,cAAE,CAAC,MAAM,EAAC;MAC5C,IAAI,eAAe,IAAIK,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,CAAC;QACvC,EAAA,OAAOK,MAAI,CAAC,oBAAoB,CAACA,MAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAA;MAC3ED,IAAIY,MAAI,GAAGX,MAAI,CAAC,WAAW,CAAC,KAAK,EAAC;MAClCW,MAAI,CAAC,MAAM,GAAG,KAAI;MAClBA,MAAI,CAAC,SAAS,GAAG,SAAQ;MACzB,IAAI,GAAGX,MAAI,CAAC,UAAU,CAACW,MAAI,EAAE,gBAAgB,EAAC;KAC/C,MAAM,IAAIX,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,SAAS,EAAE;MACzCI,IAAIY,MAAI,GAAGX,MAAI,CAAC,WAAW,CAAC,KAAK,EAAC;MAClCW,MAAI,CAAC,GAAG,GAAG,KAAI;MACfA,MAAI,CAAC,KAAK,GAAGX,MAAI,CAAC,aAAa,GAAE;MACjC,IAAI,GAAGA,MAAI,CAAC,UAAU,CAACW,MAAI,EAAE,0BAA0B,EAAC;KACzD,MAAM;MACL,OAAO,IAAI;KACZ;GACF;EACF;;AAEDJ,IAAE,CAAC,aAAa,GAAG,WAAW;EAC5BR,IAAI,KAAI;EACR,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI;EACrB,KAAKJ,cAAE,CAAC,KAAK,CAAC;EACd,KAAKA,cAAE,CAAC,MAAM;IACZI,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,KAAK,GAAG,gBAAgB,GAAG,QAAO;IAClE,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;;EAEpC,KAAKA,cAAE,CAAC,IAAI;IACVI,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,GAAE;IAClCA,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE;IAC1BA,IAAI,OAAO,GAAG,MAAK;IACnB,IAAI,EAAE,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;MACrD,IAAI,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,SAAS,CAAC;QACxB,EAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,EAAA;MACjE,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,IAAI,EAAE;QAC7B,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE;QACtB,OAAO,GAAG,KAAI;OACf;KACF;IACD,OAAO,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE;;EAEpG,KAAKA,cAAE,CAAC,MAAM;IACZ,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvBI,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;IACxB,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAC;IACrD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,MAAK;IACtB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAC;IACzD,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC;;EAEzC,KAAKJ,cAAE,CAAC,GAAG,CAAC,CAAC,KAAKA,cAAE,CAAC,MAAM;IACzB,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;IAC3B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAC;IACzD,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC;;EAEzC,KAAKA,cAAE,CAAC,KAAK,CAAC,CAAC,KAAKA,cAAE,CAAC,KAAK,CAAC,CAAC,KAAKA,cAAE,CAAC,MAAM;IAC1C,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,MAAK;IAC3E,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAO;IAChC,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC;;EAEzC,KAAKA,cAAE,CAAC,MAAM;IACZI,IAAI,UAAU,GAAG,IAAI,CAAC,eAAe,GAAE;IACvC,IAAI,CAAC,IAAI,GAAE;IACXA,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,GAAE;IAClC,IAAI,CAAC,MAAM,CAACJ,cAAE,CAAC,MAAM,EAAC;IACtB,IAAI,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,KAAK,CAAC,EAAE;;MAEtBI,IAAI,MAAM,GAAG,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,EAAC;MACzC,IAAI,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrD,EAAA,MAAM,CAAC,GAAG,GAAE,EAAA;MACd,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,MAAM,CAAC;KACvE;IACD,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;MAC/BA,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAC;MACtC,GAAG,CAAC,UAAU,GAAG,MAAK;MACtB,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,yBAAyB,EAAC;KACxD;IACD,OAAO,KAAK;;EAEd,KAAKJ,cAAE,CAAC,QAAQ;IACd,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAACA,cAAE,CAAC,QAAQ,EAAE,IAAI,EAAC;IACrD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;;EAEjD,KAAKA,cAAE,CAAC,MAAM;IACZ,OAAO,IAAI,CAAC,QAAQ,EAAE;;EAExB,KAAKA,cAAE,CAAC,MAAM;IACZ,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;EAE/B,KAAKA,cAAE,CAAC,SAAS;IACf,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;IACvB,IAAI,CAAC,IAAI,GAAE;IACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC;;EAExC,KAAKA,cAAE,CAAC,IAAI;IACV,OAAO,IAAI,CAAC,QAAQ,EAAE;;EAExB,KAAKA,cAAE,CAAC,SAAS;IACf,OAAO,IAAI,CAAC,aAAa,EAAE;;EAE7B;IACE,OAAO,IAAI,CAAC,UAAU,EAAE;GACzB;EACF;;AAEDY,IAAE,CAAC,QAAQ,GAAG,WAAW;EACvBR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,aAAY;EACnFA,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAC;EAChC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,GAAG,CAAC,EAAE;IACrD,IAAI,CAAC,IAAI,GAAG,KAAI;IAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAC;IACrC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC;GAC7C;EACDI,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,GAAE;EAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAC;EACxF,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,MAAM,EAAE;IAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAACA,cAAE,CAAC,MAAM,EAAC;GAC/C,MAAM;IACL,IAAI,CAAC,SAAS,GAAG,GAAE;GACpB;EACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC;EAC9C;;AAEDY,IAAE,CAAC,oBAAoB,GAAG,WAAW;EACnCR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;;;EAG3B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,eAAe,EAAE;IACxC,IAAI,CAAC,KAAK,GAAG;MACX,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK;MACnB,MAAM,EAAE,IAAI;MACb;GACF,MAAM;IACL,IAAI,CAAC,KAAK,GAAG;MACX,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;MAC3E,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK;MACvB;GACF;EACD,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,UAAS;EAC1C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAChD;;AAEDY,IAAE,CAAC,aAAa,GAAG,WAAW;;;EAC5BR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,WAAW,GAAG,GAAE;EACrBA,IAAI,MAAM,GAAG,IAAI,CAAC,oBAAoB,GAAE;EACxC,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,EAAC;EACtB,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;IACnBC,MAAI,CAAC,IAAI,GAAE;IACX,IAAI,CAAC,WAAW,CAAC,IAAI,CAACA,MAAI,CAAC,eAAe,EAAE,EAAC;IAC7C,IAAIA,MAAI,CAAC,MAAM,CAACL,cAAE,CAAC,MAAM,CAAC,EAAE;MAC1B,MAAM,GAAGK,MAAI,CAAC,oBAAoB,GAAE;KACrC,MAAM;MACL,MAAM,GAAGA,MAAI,CAAC,SAAS,GAAE;MACzB,MAAM,CAAC,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAC;MACpC,MAAM,CAAC,IAAI,GAAG,KAAI;MAClBA,MAAI,CAAC,UAAU,CAAC,MAAM,EAAE,iBAAiB,EAAC;KAC3C;IACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAC;GACzB;EACD,IAAI,CAAC,MAAM,CAACL,cAAE,CAAC,SAAS,EAAC;EACzB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;EAChD;;AAEDY,IAAE,CAAC,QAAQ,GAAG,WAAW;;;EACvBR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,UAAU,GAAG,GAAE;EACpB,IAAI,CAAC,MAAM,GAAE;EACbA,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,aAAY;EACzD,IAAI,CAAC,GAAG,CAACJ,cAAE,CAAC,MAAM,EAAC;EACnB,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,MAAM,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,aAAY,EAAE;EACtF,OAAO,CAAC,IAAI,CAAC,MAAM,CAACA,cAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;IAC5CI,IAAI,IAAI,GAAGC,MAAI,CAAC,SAAS,EAAE,EAAE,WAAW,WAAA,EAAE,OAAO,WAAA,EAAE,KAAK,YAAA;IACxD,IAAIA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAIA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,QAAQ,CAAC,EAAE;MAC1D,IAAI,CAAC,QAAQ,GAAGK,MAAI,CAAC,gBAAgB,GAAE;MACvC,IAAI,CAAC,UAAU,CAAC,IAAI,CAACA,MAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,EAAC;MAC5DA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,EAAC;MAClB,QAAQ;KACT;IACD,IAAIK,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;MACjC,KAAK,GAAGA,MAAI,CAAC,eAAe,GAAE;MAC9B,IAAI,CAAC,MAAM,GAAG,MAAK;MACnB,IAAI,CAAC,SAAS,GAAG,MAAK;MACtB,WAAW,GAAGA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,IAAI,EAAC;KAChC;IACDK,MAAI,CAAC,iBAAiB,CAAC,IAAI,EAAC;IAC5B,IAAIA,MAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;MAC/B,OAAO,GAAG,KAAI;MACd,WAAW,GAAGA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAIA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,IAAI,EAAC;MAChEK,MAAI,CAAC,iBAAiB,CAAC,IAAI,EAAC;KAC7B,MAAM;MACL,OAAO,GAAG,MAAK;KAChB;IACD,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,OAAO,CAACA,MAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAAA,MAAI,CAAC,IAAI,EAAE,CAAC,EAAA,CAACA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC1G,IAAIK,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,CAAC,EAAE;MACtB,IAAI,CAAC,IAAI,GAAG,OAAM;MAClB,IAAI,CAAC,KAAK,GAAGK,MAAI,CAAC,gBAAgB,GAAE;KACrC,MAAM,IAAIA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,KAAKA,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAM,IAAIK,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAM,CAAC,EAAE;MACxG,IAAI,CAAC,IAAI,GAAG,OAAM;MAClB,IAAI,CAAC,MAAM,GAAG,KAAI;MAClB,IAAI,CAAC,KAAK,GAAGK,MAAI,CAAC,WAAW,CAAC,WAAW,EAAE,OAAO,EAAC;KACpD,MAAM,IAAIA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY;eAC/D,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC;gBACrEA,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,KAAK,IAAIK,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,MAAM,IAAIK,MAAI,CAAC,GAAG,CAAC,IAAI,KAAKL,cAAE,CAAC,EAAE,CAAC,EAAE;MACjG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAI;MACzBK,MAAI,CAAC,iBAAiB,CAAC,IAAI,EAAC;MAC5B,IAAI,CAAC,KAAK,GAAGA,MAAI,CAAC,WAAW,CAAC,KAAK,EAAC;KACrC,MAAM;MACL,IAAI,CAAC,IAAI,GAAG,OAAM;MAClB,IAAIA,MAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;QACjC,IAAIA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,EAAE,CAAC,EAAE;UACnBI,IAAI,MAAM,GAAGC,MAAI,CAAC,WAAW,CAAC,KAAK,EAAC;UACpC,MAAM,CAAC,QAAQ,GAAG,IAAG;UACrB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAG;UACtB,MAAM,CAAC,KAAK,GAAGA,MAAI,CAAC,gBAAgB,GAAE;UACtC,IAAI,CAAC,KAAK,GAAGA,MAAI,CAAC,UAAU,CAAC,MAAM,EAAE,sBAAsB,EAAC;SAC7D,MAAM;UACL,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAG;SACtB;OACF,MAAM;QACL,IAAI,CAAC,KAAK,GAAGA,MAAI,CAAC,UAAU,GAAE;OAC/B;MACD,IAAI,CAAC,SAAS,GAAG,KAAI;KACtB;IACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAACA,MAAI,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,EAAC;IACvDA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,EAAC;GACnB;EACD,IAAI,CAAC,KAAK,GAAE;EACZ,IAAI,CAAC,IAAI,CAAC,GAAG,CAACA,cAAE,CAAC,MAAM,CAAC,EAAE;;;IAGxB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;IAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAK,EAAA;GACnE;EACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,kBAAkB,CAAC;EACjD;;AAEDY,IAAE,CAAC,iBAAiB,GAAG,SAAS,IAAI,EAAE;EACpC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,IAAI,CAAC,GAAG,CAACZ,cAAE,CAAC,QAAQ,CAAC,EAAE;MACzB,IAAI,CAAC,QAAQ,GAAG,KAAI;MACpB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,GAAE;MACjC,IAAI,CAAC,MAAM,CAACA,cAAE,CAAC,QAAQ,EAAC;MACxB,MAAM;KACP,MAAM;MACL,IAAI,CAAC,QAAQ,GAAG,MAAK;KACtB;GACF;EACDI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKA,cAAE,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,UAAU,GAAE;EAC9G,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,UAAU,GAAE;EACpC;;AAEDY,IAAE,CAAC,qBAAqB,GAAG,WAAW;EACpC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKZ,cAAE,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,EAAA,OAAO,IAAI,CAAC,UAAU,EAAE,EAAA;EACjF;;AAEDY,IAAE,CAAC,UAAU,GAAG,WAAW;EACzBR,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAO;EAC7E,IAAI,CAAC,IAAI,EAAE,EAAA,OAAO,IAAI,CAAC,UAAU,EAAE,EAAA;EACnCI,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,IAAI,GAAG,KAAI;EAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,YAAY,CAAC;EAC3C;;AAEDQ,IAAE,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE;EAC/B,IAAI,CAAC,EAAE,GAAG,KAAI;EACd,IAAI,CAAC,MAAM,GAAG,GAAE;EAChB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;IACjC,IAAI,CAAC,SAAS,GAAG,MAAK;IACtB,IAAI,CAAC,UAAU,GAAG,MAAK;GACxB;EACD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;IAC/B,EAAA,IAAI,CAAC,KAAK,GAAG,MAAK,EAAA;EACrB;;;;;AAKDA,IAAE,CAAC,YAAY,GAAG,SAAS,IAAI,EAAE,OAAO,EAAE;;;EACxC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,KAAK,IAAI,CAAC,IAAI,KAAK,kBAAkB,IAAI,CAAC,OAAO,CAAC,EAAE;;GAE1F,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB,EAAE;IAClD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAC;GAC5C,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,EAAE;IACvC,OAAO,IAAI,CAAC,UAAU,EAAE;GACzB,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE;IAC3C,IAAI,CAAC,IAAI,GAAG,gBAAe;IAC3B,KAAa,kBAAI,IAAI,CAAC,UAAU,yBAAA;MAA3B;MAAAR,IAAI,IAAI;;MACXC,MAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAC;KAAA;GACnC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,EAAE;IAC1C,IAAI,CAAC,IAAI,GAAG,eAAc;IAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAC;GAC9C,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;IACnC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAC;GACvC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE;IACxC,IAAI,CAAC,IAAI,GAAG,cAAa;IACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAC;GAC1C,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAsB,EAAE;IAC/C,IAAI,CAAC,IAAI,GAAG,oBAAmB;IAC/B,OAAO,IAAI,CAAC,SAAQ;GACrB,MAAM;IACL,OAAO,IAAI,CAAC,UAAU,EAAE;GACzB;EACD,OAAO,IAAI;EACZ;;AAEDO,IAAE,CAAC,gBAAgB,GAAG,SAAS,QAAQ,EAAE,OAAO,EAAE;;;EAChD,KAAa,kBAAI,QAAQ,yBAAA;IAApB;IAAAR,IAAI,IAAI;;IACXC,MAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAC;GAAA;EAClC,OAAO,QAAQ;EAChB;;AAEDO,IAAE,CAAC,mBAAmB,GAAG,SAAS,MAAM,EAAE;EACxC,MAAM,GAAG,IAAI,CAAC,aAAa,CAACZ,cAAE,CAAC,MAAM,EAAC;EACtC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC;EAC3C;;AAEDY,IAAE,CAAC,WAAW,GAAG,SAAS,WAAW,EAAE,OAAO,EAAE;EAC9CR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,CAAC,WAAU;EACvF,IAAI,CAAC,YAAY,CAAC,IAAI,EAAC;EACvB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;IAC/B,EAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,YAAW,EAAA;EAChC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;IAC/B,EAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,QAAO,EAAA;EACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAK;EACzB,IAAI,CAAC,UAAU,GAAG,KAAI;EACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,GAAE;EACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,GAAE;EAC7B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;EAChD,IAAI,CAAC,OAAO,GAAG,WAAU;EACzB,IAAI,CAAC,UAAU,GAAG,cAAa;EAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,oBAAoB,CAAC;EACnD;;AAEDQ,IAAE,CAAC,oBAAoB,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;EACxDR,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,CAAC,WAAU;EAC9D,IAAI,CAAC,YAAY,CAAC,IAAI,EAAC;EACvB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;IAC/B,EAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,QAAO,EAAA;EACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAK;EACzB,IAAI,CAAC,UAAU,GAAG,KAAI;EACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAC;EACjD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,KAAKJ,cAAE,CAAC,OAAM;EAC7C,IAAI,IAAI,CAAC,UAAU,EAAE;IACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,GAAE;GACpC,MAAM;IACL,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,GAAE;IAC7B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC;GACjD;EACD,IAAI,CAAC,OAAO,GAAG,WAAU;EACzB,IAAI,CAAC,UAAU,GAAG,cAAa;EAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,yBAAyB,CAAC;EACxD;;AAEDY,IAAE,CAAC,aAAa,GAAG,SAAS,KAAK,EAAE,UAAU,EAAE;;;EAC7C,IAAI,CAAC,MAAM,GAAE;EACbR,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,GAAG,GAAE;EAChE,IAAI,CAAC,IAAI,GAAE;EACX,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE;IAC5C,IAAIC,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,CAAC,EAAE;MACtB,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,GAAGK,MAAI,CAAC,UAAU,EAAE,EAAC;MAChD,QAAQ;KACT;IACDD,IAAI,GAAG,GAAGC,MAAI,CAAC,gBAAgB,GAAE;IACjC,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;MAChB,IAAIA,MAAI,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAA,KAAK,EAAA;MAC3CA,MAAI,CAAC,IAAI,GAAE;KACZ,MAAM;MACL,IAAI,CAAC,IAAI,CAAC,GAAG,EAAC;KACf;IACDA,MAAI,CAAC,GAAG,CAACL,cAAE,CAAC,KAAK,EAAC;GACnB;EACD,IAAI,CAAC,KAAK,GAAE;EACZ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;;;IAGpB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAK;IAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAK,EAAA;GACnE;EACD,OAAO,IAAI;EACZ;;AAEDY,IAAE,CAAC,UAAU,GAAG,WAAW;EACzBR,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,GAAE;EAC3B,IAAI,CAAC,IAAI,GAAE;EACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAE;EACtC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC;CAChD;;AC3kBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,AAQAa,oBAAc,CAAC,OAAO,GAAG,EAAC;;AAE1B,AAAO,SAAS,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE;EACpC,OAAO,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC;CACzC;;;;;;;;;;;;;"}