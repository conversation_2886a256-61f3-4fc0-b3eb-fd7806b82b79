<base-info-card wx:if="{{J}}" class="data-v-cc27e780" u-s="{{['content']}}" u-i="cc27e780-0" bind:__l="__l" u-p="{{J}}"><view class="house-details data-v-cc27e780" slot="content"><view class="house-basic-info data-v-cc27e780"><view wx:if="{{a}}" class="house-type data-v-cc27e780"><view class="house-icon type-icon data-v-cc27e780"><svg wx:if="{{d}}" class="data-v-cc27e780" u-s="{{['d']}}" u-i="cc27e780-1,cc27e780-0" bind:__l="__l" u-p="{{d}}"><path wx:if="{{b}}" class="data-v-cc27e780" u-i="cc27e780-2,cc27e780-1" bind:__l="__l" u-p="{{b}}"></path><polyline wx:if="{{c}}" class="data-v-cc27e780" u-i="cc27e780-3,cc27e780-1" bind:__l="__l" u-p="{{c}}"></polyline></svg></view><text class="house-text data-v-cc27e780">{{e}}</text></view><view wx:if="{{f}}" class="house-size data-v-cc27e780"><view class="house-icon size-icon data-v-cc27e780"><svg wx:if="{{j}}" class="data-v-cc27e780" u-s="{{['d']}}" u-i="cc27e780-4,cc27e780-0" bind:__l="__l" u-p="{{j}}"><rect wx:if="{{g}}" class="data-v-cc27e780" u-i="cc27e780-5,cc27e780-4" bind:__l="__l" u-p="{{g}}"></rect><line wx:if="{{h}}" class="data-v-cc27e780" u-i="cc27e780-6,cc27e780-4" bind:__l="__l" u-p="{{h}}"></line><line wx:if="{{i}}" class="data-v-cc27e780" u-i="cc27e780-7,cc27e780-4" bind:__l="__l" u-p="{{i}}"></line></svg></view><text class="house-text data-v-cc27e780">{{k}}㎡</text></view><view wx:if="{{l}}" class="house-floor data-v-cc27e780"><view class="house-icon floor-icon data-v-cc27e780"><svg wx:if="{{p}}" class="data-v-cc27e780" u-s="{{['d']}}" u-i="cc27e780-8,cc27e780-0" bind:__l="__l" u-p="{{p}}"><rect wx:if="{{m}}" class="data-v-cc27e780" u-i="cc27e780-9,cc27e780-8" bind:__l="__l" u-p="{{m}}"></rect><line wx:if="{{n}}" class="data-v-cc27e780" u-i="cc27e780-10,cc27e780-8" bind:__l="__l" u-p="{{n}}"></line><line wx:if="{{o}}" class="data-v-cc27e780" u-i="cc27e780-11,cc27e780-8" bind:__l="__l" u-p="{{o}}"></line></svg></view><text class="house-text data-v-cc27e780">{{q}}</text></view><view wx:if="{{r}}" class="house-direction data-v-cc27e780"><view class="house-icon direction-icon data-v-cc27e780"><svg wx:if="{{w}}" class="data-v-cc27e780" u-s="{{['d']}}" u-i="cc27e780-12,cc27e780-0" bind:__l="__l" u-p="{{w}}"><circle wx:if="{{s}}" class="data-v-cc27e780" u-i="cc27e780-13,cc27e780-12" bind:__l="__l" u-p="{{s}}"></circle><polyline wx:if="{{t}}" class="data-v-cc27e780" u-i="cc27e780-14,cc27e780-12" bind:__l="__l" u-p="{{t}}"></polyline><line wx:if="{{v}}" class="data-v-cc27e780" u-i="cc27e780-15,cc27e780-12" bind:__l="__l" u-p="{{v}}"></line></svg></view><text class="house-text data-v-cc27e780">{{x}}</text></view></view><view wx:if="{{y}}" class="house-facilities data-v-cc27e780"><view wx:for="{{z}}" wx:for-item="facility" wx:key="e" class="facility-tag data-v-cc27e780"><view class="{{['facility-icon', 'data-v-cc27e780', facility.c]}}"><svg wx:if="{{B}}" class="data-v-cc27e780" u-s="{{['d']}}" u-i="{{facility.b}}" bind:__l="__l" u-p="{{B}}"><polyline wx:if="{{A}}" class="data-v-cc27e780" u-i="{{facility.a}}" bind:__l="__l" u-p="{{A}}"></polyline></svg></view><text class="facility-text data-v-cc27e780">{{facility.d}}</text></view></view><view wx:if="{{C}}" class="house-features data-v-cc27e780"><view wx:for="{{D}}" wx:for-item="feature" wx:key="b" class="feature-tag data-v-cc27e780"><text class="feature-text data-v-cc27e780">{{feature.a}}</text></view></view><view wx:if="{{E}}" class="house-location data-v-cc27e780"><view class="location-icon data-v-cc27e780"><svg wx:if="{{H}}" class="data-v-cc27e780" u-s="{{['d']}}" u-i="cc27e780-18,cc27e780-0" bind:__l="__l" u-p="{{H}}"><path wx:if="{{F}}" class="data-v-cc27e780" u-i="cc27e780-19,cc27e780-18" bind:__l="__l" u-p="{{F}}"></path><circle wx:if="{{G}}" class="data-v-cc27e780" u-i="cc27e780-20,cc27e780-18" bind:__l="__l" u-p="{{G}}"></circle></svg></view><text class="location-text data-v-cc27e780">{{I}}</text></view></view></base-info-card>