<view class="cart-container data-v-a2ece00a"><view class="custom-navbar data-v-a2ece00a"><view class="navbar-bg data-v-a2ece00a"></view><view class="navbar-content data-v-a2ece00a"><view class="navbar-left data-v-a2ece00a" bindtap="{{c}}"><svg wx:if="{{b}}" u-s="{{['d']}}" class="icon data-v-a2ece00a" u-i="a2ece00a-0" bind:__l="__l" u-p="{{b}}"><path wx:if="{{a}}" class="data-v-a2ece00a" u-i="a2ece00a-1,a2ece00a-0" bind:__l="__l" u-p="{{a}}"></path></svg></view><view class="navbar-title data-v-a2ece00a">购物车</view><view class="navbar-right data-v-a2ece00a"><view class="edit-btn data-v-a2ece00a" bindtap="{{e}}">{{d}}</view></view></view></view><scroll-view wx:if="{{f}}" class="content-scroll data-v-a2ece00a" scroll-y bindscrolltolower="{{o}}"><view wx:for="{{g}}" wx:for-item="shop" wx:key="n" class="shop-group data-v-a2ece00a"><view class="shop-header data-v-a2ece00a"><view class="checkbox-wrapper data-v-a2ece00a" bindtap="{{shop.g}}"><view class="{{['checkbox', 'data-v-a2ece00a', shop.f && 'checked']}}"><svg wx:if="{{shop.a}}" u-s="{{['d']}}" class="check-icon data-v-a2ece00a" u-i="{{shop.d}}" bind:__l="__l" u-p="{{shop.e}}"><path wx:if="{{shop.c}}" class="data-v-a2ece00a" u-i="{{shop.b}}" bind:__l="__l" u-p="{{shop.c}}"></path></svg></view></view><view class="shop-info data-v-a2ece00a" bindtap="{{shop.l}}"><image class="shop-logo data-v-a2ece00a" src="{{shop.h}}" mode="aspectFill"></image><view class="shop-name data-v-a2ece00a">{{shop.i}}</view><svg wx:if="{{i}}" u-s="{{['d']}}" class="arrow-icon data-v-a2ece00a" u-i="{{shop.k}}" bind:__l="__l" u-p="{{i}}"><path wx:if="{{h}}" class="data-v-a2ece00a" u-i="{{shop.j}}" bind:__l="__l" u-p="{{h}}"></path></svg></view></view><view class="cart-items data-v-a2ece00a"><view wx:for="{{shop.m}}" wx:for-item="item" wx:key="v" class="cart-item data-v-a2ece00a"><view class="checkbox-wrapper data-v-a2ece00a" bindtap="{{item.g}}"><view class="{{['checkbox', 'data-v-a2ece00a', item.f && 'checked']}}"><svg wx:if="{{item.a}}" u-s="{{['d']}}" class="check-icon data-v-a2ece00a" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"><path wx:if="{{item.c}}" class="data-v-a2ece00a" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></path></svg></view></view><image class="item-image data-v-a2ece00a" src="{{item.h}}" mode="aspectFill" bindtap="{{item.i}}"></image><view class="item-info data-v-a2ece00a" bindtap="{{item.t}}"><view class="item-name data-v-a2ece00a">{{item.j}}</view><view class="item-specs data-v-a2ece00a">{{item.k}}</view><view class="item-bottom data-v-a2ece00a"><view class="item-price data-v-a2ece00a">¥{{item.l}}</view><view class="quantity-control data-v-a2ece00a"><view class="quantity-btn minus data-v-a2ece00a" catchtap="{{item.o}}"><svg wx:if="{{k}}" u-s="{{['d']}}" class="icon data-v-a2ece00a" u-i="{{item.n}}" bind:__l="__l" u-p="{{k}}"><path wx:if="{{j}}" class="data-v-a2ece00a" u-i="{{item.m}}" bind:__l="__l" u-p="{{j}}"></path></svg></view><view class="quantity-value data-v-a2ece00a">{{item.p}}</view><view class="quantity-btn plus data-v-a2ece00a" catchtap="{{item.s}}"><svg wx:if="{{m}}" u-s="{{['d']}}" class="icon data-v-a2ece00a" u-i="{{item.r}}" bind:__l="__l" u-p="{{m}}"><path wx:if="{{l}}" class="data-v-a2ece00a" u-i="{{item.q}}" bind:__l="__l" u-p="{{l}}"></path></svg></view></view></view></view></view></view></view><view class="recommend-section data-v-a2ece00a"><view class="section-title data-v-a2ece00a">猜你喜欢</view><view class="product-grid data-v-a2ece00a"><view wx:for="{{n}}" wx:for-item="product" wx:key="d" class="product-item data-v-a2ece00a" bindtap="{{product.e}}"><image class="product-image data-v-a2ece00a" src="{{product.a}}" mode="aspectFill"></image><view class="product-name data-v-a2ece00a">{{product.b}}</view><view class="product-price data-v-a2ece00a">¥{{product.c}}</view></view></view></view><view class="safe-area-bottom data-v-a2ece00a"></view></scroll-view><view wx:else class="empty-cart data-v-a2ece00a"><image class="empty-icon data-v-a2ece00a" src="{{p}}" mode="aspectFit"></image><view class="empty-text data-v-a2ece00a">购物车空空如也</view><view class="go-shopping-btn data-v-a2ece00a" bindtap="{{q}}">去逛逛</view></view><view wx:if="{{r}}" class="checkout-bar data-v-a2ece00a"><view class="select-all data-v-a2ece00a" bindtap="{{x}}"><view class="{{['checkbox', 'data-v-a2ece00a', w && 'checked']}}"><svg wx:if="{{s}}" u-s="{{['d']}}" class="check-icon data-v-a2ece00a" u-i="a2ece00a-12" bind:__l="__l" u-p="{{v}}"><path wx:if="{{t}}" class="data-v-a2ece00a" u-i="a2ece00a-13,a2ece00a-12" bind:__l="__l" u-p="{{t}}"></path></svg></view><text class="data-v-a2ece00a">全选</text></view><view wx:if="{{y}}" class="total-section data-v-a2ece00a"><view class="total-price data-v-a2ece00a"><text class="data-v-a2ece00a">合计：</text><text class="price data-v-a2ece00a">¥{{z}}</text></view><view class="checkout-btn data-v-a2ece00a" bindtap="{{B}}"> 结算({{A}}) </view></view><view wx:else class="delete-section data-v-a2ece00a"><view class="delete-btn data-v-a2ece00a" bindtap="{{C}}"> 删除 </view></view></view></view>