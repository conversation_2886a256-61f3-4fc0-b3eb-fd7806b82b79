
.share-records-container.data-v-2b334a2a {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar.data-v-2b334a2a {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}
.navbar-bg.data-v-2b334a2a {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #5856D6 0%, #A09CFF 100%);
}
.navbar-content.data-v-2b334a2a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 113rpx; /* 原来是110rpx，增加3rpx */
  padding: var(--status-bar-height) 30rpx 0;
}
.back-btn.data-v-2b334a2a, .filter-btn.data-v-2b334a2a {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-2b334a2a {
  width: 36rpx;
  height: 36rpx;
}
.navbar-title.data-v-2b334a2a {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.navbar-right.data-v-2b334a2a {
  display: flex;
  align-items: center;
}

/* 标签栏样式 */
.share-tabs.data-v-2b334a2a {
  display: flex;
  background: #FFFFFF;
  padding: 0 20rpx;
  margin-top: calc(var(--status-bar-height) + 113rpx); /* 原来是110rpx，增加3rpx */
  border-bottom: 1rpx solid #EEEEEE;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.tab-item.data-v-2b334a2a {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}
.tab-text.data-v-2b334a2a {
  font-size: 28rpx;
  color: #333333;
  padding: 0 10rpx;
}
.tab-item.active .tab-text.data-v-2b334a2a {
  color: #5856D6;
  font-weight: 500;
}
.tab-indicator.data-v-2b334a2a {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 6rpx;
  border-radius: 3rpx;
}

/* 统计卡片样式 */
.stats-card.data-v-2b334a2a {
  display: flex;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.stat-item.data-v-2b334a2a {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value.data-v-2b334a2a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}
.stat-label.data-v-2b334a2a {
  font-size: 24rpx;
  color: #999999;
}
.stat-divider.data-v-2b334a2a {
  width: 1rpx;
  height: 60rpx;
  background: #EEEEEE;
  margin: 0 10rpx;
}

/* 分享记录列表样式 */
.records-swiper.data-v-2b334a2a {
  height: calc(100vh - var(--status-bar-height) - 113rpx - 70rpx - 130rpx); /* 原来是110rpx，增加3rpx */
}
.tab-content.data-v-2b334a2a {
  height: 100%;
  padding: 0 20rpx 20rpx;
}
.records-list.data-v-2b334a2a {
  padding-bottom: 30rpx;
}
.record-card.data-v-2b334a2a {
  display: flex;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.record-preview.data-v-2b334a2a {
  width: 200rpx;
  height: 200rpx;
  position: relative;
}
.record-image.data-v-2b334a2a {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.record-type-tag.data-v-2b334a2a {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: #FFFFFF;
}
.record-info.data-v-2b334a2a {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}
.record-title.data-v-2b334a2a {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.record-meta.data-v-2b334a2a {
  display: flex;
  margin-bottom: 10rpx;
}
.meta-item.data-v-2b334a2a {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}
.meta-icon.data-v-2b334a2a {
  margin-right: 6rpx;
}
.meta-text.data-v-2b334a2a {
  font-size: 24rpx;
  color: #999999;
}
.record-stats.data-v-2b334a2a {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.stat-row.data-v-2b334a2a {
  display: flex;
  margin-bottom: 10rpx;
}
.mini-stat.data-v-2b334a2a {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}
.stat-icon.data-v-2b334a2a {
  margin-right: 6rpx;
}
.stat-text.data-v-2b334a2a {
  font-size: 24rpx;
  color: #999999;
}
.mini-stat.highlight .stat-text.data-v-2b334a2a {
  color: #5856D6;
  font-weight: 500;
}
.record-actions.data-v-2b334a2a {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 10rpx;
}
.action-btn.data-v-2b334a2a {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-left: 15rpx;
  border: 1rpx solid #DDDDDD;
  color: #666666;
}
.action-icon.data-v-2b334a2a {
  margin-right: 6rpx;
}
.action-btn.primary.data-v-2b334a2a {
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
  border: none;
}

/* 空状态样式 */
.empty-state.data-v-2b334a2a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image.data-v-2b334a2a {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-2b334a2a {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.empty-state .action-btn.data-v-2b334a2a {
  padding: 15rpx 60rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  border: none;
}

/* 筛选弹窗样式 */
.filter-popup.data-v-2b334a2a {
  background: #FFFFFF;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  padding: 30rpx;
  max-height: 70vh;
}
.filter-header.data-v-2b334a2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.filter-title.data-v-2b334a2a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.filter-close.data-v-2b334a2a {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-content.data-v-2b334a2a {
  max-height: calc(70vh - 180rpx);
  overflow-y: auto;
}
.filter-section.data-v-2b334a2a {
  margin-bottom: 30rpx;
}
.section-title.data-v-2b334a2a {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.filter-options.data-v-2b334a2a {
  display: flex;
  flex-wrap: wrap;
}
.filter-option.data-v-2b334a2a {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666666;
  background: #F5F5F5;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.filter-option.active.data-v-2b334a2a {
  background: rgba(88, 86, 214, 0.1);
  color: #5856D6;
  border: 1rpx solid rgba(88, 86, 214, 0.3);
}
.filter-footer.data-v-2b334a2a {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #F0F0F0;
}
.filter-reset.data-v-2b334a2a, .filter-apply.data-v-2b334a2a {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}
.filter-reset.data-v-2b334a2a {
  background: #F5F5F5;
  color: #666666;
  margin-right: 20rpx;
}
.filter-apply.data-v-2b334a2a {
  background: linear-gradient(135deg, #5856D6 0%, #A09CFF 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(88, 86, 214, 0.2);
}
