<template>
  <view class="coupons-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">会员卡券</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 卡券概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">卡券概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{couponData.totalCoupons}}</view>
          <view class="card-label">总卡券数</view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{couponData.issuedCoupons}}</view>
          <view class="card-label">已发放卡券</view>
          <view class="card-trend" :class="couponData.issuedTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{couponData.issuedGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{couponData.usedCoupons}}</view>
          <view class="card-label">已使用卡券</view>
          <view class="card-trend" :class="couponData.usedTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{couponData.usedGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{couponData.useRate}}%</view>
          <view class="card-label">使用率</view>
          <view class="card-trend" :class="couponData.useRateTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{couponData.useRateGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 卡券分类标签 -->
    <view class="tabs-section">
      <scroll-view scroll-x class="tabs-scroll" show-scrollbar="false">
        <view class="tabs">
          <view 
            class="tab-item" 
            v-for="(tab, index) in tabs" 
            :key="index"
            :class="{ active: currentTab === index }"
            @click="switchTab(index)"
          >
            {{tab}}
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 卡券列表 -->
    <view class="coupons-section">
      <view class="section-header">
        <text class="section-title">{{tabs[currentTab]}}</text>
        <view class="add-btn" @click="createCoupon">
          <text class="btn-text">添加卡券</text>
          <view class="plus-icon"></view>
        </view>
      </view>
      
      <view class="coupons-list">
        <view class="coupon-item" v-for="(coupon, index) in filteredCoupons" :key="index" @click="editCoupon(coupon)">
          <view class="coupon-card" :class="coupon.type">
            <view class="coupon-left">
              <view class="coupon-value">
                <text class="value-prefix" v-if="coupon.type === 'discount'">{{coupon.value}}<text class="value-unit">折</text></text>
                <text class="value-prefix" v-else>¥<text class="value-number">{{coupon.value}}</text></text>
              </view>
              <view class="coupon-condition">{{coupon.condition}}</view>
            </view>
            <view class="coupon-divider">
              <view class="circle top"></view>
              <view class="dashed-line"></view>
              <view class="circle bottom"></view>
            </view>
            <view class="coupon-right">
              <view class="coupon-name">{{coupon.name}}</view>
              <view class="coupon-desc">{{coupon.description}}</view>
              <view class="coupon-period">{{coupon.validPeriod}}</view>
              <view class="coupon-levels">
                <text class="level-tag" v-for="(level, levelIndex) in coupon.memberLevels" :key="levelIndex">{{level}}</text>
              </view>
            </view>
            <view class="coupon-status" :class="coupon.status">{{coupon.statusText}}</view>
          </view>
          <view class="coupon-actions">
            <view class="action-btn issue" @click.stop="issueCoupon(coupon)">
              <svg class="svg-icon" viewBox="0 0 24 24" fill="#FF6FD8">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
              </svg>
              <text>发放</text>
            </view>
            <view class="action-btn stats" @click.stop="viewStats(coupon)">
              <svg class="svg-icon" viewBox="0 0 24 24" fill="#3813C2">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
              </svg>
              <text>统计</text>
            </view>
            <view class="action-btn toggle">
              <switch :checked="coupon.enabled" @change="(e) => toggleCoupon(coupon, e)" color="#FF6FD8" />
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="createCoupon">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      currentTab: 0,
      
      // 卡券数据概览
      couponData: {
        totalCoupons: 16,
        issuedCoupons: 3568,
        issuedTrend: 'up',
        issuedGrowth: '15.2%',
        usedCoupons: 1245,
        usedTrend: 'up',
        usedGrowth: '8.7%',
        useRate: 34.9,
        useRateTrend: 'down',
        useRateGrowth: '2.3%'
      },
      
      // 卡券分类标签
      tabs: ['全部卡券', '满减券', '折扣券', '代金券', '会员专享'],
      
      // 卡券列表
      coupons: [
        {
          id: 1,
          name: '新人专享券',
          description: '新会员专享优惠',
          type: 'cash',
          value: 10,
          condition: '满100元可用',
          validPeriod: '领取后30天内有效',
          category: '代金券',
          memberLevels: ['全部会员'],
          status: 'active',
          statusText: '进行中',
          enabled: true
        },
        {
          id: 2,
          name: '会员日特惠',
          description: '每月28日会员专享',
          type: 'discount',
          value: 8.5,
          condition: '无门槛',
          validPeriod: '每月28日当天有效',
          category: '折扣券',
          memberLevels: ['银卡会员', '金卡会员', '钻石会员'],
          status: 'active',
          statusText: '进行中',
          enabled: true
        },
        {
          id: 3,
          name: '生日礼券',
          description: '会员生日当月专享',
          type: 'cash',
          value: 50,
          condition: '满200元可用',
          validPeriod: '生日当月有效',
          category: '会员专享',
          memberLevels: ['银卡会员', '金卡会员', '钻石会员'],
          status: 'active',
          statusText: '进行中',
          enabled: true
        },
        {
          id: 4,
          name: '满减优惠券',
          description: '全场通用满减券',
          type: 'cash',
          value: 20,
          condition: '满200元可用',
          validPeriod: '2023-04-01至2023-04-30',
          category: '满减券',
          memberLevels: ['全部会员'],
          status: 'active',
          statusText: '进行中',
          enabled: true
        },
        {
          id: 5,
          name: '钻石会员专享',
          description: '钻石会员专享优惠',
          type: 'discount',
          value: 7.5,
          condition: '无门槛',
          validPeriod: '长期有效',
          category: '会员专享',
          memberLevels: ['钻石会员'],
          status: 'active',
          statusText: '进行中',
          enabled: true
        },
        {
          id: 6,
          name: '限时满减券',
          description: '周末限时优惠',
          type: 'cash',
          value: 30,
          condition: '满300元可用',
          validPeriod: '每周六日有效',
          category: '满减券',
          memberLevels: ['全部会员'],
          status: 'active',
          statusText: '进行中',
          enabled: true
        }
      ]
    }
  },
  computed: {
    filteredCoupons() {
      if (this.currentTab === 0) {
        return this.coupons;
      } else {
        const category = this.tabs[this.currentTab];
        return this.coupons.filter(coupon => coupon.category === category);
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showHelp() {
      uni.showModal({
        title: '会员卡券帮助',
        content: '会员卡券是指为会员提供的各类优惠券，包括满减券、折扣券、代金券等，可以提高会员购买转化率和复购率。',
        showCancel: false
      });
    },
    
    showDatePicker() {
      // 实现日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    
    switchTab(index) {
      this.currentTab = index;
    },
    
    createCoupon() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/create-coupon?type=member'
      });
    },
    
    editCoupon(coupon) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/edit-coupon?id=${coupon.id}`
      });
    },
    
    issueCoupon(coupon) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/issue-coupon?id=${coupon.id}`
      });
    },
    
    viewStats(coupon) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon-stats?id=${coupon.id}`
      });
    },
    
    toggleCoupon(coupon, e) {
      // 更新卡券状态
      const index = this.coupons.findIndex(item => item.id === coupon.id);
      if (index !== -1) {
        this.coupons[index].enabled = e.detail.value;
        this.coupons[index].status = e.detail.value ? 'active' : 'inactive';
        this.coupons[index].statusText = e.detail.value ? '进行中' : '已暂停';
      }
      
      uni.showToast({
        title: e.detail.value ? `${coupon.name}已启用` : `${coupon.name}已禁用`,
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.coupons-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF6FD8, #3813C2);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 111, 216, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  background: #FF6FD8;
  border-radius: 15px;
  padding: 5px 12px;
  color: white;
}

.btn-text {
  font-size: 13px;
  margin-right: 5px;
}

.plus-icon {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon:before,
.plus-icon:after {
  content: '';
  position: absolute;
  background: white;
}

.plus-icon:before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon:after {
  height: 12px;
  width: 2px;
  left: 5px;
  top: 0;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #FF6FD8;
}

.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}

.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}

.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 标签页样式 */
.tabs-section {
  margin: 15px 15px 0;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs {
  display: inline-flex;
  padding: 5px 0;
}

.tab-item {
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.tab-item.active {
  background: #FF6FD8;
  color: white;
  box-shadow: 0 2px 8px rgba(255, 111, 216, 0.3);
}

/* 卡券列表样式 */
.coupons-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.coupons-list {
  margin-top: 10px;
}

.coupon-item {
  margin-bottom: 15px;
}

.coupon-card {
  display: flex;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
}

.coupon-card.cash {
  background: linear-gradient(135deg, rgba(255, 111, 216, 0.1), rgba(255, 111, 216, 0.05));
}

.coupon-card.discount {
  background: linear-gradient(135deg, rgba(56, 19, 194, 0.1), rgba(56, 19, 194, 0.05));
}

.coupon-left {
  width: 100px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.coupon-value {
  font-size: 24px;
  font-weight: 700;
  color: #FF6FD8;
  margin-bottom: 5px;
}

.coupon-card.discount .coupon-value {
  color: #3813C2;
}

.value-prefix {
  font-size: 16px;
}

.value-number {
  font-size: 24px;
}

.value-unit {
  font-size: 14px;
}

.coupon-condition {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.coupon-divider {
  position: relative;
  width: 1px;
  background: repeating-linear-gradient(to bottom, #ddd 0, #ddd 5px, transparent 5px, transparent 10px);
}

.circle {
  position: absolute;
  width: 16px;
  height: 16px;
  background: #F5F7FA;
  border-radius: 50%;
  left: -7.5px;
}

.circle.top {
  top: -8px;
}

.circle.bottom {
  bottom: -8px;
}

.coupon-right {
  flex: 1;
  padding: 15px;
  position: relative;
}

.coupon-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.coupon-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.coupon-period {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.coupon-levels {
  display: flex;
  flex-wrap: wrap;
}

.level-tag {
  font-size: 10px;
  color: #FF6FD8;
  background: rgba(255, 111, 216, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 5px;
  margin-bottom: 3px;
}

.coupon-card.discount .level-tag {
  color: #3813C2;
  background: rgba(56, 19, 194, 0.1);
}

.coupon-status {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.coupon-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.coupon-status.inactive {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.coupon-actions {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  background: #F8FAFC;
  border-radius: 0 0 12px 12px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-btn text {
  font-size: 12px;
  color: #666;
  margin-top: 3px;
}

.svg-icon {
  width: 20px;
  height: 20px;
}

.action-btn.toggle {
  display: flex;
  align-items: center;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #FF6FD8, #3813C2);
  box-shadow: 0 4px 15px rgba(255, 111, 216, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .stats-card {
    width: 100%;
  }
  
  .coupon-left {
    width: 80px;
  }
  
  .coupon-value {
    font-size: 20px;
  }
}
</style> 