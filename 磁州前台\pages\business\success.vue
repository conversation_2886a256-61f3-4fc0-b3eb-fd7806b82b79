<template>
  <view class="success-container">
    <!-- 顶部背景渐变 -->
    <view class="top-gradient"></view>
    
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">商家入驻</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content">
      <!-- 成功信息 -->
      <view class="success-info">
        <view class="success-message-card">
          <image :src="celebrateIcon || '/static/images/tabbar/成功.png'" class="success-message-icon"></image>
          <view class="success-message-content">
            <view class="success-message-title">恭喜！您已成功入驻</view>
            <view class="success-message-subtitle">审核通过，您的店铺已正式上线</view>
        </view>
      </view>
      
        <view class="shop-info">
          <view class="shop-avatar">
            <image :src="shopInfo.logo || '/static/images/default-shop.png'" mode="aspectFill"></image>
          </view>
          <view class="shop-name">{{shopInfo.name}}</view>
          <view class="shop-verify-tip" v-if="!isVerified">
            <image src="/static/images/tabbar/认证.png" class="verify-icon"></image>
            <text class="verify-text">去认证享更多权益</text>
            <view class="verify-btn" @click="goToVerify">去认证</view>
          </view>
          <view class="shop-verified" v-if="isVerified">
            <image src="/static/images/tabbar/已认证.png" class="verified-icon"></image>
            <text class="verified-text">已认证商家</text>
          </view>
          <view class="test-mark" v-if="isTestMerchant">测试账号</view>
        </view>
        
        <!-- 入驻版本信息 -->
        <view class="membership-info" v-if="memberType">
          <view class="membership-badge" :class="memberClass">{{memberType}}</view>
          <view class="membership-date">{{expiryDate}} 到期</view>
        </view>
      </view>
      
      <!-- 商家数据概览 -->
      <view class="data-overview">
        <view class="data-card">
          <view class="data-item">
            <view class="data-num">120万+</view>
            <view class="data-label">平台月流量</view>
          </view>
          <view class="data-divider"></view>
          <view class="data-item">
            <view class="data-num">32%</view>
            <view class="data-label">商家转化率</view>
          </view>
          <view class="data-divider"></view>
          <view class="data-item">
            <view class="data-num">16.8万</view>
            <view class="data-label">注册用户</view>
          </view>
        </view>
      </view>
      
      <!-- 认证好处提示（仅未认证商家显示） -->
      <view class="verify-benefits" v-if="!isVerified">
        <view class="verify-benefits-title">
          <image src="/static/images/tabbar/认证.png" class="verify-benefit-icon"></image>
          <text>商家认证享多重权益</text>
        </view>
        <view class="verify-benefits-list">
          <view class="verify-benefit-item">
            <view class="benefit-dot"></view>
            <text class="benefit-text">店铺获得官方认证标识，提升信任度</text>
          </view>
          <view class="verify-benefit-item">
            <view class="benefit-dot"></view>
            <text class="benefit-text">搜索结果排名靠前，提高曝光量</text>
          </view>
          <view class="verify-benefit-item">
            <view class="benefit-dot"></view>
            <text class="benefit-text">获取平台流量扶持，拓展客源渠道</text>
          </view>
          <view class="verify-benefit-item">
            <view class="benefit-dot"></view>
            <text class="benefit-text">专属客服通道，问题快速解决</text>
          </view>
        </view>
        <button class="verify-now-btn" @click="goToVerify">立即认证</button>
      </view>
      
      <!-- 功能入口 -->
      <view class="function-section">
        <view class="function-title">商家中心功能</view>
        <view class="function-grid">
          <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/goods')">
            <view class="function-icon-bg bg-blue">
              <image src="/static/images/tabbar/商品.png" class="function-icon"></image>
            </view>
            <view class="function-name">商品管理</view>
          </view>
          <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/orders')">
            <view class="function-icon-bg bg-orange">
              <image src="/static/images/tabbar/订单.png" class="function-icon"></image>
            </view>
            <view class="function-name">订单管理</view>
          </view>
          <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/marketing')">
            <view class="function-icon-bg bg-green">
              <image src="/static/images/tabbar/营销.png" class="function-icon"></image>
            </view>
            <view class="function-name">营销推广</view>
          </view>
          <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/analysis')">
            <view class="function-icon-bg bg-purple">
              <image src="/static/images/tabbar/数据.png" class="function-icon"></image>
            </view>
            <view class="function-name">数据分析</view>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="primary-btn" @click="goToMerchantCenter">进入商家后台</button>
        <button class="secondary-btn" @click="goToShopPage">查看店铺页面</button>
      </view>
      
      <!-- 店铺推广操作 -->
      <view class="promotion-section">
        <!-- 整合后的推广卡片 -->
        <view class="promotion-card compact-card">
          <view class="card-header">
            <text class="card-title">店铺推广</text>
            <text class="card-subtitle">提升店铺曝光度，获取更多客源</text>
            </view>
          <view class="promotion-tabs">
            <view class="promotion-tab" :class="{'active': activeTab === 'top'}" @click="activeTab = 'top'">
              <text class="promotion-tab-text">置顶店铺</text>
          </view>
            <view class="promotion-tab" :class="{'active': activeTab === 'refresh'}" @click="activeTab = 'refresh'">
              <text class="promotion-tab-text">刷新店铺</text>
            </view>
          </view>
          
          <view class="promotion-content">
            <view v-if="activeTab === 'top'" class="tab-content-animation">
          <ConfigurablePremiumActions
            showMode="direct"
            pageType="merchant_top"
            :itemData="topData"
            @action-completed="handleTopCompleted"
            @action-cancelled="handleTopCancelled"
          />
        </view>
            <view v-if="activeTab === 'refresh'" class="tab-content-animation">
          <ConfigurablePremiumActions
            showMode="direct"
            pageType="merchant_refresh"
            :itemData="refreshData"
            @action-completed="handleRefreshCompleted"
            @action-cancelled="handleRefreshCancelled"
          />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 下一步提示 -->
      <view class="next-steps">
        <view class="next-title">推荐您下一步</view>
        <view class="step-item" @click="navigateTo('/subPackages/merchant-plugin/pages/goods')">
          <view class="step-left">
            <view class="step-num">1</view>
            <view class="step-text">上传店铺商品，丰富店铺内容</view>
          </view>
          <view class="step-right">
            <text class="go-text">去上传</text>
            <image src="/static/images/tabbar/arrow-up.png" class="go-icon"></image>
          </view>
        </view>
        <view class="step-item" @click="navigateTo('/subPackages/merchant-plugin/pages/marketing')">
          <view class="step-left">
            <view class="step-num">2</view>
            <view class="step-text">开通店铺推广，提升曝光量</view>
          </view>
          <view class="step-right">
            <text class="go-text">去开通</text>
            <image src="/static/images/tabbar/arrow-up.png" class="go-icon"></image>
          </view>
        </view>
        <view class="step-item" @click="goToMerchantCenter">
          <view class="step-left">
            <view class="step-num">3</view>
            <view class="step-text">完善店铺信息，提高用户信任度</view>
          </view>
          <view class="step-right">
            <text class="go-text">去完善</text>
            <image src="/static/images/tabbar/arrow-up.png" class="go-icon"></image>
          </view>
        </view>
      </view>
      
      <!-- 联系客服 -->
      <view class="contact-section">
        <view class="contact-text">遇到问题？联系客服获取帮助</view>
        <button class="contact-btn" @click="contactCustomerService">联系客服</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// 响应式数据
const statusBarHeight = ref(20);
const shopInfo = ref({
  logo: '',
  name: '品牌专卖店',
  id: 'shop123456'
});
const celebrateIcon = ref('');
const memberType = ref('基础版');
const expiryDate = ref('2024-12-31');
const isTestMerchant = ref(false);
const isVerified = ref(false);
const refreshCount = ref(0); // 店铺刷新次数
const activeTab = ref('top');

// ConfigurablePremiumActions组件需要的数据
const topData = ref({
  id: '',
  title: '商家置顶',
  description: '置顶您的店铺，获得更多曝光'
});

const refreshData = ref({
  id: '',
  title: '商家刷新',
  description: '刷新您的店铺信息到最新'
});

// 计算属性
const memberClass = computed(() => {
  const classMap = {
    '基础版': 'basic-badge',
    '高级版': 'premium-badge',
    '尊贵版': 'deluxe-badge',
    '免费版': 'free-badge',
    '测试版': 'test-badge'
  };
  return classMap[memberType.value] || 'basic-badge';
});

// 生命周期钩子
onLoad((options) => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;

  // 获取管理后台上传的喝彩图标
  getCelebrateIcon();

  // 处理测试商家入驻的参数
  if (options.isTest === 'true') {
    isTestMerchant.value = true;
    if (options.shopName) {
      shopInfo.value.name = decodeURIComponent(options.shopName);
    }
    if (options.memberType) {
      memberType.value = decodeURIComponent(options.memberType);
    }
  }
  // 检查是否是测试入驻的商家
  else if (options.id) {
    loadMerchantData(options.id);
  }
  // 如果有参数传入则获取
  else if (options.shopId) {
    // 实际项目中应该从后端获取店铺信息
    getShopInfo(options.shopId);
  }

  // 设置ConfigurablePremiumActions组件需要的数据
  const shopId = options.shopId || options.id || 'shop_' + Date.now();
  topData.value.id = shopId;
  refreshData.value.id = shopId;

  // 更新店铺信息
  if (shopInfo.value.id !== shopId) {
    shopInfo.value.id = shopId;
  }

  // 获取会员类型
  if (options.memberType && !isTestMerchant.value) {
    memberType.value = decodeURIComponent(options.memberType);

    // 设置到期时间
    const now = new Date();
    if (memberType.value === '免费版') {
      // 免费版一个月
      now.setMonth(now.getMonth() + 1);
    } else {
      // 付费版一年
      now.setFullYear(now.getFullYear() + 1);
    }

    expiryDate.value = now.getFullYear() + '-' +
      String(now.getMonth() + 1).padStart(2, '0') + '-' +
      String(now.getDate()).padStart(2, '0');
  }
  
  // 加载刷新次数
  try {
    const userRefreshData = uni.getStorageSync('userRefreshData') || {};
    const shopId = options.id || options.shopId || '';
    if (shopId && userRefreshData[shopId]) {
      refreshCount.value = userRefreshData[shopId].count || 0;
    }
  } catch (e) {
    console.error('加载刷新次数失败:', e);
    refreshCount.value = 0;
  }
});

// 方法
const goBack = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

const goToVerify = () => {
  uni.navigateTo({
    url: '/pages/business/verify?id=' + shopInfo.value.id
  });
};

const navigateTo = (url) => {
  uni.navigateTo({
    url: url
  });
};

const goToMerchantCenter = () => {
  // 假设商家后台是tabbar页面
  uni.switchTab({
    url: '/pages/my/my' 
  });
};

const goToShopPage = () => {
  uni.navigateTo({
    url: '/pages/business/shop-detail?id=' + shopInfo.value.id
  });
};

const contactCustomerService = () => {
  uni.makePhoneCall({
    phoneNumber: '************' 
  });
};

// 处理置顶操作完成
const handleTopCompleted = (result) => {
  console.log('置顶操作完成:', result);

  if (result.type === 'ad') {
    uni.showToast({
      title: '广告观看完成，店铺已置顶',
      icon: 'success'
    });
  } else if (result.type === 'payment') {
    uni.showToast({
      title: `付费成功，店铺已置顶`,
      icon: 'success'
    });
  }
};

// 处理置顶操作取消
const handleTopCancelled = (result) => {
  console.log('置顶操作取消:', result);

  if (result.type === 'ad') {
    uni.showToast({
      title: '已取消观看广告',
      icon: 'none'
    });
  } else if (result.type === 'payment') {
    uni.showToast({
      title: '已取消支付',
      icon: 'none'
    });
  }
};

// 处理刷新操作完成
const handleRefreshCompleted = (result) => {
  console.log('刷新操作完成:', result);

  if (result.type === 'ad') {
    uni.showToast({
      title: '广告观看完成，店铺已刷新',
      icon: 'success'
    });
  } else if (result.type === 'payment') {
    uni.showToast({
      title: `付费成功，店铺已刷新`,
      icon: 'success'
    });
  }

  // 更新刷新次数
  if (result.data && result.data.remainingCount !== undefined) {
    refreshCount.value = result.data.remainingCount;
  }
};

// 处理刷新操作取消
const handleRefreshCancelled = (result) => {
  console.log('刷新操作取消:', result);

  if (result.type === 'ad') {
    uni.showToast({
      title: '已取消观看广告',
      icon: 'none'
    });
  } else if (result.type === 'payment') {
    uni.showToast({
      title: '已取消支付',
      icon: 'none'
    });
  }
};

const getShopInfo = (shopId) => {
  // 模拟API请求
  setTimeout(() => {
    shopInfo.value = {
      logo: '/static/images/default-shop.png',
      name: '示例店铺 ' + shopId,
      id: shopId
    };
  }, 500);
};

const loadMerchantData = (id) => {
  console.log("加载商家数据:", id);
  
  // 尝试从localStorage获取测试商家数据
  try {
    const merchantTestData = uni.getStorageSync('merchantTestData') || [];
    const merchant = merchantTestData.find(item => item.id === id);
    
    if (merchant) {
      isTestMerchant.value = true;
      shopInfo.value = {
        logo: merchant.logo || '/static/images/default-shop.png',
        name: merchant.shopName || "测试商家",
        id: merchant.id
      };
      memberType.value = "测试版";
      return;
    }
  } catch (e) {
    console.error("获取测试商家数据失败:", e);
  }
  
  // 如果没有找到测试商家数据，使用默认测试数据
  if (id === 'test') {
    isTestMerchant.value = true;
    shopInfo.value.name = "内部测试商家";
    memberType.value = "测试版";
  }
};

const getCelebrateIcon = () => {
  // 模拟从远程获取图标
  setTimeout(() => {
    // celebrateIcon.value = 'https://example.com/path/to/celebrate-icon.png';
  }, 1000);
};
</script>

<style lang="scss">
.success-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fc;
}

/* 顶部渐变背景 */
.top-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30vh;
  background: linear-gradient(135deg, #0046B3, #1677FF);
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  z-index: 0;
}

/* 导航栏 */
.navbar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  position: relative;
  z-index: 1;
}

.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.navbar-right {
  width: 44px;
}

/* 内容区域 */
.content {
  flex: 1;
  position: relative;
  z-index: 1;
  padding: 0 20px 30px;
}

/* 成功信息 */
.success-info {
  margin-top: 20px;
  text-align: center;
  position: relative;
  z-index: 5;
}

.success-message-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 24px;
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 12px 24px -10px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.5deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
  position: relative;
}

.success-message-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.6) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.success-message-icon {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  flex-shrink: 0;
}

.success-message-content {
  flex: 1;
  text-align: left;
}

.success-message-title {
  font-size: 18px;
  font-weight: bold;
  color: #0052CC;
  margin-bottom: 4px;
}

.success-message-subtitle {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.shop-info {
  background-color: #fff;
  border-radius: 24px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 20px rgba(0, 0, 0, 0.1), 
    0 15px 30px -12px rgba(0, 82, 204, 0.2);
  transform: perspective(1000px) translateZ(0) rotateX(1deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}

.shop-info::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.6) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.shop-info::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  pointer-events: none;
}

.shop-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #f0f4ff;
  margin-bottom: 12px;
  background-color: #f8f8f8;
  box-shadow: 0 4px 10px rgba(0, 82, 204, 0.15);
}

.shop-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.shop-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.shop-verify-tip {
  display: flex;
  align-items: center;
  background-color: #f0f8ff;
  padding: 4px 12px;
  border-radius: 20px;
}

.verify-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.verify-text {
  font-size: 14px;
  color: #0052CC;
}

.verify-btn {
  font-size: 14px;
  color: #0052CC;
  margin-left: 4px;
  cursor: pointer;
}

.shop-verified {
  display: flex;
  align-items: center;
  background-color: #f0f8ff;
  padding: 4px 12px;
  border-radius: 20px;
}

.verified-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.verified-text {
  font-size: 14px;
  color: #0052CC;
}

/* 数据概览 */
.data-overview {
  margin-top: 20px;
}

.data-card {
  background-color: #fff;
  border-radius: 24px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  position: relative;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 12px 24px -10px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.5deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}

.data-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.6) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.data-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.data-num {
  font-size: 18px;
  font-weight: bold;
  color: #0052CC;
  margin-bottom: 4px;
}

.data-label {
  font-size: 12px;
  color: #666;
}

.data-divider {
  width: 1px;
  background-color: #eee;
  margin: 0 10px;
}

/* 功能入口 */
.function-section {
  margin-top: 24px;
}

.function-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.function-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.function-item {
  width: 25%;
  padding: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.function-icon-bg {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.bg-blue {
  background-color: #e6f0ff;
}

.bg-orange {
  background-color: #fff2e6;
}

.bg-green {
  background-color: #e6fff0;
}

.bg-purple {
  background-color: #f0e6ff;
}

.function-icon {
  width: 28px;
  height: 28px;
}

.function-name {
  font-size: 14px;
  color: #333;
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
}

.primary-btn {
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #FFFFFF;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.secondary-btn {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  border: 1px solid rgba(0, 122, 255, 0.2);
}

/* 下一步提示 */
.next-steps {
  margin-top: 30px;
  background-color: #fff;
  border-radius: 24px;
  padding: 20px;
  position: relative;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 16px rgba(0, 0, 0, 0.08),
    0 12px 20px -8px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.8deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}

.next-steps::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.5) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.next-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.step-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.step-item:last-child {
  border-bottom: none;
}

.step-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.step-num {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #0052CC;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.step-text {
  font-size: 14px;
  color: #333;
}

.step-right {
  display: flex;
  align-items: center;
}

.go-text {
  font-size: 14px;
  color: #0052CC;
  margin-right: 4px;
}

.go-icon {
  width: 16px;
  height: 16px;
  transform: rotate(90deg);
}

/* 联系客服 */
.contact-section {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.contact-btn {
  width: 140px;
  height: 36px;
  line-height: 36px;
  background: linear-gradient(135deg, #2FB8FF, #0076FF);
  color: #fff;
  font-size: 14px;
  border-radius: 18px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 118, 255, 0.25);
  position: relative;
  overflow: hidden;
}

.contact-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: 18px;
}

/* 会员信息 */
.membership-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
}

.membership-badge {
  padding: 6rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(135deg, #1677ff, #0052cc);
  box-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.2);
}

.free-badge {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}

.basic-badge {
  background: linear-gradient(135deg, #1677ff, #0052cc);
}

.premium-badge {
  background: linear-gradient(135deg, #fa8c16, #d46b08);
}

.deluxe-badge {
  background: linear-gradient(135deg, #722ed1, #531dab);
}

.test-badge {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
}

.test-mark {
  position: absolute;
  top: -6px;
  right: -10px;
  background-color: #ff9800;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  transform: scale(0.85);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.membership-date {
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #666;
}

/* 认证好处卡片 */
.verify-benefits {
  margin-top: 20px;
  background-color: #fff;
  border-radius: 24px;
  padding: 20px;
  position: relative;
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.05),
    0 8px 16px rgba(0, 0, 0, 0.08),
    0 12px 20px -8px rgba(0, 82, 204, 0.15);
  transform: perspective(1000px) translateZ(0) rotateX(0.7deg);
  transform-style: preserve-3d;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: transform 0.3s ease;
}

.verify-benefits::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.5) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.verify-benefits-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.verify-benefit-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.verify-benefits-list {
  margin-bottom: 16px;
}

.verify-benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.benefit-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #1677FF;
  margin-right: 8px;
}

.benefit-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.verify-now-btn {
  background: linear-gradient(135deg, #1677FF, #0052CC);
  color: #fff;
  font-size: 16px;
  height: 40px;
  border-radius: 20px;
  margin-top: 10px;
  box-shadow: 0 4px 8px rgba(0, 82, 204, 0.2);
}

/* 重新设计店铺推广样式 */
.promotion-section {
  padding: 0;
  margin: 30rpx 30rpx 40rpx;
}

.promotion-card {
  background-color: #fff;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.card-header {
  padding: 16px 16px 12px;
  background-color: #FFFFFF;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
}

.card-subtitle {
  font-size: 12px;
  color: #666;
  display: block;
  margin-top: 4px;
}

.promotion-tabs {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #FFFFFF;
  padding: 0 30px;
}

/* 紧凑卡片样式 */
.compact-card {
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.compact-card .card-header {
  padding: 12px 16px 8px;
}

.compact-card .card-title {
  font-size: 15px;
}

.compact-card .card-subtitle {
  font-size: 11px;
  margin-top: 2px;
}

.compact-card .promotion-tabs {
  padding: 0 20px;
}

.compact-card .promotion-tab {
  padding: 8px 0;
}

.compact-card .promotion-content {
  padding: 12px;
}

.promotion-tab {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  position: relative;
  transition: all 0.3s ease;
  max-width: 120px;
  margin: 0 auto;
}

.promotion-tab.active {
  position: relative;
  border-bottom: none;
}

.promotion-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: linear-gradient(90deg, rgba(22, 119, 255, 0.7), #1677FF, rgba(22, 119, 255, 0.7));
  border-radius: 3px 3px 0 0;
  box-shadow: 0 1px 3px rgba(22, 119, 255, 0.2);
}

.promotion-tab-text {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s;
  position: relative;
  display: inline-block;
}

.promotion-tab.active .promotion-tab-text {
  color: #1677FF;
  font-weight: 600;
}

/* 紧凑选项卡样式 */
.compact-card .promotion-tab {
  padding: 8px 0;
}

.compact-card .promotion-tab-text {
  font-size: 13px;
}

.compact-card .promotion-tab.active::after {
  width: 16px;
  height: 2px;
}

.promotion-content {
  padding: 16px;
  background-color: #FFFFFF;
}

.tab-content-animation {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>