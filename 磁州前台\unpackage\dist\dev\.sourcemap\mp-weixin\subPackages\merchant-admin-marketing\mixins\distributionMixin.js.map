{"version": 3, "file": "distributionMixin.js", "sources": ["subPackages/merchant-admin-marketing/mixins/distributionMixin.js"], "sourcesContent": ["/**\r\n * 分销功能混入\r\n * 为使用Options API的组件提供分销相关功能\r\n */\r\nimport { distributionService } from '/subPackages/merchant-admin-marketing/services/distributionService';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      hasMerchantDistribution: false, // 商家是否开通分销功能\r\n      distributionSettings: {\r\n        enabled: false,\r\n        commissionMode: 'percentage', // 'percentage'百分比 或 'fixed'固定金额\r\n        commissions: {\r\n          level1: '',\r\n          level2: '',\r\n          level3: ''\r\n        },\r\n        enableLevel3: false\r\n      }\r\n    };\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 检查商家是否开通分销功能\r\n     */\r\n    async checkMerchantDistribution() {\r\n      try {\r\n        // 获取商家信息\r\n        const merchantInfo = this.$store.state.merchant.merchantInfo || {};\r\n        \r\n        // 检查是否开通分销功能\r\n        this.hasMerchantDistribution = await distributionService.checkMerchantDistribution(merchantInfo);\r\n        \r\n        // 如果开通了分销功能，获取默认设置\r\n        if (this.hasMerchantDistribution) {\r\n          const settings = await distributionService.getMerchantDistributionSettings(merchantInfo);\r\n          this.distributionSettings = settings;\r\n        }\r\n        \r\n        return this.hasMerchantDistribution;\r\n      } catch (error) {\r\n        console.error('检查分销功能失败', error);\r\n        this.hasMerchantDistribution = false;\r\n        return false;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 更新分销设置\r\n     * @param {Object} settings - 分销设置\r\n     */\r\n    updateDistributionSettings(settings) {\r\n      this.distributionSettings = settings;\r\n    },\r\n    \r\n    /**\r\n     * 保存活动分销设置\r\n     * @param {string} activityType - 活动类型\r\n     * @param {string} activityId - 活动ID\r\n     * @returns {Promise<boolean>} 是否保存成功\r\n     */\r\n    async saveActivityDistributionSettings(activityType, activityId) {\r\n      if (!this.hasMerchantDistribution || !this.distributionSettings.enabled) {\r\n        return true; // 如果未启用分销，直接返回成功\r\n      }\r\n      \r\n      try {\r\n        // 验证分销设置\r\n        const { valid, errors } = distributionService.validateDistributionSettings(this.distributionSettings);\r\n        if (!valid) {\r\n          uni.showToast({\r\n            title: errors[0],\r\n            icon: 'none'\r\n          });\r\n          return false;\r\n        }\r\n        \r\n        // 保存分销设置\r\n        const success = await distributionService.saveActivityDistributionSettings(\r\n          activityType, \r\n          activityId, \r\n          this.distributionSettings\r\n        );\r\n        \r\n        if (!success) {\r\n          uni.showToast({\r\n            title: '保存分销设置失败',\r\n            icon: 'none'\r\n          });\r\n          return false;\r\n        }\r\n        \r\n        return true;\r\n      } catch (error) {\r\n        console.error('保存分销设置失败', error);\r\n        uni.showToast({\r\n          title: '保存分销设置失败',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    // 组件挂载时检查分销功能\r\n    this.checkMerchantDistribution();\r\n  }\r\n}; "], "names": ["distributionService", "uni"], "mappings": ";;;AAMA,MAAe,oBAAA;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,yBAAyB;AAAA;AAAA,MACzB,sBAAsB;AAAA,QACpB,SAAS;AAAA,QACT,gBAAgB;AAAA;AAAA,QAChB,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD,cAAc;AAAA,MACf;AAAA,IACP;AAAA,EACG;AAAA,EAED,SAAS;AAAA;AAAA;AAAA;AAAA,IAIP,MAAM,4BAA4B;AAChC,UAAI;AAEF,cAAM,eAAe,KAAK,OAAO,MAAM,SAAS,gBAAgB;AAGhE,aAAK,0BAA0B,MAAMA,gEAAmB,oBAAC,0BAA0B,YAAY;AAG/F,YAAI,KAAK,yBAAyB;AAChC,gBAAM,WAAW,MAAMA,gEAAAA,oBAAoB,gCAAgC,YAAY;AACvF,eAAK,uBAAuB;AAAA,QAC7B;AAED,eAAO,KAAK;AAAA,MACb,SAAQ,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,0EAAA,YAAY,KAAK;AAC/B,aAAK,0BAA0B;AAC/B,eAAO;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,2BAA2B,UAAU;AACnC,WAAK,uBAAuB;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQD,MAAM,iCAAiC,cAAc,YAAY;AAC/D,UAAI,CAAC,KAAK,2BAA2B,CAAC,KAAK,qBAAqB,SAAS;AACvE,eAAO;AAAA,MACR;AAED,UAAI;AAEF,cAAM,EAAE,OAAO,OAAQ,IAAGD,gEAAmB,oBAAC,6BAA6B,KAAK,oBAAoB;AACpG,YAAI,CAAC,OAAO;AACVC,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,CAAC;AAAA,YACf,MAAM;AAAA,UAClB,CAAW;AACD,iBAAO;AAAA,QACR;AAGD,cAAM,UAAU,MAAMD,gEAAAA,oBAAoB;AAAA,UACxC;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACf;AAEQ,YAAI,CAAC,SAAS;AACZC,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AACD,iBAAO;AAAA,QACR;AAED,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,0EAAA,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AACD,eAAO;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EAED,UAAU;AAER,SAAK,0BAAyB;AAAA,EAC/B;AACH;;"}