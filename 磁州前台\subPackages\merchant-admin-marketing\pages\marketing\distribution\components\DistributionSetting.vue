<template>
  <view class="distribution-setting-container">
    <view class="section-header">
      <text class="section-title">分销设置</text>
      <text class="section-subtitle">设置此活动的分销规则和佣金</text>
    </view>
    
    <!-- 启用分销开关 -->
    <view class="switch-item">
      <view class="switch-label">
        <text class="label-text">启用分销</text>
        <text class="label-desc">开启后，分销员可推广此活动获得佣金</text>
      </view>
      <switch 
        :checked="distributionEnabled" 
        color="#6B0FBE" 
        @change="toggleDistribution"
      />
    </view>
    
    <!-- 分销设置内容 -->
    <view class="distribution-content" v-if="distributionEnabled">
      <!-- 佣金模式选择 -->
      <view class="mode-options">
        <view 
          class="mode-option" 
          :class="{ 'active': commissionMode === 'percentage' }"
          @click="updateCommissionMode('percentage')"
        >
          <view class="option-icon percentage">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M19 5L5 19" />
              <circle cx="6.5" cy="6.5" r="2.5" />
              <circle cx="17.5" cy="17.5" r="2.5" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">按比例计算</text>
            <text class="option-desc">按商品售价的百分比计算佣金</text>
          </view>
        </view>
        
        <view 
          class="mode-option" 
          :class="{ 'active': commissionMode === 'fixed' }"
          @click="updateCommissionMode('fixed')"
        >
          <view class="option-icon fixed">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H7" />
            </svg>
          </view>
          <view class="option-content">
            <text class="option-title">固定金额</text>
            <text class="option-desc">每件商品固定金额佣金</text>
          </view>
        </view>
      </view>
      
      <!-- 佣金设置 -->
      <view class="commission-levels">
        <view class="level-item">
          <view class="level-header">
            <view class="level-icon level1"></view>
            <text class="level-name">一级分销</text>
          </view>
          <view class="level-input-wrap">
            <input 
              class="level-input" 
              type="digit" 
              v-model="level1Commission" 
              :placeholder="commissionMode === 'percentage' ? '佣金比例' : '固定金额'"
              @input="updateCommission('level1', $event)"
            />
            <text class="input-unit">{{commissionMode === 'percentage' ? '%' : '元'}}</text>
          </view>
        </view>
        
        <view class="level-item">
          <view class="level-header">
            <view class="level-icon level2"></view>
            <text class="level-name">二级分销</text>
          </view>
          <view class="level-input-wrap">
            <input 
              class="level-input" 
              type="digit" 
              v-model="level2Commission" 
              :placeholder="commissionMode === 'percentage' ? '佣金比例' : '固定金额'"
              @input="updateCommission('level2', $event)"
            />
            <text class="input-unit">{{commissionMode === 'percentage' ? '%' : '元'}}</text>
          </view>
        </view>
        
        <view class="level-item" v-if="enableLevel3">
          <view class="level-header">
            <view class="level-icon level3"></view>
            <text class="level-name">三级分销</text>
          </view>
          <view class="level-input-wrap">
            <input 
              class="level-input" 
              type="digit" 
              v-model="level3Commission" 
              :placeholder="commissionMode === 'percentage' ? '佣金比例' : '固定金额'"
              @input="updateCommission('level3', $event)"
            />
            <text class="input-unit">{{commissionMode === 'percentage' ? '%' : '元'}}</text>
          </view>
        </view>
      </view>
      
      <!-- 三级分销开关 -->
      <view class="switch-item">
        <view class="switch-label">
          <text class="label-text">启用三级分销</text>
        </view>
        <switch 
          :checked="enableLevel3" 
          color="#6B0FBE" 
          @change="toggleLevel3"
        />
      </view>
      
      <!-- 佣金提示 -->
      <view class="commission-tips">
        <view class="tip-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="#6B0FBE" stroke-width="2">
            <circle cx="12" cy="12" r="10" />
            <path d="M12 8v4M12 16h.01" />
          </svg>
        </view>
        <text class="tip-text">{{commissionMode === 'percentage' ? '佣金比例总和建议不超过30%，以保证合理利润' : '佣金金额总和建议不超过商品价格的30%'}}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DistributionSetting',
  props: {
    // 初始分销设置
    initialSettings: {
      type: Object,
      default: () => ({
        enabled: false,
        commissionMode: 'percentage', // 'percentage' 或 'fixed'
        commissions: {
          level1: '',
          level2: '',
          level3: ''
        },
        enableLevel3: false
      })
    },
    // 是否禁用编辑
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      distributionEnabled: this.initialSettings.enabled,
      commissionMode: this.initialSettings.commissionMode,
      level1Commission: this.initialSettings.commissions.level1,
      level2Commission: this.initialSettings.commissions.level2,
      level3Commission: this.initialSettings.commissions.level3,
      enableLevel3: this.initialSettings.enableLevel3
    }
  },
  watch: {
    initialSettings: {
      handler(newVal) {
        this.distributionEnabled = newVal.enabled;
        this.commissionMode = newVal.commissionMode;
        this.level1Commission = newVal.commissions.level1;
        this.level2Commission = newVal.commissions.level2;
        this.level3Commission = newVal.commissions.level3;
        this.enableLevel3 = newVal.enableLevel3;
      },
      deep: true
    }
  },
  methods: {
    // 切换是否启用分销
    toggleDistribution(e) {
      this.distributionEnabled = e.detail.value;
      this.emitUpdate();
    },
    
    // 更新佣金模式
    updateCommissionMode(mode) {
      if (this.disabled) return;
      this.commissionMode = mode;
      this.emitUpdate();
    },
    
    // 更新佣金值
    updateCommission(level, e) {
      if (this.disabled) return;
      this[`${level}Commission`] = e.detail.value;
      this.emitUpdate();
    },
    
    // 切换是否启用三级分销
    toggleLevel3(e) {
      if (this.disabled) return;
      this.enableLevel3 = e.detail.value;
      this.emitUpdate();
    },
    
    // 向父组件发送更新事件
    emitUpdate() {
      this.$emit('update', {
        enabled: this.distributionEnabled,
        commissionMode: this.commissionMode,
        commissions: {
          level1: this.level1Commission,
          level2: this.level2Commission,
          level3: this.level3Commission
        },
        enableLevel3: this.enableLevel3
      });
    }
  }
}
</script>

<style lang="scss">
.distribution-setting-container {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.section-subtitle {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}

.switch-label {
  flex: 1;
}

.label-text {
  font-size: 14px;
  color: #333333;
}

.label-desc {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
  display: block;
}

.distribution-content {
  margin-top: 16px;
}

.mode-options {
  display: flex;
  margin-bottom: 20px;
}

.mode-option {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  margin-right: 12px;
  transition: all 0.3s ease;
}

.mode-option:last-child {
  margin-right: 0;
}

.mode-option.active {
  border-color: #6B0FBE;
  background-color: rgba(107, 15, 190, 0.05);
}

.option-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.option-icon.percentage {
  background-color: rgba(107, 15, 190, 0.1);
  color: #6B0FBE;
}

.option-icon.fixed {
  background-color: rgba(107, 15, 190, 0.1);
  color: #6B0FBE;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
}

.option-desc {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
}

.commission-levels {
  margin-bottom: 16px;
}

.level-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}

.level-header {
  display: flex;
  align-items: center;
}

.level-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  position: relative;
}

.level-icon.level1 {
  background-color: #FF9500;
}

.level-icon.level1::after {
  content: '1';
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
}

.level-icon.level2 {
  background-color: #34C759;
}

.level-icon.level2::after {
  content: '2';
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
}

.level-icon.level3 {
  background-color: #007AFF;
}

.level-icon.level3::after {
  content: '3';
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
}

.level-name {
  font-size: 14px;
  color: #333333;
}

.level-input-wrap {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 4px;
  padding: 0 12px;
  height: 36px;
}

.level-input {
  width: 80px;
  height: 36px;
  font-size: 14px;
  text-align: right;
}

.input-unit {
  font-size: 14px;
  color: #999999;
  margin-left: 4px;
}

.commission-tips {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  background-color: rgba(107, 15, 190, 0.05);
  border-radius: 8px;
  margin-top: 16px;
}

.tip-icon {
  margin-right: 8px;
  margin-top: 2px;
}

.tip-text {
  font-size: 12px;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}
</style> 