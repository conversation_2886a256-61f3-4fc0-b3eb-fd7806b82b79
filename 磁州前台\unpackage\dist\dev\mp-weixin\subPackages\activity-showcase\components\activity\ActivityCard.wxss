
/* 活动卡片基础样式 - 苹果风格 */
.activity-card.data-v-456ec6cc {
  position: relative;
  margin: 20rpx;
  border-radius: 35rpx;
  background-color: #ffffff;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08), 0 2rpx 10rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 分销标识 - 新设计（左下角斜角标签） */
.distribution-badge-new.data-v-456ec6cc {
  position: absolute;
  left: 0;
  bottom: 60rpx;
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  padding: 6rpx 16rpx;
  border-top-right-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);
  z-index: 10;
  display: flex;
  align-items: center;
}
.distribution-badge-inner.data-v-456ec6cc {
  display: flex;
  align-items: center;
}
.distribution-icon.data-v-456ec6cc {
  margin-right: 6rpx;
  color: #FFFFFF;
}
.distribution-text.data-v-456ec6cc {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 活动卡片悬停效果 */
.activity-card.data-v-456ec6cc:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.06);
}

/* 已结束活动样式 */
.activity-ended.data-v-456ec6cc {
  opacity: 0.7;
}

/* 活动类型标签 - 移至左上角 */
.activity-type-tag.data-v-456ec6cc {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
}
.type-groupBuy.data-v-456ec6cc {
  background-color: rgba(52, 199, 89, 0.9);
  color: #ffffff;
}
.type-flashSale.data-v-456ec6cc {
  background-color: rgba(255, 59, 48, 0.9);
  color: #ffffff;
}
.type-coupon.data-v-456ec6cc {
  background-color: rgba(255, 149, 0, 0.9);
  color: #ffffff;
}
.type-discount.data-v-456ec6cc {
  background-color: rgba(90, 200, 250, 0.9);
  color: #ffffff;
}
.type-text.data-v-456ec6cc {
  font-size: 22rpx;
  font-weight: 500;
}

/* 置顶标签样式 */
.top-badge.data-v-456ec6cc {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-bottom-left-radius: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}
.paid-badge.data-v-456ec6cc {
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  box-shadow: 0 8rpx 20rpx rgba(255, 59, 48, 0.2), 0 4rpx 8rpx rgba(255, 59, 48, 0.1);
}
.ad-badge.data-v-456ec6cc {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.2), 0 4rpx 8rpx rgba(0, 122, 255, 0.1);
}
.top-badge-icon.data-v-456ec6cc {
  color: #FFFFFF;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.top-badge-text.data-v-456ec6cc {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 活动封面 */
.activity-cover-container.data-v-456ec6cc {
  position: relative;
  width: 100%;
  height: 300rpx;
  overflow: hidden;
}
.activity-cover.data-v-456ec6cc {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}
.activity-card:active .activity-cover.data-v-456ec6cc {
  transform: scale(1.05);
}

/* 收藏按钮 */
.favorite-btn.data-v-456ec6cc {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff3b30;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* 热门标签 */
.activity-hot.data-v-456ec6cc {
  position: absolute;
  left: 20rpx;
  bottom: 20rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: rgba(255, 59, 48, 0.9);
  color: #ffffff;
  display: flex;
  align-items: center;
  z-index: 10;
}
.hot-icon.data-v-456ec6cc {
  margin-right: 6rpx;
}
.hot-text.data-v-456ec6cc {
  font-size: 22rpx;
  font-weight: 500;
}

/* 活动内容区 */
.activity-content.data-v-456ec6cc {
  padding: 24rpx;
}

/* 活动标题 */
.activity-title-row.data-v-456ec6cc {
  margin-bottom: 16rpx;
}
.activity-title.data-v-456ec6cc {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 活动时间 */
.activity-time.data-v-456ec6cc {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.time-icon.data-v-456ec6cc {
  margin-right: 8rpx;
  color: #8e8e93;
}
.time-text.data-v-456ec6cc {
  font-size: 24rpx;
  color: #8e8e93;
}

/* 活动地点 */
.activity-location.data-v-456ec6cc {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.location-icon.data-v-456ec6cc {
  margin-right: 8rpx;
  color: #8e8e93;
}
.location-text.data-v-456ec6cc {
  font-size: 24rpx;
  color: #8e8e93;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90%;
}

/* 活动特定信息区域 */
.activity-special-info.data-v-456ec6cc {
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 20rpx;
}

/* 拼团活动特定样式 */
.group-buy-info.data-v-456ec6cc {
  display: flex;
  flex-direction: column;
}
.price-info.data-v-456ec6cc {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}
.current-price.data-v-456ec6cc {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff3b30;
  margin-right: 12rpx;
}
.original-price.data-v-456ec6cc {
  font-size: 24rpx;
  color: #8e8e93;
  text-decoration: line-through;
}
.discount.data-v-456ec6cc {
  font-size: 24rpx;
  color: #ff3b30;
  margin-left: 12rpx;
  padding: 2rpx 8rpx;
  background-color: rgba(255, 59, 48, 0.1);
  border-radius: 10rpx;
}
.group-progress.data-v-456ec6cc {
  margin-top: 12rpx;
}
.progress-bar.data-v-456ec6cc {
  height: 10rpx;
  background-color: #e5e5ea;
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}
.progress-inner.data-v-456ec6cc {
  height: 100%;
  background-color: #34c759;
  border-radius: 5rpx;
}
.progress-text.data-v-456ec6cc {
  font-size: 22rpx;
  color: #34c759;
}

/* 秒杀活动特定样式 */
.flash-sale-info.data-v-456ec6cc {
  display: flex;
  flex-direction: column;
}
.countdown.data-v-456ec6cc {
  display: flex;
  align-items: center;
  margin: 12rpx 0;
}
.countdown-label.data-v-456ec6cc {
  font-size: 24rpx;
  color: #8e8e93;
  margin-right: 12rpx;
}
.countdown-time.data-v-456ec6cc {
  display: flex;
  align-items: center;
}
.time-block.data-v-456ec6cc {
  width: 40rpx;
  height: 40rpx;
  background-color: #1c1c1e;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
}
.time-separator.data-v-456ec6cc {
  margin: 0 6rpx;
  color: #1c1c1e;
  font-weight: 600;
}
.stock-info.data-v-456ec6cc {
  margin-top: 12rpx;
}
.stock-progress.data-v-456ec6cc {
  height: 10rpx;
  background-color: #e5e5ea;
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}
.stock-text.data-v-456ec6cc {
  font-size: 22rpx;
  color: #ff3b30;
}

/* 优惠券活动特定样式 */
.coupon-info.data-v-456ec6cc {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx;
  background: linear-gradient(135deg, #ff9500, #ff3b30);
  border-radius: 16rpx;
  color: #ffffff;
}
.coupon-value.data-v-456ec6cc {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}
.value-symbol.data-v-456ec6cc {
  font-size: 24rpx;
  font-weight: 500;
}
.value-number.data-v-456ec6cc {
  font-size: 48rpx;
  font-weight: 700;
}
.value-unit.data-v-456ec6cc {
  font-size: 24rpx;
  font-weight: 500;
  margin-left: 4rpx;
}
.coupon-condition.data-v-456ec6cc {
  margin-bottom: 8rpx;
}
.condition-text.data-v-456ec6cc {
  font-size: 22rpx;
}
.coupon-validity.data-v-456ec6cc {
  font-size: 20rpx;
  opacity: 0.8;
}

/* 满减活动特定样式 */
.discount-info.data-v-456ec6cc {
  display: flex;
  flex-direction: column;
}
.discount-rules.data-v-456ec6cc {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 12rpx;
}
.rule-item.data-v-456ec6cc {
  padding: 4rpx 12rpx;
  background-color: rgba(90, 200, 250, 0.1);
  border-radius: 10rpx;
  border: 1rpx solid rgba(90, 200, 250, 0.3);
}
.rule-text.data-v-456ec6cc {
  font-size: 22rpx;
  color: #5ac8fa;
}
.merchant-count.data-v-456ec6cc {
  margin-top: 8rpx;
}
.merchant-text.data-v-456ec6cc {
  font-size: 22rpx;
  color: #8e8e93;
}

/* 活动参与信息 */
.activity-participation.data-v-456ec6cc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.participants.data-v-456ec6cc {
  display: flex;
  align-items: center;
}
.avatar-group.data-v-456ec6cc {
  display: flex;
  margin-right: 12rpx;
}
.participant-avatar.data-v-456ec6cc {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  border: 2rpx solid #ffffff;
  margin-left: -15rpx;
}
.participant-avatar.data-v-456ec6cc:first-child {
  margin-left: 0;
}
.more-participants.data-v-456ec6cc {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #f2f2f7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -15rpx;
  border: 2rpx solid #ffffff;
}
.more-participants text.data-v-456ec6cc {
  font-size: 18rpx;
  color: #8e8e93;
}
.participant-count.data-v-456ec6cc {
  font-size: 24rpx;
  color: #8e8e93;
}

/* 分销收益标识 - 新设计 */
.distribution-profit-new.data-v-456ec6cc {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #FF9500, #FF3B30);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.15);
  margin-right: 15rpx;
}
.profit-icon.data-v-456ec6cc {
  color: #FFFFFF;
  margin-right: 6rpx;
}
.profit-text-new.data-v-456ec6cc {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 600;
}

/* 活动操作按钮 */
.activity-action.data-v-456ec6cc {
  display: flex;
}
.action-btn.data-v-456ec6cc {
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}
.btn-groupBuy.data-v-456ec6cc {
  background-color: #34c759;
  color: #ffffff;
}
.btn-flashSale.data-v-456ec6cc {
  background-color: #ff3b30;
  color: #ffffff;
}
.btn-coupon.data-v-456ec6cc {
  background-color: #ff9500;
  color: #ffffff;
}
.btn-discount.data-v-456ec6cc {
  background-color: #5ac8fa;
  color: #ffffff;
}
.btn-disabled.data-v-456ec6cc {
  background-color: #e5e5ea;
  color: #8e8e93;
}
.action-text.data-v-456ec6cc {
  font-size: 26rpx;
}

/* 活动类型特定卡片样式 */
.activity-type-groupBuy.data-v-456ec6cc {
  border-left: 6rpx solid #34c759;
}
.activity-type-flashSale.data-v-456ec6cc {
  border-left: 6rpx solid #ff3b30;
}
.activity-type-coupon.data-v-456ec6cc {
  border-left: 6rpx solid #ff9500;
}
.activity-type-discount.data-v-456ec6cc {
  border-left: 6rpx solid #5ac8fa;
}
