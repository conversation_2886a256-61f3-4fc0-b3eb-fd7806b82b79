<template>
  <view class="merchant-dashboard">
    <!-- 状态栏背景 -->
    <view class="status-bar-bg"></view>
    
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-icon" @tap="goBack"></view>
        </view>
      <text class="navbar-title">商家中心</text>
      <view class="navbar-right"></view>
    </view>

    <!-- 主内容区域 -->
    <view class="content-container">
      <!-- 业绩概览卡片 - 更精致的设计 -->
      <view class="performance-card">
        <view class="performance-header">
          <view class="performance-title">
            <view class="greeting-container">
            <text class="greeting">您好，{{userInfo.name}}</text>
              <svg class="greeting-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 17V17.01M12 13.5C12 12.12 13.12 11 14.5 11C15.88 11 17 12.12 17 13.5C17 14.88 15.88 16 14.5 16H14.475C14.4558 15.9997 14.437 15.9948 14.4201 15.9858C14.4032 15.9768 14.3886 15.9639 14.3777 15.9482C14.3667 15.9325 14.3598 15.9144 14.3573 15.8955C14.3549 15.8766 14.357 15.8573 14.364 15.84L15 13.5H14.5C13.12 13.5 12 12.38 12 11C12 9.62 13.12 8.5 14.5 8.5C15.88 8.5 17 9.62 17 11V11.5" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <text class="date">{{currentDate}}</text>
          </view>
          <view class="merchant-badge">
            <svg class="merchant-badge-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 15C15.866 15 19 11.866 19 8C19 4.13401 15.866 1 12 1C8.13401 1 5 4.13401 5 8C5 11.866 8.13401 15 12 15Z" fill="white" fill-opacity="0.2"/>
              <path d="M12 15C15.866 15 19 11.866 19 8C19 4.13401 15.866 1 12 1C8.13401 1 5 4.13401 5 8C5 11.866 8.13401 15 12 15Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M8.21 13.89L7 23L12 20L17 23L15.79 13.88" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text>{{merchantInfo.level}}</text>
      </view>
    </view>

        <view class="performance-metrics">
          <view class="metric-item">
            <text class="metric-value">¥{{todayIncome}}</text>
            <text class="metric-label">今日收入</text>
          </view>
          <view class="metric-divider"></view>
          <view class="metric-item">
            <text class="metric-value">{{todayOrders}}</text>
            <text class="metric-label">今日订单</text>
          </view>
          <view class="metric-divider"></view>
          <view class="metric-item">
            <text class="metric-value">{{pendingOrders}}</text>
            <text class="metric-label">待处理</text>
          </view>
        </view>
      </view>
      
      <!-- 快捷功能卡片 - 更现代的设计 -->
      <view class="quick-actions-card">
        <view class="section-header">
          <view class="overview-title">
            <svg class="overview-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7 11V7C7 5.93913 7.42143 4.92172 8.17157 4.17157C8.92172 3.42143 9.93913 3 11 3H13C14.0609 3 15.0783 3.42143 15.8284 4.17157C16.5786 4.92172 17 5.93913 17 7V11" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          <text>快捷功能</text>
        </view>
          <view class="section-action">
            <text class="action-text">自定义</text>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        
        <view class="quick-actions">
          <view 
            class="action-item" 
            v-for="(action, index) in quickActions" 
            :key="index" 
            @click.stop="handleQuickAction(action)"
          >
            <view class="action-icon-container" :class="action.icon">
              <svg v-if="action.icon === 'icon-product'" class="action-svg" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 7L12 3L4 7M20 7V17L12 21M20 7L12 11M12 21L4 17V7M12 21V11M4 7L12 11" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else-if="action.icon === 'icon-verification'" class="action-svg" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 11L12 14L22 4M21 12V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else-if="action.icon === 'icon-order'" class="action-svg" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 17H15M9 13H15M9 9H15M5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else-if="action.icon === 'icon-analysis'" class="action-svg" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 8V16M12 11V16M8 14V16M6 20H18C19.1046 20 20 19.1046 20 18V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else-if="action.icon === 'icon-customer'" class="action-svg" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else-if="action.icon === 'icon-campaign'" class="action-svg" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 17.25C11 17.25 16 14 16 9.75C16 5.5 11 5.5 11 9.75M11 17.25C11 17.25 6 14 6 9.75C6 5.5 11 5.5 11 9.75M11 17.25V21M11 9.75V3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else-if="action.icon === 'icon-setting'" class="action-svg" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <text class="action-text">{{action.name}}</text>
          </view>
        </view>
      </view>
      
      <!-- 数据概览 - 更精致的设计 -->
      <view class="business-overview">
        <view class="overview-header">
          <view class="overview-title">
            <svg class="overview-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 6L18.29 8.29L13.41 13.17L9.41 9.17L2 16.59L3.41 18L9.41 12L13.41 16L19.71 9.71L22 12V6H16Z" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text>业务概览</text>
          </view>
          <view class="tab-group">
            <view class="tab" :class="{ active: currentStatsType === 'today' }" @click="switchStatsType('today')">今日</view>
            <view class="tab" :class="{ active: currentStatsType === 'week' }" @click="switchStatsType('week')">本周</view>
            <view class="tab" :class="{ active: currentStatsType === 'month' }" @click="switchStatsType('month')">本月</view>
          </view>
        </view>
        
        <view class="overview-cards">
          <view class="overview-card" v-for="(stat, index) in currentStats" :key="index" @tap="viewStatDetail(stat)">
            <text class="card-label">{{stat.title}}</text>
            <text class="card-value">{{stat.value}}</text>
            <view class="card-trend" :class="stat.trend">
              <svg v-if="stat.trend === 'up'" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 20L12 4M12 4L18 10M12 4L6 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 4L12 20M12 20L6 14M12 20L18 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <text>{{stat.trendValue}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 智能助理 -->
      <view class="ai-assistant">
        <view class="section-header">
          <view class="overview-title">
            <svg class="overview-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3Z" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 12C9 12.5523 9.44772 13 10 13C10.5523 13 11 12.5523 11 12C11 11.4477 10.5523 11 10 11C9.44772 11 9 11.4477 9 12Z" fill="#0A84FF"/>
              <path d="M13 12C13 12.5523 13.4477 13 14 13C14.5523 13 15 12.5523 15 12C15 11.4477 14.5523 11 14 11C13.4477 11 13 11.4477 13 12Z" fill="#0A84FF"/>
              <path d="M12 18C14.5 18 16.5 16.5 17 15H7C7.5 16.5 9.5 18 12 18Z" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text>智能经营助手</text>
          </view>
          <view class="section-action" @tap="viewAssistantDetail">
            <text class="action-text">更多</text>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        
        <view class="assistant-insights">
          <view class="insight-card" @tap="handleInsight('trend')">
            <view class="insight-icon-container">
              <svg class="insight-icon" width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 6L18.29 8.29L13.41 13.17L9.41 9.17L2 16.59L3.41 18L9.41 12L13.41 16L19.71 9.71L22 12V6H16Z" stroke="#0A84FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <view class="insight-content">
              <text class="insight-title">消费趋势分析</text>
              <text class="insight-desc">近期顾客偏好变化，建议调整商品结构</text>
            </view>
            <view class="insight-action">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 18L15 12L9 6" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
          </view>
          
          <view class="insight-card warning" @tap="handleInsight('pricing')">
            <view class="insight-icon-container warning">
              <svg class="insight-icon" width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="#FF9F0A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <view class="insight-content">
              <text class="insight-title">竞品价格监测</text>
              <text class="insight-desc">同类商品市场价格下降5%，建议调整策略</text>
            </view>
            <view class="insight-action">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 18L15 12L9 6" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
          </view>
          
          <view class="insight-card opportunity" @tap="handleInsight('forecast')">
            <view class="insight-icon-container opportunity">
              <svg class="insight-icon" width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 8V16M12 16L8 12M12 16L16 12M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z" stroke="#30D158" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <view class="insight-content">
              <text class="insight-title">销售预测模型</text>
              <text class="insight-desc">根据历史数据，下周销售额预计增长12%</text>
            </view>
            <view class="insight-action">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 18L15 12L9 6" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
          </view>
        </view>
      </view>
      
        </view>
        
    <!-- 底部导航栏 -->
    <view class="tab-bar">
      <view v-for="(tab, index) in tabItems" :key="index" class="tab-item" :class="{ active: currentTab === tab.id }" @tap="switchTab(tab.id)">
        <view v-if="currentTab === tab.id" class="active-indicator"></view>
        <view :class="['tab-icon', tab.icon]"></view>
        <text class="tab-text">{{tab.text}}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';

// 用户信息
const userInfo = reactive({
        name: '张店长',
        avatar: ''
});

// 商家信息
const merchantInfo = reactive({
        location: '磁县',
        level: '钻石商家',
        rating: 4.9,
        followers: 1280
});

// 页面状态
const currentDate = ref('2023年6月18日');
const hasNotification = ref(true);
const currentStatsType = ref('today'); // 新的状态类型变量
const todayIncome = ref('12,846');
const todayOrders = ref('128');
const pendingOrders = ref('3');
const currentTab = ref(0);
      
      // 智能助手项目
const assistantItems = reactive([
        '基于AI分析的经营建议',
        '潜在问题提醒',
        '机会点识别与推荐'
]);
      
      // 快捷操作
const quickActions = reactive([
        {
          name: '活动管理',
          icon: 'icon-campaign',
          url: '/subPackages/merchant-admin/pages/activity/index'
        },
        {
          name: '发布商品',
          icon: 'icon-product',
          url: '/subPackages/merchant-admin/pages/products/create'
        },
        {
          name: '核销中心',
          icon: 'icon-verification',
          url: '/subPackages/merchant-admin-marketing/pages/marketing/verification/index'
        },
        {
          name: '数据分析',
          icon: 'icon-analysis',
          url: '/subPackages/merchant-admin/pages/analysis/index'
        },
        {
          name: '客户管理',
          icon: 'icon-customer',
          url: '/subPackages/merchant-admin-customer/pages/customer/index'
        },
        {
          name: '设置',
          icon: 'icon-setting',
          url: '/subPackages/merchant-admin/pages/settings/index'
        }
]);
      
      // 统计数据
const statsData = reactive({
        today: [
        {
          title: '今日销售额',
          value: '¥12,846',
          trendValue: '+15.2%',
            trend: 'up',
            type: 'primary',
            icon: 'sales'
        },
        {
          title: '访客数量',
          value: '1,286',
          trendValue: '+8.5%',
            trend: 'up',
            type: 'secondary',
            icon: 'visitor'
        },
        {
          title: '订单数量',
          value: '128',
          trendValue: '+12.3%',
            trend: 'up',
            type: 'success',
            icon: 'order'
        },
        {
          title: '退款金额',
          value: '¥1,234',
          trendValue: '-5.2%',
            trend: 'down',
            type: 'warning',
            icon: 'refund'
          }
        ],
        week: [
          {
            title: '本周销售额',
            value: '¥86,235',
            trendValue: '+10.8%',
            trend: 'up',
            type: 'primary',
            icon: 'sales'
          },
          {
            title: '访客数量',
            value: '8,562',
            trendValue: '+6.2%',
            trend: 'up',
            type: 'secondary',
            icon: 'visitor'
          },
          {
            title: '订单数量',
            value: '862',
            trendValue: '+9.1%',
            trend: 'up',
            type: 'success',
            icon: 'order'
          },
          {
            title: '退款金额',
            value: '¥7,823',
            trendValue: '-2.8%',
            trend: 'down',
            type: 'warning',
            icon: 'refund'
          }
        ],
        month: [
          {
            title: '本月销售额',
            value: '¥356,428',
            trendValue: '+18.5%',
            trend: 'up',
            type: 'primary',
            icon: 'sales'
          },
          {
            title: '访客数量',
            value: '35,621',
            trendValue: '+12.4%',
            trend: 'up',
            type: 'secondary',
            icon: 'visitor'
          },
          {
            title: '订单数量',
            value: '3,562',
            trendValue: '+15.7%',
            trend: 'up',
            type: 'success',
            icon: 'order'
          },
          {
            title: '退款金额',
            value: '¥28,456',
            trendValue: '-8.3%',
            trend: 'down',
            type: 'warning',
            icon: 'refund'
          }
        ]
});
      
// 待办事项
const todos = reactive([
        {
          title: '待处理订单提醒',
          time: '今天 14:00',
          completed: false,
          priority: 'high',
          type: 'order'
        },
        {
          title: '客户消息待回复',
          time: '今天 16:00',
          completed: true,
          priority: 'medium',
          type: 'message'
        },
        {
          title: '"夏季特惠"活动即将到期',
          time: '明天 10:00',
          completed: false,
          priority: 'high',
          type: 'activity'
        },
        {
          title: '商品置顶服务即将到期',
          time: '后天 18:00',
          completed: false,
          priority: 'medium',
          type: 'promotion'
        },
        {
          title: '更新商品库存',
          time: '周五 12:00',
          completed: false,
          priority: 'medium',
          type: 'inventory'
        },
        {
          title: '推广位即将到期',
          time: '周六 10:00',
          completed: false,
          priority: 'high',
          type: 'promotion'
        }
]);

// 活动管理
const activities = reactive([
        {
          title: '618购物节特惠',
          startDate: '2023-06-01',
          endDate: '2023-06-18',
          status: 'active',
          views: '2.5k',
          sales: '¥15,628',
          progress: 85
        },
        {
          title: '夏季新品上市',
          startDate: '2023-05-15',
          endDate: '2023-06-15',
          status: 'ending',
          views: '1.8k',
          sales: '¥12,386',
          progress: 95
        },
        {
          title: '会员专享折扣',
          startDate: '2023-06-10',
          endDate: '2023-06-30',
          status: 'upcoming',
          views: '856',
          sales: '¥6,245',
          progress: 0
        }
]);

// 导航项
const tabItems = reactive([
        {
          id: 0,
          icon: 'dashboard',
          text: '商家中心',
    url: '/subPackages/merchant-admin-home/pages/merchant-home/index'
        },
        {
          id: 1,
          icon: 'store',
          text: '店铺管理',
          url: '/subPackages/merchant-admin/pages/store/index'
        },
        {
          id: 2,
          icon: 'marketing',
          text: '营销中心',
    url: '/subPackages/merchant-admin-marketing/pages/marketing/index'
        },
        {
          id: 3,
          icon: 'orders',
          text: '订单管理',
          url: '/subPackages/merchant-admin-order/pages/order/index'
        },
        {
          id: 'more',
          icon: 'more',
          text: '更多',
          url: ''
  }
]);

// 计算属性
const currentStats = computed(() => {
  return statsData[currentStatsType.value] || statsData.today;
});

const pendingTodosCount = computed(() => {
  return todos.filter(todo => !todo.completed).length;
});
    
    // 底部导航栏显示的导航项（前4个核心功能 + 更多）
const visibleTabs = computed(() => {
      // 前4个核心导航项
  const mainTabs = tabItems.slice(0, 4);
      
      // 添加"更多"导航项
      mainTabs.push({
        id: 'more',
        icon: 'more',
        text: '更多',
        url: ''
      });
      
      // 不再自动替换路径，使用原始配置的路径
      // 保留原始路径，确保正确跳转
      
      return mainTabs;
});

// 方法
function getCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
  currentDate.value = `${year}年${month}月${day}日`;
}

// 移除onRefresh方法

function switchStatsType(type) {
  console.log('正在切换到:', type);
  // 更新当前选中的统计类型
  currentStatsType.value = type;
  
  // 根据不同类型显示不同的数据
  let typeText = type === 'today' ? '今日' : type === 'week' ? '本周' : '本月';
  
  // 显示切换成功的提示
  uni.showToast({
    title: '切换到' + typeText,
    icon: 'none',
    duration: 1500
  });
}

function getPriorityText(priority) {
      const map = {
        high: '紧急',
        medium: '普通',
        low: '低优'
      };
      return map[priority] || '普通';
}

function getStatusText(status) {
      const map = {
        active: '进行中',
        ending: '即将结束',
        upcoming: '未开始',
        completed: '已结束'
      };
      return map[status] || '未知';
}

function getActivityProgress(activity) {
      return activity.progress || 0;
}

function handleQuickAction(action) {
  console.log('正在跳转到:', action.url);
  
  try {
    // 检查URL是否有效
    if (!action.url) {
      throw new Error('URL不存在');
    }
    
    // 特殊处理商家中心页面
    if (action.name === '商家中心') {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-home/pages/merchant-home/index',
        fail: (err) => {
          console.error('导航失败:', err);
          uni.showToast({
            title: '页面跳转失败，请稍后再试',
            icon: 'none',
            duration: 2000
          });
        }
      });
      return;
    }
    
    // 核销中心特殊处理
    if (action.name === '核销中心') {
      console.log('跳转到核销中心:', action.url);
      uni.navigateTo({
        url: action.url,
        success: () => {
          console.log('成功跳转到核销中心');
        },
        fail: (err) => {
          console.error('核销中心跳转失败:', err);
          // 尝试使用switchTab
          uni.switchTab({
            url: action.url,
            fail: (switchErr) => {
              console.error('核销中心switchTab也失败:', switchErr);
              uni.showToast({
                title: '核销中心页面跳转失败，请稍后再试',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }
      });
      return;
    }
    
    // 使用uni.navigateTo导航到对应页面
      uni.navigateTo({
        url: action.url,
        fail: (err) => {
          console.error('导航失败:', err);
        
        // 尝试使用switchTab切换到Tab页面
        uni.switchTab({
          url: action.url,
          fail: (switchErr) => {
            console.error('switchTab也失败:', switchErr);
            
            // 显示错误提示
          uni.showToast({
              title: '页面跳转失败，请稍后再试',
            icon: 'none',
            duration: 2000
          });
        }
      });
      }
    });
  } catch (error) {
    console.error('处理快捷操作时出错:', error);
    
    // 显示错误提示
    uni.showToast({
      title: '功能正在开发中',
      icon: 'none',
      duration: 2000
    });
  }
}

function toggleTodoStatus(index) {
  todos[index].completed = !todos[index].completed;
      
      // 如果标记为完成，显示提示
  if (todos[index].completed) {
        uni.showToast({
          title: '已标记为完成',
          icon: 'success'
        });
      }
}

function viewAllTodos() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-home/pages/todos/index'
      });
}

function handleTodoAction(todo) {
      // 根据待办事项类型执行不同操作
      const actions = {
        order: {
          title: '订单管理',
          url: '/subPackages/merchant-admin-home/pages/orders/index'
        },
        message: {
          title: '消息中心',
          url: '/subPackages/merchant-admin-home/pages/messages/index'
        },
        activity: {
          title: '活动管理',
          url: '/subPackages/merchant-admin-home/pages/activity/list'
        },
        promotion: {
          title: '推广管理',
          url: '/subPackages/merchant-admin-home/pages/promotions/index'
        },
        inventory: {
          title: '库存管理',
          url: '/subPackages/merchant-admin-home/pages/inventory/index'
        }
      };
      
      const action = actions[todo.type];
      if (action) {
        uni.navigateTo({
          url: action.url,
          success: () => {
            uni.showToast({
              title: `正在进入${action.title}`,
              icon: 'none'
            });
          }
        });
      }
}



function switchTab(tabId) {
      // 处理"更多"选项
      if (tabId === 'more') {
    showMoreOptions();
        return;
      }
      
  if (tabId === currentTab.value) return;
      
  currentTab.value = tabId;
      
  // 特殊处理数据概览标签
  if (tabId === 0) {
      uni.navigateTo({
      url: '/subPackages/merchant-admin-home/pages/merchant-home/index',
      fail: (err) => {
        console.error('导航到商家中心失败:', err);
        uni.showToast({
          title: '页面跳转失败，请稍后再试',
          icon: 'none'
        });
      }
    });
    return;
  }
      
  // 尝试使用switchTab切换到tabBar页面
  uni.switchTab({
    url: tabItems[tabId].url,
    fail: (err) => {
      console.error('switchTab失败:', err);
      // 如果switchTab失败（可能是因为页面不在tabBar中），则使用navigateTo
      uni.navigateTo({
        url: tabItems[tabId].url,
        fail: (navErr) => {
          console.error('导航失败:', navErr);
          uni.showToast({
            title: '页面跳转失败，请稍后再试',
            icon: 'none'
          });
        }
      });
    }
      });
}
    
    // 显示更多选项弹出菜单
function showMoreOptions() {
      // 准备更多菜单中的选项（后3个导航项）
  const moreOptions = tabItems.slice(4).map(item => item.text);
      
      uni.showActionSheet({
        itemList: moreOptions,
        success: (res) => {
          // 计算实际的导航项索引 (4 + 用户点击的索引)
          const tabIndex = 4 + res.tapIndex;
      currentTab.value = tabItems[tabIndex].id;
          
          // 尝试使用switchTab切换到tabBar页面
          uni.switchTab({
            url: tabItems[tabIndex].url,
            fail: (err) => {
              console.error('switchTab失败:', err);
              // 如果switchTab失败（可能是因为页面不在tabBar中），则使用navigateTo
          uni.navigateTo({
                url: tabItems[tabIndex].url,
                fail: (navErr) => {
                  console.error('导航失败:', navErr);
                  uni.showToast({
                    title: '页面跳转失败，请稍后再试',
                    icon: 'none'
                  });
                }
              });
            }
          });
        }
      });
}

function showUserInfo() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-home/pages/user/profile'
      });
}

function viewAllStats() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-home/pages/statistics/index'
      });
}

function viewStatDetail(stat) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-home/pages/statistics/detail?type=${stat.type}`
      });
}

function viewActivityDetail(activity) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-home/pages/activity/detail?title=${encodeURIComponent(activity.title)}`
      });
}



function goBack() {
      uni.navigateBack();
}

function viewAssistantDetail() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-home/pages/assistant/detail'
      });
}

function handleInsight(type) {
      const actions = {
        trend: {
          title: '消费趋势分析',
          url: '/subPackages/merchant-admin-home/pages/analytics/consumer-trends'
        },
        pricing: {
          title: '竞品价格监测',
          url: '/subPackages/merchant-admin-home/pages/analytics/competitor-pricing'
        },
        forecast: {
          title: '销售预测模型',
          url: '/subPackages/merchant-admin-home/pages/analytics/sales-forecast'
        }
      };
      
      const action = actions[type];
      if (action) {
        uni.navigateTo({
          url: action.url,
          success: () => {
            uni.showToast({
              title: `正在查看${action.title}`,
              icon: 'none'
            });
          }
        });
      }
}

function viewFullReport() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-home/pages/statistics/index'
      });
}

function viewAnalyticDetail(type) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-home/pages/analytics/detail?type=${type}`
      });
}



function navigateToSettings(setting) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-home/pages/settings/detail?setting=${setting}`
      });
}

function viewAllSettings() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-home/pages/settings/index'
      });
    }

// 添加状态栏高度变量
const statusBarHeight = ref(20);
const navbarHeight = ref(64);

// 生命周期
onMounted(() => {
  getCurrentDate();
  
  // 获取系统信息并设置状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  navbarHeight.value = statusBarHeight.value + 44; // 状态栏 + 导航栏固定高度
});
</script>

<style lang="scss" scoped>
:root {
  /* 颜色系统 - 苹果风格 */
  --brand-primary: #0A84FF; /* 苹果蓝 */
  --brand-secondary: #5E5CE6; /* 苹果紫 */
  --accent-purple: #5E5CE6;
  --accent-pink: #FF2D55;
  --accent-orange: #FF9500;
  --accent-yellow: #FFCC00;
  --accent-green: #30D158;
  --accent-teal: #64D2FF;
  
  /* 中性色 */
  --bg-primary: #F2F3F7;
  --bg-secondary: #FFFFFF;
  --bg-tertiary: #FAFAFA;
  --bg-card: #FFFFFF;
  
  /* 文本颜色 */
  --text-primary: #1C1C1E;
  --text-secondary: #3A3A3C;
  --text-tertiary: #8E8E93;
  --text-quaternary: #C7C7CC;
  
  /* 边框颜色 */
  --border-light: rgba(60, 60, 67, 0.06);
  --border-medium: rgba(60, 60, 67, 0.12);
  
  /* 功能色 */
  --success: #30D158;
  --warning: #FF9F0A;
  --danger: #FF453A;
  --info: #64D2FF;
  
  /* 间距 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  
  /* 圆角 - 更新为35度圆角 */
  --radius-sm: 12px;
  --radius-md: 20px;
  --radius-lg: 35px;
  
  /* 阴影 - 更精致的阴影系统 */
  --shadow-xs: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 4px 12px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.03);
  --shadow-md: 0 8px 24px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 16px 32px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 24px 48px rgba(0, 0, 0, 0.12), 0 12px 24px rgba(0, 0, 0, 0.06);
}

/* 页面容器 */
.merchant-dashboard {
  background-color: var(--bg-primary);
  min-height: 100vh;
  padding-bottom: 100px;
  position: relative;
  overflow-x: hidden;
}

/* 状态栏背景 */
.status-bar-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--status-bar-height, 44px);
  background: #0052CC;
  z-index: 100;
}

/* 顶部导航栏 - 恢复原始样式 */
.navbar {
  position: fixed;
  top: var(--status-bar-height, 44px);
  left: 0;
  right: 0;
  height: 62px; /* 再增加高度3px，从59px到62px */
  background: #0052CC;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-left {
  position: absolute;
  left: 16px;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.navbar-title {
  color: white;
  font-size: 20px; /* 增加字体大小 */
  font-weight: 700; /* 增加字体粗细 */
  letter-spacing: 0.5px;
}

.navbar-right {
  position: absolute;
  right: 16px;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
}

/* 主内容区域 */
.content-container {
  padding: calc(var(--status-bar-height, 44px) + 62px + 16px) 16px 0; /* 调整padding，将59px改为62px */
  display: flex;
  flex-direction: column;
  gap: 16px; /* 减小卡片之间的间距 */
}

/* 业绩概览卡片 - 更精致的设计 */
.performance-card {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: var(--radius-lg);
  padding: 18px; /* 减小内边距 */
  position: relative;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  width: 90%; /* 调整宽度 */
  margin-left: auto;
  margin-right: auto;
  transform: translateZ(0);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.performance-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(46, 91, 255, 0.06) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(30%, -30%);
}

.performance-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(46, 91, 255, 0.04) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-30%, 30%);
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px; /* 减小下边距 */
  position: relative;
  z-index: 2;
}

.performance-title {
  flex: 1;
}

.greeting-container {
  display: flex;
  align-items: center;
  margin-top: 0;
}

.greeting {
  font-size: 18px; /* 减小字体大小 */
  font-weight: 700;
  margin-bottom: 2px;
  display: block;
  letter-spacing: 0.3px;
  color: var(--text-primary);
  background: linear-gradient(135deg, #1C1C1E, #3A3A3C);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.greeting-icon {
  width: 18px;
  height: 18px;
  opacity: 0.8;
  margin-left: 8px;
}

.date {
  font-size: 13px; /* 减小字体大小 */
  color: var(--text-tertiary);
  display: block;
  margin-top: 2px; /* 减小上边距 */
  font-weight: 500;
}

.merchant-badge {
  padding: 6px 12px; /* 减小内边距 */
  background: linear-gradient(135deg, #0A84FF, #5E5CE6);
  border-radius: 16px; /* 减小圆角 */
  font-size: 12px;
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(10, 132, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.merchant-badge-icon {
  width: 14px; /* 减小图标尺寸 */
  height: 14px; /* 减小图标尺寸 */
}

.performance-metrics {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 252, 0.9));
  border-radius: 24px;
  padding: 16px; /* 减小内边距 */
  position: relative;
  z-index: 2;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  margin-top: 12px; /* 减小上边距 */
}

.metric-item {
  text-align: center;
  flex: 1;
}

.metric-divider {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.06), transparent);
}

.metric-value {
  font-size: 20px; /* 减小字体大小 */
  font-weight: 700;
  margin-bottom: 4px;
  display: block;
  background: linear-gradient(135deg, #0A84FF, #5E5CE6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.metric-label {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 500;
}

/* 快捷功能卡片 - 更现代的设计 */
.quick-actions-card {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: var(--radius-lg);
  padding: 18px 16px; /* 增加左右内边距，从12px改为16px */
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  width: 90%; /* 调整宽度 */
  margin-left: auto;
  margin-right: auto;
  transform: translateZ(0);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.quick-actions-card::before {
  content: '';
  position: absolute;
  top: -10%;
  right: -10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(10, 132, 255, 0.03) 0%, transparent 70%);
  border-radius: 50%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px; /* 减小下边距 */
  padding-bottom: 12px; /* 减小内边距 */
  border-bottom: 1px solid rgba(60, 60, 67, 0.08);
}

.overview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.overview-icon {
  width: 20px;
  height: 20px;
}

.section-action {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--brand-primary);
  font-size: 14px;
  font-weight: 500;
}

.action-text {
  color: var(--brand-primary);
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px; /* 增加间距，从8px改为12px */
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 4px; /* 增加水平内边距，添加4px的左右内边距 */
  transition: all 0.3s ease;
  border-radius: 20px;
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  box-shadow: var(--shadow-xs);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative; /* 添加相对定位 */
  z-index: 1; /* 确保按钮在正确的层级 */
  min-height: 80px; /* 确保最小高度 */
  overflow: visible; /* 确保内容不会被裁剪 */
}

.action-item:active {
  transform: scale(0.96);
  box-shadow: var(--shadow-sm);
  background-color: rgba(0, 0, 0, 0.02); /* 添加点击效果 */
}

.action-icon-container {
  width: 44px; /* 减小图标容器尺寸 */
  height: 44px; /* 减小图标容器尺寸 */
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px; /* 减小下边距 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  position: relative; /* 添加相对定位 */
  z-index: 2; /* 确保图标在按钮上层 */
}

.action-svg {
  width: 20px; /* 减小图标尺寸 */
  height: 20px; /* 减小图标尺寸 */
  position: relative; /* 添加相对定位 */
  z-index: 3; /* 确保SVG在最上层 */
}

.action-text {
  font-size: 12px; /* 减小字体大小 */
  position: relative; /* 添加相对定位 */
  z-index: 2; /* 确保文本在按钮上层 */
  width: 100%; /* 确保文本宽度占满容器 */
  text-align: center; /* 文本居中 */
  padding: 0 2px; /* 添加水平内边距 */
}

/* 业务概览 - 更精致的设计 */
.business-overview {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-md); /* 减小左右内边距，确保卡片能完整显示 */
  margin-bottom: var(--space-lg);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.09), 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden; /* 修改回hidden，但确保内部元素不会超出容器 */
  border: 1px solid rgba(200, 210, 230, 0.3);
  width: 90%; /* 调整为90%，与其他卡片保持一致 */
  margin-left: auto;
  margin-right: auto;
  transform: translateZ(0); /* 启用GPU加速，增强立体感 */
  max-width: 600px; /* 调整最大宽度限制 */
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px; /* 减小内边距 */
  border-bottom: 1px solid #f0f2f5;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0; /* 添加顶部圆角 */
  overflow: hidden; /* 确保内容不会溢出 */
}

/* 统一所有模块标题样式 */
.overview-title {
  display: flex;
  align-items: center;
  font-size: 15px; /* 稍微减小字体大小 */
  font-weight: 600;
  color: var(--text-primary);
}

.overview-icon {
  width: 16px; /* 减小图标尺寸 */
  height: 16px; /* 减小图标尺寸 */
  margin-right: 6px; /* 减小右边距 */
  flex-shrink: 0;
}

.tab-group {
  display: flex;
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 2px;
  transform: scale(0.95); /* 稍微缩小标签组 */
}

.tab {
  padding: 4px 10px;
  font-size: 12px;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
}

.tab.active {
  background-color: white;
  color: var(--brand-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  background-color: #f0f2f5;
  width: 98%; /* 稍微减小宽度，确保不会超出父容器 */
  border-radius: 0 0 var(--radius-lg) var(--radius-lg); /* 添加底部圆角 */
  overflow: hidden; /* 确保内容不会溢出 */
  margin: 0 auto; /* 确保居中 */
}

.overview-card {
  background-color: white;
  padding: 12px 14px; /* 减小内边距 */
  display: flex;
  flex-direction: column;
  position: relative;
}

.card-label {
  font-size: 11px; /* 减小字体大小 */
  color: #666;
  margin-bottom: 3px; /* 减小下边距 */
}

.card-value {
  font-size: 16px; /* 减小字体大小 */
  font-weight: 600;
  color: #333;
  margin-bottom: 4px; /* 减小下边距 */
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 3px; /* 减小间距 */
  font-size: 11px; /* 减小字体大小 */
  font-weight: 500;
}

.card-trend.up {
  color: var(--success);
}

.card-trend.down {
  color: var(--danger);
}

.icon-customer {
  background: linear-gradient(135deg, #FF9F0A, #FF3B30);
}

.icon-campaign {
  background: linear-gradient(135deg, #FF2D55, #FF375F);
}

.icon-setting {
  background: linear-gradient(135deg, #8E8E93, #636366);
}

.icon-product {
  background: linear-gradient(135deg, #32D74B, #30B856);
}

.icon-verification {
  background: linear-gradient(135deg, #64D2FF, #5AC8F5);
}

.icon-analysis {
  background: linear-gradient(135deg, #BF5AF2, #A347D1);
}

.ai-assistant {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: var(--radius-lg);
  padding: 18px; /* 减小内边距 */
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  width: 90%; /* 调整宽度 */
  margin-left: auto;
  margin-right: auto;
  transform: translateZ(0);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.assistant-insights {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-card {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: 24px;
  padding: 16px; /* 减小内边距 */
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-left: 4px solid var(--brand-primary);
}

.insight-card.warning {
  border-left-color: var(--warning);
}

.insight-card.opportunity {
  border-left-color: var(--success);
}

.insight-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}

.insight-icon-container {
  width: 44px; /* 减小图标容器尺寸 */
  height: 44px; /* 减小图标容器尺寸 */
  border-radius: 16px;
  margin-right: 14px; /* 减小右边距 */
  background: rgba(10, 132, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-card .insight-icon-container.warning {
  background: rgba(255, 159, 10, 0.1);
}

.insight-card .insight-icon-container.opportunity {
  background: rgba(48, 209, 88, 0.1);
}

.insight-icon {
  width: 24px; /* 减小图标尺寸 */
  height: 24px; /* 减小图标尺寸 */
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 14px; /* 减小字体大小 */
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px; /* 减小下边距 */
  display: block;
}

.insight-desc {
  font-size: 12px; /* 减小字体大小 */
  color: var(--text-tertiary);
  line-height: 1.4;
}

.insight-action {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  flex-shrink: 0;
}

/* 底部导航栏 - 恢复原始样式 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 56px;
  background-color: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #F0F0F0;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 100;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}

.tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}

.tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}

.tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}

.tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

  .tab-text {
  font-size: 10px;
  color: #999999;
}

.tab-item.active .tab-icon {
  color: #0052CC;
}

.tab-item.active .tab-text {
  color: #0052CC;
}

.active-indicator {
  position: absolute;
  top: 0;
  width: 20px;
  height: 3px;
  background-color: #0052CC;
  border-radius: 1.5px;
}

/* 营销中心与其他图标保持一致的颜色 */
.tab-item.active[data-tab="marketing"] .tab-icon {
  color: #0052CC;
}

.tab-item.active[data-tab="marketing"] .tab-text {
  color: #0052CC;
}

.tab-item.active[data-tab="marketing"] .active-indicator {
  background-color: #0052CC;
}



.simple-chart.wide {
  position: relative;
  height: 280px;
  margin-bottom: 30px;
  padding: 0;
  background: #fff;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  width: 100%;
}
.trend-svg {
  width: 100%;
  height: 100%;
  display: block;
}
.y-axis.wide {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 25px;
  width: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
  pointer-events: none;
}
.y-axis-label {
  font-size: 13px;
  color: #999;
  text-align: right;
  padding-right: 10px;
  transform: translateY(-50%);
}
.x-axis.wide {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 25px;
  z-index: 2;
}
.x-axis-label {
  position: absolute;
  font-size: 13px;
  color: #999;
  transform: translateX(-50%);
  text-align: center;
  bottom: 0;
}
.chart-legend {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-lg);
}
.legend-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--text-secondary);
}
.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--space-sm);
}
.legend-dot.sales {
  background: #0A84FF;
}
.legend-dot.orders {
  background: #FF9500;
}

/* 更精致、高级的销售趋势图表样式 */
.premium-chart-container {
  background: linear-gradient(145deg, #ffffff, #f8f9fc);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin: 0 0 var(--space-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.05);
  width: 100%;
  border: 1px solid rgba(200, 210, 230, 0.3);
  position: relative;
  overflow: hidden;
}

.premium-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #0A84FF, #5E5CE6);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.premium-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid rgba(200, 210, 230, 0.3);
}

.title-section {
  display: flex;
  flex-direction: column;
}

.premium-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.premium-subtitle {
  font-size: 13px;
  color: var(--text-tertiary);
}

.premium-tabs {
  display: flex;
  background: rgba(200, 210, 230, 0.15);
  border-radius: 20px;
  padding: 2px;
}

.premium-tab {
  padding: 8px 16px;
  font-size: 13px;
  color: var(--text-tertiary);
  border-radius: 18px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.premium-tab.active {
  background: linear-gradient(90deg, #0A84FF, #5E5CE6);
  color: white;
  box-shadow: 0 4px 8px rgba(10, 132, 255, 0.2);
}

.premium-chart {
  position: relative;
  height: 280px;
  margin-bottom: var(--space-md);
  padding: 20px 20px 40px;
  background-color: #FCFCFF;
  border-radius: var(--radius-md);
  border: 1px solid rgba(200, 210, 230, 0.3);
  overflow: hidden;
}

/* 图表背景 */
.chart-background {
  position: absolute;
  left: 20px;
  right: 20px;
  top: 20px;
  bottom: 40px;
  z-index: 1;
}

.grid-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: rgba(200, 210, 230, 0.2);
}

.grid-line:nth-child(1) { top: 0%; }
.grid-line:nth-child(2) { top: 33.33%; }
.grid-line:nth-child(3) { top: 66.66%; }
.grid-line:nth-child(4) { top: 100%; }

.grid-line-vertical {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: rgba(200, 210, 230, 0.1);
}

.grid-line-vertical:nth-child(1) { left: 0%; }
.grid-line-vertical:nth-child(2) { left: 16.67%; }
.grid-line-vertical:nth-child(3) { left: 33.33%; }
.grid-line-vertical:nth-child(4) { left: 50%; }
.grid-line-vertical:nth-child(5) { left: 66.67%; }
.grid-line-vertical:nth-child(6) { left: 83.33%; }
.grid-line-vertical:nth-child(7) { left: 100%; }

/* 区域图表 */
.area-chart {
  position: absolute;
  left: 20px;
  right: 20px;
  top: 20px;
  bottom: 40px;
  z-index: 2;
}

/* 数据点 */
.data-points {
  position: absolute;
  left: 20px;
  right: 20px;
  top: 20px;
  bottom: 40px;
  z-index: 3;
}

.data-point {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.2s ease;
}

.data-point.sales {
  background-color: #0A84FF;
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.1);
}

.data-point.orders {
  background-color: #FF9500;
  box-shadow: 0 0 0 2px rgba(255, 149, 0, 0.1);
}

.active-point {
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 4;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.8);
}

.active-point.sales {
  background-color: #0A84FF;
  border: 2px solid white;
}

.active-point.orders {
  background-color: #FF9500;
  border: 2px solid white;
}

/* 提示框 */
.premium-tooltip {
  position: absolute;
  transform: translateX(-50%);
  z-index: 10;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  padding: 12px;
  min-width: 160px;
  pointer-events: none;
  border: 1px solid rgba(200, 210, 230, 0.3);
}

.premium-tooltip::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 14px;
  height: 14px;
  background: white;
  border-right: 1px solid rgba(200, 210, 230, 0.3);
  border-bottom: 1px solid rgba(200, 210, 230, 0.3);
  transform: translateX(-50%) rotate(45deg);
}

.tooltip-date {
  font-size: 12px;
  color: var(--text-tertiary);
  text-align: center;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(200, 210, 230, 0.3);
  padding-bottom: 8px;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tooltip-item {
  display: flex;
  align-items: center;
}

.tooltip-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.tooltip-item.sales .tooltip-dot {
  background-color: #0A84FF;
}

.tooltip-item.orders .tooltip-dot {
  background-color: #FF9500;
}

.tooltip-label {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-right: 8px;
}

.tooltip-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-left: auto;
}

/* 坐标轴 */
.y-axis-premium {
  position: absolute;
  left: 0;
  top: 20px;
  bottom: 40px;
  width: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
  pointer-events: none;
}

.x-axis-premium {
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 0;
  height: 40px;
  z-index: 2;
}

.y-axis-label {
  font-size: 12px;
  color: var(--text-tertiary);
  text-align: center;
  transform: translateY(-50%);
}

.x-axis-label {
  position: absolute;
  font-size: 12px;
  color: var(--text-tertiary);
  transform: translateX(-50%);
  text-align: center;
  bottom: 12px;
}

/* 图表底部 */
.chart-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-md);
}

.chart-legend {
  display: flex;
  gap: var(--space-lg);
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--text-secondary);
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--space-sm);
}

.legend-dot.sales {
  background: #0A84FF;
}

.legend-dot.orders {
  background: #FF9500;
}

.chart-actions {
  display: flex;
  gap: var(--space-md);
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 4px;
  background: rgba(200, 210, 230, 0.15);
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(200, 210, 230, 0.3);
}

.action-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  opacity: 0.7;
}

.action-icon.export {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z'/%3E%3C/svg%3E");
  background-size: contain;
}

.action-icon.report {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
  background-size: contain;
}

/* 分析洞察模块样式 */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 12px;
  margin-top: 12px;
}

.analytics-card {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-xs);
  transition: all 0.3s ease;
}

.analytics-card:active {
  transform: scale(0.98);
  background-color: var(--bg-tertiary);
}

.analytics-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 12px;
  background-size: 24px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
}

.analytics-icon.customer-insights {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M9 11.75c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zm6 0c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-.29.02-.58.05-.86 2.36-1.05 4.23-2.98 5.21-5.37C11.07 8.33 14.05 10 17.42 10c.78 0 1.53-.09 2.25-.26.21.71.33 1.47.33 2.26 0 4.41-3.59 8-8 8z'/%3E%3C/svg%3E");
}

.analytics-icon.product-analytics {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-7h2v7zm4 0h-2v-7h2v7zm4 0h-2v-7h2v7z'/%3E%3C/svg%3E");
}

.analytics-icon.market-trends {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E");
}

.analytics-icon.performance-metrics {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M15.9 5c-.17 0-.32.09-.41.23l-.07.15-5.18 11.65c-.16.29-.26.61-.26.96 0 1.11.9 2.01 2.01 2.01.96 0 1.77-.68 1.96-1.59l.01-.03L16.4 9.6l3.3 5.89c.12.22.35.35.59.35.05 0 .1 0 .15-.01.29-.06.51-.31.54-.6l.37-4.88 2.04.87c.22.09.47.01.63-.18.21-.23.21-.59-.01-.82l-6.53-5.89c-.18-.17-.43-.22-.64-.15z'/%3E%3C/svg%3E");
}

.analytics-content {
  flex: 1;
}

.analytics-title {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  display: block;
}

.analytics-desc {
  font-size: 12px;
  color: var(--text-tertiary);
  display: block;
  line-height: 1.4;
}

/* 系统设置模块样式 */
.settings-container {
  margin-bottom: 80px; /* 为底部导航腾出空间 */
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 12px;
  margin-top: 12px;
}

.settings-card {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--shadow-xs);
  transition: all 0.3s ease;
}

.settings-card:active {
  transform: scale(0.95);
  background-color: var(--bg-tertiary);
}

.settings-icon {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  margin-bottom: 8px;
  background-size: 24px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
}

.settings-icon.account {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.settings-icon.security {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E");
}

.settings-icon.notifications {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z'/%3E%3C/svg%3E");
}

.settings-icon.payment {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 1.99 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z'/%3E%3C/svg%3E");
}

.settings-title {
  font-size: 13px;
  color: var(--text-secondary);
  text-align: center;
}

/* 响应式调整 */
@media screen and (max-width: 360px) {
  .settings-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .analytics-card {
    padding: 12px;
  }
  
  .analytics-icon {
    width: 36px;
    height: 36px;
  }
  
  .analytics-title {
    font-size: 14px;
  }
}

/* 分析洞察模块 */
.analytics-section {
  margin-bottom: var(--space-lg);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  width: 96%;
  margin-left: auto;
  margin-right: auto;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 12px;
  margin-top: 12px;
}

.analytics-card {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-xs);
  transition: all 0.3s ease;
}

.analytics-card:active {
  transform: scale(0.98);
  background-color: var(--bg-tertiary);
}

.analytics-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 12px;
  background-size: 24px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
}

.analytics-icon.customer-insights {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M9 11.75c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zm6 0c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-.29.02-.58.05-.86 2.36-1.05 4.23-2.98 5.21-5.37C11.07 8.33 14.05 10 17.42 10c.78 0 1.53-.09 2.25-.26.21.71.33 1.47.33 2.26 0 4.41-3.59 8-8 8z'/%3E%3C/svg%3E");
}

.analytics-icon.product-analytics {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-7h2v7zm4 0h-2v-7h2v7zm4 0h-2v-7h2v7z'/%3E%3C/svg%3E");
}

.analytics-icon.market-trends {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E");
}

.analytics-icon.performance-metrics {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M15.9 5c-.17 0-.32.09-.41.23l-.07.15-5.18 11.65c-.16.29-.26.61-.26.96 0 1.11.9 2.01 2.01 2.01.96 0 1.77-.68 1.96-1.59l.01-.03L16.4 9.6l3.3 5.89c.12.22.35.35.59.35.05 0 .1 0 .15-.01.29-.06.51-.31.54-.6l.37-4.88 2.04.87c.22.09.47.01.63-.18.21-.23.21-.59-.01-.82l-6.53-5.89c-.18-.17-.43-.22-.64-.15z'/%3E%3C/svg%3E");
}

.analytics-content {
  flex: 1;
}

.analytics-title {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  display: block;
}

.analytics-desc {
  font-size: 12px;
  color: var(--text-tertiary);
  display: block;
  line-height: 1.4;
}

/* 系统设置模块样式 */
.settings-section {
  margin-bottom: var(--space-lg);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  width: 96%;
  margin-left: auto;
  margin-right: auto;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 12px;
  margin-top: 12px;
}

.settings-card {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--shadow-xs);
  transition: all 0.3s ease;
}

.settings-card:active {
  transform: scale(0.95);
  background-color: var(--bg-tertiary);
}

.settings-icon {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  margin-bottom: 8px;
  background-size: 24px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
}

.settings-icon.account {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.settings-icon.security {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z'/%3E%3C/svg%3E");
}

.settings-icon.notifications {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z'/%3E%3C/svg%3E");
}

.settings-icon.payment {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 1.99 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z'/%3E%3C/svg%3E");
}

.settings-title {
  font-size: 13px;
  color: var(--text-secondary);
  text-align: center;
}

/* 响应式调整 */
@media screen and (max-width: 360px) {
  .settings-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .analytics-card {
    padding: 12px;
  }
  
  .analytics-icon {
    width: 36px;
    height: 36px;
  }
  
  .analytics-title {
    font-size: 14px;
  }
}

/* 入口按钮样式 */
.entry-section {
  margin-bottom: calc(var(--space-lg) + 70px); /* 增加底部间距，避免被导航栏遮挡 */
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 0;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(200, 210, 230, 0.3);
  width: 96%;
  margin-left: auto;
  margin-right: auto;
}

.entry-button {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  transition: background-color 0.2s ease;
  position: relative;
}

.entry-button:active {
  background-color: var(--bg-secondary);
}

.entry-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-size: 20px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: rgba(10, 132, 255, 0.1);
  margin-right: var(--space-md);
  flex-shrink: 0;
}

.entry-icon.settings {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E");
}

.entry-icon.analytics {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-5h2v5zm4 0h-2v-3h2v3zm0-5h-2v-2h2v2zm4 5h-2V7h2v10z'/%3E%3C/svg%3E");
}

.entry-icon.campaign {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M11 17.25C11 17.25 16 14 16 9.75C16 5.5 11 5.5 11 9.75M11 17.25C11 17.25 6 14 6 9.75C6 5.5 11 5.5 11 9.75M11 17.25V21M11 9.75V3' stroke='%230A84FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.entry-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
}

.entry-arrow {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238E8E93'%3E%3Cpath d='M9 18L15 12L9 6' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

/* 添加媒体查询以在小屏幕上优化销售趋势图表 */
@media screen and (max-width: 375px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .time-tabs {
    margin-left: 0;
    margin-top: var(--space-xs);
    width: 100%;
  }
  
  .simple-chart.wide {
    height: 220px; /* 在小屏幕上减小图表高度 */
  }
  
  .x-axis-label {
    font-size: 10px; /* 减小标签字体大小 */
  }
}








</style>
