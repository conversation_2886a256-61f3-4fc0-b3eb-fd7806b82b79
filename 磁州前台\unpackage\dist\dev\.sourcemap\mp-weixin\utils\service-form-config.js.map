{"version": 3, "file": "service-form-config.js", "sources": ["utils/service-form-config.js"], "sourcesContent": ["// 各类服务类型的表单配置\n\n// 通用到家服务表单\nexport const generalHomeServiceForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：50-100元/次' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 8:00-20:00' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的服务内容、服务流程等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 家政保洁服务表单\nexport const homeCleaningForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的家政保洁服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：50-100元/小时' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 8:00-20:00' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'tags', label: '服务类型', name: 'serviceTags', required: true, options: ['日常保洁', '深度保洁', '开荒保洁', '家电清洗', '玻璃清洗', '地板打蜡', '沙发清洗', '厨房清洁'], maxCount: 5 },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的保洁服务内容、服务流程、使用的清洁用品等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 维修改造服务表单\nexport const repairForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的维修改造服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：上门费20元起，具体价格面议' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 8:00-20:00' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'tags', label: '维修类型', name: 'repairTags', required: true, options: ['水电维修', '木工维修', '家具维修', '电器维修', '管道疏通', '墙面翻新', '门窗维修', '其他维修'], maxCount: 5 },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片或维修案例，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的维修服务内容、服务流程、维修经验等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 上门安装服务表单\nexport const installationForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的安装服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：根据安装物品不同，价格不同' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 8:00-20:00' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'tags', label: '安装类型', name: 'installationTags', required: true, options: ['家具安装', '灯具安装', '电器安装', '窗帘安装', '洁具安装', '净水器安装', '智能锁安装', '其他安装'], maxCount: 5 },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的安装服务内容、安装流程、经验等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 开锁换锁服务表单\nexport const locksmithForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的开锁换锁服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：开锁30元起，换锁费用另计' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：24小时服务' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'tags', label: '服务类型', name: 'locksmithTags', required: true, options: ['门锁开锁', '保险柜开锁', '汽车开锁', '锁具更换', '防盗锁安装', '智能锁安装', '锁具维修', '其他服务'], maxCount: 5 },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的开锁换锁服务内容、服务流程、资质证明等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 搬家拉货服务表单\nexport const movingForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的搬家拉货服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：小型搬家200元起，大型搬家面议' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 6:00-22:00' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'tags', label: '服务类型', name: 'movingTags', required: true, options: ['居民搬家', '小型搬运', '长途搬家', '办公室搬迁', '家具拆装', '大件运输', '货物运输', '其他服务'], maxCount: 5 },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的搬家拉货服务内容、车辆信息、服务流程等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 上门美容服务表单\nexport const beautyServiceForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的上门美容服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：面部护理100元起，美甲80元起' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 9:00-21:00' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'tags', label: '服务类型', name: 'beautyTags', required: true, options: ['面部护理', '美甲服务', '美睫服务', '化妆服务', '头发护理', '身体按摩', '中医美容', '其他美容'], maxCount: 5 },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片或案例，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的美容服务内容、使用的产品、服务流程、资质证明等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 上门家教服务表单\nexport const tutorForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的上门家教服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：小学课程80元/小时，初中课程100元/小时' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周五18:00-21:00，周末全天' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'tags', label: '教学科目', name: 'tutorTags', required: true, options: ['语文', '数学', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '音乐', '美术', '体育', '编程', '其他'], maxCount: 6 },\n        { type: 'upload', label: '相关证件或照片', name: 'images', required: true, tip: '请上传学历证明、教师资格证等相关证件照片，最多5张' },\n        { type: 'textarea', label: '教学经验', name: 'description', required: true, placeholder: '请详细描述您的教学经验、教育背景、授课方法等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 宠物服务表单\nexport const petServiceForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的宠物服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：宠物洗澡50元起，宠物寄养100元/天' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 9:00-20:00' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'tags', label: '服务类型', name: 'petServiceTags', required: true, options: ['宠物洗护', '宠物美容', '宠物寄养', '宠物训练', '宠物医疗', '宠物接送', '上门遛狗', '其他服务'], maxCount: 5 },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片或案例，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的宠物服务内容、服务流程、服务环境、资质证明等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 上门疏通服务表单\nexport const plumbingForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的上门疏通服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：普通疏通50元起，疑难管道疏通面议' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：24小时服务' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'tags', label: '疏通类型', name: 'plumbingTags', required: true, options: ['马桶疏通', '地漏疏通', '厨房下水道', '洗菜池疏通', '浴缸疏通', '管道清洗', '化粪池清理', '其他疏通'], maxCount: 5 },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片或案例，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的疏通服务内容、服务流程、使用的工具、服务经验等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 其他类型服务表单\nexport const otherServiceForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的服务' },\n        { type: 'input', label: '服务类型', name: 'serviceType', required: true, placeholder: '请填写您的服务类型' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：服务价格面议' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 9:00-18:00' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的服务内容、服务流程、专业资质等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 婚恋交友表单配置\nexport const datingForm = {\n  sections: [\n    {\n      label: '基本信息',\n      fields: [\n        { type: 'input', name: 'nickname', label: '昵称', required: true, placeholder: '请输入您的昵称' },\n        { type: 'gender', name: 'gender', label: '性别', required: true },\n        { type: 'input', name: 'age', label: '年龄', required: true, inputType: 'number', placeholder: '请输入您的实际年龄' },\n        { type: 'input', name: 'height', label: '身高', required: true, inputType: 'number', placeholder: '请输入身高(cm)', suffix: 'cm' },\n        { type: 'picker', name: 'education', label: '学历', required: true, options: ['初中及以下', '高中/中专', '大专', '本科', '硕士', '博士及以上'] },\n        { type: 'picker', name: 'maritalStatus', label: '婚姻状况', required: true, options: ['未婚', '离异', '丧偶'] },\n        { type: 'input', name: 'occupation', label: '职业', required: true, placeholder: '请输入您的职业' },\n        { type: 'input', name: 'income', label: '月收入', required: true, placeholder: '如：5000-8000、8000-12000等' }\n      ]\n    },\n    {\n      label: '个人介绍',\n      fields: [\n        { type: 'textarea', name: 'selfIntro', label: '自我介绍', required: true, placeholder: '请简要介绍自己的性格、爱好、生活方式等' },\n        { type: 'textarea', name: 'expectation', label: '择偶要求', required: true, placeholder: '请描述您期望的另一半的条件、性格等' },\n        { type: 'upload', name: 'images', label: '个人照片', required: true, tip: '请上传清晰的本人近照，最多5张' },\n        { type: 'tags', name: 'interests', label: '兴趣爱好', options: ['旅游', '美食', '运动', '阅读', '电影', '音乐', '摄影', '书法', '绘画', '舞蹈', '游戏', '其他'], maxCount: 5 }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', name: 'contact', label: '联系人', required: true, placeholder: '请输入联系人姓名' },\n        { type: 'input', name: 'phone', label: '手机号', required: true, inputType: 'number', placeholder: '请输入有效的手机号码' },\n        { type: 'sendCode', name: 'code', label: '验证码', required: true },\n        { type: 'input', name: 'wechat', label: '微信号', placeholder: '方便联系的微信号' }\n      ]\n    }\n  ]\n};\n\n// 商家活动表单配置\nexport const merchantActivityForm = {\n  sections: [\n    {\n      label: '活动基本信息',\n      fields: [\n        { type: 'input', name: 'title', label: '活动标题', required: true, placeholder: '请输入活动标题，简明扼要' },\n        { type: 'input', name: 'merchantName', label: '商家名称', required: true, placeholder: '请输入商家全称' },\n        { type: 'picker', name: 'activityType', label: '活动类型', required: true, options: ['优惠折扣', '满减活动', '买赠活动', '限时特价', '新品上市', '会员专享', '节日活动', '其他'] },\n        { type: 'date', name: 'startDate', label: '开始日期', required: true },\n        { type: 'date', name: 'endDate', label: '结束日期', required: true },\n        { type: 'location', name: 'address', label: '活动地点', required: true, placeholder: '请输入详细的商家地址' }\n      ]\n    },\n    {\n      label: '活动详情',\n      fields: [\n        { type: 'textarea', name: 'description', label: '活动描述', required: true, placeholder: '请详细描述活动内容、优惠力度、参与方式等' },\n        { type: 'textarea', name: 'rules', label: '活动规则', required: true, placeholder: '请说明活动的规则、限制条件等' },\n        { type: 'upload', name: 'images', label: '活动图片', required: true, tip: '请上传活动海报、商品图片等，最多5张' },\n        { type: 'tags', name: 'tags', label: '活动标签', options: ['超值优惠', '限时抢购', '新店开业', '周年庆', '节日特惠', '会员专享', '秒杀活动', '团购优惠'], maxCount: 3 }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', name: 'contact', label: '联系人', required: true, placeholder: '请输入活动负责人姓名' },\n        { type: 'input', name: 'phone', label: '联系电话', required: true, inputType: 'number', placeholder: '请输入有效的联系电话' },\n        { type: 'sendCode', name: 'code', label: '验证码', required: true },\n        { type: 'input', name: 'wechat', label: '微信号', placeholder: '方便客户咨询的微信号' }\n      ]\n    }\n  ]\n};\n\n// 店铺/摊位转让表单\nexport const businessTransferForm = {\n  sections: [\n    {\n      label: '转让信息',\n      fields: [\n        { type: 'input', label: '标题', name: 'title', required: true, placeholder: '请简要描述您要转让的店铺/摊位' },\n        { type: 'picker', label: '转让类型', name: 'transferType', required: true, options: ['店铺转让', '摊位转让', '柜台转让', '档口转让', '其他'] },\n        { type: 'input', label: '经营类型', name: 'businessType', required: true, placeholder: '例如：餐饮、服装、美容美发' },\n        { type: 'location', label: '店铺地址', name: 'location', required: true },\n        { type: 'input', label: '店面面积', name: 'area', required: false, placeholder: '请输入店面面积', suffix: '㎡' },\n        { type: 'input', label: '月租金', name: 'rent', required: true, placeholder: '请输入月租金', suffix: '元/月' },\n        { type: 'input', label: '转让费', name: 'transferFee', required: true, placeholder: '请输入转让费', suffix: '元' },\n        { type: 'input', label: '剩余租期', name: 'leaseTime', required: false, placeholder: '例如：3年、10个月' },\n        { type: 'upload', label: '店铺照片', name: 'images', required: true, tip: '请上传清晰的店铺内外部照片，最多9张' },\n        { type: 'textarea', label: '详细描述', name: 'description', required: true, placeholder: '请详细描述店铺的装修情况、客流量、经营状况、转让原因等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 教育培训表单\nexport const educationForm = {\n  sections: [\n    {\n      label: '课程信息',\n      fields: [\n        { type: 'input', label: '标题', name: 'title', required: true, placeholder: '请简要描述您的课程或培训项目' },\n        { type: 'picker', label: '教育类型', name: 'educationType', required: true, options: ['学科辅导', '兴趣培训', '技能培训', '语言培训', '职业培训', '资格证书', '其他教育'] },\n        { type: 'input', label: '适合对象', name: 'targetStudents', required: true, placeholder: '例如：小学生、职场人士、零基础学员' },\n        { type: 'input', label: '课程价格', name: 'price', required: true, placeholder: '例如：3980元/期、198元/课时' },\n        { type: 'input', label: '课程时长', name: 'duration', required: true, placeholder: '例如：3个月、16课时' },\n        { type: 'input', label: '上课时间', name: 'classTime', required: true, placeholder: '例如：周末班、晚上7-9点' },\n        { type: 'location', label: '上课地点', name: 'location', required: true },\n        { type: 'upload', label: '相关图片', name: 'images', required: true, tip: '请上传课程、教师或场地照片，最多5张' },\n        { type: 'textarea', label: '课程详情', name: 'description', required: true, placeholder: '请详细描述课程内容、教学特点、师资力量、学习效果等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 默认表单\nexport const defaultForm = {\n  sections: [\n    {\n      label: '基本信息',\n      fields: [\n        { type: 'input', label: '标题', name: 'title', required: true, placeholder: '请输入标题' },\n        { type: 'upload', label: '相关图片', name: 'images', required: true, tip: '请上传相关图片，最多5张' },\n        { type: 'textarea', label: '详细描述', name: 'description', required: true, placeholder: '请详细描述您要发布的信息内容' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 宠物表单配置\nexport const petForm = {\n  sections: [\n    {\n      label: '宠物信息',\n      fields: [\n        { type: 'input', label: '标题', name: 'title', required: true, placeholder: '请简要描述您的宠物' },\n        { type: 'picker', label: '宠物类型', name: 'petType', required: true, options: ['狗狗', '猫咪', '兔子', '鼠类', '鸟类', '鱼类', '龟类', '其他'] },\n        { type: 'input', label: '品种', name: 'breed', required: true, placeholder: '例如：金毛、英短、荷兰垂耳兔' },\n        { type: 'picker', label: '性别', name: 'gender', required: true, options: ['公', '母', '未知'] },\n        { type: 'input', label: '年龄', name: 'age', required: true, placeholder: '例如：2岁、3个月' },\n        { type: 'picker', label: '是否纯种', name: 'isPurebred', required: false, options: ['是', '否', '不确定'] },\n        { type: 'picker', label: '疫苗情况', name: 'vaccineStatus', required: false, options: ['已接种', '部分接种', '未接种'] },\n        { type: 'picker', label: '驱虫情况', name: 'dewormingStatus', required: false, options: ['已驱虫', '未驱虫'] },\n        { type: 'picker', label: '绝育情况', name: 'neuterStatus', required: false, options: ['已绝育', '未绝育'] },\n        { type: 'input', label: '价格', name: 'price', required: true, placeholder: '例如：2000元、赠送' },\n        { type: 'upload', label: '宠物照片', name: 'images', required: true, tip: '请上传清晰的宠物照片，最多9张' },\n        { type: 'textarea', label: '宠物描述', name: 'description', required: true, placeholder: '请详细描述宠物的性格、习惯、喂养要求等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 其他到家服务表单\nexport const otherHomeServiceForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的服务' },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：50-100元/次' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 8:00-20:00' },\n        { type: 'location', label: '服务区域', name: 'serviceArea', required: true },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的服务内容、服务流程等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 找服务表单\nexport const findServiceForm = {\n  sections: [\n    {\n      label: '需求基本信息',\n      fields: [\n        { type: 'input', label: '需求标题', name: 'title', required: true, placeholder: '请简要描述您需要的服务' },\n        { type: 'input', label: '预算范围', name: 'budget', required: true, placeholder: '例如：100-200元' },\n        { type: 'location', label: '服务地点', name: 'serviceLocation', required: true },\n        { type: 'input', label: '期望时间', name: 'expectedTime', required: true, placeholder: '例如：周末或工作日晚上' },\n        { type: 'textarea', label: '需求详情', name: 'description', required: true, placeholder: '请详细描述您的需求，越详细越容易找到合适的服务' },\n        { type: 'upload', label: '相关图片', name: 'images', required: false, tip: '可上传与需求相关的参考图片，最多5张' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 招聘表单\nexport const recruitmentForm = {\n  sections: [\n    {\n      label: '职位信息',\n      fields: [\n        { type: 'input', label: '职位名称', name: 'jobTitle', required: true, placeholder: '请输入招聘的职位名称' },\n        { type: 'input', label: '公司名称', name: 'companyName', required: true, placeholder: '请输入公司全称' },\n        { type: 'input', label: '薪资范围', name: 'salary', required: true, placeholder: '例如：4000-6000元/月' },\n        { type: 'picker', label: '工作性质', name: 'jobNature', required: true, options: ['全职', '兼职', '实习', '临时工'] },\n        { type: 'picker', label: '学历要求', name: 'education', required: true, options: ['不限', '初中及以下', '高中/中专', '大专', '本科', '硕士', '博士及以上'] },\n        { type: 'input', label: '工作经验', name: 'experience', required: true, placeholder: '例如：1-3年、经验不限' },\n        { type: 'location', label: '工作地点', name: 'workLocation', required: true },\n        { type: 'textarea', label: '职位描述', name: 'jobDescription', required: true, placeholder: '请详细描述职位职责、要求等信息' },\n        { type: 'textarea', label: '公司介绍', name: 'companyDescription', required: false, placeholder: '请简要介绍公司情况、福利等' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '电子邮箱', name: 'email', required: false },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 汽车服务表单\nexport const carServiceForm = {\n  sections: [\n    {\n      label: '服务基本信息',\n      fields: [\n        { type: 'input', label: '服务标题', name: 'title', required: true, placeholder: '请简要描述您提供的汽车服务' },\n        { type: 'picker', label: '服务类型', name: 'serviceType', required: true, options: ['洗车美容', '维修保养', '汽车改装', '汽车救援', '驾驶培训', '年检代办', '其他服务'] },\n        { type: 'input', label: '价格范围', name: 'price', required: true, placeholder: '例如：洗车30元起，保养200元起' },\n        { type: 'input', label: '服务时间', name: 'serviceTime', required: true, placeholder: '例如：周一至周日 8:00-20:00' },\n        { type: 'location', label: '服务地点', name: 'serviceLocation', required: true },\n        { type: 'upload', label: '服务展示图片', name: 'images', required: true, tip: '请上传清晰的服务相关图片或店面照片，最多5张' },\n        { type: 'textarea', label: '服务详情', name: 'description', required: true, placeholder: '请详细描述您提供的汽车服务内容、服务流程、技术水平等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 二手交易表单\nexport const secondHandForm = {\n  sections: [\n    {\n      label: '商品信息',\n      fields: [\n        { type: 'input', label: '标题', name: 'title', required: true, placeholder: '请简要描述您的商品' },\n        { type: 'picker', label: '商品类别', name: 'category', required: true, options: ['手机数码', '家用电器', '电脑办公', '家具家居', '服装鞋帽', '母婴用品', '运动户外', '图书音像', '其他物品'] },\n        { type: 'input', label: '品牌型号', name: 'brand', required: false, placeholder: '例如：iPhone 13、华为P50' },\n        { type: 'input', label: '新旧程度', name: 'condition', required: true, placeholder: '例如：9成新、全新未拆封' },\n        { type: 'input', label: '使用时长', name: 'usageTime', required: false, placeholder: '例如：使用3个月、1年' },\n        { type: 'input', label: '原价', name: 'originalPrice', required: false, placeholder: '购买时的价格', suffix: '元' },\n        { type: 'input', label: '售价', name: 'price', required: true, placeholder: '请输入出售价格', suffix: '元' },\n        { type: 'location', label: '交易地点', name: 'location', required: true },\n        { type: 'picker', label: '交易方式', name: 'tradeMethod', required: true, options: ['线下自提', '快递邮寄', '同城配送', '均可'] },\n        { type: 'upload', label: '商品照片', name: 'images', required: true, tip: '请上传清晰的商品照片，包括外观和细节，最多9张' },\n        { type: 'textarea', label: '商品描述', name: 'description', required: true, placeholder: '请详细描述商品的功能、配置、包装、瑕疵等情况' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 拼车表单\nexport const carpoolForm = {\n  sections: [\n    {\n      label: '拼车信息',\n      fields: [\n        { type: 'input', label: '标题', name: 'title', required: true, placeholder: '请简要描述您的拼车需求' },\n        { type: 'picker', label: '拼车类型', name: 'carpoolType', required: true, options: ['人找车', '车找人', '货找车', '车找货'] },\n        { type: 'location', label: '出发地', name: 'startLocation', required: true },\n        { type: 'location', label: '目的地', name: 'endLocation', required: true },\n        { type: 'input', label: '出发时间', name: 'departureTime', required: true, placeholder: '例如：6月1日上午10点' },\n        { type: 'input', label: '座位数/人数', name: 'seats', required: false, placeholder: '例如：3个座位、需要2人' },\n        { type: 'input', label: '车型信息', name: 'carInfo', required: false, placeholder: '例如：大众朗逸、白色轿车' },\n        { type: 'input', label: '费用说明', name: 'price', required: true, placeholder: '例如：AA分担、每人50元' },\n        { type: 'textarea', label: '其他说明', name: 'description', required: false, placeholder: '请补充其他关于拼车的要求或说明' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 求职表单\nexport const jobWantedForm = {\n  sections: [\n    {\n      label: '个人信息',\n      fields: [\n        { type: 'input', label: '姓名', name: 'name', required: true },\n        { type: 'picker', label: '性别', name: 'gender', required: true, options: ['男', '女'] },\n        { type: 'input', label: '年龄', name: 'age', required: true, inputType: 'number' },\n        { type: 'picker', label: '学历', name: 'education', required: true, options: ['初中及以下', '高中/中专', '大专', '本科', '硕士', '博士及以上'] },\n        { type: 'input', label: '工作经验', name: 'experience', required: true, placeholder: '例如：3年销售经验' }\n      ]\n    },\n    {\n      label: '求职意向',\n      fields: [\n        { type: 'input', label: '期望职位', name: 'desiredPosition', required: true },\n        { type: 'input', label: '期望薪资', name: 'expectedSalary', required: true, placeholder: '例如：5000-8000元/月' },\n        { type: 'picker', label: '工作性质', name: 'jobNature', required: true, options: ['全职', '兼职', '实习', '临时工'] },\n        { type: 'location', label: '期望地点', name: 'desiredLocation', required: false },\n        { type: 'textarea', label: '个人简介', name: 'introduction', required: true, placeholder: '请简要介绍您的工作经历、专业技能等' },\n        { type: 'upload', label: '个人照片', name: 'images', required: false, tip: '可上传个人照片或作品，最多3张' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 房屋出租表单\nexport const houseRentForm = {\n  sections: [\n    {\n      label: '房源信息',\n      fields: [\n        { type: 'input', label: '标题', name: 'title', required: true, placeholder: '请简要描述房源特点' },\n        { type: 'picker', label: '房源类型', name: 'houseType', required: true, options: ['整租', '合租', '短租'] },\n        { type: 'input', label: '房屋户型', name: 'layout', required: true, placeholder: '例如：2室1厅1卫' },\n        { type: 'input', label: '面积', name: 'area', required: true, placeholder: '请输入房屋面积', suffix: '㎡' },\n        { type: 'input', label: '楼层', name: 'floor', required: true, placeholder: '例如：6/18层' },\n        { type: 'input', label: '朝向', name: 'orientation', required: false, placeholder: '例如：南北通透' },\n        { type: 'input', label: '小区名称', name: 'community', required: true },\n        { type: 'location', label: '房屋地址', name: 'location', required: true },\n        { type: 'input', label: '租金', name: 'price', required: true, placeholder: '请输入月租金', suffix: '元/月' },\n        { type: 'tags', label: '房屋配置', name: 'facilities', required: false, options: ['床', '衣柜', '空调', '冰箱', '洗衣机', '电视', '热水器', '宽带', '天然气', '暖气', '卫生间', '厨房', '阳台'], maxCount: 10 },\n        { type: 'upload', label: '房屋照片', name: 'images', required: true, tip: '请上传清晰的房屋照片，最多9张' },\n        { type: 'textarea', label: '房源描述', name: 'description', required: true, placeholder: '请详细描述房源情况、交通、周边配套等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 房屋出售表单\nexport const houseSellForm = {\n  sections: [\n    {\n      label: '房源信息',\n      fields: [\n        { type: 'input', label: '标题', name: 'title', required: true, placeholder: '请简要描述房源特点' },\n        { type: 'input', label: '房屋户型', name: 'layout', required: true, placeholder: '例如：3室2厅2卫' },\n        { type: 'input', label: '面积', name: 'area', required: true, placeholder: '请输入房屋面积', suffix: '㎡' },\n        { type: 'input', label: '楼层', name: 'floor', required: true, placeholder: '例如：8/25层' },\n        { type: 'input', label: '朝向', name: 'orientation', required: false, placeholder: '例如：南北通透' },\n        { type: 'input', label: '装修情况', name: 'decoration', required: true, placeholder: '例如：精装修、简装、毛坯' },\n        { type: 'input', label: '建造年代', name: 'buildYear', required: false, placeholder: '例如：2015年建' },\n        { type: 'input', label: '小区名称', name: 'community', required: true },\n        { type: 'location', label: '房屋地址', name: 'location', required: true },\n        { type: 'input', label: '售价', name: 'price', required: true, placeholder: '请输入总价', suffix: '万元' },\n        { type: 'input', label: '单价', name: 'unitPrice', required: false, placeholder: '每平米单价', suffix: '元/㎡' },\n        { type: 'picker', label: '房屋性质', name: 'propertyType', required: true, options: ['商品房', '经济适用房', '回迁房', '公寓', '别墅', '商住两用'] },\n        { type: 'picker', label: '产权年限', name: 'ownership', required: false, options: ['70年', '50年', '40年', '永久'] },\n        { type: 'tags', label: '房屋特色', name: 'features', required: false, options: ['学区房', '地铁房', '电梯房', '南北通透', '采光好', '河景房', '公园周边', '带花园', '带车位', '低总价'], maxCount: 6 },\n        { type: 'upload', label: '房屋照片', name: 'images', required: true, tip: '请上传清晰的房屋照片，最多9张' },\n        { type: 'textarea', label: '房源描述', name: 'description', required: true, placeholder: '请详细描述房源情况、交通、周边配套等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};\n\n// 二手车表单\nexport const usedCarForm = {\n  sections: [\n    {\n      label: '车辆信息',\n      fields: [\n        { type: 'input', label: '标题', name: 'title', required: true, placeholder: '请简要描述您的车辆' },\n        { type: 'input', label: '品牌车型', name: 'carModel', required: true, placeholder: '例如：大众朗逸、本田雅阁' },\n        { type: 'input', label: '上牌时间', name: 'registrationTime', required: true, placeholder: '例如：2018年5月' },\n        { type: 'input', label: '行驶里程', name: 'mileage', required: true, placeholder: '请输入行驶里程', suffix: '万公里' },\n        { type: 'input', label: '排量', name: 'displacement', required: false, placeholder: '例如：1.6L、2.0T' },\n        { type: 'picker', label: '变速箱', name: 'transmission', required: false, options: ['手动', '自动', '手自一体', 'CVT', '双离合'] },\n        { type: 'picker', label: '排放标准', name: 'emissionStandard', required: false, options: ['国五', '国六', '欧五', '欧六'] },\n        { type: 'picker', label: '车辆类型', name: 'carType', required: true, options: ['轿车', 'SUV', '跑车', 'MPV', '面包车', '皮卡', '货车'] },\n        { type: 'input', label: '车身颜色', name: 'color', required: false, placeholder: '例如：白色、黑色' },\n        { type: 'picker', label: '过户次数', name: 'transferCount', required: false, options: ['0次(新车)', '1次', '2次', '3次及以上'] },\n        { type: 'input', label: '年检到期', name: 'inspectionExpiry', required: false, placeholder: '例如：2023年6月' },\n        { type: 'input', label: '保险到期', name: 'insuranceExpiry', required: false, placeholder: '例如：2023年8月' },\n        { type: 'input', label: '售价', name: 'price', required: true, placeholder: '请输入售价', suffix: '万元' },\n        { type: 'upload', label: '车辆照片', name: 'images', required: true, tip: '请上传清晰的车辆内外照片，至少3张，最多9张' },\n        { type: 'textarea', label: '车况描述', name: 'description', required: true, placeholder: '请详细描述车辆的使用情况、维修保养记录、车况等信息' }\n      ]\n    },\n    {\n      label: '联系方式',\n      fields: [\n        { type: 'input', label: '联系人', name: 'contactName', required: true },\n        { type: 'input', label: '联系电话', name: 'contactPhone', required: true, inputType: 'number' },\n        { type: 'input', label: '微信号', name: 'wechat', required: false }\n      ]\n    }\n  ]\n};"], "names": [], "mappings": ";AAGO,MAAM,yBAAyB;AAAA,EACpC,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,cAAe;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,eAAgB;AAAA,QAC5F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,oBAAqB;AAAA,QAC7F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,wBAAyB;AAAA,MAC/G;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,mBAAmB;AAAA,EAC9B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,gBAAiB;AAAA,QAC7F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,UAAU,EAAG;AAAA,QAC5J,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,oBAAqB;AAAA,QAC7F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,kCAAmC;AAAA,MACzH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,aAAa;AAAA,EACxB,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,oBAAqB;AAAA,QACjG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,cAAc,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,UAAU,EAAG;AAAA,QAC3J,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,yBAA0B;AAAA,QAClG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,+BAAgC;AAAA,MACtH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,mBAAmB;AAAA,EAC9B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,gBAAiB;AAAA,QAC7F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,mBAAoB;AAAA,QAChG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,oBAAoB,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,MAAM,GAAG,UAAU,EAAG;AAAA,QACnK,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,oBAAqB;AAAA,QAC7F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,6BAA8B;AAAA,MACpH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,mBAAoB;AAAA,QAChG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,YAAa;AAAA,QAC/F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,iBAAiB,UAAU,MAAM,SAAS,CAAC,QAAQ,SAAS,QAAQ,QAAQ,SAAS,SAAS,QAAQ,MAAM,GAAG,UAAU,EAAG;AAAA,QACjK,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,oBAAqB;AAAA,QAC7F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,iCAAkC;AAAA,MACxH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,aAAa;AAAA,EACxB,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACnG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,cAAc,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,MAAM,GAAG,UAAU,EAAG;AAAA,QAC5J,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,oBAAqB;AAAA,QAC7F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,iCAAkC;AAAA,MACxH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,oBAAoB;AAAA,EAC/B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACnG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,cAAc,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,UAAU,EAAG;AAAA,QAC3J,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,uBAAwB;AAAA,QAChG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,qCAAsC;AAAA,MAC5H;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,YAAY;AAAA,EACvB,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,4BAA6B;AAAA,QACzG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,2BAA4B;AAAA,QAC9G,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,aAAa,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,UAAU,EAAG;AAAA,QAC9K,EAAE,MAAM,UAAU,OAAO,WAAW,MAAM,UAAU,UAAU,MAAM,KAAK,4BAA6B;AAAA,QACtG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,2BAA4B;AAAA,MAClH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,iBAAiB;AAAA,EAC5B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,gBAAiB;AAAA,QAC7F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,yBAA0B;AAAA,QACtG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,kBAAkB,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,GAAG,UAAU,EAAG;AAAA,QAC/J,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,uBAAwB;AAAA,QAChG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,oCAAqC;AAAA,MAC3H;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,eAAe;AAAA,EAC1B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,uBAAwB;AAAA,QACpG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,YAAa;AAAA,QAC/F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,SAAS,SAAS,QAAQ,QAAQ,SAAS,MAAM,GAAG,UAAU,EAAG;AAAA,QAChK,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,uBAAwB;AAAA,QAChG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,qCAAsC;AAAA,MAC5H;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGO,MAAM,mBAAmB;AAAA,EAC9B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,cAAe;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,YAAa;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,YAAa;AAAA,QACzF,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,oBAAqB;AAAA,QAC7F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,6BAA8B;AAAA,MACpH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,aAAa;AAAA,EACxB,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,MAAM,YAAY,OAAO,MAAM,UAAU,MAAM,aAAa,UAAW;AAAA,QACxF,EAAE,MAAM,UAAU,MAAM,UAAU,OAAO,MAAM,UAAU,KAAM;AAAA,QAC/D,EAAE,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM,UAAU,MAAM,WAAW,UAAU,aAAa,YAAa;AAAA,QAC1G,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,MAAM,UAAU,MAAM,WAAW,UAAU,aAAa,aAAa,QAAQ,KAAM;AAAA,QAC3H,EAAE,MAAM,UAAU,MAAM,aAAa,OAAO,MAAM,UAAU,MAAM,SAAS,CAAC,SAAS,SAAS,MAAM,MAAM,MAAM,OAAO,EAAG;AAAA,QAC1H,EAAE,MAAM,UAAU,MAAM,iBAAiB,OAAO,QAAQ,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,IAAI,EAAG;AAAA,QACrG,EAAE,MAAM,SAAS,MAAM,cAAc,OAAO,MAAM,UAAU,MAAM,aAAa,UAAW;AAAA,QAC1F,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,OAAO,UAAU,MAAM,aAAa,0BAA2B;AAAA,MACxG;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,YAAY,MAAM,aAAa,OAAO,QAAQ,UAAU,MAAM,aAAa,sBAAuB;AAAA,QAC1G,EAAE,MAAM,YAAY,MAAM,eAAe,OAAO,QAAQ,UAAU,MAAM,aAAa,oBAAqB;AAAA,QAC1G,EAAE,MAAM,UAAU,MAAM,UAAU,OAAO,QAAQ,UAAU,MAAM,KAAK,kBAAmB;AAAA,QACzF,EAAE,MAAM,QAAQ,MAAM,aAAa,OAAO,QAAQ,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,UAAU,EAAG;AAAA,MACnJ;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,MAAM,WAAW,OAAO,OAAO,UAAU,MAAM,aAAa,WAAY;AAAA,QACzF,EAAE,MAAM,SAAS,MAAM,SAAS,OAAO,OAAO,UAAU,MAAM,WAAW,UAAU,aAAa,aAAc;AAAA,QAC9G,EAAE,MAAM,YAAY,MAAM,QAAQ,OAAO,OAAO,UAAU,KAAM;AAAA,QAChE,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,OAAO,aAAa,WAAY;AAAA,MACzE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,uBAAuB;AAAA,EAClC,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,MAAM,SAAS,OAAO,QAAQ,UAAU,MAAM,aAAa,eAAgB;AAAA,QAC5F,EAAE,MAAM,SAAS,MAAM,gBAAgB,OAAO,QAAQ,UAAU,MAAM,aAAa,UAAW;AAAA,QAC9F,EAAE,MAAM,UAAU,MAAM,gBAAgB,OAAO,QAAQ,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,IAAI,EAAG;AAAA,QAChJ,EAAE,MAAM,QAAQ,MAAM,aAAa,OAAO,QAAQ,UAAU,KAAM;AAAA,QAClE,EAAE,MAAM,QAAQ,MAAM,WAAW,OAAO,QAAQ,UAAU,KAAM;AAAA,QAChE,EAAE,MAAM,YAAY,MAAM,WAAW,OAAO,QAAQ,UAAU,MAAM,aAAa,aAAc;AAAA,MAChG;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,YAAY,MAAM,eAAe,OAAO,QAAQ,UAAU,MAAM,aAAa,uBAAwB;AAAA,QAC7G,EAAE,MAAM,YAAY,MAAM,SAAS,OAAO,QAAQ,UAAU,MAAM,aAAa,iBAAkB;AAAA,QACjG,EAAE,MAAM,UAAU,MAAM,UAAU,OAAO,QAAQ,UAAU,MAAM,KAAK,qBAAsB;AAAA,QAC5F,EAAE,MAAM,QAAQ,MAAM,QAAQ,OAAO,QAAQ,SAAS,CAAC,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,MAAM,GAAG,UAAU,EAAG;AAAA,MACrI;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,MAAM,WAAW,OAAO,OAAO,UAAU,MAAM,aAAa,aAAc;AAAA,QAC3F,EAAE,MAAM,SAAS,MAAM,SAAS,OAAO,QAAQ,UAAU,MAAM,WAAW,UAAU,aAAa,aAAc;AAAA,QAC/G,EAAE,MAAM,YAAY,MAAM,QAAQ,OAAO,OAAO,UAAU,KAAM;AAAA,QAChE,EAAE,MAAM,SAAS,MAAM,UAAU,OAAO,OAAO,aAAa,aAAc;AAAA,MAC3E;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,uBAAuB;AAAA,EAClC,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAC7F,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,IAAI,EAAG;AAAA,QACxH,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,aAAa,gBAAiB;AAAA,QACpG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,YAAY,UAAU,KAAM;AAAA,QACrE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,QAAQ,UAAU,OAAO,aAAa,WAAW,QAAQ,IAAK;AAAA,QACpG,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,QAAQ,UAAU,MAAM,aAAa,UAAU,QAAQ,MAAO;AAAA,QACnG,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,MAAM,aAAa,UAAU,QAAQ,IAAK;AAAA,QACxG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,aAAa,UAAU,OAAO,aAAa,aAAc;AAAA,QAC/F,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,KAAK,qBAAsB;AAAA,QAC5F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,gCAAiC;AAAA,MACvH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,iBAAkB;AAAA,QAC5F,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,iBAAiB,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,EAAG;AAAA,QAC3I,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,kBAAkB,UAAU,MAAM,aAAa,oBAAqB;AAAA,QAC1G,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,qBAAsB;AAAA,QAClG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,YAAY,UAAU,MAAM,aAAa,cAAe;AAAA,QAC9F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,aAAa,UAAU,MAAM,aAAa,gBAAiB;AAAA,QACjG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,YAAY,UAAU,KAAM;AAAA,QACrE,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,KAAK,qBAAsB;AAAA,QAC5F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,8BAA+B;AAAA,MACrH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,cAAc;AAAA,EACzB,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,QAAS;AAAA,QACnF,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,KAAK,eAAgB;AAAA,QACtF,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,iBAAkB;AAAA,MACxG;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,UAAU;AAAA,EACrB,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,YAAa;AAAA,QACvF,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,WAAW,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,EAAG;AAAA,QAC7H,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,iBAAkB;AAAA,QAC5F,EAAE,MAAM,UAAU,OAAO,MAAM,MAAM,UAAU,UAAU,MAAM,SAAS,CAAC,KAAK,KAAK,IAAI,EAAG;AAAA,QAC1F,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,OAAO,UAAU,MAAM,aAAa,YAAa;AAAA,QACrF,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,cAAc,UAAU,OAAO,SAAS,CAAC,KAAK,KAAK,KAAK,EAAG;AAAA,QAClG,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,iBAAiB,UAAU,OAAO,SAAS,CAAC,OAAO,QAAQ,KAAK,EAAG;AAAA,QAC1G,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,mBAAmB,UAAU,OAAO,SAAS,CAAC,OAAO,KAAK,EAAG;AAAA,QACpG,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,gBAAgB,UAAU,OAAO,SAAS,CAAC,OAAO,KAAK,EAAG;AAAA,QACjG,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,cAAe;AAAA,QACzF,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,KAAK,kBAAmB;AAAA,QACzF,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,wBAAyB;AAAA,MAC/G;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,uBAAuB;AAAA,EAClC,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,cAAe;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,eAAgB;AAAA,QAC5F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,KAAM;AAAA,QACxE,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,oBAAqB;AAAA,QAC7F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,wBAAyB;AAAA,MAC/G;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,kBAAkB;AAAA,EAC7B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,cAAe;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,aAAa,cAAe;AAAA,QAC5F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,mBAAmB,UAAU,KAAM;AAAA,QAC5E,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,aAAa,cAAe;AAAA,QAClG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,0BAA2B;AAAA,QAChH,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,OAAO,KAAK,qBAAsB;AAAA,MAC9F;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,kBAAkB;AAAA,EAC7B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,YAAY,UAAU,MAAM,aAAa,aAAc;AAAA,QAC7F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,UAAW;AAAA,QAC7F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAChG,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,aAAa,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,MAAM,KAAK,EAAG;AAAA,QACxG,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,aAAa,UAAU,MAAM,SAAS,CAAC,MAAM,SAAS,SAAS,MAAM,MAAM,MAAM,OAAO,EAAG;AAAA,QAClI,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,cAAc,UAAU,MAAM,aAAa,eAAgB;AAAA,QACjG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,gBAAgB,UAAU,KAAM;AAAA,QACzE,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,kBAAkB,UAAU,MAAM,aAAa,kBAAmB;AAAA,QAC3G,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,sBAAsB,UAAU,OAAO,aAAa,gBAAiB;AAAA,MAC/G;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAO;AAAA,QAChE,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,iBAAiB;AAAA,EAC5B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,gBAAiB;AAAA,QAC7F,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,EAAG;AAAA,QACzI,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,oBAAqB;AAAA,QACjG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,sBAAuB;AAAA,QACzG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,mBAAmB,UAAU,KAAM;AAAA,QAC5E,EAAE,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU,UAAU,MAAM,KAAK,yBAA0B;AAAA,QAClG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,+BAAgC;AAAA,MACtH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,iBAAiB;AAAA,EAC5B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,YAAa;AAAA,QACvF,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,YAAY,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,EAAG;AAAA,QACtJ,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,OAAO,aAAa,qBAAsB;AAAA,QACnG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,aAAa,UAAU,MAAM,aAAa,eAAgB;AAAA,QAChG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,aAAa,UAAU,OAAO,aAAa,cAAe;AAAA,QAChG,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,iBAAiB,UAAU,OAAO,aAAa,UAAU,QAAQ,IAAK;AAAA,QAC1G,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,WAAW,QAAQ,IAAK;AAAA,QAClG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,YAAY,UAAU,KAAM;AAAA,QACrE,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,SAAS,CAAC,QAAQ,QAAQ,QAAQ,IAAI,EAAG;AAAA,QAC/G,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,KAAK,0BAA2B;AAAA,QACjG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,yBAA0B;AAAA,MAChH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,cAAc;AAAA,EACzB,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,cAAe;AAAA,QACzF,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,SAAS,CAAC,OAAO,OAAO,OAAO,KAAK,EAAG;AAAA,QAC7G,EAAE,MAAM,YAAY,OAAO,OAAO,MAAM,iBAAiB,UAAU,KAAM;AAAA,QACzE,EAAE,MAAM,YAAY,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACvE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,iBAAiB,UAAU,MAAM,aAAa,eAAgB;AAAA,QACpG,EAAE,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,UAAU,OAAO,aAAa,eAAgB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,WAAW,UAAU,OAAO,aAAa,eAAgB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,MAAM,aAAa,gBAAiB;AAAA,QAC7F,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,OAAO,aAAa,kBAAmB;AAAA,MAC1G;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,QAAQ,UAAU,KAAM;AAAA,QAC5D,EAAE,MAAM,UAAU,OAAO,MAAM,MAAM,UAAU,UAAU,MAAM,SAAS,CAAC,KAAK,GAAG,EAAG;AAAA,QACpF,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,OAAO,UAAU,MAAM,WAAW,SAAU;AAAA,QAChF,EAAE,MAAM,UAAU,OAAO,MAAM,MAAM,aAAa,UAAU,MAAM,SAAS,CAAC,SAAS,SAAS,MAAM,MAAM,MAAM,OAAO,EAAG;AAAA,QAC1H,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,cAAc,UAAU,MAAM,aAAa,YAAa;AAAA,MAC/F;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,mBAAmB,UAAU,KAAM;AAAA,QACzE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,kBAAkB,UAAU,MAAM,aAAa,kBAAmB;AAAA,QACxG,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,aAAa,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,MAAM,KAAK,EAAG;AAAA,QACxG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,mBAAmB,UAAU,MAAO;AAAA,QAC7E,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,aAAa,oBAAqB;AAAA,QAC3G,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,OAAO,KAAK,kBAAmB;AAAA,MAC3F;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,YAAa;AAAA,QACvF,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,aAAa,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,IAAI,EAAG;AAAA,QACjG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,aAAa,YAAa;AAAA,QAC1F,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,QAAQ,UAAU,MAAM,aAAa,WAAW,QAAQ,IAAK;AAAA,QACjG,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,WAAY;AAAA,QACtF,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,eAAe,UAAU,OAAO,aAAa,UAAW;AAAA,QAC5F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,aAAa,UAAU,KAAM;AAAA,QACnE,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,YAAY,UAAU,KAAM;AAAA,QACrE,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,UAAU,QAAQ,MAAO;AAAA,QACnG,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,cAAc,UAAU,OAAO,SAAS,CAAC,KAAK,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,IAAI,GAAG,UAAU,GAAI;AAAA,QAC9K,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,KAAK,kBAAmB;AAAA,QACzF,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,uBAAwB;AAAA,MAC9G;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,YAAa;AAAA,QACvF,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,aAAa,YAAa;AAAA,QAC1F,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,QAAQ,UAAU,MAAM,aAAa,WAAW,QAAQ,IAAK;AAAA,QACjG,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,WAAY;AAAA,QACtF,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,eAAe,UAAU,OAAO,aAAa,UAAW;AAAA,QAC5F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,cAAc,UAAU,MAAM,aAAa,eAAgB;AAAA,QACjG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,aAAa,UAAU,OAAO,aAAa,YAAa;AAAA,QAC9F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,aAAa,UAAU,KAAM;AAAA,QACnE,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,YAAY,UAAU,KAAM;AAAA,QACrE,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,SAAS,QAAQ,KAAM;AAAA,QACjG,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,aAAa,UAAU,OAAO,aAAa,SAAS,QAAQ,MAAO;AAAA,QACvG,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,SAAS,CAAC,OAAO,SAAS,OAAO,MAAM,MAAM,MAAM,EAAG;AAAA,QAC7H,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,aAAa,UAAU,OAAO,SAAS,CAAC,OAAO,OAAO,OAAO,IAAI,EAAG;AAAA,QAC3G,EAAE,MAAM,QAAQ,OAAO,QAAQ,MAAM,YAAY,UAAU,OAAO,SAAS,CAAC,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,GAAG,UAAU,EAAG;AAAA,QAClK,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,KAAK,kBAAmB;AAAA,QACzF,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,uBAAwB;AAAA,MAC9G;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;AAGY,MAAC,cAAc;AAAA,EACzB,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,YAAa;AAAA,QACvF,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,YAAY,UAAU,MAAM,aAAa,eAAgB;AAAA,QAC/F,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,oBAAoB,UAAU,MAAM,aAAa,aAAc;AAAA,QACrG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,WAAW,UAAU,MAAM,aAAa,WAAW,QAAQ,MAAO;AAAA,QACxG,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,gBAAgB,UAAU,OAAO,aAAa,eAAgB;AAAA,QAClG,EAAE,MAAM,UAAU,OAAO,OAAO,MAAM,gBAAgB,UAAU,OAAO,SAAS,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,EAAG;AAAA,QACpH,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,oBAAoB,UAAU,OAAO,SAAS,CAAC,MAAM,MAAM,MAAM,IAAI,EAAG;AAAA,QAC/G,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,WAAW,UAAU,MAAM,SAAS,CAAC,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,IAAI,EAAG;AAAA,QAC1H,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,UAAU,OAAO,aAAa,WAAY;AAAA,QACzF,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,iBAAiB,UAAU,OAAO,SAAS,CAAC,UAAU,MAAM,MAAM,OAAO,EAAG;AAAA,QACnH,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,oBAAoB,UAAU,OAAO,aAAa,aAAc;AAAA,QACtG,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,mBAAmB,UAAU,OAAO,aAAa,aAAc;AAAA,QACrG,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,SAAS,UAAU,MAAM,aAAa,SAAS,QAAQ,KAAM;AAAA,QACjG,EAAE,MAAM,UAAU,OAAO,QAAQ,MAAM,UAAU,UAAU,MAAM,KAAK,yBAA0B;AAAA,QAChG,EAAE,MAAM,YAAY,OAAO,QAAQ,MAAM,eAAe,UAAU,MAAM,aAAa,4BAA6B;AAAA,MACnH;AAAA,IACF;AAAA,IACD;AAAA,MACE,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,eAAe,UAAU,KAAM;AAAA,QACpE,EAAE,MAAM,SAAS,OAAO,QAAQ,MAAM,gBAAgB,UAAU,MAAM,WAAW,SAAU;AAAA,QAC3F,EAAE,MAAM,SAAS,OAAO,OAAO,MAAM,UAAU,UAAU,MAAO;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}