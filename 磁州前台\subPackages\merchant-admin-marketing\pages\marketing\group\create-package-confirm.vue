<!-- 创建中 -->
<template>
  <view class="create-package-confirm-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">拼团活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 步骤指示器 -->
    <view class="step-indicator">
      <view class="step-progress">
        <view class="step-progress-bar" style="width: 100%"></view>
      </view>
      <view class="step-text">步骤 5/5</view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
      <view class="page-title">确认套餐信息</view>
      <view class="page-subtitle">请确认团购套餐信息无误后提交</view>
      
      <!-- 套餐基本信息 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
          <view class="edit-btn" @tap="editSection('info')">编辑</view>
        </view>
        
        <view class="section-content">
          <view class="info-item">
            <text class="info-label">套餐名称</text>
            <text class="info-value">{{packageInfo.name}}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">套餐分类</text>
            <text class="info-value">{{packageInfo.category}}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">成团人数</text>
            <text class="info-value">{{packageInfo.groupSize}}人</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">活动有效期</text>
            <text class="info-value">{{packageInfo.startDate}} 至 {{packageInfo.endDate}}</text>
          </view>
          
          <view class="info-item" v-if="packageInfo.description">
            <text class="info-label">套餐描述</text>
            <text class="info-value">{{packageInfo.description}}</text>
          </view>
        </view>
      </view>
      
      <!-- 套餐价格信息 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">价格信息</text>
          <view class="edit-btn" @tap="editSection('price')">编辑</view>
        </view>
        
        <view class="section-content">
          <view class="info-item">
            <text class="info-label">市场价</text>
            <text class="info-value">¥{{priceInfo.marketPrice}}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">日常价</text>
            <text class="info-value">¥{{priceInfo.regularPrice}}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">拼团价</text>
            <text class="info-value price">¥{{priceInfo.groupPrice}}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">单人限购</text>
            <text class="info-value">{{priceInfo.limitPerUser > 0 ? priceInfo.limitPerUser + '件' : '不限购'}}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">库存数量</text>
            <text class="info-value">{{priceInfo.stock}}件</text>
          </view>
        </view>
      </view>
      
      <!-- 套餐内容信息 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">套餐内容</text>
          <view class="edit-btn" @tap="editSection('items')">编辑</view>
        </view>
        
        <view class="section-content">
          <view v-for="(item, index) in packageItems" :key="index" class="package-item">
            <view class="item-header">
              <text class="item-title">套餐项 {{index + 1}}</text>
            </view>
            
            <view class="item-content">
              <view class="item-info">
                <text class="info-label">名称:</text>
                <text class="info-value">{{item.name}}</text>
              </view>
              
              <view class="item-info">
                <text class="info-label">数量:</text>
                <text class="info-value">{{item.quantity}} {{item.unit}}</text>
              </view>
              
              <view class="item-info">
                <text class="info-label">原价:</text>
                <text class="info-value">¥{{item.price}}</text>
              </view>
              
              <view class="item-info" v-if="item.description">
                <text class="info-label">描述:</text>
                <text class="info-value">{{item.description}}</text>
              </view>
            </view>
          </view>
          
          <view class="total-value">
            <text>套餐原价总值: </text>
            <text class="value">¥{{calculateTotalValue()}}</text>
          </view>
        </view>
      </view>
      
      <!-- 支付和核销设置 -->
      <view class="info-section">
        <view class="section-header">
          <text class="section-title">支付和核销设置</text>
          <view class="edit-btn" @tap="editSection('payment')">编辑</view>
        </view>
        
        <view class="section-content">
          <view class="info-item">
            <text class="info-label">支付方式</text>
            <text class="info-value">{{paymentInfo.paymentType === 'online' ? '线上支付' : '到店支付'}}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">核销方式</text>
            <text class="info-value">{{paymentInfo.verifyType === 'online' ? '线上核销' : '到店核销'}}</text>
          </view>
          
          <view class="info-item" v-if="paymentInfo.verifyType === 'offline'">
            <text class="info-label">核销码有效期</text>
            <text class="info-value">{{paymentInfo.verifyCodeValidDays}}天</text>
          </view>
          
          <view class="info-item" v-if="paymentInfo.verifyType === 'offline'">
            <text class="info-label">可核销次数</text>
            <text class="info-value">{{paymentInfo.verifyTimes}}次</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="btn btn-secondary" @click="goBack">上一步</button>
      <button class="btn btn-primary" @click="submitPackage">提交套餐</button>
    </view>
    
    <!-- 提交成功弹窗 -->
    <view class="modal" v-if="showSuccessModal">
      <view class="modal-mask"></view>
      <view class="modal-content success-modal">
        <view class="success-icon">
          <view class="checkmark"></view>
        </view>
        <view class="success-title">创建成功</view>
        <view class="success-message">团购套餐已成功创建</view>
        <button class="success-btn" @tap="goToList">返回套餐列表</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      packageInfo: {
        name: '',
        category: '',
        groupSize: 2,
        startDate: '',
        endDate: '',
        description: ''
      },
      priceInfo: {
        marketPrice: '0.00',
        regularPrice: '0.00',
        groupPrice: '0.00',
        limitPerUser: 1,
        stock: 100
      },
      packageItems: [],
      paymentInfo: {
        paymentType: 'offline', // offline: 到店支付, online: 线上支付
        verifyType: 'offline', // offline: 到店核销, online: 线上核销
        verifyCodeValidDays: 30,
        verifyTimes: 1
      },
      showSuccessModal: false
    }
  },
  onLoad() {
    // 尝试从本地存储获取之前保存的数据
    try {
      const savedInfo = uni.getStorageSync('packageInfo');
      if (savedInfo) {
        this.packageInfo = JSON.parse(savedInfo);
      }
      
      const savedPriceInfo = uni.getStorageSync('packagePriceInfo');
      if (savedPriceInfo) {
        this.priceInfo = JSON.parse(savedPriceInfo);
      }
      
      const savedItems = uni.getStorageSync('packageItems');
      if (savedItems) {
        this.packageItems = JSON.parse(savedItems);
      }
      
      const savedPaymentInfo = uni.getStorageSync('packagePaymentInfo');
      if (savedPaymentInfo) {
        this.paymentInfo = JSON.parse(savedPaymentInfo);
      } else {
        // 如果没有保存过支付信息，则使用默认值
        uni.setStorageSync('packagePaymentInfo', JSON.stringify(this.paymentInfo));
      }
    } catch (e) {
      console.error('读取本地存储失败:', e);
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    editSection(section) {
      switch (section) {
        case 'info':
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info'
          });
          break;
        case 'price':
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price'
          });
          break;
        case 'items':
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items'
          });
          break;
        case 'payment':
          // 这里可以添加支付和核销设置页面的跳转
          uni.showToast({
            title: '支付和核销设置功能开发中',
            icon: 'none'
          });
          break;
      }
    },
    calculateTotalValue() {
      let total = 0;
      this.packageItems.forEach(item => {
        total += parseFloat(item.price) * parseFloat(item.quantity);
      });
      return total.toFixed(2);
    },
    submitPackage() {
      // 显示加载中
      uni.showLoading({
        title: '提交中...'
      });
      
      // 模拟提交数据
      setTimeout(() => {
        // 隐藏加载中
        uni.hideLoading();
        
        // 显示成功弹窗
        this.showSuccessModal = true;
        
        // 清除本地存储的数据
        try {
          uni.removeStorageSync('packageInfo');
          uni.removeStorageSync('packagePriceInfo');
          uni.removeStorageSync('packageItems');
          uni.removeStorageSync('packagePaymentInfo');
          uni.removeStorageSync('packageType');
        } catch (e) {
          console.error('清除本地存储失败:', e);
        }
      }, 1500);
    },
    goToList() {
      // 跳转到套餐列表页面
      uni.redirectTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/package-management'
      });
    },
    showHelp() {
      uni.showToast({
        title: '帮助信息',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.create-package-confirm-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
}

/* 步骤指示器 */
.step-indicator {
  padding: 15px;
  background: #FFFFFF;
}

.step-progress {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 5px;
  position: relative;
}

.step-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}

.step-text {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 20px 15px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.page-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

/* 信息区块样式 */
.info-section {
  background: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #EBEDF5;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.edit-btn {
  font-size: 14px;
  color: #9040FF;
}

.section-content {
  padding: 15px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.info-value.price {
  color: #FF3B30;
  font-weight: 600;
}

/* 套餐项样式 */
.package-item {
  background: #F9F9F9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
}

.package-item:last-child {
  margin-bottom: 0;
}

.item-header {
  margin-bottom: 10px;
}

.item-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.item-info {
  display: flex;
  font-size: 13px;
}

.total-value {
  margin-top: 15px;
  text-align: right;
  font-size: 14px;
  color: #666;
}

.total-value .value {
  font-weight: 600;
  color: #333;
}

/* 底部按钮 */
.footer-buttons {
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}

.btn-secondary {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}

/* 成功弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #FFFFFF;
  border-radius: 12px;
  padding: 30px;
  width: 80%;
  max-width: 300px;
}

.success-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  width: 60px;
  height: 60px;
  background: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.checkmark {
  width: 30px;
  height: 15px;
  border-left: 3px solid #fff;
  border-bottom: 3px solid #fff;
  transform: rotate(-45deg);
}

.success-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.success-message {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.success-btn {
  width: 100%;
  height: 45px;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
  border-radius: 22.5px;
  font-size: 16px;
  font-weight: 600;
  border: none;
}
</style>
