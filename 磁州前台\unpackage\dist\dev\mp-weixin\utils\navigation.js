"use strict";
const common_vendor = require("../common/vendor.js");
const navigateTo = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      if (!url || typeof url !== "string") {
        const error = new Error("导航URL无效");
        error.code = "INVALID_URL";
        if (options.showToast !== false) {
          common_vendor.index.showToast({
            title: error.message,
            icon: "none",
            duration: 2e3
          });
        }
        return reject(error);
      }
      common_vendor.index.navigateTo({
        url,
        success: (res) => {
          common_vendor.index.__f__("log", "at utils/navigation.js:36", "页面导航成功:", url);
          resolve(res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at utils/navigation.js:40", "页面导航失败:", err);
          let errorMessage = "页面跳转失败";
          if (err.errMsg && err.errMsg.includes("is not found")) {
            errorMessage = "该功能正在开发中";
          } else if (err.errMsg && err.errMsg.includes("can not navigate")) {
            errorMessage = "无法导航到该页面";
          }
          if (options.showToast !== false) {
            common_vendor.index.showToast({
              title: options.failMessage || errorMessage,
              icon: "none",
              duration: 2e3
            });
          }
          reject(err);
        }
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/navigation.js:64", "导航方法异常:", error);
      if (options.showToast !== false) {
        common_vendor.index.showToast({
          title: "系统异常，请稍后再试",
          icon: "none",
          duration: 2e3
        });
      }
      reject(error);
    }
  });
};
const switchTab = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      common_vendor.index.switchTab({
        url,
        success: (res) => {
          common_vendor.index.__f__("log", "at utils/navigation.js:138", "切换到Tab页面成功:", url);
          resolve(res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at utils/navigation.js:142", "切换到Tab页面失败:", err);
          if (options.showToast !== false) {
            common_vendor.index.showToast({
              title: options.failMessage || "页面切换失败",
              icon: "none",
              duration: 2e3
            });
          }
          reject(err);
        }
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/navigation.js:156", "切换Tab方法异常:", error);
      if (options.showToast !== false) {
        common_vendor.index.showToast({
          title: "系统异常，请稍后再试",
          icon: "none",
          duration: 2e3
        });
      }
      reject(error);
    }
  });
};
const smartNavigate = (urlOrConfig, options = {}) => {
  let url = urlOrConfig;
  if (typeof urlOrConfig === "object" && urlOrConfig !== null) {
    url = urlOrConfig.url;
    if (urlOrConfig.options) {
      options = { ...options, ...urlOrConfig.options };
    }
  }
  if (typeof url !== "string") {
    const error = new Error("导航URL无效");
    common_vendor.index.__f__("error", "at utils/navigation.js:193", "smartNavigate 参数错误:", urlOrConfig);
    return Promise.reject(error);
  }
  const tabPages = [
    "/pages/index/index",
    "/pages/recommend/index",
    "/pages/publish/index",
    "/pages/message/index",
    "/pages/my/my"
  ];
  const isTabPage = tabPages.some((tabUrl) => url === tabUrl || url.startsWith(tabUrl + "?"));
  if (isTabPage) {
    return switchTab(url, options);
  } else {
    return navigateTo(url, options);
  }
};
exports.smartNavigate = smartNavigate;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/navigation.js.map
