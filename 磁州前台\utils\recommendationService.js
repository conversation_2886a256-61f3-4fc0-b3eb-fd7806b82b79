import secureRequest from './secureRequest';
import securityUtils from './securityUtils';
import securityConfig from './securityConfig';
import securityAudit from './securityAudit';

/**
 * 商品推荐算法服务类
 * 负责智能推荐商品、个性化内容等
 */
class RecommendationService {
  constructor() {
    // API基础路径
    this.baseUrl = securityConfig.getApiBaseUrl();
    
    // 推荐类型
    this.recommendTypes = {
      PERSONAL: 'personal',     // 个性化推荐
      POPULAR: 'popular',       // 热门推荐
      SIMILAR: 'similar',       // 相似商品
      RELATED: 'related',       // 相关商品
      HISTORY: 'history',       // 历史浏览
      COLLABORATIVE: 'collaborative' // 协同过滤
    };
    
    // 用户行为类型
    this.userActionTypes = {
      VIEW: 'view',       // 浏览商品
      SEARCH: 'search',   // 搜索关键词
      CLICK: 'click',     // 点击商品
      FAVORITE: 'favorite', // 收藏商品
      SHARE: 'share',     // 分享商品
      BUY: 'buy',         // 购买商品
      CART: 'cart'        // 加入购物车
    };
    
    // 本地缓存键
    this.cacheKeys = {
      USER_PREFERENCES: 'user_preferences',
      BROWSE_HISTORY: 'browse_history',
      SEARCH_HISTORY: 'search_history'
    };
    
    // 初始化本地缓存
    this.initLocalCache();
  }
  
  /**
   * 初始化本地缓存
   */
  initLocalCache() {
    try {
      // 浏览历史
      if (!uni.getStorageSync(this.cacheKeys.BROWSE_HISTORY)) {
        uni.setStorageSync(this.cacheKeys.BROWSE_HISTORY, []);
      }
      
      // 搜索历史
      if (!uni.getStorageSync(this.cacheKeys.SEARCH_HISTORY)) {
        uni.setStorageSync(this.cacheKeys.SEARCH_HISTORY, []);
      }
      
      // 用户偏好
      if (!uni.getStorageSync(this.cacheKeys.USER_PREFERENCES)) {
        uni.setStorageSync(this.cacheKeys.USER_PREFERENCES, {
          categories: {},
          keywords: {},
          platforms: {}
        });
      }
    } catch (error) {
      console.error('初始化本地缓存失败', error);
    }
  }
  
  /**
   * 获取个性化推荐商品
   * @param {Object} params 推荐参数
   * @param {Number} params.page 页码
   * @param {Number} params.pageSize 每页数量
   * @param {String} params.platform 平台ID
   * @returns {Promise} 推荐商品列表
   */
  async getPersonalRecommendations(params = {}) {
    try {
      // 获取本地用户偏好
      const userPreferences = this.getUserPreferences();
      
      // 合并参数
      const requestParams = {
        ...params,
        type: this.recommendTypes.PERSONAL,
        preferences: securityUtils.encrypt(JSON.stringify(userPreferences))
      };
      
      const response = await secureRequest.post(`${this.baseUrl}/recommend/products`, requestParams);
      securityAudit.log('获取个性化推荐', true);
      
      return response.data;
    } catch (error) {
      securityAudit.log('获取个性化推荐失败', false, error);
      throw error;
    }
  }
  
  /**
   * 获取热门推荐商品
   * @param {Object} params 推荐参数
   * @returns {Promise} 热门商品列表
   */
  async getPopularRecommendations(params = {}) {
    try {
      const requestParams = {
        ...params,
        type: this.recommendTypes.POPULAR
      };
      
      const response = await secureRequest.get(`${this.baseUrl}/recommend/popular`, {
        params: requestParams
      });
      securityAudit.log('获取热门推荐', true);
      
      return response.data;
    } catch (error) {
      securityAudit.log('获取热门推荐失败', false, error);
      throw error;
    }
  }
  
  /**
   * 获取相似商品推荐
   * @param {String} productId 商品ID
   * @param {String} platform 平台ID
   * @param {Number} limit 返回数量
   * @returns {Promise} 相似商品列表
   */
  async getSimilarProducts(productId, platform, limit = 10) {
    try {
      const response = await secureRequest.get(`${this.baseUrl}/recommend/similar`, {
        params: {
          productId,
          platform,
          limit,
          type: this.recommendTypes.SIMILAR
        }
      });
      securityAudit.log('获取相似商品', true);
      
      return response.data;
    } catch (error) {
      securityAudit.log('获取相似商品失败', false, error);
      throw error;
    }
  }
  
  /**
   * 获取相关商品推荐
   * @param {String} productId 商品ID
   * @param {String} platform 平台ID
   * @param {Number} limit 返回数量
   * @returns {Promise} 相关商品列表
   */
  async getRelatedProducts(productId, platform, limit = 10) {
    try {
      const response = await secureRequest.get(`${this.baseUrl}/recommend/related`, {
        params: {
          productId,
          platform,
          limit,
          type: this.recommendTypes.RELATED
        }
      });
      securityAudit.log('获取相关商品', true);
      
      return response.data;
    } catch (error) {
      securityAudit.log('获取相关商品失败', false, error);
      throw error;
    }
  }
  
  /**
   * 获取浏览历史推荐
   * @param {Number} limit 返回数量
   * @returns {Promise} 历史浏览商品列表
   */
  async getHistoryRecommendations(limit = 20) {
    try {
      // 获取本地浏览历史
      const browseHistory = this.getBrowseHistory();
      
      // 如果本地历史为空，则返回热门推荐
      if (!browseHistory || browseHistory.length === 0) {
        return this.getPopularRecommendations({ limit });
      }
      
      const response = await secureRequest.post(`${this.baseUrl}/recommend/history`, {
        history: securityUtils.encrypt(JSON.stringify(browseHistory)),
        limit,
        type: this.recommendTypes.HISTORY
      });
      securityAudit.log('获取历史推荐', true);
      
      return response.data;
    } catch (error) {
      securityAudit.log('获取历史推荐失败', false, error);
      throw error;
    }
  }
  
  /**
   * 获取协同过滤推荐
   * @param {Number} limit 返回数量
   * @returns {Promise} 协同过滤推荐商品列表
   */
  async getCollaborativeRecommendations(limit = 20) {
    try {
      const response = await secureRequest.get(`${this.baseUrl}/recommend/collaborative`, {
        params: {
          limit,
          type: this.recommendTypes.COLLABORATIVE
        }
      });
      securityAudit.log('获取协同过滤推荐', true);
      
      return response.data;
    } catch (error) {
      securityAudit.log('获取协同过滤推荐失败', false, error);
      throw error;
    }
  }
  
  /**
   * 记录用户行为
   * @param {Object} action 用户行为
   * @param {String} action.type 行为类型
   * @param {String} action.productId 商品ID
   * @param {String} action.platform 平台ID
   * @param {Object} action.extra 额外数据
   */
  async recordUserAction(action) {
    try {
      // 本地记录用户行为
      this.updateLocalPreferences(action);
      
      // 发送到服务器
      await secureRequest.post(`${this.baseUrl}/recommend/action`, action);
      securityAudit.log('记录用户行为', true, { actionType: action.type });
    } catch (error) {
      securityAudit.log('记录用户行为失败', false, error);
      console.error('记录用户行为失败', error);
    }
  }
  
  /**
   * 记录商品浏览
   * @param {Object} product 商品信息
   */
  recordProductView(product) {
    // 添加到浏览历史
    this.addToBrowseHistory(product);
    
    // 记录用户行为
    this.recordUserAction({
      type: this.userActionTypes.VIEW,
      productId: product.id,
      platform: product.platform,
      extra: {
        categoryId: product.categoryId,
        price: product.price,
        title: product.title
      }
    });
  }
  
  /**
   * 记录商品搜索
   * @param {String} keyword 搜索关键词
   * @param {String} platform 平台ID
   */
  recordSearch(keyword, platform) {
    // 添加到搜索历史
    this.addToSearchHistory(keyword);
    
    // 记录用户行为
    this.recordUserAction({
      type: this.userActionTypes.SEARCH,
      platform,
      extra: {
        keyword
      }
    });
  }
  
  /**
   * 添加到浏览历史
   * @param {Object} product 商品信息
   */
  addToBrowseHistory(product) {
    try {
      let history = uni.getStorageSync(this.cacheKeys.BROWSE_HISTORY) || [];
      
      // 移除已存在的相同商品
      history = history.filter(item => !(item.id === product.id && item.platform === product.platform));
      
      // 添加到历史开头
      history.unshift({
        id: product.id,
        platform: product.platform,
        title: product.title,
        image: product.image,
        price: product.price,
        timestamp: new Date().getTime()
      });
      
      // 限制历史记录数量
      if (history.length > 100) {
        history = history.slice(0, 100);
      }
      
      uni.setStorageSync(this.cacheKeys.BROWSE_HISTORY, history);
    } catch (error) {
      console.error('添加浏览历史失败', error);
    }
  }
  
  /**
   * 添加到搜索历史
   * @param {String} keyword 搜索关键词
   */
  addToSearchHistory(keyword) {
    try {
      let history = uni.getStorageSync(this.cacheKeys.SEARCH_HISTORY) || [];
      
      // 移除已存在的相同关键词
      history = history.filter(item => item.keyword !== keyword);
      
      // 添加到历史开头
      history.unshift({
        keyword,
        timestamp: new Date().getTime()
      });
      
      // 限制历史记录数量
      if (history.length > 50) {
        history = history.slice(0, 50);
      }
      
      uni.setStorageSync(this.cacheKeys.SEARCH_HISTORY, history);
    } catch (error) {
      console.error('添加搜索历史失败', error);
    }
  }
  
  /**
   * 获取浏览历史
   * @param {Number} limit 返回数量
   * @returns {Array} 浏览历史
   */
  getBrowseHistory(limit = 50) {
    try {
      const history = uni.getStorageSync(this.cacheKeys.BROWSE_HISTORY) || [];
      return limit ? history.slice(0, limit) : history;
    } catch (error) {
      console.error('获取浏览历史失败', error);
      return [];
    }
  }
  
  /**
   * 获取搜索历史
   * @param {Number} limit 返回数量
   * @returns {Array} 搜索历史
   */
  getSearchHistory(limit = 20) {
    try {
      const history = uni.getStorageSync(this.cacheKeys.SEARCH_HISTORY) || [];
      return limit ? history.slice(0, limit) : history;
    } catch (error) {
      console.error('获取搜索历史失败', error);
      return [];
    }
  }
  
  /**
   * 清空浏览历史
   */
  clearBrowseHistory() {
    try {
      uni.setStorageSync(this.cacheKeys.BROWSE_HISTORY, []);
    } catch (error) {
      console.error('清空浏览历史失败', error);
    }
  }
  
  /**
   * 清空搜索历史
   */
  clearSearchHistory() {
    try {
      uni.setStorageSync(this.cacheKeys.SEARCH_HISTORY, []);
    } catch (error) {
      console.error('清空搜索历史失败', error);
    }
  }
  
  /**
   * 获取用户偏好
   * @returns {Object} 用户偏好
   */
  getUserPreferences() {
    try {
      return uni.getStorageSync(this.cacheKeys.USER_PREFERENCES) || {
        categories: {},
        keywords: {},
        platforms: {}
      };
    } catch (error) {
      console.error('获取用户偏好失败', error);
      return {
        categories: {},
        keywords: {},
        platforms: {}
      };
    }
  }
  
  /**
   * 更新本地用户偏好
   * @param {Object} action 用户行为
   */
  updateLocalPreferences(action) {
    try {
      const preferences = this.getUserPreferences();
      
      // 根据不同行为类型更新偏好
      switch (action.type) {
        case this.userActionTypes.VIEW:
          // 更新分类偏好
          if (action.extra && action.extra.categoryId) {
            const categoryId = action.extra.categoryId;
            preferences.categories[categoryId] = (preferences.categories[categoryId] || 0) + 1;
          }
          
          // 更新平台偏好
          if (action.platform) {
            preferences.platforms[action.platform] = (preferences.platforms[action.platform] || 0) + 1;
          }
          break;
          
        case this.userActionTypes.SEARCH:
          // 更新关键词偏好
          if (action.extra && action.extra.keyword) {
            const keywords = action.extra.keyword.split(/\s+/).filter(Boolean);
            keywords.forEach(keyword => {
              preferences.keywords[keyword] = (preferences.keywords[keyword] || 0) + 1;
            });
          }
          
          // 更新平台偏好
          if (action.platform) {
            preferences.platforms[action.platform] = (preferences.platforms[action.platform] || 0) + 1;
          }
          break;
          
        case this.userActionTypes.BUY:
          // 购买行为权重更高
          // 更新分类偏好
          if (action.extra && action.extra.categoryId) {
            const categoryId = action.extra.categoryId;
            preferences.categories[categoryId] = (preferences.categories[categoryId] || 0) + 5;
          }
          
          // 更新平台偏好
          if (action.platform) {
            preferences.platforms[action.platform] = (preferences.platforms[action.platform] || 0) + 5;
          }
          break;
          
        default:
          break;
      }
      
      // 保存更新后的偏好
      uni.setStorageSync(this.cacheKeys.USER_PREFERENCES, preferences);
    } catch (error) {
      console.error('更新用户偏好失败', error);
    }
  }
  
  /**
   * 同步用户偏好到服务器
   */
  async syncUserPreferences() {
    try {
      const preferences = this.getUserPreferences();
      
      await secureRequest.post(`${this.baseUrl}/recommend/sync/preferences`, {
        preferences: securityUtils.encrypt(JSON.stringify(preferences))
      });
      
      securityAudit.log('同步用户偏好', true);
    } catch (error) {
      securityAudit.log('同步用户偏好失败', false, error);
      console.error('同步用户偏好失败', error);
    }
  }
  
  /**
   * 从服务器获取用户偏好
   */
  async fetchUserPreferences() {
    try {
      const response = await secureRequest.get(`${this.baseUrl}/recommend/preferences`);
      
      if (response.data && response.data.preferences) {
        uni.setStorageSync(this.cacheKeys.USER_PREFERENCES, response.data.preferences);
      }
      
      securityAudit.log('获取用户偏好', true);
    } catch (error) {
      securityAudit.log('获取用户偏好失败', false, error);
      console.error('获取用户偏好失败', error);
    }
  }
}

// 导出单例
const recommendationService = new RecommendationService();
export default recommendationService; 