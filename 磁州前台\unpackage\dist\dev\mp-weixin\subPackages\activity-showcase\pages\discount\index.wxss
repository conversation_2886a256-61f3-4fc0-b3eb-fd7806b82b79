/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-bb3ebeba, html.data-v-bb3ebeba, #app.data-v-bb3ebeba, .index-container.data-v-bb3ebeba {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.discount-page.data-v-bb3ebeba {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-bb3ebeba {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-bb3ebeba {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
}
.custom-navbar .navbar-content.data-v-bb3ebeba {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding-top: var(--status-bar-height, 25px);
  padding-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
}
.custom-navbar .navbar-content .back-btn.data-v-bb3ebeba {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-navbar .navbar-content .back-icon.data-v-bb3ebeba {
  width: 100%;
  height: 100%;
}
.custom-navbar .navbar-content .navbar-title.data-v-bb3ebeba {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.custom-navbar .navbar-content .navbar-right.data-v-bb3ebeba {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.custom-navbar .navbar-content .navbar-right .close-btn.data-v-bb3ebeba {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 筛选选项卡 */
.filter-tabs.data-v-bb3ebeba {
  position: relative;
  display: flex;
  background-color: #FFFFFF;
  height: 88rpx;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  overflow-x: auto;
  white-space: nowrap;
}
.filter-tabs .tab-item.data-v-bb3ebeba {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  height: 100%;
}
.filter-tabs .tab-item.active.data-v-bb3ebeba {
  color: #5856D6;
  font-weight: 600;
}
.filter-tabs .tab-item.active.data-v-bb3ebeba::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #5856D6;
  border-radius: 2rpx;
}

/* 内容区域 */
.content-scroll.data-v-bb3ebeba {
  flex: 1;
  width: 100%;
}

/* 满减活动列表 */
.discount-list.data-v-bb3ebeba {
  padding: 20rpx;
}
.discount-item.data-v-bb3ebeba {
  margin-bottom: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.discount-item .merchant-info.data-v-bb3ebeba {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #F2F2F7;
}
.discount-item .merchant-info .merchant-logo.data-v-bb3ebeba {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 16rpx;
}
.discount-item .merchant-info .merchant-name.data-v-bb3ebeba {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.discount-item .merchant-info .discount-tag.data-v-bb3ebeba {
  padding: 4rpx 12rpx;
  font-size: 22rpx;
  color: #FFFFFF;
  border-radius: 10rpx;
  background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
}
.discount-item .discount-rules.data-v-bb3ebeba {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
}
.discount-item .discount-rules .rule-item.data-v-bb3ebeba {
  margin-right: 20rpx;
  margin-bottom: 10rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background-color: #F2F2F7;
}
.discount-item .discount-rules .rule-item.highlight.data-v-bb3ebeba {
  background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
}
.discount-item .discount-rules .rule-item.highlight .rule-text.data-v-bb3ebeba {
  color: #FFFFFF;
}
.discount-item .discount-rules .rule-item .rule-text.data-v-bb3ebeba {
  font-size: 24rpx;
  color: #666666;
}
.discount-item .discount-footer.data-v-bb3ebeba {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-top: 1px solid #F2F2F7;
}
.discount-item .discount-footer .discount-time.data-v-bb3ebeba {
  font-size: 24rpx;
  color: #999999;
}
.discount-item .discount-footer .discount-btn.data-v-bb3ebeba {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  width: 160rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #5E5CE6 0%, #5856D6 100%);
  font-size: 26rpx;
  font-weight: 500;
  color: #FFFFFF;
}

/* 加载更多和到底了提示 */
.loading-more.data-v-bb3ebeba, .no-more.data-v-bb3ebeba {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999999;
}