<template>
  <view class="materials-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">素材库</text>
      <view class="navbar-right">
        <view class="upload-icon" @click="showUploadOptions">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M17 8L12 3L7 8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 3V15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 分类切换 -->
    <view class="category-tabs">
      <view 
        v-for="(tab, index) in categoryTabs" 
        :key="index" 
        class="tab-item" 
        :class="{ active: activeTab === tab.value }"
        @click="switchTab(tab.value)"
      >
        <text>{{tab.name}}</text>
      </view>
    </view>
    
    <!-- 素材列表 -->
    <scroll-view class="materials-scroll" scroll-y>
      <view class="materials-grid">
        <view 
          v-for="(item, index) in filteredMaterials" 
          :key="index" 
          class="material-item"
          @click="previewMaterial(item)"
        >
          <image class="material-image" :src="item.url" mode="aspectFill"></image>
          <view class="material-footer">
            <text class="material-name">{{item.name}}</text>
            <view class="material-actions">
              <view class="action-btn share" @click.stop="shareMaterial(item)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="18" cy="5" r="3" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <circle cx="6" cy="12" r="3" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <circle cx="18" cy="19" r="3" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
              <view class="action-btn download" @click.stop="downloadMaterial(item)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M7 10L12 15L17 10" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 15V3" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 素材预览弹窗 -->
    <view v-if="showPreview" class="material-preview-modal" @click="closePreview">
      <view class="preview-content" @click.stop>
        <view class="preview-header">
          <text class="preview-title">{{currentMaterial.name}}</text>
          <view class="close-preview" @click="closePreview">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M6 6L18 18" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        
        <view class="preview-image-container">
          <image class="preview-image" :src="currentMaterial.url" mode="aspectFit"></image>
        </view>
        
        <view class="preview-info">
          <view class="info-item">
            <text class="info-label">类型：</text>
            <text class="info-value">{{currentMaterial.type === 'image' ? '图片' : '视频'}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">尺寸：</text>
            <text class="info-value">{{currentMaterial.size}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">上传时间：</text>
            <text class="info-value">{{currentMaterial.uploadDate}}</text>
          </view>
        </view>
        
        <view class="preview-actions">
          <button class="preview-btn share" @click="shareMaterial(currentMaterial)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="18" cy="5" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="6" cy="12" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="18" cy="19" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text>分享素材</text>
          </button>
          <button class="preview-btn download" @click="downloadMaterial(currentMaterial)">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7 10L12 15L17 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 15V3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text>保存到相册</text>
          </button>
        </view>
      </view>
    </view>
    
    <!-- 上传选项弹窗 -->
    <view v-if="showUpload" class="upload-options-modal" @click="closeUploadOptions">
      <view class="upload-options" @click.stop>
        <view class="upload-option" @click="uploadFromCamera">
          <view class="option-icon camera">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M23 19C23 19.5304 22.7893 20.0391 22.4142 20.4142C22.0391 20.7893 21.5304 21 21 21H3C2.46957 21 1.96086 20.7893 1.58579 20.4142C1.21071 20.0391 1 19.5304 1 19V8C1 7.46957 1.21071 6.96086 1.58579 6.58579C1.96086 6.21071 2.46957 6 3 6H7L9 3H15L17 6H21C21.5304 6 22.0391 6.21071 22.4142 6.58579C22.7893 6.96086 23 7.46957 23 8V19Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="13" r="4" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text class="option-text">拍摄</text>
        </view>
        
        <view class="upload-option" @click="uploadFromAlbum">
          <view class="option-icon album">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="18" height="18" rx="2" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="8.5" cy="8.5" r="1.5" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M21 15L16 10L5 21" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text class="option-text">从相册选择</text>
        </view>
        
        <button class="cancel-upload" @click="closeUploadOptions">取消</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';

// 分类选项卡
const categoryTabs = [
  { name: '全部', value: 'all' },
  { name: '图片', value: 'image' },
  { name: '视频', value: 'video' },
  { name: '我的上传', value: 'mine' }
];

// 素材列表
const materials = reactive([
  {
    id: 1,
    name: '店铺促销海报',
    url: '/static/images/materials/poster-1.jpg',
    type: 'image',
    category: 'store',
    size: '800×1200',
    uploadDate: '2023-06-01',
    isOwn: true
  },
  {
    id: 2,
    name: '618大促主图',
    url: '/static/images/materials/poster-2.jpg',
    type: 'image',
    category: 'activity',
    size: '1080×1080',
    uploadDate: '2023-05-20',
    isOwn: true
  },
  {
    id: 3,
    name: '商品展示视频',
    url: '/static/images/materials/video-1.jpg',
    type: 'video',
    category: 'product',
    size: '1080×1920',
    uploadDate: '2023-05-15',
    isOwn: false
  },
  {
    id: 4,
    name: '新品上市海报',
    url: '/static/images/materials/poster-3.jpg',
    type: 'image',
    category: 'product',
    size: '800×1200',
    uploadDate: '2023-05-10',
    isOwn: false
  },
  {
    id: 5,
    name: '端午节主题海报',
    url: '/static/images/materials/poster-4.jpg',
    type: 'image',
    category: 'festival',
    size: '1080×1920',
    uploadDate: '2023-05-05',
    isOwn: true
  },
  {
    id: 6,
    name: '店铺介绍视频',
    url: '/static/images/materials/video-2.jpg',
    type: 'video',
    category: 'store',
    size: '1080×1080',
    uploadDate: '2023-04-28',
    isOwn: false
  },
  {
    id: 7,
    name: '优惠券主图',
    url: '/static/images/materials/poster-5.jpg',
    type: 'image',
    category: 'coupon',
    size: '1080×720',
    uploadDate: '2023-04-20',
    isOwn: false
  },
  {
    id: 8,
    name: '会员专享海报',
    url: '/static/images/materials/poster-6.jpg',
    type: 'image',
    category: 'member',
    size: '800×1200',
    uploadDate: '2023-04-15',
    isOwn: true
  }
]);

// 状态变量
const activeTab = ref('all');
const showPreview = ref(false);
const showUpload = ref(false);
const currentMaterial = ref({});

// 计算属性
const filteredMaterials = computed(() => {
  if (activeTab.value === 'all') {
    return materials;
  } else if (activeTab.value === 'mine') {
    return materials.filter(item => item.isOwn);
  } else {
    return materials.filter(item => item.type === activeTab.value);
  }
});

// 方法
const goBack = () => {
  uni.navigateBack();
};

const switchTab = (tab) => {
  activeTab.value = tab;
};

const previewMaterial = (material) => {
  currentMaterial.value = material;
  showPreview.value = true;
};

const closePreview = () => {
  showPreview.value = false;
};

const shareMaterial = (material) => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const downloadMaterial = (material) => {
  uni.showLoading({
    title: '下载中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '已保存到相册',
      icon: 'success'
    });
  }, 1000);
};

const showUploadOptions = () => {
  showUpload.value = true;
};

const closeUploadOptions = () => {
  showUpload.value = false;
};

const uploadFromCamera = () => {
  uni.chooseImage({
    count: 1,
    sourceType: ['camera'],
    success: (res) => {
      uploadMaterial(res.tempFilePaths[0], 'camera');
    }
  });
};

const uploadFromAlbum = () => {
  uni.chooseImage({
    count: 9,
    sourceType: ['album'],
    success: (res) => {
      uploadMaterial(res.tempFilePaths[0], 'album');
    }
  });
};

const uploadMaterial = (path, source) => {
  closeUploadOptions();
  
  uni.showLoading({
    title: '上传中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟上传成功后添加到素材库
    const newMaterial = {
      id: materials.length + 1,
      name: `我的素材${materials.length + 1}`,
      url: path,
      type: 'image',
      category: 'other',
      size: '1080×1080',
      uploadDate: new Date().toISOString().split('T')[0],
      isOwn: true
    };
    
    materials.unshift(newMaterial);
    
    uni.showToast({
      title: '上传成功',
      icon: 'success'
    });
    
    // 切换到我的上传标签
    activeTab.value = 'mine';
  }, 1500);
};
</script>

<style lang="scss">
.materials-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  padding: 12px 16px;
  background-color: #FFFFFF;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 74px;
  z-index: 99;
}

.tab-item {
  padding: 8px 16px;
  border-radius: 16px;
  margin-right: 8px;
  background-color: #F0F0F0;
}

.tab-item.active {
  background-color: #6B0FBE;
  color: #FFFFFF;
}

.tab-item text {
  font-size: 14px;
  font-weight: 500;
}

/* 素材列表 */
.materials-scroll {
  height: calc(100vh - 130px);
  padding: 16px;
}

.materials-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding-bottom: 20px;
}

.material-item {
  width: calc(50% - 6px);
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.material-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.material-footer {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.material-name {
  font-size: 14px;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.material-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 30px;
  height: 30px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(107, 15, 190, 0.05);
}

/* 素材预览弹窗 */
.material-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.preview-content {
  width: 90%;
  max-width: 360px;
  background-color: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
}

.preview-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #F0F0F0;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-preview {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image-container {
  width: 100%;
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F5F7FA;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
}

.preview-info {
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #666;
  width: 80px;
}

.info-value {
  font-size: 14px;
  color: #333;
}

.preview-actions {
  display: flex;
  padding: 16px;
}

.preview-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  margin: 0 6px;
  border: none;
}

.preview-btn svg {
  margin-right: 6px;
}

.preview-btn.share {
  background-color: rgba(107, 15, 190, 0.05);
  color: #6B0FBE;
  border: 1px solid rgba(107, 15, 190, 0.2);
}

.preview-btn.download {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
}

/* 上传选项弹窗 */
.upload-options-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.upload-options {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 24px 24px 0 0;
  padding: 24px 16px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
}

.upload-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.option-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.option-icon.camera {
  background-color: rgba(107, 15, 190, 0.05);
}

.option-icon.album {
  background-color: rgba(0, 122, 255, 0.05);
}

.option-text {
  font-size: 16px;
  color: #333;
}

.cancel-upload {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  background-color: #F5F7FA;
  color: #6B0FBE;
  font-size: 16px;
  font-weight: 500;
  margin-top: 16px;
  border: none;
}
</style> 