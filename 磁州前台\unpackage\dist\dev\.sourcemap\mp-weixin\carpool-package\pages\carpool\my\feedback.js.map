{"version": 3, "file": "feedback.js", "sources": ["carpool-package/pages/carpool/my/feedback.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcbXlcZmVlZGJhY2sudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"feedback-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"header-content\">\r\n        <view class=\"left-action\" @click=\"goBack\">\r\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\r\n        </view>\r\n        <view class=\"title-area\">\r\n          <text class=\"page-title\">意见反馈</text>\r\n        </view>\r\n        <view class=\"right-action\">\r\n          <!-- 右侧占位 -->\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"scrollable-content\" :style=\"{ paddingTop: (statusBarHeight + 44) + 'px' }\">\r\n      <!-- 反馈表单 -->\r\n      <view class=\"feedback-form\">\r\n        <!-- 反馈类型 -->\r\n        <view class=\"form-section\">\r\n          <view class=\"section-title\">反馈类型</view>\r\n          <view class=\"feedback-types\">\r\n            <view \r\n              class=\"type-item\" \r\n              v-for=\"(type, index) in feedbackTypes\" \r\n              :key=\"index\"\r\n              :class=\"{ active: selectedType === index }\"\r\n              @click=\"selectType(index)\"\r\n            >\r\n              <text class=\"type-text\">{{type}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 反馈内容 -->\r\n        <view class=\"form-section\">\r\n          <view class=\"section-title\">反馈内容</view>\r\n          <view class=\"content-textarea-wrapper\">\r\n            <textarea \r\n              class=\"content-textarea\" \r\n              v-model=\"feedbackContent\" \r\n              placeholder=\"请详细描述您遇到的问题或建议，以便我们更好地解决和改进...\"\r\n              maxlength=\"500\"\r\n              placeholder-class=\"textarea-placeholder\"\r\n            ></textarea>\r\n            <view class=\"word-count\">{{feedbackContent.length}}/500</view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 上传图片 -->\r\n        <view class=\"form-section\">\r\n          <view class=\"section-title\">上传图片(选填)</view>\r\n          <view class=\"upload-section\">\r\n            <view class=\"image-grid\">\r\n              <view class=\"image-item\" v-for=\"(image, index) in uploadedImages\" :key=\"index\">\r\n                <image :src=\"image\" mode=\"aspectFill\" class=\"preview-image\"></image>\r\n                <view class=\"delete-icon\" @click.stop=\"deleteImage(index)\">×</view>\r\n              </view>\r\n              <view class=\"upload-item\" @click=\"chooseImage\" v-if=\"uploadedImages.length < 3\">\r\n                <image src=\"/static/images/icons/upload-image.png\" mode=\"aspectFit\" class=\"upload-icon\"></image>\r\n                <text class=\"upload-text\">添加图片</text>\r\n              </view>\r\n            </view>\r\n            <text class=\"upload-hint\">最多上传3张图片，每张不超过5MB</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 联系方式 -->\r\n        <view class=\"form-section\">\r\n          <view class=\"section-title\">联系方式(选填)</view>\r\n          <view class=\"contact-input-wrapper\">\r\n            <input \r\n              class=\"contact-input\" \r\n              v-model=\"contactInfo\" \r\n              placeholder=\"请留下您的手机号或微信，方便我们联系您\"\r\n              placeholder-class=\"input-placeholder\"\r\n            />\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 提交按钮 -->\r\n      <button \r\n        class=\"submit-button\" \r\n        :disabled=\"!isFormValid\" \r\n        :class=\"{ disabled: !isFormValid }\"\r\n        @click=\"submitFeedback\"\r\n      >\r\n        提交反馈\r\n      </button>\r\n      \r\n      <!-- 历史反馈 -->\r\n      <view class=\"history-section\" v-if=\"feedbackHistory.length > 0\">\r\n        <view class=\"history-header\">\r\n          <view class=\"history-title\">历史反馈</view>\r\n        </view>\r\n        \r\n        <view class=\"history-list\">\r\n          <view class=\"history-item\" v-for=\"(item, index) in feedbackHistory\" :key=\"index\" @click=\"viewHistoryDetail(item)\">\r\n            <view class=\"history-content\">\r\n              <view class=\"history-info\">\r\n                <text class=\"history-type\">{{feedbackTypes[item.type]}}</text>\r\n                <text class=\"history-time\">{{item.time}}</text>\r\n              </view>\r\n              <text class=\"history-brief\">{{item.content}}</text>\r\n            </view>\r\n            <view class=\"history-status\" :class=\"getStatusClass(item.status)\">\r\n              <text class=\"status-text\">{{getStatusText(item.status)}}</text>\r\n              <image src=\"/static/images/icons/arrow-right-gray.png\" mode=\"aspectFit\" class=\"arrow-icon\"></image>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 历史反馈详情弹窗 -->\r\n    <view class=\"popup-mask\" v-if=\"showHistoryDetail\" @click=\"closeHistoryDetail\"></view>\r\n    <view class=\"popup-container\" v-if=\"showHistoryDetail\">\r\n      <view class=\"popup-header\">\r\n        <text class=\"popup-title\">反馈详情</text>\r\n        <view class=\"popup-close\" @click=\"closeHistoryDetail\">×</view>\r\n      </view>\r\n      <scroll-view class=\"popup-content\" scroll-y>\r\n        <view class=\"detail-section\">\r\n          <view class=\"detail-label\">反馈类型</view>\r\n          <view class=\"detail-value\">{{feedbackTypes[currentHistory.type]}}</view>\r\n        </view>\r\n        \r\n        <view class=\"detail-section\">\r\n          <view class=\"detail-label\">提交时间</view>\r\n          <view class=\"detail-value\">{{currentHistory.time}}</view>\r\n        </view>\r\n        \r\n        <view class=\"detail-section\">\r\n          <view class=\"detail-label\">处理状态</view>\r\n          <view class=\"detail-value\" :class=\"getStatusClass(currentHistory.status)\">\r\n            {{getStatusText(currentHistory.status)}}\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"detail-section\">\r\n          <view class=\"detail-label\">反馈内容</view>\r\n          <view class=\"detail-value content\">{{currentHistory.content}}</view>\r\n        </view>\r\n        \r\n        <view class=\"detail-section\" v-if=\"currentHistory.images && currentHistory.images.length > 0\">\r\n          <view class=\"detail-label\">附件图片</view>\r\n          <view class=\"detail-images\">\r\n            <image \r\n              v-for=\"(img, index) in currentHistory.images\" \r\n              :key=\"index\" \r\n              :src=\"img\" \r\n              mode=\"widthFix\" \r\n              class=\"detail-image\"\r\n              @click=\"previewImage(img, currentHistory.images)\"\r\n            ></image>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"detail-section\" v-if=\"currentHistory.reply\">\r\n          <view class=\"detail-label\">官方回复</view>\r\n          <view class=\"detail-value reply\">\r\n            <text class=\"reply-time\">{{currentHistory.replyTime}}</text>\r\n            <text class=\"reply-content\">{{currentHistory.reply}}</text>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 提交成功弹窗 -->\r\n    <view class=\"popup-mask\" v-if=\"showSuccessPopup\"></view>\r\n    <view class=\"success-popup\" v-if=\"showSuccessPopup\">\r\n      <image src=\"/static/images/icons/success.png\" mode=\"aspectFit\" class=\"success-icon\"></image>\r\n      <text class=\"success-title\">提交成功</text>\r\n      <text class=\"success-message\">感谢您的反馈，我们会尽快处理</text>\r\n      <button class=\"success-button\" @click=\"closeSuccessPopup\">确定</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 状态栏高度\r\nconst statusBarHeight = ref(20);\r\n\r\n// 反馈类型\r\nconst feedbackTypes = ref(['功能建议', '信息错误', '拼车纠纷', '账号问题', '其他问题']);\r\nconst selectedType = ref(0);\r\nconst feedbackContent = ref('');\r\nconst uploadedImages = ref([]);\r\nconst contactInfo = ref('');\r\nconst showHistoryDetail = ref(false);\r\nconst currentHistory = ref({});\r\nconst showSuccessPopup = ref(false);\r\nconst feedbackHistory = ref([\r\n  {\r\n    id: '6001',\r\n    type: 0,\r\n    content: '建议在拼车页面增加路线规划功能，方便用户查看路线详情。',\r\n    time: '2023-10-10 14:30',\r\n    status: 'replied',\r\n    reply: '感谢您的建议！我们正在开发路线规划功能，预计将在下个版本中上线。',\r\n    replyTime: '2023-10-11 09:15'\r\n  },\r\n  {\r\n    id: '6002',\r\n    type: 3,\r\n    content: '无法绑定手机号，提示\"验证码错误\"，但我确认验证码输入正确。',\r\n    time: '2023-09-25 16:45',\r\n    status: 'processing',\r\n    images: ['/static/images/feedback/error.jpg']\r\n  }\r\n]);\r\n\r\n// 计算表单是否有效\r\nconst isFormValid = computed(() => {\r\n  return feedbackContent.value.trim().length >= 5;\r\n});\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 选择反馈类型\r\nconst selectType = (index) => {\r\n  selectedType.value = index;\r\n};\r\n\r\n// 选择图片\r\nconst chooseImage = () => {\r\n  if (uploadedImages.value.length >= 3) {\r\n    uni.showToast({\r\n      title: '最多上传3张图片',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  uni.chooseImage({\r\n    count: 3 - uploadedImages.value.length,\r\n    sizeType: ['compressed'],\r\n    sourceType: ['album', 'camera'],\r\n    success: (res) => {\r\n      // 检查图片大小\r\n      const tempFiles = res.tempFiles;\r\n      let isValid = true;\r\n      \r\n      tempFiles.forEach(file => {\r\n        if (file.size > 5 * 1024 * 1024) { // 5MB\r\n          isValid = false;\r\n          uni.showToast({\r\n            title: '图片大小不能超过5MB',\r\n            icon: 'none'\r\n          });\r\n          return;\r\n        }\r\n      });\r\n      \r\n      if (isValid) {\r\n        const tempFilePaths = res.tempFilePaths;\r\n        uploadedImages.value = [...uploadedImages.value, ...tempFilePaths];\r\n        \r\n        // 控制数量\r\n        if (uploadedImages.value.length > 3) {\r\n          uploadedImages.value = uploadedImages.value.slice(0, 3);\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 删除图片\r\nconst deleteImage = (index) => {\r\n  uploadedImages.value.splice(index, 1);\r\n};\r\n\r\n// 提交反馈\r\nconst submitFeedback = () => {\r\n  if (!isFormValid.value) return;\r\n  \r\n  // 显示加载状态\r\n  uni.showLoading({\r\n    title: '提交中...'\r\n  });\r\n  \r\n  // 模拟提交\r\n  setTimeout(() => {\r\n    uni.hideLoading();\r\n    \r\n    // 模拟成功\r\n    showSuccessPopup.value = true;\r\n    \r\n    // 清空表单\r\n    feedbackContent.value = '';\r\n    uploadedImages.value = [];\r\n  }, 1500);\r\n};\r\n\r\n// 关闭成功弹窗\r\nconst closeSuccessPopup = () => {\r\n  showSuccessPopup.value = false;\r\n  \r\n  // 添加到历史记录\r\n  const newFeedback = {\r\n    id: Date.now().toString(),\r\n    type: selectedType.value,\r\n    content: feedbackContent.value,\r\n    time: formatTime(new Date()),\r\n    status: 'pending',\r\n    images: uploadedImages.value\r\n  };\r\n  \r\n  feedbackHistory.value.unshift(newFeedback);\r\n};\r\n\r\n// 查看历史详情\r\nconst viewHistoryDetail = (item) => {\r\n  currentHistory.value = item;\r\n  showHistoryDetail.value = true;\r\n};\r\n\r\n// 关闭历史详情\r\nconst closeHistoryDetail = () => {\r\n  showHistoryDetail.value = false;\r\n};\r\n\r\n// 预览图片\r\nconst previewImage = (current, urls) => {\r\n  uni.previewImage({\r\n    current: current,\r\n    urls: urls\r\n  });\r\n};\r\n\r\n// 获取状态文本\r\nconst getStatusText = (status) => {\r\n  const statusMap = {\r\n    pending: '待处理',\r\n    processing: '处理中',\r\n    replied: '已回复',\r\n    closed: '已关闭'\r\n  };\r\n  return statusMap[status] || status;\r\n};\r\n\r\n// 获取状态样式类\r\nconst getStatusClass = (status) => {\r\n  const classMap = {\r\n    pending: 'status-pending',\r\n    processing: 'status-processing',\r\n    replied: 'status-replied',\r\n    closed: 'status-closed'\r\n  };\r\n  return classMap[status] || '';\r\n};\r\n\r\n// 格式化时间\r\nconst formatTime = (date) => {\r\n  const year = date.getFullYear();\r\n  const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n  const day = date.getDate().toString().padStart(2, '0');\r\n  const hours = date.getHours().toString().padStart(2, '0');\r\n  const minutes = date.getMinutes().toString().padStart(2, '0');\r\n  \r\n  return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n};\r\n\r\n// 生命周期函数\r\nonMounted(() => {\r\n  // 获取状态栏高度\r\n  const systemInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.feedback-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.custom-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 100;\r\n  background-color: #1677FF;\r\n}\r\n\r\n.header-content {\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 12px;\r\n}\r\n\r\n.left-action, .right-action {\r\n  width: 44px;\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.action-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.back-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  /* 图标是黑色的，需要转为白色 */\r\n  filter: brightness(0) invert(1);\r\n}\r\n\r\n.title-area {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.page-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #FFFFFF;\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* 内容区域 */\r\n.scrollable-content {\r\n  padding-left: 16px;\r\n  padding-right: 16px;\r\n  padding-bottom: 16px;\r\n}\r\n\r\n/* 反馈表单 */\r\n.feedback-form {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.form-section {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #F5F5F5;\r\n}\r\n\r\n.form-section:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n/* 反馈类型 */\r\n.feedback-types {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n}\r\n\r\n.type-item {\r\n  padding: 8px 16px;\r\n  border-radius: 20px;\r\n  background-color: #F5F5F5;\r\n}\r\n\r\n.type-item.active {\r\n  background-color: rgba(76, 175, 80, 0.1);\r\n  border: 1px solid #4CAF50;\r\n}\r\n\r\n.type-text {\r\n  font-size: 14px;\r\n  color: #666666;\r\n}\r\n\r\n.type-item.active .type-text {\r\n  color: #4CAF50;\r\n}\r\n\r\n/* 反馈内容 */\r\n.content-textarea-wrapper {\r\n  position: relative;\r\n  border: 1px solid #EEEEEE;\r\n  border-radius: 8px;\r\n  background-color: #F9F9F9;\r\n  padding: 12px;\r\n}\r\n\r\n.content-textarea {\r\n  width: 100%;\r\n  height: 120px;\r\n  font-size: 14px;\r\n  color: #333333;\r\n  line-height: 1.5;\r\n}\r\n\r\n.textarea-placeholder {\r\n  color: #999999;\r\n}\r\n\r\n.word-count {\r\n  position: absolute;\r\n  right: 12px;\r\n  bottom: 12px;\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n/* 上传图片 */\r\n.upload-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.image-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12px;\r\n}\r\n\r\n.image-item, .upload-item {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.preview-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.delete-icon {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  width: 20px;\r\n  height: 20px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  color: #FFFFFF;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-bottom-left-radius: 8px;\r\n}\r\n\r\n.upload-item {\r\n  border: 1px dashed #DDDDDD;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 4px;\r\n  background-color: #F9F9F9;\r\n}\r\n\r\n.upload-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n.upload-hint {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n/* 联系方式 */\r\n.contact-input-wrapper {\r\n  border: 1px solid #EEEEEE;\r\n  border-radius: 8px;\r\n  background-color: #F9F9F9;\r\n  padding: 12px;\r\n}\r\n\r\n.contact-input {\r\n  width: 100%;\r\n  height: 24px;\r\n  font-size: 14px;\r\n  color: #333333;\r\n}\r\n\r\n.input-placeholder {\r\n  color: #999999;\r\n}\r\n\r\n/* 提交按钮 */\r\n.submit-button {\r\n  width: 100%;\r\n  height: 44px;\r\n  line-height: 44px;\r\n  text-align: center;\r\n  background-color: #4CAF50;\r\n  color: #FFFFFF;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  border-radius: 22px;\r\n  margin: 24px 0;\r\n}\r\n\r\n.submit-button.disabled {\r\n  background-color: #E0E0E0;\r\n  color: #999999;\r\n}\r\n\r\n/* 历史反馈 */\r\n.history-section {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.history-header {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #F5F5F5;\r\n}\r\n\r\n.history-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.history-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 16px;\r\n  border-bottom: 1px solid #F5F5F5;\r\n}\r\n\r\n.history-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-content {\r\n  flex: 1;\r\n}\r\n\r\n.history-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.history-type {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #999999;\r\n}\r\n\r\n.history-brief {\r\n  font-size: 14px;\r\n  color: #666666;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 14px;\r\n}\r\n\r\n.arrow-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.status-pending .status-text {\r\n  color: #FF9800;\r\n}\r\n\r\n.status-processing .status-text {\r\n  color: #2196F3;\r\n}\r\n\r\n.status-replied .status-text {\r\n  color: #4CAF50;\r\n}\r\n\r\n.status-closed .status-text {\r\n  color: #9E9E9E;\r\n}\r\n\r\n/* 历史详情弹窗 */\r\n.popup-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  z-index: 1000;\r\n}\r\n\r\n.popup-container {\r\n  position: fixed;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 90%;\r\n  max-height: 80vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  z-index: 1001;\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border-bottom: 1px solid #EEEEEE;\r\n}\r\n\r\n.popup-title {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n}\r\n\r\n.popup-close {\r\n  font-size: 24px;\r\n  color: #999999;\r\n  padding: 0 8px;\r\n}\r\n\r\n.popup-content {\r\n  flex: 1;\r\n  padding: 16px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.detail-section {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.detail-label {\r\n  font-size: 14px;\r\n  color: #999999;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.detail-value {\r\n  font-size: 15px;\r\n  color: #333333;\r\n}\r\n\r\n.detail-value.content {\r\n  line-height: 1.6;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.detail-images {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.detail-image {\r\n  width: 100%;\r\n  border-radius: 8px;\r\n}\r\n\r\n.detail-value.reply {\r\n  background-color: #F8F8F8;\r\n  border-radius: 8px;\r\n  padding: 12px;\r\n}\r\n\r\n.reply-time {\r\n  font-size: 12px;\r\n  color: #999999;\r\n  display: block;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.reply-content {\r\n  font-size: 14px;\r\n  color: #333333;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 提交成功弹窗 */\r\n.success-popup {\r\n  position: fixed;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 80%;\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  z-index: 1001;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 24px;\r\n}\r\n\r\n.success-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.success-title {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  color: #333333;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.success-message {\r\n  font-size: 14px;\r\n  color: #666666;\r\n  margin-bottom: 24px;\r\n  text-align: center;\r\n}\r\n\r\n.success-button {\r\n  width: 80%;\r\n  height: 44px;\r\n  line-height: 44px;\r\n  text-align: center;\r\n  background-color: #4CAF50;\r\n  color: #FFFFFF;\r\n  font-size: 16px;\r\n  border-radius: 22px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/my/feedback.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AA2LA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,gBAAgBA,cAAAA,IAAI,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AAClE,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAC1B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,iBAAiBA,cAAAA,IAAI,CAAA,CAAE;AAC7B,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,oBAAoBA,cAAAA,IAAI,KAAK;AACnC,UAAM,iBAAiBA,cAAAA,IAAI,CAAA,CAAE;AAC7B,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAClC,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,MACZ;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ,CAAC,mCAAmC;AAAA,MAC7C;AAAA,IACH,CAAC;AAGD,UAAM,cAAcC,cAAQ,SAAC,MAAM;AACjC,aAAO,gBAAgB,MAAM,KAAI,EAAG,UAAU;AAAA,IAChD,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,aAAa,CAAC,UAAU;AAC5B,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,cAAc,MAAM;AACxB,UAAI,eAAe,MAAM,UAAU,GAAG;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO,IAAI,eAAe,MAAM;AAAA,QAChC,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAEhB,gBAAM,YAAY,IAAI;AACtB,cAAI,UAAU;AAEd,oBAAU,QAAQ,UAAQ;AACxB,gBAAI,KAAK,OAAO,IAAI,OAAO,MAAM;AAC/B,wBAAU;AACVA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AACD;AAAA,YACD;AAAA,UACT,CAAO;AAED,cAAI,SAAS;AACX,kBAAM,gBAAgB,IAAI;AAC1B,2BAAe,QAAQ,CAAC,GAAG,eAAe,OAAO,GAAG,aAAa;AAGjE,gBAAI,eAAe,MAAM,SAAS,GAAG;AACnC,6BAAe,QAAQ,eAAe,MAAM,MAAM,GAAG,CAAC;AAAA,YACvD;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,CAAC,UAAU;AAC7B,qBAAe,MAAM,OAAO,OAAO,CAAC;AAAA,IACtC;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,YAAY;AAAO;AAGxBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,yBAAiB,QAAQ;AAGzB,wBAAgB,QAAQ;AACxB,uBAAe,QAAQ;MACxB,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,oBAAoB,MAAM;AAC9B,uBAAiB,QAAQ;AAGzB,YAAM,cAAc;AAAA,QAClB,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,QACzB,MAAM,aAAa;AAAA,QACnB,SAAS,gBAAgB;AAAA,QACzB,MAAM,WAAW,oBAAI,MAAM;AAAA,QAC3B,QAAQ;AAAA,QACR,QAAQ,eAAe;AAAA,MAC3B;AAEE,sBAAgB,MAAM,QAAQ,WAAW;AAAA,IAC3C;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAClC,qBAAe,QAAQ;AACvB,wBAAkB,QAAQ;AAAA,IAC5B;AAGA,UAAM,qBAAqB,MAAM;AAC/B,wBAAkB,QAAQ;AAAA,IAC5B;AAGA,UAAM,eAAe,CAAC,SAAS,SAAS;AACtCA,oBAAAA,MAAI,aAAa;AAAA,QACf;AAAA,QACA;AAAA,MACJ,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA,QAChB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ;AACE,aAAO,SAAS,MAAM,KAAK;AAAA,IAC7B;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,YAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,YAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAE5D,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO;AAAA,IACpD;AAGAC,kBAAAA,UAAU,MAAM;AAEd,YAAM,aAAaD,oBAAI;AACvB,sBAAgB,QAAQ,WAAW,mBAAmB;AAAA,IACxD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxXD,GAAG,WAAWE,SAAe;"}