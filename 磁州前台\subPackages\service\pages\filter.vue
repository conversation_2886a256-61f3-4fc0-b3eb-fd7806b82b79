<template>
  <view class="service-filter-container premium-style">
    <!-- 高级自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="back-btn" @click="navigateBack">
        <view class="back-icon"></view>
      </view>
      <view class="navbar-title">{{serviceTitle}}</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 高级搜索框 -->
    <view class="search-container" v-if="showSearchBox">
      <view class="search-box">
        <image class="search-icon" src="/static/images/tabbar/放大镜.png"></image>
        <input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索职位、公司或关键词" confirm-type="search" @confirm="applyKeywordSearch" />
        <view class="search-btn" @click="applyKeywordSearch">搜索</view>
      </view>
    </view>
    
    <!-- 高级一级分类标签栏 -->
    <scroll-view class="top-category-tabs" scroll-x :show-scrollbar="false" enhanced :bounces="true">
      <view 
        class="top-category-item" 
        v-for="(item, index) in filteredTopCategories" 
        :key="index"
        :class="{'active-top-category': currentTopCategory === item.type}"
        @click="switchTopCategory(item)">
        {{item.name}}
      </view>
    </scroll-view>
    
    <!-- 高级子分类标签栏 -->
    <scroll-view class="subcategory-tabs" scroll-x :show-scrollbar="false" v-if="categoryList.length > 0" enhanced :bounces="true">
      <view 
        class="subcategory-item" 
        v-for="(category, index) in categoryList" 
        :key="index"
        :class="{'active-subcategory': selectedCategory === category}"
        @click="selectCategory(category)">
        {{category}}
      </view>
    </scroll-view>
    
    <!-- 高级筛选条件栏 -->
    <view class="filter-section" ref="filterSection">
      <!-- 区域筛选 -->
      <view class="filter-item" @click="toggleAreaFilter" ref="areaFilterItem">
        <text class="filter-text" :class="{ 'active-filter': selectedArea !== '全部区域' }">
          {{selectedArea}}
        </text>
        <view class="filter-arrow" :class="{ 'arrow-up': showAreaFilter }"></view>
      </view>
      
      <!-- 排序筛选 -->
      <view class="filter-item" @click="toggleSortFilter" ref="sortFilterItem">
        <text class="filter-text" :class="{ 'active-filter': selectedSort !== '默认排序' }">
          {{selectedSort}}
        </text>
        <view class="filter-arrow" :class="{ 'arrow-up': showSortFilter }"></view>
      </view>
    </view>
    
    <!-- 高级已选筛选标签 -->
    <view class="selected-filters" v-if="hasActiveFilters">
      <scroll-view scroll-x class="filter-tags-scroll" show-scrollbar="false" enhanced :bounces="true">
        <view class="filter-tags">
          <view class="filter-tag" v-if="currentTopCategory !== 'find' && currentTopCategory !== 'home_service'">
            {{getTopCategoryName(currentTopCategory)}} <text class="tag-close" @click="resetTopCategory">×</text>
      </view>
          <view class="filter-tag" v-if="selectedCategory !== '全部分类' && selectedCategory !== ''">
            {{selectedCategory}} <text class="tag-close" @click="resetCategory">×</text>
    </view>
          <view class="filter-tag" v-if="selectedSubcategory !== ''">
            {{selectedSubcategory}} <text class="tag-close" @click="resetSubcategory">×</text>
          </view>
          <view class="filter-tag" v-if="selectedArea !== '全部区域'">
            {{selectedArea}} <text class="tag-close" @click="resetArea">×</text>
          </view>
          <view class="filter-tag" v-if="selectedSort !== '默认排序'">
            {{selectedSort}} <text class="tag-close" @click="resetSort">×</text>
          </view>
          <view class="reset-all" @click="resetAllFilters">清除全部</view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 高级区域筛选弹出内容 -->
    <view class="filter-dropdown area-dropdown" v-if="showAreaFilter" :style="{ top: filterDropdownTop + 'px', left: areaFilterLeft + 'px', width: filterItemWidth + 'px' }">
      <view class="dropdown-header">
        <text class="dropdown-title">选择区域</text>
      </view>
      <scroll-view scroll-y class="dropdown-scroll">
        <view class="dropdown-item" 
          v-for="(area, index) in areaList" 
          :key="index"
          :class="{ 'active-item': area === selectedArea }"
          @click="selectArea(area)">
          <text class="dropdown-item-text">{{area}}</text>
          <text class="dropdown-item-check" v-if="area === selectedArea">✓</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 高级二级分类筛选弹出内容 -->
    <view class="filter-dropdown subcategory-dropdown" v-if="showSubcategoryFilter" :style="{ top: subcategoryDropdownTop + 'px' }">
      <view class="dropdown-header">
        <text class="dropdown-title">{{selectedCategory}}细分类别</text>
      </view>
      <scroll-view scroll-y class="dropdown-scroll">
        <view class="dropdown-item" 
          v-for="(subcat, index) in subcategoryList" 
          :key="index"
          :class="{ 'active-item': subcat === selectedSubcategory }"
          @click="selectSubcategory(subcat)">
          <text class="dropdown-item-text">{{subcat}}</text>
          <text class="dropdown-item-check" v-if="subcat === selectedSubcategory">✓</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 高级排序筛选弹出内容 -->
    <view class="filter-dropdown sort-dropdown" v-if="showSortFilter" :style="{ top: filterDropdownTop + 'px', left: sortFilterLeft + 'px', width: filterItemWidth + 'px' }">
      <view class="dropdown-header">
        <text class="dropdown-title">排序方式</text>
      </view>
      <view class="dropdown-item" 
        v-for="(sort, index) in sortList" 
        :key="index"
        :class="{ 'active-item': sort === selectedSort }"
        @click="selectSort(sort)">
        <text class="dropdown-item-text">{{sort}}</text>
        <text class="dropdown-item-check" v-if="sort === selectedSort">✓</text>
      </view>
    </view>
    
    <!-- 高级遮罩层 -->
    <view class="filter-mask" 
      v-if="showAreaFilter || showSubcategoryFilter || showSortFilter"
      @click="closeAllFilters"></view>
    
    <!-- 高级内容列表 -->
    <scroll-view 
      scroll-y 
      class="service-list" 
      @scrolltolower="loadMore" 
      refresher-enabled 
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      enhanced
      :bounces="true"
      :show-scrollbar="false">
      
      <!-- 高级数据统计提示 -->
      <view class="result-stats" v-if="serviceList.length > 0">
        共找到 <text class="stats-number">{{serviceList.length}}</text> 条相关信息
      </view>
      
      <view v-if="serviceList.length > 0" class="service-items-wrapper">
        <!-- 高级职位卡片 -->
        <view 
          v-for="(item, index) in serviceList" 
          :key="index" 
          class="service-item"
          hover-class="service-item-hover"
          @click="navigateToDetail(item)">
          <view class="service-content">
            <view class="service-header">
              <view class="service-tags">
              <text class="service-tag">{{item.category}}</text>
              <text class="service-subcategory" v-if="item.subcategory">{{item.subcategory}}</text>
              </view>
              <view class="service-meta">
              <text class="service-area">{{item.area || '全城'}}</text>
              <text class="service-time">{{item.time}}</text>
              </view>
            </view>
            
            <view class="service-body">
            <text class="service-title">{{item.content}}</text>
            
            <!-- 图片区域 -->
            <view class="service-images" v-if="item.images && item.images.length > 0">
              <image 
                v-for="(img, imgIndex) in item.images.slice(0, 3)" 
                :key="imgIndex" 
                :src="img" 
                mode="aspectFill" 
                  class="service-image"
                  :class="{'single-image': item.images.length === 1}"></image>
              <view class="image-count" v-if="item.images.length > 3">+{{item.images.length - 3}}</view>
              </view>
            </view>
            
            <view class="service-footer">
              <view class="service-stats">
                <text class="service-views">浏览: {{item.views || 0}}</text>
                <text class="service-price" v-if="item.price">￥{{item.price}}</text>
              </view>
              <view class="service-actions">
                <view class="action-btn contact-btn">
                  <text class="action-icon"></text>
                  <text class="action-text">联系</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 高级加载更多提示 -->
        <view class="loading-more" v-if="hasMore">
          <view class="loading-indicator"></view>
          <text class="loading-text">加载中...</text>
        </view>
        <view class="loading-done" v-else>
          <text class="loading-done-text">— 已经到底啦 —</text>
        </view>
      </view>
      
      <!-- 高级空状态 -->
      <view v-else class="empty-state">
        <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
        <text class="empty-text">暂无相关职位信息</text>
        <view class="empty-tips">
          换个筛选条件试试吧
        </view>
        <view class="empty-btn" @click="resetAllFilters">重置筛选</view>
      </view>
    </scroll-view>
    
    <!-- 高级悬浮发布按钮 -->
    <view class="publish-btn" hover-class="publish-btn-hover" @click="navigateToPublish">
      <text class="publish-icon">+</text>
      <text class="publish-text">发布</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue';

// 基础数据
const statusBarHeight = ref(20);
const serviceTitle = ref('服务筛选');
const serviceType = ref('find');
const refreshing = ref(false);
const hasMore = ref(true);
const page = ref(1);
const serviceList = ref([]);
const filterHistory = ref([]);
const showSearchBox = ref(true);
const loading = ref(false);

// 筛选相关数据
const showAreaFilter = ref(false);
const showSubcategoryFilter = ref(false);
const showSortFilter = ref(false);
const selectedArea = ref('全部区域');
const selectedCategory = ref('全部分类');
const selectedSubcategory = ref('');
const selectedSort = ref('默认排序');
const filterDropdownTop = ref(0); // 筛选下拉菜单顶部位置
const subcategoryDropdownTop = ref(0); // 子分类下拉菜单顶部位置
const areaFilterLeft = ref(12); // 区域筛选下拉菜单左侧位置
const sortFilterLeft = ref(0); // 排序筛选下拉菜单左侧位置
const filterItemWidth = ref(0); // 筛选按钮宽度

// 筛选选项
const areaList = ref(['全部区域', '城区', '磁州镇', '讲武城镇', '岳城镇', '观台镇', '白土镇', '黄沙镇']);
const sortList = ref(['默认排序', '价格最低', '价格最高', '距离最近']);
const categoryList = ref([]);
const subcategoryList = ref([]);

// 到家服务子分类类型
const homeServiceTypes = ref(['home_service', 'home_cleaning', 'repair', 'installation', 'locksmith', 'moving', 'beauty', 'tutor', 'pet_service', 'plumbing']);

// 顶部分类
const topCategories = ref([
  { type: 'home_service', name: '到家服务' },
  { type: 'home_cleaning', name: '家政保洁' },
  { type: 'repair', name: '维修服务' },
  { type: 'installation', name: '安装服务' },
  { type: 'locksmith', name: '开锁换锁' },
  { type: 'moving', name: '搬家拉货' },
  { type: 'beauty', name: '美容美发' },
  { type: 'tutor', name: '家教辅导' },
  { type: 'pet_service', name: '宠物服务' },
  { type: 'plumbing', name: '管道疏通' },
  { type: 'find', name: '寻找服务' },
  { type: 'business', name: '生意转让' },
  { type: 'job', name: '招聘信息' },
  { type: 'resume', name: '求职信息' },
  { type: 'house_rent', name: '房屋出租' },
  { type: 'house_sell', name: '房屋出售' },
  { type: 'second_car', name: '二手车辆' },
  { type: 'pet', name: '宠物信息' },
  { type: 'car', name: '车辆服务' },
  { type: 'second_hand', name: '二手闲置' },
  { type: 'carpool', name: '磁州拼车' },
  { type: 'education', name: '教育培训' },
  { type: 'other', name: '其他服务' }
]);
const currentTopCategory = ref('find');
const searchKeyword = ref('');
const extractedKeywords = ref([]);
const selectedKeywords = ref([]);

// 判断是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return selectedArea.value !== '全部区域' || 
         selectedCategory.value !== '全部分类' || 
         selectedSubcategory.value !== '' ||
         selectedSort.value !== '默认排序';
});

// 过滤后的顶部分类
const filteredTopCategories = computed(() => {
  // 如果当前页面是到家服务相关页面，显示到家服务子分类
  if (homeServiceTypes.value.includes(serviceType.value)) {
    // 过滤出到家服务子分类
    const homeServiceCategories = topCategories.value.filter(item => 
      homeServiceTypes.value.includes(item.type)
    );
    return homeServiceCategories;
  }
  
  // 定义主要服务类型（一级分类）
  const mainServiceTypes = [
    { type: 'find', name: '寻找服务' },
    { type: 'business', name: '生意转让' },
    { type: 'job', name: '招聘信息' },
    { type: 'resume', name: '求职信息' },
    { type: 'house_rent', name: '房屋出租' },
    { type: 'house_sell', name: '房屋出售' },
    { type: 'second_car', name: '二手车辆' },
    { type: 'pet', name: '宠物信息' },
    { type: 'car', name: '车辆服务' },
    { type: 'second_hand', name: '二手闲置' },
    { type: 'carpool', name: '磁州拼车' },
    { type: 'education', name: '教育培训' },
    { type: 'other', name: '其他服务' }
  ];
  
  // 对于其他所有服务类型，显示主要服务类型（一级分类）
  return mainServiceTypes;
});

// 设置服务标题
const setServiceTitle = (type) => {
  const titleMap = {
    'find': '寻找服务',
    'business': '生意转让',
    'job': '招聘信息',
    'resume': '求职信息',
    'house_rent': '房屋出租',
    'house_sell': '房屋出售',
    'second_car': '二手车辆',
    'pet': '宠物信息',
    'car': '车辆服务',
    'second_hand': '二手闲置',
    'carpool': '磁州拼车',
    'education': '教育培训',
    'other': '其他服务'
  };
  
  serviceTitle.value = titleMap[type] || '服务筛选';
};

// 加载历史筛选条件
const loadFilterHistory = () => {
  try {
    const key = `filter_history_${serviceType.value}`;
    const history = uni.getStorageSync(key);
    if (history) {
      filterHistory.value = JSON.parse(history);
      console.log('加载筛选历史:', filterHistory.value);
    }
  } catch (e) {
    console.error('加载筛选历史失败', e);
  }
};

// 保存筛选历史
const saveFilterHistory = () => {
  try {
    // 创建当前筛选条件对象
    const currentFilter = {
      area: selectedArea.value,
      category: selectedCategory.value,
      subcategory: selectedSubcategory.value,
      sort: selectedSort.value,
      timestamp: new Date().getTime()
    };
    
    // 更新历史数组
    filterHistory.value.unshift(currentFilter);
    
    // 只保留最近5条记录
    filterHistory.value = filterHistory.value.slice(0, 5);
    
    // 保存到本地存储
    const key = `filter_history_${serviceType.value}`;
    uni.setStorageSync(key, JSON.stringify(filterHistory.value));
  } catch (e) {
    console.error('保存筛选历史失败', e);
  }
};

// 根据服务类型设置分类选项
const setCategoryOptions = (type) => {
  // 不同服务类型的分类选项
  const categoryOptions = {
    // 到家服务子分类
    'home_service': ['全部分类', '家政保洁', '维修服务', '安装服务', '开锁换锁', '搬家拉货', '美容美发', '家教辅导', '宠物服务', '管道疏通'],
    'home_cleaning': ['全部分类', '日常保洁', '深度保洁', '开荒保洁', '家电清洗', '玻璃清洗'],
    'repair': ['全部分类', '水电维修', '家电维修', '门窗维修', '墙面翻新', '其他维修'],
    'installation': ['全部分类', '家具安装', '家电安装', '灯具安装', '门窗安装', '其他安装'],
    'locksmith': ['全部分类', '开锁服务', '换锁服务', '汽车开锁', '保险柜开锁', '智能锁安装'],
    'moving': ['全部分类', '小型搬家', '大型搬家', '长途搬家', '物品运输', '其他搬运'],
    'beauty': ['全部分类', '美甲美睫', '美容护肤', '美发造型', '化妆服务', '其他美容'],
    'tutor': ['全部分类', '小学辅导', '初中辅导', '高中辅导', '艺术辅导', '其他辅导'],
    'pet_service': ['全部分类', '宠物洗澡', '宠物美容', '宠物寄养', '宠物遛狗', '其他服务'],
    'plumbing': ['全部分类', '管道疏通', '马桶疏通', '下水道疏通', '厨房疏通', '其他疏通'],
    // 原有的一级分类
    'find': ['全部分类', '维修', '装修', '搬家', '清洁', '美容美发', '教育培训', '其他'],
    'business': ['全部分类', '餐饮店', '便利店', '服装店', '美容店', '超市', '其他店铺'],
    'job': ['全部分类', '销售', '服务员', '技工', '司机', '厨师', '文员', '其他职位'],
    'resume': ['全部分类', '销售类', '技术类', '服务类', '行政类', '教育类', '其他类'],
    'house_rent': ['全部分类', '整租', '合租', '短租', '商铺', '写字楼', '厂房'],
    'house_sell': ['全部分类', '普通住宅', '别墅', '写字楼', '商铺', '厂房', '土地'],
    'second_car': ['全部分类', '轿车', 'SUV', '面包车', '货车', '电动车', '摩托车'],
    'pet': ['全部分类', '宠物狗', '宠物猫', '小宠', '宠物用品', '宠物服务'],
    'car': ['全部分类', '洗车', '保养', '维修', '租车', '陪驾', '其他'],
    'second_hand': ['全部分类', '手机', '电脑', '家具', '家电', '服装', '图书', '其他'],
    'carpool': ['全部分类', '上下班', '周末', '节假日', '城际', '长途', '其他'],
    'education': ['全部分类', '幼儿', '小学', '初中', '高中', '大学', '职业培训', '其他'],
    'other': ['全部分类', '家政', '维修', '租赁', '其他']
  };
  
  categoryList.value = categoryOptions[type] || ['全部分类'];
  
  // 特殊处理招聘信息类型，确保显示正确的分类选项
  if (type === 'job') {
    // 这里使用与mock数据中一致的招聘职位分类
    categoryList.value = ['全部分类', '销售', '服务员', '技工', '司机', '厨师', '文员', '其他职位'];
  }
};

// 区域筛选
const selectArea = (area) => {
  // 触感反馈
  uni.vibrateShort();
  
  selectedArea.value = area;
  showAreaFilter.value = false;
  resetListAndReload();
  
  // 筛选变化后保存历史
  if (hasActiveFilters.value) {
    saveFilterHistory();
  }
};

// 一级分类选择
const selectCategory = (category) => {
  // 触感反馈
  uni.vibrateShort();
  
  // 直接应用选择的一级分类
  selectedCategory.value = category;
  
  // 当选择全部分类时，清空二级分类
  if (category === '全部分类') {
    selectedSubcategory.value = '';
    subcategoryList.value = [];
    showSubcategoryFilter.value = false;
  } else {
    // 根据选择的一级分类加载二级分类
    setSubcategoryOptions(category);
    
    // 如果当前是招聘信息类型，直接应用筛选，不显示三级分类
    if (serviceType.value === 'job' || currentTopCategory.value === 'job') {
      showSubcategoryFilter.value = false;
    } 
    // 其他类型正常处理
    else if (subcategoryList.value.length > 0) {
      showSubcategoryFilter.value = true;
      
      // 计算下拉菜单位置
      nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select('.filter-section').boundingClientRect(container => {
          if (container) {
            // 如果有已选筛选标签，考虑其高度
            if (hasActiveFilters.value) {
              query.select('.selected-filters').boundingClientRect(filters => {
                if (filters) {
                  subcategoryDropdownTop.value = filters.height + filters.top;
                } else {
                  subcategoryDropdownTop.value = container.height + container.top;
                }
              }).exec();
            } else {
              subcategoryDropdownTop.value = container.height + container.top;
            }
          }
        }).exec();
      });
    } else {
      showSubcategoryFilter.value = false;
    }
  }
  
  // 重新加载数据
  resetListAndReload();
  
  // 筛选变化后保存历史
  if (hasActiveFilters.value) {
    saveFilterHistory();
  }
};

// 二级分类选择
const selectSubcategory = (subcategory) => {
  // 触感反馈
  uni.vibrateShort();
  
  // 直接应用所选的子分类
  selectedSubcategory.value = subcategory;
  
  // 关闭筛选弹窗
  showSubcategoryFilter.value = false;
  
  // 重新加载数据
  resetListAndReload();
  
  // 筛选变化后保存历史
  if (hasActiveFilters.value) {
    saveFilterHistory();
  }
};

// 排序筛选
const selectSort = (sort) => {
  // 触感反馈
  uni.vibrateShort();
  
  selectedSort.value = sort;
  showSortFilter.value = false;
  resetListAndReload();
  
  // 筛选变化后保存历史
  if (hasActiveFilters.value) {
    saveFilterHistory();
  }
};

// 设置二级分类选项
const setSubcategoryOptions = (category) => {
  // 二级分类数据
  const subcategoryMap = {
    // 到家服务子分类的子分类
    '家政保洁': ['日常保洁', '深度保洁', '开荒保洁', '家电清洗', '玻璃清洗'],
    '维修服务': ['水电维修', '家电维修', '门窗维修', '墙面翻新', '其他维修'],
    '安装服务': ['家具安装', '家电安装', '灯具安装', '门窗安装', '其他安装'],
    '开锁换锁': ['开锁服务', '换锁服务', '汽车开锁', '保险柜开锁', '智能锁安装'],
    '搬家拉货': ['小型搬家', '大型搬家', '长途搬家', '物品运输', '其他搬运'],
    '美容美发': ['美甲美睫', '美容护肤', '美发造型', '化妆服务', '其他美容'],
    '家教辅导': ['小学辅导', '初中辅导', '高中辅导', '艺术辅导', '其他辅导'],
    '宠物服务': ['宠物洗澡', '宠物美容', '宠物寄养', '宠物遛狗', '其他服务'],
    '管道疏通': ['管道疏通', '马桶疏通', '下水道疏通', '厨房疏通', '其他疏通'],
    // 原有的分类子分类 - 除了到家服务子分类外，其他所有分类的三级分类全部删掉
    'business': {},
    'job': {},
    'second_hand': {},
    'house_rent': {},
    'house_sell': {},
    'second_car': {},
    'pet': {},
    'car': {},
    'carpool': {},
    'education': {},
    'other': {}
  };
  
  // 获取当前服务类型的二级分类映射
  if (homeServiceTypes.value.includes(serviceType.value)) {
    // 如果是到家服务子分类，直接使用映射
    subcategoryList.value = subcategoryMap[category] || [];
  } else {
    // 其他服务类型使用原有逻辑，但三级分类已被清空
    const currentTypeSubcats = subcategoryMap[serviceType.value] || {};
    subcategoryList.value = currentTypeSubcats[category] || [];
  }
};

// 关闭所有筛选
const closeAllFilters = () => {
  showAreaFilter.value = false;
  showSubcategoryFilter.value = false;
  showSortFilter.value = false;
};

// 重置所有筛选条件
const resetAllFilters = () => {
  // 触感反馈
  uni.vibrateShort({type: 'medium'});
  
  selectedArea.value = '全部区域';
  selectedCategory.value = '全部分类';
  selectedSubcategory.value = '';
  selectedSort.value = '默认排序';
  
  // 重新加载数据
  resetListAndReload();
};

// 重置区域筛选
const resetArea = () => {
  selectedArea.value = '全部区域';
  resetListAndReload();
};

// 重置子分类筛选
const resetSubcategory = () => {
  selectedSubcategory.value = '';
  resetListAndReload();
};

// 重置排序筛选
const resetSort = () => {
  selectedSort.value = '默认排序';
  resetListAndReload();
};

// 重置列表并重新加载
const resetListAndReload = () => {
  page.value = 1;
  serviceList.value = [];
  hasMore.value = true;
  loadServiceList();
};

// 加载服务列表
const loadServiceList = () => {
  if (loading.value) return;
  
  loading.value = true;
  
  // 构建筛选参数
  const params = {
    page: page.value,
    pageSize: 10,
    category: selectedCategory.value === '全部分类' ? '' : selectedCategory.value,
    subcategory: selectedSubcategory.value,
    area: selectedArea.value === '全部区域' ? '' : selectedArea.value,
    sort: selectedSort.value === '默认排序' ? '' : selectedSort.value,
    keyword: searchKeyword.value
  };
  
  // 特殊处理不同服务类型，映射到正确的category值
  const categoryMap = {
    'job': '招聘信息',
    'resume': '求职信息',
    'business': '生意转让',
    'house_rent': '房屋出租',
    'house_sell': '房屋出售',
    'second_car': '二手车辆',
    'pet': '宠物信息',
    'car': '车辆服务',
    'second_hand': '二手闲置',
    'carpool': '磁州拼车',
    'education': '教育培训',
    'other': '其他服务'
  };
  
  // 如果是特定的服务类型，强制设置category参数
  if (categoryMap[serviceType.value] || categoryMap[currentTopCategory.value]) {
    // 优先使用当前顶部分类的映射
    params.category = categoryMap[currentTopCategory.value] || categoryMap[serviceType.value];
    console.log(`应用特殊分类筛选: ${params.category}`);
  }
  
  console.log('加载服务列表参数:', params);
  
  // 调用API获取数据
  import('@/mock/api.js').then(module => {
    const { serviceApi } = module;
    
    // 确保API可用
    if (!serviceApi || !serviceApi.fetchPublishList) {
      console.error('API不可用:', serviceApi);
      loading.value = false;
      refreshing.value = false;
      return;
    }
    
    // 使用 Promise.resolve 确保即使 API 调用失败也能返回空数组
    Promise.resolve()
      .then(() => serviceApi.fetchPublishList(params))
      .then(res => {
        if (page.value === 1) {
          serviceList.value = res.list || [];
        } else {
          serviceList.value = [...serviceList.value, ...(res.list || [])];
        }
        
        hasMore.value = res.hasMore;
        loading.value = false;
        refreshing.value = false;
        
        console.log('加载服务列表成功:', serviceList.value.length);
      })
      .catch(err => {
        console.error('加载服务列表失败', err);
        loading.value = false;
        refreshing.value = false;
      });
  }).catch(err => {
    console.error('导入API模块失败', err);
    loading.value = false;
    refreshing.value = false;
  });
};

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true;
  resetListAndReload();
  
  // 添加触感反馈
  uni.vibrateShort();
};

// 加载更多
const loadMore = () => {
  if (hasMore.value) {
    page.value++;
    loadServiceList();
  }
};

// 导航到详情页
const navigateToDetail = (item) => {
  // 根据不同类型跳转到不同详情页
  let url = `/pages/publish/info-detail?id=${item.id}`;
  
  const detailPageMap = {
    'find': 'find-service-detail',
    'business': 'business-transfer-detail',
    'job': 'job-detail',
    'resume': 'job-seeking-detail',
    'house_rent': 'house-rent-detail',
    'house_sell': 'house-sale-detail',
    'second_car': 'car-detail',
    'pet': 'pet-detail',
    'car': 'vehicle-service-detail',
    'second_hand': 'second-hand-detail',
    'carpool': 'carpool-detail',
    'education': 'education-detail',
    'dating': 'dating-detail',
    'merchant_activity': 'merchant-activity-detail',
    'home_service': 'home-service-detail',
    'home_cleaning': 'home-service-detail',
    'repair': 'home-service-detail',
    'installation': 'home-service-detail',
    'locksmith': 'home-service-detail',
    'moving': 'home-service-detail',
    'beauty': 'home-service-detail',
    'tutor': 'home-service-detail',
    'pet_service': 'home-service-detail',
    'plumbing': 'home-service-detail'
  };
  
  // 如果有对应的详情页映射，使用映射
  if (detailPageMap[serviceType.value]) {
    url = `/pages/publish/${detailPageMap[serviceType.value]}?id=${item.id}`;
  }
  
  // 跳转到详情页
  uni.navigateTo({
    url: url
  });
};

// 导航到发布页
const navigateToPublish = () => {
  // 添加触感反馈
  uni.vibrateShort();
  
  // 从服务类型转换为发布类型
  const publishTypeMap = {
    'find': 'find_service',
    'business': 'business_transfer',
    'job': 'hire',
    'resume': 'job_wanted',
    'house_rent': 'house_rent',
    'house_sell': 'house_sell',
    'second_car': 'used_car',
    'pet': 'pet',
    'car': 'car_service',
    'second_hand': 'second_hand',
    'carpool': 'carpool',
    'education': 'education',
    'other': 'other_service'
  };
  
  const publishType = publishTypeMap[serviceType.value] || 'other_service';
  uni.navigateTo({
    url: `/pages/publish/detail?type=${publishType}&name=${encodeURIComponent(serviceTitle.value)}`
  });
};

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 切换顶部分类
const switchTopCategory = (item) => {
  // 触感反馈
  uni.vibrateShort();
  
  // 设置当前顶部分类
  currentTopCategory.value = item.type;
  serviceType.value = item.type;
  
  // 设置标题
  setServiceTitle(item.type);
  
  // 重置筛选条件
  selectedCategory.value = '全部分类';
  selectedSubcategory.value = '';
  
  // 设置分类选项
  setCategoryOptions(item.type);
  
  // 特殊处理某些分类类型，比如招聘信息
  const specialCategoryMap = {
    'job': '招聘信息',
    'resume': '求职信息',
    'business': '生意转让',
    'house_rent': '房屋出租',
    'house_sell': '房屋出售',
    'second_car': '二手车辆',
    'pet': '宠物信息',
    'car': '车辆服务',
    'second_hand': '二手闲置',
    'carpool': '磁州拼车',
    'education': '教育培训'
  };
  
  // 如果是特殊分类，设置对应的分类名称
  if (specialCategoryMap[item.type]) {
    console.log(`设置特殊分类: ${specialCategoryMap[item.type]}`);
  }
  
  // 重新加载数据
  resetListAndReload();
};

// 关键词搜索
const onSearchInput = () => {
  // 如果输入为空，清除提取的关键词
  if (!searchKeyword.value.trim()) {
    extractedKeywords.value = [];
    selectedKeywords.value = [];
    resetListAndReload();
    return;
  }
  
  // 自动提取关键词
  setTimeout(() => {
    extractKeywords();
  }, 300);
};

// 提取关键词
const extractKeywords = () => {
  const input = searchKeyword.value.trim();
  if (!input) return;
  
  // 使用简单的分词规则：按空格、逗号、句号等符号分割
  let words = input.split(/[\s,，.。;；!！?？、]/g)
               .filter(word => word.length >= 2) // 过滤短词
               .map(word => word.trim())
               .filter(word => word);
  
  // 去重
  words = [...new Set(words)];
  
  // 限制最多提取5个关键词
  if (words.length > 5) {
    words = words.slice(0, 5);
  }
  
  // 补充从分类和内容中提取的关键词
  addContextualKeywords(words);
  
  // 更新提取的关键词
  extractedKeywords.value = words;
  
  // 默认选中所有提取出的关键词
  selectedKeywords.value = [...words];
  
  // 应用关键词筛选
  applyKeywordFilter();
};

// 补充上下文关键词
const addContextualKeywords = (words) => {
  // 添加分类相关的关键词
  if (selectedCategory.value !== '全部分类') {
    if (!words.includes(selectedCategory.value)) {
      words.push(selectedCategory.value);
    }
  }
  
  // 添加子分类相关的关键词
  if (selectedSubcategory.value && !words.includes(selectedSubcategory.value)) {
    words.push(selectedSubcategory.value);
  }
  
  // 添加区域相关的关键词
  if (selectedArea.value !== '全部区域' && !words.includes(selectedArea.value)) {
    words.push(selectedArea.value);
  }
  
  // 从常见关键词列表中匹配
  const commonKeywords = getCommonKeywords();
  for (const keyword of commonKeywords) {
    if (searchKeyword.value.includes(keyword) && !words.includes(keyword)) {
      words.push(keyword);
    }
  }
};

// 获取常见关键词
const getCommonKeywords = () => {
  // 根据当前服务类型返回相关的常见关键词
  const commonKeywordMap = {
    'find': ['维修', '安装', '保洁', '搬家', '上门', '快速', '专业', '经验', '价格优惠'],
    'business': ['转让', '盈利', '客源稳定', '位置好', '接手即可营业', '证件齐全', '房租低'],
    'job': ['招聘', '急聘', '有经验', '薪资高', '包吃住', '五险一金', '双休', '朝九晚五'],
    'resume': ['求职', '经验丰富', '应届毕业', '有证书', '会计', '司机', '销售', '文员'],
    'house_rent': ['出租', '整租', '合租', '精装', '家电齐全', '拎包入住', '交通便利', '南北通透'],
    'house_sell': ['出售', '精装修', '学区房', '交通便利', '地段好', '采光好', '低总价', '小户型'],
    'second_car': ['二手车', '准新车', '行驶里程少', '车况好', '价格优惠', '一手车', '无事故'],
    'pet': ['宠物', '幼犬', '幼猫', '疫苗已做', '驱虫已做', '包健康', '可上门看', '纯种'],
    'car': ['洗车', '保养', '维修', '补胎', '贴膜', '换机油', '年检', '过户'],
    'second_hand': ['二手', '全新', '九成新', '低价', '急售', '搬家甩卖', '有发票', '保修期内'],
    'education': ['培训', '辅导', '一对一', '小班', '提分快', '经验丰富', '上门', '包学会']
  };
  
  return commonKeywordMap[serviceType.value] || [];
};

// 应用关键词搜索
const applyKeywordSearch = () => {
  if (!searchKeyword.value.trim()) {
    return;
  }
  
  // 触感反馈
  uni.vibrateShort();
  
  // 提取关键词并应用筛选
  extractKeywords();
};

// 清除关键词
const clearKeyword = () => {
  searchKeyword.value = '';
  extractedKeywords.value = [];
  selectedKeywords.value = [];
  
  // 重新加载数据
  resetListAndReload();
};

// 切换关键词
const toggleKeyword = (keyword) => {
  // 触感反馈
  uni.vibrateShort();
  
  const index = selectedKeywords.value.indexOf(keyword);
  if (index === -1) {
    // 添加关键词
    selectedKeywords.value.push(keyword);
  } else {
    // 移除关键词
    selectedKeywords.value.splice(index, 1);
  }
  
  // 应用关键词筛选
  applyKeywordFilter();
};

// 应用关键词筛选
const applyKeywordFilter = () => {
  // 重置列表并重新加载
  resetListAndReload();
};

// 获取顶部分类名称
const getTopCategoryName = (type) => {
  const category = topCategories.value.find(item => item.type === type);
  return category ? category.name : '';
};

// 重置顶部分类
const resetTopCategory = () => {
  currentTopCategory.value = 'find';
  serviceType.value = 'find';
  setServiceTitle('find');
  setCategoryOptions('find');
  resetListAndReload();
};

// 重置子分类
const resetCategory = () => {
  selectedCategory.value = '全部分类';
  selectedSubcategory.value = '';
  resetListAndReload();
};

// 区域筛选
const toggleAreaFilter = () => {
  // 关闭其他筛选
  showSortFilter.value = false;
  showSubcategoryFilter.value = false;
  
  // 切换区域筛选显示状态
  showAreaFilter.value = !showAreaFilter.value;
  
  // 如果显示区域筛选，计算下拉菜单位置
  if (showAreaFilter.value) {
    nextTick(() => {
      const query = uni.createSelectorQuery();
      query.select('.filter-section').boundingClientRect(filterSection => {
        if (filterSection) {
          // 下拉菜单应该紧贴筛选按钮底部
          filterDropdownTop.value = filterSection.top + filterSection.height;
          
          // 获取区域筛选按钮的位置和宽度
          query.select('.filter-item').boundingClientRect(areaItem => {
            if (areaItem) {
              areaFilterLeft.value = areaItem.left;
              filterItemWidth.value = areaItem.width;
            }
          }).exec();
        }
      }).exec();
    });
  }
};

// 排序筛选
const toggleSortFilter = () => {
  // 关闭其他筛选
  showAreaFilter.value = false;
  showSubcategoryFilter.value = false;
  
  // 切换排序筛选显示状态
  showSortFilter.value = !showSortFilter.value;
  
  // 如果显示排序筛选，计算下拉菜单位置
  if (showSortFilter.value) {
    nextTick(() => {
      const query = uni.createSelectorQuery();
      query.select('.filter-section').boundingClientRect(filterSection => {
        if (filterSection) {
          // 下拉菜单应该紧贴筛选按钮底部
          filterDropdownTop.value = filterSection.top + filterSection.height;
          
          // 获取排序筛选按钮的位置和宽度
          query.selectAll('.filter-item').boundingClientRect(items => {
            if (items && items.length >= 2) {
              const sortItem = items[1]; // 第二个是排序按钮
              sortFilterLeft.value = sortItem.left;
              filterItemWidth.value = sortItem.width;
            }
          }).exec();
        }
      }).exec();
    });
  }
};

// 页面加载
onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages();
  const page = pages[pages.length - 1];
  const options = page.$page?.options || {};
  
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  
  // 获取服务类型和设置标题
  if (options.type) {
    serviceType.value = options.type;
    currentTopCategory.value = options.type;
    
    // 优先使用传递过来的标题
    if (options.title) {
      serviceTitle.value = decodeURIComponent(options.title);
      console.log('使用传递的标题:', serviceTitle.value);
    } else {
      setServiceTitle(options.type);
    }
    
    // 加载历史筛选条件
    loadFilterHistory();
    
    // 根据类型设置分类选项
    setCategoryOptions(options.type);
    
    // 特殊处理某些服务类型，确保正确的分类筛选
    const specialCategoryMap = {
      'job': '招聘信息',
      'resume': '求职信息',
      'business': '生意转让',
      'house_rent': '房屋出租',
      'house_sell': '房屋出售',
      'second_car': '二手车辆',
      'pet': '宠物信息',
      'car': '车辆服务',
      'second_hand': '二手闲置',
      'carpool': '磁州拼车',
      'education': '教育培训'
    };
    
    // 如果是特殊服务类型，预设对应的分类名称
    if (specialCategoryMap[options.type] && !options.category) {
      console.log(`初始化特殊分类: ${specialCategoryMap[options.type]}`);
    }
  }
  
  // 如果有active参数，设置激活的顶部分类
  if (options.active) {
    console.log('设置激活的顶部分类:', options.active);
    currentTopCategory.value = options.active;
    
    // 如果active参数不同于type，重新设置分类选项
    if (options.active !== serviceType.value) {
      serviceType.value = options.active;
      setCategoryOptions(options.active);
      setServiceTitle(options.active);
    }
  }
  
  // 如果有预设筛选条件
  if (options.area) selectedArea.value = options.area;
  if (options.category) {
    selectedCategory.value = options.category;
    // 如果有一级分类，尝试加载二级分类
    if (selectedCategory.value !== '全部分类') {
      setSubcategoryOptions(selectedCategory.value);
    }
  }
  if (options.subcategory) selectedSubcategory.value = options.subcategory;
  if (options.sort) selectedSort.value = options.sort;
  
  // 添加触感反馈 - 页面加载完成
  setTimeout(() => {
    uni.vibrateShort();
  }, 300);
  
  // 加载服务列表
  loadServiceList();
});
</script>

<style lang="scss" scoped>
/* 全局容器样式 */
.service-filter-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f6f8fa;
  
  &.premium-style {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }
}

/* 高级自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background: linear-gradient(120deg, #0070f3, #00a1ff);
  padding: 0 16px;
  position: relative;
  z-index: 1000;
  box-shadow: 0 1px 12px rgba(0, 112, 243, 0.18);
}

.back-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 2;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
  transition: transform 0.2s ease;
}

.back-btn:active .back-icon {
  transform: rotate(-45deg) scale(0.9);
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: -0.2px;
}

.navbar-right {
  width: 36px;
  position: relative;
  z-index: 2;
}

/* 高级搜索框 */
.search-container {
  padding: 12px 16px 16px;
  background: #fff;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  margin-bottom: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 12px;
  padding: 0 12px;
  height: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.search-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  height: 40px;
  font-size: 15px;
  color: #333;
  background: transparent;
}

.search-btn {
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  border-radius: 10px;
  margin-right: -12px;
}

/* 高级一级分类标签栏 */
.top-category-tabs {
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
  padding: 14px 0 12px;
  white-space: nowrap;
  border-radius: 16px;
  margin: 8px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.top-category-item {
  display: inline-block;
  padding: 8px 20px;
  margin: 0 5px;
  font-size: 15px;
  color: #555;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.top-category-item:first-child {
  margin-left: 16px;
}

.top-category-item:last-child {
  margin-right: 16px;
}

.active-top-category {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}

/* 高级子分类标签栏 */
.subcategory-tabs {
  background: rgba(255, 255, 255, 0.95);
  padding: 10px 0;
  white-space: nowrap;
  border-radius: 16px;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.subcategory-item {
  display: inline-block;
  padding: 6px 16px;
  margin: 0 5px;
  font-size: 14px;
  color: #666;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  background-color: #f1f2f6;
}

.subcategory-item:first-child {
  margin-left: 16px;
}

.subcategory-item:last-child {
  margin-right: 16px;
}

.active-subcategory {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}

/* 高级筛选条件栏 */
.filter-section {
  display: flex;
  height: 54px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  position: relative;
  justify-content: center;
  padding: 0;
  z-index: 95;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
  font-size: 15px;
  color: #333;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.filter-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.filter-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.05);
}

.filter-text {
  display: inline-block;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.active-filter {
  color: #0070f3;
  font-weight: 600;
}

.filter-arrow {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999;
  margin-left: 8px;
  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.arrow-up {
  transform: rotate(180deg);
  border-top-color: #0070f3;
}

/* 高级已选筛选标签 */
.selected-filters {
  margin: 0 12px 12px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  padding: 10px 0;
}

.filter-tags-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: inline-flex;
  padding: 0 16px;
}

.filter-tag {
  display: inline-block;
  padding: 6px 12px;
  background: rgba(0, 112, 243, 0.08);
  color: #0070f3;
  font-size: 13px;
  border-radius: 16px;
  margin-right: 8px;
}

.tag-close {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  border-radius: 50%;
  background-color: rgba(0, 112, 243, 0.15);
  margin-left: 6px;
  font-size: 14px;
}

.reset-all {
  display: inline-block;
  padding: 6px 12px;
  background: rgba(255, 59, 48, 0.08);
  color: #ff3b30;
  font-size: 13px;
  border-radius: 16px;
}

/* 高级下拉菜单 */
.filter-dropdown {
  position: absolute;
  left: 0;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 400px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.dropdown-scroll {
  max-height: 350px;
}

.dropdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s ease;
}

.dropdown-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}

.active-item {
  color: #0070f3;
  font-weight: 500;
}

.dropdown-item-text {
  flex: 1;
}

.dropdown-item-check {
  color: #0070f3;
  font-weight: bold;
}

/* 高级遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 90;
  backdrop-filter: blur(2px);
}

/* 高级内容列表 */
.service-list {
  flex: 1;
  position: relative;
}

/* 高级服务卡片 */
.service-item {
  margin: 0 12px 16px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.service-item-hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.service-content {
  padding: 16px;
}

.service-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
}

.service-tag {
  background: rgba(0, 112, 243, 0.08);
  color: #0070f3;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 8px;
}

.service-subcategory {
  background: rgba(255, 149, 0, 0.08);
  color: #ff9500;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.service-meta {
  display: flex;
  align-items: center;
}

.service-area {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.service-time {
  font-size: 12px;
  color: #999;
}

.service-body {
  margin-bottom: 12px;
}

.service-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.service-images {
  display: flex;
  margin-top: 12px;
  position: relative;
}

.service-image {
  width: 33%;
  height: 180rpx;
  margin-right: 6px;
  border-radius: 8px;
  object-fit: cover;
}

.single-image {
  width: 66%;
}

.image-count {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 12px;
}

.service-stats {
  display: flex;
  align-items: center;
}

.service-views {
  font-size: 12px;
  color: #999;
  margin-right: 12px;
}

.service-price {
  font-size: 16px;
  color: #ff3b30;
  font-weight: 500;
}

.service-actions {
  display: flex;
}

.action-btn {
  display: flex;
  align-items: center;
  background: rgba(0, 112, 243, 0.08);
  color: #0070f3;
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 16px;
}

.contact-btn {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
}

.action-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.action-text {
  font-size: 13px;
}

/* 高级加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.loading-indicator {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0070f3;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
}

.loading-done {
  text-align: center;
  padding: 20px 0;
}

.loading-done-text {
  font-size: 14px;
  color: #999;
}

/* 高级空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.empty-tips {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

.empty-btn {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-size: 14px;
  padding: 8px 24px;
  border-radius: 20px;
}

/* 高级统计提示 */
.result-stats {
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
}

.stats-number {
  color: #0070f3;
  font-weight: 500;
}

.service-items-wrapper {
  padding-bottom: 80px;
}

/* 高级发布按钮 */
.publish-btn {
  position: fixed;
  right: 20px;
  bottom: 30px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  width: 120px;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.3);
  z-index: 99;
}

.publish-btn-hover {
  opacity: 0.9;
  transform: scale(0.98);
}

.publish-icon {
  font-size: 20px;
  margin-right: 4px;
  font-weight: 300;
}

.publish-text {
  font-size: 15px;
}
</style>