"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "driver-verification",
  setup(__props) {
    const verificationStatus = common_vendor.ref("none");
    const rejectReason = common_vendor.ref("");
    const showForm = common_vendor.ref(false);
    const formData = common_vendor.ref({
      realName: "",
      idCard: "",
      driverLicense: "",
      licenseExpireDate: "",
      carNumber: "",
      carModel: "",
      carColor: "",
      carRegDate: "",
      idCardFront: "",
      idCardBack: "",
      driverLicenseImg: "",
      vehicleLicenseImg: "",
      agreement: false
    });
    const isFormValid = common_vendor.computed(() => {
      return formData.value.realName && formData.value.idCard && formData.value.idCard.length === 18 && formData.value.driverLicense && formData.value.licenseExpireDate && formData.value.carNumber && formData.value.carModel && formData.value.carColor && formData.value.carRegDate && formData.value.idCardFront && formData.value.idCardBack && formData.value.driverLicenseImg && formData.value.vehicleLicenseImg && formData.value.agreement;
    });
    common_vendor.onMounted(() => {
      checkVerificationStatus();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const checkVerificationStatus = () => {
      setTimeout(() => {
        verificationStatus.value = "none";
        rejectReason.value = "提供的证件信息不清晰，请重新上传清晰照片";
      }, 500);
    };
    const resetForm = () => {
      showForm.value = true;
      formData.value = {
        realName: "",
        idCard: "",
        driverLicense: "",
        licenseExpireDate: "",
        carNumber: "",
        carModel: "",
        carColor: "",
        carRegDate: "",
        idCardFront: "",
        idCardBack: "",
        driverLicenseImg: "",
        vehicleLicenseImg: "",
        agreement: false
      };
    };
    const openDatePicker = () => {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      common_vendor.index.showDatePicker({
        date: formData.value.licenseExpireDate || `${year}-${month}-${day}`,
        success: (res) => {
          formData.value.licenseExpireDate = res.date;
        }
      });
    };
    const openRegDatePicker = () => {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      common_vendor.index.showDatePicker({
        date: formData.value.carRegDate || `${year}-${month}-${day}`,
        success: (res) => {
          formData.value.carRegDate = res.date;
        }
      });
    };
    const chooseImage = (field) => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          formData.value[field] = res.tempFilePaths[0];
        }
      });
    };
    const deleteImage = (field) => {
      formData.value[field] = "";
    };
    const checkboxChange = (e) => {
      formData.value.agreement = e.detail.value.length > 0;
    };
    const viewAgreement = () => {
      common_vendor.index.showModal({
        title: "司机认证服务协议",
        content: "司机认证服务协议内容...",
        showCancel: false
      });
    };
    const submitVerification = () => {
      if (!isFormValid.value) {
        common_vendor.index.showToast({
          title: "请完善所有必填信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "提交成功，等待审核",
          icon: "success"
        });
        verificationStatus.value = "pending";
        showForm.value = false;
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 2e3);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: _ctx.statusBarHeight + "px",
        b: common_assets._imports_0$7,
        c: common_vendor.o(goBack),
        d: verificationStatus.value !== "none"
      }, verificationStatus.value !== "none" ? common_vendor.e({
        e: verificationStatus.value === "pending" ? "/static/images/icons/pending.png" : verificationStatus.value === "verified" ? "/static/images/icons/verified.png" : "/static/images/icons/rejected.png",
        f: common_vendor.n(verificationStatus.value),
        g: common_vendor.t(verificationStatus.value === "pending" ? "认证审核中" : verificationStatus.value === "verified" ? "认证已通过" : "认证未通过"),
        h: common_vendor.t(verificationStatus.value === "pending" ? "您的认证申请正在审核中，请耐心等待" : verificationStatus.value === "verified" ? "您已通过司机认证，可以发布车找人/车找货信息" : "认证未通过，原因：" + rejectReason.value),
        i: verificationStatus.value === "rejected"
      }, verificationStatus.value === "rejected" ? {
        j: common_vendor.o(resetForm)
      } : {}) : {}, {
        k: verificationStatus.value === "none" || verificationStatus.value === "rejected" && showForm.value
      }, verificationStatus.value === "none" || verificationStatus.value === "rejected" && showForm.value ? common_vendor.e({
        l: formData.value.realName,
        m: common_vendor.o(($event) => formData.value.realName = $event.detail.value),
        n: formData.value.idCard,
        o: common_vendor.o(($event) => formData.value.idCard = $event.detail.value),
        p: formData.value.driverLicense,
        q: common_vendor.o(($event) => formData.value.driverLicense = $event.detail.value),
        r: common_vendor.t(formData.value.licenseExpireDate || "请选择有效期"),
        s: common_assets._imports_1$33,
        t: common_vendor.o(openDatePicker),
        v: formData.value.carNumber,
        w: common_vendor.o(($event) => formData.value.carNumber = $event.detail.value),
        x: formData.value.carModel,
        y: common_vendor.o(($event) => formData.value.carModel = $event.detail.value),
        z: formData.value.carColor,
        A: common_vendor.o(($event) => formData.value.carColor = $event.detail.value),
        B: common_vendor.t(formData.value.carRegDate || "请选择注册日期"),
        C: common_assets._imports_1$33,
        D: common_vendor.o(openRegDatePicker),
        E: !formData.value.idCardFront
      }, !formData.value.idCardFront ? {
        F: common_assets._imports_2$31,
        G: common_vendor.o(($event) => chooseImage("idCardFront"))
      } : {
        H: formData.value.idCardFront,
        I: common_assets._imports_3$29,
        J: common_vendor.o(($event) => deleteImage("idCardFront"))
      }, {
        K: !formData.value.idCardBack
      }, !formData.value.idCardBack ? {
        L: common_assets._imports_2$31,
        M: common_vendor.o(($event) => chooseImage("idCardBack"))
      } : {
        N: formData.value.idCardBack,
        O: common_assets._imports_3$29,
        P: common_vendor.o(($event) => deleteImage("idCardBack"))
      }, {
        Q: !formData.value.driverLicenseImg
      }, !formData.value.driverLicenseImg ? {
        R: common_assets._imports_2$31,
        S: common_vendor.o(($event) => chooseImage("driverLicenseImg"))
      } : {
        T: formData.value.driverLicenseImg,
        U: common_assets._imports_3$29,
        V: common_vendor.o(($event) => deleteImage("driverLicenseImg"))
      }, {
        W: !formData.value.vehicleLicenseImg
      }, !formData.value.vehicleLicenseImg ? {
        X: common_assets._imports_2$31,
        Y: common_vendor.o(($event) => chooseImage("vehicleLicenseImg"))
      } : {
        Z: formData.value.vehicleLicenseImg,
        aa: common_assets._imports_3$29,
        ab: common_vendor.o(($event) => deleteImage("vehicleLicenseImg"))
      }, {
        ac: formData.value.agreement,
        ad: common_vendor.o(viewAgreement),
        ae: common_vendor.o(checkboxChange),
        af: !isFormValid.value,
        ag: common_vendor.o(submitVerification)
      }) : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/driver-verification.js.map
