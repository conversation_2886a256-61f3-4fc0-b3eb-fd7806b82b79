<template>
  <view class="detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">拼车详情</text>
        </view>
        <view class="right-action" @click="showActionSheet">
          <image src="/static/images/tabbar/more-vertical.png" class="action-icon"></image>
        </view>
      </view>
    </view>
    
    <!-- 添加悬浮推广按钮 -->
    <FloatPromotionButton 
      @click="showPromotionTool" 
      :position="{right: '30rpx', bottom: '180rpx'}"
      size="100rpx"
    />
    
    <!-- 内容区域 -->
    <scroll-view class="detail-content" scroll-y>
      <!-- 标签 -->
      <view class="header-tag-container">
        <view class="header-tag" :class="carpoolInfo.type">
          <text>{{typeText}}</text>
          <view class="verified-tag" v-if="carpoolInfo.isVerified">
            <view class="verified-icon">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" fill="currentColor" />
              </svg>
            </view>
            <text>已认证</text>
          </view>
        </view>
        </view>
      
      <!-- 信息头部 -->
      <view class="detail-header">
        <!-- 行程信息 -->
        <view class="route-card">
          <view class="route-points">
            <view class="route-point">
              <view class="point-dot start"></view>
              <view class="point-info">
                <text class="point-name">{{carpoolInfo.startPoint}}</text>
                <text class="point-address" v-if="carpoolInfo.startAddress">（{{carpoolInfo.startAddress}}）</text>
              </view>
            </view>
            
            <view class="route-divider">
              <view class="divider-line"></view>
              <view class="divider-info">
                <text class="divider-text">约{{carpoolInfo.distance}}公里</text>
              </view>
            </view>
            
            <view class="route-point">
              <view class="point-dot end"></view>
              <view class="point-info">
                <text class="point-name">{{carpoolInfo.endPoint}}</text>
                <text class="point-address" v-if="carpoolInfo.endAddress">（{{carpoolInfo.endAddress}}）</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 行程详情 -->
        <view class="trip-details-card">
          <view class="trip-details-header">
            <text class="section-title">行程详情</text>
            </view>
          
          <view class="trip-details-content">
            <view class="trip-details-item">
              <text class="trip-details-label">上车地点</text>
              <text class="trip-details-value">{{carpoolInfo.startPoint}}</text>
            </view>
            <view class="trip-details-item">
              <text class="trip-details-label">下车地点</text>
              <text class="trip-details-value">{{carpoolInfo.endPoint}}</text>
          </view>
            <view class="trip-details-item">
              <text class="trip-details-label">行程距离</text>
              <text class="trip-details-value">{{carpoolInfo.distance}}公里</text>
            </view>
            <view class="trip-details-item">
              <text class="trip-details-label">预计用时</text>
              <text class="trip-details-value">约{{carpoolInfo.estimatedDuration}}小时</text>
            </view>
            <view class="trip-details-item" v-if="carpoolInfo.tripNotes">
              <text class="trip-details-label">备注</text>
              <text class="trip-details-value">{{carpoolInfo.tripNotes}}</text>
          </view>
            </view>
            </view>
        
        <!-- 出发时间 -->
        <view class="info-card">
          <view class="info-item-new">
            <text class="info-label-new">出发时间</text>
            <text class="info-value-new">{{carpoolInfo.departureDate}} {{carpoolInfo.departureTime}}</text>
          </view>
          
          <view class="info-item-new" v-if="carpoolInfo.type === 'people-to-car'">
            <text class="info-label-new">乘车人数</text>
            <text class="info-value-new">{{carpoolInfo.passengers}}人</text>
            </view>
          
          <view class="info-item-new" v-if="carpoolInfo.type === 'car-to-people'">
            <text class="info-label-new">空余座位</text>
            <text class="info-value-new">{{carpoolInfo.availableSeats}}个</text>
            </view>
          
          <view class="info-item-new" v-if="carpoolInfo.price">
            <text class="info-label-new">参考价格</text>
            <text class="info-value-new price-value">¥{{carpoolInfo.price}}</text>
          </view>
        </view>
      </view>
      
      <!-- 用户信息 -->
      <view class="user-card">
        <view class="user-header">
          <text class="section-title">发布人信息</text>
        </view>
        
        <view class="user-info">
          <image class="user-avatar" :src="carpoolInfo.avatar" mode="aspectFill"></image>
          <view class="user-details">
            <view class="user-name-row">
              <text class="user-name">{{carpoolInfo.username}}</text>
              <view class="user-badges">
                <view class="user-badge verified" v-if="carpoolInfo.isVerified">
                  <view class="verified-icon-badge">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" fill="currentColor" />
                    </svg>
                  </view>
                  <text>已认证</text>
                </view>
                <view class="user-badge premium" v-if="carpoolInfo.isPremium">置顶</view>
              </view>
            </view>
            <text class="user-meta">{{carpoolInfo.publishTime}} 发布</text>
          </view>
        </view>
        
        <!-- 司机评分 - 仅在车找人类型显示 -->
        <view class="driver-rating" v-if="carpoolInfo.type === 'car-to-people'">
          <view class="rating-header">
            <text class="rating-title">司机评分</text>
            <text class="rating-count">{{carpoolInfo.ratingCount || 0}}人评价</text>
          </view>
          <view class="rating-stars">
            <view class="star-container">
              <view class="star-fill" :style="{ width: (carpoolInfo.rating || 0) * 20 + '%' }"></view>
              <view class="star-bg"></view>
            </view>
            <text class="rating-value">{{carpoolInfo.rating || 0}}</text>
          </view>
          <view class="rating-tags" v-if="carpoolInfo.ratingTags && carpoolInfo.ratingTags.length > 0">
            <view class="rating-tag" v-for="(tag, index) in carpoolInfo.ratingTags" :key="index">
              {{tag}}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 车辆信息 -->
      <view class="car-card" v-if="carpoolInfo.type.includes('car-to')">
        <view class="car-header">
          <text class="section-title">车辆信息</text>
        </view>
        
        <view class="car-info">
          <view class="car-item">
            <text class="car-label">车型</text>
            <text class="car-value">{{carpoolInfo.carModel}}</text>
          </view>
          <view class="car-item">
            <text class="car-label">颜色</text>
            <text class="car-value">{{carpoolInfo.carColor}}</text>
          </view>
          <view class="car-item">
            <text class="car-label">车牌</text>
            <text class="car-value">{{carpoolInfo.carPlate}}</text>
          </view>
        </view>
      </view>
      
      <!-- 补充说明 -->
      <view class="remark-card" v-if="carpoolInfo.remark">
        <view class="remark-header">
          <text class="section-title">补充说明</text>
        </view>
        
        <view class="remark-content">
          <text>{{carpoolInfo.remark}}</text>
        </view>
      </view>
      
      <!-- 免责声明 -->
      <view class="disclaimer-card">
        <view class="disclaimer-header">
          <text class="section-title">免责声明</text>
        </view>
        <view class="disclaimer-content">
          <view class="disclaimer-icon">
            <image src="/static/images/tabbar/声明.png" mode="aspectFit"></image>
          </view>
          <text class="disclaimer-text">本平台仅提供信息对接服务，不对拼车双方的行为提供任何担保，不承担任何法律责任。请用户自行甄别信息真实性，注意出行安全。使用本平台即表示您已同意以上条款。</text>
        </view>
      </view>
      
      <!-- 发布时间 -->
      <view class="publish-time-card">
      <view class="publish-time">
        <text>发布时间：{{carpoolInfo.publishTime}}</text>
        <text>信息有效期至：{{carpoolInfo.expiryTime}}</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-group">
        <button class="action-btn share" open-type="share">
          <image src="/static/images/tabbar/a分享.png" mode="aspectFit"></image>
          <text>转发</text>
        </button>
        <button class="action-btn favorite" @click="toggleFavorite">
          <image :src="isFavorite ? '/static/images/tabbar/a收藏选中.png' : '/static/images/tabbar/a收藏.png'" mode="aspectFit"></image>
          <text>{{isFavorite ? '已收藏' : '收藏'}}</text>
        </button>
        <button class="action-btn rate" @click="rateDriver">
          <image src="/static/images/tabbar/a评价.png" mode="aspectFit"></image>
          <text>评价</text>
        </button>
        <button class="action-btn message" @click="sendMessage">
          <image src="/static/images/tabbar/a私信.png" mode="aspectFit"></image>
          <text>私信</text>
        </button>
      </view>
      <view class="call-btn-container">
        <button class="call-btn" @click="callDriver">
          <image src="/static/images/tabbar/电话.png" mode="aspectFit" style="filter: brightness(0) invert(1);"></image>
          <text>联系司机</text>
        </button>
      </view>
    </view>
    
    <!-- 安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import FloatPromotionButton from '@/components/FloatPromotionButton.vue';

// 状态栏高度
const statusBarHeight = ref(20);

// 拼车ID
const carpoolId = ref('');
// 收藏状态
const isFavorite = ref(false);

// 拼车信息
const carpoolInfo = ref({
  id: 1,
  type: 'car-to-people',
  startPoint: '磁州城区-汽车站',
  startAddress: '河北省邯郸市磁县磁州镇汽车站',
  endPoint: '邯郸火车站',
  endAddress: '河北省邯郸市邯山区陵西大街火车站',
  departureDate: '2023-10-25',
  departureTime: '15:30',
  distance: '35',
  estimatedDuration: '1',
  tripNotes: '途经安徽-河南-陕西-汉中，请提前10分钟到达上车地点',
  passengers: 1,
  availableSeats: 3,
  price: '30',
  username: '李师傅',
  avatar: '/static/images/avatar/user2.png',
  phone: '13987654321',
  isVerified: true,
  isPremium: true,
  publishTime: '2023-10-22 12:30',
  expiryTime: '2023-10-25 15:30',
  remark: '准时发车，不等待，有行李提前告知，谢谢配合',
  carModel: '大众途观',
  carColor: '白色',
  carPlate: '冀G·12345',
  rating: 4.8,
  ratingCount: 125,
  ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']
});

// 计算属性
const typeText = computed(() => {
  const typeMap = {
    'people-to-car': '人找车',
    'car-to-people': '车找人',
    'goods-to-car': '货找车',
    'car-to-goods': '车找货'
  };
  return typeMap[carpoolInfo.value.type] || '人找车';
});

// 页面加载
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight || 20;
  
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  if (options && options.id) {
    carpoolId.value = options.id;
    if (options.type) {
      carpoolInfo.value.type = options.type;
    }
    getCarpoolDetail();
  }
});

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};

// 分享到聊天
const onShareAppMessage = (options) => {
  return {
    title: `${typeText.value}：${carpoolInfo.value.startPoint} → ${carpoolInfo.value.endPoint}`,
    path: `/carpool-package/pages/carpool/detail/index?id=${carpoolId.value}&type=${carpoolInfo.value.type}`,
    imageUrl: '/static/images/share-cover.png',
    desc: `出发时间: ${carpoolInfo.value.departureDate} ${carpoolInfo.value.departureTime}`,
    success: (res) => {
      uni.showToast({
        title: '转发成功',
        icon: 'success'
      });
    },
    fail: (err) => {
      console.error('转发失败', err);
    }
  };
};

// 分享到朋友圈
const onShareTimeline = () => {
  return {
    title: `${typeText.value}：${carpoolInfo.value.startPoint} → ${carpoolInfo.value.endPoint}`,
    query: `id=${carpoolId.value}&type=${carpoolInfo.value.type}`,
    imageUrl: '/static/images/share-cover.png',
    success: (res) => {
      uni.showToast({
        title: '转发成功',
        icon: 'success'
      });
    },
    fail: (err) => {
      console.error('转发失败', err);
    }
  };
};

// 获取拼车详情
const getCarpoolDetail = () => {
  // 这里应该是真实的API调用
  // 目前使用模拟数据
  console.log('获取拼车ID:', carpoolId.value, '类型:', carpoolInfo.value.type);
  
  // 模拟API调用
  uni.showLoading({
    title: '加载中...'
  });
  
  // 模拟不同类型的拼车数据
  const mockData = {
    // 车找人的示例数据
    'car-to-people': {
      id: carpoolId.value,
      type: 'car-to-people',
      startPoint: '磁州城区-汽车站',
      startAddress: '磁州区新世纪广场北侧100米',
      endPoint: '邯郸火车站',
      endAddress: '邯郸市丛台区陵西大街61号',
      distance: '35',
      departureDate: '2023-10-15',
      departureTime: '14:30',
      availableSeats: 3,
      price: '25',
      username: '李师傅',
      avatar: '/static/images/avatar/user2.png',
      isVerified: true,
      isPremium: true,
      publishTime: '2023-10-14 09:30',
      expiryTime: '2023-10-17 09:30',
      contactPhone: '13987654321',
      userId: 'user456',
      carModel: '大众朗逸',
      carColor: '白色',
      carPlate: '冀D·12345',
      remark: '准时发车，不等人，请提前到达上车点。可提供小费上门接送。',
      rating: 4.8,
      ratingCount: 125,
      ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']
    },
    // 人找车的示例数据
    'people-to-car': {
      id: carpoolId.value,
      type: 'people-to-car',
      startPoint: '磁县人民医院',
      startAddress: '磁县人民医院南门',
      endPoint: '邯郸东站',
      endAddress: '邯郸市丛台区邯郸东站',
      distance: '30',
      departureDate: '2023-10-16',
      departureTime: '09:30',
      passengers: 2,
      price: '20',
      username: '王先生',
      avatar: '/static/images/avatar/user3.png',
      isVerified: true,
      isPremium: false,
      publishTime: '2023-10-15 08:30',
      expiryTime: '2023-10-18 08:30',
      contactPhone: '13812345678',
      userId: 'user789',
      remark: '两人行李少，希望拼车去高铁站。',
      rating: 4.8,
      ratingCount: 125,
      ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']
    },
    // 货找车的示例数据
    'goods-to-car': {
      id: carpoolId.value,
      type: 'goods-to-car',
      startPoint: '磁县商贸城',
      startAddress: '磁县商贸城西门',
      endPoint: '邯郸市区',
      endAddress: '邯郸市丛台区中心广场',
      distance: '28',
      departureDate: '2023-10-17',
      departureTime: '13:00',
      price: '40',
      username: '张先生',
      avatar: '/static/images/avatar/user4.png',
      isVerified: true,
      isPremium: false,
      publishTime: '2023-10-16 10:30',
      expiryTime: '2023-10-19 10:30',
      contactPhone: '13698765432',
      userId: 'user101',
      remark: '一箱水果，约20公斤，需要送到市区。',
      rating: 4.8,
      ratingCount: 125,
      ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']
    },
    // 车找货的示例数据
    'car-to-goods': {
      id: carpoolId.value,
      type: 'car-to-goods',
      startPoint: '邯郸物流园',
      startAddress: '邯郸市物流园区北门',
      endPoint: '磁县',
      endAddress: '磁县城区',
      distance: '32',
      departureDate: '2023-10-18',
      departureTime: '16:00',
      price: '35',
      username: '赵师傅',
      avatar: '/static/images/avatar/user5.png',
      isVerified: true,
      isPremium: true,
      publishTime: '2023-10-17 14:30',
      expiryTime: '2023-10-20 14:30',
      contactPhone: '13765432109',
      userId: 'user202',
      carModel: '五菱小卡',
      carColor: '白色',
      carPlate: '冀D·54321',
      remark: '回程车辆，可带小件货物，当天到达。',
      rating: 4.8,
      ratingCount: 125,
      ratingTags: ['准时守信', '驾驶平稳', '热情礼貌', '车内整洁']
    }
  };
  
  // 使用传递过来的类型参数选择正确的数据
  setTimeout(() => {
    // 确保我们有有效的类型，否则使用默认类型
    const type = carpoolInfo.value.type || 'car-to-people';
    
    // 更新拼车信息，保留原始的类型
    const originalType = carpoolInfo.value.type;
    carpoolInfo.value = mockData[type] || mockData['car-to-people'];
    
    // 确保类型正确
    carpoolInfo.value.type = originalType;
    
    uni.hideLoading();
  }, 500);
};

// 显示操作菜单
const showActionSheet = () => {
  uni.showActionSheet({
    itemList: ['评价司机', '举报', '刷新', '复制信息'],
    success: (res) => {
      switch(res.tapIndex) {
        case 0:
          rateDriver();
          break;
        case 1:
          reportCarpool();
          break;
        case 2:
          refreshCarpool();
          break;
        case 3:
          copyInfo();
          break;
      }
    }
  });
};

// 举报
const reportCarpool = () => {
  uni.navigateTo({
    url: `/pages/carpool/report?id=${carpoolId.value}`
  });
};

// 刷新
const refreshCarpool = () => {
  uni.showLoading({
    title: '刷新中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    });
  }, 1000);
};

// 显示推广工具
const showPromotionTool = () => {
  uni.navigateTo({
    url: `/subPackages/promotion/pages/promotion-tool?type=carpool&id=${carpoolInfo.value.id}`
  });
};

// 复制信息
const copyInfo = () => {
  const info = `${typeText.value}：${carpoolInfo.value.startPoint} → ${carpoolInfo.value.endPoint}，出发时间：${carpoolInfo.value.departureDate} ${carpoolInfo.value.departureTime}，联系人：${carpoolInfo.value.username}，电话：${carpoolInfo.value.contactPhone}`;
  
  uni.setClipboardData({
    data: info,
    success: () => {
      uni.showToast({
        title: '已复制到剪贴板',
        icon: 'success'
      });
    }
  });
};

// 切换收藏状态
const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  
  uni.showToast({
    title: isFavorite.value ? '收藏成功' : '已取消收藏',
    icon: 'success'
  });
};

// 拨打电话
const callDriver = () => {
  uni.makePhoneCall({
    phoneNumber: carpoolInfo.value.phone
  });
};

// 评价司机
const rateDriver = () => {
  // 检查是否有联系过司机
  const contactHistory = uni.getStorageSync('contactHistory') || [];
  const hasContacted = contactHistory.some(item => item.userId === carpoolInfo.value.userId);
  
  if (hasContacted) {
    // 已联系过，可以直接评价
    uni.navigateTo({
      url: `/carpool-package/pages/carpool/my/create-rating?driverId=${carpoolInfo.value.userId}&phoneNumber=${carpoolInfo.value.contactPhone}&carpoolId=${carpoolInfo.value.id}&startLocation=${encodeURIComponent(carpoolInfo.value.startPoint)}&endLocation=${encodeURIComponent(carpoolInfo.value.endPoint)}&departureTime=${carpoolInfo.value.departureTime}&departureDate=${carpoolInfo.value.departureDate}`
    });
  } else {
    // 显示提示对话框
    uni.showModal({
      title: '提示',
      content: '您需要先联系司机后才能进行评价，是否立即联系司机？',
      success: (res) => {
        if (res.confirm) {
          callDriver();
        }
      }
    });
  }
};

// 发送私信
const sendMessage = () => {
  // 实现发送私信的逻辑
  console.log('发送私信');
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/message/chat?userId=${carpoolInfo.value.userId}&username=${encodeURIComponent(carpoolInfo.value.username)}&avatar=${encodeURIComponent(carpoolInfo.value.avatar)}&type=carpool&carpoolId=${carpoolInfo.value.id}`
  });
};

// 暴露分享方法
defineExpose({
  onShareAppMessage,
  onShareTimeline
});
</script>

<style lang="scss">
.detail-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(44px + var(--status-bar-height));
}

/* 自定义导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}

.header-content {
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.left-action, .right-action {
  width: 40px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  text-align: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
}

/* 内容区域 */
.detail-content {
  padding: 5rpx 20rpx 20rpx;
  height: calc(100vh - 60px - var(--status-bar-height) - 44px);
  box-sizing: border-box;
}

/* 标签容器 */
.header-tag-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24rpx;
  padding: 25px 24rpx 0;
}

/* 信息头部 */
.detail-header {
  margin-bottom: 32rpx;
  width: 100%;
}

.header-tag {
  display: inline-flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  padding: 10rpx 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: relative;
}

.header-tag.people-to-car {
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
}

.header-tag.car-to-people {
  background: linear-gradient(135deg, #FF2D55, #FF9500);
}

.header-tag.goods-to-car {
  background: linear-gradient(135deg, #30D158, #34C759);
}

.header-tag.car-to-goods {
  background: linear-gradient(135deg, #FF9F0A, #FFD60A);
}

.verified-tag {
  font-size: 22rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 12rpx;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 5;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.verified-icon {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background-color: #3DE07E;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);
}

.verified-icon svg {
  color: white;
}

/* 路线卡片 */
.route-card, .info-card, .user-card, .car-card, .remark-card, .trip-details-card, .disclaimer-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx 32rpx 24rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.08);
  width: calc(100% - 48rpx);
  box-sizing: border-box;
  overflow: hidden;
}

.route-points {
  position: relative;
}

.route-point {
  display: flex;
  align-items: flex-start;
  position: relative;
  padding: 20rpx 0;
}

.point-dot {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  margin-top: 8rpx;
  flex-shrink: 0;
  z-index: 2;
}

.start {
  background-color: #0A84FF;
  box-shadow: 0 0 0 8rpx rgba(10, 132, 255, 0.15);
}

.end {
  background-color: #FF2D55;
  box-shadow: 0 0 0 8rpx rgba(255, 45, 85, 0.15);
}

.point-info {
  flex: 1;
}

.point-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.point-address {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
  display: block;
}

.route-divider {
  padding: 10rpx 0 10rpx 14rpx;
  position: relative;
}

.divider-line {
  position: absolute;
  left: 14rpx;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #0A84FF, #FF2D55);
  z-index: 1;
}

.divider-info {
  margin-left: 34rpx;
  background-color: #F2F7FD;
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  display: inline-block;
}

.divider-text {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

/* 信息卡片 - 新设计 */
.info-card {
  display: flex;
  flex-wrap: wrap;
  gap: 0;
  background-color: #ffffff;
}

.info-item-new {
  width: 50%;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #F2F2F7;
}

.info-item-new:nth-child(odd) {
  border-right: 1px solid #F2F2F7;
}

.info-label-new {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}

.info-value-new {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.price-value {
  color: #FF2D55;
}

/* 用户卡片 */
.user-card, .car-card, .remark-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.08);
}

.user-header, .car-header, .remark-header {
  margin-bottom: 24rpx;
  border-bottom: 1px solid #F2F2F7;
  padding-bottom: 16rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  bottom: 6rpx;
  width: 6rpx;
  background: linear-gradient(to bottom, #0A84FF, #5AC8FA);
  border-radius: 3rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.user-details {
  flex: 1;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 12rpx;
}

.user-badges {
  display: flex;
  gap: 8rpx;
  flex-wrap: nowrap;
}

.user-badge {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.user-badge.verified {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.15), rgba(10, 132, 255, 0.05));
  color: #0A84FF;
  border: 1px solid rgba(10, 132, 255, 0.3);
  display: inline-flex;
  align-items: center;
  box-shadow: 0 2rpx 6rpx rgba(10, 132, 255, 0.1);
}

.verified-icon-badge {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #0A84FF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6rpx;
}

.verified-icon-badge svg {
  color: white;
}

.user-badge.premium {
  background-color: rgba(255, 45, 85, 0.1);
  color: #FF2D55;
  border: 1px solid rgba(255, 45, 85, 0.3);
}

.user-meta {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 车辆信息 */
.car-info {
  display: flex;
  flex-wrap: wrap;
}

.car-item {
  width: 50%;
  margin-bottom: 16rpx;
}

.car-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
  display: block;
}

.car-value {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

/* 补充说明 */
.remark-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 行程详情 */
.trip-details-header {
  margin-bottom: 24rpx;
  border-bottom: 1px solid #F2F2F7;
  padding-bottom: 16rpx;
}

.trip-details-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.trip-details-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 8rpx 0;
  border-bottom: 1px solid #F9F9F9;
}

.trip-details-label {
  font-size: 28rpx;
  color: #8E8E93;
  flex: 1;
}

.trip-details-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  flex: 2;
  text-align: right;
}

/* 免责声明 */
.disclaimer-content {
  display: flex;
  align-items: flex-start;
}

.disclaimer-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.disclaimer-icon image {
  width: 100%;
  height: 100%;
}

.disclaimer-text {
  font-size: 26rpx;
  color: #8E8E93;
  line-height: 1.6;
  flex: 1;
}

/* 发布时间 */
.publish-time-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.08);
  width: calc(100% - 48rpx);
  box-sizing: border-box;
  overflow: hidden;
}

.publish-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.publish-time text {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
  border-top: 1px solid #f5f5f5;
}

.action-group {
  display: flex;
  align-items: center;
  flex: 1;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  flex: 1;
  background: transparent;
  border: none;
  box-shadow: none;
  height: 100rpx;
}

.action-btn::after {
  border: none;
}

.action-btn image {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 8rpx;
}

.action-btn text {
  font-size: 22rpx;
  color: #666666;
  line-height: 1;
}

.call-btn-container {
  display: flex;
  justify-content: flex-end;
  width: 240rpx;
}

.call-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  border-radius: 50rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  background-color: #1677FF;
  color: #ffffff;
  border: none;
  box-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.2);
  width: 100%;
}

.call-btn image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.call-btn::after {
  border: none;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}

/* 司机评分 */
.driver-rating {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1px solid #F2F2F7;
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.rating-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.rating-count {
  font-size: 24rpx;
  color: #8E8E93;
}

.rating-stars {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.star-container {
  position: relative;
  width: 240rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.star-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/static/images/tabbar/rating/star-bg.png');
  background-size: 240rpx 36rpx;
  background-repeat: no-repeat;
}

.star-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-image: url('/static/images/tabbar/rating/star-fill.png');
  background-size: 240rpx 36rpx;
  background-repeat: no-repeat;
  z-index: 1;
}

.rating-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF9500;
}

.rating-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.rating-tag {
  font-size: 24rpx;
  color: #666666;
  background-color: #F2F7FD;
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
}
</style>
