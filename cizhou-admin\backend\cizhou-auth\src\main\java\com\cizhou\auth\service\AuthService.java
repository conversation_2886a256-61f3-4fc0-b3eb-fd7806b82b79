package com.cizhou.auth.service;

import com.cizhou.auth.dto.ChangePasswordRequest;
import com.cizhou.auth.dto.LoginRequest;
import com.cizhou.auth.dto.LoginResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

/**
 * 认证服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface AuthService {

    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest request);

    /**
     * 用户登出
     */
    void logout(String token);

    /**
     * 刷新Token
     */
    LoginResponse refreshToken(String refreshToken);

    /**
     * 获取用户信息
     */
    Object getUserInfo(String token);

    /**
     * 获取用户权限
     */
    Object getUserPermissions(String token);

    /**
     * 修改密码
     */
    void changePassword(String token, ChangePasswordRequest request);

    /**
     * 生成验证码
     */
    void generateCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 验证Token
     */
    boolean validateToken(String token);
}
