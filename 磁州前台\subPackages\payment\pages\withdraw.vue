<template>
  <view class="withdraw-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">提现</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 提现金额区域 -->
    <view class="withdraw-section" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <view class="section-title">提现金额</view>
      <view class="amount-input-area">
        <text class="currency-symbol">¥</text>
        <input 
          class="amount-input" 
          type="digit" 
          v-model="amount" 
          placeholder="0.00"
          @input="handleAmountInput"
          focus
        />
      </view>
      <view class="balance-info">
        <text>可提现余额: ¥{{ balanceInfo.amount.toFixed(2) }}</text>
        <text class="withdraw-all" @click="withdrawAll">全部提现</text>
      </view>
    </view>
    
    <!-- 提现方式 -->
    <view class="withdraw-method">
      <view class="section-title">提现方式</view>
      <view class="bank-card-section">
        <view class="bank-card" v-if="selectedCard">
          <view class="bank-logo">
            <image :src="selectedCard.bankLogo" class="bank-logo-img"></image>
          </view>
          <view class="bank-info">
            <view class="bank-name">{{ selectedCard.bankName }}</view>
            <view class="card-number">**** **** **** {{ selectedCard.cardNumberLast4 }}</view>
          </view>
          <view class="bank-action" @click="navigateTo('/pages/my/wallet-bank')">
            <text>更换</text>
          </view>
        </view>
        <view class="add-card" v-else @click="navigateTo('/pages/my/wallet-bank')">
          <image src="/static/images/tabbar/添加.png" class="add-icon"></image>
          <text>添加银行卡</text>
        </view>
      </view>
    </view>
    
    <!-- 提现说明 -->
    <view class="withdraw-notice">
      <view class="notice-title">提现说明</view>
      <view class="notice-item">1. 提现金额最低1元，最高5000元</view>
      <view class="notice-item">2. 提现将在1-3个工作日内到账</view>
      <view class="notice-item">3. 提现手续费为金额的0.6%，最低1元</view>
    </view>
    
    <!-- 提现按钮 -->
    <view class="bottom-btn-area">
      <button 
        class="withdraw-btn" 
        :disabled="!canWithdraw" 
        :class="{'btn-disabled': !canWithdraw}"
        @click="submitWithdraw"
      >
        确认提现
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { smartNavigate } from '@/utils/navigation.js';

// 响应式状态
const statusBarHeight = ref(20);
const navbarHeight = ref(64);
const amount = ref('');
const balanceInfo = ref({
  amount: 158.50,
  frozenAmount: 0.00
});
const selectedCard = ref(null);
const bankCards = ref([]);

// 计算属性
const canWithdraw = computed(() => {
  const amountNum = parseFloat(amount.value);
  return amountNum >= 1 && 
         amountNum <= 5000 && 
         amountNum <= balanceInfo.value.amount &&
         selectedCard.value;
});

// 方法
// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面跳转
const navigateTo = (url) => {
  smartNavigate(url).catch(err => {
    console.error('页面跳转失败:', err);
  });
};

// 获取钱包余额
const getWalletBalance = () => {
  // 这里应该是从API获取钱包余额
  // 模拟API请求
  setTimeout(() => {
    balanceInfo.value = {
      amount: 158.50,
      frozenAmount: 0.00
    };
  }, 500);
};

// 获取银行卡列表
const getBankCards = () => {
  // 模拟API请求获取银行卡列表
  setTimeout(() => {
    bankCards.value = [
      {
        id: 'card001',
        bankName: '中国建设银行',
        bankLogo: '/static/images/banks/ccb.png',
        cardNumber: '6217 0012 3456 7890',
        cardNumberLast4: '7890',
        isDefault: true
      }
    ];
    
    // 设置默认选中的银行卡
    if (bankCards.value.length > 0) {
      selectedCard.value = bankCards.value.find(card => card.isDefault) || bankCards.value[0];
    }
  }, 500);
};

// 处理金额输入
const handleAmountInput = (e) => {
  const value = e.detail.value;
  // 限制只能输入两位小数
  if (value.indexOf('.') !== -1) {
    const decimal = value.split('.')[1];
    if (decimal.length > 2) {
      amount.value = parseFloat(value).toFixed(2);
    }
  }
};

// 全部提现
const withdrawAll = () => {
  amount.value = balanceInfo.value.amount.toFixed(2);
};

// 提交提现申请
const submitWithdraw = () => {
  if (!canWithdraw.value) {
    return;
  }
  
  uni.showLoading({
    title: '提交中...'
  });
  
  // 模拟API请求
  setTimeout(() => {
    uni.hideLoading();
    
    // 提示用户提现申请已提交
    uni.showModal({
      title: '提示',
      content: '提现申请已提交，将在1-3个工作日内到账',
      showCancel: false,
      success: () => {
        // 返回钱包页面
        uni.navigateBack();
      }
    });
  }, 1500);
};

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  navbarHeight.value = statusBarHeight.value + 44;
  
  // 获取钱包余额
  getWalletBalance();
  
  // 获取银行卡列表
  getBankCards();
});
</script>

<style>
.withdraw-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 提现金额区域 */
.withdraw-section {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.amount-input-area {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.currency-symbol {
  font-size: 50rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.amount-input {
  font-size: 60rpx;
  font-weight: bold;
  flex: 1;
}

.balance-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.withdraw-all {
  color: #0052CC;
}

/* 提现方式 */
.withdraw-method {
  background-color: #fff;
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.bank-card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fc;
  border-radius: 10rpx;
}

.bank-logo {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bank-logo-img {
  width: 60rpx;
  height: 60rpx;
}

.bank-info {
  flex: 1;
}

.bank-name {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.card-number {
  font-size: 24rpx;
  color: #999;
}

.bank-action {
  font-size: 24rpx;
  color: #0052CC;
}

.add-card {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  background-color: #f8f9fc;
  border-radius: 10rpx;
  color: #0052CC;
  font-size: 28rpx;
}

.add-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

/* 提现说明 */
.withdraw-notice {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.notice-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.notice-item {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 底部按钮区域 */
.bottom-btn-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.withdraw-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #0052CC;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}

.btn-disabled {
  background-color: #cccccc;
  color: #ffffff;
}
</style> 
