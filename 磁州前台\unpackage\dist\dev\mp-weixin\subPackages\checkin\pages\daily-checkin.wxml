<view class="checkin-container"><view class="navbar"><view class="navbar-left"><view class="back-button" bindtap="{{c}}"><svg wx:if="{{b}}" u-s="{{['d']}}" u-i="a2bbf940-0" bind:__l="__l" u-p="{{b}}"><path wx:if="{{a}}" u-i="a2bbf940-1,a2bbf940-0" bind:__l="__l" u-p="{{a}}"/></svg></view></view><view class="navbar-title"><text class="title-text">每日签到</text></view><view class="navbar-right"></view></view><scroll-view scroll-y class="content-area"><view class="checkin-card"><view class="card-header"><text class="card-title">每日签到</text><text class="card-subtitle">连续签到{{d}}天</text></view><view class="calendar-section"><view class="month-header"><text class="month-text">{{e}}年{{f}}月</text></view><view class="weekdays"><text wx:for="{{g}}" wx:for-item="day" wx:key="b" class="weekday">{{day.a}}</text></view><view class="days-grid"><view wx:for="{{h}}" wx:for-item="day" wx:key="d" class="{{['day-item', day.e && 'empty', day.f && 'checked', day.g && 'today', day.h && 'disabled']}}"><text wx:if="{{day.a}}" class="day-number">{{day.b}}</text><view wx:if="{{day.c}}" class="checked-mark"></view></view></view></view><view class="checkin-button-container"><view class="{{['checkin-button', l && 'disabled']}}" bindtap="{{m}}"><text>{{i}}</text><text wx:if="{{j}}" class="points-text">+{{k}}积分</text></view></view></view><view class="rules-card"><view class="card-header"><text class="card-title">签到规则</text></view><view class="rules-content"><view class="rule-item"><text class="rule-number">1.</text><text class="rule-text">每日签到可获得{{n}}积分</text></view><view class="rule-item"><text class="rule-number">2.</text><text class="rule-text">连续签到7天可额外获得{{o}}积分</text></view><view class="rule-item"><text class="rule-number">3.</text><text class="rule-text">连续签到30天可额外获得{{p}}积分</text></view><view class="rule-item"><text class="rule-number">4.</text><text class="rule-text">每日签到时间为00:00-23:59</text></view></view></view><view class="records-card"><view class="card-header"><text class="card-title">签到记录</text></view><view class="records-list"><view wx:for="{{q}}" wx:for-item="record" wx:key="d" class="record-item"><view class="record-date"><text class="date-text">{{record.a}}</text><text class="time-text">{{record.b}}</text></view><view class="record-points"><text class="points-value">+{{record.c}}</text><text class="points-label">积分</text></view></view></view></view><view class="bottom-space"></view></scroll-view></view>