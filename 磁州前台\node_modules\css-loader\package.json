{"name": "css-loader", "version": "6.11.0", "description": "css loader module for webpack", "license": "MIT", "repository": "webpack-contrib/css-loader", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/css-loader", "bugs": "https://github.com/webpack-contrib/css-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 12.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "validate:runtime": "es-check es5 \"dist/runtime/**/*.js\"", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "postbuild": "npm run validate:runtime", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}, "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.4.33", "postcss-modules-extract-imports": "^3.1.0", "postcss-modules-local-by-default": "^4.0.5", "postcss-modules-scope": "^3.2.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.2.0", "semver": "^7.5.4"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.7", "@commitlint/cli": "^16.3.0", "@commitlint/config-conventional": "^16.2.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^28.1.3", "cross-env": "^7.0.3", "cspell": "^6.31.2", "del": "^6.1.1", "del-cli": "^4.0.1", "es-check": "^7.1.0", "eslint": "^8.54.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-import": "^2.29.0", "file-loader": "^6.2.0", "husky": "^7.0.1", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "less": "^4.2.0", "less-loader": "^10.0.1", "lint-staged": "^12.5.0", "memfs": "^3.5.3", "mini-css-extract-plugin": "^2.7.5", "npm-run-all": "^4.1.5", "postcss-loader": "^6.2.1", "postcss-preset-env": "^7.8.3", "prettier": "^2.8.7", "sass": "^1.69.7", "sass-loader": "^12.6.0", "standard-version": "^9.5.0", "strip-ansi": "^6.0.0", "style-loader": "^3.3.2", "stylus": "^0.59.0", "stylus-loader": "^6.1.0", "url-loader": "^4.1.1", "webpack": "^5.89.0"}, "keywords": ["webpack", "css", "loader", "url", "import"]}