
/* 添加会员等级页面样式开始 */
.add-level-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
}

/* 表单内容样式 */
.form-content {
  padding: 20rpx;
}
.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 20rpx;
}
.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}
.form-input-group .form-input {
  flex: 1;
  border: none;
}
.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}

/* 图标上传 */
.icon-upload {
  width: 120rpx;
  height: 120rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}
.preview-icon {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}
.upload-text {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: 2rpx solid transparent;
}
.color-option.active {
  border-color: #333;
  transform: scale(1.1);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20rpx;
}
.radio-item {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}
.radio-item.active {
  background: rgba(74, 0, 224, 0.1);
  border-color: #4A00E0;
}
.radio-text {
  font-size: 26rpx;
  color: #666;
}
.radio-item.active .radio-text {
  color: #4A00E0;
  font-weight: 600;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 特权列表 */
.privilege-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 10rpx;
}
.privilege-item {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}
.privilege-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.privilege-checkbox.checked {
  border-color: #4A00E0;
  background: #4A00E0;
}
.checkbox-inner {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background: #fff;
}
.privilege-name {
  font-size: 26rpx;
  color: #666;
}

/* 自定义权益 */
.custom-privileges {
  margin-top: 10rpx;
}
.custom-privilege-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}
.custom-privilege-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.delete-btn {
  width: 60rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
}
.add-custom-btn {
  height: 80rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}
.add-custom-text {
  font-size: 26rpx;
  color: #4A00E0;
}

/* 底部按钮栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}
.save-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}
/* 添加会员等级页面样式结束 */
