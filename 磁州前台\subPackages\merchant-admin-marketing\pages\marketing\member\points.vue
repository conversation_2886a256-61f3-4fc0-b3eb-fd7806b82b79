<template>
  <view class="points-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">会员积分</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 积分概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">积分概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{pointsData.totalPoints}}</view>
          <view class="card-label">总积分发放</view>
          <view class="card-trend" :class="pointsData.pointsTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{pointsData.pointsGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{pointsData.usedPoints}}</view>
          <view class="card-label">已使用积分</view>
          <view class="card-trend" :class="pointsData.usedTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{pointsData.usedGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{pointsData.activeUsers}}</view>
          <view class="card-label">积分活跃用户</view>
          <view class="card-trend" :class="pointsData.usersTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{pointsData.usersGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{pointsData.conversionRate}}%</view>
          <view class="card-label">积分兑换率</view>
          <view class="card-trend" :class="pointsData.conversionTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{pointsData.conversionGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 积分规则 -->
    <view class="rules-section">
      <view class="section-header">
        <text class="section-title">积分规则</text>
        <view class="edit-btn" @click="navigateToRules">
          <text class="btn-text">编辑规则</text>
          <view class="edit-icon"></view>
        </view>
      </view>
      
      <view class="rules-list">
        <view class="rule-item" v-for="(rule, index) in pointsRules" :key="index">
          <view class="rule-icon" :class="rule.type">
            <svg class="svg-icon" viewBox="0 0 24 24" :style="{ fill: rule.iconColor }">
              <path :d="rule.iconPath"></path>
            </svg>
          </view>
          <view class="rule-content">
            <view class="rule-name">{{rule.name}}</view>
            <view class="rule-desc">{{rule.description}}</view>
          </view>
          <view class="rule-value">
            <text class="value-text">{{rule.value}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 积分商品 -->
    <view class="products-section">
      <view class="section-header">
        <text class="section-title">积分商品</text>
        <view class="add-btn" @click="navigateToPointsMall">
          <text class="btn-text">积分商城</text>
          <view class="arrow-icon"></view>
        </view>
      </view>
      
      <scroll-view scroll-x class="products-scroll" show-scrollbar="false">
        <view class="products-container">
          <view class="product-card" v-for="(product, index) in pointsProducts" :key="index" @click="viewProduct(product)">
            <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <view class="product-info">
              <text class="product-name">{{product.name}}</text>
              <view class="product-points">
                <text class="points-value">{{product.points}}</text>
                <text class="points-label">积分</text>
              </view>
              <view class="product-status" :class="product.status === '热门' ? 'hot' : product.status === '新品' ? 'new' : ''">
                {{product.status}}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      
      // 积分数据概览
      pointsData: {
        totalPoints: 256840,
        pointsTrend: 'up',
        pointsGrowth: '12.5%',
        
        usedPoints: 98560,
        usedTrend: 'up',
        usedGrowth: '8.3%',
        
        activeUsers: 1256,
        usersTrend: 'up',
        usersGrowth: '5.7%',
        
        conversionRate: 38.4,
        conversionTrend: 'up',
        conversionGrowth: '2.1%'
      },
      
      // 积分规则
      pointsRules: [
        {
          name: '消费积分',
          description: '会员购物可获得积分奖励',
          type: 'purchase',
          iconPath: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z',
          iconColor: '#FF9500',
          value: '10积分/元'
        },
        {
          name: '每日签到',
          description: '会员每日签到获得积分',
          type: 'checkin',
          iconPath: 'M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z',
          iconColor: '#34C759',
          value: '5积分/次'
        },
        {
          name: '商品评价',
          description: '购买商品后评价获得积分',
          type: 'review',
          iconPath: 'M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z',
          iconColor: '#007AFF',
          value: '20积分/次'
        },
        {
          name: '分享商品',
          description: '分享商品给好友获得积分',
          type: 'share',
          iconPath: 'M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z',
          iconColor: '#FF2D55',
          value: '15积分/次'
        }
      ],
      
      // 积分商品
      pointsProducts: [
        {
          id: 1,
          name: '精美保温杯',
          image: '/static/images/points-product-1.jpg',
          points: 1200,
          status: '热门'
        },
        {
          id: 2,
          name: '无线充电器',
          image: '/static/images/points-product-2.jpg',
          points: 2000,
          status: '新品'
        },
        {
          id: 3,
          name: '精致小台灯',
          image: '/static/images/points-product-3.jpg',
          points: 1500,
          status: '热门'
        },
        {
          id: 4,
          name: '优质雨伞',
          image: '/static/images/points-product-4.jpg',
          points: 800,
          status: ''
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showHelp() {
      uni.showModal({
        title: '会员积分帮助',
        content: '会员积分是会员在消费、签到等行为后获得的奖励，可用于兑换商品或服务。',
        showCancel: false
      });
    },
    
    showDatePicker() {
      // 实现日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    
    navigateToRules() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/points/rules'
      });
    },
    
    navigateToPointsMall() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/points/management'
      });
    },
    
    viewProduct(product) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/points/detail?id=${product.id}`
      });
    }
  }
}
</script>

<style lang="scss">
.points-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9500, #FF5E3A);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 149, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.edit-btn, .add-btn {
  display: flex;
  align-items: center;
  background: #FF9500;
  border-radius: 15px;
  padding: 5px 12px;
  color: white;
}

.btn-text {
  font-size: 13px;
  margin-right: 5px;
}

.edit-icon {
  width: 14px;
  height: 14px;
  position: relative;
}

.edit-icon:before {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  border: 1.5px solid white;
  border-radius: 1px;
  transform: rotate(45deg);
  top: 1px;
  left: 1px;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #fff;
  border-right: 2px solid #fff;
  transform: rotate(45deg);
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #FF9500;
}

.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}

.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}

.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 积分规则样式 */
.rules-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.rules-list {
  margin-top: 10px;
}

.rule-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.svg-icon {
  width: 24px;
  height: 24px;
}

.rule-icon.purchase {
  background: rgba(255, 149, 0, 0.1);
}

.rule-icon.checkin {
  background: rgba(52, 199, 89, 0.1);
}

.rule-icon.review {
  background: rgba(0, 122, 255, 0.1);
}

.rule-icon.share {
  background: rgba(255, 45, 85, 0.1);
}

.rule-content {
  flex: 1;
}

.rule-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
}

.rule-desc {
  font-size: 12px;
  color: #999;
}

.rule-value {
  margin-left: 10px;
}

.value-text {
  font-size: 14px;
  color: #FF9500;
  font-weight: 500;
}

/* 积分商品样式 */
.products-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.products-scroll {
  white-space: nowrap;
  margin: 0 -15px;
  padding: 0 15px;
}

.products-container {
  display: inline-flex;
  padding: 5px 0;
}

.product-card {
  width: 140px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.product-image {
  width: 140px;
  height: 140px;
  background: #f5f5f5;
}

.product-info {
  padding: 10px;
  position: relative;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
  white-space: normal;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-points {
  display: flex;
  align-items: center;
}

.points-value {
  font-size: 16px;
  font-weight: 600;
  color: #FF9500;
}

.points-label {
  font-size: 12px;
  color: #999;
  margin-left: 3px;
}

.product-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  color: white;
}

.product-status.hot {
  background: #FF3B30;
}

.product-status.new {
  background: #34C759;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .stats-card {
    width: 100%;
  }
}
</style> 