/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.distributors-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 搜索筛选区 */
.search-filter {
  background: #FFFFFF;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.search-input-wrap {
  flex: 1;
  height: 72rpx;
  background: #F5F7FA;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-right: 20rpx;
}
.search-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
  margin-right: 10rpx;
}
.search-icon::before {
  content: "";
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid white;
  border-radius: 50%;
  top: 6rpx;
  left: 6rpx;
}
.search-icon::after {
  content: "";
  position: absolute;
  width: 8rpx;
  height: 2rpx;
  background-color: white;
  transform: rotate(45deg);
  bottom: 8rpx;
  right: 6rpx;
}
.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}
.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #909399;
  border-radius: 50%;
  position: relative;
}
.clear-icon::before {
  content: "";
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}
.clear-icon::after {
  content: "";
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.search-btn {
  font-size: 28rpx;
  color: #6B0FBE;
}
.filter-options {
  display: flex;
  justify-content: space-between;
}
.filter-item {
  flex: 1;
  margin: 0 10rpx;
}
.filter-item:first-child {
  margin-left: 0;
}
.filter-item:last-child {
  margin-right: 0;
}
.picker-value {
  height: 72rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #333;
  padding: 0 20rpx;
}
.arrow-icon {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 10rpx;
}

/* 内容区域 */
.content-area {
  padding: 20rpx 30rpx;
}

/* 分销员列表 */
.distributors-list {
  margin-bottom: 30rpx;
}
.distributor-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.distributor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.distributor-info {
  display: flex;
  align-items: center;
}
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}
.info-content {
  flex: 1;
}
.name-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}
.level-tag {
  font-size: 22rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.phone {
  font-size: 24rpx;
  color: #999;
}
.status-tag {
  font-size: 24rpx;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.status-active {
  background-color: #67C23A;
}
.status-disabled {
  background-color: #909399;
}
.status-pending {
  background-color: #E6A23C;
}
.status-rejected {
  background-color: #F56C6C;
}
.distributor-stats {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}
.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.stat-label {
  font-size: 24rpx;
  color: #999;
}
.distributor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.time {
  font-size: 24rpx;
  color: #999;
}
.actions {
  display: flex;
}
.action-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}
.action-btn.detail {
  background-color: #F5F7FA;
  color: #333;
}
.action-btn.approve {
  background-color: #67C23A;
  color: #fff;
}
.action-btn.reject {
  background-color: #F56C6C;
  color: #fff;
}
.action-btn.disable {
  background-color: #909399;
  color: #fff;
}
.action-btn.enable {
  background-color: #409EFF;
  color: #fff;
}
.action-btn.set-level {
  background-color: #6B0FBE;
  color: #fff;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载中 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}
.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #6B0FBE;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;
  padding: 20rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.page-info {
  font-size: 24rpx;
  color: #999;
}
.page-actions {
  display: flex;
}
.page-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
  background-color: #6B0FBE;
  color: #fff;
}
.page-btn.disabled {
  background-color: #f5f5f5;
  color: #999;
}

/* 等级设置弹窗 */
.level-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.close-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}
.close-icon::before,
.close-icon::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32rpx;
  height: 2rpx;
  background-color: #999;
}
.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}
.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}
.modal-distributor {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.modal-distributor .avatar {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 16rpx;
  margin-right: 0;
}
.modal-distributor .name {
  margin-right: 0;
}
.level-list {
  margin-bottom: 30rpx;
}
.level-item-select {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.level-item-select:last-child {
  border-bottom: none;
}
.level-radio {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ddd;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.level-item-select.active .level-radio {
  border-color: #6B0FBE;
}
.radio-inner {
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  background-color: #6B0FBE;
}
.level-item-select .level-name {
  font-size: 28rpx;
  color: #333;
  margin-right: 0;
}
.modal-footer {
  display: flex;
  justify-content: space-between;
}
.cancel-btn,
.submit-btn {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}
.cancel-btn {
  background: #F5F7FA;
  color: #666;
}
.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
}