/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.container {
  min-height: 100vh;
  background-color: #F5F8FC;
  padding-bottom: 140rpx;
  padding-top: calc(var(--status-bar-height) + 90rpx + 10rpx);
  /* 标题栏高度 + 10rpx间距 */
  position: relative;
  overflow-x: hidden;
}

/* 自定义标题栏模块 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #1677FF;
  /* 恢复为实色背景 */
  z-index: 103;
  box-shadow: none;
}

/* 标题栏内容 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  position: relative;
  z-index: 102;
}
.left-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title-area {
  flex: 1;
  text-align: center;
}
.page-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.right-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

/* 搜索框 */
.search-section {
  padding: 20rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 40rpx;
  padding: 0 24rpx;
  height: 80rpx;
}
.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}
.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}
.search-cancel {
  font-size: 28rpx;
  color: #4f8cff;
  margin-left: 20rpx;
  font-weight: 500;
}

/* 分类选项卡 */
.tabs-section {
  position: -webkit-sticky;
  position: sticky;
  top: calc(var(--status-bar-height) + 90rpx);
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  /* 改为白色背景，卡片样式 */
  z-index: 90;
  height: 86rpx;
  display: flex;
  align-items: center;
  border-bottom: none;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  /* 添加轻微阴影，增强卡片效果 */
  width: 100%;
  border-radius: 16rpx;
  /* 添加圆角 */
  margin: 10rpx 30rpx;
  /* 上下间距改为10rpx */
  width: calc(100% - 60rpx);
  /* 减去左右外边距 */
}
.tabs-scroll {
  white-space: nowrap;
  padding: 0;
  height: 86rpx;
  /* 与tabs-section高度一致 */
  border-bottom: none;
  box-shadow: none;
  width: 100%;
}
.tab-item {
  display: inline-block;
  padding: 28rpx 30rpx;
  font-size: 30rpx;
  color: #666666;
  /* 改为深灰色文字 */
  position: relative;
  transition: all 0.3s ease;
  height: 86rpx;
  box-sizing: border-box;
  border-bottom: none;
  text-align: center;
  min-width: 20%;
}
.tab-item.active {
  color: #1677FF;
  /* 激活状态为蓝色 */
  font-weight: 600;
}
.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: #1677FF;
  /* 蓝色指示器 */
  border-radius: 6rpx;
}

/* 群组列表 */
.groups-list {
  height: calc(100vh - 210rpx - var(--status-bar-height) - 90rpx);
  padding: 0 30rpx;
  padding-top: 106rpx;
  /* 设置顶部内边距为分类标签的高度加上一些额外空间 */
  box-sizing: border-box;
  margin-top: 0;
}
.group-card {
  background-color: #ffffff;
  border-radius: 32rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  width: 100%;
  box-sizing: border-box;
  transform: translateZ(0);
  transition: transform 0.2s, box-shadow 0.2s;
  overflow: hidden;
}
.group-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.group-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #f0f4f8;
}
.group-info {
  flex: 1;
  min-width: 0;
}
.group-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #222;
  margin-bottom: 12rpx;
  line-height: 1.3;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}
.group-meta {
  display: flex;
  align-items: center;
}
.group-members {
  font-size: 24rpx;
  color: #999;
  margin-right: 16rpx;
}
.group-type {
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0.05);
  color: #666;
}
.group-type.local {
  background-color: rgba(25, 118, 210, 0.1);
  color: #1976d2;
}
.group-type.longDistance {
  background-color: rgba(156, 39, 176, 0.1);
  color: #9c27b0;
}
.group-type.commute {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}
.group-join {
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  color: #ffffff;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(79, 140, 255, 0.2);
  margin-left: 16rpx;
  transition: transform 0.15s, box-shadow 0.15s;
}
.group-join:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(79, 140, 255, 0.15);
}

/* 移除已加入样式，统一为查看按钮 */
.group-content {
  margin-bottom: 24rpx;
}
.group-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.desc-toggle {
  color: #4f8cff;
  font-size: 24rpx;
  margin-left: 12rpx;
  font-weight: 500;
}
.group-tags-scroll {
  width: 100%;
  height: 60rpx;
}
.group-tags {
  display: inline-flex;
  flex-wrap: nowrap;
  padding: 4rpx 0;
}
.tag {
  font-size: 24rpx;
  color: #4f8cff;
  background: linear-gradient(135deg, #eaf3ff, #d6eaff);
  padding: 8rpx 24rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  white-space: nowrap;
  font-weight: 500;
}
.group-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f4f8;
}
.route-info {
  display: flex;
  align-items: center;
}
.route-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.route-icon image {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}
.route-text {
  font-size: 26rpx;
  color: #999;
}
.group-activity {
  font-size: 24rpx;
  color: #bbb;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f4f8;
  border-top: 4rpx solid #4f8cff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 空状态 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}
.empty-section image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 40rpx;
  opacity: 0.8;
}
.empty-section text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.create-btn {
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  color: #ffffff;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 16rpx rgba(79, 140, 255, 0.25);
  letter-spacing: 2rpx;
}

/* 悬浮按钮 */
.floating-btn {
  position: fixed;
  right: 40rpx;
  bottom: 160rpx;
  width: 110rpx;
  height: 110rpx;
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(79, 140, 255, 0.3);
  z-index: 99;
  transition: transform 0.15s, box-shadow 0.15s;
}
.floating-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(79, 140, 255, 0.2);
}
.floating-btn image {
  width: 50rpx;
  height: 50rpx;
  filter: brightness(0) invert(1);
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 110rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 9999;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  position: relative;
  transition: all 0.2s ease;
}
.tabbar-item:active {
  opacity: 0.7;
}
.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}
.tabbar-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1;
}
.active-text {
  color: #4f8cff;
  font-weight: 600;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}

/* 群二维码弹窗 */
.qrcode-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.qrcode-container {
  width: 650rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: popup 0.3s ease;
}
@keyframes popup {
from {
    transform: scale(0.8);
    opacity: 0;
}
to {
    transform: scale(1);
    opacity: 1;
}
}
.qrcode-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f4f8;
}
.qrcode-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
}
.qrcode-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #999;
}
.group-info-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f4f8;
}
.group-route {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.route-icon {
  margin-right: 10rpx;
  display: flex;
  align-items: center;
}
.route-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}
.group-tags-display {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}
.tag-display {
  padding: 6rpx 16rpx;
  background-color: #E6F2FF;
  color: #0A84FF;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-bottom: 10rpx;
}
.group-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}
.group-members {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}
.members-count {
  color: #0A84FF;
}
.qrcode-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qrcode-image {
  width: 350rpx;
  height: 350rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #f0f4f8;
  border-radius: 12rpx;
}
.qrcode-tips {
  font-size: 28rpx;
  color: #666;
}
.qrcode-footer {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f4f8;
}
.qrcode-save-btn, .qrcode-share-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 40rpx;
}
.qrcode-save-btn {
  background-color: #f5f7fa;
  color: #666;
  margin-right: 20rpx;
}
.qrcode-share-btn {
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  color: #fff;
}

/* 聊天对话框 */
.chat-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.chat-container {
  width: 700rpx;
  height: 900rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: popup 0.3s ease;
  display: flex;
  flex-direction: column;
}
.chat-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f4f8;
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
}
.chat-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #fff;
}
.chat-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #fff;
}
.chat-messages {
  flex: 1;
  padding: 20rpx;
  background-color: #f5f7fa;
  overflow: hidden;
}
.messages-scroll {
  height: 100%;
}
.message-item {
  display: flex;
  margin-bottom: 30rpx;
  align-items: flex-start;
}
.message-mine {
  flex-direction: row-reverse;
}
.message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin: 0 20rpx;
  border: 2rpx solid #f0f4f8;
}
.message-avatar.mine {
  margin-right: 0;
}
.message-content {
  max-width: 70%;
  border-radius: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.message-mine .message-content {
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
}
.message-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}
.message-mine .message-text {
  color: #fff;
}
.location-content {
  width: 400rpx;
}
.location-title {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.message-mine .location-icon {
  filter: brightness(0) invert(1);
}
.location-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.message-mine .location-address {
  color: rgba(255, 255, 255, 0.9);
}
.location-preview {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.map-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.chat-input-area {
  padding: 20rpx;
  border-top: 1rpx solid #f0f4f8;
}
.chat-toolbar {
  display: flex;
  padding-bottom: 20rpx;
}
.toolbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 30rpx;
  opacity: 0.7;
}
.chat-input-box {
  display: flex;
  align-items: center;
}
.chat-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f7fa;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}
.send-btn {
  width: 120rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4f8cff, #6fc3ff);
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 发布类型选择弹窗样式 */
.publish-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}
.publish-card {
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: popup-in 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  transform: scale(0.95);
  opacity: 0.8;
}
@keyframes popup-in {
0% {
    transform: scale(0.95);
    opacity: 0.8;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}
.publish-header {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #F2F2F7;
}
.publish-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background-color: #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-icon {
  font-size: 32rpx;
  color: #999999;
}
.publish-options {
  padding: 32rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
}
.publish-option {
  width: calc(50% - 16rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.publish-option:active {
  transform: scale(0.95);
  background-color: #F5F8FC;
}
.option-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}
.option-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.12);
  border: 2rpx solid rgba(255, 255, 255, 0.7);
  position: relative;
  overflow: hidden;
}
.option-icon-wrapper::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.3), transparent 70%);
  z-index: 1;
}
.option-icon {
  width: 60rpx;
  height: 60rpx;
  filter: brightness(0) invert(1);
  position: relative;
  z-index: 2;
}
.people-car {
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
}
.car-people {
  background: linear-gradient(135deg, #FF2D55, #FF9500);
}
.goods-car {
  background: linear-gradient(135deg, #30D158, #34C759);
}
.car-goods {
  background: linear-gradient(135deg, #FF9F0A, #FFD60A);
}