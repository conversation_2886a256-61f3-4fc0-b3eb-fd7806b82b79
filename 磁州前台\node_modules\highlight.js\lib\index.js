var hljs = require('./core');

hljs.registerLanguage('1c', require('./languages/1c'));
hljs.registerLanguage('abnf', require('./languages/abnf'));
hljs.registerLanguage('accesslog', require('./languages/accesslog'));
hljs.registerLanguage('actionscript', require('./languages/actionscript'));
hljs.registerLanguage('ada', require('./languages/ada'));
hljs.registerLanguage('angelscript', require('./languages/angelscript'));
hljs.registerLanguage('apache', require('./languages/apache'));
hljs.registerLanguage('applescript', require('./languages/applescript'));
hljs.registerLanguage('arcade', require('./languages/arcade'));
hljs.registerLanguage('arduino', require('./languages/arduino'));
hljs.registerLanguage('armasm', require('./languages/armasm'));
hljs.registerLanguage('xml', require('./languages/xml'));
hljs.registerLanguage('asciidoc', require('./languages/asciidoc'));
hljs.registerLanguage('aspectj', require('./languages/aspectj'));
hljs.registerLanguage('autohotkey', require('./languages/autohotkey'));
hljs.registerLanguage('autoit', require('./languages/autoit'));
hljs.registerLanguage('avrasm', require('./languages/avrasm'));
hljs.registerLanguage('awk', require('./languages/awk'));
hljs.registerLanguage('axapta', require('./languages/axapta'));
hljs.registerLanguage('bash', require('./languages/bash'));
hljs.registerLanguage('basic', require('./languages/basic'));
hljs.registerLanguage('bnf', require('./languages/bnf'));
hljs.registerLanguage('brainfuck', require('./languages/brainfuck'));
hljs.registerLanguage('c-like', require('./languages/c-like'));
hljs.registerLanguage('c', require('./languages/c'));
hljs.registerLanguage('cal', require('./languages/cal'));
hljs.registerLanguage('capnproto', require('./languages/capnproto'));
hljs.registerLanguage('ceylon', require('./languages/ceylon'));
hljs.registerLanguage('clean', require('./languages/clean'));
hljs.registerLanguage('clojure', require('./languages/clojure'));
hljs.registerLanguage('clojure-repl', require('./languages/clojure-repl'));
hljs.registerLanguage('cmake', require('./languages/cmake'));
hljs.registerLanguage('coffeescript', require('./languages/coffeescript'));
hljs.registerLanguage('coq', require('./languages/coq'));
hljs.registerLanguage('cos', require('./languages/cos'));
hljs.registerLanguage('cpp', require('./languages/cpp'));
hljs.registerLanguage('crmsh', require('./languages/crmsh'));
hljs.registerLanguage('crystal', require('./languages/crystal'));
hljs.registerLanguage('csharp', require('./languages/csharp'));
hljs.registerLanguage('csp', require('./languages/csp'));
hljs.registerLanguage('css', require('./languages/css'));
hljs.registerLanguage('d', require('./languages/d'));
hljs.registerLanguage('markdown', require('./languages/markdown'));
hljs.registerLanguage('dart', require('./languages/dart'));
hljs.registerLanguage('delphi', require('./languages/delphi'));
hljs.registerLanguage('diff', require('./languages/diff'));
hljs.registerLanguage('django', require('./languages/django'));
hljs.registerLanguage('dns', require('./languages/dns'));
hljs.registerLanguage('dockerfile', require('./languages/dockerfile'));
hljs.registerLanguage('dos', require('./languages/dos'));
hljs.registerLanguage('dsconfig', require('./languages/dsconfig'));
hljs.registerLanguage('dts', require('./languages/dts'));
hljs.registerLanguage('dust', require('./languages/dust'));
hljs.registerLanguage('ebnf', require('./languages/ebnf'));
hljs.registerLanguage('elixir', require('./languages/elixir'));
hljs.registerLanguage('elm', require('./languages/elm'));
hljs.registerLanguage('ruby', require('./languages/ruby'));
hljs.registerLanguage('erb', require('./languages/erb'));
hljs.registerLanguage('erlang-repl', require('./languages/erlang-repl'));
hljs.registerLanguage('erlang', require('./languages/erlang'));
hljs.registerLanguage('excel', require('./languages/excel'));
hljs.registerLanguage('fix', require('./languages/fix'));
hljs.registerLanguage('flix', require('./languages/flix'));
hljs.registerLanguage('fortran', require('./languages/fortran'));
hljs.registerLanguage('fsharp', require('./languages/fsharp'));
hljs.registerLanguage('gams', require('./languages/gams'));
hljs.registerLanguage('gauss', require('./languages/gauss'));
hljs.registerLanguage('gcode', require('./languages/gcode'));
hljs.registerLanguage('gherkin', require('./languages/gherkin'));
hljs.registerLanguage('glsl', require('./languages/glsl'));
hljs.registerLanguage('gml', require('./languages/gml'));
hljs.registerLanguage('go', require('./languages/go'));
hljs.registerLanguage('golo', require('./languages/golo'));
hljs.registerLanguage('gradle', require('./languages/gradle'));
hljs.registerLanguage('groovy', require('./languages/groovy'));
hljs.registerLanguage('haml', require('./languages/haml'));
hljs.registerLanguage('handlebars', require('./languages/handlebars'));
hljs.registerLanguage('haskell', require('./languages/haskell'));
hljs.registerLanguage('haxe', require('./languages/haxe'));
hljs.registerLanguage('hsp', require('./languages/hsp'));
hljs.registerLanguage('htmlbars', require('./languages/htmlbars'));
hljs.registerLanguage('http', require('./languages/http'));
hljs.registerLanguage('hy', require('./languages/hy'));
hljs.registerLanguage('inform7', require('./languages/inform7'));
hljs.registerLanguage('ini', require('./languages/ini'));
hljs.registerLanguage('irpf90', require('./languages/irpf90'));
hljs.registerLanguage('isbl', require('./languages/isbl'));
hljs.registerLanguage('java', require('./languages/java'));
hljs.registerLanguage('javascript', require('./languages/javascript'));
hljs.registerLanguage('jboss-cli', require('./languages/jboss-cli'));
hljs.registerLanguage('json', require('./languages/json'));
hljs.registerLanguage('julia', require('./languages/julia'));
hljs.registerLanguage('julia-repl', require('./languages/julia-repl'));
hljs.registerLanguage('kotlin', require('./languages/kotlin'));
hljs.registerLanguage('lasso', require('./languages/lasso'));
hljs.registerLanguage('latex', require('./languages/latex'));
hljs.registerLanguage('ldif', require('./languages/ldif'));
hljs.registerLanguage('leaf', require('./languages/leaf'));
hljs.registerLanguage('less', require('./languages/less'));
hljs.registerLanguage('lisp', require('./languages/lisp'));
hljs.registerLanguage('livecodeserver', require('./languages/livecodeserver'));
hljs.registerLanguage('livescript', require('./languages/livescript'));
hljs.registerLanguage('llvm', require('./languages/llvm'));
hljs.registerLanguage('lsl', require('./languages/lsl'));
hljs.registerLanguage('lua', require('./languages/lua'));
hljs.registerLanguage('makefile', require('./languages/makefile'));
hljs.registerLanguage('mathematica', require('./languages/mathematica'));
hljs.registerLanguage('matlab', require('./languages/matlab'));
hljs.registerLanguage('maxima', require('./languages/maxima'));
hljs.registerLanguage('mel', require('./languages/mel'));
hljs.registerLanguage('mercury', require('./languages/mercury'));
hljs.registerLanguage('mipsasm', require('./languages/mipsasm'));
hljs.registerLanguage('mizar', require('./languages/mizar'));
hljs.registerLanguage('perl', require('./languages/perl'));
hljs.registerLanguage('mojolicious', require('./languages/mojolicious'));
hljs.registerLanguage('monkey', require('./languages/monkey'));
hljs.registerLanguage('moonscript', require('./languages/moonscript'));
hljs.registerLanguage('n1ql', require('./languages/n1ql'));
hljs.registerLanguage('nginx', require('./languages/nginx'));
hljs.registerLanguage('nim', require('./languages/nim'));
hljs.registerLanguage('nix', require('./languages/nix'));
hljs.registerLanguage('node-repl', require('./languages/node-repl'));
hljs.registerLanguage('nsis', require('./languages/nsis'));
hljs.registerLanguage('objectivec', require('./languages/objectivec'));
hljs.registerLanguage('ocaml', require('./languages/ocaml'));
hljs.registerLanguage('openscad', require('./languages/openscad'));
hljs.registerLanguage('oxygene', require('./languages/oxygene'));
hljs.registerLanguage('parser3', require('./languages/parser3'));
hljs.registerLanguage('pf', require('./languages/pf'));
hljs.registerLanguage('pgsql', require('./languages/pgsql'));
hljs.registerLanguage('php', require('./languages/php'));
hljs.registerLanguage('php-template', require('./languages/php-template'));
hljs.registerLanguage('plaintext', require('./languages/plaintext'));
hljs.registerLanguage('pony', require('./languages/pony'));
hljs.registerLanguage('powershell', require('./languages/powershell'));
hljs.registerLanguage('processing', require('./languages/processing'));
hljs.registerLanguage('profile', require('./languages/profile'));
hljs.registerLanguage('prolog', require('./languages/prolog'));
hljs.registerLanguage('properties', require('./languages/properties'));
hljs.registerLanguage('protobuf', require('./languages/protobuf'));
hljs.registerLanguage('puppet', require('./languages/puppet'));
hljs.registerLanguage('purebasic', require('./languages/purebasic'));
hljs.registerLanguage('python', require('./languages/python'));
hljs.registerLanguage('python-repl', require('./languages/python-repl'));
hljs.registerLanguage('q', require('./languages/q'));
hljs.registerLanguage('qml', require('./languages/qml'));
hljs.registerLanguage('r', require('./languages/r'));
hljs.registerLanguage('reasonml', require('./languages/reasonml'));
hljs.registerLanguage('rib', require('./languages/rib'));
hljs.registerLanguage('roboconf', require('./languages/roboconf'));
hljs.registerLanguage('routeros', require('./languages/routeros'));
hljs.registerLanguage('rsl', require('./languages/rsl'));
hljs.registerLanguage('ruleslanguage', require('./languages/ruleslanguage'));
hljs.registerLanguage('rust', require('./languages/rust'));
hljs.registerLanguage('sas', require('./languages/sas'));
hljs.registerLanguage('scala', require('./languages/scala'));
hljs.registerLanguage('scheme', require('./languages/scheme'));
hljs.registerLanguage('scilab', require('./languages/scilab'));
hljs.registerLanguage('scss', require('./languages/scss'));
hljs.registerLanguage('shell', require('./languages/shell'));
hljs.registerLanguage('smali', require('./languages/smali'));
hljs.registerLanguage('smalltalk', require('./languages/smalltalk'));
hljs.registerLanguage('sml', require('./languages/sml'));
hljs.registerLanguage('sqf', require('./languages/sqf'));
hljs.registerLanguage('sql_more', require('./languages/sql_more'));
hljs.registerLanguage('sql', require('./languages/sql'));
hljs.registerLanguage('stan', require('./languages/stan'));
hljs.registerLanguage('stata', require('./languages/stata'));
hljs.registerLanguage('step21', require('./languages/step21'));
hljs.registerLanguage('stylus', require('./languages/stylus'));
hljs.registerLanguage('subunit', require('./languages/subunit'));
hljs.registerLanguage('swift', require('./languages/swift'));
hljs.registerLanguage('taggerscript', require('./languages/taggerscript'));
hljs.registerLanguage('yaml', require('./languages/yaml'));
hljs.registerLanguage('tap', require('./languages/tap'));
hljs.registerLanguage('tcl', require('./languages/tcl'));
hljs.registerLanguage('thrift', require('./languages/thrift'));
hljs.registerLanguage('tp', require('./languages/tp'));
hljs.registerLanguage('twig', require('./languages/twig'));
hljs.registerLanguage('typescript', require('./languages/typescript'));
hljs.registerLanguage('vala', require('./languages/vala'));
hljs.registerLanguage('vbnet', require('./languages/vbnet'));
hljs.registerLanguage('vbscript', require('./languages/vbscript'));
hljs.registerLanguage('vbscript-html', require('./languages/vbscript-html'));
hljs.registerLanguage('verilog', require('./languages/verilog'));
hljs.registerLanguage('vhdl', require('./languages/vhdl'));
hljs.registerLanguage('vim', require('./languages/vim'));
hljs.registerLanguage('x86asm', require('./languages/x86asm'));
hljs.registerLanguage('xl', require('./languages/xl'));
hljs.registerLanguage('xquery', require('./languages/xquery'));
hljs.registerLanguage('zephir', require('./languages/zephir'));

module.exports = hljs;