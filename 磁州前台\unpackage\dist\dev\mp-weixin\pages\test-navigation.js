"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      result: "未执行测试"
    };
  },
  methods: {
    testNavigate(url) {
      this.result = `正在跳转到: ${url}`;
      common_vendor.index.navigateTo({
        url,
        success: () => {
          this.result = `跳转成功: ${url}`;
        },
        fail: (err) => {
          this.result = `跳转失败: ${url}, 错误: ${JSON.stringify(err)}`;
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o(($event) => $options.testNavigate("/pages/publish/info-detail")),
    b: common_vendor.o(($event) => $options.testNavigate("/pages/publish/publish")),
    c: common_vendor.o(($event) => $options.testNavigate("/pages/publish/detail")),
    d: common_vendor.t($data.result)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/test-navigation.js.map
