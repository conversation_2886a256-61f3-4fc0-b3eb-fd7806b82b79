{"version": 3, "file": "activities.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/activities.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcYWN0aXZpdGllcy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"activities-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">会员活动</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 活动概览 -->\r\n    <view class=\"overview-section\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"section-title\">活动概览</text>\r\n        <view class=\"date-picker\" @click=\"showDatePicker\">\r\n          <text class=\"date-text\">{{dateRange}}</text>\r\n          <view class=\"date-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-cards\">\r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{activityData.totalActivities}}</view>\r\n          <view class=\"card-label\">总活动数</view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{activityData.runningActivities}}</view>\r\n          <view class=\"card-label\">进行中活动</view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{activityData.participationCount}}</view>\r\n          <view class=\"card-label\">参与人次</view>\r\n          <view class=\"card-trend\" :class=\"activityData.participationTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{activityData.participationGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">¥{{activityData.revenue}}</view>\r\n          <view class=\"card-label\">活动收入</view>\r\n          <view class=\"card-trend\" :class=\"activityData.revenueTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{activityData.revenueGrowth}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 活动分类标签 -->\r\n    <view class=\"tabs-section\">\r\n      <scroll-view scroll-x class=\"tabs-scroll\" show-scrollbar=\"false\">\r\n        <view class=\"tabs\">\r\n          <view \r\n            class=\"tab-item\" \r\n            v-for=\"(tab, index) in tabs\" \r\n            :key=\"index\"\r\n            :class=\"{ active: currentTab === index }\"\r\n            @click=\"switchTab(index)\"\r\n          >\r\n            {{tab}}\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 活动列表 -->\r\n    <view class=\"activities-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">{{tabs[currentTab]}}</text>\r\n        <view class=\"add-btn\" @click=\"createActivity\">\r\n          <text class=\"btn-text\">添加活动</text>\r\n          <view class=\"plus-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"activities-list\">\r\n        <view class=\"activity-item\" v-for=\"(activity, index) in filteredActivities\" :key=\"index\" @click=\"viewActivityDetail(activity)\">\r\n          <image class=\"activity-image\" :src=\"activity.image\" mode=\"aspectFill\"></image>\r\n          <view class=\"activity-overlay\" :class=\"activity.status\"></view>\r\n          <view class=\"activity-status-badge\" :class=\"activity.status\">{{activity.statusText}}</view>\r\n          <view class=\"activity-content\">\r\n            <view class=\"activity-name\">{{activity.name}}</view>\r\n            <view class=\"activity-period\">{{activity.period}}</view>\r\n            <view class=\"activity-meta\">\r\n              <view class=\"meta-item\">\r\n                <svg class=\"svg-icon\" viewBox=\"0 0 24 24\" fill=\"#43E97B\">\r\n                  <path d=\"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z\"/>\r\n                </svg>\r\n                <text>{{activity.participantCount}}人参与</text>\r\n              </view>\r\n              <view class=\"meta-item\">\r\n                <svg class=\"svg-icon\" viewBox=\"0 0 24 24\" fill=\"#38F9D7\">\r\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z\"/>\r\n                </svg>\r\n                <text>{{activity.memberLevelText}}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"activity-actions\">\r\n            <view class=\"action-btn stats\" @click.stop=\"viewStats(activity)\">\r\n              <svg class=\"svg-icon\" viewBox=\"0 0 24 24\" fill=\"#43E97B\">\r\n                <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\r\n              </svg>\r\n              <text>统计</text>\r\n            </view>\r\n            <view class=\"action-btn share\" @click.stop=\"shareActivity(activity)\">\r\n              <svg class=\"svg-icon\" viewBox=\"0 0 24 24\" fill=\"#38F9D7\">\r\n                <path d=\"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z\"/>\r\n              </svg>\r\n              <text>分享</text>\r\n            </view>\r\n            <view class=\"action-btn toggle\">\r\n              <switch :checked=\"activity.enabled\" @change=\"(e) => toggleActivity(activity, e)\" color=\"#43E97B\" />\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 浮动操作按钮 -->\r\n    <view class=\"floating-action-button\" @click=\"createActivity\">\r\n      <view class=\"fab-icon\">+</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dateRange: '2023-04-01 ~ 2023-04-30',\r\n      currentTab: 0,\r\n      \r\n      // 活动数据概览\r\n      activityData: {\r\n        totalActivities: 8,\r\n        runningActivities: 5,\r\n        participationCount: 1256,\r\n        participationTrend: 'up',\r\n        participationGrowth: '18.5%',\r\n        revenue: '28560.50',\r\n        revenueTrend: 'up',\r\n        revenueGrowth: '12.3%'\r\n      },\r\n      \r\n      // 活动分类标签\r\n      tabs: ['全部活动', '会员专享', '积分活动', '抽奖活动', '限时活动'],\r\n      \r\n      // 活动列表\r\n      activities: [\r\n        {\r\n          id: 1,\r\n          name: '会员专享品鉴会',\r\n          period: '2023-04-15 ~ 2023-04-20',\r\n          image: '/static/images/activity-1.jpg',\r\n          participantCount: 128,\r\n          memberLevel: ['silver', 'gold', 'diamond'],\r\n          memberLevelText: '银卡及以上',\r\n          category: '会员专享',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '积分翻倍周',\r\n          period: '2023-04-10 ~ 2023-04-17',\r\n          image: '/static/images/activity-2.jpg',\r\n          participantCount: 356,\r\n          memberLevel: ['all'],\r\n          memberLevelText: '全部会员',\r\n          category: '积分活动',\r\n          status: 'active',\r\n          statusText: '进行中',\r\n          enabled: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '幸运大抽奖',\r\n          period: '2023-04-20 ~ 2023-04-30',\r\n          image: '/static/images/activity-3.jpg',\r\n          participantCount: 215,\r\n          memberLevel: ['all'],\r\n          memberLevelText: '全部会员',\r\n          category: '抽奖活动',\r\n          status: 'upcoming',\r\n          statusText: '即将开始',\r\n          enabled: true\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '限时秒杀专场',\r\n          period: '2023-04-05 ~ 2023-04-08',\r\n          image: '/static/images/activity-4.jpg',\r\n          participantCount: 432,\r\n          memberLevel: ['all'],\r\n          memberLevelText: '全部会员',\r\n          category: '限时活动',\r\n          status: 'ended',\r\n          statusText: '已结束',\r\n          enabled: false\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '钻石会员专享日',\r\n          period: '2023-04-28',\r\n          image: '/static/images/activity-5.jpg',\r\n          participantCount: 68,\r\n          memberLevel: ['diamond'],\r\n          memberLevelText: '钻石会员',\r\n          category: '会员专享',\r\n          status: 'upcoming',\r\n          statusText: '即将开始',\r\n          enabled: true\r\n        },\r\n        {\r\n          id: 6,\r\n          name: '积分兑换特惠',\r\n          period: '2023-04-01 ~ 2023-04-07',\r\n          image: '/static/images/activity-6.jpg',\r\n          participantCount: 187,\r\n          memberLevel: ['all'],\r\n          memberLevelText: '全部会员',\r\n          category: '积分活动',\r\n          status: 'ended',\r\n          statusText: '已结束',\r\n          enabled: false\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredActivities() {\r\n      if (this.currentTab === 0) {\r\n        return this.activities;\r\n      } else {\r\n        const category = this.tabs[this.currentTab];\r\n        return this.activities.filter(activity => activity.category === category);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    showHelp() {\r\n      uni.showModal({\r\n        title: '会员活动帮助',\r\n        content: '会员活动是指针对会员开展的各类营销活动，包括会员专享活动、积分活动、抽奖活动等，可以提高会员活跃度和忠诚度。',\r\n        showCancel: false\r\n      });\r\n    },\r\n    \r\n    showDatePicker() {\r\n      // 实现日期选择器\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    switchTab(index) {\r\n      this.currentTab = index;\r\n    },\r\n    \r\n    createActivity() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/member/create-activity'\r\n      });\r\n    },\r\n    \r\n    viewActivityDetail(activity) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/activity-detail?id=${activity.id}`\r\n      });\r\n    },\r\n    \r\n    viewStats(activity) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/activity-stats?id=${activity.id}`\r\n      });\r\n    },\r\n    \r\n    shareActivity(activity) {\r\n      uni.showShareMenu({\r\n        withShareTicket: true,\r\n        menus: ['shareAppMessage', 'shareTimeline']\r\n      });\r\n    },\r\n    \r\n    toggleActivity(activity, e) {\r\n      // 更新活动状态\r\n      const index = this.activities.findIndex(item => item.id === activity.id);\r\n      if (index !== -1) {\r\n        this.activities[index].enabled = e.detail.value;\r\n      }\r\n      \r\n      uni.showToast({\r\n        title: e.detail.value ? `${activity.name}已启用` : `${activity.name}已禁用`,\r\n        icon: 'none'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.activities-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #43E97B, #38F9D7);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(67, 233, 123, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 通用部分样式 */\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #43E97B;\r\n  border-radius: 15px;\r\n  padding: 5px 12px;\r\n  color: white;\r\n}\r\n\r\n.btn-text {\r\n  font-size: 13px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.plus-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  position: relative;\r\n}\r\n\r\n.plus-icon:before,\r\n.plus-icon:after {\r\n  content: '';\r\n  position: absolute;\r\n  background: white;\r\n}\r\n\r\n.plus-icon:before {\r\n  width: 12px;\r\n  height: 2px;\r\n  top: 5px;\r\n  left: 0;\r\n}\r\n\r\n.plus-icon:after {\r\n  height: 12px;\r\n  width: 2px;\r\n  left: 5px;\r\n  top: 0;\r\n}\r\n\r\n/* 概览部分样式 */\r\n.overview-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.date-icon {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #666;\r\n  border-right: 2px solid #666;\r\n  transform: rotate(135deg);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.stats-card {\r\n  width: 50%;\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n}\r\n\r\n.card-value {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  background: #F8FAFC;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  border-left: 3px solid #43E97B;\r\n}\r\n\r\n.card-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 25px;\r\n}\r\n\r\n.card-trend {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  right: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-trend.up {\r\n  color: #34C759;\r\n}\r\n\r\n.card-trend.down {\r\n  color: #FF3B30;\r\n}\r\n\r\n.trend-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  margin-right: 3px;\r\n}\r\n\r\n.card-trend.up .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-bottom: 6px solid #34C759;\r\n}\r\n\r\n.card-trend.down .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 6px solid #FF3B30;\r\n}\r\n\r\n/* 标签页样式 */\r\n.tabs-section {\r\n  margin: 15px 15px 0;\r\n}\r\n\r\n.tabs-scroll {\r\n  white-space: nowrap;\r\n}\r\n\r\n.tabs {\r\n  display: inline-flex;\r\n  padding: 5px 0;\r\n}\r\n\r\n.tab-item {\r\n  padding: 8px 16px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-right: 10px;\r\n  background: #fff;\r\n  border-radius: 20px;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s;\r\n}\r\n\r\n.tab-item.active {\r\n  background: #43E97B;\r\n  color: white;\r\n  box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);\r\n}\r\n\r\n/* 活动列表样式 */\r\n.activities-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.activities-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.activity-item {\r\n  margin-bottom: 20px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.activity-image {\r\n  width: 100%;\r\n  height: 150px;\r\n  position: relative;\r\n}\r\n\r\n.activity-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 150px;\r\n  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));\r\n}\r\n\r\n.activity-overlay.ended {\r\n  background: linear-gradient(to bottom, rgba(0,0,0,0.4), rgba(0,0,0,0.7));\r\n}\r\n\r\n.activity-status-badge {\r\n  position: absolute;\r\n  top: 15px;\r\n  right: 15px;\r\n  padding: 5px 10px;\r\n  border-radius: 15px;\r\n  font-size: 12px;\r\n  color: white;\r\n  font-weight: 500;\r\n}\r\n\r\n.activity-status-badge.active {\r\n  background: #43E97B;\r\n}\r\n\r\n.activity-status-badge.upcoming {\r\n  background: #007AFF;\r\n}\r\n\r\n.activity-status-badge.ended {\r\n  background: #8E8E93;\r\n}\r\n\r\n.activity-content {\r\n  padding: 15px;\r\n  background: white;\r\n}\r\n\r\n.activity-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.activity-period {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.activity-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.meta-item text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-left: 5px;\r\n}\r\n\r\n.svg-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.activity-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 10px 15px;\r\n  background: #F8FAFC;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-top: 3px;\r\n}\r\n\r\n.action-btn.toggle {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 浮动操作按钮 */\r\n.floating-action-button {\r\n  position: fixed;\r\n  bottom: 30px;\r\n  right: 30px;\r\n  width: 56px;\r\n  height: 56px;\r\n  border-radius: 28px;\r\n  background: linear-gradient(135deg, #43E97B, #38F9D7);\r\n  box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 100;\r\n}\r\n\r\n.fab-icon {\r\n  font-size: 28px;\r\n  color: #fff;\r\n  font-weight: 300;\r\n  line-height: 1;\r\n  margin-top: -2px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media screen and (max-width: 375px) {\r\n  .stats-card {\r\n    width: 100%;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/activities.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAqIA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,YAAY;AAAA;AAAA,MAGZ,cAAc;AAAA,QACZ,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,SAAS;AAAA,QACT,cAAc;AAAA,QACd,eAAe;AAAA,MAChB;AAAA;AAAA,MAGD,MAAM,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA;AAAA,MAG7C,YAAY;AAAA,QACV;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,kBAAkB;AAAA,UAClB,aAAa,CAAC,UAAU,QAAQ,SAAS;AAAA,UACzC,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,kBAAkB;AAAA,UAClB,aAAa,CAAC,KAAK;AAAA,UACnB,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,kBAAkB;AAAA,UAClB,aAAa,CAAC,KAAK;AAAA,UACnB,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,kBAAkB;AAAA,UAClB,aAAa,CAAC,KAAK;AAAA,UACnB,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,kBAAkB;AAAA,UAClB,aAAa,CAAC,SAAS;AAAA,UACvB,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,kBAAkB;AAAA,UAClB,aAAa,CAAC,KAAK;AAAA,UACnB,iBAAiB;AAAA,UACjB,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,qBAAqB;AACnB,UAAI,KAAK,eAAe,GAAG;AACzB,eAAO,KAAK;AAAA,aACP;AACL,cAAM,WAAW,KAAK,KAAK,KAAK,UAAU;AAC1C,eAAO,KAAK,WAAW,OAAO,cAAY,SAAS,aAAa,QAAQ;AAAA,MAC1E;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,mBAAmB,UAAU;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mFAAmF,SAAS,EAAE;AAAA,MACrG,CAAC;AAAA,IACF;AAAA,IAED,UAAU,UAAU;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,kFAAkF,SAAS,EAAE;AAAA,MACpG,CAAC;AAAA,IACF;AAAA,IAED,cAAc,UAAU;AACtBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,MAC5C,CAAC;AAAA,IACF;AAAA,IAED,eAAe,UAAU,GAAG;AAE1B,YAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,SAAS,EAAE;AACvE,UAAI,UAAU,IAAI;AAChB,aAAK,WAAW,KAAK,EAAE,UAAU,EAAE,OAAO;AAAA,MAC5C;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,EAAE,OAAO,QAAQ,GAAG,SAAS,IAAI,QAAQ,GAAG,SAAS,IAAI;AAAA,QAChE,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrTA,GAAG,WAAW,eAAe;"}