# 磁州生活网后台管理系统 - 后端服务

基于前台项目功能需求开发的企业级微服务后台管理系统，采用Spring Boot + Spring Cloud + Nacos架构。

## 🚀 项目概述

本项目是磁州生活网的后台管理系统，用于管理前台小程序的各项业务功能，包括：

- **用户管理**：C端用户、分销员、合伙人管理
- **商家管理**：商家入驻、店铺管理、商品管理
- **内容管理**：信息发布、内容审核、分类管理
- **拼车管理**：拼车信息、司机认证、订单管理
- **营销管理**：分销系统、活动管理、优惠券管理
- **订单管理**：订单处理、支付管理、售后服务
- **财务管理**：钱包管理、提现审核、佣金结算
- **数据分析**：用户分析、营收分析、运营报表

## 🏗️ 技术架构

### 核心技术栈
- **Java 17** - 编程语言
- **Spring Boot 3.2.1** - 应用框架
- **Spring Cloud 2023.0.0** - 微服务框架
- **Spring Cloud Alibaba** - 阿里云微服务生态
- **Nacos** - 服务注册发现 + 配置中心
- **Spring Cloud Gateway** - API网关
- **MyBatis-Plus** - ORM框架
- **MySQL 8.0** - 关系型数据库
- **Redis** - 缓存数据库
- **RabbitMQ** - 消息队列

### 开发工具
- **Maven** - 项目构建
- **Docker** - 容器化部署
- **Knife4j** - API文档
- **Lombok** - 代码简化
- **MapStruct** - 对象映射
- **Hutool** - 工具类库

## 📁 项目结构

```
cizhou-admin/backend/
├── cizhou-common/              # 公共模块
│   ├── core/                   # 核心组件
│   └── security/               # 安全组件
├── cizhou-gateway/             # API网关
├── cizhou-auth/                # 认证服务
├── cizhou-user-service/        # 用户管理服务
├── cizhou-merchant-service/    # 商家管理服务
├── cizhou-content-service/     # 内容管理服务
├── cizhou-carpool-service/     # 拼车管理服务
├── cizhou-marketing-service/   # 营销管理服务
├── cizhou-order-service/       # 订单管理服务
├── cizhou-finance-service/     # 财务管理服务
├── cizhou-data-service/        # 数据分析服务
├── cizhou-notification-service/# 通知服务
└── cizhou-system-service/      # 系统管理服务
```

## 🚀 快速开始

### 环境要求
- JDK 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+
- Nacos 2.3.0+

### 本地开发

1. **克隆项目**
```bash
git clone https://github.com/cizhou-team/cizhou-admin.git
cd cizhou-admin/backend
```

2. **启动基础服务**
```bash
# 启动MySQL
docker run -d --name mysql -p 3306:3306 -e MYSQL_ROOT_PASSWORD=cizhou123456 mysql:8.0

# 启动Redis
docker run -d --name redis -p 6379:6379 redis:7.0-alpine

# 启动Nacos
docker run -d --name nacos -p 8848:8848 nacos/nacos-server:v2.3.0
```

3. **编译项目**
```bash
mvn clean install -DskipTests
```

4. **启动服务**
```bash
# 启动网关
java -jar cizhou-gateway/target/cizhou-gateway-1.0.0.jar

# 启动认证服务
java -jar cizhou-auth/target/cizhou-auth-1.0.0.jar

# 启动其他服务...
```

### Docker部署

```bash
# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d
```

## 📋 开发规范

### 代码规范
- 使用阿里巴巴Java开发手册
- 统一使用UTF-8编码
- 类名使用PascalCase
- 方法名和变量名使用camelCase
- 常量使用UPPER_SNAKE_CASE

### 提交规范
使用Conventional Commits规范：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### API设计规范
- RESTful API设计
- 统一响应格式
- 统一错误码
- 完整的API文档

## 🔧 配置说明

### 环境配置
- `application-dev.yml` - 开发环境
- `application-test.yml` - 测试环境
- `application-prod.yml` - 生产环境

### 数据库配置
```yaml
spring:
  datasource:
    url: ************************************************************************************************************
    username: root
    password: cizhou123456
```

## 🧪 测试

```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify

# 生成测试报告
mvn jacoco:report
```

## 📦 部署

### 生产环境部署
1. 构建生产镜像
2. 配置Kubernetes部署文件
3. 配置CI/CD流水线
4. 监控和日志配置

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址：https://github.com/cizhou-team/cizhou-admin
- 问题反馈：https://github.com/cizhou-team/cizhou-admin/issues
- 邮箱：<EMAIL>

---

© 2024 磁州生活网团队. All rights reserved.
