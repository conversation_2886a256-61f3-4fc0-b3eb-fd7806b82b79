<template>
  <view class="beautiful-cizhou">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">最美磁州</view>
      <view class="navbar-right">
        <view class="share-icon" @click="share">
          <image src="/static/images/tabbar/分享.png" class="icon-image"></image>
        </view>
      </view>
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view class="content-scroll" scroll-y refresher-enabled 
      :refresher-triggered="refreshing" @refresherrefresh="onRefresh"
      :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      
      <!-- 顶部视觉冲击区 -->
      <view class="hero-section">
        <swiper class="hero-swiper" circular autoplay interval="4000" duration="600" indicator-dots indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#FFFFFF">
          <swiper-item v-for="(item, index) in heroImages" :key="index">
            <image :src="item.image" mode="aspectFill" class="hero-image"></image>
            <view class="hero-caption">
              <text class="hero-title">{{item.title}}</text>
              <text class="hero-desc">{{item.desc}}</text>
            </view>
          </swiper-item>
        </swiper>
      </view>
      
      <!-- 分类导航 -->
      <view class="category-tabs">
        <scroll-view scroll-x class="tab-scroll" show-scrollbar="false">
          <view 
            class="tab-item" 
            v-for="(tab, index) in tabs" 
            :key="index"
            :class="{ active: currentTab === index }"
            @click="switchTab(index)"
          >
            <text class="tab-text">{{ tab.name }}</text>
          </view>
        </scroll-view>
      </view>
      
      <!-- 磁州特色区 -->
      <view class="feature-section">
        <view class="section-header">
          <view class="section-title">
            <text class="title-text">磁州特色</text>
          </view>
          <view class="section-more" @click="navigateTo('/subPackages/activity/pages/features')">
            <text class="more-text">更多</text>
            <text class="more-icon">></text>
          </view>
        </view>
        
        <view class="feature-cards">
          <view class="feature-card" v-for="(feature, index) in features" :key="index" @click="viewFeature(feature)">
            <image :src="feature.image" mode="aspectFill" class="feature-image"></image>
            <view class="feature-info">
              <text class="feature-name">{{ feature.name }}</text>
              <text class="feature-desc">{{ feature.desc }}</text>
              <view class="feature-tag" v-if="feature.tag">{{ feature.tag }}</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 景点推荐 -->
      <view class="attractions-section">
        <view class="section-header">
          <view class="section-title">
            <text class="title-text">景点推荐</text>
          </view>
          <view class="section-more" @click="navigateTo('/subPackages/activity/pages/attractions')">
            <text class="more-text">全部</text>
            <text class="more-icon">></text>
          </view>
        </view>
        
        <scroll-view scroll-x class="attractions-scroll" show-scrollbar="false" enhanced>
          <view class="attraction-item" v-for="(item, index) in attractions" :key="index" @click="viewAttraction(item)">
            <image :src="item.image" mode="aspectFill" class="attraction-image"></image>
            <view class="attraction-info">
              <view class="attraction-name-row">
                <text class="attraction-name">{{ item.name }}</text>
                <view class="attraction-rating">
                  <text class="rating-value">{{ item.rating }}</text>
                  <image src="/static/images/icons/star.png" class="rating-star"></image>
                </view>
              </view>
              <text class="attraction-address">{{ item.address }}</text>
              <view class="attraction-tags">
                <text class="tag-item" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{ tag }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 活动日历 -->
      <view class="events-section">
        <view class="section-header">
          <view class="section-title">
            <text class="title-text">本月活动</text>
          </view>
          <view class="section-more" @click="navigateTo('/subPackages/activity/pages/events')">
            <text class="more-text">日历</text>
            <text class="more-icon">></text>
          </view>
        </view>
        
        <view class="events-list">
          <view class="event-item" v-for="(event, index) in events" :key="index" @click="viewEvent(event)">
            <view class="event-date">
              <text class="event-month">{{ event.month }}</text>
              <text class="event-day">{{ event.day }}</text>
            </view>
            <view class="event-content">
              <text class="event-title">{{ event.title }}</text>
              <text class="event-location">{{ event.location }}</text>
              <view class="event-status" :class="{ 'status-ongoing': event.status === 'ongoing', 'status-upcoming': event.status === 'upcoming', 'status-ended': event.status === 'ended' }">
                {{ getStatusText(event.status) }}
              </view>
            </view>
            <view class="event-action">
              <text class="action-text">{{ event.actionText }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 本地文化 -->
      <view class="culture-section">
        <view class="section-header">
          <view class="section-title">
            <text class="title-text">本地文化</text>
          </view>
          <view class="section-more" @click="navigateTo('/subPackages/activity/pages/culture')">
            <text class="more-text">探索</text>
            <text class="more-icon">></text>
          </view>
        </view>
        
        <view class="culture-cards">
          <view class="culture-card" v-for="(item, index) in culturalItems" :key="index" @click="viewCulture(item)">
            <view class="culture-icon-bg" :style="{ backgroundColor: item.bgColor }">
              <image :src="item.icon" class="culture-icon"></image>
            </view>
            <view class="culture-info">
              <text class="culture-name">{{ item.name }}</text>
              <text class="culture-desc">{{ item.desc }}</text>
            </view>
            <view class="arrow-right">
              <text class="arrow-icon">></text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 攻略推荐 -->
      <view class="guides-section">
        <view class="section-header">
          <view class="section-title">
            <text class="title-text">精选攻略</text>
          </view>
          <view class="section-more" @click="navigateTo('/subPackages/activity/pages/guides')">
            <text class="more-text">全部</text>
            <text class="more-icon">></text>
          </view>
        </view>
        
        <view class="guides-list">
          <view class="guide-item" v-for="(guide, index) in guides" :key="index" @click="viewGuide(guide)">
            <image :src="guide.image" mode="aspectFill" class="guide-image"></image>
            <view class="guide-content">
              <text class="guide-title">{{ guide.title }}</text>
              <view class="guide-info">
                <view class="guide-author">
                  <image :src="guide.authorAvatar" class="author-avatar"></image>
                  <text class="author-name">{{ guide.authorName }}</text>
                </view>
                <view class="guide-stats">
                  <view class="stat-item">
                    <image src="/static/images/icons/view.png" class="stat-icon"></image>
                    <text class="stat-count">{{ guide.views }}</text>
                  </view>
                  <view class="stat-item">
                    <image src="/static/images/icons/like.png" class="stat-icon"></image>
                    <text class="stat-count">{{ guide.likes }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      refreshing: false,
      currentTab: 0,
      heroImages: [
        {
          image: '/static/images/cizhou/hero1.jpg',
          title: '磁州印象',
          desc: '千年窑火不熄，匠心守艺传承'
        },
        {
          image: '/static/images/cizhou/hero2.jpg',
          title: '磁州风光',
          desc: '山水相依，四季如画'
        },
        {
          image: '/static/images/cizhou/hero3.jpg',
          title: '人文磁州',
          desc: '历史悠久，文化灿烂'
        }
      ],
      tabs: [
        { name: '景点', icon: 'attraction' },
        { name: '美食', icon: 'food' },
        { name: '文化', icon: 'culture' },
        { name: '活动', icon: 'event' },
        { name: '住宿', icon: 'hotel' },
        { name: '购物', icon: 'shopping' }
      ],
      features: [
        {
          id: 1,
          name: '磁州窑瓷器',
          desc: '北方民窑之冠，千年窑火不熄',
          image: '/static/images/cizhou/feature1.jpg',
          tag: '非遗'
        },
        {
          id: 2,
          name: '民间剪纸',
          desc: '巧手剪就锦绣图，浓郁乡土风情',
          image: '/static/images/cizhou/feature2.jpg',
          tag: '传统'
        },
        {
          id: 3,
          name: '地方戏曲',
          desc: '磁州梆子，古老的民间艺术表演',
          image: '/static/images/cizhou/feature3.jpg',
          tag: '艺术'
        },
        {
          id: 4,
          name: '特色美食',
          desc: '老磁州炖菜，地道农家风味',
          image: '/static/images/cizhou/feature4.jpg',
          tag: '美食'
        }
      ],
      attractions: [
        {
          id: 1,
          name: '磁州窑博物馆',
          rating: 4.8,
          address: '磁县城南2公里处',
          image: '/static/images/cizhou/attraction1.jpg',
          tags: ['历史', '博物馆', '陶瓷']
        },
        {
          id: 2,
          name: '朱山风景区',
          rating: 4.6,
          address: '磁县东北15公里',
          image: '/static/images/cizhou/attraction2.jpg',
          tags: ['自然', '山水', '远足']
        },
        {
          id: 3,
          name: '古窑址公园',
          rating: 4.5,
          address: '磁县陶瓷产业园',
          image: '/static/images/cizhou/attraction3.jpg',
          tags: ['遗址', '公园', '文化']
        },
        {
          id: 4,
          name: '古城墙遗址',
          rating: 4.3,
          address: '磁县老城区',
          image: '/static/images/cizhou/attraction4.jpg',
          tags: ['古迹', '历史', '文化']
        }
      ],
      events: [
        {
          id: 1,
          title: '磁州窑文化节',
          month: '6月',
          day: '15',
          location: '磁州窑博物馆',
          status: 'upcoming',
          actionText: '预约'
        },
        {
          id: 2,
          title: '民俗文化展演',
          month: '6月',
          day: '20',
          location: '文化广场',
          status: 'upcoming',
          actionText: '预约'
        },
        {
          id: 3,
          title: '磁州好礼品鉴会',
          month: '6月',
          day: '10',
          location: '城市会展中心',
          status: 'ongoing',
          actionText: '参与'
        },
        {
          id: 4,
          title: '传统工艺体验日',
          month: '6月',
          day: '5',
          location: '磁州文化馆',
          status: 'ended',
          actionText: '回顾'
        }
      ],
      culturalItems: [
        {
          name: '磁州窑文化',
          desc: '了解磁州窑的历史和文化',
          icon: '/static/images/cizhou/culture1.jpg',
          bgColor: '#FFD700'
        },
        {
          name: '磁州剪纸艺术',
          desc: '探索磁州剪纸的独特魅力',
          icon: '/static/images/cizhou/culture2.jpg',
          bgColor: '#FF69B4'
        },
        {
          name: '磁州梆子戏曲',
          desc: '了解磁州梆子戏曲的历史和表演',
          icon: '/static/images/cizhou/culture3.jpg',
          bgColor: '#007AFF'
        },
        {
          name: '磁州美食',
          desc: '品尝磁州的传统美食',
          icon: '/static/images/cizhou/culture4.jpg',
          bgColor: '#FF4500'
        }
      ],
      guides: [
        {
          title: '磁州一日游攻略',
          image: '/static/images/cizhou/guide1.jpg',
          authorName: '张三',
          authorAvatar: '/static/images/cizhou/author1.jpg',
          views: '1,234',
          likes: '234'
        },
        {
          title: '磁州文化深度游',
          image: '/static/images/cizhou/guide2.jpg',
          authorName: '李四',
          authorAvatar: '/static/images/cizhou/author2.jpg',
          views: '890',
          likes: '123'
        },
        {
          title: '磁州美食探索',
          image: '/static/images/cizhou/guide3.jpg',
          authorName: '王五',
          authorAvatar: '/static/images/cizhou/author3.jpg',
          views: '765',
          likes: '98'
        },
        {
          title: '磁州住宿体验',
          image: '/static/images/cizhou/guide4.jpg',
          authorName: '赵六',
          authorAvatar: '/static/images/cizhou/author4.jpg',
          views: '654',
          likes: '76'
        }
      ]
    }
  },
  onLoad() {
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    share() {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    },
    onRefresh() {
      this.refreshing = true;
      setTimeout(() => {
        this.refreshing = false;
      }, 1200);
    },
    switchTab(index) {
      this.currentTab = index;
      // 这里可以根据选中的标签加载不同的内容
    },
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    },
    viewFeature(feature) {
      uni.navigateTo({
        url: `/subPackages/activity/pages/feature-detail?id=${feature.id}`
      });
    },
    viewAttraction(attraction) {
      uni.navigateTo({
        url: `/subPackages/activity/pages/attraction-detail?id=${attraction.id}`
      });
    },
    viewEvent(event) {
      uni.navigateTo({
        url: `/subPackages/activity/pages/event-detail?id=${event.id}`
      });
    },
    viewCulture(item) {
      uni.navigateTo({
        url: `/subPackages/activity/pages/culture-detail?name=${item.name}`
      });
    },
    viewGuide(guide) {
      uni.navigateTo({
        url: `/subPackages/activity/pages/guide-detail?title=${guide.title}`
      });
    },
    getStatusText(status) {
      switch(status) {
        case 'ongoing': return '进行中';
        case 'upcoming': return '即将开始';
        case 'ended': return '已结束';
        default: return '';
      }
    }
  }
}
</script>

<style>
.beautiful-cizhou {
  position: relative;
  height: 100vh;
  background-color: #F7F7F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-left, .navbar-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  flex: 1;
  margin: 0 50px; /* 添加左右外边距，为返回按钮留出空间 */
}

.back-icon, .icon-image {
  width: 32px;
  height: 32px;
}

/* 内容区域 */
.content-scroll {
  height: 100vh;
  box-sizing: border-box;
}

/* 顶部视觉区 */
.hero-section {
  width: 100%;
  height: 420rpx;
  position: relative;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.hero-swiper {
  width: 100%;
  height: 100%;
}

.hero-image {
  width: 100%;
  height: 100%;
}

.hero-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 40rpx;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
}

.hero-title {
  font-size: 36rpx;
  color: #FFFFFF;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.hero-desc {
  font-size: 24rpx;
  color: rgba(255,255,255,0.9);
  display: block;
}

/* 分类标签 */
.category-tabs {
  background-color: #FFFFFF;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.tab-scroll {
  white-space: nowrap;
  width: 100%;
}

.tab-item {
  display: inline-block;
  padding: 16rpx 30rpx;
  margin: 0 10rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #F5F5F5;
  transition: all 0.3s;
}

.tab-item:first-child {
  margin-left: 20rpx;
}

.tab-item:last-child {
  margin-right: 20rpx;
}

.tab-item.active {
  background-color: #007AFF;
  color: #FFFFFF;
  box-shadow: 0 4rpx 10rpx rgba(0,122,255,0.3);
}

.tab-text {
  font-weight: 500;
}

/* 通用板块样式 */
.feature-section, .attractions-section, .events-section {
  margin: 0 20rpx 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 24rpx;
}

.title-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #007AFF;
  border-radius: 4rpx;
}

.section-more {
  display: flex;
  align-items: center;
}

.more-text {
  font-size: 26rpx;
  color: #007AFF;
}

.more-icon {
  font-size: 26rpx;
  color: #007AFF;
  margin-left: 4rpx;
}

/* 特色板块 */
.feature-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.feature-card {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  position: relative;
}

.feature-image {
  width: 100%;
  height: 180rpx;
}

.feature-info {
  padding: 20rpx;
  background-color: #FFFFFF;
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.feature-desc {
  font-size: 24rpx;
  color: #666666;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.feature-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  padding: 6rpx 12rpx;
  background-color: rgba(0,122,255,0.8);
  color: #FFFFFF;
  font-size: 22rpx;
  border-radius: 20rpx;
}

/* 景点推荐 */
.attractions-scroll {
  width: 100%;
  white-space: nowrap;
}

.attraction-item {
  display: inline-block;
  width: 400rpx;
  margin-right: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  background-color: #FFFFFF;
}

.attraction-item:last-child {
  margin-right: 0;
}

.attraction-image {
  width: 100%;
  height: 220rpx;
}

.attraction-info {
  padding: 20rpx;
}

.attraction-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.attraction-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.attraction-rating {
  display: flex;
  align-items: center;
}

.rating-value {
  font-size: 24rpx;
  color: #FF9500;
  font-weight: 600;
  margin-right: 4rpx;
}

.rating-star {
  width: 24rpx;
  height: 24rpx;
}

.attraction-address {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attraction-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag-item {
  font-size: 20rpx;
  color: #007AFF;
  background-color: rgba(0,122,255,0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
}

/* 活动日历 */
.events-list {
  
}

.event-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #F0F0F0;
}

.event-item:last-child {
  border-bottom: none;
}

.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  background-color: #F5F5F5;
  border-radius: 12rpx;
  padding: 16rpx 0;
  margin-right: 20rpx;
}

.event-month {
  font-size: 24rpx;
  color: #666666;
}

.event-day {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.event-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.event-location {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.event-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  display: inline-block;
}

.status-ongoing {
  background-color: rgba(40,205,65,0.1);
  color: #28CD41;
}

.status-upcoming {
  background-color: rgba(0,122,255,0.1);
  color: #007AFF;
}

.status-ended {
  background-color: rgba(142,142,147,0.1);
  color: #8E8E93;
}

.event-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
}

.action-text {
  font-size: 26rpx;
  color: #007AFF;
}

/* 本地文化 */
.culture-section {
  margin: 0 20rpx 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.culture-cards {
  
}

.culture-card {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #F0F0F0;
}

.culture-card:last-child {
  border-bottom: none;
}

.culture-icon-bg {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.culture-icon {
  width: 40rpx;
  height: 40rpx;
}

.culture-info {
  flex: 1;
}

.culture-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.culture-desc {
  font-size: 24rpx;
  color: #666666;
  display: block;
}

.arrow-right {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  font-size: 24rpx;
  color: #999999;
}

/* 攻略推荐 */
.guides-section {
  margin: 0 20rpx 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.guides-list {
  
}

.guide-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #F0F0F0;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-image {
  width: 200rpx;
  height: 140rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.guide-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.guide-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.guide-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.guide-author {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.author-name {
  font-size: 24rpx;
  color: #666666;
}

.guide-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.stat-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
}

.stat-count {
  font-size: 22rpx;
  color: #999999;
}
</style> 
