/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.order-management-container {
  min-height: 100vh;
  position: relative;
  background-color: #f5f8fc;
  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 80px);
  /* 增加底部间距，避免被底部导航栏遮挡 */
  display: flex;
  flex-direction: column;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  border-radius: 0;
}
.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: transparent;
  border-radius: 0;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
  margin-left: 16px;
}
.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  /* 增加字体粗细 */
  flex: 1;
  text-align: center;
}
.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}
.view-selector {
  display: flex;
  background-color: #fff;
  padding: 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  margin: 90px 16px 0;
  z-index: 10;
  position: relative;
}
.view-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
  position: relative;
}
.view-option.active {
  color: #1677FF;
}
.view-option.active::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #1677FF;
  border-radius: 3px;
}
.view-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}
.bg-list {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
}
.bg-detail {
  background: linear-gradient(135deg, #2196F3, #0D47A1);
}
.bg-calendar {
  background: linear-gradient(135deg, #9C27B0, #6A1B9A);
}
.view-name {
  font-size: 12px;
  margin-top: 4px;
}
.filter-section {
  display: flex;
  padding: 16px;
  background-color: #fff;
  margin: 16px 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-radius: 16px;
}
.filter-scroll {
  flex: 1;
  white-space: nowrap;
}
.status-tag {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 8px;
  background-color: #f0f0f0;
  border-radius: 24px;
  font-size: 12px;
  color: #666;
  transition: all 0.3s ease;
}
.status-tag.active {
  background-color: #e6f7ff;
  color: #1677FF;
  border: 1px solid #91caff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
}
.filter-button {
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-left: 8px;
  border-left: 1px solid #eee;
}
.filter-icon-container {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: linear-gradient(135deg, #FF9800, #F57C00);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}
.filter-icon {
  font-size: 16px;
}
.content-main {
  flex: 1;
  padding: 16px;
  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 40px);
  /* 增加底部内边距，避免被底部导航栏遮挡 */
  overflow-y: auto;
}
.order-list {
  display: flex;
  flex-direction: column;
}
.order-item {
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.order-item:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}
.order-number {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}
.order-status-tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
}
.status-pending {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}
.status-processing {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}
.status-completed {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
}
.status-cancelled {
  background: linear-gradient(135deg, #9E9E9E, #616161);
}
.status-refunding {
  background: linear-gradient(135deg, #F44336, #C62828);
}
.order-info {
  margin-bottom: 16px;
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 12px;
}
.customer-info, .time-info, .amount-info {
  display: flex;
  margin-bottom: 8px;
}
.amount-info {
  margin-bottom: 0;
}
.label {
  width: 70px;
  color: #666;
  font-size: 13px;
}
.value {
  flex: 1;
  font-size: 13px;
  color: #333;
}
.price {
  font-weight: 700;
  color: #ff6a00;
}
.order-actions {
  display: flex;
  justify-content: flex-end;
}
.action-btn {
  padding: 8px 16px;
  border-radius: 24px;
  font-size: 13px;
  margin-left: 12px;
  background-color: #f0f0f0;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.action-btn.primary {
  background: linear-gradient(135deg, #1677FF, #0D47A1);
  color: #fff;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 0;
}
.empty-icon-container {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #E0E0E0, #BDBDBD);
  border-radius: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}
.empty-icon {
  font-size: 32px;
}
.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 24px;
}
.refresh-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #1677FF, #0D47A1);
  color: #fff;
  border-radius: 24px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(22, 119, 255, 0.2);
  transition: all 0.3s ease;
}
.refresh-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2px 4px rgba(22, 119, 255, 0.2);
}
.order-detail-view, .order-calendar-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
.nav-btn {
  margin-top: 20px;
  background: linear-gradient(135deg, #1677FF, #0D47A1);
  color: #fff;
  font-size: 14px;
  padding: 10px 24px;
  border-radius: 24px;
  border: none;
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(22, 119, 255, 0.2);
  transition: all 0.3s ease;
}
.tab-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  background-color: #FFFFFF;
  border-top: 1px solid var(--border-light);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 0 10px;
  padding-bottom: env(safe-area-inset-bottom);
  flex-shrink: 0;
  border-radius: 0;
}
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  transition: all 0.2s ease;
  padding: 8px 0;
  margin: 0 8px;
}
.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 3px;
  transition: all 0.25s cubic-bezier(0.3, 0.7, 0.4, 1.5);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 22px;
  opacity: 0.7;
}
.tab-text {
  font-size: 10px;
  color: var(--text-tertiary);
  transition: color 0.2s ease, transform 0.25s ease;
  transform: scale(0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 0 2px;
}
.active-indicator {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 16px;
  height: 3px;
  border-radius: 0 0 4px 4px;
  background: linear-gradient(90deg, var(--brand-primary), var(--accent-purple));
}
.tab-item.active .tab-icon {
  transform: translateY(-2px);
  opacity: 1;
}
.tab-item.active .tab-text {
  color: var(--brand-primary);
  font-weight: 500;
  transform: scale(1);
}

/* 导航图标样式 */
.tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}
.tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}
.tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'/%3E%3C/svg%3E");
}
.tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}
.tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}
@media screen and (max-width: 375px) {
  /* 在较小屏幕上优化导航栏 */
.tab-text {
    font-size: 9px;
}
.tab-icon {
    margin-bottom: 2px;
    background-size: 20px;
}
}
/* 在较宽屏幕上优化导航栏，确保元素不会挤压 */
@media screen and (min-width: 400px) {
.tab-bar {
    padding: 0 10px;
}
.tab-item {
    margin: 0 5px;
}
}
/* 添加底部安全区域 */
.safe-area-bottom {
  height: calc(60px + env(safe-area-inset-bottom));
}