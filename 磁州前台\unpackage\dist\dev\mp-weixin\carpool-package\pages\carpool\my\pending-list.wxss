
.pending-list-container.data-v-e7b40776 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 自定义导航栏 */
.custom-navbar.data-v-e7b40776 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(140rpx + var(--status-bar-height, 44px));
  background: linear-gradient(135deg, #3a7be8, #4f8cff);
  z-index: 100;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 16rpx rgba(79,140,255,0.15);
}
.navbar-content.data-v-e7b40776 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 140rpx;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 44px);
}
.navbar-left.data-v-e7b40776, .navbar-right.data-v-e7b40776 {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-icon.data-v-e7b40776 {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}
.navbar-title.data-v-e7b40776 {
  font-size: 40rpx;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 2rpx;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 内容区域 */
.scrollable-content.data-v-e7b40776 {
  flex: 1;
  margin-top: calc(44px + var(--status-bar-height));
  padding: 12px;
}

/* 卡片列表 */
.card-list.data-v-e7b40776 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.card-item.data-v-e7b40776 {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 卡片头部 */
.card-header.data-v-e7b40776 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #F8FAFB;
  border-bottom: 1px solid #EEEEEE;
}
.header-left.data-v-e7b40776 {
  display: flex;
  align-items: center;
  gap: 8px;
}
.card-type.data-v-e7b40776 {
  font-size: 15px;
  font-weight: 500;
  color: #333333;
}
.publish-time.data-v-e7b40776 {
  font-size: 12px;
  color: #999999;
}
.status-tag.data-v-e7b40776 {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.status-pending.data-v-e7b40776 {
  background-color: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

/* 卡片内容 */
.card-content.data-v-e7b40776 {
  padding: 16px;
}
.route-info.data-v-e7b40776 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.route-points.data-v-e7b40776 {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.start-point.data-v-e7b40776, .end-point.data-v-e7b40776 {
  display: flex;
  align-items: center;
  gap: 10px;
}
.point-marker.data-v-e7b40776 {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.start.data-v-e7b40776 {
  background-color: #1677FF;
}
.end.data-v-e7b40776 {
  background-color: #FF5722;
}
.route-line.data-v-e7b40776 {
  width: 2px;
  height: 20px;
  background-color: #DDDDDD;
  margin-left: 5px;
}
.point-text.data-v-e7b40776 {
  font-size: 16px;
  color: #333333;
}
.trip-info.data-v-e7b40776 {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}
.info-item.data-v-e7b40776 {
  display: flex;
  align-items: center;
  gap: 6px;
}
.info-icon.data-v-e7b40776 {
  width: 24px;
  height: 24px;
}
.info-text.data-v-e7b40776 {
  font-size: 14px;
  color: #666666;
}
.price.data-v-e7b40776 {
  color: #FF5722;
  font-weight: 500;
}

/* 审核状态 */
.audit-status.data-v-e7b40776 {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #EEEEEE;
}
.audit-progress.data-v-e7b40776 {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.progress-bar.data-v-e7b40776 {
  height: 6px;
  background-color: #F5F5F5;
  border-radius: 3px;
  overflow: hidden;
}
.progress-inner.data-v-e7b40776 {
  height: 100%;
  background-color: #FF9800;
  border-radius: 3px;
}
.progress-text.data-v-e7b40776 {
  font-size: 12px;
  color: #999999;
  text-align: right;
}

/* 卡片底部按钮 */
.card-actions.data-v-e7b40776 {
  display: flex;
  justify-content: flex-end;
  padding: 20px 24px;
  gap: 20px;
  border-top: 1px solid #EEEEEE;
}
.action-button.data-v-e7b40776 {
  padding: 10px 20px;
  font-size: 16px;
  height: 44px;
  min-width: 90px;
  border-radius: 6px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-button.outline.data-v-e7b40776 {
  color: #666666;
  border: 1px solid #DDDDDD;
}
.action-button.primary.data-v-e7b40776 {
  color: #FFFFFF;
  background-color: #FF9800;
  border: 1px solid #FF9800;
}

/* 空状态 */
.empty-state.data-v-e7b40776 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}
.empty-image.data-v-e7b40776 {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}
.empty-text.data-v-e7b40776 {
  font-size: 16px;
  color: #999999;
  margin-bottom: 20px;
}
.publish-button.data-v-e7b40776 {
  background-color: #FF9800;
  color: #FFFFFF;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
}

/* 加载状态 */
.loading-state.data-v-e7b40776 {
  padding: 16px 0;
  text-align: center;
}
.loading-text.data-v-e7b40776 {
  font-size: 14px;
  color: #999999;
}

/* 列表底部 */
.list-bottom.data-v-e7b40776 {
  padding: 16px 0;
  text-align: center;
}
.bottom-text.data-v-e7b40776 {
  font-size: 14px;
  color: #999999;
}

/* 弹窗样式 */
.popup-mask.data-v-e7b40776 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
.popup-container.data-v-e7b40776 {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
}
.popup-header.data-v-e7b40776 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #EEEEEE;
}
.popup-title.data-v-e7b40776 {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
}
.popup-close.data-v-e7b40776 {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}
.popup-content.data-v-e7b40776 {
  padding: 16px;
}
.accelerate-info.data-v-e7b40776 {
  margin-bottom: 16px;
}
.accelerate-title.data-v-e7b40776 {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8px;
  display: block;
}
.accelerate-desc.data-v-e7b40776 {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}
.topup-options.data-v-e7b40776 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.topup-option.data-v-e7b40776 {
  display: flex;
  align-items: center;
  padding: 14px;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  gap: 12px;
}
.option-icon-wrap.data-v-e7b40776 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.payment.data-v-e7b40776 {
  background-color: rgba(22, 119, 255, 0.1);
}
.ad.data-v-e7b40776 {
  background-color: rgba(255, 152, 0, 0.1);
}
.option-icon.data-v-e7b40776 {
  width: 24px;
  height: 24px;
}
.option-info.data-v-e7b40776 {
  flex: 1;
}
.option-title.data-v-e7b40776 {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
.option-desc.data-v-e7b40776 {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
}
.option-price.data-v-e7b40776, .option-free.data-v-e7b40776 {
  font-size: 14px;
  margin-top: 4px;
}
.option-price.data-v-e7b40776 {
  color: #FF5722;
  font-weight: 500;
}
.option-free.data-v-e7b40776 {
  color: #52C41A;
  font-weight: 500;
}
.popup-footer.data-v-e7b40776 {
  display: flex;
  border-top: 1px solid #EEEEEE;
}
.popup-button.data-v-e7b40776 {
  flex: 1;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 16px;
}
.popup-button.cancel.data-v-e7b40776 {
  color: #666666;
  border-right: 1px solid #EEEEEE;
}
.popup-button.confirm.data-v-e7b40776 {
  color: #FF9800;
  font-weight: 500;
}
