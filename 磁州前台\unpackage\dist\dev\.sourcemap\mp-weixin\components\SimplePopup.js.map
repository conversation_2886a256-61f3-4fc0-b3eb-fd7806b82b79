{"version": 3, "file": "SimplePopup.js", "sources": ["components/SimplePopup.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9TaW1wbGVQb3B1cC52dWU"], "sourcesContent": ["<template>\r\n  <view v-if=\"visible\" class=\"simple-popup-mask\" @click=\"close\">\r\n    <view class=\"simple-popup-content\" :class=\"type\" @click.stop>\r\n      <slot></slot>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\n\r\n// --- Props ---\r\nconst props = defineProps({\r\n  type: {\r\n    type: String,\r\n    default: 'center'\r\n  }\r\n});\r\n\r\n// --- 响应式状态 ---\r\nconst visible = ref(false);\r\n\r\n// --- 方法 ---\r\nconst open = () => {\r\n  visible.value = true;\r\n};\r\n\r\nconst close = () => {\r\n  visible.value = false;\r\n};\r\n\r\n// --- Expose ---\r\n// 暴露方法给父组件调用\r\ndefineExpose({\r\n  open,\r\n  close\r\n});\r\n</script>\r\n\r\n<style>\r\n.simple-popup-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 999;\r\n}\r\n\r\n.simple-popup-content {\r\n  background-color: transparent;\r\n  max-width: 80%;\r\n  max-height: 80%;\r\n  overflow: hidden;\r\n}\r\n\r\n.simple-popup-content.bottom {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  max-width: 100%;\r\n}\r\n\r\n.simple-popup-content.center {\r\n  border-radius: 12rpx;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/SimplePopup.vue'\nwx.createComponent(Component)"], "names": ["ref", "Component"], "mappings": ";;;;;;;;;;;AAoBA,UAAM,UAAUA,cAAAA,IAAI,KAAK;AAGzB,UAAM,OAAO,MAAM;AACjB,cAAQ,QAAQ;AAAA,IAClB;AAEA,UAAM,QAAQ,MAAM;AAClB,cAAQ,QAAQ;AAAA,IAClB;AAIA,aAAa;AAAA,MACX;AAAA,MACA;AAAA,IACF,CAAC;;;;;;;;;;;;;ACnCD,GAAG,gBAAgBC,SAAS;"}