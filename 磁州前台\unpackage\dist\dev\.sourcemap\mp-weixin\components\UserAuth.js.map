{"version": 3, "file": "UserAuth.js", "sources": ["components/UserAuth.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9Vc2VyQXV0aC52dWU"], "sourcesContent": ["<template>\n  <view class=\"user-auth\">\n    <!-- 微信小程序环境 -->\n    <block v-if=\"mpWeixin\">\n      <button v-if=\"!hasUserInfo\" class=\"auth-btn\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\n        <text class=\"auth-btn-text\">{{ buttonText }}</text>\n      </button>\n      <slot v-else></slot>\n    </block>\n    \n    <!-- 非微信小程序环境 -->\n    <block v-else>\n      <button v-if=\"!hasUserInfo\" class=\"auth-btn\" @click=\"mockAuthorize\">\n        <text class=\"auth-btn-text\">{{ buttonText }}</text>\n      </button>\n      <slot v-else></slot>\n    </block>\n    \n    <!-- 昵称输入弹窗 -->\n    <view class=\"nickname-popup\" v-if=\"showNicknamePopup\">\n      <view class=\"nickname-container\">\n        <view class=\"nickname-header\">\n          <text class=\"nickname-title\">设置昵称</text>\n        </view>\n        <view class=\"nickname-content\">\n          <text class=\"nickname-tip\">请设置您的昵称</text>\n          <input class=\"nickname-input\" v-model=\"nickname\" placeholder=\"请输入昵称\" maxlength=\"12\" />\n        </view>\n        <view class=\"nickname-footer\">\n          <button class=\"cancel-btn\" @click=\"cancelNickname\">取消</button>\n          <button class=\"confirm-btn\" @click=\"confirmNickname\">确定</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\nimport { hasUserInfo as checkUserInfo, updateUserProfile, getLocalUserInfo } from '@/utils/userProfile.js';\n\n// 定义props\nconst props = defineProps({\n  buttonText: {\n    type: String,\n    default: '获取头像昵称'\n  },\n  autoAuth: {\n    type: Boolean,\n    default: true // 默认开启自动授权\n  }\n});\n\n// 定义事件\nconst emit = defineEmits(['update:userInfo']);\n\n// 环境检测\nconst mpWeixin = ref(false);\n// 是否已有用户信息\nconst userInfoExists = ref(false);\n// 是否显示昵称输入弹窗\nconst showNicknamePopup = ref(false);\n// 用户选择的头像\nconst selectedAvatar = ref('');\n// 用户输入的昵称\nconst nickname = ref('');\n\n// 计算是否已有用户信息\nconst hasUserInfo = computed(() => {\n  return userInfoExists.value;\n});\n\n// 选择头像回调\nconst onChooseAvatar = (e) => {\n  const avatarUrl = e.detail.avatarUrl;\n  selectedAvatar.value = avatarUrl;\n  \n  // 上传到临时目录\n  uni.uploadFile({\n    url: 'https://your-api-domain/api/upload/avatar',\n    filePath: avatarUrl,\n    name: 'file',\n    success: (uploadRes) => {\n      // 实际项目中，这里应该获取服务器返回的URL\n      // 示例中使用本地临时文件路径\n      // 打开昵称输入弹窗\n      showNicknamePopup.value = true;\n    },\n    fail: () => {\n      // 上传失败时直接使用临时路径\n      showNicknamePopup.value = true;\n    }\n  });\n};\n\n// 确认昵称\nconst confirmNickname = async () => {\n  if (!nickname.value.trim()) {\n    uni.showToast({\n      title: '昵称不能为空',\n      icon: 'none'\n    });\n    return;\n  }\n  \n  // 更新用户信息\n  const updatedUser = await updateUserProfile({\n    avatarUrl: selectedAvatar.value,\n    nickName: nickname.value\n  });\n  \n  if (updatedUser) {\n    userInfoExists.value = true;\n    showNicknamePopup.value = false;\n    \n    // 通知父组件\n    emit('update:userInfo', updatedUser);\n    \n    uni.showToast({\n      title: '设置成功',\n      icon: 'success'\n    });\n  } else {\n    uni.showToast({\n      title: '设置失败，请重试',\n      icon: 'none'\n    });\n  }\n};\n\n// 自动获取用户信息\nconst autoGetUserInfo = () => {\n  // 只在微信环境下执行\n  if (!mpWeixin.value) return;\n  \n  // 如果已有用户信息则不执行\n  if (userInfoExists.value) return;\n  \n  // 小程序环境下，使用原生接口获取用户信息\n  // #ifdef MP-WEIXIN\n  // 首先尝试通过getUserProfile获取用户基本信息\n  uni.getUserProfile({\n    desc: '用于完善用户资料',\n    success: (res) => {\n      // 保存获取到的昵称\n      nickname.value = res.userInfo.nickName || '用户' + Math.floor(Math.random() * 10000);\n      \n      // 由于头像需要用户主动授权，这里使用默认头像，\n      // 并且提示用户点击\"获取头像\"按钮来选择头像\n      selectedAvatar.value = '/static/images/default-avatar.png';\n      \n      // 自动更新用户信息\n      updateUserProfile({\n        avatarUrl: selectedAvatar.value,\n        nickName: nickname.value\n      }).then(updatedUser => {\n        if (updatedUser) {\n          userInfoExists.value = true;\n          // 通知父组件\n          emit('update:userInfo', updatedUser);\n          \n          // 提示用户可以更新头像\n          uni.showToast({\n            title: '已获取基本信息，点击更换头像',\n            icon: 'none',\n            duration: 2000\n          });\n        }\n      });\n    },\n    fail: (err) => {\n      console.log('获取用户信息失败', err);\n      \n      // 失败时，提示用户手动授权\n      uni.showToast({\n        title: '请点击按钮授权获取头像昵称',\n        icon: 'none',\n        duration: 2000\n      });\n    }\n  });\n  // #endif\n  \n  // 非微信小程序环境\n  // #ifndef MP-WEIXIN\n  mockAuthorize();\n  // #endif\n};\n\n// 取消设置昵称\nconst cancelNickname = () => {\n  showNicknamePopup.value = false;\n  selectedAvatar.value = '';\n  nickname.value = '';\n};\n\n// 模拟授权（非微信小程序环境下使用）\nconst mockAuthorize = () => {\n  uni.showModal({\n    title: '提示',\n    content: '非微信小程序环境，点击确定模拟授权',\n    success: async (res) => {\n      if (res.confirm) {\n        // 模拟授权成功\n        const updatedUser = await updateUserProfile({\n          avatarUrl: '/static/images/default-avatar.png',\n          nickName: '测试用户' + Math.floor(Math.random() * 1000)\n        });\n        \n        if (updatedUser) {\n          userInfoExists.value = true;\n          // 通知父组件\n          emit('update:userInfo', updatedUser);\n        }\n      }\n    }\n  });\n};\n\nonMounted(() => {\n  // 检查环境\n  // #ifdef MP-WEIXIN\n  mpWeixin.value = true;\n  // #endif\n  \n  // 检查是否有用户信息\n  userInfoExists.value = checkUserInfo();\n  \n  // 如果已有用户信息，通知父组件\n  if (userInfoExists.value) {\n    emit('update:userInfo', getLocalUserInfo());\n  } else if (props.autoAuth) {\n    // 如果开启了自动授权且没有用户信息，自动请求授权\n    autoGetUserInfo();\n  }\n});\n</script>\n\n<style lang=\"scss\">\n.user-auth {\n  width: 100%;\n}\n\n.auth-btn {\n  background-color: #07c160;\n  color: #ffffff;\n  border-radius: 44rpx;\n  font-size: 28rpx;\n  height: 88rpx;\n  line-height: 88rpx;\n  width: 100%;\n  text-align: center;\n  padding: 0;\n  margin: 0;\n  border: none;\n}\n\n.auth-btn::after {\n  border: none;\n}\n\n.auth-btn-text {\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n.nickname-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  z-index: 999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.nickname-container {\n  width: 560rpx;\n  background-color: #ffffff;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.nickname-header {\n  padding: 30rpx;\n  text-align: center;\n  border-bottom: 1rpx solid #eee;\n}\n\n.nickname-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.nickname-content {\n  padding: 30rpx;\n}\n\n.nickname-tip {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n  margin-bottom: 20rpx;\n}\n\n.nickname-input {\n  width: 100%;\n  height: 80rpx;\n  border: 1rpx solid #ddd;\n  border-radius: 8rpx;\n  padding: 0 20rpx;\n  font-size: 28rpx;\n  box-sizing: border-box;\n}\n\n.nickname-footer {\n  display: flex;\n  border-top: 1rpx solid #eee;\n}\n\n.cancel-btn, .confirm-btn {\n  flex: 1;\n  height: 90rpx;\n  line-height: 90rpx;\n  text-align: center;\n  font-size: 30rpx;\n  margin: 0;\n  padding: 0;\n  border-radius: 0;\n  background-color: #fff;\n}\n\n.cancel-btn {\n  color: #666;\n  border-right: 1rpx solid #eee;\n}\n\n.confirm-btn {\n  color: #07c160;\n  font-weight: 500;\n}\n\n.cancel-btn::after, .confirm-btn::after {\n  border: none;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/UserAuth.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "uni", "updateUserProfile", "onMounted", "checkUserInfo", "getLocalUserInfo", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;AA0CA,UAAM,QAAQ;AAYd,UAAM,OAAO;AAGb,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAE1B,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAEhC,UAAM,oBAAoBA,cAAAA,IAAI,KAAK;AAEnC,UAAM,iBAAiBA,cAAAA,IAAI,EAAE;AAE7B,UAAM,WAAWA,cAAAA,IAAI,EAAE;AAGvB,UAAM,cAAcC,cAAQ,SAAC,MAAM;AACjC,aAAO,eAAe;AAAA,IACxB,CAAC;AAGD,UAAM,iBAAiB,CAAC,MAAM;AAC5B,YAAM,YAAY,EAAE,OAAO;AAC3B,qBAAe,QAAQ;AAGvBC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS,CAAC,cAAc;AAItB,4BAAkB,QAAQ;AAAA,QAC3B;AAAA,QACD,MAAM,MAAM;AAEV,4BAAkB,QAAQ;AAAA,QAC3B;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,YAAY;AAClC,UAAI,CAAC,SAAS,MAAM,QAAQ;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,YAAM,cAAc,MAAMC,oCAAkB;AAAA,QAC1C,WAAW,eAAe;AAAA,QAC1B,UAAU,SAAS;AAAA,MACvB,CAAG;AAED,UAAI,aAAa;AACf,uBAAe,QAAQ;AACvB,0BAAkB,QAAQ;AAG1B,aAAK,mBAAmB,WAAW;AAEnCD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,OAAS;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAE5B,UAAI,CAAC,SAAS;AAAO;AAGrB,UAAI,eAAe;AAAO;AAK1BA,oBAAAA,MAAI,eAAe;AAAA,QACjB,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AAEhB,mBAAS,QAAQ,IAAI,SAAS,YAAY,OAAO,KAAK,MAAM,KAAK,OAAQ,IAAG,GAAK;AAIjF,yBAAe,QAAQ;AAGvBC,8CAAkB;AAAA,YAChB,WAAW,eAAe;AAAA,YAC1B,UAAU,SAAS;AAAA,UAC3B,CAAO,EAAE,KAAK,iBAAe;AACrB,gBAAI,aAAa;AACf,6BAAe,QAAQ;AAEvB,mBAAK,mBAAmB,WAAW;AAGnCD,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cACtB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAY,MAAA,MAAA,OAAA,kCAAA,YAAY,GAAG;AAG3BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IAOH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,wBAAkB,QAAQ;AAC1B,qBAAe,QAAQ;AACvB,eAAS,QAAQ;AAAA,IACnB;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AAEf,kBAAM,cAAc,MAAMC,oCAAkB;AAAA,cAC1C,WAAW;AAAA,cACX,UAAU,SAAS,KAAK,MAAM,KAAK,OAAQ,IAAG,GAAI;AAAA,YAC5D,CAAS;AAED,gBAAI,aAAa;AACf,6BAAe,QAAQ;AAEvB,mBAAK,mBAAmB,WAAW;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEAC,kBAAAA,UAAU,MAAM;AAGd,eAAS,QAAQ;AAIjB,qBAAe,QAAQC,kBAAAA;AAGvB,UAAI,eAAe,OAAO;AACxB,aAAK,mBAAmBC,kBAAgB,iBAAA,CAAE;AAAA,MAC9C,WAAa,MAAM,UAAU;AAEzB;MACD;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;AC1OD,GAAG,gBAAgBC,SAAS;"}