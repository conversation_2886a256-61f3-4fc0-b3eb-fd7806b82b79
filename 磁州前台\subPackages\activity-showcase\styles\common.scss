// 苹果风格通用样式
.page-container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 24rpx;
}

.card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  
  &::after {
    content: '';
    display: block;
    width: 48rpx;
    height: 4rpx;
    background: #007AFF;
    margin-top: 8rpx;
    border-radius: 2rpx;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 32rpx;
  }
  
  .empty-text {
    color: #999;
    font-size: 28rpx;
  }
}

.btn-primary {
  background: #007AFF;
  color: #fff;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
  
  &:active {
    background: darken(#007AFF, 10%);
  }
}

.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 2rpx solid #f5f5f5;
  
  &:last-child {
    border-bottom: none;
  }
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
} 