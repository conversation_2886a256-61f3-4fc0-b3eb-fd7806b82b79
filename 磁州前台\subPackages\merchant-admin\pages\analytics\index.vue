<template>
  <view class="analytics-page">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg class="back-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-main">数据分析</text>
      </view>
      <view class="navbar-right">
        <view class="more-button" @tap="showMore">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
    </view>

    <!-- 内容滚动区域 -->
    <scroll-view class="content-container" scroll-y refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="isRefreshing">
      <!-- 日期选择器 -->
      <view class="date-range-picker">
        <view class="date-tabs">
          <view 
            v-for="(tab, index) in dateTabs" 
            :key="index" 
            class="date-tab" 
            :class="{ active: currentDateTab === tab.value }"
            @tap="switchDateTab(tab.value)"
          >
            {{ tab.label }}
          </view>
        </view>
        <view class="custom-date" @tap="showDateRangePicker">
          <view class="custom-date-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 2V6M16 2V6M3 10H21M5 4H19C20.1046 4 21 4.89543 21 6V20C21 21.1046 20.1046 22 19 22H5C3.89543 22 3 21.1046 3 20V6C3 4.89543 3.89543 4 5 4Z" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text class="custom-date-text">自定义</text>
        </view>
      </view>
      
      <!-- 概览卡片 -->
      <view class="overview-card">
        <view class="overview-header">
          <text class="overview-title">数据概览</text>
          <text class="overview-period">{{ dateRangeText }}</text>
        </view>
        
        <view class="overview-grid">
          <view class="metric-card" v-for="(metric, index) in metrics" :key="index" @tap="navigateToDetail(metric.type)">
            <view class="metric-content">
              <view class="metric-header">
                <text class="metric-title">{{ metric.title }}</text>
                <svg class="metric-icon" v-if="metric.type === 'sales'" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="24" height="24" rx="12" fill="#5E5CE6" fill-opacity="0.1"/>
                  <path d="M17 9L13.5 12.5M12 6L7.5 10.5M15 12L12.5 14.5M15 6L12.5 8.5M10 7.5L7.5 10M10 12L6 16" stroke="#5E5CE6" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <svg class="metric-icon" v-else-if="metric.type === 'orders'" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="24" height="24" rx="12" fill="#30D158" fill-opacity="0.1"/>
                  <path d="M9 17H15M9 13H15M9 9H15M5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21Z" stroke="#30D158" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <svg class="metric-icon" v-else-if="metric.type === 'customers'" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="24" height="24" rx="12" fill="#FF9F0A" fill-opacity="0.1"/>
                  <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z" stroke="#FF9F0A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <svg class="metric-icon" v-else-if="metric.type === 'conversion'" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="24" height="24" rx="12" fill="#FF453A" fill-opacity="0.1"/>
                  <path d="M16 3H21V8M18 14C18 15.5913 17.5308 17.1174 16.6518 18.4435C15.7727 19.7695 14.5233 20.8411 13.0615 21.5373C11.5997 22.2335 9.99113 22.5208 8.39779 22.3684C6.80445 22.2159 5.29824 21.6292 4.05026 20.6694C2.80228 19.7096 1.86543 18.4161 1.35126 16.9412C0.837085 15.4662 0.769894 13.8788 1.15622 12.3657C1.54254 10.8526 2.36523 9.47347 3.52513 8.39761C4.68503 7.32175 6.13294 6.57589 7.695 6.249M21 16L12 7L8 11" stroke="#FF453A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
              <text class="metric-value">{{ metric.value }}</text>
              <view class="metric-trend" :class="metric.trend">
                <svg v-if="metric.trend === 'up'" class="trend-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 20L12 4M12 4L18 10M12 4L6 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <svg v-else class="trend-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 4L12 20M12 20L6 14M12 20L18 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <text class="trend-text">{{ metric.trendValue }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 销售趋势图表 -->
      <view class="chart-card">
        <view class="chart-header">
          <text class="chart-title">销售趋势</text>
          <view class="chart-legend">
            <view class="legend-item">
              <view class="legend-color" style="background-color: #5E5CE6;"></view>
              <text class="legend-text">销售额</text>
            </view>
            <view class="legend-item">
              <view class="legend-color" style="background-color: #30D158;"></view>
              <text class="legend-text">订单数</text>
            </view>
          </view>
        </view>
        
        <view class="chart-container">
          <!-- 模拟图表 - 使用苹果设计风格的UI元素来展示 -->
          <view class="mock-chart">
            <view class="chart-y-axis">
              <text v-for="(value, index) in yAxisValues" :key="index" class="y-axis-label">{{ value }}</text>
            </view>
            <view class="chart-area">
              <!-- 绘制销售额曲线 -->
              <view class="line-chart sales-line">
                <view class="chart-point" v-for="(point, index) in salesData" :key="index" :style="{ left: `${index * (100 / (salesData.length - 1))}%`, bottom: `${point.value}%` }">
                  <view class="point-dot"></view>
                  <view class="point-tooltip">
                    <text class="tooltip-value">¥{{ point.amount }}</text>
                    <text class="tooltip-date">{{ point.date }}</text>
                  </view>
                </view>
              </view>
              
              <!-- 绘制订单数曲线 -->
              <view class="line-chart orders-line">
                <view class="chart-point" v-for="(point, index) in ordersData" :key="index" :style="{ left: `${index * (100 / (ordersData.length - 1))}%`, bottom: `${point.value}%` }">
                  <view class="point-dot"></view>
                  <view class="point-tooltip">
                    <text class="tooltip-value">{{ point.amount }}单</text>
                    <text class="tooltip-date">{{ point.date }}</text>
                  </view>
                </view>
              </view>
              
              <!-- 绘制网格线 -->
              <view class="chart-grid">
                <view class="grid-line" v-for="i in 5" :key="i"></view>
              </view>
            </view>
            <view class="chart-x-axis">
              <text v-for="(day, index) in xAxisLabels" :key="index" class="x-axis-label">{{ day }}</text>
            </view>
          </view>
        </view>
        
        <!-- 图表按钮栏 -->
        <view class="chart-actions">
          <view class="action-button" @tap="navigateToDetail('sales')">
            <text class="action-text">查看详情</text>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
      </view>
      
      <!-- 热门商品 -->
      <view class="products-card">
        <view class="products-header">
          <text class="products-title">热门商品</text>
          <view class="more-link" @tap="navigateToDetail('products')">
            <text class="more-text">查看全部</text>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
        </view>
        
        <view class="product-list">
          <view class="product-item" v-for="(product, index) in topProducts" :key="index" @tap="navigateToProduct(product.id)">
            <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <text class="product-sales">销量 {{ product.sales }}件</text>
              <view class="product-price-row">
                <text class="product-price">¥{{ product.price }}</text>
                <view class="product-rank" :class="{ top: index < 3 }">
                  <text class="rank-number">#{{ index + 1 }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';

// 状态变量
const isRefreshing = ref(false);
const currentDateTab = ref('today');

// 日期选项卡
const dateTabs = [
  { label: '今天', value: 'today' },
  { label: '昨天', value: 'yesterday' },
  { label: '7天', value: '7days' },
  { label: '30天', value: '30days' }
];

// 指标数据
const metrics = reactive([
  {
    title: '销售额',
    value: '¥12,486',
    trendValue: '+15.2%',
    trend: 'up',
    type: 'sales'
  },
  {
    title: '订单数',
    value: '186',
    trendValue: '+8.7%',
    trend: 'up',
    type: 'orders'
  },
  {
    title: '访客数',
    value: '2,589',
    trendValue: '+12.4%',
    trend: 'up',
    type: 'customers'
  },
  {
    title: '转化率',
    value: '7.2%',
    trendValue: '-2.1%',
    trend: 'down',
    type: 'conversion'
  }
]);

// 图表Y轴数据
const yAxisValues = ['0', '5k', '10k', '15k', '20k'];

// 图表X轴标签
const xAxisLabels = computed(() => {
  const labels = [];
  const now = new Date();
  
  switch (currentDateTab.value) {
    case 'today':
      return ['9:00', '12:00', '15:00', '18:00', '21:00'];
    case 'yesterday':
      return ['9:00', '12:00', '15:00', '18:00', '21:00'];
    case '7days':
      for (let i = 6; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        labels.push(`${date.getMonth() + 1}/${date.getDate()}`);
      }
      return labels;
    case '30days':
      for (let i = 0; i < 6; i++) {
        const date = new Date(now);
        date.setDate(date.getDate() - i * 5);
        labels.push(`${date.getMonth() + 1}/${date.getDate()}`);
      }
      return labels.reverse();
    default:
      return ['9:00', '12:00', '15:00', '18:00', '21:00'];
  }
});

// 销售额数据
const salesData = reactive([
  { date: '05-01', amount: '2345', value: 30 },
  { date: '05-02', amount: '3521', value: 45 },
  { date: '05-03', amount: '4268', value: 55 },
  { date: '05-04', amount: '6459', value: 82 },
  { date: '05-05', amount: '5384', value: 70 },
  { date: '05-06', amount: '7891', value: 95 },
  { date: '05-07', amount: '6459', value: 82 }
]);

// 订单数数据
const ordersData = reactive([
  { date: '05-01', amount: '32', value: 35 },
  { date: '05-02', amount: '41', value: 42 },
  { date: '05-03', amount: '38', value: 40 },
  { date: '05-04', amount: '52', value: 55 },
  { date: '05-05', amount: '48', value: 50 },
  { date: '05-06', amount: '64', value: 65 },
  { date: '05-07', amount: '58', value: 60 }
]);

// 热门商品数据
const topProducts = reactive([
  {
    id: '1',
    name: '时尚休闲连帽卫衣',
    price: '129',
    sales: '342',
    image: '/static/mock/product1.jpg'
  },
  {
    id: '2',
    name: '高级感羊绒围巾',
    price: '89',
    sales: '256',
    image: '/static/mock/product2.jpg'
  },
  {
    id: '3',
    name: '轻奢风真皮钱包',
    price: '99',
    sales: '198',
    image: '/static/mock/product3.jpg'
  },
  {
    id: '4',
    name: '韩版宽松牛仔裤',
    price: '139',
    sales: '167',
    image: '/static/mock/product4.jpg'
  }
]);

// 日期范围文本
const dateRangeText = computed(() => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  
  switch (currentDateTab.value) {
    case 'today':
      return `${year}-${month}-${day}`;
    case 'yesterday':
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      return `${yesterday.getFullYear()}-${yesterday.getMonth() + 1}-${yesterday.getDate()}`;
    case '7days':
      const sevenDaysAgo = new Date(now);
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6);
      return `${sevenDaysAgo.getFullYear()}-${sevenDaysAgo.getMonth() + 1}-${sevenDaysAgo.getDate()} 至 ${year}-${month}-${day}`;
    case '30days':
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 29);
      return `${thirtyDaysAgo.getFullYear()}-${thirtyDaysAgo.getMonth() + 1}-${thirtyDaysAgo.getDate()} 至 ${year}-${month}-${day}`;
    default:
      return `${year}-${month}-${day}`;
  }
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示更多菜单
const showMore = () => {
  uni.showActionSheet({
    itemList: ['导出数据', '设置时间范围', '帮助中心'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          uni.showToast({ title: '导出数据功能即将上线', icon: 'none' });
          break;
        case 1:
          showDateRangePicker();
          break;
        case 2:
          uni.showToast({ title: '跳转至帮助中心', icon: 'none' });
          break;
      }
    }
  });
};

// 显示日期范围选择器
const showDateRangePicker = () => {
  // 这里可以实现日期范围选择器
  uni.showToast({
    title: '日期范围选择功能开发中',
    icon: 'none'
  });
};

// 切换日期选项卡
const switchDateTab = (tab) => {
  currentDateTab.value = tab;
  
  // 这里可以根据所选日期范围重新加载数据
  uni.showLoading({
    title: '加载中...'
  });
  
  // 模拟加载数据
  setTimeout(() => {
    uni.hideLoading();
  }, 500);
};

// 跳转到详情页
const navigateToDetail = (type) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin/pages/analytics/detail?type=${type}`
  });
};

// 跳转到产品详情
const navigateToProduct = (productId) => {
  uni.navigateTo({
    url: `/subPackages/merchant-admin/pages/products/detail?id=${productId}`
  });
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  
  // 模拟数据刷新
  setTimeout(() => {
    isRefreshing.value = false;
    uni.showToast({
      title: '数据已更新',
      icon: 'none'
    });
  }, 1500);
};

// 生命周期
onMounted(() => {
  // 可以在这里加载初始数据
});
</script>

<style>
/* 页面根样式 */
.analytics-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #5E5CE6, #5AC8FA);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-left {
  display: flex;
  align-items: center;
  width: 40px;
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
}

.navbar-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.title-main {
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: white;
}

.navbar-right {
  width: 40px;
  display: flex;
  justify-content: flex-end;
}

.more-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content-container {
  flex: 1;
  padding: 20px;
  box-sizing: border-box;
  background-color: #f5f5f7;
}

/* 日期选择器 */
.date-range-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 12px;
  padding: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.date-tabs {
  display: flex;
  flex: 1;
}

.date-tab {
  padding: 8px 0;
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #8e8e93;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.date-tab.active {
  background-color: #5E5CE6;
  color: white;
}

.custom-date {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: #5E5CE6;
  font-size: 14px;
  font-weight: 500;
  border-left: 1px solid #f0f0f0;
}

.custom-date-icon {
  margin-right: 4px;
}

.custom-date-text {
  white-space: nowrap;
}

/* 概览卡片 */
.overview-card {
  background-color: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  margin-bottom: 20px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.overview-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
}

.overview-period {
  font-size: 14px;
  color: #8e8e93;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.metric-card {
  background-color: #f5f5f7;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:active {
  transform: scale(0.98);
  background-color: #f0f0f0;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #5E5CE6;
  opacity: 0.3;
}

.metric-card:nth-child(1)::before {
  background: #5E5CE6;
}

.metric-card:nth-child(2)::before {
  background: #30D158;
}

.metric-card:nth-child(3)::before {
  background: #FF9F0A;
}

.metric-card:nth-child(4)::before {
  background: #FF453A;
}

.metric-content {
  position: relative;
  z-index: 1;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.metric-title {
  font-size: 14px;
  color: #8e8e93;
  font-weight: 500;
}

.metric-icon {
  width: 16px;
  height: 16px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  display: block;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
}

.metric-trend.up {
  color: #30D158;
}

.metric-trend.down {
  color: #FF453A;
}

.trend-icon {
  margin-right: 4px;
}

.trend-text {
  font-size: 14px;
  font-weight: 500;
}

/* 销售趋势图表 */
.chart-card {
  background-color: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
}

.chart-legend {
  display: flex;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  margin-right: 8px;
}

.legend-text {
  font-size: 14px;
  font-weight: 500;
  color: #8e8e93;
}

.chart-container {
  margin-bottom: 20px;
}

.mock-chart {
  position: relative;
  height: 200px;
}

.chart-y-axis {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 100%;
}

.y-axis-label {
  display: block;
  text-align: right;
  padding: 4px 8px;
  font-size: 12px;
  color: #8e8e93;
}

.chart-area {
  position: absolute;
  top: 0;
  left: 40px;
  right: 0;
  bottom: 0;
}

.line-chart {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.sales-line {
  stroke: #5E5CE6;
}

.orders-line {
  stroke: #30D158;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 40px;
  right: 0;
  bottom: 0;
}

.grid-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #f0f0f0;
}

.chart-x-axis {
  position: absolute;
  bottom: 0;
  left: 40px;
  right: 0;
  height: 20px;
}

.x-axis-label {
  display: block;
  text-align: center;
  padding: 4px 8px;
  font-size: 12px;
  color: #8e8e93;
}

.chart-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #5E5CE6;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-button:hover {
  background-color: #4A45E5;
}

.action-text {
  margin-right: 8px;
}

/* 热门商品 */
.products-card {
  background-color: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.products-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
}

.more-link {
  display: flex;
  align-items: center;
  color: #8e8e93;
  font-size: 14px;
  font-weight: 500;
}

.more-text {
  margin-right: 8px;
}

.product-list {
  display: flex;
  flex-wrap: wrap;
}

.product-item {
  width: 50%;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 16px;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
}

.product-sales {
  font-size: 12px;
  color: #8e8e93;
}

.product-price-row {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.product-price {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
}

.product-rank {
  background-color: #5E5CE6;
  border-radius: 4px;
  padding: 2px 4px;
  margin-left: 8px;
  color: white;
}

.top {
  background-color: #30D158;
}
</style> 