"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const _sfc_main = {
  __name: "driver-profile",
  setup(__props, { expose: __expose }) {
    const statusBarHeight = common_vendor.ref(20);
    const driverInfo = common_vendor.ref({
      id: "**********",
      name: "张先生",
      avatar: "/static/images/avatar/user1.png",
      isVerified: true,
      level: "金牌司机",
      tripCount: 386,
      serviceYears: 2.5,
      rating: 4.9,
      tags: ["驾驶平稳", "很准时", "路线熟悉", "热心服务"],
      introduction: "您好，我是一名有着5年驾龄的老司机，熟悉磁县及周边地区的道路情况。我的车辆定期保养，保证舒适安全的乘车体验。欢迎选择我的拼车服务，我会尽力为您提供最好的出行体验。",
      vehicle: {
        brand: "大众",
        model: "帕萨特",
        plateNumber: "冀E·12345",
        color: "白色",
        seats: 5,
        image: "/static/images/vehicles/car1.jpg"
      },
      serviceStats: {
        totalTrips: 386,
        totalHours: 720,
        totalDistance: 15680,
        positiveRate: 98
      },
      serviceAreas: [
        "磁县城区",
        "邯郸市区",
        "磁县-邯郸",
        "磁县-石家庄",
        "磁县-邢台"
      ]
    });
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 20;
      loadDriverInfo();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const loadDriverInfo = () => {
      common_vendor.index.__f__("log", "at carpool-package/pages/carpool/my/driver-profile.vue:214", "加载司机信息");
    };
    const navigateToPage = (page) => {
      if (page === "vehicle-detail") {
        common_vendor.index.navigateTo({
          url: "/carpool-package/pages/carpool/my/vehicle-detail"
        });
      }
    };
    const contactDriver = () => {
      common_vendor.index.showModal({
        title: "联系司机",
        content: "是否拨打司机电话？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.makePhoneCall({
              phoneNumber: "13812345678",
              fail: () => {
                common_vendor.index.showToast({
                  title: "拨打电话失败",
                  icon: "none"
                });
              }
            });
          }
        }
      });
    };
    __expose({});
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: driverInfo.value.avatar,
        e: driverInfo.value.isVerified
      }, driverInfo.value.isVerified ? {
        f: common_assets._imports_1$38
      } : {}, {
        g: common_vendor.t(driverInfo.value.name),
        h: common_assets._imports_2$34,
        i: common_vendor.t(driverInfo.value.level),
        j: common_vendor.t(driverInfo.value.id),
        k: common_vendor.t(driverInfo.value.tripCount),
        l: common_vendor.t(driverInfo.value.serviceYears),
        m: common_vendor.t(driverInfo.value.rating),
        n: common_vendor.f(driverInfo.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        o: common_vendor.t(driverInfo.value.introduction),
        p: statusBarHeight.value + 44 + "px",
        q: common_assets._imports_3$31,
        r: common_vendor.o(($event) => navigateToPage("vehicle-detail")),
        s: driverInfo.value.vehicle.image,
        t: common_vendor.t(driverInfo.value.vehicle.brand),
        v: common_vendor.t(driverInfo.value.vehicle.model),
        w: common_vendor.t(driverInfo.value.vehicle.plateNumber),
        x: common_vendor.t(driverInfo.value.vehicle.color),
        y: common_vendor.t(driverInfo.value.vehicle.seats),
        z: common_assets._imports_4$19,
        A: common_vendor.t(driverInfo.value.serviceStats.totalTrips),
        B: common_vendor.t(driverInfo.value.serviceStats.totalHours),
        C: common_vendor.t(driverInfo.value.serviceStats.totalDistance),
        D: common_vendor.t(driverInfo.value.serviceStats.positiveRate),
        E: common_assets._imports_5$18,
        F: common_vendor.f(driverInfo.value.serviceAreas, (area, index, i0) => {
          return {
            a: common_vendor.t(area),
            b: index
          };
        }),
        G: common_assets._imports_6$16,
        H: common_vendor.o(contactDriver)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/carpool-package/pages/carpool/my/driver-profile.js.map
