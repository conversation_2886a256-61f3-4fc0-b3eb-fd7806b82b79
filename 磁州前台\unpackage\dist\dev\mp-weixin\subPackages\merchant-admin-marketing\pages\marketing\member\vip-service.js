"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 专属客服服务设置
      serviceSettings: {
        enabled: true,
        serviceTypes: ["online", "phone", "wechat"],
        priority: "high",
        responseTime: 5,
        fullTimeService: false,
        description: "会员专享一对一客服服务，为您提供更快速、更专业、更贴心的服务体验。"
      },
      // 客服类型选项
      serviceTypes: [
        { label: "在线客服", value: "online" },
        { label: "电话客服", value: "phone" },
        { label: "微信客服", value: "wechat" },
        { label: "上门服务", value: "visit" }
      ],
      // 优先级选项
      priorityOptions: [
        { label: "普通", value: "normal" },
        { label: "优先", value: "medium" },
        { label: "最高", value: "high" }
      ],
      // 客服人员列表
      staffList: [
        {
          id: 1,
          name: "张小美",
          avatar: "/static/images/avatar-1.png",
          position: "高级客服专员",
          phone: "13800138001",
          wechat: "cs_xiaomei",
          active: true
        },
        {
          id: 2,
          name: "李大壮",
          avatar: "/static/images/avatar-2.png",
          position: "客服主管",
          phone: "13800138002",
          wechat: "cs_dazhuang",
          active: true
        },
        {
          id: 3,
          name: "王晓丽",
          avatar: "/static/images/avatar-3.png",
          position: "资深客服专员",
          phone: "13800138003",
          wechat: "cs_xiaoli",
          active: false
        }
      ],
      // 会员等级
      memberLevels: [
        {
          id: 1,
          name: "普通会员",
          memberCount: 2156,
          selected: false
        },
        {
          id: 2,
          name: "银卡会员",
          memberCount: 864,
          selected: false
        },
        {
          id: 3,
          name: "金卡会员",
          memberCount: 426,
          selected: true
        },
        {
          id: 4,
          name: "钻石会员",
          memberCount: 116,
          selected: true
        }
      ],
      // 客服表单
      staffForm: {
        id: "",
        name: "",
        avatar: "",
        position: "",
        phone: "",
        wechat: "",
        active: true
      },
      isEditing: false,
      currentStaffId: null
    };
  },
  methods: {
    // 切换专属客服服务
    toggleVipService(e) {
      this.serviceSettings.enabled = e.detail.value;
    },
    // 检查服务类型是否选中
    isServiceTypeSelected(type) {
      return this.serviceSettings.serviceTypes.includes(type);
    },
    // 切换服务类型
    toggleServiceType(type) {
      const index = this.serviceSettings.serviceTypes.indexOf(type);
      if (index === -1) {
        this.serviceSettings.serviceTypes.push(type);
      } else {
        this.serviceSettings.serviceTypes.splice(index, 1);
      }
    },
    // 设置优先级
    setPriority(priority) {
      this.serviceSettings.priority = priority;
    },
    // 切换全时服务
    toggleFullTimeService(e) {
      this.serviceSettings.fullTimeService = e.detail.value;
    },
    // 切换会员等级
    toggleLevel(level) {
      const index = this.memberLevels.findIndex((item) => item.id === level.id);
      if (index !== -1) {
        this.memberLevels[index].selected = !this.memberLevels[index].selected;
      }
    },
    // 显示添加客服弹窗
    showAddStaffModal() {
      this.isEditing = false;
      this.staffForm = {
        id: "",
        name: "",
        avatar: "",
        position: "",
        phone: "",
        wechat: "",
        active: true
      };
      this.$refs.staffFormPopup.open();
    },
    // 编辑客服
    editStaff(staff) {
      this.isEditing = true;
      this.currentStaffId = staff.id;
      this.staffForm = JSON.parse(JSON.stringify(staff));
      this.$refs.staffFormPopup.open();
    },
    // 关闭客服表单弹窗
    closeStaffModal() {
      this.$refs.staffFormPopup.close();
    },
    // 切换客服状态
    toggleStaffStatus(e) {
      this.staffForm.active = e.detail.value;
    },
    // 选择头像
    chooseAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          this.staffForm.avatar = res.tempFilePaths[0];
        }
      });
    },
    // 保存客服表单
    saveStaffForm() {
      if (!this.staffForm.name) {
        common_vendor.index.showToast({
          title: "请输入客服姓名",
          icon: "none"
        });
        return;
      }
      if (!this.staffForm.position) {
        common_vendor.index.showToast({
          title: "请输入客服职位",
          icon: "none"
        });
        return;
      }
      if (this.isEditing) {
        const index = this.staffList.findIndex((item) => item.id === this.currentStaffId);
        if (index !== -1) {
          this.staffList.splice(index, 1, JSON.parse(JSON.stringify(this.staffForm)));
        }
      } else {
        this.staffForm.id = Date.now();
        this.staffList.push(JSON.parse(JSON.stringify(this.staffForm)));
      }
      this.closeStaffModal();
      common_vendor.index.showToast({
        title: this.isEditing ? "客服修改成功" : "客服添加成功"
      });
    },
    // 确认删除客服
    confirmDeleteStaff(staff) {
      this.currentStaffId = staff.id;
      this.$refs.deleteConfirmPopup.open();
    },
    // 删除客服
    deleteStaff() {
      const index = this.staffList.findIndex((item) => item.id === this.currentStaffId);
      if (index !== -1) {
        this.staffList.splice(index, 1);
      }
      this.$refs.deleteConfirmPopup.close();
      common_vendor.index.showToast({
        title: "客服删除成功"
      });
    },
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    // 保存设置
    saveSettings() {
      common_vendor.index.showToast({
        title: "设置保存成功",
        icon: "success"
      });
    }
  }
};
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  const _component_uni_popup_dialog = common_vendor.resolveComponent("uni-popup-dialog");
  (_component_uni_popup + _component_uni_popup_dialog)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.serviceSettings.enabled,
    b: common_vendor.o((...args) => $options.toggleVipService && $options.toggleVipService(...args)),
    c: $data.serviceSettings.enabled
  }, $data.serviceSettings.enabled ? {
    d: common_vendor.f($data.serviceTypes, (type, index, i0) => {
      return common_vendor.e({
        a: $options.isServiceTypeSelected(type.value)
      }, $options.isServiceTypeSelected(type.value) ? {} : {}, {
        b: common_vendor.t(type.label),
        c: index,
        d: $options.isServiceTypeSelected(type.value) ? 1 : "",
        e: common_vendor.o(($event) => $options.toggleServiceType(type.value), index)
      });
    }),
    e: common_vendor.f($data.priorityOptions, (priority, index, i0) => {
      return {
        a: common_vendor.t(priority.label),
        b: index,
        c: $data.serviceSettings.priority === priority.value ? 1 : "",
        d: common_vendor.o(($event) => $options.setPriority(priority.value), index)
      };
    }),
    f: $data.serviceSettings.responseTime,
    g: common_vendor.o(($event) => $data.serviceSettings.responseTime = $event.detail.value),
    h: $data.serviceSettings.fullTimeService,
    i: common_vendor.o((...args) => $options.toggleFullTimeService && $options.toggleFullTimeService(...args)),
    j: common_vendor.f($data.staffList, (staff, index, i0) => {
      return {
        a: staff.avatar,
        b: common_vendor.t(staff.name),
        c: common_vendor.t(staff.position),
        d: common_vendor.t(staff.active ? "在线" : "离线"),
        e: staff.active ? 1 : "",
        f: common_vendor.o(($event) => $options.editStaff(staff), index),
        g: common_vendor.o(($event) => $options.confirmDeleteStaff(staff), index),
        h: index
      };
    }),
    k: common_vendor.o((...args) => $options.showAddStaffModal && $options.showAddStaffModal(...args)),
    l: common_vendor.f($data.memberLevels, (level, index, i0) => {
      return common_vendor.e({
        a: level.selected
      }, level.selected ? {} : {}, {
        b: level.selected ? 1 : "",
        c: common_vendor.o(($event) => $options.toggleLevel(level), index),
        d: common_vendor.t(level.name),
        e: common_vendor.t(level.memberCount),
        f: index
      });
    }),
    m: $data.serviceSettings.description,
    n: common_vendor.o(($event) => $data.serviceSettings.description = $event.detail.value),
    o: common_vendor.o((...args) => $options.saveSettings && $options.saveSettings(...args))
  } : {}, {
    p: common_vendor.t($data.isEditing ? "编辑客服" : "添加客服"),
    q: common_vendor.o((...args) => $options.closeStaffModal && $options.closeStaffModal(...args)),
    r: $data.staffForm.name,
    s: common_vendor.o(($event) => $data.staffForm.name = $event.detail.value),
    t: $data.staffForm.avatar
  }, $data.staffForm.avatar ? {
    v: $data.staffForm.avatar
  } : {
    w: common_vendor.o((...args) => $options.chooseAvatar && $options.chooseAvatar(...args))
  }, {
    x: $data.staffForm.position,
    y: common_vendor.o(($event) => $data.staffForm.position = $event.detail.value),
    z: $data.staffForm.phone,
    A: common_vendor.o(($event) => $data.staffForm.phone = $event.detail.value),
    B: $data.staffForm.wechat,
    C: common_vendor.o(($event) => $data.staffForm.wechat = $event.detail.value),
    D: $data.staffForm.active,
    E: common_vendor.o((...args) => $options.toggleStaffStatus && $options.toggleStaffStatus(...args)),
    F: common_vendor.o((...args) => $options.closeStaffModal && $options.closeStaffModal(...args)),
    G: common_vendor.o((...args) => $options.saveStaffForm && $options.saveStaffForm(...args)),
    H: common_vendor.sr("staffFormPopup", "a6b86a0e-0"),
    I: common_vendor.p({
      type: "center"
    }),
    J: common_vendor.o($options.deleteStaff),
    K: common_vendor.o($options.closeDeleteConfirm),
    L: common_vendor.p({
      type: "warning",
      title: "删除确认",
      content: "确定要删除该客服人员吗？删除后将无法恢复。",
      ["before-close"]: true
    }),
    M: common_vendor.sr("deleteConfirmPopup", "a6b86a0e-1"),
    N: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a6b86a0e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/vip-service.js.map
