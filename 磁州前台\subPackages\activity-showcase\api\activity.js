/**
 * 磁州前台活动API服务
 * 提供活动展示、参与、评论等功能的API接口
 */

import { request } from '../utils/request'

// API基础配置
const API_BASE = '/api/frontend/activities'

/**
 * 活动API服务类
 */
class ActivityApiService {
  /**
   * 获取活动列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.size 每页数量
   * @param {string} params.type 活动类型
   * @param {boolean} params.isRecommended 是否推荐
   * @param {Array} params.tags 标签数组
   * @returns {Promise} 活动列表数据
   */
  async getList(params = {}) {
    try {
      const response = await request({
        url: API_BASE,
        method: 'GET',
        data: {
          page: 1,
          size: 10,
          status: 'active', // 前台只显示进行中的活动
          ...params
        }
      })
      return response
    } catch (error) {
      console.error('获取活动列表失败:', error)
      throw error
    }
  }

  /**
   * 获取推荐活动列表
   * @param {number} limit 限制数量
   * @returns {Promise} 推荐活动列表
   */
  async getRecommended(limit = 5) {
    try {
      const response = await request({
        url: `${API_BASE}/recommended`,
        method: 'GET',
        data: { limit }
      })
      return response
    } catch (error) {
      console.error('获取推荐活动失败:', error)
      throw error
    }
  }

  /**
   * 获取热门活动列表
   * @param {number} limit 限制数量
   * @returns {Promise} 热门活动列表
   */
  async getPopular(limit = 5) {
    try {
      const response = await request({
        url: `${API_BASE}/popular`,
        method: 'GET',
        data: { limit }
      })
      return response
    } catch (error) {
      console.error('获取热门活动失败:', error)
      throw error
    }
  }

  /**
   * 获取活动详情
   * @param {number} id 活动ID
   * @returns {Promise} 活动详情数据
   */
  async getDetail(id) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}`,
        method: 'GET'
      })
      
      // 记录浏览量
      this.recordView(id).catch(err => {
        console.warn('记录浏览量失败:', err)
      })
      
      return response
    } catch (error) {
      console.error('获取活动详情失败:', error)
      throw error
    }
  }

  /**
   * 搜索活动
   * @param {string} keyword 搜索关键词
   * @param {Object} params 其他查询参数
   * @returns {Promise} 搜索结果
   */
  async search(keyword, params = {}) {
    try {
      const response = await request({
        url: `${API_BASE}/search`,
        method: 'GET',
        data: {
          keyword,
          page: 1,
          size: 10,
          ...params
        }
      })
      return response
    } catch (error) {
      console.error('搜索活动失败:', error)
      throw error
    }
  }

  /**
   * 参与活动
   * @param {number} id 活动ID
   * @param {Object} data 参与信息
   * @param {string} data.name 姓名
   * @param {string} data.phone 手机号
   * @param {string} data.message 留言
   * @returns {Promise} 参与结果
   */
  async join(id, data = {}) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}/join`,
        method: 'POST',
        data
      })
      
      uni.showToast({
        title: '报名成功',
        icon: 'success'
      })
      
      return response
    } catch (error) {
      console.error('参与活动失败:', error)
      uni.showToast({
        title: error.message || '报名失败',
        icon: 'none'
      })
      throw error
    }
  }

  /**
   * 取消参与活动
   * @param {number} id 活动ID
   * @returns {Promise} 取消结果
   */
  async leave(id) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}/leave`,
        method: 'POST'
      })
      
      uni.showToast({
        title: '已取消报名',
        icon: 'success'
      })
      
      return response
    } catch (error) {
      console.error('取消参与失败:', error)
      uni.showToast({
        title: error.message || '取消失败',
        icon: 'none'
      })
      throw error
    }
  }

  /**
   * 分享活动
   * @param {number} id 活动ID
   * @returns {Promise} 分享结果
   */
  async share(id) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}/share`,
        method: 'POST'
      })
      return response
    } catch (error) {
      console.error('分享活动失败:', error)
      throw error
    }
  }

  /**
   * 点赞活动
   * @param {number} id 活动ID
   * @returns {Promise} 点赞结果
   */
  async like(id) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}/like`,
        method: 'POST'
      })
      return response
    } catch (error) {
      console.error('点赞失败:', error)
      throw error
    }
  }

  /**
   * 取消点赞活动
   * @param {number} id 活动ID
   * @returns {Promise} 取消点赞结果
   */
  async unlike(id) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}/unlike`,
        method: 'POST'
      })
      return response
    } catch (error) {
      console.error('取消点赞失败:', error)
      throw error
    }
  }

  /**
   * 评论活动
   * @param {number} id 活动ID
   * @param {string} content 评论内容
   * @returns {Promise} 评论结果
   */
  async comment(id, content) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}/comment`,
        method: 'POST',
        data: { content }
      })
      
      uni.showToast({
        title: '评论成功',
        icon: 'success'
      })
      
      return response
    } catch (error) {
      console.error('评论失败:', error)
      uni.showToast({
        title: error.message || '评论失败',
        icon: 'none'
      })
      throw error
    }
  }

  /**
   * 获取活动评论列表
   * @param {number} id 活动ID
   * @param {Object} params 查询参数
   * @returns {Promise} 评论列表
   */
  async getComments(id, params = {}) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}/comments`,
        method: 'GET',
        data: {
          page: 1,
          size: 20,
          ...params
        }
      })
      return response
    } catch (error) {
      console.error('获取评论失败:', error)
      throw error
    }
  }

  /**
   * 获取用户参与的活动
   * @param {Object} params 查询参数
   * @returns {Promise} 用户活动列表
   */
  async getUserActivities(params = {}) {
    try {
      const response = await request({
        url: '/api/frontend/user/activities',
        method: 'GET',
        data: {
          page: 1,
          size: 10,
          ...params
        }
      })
      return response
    } catch (error) {
      console.error('获取用户活动失败:', error)
      throw error
    }
  }

  /**
   * 记录活动浏览量
   * @param {number} id 活动ID
   * @returns {Promise} 记录结果
   */
  async recordView(id) {
    try {
      // 防止重复记录，使用本地存储记录已浏览的活动
      const viewedKey = `activity_viewed_${id}`
      const lastViewTime = uni.getStorageSync(viewedKey)
      const now = Date.now()
      
      // 如果距离上次浏览超过30分钟，才记录新的浏览量
      if (!lastViewTime || (now - lastViewTime) > 30 * 60 * 1000) {
        await request({
          url: `${API_BASE}/${id}/view`,
          method: 'POST'
        })
        
        uni.setStorageSync(viewedKey, now)
      }
    } catch (error) {
      console.error('记录浏览量失败:', error)
      // 不抛出错误，避免影响主要功能
    }
  }

  /**
   * 获取活动标签
   * @returns {Promise} 标签列表
   */
  async getTags() {
    try {
      const response = await request({
        url: `${API_BASE}/tags`,
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取标签失败:', error)
      throw error
    }
  }

  /**
   * 检查用户是否已参与活动
   * @param {number} id 活动ID
   * @returns {Promise} 参与状态
   */
  async checkJoinStatus(id) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}/join-status`,
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('检查参与状态失败:', error)
      return { isJoined: false }
    }
  }

  /**
   * 获取活动统计信息
   * @param {number} id 活动ID
   * @returns {Promise} 统计信息
   */
  async getStatistics(id) {
    try {
      const response = await request({
        url: `${API_BASE}/${id}/statistics`,
        method: 'GET'
      })
      return response
    } catch (error) {
      console.error('获取统计信息失败:', error)
      return {
        viewCount: 0,
        participantCount: 0,
        shareCount: 0,
        commentCount: 0
      }
    }
  }
}

// 创建API服务实例
const activityApi = new ActivityApiService()

// 导出API方法
export const {
  getList,
  getRecommended,
  getPopular,
  getDetail,
  search,
  join,
  leave,
  share,
  like,
  unlike,
  comment,
  getComments,
  getUserActivities,
  recordView,
  getTags,
  checkJoinStatus,
  getStatistics
} = activityApi

// 默认导出
export default activityApi
