{"version": 3, "file": "detail.js", "sources": ["subPackages/merchant-admin-order/pages/orders/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tb3JkZXJccGFnZXNcb3JkZXJzXGRldGFpbC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"order-detail-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">订单详情</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"more-icon\">⋮</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 订单状态卡片 -->\r\n    <view class=\"status-card\" :class=\"'status-' + orderData.status\">\r\n      <view class=\"status-header\">\r\n        <text class=\"status-text\">{{getStatusText(orderData.status)}}</text>\r\n        <text class=\"status-desc\">{{getStatusDescription(orderData.status)}}</text>\r\n      </view>\r\n      <view class=\"status-timeline\">\r\n        <view \r\n          v-for=\"(step, index) in orderTimeline\" \r\n          :key=\"index\" \r\n          class=\"timeline-item\"\r\n          :class=\"{'completed': step.completed, 'current': step.current}\">\r\n          <view class=\"timeline-dot\"></view>\r\n          <view class=\"timeline-content\">\r\n            <text class=\"timeline-title\">{{step.title}}</text>\r\n            <text class=\"timeline-time\">{{step.time}}</text>\r\n          </view>\r\n          <view v-if=\"index < orderTimeline.length - 1\" class=\"timeline-line\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 订单基本信息 -->\r\n    <view class=\"info-card\">\r\n      <view class=\"card-title\">基本信息</view>\r\n      <view class=\"info-item\">\r\n        <text class=\"info-label\">订单编号</text>\r\n        <text class=\"info-value\">{{orderData.orderNo}}</text>\r\n      </view>\r\n      <view class=\"info-item\">\r\n        <text class=\"info-label\">下单时间</text>\r\n        <text class=\"info-value\">{{orderData.createTime}}</text>\r\n      </view>\r\n      <view class=\"info-item\">\r\n        <text class=\"info-label\">支付方式</text>\r\n        <text class=\"info-value\">{{orderData.paymentMethod}}</text>\r\n      </view>\r\n      <view class=\"info-item\">\r\n        <text class=\"info-label\">支付状态</text>\r\n        <text class=\"info-value\" :class=\"'payment-' + orderData.paymentStatus\">{{orderData.paymentStatus === 'paid' ? '已支付' : '待支付'}}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 客户信息 -->\r\n    <view class=\"info-card\">\r\n      <view class=\"card-title\">客户信息</view>\r\n      <view class=\"customer-profile\">\r\n        <image class=\"customer-avatar\" :src=\"orderData.customer.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\r\n        <view class=\"customer-info\">\r\n          <text class=\"customer-name\">{{orderData.customer.name}}</text>\r\n          <text class=\"customer-id\">ID: {{orderData.customer.id}}</text>\r\n        </view>\r\n        <view class=\"contact-btn\" @click=\"contactCustomer\">联系客户</view>\r\n      </view>\r\n      <view v-if=\"orderData.deliveryType === 'delivery'\" class=\"address-info\">\r\n        <view class=\"address-title\">\r\n          <text class=\"address-icon\">📍</text>\r\n          <text>收货地址</text>\r\n        </view>\r\n        <view class=\"address-content\">\r\n          <text class=\"address-name\">{{orderData.address.name}} {{orderData.address.phone}}</text>\r\n          <text class=\"address-detail\">{{orderData.address.province}}{{orderData.address.city}}{{orderData.address.district}}{{orderData.address.detail}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品信息 -->\r\n    <view class=\"info-card\">\r\n      <view class=\"card-title\">商品信息</view>\r\n      <view \r\n        v-for=\"(item, index) in orderData.items\" \r\n        :key=\"index\"\r\n        class=\"product-item\">\r\n        <image class=\"product-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n        <view class=\"product-info\">\r\n          <text class=\"product-name\">{{item.name}}</text>\r\n          <text class=\"product-spec\">{{item.spec}}</text>\r\n          <view class=\"product-price-wrap\">\r\n            <text class=\"product-price\">¥{{item.price}}</text>\r\n            <text class=\"product-quantity\">x{{item.quantity}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 金额信息 -->\r\n      <view class=\"amount-info\">\r\n        <view class=\"amount-item\">\r\n          <text class=\"amount-label\">商品总价</text>\r\n          <text class=\"amount-value\">¥{{orderData.productTotal}}</text>\r\n        </view>\r\n        <view class=\"amount-item\">\r\n          <text class=\"amount-label\">配送费</text>\r\n          <text class=\"amount-value\">¥{{orderData.deliveryFee}}</text>\r\n        </view>\r\n        <view v-if=\"orderData.discount > 0\" class=\"amount-item discount\">\r\n          <text class=\"amount-label\">优惠金额</text>\r\n          <text class=\"amount-value\">-¥{{orderData.discount}}</text>\r\n        </view>\r\n        <view class=\"amount-item total\">\r\n          <text class=\"amount-label\">订单总价</text>\r\n          <text class=\"amount-value\">¥{{orderData.totalAmount}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 订单备注 -->\r\n    <view v-if=\"orderData.remark\" class=\"info-card\">\r\n      <view class=\"card-title\">订单备注</view>\r\n      <view class=\"remark-content\">\r\n        {{orderData.remark}}\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 订单日志 -->\r\n    <view class=\"info-card\">\r\n      <view class=\"card-title\">订单日志</view>\r\n      <view \r\n        v-for=\"(log, index) in orderData.logs\" \r\n        :key=\"index\"\r\n        class=\"log-item\">\r\n        <text class=\"log-time\">{{log.time}}</text>\r\n        <text class=\"log-content\">{{log.content}}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部操作栏 -->\r\n    <view class=\"action-bar\">\r\n      <view v-if=\"orderData.status === 'pending'\" class=\"action-btn primary\" @click=\"processOrder\">接单处理</view>\r\n      <view v-if=\"orderData.status === 'processing'\" class=\"action-btn primary\" @click=\"completeOrder\">完成订单</view>\r\n      <view v-if=\"['pending', 'processing'].includes(orderData.status)\" class=\"action-btn\" @click=\"cancelOrder\">取消订单</view>\r\n      <view class=\"action-btn\" @click=\"printOrder\">打印订单</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      orderId: '',\r\n      orderData: {\r\n        id: '1001',\r\n        orderNo: 'CZ20230501001',\r\n        status: 'pending', // pending, processing, completed, cancelled, refunding\r\n        createTime: '2023-05-01 10:30:25',\r\n        paymentMethod: '微信支付',\r\n        paymentStatus: 'paid', // paid, unpaid\r\n        deliveryType: 'delivery', // delivery, self-pickup\r\n        productTotal: '118.00',\r\n        deliveryFee: '10.00',\r\n        discount: '0.00',\r\n        totalAmount: '128.00',\r\n        remark: '请尽快发货，谢谢！',\r\n        customer: {\r\n          id: '10086',\r\n          name: '张三',\r\n          avatar: '/static/images/default-avatar.png',\r\n          phone: '13800138000'\r\n        },\r\n        address: {\r\n          name: '张三',\r\n          phone: '13800138000',\r\n          province: '河北省',\r\n          city: '邯郸市',\r\n          district: '磁县',\r\n          detail: '北辰小区3号楼2单元101'\r\n        },\r\n        items: [\r\n          {\r\n            id: '2001',\r\n            name: '精品水果礼盒',\r\n            spec: '3kg装',\r\n            price: '88.00',\r\n            quantity: 1,\r\n            image: '/static/images/product-1.jpg'\r\n          },\r\n          {\r\n            id: '2002',\r\n            name: '有机蔬菜套餐',\r\n            spec: '标准版',\r\n            price: '30.00',\r\n            quantity: 1,\r\n            image: '/static/images/product-2.jpg'\r\n          }\r\n        ],\r\n        logs: [\r\n          {\r\n            time: '2023-05-01 10:30:25',\r\n            content: '用户下单成功'\r\n          },\r\n          {\r\n            time: '2023-05-01 10:31:05',\r\n            content: '用户完成支付'\r\n          }\r\n        ]\r\n      },\r\n      orderTimeline: [\r\n        {\r\n          title: '下单',\r\n          time: '2023-05-01 10:30',\r\n          completed: true,\r\n          current: false\r\n        },\r\n        {\r\n          title: '支付',\r\n          time: '2023-05-01 10:31',\r\n          completed: true,\r\n          current: false\r\n        },\r\n        {\r\n          title: '商家接单',\r\n          time: '',\r\n          completed: false,\r\n          current: true\r\n        },\r\n        {\r\n          title: '配送中',\r\n          time: '',\r\n          completed: false,\r\n          current: false\r\n        },\r\n        {\r\n          title: '已完成',\r\n          time: '',\r\n          completed: false,\r\n          current: false\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    getStatusText(status) {\r\n      const texts = {\r\n        pending: '待处理',\r\n        processing: '处理中',\r\n        completed: '已完成',\r\n        cancelled: '已取消',\r\n        refunding: '退款中'\r\n      };\r\n      return texts[status] || '未知状态';\r\n    },\r\n    getStatusDescription(status) {\r\n      const descriptions = {\r\n        pending: '订单已提交，等待商家处理',\r\n        processing: '商家正在处理订单',\r\n        completed: '订单已完成',\r\n        cancelled: '订单已取消',\r\n        refunding: '订单正在申请退款'\r\n      };\r\n      return descriptions[status] || '';\r\n    },\r\n    contactCustomer() {\r\n      uni.showActionSheet({\r\n        itemList: ['拨打电话', '发送消息'],\r\n        success: (res) => {\r\n          if (res.tapIndex === 0) {\r\n            // 拨打电话\r\n            uni.makePhoneCall({\r\n              phoneNumber: this.orderData.customer.phone,\r\n              fail: () => {\r\n                uni.showToast({\r\n                  title: '拨打电话失败',\r\n                  icon: 'none'\r\n                });\r\n              }\r\n            });\r\n          } else if (res.tapIndex === 1) {\r\n            // 发送消息\r\n            uni.showToast({\r\n              title: '消息功能开发中',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    processOrder() {\r\n      uni.showModal({\r\n        title: '确认接单',\r\n        content: '接单后将开始处理该订单，是否继续？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 更新订单状态\r\n            this.orderData.status = 'processing';\r\n            this.orderData.logs.push({\r\n              time: this.getCurrentTime(),\r\n              content: '商家已接单'\r\n            });\r\n            \r\n            // 更新时间线\r\n            this.orderTimeline[2].completed = true;\r\n            this.orderTimeline[2].current = false;\r\n            this.orderTimeline[2].time = this.getCurrentTime();\r\n            this.orderTimeline[3].current = true;\r\n            \r\n            uni.showToast({\r\n              title: '接单成功',\r\n              icon: 'success'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    completeOrder() {\r\n      uni.showModal({\r\n        title: '确认完成',\r\n        content: '确认订单已完成？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 更新订单状态\r\n            this.orderData.status = 'completed';\r\n            this.orderData.logs.push({\r\n              time: this.getCurrentTime(),\r\n              content: '订单已完成'\r\n            });\r\n            \r\n            // 更新时间线\r\n            this.orderTimeline[3].completed = true;\r\n            this.orderTimeline[3].current = false;\r\n            this.orderTimeline[3].time = this.getCurrentTime();\r\n            this.orderTimeline[4].completed = true;\r\n            this.orderTimeline[4].current = true;\r\n            this.orderTimeline[4].time = this.getCurrentTime();\r\n            \r\n            uni.showToast({\r\n              title: '订单已完成',\r\n              icon: 'success'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    cancelOrder() {\r\n      uni.showModal({\r\n        title: '取消订单',\r\n        content: '确认取消该订单？取消后将无法恢复',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 更新订单状态\r\n            this.orderData.status = 'cancelled';\r\n            this.orderData.logs.push({\r\n              time: this.getCurrentTime(),\r\n              content: '商家取消订单'\r\n            });\r\n            \r\n            uni.showToast({\r\n              title: '订单已取消',\r\n              icon: 'success'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    printOrder() {\r\n      uni.showToast({\r\n        title: '打印功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    getCurrentTime() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      const day = String(now.getDate()).padStart(2, '0');\r\n      const hour = String(now.getHours()).padStart(2, '0');\r\n      const minute = String(now.getMinutes()).padStart(2, '0');\r\n      const second = String(now.getSeconds()).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;\r\n    },\r\n    loadOrderData() {\r\n      // 实际项目中，这里应该从服务器获取订单数据\r\n      console.log('加载订单数据，ID:', this.orderId);\r\n      // 模拟异步加载\r\n      setTimeout(() => {\r\n        // 数据已经在data中初始化了\r\n      }, 500);\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    this.orderId = options.id || '';\r\n    this.loadOrderData();\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.order-detail-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 80px; /* 为底部操作栏留出空间 */\r\n}\r\n\r\n.navbar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 44px 16px 10px;\r\n  background: linear-gradient(135deg, #1677FF, #065DD2);\r\n  position: relative;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-back {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  color: #fff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.more-icon {\r\n  color: #fff;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-card {\r\n  margin: 16px;\r\n  border-radius: 8px;\r\n  background-color: #fff;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n  overflow: hidden;\r\n}\r\n\r\n.status-pending {\r\n  border-top: 4px solid #FF9800;\r\n}\r\n\r\n.status-processing {\r\n  border-top: 4px solid #2196F3;\r\n}\r\n\r\n.status-completed {\r\n  border-top: 4px solid #4CAF50;\r\n}\r\n\r\n.status-cancelled {\r\n  border-top: 4px solid #9E9E9E;\r\n}\r\n\r\n.status-refunding {\r\n  border-top: 4px solid #F44336;\r\n}\r\n\r\n.status-header {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 4px;\r\n  display: block;\r\n}\r\n\r\n.status-desc {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.status-timeline {\r\n  margin-top: 24px;\r\n}\r\n\r\n.timeline-item {\r\n  display: flex;\r\n  position: relative;\r\n  padding-bottom: 16px;\r\n}\r\n\r\n.timeline-dot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  background-color: #ddd;\r\n  margin-top: 4px;\r\n  margin-right: 12px;\r\n  z-index: 1;\r\n}\r\n\r\n.timeline-item.completed .timeline-dot {\r\n  background-color: #4CAF50;\r\n}\r\n\r\n.timeline-item.current .timeline-dot {\r\n  background-color: #1677FF;\r\n  box-shadow: 0 0 0 4px rgba(22, 119, 255, 0.2);\r\n}\r\n\r\n.timeline-line {\r\n  position: absolute;\r\n  left: 6px;\r\n  top: 16px;\r\n  bottom: 0;\r\n  width: 1px;\r\n  background-color: #ddd;\r\n}\r\n\r\n.timeline-content {\r\n  flex: 1;\r\n}\r\n\r\n.timeline-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  display: block;\r\n}\r\n\r\n.timeline-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 2px;\r\n  display: block;\r\n}\r\n\r\n.info-card {\r\n  margin: 16px;\r\n  border-radius: 8px;\r\n  background-color: #fff;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.card-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 16px;\r\n  position: relative;\r\n  padding-left: 12px;\r\n}\r\n\r\n.card-title::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 0;\r\n  top: 2px;\r\n  bottom: 2px;\r\n  width: 4px;\r\n  background-color: #1677FF;\r\n  border-radius: 2px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.info-label {\r\n  width: 80px;\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.payment-paid {\r\n  color: #4CAF50;\r\n}\r\n\r\n.payment-unpaid {\r\n  color: #F44336;\r\n}\r\n\r\n.customer-profile {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.customer-avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 24px;\r\n  margin-right: 12px;\r\n}\r\n\r\n.customer-info {\r\n  flex: 1;\r\n}\r\n\r\n.customer-name {\r\n  font-size: 16px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  display: block;\r\n}\r\n\r\n.customer-id {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 2px;\r\n  display: block;\r\n}\r\n\r\n.contact-btn {\r\n  padding: 6px 12px;\r\n  background-color: #e6f7ff;\r\n  color: #1677FF;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n.address-info {\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n}\r\n\r\n.address-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.address-icon {\r\n  margin-right: 4px;\r\n}\r\n\r\n.address-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  display: block;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.address-detail {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.4;\r\n}\r\n\r\n.product-item {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.product-item:last-child {\r\n  margin-bottom: 0;\r\n  padding-bottom: 0;\r\n  border-bottom: none;\r\n}\r\n\r\n.product-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 4px;\r\n  margin-right: 12px;\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n.product-info {\r\n  flex: 1;\r\n}\r\n\r\n.product-name {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin-bottom: 4px;\r\n  display: block;\r\n}\r\n\r\n.product-spec {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n}\r\n\r\n.product-price-wrap {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.product-price {\r\n  font-size: 16px;\r\n  color: #ff6a00;\r\n  font-weight: 600;\r\n}\r\n\r\n.product-quantity {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n.amount-info {\r\n  margin-top: 16px;\r\n  padding-top: 16px;\r\n  border-top: 1px dashed #eee;\r\n}\r\n\r\n.amount-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.amount-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.amount-value {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.amount-item.discount .amount-value {\r\n  color: #ff6a00;\r\n}\r\n\r\n.amount-item.total {\r\n  margin-top: 12px;\r\n  padding-top: 12px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.amount-item.total .amount-label,\r\n.amount-item.total .amount-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.remark-content {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n}\r\n\r\n.log-item {\r\n  display: flex;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.log-time {\r\n  width: 140px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.log-content {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.action-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  display: flex;\r\n  padding: 12px 16px;\r\n  background-color: #fff;\r\n  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);\r\n  z-index: 100;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 6px;\r\n  border-radius: 22px;\r\n  font-size: 16px;\r\n  background-color: #f0f0f0;\r\n  color: #333;\r\n}\r\n\r\n.action-btn.primary {\r\n  background-color: #1677FF;\r\n  color: #fff;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-order/pages/orders/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAqJA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,WAAW;AAAA,QACT,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA,QACR,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,eAAe;AAAA;AAAA,QACf,cAAc;AAAA;AAAA,QACd,cAAc;AAAA,QACd,aAAa;AAAA,QACb,UAAU;AAAA,QACV,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,QACR;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,UACN,OAAO;AAAA,UACP,UAAU;AAAA,UACV,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ;AAAA,QACT;AAAA,QACD,OAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,OAAO;AAAA,YACP,UAAU;AAAA,YACV,OAAO;AAAA,UACT;AAAA,QACD;AAAA,QACD,MAAM;AAAA,UACJ;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,UACV;AAAA,UACD;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACD;AAAA,MACD,eAAe;AAAA,QACb;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,WAAW;AAAA,UACX,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,WAAW;AAAA,UACX,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,WAAW;AAAA,UACX,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,WAAW;AAAA,UACX,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,cAAc,QAAQ;AACpB,YAAM,QAAQ;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA;AAEb,aAAO,MAAM,MAAM,KAAK;AAAA,IACzB;AAAA,IACD,qBAAqB,QAAQ;AAC3B,YAAM,eAAe;AAAA,QACnB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA;AAEb,aAAO,aAAa,MAAM,KAAK;AAAA,IAChC;AAAA,IACD,kBAAkB;AAChBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,MAAM;AAAA,QACzB,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,aAAa,GAAG;AAEtBA,0BAAAA,MAAI,cAAc;AAAA,cAChB,aAAa,KAAK,UAAU,SAAS;AAAA,cACrC,MAAM,MAAM;AACVA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,qBACQ,IAAI,aAAa,GAAG;AAE7BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,iBAAK,UAAU,SAAS;AACxB,iBAAK,UAAU,KAAK,KAAK;AAAA,cACvB,MAAM,KAAK,eAAgB;AAAA,cAC3B,SAAS;AAAA,YACX,CAAC;AAGD,iBAAK,cAAc,CAAC,EAAE,YAAY;AAClC,iBAAK,cAAc,CAAC,EAAE,UAAU;AAChC,iBAAK,cAAc,CAAC,EAAE,OAAO,KAAK;AAClC,iBAAK,cAAc,CAAC,EAAE,UAAU;AAEhCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,gBAAgB;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,iBAAK,UAAU,SAAS;AACxB,iBAAK,UAAU,KAAK,KAAK;AAAA,cACvB,MAAM,KAAK,eAAgB;AAAA,cAC3B,SAAS;AAAA,YACX,CAAC;AAGD,iBAAK,cAAc,CAAC,EAAE,YAAY;AAClC,iBAAK,cAAc,CAAC,EAAE,UAAU;AAChC,iBAAK,cAAc,CAAC,EAAE,OAAO,KAAK;AAClC,iBAAK,cAAc,CAAC,EAAE,YAAY;AAClC,iBAAK,cAAc,CAAC,EAAE,UAAU;AAChC,iBAAK,cAAc,CAAC,EAAE,OAAO,KAAK;AAElCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,iBAAK,UAAU,SAAS;AACxB,iBAAK,UAAU,KAAK,KAAK;AAAA,cACvB,MAAM,KAAK,eAAgB;AAAA,cAC3B,SAAS;AAAA,YACX,CAAC;AAEDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACf,YAAM,MAAM,oBAAI;AAChB,YAAM,OAAO,IAAI;AACjB,YAAM,QAAQ,OAAO,IAAI,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACxD,YAAM,MAAM,OAAO,IAAI,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACjD,YAAM,OAAO,OAAO,IAAI,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACnD,YAAM,SAAS,OAAO,IAAI,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACvD,YAAM,SAAS,OAAO,IAAI,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAEvD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM;AAAA,IAC3D;AAAA,IACD,gBAAgB;AAEdA,oBAAY,MAAA,MAAA,OAAA,mEAAA,cAAc,KAAK,OAAO;AAEtC,iBAAW,MAAM;AAAA,MAEhB,GAAE,GAAG;AAAA,IACR;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,SAAK,UAAU,QAAQ,MAAM;AAC7B,SAAK,cAAa;AAAA,EACpB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9YA,GAAG,WAAW,eAAe;"}