"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  name: "MarketingCenter",
  // 移除TabBar组件注册
  components: {},
  data() {
    return {
      currentTab: 2,
      // 当前是营销中心
      hoveredTool: null,
      dateRange: "2023-04-01 ~ 2023-04-15",
      currentTab: 0,
      // 当前选中的内容标签（概览、优惠券、满减等）
      navTab: 2,
      // 底部导航栏当前选中标签为营销中心
      tabList: ["概览", "拼团", "分销", "优惠券", "满减", "秒杀", "会员"],
      // 图表数据
      chartData: [
        { date: "4/10", revenue: 8500, conversion: 3.2, revenueHeight: 35, conversionHeight: 32 },
        { date: "4/11", revenue: 10200, conversion: 3.8, revenueHeight: 42, conversionHeight: 38 },
        { date: "4/12", revenue: 9800, conversion: 3.6, revenueHeight: 40, conversionHeight: 36 },
        { date: "4/13", revenue: 12400, conversion: 4.5, revenueHeight: 51, conversionHeight: 45 },
        { date: "4/14", revenue: 15e3, conversion: 5.2, revenueHeight: 62, conversionHeight: 52 },
        { date: "4/15", revenue: 13600, conversion: 4.8, revenueHeight: 56, conversionHeight: 48 },
        { date: "4/16", revenue: 14800, conversion: 5.1, revenueHeight: 60, conversionHeight: 51 }
      ],
      // 优惠券列表
      couponsList: [
        {
          id: 1,
          title: "新客专享优惠",
          status: "active",
          statusText: "进行中",
          value: 10,
          minSpend: 100,
          expireDate: "2023-05-15",
          usedCount: 234,
          totalCount: 500,
          conversionRate: 46.8
        },
        {
          id: 2,
          title: "满减优惠券",
          status: "active",
          statusText: "进行中",
          value: 20,
          minSpend: 200,
          expireDate: "2023-05-20",
          usedCount: 156,
          totalCount: 300,
          conversionRate: 52
        },
        {
          id: 3,
          title: "节日特别券",
          status: "upcoming",
          statusText: "未开始",
          value: 50,
          minSpend: 300,
          expireDate: "2023-06-10",
          usedCount: 0,
          totalCount: 200,
          conversionRate: 0
        }
      ],
      // 满减活动列表
      discountList: [
        {
          id: 1,
          title: "春季促销活动",
          rules: [
            { minAmount: 100, discountAmount: 10 },
            { minAmount: 200, discountAmount: 25 },
            { minAmount: 300, discountAmount: 50 }
          ],
          timeRange: "2023-04-01 ~ 2023-04-30",
          usageCount: 352,
          totalDiscount: 8562.5
        },
        {
          id: 2,
          title: "周末特惠",
          rules: [
            { minAmount: 150, discountAmount: 15 },
            { minAmount: 300, discountAmount: 40 }
          ],
          timeRange: "每周五至周日",
          usageCount: 126,
          totalDiscount: 3240
        }
      ],
      // 分销数据
      distributionStats: {
        distributors: 128,
        commission: "15,682.50",
        orders: 356
      },
      marketingData: {
        revenue: 23586.5,
        revenueTrend: "up",
        revenueGrowth: "15.2%",
        conversion: 5.8,
        conversionTrend: "up",
        conversionGrowth: "0.8%",
        roi: 286,
        roiTrend: "up",
        roiGrowth: "23%",
        customers: 168,
        customersTrend: "up",
        customersGrowth: "12%"
      },
      activeCampaigns: [
        {
          id: 1,
          name: "春季新品5折优惠",
          status: "进行中",
          statusClass: "active",
          timeRange: "2023-04-01 ~ 2023-04-20",
          views: 3862,
          conversions: 215,
          revenue: 15632.5
        },
        {
          id: 2,
          name: "满300减50活动",
          status: "即将结束",
          statusClass: "ending",
          timeRange: "2023-04-05 ~ 2023-04-18",
          views: 2451,
          conversions: 128,
          revenue: 7954
        },
        {
          id: 3,
          name: "会员专享折扣",
          status: "进行中",
          statusClass: "active",
          timeRange: "2023-04-10 ~ 2023-04-30",
          views: 1752,
          conversions: 86,
          revenue: 4328
        }
      ],
      marketingTools: [
        {
          id: 1,
          name: "优惠券",
          description: "创建多样化的优惠券",
          icon: "coupon",
          class: "coupon",
          path: "/coupon/management",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 12v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-8"/><path d="M18 5.5V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v1.5"/><path d="M2 12h20"/><path d="M12 2v20"/><path d="M8 16h.01"/><path d="M16 16h.01"/><path d="M8 19h.01"/><path d="M16 19h.01"/></svg>'
        },
        {
          id: 2,
          name: "满减活动",
          description: "设置满额减免活动",
          icon: "discount",
          class: "discount",
          path: "/discount/management",
          directPath: "/subPackages/merchant-admin-marketing/pages/marketing/discount/management",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 15-5-5 5-5"/><path d="M4 10h16"/><path d="M15 9v6"/><path d="M12 15h6"/><circle cx="9" cy="20" r="1"/><circle cx="20" cy="20" r="1"/></svg>'
        },
        {
          id: 3,
          name: "拼团活动",
          description: "创建团购优惠活动",
          icon: "group",
          class: "group",
          path: "/group/management",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="17" cy="5" r="3"/><circle cx="7" cy="5" r="3"/><circle cx="17" cy="19" r="3"/><circle cx="7" cy="19" r="3"/><path d="M8 14h8"/><path d="M12 8v6"/></svg>'
        },
        {
          id: 4,
          name: "限时秒杀",
          description: "限时限量特价活动",
          icon: "flash",
          class: "flash",
          path: "/flash/management",
          directPath: "/subPackages/merchant-admin-marketing/pages/marketing/flash/management",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/></svg>'
        },
        {
          id: 5,
          name: "积分商城",
          description: "设置积分兑换商品",
          icon: "points",
          class: "points",
          path: "/points/management",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/></svg>'
        },
        {
          id: 6,
          name: "分销系统",
          description: "设置分销规则与佣金",
          icon: "distribution",
          class: "distribution",
          path: "/distribution/index",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14"/><path d="M5 12h14"/><path d="M19 5v14"/><path d="M5 5v14"/></svg>'
        },
        {
          id: 7,
          name: "会员特权",
          description: "设置会员专属优惠",
          icon: "member",
          class: "member",
          path: "/member/index",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>'
        },
        {
          id: 8,
          name: "红包营销",
          description: "发放现金红包活动",
          icon: "redpacket",
          class: "redpacket",
          path: "/redpacket/index",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z"/><path d="M4 14h16a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2z"/><path d="M2 10h20"/></svg>'
        },
        {
          id: 9,
          name: "营销自动化",
          description: "自动触发的营销流程",
          icon: "automation",
          class: "automation",
          path: "/automation/index",
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/></svg>'
        }
      ],
      advancedTools: [
        {
          id: 1,
          title: "智能营销助手",
          subtitle: "AI驱动的营销策划工具",
          description: "基于历史数据和行业洞察，智能生成最优营销策略与执行方案",
          icon: "/static/images/ai-marketing.png",
          class: "ai",
          tags: ["AI驱动", "数据分析", "自动优化"],
          path: "/ai/index"
        },
        {
          id: 2,
          title: "客户群体细分",
          subtitle: "精准划分客户群体",
          description: "对客户进行多维度分析与分类，实现更精准的营销触达",
          icon: "/static/images/user-segment.png",
          class: "segment",
          tags: ["客户分析", "精准营销", "提升转化"],
          path: "/segment/index"
        },
        {
          id: 3,
          title: "营销自动化",
          subtitle: "设置自动触发营销流",
          description: "基于客户行为自动触发相应营销动作，提高运营效率",
          icon: "/static/images/marketing-automation.png",
          class: "automation",
          tags: ["自动触发", "多渠道", "效率提升"],
          path: "/automation/index"
        }
      ],
      calendarEvents: [
        {
          id: 1,
          day: "15",
          month: "4月",
          title: "春季新品发布会",
          type: "新品发布",
          typeClass: "new-product",
          time: "10:00 - 12:00"
        },
        {
          id: 2,
          day: "18",
          month: "4月",
          title: "限时特惠活动",
          type: "折扣活动",
          typeClass: "discount",
          time: "全天"
        },
        {
          id: 3,
          day: "22",
          month: "4月",
          title: "会员专享日",
          type: "会员活动",
          typeClass: "member",
          time: "全天"
        }
      ],
      marketingInsights: [
        {
          id: 1,
          iconType: "insight",
          title: "客群扩展机会",
          description: "您当前的25-35岁女性客户转化率较高，建议针对此群体增加营销预算，预计可提高20%销售额"
        },
        {
          id: 2,
          iconType: "warning",
          title: "活动优化建议",
          description: "满300减50活动的转化率低于平均水平，建议调整为满200减40，预计可提高15%的转化"
        },
        {
          id: 3,
          iconType: "opportunity",
          title: "节日营销机会",
          description: "五一假期即将到来，根据往年数据，建议提前7天开始促销活动，预计可增加30%的销售额"
        }
      ],
      // 底部导航栏显示的导航项（前4个核心功能 + 更多）
      visibleTabs: [
        {
          id: 0,
          icon: "dashboard",
          text: "商家中心",
          url: "/subPackages/merchant-admin-home/pages/merchant-home/index"
        },
        {
          id: 1,
          icon: "store",
          text: "店铺管理",
          url: "/subPackages/merchant-admin/pages/store/index"
        },
        {
          id: 2,
          icon: "marketing",
          text: "营销中心",
          url: "/subPackages/merchant-admin-marketing/pages/marketing/index"
        },
        {
          id: 3,
          icon: "orders",
          text: "订单管理",
          url: "/subPackages/merchant-admin-order/pages/order/index"
        },
        {
          id: "more",
          icon: "more",
          text: "更多",
          url: ""
        }
      ]
    };
  },
  computed: {
    // 计算营销收入区域路径
    revenueClipPath() {
      const points = this.chartData.map((item, index) => {
        const x = index * (100 / (this.chartData.length - 1));
        const y = 100 - item.revenueHeight;
        return `${x}% ${y}%`;
      });
      const lastX = 100;
      const lastY = 100;
      const firstX = 0;
      const firstY = 100;
      return `polygon(${points.join(", ")}, ${lastX}% ${lastY}%, ${firstX}% ${firstY}%)`;
    },
    // 计算转化率区域路径
    conversionClipPath() {
      const points = this.chartData.map((item, index) => {
        const x = index * (100 / (this.chartData.length - 1));
        const y = 100 - item.conversionHeight;
        return `${x}% ${y}%`;
      });
      const lastX = 100;
      const lastY = 100;
      const firstX = 0;
      const firstY = 100;
      return `polygon(${points.join(", ")}, ${lastX}% ${lastY}%, ${firstX}% ${firstY}%)`;
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    formatNumber(number) {
      return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    switchTab(index) {
      this.currentTab = index;
    },
    switchNavTab(tabId) {
      if (tabId === "more") {
        this.showMoreOptions();
        return;
      }
      if (tabId === this.navTab)
        return;
      this.navTab = tabId;
      if (tabId === 2) {
        return;
      }
      common_vendor.index.redirectTo({
        url: this.visibleTabs[tabId].url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/index.vue:945", "redirectTo失败:", err);
          common_vendor.index.switchTab({
            url: this.visibleTabs[tabId].url,
            fail: (switchErr) => {
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/index.vue:950", "switchTab也失败:", switchErr);
              common_vendor.index.showToast({
                title: "页面跳转失败，请稍后再试",
                icon: "none"
              });
            }
          });
        }
      });
    },
    viewAllCampaigns() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/campaign-list"
      });
    },
    viewCampaignDetail(campaign) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/campaign-detail?id=${campaign.id}`
      });
    },
    navigateToTool(tool) {
      let route = "";
      switch (tool.id) {
        case 2:
          route = "/subPackages/merchant-admin-marketing/pages/marketing/discount/management";
          break;
        case 4:
          route = `/subPackages/merchant-admin-marketing/pages/marketing${tool.path}`;
          break;
        case 6:
          route = "/subPackages/merchant-admin-marketing/pages/marketing/distribution/index";
          break;
        case 7:
          route = "/subPackages/merchant-admin-marketing/pages/marketing/member/index";
          break;
        case 8:
          route = "/subPackages/merchant-admin-marketing/pages/marketing/redpacket/index";
          break;
        case 9:
          route = "/subPackages/merchant-admin-marketing/pages/marketing/verification/index";
          break;
        default:
          route = `/subPackages/merchant-admin-marketing/pages/marketing${tool.path}`;
      }
      common_vendor.index.navigateTo({
        url: route,
        animationType: "slide-in-right",
        animationDuration: 300,
        success: () => {
          common_vendor.index.$emit("marketing-tool-click", {
            toolId: tool.id,
            toolName: tool.name,
            timestamp: Date.now()
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/index.vue:1013", "导航失败:", err);
          common_vendor.index.showToast({
            title: "功能正在建设中",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToAdvancedTool(tool) {
      let route = `/subPackages/merchant-admin-marketing/pages/marketing${tool.path}`;
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/index.vue:1028", "导航路径:", route);
      common_vendor.index.navigateTo({
        url: route,
        animationType: "slide-in-right",
        animationDuration: 300,
        success: () => {
          common_vendor.index.$emit("advanced-tool-click", {
            toolId: tool.id,
            toolName: tool.title,
            timestamp: Date.now()
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/index.vue:1043", "导航失败:", err);
          common_vendor.index.showToast({
            title: "高级功能正在建设中",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    manageEvent(event) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/event-detail?id=${event.id}`
      });
    },
    addNewEvent() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/create-event"
      });
    },
    createNewCampaign() {
      common_vendor.index.showActionSheet({
        itemList: ["创建优惠券", "创建满减活动", "创建拼团活动", "创建秒杀活动", "创建分销计划"],
        success: (res) => {
          const urls = [
            "/subPackages/merchant-admin-marketing/pages/marketing/coupon/create",
            "/subPackages/merchant-admin-marketing/pages/marketing/discount/create",
            "/subPackages/merchant-admin-marketing/pages/marketing/group/create",
            "/subPackages/merchant-admin-marketing/pages/marketing/flash/create",
            "/subPackages/merchant-admin-marketing/pages/marketing/distribution/create"
          ];
          common_vendor.index.navigateTo({
            url: urls[res.tapIndex]
          });
        }
      });
    },
    applyInsight(insight) {
      common_vendor.index.showToast({
        title: "已应用该建议",
        icon: "success"
      });
    },
    ignoreInsight(insight) {
      common_vendor.index.showToast({
        title: "已忽略该建议",
        icon: "none"
      });
    },
    // 优惠券相关方法
    createCoupon() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/create-coupon"
      });
    },
    viewCouponDetail(coupon) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon-detail?id=${coupon.id}`
      });
    },
    // 满减相关方法
    createDiscount() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/discount/create"
      });
    },
    viewDiscountDetail(discount) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/detail?id=${discount.id}`
      });
    },
    // 拼团相关方法
    createGroup() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/create-group"
      });
    },
    // 秒杀相关方法
    createFlash() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/index.vue:1127", "正在跳转到秒杀活动创建页面");
      common_vendor.index.navigateTo({
        url: "/pages/marketing/flash/create",
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/index.vue:1131", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "跳转失败，请检查路径",
            icon: "none"
          });
        }
      });
    },
    // 分销相关方法
    manageDistribution() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/index"
      });
    },
    // 工具卡片交互相关方法
    setHoveredTool(toolId) {
      this.hoveredTool = toolId;
    },
    clearHoveredTool() {
      this.hoveredTool = null;
    },
    // 已用switchNavTab方法替代，此方法不再需要
    showMoreOptions() {
      const moreOptions = ["客户运营", "分析洞察", "系统设置"];
      common_vendor.index.showActionSheet({
        itemList: moreOptions,
        success: (res) => {
          const routes = [
            "/subPackages/merchant-admin-customer/pages/customer/index",
            "/subPackages/merchant-admin/pages/settings/index"
          ];
          common_vendor.index.navigateTo({
            url: routes[res.tapIndex]
          });
        }
      });
    },
    showPublishOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["发布优惠券", "发布满减活动", "发布拼团活动", "发布秒杀活动"],
        success: (res) => {
          const actions = [
            this.createCoupon,
            this.createDiscount,
            this.createGroup,
            this.createFlash
          ];
          if (actions[res.tapIndex]) {
            actions[res.tapIndex]();
          }
        }
      });
    },
    // 智能营销助手相关方法
    viewAllAiTools() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/ai/index"
      });
    },
    useAiTool(toolType) {
      switch (toolType) {
        case "trend":
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/ai/trend-analysis"
          });
          break;
        case "price":
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/ai/price-monitor"
          });
          break;
        case "forecast":
          common_vendor.index.navigateTo({
            url: "/subPackages/merchant-admin-marketing/pages/marketing/ai/sales-forecast"
          });
          break;
        default:
          common_vendor.index.showToast({
            title: "功能开发中",
            icon: "none"
          });
      }
    },
    showActionMenu() {
      common_vendor.index.showActionSheet({
        itemList: ["发布优惠券", "发布满减活动", "发布拼团活动", "发布秒杀活动"],
        success: (res) => {
          const actions = [
            this.createCoupon,
            this.createDiscount,
            this.createGroup,
            this.createFlash
          ];
          if (actions[res.tapIndex]) {
            actions[res.tapIndex]();
          }
        }
      });
    },
    // 添加处理标签页切换的方法
    handleTabChange(tabId) {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/index.vue:1247", "切换到标签:", tabId);
      this.navTab = tabId === "marketing" ? 2 : 0;
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.f($data.marketingTools, (tool, index, i0) => {
      return {
        a: tool.svg,
        b: common_vendor.n(tool.class),
        c: common_vendor.t(tool.name),
        d: tool.name.length === 3 ? 1 : "",
        e: common_vendor.t(tool.description),
        f: index,
        g: common_vendor.o(($event) => $options.navigateToTool(tool), index),
        h: $data.hoveredTool === tool.id ? 1 : "",
        i: common_vendor.o(($event) => $options.setHoveredTool(tool.id), index),
        j: common_vendor.o(($event) => $options.clearHoveredTool(), index)
      };
    }),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.t($options.formatNumber($data.marketingData.revenue)),
    e: common_vendor.t($data.marketingData.revenueGrowth),
    f: common_vendor.n($data.marketingData.revenueTrend),
    g: common_vendor.t($data.marketingData.conversion),
    h: common_vendor.t($data.marketingData.conversionGrowth),
    i: common_vendor.n($data.marketingData.conversionTrend),
    j: common_vendor.t($data.marketingData.roi),
    k: common_vendor.t($data.marketingData.roiGrowth),
    l: common_vendor.n($data.marketingData.roiTrend),
    m: common_vendor.t($data.marketingData.customers),
    n: common_vendor.t($data.marketingData.customersGrowth),
    o: common_vendor.n($data.marketingData.customersTrend),
    p: common_vendor.o((...args) => $options.viewAllAiTools && $options.viewAllAiTools(...args)),
    q: common_vendor.p({
      d: "M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"
    }),
    r: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "#1989FA"
    }),
    s: common_vendor.o(($event) => $options.useAiTool("trend")),
    t: common_vendor.p({
      d: "M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"
    }),
    v: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "#FF9500"
    }),
    w: common_vendor.o(($event) => $options.useAiTool("price")),
    x: common_vendor.p({
      d: "M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"
    }),
    y: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "#34C759"
    }),
    z: common_vendor.o(($event) => $options.useAiTool("forecast")),
    A: common_vendor.f($data.tabList, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    B: $data.currentTab === 0
  }, $data.currentTab === 0 ? {
    C: common_vendor.f($data.chartData, (item, index, i0) => {
      return {
        a: common_vendor.t(item.date),
        b: index,
        c: index * (100 / ($data.chartData.length - 1)) + "%"
      };
    }),
    D: $options.revenueClipPath,
    E: $options.conversionClipPath,
    F: common_vendor.f($data.chartData, (item, index, i0) => {
      return {
        a: "rev-" + index,
        b: index * (100 / ($data.chartData.length - 1)) + "%",
        c: item.revenueHeight + "%"
      };
    }),
    G: common_vendor.f($data.chartData, (item, index, i0) => {
      return {
        a: "conv-" + index,
        b: index * (100 / ($data.chartData.length - 1)) + "%",
        c: item.conversionHeight + "%"
      };
    }),
    H: common_vendor.o((...args) => $options.viewAllCampaigns && $options.viewAllCampaigns(...args)),
    I: common_vendor.f($data.activeCampaigns, (campaign, index, i0) => {
      return {
        a: common_vendor.t(campaign.status),
        b: common_vendor.n(campaign.statusClass),
        c: common_vendor.t(campaign.name),
        d: common_vendor.t(campaign.timeRange),
        e: common_vendor.t(campaign.views),
        f: common_vendor.t(campaign.conversions),
        g: common_vendor.t(campaign.revenue),
        h: index,
        i: common_vendor.o(($event) => $options.viewCampaignDetail(campaign), index)
      };
    }),
    J: common_vendor.f($data.advancedTools, (tool, index, i0) => {
      return {
        a: tool.icon,
        b: common_vendor.n(tool.class),
        c: common_vendor.t(tool.title),
        d: common_vendor.t(tool.subtitle),
        e: common_vendor.t(tool.description),
        f: common_vendor.f(tool.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        g: index,
        h: common_vendor.o(($event) => $options.navigateToAdvancedTool(tool), index)
      };
    }),
    K: common_vendor.f($data.calendarEvents, (event, index, i0) => {
      return {
        a: common_vendor.t(event.day),
        b: common_vendor.t(event.month),
        c: common_vendor.t(event.title),
        d: common_vendor.t(event.type),
        e: common_vendor.n(event.typeClass),
        f: common_vendor.t(event.time),
        g: common_vendor.o(($event) => $options.manageEvent(event), index),
        h: index
      };
    }),
    L: common_vendor.o((...args) => $options.addNewEvent && $options.addNewEvent(...args))
  } : {}, {
    M: $data.currentTab === 3
  }, $data.currentTab === 3 ? {
    N: common_vendor.o((...args) => $options.createCoupon && $options.createCoupon(...args)),
    O: common_vendor.f($data.couponsList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.statusText),
        c: common_vendor.n("status-" + item.status),
        d: common_vendor.t(item.value),
        e: common_vendor.t(item.minSpend),
        f: common_vendor.t(item.expireDate),
        g: common_vendor.t(item.usedCount),
        h: common_vendor.t(item.totalCount),
        i: common_vendor.t(item.conversionRate),
        j: index,
        k: common_vendor.o(($event) => $options.viewCouponDetail(item), index)
      };
    })
  } : {}, {
    P: $data.currentTab === 4
  }, $data.currentTab === 4 ? {
    Q: common_vendor.o((...args) => $options.createDiscount && $options.createDiscount(...args)),
    R: common_vendor.f($data.discountList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.f(item.rules, (rule, ruleIndex, i1) => {
          return {
            a: common_vendor.t(rule.minAmount),
            b: common_vendor.t(rule.discountAmount),
            c: ruleIndex
          };
        }),
        c: common_vendor.t(item.timeRange),
        d: common_vendor.t(item.usageCount),
        e: common_vendor.t(item.totalDiscount),
        f: index,
        g: common_vendor.o(($event) => $options.viewDiscountDetail(item), index)
      };
    })
  } : {}, {
    S: $data.currentTab === 1
  }, $data.currentTab === 1 ? {
    T: common_vendor.o((...args) => $options.createGroup && $options.createGroup(...args))
  } : {}, {
    U: $data.currentTab === 5
  }, $data.currentTab === 5 ? {
    V: common_vendor.o((...args) => $options.createFlash && $options.createFlash(...args))
  } : {}, {
    W: $data.currentTab === 2
  }, $data.currentTab === 2 ? {
    X: common_vendor.t($data.distributionStats.distributors),
    Y: common_vendor.t($data.distributionStats.commission),
    Z: common_vendor.t($data.distributionStats.orders),
    aa: common_vendor.o((...args) => $options.manageDistribution && $options.manageDistribution(...args))
  } : {}, {
    ab: common_vendor.f($data.marketingInsights, (insight, index, i0) => {
      return {
        a: common_vendor.n(insight.iconType),
        b: common_vendor.t(insight.title),
        c: common_vendor.t(insight.description),
        d: common_vendor.o(($event) => $options.applyInsight(insight), index),
        e: common_vendor.o(($event) => $options.ignoreInsight(insight), index),
        f: index
      };
    }),
    ac: common_vendor.o((...args) => $options.createNewCampaign && $options.createNewCampaign(...args)),
    ad: common_vendor.f($data.visibleTabs, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n(tab.icon),
        b: common_vendor.t(tab.text),
        c: $data.navTab === tab.id
      }, $data.navTab === tab.id ? {} : {}, {
        d: index,
        e: $data.navTab === tab.id ? 1 : "",
        f: common_vendor.o(($event) => $options.switchNavTab(tab.id), index)
      });
    }),
    ae: common_vendor.o((...args) => $options.showActionMenu && $options.showActionMenu(...args)),
    af: common_vendor.f($data.visibleTabs, (tab, index, i0) => {
      return common_vendor.e({
        a: $data.navTab === tab.id
      }, $data.navTab === tab.id ? {} : {}, {
        b: common_vendor.n(tab.icon),
        c: common_vendor.t(tab.text),
        d: index,
        e: $data.navTab === tab.id ? 1 : "",
        f: common_vendor.o(($event) => $options.switchNavTab(tab.id), index)
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/index.js.map
