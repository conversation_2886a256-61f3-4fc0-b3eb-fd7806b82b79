<!-- 添加会员特权页面开始 -->
<template>
  <view class="add-privilege-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">添加会员特权</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 基本信息 -->
      <view class="section-card">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="form-label">特权名称</text>
          <input class="form-input" v-model="privilegeForm.name" placeholder="请输入特权名称" maxlength="10" />
        </view>
        
        <view class="form-item">
          <text class="form-label">特权描述</text>
          <input class="form-input" v-model="privilegeForm.description" placeholder="请输入特权描述" maxlength="20" />
        </view>
        
        <view class="form-item">
          <text class="form-label">特权图标</text>
          <view class="icon-upload" @click="chooseIcon">
            <image class="preview-icon" v-if="privilegeForm.icon" :src="privilegeForm.icon" mode="aspectFit"></image>
            <view class="upload-placeholder" v-else>
              <text class="upload-icon">+</text>
              <text class="upload-text">上传图标</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">特权类型</text>
          <picker mode="selector" :range="privilegeTypes" range-key="name" @change="onTypeChange">
            <view class="picker-view">
              <text class="picker-text">{{ getTypeName(privilegeForm.type) }}</text>
              <view class="picker-arrow"></view>
            </view>
          </picker>
        </view>
      </view>
      
      <!-- 特权设置 -->
      <view class="section-card">
        <view class="section-title">特权设置</view>
        
        <view class="form-item switch-item">
          <text class="form-label">默认启用</text>
          <switch :checked="privilegeForm.enabled" @change="toggleEnabled" color="#FF6B22" />
        </view>
        
        <!-- 折扣特权设置 -->
        <block v-if="privilegeForm.type === 'discount'">
          <view class="form-item">
            <text class="form-label">折扣比例</text>
            <view class="form-input-group">
              <input type="digit" class="form-input" v-model="privilegeForm.discountValue" placeholder="请输入折扣比例" />
              <text class="input-suffix">折</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">适用范围</text>
            <view class="radio-group">
              <view class="radio-item" :class="{ active: privilegeForm.discountScope === 'all' }" @click="setDiscountScope('all')">
                <text class="radio-text">全部商品</text>
              </view>
              <view class="radio-item" :class="{ active: privilegeForm.discountScope === 'category' }" @click="setDiscountScope('category')">
                <text class="radio-text">指定分类</text>
              </view>
              <view class="radio-item" :class="{ active: privilegeForm.discountScope === 'product' }" @click="setDiscountScope('product')">
                <text class="radio-text">指定商品</text>
              </view>
            </view>
          </view>
          
          <view class="form-item" v-if="privilegeForm.discountScope !== 'all'">
            <button class="select-btn" @click="selectItems">选择{{ privilegeForm.discountScope === 'category' ? '分类' : '商品' }}</button>
          </view>
        </block>
        
        <!-- 积分特权设置 -->
        <block v-if="privilegeForm.type === 'points'">
          <view class="form-item">
            <text class="form-label">积分倍率</text>
            <view class="form-input-group">
              <input type="digit" class="form-input" v-model="privilegeForm.pointsRatio" placeholder="请输入积分倍率" />
              <text class="input-suffix">倍</text>
            </view>
          </view>
        </block>
        
        <!-- 配送特权设置 -->
        <block v-if="privilegeForm.type === 'delivery'">
          <view class="form-item">
            <text class="form-label">免运费条件</text>
            <view class="radio-group">
              <view class="radio-item" :class="{ active: privilegeForm.freeShippingCondition === 'none' }" @click="setShippingCondition('none')">
                <text class="radio-text">无条件</text>
              </view>
              <view class="radio-item" :class="{ active: privilegeForm.freeShippingCondition === 'amount' }" @click="setShippingCondition('amount')">
                <text class="radio-text">满额免运费</text>
              </view>
            </view>
          </view>
          
          <view class="form-item" v-if="privilegeForm.freeShippingCondition === 'amount'">
            <text class="form-label">满额金额</text>
            <view class="form-input-group">
              <input type="digit" class="form-input" v-model="privilegeForm.freeShippingAmount" placeholder="请输入满额金额" />
              <text class="input-suffix">元</text>
            </view>
          </view>
        </block>
        
        <!-- 礼包特权设置 -->
        <block v-if="privilegeForm.type === 'gift'">
          <view class="form-item">
            <text class="form-label">礼包类型</text>
            <view class="radio-group">
              <view class="radio-item" :class="{ active: privilegeForm.giftType === 'coupon' }" @click="setGiftType('coupon')">
                <text class="radio-text">优惠券</text>
              </view>
              <view class="radio-item" :class="{ active: privilegeForm.giftType === 'points' }" @click="setGiftType('points')">
                <text class="radio-text">积分</text>
              </view>
              <view class="radio-item" :class="{ active: privilegeForm.giftType === 'product' }" @click="setGiftType('product')">
                <text class="radio-text">实物礼品</text>
              </view>
            </view>
          </view>
          
          <view class="form-item" v-if="privilegeForm.giftType === 'coupon'">
            <button class="select-btn" @click="selectCoupons">选择优惠券</button>
          </view>
          
          <view class="form-item" v-if="privilegeForm.giftType === 'points'">
            <text class="form-label">赠送积分</text>
            <view class="form-input-group">
              <input type="number" class="form-input" v-model="privilegeForm.giftPoints" placeholder="请输入赠送积分" />
              <text class="input-suffix">积分</text>
            </view>
          </view>
          
          <view class="form-item" v-if="privilegeForm.giftType === 'product'">
            <button class="select-btn" @click="selectProducts">选择礼品</button>
          </view>
        </block>
        
        <!-- 客服特权设置 -->
        <block v-if="privilegeForm.type === 'service'">
          <view class="form-item">
            <text class="form-label">客服类型</text>
            <view class="radio-group">
              <view class="radio-item" :class="{ active: privilegeForm.serviceType === 'priority' }" @click="setServiceType('priority')">
                <text class="radio-text">优先接入</text>
              </view>
              <view class="radio-item" :class="{ active: privilegeForm.serviceType === 'exclusive' }" @click="setServiceType('exclusive')">
                <text class="radio-text">专属客服</text>
              </view>
            </view>
          </view>
        </block>
      </view>
      
      <!-- 适用会员等级 -->
      <view class="section-card">
        <view class="section-title">适用会员等级</view>
        
        <view class="level-list">
          <view class="level-item" v-for="(level, index) in memberLevels" :key="index">
            <view class="level-checkbox" :class="{ checked: level.selected }" @click="toggleLevel(level)">
              <view class="checkbox-inner" v-if="level.selected"></view>
            </view>
            <view class="level-content">
              <text class="level-name">{{level.name}}</text>
              <text class="level-desc">{{level.memberCount}}名会员</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 有效期设置 -->
      <view class="section-card">
        <view class="section-title">有效期设置</view>
        
        <view class="form-item">
          <text class="form-label">有效期类型</text>
          <view class="radio-group">
            <view class="radio-item" :class="{ active: privilegeForm.validityType === 'permanent' }" @click="setValidityType('permanent')">
              <text class="radio-text">永久有效</text>
            </view>
            <view class="radio-item" :class="{ active: privilegeForm.validityType === 'fixed' }" @click="setValidityType('fixed')">
              <text class="radio-text">固定日期</text>
            </view>
          </view>
        </view>
        
        <block v-if="privilegeForm.validityType === 'fixed'">
          <view class="form-item">
            <text class="form-label">开始日期</text>
            <picker mode="date" :value="privilegeForm.startDate" @change="onStartDateChange">
              <view class="picker-view">
                <text class="picker-text">{{ privilegeForm.startDate || '请选择开始日期' }}</text>
                <view class="picker-arrow"></view>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">结束日期</text>
            <picker mode="date" :value="privilegeForm.endDate" @change="onEndDateChange">
              <view class="picker-view">
                <text class="picker-text">{{ privilegeForm.endDate || '请选择结束日期' }}</text>
                <view class="picker-arrow"></view>
              </view>
            </picker>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="bottom-bar">
      <button class="cancel-btn" @click="goBack">取消</button>
      <button class="save-btn" @click="savePrivilege">保存</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 表单数据
      privilegeForm: {
        name: '',
        description: '',
        icon: '',
        type: 'discount',
        enabled: true,
        
        // 折扣特权
        discountValue: '9.5',
        discountScope: 'all',
        
        // 积分特权
        pointsRatio: '1.5',
        
        // 配送特权
        freeShippingCondition: 'none',
        freeShippingAmount: '99',
        
        // 礼包特权
        giftType: 'coupon',
        giftPoints: '100',
        
        // 客服特权
        serviceType: 'priority',
        
        // 有效期
        validityType: 'permanent',
        startDate: '',
        endDate: ''
      },
      
      // 特权类型
      privilegeTypes: [
        { id: 'discount', name: '会员折扣' },
        { id: 'points', name: '积分加速' },
        { id: 'delivery', name: '免费配送' },
        { id: 'gift', name: '礼品赠送' },
        { id: 'service', name: '专属客服' }
      ],
      
      // 会员等级
      memberLevels: [
        {
          id: 1,
          name: '普通会员',
          memberCount: 2156,
          selected: false
        },
        {
          id: 2,
          name: '银卡会员',
          memberCount: 864,
          selected: true
        },
        {
          id: 3,
          name: '金卡会员',
          memberCount: 426,
          selected: true
        },
        {
          id: 4,
          name: '钻石会员',
          memberCount: 116,
          selected: true
        }
      ]
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    getTypeName(typeId) {
      const type = this.privilegeTypes.find(item => item.id === typeId);
      return type ? type.name : '请选择特权类型';
    },
    
    onTypeChange(e) {
      const index = e.detail.value;
      this.privilegeForm.type = this.privilegeTypes[index].id;
    },
    
    chooseIcon() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.privilegeForm.icon = res.tempFilePaths[0];
        }
      });
    },
    
    toggleEnabled(e) {
      this.privilegeForm.enabled = e.detail.value;
    },
    
    setDiscountScope(scope) {
      this.privilegeForm.discountScope = scope;
    },
    
    setShippingCondition(condition) {
      this.privilegeForm.freeShippingCondition = condition;
    },
    
    setGiftType(type) {
      this.privilegeForm.giftType = type;
    },
    
    setServiceType(type) {
      this.privilegeForm.serviceType = type;
    },
    
    toggleLevel(level) {
      const index = this.memberLevels.findIndex(item => item.id === level.id);
      if (index !== -1) {
        this.memberLevels[index].selected = !this.memberLevels[index].selected;
      }
    },
    
    setValidityType(type) {
      this.privilegeForm.validityType = type;
    },
    
    onStartDateChange(e) {
      this.privilegeForm.startDate = e.detail.value;
    },
    
    onEndDateChange(e) {
      this.privilegeForm.endDate = e.detail.value;
    },
    
    selectItems() {
      uni.showToast({
        title: `选择${this.privilegeForm.discountScope === 'category' ? '分类' : '商品'}功能开发中`,
        icon: 'none'
      });
    },
    
    selectCoupons() {
      uni.showToast({
        title: '选择优惠券功能开发中',
        icon: 'none'
      });
    },
    
    selectProducts() {
      uni.showToast({
        title: '选择礼品功能开发中',
        icon: 'none'
      });
    },
    
    validateForm() {
      if (!this.privilegeForm.name) {
        uni.showToast({
          title: '请输入特权名称',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.privilegeForm.description) {
        uni.showToast({
          title: '请输入特权描述',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.privilegeForm.icon) {
        uni.showToast({
          title: '请上传特权图标',
          icon: 'none'
        });
        return false;
      }
      
      // 检查是否选择了会员等级
      const hasSelectedLevel = this.memberLevels.some(level => level.selected);
      if (!hasSelectedLevel) {
        uni.showToast({
          title: '请选择适用会员等级',
          icon: 'none'
        });
        return false;
      }
      
      // 检查固定日期
      if (this.privilegeForm.validityType === 'fixed') {
        if (!this.privilegeForm.startDate) {
          uni.showToast({
            title: '请选择开始日期',
            icon: 'none'
          });
          return false;
        }
        
        if (!this.privilegeForm.endDate) {
          uni.showToast({
            title: '请选择结束日期',
            icon: 'none'
          });
          return false;
        }
      }
      
      return true;
    },
    
    savePrivilege() {
      if (!this.validateForm()) return;
      
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '会员特权添加成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
}
</script>

<style>
/* 添加会员特权页面样式开始 */
.add-privilege-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 120rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9500, #FF6B22);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 107, 34, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
}

/* 表单内容样式 */
.form-content {
  padding: 20rpx;
}

.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.form-input-group .form-input {
  flex: 1;
  border: none;
}

.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}

/* 图标上传 */
.icon-upload {
  width: 120rpx;
  height: 120rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.preview-icon {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.upload-text {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 选择器样式 */
.picker-view {
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20rpx;
}

.radio-item {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.radio-item.active {
  background: rgba(255, 107, 34, 0.1);
  border-color: #FF6B22;
}

.radio-text {
  font-size: 26rpx;
  color: #666;
}

.radio-item.active .radio-text {
  color: #FF6B22;
  font-weight: 600;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 选择按钮 */
.select-btn {
  background: rgba(255, 107, 34, 0.1);
  color: #FF6B22;
  border: none;
  border-radius: 8rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

/* 会员等级列表 */
.level-list {
  margin-bottom: 20rpx;
}

.level-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.level-item:last-child {
  border-bottom: none;
}

.level-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-checkbox.checked {
  border-color: #FF6B22;
  background: #FF6B22;
}

.checkbox-inner {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background: #fff;
}

.level-content {
  flex: 1;
}

.level-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.level-desc {
  font-size: 24rpx;
  color: #999;
}

/* 底部按钮栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.save-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #FF6B22;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}
/* 添加会员特权页面样式结束 */
</style>
<!-- 添加会员特权页面结束 --> 