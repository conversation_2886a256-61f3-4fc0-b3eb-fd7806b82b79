<template>
  <view class="data-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <view class="safe-area-top"></view>
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">活动数据</view>
        <view class="navbar-right"></view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 数据概览 -->
      <view class="overview-section">
        <view class="section-header">
          <text class="section-title">数据概览</text>
          <view class="date-filter">
            <picker mode="selector" :range="dateRanges" @change="onDateRangeChange">
              <view class="date-picker">
                <text>{{dateRanges[currentDateRange]}}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>
        </view>
        
        <view class="overview-cards">
          <view class="overview-card">
            <view class="card-value">{{overviewData.activityCount}}</view>
            <view class="card-label">活动总数</view>
          </view>
          <view class="overview-card">
            <view class="card-value">{{overviewData.totalViews}}</view>
            <view class="card-label">总浏览量</view>
          </view>
          <view class="overview-card">
            <view class="card-value">{{overviewData.totalParticipants}}</view>
            <view class="card-label">总参与人数</view>
          </view>
          <view class="overview-card">
            <view class="card-value">{{overviewData.avgConversionRate}}%</view>
            <view class="card-label">平均转化率</view>
          </view>
        </view>
      </view>
      
      <!-- 趋势图表 -->
      <view class="chart-section">
        <view class="section-header">
          <text class="section-title">趋势分析</text>
          <view class="chart-tabs">
            <view 
              class="chart-tab" 
              :class="{ active: currentChartType === type.value }" 
              v-for="type in chartTypes" 
              :key="type.value"
              @click="switchChartType(type.value)"
            >
              {{type.label}}
            </view>
          </view>
        </view>
        
        <view class="chart-container">
          <image :src="getChartImage()" mode="widthFix" class="chart-image"></image>
        </view>
      </view>
      
      <!-- 活动排行 -->
      <view class="ranking-section">
        <view class="section-header">
          <text class="section-title">活动排行</text>
          <view class="ranking-tabs">
            <view 
              class="ranking-tab" 
              :class="{ active: currentRankingType === type.value }" 
              v-for="type in rankingTypes" 
              :key="type.value"
              @click="switchRankingType(type.value)"
            >
              {{type.label}}
            </view>
          </view>
        </view>
        
        <view class="ranking-list">
          <view class="ranking-item" v-for="(item, index) in rankingList" :key="index">
            <view class="ranking-number" :class="{ 'top-three': index < 3 }">{{index + 1}}</view>
            <view class="ranking-content">
              <view class="ranking-title">{{item.title}}</view>
              <view class="ranking-info">
                <text>{{item.timeRange}}</text>
                <text class="info-divider">|</text>
                <text>{{getRankingValueText(item)}}</text>
              </view>
            </view>
            <view class="ranking-value">{{getRankingValue(item)}}</view>
          </view>
        </view>
      </view>
      
      <!-- 用户分析 -->
      <view class="user-section">
        <view class="section-header">
          <text class="section-title">用户分析</text>
        </view>
        
        <view class="analysis-cards">
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">用户年龄分布</text>
            </view>
            <view class="pie-chart">
              <image src="/static/images/age-chart.png" mode="widthFix" class="pie-image"></image>
            </view>
            <view class="pie-legend">
              <view class="legend-item" v-for="(item, index) in ageDistribution" :key="index">
                <view class="legend-color" :style="{ backgroundColor: item.color }"></view>
                <text class="legend-label">{{item.label}}</text>
                <text class="legend-value">{{item.percentage}}%</text>
              </view>
            </view>
          </view>
          
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">用户性别比例</text>
            </view>
            <view class="gender-chart">
              <view class="gender-item" v-for="(item, index) in genderDistribution" :key="index">
                <view class="gender-bar" :style="{ width: item.percentage + '%', backgroundColor: item.color }"></view>
                <view class="gender-info">
                  <text class="gender-label">{{item.label}}</text>
                  <text class="gender-value">{{item.percentage}}%</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 地域分布 -->
      <view class="region-section">
        <view class="section-header">
          <text class="section-title">地域分布</text>
        </view>
        
        <view class="region-map">
          <image src="/static/images/region-map.png" mode="widthFix" class="map-image"></image>
        </view>
        
        <view class="region-ranking">
          <view class="region-item" v-for="(item, index) in regionDistribution" :key="index">
            <view class="region-rank">{{index + 1}}</view>
            <view class="region-name">{{item.name}}</view>
            <view class="region-bar-container">
              <view class="region-bar" :style="{ width: (item.percentage / regionDistribution[0].percentage * 100) + '%' }"></view>
            </view>
            <view class="region-percentage">{{item.percentage}}%</view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      dateRanges: ['过去7天', '过去30天', '过去90天', '全部'],
      currentDateRange: 0,
      chartTypes: [
        { label: '浏览量', value: 'views' },
        { label: '参与人数', value: 'participants' },
        { label: '转化率', value: 'conversion' }
      ],
      currentChartType: 'views',
      rankingTypes: [
        { label: '浏览量', value: 'views' },
        { label: '参与人数', value: 'participants' },
        { label: '转化率', value: 'conversion' }
      ],
      currentRankingType: 'views',
      overviewData: {
        activityCount: 12,
        totalViews: 5682,
        totalParticipants: 748,
        avgConversionRate: 13.2
      },
      activities: [
        {
          id: 1,
          title: '双十一大促销，全场满300减50',
          timeRange: '2023-11-01 至 2023-11-11',
          views: 1258,
          participants: 78,
          conversionRate: 6.2
        },
        {
          id: 2,
          title: '开业庆典，免费品尝活动',
          timeRange: '2023-10-15 至 2023-10-20',
          views: 876,
          participants: 126,
          conversionRate: 14.4
        },
        {
          id: 3,
          title: '周年庆典，抽奖赢大礼',
          timeRange: '2023-12-01 至 2023-12-10',
          views: 322,
          participants: 0,
          conversionRate: 0
        },
        {
          id: 4,
          title: '夏季特惠，冰爽饮品第二杯半价',
          timeRange: '2023-07-01 至 2023-08-31',
          views: 2541,
          participants: 376,
          conversionRate: 14.8
        },
        {
          id: 5,
          title: '新品推广，免费试吃活动',
          timeRange: '2023-10-25 至 2023-11-05',
          views: 684,
          participants: 47,
          conversionRate: 6.9
        }
      ],
      ageDistribution: [
        { label: '18-24岁', percentage: 28, color: '#0A84FF' },
        { label: '25-34岁', percentage: 42, color: '#30DB5B' },
        { label: '35-44岁', percentage: 18, color: '#FF9500' },
        { label: '45岁以上', percentage: 12, color: '#FF3B30' }
      ],
      genderDistribution: [
        { label: '女性', percentage: 62, color: '#FF2D55' },
        { label: '男性', percentage: 38, color: '#0A84FF' }
      ],
      regionDistribution: [
        { name: '北京市', percentage: 22.5 },
        { name: '上海市', percentage: 18.3 },
        { name: '广东省', percentage: 15.7 },
        { name: '江苏省', percentage: 10.2 },
        { name: '浙江省', percentage: 8.6 }
      ]
    }
  },
  computed: {
    rankingList() {
      const sortedList = [...this.activities].sort((a, b) => {
        if (this.currentRankingType === 'views') {
          return b.views - a.views;
        } else if (this.currentRankingType === 'participants') {
          return b.participants - a.participants;
        } else {
          return b.conversionRate - a.conversionRate;
        }
      });
      return sortedList;
    }
  },
  onLoad() {
    this.setStatusBarHeight();
  },
  methods: {
    setStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    goBack() {
      uni.navigateBack();
    },
    onDateRangeChange(e) {
      this.currentDateRange = parseInt(e.detail.value);
      // 在实际应用中，这里应该根据选择的日期范围重新加载数据
    },
    switchChartType(type) {
      this.currentChartType = type;
    },
    switchRankingType(type) {
      this.currentRankingType = type;
    },
    getChartImage() {
      // 实际应用中，这里应该根据当前选择的图表类型返回不同的图表图片
      const chartImages = {
        'views': '/static/images/views-chart.png',
        'participants': '/static/images/participants-chart.png',
        'conversion': '/static/images/conversion-chart.png'
      };
      return chartImages[this.currentChartType] || chartImages.views;
    },
    getRankingValue(item) {
      if (this.currentRankingType === 'views') {
        return item.views;
      } else if (this.currentRankingType === 'participants') {
        return item.participants;
      } else {
        return item.conversionRate + '%';
      }
    },
    getRankingValueText(item) {
      if (this.currentRankingType === 'views') {
        return '浏览量';
      } else if (this.currentRankingType === 'participants') {
        return '参与人数';
      } else {
        return '转化率';
      }
    }
  }
}
</script>

<style lang="scss">
.data-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

.content-scroll {
  padding: 30rpx;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

/* 数据概览样式 */
.overview-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.date-filter {
  background-color: #F5F8FC;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.date-picker {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #0A84FF;
}

.picker-arrow {
  margin-left: 10rpx;
  font-size: 20rpx;
}

.overview-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.overview-card {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #F5F8FC;
  border-radius: 16rpx;
  padding: 20rpx;
  text-align: center;
}

.card-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #0A84FF;
  margin-bottom: 10rpx;
}

.card-label {
  font-size: 26rpx;
  color: #666;
}

/* 图表区域样式 */
.chart-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.chart-tabs {
  display: flex;
}

.chart-tab {
  padding: 6rpx 20rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.chart-tab.active {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
}

.chart-container {
  margin-top: 20rpx;
  padding: 10rpx;
}

.chart-image {
  width: 100%;
  border-radius: 16rpx;
}

/* 排行榜样式 */
.ranking-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.ranking-tabs {
  display: flex;
}

.ranking-tab {
  padding: 6rpx 20rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.ranking-tab.active {
  background-color: rgba(10, 132, 255, 0.1);
  color: #0A84FF;
}

.ranking-list {
  margin-top: 20rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #F2F2F7;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #F5F8FC;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
}

.ranking-number.top-three {
  background-color: #0A84FF;
  color: #FFFFFF;
}

.ranking-content {
  flex: 1;
}

.ranking-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.ranking-info {
  font-size: 24rpx;
  color: #999;
}

.info-divider {
  margin: 0 10rpx;
}

.ranking-value {
  font-size: 30rpx;
  color: #0A84FF;
  font-weight: 500;
}

/* 用户分析样式 */
.user-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.analysis-cards {
  display: flex;
  flex-direction: column;
}

.analysis-card {
  margin-bottom: 30rpx;
}

.analysis-card:last-child {
  margin-bottom: 0;
}

.card-header {
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.pie-chart {
  text-align: center;
  margin-bottom: 20rpx;
}

.pie-image {
  width: 100%;
  max-width: 400rpx;
}

.pie-legend {
  display: flex;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 16rpx;
}

.legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.legend-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.legend-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.gender-chart {
  margin-top: 20rpx;
}

.gender-item {
  margin-bottom: 20rpx;
}

.gender-bar {
  height: 30rpx;
  border-radius: 15rpx;
  margin-bottom: 10rpx;
}

.gender-info {
  display: flex;
  justify-content: space-between;
}

.gender-label {
  font-size: 24rpx;
  color: #666;
}

.gender-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 地域分布样式 */
.region-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.region-map {
  margin-bottom: 30rpx;
}

.map-image {
  width: 100%;
  border-radius: 16rpx;
}

.region-ranking {
  margin-top: 20rpx;
}

.region-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.region-rank {
  width: 40rpx;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.region-name {
  width: 120rpx;
  font-size: 26rpx;
  color: #333;
}

.region-bar-container {
  flex: 1;
  height: 16rpx;
  background-color: #F5F8FC;
  border-radius: 8rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.region-bar {
  height: 100%;
  background: linear-gradient(90deg, #0A84FF, #0055FF);
  border-radius: 8rpx;
}

.region-percentage {
  width: 80rpx;
  font-size: 26rpx;
  color: #0A84FF;
  text-align: right;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 40rpx;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .data-container {
    background-color: #1C1C1E;
  }
  
  .overview-section,
  .chart-section,
  .ranking-section,
  .user-section,
  .region-section {
    background-color: #2C2C2E;
  }
  
  .section-title,
  .card-title,
  .ranking-title,
  .legend-value,
  .gender-value,
  .region-rank,
  .region-name {
    color: #FFFFFF;
  }
  
  .card-label,
  .ranking-info,
  .legend-label,
  .gender-label {
    color: #A8A8A8;
  }
  
  .date-filter,
  .overview-card,
  .ranking-number,
  .region-bar-container {
    background-color: #3A3A3C;
  }
  
  .ranking-item {
    border-color: #3A3A3C;
  }
}
</style> 