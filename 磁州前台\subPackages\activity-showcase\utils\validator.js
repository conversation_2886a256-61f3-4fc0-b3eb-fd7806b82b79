/**
 * 磁州前台数据验证工具
 * 提供表单验证、数据校验、规则引擎等功能
 */

// 验证规则
const VALIDATION_RULES = {
  // 必填
  required: {
    validator: (value) => {
      if (Array.isArray(value)) return value.length > 0
      if (typeof value === 'object' && value !== null) return Object.keys(value).length > 0
      return value !== null && value !== undefined && String(value).trim() !== ''
    },
    message: '此字段为必填项'
  },
  
  // 邮箱
  email: {
    validator: (value) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return !value || emailRegex.test(value)
    },
    message: '请输入有效的邮箱地址'
  },
  
  // 手机号
  phone: {
    validator: (value) => {
      const phoneRegex = /^1[3-9]\d{9}$/
      return !value || phoneRegex.test(value)
    },
    message: '请输入有效的手机号码'
  },
  
  // 身份证号
  idCard: {
    validator: (value) => {
      const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      return !value || idCardRegex.test(value)
    },
    message: '请输入有效的身份证号码'
  },
  
  // 数字
  number: {
    validator: (value) => {
      return !value || !isNaN(Number(value))
    },
    message: '请输入有效的数字'
  },
  
  // 整数
  integer: {
    validator: (value) => {
      return !value || (Number.isInteger(Number(value)) && Number(value) >= 0)
    },
    message: '请输入有效的整数'
  },
  
  // 最小长度
  minLength: {
    validator: (value, min) => {
      return !value || String(value).length >= min
    },
    message: (min) => `最少需要${min}个字符`
  },
  
  // 最大长度
  maxLength: {
    validator: (value, max) => {
      return !value || String(value).length <= max
    },
    message: (max) => `最多允许${max}个字符`
  },
  
  // 最小值
  min: {
    validator: (value, min) => {
      return !value || Number(value) >= min
    },
    message: (min) => `最小值为${min}`
  },
  
  // 最大值
  max: {
    validator: (value, max) => {
      return !value || Number(value) <= max
    },
    message: (max) => `最大值为${max}`
  },
  
  // 正则表达式
  pattern: {
    validator: (value, pattern) => {
      if (!value) return true
      const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern
      return regex.test(value)
    },
    message: '格式不正确'
  },
  
  // URL
  url: {
    validator: (value) => {
      if (!value) return true
      try {
        new URL(value)
        return true
      } catch {
        return false
      }
    },
    message: '请输入有效的URL地址'
  },
  
  // 日期
  date: {
    validator: (value) => {
      if (!value) return true
      const date = new Date(value)
      return !isNaN(date.getTime())
    },
    message: '请输入有效的日期'
  },
  
  // 密码强度
  password: {
    validator: (value) => {
      if (!value) return true
      // 至少8位，包含大小写字母和数字
      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
      return passwordRegex.test(value)
    },
    message: '密码至少8位，需包含大小写字母和数字'
  },
  
  // 确认密码
  confirmPassword: {
    validator: (value, originalPassword) => {
      return !value || value === originalPassword
    },
    message: '两次输入的密码不一致'
  }
}

// 验证器类
class Validator {
  constructor() {
    this.rules = { ...VALIDATION_RULES }
    this.customRules = {}
  }
  
  // 添加自定义规则
  addRule(name, rule) {
    this.customRules[name] = rule
  }
  
  // 获取规则
  getRule(name) {
    return this.customRules[name] || this.rules[name]
  }
  
  // 验证单个字段
  validateField(value, rules, data = {}) {
    const errors = []
    
    if (!Array.isArray(rules)) {
      rules = [rules]
    }
    
    for (const rule of rules) {
      const result = this.executeRule(value, rule, data)
      if (!result.valid) {
        errors.push(result.message)
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  // 执行单个规则
  executeRule(value, rule, data = {}) {
    let ruleName, ruleConfig, ruleParams
    
    if (typeof rule === 'string') {
      ruleName = rule
      ruleConfig = this.getRule(ruleName)
      ruleParams = []
    } else if (typeof rule === 'object') {
      ruleName = rule.type || rule.name
      ruleConfig = rule.validator ? rule : this.getRule(ruleName)
      ruleParams = rule.params || []
      
      // 如果规则对象包含自定义消息，使用自定义消息
      if (rule.message) {
        ruleConfig = {
          ...ruleConfig,
          message: rule.message
        }
      }
    }
    
    if (!ruleConfig) {
      console.warn(`验证规则 ${ruleName} 不存在`)
      return { valid: true }
    }
    
    try {
      const isValid = ruleConfig.validator(value, ...ruleParams, data)
      const message = typeof ruleConfig.message === 'function' 
        ? ruleConfig.message(...ruleParams)
        : ruleConfig.message
      
      return {
        valid: isValid,
        message: isValid ? '' : message
      }
    } catch (error) {
      console.error(`验证规则 ${ruleName} 执行失败:`, error)
      return {
        valid: false,
        message: '验证失败'
      }
    }
  }
  
  // 验证整个表单
  validateForm(data, schema) {
    const results = {}
    let isValid = true
    
    Object.keys(schema).forEach(field => {
      const fieldRules = schema[field]
      const fieldValue = data[field]
      
      const result = this.validateField(fieldValue, fieldRules, data)
      results[field] = result
      
      if (!result.valid) {
        isValid = false
      }
    })
    
    return {
      valid: isValid,
      results,
      errors: this.extractErrors(results)
    }
  }
  
  // 提取错误信息
  extractErrors(results) {
    const errors = {}
    
    Object.keys(results).forEach(field => {
      if (!results[field].valid) {
        errors[field] = results[field].errors
      }
    })
    
    return errors
  }
  
  // 异步验证
  async validateAsync(data, schema) {
    const results = {}
    let isValid = true
    
    const promises = Object.keys(schema).map(async (field) => {
      const fieldRules = schema[field]
      const fieldValue = data[field]
      
      // 处理异步规则
      const asyncRules = fieldRules.filter(rule => 
        typeof rule === 'object' && rule.async
      )
      
      if (asyncRules.length > 0) {
        const asyncResults = await Promise.all(
          asyncRules.map(rule => this.executeAsyncRule(fieldValue, rule, data))
        )
        
        const syncRules = fieldRules.filter(rule => 
          !(typeof rule === 'object' && rule.async)
        )
        
        const syncResult = this.validateField(fieldValue, syncRules, data)
        
        const allErrors = [
          ...syncResult.errors,
          ...asyncResults.filter(r => !r.valid).map(r => r.message)
        ]
        
        results[field] = {
          valid: syncResult.valid && asyncResults.every(r => r.valid),
          errors: allErrors
        }
      } else {
        results[field] = this.validateField(fieldValue, fieldRules, data)
      }
      
      if (!results[field].valid) {
        isValid = false
      }
    })
    
    await Promise.all(promises)
    
    return {
      valid: isValid,
      results,
      errors: this.extractErrors(results)
    }
  }
  
  // 执行异步规则
  async executeAsyncRule(value, rule, data = {}) {
    try {
      const isValid = await rule.validator(value, ...(rule.params || []), data)
      const message = typeof rule.message === 'function' 
        ? rule.message(...(rule.params || []))
        : rule.message
      
      return {
        valid: isValid,
        message: isValid ? '' : message
      }
    } catch (error) {
      console.error('异步验证规则执行失败:', error)
      return {
        valid: false,
        message: '验证失败'
      }
    }
  }
  
  // 创建验证规则构建器
  createRuleBuilder() {
    return new RuleBuilder(this)
  }
}

// 规则构建器类
class RuleBuilder {
  constructor(validator) {
    this.validator = validator
    this.rules = []
  }
  
  // 必填
  required(message) {
    this.rules.push({
      type: 'required',
      message: message || VALIDATION_RULES.required.message
    })
    return this
  }
  
  // 邮箱
  email(message) {
    this.rules.push({
      type: 'email',
      message: message || VALIDATION_RULES.email.message
    })
    return this
  }
  
  // 手机号
  phone(message) {
    this.rules.push({
      type: 'phone',
      message: message || VALIDATION_RULES.phone.message
    })
    return this
  }
  
  // 最小长度
  minLength(min, message) {
    this.rules.push({
      type: 'minLength',
      params: [min],
      message: message || VALIDATION_RULES.minLength.message(min)
    })
    return this
  }
  
  // 最大长度
  maxLength(max, message) {
    this.rules.push({
      type: 'maxLength',
      params: [max],
      message: message || VALIDATION_RULES.maxLength.message(max)
    })
    return this
  }
  
  // 数值范围
  range(min, max, message) {
    this.rules.push({
      type: 'min',
      params: [min],
      message: message || VALIDATION_RULES.min.message(min)
    })
    this.rules.push({
      type: 'max',
      params: [max],
      message: message || VALIDATION_RULES.max.message(max)
    })
    return this
  }
  
  // 正则表达式
  pattern(regex, message) {
    this.rules.push({
      type: 'pattern',
      params: [regex],
      message: message || VALIDATION_RULES.pattern.message
    })
    return this
  }
  
  // 自定义验证
  custom(validator, message) {
    this.rules.push({
      validator,
      message: message || '验证失败'
    })
    return this
  }
  
  // 异步验证
  async(validator, message) {
    this.rules.push({
      validator,
      message: message || '验证失败',
      async: true
    })
    return this
  }
  
  // 构建规则
  build() {
    return [...this.rules]
  }
}

// 创建全局验证器实例
const validator = new Validator()

// 添加一些常用的自定义规则
validator.addRule('username', {
  validator: (value) => {
    if (!value) return true
    // 用户名：4-20位，字母数字下划线
    const usernameRegex = /^[a-zA-Z0-9_]{4,20}$/
    return usernameRegex.test(value)
  },
  message: '用户名为4-20位字母、数字或下划线'
})

validator.addRule('chinese', {
  validator: (value) => {
    if (!value) return true
    const chineseRegex = /^[\u4e00-\u9fa5]+$/
    return chineseRegex.test(value)
  },
  message: '请输入中文字符'
})

validator.addRule('positiveNumber', {
  validator: (value) => {
    if (!value) return true
    const num = Number(value)
    return !isNaN(num) && num > 0
  },
  message: '请输入大于0的数字'
})

// 导出验证器和工具函数
export {
  validator,
  Validator,
  RuleBuilder,
  VALIDATION_RULES
}

// 便捷方法
export const validateField = (value, rules, data) => validator.validateField(value, rules, data)
export const validateForm = (data, schema) => validator.validateForm(data, schema)
export const validateAsync = (data, schema) => validator.validateAsync(data, schema)
export const createRuleBuilder = () => validator.createRuleBuilder()
export const addRule = (name, rule) => validator.addRule(name, rule)

// 默认导出
export default validator
