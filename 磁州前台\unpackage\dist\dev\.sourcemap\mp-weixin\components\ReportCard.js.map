{"version": 3, "file": "ReportCard.js", "sources": ["components/ReportCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9SZXBvcnRDYXJkLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"report-card\" @click=\"navigateToReport\">\r\n    <view class=\"report-icon\">\r\n      <image src=\"/static/images/tabbar/举报.png\" mode=\"aspectFit\"></image>\r\n    </view>\r\n    <view class=\"report-content\">\r\n      <view class=\"report-title\">如遇无效、虚假信息，请立即举报</view>\r\n      <view class=\"report-subtitle\">平台将快速核实处理</view>\r\n    </view>\r\n    <view class=\"report-arrow\">\r\n      <image src=\"/static/images/tabbar/箭头.png\" class=\"arrow-image\" mode=\"aspectFit\"></image>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ReportCard',\r\n  props: {\r\n    contentId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    contentType: {\r\n      type: String,\r\n      default: 'info' // 默认为普通信息类型\r\n    }\r\n  },\r\n  methods: {\r\n    navigateToReport() {\r\n      // 获取当前页面路径和参数\r\n      const pages = getCurrentPages();\r\n      const currentPage = pages[pages.length - 1];\r\n      const options = currentPage.options || {};\r\n      \r\n      // 优先使用props传入的contentId，如果没有则尝试从当前页面获取id\r\n      const id = this.contentId || options.id || '';\r\n      \r\n      // 跳转到举报页面，并传递内容ID和类型\r\n      uni.navigateTo({\r\n        url: `/pages/common/report?id=${id}&type=${this.contentType}`\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.report-card {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #fff;\r\n  padding: 24rpx 30rpx;\r\n  margin: 20rpx;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.report-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  margin-right: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  \r\n  image {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n  }\r\n}\r\n\r\n.report-content {\r\n  flex: 1;\r\n}\r\n\r\n.report-title {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 4rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.report-subtitle {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.report-arrow {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.arrow-image {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  opacity: 0.5;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/ReportCard.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;AAgBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,IACX;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,mBAAmB;AAEjB,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,YAAM,KAAK,KAAK,aAAa,QAAQ,MAAM;AAG3CA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2BAA2B,EAAE,SAAS,KAAK,WAAW;AAAA,MAC7D,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;AC3CA,GAAG,gBAAgB,SAAS;"}