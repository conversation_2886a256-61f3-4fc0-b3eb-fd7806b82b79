{"version": 3, "file": "contentCheck.js", "sources": ["utils/contentCheck.js"], "sourcesContent": ["/**\r\n * 内容审核工具模块\r\n * 用于检测用户输入的文本和上传的图片是否包含违规内容\r\n */\r\n// 移除错误的导入方式，直接使用全局变量\r\n// import { uniCloud } from '@dcloudio/uni-app';\r\n\r\n// 违规词汇库（示例）- 实际应该放在云端或后端\r\nconst SENSITIVE_WORDS = [\r\n  '赌博', '博彩', '色情', '暴力', '毒品', '违禁品', \r\n  '诈骗', '传销', '非法', '黄色', '办证', '枪支', \r\n  '走私', '病毒', '黑客', '私服', '外挂', '代考', \r\n  '代写', '代办', '伪造', '假证', '黑产', '裸聊'\r\n];\r\n\r\n// 违规类型定义\r\nexport const VIOLATION_TYPES = {\r\n  NORMAL: 0,       // 正常内容\r\n  SENSITIVE: 1,    // 敏感内容\r\n  POLITICAL: 2,    // 政治敏感\r\n  PORN: 3,         // 色情\r\n  ABUSE: 4,        // 辱骂\r\n  VIOLENCE: 5,     // 暴力\r\n  FRAUD: 6,        // 诈骗\r\n  ILLEGAL: 7,      // 其他违法\r\n  AD: 8,           // 广告\r\n  SPAM: 9          // 垃圾信息\r\n};\r\n\r\n/**\r\n * 文本内容审核\r\n * @param {String} text 待审核的文本内容\r\n * @returns {Promise} 审核结果\r\n */\r\nexport const checkText = async (text) => {\r\n  if (!text || text.trim() === '') {\r\n    return {\r\n      pass: true,\r\n      type: VIOLATION_TYPES.NORMAL,\r\n      message: '内容正常'\r\n    };\r\n  }\r\n  \r\n  try {\r\n    // 开发环境进行简易本地检测\r\n    if (process.env.NODE_ENV === 'development') {\r\n      return localCheckText(text);\r\n    }\r\n    \r\n    // 生产环境调用云函数进行内容检测\r\n    const result = await uniCloud.callFunction({\r\n      name: 'contentCheck',\r\n      data: {\r\n        type: 'text',\r\n        content: text\r\n      }\r\n    });\r\n    \r\n    return result.result || {\r\n      pass: true,\r\n      type: VIOLATION_TYPES.NORMAL,\r\n      message: '内容正常'\r\n    };\r\n  } catch (error) {\r\n    console.error('文本内容审核异常:', error);\r\n    // 审核失败默认放行，由人工二次审核\r\n    return {\r\n      pass: true,\r\n      type: VIOLATION_TYPES.NORMAL,\r\n      message: '内容审核失败，将进行人工复核',\r\n      needReview: true\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * 图片内容审核\r\n * @param {String|Array} images 图片路径或路径数组\r\n * @returns {Promise} 审核结果\r\n */\r\nexport const checkImage = async (images) => {\r\n  if (!images) {\r\n    return {\r\n      pass: true,\r\n      type: VIOLATION_TYPES.NORMAL,\r\n      message: '内容正常'\r\n    };\r\n  }\r\n  \r\n  // 转换为数组形式处理\r\n  const imageArray = Array.isArray(images) ? images : [images];\r\n  \r\n  if (imageArray.length === 0) {\r\n    return {\r\n      pass: true,\r\n      type: VIOLATION_TYPES.NORMAL,\r\n      message: '内容正常'\r\n    };\r\n  }\r\n  \r\n  try {\r\n    // 开发环境暂不做实际检测，默认通过\r\n    if (process.env.NODE_ENV === 'development') {\r\n      return {\r\n        pass: true,\r\n        type: VIOLATION_TYPES.NORMAL,\r\n        message: '开发环境图片默认通过'\r\n      };\r\n    }\r\n    \r\n    // 检测每张图片\r\n    const checkResults = await Promise.all(\r\n      imageArray.map(async (imagePath) => {\r\n        // 调用云函数检测图片\r\n        const result = await uniCloud.callFunction({\r\n          name: 'contentCheck',\r\n          data: {\r\n            type: 'image',\r\n            url: imagePath\r\n          }\r\n        });\r\n        \r\n        return result.result || {\r\n          pass: true,\r\n          type: VIOLATION_TYPES.NORMAL,\r\n          message: '内容正常',\r\n          url: imagePath\r\n        };\r\n      })\r\n    );\r\n    \r\n    // 只要有一张图片不通过，则整体不通过\r\n    const failedCheck = checkResults.find(item => !item.pass);\r\n    \r\n    if (failedCheck) {\r\n      return {\r\n        pass: false,\r\n        type: failedCheck.type,\r\n        message: `图片内容违规: ${failedCheck.message}`,\r\n        url: failedCheck.url\r\n      };\r\n    }\r\n    \r\n    return {\r\n      pass: true,\r\n      type: VIOLATION_TYPES.NORMAL,\r\n      message: '所有图片内容正常'\r\n    };\r\n  } catch (error) {\r\n    console.error('图片内容审核异常:', error);\r\n    // 审核失败默认放行，由人工二次审核\r\n    return {\r\n      pass: true,\r\n      type: VIOLATION_TYPES.NORMAL,\r\n      message: '图片审核失败，将进行人工复核',\r\n      needReview: true\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * 本地简易文本检测（仅用于开发环境）\r\n * @param {String} text 待检测文本\r\n * @returns {Object} 检测结果\r\n */\r\nconst localCheckText = (text) => {\r\n  if (!text) return { pass: true, type: VIOLATION_TYPES.NORMAL, message: '内容正常' };\r\n  \r\n  // 转小写便于匹配\r\n  const lowerText = text.toLowerCase();\r\n  \r\n  // 检测是否包含违规词汇\r\n  for (const word of SENSITIVE_WORDS) {\r\n    if (lowerText.includes(word)) {\r\n      return {\r\n        pass: false,\r\n        type: VIOLATION_TYPES.SENSITIVE,\r\n        message: `内容包含违规词汇: ${word}`,\r\n        word\r\n      };\r\n    }\r\n  }\r\n  \r\n  // 检测电话号码泄露\r\n  const phoneRegex = /1[3-9]\\d{9}/g;\r\n  if (phoneRegex.test(text)) {\r\n    // 此处可根据业务需求决定是否放行手机号\r\n    return {\r\n      pass: true,\r\n      type: VIOLATION_TYPES.NORMAL,\r\n      message: '内容包含电话号码，请注意保护隐私',\r\n      containsPhone: true\r\n    };\r\n  }\r\n  \r\n  // 检测垃圾广告特征\r\n  if (\r\n    (lowerText.includes('优惠') && lowerText.includes('活动')) ||\r\n    (lowerText.includes('推广') && lowerText.includes('促销')) ||\r\n    (lowerText.includes('免费') && lowerText.includes('送')) ||\r\n    lowerText.includes('点击链接')\r\n  ) {\r\n    return {\r\n      pass: false,\r\n      type: VIOLATION_TYPES.AD,\r\n      message: '内容疑似广告信息'\r\n    };\r\n  }\r\n  \r\n  return {\r\n    pass: true,\r\n    type: VIOLATION_TYPES.NORMAL,\r\n    message: '内容正常'\r\n  };\r\n};\r\n\r\n/**\r\n * 综合检测内容（文本+图片）\r\n * @param {Object} content 内容对象 {text, images}\r\n * @returns {Promise} 检测结果\r\n */\r\nexport const checkContent = async (content) => {\r\n  const { text, images } = content;\r\n  \r\n  // 并行检测文本和图片\r\n  const [textResult, imageResult] = await Promise.all([\r\n    checkText(text),\r\n    checkImage(images)\r\n  ]);\r\n  \r\n  // 如果任一检测不通过，则整体不通过\r\n  if (!textResult.pass || !imageResult.pass) {\r\n    return {\r\n      pass: false,\r\n      textCheck: textResult,\r\n      imageCheck: imageResult,\r\n      message: textResult.pass ? imageResult.message : textResult.message\r\n    };\r\n  }\r\n  \r\n  // 是否需要人工复核\r\n  const needReview = textResult.needReview || imageResult.needReview;\r\n  \r\n  return {\r\n    pass: true,\r\n    textCheck: textResult,\r\n    imageCheck: imageResult,\r\n    message: '内容审核通过',\r\n    needReview\r\n  };\r\n};\r\n\r\nexport default {\r\n  checkText,\r\n  checkImage,\r\n  checkContent,\r\n  VIOLATION_TYPES\r\n}; "], "names": ["uniCloud", "uni"], "mappings": ";;AAQA,MAAM,kBAAkB;AAAA,EACtB;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAC9B;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAC9B;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAC9B;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAChC;AAGO,MAAM,kBAAkB;AAAA,EAC7B,QAAQ;AAAA;AAAA,EACR,WAAW;AAAA;AAAA,EACX,WAAW;AAAA;AAAA,EACX,MAAM;AAAA;AAAA,EACN,OAAO;AAAA;AAAA,EACP,UAAU;AAAA;AAAA,EACV,OAAO;AAAA;AAAA,EACP,SAAS;AAAA;AAAA,EACT,IAAI;AAAA;AAAA,EACJ,MAAM;AAAA;AACR;AAOa,MAAA,YAAY,OAAO,SAAS;AACvC,MAAI,CAAC,QAAQ,KAAK,KAAA,MAAW,IAAI;AACxB,WAAA;AAAA,MACL,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,IAAA;AAAA,EAEb;AAEI,MAAA;AAEF,QAAI,MAAwC;AAC1C,aAAO,eAAe,IAAI;AAAA,IAC5B;AAGM,UAAA,SAAS,MAAMA,cAAA,GAAS,aAAa;AAAA,MACzC,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IAAA,CACD;AAED,WAAO,OAAO,UAAU;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,IAAA;AAAA,WAEJ,OAAO;AACdC,kBAAA,MAAc,MAAA,SAAA,+BAAA,aAAa,KAAK;AAEzB,WAAA;AAAA,MACL,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,MACT,YAAY;AAAA,IAAA;AAAA,EAEhB;AACF;AAOa,MAAA,aAAa,OAAO,WAAW;AAC1C,MAAI,CAAC,QAAQ;AACJ,WAAA;AAAA,MACL,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,IAAA;AAAA,EAEb;AAGA,QAAM,aAAa,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAEvD,MAAA,WAAW,WAAW,GAAG;AACpB,WAAA;AAAA,MACL,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,IAAA;AAAA,EAEb;AAEI,MAAA;AAEF,QAAI,MAAwC;AACnC,aAAA;AAAA,QACL,MAAM;AAAA,QACN,MAAM,gBAAgB;AAAA,QACtB,SAAS;AAAA,MAAA;AAAA,IAEb;AAGM,UAAA,eAAe,MAAM,QAAQ;AAAA,MACjC,WAAW,IAAI,OAAO,cAAc;AAE5B,cAAA,SAAS,MAAMD,cAAA,GAAS,aAAa;AAAA,UACzC,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,KAAK;AAAA,UACP;AAAA,QAAA,CACD;AAED,eAAO,OAAO,UAAU;AAAA,UACtB,MAAM;AAAA,UACN,MAAM,gBAAgB;AAAA,UACtB,SAAS;AAAA,UACT,KAAK;AAAA,QAAA;AAAA,MACP,CACD;AAAA,IAAA;AAIH,UAAM,cAAc,aAAa,KAAK,CAAQ,SAAA,CAAC,KAAK,IAAI;AAExD,QAAI,aAAa;AACR,aAAA;AAAA,QACL,MAAM;AAAA,QACN,MAAM,YAAY;AAAA,QAClB,SAAS,WAAW,YAAY,OAAO;AAAA,QACvC,KAAK,YAAY;AAAA,MAAA;AAAA,IAErB;AAEO,WAAA;AAAA,MACL,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,IAAA;AAAA,WAEJ,OAAO;AACdC,kBAAA,MAAc,MAAA,SAAA,gCAAA,aAAa,KAAK;AAEzB,WAAA;AAAA,MACL,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,MACT,YAAY;AAAA,IAAA;AAAA,EAEhB;AACF;AAOA,MAAM,iBAAiB,CAAC,SAAS;AAC/B,MAAI,CAAC;AAAM,WAAO,EAAE,MAAM,MAAM,MAAM,gBAAgB,QAAQ,SAAS;AAGjE,QAAA,YAAY,KAAK;AAGvB,aAAW,QAAQ,iBAAiB;AAC9B,QAAA,UAAU,SAAS,IAAI,GAAG;AACrB,aAAA;AAAA,QACL,MAAM;AAAA,QACN,MAAM,gBAAgB;AAAA,QACtB,SAAS,aAAa,IAAI;AAAA,QAC1B;AAAA,MAAA;AAAA,IAEJ;AAAA,EACF;AAGA,QAAM,aAAa;AACf,MAAA,WAAW,KAAK,IAAI,GAAG;AAElB,WAAA;AAAA,MACL,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,MACT,eAAe;AAAA,IAAA;AAAA,EAEnB;AAIG,MAAA,UAAU,SAAS,IAAI,KAAK,UAAU,SAAS,IAAI,KACnD,UAAU,SAAS,IAAI,KAAK,UAAU,SAAS,IAAI,KACnD,UAAU,SAAS,IAAI,KAAK,UAAU,SAAS,GAAG,KACnD,UAAU,SAAS,MAAM,GACzB;AACO,WAAA;AAAA,MACL,MAAM;AAAA,MACN,MAAM,gBAAgB;AAAA,MACtB,SAAS;AAAA,IAAA;AAAA,EAEb;AAEO,SAAA;AAAA,IACL,MAAM;AAAA,IACN,MAAM,gBAAgB;AAAA,IACtB,SAAS;AAAA,EAAA;AAEb;AAOa,MAAA,eAAe,OAAO,YAAY;AACvC,QAAA,EAAE,MAAM,OAAW,IAAA;AAGzB,QAAM,CAAC,YAAY,WAAW,IAAI,MAAM,QAAQ,IAAI;AAAA,IAClD,UAAU,IAAI;AAAA,IACd,WAAW,MAAM;AAAA,EAAA,CAClB;AAGD,MAAI,CAAC,WAAW,QAAQ,CAAC,YAAY,MAAM;AAClC,WAAA;AAAA,MACL,MAAM;AAAA,MACN,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS,WAAW,OAAO,YAAY,UAAU,WAAW;AAAA,IAAA;AAAA,EAEhE;AAGM,QAAA,aAAa,WAAW,cAAc,YAAY;AAEjD,SAAA;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT;AAAA,EAAA;AAEJ;;"}