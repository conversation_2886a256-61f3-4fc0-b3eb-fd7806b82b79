"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
class ContentApiService {
  /**
   * 获取新闻分类列表
   * @returns {Promise} 分类列表
   */
  async getNewsCategories() {
    try {
      const response = await utils_request.request.get("/content/news/categories");
      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:24", "获取新闻分类失败:", error);
      return {
        success: false,
        data: [],
        message: error.message || "获取分类失败"
      };
    }
  }
  /**
   * 获取新闻列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {number} params.category_id - 分类ID
   * @returns {Promise} 新闻列表
   */
  async getNewsList(params = {}) {
    try {
      const queryParams = {
        page: params.page || 1,
        limit: params.limit || 10,
        category_id: params.category_id || null
      };
      const response = await utils_request.request.get("/content/news", { params: queryParams });
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0,
        page: response.page || 1,
        limit: response.limit || 10
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:59", "获取新闻列表失败:", error);
      return {
        success: false,
        data: [],
        message: error.message || "获取新闻失败"
      };
    }
  }
  /**
   * 获取新闻详情
   * @param {number} newsId - 新闻ID
   * @returns {Promise} 新闻详情
   */
  async getNewsDetail(newsId) {
    try {
      const response = await utils_request.request.get(`/content/news/${newsId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:82", "获取新闻详情失败:", error);
      return {
        success: false,
        message: error.message || "获取新闻详情失败"
      };
    }
  }
  /**
   * 获取信息分类列表
   * @returns {Promise} 分类列表
   */
  async getInfoCategories() {
    try {
      const response = await utils_request.request.get("/content/info/categories");
      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:103", "获取信息分类失败:", error);
      return {
        success: false,
        data: [],
        message: error.message || "获取分类失败"
      };
    }
  }
  /**
   * 获取信息列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {number} params.category_id - 分类ID
   * @param {string} params.type - 信息类型 (all, topped, normal)
   * @returns {Promise} 信息列表
   */
  async getInfoList(params = {}) {
    try {
      const queryParams = {
        page: params.page || 1,
        limit: params.limit || 10,
        category_id: params.category_id || null,
        type: params.type || "all"
      };
      const response = await utils_request.request.get("/content/info", { params: queryParams });
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0,
        page: response.page || 1,
        limit: response.limit || 10
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:140", "获取信息列表失败:", error);
      return {
        success: false,
        data: [],
        message: error.message || "获取信息失败"
      };
    }
  }
  /**
   * 获取置顶信息列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 置顶信息列表
   */
  async getToppedInfo(params = {}) {
    return await this.getInfoList({ ...params, type: "topped" });
  }
  /**
   * 获取信息详情
   * @param {number} infoId - 信息ID
   * @returns {Promise} 信息详情
   */
  async getInfoDetail(infoId) {
    try {
      const response = await utils_request.request.get(`/content/info/${infoId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:172", "获取信息详情失败:", error);
      return {
        success: false,
        message: error.message || "获取信息详情失败"
      };
    }
  }
  /**
   * 发布信息
   * @param {Object} infoData - 信息数据
   * @returns {Promise} 发布结果
   */
  async publishInfo(infoData) {
    try {
      const response = await utils_request.request.post("/content/info", infoData);
      return {
        success: true,
        data: response.data,
        message: "发布成功"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:195", "发布信息失败:", error);
      return {
        success: false,
        message: error.message || "发布失败，请重试"
      };
    }
  }
  /**
   * 获取商家分类列表
   * @returns {Promise} 分类列表
   */
  async getBusinessCategories() {
    try {
      const response = await utils_request.request.get("/content/business/categories");
      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:216", "获取商家分类失败:", error);
      return {
        success: false,
        data: [],
        message: error.message || "获取分类失败"
      };
    }
  }
  /**
   * 获取商家列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {number} params.category_id - 分类ID
   * @returns {Promise} 商家列表
   */
  async getBusinessList(params = {}) {
    try {
      const queryParams = {
        page: params.page || 1,
        limit: params.limit || 10,
        category_id: params.category_id || null
      };
      const response = await utils_request.request.get("/content/business", { params: queryParams });
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0,
        page: response.page || 1,
        limit: response.limit || 10
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:251", "获取商家列表失败:", error);
      return {
        success: false,
        data: [],
        message: error.message || "获取商家失败"
      };
    }
  }
  /**
   * 获取商家详情
   * @param {number} businessId - 商家ID
   * @returns {Promise} 商家详情
   */
  async getBusinessDetail(businessId) {
    try {
      const response = await utils_request.request.get(`/content/business/${businessId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:274", "获取商家详情失败:", error);
      return {
        success: false,
        message: error.message || "获取商家详情失败"
      };
    }
  }
  /**
   * 上传图片
   * @param {string} filePath - 图片路径
   * @param {string} type - 上传类型 (news, info, business, avatar)
   * @returns {Promise} 上传结果
   */
  async uploadImage(filePath, type = "info") {
    try {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: `${utils_request.request.defaults.baseURL}/content/upload`,
          filePath,
          name: "file",
          formData: {
            type
          },
          header: {
            "Authorization": `Bearer ${common_vendor.index.getStorageSync("token")}`
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.success) {
                resolve({
                  success: true,
                  data: data.data,
                  url: data.data.url
                });
              } else {
                reject(new Error(data.message || "上传失败"));
              }
            } catch (error) {
              reject(new Error("上传响应解析失败"));
            }
          },
          fail: (error) => {
            reject(new Error(error.errMsg || "上传失败"));
          }
        });
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:323", "上传图片失败:", error);
      return {
        success: false,
        message: error.message || "上传失败"
      };
    }
  }
  /**
   * 搜索内容
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词
   * @param {string} params.type - 搜索类型 (news, info, business, all)
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @returns {Promise} 搜索结果
   */
  async searchContent(params) {
    try {
      const queryParams = {
        keyword: params.keyword,
        type: params.type || "all",
        page: params.page || 1,
        limit: params.limit || 10
      };
      const response = await utils_request.request.get("/content/search", { params: queryParams });
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0,
        page: response.page || 1,
        limit: response.limit || 10
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at api/contentApi.js:359", "搜索内容失败:", error);
      return {
        success: false,
        data: [],
        message: error.message || "搜索失败"
      };
    }
  }
}
const contentApi = new ContentApiService();
exports.contentApi = contentApi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/contentApi.js.map
