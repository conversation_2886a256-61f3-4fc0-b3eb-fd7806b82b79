"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  ReportCard();
}
const ReportCard = () => "../../components/ReportCard.js";
const _sfc_main = {
  __name: "dating-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const datingId = common_vendor.ref("");
    const isCollected = common_vendor.ref(false);
    const showPosterModal = common_vendor.ref(false);
    const posterUrl = common_vendor.ref("");
    const datingData = common_vendor.reactive({
      id: "1",
      nickname: "阳光男孩",
      gender: "male",
      age: 28,
      height: 180,
      education: "本科",
      maritalStatus: "未婚",
      occupation: "工程师",
      income: "8000-12000",
      selfIntro: "性格开朗，喜欢运动，热爱生活。工作稳定，有上进心，希望找一个志同道合的伴侣一起成长。",
      expectation: "希望对方性格温柔，有一定的生活情趣，年龄25-30岁，身高160cm以上，学历大专以上。",
      interests: ["旅游", "美食", "运动", "电影"],
      images: [
        "/static/images/default-avatar.png",
        "/static/images/default-image.png",
        "/static/images/default-image.png"
      ],
      contact: "张先生",
      phone: "13800138000",
      wechat: "sunshine123",
      publishTime: (/* @__PURE__ */ new Date()).getTime() - 864e5,
      hasRedPacket: true,
      redPacket: {
        type: "random",
        amount: 50,
        remain: 15,
        total: 30
      }
    });
    const relatedDating = common_vendor.reactive([
      {
        id: "2",
        nickname: "甜心女孩",
        age: 26,
        avatar: "/static/images/default-avatar.png",
        education: "研究生",
        occupation: "教师"
      },
      {
        id: "3",
        nickname: "成熟稳重",
        age: 32,
        avatar: "/static/images/default-avatar.png",
        education: "本科",
        occupation: "金融"
      },
      {
        id: "4",
        nickname: "活力四射",
        age: 25,
        avatar: "/static/images/default-avatar.png",
        education: "大专",
        occupation: "设计师"
      }
    ]);
    const goBack = () => common_vendor.index.navigateBack();
    const previewImage = (index) => {
      common_vendor.index.previewImage({
        current: index,
        urls: datingData.images
      });
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({ phoneNumber: datingData.phone });
    };
    const getRedPacketConditionText = () => "查看详情可领取";
    const openRedPacket = () => {
      common_vendor.index.showToast({ title: "红包功能正在开发中", icon: "none" });
    };
    const navigateToDatingDetail = (id) => {
      common_vendor.index.navigateTo({ url: `/pages/publish/dating-detail?id=${id}` });
    };
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      common_vendor.index.showToast({ title: isCollected.value ? "收藏成功" : "取消收藏" });
    };
    const shareToFriend = () => {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        title: `${datingData.nickname}的交友信息`,
        summary: datingData.selfIntro,
        imageUrl: datingData.images[0]
      });
    };
    const contactPerson = () => {
      common_vendor.index.showActionSheet({
        itemList: ["拨打电话", "添加微信"],
        success: (res) => {
          if (res.tapIndex === 0)
            callPhone();
          if (res.tapIndex === 1) {
            common_vendor.index.setClipboardData({
              data: datingData.wechat,
              success: () => common_vendor.index.showToast({ title: "微信号已复制" })
            });
          }
        }
      });
    };
    const generateShareImage = () => {
      common_vendor.index.showLoading({ title: "海报生成中..." });
      common_vendor.index.createCanvasContext("posterCanvas");
      setTimeout(() => {
        posterUrl.value = "/static/images/default-image.png";
        showPosterModal.value = true;
        common_vendor.index.hideLoading();
      }, 1e3);
    };
    const closePosterModal = () => {
      showPosterModal.value = false;
    };
    const savePoster = () => {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: posterUrl.value,
        success: () => common_vendor.index.showToast({ title: "保存成功" }),
        fail: () => common_vendor.index.showToast({ title: "保存失败", icon: "error" })
      });
    };
    common_vendor.onLoad((options) => {
      datingId.value = options.id || "";
    });
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 20;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_assets._imports_10,
        e: common_vendor.o(generateShareImage),
        f: datingData.images[0] || "/static/images/default-avatar.png",
        g: common_vendor.t(datingData.nickname),
        h: common_vendor.t(datingData.age),
        i: common_vendor.t(datingData.gender === "male" ? "男" : "女"),
        j: common_vendor.t(datingData.height),
        k: common_vendor.t(datingData.education),
        l: common_vendor.t(datingData.maritalStatus),
        m: common_vendor.t(datingData.occupation),
        n: common_vendor.t(datingData.income),
        o: common_vendor.f(datingData.images, (image, index, i0) => {
          return {
            a: image,
            b: index,
            c: common_vendor.o(($event) => previewImage(index), index)
          };
        }),
        p: datingData.selfIntro,
        q: datingData.expectation,
        r: common_vendor.f(datingData.interests, (interest, index, i0) => {
          return {
            a: common_vendor.t(interest),
            b: index
          };
        }),
        s: common_vendor.t(datingData.contact),
        t: common_vendor.t(datingData.phone),
        v: common_vendor.o(callPhone),
        w: common_vendor.p({
          ["content-id"]: datingData.id,
          ["content-type"]: "dating"
        }),
        x: datingData.hasRedPacket
      }, datingData.hasRedPacket ? {
        y: common_assets._imports_0$4,
        z: common_vendor.t(datingData.redPacket.type === "random" ? "随机金额红包" : "查看信息领红包"),
        A: common_vendor.t(datingData.redPacket.remain),
        B: common_vendor.t(getRedPacketConditionText()),
        C: common_vendor.t(datingData.redPacket.amount),
        D: common_vendor.o(openRedPacket)
      } : {}, {
        E: common_vendor.f(relatedDating.slice(0, 3), (item, index, i0) => {
          return {
            a: item.avatar,
            b: common_vendor.t(item.nickname),
            c: common_vendor.t(item.age),
            d: common_vendor.t(item.education),
            e: common_vendor.t(item.occupation),
            f: index,
            g: common_vendor.o(($event) => navigateToDatingDetail(item.id), index)
          };
        }),
        F: isCollected.value ? 1 : "",
        G: !isCollected.value ? 1 : "",
        H: common_vendor.t(isCollected.value ? "已收藏" : "收藏"),
        I: common_vendor.o(toggleCollect),
        J: common_vendor.o(shareToFriend),
        K: common_vendor.o(contactPerson),
        L: showPosterModal.value
      }, showPosterModal.value ? {
        M: common_vendor.o(closePosterModal),
        N: posterUrl.value,
        O: common_vendor.o(savePoster),
        P: common_vendor.o(() => {
        }),
        Q: common_vendor.o(closePosterModal)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/dating-detail.js.map
