<template>
  <view class="member-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">会员特权</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 会员系统开关 -->
    <view class="system-switch">
      <view class="switch-content">
        <text class="switch-title">会员系统</text>
        <text class="switch-desc">开启后，用户可以享受会员等级特权</text>
      </view>
      <switch :checked="memberSystemEnabled" @change="toggleMemberSystem" color="#4A00E0" />
    </view>
    
    <!-- 会员数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">会员数据概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{memberData.totalMembers}}</view>
          <view class="card-label">会员总数</view>
          <view class="card-trend" :class="memberData.membersTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{memberData.membersGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">¥{{formatNumber(memberData.memberRevenue)}}</view>
          <view class="card-label">会员消费额</view>
          <view class="card-trend" :class="memberData.revenueTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{memberData.revenueGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{memberData.activeRate}}%</view>
          <view class="card-label">会员活跃率</view>
          <view class="card-trend" :class="memberData.activeRateTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{memberData.activeRateGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{memberData.conversionRate}}%</view>
          <view class="card-label">会员转化率</view>
          <view class="card-trend" :class="memberData.conversionTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{memberData.conversionGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 会员等级展示 -->
    <view class="levels-section">
      <view class="section-header">
        <text class="section-title">会员等级</text>
        <view class="add-btn" @click="navigateToMemberLevel">
          <text class="btn-text">添加等级</text>
          <view class="plus-icon-small"></view>
        </view>
      </view>
      
      <scroll-view scroll-x class="levels-scroll" show-scrollbar="false">
        <view class="levels-container">
          <view class="level-card" v-for="(level, index) in memberLevels" :key="index" @click="editLevel(level)">
            <view class="level-header" :style="{ background: level.color }">
              <text class="level-name">{{level.name}}</text>
              <view class="level-icon">
                <image class="icon-image" :src="level.icon" mode="aspectFit"></image>
              </view>
            </view>
            <view class="level-body">
              <view class="level-stat">
                <text class="stat-value">{{level.memberCount}}</text>
                <text class="stat-label">会员数</text>
              </view>
              <view class="level-condition">
                <text class="condition-label">升级条件:</text>
                <text class="condition-value">{{level.condition}}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 会员特权管理 -->
    <view class="privileges-section">
      <view class="section-header">
        <text class="section-title">会员特权</text>
        <view class="add-btn" @click="navigateToMemberPrivilege">
          <text class="btn-text">添加特权</text>
          <view class="plus-icon-small"></view>
        </view>
      </view>
      
      <view class="privileges-list">
        <view class="privilege-item" v-for="(privilege, index) in memberPrivileges" :key="index" @click="editPrivilege(privilege)">
          <view class="privilege-icon" :class="privilege.type">
            <image class="icon-image" :src="privilege.icon" mode="aspectFit"></image>
          </view>
          <view class="privilege-content">
            <view class="privilege-name">{{privilege.name}}</view>
            <view class="privilege-desc">{{privilege.description}}</view>
            <view class="privilege-levels">
              <text class="level-tag" v-for="(level, levelIndex) in privilege.availableLevels" :key="levelIndex">{{level}}</text>
            </view>
          </view>
          <view class="privilege-status">
            <switch :checked="privilege.enabled" @change="(e) => togglePrivilege(privilege, e)" color="#4A00E0" />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 会员设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">会员设置</text>
      </view>
      
      <view class="settings-list">
        <view class="settings-item" @click="navigateToSetting('points')">
          <view class="item-left">
            <view class="item-icon points"></view>
            <text class="item-title">积分规则</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{memberSettings.pointsRatio}}积分/元</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('growth')">
          <view class="item-left">
            <view class="item-icon growth"></view>
            <text class="item-title">成长值规则</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{memberSettings.growthRatio}}成长值/元</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('birthday')">
          <view class="item-left">
            <view class="item-icon birthday"></view>
            <text class="item-title">生日特权</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{memberSettings.birthdayPrivilege ? '已开启' : '未开启'}}</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('agreement')">
          <view class="item-left">
            <view class="item-icon agreement"></view>
            <text class="item-title">会员协议</text>
          </view>
          <view class="item-right">
            <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 会员营销工具 -->
    <view class="tools-section">
      <view class="section-header">
        <text class="section-title">会员营销工具</text>
      </view>
      
      <view class="tools-grid">
        <view class="tool-card" v-for="(tool, index) in marketingTools" :key="index" @click="useTool(tool)">
          <view class="tool-icon" :style="{ background: tool.color }">
            <image class="icon-image" :src="tool.icon" mode="aspectFit"></image>
          </view>
          <text class="tool-name">{{tool.name}}</text>
          <text class="tool-desc">{{tool.description}}</text>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="showActionMenu">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      memberSystemEnabled: true,
      dateRange: '2023-04-01 ~ 2023-04-30',
      
      // 会员数据概览
      memberData: {
        totalMembers: 3562,
        membersTrend: 'up',
        membersGrowth: '8.2%',
        
        memberRevenue: 156820.50,
        revenueTrend: 'up',
        revenueGrowth: '12.5%',
        
        activeRate: 68.4,
        activeRateTrend: 'up',
        activeRateGrowth: '3.2%',
        
        conversionRate: 35.6,
        conversionTrend: 'down',
        conversionGrowth: '1.8%'
      },
      
      // 会员等级
      memberLevels: [
        {
          id: 1,
          name: '普通会员',
          icon: '/static/images/member-level-1.png',
          color: 'linear-gradient(135deg, #B7B7B7, #8E8E93)',
          memberCount: 2156,
          condition: '注册即可'
        },
        {
          id: 2,
          name: '银卡会员',
          icon: '/static/images/member-level-2.png',
          color: 'linear-gradient(135deg, #D4D4D8, #A1A1AA)',
          memberCount: 864,
          condition: '累计消费满1000元'
        },
        {
          id: 3,
          name: '金卡会员',
          icon: '/static/images/member-level-3.png',
          color: 'linear-gradient(135deg, #FFC837, #FF8008)',
          memberCount: 426,
          condition: '累计消费满5000元'
        },
        {
          id: 4,
          name: '钻石会员',
          icon: '/static/images/member-level-4.png',
          color: 'linear-gradient(135deg, #8E2DE2, #4A00E0)',
          memberCount: 116,
          condition: '累计消费满20000元'
        }
      ],
      
      // 会员特权
      memberPrivileges: [
        {
          id: 1,
          name: '会员折扣',
          description: '会员专享商品折扣',
          icon: '/static/images/discount-icon.png',
          type: 'discount',
          enabled: true,
          availableLevels: ['银卡会员', '金卡会员', '钻石会员']
        },
        {
          id: 2,
          name: '积分加速',
          description: '购物获得更多积分',
          icon: '/static/images/points-icon.png',
          type: 'points',
          enabled: true,
          availableLevels: ['金卡会员', '钻石会员']
        },
        {
          id: 3,
          name: '免费配送',
          description: '会员专享免费配送服务',
          icon: '/static/images/delivery-icon.png',
          type: 'delivery',
          enabled: true,
          availableLevels: ['钻石会员']
        },
        {
          id: 4,
          name: '生日礼包',
          description: '会员生日当月专享礼包',
          icon: '/static/images/gift-icon.png',
          type: 'gift',
          enabled: true,
          availableLevels: ['银卡会员', '金卡会员', '钻石会员']
        },
        {
          id: 5,
          name: '专属客服',
          description: '会员专属客服服务',
          icon: '/static/images/service-icon.png',
          type: 'service',
          enabled: false,
          availableLevels: ['金卡会员', '钻石会员']
        }
      ],
      
      // 会员设置
      memberSettings: {
        pointsRatio: 10,
        growthRatio: 5,
        birthdayPrivilege: true
      },
      
      // 会员营销工具
      marketingTools: [
        {
          id: 1,
          name: '会员积分',
          description: '管理会员积分规则',
          icon: '/static/images/points-icon.png',
          color: 'linear-gradient(135deg, #FF9500, #FF5E3A)',
          path: '/subPackages/merchant-admin-marketing/pages/marketing/member/points'
        },
        {
          id: 2,
          name: '会员任务',
          description: '设置会员任务和奖励',
          icon: '/static/images/task-icon.png',
          color: 'linear-gradient(135deg, #F6D365, #FDA085)',
          path: '/subPackages/merchant-admin-marketing/pages/marketing/member/tasks'
        },
        {
          id: 3,
          name: '会员卡券',
          description: '发放会员专属卡券',
          icon: '/static/images/coupon-icon.png',
          color: 'linear-gradient(135deg, #FF6FD8, #3813C2)',
          path: '/subPackages/merchant-admin-marketing/pages/marketing/member/coupons'
        },
        {
          id: 4,
          name: '会员活动',
          description: '创建会员专属活动',
          icon: '/static/images/activity-icon.png',
          color: 'linear-gradient(135deg, #43E97B, #38F9D7)',
          path: '/subPackages/merchant-admin-marketing/pages/marketing/member/activities'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showHelp() {
      uni.showModal({
        title: '会员特权帮助',
        content: '会员特权是指根据会员等级为用户提供的专属权益，可以提高用户忠诚度和复购率。',
        showCancel: false
      });
    },
    
    formatNumber(number) {
      return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    
    showDatePicker() {
      // 实现日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    
    toggleMemberSystem(e) {
      this.memberSystemEnabled = e.detail.value;
      uni.showToast({
        title: this.memberSystemEnabled ? '会员系统已开启' : '会员系统已关闭',
        icon: 'none'
      });
    },
    
    addMemberLevel() {
      this.navigateToMemberLevel();
    },
    
    editLevel(level) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/edit-level?id=${level.id}`
      });
    },
    
    addPrivilege() {
      this.navigateToMemberPrivilege();
    },
    
    editPrivilege(privilege) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/edit-privilege?id=${privilege.id}`
      });
    },
    
    togglePrivilege(privilege, e) {
      // 更新特权状态
      const index = this.memberPrivileges.findIndex(item => item.id === privilege.id);
      if (index !== -1) {
        this.memberPrivileges[index].enabled = e.detail.value;
      }
      
      uni.showToast({
        title: e.detail.value ? `${privilege.name}已启用` : `${privilege.name}已禁用`,
        icon: 'none'
      });
    },
    
    navigateToSetting(type) {
      const routes = {
        points: '/subPackages/merchant-admin-marketing/pages/marketing/member/points-rule',
        growth: '/subPackages/merchant-admin-marketing/pages/marketing/member/growth-rule',
        birthday: '/subPackages/merchant-admin-marketing/pages/marketing/member/birthday-privilege',
        agreement: '/subPackages/merchant-admin-marketing/pages/marketing/member/member-agreement'
      };
      
      uni.navigateTo({
        url: routes[type]
      });
    },
    
    useTool(tool) {
      if (tool.path) {
        uni.navigateTo({
          url: tool.path
        });
      } else {
        uni.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      }
    },
    
    showActionMenu() {
      uni.showActionSheet({
        itemList: ['会员数据分析', '导出会员数据', '会员标签管理', '会员营销活动'],
        success: (res) => {
          const actions = [
            () => this.analyzeData(),
            () => this.exportData(),
            () => this.manageLabels(),
            () => this.createActivity()
          ];
          
          actions[res.tapIndex]();
        }
      });
    },
    
    analyzeData() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/member/data-analysis'
      });
    },
    
    exportData() {
      uni.showLoading({
        title: '导出中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '会员数据导出成功',
          icon: 'success'
        });
      }, 1500);
    },
    
    manageLabels() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/member/member-labels'
      });
    },
    
    createActivity() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/member/create-activity'
      });
    },
    
    navigateToMemberLevel() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/member/member-level'
      });
    },
    
    navigateToMemberPrivilege() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/member/member-privilege'
      });
    }
  }
}
</script>

<style lang="scss">
.member-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 系统开关样式 */
.system-switch {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-content {
  flex: 1;
}

.switch-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.switch-desc {
  font-size: 12px;
  color: #999;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  background: #4A00E0;
  border-radius: 15px;
  padding: 5px 12px;
  color: white;
}

.btn-text {
  font-size: 13px;
  margin-right: 5px;
}

.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon-small:before,
.plus-icon-small:after {
  content: '';
  position: absolute;
  background: white;
}

.plus-icon-small:before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon-small:after {
  height: 12px;
  width: 2px;
  left: 5px;
  top: 0;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #4A00E0;
}

.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}

.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}

.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 会员等级样式 */
.levels-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.levels-scroll {
  white-space: nowrap;
  margin: 0 -15px;
  padding: 0 15px;
}

.levels-container {
  display: inline-flex;
  padding: 5px 0;
}

.level-card {
  width: 200px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.level-header {
  padding: 15px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.level-name {
  font-size: 16px;
  font-weight: 600;
}

.level-icon {
  width: 30px;
  height: 30px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-image {
  width: 20px;
  height: 20px;
}

.level-body {
  padding: 15px;
  background: white;
}

.level-stat {
  margin-bottom: 10px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.level-condition {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}

.condition-label {
  color: #999;
  margin-right: 5px;
}

.condition-value {
  color: #4A00E0;
}

/* 会员特权样式 */
.privileges-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.privileges-list {
  margin-top: 10px;
}

.privilege-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.privilege-item:last-child {
  border-bottom: none;
}

.privilege-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.privilege-icon.discount {
  background: rgba(255, 69, 58, 0.1);
}

.privilege-icon.points {
  background: rgba(255, 149, 0, 0.1);
}

.privilege-icon.delivery {
  background: rgba(52, 199, 89, 0.1);
}

.privilege-icon.gift {
  background: rgba(255, 45, 85, 0.1);
}

.privilege-icon.service {
  background: rgba(0, 122, 255, 0.1);
}

.privilege-content {
  flex: 1;
}

.privilege-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
}

.privilege-desc {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.privilege-levels {
  display: flex;
  flex-wrap: wrap;
}

.level-tag {
  font-size: 10px;
  color: #4A00E0;
  background: rgba(74, 0, 224, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 5px;
  margin-bottom: 3px;
}

.privilege-status {
  margin-left: 15px;
}

/* 设置部分样式 */
.settings-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.settings-list {
  margin-top: 10px;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.item-icon.points {
  background: rgba(255, 149, 0, 0.1);
}

.item-icon.points::before {
  content: 'P';
  color: #FF9500;
  font-weight: bold;
  font-size: 16px;
}

.item-icon.growth {
  background: rgba(52, 199, 89, 0.1);
}

.item-icon.growth::before {
  content: 'G';
  color: #34C759;
  font-weight: bold;
  font-size: 16px;
}

.item-icon.birthday {
  background: rgba(255, 45, 85, 0.1);
}

.item-icon.birthday::before {
  content: 'B';
  color: #FF2D55;
  font-weight: bold;
  font-size: 16px;
}

.item-icon.agreement {
  background: rgba(88, 86, 214, 0.1);
}

.item-icon.agreement::before {
  content: 'A';
  color: #5856D6;
  font-weight: bold;
  font-size: 16px;
}

.item-title {
  font-size: 15px;
  color: #333;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 14px;
  color: #999;
  margin-right: 10px;
}

.item-arrow {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #999;
  border-right: 1.5px solid #999;
  transform: rotate(45deg);
}

/* 营销工具样式 */
.tools-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.tools-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.tool-card {
  width: 25%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tool-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.tool-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 3px;
  text-align: center;
}

.tool-desc {
  font-size: 10px;
  color: #999;
  text-align: center;
  height: 28px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  box-shadow: 0 4px 15px rgba(74, 0, 224, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .tool-card {
    width: 33.33%;
  }
}

@media screen and (max-width: 320px) {
  .stats-card {
    width: 100%;
  }
  
  .tool-card {
    width: 50%;
  }
}
</style> 