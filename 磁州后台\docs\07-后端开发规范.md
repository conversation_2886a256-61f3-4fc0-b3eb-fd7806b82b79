# 磁州生活网后台管理系统 - 后端开发规范

## 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025-01-04
- **文档类型**: 后端开发规范
- **目标读者**: 后端开发工程师、架构师

## 1. 项目结构规范

### 1.1 Maven多模块结构
```
cizhou-admin-backend/
├── cizhou-admin-parent/              # 父项目
│   └── pom.xml                      # 依赖管理
├── cizhou-admin-common/             # 公共模块
│   ├── common-core/                 # 核心工具类
│   ├── common-security/             # 安全组件
│   ├── common-redis/                # Redis组件
│   ├── common-swagger/              # API文档组件
│   └── common-log/                  # 日志组件
├── cizhou-admin-gateway/            # API网关
├── cizhou-admin-auth/               # 认证服务
└── cizhou-admin-modules/            # 业务模块
    ├── user-service/                # 用户服务
    ├── content-service/             # 内容服务
    ├── merchant-service/            # 商家服务
    ├── carpool-service/             # 拼车服务
    ├── cashback-service/            # 返利服务
    ├── payment-service/             # 支付服务
    ├── message-service/             # 消息服务
    └── file-service/                # 文件服务
```

### 1.2 单个服务模块结构
```
user-service/
├── src/main/java/com/cizhou/user/
│   ├── controller/                  # 控制器层
│   │   ├── UserController.java
│   │   └── RoleController.java
│   ├── service/                     # 服务层
│   │   ├── UserService.java
│   │   ├── impl/
│   │   │   └── UserServiceImpl.java
│   │   └── RoleService.java
│   ├── mapper/                      # 数据访问层
│   │   ├── UserMapper.java
│   │   └── RoleMapper.java
│   ├── entity/                      # 实体类
│   │   ├── User.java
│   │   └── Role.java
│   ├── dto/                         # 数据传输对象
│   │   ├── request/
│   │   │   ├── UserCreateRequest.java
│   │   │   └── UserUpdateRequest.java
│   │   └── response/
│   │       └── UserResponse.java
│   ├── config/                      # 配置类
│   │   └── UserServiceConfig.java
│   ├── exception/                   # 异常类
│   │   └── UserNotFoundException.java
│   └── UserServiceApplication.java  # 启动类
├── src/main/resources/
│   ├── mapper/                      # MyBatis映射文件
│   │   ├── UserMapper.xml
│   │   └── RoleMapper.xml
│   ├── application.yml              # 配置文件
│   └── bootstrap.yml                # 启动配置
└── pom.xml                          # 项目依赖
```

## 2. 编码规范

### 2.1 命名规范
- **包名**: 全小写，使用点分隔 (com.cizhou.user.service)
- **类名**: PascalCase (UserService, UserController)
- **方法名**: camelCase (getUserById, createUser)
- **变量名**: camelCase (userId, userName)
- **常量名**: UPPER_SNAKE_CASE (MAX_RETRY_COUNT)

### 2.2 Controller层规范

#### 2.2.1 控制器基础结构
```java
@RestController
@RequestMapping("/api/admin/v1/users")
@Api(tags = "用户管理")
@Slf4j
@Validated
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping
    @ApiOperation("获取用户列表")
    @PreAuthorize("hasPermission('user:read')")
    public Result<PageResponse<UserResponse>> getUserList(
            @Valid UserListRequest request) {
        PageResponse<UserResponse> response = userService.getUserList(request);
        return Result.success(response);
    }

    @PostMapping
    @ApiOperation("创建用户")
    @PreAuthorize("hasPermission('user:create')")
    public Result<UserResponse> createUser(
            @Valid @RequestBody UserCreateRequest request) {
        UserResponse response = userService.createUser(request);
        return Result.success(response);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新用户")
    @PreAuthorize("hasPermission('user:update')")
    public Result<UserResponse> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UserUpdateRequest request) {
        UserResponse response = userService.updateUser(id, request);
        return Result.success(response);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除用户")
    @PreAuthorize("hasPermission('user:delete')")
    public Result<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return Result.success();
    }
}
```

#### 2.2.2 统一响应结果
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {
    private Integer code;
    private String message;
    private T data;
    private String timestamp;
    private String traceId;

    public static <T> Result<T> success() {
        return success(null);
    }

    public static <T> Result<T> success(T data) {
        return Result.<T>builder()
                .code(200)
                .message("success")
                .data(data)
                .timestamp(Instant.now().toString())
                .traceId(MDC.get("traceId"))
                .build();
    }

    public static <T> Result<T> error(Integer code, String message) {
        return Result.<T>builder()
                .code(code)
                .message(message)
                .timestamp(Instant.now().toString())
                .traceId(MDC.get("traceId"))
                .build();
    }
}
```

### 2.3 Service层规范

#### 2.3.1 服务接口定义
```java
public interface UserService {
    
    /**
     * 获取用户列表
     * @param request 查询请求
     * @return 用户列表
     */
    PageResponse<UserResponse> getUserList(UserListRequest request);
    
    /**
     * 根据ID获取用户
     * @param id 用户ID
     * @return 用户信息
     * @throws UserNotFoundException 用户不存在
     */
    UserResponse getUserById(Long id);
    
    /**
     * 创建用户
     * @param request 创建请求
     * @return 用户信息
     * @throws UserExistsException 用户已存在
     */
    UserResponse createUser(UserCreateRequest request);
    
    /**
     * 更新用户
     * @param id 用户ID
     * @param request 更新请求
     * @return 用户信息
     * @throws UserNotFoundException 用户不存在
     */
    UserResponse updateUser(Long id, UserUpdateRequest request);
    
    /**
     * 删除用户
     * @param id 用户ID
     * @throws UserNotFoundException 用户不存在
     */
    void deleteUser(Long id);
}
```

#### 2.3.2 服务实现类
```java
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final RedisTemplate<String, Object> redisTemplate;

    public UserServiceImpl(UserMapper userMapper, 
                          PasswordEncoder passwordEncoder,
                          RedisTemplate<String, Object> redisTemplate) {
        this.userMapper = userMapper;
        this.passwordEncoder = passwordEncoder;
        this.redisTemplate = redisTemplate;
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<UserResponse> getUserList(UserListRequest request) {
        log.info("获取用户列表, 请求参数: {}", request);
        
        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .like(StringUtils.hasText(request.getKeyword()), 
                      User::getUsername, request.getKeyword())
                .eq(request.getStatus() != null, 
                    User::getStatus, request.getStatus())
                .between(request.getStartDate() != null && request.getEndDate() != null,
                        User::getCreatedAt, request.getStartDate(), request.getEndDate())
                .orderByDesc(User::getCreatedAt);

        // 分页查询
        Page<User> page = new Page<>(request.getPage(), request.getSize());
        Page<User> userPage = userMapper.selectPage(page, queryWrapper);

        // 转换响应对象
        List<UserResponse> userResponses = userPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        return PageResponse.<UserResponse>builder()
                .total(userPage.getTotal())
                .page(request.getPage())
                .size(request.getSize())
                .pages(userPage.getPages())
                .records(userResponses)
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public UserResponse getUserById(Long id) {
        log.info("获取用户详情, ID: {}", id);
        
        // 先从缓存获取
        String cacheKey = "user:" + id;
        UserResponse cached = (UserResponse) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }

        // 从数据库获取
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new UserNotFoundException("用户不存在: " + id);
        }

        UserResponse response = convertToResponse(user);
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, response, Duration.ofMinutes(30));
        
        return response;
    }

    @Override
    public UserResponse createUser(UserCreateRequest request) {
        log.info("创建用户, 请求参数: {}", request);
        
        // 检查用户名是否存在
        if (userMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, request.getUsername())) != null) {
            throw new UserExistsException("用户名已存在: " + request.getUsername());
        }

        // 构建用户对象
        User user = User.builder()
                .username(request.getUsername())
                .password(passwordEncoder.encode(request.getPassword()))
                .nickname(request.getNickname())
                .phone(request.getPhone())
                .email(request.getEmail())
                .userType(request.getUserType())
                .status(UserStatus.ENABLED)
                .createdAt(LocalDateTime.now())
                .build();

        // 保存用户
        userMapper.insert(user);
        
        log.info("用户创建成功, ID: {}", user.getId());
        return convertToResponse(user);
    }

    private UserResponse convertToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .phone(user.getPhone())
                .email(user.getEmail())
                .avatar(user.getAvatar())
                .status(user.getStatus())
                .userType(user.getUserType())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .build();
    }
}
```

### 2.4 Entity层规范

#### 2.4.1 实体类定义
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("users")
@ApiModel("用户实体")
public class User {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("性别")
    private Gender gender;

    @ApiModelProperty("生日")
    private LocalDate birthday;

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("状态")
    private UserStatus status;

    @ApiModelProperty("用户类型")
    private UserType userType;

    @ApiModelProperty("会员等级ID")
    private Long levelId;

    @ApiModelProperty("积分")
    private Integer points;

    @ApiModelProperty("余额")
    private BigDecimal balance;

    @ApiModelProperty("最后登录时间")
    private LocalDateTime lastLoginTime;

    @ApiModelProperty("最后登录IP")
    private String lastLoginIp;

    @TableLogic
    @ApiModelProperty("删除标记")
    private Boolean deleted;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;
}
```

#### 2.4.2 枚举类定义
```java
@Getter
@AllArgsConstructor
public enum UserStatus {
    DISABLED(0, "禁用"),
    ENABLED(1, "正常"),
    PENDING(2, "待审核");

    private final Integer code;
    private final String description;

    public static UserStatus fromCode(Integer code) {
        for (UserStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的用户状态: " + code);
    }
}
```

### 2.5 DTO层规范

#### 2.5.1 请求DTO
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("用户创建请求")
public class UserCreateRequest {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 8, max = 20, message = "密码长度必须在8-20之间")
    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @NotBlank(message = "昵称不能为空")
    @Size(max = 50, message = "昵称长度不能超过50")
    @ApiModelProperty(value = "昵称", required = true)
    private String nickname;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @ApiModelProperty("手机号")
    private String phone;

    @Email(message = "邮箱格式不正确")
    @ApiModelProperty("邮箱")
    private String email;

    @NotNull(message = "用户类型不能为空")
    @ApiModelProperty(value = "用户类型", required = true)
    private UserType userType;
}
```

#### 2.5.2 响应DTO
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("用户响应")
public class UserResponse {

    @ApiModelProperty("用户ID")
    private Long id;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("状态")
    private UserStatus status;

    @ApiModelProperty("用户类型")
    private UserType userType;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
```

### 2.6 异常处理规范

#### 2.6.1 自定义异常
```java
@Getter
public class BusinessException extends RuntimeException {
    private final Integer code;
    private final String message;

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }
}

public class UserNotFoundException extends BusinessException {
    public UserNotFoundException(String message) {
        super(404, message);
    }
}

public class UserExistsException extends BusinessException {
    public UserExistsException(String message) {
        super(409, message);
    }
}
```

#### 2.6.2 全局异常处理
```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.warn("参数验证失败: {}", message);
        return Result.error(400, message);
    }

    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error(500, "系统内部错误");
    }
}
```

## 3. 配置规范

### 3.1 应用配置
```yaml
# application.yml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: user-service
  profiles:
    active: dev
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************
    username: ${DB_USERNAME:cizhou}
    password: ${DB_PASSWORD:cizhou123}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000

  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER:localhost:8848}
        namespace: ${NACOS_NAMESPACE:dev}
      config:
        server-addr: ${NACOS_SERVER:localhost:8848}
        namespace: ${NACOS_NAMESPACE:dev}
        file-extension: yml

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  level:
    com.cizhou: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
```

### 3.2 安全配置
```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAccessDeniedHandler jwtAccessDeniedHandler;
    private final JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                .anyRequest().authenticated()
            )
            .exceptionHandling()
                .authenticationEntryPoint(jwtAuthenticationEntryPoint)
                .accessDeniedHandler(jwtAccessDeniedHandler)
            .and()
            .addFilterBefore(jwtAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

## 4. 测试规范

### 4.1 单元测试
```java
@ExtendWith(MockitoExtension.class)
class UserServiceImplTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @InjectMocks
    private UserServiceImpl userService;

    @Test
    @DisplayName("根据ID获取用户 - 成功")
    void getUserById_Success() {
        // Given
        Long userId = 1L;
        User user = User.builder()
                .id(userId)
                .username("testuser")
                .nickname("测试用户")
                .build();

        when(userMapper.selectById(userId)).thenReturn(user);

        // When
        UserResponse result = userService.getUserById(userId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(userId);
        assertThat(result.getUsername()).isEqualTo("testuser");
        assertThat(result.getNickname()).isEqualTo("测试用户");

        verify(userMapper).selectById(userId);
    }

    @Test
    @DisplayName("根据ID获取用户 - 用户不存在")
    void getUserById_UserNotFound() {
        // Given
        Long userId = 999L;
        when(userMapper.selectById(userId)).thenReturn(null);

        // When & Then
        assertThatThrownBy(() -> userService.getUserById(userId))
                .isInstanceOf(UserNotFoundException.class)
                .hasMessage("用户不存在: " + userId);

        verify(userMapper).selectById(userId);
    }
}
```

### 4.2 集成测试
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class UserControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserMapper userMapper;

    @Test
    @DisplayName("创建用户 - 集成测试")
    void createUser_IntegrationTest() {
        // Given
        UserCreateRequest request = UserCreateRequest.builder()
                .username("newuser")
                .password("password123")
                .nickname("新用户")
                .phone("13800138000")
                .email("<EMAIL>")
                .userType(UserType.NORMAL)
                .build();

        // When
        ResponseEntity<Result> response = restTemplate.postForEntity(
                "/api/admin/v1/users", request, Result.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getCode()).isEqualTo(200);

        // 验证数据库中的数据
        User savedUser = userMapper.selectOne(
                new LambdaQueryWrapper<User>()
                        .eq(User::getUsername, "newuser"));
        assertThat(savedUser).isNotNull();
        assertThat(savedUser.getNickname()).isEqualTo("新用户");
    }
}
```

---

**文档状态**: 后端开发规范完成，待团队评审
**下一步**: 部署运维文档
