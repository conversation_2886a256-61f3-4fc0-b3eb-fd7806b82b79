/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s;
}
.icon-button.small {
  padding: 6px 12px;
  font-size: 12px;
}
.icon-button.medium {
  padding: 8px 16px;
  font-size: 14px;
}
.icon-button.large {
  padding: 10px 20px;
  font-size: 16px;
}
.icon-button.default {
  background-color: #F5F5F5;
  color: #333;
}
.icon-button.default:active {
  background-color: #E5E5E5;
}
.icon-button.primary {
  background-color: #0A84FF;
  color: white;
}
.icon-button.primary:active {
  background-color: #0071E3;
}
.icon-button.success {
  background-color: #34C759;
  color: white;
}
.icon-button.success:active {
  background-color: #30B352;
}
.icon-button.warning {
  background-color: #FF9500;
  color: white;
}
.icon-button.warning:active {
  background-color: #E68600;
}
.icon-button.danger {
  background-color: #FF3B30;
  color: white;
}
.icon-button.danger:active {
  background-color: #E63028;
}
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
}
.button-text {
  font-weight: 500;
}