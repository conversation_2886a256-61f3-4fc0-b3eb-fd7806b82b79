<template>
  <view class="group-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">拼团活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 拼团系统开关 -->
    <view class="system-switch">
      <view class="switch-content">
        <text class="switch-title">拼团系统</text>
        <text class="switch-desc">开启后，用户可以参与商品拼团活动</text>
      </view>
      <switch :checked="groupSystemEnabled" @change="toggleGroupSystem" color="#7E30E1" />
    </view>
    
    <!-- 拼团数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">拼团数据概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{groupData.totalGroups}}</view>
          <view class="card-label">拼团总数</view>
          <view class="card-trend" :class="groupData.groupsTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{groupData.groupsGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{groupData.successRate}}%</view>
          <view class="card-label">成团率</view>
          <view class="card-trend" :class="groupData.successRateTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{groupData.successRateGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">¥{{formatNumber(groupData.totalRevenue)}}</view>
          <view class="card-label">拼团收入</view>
          <view class="card-trend" :class="groupData.revenueTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{groupData.revenueGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{groupData.participantsCount}}</view>
          <view class="card-label">参与人数</view>
          <view class="card-trend" :class="groupData.participantsTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{groupData.participantsGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 进行中的拼团活动 -->
    <view class="active-groups-section">
      <view class="section-header">
        <text class="section-title">进行中的拼团</text>
        <text class="view-all" @click="viewAllGroups">查看全部</text>
      </view>
      
      <view class="group-list">
        <view class="group-item" v-for="(group, index) in activeGroups" :key="index" @click="viewGroupDetail(group)">
          <image class="group-image" :src="group.image" mode="aspectFill"></image>
          <view class="group-content">
            <view class="group-name">{{group.name}}</view>
            <view class="group-info">
              <view class="info-item">
                <text class="info-label">原价：</text>
                <text class="info-value original-price">¥{{group.originalPrice}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">拼团价：</text>
                <text class="info-value group-price">¥{{group.groupPrice}}</text>
              </view>
              <view class="info-item">
                <text class="info-label">成团人数：</text>
                <text class="info-value">{{group.requiredMembers}}人</text>
              </view>
              <view class="info-item">
                <text class="info-label">剩余时间：</text>
                <text class="info-value time-left">{{group.timeLeft}}</text>
              </view>
            </view>
            <view class="group-progress">
              <view class="progress-text">
                <text>已参与：{{group.currentMembers}}/{{group.requiredMembers}}人</text>
                <text>{{group.progressPercent}}%</text>
              </view>
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: group.progressPercent + '%' }"></view>
              </view>
            </view>
          </view>
          <view class="group-status" :class="group.statusClass">{{group.statusText}}</view>
        </view>
      </view>
    </view>
    
    <!-- 拼团设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">拼团设置</text>
      </view>
      
      <view class="settings-list">
        <view class="settings-item" @click="navigateToSetting('rules')">
          <view class="item-left">
            <view class="item-icon rules"></view>
            <text class="item-title">拼团规则</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{groupSettings.rulesCount}}项规则</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('time')">
          <view class="item-left">
            <view class="item-icon time"></view>
            <text class="item-title">拼团时间</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{groupSettings.timeLimit}}小时</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('discount')">
          <view class="item-left">
            <view class="item-icon discount"></view>
            <text class="item-title">拼团折扣</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{groupSettings.discountRange}}</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('notification')">
          <view class="item-left">
            <view class="item-icon notification"></view>
            <text class="item-title">通知设置</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{groupSettings.notificationEnabled ? '已开启' : '未开启'}}</text>
            <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 拼团工具 -->
    <view class="tools-section">
      <view class="section-header">
        <text class="section-title">拼团工具</text>
      </view>
      
      <view class="tools-grid">
        <view class="tool-card" v-for="(tool, index) in groupTools" :key="index" @click="useTool(tool)">
          <view class="tool-icon" :style="{ background: tool.color }">
            <view class="tool-icon-svg" v-html="tool.svg"></view>
          </view>
          <text class="tool-name">{{tool.name}}</text>
          <text class="tool-desc">{{tool.description}}</text>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="createNewGroup">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      groupSystemEnabled: true,
      dateRange: '2023-04-01 ~ 2023-04-30',
      
      // 拼团数据概览
      groupData: {
        totalGroups: 356,
        groupsTrend: 'up',
        groupsGrowth: '15.2%',
        
        successRate: 78.6,
        successRateTrend: 'up',
        successRateGrowth: '3.8%',
        
        totalRevenue: 42680.50,
        revenueTrend: 'up',
        revenueGrowth: '12.5%',
        
        participantsCount: 1268,
        participantsTrend: 'up',
        participantsGrowth: '18.3%'
      },
      
      // 进行中的拼团活动
      activeGroups: [
        {
          id: 1,
          name: '苹果 iPhone 14 Pro Max',
          image: '/static/images/products/iphone.jpg',
          originalPrice: '9999.00',
          groupPrice: '9499.00',
          requiredMembers: 5,
          currentMembers: 3,
          progressPercent: 60,
          timeLeft: '23小时56分',
          statusText: '进行中',
          statusClass: 'active'
        },
        {
          id: 2,
          name: 'Apple Watch Series 8',
          image: '/static/images/products/watch.jpg',
          originalPrice: '3299.00',
          groupPrice: '2999.00',
          requiredMembers: 3,
          currentMembers: 2,
          progressPercent: 67,
          timeLeft: '12小时34分',
          statusText: '进行中',
          statusClass: 'active'
        },
        {
          id: 3,
          name: 'AirPods Pro 2代',
          image: '/static/images/products/airpods.jpg',
          originalPrice: '1999.00',
          groupPrice: '1799.00',
          requiredMembers: 4,
          currentMembers: 1,
          progressPercent: 25,
          timeLeft: '2天5小时',
          statusText: '进行中',
          statusClass: 'active'
        },
        {
          id: 4,
          name: 'MacBook Air M2',
          image: '/static/images/products/macbook.jpg',
          originalPrice: '7999.00',
          groupPrice: '7599.00',
          requiredMembers: 3,
          currentMembers: 3,
          progressPercent: 100,
          timeLeft: '已成团',
          statusText: '已成团',
          statusClass: 'success'
        }
      ],
      
      // 拼团设置
      groupSettings: {
        rulesCount: 5,
        timeLimit: 24,
        discountRange: '10% - 30%',
        notificationEnabled: true
      },
      
      // 拼团工具
      groupTools: [
        {
          name: '拼团分析',
          description: '分析拼团活动的效果',
          color: '#7E30E1',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4-8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4zm-2 0c0-1.1-.9-2-2-2s-2 .9-2 2 .9 2 2 2 2-.9 2-2 2z"/></svg>'
        },
        {
          name: '拼团推广',
          description: '推广拼团活动',
          color: '#34C759',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm4 10c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4zm0-6c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/></svg>'
        },
        {
          name: '拼团管理',
          description: '管理拼团活动',
          color: '#FF3B30',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8zm4 10c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4 4zm0-6c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/></svg>'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '拼团活动帮助',
        icon: 'none'
      });
    },
    toggleGroupSystem(e) {
      this.groupSystemEnabled = e.detail.value;
      uni.showToast({
        title: this.groupSystemEnabled ? '已开启拼团系统' : '已关闭拼团系统',
        icon: 'none'
      });
    },
    showDatePicker() {
      // 显示日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    formatNumber(num) {
      return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    },
    viewAllGroups() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin/pages/marketing/group/list'
      });
    },
    viewGroupDetail(group) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin/pages/marketing/group/detail?id=${group.id}`
      });
    },
    navigateToSetting(setting) {
      // 实现导航到设置页面的逻辑
      uni.showToast({
        title: `导航到${setting}设置页面`,
        icon: 'none'
      });
    },
    useTool(tool) {
      // 实现使用工具的逻辑
      uni.showToast({
        title: `使用${tool.name}工具`,
        icon: 'none'
      });
    },
    createNewGroup() {
      // 实现创建新拼团活动的逻辑
      uni.showToast({
        title: '创建新拼团活动',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.group-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 系统开关样式 */
.system-switch {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-content {
  flex: 1;
}

.switch-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.switch-desc {
  font-size: 12px;
  color: #999;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 数据卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
}

.card-value {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border-left: 3px solid #7E30E1;
  margin-bottom: 5px;
}

.card-label {
  font-size: 12px;
  color: #999;
  padding-left: 3px;
}

.card-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-top: 3px;
  padding-left: 3px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}

.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 进行中的拼团活动样式 */
.active-groups-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.view-all {
  font-size: 12px;
  color: #7E30E1;
  text-decoration: underline;
}

.group-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.group-item {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.group-image {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  margin-right: 10px;
}

.group-content {
  flex: 1;
}

.group-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.group-info {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 5px;
}

.info-label {
  font-size: 12px;
  color: #999;
}

.info-value {
  font-size: 12px;
  color: #333;
}

.original-price {
  color: #FF3B30;
}

.group-price {
  color: #34C759;
}

.time-left {
  color: #7E30E1;
}

.group-progress {
  margin-top: 5px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.progress-bar {
  height: 5px;
  background-color: #F5F7FA;
  border-radius: 2.5px;
  margin-top: 5px;
}

.progress-fill {
  height: 100%;
  background-color: #7E30E1;
  border-radius: 2.5px;
}

.group-status {
  font-size: 12px;
  color: #333;
  padding: 5px 10px;
  border-radius: 10px;
  margin-left: 10px;
}

.group-status.active {
  background-color: #34C759;
  color: #FFFFFF;
}

.group-status.inactive {
  background-color: #FF3B30;
  color: #FFFFFF;
}

/* 拼团设置样式 */
.settings-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.settings-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.settings-item {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 7.5px;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-icon.rules {
  background-color: rgba(126, 48, 225, 0.1);
  position: relative;
}

.item-icon.rules::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #7E30E1;
  border-radius: 2px;
}

.item-icon.time {
  background-color: rgba(52, 199, 89, 0.1);
  position: relative;
}

.item-icon.time::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #34C759;
  border-radius: 6px;
}

.item-icon.discount {
  background-color: rgba(255, 59, 48, 0.1);
  position: relative;
}

.item-icon.discount::before {
  content: '%';
  color: #FF3B30;
  font-size: 14px;
  font-weight: bold;
}

.item-icon.notification {
  background-color: rgba(0, 122, 255, 0.1);
  position: relative;
}

.item-icon.notification::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #007AFF;
  border-radius: 6px;
  position: relative;
}

.item-icon.notification::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: #007AFF;
  border-radius: 3px;
  top: 9px;
  left: 9px;
}

.item-title {
  font-size: 12px;
  color: #333;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 12px;
  color: #333;
  margin-right: 10px;
}

.item-arrow {
  width: 8px;
  height: 8px;
  border-top: 2px solid #333;
  border-right: 2px solid #333;
  transform: rotate(45deg);
}

/* 拼团工具样式 */
.tools-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.tools-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.tool-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 7.5px;
}

.tool-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  background-color: #7E30E1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.tool-icon-svg {
  width: 24px;
  height: 24px;
}

.tool-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.tool-desc {
  font-size: 12px;
  color: #999;
}

/* 浮动操作按钮样式 */
.floating-action-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  background-color: #7E30E1;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.fab-icon {
  font-size: 24px;
  font-weight: bold;
  color: #FFFFFF;
}
</style> 