/**
 * 红包系统工具函数
 */

// 红包状态常量
export const RED_PACKET_STATUS = {
  ACTIVE: 0,           // 进行中
  FINISHED: 1,         // 已领完
  EXPIRED: 2           // 已过期
};

// 红包类型常量
export const RED_PACKET_TYPE = {
  NORMAL: 'normal',    // 普通红包
  LUCKY: 'lucky',      // 拼手气红包
  FIXED: 'fixed',      // 固定金额红包
  CONSUME: 'consume'   // 消费满额红包
};

// 消费满额红包配置
export const CONSUME_RED_PACKET_CONFIG = {
  THRESHOLD: 100,      // 消费门槛（元）
  MIN_AMOUNT: 1,       // 最小红包金额
  MAX_AMOUNT: 88       // 最大红包金额
};

import request from './request';

/**
 * 获取商家红包列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.status 红包状态
 * @param {string} params.type 红包类型
 * @returns {Promise<Object>} 红包列表数据
 */
export async function getMerchantRedPackets(params) {
  try {
    const res = await request({
      url: '/api/redpacket/merchant',
      method: 'GET',
      data: params
    });
    return res.data;
  } catch (error) {
    console.error('获取商家红包列表失败:', error);
    throw error;
  }
}

/**
 * 获取我的红包列表
 * @param {Object} params 查询参数
 * @param {string} params.type 红包类型：received-收到的红包，sent-发出的红包
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @returns {Promise<Object>} 红包列表数据
 */
export async function getMyRedPackets(params) {
  try {
    const res = await request({
      url: '/api/redpacket/my',
      method: 'GET',
      data: params
    });
    return res.data;
  } catch (error) {
    console.error('获取红包列表失败:', error);
    throw error;
  }
}

/**
 * 获取红包详情
 * @param {string} id 红包ID
 * @returns {Promise<Object>} 红包详情数据
 */
export async function getRedPacketDetail(id) {
  try {
    const res = await request({
      url: `/api/redpacket/${id}`,
      method: 'GET'
    });
    return res.data;
  } catch (error) {
    console.error('获取红包详情失败:', error);
    throw error;
  }
}

/**
 * 抢红包
 * @param {string} id 红包ID
 * @returns {Promise<Object>} 抢红包结果
 */
export async function grabRedPacket(id) {
  try {
    const res = await request({
      url: `/api/redpacket/${id}/grab`,
      method: 'POST'
    });
    return res.data;
  } catch (error) {
    console.error('抢红包失败:', error);
    throw error;
  }
}

/**
 * 创建红包
 * @param {Object} data 红包数据
 * @returns {Promise<Object>} 创建结果
 */
export async function createRedPacket(data) {
  try {
    const res = await request({
      url: '/api/redpacket',
      method: 'POST',
      data
    });
    return res.data;
  } catch (error) {
    console.error('创建红包失败:', error);
    throw error;
  }
}

/**
 * 格式化红包金额
 * @param {number} amount 金额
 * @returns {string} 格式化后的金额
 */
export function formatRedPacketAmount(amount) {
  if (typeof amount !== 'number') return '0.00';
  return (amount / 100).toFixed(2);
}

/**
 * 获取红包状态文本
 * @param {string} status 状态码
 * @returns {string} 状态文本
 */
export function getRedPacketStatusText(status) {
  const statusMap = {
    'pending': '待领取',
    'active': '进行中',
    'completed': '已领完',
    'expired': '已过期',
    'closed': '已关闭'
  };
  return statusMap[status] || '未知状态';
}

/**
 * 获取红包类型文本
 * @param {string} type 类型码
 * @returns {string} 类型文本
 */
export function getRedPacketTypeText(type) {
  const typeMap = {
    'normal': '普通红包',
    'lucky': '拼手气红包',
    'fixed': '固定金额红包',
    'merchant': '商家红包'
  };
  return typeMap[type] || '未知类型';
}

/**
 * 检查红包是否可抢
 * @param {Object} redPacket 红包对象
 * @returns {boolean} 是否可抢
 */
export function isRedPacketGrabable(redPacket) {
  if (!redPacket) return false;
  
  const now = Date.now();
  const startTime = new Date(redPacket.startTime).getTime();
  const endTime = new Date(redPacket.endTime).getTime();
  
  return (
    redPacket.status === 'active' &&
    now >= startTime &&
    now <= endTime &&
    redPacket.remainCount > 0
  );
}

/**
 * 计算红包状态
 * @param {Object} redPacket 红包数据
 * @returns {Number} 返回红包状态
 */
export function calculateRedPacketStatus(redPacket) {
  const now = Date.now();
  
  // 已过期
  if (redPacket.expireTime && now > redPacket.expireTime) {
    return RED_PACKET_STATUS.EXPIRED;
  }
  
  // 已领完
  if (redPacket.remainCount === 0) {
    return RED_PACKET_STATUS.FINISHED;
  }
  
  // 默认为进行中
  return RED_PACKET_STATUS.ACTIVE;
}

/**
 * 获取用户红包记录
 * @param {Object} params 查询参数
 * @returns {Promise} 返回红包记录
 */
export function getUserRedPacketRecords(params) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: '/api/red-packet/user-records',
      method: 'GET',
      data: params,
      success: (res) => {
        if (res.data.code === 0) {
          resolve(res.data.data);
        } else {
          reject(new Error(res.data.message || '获取红包记录失败'));
        }
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '网络请求失败'));
      }
    });
  });
}

/**
 * 检查用户是否有资格参与消费红包活动
 * @param {string} userId 用户ID
 * @param {string} merchantId 商家ID
 * @returns {Promise<boolean>} 是否有资格
 */
export async function checkConsumeRedPacketEligibility(userId, merchantId) {
  try {
    // 查询用户在该商家的消费记录
    const consumeAmount = await getConsumeAmount(userId, merchantId);
    
    // 判断是否达到门槛
    return consumeAmount >= CONSUME_RED_PACKET_CONFIG.THRESHOLD;
  } catch (error) {
    console.error('检查红包资格失败', error);
    return false;
  }
}

/**
 * 获取用户在商家的消费金额
 * @param {string} userId 用户ID
 * @param {string} merchantId 商家ID
 * @returns {Promise<number>} 消费金额
 */
async function getConsumeAmount(userId, merchantId) {
  try {
    // 调用API获取用户消费记录
    const res = await uni.request({
      url: '/api/merchant/consume/amount',
      method: 'GET',
      data: {
        userId,
        merchantId,
        timeRange: 30 // 查询最近30天的消费
      }
    });
    
    if (res.data.code === 0) {
      return res.data.data.amount || 0;
    }
    return 0;
  } catch (error) {
    console.error('获取消费金额失败', error);
    return 0;
  }
}

/**
 * 抽取红包金额
 * @param {string} redPacketId 红包ID
 * @param {string} type 红包类型
 * @returns {Promise<number>} 红包金额
 */
export async function getRandomRedPacketAmount(redPacketId, type) {
  try {
    if (type === RED_PACKET_TYPE.CONSUME) {
      // 消费红包随机金额
      const min = CONSUME_RED_PACKET_CONFIG.MIN_AMOUNT * 100; // 转为分
      const max = CONSUME_RED_PACKET_CONFIG.MAX_AMOUNT * 100; // 转为分
      const amount = Math.floor(Math.random() * (max - min + 1)) + min;
      return amount / 100; // 转回元
    }
    
    // 其他类型红包需调用API
    const res = await uni.request({
      url: `/api/red-packets/${redPacketId}/draw`,
      method: 'POST'
    });
    
    if (res.data.code === 0) {
      return res.data.data.amount || 0;
    }
    return 0;
  } catch (error) {
    console.error('抽取红包金额失败', error);
    return 0;
  }
}

/**
 * 保存红包领取记录
 * @param {Object} record 红包记录
 * @returns {Promise<boolean>} 是否保存成功
 */
export async function saveRedPacketRecord(record) {
  try {
    const res = await uni.request({
      url: '/api/red-packets/records',
      method: 'POST',
      data: record
    });
    
    return res.data.code === 0;
  } catch (error) {
    console.error('保存红包记录失败', error);
    return false;
  }
}

/**
 * 检查用户是否已领取过红包
 * @param {string} userId 用户ID
 * @param {string} redPacketId 红包ID
 * @returns {Promise<boolean>} 是否已领取
 */
export async function hasGrabbedRedPacket(userId, redPacketId) {
  try {
    const res = await uni.request({
      url: '/api/red-packets/check',
      method: 'GET',
      data: {
        userId,
        redPacketId
      }
    });
    
    return res.data.code === 0 && res.data.data.hasGrabbed;
  } catch (error) {
    console.error('检查红包领取状态失败', error);
    return false;
  }
} 