<view class="product-cards-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">商品推广卡片</text><view class="navbar-right"><view class="history-icon" bindtap="{{e}}"><svg wx:if="{{d}}" u-s="{{['d']}}" u-i="c9d53024-0" bind:__l="__l" u-p="{{d}}"><circle wx:if="{{b}}" u-i="c9d53024-1,c9d53024-0" bind:__l="__l" u-p="{{b}}"/><polyline wx:if="{{c}}" u-i="c9d53024-2,c9d53024-0" bind:__l="__l" u-p="{{c}}"/></svg></view></view></view><view class="product-selection-section"><view class="section-header"><text class="section-title">选择推广商品</text><view class="search-btn" bindtap="{{i}}"><svg wx:if="{{h}}" u-s="{{['d']}}" u-i="c9d53024-3" bind:__l="__l" u-p="{{h}}"><path wx:if="{{f}}" u-i="c9d53024-4,c9d53024-3" bind:__l="__l" u-p="{{f}}"/><path wx:if="{{g}}" u-i="c9d53024-5,c9d53024-3" bind:__l="__l" u-p="{{g}}"/></svg><text>搜索商品</text></view></view><scroll-view class="product-scroll" scroll-y><view class="product-list"><view wx:for="{{j}}" wx:for-item="product" wx:key="l" class="{{['product-item', product.m && 'selected']}}" bindtap="{{product.n}}"><view wx:if="{{product.a}}" class="select-indicator"><svg wx:if="{{product.e}}" u-s="{{['d']}}" u-i="{{product.d}}" bind:__l="__l" u-p="{{product.e}}"><path wx:if="{{product.c}}" u-i="{{product.b}}" bind:__l="__l" u-p="{{product.c}}"/></svg></view><image class="product-image" src="{{product.f}}" mode="aspectFill"></image><view class="product-info"><text class="product-name">{{product.g}}</text><view class="product-price-row"><text class="product-price">¥{{product.h}}</text><text class="product-commission">佣金: ¥{{product.i}}</text></view><view class="product-sales">已售 {{product.j}} | 库存 {{product.k}}</view></view></view></view></scroll-view></view><view wx:if="{{k}}" class="card-preview-section"><view class="section-header"><text class="section-title">卡片预览</text><view class="template-selector" bindtap="{{m}}"><text>{{l}}</text><view class="selector-arrow"></view></view></view><view class="{{['card-preview', v]}}"><view class="preview-card"><image class="card-product-image" src="{{n}}" mode="aspectFill"></image><view class="card-content"><text class="card-product-name">{{o}}</text><view class="card-price-row"><text class="card-price">¥{{p}}</text><text wx:if="{{q}}" class="card-original-price">¥{{r}}</text></view><view class="card-footer"><view class="card-shop-info"><image class="card-shop-logo" src="{{s}}" mode="aspectFill"></image><text class="card-shop-name">{{t}}</text></view><view class="card-qrcode-hint"><view class="card-qrcode-placeholder"></view><text class="card-scan-text">扫码购买</text></view></view></view></view></view><view class="customization-options"><view class="option-group"><text class="option-title">卡片样式</text><view class="style-options"><view wx:for="{{w}}" wx:for-item="style" wx:key="c" class="{{['style-option', style.d && 'active']}}" bindtap="{{style.e}}"><view class="style-color" style="{{'background-color:' + style.a}}"></view><text class="style-name">{{style.b}}</text></view></view></view><view class="option-group"><view class="toggle-option"><text class="toggle-label">显示原价</text><switch checked="{{x}}" bindchange="{{y}}" color="#6B0FBE"/></view><view class="toggle-option"><text class="toggle-label">添加促销标签</text><switch checked="{{z}}" bindchange="{{A}}" color="#6B0FBE"/></view><view wx:if="{{B}}" class="toggle-option"><text class="toggle-label">促销标签文字</text><input class="tag-input" type="text" placeholder="限时优惠" maxlength="6" value="{{C}}" bindinput="{{D}}"/></view></view></view><view class="generate-button-container"><button class="generate-button" bindtap="{{E}}"><text>生成推广卡片</text></button></view></view><view wx:if="{{F}}" class="empty-state"><image class="empty-image" src="{{G}}" mode="aspectFit"></image><text class="empty-text">请选择一个商品来创建推广卡片</text></view></view>