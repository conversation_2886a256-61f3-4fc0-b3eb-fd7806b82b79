/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 页面容器 */
.points-management-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}
.navbar-left {
  width: 40px;
}
.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
}
.title-text {
  font-size: 18px;
  font-weight: 600;
}
.navbar-right {
  width: 40px;
}

/* 顶部操作区 */
.top-actions {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: calc(100vh - 80px);
}

/* 概览区块 */
.overview-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 16px;
}
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}
.date-picker {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  border-radius: 16px;
  padding: 6px 12px;
}
.date-text {
  font-size: 12px;
  color: #666666;
}
.arrow-icon {
  width: 12px;
  height: 12px;
  border-right: 2px solid #999;
  border-bottom: 2px solid #999;
  margin-left: 4px;
  transform: rotate(45deg);
}
.overview-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}
.overview-card {
  width: 50%;
  padding: 10px;
  box-sizing: border-box;
}
.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
}
.card-label {
  font-size: 14px;
  color: #999999;
}

/* 积分商品列表 */
.points-items-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.filter-buttons {
  display: flex;
  align-items: center;
}
.filter-button {
  padding: 8px 16px;
  border-radius: 16px;
  background-color: #F5F7FA;
  margin-left: 8px;
  font-size: 14px;
}
.filter-button.active {
  background-color: #FF7600;
  color: white;
}
.points-items-list {
  margin-bottom: 16px;
}
.points-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #F0F0F0;
  position: relative;
}
.item-image-container {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 16px;
}
.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.item-status {
  position: absolute;
  top: 0;
  left: 0;
  padding: 4px 8px;
  font-size: 12px;
  color: white;
  z-index: 2;
}
.status-active {
  background-color: #34C759;
}
.status-draft {
  background-color: #999999;
}
.status-ended {
  background-color: #FF3B30;
}
.item-content {
  flex: 1;
}
.item-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
}
.item-points {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.points-value {
  font-size: 16px;
  font-weight: 600;
  color: #FF7600;
  margin-right: 4px;
}
.points-label {
  font-size: 14px;
  color: #999999;
}
.item-stats {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.stat-item {
  margin-right: 16px;
  display: flex;
  align-items: center;
}
.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  margin-right: 4px;
}
.stat-label {
  font-size: 12px;
  color: #999999;
}
.item-time {
  font-size: 12px;
  color: #999999;
}
.item-actions {
  display: flex;
  align-items: center;
}
.action-button {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}
.action-button.edit {
  background-color: rgba(94, 92, 230, 0.1);
}
.action-button.delete {
  background-color: rgba(255, 59, 48, 0.1);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  text-align: center;
}
.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}
.empty-text {
  font-size: 14px;
  color: #999999;
  margin-bottom: 16px;
}

/* 积分规则 */
.points-rules-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.edit-button {
  display: flex;
  align-items: center;
  color: #5E5CE6;
  font-size: 14px;
}
.edit-button text {
  margin-left: 4px;
}
.rules-list {
  margin-top: 16px;
}
.rule-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}
.rule-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}
.rule-icon.purchase {
  background-color: rgba(25, 137, 250, 0.1);
  color: #1989FA;
}
.rule-icon.checkin {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}
.rule-icon.share {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
.rule-content {
  flex: 1;
}
.rule-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}
.rule-desc {
  font-size: 12px;
  color: #999999;
}
.rule-points {
  font-size: 16px;
  font-weight: 600;
  color: #FF7600;
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}