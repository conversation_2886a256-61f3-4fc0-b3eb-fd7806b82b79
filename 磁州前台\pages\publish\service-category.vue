<template>
  <view class="service-category-container">
    <!-- 顶部背景色覆盖层 -->
    <view class="nav-background"></view>
    
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="back-btn" @click="navigateBack">
        <view class="back-icon"></view>
      </view>
      <view class="navbar-title">选择服务类型</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 提示文本 -->
    <view class="service-tip">
      <text class="tip-text">请选择服务类型</text>
      <text class="tip-desc">选择您要提供的服务类型，让更多人找到您</text>
    </view>
    
    <!-- 服务类型列表 -->
    <view class="service-grid">
      <!-- 家政服务 -->
      <view class="service-item" @click="selectServiceType('家政服务', 'home_cleaning')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/家政服务.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">家政服务</text>
      </view>
      
      <!-- 维修改造 -->
      <view class="service-item" @click="selectServiceType('维修改造', 'repair')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/维修改造.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">维修改造</text>
      </view>
      
      <!-- 上门安装 -->
      <view class="service-item" @click="selectServiceType('上门安装', 'installation')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/上门安装.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">上门安装</text>
      </view>
      
      <!-- 开锁换锁 -->
      <view class="service-item" @click="selectServiceType('开锁换锁', 'locksmith')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/开锁换锁.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">开锁换锁</text>
      </view>
      
      <!-- 搬家拉货 -->
      <view class="service-item" @click="selectServiceType('搬家拉货', 'moving')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/搬家拉货.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">搬家拉货</text>
      </view>
      
      <!-- 上门美容 -->
      <view class="service-item" @click="selectServiceType('上门美容', 'beauty')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/上门美容.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">上门美容</text>
      </view>
      
      <!-- 上门家教 -->
      <view class="service-item" @click="selectServiceType('上门家教', 'tutor')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/上门家教.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">上门家教</text>
      </view>
      
      <!-- 宠物服务 -->
      <view class="service-item" @click="selectServiceType('宠物服务', 'pet_service')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/宠物服务.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">宠物服务</text>
      </view>
      
      <!-- 上门疏通 -->
      <view class="service-item" @click="selectServiceType('上门疏通', 'plumbing')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/上门疏通.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">上门疏通</text>
      </view>
      
      <!-- 其他类型 -->
      <view class="service-item" @click="selectServiceType('其他类型', 'other')">
        <view class="service-icon-wrapper">
          <image class="service-icon" src="/static/images/tabbar/123/其他类型.png" mode="aspectFit"></image>
        </view>
        <text class="service-name">其他类型</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// --- 数据迁移开始 ---
const statusBarHeight = ref(20);
// --- 数据迁移结束 ---

// --- 方法迁移开始 ---
    // 返回上一页
const navigateBack = () => {
      uni.navigateBack();
};
    
    // 选择服务类型
const selectServiceType = (name, type) => {
      // Navigate to the detail page with the correct service type parameters
      uni.navigateTo({
        url: `/pages/publish/detail?type=home_service&serviceType=${type}&serviceTypeName=${encodeURIComponent(name)}&name=${encodeURIComponent('到家服务')}&categoryType=home_service&categoryName=${encodeURIComponent('到家服务')}`
      });
};
// --- 方法迁移结束 ---

// --- 生命周期钩子迁移开始 ---
onLoad(() => {
	const sysInfo = uni.getSystemInfoSync();
	statusBarHeight.value = sysInfo.statusBarHeight;
});
// --- 生命周期钩子迁移结束 ---
</script>

<style lang="scss" scoped>
.service-category-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}

/* 顶部背景覆盖层 */
.nav-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(100rpx + var(--status-bar-height, 20px)); /* 增加高度，使背景往下延伸 */
  background-color: #1677FF;
  z-index: 990;
}

.custom-navbar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  background-color: transparent;
  position: fixed;
  top: var(--status-bar-height, 20px);
  left: 0;
  right: 0;
  z-index: 1000;
  width: 100%;
  box-sizing: border-box;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10rpx; /* 微调关闭按钮位置 */
}

.back-icon {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #fff;
  border-left: 4rpx solid #fff;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  color: #ffffff;
  font-size: 34rpx;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  line-height: 88rpx; /* 使用line-height替代padding让标题垂直居中 */
}

.navbar-right {
  width: 60rpx;
}

.service-tip {
  padding: 30rpx 30rpx 10rpx;
  background-color: transparent;
  margin-top: calc(100rpx + var(--status-bar-height, 20px) + 15rpx); /* 调整顶部边距，适应新的背景高度 */
  margin-left: 20rpx;
  margin-right: 20rpx;
  position: relative;
  z-index: 995;
}

.tip-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.service-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 15rpx 30rpx;
  background-color: #fff;
  margin-top: 10rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
}

.service-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  transition: transform 0.2s;
}

.service-item:active {
  transform: scale(0.92);
}

.service-icon-wrapper {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-icon {
  width: 90rpx;
  height: 90rpx;
  object-fit: contain;
}

.service-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 10rpx;
  box-sizing: border-box;
}
</style> 