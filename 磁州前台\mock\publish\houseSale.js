// 房屋出售详情模拟数据
export const houseSaleDetail = {
  id: 'house-sale-001',
  title: '磁县城区精装修三室两厅出售',
  price: 580000,
  unitPrice: 5800, // 单价，元/平方米
  area: 100, // 面积，平方米
  roomType: '3室2厅1卫',
  floor: '6/18', // 楼层/总楼层
  orientation: '南北通透', // 朝向
  decoration: '精装修', // 装修情况
  age: 5, // 房龄
  community: '阳光花园',
  address: '磁县城区阳光路123号阳光花园',
  description: '房屋位于磁县城区核心位置，南北通透，采光良好，精装修，拎包入住。周边配套设施齐全，交通便利，距离商业中心、学校、医院均在1公里范围内。',
  contact: {
    name: '张先生',
    phone: '138****1234',
    wechat: 'zhangxiansheng123'
  },
  features: ['南北通透', '精装修', '电梯房', '拎包入住', '学区房', '地铁房'],
  facilities: ['水', '电', '气', '暖', '宽带', '电视', '冰箱', '洗衣机', '空调', '热水器', '衣柜', '沙发', '床'],
  images: [
    '/static/images/house/house-sale-1.jpg',
    '/static/images/house/house-sale-2.jpg',
    '/static/images/house/house-sale-3.jpg',
    '/static/images/house/house-sale-4.jpg'
  ],
  location: {
    latitude: 36.314736,
    longitude: 114.711234
  },
  surroundings: {
    school: ['磁县第一小学(500m)', '磁县第一中学(800m)'],
    hospital: ['磁县人民医院(1.2km)'],
    shopping: ['万达广场(1km)', '永辉超市(600m)'],
    transportation: ['公交站(200m)', '地铁站(1.5km)']
  },
  publishTime: '2024-03-10 14:30:00',
  views: 356,
  likes: 28,
  collections: 15,
  status: 'active' // active, sold, expired
};

// 相关房源模拟数据
export const relatedHouseSales = [
  {
    id: 'house-sale-002',
    title: '磁县城区两室一厅出售',
    price: 420000,
    area: 80,
    roomType: '2室1厅1卫',
    community: '幸福家园',
    decoration: '简装',
    image: '/static/images/house/house-sale-5.jpg'
  },
  {
    id: 'house-sale-003',
    title: '磁县城区四室两厅出售',
    price: 680000,
    area: 130,
    roomType: '4室2厅2卫',
    community: '和平小区',
    decoration: '精装修',
    image: '/static/images/house/house-sale-6.jpg'
  },
  {
    id: 'house-sale-004',
    title: '磁县城区三室一厅出售',
    price: 520000,
    area: 95,
    roomType: '3室1厅1卫',
    community: '阳光花园',
    decoration: '中等装修',
    image: '/static/images/house/house-sale-7.jpg'
  }
];

// 获取房屋出售详情的API函数
export const fetchHouseSaleDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(houseSaleDetail);
    }, 500);
  });
};

// 获取相关房源的API函数
export const fetchRelatedHouseSales = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(relatedHouseSales);
    }, 500);
  });
}; 