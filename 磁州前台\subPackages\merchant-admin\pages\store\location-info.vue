<template>
  <view class="location-info-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">位置管理</view>
        <view class="navbar-right">
          <!-- 占位元素保持导航栏平衡 -->
        </view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <form @submit="saveLocationInfo">
        <!-- 位置选择 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">店铺位置</text>
          </view>
          
          <view class="location-map">
            <map 
              id="map" 
              class="map" 
              :latitude="formData.latitude" 
              :longitude="formData.longitude" 
              scale="16" 
              show-location
              :markers="markers"
            ></map>
            <view class="map-tools">
              <view class="tool-btn" @click="selectLocation">
                <image src="/static/images/tabbar/定位.png" class="tool-icon"></image>
              </view>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label required">详细地址</text>
            <view class="search-location-wrapper">
              <input type="text" class="form-input" v-model="formData.address" placeholder="点击选择位置" disabled />
              <view class="search-location-btn" @click="selectLocation">
                <text class="search-icon">🔍</text>
              </view>
            </view>
          </view>
          
          <view class="form-item" v-if="formData.address">
            <text class="form-label required">门牌号/楼层</text>
            <input type="text" class="form-input" v-model="formData.addressDetail" placeholder="例：8栋3单元201室" />
          </view>
        </view>
        
        <!-- 导航设置 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">导航设置</text>
          </view>
          
          <view class="switch-item">
            <text class="switch-label">在线导航</text>
            <switch :checked="formData.navigation.enabled" @change="onNavigationChange" color="#0A84FF" />
          </view>
          
          <view class="navigation-tips" v-if="formData.navigation.enabled">
            <text class="tips-text">开启后，顾客可在详情页使用导航功能直接前往您的店铺</text>
          </view>
          
          <view class="form-item" v-if="formData.navigation.enabled">
            <text class="form-label">导航提示文本</text>
            <input type="text" class="form-input" v-model="formData.navigation.navigationText" placeholder="例：店铺位于大厅A区12号商铺" />
          </view>
        </view>
        
        <!-- 位置标签 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">位置标签</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">周边环境</text>
            <view class="tags-container">
              <view class="tag-item" v-for="(tag, index) in formData.locationTags" :key="index">
                <text class="tag-text">{{tag}}</text>
                <text class="tag-delete" @click="removeTag(index)">×</text>
              </view>
              <view class="tag-add" @click="showAddTagModal" v-if="formData.locationTags.length < 5">
                <text class="tag-add-icon">+</text>
                <text class="tag-add-text">添加标签</text>
              </view>
            </view>
            <text class="tags-tips">添加周边环境标签，帮助顾客更快找到店铺，最多5个</text>
          </view>
        </view>
        
        <!-- 交通信息 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">交通信息</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">公交信息</text>
            <input type="text" class="form-input" v-model="formData.transportation.bus" placeholder="例：乘坐10路、15路公交车至人民医院站下车" />
          </view>
          
          <view class="form-item">
            <text class="form-label">停车信息</text>
            <input type="text" class="form-input" v-model="formData.transportation.parking" placeholder="例：商场地下停车场，首小时免费" />
          </view>
        </view>
        
        <!-- 底部安全区域 -->
        <view class="safe-area-bottom"></view>
      </form>
    </scroll-view>
    
    <!-- 底部保存按钮 -->
    <view class="footer">
      <view class="safe-area-bottom-footer"></view>
      <view class="footer-content">
        <button class="save-btn" @click="saveLocationInfo">保存修改</button>
      </view>
    </view>
    
    <!-- 添加标签弹窗 -->
    <view class="modal-overlay" v-if="showTagModal" @click="hideAddTagModal"></view>
    <view class="tag-modal" v-if="showTagModal">
      <view class="modal-header">
        <text class="modal-title">添加位置标签</text>
      </view>
      <view class="modal-content">
        <input type="text" class="tag-input" v-model="newTag" placeholder="请输入标签内容" maxlength="8" />
        <text class="tag-input-counter">{{newTag.length}}/8</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="hideAddTagModal">取消</button>
        <button class="modal-btn confirm" @click="addTag" :disabled="!newTag.trim()">确认</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        latitude: 36.2325,
        longitude: 114.5325,
        address: '河北省磁县东路15号鑫鑫商场',
        addressDetail: 'A区213号商铺',
        navigation: {
          enabled: true,
          navigationText: '位于商场一楼食品区，店门口有品牌招牌'
        },
        locationTags: ['商场内', '电梯旁', '一楼大厅'],
        transportation: {
          bus: '101路、205路公交车至磁县商场站下车',
          parking: '商场地下停车场，凭消费小票2小时内免费'
        }
      },
      markers: [
        {
          id: 1,
          latitude: 36.2325,
          longitude: 114.5325,
          iconPath: '/static/images/tabbar/店铺标记.png',
          width: 30,
          height: 30
        }
      ],
      showTagModal: false,
      newTag: '',
      statusBarHeight: 20
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 选择位置
    selectLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.formData.latitude = res.latitude;
          this.formData.longitude = res.longitude;
          this.formData.address = res.address + res.name;
          
          // 更新地图标记
          this.markers = [{
            id: 1,
            latitude: res.latitude,
            longitude: res.longitude,
            iconPath: '/static/images/tabbar/店铺标记.png',
            width: 30,
            height: 30
          }];
        },
        fail: (err) => {
          console.log('选择位置失败', err);
          uni.showToast({
            title: '选择位置失败，请重试',
            icon: 'none'
          });
        }
      });
    },
    
    // 导航设置变化
    onNavigationChange(e) {
      this.formData.navigation.enabled = e.detail.value;
    },
    
    // 显示添加标签弹窗
    showAddTagModal() {
      this.showTagModal = true;
      this.newTag = '';
    },
    
    // 隐藏添加标签弹窗
    hideAddTagModal() {
      this.showTagModal = false;
    },
    
    // 添加标签
    addTag() {
      if (this.newTag.trim() && this.formData.locationTags.length < 5) {
        this.formData.locationTags.push(this.newTag.trim());
        this.hideAddTagModal();
      }
    },
    
    // 删除标签
    removeTag(index) {
      this.formData.locationTags.splice(index, 1);
    },
    
    // 保存位置信息
    saveLocationInfo() {
      // 表单验证
      if (!this.formData.address) {
        uni.showToast({
          title: '请选择店铺位置',
          icon: 'none'
        });
        return;
      }
      
      if (!this.formData.addressDetail) {
        uni.showToast({
          title: '请填写详细门牌号',
          icon: 'none'
        });
        return;
      }
      
      // 显示加载中
      uni.showLoading({
        title: '保存中...',
        mask: true
      });
      
      // 模拟保存过程
      setTimeout(() => {
        // 这里应该是实际的保存逻辑
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 延迟返回上一页
            setTimeout(() => {
              this.goBack();
            }, 2000);
          }
        });
      }, 1500);
    }
  }
}
</script>

<style lang="scss">
.location-info-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
  padding-bottom: 180rpx;
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 内容区域 */
.content-scroll {
  padding: 30rpx;
}

/* 表单区块样式 */
.form-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
  display: inline-block;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

/* 地图区域 */
.location-map {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
  margin-bottom: 30rpx;
}

.map {
  width: 100%;
  height: 100%;
}

.map-tools {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  z-index: 10;
}

.tool-btn {
  width: 80rpx;
  height: 80rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.tool-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required::after {
  content: '*';
  color: #FF3B30;
  margin-left: 6rpx;
}

.form-input {
  width: 100%;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.search-location-wrapper {
  position: relative;
}

.search-location-btn {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  font-size: 32rpx;
}

/* 开关项样式 */
.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.navigation-tips {
  padding: 20rpx 10rpx;
  margin-bottom: 20rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.tag-item, .tag-add {
  display: flex;
  align-items: center;
  background-color: #F5F8FC;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.tag-item {
  background-color: rgba(10, 132, 255, 0.1);
}

.tag-text {
  font-size: 26rpx;
  color: #0A84FF;
}

.tag-delete {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #0A84FF;
}

.tag-add {
  background-color: #FFFFFF;
  border: 2rpx dashed #CCCCCC;
  padding: 10rpx 20rpx;
}

.tag-add-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 8rpx;
}

.tag-add-text {
  font-size: 26rpx;
  color: #999;
}

.tags-tips {
  font-size: 24rpx;
  color: #999;
}

/* 底部按钮区域 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.safe-area-bottom-footer {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}

.footer-content {
  display: flex;
  justify-content: center;
}

.save-btn {
  width: 90%;
  height: 88rpx;
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.3);
}

.save-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(10, 132, 255, 0.3);
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx;
}

/* 添加标签弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.tag-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 30rpx;
  position: relative;
}

.tag-input {
  width: 100%;
  height: 88rpx;
  background-color: #F5F8FC;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
}

.tag-input-counter {
  position: absolute;
  right: 54rpx;
  bottom: 42rpx;
  font-size: 24rpx;
  color: #999;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background-color: transparent;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .location-info-container {
    background-color: #1C1C1E;
  }
  
  .form-section {
    background-color: #2C2C2E;
  }
  
  .section-title {
    color: #FFFFFF;
  }
  
  .form-label,
  .form-input,
  .switch-label {
    color: #FFFFFF;
  }
  
  .form-input {
    background-color: #3A3A3C;
  }
  
  .tool-btn {
    background-color: #2C2C2E;
  }
  
  .tag-modal {
    background-color: #2C2C2E;
  }
  
  .modal-title {
    color: #FFFFFF;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #3A3A3C;
  }
  
  .tag-input {
    background-color: #3A3A3C;
    color: #FFFFFF;
  }
  
  .footer {
    background-color: #2C2C2E;
  }
}
</style> 