<template>
  <view class="local-mall-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="navbar-title">磁州商城</view>
        <view class="navbar-right">
          <view class="search-btn" @click="navigateTo('/subPackages/activity-showcase/pages/search/index')">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M11 17.25a6.25 6.25 0 110-12.5 6.25 6.25 0 010 12.5zm0 0L21 21" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
          <view class="notification-btn" @click="showNotifications">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9M13.73 21a2 2 0 01-3.46 0" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            <view class="badge" v-if="unreadNotifications > 0">{{ unreadNotifications > 99 ? '99+' : unreadNotifications }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
    >
      <!-- 搜索框和分类导航 -->
      <SearchAndCategory 
        :categories="categories" 
        @search="navigateTo('/subPackages/activity-showcase/pages/search/index')"
        @scan="showScanCode"
        @categoryChange="onCategoryChange"
      />
      
      <!-- 限时特惠 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">今日特惠</text>
          <view class="section-more" @click="navigateTo('/subPackages/activity-showcase/pages/flash-sale/index')">
            <text>查看更多</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <scroll-view class="product-scroll" scroll-x show-scrollbar="false">
          <view class="product-scroll-list">
            <view 
              class="product-scroll-item" 
              v-for="(product, index) in flashSaleProducts" 
              :key="index"
            >
              <ProductCard 
                :product="product"
                :showShop="false"
                :cardStyle="{
                  width: '280rpx',
                  marginRight: index === flashSaleProducts.length - 1 ? '0' : '20rpx'
                }"
                @click="viewProductDetail(product)"
                @addToCart="addToCart(product)"
              />
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 附近好店 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">磁州好店</text>
          <view class="section-more" @click="navigateTo('/subPackages/activity-showcase/pages/shops/index')">
            <text>查看更多</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="shops-list">
          <ShopCard 
            v-for="(shop, index) in nearbyShops" 
            :key="index"
            :shop="shop"
            @click="viewShopDetail(shop)"
            @enter="viewShopDetail(shop)"
          />
        </view>
      </view>
      
      <!-- 热门商品 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">精选商品</text>
          <view class="section-more" @click="navigateTo('/subPackages/activity-showcase/pages/products/hot')">
            <text>查看更多</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="products-grid">
          <view 
            class="product-grid-item" 
            v-for="(product, index) in hotProducts" 
            :key="index"
          >
            <ProductCard 
              :product="product"
              @click="viewProductDetail(product)"
              @addToCart="addToCart(product)"
            />
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <view class="tabbar">
      <view 
        class="tabbar-item" 
        @click="switchTab('home')"
        data-tab="home"
      >
        <view class="tab-icon home"></view>
        <text class="tabbar-text">首页</text>
      </view>
      <view 
        class="tabbar-item active" 
        data-tab="discover"
      >
        <view class="tab-icon discover"></view>
        <text class="tabbar-text">商城</text>
      </view>
      <view 
        class="tabbar-item" 
        @click="switchTab('distribution')"
        data-tab="distribution"
      >
        <view class="tab-icon distribution"></view>
        <text class="tabbar-text">分销</text>
      </view>
      <view 
        class="tabbar-item" 
        @click="switchTab('message')"
        data-tab="message"
      >
        <view class="tab-icon message">
          <view class="badge" v-if="unreadMessageCount > 0">{{ unreadMessageCount > 99 ? '99+' : unreadMessageCount }}</view>
        </view>
        <text class="tabbar-text">消息</text>
      </view>
      <view 
        class="tabbar-item" 
        @click="switchTab('my')"
        data-tab="my"
      >
        <view class="tab-icon user"></view>
        <text class="tabbar-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import SearchAndCategory from '../../components/mall/SearchAndCategory.vue';
import ProductCard from '../../components/mall/ProductCard.vue';
import ShopCard from '../../components/mall/ShopCard.vue';

// 页面状态
const unreadNotifications = ref(5);
const unreadMessageCount = ref(3);

// 分类数据
const categories = ref([
  { name: '全部', icon: '/static/images/category/all.png', bgColor: '#FFF0F5' },
  { name: '美食', icon: '/static/images/category/food.png', bgColor: '#FFF4E8' },
  { name: '服饰', icon: '/static/images/category/clothing.png', bgColor: '#E8F5FF' },
  { name: '美妆', icon: '/static/images/category/beauty.png', bgColor: '#F0FFF0' },
  { name: '电子', icon: '/static/images/category/electronics.png', bgColor: '#F5E8FF' },
  { name: '家居', icon: '/static/images/category/home.png', bgColor: '#E8FFFF' },
  { name: '母婴', icon: '/static/images/category/baby.png', bgColor: '#FFF0E8' },
  { name: '更多', icon: '/static/images/category/more.png', bgColor: '#F2F2F7' },
  { name: '生鲜', icon: '/static/images/category/fresh.png', bgColor: '#E8FFF0' },
  { name: '书籍', icon: '/static/images/category/books.png', bgColor: '#FFE8F5' }
]);

// 限时特惠商品
const flashSaleProducts = ref([
  {
    id: 1,
    title: 'iPhone 14 Pro 深空黑 256G',
    coverImage: 'https://via.placeholder.com/300x300',
    shopName: 'Apple授权专卖店',
    shopLogo: 'https://via.placeholder.com/100',
    price: 7999,
    originalPrice: 8999,
    soldCount: 235,
    tag: '限时秒杀',
    labels: [
      { type: 'discount', text: '满3000减300' },
      { type: 'new', text: '新品' }
    ]
  },
  {
    id: 2,
    title: '华为Mate 50 Pro 曜金黑 512G',
    coverImage: 'https://via.placeholder.com/300x300',
    shopName: '华为官方旗舰店',
    shopLogo: 'https://via.placeholder.com/100',
    price: 6999,
    originalPrice: 7999,
    soldCount: 189,
    tag: '限时特惠',
    labels: [
      { type: 'discount', text: '满5000减500' },
      { type: 'hot', text: '热卖' }
    ]
  },
  {
    id: 3,
    title: '小米12S Ultra 陶瓷白 256G',
    coverImage: 'https://via.placeholder.com/300x300',
    shopName: '小米官方旗舰店',
    shopLogo: 'https://via.placeholder.com/100',
    price: 5999,
    originalPrice: 6999,
    soldCount: 156,
    tag: '限时秒杀',
    labels: [
      { type: 'coupon', text: '满3000减400' }
    ]
  },
  {
    id: 4,
    title: 'OPPO Find X5 Pro 陶瓷白 256G',
    coverImage: 'https://via.placeholder.com/300x300',
    shopName: 'OPPO官方旗舰店',
    shopLogo: 'https://via.placeholder.com/100',
    price: 4999,
    originalPrice: 5999,
    soldCount: 132,
    tag: '限时特惠',
    labels: [
      { type: 'new', text: '新品' }
    ]
  }
]);

// 附近好店
const nearbyShops = ref([
  {
    id: 1,
    name: '星巴克咖啡(万达广场店)',
    logo: 'https://via.placeholder.com/100',
    coverImage: 'https://via.placeholder.com/750x300',
    rating: 4.8,
    distance: '500m',
    orderCount: 5689,
    tags: ['咖啡', '甜点', '下午茶'],
    description: '提供优质咖啡和舒适环境的星巴克门店，欢迎品尝我们的季节限定饮品。'
  },
  {
    id: 2,
    name: '海底捞火锅(环球中心店)',
    logo: 'https://via.placeholder.com/100',
    coverImage: 'https://via.placeholder.com/750x300',
    rating: 4.9,
    distance: '1.2km',
    orderCount: 8976,
    tags: ['火锅', '川菜', '聚餐'],
    description: '提供优质服务和美味火锅的海底捞门店，欢迎您的光临。'
  }
]);

// 热门商品
const hotProducts = ref([
  {
    id: 5,
    title: 'Apple AirPods Pro 2代',
    coverImage: 'https://via.placeholder.com/300x300',
    shopName: 'Apple授权专卖店',
    shopLogo: 'https://via.placeholder.com/100',
    price: 1999,
    originalPrice: 2299,
    soldCount: 456,
    distance: '2.5km',
    labels: [
      { type: 'hot', text: '热卖' }
    ]
  },
  {
    id: 6,
    title: '戴森吹风机 Supersonic HD08',
    coverImage: 'https://via.placeholder.com/300x300',
    shopName: '戴森官方旗舰店',
    shopLogo: 'https://via.placeholder.com/100',
    price: 3190,
    originalPrice: 3690,
    soldCount: 325,
    distance: '3.1km',
    labels: [
      { type: 'discount', text: '满3000减300' }
    ]
  },
  {
    id: 7,
    title: 'NIKE Air Jordan 1 高帮篮球鞋',
    coverImage: 'https://via.placeholder.com/300x300',
    shopName: 'NIKE官方旗舰店',
    shopLogo: 'https://via.placeholder.com/100',
    price: 1299,
    originalPrice: 1499,
    soldCount: 567,
    distance: '4.2km',
    labels: [
      { type: 'new', text: '新品' }
    ]
  },
  {
    id: 8,
    title: '三星Galaxy Watch 5 Pro',
    coverImage: 'https://via.placeholder.com/300x300',
    shopName: '三星电子旗舰店',
    shopLogo: 'https://via.placeholder.com/100',
    price: 2999,
    originalPrice: 3299,
    soldCount: 234,
    distance: '1.8km',
    labels: [
      { type: 'coupon', text: '满2000减200' }
    ]
  }
]);

// 显示通知
const showNotifications = () => {
  uni.showToast({
    title: '通知功能开发中',
    icon: 'none'
  });
};

// 显示扫码
const showScanCode = () => {
  uni.scanCode({
    success: (res) => {
      uni.showToast({
        title: '扫码成功: ' + res.result,
        icon: 'none'
      });
    },
    fail: () => {
      uni.showToast({
        title: '扫码失败',
        icon: 'none'
      });
    }
  });
};

// 分类切换
const onCategoryChange = (index) => {
  uni.showToast({
    title: `已切换到${categories.value[index].name}分类`,
    icon: 'none'
  });
};

// 查看商品详情
const viewProductDetail = (product) => {
  navigateTo(`/subPackages/activity-showcase/pages/products/detail?id=${product.id}`);
};

// 查看店铺详情
const viewShopDetail = (shop) => {
  navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.id}`);
};

// 添加到购物车
const addToCart = (product) => {
  uni.showToast({
    title: '已添加到购物车',
    icon: 'success'
  });
};

// 切换底部导航标签页
const switchTab = (tab) => {
  switch(tab) {
    case 'home':
      uni.redirectTo({
        url: '/subPackages/activity-showcase/pages/index/index'
      });
      break;
    case 'distribution':
      uni.redirectTo({
        url: '/subPackages/activity-showcase/pages/distribution/index'
      });
      break;
    case 'message':
      uni.redirectTo({
        url: '/subPackages/activity-showcase/pages/message/index'
      });
      break;
    case 'my':
      uni.redirectTo({
        url: '/subPackages/activity-showcase/pages/my/index'
      });
      break;
  }
};

// 页面导航
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 加载更多
const loadMore = () => {
  // 加载更多数据...
  uni.showToast({
    title: '已加载全部数据',
    icon: 'none'
  });
};

// 页面加载
onMounted(() => {
  // 初始化数据
});
</script>

<style lang="scss" scoped>
.local-mall-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(255,59,105,0.15);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  }
  
  .navbar-right {
    display: flex;
    align-items: center;
    position: absolute;
    right: 30rpx;
  }
  
  .search-btn, .notification-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    .icon {
      width: 48rpx;
      height: 48rpx;
    }
    
    .badge {
      position: absolute;
      top: 10rpx;
      right: 10rpx;
      min-width: 32rpx;
      height: 32rpx;
      border-radius: 16rpx;
      background: #FF3B30;
      color: #FFFFFF;
      font-size: 20rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 6rpx;
      box-sizing: border-box;
      font-weight: 600;
      box-shadow: 0 2rpx 6rpx rgba(255,59,48,0.3);
      border: 1rpx solid rgba(255,255,255,0.8);
    }
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 100rpx; /* 底部导航栏高度 */
  box-sizing: border-box;
}

/* 内容区域样式 */
.content-scroll {
  padding: 0;
}

/* 区块卡片 */
.section-card {
  background-color: #FFFFFF;
  margin: 30rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  position: relative;
  z-index: 1;
}

.section-card:first-of-type {
  margin-top: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .section-more {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #FF3B69;
    
    .icon {
      margin-left: 4rpx;
    }
  }
}

/* 商品横向滚动 */
.product-scroll {
  white-space: nowrap;
  
  .product-scroll-list {
    display: inline-flex;
    padding: 10rpx 0;
  }
}

/* 店铺列表 */
.shops-list {
  margin-top: 20rpx;
}

/* 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 34px; /* iOS 安全区域高度 */
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom); /* 适配 iPhone X 以上的底部安全区域 */
  z-index: 99;
  border-top: 1rpx solid #EEEEEE;
}
  
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}

.tabbar-item:active {
  transform: scale(0.9);
}

.tabbar-item.active .tab-icon {
  transform: translateY(-5rpx);
}

.tabbar-item.active .tabbar-text {
  color: #FF3B69;
  font-weight: 600;
  transform: translateY(-2rpx);
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 首页图标 */
.tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="home"] .tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

/* 发现图标（本地商城） */
.tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 3c0 .55.45 1 1 1h1l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h11c.55 0 1-.45 1-1s-.45-1-1-1H7l1.1-2h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.67-1.43c-.16-.35-.52-.57-.9-.57H2c-.55 0-1 .45-1 1zm16 15c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="discover"] .tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 3c0 .55.45 1 1 1h1l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h11c.55 0 1-.45 1-1s-.45-1-1-1H7l1.1-2h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.67-1.43c-.16-.35-.52-.57-.9-.57H2c-.55 0-1 .45-1 1zm16 15c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 分销图标 */
.tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="distribution"] .tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

/* 消息图标 */
.tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="message"] .tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 我的图标 */
.tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="my"] .tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active .tab-icon {
  filter: drop-shadow(0 1px 2px rgba(255,59,105,0.3));
}

.badge {
  position: absolute;
  top: -8rpx;
  right: -12rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #FF453A, #FF2D55);
  color: #FFFFFF;
  font-size: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  font-weight: 600;
  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transform: scale(0.9);
}

.tabbar-text {
  font-size: 22rpx;
  color: #8E8E93;
  margin-top: 2rpx;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tabbar-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 30rpx;
  height: 4rpx;
  background: #FF3B69;
  border-radius: 2rpx;
  transition: transform 0.3s ease;
}

.tabbar-item.active::after {
  transform: translateX(-50%) scaleX(1);
}
</style>
