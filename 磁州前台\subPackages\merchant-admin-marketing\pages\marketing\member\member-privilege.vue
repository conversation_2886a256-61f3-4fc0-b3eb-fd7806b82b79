<!-- 会员特权页面开始 -->
<template>
  <view class="member-privilege-container">
    <!-- 页面标题区域 -->
    <view class="page-header">
      <view class="title-section">
        <text class="page-title">会员特权</text>
        <text class="page-subtitle">管理店铺会员专属特权</text>
      </view>
      
      <!-- 添加特权按钮 -->
      <view class="add-privilege-btn" @click="showAddPrivilegeModal">
        <text class="btn-text">添加特权</text>
        <text class="btn-icon">+</text>
      </view>
    </view>
    
    <!-- 特权类型快捷入口 -->
    <view class="privilege-shortcuts">
      <view class="shortcut-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/member-discount')">
        <view class="shortcut-icon" style="background-color: #FF6B22;">
          <text class="icon-text">折</text>
        </view>
        <text class="shortcut-name">会员折扣</text>
      </view>
      <view class="shortcut-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/points-acceleration')">
        <view class="shortcut-icon" style="background-color: #1E90FF;">
          <text class="icon-text">积</text>
        </view>
        <text class="shortcut-name">积分加速</text>
      </view>
      <view class="shortcut-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/free-shipping')">
        <view class="shortcut-icon" style="background-color: #32CD32;">
          <text class="icon-text">送</text>
        </view>
        <text class="shortcut-name">免费配送</text>
      </view>
      <view class="shortcut-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/birthday-gift')">
        <view class="shortcut-icon" style="background-color: #FF69B4;">
          <text class="icon-text">礼</text>
        </view>
        <text class="shortcut-name">生日礼包</text>
      </view>
      <view class="shortcut-item" @click="navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/vip-service')">
        <view class="shortcut-icon" style="background-color: #8A2BE2;">
          <text class="icon-text">客</text>
        </view>
        <text class="shortcut-name">专属客服</text>
      </view>
    </view>
    
    <!-- 会员特权列表 -->
    <view class="privilege-list">
      <view v-if="privileges.length === 0" class="empty-tip">
        <image class="empty-icon" src="/static/images/empty-data.svg"></image>
        <text class="empty-text">暂无会员特权，点击"添加特权"创建</text>
      </view>
      
      <view v-else class="privilege-cards">
        <view v-for="(privilege, index) in privileges" :key="index" class="privilege-card">
          <view class="privilege-card-header" :style="{ backgroundColor: privilege.color }">
            <view class="privilege-name">{{ privilege.name }}</view>
            <view class="privilege-actions">
              <text class="action-btn edit" @click="editPrivilege(privilege)">编辑</text>
              <text class="action-btn delete" @click="confirmDeletePrivilege(privilege)">删除</text>
            </view>
          </view>
          <view class="privilege-card-body">
            <view class="privilege-info-item">
              <text class="info-label">特权图标：</text>
              <image class="privilege-icon" :src="privilege.icon" mode="aspectFit"></image>
            </view>
            <view class="privilege-info-item">
              <text class="info-label">适用等级：</text>
              <view class="level-tags">
                <text 
                  v-for="(level, idx) in privilege.applicableLevels" 
                  :key="idx" 
                  class="level-tag"
                >{{ level }}</text>
              </view>
            </view>
            <view class="privilege-info-item">
              <text class="info-label">特权类型：</text>
              <text class="info-value">{{ privilege.type }}</text>
            </view>
            <view class="privilege-info-item">
              <text class="info-label">特权价值：</text>
              <text class="info-value">{{ privilege.value }}</text>
            </view>
            <view class="privilege-info-item">
              <text class="info-label">特权说明：</text>
              <text class="info-value">{{ privilege.description || '暂无特权说明' }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 添加/编辑特权弹窗 -->
    <uni-popup ref="privilegeFormPopup" type="center">
      <view class="privilege-form-popup">
        <view class="popup-header">
          <text class="popup-title">{{ isEditing ? '编辑特权' : '添加特权' }}</text>
          <text class="popup-close" @click="closePrivilegeModal">×</text>
        </view>
        <view class="popup-body">
          <view class="form-item">
            <text class="form-label">特权名称</text>
            <input class="form-input" v-model="privilegeForm.name" placeholder="请输入特权名称" />
          </view>
          <view class="form-item">
            <text class="form-label">特权颜色</text>
            <view class="color-picker">
              <view 
                v-for="(color, idx) in colorOptions" 
                :key="idx" 
                class="color-option"
                :class="{ active: privilegeForm.color === color }"
                :style="{ backgroundColor: color }"
                @click="privilegeForm.color = color"
              ></view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">特权图标</text>
            <view class="icon-upload">
              <image v-if="privilegeForm.icon" class="preview-icon" :src="privilegeForm.icon" mode="aspectFit"></image>
              <view v-else class="upload-btn" @click="chooseIcon">
                <text class="upload-icon">+</text>
                <text class="upload-text">上传图标</text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">特权类型</text>
            <picker class="form-picker" :range="privilegeTypes" @change="onTypeChange">
              <view class="picker-value">{{ privilegeForm.type || '请选择特权类型' }}</view>
            </picker>
          </view>
          <view class="form-item">
            <text class="form-label">特权价值</text>
            <input class="form-input" v-model="privilegeForm.value" placeholder="请输入特权价值，如折扣力度、赠送数量等" />
          </view>
          <view class="form-item">
            <text class="form-label">适用等级</text>
            <view class="level-checkboxes">
              <view 
                v-for="(level, idx) in memberLevels" 
                :key="idx" 
                class="level-checkbox"
                :class="{ active: isLevelSelected(level.name) }"
                @click="toggleLevelSelection(level.name)"
              >
                <text class="checkbox-icon">{{ isLevelSelected(level.name) ? '✓' : '' }}</text>
                <text class="checkbox-label">{{ level.name }}</text>
              </view>
            </view>
          </view>
          <view class="form-item">
            <text class="form-label">特权说明</text>
            <textarea class="form-textarea" v-model="privilegeForm.description" placeholder="请输入特权说明"></textarea>
          </view>
        </view>
        <view class="popup-footer">
          <button class="cancel-btn" @click="closePrivilegeModal">取消</button>
          <button class="confirm-btn" @click="savePrivilegeForm">确认</button>
        </view>
      </view>
    </uni-popup>
    
    <!-- 删除确认弹窗 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog
        type="warning"
        title="删除确认"
        content="确定要删除该会员特权吗？删除后将无法恢复。"
        :before-close="true"
        @confirm="deletePrivilege"
        @close="closeDeleteConfirm"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      privileges: [], // 会员特权列表
      privilegeForm: {
        id: '',
        name: '',
        color: '#FF6B22', // 默认橙色
        icon: '',
        type: '',
        value: '',
        applicableLevels: [],
        description: ''
      },
      isEditing: false, // 是否为编辑模式
      currentPrivilegeId: null, // 当前编辑的特权ID
      colorOptions: [
        '#FF6B22', // 橙色
        '#8A2BE2', // 紫色
        '#1E90FF', // 道奇蓝
        '#32CD32', // 酸橙绿
        '#FFD700', // 金色
        '#FF69B4', // 热粉红
        '#20B2AA', // 浅海绿
        '#FF8C00'  // 深橙色
      ],
      privilegeTypes: [
        '折扣优惠',
        '积分加速',
        '专属礼品',
        '免费服务',
        '专属活动',
        '专属客服',
        '其他特权'
      ],
      memberLevels: [] // 会员等级列表
    };
  },
  onLoad() {
    this.fetchPrivileges();
    this.fetchMemberLevels();
  },
  methods: {
    // 页面导航
    navigateTo(url) {
      uni.navigateTo({
        url: url
      });
    },
    
    // 获取会员特权列表
    fetchPrivileges() {
      // 模拟数据，实际项目中应从API获取
      this.privileges = [
        {
          id: '1',
          name: '生日礼包',
          color: '#FF69B4',
          icon: '/static/images/privilege-birthday.svg',
          type: '专属礼品',
          value: '价值100元礼品',
          applicableLevels: ['金卡会员', '钻石会员'],
          description: '会员生日当月可领取专属生日礼包一份'
        },
        {
          id: '2',
          name: '专属折扣',
          color: '#FF6B22',
          icon: '/static/images/privilege-discount.svg',
          type: '折扣优惠',
          value: '9折起',
          applicableLevels: ['银卡会员', '金卡会员', '钻石会员'],
          description: '会员专享商品折扣，最低享受9折优惠'
        },
        {
          id: '3',
          name: '积分翻倍',
          color: '#1E90FF',
          icon: '/static/images/privilege-points.svg',
          type: '积分加速',
          value: '最高2倍积分',
          applicableLevels: ['金卡会员', '钻石会员'],
          description: '消费即可获得最高2倍积分奖励'
        }
      ];
    },
    
    // 获取会员等级列表
    fetchMemberLevels() {
      // 模拟数据，实际项目中应从API获取
      this.memberLevels = [
        {
          id: '1',
          name: '普通会员'
        },
        {
          id: '2',
          name: '银卡会员'
        },
        {
          id: '3',
          name: '金卡会员'
        },
        {
          id: '4',
          name: '钻石会员'
        }
      ];
    },
    
    // 显示添加特权弹窗
    showAddPrivilegeModal() {
      this.isEditing = false;
      this.resetPrivilegeForm();
      this.$refs.privilegeFormPopup.open();
    },
    
    // 编辑特权
    editPrivilege(privilege) {
      this.isEditing = true;
      this.currentPrivilegeId = privilege.id;
      this.privilegeForm = { ...privilege };
      this.$refs.privilegeFormPopup.open();
    },
    
    // 关闭特权表单弹窗
    closePrivilegeModal() {
      this.$refs.privilegeFormPopup.close();
      this.resetPrivilegeForm();
    },
    
    // 重置表单
    resetPrivilegeForm() {
      this.privilegeForm = {
        id: '',
        name: '',
        color: '#FF6B22',
        icon: '',
        type: '',
        value: '',
        applicableLevels: [],
        description: ''
      };
      this.currentPrivilegeId = null;
    },
    
    // 选择图标
    chooseIcon() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.privilegeForm.icon = res.tempFilePaths[0];
          // 实际项目中应上传图片到服务器并获取URL
        }
      });
    },
    
    // 特权类型选择变化
    onTypeChange(e) {
      const index = e.detail.value;
      this.privilegeForm.type = this.privilegeTypes[index];
    },
    
    // 检查等级是否已选中
    isLevelSelected(levelName) {
      return this.privilegeForm.applicableLevels.includes(levelName);
    },
    
    // 切换等级选择状态
    toggleLevelSelection(levelName) {
      const index = this.privilegeForm.applicableLevels.indexOf(levelName);
      if (index === -1) {
        this.privilegeForm.applicableLevels.push(levelName);
      } else {
        this.privilegeForm.applicableLevels.splice(index, 1);
      }
    },
    
    // 保存特权表单
    savePrivilegeForm() {
      // 表单验证
      if (!this.privilegeForm.name) {
        uni.showToast({
          title: '请输入特权名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.privilegeForm.type) {
        uni.showToast({
          title: '请选择特权类型',
          icon: 'none'
        });
        return;
      }
      
      if (!this.privilegeForm.value) {
        uni.showToast({
          title: '请输入特权价值',
          icon: 'none'
        });
        return;
      }
      
      if (this.privilegeForm.applicableLevels.length === 0) {
        uni.showToast({
          title: '请选择适用等级',
          icon: 'none'
        });
        return;
      }
      
      if (this.isEditing) {
        // 编辑现有特权
        const index = this.privileges.findIndex(item => item.id === this.currentPrivilegeId);
        if (index !== -1) {
          this.privileges[index] = { ...this.privilegeForm, id: this.currentPrivilegeId };
        }
      } else {
        // 添加新特权
        const newPrivilege = {
          ...this.privilegeForm,
          id: Date.now().toString() // 生成临时ID，实际项目中应由后端生成
        };
        this.privileges.push(newPrivilege);
      }
      
      // 关闭弹窗
      this.$refs.privilegeFormPopup.close();
      this.resetPrivilegeForm();
      
      // 提示成功
      uni.showToast({
        title: this.isEditing ? '编辑成功' : '添加成功',
        icon: 'success'
      });
    },
    
    // 确认删除特权
    confirmDeletePrivilege(privilege) {
      this.currentPrivilegeId = privilege.id;
      this.$refs.deleteConfirmPopup.open();
    },
    
    // 关闭删除确认弹窗
    closeDeleteConfirm() {
      this.$refs.deleteConfirmPopup.close();
    },
    
    // 删除特权
    deletePrivilege() {
      const index = this.privileges.findIndex(item => item.id === this.currentPrivilegeId);
      if (index !== -1) {
        this.privileges.splice(index, 1);
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
      this.currentPrivilegeId = null;
    }
  }
};
</script>

<style lang="scss" scoped>
/* 会员特权页面样式开始 */
.member-privilege-container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-section {
  display: flex;
  flex-direction: column;
  
  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .page-subtitle {
    font-size: 24rpx;
    color: #666;
    margin-top: 8rpx;
  }
}

.add-privilege-btn {
  display: flex;
  align-items: center;
  background-color: #FF6B22;
  color: #fff;
  padding: 16rpx 30rpx;
  border-radius: 50rpx;
  
  .btn-text {
    font-size: 28rpx;
  }
  
  .btn-icon {
    font-size: 32rpx;
    margin-left: 8rpx;
  }
}

/* 特权类型快捷入口样式 */
.privilege-shortcuts {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 10rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .shortcut-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 20%;
    
    .shortcut-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;
      
      .icon-text {
        color: #fff;
        font-size: 36rpx;
        font-weight: bold;
      }
    }
    
    .shortcut-name {
      font-size: 24rpx;
      color: #333;
    }
  }
}

.privilege-list {
  margin-top: 20rpx;
  
  .empty-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    background-color: #fff;
    border-radius: 12rpx;
    
    .empty-icon {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.privilege-cards {
  .privilege-card {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .privilege-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 24rpx;
      color: #fff;
      
      .privilege-name {
        font-size: 32rpx;
        font-weight: bold;
      }
      
      .privilege-actions {
        display: flex;
        
        .action-btn {
          font-size: 24rpx;
          padding: 6rpx 16rpx;
          border-radius: 30rpx;
          margin-left: 16rpx;
          
          &.edit {
            background-color: rgba(255, 255, 255, 0.3);
          }
          
          &.delete {
            background-color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
    
    .privilege-card-body {
      padding: 24rpx;
      
      .privilege-info-item {
        display: flex;
        margin-bottom: 16rpx;
        
        .info-label {
          width: 160rpx;
          font-size: 28rpx;
          color: #666;
        }
        
        .info-value {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
        
        .privilege-icon {
          width: 60rpx;
          height: 60rpx;
        }
        
        .level-tags {
          display: flex;
          flex-wrap: wrap;
          
          .level-tag {
            font-size: 24rpx;
            color: #4A00E0;
            background-color: rgba(74, 0, 224, 0.1);
            padding: 6rpx 16rpx;
            border-radius: 6rpx;
            margin-right: 12rpx;
            margin-bottom: 12rpx;
          }
        }
      }
    }
  }
}

.privilege-form-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    border-bottom: 1rpx solid #eee;
    
    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .popup-close {
      font-size: 40rpx;
      color: #999;
    }
  }
  
  .popup-body {
    padding: 24rpx;
    max-height: 60vh;
    overflow-y: auto;
    
    .form-item {
      margin-bottom: 24rpx;
      
      .form-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 12rpx;
      }
      
      .form-input {
        width: 100%;
        height: 80rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
      
      .form-textarea {
        width: 100%;
        height: 160rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        padding: 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
      
      .form-picker {
        width: 100%;
        height: 80rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        
        .picker-value {
          color: #333;
        }
      }
      
      .color-picker {
        display: flex;
        flex-wrap: wrap;
        
        .color-option {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          position: relative;
          
          &.active::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 32rpx;
          }
        }
      }
      
      .icon-upload {
        .preview-icon {
          width: 100rpx;
          height: 100rpx;
          border-radius: 8rpx;
        }
        
        .upload-btn {
          width: 100rpx;
          height: 100rpx;
          border: 1rpx dashed #ddd;
          border-radius: 8rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          
          .upload-icon {
            font-size: 40rpx;
            color: #999;
            margin-bottom: 4rpx;
          }
          
          .upload-text {
            font-size: 20rpx;
            color: #999;
          }
        }
      }
      
      .level-checkboxes {
        display: flex;
        flex-wrap: wrap;
        
        .level-checkbox {
          display: flex;
          align-items: center;
          margin-right: 30rpx;
          margin-bottom: 20rpx;
          
          .checkbox-icon {
            width: 40rpx;
            height: 40rpx;
            border: 1rpx solid #ddd;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10rpx;
            color: #fff;
            font-size: 24rpx;
          }
          
          &.active .checkbox-icon {
            background-color: #4A00E0;
            border-color: #4A00E0;
          }
          
          .checkbox-label {
            font-size: 28rpx;
            color: #333;
          }
        }
      }
    }
  }
  
  .popup-footer {
    display: flex;
    border-top: 1rpx solid #eee;
    
    button {
      flex: 1;
      height: 90rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      border-radius: 0;
      
      &.cancel-btn {
        background-color: #f5f5f5;
        color: #666;
      }
      
      &.confirm-btn {
        background-color: #4A00E0;
        color: #fff;
      }
    }
  }
}
</style>
<!-- 会员特权页面结束 --> 