/**
 * 红包组件模块
 */
import RedPacketCard from './RedPacketCard.vue';
import RedPacketPopup from './RedPacketPopup.vue';
import RedPacketCreator from './RedPacketCreator.vue';
import RedPacketSelector from './RedPacketSelector.vue';

// 安装方法
const install = function(Vue) {
  if (install.installed) return;
  install.installed = true;
  
  // 注册组件
  Vue.component(RedPacketCard.name, RedPacketCard);
  Vue.component(RedPacketPopup.name, RedPacketPopup);
  Vue.component(RedPacketCreator.name, RedPacketCreator);
  Vue.component(RedPacketSelector.name, RedPacketSelector);
};

// 自动安装
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export {
  RedPacketCard,
  RedPacketPopup,
  RedPacketCreator,
  RedPacketSelector
};

export default {
  install,
}; 