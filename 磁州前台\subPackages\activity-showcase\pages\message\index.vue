<template>
  <view class="message-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="navbar-title">消息</view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 消息分类 -->
      <view class="message-tabs">
        <view class="tab-item" :class="{ active: currentTab === 'all' }" @click="switchMessageTab('all')">
          <text>全部</text>
        </view>
        <view class="tab-item" :class="{ active: currentTab === 'system' }" @click="switchMessageTab('system')">
          <text>系统通知</text>
          <view class="tab-badge" v-if="unreadCounts.system > 0">{{ unreadCounts.system }}</view>
        </view>
        <view class="tab-item" :class="{ active: currentTab === 'activity' }" @click="switchMessageTab('activity')">
          <text>活动消息</text>
          <view class="tab-badge" v-if="unreadCounts.activity > 0">{{ unreadCounts.activity }}</view>
        </view>
        <view class="tab-item" :class="{ active: currentTab === 'interaction' }" @click="switchMessageTab('interaction')">
          <text>互动消息</text>
          <view class="tab-badge" v-if="unreadCounts.interaction > 0">{{ unreadCounts.interaction }}</view>
        </view>
      </view>
      
      <!-- 消息列表 -->
      <view class="message-list">
        <view class="empty-tip" v-if="filteredMessages.length === 0">
          <image class="empty-icon" src="/static/images/message/empty-message.png"></image>
          <text class="empty-text">暂无消息</text>
        </view>
        
        <view class="message-item" v-for="(message, index) in filteredMessages" :key="index" @click="readMessage(message)">
          <view class="message-avatar">
            <image :src="message.avatar" mode="aspectFill"></image>
            <view class="message-dot" v-if="!message.isRead"></view>
          </view>
          <view class="message-content">
            <view class="message-header">
              <text class="message-title">{{ message.title }}</text>
              <text class="message-time">{{ formatTime(message.time) }}</text>
            </view>
            <view class="message-body">{{ message.content }}</view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <view class="tabbar">
      <view 
        class="tabbar-item" 
        @click="switchTab('home')"
        data-tab="home"
      >
        <view class="tab-icon home"></view>
        <text class="tabbar-text">首页</text>
      </view>
      <view 
        class="tabbar-item" 
        @click="switchTab('discover')"
        data-tab="discover"
      >
        <view class="tab-icon discover"></view>
        <text class="tabbar-text">本地商城</text>
      </view>
      <view 
        class="tabbar-item" 
        @click="switchTab('distribution')"
        data-tab="distribution"
      >
        <view class="tab-icon distribution"></view>
        <text class="tabbar-text">分销</text>
      </view>
      <view 
        class="tabbar-item active" 
        data-tab="message"
      >
        <view class="tab-icon message">
          <view class="badge" v-if="unreadCounts.system + unreadCounts.activity + unreadCounts.interaction > 0">{{ unreadCounts.system + unreadCounts.activity + unreadCounts.interaction > 99 ? '99+' : unreadCounts.system + unreadCounts.activity + unreadCounts.interaction }}</view>
        </view>
        <text class="tabbar-text active-text">消息</text>
      </view>
      <view 
        class="tabbar-item" 
        @click="switchTab('my')"
        data-tab="my"
      >
        <view class="tab-icon user"></view>
        <text class="tabbar-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 'all',
      unreadCounts: {
        system: 2,
        activity: 1,
        interaction: 3
      },
      messages: [
        {
          id: 1,
          type: 'system',
          title: '系统通知',
          content: '您的账号已成功注册，欢迎使用我们的服务！',
          avatar: 'https://via.placeholder.com/80x80',
          time: '2023-12-01 10:30:00',
          isRead: false
        },
        {
          id: 2,
          type: 'system',
          title: '安全提醒',
          content: '您的账号于今日10:30在新设备上登录，如非本人操作，请及时修改密码。',
          avatar: 'https://via.placeholder.com/80x80',
          time: '2023-12-02 15:20:00',
          isRead: false
        },
        {
          id: 3,
          type: 'activity',
          title: '拼团成功',
          content: '恭喜您！您参与的"iPhone 14 Pro"拼团已成功，订单正在处理中。',
          avatar: 'https://via.placeholder.com/80x80',
          time: '2023-12-03 09:15:00',
          isRead: true
        },
        {
          id: 4,
          type: 'activity',
          title: '优惠券到账',
          content: '您有一张满100减20的优惠券已到账，有效期至2023-12-31。',
          avatar: 'https://via.placeholder.com/80x80',
          time: '2023-12-04 14:05:00',
          isRead: false
        },
        {
          id: 5,
          type: 'interaction',
          title: '新的点赞',
          content: '用户"张三"点赞了您的评论："这个产品真的很好用！"',
          avatar: 'https://via.placeholder.com/80x80',
          time: '2023-12-05 11:30:00',
          isRead: false
        },
        {
          id: 6,
          type: 'interaction',
          title: '新的评论',
          content: '用户"李四"回复了您的评论："谢谢分享，我也觉得不错。"',
          avatar: 'https://via.placeholder.com/80x80',
          time: '2023-12-06 16:45:00',
          isRead: false
        },
        {
          id: 7,
          type: 'interaction',
          title: '新的关注',
          content: '用户"王五"关注了您，去看看Ta的主页吧！',
          avatar: 'https://via.placeholder.com/80x80',
          time: '2023-12-07 08:20:00',
          isRead: false
        }
      ]
    }
  },
  computed: {
    // 根据当前选中的标签过滤消息
    filteredMessages() {
      if (this.currentTab === 'all') {
        return this.messages;
      } else {
        return this.messages.filter(message => message.type === this.currentTab);
      }
    },
    
    // 计算总未读消息数
    totalUnreadCount() {
      return this.unreadCounts.system + this.unreadCounts.activity + this.unreadCounts.interaction;
    }
  },
  onLoad() {
    // 页面加载时获取数据
    this.fetchMessages();
  },
  methods: {
    // 获取消息列表
    fetchMessages() {
      // 模拟从服务器获取消息
      // 实际应用中应该调用API
      // 示例数据已在data中定义
      
      // 计算未读消息数量
      this.calculateUnreadCounts();
    },
    
    // 计算各类型未读消息数量
    calculateUnreadCounts() {
      const counts = {
        system: 0,
        activity: 0,
        interaction: 0
      };
      
      this.messages.forEach(message => {
        if (!message.isRead) {
          counts[message.type]++;
        }
      });
      
      this.unreadCounts = counts;
    },
    
    // 切换消息标签
    switchMessageTab(tab) {
      this.currentTab = tab;
    },
    
    // 阅读消息
    readMessage(message) {
      // 标记消息为已读
      if (!message.isRead) {
        message.isRead = true;
        this.calculateUnreadCounts();
      }
      
      // 导航到消息详情页
      uni.navigateTo({
        url: `/subPackages/activity-showcase/pages/message/detail?id=${message.id}`
      });
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const now = new Date();
      const messageTime = new Date(timestamp);
      const diff = now - messageTime;
      
      // 如果是今天的消息，只显示时间
      if (diff < 24 * 60 * 60 * 1000 && 
          now.getDate() === messageTime.getDate() &&
          now.getMonth() === messageTime.getMonth() &&
          now.getFullYear() === messageTime.getFullYear()) {
        return messageTime.toTimeString().slice(0, 5);
      }
      
      // 如果是昨天的消息
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);
      if (yesterday.getDate() === messageTime.getDate() &&
          yesterday.getMonth() === messageTime.getMonth() &&
          yesterday.getFullYear() === messageTime.getFullYear()) {
        return '昨天 ' + messageTime.toTimeString().slice(0, 5);
      }
      
      // 如果是今年的消息
      if (now.getFullYear() === messageTime.getFullYear()) {
        return `${messageTime.getMonth() + 1}月${messageTime.getDate()}日`;
      }
      
      // 其他情况显示完整日期
      return `${messageTime.getFullYear()}/${messageTime.getMonth() + 1}/${messageTime.getDate()}`;
    },
    
    // 切换底部导航标签页
    switchTab(tab) {
      switch(tab) {
        case 'home':
          uni.redirectTo({
            url: '/subPackages/activity-showcase/pages/index/index'
          });
          break;
        case 'discover':
          uni.redirectTo({
            url: '/subPackages/activity-showcase/pages/discover/index'
          });
          break;
        case 'distribution':
          uni.redirectTo({
            url: '/subPackages/activity-showcase/pages/distribution/index'
          });
          break;
        case 'my':
          uni.redirectTo({
            url: '/subPackages/activity-showcase/pages/my/index'
          });
          break;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.message-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-title {
    font-size: 18px;
    font-weight: 600;
    color: #FFFFFF;
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  padding-bottom: 15rpx;
  margin-bottom: 100rpx; /* 为底部导航栏留出空间 */
}

/* 消息分类标签 */
.message-tabs {
  display: flex;
  background-color: #FFFFFF;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  
  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 10rpx 0;
    
    text {
      font-size: 28rpx;
      color: #666666;
      transition: color 0.3s ease;
    }
    
    &.active text {
      color: #FF2C54;
      font-weight: 600;
    }
    
    &.active::after {
      content: '';
      position: absolute;
      bottom: -10rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: #FF2C54;
      border-radius: 2rpx;
    }
    
    .tab-badge {
      position: absolute;
      top: 0;
      right: 50%;
      transform: translateX(20rpx);
      min-width: 32rpx;
      height: 32rpx;
      border-radius: 16rpx;
      background-color: #FF3B30;
      color: #FFFFFF;
      font-size: 18rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 6rpx;
      box-sizing: border-box;
      font-weight: 600;
    }
  }
}

/* 消息列表 */
.message-list {
  padding: 0 20rpx;
}

.message-item {
  display: flex;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.message-avatar {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  
  image {
    width: 100%;
    height: 100%;
    border-radius: 40rpx;
  }
  
  .message-dot {
    position: absolute;
    top: 0;
    right: 0;
    width: 20rpx;
    height: 20rpx;
    border-radius: 10rpx;
    background-color: #FF3B30;
    border: 2rpx solid #FFFFFF;
  }
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.message-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.message-time {
  font-size: 22rpx;
  color: #999999;
}

.message-body {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

/* 空状态提示 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #999999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 34px; /* iOS 安全区域高度 */
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 99;
  border-top: 1rpx solid #EEEEEE;
}
  
  .tabbar-item {
  flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}
    
.tabbar-item:active {
  transform: scale(0.9);
}
    
.tabbar-item.active .tab-icon {
  transform: translateY(-5rpx);
}
      
.tabbar-item.active .tabbar-text {
  color: #FF3B69;
  font-weight: 600;
  transform: translateY(-2rpx);
}
    
.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
      display: flex;
      justify-content: center;
      align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 首页图标 */
.tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="home"] .tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

/* 发现图标 */
.tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="discover"] .tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

/* 分销图标 */
.tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="distribution"] .tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

/* 消息图标 */
.tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="message"] .tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 我的图标 */
.tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="my"] .tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active .tab-icon {
  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));
}
      
      .badge {
        position: absolute;
  top: -8rpx;
        right: -12rpx;
        min-width: 32rpx;
        height: 32rpx;
        border-radius: 16rpx;
  background: linear-gradient(135deg, #FF453A, #FF2D55);
        color: #FFFFFF;
        font-size: 18rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 6rpx;
        box-sizing: border-box;
        font-weight: 600;
  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transform: scale(0.9);
    }
    
    .tabbar-text {
  font-size: 22rpx;
      color: #8E8E93;
  margin-top: 2rpx;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
    
.tabbar-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 30rpx;
  height: 4rpx;
  background: #FF3B69;
  border-radius: 2rpx;
  transition: transform 0.3s ease;
}
    
.tabbar-item.active::after {
  transform: translateX(-50%) scaleX(1);
}
</style> 