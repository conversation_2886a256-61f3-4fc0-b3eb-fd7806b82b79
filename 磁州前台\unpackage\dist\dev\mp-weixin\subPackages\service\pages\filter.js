"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "filter",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const serviceTitle = common_vendor.ref("服务筛选");
    const serviceType = common_vendor.ref("find");
    const refreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const page = common_vendor.ref(1);
    const serviceList = common_vendor.ref([]);
    const filterHistory = common_vendor.ref([]);
    const showSearchBox = common_vendor.ref(true);
    const loading = common_vendor.ref(false);
    const showAreaFilter = common_vendor.ref(false);
    const showSubcategoryFilter = common_vendor.ref(false);
    const showSortFilter = common_vendor.ref(false);
    const selectedArea = common_vendor.ref("全部区域");
    const selectedCategory = common_vendor.ref("全部分类");
    const selectedSubcategory = common_vendor.ref("");
    const selectedSort = common_vendor.ref("默认排序");
    const filterDropdownTop = common_vendor.ref(0);
    const subcategoryDropdownTop = common_vendor.ref(0);
    const areaFilterLeft = common_vendor.ref(12);
    const sortFilterLeft = common_vendor.ref(0);
    const filterItemWidth = common_vendor.ref(0);
    const areaList = common_vendor.ref(["全部区域", "城区", "磁州镇", "讲武城镇", "岳城镇", "观台镇", "白土镇", "黄沙镇"]);
    const sortList = common_vendor.ref(["默认排序", "价格最低", "价格最高", "距离最近"]);
    const categoryList = common_vendor.ref([]);
    const subcategoryList = common_vendor.ref([]);
    const homeServiceTypes = common_vendor.ref(["home_service", "home_cleaning", "repair", "installation", "locksmith", "moving", "beauty", "tutor", "pet_service", "plumbing"]);
    const topCategories = common_vendor.ref([
      { type: "home_service", name: "到家服务" },
      { type: "home_cleaning", name: "家政保洁" },
      { type: "repair", name: "维修服务" },
      { type: "installation", name: "安装服务" },
      { type: "locksmith", name: "开锁换锁" },
      { type: "moving", name: "搬家拉货" },
      { type: "beauty", name: "美容美发" },
      { type: "tutor", name: "家教辅导" },
      { type: "pet_service", name: "宠物服务" },
      { type: "plumbing", name: "管道疏通" },
      { type: "find", name: "寻找服务" },
      { type: "business", name: "生意转让" },
      { type: "job", name: "招聘信息" },
      { type: "resume", name: "求职信息" },
      { type: "house_rent", name: "房屋出租" },
      { type: "house_sell", name: "房屋出售" },
      { type: "second_car", name: "二手车辆" },
      { type: "pet", name: "宠物信息" },
      { type: "car", name: "车辆服务" },
      { type: "second_hand", name: "二手闲置" },
      { type: "carpool", name: "磁州拼车" },
      { type: "education", name: "教育培训" },
      { type: "other", name: "其他服务" }
    ]);
    const currentTopCategory = common_vendor.ref("find");
    const searchKeyword = common_vendor.ref("");
    const extractedKeywords = common_vendor.ref([]);
    const selectedKeywords = common_vendor.ref([]);
    const hasActiveFilters = common_vendor.computed(() => {
      return selectedArea.value !== "全部区域" || selectedCategory.value !== "全部分类" || selectedSubcategory.value !== "" || selectedSort.value !== "默认排序";
    });
    const filteredTopCategories = common_vendor.computed(() => {
      if (homeServiceTypes.value.includes(serviceType.value)) {
        const homeServiceCategories = topCategories.value.filter(
          (item) => homeServiceTypes.value.includes(item.type)
        );
        return homeServiceCategories;
      }
      const mainServiceTypes = [
        { type: "find", name: "寻找服务" },
        { type: "business", name: "生意转让" },
        { type: "job", name: "招聘信息" },
        { type: "resume", name: "求职信息" },
        { type: "house_rent", name: "房屋出租" },
        { type: "house_sell", name: "房屋出售" },
        { type: "second_car", name: "二手车辆" },
        { type: "pet", name: "宠物信息" },
        { type: "car", name: "车辆服务" },
        { type: "second_hand", name: "二手闲置" },
        { type: "carpool", name: "磁州拼车" },
        { type: "education", name: "教育培训" },
        { type: "other", name: "其他服务" }
      ];
      return mainServiceTypes;
    });
    const setServiceTitle = (type) => {
      const titleMap = {
        "find": "寻找服务",
        "business": "生意转让",
        "job": "招聘信息",
        "resume": "求职信息",
        "house_rent": "房屋出租",
        "house_sell": "房屋出售",
        "second_car": "二手车辆",
        "pet": "宠物信息",
        "car": "车辆服务",
        "second_hand": "二手闲置",
        "carpool": "磁州拼车",
        "education": "教育培训",
        "other": "其他服务"
      };
      serviceTitle.value = titleMap[type] || "服务筛选";
    };
    const loadFilterHistory = () => {
      try {
        const key = `filter_history_${serviceType.value}`;
        const history = common_vendor.index.getStorageSync(key);
        if (history) {
          filterHistory.value = JSON.parse(history);
          common_vendor.index.__f__("log", "at subPackages/service/pages/filter.vue:376", "加载筛选历史:", filterHistory.value);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at subPackages/service/pages/filter.vue:379", "加载筛选历史失败", e);
      }
    };
    const saveFilterHistory = () => {
      try {
        const currentFilter = {
          area: selectedArea.value,
          category: selectedCategory.value,
          subcategory: selectedSubcategory.value,
          sort: selectedSort.value,
          timestamp: (/* @__PURE__ */ new Date()).getTime()
        };
        filterHistory.value.unshift(currentFilter);
        filterHistory.value = filterHistory.value.slice(0, 5);
        const key = `filter_history_${serviceType.value}`;
        common_vendor.index.setStorageSync(key, JSON.stringify(filterHistory.value));
      } catch (e) {
        common_vendor.index.__f__("error", "at subPackages/service/pages/filter.vue:405", "保存筛选历史失败", e);
      }
    };
    const setCategoryOptions = (type) => {
      const categoryOptions = {
        // 到家服务子分类
        "home_service": ["全部分类", "家政保洁", "维修服务", "安装服务", "开锁换锁", "搬家拉货", "美容美发", "家教辅导", "宠物服务", "管道疏通"],
        "home_cleaning": ["全部分类", "日常保洁", "深度保洁", "开荒保洁", "家电清洗", "玻璃清洗"],
        "repair": ["全部分类", "水电维修", "家电维修", "门窗维修", "墙面翻新", "其他维修"],
        "installation": ["全部分类", "家具安装", "家电安装", "灯具安装", "门窗安装", "其他安装"],
        "locksmith": ["全部分类", "开锁服务", "换锁服务", "汽车开锁", "保险柜开锁", "智能锁安装"],
        "moving": ["全部分类", "小型搬家", "大型搬家", "长途搬家", "物品运输", "其他搬运"],
        "beauty": ["全部分类", "美甲美睫", "美容护肤", "美发造型", "化妆服务", "其他美容"],
        "tutor": ["全部分类", "小学辅导", "初中辅导", "高中辅导", "艺术辅导", "其他辅导"],
        "pet_service": ["全部分类", "宠物洗澡", "宠物美容", "宠物寄养", "宠物遛狗", "其他服务"],
        "plumbing": ["全部分类", "管道疏通", "马桶疏通", "下水道疏通", "厨房疏通", "其他疏通"],
        // 原有的一级分类
        "find": ["全部分类", "维修", "装修", "搬家", "清洁", "美容美发", "教育培训", "其他"],
        "business": ["全部分类", "餐饮店", "便利店", "服装店", "美容店", "超市", "其他店铺"],
        "job": ["全部分类", "销售", "服务员", "技工", "司机", "厨师", "文员", "其他职位"],
        "resume": ["全部分类", "销售类", "技术类", "服务类", "行政类", "教育类", "其他类"],
        "house_rent": ["全部分类", "整租", "合租", "短租", "商铺", "写字楼", "厂房"],
        "house_sell": ["全部分类", "普通住宅", "别墅", "写字楼", "商铺", "厂房", "土地"],
        "second_car": ["全部分类", "轿车", "SUV", "面包车", "货车", "电动车", "摩托车"],
        "pet": ["全部分类", "宠物狗", "宠物猫", "小宠", "宠物用品", "宠物服务"],
        "car": ["全部分类", "洗车", "保养", "维修", "租车", "陪驾", "其他"],
        "second_hand": ["全部分类", "手机", "电脑", "家具", "家电", "服装", "图书", "其他"],
        "carpool": ["全部分类", "上下班", "周末", "节假日", "城际", "长途", "其他"],
        "education": ["全部分类", "幼儿", "小学", "初中", "高中", "大学", "职业培训", "其他"],
        "other": ["全部分类", "家政", "维修", "租赁", "其他"]
      };
      categoryList.value = categoryOptions[type] || ["全部分类"];
      if (type === "job") {
        categoryList.value = ["全部分类", "销售", "服务员", "技工", "司机", "厨师", "文员", "其他职位"];
      }
    };
    const selectArea = (area) => {
      common_vendor.index.vibrateShort();
      selectedArea.value = area;
      showAreaFilter.value = false;
      resetListAndReload();
      if (hasActiveFilters.value) {
        saveFilterHistory();
      }
    };
    const selectCategory = (category) => {
      common_vendor.index.vibrateShort();
      selectedCategory.value = category;
      if (category === "全部分类") {
        selectedSubcategory.value = "";
        subcategoryList.value = [];
        showSubcategoryFilter.value = false;
      } else {
        setSubcategoryOptions(category);
        if (serviceType.value === "job" || currentTopCategory.value === "job") {
          showSubcategoryFilter.value = false;
        } else if (subcategoryList.value.length > 0) {
          showSubcategoryFilter.value = true;
          common_vendor.nextTick$1(() => {
            const query = common_vendor.index.createSelectorQuery().in(this);
            query.select(".filter-section").boundingClientRect((container) => {
              if (container) {
                if (hasActiveFilters.value) {
                  query.select(".selected-filters").boundingClientRect((filters) => {
                    if (filters) {
                      subcategoryDropdownTop.value = filters.height + filters.top;
                    } else {
                      subcategoryDropdownTop.value = container.height + container.top;
                    }
                  }).exec();
                } else {
                  subcategoryDropdownTop.value = container.height + container.top;
                }
              }
            }).exec();
          });
        } else {
          showSubcategoryFilter.value = false;
        }
      }
      resetListAndReload();
      if (hasActiveFilters.value) {
        saveFilterHistory();
      }
    };
    const selectSubcategory = (subcategory) => {
      common_vendor.index.vibrateShort();
      selectedSubcategory.value = subcategory;
      showSubcategoryFilter.value = false;
      resetListAndReload();
      if (hasActiveFilters.value) {
        saveFilterHistory();
      }
    };
    const selectSort = (sort) => {
      common_vendor.index.vibrateShort();
      selectedSort.value = sort;
      showSortFilter.value = false;
      resetListAndReload();
      if (hasActiveFilters.value) {
        saveFilterHistory();
      }
    };
    const setSubcategoryOptions = (category) => {
      const subcategoryMap = {
        // 到家服务子分类的子分类
        "家政保洁": ["日常保洁", "深度保洁", "开荒保洁", "家电清洗", "玻璃清洗"],
        "维修服务": ["水电维修", "家电维修", "门窗维修", "墙面翻新", "其他维修"],
        "安装服务": ["家具安装", "家电安装", "灯具安装", "门窗安装", "其他安装"],
        "开锁换锁": ["开锁服务", "换锁服务", "汽车开锁", "保险柜开锁", "智能锁安装"],
        "搬家拉货": ["小型搬家", "大型搬家", "长途搬家", "物品运输", "其他搬运"],
        "美容美发": ["美甲美睫", "美容护肤", "美发造型", "化妆服务", "其他美容"],
        "家教辅导": ["小学辅导", "初中辅导", "高中辅导", "艺术辅导", "其他辅导"],
        "宠物服务": ["宠物洗澡", "宠物美容", "宠物寄养", "宠物遛狗", "其他服务"],
        "管道疏通": ["管道疏通", "马桶疏通", "下水道疏通", "厨房疏通", "其他疏通"],
        // 原有的分类子分类 - 除了到家服务子分类外，其他所有分类的三级分类全部删掉
        "business": {},
        "job": {},
        "second_hand": {},
        "house_rent": {},
        "house_sell": {},
        "second_car": {},
        "pet": {},
        "car": {},
        "carpool": {},
        "education": {},
        "other": {}
      };
      if (homeServiceTypes.value.includes(serviceType.value)) {
        subcategoryList.value = subcategoryMap[category] || [];
      } else {
        const currentTypeSubcats = subcategoryMap[serviceType.value] || {};
        subcategoryList.value = currentTypeSubcats[category] || [];
      }
    };
    const closeAllFilters = () => {
      showAreaFilter.value = false;
      showSubcategoryFilter.value = false;
      showSortFilter.value = false;
    };
    const resetAllFilters = () => {
      common_vendor.index.vibrateShort({ type: "medium" });
      selectedArea.value = "全部区域";
      selectedCategory.value = "全部分类";
      selectedSubcategory.value = "";
      selectedSort.value = "默认排序";
      resetListAndReload();
    };
    const resetArea = () => {
      selectedArea.value = "全部区域";
      resetListAndReload();
    };
    const resetSubcategory = () => {
      selectedSubcategory.value = "";
      resetListAndReload();
    };
    const resetSort = () => {
      selectedSort.value = "默认排序";
      resetListAndReload();
    };
    const resetListAndReload = () => {
      page.value = 1;
      serviceList.value = [];
      hasMore.value = true;
      loadServiceList();
    };
    const loadServiceList = () => {
      if (loading.value)
        return;
      loading.value = true;
      const params = {
        page: page.value,
        pageSize: 10,
        category: selectedCategory.value === "全部分类" ? "" : selectedCategory.value,
        subcategory: selectedSubcategory.value,
        area: selectedArea.value === "全部区域" ? "" : selectedArea.value,
        sort: selectedSort.value === "默认排序" ? "" : selectedSort.value,
        keyword: searchKeyword.value
      };
      const categoryMap = {
        "job": "招聘信息",
        "resume": "求职信息",
        "business": "生意转让",
        "house_rent": "房屋出租",
        "house_sell": "房屋出售",
        "second_car": "二手车辆",
        "pet": "宠物信息",
        "car": "车辆服务",
        "second_hand": "二手闲置",
        "carpool": "磁州拼车",
        "education": "教育培训",
        "other": "其他服务"
      };
      if (categoryMap[serviceType.value] || categoryMap[currentTopCategory.value]) {
        params.category = categoryMap[currentTopCategory.value] || categoryMap[serviceType.value];
        common_vendor.index.__f__("log", "at subPackages/service/pages/filter.vue:681", `应用特殊分类筛选: ${params.category}`);
      }
      common_vendor.index.__f__("log", "at subPackages/service/pages/filter.vue:684", "加载服务列表参数:", params);
      "../../../mock/api.js".then((module) => {
        const { serviceApi } = module;
        if (!serviceApi || !serviceApi.fetchPublishList) {
          common_vendor.index.__f__("error", "at subPackages/service/pages/filter.vue:692", "API不可用:", serviceApi);
          loading.value = false;
          refreshing.value = false;
          return;
        }
        Promise.resolve().then(() => serviceApi.fetchPublishList(params)).then((res) => {
          if (page.value === 1) {
            serviceList.value = res.list || [];
          } else {
            serviceList.value = [...serviceList.value, ...res.list || []];
          }
          hasMore.value = res.hasMore;
          loading.value = false;
          refreshing.value = false;
          common_vendor.index.__f__("log", "at subPackages/service/pages/filter.vue:712", "加载服务列表成功:", serviceList.value.length);
        }).catch((err) => {
          common_vendor.index.__f__("error", "at subPackages/service/pages/filter.vue:715", "加载服务列表失败", err);
          loading.value = false;
          refreshing.value = false;
        });
      }).catch((err) => {
        common_vendor.index.__f__("error", "at subPackages/service/pages/filter.vue:720", "导入API模块失败", err);
        loading.value = false;
        refreshing.value = false;
      });
    };
    const onRefresh = () => {
      refreshing.value = true;
      resetListAndReload();
      common_vendor.index.vibrateShort();
    };
    const loadMore = () => {
      if (hasMore.value) {
        page.value++;
        loadServiceList();
      }
    };
    const navigateToDetail = (item) => {
      let url = `/pages/publish/info-detail?id=${item.id}`;
      const detailPageMap = {
        "find": "find-service-detail",
        "business": "business-transfer-detail",
        "job": "job-detail",
        "resume": "job-seeking-detail",
        "house_rent": "house-rent-detail",
        "house_sell": "house-sale-detail",
        "second_car": "car-detail",
        "pet": "pet-detail",
        "car": "vehicle-service-detail",
        "second_hand": "second-hand-detail",
        "carpool": "carpool-detail",
        "education": "education-detail",
        "dating": "dating-detail",
        "merchant_activity": "merchant-activity-detail",
        "home_service": "home-service-detail",
        "home_cleaning": "home-service-detail",
        "repair": "home-service-detail",
        "installation": "home-service-detail",
        "locksmith": "home-service-detail",
        "moving": "home-service-detail",
        "beauty": "home-service-detail",
        "tutor": "home-service-detail",
        "pet_service": "home-service-detail",
        "plumbing": "home-service-detail"
      };
      if (detailPageMap[serviceType.value]) {
        url = `/pages/publish/${detailPageMap[serviceType.value]}?id=${item.id}`;
      }
      common_vendor.index.navigateTo({
        url
      });
    };
    const navigateToPublish = () => {
      common_vendor.index.vibrateShort();
      const publishTypeMap = {
        "find": "find_service",
        "business": "business_transfer",
        "job": "hire",
        "resume": "job_wanted",
        "house_rent": "house_rent",
        "house_sell": "house_sell",
        "second_car": "used_car",
        "pet": "pet",
        "car": "car_service",
        "second_hand": "second_hand",
        "carpool": "carpool",
        "education": "education",
        "other": "other_service"
      };
      const publishType = publishTypeMap[serviceType.value] || "other_service";
      common_vendor.index.navigateTo({
        url: `/pages/publish/detail?type=${publishType}&name=${encodeURIComponent(serviceTitle.value)}`
      });
    };
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTopCategory = (item) => {
      common_vendor.index.vibrateShort();
      currentTopCategory.value = item.type;
      serviceType.value = item.type;
      setServiceTitle(item.type);
      selectedCategory.value = "全部分类";
      selectedSubcategory.value = "";
      setCategoryOptions(item.type);
      const specialCategoryMap = {
        "job": "招聘信息",
        "resume": "求职信息",
        "business": "生意转让",
        "house_rent": "房屋出租",
        "house_sell": "房屋出售",
        "second_car": "二手车辆",
        "pet": "宠物信息",
        "car": "车辆服务",
        "second_hand": "二手闲置",
        "carpool": "磁州拼车",
        "education": "教育培训"
      };
      if (specialCategoryMap[item.type]) {
        common_vendor.index.__f__("log", "at subPackages/service/pages/filter.vue:855", `设置特殊分类: ${specialCategoryMap[item.type]}`);
      }
      resetListAndReload();
    };
    const extractKeywords = () => {
      const input = searchKeyword.value.trim();
      if (!input)
        return;
      let words = input.split(/[\s,，.。;；!！?？、]/g).filter((word) => word.length >= 2).map((word) => word.trim()).filter((word) => word);
      words = [...new Set(words)];
      if (words.length > 5) {
        words = words.slice(0, 5);
      }
      addContextualKeywords(words);
      extractedKeywords.value = words;
      selectedKeywords.value = [...words];
      applyKeywordFilter();
    };
    const addContextualKeywords = (words) => {
      if (selectedCategory.value !== "全部分类") {
        if (!words.includes(selectedCategory.value)) {
          words.push(selectedCategory.value);
        }
      }
      if (selectedSubcategory.value && !words.includes(selectedSubcategory.value)) {
        words.push(selectedSubcategory.value);
      }
      if (selectedArea.value !== "全部区域" && !words.includes(selectedArea.value)) {
        words.push(selectedArea.value);
      }
      const commonKeywords = getCommonKeywords();
      for (const keyword of commonKeywords) {
        if (searchKeyword.value.includes(keyword) && !words.includes(keyword)) {
          words.push(keyword);
        }
      }
    };
    const getCommonKeywords = () => {
      const commonKeywordMap = {
        "find": ["维修", "安装", "保洁", "搬家", "上门", "快速", "专业", "经验", "价格优惠"],
        "business": ["转让", "盈利", "客源稳定", "位置好", "接手即可营业", "证件齐全", "房租低"],
        "job": ["招聘", "急聘", "有经验", "薪资高", "包吃住", "五险一金", "双休", "朝九晚五"],
        "resume": ["求职", "经验丰富", "应届毕业", "有证书", "会计", "司机", "销售", "文员"],
        "house_rent": ["出租", "整租", "合租", "精装", "家电齐全", "拎包入住", "交通便利", "南北通透"],
        "house_sell": ["出售", "精装修", "学区房", "交通便利", "地段好", "采光好", "低总价", "小户型"],
        "second_car": ["二手车", "准新车", "行驶里程少", "车况好", "价格优惠", "一手车", "无事故"],
        "pet": ["宠物", "幼犬", "幼猫", "疫苗已做", "驱虫已做", "包健康", "可上门看", "纯种"],
        "car": ["洗车", "保养", "维修", "补胎", "贴膜", "换机油", "年检", "过户"],
        "second_hand": ["二手", "全新", "九成新", "低价", "急售", "搬家甩卖", "有发票", "保修期内"],
        "education": ["培训", "辅导", "一对一", "小班", "提分快", "经验丰富", "上门", "包学会"]
      };
      return commonKeywordMap[serviceType.value] || [];
    };
    const applyKeywordSearch = () => {
      if (!searchKeyword.value.trim()) {
        return;
      }
      common_vendor.index.vibrateShort();
      extractKeywords();
    };
    const applyKeywordFilter = () => {
      resetListAndReload();
    };
    const getTopCategoryName = (type) => {
      const category = topCategories.value.find((item) => item.type === type);
      return category ? category.name : "";
    };
    const resetTopCategory = () => {
      currentTopCategory.value = "find";
      serviceType.value = "find";
      setServiceTitle("find");
      setCategoryOptions("find");
      resetListAndReload();
    };
    const resetCategory = () => {
      selectedCategory.value = "全部分类";
      selectedSubcategory.value = "";
      resetListAndReload();
    };
    const toggleAreaFilter = () => {
      showSortFilter.value = false;
      showSubcategoryFilter.value = false;
      showAreaFilter.value = !showAreaFilter.value;
      if (showAreaFilter.value) {
        common_vendor.nextTick$1(() => {
          const query = common_vendor.index.createSelectorQuery();
          query.select(".filter-section").boundingClientRect((filterSection) => {
            if (filterSection) {
              filterDropdownTop.value = filterSection.top + filterSection.height;
              query.select(".filter-item").boundingClientRect((areaItem) => {
                if (areaItem) {
                  areaFilterLeft.value = areaItem.left;
                  filterItemWidth.value = areaItem.width;
                }
              }).exec();
            }
          }).exec();
        });
      }
    };
    const toggleSortFilter = () => {
      showAreaFilter.value = false;
      showSubcategoryFilter.value = false;
      showSortFilter.value = !showSortFilter.value;
      if (showSortFilter.value) {
        common_vendor.nextTick$1(() => {
          const query = common_vendor.index.createSelectorQuery();
          query.select(".filter-section").boundingClientRect((filterSection) => {
            if (filterSection) {
              filterDropdownTop.value = filterSection.top + filterSection.height;
              query.selectAll(".filter-item").boundingClientRect((items) => {
                if (items && items.length >= 2) {
                  const sortItem = items[1];
                  sortFilterLeft.value = sortItem.left;
                  filterItemWidth.value = sortItem.width;
                }
              }).exec();
            }
          }).exec();
        });
      }
    };
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const page2 = pages[pages.length - 1];
      const options = ((_a = page2.$page) == null ? void 0 : _a.options) || {};
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      if (options.type) {
        serviceType.value = options.type;
        currentTopCategory.value = options.type;
        if (options.title) {
          serviceTitle.value = decodeURIComponent(options.title);
          common_vendor.index.__f__("log", "at subPackages/service/pages/filter.vue:1109", "使用传递的标题:", serviceTitle.value);
        } else {
          setServiceTitle(options.type);
        }
        loadFilterHistory();
        setCategoryOptions(options.type);
        const specialCategoryMap = {
          "job": "招聘信息",
          "resume": "求职信息",
          "business": "生意转让",
          "house_rent": "房屋出租",
          "house_sell": "房屋出售",
          "second_car": "二手车辆",
          "pet": "宠物信息",
          "car": "车辆服务",
          "second_hand": "二手闲置",
          "carpool": "磁州拼车",
          "education": "教育培训"
        };
        if (specialCategoryMap[options.type] && !options.category) {
          common_vendor.index.__f__("log", "at subPackages/service/pages/filter.vue:1137", `初始化特殊分类: ${specialCategoryMap[options.type]}`);
        }
      }
      if (options.active) {
        common_vendor.index.__f__("log", "at subPackages/service/pages/filter.vue:1143", "设置激活的顶部分类:", options.active);
        currentTopCategory.value = options.active;
        if (options.active !== serviceType.value) {
          serviceType.value = options.active;
          setCategoryOptions(options.active);
          setServiceTitle(options.active);
        }
      }
      if (options.area)
        selectedArea.value = options.area;
      if (options.category) {
        selectedCategory.value = options.category;
        if (selectedCategory.value !== "全部分类") {
          setSubcategoryOptions(selectedCategory.value);
        }
      }
      if (options.subcategory)
        selectedSubcategory.value = options.subcategory;
      if (options.sort)
        selectedSort.value = options.sort;
      setTimeout(() => {
        common_vendor.index.vibrateShort();
      }, 300);
      loadServiceList();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(navigateBack),
        b: common_vendor.t(serviceTitle.value),
        c: statusBarHeight.value + "px",
        d: showSearchBox.value
      }, showSearchBox.value ? {
        e: common_assets._imports_0$10,
        f: common_vendor.o(applyKeywordSearch),
        g: searchKeyword.value,
        h: common_vendor.o(($event) => searchKeyword.value = $event.detail.value),
        i: common_vendor.o(applyKeywordSearch)
      } : {}, {
        j: common_vendor.f(filteredTopCategories.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index,
            c: currentTopCategory.value === item.type ? 1 : "",
            d: common_vendor.o(($event) => switchTopCategory(item), index)
          };
        }),
        k: categoryList.value.length > 0
      }, categoryList.value.length > 0 ? {
        l: common_vendor.f(categoryList.value, (category, index, i0) => {
          return {
            a: common_vendor.t(category),
            b: index,
            c: selectedCategory.value === category ? 1 : "",
            d: common_vendor.o(($event) => selectCategory(category), index)
          };
        })
      } : {}, {
        m: common_vendor.t(selectedArea.value),
        n: selectedArea.value !== "全部区域" ? 1 : "",
        o: showAreaFilter.value ? 1 : "",
        p: common_vendor.o(toggleAreaFilter),
        q: common_vendor.t(selectedSort.value),
        r: selectedSort.value !== "默认排序" ? 1 : "",
        s: showSortFilter.value ? 1 : "",
        t: common_vendor.o(toggleSortFilter),
        v: hasActiveFilters.value
      }, hasActiveFilters.value ? common_vendor.e({
        w: currentTopCategory.value !== "find" && currentTopCategory.value !== "home_service"
      }, currentTopCategory.value !== "find" && currentTopCategory.value !== "home_service" ? {
        x: common_vendor.t(getTopCategoryName(currentTopCategory.value)),
        y: common_vendor.o(resetTopCategory)
      } : {}, {
        z: selectedCategory.value !== "全部分类" && selectedCategory.value !== ""
      }, selectedCategory.value !== "全部分类" && selectedCategory.value !== "" ? {
        A: common_vendor.t(selectedCategory.value),
        B: common_vendor.o(resetCategory)
      } : {}, {
        C: selectedSubcategory.value !== ""
      }, selectedSubcategory.value !== "" ? {
        D: common_vendor.t(selectedSubcategory.value),
        E: common_vendor.o(resetSubcategory)
      } : {}, {
        F: selectedArea.value !== "全部区域"
      }, selectedArea.value !== "全部区域" ? {
        G: common_vendor.t(selectedArea.value),
        H: common_vendor.o(resetArea)
      } : {}, {
        I: selectedSort.value !== "默认排序"
      }, selectedSort.value !== "默认排序" ? {
        J: common_vendor.t(selectedSort.value),
        K: common_vendor.o(resetSort)
      } : {}, {
        L: common_vendor.o(resetAllFilters)
      }) : {}, {
        M: showAreaFilter.value
      }, showAreaFilter.value ? {
        N: common_vendor.f(areaList.value, (area, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(area),
            b: area === selectedArea.value
          }, area === selectedArea.value ? {} : {}, {
            c: index,
            d: area === selectedArea.value ? 1 : "",
            e: common_vendor.o(($event) => selectArea(area), index)
          });
        }),
        O: filterDropdownTop.value + "px",
        P: areaFilterLeft.value + "px",
        Q: filterItemWidth.value + "px"
      } : {}, {
        R: showSubcategoryFilter.value
      }, showSubcategoryFilter.value ? {
        S: common_vendor.t(selectedCategory.value),
        T: common_vendor.f(subcategoryList.value, (subcat, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(subcat),
            b: subcat === selectedSubcategory.value
          }, subcat === selectedSubcategory.value ? {} : {}, {
            c: index,
            d: subcat === selectedSubcategory.value ? 1 : "",
            e: common_vendor.o(($event) => selectSubcategory(subcat), index)
          });
        }),
        U: subcategoryDropdownTop.value + "px"
      } : {}, {
        V: showSortFilter.value
      }, showSortFilter.value ? {
        W: common_vendor.f(sortList.value, (sort, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(sort),
            b: sort === selectedSort.value
          }, sort === selectedSort.value ? {} : {}, {
            c: index,
            d: sort === selectedSort.value ? 1 : "",
            e: common_vendor.o(($event) => selectSort(sort), index)
          });
        }),
        X: filterDropdownTop.value + "px",
        Y: sortFilterLeft.value + "px",
        Z: filterItemWidth.value + "px"
      } : {}, {
        aa: showAreaFilter.value || showSubcategoryFilter.value || showSortFilter.value
      }, showAreaFilter.value || showSubcategoryFilter.value || showSortFilter.value ? {
        ab: common_vendor.o(closeAllFilters)
      } : {}, {
        ac: serviceList.value.length > 0
      }, serviceList.value.length > 0 ? {
        ad: common_vendor.t(serviceList.value.length)
      } : {}, {
        ae: serviceList.value.length > 0
      }, serviceList.value.length > 0 ? common_vendor.e({
        af: common_vendor.f(serviceList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.category),
            b: item.subcategory
          }, item.subcategory ? {
            c: common_vendor.t(item.subcategory)
          } : {}, {
            d: common_vendor.t(item.area || "全城"),
            e: common_vendor.t(item.time),
            f: common_vendor.t(item.content),
            g: item.images && item.images.length > 0
          }, item.images && item.images.length > 0 ? common_vendor.e({
            h: common_vendor.f(item.images.slice(0, 3), (img, imgIndex, i1) => {
              return {
                a: imgIndex,
                b: img
              };
            }),
            i: item.images.length === 1 ? 1 : "",
            j: item.images.length > 3
          }, item.images.length > 3 ? {
            k: common_vendor.t(item.images.length - 3)
          } : {}) : {}, {
            l: common_vendor.t(item.views || 0),
            m: item.price
          }, item.price ? {
            n: common_vendor.t(item.price)
          } : {}, {
            o: index,
            p: common_vendor.o(($event) => navigateToDetail(item), index)
          });
        }),
        ag: hasMore.value
      }, hasMore.value ? {} : {}) : {
        ah: common_assets._imports_1$3,
        ai: common_vendor.o(resetAllFilters)
      }, {
        aj: common_vendor.o(loadMore),
        ak: refreshing.value,
        al: common_vendor.o(onRefresh),
        am: common_vendor.o(navigateToPublish)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-bd48d0a8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/service/pages/filter.js.map
