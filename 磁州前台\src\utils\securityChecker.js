/**
 * 安全检查工具
 */
import { PASSWORD_POLICY, SECURITY_CONSTANTS } from '@/config/securityConfig';
import { createSecurityLog, SECURITY_EVENTS, LOG_LEVELS } from './securityAudit';
import { secureStorage } from './securityUtils';

/**
 * 检查密码强度
 * @param {string} password 密码
 * @returns {Object} 检查结果
 */
export const checkPasswordStrength = (password) => {
  const result = {
    valid: true,
    errors: []
  };
  
  // 检查长度
  if (password.length < PASSWORD_POLICY.minLength) {
    result.valid = false;
    result.errors.push(`密码长度不能少于${PASSWORD_POLICY.minLength}个字符`);
  }
  
  // 检查大写字母
  if (PASSWORD_POLICY.requireUppercase && !/[A-Z]/.test(password)) {
    result.valid = false;
    result.errors.push('密码必须包含至少一个大写字母');
  }
  
  // 检查小写字母
  if (PASSWORD_POLICY.requireLowercase && !/[a-z]/.test(password)) {
    result.valid = false;
    result.errors.push('密码必须包含至少一个小写字母');
  }
  
  // 检查数字
  if (PASSWORD_POLICY.requireNumbers && !/[0-9]/.test(password)) {
    result.valid = false;
    result.errors.push('密码必须包含至少一个数字');
  }
  
  // 检查特殊字符
  if (PASSWORD_POLICY.requireSpecialChars && !/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
    result.valid = false;
    result.errors.push('密码必须包含至少一个特殊字符');
  }
  
  return result;
};

/**
 * 检查是否是常见弱密码
 * @param {string} password 密码
 * @returns {boolean} 是否是弱密码
 */
export const isCommonWeakPassword = (password) => {
  // 常见弱密码列表
  const weakPasswords = [
    '123456', 'password', '12345678', 'qwerty', 'admin',
    '111111', '123123', '000000', '666666', 'abc123'
  ];
  
  return weakPasswords.includes(password.toLowerCase());
};

/**
 * 检查是否需要重置密码
 * @returns {boolean} 是否需要重置密码
 */
export const checkPasswordExpiration = () => {
  const passwordLastChanged = secureStorage.get('passwordLastChanged');
  
  if (!passwordLastChanged) {
    return false;
  }
  
  const now = new Date().getTime();
  const lastChanged = new Date(passwordLastChanged).getTime();
  const daysDiff = Math.floor((now - lastChanged) / (1000 * 60 * 60 * 24));
  
  return daysDiff > PASSWORD_POLICY.maxAge;
};

/**
 * 检查会话状态
 * @returns {Object} 会话状态
 */
export const checkSessionStatus = () => {
  const lastActivity = secureStorage.get('lastActivityTime');
  const token = uni.getStorageSync(SECURITY_CONSTANTS.TOKEN_KEY);
  
  if (!token || !lastActivity) {
    return {
      valid: false,
      expired: true,
      remainingMinutes: 0
    };
  }
  
  const now = new Date().getTime();
  const lastActivityTime = new Date(lastActivity).getTime();
  const diffMinutes = Math.floor((now - lastActivityTime) / (1000 * 60));
  
  // 更新最后活动时间
  secureStorage.set('lastActivityTime', new Date().toISOString());
  
  return {
    valid: diffMinutes < SECURITY_CONSTANTS.SESSION_TIMEOUT,
    expired: diffMinutes >= SECURITY_CONSTANTS.SESSION_TIMEOUT,
    remainingMinutes: Math.max(0, SECURITY_CONSTANTS.SESSION_TIMEOUT - diffMinutes)
  };
};

/**
 * 检查内容安全
 * @param {string} content 内容
 * @returns {Object} 检查结果
 */
export const checkContentSecurity = (content) => {
  // 敏感词列表
  const sensitiveWords = [
    '赌博', '博彩', '色情', '暴力', '诈骗', '违法', '犯罪',
    '毒品', '枪支', '黄赌毒', '传销', '非法集资'
  ];
  
  const result = {
    safe: true,
    violations: []
  };
  
  sensitiveWords.forEach(word => {
    if (content.includes(word)) {
      result.safe = false;
      result.violations.push(`内容包含敏感词: ${word}`);
    }
  });
  
  return result;
};

/**
 * 检查推广内容安全
 * @param {Object} promotionData 推广数据
 * @returns {Object} 检查结果
 */
export const checkPromotionSecurity = (promotionData) => {
  const result = {
    safe: true,
    violations: []
  };
  
  // 检查内容类型是否允许推广
  if (!promotionData.contentType || !['merchant', 'product', 'carpool', 'house', 'service', 'community', 'activity', 'content'].includes(promotionData.contentType)) {
    result.safe = false;
    result.violations.push('不支持的内容类型');
  }
  
  // 检查内容安全
  if (promotionData.title) {
    const titleCheck = checkContentSecurity(promotionData.title);
    if (!titleCheck.safe) {
      result.safe = false;
      result.violations = result.violations.concat(titleCheck.violations);
    }
  }
  
  if (promotionData.description) {
    const descCheck = checkContentSecurity(promotionData.description);
    if (!descCheck.safe) {
      result.safe = false;
      result.violations = result.violations.concat(descCheck.violations);
    }
  }
  
  // 记录不安全的推广尝试
  if (!result.safe) {
    createSecurityLog(SECURITY_EVENTS.UNAUTHORIZED_ACCESS, {
      contentType: promotionData.contentType,
      contentId: promotionData.contentId,
      violations: result.violations,
      timestamp: new Date().toISOString()
    }, LOG_LEVELS.SECURITY);
  }
  
  return result;
};

export default {
  checkPasswordStrength,
  isCommonWeakPassword,
  checkPasswordExpiration,
  checkSessionStatus,
  checkContentSecurity,
  checkPromotionSecurity
}; 