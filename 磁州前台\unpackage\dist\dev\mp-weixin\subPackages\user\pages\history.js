"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "history",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const historyList = common_vendor.ref([]);
    const showClearConfirm = common_vendor.ref(false);
    const groupedHistory = common_vendor.computed(() => {
      const groups = {};
      historyList.value.forEach((item) => {
        const date = new Date(item.time).toISOString().slice(0, 10);
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(item);
      });
      return groups;
    });
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      loadHistoryData();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const loadHistoryData = () => {
      setTimeout(() => {
        historyList.value = [
          // 今天
          {
            id: "history_1",
            title: "专业家政保洁服务",
            desc: "专业保洁，上门服务，价格实惠",
            image: "/static/images/service1.jpg",
            type: "服务",
            time: (/* @__PURE__ */ new Date()).toISOString()
          },
          {
            id: "history_2",
            title: "黄金地段餐饮店整体转让",
            desc: "位于商业街黄金地段，客流稳定，接手即可盈利",
            image: "/static/images/shop1.jpg",
            type: "生意转让",
            time: (/* @__PURE__ */ new Date()).toISOString()
          },
          // 昨天
          {
            id: "history_3",
            title: "求职行政文员",
            desc: "有1年工作经验，熟悉办公软件，可立即上岗",
            image: "/static/images/avatar.png",
            type: "求职信息",
            time: new Date(Date.now() - 864e5).toISOString()
          },
          {
            id: "history_4",
            title: "餐饮店急招服务员",
            desc: "工作轻松，待遇优厚，提供食宿",
            image: "/static/images/service2.jpg",
            type: "招聘信息",
            time: new Date(Date.now() - 864e5).toISOString()
          },
          // 前天
          {
            id: "history_5",
            title: "市中心两室一厅出租",
            desc: "位于市中心，交通便利，家具家电齐全",
            image: "/static/images/house1.jpg",
            type: "房屋出租",
            time: new Date(Date.now() - 864e5 * 2).toISOString()
          },
          {
            id: "history_6",
            title: "二手苹果手机出售",
            desc: "iPhone 13 Pro，128G，成色新，无拆修",
            image: "/static/images/phone.jpg",
            type: "二手闲置",
            time: new Date(Date.now() - 864e5 * 2).toISOString()
          }
        ];
      }, 500);
    };
    const formatDate = (dateStr) => {
      const date = new Date(dateStr);
      const today = /* @__PURE__ */ new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      if (date.toDateString() === today.toDateString()) {
        return "今天";
      } else if (date.toDateString() === yesterday.toDateString()) {
        return "昨天";
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
    };
    const formatTime = (timeStr) => {
      const date = new Date(timeStr);
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      return `${hours}:${minutes}`;
    };
    const viewDetail = (item) => {
      let url = "";
      switch (item.type) {
        case "服务":
          url = `/pages/publish/home-service-detail?id=${item.id}`;
          break;
        case "生意转让":
          url = `/pages/publish/business-transfer-detail?id=${item.id}`;
          break;
        case "求职信息":
          url = `/pages/publish/job-seeking-detail?id=${item.id}`;
          break;
        case "招聘信息":
          url = `/pages/publish/job-detail?id=${item.id}`;
          break;
        case "房屋出租":
          url = `/pages/publish/house-rent-detail?id=${item.id}`;
          break;
        case "二手闲置":
          url = `/pages/publish/second-hand-detail?id=${item.id}`;
          break;
        default:
          url = `/pages/publish/info-detail?id=${item.id}`;
      }
      common_vendor.index.navigateTo({ url });
    };
    const clearHistory = () => {
      showClearConfirm.value = true;
    };
    const cancelClear = () => {
      showClearConfirm.value = false;
    };
    const confirmClear = () => {
      historyList.value = [];
      showClearConfirm.value = false;
      common_vendor.index.showToast({
        title: "已清空浏览记录",
        icon: "success"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: common_vendor.o(clearHistory),
        d: statusBarHeight.value + "px",
        e: historyList.value.length > 0
      }, historyList.value.length > 0 ? {
        f: common_vendor.f(groupedHistory.value, (group, date, i0) => {
          return {
            a: common_vendor.t(formatDate(date)),
            b: common_vendor.f(group, (item, index, i1) => {
              return {
                a: item.image,
                b: common_vendor.t(item.title),
                c: common_vendor.t(item.desc),
                d: common_vendor.t(item.type),
                e: common_vendor.t(formatTime(item.time)),
                f: index,
                g: common_vendor.o(($event) => viewDetail(item), index)
              };
            }),
            c: date
          };
        })
      } : {
        g: common_assets._imports_1$3
      }, {
        h: navbarHeight.value + "px",
        i: showClearConfirm.value
      }, showClearConfirm.value ? {
        j: common_vendor.o(cancelClear),
        k: common_vendor.o(confirmClear),
        l: common_vendor.o(() => {
        }),
        m: common_vendor.o(cancelClear)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/user/pages/history.js.map
