{"version": 3, "file": "management.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/coupon/management.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xjb3Vwb25cbWFuYWdlbWVudC52dWU"], "sourcesContent": ["<template>\n  <view class=\"coupon-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @tap=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">优惠券管理</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\">?</view>\n      </view>\n    </view>\n    \n    <!-- 顶部操作区 -->\n    <view class=\"top-actions\">\n      <CreateButton text=\"创建优惠券\" theme=\"coupon\" @click=\"createNewCoupon\" />\n    </view>\n    \n    <!-- 数据概览 -->\n    <view class=\"overview-section\">\n      <view class=\"overview-cards\">\n        <view class=\"overview-card\">\n          <text class=\"card-value\">{{statistics.total}}</text>\n          <text class=\"card-label\">优惠券总数</text>\n        </view>\n        <view class=\"overview-card\">\n          <text class=\"card-value\">{{statistics.active}}</text>\n          <text class=\"card-label\">进行中</text>\n        </view>\n        <view class=\"overview-card\">\n          <text class=\"card-value\">{{statistics.used}}</text>\n          <text class=\"card-label\">已使用</text>\n        </view>\n        <view class=\"overview-card\">\n          <text class=\"card-value\">{{statistics.conversion}}%</text>\n          <text class=\"card-label\">转化率</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 操作栏 -->\n    <view class=\"action-bar\">\n      <view class=\"search-box\">\n        <view class=\"search-icon\">\n          <view class=\"icon-svg\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\n              <line x1=\"21\" y1=\"21\" x2=\"16.65\" y2=\"16.65\"></line>\n            </svg>\n          </view>\n        </view>\n        <input type=\"text\" class=\"search-input\" placeholder=\"搜索优惠券\" v-model=\"searchKeyword\" @input=\"onSearch\" />\n      </view>\n      \n      <view class=\"filter-btn\" @tap=\"showFilterOptions\">\n        <view class=\"btn-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <polygon points=\"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3\"></polygon>\n          </svg>\n        </view>\n        <text class=\"btn-text\">筛选</text>\n      </view>\n      \n      <view class=\"sort-btn\" @tap=\"showSortOptions\">\n        <view class=\"btn-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <line x1=\"4\" y1=\"9\" x2=\"20\" y2=\"9\"></line>\n            <line x1=\"4\" y1=\"15\" x2=\"20\" y2=\"15\"></line>\n            <line x1=\"10\" y1=\"3\" x2=\"8\" y2=\"21\"></line>\n            <line x1=\"16\" y1=\"3\" x2=\"14\" y2=\"21\"></line>\n          </svg>\n        </view>\n        <text class=\"btn-text\">排序</text>\n      </view>\n    </view>\n    \n    <!-- 优惠券列表 -->\n    <view class=\"coupon-list\">\n      <view \n        class=\"coupon-card\" \n        v-for=\"(item, index) in displayCoupons\" \n        :key=\"index\"\n        @tap=\"viewCouponDetail(item)\"\n        :class=\"{'hover': hoveredCoupon === item.id}\"\n        @mouseenter=\"setHoveredCoupon(item.id)\"\n        @mouseleave=\"clearHoveredCoupon()\">\n        <view class=\"coupon-header\">\n          <text class=\"coupon-title\">{{item.title}}</text>\n          <view class=\"coupon-status\" :class=\"'status-'+item.status\">{{item.statusText}}</view>\n        </view>\n        \n        <view class=\"coupon-value\">\n          <text class=\"discount-symbol\">¥</text>\n          <text class=\"discount-amount\">{{item.value}}</text>\n          <text class=\"discount-condition\">满{{item.minSpend}}元可用</text>\n        </view>\n        \n        <view class=\"coupon-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">有效期至:</text>\n            <text class=\"info-value\">{{item.expireDate}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">使用次数:</text>\n            <text class=\"info-value\">{{item.usedCount}}/{{item.totalCount}}</text>\n          </view>\n        </view>\n        \n        <view class=\"coupon-actions\">\n          <view class=\"action-btn edit\" @tap.stop=\"editCoupon(item)\">\n            <view class=\"btn-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <path d=\"M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z\"></path>\n              </svg>\n            </view>\n            <text class=\"btn-text\">编辑</text>\n          </view>\n          \n          <view \n            class=\"action-btn\" \n            :class=\"item.status === 'active' ? 'pause' : 'activate'\"\n            @tap.stop=\"toggleCouponStatus(item)\">\n            <view class=\"btn-icon\">\n              <svg v-if=\"item.status === 'active'\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <rect x=\"6\" y=\"4\" width=\"4\" height=\"16\"></rect>\n                <rect x=\"14\" y=\"4\" width=\"4\" height=\"16\"></rect>\n              </svg>\n              <svg v-else xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <polygon points=\"5 3 19 12 5 21 5 3\"></polygon>\n              </svg>\n            </view>\n            <text class=\"btn-text\">{{item.status === 'active' ? '暂停' : '启用'}}</text>\n          </view>\n          \n          <view class=\"action-btn delete\" @tap.stop=\"confirmDeleteCoupon(item)\">\n            <view class=\"btn-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <polyline points=\"3 6 5 6 21 6\"></polyline>\n                <path d=\"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2\"></path>\n                <line x1=\"10\" y1=\"11\" x2=\"10\" y2=\"17\"></line>\n                <line x1=\"14\" y1=\"11\" x2=\"14\" y2=\"17\"></line>\n              </svg>\n            </view>\n            <text class=\"btn-text\">删除</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"displayCoupons.length === 0\">\n        <view class=\"empty-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" ry=\"2\"></rect>\n            <path d=\"M12 8v8\"></path>\n            <path d=\"M8 12h8\"></path>\n          </svg>\n        </view>\n        <text class=\"empty-text\">暂无优惠券</text>\n        <text class=\"empty-subtext\">点击下方按钮创建新的优惠券</text>\n      </view>\n    </view>\n    \n    <!-- 创建优惠券按钮 -->\n    <!-- 悬浮按钮已移除，改用顶部CreateButton组件 -->\n  </view>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport CreateButton from '/subPackages/merchant-admin-marketing/components/CreateButton.vue';\n\nexport default {\n  components: {\n    CreateButton\n  },\n  setup() {\n    // 响应式状态\n    const statistics = reactive({\n      total: 35,\n      active: 12,\n      used: 258,\n      conversion: 46.8\n    });\n    \n    const coupons = ref([\n      {\n        id: 1,\n        title: '新客专享优惠',\n        status: 'active',\n        statusText: '进行中',\n        value: 10,\n        minSpend: 100,\n        expireDate: '2023-05-15',\n        usedCount: 234,\n        totalCount: 500,\n        conversionRate: 46.8\n      },\n      {\n        id: 2,\n        title: '满减优惠券',\n        status: 'active',\n        statusText: '进行中',\n        value: 20,\n        minSpend: 200,\n        expireDate: '2023-05-20',\n        usedCount: 156,\n        totalCount: 300,\n        conversionRate: 52.0\n      },\n      {\n        id: 3,\n        title: '节日特别券',\n        status: 'upcoming',\n        statusText: '未开始',\n        value: 50,\n        minSpend: 300,\n        expireDate: '2023-06-10',\n        usedCount: 0,\n        totalCount: 200,\n        conversionRate: 0\n      },\n      {\n        id: 4,\n        title: '会员专享券',\n        status: 'active',\n        statusText: '进行中',\n        value: 15,\n        minSpend: 150,\n        expireDate: '2023-05-25',\n        usedCount: 89,\n        totalCount: 200,\n        conversionRate: 44.5\n      },\n      {\n        id: 5,\n        title: '周末特惠券',\n        status: 'expired',\n        statusText: '已结束',\n        value: 30,\n        minSpend: 250,\n        expireDate: '2023-04-30',\n        usedCount: 178,\n        totalCount: 300,\n        conversionRate: 59.3\n      }\n    ]);\n    \n    const searchKeyword = ref('');\n    const hoveredCoupon = ref(null);\n    \n    // 计算属性\n    const displayCoupons = computed(() => {\n      if (!searchKeyword.value) {\n        return coupons.value;\n      }\n      \n      const keyword = searchKeyword.value.toLowerCase();\n      return coupons.value.filter(coupon => {\n        return coupon.title.toLowerCase().includes(keyword);\n      });\n    });\n    \n    // 方法\n    function goBack() {\n      uni.navigateBack();\n    }\n    \n    function onSearch(e) {\n      // 实时搜索处理\n    }\n    \n    function showFilterOptions() {\n      uni.showActionSheet({\n        itemList: ['全部', '进行中', '未开始', '已结束'],\n        success: (res) => {\n          // 处理筛选结果\n        }\n      });\n    }\n    \n    function showSortOptions() {\n      uni.showActionSheet({\n        itemList: ['默认排序', '面额从高到低', '面额从低到高', '使用率从高到低', '使用率从低到高'],\n        success: (res) => {\n          // 处理排序结果\n        }\n      });\n    }\n    \n    function viewCouponDetail(coupon) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon/detail?id=${coupon.id}`,\n        animationType: 'slide-in-right'\n      });\n    }\n    \n    function editCoupon(coupon) {\n      uni.navigateTo({\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon/edit?id=${coupon.id}`,\n        animationType: 'slide-in-right'\n      });\n    }\n    \n    function toggleCouponStatus(coupon) {\n      const isActive = coupon.status === 'active';\n      const newStatus = isActive ? 'paused' : 'active';\n      const statusText = isActive ? '已暂停' : '进行中';\n      \n      // 在实际应用中，这里应该调用API更新状态\n      \n      // 本地状态更新示例\n      coupon.status = newStatus;\n      coupon.statusText = statusText;\n      \n      uni.showToast({\n        title: isActive ? '已暂停优惠券' : '已启用优惠券',\n        icon: 'success'\n      });\n    }\n    \n    function confirmDeleteCoupon(coupon) {\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除\"${coupon.title}\"吗？此操作无法撤销。`,\n        confirmColor: '#FF3B30',\n        success: (res) => {\n          if (res.confirm) {\n            deleteCoupon(coupon);\n          }\n        }\n      });\n    }\n    \n    function deleteCoupon(coupon) {\n      // 在实际应用中，这里应该调用API删除\n      \n      // 本地状态更新示例\n      const index = coupons.value.findIndex(item => item.id === coupon.id);\n      if (index > -1) {\n        coupons.value.splice(index, 1);\n      }\n      \n      uni.showToast({\n        title: '已删除优惠券',\n        icon: 'success'\n      });\n    }\n    \n    function createNewCoupon() {\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/coupon/create',\n        animationType: 'slide-in-right'\n      });\n    }\n    \n    function setHoveredCoupon(couponId) {\n      hoveredCoupon.value = couponId;\n    }\n    \n    function clearHoveredCoupon() {\n      hoveredCoupon.value = null;\n    }\n    \n    onMounted(() => {\n      // 可以在这里加载数据\n    });\n    \n    return {\n      statistics,\n      displayCoupons,\n      searchKeyword,\n      hoveredCoupon,\n      goBack,\n      onSearch,\n      showFilterOptions,\n      showSortOptions,\n      viewCouponDetail,\n      editCoupon,\n      toggleCouponStatus,\n      confirmDeleteCoupon,\n      createNewCoupon,\n      setHoveredCoupon,\n      clearHoveredCoupon\n    };\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.coupon-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF9966, #FF5E62);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 概览部分样式 */\n.overview-section {\n  margin: 15px;\n  background: #fff;\n  border-radius: 12px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  padding: 15px;\n}\n\n.overview-cards {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -5px;\n}\n\n.overview-card {\n  width: 25%;\n  padding: 0 5px;\n  box-sizing: border-box;\n}\n\n.card-value {\n  display: block;\n  font-size: 18px;\n  font-weight: bold;\n  color: #FF5E62;\n  text-align: center;\n  margin-bottom: 5px;\n}\n\n.card-label {\n  display: block;\n  font-size: 12px;\n  color: #666;\n  text-align: center;\n}\n\n/* 操作栏样式 */\n.action-bar {\n  margin: 0 15px 15px;\n  display: flex;\n  align-items: center;\n}\n\n.search-box {\n  flex: 1;\n  height: 36px;\n  background: #fff;\n  border-radius: 18px;\n  display: flex;\n  align-items: center;\n  padding: 0 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.search-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n  color: #999;\n}\n\n.icon-svg {\n  width: 16px;\n  height: 16px;\n}\n\n.search-input {\n  flex: 1;\n  height: 36px;\n  font-size: 14px;\n  color: #333;\n  border: none;\n  background: transparent;\n}\n\n.filter-btn, .sort-btn {\n  height: 36px;\n  padding: 0 12px;\n  background: #fff;\n  border-radius: 18px;\n  margin-left: 10px;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.btn-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 5px;\n  color: #666;\n}\n\n.btn-text {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 优惠券列表样式 */\n.coupon-list {\n  padding: 0 15px;\n  margin-bottom: 80px; /* 为悬浮按钮留出空间 */\n}\n\n.coupon-card {\n  background: #fff;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  position: relative;\n  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);\n}\n\n.coupon-card.hover {\n  transform: translateY(-3px);\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n}\n\n.coupon-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.coupon-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.coupon-status {\n  padding: 3px 8px;\n  border-radius: 10px;\n  font-size: 12px;\n  color: white;\n}\n\n.status-active {\n  background: #34C759;\n}\n\n.status-expired {\n  background: #8E8E93;\n}\n\n.status-upcoming {\n  background: #FF9500;\n}\n\n.status-paused {\n  background: #FF9500;\n}\n\n.coupon-value {\n  display: flex;\n  align-items: baseline;\n  margin-bottom: 12px;\n}\n\n.discount-symbol {\n  font-size: 16px;\n  color: #FF5E62;\n  margin-right: 2px;\n}\n\n.discount-amount {\n  font-size: 24px;\n  font-weight: bold;\n  color: #FF5E62;\n  margin-right: 8px;\n}\n\n.discount-condition {\n  font-size: 12px;\n  color: #666;\n}\n\n.coupon-info {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 15px;\n}\n\n.info-item {\n  width: 50%;\n  margin-bottom: 5px;\n  display: flex;\n}\n\n.info-label {\n  font-size: 12px;\n  color: #999;\n  margin-right: 5px;\n}\n\n.info-value {\n  font-size: 12px;\n  color: #333;\n  font-weight: 500;\n}\n\n.coupon-actions {\n  display: flex;\n  border-top: 1px solid #f0f0f0;\n  padding-top: 12px;\n}\n\n.action-btn {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 5px 0;\n}\n\n.action-btn .btn-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 0;\n  margin-bottom: 3px;\n}\n\n.action-btn .btn-text {\n  font-size: 12px;\n}\n\n.action-btn.edit {\n  color: #007AFF;\n}\n\n.action-btn.pause {\n  color: #FF9500;\n}\n\n.action-btn.activate {\n  color: #34C759;\n}\n\n.action-btn.delete {\n  color: #FF3B30;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 0;\n}\n\n.empty-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 30px;\n  background: rgba(255, 94, 98, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 15px;\n  color: #FF5E62;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #333;\n  margin-bottom: 8px;\n  font-weight: 500;\n}\n\n.empty-subtext {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 顶部操作区样式 */\n.top-actions {\n  padding: 15px;\n  display: flex;\n  justify-content: flex-end;\n  background-color: #fff;\n}\n\n/* 悬浮操作按钮样式已移除，使用CreateButton组件 */\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/coupon/management.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "uni", "onMounted"], "mappings": ";;AAyKA,MAAO,eAAc,MAAW;AAEhC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,QAAQ;AAEN,UAAM,aAAaA,cAAAA,SAAS;AAAA,MAC1B,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAED,UAAM,UAAUC,cAAAA,IAAI;AAAA,MAClB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,gBAAgB;AAAA,MACjB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAED,UAAM,gBAAgBA,kBAAI,EAAE;AAC5B,UAAM,gBAAgBA,kBAAI,IAAI;AAG9B,UAAM,iBAAiBC,cAAAA,SAAS,MAAM;AACpC,UAAI,CAAC,cAAc,OAAO;AACxB,eAAO,QAAQ;AAAA,MACjB;AAEA,YAAM,UAAU,cAAc,MAAM,YAAW;AAC/C,aAAO,QAAQ,MAAM,OAAO,YAAU;AACpC,eAAO,OAAO,MAAM,YAAa,EAAC,SAAS,OAAO;AAAA,MACpD,CAAC;AAAA,IACH,CAAC;AAGD,aAAS,SAAS;AAChBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,aAAS,SAAS,GAAG;AAAA,IAErB;AAEA,aAAS,oBAAoB;AAC3BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,MAAM,OAAO,OAAO,KAAK;AAAA,QACpC,SAAS,CAAC,QAAQ;AAAA,QAElB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,kBAAkB;AACzBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,UAAU,UAAU,WAAW,SAAS;AAAA,QAC3D,SAAS,CAAC,QAAQ;AAAA,QAElB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,QAAQ;AAChCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0EAA0E,OAAO,EAAE;AAAA,QACxF,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,aAAS,WAAW,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wEAAwE,OAAO,EAAE;AAAA,QACtF,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,aAAS,mBAAmB,QAAQ;AAClC,YAAM,WAAW,OAAO,WAAW;AACnC,YAAM,YAAY,WAAW,WAAW;AACxC,YAAM,aAAa,WAAW,QAAQ;AAKtC,aAAO,SAAS;AAChB,aAAO,aAAa;AAEpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,WAAW,WAAW;AAAA,QAC7B,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,aAAS,oBAAoB,QAAQ;AACnCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,OAAO,KAAK;AAAA,QAC9B,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,yBAAa,MAAM;AAAA,UACrB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,aAAa,QAAQ;AAI5B,YAAM,QAAQ,QAAQ,MAAM,UAAU,UAAQ,KAAK,OAAO,OAAO,EAAE;AACnE,UAAI,QAAQ,IAAI;AACd,gBAAQ,MAAM,OAAO,OAAO,CAAC;AAAA,MAC/B;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,aAAS,kBAAkB;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,UAAU;AAClC,oBAAc,QAAQ;AAAA,IACxB;AAEA,aAAS,qBAAqB;AAC5B,oBAAc,QAAQ;AAAA,IACxB;AAEAC,kBAAAA,UAAU,MAAM;AAAA,IAEhB,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChYA,GAAG,WAAW,eAAe;"}