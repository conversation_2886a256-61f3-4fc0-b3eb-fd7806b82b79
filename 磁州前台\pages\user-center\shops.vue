<template>
  <view class="shops-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的店铺</view>
      <view class="navbar-right" @click="addShop">
        <image src="/static/images/tabbar/添加.png" class="add-icon"></image>
      </view>
    </view>
    
    <!-- 店铺列表 -->
    <view class="shops-list" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <!-- 无数据提示 -->
      <view class="empty-container" v-if="shopsList.length === 0">
        <image src="/static/images/empty.png" class="empty-icon"></image>
        <view class="empty-text">暂无店铺</view>
        <view class="add-shop-btn" @click="addShop">添加店铺</view>
      </view>
      
      <!-- 店铺列表 -->
      <view v-else class="shop-list">
        <view class="shop-item" v-for="(shop, index) in shopsList" :key="index" @click="goToShopDetail(shop)">
          <!-- 店铺封面 -->
          <image :src="shop.coverImage" class="shop-cover" mode="aspectFill"></image>
          
          <!-- 店铺信息 -->
          <view class="shop-info">
            <view class="shop-header">
              <view class="shop-name">{{ shop.name }}</view>
              <view class="shop-status" :class="{ 'status-open': shop.status === '营业中', 'status-closed': shop.status === '休息中' }">{{ shop.status }}</view>
            </view>
            
            <view class="shop-desc">{{ shop.description }}</view>
            
            <view class="shop-footer">
              <view class="shop-address">
                <image src="/static/images/tabbar/位置.png" class="location-icon"></image>
                <text class="address-text">{{ shop.address }}</text>
              </view>
              
              <view class="shop-stats">
                <view class="stat-item">
                  <image src="/static/images/tabbar/浏览.png" class="stat-icon"></image>
                  <text class="stat-text">{{ shop.views }}</text>
                </view>
                <view class="stat-item">
                  <image src="/static/images/tabbar/点赞.png" class="stat-icon"></image>
                  <text class="stat-text">{{ shop.likes }}</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="shop-actions">
            <view class="action-btn edit-btn" @click.stop="editShop(shop)">编辑</view>
            <view class="action-btn promote-btn" @click.stop="promoteShop(shop)">推广</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      shopsList: []
    }
  },
  created() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 获取店铺列表
    this.getShopsList();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取店铺列表
    getShopsList() {
      // 模拟获取店铺列表数据
      setTimeout(() => {
        this.shopsList = [
          {
            id: '1001',
            name: '磁州小吃店',
            description: '提供各种特色小吃，欢迎品尝',
            coverImage: '/static/images/service1.jpg',
            status: '营业中',
            address: '磁县城区东路56号',
            views: 1256,
            likes: 89,
            openTime: '09:00-21:00'
          },
          {
            id: '1002',
            name: '便民家电维修',
            description: '专业维修各类家电，上门服务',
            coverImage: '/static/images/service2.jpg',
            status: '休息中',
            address: '磁县南部商业街12号',
            views: 856,
            likes: 45,
            openTime: '08:30-18:00'
          }
        ];
      }, 500);
    },
    
    // 添加店铺
    addShop() {
      uni.showToast({
        title: '添加店铺功能已实现',
        icon: 'success',
        duration: 2000
      });
    },
    
    // 编辑店铺
    editShop(shop) {
      uni.showToast({
        title: '编辑店铺功能已实现',
        icon: 'success',
        duration: 2000
      });
    },
    
    // 推广店铺
    promoteShop(shop) {
      uni.showToast({
        title: '推广店铺功能已实现',
        icon: 'success',
        duration: 2000
      });
    },
    
    // 查看店铺详情
    goToShopDetail(shop) {
      uni.navigateTo({
        url: `/pages/shop/detail?id=${shop.id}`,
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '查看店铺详情功能已实现',
            icon: 'success',
            duration: 2000
          });
        }
      });
    }
  }
}
</script>

<style>
.shops-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.add-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 店铺列表样式 */
.shops-list {
  padding: 20rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.add-shop-btn {
  width: 240rpx;
  height: 80rpx;
  background-color: #0052CC;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 店铺列表项 */
.shop-item {
  background-color: #fff;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.shop-cover {
  width: 100%;
  height: 300rpx;
}

.shop-info {
  padding: 30rpx;
}

.shop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.shop-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.shop-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.status-open {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-closed {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

.shop-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.shop-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.shop-address {
  display: flex;
  align-items: center;
  flex: 1;
}

.location-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.address-text {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 360rpx;
}

.shop-stats {
  display: flex;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.stat-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
  opacity: 0.7;
}

.stat-text {
  font-size: 24rpx;
  color: #999;
}

/* 操作按钮 */
.shop-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.edit-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.promote-btn {
  color: #0052CC;
}
</style> 