"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      redirected: false
    };
  },
  onLoad() {
    common_vendor.index.showLoading({
      title: "加载中...",
      mask: true
    });
    setTimeout(() => {
      this.navigateToCarpoolMain();
    }, 500);
  },
  onShow() {
    if (this.redirected) {
      this.navigateToCarpoolMain();
    }
  },
  methods: {
    navigateToCarpoolMain() {
      common_vendor.index.hideTabBar();
      this.redirected = true;
      common_vendor.index.hideLoading();
      common_vendor.index.navigateTo({
        url: "/carpool-package/pages/carpool-main/index",
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/carpool-entry/index.vue:50", "跳转失败", err);
          common_vendor.index.showTabBar();
          common_vendor.index.showToast({
            title: "拼车服务加载失败，请重试",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$8
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/carpool-entry/index.js.map
