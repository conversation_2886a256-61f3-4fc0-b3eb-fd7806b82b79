"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "ServiceGrid",
  setup(__props) {
    const categoryPage = common_vendor.ref(0);
    const showSubcategory = common_vendor.ref(false);
    const serviceCategory = common_vendor.ref([
      { name: "到家服务", icon: "/static/images/tabbar/到家服务.png", url: "/subPackages/service/pages/home-service-list" },
      { name: "寻找服务", icon: "/static/images/tabbar/寻找服务.png", url: "/subPackages/service/pages/list?type=find" },
      { name: "生意转让", icon: "/static/images/tabbar/生意转让.png", url: "/subPackages/service/pages/list?type=business" },
      { name: "招聘信息", icon: "/static/images/tabbar/招聘信息.png", url: "/subPackages/service/pages/list?type=job" },
      { name: "求职信息", icon: "/static/images/tabbar/求职信息.png", url: "/subPackages/service/pages/list?type=resume" },
      { name: "房屋出租", icon: "/static/images/tabbar/出租.png", url: "/subPackages/service/pages/list?type=house_rent" },
      { name: "房屋出售", icon: "/static/images/tabbar/出售.png", url: "/subPackages/service/pages/list?type=house_sell" },
      { name: "二手车辆", icon: "/static/images/tabbar/二手车辆.png", url: "/subPackages/service/pages/list?type=second_car" },
      { name: "宠物信息", icon: "/static/images/tabbar/宠物信息.png", url: "/subPackages/service/pages/list?type=pet" },
      { name: "商家活动", icon: "/static/images/tabbar/商家活动.png", url: "/subPackages/service/pages/list?type=merchant_activity" },
      { name: "婚恋交友", icon: "/static/images/tabbar/婚恋交友.png", url: "/subPackages/service/pages/list?type=dating" },
      { name: "车辆服务", icon: "/static/images/tabbar/车辆服务.png", url: "/subPackages/service/pages/list?type=car" },
      { name: "二手闲置", icon: "/static/images/tabbar/二手闲置.png", url: "/subPackages/service/pages/list?type=second_hand" },
      { name: "磁州拼车", icon: "/static/images/tabbar/磁州拼车.png", url: "/subPackages/service/pages/list?type=carpool" },
      { name: "教育培训", icon: "/static/images/tabbar/教育培训.png", url: "/subPackages/service/pages/list?type=education" },
      { name: "其他服务", icon: "/static/images/tabbar/其他服务.png", url: "/subPackages/service/pages/list?type=other" }
    ]);
    const homeServiceCategories = common_vendor.ref([
      { name: "家政服务", icon: "/static/images/tabbar/123/家政服务.png", url: "/subPackages/service/pages/list?type=home&subType=home_cleaning&name=家政服务" },
      { name: "维修改造", icon: "/static/images/tabbar/123/维修改造.png", url: "/subPackages/service/pages/list?type=home&subType=repair&name=维修改造" },
      { name: "上门安装", icon: "/static/images/tabbar/123/上门安装.png", url: "/subPackages/service/pages/list?type=home&subType=installation&name=上门安装" },
      { name: "开锁换锁", icon: "/static/images/tabbar/123/开锁换锁.png", url: "/subPackages/service/pages/list?type=home&subType=locksmith&name=开锁换锁" },
      { name: "搬家拉货", icon: "/static/images/tabbar/123/搬家拉货.png", url: "/subPackages/service/pages/list?type=home&subType=moving&name=搬家拉货" },
      { name: "上门美容", icon: "/static/images/tabbar/123/上门美容.png", url: "/subPackages/service/pages/list?type=home&subType=beauty&name=上门美容" },
      { name: "上门家教", icon: "/static/images/tabbar/123/上门家教.png", url: "/subPackages/service/pages/list?type=home&subType=tutor&name=上门家教" },
      { name: "宠物服务", icon: "/static/images/tabbar/123/宠物服务.png", url: "/subPackages/service/pages/list?type=home&subType=pet_service&name=宠物服务" },
      { name: "上门疏通", icon: "/static/images/tabbar/123/上门疏通.png", url: "/subPackages/service/pages/list?type=home&subType=plumbing&name=上门疏通" },
      { name: "其他类型", icon: "/static/images/tabbar/123/其他类型.png", url: "/subPackages/service/pages/list?type=home&subType=other&name=其他类型" }
    ]);
    function changeCategoryPage(page) {
      categoryPage.value = page;
    }
    function onCategoryPageChange(e) {
      categoryPage.value = e.detail.current;
    }
    function handleCategoryClick(item, index) {
      if (index === 0 && item.name === "到家服务") {
        showSubcategory.value = true;
        common_vendor.index.vibrateShort();
      } else {
        const typeMatch = item.url.match(/type=([^&]+)/);
        const serviceType = typeMatch ? typeMatch[1] : "";
        if (serviceType) {
          common_vendor.index.navigateTo({
            url: `/subPackages/service/pages/list?type=${serviceType}&title=${encodeURIComponent(item.name)}`,
            success: () => {
              common_vendor.index.__f__("log", "at components/index/ServiceGrid.vue:128", "成功跳转到服务列表页面:", item.name);
              common_vendor.index.vibrateShort();
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at components/index/ServiceGrid.vue:132", "跳转到服务列表页面失败:", err);
              navigateTo(item.url);
            }
          });
        } else {
          navigateTo(item.url);
        }
      }
    }
    function closeSubcategory() {
      showSubcategory.value = false;
    }
    function navigateTo(url) {
      if (!url)
        return;
      try {
        common_vendor.index.navigateTo({
          url,
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/index/ServiceGrid.vue:153", "页面跳转失败:", err);
            common_vendor.index.switchTab({
              url,
              fail: (err2) => {
                common_vendor.index.__f__("error", "at components/index/ServiceGrid.vue:157", "switchTab跳转也失败:", err2);
                common_vendor.index.showToast({
                  title: "页面跳转失败",
                  icon: "none"
                });
              }
            });
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at components/index/ServiceGrid.vue:167", "导航出错:", error);
      }
    }
    function handleServiceItemClick(item) {
      common_vendor.index.__f__("log", "at components/index/ServiceGrid.vue:172", "点击了到家服务子分类:", item.name);
      closeSubcategory();
      common_vendor.index.vibrateShort();
      const subTypeMatch = item.url.match(/subType=([^&]+)/);
      const nameMatch = item.url.match(/name=([^&]+)/);
      const subType = subTypeMatch ? subTypeMatch[1] : "";
      const name = nameMatch ? decodeURIComponent(nameMatch[1]) : item.name;
      try {
        common_vendor.index.navigateTo({
          url: `/subPackages/service/pages/list?type=home&subType=${subType}&name=${encodeURIComponent(name)}`,
          success: () => {
            common_vendor.index.__f__("log", "at components/index/ServiceGrid.vue:186", "成功跳转到到家服务子分类:", name);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/index/ServiceGrid.vue:189", "跳转到到家服务子分类失败:", err);
            common_vendor.index.navigateTo({
              url: "/subPackages/service/pages/list",
              fail: (err2) => {
                common_vendor.index.__f__("error", "at components/index/ServiceGrid.vue:193", "备用跳转也失败:", err2);
                common_vendor.index.showToast({
                  title: "页面跳转失败",
                  icon: "none"
                });
              }
            });
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at components/index/ServiceGrid.vue:203", "到家服务子分类跳转出错:", error);
        common_vendor.index.navigateTo({
          url: "/subPackages/service/pages/list"
        });
      }
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: categoryPage.value === 0 ? 1 : "",
        b: common_vendor.o(($event) => changeCategoryPage(0)),
        c: categoryPage.value === 1 ? 1 : "",
        d: common_vendor.o(($event) => changeCategoryPage(1)),
        e: common_vendor.f(serviceCategory.value.slice(0, 10), (item, index, i0) => {
          return {
            a: item.icon,
            b: common_vendor.t(item.name),
            c: item.name,
            d: common_vendor.o(($event) => handleCategoryClick(item, index), item.name)
          };
        }),
        f: common_vendor.f(serviceCategory.value.slice(10), (item, index, i0) => {
          return {
            a: item.icon,
            b: common_vendor.t(item.name),
            c: item.name,
            d: common_vendor.o(($event) => handleCategoryClick(item, index + 10), item.name)
          };
        }),
        g: categoryPage.value,
        h: common_vendor.o(onCategoryPageChange),
        i: showSubcategory.value
      }, showSubcategory.value ? {
        j: common_vendor.o(closeSubcategory),
        k: common_vendor.f(homeServiceCategories.value, (item, index, i0) => {
          return {
            a: item.icon,
            b: common_vendor.t(item.name),
            c: index,
            d: common_vendor.o(($event) => handleServiceItemClick(item), index)
          };
        }),
        l: common_vendor.o(($event) => navigateTo("/subPackages/service/pages/list?type=home")),
        m: common_vendor.o(() => {
        }),
        n: common_vendor.o(closeSubcategory)
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f47550b4"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/index/ServiceGrid.js.map
