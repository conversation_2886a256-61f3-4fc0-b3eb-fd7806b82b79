{"version": 3, "file": "filter.js", "sources": ["subPackages/service/pages/filter.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcc2VydmljZVxwYWdlc1xmaWx0ZXIudnVl"], "sourcesContent": ["<template>\n  <view class=\"service-filter-container premium-style\">\n    <!-- 高级自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"back-btn\" @click=\"navigateBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <view class=\"navbar-title\">{{serviceTitle}}</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 高级搜索框 -->\n    <view class=\"search-container\" v-if=\"showSearchBox\">\n      <view class=\"search-box\">\n        <image class=\"search-icon\" src=\"/static/images/tabbar/放大镜.png\"></image>\n        <input class=\"search-input\" type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索职位、公司或关键词\" confirm-type=\"search\" @confirm=\"applyKeywordSearch\" />\n        <view class=\"search-btn\" @click=\"applyKeywordSearch\">搜索</view>\n      </view>\n    </view>\n    \n    <!-- 高级一级分类标签栏 -->\n    <scroll-view class=\"top-category-tabs\" scroll-x :show-scrollbar=\"false\" enhanced :bounces=\"true\">\n      <view \n        class=\"top-category-item\" \n        v-for=\"(item, index) in filteredTopCategories\" \n        :key=\"index\"\n        :class=\"{'active-top-category': currentTopCategory === item.type}\"\n        @click=\"switchTopCategory(item)\">\n        {{item.name}}\n      </view>\n    </scroll-view>\n    \n    <!-- 高级子分类标签栏 -->\n    <scroll-view class=\"subcategory-tabs\" scroll-x :show-scrollbar=\"false\" v-if=\"categoryList.length > 0\" enhanced :bounces=\"true\">\n      <view \n        class=\"subcategory-item\" \n        v-for=\"(category, index) in categoryList\" \n        :key=\"index\"\n        :class=\"{'active-subcategory': selectedCategory === category}\"\n        @click=\"selectCategory(category)\">\n        {{category}}\n      </view>\n    </scroll-view>\n    \n    <!-- 高级筛选条件栏 -->\n    <view class=\"filter-section\" ref=\"filterSection\">\n      <!-- 区域筛选 -->\n      <view class=\"filter-item\" @click=\"toggleAreaFilter\" ref=\"areaFilterItem\">\n        <text class=\"filter-text\" :class=\"{ 'active-filter': selectedArea !== '全部区域' }\">\n          {{selectedArea}}\n        </text>\n        <view class=\"filter-arrow\" :class=\"{ 'arrow-up': showAreaFilter }\"></view>\n      </view>\n      \n      <!-- 排序筛选 -->\n      <view class=\"filter-item\" @click=\"toggleSortFilter\" ref=\"sortFilterItem\">\n        <text class=\"filter-text\" :class=\"{ 'active-filter': selectedSort !== '默认排序' }\">\n          {{selectedSort}}\n        </text>\n        <view class=\"filter-arrow\" :class=\"{ 'arrow-up': showSortFilter }\"></view>\n      </view>\n    </view>\n    \n    <!-- 高级已选筛选标签 -->\n    <view class=\"selected-filters\" v-if=\"hasActiveFilters\">\n      <scroll-view scroll-x class=\"filter-tags-scroll\" show-scrollbar=\"false\" enhanced :bounces=\"true\">\n        <view class=\"filter-tags\">\n          <view class=\"filter-tag\" v-if=\"currentTopCategory !== 'find' && currentTopCategory !== 'home_service'\">\n            {{getTopCategoryName(currentTopCategory)}} <text class=\"tag-close\" @click=\"resetTopCategory\">×</text>\n      </view>\n          <view class=\"filter-tag\" v-if=\"selectedCategory !== '全部分类' && selectedCategory !== ''\">\n            {{selectedCategory}} <text class=\"tag-close\" @click=\"resetCategory\">×</text>\n    </view>\n          <view class=\"filter-tag\" v-if=\"selectedSubcategory !== ''\">\n            {{selectedSubcategory}} <text class=\"tag-close\" @click=\"resetSubcategory\">×</text>\n          </view>\n          <view class=\"filter-tag\" v-if=\"selectedArea !== '全部区域'\">\n            {{selectedArea}} <text class=\"tag-close\" @click=\"resetArea\">×</text>\n          </view>\n          <view class=\"filter-tag\" v-if=\"selectedSort !== '默认排序'\">\n            {{selectedSort}} <text class=\"tag-close\" @click=\"resetSort\">×</text>\n          </view>\n          <view class=\"reset-all\" @click=\"resetAllFilters\">清除全部</view>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 高级区域筛选弹出内容 -->\n    <view class=\"filter-dropdown area-dropdown\" v-if=\"showAreaFilter\" :style=\"{ top: filterDropdownTop + 'px', left: areaFilterLeft + 'px', width: filterItemWidth + 'px' }\">\n      <view class=\"dropdown-header\">\n        <text class=\"dropdown-title\">选择区域</text>\n      </view>\n      <scroll-view scroll-y class=\"dropdown-scroll\">\n        <view class=\"dropdown-item\" \n          v-for=\"(area, index) in areaList\" \n          :key=\"index\"\n          :class=\"{ 'active-item': area === selectedArea }\"\n          @click=\"selectArea(area)\">\n          <text class=\"dropdown-item-text\">{{area}}</text>\n          <text class=\"dropdown-item-check\" v-if=\"area === selectedArea\">✓</text>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 高级二级分类筛选弹出内容 -->\n    <view class=\"filter-dropdown subcategory-dropdown\" v-if=\"showSubcategoryFilter\" :style=\"{ top: subcategoryDropdownTop + 'px' }\">\n      <view class=\"dropdown-header\">\n        <text class=\"dropdown-title\">{{selectedCategory}}细分类别</text>\n      </view>\n      <scroll-view scroll-y class=\"dropdown-scroll\">\n        <view class=\"dropdown-item\" \n          v-for=\"(subcat, index) in subcategoryList\" \n          :key=\"index\"\n          :class=\"{ 'active-item': subcat === selectedSubcategory }\"\n          @click=\"selectSubcategory(subcat)\">\n          <text class=\"dropdown-item-text\">{{subcat}}</text>\n          <text class=\"dropdown-item-check\" v-if=\"subcat === selectedSubcategory\">✓</text>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 高级排序筛选弹出内容 -->\n    <view class=\"filter-dropdown sort-dropdown\" v-if=\"showSortFilter\" :style=\"{ top: filterDropdownTop + 'px', left: sortFilterLeft + 'px', width: filterItemWidth + 'px' }\">\n      <view class=\"dropdown-header\">\n        <text class=\"dropdown-title\">排序方式</text>\n      </view>\n      <view class=\"dropdown-item\" \n        v-for=\"(sort, index) in sortList\" \n        :key=\"index\"\n        :class=\"{ 'active-item': sort === selectedSort }\"\n        @click=\"selectSort(sort)\">\n        <text class=\"dropdown-item-text\">{{sort}}</text>\n        <text class=\"dropdown-item-check\" v-if=\"sort === selectedSort\">✓</text>\n      </view>\n    </view>\n    \n    <!-- 高级遮罩层 -->\n    <view class=\"filter-mask\" \n      v-if=\"showAreaFilter || showSubcategoryFilter || showSortFilter\"\n      @click=\"closeAllFilters\"></view>\n    \n    <!-- 高级内容列表 -->\n    <scroll-view \n      scroll-y \n      class=\"service-list\" \n      @scrolltolower=\"loadMore\" \n      refresher-enabled \n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\"\n      enhanced\n      :bounces=\"true\"\n      :show-scrollbar=\"false\">\n      \n      <!-- 高级数据统计提示 -->\n      <view class=\"result-stats\" v-if=\"serviceList.length > 0\">\n        共找到 <text class=\"stats-number\">{{serviceList.length}}</text> 条相关信息\n      </view>\n      \n      <view v-if=\"serviceList.length > 0\" class=\"service-items-wrapper\">\n        <!-- 高级职位卡片 -->\n        <view \n          v-for=\"(item, index) in serviceList\" \n          :key=\"index\" \n          class=\"service-item\"\n          hover-class=\"service-item-hover\"\n          @click=\"navigateToDetail(item)\">\n          <view class=\"service-content\">\n            <view class=\"service-header\">\n              <view class=\"service-tags\">\n              <text class=\"service-tag\">{{item.category}}</text>\n              <text class=\"service-subcategory\" v-if=\"item.subcategory\">{{item.subcategory}}</text>\n              </view>\n              <view class=\"service-meta\">\n              <text class=\"service-area\">{{item.area || '全城'}}</text>\n              <text class=\"service-time\">{{item.time}}</text>\n              </view>\n            </view>\n            \n            <view class=\"service-body\">\n            <text class=\"service-title\">{{item.content}}</text>\n            \n            <!-- 图片区域 -->\n            <view class=\"service-images\" v-if=\"item.images && item.images.length > 0\">\n              <image \n                v-for=\"(img, imgIndex) in item.images.slice(0, 3)\" \n                :key=\"imgIndex\" \n                :src=\"img\" \n                mode=\"aspectFill\" \n                  class=\"service-image\"\n                  :class=\"{'single-image': item.images.length === 1}\"></image>\n              <view class=\"image-count\" v-if=\"item.images.length > 3\">+{{item.images.length - 3}}</view>\n              </view>\n            </view>\n            \n            <view class=\"service-footer\">\n              <view class=\"service-stats\">\n                <text class=\"service-views\">浏览: {{item.views || 0}}</text>\n                <text class=\"service-price\" v-if=\"item.price\">￥{{item.price}}</text>\n              </view>\n              <view class=\"service-actions\">\n                <view class=\"action-btn contact-btn\">\n                  <text class=\"action-icon\"></text>\n                  <text class=\"action-text\">联系</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 高级加载更多提示 -->\n        <view class=\"loading-more\" v-if=\"hasMore\">\n          <view class=\"loading-indicator\"></view>\n          <text class=\"loading-text\">加载中...</text>\n        </view>\n        <view class=\"loading-done\" v-else>\n          <text class=\"loading-done-text\">— 已经到底啦 —</text>\n        </view>\n      </view>\n      \n      <!-- 高级空状态 -->\n      <view v-else class=\"empty-state\">\n        <image src=\"/static/images/empty.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\n        <text class=\"empty-text\">暂无相关职位信息</text>\n        <view class=\"empty-tips\">\n          换个筛选条件试试吧\n        </view>\n        <view class=\"empty-btn\" @click=\"resetAllFilters\">重置筛选</view>\n      </view>\n    </scroll-view>\n    \n    <!-- 高级悬浮发布按钮 -->\n    <view class=\"publish-btn\" hover-class=\"publish-btn-hover\" @click=\"navigateToPublish\">\n      <text class=\"publish-icon\">+</text>\n      <text class=\"publish-text\">发布</text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, nextTick, onMounted } from 'vue';\n\n// 基础数据\nconst statusBarHeight = ref(20);\nconst serviceTitle = ref('服务筛选');\nconst serviceType = ref('find');\nconst refreshing = ref(false);\nconst hasMore = ref(true);\nconst page = ref(1);\nconst serviceList = ref([]);\nconst filterHistory = ref([]);\nconst showSearchBox = ref(true);\nconst loading = ref(false);\n\n// 筛选相关数据\nconst showAreaFilter = ref(false);\nconst showSubcategoryFilter = ref(false);\nconst showSortFilter = ref(false);\nconst selectedArea = ref('全部区域');\nconst selectedCategory = ref('全部分类');\nconst selectedSubcategory = ref('');\nconst selectedSort = ref('默认排序');\nconst filterDropdownTop = ref(0); // 筛选下拉菜单顶部位置\nconst subcategoryDropdownTop = ref(0); // 子分类下拉菜单顶部位置\nconst areaFilterLeft = ref(12); // 区域筛选下拉菜单左侧位置\nconst sortFilterLeft = ref(0); // 排序筛选下拉菜单左侧位置\nconst filterItemWidth = ref(0); // 筛选按钮宽度\n\n// 筛选选项\nconst areaList = ref(['全部区域', '城区', '磁州镇', '讲武城镇', '岳城镇', '观台镇', '白土镇', '黄沙镇']);\nconst sortList = ref(['默认排序', '价格最低', '价格最高', '距离最近']);\nconst categoryList = ref([]);\nconst subcategoryList = ref([]);\n\n// 到家服务子分类类型\nconst homeServiceTypes = ref(['home_service', 'home_cleaning', 'repair', 'installation', 'locksmith', 'moving', 'beauty', 'tutor', 'pet_service', 'plumbing']);\n\n// 顶部分类\nconst topCategories = ref([\n  { type: 'home_service', name: '到家服务' },\n  { type: 'home_cleaning', name: '家政保洁' },\n  { type: 'repair', name: '维修服务' },\n  { type: 'installation', name: '安装服务' },\n  { type: 'locksmith', name: '开锁换锁' },\n  { type: 'moving', name: '搬家拉货' },\n  { type: 'beauty', name: '美容美发' },\n  { type: 'tutor', name: '家教辅导' },\n  { type: 'pet_service', name: '宠物服务' },\n  { type: 'plumbing', name: '管道疏通' },\n  { type: 'find', name: '寻找服务' },\n  { type: 'business', name: '生意转让' },\n  { type: 'job', name: '招聘信息' },\n  { type: 'resume', name: '求职信息' },\n  { type: 'house_rent', name: '房屋出租' },\n  { type: 'house_sell', name: '房屋出售' },\n  { type: 'second_car', name: '二手车辆' },\n  { type: 'pet', name: '宠物信息' },\n  { type: 'car', name: '车辆服务' },\n  { type: 'second_hand', name: '二手闲置' },\n  { type: 'carpool', name: '磁州拼车' },\n  { type: 'education', name: '教育培训' },\n  { type: 'other', name: '其他服务' }\n]);\nconst currentTopCategory = ref('find');\nconst searchKeyword = ref('');\nconst extractedKeywords = ref([]);\nconst selectedKeywords = ref([]);\n\n// 判断是否有激活的筛选条件\nconst hasActiveFilters = computed(() => {\n  return selectedArea.value !== '全部区域' || \n         selectedCategory.value !== '全部分类' || \n         selectedSubcategory.value !== '' ||\n         selectedSort.value !== '默认排序';\n});\n\n// 过滤后的顶部分类\nconst filteredTopCategories = computed(() => {\n  // 如果当前页面是到家服务相关页面，显示到家服务子分类\n  if (homeServiceTypes.value.includes(serviceType.value)) {\n    // 过滤出到家服务子分类\n    const homeServiceCategories = topCategories.value.filter(item => \n      homeServiceTypes.value.includes(item.type)\n    );\n    return homeServiceCategories;\n  }\n  \n  // 定义主要服务类型（一级分类）\n  const mainServiceTypes = [\n    { type: 'find', name: '寻找服务' },\n    { type: 'business', name: '生意转让' },\n    { type: 'job', name: '招聘信息' },\n    { type: 'resume', name: '求职信息' },\n    { type: 'house_rent', name: '房屋出租' },\n    { type: 'house_sell', name: '房屋出售' },\n    { type: 'second_car', name: '二手车辆' },\n    { type: 'pet', name: '宠物信息' },\n    { type: 'car', name: '车辆服务' },\n    { type: 'second_hand', name: '二手闲置' },\n    { type: 'carpool', name: '磁州拼车' },\n    { type: 'education', name: '教育培训' },\n    { type: 'other', name: '其他服务' }\n  ];\n  \n  // 对于其他所有服务类型，显示主要服务类型（一级分类）\n  return mainServiceTypes;\n});\n\n// 设置服务标题\nconst setServiceTitle = (type) => {\n  const titleMap = {\n    'find': '寻找服务',\n    'business': '生意转让',\n    'job': '招聘信息',\n    'resume': '求职信息',\n    'house_rent': '房屋出租',\n    'house_sell': '房屋出售',\n    'second_car': '二手车辆',\n    'pet': '宠物信息',\n    'car': '车辆服务',\n    'second_hand': '二手闲置',\n    'carpool': '磁州拼车',\n    'education': '教育培训',\n    'other': '其他服务'\n  };\n  \n  serviceTitle.value = titleMap[type] || '服务筛选';\n};\n\n// 加载历史筛选条件\nconst loadFilterHistory = () => {\n  try {\n    const key = `filter_history_${serviceType.value}`;\n    const history = uni.getStorageSync(key);\n    if (history) {\n      filterHistory.value = JSON.parse(history);\n      console.log('加载筛选历史:', filterHistory.value);\n    }\n  } catch (e) {\n    console.error('加载筛选历史失败', e);\n  }\n};\n\n// 保存筛选历史\nconst saveFilterHistory = () => {\n  try {\n    // 创建当前筛选条件对象\n    const currentFilter = {\n      area: selectedArea.value,\n      category: selectedCategory.value,\n      subcategory: selectedSubcategory.value,\n      sort: selectedSort.value,\n      timestamp: new Date().getTime()\n    };\n    \n    // 更新历史数组\n    filterHistory.value.unshift(currentFilter);\n    \n    // 只保留最近5条记录\n    filterHistory.value = filterHistory.value.slice(0, 5);\n    \n    // 保存到本地存储\n    const key = `filter_history_${serviceType.value}`;\n    uni.setStorageSync(key, JSON.stringify(filterHistory.value));\n  } catch (e) {\n    console.error('保存筛选历史失败', e);\n  }\n};\n\n// 根据服务类型设置分类选项\nconst setCategoryOptions = (type) => {\n  // 不同服务类型的分类选项\n  const categoryOptions = {\n    // 到家服务子分类\n    'home_service': ['全部分类', '家政保洁', '维修服务', '安装服务', '开锁换锁', '搬家拉货', '美容美发', '家教辅导', '宠物服务', '管道疏通'],\n    'home_cleaning': ['全部分类', '日常保洁', '深度保洁', '开荒保洁', '家电清洗', '玻璃清洗'],\n    'repair': ['全部分类', '水电维修', '家电维修', '门窗维修', '墙面翻新', '其他维修'],\n    'installation': ['全部分类', '家具安装', '家电安装', '灯具安装', '门窗安装', '其他安装'],\n    'locksmith': ['全部分类', '开锁服务', '换锁服务', '汽车开锁', '保险柜开锁', '智能锁安装'],\n    'moving': ['全部分类', '小型搬家', '大型搬家', '长途搬家', '物品运输', '其他搬运'],\n    'beauty': ['全部分类', '美甲美睫', '美容护肤', '美发造型', '化妆服务', '其他美容'],\n    'tutor': ['全部分类', '小学辅导', '初中辅导', '高中辅导', '艺术辅导', '其他辅导'],\n    'pet_service': ['全部分类', '宠物洗澡', '宠物美容', '宠物寄养', '宠物遛狗', '其他服务'],\n    'plumbing': ['全部分类', '管道疏通', '马桶疏通', '下水道疏通', '厨房疏通', '其他疏通'],\n    // 原有的一级分类\n    'find': ['全部分类', '维修', '装修', '搬家', '清洁', '美容美发', '教育培训', '其他'],\n    'business': ['全部分类', '餐饮店', '便利店', '服装店', '美容店', '超市', '其他店铺'],\n    'job': ['全部分类', '销售', '服务员', '技工', '司机', '厨师', '文员', '其他职位'],\n    'resume': ['全部分类', '销售类', '技术类', '服务类', '行政类', '教育类', '其他类'],\n    'house_rent': ['全部分类', '整租', '合租', '短租', '商铺', '写字楼', '厂房'],\n    'house_sell': ['全部分类', '普通住宅', '别墅', '写字楼', '商铺', '厂房', '土地'],\n    'second_car': ['全部分类', '轿车', 'SUV', '面包车', '货车', '电动车', '摩托车'],\n    'pet': ['全部分类', '宠物狗', '宠物猫', '小宠', '宠物用品', '宠物服务'],\n    'car': ['全部分类', '洗车', '保养', '维修', '租车', '陪驾', '其他'],\n    'second_hand': ['全部分类', '手机', '电脑', '家具', '家电', '服装', '图书', '其他'],\n    'carpool': ['全部分类', '上下班', '周末', '节假日', '城际', '长途', '其他'],\n    'education': ['全部分类', '幼儿', '小学', '初中', '高中', '大学', '职业培训', '其他'],\n    'other': ['全部分类', '家政', '维修', '租赁', '其他']\n  };\n  \n  categoryList.value = categoryOptions[type] || ['全部分类'];\n  \n  // 特殊处理招聘信息类型，确保显示正确的分类选项\n  if (type === 'job') {\n    // 这里使用与mock数据中一致的招聘职位分类\n    categoryList.value = ['全部分类', '销售', '服务员', '技工', '司机', '厨师', '文员', '其他职位'];\n  }\n};\n\n// 区域筛选\nconst selectArea = (area) => {\n  // 触感反馈\n  uni.vibrateShort();\n  \n  selectedArea.value = area;\n  showAreaFilter.value = false;\n  resetListAndReload();\n  \n  // 筛选变化后保存历史\n  if (hasActiveFilters.value) {\n    saveFilterHistory();\n  }\n};\n\n// 一级分类选择\nconst selectCategory = (category) => {\n  // 触感反馈\n  uni.vibrateShort();\n  \n  // 直接应用选择的一级分类\n  selectedCategory.value = category;\n  \n  // 当选择全部分类时，清空二级分类\n  if (category === '全部分类') {\n    selectedSubcategory.value = '';\n    subcategoryList.value = [];\n    showSubcategoryFilter.value = false;\n  } else {\n    // 根据选择的一级分类加载二级分类\n    setSubcategoryOptions(category);\n    \n    // 如果当前是招聘信息类型，直接应用筛选，不显示三级分类\n    if (serviceType.value === 'job' || currentTopCategory.value === 'job') {\n      showSubcategoryFilter.value = false;\n    } \n    // 其他类型正常处理\n    else if (subcategoryList.value.length > 0) {\n      showSubcategoryFilter.value = true;\n      \n      // 计算下拉菜单位置\n      nextTick(() => {\n        const query = uni.createSelectorQuery().in(this);\n        query.select('.filter-section').boundingClientRect(container => {\n          if (container) {\n            // 如果有已选筛选标签，考虑其高度\n            if (hasActiveFilters.value) {\n              query.select('.selected-filters').boundingClientRect(filters => {\n                if (filters) {\n                  subcategoryDropdownTop.value = filters.height + filters.top;\n                } else {\n                  subcategoryDropdownTop.value = container.height + container.top;\n                }\n              }).exec();\n            } else {\n              subcategoryDropdownTop.value = container.height + container.top;\n            }\n          }\n        }).exec();\n      });\n    } else {\n      showSubcategoryFilter.value = false;\n    }\n  }\n  \n  // 重新加载数据\n  resetListAndReload();\n  \n  // 筛选变化后保存历史\n  if (hasActiveFilters.value) {\n    saveFilterHistory();\n  }\n};\n\n// 二级分类选择\nconst selectSubcategory = (subcategory) => {\n  // 触感反馈\n  uni.vibrateShort();\n  \n  // 直接应用所选的子分类\n  selectedSubcategory.value = subcategory;\n  \n  // 关闭筛选弹窗\n  showSubcategoryFilter.value = false;\n  \n  // 重新加载数据\n  resetListAndReload();\n  \n  // 筛选变化后保存历史\n  if (hasActiveFilters.value) {\n    saveFilterHistory();\n  }\n};\n\n// 排序筛选\nconst selectSort = (sort) => {\n  // 触感反馈\n  uni.vibrateShort();\n  \n  selectedSort.value = sort;\n  showSortFilter.value = false;\n  resetListAndReload();\n  \n  // 筛选变化后保存历史\n  if (hasActiveFilters.value) {\n    saveFilterHistory();\n  }\n};\n\n// 设置二级分类选项\nconst setSubcategoryOptions = (category) => {\n  // 二级分类数据\n  const subcategoryMap = {\n    // 到家服务子分类的子分类\n    '家政保洁': ['日常保洁', '深度保洁', '开荒保洁', '家电清洗', '玻璃清洗'],\n    '维修服务': ['水电维修', '家电维修', '门窗维修', '墙面翻新', '其他维修'],\n    '安装服务': ['家具安装', '家电安装', '灯具安装', '门窗安装', '其他安装'],\n    '开锁换锁': ['开锁服务', '换锁服务', '汽车开锁', '保险柜开锁', '智能锁安装'],\n    '搬家拉货': ['小型搬家', '大型搬家', '长途搬家', '物品运输', '其他搬运'],\n    '美容美发': ['美甲美睫', '美容护肤', '美发造型', '化妆服务', '其他美容'],\n    '家教辅导': ['小学辅导', '初中辅导', '高中辅导', '艺术辅导', '其他辅导'],\n    '宠物服务': ['宠物洗澡', '宠物美容', '宠物寄养', '宠物遛狗', '其他服务'],\n    '管道疏通': ['管道疏通', '马桶疏通', '下水道疏通', '厨房疏通', '其他疏通'],\n    // 原有的分类子分类 - 除了到家服务子分类外，其他所有分类的三级分类全部删掉\n    'business': {},\n    'job': {},\n    'second_hand': {},\n    'house_rent': {},\n    'house_sell': {},\n    'second_car': {},\n    'pet': {},\n    'car': {},\n    'carpool': {},\n    'education': {},\n    'other': {}\n  };\n  \n  // 获取当前服务类型的二级分类映射\n  if (homeServiceTypes.value.includes(serviceType.value)) {\n    // 如果是到家服务子分类，直接使用映射\n    subcategoryList.value = subcategoryMap[category] || [];\n  } else {\n    // 其他服务类型使用原有逻辑，但三级分类已被清空\n    const currentTypeSubcats = subcategoryMap[serviceType.value] || {};\n    subcategoryList.value = currentTypeSubcats[category] || [];\n  }\n};\n\n// 关闭所有筛选\nconst closeAllFilters = () => {\n  showAreaFilter.value = false;\n  showSubcategoryFilter.value = false;\n  showSortFilter.value = false;\n};\n\n// 重置所有筛选条件\nconst resetAllFilters = () => {\n  // 触感反馈\n  uni.vibrateShort({type: 'medium'});\n  \n  selectedArea.value = '全部区域';\n  selectedCategory.value = '全部分类';\n  selectedSubcategory.value = '';\n  selectedSort.value = '默认排序';\n  \n  // 重新加载数据\n  resetListAndReload();\n};\n\n// 重置区域筛选\nconst resetArea = () => {\n  selectedArea.value = '全部区域';\n  resetListAndReload();\n};\n\n// 重置子分类筛选\nconst resetSubcategory = () => {\n  selectedSubcategory.value = '';\n  resetListAndReload();\n};\n\n// 重置排序筛选\nconst resetSort = () => {\n  selectedSort.value = '默认排序';\n  resetListAndReload();\n};\n\n// 重置列表并重新加载\nconst resetListAndReload = () => {\n  page.value = 1;\n  serviceList.value = [];\n  hasMore.value = true;\n  loadServiceList();\n};\n\n// 加载服务列表\nconst loadServiceList = () => {\n  if (loading.value) return;\n  \n  loading.value = true;\n  \n  // 构建筛选参数\n  const params = {\n    page: page.value,\n    pageSize: 10,\n    category: selectedCategory.value === '全部分类' ? '' : selectedCategory.value,\n    subcategory: selectedSubcategory.value,\n    area: selectedArea.value === '全部区域' ? '' : selectedArea.value,\n    sort: selectedSort.value === '默认排序' ? '' : selectedSort.value,\n    keyword: searchKeyword.value\n  };\n  \n  // 特殊处理不同服务类型，映射到正确的category值\n  const categoryMap = {\n    'job': '招聘信息',\n    'resume': '求职信息',\n    'business': '生意转让',\n    'house_rent': '房屋出租',\n    'house_sell': '房屋出售',\n    'second_car': '二手车辆',\n    'pet': '宠物信息',\n    'car': '车辆服务',\n    'second_hand': '二手闲置',\n    'carpool': '磁州拼车',\n    'education': '教育培训',\n    'other': '其他服务'\n  };\n  \n  // 如果是特定的服务类型，强制设置category参数\n  if (categoryMap[serviceType.value] || categoryMap[currentTopCategory.value]) {\n    // 优先使用当前顶部分类的映射\n    params.category = categoryMap[currentTopCategory.value] || categoryMap[serviceType.value];\n    console.log(`应用特殊分类筛选: ${params.category}`);\n  }\n  \n  console.log('加载服务列表参数:', params);\n  \n  // 调用API获取数据\n  import('@/mock/api.js').then(module => {\n    const { serviceApi } = module;\n    \n    // 确保API可用\n    if (!serviceApi || !serviceApi.fetchPublishList) {\n      console.error('API不可用:', serviceApi);\n      loading.value = false;\n      refreshing.value = false;\n      return;\n    }\n    \n    // 使用 Promise.resolve 确保即使 API 调用失败也能返回空数组\n    Promise.resolve()\n      .then(() => serviceApi.fetchPublishList(params))\n      .then(res => {\n        if (page.value === 1) {\n          serviceList.value = res.list || [];\n        } else {\n          serviceList.value = [...serviceList.value, ...(res.list || [])];\n        }\n        \n        hasMore.value = res.hasMore;\n        loading.value = false;\n        refreshing.value = false;\n        \n        console.log('加载服务列表成功:', serviceList.value.length);\n      })\n      .catch(err => {\n        console.error('加载服务列表失败', err);\n        loading.value = false;\n        refreshing.value = false;\n      });\n  }).catch(err => {\n    console.error('导入API模块失败', err);\n    loading.value = false;\n    refreshing.value = false;\n  });\n};\n\n// 下拉刷新\nconst onRefresh = () => {\n  refreshing.value = true;\n  resetListAndReload();\n  \n  // 添加触感反馈\n  uni.vibrateShort();\n};\n\n// 加载更多\nconst loadMore = () => {\n  if (hasMore.value) {\n    page.value++;\n    loadServiceList();\n  }\n};\n\n// 导航到详情页\nconst navigateToDetail = (item) => {\n  // 根据不同类型跳转到不同详情页\n  let url = `/pages/publish/info-detail?id=${item.id}`;\n  \n  const detailPageMap = {\n    'find': 'find-service-detail',\n    'business': 'business-transfer-detail',\n    'job': 'job-detail',\n    'resume': 'job-seeking-detail',\n    'house_rent': 'house-rent-detail',\n    'house_sell': 'house-sale-detail',\n    'second_car': 'car-detail',\n    'pet': 'pet-detail',\n    'car': 'vehicle-service-detail',\n    'second_hand': 'second-hand-detail',\n    'carpool': 'carpool-detail',\n    'education': 'education-detail',\n    'dating': 'dating-detail',\n    'merchant_activity': 'merchant-activity-detail',\n    'home_service': 'home-service-detail',\n    'home_cleaning': 'home-service-detail',\n    'repair': 'home-service-detail',\n    'installation': 'home-service-detail',\n    'locksmith': 'home-service-detail',\n    'moving': 'home-service-detail',\n    'beauty': 'home-service-detail',\n    'tutor': 'home-service-detail',\n    'pet_service': 'home-service-detail',\n    'plumbing': 'home-service-detail'\n  };\n  \n  // 如果有对应的详情页映射，使用映射\n  if (detailPageMap[serviceType.value]) {\n    url = `/pages/publish/${detailPageMap[serviceType.value]}?id=${item.id}`;\n  }\n  \n  // 跳转到详情页\n  uni.navigateTo({\n    url: url\n  });\n};\n\n// 导航到发布页\nconst navigateToPublish = () => {\n  // 添加触感反馈\n  uni.vibrateShort();\n  \n  // 从服务类型转换为发布类型\n  const publishTypeMap = {\n    'find': 'find_service',\n    'business': 'business_transfer',\n    'job': 'hire',\n    'resume': 'job_wanted',\n    'house_rent': 'house_rent',\n    'house_sell': 'house_sell',\n    'second_car': 'used_car',\n    'pet': 'pet',\n    'car': 'car_service',\n    'second_hand': 'second_hand',\n    'carpool': 'carpool',\n    'education': 'education',\n    'other': 'other_service'\n  };\n  \n  const publishType = publishTypeMap[serviceType.value] || 'other_service';\n  uni.navigateTo({\n    url: `/pages/publish/detail?type=${publishType}&name=${encodeURIComponent(serviceTitle.value)}`\n  });\n};\n\n// 返回上一页\nconst navigateBack = () => {\n  uni.navigateBack();\n};\n\n// 切换顶部分类\nconst switchTopCategory = (item) => {\n  // 触感反馈\n  uni.vibrateShort();\n  \n  // 设置当前顶部分类\n  currentTopCategory.value = item.type;\n  serviceType.value = item.type;\n  \n  // 设置标题\n  setServiceTitle(item.type);\n  \n  // 重置筛选条件\n  selectedCategory.value = '全部分类';\n  selectedSubcategory.value = '';\n  \n  // 设置分类选项\n  setCategoryOptions(item.type);\n  \n  // 特殊处理某些分类类型，比如招聘信息\n  const specialCategoryMap = {\n    'job': '招聘信息',\n    'resume': '求职信息',\n    'business': '生意转让',\n    'house_rent': '房屋出租',\n    'house_sell': '房屋出售',\n    'second_car': '二手车辆',\n    'pet': '宠物信息',\n    'car': '车辆服务',\n    'second_hand': '二手闲置',\n    'carpool': '磁州拼车',\n    'education': '教育培训'\n  };\n  \n  // 如果是特殊分类，设置对应的分类名称\n  if (specialCategoryMap[item.type]) {\n    console.log(`设置特殊分类: ${specialCategoryMap[item.type]}`);\n  }\n  \n  // 重新加载数据\n  resetListAndReload();\n};\n\n// 关键词搜索\nconst onSearchInput = () => {\n  // 如果输入为空，清除提取的关键词\n  if (!searchKeyword.value.trim()) {\n    extractedKeywords.value = [];\n    selectedKeywords.value = [];\n    resetListAndReload();\n    return;\n  }\n  \n  // 自动提取关键词\n  setTimeout(() => {\n    extractKeywords();\n  }, 300);\n};\n\n// 提取关键词\nconst extractKeywords = () => {\n  const input = searchKeyword.value.trim();\n  if (!input) return;\n  \n  // 使用简单的分词规则：按空格、逗号、句号等符号分割\n  let words = input.split(/[\\s,，.。;；!！?？、]/g)\n               .filter(word => word.length >= 2) // 过滤短词\n               .map(word => word.trim())\n               .filter(word => word);\n  \n  // 去重\n  words = [...new Set(words)];\n  \n  // 限制最多提取5个关键词\n  if (words.length > 5) {\n    words = words.slice(0, 5);\n  }\n  \n  // 补充从分类和内容中提取的关键词\n  addContextualKeywords(words);\n  \n  // 更新提取的关键词\n  extractedKeywords.value = words;\n  \n  // 默认选中所有提取出的关键词\n  selectedKeywords.value = [...words];\n  \n  // 应用关键词筛选\n  applyKeywordFilter();\n};\n\n// 补充上下文关键词\nconst addContextualKeywords = (words) => {\n  // 添加分类相关的关键词\n  if (selectedCategory.value !== '全部分类') {\n    if (!words.includes(selectedCategory.value)) {\n      words.push(selectedCategory.value);\n    }\n  }\n  \n  // 添加子分类相关的关键词\n  if (selectedSubcategory.value && !words.includes(selectedSubcategory.value)) {\n    words.push(selectedSubcategory.value);\n  }\n  \n  // 添加区域相关的关键词\n  if (selectedArea.value !== '全部区域' && !words.includes(selectedArea.value)) {\n    words.push(selectedArea.value);\n  }\n  \n  // 从常见关键词列表中匹配\n  const commonKeywords = getCommonKeywords();\n  for (const keyword of commonKeywords) {\n    if (searchKeyword.value.includes(keyword) && !words.includes(keyword)) {\n      words.push(keyword);\n    }\n  }\n};\n\n// 获取常见关键词\nconst getCommonKeywords = () => {\n  // 根据当前服务类型返回相关的常见关键词\n  const commonKeywordMap = {\n    'find': ['维修', '安装', '保洁', '搬家', '上门', '快速', '专业', '经验', '价格优惠'],\n    'business': ['转让', '盈利', '客源稳定', '位置好', '接手即可营业', '证件齐全', '房租低'],\n    'job': ['招聘', '急聘', '有经验', '薪资高', '包吃住', '五险一金', '双休', '朝九晚五'],\n    'resume': ['求职', '经验丰富', '应届毕业', '有证书', '会计', '司机', '销售', '文员'],\n    'house_rent': ['出租', '整租', '合租', '精装', '家电齐全', '拎包入住', '交通便利', '南北通透'],\n    'house_sell': ['出售', '精装修', '学区房', '交通便利', '地段好', '采光好', '低总价', '小户型'],\n    'second_car': ['二手车', '准新车', '行驶里程少', '车况好', '价格优惠', '一手车', '无事故'],\n    'pet': ['宠物', '幼犬', '幼猫', '疫苗已做', '驱虫已做', '包健康', '可上门看', '纯种'],\n    'car': ['洗车', '保养', '维修', '补胎', '贴膜', '换机油', '年检', '过户'],\n    'second_hand': ['二手', '全新', '九成新', '低价', '急售', '搬家甩卖', '有发票', '保修期内'],\n    'education': ['培训', '辅导', '一对一', '小班', '提分快', '经验丰富', '上门', '包学会']\n  };\n  \n  return commonKeywordMap[serviceType.value] || [];\n};\n\n// 应用关键词搜索\nconst applyKeywordSearch = () => {\n  if (!searchKeyword.value.trim()) {\n    return;\n  }\n  \n  // 触感反馈\n  uni.vibrateShort();\n  \n  // 提取关键词并应用筛选\n  extractKeywords();\n};\n\n// 清除关键词\nconst clearKeyword = () => {\n  searchKeyword.value = '';\n  extractedKeywords.value = [];\n  selectedKeywords.value = [];\n  \n  // 重新加载数据\n  resetListAndReload();\n};\n\n// 切换关键词\nconst toggleKeyword = (keyword) => {\n  // 触感反馈\n  uni.vibrateShort();\n  \n  const index = selectedKeywords.value.indexOf(keyword);\n  if (index === -1) {\n    // 添加关键词\n    selectedKeywords.value.push(keyword);\n  } else {\n    // 移除关键词\n    selectedKeywords.value.splice(index, 1);\n  }\n  \n  // 应用关键词筛选\n  applyKeywordFilter();\n};\n\n// 应用关键词筛选\nconst applyKeywordFilter = () => {\n  // 重置列表并重新加载\n  resetListAndReload();\n};\n\n// 获取顶部分类名称\nconst getTopCategoryName = (type) => {\n  const category = topCategories.value.find(item => item.type === type);\n  return category ? category.name : '';\n};\n\n// 重置顶部分类\nconst resetTopCategory = () => {\n  currentTopCategory.value = 'find';\n  serviceType.value = 'find';\n  setServiceTitle('find');\n  setCategoryOptions('find');\n  resetListAndReload();\n};\n\n// 重置子分类\nconst resetCategory = () => {\n  selectedCategory.value = '全部分类';\n  selectedSubcategory.value = '';\n  resetListAndReload();\n};\n\n// 区域筛选\nconst toggleAreaFilter = () => {\n  // 关闭其他筛选\n  showSortFilter.value = false;\n  showSubcategoryFilter.value = false;\n  \n  // 切换区域筛选显示状态\n  showAreaFilter.value = !showAreaFilter.value;\n  \n  // 如果显示区域筛选，计算下拉菜单位置\n  if (showAreaFilter.value) {\n    nextTick(() => {\n      const query = uni.createSelectorQuery();\n      query.select('.filter-section').boundingClientRect(filterSection => {\n        if (filterSection) {\n          // 下拉菜单应该紧贴筛选按钮底部\n          filterDropdownTop.value = filterSection.top + filterSection.height;\n          \n          // 获取区域筛选按钮的位置和宽度\n          query.select('.filter-item').boundingClientRect(areaItem => {\n            if (areaItem) {\n              areaFilterLeft.value = areaItem.left;\n              filterItemWidth.value = areaItem.width;\n            }\n          }).exec();\n        }\n      }).exec();\n    });\n  }\n};\n\n// 排序筛选\nconst toggleSortFilter = () => {\n  // 关闭其他筛选\n  showAreaFilter.value = false;\n  showSubcategoryFilter.value = false;\n  \n  // 切换排序筛选显示状态\n  showSortFilter.value = !showSortFilter.value;\n  \n  // 如果显示排序筛选，计算下拉菜单位置\n  if (showSortFilter.value) {\n    nextTick(() => {\n      const query = uni.createSelectorQuery();\n      query.select('.filter-section').boundingClientRect(filterSection => {\n        if (filterSection) {\n          // 下拉菜单应该紧贴筛选按钮底部\n          filterDropdownTop.value = filterSection.top + filterSection.height;\n          \n          // 获取排序筛选按钮的位置和宽度\n          query.selectAll('.filter-item').boundingClientRect(items => {\n            if (items && items.length >= 2) {\n              const sortItem = items[1]; // 第二个是排序按钮\n              sortFilterLeft.value = sortItem.left;\n              filterItemWidth.value = sortItem.width;\n            }\n          }).exec();\n        }\n      }).exec();\n    });\n  }\n};\n\n// 页面加载\nonMounted(() => {\n  // 获取路由参数\n  const pages = getCurrentPages();\n  const page = pages[pages.length - 1];\n  const options = page.$page?.options || {};\n  \n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n  \n  // 获取服务类型和设置标题\n  if (options.type) {\n    serviceType.value = options.type;\n    currentTopCategory.value = options.type;\n    \n    // 优先使用传递过来的标题\n    if (options.title) {\n      serviceTitle.value = decodeURIComponent(options.title);\n      console.log('使用传递的标题:', serviceTitle.value);\n    } else {\n      setServiceTitle(options.type);\n    }\n    \n    // 加载历史筛选条件\n    loadFilterHistory();\n    \n    // 根据类型设置分类选项\n    setCategoryOptions(options.type);\n    \n    // 特殊处理某些服务类型，确保正确的分类筛选\n    const specialCategoryMap = {\n      'job': '招聘信息',\n      'resume': '求职信息',\n      'business': '生意转让',\n      'house_rent': '房屋出租',\n      'house_sell': '房屋出售',\n      'second_car': '二手车辆',\n      'pet': '宠物信息',\n      'car': '车辆服务',\n      'second_hand': '二手闲置',\n      'carpool': '磁州拼车',\n      'education': '教育培训'\n    };\n    \n    // 如果是特殊服务类型，预设对应的分类名称\n    if (specialCategoryMap[options.type] && !options.category) {\n      console.log(`初始化特殊分类: ${specialCategoryMap[options.type]}`);\n    }\n  }\n  \n  // 如果有active参数，设置激活的顶部分类\n  if (options.active) {\n    console.log('设置激活的顶部分类:', options.active);\n    currentTopCategory.value = options.active;\n    \n    // 如果active参数不同于type，重新设置分类选项\n    if (options.active !== serviceType.value) {\n      serviceType.value = options.active;\n      setCategoryOptions(options.active);\n      setServiceTitle(options.active);\n    }\n  }\n  \n  // 如果有预设筛选条件\n  if (options.area) selectedArea.value = options.area;\n  if (options.category) {\n    selectedCategory.value = options.category;\n    // 如果有一级分类，尝试加载二级分类\n    if (selectedCategory.value !== '全部分类') {\n      setSubcategoryOptions(selectedCategory.value);\n    }\n  }\n  if (options.subcategory) selectedSubcategory.value = options.subcategory;\n  if (options.sort) selectedSort.value = options.sort;\n  \n  // 添加触感反馈 - 页面加载完成\n  setTimeout(() => {\n    uni.vibrateShort();\n  }, 300);\n  \n  // 加载服务列表\n  loadServiceList();\n});\n</script>\n\n<style lang=\"scss\" scoped>\n/* 全局容器样式 */\n.service-filter-container {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f6f8fa;\n  \n  &.premium-style {\n    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n  }\n}\n\n/* 高级自定义导航栏 */\n.custom-navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background: linear-gradient(120deg, #0070f3, #00a1ff);\n  padding: 0 16px;\n  position: relative;\n  z-index: 1000;\n  box-shadow: 0 1px 12px rgba(0, 112, 243, 0.18);\n}\n\n.back-btn {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  position: relative;\n  z-index: 2;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #fff;\n  border-left: 2px solid #fff;\n  transform: rotate(-45deg);\n  transition: transform 0.2s ease;\n}\n\n.back-btn:active .back-icon {\n  transform: rotate(-45deg) scale(0.9);\n}\n\n.navbar-title {\n  position: absolute;\n  left: 0;\n  right: 0;\n  text-align: center;\n  color: #fff;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: -0.2px;\n}\n\n.navbar-right {\n  width: 36px;\n  position: relative;\n  z-index: 2;\n}\n\n/* 高级搜索框 */\n.search-container {\n  padding: 12px 16px 16px;\n  background: #fff;\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n  margin-bottom: 8px;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  background-color: #f5f7fa;\n  border-radius: 12px;\n  padding: 0 12px;\n  height: 40px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);\n  overflow: hidden;\n}\n\n.search-icon {\n  width: 18px;\n  height: 18px;\n  margin-right: 8px;\n  opacity: 0.6;\n}\n\n.search-input {\n  flex: 1;\n  height: 40px;\n  font-size: 15px;\n  color: #333;\n  background: transparent;\n}\n\n.search-btn {\n  height: 40px;\n  line-height: 40px;\n  padding: 0 16px;\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  color: #fff;\n  font-size: 15px;\n  font-weight: 500;\n  border-radius: 10px;\n  margin-right: -12px;\n}\n\n/* 高级一级分类标签栏 */\n.top-category-tabs {\n  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));\n  padding: 14px 0 12px;\n  white-space: nowrap;\n  border-radius: 16px;\n  margin: 8px 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\n}\n\n.top-category-item {\n  display: inline-block;\n  padding: 8px 20px;\n  margin: 0 5px;\n  font-size: 15px;\n  color: #555;\n  border-radius: 20px;\n  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);\n}\n\n.top-category-item:first-child {\n  margin-left: 16px;\n}\n\n.top-category-item:last-child {\n  margin-right: 16px;\n}\n\n.active-top-category {\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  color: #fff;\n  font-weight: 500;\n  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);\n}\n\n/* 高级子分类标签栏 */\n.subcategory-tabs {\n  background: rgba(255, 255, 255, 0.95);\n  padding: 10px 0;\n  white-space: nowrap;\n  border-radius: 16px;\n  margin: 0 12px 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\n}\n\n.subcategory-item {\n  display: inline-block;\n  padding: 6px 16px;\n  margin: 0 5px;\n  font-size: 14px;\n  color: #666;\n  border-radius: 16px;\n  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);\n  background-color: #f1f2f6;\n}\n\n.subcategory-item:first-child {\n  margin-left: 16px;\n}\n\n.subcategory-item:last-child {\n  margin-right: 16px;\n}\n\n.active-subcategory {\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  color: #fff;\n  font-weight: 500;\n  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);\n}\n\n/* 高级筛选条件栏 */\n.filter-section {\n  display: flex;\n  height: 54px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 16px;\n  position: relative;\n  justify-content: center;\n  padding: 0;\n  z-index: 95;\n  margin: 0 12px 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\n}\n\n.filter-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  height: 100%;\n  font-size: 15px;\n  color: #333;\n  font-weight: 500;\n  transition: background-color 0.2s ease;\n}\n\n.filter-item:active {\n  background-color: rgba(0, 0, 0, 0.02);\n}\n\n.filter-item:not(:last-child)::after {\n  content: '';\n  position: absolute;\n  right: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 1px;\n  height: 24px;\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.filter-text {\n  display: inline-block;\n  max-width: 80%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.active-filter {\n  color: #0070f3;\n  font-weight: 600;\n}\n\n.filter-arrow {\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-top: 5px solid #999;\n  margin-left: 8px;\n  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);\n}\n\n.arrow-up {\n  transform: rotate(180deg);\n  border-top-color: #0070f3;\n}\n\n/* 高级已选筛选标签 */\n.selected-filters {\n  margin: 0 12px 12px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 16px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\n  padding: 10px 0;\n}\n\n.filter-tags-scroll {\n  white-space: nowrap;\n}\n\n.filter-tags {\n  display: inline-flex;\n  padding: 0 16px;\n}\n\n.filter-tag {\n  display: inline-block;\n  padding: 6px 12px;\n  background: rgba(0, 112, 243, 0.08);\n  color: #0070f3;\n  font-size: 13px;\n  border-radius: 16px;\n  margin-right: 8px;\n}\n\n.tag-close {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  line-height: 16px;\n  text-align: center;\n  border-radius: 50%;\n  background-color: rgba(0, 112, 243, 0.15);\n  margin-left: 6px;\n  font-size: 14px;\n}\n\n.reset-all {\n  display: inline-block;\n  padding: 6px 12px;\n  background: rgba(255, 59, 48, 0.08);\n  color: #ff3b30;\n  font-size: 13px;\n  border-radius: 16px;\n}\n\n/* 高级下拉菜单 */\n.filter-dropdown {\n  position: absolute;\n  left: 0;\n  background: #fff;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  z-index: 100;\n  max-height: 400px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.dropdown-header {\n  padding: 12px 16px;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.dropdown-title {\n  font-size: 15px;\n  font-weight: 600;\n  color: #333;\n}\n\n.dropdown-scroll {\n  max-height: 350px;\n}\n\n.dropdown-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  font-size: 14px;\n  color: #333;\n  transition: background-color 0.2s ease;\n}\n\n.dropdown-item:active {\n  background-color: rgba(0, 0, 0, 0.02);\n}\n\n.active-item {\n  color: #0070f3;\n  font-weight: 500;\n}\n\n.dropdown-item-text {\n  flex: 1;\n}\n\n.dropdown-item-check {\n  color: #0070f3;\n  font-weight: bold;\n}\n\n/* 高级遮罩层 */\n.filter-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 90;\n  backdrop-filter: blur(2px);\n}\n\n/* 高级内容列表 */\n.service-list {\n  flex: 1;\n  position: relative;\n}\n\n/* 高级服务卡片 */\n.service-item {\n  margin: 0 12px 16px;\n  background: #fff;\n  border-radius: 16px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\n  overflow: hidden;\n  position: relative;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.service-item-hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n}\n\n.service-content {\n  padding: 16px;\n}\n\n.service-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 12px;\n}\n\n.service-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.service-tag {\n  background: rgba(0, 112, 243, 0.08);\n  color: #0070f3;\n  font-size: 12px;\n  padding: 4px 8px;\n  border-radius: 4px;\n  margin-right: 8px;\n}\n\n.service-subcategory {\n  background: rgba(255, 149, 0, 0.08);\n  color: #ff9500;\n  font-size: 12px;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n\n.service-meta {\n  display: flex;\n  align-items: center;\n}\n\n.service-area {\n  font-size: 12px;\n  color: #666;\n  margin-right: 8px;\n}\n\n.service-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.service-body {\n  margin-bottom: 12px;\n}\n\n.service-title {\n  font-size: 16px;\n  color: #333;\n  font-weight: 500;\n  line-height: 1.5;\n  margin-bottom: 12px;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.service-images {\n  display: flex;\n  margin-top: 12px;\n  position: relative;\n}\n\n.service-image {\n  width: 33%;\n  height: 180rpx;\n  margin-right: 6px;\n  border-radius: 8px;\n  object-fit: cover;\n}\n\n.single-image {\n  width: 66%;\n}\n\n.image-count {\n  position: absolute;\n  right: 10px;\n  bottom: 10px;\n  background: rgba(0, 0, 0, 0.6);\n  color: #fff;\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 10px;\n}\n\n.service-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\n  padding-top: 12px;\n}\n\n.service-stats {\n  display: flex;\n  align-items: center;\n}\n\n.service-views {\n  font-size: 12px;\n  color: #999;\n  margin-right: 12px;\n}\n\n.service-price {\n  font-size: 16px;\n  color: #ff3b30;\n  font-weight: 500;\n}\n\n.service-actions {\n  display: flex;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 112, 243, 0.08);\n  color: #0070f3;\n  font-size: 13px;\n  padding: 6px 12px;\n  border-radius: 16px;\n}\n\n.contact-btn {\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  color: #fff;\n}\n\n.action-icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 4px;\n}\n\n.action-text {\n  font-size: 13px;\n}\n\n/* 高级加载更多 */\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px 0;\n}\n\n.loading-indicator {\n  width: 20px;\n  height: 20px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #0070f3;\n  border-radius: 50%;\n  animation: spin 0.8s linear infinite;\n  margin-right: 8px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  font-size: 14px;\n  color: #999;\n}\n\n.loading-done {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.loading-done-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 高级空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 0;\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 20px;\n}\n\n.empty-text {\n  font-size: 16px;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.empty-tips {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 20px;\n}\n\n.empty-btn {\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  color: #fff;\n  font-size: 14px;\n  padding: 8px 24px;\n  border-radius: 20px;\n}\n\n/* 高级统计提示 */\n.result-stats {\n  padding: 12px 16px;\n  font-size: 14px;\n  color: #666;\n}\n\n.stats-number {\n  color: #0070f3;\n  font-weight: 500;\n}\n\n.service-items-wrapper {\n  padding-bottom: 80px;\n}\n\n/* 高级发布按钮 */\n.publish-btn {\n  position: fixed;\n  right: 20px;\n  bottom: 30px;\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  width: 120px;\n  height: 44px;\n  border-radius: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.3);\n  z-index: 99;\n}\n\n.publish-btn-hover {\n  opacity: 0.9;\n  transform: scale(0.98);\n}\n\n.publish-icon {\n  font-size: 20px;\n  margin-right: 4px;\n  font-weight: 300;\n}\n\n.publish-text {\n  font-size: 15px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/service/pages/filter.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "nextTick", "onMounted", "page"], "mappings": ";;;;;;AAkPA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAC/B,UAAM,cAAcA,cAAAA,IAAI,MAAM;AAC9B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,OAAOA,cAAAA,IAAI,CAAC;AAClB,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,IAAI;AAC9B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AAGzB,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,wBAAwBA,cAAAA,IAAI,KAAK;AACvC,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAC/B,UAAM,mBAAmBA,cAAAA,IAAI,MAAM;AACnC,UAAM,sBAAsBA,cAAAA,IAAI,EAAE;AAClC,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAC/B,UAAM,oBAAoBA,cAAAA,IAAI,CAAC;AAC/B,UAAM,yBAAyBA,cAAAA,IAAI,CAAC;AACpC,UAAM,iBAAiBA,cAAAA,IAAI,EAAE;AAC7B,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAC5B,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAG7B,UAAM,WAAWA,cAAG,IAAC,CAAC,QAAQ,MAAM,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,CAAC;AAC9E,UAAM,WAAWA,cAAG,IAAC,CAAC,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AACrD,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAC3B,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAG9B,UAAM,mBAAmBA,cAAAA,IAAI,CAAC,gBAAgB,iBAAiB,UAAU,gBAAgB,aAAa,UAAU,UAAU,SAAS,eAAe,UAAU,CAAC;AAG7J,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,EAAE,MAAM,gBAAgB,MAAM,OAAQ;AAAA,MACtC,EAAE,MAAM,iBAAiB,MAAM,OAAQ;AAAA,MACvC,EAAE,MAAM,UAAU,MAAM,OAAQ;AAAA,MAChC,EAAE,MAAM,gBAAgB,MAAM,OAAQ;AAAA,MACtC,EAAE,MAAM,aAAa,MAAM,OAAQ;AAAA,MACnC,EAAE,MAAM,UAAU,MAAM,OAAQ;AAAA,MAChC,EAAE,MAAM,UAAU,MAAM,OAAQ;AAAA,MAChC,EAAE,MAAM,SAAS,MAAM,OAAQ;AAAA,MAC/B,EAAE,MAAM,eAAe,MAAM,OAAQ;AAAA,MACrC,EAAE,MAAM,YAAY,MAAM,OAAQ;AAAA,MAClC,EAAE,MAAM,QAAQ,MAAM,OAAQ;AAAA,MAC9B,EAAE,MAAM,YAAY,MAAM,OAAQ;AAAA,MAClC,EAAE,MAAM,OAAO,MAAM,OAAQ;AAAA,MAC7B,EAAE,MAAM,UAAU,MAAM,OAAQ;AAAA,MAChC,EAAE,MAAM,cAAc,MAAM,OAAQ;AAAA,MACpC,EAAE,MAAM,cAAc,MAAM,OAAQ;AAAA,MACpC,EAAE,MAAM,cAAc,MAAM,OAAQ;AAAA,MACpC,EAAE,MAAM,OAAO,MAAM,OAAQ;AAAA,MAC7B,EAAE,MAAM,OAAO,MAAM,OAAQ;AAAA,MAC7B,EAAE,MAAM,eAAe,MAAM,OAAQ;AAAA,MACrC,EAAE,MAAM,WAAW,MAAM,OAAQ;AAAA,MACjC,EAAE,MAAM,aAAa,MAAM,OAAQ;AAAA,MACnC,EAAE,MAAM,SAAS,MAAM,OAAQ;AAAA,IACjC,CAAC;AACD,UAAM,qBAAqBA,cAAAA,IAAI,MAAM;AACrC,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,oBAAoBA,cAAAA,IAAI,CAAA,CAAE;AAChC,UAAM,mBAAmBA,cAAAA,IAAI,CAAA,CAAE;AAG/B,UAAM,mBAAmBC,cAAQ,SAAC,MAAM;AACtC,aAAO,aAAa,UAAU,UACvB,iBAAiB,UAAU,UAC3B,oBAAoB,UAAU,MAC9B,aAAa,UAAU;AAAA,IAChC,CAAC;AAGD,UAAM,wBAAwBA,cAAQ,SAAC,MAAM;AAE3C,UAAI,iBAAiB,MAAM,SAAS,YAAY,KAAK,GAAG;AAEtD,cAAM,wBAAwB,cAAc,MAAM;AAAA,UAAO,UACvD,iBAAiB,MAAM,SAAS,KAAK,IAAI;AAAA,QAC/C;AACI,eAAO;AAAA,MACR;AAGD,YAAM,mBAAmB;AAAA,QACvB,EAAE,MAAM,QAAQ,MAAM,OAAQ;AAAA,QAC9B,EAAE,MAAM,YAAY,MAAM,OAAQ;AAAA,QAClC,EAAE,MAAM,OAAO,MAAM,OAAQ;AAAA,QAC7B,EAAE,MAAM,UAAU,MAAM,OAAQ;AAAA,QAChC,EAAE,MAAM,cAAc,MAAM,OAAQ;AAAA,QACpC,EAAE,MAAM,cAAc,MAAM,OAAQ;AAAA,QACpC,EAAE,MAAM,cAAc,MAAM,OAAQ;AAAA,QACpC,EAAE,MAAM,OAAO,MAAM,OAAQ;AAAA,QAC7B,EAAE,MAAM,OAAO,MAAM,OAAQ;AAAA,QAC7B,EAAE,MAAM,eAAe,MAAM,OAAQ;AAAA,QACrC,EAAE,MAAM,WAAW,MAAM,OAAQ;AAAA,QACjC,EAAE,MAAM,aAAa,MAAM,OAAQ;AAAA,QACnC,EAAE,MAAM,SAAS,MAAM,OAAQ;AAAA,MACnC;AAGE,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,kBAAkB,CAAC,SAAS;AAChC,YAAM,WAAW;AAAA,QACf,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,MACb;AAEE,mBAAa,QAAQ,SAAS,IAAI,KAAK;AAAA,IACzC;AAGA,UAAM,oBAAoB,MAAM;AAC9B,UAAI;AACF,cAAM,MAAM,kBAAkB,YAAY,KAAK;AAC/C,cAAM,UAAUC,cAAAA,MAAI,eAAe,GAAG;AACtC,YAAI,SAAS;AACX,wBAAc,QAAQ,KAAK,MAAM,OAAO;AACxCA,0FAAY,WAAW,cAAc,KAAK;AAAA,QAC3C;AAAA,MACF,SAAQ,GAAG;AACVA,0FAAc,YAAY,CAAC;AAAA,MAC5B;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9B,UAAI;AAEF,cAAM,gBAAgB;AAAA,UACpB,MAAM,aAAa;AAAA,UACnB,UAAU,iBAAiB;AAAA,UAC3B,aAAa,oBAAoB;AAAA,UACjC,MAAM,aAAa;AAAA,UACnB,YAAW,oBAAI,KAAM,GAAC,QAAS;AAAA,QACrC;AAGI,sBAAc,MAAM,QAAQ,aAAa;AAGzC,sBAAc,QAAQ,cAAc,MAAM,MAAM,GAAG,CAAC;AAGpD,cAAM,MAAM,kBAAkB,YAAY,KAAK;AAC/CA,sBAAG,MAAC,eAAe,KAAK,KAAK,UAAU,cAAc,KAAK,CAAC;AAAA,MAC5D,SAAQ,GAAG;AACVA,0FAAc,YAAY,CAAC;AAAA,MAC5B;AAAA,IACH;AAGA,UAAM,qBAAqB,CAAC,SAAS;AAEnC,YAAM,kBAAkB;AAAA;AAAA,QAEtB,gBAAgB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC/F,iBAAiB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAChE,UAAU,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzD,gBAAgB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC/D,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,OAAO;AAAA,QAC9D,UAAU,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzD,UAAU,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzD,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACxD,eAAe,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC9D,YAAY,CAAC,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,MAAM;AAAA;AAAA,QAE5D,QAAQ,CAAC,QAAQ,MAAM,MAAM,MAAM,MAAM,QAAQ,QAAQ,IAAI;AAAA,QAC7D,YAAY,CAAC,QAAQ,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM;AAAA,QAC7D,OAAO,CAAC,QAAQ,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM;AAAA,QAC3D,UAAU,CAAC,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,QAC3D,cAAc,CAAC,QAAQ,MAAM,MAAM,MAAM,MAAM,OAAO,IAAI;AAAA,QAC1D,cAAc,CAAC,QAAQ,QAAQ,MAAM,OAAO,MAAM,MAAM,IAAI;AAAA,QAC5D,cAAc,CAAC,QAAQ,MAAM,OAAO,OAAO,MAAM,OAAO,KAAK;AAAA,QAC7D,OAAO,CAAC,QAAQ,OAAO,OAAO,MAAM,QAAQ,MAAM;AAAA,QAClD,OAAO,CAAC,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QAClD,eAAe,CAAC,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QAChE,WAAW,CAAC,QAAQ,OAAO,MAAM,OAAO,MAAM,MAAM,IAAI;AAAA,QACxD,aAAa,CAAC,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,QAAQ,IAAI;AAAA,QAChE,SAAS,CAAC,QAAQ,MAAM,MAAM,MAAM,IAAI;AAAA,MAC5C;AAEE,mBAAa,QAAQ,gBAAgB,IAAI,KAAK,CAAC,MAAM;AAGrD,UAAI,SAAS,OAAO;AAElB,qBAAa,QAAQ,CAAC,QAAQ,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM;AAAA,MAC1E;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAE3BA,oBAAG,MAAC,aAAY;AAEhB,mBAAa,QAAQ;AACrB,qBAAe,QAAQ;AACvB;AAGA,UAAI,iBAAiB,OAAO;AAC1B;MACD;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,aAAa;AAEnCA,oBAAG,MAAC,aAAY;AAGhB,uBAAiB,QAAQ;AAGzB,UAAI,aAAa,QAAQ;AACvB,4BAAoB,QAAQ;AAC5B,wBAAgB,QAAQ;AACxB,8BAAsB,QAAQ;AAAA,MAClC,OAAS;AAEL,8BAAsB,QAAQ;AAG9B,YAAI,YAAY,UAAU,SAAS,mBAAmB,UAAU,OAAO;AACrE,gCAAsB,QAAQ;AAAA,QAC/B,WAEQ,gBAAgB,MAAM,SAAS,GAAG;AACzC,gCAAsB,QAAQ;AAG9BC,wBAAAA,WAAS,MAAM;AACb,kBAAM,QAAQD,cAAAA,MAAI,oBAAqB,EAAC,GAAG,IAAI;AAC/C,kBAAM,OAAO,iBAAiB,EAAE,mBAAmB,eAAa;AAC9D,kBAAI,WAAW;AAEb,oBAAI,iBAAiB,OAAO;AAC1B,wBAAM,OAAO,mBAAmB,EAAE,mBAAmB,aAAW;AAC9D,wBAAI,SAAS;AACX,6CAAuB,QAAQ,QAAQ,SAAS,QAAQ;AAAA,oBAC1E,OAAuB;AACL,6CAAuB,QAAQ,UAAU,SAAS,UAAU;AAAA,oBAC7D;AAAA,kBACjB,CAAe,EAAE,KAAI;AAAA,gBACrB,OAAmB;AACL,yCAAuB,QAAQ,UAAU,SAAS,UAAU;AAAA,gBAC7D;AAAA,cACF;AAAA,YACX,CAAS,EAAE,KAAI;AAAA,UACf,CAAO;AAAA,QACP,OAAW;AACL,gCAAsB,QAAQ;AAAA,QAC/B;AAAA,MACF;AAGD;AAGA,UAAI,iBAAiB,OAAO;AAC1B;MACD;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,gBAAgB;AAEzCA,oBAAG,MAAC,aAAY;AAGhB,0BAAoB,QAAQ;AAG5B,4BAAsB,QAAQ;AAG9B;AAGA,UAAI,iBAAiB,OAAO;AAC1B;MACD;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAE3BA,oBAAG,MAAC,aAAY;AAEhB,mBAAa,QAAQ;AACrB,qBAAe,QAAQ;AACvB;AAGA,UAAI,iBAAiB,OAAO;AAC1B;MACD;AAAA,IACH;AAGA,UAAM,wBAAwB,CAAC,aAAa;AAE1C,YAAM,iBAAiB;AAAA;AAAA,QAErB,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC/C,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC/C,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC/C,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,SAAS,OAAO;AAAA,QACjD,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC/C,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC/C,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC/C,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAC/C,QAAQ,CAAC,QAAQ,QAAQ,SAAS,QAAQ,MAAM;AAAA;AAAA,QAEhD,YAAY,CAAE;AAAA,QACd,OAAO,CAAE;AAAA,QACT,eAAe,CAAE;AAAA,QACjB,cAAc,CAAE;AAAA,QAChB,cAAc,CAAE;AAAA,QAChB,cAAc,CAAE;AAAA,QAChB,OAAO,CAAE;AAAA,QACT,OAAO,CAAE;AAAA,QACT,WAAW,CAAE;AAAA,QACb,aAAa,CAAE;AAAA,QACf,SAAS,CAAE;AAAA,MACf;AAGE,UAAI,iBAAiB,MAAM,SAAS,YAAY,KAAK,GAAG;AAEtD,wBAAgB,QAAQ,eAAe,QAAQ,KAAK,CAAA;AAAA,MACxD,OAAS;AAEL,cAAM,qBAAqB,eAAe,YAAY,KAAK,KAAK,CAAA;AAChE,wBAAgB,QAAQ,mBAAmB,QAAQ,KAAK,CAAA;AAAA,MACzD;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AACvB,4BAAsB,QAAQ;AAC9B,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,kBAAkB,MAAM;AAE5BA,oBAAAA,MAAI,aAAa,EAAC,MAAM,SAAQ,CAAC;AAEjC,mBAAa,QAAQ;AACrB,uBAAiB,QAAQ;AACzB,0BAAoB,QAAQ;AAC5B,mBAAa,QAAQ;AAGrB;IACF;AAGA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB;IACF;AAGA,UAAM,mBAAmB,MAAM;AAC7B,0BAAoB,QAAQ;AAC5B;IACF;AAGA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB;IACF;AAGA,UAAM,qBAAqB,MAAM;AAC/B,WAAK,QAAQ;AACb,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAChB;IACF;AAGA,UAAM,kBAAkB,MAAM;AAC5B,UAAI,QAAQ;AAAO;AAEnB,cAAQ,QAAQ;AAGhB,YAAM,SAAS;AAAA,QACb,MAAM,KAAK;AAAA,QACX,UAAU;AAAA,QACV,UAAU,iBAAiB,UAAU,SAAS,KAAK,iBAAiB;AAAA,QACpE,aAAa,oBAAoB;AAAA,QACjC,MAAM,aAAa,UAAU,SAAS,KAAK,aAAa;AAAA,QACxD,MAAM,aAAa,UAAU,SAAS,KAAK,aAAa;AAAA,QACxD,SAAS,cAAc;AAAA,MAC3B;AAGE,YAAM,cAAc;AAAA,QAClB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,MACb;AAGE,UAAI,YAAY,YAAY,KAAK,KAAK,YAAY,mBAAmB,KAAK,GAAG;AAE3E,eAAO,WAAW,YAAY,mBAAmB,KAAK,KAAK,YAAY,YAAY,KAAK;AACxFA,4BAAA,MAAA,OAAA,+CAAY,aAAa,OAAO,QAAQ,EAAE;AAAA,MAC3C;AAEDA,sFAAY,aAAa,MAAM;AAG/B,MAAO,uBAAiB,KAAK,YAAU;AACrC,cAAM,EAAE,WAAY,IAAG;AAGvB,YAAI,CAAC,cAAc,CAAC,WAAW,kBAAkB;AAC/CA,wBAAA,MAAA,MAAA,SAAA,+CAAc,WAAW,UAAU;AACnC,kBAAQ,QAAQ;AAChB,qBAAW,QAAQ;AACnB;AAAA,QACD;AAGD,gBAAQ,QAAS,EACd,KAAK,MAAM,WAAW,iBAAiB,MAAM,CAAC,EAC9C,KAAK,SAAO;AACX,cAAI,KAAK,UAAU,GAAG;AACpB,wBAAY,QAAQ,IAAI,QAAQ,CAAA;AAAA,UAC1C,OAAe;AACL,wBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAI,IAAI,QAAQ,CAAA;UAC5D;AAED,kBAAQ,QAAQ,IAAI;AACpB,kBAAQ,QAAQ;AAChB,qBAAW,QAAQ;AAEnBA,0FAAY,aAAa,YAAY,MAAM,MAAM;AAAA,QACzD,CAAO,EACA,MAAM,SAAO;AACZA,wBAAA,MAAA,MAAA,SAAA,+CAAc,YAAY,GAAG;AAC7B,kBAAQ,QAAQ;AAChB,qBAAW,QAAQ;AAAA,QAC3B,CAAO;AAAA,MACP,CAAG,EAAE,MAAM,SAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,+CAAc,aAAa,GAAG;AAC9B,gBAAQ,QAAQ;AAChB,mBAAW,QAAQ;AAAA,MACvB,CAAG;AAAA,IACH;AAGA,UAAM,YAAY,MAAM;AACtB,iBAAW,QAAQ;AACnB;AAGAA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,QAAQ,OAAO;AACjB,aAAK;AACL;MACD;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,SAAS;AAEjC,UAAI,MAAM,iCAAiC,KAAK,EAAE;AAElD,YAAM,gBAAgB;AAAA,QACpB,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,aAAa;AAAA,QACb,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,QACT,eAAe;AAAA,QACf,YAAY;AAAA,MAChB;AAGE,UAAI,cAAc,YAAY,KAAK,GAAG;AACpC,cAAM,kBAAkB,cAAc,YAAY,KAAK,CAAC,OAAO,KAAK,EAAE;AAAA,MACvE;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACJ,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAE9BA,oBAAG,MAAC,aAAY;AAGhB,YAAM,iBAAiB;AAAA,QACrB,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,MACb;AAEE,YAAM,cAAc,eAAe,YAAY,KAAK,KAAK;AACzDA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,8BAA8B,WAAW,SAAS,mBAAmB,aAAa,KAAK,CAAC;AAAA,MACjG,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAElCA,oBAAG,MAAC,aAAY;AAGhB,yBAAmB,QAAQ,KAAK;AAChC,kBAAY,QAAQ,KAAK;AAGzB,sBAAgB,KAAK,IAAI;AAGzB,uBAAiB,QAAQ;AACzB,0BAAoB,QAAQ;AAG5B,yBAAmB,KAAK,IAAI;AAG5B,YAAM,qBAAqB;AAAA,QACzB,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,QACP,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,aAAa;AAAA,MACjB;AAGE,UAAI,mBAAmB,KAAK,IAAI,GAAG;AACjCA,sBAAAA,MAAA,MAAA,OAAA,+CAAY,WAAW,mBAAmB,KAAK,IAAI,CAAC,EAAE;AAAA,MACvD;AAGD;IACF;AAmBA,UAAM,kBAAkB,MAAM;AAC5B,YAAM,QAAQ,cAAc,MAAM,KAAI;AACtC,UAAI,CAAC;AAAO;AAGZ,UAAI,QAAQ,MAAM,MAAM,kBAAkB,EAC5B,OAAO,UAAQ,KAAK,UAAU,CAAC,EAC/B,IAAI,UAAQ,KAAK,MAAM,EACvB,OAAO,UAAQ,IAAI;AAGjC,cAAQ,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAG1B,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,MAAM,MAAM,GAAG,CAAC;AAAA,MACzB;AAGD,4BAAsB,KAAK;AAG3B,wBAAkB,QAAQ;AAG1B,uBAAiB,QAAQ,CAAC,GAAG,KAAK;AAGlC;IACF;AAGA,UAAM,wBAAwB,CAAC,UAAU;AAEvC,UAAI,iBAAiB,UAAU,QAAQ;AACrC,YAAI,CAAC,MAAM,SAAS,iBAAiB,KAAK,GAAG;AAC3C,gBAAM,KAAK,iBAAiB,KAAK;AAAA,QAClC;AAAA,MACF;AAGD,UAAI,oBAAoB,SAAS,CAAC,MAAM,SAAS,oBAAoB,KAAK,GAAG;AAC3E,cAAM,KAAK,oBAAoB,KAAK;AAAA,MACrC;AAGD,UAAI,aAAa,UAAU,UAAU,CAAC,MAAM,SAAS,aAAa,KAAK,GAAG;AACxE,cAAM,KAAK,aAAa,KAAK;AAAA,MAC9B;AAGD,YAAM,iBAAiB;AACvB,iBAAW,WAAW,gBAAgB;AACpC,YAAI,cAAc,MAAM,SAAS,OAAO,KAAK,CAAC,MAAM,SAAS,OAAO,GAAG;AACrE,gBAAM,KAAK,OAAO;AAAA,QACnB;AAAA,MACF;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAE9B,YAAM,mBAAmB;AAAA,QACvB,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAAA,QAC/D,YAAY,CAAC,MAAM,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK;AAAA,QAC/D,OAAO,CAAC,MAAM,MAAM,OAAO,OAAO,OAAO,QAAQ,MAAM,MAAM;AAAA,QAC7D,UAAU,CAAC,MAAM,QAAQ,QAAQ,OAAO,MAAM,MAAM,MAAM,IAAI;AAAA,QAC9D,cAAc,CAAC,MAAM,MAAM,MAAM,MAAM,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACrE,cAAc,CAAC,MAAM,OAAO,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK;AAAA,QACrE,cAAc,CAAC,OAAO,OAAO,SAAS,OAAO,QAAQ,OAAO,KAAK;AAAA,QACjE,OAAO,CAAC,MAAM,MAAM,MAAM,QAAQ,QAAQ,OAAO,QAAQ,IAAI;AAAA,QAC7D,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,IAAI;AAAA,QACvD,eAAe,CAAC,MAAM,MAAM,OAAO,MAAM,MAAM,QAAQ,OAAO,MAAM;AAAA,QACpE,aAAa,CAAC,MAAM,MAAM,OAAO,MAAM,OAAO,QAAQ,MAAM,KAAK;AAAA,MACrE;AAEE,aAAO,iBAAiB,YAAY,KAAK,KAAK,CAAA;AAAA,IAChD;AAGA,UAAM,qBAAqB,MAAM;AAC/B,UAAI,CAAC,cAAc,MAAM,QAAQ;AAC/B;AAAA,MACD;AAGDA,oBAAG,MAAC,aAAY;AAGhB;IACF;AA+BA,UAAM,qBAAqB,MAAM;AAE/B;IACF;AAGA,UAAM,qBAAqB,CAAC,SAAS;AACnC,YAAM,WAAW,cAAc,MAAM,KAAK,UAAQ,KAAK,SAAS,IAAI;AACpE,aAAO,WAAW,SAAS,OAAO;AAAA,IACpC;AAGA,UAAM,mBAAmB,MAAM;AAC7B,yBAAmB,QAAQ;AAC3B,kBAAY,QAAQ;AACpB,sBAAgB,MAAM;AACtB,yBAAmB,MAAM;AACzB;IACF;AAGA,UAAM,gBAAgB,MAAM;AAC1B,uBAAiB,QAAQ;AACzB,0BAAoB,QAAQ;AAC5B;IACF;AAGA,UAAM,mBAAmB,MAAM;AAE7B,qBAAe,QAAQ;AACvB,4BAAsB,QAAQ;AAG9B,qBAAe,QAAQ,CAAC,eAAe;AAGvC,UAAI,eAAe,OAAO;AACxBC,sBAAAA,WAAS,MAAM;AACb,gBAAM,QAAQD,oBAAI;AAClB,gBAAM,OAAO,iBAAiB,EAAE,mBAAmB,mBAAiB;AAClE,gBAAI,eAAe;AAEjB,gCAAkB,QAAQ,cAAc,MAAM,cAAc;AAG5D,oBAAM,OAAO,cAAc,EAAE,mBAAmB,cAAY;AAC1D,oBAAI,UAAU;AACZ,iCAAe,QAAQ,SAAS;AAChC,kCAAgB,QAAQ,SAAS;AAAA,gBAClC;AAAA,cACb,CAAW,EAAE,KAAI;AAAA,YACR;AAAA,UACT,CAAO,EAAE,KAAI;AAAA,QACb,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAE7B,qBAAe,QAAQ;AACvB,4BAAsB,QAAQ;AAG9B,qBAAe,QAAQ,CAAC,eAAe;AAGvC,UAAI,eAAe,OAAO;AACxBC,sBAAAA,WAAS,MAAM;AACb,gBAAM,QAAQD,oBAAI;AAClB,gBAAM,OAAO,iBAAiB,EAAE,mBAAmB,mBAAiB;AAClE,gBAAI,eAAe;AAEjB,gCAAkB,QAAQ,cAAc,MAAM,cAAc;AAG5D,oBAAM,UAAU,cAAc,EAAE,mBAAmB,WAAS;AAC1D,oBAAI,SAAS,MAAM,UAAU,GAAG;AAC9B,wBAAM,WAAW,MAAM,CAAC;AACxB,iCAAe,QAAQ,SAAS;AAChC,kCAAgB,QAAQ,SAAS;AAAA,gBAClC;AAAA,cACb,CAAW,EAAE,KAAI;AAAA,YACR;AAAA,UACT,CAAO,EAAE,KAAI;AAAA,QACb,CAAK;AAAA,MACF;AAAA,IACH;AAGAE,kBAAAA,UAAU,MAAM;;AAEd,YAAM,QAAQ;AACd,YAAMC,QAAO,MAAM,MAAM,SAAS,CAAC;AACnC,YAAM,YAAU,KAAAA,MAAK,UAAL,mBAAY,YAAW,CAAA;AAGvC,YAAM,UAAUH,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAGhC,UAAI,QAAQ,MAAM;AAChB,oBAAY,QAAQ,QAAQ;AAC5B,2BAAmB,QAAQ,QAAQ;AAGnC,YAAI,QAAQ,OAAO;AACjB,uBAAa,QAAQ,mBAAmB,QAAQ,KAAK;AACrDA,2FAAY,YAAY,aAAa,KAAK;AAAA,QAChD,OAAW;AACL,0BAAgB,QAAQ,IAAI;AAAA,QAC7B;AAGD;AAGA,2BAAmB,QAAQ,IAAI;AAG/B,cAAM,qBAAqB;AAAA,UACzB,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,UACP,eAAe;AAAA,UACf,WAAW;AAAA,UACX,aAAa;AAAA,QACnB;AAGI,YAAI,mBAAmB,QAAQ,IAAI,KAAK,CAAC,QAAQ,UAAU;AACzDA,wBAAAA,mEAAY,YAAY,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,QAC3D;AAAA,MACF;AAGD,UAAI,QAAQ,QAAQ;AAClBA,sBAAA,MAAA,MAAA,OAAA,gDAAY,cAAc,QAAQ,MAAM;AACxC,2BAAmB,QAAQ,QAAQ;AAGnC,YAAI,QAAQ,WAAW,YAAY,OAAO;AACxC,sBAAY,QAAQ,QAAQ;AAC5B,6BAAmB,QAAQ,MAAM;AACjC,0BAAgB,QAAQ,MAAM;AAAA,QAC/B;AAAA,MACF;AAGD,UAAI,QAAQ;AAAM,qBAAa,QAAQ,QAAQ;AAC/C,UAAI,QAAQ,UAAU;AACpB,yBAAiB,QAAQ,QAAQ;AAEjC,YAAI,iBAAiB,UAAU,QAAQ;AACrC,gCAAsB,iBAAiB,KAAK;AAAA,QAC7C;AAAA,MACF;AACD,UAAI,QAAQ;AAAa,4BAAoB,QAAQ,QAAQ;AAC7D,UAAI,QAAQ;AAAM,qBAAa,QAAQ,QAAQ;AAG/C,iBAAW,MAAM;AACfA,sBAAG,MAAC,aAAY;AAAA,MACjB,GAAE,GAAG;AAGN;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnpCD,GAAG,WAAW,eAAe;"}