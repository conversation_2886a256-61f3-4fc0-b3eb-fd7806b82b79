<template>
  <view class="driver-profile-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">个人中心</text>
        </view>
        <view class="right-action">
        </view>
      </view>
    </view>
    
    <!-- 司机信息卡片 -->
    <view class="driver-card" :style="{ marginTop: (statusBarHeight + 44) + 'px' }">
      <view class="driver-info-section">
        <view class="driver-avatar-container">
          <image :src="driverInfo.avatar" class="driver-avatar"></image>
          <view class="verified-badge" v-if="driverInfo.isVerified">
            <image src="/static/images/icons/verified.png" class="verified-icon"></image>
          </view>
        </view>
        
        <view class="driver-basic-info">
          <view class="driver-name-wrap">
            <text class="driver-name">{{driverInfo.name}}</text>
            <view class="driver-level">
              <image src="/static/images/icons/crown.png" class="level-icon"></image>
              <text class="level-text">{{driverInfo.level}}</text>
            </view>
          </view>
          
          <view class="driver-id">ID: {{driverInfo.id}}</view>
          
          <view class="driver-stats">
            <view class="stat-item">
              <text class="stat-value">{{driverInfo.tripCount}}</text>
              <text class="stat-label">行程数</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-value">{{driverInfo.serviceYears}}</text>
              <text class="stat-label">服务年限</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-value">{{driverInfo.rating}}</text>
              <text class="stat-label">评分</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="driver-tags">
        <view class="tag-item" v-for="(tag, index) in driverInfo.tags" :key="index">
          <text class="tag-text">{{tag}}</text>
        </view>
      </view>
      
      <view class="driver-intro">
        <text class="intro-title">个人介绍</text>
        <text class="intro-content">{{driverInfo.introduction}}</text>
      </view>
    </view>
    
    <!-- 车辆信息 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title-wrap">
          <image src="/static/images/icons/car.png" class="section-icon"></image>
          <text class="section-title">车辆信息</text>
        </view>
        <view class="section-more" @click="navigateToPage('vehicle-detail')">
          <text class="more-text">查看详情</text>
          <text class="more-icon">&gt;</text>
        </view>
      </view>
      
      <view class="vehicle-info">
        <view class="vehicle-image-container">
          <image :src="driverInfo.vehicle.image" class="vehicle-image"></image>
        </view>
        
        <view class="vehicle-details">
          <text class="vehicle-name">{{driverInfo.vehicle.brand}} {{driverInfo.vehicle.model}}</text>
          <text class="vehicle-plate">{{driverInfo.vehicle.plateNumber}}</text>
          <text class="vehicle-desc">{{driverInfo.vehicle.color}} · {{driverInfo.vehicle.seats}}座</text>
        </view>
      </view>
    </view>
    
    <!-- 服务统计 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title-wrap">
          <image src="/static/images/icons/stats.png" class="section-icon"></image>
          <text class="section-title">服务统计</text>
        </view>
      </view>
      
      <view class="service-stats">
        <view class="stat-row">
          <view class="stat-label">总行程数</view>
          <view class="stat-value-large">{{driverInfo.serviceStats.totalTrips}}</view>
        </view>
        
        <view class="stat-row">
          <view class="stat-label">总服务时长</view>
          <view class="stat-value-large">{{driverInfo.serviceStats.totalHours}}小时</view>
        </view>
        
        <view class="stat-row">
          <view class="stat-label">总行驶里程</view>
          <view class="stat-value-large">{{driverInfo.serviceStats.totalDistance}}公里</view>
        </view>
        
        <view class="stat-row">
          <view class="stat-label">好评率</view>
          <view class="stat-value-large">{{driverInfo.serviceStats.positiveRate}}%</view>
        </view>
      </view>
    </view>
    
    <!-- 服务区域 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title-wrap">
          <image src="/static/images/icons/location.png" class="section-icon"></image>
          <text class="section-title">服务区域</text>
        </view>
      </view>
      
      <view class="service-areas">
        <view class="area-item" v-for="(area, index) in driverInfo.serviceAreas" :key="index">
          <view class="area-icon-wrap">
            <image src="/static/images/icons/area-pin.png" class="area-icon"></image>
          </view>
          <text class="area-name">{{area}}</text>
        </view>
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
    
    <!-- 底部联系按钮 -->
    <view class="contact-bar">
      <button class="contact-btn primary" @click="contactDriver">联系司机</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 状态栏高度
const statusBarHeight = ref(20);

// 司机信息
const driverInfo = ref({
  id: '**********',
  name: '张先生',
  avatar: '/static/images/avatar/user1.png',
  isVerified: true,
  level: '金牌司机',
  tripCount: 386,
  serviceYears: 2.5,
  rating: 4.9,
  tags: ['驾驶平稳', '很准时', '路线熟悉', '热心服务'],
  introduction: '您好，我是一名有着5年驾龄的老司机，熟悉磁县及周边地区的道路情况。我的车辆定期保养，保证舒适安全的乘车体验。欢迎选择我的拼车服务，我会尽力为您提供最好的出行体验。',
  vehicle: {
    brand: '大众',
    model: '帕萨特',
    plateNumber: '冀E·12345',
    color: '白色',
    seats: 5,
    image: '/static/images/vehicles/car1.jpg'
  },
  serviceStats: {
    totalTrips: 386,
    totalHours: 720,
    totalDistance: 15680,
    positiveRate: 98
  },
  serviceAreas: [
    '磁县城区',
    '邯郸市区',
    '磁县-邯郸',
    '磁县-石家庄',
    '磁县-邢台'
  ]
});

// 页面加载时执行
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;
  
  loadDriverInfo();
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 加载司机信息
const loadDriverInfo = () => {
  // 实际应用中应该从API获取数据
  console.log('加载司机信息');
};

// 分享功能已移除

// 导航到页面
const navigateToPage = (page) => {
  if (page === 'vehicle-detail') {
    uni.navigateTo({
      url: '/carpool-package/pages/carpool/my/vehicle-detail'
    });
  }
};

// 联系司机
const contactDriver = () => {
  uni.showModal({
    title: '联系司机',
    content: '是否拨打司机电话？',
    success: (res) => {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: '13812345678',
          fail: () => {
            uni.showToast({
              title: '拨打电话失败',
              icon: 'none'
            });
          }
        });
      }
    }
  });
};

// 暴露页面方法给页面实例
defineExpose({});
</script>

<style lang="scss">
/* 容器样式 */
.driver-profile-container {
  background-color: #F5F5F5;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 司机卡片 */
.driver-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.driver-info-section {
  display: flex;
  margin-bottom: 24rpx;
}

.driver-avatar-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
}

.driver-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2rpx solid #F2F2F7;
}

.verified-badge {
  position: absolute;
  right: -6rpx;
  bottom: -6rpx;
  width: 36rpx;
  height: 36rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.verified-icon {
  width: 28rpx;
  height: 28rpx;
}

.driver-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.driver-name-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.driver-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 12rpx;
}

.driver-level {
  display: flex;
  align-items: center;
  background-color: #FFF9E6;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.level-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.level-text {
  font-size: 22rpx;
  color: #FF9F0A;
}

.driver-id {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}

.driver-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1;
}

.stat-label {
  font-size: 22rpx;
  color: #8E8E93;
  margin-top: 4rpx;
}

.stat-divider {
  width: 1px;
  height: 30rpx;
  background-color: #DDDDDD;
  margin: 0 24rpx;
}

.driver-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.tag-item {
  background-color: #F2F2F7;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.tag-text {
  font-size: 24rpx;
  color: #666666;
}

.driver-intro {
  border-top: 1px solid #F2F2F7;
  padding-top: 20rpx;
}

.intro-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.intro-content {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
}

/* 通用卡片样式 */
.section-card {
  margin: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title-wrap {
  display: flex;
  align-items: center;
}

.section-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.section-more {
  display: flex;
  align-items: center;
}

.more-text {
  font-size: 26rpx;
  color: #8E8E93;
  margin-right: 4rpx;
}

.more-icon {
  font-size: 26rpx;
  color: #8E8E93;
}

/* 车辆信息样式 */
.vehicle-info {
  display: flex;
  align-items: center;
}

.vehicle-image-container {
  width: 180rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.vehicle-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.vehicle-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.vehicle-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6rpx;
}

.vehicle-plate {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 6rpx;
}

.vehicle-desc {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 服务统计样式 */
.service-stats {
  display: flex;
  flex-direction: column;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #F2F2F7;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 28rpx;
  color: #666666;
}

.stat-value-large {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

/* 服务区域样式 */
.service-areas {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.area-item {
  display: flex;
  align-items: center;
  background-color: #F2F2F7;
  padding: 10rpx 16rpx;
  border-radius: 20rpx;
}

.area-icon-wrap {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.area-icon {
  width: 100%;
  height: 100%;
}

.area-name {
  font-size: 26rpx;
  color: #666666;
}

/* 底部联系按钮 */
.contact-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.contact-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
}

.primary {
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  color: #FFFFFF;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}
</style> 