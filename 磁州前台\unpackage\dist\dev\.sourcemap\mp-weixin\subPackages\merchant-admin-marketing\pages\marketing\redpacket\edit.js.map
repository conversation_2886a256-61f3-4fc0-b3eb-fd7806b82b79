{"version": 3, "file": "edit.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/redpacket/edit.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xyZWRwYWNrZXRcZWRpdC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">编辑红包</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 表单容器 -->\r\n    <view class=\"form-container\">\r\n      <!-- 步骤指示器 -->\r\n      <view class=\"step-indicator\">\r\n        <view class=\"step-item\" :class=\"{ active: currentStep >= 1, completed: currentStep > 1 }\">\r\n          <view class=\"step-number\">1</view>\r\n          <text class=\"step-text\">基本信息</text>\r\n        </view>\r\n        <view class=\"step-line\" :class=\"{ active: currentStep > 1 }\"></view>\r\n        <view class=\"step-item\" :class=\"{ active: currentStep >= 2, completed: currentStep > 2 }\">\r\n          <view class=\"step-number\">2</view>\r\n          <text class=\"step-text\">红包设置</text>\r\n        </view>\r\n        <view class=\"step-line\" :class=\"{ active: currentStep > 2 }\"></view>\r\n        <view class=\"step-item\" :class=\"{ active: currentStep >= 3 }\">\r\n          <view class=\"step-number\">3</view>\r\n          <text class=\"step-text\">发放规则</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 步骤1：基本信息 -->\r\n      <view class=\"form-step\" v-if=\"currentStep === 1\">\r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">红包名称</view>\r\n          <input class=\"form-input\" type=\"text\" v-model=\"formData.name\" placeholder=\"请输入红包活动名称\" />\r\n        </view>\r\n        \r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">活动时间</view>\r\n          <view class=\"date-range-picker\" @click=\"showDateRangePicker\">\r\n            <text class=\"date-range-text\">{{formData.startDate}} ~ {{formData.endDate}}</text>\r\n            <view class=\"picker-arrow\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">红包类型</view>\r\n          <view class=\"radio-group\">\r\n            <view \r\n              class=\"radio-item\" \r\n              v-for=\"(type, index) in redpacketTypes\" \r\n              :key=\"index\"\r\n              :class=\"{ active: formData.type === type.value }\"\r\n              @click=\"selectRedpacketType(type.value)\">\r\n              <view class=\"radio-icon\" :class=\"{ active: formData.type === type.value }\"></view>\r\n              <text class=\"radio-text\">{{type.label}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">红包封面</view>\r\n          <view class=\"cover-uploader\" @click=\"uploadCover\">\r\n            <image v-if=\"formData.coverUrl\" class=\"cover-preview\" :src=\"formData.coverUrl\" mode=\"aspectFill\"></image>\r\n            <view v-else class=\"upload-placeholder\">\r\n              <view class=\"upload-icon\">+</view>\r\n              <text class=\"upload-text\">上传封面图</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 步骤2：红包设置 -->\r\n      <view class=\"form-step\" v-if=\"currentStep === 2\">\r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">红包金额类型</view>\r\n          <view class=\"radio-group\">\r\n            <view \r\n              class=\"radio-item\" \r\n              v-for=\"(type, index) in amountTypes\" \r\n              :key=\"index\"\r\n              :class=\"{ active: formData.amountType === type.value }\"\r\n              @click=\"selectAmountType(type.value)\">\r\n              <view class=\"radio-icon\" :class=\"{ active: formData.amountType === type.value }\"></view>\r\n              <text class=\"radio-text\">{{type.label}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-group\" v-if=\"formData.amountType === 'fixed'\">\r\n          <view class=\"form-label\">红包金额 (元)</view>\r\n          <input class=\"form-input\" type=\"digit\" v-model=\"formData.fixedAmount\" placeholder=\"请输入红包金额\" />\r\n        </view>\r\n        \r\n        <view class=\"form-group\" v-if=\"formData.amountType === 'random'\">\r\n          <view class=\"form-label\">最小金额 (元)</view>\r\n          <input class=\"form-input\" type=\"digit\" v-model=\"formData.minAmount\" placeholder=\"请输入最小金额\" />\r\n        </view>\r\n        \r\n        <view class=\"form-group\" v-if=\"formData.amountType === 'random'\">\r\n          <view class=\"form-label\">最大金额 (元)</view>\r\n          <input class=\"form-input\" type=\"digit\" v-model=\"formData.maxAmount\" placeholder=\"请输入最大金额\" />\r\n        </view>\r\n        \r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">红包总数量</view>\r\n          <input class=\"form-input\" type=\"number\" v-model=\"formData.totalCount\" placeholder=\"请输入红包总数量\" />\r\n        </view>\r\n        \r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">单用户领取上限</view>\r\n          <input class=\"form-input\" type=\"number\" v-model=\"formData.userLimit\" placeholder=\"请输入单用户领取上限\" />\r\n        </view>\r\n        \r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">红包有效期</view>\r\n          <view class=\"validity-selector\">\r\n            <view \r\n              class=\"validity-option\" \r\n              v-for=\"(option, index) in validityOptions\" \r\n              :key=\"index\"\r\n              :class=\"{ active: formData.validity === option.value }\"\r\n              @click=\"selectValidity(option.value)\">\r\n              {{option.label}}\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 步骤3：发放规则 -->\r\n      <view class=\"form-step\" v-if=\"currentStep === 3\">\r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">发放对象</view>\r\n          <view class=\"radio-group\">\r\n            <view \r\n              class=\"radio-item\" \r\n              v-for=\"(target, index) in targetOptions\" \r\n              :key=\"index\"\r\n              :class=\"{ active: formData.target === target.value }\"\r\n              @click=\"selectTarget(target.value)\">\r\n              <view class=\"radio-icon\" :class=\"{ active: formData.target === target.value }\"></view>\r\n              <text class=\"radio-text\">{{target.label}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-group\" v-if=\"formData.target === 'member'\">\r\n          <view class=\"form-label\">会员等级</view>\r\n          <view class=\"checkbox-group\">\r\n            <view \r\n              class=\"checkbox-item\" \r\n              v-for=\"(level, index) in memberLevels\" \r\n              :key=\"index\"\r\n              :class=\"{ active: formData.memberLevels.includes(level.value) }\"\r\n              @click=\"toggleMemberLevel(level.value)\">\r\n              <view class=\"checkbox-icon\" :class=\"{ active: formData.memberLevels.includes(level.value) }\"></view>\r\n              <text class=\"checkbox-text\">{{level.label}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">使用门槛</view>\r\n          <view class=\"radio-group\">\r\n            <view \r\n              class=\"radio-item\" \r\n              v-for=\"(threshold, index) in thresholdOptions\" \r\n              :key=\"index\"\r\n              :class=\"{ active: formData.threshold === threshold.value }\"\r\n              @click=\"selectThreshold(threshold.value)\">\r\n              <view class=\"radio-icon\" :class=\"{ active: formData.threshold === threshold.value }\"></view>\r\n              <text class=\"radio-text\">{{threshold.label}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-group\" v-if=\"formData.threshold === 'minimum'\">\r\n          <view class=\"form-label\">最低消费金额 (元)</view>\r\n          <input class=\"form-input\" type=\"digit\" v-model=\"formData.minimumAmount\" placeholder=\"请输入最低消费金额\" />\r\n        </view>\r\n        \r\n        <view class=\"form-group\">\r\n          <view class=\"form-label\">使用说明</view>\r\n          <textarea class=\"form-textarea\" v-model=\"formData.description\" placeholder=\"请输入红包使用说明\"></textarea>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部按钮 -->\r\n      <view class=\"form-actions\">\r\n        <view class=\"action-btn back\" v-if=\"currentStep > 1\" @click=\"prevStep\">上一步</view>\r\n        <view class=\"action-btn next\" v-if=\"currentStep < 3\" @click=\"nextStep\">下一步</view>\r\n        <view class=\"action-btn submit\" v-if=\"currentStep === 3\" @click=\"submitForm\">保存</view>\r\n        <view class=\"action-btn cancel\" @click=\"cancelEdit\">取消</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      currentStep: 1,\r\n      formData: {\r\n        id: 1,\r\n        name: '新用户专享红包',\r\n        startDate: '2023-05-01',\r\n        endDate: '2023-05-31',\r\n        type: 'normal',\r\n        coverUrl: '/static/images/redpacket/cover-placeholder.jpg',\r\n        amountType: 'fixed',\r\n        fixedAmount: '10.00',\r\n        minAmount: '',\r\n        maxAmount: '',\r\n        totalCount: '1000',\r\n        userLimit: '1',\r\n        validity: '7',\r\n        target: 'all',\r\n        memberLevels: [],\r\n        threshold: 'none',\r\n        minimumAmount: '',\r\n        description: '1. 每位用户限领1个红包\\n2. 红包领取后7天内有效\\n3. 红包可在下单时直接抵扣\\n4. 不可与其他优惠同时使用\\n5. 最终解释权归商家所有'\r\n      },\r\n      \r\n      // 红包类型选项\r\n      redpacketTypes: [\r\n        { label: '普通红包', value: 'normal' },\r\n        { label: '裂变红包', value: 'fission' },\r\n        { label: '群发红包', value: 'mass' },\r\n        { label: '红包雨', value: 'rain' }\r\n      ],\r\n      \r\n      // 金额类型选项\r\n      amountTypes: [\r\n        { label: '固定金额', value: 'fixed' },\r\n        { label: '随机金额', value: 'random' }\r\n      ],\r\n      \r\n      // 有效期选项\r\n      validityOptions: [\r\n        { label: '3天', value: '3' },\r\n        { label: '7天', value: '7' },\r\n        { label: '15天', value: '15' },\r\n        { label: '30天', value: '30' }\r\n      ],\r\n      \r\n      // 发放对象选项\r\n      targetOptions: [\r\n        { label: '所有用户', value: 'all' },\r\n        { label: '新用户', value: 'new' },\r\n        { label: '会员用户', value: 'member' }\r\n      ],\r\n      \r\n      // 会员等级选项\r\n      memberLevels: [\r\n        { label: '普通会员', value: 'normal' },\r\n        { label: '银卡会员', value: 'silver' },\r\n        { label: '金卡会员', value: 'gold' },\r\n        { label: '钻石会员', value: 'diamond' }\r\n      ],\r\n      \r\n      // 使用门槛选项\r\n      thresholdOptions: [\r\n        { label: '无门槛', value: 'none' },\r\n        { label: '满额可用', value: 'minimum' }\r\n      ]\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 在实际应用中，这里会根据传入的红包ID从服务器获取红包详情\r\n    if (options && options.id) {\r\n      this.loadRedpacketData(options.id);\r\n    }\r\n  },\r\n  methods: {\r\n    loadRedpacketData(id) {\r\n      // 模拟从服务器获取数据\r\n      // 实际应用中应该发起网络请求获取红包详情\r\n      console.log('加载红包ID:', id);\r\n      // 这里使用的是预设数据，实际应用中应该替换为从服务器获取的数据\r\n    },\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    showHelp() {\r\n      uni.showModal({\r\n        title: '编辑红包帮助',\r\n        content: '在此页面您可以修改红包活动的各项参数，包括基本信息、红包设置和发放规则。',\r\n        showCancel: false\r\n      });\r\n    },\r\n    showDateRangePicker() {\r\n      // 显示日期范围选择器\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    selectRedpacketType(type) {\r\n      this.formData.type = type;\r\n    },\r\n    uploadCover() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        success: (res) => {\r\n          this.formData.coverUrl = res.tempFilePaths[0];\r\n        }\r\n      });\r\n    },\r\n    selectAmountType(type) {\r\n      this.formData.amountType = type;\r\n    },\r\n    selectValidity(validity) {\r\n      this.formData.validity = validity;\r\n    },\r\n    selectTarget(target) {\r\n      this.formData.target = target;\r\n    },\r\n    toggleMemberLevel(level) {\r\n      const index = this.formData.memberLevels.indexOf(level);\r\n      if (index === -1) {\r\n        this.formData.memberLevels.push(level);\r\n      } else {\r\n        this.formData.memberLevels.splice(index, 1);\r\n      }\r\n    },\r\n    selectThreshold(threshold) {\r\n      this.formData.threshold = threshold;\r\n    },\r\n    nextStep() {\r\n      if (this.currentStep < 3) {\r\n        this.currentStep++;\r\n      }\r\n    },\r\n    prevStep() {\r\n      if (this.currentStep > 1) {\r\n        this.currentStep--;\r\n      }\r\n    },\r\n    submitForm() {\r\n      // 表单验证\r\n      if (!this.validateForm()) {\r\n        return;\r\n      }\r\n      \r\n      // 提交表单\r\n      uni.showLoading({\r\n        title: '保存中...'\r\n      });\r\n      \r\n      // 模拟提交\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showModal({\r\n          title: '保存成功',\r\n          content: '红包活动已更新成功',\r\n          showCancel: false,\r\n          success: (res) => {\r\n            if (res.confirm) {\r\n              uni.navigateBack();\r\n            }\r\n          }\r\n        });\r\n      }, 1500);\r\n    },\r\n    cancelEdit() {\r\n      uni.showModal({\r\n        title: '确认取消',\r\n        content: '确定要取消编辑吗？未保存的修改将丢失。',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.navigateBack();\r\n          }\r\n        }\r\n      });\r\n    },\r\n    validateForm() {\r\n      // 简单表单验证\r\n      if (!this.formData.name) {\r\n        uni.showToast({\r\n          title: '请输入红包名称',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (this.formData.amountType === 'fixed' && !this.formData.fixedAmount) {\r\n        uni.showToast({\r\n          title: '请输入红包金额',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (this.formData.amountType === 'random') {\r\n        if (!this.formData.minAmount || !this.formData.maxAmount) {\r\n          uni.showToast({\r\n            title: '请输入红包金额范围',\r\n            icon: 'none'\r\n          });\r\n          return false;\r\n        }\r\n        \r\n        if (parseFloat(this.formData.minAmount) >= parseFloat(this.formData.maxAmount)) {\r\n          uni.showToast({\r\n            title: '最小金额必须小于最大金额',\r\n            icon: 'none'\r\n          });\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      if (!this.formData.totalCount) {\r\n        uni.showToast({\r\n          title: '请输入红包总数量',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (this.formData.target === 'member' && this.formData.memberLevels.length === 0) {\r\n        uni.showToast({\r\n          title: '请选择会员等级',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (this.formData.threshold === 'minimum' && !this.formData.minimumAmount) {\r\n        uni.showToast({\r\n          title: '请输入最低消费金额',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.page-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 表单容器 */\r\n.form-container {\r\n  padding: 15px;\r\n}\r\n\r\n/* 步骤指示器 */\r\n.step-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.step-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.step-number {\r\n  width: 28px;\r\n  height: 28px;\r\n  border-radius: 14px;\r\n  background-color: #eee;\r\n  color: #999;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.step-item.active .step-number {\r\n  background-color: #FF5858;\r\n  color: #fff;\r\n}\r\n\r\n.step-item.completed .step-number {\r\n  background-color: #4ECDC4;\r\n  color: #fff;\r\n}\r\n\r\n.step-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.step-item.active .step-text,\r\n.step-item.completed .step-text {\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.step-line {\r\n  flex: 1;\r\n  height: 1px;\r\n  background-color: #eee;\r\n  margin: 0 10px;\r\n}\r\n\r\n.step-line.active {\r\n  background-color: #4ECDC4;\r\n}\r\n\r\n/* 表单步骤 */\r\n.form-step {\r\n  background-color: #fff;\r\n  border-radius: 12px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-label {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 44px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  padding: 0 12px;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 100px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  padding: 10px 12px;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n/* 日期范围选择器 */\r\n.date-range-picker {\r\n  height: 44px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  padding: 0 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.date-range-text {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.picker-arrow {\r\n  width: 10px;\r\n  height: 10px;\r\n  border-right: 1px solid #999;\r\n  border-bottom: 1px solid #999;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n/* 单选按钮组 */\r\n.radio-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.radio-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.radio-icon {\r\n  width: 18px;\r\n  height: 18px;\r\n  border-radius: 9px;\r\n  border: 1px solid #ddd;\r\n  margin-right: 6px;\r\n  position: relative;\r\n}\r\n\r\n.radio-icon.active {\r\n  border-color: #FF5858;\r\n}\r\n\r\n.radio-icon.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 5px;\r\n  background-color: #FF5858;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n.radio-text {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n/* 封面上传 */\r\n.cover-uploader {\r\n  width: 100%;\r\n  height: 180px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.upload-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 30px;\r\n  color: #ccc;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n.cover-preview {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 有效期选择器 */\r\n.validity-selector {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.validity-option {\r\n  height: 36px;\r\n  padding: 0 15px;\r\n  background-color: #F5F7FA;\r\n  border-radius: 18px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-right: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.validity-option.active {\r\n  background-color: #FF5858;\r\n  color: #fff;\r\n}\r\n\r\n/* 复选框组 */\r\n.checkbox-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.checkbox-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.checkbox-icon {\r\n  width: 18px;\r\n  height: 18px;\r\n  border-radius: 4px;\r\n  border: 1px solid #ddd;\r\n  margin-right: 6px;\r\n  position: relative;\r\n}\r\n\r\n.checkbox-icon.active {\r\n  background-color: #FF5858;\r\n  border-color: #FF5858;\r\n}\r\n\r\n.checkbox-icon.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  width: 10px;\r\n  height: 5px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  top: 40%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%) rotate(-45deg);\r\n}\r\n\r\n.checkbox-text {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n/* 底部按钮 */\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-top: 20px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  height: 44px;\r\n  border-radius: 22px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n}\r\n\r\n.action-btn.back {\r\n  background-color: #F5F7FA;\r\n  color: #666;\r\n  margin-right: 10px;\r\n}\r\n\r\n.action-btn.next,\r\n.action-btn.submit {\r\n  background: linear-gradient(135deg, #FF5858, #FF0000);\r\n  color: #fff;\r\n  margin-left: 10px;\r\n}\r\n\r\n.action-btn.cancel {\r\n  background-color: #F5F7FA;\r\n  color: #666;\r\n  margin-left: 10px;\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/redpacket/edit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA0MA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,UAAU;AAAA,QACR,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,cAAc,CAAE;AAAA,QAChB,WAAW;AAAA,QACX,eAAe;AAAA,QACf,aAAa;AAAA,MACd;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,QAClC,EAAE,OAAO,QAAQ,OAAO,UAAW;AAAA,QACnC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,QAChC,EAAE,OAAO,OAAO,OAAO,OAAO;AAAA,MAC/B;AAAA;AAAA,MAGD,aAAa;AAAA,QACX,EAAE,OAAO,QAAQ,OAAO,QAAS;AAAA,QACjC,EAAE,OAAO,QAAQ,OAAO,SAAS;AAAA,MAClC;AAAA;AAAA,MAGD,iBAAiB;AAAA,QACf,EAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAC3B,EAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAC3B,EAAE,OAAO,OAAO,OAAO,KAAM;AAAA,QAC7B,EAAE,OAAO,OAAO,OAAO,KAAK;AAAA,MAC7B;AAAA;AAAA,MAGD,eAAe;AAAA,QACb,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,OAAO,OAAO,MAAO;AAAA,QAC9B,EAAE,OAAO,QAAQ,OAAO,SAAS;AAAA,MAClC;AAAA;AAAA,MAGD,cAAc;AAAA,QACZ,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,QAClC,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,QAClC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,QAChC,EAAE,OAAO,QAAQ,OAAO,UAAU;AAAA,MACnC;AAAA;AAAA,MAGD,kBAAkB;AAAA,QAChB,EAAE,OAAO,OAAO,OAAO,OAAQ;AAAA,QAC/B,EAAE,OAAO,QAAQ,OAAO,UAAU;AAAA,MACpC;AAAA,IACF;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AAEd,QAAI,WAAW,QAAQ,IAAI;AACzB,WAAK,kBAAkB,QAAQ,EAAE;AAAA,IACnC;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,kBAAkB,IAAI;AAGpBA,oBAAY,MAAA,MAAA,OAAA,kFAAA,WAAW,EAAE;AAAA,IAE1B;AAAA,IACD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IACD,sBAAsB;AAEpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,oBAAoB,MAAM;AACxB,WAAK,SAAS,OAAO;AAAA,IACtB;AAAA,IACD,cAAc;AACZA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAChB,eAAK,SAAS,WAAW,IAAI,cAAc,CAAC;AAAA,QAC9C;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB,MAAM;AACrB,WAAK,SAAS,aAAa;AAAA,IAC5B;AAAA,IACD,eAAe,UAAU;AACvB,WAAK,SAAS,WAAW;AAAA,IAC1B;AAAA,IACD,aAAa,QAAQ;AACnB,WAAK,SAAS,SAAS;AAAA,IACxB;AAAA,IACD,kBAAkB,OAAO;AACvB,YAAM,QAAQ,KAAK,SAAS,aAAa,QAAQ,KAAK;AACtD,UAAI,UAAU,IAAI;AAChB,aAAK,SAAS,aAAa,KAAK,KAAK;AAAA,aAChC;AACL,aAAK,SAAS,aAAa,OAAO,OAAO,CAAC;AAAA,MAC5C;AAAA,IACD;AAAA,IACD,gBAAgB,WAAW;AACzB,WAAK,SAAS,YAAY;AAAA,IAC3B;AAAA,IACD,WAAW;AACT,UAAI,KAAK,cAAc,GAAG;AACxB,aAAK;AAAA,MACP;AAAA,IACD;AAAA,IACD,WAAW;AACT,UAAI,KAAK,cAAc,GAAG;AACxB,aAAK;AAAA,MACP;AAAA,IACD;AAAA,IACD,aAAa;AAEX,UAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,MACF;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfA,4BAAG,MAAC,aAAY;AAAA,YAClB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AAAA,IACD,aAAa;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAG,MAAC,aAAY;AAAA,UAClB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,eAAe;AAEb,UAAI,CAAC,KAAK,SAAS,MAAM;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,eAAe,WAAW,CAAC,KAAK,SAAS,aAAa;AACtEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,eAAe,UAAU;AACzC,YAAI,CAAC,KAAK,SAAS,aAAa,CAAC,KAAK,SAAS,WAAW;AACxDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAEA,YAAI,WAAW,KAAK,SAAS,SAAS,KAAK,WAAW,KAAK,SAAS,SAAS,GAAG;AAC9EA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,SAAS,YAAY;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,WAAW,YAAY,KAAK,SAAS,aAAa,WAAW,GAAG;AAChFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,cAAc,aAAa,CAAC,KAAK,SAAS,eAAe;AACzEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzbA,GAAG,WAAW,eAAe;"}