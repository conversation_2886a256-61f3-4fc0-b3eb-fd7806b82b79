/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.distribution-setting-container {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.section-header {
  margin-bottom: 16px;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}
.section-subtitle {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
}
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}
.switch-label {
  flex: 1;
}
.label-text {
  font-size: 14px;
  color: #333333;
}
.label-desc {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
  display: block;
}
.distribution-content {
  margin-top: 16px;
}
.mode-options {
  display: flex;
  margin-bottom: 20px;
}
.mode-option {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  margin-right: 12px;
  transition: all 0.3s ease;
}
.mode-option:last-child {
  margin-right: 0;
}
.mode-option.active {
  border-color: #6B0FBE;
  background-color: rgba(107, 15, 190, 0.05);
}
.option-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}
.option-icon.percentage {
  background-color: rgba(107, 15, 190, 0.1);
  color: #6B0FBE;
}
.option-icon.fixed {
  background-color: rgba(107, 15, 190, 0.1);
  color: #6B0FBE;
}
.option-content {
  flex: 1;
}
.option-title {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
}
.option-desc {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
}
.commission-levels {
  margin-bottom: 16px;
}
.level-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}
.level-header {
  display: flex;
  align-items: center;
}
.level-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  position: relative;
}
.level-icon.level1 {
  background-color: #FF9500;
}
.level-icon.level1::after {
  content: "1";
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
}
.level-icon.level2 {
  background-color: #34C759;
}
.level-icon.level2::after {
  content: "2";
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
}
.level-icon.level3 {
  background-color: #007AFF;
}
.level-icon.level3::after {
  content: "3";
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
}
.level-name {
  font-size: 14px;
  color: #333333;
}
.level-input-wrap {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 4px;
  padding: 0 12px;
  height: 36px;
}
.level-input {
  width: 80px;
  height: 36px;
  font-size: 14px;
  text-align: right;
}
.input-unit {
  font-size: 14px;
  color: #999999;
  margin-left: 4px;
}
.commission-tips {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  background-color: rgba(107, 15, 190, 0.05);
  border-radius: 8px;
  margin-top: 16px;
}
.tip-icon {
  margin-right: 8px;
  margin-top: 2px;
}
.tip-text {
  font-size: 12px;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}