"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
const homeApi = {
  // 获取轮播图列表 - 从后台管理系统获取
  getBanners: async () => {
    try {
      const response = await utils_request.request.get("/banners/public");
      return response.data || [];
    } catch (error) {
      common_vendor.index.__f__("error", "at api/homeApi.js:16", "获取轮播图失败:", error);
      return [
        { id: 1, image: "/static/images/banner/banner-1.png", url: "", title: "磁州生活网" },
        { id: 2, image: "/static/images/banner/banner-2.png", url: "", title: "本地生活服务" }
      ];
    }
  },
  // 获取首页配置信息
  getHomeConfig: async () => {
    try {
      const response = await utils_request.request.get("/home/<USER>/public");
      return response.data || {};
    } catch (error) {
      common_vendor.index.__f__("error", "at api/homeApi.js:31", "获取首页配置失败:", error);
      return {
        site_title: "磁州生活网",
        site_description: "本地生活服务平台",
        contact_phone: "",
        contact_email: "",
        home_notice: "",
        features: []
      };
    }
  },
  // 获取首页统计数据
  getHomeStats: async () => {
    try {
      const response = await utils_request.request.get("/home/<USER>/public");
      return response.data || {};
    } catch (error) {
      common_vendor.index.__f__("error", "at api/homeApi.js:49", "获取首页统计失败:", error);
      return {
        banner_count: 0,
        active_banner_count: 0,
        total_views: 0,
        today_views: 0
      };
    }
  },
  // 获取服务分类 - 从后台管理系统获取
  getServiceCategories: async () => {
    try {
      const response = await utils_request.request.get("/services/categories/public");
      return response.data || [];
    } catch (error) {
      common_vendor.index.__f__("error", "at api/homeApi.js:65", "获取服务分类失败:", error);
      return [];
    }
  },
  // 获取商家推荐 - 从后台管理系统获取
  getMerchantRecommend: async () => {
    try {
      const response = await utils_request.request.get("/merchants/recommend/public");
      return response.data || [];
    } catch (error) {
      common_vendor.index.__f__("error", "at api/homeApi.js:76", "获取商家推荐失败:", error);
      return [];
    }
  },
  // 获取同城资讯 - 从后台管理系统获取
  getCityNews: async () => {
    try {
      const response = await utils_request.request.get("/news/city/public");
      return response.data || [];
    } catch (error) {
      common_vendor.index.__f__("error", "at api/homeApi.js:87", "获取同城资讯失败:", error);
      return [];
    }
  },
  // 获取特色功能配置
  getFeatureConfig: async () => {
    try {
      const response = await utils_request.request.get("/features/config/public");
      return response.data || [];
    } catch (error) {
      common_vendor.index.__f__("error", "at api/homeApi.js:98", "获取特色功能配置失败:", error);
      return [];
    }
  },
  // 记录首页访问统计
  recordPageView: async () => {
    try {
      await utils_request.request.post("/home/<USER>", {
        timestamp: Date.now(),
        user_agent: navigator.userAgent,
        referrer: document.referrer
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at api/homeApi.js:112", "记录页面访问失败:", error);
    }
  },
  // 获取广告横幅
  getAdBanner: async () => {
    try {
      const response = await utils_request.request.get("/ads/banner/public");
      return response.data || null;
    } catch (error) {
      common_vendor.index.__f__("error", "at api/homeApi.js:122", "获取广告横幅失败:", error);
      return null;
    }
  }
};
exports.homeApi = homeApi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/homeApi.js.map
