"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "bills",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const activeTab = common_vendor.ref(0);
    const tabs = common_vendor.ref([
      { name: "全部", type: "all" },
      { name: "收入", type: "income" },
      { name: "支出", type: "expense" }
    ]);
    const selectedDate = common_vendor.ref((/* @__PURE__ */ new Date()).toISOString().split("T")[0].substr(0, 7));
    const statistics = common_vendor.ref({
      income: 0,
      expense: 0
    });
    const transactions = common_vendor.ref([
      {
        id: "tx001",
        title: "充值",
        date: "2023-11-05",
        time: "14:30",
        fullTime: "2023-11-05 14:30",
        amount: 100,
        type: "income",
        status: "已完成"
      },
      {
        id: "tx002",
        title: "服务支付",
        date: "2023-11-03",
        time: "09:15",
        fullTime: "2023-11-03 09:15",
        amount: 35,
        type: "expense",
        status: "已完成"
      },
      {
        id: "tx003",
        title: "提现",
        date: "2023-11-03",
        time: "16:22",
        fullTime: "2023-11-03 16:22",
        amount: 50,
        type: "expense",
        status: "已完成"
      },
      {
        id: "tx004",
        title: "充值",
        date: "2023-11-01",
        time: "11:05",
        fullTime: "2023-11-01 11:05",
        amount: 100,
        type: "income",
        status: "已完成"
      },
      {
        id: "tx005",
        title: "任务收入",
        date: "2023-10-30",
        time: "18:45",
        fullTime: "2023-10-30 18:45",
        amount: 88,
        type: "income",
        status: "已完成"
      }
    ]);
    const page = common_vendor.ref(1);
    common_vendor.ref(10);
    const hasMoreData = common_vendor.ref(true);
    const filteredTransactions = common_vendor.computed(() => {
      const yearMonth = selectedDate.value;
      let result = transactions.value.filter((item) => item.date.startsWith(yearMonth));
      if (activeTab.value !== 0) {
        const type = tabs.value[activeTab.value].type;
        result = result.filter((item) => item.type === type);
      }
      return result;
    });
    const groupedTransactions = common_vendor.computed(() => {
      const groups = {};
      filteredTransactions.value.forEach((item) => {
        if (!groups[item.date]) {
          groups[item.date] = [];
        }
        groups[item.date].push(item);
      });
      const sortedGroups = {};
      Object.keys(groups).sort((a, b) => new Date(b) - new Date(a)).forEach((key) => {
        sortedGroups[key] = groups[key];
      });
      return sortedGroups;
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const switchTab = (index) => {
      activeTab.value = index;
      calculateStatistics();
    };
    const onDateChange = (e) => {
      selectedDate.value = e.detail.value;
      page.value = 1;
      getTransactions();
    };
    const formatDate = (dateStr) => {
      if (!dateStr)
        return "";
      const [year, month] = dateStr.split("-");
      return `${year}年${month}月`;
    };
    const formatDayDate = (dateStr) => {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      const weekDay = weekDays[date.getDay()];
      return `${month}月${day}日 ${weekDay}`;
    };
    const getTransactions = () => {
      setTimeout(() => {
        if (page.value === 1) {
          calculateStatistics();
        } else {
          if (page.value >= 3) {
            hasMoreData.value = false;
          } else {
            const moreData = [
              {
                id: "tx006",
                title: "服务支付",
                date: "2023-10-22",
                time: "10:30",
                fullTime: "2023-10-22 10:30",
                amount: 25,
                type: "expense",
                status: "已完成"
              },
              {
                id: "tx007",
                title: "充值",
                date: "2023-10-15",
                time: "16:40",
                fullTime: "2023-10-15 16:40",
                amount: 50,
                type: "income",
                status: "已完成"
              }
            ];
            transactions.value = [...transactions.value, ...moreData];
            calculateStatistics();
          }
        }
      }, 500);
    };
    const calculateStatistics = () => {
      let income = 0;
      let expense = 0;
      filteredTransactions.value.forEach((item) => {
        if (item.type === "income") {
          income += item.amount;
        } else if (item.type === "expense") {
          expense += item.amount;
        }
      });
      statistics.value = { income, expense };
    };
    const getDateIncome = (transactions2) => {
      return transactions2.reduce((sum, item) => {
        return item.type === "income" ? sum + item.amount : sum;
      }, 0);
    };
    const getDateExpense = (transactions2) => {
      return transactions2.reduce((sum, item) => {
        return item.type === "expense" ? sum + item.amount : sum;
      }, 0);
    };
    const loadMoreData = () => {
      if (!hasMoreData.value)
        return;
      page.value++;
      getTransactions();
    };
    const getTransactionTypeIcon = (type) => {
      const icons = {
        "income": "/static/images/tabbar/收入.png",
        "expense": "/static/images/tabbar/支出.png"
      };
      return icons[type] || icons["income"];
    };
    const getTransactionTypeClass = (type) => {
      return {
        "income-icon": type === "income",
        "expense-icon": type === "expense"
      };
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      getTransactions();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: activeTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        e: common_vendor.t(formatDate(selectedDate.value)),
        f: common_assets._imports_1$26,
        g: selectedDate.value,
        h: common_vendor.o(onDateChange),
        i: navbarHeight.value + 10 + "px",
        j: common_vendor.t(statistics.value.income.toFixed(2)),
        k: common_vendor.t(statistics.value.expense.toFixed(2)),
        l: filteredTransactions.value.length > 0
      }, filteredTransactions.value.length > 0 ? common_vendor.e({
        m: common_vendor.f(groupedTransactions.value, (group, date, i0) => {
          return common_vendor.e({
            a: common_vendor.t(formatDayDate(date)),
            b: getDateIncome(group) > 0
          }, getDateIncome(group) > 0 ? {
            c: common_vendor.t(getDateIncome(group).toFixed(2))
          } : {}, {
            d: getDateExpense(group) > 0
          }, getDateExpense(group) > 0 ? {
            e: common_vendor.t(getDateExpense(group).toFixed(2))
          } : {}, {
            f: common_vendor.f(group, (item, index, i1) => {
              return {
                a: getTransactionTypeIcon(item.type),
                b: common_vendor.n(getTransactionTypeClass(item.type)),
                c: common_vendor.t(item.title),
                d: common_vendor.t(item.time),
                e: common_vendor.t(item.type === "income" ? "+" : "-"),
                f: common_vendor.t(item.amount.toFixed(2)),
                g: item.type === "income" ? 1 : "",
                h: item.type === "expense" ? 1 : "",
                i: common_vendor.t(item.status),
                j: index
              };
            }),
            g: date
          });
        }),
        n: hasMoreData.value
      }, hasMoreData.value ? {
        o: common_vendor.o(loadMoreData)
      } : {}) : {
        p: common_assets._imports_1$3
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/payment/pages/bills.js.map
