<template>
	<view class="publish-detail" :class="categoryType">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="back-btn" @click="navigateBack">
				<image class="back-icon" src="/static/images/tabbar/arrow-up.png"></image>
			</view>
			<view class="navbar-title">发布{{ serviceTypeName ? serviceTypeName : categoryName }}</view>
			<view class="navbar-right">
				<!-- 删除原来的按钮 -->
			</view>
		</view>
					
		<!-- 免责声明 -->
		<view class="disclaimer">
			<text class="disclaimer-text">免责声明：本平台发布的所有信息展示，内容本身与平台本身无关，平台不负任何责任。</text>
		</view>
					
		<!-- 表单内容区域 -->
		<scroll-view scroll-y class="content-scroll">
			<view class="form-container">
				<!-- 动态渲染表单 -->
				<view class="form-section" v-for="(section, sectionIndex) in formConfig.sections" :key="sectionIndex">
					<view class="form-item" v-if="section.label">
						<view class="section-title">{{ section.label }}</view>
							</view>
					
					<template v-for="(field, fieldIndex) in section.fields" :key="fieldIndex">
						<view class="form-item" v-if="shouldShowField(field)">
							<!-- 输入框字段 -->
							<template v-if="field.type === 'input'">
								<view class="form-label" :class="{ required: field.required }">{{ field.label }}</view>
								<view class="form-input-wrapper">
									<input :type="field.inputType || 'text'" class="form-input" :placeholder="field.placeholder || '请输入'" v-model="formData[field.name]" />
							</view>
							</template>
							
							<!-- 性别选择字段 -->
							<template v-else-if="field.type === 'gender'">
								<view class="form-label" :class="{ required: field.required }">{{ field.label }}</view>
								<view class="gender-selector">
									<view class="gender-option" :class="{'gender-selected': formData[field.name] === '男'}" @click="selectGender(field.name, '男')">
										<text>男</text>
						</view>
									<view class="gender-option" :class="{'gender-selected': formData[field.name] === '女'}" @click="selectGender(field.name, '女')">
										<text>女</text>
					</view>
								</view>
							</template>
							
							<!-- 下拉选择字段 -->
							<template v-else-if="field.type === 'picker'">
								<view class="form-label" :class="{ required: field.required }">{{ field.label }}</view>
								<view class="form-input-wrapper">
									<input type="text" class="form-input" :placeholder="getPickerPlaceholder(field)" v-model="formData[field.name]" />
					</view>
							</template>
							
							<!-- 定位输入字段 -->
							<template v-else-if="field.type === 'location'">
								<view class="form-label" :class="{ required: field.required }">{{ field.label }}</view>
								<view class="form-input-wrapper location-input-wrapper">
									<input class="form-input" type="text" 
										v-model="formData[field.name]"
										:placeholder="field.placeholder || '请输入地址或点击右侧定位'" />
									<view class="location-btn" @click="getLocation(field.name)">
										<image class="location-icon" src="/static/images/tabbar/定位.png"></image>
					</view>
								</view>
							</template>
							
							<!-- 文本区域字段 -->
							<template v-else-if="field.type === 'textarea'">
								<view class="form-label" :class="{ required: field.required }">{{ field.label }}</view>
								<view class="textarea-wrapper">
									<textarea class="form-textarea" :placeholder="field.placeholder || '请输入'" v-model="formData[field.name]"></textarea>
					</view>
							</template>
							
							<!-- 上传图片字段 -->
							<template v-else-if="field.type === 'upload'">
								<view class="form-label" :class="{ required: field.required }">{{ field.label }}</view>
								<view class="upload-wrapper">
									<view class="upload-btn" @click="chooseImage(field.name)">
										<view class="upload-btn-inner">
											<image class="upload-icon" src="/static/images/tabbar/上传.png"></image>
											<text class="upload-text">上传照片</text>
							</view>
							</view>
									<view class="upload-preview" v-for="(item, index) in formData[field.name] || []" :key="index">
										<image class="preview-image" :src="item" mode="aspectFill"></image>
										<view class="delete-btn" @click="deleteImage(field.name, index)">
											<text class="delete-icon">×</text>
							</view>
						</view>
					</view>
								<text class="upload-tip" v-if="field.tip">{{ field.tip }}</text>
							</template>
							
							<!-- 标签选择字段 -->
							<template v-else-if="field.type === 'tags'">
								<view class="tag-section">
									<view class="tag-title">{{ field.label }}（最多选择{{ field.maxCount || 3 }}个）</view>
									<view class="tag-list">
										<view class="tag-item" 
											v-for="(tag, tagIndex) in field.options" 
											:key="tagIndex" 
											:class="{ 'tag-active': isTagSelected(tag, field.name) }" 
											@click="toggleTag(tag, field.name, field.maxCount || 3)">
											{{ tag }}
				</view>
							</view>
							</view>
							</template>
							
							<!-- 验证码字段 -->
							<template v-else-if="field.type === 'code'">
								<view class="form-label" :class="{ required: field.required }">{{ field.label }}</view>
								<view class="form-input-wrapper code-input-wrapper">
									<input type="number" class="form-input code-input" placeholder="请输入验证码" v-model="formData[field.name]" />
									<view class="code-btn">获取验证码</view>
						</view>
							</template>
					</view>
					</template>
				</view>
				
				<!-- 红包功能区域 -->
				<view class="form-section">
					<view class="form-item">
						<view class="section-title">红包设置</view>
					</view>
					
					<view class="form-item">
						<view class="form-label">是否开启红包</view>
						<switch 
							:checked="formData.redPacketEnabled || false" 
							color="#FF4D4F" 
							@change="(e) => { formData.redPacketEnabled = e.detail.value }"
							class="switch-control"
						/>
					</view>
					
					<view class="form-item" v-if="formData.redPacketEnabled">
						<view class="form-label required">红包金额</view>
						<view class="form-input-wrapper">
							<input 
								type="digit" 
								v-model="formData.redPacketAmount" 
								placeholder="请输入红包金额" 
								class="form-input"
							/>
							<text class="input-unit">元</text>
						</view>
					</view>
					
					<view class="form-item" v-if="formData.redPacketEnabled">
						<view class="form-label required">红包个数</view>
						<view class="form-input-wrapper">
							<input 
								type="number" 
								v-model="formData.redPacketCount" 
								placeholder="请输入红包个数" 
								class="form-input"
							/>
							<text class="input-unit">个</text>
						</view>
					</view>
					
					<view class="form-item" v-if="formData.redPacketEnabled">
						<view class="form-label">红包类型</view>
						<view class="radio-group">
							<view class="radio-item" @click="formData.redPacketType = 'average'">
								<view class="radio-circle" :class="{ active: formData.redPacketType === 'average' }"></view>
								<text class="radio-text">平均分配</text>
							</view>
							<view class="radio-item" @click="formData.redPacketType = 'random'">
								<view class="radio-circle" :class="{ active: formData.redPacketType === 'random' }"></view>
								<text class="radio-text">随机金额</text>
							</view>
						</view>
					</view>
					
					<view class="form-item" v-if="formData.redPacketEnabled">
						<view class="form-label">领取条件</view>
						<view class="checkbox-group">
							<view class="checkbox-item" @click="toggleCondition('share')">
								<view class="checkbox-square" :class="{ active: formData.redPacketConditions?.includes('share') }"></view>
								<text class="checkbox-text">转发分享</text>
							</view>
							<view class="checkbox-item" @click="toggleCondition('follow')">
								<view class="checkbox-square" :class="{ active: formData.redPacketConditions?.includes('follow') }"></view>
								<text class="checkbox-text">关注店铺</text>
							</view>
						</view>
					</view>
					
					<view class="form-item" v-if="formData.redPacketEnabled">
						<view class="form-label">有效期</view>
						<view class="form-input-wrapper" @click="showRedPacketDatePicker = true">
						<view class="date-picker-text">{{ formData.redPacketExpiry || '请选择红包有效期' }}</view>
							<image src="/static/images/tabbar/日历.png" class="date-icon"></image>
						</view>
					</view>
					
					<view class="form-item" v-if="formData.redPacketEnabled">
						<view class="form-label">红包说明</view>
						<view class="form-input-wrapper">
							<textarea 
								v-model="formData.redPacketDesc" 
								placeholder="请输入红包说明，如参与方式、注意事项等" 
								class="form-textarea"
							/>
						</view>
					</view>
					
					<!-- 添加红包提示词 -->
					<view class="redpacket-tips" v-if="!formData.redPacketEnabled">
						<image src="/static/images/tabbar/提示.png" class="tips-icon"></image>
						<text class="tips-text">添加红包可提高信息曝光率，让更多人看到您的信息，转发分享后可抢红包，提高信息传播效果！</text>
					</view>
					
					<!-- 红包预览 -->
					<view class="red-packet-preview" v-if="formData.redPacketEnabled && formData.redPacketAmount && formData.redPacketCount">
						<view class="red-packet-preview-container">
							<image class="red-packet-preview-bg" src="/static/images/tabbar/红包背景.png" mode="aspectFill"></image>
							<view class="red-packet-preview-content">
								<view class="red-packet-preview-title">
									<image class="red-packet-preview-icon" src="/static/images/tabbar/红包图标.png"></image>
									<text>红包福利</text>
								</view>
								<view class="red-packet-preview-info">
									<text class="red-packet-preview-amount">¥{{ formData.redPacketAmount }}</text>
									<text class="red-packet-preview-desc">{{ formData.redPacketType === 'average' ? '平均分配' : '随机金额' }} · {{ formData.redPacketCount }}个红包</text>
								</view>
								<view class="red-packet-preview-conditions" v-if="formData.redPacketConditions && formData.redPacketConditions.length > 0">
									<text class="red-packet-preview-condition-label">领取条件：</text>
									<text class="red-packet-preview-condition-item" v-if="formData.redPacketConditions.includes('share')">转发分享</text>
									<text class="red-packet-preview-condition-item" v-if="formData.redPacketConditions.includes('follow')">关注店铺</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 广告位 -->
				<view class="ad-section">
					<view class="publish-method-title">选择发布方式</view>
					<view class="ad-item free-ad" @click="watchAdToPublish">
						<view class="ad-icon-wrap">
							<image class="ad-icon" src="/static/images/tabbar/广告.png"></image>
						</view>
						<view class="ad-content">
							<text class="ad-title">看广告发布</text>
							<text class="ad-desc">看一个广告免费发布一天</text>
						</view>
						<view class="ad-btn free-btn">免费</view>
					</view>
				
					<view class="ad-item pay-ad" @click="togglePayOptions">
						<view class="ad-icon-wrap">
							<image class="ad-icon" src="/static/images/tabbar/优惠券.png"></image>
		</view>
						<view class="ad-content">
							<text class="ad-title">付费发布</text>
							<text class="ad-desc">3天/1周/1个月任选</text>
						</view>
						<view class="ad-btn pay-btn">付费</view>
					</view>
					
					<!-- 付费选项下拉框 -->
					<view class="pay-options" v-if="showPayOptions">
						<view class="pay-options-title">选择发布时长</view>
						<view class="pay-options-list">
							<view class="pay-option-item" @click="selectPublishDuration('3天', 2.8)">
								<text class="pay-option-label">3天</text>
								<text class="pay-option-price">¥2.8</text>
							</view>
							<view class="pay-option-item" @click="selectPublishDuration('1周', 5.8)">
								<text class="pay-option-label">1周</text>
								<text class="pay-option-price">¥5.8</text>
							</view>
							<view class="pay-option-item" @click="selectPublishDuration('1个月', 19.8)">
								<text class="pay-option-label">1个月</text>
								<text class="pay-option-price">¥19.8</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 协议 -->
				<view class="agreement-section">
					<checkbox-group @change="e => formData.agree = e.detail.value.length > 0">
						<label class="agreement-row">
							<checkbox value="agree" :checked="formData.agree" />
							<text class="agreement-text">我已阅读并同意</text>
							<text class="agreement-link" @click.stop="showAgreement('service')">《服务协议》</text>
							<text class="agreement-text">和</text>
							<text class="agreement-link" @click.stop="showAgreement('privacy')">《隐私政策》</text>
						</label>
					</checkbox-group>
				</view>
				
				<!-- 发布按钮 -->
				<view class="publish-btn-wrapper">
					<button class="publish-btn" :disabled="!formData.agree" @click="submitForm">确认发布</button>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import * as serviceFormConfig from '../../utils/service-form-config.js';
	
	// 在import区域添加内容审核工具
	import { checkContent } from '@/utils/contentCheck.js';
	import RedPacketEntry from '@/components/RedPacket/RedPacketEntry.vue';
	
	export default {
		components: {
			RedPacketEntry
		},
		data() {
			return {
				categoryType: '',
				categoryName: '',
				formConfig: {}, // 表单配置
				
				formData: {
				name: '',
				gender: '',
				age: '',
				workArea: '',
				education: '',
				experience: '',
				jobPosition: '',
				salary: '',
				introduction: '',
				images: [],
				tags: [],
				contact: '',
				phone: '',
				code: '',
				wechat: '',
				petType: '', // 添加宠物类型字段
				petBreed: '', // 添加宠物品种字段
				purpose: '', // 添加发布目的字段
				petGender: '', // 添加宠物性别字段
				petAge: '',   // 添加宠物年龄字段
				petPrice: '', // 添加宠物价格字段
				petVaccine: '', // 添加宠物疫苗情况字段
				petDescription: '', // 添加宠物描述字段
				petFeatures: [], // 添加宠物特点字段
				// 招聘信息相关字段
				companyName: '',
				workAddress: '',
				jobType: '',
				education: '',
				experience: '',
				salary: '',
				count: '',
				jobDescription: '',
				welfare: [],
				agree: false,
				redPacket: {
					enabled: false,
					amount: 0,
					totalCount: 0,
					remainCount: 0,
					desc: ''
				},
				// 红包相关字段
				redPacketEnabled: false,
				redPacketAmount: '',
				redPacketCount: '',
				redPacketType: 'random',
				redPacketConditions: [],
				redPacketExpiry: '',
				redPacketDesc: ''
			},
				showPayOptions: false, // 控制付费选项的显示
				selectedDuration: '', // 存储选中的发布时长
				publishPrice: 0,      // 存储选中的价格
				isPaidPublish: false, // 是否选择了付费发布
				adStartTime: null, // 广告开始时间
				serviceType: '', // 添加服务类型字段
				serviceTypeName: '', // 添加服务类型名称字段
				showRedPacketDatePicker: false // 控制红包日期选择器的显示
			}
		},
		onLoad(options) {
			console.log('接收到的参数:', options);
			// 获取URL参数
			if (options.type) {
				this.categoryType = options.type;
			}
			
			if (options.name) {
				this.categoryName = decodeURIComponent(options.name);
			}
			
			// 接收服务类型参数
			if (options.serviceType && options.serviceTypeName) {
				this.serviceType = options.serviceType;
				this.serviceTypeName = decodeURIComponent(options.serviceTypeName);
				console.log('服务类型:', this.serviceTypeName);
			}
			
			// 根据类目类型初始化表单配置
			this.initFormConfig();
			
			// 初始化必要的表单数据，避免未定义错误
			this.formData.images = [];
			this.formData.tags = [];
			this.formData.agree = false;
		},
		methods: {
			navigateBack() {
				uni.navigateBack();
			},
			
			togglePayOptions() {
				this.showPayOptions = !this.showPayOptions;
			},
			
			selectGender(fieldName = 'gender', value) {
				this.formData[fieldName] = value;
			},
			
			// 初始化表单配置
			initFormConfig() {
				// 根据类目类型设置表单配置
				switch(this.categoryType) {
					case 'home_service': // 到家服务
						// 如果有具体的服务类型，加载不同的表单
						if (this.serviceType) {
							switch(this.serviceType) {
								case 'home_cleaning':
									this.formConfig = serviceFormConfig.homeCleaningForm;
									break;
								case 'repair':
									this.formConfig = serviceFormConfig.repairForm;
									break;
								case 'installation':
									this.formConfig = serviceFormConfig.installationForm;
									break;
								case 'locksmith':
									this.formConfig = serviceFormConfig.locksmithForm;
									break;
								case 'moving':
									this.formConfig = serviceFormConfig.movingForm;
									break;
								case 'beauty':
									this.formConfig = serviceFormConfig.beautyForm;
									break;
								case 'tutor':
									this.formConfig = serviceFormConfig.tutorForm;
									break;
								case 'pet_service':
									this.formConfig = serviceFormConfig.petServiceForm;
									break;
								case 'plumbing':
									this.formConfig = serviceFormConfig.plumbingForm;
									break;
								default:
									this.formConfig = serviceFormConfig.otherHomeServiceForm;
									break;
							}
						} else {
							// 如果没有具体服务类型，默认一个表单
							this.formConfig = serviceFormConfig.otherHomeServiceForm;
						}
						break;
					case 'find_service': // 寻找服务
						this.formConfig = serviceFormConfig.findServiceForm;
						break;
					case 'hire': // 招聘信息
						this.formConfig = serviceFormConfig.recruitmentForm;
						break;
					case 'job_wanted': // 求职信息
						this.formConfig = serviceFormConfig.jobWantedForm;
						break;
					case 'house_rent': // 房屋出租
						this.formConfig = serviceFormConfig.houseRentForm;
						break;
					case 'house_sell': // 房屋出售
						this.formConfig = serviceFormConfig.houseSellForm;
						break;
					case 'used_car': // 二手车辆
						this.formConfig = serviceFormConfig.usedCarForm;
						break;
					case 'pet': // 宠物信息
						this.formConfig = serviceFormConfig.petForm;
						break;
					case 'merchant_activity': // 商家活动
						this.formConfig = serviceFormConfig.merchantActivityForm;
						break;
					case 'car_service': // 车辆服务
						this.formConfig = serviceFormConfig.carServiceForm;
						break;
					case 'second_hand': // 二手闲置
						this.formConfig = serviceFormConfig.secondHandForm;
						break;
					case 'carpool': // 磁州拼车
						this.formConfig = serviceFormConfig.carpoolForm;
						break;
					case 'business_transfer': // 生意转让
						this.formConfig = serviceFormConfig.businessTransferForm;
						break;
					case 'education': // 教育培训
						this.formConfig = serviceFormConfig.educationForm;
						break;
					case 'dating': // 婚恋交友
						this.formConfig = serviceFormConfig.datingForm;
						break;
					default: // 默认表单
						this.formConfig = serviceFormConfig.defaultForm;
						break;
				}
			},
			
			getPickerPlaceholder(field) {
				if (this.formData[field.name]) {
					return this.formData[field.name];
				}
				return field.placeholder || '请选择';
			},
			
			shouldShowField(field) {
				if (!field.showIf) {
					return true;
				}
				const condition = field.showIf;
				return this.formData[condition.field] === condition.value;
			},
			
			getLocation(fieldName) {
				uni.chooseLocation({
					success: (res) => {
						if (res.address) {
							this.formData[fieldName] = res.address;
						}
					}
				});
			},
			
			// 上传图片
			chooseImage(fieldName) {
				const fieldConfig = this.formConfig.sections
					.flatMap(section => section.fields)
					.find(field => field.name === fieldName);
				
				const maxCount = fieldConfig ? fieldConfig.maxCount : 9;
				const currentCount = this.formData[fieldName] ? this.formData[fieldName].length : 0;
				
				if (currentCount >= maxCount) {
					uni.showToast({
						title: `最多上传${maxCount}张图片`,
						icon: 'none'
					});
					return;
				}
				
				uni.chooseImage({
					count: maxCount - currentCount,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						if (!this.formData[fieldName]) {
							this.formData[fieldName] = [];
						}
						this.formData[fieldName].push(...res.tempFilePaths);
					}
				});
			},
			
			// 删除图片
			deleteImage(fieldName, index) {
				this.formData[fieldName].splice(index, 1);
			},
			
			// 判断标签是否选中
			isTagSelected(tag, fieldName) {
				return this.formData[fieldName] && this.formData[fieldName].includes(tag);
			},
			
			// 切换标签
			toggleTag(tag, fieldName, maxCount) {
				if (!this.formData[fieldName]) {
					this.formData[fieldName] = [];
				}
				
				const index = this.formData[fieldName].indexOf(tag);
				
				if (index > -1) {
					// 已选中，取消选中
					this.formData[fieldName].splice(index, 1);
				} else {
					// 未选中，添加
					if (this.formData[fieldName].length < (maxCount || 3)) {
						this.formData[fieldName].push(tag);
					} else {
						uni.showToast({
							title: `最多选择${maxCount || 3}个标签`,
							icon: 'none'
						});
					}
				}
			},
			
			toggleCondition(condition) {
				if (!this.formData.redPacketConditions) {
					this.formData.redPacketConditions = [];
				}
				
				const index = this.formData.redPacketConditions.indexOf(condition);
				
				if (index > -1) {
					this.formData.redPacketConditions.splice(index, 1);
				} else {
					this.formData.redPacketConditions.push(condition);
				}
			},
			
			// 显示协议
			showAgreement(type) {
				let url = '/pages/agreement/';
				if (type === 'service') {
					url += 'service';
				} else {
					url += 'privacy';
				}
				uni.navigateTo({ url });
			},
			
			// 看广告发布
			watchAdToPublish() {
				// 记录广告开始时间
				this.adStartTime = new Date();
				
				// 这里需要集成广告SDK
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
				
				// 模拟看完广告
				setTimeout(() => {
					this.submitForm(true);
				}, 2000);
			},
			
			// 选择付费发布
			selectPublishDuration(duration, price) {
				this.selectedDuration = duration;
				this.publishPrice = price;
				this.isPaidPublish = true;
				
				// 提交表单
				this.submitForm();
			},
			
			// 提交表单
			async submitForm(isFree = false) {
				// 验证表单
				if (!this.formData.agree) {
					uni.showToast({
						title: '请先阅读并同意协议',
						icon: 'none'
					});
					return;
				}
				
				// 验证表单字段
				for (const section of this.formConfig.sections) {
					for (const field of section.fields) {
						if (field.required && !this.formData[field.name]) {
							uni.showToast({
								title: `${field.label}不能为空`,
								icon: 'none'
							});
							return;
						}
						
						// 内容安全审核
						if (field.checkContent && this.formData[field.name]) {
							const checkResult = await checkContent(this.formData[field.name], 'text');
							if (!checkResult) {
								uni.showToast({
									title: `${field.label}包含违规内容`,
									icon: 'none'
								});
								return;
							}
						}
					}
				}
				
				// 显示加载中
				uni.showLoading({
					title: '发布中...',
					mask: true
				});
				
				// 模拟异步操作
				setTimeout(() => {
					uni.hideLoading();
					
					// 准备提交数据
					const submission = {
						...this.formData,
						categoryType: this.categoryType,
						categoryName: this.categoryName,
						serviceType: this.serviceType,
						serviceTypeName: this.serviceTypeName,
						id: Date.now().toString(),
						createTime: new Date().toISOString()
					};
					
					// 存储到本地
					let allData = uni.getStorageSync('publishedData') || [];
					allData.push(submission);
					uni.setStorageSync('publishedData', allData);
					
					// 跳转到成功页面
					uni.redirectTo({
						url: `/pages/publish/success?id=${submission.id}`
					});
					
				}, 1500);
			}
		}
	}
</script>

<style lang="scss">
	.publish-detail {
		min-height: 100vh;
		background-color: #F8F9FD;
		display: flex;
		flex-direction: column;
		position: relative;
		padding-top: calc(88rpx + var(--status-bar-height, 20px));
	}
	
	.custom-navbar {
		background: #FFFFFF;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		padding-top: var(--status-bar-height, 20px);
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-icon {
		width: 20rpx;
		height: 20rpx;
		border-top: 4rpx solid #333;
		border-left: 4rpx solid #333;
		transform: rotate(-45deg);
	}
	
	.navbar-title {
		font-size: 34rpx;
		font-weight: 600;
		color: #333;
	}
	
	.navbar-right {
		width: 60rpx;
	}
	
	.disclaimer {
		padding: 20rpx 30rpx;
		background-color: #FFF7E6;
		color: #FA8C16;
		font-size: 24rpx;
		line-height: 1.5;
	}
	
	.content-scroll {
		flex: 1;
	}
	
	.form-container {
		padding: 30rpx;
		background-color: #FFFFFF;
	}
	
	.form-section {
		margin-bottom: 40rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 30rpx;
	}
	
	.form-item {
		margin-bottom: 30rpx;
	}
	
	.form-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
	}
	
	.form-label.required::after {
		content: '*';
		color: #FF4D4F;
		margin-left: 5rpx;
	}
	
	.form-input-wrapper {
		display: flex;
		align-items: center;
		border: 1rpx solid #E5E6EB;
		border-radius: 8rpx;
		padding: 0 20rpx;
		height: 80rpx;
	}
	
	.form-input {
		flex: 1;
		font-size: 28rpx;
		color: #333;
	}
	
	.gender-selector {
		display: flex;
	}
	
	.gender-option {
		width: 120rpx;
		height: 70rpx;
		border: 1rpx solid #E5E6EB;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666;
		margin-right: 20rpx;
	}
	
	.gender-option.gender-selected {
		background-color: #1677FF;
		color: #FFFFFF;
		border-color: #1677FF;
	}
	
	.location-input-wrapper {
		padding-right: 10rpx;
	}
	
	.location-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.location-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	.textarea-wrapper {
		border: 1rpx solid #E5E6EB;
		border-radius: 8rpx;
		padding: 20rpx;
	}
	
	.form-textarea {
		width: 100%;
		height: 200rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.upload-wrapper {
		display: flex;
		flex-wrap: wrap;
	}
	
	.upload-btn {
		width: 150rpx;
		height: 150rpx;
		border: 2rpx dashed #C9CDD4;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.upload-btn-inner {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.upload-icon {
		width: 50rpx;
		height: 50rpx;
		margin-bottom: 10rpx;
	}
	
	.upload-text {
		font-size: 24rpx;
		color: #86909C;
	}
	
	.upload-preview {
		width: 150rpx;
		height: 150rpx;
		position: relative;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}
	
	.delete-btn {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		width: 36rpx;
		height: 36rpx;
		background-color: #FF4D4F;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.delete-icon {
		color: #FFFFFF;
		font-size: 24rpx;
	}
	
	.upload-tip {
		font-size: 24rpx;
		color: #86909C;
		width: 100%;
	}
	
	.tag-section {
		margin-bottom: 30rpx;
	}
	
	.tag-title {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
	}
	
	.tag-list {
		display: flex;
		flex-wrap: wrap;
	}
	
	.tag-item {
		padding: 10rpx 25rpx;
		border: 1rpx solid #E5E6EB;
		border-radius: 30rpx;
		font-size: 26rpx;
		color: #666;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.tag-item.tag-active {
		background-color: #1677FF;
		color: #FFFFFF;
		border-color: #1677FF;
	}
	
	.code-input-wrapper {
		padding-right: 10rpx;
	}
	
	.code-input {
		flex: none;
		width: 70%;
	}
	
	.code-btn {
		width: 160rpx;
		height: 60rpx;
		background-color: #1677FF;
		color: #FFFFFF;
		font-size: 26rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8rpx;
	}
	
	.input-unit {
		font-size: 28rpx;
		color: #333;
		margin-left: 10rpx;
	}
	
	.radio-group, .checkbox-group {
		display: flex;
	}
	
	.radio-item, .checkbox-item {
		display: flex;
		align-items: center;
		margin-right: 40rpx;
	}
	
	.radio-circle, .checkbox-square {
		width: 36rpx;
		height: 36rpx;
		border: 2rpx solid #C9CDD4;
		margin-right: 15rpx;
	}
	
	.radio-circle {
		border-radius: 50%;
	}
	
	.checkbox-square {
		border-radius: 4rpx;
	}
	
	.radio-circle.active, .checkbox-square.active {
		background-color: #1677FF;
		border-color: #1677FF;
	}
	
	.radio-text, .checkbox-text {
		font-size: 28rpx;
		color: #333;
	}
	
	.date-picker-text {
		flex: 1;
		font-size: 28rpx;
		color: #333;
	}
	
	.date-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	.redpacket-tips {
		display: flex;
		align-items: center;
		background-color: #FFF7E6;
		padding: 20rpx;
		border-radius: 8rpx;
	}
	
	.tips-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 15rpx;
	}
	
	.tips-text {
		flex: 1;
		font-size: 24rpx;
		color: #FA8C16;
		line-height: 1.5;
	}
	
	.red-packet-preview {
		margin-top: 30rpx;
	}
	
	.red-packet-preview-container {
		position: relative;
		width: 100%;
		height: 200rpx;
	}
	
	.red-packet-preview-bg {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
	}
	
	.red-packet-preview-content {
		position: relative;
		z-index: 1;
		padding: 30rpx;
		color: #FFE1B8;
	}
	
	.red-packet-preview-title {
		display: flex;
		align-items: center;
		font-size: 30rpx;
	}
	
	.red-packet-preview-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 15rpx;
	}
	
	.red-packet-preview-info {
		margin-top: 20rpx;
	}
	
	.red-packet-preview-amount {
		font-size: 48rpx;
		font-weight: bold;
	}
	
	.red-packet-preview-desc {
		font-size: 24rpx;
		margin-left: 15rpx;
	}
	
	.red-packet-preview-conditions {
		margin-top: 10rpx;
		font-size: 22rpx;
	}
	
	.ad-section {
		margin-top: 50rpx;
	}
	
	.publish-method-title {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 20rpx;
	}
	
	.ad-item {
		display: flex;
		align-items: center;
		background-color: #FFFFFF;
		padding: 25rpx;
		border-radius: 8rpx;
		border: 1rpx solid #E5E6EB;
		margin-bottom: 20rpx;
	}
	
	.ad-icon-wrap {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 25rpx;
	}
	
	.free-ad .ad-icon-wrap {
		background-color: #E6F7FF;
	}
	
	.pay-ad .ad-icon-wrap {
		background-color: #FFF1F0;
	}
	
	.ad-icon {
		width: 50rpx;
		height: 50rpx;
	}
	
	.ad-content {
		flex: 1;
	}
	
	.ad-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
	}
	
	.ad-desc {
		font-size: 24rpx;
		color: #86909C;
		margin-top: 5rpx;
	}
	
	.ad-btn {
		width: 120rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 30rpx;
		font-size: 26rpx;
		font-weight: 600;
	}
	
	.free-btn {
		background-color: #E6F7FF;
		color: #1677FF;
	}
	
	.pay-btn {
		background-color: #FFF1F0;
		color: #FF4D4F;
	}
	
	.pay-options {
		background-color: #FFFFFF;
		padding: 20rpx 30rpx;
		border-radius: 8rpx;
		border: 1rpx solid #E5E6EB;
		margin-top: -10rpx;
	}
	
	.pay-options-title {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 20rpx;
	}
	
	.pay-options-list {
		display: flex;
		justify-content: space-between;
	}
	
	.pay-option-item {
		width: 30%;
		padding: 20rpx;
		border: 1rpx solid #E5E6EB;
		border-radius: 8rpx;
		text-align: center;
	}
	
	.pay-option-label {
		font-size: 28rpx;
		color: #333;
	}
	
	.pay-option-price {
		font-size: 26rpx;
		color: #FF4D4F;
		margin-top: 10rpx;
	}
	
	.agreement-section {
		margin-top: 40rpx;
	}
	
	.agreement-row {
		display: flex;
		align-items: center;
		font-size: 24rpx;
	}
	
	.agreement-text {
		color: #86909C;
	}
	
	.agreement-link {
		color: #1677FF;
	}
	
	.publish-btn-wrapper {
		margin-top: 50rpx;
	}
	
	.publish-btn {
		background-color: #1677FF;
		color: #FFFFFF;
		height: 90rpx;
		line-height: 90rpx;
		font-size: 32rpx;
		font-weight: 600;
		border-radius: 45rpx;
	}
	
	.publish-btn[disabled] {
		background-color: #C9CDD4;
		color: #FFFFFF;
	}
</style> 