/**
 * 分享服务
 * 处理商品、店铺等内容的分享功能
 * 包括分享到微信好友、朋友圈等渠道
 * 并处理分享后的积分奖励
 */

import { getToken } from './authService';
import { request } from './request';
import { showToast, showLoading, hideLoading } from './uiHelper';

/**
 * 分享内容类型
 */
export const ShareType = {
  PRODUCT: 'product',     // 商品
  MERCHANT: 'merchant',   // 商家
  ARTICLE: 'article',     // 文章
  ACTIVITY: 'activity',   // 活动
  COUPON: 'coupon',       // 优惠券
  COMMUNITY: 'community', // 社区帖子
};

/**
 * 分享渠道
 */
export const ShareChannel = {
  WECHAT: 'wechat',           // 微信好友
  MOMENTS: 'moments',         // 朋友圈
  QQ: 'qq',                   // QQ好友
  WEIBO: 'weibo',             // 微博
  COPY_LINK: 'copy_link',     // 复制链接
  GENERATE_POSTER: 'poster',  // 生成海报
};

/**
 * 分享商品
 * @param {Object} product 商品信息
 * @param {String} channel 分享渠道
 * @returns {Promise}
 */
export const shareProduct = async (product, channel) => {
  if (!product || !product.id) {
    showToast('商品信息不完整');
    return;
  }

  try {
    // 1. 生成分享参数
    const shareParams = await generateShareParams(ShareType.PRODUCT, product.id, channel);
    
    // 2. 执行实际分享
    const shareResult = await doShare(shareParams, channel);
    
    // 3. 记录分享行为并获取积分
    if (shareResult.success) {
      await recordShareAndGetPoints(ShareType.PRODUCT, product.id, channel);
    }
    
    return shareResult;
  } catch (error) {
    console.error('分享商品失败:', error);
    showToast('分享失败，请稍后重试');
    return { success: false, error };
  }
};

/**
 * 分享商家
 * @param {Object} merchant 商家信息
 * @param {String} channel 分享渠道
 * @returns {Promise}
 */
export const shareMerchant = async (merchant, channel) => {
  if (!merchant || !merchant.id) {
    showToast('商家信息不完整');
    return;
  }

  try {
    // 1. 生成分享参数
    const shareParams = await generateShareParams(ShareType.MERCHANT, merchant.id, channel);
    
    // 2. 执行实际分享
    const shareResult = await doShare(shareParams, channel);
    
    // 3. 记录分享行为并获取积分
    if (shareResult.success) {
      await recordShareAndGetPoints(ShareType.MERCHANT, merchant.id, channel);
    }
    
    return shareResult;
  } catch (error) {
    console.error('分享商家失败:', error);
    showToast('分享失败，请稍后重试');
    return { success: false, error };
  }
};

/**
 * 生成分享参数
 * @param {String} type 分享类型
 * @param {String|Number} id 内容ID
 * @param {String} channel 分享渠道
 * @returns {Promise<Object>}
 */
const generateShareParams = async (type, id, channel) => {
  // 获取分享内容信息
  const contentInfo = await getContentInfo(type, id);
  
  // 生成分享链接
  const shareLink = generateShareLink(type, id);
  
  // 构建分享参数
  const params = {
    title: contentInfo.title,
    desc: contentInfo.description,
    imageUrl: contentInfo.imageUrl,
    link: shareLink,
    type,
    id,
    channel,
  };
  
  return params;
};

/**
 * 获取要分享的内容信息
 * @param {String} type 内容类型
 * @param {String|Number} id 内容ID
 * @returns {Promise<Object>}
 */
const getContentInfo = async (type, id) => {
  // 实际应用中应该从API获取最新数据
  // 这里简化处理，直接返回mock数据
  
  // 模拟API请求延迟
  await new Promise(resolve => setTimeout(resolve, 200));
  
  switch (type) {
    case ShareType.PRODUCT:
      return {
        title: '超值好物推荐',
        description: '我发现了一个不错的商品，分享给你！',
        imageUrl: 'https://example.com/product-image.jpg',
      };
    case ShareType.MERCHANT:
      return {
        title: '优质商家推荐',
        description: '这家店的服务和商品都很棒，推荐给你！',
        imageUrl: 'https://example.com/merchant-image.jpg',
      };
    default:
      return {
        title: '磁州生活网',
        description: '发现身边好物，享受品质生活',
        imageUrl: 'https://example.com/default-image.jpg',
      };
  }
};

/**
 * 生成分享链接
 * @param {String} type 内容类型
 * @param {String|Number} id 内容ID
 * @returns {String}
 */
const generateShareLink = (type, id) => {
  // 获取当前用户ID，用于分享溯源
  const userId = uni.getStorageSync('userId') || '';
  
  // 构建基础链接
  let baseUrl = 'https://example.com/share';
  
  // 根据不同类型构建不同的链接
  switch (type) {
    case ShareType.PRODUCT:
      baseUrl = `https://example.com/product/${id}`;
      break;
    case ShareType.MERCHANT:
      baseUrl = `https://example.com/merchant/${id}`;
      break;
    case ShareType.ARTICLE:
      baseUrl = `https://example.com/article/${id}`;
      break;
    case ShareType.ACTIVITY:
      baseUrl = `https://example.com/activity/${id}`;
      break;
    default:
      baseUrl = `https://example.com/${type}/${id}`;
  }
  
  // 添加分享参数
  const shareUrl = `${baseUrl}?source=share&sharer=${userId}`;
  
  return shareUrl;
};

/**
 * 执行实际分享操作
 * @param {Object} params 分享参数
 * @param {String} channel 分享渠道
 * @returns {Promise<Object>}
 */
const doShare = async (params, channel) => {
  // 根据不同渠道执行不同的分享逻辑
  switch (channel) {
    case ShareChannel.WECHAT:
      return shareToWechat(params);
    case ShareChannel.MOMENTS:
      return shareToMoments(params);
    case ShareChannel.COPY_LINK:
      return copyShareLink(params);
    case ShareChannel.GENERATE_POSTER:
      return generateSharePoster(params);
    default:
      return shareToWechat(params);
  }
};

/**
 * 分享到微信好友
 * @param {Object} params 分享参数
 * @returns {Promise<Object>}
 */
const shareToWechat = async (params) => {
  return new Promise((resolve) => {
    uni.share({
      provider: 'weixin',
      scene: 'WXSceneSession', // 微信好友
      type: 0, // 分享类型，0:图文，1:纯文字，2:纯图片，3:音乐，4:视频，5:小程序
      title: params.title,
      summary: params.desc,
      imageUrl: params.imageUrl,
      href: params.link,
      success: () => {
        resolve({ success: true });
      },
      fail: (err) => {
        console.error('分享到微信好友失败:', err);
        resolve({ success: false, error: err });
      }
    });
  });
};

/**
 * 分享到朋友圈
 * @param {Object} params 分享参数
 * @returns {Promise<Object>}
 */
const shareToMoments = async (params) => {
  return new Promise((resolve) => {
    uni.share({
      provider: 'weixin',
      scene: 'WXSceneTimeline', // 朋友圈
      type: 0,
      title: params.title,
      summary: params.desc,
      imageUrl: params.imageUrl,
      href: params.link,
      success: () => {
        resolve({ success: true });
      },
      fail: (err) => {
        console.error('分享到朋友圈失败:', err);
        resolve({ success: false, error: err });
      }
    });
  });
};

/**
 * 复制分享链接
 * @param {Object} params 分享参数
 * @returns {Promise<Object>}
 */
const copyShareLink = async (params) => {
  return new Promise((resolve) => {
    uni.setClipboardData({
      data: params.link,
      success: () => {
        showToast('链接已复制，快去分享给好友吧');
        resolve({ success: true });
      },
      fail: (err) => {
        console.error('复制链接失败:', err);
        showToast('复制失败，请重试');
        resolve({ success: false, error: err });
      }
    });
  });
};

/**
 * 生成分享海报
 * @param {Object} params 分享参数
 * @returns {Promise<Object>}
 */
const generateSharePoster = async (params) => {
  showLoading('海报生成中...');
  
  try {
    // 实际应用中应调用API生成海报
    // 这里简化处理，模拟延迟后返回结果
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    hideLoading();
    showToast('海报生成成功');
    
    // 模拟海报图片路径
    const posterPath = 'https://example.com/generated-poster.jpg';
    
    // 保存海报到相册
    const saveResult = await savePosterToAlbum(posterPath);
    
    return { 
      success: true, 
      posterPath,
      saved: saveResult.success
    };
  } catch (error) {
    hideLoading();
    showToast('海报生成失败');
    console.error('生成海报失败:', error);
    return { success: false, error };
  }
};

/**
 * 保存海报到相册
 * @param {String} filePath 海报图片路径
 * @returns {Promise<Object>}
 */
const savePosterToAlbum = async (filePath) => {
  return new Promise((resolve) => {
    uni.saveImageToPhotosAlbum({
      filePath,
      success: () => {
        showToast('海报已保存到相册');
        resolve({ success: true });
      },
      fail: (err) => {
        console.error('保存海报失败:', err);
        showToast('保存失败，请检查权限设置');
        resolve({ success: false, error: err });
      }
    });
  });
};

/**
 * 记录分享行为并获取积分
 * @param {String} type 分享类型
 * @param {String|Number} id 内容ID
 * @param {String} channel 分享渠道
 * @returns {Promise<Object>}
 */
const recordShareAndGetPoints = async (type, id, channel) => {
  try {
    const token = getToken();
    if (!token) {
      console.warn('用户未登录，无法记录分享行为');
      return { success: false, error: 'NOT_LOGGED_IN' };
    }
    
    // 调用API记录分享行为
    const result = await request({
      url: '/api/share/record',
      method: 'POST',
      data: {
        type,
        contentId: id,
        channel,
      },
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      // 获取积分奖励
      const pointsResult = await getSharePoints(type, id);
      
      if (pointsResult.success && pointsResult.points > 0) {
        showToast(`分享成功，获得${pointsResult.points}积分`);
      }
      
      return {
        success: true,
        shared: true,
        points: pointsResult.points || 0
      };
    }
    
    return { success: true, shared: true, points: 0 };
  } catch (error) {
    console.error('记录分享行为失败:', error);
    return { success: false, error };
  }
};

/**
 * 获取分享积分奖励
 * @param {String} type 分享类型
 * @param {String|Number} id 内容ID
 * @returns {Promise<Object>}
 */
const getSharePoints = async (type, id) => {
  try {
    const token = getToken();
    if (!token) {
      return { success: false, points: 0 };
    }
    
    // 调用API获取积分奖励
    const result = await request({
      url: '/api/points/share-reward',
      method: 'POST',
      data: {
        type,
        contentId: id
      },
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      return {
        success: true,
        points: result.data.points || 0
      };
    }
    
    return { success: false, points: 0 };
  } catch (error) {
    console.error('获取分享积分失败:', error);
    return { success: false, points: 0, error };
  }
};

/**
 * 检查今日是否已达到分享积分上限
 * @returns {Promise<Object>}
 */
export const checkSharePointsLimit = async () => {
  try {
    const token = getToken();
    if (!token) {
      return { reached: false, current: 0, limit: 0 };
    }
    
    // 调用API检查积分上限
    const result = await request({
      url: '/api/points/share-limit',
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (result && result.success) {
      return {
        reached: result.data.reached || false,
        current: result.data.current || 0,
        limit: result.data.limit || 0
      };
    }
    
    return { reached: false, current: 0, limit: 0 };
  } catch (error) {
    console.error('检查分享积分上限失败:', error);
    return { reached: false, current: 0, limit: 0 };
  }
};

/**
 * 打开分享面板
 * @param {Object} content 要分享的内容
 * @param {String} type 内容类型
 * @returns {Promise<Object>}
 */
export const openSharePanel = async (content, type) => {
  return new Promise((resolve) => {
    // 构建分享参数
    const shareOptions = [
      { icon: 'wechat', name: '微信好友', id: ShareChannel.WECHAT },
      { icon: 'moments', name: '朋友圈', id: ShareChannel.MOMENTS },
      { icon: 'link', name: '复制链接', id: ShareChannel.COPY_LINK },
      { icon: 'poster', name: '生成海报', id: ShareChannel.GENERATE_POSTER },
    ];
    
    // 显示分享面板
    uni.showActionSheet({
      itemList: shareOptions.map(option => option.name),
      success: async (res) => {
        const selectedOption = shareOptions[res.tapIndex];
        let result;
        
        // 根据内容类型执行不同的分享方法
        switch (type) {
          case ShareType.PRODUCT:
            result = await shareProduct(content, selectedOption.id);
            break;
          case ShareType.MERCHANT:
            result = await shareMerchant(content, selectedOption.id);
            break;
          default:
            showToast('不支持的分享类型');
            result = { success: false };
        }
        
        resolve(result);
      },
      fail: () => {
        resolve({ success: false, cancelled: true });
      }
    });
  });
}; 