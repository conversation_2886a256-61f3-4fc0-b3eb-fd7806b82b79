{"version": 3, "file": "partner-levels.js", "sources": ["subPackages/partner/pages/partner-levels.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGFydG5lclxwYWdlc1xwYXJ0bmVyLWxldmVscy52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"levels-container\">\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<view class=\"custom-navbar\">\r\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\r\n\t\t\t\t<image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"navbar-title\">等级说明</view>\r\n\t\t\t<view class=\"navbar-right\">\r\n\t\t\t\t<!-- 预留位置与发布页面保持一致 -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 添加顶部安全区域 -->\r\n\t\t<view class=\"safe-area-top\"></view>\r\n\t\t\r\n\t\t<!-- 当前等级信息 -->\r\n\t\t<view class=\"current-level-card\">\r\n\t\t\t<view class=\"level-header\">\r\n\t\t\t\t<view class=\"level-info\">\r\n\t\t\t\t\t<image class=\"level-badge\" :src=\"getLevelIcon()\"></image>\r\n\t\t\t\t\t<view class=\"level-detail\">\r\n\t\t\t\t\t\t<text class=\"level-name\">{{ getLevelName() }}</text>\r\n\t\t\t\t\t\t<text class=\"level-desc\">{{ getLevelDesc() }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"upgrade-btn\" v-if=\"canUpgrade\" @click=\"showUpgradeModal\">\r\n\t\t\t\t\t<text>立即升级</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"level-progress\">\r\n\t\t\t\t<view class=\"progress-info\">\r\n\t\t\t\t\t<text class=\"progress-text\">距离下一等级还需</text>\r\n\t\t\t\t\t<text class=\"progress-value\">{{ nextLevelRequirement }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-track\">\r\n\t\t\t\t\t<view class=\"progress-bar\" :style=\"{ width: progressWidth + '%' }\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 等级特权 -->\r\n\t\t<view class=\"privileges-card\">\r\n\t\t\t<view class=\"card-title\">等级特权</view>\r\n\t\t\t\r\n\t\t\t<view class=\"privileges-table\">\r\n\t\t\t\t<view class=\"table-header\">\r\n\t\t\t\t\t<view class=\"header-cell level-cell\">等级</view>\r\n\t\t\t\t\t<view class=\"header-cell\">一级佣金</view>\r\n\t\t\t\t\t<view class=\"header-cell\">二级佣金</view>\r\n\t\t\t\t\t<view class=\"header-cell\">特权</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"table-row\" :class=\"{ active: partnerInfo.level === 1 }\">\r\n\t\t\t\t\t<view class=\"table-cell level-cell\">\r\n\t\t\t\t\t\t<view class=\"level-tag level-1\">\r\n\t\t\t\t\t\t\t<image class=\"mini-badge\" src=\"/static/images/tabbar/partner-level-1.png\"></image>\r\n\t\t\t\t\t\t\t<text>普通合伙人</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"table-cell\">5%</view>\r\n\t\t\t\t\t<view class=\"table-cell\">2%</view>\r\n\t\t\t\t\t<view class=\"table-cell\">基础推广权益</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"table-row\" :class=\"{ active: partnerInfo.level === 2 }\">\r\n\t\t\t\t\t<view class=\"table-cell level-cell\">\r\n\t\t\t\t\t\t<view class=\"level-tag level-2\">\r\n\t\t\t\t\t\t\t<image class=\"mini-badge\" src=\"/static/images/tabbar/partner-level-2.png\"></image>\r\n\t\t\t\t\t\t\t<text>银牌合伙人</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"table-cell\">8%</view>\r\n\t\t\t\t\t<view class=\"table-cell\">3%</view>\r\n\t\t\t\t\t<view class=\"table-cell\">专属推广海报</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"table-row\" :class=\"{ active: partnerInfo.level === 3 }\">\r\n\t\t\t\t\t<view class=\"table-cell level-cell\">\r\n\t\t\t\t\t\t<view class=\"level-tag level-3\">\r\n\t\t\t\t\t\t\t<image class=\"mini-badge\" src=\"/static/images/tabbar/partner-level-3.png\"></image>\r\n\t\t\t\t\t\t\t<text>金牌合伙人</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"table-cell\">12%</view>\r\n\t\t\t\t\t<view class=\"table-cell\">5%</view>\r\n\t\t\t\t\t<view class=\"table-cell\">专属客服</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"table-row\" :class=\"{ active: partnerInfo.level === 4 }\">\r\n\t\t\t\t\t<view class=\"table-cell level-cell\">\r\n\t\t\t\t\t\t<view class=\"level-tag level-4\">\r\n\t\t\t\t\t\t\t<image class=\"mini-badge\" src=\"/static/images/tabbar/partner-level-4.png\"></image>\r\n\t\t\t\t\t\t\t<text>钻石合伙人</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"table-cell\">15%</view>\r\n\t\t\t\t\t<view class=\"table-cell\">8%</view>\r\n\t\t\t\t\t<view class=\"table-cell\">专属活动特权</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 升级规则 -->\r\n\t\t<view class=\"rules-card\">\r\n\t\t\t<view class=\"card-title\">升级规则</view>\r\n\t\t\t\r\n\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t<view class=\"rule-title\">\r\n\t\t\t\t\t<view class=\"rule-dot\"></view>\r\n\t\t\t\t\t<text>普通合伙人</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rule-content\">\r\n\t\t\t\t\t<text>注册成为合伙人即可获得普通合伙人资格，享受一级佣金5%，二级佣金2%的分润。</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t<view class=\"rule-title\">\r\n\t\t\t\t\t<view class=\"rule-dot\"></view>\r\n\t\t\t\t\t<text>银牌合伙人</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rule-content\">\r\n\t\t\t\t\t<text>累计推广30名有效用户，或累计推广订单金额达到5000元，即可升级为银牌合伙人。</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t<view class=\"rule-title\">\r\n\t\t\t\t\t<view class=\"rule-dot\"></view>\r\n\t\t\t\t\t<text>金牌合伙人</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rule-content\">\r\n\t\t\t\t\t<text>累计推广100名有效用户，或累计推广订单金额达到20000元，即可升级为金牌合伙人。</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t<view class=\"rule-title\">\r\n\t\t\t\t\t<view class=\"rule-dot\"></view>\r\n\t\t\t\t\t<text>钻石合伙人</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"rule-content\">\r\n\t\t\t\t\t<text>累计推广300名有效用户，或累计推广订单金额达到50000元，即可升级为钻石合伙人。</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 常见问题 -->\r\n\t\t<view class=\"faq-card\">\r\n\t\t\t<view class=\"card-title\">常见问题</view>\r\n\t\t\t\r\n\t\t\t<view class=\"faq-item\" @click=\"toggleFaq(0)\">\r\n\t\t\t\t<view class=\"faq-question\">\r\n\t\t\t\t\t<text>什么是合伙人系统？</text>\r\n\t\t\t\t\t<image class=\"arrow-icon\" :class=\"{ rotated: openedFaq === 0 }\" src=\"/static/images/tabbar/arrow-down.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"faq-answer\" v-if=\"openedFaq === 0\">\r\n\t\t\t\t\t<text>合伙人系统是磁州同城为用户提供的推广分销系统，通过分享平台内容，邀请好友注册并消费，合伙人可以获得相应的佣金奖励。</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"faq-item\" @click=\"toggleFaq(1)\">\r\n\t\t\t\t<view class=\"faq-question\">\r\n\t\t\t\t\t<text>如何成为合伙人？</text>\r\n\t\t\t\t\t<image class=\"arrow-icon\" :class=\"{ rotated: openedFaq === 1 }\" src=\"/static/images/tabbar/arrow-down.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"faq-answer\" v-if=\"openedFaq === 1\">\r\n\t\t\t\t\t<text>在\"我的\"页面点击\"合伙人\"，根据页面提示完成实名认证并同意合伙人协议即可成为普通合伙人。</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"faq-item\" @click=\"toggleFaq(2)\">\r\n\t\t\t\t<view class=\"faq-question\">\r\n\t\t\t\t\t<text>佣金如何结算？</text>\r\n\t\t\t\t\t<image class=\"arrow-icon\" :class=\"{ rotated: openedFaq === 2 }\" src=\"/static/images/tabbar/arrow-down.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"faq-answer\" v-if=\"openedFaq === 2\">\r\n\t\t\t\t\t<text>佣金将在订单完成后自动计入您的合伙人账户，可在\"收益提现\"中申请提现至微信钱包或银行卡，T+1个工作日到账。</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"faq-item\" @click=\"toggleFaq(3)\">\r\n\t\t\t\t<view class=\"faq-question\">\r\n\t\t\t\t\t<text>如何提高我的佣金收益？</text>\r\n\t\t\t\t\t<image class=\"arrow-icon\" :class=\"{ rotated: openedFaq === 3 }\" src=\"/static/images/tabbar/arrow-down.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"faq-answer\" v-if=\"openedFaq === 3\">\r\n\t\t\t\t\t<text>1. 提升合伙人等级，获得更高的佣金比例；\\n2. 扩大您的推广范围，邀请更多用户；\\n3. 关注平台活动，参与高佣金的特殊推广活动。</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 升级弹窗 -->\r\n\t\t<view class=\"upgrade-modal\" v-if=\"showModal\" @click=\"hideUpgradeModal\">\r\n\t\t\t<view class=\"modal-content\" @click.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">升级{{ getNextLevelName() }}</text>\r\n\t\t\t\t\t<view class=\"close-btn\" @click=\"hideUpgradeModal\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t\t<view class=\"upgrade-info\">\r\n\t\t\t\t\t\t<image class=\"upgrade-icon\" src=\"/static/images/tabbar/upgrade.png\"></image>\r\n\t\t\t\t\t\t<view class=\"upgrade-text\">\r\n\t\t\t\t\t\t\t<text class=\"upgrade-title\">当前等级：{{ getLevelName() }}</text>\r\n\t\t\t\t\t\t\t<text class=\"upgrade-desc\">升级后将享受更高佣金比例和更多特权</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"upgrade-requirements\">\r\n\t\t\t\t\t\t<view class=\"requirement-item\">\r\n\t\t\t\t\t\t\t<text class=\"requirement-label\">升级条件：</text>\r\n\t\t\t\t\t\t\t<text class=\"requirement-value\">{{ getUpgradeRequirements() }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"requirement-item\">\r\n\t\t\t\t\t\t\t<text class=\"requirement-label\">当前进度：</text>\r\n\t\t\t\t\t\t\t<text class=\"requirement-value\">{{ getCurrentProgress() }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<button class=\"upgrade-confirm-btn\" @click=\"confirmUpgrade\">立即升级</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\nimport { getLocalUserInfo } from '@/utils/userProfile.js';\r\nimport { smartNavigate } from '@/utils/navigation.js';\r\n\r\n// 响应式数据\r\nconst partnerInfo = reactive({\r\n\tlevel: 2, // 默认等级\r\n\tusers: 28,\r\n\torderAmount: 4200\r\n});\r\nconst progressWidth = ref(65); // 升级进度百分比\r\nconst nextLevelRequirement = ref('2名用户或800元订单');\r\nconst openedFaq = ref(-1); // 当前打开的FAQ项，-1表示都关闭\r\nconst showModal = ref(false); // 升级弹窗显示状态\r\n\r\n// 计算属性\r\nconst canUpgrade = computed(() => {\r\n\treturn partnerInfo.level < 4; // 钻石合伙人是最高级别\r\n});\r\n\r\n// 获取合伙人信息\r\nconst getPartnerInfo = () => {\r\n\t// 模拟数据，实际应从API获取\r\n\tsetTimeout(() => {\r\n\t\t// 模拟请求返回数据\r\n\t\tpartnerInfo.level = 2;\r\n\t\tpartnerInfo.users = 28;\r\n\t\tpartnerInfo.orderAmount = 4200;\r\n\t\t\r\n\t\t// 计算升级进度\r\n\t\tcalculateProgress();\r\n\t}, 500);\r\n};\r\n\r\n// 计算升级进度\r\nconst calculateProgress = () => {\r\n\tif (partnerInfo.level === 1) {\r\n\t\t// 普通合伙人升级到银牌：需要30名用户或5000元订单\r\n\t\tconst userProgress = (partnerInfo.users / 30) * 100;\r\n\t\tconst amountProgress = (partnerInfo.orderAmount / 5000) * 100;\r\n\t\tprogressWidth.value = Math.max(userProgress, amountProgress);\r\n\t\t\r\n\t\tconst remainingUsers = Math.max(0, 30 - partnerInfo.users);\r\n\t\tconst remainingAmount = Math.max(0, 5000 - partnerInfo.orderAmount);\r\n\t\tnextLevelRequirement.value = `${remainingUsers}名用户或${remainingAmount}元订单`;\r\n\t} else if (partnerInfo.level === 2) {\r\n\t\t// 银牌合伙人升级到金牌：需要100名用户或20000元订单\r\n\t\tconst userProgress = (partnerInfo.users / 100) * 100;\r\n\t\tconst amountProgress = (partnerInfo.orderAmount / 20000) * 100;\r\n\t\tprogressWidth.value = Math.max(userProgress, amountProgress);\r\n\t\t\r\n\t\tconst remainingUsers = Math.max(0, 100 - partnerInfo.users);\r\n\t\tconst remainingAmount = Math.max(0, 20000 - partnerInfo.orderAmount);\r\n\t\tnextLevelRequirement.value = `${remainingUsers}名用户或${remainingAmount}元订单`;\r\n\t} else if (partnerInfo.level === 3) {\r\n\t\t// 金牌合伙人升级到钻石：需要300名用户或50000元订单\r\n\t\tconst userProgress = (partnerInfo.users / 300) * 100;\r\n\t\tconst amountProgress = (partnerInfo.orderAmount / 50000) * 100;\r\n\t\tprogressWidth.value = Math.max(userProgress, amountProgress);\r\n\t\t\r\n\t\tconst remainingUsers = Math.max(0, 300 - partnerInfo.users);\r\n\t\tconst remainingAmount = Math.max(0, 50000 - partnerInfo.orderAmount);\r\n\t\tnextLevelRequirement.value = `${remainingUsers}名用户或${remainingAmount}元订单`;\r\n\t} else {\r\n\t\t// 钻石合伙人是最高级别\r\n\t\tprogressWidth.value = 100;\r\n\t\tnextLevelRequirement.value = '已达最高等级';\r\n\t}\r\n\t\r\n\t// 限制进度条最大值为100%\r\n\tprogressWidth.value = Math.min(progressWidth.value, 100);\r\n};\r\n\r\n// 获取等级图标\r\nconst getLevelIcon = () => {\r\n\tconst icons = [\r\n\t\t'/static/images/tabbar/partner-level-1.png',\r\n\t\t'/static/images/tabbar/partner-level-2.png',\r\n\t\t'/static/images/tabbar/partner-level-3.png',\r\n\t\t'/static/images/tabbar/partner-level-4.png'\r\n\t];\r\n\treturn icons[partnerInfo.level - 1] || icons[0];\r\n};\r\n\r\n// 获取等级名称\r\nconst getLevelName = () => {\r\n\tconst names = [\r\n\t\t'普通合伙人',\r\n\t\t'银牌合伙人',\r\n\t\t'金牌合伙人',\r\n\t\t'钻石合伙人'\r\n\t];\r\n\treturn names[partnerInfo.level - 1] || '未知等级';\r\n};\r\n\r\n// 获取下一等级名称\r\nconst getNextLevelName = () => {\r\n\tconst names = [\r\n\t\t'普通合伙人',\r\n\t\t'银牌合伙人',\r\n\t\t'金牌合伙人',\r\n\t\t'钻石合伙人'\r\n\t];\r\n\tif (partnerInfo.level < 4) {\r\n\t\treturn names[partnerInfo.level];\r\n\t}\r\n\treturn '最高等级';\r\n};\r\n\r\n// 获取等级描述\r\nconst getLevelDesc = () => {\r\n\tconst descs = [\r\n\t\t'一级佣金5%，二级佣金2%',\r\n\t\t'一级佣金8%，二级佣金3%',\r\n\t\t'一级佣金12%，二级佣金5%',\r\n\t\t'一级佣金15%，二级佣金8%'\r\n\t];\r\n\treturn descs[partnerInfo.level - 1] || '';\r\n};\r\n\r\n// 切换FAQ展开状态\r\nconst toggleFaq = (index) => {\r\n\tif (openedFaq.value === index) {\r\n\t\topenedFaq.value = -1; // 关闭当前展开的FAQ\r\n\t} else {\r\n\t\topenedFaq.value = index; // 展开所点击的FAQ\r\n\t}\r\n};\r\n\r\n// 显示升级弹窗\r\nconst showUpgradeModal = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/subPackages/partner/pages/partner-upgrade'\r\n\t});\r\n};\r\n\r\n// 隐藏升级弹窗\r\nconst hideUpgradeModal = () => {\r\n\tshowModal.value = false;\r\n};\r\n\r\n// 获取升级条件\r\nconst getUpgradeRequirements = () => {\r\n\tif (partnerInfo.level === 1) {\r\n\t\treturn '30名有效用户或5000元订单';\r\n\t} else if (partnerInfo.level === 2) {\r\n\t\treturn '100名有效用户或20000元订单';\r\n\t} else if (partnerInfo.level === 3) {\r\n\t\treturn '300名有效用户或50000元订单';\r\n\t}\r\n\treturn '已达最高等级';\r\n};\r\n\r\n// 获取当前进度\r\nconst getCurrentProgress = () => {\r\n\treturn `${partnerInfo.users}名用户，${partnerInfo.orderAmount}元订单`;\r\n};\r\n\r\n// 确认升级\r\nconst confirmUpgrade = () => {\r\n\t// 跳转到升级页面\r\n\tuni.navigateTo({\r\n\t\turl: '/subPackages/partner/pages/partner-upgrade'\r\n\t});\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n\tuni.navigateBack({\r\n\t\tfail: () => {\r\n\t\t\t// 如果返回失败（无页面可返回），则跳转到合伙人首页\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/subPackages/partner/pages/partner'\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\t// 获取合伙人信息\r\n\tgetPartnerInfo();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.levels-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f7fa;\r\n\tpadding-bottom: 40rpx;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\theight: 88rpx;\r\n\tpadding: 0 30rpx;\r\n\tpadding-top: 44px; /* 状态栏高度 */\r\n\tposition: fixed; /* 改为固定定位 */\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground-image: linear-gradient(135deg, #0066FF, #0052CC);\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);\r\n\tz-index: 100; /* 提高z-index确保在最上层 */\r\n}\r\n\r\n.navbar-title {\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tcolor: #ffffff;\r\n\tfont-size: 36rpx;\r\n\tfont-weight: 700;\r\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\r\n\ttext-align: center;\r\n}\r\n\r\n.navbar-left {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tposition: relative;\r\n\tz-index: 20; /* 确保在标题上层，可以被点击 */\r\n}\r\n\r\n.back-icon {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.safe-area-top {\r\n\theight: 180rpx;\r\n\twidth: 100%;\r\n}\r\n\r\n/* 当前等级卡片 */\r\n.current-level-card {\r\n\tmargin: 30rpx;\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.level-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.level-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.level-badge {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.level-detail {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.level-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333333;\r\n\tmargin-bottom: 6rpx;\r\n}\r\n\r\n.level-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666666;\r\n}\r\n\r\n.upgrade-btn {\r\n\tbackground: linear-gradient(135deg, #0066FF, #0052CC);\r\n\tpadding: 12rpx 30rpx;\r\n\tborder-radius: 30rpx;\r\n\tcolor: #ffffff;\r\n\tfont-size: 26rpx;\r\n\tfont-weight: 500;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\r\n}\r\n\r\n.level-progress {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.progress-info {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.progress-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666666;\r\n}\r\n\r\n.progress-value {\r\n\tfont-size: 26rpx;\r\n\tcolor: #FF6B00;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.progress-track {\r\n\theight: 16rpx;\r\n\tbackground-color: #f0f0f0;\r\n\tborder-radius: 8rpx;\r\n\toverflow: hidden;\r\n}\r\n\r\n.progress-bar {\r\n\theight: 100%;\r\n\tbackground: linear-gradient(to right, #0066FF, #36CBCB);\r\n\tborder-radius: 8rpx;\r\n\ttransition: width 0.5s ease;\r\n}\r\n\r\n/* 等级特权卡片 */\r\n.privileges-card {\r\n\tmargin: 30rpx;\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.card-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333333;\r\n\tmargin-bottom: 30rpx;\r\n\tposition: relative;\r\n\t\r\n\t&::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\tbottom: -10rpx;\r\n\t\tleft: 0;\r\n\t\twidth: 60rpx;\r\n\t\theight: 4rpx;\r\n\t\tbackground: linear-gradient(to right, #0066FF, #36CBCB);\r\n\t\tborder-radius: 2rpx;\r\n\t}\r\n}\r\n\r\n.privileges-table {\r\n\twidth: 100%;\r\n\tborder-radius: 12rpx;\r\n\toverflow: hidden;\r\n\tborder: 1px solid #e0e0e0;\r\n}\r\n\r\n.table-header {\r\n\tdisplay: flex;\r\n\tbackground-color: #f5f7fa;\r\n}\r\n\r\n.header-cell {\r\n\tflex: 1;\r\n\tpadding: 20rpx 10rpx;\r\n\ttext-align: center;\r\n\tfont-size: 26rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333333;\r\n\tborder-right: 1px solid #e0e0e0;\r\n\t\r\n\t&:last-child {\r\n\t\tborder-right: none;\r\n\t}\r\n}\r\n\r\n.level-cell {\r\n\tflex: 1.5;\r\n}\r\n\r\n.table-row {\r\n\tdisplay: flex;\r\n\tborder-top: 1px solid #e0e0e0;\r\n\t\r\n\t&.active {\r\n\t\tbackground-color: rgba(0, 102, 255, 0.05);\r\n\t}\r\n}\r\n\r\n.table-cell {\r\n\tflex: 1;\r\n\tpadding: 20rpx 10rpx;\r\n\ttext-align: center;\r\n\tfont-size: 26rpx;\r\n\tcolor: #666666;\r\n\tborder-right: 1px solid #e0e0e0;\r\n\t\r\n\t&:last-child {\r\n\t\tborder-right: none;\r\n\t}\r\n}\r\n\r\n.level-tag {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\t\r\n\t.mini-badge {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t\r\n\ttext {\r\n\t\tfont-size: 24rpx;\r\n\t\twhite-space: nowrap;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t}\r\n}\r\n\r\n/* 升级规则卡片 */\r\n.rules-card {\r\n\tmargin: 30rpx;\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.rule-item {\r\n\tmargin-bottom: 20rpx;\r\n\t\r\n\t&:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n}\r\n\r\n.rule-title {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.rule-dot {\r\n\twidth: 12rpx;\r\n\theight: 12rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #0066FF;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.rule-title text {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333333;\r\n}\r\n\r\n.rule-content {\r\n\tpadding-left: 22rpx;\r\n}\r\n\r\n.rule-content text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666666;\r\n\tline-height: 1.6;\r\n}\r\n\r\n/* FAQ卡片 */\r\n.faq-card {\r\n\tmargin: 30rpx;\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.faq-item {\r\n\tmargin-bottom: 20rpx;\r\n\tpadding-bottom: 20rpx;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n\t\r\n\t&:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t\tpadding-bottom: 0;\r\n\t\tborder-bottom: none;\r\n\t}\r\n}\r\n\r\n.faq-question {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 10rpx 0;\r\n}\r\n\r\n.faq-question text {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333333;\r\n}\r\n\r\n.arrow-icon {\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n\ttransition: transform 0.3s ease;\r\n\t\r\n\t&.rotated {\r\n\t\ttransform: rotate(180deg);\r\n\t}\r\n}\r\n\r\n.faq-answer {\r\n\tmargin-top: 15rpx;\r\n\tpadding: 20rpx;\r\n\tbackground-color: #f9f9f9;\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.faq-answer text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666666;\r\n\tline-height: 1.6;\r\n}\r\n\r\n/* 升级弹窗 */\r\n.upgrade-modal {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 999;\r\n}\r\n\r\n.modal-content {\r\n\twidth: 80%;\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 16rpx;\r\n\toverflow: hidden;\r\n}\r\n\r\n.modal-header {\r\n\tpadding: 30rpx;\r\n\tbackground: linear-gradient(135deg, #0066FF, #0052CC);\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.modal-title {\r\n\tcolor: #ffffff;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.close-btn {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tcolor: #ffffff;\r\n\tfont-size: 40rpx;\r\n\tfont-weight: 300;\r\n}\r\n\r\n.modal-body {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.upgrade-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/partner/pages/partner-levels.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "uni", "onMounted"], "mappings": ";;;;;;AA2OA,UAAM,cAAcA,cAAAA,SAAS;AAAA,MAC5B,OAAO;AAAA;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,IACd,CAAC;AACD,UAAM,gBAAgBC,cAAAA,IAAI,EAAE;AAC5B,UAAM,uBAAuBA,cAAAA,IAAI,aAAa;AAC9C,UAAM,YAAYA,cAAAA,IAAI,EAAE;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,aAAaC,cAAQ,SAAC,MAAM;AACjC,aAAO,YAAY,QAAQ;AAAA,IAC5B,CAAC;AAGD,UAAM,iBAAiB,MAAM;AAE5B,iBAAW,MAAM;AAEhB,oBAAY,QAAQ;AACpB,oBAAY,QAAQ;AACpB,oBAAY,cAAc;AAG1B;MACA,GAAE,GAAG;AAAA,IACP;AAGA,UAAM,oBAAoB,MAAM;AAC/B,UAAI,YAAY,UAAU,GAAG;AAE5B,cAAM,eAAgB,YAAY,QAAQ,KAAM;AAChD,cAAM,iBAAkB,YAAY,cAAc,MAAQ;AAC1D,sBAAc,QAAQ,KAAK,IAAI,cAAc,cAAc;AAE3D,cAAM,iBAAiB,KAAK,IAAI,GAAG,KAAK,YAAY,KAAK;AACzD,cAAM,kBAAkB,KAAK,IAAI,GAAG,MAAO,YAAY,WAAW;AAClE,6BAAqB,QAAQ,GAAG,cAAc,OAAO,eAAe;AAAA,MACtE,WAAY,YAAY,UAAU,GAAG;AAEnC,cAAM,eAAgB,YAAY,QAAQ,MAAO;AACjD,cAAM,iBAAkB,YAAY,cAAc,MAAS;AAC3D,sBAAc,QAAQ,KAAK,IAAI,cAAc,cAAc;AAE3D,cAAM,iBAAiB,KAAK,IAAI,GAAG,MAAM,YAAY,KAAK;AAC1D,cAAM,kBAAkB,KAAK,IAAI,GAAG,MAAQ,YAAY,WAAW;AACnE,6BAAqB,QAAQ,GAAG,cAAc,OAAO,eAAe;AAAA,MACtE,WAAY,YAAY,UAAU,GAAG;AAEnC,cAAM,eAAgB,YAAY,QAAQ,MAAO;AACjD,cAAM,iBAAkB,YAAY,cAAc,MAAS;AAC3D,sBAAc,QAAQ,KAAK,IAAI,cAAc,cAAc;AAE3D,cAAM,iBAAiB,KAAK,IAAI,GAAG,MAAM,YAAY,KAAK;AAC1D,cAAM,kBAAkB,KAAK,IAAI,GAAG,MAAQ,YAAY,WAAW;AACnE,6BAAqB,QAAQ,GAAG,cAAc,OAAO,eAAe;AAAA,MACtE,OAAQ;AAEN,sBAAc,QAAQ;AACtB,6BAAqB,QAAQ;AAAA,MAC7B;AAGD,oBAAc,QAAQ,KAAK,IAAI,cAAc,OAAO,GAAG;AAAA,IACxD;AAGA,UAAM,eAAe,MAAM;AAC1B,YAAM,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACC,aAAO,MAAM,YAAY,QAAQ,CAAC,KAAK,MAAM,CAAC;AAAA,IAC/C;AAGA,UAAM,eAAe,MAAM;AAC1B,YAAM,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACC,aAAO,MAAM,YAAY,QAAQ,CAAC,KAAK;AAAA,IACxC;AAGA,UAAM,mBAAmB,MAAM;AAC9B,YAAM,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACC,UAAI,YAAY,QAAQ,GAAG;AAC1B,eAAO,MAAM,YAAY,KAAK;AAAA,MAC9B;AACD,aAAO;AAAA,IACR;AAGA,UAAM,eAAe,MAAM;AAC1B,YAAM,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACC,aAAO,MAAM,YAAY,QAAQ,CAAC,KAAK;AAAA,IACxC;AAGA,UAAM,YAAY,CAAC,UAAU;AAC5B,UAAI,UAAU,UAAU,OAAO;AAC9B,kBAAU,QAAQ;AAAA,MACpB,OAAQ;AACN,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAGA,UAAM,mBAAmB,MAAM;AAC9BC,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAGA,UAAM,mBAAmB,MAAM;AAC9B,gBAAU,QAAQ;AAAA,IACnB;AAGA,UAAM,yBAAyB,MAAM;AACpC,UAAI,YAAY,UAAU,GAAG;AAC5B,eAAO;AAAA,MACT,WAAY,YAAY,UAAU,GAAG;AACnC,eAAO;AAAA,MACT,WAAY,YAAY,UAAU,GAAG;AACnC,eAAO;AAAA,MACP;AACD,aAAO;AAAA,IACR;AAGA,UAAM,qBAAqB,MAAM;AAChC,aAAO,GAAG,YAAY,KAAK,OAAO,YAAY,WAAW;AAAA,IAC1D;AAGA,UAAM,iBAAiB,MAAM;AAE5BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAGA,UAAM,SAAS,MAAM;AACpBA,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM,MAAM;AAEXA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACT,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGAC,kBAAAA,UAAU,MAAM;AAEf;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3ZD,GAAG,WAAW,eAAe;"}