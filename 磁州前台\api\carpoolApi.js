/**
 * 拼车API服务
 * 处理拼车相关的所有API调用
 */

import request from '@/utils/request';

// 拼车API服务类
class CarpoolApiService {

  /**
   * 获取拼车列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {string} params.type - 拼车类型 (people-to-car, car-to-people)
   * @param {string} params.departure - 出发地
   * @param {string} params.destination - 目的地
   * @param {string} params.date - 出发日期
   * @returns {Promise} 拼车列表
   */
  async getCarpoolList(params = {}) {
    try {
      const queryParams = {
        page: params.page || 1,
        limit: params.limit || 10,
        type: params.type || null,
        departure: params.departure || null,
        destination: params.destination || null,
        date: params.date || null
      };

      const response = await request.get('/carpool/list', { params: queryParams });
      
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0,
        page: response.page || 1,
        limit: response.limit || 10
      };
    } catch (error) {
      console.error('获取拼车列表失败:', error);
      return {
        success: false,
        data: [],
        message: error.message || '获取拼车信息失败'
      };
    }
  }

  /**
   * 获取拼车详情
   * @param {number} carpoolId - 拼车ID
   * @returns {Promise} 拼车详情
   */
  async getCarpoolDetail(carpoolId) {
    try {
      const response = await request.get(`/carpool/${carpoolId}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取拼车详情失败:', error);
      return {
        success: false,
        message: error.message || '获取拼车详情失败'
      };
    }
  }

  /**
   * 发布拼车信息
   * @param {Object} carpoolData - 拼车数据
   * @returns {Promise} 发布结果
   */
  async publishCarpool(carpoolData) {
    try {
      const response = await request.post('/carpool/publish', carpoolData);
      
      return {
        success: true,
        data: response.data,
        message: '发布成功'
      };
    } catch (error) {
      console.error('发布拼车失败:', error);
      return {
        success: false,
        message: error.message || '发布失败，请重试'
      };
    }
  }

  /**
   * 更新拼车信息
   * @param {number} carpoolId - 拼车ID
   * @param {Object} carpoolData - 拼车数据
   * @returns {Promise} 更新结果
   */
  async updateCarpool(carpoolId, carpoolData) {
    try {
      const response = await request.put(`/carpool/${carpoolId}`, carpoolData);
      
      return {
        success: true,
        data: response.data,
        message: '更新成功'
      };
    } catch (error) {
      console.error('更新拼车失败:', error);
      return {
        success: false,
        message: error.message || '更新失败，请重试'
      };
    }
  }

  /**
   * 删除拼车信息
   * @param {number} carpoolId - 拼车ID
   * @returns {Promise} 删除结果
   */
  async deleteCarpool(carpoolId) {
    try {
      await request.delete(`/carpool/${carpoolId}`);
      
      return {
        success: true,
        message: '删除成功'
      };
    } catch (error) {
      console.error('删除拼车失败:', error);
      return {
        success: false,
        message: error.message || '删除失败，请重试'
      };
    }
  }

  /**
   * 获取我的拼车列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {string} params.status - 状态筛选
   * @returns {Promise} 我的拼车列表
   */
  async getMyCarpoolList(params = {}) {
    try {
      const queryParams = {
        page: params.page || 1,
        limit: params.limit || 10,
        status: params.status || null
      };

      const response = await request.get('/carpool/my', { params: queryParams });
      
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0,
        page: response.page || 1,
        limit: response.limit || 10
      };
    } catch (error) {
      console.error('获取我的拼车列表失败:', error);
      return {
        success: false,
        data: [],
        message: error.message || '获取我的拼车失败'
      };
    }
  }

  /**
   * 申请加入拼车
   * @param {number} carpoolId - 拼车ID
   * @param {Object} applicationData - 申请数据
   * @returns {Promise} 申请结果
   */
  async applyCarpool(carpoolId, applicationData) {
    try {
      const response = await request.post(`/carpool/${carpoolId}/apply`, applicationData);
      
      return {
        success: true,
        data: response.data,
        message: '申请成功'
      };
    } catch (error) {
      console.error('申请拼车失败:', error);
      return {
        success: false,
        message: error.message || '申请失败，请重试'
      };
    }
  }

  /**
   * 处理拼车申请
   * @param {number} applicationId - 申请ID
   * @param {string} action - 操作 (accept, reject)
   * @param {string} reason - 原因（拒绝时）
   * @returns {Promise} 处理结果
   */
  async handleApplication(applicationId, action, reason = '') {
    try {
      const response = await request.post(`/carpool/application/${applicationId}/${action}`, {
        reason: reason
      });
      
      return {
        success: true,
        message: action === 'accept' ? '已接受申请' : '已拒绝申请'
      };
    } catch (error) {
      console.error('处理申请失败:', error);
      return {
        success: false,
        message: error.message || '处理失败，请重试'
      };
    }
  }

  /**
   * 获取拼车申请列表
   * @param {number} carpoolId - 拼车ID
   * @returns {Promise} 申请列表
   */
  async getApplications(carpoolId) {
    try {
      const response = await request.get(`/carpool/${carpoolId}/applications`);
      
      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      console.error('获取申请列表失败:', error);
      return {
        success: false,
        data: [],
        message: error.message || '获取申请列表失败'
      };
    }
  }

  /**
   * 拼车置顶
   * @param {number} carpoolId - 拼车ID
   * @param {string} duration - 置顶时长 (1h, 3h, 7h)
   * @param {string} paymentType - 支付类型 (paid, ad)
   * @returns {Promise} 置顶结果
   */
  async topCarpool(carpoolId, duration, paymentType = 'paid') {
    try {
      const response = await request.post(`/carpool/${carpoolId}/top`, {
        duration: duration,
        payment_type: paymentType
      });
      
      return {
        success: true,
        data: response.data,
        message: '置顶成功'
      };
    } catch (error) {
      console.error('拼车置顶失败:', error);
      return {
        success: false,
        message: error.message || '置顶失败，请重试'
      };
    }
  }

  /**
   * 刷新拼车信息
   * @param {number} carpoolId - 拼车ID
   * @param {string} paymentType - 支付类型 (paid, ad)
   * @returns {Promise} 刷新结果
   */
  async refreshCarpool(carpoolId, paymentType = 'paid') {
    try {
      const response = await request.post(`/carpool/${carpoolId}/refresh`, {
        payment_type: paymentType
      });
      
      return {
        success: true,
        data: response.data,
        message: '刷新成功'
      };
    } catch (error) {
      console.error('刷新拼车失败:', error);
      return {
        success: false,
        message: error.message || '刷新失败，请重试'
      };
    }
  }

  /**
   * 获取热门路线
   * @returns {Promise} 热门路线列表
   */
  async getHotRoutes() {
    try {
      const response = await request.get('/carpool/hot-routes');
      
      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      console.error('获取热门路线失败:', error);
      return {
        success: false,
        data: [],
        message: error.message || '获取热门路线失败'
      };
    }
  }

  /**
   * 搜索拼车
   * @param {Object} searchParams - 搜索参数
   * @param {string} searchParams.departure - 出发地
   * @param {string} searchParams.destination - 目的地
   * @param {string} searchParams.date - 出发日期
   * @param {string} searchParams.type - 拼车类型
   * @returns {Promise} 搜索结果
   */
  async searchCarpool(searchParams) {
    try {
      const response = await request.get('/carpool/search', { params: searchParams });
      
      return {
        success: true,
        data: response.data || [],
        total: response.total || 0
      };
    } catch (error) {
      console.error('搜索拼车失败:', error);
      return {
        success: false,
        data: [],
        message: error.message || '搜索失败'
      };
    }
  }

  /**
   * 获取拼车统计信息
   * @returns {Promise} 统计信息
   */
  async getCarpoolStats() {
    try {
      const response = await request.get('/carpool/stats');
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return {
        success: false,
        data: {},
        message: error.message || '获取统计信息失败'
      };
    }
  }

  /**
   * 举报拼车信息
   * @param {number} carpoolId - 拼车ID
   * @param {Object} reportData - 举报数据
   * @returns {Promise} 举报结果
   */
  async reportCarpool(carpoolId, reportData) {
    try {
      const response = await request.post(`/carpool/${carpoolId}/report`, reportData);
      
      return {
        success: true,
        message: '举报成功，我们会尽快处理'
      };
    } catch (error) {
      console.error('举报失败:', error);
      return {
        success: false,
        message: error.message || '举报失败，请重试'
      };
    }
  }
}

// 创建单例实例
const carpoolApi = new CarpoolApiService();

export default carpoolApi;
