{"version": 3, "file": "index.js", "sources": ["carpool-package/pages/carpool/groups/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y2FycG9vbC1wYWNrYWdlXHBhZ2VzXGNhcnBvb2xcZ3JvdXBzXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 导航栏 -->\n    <view class=\"custom-header\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"header-content\">\n        <view class=\"left-action\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" class=\"action-icon back-icon\"></image>\n        </view>\n        <view class=\"title-area\">\n          <text class=\"page-title\">拼车群</text>\n        </view>\n        <view class=\"right-action\">\n          <image src=\"/static/images/tabbar/search.png\" class=\"action-icon\" @click=\"showSearch\"></image>\n        </view>\n      </view>\n    </view>\n\n    <!-- 搜索框 -->\n    <view class=\"search-section\" v-if=\"isSearchVisible\">\n      <view class=\"search-box\">\n        <image src=\"/static/images/tabbar/search.png\" mode=\"aspectFit\" class=\"search-icon\"></image>\n        <input type=\"text\" class=\"search-input\" placeholder=\"搜索拼车群\" v-model=\"searchKeyword\" confirm-type=\"search\" @confirm=\"searchGroups\" />\n        <view class=\"search-cancel\" @click=\"hideSearch\">取消</view>\n      </view>\n    </view>\n\n    <!-- 分类选项卡 -->\n    <view class=\"tabs-section\">\n      <scroll-view scroll-x class=\"tabs-scroll\" show-scrollbar=\"false\">\n        <view \n          class=\"tab-item\" \n          v-for=\"(tab, index) in tabs\" \n          :key=\"index\"\n          :class=\"{ active: currentTab === index }\"\n          @click=\"switchTab(index)\"\n        >\n          <text>{{ tab }}</text>\n          <view class=\"tab-indicator\" v-if=\"currentTab === index\"></view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 群组列表 -->\n    <scroll-view \n      scroll-y \n      class=\"groups-list\"\n      refresher-enabled\n      :refresher-triggered=\"isRefreshing\"\n      @refresherrefresh=\"onRefresh\"\n      @scrolltolower=\"loadMore\"\n    >\n      <view class=\"group-card\" v-for=\"(group, index) in filteredGroups\" :key=\"index\" @click=\"viewGroupDetail(group)\">\n        <view class=\"group-header\">\n          <image :src=\"group.avatar\" mode=\"aspectFill\" class=\"group-avatar\"></image>\n          <view class=\"group-info\">\n            <view class=\"group-name\">{{ group.name }}</view>\n            <view class=\"group-meta\">\n              <text class=\"group-members\">{{ group.memberCount }}人</text>\n              <text class=\"group-type\" :class=\"group.type\">{{ group.typeText }}</text>\n            </view>\n          </view>\n          <view class=\"group-join\" @click.stop=\"viewGroupDetail(group)\">\n            <text>查看</text>\n          </view>\n        </view>\n        <view class=\"group-content\">\n          <view class=\"group-description\">\n            <text v-if=\"!group._descExpand && group.description.length > 36\">{{ group.description.slice(0, 36) + '...' }}</text>\n            <text v-else>{{ group.description }}</text>\n            <text v-if=\"group.description.length > 36\" class=\"desc-toggle\" @click.stop=\"toggleDesc(group)\">{{ group._descExpand ? '收起' : '展开' }}</text>\n          </view>\n          <scroll-view class=\"group-tags-scroll\" scroll-x enhanced show-scrollbar=\"false\">\n            <view class=\"group-tags\">\n              <view class=\"tag\" v-for=\"(tag, tagIndex) in group.tags\" :key=\"tagIndex\">{{ tag }}</view>\n            </view>\n          </scroll-view>\n        </view>\n        <view class=\"group-footer\">\n          <view class=\"route-info\" v-if=\"group.routeInfo\">\n            <view class=\"route-icon\">\n              <image src=\"/static/images/tabbar/route.png\" mode=\"aspectFit\"></image>\n            </view>\n            <view class=\"route-text\">{{ group.routeInfo }}</view>\n          </view>\n          <view class=\"group-activity\">\n            <text>{{ group.lastActiveTime }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 加载状态 -->\n      <view class=\"loading-section\" v-if=\"isLoading\">\n        <view class=\"loading-spinner\"></view>\n        <text>加载中...</text>\n      </view>\n\n      <!-- 无数据状态 -->\n      <view class=\"empty-section\" v-if=\"filteredGroups.length === 0 && !isLoading\">\n        <image src=\"/static/images/tabbar/empty-groups.png\" mode=\"aspectFit\"></image>\n        <text>暂无相关拼车群</text>\n        <button class=\"create-btn\" @click=\"createGroup\" v-if=\"isAdmin\">创建拼车群</button>\n        <text v-else class=\"empty-tip\">拼车群由管理员创建</text>\n      </view>\n    </scroll-view>\n\n    <!-- 悬浮创建按钮 - 仅管理员可见 -->\n    <view class=\"floating-btn\" @click=\"createGroup\" v-if=\"isAdmin\">\n      <image src=\"/static/images/tabbar/plus.png\" mode=\"aspectFit\"></image>\n    </view>\n    \n    <!-- 底部导航栏 -->\n    <view class=\"tabbar\">\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'home' }\" @click=\"navigateToPage('home')\">\n        <image :src=\"activeTab === 'home' ? '/static/images/tabbar/p首页选中.png' : '/static/images/tabbar/p首页.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'home' }\">同城</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'carpool' }\" @click=\"navigateToPage('carpool')\">\n        <image :src=\"activeTab === 'carpool' ? '/static/images/tabbar/p拼车选中.png' : '/static/images/tabbar/p拼车.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'carpool' }\">拼车</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'publish' }\" @click=\"publishCarpool\">\n        <image :src=\"activeTab === 'publish' ? '/static/images/tabbar/p发布选中.png' : '/static/images/tabbar/p发布.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'publish' }\">发布</text>\n      </view>\n      <view class=\"tabbar-item active\">\n        <image src=\"/static/images/tabbar/p拼车群选中.png\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text active-text\">拼车群</text>\n      </view>\n      <view class=\"tabbar-item\" :class=\"{ active: activeTab === 'my' }\" @click=\"navigateToPage('my')\">\n        <image :src=\"activeTab === 'my' ? '/static/images/tabbar/p我的选中.png' : '/static/images/tabbar/p我的.png'\" mode=\"aspectFit\" class=\"tabbar-icon\"></image>\n        <text class=\"tabbar-text\" :class=\"{ 'active-text': activeTab === 'my' }\">我的</text>\n      </view>\n    </view>\n    \n    <!-- 群二维码弹窗 -->\n    <view class=\"qrcode-popup\" v-if=\"showQRCode\" @click.stop=\"hideQRCode\">\n      <view class=\"qrcode-container\" @click.stop>\n        <view class=\"qrcode-header\">\n          <text class=\"qrcode-title\">{{ currentGroup ? currentGroup.name : '拼车群' }}</text>\n          <view class=\"qrcode-close\" @click=\"hideQRCode\">×</view>\n        </view>\n        <view class=\"group-info-section\" v-if=\"currentGroup\">\n          <view class=\"group-route\">\n            <view class=\"route-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#0A84FF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M3 12h18M3 6h18M3 18h18\"></path>\n              </svg>\n            </view>\n            <text class=\"route-text\">{{ currentGroup.routeInfo }}</text>\n          </view>\n          <view class=\"group-tags-display\">\n            <view class=\"tag-display\" v-for=\"(tag, tagIndex) in currentGroup.tags\" :key=\"tagIndex\">{{ tag }}</view>\n          </view>\n          <view class=\"group-desc\">\n            <text>{{ currentGroup.description }}</text>\n          </view>\n          <view class=\"group-members\">\n            <text class=\"members-count\">{{ currentGroup.memberCount }}人</text>\n            <text class=\"activity-time\">{{ currentGroup.lastActiveTime }}活跃</text>\n          </view>\n        </view>\n        <view class=\"qrcode-content\">\n          <image :src=\"currentGroup ? currentGroup.qrcode || '/static/images/tabbar/default-qrcode.png' : '/static/images/tabbar/default-qrcode.png'\" mode=\"aspectFit\" class=\"qrcode-image\"></image>\n          <text class=\"qrcode-tips\">扫描微信二维码加入拼车群</text>\n        </view>\n        <view class=\"qrcode-footer\">\n          <button class=\"qrcode-save-btn\" @click=\"saveQRCode\">保存二维码</button>\n          <button class=\"qrcode-share-btn\" @click=\"shareQRCode\">分享给好友</button>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 移除聊天对话框，只保留二维码弹窗 -->\n    \n    <!-- 底部安全区域 -->\n    <view class=\"safe-area-bottom\"></view>\n    \n    <!-- 发布类型选择弹窗 -->\n    <view class=\"publish-popup\" v-if=\"showPublishPopup\" @click=\"closePublishPopup\">\n      <view class=\"publish-card\" @click.stop>\n        <view class=\"publish-header\">\n          <text class=\"publish-title\">选择发布类型</text>\n          <view class=\"close-btn\" @click=\"closePublishPopup\">\n            <text class=\"close-icon\">×</text>\n          </view>\n        </view>\n        <view class=\"publish-options\">\n          <view class=\"publish-option\" @click=\"navigateToPublish('people-to-car')\">\n            <view class=\"option-icon-wrapper people-car\">\n              <image src=\"/static/images/tabbar/人找车.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n            </view>\n            <text class=\"option-text\">人找车</text>\n          </view>\n          <view class=\"publish-option\" @click=\"navigateToPublish('car-to-people')\">\n            <view class=\"option-icon-wrapper car-people\">\n              <image src=\"/static/images/tabbar/车找人.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n            </view>\n            <text class=\"option-text\">车找人</text>\n          </view>\n          <view class=\"publish-option\" @click=\"navigateToPublish('goods-to-car')\">\n            <view class=\"option-icon-wrapper goods-car\">\n              <image src=\"/static/images/tabbar/货找车.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n            </view>\n            <text class=\"option-text\">货找车</text>\n          </view>\n          <view class=\"publish-option\" @click=\"navigateToPublish('car-to-goods')\">\n            <view class=\"option-icon-wrapper car-goods\">\n              <image src=\"/static/images/tabbar/车找货.png\" mode=\"aspectFit\" class=\"option-icon\"></image>\n            </view>\n            <text class=\"option-text\">车找货</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\nimport { onShow, onHide, onUnload } from '@dcloudio/uni-app';\n\n// 搜索相关\nconst isSearchVisible = ref(false);\nconst searchKeyword = ref('');\n\n// 标签和分页\nconst currentTab = ref(0);\nconst tabs = ref(['全部', '本地', '长途', '通勤', '我的群']);\nconst isRefreshing = ref(false);\nconst isLoading = ref(false);\nconst hasMore = ref(true);\nconst page = ref(1);\n\n// 当前激活的tab\nconst activeTab = ref('groups');\n\n// 弹窗控制\nconst showQRCode = ref(false); // 是否显示二维码弹窗\nconst currentGroup = ref(null); // 当前选中的群组\nconst showPublishPopup = ref(false); // 添加发布弹窗控制变量\n\n// 状态栏高度\nconst statusBarHeight = ref(20);\n\n// 用户是否为管理员\nconst isAdmin = ref(false);\n\n// 移除聊天消息数据\n\n// 群组数据\nconst groups = ref([\n  {\n    id: 1,\n    name: '磁州-邯郸拼车群',\n    avatar: '/static/images/tabbar/group-avatar1.png',\n    memberCount: 128,\n    type: 'local',\n    typeText: '本地',\n    description: '磁州到邯郸日常拼车，安全出行，拼车省钱',\n    tags: ['安全', '准时', '舒适'],\n    routeInfo: '磁州 → 邯郸',\n    lastActiveTime: '10分钟前',\n    isJoined: true,\n    qrcode: '/static/images/tabbar/qrcode1.png'\n  },\n  {\n    id: 2,\n    name: '磁州-石家庄通勤群',\n    avatar: '/static/images/tabbar/group-avatar2.png',\n    memberCount: 86,\n    type: 'commute',\n    typeText: '通勤',\n    description: '工作日通勤拼车，每天准时发车，欢迎加入',\n    tags: ['通勤', '准时', '固定'],\n    routeInfo: '磁州 → 石家庄',\n    lastActiveTime: '30分钟前',\n    isJoined: false,\n    qrcode: '/static/images/tabbar/qrcode2.png'\n  },\n  {\n    id: 3,\n    name: '磁州-北京长途拼车',\n    avatar: '/static/images/tabbar/group-avatar3.png',\n    memberCount: 64,\n    type: 'longDistance',\n    typeText: '长途',\n    description: '周末和节假日磁州到北京长途拼车，舒适安全',\n    tags: ['长途', '舒适', '周末'],\n    routeInfo: '磁州 → 北京',\n    lastActiveTime: '2小时前',\n    isJoined: false,\n    qrcode: '/static/images/tabbar/qrcode3.png'\n  },\n  {\n    id: 4,\n    name: '磁州本地拼车群',\n    avatar: '/static/images/tabbar/group-avatar4.png',\n    memberCount: 256,\n    type: 'local',\n    typeText: '本地',\n    description: '磁州城区及周边拼车，随时发车，方便快捷',\n    tags: ['本地', '灵活', '快捷'],\n    routeInfo: '磁州城区',\n    lastActiveTime: '1小时前',\n    isJoined: true,\n    qrcode: '/static/images/tabbar/qrcode4.png'\n  }\n]);\n\n// 计算属性 - 筛选后的群组\nconst filteredGroups = computed(() => {\n  let result = groups.value;\n  \n  // 根据标签筛选\n  if (currentTab.value > 0) {\n    const tabType = ['', 'local', 'longDistance', 'commute', ''][currentTab.value];\n    if (currentTab.value === 4) {\n      // 我的群\n      result = result.filter(group => group.isJoined);\n    } else if (tabType) {\n      result = result.filter(group => group.type === tabType);\n    }\n  }\n  \n  // 根据搜索关键词筛选\n  if (searchKeyword.value) {\n    const keyword = searchKeyword.value.toLowerCase();\n    result = result.filter(group => \n      group.name.toLowerCase().includes(keyword) || \n      group.description.toLowerCase().includes(keyword) ||\n      group.routeInfo?.toLowerCase().includes(keyword) ||\n      group.tags.some(tag => tag.toLowerCase().includes(keyword))\n    );\n  }\n  \n  return result;\n});\n\n// 生命周期钩子\nonMounted(() => {\n  loadGroups();\n  // 隐藏原生tabBar\n  uni.hideTabBar();\n  // 获取状态栏高度\n  const systemInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = systemInfo.statusBarHeight;\n  \n  // 检查用户是否为管理员\n  const userInfo = uni.getStorageSync('userInfo') || {};\n  isAdmin.value = !!userInfo.isAdmin;\n});\n\nonShow(() => {\n  // 确保每次页面显示时都隐藏原生tabBar\n  uni.hideTabBar();\n});\n\nonHide(() => {\n  // 页面隐藏时，不需要显示原生tabBar，因为返回到拼车主页也是隐藏状态\n});\n\nonUnload(() => {\n  // 如果不是返回到拼车主页，需要显示默认tabBar\n  const pages = getCurrentPages();\n  const prevPage = pages[pages.length - 2];\n  if (!prevPage || prevPage.route !== 'carpool-package/pages/carpool-main/index') {\n    uni.showTabBar();\n  }\n});\n\n// 方法\nconst goBack = () => {\n  uni.navigateBack({\n    fail: () => {\n      // 如果返回失败，则跳转到拼车主页\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool-main/index'\n      });\n    }\n  });\n};\n\nconst showSearch = () => {\n  isSearchVisible.value = true;\n};\n\nconst hideSearch = () => {\n  isSearchVisible.value = false;\n  searchKeyword.value = '';\n};\n\nconst switchTab = (index) => {\n  currentTab.value = index;\n};\n\nconst searchGroups = () => {\n  console.log('搜索关键词：', searchKeyword.value);\n  // 实际应用中这里应该调用API进行搜索\n};\n\nconst loadGroups = () => {\n  isLoading.value = true;\n  \n  // 模拟API请求\n  setTimeout(() => {\n    // 在实际应用中，这里应该是API请求获取数据\n    isLoading.value = false;\n    hasMore.value = page.value < 3; // 模拟只有3页数据\n    page.value++;\n  }, 1000);\n};\n\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  page.value = 1;\n  \n  // 模拟刷新\n  setTimeout(() => {\n    // 在实际应用中，这里应该是API请求刷新数据\n    isRefreshing.value = false;\n    uni.showToast({\n      title: '刷新成功',\n      icon: 'success'\n    });\n  }, 1000);\n};\n\nconst loadMore = () => {\n  if (hasMore.value && !isLoading.value) {\n    loadGroups();\n  }\n};\n\nconst viewGroupDetail = (group) => {\n  // 直接显示二维码弹窗\n  currentGroup.value = group;\n  showQRCode.value = true;\n};\n\n// 移除聊天相关函数，只保留二维码展示功能\n\n// 已合并到viewGroupDetail函数中\n\nconst hideQRCode = () => {\n  showQRCode.value = false;\n  currentGroup.value = null;\n};\n\nconst saveQRCode = () => {\n  // 保存二维码到相册\n  if (currentGroup.value && currentGroup.value.qrcode) {\n    uni.saveImageToPhotosAlbum({\n      filePath: currentGroup.value.qrcode,\n      success: () => {\n        uni.showToast({\n          title: '二维码已保存到相册',\n          icon: 'success'\n        });\n      },\n      fail: () => {\n        uni.showToast({\n          title: '保存失败，请检查权限',\n          icon: 'none'\n        });\n      }\n    });\n  }\n};\n\nconst shareQRCode = () => {\n  // 分享二维码\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 2,\n    imageUrl: currentGroup.value ? currentGroup.value.qrcode : '',\n    title: `邀请您加入${currentGroup.value ? currentGroup.value.name : '拼车群'}`,\n    success: function() {\n      uni.showToast({\n        title: '分享成功',\n        icon: 'success'\n      });\n    },\n    fail: function() {\n      uni.showToast({\n        title: '分享失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\nconst createGroup = () => {\n  // 检查用户是否为管理员\n  const userInfo = uni.getStorageSync('userInfo') || {};\n  if (userInfo.isAdmin) {\n    uni.navigateTo({\n      url: '/pages/carpool/groups/create'\n    });\n  } else {\n    uni.showModal({\n      title: '提示',\n      content: '只有管理员可以创建拼车群',\n      showCancel: false\n    });\n  }\n};\n\nconst navigateToPage = (page) => {\n  // 避免重复点击当前页面\n  if (page === 'groups') return;\n  \n  activeTab.value = page; // 更新激活的标签\n  \n  switch(page) {\n    case 'home':\n      // 确保在切换到首页之前恢复系统tabBar\n      uni.showTabBar();\n      setTimeout(() => {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n      }, 50);\n      break;\n    case 'carpool':\n      // 隐藏tabBar确保不会在跳转过程中闪烁\n      uni.hideTabBar();\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool-main/index'\n      });\n      break;\n    case 'my':\n      // 隐藏tabBar确保不会在跳转过程中闪烁\n      uni.hideTabBar();\n      uni.navigateTo({\n        url: '/carpool-package/pages/carpool/my/index'\n      });\n      break;\n  }\n};\n\nconst publishCarpool = () => {\n  // 显示发布选择弹窗，而不是直接跳转\n  showPublishPopup.value = true;\n  activeTab.value = 'publish'; // 设置发布按钮为激活状态\n};\n\n// 添加选择发布类型的方法\nconst navigateToPublish = (type) => {\n  showPublishPopup.value = false;\n  \n  // 根据类型直接导航到相应的表单页面\n  let url = '';\n  switch(type) {\n    case 'people-to-car':\n      url = '/carpool-package/pages/carpool/publish/people-to-car';\n      break;\n    case 'car-to-people':\n      url = '/carpool-package/pages/carpool/publish/car-to-people';\n      break;\n    case 'goods-to-car':\n      url = '/carpool-package/pages/carpool/publish/goods-to-car';\n      break;\n    case 'car-to-goods':\n      url = '/carpool-package/pages/carpool/publish/car-to-goods';\n      break;\n    default:\n      url = '/carpool-package/pages/carpool/publish/people-to-car';\n  }\n  \n  uni.navigateTo({\n    url\n  });\n};\n\n// 关闭发布选择弹窗\nconst closePublishPopup = () => {\n  showPublishPopup.value = false;\n  activeTab.value = 'groups'; // 重置为拼车群标签\n};\n\nconst toggleDesc = (group) => {\n  // Vue 3 中不再需要 $set，直接设置响应式属性\n  group._descExpand = !group._descExpand;\n};\n\nconst cardTapAnim = (e) => {\n  e.currentTarget.classList.add('card-tap-anim');\n  setTimeout(() => e.currentTarget.classList.remove('card-tap-anim'), 180);\n};\n\nconst joinGroupAnim = (e, group) => {\n  e.currentTarget.classList.add('btn-tap-anim');\n  setTimeout(() => e.currentTarget.classList.remove('btn-tap-anim'), 180);\n  joinGroup(group);\n};\n</script>\n\n<style lang=\"scss\">\n.container {\n  min-height: 100vh;\n  background-color: #F5F8FC;\n  padding-bottom: 140rpx;\n  padding-top: calc(var(--status-bar-height) + 90rpx + 10rpx); /* 标题栏高度 + 10rpx间距 */\n  position: relative;\n  overflow-x: hidden;\n}\n\n/* 自定义标题栏模块 */\n.custom-header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  width: 100%;\n  background-color: #1677FF; /* 恢复为实色背景 */\n  z-index: 103;\n  box-shadow: none;\n}\n\n/* 标题栏内容 */\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 90rpx;\n  padding: 0 30rpx;\n  position: relative;\n  z-index: 102;\n}\n\n.left-action {\n  width: 70rpx;\n  height: 70rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.title-area {\n  flex: 1;\n  text-align: center;\n}\n\n.page-title {\n  color: #ffffff;\n  font-size: 36rpx;\n  font-weight: 600;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.right-action {\n  width: 70rpx;\n  height: 70rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-icon {\n  width: 44rpx;\n  height: 44rpx;\n  filter: brightness(0) invert(1);\n}\n\n/* 搜索框 */\n.search-section {\n  padding: 20rpx;\n  background-color: #ffffff;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  background-color: #f5f7fa;\n  border-radius: 40rpx;\n  padding: 0 24rpx;\n  height: 80rpx;\n}\n\n.search-icon {\n  width: 36rpx;\n  height: 36rpx;\n  margin-right: 16rpx;\n  opacity: 0.6;\n}\n\n.search-input {\n  flex: 1;\n  height: 80rpx;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.search-cancel {\n  font-size: 28rpx;\n  color: #4f8cff;\n  margin-left: 20rpx;\n  font-weight: 500;\n}\n\n/* 分类选项卡 */\n.tabs-section {\n  position: sticky;\n  top: calc(var(--status-bar-height) + 90rpx);\n  left: 0;\n  right: 0;\n  background-color: #FFFFFF; /* 改为白色背景，卡片样式 */\n  z-index: 90;\n  height: 86rpx;\n  display: flex;\n  align-items: center;\n  border-bottom: none;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05); /* 添加轻微阴影，增强卡片效果 */\n  width: 100%;\n  border-radius: 16rpx; /* 添加圆角 */\n  margin: 10rpx 30rpx; /* 上下间距改为10rpx */\n  width: calc(100% - 60rpx); /* 减去左右外边距 */\n}\n\n.tabs-scroll {\n  white-space: nowrap;\n  padding: 0;\n  height: 86rpx; /* 与tabs-section高度一致 */\n  border-bottom: none;\n  box-shadow: none;\n  width: 100%;\n}\n\n.tab-item {\n  display: inline-block;\n  padding: 28rpx 30rpx;\n  font-size: 30rpx;\n  color: #666666; /* 改为深灰色文字 */\n  position: relative;\n  transition: all 0.3s ease;\n  height: 86rpx;\n  box-sizing: border-box;\n  border-bottom: none;\n  text-align: center;\n  min-width: 20%;\n}\n\n.tab-item.active {\n  color: #1677FF; /* 激活状态为蓝色 */\n  font-weight: 600;\n}\n\n.tab-indicator {\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40rpx;\n  height: 6rpx;\n  background: #1677FF; /* 蓝色指示器 */\n  border-radius: 6rpx;\n}\n\n/* 群组列表 */\n.groups-list {\n  height: calc(100vh - 210rpx - var(--status-bar-height) - 90rpx);\n  padding: 0 30rpx;\n  padding-top: 106rpx; /* 设置顶部内边距为分类标签的高度加上一些额外空间 */\n  box-sizing: border-box;\n  margin-top: 0;\n}\n\n.group-card {\n  background-color: #ffffff;\n  border-radius: 32rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.06);\n  width: 100%;\n  box-sizing: border-box;\n  transform: translateZ(0);\n  transition: transform 0.2s, box-shadow 0.2s;\n  overflow: hidden;\n}\n\n.group-card:active {\n  transform: scale(0.98);\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.04);\n}\n\n.group-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24rpx;\n}\n\n.group-avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 24rpx;\n  margin-right: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);\n  border: 2rpx solid #f0f4f8;\n}\n\n.group-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.group-name {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #222;\n  margin-bottom: 12rpx;\n  line-height: 1.3;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n\n.group-meta {\n  display: flex;\n  align-items: center;\n}\n\n.group-members {\n  font-size: 24rpx;\n  color: #999;\n  margin-right: 16rpx;\n}\n\n.group-type {\n  font-size: 22rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n  background-color: rgba(0,0,0,0.05);\n  color: #666;\n}\n\n.group-type.local {\n  background-color: rgba(25,118,210,0.1);\n  color: #1976d2;\n}\n\n.group-type.longDistance {\n  background-color: rgba(156,39,176,0.1);\n  color: #9c27b0;\n}\n\n.group-type.commute {\n  background-color: rgba(76,175,80,0.1);\n  color: #4caf50;\n}\n\n.group-join {\n  background: linear-gradient(135deg, #4f8cff, #6fc3ff);\n  color: #ffffff;\n  padding: 16rpx 40rpx;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  box-shadow: 0 4rpx 16rpx rgba(79,140,255,0.2);\n  margin-left: 16rpx;\n  transition: transform 0.15s, box-shadow 0.15s;\n}\n\n.group-join:active {\n  transform: scale(0.95);\n  box-shadow: 0 2rpx 8rpx rgba(79,140,255,0.15);\n}\n\n/* 移除已加入样式，统一为查看按钮 */\n\n.group-content {\n  margin-bottom: 24rpx;\n}\n\n.group-description {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 20rpx;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.desc-toggle {\n  color: #4f8cff;\n  font-size: 24rpx;\n  margin-left: 12rpx;\n  font-weight: 500;\n}\n\n.group-tags-scroll {\n  width: 100%;\n  height: 60rpx;\n}\n\n.group-tags {\n  display: inline-flex;\n  flex-wrap: nowrap;\n  padding: 4rpx 0;\n}\n\n.tag {\n  font-size: 24rpx;\n  color: #4f8cff;\n  background: linear-gradient(135deg, #eaf3ff, #d6eaff);\n  padding: 8rpx 24rpx;\n  border-radius: 20rpx;\n  margin-right: 16rpx;\n  white-space: nowrap;\n  font-weight: 500;\n}\n\n.group-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 20rpx;\n  border-top: 1rpx solid #f0f4f8;\n}\n\n.route-info {\n  display: flex;\n  align-items: center;\n}\n\n.route-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 10rpx;\n}\n\n.route-icon image {\n  width: 100%;\n  height: 100%;\n  opacity: 0.6;\n}\n\n.route-text {\n  font-size: 26rpx;\n  color: #999;\n}\n\n.group-activity {\n  font-size: 24rpx;\n  color: #bbb;\n}\n\n/* 加载状态 */\n.loading-section {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60rpx 0;\n}\n\n.loading-spinner {\n  width: 60rpx;\n  height: 60rpx;\n  border: 4rpx solid #f0f4f8;\n  border-top: 4rpx solid #4f8cff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20rpx;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 空状态 */\n.empty-section {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 0;\n}\n\n.empty-section image {\n  width: 240rpx;\n  height: 240rpx;\n  margin-bottom: 40rpx;\n  opacity: 0.8;\n}\n\n.empty-section text {\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 40rpx;\n}\n\n.create-btn {\n  background: linear-gradient(135deg, #4f8cff, #6fc3ff);\n  color: #ffffff;\n  padding: 20rpx 60rpx;\n  border-radius: 40rpx;\n  font-size: 30rpx;\n  font-weight: 600;\n  box-shadow: 0 6rpx 16rpx rgba(79,140,255,0.25);\n  letter-spacing: 2rpx;\n}\n\n/* 悬浮按钮 */\n.floating-btn {\n  position: fixed;\n  right: 40rpx;\n  bottom: 160rpx;\n  width: 110rpx;\n  height: 110rpx;\n  background: linear-gradient(135deg, #4f8cff, #6fc3ff);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 24rpx rgba(79,140,255,0.3);\n  z-index: 99;\n  transition: transform 0.15s, box-shadow 0.15s;\n}\n\n.floating-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 4rpx 12rpx rgba(79,140,255,0.2);\n}\n\n.floating-btn image {\n  width: 50rpx;\n  height: 50rpx;\n  filter: brightness(0) invert(1);\n}\n\n/* 底部导航栏 */\n.tabbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 110rpx;\n  background-color: rgba(255, 255, 255, 0.95);\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\n  z-index: 9999;\n  padding-bottom: env(safe-area-inset-bottom);\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n}\n\n.tabbar-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 10rpx 0;\n  position: relative;\n  transition: all 0.2s ease;\n}\n\n.tabbar-item:active {\n  opacity: 0.7;\n}\n\n.tabbar-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-bottom: 6rpx;\n}\n\n.tabbar-text {\n  font-size: 24rpx;\n  color: #999;\n  line-height: 1;\n}\n\n.active-text {\n  color: #4f8cff;\n  font-weight: 600;\n}\n\n/* 底部安全区域 */\n.safe-area-bottom {\n  height: env(safe-area-inset-bottom);\n  width: 100%;\n}\n\n/* 群二维码弹窗 */\n.qrcode-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.qrcode-container {\n  width: 650rpx;\n  background-color: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);\n  animation: popup 0.3s ease;\n}\n\n@keyframes popup {\n  from {\n    transform: scale(0.8);\n    opacity: 0;\n  }\n  to {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n.qrcode-header {\n  padding: 30rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1rpx solid #f0f4f8;\n}\n\n.qrcode-title {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #333;\n}\n\n.qrcode-close {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 48rpx;\n  color: #999;\n}\n\n.group-info-section {\n  padding: 30rpx;\n  border-bottom: 1rpx solid #f0f4f8;\n}\n\n.group-route {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.route-icon {\n  margin-right: 10rpx;\n  display: flex;\n  align-items: center;\n}\n\n.route-text {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 600;\n}\n\n.group-tags-display {\n  display: flex;\n  flex-wrap: wrap;\n  margin-bottom: 20rpx;\n}\n\n.tag-display {\n  padding: 6rpx 16rpx;\n  background-color: #E6F2FF;\n  color: #0A84FF;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  margin-right: 12rpx;\n  margin-bottom: 10rpx;\n}\n\n.group-desc {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.5;\n  margin-bottom: 20rpx;\n}\n\n.group-members {\n  display: flex;\n  justify-content: space-between;\n  font-size: 24rpx;\n  color: #999;\n}\n\n.members-count {\n  color: #0A84FF;\n}\n\n.qrcode-content {\n  padding: 30rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.qrcode-image {\n  width: 350rpx;\n  height: 350rpx;\n  margin-bottom: 20rpx;\n  border: 1rpx solid #f0f4f8;\n  border-radius: 12rpx;\n}\n\n.qrcode-tips {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.qrcode-footer {\n  padding: 30rpx;\n  display: flex;\n  justify-content: space-between;\n  border-top: 1rpx solid #f0f4f8;\n}\n\n.qrcode-save-btn, .qrcode-share-btn {\n  flex: 1;\n  height: 80rpx;\n  line-height: 80rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n  border-radius: 40rpx;\n}\n\n.qrcode-save-btn {\n  background-color: #f5f7fa;\n  color: #666;\n  margin-right: 20rpx;\n}\n\n.qrcode-share-btn {\n  background: linear-gradient(135deg, #4f8cff, #6fc3ff);\n  color: #fff;\n}\n\n/* 聊天对话框 */\n.chat-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.chat-container {\n  width: 700rpx;\n  height: 900rpx;\n  background-color: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);\n  animation: popup 0.3s ease;\n  display: flex;\n  flex-direction: column;\n}\n\n.chat-header {\n  padding: 30rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1rpx solid #f0f4f8;\n  background: linear-gradient(135deg, #4f8cff, #6fc3ff);\n}\n\n.chat-title {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #fff;\n}\n\n.chat-close {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 48rpx;\n  color: #fff;\n}\n\n.chat-messages {\n  flex: 1;\n  padding: 20rpx;\n  background-color: #f5f7fa;\n  overflow: hidden;\n}\n\n.messages-scroll {\n  height: 100%;\n}\n\n.message-item {\n  display: flex;\n  margin-bottom: 30rpx;\n  align-items: flex-start;\n}\n\n.message-mine {\n  flex-direction: row-reverse;\n}\n\n.message-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin: 0 20rpx;\n  border: 2rpx solid #f0f4f8;\n}\n\n.message-avatar.mine {\n  margin-right: 0;\n}\n\n.message-content {\n  max-width: 70%;\n  border-radius: 20rpx;\n  padding: 20rpx;\n  background-color: #fff;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.message-mine .message-content {\n  background: linear-gradient(135deg, #4f8cff, #6fc3ff);\n}\n\n.message-text {\n  font-size: 28rpx;\n  color: #333;\n  line-height: 1.5;\n}\n\n.message-mine .message-text {\n  color: #fff;\n}\n\n.location-content {\n  width: 400rpx;\n}\n\n.location-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.location-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 10rpx;\n}\n\n.message-mine .location-icon {\n  filter: brightness(0) invert(1);\n}\n\n.location-address {\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.message-mine .location-address {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.location-preview {\n  width: 100%;\n  height: 200rpx;\n  border-radius: 12rpx;\n  overflow: hidden;\n}\n\n.map-preview {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.chat-input-area {\n  padding: 20rpx;\n  border-top: 1rpx solid #f0f4f8;\n}\n\n.chat-toolbar {\n  display: flex;\n  padding-bottom: 20rpx;\n}\n\n.toolbar-icon {\n  width: 48rpx;\n  height: 48rpx;\n  margin-right: 30rpx;\n  opacity: 0.7;\n}\n\n.chat-input-box {\n  display: flex;\n  align-items: center;\n}\n\n.chat-input {\n  flex: 1;\n  height: 80rpx;\n  background-color: #f5f7fa;\n  border-radius: 40rpx;\n  padding: 0 30rpx;\n  font-size: 28rpx;\n}\n\n.send-btn {\n  width: 120rpx;\n  height: 80rpx;\n  background: linear-gradient(135deg, #4f8cff, #6fc3ff);\n  color: #fff;\n  border-radius: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 20rpx;\n  font-size: 28rpx;\n  font-weight: 600;\n}\n\n/* 发布类型选择弹窗样式 */\n.publish-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(8px);\n  -webkit-backdrop-filter: blur(8px);\n}\n\n.publish-card {\n  width: 80%;\n  background-color: #FFFFFF;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);\n  animation: popup-in 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;\n  transform: scale(0.95);\n  opacity: 0.8;\n}\n\n@keyframes popup-in {\n  0% {\n    transform: scale(0.95);\n    opacity: 0.8;\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n.publish-header {\n  padding: 24rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #F2F2F7;\n}\n\n.publish-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333333;\n}\n\n.close-btn {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 24rpx;\n  background-color: #F2F2F7;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.close-icon {\n  font-size: 32rpx;\n  color: #999999;\n}\n\n.publish-options {\n  padding: 32rpx;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 32rpx;\n}\n\n.publish-option {\n  width: calc(50% - 16rpx);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16rpx;\n  padding: 24rpx 0;\n  border-radius: 16rpx;\n  transition: all 0.3s ease;\n}\n\n.publish-option:active {\n  transform: scale(0.95);\n  background-color: #F5F8FC;\n}\n\n.option-text {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n}\n\n.option-icon-wrapper {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 12rpx;\n  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.12);\n  border: 2rpx solid rgba(255, 255, 255, 0.7);\n  position: relative;\n  overflow: hidden;\n}\n\n.option-icon-wrapper::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.3), transparent 70%);\n  z-index: 1;\n}\n\n.option-icon {\n  width: 60rpx;\n  height: 60rpx;\n  filter: brightness(0) invert(1);\n  position: relative;\n  z-index: 2;\n}\n\n.people-car {\n  background: linear-gradient(135deg, #0A84FF, #5AC8FA);\n}\n\n.car-people {\n  background: linear-gradient(135deg, #FF2D55, #FF9500);\n}\n\n.goods-car {\n  background: linear-gradient(135deg, #30D158, #34C759);\n}\n\n.car-goods {\n  background: linear-gradient(135deg, #FF9F0A, #FFD60A);\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/carpool-package/pages/carpool/groups/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "onShow", "onHide", "onUnload", "page", "MiniProgramPage"], "mappings": ";;;;;;;;;;;AA8NA,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAG5B,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,OAAOA,cAAAA,IAAI,CAAC,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC;AAChD,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,OAAOA,cAAAA,IAAI,CAAC;AAGlB,UAAM,YAAYA,cAAAA,IAAI,QAAQ;AAG9B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,eAAeA,cAAAA,IAAI,IAAI;AAC7B,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAGlC,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAG9B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AAKzB,UAAM,SAASA,cAAAA,IAAI;AAAA,MACjB;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,QACvB,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AAGD,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AACpC,UAAI,SAAS,OAAO;AAGpB,UAAI,WAAW,QAAQ,GAAG;AACxB,cAAM,UAAU,CAAC,IAAI,SAAS,gBAAgB,WAAW,EAAE,EAAE,WAAW,KAAK;AAC7E,YAAI,WAAW,UAAU,GAAG;AAE1B,mBAAS,OAAO,OAAO,WAAS,MAAM,QAAQ;AAAA,QAC/C,WAAU,SAAS;AAClB,mBAAS,OAAO,OAAO,WAAS,MAAM,SAAS,OAAO;AAAA,QACvD;AAAA,MACF;AAGD,UAAI,cAAc,OAAO;AACvB,cAAM,UAAU,cAAc,MAAM,YAAW;AAC/C,iBAAS,OAAO;AAAA,UAAO,WAAK;;AAC1B,yBAAM,KAAK,cAAc,SAAS,OAAO,KACzC,MAAM,YAAY,cAAc,SAAS,OAAO,OAChD,WAAM,cAAN,mBAAiB,cAAc,SAAS,aACxC,MAAM,KAAK,KAAK,SAAO,IAAI,YAAa,EAAC,SAAS,OAAO,CAAC;AAAA;AAAA,QAChE;AAAA,MACG;AAED,aAAO;AAAA,IACT,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACd;AAEAC,oBAAG,MAAC,WAAU;AAEd,YAAM,aAAaA,oBAAI;AACvB,sBAAgB,QAAQ,WAAW;AAGnC,YAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,cAAQ,QAAQ,CAAC,CAAC,SAAS;AAAA,IAC7B,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AAEXD,oBAAG,MAAC,WAAU;AAAA,IAChB,CAAC;AAEDE,kBAAAA,OAAO,MAAM;AAAA,IAEb,CAAC;AAEDC,kBAAAA,SAAS,MAAM;AAEb,YAAM,QAAQ;AACd,YAAM,WAAW,MAAM,MAAM,SAAS,CAAC;AACvC,UAAI,CAAC,YAAY,SAAS,UAAU,4CAA4C;AAC9EH,sBAAG,MAAC,WAAU;AAAA,MACf;AAAA,IACH,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM,MAAM;AAEVA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,aAAa,MAAM;AACvB,sBAAgB,QAAQ;AAAA,IAC1B;AAEA,UAAM,aAAa,MAAM;AACvB,sBAAgB,QAAQ;AACxB,oBAAc,QAAQ;AAAA,IACxB;AAEA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAEA,UAAM,eAAe,MAAM;AACzBA,oBAAA,MAAA,MAAA,OAAA,yDAAY,UAAU,cAAc,KAAK;AAAA,IAE3C;AAEA,UAAM,aAAa,MAAM;AACvB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAEf,kBAAU,QAAQ;AAClB,gBAAQ,QAAQ,KAAK,QAAQ;AAC7B,aAAK;AAAA,MACN,GAAE,GAAI;AAAA,IACT;AAEA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,WAAK,QAAQ;AAGb,iBAAW,MAAM;AAEf,qBAAa,QAAQ;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAEA,UAAM,WAAW,MAAM;AACrB,UAAI,QAAQ,SAAS,CAAC,UAAU,OAAO;AACrC;MACD;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,UAAU;AAEjC,mBAAa,QAAQ;AACrB,iBAAW,QAAQ;AAAA,IACrB;AAMA,UAAM,aAAa,MAAM;AACvB,iBAAW,QAAQ;AACnB,mBAAa,QAAQ;AAAA,IACvB;AAEA,UAAM,aAAa,MAAM;AAEvB,UAAI,aAAa,SAAS,aAAa,MAAM,QAAQ;AACnDA,sBAAAA,MAAI,uBAAuB;AAAA,UACzB,UAAU,aAAa,MAAM;AAAA,UAC7B,SAAS,MAAM;AACbA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,UACD,MAAM,MAAM;AACVA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,cAAc,MAAM;AAExBA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU,aAAa,QAAQ,aAAa,MAAM,SAAS;AAAA,QAC3D,OAAO,QAAQ,aAAa,QAAQ,aAAa,MAAM,OAAO,KAAK;AAAA,QACnE,SAAS,WAAW;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,QACD,MAAM,WAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,cAAc,MAAM;AAExB,YAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,UAAI,SAAS,SAAS;AACpBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACX,CAAK;AAAA,MACL,OAAS;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QAClB,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,iBAAiB,CAACI,UAAS;AAE/B,UAAIA,UAAS;AAAU;AAEvB,gBAAU,QAAQA;AAElB,cAAOA,OAAI;AAAA,QACT,KAAK;AAEHJ,wBAAG,MAAC,WAAU;AACd,qBAAW,MAAM;AACjBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,KAAK;AAAA,YACb,CAAO;AAAA,UACA,GAAE,EAAE;AACL;AAAA,QACF,KAAK;AAEHA,wBAAG,MAAC,WAAU;AACdA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,QACF,KAAK;AAEHA,wBAAG,MAAC,WAAU;AACdA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,UACb,CAAO;AACD;AAAA,MACH;AAAA,IACH;AAEA,UAAM,iBAAiB,MAAM;AAE3B,uBAAiB,QAAQ;AACzB,gBAAU,QAAQ;AAAA,IACpB;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAClC,uBAAiB,QAAQ;AAGzB,UAAI,MAAM;AACV,cAAO,MAAI;AAAA,QACT,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF;AACE,gBAAM;AAAA,MACT;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACJ,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9B,uBAAiB,QAAQ;AACzB,gBAAU,QAAQ;AAAA,IACpB;AAEA,UAAM,aAAa,CAAC,UAAU;AAE5B,YAAM,cAAc,CAAC,MAAM;AAAA,IAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvkBA,GAAG,WAAWK,SAAe;"}