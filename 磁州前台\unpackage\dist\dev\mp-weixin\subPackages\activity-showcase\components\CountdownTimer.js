"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "CountdownTimer",
  props: {
    endTime: {
      type: [String, Number, Date],
      required: true
    },
    type: {
      type: String,
      default: "default",
      validator: (value) => {
        return ["flash", "group", "discount", "coupon", "default"].includes(value);
      }
    }
  },
  data() {
    return {
      days: "00",
      hours: "00",
      minutes: "00",
      seconds: "00",
      timer: null,
      isEnded: false
    };
  },
  computed: {
    label() {
      if (this.isEnded) {
        return "活动已结束";
      }
      const labelMap = {
        flash: "距结束还剩",
        group: "拼团倒计时",
        discount: "优惠倒计时",
        coupon: "领取倒计时",
        default: "距结束还剩"
      };
      return labelMap[this.type] || "距结束还剩";
    }
  },
  mounted() {
    this.startCountdown();
  },
  beforeUnmount() {
    this.clearCountdown();
  },
  methods: {
    startCountdown() {
      this.calculateTime();
      this.timer = setInterval(() => {
        this.calculateTime();
      }, 1e3);
    },
    clearCountdown() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    calculateTime() {
      const endTime = new Date(this.endTime).getTime();
      const now = (/* @__PURE__ */ new Date()).getTime();
      const diff = endTime - now;
      if (diff <= 0) {
        this.days = "00";
        this.hours = "00";
        this.minutes = "00";
        this.seconds = "00";
        this.isEnded = true;
        this.clearCountdown();
        this.$emit("countdown-end");
        return;
      }
      const days = Math.floor(diff / (1e3 * 60 * 60 * 24));
      const hours = Math.floor(diff % (1e3 * 60 * 60 * 24) / (1e3 * 60 * 60));
      const minutes = Math.floor(diff % (1e3 * 60 * 60) / (1e3 * 60));
      const seconds = Math.floor(diff % (1e3 * 60) / 1e3);
      this.days = days < 10 ? `0${days}` : `${days}`;
      this.hours = hours < 10 ? `0${hours}` : `${hours}`;
      this.minutes = minutes < 10 ? `0${minutes}` : `${minutes}`;
      this.seconds = seconds < 10 ? `0${seconds}` : `${seconds}`;
      const secondsBlock = document.querySelector(".time-block:last-child .time-value");
      if (secondsBlock) {
        secondsBlock.classList.add("time-pulse");
        setTimeout(() => {
          secondsBlock.classList.remove("time-pulse");
        }, 500);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($options.label),
    b: common_vendor.t($data.days),
    c: common_vendor.t($data.hours),
    d: common_vendor.t($data.minutes),
    e: common_vendor.t($data.seconds),
    f: common_vendor.n(`countdown-${$props.type}`)
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1a83d72f"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/CountdownTimer.js.map
