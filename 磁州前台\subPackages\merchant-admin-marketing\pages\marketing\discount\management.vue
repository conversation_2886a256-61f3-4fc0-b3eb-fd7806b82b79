<template>
  <view class="discount-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @tap="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">满减活动管理</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 顶部操作区 -->
    <view class="top-actions">
      <CreateButton text="创建满减活动" theme="discount" @click="createNewDiscount" />
    </view>
    
    <!-- 数据概览 -->
    <view class="overview-section">
      <view class="overview-cards">
        <view class="overview-card">
          <text class="card-value">{{statistics.total}}</text>
          <text class="card-label">活动总数</text>
        </view>
        <view class="overview-card">
          <text class="card-value">{{statistics.active}}</text>
          <text class="card-label">进行中</text>
        </view>
        <view class="overview-card">
          <text class="card-value">{{statistics.orders}}</text>
          <text class="card-label">参与订单</text>
        </view>
        <view class="overview-card">
          <text class="card-value">¥{{statistics.discount}}</text>
          <text class="card-label">总优惠额</text>
        </view>
      </view>
    </view>
    
    <!-- 操作栏 -->
    <view class="action-bar">
      <view class="search-box">
        <view class="search-icon">
          <view class="icon-svg">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </view>
        </view>
        <input type="text" class="search-input" placeholder="搜索满减活动" v-model="searchKeyword" @input="onSearch" />
      </view>
      
      <view class="filter-btn" @tap="showFilterOptions">
        <view class="btn-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
          </svg>
        </view>
        <text class="btn-text">筛选</text>
      </view>
    </view>
    
    <!-- 满减活动列表 -->
    <view class="discount-list">
      <view 
        class="discount-item" 
        v-for="(item, index) in displayDiscounts" 
        :key="index"
        @tap="viewDiscountDetail(item)"
        :class="{'hover': hoveredDiscount === item.id}"
        @mouseenter="setHoveredDiscount(item.id)"
        @mouseleave="clearHoveredDiscount()">
        <view class="discount-status" :class="'status-'+item.status">{{item.statusText}}</view>
        <view class="discount-content">
          <text class="discount-title">{{item.title}}</text>
          <view class="discount-rules">
            <view class="rule-item" v-for="(rule, ruleIndex) in item.rules" :key="ruleIndex">
              <text class="rule-text">满{{rule.minAmount}}减{{rule.discountAmount}}</text>
            </view>
          </view>
          <view class="discount-info">
            <view class="info-item">
              <text class="info-label">活动时间:</text>
              <text class="info-value">{{item.timeRange}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">使用次数:</text>
              <text class="info-value">{{item.usageCount}}次</text>
            </view>
            <view class="info-item">
              <text class="info-label">优惠金额:</text>
              <text class="info-value">¥{{item.totalDiscount}}</text>
            </view>
          </view>
        </view>
        <view class="discount-actions">
          <view class="action-btn edit" @tap.stop="editDiscount(item)">
            <view class="btn-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
              </svg>
            </view>
            <text class="btn-text">编辑</text>
          </view>
          
          <view 
            class="action-btn" 
            :class="item.status === 'active' ? 'pause' : 'activate'"
            @tap.stop="toggleDiscountStatus(item)">
            <view class="btn-icon">
              <svg v-if="item.status === 'active'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="6" y="4" width="4" height="16"></rect>
                <rect x="14" y="4" width="4" height="16"></rect>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
            </view>
            <text class="btn-text">{{item.status === 'active' ? '暂停' : '启用'}}</text>
          </view>
          
          <view class="action-btn delete" @tap.stop="confirmDeleteDiscount(item)">
            <view class="btn-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="3 6 5 6 21 6"></polyline>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
              </svg>
            </view>
            <text class="btn-text">删除</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="displayDiscounts.length === 0">
        <view class="empty-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9 10 4 15 9 20"></polyline>
            <path d="M20 4v7a4 4 0 0 1-4 4H4"></path>
          </svg>
        </view>
        <text class="empty-text">暂无满减活动</text>
        <text class="empty-subtext">点击下方按钮创建新的满减活动</text>
      </view>
    </view>
    
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import CreateButton from '/subPackages/merchant-admin-marketing/components/CreateButton.vue';

export default {
  components: {
    CreateButton
  },
  setup() {
    // 响应式状态
    const statistics = reactive({
      total: 8,
      active: 3,
      orders: 546,
      discount: '12,856.50'
    });
    
    const discounts = ref([
      {
        id: 1,
        title: '春季促销活动',
        status: 'active',
        statusText: '进行中',
        rules: [
          { minAmount: 100, discountAmount: 10 },
          { minAmount: 200, discountAmount: 25 },
          { minAmount: 300, discountAmount: 50 }
        ],
        timeRange: '2023-04-01 ~ 2023-04-30',
        usageCount: 352,
        totalDiscount: '8,562.50'
      },
      {
        id: 2,
        title: '周末特惠',
        status: 'active',
        statusText: '进行中',
        rules: [
          { minAmount: 150, discountAmount: 15 },
          { minAmount: 300, discountAmount: 40 }
        ],
        timeRange: '每周五至周日',
        usageCount: 126,
        totalDiscount: '3,240.00'
      },
      {
        id: 3,
        title: '五一假期特惠',
        status: 'upcoming',
        statusText: '未开始',
        rules: [
          { minAmount: 200, discountAmount: 30 },
          { minAmount: 400, discountAmount: 70 },
          { minAmount: 600, discountAmount: 120 }
        ],
        timeRange: '2023-05-01 ~ 2023-05-03',
        usageCount: 0,
        totalDiscount: '0.00'
      },
      {
        id: 4,
        title: '清仓特惠',
        status: 'expired',
        statusText: '已结束',
        rules: [
          { minAmount: 100, discountAmount: 20 },
          { minAmount: 300, discountAmount: 60 }
        ],
        timeRange: '2023-03-15 ~ 2023-03-31',
        usageCount: 68,
        totalDiscount: '1,054.00'
      }
    ]);
    
    const searchKeyword = ref('');
    const hoveredDiscount = ref(null);
    
    // 计算属性
    const displayDiscounts = computed(() => {
      if (!searchKeyword.value) {
        return discounts.value;
      }
      
      const keyword = searchKeyword.value.toLowerCase();
      return discounts.value.filter(discount => {
        return discount.title.toLowerCase().includes(keyword);
      });
    });
    
    // 基础方法
    function goBack() {
      uni.navigateBack();
    }
    
    function onSearch(e) {
      // 实时搜索处理
    }
    
    function showFilterOptions() {
      uni.showActionSheet({
        itemList: ['全部', '进行中', '未开始', '已结束'],
        success: (res) => {
          // 处理筛选结果
        }
      });
    }
    
    function viewDiscountDetail(discount) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/detail?id=${discount.id}`,
        animationType: 'slide-in-right'
      });
    }
    
    function editDiscount(discount) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/edit?id=${discount.id}`,
        animationType: 'slide-in-right'
      });
    }
    
    function toggleDiscountStatus(discount) {
      const isActive = discount.status === 'active';
      const newStatus = isActive ? 'paused' : 'active';
      const statusText = isActive ? '已暂停' : '进行中';
      
      // 在实际应用中，这里应该调用API更新状态
      
      // 本地状态更新示例
      discount.status = newStatus;
      discount.statusText = statusText;
      
      uni.showToast({
        title: isActive ? '已暂停活动' : '已启用活动',
        icon: 'success'
      });
    }
    
    function confirmDeleteDiscount(discount) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除"${discount.title}"吗？此操作无法撤销。`,
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            deleteDiscount(discount);
          }
        }
      });
    }
    
    function deleteDiscount(discount) {
      // 在实际应用中，这里应该调用API删除
      
      // 本地状态更新示例
      const index = discounts.value.findIndex(item => item.id === discount.id);
      if (index > -1) {
        discounts.value.splice(index, 1);
      }
      
      uni.showToast({
        title: '已删除活动',
        icon: 'success'
      });
    }
    
    function createNewDiscount() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/discount/create',
        animationType: 'slide-in-right'
      });
    }
    
    function setHoveredDiscount(discountId) {
      hoveredDiscount.value = discountId;
    }
    
    function clearHoveredDiscount() {
      hoveredDiscount.value = null;
    }
    
    onMounted(() => {
      // 可以在这里加载数据
    });
    
    return {
      statistics,
      displayDiscounts,
      searchKeyword,
      hoveredDiscount,
      goBack,
      onSearch,
      showFilterOptions,
      viewDiscountDetail,
      editDiscount,
      toggleDiscountStatus,
      confirmDeleteDiscount,
      createNewDiscount,
      setHoveredDiscount,
      clearHoveredDiscount
    };
  }
}
</script>

<style lang="scss">
.discount-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #333;
  border-bottom: 2px solid #333;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.overview-card {
  width: 25%;
  padding: 0 5px;
  box-sizing: border-box;
}

.card-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #F8D800;
  text-align: center;
  margin-bottom: 5px;
}

.card-label {
  display: block;
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 操作栏样式 */
.action-bar {
  margin: 0 15px 15px;
  display: flex;
  align-items: center;
}

.search-box {
  flex: 1;
  height: 36px;
  background: #fff;
  border-radius: 18px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #999;
}

.icon-svg {
  width: 16px;
  height: 16px;
}

.search-input {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #333;
  border: none;
  background: transparent;
}

.filter-btn {
  height: 36px;
  padding: 0 12px;
  background: #fff;
  border-radius: 18px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  color: #666;
}

.btn-text {
  font-size: 14px;
  color: #666;
}

/* 满减活动列表样式 */
.discount-list {
  padding: 0 15px;
  margin-bottom: 80px; /* 为悬浮按钮留出空间 */
}

.discount-item {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.discount-item.hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.discount-status {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  color: white;
}

.status-active {
  background: #34C759;
}

.status-expired {
  background: #8E8E93;
}

.status-upcoming {
  background: #FF9500;
}

.status-paused {
  background: #FF9500;
}

.discount-content {
  margin-right: 70px; /* 为状态标签留出空间 */
  margin-bottom: 15px;
}

.discount-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.discount-rules {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.rule-item {
  background: rgba(248, 216, 0, 0.1);
  border-radius: 15px;
  padding: 5px 10px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.rule-text {
  font-size: 13px;
  color: #D4B100;
  font-weight: 500;
}

.discount-info {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 5px;
  display: flex;
}

.info-label {
  font-size: 12px;
  color: #999;
  margin-right: 5px;
}

.info-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.discount-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}

.action-btn .btn-icon {
  width: 20px;
  height: 20px;
  margin-right: 0;
  margin-bottom: 3px;
}

.action-btn .btn-text {
  font-size: 12px;
}

.action-btn.edit {
  color: #007AFF;
}

.action-btn.pause {
  color: #FF9500;
}

.action-btn.activate {
  color: #34C759;
}

.action-btn.delete {
  color: #FF3B30;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: rgba(248, 216, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: #F8D800;
}

.empty-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

/* 顶部操作区样式 */
.top-actions {
  padding: 15px;
  display: flex;
  justify-content: flex-end;
  background-color: #fff;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .overview-card {
    width: 50%;
    margin-bottom: 10px;
  }
}
</style> 