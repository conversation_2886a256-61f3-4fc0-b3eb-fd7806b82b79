{"version": 3, "file": "call-history.js", "sources": ["subPackages/user/pages/call-history.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcdXNlclxwYWdlc1xjYWxsLWhpc3RvcnkudnVl"], "sourcesContent": ["<template>\n  <view class=\"call-history-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">接打记录</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 顶部选项卡 -->\n    <view class=\"tabs-container\" :style=\"{ top: navbarHeight + 'px' }\">\n      <view \n        class=\"tab-item\" \n        v-for=\"(tab, index) in tabs\" \n        :key=\"index\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.name }}</text>\n      </view>\n      <view class=\"tab-line\" :style=\"tabLineStyle\"></view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-area\" :style=\"{ paddingTop: (navbarHeight + tabsHeight) + 'px' }\">\n      <swiper class=\"content-swiper\" :current=\"currentTab\" @change=\"onSwiperChange\">\n        <!-- 拨出电话 -->\n        <swiper-item>\n          <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(0)\" refresher-enabled :refresher-triggered=\"refreshing[0]\" @refresherrefresh=\"onRefresh(0)\">\n            <view v-if=\"outCallList.length > 0\" class=\"call-list\">\n              <view class=\"call-item\" v-for=\"(item, index) in outCallList\" :key=\"index\">\n                <view class=\"call-left\">\n                  <image class=\"call-avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"call-middle\">\n                  <view class=\"call-name\">{{ item.name }}</view>\n                  <view class=\"call-info\">\n                    <text class=\"call-type\">{{ item.type }}</text>\n                    <text class=\"call-desc\">{{ item.desc }}</text>\n                  </view>\n                </view>\n                <view class=\"call-right\">\n                  <text class=\"call-time\">{{ item.time }}</text>\n                  <view class=\"call-again\" @click.stop=\"callAgain(item)\">\n                    <image class=\"call-icon\" src=\"/static/images/tabbar/拨打记录.png\"></image>\n                  </view>\n                </view>\n              </view>\n            </view>\n            <view v-else class=\"empty-view\">\n              <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n              <view class=\"empty-text\">暂无拨出记录</view>\n            </view>\n            <view v-if=\"outCallList.length > 0 && !hasMore[0]\" class=\"list-bottom\">没有更多了</view>\n          </scroll-view>\n        </swiper-item>\n        \n        <!-- 接听电话 -->\n        <swiper-item>\n          <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(1)\" refresher-enabled :refresher-triggered=\"refreshing[1]\" @refresherrefresh=\"onRefresh(1)\">\n            <view v-if=\"inCallList.length > 0\" class=\"call-list\">\n              <view class=\"call-item\" v-for=\"(item, index) in inCallList\" :key=\"index\">\n                <view class=\"call-left\">\n                  <image class=\"call-avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"call-middle\">\n                  <view class=\"call-name\">{{ item.name }}</view>\n                  <view class=\"call-info\">\n                    <text class=\"call-type\">{{ item.type }}</text>\n                    <text class=\"call-desc\">{{ item.desc }}</text>\n                  </view>\n                </view>\n                <view class=\"call-right\">\n                  <text class=\"call-time\">{{ item.time }}</text>\n                  <view class=\"call-again\" @click.stop=\"callAgain(item)\">\n                    <image class=\"call-icon\" src=\"/static/images/tabbar/拨打记录.png\"></image>\n                  </view>\n                </view>\n              </view>\n            </view>\n            <view v-else class=\"empty-view\">\n              <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n              <view class=\"empty-text\">暂无接听记录</view>\n            </view>\n            <view v-if=\"inCallList.length > 0 && !hasMore[1]\" class=\"list-bottom\">没有更多了</view>\n          </scroll-view>\n        </swiper-item>\n        \n        <!-- 未接电话 -->\n        <swiper-item>\n          <scroll-view scroll-y class=\"tab-scroll\" @scrolltolower=\"loadMore(2)\" refresher-enabled :refresher-triggered=\"refreshing[2]\" @refresherrefresh=\"onRefresh(2)\">\n            <view v-if=\"missedCallList.length > 0\" class=\"call-list\">\n              <view class=\"call-item\" v-for=\"(item, index) in missedCallList\" :key=\"index\">\n                <view class=\"call-left\">\n                  <image class=\"call-avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"call-middle\">\n                  <view class=\"call-name call-missed\">{{ item.name }}</view>\n                  <view class=\"call-info\">\n                    <text class=\"call-type call-missed\">{{ item.type }}</text>\n                    <text class=\"call-desc\">{{ item.desc }}</text>\n                  </view>\n                </view>\n                <view class=\"call-right\">\n                  <text class=\"call-time\">{{ item.time }}</text>\n                  <view class=\"call-again\" @click.stop=\"callAgain(item)\">\n                    <image class=\"call-icon\" src=\"/static/images/tabbar/拨打记录.png\"></image>\n                  </view>\n                </view>\n              </view>\n            </view>\n            <view v-else class=\"empty-view\">\n              <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n              <view class=\"empty-text\">暂无未接来电</view>\n            </view>\n            <view v-if=\"missedCallList.length > 0 && !hasMore[2]\" class=\"list-bottom\">没有更多了</view>\n          </scroll-view>\n        </swiper-item>\n      </swiper>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 响应式数据\nconst statusBarHeight = ref(20);\nconst navbarHeight = ref(64); // 导航栏高度\nconst tabsHeight = ref(44); // 选项卡高度\nconst tabs = ref([\n  { name: '拨出' },\n  { name: '接听' },\n  { name: '未接' }\n]);\nconst currentTab = ref(0);\nconst outCallList = ref([]);\nconst inCallList = ref([]);\nconst missedCallList = ref([]);\nconst page = ref([1, 1, 1]); // 当前页码\nconst pageSize = ref(10); // 每页显示数量\nconst hasMore = ref([true, true, true]); // 是否有更多数据\nconst refreshing = ref([false, false, false]); // 刷新状态\n\n// 计算属性\nconst tabLineStyle = computed(() => {\n  return {\n    transform: `translateX(${currentTab.value * (100 / tabs.value.length)}%)`,\n    width: `${100 / tabs.value.length}%`\n  };\n});\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 切换选项卡\nconst switchTab = (index) => {\n  currentTab.value = index;\n};\n\n// 轮播图切换事件\nconst onSwiperChange = (e) => {\n  currentTab.value = e.detail.current;\n};\n\n// 加载拨出电话列表\nconst loadOutCallList = () => {\n  // 模拟请求延迟\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = Array.from({ length: 10 }, (_, i) => ({\n      id: `out_${page.value[0]}_${i}`,\n      name: `张师傅 ${page.value[0]}_${i}`,\n      avatar: '/static/images/avatar.png',\n      type: '店铺转让',\n      desc: '通话时长: 2分30秒',\n      time: '2023-10-15 14:30',\n      phone: '13812345678'\n    }));\n    \n    if (page.value[0] === 1) {\n      outCallList.value = mockData;\n    } else {\n      outCallList.value = [...outCallList.value, ...mockData];\n    }\n    \n    // 模拟是否还有更多数据\n    hasMore.value[0] = page.value[0] < 3;\n    \n    // 关闭刷新状态\n    refreshing.value[0] = false;\n  }, 500);\n};\n\n// 加载接听电话列表\nconst loadInCallList = () => {\n  // 模拟请求延迟\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = Array.from({ length: 10 }, (_, i) => ({\n      id: `in_${page.value[1]}_${i}`,\n      name: `李客户 ${page.value[1]}_${i}`,\n      avatar: '/static/images/avatar.png',\n      type: '求职信息',\n      desc: '通话时长: 1分45秒',\n      time: '2023-10-14 10:20',\n      phone: '13987654321'\n    }));\n    \n    if (page.value[1] === 1) {\n      inCallList.value = mockData;\n    } else {\n      inCallList.value = [...inCallList.value, ...mockData];\n    }\n    \n    // 模拟是否还有更多数据\n    hasMore.value[1] = page.value[1] < 3;\n    \n    // 关闭刷新状态\n    refreshing.value[1] = false;\n  }, 500);\n};\n\n// 加载未接电话列表\nconst loadMissedCallList = () => {\n  // 模拟请求延迟\n  setTimeout(() => {\n    // 模拟数据\n    const mockData = Array.from({ length: 10 }, (_, i) => ({\n      id: `missed_${page.value[2]}_${i}`,\n      name: `王经理 ${page.value[2]}_${i}`,\n      avatar: '/static/images/avatar.png',\n      type: '招聘信息',\n      desc: '未接来电',\n      time: '2023-10-13 16:05',\n      phone: '13755667788'\n    }));\n    \n    if (page.value[2] === 1) {\n      missedCallList.value = mockData;\n    } else {\n      missedCallList.value = [...missedCallList.value, ...mockData];\n    }\n    \n    // 模拟是否还有更多数据\n    hasMore.value[2] = page.value[2] < 3;\n    \n    // 关闭刷新状态\n    refreshing.value[2] = false;\n  }, 500);\n};\n\n// 加载更多数据\nconst loadMore = (tabIndex) => {\n  if (!hasMore.value[tabIndex]) return;\n  \n  page.value[tabIndex]++;\n  if (tabIndex === 0) {\n    loadOutCallList();\n  } else if (tabIndex === 1) {\n    loadInCallList();\n  } else {\n    loadMissedCallList();\n  }\n};\n\n// 下拉刷新\nconst onRefresh = (tabIndex) => {\n  refreshing.value[tabIndex] = true;\n  page.value[tabIndex] = 1;\n  hasMore.value[tabIndex] = true;\n  \n  if (tabIndex === 0) {\n    loadOutCallList();\n  } else if (tabIndex === 1) {\n    loadInCallList();\n  } else {\n    loadMissedCallList();\n  }\n};\n\n// 再次拨打电话\nconst callAgain = (item) => {\n  uni.makePhoneCall({\n    phoneNumber: item.phone,\n    success: () => {\n      console.log('拨打电话成功');\n    },\n    fail: (err) => {\n      console.error('拨打电话失败', err);\n      uni.showToast({\n        title: '拨打电话失败',\n        icon: 'none'\n      });\n    }\n  });\n};\n\n// 生命周期钩子\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n  navbarHeight.value = statusBarHeight.value + 44;\n  \n  // 加载初始数据\n  loadOutCallList();\n  loadInCallList();\n  loadMissedCallList();\n});\n</script>\n\n<style>\n.call-history-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  position: relative;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  padding: 0 15px;\n  background-color: #0052CC;\n  color: #fff;\n  z-index: 100;\n}\n\n.navbar-left {\n  width: 80rpx;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.navbar-right {\n  width: 80rpx;\n  text-align: right;\n}\n\n.back-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n/* 选项卡样式 */\n.tabs-container {\n  position: fixed;\n  left: 0;\n  right: 0;\n  height: 44px;\n  display: flex;\n  background-color: #fff;\n  border-bottom: 1px solid #eee;\n  z-index: 99;\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n}\n\n.tab-text {\n  font-size: 14px;\n  color: #333;\n  padding: 0 15px;\n}\n\n.tab-item.active .tab-text {\n  color: #0052CC;\n  font-weight: bold;\n}\n\n.tab-line {\n  position: absolute;\n  bottom: 0;\n  height: 3px;\n  background-color: #0052CC;\n  border-radius: 2px;\n  transition: transform 0.3s;\n}\n\n/* 内容区域 */\n.content-area {\n  position: relative;\n  height: 100vh;\n}\n\n.content-swiper {\n  height: 100%;\n}\n\n.tab-scroll {\n  height: 100%;\n}\n\n/* 通话记录列表 */\n.call-list {\n  padding: 15px;\n}\n\n.call-item {\n  display: flex;\n  align-items: center;\n  padding: 15px;\n  background-color: #fff;\n  border-radius: 8px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.call-left {\n  margin-right: 15px;\n}\n\n.call-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n}\n\n.call-middle {\n  flex: 1;\n}\n\n.call-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.call-info {\n  font-size: 13px;\n  color: #999;\n}\n\n.call-type {\n  margin-right: 10px;\n  color: #666;\n}\n\n.call-desc {\n  color: #999;\n}\n\n.call-right {\n  text-align: right;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n\n.call-time {\n  font-size: 12px;\n  color: #999;\n  margin-bottom: 10px;\n}\n\n.call-again {\n  width: 60rpx;\n  height: 60rpx;\n  background-color: #e6f7ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.call-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n/* 未接电话样式 */\n.call-missed {\n  color: #ff4d4f !important;\n}\n\n/* 空状态 */\n.empty-view {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding-top: 100px;\n}\n\n.empty-icon {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 20px;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n/* 列表底部 */\n.list-bottom {\n  text-align: center;\n  padding: 15px 0;\n  font-size: 14px;\n  color: #999;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/user/pages/call-history.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AAiIA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,EAAE,MAAM,KAAM;AAAA,MACd,EAAE,MAAM,KAAM;AAAA,MACd,EAAE,MAAM,KAAM;AAAA,IAChB,CAAC;AACD,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AACzB,UAAM,iBAAiBA,cAAAA,IAAI,CAAA,CAAE;AAC7B,UAAM,OAAOA,cAAG,IAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACTA,kBAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAG,IAAC,CAAC,MAAM,MAAM,IAAI,CAAC;AACtC,UAAM,aAAaA,cAAG,IAAC,CAAC,OAAO,OAAO,KAAK,CAAC;AAG5C,UAAM,eAAeC,cAAQ,SAAC,MAAM;AAClC,aAAO;AAAA,QACL,WAAW,cAAc,WAAW,SAAS,MAAM,KAAK,MAAM,OAAO;AAAA,QACrE,OAAO,GAAG,MAAM,KAAK,MAAM,MAAM;AAAA,MACrC;AAAA,IACA,CAAC;AAGD,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,iBAAW,QAAQ,EAAE,OAAO;AAAA,IAC9B;AAGA,UAAM,kBAAkB,MAAM;AAE5B,iBAAW,MAAM;AAEf,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ,GAAI,GAAE,CAAC,GAAG,OAAO;AAAA,UACrD,IAAI,OAAO,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAC7B,MAAM,OAAO,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAC/B,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR,EAAC;AAEF,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,sBAAY,QAAQ;AAAA,QAC1B,OAAW;AACL,sBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAG,QAAQ;AAAA,QACvD;AAGD,gBAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAGnC,mBAAW,MAAM,CAAC,IAAI;AAAA,MACvB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,iBAAiB,MAAM;AAE3B,iBAAW,MAAM;AAEf,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ,GAAI,GAAE,CAAC,GAAG,OAAO;AAAA,UACrD,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAC5B,MAAM,OAAO,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAC/B,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR,EAAC;AAEF,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,qBAAW,QAAQ;AAAA,QACzB,OAAW;AACL,qBAAW,QAAQ,CAAC,GAAG,WAAW,OAAO,GAAG,QAAQ;AAAA,QACrD;AAGD,gBAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAGnC,mBAAW,MAAM,CAAC,IAAI;AAAA,MACvB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,qBAAqB,MAAM;AAE/B,iBAAW,MAAM;AAEf,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ,GAAI,GAAE,CAAC,GAAG,OAAO;AAAA,UACrD,IAAI,UAAU,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAChC,MAAM,OAAO,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC;AAAA,UAC/B,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACR,EAAC;AAEF,YAAI,KAAK,MAAM,CAAC,MAAM,GAAG;AACvB,yBAAe,QAAQ;AAAA,QAC7B,OAAW;AACL,yBAAe,QAAQ,CAAC,GAAG,eAAe,OAAO,GAAG,QAAQ;AAAA,QAC7D;AAGD,gBAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAGnC,mBAAW,MAAM,CAAC,IAAI;AAAA,MACvB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,WAAW,CAAC,aAAa;AAC7B,UAAI,CAAC,QAAQ,MAAM,QAAQ;AAAG;AAE9B,WAAK,MAAM,QAAQ;AACnB,UAAI,aAAa,GAAG;AAClB;MACJ,WAAa,aAAa,GAAG;AACzB;MACJ,OAAS;AACL;MACD;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,aAAa;AAC9B,iBAAW,MAAM,QAAQ,IAAI;AAC7B,WAAK,MAAM,QAAQ,IAAI;AACvB,cAAQ,MAAM,QAAQ,IAAI;AAE1B,UAAI,aAAa,GAAG;AAClB;MACJ,WAAa,aAAa,GAAG;AACzB;MACJ,OAAS;AACL;MACD;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,SAAS;AAC1BA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,KAAK;AAAA,QAClB,SAAS,MAAM;AACbA,wBAAAA,MAAY,MAAA,OAAA,kDAAA,QAAQ;AAAA,QACrB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,kDAAA,UAAU,GAAG;AAC3BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUD,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAG7C;AACA;AACA;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxTD,GAAG,WAAWE,SAAe;"}