{"version": 3, "file": "index.js", "sources": ["api/index.js"], "sourcesContent": ["/**\n * API统一入口\n * 整合所有真实API模块，替换Mock数据\n */\n\nimport userApi from './userApi';\nimport contentApi from './contentApi';\nimport carpoolApi from './carpoolApi';\nimport homeApi from './homeApi';\nimport publishApi from './publishApi';\n\n// 统一导出所有API - 使用真实API替换Mock数据\nexport default {\n  // 用户相关API\n  user: {\n    login: userApi.login.bind(userApi),\n    wxLogin: userApi.wxLogin.bind(userApi),\n    register: userApi.register.bind(userApi),\n    sendSmsCode: userApi.sendSmsCode.bind(userApi),\n    getUserInfo: userApi.getUserInfo.bind(userApi),\n    updateUserInfo: userApi.updateUserInfo.bind(userApi),\n    changePassword: userApi.changePassword.bind(userApi),\n    logout: userApi.logout.bind(userApi),\n    isLoggedIn: userApi.isLoggedIn.bind(userApi),\n    getCurrentUser: userApi.getCurrentUser.bind(userApi),\n    getToken: userApi.getToken.bind(userApi)\n  },\n  \n  // 新闻相关API\n  news: {\n    getCategories: contentApi.getNewsCategories.bind(contentApi),\n    getList: contentApi.getNewsList.bind(contentApi),\n    getDetail: contentApi.getNewsDetail.bind(contentApi)\n  },\n  \n  // 信息相关API\n  info: {\n    getCategories: contentApi.getInfoCategories.bind(contentApi),\n    getTopped: contentApi.getToppedInfo.bind(contentApi),\n    getAll: contentApi.getInfoList.bind(contentApi),\n    getDetail: contentApi.getInfoDetail.bind(contentApi),\n    publish: contentApi.publishInfo.bind(contentApi)\n  },\n  \n  // 商家相关API\n  business: {\n    getCategories: contentApi.getBusinessCategories.bind(contentApi),\n    getList: contentApi.getBusinessList.bind(contentApi),\n    getDetail: contentApi.getBusinessDetail.bind(contentApi)\n  },\n  \n  // 拼车相关API\n  carpool: {\n    getList: carpoolApi.getCarpoolList.bind(carpoolApi),\n    getDetail: carpoolApi.getCarpoolDetail.bind(carpoolApi),\n    publish: carpoolApi.publishCarpool.bind(carpoolApi),\n    update: carpoolApi.updateCarpool.bind(carpoolApi),\n    delete: carpoolApi.deleteCarpool.bind(carpoolApi),\n    getMy: carpoolApi.getMyCarpoolList.bind(carpoolApi),\n    apply: carpoolApi.applyCarpool.bind(carpoolApi),\n    handleApplication: carpoolApi.handleApplication.bind(carpoolApi),\n    getApplications: carpoolApi.getApplications.bind(carpoolApi),\n    top: carpoolApi.topCarpool.bind(carpoolApi),\n    refresh: carpoolApi.refreshCarpool.bind(carpoolApi),\n    getHotRoutes: carpoolApi.getHotRoutes.bind(carpoolApi),\n    search: carpoolApi.searchCarpool.bind(carpoolApi),\n    getStats: carpoolApi.getCarpoolStats.bind(carpoolApi),\n    report: carpoolApi.reportCarpool.bind(carpoolApi)\n  },\n  \n  // 通用内容API\n  content: {\n    uploadImage: contentApi.uploadImage.bind(contentApi),\n    search: contentApi.searchContent.bind(contentApi)\n  },\n  \n  // 首页相关API - 与后台管理系统互通\n  home: {\n    getBanners: homeApi.getBanners.bind(homeApi),\n    getHomeConfig: homeApi.getHomeConfig.bind(homeApi),\n    getHomeStats: homeApi.getHomeStats.bind(homeApi),\n    getServiceCategories: homeApi.getServiceCategories.bind(homeApi),\n    getMerchantRecommend: homeApi.getMerchantRecommend.bind(homeApi),\n    getCityNews: homeApi.getCityNews.bind(homeApi),\n    getFeatureConfig: homeApi.getFeatureConfig.bind(homeApi),\n    recordPageView: homeApi.recordPageView.bind(homeApi),\n    getAdBanner: homeApi.getAdBanner.bind(homeApi)\n  },\n  \n  // 发布相关API - 与后台管理系统互通\n  publish: {\n    publishPost: publishApi.publishPost.bind(publishApi),\n    getPosts: publishApi.getPosts.bind(publishApi),\n    getPost: publishApi.getPost.bind(publishApi),\n    updatePost: publishApi.updatePost.bind(publishApi),\n    deletePost: publishApi.deletePost.bind(publishApi),\n    topPost: publishApi.topPost.bind(publishApi),\n    refreshPost: publishApi.refreshPost.bind(publishApi),\n    addRedPacket: publishApi.addRedPacket.bind(publishApi),\n    claimRedPacket: publishApi.claimRedPacket.bind(publishApi),\n    getConfigs: publishApi.getConfigs.bind(publishApi),\n    getPublishConfigs: publishApi.getPublishConfigs.bind(publishApi),\n    uploadImage: publishApi.uploadImage.bind(publishApi),\n    getCategories: publishApi.getCategories.bind(publishApi),\n    getHomePosts: publishApi.getHomePosts.bind(publishApi),\n    getCategoryPosts: publishApi.getCategoryPosts.bind(publishApi),\n    getTopPosts: publishApi.getTopPosts.bind(publishApi),\n    searchPosts: publishApi.searchPosts.bind(publishApi),\n    getMyPosts: publishApi.getMyPosts.bind(publishApi),\n    getPostStats: publishApi.getPostStats.bind(publishApi)\n  }\n};\n\n// 兼容性导出 - 保持与原有代码的兼容性\nexport {\n  userApi,\n  contentApi,\n  carpoolApi,\n  homeApi,\n  publishApi\n};\n"], "names": ["userApi", "contentApi", "carpoolApi", "homeApi", "publishApi"], "mappings": ";;;;;;AAYA,MAAe,MAAA;AAAA;AAAA,EAEb,MAAM;AAAA,IACJ,OAAOA,YAAO,QAAC,MAAM,KAAKA,YAAAA,OAAO;AAAA,IACjC,SAASA,YAAO,QAAC,QAAQ,KAAKA,YAAAA,OAAO;AAAA,IACrC,UAAUA,YAAO,QAAC,SAAS,KAAKA,YAAAA,OAAO;AAAA,IACvC,aAAaA,YAAO,QAAC,YAAY,KAAKA,YAAAA,OAAO;AAAA,IAC7C,aAAaA,YAAO,QAAC,YAAY,KAAKA,YAAAA,OAAO;AAAA,IAC7C,gBAAgBA,YAAO,QAAC,eAAe,KAAKA,YAAAA,OAAO;AAAA,IACnD,gBAAgBA,YAAO,QAAC,eAAe,KAAKA,YAAAA,OAAO;AAAA,IACnD,QAAQA,YAAO,QAAC,OAAO,KAAKA,YAAAA,OAAO;AAAA,IACnC,YAAYA,YAAO,QAAC,WAAW,KAAKA,YAAAA,OAAO;AAAA,IAC3C,gBAAgBA,YAAO,QAAC,eAAe,KAAKA,YAAAA,OAAO;AAAA,IACnD,UAAUA,YAAO,QAAC,SAAS,KAAKA,YAAAA,OAAO;AAAA,EACxC;AAAA;AAAA,EAGD,MAAM;AAAA,IACJ,eAAeC,eAAU,WAAC,kBAAkB,KAAKA,eAAAA,UAAU;AAAA,IAC3D,SAASA,eAAU,WAAC,YAAY,KAAKA,eAAAA,UAAU;AAAA,IAC/C,WAAWA,eAAU,WAAC,cAAc,KAAKA,eAAAA,UAAU;AAAA,EACpD;AAAA;AAAA,EAGD,MAAM;AAAA,IACJ,eAAeA,eAAU,WAAC,kBAAkB,KAAKA,eAAAA,UAAU;AAAA,IAC3D,WAAWA,eAAU,WAAC,cAAc,KAAKA,eAAAA,UAAU;AAAA,IACnD,QAAQA,eAAU,WAAC,YAAY,KAAKA,eAAAA,UAAU;AAAA,IAC9C,WAAWA,eAAU,WAAC,cAAc,KAAKA,eAAAA,UAAU;AAAA,IACnD,SAASA,eAAU,WAAC,YAAY,KAAKA,eAAAA,UAAU;AAAA,EAChD;AAAA;AAAA,EAGD,UAAU;AAAA,IACR,eAAeA,eAAU,WAAC,sBAAsB,KAAKA,eAAAA,UAAU;AAAA,IAC/D,SAASA,eAAU,WAAC,gBAAgB,KAAKA,eAAAA,UAAU;AAAA,IACnD,WAAWA,eAAU,WAAC,kBAAkB,KAAKA,eAAAA,UAAU;AAAA,EACxD;AAAA;AAAA,EAGD,SAAS;AAAA,IACP,SAASC,eAAU,WAAC,eAAe,KAAKA,eAAAA,UAAU;AAAA,IAClD,WAAWA,eAAU,WAAC,iBAAiB,KAAKA,eAAAA,UAAU;AAAA,IACtD,SAASA,eAAU,WAAC,eAAe,KAAKA,eAAAA,UAAU;AAAA,IAClD,QAAQA,eAAU,WAAC,cAAc,KAAKA,eAAAA,UAAU;AAAA,IAChD,QAAQA,eAAU,WAAC,cAAc,KAAKA,eAAAA,UAAU;AAAA,IAChD,OAAOA,eAAU,WAAC,iBAAiB,KAAKA,eAAAA,UAAU;AAAA,IAClD,OAAOA,eAAU,WAAC,aAAa,KAAKA,eAAAA,UAAU;AAAA,IAC9C,mBAAmBA,eAAU,WAAC,kBAAkB,KAAKA,eAAAA,UAAU;AAAA,IAC/D,iBAAiBA,eAAU,WAAC,gBAAgB,KAAKA,eAAAA,UAAU;AAAA,IAC3D,KAAKA,eAAU,WAAC,WAAW,KAAKA,eAAAA,UAAU;AAAA,IAC1C,SAASA,eAAU,WAAC,eAAe,KAAKA,eAAAA,UAAU;AAAA,IAClD,cAAcA,eAAU,WAAC,aAAa,KAAKA,eAAAA,UAAU;AAAA,IACrD,QAAQA,eAAU,WAAC,cAAc,KAAKA,eAAAA,UAAU;AAAA,IAChD,UAAUA,eAAU,WAAC,gBAAgB,KAAKA,eAAAA,UAAU;AAAA,IACpD,QAAQA,eAAU,WAAC,cAAc,KAAKA,eAAAA,UAAU;AAAA,EACjD;AAAA;AAAA,EAGD,SAAS;AAAA,IACP,aAAaD,eAAU,WAAC,YAAY,KAAKA,eAAAA,UAAU;AAAA,IACnD,QAAQA,eAAU,WAAC,cAAc,KAAKA,eAAAA,UAAU;AAAA,EACjD;AAAA;AAAA,EAGD,MAAM;AAAA,IACJ,YAAYE,YAAO,QAAC,WAAW,KAAKA,YAAAA,OAAO;AAAA,IAC3C,eAAeA,YAAO,QAAC,cAAc,KAAKA,YAAAA,OAAO;AAAA,IACjD,cAAcA,YAAO,QAAC,aAAa,KAAKA,YAAAA,OAAO;AAAA,IAC/C,sBAAsBA,YAAO,QAAC,qBAAqB,KAAKA,YAAAA,OAAO;AAAA,IAC/D,sBAAsBA,YAAO,QAAC,qBAAqB,KAAKA,YAAAA,OAAO;AAAA,IAC/D,aAAaA,YAAO,QAAC,YAAY,KAAKA,YAAAA,OAAO;AAAA,IAC7C,kBAAkBA,YAAO,QAAC,iBAAiB,KAAKA,YAAAA,OAAO;AAAA,IACvD,gBAAgBA,YAAO,QAAC,eAAe,KAAKA,YAAAA,OAAO;AAAA,IACnD,aAAaA,YAAO,QAAC,YAAY,KAAKA,YAAAA,OAAO;AAAA,EAC9C;AAAA;AAAA,EAGD,SAAS;AAAA,IACP,aAAaC,eAAU,WAAC,YAAY,KAAKA,eAAAA,UAAU;AAAA,IACnD,UAAUA,eAAU,WAAC,SAAS,KAAKA,eAAAA,UAAU;AAAA,IAC7C,SAASA,eAAU,WAAC,QAAQ,KAAKA,eAAAA,UAAU;AAAA,IAC3C,YAAYA,eAAU,WAAC,WAAW,KAAKA,eAAAA,UAAU;AAAA,IACjD,YAAYA,eAAU,WAAC,WAAW,KAAKA,eAAAA,UAAU;AAAA,IACjD,SAASA,eAAU,WAAC,QAAQ,KAAKA,eAAAA,UAAU;AAAA,IAC3C,aAAaA,eAAU,WAAC,YAAY,KAAKA,eAAAA,UAAU;AAAA,IACnD,cAAcA,eAAU,WAAC,aAAa,KAAKA,eAAAA,UAAU;AAAA,IACrD,gBAAgBA,eAAU,WAAC,eAAe,KAAKA,eAAAA,UAAU;AAAA,IACzD,YAAYA,eAAU,WAAC,WAAW,KAAKA,eAAAA,UAAU;AAAA,IACjD,mBAAmBA,eAAU,WAAC,kBAAkB,KAAKA,eAAAA,UAAU;AAAA,IAC/D,aAAaA,eAAU,WAAC,YAAY,KAAKA,eAAAA,UAAU;AAAA,IACnD,eAAeA,eAAU,WAAC,cAAc,KAAKA,eAAAA,UAAU;AAAA,IACvD,cAAcA,eAAU,WAAC,aAAa,KAAKA,eAAAA,UAAU;AAAA,IACrD,kBAAkBA,eAAU,WAAC,iBAAiB,KAAKA,eAAAA,UAAU;AAAA,IAC7D,aAAaA,eAAU,WAAC,YAAY,KAAKA,eAAAA,UAAU;AAAA,IACnD,aAAaA,eAAU,WAAC,YAAY,KAAKA,eAAAA,UAAU;AAAA,IACnD,YAAYA,eAAU,WAAC,WAAW,KAAKA,eAAAA,UAAU;AAAA,IACjD,cAAcA,eAAU,WAAC,aAAa,KAAKA,eAAAA,UAAU;AAAA,EACtD;AACH;;"}