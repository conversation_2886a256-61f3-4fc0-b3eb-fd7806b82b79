<template>
	<view class="success-container">
		<view class="success-content">
			<image class="success-icon" src="/static/images/pay/success.png" mode="aspectFit"></image>
			<view class="success-title">支付成功</view>
			<view class="success-desc">您的订单已支付完成</view>
			
			<view class="order-info">
				<view class="info-item">
					<text class="label">订单类型</text>
					<text class="value">{{getOrderTypeText(orderInfo.orderType)}}</text>
				</view>
				<view class="info-item" v-if="orderInfo.days">
					<text class="label">置顶时长</text>
					<text class="value">{{orderInfo.days}}天</text>
				</view>
				<view class="info-item">
					<text class="label">支付金额</text>
					<text class="value">¥{{orderInfo.amount}}</text>
				</view>
				<view class="info-item">
					<text class="label">订单编号</text>
					<text class="value">{{orderInfo.orderNo}}</text>
				</view>
			</view>
			
			<view class="action-buttons">
				<button class="action-btn view-btn" @click="viewDetail">查看详情</button>
				<button class="action-btn back-btn" @click="goBack">返回首页</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 响应式状态
const orderInfo = ref({
	orderType: '',
	days: 0,
	amount: 0,
	orderNo: ''
});

// 获取订单类型文本
const getOrderTypeText = (type) => {
	const typeMap = {
		'top': '信息置顶',
		'publish': '信息发布',
		'vip': '会员开通'
	};
	return typeMap[type] || type;
};

// 查看详情
const viewDetail = () => {
	const infoId = uni.getStorageSync('lastPublishId');
	uni.navigateTo({
		url: `/pages/publish/detail?id=${infoId}`
	});
};

// 返回首页
const goBack = () => {
	uni.switchTab({
		url: '/pages/index/index'
	});
};

// 生命周期钩子
onMounted(() => {
	// 获取路由参数
	const pages = getCurrentPages();
	const currentPage = pages[pages.length - 1];
	const options = currentPage.options;
	
	if (options.orderInfo) {
		orderInfo.value = JSON.parse(decodeURIComponent(options.orderInfo));
	}
});
</script>

<style>
	.success-container {
		min-height: 100vh;
		background-color: #f5f7fa;
		padding: 40rpx;
	}
	
	.success-content {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 60rpx 40rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.success-icon {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 40rpx;
	}
	
	.success-title {
		font-size: 40rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 20rpx;
	}
	
	.success-desc {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 60rpx;
	}
	
	.order-info {
		width: 100%;
		background-color: #f8f9fa;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;
	}
	
	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 2rpx solid #f0f0f0;
	}
	
	.info-item:last-child {
		border-bottom: none;
	}
	
	.label {
		font-size: 28rpx;
		color: #666;
	}
	
	.value {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}
	
	.action-buttons {
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-top: 40rpx;
	}
	
	.action-btn {
		width: 45%;
		height: 88rpx;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s;
	}
	
	.view-btn {
		background-color: #0052CC;
		color: #FFFFFF;
		box-shadow: 0 8rpx 16rpx rgba(0, 82, 204, 0.2);
	}
	
	.back-btn {
		background-color: #f5f7fa;
		color: #666;
		border: 2rpx solid #e5e5e5;
	}
	
	.action-btn:active {
		transform: scale(0.98);
	}
</style> 
