/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.feedback-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}
.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}
.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 24px;
  height: 24px;
}
.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}
.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 内容区域 */
.scrollable-content {
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 16px;
}

/* 反馈表单 */
.feedback-form {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
}
.form-section {
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}
.form-section:last-child {
  border-bottom: none;
}
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12px;
}

/* 反馈类型 */
.feedback-types {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.type-item {
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #F5F5F5;
}
.type-item.active {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
}
.type-text {
  font-size: 14px;
  color: #666666;
}
.type-item.active .type-text {
  color: #4CAF50;
}

/* 反馈内容 */
.content-textarea-wrapper {
  position: relative;
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  background-color: #F9F9F9;
  padding: 12px;
}
.content-textarea {
  width: 100%;
  height: 120px;
  font-size: 14px;
  color: #333333;
  line-height: 1.5;
}
.textarea-placeholder {
  color: #999999;
}
.word-count {
  position: absolute;
  right: 12px;
  bottom: 12px;
  font-size: 12px;
  color: #999999;
}

/* 上传图片 */
.upload-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.image-item, .upload-item {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}
.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #FFFFFF;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 8px;
}
.upload-item {
  border: 1px dashed #DDDDDD;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background-color: #F9F9F9;
}
.upload-icon {
  width: 24px;
  height: 24px;
}
.upload-text {
  font-size: 12px;
  color: #999999;
}
.upload-hint {
  font-size: 12px;
  color: #999999;
}

/* 联系方式 */
.contact-input-wrapper {
  border: 1px solid #EEEEEE;
  border-radius: 8px;
  background-color: #F9F9F9;
  padding: 12px;
}
.contact-input {
  width: 100%;
  height: 24px;
  font-size: 14px;
  color: #333333;
}
.input-placeholder {
  color: #999999;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #4CAF50;
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  margin: 24px 0;
}
.submit-button.disabled {
  background-color: #E0E0E0;
  color: #999999;
}

/* 历史反馈 */
.history-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 24px;
}
.history-header {
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}
.history-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
.history-list {
  display: flex;
  flex-direction: column;
}
.history-item {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}
.history-item:last-child {
  border-bottom: none;
}
.history-content {
  flex: 1;
}
.history-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}
.history-type {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
}
.history-time {
  font-size: 12px;
  color: #999999;
}
.history-brief {
  font-size: 14px;
  color: #666666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
.history-status {
  display: flex;
  align-items: center;
  gap: 4px;
}
.status-text {
  font-size: 14px;
}
.arrow-icon {
  width: 16px;
  height: 16px;
}
.status-pending .status-text {
  color: #FF9800;
}
.status-processing .status-text {
  color: #2196F3;
}
.status-replied .status-text {
  color: #4CAF50;
}
.status-closed .status-text {
  color: #9E9E9E;
}

/* 历史详情弹窗 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
.popup-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #EEEEEE;
}
.popup-title {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
}
.popup-close {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}
.popup-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}
.detail-section {
  margin-bottom: 16px;
}
.detail-label {
  font-size: 14px;
  color: #999999;
  margin-bottom: 8px;
}
.detail-value {
  font-size: 15px;
  color: #333333;
}
.detail-value.content {
  line-height: 1.6;
  white-space: pre-wrap;
}
.detail-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}
.detail-image {
  width: 100%;
  border-radius: 8px;
}
.detail-value.reply {
  background-color: #F8F8F8;
  border-radius: 8px;
  padding: 12px;
}
.reply-time {
  font-size: 12px;
  color: #999999;
  display: block;
  margin-bottom: 8px;
}
.reply-content {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
}

/* 提交成功弹窗 */
.success-popup {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
}
.success-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
}
.success-title {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8px;
}
.success-message {
  font-size: 14px;
  color: #666666;
  margin-bottom: 24px;
  text-align: center;
}
.success-button {
  width: 80%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #4CAF50;
  color: #FFFFFF;
  font-size: 16px;
  border-radius: 22px;
}