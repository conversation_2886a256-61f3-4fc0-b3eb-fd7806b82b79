"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "pet-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.onMounted(() => {
      try {
        const sysInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = sysInfo.statusBarHeight || 20;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/pet-detail.vue:208", "获取状态栏高度失败", e);
      }
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const isCollected = common_vendor.ref(false);
    const petData = common_vendor.ref({
      id: "pet12345",
      title: "纯种金毛幼犬",
      price: "2000元",
      tags: ["纯种", "已打疫苗", "可上门看"],
      publishTime: Date.now() - 864e5 * 2,
      // 2天前
      images: [
        "/static/images/pet1.jpg",
        "/static/images/pet2.jpg",
        "/static/images/pet3.jpg"
      ],
      breed: "金毛",
      age: "3个月",
      gender: "公",
      vaccination: "已打两针",
      features: [
        { label: "毛色", value: "金黄色" },
        { label: "体型", value: "中等" },
        { label: "性格", value: "温顺" },
        { label: "训练情况", value: "已学会基本指令" }
      ],
      health: [
        { label: "驱虫情况", value: "已驱虫" },
        { label: "疫苗情况", value: "已打两针" },
        { label: "体检情况", value: "健康" },
        { label: "其他情况", value: "无" }
      ],
      description: "纯种金毛幼犬，3个月大，已打两针疫苗，已驱虫，身体健康。性格温顺，已学会基本指令，适合家庭饲养。可上门看狗，价格可谈。",
      owner: {
        name: "李女士",
        avatar: "/static/images/avatar.png",
        type: "个人",
        rating: "A+",
        isVerified: true
      },
      contact: {
        name: "李女士",
        phone: "13912345678"
      }
    });
    const relatedPets = common_vendor.ref([
      {
        id: "pet001",
        title: "纯种金毛幼犬",
        price: "1800元",
        breed: "金毛",
        age: "2个月",
        image: "/static/images/pet-similar1.jpg",
        tags: ["纯种", "健康", "可上门看"]
      },
      {
        id: "pet002",
        title: "纯种金毛幼犬",
        price: "2200元",
        breed: "金毛",
        age: "4个月",
        image: "/static/images/pet-similar2.jpg",
        tags: ["纯种", "已打疫苗"]
      },
      {
        id: "pet003",
        title: "拉布拉多幼犬",
        price: "1600元",
        breed: "拉布拉多",
        age: "3个月",
        image: "/static/images/pet-similar3.jpg",
        tags: ["健康", "聪明"]
      }
    ]);
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: petData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const navigateToPet = (id) => {
      if (id === petData.value.id)
        return;
      common_vendor.index.navigateTo({ url: `/pages/publish/pet-detail?id=${id}` });
    };
    const navigateToPetList = (e) => {
      if (e)
        e.stopPropagation();
      const petCategory = petData.value.breed || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=pet&title=${encodeURIComponent("宠物信息")}&category=${encodeURIComponent(petCategory)}&active=pet`
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const openChat = () => {
      if (!petData.value.owner || !petData.value.owner.id) {
        common_vendor.index.showToast({
          title: "无法获取发布者信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/chat/chat?targetId=${petData.value.owner.id}&targetName=${encodeURIComponent(petData.value.owner.name)}&targetAvatar=${encodeURIComponent(petData.value.owner.avatar)}`
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.setNavigationBarTitle({
        title: "宠物详情"
      });
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(petData.value.title),
        e: common_vendor.t(petData.value.price),
        f: common_vendor.f(petData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        g: common_vendor.t(formatTime(petData.value.publishTime)),
        h: common_vendor.f(petData.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        i: common_vendor.t(petData.value.breed),
        j: common_vendor.t(petData.value.age),
        k: common_vendor.t(petData.value.gender),
        l: common_vendor.t(petData.value.vaccination),
        m: common_vendor.f(petData.value.features, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        n: common_vendor.f(petData.value.health, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        o: common_vendor.t(petData.value.description),
        p: petData.value.owner.avatar,
        q: common_vendor.t(petData.value.owner.name),
        r: common_vendor.t(petData.value.owner.type),
        s: common_vendor.t(petData.value.owner.rating),
        t: petData.value.owner.isVerified
      }, petData.value.owner.isVerified ? {} : {}, {
        v: common_vendor.t(petData.value.contact.name),
        w: common_vendor.t(petData.value.contact.phone),
        x: common_vendor.o(callPhone),
        y: common_vendor.f(relatedPets.value.slice(0, 3), (pet, index, i0) => {
          return common_vendor.e({
            a: pet.image,
            b: common_vendor.t(pet.title),
            c: common_vendor.t(pet.breed),
            d: common_vendor.t(pet.age),
            e: common_vendor.f(pet.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            f: pet.tags && pet.tags.length > 2
          }, pet.tags && pet.tags.length > 2 ? {
            g: common_vendor.t(pet.tags.length - 2)
          } : {}, {
            h: common_vendor.t(pet.price),
            i: index,
            j: common_vendor.o(($event) => navigateToPet(pet.id), index)
          });
        }),
        z: relatedPets.value.length === 0
      }, relatedPets.value.length === 0 ? {
        A: common_assets._imports_1$3
      } : {}, {
        B: relatedPets.value.length > 0
      }, relatedPets.value.length > 0 ? {
        C: common_vendor.o(navigateToPetList)
      } : {}, {
        D: common_assets._imports_12,
        E: common_vendor.o(goToHome),
        F: common_assets._imports_3$2,
        G: common_vendor.o(toggleCollect),
        H: common_assets._imports_3$3,
        I: common_assets._imports_14,
        J: common_vendor.o(openChat),
        K: common_vendor.o(callPhone)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/pet-detail.js.map
