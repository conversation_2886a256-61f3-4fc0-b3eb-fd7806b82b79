/**
 * 网络请求封装模块
 */

// API基础URL - 根据环境自动切换
const BASE_URL = process.env.NODE_ENV === 'development'
  ? 'http://localhost:8080/api'     // 开发环境 - 修复路径匹配
  : 'https://api.czshw.com/api';    // 生产环境

// HTTP请求方法
const HTTP_METHOD = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE'
};

// 错误码映射表
const ERROR_CODE_MAP = {
  400: '请求参数错误',
  401: '未授权，请重新登录',
  403: '拒绝访问',
  404: '请求的资源不存在',
  500: '服务器内部错误',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时'
};

// 请求队列，用于取消重复请求
let requestQueue = [];

/**
 * 移除请求队列中的请求
 * @param {Object} config 请求配置
 */
const removeRequest = (config) => {
  const index = requestQueue.findIndex(item => {
    return item.url === config.url && 
           item.method === config.method && 
           JSON.stringify(item.data) === JSON.stringify(config.data);
  });
  
  if (index !== -1) {
    requestQueue.splice(index, 1);
  }
};

/**
 * 检查是否存在相同的请求
 * @param {Object} config 请求配置
 * @returns {Boolean} 是否存在
 */
const checkSameRequest = (config) => {
  const index = requestQueue.findIndex(item => {
    return item.url === config.url && 
           item.method === config.method && 
           JSON.stringify(item.data) === JSON.stringify(config.data);
  });
  
  if (index !== -1) {
    return true;
  }
  
  // 不存在则添加到队列中
  requestQueue.push(config);
  return false;
};

/**
 * 构建完整的API URL
 * @param {String} url 接口路径
 * @returns {String} 完整的URL
 */
const buildUrl = (url) => {
  if (url.startsWith('http')) {
    return url;
  }
  return `${BASE_URL}${url.startsWith('/') ? url : `/${url}`}`;
};

/**
 * 从本地存储获取token
 * @returns {String} token
 */
const getToken = () => {
  try {
    const userInfo = uni.getStorageSync('user_info');
    return userInfo ? userInfo.token : '';
  } catch (e) {
    console.error('获取token失败', e);
    return '';
  }
};

/**
 * 处理响应错误
 * @param {Object} response 响应对象
 * @returns {Object} 错误对象
 */
const handleResponseError = (response) => {
  const { statusCode, data } = response;
  
  // 处理HTTP错误
  if (statusCode !== 200) {
    const message = ERROR_CODE_MAP[statusCode] || '网络异常，请稍后再试';
    
    // 401特殊处理：清除登录信息并跳转
    if (statusCode === 401) {
      uni.removeStorageSync('user_info');
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
    }
    
    return Promise.reject({ message, response });
  }
  
  // 处理业务错误
  if (data && data.code !== 0 && data.code !== 200) {
    return Promise.reject({ message: data.message || '请求失败', response });
  }
  
  return data;
};

/**
 * 统一请求封装
 * @param {Object} options 请求配置
 * @returns {Promise} Promise对象
 */
const request = (options = {}) => {
  const { url, method = HTTP_METHOD.GET, data = {}, header = {}, loading = true, retry = 1 } = options;
  
  // 构建请求配置
  const config = {
    url: buildUrl(url),
    method,
    data,
    header: {
      'Content-Type': 'application/json',
      ...header
    }
  };
  
  // 添加token
  const token = getToken();
  if (token) {
    config.header.Authorization = `Bearer ${token}`;
  }
  
  // 如果是重复请求，直接返回拒绝
  if (checkSameRequest(config)) {
    return Promise.reject({ message: '请勿重复请求' });
  }
  
  // 显示加载中
  if (loading) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
  }
  
  // 发起请求
  return new Promise((resolve, reject) => {
    uni.request({
      ...config,
      success: (response) => {
        try {
          const result = handleResponseError(response);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      },
      fail: (error) => {
        // 网络错误时重试
        if (retry > 0 && (error.errMsg || '').includes('request:fail')) {
          setTimeout(() => {
            options.retry = retry - 1;
            resolve(request(options));
          }, 1000);
          return;
        }
        
        reject({ message: '网络连接失败，请检查网络', error });
      },
      complete: () => {
        // 隐藏加载中
        if (loading) {
          uni.hideLoading();
        }
        // 从队列中移除
        removeRequest(config);
      }
    });
  });
};

// 导出不同请求方法
export default {
  get: (url, data = {}, options = {}) => {
    return request({ url, method: HTTP_METHOD.GET, data, ...options });
  },
  post: (url, data = {}, options = {}) => {
    return request({ url, method: HTTP_METHOD.POST, data, ...options });
  },
  put: (url, data = {}, options = {}) => {
    return request({ url, method: HTTP_METHOD.PUT, data, ...options });
  },
  delete: (url, data = {}, options = {}) => {
    return request({ url, method: HTTP_METHOD.DELETE, data, ...options });
  },
  // 导出原始request方法，供特殊需求使用
  request
}; 