/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-fee967c6, html.data-v-fee967c6, #app.data-v-fee967c6, .index-container.data-v-fee967c6 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.product-card.data-v-fee967c6 {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.product-card.data-v-fee967c6:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.product-image-container.data-v-fee967c6 {
  width: 100%;
  height: 300rpx;
  position: relative;
}
.product-image-container .product-image.data-v-fee967c6 {
  width: 100%;
  height: 100%;
}
.product-image-container .product-tag.data-v-fee967c6 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  padding: 6rpx 16rpx;
  background-color: rgba(255, 59, 105, 0.9);
  color: #FFFFFF;
  font-size: 22rpx;
  border-radius: 16rpx;
  font-weight: 500;
}
.product-info.data-v-fee967c6 {
  padding: 20rpx;
}
.product-info .product-title.data-v-fee967c6 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 1.4;
  height: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.product-info .shop-info.data-v-fee967c6 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.product-info .shop-info .shop-logo.data-v-fee967c6 {
  width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  margin-right: 8rpx;
}
.product-info .shop-info .shop-name.data-v-fee967c6 {
  font-size: 24rpx;
  color: #8E8E93;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.product-info .shop-info .distance.data-v-fee967c6 {
  font-size: 24rpx;
  color: #8E8E93;
}
.product-info .product-bottom.data-v-fee967c6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product-info .product-bottom .price-container.data-v-fee967c6 {
  display: flex;
  align-items: baseline;
}
.product-info .product-bottom .price-container .price-symbol.data-v-fee967c6 {
  font-size: 24rpx;
  color: #FF3B69;
  margin-right: 4rpx;
}
.product-info .product-bottom .price-container .price-value.data-v-fee967c6 {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF3B69;
}
.product-info .product-bottom .price-container .price-original.data-v-fee967c6 {
  font-size: 24rpx;
  color: #8E8E93;
  margin-left: 12rpx;
  text-decoration: line-through;
}
.product-info .product-bottom .sold-count.data-v-fee967c6 {
  font-size: 24rpx;
  color: #8E8E93;
}
.product-info .product-labels.data-v-fee967c6 {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16rpx;
}
.product-info .product-labels .label-item.data-v-fee967c6 {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
  color: #FF3B69;
}
.action-btn.data-v-fee967c6 {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(255, 59, 105, 0.3);
}
.action-btn .action-icon.data-v-fee967c6 {
  width: 32rpx;
  height: 32rpx;
  color: #FFFFFF;
}
.action-btn.data-v-fee967c6:active {
  transform: scale(0.9);
}