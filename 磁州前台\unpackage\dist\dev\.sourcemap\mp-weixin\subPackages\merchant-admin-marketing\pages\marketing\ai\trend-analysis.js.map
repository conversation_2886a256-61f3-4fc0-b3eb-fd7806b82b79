{"version": 3, "file": "trend-analysis.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/ai/trend-analysis.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xhaVx0cmVuZC1hbmFseXNpcy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"trend-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">消费趋势分析</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"refresh-icon\" @click=\"refreshData\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" fill=\"#FFFFFF\">\r\n            <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 页面内容 -->\r\n    <scroll-view scroll-y class=\"page-content\">\r\n      <!-- 数据概览卡片 -->\r\n      <view class=\"overview-section\">\r\n        <view class=\"overview-header\">\r\n          <view class=\"header-left\">\r\n            <text class=\"header-title\">数据概览</text>\r\n            <text class=\"header-subtitle\">最近30天消费趋势</text>\r\n          </view>\r\n          <view class=\"date-selector\" @click=\"showDatePicker\">\r\n            <text class=\"date-text\">{{dateRange}}</text>\r\n            <view class=\"date-icon\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" fill=\"#666666\">\r\n                <path d=\"M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z\"/>\r\n              </svg>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"metrics-grid\">\r\n          <view class=\"metric-card\">\r\n            <view class=\"metric-icon sales\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" fill=\"#6366F1\">\r\n                <path d=\"M7 18h2V6H7v12zm4 4h2V2h-2v20zm-8-8h2v-4H3v4zm12 4h2V6h-2v12zm4-8v4h2v-4h-2z\"/>\r\n              </svg>\r\n            </view>\r\n            <view class=\"metric-content\">\r\n              <text class=\"metric-label\">总销售额</text>\r\n              <view class=\"metric-value-container\">\r\n                <text class=\"metric-value\">¥ {{formatNumber(overviewData.totalSales)}}</text>\r\n                <view class=\"metric-trend up\">\r\n                  <view class=\"trend-icon\"></view>\r\n                  <text class=\"trend-value\">{{overviewData.salesGrowth}}%</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"metric-card\">\r\n            <view class=\"metric-icon orders\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" fill=\"#FF9500\">\r\n                <path d=\"M18 17H6v-2h12v2zm0-4H6v-2h12v2zm0-4H6V7h12v2zM3 22l1.5-1.5L6 22l1.5-1.5L9 22l1.5-1.5L12 22l1.5-1.5L15 22l1.5-1.5L18 22l1.5-1.5L21 22V2l-1.5 1.5L18 2l-1.5 1.5L15 2l-1.5 1.5L12 2l-1.5 1.5L9 2 7.5 3.5 6 2 4.5 3.5 3 2v20z\"/>\r\n              </svg>\r\n            </view>\r\n            <view class=\"metric-content\">\r\n              <text class=\"metric-label\">订单数量</text>\r\n              <view class=\"metric-value-container\">\r\n                <text class=\"metric-value\">{{formatNumber(overviewData.totalOrders)}}</text>\r\n                <view class=\"metric-trend up\">\r\n                  <view class=\"trend-icon\"></view>\r\n                  <text class=\"trend-value\">{{overviewData.ordersGrowth}}%</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"metric-card\">\r\n            <view class=\"metric-icon customers\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" fill=\"#34C759\">\r\n                <path d=\"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5z\"/>\r\n              </svg>\r\n            </view>\r\n            <view class=\"metric-content\">\r\n              <text class=\"metric-label\">客户数量</text>\r\n              <view class=\"metric-value-container\">\r\n                <text class=\"metric-value\">{{formatNumber(overviewData.totalCustomers)}}</text>\r\n                <view class=\"metric-trend up\">\r\n                  <view class=\"trend-icon\"></view>\r\n                  <text class=\"trend-value\">{{overviewData.customersGrowth}}%</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"metric-card\">\r\n            <view class=\"metric-icon average\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\" fill=\"#FF3B30\">\r\n                <path d=\"M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z\"/>\r\n              </svg>\r\n            </view>\r\n            <view class=\"metric-content\">\r\n              <text class=\"metric-label\">客单价</text>\r\n              <view class=\"metric-value-container\">\r\n                <text class=\"metric-value\">¥ {{overviewData.averageOrderValue}}</text>\r\n                <view class=\"metric-trend down\">\r\n                  <view class=\"trend-icon\"></view>\r\n                  <text class=\"trend-value\">{{overviewData.aovGrowth}}%</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 分析概述 -->\r\n      <view class=\"summary-section\">\r\n        <view class=\"summary-header\">\r\n          <text class=\"summary-title\">趋势概述</text>\r\n          <view class=\"date-range\">\r\n            <text class=\"date-text\">{{dateRange}}</text>\r\n            <view class=\"date-icon\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"summary-content\">\r\n          <text class=\"summary-text\">基于最近30天的消费数据分析，您的店铺顾客消费偏好正在发生变化。女性顾客对高端美妆产品的兴趣增加了15%，25-35岁男性对科技产品的购买频率提高了8%。建议调整商品结构，增加相关品类的库存和促销力度。</text>\r\n        </view>\r\n        \r\n        <view class=\"ai-badge\">\r\n          <text class=\"badge-text\">AI分析</text>\r\n          <text class=\"badge-confidence\">可信度: 98%</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 销售趋势图表 -->\r\n      <view class=\"chart-section\">\r\n        <view class=\"section-header\">\r\n          <view class=\"header-left\">\r\n            <text class=\"section-title\">销售趋势</text>\r\n            <text class=\"section-subtitle\">过去30天销售与订单量变化</text>\r\n          </view>\r\n          <view class=\"chart-tabs\">\r\n            <view \r\n              v-for=\"(tab, index) in chartTabs\" \r\n              :key=\"index\" \r\n              :class=\"['chart-tab', activeChartTab === index ? 'active' : '']\"\r\n              @click=\"switchChartTab(index)\"\r\n            >\r\n              {{tab}}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"chart-container\">\r\n          <view class=\"chart-legend\">\r\n            <view class=\"legend-item\">\r\n              <view class=\"legend-color sales-line\"></view>\r\n              <text class=\"legend-text\">销售额</text>\r\n            </view>\r\n            <view class=\"legend-item\">\r\n              <view class=\"legend-color orders-line\"></view>\r\n              <text class=\"legend-text\">订单量</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"line-chart\">\r\n            <!-- 图表网格线 -->\r\n            <view class=\"chart-grid\">\r\n              <view class=\"grid-line\" v-for=\"i in 5\" :key=\"i\"></view>\r\n            </view>\r\n            \r\n            <!-- 销售额曲线 -->\r\n            <view class=\"chart-line sales-line\">\r\n              <svg :width=\"chartWidth\" :height=\"chartHeight\" viewBox=\"0 0 300 150\">\r\n                <path :d=\"salesPath\" fill=\"none\" stroke=\"#6366F1\" stroke-width=\"2\" />\r\n                <circle v-for=\"(point, index) in salesPoints\" :key=\"'sales-point-'+index\"\r\n                  :cx=\"point.x\" :cy=\"point.y\" r=\"4\" fill=\"#6366F1\"\r\n                />\r\n              </svg>\r\n            </view>\r\n            \r\n            <!-- 订单量曲线 -->\r\n            <view class=\"chart-line orders-line\">\r\n              <svg :width=\"chartWidth\" :height=\"chartHeight\" viewBox=\"0 0 300 150\">\r\n                <path :d=\"ordersPath\" fill=\"none\" stroke=\"#FF9500\" stroke-width=\"2\" />\r\n                <circle v-for=\"(point, index) in ordersPoints\" :key=\"'orders-point-'+index\"\r\n                  :cx=\"point.x\" :cy=\"point.y\" r=\"4\" fill=\"#FF9500\"\r\n                />\r\n              </svg>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- X轴标签 -->\r\n          <view class=\"x-axis\">\r\n            <text v-for=\"(date, index) in chartData.dates\" :key=\"index\" class=\"x-axis-label\">{{date}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 数据统计 -->\r\n        <view class=\"chart-stats\">\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-label\">日均销售额</text>\r\n            <text class=\"stat-value\">¥ {{formatNumber(chartStats.avgDailySales)}}</text>\r\n          </view>\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-label\">最高销售日</text>\r\n            <text class=\"stat-value\">{{chartStats.peakSalesDay}}</text>\r\n          </view>\r\n          <view class=\"stat-item\">\r\n            <text class=\"stat-label\">销售增长率</text>\r\n            <text class=\"stat-value\">{{chartStats.salesGrowthRate}}%</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 消费趋势图表 -->\r\n      <view class=\"chart-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">消费类别趋势</text>\r\n          <view class=\"filter-dropdown\" @click=\"showCategoryFilter\">\r\n            <text class=\"selected-value\">全部类别</text>\r\n            <view class=\"dropdown-arrow\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"chart-container\">\r\n          <!-- 这里是图表，实际项目中可以使用ECharts等图表库 -->\r\n          <view class=\"chart-placeholder\">\r\n            <view class=\"chart-bars\">\r\n              <view class=\"chart-bar\" style=\"height: 60%;\">\r\n                <text class=\"bar-label\">美妆</text>\r\n              </view>\r\n              <view class=\"chart-bar\" style=\"height: 45%;\">\r\n                <text class=\"bar-label\">服装</text>\r\n              </view>\r\n              <view class=\"chart-bar\" style=\"height: 75%;\">\r\n                <text class=\"bar-label\">科技</text>\r\n              </view>\r\n              <view class=\"chart-bar\" style=\"height: 30%;\">\r\n                <text class=\"bar-label\">家居</text>\r\n              </view>\r\n              <view class=\"chart-bar\" style=\"height: 50%;\">\r\n                <text class=\"bar-label\">食品</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"chart-legend\">\r\n              <view class=\"legend-item\">\r\n                <view class=\"legend-color\"></view>\r\n                <text class=\"legend-text\">消费比例</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 消费者画像变化 -->\r\n      <view class=\"profile-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">消费者画像变化</text>\r\n        </view>\r\n        \r\n        <view class=\"profile-cards\">\r\n          <view class=\"profile-card\">\r\n            <view class=\"profile-icon gender-female\"></view>\r\n            <view class=\"profile-content\">\r\n              <text class=\"profile-title\">女性顾客</text>\r\n              <text class=\"profile-value\">占比 65% <text class=\"trend-up\">↑3%</text></text>\r\n              <view class=\"profile-tags\">\r\n                <text class=\"profile-tag\">25-35岁</text>\r\n                <text class=\"profile-tag\">高端美妆</text>\r\n                <text class=\"profile-tag\">护肤品</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"profile-card\">\r\n            <view class=\"profile-icon gender-male\"></view>\r\n            <view class=\"profile-content\">\r\n              <text class=\"profile-title\">男性顾客</text>\r\n              <text class=\"profile-value\">占比 35% <text class=\"trend-down\">↓3%</text></text>\r\n              <view class=\"profile-tags\">\r\n                <text class=\"profile-tag\">25-35岁</text>\r\n                <text class=\"profile-tag\">科技产品</text>\r\n                <text class=\"profile-tag\">数码配件</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 智能建议 -->\r\n      <view class=\"recommendations-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">智能建议</text>\r\n        </view>\r\n        \r\n        <view class=\"recommendation-list\">\r\n          <view class=\"recommendation-item\">\r\n            <view class=\"recommendation-icon stock\"></view>\r\n            <view class=\"recommendation-content\">\r\n              <text class=\"recommendation-title\">库存调整</text>\r\n              <text class=\"recommendation-desc\">建议增加高端美妆产品库存20%，减少男士服装库存10%</text>\r\n              <view class=\"recommendation-actions\">\r\n                <button class=\"action-btn apply\" @click=\"applyRecommendation(0)\">应用</button>\r\n                <button class=\"action-btn ignore\" @click=\"ignoreRecommendation(0)\">忽略</button>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"recommendation-item\">\r\n            <view class=\"recommendation-icon promotion\"></view>\r\n            <view class=\"recommendation-content\">\r\n              <text class=\"recommendation-title\">促销策略</text>\r\n              <text class=\"recommendation-desc\">针对25-35岁女性顾客推出美妆套装优惠活动，预计可提升销售额15%</text>\r\n              <view class=\"recommendation-actions\">\r\n                <button class=\"action-btn apply\" @click=\"applyRecommendation(1)\">应用</button>\r\n                <button class=\"action-btn ignore\" @click=\"ignoreRecommendation(1)\">忽略</button>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"recommendation-item\">\r\n            <view class=\"recommendation-icon product\"></view>\r\n            <view class=\"recommendation-content\">\r\n              <text class=\"recommendation-title\">商品结构</text>\r\n              <text class=\"recommendation-desc\">建议引入3个新的高端美妆品牌，扩充科技产品品类</text>\r\n              <view class=\"recommendation-actions\">\r\n                <button class=\"action-btn apply\" @click=\"applyRecommendation(2)\">应用</button>\r\n                <button class=\"action-btn ignore\" @click=\"ignoreRecommendation(2)\">忽略</button>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 底部空间 -->\r\n      <view style=\"height: 20px;\"></view>\r\n    </scroll-view>\r\n    \r\n    <!-- 底部导航栏 -->\r\n    <tab-bar active-tab=\"marketing\" @tab-change=\"handleTabChange\"></tab-bar>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport TabBar from '../../../../../components/TabBar.vue'\r\n\r\nexport default {\r\n  components: {\r\n    TabBar\r\n  },\r\n  data() {\r\n    return {\r\n      dateRange: '2023/10/01 - 2023/10/30',\r\n      overviewData: {\r\n        totalSales: 128650,\r\n        salesGrowth: 15.8,\r\n        totalOrders: 1256,\r\n        ordersGrowth: 12.3,\r\n        totalCustomers: 876,\r\n        customersGrowth: 8.5,\r\n        averageOrderValue: 102.43,\r\n        aovGrowth: -2.1\r\n      },\r\n      isLoading: false,\r\n      chartTabs: ['日', '周', '月'],\r\n      activeChartTab: 0,\r\n      chartWidth: 300,\r\n      chartHeight: 150,\r\n      salesPath: '',\r\n      ordersPath: '',\r\n      salesPoints: [],\r\n      ordersPoints: [],\r\n      chartData: {\r\n        dates: ['10/01', '10/05', '10/10', '10/15', '10/20', '10/25', '10/30'],\r\n        sales: [5000, 8000, 7500, 12000, 9000, 15000, 13000],\r\n        orders: [50, 80, 75, 120, 90, 150, 130]\r\n      },\r\n      chartStats: {\r\n        avgDailySales: 4288.33,\r\n        peakSalesDay: '10月25日',\r\n        salesGrowthRate: 15.8\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    refreshData() {\r\n      this.isLoading = true;\r\n      \r\n      // 模拟数据刷新\r\n      setTimeout(() => {\r\n        this.isLoading = false;\r\n        uni.showToast({\r\n          title: '数据已更新',\r\n          icon: 'success'\r\n        });\r\n      }, 1500);\r\n    },\r\n    showCategoryFilter() {\r\n      uni.showActionSheet({\r\n        itemList: ['全部类别', '美妆', '服装', '科技', '家居', '食品'],\r\n        success: (res) => {\r\n          // 处理选择结果\r\n        }\r\n      });\r\n    },\r\n    showDatePicker() {\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    formatNumber(num) {\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n    },\r\n    switchChartTab(index) {\r\n      this.activeChartTab = index;\r\n      // 在实际项目中，这里应该根据选择的时间段重新加载图表数据\r\n      uni.showToast({\r\n        title: '切换到' + this.chartTabs[index] + '视图',\r\n        icon: 'none'\r\n      });\r\n      // 重新计算路径\r\n      this.calculateChartPaths();\r\n    },\r\n    // 计算销售额和订单量的SVG路径\r\n    calculateChartPaths() {\r\n      const salesData = this.chartData.sales;\r\n      const ordersData = this.chartData.orders;\r\n      const maxSales = Math.max(...salesData);\r\n      const maxOrders = Math.max(...ordersData);\r\n      \r\n      // 计算销售额点位置\r\n      this.salesPoints = salesData.map((value, index) => {\r\n        const x = (index / (salesData.length - 1)) * this.chartWidth;\r\n        const y = this.chartHeight - (value / maxSales) * this.chartHeight;\r\n        return { x, y };\r\n      });\r\n      \r\n      // 计算订单量点位置\r\n      this.ordersPoints = ordersData.map((value, index) => {\r\n        const x = (index / (ordersData.length - 1)) * this.chartWidth;\r\n        const y = this.chartHeight - (value / maxOrders) * this.chartHeight;\r\n        return { x, y };\r\n      });\r\n      \r\n      // 生成销售额路径\r\n      this.salesPath = this.generatePath(this.salesPoints);\r\n      \r\n      // 生成订单量路径\r\n      this.ordersPath = this.generatePath(this.ordersPoints);\r\n    },\r\n    // 生成SVG路径\r\n    generatePath(points) {\r\n      if (points.length === 0) return '';\r\n      \r\n      let path = `M ${points[0].x} ${points[0].y}`;\r\n      \r\n      for (let i = 1; i < points.length; i++) {\r\n        // 使用贝塞尔曲线使线条更平滑\r\n        const cp1x = points[i-1].x + (points[i].x - points[i-1].x) / 3;\r\n        const cp1y = points[i-1].y;\r\n        const cp2x = points[i].x - (points[i].x - points[i-1].x) / 3;\r\n        const cp2y = points[i].y;\r\n        path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${points[i].x} ${points[i].y}`;\r\n      }\r\n      \r\n      return path;\r\n    },\r\n    applyRecommendation(index) {\r\n      uni.showToast({\r\n        title: '已应用建议' + (index + 1),\r\n        icon: 'success'\r\n      })\r\n    },\r\n    \r\n    ignoreRecommendation(index) {\r\n      uni.showToast({\r\n        title: '已忽略建议' + (index + 1),\r\n        icon: 'none'\r\n      })\r\n    },\r\n    \r\n    handleTabChange(tabId) {\r\n      // 处理底部标签页切换事件\r\n      console.log('切换到标签:', tabId);\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化图表\r\n    this.calculateChartPaths();\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.trend-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n}\r\n\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #6366F1, #4F46E5);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(99, 102, 241, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.refresh-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.page-content {\r\n  height: calc(100vh - 77px);\r\n  box-sizing: border-box;\r\n  padding: 15px;\r\n}\r\n\r\n/* 数据概览部分样式 */\r\n.overview-section {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 20px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.header-subtitle {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 4px;\r\n  display: block;\r\n}\r\n\r\n.date-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #F5F7FA;\r\n  padding: 6px 12px;\r\n  border-radius: 16px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.date-icon {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.metrics-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 15px;\r\n}\r\n\r\n.metric-card {\r\n  background-color: #F8FAFC;\r\n  border-radius: 12px;\r\n  padding: 15px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.metric-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px;\r\n}\r\n\r\n.metric-icon.sales {\r\n  background-color: rgba(99, 102, 241, 0.1);\r\n}\r\n\r\n.metric-icon.orders {\r\n  background-color: rgba(255, 149, 0, 0.1);\r\n}\r\n\r\n.metric-icon.customers {\r\n  background-color: rgba(52, 199, 89, 0.1);\r\n}\r\n\r\n.metric-icon.average {\r\n  background-color: rgba(255, 59, 48, 0.1);\r\n}\r\n\r\n.metric-content {\r\n  flex: 1;\r\n}\r\n\r\n.metric-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-bottom: 5px;\r\n  display: block;\r\n}\r\n\r\n.metric-value-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.metric-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.metric-trend {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.trend-icon {\r\n  width: 0;\r\n  height: 0;\r\n  margin-right: 2px;\r\n}\r\n\r\n.metric-trend.up .trend-icon {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-bottom: 6px solid #34C759;\r\n}\r\n\r\n.metric-trend.down .trend-icon {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 6px solid #FF3B30;\r\n}\r\n\r\n.trend-value {\r\n  font-size: 12px;\r\n}\r\n\r\n.metric-trend.up .trend-value {\r\n  color: #34C759;\r\n}\r\n\r\n.metric-trend.down .trend-value {\r\n  color: #FF3B30;\r\n}\r\n\r\n.summary-section {\r\n  margin: 15px;\r\n  padding: 20px;\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  position: relative;\r\n}\r\n\r\n.summary-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.summary-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.date-range {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.date-icon {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #666;\r\n  border-right: 2px solid #666;\r\n  transform: rotate(135deg);\r\n}\r\n\r\n.summary-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.summary-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.6;\r\n}\r\n\r\n.ai-badge {\r\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n  background: linear-gradient(135deg, #1989FA, #0D6EFD);\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.badge-text {\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.badge-confidence {\r\n  font-size: 10px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.chart-section, .profile-section, .recommendations-section {\r\n  margin: 15px;\r\n  padding: 20px;\r\n  background: #FFFFFF;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.section-subtitle {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 4px;\r\n  display: block;\r\n}\r\n\r\n.filter-dropdown {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n}\r\n\r\n.selected-value {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.dropdown-arrow {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #666;\r\n  border-right: 2px solid #666;\r\n  transform: rotate(135deg);\r\n}\r\n\r\n.chart-container {\r\n  height: 250px;\r\n  position: relative;\r\n}\r\n\r\n.chart-placeholder {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-bars {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: flex-end;\r\n  padding: 0 10px;\r\n}\r\n\r\n.chart-bar {\r\n  width: 40px;\r\n  background: linear-gradient(to top, #1989FA, #0D6EFD);\r\n  border-radius: 5px 5px 0 0;\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.bar-label {\r\n  position: absolute;\r\n  bottom: -25px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.chart-legend {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.legend-color {\r\n  width: 12px;\r\n  height: 12px;\r\n  background: linear-gradient(to right, #1989FA, #0D6EFD);\r\n  border-radius: 2px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.legend-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.profile-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.profile-card {\r\n  width: 50%;\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.profile-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  margin-right: 10px;\r\n  position: relative;\r\n}\r\n\r\n.profile-icon.gender-female {\r\n  background-color: rgba(255, 105, 180, 0.1);\r\n}\r\n\r\n.profile-icon.gender-female::before {\r\n  content: '♀';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  font-size: 24px;\r\n  color: #FF69B4;\r\n}\r\n\r\n.profile-icon.gender-male {\r\n  background-color: rgba(25, 137, 250, 0.1);\r\n}\r\n\r\n.profile-icon.gender-male::before {\r\n  content: '♂';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  font-size: 24px;\r\n  color: #1989FA;\r\n}\r\n\r\n.profile-content {\r\n  flex: 1;\r\n}\r\n\r\n.profile-title {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.profile-value {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.trend-up {\r\n  color: #34C759;\r\n}\r\n\r\n.trend-down {\r\n  color: #FF3B30;\r\n}\r\n\r\n.profile-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.profile-tag {\r\n  font-size: 10px;\r\n  color: #666;\r\n  background: #F5F7FA;\r\n  padding: 2px 6px;\r\n  border-radius: 10px;\r\n  margin-right: 5px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.recommendation-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.recommendation-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  background: #F8FAFC;\r\n  border-radius: 10px;\r\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);\r\n}\r\n\r\n.recommendation-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  margin-right: 15px;\r\n  position: relative;\r\n}\r\n\r\n.recommendation-icon.stock {\r\n  background-color: rgba(25, 137, 250, 0.1);\r\n}\r\n\r\n.recommendation-icon.stock::before {\r\n  content: '📦';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  font-size: 20px;\r\n}\r\n\r\n.recommendation-icon.promotion {\r\n  background-color: rgba(255, 149, 0, 0.1);\r\n}\r\n\r\n.recommendation-icon.promotion::before {\r\n  content: '🏷️';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  font-size: 20px;\r\n}\r\n\r\n.recommendation-icon.product {\r\n  background-color: rgba(52, 199, 89, 0.1);\r\n}\r\n\r\n.recommendation-icon.product::before {\r\n  content: '🛍️';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  font-size: 20px;\r\n}\r\n\r\n.recommendation-content {\r\n  flex: 1;\r\n}\r\n\r\n.recommendation-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.recommendation-desc {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.recommendation-actions {\r\n  display: flex;\r\n}\r\n\r\n.action-btn {\r\n  padding: 5px 15px;\r\n  border-radius: 15px;\r\n  font-size: 12px;\r\n  margin-right: 10px;\r\n  border: none;\r\n}\r\n\r\n.action-btn.apply {\r\n  background: #1989FA;\r\n  color: white;\r\n}\r\n\r\n.action-btn.ignore {\r\n  background: #F5F7FA;\r\n  color: #666;\r\n}\r\n\r\n.chart-tabs {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.chart-tab {\r\n  padding: 5px 10px;\r\n  border-radius: 15px;\r\n  background: #F5F7FA;\r\n  margin-left: 5px;\r\n  cursor: pointer;\r\n}\r\n\r\n.chart-tab.active {\r\n  background: #1989FA;\r\n  color: white;\r\n}\r\n\r\n.line-chart {\r\n  position: relative;\r\n  height: 180px;\r\n  margin: 20px 0;\r\n}\r\n\r\n.chart-grid {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.grid-line {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 1px;\r\n  background: #E0E0E0;\r\n}\r\n\r\n.grid-line:nth-child(1) { top: 0; }\r\n.grid-line:nth-child(2) { top: 25%; }\r\n.grid-line:nth-child(3) { top: 50%; }\r\n.grid-line:nth-child(4) { top: 75%; }\r\n.grid-line:nth-child(5) { top: 100%; }\r\n\r\n.chart-line {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.legend-color.sales-line {\r\n  background-color: #6366F1;\r\n}\r\n\r\n.legend-color.orders-line {\r\n  background-color: #FF9500;\r\n}\r\n\r\n.x-axis {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 0 10px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.x-axis-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  text-align: center;\r\n  flex: 1;\r\n}\r\n\r\n.chart-stats {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  margin-top: 20px;\r\n  padding-top: 15px;\r\n  border-top: 1px solid #EEEEEE;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/ai/trend-analysis.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAsVA,eAAe,MAAW;AAE1B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,cAAc;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,WAAW;AAAA,MACZ;AAAA,MACD,WAAW;AAAA,MACX,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa,CAAE;AAAA,MACf,cAAc,CAAE;AAAA,MAChB,WAAW;AAAA,QACT,OAAO,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,OAAO;AAAA,QACrE,OAAO,CAAC,KAAM,KAAM,MAAM,MAAO,KAAM,MAAO,IAAK;AAAA,QACnD,QAAQ,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,GAAG;AAAA,MACvC;AAAA,MACD,YAAY;AAAA,QACV,eAAe;AAAA,QACf,cAAc;AAAA,QACd,iBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,cAAc;AACZ,WAAK,YAAY;AAGjB,iBAAW,MAAM;AACf,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AAAA,IACD,qBAAqB;AACnBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QAC/C,SAAS,CAAC,QAAQ;AAAA,QAElB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa,KAAK;AAChB,aAAO,IAAI,SAAU,EAAC,QAAQ,yBAAyB,GAAG;AAAA,IAC3D;AAAA,IACD,eAAe,OAAO;AACpB,WAAK,iBAAiB;AAEtBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,QAAQ,KAAK,UAAU,KAAK,IAAI;AAAA,QACvC,MAAM;AAAA,MACR,CAAC;AAED,WAAK,oBAAmB;AAAA,IACzB;AAAA;AAAA,IAED,sBAAsB;AACpB,YAAM,YAAY,KAAK,UAAU;AACjC,YAAM,aAAa,KAAK,UAAU;AAClC,YAAM,WAAW,KAAK,IAAI,GAAG,SAAS;AACtC,YAAM,YAAY,KAAK,IAAI,GAAG,UAAU;AAGxC,WAAK,cAAc,UAAU,IAAI,CAAC,OAAO,UAAU;AACjD,cAAM,IAAK,SAAS,UAAU,SAAS,KAAM,KAAK;AAClD,cAAM,IAAI,KAAK,cAAe,QAAQ,WAAY,KAAK;AACvD,eAAO,EAAE,GAAG;MACd,CAAC;AAGD,WAAK,eAAe,WAAW,IAAI,CAAC,OAAO,UAAU;AACnD,cAAM,IAAK,SAAS,WAAW,SAAS,KAAM,KAAK;AACnD,cAAM,IAAI,KAAK,cAAe,QAAQ,YAAa,KAAK;AACxD,eAAO,EAAE,GAAG;MACd,CAAC;AAGD,WAAK,YAAY,KAAK,aAAa,KAAK,WAAW;AAGnD,WAAK,aAAa,KAAK,aAAa,KAAK,YAAY;AAAA,IACtD;AAAA;AAAA,IAED,aAAa,QAAQ;AACnB,UAAI,OAAO,WAAW;AAAG,eAAO;AAEhC,UAAI,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC;AAE1C,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAEtC,cAAM,OAAO,OAAO,IAAE,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,IAAE,CAAC,EAAE,KAAK;AAC7D,cAAM,OAAO,OAAO,IAAE,CAAC,EAAE;AACzB,cAAM,OAAO,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,IAAE,CAAC,EAAE,KAAK;AAC3D,cAAM,OAAO,OAAO,CAAC,EAAE;AACvB,gBAAQ,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,MAC5E;AAEA,aAAO;AAAA,IACR;AAAA,IACD,oBAAoB,OAAO;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,WAAW,QAAQ;AAAA,QAC1B,MAAM;AAAA,OACP;AAAA,IACF;AAAA,IAED,qBAAqB,OAAO;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,WAAW,QAAQ;AAAA,QAC1B,MAAM;AAAA,OACP;AAAA,IACF;AAAA,IAED,gBAAgB,OAAO;AAErBA,oBAAY,MAAA,MAAA,OAAA,qFAAA,UAAU,KAAK;AAAA,IAC7B;AAAA,EACD;AAAA,EACD,UAAU;AAER,SAAK,oBAAmB;AAAA,EAC1B;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3eA,GAAG,WAAW,eAAe;"}