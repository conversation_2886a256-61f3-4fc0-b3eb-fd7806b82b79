"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  ConfigurablePremiumActions();
}
const ConfigurablePremiumActions = () => "../../components/premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "join",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const showCategoryPicker = common_vendor.ref(false);
    const showScalePicker = common_vendor.ref(false);
    const showTimePicker = common_vendor.ref(false);
    const showVersionModal = common_vendor.ref(false);
    const formData = common_vendor.reactive({
      shopName: "",
      address: "",
      category: "",
      scale: "",
      businessTime: "",
      contactPhone: "",
      description: "",
      shopImage: null,
      logo: null,
      qrcode: null,
      album: [],
      joinMethod: "free",
      version: "premium",
      agreed: false
    });
    const adStats = common_vendor.ref({
      join: {
        remaining: 1,
        limit: 1,
        lastWatchTime: null
      }
    });
    const categories = common_vendor.reactive(["美食小吃", "休闲娱乐", "装修家居", "母婴专区", "房产楼盘", "到家服务", "车辆服务", "教育培训", "其他行业"]);
    const scales = common_vendor.reactive(["1-10人", "11-50人", "51-100人", "100-200人", "200人以上"]);
    const businessTimes = common_vendor.reactive(["08:00-18:00", "09:00-21:00", "24小时营业", "自定义"]);
    const versions = common_vendor.reactive([
      {
        key: "basic",
        name: "基础版",
        price: "49.9",
        desc: "适合个体商户",
        features: [
          "商品发布数量：最多20个",
          "店铺展示位置：普通位置",
          "客户数据分析：基础版",
          "免费获赠一次店铺推广"
        ]
      },
      {
        key: "premium",
        name: "高级版",
        price: "149.9",
        desc: "性价比最高",
        features: [
          "商品发布数量：最多50个",
          "店铺展示位置：优先位置",
          "客户数据分析：专业版",
          "赠送3次店铺推广和置顶",
          "商品视频展示功能"
        ]
      },
      {
        key: "deluxe",
        name: "尊贵版",
        price: "299.9",
        desc: "全功能无限制",
        features: [
          "商品发布数量：无限制",
          "店铺展示位置：最佳位置",
          "客户数据分析：高级版",
          "免费获赠整年店铺推广",
          "优先客服一对一服务",
          "专属VIP店铺标识"
        ]
      }
    ]);
    common_vendor.computed(() => {
      var _a;
      return ((_a = versions.find((v) => v.key === formData.version)) == null ? void 0 : _a.name) || "";
    });
    common_vendor.computed(() => {
      var _a;
      return ((_a = versions.find((v) => v.key === formData.version)) == null ? void 0 : _a.price) || 0;
    });
    common_vendor.computed(() => {
      switch (formData.version) {
        case "premium":
          return "premium-badge";
        case "deluxe":
          return "deluxe-badge";
        default:
          return "basic-badge";
      }
    });
    common_vendor.computed(() => {
      var _a;
      return ((_a = versions.find((v) => v.key === formData.version)) == null ? void 0 : _a.features[0]) || "";
    });
    common_vendor.computed(() => {
      var _a;
      return ((_a = versions.find((v) => v.key === formData.version)) == null ? void 0 : _a.features[1]) || "";
    });
    const merchantJoinData = common_vendor.reactive({
      id: "merchant_join",
      title: "商家入驻",
      description: "加入我们的商家平台，获得更多商业机会"
    });
    const goBack = () => common_vendor.index.navigateBack();
    const getLocation = () => {
      common_vendor.index.chooseLocation({
        success: (res) => {
          formData.address = res.address + (res.name || "");
        }
      });
    };
    const hideAllModals = () => {
      showCategoryPicker.value = false;
      showScalePicker.value = false;
      showTimePicker.value = false;
    };
    const showCategoryPickerFn = () => {
      showCategoryPicker.value = true;
    };
    const hideCategoryPicker = () => {
      showCategoryPicker.value = false;
    };
    const selectCategory = (item) => {
      formData.category = item;
      hideCategoryPicker();
    };
    const showScalePickerFn = () => {
      showScalePicker.value = true;
    };
    const hideScalePicker = () => {
      showScalePicker.value = false;
    };
    const selectScale = (item) => {
      formData.scale = item;
      hideScalePicker();
    };
    const showTimePickerFn = () => {
      showTimePicker.value = true;
    };
    const hideTimePicker = () => {
      showTimePicker.value = false;
    };
    const selectTime = (item) => {
      formData.businessTime = item;
      hideTimePicker();
    };
    const uploadImage = (type) => {
      common_vendor.index.chooseImage({
        count: type === "album" ? 10 - formData.album.length : 1,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          if (type === "album") {
            formData.album.push(...res.tempFilePaths);
          } else {
            formData[type] = res.tempFilePaths[0];
          }
        }
      });
    };
    const selectVersion = (versionKey) => {
      formData.version = versionKey;
    };
    const confirmVersion = () => {
      showVersionModal.value = false;
    };
    const hideVersionModal = () => {
      showVersionModal.value = false;
    };
    const toggleAgreement = () => {
      formData.agreed = !formData.agreed;
    };
    const validateForm = (isFree) => {
      if (!formData.shopName) {
        common_vendor.index.showToast({ title: "请输入店铺名称", icon: "none" });
        return false;
      }
      if (!formData.address) {
        common_vendor.index.showToast({ title: "请输入店铺地址", icon: "none" });
        return false;
      }
      if (!formData.category) {
        common_vendor.index.showToast({ title: "请选择所属行业", icon: "none" });
        return false;
      }
      if (!formData.contactPhone) {
        common_vendor.index.showToast({ title: "请输入联系电话", icon: "none" });
        return false;
      }
      if (!formData.agreed) {
        common_vendor.index.showToast({ title: "请阅读并同意服务协议", icon: "none" });
        return false;
      }
      if (!isFree && formData.joinMethod === "paid" && !formData.version) {
        common_vendor.index.showToast({ title: "请选择一个付费版本", icon: "none" });
        return false;
      }
      return true;
    };
    const handleJoinCompleted = (result) => {
      var _a;
      common_vendor.index.__f__("log", "at pages/business/join.vue:626", "入驻完成:", result);
      if (result.type === "ad") {
        common_vendor.index.showModal({
          title: "入驻成功",
          content: `恭喜您成功入驻！获得${((_a = result.data) == null ? void 0 : _a.days) || 30}天免费特权`,
          showCancel: false,
          confirmText: "知道了",
          success: () => {
            common_vendor.index.redirectTo({
              url: "/pages/business/success?shopId=test&type=ad"
            });
          }
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: "入驻成功",
          icon: "success"
        });
        common_vendor.index.redirectTo({
          url: "/pages/business/success?shopId=test&type=payment"
        });
      }
    };
    const handleJoinCancelled = (result) => {
      common_vendor.index.__f__("log", "at pages/business/join.vue:655", "入驻取消:", result);
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "已取消观看广告",
          icon: "none"
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: "已取消支付",
          icon: "none"
        });
      }
    };
    const submitForm = (fromAd = false) => {
      if (!validateForm(fromAd))
        return;
      common_vendor.index.__f__("log", "at pages/business/join.vue:672", "提交的表单数据:", JSON.parse(JSON.stringify(formData)));
      common_vendor.index.showLoading({ title: "提交中..." });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({ title: "提交成功，等待审核", icon: "success" });
        common_vendor.index.navigateTo({ url: "/pages/business/success?shopId=test" });
      }, 1500);
    };
    const loadAdStats = async () => {
      try {
        adStats.value.join = {
          remaining: 1,
          limit: 1,
          lastWatchTime: null
        };
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/business/join.vue:763", "加载广告统计失败", error);
      }
    };
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 20;
      loadAdStats();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$12,
        b: statusBarHeight.value + "px",
        c: common_assets._imports_0$13,
        d: common_vendor.o(goBack),
        e: common_assets._imports_2$4,
        f: formData.shopName,
        g: common_vendor.o(($event) => formData.shopName = $event.detail.value),
        h: formData.address,
        i: common_vendor.o(($event) => formData.address = $event.detail.value),
        j: common_assets._imports_3$4,
        k: common_vendor.o(getLocation),
        l: formData.address
      }, formData.address ? {
        m: common_vendor.o(getLocation)
      } : {}, {
        n: common_vendor.t(formData.category || "点击选择所属行业"),
        o: !formData.category ? 1 : "",
        p: common_assets._imports_1$5,
        q: common_vendor.o(showCategoryPickerFn),
        r: common_vendor.t(formData.scale || "点击选择商家规模人数"),
        s: !formData.scale ? 1 : "",
        t: common_assets._imports_1$5,
        v: common_vendor.o(showScalePickerFn),
        w: common_vendor.t(formData.businessTime || "点击选择营业时间"),
        x: !formData.businessTime ? 1 : "",
        y: common_assets._imports_1$5,
        z: common_vendor.o(showTimePickerFn),
        A: formData.contactPhone,
        B: common_vendor.o(($event) => formData.contactPhone = $event.detail.value),
        C: formData.description,
        D: common_vendor.o(($event) => formData.description = $event.detail.value),
        E: common_vendor.o(($event) => uploadImage("shopImage")),
        F: common_vendor.o(($event) => uploadImage("logo")),
        G: common_vendor.o(($event) => uploadImage("qrcode")),
        H: common_vendor.o(($event) => uploadImage("album")),
        I: common_vendor.o(handleJoinCompleted),
        J: common_vendor.o(handleJoinCancelled),
        K: common_vendor.p({
          pageType: "merchant_join",
          showMode: "selection",
          itemData: merchantJoinData
        }),
        L: showVersionModal.value
      }, showVersionModal.value ? {
        M: common_vendor.o(hideVersionModal)
      } : {}, {
        N: showVersionModal.value
      }, showVersionModal.value ? {
        O: common_assets._imports_11$2,
        P: common_vendor.o(hideVersionModal),
        Q: common_vendor.t(formData.version === "basic" ? "已选择" : "选择此版本"),
        R: formData.version === "basic" ? 1 : "",
        S: formData.version === "basic" ? 1 : "",
        T: common_vendor.o(($event) => selectVersion("basic")),
        U: common_vendor.t(formData.version === "premium" ? "已选择" : "选择此版本"),
        V: formData.version === "premium" ? 1 : "",
        W: formData.version === "premium" ? 1 : "",
        X: common_vendor.o(($event) => selectVersion("premium")),
        Y: common_vendor.t(formData.version === "deluxe" ? "已选择" : "选择此版本"),
        Z: formData.version === "deluxe" ? 1 : "",
        aa: formData.version === "deluxe" ? 1 : "",
        ab: common_vendor.o(($event) => selectVersion("deluxe")),
        ac: common_vendor.o(confirmVersion)
      } : {}, {
        ad: formData.agreed ? 1 : "",
        ae: common_vendor.o(toggleAgreement),
        af: common_vendor.o(($event) => submitForm()),
        ag: showCategoryPicker.value || showScalePicker.value || showTimePicker.value
      }, showCategoryPicker.value || showScalePicker.value || showTimePicker.value ? {
        ah: common_vendor.o(hideAllModals)
      } : {}, {
        ai: showCategoryPicker.value
      }, showCategoryPicker.value ? {
        aj: common_assets._imports_11$2,
        ak: common_vendor.o(hideCategoryPicker),
        al: common_vendor.f(categories, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item),
            b: formData.category === item
          }, formData.category === item ? {
            c: common_assets._imports_1$6
          } : {}, {
            d: index,
            e: common_vendor.o(($event) => selectCategory(item), index),
            f: formData.category === item ? 1 : ""
          });
        })
      } : {}, {
        am: showScalePicker.value
      }, showScalePicker.value ? {
        an: common_assets._imports_11$2,
        ao: common_vendor.o(hideScalePicker),
        ap: common_vendor.f(scales, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item),
            b: formData.scale === item
          }, formData.scale === item ? {
            c: common_assets._imports_1$6
          } : {}, {
            d: index,
            e: common_vendor.o(($event) => selectScale(item), index),
            f: formData.scale === item ? 1 : ""
          });
        })
      } : {}, {
        aq: showTimePicker.value
      }, showTimePicker.value ? {
        ar: common_assets._imports_11$2,
        as: common_vendor.o(hideTimePicker),
        at: common_vendor.f(businessTimes, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item),
            b: formData.businessTime === item
          }, formData.businessTime === item ? {
            c: common_assets._imports_1$6
          } : {}, {
            d: index,
            e: common_vendor.o(($event) => selectTime(item), index),
            f: formData.businessTime === item ? 1 : ""
          });
        })
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/business/join.js.map
