{"version": 3, "file": "SecondHandCard.js", "sources": ["components/cards/SecondHandCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7AvY29tcG9uZW50cy9jYXJkcy9TZWNvbmRIYW5kQ2FyZC52dWU"], "sourcesContent": ["<template>\n  <BaseInfoCard :item=\"item\">\n    <template #content>\n      <view class=\"secondhand-details\">\n        <!-- 商品基本信息 -->\n        <view class=\"product-basic-info\">\n          <view class=\"product-condition\" v-if=\"item.condition\">\n            <view class=\"product-icon condition-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\n                <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\n              </svg>\n            </view>\n            <text class=\"product-text\">{{item.condition}}</text>\n          </view>\n          \n          <view class=\"product-brand\" v-if=\"item.brand\">\n            <view class=\"product-icon brand-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <path d=\"M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z\"></path>\n                <line x1=\"7\" y1=\"7\" x2=\"7.01\" y2=\"7\"></line>\n              </svg>\n            </view>\n            <text class=\"product-text\">{{item.brand}}</text>\n          </view>\n          \n          <view class=\"product-age\" v-if=\"item.age\">\n            <view class=\"product-icon age-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                <polyline points=\"12 6 12 12 16 14\"></polyline>\n              </svg>\n            </view>\n            <text class=\"product-text\">{{item.age}}</text>\n          </view>\n          \n          <view class=\"product-transaction\" v-if=\"item.transactionType\">\n            <view class=\"product-icon transaction-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"23\"></line>\n                <path d=\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"></path>\n              </svg>\n            </view>\n            <text class=\"product-text\">{{item.transactionType}}</text>\n          </view>\n        </view>\n        \n        <!-- 价格对比 -->\n        <view class=\"price-comparison\" v-if=\"item.originalPrice\">\n          <view class=\"price-tag original-price\">\n            <text class=\"price-label\">原价</text>\n            <text class=\"price-value original-value\">¥{{item.originalPrice}}</text>\n          </view>\n          <view class=\"price-tag current-price\">\n            <text class=\"price-label\">现价</text>\n            <text class=\"price-value current-value\">¥{{item.price}}</text>\n          </view>\n          <view class=\"price-tag discount-rate\" v-if=\"calculateDiscount(item.price, item.originalPrice)\">\n            <text class=\"price-value discount-value\">{{calculateDiscount(item.price, item.originalPrice)}}折</text>\n          </view>\n        </view>\n        \n        <!-- 商品描述 -->\n        <view class=\"product-description\" v-if=\"item.description\">\n          <text class=\"description-text\">{{item.description}}</text>\n        </view>\n        \n        <!-- 商品标签 -->\n        <view class=\"product-tags\" v-if=\"item.tags && item.tags.length\">\n          <view class=\"product-tag\" v-for=\"(tag, index) in item.tags\" :key=\"index\">\n            <text class=\"tag-text\">{{tag}}</text>\n          </view>\n        </view>\n        \n        <!-- 交易方式 -->\n        <view class=\"transaction-methods\" v-if=\"filteredTransactionMethods.length\">\n          <view class=\"transaction-method\" v-for=\"(method, index) in filteredTransactionMethods\" :key=\"index\">\n            <view class=\"method-icon\" :class=\"getMethodIconClass(method)\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                <polyline points=\"20 6 9 17 4 12\"></polyline>\n              </svg>\n            </view>\n            <text class=\"method-text\">{{method}}</text>\n          </view>\n        </view>\n      </view>\n    </template>\n  </BaseInfoCard>\n</template>\n\n<script setup>\nimport { computed } from 'vue';\nimport BaseInfoCard from './BaseInfoCard.vue';\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  }\n});\n\n// 过滤掉与tags重复的交易方式\nconst filteredTransactionMethods = computed(() => {\n  if (!props.item.transactionMethods || !props.item.transactionMethods.length) return [];\n  if (!props.item.tags || !props.item.tags.length) return props.item.transactionMethods;\n  \n  // 将tags转换为小写，用于不区分大小写的比较\n  const tagsLowerCase = props.item.tags.map(tag => tag.toLowerCase());\n  \n  // 过滤transactionMethods，去除与tags重复的项\n  return props.item.transactionMethods.filter(method => \n    !tagsLowerCase.includes(method.toLowerCase())\n  );\n});\n\nfunction getMethodIconClass(method) {\n  const methodMap = {\n    '自提': 'pickup-icon',\n    '邮寄': 'delivery-icon',\n    '同城配送': 'local-icon',\n    '见面交易': 'face-icon',\n    '可议价': 'negotiate-icon'\n  };\n  \n  return methodMap[method] || 'default-icon';\n}\n\nfunction calculateDiscount(currentPrice, originalPrice) {\n  if (!currentPrice || !originalPrice || originalPrice <= 0) return null;\n  const discount = (currentPrice / originalPrice * 10).toFixed(1);\n  return discount < 10 ? discount : null; // 只显示打折的情况\n}\n</script>\n\n<style scoped>\n.secondhand-details {\n  margin-top: 16rpx;\n  padding-top: 16rpx;\n  border-top: 1rpx solid rgba(60, 60, 67, 0.1);\n}\n\n/* 商品基本信息 */\n.product-basic-info {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.product-condition, .product-brand, .product-age, .product-transaction {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 8rpx 16rpx;\n  border-radius: 24rpx;\n  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);\n}\n\n.product-icon {\n  margin-right: 8rpx;\n  color: #8e8e93;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.condition-icon {\n  color: #34C759;\n}\n\n.brand-icon {\n  color: #FF9500;\n}\n\n.age-icon {\n  color: #5856D6;\n}\n\n.transaction-icon {\n  color: #FF3B30;\n}\n\n.product-text {\n  font-size: 24rpx;\n  color: #636366;\n  font-weight: 500;\n}\n\n/* 价格对比 */\n.price-comparison {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n  margin-bottom: 16rpx;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 12rpx 16rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.price-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.price-label {\n  font-size: 20rpx;\n  color: #8e8e93;\n  margin-bottom: 4rpx;\n}\n\n.price-value {\n  font-size: 26rpx;\n  font-weight: 600;\n}\n\n.original-value {\n  color: #8e8e93;\n  text-decoration: line-through;\n}\n\n.current-value {\n  color: #FF3B30;\n  font-size: 30rpx;\n}\n\n.discount-rate {\n  margin-left: auto;\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n  border-radius: 16rpx;\n  padding: 8rpx 16rpx;\n}\n\n.discount-value {\n  color: #FFFFFF;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n\n/* 商品描述 */\n.product-description {\n  margin-bottom: 16rpx;\n  padding: 16rpx;\n  background: rgba(0, 0, 0, 0.02);\n  border-radius: 16rpx;\n  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);\n}\n\n.description-text {\n  font-size: 26rpx;\n  color: #636366;\n  line-height: 1.5;\n}\n\n/* 商品标签 */\n.product-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n  margin-bottom: 16rpx;\n}\n\n.product-tag {\n  background: linear-gradient(135deg, rgba(88, 86, 214, 0.1), rgba(0, 122, 255, 0.1));\n  border-radius: 16rpx;\n  padding: 6rpx 16rpx;\n  border: 1rpx solid rgba(88, 86, 214, 0.2);\n}\n\n.tag-text {\n  font-size: 22rpx;\n  color: #5856D6;\n  font-weight: 500;\n}\n\n/* 交易方式 */\n.transaction-methods {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12rpx;\n}\n\n.transaction-method {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 8rpx 16rpx;\n  border-radius: 24rpx;\n}\n\n.method-icon {\n  margin-right: 6rpx;\n  color: #8e8e93;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.pickup-icon {\n  color: #34C759;\n}\n\n.delivery-icon {\n  color: #007AFF;\n}\n\n.local-icon {\n  color: #5856D6;\n}\n\n.face-icon {\n  color: #FF9500;\n}\n\n.negotiate-icon {\n  color: #FF3B30;\n}\n\n.method-text {\n  font-size: 22rpx;\n  color: #636366;\n}\n\n/* 车辆特殊信息 */\n.vehicle-info {\n  margin-top: 16rpx;\n  padding-top: 16rpx;\n  border-top: 1rpx solid rgba(60, 60, 67, 0.1);\n}\n\n.vehicle-specs {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n.vehicle-spec {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.02);\n  padding: 12rpx 20rpx;\n  border-radius: 16rpx;\n  flex: 1;\n  min-width: 120rpx;\n}\n\n.spec-label {\n  font-size: 20rpx;\n  color: #8e8e93;\n  margin-bottom: 6rpx;\n}\n\n.spec-value {\n  font-size: 26rpx;\n  color: #1c1c1e;\n  font-weight: 600;\n}\n\n.mileage-value {\n  color: #FF9500;\n}\n\n.year-value {\n  color: #5856D6;\n}\n\n.license-value {\n  color: #007AFF;\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/components/cards/SecondHandCard.vue'\nwx.createComponent(Component)"], "names": ["computed"], "mappings": ";;;;;;;;;;;;;AA4FA,MAAM,eAAe,MAAW;;;;;;;;;;AAEhC,UAAM,QAAQ;AAQd,UAAM,6BAA6BA,cAAQ,SAAC,MAAM;AAChD,UAAI,CAAC,MAAM,KAAK,sBAAsB,CAAC,MAAM,KAAK,mBAAmB;AAAQ,eAAO;AACpF,UAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAK;AAAQ,eAAO,MAAM,KAAK;AAGnE,YAAM,gBAAgB,MAAM,KAAK,KAAK,IAAI,SAAO,IAAI,YAAW,CAAE;AAGlE,aAAO,MAAM,KAAK,mBAAmB;AAAA,QAAO,YAC1C,CAAC,cAAc,SAAS,OAAO,YAAW,CAAE;AAAA,MAChD;AAAA,IACA,CAAC;AAED,aAAS,mBAAmB,QAAQ;AAClC,YAAM,YAAY;AAAA,QAChB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,MACX;AAEE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAEA,aAAS,kBAAkB,cAAc,eAAe;AACtD,UAAI,CAAC,gBAAgB,CAAC,iBAAiB,iBAAiB;AAAG,eAAO;AAClE,YAAM,YAAY,eAAe,gBAAgB,IAAI,QAAQ,CAAC;AAC9D,aAAO,WAAW,KAAK,WAAW;AAAA,IACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClIA,GAAG,gBAAgB,SAAS;"}