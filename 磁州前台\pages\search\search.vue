<template>
	<view class="search-page">
		<!-- 搜索框 -->
		<view class="search-header">
			<view class="search-box">
				<image src="/static/images/icons/search.png" class="search-icon"></image>
				<input type="text" class="search-input" v-model="keyword" placeholder="搜索招聘、寻找服务、求职等信息" 
					placeholder-class="search-placeholder" auto-focus @confirm="doSearch" confirm-type="search"/>
				<view class="clear-icon" v-if="keyword" @click="clearKeyword">
					<text class="clear-text">×</text>
				</view>
				<view class="mic-icon-container" @click="startVoiceRecognition">
					<image src="/static/images/icons/mic.png" class="mic-icon"></image>
				</view>
			</view>
			<view class="cancel-btn" @click="goBack">取消</view>
		</view>
		
		<!-- 搜索历史 -->
		<view class="search-history" v-if="!keyword && searchHistory.length > 0">
			<view class="history-header">
				<text class="history-title">搜索历史</text>
				<view class="clear-history" @click="clearHistory">
					<image src="/static/images/icons/delete.png" class="delete-icon"></image>
					<text class="clear-text">清除</text>
				</view>
			</view>
			<view class="history-list">
				<view class="history-item" v-for="(item, index) in searchHistory" :key="index" @click="useHistoryKeyword(item)">
					<image src="/static/images/icons/history.png" class="history-icon"></image>
					<text class="history-keyword">{{item}}</text>
				</view>
			</view>
		</view>
		
		<!-- 热门搜索 -->
		<view class="hot-search" v-if="!keyword">
			<view class="hot-header">
				<text class="hot-title">热门搜索</text>
			</view>
			<view class="hot-tags">
				<view class="hot-tag" v-for="(tag, index) in hotTags" :key="index" @click="useHistoryKeyword(tag)">
					{{tag}}
				</view>
			</view>
		</view>
		
		<!-- 搜索结果 -->
		<view class="search-results" v-if="keyword && hasSearched">
			<view class="result-stats">
				<text class="result-count">共找到 {{searchResults.length}} 条结果</text>
			</view>
			
			<!-- 结果列表 -->
			<view class="result-list" v-if="searchResults.length > 0">
				<view class="result-item" v-for="(item, index) in searchResults" :key="index" @click="navigateToDetail(item)">
					<view class="result-header">
						<text class="result-tag">{{item.category}}</text>
						<text class="result-time">{{item.time}}</text>
					</view>
					<text class="result-content">{{item.content}}</text>
					<view class="result-meta">
						<text class="result-views">浏览: {{item.views}}</text>
					</view>
				</view>
			</view>
			
			<!-- 无结果提示 -->
			<view class="no-result" v-else>
				<image src="/static/images/icons/no-result.png" class="no-result-icon"></image>
				<text class="no-result-text">没有找到相关信息</text>
				<text class="no-result-tip">换个关键词试试吧</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				keyword: '',
				searchHistory: [],
				hasSearched: false,
				searchResults: [],
				hotTags: ['招聘信息', '房屋出租', '二手闲置', '寻找服务', '到家服务', '生意转让']
			}
		},
		onLoad(options) {
			// 获取搜索历史
			this.loadSearchHistory();
			
			// 如果有传入的关键词，直接搜索
			if (options.keyword) {
				this.keyword = decodeURIComponent(options.keyword);
				this.doSearch();
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 清除关键词
			clearKeyword() {
				this.keyword = '';
				this.hasSearched = false;
			},
			
			// 使用历史关键词
			useHistoryKeyword(keyword) {
				this.keyword = keyword;
				this.doSearch();
			},
			
			// 执行搜索
			doSearch() {
				if (!this.keyword.trim()) {
					uni.showToast({
						title: '请输入搜索关键词',
						icon: 'none'
					});
					return;
				}
				
				// 显示加载中
				uni.showLoading({
					title: '搜索中...'
				});
				
				// 保存搜索历史
				this.saveSearchHistory(this.keyword);
				
				// 模拟搜索过程
				setTimeout(() => {
					// 模拟搜索结果
					this.searchResults = this.mockSearchResults(this.keyword);
					this.hasSearched = true;
					uni.hideLoading();
				}, 500);
			},
			
			// 加载搜索历史
			loadSearchHistory() {
				try {
					const history = uni.getStorageSync('searchHistory');
					if (history) {
						this.searchHistory = JSON.parse(history);
					}
				} catch (e) {
					console.error('加载搜索历史失败', e);
				}
			},
			
			// 保存搜索历史
			saveSearchHistory(keyword) {
				try {
					// 去重
					const index = this.searchHistory.indexOf(keyword);
					if (index !== -1) {
						this.searchHistory.splice(index, 1);
					}
					
					// 添加到最前面
					this.searchHistory.unshift(keyword);
					
					// 只保留最近10条
					if (this.searchHistory.length > 10) {
						this.searchHistory = this.searchHistory.slice(0, 10);
					}
					
					// 保存到本地
					uni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));
				} catch (e) {
					console.error('保存搜索历史失败', e);
				}
			},
			
			// 清除搜索历史
			clearHistory() {
				uni.showModal({
					title: '提示',
					content: '确定要清除搜索历史吗？',
					success: (res) => {
						if (res.confirm) {
							this.searchHistory = [];
							uni.removeStorageSync('searchHistory');
							uni.showToast({
								title: '已清除搜索历史',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 模拟搜索结果
			mockSearchResults(keyword) {
				// 模拟数据
				const allData = [
					{ id: 1, category: '到家服务', content: '专业家政保洁，小时工上门服务', time: '2024-05-01 09:30', views: 120 },
					{ id: 2, category: '寻找服务', content: '急寻钢筋工两名，价格从优', time: '2024-05-01 09:30', views: 185 },
					{ id: 12, category: '生意转让', content: '旺铺转让，位置优越，接手可盈利', time: '2024-02-20 16:00', views: 54 },
					{ id: 3, category: '招聘信息', content: '招聘：磁县超市收银员2名', time: '2024-02-20 10:30', views: 326 },
					{ id: 4, category: '求职信息', content: '会计专业毕业生求职财务相关工作', time: '2024-02-20 10:45', views: 89 },
					{ id: 5, category: '房屋出租', content: '磁县城区电梯房两室一厅90平米出租', time: '2024-02-20 11:20', views: 245 },
					{ id: 6, category: '房屋出售', content: '市中心三室两厅精装房出售', time: '2024-02-20 12:00', views: 178 },
					{ id: 7, category: '二手车辆', content: '转让二手大众朗逸，车况良好', time: '2024-02-20 13:00', views: 99 },
					{ id: 8, category: '宠物信息', content: '家养金毛幼犬低价转让', time: '2024-02-20 13:30', views: 66 },
					{ id: 9, category: '车辆服务', content: '专业汽车维修保养，免费检测', time: '2024-02-20 14:00', views: 88 },
					{ id: 10, category: '二手闲置', content: '9成新iPhone 13 Pro Max 256G 国行全套', time: '2024-02-20 13:15', views: 156 },
					{ id: 11, category: '磁州拼车', content: '磁县到邯郸顺风车，每天早上7点发车', time: '2024-02-20 15:00', views: 77 },
					{ id: 13, category: '教育培训', content: '小学数学一对一辅导，提分快', time: '2024-02-20 17:00', views: 61 },
					{ id: 14, category: '其他服务', content: '专业水电维修，快速上门', time: '2024-02-20 18:00', views: 42 }
				];
				
				// 根据关键词过滤
				return allData.filter(item => {
					return item.content.includes(keyword) || 
						   item.category.includes(keyword);
				});
			},
			
			// 跳转到详情页
			navigateToDetail(item) {
				uni.navigateTo({
					url: `/pages/publish/info-detail?id=${item.id}&content=${encodeURIComponent(item.content)}&category=${encodeURIComponent(item.category)}`
				});
			},
			
			// 开始语音识别
			startVoiceRecognition() {
				// 添加触感反馈
				uni.vibrateShort();
				
				// 检查平台是否支持语音识别
				if (uni.getSystemInfoSync().platform === 'android' || uni.getSystemInfoSync().platform === 'ios') {
					// 显示正在识别的提示
					uni.showToast({
						title: '正在聆听...',
						icon: 'none',
						duration: 60000
					});
					
					// 调用语音识别API
					// 注意：实际项目中需要使用真实的语音识别API
					// 这里使用setTimeout模拟语音识别过程
					setTimeout(() => {
						// 隐藏提示
						uni.hideToast();
						
						// 模拟识别结果
						const recognitionResult = '招聘信息';
						
						// 显示识别结果
						uni.showModal({
							title: '语音识别结果',
							content: `您是否要搜索"${recognitionResult}"？`,
							confirmText: '搜索',
							success: (res) => {
								if (res.confirm) {
									this.keyword = recognitionResult;
									this.doSearch();
								}
							}
						});
					}, 2000); // 模拟2秒的识别时间
				} else {
					// 不支持语音识别的平台
					uni.showToast({
						title: '当前平台不支持语音识别',
						icon: 'none',
						duration: 2000
					});
				}
			}
		}
	}
</script>

<style>
	.search-page {
		min-height: 100vh;
		background-color: #f8f9fc;
		padding-bottom: 30rpx;
	}
	
	/* 搜索框 */
	.search-header {
		padding: 20rpx 30rpx;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		position: sticky;
		top: 0;
		z-index: 10;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.search-box {
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #f7f9fc;
		border-radius: 40rpx;
		padding: 0 20rpx 0 30rpx;
		height: 80rpx;
		border: 1rpx solid #eaedf2;
	}
	
	.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 16rpx;
		opacity: 0.4;
	}
	
	.search-input {
		flex: 1;
		height: 80rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.search-placeholder {
		color: #b8bdcc;
		font-size: 28rpx;
	}
	
	.clear-icon {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #e0e0e0;
		border-radius: 20rpx;
		margin-right: 10rpx;
	}
	
	.clear-text {
		color: #ffffff;
		font-size: 28rpx;
		line-height: 28rpx;
	}
	
	.mic-icon-container {
		width: 60rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background-color: #0964e3;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.mic-icon {
		width: 36rpx;
		height: 36rpx;
		opacity: 1;
		filter: brightness(0) invert(1);
	}
	
	.cancel-btn {
		padding: 0 20rpx;
		font-size: 30rpx;
		color: #333;
		height: 80rpx;
		line-height: 80rpx;
	}
	
	/* 搜索历史 */
	.search-history {
		margin: 30rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 20rpx 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
	}
	
	.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.history-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}
	
	.clear-history {
		display: flex;
		align-items: center;
	}
	
	.delete-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
		opacity: 0.6;
	}
	
	.clear-text {
		font-size: 26rpx;
		color: #999;
	}
	
	.history-list {
		display: flex;
		flex-wrap: wrap;
	}
	
	.history-item {
		display: flex;
		align-items: center;
		background-color: #f5f7fa;
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.history-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
		opacity: 0.5;
	}
	
	.history-keyword {
		font-size: 26rpx;
		color: #666;
	}
	
	/* 热门搜索 */
	.hot-search {
		margin: 30rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 20rpx 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
	}
	
	.hot-header {
		margin-bottom: 20rpx;
	}
	
	.hot-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}
	
	.hot-tags {
		display: flex;
		flex-wrap: wrap;
	}
	
	.hot-tag {
		background-color: #f5f7fa;
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		font-size: 26rpx;
		color: #666;
	}
	
	/* 搜索结果 */
	.search-results {
		padding: 0 30rpx;
	}
	
	.result-stats {
		margin-bottom: 20rpx;
	}
	
	.result-count {
		font-size: 26rpx;
		color: #999;
	}
	
	.result-list {
		margin-bottom: 30rpx;
	}
	
	.result-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
	}
	
	.result-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.result-tag {
		font-size: 22rpx;
		color: #1677ff;
		background-color: rgba(230, 240, 255, 0.7);
		padding: 4rpx 16rpx;
		border-radius: 6rpx;
	}
	
	.result-time {
		font-size: 22rpx;
		color: #999;
	}
	
	.result-content {
		font-size: 26rpx;
		color: #333;
		margin-bottom: 10rpx;
		line-height: 1.5;
	}
	
	.result-meta {
		font-size: 22rpx;
		color: #999;
		text-align: right;
	}
	
	/* 无结果提示 */
	.no-result {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 100rpx 0;
	}
	
	.no-result-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		opacity: 0.5;
	}
	
	.no-result-text {
		font-size: 32rpx;
		color: #999;
		margin-bottom: 10rpx;
	}
	
	.no-result-tip {
		font-size: 26rpx;
		color: #b8bdcc;
	}
</style> 