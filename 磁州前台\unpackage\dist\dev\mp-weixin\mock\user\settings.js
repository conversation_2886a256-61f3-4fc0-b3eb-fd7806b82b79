"use strict";
const userSettings = {
  notifications: {
    pushEnabled: true,
    commentNotify: true,
    likeNotify: true,
    followNotify: true,
    systemNotify: true,
    activityNotify: true,
    messageNotify: true
  },
  privacy: {
    profileVisible: "public",
    // public, friends, private
    phoneVisible: "friends",
    // public, friends, private
    locationVisible: "friends",
    // public, friends, private
    allowStrangerMessage: true
  },
  theme: {
    darkMode: false,
    fontScale: 1,
    colorScheme: "blue"
    // blue, green, orange, purple
  },
  language: "zh-CN",
  autoPlay: true,
  dataUsage: {
    saveData: false,
    autoDownload: true
  }
};
const fetchUserSettings = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(userSettings);
    }, 300);
  });
};
const updateUserSettings = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const updatedSettings = {
        ...userSettings,
        ...data
      };
      resolve({
        success: true,
        data: updatedSettings
      });
    }, 500);
  });
};
exports.fetchUserSettings = fetchUserSettings;
exports.updateUserSettings = updateUserSettings;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/mock/user/settings.js.map
