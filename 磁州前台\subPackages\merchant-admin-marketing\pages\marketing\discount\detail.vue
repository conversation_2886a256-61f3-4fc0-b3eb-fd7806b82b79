<!-- 满减活动详情页面 (detail.vue) -->
<template>
  <view class="discount-detail-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @tap="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">满减活动详情</text>
      <view class="navbar-right">
        <view class="more-icon" @tap="showMoreOptions">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="1"></circle>
            <circle cx="19" cy="12" r="1"></circle>
            <circle cx="5" cy="12" r="1"></circle>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 活动卡片 -->
    <view class="discount-card">
      <view class="discount-header">
        <text class="discount-title">{{discountData.title}}</text>
        <view class="discount-status" :class="'status-'+discountData.status">{{discountData.statusText}}</view>
      </view>
      
      <view class="discount-rules">
        <view class="rule-item" v-for="(rule, index) in discountData.rules" :key="index">
          <view class="rule-content">
            <text class="rule-text">满 {{rule.minAmount}} 元减 {{rule.discountAmount}} 元</text>
          </view>
        </view>
      </view>
      
      <view class="validity-period">
        <text class="validity-label">活动时间:</text>
        <text class="validity-value">{{discountData.timeRange}}</text>
      </view>
      
      <view class="discount-qrcode" v-if="discountData.status === 'active'">
        <image class="qrcode-image" :src="discountData.qrCodeUrl" mode="aspectFit"></image>
        <text class="qrcode-hint">扫描二维码分享活动</text>
      </view>
    </view>
    
    <!-- 使用情况 -->
    <view class="usage-section">
      <view class="section-header">
        <text class="section-title">使用情况</text>
      </view>
      
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-value">{{discountData.totalOrders}}</text>
          <text class="stats-label">参与订单</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{discountData.totalUsers}}</text>
          <text class="stats-label">参与用户</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">¥{{discountData.totalDiscount}}</text>
          <text class="stats-label">优惠金额</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{discountData.conversionRate}}%</text>
          <text class="stats-label">转化率</text>
        </view>
      </view>
      
      <view class="chart-container">
        <text class="chart-title">优惠金额趋势</text>
        <view class="chart-placeholder">
          <text class="placeholder-text">图表加载中...</text>
        </view>
      </view>
    </view>
    
    <!-- 活动设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">活动设置</text>
      </view>
      
      <view class="settings-list">
        <view class="settings-item">
          <text class="item-label">活动类型</text>
          <text class="item-value">{{discountData.typeText}}</text>
        </view>
        <view class="settings-item">
          <text class="item-label">适用商品</text>
          <text class="item-value">{{discountData.applicableProducts}}</text>
        </view>
        <view class="settings-item">
          <text class="item-label">叠加使用</text>
          <text class="item-value">{{discountData.canStack ? '允许' : '不允许'}}</text>
        </view>
        <view class="settings-item">
          <text class="item-label">每人限用</text>
          <text class="item-value">{{discountData.perPersonLimit}}次</text>
        </view>
        <view class="settings-item">
          <text class="item-label">活动说明</text>
          <text class="item-value">{{discountData.instructions}}</text>
        </view>
      </view>
    </view>
    
    <!-- 订单记录 -->
    <view class="records-section">
      <view class="section-header">
        <text class="section-title">订单记录</text>
        <text class="view-all" @tap="viewAllRecords">查看全部</text>
      </view>
      
      <view class="records-list">
        <view class="record-item" v-for="(record, index) in discountData.recentRecords" :key="index">
          <view class="record-info">
            <text class="order-number">订单号: {{record.orderNumber}}</text>
            <text class="record-time">{{record.time}}</text>
          </view>
          <view class="record-details">
            <text class="user-name">{{record.userName}}</text>
            <text class="discount-amount">-¥{{record.discountAmount}}</text>
          </view>
        </view>
      </view>
      
      <view class="empty-records" v-if="discountData.recentRecords.length === 0">
        <text class="empty-text">暂无订单记录</text>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-button edit" @tap="editDiscount">
        <view class="button-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
          </svg>
        </view>
        <text class="button-text">编辑</text>
      </view>
      
      <view 
        class="action-button" 
        :class="discountData.status === 'active' ? 'pause' : 'activate'"
        @tap="toggleDiscountStatus">
        <view class="button-icon">
          <svg v-if="discountData.status === 'active'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="6" y="4" width="4" height="16"></rect>
            <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
        </view>
        <text class="button-text">{{discountData.status === 'active' ? '暂停' : '启用'}}</text>
      </view>
      
      <view class="action-button share" @tap="shareDiscount">
        <view class="button-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
          </svg>
        </view>
        <text class="button-text">分享</text>
      </view>
      
      <view class="action-button delete" @tap="confirmDeleteDiscount">
        <view class="button-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="3 6 5 6 21 6"></polyline>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
        </view>
        <text class="button-text">删除</text>
      </view>
    </view>
    
    <!-- 分享弹窗 -->
    <view class="share-popup" v-if="showSharePopup">
      <view class="popup-mask" @tap="hideSharePopup"></view>
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">分享活动</text>
          <view class="popup-close" @tap="hideSharePopup">×</view>
        </view>
        
        <view class="share-options">
          <view class="share-option" @tap="shareToChannel('wechat')">
            <view class="option-icon wechat">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9.5 9.5c-.3-2.1 1.6-4 4.8-4.3 3.1-.3 5.5 1.2 5.9 3.5.4 2.3-1.5 4.8-4.5 5.2-1 .1-1.9 0-2.7-.4l-2.4 1.2.5-2.2c-1.1-.8-1.8-1.9-1.6-3z"></path>
                <path d="M4 14.5c-.5-2.5 2.3-5 6.2-5.5 3.9-.5 7.5 1 8 3.5.5 2.5-1.8 5-5.7 5.5-1.2.2-2.3 0-3.4-.4l-3 1.5.6-2.7c-1.4-1-2.4-2.3-2.7-3.9z"></path>
              </svg>
            </view>
            <text class="option-name">微信</text>
          </view>
          
          <view class="share-option" @tap="shareToChannel('moments')">
            <view class="option-icon moments">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 8L12 16"></path>
                <path d="M8 12L16 12"></path>
              </svg>
            </view>
            <text class="option-name">朋友圈</text>
          </view>
          
          <view class="share-option" @tap="shareToChannel('link')">
            <view class="option-icon link">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
              </svg>
            </view>
            <text class="option-name">复制链接</text>
          </view>
          
          <view class="share-option" @tap="shareToChannel('qrcode')">
            <view class="option-icon qrcode">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
              </svg>
            </view>
            <text class="option-name">二维码</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';

export default {
  setup() {
    // 响应式状态
    const discountId = ref('');
    const discountData = reactive({
      id: 1,
      title: '春季促销活动',
      status: 'active',
      statusText: '进行中',
      rules: [
        { minAmount: 100, discountAmount: 10 },
        { minAmount: 200, discountAmount: 25 },
        { minAmount: 300, discountAmount: 50 }
      ],
      timeRange: '2023-04-01 ~ 2023-04-30',
      qrCodeUrl: '/static/images/discount-qrcode-placeholder.png',
      totalOrders: 352,
      totalUsers: 280,
      totalDiscount: '8,562.50',
      conversionRate: 65.3,
      typeText: '满减活动',
      applicableProducts: '全部商品',
      canStack: false,
      perPersonLimit: 3,
      instructions: '活动期间，每位用户最多可使用3次满减优惠，不可与其他优惠同时使用',
      recentRecords: [
        {
          id: 1,
          orderNumber: 'DD20230420001',
          userName: '张三',
          time: '2023-04-20 14:30',
          discountAmount: '25.00'
        },
        {
          id: 2,
          orderNumber: 'DD20230420002',
          userName: '李四',
          time: '2023-04-20 15:45',
          discountAmount: '10.00'
        },
        {
          id: 3,
          orderNumber: 'DD20230421003',
          userName: '王五',
          time: '2023-04-21 09:20',
          discountAmount: '50.00'
        }
      ]
    });
    
    const showSharePopup = ref(false);
    
    // 方法
    function goBack() {
      uni.navigateBack();
    }
    
    function showMoreOptions() {
      uni.showActionSheet({
        itemList: ['复制活动信息', '导出数据', '设为模板'],
        success: (res) => {
          // 处理更多选项
          switch(res.tapIndex) {
            case 0:
              // 复制活动信息
              uni.setClipboardData({
                data: `${discountData.title}：${discountData.rules.map(rule => `满${rule.minAmount}减${rule.discountAmount}`).join('，')}，活动时间：${discountData.timeRange}`,
                success: () => {
                  uni.showToast({
                    title: '已复制活动信息',
                    icon: 'success'
                  });
                }
              });
              break;
            case 1:
              // 导出数据
              uni.showToast({
                title: '正在导出数据...',
                icon: 'loading'
              });
              setTimeout(() => {
                uni.showToast({
                  title: '导出成功',
                  icon: 'success'
                });
              }, 1500);
              break;
            case 2:
              // 设为模板
              uni.showToast({
                title: '已设为模板',
                icon: 'success'
              });
              break;
          }
        }
      });
    }
    
    function toggleDiscountStatus() {
      const isActive = discountData.status === 'active';
      const newStatus = isActive ? 'paused' : 'active';
      const statusText = isActive ? '已暂停' : '进行中';
      
      // 在实际应用中，这里应该调用API更新状态
      
      // 本地状态更新示例
      discountData.status = newStatus;
      discountData.statusText = statusText;
      
      uni.showToast({
        title: isActive ? '已暂停活动' : '已启用活动',
        icon: 'success'
      });
    }
    
    function confirmDeleteDiscount() {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除"${discountData.title}"吗？此操作无法撤销。`,
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            // 在实际应用中，这里应该调用API删除
            uni.showToast({
              title: '已删除活动',
              icon: 'success'
            });
            
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        }
      });
    }
    
    function editDiscount() {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/edit?id=${discountData.id}`,
        animationType: 'slide-in-right'
      });
    }
    
    function viewAllRecords() {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/records?id=${discountData.id}`,
        animationType: 'slide-in-right'
      });
    }
    
    function shareDiscount() {
      showSharePopup.value = true;
    }
    
    function hideSharePopup() {
      showSharePopup.value = false;
    }
    
    function shareToChannel(channel) {
      // 在实际应用中，这里应该实现不同渠道的分享逻辑
      
      uni.showToast({
        title: `已分享到${channel === 'wechat' ? '微信' : channel === 'moments' ? '朋友圈' : channel === 'link' ? '链接已复制' : '二维码已保存'}`,
        icon: 'success'
      });
      
      hideSharePopup();
    }
    
    function loadDiscountData(id) {
      // 在实际应用中，这里应该调用API获取满减活动详情
      discountId.value = id;
      
      // 模拟加载数据
      uni.showLoading({
        title: '加载中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
      }, 500);
    }
    
    onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.$page?.options || {};
      
      if (options.id) {
        loadDiscountData(options.id);
      }
    });
    
    return {
      discountData,
      showSharePopup,
      goBack,
      showMoreOptions,
      toggleDiscountStatus,
      confirmDeleteDiscount,
      editDiscount,
      viewAllRecords,
      shareDiscount,
      hideSharePopup,
      shareToChannel
    };
  }
}
</script>

<style lang="scss">
.discount-detail-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px; /* 为底部操作栏留出空间 */
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #333;
  border-bottom: 2px solid #333;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-icon {
  width: 24px;
  height: 24px;
  color: #333;
}

/* 活动卡片样式 */
.discount-card {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.discount-card::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 4px;
  background: linear-gradient(90deg, #FDEB71, #F8D800);
}

.discount-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.discount-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.discount-status {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
}

.status-active {
  background: #34C759;
}

.status-expired {
  background: #8E8E93;
}

.status-upcoming {
  background: #FF9500;
}

.status-paused {
  background: #FF9500;
}

.discount-rules {
  margin-bottom: 15px;
}

.rule-item {
  margin-bottom: 8px;
}

.rule-content {
  background: rgba(248, 216, 0, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  display: inline-block;
}

.rule-text {
  font-size: 15px;
  font-weight: 500;
  color: #D4B100;
}

.validity-period {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.validity-label {
  font-size: 14px;
  color: #999;
  margin-right: 5px;
}

.validity-value {
  font-size: 14px;
  color: #333;
}

.discount-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #eee;
}

.qrcode-image {
  width: 120px;
  height: 120px;
  margin-bottom: 10px;
}

.qrcode-hint {
  font-size: 12px;
  color: #999;
}

/* 使用情况样式 */
.usage-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stats-grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.stats-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-value {
  font-size: 18px;
  font-weight: bold;
  color: #F8D800;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 12px;
  color: #999;
}

.chart-container {
  padding: 15px 0;
  border-top: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}

.chart-placeholder {
  height: 150px;
  background: #f9f9f9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 14px;
  color: #999;
}

/* 活动设置样式 */
.settings-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.settings-list {
  
}

.settings-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 14px;
  color: #666;
}

.item-value {
  font-size: 14px;
  color: #333;
  max-width: 60%;
  text-align: right;
}

/* 订单记录样式 */
.records-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.view-all {
  font-size: 14px;
  color: #F8D800;
}

.records-list {
  
}

.record-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.order-number {
  font-size: 14px;
  color: #333;
}

.record-time {
  font-size: 12px;
  color: #999;
}

.record-details {
  display: flex;
  justify-content: space-between;
}

.user-name {
  font-size: 14px;
  color: #666;
}

.discount-amount {
  font-size: 14px;
  color: #F8D800;
  font-weight: 500;
}

.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 底部操作栏样式 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  padding: 10px 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}

.button-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.button-text {
  font-size: 12px;
}

.action-button.edit {
  color: #007AFF;
}

.action-button.pause {
  color: #FF9500;
}

.action-button.activate {
  color: #34C759;
}

.action-button.share {
  color: #5856D6;
}

.action-button.delete {
  color: #FF3B30;
}

/* 分享弹窗样式 */
.share-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 15px;
  transform: translateY(0);
  transition: transform 0.3s;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.popup-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}

.share-options {
  display: flex;
  justify-content: space-around;
  padding: 10px 0 20px;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.option-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  color: #fff;
}

.option-icon.wechat {
  background: #07C160;
}

.option-icon.moments {
  background: #07C160;
}

.option-icon.link {
  background: #007AFF;
}

.option-icon.qrcode {
  background: #FF9500;
}

.option-name {
  font-size: 12px;
  color: #333;
}
</style>