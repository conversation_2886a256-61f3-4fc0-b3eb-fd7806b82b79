<template>
	<view class="join-page">
		<!-- 高斯模糊背景 -->
		<view class="blur-bg">
			<image src="/static/images/banner/banner-3.jpg" mode="aspectFill" class="bg-image"></image>
		</view>
		
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="navbar-left" @click="goBack">
				<image src="/static/images/tabbar/返回.png" class="back-icon-img"></image>
			</view>
			<text class="navbar-title">商家入驻</text>
			<view class="navbar-right"></view>
		</view>
		
		<!-- 内容区 -->
		<view class="content">
			<!-- 入驻说明卡片 -->
			<view class="intro-card glass-card">
				<view class="intro-header">
					<view class="intro-icon-wrap">
						<image src="/static/images/tabbar/入驻卡片.png" class="intro-icon"></image>
					</view>
					<view class="intro-titles">
						<text class="intro-title">商家免费入驻</text>
						<view class="subtitle-wrapper">
							<text class="intro-subtitle">获取更多商业机会和客户资源</text>
						</view>
					</view>
				</view>
				<view class="intro-stats">
					<view class="stat-item">
						<text class="stat-num">123.4万</text>
						<text class="stat-label">月曝光量</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-num">567</text>
						<text class="stat-label">已入驻商家</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-num">89%</text>
						<text class="stat-label">转化率</text>
					</view>
				</view>
			</view>
			
			<!-- 表单区域 -->
			<view class="form-section glass-card">
				<text class="form-title">基本信息</text>
				
				<view class="form-item">
					<text class="form-label">店铺名称</text>
					<input type="text" class="form-input" placeholder="请输入商家名称" v-model="formData.shopName" />
				</view>
				
				<view class="form-item">
					<text class="form-label">详细地址</text>
					<view class="address-wrap">
						<input type="text" class="form-input address-input" placeholder="请输入店铺地址" v-model="formData.address" />
						<view class="location-btn" @click.stop="getLocation">
							<image src="/static/images/tabbar/定位.png" class="location-icon-img"></image>
						</view>
					</view>
					<view class="location-tips" v-if="formData.address">
						<text class="location-text">磁县正义路</text>
						<text class="location-btn-text" @click="getLocation">定位</text>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">所属行业</text>
					<view class="form-select" @click="showCategoryPickerFn">
						<text :class="{'placeholder': !formData.category}">{{formData.category || '点击选择所属行业'}}</text>
						<image src="/static/images/tabbar/箭头.png" class="select-arrow-img"></image>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">商家规模</text>
					<view class="form-select" @click="showScalePickerFn">
						<text :class="{'placeholder': !formData.scale}">{{formData.scale || '点击选择商家规模人数'}}</text>
						<image src="/static/images/tabbar/箭头.png" class="select-arrow-img"></image>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">营业时间</text>
					<view class="form-select" @click="showTimePickerFn">
						<text :class="{'placeholder': !formData.businessTime}">{{formData.businessTime || '点击选择营业时间'}}</text>
						<image src="/static/images/tabbar/箭头.png" class="select-arrow-img"></image>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">联系电话</text>
					<input type="number" class="form-input" placeholder="请输入联系电话" v-model="formData.contactPhone" />
				</view>
				
				<view class="form-item">
					<text class="form-label">商家介绍</text>
					<textarea class="form-textarea" placeholder="请填写商家介绍内容" v-model="formData.description"></textarea>
				</view>
			</view>
			
			<!-- 图片上传区域 -->
			<view class="form-section glass-card">
				<text class="form-title">商家图片</text>
				<view class="upload-box" @click="uploadImage('shopImage')">
					<view class="upload-placeholder">
						<text class="plus-icon">+</text>
					</view>
				</view>
			</view>
			
			<view class="form-section glass-card">
				<text class="form-title">商家logo</text>
				<view class="upload-box" @click="uploadImage('logo')">
					<view class="upload-placeholder">
						<text class="plus-icon">+</text>
					</view>
					<view class="upload-desc-container">
						<text class="upload-desc">请上传商家logo</text>
						<text class="upload-desc">或门头照片</text>
					</view>
				</view>
			</view>
			
			<view class="form-section glass-card">
				<text class="form-title">客服微信二维码</text>
				<view class="upload-box" @click="uploadImage('qrcode')">
					<view class="upload-placeholder">
						<text class="plus-icon">+</text>
					</view>
					<view class="upload-desc-container">
						<text class="upload-desc">请上传客服</text>
						<text class="upload-desc">微信二维码</text>
					</view>
				</view>
			</view>
			
			<view class="form-section glass-card">
				<text class="form-title">商家相册</text>
				<view class="upload-box" @click="uploadImage('album')">
					<view class="upload-placeholder">
						<text class="plus-icon">+</text>
					</view>
					<text class="upload-desc">最多上传10张照片</text>
				</view>
			</view>
			
			<!-- 商家入驻推广操作 -->
			<view class="form-section glass-card">
				<text class="form-title">商家入驻</text>
				<ConfigurablePremiumActions
					pageType="merchant_join"
					showMode="selection"
					:itemData="merchantJoinData"
					@action-completed="handleJoinCompleted"
					@action-cancelled="handleJoinCancelled"
				/>
			</view>
			
			<!-- 付费入驻版本弹窗 -->
			<view class="popup-mask" v-if="showVersionModal" @click="hideVersionModal"></view>
			<view class="popup-content version-popup" v-if="showVersionModal">
				<view class="popup-header">
					<text class="popup-title">选择付费入驻版本</text>
					<view class="popup-close" @click="hideVersionModal">
						<image src="/static/images/tabbar/关闭.png" class="close-icon-img"></image>
					</view>
				</view>
				
				<scroll-view class="version-list" scroll-y>
					<!-- 基础版 -->
					<view class="version-card apple-style" :class="{'active': formData.version === 'basic'}" @click="selectVersion('basic')">
						<view class="version-card-bg basic-gradient">
							<view class="version-pill">基础版</view>
							<view class="version-content">
								<view class="version-price-area">
									<text class="version-price-label">¥</text>
									<text class="version-price-value">49.9</text>
									<text class="version-price-cycle">/年</text>
								</view>
								<view class="version-desc">适合个体商户</view>
								
								<view class="version-features">
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">商品发布数量：最多20个</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">店铺展示位置：普通位置</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">客户数据分析：基础版</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">免费获赠一次店铺推广</text>
									</view>
								</view>
								
								<view class="version-select-btn" :class="{'selected': formData.version === 'basic'}">
									<text>{{ formData.version === 'basic' ? '已选择' : '选择此版本' }}</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 高级版 -->
					<view class="version-card apple-style" :class="{'active': formData.version === 'premium'}" @click="selectVersion('premium')">
						<view class="version-card-bg premium-gradient">
							<view class="version-badge">
								<text class="badge-text">热门</text>
							</view>
							<view class="version-pill">高级版</view>
							<view class="version-content">
								<view class="version-price-area">
									<text class="version-price-label">¥</text>
									<text class="version-price-value">149.9</text>
									<text class="version-price-cycle">/年</text>
								</view>
								<view class="version-desc">性价比最高</view>
								
								<view class="version-features">
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">商品发布数量：最多50个</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">店铺展示位置：优先位置</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">客户数据分析：专业版</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">赠送3次店铺推广和置顶</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">商品视频展示功能</text>
									</view>
								</view>
								
								<view class="version-select-btn premium-select" :class="{'selected': formData.version === 'premium'}">
									<text>{{ formData.version === 'premium' ? '已选择' : '选择此版本' }}</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 尊贵版 -->
					<view class="version-card apple-style" :class="{'active': formData.version === 'deluxe'}" @click="selectVersion('deluxe')">
						<view class="version-card-bg deluxe-gradient">
							<view class="version-pill">尊贵版</view>
							<view class="version-content">
								<view class="version-price-area">
									<text class="version-price-label">¥</text>
									<text class="version-price-value">299.9</text>
									<text class="version-price-cycle">/年</text>
								</view>
								<view class="version-desc">全功能无限制</view>
								
								<view class="version-features">
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">商品发布数量：无限制</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">店铺展示位置：最佳位置</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">客户数据分析：高级版</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">免费获赠整年店铺推广</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">优先客服一对一服务</text>
									</view>
									<view class="version-feature-item">
										<view class="feature-dot"></view>
										<text class="feature-text">专属VIP店铺标识</text>
									</view>
								</view>
								
								<view class="version-select-btn deluxe-select" :class="{'selected': formData.version === 'deluxe'}">
									<text>{{ formData.version === 'deluxe' ? '已选择' : '选择此版本' }}</text>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
				
				<view class="version-popup-footer">
					<button class="confirm-version-btn" @click="confirmVersion">确认选择</button>
				</view>
			</view>
			
			<!-- 协议同意 -->
			<view class="agreement-section">
				<view class="agreement-checkbox" @click="toggleAgreement">
					<view class="checkbox" :class="{'checked': formData.agreed}"></view>
				</view>
				<view class="agreement-text">我已阅读同意<text class="agreement-link">《服务协议》</text>和<text class="agreement-link">《隐私政策》</text></view>
			</view>
			
			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" @click="submitForm()">确认入驻</button>
			</view>
		</view>
		
		<!-- 分类选择弹出层 -->
		<view class="popup-mask" v-if="showCategoryPicker || showScalePicker || showTimePicker" @click="hideAllModals"></view>
		
		<!-- 分类选择 -->
		<view class="popup-content" v-if="showCategoryPicker">
			<view class="popup-header">
				<text class="popup-title">选择经营类目</text>
				<view class="popup-close" @click="hideCategoryPicker">
					<image src="/static/images/tabbar/关闭.png" class="close-icon-img"></image>
				</view>
			</view>
			<scroll-view class="category-list" scroll-y>
				<view 
					class="category-item" 
					v-for="(item, index) in categories" 
					:key="index"
					@click="selectCategory(item)"
					:class="{'active': formData.category === item}"
				>
					<text>{{item}}</text>
					<image src="/static/images/tabbar/选中.png" class="check-icon-img" v-if="formData.category === item"></image>
				</view>
			</scroll-view>
		</view>
		
		<!-- 规模选择 -->
		<view class="popup-content" v-if="showScalePicker">
			<view class="popup-header">
				<text class="popup-title">选择商家规模</text>
				<view class="popup-close" @click="hideScalePicker">
					<image src="/static/images/tabbar/关闭.png" class="close-icon-img"></image>
				</view>
			</view>
			<scroll-view class="category-list" scroll-y>
				<view 
					class="category-item" 
					v-for="(item, index) in scales" 
					:key="index"
					@click="selectScale(item)"
					:class="{'active': formData.scale === item}"
				>
					<text>{{item}}</text>
					<image src="/static/images/tabbar/选中.png" class="check-icon-img" v-if="formData.scale === item"></image>
				</view>
			</scroll-view>
		</view>
		
		<!-- 时间选择 -->
		<view class="popup-content" v-if="showTimePicker">
			<view class="popup-header">
				<text class="popup-title">选择营业时间</text>
				<view class="popup-close" @click="hideTimePicker">
					<image src="/static/images/tabbar/关闭.png" class="close-icon-img"></image>
				</view>
			</view>
			<scroll-view class="category-list" scroll-y>
				<view 
					class="category-item" 
					v-for="(item, index) in businessTimes" 
					:key="index"
					@click="selectTime(item)"
					:class="{'active': formData.businessTime === item}"
				>
					<text>{{item}}</text>
					<image src="/static/images/tabbar/选中.png" class="check-icon-img" v-if="formData.businessTime === item"></image>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import enhancedRewardedAdService from '@/services/enhancedRewardedAdService.js';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

// --- 响应式状态 ---
const statusBarHeight = ref(20);
const showCategoryPicker = ref(false);
const showScalePicker = ref(false);
const showTimePicker = ref(false);
const showVersionModal = ref(false);

const formData = reactive({
					shopName: '',
					address: '',
  category: '',
					scale: '',
					businessTime: '',
  contactPhone: '',
  description: '',
  shopImage: null,
  logo: null,
  qrcode: null,
  album: [],
					joinMethod: 'free',
  version: 'premium',
					agreed: false
});

// 广告统计数据
const adStats = ref({
	join: {
		remaining: 1,
		limit: 1,
		lastWatchTime: null
	}
});

const categories = reactive(['美食小吃', '休闲娱乐', '装修家居', '母婴专区', '房产楼盘', '到家服务', '车辆服务', '教育培训', '其他行业']);
const scales = reactive(['1-10人', '11-50人', '51-100人', '100-200人', '200人以上']);
const businessTimes = reactive(['08:00-18:00', '09:00-21:00', '24小时营业', '自定义']);
const versions = reactive([
  { 
    key: 'basic', 
    name: '基础版', 
    price: '49.9', 
    desc: '适合个体商户',
    features: [
        '商品发布数量：最多20个',
        '店铺展示位置：普通位置',
        '客户数据分析：基础版',
        '免费获赠一次店铺推广'
    ] 
  },
  { 
    key: 'premium', 
    name: '高级版', 
    price: '149.9', 
    desc: '性价比最高',
    features: [
        '商品发布数量：最多50个',
        '店铺展示位置：优先位置',
        '客户数据分析：专业版',
        '赠送3次店铺推广和置顶',
        '商品视频展示功能'
    ] 
  },
  { 
    key: 'deluxe', 
    name: '尊贵版', 
    price: '299.9', 
    desc: '全功能无限制',
    features: [
        '商品发布数量：无限制',
        '店铺展示位置：最佳位置',
        '客户数据分析：高级版',
        '免费获赠整年店铺推广',
        '优先客服一对一服务',
        '专属VIP店铺标识'
    ] 
  }
]);

// --- 计算属性 ---
const versionName = computed(() => versions.find(v => v.key === formData.version)?.name || '');
const versionPrice = computed(() => versions.find(v => v.key === formData.version)?.price || 0);

const getPlanBadgeClass = computed(() => {
	switch(formData.version) {
		case 'premium': return 'premium-badge';
		case 'deluxe': return 'deluxe-badge';
		default: return 'basic-badge';
	}
});
const getVersionMainFeature = computed(() => versions.find(v => v.key === formData.version)?.features[0] || '');
const getVersionSecondFeature = computed(() => versions.find(v => v.key === formData.version)?.features[1] || '');

// 商家入驻数据
const merchantJoinData = reactive({
	id: 'merchant_join',
	title: '商家入驻',
	description: '加入我们的商家平台，获得更多商业机会'
});

// --- 方法 ---
const goBack = () => uni.navigateBack();

const getLocation = () => {
	uni.chooseLocation({
		success: (res) => {
			formData.address = res.address + (res.name || '');
		}
	});
};

const hideAllModals = () => {
    showCategoryPicker.value = false;
    showScalePicker.value = false;
    showTimePicker.value = false;
};

// 分类选择器
const showCategoryPickerFn = () => { showCategoryPicker.value = true; };
const hideCategoryPicker = () => { showCategoryPicker.value = false; };
const selectCategory = (item) => {
    formData.category = item;
    hideCategoryPicker();
};

// 规模选择器
const showScalePickerFn = () => { showScalePicker.value = true; };
const hideScalePicker = () => { showScalePicker.value = false; };
const selectScale = (item) => {
    formData.scale = item;
    hideScalePicker();
};

// 时间选择器
const showTimePickerFn = () => { showTimePicker.value = true; };
const hideTimePicker = () => { showTimePicker.value = false; };
const selectTime = (item) => {
    formData.businessTime = item;
    hideTimePicker();
};

const uploadImage = (type) => {
				uni.chooseImage({
		count: type === 'album' ? (10 - formData.album.length) : 1,
        sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
			if(type === 'album') {
				formData.album.push(...res.tempFilePaths);
							} else {
				formData[type] = res.tempFilePaths[0];
			}
		}
	});
};

const selectJoinMethod = (method) => {
	formData.joinMethod = method;
};



const selectVersion = (versionKey) => {
	formData.version = versionKey;
};

const confirmVersion = () => {
    showVersionModal.value = false;
};

const hideVersionModal = () => {
    showVersionModal.value = false;
}

const toggleAgreement = () => {
    formData.agreed = !formData.agreed;
};

const validateForm = (isFree) => {
    if(!formData.shopName) { uni.showToast({ title: '请输入店铺名称', icon: 'none' }); return false; }
	if(!formData.address) { uni.showToast({ title: '请输入店铺地址', icon: 'none' }); return false; }
	if(!formData.category) { uni.showToast({ title: '请选择所属行业', icon: 'none' }); return false; }
	if(!formData.contactPhone) { uni.showToast({ title: '请输入联系电话', icon: 'none' }); return false; }
    if(!formData.agreed) { uni.showToast({ title: '请阅读并同意服务协议', icon: 'none' }); return false; }

    if (!isFree && formData.joinMethod === 'paid' && !formData.version) {
        uni.showToast({ title: '请选择一个付费版本', icon: 'none' });
        return false;
    }
    return true;
};

// 广告成功回调
const handleAdSuccess = (result) => {
	console.log('广告观看成功:', result);
	if (result.type === 'join') {
		uni.showToast({
			title: '观看成功！获得1个月免费特权',
			icon: 'success',
			duration: 2500
		});
		// 提交表单
		submitForm(true);
	}
};

// 广告失败回调
const handleAdFailed = (type) => {
	console.log('广告观看失败:', type);
	uni.showToast({
		title: '广告播放失败，请重试',
		icon: 'none'
	});
};

// 广告取消回调
const handleAdCancelled = (type) => {
	console.log('用户取消观看广告:', type);
};

// 处理入驻完成
const handleJoinCompleted = (result) => {
	console.log('入驻完成:', result);

	if (result.type === 'ad') {
		// 看广告入驻成功
		uni.showModal({
			title: '入驻成功',
			content: `恭喜您成功入驻！获得${result.data?.days || 30}天免费特权`,
			showCancel: false,
			confirmText: '知道了',
			success: () => {
				uni.redirectTo({
					url: '/pages/business/success?shopId=test&type=ad'
				});
			}
		});
	} else if (result.type === 'payment') {
		// 付费入驻成功
		uni.showToast({
			title: '入驻成功',
			icon: 'success'
		});
		uni.redirectTo({
			url: '/pages/business/success?shopId=test&type=payment'
		});
	}
};

// 处理入驻取消
const handleJoinCancelled = (result) => {
	console.log('入驻取消:', result);
	if (result.type === 'ad') {
		uni.showToast({
			title: '已取消观看广告',
			icon: 'none'
		});
	} else if (result.type === 'payment') {
		uni.showToast({
			title: '已取消支付',
			icon: 'none'
		});
	}
};

const submitForm = (fromAd = false) => {
    if (!validateForm(fromAd)) return;

	console.log('提交的表单数据:', JSON.parse(JSON.stringify(formData)));

	uni.showLoading({ title: '提交中...' });
				setTimeout(() => {
					uni.hideLoading();
		uni.showToast({ title: '提交成功，等待审核', icon: 'success' });
		uni.navigateTo({ url: '/pages/business/success?shopId=test' });
				}, 1500);
};

// --- 广告相关方法 ---
const handleWatchAdJoin = async () => {
	// 检查剩余次数
	if (adStats.value.join.remaining <= 0) {
		uni.showToast({
			title: '今日入驻次数已用完',
			icon: 'none'
		});
		return;
	}

	try {
		// 验证表单
		if (!validateForm(true)) {
			return;
		}

		// 调用增强版广告服务
		const success = await enhancedRewardedAdService.watchAdForJoin({
			shop_name: formData.shopName,
			category: formData.category,
			address: formData.address,
			contact_phone: formData.contactPhone,
			business_time: formData.businessTime,
			scale: formData.scale,
			description: formData.description,
			version: formData.version
		});

		if (success) {
			// 监听奖励发放成功事件
			uni.$on('rewardGranted', handleAdRewardGranted);
		}
	} catch (error) {
		console.error('看广告入驻失败', error);
		uni.showToast({
			title: '操作失败，请重试',
			icon: 'none'
		});
	}
};

const handleAdRewardGranted = (data) => {
	if (data.type === 'merchant_join') {
		// 入驻成功，更新统计数据
		adStats.value.join.remaining--;
		adStats.value.join.lastWatchTime = new Date();

		// 显示成功提示
		uni.showModal({
			title: '入驻成功',
			content: `恭喜您成功入驻！获得${data.data.days}天免费特权，有效期至${data.data.expire_date}`,
			showCancel: false,
			confirmText: '知道了',
			success: () => {
				// 跳转到商家后台
				uni.redirectTo({
					url: '/subPackages/merchant-admin/pages/dashboard'
				});
			}
		});

		// 移除事件监听
		uni.$off('rewardGranted', handleAdRewardGranted);
	}
};

const loadAdStats = async () => {
	try {
		// 这里应该从后台API获取用户的广告观看统计
		// const response = await request.get('/api/merchant/ads/stats', {
		//   params: { ad_type: 'merchant_join', date: new Date().toISOString().split('T')[0] }
		// });

		// 暂时使用默认数据
		adStats.value.join = {
			remaining: 1,
			limit: 1,
			lastWatchTime: null
		};
	} catch (error) {
		console.error('加载广告统计失败', error);
	}
};

// --- 生命周期 ---
onMounted(() => {
	const systemInfo = uni.getSystemInfoSync();
	statusBarHeight.value = systemInfo.statusBarHeight || 20;

	// 加载广告统计数据
	loadAdStats();
});
</script>

<style>
	/* 页面样式 */
	.join-page {
		min-height: 100vh;
		background-color: #f8f9fc;
		position: relative;
	}
	
	/* 高斯模糊背景 */
	.blur-bg {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 450rpx;
		z-index: 0;
		overflow: hidden;
	}
	
	.bg-image {
		width: 100%;
		height: 100%;
		filter: blur(20px);
		transform: scale(1.1);
		opacity: 0.8;
	}
	
	.blur-bg::after {
		content: '';
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		background: linear-gradient(to bottom, 
			#0052CC 0%, 
			#0066FF 50%,
			#f8f9fc 100%
		);
	}
	
	/* 导航栏 */
	.status-bar, .navbar {
		position: relative;
		z-index: 10;
	}
	
	.navbar {
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
	}
	
	.navbar-left, .navbar-right {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-icon-img {
		width: 32rpx;
		height: 32rpx;
	}
	
	.navbar-title {
		color: #ffffff;
		font-size: 34rpx;
		font-weight: 500;
		letter-spacing: 1rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
	}
	
	/* 内容区 */
	.content {
		position: relative;
		z-index: 5;
		padding: 30rpx;
		padding-bottom: 80rpx;
	}
	
	/* 玻璃拟态卡片通用样式 */
	.glass-card {
		background-color: rgba(255, 255, 255, 0.92);
		backdrop-filter: blur(16px);
		border-radius: 20rpx;
		padding: 32rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}
	
	/* 入驻说明卡片 */
	.intro-card {
		padding: 36rpx;
		position: relative;
		overflow: hidden;
		border-radius: 24rpx;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 249, 255, 0.95));
		box-shadow: 
			0 10rpx 30rpx rgba(22, 119, 255, 0.15), 
			0 6rpx 12rpx rgba(0, 0, 0, 0.1),
			0 1rpx 0 rgba(255, 255, 255, 1) inset,
			0 -20rpx 60rpx rgba(255, 255, 255, 0.5) inset;
		border: 1px solid rgba(255, 255, 255, 0.7);
		transform: translateY(-8rpx);
		transition: all 0.3s ease;
	}
	
	.intro-card::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), transparent);
		pointer-events: none;
	}
	
	.intro-header {
		display: flex;
		align-items: center;
		margin-bottom: 36rpx;
	}
	
	.intro-icon-wrap {
		width: 90rpx;
		height: 90rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		background: linear-gradient(135deg, #1677ff, #0052cc);
		box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.2);
	}
	
	.intro-icon {
		width: 50rpx;
		height: 50rpx;
		filter: brightness(0) invert(1);
	}
	
	.intro-titles {
		flex: 1;
	}
	
	.subtitle-wrapper {
		display: block;
		width: 100%;
	}
	
	.intro-title {
		font-size: 34rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 12rpx;
		letter-spacing: 1px;
	}
	
	.intro-subtitle {
		font-size: 22rpx;
		color: #666;
		letter-spacing: 0.5px;
		line-height: 1.3;
		display: block;
	}
	
	.intro-stats {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 24rpx;
		background-color: rgba(248, 249, 252, 0.8);
		border-radius: 16rpx;
		padding: 24rpx 20rpx;
		box-shadow: 
			0 4rpx 12rpx rgba(0, 0, 0, 0.05),
			0 1rpx 0 rgba(255, 255, 255, 1) inset;
		border: 1px solid rgba(255, 255, 255, 0.7);
	}
	
	.stat-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0 10rpx;
	}
	
	.stat-num {
		font-size: 32rpx;
		color: #1677ff;
		font-weight: 600;
		margin-bottom: 10rpx;
		text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
	}
	
	.stat-label {
		font-size: 24rpx;
		color: #666;
		letter-spacing: 1px;
	}
	
	.stat-divider {
		width: 2px;
		height: 44rpx;
		background-color: rgba(0, 0, 0, 0.08);
		margin: 0 15rpx;
	}
	
	/* 表单样式 */
	.form-section {
		margin-bottom: 30rpx;
	}
	
	.form-title {
		font-size: 30rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 24rpx;
		display: block;
	}
	
	.form-item {
		margin-bottom: 24rpx;
	}
	
	.form-label {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 12rpx;
		display: block;
	}
	
	.form-input {
		height: 80rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 26rpx;
		color: #333;
		border: 1px solid rgba(0, 0, 0, 0.05);
	}
	
	.address-wrap {
		display: flex;
		position: relative;
	}
	
	.address-input {
		padding-right: 90rpx;
		flex: 1;
	}
	
	.location-btn {
		position: absolute;
		right: 0;
		top: 0;
		height: 80rpx;
		width: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #1677ff, #0052cc);
		border-radius: 0 12rpx 12rpx 0;
		z-index: 5;
	}
	
	.location-icon-img {
		width: 32rpx;
		height: 32rpx;
		filter: brightness(0) invert(1);
	}
	
	.form-select {
		height: 80rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 26rpx;
		color: #333;
		border: 1px solid rgba(0, 0, 0, 0.05);
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.placeholder {
		color: #999;
	}
	
	.select-arrow-img {
		width: 24rpx;
		height: 24rpx;
		opacity: 0.6;
	}
	
	.form-textarea {
		height: 160rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		padding: 24rpx;
		font-size: 26rpx;
		color: #333;
		border: 1px solid rgba(0, 0, 0, 0.05);
		width: 100%;
		box-sizing: border-box;
	}
	
	/* 入驻优势 */
	.benefits-section {
		margin-bottom: 30rpx;
	}
	
	.benefits-title {
		font-size: 30rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 24rpx;
		display: block;
	}
	
	.benefits-list {
		display: flex;
		flex-direction: column;
	}
	
	.benefit-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		padding: 16rpx 20rpx;
	}
	
	.benefit-item:last-child {
		margin-bottom: 0;
	}
	
	.benefit-icon-wrap {
		width: 64rpx;
		height: 64rpx;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba(22, 119, 255, 0.1);
		margin-right: 16rpx;
	}
	
	.benefit-icon-img {
		width: 32rpx;
		height: 32rpx;
	}
	
	.benefit-content {
		flex: 1;
	}
	
	.benefit-name {
		font-size: 26rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 4rpx;
	}
	
	.benefit-desc {
		font-size: 22rpx;
		color: #666;
	}
	
	/* 提交按钮 */
	.submit-section {
		margin-top: 40rpx;
		margin-bottom: 60rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.submit-btn {
		width: 90%;
		height: 88rpx;
		background: linear-gradient(135deg, #1677ff, #0052cc);
		border-radius: 44rpx;
		color: #ffffff;
		font-size: 30rpx;
		font-weight: 600;
		margin-bottom: 20rpx;
		box-shadow: 0 8rpx 16rpx rgba(22, 119, 255, 0.2);
	}
	
	.agreement-section {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 30rpx;
	}

	.agreement-checkbox {
		width: 32rpx;
		height: 32rpx;
		border: 1px solid #ccc;
		border-radius: 50%;
		margin-right: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.checkbox.checked {
		background-color: #1677ff;
		border-color: #1677ff;
	}
	
	.agreement-text {
		font-size: 24rpx;
		color: #999;
	}
	
	.agreement-link {
		color: #1677ff;
	}
	
	/* 弹窗样式 */
	.popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 100;
		backdrop-filter: blur(3px);
		-webkit-backdrop-filter: blur(3px);
	}
	
	.popup-content {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		z-index: 101;
		border-radius: 24rpx 24rpx 0 0;
		padding-bottom: env(safe-area-inset-bottom);
		max-height: 70vh;
		display: flex;
		flex-direction: column;
		box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
		animation: slideUp 0.3s ease-out;
	}
	
	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;
		border-bottom: 1px solid rgba(0, 0, 0, 0.05);
	}
	
	.popup-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
	}
	
	.popup-close {
		width: 56rpx;
		height: 56rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background-color: rgba(0, 0, 0, 0.05);
	}
	
	.close-icon-img {
		width: 28rpx;
		height: 28rpx;
		opacity: 0.6;
	}
	
	.category-list {
		max-height: calc(70vh - 90rpx);
		padding: 0 30rpx;
	}
	
	.category-list .category-item {
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid rgba(0, 0, 0, 0.05);
		font-size: 28rpx;
		color: #333;
	}
	
	.category-list .category-item.active {
		color: #1677ff;
	}
	
	.check-icon-img {
		width: 36rpx;
		height: 36rpx;
	}
	
	.location-tips {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 8rpx;
		padding: 0 8rpx;
	}
	
	.location-text {
		font-size: 22rpx;
		color: #0052cc;
	}
	
	.location-btn-text {
		font-size: 22rpx;
		color: #0052cc;
		padding: 4rpx 12rpx;
		background-color: rgba(0, 82, 204, 0.05);
		border-radius: 10rpx;
	}
	
	/* 上传图片样式 */
	.upload-box {
		width: 200rpx;
		height: 200rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 12rpx;
		border: 1px dashed rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-top: 16rpx;
	}
	
	.upload-placeholder {
		width: 80rpx;
		height: 80rpx;
		background-color: rgba(0, 82, 204, 0.05);
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 12rpx;
	}
	
	.plus-icon {
		font-size: 44rpx;
		color: #1677ff;
		font-weight: 200;
	}
	
	.upload-desc-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 8rpx;
	}
	
	.upload-desc {
		font-size: 22rpx;
		color: #999;
		line-height: 32rpx;
		text-align: center;
	}
	
	/* 入驻方式选择 */
	.join-methods {
		margin-top: 20rpx;
	}
	
	.join-option {
		display: flex;
		padding: 20rpx;
		border-radius: 16rpx;
		background-color: rgba(255, 255, 255, 0.5);
		border: 1px solid rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
	}
	
	.join-option.active {
		background-color: rgba(22, 119, 255, 0.05);
		border: 1px solid rgba(22, 119, 255, 0.3);
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.06);
	}
	
	.option-radio {
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		border: 2px solid #e0e0e0;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.join-option.active .option-radio {
		border-color: #1677ff;
	}
	
	.radio-dot {
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		background-color: #1677ff;
	}
	
	.option-content {
		flex: 1;
	}
	
	.option-title-row {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}
	
	.option-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 10rpx;
	}
	
	.option-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-right: 10rpx;
	}
	
	.option-desc {
		font-size: 24rpx;
		color: #666;
	}
	
	.option-badge {
		padding: 2rpx 12rpx;
		background-color: #e6f7ff;
		border-radius: 8rpx;
		font-size: 20rpx;
		color: #1677ff;
	}
	
	.premium-badge {
		background-color: #fff7e6;
		color: #fa8c16;
	}
	
	/* 特权列表 */
	.privilege-list {
		margin-top: 30rpx;
		padding: 24rpx;
		background-color: rgba(248, 249, 252, 0.6);
		border-radius: 16rpx;
		border: 1px dashed rgba(22, 119, 255, 0.3);
	}
	
	.privilege-title {
		font-size: 26rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 16rpx;
	}
	
	.privilege-items {
		margin-bottom: 20rpx;
	}
	
	.privilege-item {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
	}
	
	.privilege-dot {
		width: 8rpx;
		height: 8rpx;
		border-radius: 4rpx;
		background-color: #1677ff;
		margin-right: 12rpx;
	}
	
	.privilege-text {
		font-size: 24rpx;
		color: #666;
	}

	/* 增强版广告按钮样式 */
	.enhanced-ad-section {
		margin-top: 30rpx;
	}

	.ad-button-wrapper {
		background: linear-gradient(135deg, #FF6B35, #FF8A50);
		border-radius: 20rpx;
		padding: 4rpx;
		box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);
		transition: all 0.3s ease;
	}

	.ad-button-wrapper:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.4);
	}

	.ad-button-content {
		background: white;
		border-radius: 16rpx;
		padding: 24rpx;
		display: flex;
		align-items: center;
		position: relative;
		overflow: hidden;
	}

	.ad-button-content::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 138, 80, 0.05));
		pointer-events: none;
	}

	.ad-icon-wrapper {
		position: relative;
		margin-right: 20rpx;
	}

	.ad-icon {
		width: 60rpx;
		height: 60rpx;
		border-radius: 12rpx;
	}

	.free-badge {
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		background: linear-gradient(45deg, #52C41A, #73D13D);
		color: white;
		font-size: 20rpx;
		padding: 4rpx 8rpx;
		border-radius: 8rpx;
		font-weight: 600;
		box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
	}

	.ad-text-content {
		flex: 1;
	}

	.ad-title {
		display: block;
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 8rpx;
	}

	.ad-description {
		display: block;
		font-size: 24rpx;
		color: #666;
	}

	.ad-arrow {
		margin-left: 20rpx;
	}

	.arrow-icon {
		font-size: 32rpx;
		color: #FF6B35;
		font-weight: bold;
	}

	.ad-tips {
		margin-top: 16rpx;
		text-align: center;
	}

	.tips-text {
		font-size: 24rpx;
		color: #999;
	}

	.watch-ad-button {
		background: linear-gradient(135deg, #1677ff, #0052cc);
		border-radius: 44rpx;
		height: 80rpx;
		font-size: 28rpx;
		color: #fff;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 20rpx;
		box-shadow: 0 8rpx 16rpx rgba(22, 119, 255, 0.2);
	}
	
	.btn-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 10rpx;
	}
	
	/* 已选版本展示 */
	.selected-plan {
		margin-top: 20rpx;
		padding: 24rpx;
		border-radius: 20rpx;
		background-color: #f8f9fc;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		width: 92%; /* 从88%增加到92% */
		margin-left: auto;
		margin-right: auto;
		border: 1px solid rgba(230, 235, 245, 0.8);
	}
	
	.apple-card {
		background-color: rgba(255, 255, 255, 0.85);
		backdrop-filter: blur(10px);
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
		border: 1px solid rgba(255, 255, 255, 0.5);
	}
	
	.plan-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 14rpx; /* 减小底部间距 */
	}
	
	.plan-badge {
		padding: 6rpx 18rpx;
		border-radius: 14rpx;
		font-size: 22rpx;
		font-weight: 600;
		color: #fff;
	}
	
	.basic-badge {
		background-color: rgba(33, 150, 243, 0.8);
		color: #ffffff;
		border: 1px solid rgba(33, 150, 243, 0.2);
	}
	
	.premium-badge {
		background-color: rgba(255, 152, 0, 0.8);
		color: #ffffff;
		border: 1px solid rgba(255, 152, 0, 0.2);
	}
	
	.deluxe-badge {
		background-color: rgba(156, 39, 176, 0.8);
		color: #ffffff;
		border: 1px solid rgba(156, 39, 176, 0.2);
	}
	
	.plan-price-row {
		display: flex;
		align-items: baseline;
		margin-bottom: 12rpx;
	}
	
	.plan-price-label {
		font-size: 24rpx;
		font-weight: 500;
		color: #333;
	}
	
	.plan-price-value {
		font-size: 40rpx; /* 减小字体大小 */
		font-weight: 700;
		color: #333;
		margin: 0 4rpx;
	}
	
	.plan-price-cycle {
		font-size: 22rpx;
		color: #666;
	}
	
	.change-plan {
		font-size: 24rpx;
		color: #007aff;
		padding: 6rpx 12rpx;
		background-color: rgba(0, 122, 255, 0.1);
		border-radius: 12rpx;
	}
	
	.plan-divider {
		height: 1px;
		background-color: rgba(0, 0, 0, 0.05);
		margin: 12rpx 0 16rpx;
	}
	
	.plan-features {
		margin-bottom: 16rpx;
	}
	
	.plan-feature {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
		font-size: 24rpx;
	}
	
	.feature-dot {
		width: 8rpx;
		height: 8rpx;
		border-radius: 50%;
		background-color: #1677ff;
		margin-right: 10rpx;
	}
	
	.feature-text {
		color: #666;
		font-size: 24rpx;
	}
	
	.select-other-btn {
		margin-top: 10rpx;
		background-color: rgba(0, 122, 255, 0.08);
		border-radius: 14rpx;
		height: 64rpx;
		line-height: 64rpx;
		color: #007aff;
		font-size: 24rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid rgba(0, 122, 255, 0.2);
	}
	
	/* 付费入驻版本弹窗 */
	.version-popup {
		padding-bottom: 100rpx;
		max-height: 75vh; /* 减小最大高度 */
		border-radius: 30rpx 30rpx 0 0;
		box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
		background-color: #f5f5f7;
	}
	
	.version-list {
		padding: 20rpx 10rpx; /* 调整左右内边距 */
		max-height: 50vh;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
	}
	
	.version-card {
		margin-bottom: 24rpx; /* 减小底部间距 */
		border-radius: 24rpx;
		border: none;
		overflow: hidden;
		transition: all 0.3s ease;
		position: relative;
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);
		width: 85%; /* 从75%增加到85% */
		margin-left: auto;
		margin-right: auto;
		transform: scale(0.98); /* 略微调整默认缩放 */
	}
	
	.version-card.active {
		transform: scale(1);
		box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.12);
	}
	
	.apple-style {
		background-color: transparent;
	}
	
	.version-card-bg {
		position: relative;
		padding: 24rpx; /* 从28rpx减小到24rpx */
		border-radius: 24rpx;
		overflow: hidden;
		height: 100%;
	}
	
	.basic-gradient {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		border: 1px solid rgba(33, 150, 243, 0.2);
	}
	
	.premium-gradient {
		background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
		border: 1px solid rgba(255, 193, 7, 0.2);
	}
	
	.deluxe-gradient {
		background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
		border: 1px solid rgba(156, 39, 176, 0.2);
	}
	
	.version-badge {
		position: absolute;
		top: 16rpx;
		right: 16rpx;
		background: linear-gradient(135deg, #ff9a00, #ff6a00);
		color: white;
		font-size: 20rpx;
		font-weight: 600;
		padding: 4rpx 16rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(255, 106, 0, 0.3);
	}
	
	.badge-text {
		letter-spacing: 1px;
	}
	
	.version-pill {
		display: inline-block;
		margin-bottom: 24rpx;
		padding: 6rpx 20rpx;
		background-color: rgba(255, 255, 255, 0.5);
		border-radius: 20rpx;
		font-size: 24rpx;
		font-weight: 600;
		color: #333;
		backdrop-filter: blur(10rpx);
		border: 1px solid rgba(255, 255, 255, 0.7);
	}
	
	.version-content {
		display: flex;
		flex-direction: column;
	}
	
	.version-price-area {
		margin: 6rpx 0 12rpx; /* 减小上下间距 */
	}
	
	.version-price-label {
		font-size: 26rpx;
		color: #333;
		font-weight: 600;
	}
	
	.version-price-value {
		font-size: 40rpx; /* 减小字体大小 */
		color: #333;
		font-weight: 700;
	}
	
	.version-price-cycle {
		font-size: 22rpx;
		color: #666;
		font-weight: 400;
		margin-left: 2rpx;
	}
	
	.version-desc {
		font-size: 22rpx;
		color: #666;
		margin-bottom: 16rpx; /* 减小底部间距 */
	}
	
	.version-features {
		background-color: rgba(255, 255, 255, 0.6);
		border-radius: 16rpx;
		padding: 16rpx 18rpx; /* 调整内边距 */
		margin-bottom: 20rpx;
		backdrop-filter: blur(8rpx);
		border: 1px solid rgba(255, 255, 255, 0.8);
	}
	
	.version-feature-item {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx; /* 减小底部间距 */
		font-size: 22rpx; /* 减小字体大小 */
	}
	
	.feature-dot {
		width: 6rpx;
		height: 6rpx;
		border-radius: 3rpx;
		background-color: #333;
		margin-right: 10rpx;
	}
	
	.feature-text {
		font-size: 22rpx;
		color: #333;
		line-height: 1.4;
	}
	
	.version-select-btn {
		width: 100%;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 36rpx;
		background: rgba(0, 122, 255, 0.9);
		color: #fff;
		font-size: 28rpx;
		font-weight: 500;
		box-shadow: 0 6rpx 12rpx rgba(0, 122, 255, 0.2);
		transition: all 0.3s ease;
	}
	
	.version-select-btn.selected {
		background: rgba(0, 98, 204, 0.9);
	}
	
	.premium-select {
		background: rgba(255, 149, 0, 0.9);
		box-shadow: 0 6rpx 12rpx rgba(255, 149, 0, 0.2);
	}
	
	.premium-select.selected {
		background: rgba(230, 134, 0, 0.9);
	}
	
	.deluxe-select {
		background: rgba(175, 82, 222, 0.9);
		box-shadow: 0 6rpx 12rpx rgba(175, 82, 222, 0.2);
	}
	
	.deluxe-select.selected {
		background: rgba(150, 70, 190, 0.9);
	}
	
	.version-popup-footer {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		padding: 24rpx 32rpx;
		border-top: 1px solid rgba(0, 0, 0, 0.05);
	}
	
	.confirm-version-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #007AFF, #0062CC);
		border-radius: 44rpx;
		color: #ffffff;
		font-size: 30rpx;
		font-weight: 600;
		box-shadow: 0 8rpx 16rpx rgba(0, 122, 255, 0.2);
	}
</style> 