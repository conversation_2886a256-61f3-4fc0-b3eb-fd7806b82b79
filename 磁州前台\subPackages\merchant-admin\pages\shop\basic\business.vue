<template>
  <view class="business-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">经营信息</text>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 营业时间 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">营业时间</text>
        </view>
        
        <view class="time-settings">
          <view class="time-item" v-for="(day, index) in businessHours" :key="index">
            <view class="day-info">
              <text class="day-name">{{day.name}}</text>
              <switch :checked="day.isOpen" @change="(e) => toggleDayStatus(index, e)" color="#1677FF" />
            </view>
            <view class="time-range" v-if="day.isOpen">
              <view class="time-picker" @click="showTimePicker('start', index)">
                <text>{{day.startTime}}</text>
                <view class="arrow-icon"></view>
              </view>
              <text class="time-separator">至</text>
              <view class="time-picker" @click="showTimePicker('end', index)">
                <text>{{day.endTime}}</text>
                <view class="arrow-icon"></view>
              </view>
            </view>
            <view class="closed-tip" v-else>
              <text>休息</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <view class="switch-item">
            <text>支持全天营业</text>
            <switch :checked="is24Hours" @change="toggle24Hours" color="#1677FF" />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">营业时间备注</text>
          <textarea class="time-remark" v-model="businessRemark" placeholder="可填写节假日营业时间调整等信息" />
        </view>
      </view>
      
      <!-- 配送信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">配送信息</text>
        </view>
        
        <view class="form-item">
          <view class="switch-item">
            <text>支持配送</text>
            <switch :checked="deliveryEnabled" @change="toggleDelivery" color="#1677FF" />
          </view>
        </view>
        
        <block v-if="deliveryEnabled">
          <view class="form-item">
            <text class="form-label">起送金额</text>
            <view class="price-input">
              <text class="price-symbol">¥</text>
              <input type="digit" v-model="minOrderAmount" placeholder="0" />
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">配送费</text>
            <view class="price-input">
              <text class="price-symbol">¥</text>
              <input type="digit" v-model="deliveryFee" placeholder="0" />
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">免配送费订单金额</text>
            <view class="price-input">
              <text class="price-symbol">¥</text>
              <input type="digit" v-model="freeDeliveryAmount" placeholder="0" />
              <text class="price-tip">0表示不设置免配送费</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">配送范围</text>
            <view class="radius-slider">
              <slider 
                :value="deliveryRadius" 
                :min="1" 
                :max="20" 
                :step="0.5"
                show-value 
                @change="changeDeliveryRadius" 
                activeColor="#1677FF" />
              <text class="radius-unit">公里</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">配送时间</text>
            <view class="time-range">
              <view class="time-picker" @click="showTimePicker('deliveryStart', -1)">
                <text>{{deliveryStartTime}}</text>
                <view class="arrow-icon"></view>
              </view>
              <text class="time-separator">至</text>
              <view class="time-picker" @click="showTimePicker('deliveryEnd', -1)">
                <text>{{deliveryEndTime}}</text>
                <view class="arrow-icon"></view>
              </view>
            </view>
          </view>
        </block>
      </view>
      
      <!-- 服务标签 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">服务标签</text>
          <text class="section-desc">选择您提供的服务，最多可选5项</text>
        </view>
        
        <view class="service-tags">
          <view 
            v-for="(tag, index) in serviceTags" 
            :key="index"
            :class="['tag-item', {'active': selectedTags.includes(tag.id)}]"
            @click="toggleServiceTag(tag.id)">
            <view class="tag-icon" :style="{backgroundColor: tag.color}">
              <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="#fff" stroke-width="2">
                <path :d="tag.icon"></path>
              </svg>
            </view>
            <text class="tag-name">{{tag.name}}</text>
          </view>
        </view>
      </view>
      
      <!-- 支付方式 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">支付方式</text>
        </view>
        
        <view class="payment-methods">
          <view 
            v-for="(method, index) in paymentMethods" 
            :key="index"
            :class="['payment-item', {'active': selectedPayments.includes(method.id)}]"
            @click="togglePaymentMethod(method.id)">
            <image :src="method.icon" mode="aspectFit" class="payment-icon"></image>
            <text class="payment-name">{{method.name}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部保存按钮 -->
    <view class="bottom-bar">
      <button class="save-button" @click="saveBusinessInfo">保存</button>
    </view>
    
    <!-- 时间选择器弹窗 -->
    <uni-popup ref="timePicker" type="bottom">
      <view class="picker-container">
        <view class="picker-header">
          <text class="cancel-btn" @click="cancelTimePicker">取消</text>
          <text class="picker-title">选择时间</text>
          <text class="confirm-btn" @click="confirmTimePicker">确定</text>
        </view>
        <picker-view :value="timeValue" @change="timeChange" class="picker-view">
          <picker-view-column>
            <view class="picker-item" v-for="(hour, index) in hours" :key="index">{{hour}}</view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(minute, index) in minutes" :key="index">{{minute}}</view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      businessHours: [
        { name: '周一', isOpen: true, startTime: '09:00', endTime: '21:00' },
        { name: '周二', isOpen: true, startTime: '09:00', endTime: '21:00' },
        { name: '周三', isOpen: true, startTime: '09:00', endTime: '21:00' },
        { name: '周四', isOpen: true, startTime: '09:00', endTime: '21:00' },
        { name: '周五', isOpen: true, startTime: '09:00', endTime: '21:00' },
        { name: '周六', isOpen: true, startTime: '09:00', endTime: '22:00' },
        { name: '周日', isOpen: true, startTime: '09:00', endTime: '22:00' }
      ],
      is24Hours: false,
      businessRemark: '国家法定节假日正常营业，春节期间另行通知',
      
      deliveryEnabled: true,
      minOrderAmount: '20',
      deliveryFee: '5',
      freeDeliveryAmount: '50',
      deliveryRadius: 5,
      deliveryStartTime: '09:00',
      deliveryEndTime: '20:30',
      
      serviceTags: [
        { id: 1, name: '免费WiFi', color: '#1677FF', icon: 'M8.111 16.404a5.5 5.5 0 017.778 0M12 12a9 9 0 00-9 9m18 0a9 9 0 00-9-9m0 13a4 4 0 100-8 4 4 0 000 8z' },
        { id: 2, name: '停车场', color: '#52c41a', icon: 'M5 17h-2v-2h2v2zm2 0h-2v-2h2v2zm2 0h-2v-2h2v2zm2 0h-2v-2h2v2zm2 0h-2v-2h2v2zm2 0h-2v-2h2v2zm2 0h-2v-2h2v2zm2 0h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2v-2h2v2z' },
        { id: 3, name: '禁烟', color: '#ff4d4f', icon: 'M8 11V7a4 4 0 118 0m-4 8v3m-8-3h16M2 2l20 20' },
        { id: 4, name: '刷卡', color: '#fa8c16', icon: 'M3 10h18M7 15h1m4 0h1m4 0h1M7 6h1m4 0h1m4 0h1M3 10h18M3 10v8a2 2 0 002 2h14a2 2 0 002-2v-8M3 10V6a2 2 0 012-2h14a2 2 0 012 2v4' },
        { id: 5, name: '宝宝椅', color: '#722ed1', icon: 'M12 5v6m0 0v6m0-6h6m-6 0H6' },
        { id: 6, name: '残障设施', color: '#13c2c2', icon: 'M16 4h2a2 2 0 012 2v14a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2h2' },
        { id: 7, name: '包厢', color: '#eb2f96', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },
        { id: 8, name: '露天座位', color: '#faad14', icon: 'M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z' }
      ],
      selectedTags: [1, 2, 4],
      
      paymentMethods: [
        { id: 'wechat', name: '微信支付', icon: '/static/images/shop/wechat-pay.png' },
        { id: 'alipay', name: '支付宝', icon: '/static/images/shop/alipay.png' },
        { id: 'card', name: '银行卡', icon: '/static/images/shop/card-pay.png' },
        { id: 'cash', name: '现金', icon: '/static/images/shop/cash-pay.png' }
      ],
      selectedPayments: ['wechat', 'alipay', 'cash'],
      
      // 时间选择器相关
      hours: Array.from({length: 24}, (_, i) => i < 10 ? `0${i}` : `${i}`),
      minutes: Array.from({length: 60}, (_, i) => i < 10 ? `0${i}` : `${i}`),
      timeValue: [9, 0],
      currentTimeType: '',
      currentDayIndex: -1
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    toggleDayStatus(index, e) {
      this.businessHours[index].isOpen = e.detail.value;
    },
    toggle24Hours(e) {
      this.is24Hours = e.detail.value;
      if (this.is24Hours) {
        this.businessHours.forEach(day => {
          day.startTime = '00:00';
          day.endTime = '24:00';
        });
      }
    },
    toggleDelivery(e) {
      this.deliveryEnabled = e.detail.value;
    },
    changeDeliveryRadius(e) {
      this.deliveryRadius = e.detail.value;
    },
    toggleServiceTag(tagId) {
      const index = this.selectedTags.indexOf(tagId);
      if (index > -1) {
        this.selectedTags.splice(index, 1);
      } else {
        if (this.selectedTags.length >= 5) {
          uni.showToast({
            title: '最多选择5个标签',
            icon: 'none'
          });
          return;
        }
        this.selectedTags.push(tagId);
      }
    },
    togglePaymentMethod(methodId) {
      const index = this.selectedPayments.indexOf(methodId);
      if (index > -1) {
        this.selectedPayments.splice(index, 1);
      } else {
        this.selectedPayments.push(methodId);
      }
    },
    showTimePicker(type, dayIndex) {
      this.currentTimeType = type;
      this.currentDayIndex = dayIndex;
      
      // 设置初始值
      let timeString = '';
      if (dayIndex >= 0) {
        timeString = type === 'start' ? this.businessHours[dayIndex].startTime : this.businessHours[dayIndex].endTime;
      } else {
        timeString = type === 'deliveryStart' ? this.deliveryStartTime : this.deliveryEndTime;
      }
      
      const [hour, minute] = timeString.split(':');
      this.timeValue = [parseInt(hour), parseInt(minute)];
      
      this.$refs.timePicker.open();
    },
    timeChange(e) {
      this.timeValue = e.detail.value;
    },
    confirmTimePicker() {
      const hour = this.hours[this.timeValue[0]];
      const minute = this.minutes[this.timeValue[1]];
      const timeString = `${hour}:${minute}`;
      
      if (this.currentDayIndex >= 0) {
        if (this.currentTimeType === 'start') {
          this.businessHours[this.currentDayIndex].startTime = timeString;
        } else {
          this.businessHours[this.currentDayIndex].endTime = timeString;
        }
      } else {
        if (this.currentTimeType === 'deliveryStart') {
          this.deliveryStartTime = timeString;
        } else {
          this.deliveryEndTime = timeString;
        }
      }
      
      this.$refs.timePicker.close();
    },
    cancelTimePicker() {
      this.$refs.timePicker.close();
    },
    saveBusinessInfo() {
      // 表单验证
      if (this.deliveryEnabled) {
        if (!this.minOrderAmount) {
          uni.showToast({
            title: '请输入起送金额',
            icon: 'none'
          });
          return;
        }
        
        if (!this.deliveryFee) {
          uni.showToast({
            title: '请输入配送费',
            icon: 'none'
          });
          return;
        }
      }
      
      if (this.selectedPayments.length === 0) {
        uni.showToast({
          title: '请至少选择一种支付方式',
          icon: 'none'
        });
        return;
      }
      
      // 模拟保存
      uni.showLoading({
        title: '保存中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
}
</script>

<style>
.business-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 100px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
}

.form-container {
  padding: 16px;
}

.form-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
}

.section-desc {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}

.time-settings {
  
}

.time-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.time-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.day-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.day-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.time-range {
  display: flex;
  align-items: center;
}

.time-picker {
  flex: 1;
  height: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-separator {
  margin: 0 12px;
  color: #999;
}

.closed-tip {
  height: 40px;
  display: flex;
  align-items: center;
  color: #ff4d4f;
  font-size: 14px;
}

.form-item {
  margin-bottom: 16px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-item text {
  font-size: 14px;
  color: #333;
}

.time-remark {
  width: 100%;
  height: 80px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  background-color: #f9f9f9;
}

.price-input {
  position: relative;
}

.price-symbol {
  position: absolute;
  left: 12px;
  top: 0;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #666;
}

input {
  width: 100%;
  height: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px 0 24px;
  font-size: 14px;
  background-color: #f9f9f9;
}

.price-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}

.radius-slider {
  display: flex;
  align-items: center;
}

.radius-unit {
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.tag-item {
  width: 72px;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.6;
}

.tag-item.active {
  opacity: 1;
}

.tag-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.tag-name {
  font-size: 12px;
  color: #666;
}

.payment-methods {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.payment-item {
  width: 72px;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.6;
}

.payment-item.active {
  opacity: 1;
}

.payment-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 4px;
}

.payment-name {
  font-size: 12px;
  color: #666;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.save-button {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  color: #fff;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 时间选择器样式 */
.picker-container {
  background-color: #fff;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.cancel-btn {
  font-size: 14px;
  color: #999;
}

.picker-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.confirm-btn {
  font-size: 14px;
  color: #1677FF;
}

.picker-view {
  height: 240px;
  width: 100%;
}

.picker-item {
  line-height: 40px;
  text-align: center;
  font-size: 14px;
}
</style> 