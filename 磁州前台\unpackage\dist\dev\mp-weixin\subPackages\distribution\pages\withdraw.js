"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_distributionService = require("../../../utils/distributionService.js");
const _sfc_main = {
  __name: "withdraw",
  setup(__props) {
    const availableCommission = common_vendor.ref(0);
    const withdrawalSettings = common_vendor.reactive({
      minAmount: 50,
      maxAmount: 5e3,
      feeRate: 0.01,
      withdrawalCycle: "weekly",
      withdrawalDays: [1, 4],
      // 周一和周四可提现
      autoApprove: false,
      needRealName: true,
      withdrawalMethods: ["wechat", "alipay", "bank"]
    });
    const withdrawMethods = [
      { name: "微信", value: "wechat" },
      { name: "支付宝", value: "alipay" },
      { name: "银行卡", value: "bank" }
    ];
    const formData = common_vendor.reactive({
      amount: "",
      method: "wechat",
      account: "",
      bankName: "",
      realName: "",
      mobile: ""
    });
    const withdrawalRecords = common_vendor.ref([]);
    const canSubmit = common_vendor.computed(() => {
      const amountValid = formData.amount && parseFloat(formData.amount) >= withdrawalSettings.minAmount && parseFloat(formData.amount) <= Math.min(withdrawalSettings.maxAmount, availableCommission.value);
      const accountValid = formData.account.trim() !== "";
      const bankNameValid = formData.method !== "bank" || formData.bankName.trim() !== "";
      const realNameValid = formData.realName.trim() !== "";
      const mobileValid = formData.mobile.trim() !== "" && formData.mobile.length === 11;
      return amountValid && accountValid && bankNameValid && realNameValid && mobileValid;
    });
    common_vendor.onMounted(async () => {
      await getAvailableCommission();
      await getWithdrawalSettings();
      await getWithdrawalRecords();
    });
    const getAvailableCommission = async () => {
      try {
        const info = await utils_distributionService.distributionService.getDistributorInfo({
          userId: "1001"
          // 模拟数据，实际应从用户系统获取
        });
        if (info) {
          availableCommission.value = info.availableCommission;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/withdraw.vue:251", "获取可提现佣金失败", error);
        common_vendor.index.showToast({
          title: "获取可提现佣金失败",
          icon: "none"
        });
      }
    };
    const getWithdrawalSettings = async () => {
      try {
        const settings = await utils_distributionService.distributionService.getWithdrawalSettings();
        if (settings) {
          Object.assign(withdrawalSettings, settings);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/withdraw.vue:268", "获取提现设置失败", error);
      }
    };
    const getWithdrawalRecords = async () => {
      try {
        withdrawalRecords.value = [
          {
            id: "W1001",
            amount: 100,
            fee: 1,
            actualAmount: 99,
            method: "wechat",
            account: "wx123456",
            status: "pending",
            createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1e3).toISOString()
          },
          {
            id: "W1002",
            amount: 200,
            fee: 2,
            actualAmount: 198,
            method: "alipay",
            account: "<EMAIL>",
            status: "success",
            createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1e3).toISOString()
          }
        ];
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/withdraw.vue:300", "获取提现记录失败", error);
      }
    };
    const validateAmount = () => {
      if (!formData.amount)
        return;
      let amount = parseFloat(formData.amount);
      formData.amount = amount.toFixed(2);
      if (amount > Math.min(withdrawalSettings.maxAmount, availableCommission.value)) {
        formData.amount = Math.min(withdrawalSettings.maxAmount, availableCommission.value).toFixed(2);
        common_vendor.index.showToast({
          title: `提现金额不能超过${formData.amount}元`,
          icon: "none"
        });
      }
      if (amount < withdrawalSettings.minAmount) {
        common_vendor.index.showToast({
          title: `提现金额不能低于${withdrawalSettings.minAmount}元`,
          icon: "none"
        });
      }
    };
    const withdrawAll = () => {
      formData.amount = Math.min(withdrawalSettings.maxAmount, availableCommission.value).toFixed(2);
    };
    const selectMethod = (method) => {
      formData.method = method;
      formData.account = "";
      if (method !== "bank") {
        formData.bankName = "";
      }
    };
    const submitWithdrawal = async () => {
      if (!canSubmit.value)
        return;
      try {
        common_vendor.index.showLoading({
          title: "提交中...",
          mask: true
        });
        const result = await utils_distributionService.distributionService.applyWithdrawal({
          amount: parseFloat(formData.amount),
          method: formData.method,
          account: formData.account,
          bankName: formData.bankName,
          realName: formData.realName,
          mobile: formData.mobile
        });
        common_vendor.index.hideLoading();
        if (result.success) {
          common_vendor.index.showModal({
            title: "提现申请成功",
            content: "您的提现申请已提交，请耐心等待处理。",
            showCancel: false,
            success: () => {
              getAvailableCommission();
              getWithdrawalRecords();
              formData.amount = "";
            }
          });
        } else {
          common_vendor.index.showModal({
            title: "提现申请失败",
            content: result.message || "请稍后再试",
            showCancel: false
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subPackages/distribution/pages/withdraw.vue:389", "提交提现申请失败", error);
        common_vendor.index.showToast({
          title: "提交提现申请失败",
          icon: "none"
        });
      }
    };
    const getMethodName = (method) => {
      const methodMap = {
        "wechat": "微信",
        "alipay": "支付宝",
        "bank": "银行卡"
      };
      return methodMap[method] || method;
    };
    const getStatusText = (status) => {
      const statusMap = {
        "pending": "处理中",
        "success": "已到账",
        "failed": "提现失败"
      };
      return statusMap[status] || status;
    };
    const getStatusClass = (status) => {
      return {
        "pending": "status-pending",
        "success": "status-success",
        "failed": "status-failed"
      }[status] || "";
    };
    const formatCommission = (amount) => {
      return utils_distributionService.distributionService.formatCommission(amount);
    };
    const formatTime = (time) => {
      if (!time)
        return "";
      const date = new Date(time);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "提现帮助",
        content: `提现金额最低${withdrawalSettings.minAmount}元，最高${withdrawalSettings.maxAmount}元。提现手续费为提现金额的${withdrawalSettings.feeRate * 100}%。提现申请将在1-3个工作日内处理，请耐心等待。`,
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: common_vendor.t(formatCommission(availableCommission.value)),
        d: common_vendor.o([($event) => formData.amount = $event.detail.value, validateAmount]),
        e: formData.amount,
        f: common_vendor.t(withdrawalSettings.minAmount),
        g: common_vendor.o(withdrawAll),
        h: common_vendor.f(withdrawMethods, (method, index, i0) => {
          return {
            a: common_vendor.n(method.value),
            b: common_vendor.t(method.name),
            c: index,
            d: formData.method === method.value ? 1 : "",
            e: common_vendor.o(($event) => selectMethod(method.value), index)
          };
        }),
        i: formData.method === "wechat"
      }, formData.method === "wechat" ? {
        j: formData.account,
        k: common_vendor.o(($event) => formData.account = $event.detail.value)
      } : {}, {
        l: formData.method === "alipay"
      }, formData.method === "alipay" ? {
        m: formData.account,
        n: common_vendor.o(($event) => formData.account = $event.detail.value)
      } : {}, {
        o: formData.method === "bank"
      }, formData.method === "bank" ? {
        p: formData.account,
        q: common_vendor.o(($event) => formData.account = $event.detail.value)
      } : {}, {
        r: formData.method === "bank"
      }, formData.method === "bank" ? {
        s: formData.bankName,
        t: common_vendor.o(($event) => formData.bankName = $event.detail.value)
      } : {}, {
        v: formData.realName,
        w: common_vendor.o(($event) => formData.realName = $event.detail.value),
        x: formData.mobile,
        y: common_vendor.o(($event) => formData.mobile = $event.detail.value),
        z: common_vendor.t(formData.amount ? (formData.amount * withdrawalSettings.feeRate).toFixed(2) : "0.00"),
        A: common_vendor.t(withdrawalSettings.feeRate * 100),
        B: common_vendor.t(formData.amount ? (formData.amount * (1 - withdrawalSettings.feeRate)).toFixed(2) : "0.00"),
        C: !canSubmit.value,
        D: !canSubmit.value ? 1 : "",
        E: common_vendor.o(submitWithdrawal),
        F: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/commission")),
        G: withdrawalRecords.value.length > 0
      }, withdrawalRecords.value.length > 0 ? {
        H: common_vendor.f(withdrawalRecords.value, (record, index, i0) => {
          return {
            a: common_vendor.t(getMethodName(record.method)),
            b: common_vendor.t(formatCommission(record.amount)),
            c: common_vendor.t(formatTime(record.createdAt)),
            d: common_vendor.t(getStatusText(record.status)),
            e: common_vendor.n(getStatusClass(record.status)),
            f: index
          };
        })
      } : {}, {
        I: common_vendor.t(withdrawalSettings.minAmount),
        J: common_vendor.t(withdrawalSettings.maxAmount),
        K: common_vendor.t(withdrawalSettings.feeRate * 100)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/distribution/pages/withdraw.js.map
