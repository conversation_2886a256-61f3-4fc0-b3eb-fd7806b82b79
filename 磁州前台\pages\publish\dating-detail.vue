<template>
  <view class="detail-container dating-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">婚恋交友详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <!-- 隐藏的Canvas用于绘制海报 -->
    <canvas canvas-id="posterCanvas" class="poster-canvas" style="width: 600px; height: 900px; position: fixed; top: -9999px;"></canvas>
    
    <!-- 悬浮海报按钮 -->
    <view class="float-poster-btn" @click="generateShareImage">
      <image src="/static/images/tabbar/海报.png" class="poster-icon"></image>
      <text class="poster-text">海报</text>
    </view>
    
    <view class="detail-wrapper dating-detail-wrapper">
      <!-- 个人信息卡片 -->
      <view class="content-card personal-card">
        <view class="section-title">个人信息</view>
        <view class="personal-header">
          <view class="avatar-container">
            <image :src="datingData.images[0] || '/static/images/default-avatar.png'" mode="aspectFill" class="avatar-image"></image>
          </view>
          <view class="personal-info">
            <text class="personal-name">{{datingData.nickname}}</text>
            <view class="personal-meta">
              <text class="meta-text">{{datingData.age}}岁</text>
              <text class="meta-text">{{datingData.gender === 'male' ? '男' : '女'}}</text>
              <text class="meta-text">{{datingData.height}}cm</text>
            </view>
          </view>
        </view>
        
        <!-- 基本信息列表 -->
        <view class="basic-info">
          <view class="info-item">
            <text class="info-label">学历</text>
            <text class="info-value">{{datingData.education}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">婚姻状况</text>
            <text class="info-value">{{datingData.maritalStatus}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">职业</text>
            <text class="info-value">{{datingData.occupation}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">月收入</text>
            <text class="info-value">{{datingData.income}}</text>
          </view>
        </view>
      </view>
      
      <!-- 个人照片展示 -->
      <view class="content-card photos-card">
        <view class="section-title">个人照片</view>
        <view class="photos-grid">
          <view class="photo-item" v-for="(image, index) in datingData.images" :key="index" @click="previewImage(index)">
            <image :src="image" mode="aspectFill" class="photo-image"></image>
          </view>
        </view>
      </view>
      
      <!-- 自我介绍 -->
      <view class="content-card intro-card">
        <view class="section-title">自我介绍</view>
        <view class="intro-content">
          <rich-text :nodes="datingData.selfIntro" class="intro-text"></rich-text>
        </view>
      </view>
      
      <!-- 择偶要求 -->
      <view class="content-card expectation-card">
        <view class="section-title">择偶要求</view>
        <view class="expectation-content">
          <rich-text :nodes="datingData.expectation" class="expectation-text"></rich-text>
        </view>
      </view>
      
      <!-- 兴趣爱好 -->
      <view class="content-card interests-card">
        <view class="section-title">兴趣爱好</view>
        <view class="interests-tags">
          <view class="interest-tag" v-for="(interest, index) in datingData.interests" :key="index">
            <text class="interest-text">{{interest}}</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式卡片 -->
      <view class="content-card contact-card">
        <view class="section-title">联系方式</view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{datingData.contact}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{datingData.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 添加举报卡片 -->
      <report-card :content-id="datingData.id" content-type="dating"></report-card>
      
      <!-- 红包区域 -->
      <view class="content-card red-packet-card" v-if="datingData.hasRedPacket">
        <view class="section-title">红包福利</view>
        <view class="red-packet-section">
          <view class="red-packet-container" @click="openRedPacket">
            <view class="red-packet-blur-bg"></view>
            <view class="red-packet-content">
              <view class="red-packet-left">
                <image class="red-packet-icon" src="/static/images/tabbar/抢红包.gif"></image>
              <view class="red-packet-info">
                <view class="red-packet-title">
                  {{datingData.redPacket.type === 'random' ? '随机金额红包' : '查看信息领红包'}}
                </view>
                <view class="red-packet-desc">
                  还剩{{datingData.redPacket.remain}}个，{{getRedPacketConditionText()}}
                  </view>
                </view>
              </view>
              <view class="red-packet-right">
                <view class="red-packet-amount"><text class="prefix">共</text> ¥{{datingData.redPacket.amount}}</view>
                <view class="grab-btn">立即领取</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 相关推荐卡片 -->
      <view class="content-card related-dating-card">
        <!-- 标题栏 -->
        <view class="collapsible-header">
          <view class="section-title">相关推荐</view>
        </view>
        
        <!-- 内容区 -->
        <view class="collapsible-content">
          <!-- 简洁的列表 -->
          <view class="related-dating-list">
            <view class="related-dating-item" 
                 v-for="(item, index) in relatedDating.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToDatingDetail(item.id)">
              <view class="related-dating-left">
                <image :src="item.avatar" mode="aspectFill" class="related-avatar"></image>
              </view>
              <view class="related-dating-info">
                <view class="related-dating-title">
                  <text class="related-name">{{item.nickname}}</text>
                  <text class="related-age">{{item.age}}岁</text>
                </view>
                <view class="related-dating-meta">
                  <text class="related-meta-text">{{item.education}}</text>
                  <text class="related-meta-text">{{item.occupation}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="footer-action-bar">
      <view class="action-item collect-action" @click="toggleCollect">
        <text class="action-icon" :class="{'icon-collected': isCollected, 'icon-collect': !isCollected}"></text>
        <text class="action-text">{{isCollected ? '已收藏' : '收藏'}}</text>
      </view>
      <view class="action-item share-action" @click="shareToFriend">
        <text class="action-icon icon-share"></text>
        <text class="action-text">分享</text>
      </view>
      <view class="action-item contact-action" @click="contactPerson">
        <text class="action-text">联系TA</text>
      </view>
    </view>
    
    <!-- 海报弹窗 -->
    <view class="poster-modal" v-if="showPosterModal" @click="closePosterModal">
      <view class="poster-container" @click.stop>
        <view class="poster-header">
          <text class="poster-modal-title">分享海报</text>
          <text class="close-icon" @click="closePosterModal">×</text>
        </view>
        <view class="poster-image-container">
          <image :src="posterUrl" mode="widthFix" class="poster-preview"></image>
        </view>
        <view class="poster-footer">
          <button class="poster-btn save-btn" @click="savePoster">保存到相册</button>
          <button class="poster-btn share-btn" open-type="share">分享给朋友</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { onLoad, onShow, onHide, onUnload } from '@dcloudio/uni-app';
import { formatTime } from '../../utils/date.js';
import ReportCard from '@/components/ReportCard.vue';

// --- 响应式状态 ---
const statusBarHeight = ref(20);
const datingId = ref('');
const isCollected = ref(false);
const showPosterModal = ref(false);
const posterUrl = ref('');

const datingData = reactive({
        id: '1',
        nickname: '阳光男孩',
        gender: 'male',
        age: 28,
        height: 180,
        education: '本科',
        maritalStatus: '未婚',
        occupation: '工程师',
        income: '8000-12000',
        selfIntro: '性格开朗，喜欢运动，热爱生活。工作稳定，有上进心，希望找一个志同道合的伴侣一起成长。',
        expectation: '希望对方性格温柔，有一定的生活情趣，年龄25-30岁，身高160cm以上，学历大专以上。',
        interests: ['旅游', '美食', '运动', '电影'],
        images: [
          '/static/images/default-avatar.png',
          '/static/images/default-image.png',
          '/static/images/default-image.png'
        ],
        contact: '张先生',
        phone: '13800138000',
        wechat: 'sunshine123',
  publishTime: new Date().getTime() - 86400000,
        hasRedPacket: true,
        redPacket: {
          type: 'random',
    amount: 50,
    remain: 15,
    total: 30
        }
});

const relatedDating = reactive([
        {
          id: '2',
          nickname: '甜心女孩',
          age: 26,
          avatar: '/static/images/default-avatar.png',
          education: '研究生',
          occupation: '教师'
        },
        {
          id: '3',
          nickname: '成熟稳重',
          age: 32,
          avatar: '/static/images/default-avatar.png',
          education: '本科',
          occupation: '金融'
        },
        {
          id: '4',
          nickname: '活力四射',
          age: 25,
          avatar: '/static/images/default-avatar.png',
          education: '大专',
          occupation: '设计师'
        }
]);

// --- 方法 ---

const goBack = () => uni.navigateBack();

const previewImage = (index) => {
      uni.previewImage({
        current: index,
    urls: datingData.images,
      });
};

const callPhone = () => {
  uni.makePhoneCall({ phoneNumber: datingData.phone });
};

const getRedPacketConditionText = () => "查看详情可领取";

const openRedPacket = () => {
  uni.showToast({ title: '红包功能正在开发中', icon: 'none' });
};

const navigateToDatingDetail = (id) => {
  uni.navigateTo({ url: `/pages/publish/dating-detail?id=${id}` });
};

const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  uni.showToast({ title: isCollected.value ? '收藏成功' : '取消收藏' });
};

const shareToFriend = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    title: `${datingData.nickname}的交友信息`,
    summary: datingData.selfIntro,
    imageUrl: datingData.images[0],
  });
};

const contactPerson = () => {
  uni.showActionSheet({
    itemList: ['拨打电话', '添加微信'],
    success: (res) => {
      if (res.tapIndex === 0) callPhone();
      if (res.tapIndex === 1) {
        uni.setClipboardData({
          data: datingData.wechat,
          success: () => uni.showToast({ title: '微信号已复制' }),
        });
      }
    },
  });
};

const generateShareImage = () => {
  uni.showLoading({ title: '海报生成中...' });
  const ctx = uni.createCanvasContext('posterCanvas');
    // ... (省略Canvas绘制逻辑)
      setTimeout(() => {
    posterUrl.value = '/static/images/default-image.png'; // 假设这是生成的海报
    showPosterModal.value = true;
        uni.hideLoading();
      }, 1000);
};

const closePosterModal = () => {
  showPosterModal.value = false;
};

const savePoster = () => {
  uni.saveImageToPhotosAlbum({
    filePath: posterUrl.value,
    success: () => uni.showToast({ title: '保存成功' }),
    fail: () => uni.showToast({ title: '保存失败', icon: 'error' }),
  });
};

// --- 生命周期 ---
onLoad((options) => {
  datingId.value = options.id || '';
  // fetchDatingData(datingId.value);
});

onMounted(() => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;
});
</script>

<style lang="scss">
.detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #FF6B95, #FF8E7F);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 149, 0.2);
}

.navbar-left {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-right {
  position: absolute;
  right: 30rpx;
}

/* 详情包装器 */
.detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

/* 内容卡片通用样式 */
.content-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 章节标题 */
.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 6rpx;
  height: 32rpx;
  width: 8rpx;
  background: linear-gradient(to bottom, #FF6B95, #FF8E7F);
  border-radius: 4rpx;
}

/* 个人信息卡片 */
.personal-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar-container {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 30rpx;
  border: 4rpx solid #FFF;
  box-shadow: 0 6rpx 16rpx rgba(255, 107, 149, 0.2);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.personal-info {
  flex: 1;
}

.personal-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.personal-meta {
  display: flex;
  flex-wrap: wrap;
}

.meta-text {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f7fa;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

/* 基本信息列表 */
.basic-info {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}

.info-label {
  color: #666;
  font-size: 28rpx;
}

.info-value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

/* 照片网格 */
.photos-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.photo-item {
  width: calc(33.33% - 20rpx);
  padding-bottom: calc(33.33% - 20rpx);
  margin: 10rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.photo-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 自我介绍和择偶要求 */
.intro-content, .expectation-content {
  color: #333;
  font-size: 28rpx;
  line-height: 1.6;
}

/* 兴趣标签 */
.interests-tags {
  display: flex;
  flex-wrap: wrap;
}

.interest-tag {
  background: #f5f7fa;
  color: #666;
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

/* 联系方式卡片 */
.contact-content {
  padding: 10rpx 0;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}

.contact-label {
  color: #666;
  font-size: 28rpx;
}

.contact-value {
  color: #333;
  font-size: 28rpx;
}

.contact-phone {
  color: #FF6B95;
}

.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}

.tips-icon {
  color: #FF6B95;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.tips-text {
  color: #999;
  font-size: 24rpx;
}

/* 红包卡片 */
.red-packet-section {
  padding: 10rpx 0;
}

.red-packet-container {
  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);
  border-radius: 12rpx;
  padding: 4rpx;
  position: relative;
  overflow: hidden;
}

.red-packet-blur-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("../../../static/images/tabbar/红包背景.png") no-repeat center/cover;
  opacity: 0.1;
}

.red-packet-content {
  background: #FFF;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.red-packet-left {
  display: flex;
  align-items: center;
}

.red-packet-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
}

.red-packet-info {
  display: flex;
  flex-direction: column;
}

.red-packet-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4D4F;
  margin-bottom: 6rpx;
}

.red-packet-desc {
  font-size: 22rpx;
  color: #999;
}

.red-packet-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.red-packet-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF4D4F;
  margin-bottom: 10rpx;
}

.prefix {
  font-size: 22rpx;
  font-weight: normal;
}

.grab-btn {
  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);
  color: #FFF;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

/* 相关推荐 */
.related-dating-list {
  padding: 10rpx 0;
}

.related-dating-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}

.related-dating-item:last-child {
  border-bottom: none;
}

.related-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.related-dating-info {
  flex: 1;
}

.related-dating-title {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.related-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 10rpx;
}

.related-age {
  font-size: 24rpx;
  color: #FF6B95;
  background: rgba(255, 107, 149, 0.1);
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
}

.related-dating-meta {
  display: flex;
}

.related-meta-text {
  font-size: 24rpx;
  color: #999;
  margin-right: 16rpx;
}

/* 底部操作栏 */
.footer-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #FFF;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.action-icon {
  font-size: 40rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.icon-collected {
  color: #FF6B95;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

.contact-action {
  flex: 2;
  background: linear-gradient(135deg, #FF6B95, #FF8E7F);
  height: 70rpx;
  border-radius: 35rpx;
  margin: 0 20rpx;
}

.contact-action .action-text {
  color: #FFF;
  font-size: 28rpx;
  font-weight: 500;
}

/* 海报弹窗 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.poster-container {
  width: 80%;
  background: #FFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.poster-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.poster-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.poster-image-container {
  padding: 30rpx;
  display: flex;
  justify-content: center;
}

.poster-preview {
  width: 100%;
  border-radius: 8rpx;
}

.poster-footer {
  display: flex;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.poster-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
}

.save-btn {
  background: #f5f7fa;
  color: #666;
}

.share-btn {
  background: linear-gradient(135deg, #FF6B95, #FF8E7F);
  color: #FFF;
}

/* 悬浮海报按钮 */
.float-poster-btn {
  position: fixed;
  right: 30rpx;
  bottom: 140rpx;
  width: 100rpx;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 80;
}

.poster-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

.poster-text {
  font-size: 20rpx;
  color: #666;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* 海报Canvas */
.poster-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
}
</style>