<template>
  <view class="product-card" :style="cardStyle" @click="$emit('click', product)">
    <!-- 商品图片 -->
    <view class="product-image-container">
      <image class="product-image" :src="product.coverImage" mode="aspectFill"></image>
      <view class="product-tag" v-if="product.tag">{{ product.tag }}</view>
    </view>
    
    <!-- 商品信息 -->
    <view class="product-info">
      <view class="product-title">{{ product.title }}</view>
      
      <view class="shop-info" v-if="showShop">
        <image class="shop-logo" :src="product.shopLogo" mode="aspectFill"></image>
        <text class="shop-name">{{ product.shopName }}</text>
        <view class="distance" v-if="product.distance">{{ product.distance }}</view>
      </view>
      
      <view class="product-bottom">
        <view class="price-container">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{ product.price }}</text>
          <text class="price-original" v-if="product.originalPrice">¥{{ product.originalPrice }}</text>
        </view>
        
        <view class="sold-count" v-if="product.soldCount">已售{{ product.soldCount }}+</view>
      </view>
      
      <!-- 商品标签 -->
      <view class="product-labels" v-if="product.labels && product.labels.length > 0">
        <view 
          class="label-item"
          v-for="(label, index) in product.labels"
          :key="index"
          :style="{ backgroundColor: getLabelColor(label.type) }"
        >
          {{ label.text }}
        </view>
      </view>
    </view>
    
    <!-- 快捷操作按钮 -->
    <view class="action-btn" @click.stop="$emit('addToCart', product)" v-if="showActionBtn">
      <svg class="action-icon" viewBox="0 0 24 24" width="24" height="24">
        <path d="M12 9v6m-3-3h6" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
      </svg>
    </view>
  </view>
</template>

<script setup>
// 组件属性定义
const props = defineProps({
  product: {
    type: Object,
    required: true
  },
  showShop: {
    type: Boolean,
    default: true
  },
  showActionBtn: {
    type: Boolean,
    default: true
  },
  cardStyle: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
defineEmits(['click', 'addToCart']);

// 获取标签颜色
const getLabelColor = (type) => {
  switch(type) {
    case 'discount':
      return 'rgba(255,59,105,0.1)';
    case 'coupon':
      return 'rgba(255,149,0,0.1)';
    case 'new':
      return 'rgba(52,199,89,0.1)';
    case 'hot':
      return 'rgba(255,59,48,0.1)';
    default:
      return 'rgba(142,142,147,0.1)';
  }
};
</script>

<style lang="scss" scoped>
.product-card {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  margin-bottom: 20rpx;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  }
}

.product-image-container {
  width: 100%;
  height: 300rpx;
  position: relative;
  
  .product-image {
    width: 100%;
    height: 100%;
  }
  
  .product-tag {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    padding: 6rpx 16rpx;
    background-color: rgba(255,59,105,0.9);
    color: #FFFFFF;
    font-size: 22rpx;
    border-radius: 16rpx;
    font-weight: 500;
  }
}

.product-info {
  padding: 20rpx;
  
  .product-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 16rpx;
    line-height: 1.4;
    height: 80rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .shop-info {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    
    .shop-logo {
      width: 32rpx;
      height: 32rpx;
      border-radius: 16rpx;
      margin-right: 8rpx;
    }
    
    .shop-name {
      font-size: 24rpx;
      color: #8E8E93;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .distance {
      font-size: 24rpx;
      color: #8E8E93;
    }
  }
  
  .product-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .price-container {
      display: flex;
      align-items: baseline;
      
      .price-symbol {
        font-size: 24rpx;
        color: #FF3B69;
        margin-right: 4rpx;
      }
      
      .price-value {
        font-size: 32rpx;
        font-weight: 600;
        color: #FF3B69;
      }
      
      .price-original {
        font-size: 24rpx;
        color: #8E8E93;
        margin-left: 12rpx;
        text-decoration: line-through;
      }
    }
    
    .sold-count {
      font-size: 24rpx;
      color: #8E8E93;
    }
  }
  
  .product-labels {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16rpx;
    
    .label-item {
      padding: 4rpx 12rpx;
      border-radius: 6rpx;
      font-size: 20rpx;
      margin-right: 12rpx;
      margin-bottom: 8rpx;
      color: #FF3B69;
    }
  }
}

.action-btn {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(255,59,105,0.3);
  
  .action-icon {
    width: 32rpx;
    height: 32rpx;
    color: #FFFFFF;
  }
  
  &:active {
    transform: scale(0.9);
  }
}
</style> 