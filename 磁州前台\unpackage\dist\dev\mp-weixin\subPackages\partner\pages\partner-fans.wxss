/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-c08f443b, html.data-v-c08f443b, #app.data-v-c08f443b, .index-container.data-v-c08f443b {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.fans-container.data-v-c08f443b {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}
.stats-card.data-v-c08f443b {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.stats-item.data-v-c08f443b {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stats-num.data-v-c08f443b {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}
.stats-label.data-v-c08f443b {
  font-size: 24rpx;
  color: #999999;
}
.stats-divider.data-v-c08f443b {
  width: 1px;
  height: 60rpx;
  background-color: #eeeeee;
}
.filter-section.data-v-c08f443b {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}
.filter-tabs.data-v-c08f443b {
  display: flex;
  overflow-x: auto;
  margin-bottom: 20rpx;
}
.filter-tabs.data-v-c08f443b::-webkit-scrollbar {
  display: none;
}
.tab-item.data-v-c08f443b {
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  white-space: nowrap;
}
.tab-item.active.data-v-c08f443b {
  color: #1677FF;
  font-weight: 500;
}
.tab-item.active.data-v-c08f443b::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1677FF;
  border-radius: 2rpx;
}
.search-box.data-v-c08f443b {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}
.search-box .cuIcon-search.data-v-c08f443b {
  font-size: 32rpx;
  color: #999999;
  margin-right: 10rpx;
}
.search-box input.data-v-c08f443b {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}
.search-box .cuIcon-close.data-v-c08f443b {
  font-size: 32rpx;
  color: #999999;
  padding: 10rpx;
}
.fans-list.data-v-c08f443b {
  padding: 0 30rpx;
}
.fans-item.data-v-c08f443b {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.fans-avatar.data-v-c08f443b {
  position: relative;
  margin-right: 20rpx;
}
.fans-avatar image.data-v-c08f443b {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.fans-avatar .fans-badge.data-v-c08f443b {
  position: absolute;
  bottom: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9500, #FF6000);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  transform: scale(0.8);
  transform-origin: right bottom;
}
.fans-info.data-v-c08f443b {
  flex: 1;
}
.fans-name-row.data-v-c08f443b {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.fans-name.data-v-c08f443b {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-right: 10rpx;
}
.fans-tag.data-v-c08f443b {
  background-color: #e6f4ff;
  color: #1677FF;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
}
.fans-data.data-v-c08f443b {
  font-size: 24rpx;
  color: #999999;
}
.fans-data .join-time.data-v-c08f443b {
  margin-right: 20rpx;
}
.fans-data .contribution.data-v-c08f443b {
  color: #ff6000;
}
.fans-action .message-btn.data-v-c08f443b {
  background: #f0f7ff;
  color: #1677FF;
  font-size: 26rpx;
  min-width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 20rpx;
  border-radius: 30rpx;
  border: 1px solid #1677FF;
}
.empty-state.data-v-c08f443b {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}
.empty-state image.data-v-c08f443b {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}
.empty-state text.data-v-c08f443b {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 40rpx;
}
.empty-state .share-btn.data-v-c08f443b {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #1677FF, #0E5FD8);
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.load-more.data-v-c08f443b, .load-end.data-v-c08f443b {
  text-align: center;
  font-size: 26rpx;
  color: #999999;
  padding: 30rpx 0;
}
.share-modal.data-v-c08f443b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
.share-modal .modal-mask.data-v-c08f443b {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.share-modal .modal-content.data-v-c08f443b {
  width: 100%;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  position: relative;
  z-index: 1000;
}
.share-modal .modal-title.data-v-c08f443b {
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.share-modal .share-options.data-v-c08f443b {
  display: flex;
  flex-wrap: wrap;
  padding: 30rpx;
}
.share-modal .share-option.data-v-c08f443b {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}
.share-modal .share-option image.data-v-c08f443b {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 15rpx;
}
.share-modal .share-option text.data-v-c08f443b {
  font-size: 26rpx;
  color: #666666;
}
.share-modal .cancel-btn.data-v-c08f443b {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333333;
  border-top: 10rpx solid #f5f5f5;
}

/* 自定义导航栏样式 */
.custom-navbar.data-v-c08f443b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-title.data-v-c08f443b {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: "AlimamaShuHeiTi", sans-serif;
  text-align: center;
}
.navbar-right.data-v-c08f443b {
  width: 40rpx;
  height: 40rpx;
}
.navbar-left.data-v-c08f443b {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20;
  /* 确保在标题上层，可以被点击 */
}
.back-icon.data-v-c08f443b {
  width: 100%;
  height: 100%;
}
.safe-area-top.data-v-c08f443b {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}