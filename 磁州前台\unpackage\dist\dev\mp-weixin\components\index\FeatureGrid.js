"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "FeatureGrid",
  setup(__props) {
    function navigateTo(url) {
      if (!url)
        return;
      common_vendor.index.navigateTo({
        url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/index/FeatureGrid.vue:61", "页面跳转失败:", err);
          common_vendor.index.switchTab({
            url,
            fail: (err2) => {
              common_vendor.index.__f__("error", "at components/index/FeatureGrid.vue:66", "switchTab也失败:", err2);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
        }
      });
    }
    function goToSignIn() {
      common_vendor.index.navigateTo({
        url: "/subPackages/checkin/pages/points",
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/index/FeatureGrid.vue:82", "签到页面跳转失败:", err);
          common_vendor.index.showToast({
            title: "签到功能暂未开放",
            icon: "none"
          });
        }
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$4,
        b: common_vendor.o(($event) => navigateTo("/pages/red-packet/list")),
        c: common_assets._imports_1$58,
        d: common_vendor.o(goToSignIn),
        e: common_assets._imports_2$46,
        f: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/index/index")),
        g: common_assets._imports_3$40,
        h: common_vendor.o(($event) => navigateTo("/subPackages/activity/pages/city-events"))
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a3d9a803"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/index/FeatureGrid.js.map
