{"version": 3, "file": "group-orders.js", "sources": ["pages/user/group-orders.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9ncm91cC1vcmRlcnMudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"group-orders-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">团购订单</view>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 状态筛选 -->\r\n    <view class=\"filter-tabs\" :style=\"{ marginTop: (statusBarHeight + 44) + 'px' }\">\r\n      <view \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\" \r\n        class=\"tab-item\" \r\n        :class=\"{ active: currentTab === index }\"\r\n        @click=\"switchTab(index)\"\r\n      >\r\n        <text>{{ tab.name }}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 订单列表 -->\r\n    <scroll-view \r\n      scroll-y \r\n      class=\"orders-list\" \r\n      @scrolltolower=\"loadMore\" \r\n      refresher-enabled \r\n      :refresher-triggered=\"refreshing\" \r\n      @refresherrefresh=\"refresh\"\r\n    >\r\n      <view v-if=\"ordersList.length > 0\">\r\n        <view class=\"order-item\" v-for=\"item in ordersList\" :key=\"item.id\" @click=\"viewOrderDetail(item)\">\r\n          <view class=\"order-header\">\r\n            <view class=\"shop-info\">\r\n              <image class=\"shop-avatar\" :src=\"item.shopAvatar\" mode=\"aspectFill\"></image>\r\n              <text class=\"shop-name\">{{ item.shopName }}</text>\r\n              <image class=\"shop-arrow\" src=\"/static/images/tabbar/右箭头.png\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <view class=\"order-status\" :class=\"'status-' + item.status\">{{ getStatusText(item.status) }}</view>\r\n          </view>\r\n          \r\n          <view class=\"order-content\">\r\n            <image class=\"goods-image\" :src=\"item.goodsImage\" mode=\"aspectFill\"></image>\r\n            <view class=\"goods-info\">\r\n              <view class=\"goods-name\">{{ item.goodsName }}</view>\r\n              <view class=\"goods-spec\" v-if=\"item.goodsSpec\">{{ item.goodsSpec }}</view>\r\n              <view class=\"goods-price-count\">\r\n                <text class=\"goods-price\">¥{{ item.price }}</text>\r\n                <text class=\"goods-count\">x{{ item.count }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"order-footer\">\r\n            <view class=\"order-time\">下单时间：{{ item.createTime }}</view>\r\n            <view class=\"order-total\">\r\n              <text>共{{ item.count }}件商品</text>\r\n              <text class=\"total-price\">实付：<text class=\"price-num\">¥{{ item.totalAmount }}</text></text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"order-actions\">\r\n            <view class=\"action-btn cancel-btn\" v-if=\"item.status === 1\" @click.stop=\"cancelOrder(item)\">取消订单</view>\r\n            <view class=\"action-btn pay-btn\" v-if=\"item.status === 1\" @click.stop=\"payOrder(item)\">立即付款</view>\r\n            <view class=\"action-btn\" v-if=\"item.status === 2\" @click.stop=\"checkDelivery(item)\">查看配送</view>\r\n            <view class=\"action-btn\" v-if=\"item.status === 3\" @click.stop=\"confirmReceive(item)\">确认收货</view>\r\n            <view class=\"action-btn\" v-if=\"item.status === 4\" @click.stop=\"writeReview(item)\">评价</view>\r\n            <view class=\"action-btn\" v-if=\"item.status === 4 || item.status === 5\" @click.stop=\"deleteOrder(item)\">删除订单</view>\r\n            <view class=\"action-btn contact-btn\" @click.stop=\"contactShop(item)\">联系商家</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view class=\"empty-state\" v-if=\"ordersList.length === 0 && !loading\">\r\n        <image src=\"/static/images/tabbar/空状态.png\" class=\"empty-icon\"></image>\r\n        <view class=\"empty-text\">暂无团购订单</view>\r\n        <view class=\"empty-subtext\">去看看有哪些优惠的团购活动吧</view>\r\n        <view class=\"empty-btn\" @click=\"goToGroupBuy\">浏览团购</view>\r\n      </view>\r\n      \r\n      <!-- 加载状态 -->\r\n      <view class=\"loading-state\" v-if=\"loading && !refreshing\">\r\n        <view class=\"loading-icon\"></view>\r\n        <text>加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 加载完成 -->\r\n      <view class=\"load-all\" v-if=\"loadAll && ordersList.length > 0\">\r\n        <text>已加载全部订单</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20,\r\n      currentTab: 0,\r\n      tabs: [\r\n        { name: '全部' },\r\n        { name: '待付款' },\r\n        { name: '待发货' },\r\n        { name: '待收货' },\r\n        { name: '已完成' }\r\n      ],\r\n      ordersList: [],\r\n      page: 1,\r\n      limit: 10,\r\n      loading: false,\r\n      refreshing: false,\r\n      loadAll: false\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 获取状态栏高度\r\n    const sysInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = sysInfo.statusBarHeight;\r\n    \r\n    // 加载订单数据\r\n    this.loadOrders();\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 切换选项卡\r\n    switchTab(index) {\r\n      if (this.currentTab !== index) {\r\n        this.currentTab = index;\r\n        this.refresh();\r\n      }\r\n    },\r\n    \r\n    // 获取状态文字\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 1: return '待付款';\r\n        case 2: return '待发货';\r\n        case 3: return '待收货';\r\n        case 4: return '已完成';\r\n        case 5: return '已取消';\r\n        default: return '未知状态';\r\n      }\r\n    },\r\n    \r\n    // 加载订单数据\r\n    loadOrders() {\r\n      if (this.loading || this.loadAll) return;\r\n      \r\n      this.loading = true;\r\n      \r\n      // 模拟请求\r\n      setTimeout(() => {\r\n        // 构造请求参数\r\n        const params = {\r\n          page: this.page,\r\n          limit: this.limit,\r\n          status: this.currentTab === 0 ? '' : this.currentTab\r\n        };\r\n        \r\n        // 模拟数据\r\n        const mockData = this.getMockOrdersData();\r\n        let filteredData = mockData;\r\n        \r\n        // 根据状态筛选\r\n        if (this.currentTab > 0) {\r\n          filteredData = mockData.filter(item => item.status === this.currentTab);\r\n        }\r\n        \r\n        // 更新数据\r\n        if (this.page === 1) {\r\n          this.ordersList = filteredData;\r\n        } else {\r\n          this.ordersList = [...this.ordersList, ...filteredData];\r\n        }\r\n        \r\n        // 判断是否加载完全部\r\n        this.loadAll = true; // 模拟数据全部加载完\r\n        \r\n        this.loading = false;\r\n        this.refreshing = false;\r\n        \r\n        // 页码+1\r\n        if (!this.loadAll) {\r\n          this.page++;\r\n        }\r\n      }, 800);\r\n    },\r\n    \r\n    // 刷新\r\n    refresh() {\r\n      this.refreshing = true;\r\n      this.page = 1;\r\n      this.loadAll = false;\r\n      this.loadOrders();\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      this.loadOrders();\r\n    },\r\n    \r\n    // 查看订单详情\r\n    viewOrderDetail(order) {\r\n      uni.navigateTo({\r\n        url: `/pages/groupbuy/order-detail?id=${order.id}`\r\n      });\r\n    },\r\n    \r\n    // 取消订单\r\n    cancelOrder(order) {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确定要取消该订单吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showLoading({\r\n              title: '处理中...'\r\n            });\r\n            \r\n            // 模拟请求\r\n            setTimeout(() => {\r\n              uni.hideLoading();\r\n              \r\n              // 更新本地数据\r\n              const index = this.ordersList.findIndex(item => item.id === order.id);\r\n              if (index !== -1) {\r\n                this.ordersList[index].status = 5; // 已取消\r\n              }\r\n              \r\n              uni.showToast({\r\n                title: '订单已取消',\r\n                icon: 'success'\r\n              });\r\n            }, 500);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 支付订单\r\n    payOrder(order) {\r\n      uni.navigateTo({\r\n        url: `/pages/pay/index?orderId=${order.id}&amount=${order.totalAmount}`\r\n      });\r\n    },\r\n    \r\n    // 查看配送\r\n    checkDelivery(order) {\r\n      uni.showToast({\r\n        title: '暂无配送信息',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    // 确认收货\r\n    confirmReceive(order) {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确认已收到商品吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showLoading({\r\n              title: '处理中...'\r\n            });\r\n            \r\n            // 模拟请求\r\n            setTimeout(() => {\r\n              uni.hideLoading();\r\n              \r\n              // 更新本地数据\r\n              const index = this.ordersList.findIndex(item => item.id === order.id);\r\n              if (index !== -1) {\r\n                this.ordersList[index].status = 4; // 已完成\r\n              }\r\n              \r\n              uni.showToast({\r\n                title: '确认收货成功',\r\n                icon: 'success'\r\n              });\r\n            }, 500);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 评价\r\n    writeReview(order) {\r\n      uni.navigateTo({\r\n        url: `/pages/review/write?orderId=${order.id}`\r\n      });\r\n    },\r\n    \r\n    // 删除订单\r\n    deleteOrder(order) {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确定要删除该订单吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showLoading({\r\n              title: '处理中...'\r\n            });\r\n            \r\n            // 模拟请求\r\n            setTimeout(() => {\r\n              uni.hideLoading();\r\n              \r\n              // 从列表中移除\r\n              const index = this.ordersList.findIndex(item => item.id === order.id);\r\n              if (index !== -1) {\r\n                this.ordersList.splice(index, 1);\r\n              }\r\n              \r\n              uni.showToast({\r\n                title: '删除成功',\r\n                icon: 'success'\r\n              });\r\n            }, 500);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 联系商家\r\n    contactShop(order) {\r\n      uni.makePhoneCall({\r\n        phoneNumber: order.shopPhone || '10086',\r\n        fail: () => {\r\n          uni.showToast({\r\n            title: '拨打电话失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 前往团购页面\r\n    goToGroupBuy() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/activity-showcase/pages/group-buy/index'\r\n      });\r\n    },\r\n    \r\n    // 模拟订单数据\r\n    getMockOrdersData() {\r\n      return [\r\n        {\r\n          id: '1001',\r\n          shopName: '磁州烧饼店',\r\n          shopAvatar: '/static/images/shop/shop1.jpg',\r\n          shopPhone: '13812345678',\r\n          goodsName: '正宗磁州烧饼 买二送一',\r\n          goodsImage: '/static/images/product/food1.jpg',\r\n          goodsSpec: '原味 3个装',\r\n          price: 19.9,\r\n          count: 1,\r\n          totalAmount: 19.9,\r\n          status: 1, // 待付款\r\n          createTime: '2023-09-15 14:30'\r\n        },\r\n        {\r\n          id: '1002',\r\n          shopName: '水果鲜生',\r\n          shopAvatar: '/static/images/shop/shop2.jpg',\r\n          shopPhone: '13998765432',\r\n          goodsName: '精品水果礼盒 新鲜当季水果',\r\n          goodsImage: '/static/images/product/food2.jpg',\r\n          goodsSpec: '精品混合装 3kg',\r\n          price: 59.9,\r\n          count: 1,\r\n          totalAmount: 59.9,\r\n          status: 2, // 待发货\r\n          createTime: '2023-09-14 10:15'\r\n        },\r\n        {\r\n          id: '1003',\r\n          shopName: '老北京小吃',\r\n          shopAvatar: '/static/images/shop/shop3.jpg',\r\n          shopPhone: '13756781234',\r\n          goodsName: '手工制作老北京糖葫芦',\r\n          goodsImage: '/static/images/product/food3.jpg',\r\n          goodsSpec: '山楂味 10串',\r\n          price: 15.9,\r\n          count: 2,\r\n          totalAmount: 31.8,\r\n          status: 3, // 待收货\r\n          createTime: '2023-09-13 16:45'\r\n        },\r\n        {\r\n          id: '1004',\r\n          shopName: '磁州特色小吃',\r\n          shopAvatar: '/static/images/shop/shop4.jpg',\r\n          shopPhone: '13612345678',\r\n          goodsName: '特色小吃套餐 多种口味',\r\n          goodsImage: '/static/images/product/food4.jpg',\r\n          goodsSpec: '经典6件套',\r\n          price: 39.9,\r\n          count: 1,\r\n          totalAmount: 39.9,\r\n          status: 4, // 已完成\r\n          createTime: '2023-09-10 09:20'\r\n        },\r\n        {\r\n          id: '1005',\r\n          shopName: '磁州烧饼店',\r\n          shopAvatar: '/static/images/shop/shop1.jpg',\r\n          shopPhone: '13812345678',\r\n          goodsName: '正宗磁州烧饼 买二送一',\r\n          goodsImage: '/static/images/product/food1.jpg',\r\n          goodsSpec: '芝麻味 3个装',\r\n          price: 21.9,\r\n          count: 1,\r\n          totalAmount: 21.9,\r\n          status: 5, // 已取消\r\n          createTime: '2023-09-08 11:30'\r\n        }\r\n      ];\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.group-orders-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  background: linear-gradient(135deg, #1677FF, #0052CC);\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  z-index: 100;\r\n}\r\n\r\n.navbar-left {\r\n  width: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.navbar-right {\r\n  width: 40px;\r\n}\r\n\r\n/* 状态筛选 */\r\n.filter-tabs {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  border-bottom: 1px solid #F0F0F0;\r\n  z-index: 99;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  height: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  color: #666;\r\n  position: relative;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #1677FF;\r\n  font-weight: 500;\r\n}\r\n\r\n.tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 20px;\r\n  height: 3px;\r\n  background-color: #1677FF;\r\n  border-radius: 2px;\r\n}\r\n\r\n/* 订单列表 */\r\n.orders-list {\r\n  position: absolute;\r\n  top: 88px;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  padding: 10px;\r\n}\r\n\r\n.order-item {\r\n  background-color: #FFFFFF;\r\n  border-radius: 12px;\r\n  margin-bottom: 15px;\r\n  padding: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #F5F5F5;\r\n}\r\n\r\n.shop-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.shop-avatar {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.shop-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-right: 5px;\r\n}\r\n\r\n.shop-arrow {\r\n  width: 14px;\r\n  height: 14px;\r\n}\r\n\r\n.order-status {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-1 {\r\n  color: #FF9500; /* 待付款 */\r\n}\r\n\r\n.status-2 {\r\n  color: #007AFF; /* 待发货 */\r\n}\r\n\r\n.status-3 {\r\n  color: #34C759; /* 待收货 */\r\n}\r\n\r\n.status-4 {\r\n  color: #8E8E93; /* 已完成 */\r\n}\r\n\r\n.status-5 {\r\n  color: #8E8E93; /* 已取消 */\r\n}\r\n\r\n.order-content {\r\n  display: flex;\r\n  padding: 15px 0;\r\n  border-bottom: 1px solid #F5F5F5;\r\n}\r\n\r\n.goods-image {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 6px;\r\n  margin-right: 12px;\r\n}\r\n\r\n.goods-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 15px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 6px;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-spec {\r\n  font-size: 13px;\r\n  color: #999;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.goods-price-count {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.goods-price {\r\n  font-size: 15px;\r\n  color: #FF3B30;\r\n  font-weight: 500;\r\n}\r\n\r\n.goods-count {\r\n  font-size: 13px;\r\n  color: #999;\r\n}\r\n\r\n.order-footer {\r\n  padding: 12px 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.order-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.order-total {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-size: 13px;\r\n  color: #666;\r\n}\r\n\r\n.total-price {\r\n  font-size: 13px;\r\n  color: #333;\r\n}\r\n\r\n.price-num {\r\n  font-size: 16px;\r\n  color: #FF3B30;\r\n  font-weight: 500;\r\n}\r\n\r\n.order-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 12px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  height: 30px;\r\n  padding: 0 12px;\r\n  border-radius: 15px;\r\n  font-size: 13px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 10px;\r\n  background-color: #F5F5F5;\r\n  color: #666;\r\n  border: 1px solid #E5E5E5;\r\n}\r\n\r\n.cancel-btn {\r\n  color: #666;\r\n}\r\n\r\n.pay-btn {\r\n  background-color: #1677FF;\r\n  color: #FFF;\r\n  border: none;\r\n}\r\n\r\n.contact-btn {\r\n  border: 1px solid #1677FF;\r\n  color: #1677FF;\r\n  background-color: #FFF;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding-top: 100px;\r\n}\r\n\r\n.empty-icon {\r\n  width: 120px;\r\n  height: 120px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 16px;\r\n  color: #999;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.empty-subtext {\r\n  font-size: 14px;\r\n  color: #AAAAAA;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.empty-btn {\r\n  width: 120px;\r\n  height: 40px;\r\n  background: linear-gradient(135deg, #1677FF, #0052CC);\r\n  color: #FFF;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-state {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 50px;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin-right: 10px;\r\n  border: 2px solid #f3f3f3;\r\n  border-top: 2px solid #1677FF;\r\n  border-radius: 50%;\r\n  animation: spin 0.8s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 加载完成 */\r\n.load-all {\r\n  text-align: center;\r\n  padding: 15px 0;\r\n  color: #999;\r\n  font-size: 13px;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/user/group-orders.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAmGA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,EAAE,MAAM,KAAM;AAAA,QACd,EAAE,MAAM,MAAO;AAAA,QACf,EAAE,MAAM,MAAO;AAAA,QACf,EAAE,MAAM,MAAO;AAAA,QACf,EAAE,MAAM,MAAM;AAAA,MACf;AAAA,MACD,YAAY,CAAE;AAAA,MACd,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,SAAS;AAEP,UAAM,UAAUA,oBAAI;AACpB,SAAK,kBAAkB,QAAQ;AAG/B,SAAK,WAAU;AAAA,EAChB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,UAAI,KAAK,eAAe,OAAO;AAC7B,aAAK,aAAa;AAClB,aAAK,QAAO;AAAA,MACd;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,cAAQ,QAAM;AAAA,QACZ,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MAClB;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACX,UAAI,KAAK,WAAW,KAAK;AAAS;AAElC,WAAK,UAAU;AAGf,iBAAW,MAAM;AAEA,SAAA;AAAA,UACb,MAAM,KAAK;AAAA,UACX,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK,eAAe,IAAI,KAAK,KAAK;AAAA,QAC3C;AAGD,cAAM,WAAW,KAAK;AACtB,YAAI,eAAe;AAGnB,YAAI,KAAK,aAAa,GAAG;AACvB,yBAAe,SAAS,OAAO,UAAQ,KAAK,WAAW,KAAK,UAAU;AAAA,QACxE;AAGA,YAAI,KAAK,SAAS,GAAG;AACnB,eAAK,aAAa;AAAA,eACb;AACL,eAAK,aAAa,CAAC,GAAG,KAAK,YAAY,GAAG,YAAY;AAAA,QACxD;AAGA,aAAK,UAAU;AAEf,aAAK,UAAU;AACf,aAAK,aAAa;AAGlB,YAAI,CAAC,KAAK,SAAS;AACjB,eAAK;AAAA,QACP;AAAA,MACD,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,UAAU;AACR,WAAK,aAAa;AAClB,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,WAAU;AAAA,IAChB;AAAA;AAAA,IAGD,WAAW;AACT,WAAK,WAAU;AAAA,IAChB;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mCAAmC,MAAM,EAAE;AAAA,MAClD,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,YACT,CAAC;AAGD,uBAAW,MAAM;AACfA,4BAAG,MAAC,YAAW;AAGf,oBAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACpE,kBAAI,UAAU,IAAI;AAChB,qBAAK,WAAW,KAAK,EAAE,SAAS;AAAA,cAClC;AAEAA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACF,GAAE,GAAG;AAAA,UACR;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,SAAS,OAAO;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,MAAM,EAAE,WAAW,MAAM,WAAW;AAAA,MACvE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,OAAO;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,OAAO;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,YACT,CAAC;AAGD,uBAAW,MAAM;AACfA,4BAAG,MAAC,YAAW;AAGf,oBAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACpE,kBAAI,UAAU,IAAI;AAChB,qBAAK,WAAW,KAAK,EAAE,SAAS;AAAA,cAClC;AAEAA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACF,GAAE,GAAG;AAAA,UACR;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,+BAA+B,MAAM,EAAE;AAAA,MAC9C,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,YACT,CAAC;AAGD,uBAAW,MAAM;AACfA,4BAAG,MAAC,YAAW;AAGf,oBAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AACpE,kBAAI,UAAU,IAAI;AAChB,qBAAK,WAAW,OAAO,OAAO,CAAC;AAAA,cACjC;AAEAA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACF,GAAE,GAAG;AAAA,UACR;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjBA,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa,MAAM,aAAa;AAAA,QAChC,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClB,aAAO;AAAA,QACL;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA;AAAA,UACR,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA;AAAA,UACR,YAAY;AAAA,QACd;AAAA;IAEJ;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3aA,GAAG,WAAW,eAAe;"}