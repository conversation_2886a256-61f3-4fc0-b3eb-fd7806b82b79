<template>
	<view class="business-page">
		<!-- 顶部蓝色背景 -->
		<view class="top-blue-bg"></view>
		
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 系统标题栏 -->
		<view class="navbar">
			<text class="navbar-title">同城商圈</text>
		</view>
		
		<!-- 轮播图 -->
		<view class="swiper-container">
			<swiper class="banner-swiper" circular :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500" indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#ffffff">
				<swiper-item v-for="(banner, index) in bannerList" :key="index">
					<image class="banner-image" :src="banner.image" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 曝光量和入驻数 -->
		<view class="stats-bar">
			<view class="stat-item">
				<image class="stat-icon" src="/static/images/tabbar/喇叭.png"></image>
			<text class="exposure-text">曝光量: 123.4万次</text>
			</view>
			<text class="stat-divider">|</text>
			<view class="stat-item">
			<text class="in-row">已入驻: 567家</text>
			</view>
		</view>
		
		<!-- 商家推荐模块 -->
		<view class="merchant-recommend-section card-section fade-in">
			<view class="section-header">
				<view class="section-title-wrap">
					<view class="section-bar"></view>
					<text class="section-title">商家推荐</text>
				</view>
				<view class="section-more" @click="navigateTo('/pages/business/list')">
					<text class="more-text">全部</text>
					<text class="more-icon">&gt;</text>
				</view>
			</view>
			<swiper class="merchant-swiper" vertical :current="currentMerchantPage" @change="onMerchantPageChange" circular>
				<swiper-item v-for="pageIndex in Math.ceil(recommendBusinessList.length / 2)" :key="'page-'+pageIndex">
					<view class="merchant-swiper-page">
						<view 
							class="merchant-item" 
							v-for="item in getMerchantsForPage(pageIndex-1)" 
							:key="item.id"
							@click="navigateTo(`/pages/business/shop-detail?id=${item.id}`)"
						>
							<view class="merchant-card">
								<image class="merchant-logo" :src="item.logo" mode="aspectFill"></image>
								<view class="merchant-info">
									<view class="merchant-name-wrap">
										<text class="merchant-name">{{item.name}}</text>
									</view>
									<text class="merchant-desc">{{item.description}}</text>
									<text class="merchant-category">{{item.category}}</text>
								</view>
								<view class="merchant-collect-btn" @click.stop="followBusiness(item.id)">
									<text class="collect-btn-text">{{item.isFollowed ? '已收藏' : '收藏'}}</text>
								</view>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
			<view class="merchant-indicators">
				<view class="merchant-dot" 
					v-for="(dot, i) in Math.ceil(recommendBusinessList.length / 2)" 
					:key="i" 
					:class="{ active: currentMerchantPage === i }"
					@click="changeMerchantPage(i)"></view>
			</view>
		</view>
		
		<!-- 商家入驻卡片 -->
		<view class="join-card" @click="navigateToJoin">
			<view class="join-card-left">
				<view class="join-icon-wrap">
					<image class="join-icon" src="/static/images/tabbar/入驻卡片.png" />
				</view>
				<view class="join-info">
					<text class="join-title">商家入驻</text>
					<text class="join-desc">免费申请入驻，获取更多商机</text>
				</view>
			</view>
			<button class="join-btn" @click.stop="navigateToJoin">立即申请</button>
			<view class="hot-badge">热门</view>
		</view>
		
		<!-- 悬浮按钮 -->
		<FabButtons 
			pageName="business" 
			:pageInfo="{
				title: '磁州生活网 - 同城商圈',
				path: '/pages/business/business',
				imageUrl: '/static/images/banner/banner-1.png'
			}" 
		/>
		
		<!-- 分类宫格 -->
		<view class="category-grid">
			<!-- 搜索框移到分类宫格内部上方 -->
			<view class="search-container-inner">
				<view class="search-box">
					<input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索或筛选商家" confirm-type="search" />
					<image class="search-icon" src="/static/images/tabbar/放大镜.png"></image>
				</view>
			</view>
			
			<view class="category-row">
				<view class="category-item" @click="navigateToFilter('房产楼盘')">
					<image class="category-icon" src="/static/images/tabbar/房产楼盘.png"></image>
					<text class="category-name">房产楼盘</text>
				</view>
				<view class="category-item" @click="navigateToFilter('美食小吃')">
					<image class="category-icon" src="/static/images/tabbar/美食小吃.png"></image>
					<text class="category-name">美食小吃</text>
				</view>
				<view class="category-item" @click="navigateToFilter('装修家居')">
					<image class="category-icon" src="/static/images/tabbar/装修家居.png"></image>
					<text class="category-name">装修家居</text>
				</view>
				<view class="category-item" @click="navigateToFilter('母婴专区')">
					<image class="category-icon" src="/static/images/tabbar/母婴专区.png"></image>
					<text class="category-name">母婴专区</text>
					<view class="hot-tag">热门</view>
				</view>
			</view>
			
			<view class="category-row">
				<view class="category-item" @click="navigateToFilter('休闲娱乐')">
					<image class="category-icon" src="/static/images/tabbar/休闲娱乐.png"></image>
					<text class="category-name">休闲娱乐</text>
				</view>
				<view class="category-item" @click="navigateToFilter('到家服务')">
					<image class="category-icon" src="/static/images/tabbar/商到家服务.png"></image>
					<text class="category-name">到家服务</text>
				</view>
				<view class="category-item" @click="navigateToFilter('开锁换锁')">
					<image class="category-icon" src="/static/images/tabbar/开锁换锁.png"></image>
					<text class="category-name">开锁换锁</text>
				</view>
				<view class="category-item" @click="navigateToFilter('数码通讯')">
					<image class="category-icon" src="/static/images/tabbar/数码通讯.png"></image>
					<text class="category-name">数码通讯</text>
				</view>
			</view>
			
			<view class="category-row">
				<view class="category-item" @click="navigateToFilter('车辆服务')">
					<image class="category-icon" src="/static/images/tabbar/商车辆服务.png"></image>
					<text class="category-name">车辆服务</text>
				</view>
				<view class="category-item" @click="navigateToFilter('教育培训')">
					<image class="category-icon" src="/static/images/tabbar/商教育培训.png"></image>
					<text class="category-name">教育培训</text>
				</view>
				<view class="category-item" @click="navigateToFilter('婚纱摄影')">
					<image class="category-icon" src="/static/images/tabbar/婚纱摄影.png"></image>
					<text class="category-name">婚纱摄影</text>
				</view>
				<view class="category-item" @click="navigateToFilter('农林牧渔')">
					<image class="category-icon" src="/static/images/tabbar/农林牧渔.png"></image>
					<text class="category-name">农林牧渔</text>
				</view>
			</view>
			
			<view class="category-row">
				<view class="category-item" @click="navigateToFilter('广告传媒')">
					<image class="category-icon" src="/static/images/tabbar/广告传媒.png"></image>
					<text class="category-name">广告传媒</text>
				</view>
				<view class="category-item" @click="navigateToFilter('其他行业')">
					<image class="category-icon" src="/static/images/tabbar/其他.png"></image>
					<text class="category-name">其他行业</text>
				</view>
				<view class="category-item"></view>
				<view class="category-item"></view>
			</view>
		</view>
		
		<!-- 标签页切换 -->
		<view class="tab-bar">
			<view class="tab-item" :class="{active: currentTab === 0}" @click="changeTab(0)">
				<text class="tab-text">推荐</text>
				<view class="tab-line" v-if="currentTab === 0"></view>
			</view>
			<view class="tab-item" :class="{active: currentTab === 1}" @click="changeTab(1)">
				<text class="tab-text">新入</text>
				<view class="tab-line" v-if="currentTab === 1"></view>
			</view>
			<view class="tab-item" :class="{active: currentTab === 2}" @click="changeTab(2)">
				<text class="tab-text">附近</text>
				<view class="tab-line" v-if="currentTab === 2"></view>
			</view>
		</view>
		
		<!-- 商家列表 -->
		<view class="business-list">
			<view class="business-item" v-for="business in businessList" :key="business.id" @click="navigateToShopDetail(business.id)">
				<image class="business-logo" :src="business.logo"></image>
				<view class="business-info">
					<text class="business-name">{{ business.name }}</text>
					<text class="business-desc">{{ business.description }}</text>
					<!-- 添加消费红包标识 -->
					<view class="business-tags">
						<view class="business-tag red-packet-tag" v-if="business.hasConsumeRedPacket">
							<image class="tag-icon" src="/static/images/red-packet-icon.png"></image>
							<text class="tag-text">消费满100元抽红包</text>
						</view>
					</view>
				</view>
				<button class="follow-btn" @click.stop="followBusiness(business.id)">+ 关注</button>
			</view>
		</view>
	</view>
</template>

<script>
	import FabButtons from '@/components/FabButtons.vue'
	export default {
		data() {
			return {
				currentTab: 0,
				statusBarHeight: 20,
				searchKeyword: '',
				displayItems: 2, // 改为显示2个，通过margin显示部分第3个
				currentIndex: 0, // 当前显示的轮播项索引
				resetTimer: null, // 用于轮播重置的定时器
				locationAuthChecked: false, // 是否已检查过位置权限
				currentMerchantPage: 0, // 当前商家推荐页
				merchantAutoplayTimer: null, // 商家自动轮播定时器
				bannerList: [
					{ image: '/static/images/banner/banner-1.png' },
					{ image: '/static/images/banner/banner-2.png' },
					{ image: '/static/images/banner/banner-3.jpg' }
				],
				categories: [
					{ name: '房产楼盘', icon: '/static/images/tabbar/房产楼盘.png' },
					{ name: '美食小吃', icon: '/static/images/tabbar/美食小吃.png' },
					{ name: '装修家居', icon: '/static/images/tabbar/装修家居.png' },
					{ name: '母婴专区', icon: '/static/images/tabbar/母婴专区.png', hot: true },
					{ name: '休闲娱乐', icon: '/static/images/tabbar/休闲娱乐.png' },
					{ name: '商到家服务', icon: '/static/images/tabbar/商到家服务.png' },
					{ name: '开锁换锁', icon: '/static/images/tabbar/开锁换锁.png' },
					{ name: '数码通讯', icon: '/static/images/tabbar/数码通讯.png' },
					{ name: '商车辆服务', icon: '/static/images/tabbar/商车辆服务.png' },
					{ name: '教育培训', icon: '/static/images/tabbar/商教育培训.png' },
					{ name: '婚纱摄影', icon: '/static/images/tabbar/婚纱摄影.png' },
					{ name: '农林牧渔', icon: '/static/images/tabbar/农林牧渔.png' },
					{ name: '广告传媒', icon: '/static/images/tabbar/广告传媒.png' },
					{ name: '其他行业', icon: '/static/images/tabbar/其他.png' }
				],
				recommendBusinessList: [
					{
						id: "1",
						logo: '/static/images/cizhou.png',
						name: '五分利电器',
						description: '家电全网调货',
						category: '数码电器',
						isFollowed: false
					},
					{
						id: "2",
						logo: '/static/images/cizhou.png',
						name: '金鼎家居',
						description: '全屋定制服务',
						category: '家居家装',
						isFollowed: false
					},
					{
						id: "3",
						logo: '/static/images/cizhou.png',
						name: '鲜丰水果',
						description: '新鲜水果配送',
						category: '生鲜果蔬',
						isFollowed: false
					},
					{
						id: "4",
						logo: '/static/images/cizhou.png',
						name: '磁州书院',
						description: '综合文化教育',
						category: '文化教育',
						isFollowed: false
					},
					{
						id: "5",
						logo: '/static/images/cizhou.png',
						name: '康美大药房',
						description: '全天24小时营业',
						category: '医疗健康',
						isFollowed: false
					},
					{
						id: "6",
						logo: '/static/images/cizhou.png',
						name: '聚福楼',
						description: '特色川菜',
						category: '餐饮美食',
						isFollowed: false
					},
					{
						id: "7",
						logo: '/static/images/cizhou.png',
						name: '速达物流',
						description: '同城急速配送',
						category: '物流快递',
						isFollowed: false
					},
					{
						id: "8",
						logo: '/static/images/cizhou.png',
						name: '美丽人生',
						description: '专业美容美发',
						category: '美容美发',
						isFollowed: false
					}
				],
				businessList: [
					{
						id: "1",
						logo: '/static/images/cizhou.png',
						name: '五分利电器',
						description: '家电全网调货，全场特价，送货上门',
						category: '数码电器',
						scale: '10-20人',
						hasConsumeRedPacket: true
					},
					{
						id: "2",
						logo: '/static/images/cizhou.png',
						name: '金鼎家居',
						description: '全屋定制、软装搭配、设计施工一站式服务',
						category: '家居家装',
						scale: '20-50人',
						hasConsumeRedPacket: true
					},
					{
						id: "3",
						logo: '/static/images/cizhou.png',
						name: '鲜丰水果',
						description: '新鲜水果，每日直采，支持微信下单，全城配送',
						category: '生鲜果蔬',
						scale: '5-10人',
						hasConsumeRedPacket: true
					},
					{
						id: "4",
						logo: '/static/images/cizhou.png',
						name: '磁州书院',
						description: '综合性文化教育机构，少儿教育、成人培训、艺术培训',
						category: '文化教育',
						scale: '10-15人',
						hasConsumeRedPacket: true
					},
					{
						id: "5",
						logo: '/static/images/cizhou.png',
						name: '康美大药房',
						description: '全天24小时营业，医保定点药房，送药上门服务',
						category: '医疗健康',
						scale: '15-20人',
						hasConsumeRedPacket: true
					}
				]
			}
		},
		onLoad() {
			// 获取状态栏高度
			uni.getSystemInfo({
				success: (res) => {
					this.statusBarHeight = res.statusBarHeight;
					
					// 固定显示2个
					this.displayItems = 2;
				}
			});
			
			// 检查位置权限
			this.checkLocationAuth();
			
			// 启动商家推荐自动轮播
			this.startMerchantAutoplay();
		},
		onShow() {
			// 页面显示时重新启动自动轮播
			this.startMerchantAutoplay();
		},
		onHide() {
			// 页面隐藏时停止自动轮播
			this.stopMerchantAutoplay();
		},
		onUnload() {
			// 页面卸载时停止自动轮播
			this.stopMerchantAutoplay();
		},
		onShareAppMessage() {
			// 自定义转发内容
			return {
				title: '磁县商家免费入驻，多种营销活动，助力商家起飞！',
				path: '/pages/business/business'
			}
		},
		methods: {
			// 检查位置权限
			checkLocationAuth() {
				// 检查是否已经存储过位置权限
				uni.getStorage({
					key: 'locationAuthChecked',
					success: () => {
						// 已经获取过权限，不再提示
						this.locationAuthChecked = true;
						console.log('已获取过位置权限，不再请求');
					},
					fail: () => {
						// 没有获取过权限，首次请求
						this.requestLocationAuth();
					}
				});
			},
			
			// 请求位置权限
			requestLocationAuth() {
				uni.getSetting({
					success: (res) => {
						// 检查是否已授权位置
						if (!res.authSetting['scope.userLocation']) {
							uni.authorize({
								scope: 'scope.userLocation',
								success: () => {
									// 设置已获取过权限的标记
									this.setLocationAuthChecked();
									// 获取位置
									this.getLocation();
								},
								fail: () => {
									// 即使用户拒绝，也记录已经请求过权限
									this.setLocationAuthChecked();
									console.log('用户拒绝了位置权限');
								}
							});
						} else {
							// 已经有权限，直接获取位置
							this.setLocationAuthChecked();
							this.getLocation();
						}
					}
				});
			},
			
			// 设置位置权限已检查标记
			setLocationAuthChecked() {
				// 存储到本地，避免再次提示
				uni.setStorage({
					key: 'locationAuthChecked',
					data: true,
					success: () => {
						this.locationAuthChecked = true;
						console.log('已记录位置权限状态');
					}
				});
			},
			
			// 获取位置信息
			getLocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						// 存储位置信息
						const location = {
							latitude: res.latitude,
							longitude: res.longitude
						};
						
						uni.setStorage({
							key: 'userLocation',
							data: location
						});
						
						console.log('已获取用户位置', location);
					},
					fail: (err) => {
						console.log('获取位置失败', err);
					}
				});
			},
			
			// 手动获取位置（用户主动点击时）
			refreshLocation() {
				uni.showLoading({
					title: '获取位置中...'
				});
				
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						const location = {
							latitude: res.latitude,
							longitude: res.longitude
						};
						
						uni.setStorage({
							key: 'userLocation',
							data: location
						});
						
						uni.showToast({
							title: '位置已更新',
							icon: 'success'
						});
						
						// 如果当前在"附近"标签页，需要刷新数据
						if (this.currentTab === 2) {
							// 这里添加刷新附近商家的逻辑
							// this.loadNearbyBusinesses();
						}
					},
					fail: () => {
						uni.showModal({
							title: '位置获取失败',
							content: '请检查是否授予位置权限，或稍后再试',
							showCancel: false
						});
					},
					complete: () => {
						uni.hideLoading();
					}
				});
			},
			
			changeTab(index) {
				this.currentTab = index;
				
				// 如果切换到附近标签，检查是否有位置信息
				if (index === 2) {
					uni.getStorage({
						key: 'userLocation',
						success: () => {
							// 有位置信息，可以加载附近商家
							// this.loadNearbyBusinesses();
						},
						fail: () => {
							// 没有位置信息，提示用户
							uni.showModal({
								title: '位置信息缺失',
								content: '需要位置信息才能查看附近商家，是否获取位置？',
								success: (res) => {
									if (res.confirm) {
										this.refreshLocation();
									}
								}
							});
						}
					});
				}
			},
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			navigateToJoin() {
				uni.navigateTo({
					url: '/pages/business/join'
				});
			},
			navigateToSearch() {
				// 携带搜索关键词跳转到筛选页面
				uni.navigateTo({
					url: '/pages/business/filter?keyword=' + encodeURIComponent(this.searchKeyword)
				});
			},
			navigateToShopDetail(shopId) {
				uni.navigateTo({
					url: '/pages/business/shop-detail?id=' + shopId
				});
			},
			followBusiness(businessId) {
				// 关注商家的逻辑
				const recommendBusiness = this.recommendBusinessList.find(item => item.id === businessId);
				if (recommendBusiness) {
					recommendBusiness.isFollowed = !recommendBusiness.isFollowed;
					uni.showToast({
						title: recommendBusiness.isFollowed ? '已收藏' : '已取消收藏',
						icon: 'success'
					});
				} else {
					// 对于列表中的商家
					uni.showToast({
						title: '已收藏',
						icon: 'success'
					});
				}
			},
			navigateToFilter(category) {
				// 携带分类参数跳转到筛选页面
				uni.navigateTo({
					url: '/pages/business/filter?category=' + encodeURIComponent(category)
				});
			},
			// 处理轮播切换事件
			handleSwiperChange(e) {
				// 记录当前索引
				this.currentIndex = e.detail.current;
				
				// 如果不是第一个，一段时间后自动回到第一个
				if (this.currentIndex !== 0) {
					// 清除可能存在的定时器
					if(this.resetTimer) {
						clearTimeout(this.resetTimer);
					}
					
					// 设置新的定时器
					this.resetTimer = setTimeout(() => {
						// 5秒后自动回到第一项
						this.currentIndex = 0;
					}, 5000);
				}
			},
			
			// 获取轮播项的样式类
			getSwiperItemClass(index) {
				// 如果是第一个卡片，始终保持特定样式
				if (index === 0) {
					return 'merchant-swiper-item first-item';
				}
				
				// 获取轮播项的位置状态
				const items = this.displayItems;
				const current = this.currentIndex;
				
				// 计算当前项与显示中心的距离
				let distance = Math.abs(index - current);
				if (distance > Math.floor(items / 2)) {
					distance = Math.abs(index + this.recommendBusinessList.length - current);
					if (distance > Math.floor(items / 2)) {
						distance = Math.abs(current + this.recommendBusinessList.length - index);
					}
				}
				
				// 根据距离返回对应的类名
				if (distance === 0) {
					return 'merchant-swiper-item on-center';
				} else if (distance === 1) {
					return 'merchant-swiper-item near-center';
				} else {
					return 'merchant-swiper-item';
				}
			},
			updateDisplayItems() {
				// 基于屏幕宽度设置显示的商家卡片数量
				const windowWidth = uni.getSystemInfoSync().windowWidth;
				if (windowWidth < 360) {
					this.displayItems = 2;
				} else {
					this.displayItems = 2; // 固定显示2个
				}
			},
			// 商家推荐模块相关方法
			// 处理商家页面变化
			onMerchantPageChange(e) {
				this.currentMerchantPage = e.detail.current;
			},
			
			// 获取当前页面显示的商家列表
			getMerchantsForPage(pageIndex) {
				const start = pageIndex * 2;
				const end = start + 2;
				return this.recommendBusinessList.slice(start, end);
			},
			
			// 启动商家推荐自动轮播
			startMerchantAutoplay() {
				// 清除可能存在的定时器
				this.stopMerchantAutoplay();
				
				// 设置定时器，每2秒切换一次
				this.merchantAutoplayTimer = setInterval(() => {
					const totalPages = Math.ceil(this.recommendBusinessList.length / 2);
					if (totalPages > 1) {
						this.currentMerchantPage = (this.currentMerchantPage + 1) % totalPages;
					}
				}, 2000);
			},
			
			// 停止商家推荐自动轮播
			stopMerchantAutoplay() {
				if (this.merchantAutoplayTimer) {
					clearInterval(this.merchantAutoplayTimer);
					this.merchantAutoplayTimer = null;
				}
			},
			
			// 手动切换商家推荐页面
			changeMerchantPage(pageIndex) {
				if (this.currentMerchantPage !== pageIndex) {
					this.currentMerchantPage = pageIndex;
					// 手动切换后，重置自动轮播计时器
					this.startMerchantAutoplay();
				}
			}
		},
		components: { FabButtons }
	}
</script>

<style scoped>
/* 使用系统默认字体 */
.business-container {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 其他需要使用阿里妈妈字体的地方也替换为系统字体 */
.title, .heading, .subtitle {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-weight: bold; /* 使用系统字体的粗体代替阿里妈妈黑体 */
}

.business-page {
	min-height: 100vh;
	background: #f5f7fa;
	position: relative;
	padding-bottom: 30rpx;
	overflow: hidden;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 顶部蓝色背景 */
.top-blue-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 320rpx;
	background: linear-gradient(135deg, #0052CC, #0066FF);
	z-index: 0;
}

/* 状态栏占位 */
.status-bar {
	position: relative;
	width: 100%;
	z-index: 1;
}

/* 导航栏样式 */
.navbar {
	position: relative;
	z-index: 1;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navbar-title {
	color: #ffffff;
	font-size: 18px;
	font-weight: 700;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 统计栏 */
.stats-bar {
	position: relative;
	z-index: 1;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 20rpx 40rpx 5rpx;
	color: #333333;
	font-size: 24rpx;
}

.stat-item {
	display: flex;
	align-items: center;
}

.stat-icon {
	width: 36rpx;
	height: 36rpx;
	margin-right: 8rpx;
	opacity: 0.95;
}

.stat-divider {
	margin: 0 16rpx;
	color: rgba(0, 0, 0, 0.3);
	font-weight: 200;
}

.exposure-text, .in-row {
	color: #999999;
	font-weight: bold;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 商家入驻卡片 */
.join-card {
	margin: 30rpx 30rpx 40rpx;
	padding: 20rpx 30rpx;
	background: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
}

.join-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
	z-index: 0;
}

.join-card-left {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 2;
}

.join-icon-wrap {
	width: 76rpx;
	height: 76rpx;
	background: #EEF1F6;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	box-shadow: 6rpx 6rpx 12rpx rgba(174, 184, 210, 0.6),
	            -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.9),
	            inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.4);
	position: relative;
	z-index: 3;
	border: none;
}

.join-icon {
	width: 46rpx;
	height: 46rpx;
	opacity: 1;
	filter: none;
}

.join-info {
	display: flex;
	flex-direction: column;
}

.join-title {
	color: #3D56C1;
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
	text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	letter-spacing: 1rpx;
}

.join-desc {
	color: #5F6A8A;
	font-size: 24rpx;
	text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.join-btn {
	background: linear-gradient(145deg, #394FC2, #4A67D7);
	color: #ffffff;
	font-size: 28rpx;
	padding: 0 32rpx;
	height: 68rpx;
	line-height: 68rpx;
	border-radius: 34rpx;
	margin: 0;
	position: relative;
	z-index: 2;
	box-shadow: 6rpx 6rpx 12rpx rgba(61, 86, 193, 0.3),
	            -2rpx -2rpx 8rpx rgba(255, 255, 255, 0.3),
	            inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);
	font-weight: 600;
	border: none;
	transition: all 0.2s ease;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	text-align: center;
	letter-spacing: 1px;
}

.join-btn:active {
	transform: scale(0.96);
	box-shadow: 3rpx 3rpx 6rpx rgba(61, 86, 193, 0.3),
	            -1rpx -1rpx 4rpx rgba(255, 255, 255, 0.3),
	            inset 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.hot-badge {
	position: absolute;
	top: -3rpx;
	right: 8rpx;
	background: #FF5757;
	color: #ffffff;
	font-size: 20rpx;
	font-weight: bold;
	padding: 6rpx 12rpx;
	border-radius: 0 0 12rpx 12rpx;
	box-shadow: 0 4rpx 8rpx rgba(255, 87, 87, 0.4);
	display: block;
}

/* 分类宫格 */
.category-grid {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	margin: 15rpx 24rpx 24rpx;
	padding: 20rpx 15rpx;
	position: relative;
	z-index: 2;
	box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1), 0 8rpx 15rpx rgba(0, 0, 0, 0.05);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.6);
	transform: translateZ(0);
}

.category-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.category-row:last-child {
	margin-bottom: 0;
}

.category-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	padding: 10rpx 0;
	transition: transform 0.3s ease;
}

.category-item:active {
	transform: scale(0.95);
}

.category-icon {
	width: 80rpx;
	height: 80rpx;
	margin-bottom: 6rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.category-name {
	font-size: 22rpx;
	color: #333;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.hot-tag {
	position: absolute;
	top: -3rpx;
	right: 8rpx;
	background: #ff4d4f;
	color: #ffffff;
	font-size: 16rpx;
	padding: 0 6rpx;
	border-radius: 6rpx;
	transform: rotate(10deg);
}

/* 标签页切换 */
.tab-bar {
	display: flex;
	background: rgba(255, 255, 255, 0.95);
	padding: 15rpx 0;
	margin: 30rpx 24rpx;
	position: relative;
	z-index: 2;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
	border-radius: 16rpx;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.6);
	transform: translateZ(0);
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	padding: 3rpx 0;
}

.tab-text {
	font-size: 30rpx;
	color: #999;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	padding: 6rpx 0;
}

.tab-item.active .tab-text {
	color: #0052cc;
	font-weight: 500;
}

.tab-line {
	width: 40rpx;
	height: 4rpx;
	background: #0052cc;
	border-radius: 2rpx;
	position: absolute;
	bottom: -3rpx;
}

/* 商家列表 */
.business-list {
	padding: 0 24rpx;
	position: relative;
	z-index: 2;
}

.business-item {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	margin-bottom: 25rpx;
	box-shadow: 0 12rpx 25rpx rgba(0, 0, 0, 0.08), 0 5rpx 12rpx rgba(0, 0, 0, 0.05);
	backdrop-filter: blur(8px);
	-webkit-backdrop-filter: blur(8px);
	border: 1px solid rgba(255, 255, 255, 0.5);
	transform: translateZ(0);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.business-item:active {
	transform: translateY(2rpx);
	box-shadow: 0 8rpx 15rpx rgba(0, 0, 0, 0.06), 0 3rpx 8rpx rgba(0, 0, 0, 0.04);
}

.business-logo {
	width: 100rpx !important;
	height: 100rpx !important;
	border-radius: 50rpx !important; /* 使用具体数值而不是百分比 */
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
	margin-right: 20rpx;
	object-fit: cover;
	overflow: hidden;
	border: 2rpx solid #fff;
}

.business-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.business-name {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 10rpx;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.business-desc {
	font-size: 24rpx;
	color: #999;
}

.business-tags {
	margin-top: 10rpx;
}

.business-tag {
	display: inline-flex;
	align-items: center;
	background: rgba(255, 69, 58, 0.1);
	border-radius: 12rpx;
	padding: 6rpx 12rpx;
	margin-right: 8rpx;
	border: 1px solid rgba(255, 69, 58, 0.2);
}

.red-packet-tag {
	background: linear-gradient(to right, rgba(255, 69, 58, 0.1), rgba(255, 99, 88, 0.15));
}

.tag-icon {
	width: 24rpx;
	height: 24rpx;
	margin-right: 4rpx;
	vertical-align: middle;
}

.tag-text {
	font-size: 22rpx;
	color: #ff4538;
	line-height: 1;
	vertical-align: middle;
}

.follow-btn {
	background: linear-gradient(to right, #0052cc, #0070e0);
	color: #ffffff;
	font-size: 24rpx;
	padding: 0 24rpx;
	height: 56rpx;
	line-height: 56rpx;
	border-radius: 28rpx;
	margin: 0;
	box-shadow: 0 4rpx 10rpx rgba(0, 82, 204, 0.2);
	transition: transform 0.2s ease;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.follow-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 5rpx rgba(0, 82, 204, 0.15);
}

/* 轮播图 */
.swiper-container {
	position: relative;
	z-index: 1;
	width: 650rpx;
	height: 230rpx;
	margin: 15rpx auto 15rpx;
	box-sizing: content-box;
	border: 12rpx solid #f0f5ff;
	border-radius: 20rpx;
	background: #f0f5ff;
	box-shadow: 0 25rpx 35rpx -15rpx rgba(0, 0, 0, 0.2),
	            0 15rpx 20rpx -15rpx rgba(0, 0, 0, 0.15),
	            inset 0 -2rpx 8rpx rgba(255, 255, 255, 0.7);
	transform: translateY(0);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	animation: float 6s ease-in-out infinite;
}

@keyframes float {
	0% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-10rpx);
	}
	100% {
		transform: translateY(0);
	}
}

.banner-swiper {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
	overflow: hidden;
	border: none;
	box-shadow: inset 0 2rpx 15rpx rgba(0, 0, 0, 0.1);
}

.banner-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transform: scale(1.01); /* 轻微放大，防止边缘出现空白 */
	transition: transform 0.3s ease;
}

.swiper-container:hover {
	box-shadow: 0 30rpx 40rpx -15rpx rgba(0, 0, 0, 0.25),
	            0 20rpx 25rpx -15rpx rgba(0, 0, 0, 0.18),
	            inset 0 -2rpx 8rpx rgba(255, 255, 255, 0.7);
	transform: translateY(-5rpx);
}

/* 搜索框 - 更新样式 */
.search-container-inner {
	position: relative;
	z-index: 2;
	margin: 0 20rpx 20rpx;
	width: 75%;
	margin-left: auto;
	margin-right: auto;
}

.search-box {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 36rpx;
	padding: 10rpx 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);
	border: 1px solid rgba(0, 0, 0, 0.05);
}

.search-input {
	flex: 1;
	font-size: 26rpx;
	height: 60rpx;
	color: #333;
	padding-left: 10rpx;
}

.search-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 0.6;
}

/* 商家卡片轮播 */
.merchant-recommend-section {
	margin: 24rpx 30rpx 30rpx;
	position: relative;
	z-index: 2;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 24rpx 15rpx 50rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	overflow: visible !important;
}
.section-header {
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	align-items: flex-start;
	margin-bottom: 20rpx;
	padding: 0;
	position: relative;
}
.section-title-wrap {
	display: flex;
	align-items: center;
}
.section-bar {
	width: 6rpx;
	height: 28rpx;
	background: linear-gradient(to bottom, #007AFF, #5AC8FA);
	border-radius: 3rpx;
	margin-right: 12rpx;
}
.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	margin-bottom: 10rpx;
}
.section-more {
	position: absolute;
	right: 0;
	top: 0;
	margin-top: 0;
	z-index: 2;
}
.section-more:active {
	background: rgba(0, 0, 0, 0.05);
	transform: scale(0.96);
}
.more-text {
	font-size: 26rpx;
	color: #007AFF;
}
.more-icon {
	font-size: 26rpx;
	color: #007AFF;
	margin-left: 2rpx;
}
.merchant-swiper {
	width: 100%;
	height: 460rpx;
	margin-top: 20rpx;
	overflow: visible !important;
}
.merchant-swiper-page {
	display: flex;
	justify-content: center;
	padding: 10rpx 0;
	overflow: visible !important;
	height: 460rpx;
	box-sizing: border-box;
}
.merchant-item {
	width: 300rpx;
	height: 400rpx;
	overflow: visible !important;
	position: relative;
	margin: 0 15rpx;
	transform: translateZ(0);
	will-change: transform;
	backface-visibility: hidden;
	background: transparent;
	box-shadow: none;
	border: none;
	padding: 20rpx;
	box-sizing: border-box;
}
.merchant-card {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: linear-gradient(135deg, #f8faff 80%, #e6edfa 100%);
	border-radius: 20rpx;
	padding: 20rpx 15rpx 80rpx 15rpx;
	box-shadow: 0 8rpx 20rpx rgba(78, 96, 230, 0.12), 0 2rpx 8rpx rgba(0,0,0,0.06);
	border: 3rpx solid #e6edfa;
	position: relative;
	transform: translateZ(0);
	will-change: transform;
	backface-visibility: hidden;
	transition: all 0.4s ease;
	opacity: 1;
	box-sizing: border-box;
	overflow: visible;
}
.merchant-card::before {
	content: "";
	position: absolute;
	top: -18rpx;
	left: -18rpx;
	right: -18rpx;
	bottom: -18rpx;
	background: transparent;
	border-radius: 22rpx;
	border: 3rpx solid rgba(230, 237, 250, 0.9);
	z-index: -1;
	box-shadow: 0 18rpx 40rpx rgba(78, 96, 230, 0.25);
}
.merchant-logo {
	width: 110rpx !important;
	height: 110rpx !important;
	border-radius: 55rpx !important; /* 使用具体数值而不是百分比 */
	margin-bottom: 15rpx;
	border: 5rpx solid #fff;
	box-shadow: 0 6rpx 18rpx rgba(78, 96, 230, 0.15);
	background: linear-gradient(135deg, #e6edfa 60%, #f8faff 100%);
	object-fit: cover;
	overflow: hidden; /* 确保内容不会溢出圆形边界 */
	display: block; /* 确保元素为块级 */
	flex-shrink: 0; /* 防止在flex布局中被压缩 */
}
.merchant-info {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.merchant-name-wrap {
	width: 100%;
	text-align: center;
	margin-bottom: 8rpx;
}
.merchant-name {
	font-size: 26rpx;
	color: #222;
	font-weight: 700;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 90%;
	margin: 0 auto;
	letter-spacing: 1rpx;
}
.merchant-desc {
	font-size: 22rpx;
	color: #8E8E93;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 90%;
	margin: 0 auto 10rpx;
}
.merchant-category {
	font-size: 20rpx;
	color: #007AFF;
	background: rgba(0, 122, 255, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	text-align: center;
	max-width: 90%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	z-index: 1;
}
.merchant-collect-btn {
	width: 70%;
	height: 60rpx;
	line-height: 60rpx;
	font-size: 24rpx;
	color: #FFFFFF;
	background: linear-gradient(to right, #007AFF, #5AC8FA) !important;
	border-radius: 30rpx !important;
	padding: 0 10rpx;
	margin: 0;
	position: absolute;
	bottom: 15rpx;
	left: 50%;
	transform: translateX(-50%);
	box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.2) !important;
	white-space: nowrap;
	overflow: visible;
	border: none !important;
	outline: none !important;
	-webkit-appearance: none !important;
	-moz-appearance: none !important;
	appearance: none !important;
	border-style: none !important;
	border-width: 0 !important;
	border-color: transparent !important;
	box-sizing: border-box;
	text-decoration: none;
	text-align: center !important;
	display: flex !important;
	justify-content: center !important;
	align-items: center !important;
	z-index: 2;
}

.merchant-indicators {
	display: flex;
	justify-content: center;
	margin-top: 15rpx;
}
.merchant-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 6rpx;
	background-color: #D1D1D6;
	margin: 0 6rpx;
	transition: all 0.3s ease;
}
.merchant-dot.active {
	width: 24rpx;
	background: #007AFF;
}

.collect-btn-text {
	text-align: center;
	width: 100%;
	display: inline-block;
	font-size: 24rpx;
	color: #FFFFFF;
	line-height: 60rpx;
	height: 60rpx;
	margin: 0;
	padding: 0;
}
</style> 