<template>
  <view class="success-container">
    <!-- 导航栏 -->
    <carpool-nav title="发布成功"></carpool-nav>
    
    <!-- 页面主体 -->
    <view class="success-content">
      <!-- 成功状态卡片 - 新设计 -->
      <view class="main-card">
        <view class="main-title">信息发布成功 - 磁州拼车</view>
        <view class="main-desc">完善信息或置顶，可以大幅度提高拼车信息传播效果哦</view>
        <view class="main-btns">
          <view class="main-btn" @click="goHome">
            <view class="btn-text-container">
              <text class="btn-text">首</text>
              <text class="btn-text">页</text>
            </view>
          </view>
          <view class="main-btn main-btn-active" @click="viewDetail">
            <view class="btn-text-container">
              <text class="btn-text">查看</text>
              <text class="btn-text">信息</text>
            </view>
          </view>
          <view class="main-btn" @click="publishAgain">
            <view class="btn-text-container">
              <text class="btn-text">再发</text>
              <text class="btn-text">一条</text>
            </view>
          </view>
          <view class="main-btn" @click="shareInfo">
            <view class="btn-text-container">
              <text class="btn-text">分享</text>
              <text class="btn-text">信息</text>
            </view>
          </view>
          <view class="main-btn" @click="goToMyPublish">
            <view class="btn-text-container">
              <text class="btn-text">管理</text>
              <text class="btn-text">信息</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 置顶信息组件 -->
      <view class="premium-card">
        <view class="premium-title">
          <view class="premium-title-icon">
            <image src="/static/images/tabbar/crown.png" mode="aspectFit"></image>
          </view>
          <text class="premium-title-text">置顶信息，提升10倍曝光率</text>
        </view>
        <view class="custom-premium-actions">
          <!-- 广告置顶选项 -->
          <view class="premium-option-item ad-option" @click="handleAdTop">
            <view class="option-left">
              <view class="option-circle blue-circle">
                <image src="/static/images/tabbar/crown.png" mode="aspectFit" class="option-icon"></image>
              </view>
              <view class="option-info">
                <text class="option-title">看广告置顶</text>
                <text class="option-desc">免费置顶2小时，排名次于付费</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-button free-button">免费</view>
            </view>
          </view>
          
          <!-- 付费置顶选项 -->
          <view class="premium-option-item paid-option" @click="handlePaidTop">
            <view class="option-left">
              <view class="option-circle orange-circle">
                <image src="/static/images/tabbar/crown.png" mode="aspectFit" class="option-icon"></image>
              </view>
              <view class="option-info">
                <text class="option-title">付费置顶</text>
                <text class="option-desc">获得优先展示位置，排名最靠前</text>
              </view>
            </view>
            <view class="option-right">
              <view class="option-button paid-button">付费</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 信息卡片 -->
        <view class="info-card">
        <view class="card-header">
          <text class="card-title">拼车信息概览</text>
            <view class="info-tag" :class="typeClass">{{typeText}}</view>
          </view>
          
        <!-- 路线信息 -->
        <view class="route-section">
          <view class="route-points">
            <view class="route-point">
              <view class="point-dot start"></view>
              <text class="point-text">{{formData.startPoint}}</text>
            </view>
            <view class="route-divider">
              <view class="divider-line"></view>
              <view class="divider-arrow">
                <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
              </view>
            </view>
            <view class="route-point">
              <view class="point-dot end"></view>
              <text class="point-text">{{formData.endPoint}}</text>
            </view>
          </view>
          
          <!-- 途径点 -->
          <view class="via-points" v-if="formData.viaPoints && formData.viaPoints.length > 0">
            <view class="via-title">
              <image src="/static/images/tabbar/location.png" mode="aspectFit" class="via-icon"></image>
              <text class="via-text">途径：</text>
            </view>
            <view class="via-list">
              <text class="via-item" v-for="(point, index) in formData.viaPoints" :key="index">{{point}}</text>
            </view>
          </view>
        </view>
        
        <!-- 详细信息 -->
        <view class="details-section">
          <view class="detail-row">
            <view class="detail-item">
              <image src="/static/images/tabbar/calendar.png" mode="aspectFit" class="detail-icon"></image>
              <text class="detail-text">{{formData.departureDate}}</text>
            </view>
            <view class="detail-item">
              <image src="/static/images/tabbar/time.png" mode="aspectFit" class="detail-icon"></image>
              <text class="detail-text">{{formData.departureTime}}</text>
            </view>
            </view>
            
          <view class="detail-row">
            <view class="detail-item" v-if="publishType === 'people-to-car'">
              <image src="/static/images/tabbar/user.png" mode="aspectFit" class="detail-icon"></image>
              <text class="detail-text">{{formData.passengers}}人</text>
            </view>
            <view class="detail-item" v-if="publishType === 'car-to-people'">
              <image src="/static/images/tabbar/座位.png" mode="aspectFit" class="detail-icon"></image>
              <text class="detail-text">空余座位 {{formData.availableSeats}}</text>
            </view>
            <view class="detail-item" v-if="publishType === 'goods-to-car' || publishType === 'car-to-goods'">
              <image src="/static/images/tabbar/货物.png" mode="aspectFit" class="detail-icon"></image>
              <text class="detail-text">{{formData.goodsType || '普通货物'}}</text>
            </view>
            <view class="detail-item" v-if="mode === 'premium'">
              <image src="/static/images/tabbar/vip-crown.png" mode="aspectFit" class="detail-icon premium-icon"></image>
              <text class="detail-text premium-text">已置顶展示</text>
            </view>
          </view>
          
          <view class="contact-row">
            <view class="contact-item">
              <image src="/static/images/tabbar/phone.png" mode="aspectFit" class="contact-icon"></image>
              <text class="contact-text">{{formData.contactPhone}}</text>
            </view>
            <view class="contact-item" v-if="formData.contactName">
              <image src="/static/images/tabbar/user-circle.png" mode="aspectFit" class="contact-icon"></image>
              <text class="contact-text">{{formData.contactName}}</text>
          </view>
        </view>
        
          <view class="remark-row" v-if="formData.remark">
            <text class="remark-label">备注：</text>
            <text class="remark-content">{{formData.remark}}</text>
          </view>
        </view>
      </view>
      
      <!-- 温馨提示 -->
      <view class="tips-card">
        <view class="tips-header">
          <image src="/static/images/tabbar/info.png" mode="aspectFit" class="tips-icon"></image>
            <text class="tips-title">温馨提示</text>
          </view>
        <view class="tips-content">
          <text class="tips-text">· 发布信息将在人工审核后展示，预计5分钟内完成</text>
          <text class="tips-text">· 请保持电话畅通，以便有意向的用户联系您</text>
          <text class="tips-text">· 拼车出行请注意安全，提前确认对方身份</text>
        </view>
      </view>
      
      <!-- 悬浮分享按钮 -->
      <button class="float-btn share-btn" open-type="share" @click="beforeShare">
        <image src="/static/images/tabbar/share.png" mode="aspectFit" class="float-icon"></image>
        </button>
        
      <!-- 客服按钮 -->
      <view class="float-btn kefu-btn" @click="showKefu">
        <image src="/static/images/tabbar/kefu.png" mode="aspectFit" class="float-icon"></image>
      </view>
      </view>
      
    <!-- 分享提示弹窗 -->
    <view class="share-tips-overlay" v-if="shareTipsVisible">
      <view class="share-tips-card" @click.stop>
        <view class="share-tips-icon">
          <image src="/static/images/tabbar/crown.png" mode="aspectFit" class="crown-icon"></image>
        </view>
        <view class="share-tips-title">恭喜你!获得免费置顶和群发机会</view>
        
        <view class="share-tips-item">
          <view class="tips-item-number">1</view>
          <view class="tips-item-content">
            <text class="tips-text">把信息分享到朋友圈、微信群或好友处，</text>
            <text class="tips-text">您的信息将自动置顶1天！</text>
          </view>
        </view>
        
        <view class="share-tips-item">
          <view class="tips-item-number">2</view>
          <view class="tips-item-content">
            <text class="tips-text">把你发布的信息发送给客服后，客服会给</text>
            <text class="tips-text">您群发多个群，扩大曝光量!</text>
          </view>
        </view>
        
        <view class="share-tips-btns">
          <view class="share-btn-item close-btn" @click="hideShareTips">关闭</view>
          <view class="share-btn-item share-btn-blue pulsing-btn" @click="jumpToDetailAndShare">
            <text>去分享</text>
          </view>
          <view class="share-btn-item share-btn-green" @click="contactService">加客服</view>
        </view>
      </view>
    </view>
    
    <!-- 客服二维码弹窗 -->
    <view class="qrcode-overlay" v-if="qrcodeVisible" @click="hideQrcode">
      <view class="qrcode-card" @click.stop>
        <view class="qrcode-header">
          <view class="qrcode-close" @click="hideQrcode">
            <svg t="1692586074385" class="close-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="20">
              <path d="M572.16 512l183.466667-183.04a42.666667 42.666667 0 1 0-60.586667-60.586667L512 451.84 328.96 268.373333a42.666667 42.666667 0 0 0-60.586667 60.586667l183.04 183.04-183.04 183.466667a42.666667 42.666667 0 0 0 60.586667 60.586666L512 572.16l183.04 183.466667a42.666667 42.666667 0 0 0 60.586667-60.586667z" fill="#999999"></path>
            </svg>
          </view>
        </view>
        
        <view class="qrcode-content">
          <view class="qrcode-title-container">
            <image class="qrcode-title-icon" src="/static/images/icons/customer-service.png" mode="aspectFit"></image>
            <text class="qrcode-title">微信扫码添加客服</text>
          </view>
          
          <view class="qrcode-desc">添加客服微信，提供更多发布推广服务</view>
          
          <view class="qrcode-image-container">
            <image src="/static/images/qrcode.png" mode="aspectFit" class="qrcode-image"></image>
            <view class="qrcode-scan-hint">
              <view class="scan-icon-container">
                <svg viewBox="0 0 24 24" width="16" height="16" class="scan-icon">
                  <path fill="currentColor" d="M9.5,6.5v3h-3v-3H9.5 M11,5H5v6h6V5L11,5z M9.5,14.5v3h-3v-3H9.5 M11,13H5v6h6V13z M17.5,6.5v3h-3v-3H17.5 M19,5h-6v6h6V5L19,5z M13,13h1.5v1.5H13V13z M14.5,14.5H16V16h-1.5V14.5z M16,13h1.5v1.5H16V13z M13,16h1.5v1.5H13V16z M14.5,17.5H16V19h-1.5V17.5z M16,16h1.5v1.5H16V16z M17.5,14.5H19V16h-1.5V14.5z M17.5,17.5H19V19h-1.5V17.5z M22,7h-2V4h-3V2h5V7z M22,22v-5h-2v3h-3v2H22z M2,22h5v-2H4v-3H2V22z M2,2v5h2V4h3V2H2z"/>
                </svg>
              </view>
              <text>长按识别二维码添加客服</text>
            </view>
          </view>
          
          <view class="qrcode-info-container">
            <view class="qrcode-info-item">
              <svg viewBox="0 0 24 24" width="16" height="16" class="info-icon">
                <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,7H13V13H11V7M11,15H13V17H11V15Z"/>
              </svg>
              <text>客服在线时间: 8:00-22:00</text>
            </view>
            <view class="qrcode-info-item">
              <svg viewBox="0 0 24 24" width="16" height="16" class="info-icon">
                <path fill="currentColor" d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,6A2,2 0 0,0 10,8A2,2 0 0,0 12,10A2,2 0 0,0 14,8A2,2 0 0,0 12,6M12,13C14.67,13 20,14.33 20,17V20H4V17C4,14.33 9.33,13 12,13M12,14.9C9.03,14.9 5.9,16.36 5.9,17V18.1H18.1V17C18.1,16.36 14.97,14.9 12,14.9Z"/>
              </svg>
              <text>客服微信: cishangtc</text>
            </view>
          </view>
        </view>
        
        <view class="qrcode-actions">
          <view class="qrcode-btn copy-btn" @click="copyWechatId">
            <svg viewBox="0 0 24 24" width="16" height="16" class="btn-icon">
              <path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
            </svg>
            <text>复制微信号</text>
          </view>
          <view class="qrcode-btn save-btn" @click="saveQrcode">
            <svg viewBox="0 0 24 24" width="16" height="16" class="btn-icon">
              <path fill="currentColor" d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/>
            </svg>
            <text>保存图片</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import rewardedAdService from '@/services/rewardedAdService';

// 发布信息数据
const publishId = ref('');
const publishType = ref('people-to-car');
const mode = ref('ad'); // ad 或 premium

// 表单数据
const formData = ref({
  startPoint: '磁州城区',
  endPoint: '邯郸站',
  departureDate: '2023-10-15',
  departureTime: '14:30',
  passengers: '2',
  availableSeats: 3,
  contactName: '张先生',
  contactPhone: '138****5678',
  remark: '行李少，准时出发',
  viaPoints: ['磁县政府', '磁县一中']
});

// 计算属性
const typeText = computed(() => {
  const typeMap = {
    'people-to-car': '人找车',
    'car-to-people': '车找人',
    'goods-to-car': '货找车',
    'car-to-goods': '车找货'
  };
  return typeMap[publishType.value] || '人找车';
});

const typeClass = computed(() => {
  return publishType.value;
});

// 页面加载
onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};

  // 获取发布类型和模式
  if (options && options.type) {
    publishType.value = options.type;
  }
  if (options && options.mode) {
    mode.value = options.mode;
  }
  if (options && options.id) {
    publishId.value = options.id;
    getPublishDetail();
  }

  // 初始化激励视频广告
  rewardedAdService.initRewardedVideoAd();

  // 监听奖励发放成功事件
  uni.$on('rewardGranted', handleRewardGranted);

  // 自动显示分享提示弹窗
  setTimeout(() => {
    showShareTips();
  }, 1000);
});

onUnmounted(() => {
  // 清理事件监听
  uni.$off('rewardGranted', handleRewardGranted);

  // 销毁广告实例
  rewardedAdService.destroy();
});

// 分享配置
// 注意：在组合式API中，需要使用defineExpose将分享方法暴露出去
const onShareAppMessage = () => {
  return {
    title: `${typeText.value}：${formData.value.startPoint} → ${formData.value.endPoint}`,
    path: `/carpool-package/pages/carpool/detail/index?id=${publishId.value}`
  };
};

// 获取发布详情
const getPublishDetail = () => {
  // 这里应该是真实的API调用
  // 目前使用模拟数据
  console.log('获取发布ID:', publishId.value);
};

// 处理奖励发放成功
const handleRewardGranted = (rewardInfo) => {
  console.log('奖励发放成功', rewardInfo);

  // 根据奖励类型更新界面状态
  switch (rewardInfo.type) {
    case 'free_top':
      // 更新置顶状态显示
      uni.showModal({
        title: '置顶成功',
        content: '您的拼车信息已成功置顶2小时，将获得更多曝光机会！',
        showCancel: false
      });
      break;
    case 'free_publish':
      uni.showModal({
        title: '发布成功',
        content: '恭喜您获得免费发布机会！',
        showCancel: false
      });
      break;
    case 'free_refresh':
      uni.showModal({
        title: '刷新成功',
        content: '您的拼车信息已刷新，排名已更新！',
        showCancel: false
      });
      break;
  }

  // 可以在这里刷新页面数据或更新状态
  // getPublishDetail();
};

// 处理置顶选项选择
const handlePremiumAction = (option) => {
  console.log('选择了置顶选项:', option);
  if (option.type === 'paid') {
    uni.navigateTo({
      url: `/carpool-package/pages/carpool/premium/top?id=${publishId.value}`
    });
  } else {
    // 处理广告置顶
    uni.showToast({
      title: '广告置顶成功！信息已置顶2小时',
      icon: 'success',
      duration: 2000
    });
  }
};

// 处理广告置顶点击
const handleAdTop = async () => {
  try {
    // 检查今日观看次数
    const checkResult = await rewardedAdService.checkTodayWatchCount('top');
    if (!checkResult.canWatch) {
      uni.showToast({
        title: checkResult.message,
        icon: 'none'
      });
      return;
    }

    // 显示激励视频广告
    const success = await rewardedAdService.showRewardedVideoAd('free_top', publishId.value);

    if (!success) {
      // 广告显示失败，提供备选方案
      uni.showModal({
        title: '广告暂不可用',
        content: '是否选择付费置顶？',
        success: (res) => {
          if (res.confirm) {
            handlePaidTop();
          }
        }
      });
    }
  } catch (error) {
    console.error('广告置顶失败', error);
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    });
  }
};

// 处理付费置顶点击
const handlePaidTop = () => {
  // 跳转到付费置顶页面
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/premium/top?id=${publishId.value}`
  });
};

// 分享提示相关
const shareTipsVisible = ref(false);

// 客服二维码相关
const qrcodeVisible = ref(false);
const wechatId = ref('cishangtc'); // 客服微信ID

// 分享相关方法
const beforeShare = () => {
  // 可以在这里记录分享行为或其他操作
  console.log('用户点击了分享按钮');
};

// 显示分享提示
const showShareTips = () => {
  shareTipsVisible.value = true;
};

// 隐藏分享提示
const hideShareTips = () => {
  shareTipsVisible.value = false;
};

// 跳转到详情页并分享
const jumpToDetailAndShare = () => {
  hideShareTips();
  viewDetail();
  // 延迟显示系统分享菜单
  setTimeout(() => {
    uni.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  }, 500);
};

// 联系客服
const contactService = () => {
  hideShareTips();
  showKefu();
};

// 显示客服二维码
const showKefu = () => {
  qrcodeVisible.value = true;
};

// 隐藏客服二维码
const hideQrcode = () => {
  qrcodeVisible.value = false;
};

// 复制微信号
const copyWechatId = () => {
  uni.setClipboardData({
    data: wechatId.value,
    success: () => {
      uni.showToast({
        title: '微信号已复制',
        icon: 'success'
      });
    }
  });
};

// 保存二维码图片
const saveQrcode = () => {
  uni.saveImageToPhotosAlbum({
    filePath: '/static/images/qrcode.png',
    success: () => {
      uni.showToast({
        title: '二维码已保存',
        icon: 'success'
      });
    },
    fail: (err) => {
      if (err.errMsg.indexOf('auth deny') >= 0) {
        uni.showModal({
          title: '提示',
          content: '需要您授权保存图片到相册',
          success: (res) => {
            if (res.confirm) {
              uni.openSetting();
            }
          }
        });
      }
    }
  });
};

// 分享信息
const shareInfo = () => {
  // 调用分享功能
  uni.showShareMenu({
    withShareTicket: true
  });
};

// 查看详情
const viewDetail = () => {
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/detail/index?id=${publishId.value}`
  });
};

// 返回首页
const goHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 继续发布
const publishAgain = () => {
  uni.redirectTo({
    url: '/carpool-package/pages/carpool/publish/ad-publish'
  });
};

// 跳转到我的发布列表
const goToMyPublish = () => {
  uni.navigateTo({
    url: '/carpool-package/pages/carpool/my/published-list'
  });
};

// 暴露分享方法
defineExpose({
  onShareAppMessage
});
</script>

<style lang="scss">
.success-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

.success-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 成功状态卡片 - 新设计 */
.main-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(10, 132, 255, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.main-title {
  color: #1976d2;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-align: center;
}

.main-desc {
  color: #888;
  font-size: 24rpx;
  margin-bottom: 36rpx;
  text-align: center;
}

.main-btns {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 10rpx;
}

.main-btn {
  flex: 1;
  margin: 0 10rpx;
  background: #eaf3ff;
  color: #1976d2;
  border-radius: 32rpx;
  font-weight: 500;
  text-align: center;
  padding: 25rpx 0;
  height: 180rpx;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 10rpx 0;
}

.btn-text {
  font-size: 28rpx;
  line-height: 1.8;
  display: block;
  font-weight: bold;
  letter-spacing: 2rpx;
}

.main-btn-active {
  background: linear-gradient(90deg, #1976d2 0%, #4a90e2 100%);
  color: #fff;
}

.main-btn-active .btn-text {
  color: #fff;
}

/* 置顶信息组件 */
.premium-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 30rpx;
}

.premium-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.premium-title-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.premium-title-icon image {
  width: 36rpx;
  height: 36rpx;
}

.premium-title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

/* 自定义置顶选项 */
.custom-premium-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.premium-option-item {
  background-color: #f8f9fe;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-left {
  display: flex;
  align-items: center;
}

.option-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.blue-circle {
  background-color: #4a90e2;
}

.orange-circle {
  background-color: #f5a623;
}

.option-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.option-info {
  display: flex;
  flex-direction: column;
}

.option-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 6rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #999999;
}

.option-right {
  display: flex;
  align-items: center;
}

.option-button {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  font-weight: bold;
}

.free-button {
  background-color: #4a90e2;
  color: #ffffff;
}

.paid-button {
  background-color: #f5a623;
  color: #ffffff;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.info-tag {
  margin-left: 15rpx;
  font-size: 22rpx;
  color: #ffffff;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.people-to-car {
  background-color: #0A84FF;
}

.car-to-people {
  background-color: #FF453A;
}

.goods-to-car {
  background-color: #30D158;
}

.car-to-goods {
  background-color: #FF9F0A;
}

/* 路线信息 */
.route-section {
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  padding-bottom: 20rpx;
}

.route-points {
  margin-bottom: 20rpx;
}

.route-point {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.point-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.start {
  background-color: #0A84FF;
}

.end {
  background-color: #FF453A;
}

.point-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.route-divider {
  padding-left: 7rpx;
  margin: 10rpx 0;
  display: flex;
}

.divider-line {
  width: 2rpx;
  height: 30rpx;
  background-color: #dddddd;
}

.divider-arrow {
  margin-left: -7rpx;
  margin-top: 30rpx;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 途径点 */
.via-points {
  margin-top: 20rpx;
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 12rpx;
  padding: 15rpx;
}

.via-title {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.via-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
  opacity: 0.7;
}

.via-text {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.via-list {
  display: flex;
  flex-wrap: wrap;
}

.via-item {
  font-size: 26rpx;
  color: #0A84FF;
  background-color: rgba(10, 132, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
}

/* 详细信息 */
.details-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-row, .contact-row {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
}

.detail-item, .contact-item {
  display: flex;
  align-items: center;
}

.detail-icon, .contact-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  opacity: 0.6;
}

.detail-text, .contact-text {
  font-size: 28rpx;
  color: #666666;
}

.premium-icon {
  opacity: 1;
}

.premium-text {
  color: #FF9F0A;
  font-weight: 500;
}

.remark-row {
  background-color: rgba(0, 0, 0, 0.02);
  padding: 15rpx;
  border-radius: 12rpx;
}

.remark-label {
  font-size: 26rpx;
  color: #999999;
  margin-right: 10rpx;
}

.remark-content {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 温馨提示 */
.tips-card {
  background-color: rgba(10, 132, 255, 0.05);
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.tips-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  opacity: 0.7;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 悬浮分享按钮 */
.float-btn {
  position: fixed;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background-color: #1677FF;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 0;
  margin: 0;
  border: none;
}

.share-btn {
  bottom: 40rpx;
  right: 40rpx;
}

.kefu-btn {
  bottom: 40rpx;
  left: 40rpx;
}

.float-icon {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* 分享提示弹窗 */
.share-tips-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.share-tips-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  max-width: 80%;
  width: 100%;
  text-align: center;
}

.share-tips-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.crown-icon {
  width: 80rpx;
  height: 80rpx;
}

.share-tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF6B00;
  margin-bottom: 30rpx;
}

.share-tips-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  text-align: left;
}

.tips-item-number {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  background-color: #1677FF;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.tips-item-content {
  flex: 1;
}

.tips-text {
  font-size: 28rpx;
  color: #333333;
  display: block;
  line-height: 1.5;
}

.share-tips-btns {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  border-top: 1rpx solid #EEEEEE;
}

.share-btn-item {
  flex: 1;
  padding: 20rpx 0;
  font-size: 30rpx;
  font-weight: bold;
}

.close-btn {
  color: #999999;
  border-right: 1rpx solid #EEEEEE;
}

.share-btn-blue {
  background-color: #1677FF;
  color: #ffffff;
}

.share-btn-green {
  background-color: #07C160;
  color: #ffffff;
}

/* 脉动动画 */
.pulsing-btn {
  animation: pulse 1.5s infinite;
  position: relative;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(22, 119, 255, 0.7);
  }
  
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(22, 119, 255, 0);
  }
  
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(22, 119, 255, 0);
  }
}

/* 客服二维码弹窗 */
.qrcode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.qrcode-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx;
  width: 80%;
  max-width: 600rpx;
}

.qrcode-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.qrcode-close {
  width: 40rpx;
  height: 40rpx;
  padding: 10rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 24rpx;
  height: 24rpx;
}

.qrcode-content {
  text-align: center;
}

.qrcode-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.qrcode-title-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.qrcode-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.qrcode-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.qrcode-image-container {
  position: relative;
  margin: 0 auto 30rpx;
  width: 300rpx;
  height: 300rpx;
}

.qrcode-image {
  width: 100%;
  height: 100%;
}

.qrcode-scan-hint {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  font-size: 22rpx;
  border-radius: 0 0 8rpx 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-icon-container {
  margin-right: 8rpx;
  color: #ffffff;
}

.scan-icon {
  width: 24rpx;
  height: 24rpx;
}

.qrcode-info-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.qrcode-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  color: #666666;
  font-size: 24rpx;
}

.info-icon {
  margin-right: 8rpx;
  color: #1677FF;
}

.qrcode-actions {
  display: flex;
  justify-content: space-around;
  border-top: 1rpx solid #EEEEEE;
  padding-top: 20rpx;
}

.qrcode-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #1677FF;
}

.copy-btn {
  border-right: 1rpx solid #EEEEEE;
}

.btn-icon {
  margin-right: 8rpx;
  color: #1677FF;
}
</style> 