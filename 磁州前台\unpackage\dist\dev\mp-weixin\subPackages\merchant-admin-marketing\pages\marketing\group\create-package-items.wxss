/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.create-package-items-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
}

/* 步骤指示器 */
.step-indicator {
  padding: 15px;
  background: #FFFFFF;
}
.step-progress {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 5px;
  position: relative;
}
.step-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #9040FF, #5E35B1);
  border-radius: 2px;
}
.step-text {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 20px 15px;
}
.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}
.page-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

/* 套餐项样式 */
.package-items {
  margin-bottom: 20px;
}
.package-item {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #EBEDF5;
}
.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.item-actions {
  display: flex;
  gap: 10px;
}
.action-btn {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 15px;
}
.action-btn.edit {
  background: #F0F7FF;
  color: #007AFF;
}
.action-btn.delete {
  background: #FFF0F0;
  color: #FF3B30;
}
.item-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.item-info {
  display: flex;
  align-items: flex-start;
}
.info-label {
  width: 60px;
  font-size: 14px;
  color: #666;
}
.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}
.add-item-btn {
  background: #FFFFFF;
  border: 1px dashed #CCCCCC;
  border-radius: 12px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.add-icon {
  width: 30px;
  height: 30px;
  background: #F5F7FA;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #9040FF;
  margin-bottom: 5px;
}
.add-text {
  font-size: 14px;
  color: #9040FF;
}

/* 总价值预览 */
.total-value-preview {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.preview-header {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #EBEDF5;
}
.preview-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.preview-label {
  font-size: 14px;
  color: #666;
}
.preview-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.preview-value.group-price {
  color: #FF3B30;
}
.preview-value.discount {
  color: #34C759;
}

/* 底部按钮 */
.footer-buttons {
  padding: 15px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  gap: 15px;
}
.btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}
.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}
.btn-secondary {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}
.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  transform: translateY(0);
  transition: transform 0.3s;
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.modal-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #999;
}
.modal-body {
  max-height: 60vh;
  overflow-y: auto;
}
.modal-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 15px;
}
.modal-btn {
  flex: 1;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  border: none;
}
.modal-btn.cancel {
  background: #F5F7FA;
  color: #666;
  border: 1px solid #EBEDF5;
}
.modal-btn.confirm {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #FFFFFF;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20px;
}
.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}
.required {
  color: #FF3B30;
}
.form-input {
  width: 100%;
  height: 45px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
}
.form-textarea {
  width: 100%;
  height: 100px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
}
.quantity-input {
  display: flex;
  gap: 10px;
}
.unit-input {
  width: 80px;
  height: 45px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  border: 1px solid #EBEDF5;
}
.price-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 8px;
  border: 1px solid #EBEDF5;
  padding: 0 12px;
}
.price-symbol {
  font-size: 14px;
  color: #333;
  margin-right: 5px;
}