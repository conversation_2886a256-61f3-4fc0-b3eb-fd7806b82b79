/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.detail-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(44px + var(--status-bar-height));
}

/* 自定义导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}
.header-content {
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}
.left-action, .right-action {
  width: 40px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}
.title-area {
  flex: 1;
  text-align: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
}

/* 内容区域 */
.detail-content {
  padding: 5rpx 20rpx 20rpx;
  height: calc(100vh - 60px - var(--status-bar-height) - 44px);
  box-sizing: border-box;
}

/* 标签容器 */
.header-tag-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24rpx;
  padding: 25px 24rpx 0;
}

/* 信息头部 */
.detail-header {
  margin-bottom: 32rpx;
  width: 100%;
}
.header-tag {
  display: inline-flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  padding: 10rpx 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: relative;
}
.header-tag.people-to-car {
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
}
.header-tag.car-to-people {
  background: linear-gradient(135deg, #FF2D55, #FF9500);
}
.header-tag.goods-to-car {
  background: linear-gradient(135deg, #30D158, #34C759);
}
.header-tag.car-to-goods {
  background: linear-gradient(135deg, #FF9F0A, #FFD60A);
}
.verified-tag {
  font-size: 22rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 12rpx;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 5;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
.verified-icon {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background-color: #3DE07E;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);
}
.verified-icon svg {
  color: white;
}

/* 路线卡片 */
.route-card, .info-card, .user-card, .car-card, .remark-card, .trip-details-card, .disclaimer-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx 32rpx 24rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.08);
  width: calc(100% - 48rpx);
  box-sizing: border-box;
  overflow: hidden;
}
.route-points {
  position: relative;
}
.route-point {
  display: flex;
  align-items: flex-start;
  position: relative;
  padding: 20rpx 0;
}
.point-dot {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  margin-top: 8rpx;
  flex-shrink: 0;
  z-index: 2;
}
.start {
  background-color: #0A84FF;
  box-shadow: 0 0 0 8rpx rgba(10, 132, 255, 0.15);
}
.end {
  background-color: #FF2D55;
  box-shadow: 0 0 0 8rpx rgba(255, 45, 85, 0.15);
}
.point-info {
  flex: 1;
}
.point-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}
.point-address {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
  display: block;
}
.route-divider {
  padding: 10rpx 0 10rpx 14rpx;
  position: relative;
}
.divider-line {
  position: absolute;
  left: 14rpx;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #0A84FF, #FF2D55);
  z-index: 1;
}
.divider-info {
  margin-left: 34rpx;
  background-color: #F2F7FD;
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  display: inline-block;
}
.divider-text {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

/* 信息卡片 - 新设计 */
.info-card {
  display: flex;
  flex-wrap: wrap;
  gap: 0;
  background-color: #ffffff;
}
.info-item-new {
  width: 50%;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #F2F2F7;
}
.info-item-new:nth-child(odd) {
  border-right: 1px solid #F2F2F7;
}
.info-label-new {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 12rpx;
}
.info-value-new {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}
.price-value {
  color: #FF2D55;
}

/* 用户卡片 */
.user-card, .car-card, .remark-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.08);
}
.user-header, .car-header, .remark-header {
  margin-bottom: 24rpx;
  border-bottom: 1px solid #F2F2F7;
  padding-bottom: 16rpx;
}
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 16rpx;
}
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 6rpx;
  bottom: 6rpx;
  width: 6rpx;
  background: linear-gradient(to bottom, #0A84FF, #5AC8FA);
  border-radius: 3rpx;
}
.user-info {
  display: flex;
  align-items: center;
}
.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.user-details {
  flex: 1;
}
.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 12rpx;
}
.user-badges {
  display: flex;
  gap: 8rpx;
  flex-wrap: nowrap;
}
.user-badge {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.user-badge.verified {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.15), rgba(10, 132, 255, 0.05));
  color: #0A84FF;
  border: 1px solid rgba(10, 132, 255, 0.3);
  display: inline-flex;
  align-items: center;
  box-shadow: 0 2rpx 6rpx rgba(10, 132, 255, 0.1);
}
.verified-icon-badge {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #0A84FF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6rpx;
}
.verified-icon-badge svg {
  color: white;
}
.user-badge.premium {
  background-color: rgba(255, 45, 85, 0.1);
  color: #FF2D55;
  border: 1px solid rgba(255, 45, 85, 0.3);
}
.user-meta {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 车辆信息 */
.car-info {
  display: flex;
  flex-wrap: wrap;
}
.car-item {
  width: 50%;
  margin-bottom: 16rpx;
}
.car-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
  display: block;
}
.car-value {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

/* 补充说明 */
.remark-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 行程详情 */
.trip-details-header {
  margin-bottom: 24rpx;
  border-bottom: 1px solid #F2F2F7;
  padding-bottom: 16rpx;
}
.trip-details-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.trip-details-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 8rpx 0;
  border-bottom: 1px solid #F9F9F9;
}
.trip-details-label {
  font-size: 28rpx;
  color: #8E8E93;
  flex: 1;
}
.trip-details-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  flex: 2;
  text-align: right;
}

/* 免责声明 */
.disclaimer-content {
  display: flex;
  align-items: flex-start;
}
.disclaimer-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.disclaimer-icon image {
  width: 100%;
  height: 100%;
}
.disclaimer-text {
  font-size: 26rpx;
  color: #8E8E93;
  line-height: 1.6;
  flex: 1;
}

/* 发布时间 */
.publish-time-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(10, 132, 255, 0.08);
  width: calc(100% - 48rpx);
  box-sizing: border-box;
  overflow: hidden;
}
.publish-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.publish-time text {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
  border-top: 1px solid #f5f5f5;
}
.action-group {
  display: flex;
  align-items: center;
  flex: 1;
}
.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  flex: 1;
  background: transparent;
  border: none;
  box-shadow: none;
  height: 100rpx;
}
.action-btn::after {
  border: none;
}
.action-btn image {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 8rpx;
}
.action-btn text {
  font-size: 22rpx;
  color: #666666;
  line-height: 1;
}
.call-btn-container {
  display: flex;
  justify-content: flex-end;
  width: 240rpx;
}
.call-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  border-radius: 50rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  background-color: #1677FF;
  color: #ffffff;
  border: none;
  box-shadow: 0 4rpx 8rpx rgba(22, 119, 255, 0.2);
  width: 100%;
}
.call-btn image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.call-btn::after {
  border: none;
}
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}

/* 司机评分 */
.driver-rating {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1px solid #F2F2F7;
}
.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.rating-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.rating-count {
  font-size: 24rpx;
  color: #8E8E93;
}
.rating-stars {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.star-container {
  position: relative;
  width: 240rpx;
  height: 36rpx;
  margin-right: 16rpx;
}
.star-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/png;base64,");
  background-size: 240rpx 36rpx;
  background-repeat: no-repeat;
}
.star-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-image: url("data:image/png;base64,");
  background-size: 240rpx 36rpx;
  background-repeat: no-repeat;
  z-index: 1;
}
.rating-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF9500;
}
.rating-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.rating-tag {
  font-size: 24rpx;
  color: #666666;
  background-color: #F2F7FD;
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
}