/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-f7d85fe7, html.data-v-f7d85fe7, #app.data-v-f7d85fe7, .index-container.data-v-f7d85fe7 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.distribution-container.data-v-f7d85fe7 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar.data-v-f7d85fe7 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-f7d85fe7 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(172, 57, 255, 0.15);
}
.custom-navbar .navbar-content.data-v-f7d85fe7 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
}
.custom-navbar .navbar-title.data-v-f7d85fe7 {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
}
.custom-navbar .back-btn.data-v-f7d85fe7, .custom-navbar .help-btn.data-v-f7d85fe7 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-navbar .back-btn .icon.data-v-f7d85fe7, .custom-navbar .help-btn .icon.data-v-f7d85fe7 {
  width: 48rpx;
  height: 48rpx;
}

/* 内容区域 */
.content-scroll.data-v-f7d85fe7 {
  position: absolute;
  top: calc(var(--status-bar-height, 25px) + 62px);
  left: 0;
  right: 0;
  bottom: 100rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 底部安全区域 */
.safe-area-bottom.data-v-f7d85fe7 {
  height: 34px;
  /* iOS 安全区域高度 */
}

/* 分销员信息卡片 */
.distributor-card.data-v-f7d85fe7 {
  display: flex;
  flex-direction: column;
}
.distributor-card .distributor-header.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.distributor-card .distributor-header .distributor-avatar-container.data-v-f7d85fe7 {
  position: relative;
  margin-right: 20rpx;
}
.distributor-card .distributor-header .distributor-avatar-container .distributor-avatar.data-v-f7d85fe7 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 3rpx solid #FFFFFF;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.distributor-card .distributor-header .distributor-avatar-container .distributor-badge.data-v-f7d85fe7 {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #FFFFFF;
}
.distributor-card .distributor-header .distributor-info.data-v-f7d85fe7 {
  flex: 1;
}
.distributor-card .distributor-header .distributor-info .distributor-name-container.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.distributor-card .distributor-header .distributor-info .distributor-name-container .distributor-name.data-v-f7d85fe7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 10rpx;
}
.distributor-card .distributor-header .distributor-info .distributor-name-container .distributor-level.data-v-f7d85fe7 {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.distributor-card .distributor-header .distributor-info .distributor-name-container .distributor-level text.data-v-f7d85fe7 {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 500;
}
.distributor-card .distributor-header .distributor-info .distributor-id.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 10rpx;
}
.distributor-card .distributor-header .distributor-info .distributor-stats.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
}
.distributor-card .distributor-header .distributor-info .distributor-stats .stat-item.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
}
.distributor-card .distributor-header .distributor-info .distributor-stats .stat-divider.data-v-f7d85fe7 {
  margin: 0 10rpx;
  font-size: 24rpx;
  color: #D1D1D6;
}
.distributor-card .distributor-header .qrcode-btn.data-v-f7d85fe7 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
}
.distributor-card .distributor-header .qrcode-btn .icon.data-v-f7d85fe7 {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 5rpx;
}
.distributor-card .distributor-header .qrcode-btn text.data-v-f7d85fe7 {
  font-size: 20rpx;
  color: #FFFFFF;
}
.distributor-card .level-progress.data-v-f7d85fe7 {
  margin-top: 10rpx;
}
.distributor-card .level-progress .level-progress-header.data-v-f7d85fe7 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.distributor-card .level-progress .level-progress-header .level-text.data-v-f7d85fe7, .distributor-card .level-progress .level-progress-header .next-level-text.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #333333;
}
.distributor-card .level-progress .progress-hint.data-v-f7d85fe7 {
  font-size: 22rpx;
  color: #8E8E93;
  margin-top: 10rpx;
  text-align: center;
}

/* 收益概览卡片 */
.earnings-card .card-header.data-v-f7d85fe7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.earnings-card .card-header .card-title.data-v-f7d85fe7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.earnings-card .card-header .header-right.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
}
.earnings-card .card-header .header-right .view-all.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #AC39FF;
  margin-right: 5rpx;
}
.earnings-card .card-header .header-right .icon.data-v-f7d85fe7 {
  width: 24rpx;
  height: 24rpx;
}
.earnings-card .total-earnings.data-v-f7d85fe7 {
  text-align: center;
  margin-bottom: 30rpx;
}
.earnings-card .total-earnings .earnings-label.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}
.earnings-card .total-earnings .earnings-label text.data-v-f7d85fe7 {
  font-size: 28rpx;
  color: #8E8E93;
  margin-right: 5rpx;
}
.earnings-card .total-earnings .earnings-label .icon.data-v-f7d85fe7 {
  width: 28rpx;
  height: 28rpx;
}
.earnings-card .total-earnings .earnings-value.data-v-f7d85fe7 {
  font-size: 48rpx;
  font-weight: 700;
  color: #AC39FF;
  margin-bottom: 10rpx;
}
.earnings-card .total-earnings .earnings-trend .trend-value.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
}
.earnings-card .total-earnings .earnings-trend .trend-up.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #FF3B30;
}
.earnings-card .total-earnings .earnings-trend .trend-down.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #34C759;
}
.earnings-card .earnings-grid.data-v-f7d85fe7 {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}
.earnings-card .earnings-grid .earnings-grid-item.data-v-f7d85fe7 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.earnings-card .earnings-grid .earnings-grid-item .grid-value.data-v-f7d85fe7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 5rpx;
}
.earnings-card .earnings-grid .earnings-grid-item .grid-label.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
}
.earnings-card .action-buttons.data-v-f7d85fe7 {
  display: flex;
  justify-content: space-between;
}
.earnings-card .action-buttons .action-btn.data-v-f7d85fe7 {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
}
.earnings-card .action-buttons .action-btn text.data-v-f7d85fe7 {
  font-size: 28rpx;
  font-weight: 500;
}
.earnings-card .action-buttons .action-btn.withdraw-btn.data-v-f7d85fe7 {
  margin-right: 15rpx;
}
.earnings-card .action-buttons .action-btn.withdraw-btn text.data-v-f7d85fe7 {
  color: #FFFFFF;
}
.earnings-card .action-buttons .action-btn.record-btn.data-v-f7d85fe7 {
  border: 2rpx solid #AC39FF;
}
.earnings-card .action-buttons .action-btn.record-btn text.data-v-f7d85fe7 {
  color: #AC39FF;
}

/* 功能入口 */
.feature-grid.data-v-f7d85fe7 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.feature-grid .feature-item.data-v-f7d85fe7 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.feature-grid .feature-item .feature-icon.data-v-f7d85fe7 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}
.feature-grid .feature-item .feature-icon .icon.data-v-f7d85fe7 {
  width: 40rpx;
  height: 40rpx;
}
.feature-grid .feature-item .feature-name.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 5rpx;
}
.feature-grid .feature-item .feature-count.data-v-f7d85fe7 {
  font-size: 20rpx;
  color: #8E8E93;
}

/* 卡片头部样式 */
.card-header.data-v-f7d85fe7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.card-header .card-title.data-v-f7d85fe7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.card-header .header-right.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
}
.card-header .header-right .view-all.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #AC39FF;
  margin-right: 5rpx;
}
.card-header .header-right .icon.data-v-f7d85fe7 {
  width: 24rpx;
  height: 24rpx;
}
.card-header .view-all-btn.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(172, 57, 255, 0.25);
  transition: all 0.3s ease;
}
.card-header .view-all-btn.data-v-f7d85fe7:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(172, 57, 255, 0.15);
}
.card-header .view-all-btn text.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 500;
  margin-right: 5rpx;
}
.card-header .view-all-btn .icon.data-v-f7d85fe7 {
  width: 24rpx;
  height: 24rpx;
}

/* 订单卡片 */
.orders-card.data-v-f7d85fe7 {
  margin-bottom: 30rpx;
}
.orders-card .order-tabs.data-v-f7d85fe7 {
  display: flex;
  overflow-x: auto;
  margin: 20rpx 0;
}
.orders-card .order-tabs .order-tab.data-v-f7d85fe7 {
  padding: 10rpx 20rpx;
  position: relative;
  white-space: nowrap;
}
.orders-card .order-tabs .order-tab text.data-v-f7d85fe7 {
  font-size: 28rpx;
  color: #8E8E93;
  transition: color 0.3s ease;
}
.orders-card .order-tabs .order-tab .tab-indicator.data-v-f7d85fe7 {
  position: absolute;
  bottom: -5rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 3px;
  border-radius: 1.5px;
  background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);
  transition: all 0.3s ease;
}
.orders-card .order-tabs .order-tab.active text.data-v-f7d85fe7 {
  color: #AC39FF;
  font-weight: 500;
}
.orders-card .order-list .order-item.data-v-f7d85fe7 {
  background: #F9F9F9;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}
.orders-card .order-list .order-item.data-v-f7d85fe7:active {
  transform: scale(0.98);
}
.orders-card .order-list .order-item .order-header.data-v-f7d85fe7 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}
.orders-card .order-list .order-item .order-header .order-id.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
}
.orders-card .order-list .order-item .order-header .order-status.data-v-f7d85fe7 {
  font-size: 24rpx;
  font-weight: 500;
}
.orders-card .order-list .order-item .order-product.data-v-f7d85fe7 {
  display: flex;
  margin-bottom: 15rpx;
}
.orders-card .order-list .order-item .order-product .product-image.data-v-f7d85fe7 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 15rpx;
}
.orders-card .order-list .order-item .order-product .product-info.data-v-f7d85fe7 {
  flex: 1;
}
.orders-card .order-list .order-item .order-product .product-info .product-name.data-v-f7d85fe7 {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.orders-card .order-list .order-item .order-product .product-info .product-price-qty.data-v-f7d85fe7 {
  display: flex;
  justify-content: space-between;
}
.orders-card .order-list .order-item .order-product .product-info .product-price-qty .product-price.data-v-f7d85fe7 {
  font-size: 28rpx;
  color: #FF3B69;
  font-weight: 600;
}
.orders-card .order-list .order-item .order-product .product-info .product-price-qty .product-qty.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
}
.orders-card .order-list .order-item .order-footer.data-v-f7d85fe7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.orders-card .order-list .order-item .order-footer .order-time.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
}
.orders-card .order-list .order-item .order-footer .order-commission.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
}
.orders-card .order-list .order-item .order-footer .order-commission .commission-label.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
}
.orders-card .order-list .order-item .order-footer .order-commission .commission-value.data-v-f7d85fe7 {
  font-size: 28rpx;
  color: #FF3B69;
  font-weight: 600;
}

/* 团队卡片 */
.team-card.data-v-f7d85fe7 {
  margin-bottom: 30rpx;
}
.team-card .team-stats.data-v-f7d85fe7 {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}
.team-card .team-stats .team-stat-item.data-v-f7d85fe7 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.team-card .team-stats .team-stat-item .stat-value.data-v-f7d85fe7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 5rpx;
}
.team-card .team-stats .team-stat-item .stat-label.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
}
.team-card .team-members .section-subtitle.data-v-f7d85fe7 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}
.team-card .team-members .member-list .member-item.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #F2F2F7;
  margin-bottom: 20rpx;
  background: #F9F9F9;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}
.team-card .team-members .member-list .member-item.data-v-f7d85fe7:active {
  transform: scale(0.98);
}
.team-card .team-members .member-list .member-item.data-v-f7d85fe7:last-child {
  border-bottom: none;
}
.team-card .team-members .member-list .member-item .member-avatar.data-v-f7d85fe7 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}
.team-card .team-members .member-list .member-item .member-info.data-v-f7d85fe7 {
  flex: 1;
}
.team-card .team-members .member-list .member-item .member-info .member-name-level.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.team-card .team-members .member-list .member-item .member-info .member-name-level .member-name.data-v-f7d85fe7 {
  font-size: 28rpx;
  color: #333333;
  margin-right: 10rpx;
}
.team-card .team-members .member-list .member-item .member-info .member-name-level .member-level.data-v-f7d85fe7 {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.team-card .team-members .member-list .member-item .member-info .member-name-level .member-level text.data-v-f7d85fe7 {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 500;
}
.team-card .team-members .member-list .member-item .member-info .member-stats.data-v-f7d85fe7 {
  display: flex;
  align-items: center;
}
.team-card .team-members .member-list .member-item .member-info .member-stats .member-stat.data-v-f7d85fe7 {
  font-size: 24rpx;
  color: #8E8E93;
}
.team-card .team-members .member-list .member-item .member-info .member-stats .member-stat-divider.data-v-f7d85fe7 {
  margin: 0 10rpx;
  font-size: 24rpx;
  color: #D1D1D6;
}
.team-card .team-members .member-list .member-item .arrow-icon.data-v-f7d85fe7 {
  width: 32rpx;
  height: 32rpx;
}

/* 空状态 */
.empty-state.data-v-f7d85fe7 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background: #F9F9F9;
  border-radius: 20rpx;
  margin: 20rpx 0;
}
.empty-state .empty-image.data-v-f7d85fe7 {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}
.empty-state .empty-text.data-v-f7d85fe7 {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 30rpx;
  font-weight: 500;
}
.empty-state .action-btn.data-v-f7d85fe7 {
  padding: 16rpx 40rpx;
  border-radius: 35px;
  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
  box-shadow: 0 4rpx 8rpx rgba(172, 57, 255, 0.25);
}
.empty-state .action-btn text.data-v-f7d85fe7 {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
}
.empty-state .action-btn.data-v-f7d85fe7:active {
  opacity: 0.9;
  transform: scale(0.98);
  box-shadow: 0 2rpx 4rpx rgba(172, 57, 255, 0.15);
}

/* 底部导航栏 */
.tabbar.data-v-f7d85fe7 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 99;
  border-top: 1rpx solid #EEEEEE;
}
.tabbar-item.data-v-f7d85fe7 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}
.tabbar-item.data-v-f7d85fe7:active {
  transform: scale(0.9);
}
.tabbar-item.active .tab-icon.data-v-f7d85fe7 {
  transform: translateY(-5rpx);
}
.tabbar-item.active .tabbar-text.data-v-f7d85fe7 {
  color: #FF3B69;
  font-weight: 600;
  transform: translateY(-2rpx);
}
.tab-icon.data-v-f7d85fe7 {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 首页图标 */
.tab-icon.home.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=home] .tab-icon.home.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

/* 发现图标 */
.tab-icon.discover.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=discover] .tab-icon.discover.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

/* 分销图标 */
.tab-icon.distribution.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=distribution] .tab-icon.distribution.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

/* 消息图标 */
.tab-icon.message.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=message] .tab-icon.message.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 我的图标 */
.tab-icon.user.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.tabbar-item.active[data-tab=my] .tab-icon.user.data-v-f7d85fe7 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}
.tabbar-item.active .tab-icon.data-v-f7d85fe7 {
  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));
}
.badge.data-v-f7d85fe7 {
  position: absolute;
  top: -8rpx;
  right: -12rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #FF453A, #FF2D55);
  color: #FFFFFF;
  font-size: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  font-weight: 600;
  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transform: scale(0.9);
}
.tabbar-text.data-v-f7d85fe7 {
  font-size: 22rpx;
  color: #8E8E93;
  margin-top: 2rpx;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.tabbar-item.data-v-f7d85fe7::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 30rpx;
  height: 4rpx;
  background: #FF3B69;
  border-radius: 2rpx;
  transition: transform 0.3s ease;
}
.tabbar-item.active.data-v-f7d85fe7::after {
  transform: translateX(-50%) scaleX(1);
}