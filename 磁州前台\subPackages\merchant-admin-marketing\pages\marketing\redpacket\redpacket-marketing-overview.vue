<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包营销概述实践</text>
      <view class="navbar-right">
        <view class="share-icon" @click="shareGuide">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="content-scroll">
      <!-- 页面头部 -->
      <view class="page-header">
        <view class="header-bg" style="background: linear-gradient(135deg, #6EE7B7, #3B82F6);">
          <text class="header-title">了解如何有效使用红包</text>
          <text class="header-subtitle">提高转化率的营销秘籍</text>
        </view>
      </view>
      
      <!-- 内容部分 -->
      <view class="content-section">
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(110, 231, 183, 0.2);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6EE7B7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
              <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
              <line x1="12" y1="22.08" x2="12" y2="12"></line>
            </svg>
          </view>
          <text class="title-text">红包营销的基本原理</text>
        </view>
        
        <view class="content-text">
          <text>红包营销是一种基于用户心理的营销手段，通过发放现金或优惠券形式的红包，激发用户参与热情，提高转化率。红包营销利用了人们对免费和意外惊喜的心理需求，能够有效提升品牌曝光和用户粘性。</text>
        </view>
        
        <view class="content-image">
          <image src="/static/images/redpacket-principle.png" mode="widthFix"></image>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(110, 231, 183, 0.2);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6EE7B7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
            </svg>
          </view>
          <text class="title-text">红包营销的效果分析</text>
        </view>
        
        <view class="data-cards">
          <view class="data-card">
            <text class="data-value">68%</text>
            <text class="data-label">用户转化率提升</text>
          </view>
          <view class="data-card">
            <text class="data-value">3.5倍</text>
            <text class="data-label">用户停留时间增长</text>
          </view>
          <view class="data-card">
            <text class="data-value">42%</text>
            <text class="data-label">复购率提升</text>
          </view>
          <view class="data-card">
            <text class="data-value">56%</text>
            <text class="data-label">分享率提升</text>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(110, 231, 183, 0.2);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6EE7B7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </view>
          <text class="title-text">红包营销的应用场景</text>
        </view>
        
        <view class="scenario-list">
          <view class="scenario-item">
            <view class="scenario-icon" style="background-color: rgba(110, 231, 183, 0.2);">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6EE7B7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
            </view>
            <view class="scenario-content">
              <text class="scenario-title">新客获取</text>
              <text class="scenario-desc">通过发放新人红包，吸引潜在用户注册，降低获客成本</text>
            </view>
          </view>
          
          <view class="scenario-item">
            <view class="scenario-icon" style="background-color: rgba(110, 231, 183, 0.2);">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6EE7B7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </view>
            <view class="scenario-content">
              <text class="scenario-title">节日营销</text>
              <text class="scenario-desc">在重要节日发放红包，提升用户好感度和品牌认知</text>
            </view>
          </view>
          
          <view class="scenario-item">
            <view class="scenario-icon" style="background-color: rgba(110, 231, 183, 0.2);">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6EE7B7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            </view>
            <view class="scenario-content">
              <text class="scenario-title">社交裂变</text>
              <text class="scenario-desc">鼓励用户分享红包给好友，实现低成本获客</text>
            </view>
          </view>
          
          <view class="scenario-item">
            <view class="scenario-icon" style="background-color: rgba(110, 231, 183, 0.2);">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6EE7B7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </view>
            <view class="scenario-content">
              <text class="scenario-title">促进复购</text>
              <text class="scenario-desc">向已购买用户发放红包，鼓励再次消费</text>
            </view>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(110, 231, 183, 0.2);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6EE7B7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </view>
          <text class="title-text">红包营销的最佳实践</text>
        </view>
        
        <view class="practice-list">
          <view class="practice-item">
            <text class="practice-number">01</text>
            <view class="practice-content">
              <text class="practice-title">明确营销目标</text>
              <text class="practice-desc">在开始红包活动前，明确活动目标是获客、促活还是转化，针对不同目标设计不同的红包策略。</text>
            </view>
          </view>
          
          <view class="practice-item">
            <text class="practice-number">02</text>
            <view class="practice-content">
              <text class="practice-title">精准用户定向</text>
              <text class="practice-desc">根据用户画像和行为数据，向最有价值的用户群体投放红包，提高转化效率。</text>
            </view>
          </view>
          
          <view class="practice-item">
            <text class="practice-number">03</text>
            <view class="practice-content">
              <text class="practice-title">设计合理金额</text>
              <text class="practice-desc">根据商品价格和预期转化率，设计具有吸引力且经济合理的红包金额。</text>
            </view>
          </view>
          
          <view class="practice-item">
            <text class="practice-number">04</text>
            <view class="practice-content">
              <text class="practice-title">设置使用门槛</text>
              <text class="practice-desc">合理设置红包使用门槛，既能刺激消费又能保证商家利润。</text>
            </view>
          </view>
          
          <view class="practice-item">
            <text class="practice-number">05</text>
            <view class="practice-content">
              <text class="practice-title">数据追踪与优化</text>
              <text class="practice-desc">实时监控红包活动数据，根据效果及时调整策略，优化投放效果。</text>
            </view>
          </view>
        </view>
        
        <view class="section-title">
          <view class="title-icon" style="background-color: rgba(110, 231, 183, 0.2);">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#6EE7B7" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
          </view>
          <text class="title-text">案例分析</text>
        </view>
        
        <view class="case-study">
          <view class="case-header">
            <text class="case-title">某餐饮品牌新店开业红包案例</text>
          </view>
          <view class="case-content">
            <text class="case-desc">某连锁餐饮品牌在新店开业期间，通过发放"满50减20"的红包，并鼓励用户分享给好友，好友领取后双方各得5元红包。活动期间，新店日均客流量提升了65%，新客获取成本降低了40%，社交媒体曝光增加了3倍。</text>
          </view>
          <view class="case-results">
            <view class="result-item">
              <text class="result-label">客流提升</text>
              <text class="result-value">65%</text>
            </view>
            <view class="result-item">
              <text class="result-label">获客成本降低</text>
              <text class="result-value">40%</text>
            </view>
            <view class="result-item">
              <text class="result-label">社媒曝光增加</text>
              <text class="result-value">3倍</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 联系我们 -->
      <view class="contact-section">
        <text class="contact-title">需要更多帮助？</text>
        <text class="contact-desc">如果您对红包营销有任何疑问，请联系我们的客服团队</text>
        <button class="contact-btn" @click="contactService">联系客服</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    shareGuide() {
      uni.showActionSheet({
        itemList: ['分享给好友', '分享到朋友圈', '复制链接'],
        success: function(res) {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        }
      });
    },
    contactService() {
      uni.makePhoneCall({
        phoneNumber: '************'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}

.navbar-back {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-icon {
  color: #333;
}

/* 内容滚动区 */
.content-scroll {
  flex: 1;
}

/* 页面头部 */
.page-header {
  height: 180px;
  position: relative;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 10px;
}

.header-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

/* 内容部分 */
.content-section {
  padding: 20px 15px;
  background-color: #fff;
  border-radius: 15px 15px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 1;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  margin-top: 25px;
}

.section-title:first-child {
  margin-top: 0;
}

.title-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.content-text {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.content-image {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.content-image image {
  width: 100%;
}

/* 数据卡片 */
.data-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px 20px;
}

.data-card {
  width: calc(50% - 10px);
  margin: 5px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.data-value {
  font-size: 20px;
  font-weight: 700;
  color: #6EE7B7;
  display: block;
  margin-bottom: 5px;
}

.data-label {
  font-size: 13px;
  color: #666;
}

/* 场景列表 */
.scenario-list {
  margin-bottom: 20px;
}

.scenario-item {
  display: flex;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.scenario-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.scenario-content {
  flex: 1;
}

.scenario-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.scenario-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 最佳实践 */
.practice-list {
  margin-bottom: 20px;
}

.practice-item {
  display: flex;
  margin-bottom: 15px;
}

.practice-number {
  font-size: 18px;
  font-weight: 700;
  color: #6EE7B7;
  margin-right: 15px;
}

.practice-content {
  flex: 1;
}

.practice-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.practice-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 案例分析 */
.case-study {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.case-header {
  margin-bottom: 10px;
}

.case-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.case-content {
  margin-bottom: 15px;
}

.case-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.case-results {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.result-item {
  text-align: center;
  flex: 1;
}

.result-label {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 5px;
}

.result-value {
  font-size: 18px;
  font-weight: 600;
  color: #6EE7B7;
}

/* 联系我们 */
.contact-section {
  margin: 0 15px 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.contact-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.contact-desc {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 15px;
}

.contact-btn {
  background-color: #6EE7B7;
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
}
</style>