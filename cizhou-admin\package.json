{"name": "cizhou-admin", "version": "1.0.0", "description": "磁州生活网后台管理系统 - 企业级微服务管理平台", "main": "index.js", "scripts": {"dev": "npm run dev:frontend & npm run dev:backend", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && mvn spring-boot:run", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && mvn clean package", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && mvn test", "lint": "npm run lint:frontend", "lint:frontend": "cd frontend && npm run lint", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup": "npm run setup:frontend && npm run setup:backend", "setup:frontend": "cd frontend && npm install", "setup:backend": "cd backend && mvn clean install"}, "keywords": ["cizhou", "admin", "management", "vue3", "spring-boot", "microservices", "enterprise"], "author": "Cizhou Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cizhou/admin-system.git"}, "bugs": {"url": "https://github.com/cizhou/admin-system/issues"}, "homepage": "https://github.com/cizhou/admin-system#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "workspaces": ["frontend", "backend"]}