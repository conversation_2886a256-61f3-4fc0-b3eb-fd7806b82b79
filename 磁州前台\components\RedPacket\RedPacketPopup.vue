<template>
  <view class="red-packet-popup" :class="{ show: show }">
    <view class="mask" @click="handleClose"></view>
    
    <!-- 红包主体内容 -->
    <view class="red-packet-container" :class="{ 'animation-scale': show }">
      <!-- 关闭按钮 -->
      <view class="close-btn" @click="handleClose">
        <text class="icon">×</text>
      </view>
      
      <!-- 展示不同状态的红包 -->
      <block v-if="currentStep === 'grab'">
        <view class="red-packet-grab">
          <view class="red-packet-top">
            <view class="packet-header">
              <image :src="redPacket.sender?.avatar || '/static/images/default-avatar.png'" class="sender-avatar"></image>
              <view class="packet-info">
                <text class="sender-name">{{ redPacket.sender?.nickname || '用户' }}的红包</text>
                <text class="packet-title">{{ redPacket.title || '恭喜发财，大吉大利' }}</text>
              </view>
            </view>
            
            <view class="packet-envelope" @click="openRedPacket">
              <image src="/static/images/red-packet-envelope.png" class="envelope-image"></image>
              <view class="open-text">点击拆开</view>
            </view>
          </view>
        </view>
      </block>
      
      <block v-else-if="currentStep === 'opening'">
        <view class="red-packet-opening">
          <view class="opening-animation">
            <image src="/static/images/red-packet-opening.gif" mode="aspectFit"></image>
          </view>
          <text class="opening-text">正在拆红包...</text>
        </view>
      </block>
      
      <block v-else-if="currentStep === 'result'">
        <view class="red-packet-result">
          <view class="result-header">
            <image :src="redPacket.sender?.avatar || '/static/images/default-avatar.png'" class="sender-avatar"></image>
            <view class="result-info">
              <text class="packet-desc">{{ redPacket.sender?.nickname || '用户' }}的红包</text>
              <text class="packet-title">{{ redPacket.title || '恭喜发财，大吉大利' }}</text>
            </view>
          </view>
          
          <view class="result-content">
            <view v-if="grabResult.status === 'success'" class="result-success">
              <image src="/static/images/red-packet-success.png" class="result-icon"></image>
              <text class="amount-text">{{ formatAmount(grabResult.amount) }}</text>
              <text class="amount-unit">元</text>
              <text class="result-tips">已存入钱包，可直接提现</text>
            </view>
            
            <view v-else-if="grabResult.status === 'fail'" class="result-fail">
              <image src="/static/images/red-packet-fail.png" class="result-icon"></image>
              <text class="fail-text">{{ grabResult.message }}</text>
            </view>
          </view>
          
          <view class="action-area">
            <button 
              class="action-btn check-wallet"
              v-if="grabResult.status === 'success'"
              @click="goToWallet"
            >查看钱包</button>
            
            <button 
              v-else
              class="action-btn know"
              @click="handleClose"
            >我知道了</button>
          </view>
        </view>
      </block>
      
      <block v-else-if="currentStep === 'detail'">
        <view class="red-packet-detail">
          <view class="detail-header">
            <image :src="redPacket.sender?.avatar || '/static/images/default-avatar.png'" class="sender-avatar"></image>
            <view class="detail-info">
              <text class="packet-desc">{{ redPacket.sender?.nickname || '用户' }}的红包</text>
              <text class="packet-title">{{ redPacket.title || '恭喜发财，大吉大利' }}</text>
            </view>
          </view>
          
          <view class="detail-summary">
            <text class="summary-text">已领取 {{ redPacket.totalCount - redPacket.remainCount || 0 }}/{{ redPacket.totalCount || 0 }} 个，共 {{ formatAmount(redPacket.totalAmount - redPacket.remainAmount) }}/{{ formatAmount(redPacket.totalAmount) }} 元</text>
          </view>
          
          <scroll-view 
            class="grab-records"
            scroll-y 
            enable-flex
          >
            <block v-if="records && records.length">
              <view
                v-for="(record, index) in records"
                :key="index"
                class="record-item"
              >
                <view class="record-user">
                  <image :src="record.avatar || '/static/images/default-avatar.png'" class="user-avatar"></image>
                  <text class="user-name">{{ record.nickname || '用户' }}</text>
                </view>
                
                <view class="record-amount">
                  <text class="amount-value">{{ formatAmount(record.amount) }}</text>
                  <text class="amount-unit">元</text>
                  <text v-if="record.isBest" class="best-tag">手气最佳</text>
                </view>
                
                <text class="record-time">{{ formatTime(record.grabTime) }}</text>
              </view>
            </block>
            
            <view v-else class="no-records">
              <text>暂无领取记录</text>
            </view>
          </scroll-view>
          
          <view class="action-area">
            <button class="action-btn know" @click="handleClose">我知道了</button>
          </view>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
import { formatRedPacketAmount } from '@/utils/redPacket.js';
import { grabRedPacket } from '@/utils/redPacket.js';
import { formatDate } from '@/utils/date.js';

export default {
  name: 'RedPacketPopup',
  props: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      default: false
    },
    // 红包数据
    redPacket: {
      type: Object,
      default: () => ({})
    },
    // 红包记录
    records: {
      type: Array,
      default: () => []
    },
    // 初始步骤
    initialStep: {
      type: String,
      default: 'grab' // grab, opening, result, detail
    }
  },
  data() {
    return {
      currentStep: 'grab',
      grabResult: {
        status: '',
        amount: 0,
        message: ''
      }
    };
  },
  watch: {
    show(val) {
      if (val) {
        this.currentStep = this.initialStep;
      }
    },
    initialStep(val) {
      if (this.show) {
        this.currentStep = val;
      }
    }
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.$emit('close');
    },
    
    // 打开红包
    openRedPacket() {
      this.currentStep = 'opening';
      
      // 调用抢红包接口
      grabRedPacket(this.redPacket.id).then(res => {
        setTimeout(() => {
          this.grabResult = {
            status: 'success',
            amount: res.amount,
            message: ''
          };
          this.currentStep = 'result';
          this.$emit('grab-success', res);
        }, 1500); // 延迟展示结果，增强体验
      }).catch(err => {
        setTimeout(() => {
          this.grabResult = {
            status: 'fail',
            amount: 0,
            message: err.message || '红包已抢完'
          };
          this.currentStep = 'result';
        }, 1500);
      });
    },
    
    // 格式化金额
    formatAmount(amount) {
      return formatRedPacketAmount(amount);
    },
    
    // 格式化时间
    formatTime(timestamp) {
      return formatDate(timestamp, 'MM-DD HH:mm');
    },
    
    // 跳转到钱包
    goToWallet() {
      uni.navigateTo({
        url: '/subPackages/payment/pages/wallet'
      });
      this.handleClose();
    }
  }
}
</script>

<style lang="scss">
.red-packet-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
  
  .mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
  }
  
  .red-packet-container {
    position: relative;
    width: 650rpx;
    max-height: 80vh;
    z-index: 1001;
    transform: scale(0.8);
    transition: transform 0.3s;
    
    &.animation-scale {
      transform: scale(1);
    }
    
    .close-btn {
      position: absolute;
      top: -80rpx;
      right: 0;
      width: 60rpx;
      height: 60rpx;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10;
      
      .icon {
        color: #fff;
        font-size: 40rpx;
        line-height: 1;
      }
    }
  }
  
  /* 抢红包阶段样式 */
  .red-packet-grab {
    width: 100%;
    background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 30rpx rgba(255, 8, 68, 0.3);
    
    .red-packet-top {
      padding: 40rpx 30rpx;
      
      .packet-header {
        display: flex;
        align-items: center;
        margin-bottom: 40rpx;
        
        .sender-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          border: 4rpx solid rgba(255, 255, 255, 0.3);
          margin-right: 20rpx;
        }
        
        .packet-info {
          color: #fff;
          
          .sender-name {
            display: block;
            font-size: 28rpx;
            margin-bottom: 8rpx;
            opacity: 0.9;
          }
          
          .packet-title {
            display: block;
            font-size: 36rpx;
            font-weight: 600;
          }
        }
      }
      
      .packet-envelope {
        width: 100%;
        height: 600rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        
        .envelope-image {
          width: 400rpx;
          height: 400rpx;
          transform: rotate(-5deg);
        }
        
        .open-text {
          position: absolute;
          bottom: 40rpx;
          color: #FFEE95;
          font-size: 36rpx;
          font-weight: 600;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
          animation: pulse 1.5s infinite;
        }
      }
    }
  }
  
  /* 正在打开红包样式 */
  .red-packet-opening {
    width: 100%;
    height: 750rpx;
    background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .opening-animation {
      width: 300rpx;
      height: 300rpx;
      margin-bottom: 40rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .opening-text {
      color: #fff;
      font-size: 36rpx;
      font-weight: 500;
    }
  }
  
  /* 红包结果样式 */
  .red-packet-result {
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx;
    overflow: hidden;
    
    .result-header {
      padding: 40rpx 30rpx;
      background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
      display: flex;
      align-items: center;
      
      .sender-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
        margin-right: 20rpx;
      }
      
      .result-info {
        color: #fff;
        
        .packet-desc {
          display: block;
          font-size: 28rpx;
          margin-bottom: 8rpx;
          opacity: 0.9;
        }
        
        .packet-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
        }
      }
    }
    
    .result-content {
      padding: 60rpx 30rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .result-success {
        text-align: center;
        
        .result-icon {
          width: 120rpx;
          height: 120rpx;
          margin-bottom: 30rpx;
        }
        
        .amount-text {
          font-size: 80rpx;
          font-weight: 600;
          color: #FF3B30;
          font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .amount-unit {
          font-size: 36rpx;
          color: #FF3B30;
          margin-left: 8rpx;
        }
        
        .result-tips {
          display: block;
          margin-top: 24rpx;
          font-size: 28rpx;
          color: #999;
        }
      }
      
      .result-fail {
        text-align: center;
        
        .result-icon {
          width: 120rpx;
          height: 120rpx;
          margin-bottom: 30rpx;
        }
        
        .fail-text {
          font-size: 32rpx;
          color: #666;
        }
      }
    }
    
    .action-area {
      padding: 0 30rpx 40rpx;
      display: flex;
      justify-content: center;
      
      .action-btn {
        width: 320rpx;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 40rpx;
        font-size: 30rpx;
        font-weight: 500;
        
        &.check-wallet {
          background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
          color: #fff;
        }
        
        &.know {
          background-color: #F5F5F5;
          color: #666;
        }
      }
    }
  }
  
  /* 红包详情样式 */
  .red-packet-detail {
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx;
    overflow: hidden;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    
    .detail-header {
      padding: 40rpx 30rpx;
      background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
      display: flex;
      align-items: center;
      
      .sender-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
        margin-right: 20rpx;
      }
      
      .detail-info {
        color: #fff;
        
        .packet-desc {
          display: block;
          font-size: 28rpx;
          margin-bottom: 8rpx;
          opacity: 0.9;
        }
        
        .packet-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
        }
      }
    }
    
    .detail-summary {
      padding: 24rpx 30rpx;
      border-bottom: 1px solid #F0F0F0;
      
      .summary-text {
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .grab-records {
      flex: 1;
      max-height: 600rpx;
      padding: 0 30rpx;
      
      .record-item {
        display: flex;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1px solid #F5F5F5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .record-user {
          display: flex;
          align-items: center;
          flex: 1;
          
          .user-avatar {
            width: 64rpx;
            height: 64rpx;
            border-radius: 50%;
            margin-right: 16rpx;
          }
          
          .user-name {
            font-size: 28rpx;
            color: #333;
            max-width: 200rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        
        .record-amount {
          display: flex;
          align-items: center;
          
          .amount-value {
            font-size: 36rpx;
            color: #FF3B30;
            font-weight: 600;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
          }
          
          .amount-unit {
            font-size: 24rpx;
            color: #FF3B30;
            margin-left: 4rpx;
          }
          
          .best-tag {
            margin-left: 12rpx;
            font-size: 20rpx;
            color: #fff;
            background-color: #FF9500;
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
          }
        }
        
        .record-time {
          font-size: 24rpx;
          color: #999;
          margin-left: 24rpx;
        }
      }
      
      .no-records {
        padding: 80rpx 0;
        text-align: center;
        color: #999;
        font-size: 28rpx;
      }
    }
    
    .action-area {
      padding: 24rpx 30rpx;
      display: flex;
      justify-content: center;
      border-top: 1px solid #F0F0F0;
      
      .action-btn {
        width: 320rpx;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 40rpx;
        font-size: 30rpx;
        font-weight: 500;
        
        &.know {
          background-color: #F5F5F5;
          color: #666;
        }
      }
    }
  }
}

/* 红包动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style> 