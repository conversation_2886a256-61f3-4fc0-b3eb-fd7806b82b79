{"version": 3, "file": "index.js", "sources": ["subPackages/payment/pages/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGF5bWVudFxwYWdlc1xpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"wallet-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">我的钱包</view>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 账户余额卡片 -->\r\n    <view class=\"balance-card\" :style=\"{ marginTop: (navbarHeight + 10) + 'px' }\">\r\n      <view class=\"balance-title\">账户余额 (元)</view>\r\n      <view class=\"balance-amount\">{{ balanceInfo.amount.toFixed(2) }}</view>\r\n      <view class=\"balance-buttons\">\r\n        <button class=\"balance-btn withdraw-btn\" @click=\"navigateToWithdraw\">提现</button>\r\n        <button class=\"balance-btn recharge-btn\" @click=\"navigateToRecharge\">充值</button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 钱包功能区 -->\r\n    <view class=\"wallet-functions\">\r\n      <view class=\"function-item\" @click=\"navigateTo('/subPackages/payment/pages/detail')\">\r\n        <view class=\"function-left\">\r\n          <image class=\"function-icon\" src=\"/static/images/tabbar/钱包明细.png\"></image>\r\n          <text class=\"function-name\">钱包明细</text>\r\n        </view>\r\n        <image class=\"arrow-icon\" src=\"/static/images/tabbar/arrow-up.png\"></image>\r\n      </view>\r\n      \r\n      <view class=\"function-item\" @click=\"navigateTo('/subPackages/payment/pages/bills')\">\r\n        <view class=\"function-left\">\r\n          <image class=\"function-icon\" src=\"/static/images/tabbar/收支记录.png\"></image>\r\n          <text class=\"function-name\">收支记录</text>\r\n        </view>\r\n        <image class=\"arrow-icon\" src=\"/static/images/tabbar/arrow-up.png\"></image>\r\n      </view>\r\n      \r\n      <view class=\"function-item\" @click=\"navigateTo('/subPackages/payment/pages/bank')\">\r\n        <view class=\"function-left\">\r\n          <image class=\"function-icon\" src=\"/static/images/tabbar/银行卡.png\"></image>\r\n          <text class=\"function-name\">银行卡管理</text>\r\n        </view>\r\n        <image class=\"arrow-icon\" src=\"/static/images/tabbar/arrow-up.png\"></image>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 交易记录 -->\r\n    <view class=\"transaction-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">近期交易</text>\r\n        <text class=\"section-more\" @click=\"navigateTo('/subPackages/payment/pages/bills')\">查看更多</text>\r\n      </view>\r\n      \r\n      <view class=\"transaction-list\">\r\n        <view v-if=\"transactions.length > 0\">\r\n          <view class=\"transaction-item\" v-for=\"(item, index) in transactions\" :key=\"index\">\r\n            <view class=\"transaction-left\">\r\n              <view class=\"transaction-title\">{{ item.title }}</view>\r\n              <view class=\"transaction-time\">{{ item.time }}</view>\r\n            </view>\r\n            <view class=\"transaction-amount\" :class=\"{'income': item.type === 'income', 'expense': item.type === 'expense'}\">\r\n              {{ item.type === 'income' ? '+' : '-' }}{{ item.amount.toFixed(2) }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view v-else class=\"empty-view\">\r\n          <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n          <view class=\"empty-text\">暂无交易记录</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\nimport { smartNavigate } from '@/utils/navigation.js';\r\n\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst navbarHeight = ref(64);\r\nconst balanceInfo = ref({\r\n  amount: 0.00,\r\n  frozenAmount: 0.00\r\n});\r\nconst transactions = ref([\r\n  {\r\n    id: 'tx001',\r\n    title: '充值',\r\n    time: '2023-11-05 14:30',\r\n    amount: 100.00,\r\n    type: 'income'\r\n  },\r\n  {\r\n    id: 'tx002',\r\n    title: '服务支付',\r\n    time: '2023-11-03 09:15',\r\n    amount: 35.00,\r\n    type: 'expense'\r\n  },\r\n  {\r\n    id: 'tx003',\r\n    title: '提现',\r\n    time: '2023-10-28 16:22',\r\n    amount: 50.00,\r\n    type: 'expense'\r\n  }\r\n]);\r\n\r\n// 方法\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 页面跳转\r\nconst navigateTo = (url) => {\r\n  smartNavigate(url).catch(err => {\r\n    console.error('页面跳转失败:', err);\r\n  });\r\n};\r\n\r\n// 跳转到提现页面\r\nconst navigateToWithdraw = () => {\r\n  navigateTo('/subPackages/payment/pages/withdraw');\r\n};\r\n\r\n// 跳转到充值页面\r\nconst navigateToRecharge = () => {\r\n  navigateTo('/subPackages/payment/pages/recharge');\r\n};\r\n\r\n// 获取钱包余额\r\nconst getWalletBalance = () => {\r\n  // 这里应该是从API获取钱包余额\r\n  // 模拟API请求\r\n  setTimeout(() => {\r\n    balanceInfo.value = {\r\n      amount: 158.50,\r\n      frozenAmount: 0.00\r\n    };\r\n  }, 500);\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取状态栏高度\r\n  const sysInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = sysInfo.statusBarHeight;\r\n  navbarHeight.value = statusBarHeight.value + 44;\r\n  \r\n  // 获取钱包余额\r\n  getWalletBalance();\r\n});\r\n</script>\r\n\r\n<style>\r\n.wallet-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  z-index: 999;\r\n}\r\n\r\n.navbar-left {\r\n  width: 80rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.navbar-right {\r\n  width: 80rpx;\r\n}\r\n\r\n/* 余额卡片 */\r\n.balance-card {\r\n  background: linear-gradient(to right, #0052CC, #0066FF);\r\n  margin: 0 30rpx;\r\n  padding: a0rpx;\r\n  border-radius: 20rpx;\r\n  color: #fff;\r\n  overflow: hidden;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 82, 204, 0.2);\r\n}\r\n\r\n.balance-title {\r\n  font-size: 28rpx;\r\n  opacity: 0.9;\r\n  margin: 30rpx 0 20rpx 40rpx;\r\n}\r\n\r\n.balance-amount {\r\n  font-size: 60rpx;\r\n  font-weight: bold;\r\n  margin: 0 0 40rpx 40rpx;\r\n}\r\n\r\n.balance-buttons {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  margin: 30rpx 40rpx 40rpx;\r\n}\r\n\r\n.balance-btn {\r\n  width: 240rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 28rpx;\r\n  border-radius: 40rpx;\r\n  margin: 0;\r\n}\r\n\r\n.withdraw-btn {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  color: #fff;\r\n  border: 1px solid rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.recharge-btn {\r\n  background-color: #fff;\r\n  color: #0052CC;\r\n}\r\n\r\n/* 钱包功能区 */\r\n.wallet-functions {\r\n  background-color: #fff;\r\n  margin: 30rpx;\r\n  border-radius: 20rpx;\r\n  padding: 20rpx 0;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.function-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #f5f5f5;\r\n}\r\n\r\n.function-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.function-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.function-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  margin-right: 20rpx;\r\n  background-color: #f8f9fc;\r\n  border-radius: 20rpx;\r\n  padding: 10rpx;\r\n}\r\n\r\n.function-name {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.arrow-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  transform: rotate(90deg);\r\n  opacity: 0.5;\r\n}\r\n\r\n/* 交易记录区域 */\r\n.transaction-section {\r\n  background-color: #fff;\r\n  margin: 30rpx;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 20rpx;\r\n  border-bottom: 1rpx solid #f5f5f5;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.section-more {\r\n  font-size: 24rpx;\r\n  color: #0052CC;\r\n}\r\n\r\n.transaction-list {\r\n  min-height: 300rpx;\r\n}\r\n\r\n.transaction-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx 0;\r\n  border-bottom: 1rpx solid #f5f5f5;\r\n}\r\n\r\n.transaction-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.transaction-left {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.transaction-title {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.transaction-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.transaction-amount {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.income {\r\n  color: #07c160;\r\n}\r\n\r\n.expense {\r\n  color: #f56c6c;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-view {\r\n  padding: 60rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.empty-icon {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n</style> \r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/payment/pages/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "smartNavigate", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;AAiFA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,QAAQ;AAAA,MACR,cAAc;AAAA,IAChB,CAAC;AACD,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACP;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MACP;AAAA,IACH,CAAC;AAID,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BC,uBAAAA,cAAc,GAAG,EAAE,MAAM,SAAO;AAC9BD,sBAAA,MAAA,MAAA,SAAA,8CAAc,WAAW,GAAG;AAAA,MAChC,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/B,iBAAW,qCAAqC;AAAA,IAClD;AAGA,UAAM,qBAAqB,MAAM;AAC/B,iBAAW,qCAAqC;AAAA,IAClD;AAGA,UAAM,mBAAmB,MAAM;AAG7B,iBAAW,MAAM;AACf,oBAAY,QAAQ;AAAA,UAClB,QAAQ;AAAA,UACR,cAAc;AAAA,QACpB;AAAA,MACG,GAAE,GAAG;AAAA,IACR;AAGAE,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUF,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAG7C;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1JD,GAAG,WAAWG,SAAe;"}