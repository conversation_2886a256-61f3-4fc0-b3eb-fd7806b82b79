
.earnings-container.data-v-770c6e5d {
  padding: 30rpx;
  background-color: #F2F2F7;
  min-height: 100vh;
}
.card-header.data-v-770c6e5d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.time-filter.data-v-770c6e5d {
  display: flex;
}
.filter-item.data-v-770c6e5d {
  transition: all 0.3s ease;
}
.filter-item.active.data-v-770c6e5d {
  font-weight: 500;
}
.action-btn.data-v-770c6e5d:active {
  opacity: 0.9;
  transform: scale(0.98);
}
