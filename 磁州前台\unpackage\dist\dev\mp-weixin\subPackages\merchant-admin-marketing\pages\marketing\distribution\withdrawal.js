"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  __name: "withdrawal",
  setup(__props) {
    const minWithdrawalAmount = common_vendor.ref("50");
    const withdrawalFee = common_vendor.ref("0");
    const timeOptions = {
      "realtime": "实时到账",
      "T1": "T+1天到账",
      "T2": "T+2天到账",
      "T3": "T+3天到账"
    };
    const selectedTime = common_vendor.ref("T1");
    const autoApprove = common_vendor.ref(false);
    const withdrawalMethods = common_vendor.ref([
      {
        id: "wechat",
        name: "微信零钱",
        icon: "/static/images/payment/wechat.png",
        enabled: true
      },
      {
        id: "alipay",
        name: "支付宝",
        icon: "/static/images/payment/alipay.png",
        enabled: true
      },
      {
        id: "bank",
        name: "银行卡",
        icon: "/static/images/payment/bank.png",
        enabled: false
      }
    ]);
    const withdrawalDescription = common_vendor.ref("1. 提现金额必须大于等于最低提现金额\n2. 提现申请提交后将在1-3个工作日内处理\n3. 如有疑问，请联系客服");
    common_vendor.onMounted(() => {
      getWithdrawalSettings();
    });
    const getWithdrawalSettings = () => {
    };
    const showTimeOptions = () => {
      const options = Object.values(timeOptions);
      common_vendor.index.showActionSheet({
        itemList: options,
        success: (res) => {
          const keys = Object.keys(timeOptions);
          selectedTime.value = keys[res.tapIndex];
        }
      });
    };
    const toggleAutoApprove = (e) => {
      autoApprove.value = e.detail.value;
    };
    const toggleMethod = (index, e) => {
      withdrawalMethods.value[index].enabled = e.detail.value;
      const enabledMethods = withdrawalMethods.value.filter((method) => method.enabled);
      if (enabledMethods.length === 0) {
        common_vendor.index.showToast({
          title: "至少需要开启一种提现方式",
          icon: "none"
        });
        withdrawalMethods.value[index].enabled = true;
      }
    };
    const saveSettings = () => {
      if (!minWithdrawalAmount.value || Number(minWithdrawalAmount.value) <= 0) {
        common_vendor.index.showToast({
          title: "请输入有效的最低提现金额",
          icon: "none"
        });
        return;
      }
      if (!withdrawalFee.value || Number(withdrawalFee.value) < 0 || Number(withdrawalFee.value) > 100) {
        common_vendor.index.showToast({
          title: "请输入有效的提现手续费比例",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "提现设置帮助",
        content: "您可以设置分销员的提现规则，包括最低提现金额、手续费、到账时间和提现方式等。",
        showCancel: false
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: minWithdrawalAmount.value,
        d: common_vendor.o(($event) => minWithdrawalAmount.value = $event.detail.value),
        e: withdrawalFee.value,
        f: common_vendor.o(($event) => withdrawalFee.value = $event.detail.value),
        g: common_vendor.t(timeOptions[selectedTime.value]),
        h: common_vendor.o(showTimeOptions),
        i: autoApprove.value,
        j: common_vendor.o(toggleAutoApprove),
        k: common_vendor.f(withdrawalMethods.value, (method, index, i0) => {
          return {
            a: method.icon,
            b: common_vendor.n(`method-${method.id}`),
            c: common_vendor.t(method.name),
            d: method.enabled,
            e: common_vendor.o((e) => toggleMethod(index, e), index),
            f: index
          };
        }),
        l: withdrawalDescription.value,
        m: common_vendor.o(($event) => withdrawalDescription.value = $event.detail.value),
        n: common_vendor.o(saveSettings)
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal.js.map
