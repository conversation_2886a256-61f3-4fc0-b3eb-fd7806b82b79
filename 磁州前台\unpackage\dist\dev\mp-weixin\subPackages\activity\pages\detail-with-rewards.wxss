/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-7df6a6fd, html.data-v-7df6a6fd, #app.data-v-7df6a6fd, .index-container.data-v-7df6a6fd {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.activity-detail-container.data-v-7df6a6fd {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 100rpx;
}

/* 导航栏样式 */
.custom-navbar.data-v-7df6a6fd {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background: transparent;
  color: #fff;
  z-index: 100;
}
.navbar-left.data-v-7df6a6fd {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-7df6a6fd {
  width: 36rpx;
  height: 36rpx;
}
.navbar-title.data-v-7df6a6fd {
  font-size: 18px;
  font-weight: 500;
}
.navbar-right.data-v-7df6a6fd {
  width: 60rpx;
}

/* 活动封面 */
.activity-cover.data-v-7df6a6fd {
  position: relative;
  width: 100%;
  height: 400rpx;
}
.cover-image.data-v-7df6a6fd {
  width: 100%;
  height: 100%;
}
.activity-status.data-v-7df6a6fd {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #fff;
}
.status-upcoming.data-v-7df6a6fd {
  background-color: #faad14;
}
.status-ongoing.data-v-7df6a6fd {
  background-color: #52c41a;
}
.status-ended.data-v-7df6a6fd {
  background-color: #999;
}

/* 活动信息卡片 */
.activity-card.data-v-7df6a6fd {
  margin: -40rpx 20rpx 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}
.activity-title.data-v-7df6a6fd {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.activity-meta.data-v-7df6a6fd {
  display: flex;
  flex-wrap: wrap;
}
.meta-item.data-v-7df6a6fd {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 16rpx;
  width: calc(50% - 30rpx);
}
.meta-icon.data-v-7df6a6fd {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.meta-text.data-v-7df6a6fd {
  font-size: 26rpx;
  color: #666;
}

/* 我的奖励 */
.rewards-card.data-v-7df6a6fd {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.section-header.data-v-7df6a6fd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-title.data-v-7df6a6fd {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.section-more.data-v-7df6a6fd {
  font-size: 24rpx;
  color: #3a7afe;
}
.rewards-list.data-v-7df6a6fd {
  margin-top: 20rpx;
}
.reward-item.data-v-7df6a6fd {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.reward-item.data-v-7df6a6fd:last-child {
  border-bottom: none;
}
.reward-left.data-v-7df6a6fd {
  margin-right: 20rpx;
}
.reward-icon.data-v-7df6a6fd {
  width: 80rpx;
  height: 80rpx;
}
.reward-info.data-v-7df6a6fd {
  flex: 1;
}
.reward-name.data-v-7df6a6fd {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}
.reward-desc.data-v-7df6a6fd {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
  display: block;
}
.reward-time.data-v-7df6a6fd {
  font-size: 22rpx;
  color: #999;
  display: block;
}
.reward-right.data-v-7df6a6fd {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.reward-amount.data-v-7df6a6fd {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d4f;
  margin-bottom: 6rpx;
}
.reward-status.data-v-7df6a6fd {
  font-size: 24rpx;
  color: #3a7afe;
  padding: 4rpx 12rpx;
  background-color: #f0f5ff;
  border-radius: 6rpx;
}
.status-used.data-v-7df6a6fd {
  color: #52c41a;
  background-color: #f6ffed;
}
.status-expired.data-v-7df6a6fd {
  color: #999;
  background-color: #f5f5f5;
}

/* 活动详情 */
.detail-card.data-v-7df6a6fd {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.detail-content.data-v-7df6a6fd {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 活动规则 */
.rules-card.data-v-7df6a6fd {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.rules-content.data-v-7df6a6fd {
  margin-top: 20rpx;
}
.rule-item.data-v-7df6a6fd {
  display: flex;
  margin-bottom: 16rpx;
}
.rule-number.data-v-7df6a6fd {
  width: 40rpx;
  height: 40rpx;
  background-color: #f0f5ff;
  color: #3a7afe;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.rule-text.data-v-7df6a6fd {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 相关推荐 */
.related-card.data-v-7df6a6fd {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.related-scroll.data-v-7df6a6fd {
  width: 100%;
  white-space: nowrap;
}
.related-list.data-v-7df6a6fd {
  display: flex;
  padding: 10rpx 0;
}
.related-item.data-v-7df6a6fd {
  width: 300rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: inline-block;
}
.related-image.data-v-7df6a6fd {
  width: 100%;
  height: 180rpx;
}
.related-info.data-v-7df6a6fd {
  padding: 16rpx;
}
.related-title.data-v-7df6a6fd {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.related-time.data-v-7df6a6fd {
  font-size: 22rpx;
  color: #999;
}

/* 底部操作栏 */
.footer-bar.data-v-7df6a6fd {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.footer-left.data-v-7df6a6fd {
  display: flex;
  align-items: center;
}
.share-btn.data-v-7df6a6fd {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  outline: none;
}
.share-btn.data-v-7df6a6fd::after {
  border: none;
}
.share-icon.data-v-7df6a6fd {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}
.share-text.data-v-7df6a6fd {
  font-size: 22rpx;
  color: #666;
}
.footer-right.data-v-7df6a6fd {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}
.action-btn.data-v-7df6a6fd {
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}
.primary-btn.data-v-7df6a6fd {
  background: linear-gradient(135deg, #3a7afe, #6ca6ff);
  color: #fff;
}
.secondary-btn.data-v-7df6a6fd {
  background-color: #f0f5ff;
  color: #3a7afe;
}
.disabled-btn.data-v-7df6a6fd {
  background-color: #f5f5f5;
  color: #999;
}

/* 参与成功弹窗 */
.success-popup.data-v-7df6a6fd {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.success-content.data-v-7df6a6fd {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}
.success-header.data-v-7df6a6fd {
  position: relative;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}
.success-title.data-v-7df6a6fd {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.close-btn.data-v-7df6a6fd {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}
.success-body.data-v-7df6a6fd {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.success-icon.data-v-7df6a6fd {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.success-message.data-v-7df6a6fd {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.success-desc.data-v-7df6a6fd {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
}
.popup-rewards.data-v-7df6a6fd {
  width: 100%;
  margin-top: 20rpx;
}
.popup-reward-item.data-v-7df6a6fd {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.popup-reward-icon.data-v-7df6a6fd {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
}
.popup-reward-info.data-v-7df6a6fd {
  flex: 1;
}
.popup-reward-name.data-v-7df6a6fd {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}
.popup-reward-desc.data-v-7df6a6fd {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.popup-reward-amount.data-v-7df6a6fd {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d4f;
}
.success-footer.data-v-7df6a6fd {
  padding: 20rpx 30rpx 30rpx;
}
.success-btn.data-v-7df6a6fd {
  height: 80rpx;
  background: linear-gradient(135deg, #3a7afe, #6ca6ff);
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}