<template>
  <div id="app" class="app-container">
    <router-view v-slot="{ Component, route }">
      <transition
        :name="route.meta.transition || 'fade'"
        mode="out-in"
        appear
      >
        <keep-alive :include="cachedViews">
          <component :is="Component" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>

    <!-- 全局加载组件 -->
    <GlobalLoading />

    <!-- 全局确认对话框 -->
    <GlobalConfirm />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import GlobalLoading from '@/components/Global/GlobalLoading.vue'
import GlobalConfirm from '@/components/Global/GlobalConfirm.vue'

// 应用状态管理
const appStore = useAppStore()
const userStore = useUserStore()

// 缓存的视图组件
const cachedViews = computed(() => appStore.cachedViews)

// 监听系统主题变化
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)')
prefersDark.addEventListener('change', (e) => {
  if (appStore.theme === 'auto') {
    document.documentElement.classList.toggle('dark', e.matches)
  }
})

// 初始化主题
const initTheme = () => {
  const { theme } = appStore
  if (theme === 'dark' || (theme === 'auto' && prefersDark.matches)) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
}

// 监听主题变化
watch(
  () => appStore.theme,
  () => {
    initTheme()
  },
  { immediate: true }
)

// 页面可见性变化处理
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'visible') {
    // 页面变为可见时，刷新用户token
    if (userStore.isLoggedIn) {
      userStore.refreshToken()
    }
  }
})

// 页面卸载前的清理工作
window.addEventListener('beforeunload', () => {
  // 清理定时器、取消请求等
  appStore.cleanup()
})
</script>

<style lang="scss">
.app-container {
  height: 100vh;
  overflow: hidden;
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-border-color);
  }
}

// 暗色主题下的滚动条
.dark {
  ::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--el-border-color-darker);
    
    &:hover {
      background: var(--el-border-color-dark);
    }
  }
}

// 全局样式重置
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

// Element Plus 样式覆盖
.el-message {
  min-width: 300px;
}

.el-notification {
  min-width: 350px;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
  
  .dark & {
    background-color: rgba(0, 0, 0, 0.8);
  }
}
</style>
