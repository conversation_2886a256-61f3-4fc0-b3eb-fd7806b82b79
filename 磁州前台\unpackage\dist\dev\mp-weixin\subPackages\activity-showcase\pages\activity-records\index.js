"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  (_component_path + _component_svg + _component_rect + _component_line + _component_circle + _component_polyline + _component_uni_popup)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const isRefreshing = common_vendor.ref(false);
    const recordsList = common_vendor.ref([]);
    const filterPopup = common_vendor.ref(null);
    const selectedTimeOption = common_vendor.ref(0);
    const selectedStatusOptions = common_vendor.ref([0]);
    const selectedTypeOptions = common_vendor.ref([0]);
    const timeOptions = [
      { label: "全部时间", value: "all" },
      { label: "最近一周", value: "week" },
      { label: "最近一月", value: "month" },
      { label: "最近三月", value: "three_months" },
      { label: "自定义", value: "custom" }
    ];
    const statusOptions = [
      { label: "全部状态", value: "all" },
      { label: "未开始", value: "upcoming" },
      { label: "进行中", value: "ongoing" },
      { label: "已结束", value: "ended" },
      { label: "已取消", value: "cancelled" }
    ];
    const typeOptions = [
      { label: "全部类型", value: "all" },
      { label: "文化活动", value: "culture" },
      { label: "体育赛事", value: "sports" },
      { label: "亲子活动", value: "family" },
      { label: "公益活动", value: "charity" },
      { label: "户外拓展", value: "outdoor" }
    ];
    const activityTabs = [
      { name: "全部", type: "all", emptyText: "暂无活动记录", emptyImage: "/static/images/empty-activities.png" },
      { name: "我参与的", type: "participated", emptyText: "暂无参与活动", emptyImage: "/static/images/empty-participated.png" },
      { name: "我发起的", type: "created", emptyText: "暂无发起活动", emptyImage: "/static/images/empty-created.png" },
      { name: "已收藏", type: "favorite", emptyText: "暂无收藏活动", emptyImage: "/static/images/empty-favorites.png" }
    ];
    const mockRecords = [
      {
        id: "1001",
        title: "磁州文化节",
        type: "culture",
        status: "upcoming",
        date: "2024-06-15",
        time: "09:00-18:00",
        location: "磁州文化广场",
        image: "/static/demo/activity1.jpg",
        participants: [
          "/static/demo/avatar1.png",
          "/static/demo/avatar2.png",
          "/static/demo/avatar3.png",
          "/static/demo/avatar4.png",
          "/static/demo/avatar5.png"
        ],
        recordType: "participated"
      },
      {
        id: "1002",
        title: "亲子户外拓展活动",
        type: "family",
        status: "ongoing",
        date: "2024-05-28",
        time: "14:00-17:00",
        location: "磁州森林公园",
        image: "/static/demo/activity2.jpg",
        participants: [
          "/static/demo/avatar1.png",
          "/static/demo/avatar3.png",
          "/static/demo/avatar5.png"
        ],
        recordType: "participated"
      },
      {
        id: "1003",
        title: "社区篮球赛",
        type: "sports",
        status: "ended",
        date: "2024-05-20",
        time: "10:00-12:00",
        location: "磁州体育中心",
        image: "/static/demo/activity3.jpg",
        participants: [
          "/static/demo/avatar2.png",
          "/static/demo/avatar4.png",
          "/static/demo/avatar5.png",
          "/static/demo/avatar1.png"
        ],
        recordType: "created"
      },
      {
        id: "1004",
        title: "传统文化体验课",
        type: "culture",
        status: "cancelled",
        date: "2024-05-15",
        time: "15:00-17:00",
        location: "磁州文化馆",
        image: "/static/demo/activity4.jpg",
        participants: [
          "/static/demo/avatar3.png",
          "/static/demo/avatar1.png"
        ],
        recordType: "favorite"
      },
      {
        id: "1005",
        title: "环保公益行动",
        type: "charity",
        status: "upcoming",
        date: "2024-06-05",
        time: "09:00-12:00",
        location: "磁州河畔",
        image: "/static/demo/activity5.jpg",
        participants: [
          "/static/demo/avatar1.png",
          "/static/demo/avatar2.png",
          "/static/demo/avatar3.png",
          "/static/demo/avatar4.png",
          "/static/demo/avatar5.png",
          "/static/demo/avatar1.png",
          "/static/demo/avatar2.png"
        ],
        recordType: "created"
      }
    ];
    common_vendor.onMounted(() => {
      loadRecords();
    });
    const loadRecords = () => {
      recordsList.value = mockRecords;
    };
    const getRecordsByType = (type) => {
      if (type === "all") {
        return recordsList.value;
      }
      return recordsList.value.filter((record) => record.recordType === type);
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const onSwiperChange = (e) => {
      currentTab.value = e.detail.current;
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      setTimeout(() => {
        loadRecords();
        isRefreshing.value = false;
      }, 1e3);
    };
    const loadMore = () => {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/activity-records/index.vue:411", "加载更多记录");
    };
    const getStatusText = (status) => {
      const statusMap = {
        "upcoming": "未开始",
        "ongoing": "进行中",
        "ended": "已结束",
        "cancelled": "已取消"
      };
      return statusMap[status] || "未知状态";
    };
    const getStatusBackground = (status) => {
      const bgMap = {
        "upcoming": "rgba(255, 149, 0, 0.8)",
        "ongoing": "rgba(52, 199, 89, 0.8)",
        "ended": "rgba(142, 142, 147, 0.8)",
        "cancelled": "rgba(255, 59, 48, 0.8)"
      };
      return bgMap[status] || "rgba(142, 142, 147, 0.8)";
    };
    const getPrimaryActionText = (status) => {
      const actionMap = {
        "upcoming": "立即报名",
        "ongoing": "查看详情",
        "ended": "活动回顾",
        "cancelled": "查看详情"
      };
      return actionMap[status] || "查看详情";
    };
    const getPrimaryActionBgColor = (status) => {
      const bgColorMap = {
        "upcoming": "linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)",
        "ongoing": "linear-gradient(135deg, #34C759 0%, #7ED321 100%)",
        "ended": "linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)",
        "cancelled": "linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)"
      };
      return bgColorMap[status] || "linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)";
    };
    const handlePrimaryAction = (record) => {
      switch (record.status) {
        case "upcoming":
          navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}&action=register`);
          break;
        case "ongoing":
          navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);
          break;
        case "ended":
          navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}&tab=review`);
          break;
        default:
          navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);
      }
    };
    const viewActivityDetail = (record) => {
      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${record.id}`);
    };
    const shareActivity = (record) => {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    };
    const showFilter = () => {
      filterPopup.value.open();
    };
    const closeFilter = () => {
      filterPopup.value.close();
    };
    const selectTimeOption = (index) => {
      selectedTimeOption.value = index;
    };
    const toggleStatusOption = (index) => {
      const position = selectedStatusOptions.value.indexOf(index);
      if (index === 0) {
        selectedStatusOptions.value = [0];
      } else {
        if (selectedStatusOptions.value.includes(0)) {
          selectedStatusOptions.value = selectedStatusOptions.value.filter((item) => item !== 0);
        }
        if (position !== -1) {
          selectedStatusOptions.value.splice(position, 1);
          if (selectedStatusOptions.value.length === 0) {
            selectedStatusOptions.value = [0];
          }
        } else {
          selectedStatusOptions.value.push(index);
        }
      }
    };
    const toggleTypeOption = (index) => {
      const position = selectedTypeOptions.value.indexOf(index);
      if (index === 0) {
        selectedTypeOptions.value = [0];
      } else {
        if (selectedTypeOptions.value.includes(0)) {
          selectedTypeOptions.value = selectedTypeOptions.value.filter((item) => item !== 0);
        }
        if (position !== -1) {
          selectedTypeOptions.value.splice(position, 1);
          if (selectedTypeOptions.value.length === 0) {
            selectedTypeOptions.value = [0];
          }
        } else {
          selectedTypeOptions.value.push(index);
        }
      }
    };
    const resetFilter = () => {
      selectedTimeOption.value = 0;
      selectedStatusOptions.value = [0];
      selectedTypeOptions.value = [0];
    };
    const applyFilter = () => {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/activity-records/index.vue:549", "应用筛选", {
        time: timeOptions[selectedTimeOption.value].value,
        status: selectedStatusOptions.value.map((index) => statusOptions[index].value),
        type: selectedTypeOptions.value.map((index) => typeOptions[index].value)
      });
      common_vendor.index.showToast({
        title: "筛选已应用",
        icon: "success"
      });
      closeFilter();
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.p({
          d: "M22 3H2l8 9.46V19l4 2v-8.54L22 3z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        d: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        e: common_vendor.o(showFilter),
        f: common_vendor.f(activityTabs, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.name),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        g: common_vendor.f(activityTabs, (tab, tabIndex, i0) => {
          return common_vendor.e({
            a: common_vendor.f(getRecordsByType(tab.type), (record, k1, i1) => {
              return common_vendor.e({
                a: record.image,
                b: common_vendor.t(getStatusText(record.status)),
                c: getStatusBackground(record.status),
                d: common_vendor.t(record.title),
                e: "2492d9f5-3-" + i0 + "-" + i1 + "," + ("2492d9f5-2-" + i0 + "-" + i1),
                f: "2492d9f5-4-" + i0 + "-" + i1 + "," + ("2492d9f5-2-" + i0 + "-" + i1),
                g: "2492d9f5-5-" + i0 + "-" + i1 + "," + ("2492d9f5-2-" + i0 + "-" + i1),
                h: "2492d9f5-6-" + i0 + "-" + i1 + "," + ("2492d9f5-2-" + i0 + "-" + i1),
                i: "2492d9f5-2-" + i0 + "-" + i1,
                j: common_vendor.t(record.date),
                k: "2492d9f5-8-" + i0 + "-" + i1 + "," + ("2492d9f5-7-" + i0 + "-" + i1),
                l: "2492d9f5-9-" + i0 + "-" + i1 + "," + ("2492d9f5-7-" + i0 + "-" + i1),
                m: "2492d9f5-7-" + i0 + "-" + i1,
                n: common_vendor.t(record.time),
                o: "2492d9f5-11-" + i0 + "-" + i1 + "," + ("2492d9f5-10-" + i0 + "-" + i1),
                p: "2492d9f5-12-" + i0 + "-" + i1 + "," + ("2492d9f5-10-" + i0 + "-" + i1),
                q: "2492d9f5-10-" + i0 + "-" + i1,
                r: common_vendor.t(record.location),
                s: common_vendor.f(record.participants.slice(0, 3), (avatar, avatarIndex, i2) => {
                  return {
                    a: avatarIndex,
                    b: avatar
                  };
                }),
                t: record.participants.length > 3
              }, record.participants.length > 3 ? {
                v: common_vendor.t(record.participants.length - 3)
              } : {}, {
                w: common_vendor.t(record.participants.length),
                x: "2492d9f5-14-" + i0 + "-" + i1 + "," + ("2492d9f5-13-" + i0 + "-" + i1),
                y: "2492d9f5-15-" + i0 + "-" + i1 + "," + ("2492d9f5-13-" + i0 + "-" + i1),
                z: "2492d9f5-16-" + i0 + "-" + i1 + "," + ("2492d9f5-13-" + i0 + "-" + i1),
                A: "2492d9f5-17-" + i0 + "-" + i1 + "," + ("2492d9f5-13-" + i0 + "-" + i1),
                B: "2492d9f5-18-" + i0 + "-" + i1 + "," + ("2492d9f5-13-" + i0 + "-" + i1),
                C: "2492d9f5-13-" + i0 + "-" + i1,
                D: common_vendor.o(($event) => shareActivity(), record.id),
                E: common_vendor.t(getPrimaryActionText(record.status)),
                F: getPrimaryActionBgColor(record.status),
                G: common_vendor.o(($event) => handlePrimaryAction(record), record.id),
                H: record.id,
                I: common_vendor.o(($event) => viewActivityDetail(record), record.id)
              });
            }),
            b: getRecordsByType(tab.type).length === 0
          }, getRecordsByType(tab.type).length === 0 ? {
            c: tab.emptyImage || "/static/images/empty-records.png",
            d: common_vendor.t(tab.emptyText || "暂无相关活动记录"),
            e: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/list/index"), tabIndex)
          } : {}, {
            f: common_vendor.o(onRefresh, tabIndex),
            g: common_vendor.o(loadMore, tabIndex),
            h: tabIndex
          });
        }),
        h: common_vendor.p({
          x: "3",
          y: "4",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        i: common_vendor.p({
          x1: "16",
          y1: "2",
          x2: "16",
          y2: "6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        j: common_vendor.p({
          x1: "8",
          y1: "2",
          x2: "8",
          y2: "6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        k: common_vendor.p({
          x1: "3",
          y1: "10",
          x2: "21",
          y2: "10",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        l: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        m: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          points: "12 6 12 12 16 14",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        p: common_vendor.p({
          d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        q: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        r: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        s: common_vendor.p({
          cx: "18",
          cy: "5",
          r: "3",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        t: common_vendor.p({
          cx: "6",
          cy: "12",
          r: "3",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          cx: "18",
          cy: "19",
          r: "3",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          x1: "8.59",
          y1: "13.51",
          x2: "15.42",
          y2: "17.49",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.p({
          x1: "15.41",
          y1: "6.51",
          x2: "8.59",
          y2: "10.49",
          stroke: "#666666",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        y: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        z: isRefreshing.value,
        A: currentTab.value,
        B: common_vendor.o(onSwiperChange),
        C: common_vendor.p({
          x1: "18",
          y1: "6",
          x2: "6",
          y2: "18",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        D: common_vendor.p({
          x1: "6",
          y1: "6",
          x2: "18",
          y2: "18",
          stroke: "#333333",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        E: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        F: common_vendor.o(closeFilter),
        G: common_vendor.f(timeOptions, (option, index, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: index,
            c: selectedTimeOption.value === index ? 1 : "",
            d: common_vendor.o(($event) => selectTimeOption(index), index)
          };
        }),
        H: common_vendor.f(statusOptions, (option, index, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: index,
            c: selectedStatusOptions.value.includes(index) ? 1 : "",
            d: common_vendor.o(($event) => toggleStatusOption(index), index)
          };
        }),
        I: common_vendor.f(typeOptions, (option, index, i0) => {
          return {
            a: common_vendor.t(option.label),
            b: index,
            c: selectedTypeOptions.value.includes(index) ? 1 : "",
            d: common_vendor.o(($event) => toggleTypeOption(index), index)
          };
        }),
        J: common_vendor.o(resetFilter),
        K: common_vendor.o(applyFilter),
        L: common_vendor.sr(filterPopup, "2492d9f5-19", {
          "k": "filterPopup"
        }),
        M: common_vendor.p({
          type: "bottom"
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2492d9f5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/activity-records/index.js.map
