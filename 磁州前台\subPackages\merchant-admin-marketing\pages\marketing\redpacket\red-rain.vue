<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包雨</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 数据概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">红包雨数据概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <text class="card-value">{{rainData.totalCount}}</text>
          <text class="card-label">红包雨活动数</text>
          <view class="card-trend" :class="rainData.countTrend">
            <view class="trend-arrow"></view>
            <text>{{rainData.countGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <text class="card-value">{{rainData.totalUsers}}</text>
          <text class="card-label">参与用户数</text>
          <view class="card-trend" :class="rainData.usersTrend">
            <view class="trend-arrow"></view>
            <text>{{rainData.usersGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <text class="card-value">{{formatNumber(rainData.totalAmount)}}</text>
          <text class="card-label">发放金额(元)</text>
          <view class="card-trend" :class="rainData.amountTrend">
            <view class="trend-arrow"></view>
            <text>{{rainData.amountGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <text class="card-value">{{rainData.conversionRate}}%</text>
          <text class="card-label">领取转化率</text>
          <view class="card-trend" :class="rainData.conversionTrend">
            <view class="trend-arrow"></view>
            <text>{{rainData.conversionGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 红包雨列表 -->
    <view class="rain-list-section">
      <view class="section-header">
        <text class="section-title">红包雨活动</text>
        <view class="add-btn" @click="createRain">
          <text class="btn-text">新建活动</text>
          <view class="plus-icon-small"></view>
        </view>
      </view>
      
      <view class="tab-header">
        <view class="tab-item" 
              v-for="(tab, index) in tabList" 
              :key="index" 
              :class="{ active: currentTab === index }" 
              @click="switchTab(index)">
          <text class="tab-text">{{tab}}</text>
        </view>
      </view>
      
      <view class="rain-list">
        <view class="rain-item" v-for="(item, index) in filteredRainList" :key="index">
          <view class="rain-header">
            <text class="rain-title">{{item.title}}</text>
            <text class="rain-status" :class="'status-' + item.status">{{item.statusText}}</text>
          </view>
          
          <view class="rain-content">
            <view class="rain-icon"></view>
            <view class="rain-info">
              <text class="rain-time">{{item.timeRange}}</text>
              <view class="rain-amount">
                <text class="amount-label">红包金额: </text>
                <text class="amount-value">{{item.minAmount}}元 - {{item.maxAmount}}元</text>
              </view>
              <view class="rain-duration">
                <text class="duration-label">持续时间: </text>
                <text class="duration-value">{{item.duration}}秒</text>
              </view>
            </view>
          </view>
          
          <view class="rain-stats">
            <view class="stat-row">
              <text class="stat-label">参与人数:</text>
              <text class="stat-value">{{item.participantCount}}人</text>
            </view>
            <view class="stat-row">
              <text class="stat-label">发放红包:</text>
              <text class="stat-value">{{item.sentCount}}个</text>
            </view>
            <view class="stat-row">
              <text class="stat-label">领取红包:</text>
              <text class="stat-value">{{item.receivedCount}}个</text>
            </view>
          </view>
          
          <view class="rain-actions">
            <view class="action-btn" @click.stop="viewRainDetail(item)">详情</view>
            <view class="action-btn" @click.stop="editRain(item)" v-if="item.status === 'draft'">编辑</view>
            <view class="action-btn" @click.stop="shareRain(item)" v-if="item.status === 'active' || item.status === 'upcoming'">分享</view>
            <view class="action-btn delete" @click.stop="deleteRain(item)" v-if="item.status === 'draft' || item.status === 'ended'">删除</view>
          </view>
        </view>
        
        <view class="empty-state" v-if="filteredRainList.length === 0">
          <view class="empty-icon"></view>
          <text class="empty-text">暂无{{tabList[currentTab]}}红包雨活动</text>
        </view>
      </view>
    </view>
    
    <!-- 红包雨设置指南 -->
    <view class="guide-section">
      <view class="section-header">
        <text class="section-title">红包雨设置指南</text>
      </view>
      
      <view class="guide-steps">
        <view class="guide-step" v-for="(step, index) in guideSteps" :key="index">
          <view class="step-number">{{index + 1}}</view>
          <view class="step-content">
            <text class="step-title">{{step.title}}</text>
            <text class="step-desc">{{step.description}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 红包雨效果预览 -->
    <view class="preview-section">
      <view class="section-header">
        <text class="section-title">效果预览</text>
      </view>
      
      <view class="preview-container">
        <image class="preview-image" src="/static/images/redpacket/rain-preview.png" mode="widthFix"></image>
        <view class="preview-btn" @click="showPreview">查看动态效果</view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="createRain">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      currentTab: 0,
      tabList: ['全部', '进行中', '未开始', '已结束', '草稿'],
      
      // 红包雨数据概览
      rainData: {
        totalCount: 18,
        countTrend: 'up',
        countGrowth: '22.5%',
        
        totalUsers: 8752,
        usersTrend: 'up',
        usersGrowth: '35.7%',
        
        totalAmount: 12680.50,
        amountTrend: 'up',
        amountGrowth: '28.2%',
        
        conversionRate: 45.6,
        conversionTrend: 'up',
        conversionGrowth: '7.8%'
      },
      
      // 红包雨活动列表
      rainList: [
        {
          id: 1,
          title: '周年庆红包雨',
          status: 'active',
          statusText: '进行中',
          timeRange: '2023-04-15 ~ 2023-04-20',
          minAmount: 1.00,
          maxAmount: 50.00,
          duration: 60,
          participantCount: 3562,
          sentCount: 15820,
          receivedCount: 12453
        },
        {
          id: 2,
          title: '五一节日红包雨',
          status: 'upcoming',
          statusText: '未开始',
          timeRange: '2023-05-01 ~ 2023-05-07',
          minAmount: 2.00,
          maxAmount: 88.00,
          duration: 90,
          participantCount: 0,
          sentCount: 0,
          receivedCount: 0
        },
        {
          id: 3,
          title: '新品发布红包雨',
          status: 'draft',
          statusText: '草稿',
          timeRange: '未设置',
          minAmount: 1.00,
          maxAmount: 20.00,
          duration: 45,
          participantCount: 0,
          sentCount: 0,
          receivedCount: 0
        },
        {
          id: 4,
          title: '春节红包雨',
          status: 'ended',
          statusText: '已结束',
          timeRange: '2023-01-20 ~ 2023-02-05',
          minAmount: 1.00,
          maxAmount: 100.00,
          duration: 120,
          participantCount: 5189,
          sentCount: 25680,
          receivedCount: 22547
        }
      ],
      
      // 红包雨设置指南
      guideSteps: [
        {
          title: '设置活动时间',
          description: '选择红包雨活动的开始和结束时间，可设置多场次'
        },
        {
          title: '设置红包参数',
          description: '配置红包金额范围、数量、持续时间等参数'
        },
        {
          title: '设置触发条件',
          description: '可选择自动触发或用户手动触发红包雨'
        },
        {
          title: '设置活动页面',
          description: '自定义红包雨活动页面的背景、文案等元素'
        },
        {
          title: '发布活动',
          description: '预览效果无误后发布活动，可通过多种渠道分享'
        }
      ]
    }
  },
  computed: {
    filteredRainList() {
      if (this.currentTab === 0) {
        return this.rainList;
      } else {
        const statusMap = {
          1: 'active',
          2: 'upcoming',
          3: 'ended',
          4: 'draft'
        };
        return this.rainList.filter(item => item.status === statusMap[this.currentTab]);
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showModal({
        title: '红包雨帮助',
        content: '红包雨是一种互动营销活动，在指定时间内向用户随机发放红包，提高用户活跃度和参与感。',
        showCancel: false
      });
    },
    showDatePicker() {
      // 显示日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    formatNumber(num) {
      return num.toFixed(2);
    },
    switchTab(index) {
      this.currentTab = index;
    },
    createRain() {
      uni.showToast({
        title: '创建红包雨功能开发中',
        icon: 'none'
      });
    },
    viewRainDetail(item) {
      uni.showToast({
        title: '查看详情功能开发中',
        icon: 'none'
      });
    },
    editRain(item) {
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      });
    },
    shareRain(item) {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    deleteRain(item) {
      uni.showModal({
        title: '删除确认',
        content: `确定要删除"${item.title}"吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },
    showPreview() {
      uni.showToast({
        title: '预览功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 数据概览样式 */
.overview-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-picker {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  padding: 5px 10px;
  border-radius: 15px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 12px;
  height: 12px;
  border-left: 1px solid #666;
  border-bottom: 1px solid #666;
  transform: rotate(-45deg);
}

.stats-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.stats-card {
  width: 48%;
  background: linear-gradient(135deg, #FFF, #F5F7FA);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.card-value {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.card-label {
  font-size: 12px;
  color: #666;
}

.card-trend {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #FF5858;
}

.card-trend.down {
  color: #2ED573;
}

.trend-arrow {
  width: 8px;
  height: 8px;
  border-left: 1px solid currentColor;
  border-top: 1px solid currentColor;
  margin-right: 2px;
}

.up .trend-arrow {
  transform: rotate(45deg);
}

.down .trend-arrow {
  transform: rotate(-135deg);
}

/* 红包雨列表样式 */
.rain-list-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.add-btn {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  padding: 6px 12px;
  border-radius: 15px;
  color: #fff;
}

.btn-text {
  font-size: 12px;
  margin-right: 5px;
}

.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon-small::before,
.plus-icon-small::after {
  content: '';
  position: absolute;
  background-color: #fff;
}

.plus-icon-small::before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon-small::after {
  width: 2px;
  height: 12px;
  top: 0;
  left: 5px;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 15px;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  position: relative;
}

.tab-text {
  font-size: 14px;
  color: #666;
}

.tab-item.active .tab-text {
  color: #FF5858;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 25%;
  width: 50%;
  height: 2px;
  background-color: #FF5858;
  border-radius: 1px;
}

.rain-list {
  margin-top: 10px;
}

.rain-item {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  border: 1px solid #eee;
}

.rain-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.rain-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.rain-status {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 10px;
}

.status-active {
  background-color: #E8F5E9;
  color: #388E3C;
}

.status-upcoming {
  background-color: #E3F2FD;
  color: #1976D2;
}

.status-ended {
  background-color: #EEEEEE;
  color: #757575;
}

.status-draft {
  background-color: #FFF3E0;
  color: #E65100;
}

.rain-content {
  display: flex;
  margin-bottom: 10px;
}

.rain-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, #FFD166, #FF9A8B);
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.rain-icon::before {
  content: '雨';
  color: #fff;
  font-weight: bold;
  font-size: 16px;
}

.rain-info {
  flex: 1;
}

.rain-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: block;
}

.rain-amount,
.rain-duration {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  display: flex;
}

.amount-label,
.duration-label {
  color: #999;
}

.amount-value,
.duration-value {
  color: #333;
}

.rain-stats {
  background-color: #F9F9F9;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 5px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: #666;
}

.stat-value {
  color: #333;
  font-weight: 500;
}

.rain-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.action-btn {
  margin-left: 10px;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  background-color: #F5F7FA;
  color: #666;
}

.action-btn.delete {
  background-color: #FEE8E8;
  color: #FF5858;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.empty-icon {
  width: 60px;
  height: 60px;
  background-color: #F5F7FA;
  border-radius: 30px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 指南样式 */
.guide-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.guide-steps {
  margin-top: 15px;
}

.guide-step {
  display: flex;
  margin-bottom: 15px;
}

.guide-step:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #FF5858;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.step-desc {
  font-size: 12px;
  color: #666;
}

/* 预览样式 */
.preview-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 80px;
}

.preview-container {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
  margin-bottom: 15px;
}

.preview-btn {
  padding: 8px 20px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  color: #fff;
  border-radius: 20px;
  font-size: 14px;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #FF5858, #FF0000);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(255, 88, 88, 0.3);
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
}
</style>