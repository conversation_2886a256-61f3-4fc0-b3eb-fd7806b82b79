{"version": 3, "file": "withdraw.js", "sources": ["subPackages/distribution/pages/withdraw.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcZGlzdHJpYnV0aW9uXHBhZ2VzXHdpdGhkcmF3LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"withdraw-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">佣金提现</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 可提现金额卡片 -->\r\n    <view class=\"balance-card\">\r\n      <view class=\"balance-header\">\r\n        <text class=\"balance-label\">可提现佣金</text>\r\n      </view>\r\n      \r\n      <view class=\"balance-amount\">\r\n        <text class=\"currency\">¥</text>\r\n        <text class=\"amount\">{{formatCommission(availableCommission)}}</text>\r\n      </view>\r\n      \r\n      <view class=\"balance-tips\">\r\n        <text>提现将在1-3个工作日内到账</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提现表单 -->\r\n    <view class=\"form-card\">\r\n      <view class=\"form-header\">\r\n        <text class=\"form-title\">提现申请</text>\r\n      </view>\r\n      \r\n      <view class=\"form-content\">\r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">提现金额</text>\r\n          <view class=\"amount-input-wrap\">\r\n            <text class=\"currency-symbol\">¥</text>\r\n            <input \r\n              class=\"amount-input\" \r\n              type=\"digit\" \r\n              v-model=\"formData.amount\" \r\n              placeholder=\"请输入提现金额\" \r\n              @input=\"validateAmount\"\r\n            />\r\n          </view>\r\n          <view class=\"amount-tips\">\r\n            <text>最低提现金额{{withdrawalSettings.minAmount}}元</text>\r\n            <text class=\"all-amount\" @click=\"withdrawAll\">全部提现</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">提现方式</text>\r\n          <view class=\"method-options\">\r\n            <view \r\n              v-for=\"(method, index) in withdrawMethods\" \r\n              :key=\"index\" \r\n              class=\"method-option\" \r\n              :class=\"{ 'active': formData.method === method.value }\"\r\n              @click=\"selectMethod(method.value)\"\r\n            >\r\n              <view class=\"method-icon\" :class=\"method.value\"></view>\r\n              <text class=\"method-name\">{{method.name}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"form-item\" v-if=\"formData.method === 'wechat'\">\r\n          <text class=\"form-label\">微信号</text>\r\n          <input class=\"form-input\" type=\"text\" v-model=\"formData.account\" placeholder=\"请输入微信号\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\" v-if=\"formData.method === 'alipay'\">\r\n          <text class=\"form-label\">支付宝账号</text>\r\n          <input class=\"form-input\" type=\"text\" v-model=\"formData.account\" placeholder=\"请输入支付宝账号\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\" v-if=\"formData.method === 'bank'\">\r\n          <text class=\"form-label\">银行卡号</text>\r\n          <input class=\"form-input\" type=\"text\" v-model=\"formData.account\" placeholder=\"请输入银行卡号\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\" v-if=\"formData.method === 'bank'\">\r\n          <text class=\"form-label\">开户行</text>\r\n          <input class=\"form-input\" type=\"text\" v-model=\"formData.bankName\" placeholder=\"请输入开户行\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">真实姓名</text>\r\n          <input class=\"form-input\" type=\"text\" v-model=\"formData.realName\" placeholder=\"请输入真实姓名\" />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"form-label\">手机号码</text>\r\n          <input class=\"form-input\" type=\"number\" v-model=\"formData.mobile\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"form-footer\">\r\n        <view class=\"fee-info\">\r\n          <text>手续费：{{formData.amount ? (formData.amount * withdrawalSettings.feeRate).toFixed(2) : '0.00'}}元 ({{withdrawalSettings.feeRate * 100}}%)</text>\r\n          <text>实际到账：{{formData.amount ? (formData.amount * (1 - withdrawalSettings.feeRate)).toFixed(2) : '0.00'}}元</text>\r\n        </view>\r\n        \r\n        <button class=\"submit-btn\" :disabled=\"!canSubmit\" :class=\"{ 'disabled': !canSubmit }\" @click=\"submitWithdrawal\">确认提现</button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提现记录 -->\r\n    <view class=\"records-card\">\r\n      <view class=\"records-header\">\r\n        <text class=\"records-title\">提现记录</text>\r\n        <view class=\"view-all\" @click=\"navigateTo('/subPackages/distribution/pages/commission')\">查看全部</view>\r\n      </view>\r\n      \r\n      <view class=\"records-list\" v-if=\"withdrawalRecords.length > 0\">\r\n        <view \r\n          v-for=\"(record, index) in withdrawalRecords\" \r\n          :key=\"index\" \r\n          class=\"record-item\"\r\n        >\r\n          <view class=\"record-info\">\r\n            <view class=\"record-main\">\r\n              <text class=\"record-type\">提现到{{getMethodName(record.method)}}</text>\r\n              <text class=\"record-amount\">-¥{{formatCommission(record.amount)}}</text>\r\n            </view>\r\n            \r\n            <view class=\"record-sub\">\r\n              <text class=\"record-time\">{{formatTime(record.createdAt)}}</text>\r\n              <text class=\"record-status\" :class=\"getStatusClass(record.status)\">{{getStatusText(record.status)}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"empty-records\" v-else>\r\n        <text>暂无提现记录</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提现说明 -->\r\n    <view class=\"tips-card\">\r\n      <view class=\"tips-header\">\r\n        <text class=\"tips-title\">提现说明</text>\r\n      </view>\r\n      \r\n      <view class=\"tips-content\">\r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">提现金额最低{{withdrawalSettings.minAmount}}元，最高{{withdrawalSettings.maxAmount}}元</text>\r\n        </view>\r\n        \r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">提现手续费为提现金额的{{withdrawalSettings.feeRate * 100}}%</text>\r\n        </view>\r\n        \r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">提现申请将在1-3个工作日内处理，请耐心等待</text>\r\n        </view>\r\n        \r\n        <view class=\"tip-item\">\r\n          <view class=\"tip-icon\"></view>\r\n          <text class=\"tip-text\">提现账户必须与实名认证信息一致</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\nimport distributionService from '@/utils/distributionService';\r\n\r\n// 可提现佣金\r\nconst availableCommission = ref(0);\r\n\r\n// 提现设置\r\nconst withdrawalSettings = reactive({\r\n  minAmount: 50,\r\n  maxAmount: 5000,\r\n  feeRate: 0.01,\r\n  withdrawalCycle: 'weekly',\r\n  withdrawalDays: [1, 4], // 周一和周四可提现\r\n  autoApprove: false,\r\n  needRealName: true,\r\n  withdrawalMethods: ['wechat', 'alipay', 'bank']\r\n});\r\n\r\n// 提现方式\r\nconst withdrawMethods = [\r\n  { name: '微信', value: 'wechat' },\r\n  { name: '支付宝', value: 'alipay' },\r\n  { name: '银行卡', value: 'bank' }\r\n];\r\n\r\n// 表单数据\r\nconst formData = reactive({\r\n  amount: '',\r\n  method: 'wechat',\r\n  account: '',\r\n  bankName: '',\r\n  realName: '',\r\n  mobile: ''\r\n});\r\n\r\n// 提现记录\r\nconst withdrawalRecords = ref([]);\r\n\r\n// 是否可以提交\r\nconst canSubmit = computed(() => {\r\n  const amountValid = formData.amount && \r\n                      parseFloat(formData.amount) >= withdrawalSettings.minAmount &&\r\n                      parseFloat(formData.amount) <= Math.min(withdrawalSettings.maxAmount, availableCommission.value);\r\n  \r\n  const accountValid = formData.account.trim() !== '';\r\n  const bankNameValid = formData.method !== 'bank' || formData.bankName.trim() !== '';\r\n  const realNameValid = formData.realName.trim() !== '';\r\n  const mobileValid = formData.mobile.trim() !== '' && formData.mobile.length === 11;\r\n  \r\n  return amountValid && accountValid && bankNameValid && realNameValid && mobileValid;\r\n});\r\n\r\n// 页面加载\r\nonMounted(async () => {\r\n  // 获取可提现佣金\r\n  await getAvailableCommission();\r\n  \r\n  // 获取提现设置\r\n  await getWithdrawalSettings();\r\n  \r\n  // 获取提现记录\r\n  await getWithdrawalRecords();\r\n});\r\n\r\n// 获取可提现佣金\r\nconst getAvailableCommission = async () => {\r\n  try {\r\n    const info = await distributionService.getDistributorInfo({\r\n      userId: '1001' // 模拟数据，实际应从用户系统获取\r\n    });\r\n    \r\n    if (info) {\r\n      availableCommission.value = info.availableCommission;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取可提现佣金失败', error);\r\n    uni.showToast({\r\n      title: '获取可提现佣金失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 获取提现设置\r\nconst getWithdrawalSettings = async () => {\r\n  try {\r\n    const settings = await distributionService.getWithdrawalSettings();\r\n    \r\n    if (settings) {\r\n      Object.assign(withdrawalSettings, settings);\r\n    }\r\n  } catch (error) {\r\n    console.error('获取提现设置失败', error);\r\n  }\r\n};\r\n\r\n// 获取提现记录\r\nconst getWithdrawalRecords = async () => {\r\n  try {\r\n    // 这里应该调用API获取提现记录\r\n    // 暂时使用模拟数据\r\n    withdrawalRecords.value = [\r\n      {\r\n        id: 'W1001',\r\n        amount: 100,\r\n        fee: 1,\r\n        actualAmount: 99,\r\n        method: 'wechat',\r\n        account: 'wx123456',\r\n        status: 'pending',\r\n        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()\r\n      },\r\n      {\r\n        id: 'W1002',\r\n        amount: 200,\r\n        fee: 2,\r\n        actualAmount: 198,\r\n        method: 'alipay',\r\n        account: '<EMAIL>',\r\n        status: 'success',\r\n        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()\r\n      }\r\n    ];\r\n  } catch (error) {\r\n    console.error('获取提现记录失败', error);\r\n  }\r\n};\r\n\r\n// 验证提现金额\r\nconst validateAmount = () => {\r\n  if (!formData.amount) return;\r\n  \r\n  let amount = parseFloat(formData.amount);\r\n  \r\n  // 限制小数点后两位\r\n  formData.amount = amount.toFixed(2);\r\n  \r\n  // 限制最大金额\r\n  if (amount > Math.min(withdrawalSettings.maxAmount, availableCommission.value)) {\r\n    formData.amount = Math.min(withdrawalSettings.maxAmount, availableCommission.value).toFixed(2);\r\n    uni.showToast({\r\n      title: `提现金额不能超过${formData.amount}元`,\r\n      icon: 'none'\r\n    });\r\n  }\r\n  \r\n  // 限制最小金额\r\n  if (amount < withdrawalSettings.minAmount) {\r\n    uni.showToast({\r\n      title: `提现金额不能低于${withdrawalSettings.minAmount}元`,\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 全部提现\r\nconst withdrawAll = () => {\r\n  formData.amount = Math.min(withdrawalSettings.maxAmount, availableCommission.value).toFixed(2);\r\n};\r\n\r\n// 选择提现方式\r\nconst selectMethod = (method) => {\r\n  formData.method = method;\r\n  formData.account = ''; // 切换方式时清空账号\r\n  if (method !== 'bank') {\r\n    formData.bankName = '';\r\n  }\r\n};\r\n\r\n// 提交提现申请\r\nconst submitWithdrawal = async () => {\r\n  if (!canSubmit.value) return;\r\n  \r\n  try {\r\n    uni.showLoading({\r\n      title: '提交中...',\r\n      mask: true\r\n    });\r\n    \r\n    const result = await distributionService.applyWithdrawal({\r\n      amount: parseFloat(formData.amount),\r\n      method: formData.method,\r\n      account: formData.account,\r\n      bankName: formData.bankName,\r\n      realName: formData.realName,\r\n      mobile: formData.mobile\r\n    });\r\n    \r\n    uni.hideLoading();\r\n    \r\n    if (result.success) {\r\n      uni.showModal({\r\n        title: '提现申请成功',\r\n        content: '您的提现申请已提交，请耐心等待处理。',\r\n        showCancel: false,\r\n        success: () => {\r\n          // 刷新数据\r\n          getAvailableCommission();\r\n          getWithdrawalRecords();\r\n          \r\n          // 清空表单\r\n          formData.amount = '';\r\n        }\r\n      });\r\n    } else {\r\n      uni.showModal({\r\n        title: '提现申请失败',\r\n        content: result.message || '请稍后再试',\r\n        showCancel: false\r\n      });\r\n    }\r\n  } catch (error) {\r\n    uni.hideLoading();\r\n    console.error('提交提现申请失败', error);\r\n    uni.showToast({\r\n      title: '提交提现申请失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 获取提现方式名称\r\nconst getMethodName = (method) => {\r\n  const methodMap = {\r\n    'wechat': '微信',\r\n    'alipay': '支付宝',\r\n    'bank': '银行卡'\r\n  };\r\n  return methodMap[method] || method;\r\n};\r\n\r\n// 获取状态文本\r\nconst getStatusText = (status) => {\r\n  const statusMap = {\r\n    'pending': '处理中',\r\n    'success': '已到账',\r\n    'failed': '提现失败'\r\n  };\r\n  return statusMap[status] || status;\r\n};\r\n\r\n// 获取状态样式类\r\nconst getStatusClass = (status) => {\r\n  return {\r\n    'pending': 'status-pending',\r\n    'success': 'status-success',\r\n    'failed': 'status-failed'\r\n  }[status] || '';\r\n};\r\n\r\n// 格式化佣金\r\nconst formatCommission = (amount) => {\r\n  return distributionService.formatCommission(amount);\r\n};\r\n\r\n// 格式化时间\r\nconst formatTime = (time) => {\r\n  if (!time) return '';\r\n  \r\n  const date = new Date(time);\r\n  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\r\n};\r\n\r\n// 页面导航\r\nconst navigateTo = (url) => {\r\n  uni.navigateTo({ url });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 显示帮助\r\nconst showHelp = () => {\r\n  uni.showModal({\r\n    title: '提现帮助',\r\n    content: `提现金额最低${withdrawalSettings.minAmount}元，最高${withdrawalSettings.maxAmount}元。提现手续费为提现金额的${withdrawalSettings.feeRate * 100}%。提现申请将在1-3个工作日内处理，请耐心等待。`,\r\n    showCancel: false\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.withdraw-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  padding: 88rpx 32rpx 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-left: 4rpx solid #fff;\r\n  border-bottom: 4rpx solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  letter-spacing: 1rpx;\r\n}\r\n\r\n.navbar-right {\r\n  width: 72rpx;\r\n  height: 72rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 24rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 可提现金额卡片 */\r\n.balance-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n  text-align: center;\r\n}\r\n\r\n.balance-header {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.balance-label {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n.balance-amount {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.currency {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #FF5722;\r\n}\r\n\r\n.amount {\r\n  font-size: 60rpx;\r\n  font-weight: 600;\r\n  color: #FF5722;\r\n}\r\n\r\n.balance-tips {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 表单卡片 */\r\n.form-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.form-header {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.form-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.form-content {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.form-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n  display: block;\r\n}\r\n\r\n.amount-input-wrap {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 10rpx;\r\n  padding: 0 20rpx;\r\n  height: 80rpx;\r\n}\r\n\r\n.currency-symbol {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.amount-input {\r\n  flex: 1;\r\n  height: 100%;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.amount-tips {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 10rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.all-amount {\r\n  color: #6B0FBE;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  background: #F5F7FA;\r\n  border-radius: 10rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.method-options {\r\n  display: flex;\r\n  margin: 0 -10rpx;\r\n}\r\n\r\n.method-option {\r\n  flex: 1;\r\n  margin: 0 10rpx;\r\n  background: #F5F7FA;\r\n  border-radius: 10rpx;\r\n  padding: 20rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  border: 2rpx solid transparent;\r\n}\r\n\r\n.method-option.active {\r\n  border-color: #6B0FBE;\r\n  background: rgba(107, 15, 190, 0.05);\r\n}\r\n\r\n.method-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  margin-bottom: 10rpx;\r\n  border-radius: 40rpx;\r\n}\r\n\r\n.method-icon.wechat {\r\n  background-color: #07C160;\r\n}\r\n\r\n.method-icon.alipay {\r\n  background-color: #1677FF;\r\n}\r\n\r\n.method-icon.bank {\r\n  background-color: #FF9500;\r\n}\r\n\r\n.method-name {\r\n  font-size: 26rpx;\r\n  color: #333;\r\n}\r\n\r\n.form-footer {\r\n  padding-top: 30rpx;\r\n  border-top: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.fee-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.submit-btn {\r\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 32rpx;\r\n  padding: 20rpx 0;\r\n  line-height: 1.5;\r\n  width: 100%;\r\n}\r\n\r\n.submit-btn.disabled {\r\n  background: #cccccc;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 提现记录 */\r\n.records-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.records-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.records-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.view-all {\r\n  font-size: 26rpx;\r\n  color: #6B0FBE;\r\n}\r\n\r\n.record-item {\r\n  padding: 20rpx 0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.record-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.record-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.record-main {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.record-type {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.record-amount {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.record-sub {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.record-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.record-status {\r\n  font-size: 24rpx;\r\n}\r\n\r\n.record-status.status-pending {\r\n  color: #FF9500;\r\n}\r\n\r\n.record-status.status-success {\r\n  color: #34C759;\r\n}\r\n\r\n.record-status.status-failed {\r\n  color: #FF3B30;\r\n}\r\n\r\n.empty-records {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 26rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 提现说明 */\r\n.tips-card {\r\n  margin: 30rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.tips-header {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.tips-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.tips-content {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.tip-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.tip-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.tip-icon {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-radius: 8rpx;\r\n  background-color: #6B0FBE;\r\n  margin-right: 16rpx;\r\n  margin-top: 10rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.tip-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  line-height: 1.6;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/distribution/pages/withdraw.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "onMounted", "distributionService", "uni", "MiniProgramPage"], "mappings": ";;;;;;AAmLA,UAAM,sBAAsBA,cAAAA,IAAI,CAAC;AAGjC,UAAM,qBAAqBC,cAAAA,SAAS;AAAA,MAClC,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,gBAAgB,CAAC,GAAG,CAAC;AAAA;AAAA,MACrB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,mBAAmB,CAAC,UAAU,UAAU,MAAM;AAAA,IAChD,CAAC;AAGD,UAAM,kBAAkB;AAAA,MACtB,EAAE,MAAM,MAAM,OAAO,SAAU;AAAA,MAC/B,EAAE,MAAM,OAAO,OAAO,SAAU;AAAA,MAChC,EAAE,MAAM,OAAO,OAAO,OAAQ;AAAA,IAChC;AAGA,UAAM,WAAWA,cAAAA,SAAS;AAAA,MACxB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,IACV,CAAC;AAGD,UAAM,oBAAoBD,cAAAA,IAAI,CAAA,CAAE;AAGhC,UAAM,YAAYE,cAAQ,SAAC,MAAM;AAC/B,YAAM,cAAc,SAAS,UACT,WAAW,SAAS,MAAM,KAAK,mBAAmB,aAClD,WAAW,SAAS,MAAM,KAAK,KAAK,IAAI,mBAAmB,WAAW,oBAAoB,KAAK;AAEnH,YAAM,eAAe,SAAS,QAAQ,KAAI,MAAO;AACjD,YAAM,gBAAgB,SAAS,WAAW,UAAU,SAAS,SAAS,KAAM,MAAK;AACjF,YAAM,gBAAgB,SAAS,SAAS,KAAI,MAAO;AACnD,YAAM,cAAc,SAAS,OAAO,KAAI,MAAO,MAAM,SAAS,OAAO,WAAW;AAEhF,aAAO,eAAe,gBAAgB,iBAAiB,iBAAiB;AAAA,IAC1E,CAAC;AAGDC,kBAAAA,UAAU,YAAY;AAEpB,YAAM,uBAAsB;AAG5B,YAAM,sBAAqB;AAG3B,YAAM,qBAAoB;AAAA,IAC5B,CAAC;AAGD,UAAM,yBAAyB,YAAY;AACzC,UAAI;AACF,cAAM,OAAO,MAAMC,0BAAmB,oBAAC,mBAAmB;AAAA,UACxD,QAAQ;AAAA;AAAA,QACd,CAAK;AAED,YAAI,MAAM;AACR,8BAAoB,QAAQ,KAAK;AAAA,QAClC;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,sDAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,wBAAwB,YAAY;AACxC,UAAI;AACF,cAAM,WAAW,MAAMD,8CAAoB;AAE3C,YAAI,UAAU;AACZ,iBAAO,OAAO,oBAAoB,QAAQ;AAAA,QAC3C;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAA,MAAA,MAAA,SAAA,sDAAc,YAAY,KAAK;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,uBAAuB,YAAY;AACvC,UAAI;AAGF,0BAAkB,QAAQ;AAAA,UACxB;AAAA,YACE,IAAI;AAAA,YACJ,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,cAAc;AAAA,YACd,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACxE;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,cAAc;AAAA,YACd,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACzE;AAAA,QACP;AAAA,MACG,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,sDAAc,YAAY,KAAK;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,SAAS;AAAQ;AAEtB,UAAI,SAAS,WAAW,SAAS,MAAM;AAGvC,eAAS,SAAS,OAAO,QAAQ,CAAC;AAGlC,UAAI,SAAS,KAAK,IAAI,mBAAmB,WAAW,oBAAoB,KAAK,GAAG;AAC9E,iBAAS,SAAS,KAAK,IAAI,mBAAmB,WAAW,oBAAoB,KAAK,EAAE,QAAQ,CAAC;AAC7FA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,WAAW,SAAS,MAAM;AAAA,UACjC,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAGD,UAAI,SAAS,mBAAmB,WAAW;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,WAAW,mBAAmB,SAAS;AAAA,UAC9C,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,eAAS,SAAS,KAAK,IAAI,mBAAmB,WAAW,oBAAoB,KAAK,EAAE,QAAQ,CAAC;AAAA,IAC/F;AAGA,UAAM,eAAe,CAAC,WAAW;AAC/B,eAAS,SAAS;AAClB,eAAS,UAAU;AACnB,UAAI,WAAW,QAAQ;AACrB,iBAAS,WAAW;AAAA,MACrB;AAAA,IACH;AAGA,UAAM,mBAAmB,YAAY;AACnC,UAAI,CAAC,UAAU;AAAO;AAEtB,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,SAAS,MAAMD,0BAAmB,oBAAC,gBAAgB;AAAA,UACvD,QAAQ,WAAW,SAAS,MAAM;AAAA,UAClC,QAAQ,SAAS;AAAA,UACjB,SAAS,SAAS;AAAA,UAClB,UAAU,SAAS;AAAA,UACnB,UAAU,SAAS;AAAA,UACnB,QAAQ,SAAS;AAAA,QACvB,CAAK;AAEDC,sBAAG,MAAC,YAAW;AAEf,YAAI,OAAO,SAAS;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS,MAAM;AAEb;AACA;AAGA,uBAAS,SAAS;AAAA,YACnB;AAAA,UACT,CAAO;AAAA,QACP,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,OAAO,WAAW;AAAA,YAC3B,YAAY;AAAA,UACpB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,sDAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,MACZ;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,MACd;AACE,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,aAAO;AAAA,QACL,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,MACd,EAAI,MAAM,KAAK;AAAA,IACf;AAGA,UAAM,mBAAmB,CAAC,WAAW;AACnC,aAAOD,0BAAmB,oBAAC,iBAAiB,MAAM;AAAA,IACpD;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,CAAC;AAAM,eAAO;AAElB,YAAM,OAAO,IAAI,KAAK,IAAI;AAC1B,aAAO,GAAG,KAAK,YAAW,CAAE,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACzH;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BC,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,mBAAmB,SAAS,OAAO,mBAAmB,SAAS,gBAAgB,mBAAmB,UAAU,GAAG;AAAA,QACjI,YAAY;AAAA,MAChB,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtcA,GAAG,WAAWC,SAAe;"}