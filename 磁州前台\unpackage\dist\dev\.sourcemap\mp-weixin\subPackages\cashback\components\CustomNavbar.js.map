{"version": 3, "file": "CustomNavbar.js", "sources": ["subPackages/cashback/components/CustomNavbar.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvY2FzaGJhY2svY29tcG9uZW50cy9DdXN0b21OYXZiYXIudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n    <view class=\"navbar-content\">\r\n      <!-- 返回按钮 -->\r\n      <view v-if=\"showBack\" class=\"navbar-back\" @tap=\"goBack\">\r\n        <image class=\"back-icon\" src=\"/static/images/cashback/back.png\" mode=\"aspectFit\" />\r\n      </view>\r\n      <view v-else class=\"navbar-placeholder\"></view>\r\n      \r\n      <!-- 标题 -->\r\n      <view class=\"navbar-title\">\r\n        <text>{{ title }}</text>\r\n      </view>\r\n      \r\n      <!-- 关闭按钮 -->\r\n      <view v-if=\"showClose\" class=\"navbar-close\" @tap=\"close\">\r\n        <svg class=\"close-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">\r\n          <path fill=\"#FFFFFF\" d=\"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\" />\r\n        </svg>\r\n      </view>\r\n      <view v-else class=\"navbar-placeholder\"></view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CustomNavbar',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: '返利商城'\r\n    },\r\n    showBack: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showClose: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20\r\n    };\r\n  },\r\n  created() {\r\n    // 获取状态栏高度\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = systemInfo.statusBarHeight || 20;\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack({\r\n        delta: 1,\r\n        fail: () => {\r\n          uni.switchTab({\r\n            url: '/pages/index/index'\r\n          });\r\n        }\r\n      });\r\n    },\r\n    close() {\r\n      // 关闭当前页面，返回上一页面或首页\r\n      uni.navigateBack({\r\n        delta: 1,\r\n        fail: () => {\r\n          uni.switchTab({\r\n            url: '/pages/index/index'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background: linear-gradient(135deg, #AB47BC 0%, #9C27B0 100%);\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 44px;\r\n    padding: 0 16px;\r\n  }\r\n  \r\n  .navbar-back, .navbar-close, .navbar-placeholder {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 32px;\r\n    height: 32px;\r\n    border-radius: 16px;\r\n    \r\n    .back-icon {\r\n      width: 20px;\r\n      height: 20px;\r\n    }\r\n    \r\n    .close-icon {\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n  \r\n  .navbar-title {\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 50%;\r\n    transform: translate(-50%, -50%);\r\n    text-align: center;\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    max-width: 60%;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/cashback/components/CustomNavbar.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;AA0BA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA;EAEpB;AAAA,EACD,UAAU;AAER,UAAM,aAAaA,oBAAI;AACvB,SAAK,kBAAkB,WAAW,mBAAmB;AAAA,EACtD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,QACf,OAAO;AAAA,QACP,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACP,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,QAAQ;AAENA,oBAAAA,MAAI,aAAa;AAAA,QACf,OAAO;AAAA,QACP,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACP,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1EA,GAAG,gBAAgB,SAAS;"}