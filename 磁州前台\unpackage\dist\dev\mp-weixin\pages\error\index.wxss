
.error-page {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-height: 100vh;
	background-color: #f5f7fa;
	position: relative;
	padding: 0 30rpx;
}
.error-header-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 40vh;
	background: linear-gradient(to bottom, #0066FF, #409EFF);
	border-radius: 0 0 50rpx 50rpx;
	z-index: 0;
}
.error-icon-container {
	margin-top: 15vh;
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 240rpx;
	height: 240rpx;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	box-shadow: 0 8rpx 24rpx rgba(0, 102, 255, 0.15);
}
.error-icon {
	width: 160rpx;
	height: 160rpx;
}
.error-content {
	width: 100%;
	padding: 40rpx;
	margin-top: 60rpx;
	background-color: #FFFFFF;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	z-index: 1;
	text-align: center;
}
.error-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 20rpx;
	display: block;
}
.error-message {
	font-size: 30rpx;
	color: #666666;
	line-height: 1.5;
	margin-bottom: 20rpx;
	display: block;
}
.error-details {
	font-size: 26rpx;
	color: #999999;
	background-color: #f7f7f7;
	padding: 20rpx;
	border-radius: 12rpx;
	text-align: left;
	word-break: break-word;
	margin-top: 20rpx;
	display: block;
	max-height: 300rpx;
	overflow-y: auto;
}
.error-actions {
	width: 100%;
	margin-top: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 1;
}
.action-btn {
	width: 80%;
	margin-bottom: 20rpx;
	height: 80rpx;
	line-height: 80rpx;
	font-size: 30rpx;
	border-radius: 40rpx;
}
.primary {
	background-color: #0066FF;
	color: #FFFFFF;
}
.secondary {
	background-color: #FFFFFF;
	color: #0066FF;
	border: 1rpx solid #0066FF;
}
.tertiary {
	background-color: transparent;
	color: #666666;
	font-size: 26rpx;
}
