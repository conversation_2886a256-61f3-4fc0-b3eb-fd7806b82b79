"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_circle = common_vendor.resolveComponent("circle");
  (_component_path + _component_svg + _component_rect + _component_line + _component_circle)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const unreadMessages = common_vendor.ref(3);
    const countdown = common_vendor.ref({ hours: "00", minutes: "00", seconds: "00" });
    let countdownTimer = null;
    const banners = [
      {
        id: "1",
        image: "/static/demo/banner1.jpg",
        type: "flash_sale",
        targetId: "1001"
      },
      {
        id: "2",
        image: "/static/demo/banner2.jpg",
        type: "group_buy",
        targetId: "1002"
      },
      {
        id: "3",
        image: "/static/demo/banner3.jpg",
        type: "activity",
        targetId: "1003"
      }
    ];
    const promotionTypes = [
      {
        name: "限时特惠",
        icon: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",
        url: "/subPackages/activity-showcase/pages/flash-sale/index",
        bgColor: "linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)"
      },
      {
        name: "拼团优惠",
        icon: "M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87m-4-12a4 4 0 010 7.75",
        url: "/subPackages/activity-showcase/pages/group-buy/index",
        bgColor: "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)"
      },
      {
        name: "满减优惠",
        icon: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
        url: "/subPackages/activity-showcase/pages/discount/index",
        bgColor: "linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)"
      },
      {
        name: "优惠券",
        icon: "M20 7h-4m4 0v16H4V7h4m12 0l-3-3H7l-3 3m4 0h10",
        url: "/subPackages/activity-showcase/pages/coupon/index",
        bgColor: "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)"
      },
      {
        name: "全部活动",
        icon: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",
        url: "/subPackages/activity-showcase/pages/list/index",
        bgColor: "linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)"
      }
    ];
    const flashSaleItems = [
      {
        id: "1001",
        title: "磁州窑陶艺体验课程",
        image: "/static/demo/product1.jpg",
        currentPrice: 99,
        originalPrice: 198,
        discount: "5折",
        soldCount: 356,
        soldPercentage: 85
      },
      {
        id: "1002",
        title: "磁州特产礼盒",
        image: "/static/demo/product2.jpg",
        currentPrice: 128,
        originalPrice: 198,
        discount: "6.5折",
        soldCount: 512,
        soldPercentage: 60
      },
      {
        id: "1003",
        title: "户外露营套装",
        image: "/static/demo/product3.jpg",
        currentPrice: 199,
        originalPrice: 399,
        discount: "5折",
        soldCount: 128,
        soldPercentage: 90
      },
      {
        id: "1004",
        title: "亲子烘焙课程",
        image: "/static/demo/product4.jpg",
        currentPrice: 158,
        originalPrice: 258,
        discount: "6折",
        soldCount: 245,
        soldPercentage: 75
      }
    ];
    const groupBuyItems = [
      {
        id: "2001",
        title: "磁州文化体验一日游",
        image: "/static/demo/activity1.jpg",
        groupPrice: 99,
        originalPrice: 198,
        groupSize: 3
      },
      {
        id: "2002",
        title: "亲子户外拓展活动",
        image: "/static/demo/activity2.jpg",
        groupPrice: 128,
        originalPrice: 198,
        groupSize: 2
      },
      {
        id: "2003",
        title: "传统文化体验课",
        image: "/static/demo/activity4.jpg",
        groupPrice: 88,
        originalPrice: 158,
        groupSize: 3
      },
      {
        id: "2004",
        title: "磁州美食体验",
        image: "/static/demo/product6.jpg",
        groupPrice: 68,
        originalPrice: 128,
        groupSize: 4
      },
      {
        id: "2005",
        title: "陶艺DIY体验",
        image: "/static/demo/product7.jpg",
        groupPrice: 79,
        originalPrice: 158,
        groupSize: 2
      }
    ];
    const discountShops = [
      {
        id: "3001",
        name: "磁州文化体验馆",
        logo: "/static/demo/shop1.jpg",
        discounts: ["满200减30", "满500减100"]
      },
      {
        id: "3002",
        name: "磁州美食城",
        logo: "/static/demo/shop2.jpg",
        discounts: ["满100减15", "满300减50"]
      },
      {
        id: "3003",
        name: "磁州户外拓展中心",
        logo: "/static/demo/shop3.jpg",
        discounts: ["满300减50", "满600减120"]
      },
      {
        id: "3004",
        name: "磁州亲子乐园",
        logo: "/static/demo/shop4.jpg",
        discounts: ["满150减30", "满300减70"]
      }
    ];
    const coupons = [
      {
        id: "4001",
        name: "新人专享券",
        shopName: "磁州文化体验馆",
        type: "cash",
        value: "30",
        condition: "满200可用",
        validityPeriod: "有效期至2024-06-30"
      },
      {
        id: "4002",
        name: "美食专享券",
        shopName: "磁州美食城",
        type: "cash",
        value: "15",
        condition: "满100可用",
        validityPeriod: "有效期至2024-06-15"
      },
      {
        id: "4003",
        name: "户外活动券",
        shopName: "磁州户外拓展中心",
        type: "discount",
        value: "8.5",
        condition: "无门槛",
        validityPeriod: "有效期至2024-06-20"
      },
      {
        id: "4004",
        name: "亲子活动券",
        shopName: "磁州亲子乐园",
        type: "cash",
        value: "50",
        condition: "满300可用",
        validityPeriod: "有效期至2024-06-25"
      }
    ];
    const recommendedActivities = [
      {
        id: "5001",
        title: "磁州文化节",
        image: "/static/demo/activity1.jpg",
        date: "2024-06-15",
        location: "磁州文化广场",
        price: 0,
        participants: [
          "/static/demo/avatar1.png",
          "/static/demo/avatar2.png",
          "/static/demo/avatar3.png",
          "/static/demo/avatar4.png",
          "/static/demo/avatar5.png"
        ]
      },
      {
        id: "5002",
        title: "亲子户外拓展活动",
        image: "/static/demo/activity2.jpg",
        date: "2024-05-28",
        location: "磁州森林公园",
        price: 128,
        participants: [
          "/static/demo/avatar1.png",
          "/static/demo/avatar3.png",
          "/static/demo/avatar5.png"
        ]
      },
      {
        id: "5003",
        title: "社区篮球赛",
        image: "/static/demo/activity3.jpg",
        date: "2024-06-05",
        location: "磁州体育中心",
        price: 50,
        participants: [
          "/static/demo/avatar2.png",
          "/static/demo/avatar4.png",
          "/static/demo/avatar5.png",
          "/static/demo/avatar1.png"
        ]
      }
    ];
    common_vendor.onMounted(() => {
      startCountdown();
    });
    common_vendor.onUnmounted(() => {
      if (countdownTimer) {
        clearInterval(countdownTimer);
      }
    });
    const startCountdown = () => {
      const endTime = /* @__PURE__ */ new Date();
      endTime.setHours(endTime.getHours() + 12);
      countdownTimer = setInterval(() => {
        const now = /* @__PURE__ */ new Date();
        const diff = endTime - now;
        if (diff <= 0) {
          clearInterval(countdownTimer);
          countdown.value = { hours: "00", minutes: "00", seconds: "00" };
          return;
        }
        const hours = Math.floor(diff / (1e3 * 60 * 60));
        const minutes = Math.floor(diff % (1e3 * 60 * 60) / (1e3 * 60));
        const seconds = Math.floor(diff % (1e3 * 60) / 1e3);
        countdown.value = {
          hours: hours.toString().padStart(2, "0"),
          minutes: minutes.toString().padStart(2, "0"),
          seconds: seconds.toString().padStart(2, "0")
        };
      }, 1e3);
    };
    const handleBannerClick = (banner) => {
      switch (banner.type) {
        case "flash_sale":
          navigateTo(`/subPackages/activity-showcase/pages/flash-sale/detail?id=${banner.targetId}`);
          break;
        case "group_buy":
          navigateTo(`/subPackages/activity-showcase/pages/group-buy/detail?id=${banner.targetId}`);
          break;
        case "activity":
          navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${banner.targetId}`);
          break;
        default:
          navigateTo("/subPackages/activity-showcase/pages/list/index");
      }
    };
    const viewFlashSaleDetail = (item) => {
      navigateTo(`/subPackages/activity-showcase/pages/flash-sale/detail?id=${item.id}`);
    };
    const viewGroupBuyDetail = (item) => {
      navigateTo(`/subPackages/activity-showcase/pages/group-buy/detail?id=${item.id}`);
    };
    const viewShopDetail = (shop) => {
      navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.id}`);
    };
    const viewActivityDetail = (activity) => {
      navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${activity.id}`);
    };
    const receiveCoupon = (coupon) => {
      common_vendor.index.showToast({
        title: "领取成功",
        icon: "success"
      });
    };
    const getCouponBackground = (type) => {
      const bgMap = {
        "cash": "linear-gradient(to right, #FF9500, #FFCC00)",
        "discount": "linear-gradient(to right, #5AC8FA, #90E0FF)",
        "free": "linear-gradient(to right, #34C759, #7ED321)"
      };
      return bgMap[type] || bgMap["cash"];
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M19 12H5M12 19l-7-7 7-7",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(goBack),
        d: common_vendor.p({
          d: "M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        f: unreadMessages.value > 0
      }, unreadMessages.value > 0 ? {
        g: common_vendor.t(unreadMessages.value > 99 ? "99+" : unreadMessages.value)
      } : {}, {
        h: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/message/index")),
        i: common_vendor.f(banners, (banner, index, i0) => {
          return {
            a: banner.image,
            b: common_vendor.o(($event) => handleBannerClick(banner), index),
            c: index
          };
        }),
        j: common_vendor.f(promotionTypes, (item, index, i0) => {
          return {
            a: "648aa685-5-" + i0 + "," + ("648aa685-4-" + i0),
            b: common_vendor.p({
              d: item.icon,
              stroke: "#FFFFFF",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            c: "648aa685-4-" + i0,
            d: item.bgColor,
            e: common_vendor.t(item.name),
            f: index,
            g: common_vendor.o(($event) => navigateTo(item.url), index)
          };
        }),
        k: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        l: common_vendor.t(countdown.value.hours),
        m: common_vendor.t(countdown.value.minutes),
        n: common_vendor.t(countdown.value.seconds),
        o: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF3B30",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        q: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/flash-sale/index")),
        r: common_vendor.f(flashSaleItems, (item, index, i0) => {
          return common_vendor.e({
            a: item.image,
            b: common_vendor.t(item.discount),
            c: common_vendor.t(item.title),
            d: common_vendor.t(item.currentPrice.toFixed(2)),
            e: common_vendor.t(item.originalPrice.toFixed(2)),
            f: `${item.soldPercentage}%`,
            g: common_vendor.t(item.soldCount),
            h: item.soldPercentage > 80
          }, item.soldPercentage > 80 ? {} : {}, {
            i: index,
            j: common_vendor.o(($event) => viewFlashSaleDetail(item), index)
          });
        }),
        s: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        t: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        v: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/group-buy/index")),
        w: common_vendor.f(groupBuyItems.slice(0, 2), (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.groupSize),
            d: common_vendor.t(item.groupPrice.toFixed(2)),
            e: common_vendor.t(item.originalPrice.toFixed(2)),
            f: index,
            g: common_vendor.o(($event) => viewGroupBuyDetail(item), index)
          };
        }),
        x: common_vendor.f(groupBuyItems.slice(2, 5), (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.groupSize),
            d: common_vendor.t(item.groupPrice.toFixed(2)),
            e: common_vendor.t(item.originalPrice.toFixed(2)),
            f: index,
            g: common_vendor.o(($event) => viewGroupBuyDetail(item), index)
          };
        }),
        y: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        z: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        A: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/discount/index")),
        B: common_vendor.f(discountShops, (shop, index, i0) => {
          return {
            a: shop.logo,
            b: common_vendor.t(shop.name),
            c: common_vendor.f(shop.discounts, (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            d: index,
            e: common_vendor.o(($event) => viewShopDetail(shop), index)
          };
        }),
        C: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF9500",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        D: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        E: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/coupon/index")),
        F: common_vendor.f(coupons, (coupon, index, i0) => {
          return common_vendor.e({
            a: coupon.type !== "discount"
          }, coupon.type !== "discount" ? {} : {}, {
            b: common_vendor.t(coupon.value),
            c: coupon.type === "discount"
          }, coupon.type === "discount" ? {} : {}, {
            d: common_vendor.t(coupon.condition),
            e: common_vendor.t(coupon.name),
            f: common_vendor.t(coupon.shopName),
            g: common_vendor.t(coupon.validityPeriod),
            h: index,
            i: getCouponBackground(coupon.type),
            j: common_vendor.o(($event) => receiveCoupon(), index)
          });
        }),
        G: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF3B69",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        H: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        I: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/list/index")),
        J: common_vendor.f(recommendedActivities, (activity, index, i0) => {
          return common_vendor.e({
            a: activity.image,
            b: common_vendor.t(activity.title),
            c: "648aa685-17-" + i0 + "," + ("648aa685-16-" + i0),
            d: "648aa685-18-" + i0 + "," + ("648aa685-16-" + i0),
            e: "648aa685-19-" + i0 + "," + ("648aa685-16-" + i0),
            f: "648aa685-20-" + i0 + "," + ("648aa685-16-" + i0),
            g: "648aa685-16-" + i0,
            h: common_vendor.t(activity.date),
            i: "648aa685-22-" + i0 + "," + ("648aa685-21-" + i0),
            j: "648aa685-23-" + i0 + "," + ("648aa685-21-" + i0),
            k: "648aa685-21-" + i0,
            l: common_vendor.t(activity.location),
            m: activity.price > 0
          }, activity.price > 0 ? {
            n: common_vendor.t(activity.price.toFixed(2))
          } : {}, {
            o: common_vendor.f(activity.participants.slice(0, 3), (avatar, avatarIndex, i1) => {
              return {
                a: avatarIndex,
                b: avatar
              };
            }),
            p: common_vendor.t(activity.participants.length),
            q: index,
            r: common_vendor.o(($event) => viewActivityDetail(activity), index)
          });
        }),
        K: common_vendor.p({
          x: "3",
          y: "4",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        L: common_vendor.p({
          x1: "16",
          y1: "2",
          x2: "16",
          y2: "6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        M: common_vendor.p({
          x1: "8",
          y1: "2",
          x2: "8",
          y2: "6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        N: common_vendor.p({
          x1: "3",
          y1: "10",
          x2: "21",
          y2: "10",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        O: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        P: common_vendor.p({
          d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Q: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        R: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-648aa685"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/promotion-center/index.js.map
