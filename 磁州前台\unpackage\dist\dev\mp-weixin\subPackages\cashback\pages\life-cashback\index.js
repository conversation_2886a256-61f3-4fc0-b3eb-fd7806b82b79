"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
const CustomNavbar = () => "../../components/CustomNavbar.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {};
  },
  onLoad() {
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#9C27B0"
    });
  },
  methods: {
    navigateToService(type, id = 0) {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/life-service/index?type=${type}${id ? "&id=" + id : ""}`
      });
    },
    goToCoupons() {
      common_vendor.index.showToast({
        title: "优惠券功能正在开发中",
        icon: "none",
        duration: 2e3
      });
    },
    navigateToActivity(id) {
      common_vendor.index.showToast({
        title: "活动详情功能正在开发中",
        icon: "none",
        duration: 2e3
      });
    }
  }
};
if (!Array) {
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_custom_navbar + _component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "生活返现",
      ["show-back"]: true
    }),
    b: common_vendor.p({
      fill: "#FF6B6B",
      d: "M15.5,21L14,8H16.23L15.1,3.46L16.84,3L18.09,8H22L20.5,21H15.5M5,11H10A3,3 0 0,1 13,14H2A3,3 0 0,1 5,11M13,18A3,3 0 0,1 10,21H5A3,3 0 0,1 2,18H13M3,15H8L9.5,16.5L11,15H12A1,1 0 0,1 13,16A1,1 0 0,1 12,17H3A1,1 0 0,1 2,16A1,1 0 0,1 3,15Z"
    }),
    c: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "28",
      height: "28"
    }),
    d: common_vendor.o(($event) => $options.navigateToService("takeout")),
    e: common_vendor.p({
      fill: "#FFA726",
      d: "M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z"
    }),
    f: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "28",
      height: "28"
    }),
    g: common_vendor.o(($event) => $options.navigateToService("taxi")),
    h: common_vendor.p({
      fill: "#E91E63",
      d: "M18,9H16V7H18M18,13H16V11H18M18,17H16V15H18M8,9H6V7H8M8,13H6V11H8M8,17H6V15H8M18,3V5H16V3H8V5H6V3H4V21H6V19H8V21H16V19H18V21H20V3H18Z"
    }),
    i: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "28",
      height: "28"
    }),
    j: common_vendor.o(($event) => $options.navigateToService("movie")),
    k: common_vendor.p({
      fill: "#2196F3",
      d: "M3,14H5V20H19V14H21V21A1,1 0 0,1 20,22H4A1,1 0 0,1 3,21V14M17,4H7V2H17V4M17.5,5L12,10.5L6.5,5H17.5M20,6.4L17.9,8.5L15.5,6.1L16.9,4.7L20,7.8V6.4M5.93,4.7L7.33,6.1L4.93,8.5L2.83,6.4V7.8L5.93,4.7Z"
    }),
    l: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "28",
      height: "28"
    }),
    m: common_vendor.o(($event) => $options.navigateToService("express")),
    n: common_vendor.p({
      fill: "#4CAF50",
      d: "M19,7H11V14H3V5H1V20H3V17H21V20H23V11A4,4 0 0,0 19,7M7,13A3,3 0 0,0 10,10A3,3 0 0,0 7,7A3,3 0 0,0 4,10A3,3 0 0,0 7,13Z"
    }),
    o: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "28",
      height: "28"
    }),
    p: common_vendor.o(($event) => $options.navigateToService("hotel")),
    q: common_vendor.p({
      fill: "#00BCD4",
      d: "M21,16V14L13,9V3.5A1.5,1.5 0 0,0 11.5,2A1.5,1.5 0 0,0 10,3.5V9L2,14V16L10,13.5V19L8,20.5V22L11.5,21L15,22V20.5L13,19V13.5L21,16Z"
    }),
    r: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "28",
      height: "28"
    }),
    s: common_vendor.o(($event) => $options.navigateToService("flight")),
    t: common_vendor.p({
      fill: "#FF9800",
      d: "M18,10H6V5H18M12,17C10.89,17 10,16.1 10,15C10,13.89 10.89,13 12,13A2,2 0 0,1 14,15A2,2 0 0,1 12,17M4,15.5A3.5,3.5 0 0,0 7.5,19L6,20.5V21H18V20.5L16.5,19A3.5,3.5 0 0,0 20,15.5V5C20,1.5 16.42,1 12,1C7.58,1 4,1.5 4,5V15.5Z"
    }),
    v: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "28",
      height: "28"
    }),
    w: common_vendor.o(($event) => $options.navigateToService("train")),
    x: common_vendor.p({
      fill: "#9C27B0",
      d: "M12,8H4A2,2 0 0,0 2,10V14A2,2 0 0,0 4,16H5V20A1,1 0 0,0 6,21H8A1,1 0 0,0 9,20V16H12L17,20V4L12,8M21.5,12C21.5,13.71 20.54,15.26 19,16V8C20.53,8.75 21.5,10.3 21.5,12Z"
    }),
    y: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "28",
      height: "28"
    }),
    z: common_vendor.o(($event) => $options.navigateToService("vip")),
    A: common_vendor.p({
      fill: "#999999",
      d: "M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
    }),
    B: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "16",
      height: "16"
    }),
    C: common_vendor.o((...args) => $options.goToCoupons && $options.goToCoupons(...args)),
    D: common_vendor.o(($event) => $options.navigateToService("coupon", 1)),
    E: common_vendor.o(($event) => $options.navigateToService("coupon", 2)),
    F: common_vendor.o(($event) => $options.navigateToService("coupon", 3)),
    G: common_assets._imports_0$54,
    H: common_vendor.o(($event) => $options.navigateToActivity(1)),
    I: common_assets._imports_1$50,
    J: common_vendor.o(($event) => $options.navigateToActivity(2))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ef32ba16"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/cashback/pages/life-cashback/index.js.map
