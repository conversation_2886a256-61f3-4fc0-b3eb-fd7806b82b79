<template>
  <view class="education-detail-container">
    <!-- 添加自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">教育培训详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <view class="education-detail-wrapper">
      <!-- 课程基本信息卡片 -->
      <view class="content-card course-info-card">
        <view class="course-header">
          <view class="course-title-row">
            <text class="course-title">{{courseData.title}}</text>
            <text class="course-price">{{courseData.price}}</text>
          </view>
          <view class="course-meta">
            <view class="course-tag-group">
              <view class="course-tag" v-for="(tag, index) in courseData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="course-publish-time">发布于 {{formatTime(courseData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 课程图片轮播 -->
        <swiper class="course-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
          <swiper-item v-for="(image, index) in courseData.images" :key="index">
            <image :src="image" mode="aspectFill" class="course-image"></image>
          </swiper-item>
        </swiper>
        
        <!-- 基本信息 -->
        <view class="course-basic-info">
          <view class="info-item">
            <text class="info-label">课程类型</text>
            <text class="info-value">{{courseData.type}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">适合人群</text>
            <text class="info-value">{{courseData.target}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">开课时间</text>
            <text class="info-value">{{courseData.startTime}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">课程周期</text>
            <text class="info-value">{{courseData.duration}}</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="contact-header">
          <text class="card-title">联系方式</text>
        </view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{courseData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{courseData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 课程内容 -->
      <view class="content-card course-content-card">
        <view class="section-title">课程内容</view>
        <view class="content-list">
          <view class="content-item" v-for="(item, index) in courseData.contents" :key="index">
            <text class="content-label">{{item.label}}</text>
            <text class="content-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 课程安排 -->
      <view class="content-card schedule-card">
        <view class="section-title">课程安排</view>
        <view class="schedule-list">
          <view class="schedule-item" v-for="(item, index) in courseData.schedule" :key="index">
            <view class="schedule-time">{{item.time}}</view>
            <view class="schedule-info">
              <text class="schedule-title">{{item.title}}</text>
              <text class="schedule-desc">{{item.description}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 教学特色 -->
      <view class="content-card features-card">
        <view class="section-title">教学特色</view>
        <view class="features-list">
          <view class="feature-item" v-for="(item, index) in courseData.features" :key="index">
            <text class="feature-icon iconfont" :class="item.icon"></text>
            <view class="feature-info">
              <text class="feature-title">{{item.title}}</text>
              <text class="feature-desc">{{item.description}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 教师信息 -->
      <view class="content-card teacher-card">
        <view class="teacher-header">
          <view class="teacher-avatar">
            <image :src="courseData.teacher.avatar" mode="aspectFill"></image>
          </view>
          <view class="teacher-info">
            <text class="teacher-name">{{courseData.teacher.name}}</text>
            <view class="teacher-meta">
              <text class="teacher-title">{{courseData.teacher.title}}</text>
              <text class="teacher-rating">好评率 {{courseData.teacher.rating}}</text>
            </view>
          </view>
          <view class="teacher-auth" v-if="courseData.teacher.isVerified">
            <text class="iconfont icon-verified"></text>
            <text class="auth-text">已认证</text>
          </view>
        </view>
        <view class="teacher-intro">
          <text class="intro-text">{{courseData.teacher.introduction}}</text>
        </view>
      </view>
      
      <!-- 相似课程推荐 -->
      <view class="content-card similar-courses-card">
        <view class="section-title">相关课程推荐</view>
        <view class="related-courses-content">
          <!-- 简洁的课程列表 -->
          <view class="related-courses-list">
            <view class="related-course-item" 
                 v-for="(course, index) in relatedCourses.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToCourseDetail(course.id)">
              <view class="course-item-content">
                <view class="course-item-left">
                  <image class="course-image" :src="course.image" mode="aspectFill"></image>
                </view>
                <view class="course-item-middle">
                  <text class="course-item-title">{{course.title}}</text>
                  <view class="course-item-target">{{course.type}} · {{course.target}}</view>
                  <view class="course-item-tags">
                    <text class="course-item-tag" v-for="(tag, tagIndex) in course.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="course-item-tag-more" v-if="course.tags && course.tags.length > 2">+{{course.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="course-item-right">
                  <text class="course-item-price">{{course.price}}</text>
                </view>
              </view>
            </view>
            
            <!-- 暂无数据提示 -->
            <view class="empty-related-courses" v-if="relatedCourses.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关课程</text>
            </view>
          </view>
          
          <!-- 查看更多按钮 -->
          <view class="view-more-btn" v-if="relatedCourses.length > 0" @click.stop="navigateToEducationList">
            <text class="view-more-text">查看更多相关课程</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 状态栏高度
const statusBarHeight = ref(20);

// 获取状态栏高度
const getStatusBarHeight = () => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 20;
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);
const courseData = ref({
  id: 'course12345',
  title: '少儿英语启蒙课程',
  price: '2999元/期',
  tags: ['小班教学', '外教授课', '免费试听', '趣味教学'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  images: [
    '/static/images/course1.jpg',
    '/static/images/course2.jpg',
    '/static/images/course3.jpg',
    '/static/images/course4.jpg'
  ],
  type: '少儿英语',
  target: '4-12岁儿童',
  startTime: '2024-04-01',
  duration: '3个月',
  contents: [
    { label: '课程目标', value: '培养英语兴趣，建立语言基础，提升口语表达能力' },
    { label: '课程特色', value: '互动教学，寓教于乐，情景对话，角色扮演' },
    { label: '教学方式', value: '小班教学（每班8人），外教授课，中教辅助' },
    { label: '课程安排', value: '每周2次课，每次90分钟，课后作业辅导' },
    { label: '教材使用', value: '牛津少儿英语教材，配套练习册和多媒体资源' },
    { label: '学习效果', value: '掌握300个基础词汇，能进行简单日常对话' }
  ],
  schedule: [
    {
      time: '周一 16:00-17:30',
      title: '英语启蒙',
      description: '基础词汇、简单对话、字母发音'
    },
    {
      time: '周三 16:00-17:30',
      title: '趣味英语',
      description: '英语游戏、歌曲学习、情景对话'
    },
    {
      time: '周六 10:00-11:30',
      title: '英语实践',
      description: '角色扮演、故事阅读、口语练习'
    },
    {
      time: '周日 10:00-11:30',
      title: '英语活动',
      description: '英语角、文化体验、成果展示'
    }
  ],
  features: [
    {
      icon: 'icon-teacher',
      title: '专业外教',
      description: '来自英语母语国家的外教，均持有TESOL/TEFL证书'
    },
    {
      icon: 'icon-class',
      title: '小班教学',
      description: '每班不超过8人，确保每个孩子都能得到充分关注'
    },
    {
      icon: 'icon-material',
      title: '优质教材',
      description: '使用国际知名教材，配套丰富的多媒体教学资源'
    },
    {
      icon: 'icon-method',
      title: '趣味教学',
      description: '通过游戏、歌曲、故事等多种方式激发学习兴趣'
    },
    {
      icon: 'icon-progress',
      title: '进度跟踪',
      description: '定期评估学习效果，及时调整教学计划'
    }
  ],
  teacher: {
    name: 'Sarah Johnson',
    avatar: '/static/images/avatar.png',
    title: '资深外教',
    rating: '98%',
    isVerified: true,
    introduction: '来自英国伦敦，拥有剑桥大学教育学硕士学位，TESOL证书持有者。5年少儿英语教学经验，擅长互动教学和趣味教学法。曾在新加坡国际学校任教，深受学生喜爱。教学风格活泼有趣，善于激发孩子的学习兴趣。'
  },
  contact: {
    name: '王老师',
    phone: '13912345678'
  }
});

const similarCourses = ref([
  {
    id: 'course001',
    title: '少儿英语进阶课程',
    price: '3999元/期',
    type: '少儿英语',
    target: '6-12岁儿童',
    image: '/static/images/course-similar1.jpg'
  },
  {
    id: 'course002',
    title: '少儿英语口语课程',
    price: '2599元/期',
    type: '少儿英语',
    target: '4-12岁儿童',
    image: '/static/images/course-similar2.jpg'
  },
  {
    id: 'course003',
    title: '少儿英语阅读写作',
    price: '3299元/期',
    type: '少儿英语',
    target: '8-12岁儿童',
    image: '/static/images/course-similar3.jpg'
  },
  {
    id: 'course004',
    title: '少儿英语自然拼读',
    price: '2899元/期',
    type: '少儿英语',
    target: '5-10岁儿童',
    image: '/static/images/course-similar4.jpg'
  }
]);

// 相关课程推荐数据
const relatedCourses = ref([]);

// 加载相关课程推荐
const loadRelatedCourses = () => {
  // 这里应该调用API获取数据
  // 实际项目中应该根据当前课程的类型、标签等进行相关性匹配
  
  // 使用现有数据作为模拟数据
  setTimeout(() => {
    relatedCourses.value = similarCourses.value.map(course => ({
      ...course,
      tags: ['专业师资', '小班教学', '趣味教学']
    }));
  }, 500);
};

// 跳转到课程详情页
const navigateToCourseDetail = (courseId) => {
  // 防止跳转到当前页面
  if (courseId === courseData.value.id) {
    return;
  }
  
  uni.navigateTo({
    url: `/pages/publish/education-detail?id=${courseId}`
  });
};

// 跳转到教育培训列表页
const navigateToEducationList = (e) => {
  if (e) e.stopPropagation();
  const educationCategory = courseData.value.tags?.[0] || '';
  uni.navigateTo({ 
    url: `/subPackages/service/pages/filter?type=education&title=${encodeURIComponent('教育培训')}&category=${encodeURIComponent(educationCategory)}&active=education` 
  });
};

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: courseData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

const navigateToCourse = (id) => {
  uni.navigateTo({
    url: `/pages/publish/education-detail?id=${id}`
  });
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  if (!courseData.value.contact || !courseData.value.contact.id) {
    uni.showToast({
      title: '无法获取教育机构信息',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/index?userId=${courseData.value.contact.id}&username=${encodeURIComponent(courseData.value.contact.name || '教育机构')}`
  });
};

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  getStatusBarHeight();
  
  // 修改页面标题
  uni.setNavigationBarTitle({
    title: '课程详情'
  });
  
  // 设置导航栏颜色
  uni.setNavigationBarColor({
    frontColor: '#000000',
    backgroundColor: '#ffffff'
  });
  
  // 获取路由参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 获取课程ID
  const id = options.id || '';
  console.log('课程详情页ID:', id);
  
  // 加载相关课程推荐
  loadRelatedCourses();
});
</script>

<style>
/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #FFFFFF; /* 修改为白色文字 */
}

.navbar-right {
  width: 60px;
}

.education-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
}

.education-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 课程基本信息卡片 */
.course-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.course-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.course-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}

.course-meta {
  margin-bottom: 24rpx;
}

.course-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.course-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.course-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.course-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.course-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.course-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 课程内容 */
.content-list {
  display: flex;
  flex-direction: column;
}

.content-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.content-item:last-child {
  border-bottom: none;
}

.content-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.content-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 课程安排 */
.schedule-list {
  display: flex;
  flex-direction: column;
}

.schedule-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-time {
  width: 200rpx;
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 500;
}

.schedule-info {
  flex: 1;
}

.schedule-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.schedule-desc {
  font-size: 26rpx;
  color: #666;
}

/* 教学特色 */
.features-list {
  display: flex;
  flex-direction: column;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 40rpx;
  color: #1890ff;
  margin-right: 20rpx;
}

.feature-info {
  flex: 1;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
}

/* 教师信息 */
.teacher-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.teacher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.teacher-avatar image {
  width: 100%;
  height: 100%;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.teacher-meta {
  display: flex;
  align-items: center;
}

.teacher-title, .teacher-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

.teacher-intro {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.intro-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 相似课程推荐样式优化 */
.similar-courses-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.related-courses-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 10px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 3px;
  height: 16px;
  background-color: #0052CC;
  border-radius: 3px;
}

/* 相关课程列表样式 */
.related-courses-list {
  margin-bottom: 12px;
}

.related-course-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.related-course-item:last-child {
  border-bottom: none;
}

.course-item-content {
  display: flex;
  align-items: center;
}

.course-item-left {
  margin-right: 12px;
}

.course-image {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  background-color: #f5f7fa;
  object-fit: cover;
}

.course-item-middle {
  flex: 1;
}

.course-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.course-item-target {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.course-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.course-item-tag {
  font-size: 12px;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  margin-right: 8px;
  margin-bottom: 4px;
}

.course-item-tag-more {
  font-size: 12px;
  color: #999;
}

.course-item-right {
  text-align: right;
  margin-left: 10px;
}

.course-item-price {
  font-size: 16px;
  font-weight: 500;
  color: #ff4d4f;
}

/* 查看更多按钮 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  border-top: 1px solid #f5f5f5;
}

.view-more-text {
  font-size: 14px;
  color: #1890ff;
  margin-right: 4px;
}

.view-more-icon {
  font-size: 12px;
  color: #1890ff;
}

/* 空状态 */
.empty-related-courses {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image {
  width: 100px;
  height: 100px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 底部操作栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.toolbar-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 20rpx;
  color: #666;
}

.call-button {
  flex: 2;
  background-color: #1890ff;
  color: #fff;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.call-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
}

.call-subtitle {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4rpx;
}

.share-button {
  background-color: transparent;
  padding: 0;
  margin: 0;
  line-height: normal;
  border: none;
}

.share-button::after {
  border: none;
}

.hidden-share-btn {
  position: absolute;
  top: -999px;
  left: -999px;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}
</style>
