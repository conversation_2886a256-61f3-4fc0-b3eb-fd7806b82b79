"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "PlatformCard",
  props: {
    platform: {
      type: Object,
      required: true
    }
  },
  methods: {
    navigateToPlatform() {
      common_vendor.index.navigateTo({
        url: `/subPackages/cashback/pages/platform-detail/index?id=${this.platform.id}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.platform.logo,
    b: common_vendor.t($props.platform.name),
    c: common_vendor.t($props.platform.maxCashbackRate),
    d: common_vendor.o((...args) => $options.navigateToPlatform && $options.navigateToPlatform(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-600d0744"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/cashback/components/PlatformCard.js.map
