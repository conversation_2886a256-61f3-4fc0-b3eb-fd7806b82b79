{"version": 3, "file": "points.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/points.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJccG9pbnRzLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"points-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">会员积分</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 积分概览 -->\r\n    <view class=\"overview-section\">\r\n      <view class=\"overview-header\">\r\n        <text class=\"section-title\">积分概览</text>\r\n        <view class=\"date-picker\" @click=\"showDatePicker\">\r\n          <text class=\"date-text\">{{dateRange}}</text>\r\n          <view class=\"date-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"stats-cards\">\r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{pointsData.totalPoints}}</view>\r\n          <view class=\"card-label\">总积分发放</view>\r\n          <view class=\"card-trend\" :class=\"pointsData.pointsTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{pointsData.pointsGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{pointsData.usedPoints}}</view>\r\n          <view class=\"card-label\">已使用积分</view>\r\n          <view class=\"card-trend\" :class=\"pointsData.usedTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{pointsData.usedGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{pointsData.activeUsers}}</view>\r\n          <view class=\"card-label\">积分活跃用户</view>\r\n          <view class=\"card-trend\" :class=\"pointsData.usersTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{pointsData.usersGrowth}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"stats-card\">\r\n          <view class=\"card-value\">{{pointsData.conversionRate}}%</view>\r\n          <view class=\"card-label\">积分兑换率</view>\r\n          <view class=\"card-trend\" :class=\"pointsData.conversionTrend\">\r\n            <view class=\"trend-arrow\"></view>\r\n            <text class=\"trend-value\">{{pointsData.conversionGrowth}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 积分规则 -->\r\n    <view class=\"rules-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">积分规则</text>\r\n        <view class=\"edit-btn\" @click=\"navigateToRules\">\r\n          <text class=\"btn-text\">编辑规则</text>\r\n          <view class=\"edit-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"rules-list\">\r\n        <view class=\"rule-item\" v-for=\"(rule, index) in pointsRules\" :key=\"index\">\r\n          <view class=\"rule-icon\" :class=\"rule.type\">\r\n            <svg class=\"svg-icon\" viewBox=\"0 0 24 24\" :style=\"{ fill: rule.iconColor }\">\r\n              <path :d=\"rule.iconPath\"></path>\r\n            </svg>\r\n          </view>\r\n          <view class=\"rule-content\">\r\n            <view class=\"rule-name\">{{rule.name}}</view>\r\n            <view class=\"rule-desc\">{{rule.description}}</view>\r\n          </view>\r\n          <view class=\"rule-value\">\r\n            <text class=\"value-text\">{{rule.value}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 积分商品 -->\r\n    <view class=\"products-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">积分商品</text>\r\n        <view class=\"add-btn\" @click=\"navigateToPointsMall\">\r\n          <text class=\"btn-text\">积分商城</text>\r\n          <view class=\"arrow-icon\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <scroll-view scroll-x class=\"products-scroll\" show-scrollbar=\"false\">\r\n        <view class=\"products-container\">\r\n          <view class=\"product-card\" v-for=\"(product, index) in pointsProducts\" :key=\"index\" @click=\"viewProduct(product)\">\r\n            <image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\"></image>\r\n            <view class=\"product-info\">\r\n              <text class=\"product-name\">{{product.name}}</text>\r\n              <view class=\"product-points\">\r\n                <text class=\"points-value\">{{product.points}}</text>\r\n                <text class=\"points-label\">积分</text>\r\n              </view>\r\n              <view class=\"product-status\" :class=\"product.status === '热门' ? 'hot' : product.status === '新品' ? 'new' : ''\">\r\n                {{product.status}}\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dateRange: '2023-04-01 ~ 2023-04-30',\r\n      \r\n      // 积分数据概览\r\n      pointsData: {\r\n        totalPoints: 256840,\r\n        pointsTrend: 'up',\r\n        pointsGrowth: '12.5%',\r\n        \r\n        usedPoints: 98560,\r\n        usedTrend: 'up',\r\n        usedGrowth: '8.3%',\r\n        \r\n        activeUsers: 1256,\r\n        usersTrend: 'up',\r\n        usersGrowth: '5.7%',\r\n        \r\n        conversionRate: 38.4,\r\n        conversionTrend: 'up',\r\n        conversionGrowth: '2.1%'\r\n      },\r\n      \r\n      // 积分规则\r\n      pointsRules: [\r\n        {\r\n          name: '消费积分',\r\n          description: '会员购物可获得积分奖励',\r\n          type: 'purchase',\r\n          iconPath: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z',\r\n          iconColor: '#FF9500',\r\n          value: '10积分/元'\r\n        },\r\n        {\r\n          name: '每日签到',\r\n          description: '会员每日签到获得积分',\r\n          type: 'checkin',\r\n          iconPath: 'M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z',\r\n          iconColor: '#34C759',\r\n          value: '5积分/次'\r\n        },\r\n        {\r\n          name: '商品评价',\r\n          description: '购买商品后评价获得积分',\r\n          type: 'review',\r\n          iconPath: 'M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z',\r\n          iconColor: '#007AFF',\r\n          value: '20积分/次'\r\n        },\r\n        {\r\n          name: '分享商品',\r\n          description: '分享商品给好友获得积分',\r\n          type: 'share',\r\n          iconPath: 'M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z',\r\n          iconColor: '#FF2D55',\r\n          value: '15积分/次'\r\n        }\r\n      ],\r\n      \r\n      // 积分商品\r\n      pointsProducts: [\r\n        {\r\n          id: 1,\r\n          name: '精美保温杯',\r\n          image: '/static/images/points-product-1.jpg',\r\n          points: 1200,\r\n          status: '热门'\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '无线充电器',\r\n          image: '/static/images/points-product-2.jpg',\r\n          points: 2000,\r\n          status: '新品'\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '精致小台灯',\r\n          image: '/static/images/points-product-3.jpg',\r\n          points: 1500,\r\n          status: '热门'\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '优质雨伞',\r\n          image: '/static/images/points-product-4.jpg',\r\n          points: 800,\r\n          status: ''\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    showHelp() {\r\n      uni.showModal({\r\n        title: '会员积分帮助',\r\n        content: '会员积分是会员在消费、签到等行为后获得的奖励，可用于兑换商品或服务。',\r\n        showCancel: false\r\n      });\r\n    },\r\n    \r\n    showDatePicker() {\r\n      // 实现日期选择器\r\n      uni.showToast({\r\n        title: '日期选择功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    navigateToRules() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/points/rules'\r\n      });\r\n    },\r\n    \r\n    navigateToPointsMall() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/points/management'\r\n      });\r\n    },\r\n    \r\n    viewProduct(product) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/merchant-admin-marketing/pages/marketing/points/detail?id=${product.id}`\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.points-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #FF9500, #FF5E3A);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(255, 149, 0, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 通用部分样式 */\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.edit-btn, .add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #FF9500;\r\n  border-radius: 15px;\r\n  padding: 5px 12px;\r\n  color: white;\r\n}\r\n\r\n.btn-text {\r\n  font-size: 13px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.edit-icon {\r\n  width: 14px;\r\n  height: 14px;\r\n  position: relative;\r\n}\r\n\r\n.edit-icon:before {\r\n  content: '';\r\n  position: absolute;\r\n  width: 10px;\r\n  height: 10px;\r\n  border: 1.5px solid white;\r\n  border-radius: 1px;\r\n  transform: rotate(45deg);\r\n  top: 1px;\r\n  left: 1px;\r\n}\r\n\r\n.arrow-icon {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #fff;\r\n  border-right: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n/* 概览部分样式 */\r\n.overview-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.overview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 15px;\r\n  padding: 5px 10px;\r\n}\r\n\r\n.date-text {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.date-icon {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #666;\r\n  border-right: 2px solid #666;\r\n  transform: rotate(135deg);\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -7.5px;\r\n}\r\n\r\n.stats-card {\r\n  width: 50%;\r\n  padding: 7.5px;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n}\r\n\r\n.card-value {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 5px;\r\n  background: #F8FAFC;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  border-left: 3px solid #FF9500;\r\n}\r\n\r\n.card-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 25px;\r\n}\r\n\r\n.card-trend {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  right: 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.card-trend.up {\r\n  color: #34C759;\r\n}\r\n\r\n.card-trend.down {\r\n  color: #FF3B30;\r\n}\r\n\r\n.trend-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  margin-right: 3px;\r\n}\r\n\r\n.card-trend.up .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-bottom: 6px solid #34C759;\r\n}\r\n\r\n.card-trend.down .trend-arrow {\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n  border-top: 6px solid #FF3B30;\r\n}\r\n\r\n/* 积分规则样式 */\r\n.rules-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.rules-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.rule-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.rule-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.rule-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 15px;\r\n}\r\n\r\n.svg-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.rule-icon.purchase {\r\n  background: rgba(255, 149, 0, 0.1);\r\n}\r\n\r\n.rule-icon.checkin {\r\n  background: rgba(52, 199, 89, 0.1);\r\n}\r\n\r\n.rule-icon.review {\r\n  background: rgba(0, 122, 255, 0.1);\r\n}\r\n\r\n.rule-icon.share {\r\n  background: rgba(255, 45, 85, 0.1);\r\n}\r\n\r\n.rule-content {\r\n  flex: 1;\r\n}\r\n\r\n.rule-name {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.rule-desc {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.rule-value {\r\n  margin-left: 10px;\r\n}\r\n\r\n.value-text {\r\n  font-size: 14px;\r\n  color: #FF9500;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 积分商品样式 */\r\n.products-section {\r\n  margin: 15px;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\r\n  padding: 15px;\r\n}\r\n\r\n.products-scroll {\r\n  white-space: nowrap;\r\n  margin: 0 -15px;\r\n  padding: 0 15px;\r\n}\r\n\r\n.products-container {\r\n  display: inline-flex;\r\n  padding: 5px 0;\r\n}\r\n\r\n.product-card {\r\n  width: 140px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  margin-right: 15px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  background: white;\r\n}\r\n\r\n.product-image {\r\n  width: 140px;\r\n  height: 140px;\r\n  background: #f5f5f5;\r\n}\r\n\r\n.product-info {\r\n  padding: 10px;\r\n  position: relative;\r\n}\r\n\r\n.product-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n  white-space: normal;\r\n  height: 40px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.product-points {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.points-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #FF9500;\r\n}\r\n\r\n.points-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-left: 3px;\r\n}\r\n\r\n.product-status {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 10px;\r\n  padding: 2px 6px;\r\n  border-radius: 10px;\r\n  font-size: 10px;\r\n  color: white;\r\n}\r\n\r\n.product-status.hot {\r\n  background: #FF3B30;\r\n}\r\n\r\n.product-status.new {\r\n  background: #34C759;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media screen and (max-width: 375px) {\r\n  .stats-card {\r\n    width: 100%;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/points.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA0HA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA;AAAA,MAGX,YAAY;AAAA,QACV,aAAa;AAAA,QACb,aAAa;AAAA,QACb,cAAc;AAAA,QAEd,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,YAAY;AAAA,QAEZ,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QAEb,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MACnB;AAAA;AAAA,MAGD,aAAa;AAAA,QACX;AAAA,UACE,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,QACR;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,MACD;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACd;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,uBAAuB;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,YAAY,SAAS;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0EAA0E,QAAQ,EAAE;AAAA,MAC3F,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7PA,GAAG,WAAW,eAAe;"}