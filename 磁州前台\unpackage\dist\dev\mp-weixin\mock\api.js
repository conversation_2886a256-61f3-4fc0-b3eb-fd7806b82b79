"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const mock_news_index = require("./news/index.js");
const mock_info_categories = require("./info/categories.js");
const mock_info_toppedInfo = require("./info/toppedInfo.js");
const mock_info_normalInfo = require("./info/normalInfo.js");
const mock_common_adBanner = require("./common/adBanner.js");
const mock_business_categories = require("./business/categories.js");
const mock_business_shopList = require("./business/shopList.js");
const mock_publish_businessTransfer = require("./publish/businessTransfer.js");
const mock_publish_houseRent = require("./publish/houseRent.js");
const mock_publish_jobInfo = require("./publish/jobInfo.js");
const mock_publish_houseSale = require("./publish/houseSale.js");
const mock_publish_carInfo = require("./publish/carInfo.js");
const mock_publish_secondHand = require("./publish/secondHand.js");
const mock_user_profile = require("./user/profile.js");
const mock_user_settings = require("./user/settings.js");
const mock_user_auth = require("./user/auth.js");
const mock_activity_activityList = require("./activity/activityList.js");
const mock_activity_activityDetail = require("./activity/activityDetail.js");
const mock_payment_wallet = require("./payment/wallet.js");
const mock_payment_transaction = require("./payment/transaction.js");
const mock_service_index = require("./service/index.js");
const mock_service_serviceList = require("./service/serviceList.js");
const mock_service_publishList = require("./service/publishList.js");
const mock_news_newsList = require("./news/newsList.js");
const mock_news_newsDetail = require("./news/newsDetail.js");
const mock_service_serviceDetail = require("./service/serviceDetail.js");
const extendedServiceApi = {
  ...mock_service_index.serviceApi,
  fetchPublishList: mock_service_publishList.fetchPublishList,
  fetchPublishDetail: mock_service_publishList.fetchPublishDetail
};
const mockApi = {
  // 新闻相关API
  news: {
    getCategories: () => mock_news_index.newsCategories,
    getList: mock_news_newsList.fetchNewsList,
    getDetail: mock_news_newsDetail.fetchNewsDetail,
    getMoreComments: mock_news_newsDetail.fetchMoreComments
  },
  // 信息相关API
  info: {
    getCategories: () => mock_info_categories.infoCategories,
    getVisibleCategories: () => mock_info_categories.visibleCategories,
    getTopped: mock_info_toppedInfo.fetchToppedInfo,
    getAll: mock_info_normalInfo.fetchAllInfo,
    getByCategory: mock_info_normalInfo.fetchInfoByCategory
  },
  // 商圈相关API
  business: {
    getCategories: mock_business_categories.fetchBusinessCategories,
    getShopList: mock_business_shopList.fetchShopList,
    getShopDetail: mock_business_shopList.fetchShopDetail
  },
  // 发布相关API
  publish: {
    // 生意转让
    getBusinessTransferDetail: mock_publish_businessTransfer.fetchBusinessTransferDetail,
    getRelatedShops: mock_publish_businessTransfer.fetchRelatedShops,
    // 房屋出租
    getHouseRentDetail: mock_publish_houseRent.fetchHouseRentDetail,
    getRelatedHouses: mock_publish_houseRent.fetchRelatedHouses,
    // 招聘信息
    getJobDetail: mock_publish_jobInfo.fetchJobDetail,
    getRelatedJobs: mock_publish_jobInfo.fetchRelatedJobs,
    // 房屋出售
    getHouseSaleDetail: mock_publish_houseSale.fetchHouseSaleDetail,
    getRelatedHouseSales: mock_publish_houseSale.fetchRelatedHouseSales,
    // 二手车辆
    getCarDetail: mock_publish_carInfo.fetchCarDetail,
    getRelatedCars: mock_publish_carInfo.fetchRelatedCars,
    // 二手闲置
    getSecondHandDetail: mock_publish_secondHand.fetchSecondHandDetail,
    getRelatedSecondHands: mock_publish_secondHand.fetchRelatedSecondHands
  },
  // 用户相关API
  user: {
    // 用户资料相关
    getProfile: mock_user_profile.fetchUserProfile,
    getStats: mock_user_profile.fetchUserStats,
    updateProfile: mock_user_profile.updateUserProfile,
    // 用户设置相关
    getSettings: mock_user_settings.fetchUserSettings,
    updateSettings: mock_user_settings.updateUserSettings,
    // 用户认证相关
    login: mock_user_auth.login,
    register: mock_user_auth.register,
    getVerificationCode: mock_user_auth.getVerificationCode,
    logout: mock_user_auth.logout,
    refreshToken: mock_user_auth.refreshAuthToken
  },
  // 活动相关API
  activity: {
    // 活动列表相关
    getCategories: mock_activity_activityList.fetchActivityCategories,
    getList: mock_activity_activityList.fetchActivityList,
    // 活动详情相关
    getDetail: mock_activity_activityDetail.fetchActivityDetail,
    getRelated: mock_activity_activityDetail.fetchRelatedActivities,
    getMoreComments: mock_activity_activityDetail.fetchMoreComments,
    // 活动参与相关
    participate: mock_activity_activityDetail.participateActivity,
    cancelParticipation: mock_activity_activityDetail.cancelParticipation
  },
  // 支付相关API
  payment: {
    // 钱包相关
    getWalletInfo: mock_payment_wallet.fetchWalletInfo,
    recharge: mock_payment_wallet.rechargeWallet,
    withdraw: mock_payment_wallet.withdrawWallet,
    addBankCard: mock_payment_wallet.addBankCard,
    setDefaultBankCard: mock_payment_wallet.setDefaultBankCard,
    // 交易记录相关
    getTransactionList: mock_payment_transaction.fetchTransactionList,
    getTransactionDetail: mock_payment_transaction.fetchTransactionDetail,
    getTransactionStats: mock_payment_transaction.fetchTransactionStats
  },
  // 服务相关API
  service: {
    // 服务列表相关
    getCategories: mock_service_serviceList.fetchServiceCategories,
    getList: mock_service_serviceList.fetchServiceList,
    getHotServices: mock_service_serviceList.fetchHotServices,
    // 服务详情相关
    getDetail: mock_service_serviceDetail.fetchServiceDetail,
    getRelated: mock_service_serviceDetail.fetchRelatedServices,
    getMoreReviews: mock_service_serviceDetail.fetchMoreReviews,
    // 服务收藏相关
    collect: mock_service_serviceDetail.collectService,
    cancelCollect: mock_service_serviceDetail.cancelCollectService,
    // 发布列表相关API
    fetchPublishList: mock_service_publishList.fetchPublishList,
    fetchPublishDetail: mock_service_publishList.fetchPublishDetail
  },
  // 广告相关API
  ad: {
    getBanner: mock_common_adBanner.fetchAdBanner
  }
};
exports.default = mockApi;
exports.serviceApi = extendedServiceApi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/mock/api.js.map
