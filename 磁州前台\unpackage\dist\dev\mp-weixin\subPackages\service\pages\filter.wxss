/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-bd48d0a8, html.data-v-bd48d0a8, #app.data-v-bd48d0a8, .index-container.data-v-bd48d0a8 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 全局容器样式 */
.service-filter-container.data-v-bd48d0a8 {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f6f8fa;
}
.service-filter-container.premium-style.data-v-bd48d0a8 {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 高级自定义导航栏 */
.custom-navbar.data-v-bd48d0a8 {
  display: flex;
  align-items: center;
  height: 44px;
  background: linear-gradient(120deg, #0070f3, #00a1ff);
  padding: 0 16px;
  position: relative;
  z-index: 1000;
  box-shadow: 0 1px 12px rgba(0, 112, 243, 0.18);
}
.back-btn.data-v-bd48d0a8 {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 2;
}
.back-icon.data-v-bd48d0a8 {
  width: 12px;
  height: 12px;
  border-top: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
  transition: transform 0.2s ease;
}
.back-btn:active .back-icon.data-v-bd48d0a8 {
  transform: rotate(-45deg) scale(0.9);
}
.navbar-title.data-v-bd48d0a8 {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: -0.2px;
}
.navbar-right.data-v-bd48d0a8 {
  width: 36px;
  position: relative;
  z-index: 2;
}

/* 高级搜索框 */
.search-container.data-v-bd48d0a8 {
  padding: 12px 16px 16px;
  background: #fff;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  margin-bottom: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.search-box.data-v-bd48d0a8 {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 12px;
  padding: 0 12px;
  height: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  overflow: hidden;
}
.search-icon.data-v-bd48d0a8 {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  opacity: 0.6;
}
.search-input.data-v-bd48d0a8 {
  flex: 1;
  height: 40px;
  font-size: 15px;
  color: #333;
  background: transparent;
}
.search-btn.data-v-bd48d0a8 {
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  border-radius: 10px;
  margin-right: -12px;
}

/* 高级一级分类标签栏 */
.top-category-tabs.data-v-bd48d0a8 {
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
  padding: 14px 0 12px;
  white-space: nowrap;
  border-radius: 16px;
  margin: 8px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}
.top-category-item.data-v-bd48d0a8 {
  display: inline-block;
  padding: 8px 20px;
  margin: 0 5px;
  font-size: 15px;
  color: #555;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}
.top-category-item.data-v-bd48d0a8:first-child {
  margin-left: 16px;
}
.top-category-item.data-v-bd48d0a8:last-child {
  margin-right: 16px;
}
.active-top-category.data-v-bd48d0a8 {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}

/* 高级子分类标签栏 */
.subcategory-tabs.data-v-bd48d0a8 {
  background: rgba(255, 255, 255, 0.95);
  padding: 10px 0;
  white-space: nowrap;
  border-radius: 16px;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}
.subcategory-item.data-v-bd48d0a8 {
  display: inline-block;
  padding: 6px 16px;
  margin: 0 5px;
  font-size: 14px;
  color: #666;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  background-color: #f1f2f6;
}
.subcategory-item.data-v-bd48d0a8:first-child {
  margin-left: 16px;
}
.subcategory-item.data-v-bd48d0a8:last-child {
  margin-right: 16px;
}
.active-subcategory.data-v-bd48d0a8 {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);
}

/* 高级筛选条件栏 */
.filter-section.data-v-bd48d0a8 {
  display: flex;
  height: 54px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  position: relative;
  justify-content: center;
  padding: 0;
  z-index: 95;
  margin: 0 12px 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}
.filter-item.data-v-bd48d0a8 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
  font-size: 15px;
  color: #333;
  font-weight: 500;
  transition: background-color 0.2s ease;
}
.filter-item.data-v-bd48d0a8:active {
  background-color: rgba(0, 0, 0, 0.02);
}
.filter-item.data-v-bd48d0a8:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.05);
}
.filter-text.data-v-bd48d0a8 {
  display: inline-block;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.active-filter.data-v-bd48d0a8 {
  color: #0070f3;
  font-weight: 600;
}
.filter-arrow.data-v-bd48d0a8 {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999;
  margin-left: 8px;
  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}
.arrow-up.data-v-bd48d0a8 {
  transform: rotate(180deg);
  border-top-color: #0070f3;
}

/* 高级已选筛选标签 */
.selected-filters.data-v-bd48d0a8 {
  margin: 0 12px 12px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  padding: 10px 0;
}
.filter-tags-scroll.data-v-bd48d0a8 {
  white-space: nowrap;
}
.filter-tags.data-v-bd48d0a8 {
  display: inline-flex;
  padding: 0 16px;
}
.filter-tag.data-v-bd48d0a8 {
  display: inline-block;
  padding: 6px 12px;
  background: rgba(0, 112, 243, 0.08);
  color: #0070f3;
  font-size: 13px;
  border-radius: 16px;
  margin-right: 8px;
}
.tag-close.data-v-bd48d0a8 {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  border-radius: 50%;
  background-color: rgba(0, 112, 243, 0.15);
  margin-left: 6px;
  font-size: 14px;
}
.reset-all.data-v-bd48d0a8 {
  display: inline-block;
  padding: 6px 12px;
  background: rgba(255, 59, 48, 0.08);
  color: #ff3b30;
  font-size: 13px;
  border-radius: 16px;
}

/* 高级下拉菜单 */
.filter-dropdown.data-v-bd48d0a8 {
  position: absolute;
  left: 0;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 400px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.dropdown-header.data-v-bd48d0a8 {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.dropdown-title.data-v-bd48d0a8 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}
.dropdown-scroll.data-v-bd48d0a8 {
  max-height: 350px;
}
.dropdown-item.data-v-bd48d0a8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s ease;
}
.dropdown-item.data-v-bd48d0a8:active {
  background-color: rgba(0, 0, 0, 0.02);
}
.active-item.data-v-bd48d0a8 {
  color: #0070f3;
  font-weight: 500;
}
.dropdown-item-text.data-v-bd48d0a8 {
  flex: 1;
}
.dropdown-item-check.data-v-bd48d0a8 {
  color: #0070f3;
  font-weight: bold;
}

/* 高级遮罩层 */
.filter-mask.data-v-bd48d0a8 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 90;
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
}

/* 高级内容列表 */
.service-list.data-v-bd48d0a8 {
  flex: 1;
  position: relative;
}

/* 高级服务卡片 */
.service-item.data-v-bd48d0a8 {
  margin: 0 12px 16px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.service-item-hover.data-v-bd48d0a8 {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}
.service-content.data-v-bd48d0a8 {
  padding: 16px;
}
.service-header.data-v-bd48d0a8 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}
.service-tags.data-v-bd48d0a8 {
  display: flex;
  flex-wrap: wrap;
}
.service-tag.data-v-bd48d0a8 {
  background: rgba(0, 112, 243, 0.08);
  color: #0070f3;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 8px;
}
.service-subcategory.data-v-bd48d0a8 {
  background: rgba(255, 149, 0, 0.08);
  color: #ff9500;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}
.service-meta.data-v-bd48d0a8 {
  display: flex;
  align-items: center;
}
.service-area.data-v-bd48d0a8 {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}
.service-time.data-v-bd48d0a8 {
  font-size: 12px;
  color: #999;
}
.service-body.data-v-bd48d0a8 {
  margin-bottom: 12px;
}
.service-title.data-v-bd48d0a8 {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.service-images.data-v-bd48d0a8 {
  display: flex;
  margin-top: 12px;
  position: relative;
}
.service-image.data-v-bd48d0a8 {
  width: 33%;
  height: 180rpx;
  margin-right: 6px;
  border-radius: 8px;
  object-fit: cover;
}
.single-image.data-v-bd48d0a8 {
  width: 66%;
}
.image-count.data-v-bd48d0a8 {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.service-footer.data-v-bd48d0a8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 12px;
}
.service-stats.data-v-bd48d0a8 {
  display: flex;
  align-items: center;
}
.service-views.data-v-bd48d0a8 {
  font-size: 12px;
  color: #999;
  margin-right: 12px;
}
.service-price.data-v-bd48d0a8 {
  font-size: 16px;
  color: #ff3b30;
  font-weight: 500;
}
.service-actions.data-v-bd48d0a8 {
  display: flex;
}
.action-btn.data-v-bd48d0a8 {
  display: flex;
  align-items: center;
  background: rgba(0, 112, 243, 0.08);
  color: #0070f3;
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 16px;
}
.contact-btn.data-v-bd48d0a8 {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
}
.action-icon.data-v-bd48d0a8 {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.action-text.data-v-bd48d0a8 {
  font-size: 13px;
}

/* 高级加载更多 */
.loading-more.data-v-bd48d0a8 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}
.loading-indicator.data-v-bd48d0a8 {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0070f3;
  border-radius: 50%;
  animation: spin-bd48d0a8 0.8s linear infinite;
  margin-right: 8px;
}
@keyframes spin-bd48d0a8 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text.data-v-bd48d0a8 {
  font-size: 14px;
  color: #999;
}
.loading-done.data-v-bd48d0a8 {
  text-align: center;
  padding: 20px 0;
}
.loading-done-text.data-v-bd48d0a8 {
  font-size: 14px;
  color: #999;
}

/* 高级空状态 */
.empty-state.data-v-bd48d0a8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}
.empty-image.data-v-bd48d0a8 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20px;
}
.empty-text.data-v-bd48d0a8 {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}
.empty-tips.data-v-bd48d0a8 {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}
.empty-btn.data-v-bd48d0a8 {
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  color: #fff;
  font-size: 14px;
  padding: 8px 24px;
  border-radius: 20px;
}

/* 高级统计提示 */
.result-stats.data-v-bd48d0a8 {
  padding: 12px 16px;
  font-size: 14px;
  color: #666;
}
.stats-number.data-v-bd48d0a8 {
  color: #0070f3;
  font-weight: 500;
}
.service-items-wrapper.data-v-bd48d0a8 {
  padding-bottom: 80px;
}

/* 高级发布按钮 */
.publish-btn.data-v-bd48d0a8 {
  position: fixed;
  right: 20px;
  bottom: 30px;
  background: linear-gradient(135deg, #0070f3, #00a1ff);
  width: 120px;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.3);
  z-index: 99;
}
.publish-btn-hover.data-v-bd48d0a8 {
  opacity: 0.9;
  transform: scale(0.98);
}
.publish-icon.data-v-bd48d0a8 {
  font-size: 20px;
  margin-right: 4px;
  font-weight: 300;
}
.publish-text.data-v-bd48d0a8 {
  font-size: 15px;
}