<template>
  <view class="uni-tabbar">
    <view 
      v-for="(item, index) in list" 
      :key="index" 
      class="uni-tabbar__item" 
      :class="{ 'uni-tabbar__item--active': selected === index }"
      @click="switchTab(index)"
    >
      <view class="uni-tabbar__icon">
        <image 
          :src="selected === index ? item.selectedIconPath : item.iconPath"
          class="uni-tabbar__icon-image"
        ></image>
      </view>
      <view class="uni-tabbar__text" :class="{ 'uni-tabbar__text--active': selected === index }">
        {{ item.text }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UniTabbar',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    selectedIndex: {
      type: Number,
      default: 0
    },
    color: {
      type: String,
      default: '#999999'
    },
    selectedColor: {
      type: String,
      default: '#1677FF'
    }
  },
  data() {
    return {
      selected: this.selectedIndex
    };
  },
  watch: {
    selectedIndex(val) {
      this.selected = val;
    }
  },
  methods: {
    switchTab(index) {
      if (this.selected === index) return;
      
      this.selected = index;
      this.$emit('tabClick', {
        index,
        item: this.list[index]
      });
    }
  }
};
</script>

<style>
.uni-tabbar {
  display: flex;
  height: 50px;
  background-color: #FFFFFF;
  border-top: 1px solid #F5F5F5;
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  padding-bottom: env(safe-area-inset-bottom);
}

.uni-tabbar__item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.uni-tabbar__icon {
  height: 24px;
  line-height: 1;
  margin-bottom: 2px;
}

.uni-tabbar__icon-image {
  width: 24px;
  height: 24px;
}

.uni-tabbar__text {
  font-size: 10px;
  color: #999999;
  text-align: center;
}

.uni-tabbar__text--active {
  color: #1677FF;
}
</style>
