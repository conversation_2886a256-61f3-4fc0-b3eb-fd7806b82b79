{"version": 3, "file": "date.js", "sources": ["utils/date.js"], "sourcesContent": ["/**\r\n * 日期工具函数\r\n */\r\n\r\n/**\r\n * 获取当前时间戳\r\n * @returns {number} 当前时间戳（毫秒）\r\n */\r\nexport function getCurrentTimestamp() {\r\n  return Date.now();\r\n}\r\n\r\n/**\r\n * 获取当前日期对象\r\n * @returns {Date} 当前日期对象\r\n */\r\nexport function getCurrentDate() {\r\n  return new Date();\r\n}\r\n\r\n/**\r\n * 判断日期是否过期\r\n * @param {string|number|Date} date 日期\r\n * @returns {boolean} 是否过期\r\n */\r\nexport function isExpired(date) {\r\n  if (!date) return true;\r\n  return new Date(date).getTime() < Date.now();\r\n}\r\n\r\n/**\r\n * 获取两个日期之间的天数差\r\n * @param {string|number|Date} date1 日期1\r\n * @param {string|number|Date} date2 日期2\r\n * @returns {number} 天数差\r\n */\r\nexport function getDaysDiff(date1, date2) {\r\n  const d1 = new Date(date1).getTime();\r\n  const d2 = new Date(date2).getTime();\r\n  return Math.floor(Math.abs(d1 - d2) / (1000 * 60 * 60 * 24));\r\n}\r\n\r\n/**\r\n * 获取指定日期的开始时间\r\n * @param {string|number|Date} date 日期\r\n * @returns {Date} 开始时间\r\n */\r\nexport function getStartOfDay(date) {\r\n  const d = new Date(date);\r\n  d.setHours(0, 0, 0, 0);\r\n  return d;\r\n}\r\n\r\n/**\r\n * 获取指定日期的结束时间\r\n * @param {string|number|Date} date 日期\r\n * @returns {Date} 结束时间\r\n */\r\nexport function getEndOfDay(date) {\r\n  const d = new Date(date);\r\n  d.setHours(23, 59, 59, 999);\r\n  return d;\r\n}\r\n\r\n/**\r\n * 获取指定日期所在月份的第一天\r\n * @param {string|number|Date} date 日期\r\n * @returns {Date} 月份第一天\r\n */\r\nexport function getFirstDayOfMonth(date) {\r\n  const d = new Date(date);\r\n  d.setDate(1);\r\n  d.setHours(0, 0, 0, 0);\r\n  return d;\r\n}\r\n\r\n/**\r\n * 获取指定日期所在月份的最后一天\r\n * @param {string|number|Date} date 日期\r\n * @returns {Date} 月份最后一天\r\n */\r\nexport function getLastDayOfMonth(date) {\r\n  const d = new Date(date);\r\n  d.setMonth(d.getMonth() + 1);\r\n  d.setDate(0);\r\n  d.setHours(23, 59, 59, 999);\r\n  return d;\r\n}\r\n\r\n/**\r\n * 判断是否是同一天\r\n * @param {string|number|Date} date1 日期1\r\n * @param {string|number|Date} date2 日期2\r\n * @returns {boolean} 是否是同一天\r\n */\r\nexport function isSameDay(date1, date2) {\r\n  const d1 = new Date(date1);\r\n  const d2 = new Date(date2);\r\n  return d1.getFullYear() === d2.getFullYear() &&\r\n    d1.getMonth() === d2.getMonth() &&\r\n    d1.getDate() === d2.getDate();\r\n}\r\n\r\n/**\r\n * 获取相对时间描述\r\n * @param {string|number|Date} date 日期\r\n * @returns {string} 相对时间描述\r\n */\r\nexport function getRelativeTime(date) {\r\n  const now = Date.now();\r\n  const diff = now - new Date(date).getTime();\r\n  \r\n  // 小于1分钟\r\n  if (diff < 60 * 1000) {\r\n    return '刚刚';\r\n  }\r\n  // 小于1小时\r\n  if (diff < 60 * 60 * 1000) {\r\n    return Math.floor(diff / (60 * 1000)) + '分钟前';\r\n  }\r\n  // 小于24小时\r\n  if (diff < 24 * 60 * 60 * 1000) {\r\n    return Math.floor(diff / (60 * 60 * 1000)) + '小时前';\r\n  }\r\n  // 小于30天\r\n  if (diff < 30 * 24 * 60 * 60 * 1000) {\r\n    return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';\r\n  }\r\n  // 小于12个月\r\n  if (diff < 12 * 30 * 24 * 60 * 60 * 1000) {\r\n    return Math.floor(diff / (30 * 24 * 60 * 60 * 1000)) + '个月前';\r\n  }\r\n  // 大于等于12个月\r\n  return Math.floor(diff / (12 * 30 * 24 * 60 * 60 * 1000)) + '年前';\r\n}\r\n\r\n/**\r\n * 格式化日期\r\n * @param {string|number|Date} date 日期\r\n * @param {string} format 格式化模式，默认为 'YYYY-MM-DD'\r\n * @returns {string} 格式化后的日期字符串\r\n */\r\nexport function formatDate(date, format = 'YYYY-MM-DD') {\r\n  if (!date) return '';\r\n  \r\n  const d = new Date(date);\r\n  if (isNaN(d.getTime())) return '';\r\n  \r\n  const year = d.getFullYear();\r\n  const month = d.getMonth() + 1;\r\n  const day = d.getDate();\r\n  const hour = d.getHours();\r\n  const minute = d.getMinutes();\r\n  const second = d.getSeconds();\r\n  \r\n  return format\r\n    .replace('YYYY', year)\r\n    .replace('MM', month.toString().padStart(2, '0'))\r\n    .replace('DD', day.toString().padStart(2, '0'))\r\n    .replace('HH', hour.toString().padStart(2, '0'))\r\n    .replace('mm', minute.toString().padStart(2, '0'))\r\n    .replace('ss', second.toString().padStart(2, '0'));\r\n}\r\n\r\n/**\r\n * 格式化时间\r\n * @param {string|number|Date} date 日期\r\n * @returns {string} 格式化后的时间字符串\r\n */\r\nexport function formatTime(date) {\r\n  if (!date) return '';\r\n  \r\n  const d = new Date(date);\r\n  if (isNaN(d.getTime())) return '';\r\n  \r\n  return formatDate(d, 'YYYY-MM-DD HH:mm');\r\n} "], "names": [], "mappings": ";AA8IO,SAAS,WAAW,MAAM,SAAS,cAAc;AACtD,MAAI,CAAC;AAAM,WAAO;AAElB,QAAM,IAAI,IAAI,KAAK,IAAI;AACvB,MAAI,MAAM,EAAE,QAAO,CAAE;AAAG,WAAO;AAE/B,QAAM,OAAO,EAAE;AACf,QAAM,QAAQ,EAAE,SAAQ,IAAK;AAC7B,QAAM,MAAM,EAAE;AACd,QAAM,OAAO,EAAE;AACf,QAAM,SAAS,EAAE;AACjB,QAAM,SAAS,EAAE;AAEjB,SAAO,OACJ,QAAQ,QAAQ,IAAI,EACpB,QAAQ,MAAM,MAAM,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,EAC/C,QAAQ,MAAM,IAAI,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,EAC7C,QAAQ,MAAM,KAAK,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,EAC9C,QAAQ,MAAM,OAAO,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,EAChD,QAAQ,MAAM,OAAO,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC;AACrD;AAOO,SAAS,WAAW,MAAM;AAC/B,MAAI,CAAC;AAAM,WAAO;AAElB,QAAM,IAAI,IAAI,KAAK,IAAI;AACvB,MAAI,MAAM,EAAE,QAAO,CAAE;AAAG,WAAO;AAE/B,SAAO,WAAW,GAAG,kBAAkB;AACzC;;;"}