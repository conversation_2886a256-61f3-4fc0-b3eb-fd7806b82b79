{"version": 3, "file": "partner-poster.js", "sources": ["subPackages/partner/pages/partner-poster.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGFydG5lclxwYWdlc1xwYXJ0bmVyLXBvc3Rlci52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"poster-container\">\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<view class=\"custom-navbar\">\r\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\r\n\t\t\t\t<image src=\"/static/images/tabbar/最新返回键.png\" class=\"back-icon\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"navbar-title\">推广海报</view>\r\n\t\t\t<view class=\"navbar-right\">\r\n\t\t\t\t<!-- 预留位置与发布页面保持一致 -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 添加顶部安全区域 -->\r\n\t\t<view class=\"safe-area-top\"></view>\r\n\t\t\r\n\t\t<!-- 海报预览区域 -->\r\n\t\t<view class=\"poster-preview\">\r\n\t\t\t<view class=\"preview-header\">\r\n\t\t\t\t<text class=\"preview-title\">我的推广海报</text>\r\n\t\t\t\t<view class=\"template-switch\" @click=\"showTemplateModal\">\r\n\t\t\t\t\t<image class=\"switch-icon\" src=\"/static/images/tabbar/template.png\"></image>\r\n\t\t\t\t\t<text class=\"switch-text\">切换模板</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"poster-card\">\r\n\t\t\t\t<image class=\"poster-image\" :src=\"currentPoster\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"poster-loading\" v-if=\"isGenerating\">\r\n\t\t\t\t\t<view class=\"loading-spinner\"></view>\r\n\t\t\t\t\t<text class=\"loading-text\">海报生成中...</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 美化后的按钮组 -->\r\n\t\t\t<view class=\"new-poster-actions\">\r\n\t\t\t\t<button class=\"action-button refresh-button\" @click=\"refreshPoster\">\r\n\t\t\t\t\t<view class=\"button-icon-wrap\">\r\n\t\t\t\t\t\t<image class=\"button-icon\" src=\"/static/images/tabbar/refresh.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"button-text\">重新生成</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t\r\n\t\t\t\t<button class=\"action-button save-button\" @click=\"savePoster\">\r\n\t\t\t\t\t<view class=\"button-icon-wrap save-icon-bg\">\r\n\t\t\t\t\t\t<image class=\"button-icon\" src=\"/static/images/tabbar/download.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"button-text\">保存到相册</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t\r\n\t\t\t\t<button class=\"action-button share-button\" @click=\"sharePoster\">\r\n\t\t\t\t\t<view class=\"button-icon-wrap share-icon-bg\">\r\n\t\t\t\t\t\t<image class=\"button-icon\" src=\"/static/images/tabbar/share.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"button-text\">立即分享</text>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 推广数据卡片 -->\r\n\t\t<view class=\"stats-card\">\r\n\t\t\t<view class=\"stats-header\">\r\n\t\t\t\t<text class=\"stats-title\">今日推广数据</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-content\">\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-value\">{{ promotionData.viewCount }}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">浏览量</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-divider\"></view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-value\">{{ promotionData.registerCount }}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">新增用户</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-divider\"></view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-value\">{{ promotionData.orderCount }}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">订单数</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-divider\"></view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-value\">{{ promotionData.income }}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">收益</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 佣金规则卡片 -->\r\n\t\t<view class=\"commission-card\">\r\n\t\t\t<view class=\"commission-header\">\r\n\t\t\t\t<text class=\"commission-title\">合伙人等级</text>\r\n\t\t\t\t<view class=\"upgrade-btn\" @click=\"goToUpgrade\">\r\n\t\t\t\t\t<text class=\"upgrade-text\">立即升级</text>\r\n\t\t\t\t\t<image class=\"upgrade-icon\" src=\"/static/images/tabbar/右箭头.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"commission-content\">\r\n\t\t\t\t<view class=\"commission-item\">\r\n\t\t\t\t\t<view class=\"commission-info\">\r\n\t\t\t\t\t\t<view class=\"commission-label\">当前等级</view>\r\n\t\t\t\t\t\t<view class=\"commission-desc\">{{ getLevelName(partnerInfo.level) }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"commission-value level-tag\">{{ partnerInfo.level }}级</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"level-benefits\">\r\n\t\t\t\t\t<view class=\"benefit-item\">\r\n\t\t\t\t\t\t<view class=\"benefit-icon-wrap\">\r\n\t\t\t\t\t\t\t<image class=\"benefit-icon\" src=\"/static/images/tabbar/收益明细.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"benefit-text\">一级佣金 {{ getCommissionRate(1) }}%</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"benefit-item\">\r\n\t\t\t\t\t\t<view class=\"benefit-icon-wrap\">\r\n\t\t\t\t\t\t\t<image class=\"benefit-icon\" src=\"/static/images/tabbar/我的粉丝.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"benefit-text\">二级佣金 {{ getCommissionRate(2) }}%</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"upgrade-tips\">\r\n\t\t\t\t\t<text class=\"tips-text\">升级更高等级可获得更多佣金比例和特权</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 推广步骤 -->\r\n\t\t<view class=\"steps-card\">\r\n\t\t\t<view class=\"steps-header\">\r\n\t\t\t\t<text class=\"steps-title\">推广步骤</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"steps-content\">\r\n\t\t\t\t<view class=\"step-item\">\r\n\t\t\t\t\t<view class=\"step-icon-wrap step1-bg\">\r\n\t\t\t\t\t\t<image class=\"step-icon\" src=\"/static/images/tabbar/save.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"step-info\">\r\n\t\t\t\t\t\t<view class=\"step-name\">保存海报</view>\r\n\t\t\t\t\t\t<view class=\"step-desc\">保存推广海报到手机相册</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"step-connector\"></view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"step-item\">\r\n\t\t\t\t\t<view class=\"step-icon-wrap step2-bg\">\r\n\t\t\t\t\t\t<image class=\"step-icon\" src=\"/static/images/tabbar/share.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"step-info\">\r\n\t\t\t\t\t\t<view class=\"step-name\">分享海报</view>\r\n\t\t\t\t\t\t<view class=\"step-desc\">分享海报到朋友圈或好友</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"step-connector\"></view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"step-item\">\r\n\t\t\t\t\t<view class=\"step-icon-wrap step3-bg\">\r\n\t\t\t\t\t\t<image class=\"step-icon\" src=\"/static/images/tabbar/scan.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"step-info\">\r\n\t\t\t\t\t\t<view class=\"step-name\">好友扫码</view>\r\n\t\t\t\t\t\t<view class=\"step-desc\">好友扫描海报二维码</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"step-connector\"></view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"step-item\">\r\n\t\t\t\t\t<view class=\"step-icon-wrap step4-bg\">\r\n\t\t\t\t\t\t<image class=\"step-icon\" src=\"/static/images/tabbar/money.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"step-info\">\r\n\t\t\t\t\t\t<view class=\"step-name\">获得佣金</view>\r\n\t\t\t\t\t\t<view class=\"step-desc\">好友消费后您获得佣金</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 推广规则 -->\r\n\t\t<view class=\"rules-card\">\r\n\t\t\t<view class=\"rules-header\">\r\n\t\t\t\t<text class=\"rules-title\">合伙人升级规则</text>\r\n\t\t\t\t<view class=\"rules-tag\">付费升级</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"rules-content\">\r\n\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t<view class=\"rule-dot\"></view>\r\n\t\t\t\t\t<text class=\"rule-text\">普通合伙人：免费，一级佣金{{ getCommissionRate(1, 1) }}%，二级佣金{{ getCommissionRate(2, 1) }}%</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t<view class=\"rule-dot silver-dot\"></view>\r\n\t\t\t\t\t<text class=\"rule-text\">银牌合伙人：￥198/年，一级佣金{{ getCommissionRate(1, 2) }}%，二级佣金{{ getCommissionRate(2, 2) }}%</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t<view class=\"rule-dot gold-dot\"></view>\r\n\t\t\t\t\t<text class=\"rule-text\">金牌合伙人：￥498/年，一级佣金{{ getCommissionRate(1, 3) }}%，二级佣金{{ getCommissionRate(2, 3) }}%</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"rule-item\">\r\n\t\t\t\t\t<view class=\"rule-dot diamond-dot\"></view>\r\n\t\t\t\t\t<text class=\"rule-text\">钻石合伙人：￥998/年，一级佣金{{ getCommissionRate(1, 4) }}%，二级佣金{{ getCommissionRate(2, 4) }}%，专属客服</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"upgrade-action\">\r\n\t\t\t\t<button class=\"upgrade-now-btn\" @click=\"goToUpgrade\">立即升级</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 模板选择弹窗 -->\r\n\t\t<view class=\"template-modal\" v-if=\"showModal\" @click=\"hideTemplateModal\">\r\n\t\t\t<view class=\"modal-content\" @click.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">选择海报模板</text>\r\n\t\t\t\t\t<view class=\"close-btn\" @click=\"hideTemplateModal\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<scroll-view class=\"template-list\" scroll-y>\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"template-item\" \r\n\t\t\t\t\t\tv-for=\"(template, index) in posterTemplates\" \r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t:class=\"{ active: selectedTemplate === index }\"\r\n\t\t\t\t\t\t@click=\"selectTemplate(index)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<image class=\"template-image\" :src=\"template\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<view class=\"template-mask\" v-if=\"selectedTemplate === index\">\r\n\t\t\t\t\t\t\t<image class=\"check-icon\" src=\"/static/images/tabbar/check.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t\t\r\n\t\t\t\t<button class=\"confirm-btn\" @click=\"confirmTemplate\">确认选择</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 分享菜单 -->\r\n\t\t<view class=\"share-modal\" v-if=\"showShareModal\" @click=\"hideShareModal\">\r\n\t\t\t<view class=\"share-content\" @click.stop>\r\n\t\t\t\t<view class=\"share-header\">\r\n\t\t\t\t\t<text class=\"share-title\">分享到</text>\r\n\t\t\t\t\t<view class=\"close-btn\" @click=\"hideShareModal\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"share-options\">\r\n\t\t\t\t\t<view class=\"share-option\" @click=\"shareToWechat\">\r\n\t\t\t\t\t\t<view class=\"share-icon-wrap wechat-bg\">\r\n\t\t\t\t\t\t\t<image class=\"share-icon\" src=\"/static/images/tabbar/wechat.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"share-name\">微信</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"share-option\" @click=\"shareToMoments\">\r\n\t\t\t\t\t\t<view class=\"share-icon-wrap moments-bg\">\r\n\t\t\t\t\t\t\t<image class=\"share-icon\" src=\"/static/images/tabbar/moments.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"share-name\">朋友圈</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"share-option\" @click=\"shareToQQ\">\r\n\t\t\t\t\t\t<view class=\"share-icon-wrap qq-bg\">\r\n\t\t\t\t\t\t\t<image class=\"share-icon\" src=\"/static/images/tabbar/qq.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"share-name\">QQ</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"share-option\" @click=\"shareToWeibo\">\r\n\t\t\t\t\t\t<view class=\"share-icon-wrap weibo-bg\">\r\n\t\t\t\t\t\t\t<image class=\"share-icon\" src=\"/static/images/tabbar/weibo.png\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"share-name\">微博</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<button class=\"cancel-btn\" @click=\"hideShareModal\">取消</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"bottom-actions\">\r\n\t\t\t<button class=\"generate-btn\" @click=\"refreshPoster\">\r\n\t\t\t\t<image class=\"btn-icon\" src=\"/static/images/tabbar/refresh.png\"></image>\r\n\t\t\t\t<text class=\"btn-text\">重新生成海报</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue';\r\nimport { getLocalUserInfo } from '@/utils/userProfile.js';\r\n\r\n// 响应式数据\r\nconst partnerInfo = reactive({\r\n\tlevel: 2, // 默认等级\r\n\tnickname: '',\r\n\tavatar: ''\r\n});\r\nconst currentPoster = ref('/static/images/cizhou.png');\r\nconst posterTemplates = ref([\r\n\t'/static/images/cizhou.png',\r\n\t'/static/images/tabbar/推广海报.png',\r\n\t'/static/images/tabbar/我的二维码.png',\r\n\t'/static/images/tabbar/规则说明.png'\r\n]);\r\nconst selectedTemplate = ref(0);\r\nconst isGenerating = ref(false);\r\nconst showModal = ref(false);\r\nconst showShareModal = ref(false);\r\nconst promotionData = reactive({\r\n\tviewCount: '0',\r\n\tregisterCount: '0',\r\n\torderCount: '0',\r\n\tincome: '¥0.00'\r\n});\r\n\r\n// 获取用户信息\r\nconst getUserInfo = () => {\r\n\tconst userInfo = getLocalUserInfo();\r\n\tif (userInfo) {\r\n\t\tpartnerInfo.nickname = userInfo.nickname;\r\n\t\tpartnerInfo.avatar = userInfo.avatar;\r\n\t}\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n\tuni.navigateBack();\r\n};\r\n\r\n// 获取合伙人信息\r\nconst getPartnerInfo = () => {\r\n\t// 模拟数据，实际应从API获取\r\n\tpartnerInfo.level = 2;\r\n};\r\n\r\n// 获取推广数据\r\nconst getPromotionData = () => {\r\n\t// 模拟数据，实际应从API获取\r\n\tpromotionData.viewCount = '28';\r\n\tpromotionData.registerCount = '5';\r\n\tpromotionData.orderCount = '3';\r\n\tpromotionData.income = '¥35.60';\r\n};\r\n\r\n// 生成海报\r\nconst generatePoster = () => {\r\n\tisGenerating.value = true;\r\n\t\r\n\t// 模拟海报生成过程\r\n\tsetTimeout(() => {\r\n\t\t// 确保使用当前选择的模板\r\n\t\tcurrentPoster.value = posterTemplates.value[selectedTemplate.value];\r\n\t\tisGenerating.value = false;\r\n\t\t\r\n\t\t// 显示生成成功提示\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '海报生成成功',\r\n\t\t\ticon: 'success',\r\n\t\t\tduration: 1500\r\n\t\t});\r\n\t}, 1500);\r\n\t\r\n\t// 实际应调用后端API生成海报\r\n\t// const params = {\r\n\t//   templateId: selectedTemplate.value,\r\n\t//   userId: getApp().globalData.userInfo.userId,\r\n\t//   nickname: partnerInfo.nickname,\r\n\t//   avatar: partnerInfo.avatar,\r\n\t//   level: partnerInfo.level\r\n\t// };\r\n\t// \r\n\t// uni.request({\r\n\t//   url: 'https://api.example.com/generate-poster',\r\n\t//   method: 'POST',\r\n\t//   data: params,\r\n\t//   success: (res) => {\r\n\t//     if (res.data.code === 0) {\r\n\t//       currentPoster.value = res.data.data.posterUrl;\r\n\t//     } else {\r\n\t//       uni.showToast({\r\n\t//         title: res.data.message || '海报生成失败',\r\n\t//         icon: 'none'\r\n\t//       });\r\n\t//     }\r\n\t//   },\r\n\t//   fail: () => {\r\n\t//     uni.showToast({\r\n\t//       title: '网络错误，请重试',\r\n\t//       icon: 'none'\r\n\t//     });\r\n\t//   },\r\n\t//   complete: () => {\r\n\t//     isGenerating.value = false;\r\n\t//   }\r\n\t// });\r\n};\r\n\r\n// 刷新海报\r\nconst refreshPoster = () => {\r\n\tgeneratePoster();\r\n};\r\n\r\n// 保存海报到相册\r\nconst savePoster = () => {\r\n\tuni.showLoading({\r\n\t\ttitle: '保存中...'\r\n\t});\r\n\t\r\n\tuni.downloadFile({\r\n\t\turl: currentPoster.value,\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('保存失败:', err);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (err.errMsg.indexOf('auth deny') !== -1) {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\tcontent: '请授权保存图片到相册',\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\tuni.openSetting();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '保存失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '图片下载失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tfail: () => {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '网络错误，请重试',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t},\r\n\t\tcomplete: () => {\r\n\t\t\tuni.hideLoading();\r\n\t\t}\r\n\t});\r\n};\r\n\r\n// 分享海报\r\nconst sharePoster = () => {\r\n\tshowShareModal.value = true;\r\n};\r\n\r\n// 显示模板选择弹窗\r\nconst showTemplateModal = () => {\r\n\tshowModal.value = true;\r\n};\r\n\r\n// 隐藏模板选择弹窗\r\nconst hideTemplateModal = () => {\r\n\tshowModal.value = false;\r\n};\r\n\r\n// 选择模板\r\nconst selectTemplate = (index) => {\r\n\tselectedTemplate.value = index;\r\n};\r\n\r\n// 确认选择模板\r\nconst confirmTemplate = () => {\r\n\thideTemplateModal();\r\n\tgeneratePoster();\r\n};\r\n\r\n// 隐藏分享菜单\r\nconst hideShareModal = () => {\r\n\tshowShareModal.value = false;\r\n};\r\n\r\n// 分享到微信\r\nconst shareToWechat = () => {\r\n\t// 实际分享逻辑\r\n\tuni.showToast({\r\n\t\ttitle: '分享成功',\r\n\t\ticon: 'success'\r\n\t});\r\n\thideShareModal();\r\n};\r\n\r\n// 分享到朋友圈\r\nconst shareToMoments = () => {\r\n\t// 实际分享逻辑\r\n\tuni.showToast({\r\n\t\ttitle: '分享成功',\r\n\t\ticon: 'success'\r\n\t});\r\n\thideShareModal();\r\n};\r\n\r\n// 分享到QQ\r\nconst shareToQQ = () => {\r\n\t// 实际分享逻辑\r\n\tuni.showToast({\r\n\t\ttitle: '分享成功',\r\n\t\ticon: 'success'\r\n\t});\r\n\thideShareModal();\r\n};\r\n\r\n// 分享到微博\r\nconst shareToWeibo = () => {\r\n\t// 实际分享逻辑\r\n\tuni.showToast({\r\n\t\ttitle: '分享成功',\r\n\t\ticon: 'success'\r\n\t});\r\n\thideShareModal();\r\n};\r\n\r\n// 获取佣金比例\r\nconst getCommissionRate = (level, partnerLevel) => {\r\n\t// 根据合伙人等级返回不同的佣金比例\r\n\tconst commissionRates = {\r\n\t\t1: { 1: 5, 2: 2 },    // 普通合伙人：一级5%，二级2%\r\n\t\t2: { 1: 8, 2: 3 },    // 银牌合伙人：一级8%，二级3%\r\n\t\t3: { 1: 12, 2: 5 },   // 金牌合伙人：一级12%，二级5%\r\n\t\t4: { 1: 15, 2: 8 }    // 钻石合伙人：一级15%，二级8%\r\n\t};\r\n\t\r\n\t// 如果指定了合伙人等级，使用指定的等级\r\n\tif (partnerLevel) {\r\n\t\treturn commissionRates[partnerLevel]?.[level] || 0;\r\n\t}\r\n\t\r\n\t// 否则使用当前用户的合伙人等级\r\n\treturn commissionRates[partnerInfo.level]?.[level] || 0;\r\n};\r\n\r\n// 获取等级名称\r\nconst getLevelName = (level) => {\r\n\tconst levelNames = {\r\n\t\t1: '普通合伙人',\r\n\t\t2: '银牌合伙人',\r\n\t\t3: '金牌合伙人',\r\n\t\t4: '钻石合伙人'\r\n\t};\r\n\treturn levelNames[level] || '未知等级';\r\n};\r\n\r\n// 跳转到升级页面\r\nconst goToUpgrade = () => {\r\n\t// 跳转到合伙人升级页面\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/new-partner/partner-upgrade',\r\n\t\tsuccess: () => {\r\n\t\t\tconsole.log('跳转到合伙人升级页面成功');\r\n\t\t},\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.error('跳转失败:', err);\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '页面跳转失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\t// 获取用户信息\r\n\tgetUserInfo();\r\n\t// 获取合伙人信息\r\n\tgetPartnerInfo();\r\n\t// 获取推广数据\r\n\tgetPromotionData();\r\n\t// 生成海报\r\n\tgeneratePoster();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.poster-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #F5F7FA;\r\n\t\tpadding-bottom: 30rpx;\r\n\t\tpadding-top: calc(44px + var(--status-bar-height));\r\n\t}\r\n\t\r\n\t/* 自定义导航栏 */\r\n\t.custom-navbar {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\theight: 88rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tpadding-top: 44px; /* 状态栏高度 */\r\n\t\tposition: fixed; /* 改为固定定位 */\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbackground-image: linear-gradient(135deg, #0066FF, #0052CC); /* 改为与发布页一致的渐变角度 */\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);\r\n\t\tz-index: 100; /* 提高z-index确保在最上层 */\r\n\t}\r\n\t\r\n\t.navbar-title {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 700;\r\n\t\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.navbar-right {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\t\r\n\t.navbar-left {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: relative;\r\n\t\tz-index: 20; /* 确保在标题上层，可以被点击 */\r\n\t}\r\n\t\r\n\t.back-icon {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t\r\n\t.safe-area-top {\r\n\t\theight: var(--status-bar-height);\r\n\t\twidth: 100%;\r\n\t\tbackground-image: linear-gradient(135deg, #0066FF, #0052CC);\r\n\t}\r\n\t\r\n\t/* 海报预览区域 */\r\n\t.poster-preview {\r\n\t\tmargin: 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\r\n\t}\r\n\t\r\n\t.preview-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\t\r\n\t.preview-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333333;\r\n\t}\r\n\t\r\n\t.template-switch {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #E6F4FF;\r\n\t\tborder-radius: 30rpx;\r\n\t\tpadding: 12rpx 24rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.1);\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\t\r\n\t.template-switch:active {\r\n\t\ttransform: scale(0.95);\r\n\t\tbackground-color: #D1EBFF;\r\n\t}\r\n\t\r\n\t.switch-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #1677FF;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t.switch-icon {\r\n\t\twidth: 28rpx;\r\n\t\theight: 28rpx;\r\n\t\tmargin-right: 8rpx;\r\n\t}\r\n\t\r\n\t.poster-card {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t.poster-image {\r\n\t\twidth: 80%;\r\n\t\tdisplay: block;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t\tmin-height: 400rpx;\r\n\t\tobject-fit: contain;\r\n\t}\r\n\t\r\n\t.poster-loading {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(255, 255, 255, 0.9);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 16rpx;\r\n\t}\r\n\t\r\n\t.loading-spinner {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder: 8rpx solid #f3f3f3;\r\n\t\tborder-top: 8rpx solid #1677FF;\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: spin 1s linear infinite;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t@keyframes spin {\r\n\t\t0% { transform: rotate(0deg); }\r\n\t\t100% { transform: rotate(360deg); }\r\n\t}\r\n\t\r\n\t.loading-text {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t/* 新的美化后的按钮组样式 */\r\n\t.new-poster-actions {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-top: 1rpx solid #f0f0f0;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\t\r\n\t.action-button {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tbackground-color: transparent;\r\n\t\tpadding: 0;\r\n\t\tline-height: normal;\r\n\t\twidth: 33%;\r\n\t\theight: auto;\r\n\t}\r\n\t\r\n\t.action-button::after {\r\n\t\tborder: none;\r\n\t}\r\n\t\r\n\t.button-icon-wrap {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-color: #f0f5ff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\ttransition: all 0.3s;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\t\r\n\t.save-icon-bg {\r\n\t\tbackground-color: #e6f7ff;\r\n\t}\r\n\t\r\n\t.share-icon-bg {\r\n\t\tbackground-color: #f6ffed;\r\n\t}\r\n\t\r\n\t.button-icon {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\t\r\n\t.button-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t.refresh-button:active .button-icon-wrap {\r\n\t\ttransform: rotate(180deg);\r\n\t}\r\n\t\r\n\t.save-button:active .button-icon-wrap, \r\n\t.share-button:active .button-icon-wrap {\r\n\t\ttransform: scale(0.9);\r\n\t}\r\n\t\r\n\t/* 统计卡片 */\r\n\t.stats-card {\r\n\t\tmargin: 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\r\n\t}\r\n\t\r\n\t.stats-header {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\t\r\n\t.stats-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333333;\r\n\t}\r\n\t\r\n\t.stats-content {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 30rpx 0;\r\n\t}\r\n\t\r\n\t.stats-item {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.stats-value {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #1677FF;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.stats-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\t\r\n\t.stats-divider {\r\n\t\twidth: 1rpx;\r\n\t\theight: 60rpx;\r\n\t\tbackground-color: #f0f0f0;\r\n\t\talign-self: center;\r\n\t}\r\n\t\r\n\t/* 佣金卡片 */\r\n\t.commission-card {\r\n\t\tmargin: 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\r\n\t}\r\n\t\r\n\t.commission-header {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\t\r\n\t.commission-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333333;\r\n\t}\r\n\t\r\n\t.commission-content {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t}\r\n\t\r\n\t.commission-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 24rpx 0;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.commission-info {\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.commission-label {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\t\r\n\t.commission-desc {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\t\r\n\t.commission-value {\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #FF6B00;\r\n\t\tbackground-color: rgba(255, 107, 0, 0.1);\r\n\t\tpadding: 10rpx 24rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t}\r\n\t\r\n\t.level-tag {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #FF6B00;\r\n\t\tbackground-color: rgba(255, 107, 0, 0.1);\r\n\t\tpadding: 4rpx 12rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\t\r\n\t.level-benefits {\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.benefit-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.benefit-icon-wrap {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t\r\n\t.benefit-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t.upgrade-tips {\r\n\t\tmargin-top: 10rpx;\r\n\t\ttext-align: right;\r\n\t}\r\n\t\r\n\t.tips-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\t\r\n\t.upgrade-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #E6F4FF;\r\n\t\tborder-radius: 30rpx;\r\n\t\tpadding: 8rpx 20rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.1);\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\t\r\n\t.upgrade-btn:active {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\t\r\n\t.upgrade-icon {\r\n\t\twidth: 20rpx;\r\n\t\theight: 20rpx;\r\n\t\tmargin-left: 8rpx;\r\n\t}\r\n\t\r\n\t/* 步骤卡片 */\r\n\t.steps-card {\r\n\t\tmargin: 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\r\n\t}\r\n\t\r\n\t.steps-header {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\t\r\n\t.steps-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333333;\r\n\t}\r\n\t\r\n\t.steps-content {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\t\r\n\t.step-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.step-icon-wrap {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 30rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\t\r\n\t.step1-bg {\r\n\t\tbackground-color: rgba(22, 119, 255, 0.1);\r\n\t}\r\n\t\r\n\t.step2-bg {\r\n\t\tbackground-color: rgba(255, 149, 0, 0.1);\r\n\t}\r\n\t\r\n\t.step3-bg {\r\n\t\tbackground-color: rgba(52, 199, 89, 0.1);\r\n\t}\r\n\t\r\n\t.step4-bg {\r\n\t\tbackground-color: rgba(255, 59, 48, 0.1);\r\n\t}\r\n\t\r\n\t.step-icon {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\t\r\n\t.step-info {\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.step-name {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\t\r\n\t.step-desc {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\t\r\n\t.step-connector {\r\n\t\twidth: 2rpx;\r\n\t\theight: 40rpx;\r\n\t\tbackground-color: #E6E6E6;\r\n\t\tmargin-left: 40rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\t\r\n\t/* 规则卡片 */\r\n\t.rules-card {\r\n\t\tmargin: 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);\r\n\t}\r\n\t\r\n\t.rules-header {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.rules-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333333;\r\n\t\tmargin-right: 16rpx;\r\n\t}\r\n\t\r\n\t.rules-tag {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #FF6B00;\r\n\t\tbackground-color: rgba(255, 107, 0, 0.1);\r\n\t\tpadding: 4rpx 12rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\t\r\n\t.rules-content {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t}\r\n\t\r\n\t.rule-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.rule-dot {\r\n\t\twidth: 12rpx;\r\n\t\theight: 12rpx;\r\n\t\tbackground-color: #1677FF;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-top: 12rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\t\r\n\t.silver-dot {\r\n\t\tbackground-color: #C0C0C0;\r\n\t}\r\n\t\r\n\t.gold-dot {\r\n\t\tbackground-color: #FFD700;\r\n\t}\r\n\t\r\n\t.diamond-dot {\r\n\t\tbackground-color: #B9F2FF;\r\n\t\tbox-shadow: 0 0 5rpx #B9F2FF;\r\n\t}\r\n\t\r\n\t.rule-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666666;\r\n\t\tline-height: 1.6;\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.upgrade-action {\r\n\t\tpadding: 20rpx 30rpx 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t.upgrade-now-btn {\r\n\t\twidth: 80%;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tbackground-image: linear-gradient(135deg, #FF9500, #FF6B00);\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tborder-radius: 40rpx;\r\n\t\tbox-shadow: 0 6rpx 12rpx rgba(255, 107, 0, 0.2);\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\t\r\n\t.upgrade-now-btn:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbox-shadow: 0 3rpx 6rpx rgba(255, 107, 0, 0.3);\r\n\t}\r\n\t\r\n\t/* 模板选择弹窗 */\r\n\t.template-modal, .share-modal {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: flex-end;\r\n\t\tz-index: 999;\r\n\t}\r\n\t\r\n\t.template-modal {\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.share-content {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 24rpx 24rpx 0 0;\r\n\t\toverflow: hidden;\r\n\t\tanimation: slideUp 0.3s ease-out;\r\n\t}\r\n\t\r\n\t@keyframes slideUp {\r\n\t\tfrom {\r\n\t\t\ttransform: translateY(100%);\r\n\t\t}\r\n\t\tto {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.share-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\t\r\n\t.share-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333333;\r\n\t}\r\n\t\r\n\t.close-btn {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #999;\r\n\t\tline-height: 1;\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\t\r\n\t.share-options {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 40rpx 20rpx 20rpx;\r\n\t}\r\n\t\r\n\t.share-option {\r\n\t\twidth: 25%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\t\r\n\t.share-icon-wrap {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 16rpx;\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\t\r\n\t.wechat-bg {\r\n\t\tbackground-color: #95EC69;\r\n\t}\r\n\t\r\n\t.moments-bg {\r\n\t\tbackground-color: #FFD666;\r\n\t}\r\n\t\r\n\t.qq-bg {\r\n\t\tbackground-color: #91D5FF;\r\n\t}\r\n\t\r\n\t.weibo-bg {\r\n\t\tbackground-color: #FFA39E;\r\n\t}\r\n\t\r\n\t.share-option:active .share-icon-wrap {\r\n\t\ttransform: scale(0.9);\r\n\t}\r\n\t\r\n\t.share-icon {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t}\r\n\t\r\n\t.share-name {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\t\r\n\t.cancel-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t\tline-height: 100rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t\tborder-radius: 0;\r\n\t\tborder-top: 10rpx solid #f5f5f7;\r\n\t}\r\n\t\r\n\t/* 底部操作按钮 */\r\n\t.bottom-actions {\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.generate-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t\tbackground-image: linear-gradient(135deg, #0066FF, #0052CC);\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t\tborder-radius: 45rpx;\r\n\t\tbox-shadow: 0 8rpx 16rpx rgba(0, 82, 204, 0.2);\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\ttransition: all 0.3s;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t.btn-icon {\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tmargin-right: 12rpx;\r\n\t}\r\n\t\r\n\t.btn-text {\r\n\t\tfont-weight: 600;\r\n\t\tletter-spacing: 2rpx;\r\n\t}\r\n\t\r\n\t.generate-btn::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: -100%;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-image: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.5) 50%, rgba(255,255,255,0) 100%);\r\n\t\topacity: 0.6;\r\n\t\ttransition: all 0.8s;\r\n\t}\r\n\t\r\n\t.generate-btn:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.3);\r\n\t}\r\n\t\r\n\t.generate-btn:active::after {\r\n\t\tleft: 100%;\r\n\t}\r\n\t\r\n\t/* 模板选择弹窗 */\r\n\t.modal-content {\r\n\t\twidth: 80%;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tanimation: fadeIn 0.3s ease-out;\r\n\t}\r\n\t\r\n\t@keyframes fadeIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: scale(0.9);\r\n\t\t}\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.modal-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\t\r\n\t.modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333333;\r\n\t}\r\n\t\r\n\t.template-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 20rpx;\r\n\t\tmax-height: 600rpx;\r\n\t}\r\n\t\r\n\t.template-item {\r\n\t\tposition: relative;\r\n\t\twidth: calc(50% - 20rpx);\r\n\t\tmargin: 10rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n\t\tbackground-color: #f5f7fa;\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\t\r\n\t.template-item:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\t\r\n\t.template-item.active {\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.3);\r\n\t}\r\n\t\r\n\t.template-image {\r\n\t\twidth: 100%;\r\n\t\theight: 300rpx;\r\n\t\tdisplay: block;\r\n\t\tobject-fit: contain;\r\n\t}\r\n\t\r\n\t.template-mask {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(22, 119, 255, 0.3);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t.check-icon {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t}\r\n\t\r\n\t.confirm-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t\tbackground-color: #1677FF;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tborder-radius: 0;\r\n\t}\r\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/partner/pages/partner-poster.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "getLocalUserInfo", "uni", "res", "onMounted"], "mappings": ";;;;;;;AAuSA,UAAM,cAAcA,cAAAA,SAAS;AAAA,MAC5B,OAAO;AAAA;AAAA,MACP,UAAU;AAAA,MACV,QAAQ;AAAA,IACT,CAAC;AACD,UAAM,gBAAgBC,cAAAA,IAAI,2BAA2B;AACrD,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AACD,UAAM,mBAAmBA,cAAAA,IAAI,CAAC;AAC9B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,gBAAgBD,cAAAA,SAAS;AAAA,MAC9B,WAAW;AAAA,MACX,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,IACT,CAAC;AAGD,UAAM,cAAc,MAAM;AACzB,YAAM,WAAWE,kBAAAA;AACjB,UAAI,UAAU;AACb,oBAAY,WAAW,SAAS;AAChC,oBAAY,SAAS,SAAS;AAAA,MAC9B;AAAA,IACF;AAGA,UAAM,SAAS,MAAM;AACpBC,oBAAG,MAAC,aAAY;AAAA,IACjB;AAGA,UAAM,iBAAiB,MAAM;AAE5B,kBAAY,QAAQ;AAAA,IACrB;AAGA,UAAM,mBAAmB,MAAM;AAE9B,oBAAc,YAAY;AAC1B,oBAAc,gBAAgB;AAC9B,oBAAc,aAAa;AAC3B,oBAAc,SAAS;AAAA,IACxB;AAGA,UAAM,iBAAiB,MAAM;AAC5B,mBAAa,QAAQ;AAGrB,iBAAW,MAAM;AAEhB,sBAAc,QAAQ,gBAAgB,MAAM,iBAAiB,KAAK;AAClE,qBAAa,QAAQ;AAGrBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AAAA,MACD,GAAE,IAAI;AAAA,IAmCR;AAGA,UAAM,gBAAgB,MAAM;AAC3B;IACD;AAGA,UAAM,aAAa,MAAM;AACxBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACT,CAAE;AAEDA,oBAAAA,MAAI,aAAa;AAAA,QAChB,KAAK,cAAc;AAAA,QACnB,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,eAAe,KAAK;AAC3BA,0BAAAA,MAAI,uBAAuB;AAAA,cAC1B,UAAU,IAAI;AAAA,cACd,SAAS,MAAM;AACdA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACb,CAAO;AAAA,cACD;AAAA,cACD,MAAM,CAAC,QAAQ;AACdA,8BAAc,MAAA,MAAA,SAAA,uDAAA,SAAS,GAAG;AAE1B,oBAAI,IAAI,OAAO,QAAQ,WAAW,MAAM,IAAI;AAC3CA,gCAAAA,MAAI,UAAU;AAAA,oBACb,OAAO;AAAA,oBACP,SAAS;AAAA,oBACT,SAAS,CAACC,SAAQ;AACjB,0BAAIA,KAAI,SAAS;AAChBD,sCAAG,MAAC,YAAW;AAAA,sBACf;AAAA,oBACD;AAAA,kBACT,CAAQ;AAAA,gBACR,OAAa;AACNA,gCAAAA,MAAI,UAAU;AAAA,oBACb,OAAO;AAAA,oBACP,MAAM;AAAA,kBACd,CAAQ;AAAA,gBACD;AAAA,cACD;AAAA,YACN,CAAK;AAAA,UACL,OAAU;AACNA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACX,CAAK;AAAA,UACD;AAAA,QACD;AAAA,QACD,MAAM,MAAM;AACXA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,QACD,UAAU,MAAM;AACfA,wBAAG,MAAC,YAAW;AAAA,QACf;AAAA,MACH,CAAE;AAAA,IACF;AAGA,UAAM,cAAc,MAAM;AACzB,qBAAe,QAAQ;AAAA,IACxB;AAGA,UAAM,oBAAoB,MAAM;AAC/B,gBAAU,QAAQ;AAAA,IACnB;AAGA,UAAM,oBAAoB,MAAM;AAC/B,gBAAU,QAAQ;AAAA,IACnB;AAGA,UAAM,iBAAiB,CAAC,UAAU;AACjC,uBAAiB,QAAQ;AAAA,IAC1B;AAGA,UAAM,kBAAkB,MAAM;AAC7B;AACA;IACD;AAGA,UAAM,iBAAiB,MAAM;AAC5B,qBAAe,QAAQ;AAAA,IACxB;AAGA,UAAM,gBAAgB,MAAM;AAE3BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AACD;IACD;AAGA,UAAM,iBAAiB,MAAM;AAE5BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AACD;IACD;AAGA,UAAM,YAAY,MAAM;AAEvBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AACD;IACD;AAGA,UAAM,eAAe,MAAM;AAE1BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AACD;IACD;AAGA,UAAM,oBAAoB,CAAC,OAAO,iBAAiB;;AAElD,YAAM,kBAAkB;AAAA,QACvB,GAAG,EAAE,GAAG,GAAG,GAAG,EAAG;AAAA;AAAA,QACjB,GAAG,EAAE,GAAG,GAAG,GAAG,EAAG;AAAA;AAAA,QACjB,GAAG,EAAE,GAAG,IAAI,GAAG,EAAG;AAAA;AAAA,QAClB,GAAG,EAAE,GAAG,IAAI,GAAG,EAAG;AAAA;AAAA,MACpB;AAGC,UAAI,cAAc;AACjB,iBAAO,qBAAgB,YAAY,MAA5B,mBAAgC,WAAU;AAAA,MACjD;AAGD,eAAO,qBAAgB,YAAY,KAAK,MAAjC,mBAAqC,WAAU;AAAA,IACvD;AAGA,UAAM,eAAe,CAAC,UAAU;AAC/B,YAAM,aAAa;AAAA,QAClB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACC,aAAO,WAAW,KAAK,KAAK;AAAA,IAC7B;AAGA,UAAM,cAAc,MAAM;AAEzBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,QACL,SAAS,MAAM;AACdA,wBAAAA,MAAA,MAAA,OAAA,uDAAY,cAAc;AAAA,QAC1B;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAA,MAAA,MAAA,SAAA,uDAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGAE,kBAAAA,UAAU,MAAM;AAEf;AAEA;AAEA;AAEA;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/kBD,GAAG,WAAW,eAAe;"}