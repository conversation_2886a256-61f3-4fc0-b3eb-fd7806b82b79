"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_navigation = require("../../../utils/navigation.js");
const _sfc_main = {
  __name: "withdraw",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const amount = common_vendor.ref("");
    const balanceInfo = common_vendor.ref({
      amount: 158.5,
      frozenAmount: 0
    });
    const selectedCard = common_vendor.ref(null);
    const bankCards = common_vendor.ref([]);
    const canWithdraw = common_vendor.computed(() => {
      const amountNum = parseFloat(amount.value);
      return amountNum >= 1 && amountNum <= 5e3 && amountNum <= balanceInfo.value.amount && selectedCard.value;
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      utils_navigation.smartNavigate(url).catch((err) => {
        common_vendor.index.__f__("error", "at subPackages/payment/pages/withdraw.vue:110", "页面跳转失败:", err);
      });
    };
    const getWalletBalance = () => {
      setTimeout(() => {
        balanceInfo.value = {
          amount: 158.5,
          frozenAmount: 0
        };
      }, 500);
    };
    const getBankCards = () => {
      setTimeout(() => {
        bankCards.value = [
          {
            id: "card001",
            bankName: "中国建设银行",
            bankLogo: "/static/images/banks/ccb.png",
            cardNumber: "6217 0012 3456 7890",
            cardNumberLast4: "7890",
            isDefault: true
          }
        ];
        if (bankCards.value.length > 0) {
          selectedCard.value = bankCards.value.find((card) => card.isDefault) || bankCards.value[0];
        }
      }, 500);
    };
    const handleAmountInput = (e) => {
      const value = e.detail.value;
      if (value.indexOf(".") !== -1) {
        const decimal = value.split(".")[1];
        if (decimal.length > 2) {
          amount.value = parseFloat(value).toFixed(2);
        }
      }
    };
    const withdrawAll = () => {
      amount.value = balanceInfo.value.amount.toFixed(2);
    };
    const submitWithdraw = () => {
      if (!canWithdraw.value) {
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "提示",
          content: "提现申请已提交，将在1-3个工作日内到账",
          showCancel: false,
          success: () => {
            common_vendor.index.navigateBack();
          }
        });
      }, 1500);
    };
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      getWalletBalance();
      getBankCards();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.o([($event) => amount.value = $event.detail.value, handleAmountInput]),
        e: amount.value,
        f: common_vendor.t(balanceInfo.value.amount.toFixed(2)),
        g: common_vendor.o(withdrawAll),
        h: navbarHeight.value + 10 + "px",
        i: selectedCard.value
      }, selectedCard.value ? {
        j: selectedCard.value.bankLogo,
        k: common_vendor.t(selectedCard.value.bankName),
        l: common_vendor.t(selectedCard.value.cardNumberLast4),
        m: common_vendor.o(($event) => navigateTo("/pages/my/wallet-bank"))
      } : {
        n: common_assets._imports_2$12,
        o: common_vendor.o(($event) => navigateTo("/pages/my/wallet-bank"))
      }, {
        p: !canWithdraw.value,
        q: !canWithdraw.value ? 1 : "",
        r: common_vendor.o(submitWithdraw)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/payment/pages/withdraw.js.map
