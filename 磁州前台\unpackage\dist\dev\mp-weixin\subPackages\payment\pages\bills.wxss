
.bills-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}
.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
}

/* 筛选区域 */
.filter-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
}
.filter-tabs {
  display: flex;
}
.filter-tab {
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  margin-right: 20rpx;
}
.filter-tab.active {
  color: #0052CC;
  font-weight: 500;
}
.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #0052CC;
  border-radius: 2rpx;
}
.filter-date {
  font-size: 28rpx;
  color: #333;
}
.date-picker {
  display: flex;
  align-items: center;
}
.date-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}

/* 统计区域 */
.statistics-section {
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 20rpx;
}
.statistics-item {
  text-align: center;
}
.statistics-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.statistics-value {
  font-size: 36rpx;
  font-weight: 500;
}
.income {
  color: #07c160;
}
.expense {
  color: #f56c6c;
}
.statistics-divider {
  width: 1px;
  height: 60rpx;
  background-color: #f5f5f5;
}

/* 交易记录列表 */
.transaction-list {
  padding: 0 30rpx;
}
.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}
.date-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.date-summary {
  font-size: 24rpx;
}
.date-income {
  color: #07c160;
  margin-right: 20rpx;
}
.date-expense {
  color: #f56c6c;
}
.transaction-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}
.transaction-left {
  margin-right: 20rpx;
}
.transaction-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.income-icon {
  background-color: rgba(7, 193, 96, 0.1);
}
.expense-icon {
  background-color: rgba(245, 108, 108, 0.1);
}
.type-icon {
  width: 40rpx;
  height: 40rpx;
}
.transaction-center {
  flex: 1;
}
.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.transaction-time {
  font-size: 24rpx;
  color: #999;
}
.transaction-right {
  text-align: right;
}
.transaction-amount {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.transaction-status {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999;
}
.load-more {
  color: #0052CC;
}

/* 空状态 */
.empty-view {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
