{"version": 3, "file": "navigation.js", "sources": ["utils/navigation.js"], "sourcesContent": ["/**\n * 导航工具类\n * 提供安全的页面导航方法，包含错误处理\n */\n\n/**\n * 安全导航到指定页面\n * @param {string} url 目标页面URL\n * @param {Object} options 额外选项\n * @returns {Promise} 导航结果Promise\n */\nexport const navigateTo = (url, options = {}) => {\n  return new Promise((resolve, reject) => {\n    try {\n      // 导航前检查URL格式\n      if (!url || typeof url !== 'string') {\n        const error = new Error('导航URL无效');\n        error.code = 'INVALID_URL';\n        \n        // 根据选项决定是否显示提示\n        if (options.showToast !== false) {\n          uni.showToast({\n            title: error.message,\n            icon: 'none',\n            duration: 2000\n          });\n        }\n        \n        return reject(error);\n      }\n      \n      // 执行导航\n      uni.navigateTo({\n        url,\n        success: (res) => {\n          console.log('页面导航成功:', url);\n          resolve(res);\n        },\n        fail: (err) => {\n          console.error('页面导航失败:', err);\n          \n          // 根据错误类型进行处理\n          let errorMessage = '页面跳转失败';\n          \n          if (err.errMsg && err.errMsg.includes('is not found')) {\n            errorMessage = '该功能正在开发中';\n          } else if (err.errMsg && err.errMsg.includes('can not navigate')) {\n            errorMessage = '无法导航到该页面';\n          }\n          \n          // 根据选项决定是否显示提示\n          if (options.showToast !== false) {\n            uni.showToast({\n              title: options.failMessage || errorMessage,\n              icon: 'none',\n              duration: 2000\n            });\n          }\n          \n          reject(err);\n        }\n      });\n    } catch (error) {\n      console.error('导航方法异常:', error);\n      \n      // 根据选项决定是否显示提示\n      if (options.showToast !== false) {\n        uni.showToast({\n          title: '系统异常，请稍后再试',\n          icon: 'none',\n          duration: 2000\n        });\n      }\n      \n      reject(error);\n    }\n  });\n};\n\n/**\n * 重定向到指定页面\n * @param {string} url 目标页面URL\n * @param {Object} options 额外选项\n * @returns {Promise} 导航结果Promise\n */\nexport const redirectTo = (url, options = {}) => {\n  return new Promise((resolve, reject) => {\n    try {\n      uni.redirectTo({\n        url,\n        success: (res) => {\n          console.log('页面重定向成功:', url);\n          resolve(res);\n        },\n        fail: (err) => {\n          console.error('页面重定向失败:', err);\n          \n          // 显示提示\n          if (options.showToast !== false) {\n            uni.showToast({\n              title: options.failMessage || '页面跳转失败',\n              icon: 'none',\n              duration: 2000\n            });\n          }\n          \n          reject(err);\n        }\n      });\n    } catch (error) {\n      console.error('重定向方法异常:', error);\n      \n      if (options.showToast !== false) {\n        uni.showToast({\n          title: '系统异常，请稍后再试',\n          icon: 'none',\n          duration: 2000\n        });\n      }\n      \n      reject(error);\n    }\n  });\n};\n\n/**\n * 导航到Tab页面\n * @param {string} url 目标页面URL\n * @param {Object} options 额外选项\n * @returns {Promise} 导航结果Promise\n */\nexport const switchTab = (url, options = {}) => {\n  return new Promise((resolve, reject) => {\n    try {\n      uni.switchTab({\n        url,\n        success: (res) => {\n          console.log('切换到Tab页面成功:', url);\n          resolve(res);\n        },\n        fail: (err) => {\n          console.error('切换到Tab页面失败:', err);\n          \n          if (options.showToast !== false) {\n            uni.showToast({\n              title: options.failMessage || '页面切换失败',\n              icon: 'none',\n              duration: 2000\n            });\n          }\n          \n          reject(err);\n        }\n      });\n    } catch (error) {\n      console.error('切换Tab方法异常:', error);\n      \n      if (options.showToast !== false) {\n        uni.showToast({\n          title: '系统异常，请稍后再试',\n          icon: 'none',\n          duration: 2000\n        });\n      }\n      \n      reject(error);\n    }\n  });\n};\n\n/**\n * 智能导航方法 - 自动选择最合适的导航方式\n * @param {string|Object} urlOrConfig 目标页面URL或配置对象\n * @param {Object} options 额外选项\n * @returns {Promise} 导航结果Promise\n */\nexport const smartNavigate = (urlOrConfig, options = {}) => {\n  // 处理兼容性：接受字符串或对象参数\n  let url = urlOrConfig;\n  if (typeof urlOrConfig === 'object' && urlOrConfig !== null) {\n    // 如果传入的是对象，尝试从对象中获取url\n    url = urlOrConfig.url;\n    \n    // 合并options\n    if (urlOrConfig.options) {\n      options = { ...options, ...urlOrConfig.options };\n    }\n  }\n  \n  // 如果url不是字符串，抛出错误\n  if (typeof url !== 'string') {\n    const error = new Error('导航URL无效');\n    console.error('smartNavigate 参数错误:', urlOrConfig);\n    return Promise.reject(error);\n  }\n  \n  // 判断是否是Tabbar页面\n  const tabPages = [\n    '/pages/index/index',\n    '/pages/recommend/index',\n    '/pages/publish/index',\n    '/pages/message/index',\n    '/pages/my/my'\n  ];\n  \n  const isTabPage = tabPages.some(tabUrl => url === tabUrl || url.startsWith(tabUrl + '?'));\n  \n  // 根据页面类型选择导航方式\n  if (isTabPage) {\n    return switchTab(url, options);\n  } else {\n    return navigateTo(url, options);\n  }\n};\n\nexport default {\n  navigateTo,\n  redirectTo,\n  switchTab,\n  smartNavigate\n}; "], "names": ["uni"], "mappings": ";;AAWO,MAAM,aAAa,CAAC,KAAK,UAAU,OAAO;AAC/C,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI;AAEF,UAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACnC,cAAM,QAAQ,IAAI,MAAM,SAAS;AACjC,cAAM,OAAO;AAGb,YAAI,QAAQ,cAAc,OAAO;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM;AAAA,YACb,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AAAA,QACF;AAED,eAAO,OAAO,KAAK;AAAA,MACpB;AAGDA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,SAAS,CAAC,QAAQ;AAChBA,wBAAA,MAAA,MAAA,OAAA,6BAAY,WAAW,GAAG;AAC1B,kBAAQ,GAAG;AAAA,QACZ;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,6BAAc,WAAW,GAAG;AAG5B,cAAI,eAAe;AAEnB,cAAI,IAAI,UAAU,IAAI,OAAO,SAAS,cAAc,GAAG;AACrD,2BAAe;AAAA,UAC3B,WAAqB,IAAI,UAAU,IAAI,OAAO,SAAS,kBAAkB,GAAG;AAChE,2BAAe;AAAA,UAChB;AAGD,cAAI,QAAQ,cAAc,OAAO;AAC/BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,QAAQ,eAAe;AAAA,cAC9B,MAAM;AAAA,cACN,UAAU;AAAA,YACxB,CAAa;AAAA,UACF;AAED,iBAAO,GAAG;AAAA,QACX;AAAA,MACT,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,6BAAA,WAAW,KAAK;AAG9B,UAAI,QAAQ,cAAc,OAAO;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACpB,CAAS;AAAA,MACF;AAED,aAAO,KAAK;AAAA,IACb;AAAA,EACL,CAAG;AACH;AAsDO,MAAM,YAAY,CAAC,KAAK,UAAU,OAAO;AAC9C,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI;AACFA,oBAAAA,MAAI,UAAU;AAAA,QACZ;AAAA,QACA,SAAS,CAAC,QAAQ;AAChBA,wBAAA,MAAA,MAAA,OAAA,8BAAY,eAAe,GAAG;AAC9B,kBAAQ,GAAG;AAAA,QACZ;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,8BAAc,eAAe,GAAG;AAEhC,cAAI,QAAQ,cAAc,OAAO;AAC/BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,QAAQ,eAAe;AAAA,cAC9B,MAAM;AAAA,cACN,UAAU;AAAA,YACxB,CAAa;AAAA,UACF;AAED,iBAAO,GAAG;AAAA,QACX;AAAA,MACT,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,8BAAA,cAAc,KAAK;AAEjC,UAAI,QAAQ,cAAc,OAAO;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACpB,CAAS;AAAA,MACF;AAED,aAAO,KAAK;AAAA,IACb;AAAA,EACL,CAAG;AACH;AAQY,MAAC,gBAAgB,CAAC,aAAa,UAAU,OAAO;AAE1D,MAAI,MAAM;AACV,MAAI,OAAO,gBAAgB,YAAY,gBAAgB,MAAM;AAE3D,UAAM,YAAY;AAGlB,QAAI,YAAY,SAAS;AACvB,gBAAU,EAAE,GAAG,SAAS,GAAG,YAAY,QAAO;AAAA,IAC/C;AAAA,EACF;AAGD,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,QAAQ,IAAI,MAAM,SAAS;AACjCA,kBAAc,MAAA,MAAA,SAAA,8BAAA,uBAAuB,WAAW;AAChD,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC5B;AAGD,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAEE,QAAM,YAAY,SAAS,KAAK,YAAU,QAAQ,UAAU,IAAI,WAAW,SAAS,GAAG,CAAC;AAGxF,MAAI,WAAW;AACb,WAAO,UAAU,KAAK,OAAO;AAAA,EACjC,OAAS;AACL,WAAO,WAAW,KAAK,OAAO;AAAA,EAC/B;AACH;;"}