
.house-details.data-v-cc27e780 {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

/* 房屋基本信息 */
.house-basic-info.data-v-cc27e780 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.house-type.data-v-cc27e780, .house-size.data-v-cc27e780, .house-floor.data-v-cc27e780, .house-direction.data-v-cc27e780 {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}
.house-icon.data-v-cc27e780 {
  margin-right: 8rpx;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
}
.type-icon.data-v-cc27e780 {
  color: #FF9500;
}
.size-icon.data-v-cc27e780 {
  color: #34C759;
}
.floor-icon.data-v-cc27e780 {
  color: #5856D6;
}
.direction-icon.data-v-cc27e780 {
  color: #007AFF;
}
.house-text.data-v-cc27e780 {
  font-size: 24rpx;
  color: #636366;
  font-weight: 500;
}

/* 房屋配套设施 */
.house-facilities.data-v-cc27e780 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}
.facility-tag.data-v-cc27e780 {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 176, 199, 0.1));
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid rgba(52, 199, 89, 0.2);
}
.facility-icon.data-v-cc27e780 {
  margin-right: 6rpx;
  color: #34C759;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ac-icon.data-v-cc27e780, .water-icon.data-v-cc27e780 {
  color: #007AFF;
}
.washer-icon.data-v-cc27e780, .fridge-icon.data-v-cc27e780 {
  color: #5856D6;
}
.tv-icon.data-v-cc27e780, .wifi-icon.data-v-cc27e780 {
  color: #FF9500;
}
.wardrobe-icon.data-v-cc27e780, .bed-icon.data-v-cc27e780, .sofa-icon.data-v-cc27e780 {
  color: #FF3B30;
}
.gas-icon.data-v-cc27e780 {
  color: #FF2D55;
}
.facility-text.data-v-cc27e780 {
  font-size: 22rpx;
  color: #34C759;
  font-weight: 500;
}

/* 房屋特色标签 */
.house-features.data-v-cc27e780 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}
.feature-tag.data-v-cc27e780 {
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 59, 48, 0.1));
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid rgba(255, 149, 0, 0.2);
}
.feature-text.data-v-cc27e780 {
  font-size: 22rpx;
  color: #FF9500;
  font-weight: 500;
}

/* 房屋地理位置 */
.house-location.data-v-cc27e780 {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 12rpx 16rpx;
  border-radius: 16rpx;
  margin-top: 16rpx;
}
.location-icon.data-v-cc27e780 {
  color: #007AFF;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.location-text.data-v-cc27e780 {
  font-size: 26rpx;
  color: #636366;
  flex: 1;
}
