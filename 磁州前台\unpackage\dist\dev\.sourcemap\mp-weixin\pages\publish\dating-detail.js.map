{"version": 3, "file": "dating-detail.js", "sources": ["pages/publish/dating-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9kYXRpbmctZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"detail-container dating-detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">婚恋交友详情</view>\n      <view class=\"navbar-right\">\n        <!-- 占位 -->\n      </view>\n    </view>\n    \n    <!-- 隐藏的分享按钮，用于自动触发 -->\n    <button id=\"shareButton\" class=\"hidden-share-btn\" open-type=\"share\"></button>\n    \n    <!-- 隐藏的Canvas用于绘制海报 -->\n    <canvas canvas-id=\"posterCanvas\" class=\"poster-canvas\" style=\"width: 600px; height: 900px; position: fixed; top: -9999px;\"></canvas>\n    \n    <!-- 悬浮海报按钮 -->\n    <view class=\"float-poster-btn\" @click=\"generateShareImage\">\n      <image src=\"/static/images/tabbar/海报.png\" class=\"poster-icon\"></image>\n      <text class=\"poster-text\">海报</text>\n    </view>\n    \n    <view class=\"detail-wrapper dating-detail-wrapper\">\n      <!-- 个人信息卡片 -->\n      <view class=\"content-card personal-card\">\n        <view class=\"section-title\">个人信息</view>\n        <view class=\"personal-header\">\n          <view class=\"avatar-container\">\n            <image :src=\"datingData.images[0] || '/static/images/default-avatar.png'\" mode=\"aspectFill\" class=\"avatar-image\"></image>\n          </view>\n          <view class=\"personal-info\">\n            <text class=\"personal-name\">{{datingData.nickname}}</text>\n            <view class=\"personal-meta\">\n              <text class=\"meta-text\">{{datingData.age}}岁</text>\n              <text class=\"meta-text\">{{datingData.gender === 'male' ? '男' : '女'}}</text>\n              <text class=\"meta-text\">{{datingData.height}}cm</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 基本信息列表 -->\n        <view class=\"basic-info\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">学历</text>\n            <text class=\"info-value\">{{datingData.education}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">婚姻状况</text>\n            <text class=\"info-value\">{{datingData.maritalStatus}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">职业</text>\n            <text class=\"info-value\">{{datingData.occupation}}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">月收入</text>\n            <text class=\"info-value\">{{datingData.income}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 个人照片展示 -->\n      <view class=\"content-card photos-card\">\n        <view class=\"section-title\">个人照片</view>\n        <view class=\"photos-grid\">\n          <view class=\"photo-item\" v-for=\"(image, index) in datingData.images\" :key=\"index\" @click=\"previewImage(index)\">\n            <image :src=\"image\" mode=\"aspectFill\" class=\"photo-image\"></image>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 自我介绍 -->\n      <view class=\"content-card intro-card\">\n        <view class=\"section-title\">自我介绍</view>\n        <view class=\"intro-content\">\n          <rich-text :nodes=\"datingData.selfIntro\" class=\"intro-text\"></rich-text>\n        </view>\n      </view>\n      \n      <!-- 择偶要求 -->\n      <view class=\"content-card expectation-card\">\n        <view class=\"section-title\">择偶要求</view>\n        <view class=\"expectation-content\">\n          <rich-text :nodes=\"datingData.expectation\" class=\"expectation-text\"></rich-text>\n        </view>\n      </view>\n      \n      <!-- 兴趣爱好 -->\n      <view class=\"content-card interests-card\">\n        <view class=\"section-title\">兴趣爱好</view>\n        <view class=\"interests-tags\">\n          <view class=\"interest-tag\" v-for=\"(interest, index) in datingData.interests\" :key=\"index\">\n            <text class=\"interest-text\">{{interest}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 联系方式卡片 -->\n      <view class=\"content-card contact-card\">\n        <view class=\"section-title\">联系方式</view>\n        <view class=\"contact-content\">\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">联系人</text>\n            <text class=\"contact-value\">{{datingData.contact}}</text>\n          </view>\n          <view class=\"contact-item\">\n            <text class=\"contact-label\">电话</text>\n            <text class=\"contact-value contact-phone\" @click=\"callPhone\">{{datingData.phone}}</text>\n          </view>\n          <view class=\"contact-tips\">\n            <text class=\"tips-icon iconfont icon-info\"></text>\n            <text class=\"tips-text\">请说明在\"磁州生活网\"看到的信息</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 添加举报卡片 -->\n      <report-card :content-id=\"datingData.id\" content-type=\"dating\"></report-card>\n      \n      <!-- 红包区域 -->\n      <view class=\"content-card red-packet-card\" v-if=\"datingData.hasRedPacket\">\n        <view class=\"section-title\">红包福利</view>\n        <view class=\"red-packet-section\">\n          <view class=\"red-packet-container\" @click=\"openRedPacket\">\n            <view class=\"red-packet-blur-bg\"></view>\n            <view class=\"red-packet-content\">\n              <view class=\"red-packet-left\">\n                <image class=\"red-packet-icon\" src=\"/static/images/tabbar/抢红包.gif\"></image>\n              <view class=\"red-packet-info\">\n                <view class=\"red-packet-title\">\n                  {{datingData.redPacket.type === 'random' ? '随机金额红包' : '查看信息领红包'}}\n                </view>\n                <view class=\"red-packet-desc\">\n                  还剩{{datingData.redPacket.remain}}个，{{getRedPacketConditionText()}}\n                  </view>\n                </view>\n              </view>\n              <view class=\"red-packet-right\">\n                <view class=\"red-packet-amount\"><text class=\"prefix\">共</text> ¥{{datingData.redPacket.amount}}</view>\n                <view class=\"grab-btn\">立即领取</view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 相关推荐卡片 -->\n      <view class=\"content-card related-dating-card\">\n        <!-- 标题栏 -->\n        <view class=\"collapsible-header\">\n          <view class=\"section-title\">相关推荐</view>\n        </view>\n        \n        <!-- 内容区 -->\n        <view class=\"collapsible-content\">\n          <!-- 简洁的列表 -->\n          <view class=\"related-dating-list\">\n            <view class=\"related-dating-item\" \n                 v-for=\"(item, index) in relatedDating.slice(0, 3)\" \n                 :key=\"index\" \n                 @click=\"navigateToDatingDetail(item.id)\">\n              <view class=\"related-dating-left\">\n                <image :src=\"item.avatar\" mode=\"aspectFill\" class=\"related-avatar\"></image>\n              </view>\n              <view class=\"related-dating-info\">\n                <view class=\"related-dating-title\">\n                  <text class=\"related-name\">{{item.nickname}}</text>\n                  <text class=\"related-age\">{{item.age}}岁</text>\n                </view>\n                <view class=\"related-dating-meta\">\n                  <text class=\"related-meta-text\">{{item.education}}</text>\n                  <text class=\"related-meta-text\">{{item.occupation}}</text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"footer-action-bar\">\n      <view class=\"action-item collect-action\" @click=\"toggleCollect\">\n        <text class=\"action-icon\" :class=\"{'icon-collected': isCollected, 'icon-collect': !isCollected}\"></text>\n        <text class=\"action-text\">{{isCollected ? '已收藏' : '收藏'}}</text>\n      </view>\n      <view class=\"action-item share-action\" @click=\"shareToFriend\">\n        <text class=\"action-icon icon-share\"></text>\n        <text class=\"action-text\">分享</text>\n      </view>\n      <view class=\"action-item contact-action\" @click=\"contactPerson\">\n        <text class=\"action-text\">联系TA</text>\n      </view>\n    </view>\n    \n    <!-- 海报弹窗 -->\n    <view class=\"poster-modal\" v-if=\"showPosterModal\" @click=\"closePosterModal\">\n      <view class=\"poster-container\" @click.stop>\n        <view class=\"poster-header\">\n          <text class=\"poster-modal-title\">分享海报</text>\n          <text class=\"close-icon\" @click=\"closePosterModal\">×</text>\n        </view>\n        <view class=\"poster-image-container\">\n          <image :src=\"posterUrl\" mode=\"widthFix\" class=\"poster-preview\"></image>\n        </view>\n        <view class=\"poster-footer\">\n          <button class=\"poster-btn save-btn\" @click=\"savePoster\">保存到相册</button>\n          <button class=\"poster-btn share-btn\" open-type=\"share\">分享给朋友</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue';\nimport { onLoad, onShow, onHide, onUnload } from '@dcloudio/uni-app';\nimport { formatTime } from '../../utils/date.js';\nimport ReportCard from '@/components/ReportCard.vue';\n\n// --- 响应式状态 ---\nconst statusBarHeight = ref(20);\nconst datingId = ref('');\nconst isCollected = ref(false);\nconst showPosterModal = ref(false);\nconst posterUrl = ref('');\n\nconst datingData = reactive({\n        id: '1',\n        nickname: '阳光男孩',\n        gender: 'male',\n        age: 28,\n        height: 180,\n        education: '本科',\n        maritalStatus: '未婚',\n        occupation: '工程师',\n        income: '8000-12000',\n        selfIntro: '性格开朗，喜欢运动，热爱生活。工作稳定，有上进心，希望找一个志同道合的伴侣一起成长。',\n        expectation: '希望对方性格温柔，有一定的生活情趣，年龄25-30岁，身高160cm以上，学历大专以上。',\n        interests: ['旅游', '美食', '运动', '电影'],\n        images: [\n          '/static/images/default-avatar.png',\n          '/static/images/default-image.png',\n          '/static/images/default-image.png'\n        ],\n        contact: '张先生',\n        phone: '13800138000',\n        wechat: 'sunshine123',\n  publishTime: new Date().getTime() - 86400000,\n        hasRedPacket: true,\n        redPacket: {\n          type: 'random',\n    amount: 50,\n    remain: 15,\n    total: 30\n        }\n});\n\nconst relatedDating = reactive([\n        {\n          id: '2',\n          nickname: '甜心女孩',\n          age: 26,\n          avatar: '/static/images/default-avatar.png',\n          education: '研究生',\n          occupation: '教师'\n        },\n        {\n          id: '3',\n          nickname: '成熟稳重',\n          age: 32,\n          avatar: '/static/images/default-avatar.png',\n          education: '本科',\n          occupation: '金融'\n        },\n        {\n          id: '4',\n          nickname: '活力四射',\n          age: 25,\n          avatar: '/static/images/default-avatar.png',\n          education: '大专',\n          occupation: '设计师'\n        }\n]);\n\n// --- 方法 ---\n\nconst goBack = () => uni.navigateBack();\n\nconst previewImage = (index) => {\n      uni.previewImage({\n        current: index,\n    urls: datingData.images,\n      });\n};\n\nconst callPhone = () => {\n  uni.makePhoneCall({ phoneNumber: datingData.phone });\n};\n\nconst getRedPacketConditionText = () => \"查看详情可领取\";\n\nconst openRedPacket = () => {\n  uni.showToast({ title: '红包功能正在开发中', icon: 'none' });\n};\n\nconst navigateToDatingDetail = (id) => {\n  uni.navigateTo({ url: `/pages/publish/dating-detail?id=${id}` });\n};\n\nconst toggleCollect = () => {\n  isCollected.value = !isCollected.value;\n  uni.showToast({ title: isCollected.value ? '收藏成功' : '取消收藏' });\n};\n\nconst shareToFriend = () => {\n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 0,\n    title: `${datingData.nickname}的交友信息`,\n    summary: datingData.selfIntro,\n    imageUrl: datingData.images[0],\n  });\n};\n\nconst contactPerson = () => {\n  uni.showActionSheet({\n    itemList: ['拨打电话', '添加微信'],\n    success: (res) => {\n      if (res.tapIndex === 0) callPhone();\n      if (res.tapIndex === 1) {\n        uni.setClipboardData({\n          data: datingData.wechat,\n          success: () => uni.showToast({ title: '微信号已复制' }),\n        });\n      }\n    },\n  });\n};\n\nconst generateShareImage = () => {\n  uni.showLoading({ title: '海报生成中...' });\n  const ctx = uni.createCanvasContext('posterCanvas');\n    // ... (省略Canvas绘制逻辑)\n      setTimeout(() => {\n    posterUrl.value = '/static/images/default-image.png'; // 假设这是生成的海报\n    showPosterModal.value = true;\n        uni.hideLoading();\n      }, 1000);\n};\n\nconst closePosterModal = () => {\n  showPosterModal.value = false;\n};\n\nconst savePoster = () => {\n  uni.saveImageToPhotosAlbum({\n    filePath: posterUrl.value,\n    success: () => uni.showToast({ title: '保存成功' }),\n    fail: () => uni.showToast({ title: '保存失败', icon: 'error' }),\n  });\n};\n\n// --- 生命周期 ---\nonLoad((options) => {\n  datingId.value = options.id || '';\n  // fetchDatingData(datingId.value);\n});\n\nonMounted(() => {\n  const systemInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = systemInfo.statusBarHeight || 20;\n});\n</script>\n\n<style lang=\"scss\">\n.detail-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  background: linear-gradient(135deg, #FF6B95, #FF8E7F);\n  height: 88rpx;\n  padding-top: 44px; /* 状态栏高度 */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 149, 0.2);\n}\n\n.navbar-left {\n  position: absolute;\n  left: 30rpx;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.navbar-title {\n  color: #FFFFFF;\n  font-size: 36rpx;\n  font-weight: 600;\n}\n\n.navbar-right {\n  position: absolute;\n  right: 30rpx;\n}\n\n/* 详情包装器 */\n.detail-wrapper {\n  padding: 24rpx;\n  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */\n}\n\n/* 内容卡片通用样式 */\n.content-card {\n  background-color: #FFFFFF;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\n}\n\n/* 章节标题 */\n.section-title {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 600;\n  margin-bottom: 20rpx;\n  position: relative;\n  padding-left: 20rpx;\n}\n\n.section-title::before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 6rpx;\n  height: 32rpx;\n  width: 8rpx;\n  background: linear-gradient(to bottom, #FF6B95, #FF8E7F);\n  border-radius: 4rpx;\n}\n\n/* 个人信息卡片 */\n.personal-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.avatar-container {\n  width: 140rpx;\n  height: 140rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 30rpx;\n  border: 4rpx solid #FFF;\n  box-shadow: 0 6rpx 16rpx rgba(255, 107, 149, 0.2);\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n}\n\n.personal-info {\n  flex: 1;\n}\n\n.personal-name {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.personal-meta {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.meta-text {\n  font-size: 24rpx;\n  color: #666;\n  background-color: #f5f7fa;\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n  margin-right: 10rpx;\n  margin-bottom: 10rpx;\n}\n\n/* 基本信息列表 */\n.basic-info {\n  border-top: 1rpx solid #f0f0f0;\n  padding-top: 20rpx;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f9f9f9;\n}\n\n.info-label {\n  color: #666;\n  font-size: 28rpx;\n}\n\n.info-value {\n  color: #333;\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n/* 照片网格 */\n.photos-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -10rpx;\n}\n\n.photo-item {\n  width: calc(33.33% - 20rpx);\n  padding-bottom: calc(33.33% - 20rpx);\n  margin: 10rpx;\n  position: relative;\n  border-radius: 8rpx;\n  overflow: hidden;\n}\n\n.photo-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 自我介绍和择偶要求 */\n.intro-content, .expectation-content {\n  color: #333;\n  font-size: 28rpx;\n  line-height: 1.6;\n}\n\n/* 兴趣标签 */\n.interests-tags {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.interest-tag {\n  background: #f5f7fa;\n  color: #666;\n  font-size: 24rpx;\n  padding: 8rpx 20rpx;\n  border-radius: 20rpx;\n  margin-right: 16rpx;\n  margin-bottom: 16rpx;\n}\n\n/* 联系方式卡片 */\n.contact-content {\n  padding: 10rpx 0;\n}\n\n.contact-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #f9f9f9;\n}\n\n.contact-label {\n  color: #666;\n  font-size: 28rpx;\n}\n\n.contact-value {\n  color: #333;\n  font-size: 28rpx;\n}\n\n.contact-phone {\n  color: #FF6B95;\n}\n\n.contact-tips {\n  display: flex;\n  align-items: center;\n  margin-top: 20rpx;\n}\n\n.tips-icon {\n  color: #FF6B95;\n  font-size: 28rpx;\n  margin-right: 10rpx;\n}\n\n.tips-text {\n  color: #999;\n  font-size: 24rpx;\n}\n\n/* 红包卡片 */\n.red-packet-section {\n  padding: 10rpx 0;\n}\n\n.red-packet-container {\n  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);\n  border-radius: 12rpx;\n  padding: 4rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n.red-packet-blur-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url(\"../../../static/images/tabbar/红包背景.png\") no-repeat center/cover;\n  opacity: 0.1;\n}\n\n.red-packet-content {\n  background: #FFF;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.red-packet-left {\n  display: flex;\n  align-items: center;\n}\n\n.red-packet-icon {\n  width: 60rpx;\n  height: 60rpx;\n  margin-right: 16rpx;\n}\n\n.red-packet-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.red-packet-title {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #FF4D4F;\n  margin-bottom: 6rpx;\n}\n\n.red-packet-desc {\n  font-size: 22rpx;\n  color: #999;\n}\n\n.red-packet-right {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n\n.red-packet-amount {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #FF4D4F;\n  margin-bottom: 10rpx;\n}\n\n.prefix {\n  font-size: 22rpx;\n  font-weight: normal;\n}\n\n.grab-btn {\n  background: linear-gradient(135deg, #FF9B9B, #FF6B6B);\n  color: #FFF;\n  font-size: 24rpx;\n  padding: 6rpx 20rpx;\n  border-radius: 20rpx;\n}\n\n/* 相关推荐 */\n.related-dating-list {\n  padding: 10rpx 0;\n}\n\n.related-dating-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f9f9f9;\n}\n\n.related-dating-item:last-child {\n  border-bottom: none;\n}\n\n.related-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n}\n\n.related-dating-info {\n  flex: 1;\n}\n\n.related-dating-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 6rpx;\n}\n\n.related-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  margin-right: 10rpx;\n}\n\n.related-age {\n  font-size: 24rpx;\n  color: #FF6B95;\n  background: rgba(255, 107, 149, 0.1);\n  padding: 2rpx 10rpx;\n  border-radius: 10rpx;\n}\n\n.related-dating-meta {\n  display: flex;\n}\n\n.related-meta-text {\n  font-size: 24rpx;\n  color: #999;\n  margin-right: 16rpx;\n}\n\n/* 底部操作栏 */\n.footer-action-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background: #FFF;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  z-index: 90;\n}\n\n.action-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n.action-icon {\n  font-size: 40rpx;\n  color: #999;\n  margin-bottom: 6rpx;\n}\n\n.icon-collected {\n  color: #FF6B95;\n}\n\n.action-text {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.contact-action {\n  flex: 2;\n  background: linear-gradient(135deg, #FF6B95, #FF8E7F);\n  height: 70rpx;\n  border-radius: 35rpx;\n  margin: 0 20rpx;\n}\n\n.contact-action .action-text {\n  color: #FFF;\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n/* 海报弹窗 */\n.poster-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n}\n\n.poster-container {\n  width: 80%;\n  background: #FFF;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.poster-header {\n  padding: 20rpx;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.poster-modal-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.close-icon {\n  font-size: 40rpx;\n  color: #999;\n  padding: 10rpx;\n}\n\n.poster-image-container {\n  padding: 30rpx;\n  display: flex;\n  justify-content: center;\n}\n\n.poster-preview {\n  width: 100%;\n  border-radius: 8rpx;\n}\n\n.poster-footer {\n  display: flex;\n  padding: 20rpx;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.poster-btn {\n  flex: 1;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  border-radius: 40rpx;\n  margin: 0 10rpx;\n  font-size: 28rpx;\n}\n\n.save-btn {\n  background: #f5f7fa;\n  color: #666;\n}\n\n.share-btn {\n  background: linear-gradient(135deg, #FF6B95, #FF8E7F);\n  color: #FFF;\n}\n\n/* 悬浮海报按钮 */\n.float-poster-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 140rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n  z-index: 80;\n}\n\n.poster-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 4rpx;\n}\n\n.poster-text {\n  font-size: 20rpx;\n  color: #666;\n}\n\n/* 隐藏的分享按钮 */\n.hidden-share-btn {\n  position: absolute;\n  opacity: 0;\n  width: 0;\n  height: 0;\n}\n\n/* 海报Canvas */\n.poster-canvas {\n  position: fixed;\n  top: -9999px;\n  left: -9999px;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/dating-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "onLoad", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AA6NA,MAAM,aAAa,MAAW;;;;AAG9B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,WAAWA,cAAAA,IAAI,EAAE;AACvB,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAC7B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,YAAYA,cAAAA,IAAI,EAAE;AAExB,UAAM,aAAaC,cAAAA,SAAS;AAAA,MACpB,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAClC,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACd,cAAa,oBAAI,QAAO,QAAS,IAAG;AAAA,MAC9B,cAAc;AAAA,MACd,WAAW;AAAA,QACT,MAAM;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,MACF;AAAA,IACT,CAAC;AAED,UAAM,gBAAgBA,cAAAA,SAAS;AAAA,MACvB;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,YAAY;AAAA,MACb;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,YAAY;AAAA,MACb;AAAA,IACT,CAAC;AAID,UAAM,SAAS,MAAMC,oBAAI;AAEzB,UAAM,eAAe,CAAC,UAAU;AAC1BA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACb,MAAM,WAAW;AAAA,MACrB,CAAO;AAAA,IACP;AAEA,UAAM,YAAY,MAAM;AACtBA,oBAAG,MAAC,cAAc,EAAE,aAAa,WAAW,MAAO,CAAA;AAAA,IACrD;AAEA,UAAM,4BAA4B,MAAM;AAExC,UAAM,gBAAgB,MAAM;AAC1BA,oBAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,OAAM,CAAE;AAAA,IACpD;AAEA,UAAM,yBAAyB,CAAC,OAAO;AACrCA,oBAAG,MAAC,WAAW,EAAE,KAAK,mCAAmC,EAAE,GAAE,CAAE;AAAA,IACjE;AAEA,UAAM,gBAAgB,MAAM;AAC1B,kBAAY,QAAQ,CAAC,YAAY;AACjCA,0BAAI,UAAU,EAAE,OAAO,YAAY,QAAQ,SAAS,OAAM,CAAE;AAAA,IAC9D;AAEA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO,GAAG,WAAW,QAAQ;AAAA,QAC7B,SAAS,WAAW;AAAA,QACpB,UAAU,WAAW,OAAO,CAAC;AAAA,MACjC,CAAG;AAAA,IACH;AAEA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,MAAM;AAAA,QACzB,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,aAAa;AAAG,sBAAS;AACjC,cAAI,IAAI,aAAa,GAAG;AACtBA,0BAAAA,MAAI,iBAAiB;AAAA,cACnB,MAAM,WAAW;AAAA,cACjB,SAAS,MAAMA,cAAAA,MAAI,UAAU,EAAE,OAAO,SAAQ,CAAE;AAAA,YAC1D,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,YAAY,EAAE,OAAO,WAAY,CAAA;AACzBA,oBAAG,MAAC,oBAAoB,cAAc;AAE9C,iBAAW,MAAM;AACnB,kBAAU,QAAQ;AAClB,wBAAgB,QAAQ;AACpBA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAI;AAAA,IACb;AAEA,UAAM,mBAAmB,MAAM;AAC7B,sBAAgB,QAAQ;AAAA,IAC1B;AAEA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAI,uBAAuB;AAAA,QACzB,UAAU,UAAU;AAAA,QACpB,SAAS,MAAMA,cAAAA,MAAI,UAAU,EAAE,OAAO,OAAM,CAAE;AAAA,QAC9C,MAAM,MAAMA,cAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,SAAS;AAAA,MAC9D,CAAG;AAAA,IACH;AAGAC,kBAAM,OAAC,CAAC,YAAY;AAClB,eAAS,QAAQ,QAAQ,MAAM;AAAA,IAEjC,CAAC;AAEDC,kBAAAA,UAAU,MAAM;AACd,YAAM,aAAaF,oBAAI;AACvB,sBAAgB,QAAQ,WAAW,mBAAmB;AAAA,IACxD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvXD,GAAG,WAAWG,SAAe;"}