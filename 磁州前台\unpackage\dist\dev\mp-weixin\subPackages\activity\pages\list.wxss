/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-88f4bae4, html.data-v-88f4bae4, #app.data-v-88f4bae4, .index-container.data-v-88f4bae4 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-container.data-v-88f4bae4 {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;
}

/* 自定义导航栏 */
.custom-navbar.data-v-88f4bae4 {
  width: 100%;
  background: linear-gradient(to right, #3a8afe, #6f7bfd);
  z-index: 100;
  position: relative;
}
.status-bar.data-v-88f4bae4 {
  width: 100%;
}
.navbar-content.data-v-88f4bae4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 12px;
}
.navbar-left.data-v-88f4bae4 {
  display: flex;
  align-items: center;
}
.back-button.data-v-88f4bae4 {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-88f4bae4 {
  width: 20px;
  height: 20px;
}
.navbar-title.data-v-88f4bae4 {
  flex: 1;
  text-align: center;
}
.title-text.data-v-88f4bae4 {
  font-size: 18px;
  font-weight: 800;
  color: white;
}
.navbar-right.data-v-88f4bae4 {
  display: flex;
  align-items: center;
}
.filter-button.data-v-88f4bae4 {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 状态筛选 */
.filter-tabs.data-v-88f4bae4 {
  display: flex;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  z-index: 10;
  margin-bottom: 16px;
}
.tab-item.data-v-88f4bae4 {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  position: relative;
}
.tab-text.data-v-88f4bae4 {
  font-size: 14px;
  color: #666;
}
.tab-item.active .tab-text.data-v-88f4bae4 {
  color: #3a8afe;
  font-weight: 500;
}
.tab-item.active.data-v-88f4bae4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #3a8afe;
  border-radius: 3px;
}

/* 活动列表 */
.activity-list.data-v-88f4bae4 {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
}
.empty-state.data-v-88f4bae4 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 32px;
}
.empty-icon.data-v-88f4bae4 {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}
.empty-text.data-v-88f4bae4 {
  font-size: 16px;
  color: #999;
  text-align: center;
}

/* 活动卡片 */
.activity-card.data-v-88f4bae4 {
  background-color: white;
  border-radius: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  transition: transform 0.2s, box-shadow 0.2s;
}
.activity-card.data-v-88f4bae4:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.card-image.data-v-88f4bae4 {
  width: 100%;
  height: 180px;
  background-size: cover;
  background-position: center;
}
.card-content.data-v-88f4bae4 {
  padding: 18px;
}
.card-title.data-v-88f4bae4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
  text-align: center;
}
.card-info.data-v-88f4bae4 {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 12px;
}
.info-item.data-v-88f4bae4 {
  display: flex;
  align-items: center;
  margin: 0 10px 6px;
}
.info-icon.data-v-88f4bae4 {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.info-text.data-v-88f4bae4 {
  font-size: 13px;
  color: #666;
}
.card-status.data-v-88f4bae4 {
  display: block;
  padding: 6px 0;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  width: 100px;
  margin: 0 auto 12px;
}
.status-ongoing.data-v-88f4bae4 {
  background-color: rgba(58, 138, 254, 0.1);
  color: #3a8afe;
}
.status-upcoming.data-v-88f4bae4 {
  background-color: rgba(111, 123, 253, 0.1);
  color: #6f7bfd;
}
.status-ended.data-v-88f4bae4 {
  background-color: rgba(128, 128, 128, 0.1);
  color: #808080;
}
.card-actions.data-v-88f4bae4 {
  display: flex;
  justify-content: space-around;
  margin-top: 12px;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}
.action-button.data-v-88f4bae4 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 20px;
  border-radius: 16px;
}
.action-button.data-v-88f4bae4:active {
  background-color: rgba(0, 0, 0, 0.05);
}
.action-icon.data-v-88f4bae4 {
  width: 22px;
  height: 22px;
  margin-bottom: 6px;
}
.action-text.data-v-88f4bae4 {
  font-size: 12px;
  color: #666;
}

/* 加载状态 */
.loading-container.data-v-88f4bae4 {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}
.loading-text.data-v-88f4bae4 {
  font-size: 14px;
  color: #999;
}

/* 没有更多数据提示 */
.no-more.data-v-88f4bae4 {
  text-align: center;
  padding: 16px 0;
  font-size: 14px;
  color: #999;
}

/* 隐藏滚动条 */
.data-v-88f4bae4::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}