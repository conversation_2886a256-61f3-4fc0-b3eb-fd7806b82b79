"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      packageInfo: {
        name: "",
        description: "",
        category: "",
        categoryIndex: 0,
        groupSize: 2,
        startDate: "",
        endDate: ""
      },
      categoryOptions: ["餐饮美食", "休闲娱乐", "美容美发", "生活服务", "其他"]
    };
  },
  onLoad() {
    try {
      const packageType = common_vendor.index.getStorageSync("packageType");
      const savedInfo = common_vendor.index.getStorageSync("packageInfo");
      if (packageType) {
        common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info.vue:114", "获取到套餐类型:", packageType);
      }
      if (savedInfo) {
        this.packageInfo = JSON.parse(savedInfo);
      }
    } catch (e) {
      common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info.vue:121", "读取本地存储失败:", e);
    }
  },
  methods: {
    goBack() {
      this.saveData();
      common_vendor.index.navigateBack();
    },
    nextStep() {
      if (!this.packageInfo.name) {
        common_vendor.index.showToast({
          title: "请输入套餐名称",
          icon: "none"
        });
        return;
      }
      if (!this.packageInfo.category) {
        common_vendor.index.showToast({
          title: "请选择套餐分类",
          icon: "none"
        });
        return;
      }
      if (!this.packageInfo.startDate || !this.packageInfo.endDate) {
        common_vendor.index.showToast({
          title: "请选择活动有效期",
          icon: "none"
        });
        return;
      }
      this.saveData();
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price"
      });
    },
    saveData() {
      try {
        common_vendor.index.setStorageSync("packageInfo", JSON.stringify(this.packageInfo));
      } catch (e) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info.vue:167", "保存数据失败:", e);
      }
    },
    onCategoryChange(e) {
      const index = e.detail.value;
      this.packageInfo.categoryIndex = index;
      this.packageInfo.category = this.categoryOptions[index];
    },
    onStartDateChange(e) {
      this.packageInfo.startDate = e.detail.value;
    },
    onEndDateChange(e) {
      this.packageInfo.endDate = e.detail.value;
    },
    decrementGroupSize() {
      if (this.packageInfo.groupSize > 2) {
        this.packageInfo.groupSize--;
      }
    },
    incrementGroupSize() {
      if (this.packageInfo.groupSize < 100) {
        this.packageInfo.groupSize++;
      }
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "帮助信息",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: $data.packageInfo.name,
    d: common_vendor.o(($event) => $data.packageInfo.name = $event.detail.value),
    e: common_vendor.t($data.packageInfo.name.length),
    f: $data.packageInfo.description,
    g: common_vendor.o(($event) => $data.packageInfo.description = $event.detail.value),
    h: common_vendor.t($data.packageInfo.description.length),
    i: $data.packageInfo.category
  }, $data.packageInfo.category ? {
    j: common_vendor.t($data.categoryOptions[$data.packageInfo.categoryIndex])
  } : {}, {
    k: $data.categoryOptions,
    l: common_vendor.o((...args) => $options.onCategoryChange && $options.onCategoryChange(...args)),
    m: common_vendor.o((...args) => $options.decrementGroupSize && $options.decrementGroupSize(...args)),
    n: $data.packageInfo.groupSize,
    o: common_vendor.o(($event) => $data.packageInfo.groupSize = $event.detail.value),
    p: common_vendor.o((...args) => $options.incrementGroupSize && $options.incrementGroupSize(...args)),
    q: $data.packageInfo.startDate
  }, $data.packageInfo.startDate ? {
    r: common_vendor.t($data.packageInfo.startDate)
  } : {}, {
    s: $data.packageInfo.startDate,
    t: common_vendor.o((...args) => $options.onStartDateChange && $options.onStartDateChange(...args)),
    v: $data.packageInfo.endDate
  }, $data.packageInfo.endDate ? {
    w: common_vendor.t($data.packageInfo.endDate)
  } : {}, {
    x: $data.packageInfo.endDate,
    y: common_vendor.o((...args) => $options.onEndDateChange && $options.onEndDateChange(...args)),
    z: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    A: common_vendor.o((...args) => $options.nextStep && $options.nextStep(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info.js.map
