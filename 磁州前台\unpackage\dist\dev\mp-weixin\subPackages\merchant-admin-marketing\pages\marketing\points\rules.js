"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
const _sfc_main = {
  __name: "rules",
  setup(__props) {
    const isEditing = common_vendor.ref(false);
    const pointsRules = common_vendor.ref([
      {
        id: 1,
        type: "purchase",
        title: "商品购买",
        description: "每消费1元获得1积分",
        points: 1
      },
      {
        id: 2,
        type: "checkin",
        title: "每日签到",
        description: "每日签到获得积分奖励",
        points: 10
      },
      {
        id: 3,
        type: "share",
        title: "分享商品",
        description: "分享商品到社交媒体获得积分",
        points: 5
      }
    ]);
    const startEditing = () => {
      isEditing.value = true;
    };
    const saveRules = () => {
      isEditing.value = false;
      common_vendor.index.showToast({
        title: "保存成功",
        icon: "success"
      });
    };
    const showModal = common_vendor.ref(false);
    const typeOptions = [
      { value: "purchase", label: "商品购买" },
      { value: "checkin", label: "每日签到" },
      { value: "share", label: "分享商品" },
      { value: "review", label: "评价商品" },
      { value: "register", label: "注册会员" },
      { value: "birthday", label: "生日特权" }
    ];
    const typeIndex = common_vendor.ref(0);
    const newRule = common_vendor.reactive({
      title: "",
      description: "",
      points: "",
      type: "purchase"
    });
    const showAddRuleModal = () => {
      showModal.value = true;
      typeIndex.value = 0;
      newRule.title = "";
      newRule.description = "";
      newRule.points = "";
      newRule.type = "purchase";
    };
    const hideModal = () => {
      showModal.value = false;
    };
    const typeChange = (e) => {
      typeIndex.value = e.detail.value;
      newRule.type = typeOptions[typeIndex.value].value;
    };
    const addRule = () => {
      if (!newRule.title) {
        common_vendor.index.showToast({
          title: "请输入规则名称",
          icon: "none"
        });
        return;
      }
      if (!newRule.description) {
        common_vendor.index.showToast({
          title: "请输入规则描述",
          icon: "none"
        });
        return;
      }
      if (!newRule.points) {
        common_vendor.index.showToast({
          title: "请输入积分值",
          icon: "none"
        });
        return;
      }
      pointsRules.value.push({
        id: Date.now(),
        type: newRule.type,
        title: newRule.title,
        description: newRule.description,
        points: newRule.points
      });
      hideModal();
      common_vendor.index.showToast({
        title: "添加成功",
        icon: "success"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M15 18L9 12L15 6",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        c: common_vendor.o(goBack),
        d: isEditing.value
      }, isEditing.value ? {
        e: common_vendor.o(saveRules)
      } : {
        f: common_vendor.o(startEditing)
      }, {
        g: common_vendor.f(pointsRules.value, (rule, index, i0) => {
          return common_vendor.e({
            a: common_vendor.n(rule.type),
            b: common_vendor.t(rule.title),
            c: common_vendor.t(rule.description)
          }, !isEditing.value ? {
            d: common_vendor.t(rule.points)
          } : {
            e: rule.points,
            f: common_vendor.o(($event) => rule.points = $event.detail.value, index)
          }, {
            g: index
          });
        }),
        h: !isEditing.value,
        i: isEditing.value
      }, isEditing.value ? {
        j: common_vendor.o(showAddRuleModal)
      } : {}, {
        k: showModal.value
      }, showModal.value ? {
        l: common_vendor.o(hideModal),
        m: common_vendor.o(hideModal),
        n: common_vendor.t(typeOptions[typeIndex.value].label),
        o: common_vendor.o(typeChange),
        p: typeIndex.value,
        q: typeOptions,
        r: newRule.title,
        s: common_vendor.o(($event) => newRule.title = $event.detail.value),
        t: newRule.description,
        v: common_vendor.o(($event) => newRule.description = $event.detail.value),
        w: newRule.points,
        x: common_vendor.o(($event) => newRule.points = $event.detail.value),
        y: common_vendor.o(hideModal),
        z: common_vendor.o(addRule)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/points/rules.js.map
