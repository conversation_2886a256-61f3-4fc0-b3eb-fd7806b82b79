/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-13a58514, html.data-v-13a58514, #app.data-v-13a58514, .index-container.data-v-13a58514 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
page.data-v-13a58514 {
  background-color: #F0F2F5;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.points-container.data-v-13a58514 {
  min-height: 100vh;
  background: #f8f8f8;
  position: relative;
  padding-bottom: 24px;
}

/* 导航栏样式 */
.status-bar.data-v-13a58514 {
  background: #1677FF;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}
.navbar.data-v-13a58514 {
  position: fixed;
  left: 0;
  right: 0;
  height: 44px;
  background: #1677FF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.04);
  border-bottom: 1px solid #f1f1f1;
  z-index: 100;
}
.navbar-left.data-v-13a58514, .navbar-right.data-v-13a58514 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-13a58514 {
  width: 24px;
  height: 24px;
  display: block;
  background: none;
  border-radius: 0;
  margin: 0 auto;
}
.navbar-title.data-v-13a58514 {
  font-size: 18px;
  font-weight: 700;
  color: #fff;
  letter-spacing: 0.5px;
}

/* 内容区域 */
.content-area.data-v-13a58514 {
  padding-top: 88px;
  padding-bottom: 30rpx;
}

/* 积分概览卡片 */
.points-overview-card.data-v-13a58514 {
  background: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: 20rpx 24rpx 30rpx;
  padding: 24rpx;
  position: relative;
  overflow: hidden;
}
.points-overview-card.data-v-13a58514::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, #1677FF, #06B6D4);
}
.points-balance-section.data-v-13a58514 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
}
.points-title.data-v-13a58514 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.points-balance.data-v-13a58514 {
  font-size: 60rpx;
  font-weight: bold;
  color: #1677FF;
  margin-bottom: 16rpx;
  font-family: "DIN Condensed", Arial, sans-serif;
  background: linear-gradient(90deg, #1677FF, #06B6D4);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  letter-spacing: 1rpx;
}
.points-balance-divider.data-v-13a58514 {
  width: 30%;
  height: 2rpx;
  background: #f0f0f0;
  margin-bottom: 16rpx;
}
.points-actions.data-v-13a58514 {
  display: flex;
  width: 100%;
  justify-content: center;
  gap: 30rpx;
}
.action-btn.data-v-13a58514 {
  padding: 10rpx 24rpx;
  font-size: 24rpx;
  color: #666;
  background: #F5F5F5;
  border-radius: 30rpx;
}
.action-btn.primary.data-v-13a58514 {
  color: #fff;
  background: #1677FF;
}

/* 签到模块 */
.sign-in-module.data-v-13a58514 {
  background: #FFFFFF;
  border-radius: 16rpx;
  margin: 0 24rpx 30rpx;
  padding: 30rpx 24rpx;
  position: relative;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.rule-float-btn.data-v-13a58514 {
  position: absolute;
  top: 18px;
  right: 18px;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #fafdff 60%, #eaf3ff 100%);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  cursor: pointer;
  transition: box-shadow 0.2s;
}
.rule-float-btn.data-v-13a58514:active {
  box-shadow: 0 4px 16px rgba(22, 119, 255, 0.12);
}
.rules-icon.data-v-13a58514 {
  width: 18px;
  height: 18px;
}
.module-header.data-v-13a58514 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 0 12px;
}
.module-title-container.data-v-13a58514 {
  display: flex;
  flex-direction: column;
}
.module-title.data-v-13a58514 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.module-subtitle.data-v-13a58514 {
  color: #999;
  font-size: 12px;
  margin-top: 2px;
}
.calendar-container.data-v-13a58514 {
  background: #f8f8f8;
  border-radius: 12px;
  margin: 12px 12px 0 12px;
  padding: 10px 0 6px 0;
}
.calendar-week-header.data-v-13a58514 {
  display: flex;
  justify-content: space-around;
  margin-bottom: 10px;
}
.week-day.data-v-13a58514 {
  font-size: 13px;
  color: #999;
  width: 38px;
  text-align: center;
}
.calendar-days.data-v-13a58514 {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
}
.day-item.data-v-13a58514 {
  background: #fff;
  border-radius: 8px;
  margin: 2px 0;
  color: #333;
  font-size: 15px;
  box-shadow: 0 1px 3px rgba(22, 119, 255, 0.04);
  transition: background 0.2s;
  width: 38px;
  height: 38px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
}
.day-item.signed.data-v-13a58514 {
  background: #e6f0ff;
  color: #1677FF;
}
.day-item.today.data-v-13a58514 {
  border: 2px solid #1677FF;
}
.day-number.data-v-13a58514 {
  font-size: 13px;
  color: #333;
  margin-bottom: 2px;
  line-height: 1;
}
.day-status.data-v-13a58514,
.day-status.sign-text.data-v-13a58514 {
  margin: 0;
}
.day-status.sign-text.data-v-13a58514 {
  background: none;
  border-radius: 0;
  padding: 0;
  font-size: 0;
  min-width: 0;
  height: auto;
  box-shadow: none;
  border: none;
}
.sign-btn-container.data-v-13a58514 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16px;
}
.sign-in-btn.data-v-13a58514 {
  background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
  color: #fff;
  border-radius: 18px;
  font-size: 16px;
  font-weight: 600;
  padding: 8px 0;
  margin: 10px 0 0 0;
  width: 80%;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.08);
  border: none;
}
.sign-in-btn.signed-today.data-v-13a58514 {
  background: #f1f1f1;
  color: #999;
}
.sign-in-btn.data-v-13a58514:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.sign-in-tip.data-v-13a58514 {
  color: #999;
  font-size: 13px;
  margin-top: 6px;
}

/* 排行榜按钮新样式 */
.rank-btn.data-v-13a58514 {
  display: flex;
  align-items: center;
  background: transparent;
  border-radius: 16px;
  padding: 6px 12px;
  gap: 4px;
}
.rank-btn-text.data-v-13a58514 {
  color: #1677FF;
  font-size: 13px;
  font-weight: 500;
}
.rank-btn-icon.data-v-13a58514 {
  width: 16px;
  height: 16px;
}

/* 每日任务模块 */
.daily-tasks-module.data-v-13a58514 {
  background: #FFFFFF;
  border-radius: 16rpx;
  margin: 0 24rpx 30rpx;
  padding: 30rpx 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.task-list.data-v-13a58514 {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.task-item.data-v-13a58514 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(22, 119, 255, 0.04);
  border: 1px solid #f1f1f1;
  transition: all 0.3s;
}
.task-item.data-v-13a58514:active {
  transform: scale(0.98);
}
.task-info.data-v-13a58514 {
  display: flex;
  align-items: center;
  gap: 10px;
}
.task-icon-wrap.data-v-13a58514 {
  width: 34px;
  height: 34px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e6f0ff 0%, #f8f8f8 100%);
  box-shadow: 0 2px 6px rgba(22, 119, 255, 0.04);
}
.task-icon.data-v-13a58514 {
  width: 20px;
  height: 20px;
}
.task-detail.data-v-13a58514 {
  display: flex;
  flex-direction: column;
}
.task-name.data-v-13a58514 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}
.task-desc.data-v-13a58514 {
  font-size: 12px;
  color: #999;
}
.task-status.data-v-13a58514 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6px;
}
.task-points.data-v-13a58514 {
  font-size: 14px;
  font-weight: 700;
  color: #1677FF;
}
.task-btn.data-v-13a58514 {
  min-width: 72px;
  height: 28px;
  background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
  color: #fff;
  font-size: 12px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(22, 119, 255, 0.08);
  font-weight: 600;
  border: none;
}
.task-btn.task-completed.data-v-13a58514 {
  background: #f1f1f1;
  color: #999;
  box-shadow: none;
}
.task-btn.task-in-progress.data-v-13a58514 {
  background: linear-gradient(90deg, #ffb300 0%, #ffec80 100%);
  color: #fff;
}
.progress-bar.data-v-13a58514 {
  width: 72px;
  height: 4px;
  background: #f1f1f1;
  border-radius: 2px;
  overflow: hidden;
}
.progress-fill.data-v-13a58514 {
  height: 100%;
  background: linear-gradient(90deg, #1677FF 0%, #63b3ed 100%);
  border-radius: 2px;
  transition: width 0.3s;
}

/* 任务按钮样式 */
.task-btn.task-in-progress + .progress-bar.data-v-13a58514 {
  display: block;
}

/* 全新的积分规则弹窗样式 */
.popup-mask.data-v-13a58514 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.25);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  z-index: 1000;
}
.new-rules-popup.data-v-13a58514 {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 580rpx;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 6px 24px rgba(22, 119, 255, 0.08);
  z-index: 1001;
  animation: popupIn 0.3s;
  overflow: hidden;
}
.new-rules-popup.data-v-13a58514::before {
  content: "";
  display: block;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
}
.new-rules-header.data-v-13a58514 {
  padding: 18px 18px 14px;
  border-bottom: 1px solid #f1f1f1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.new-rules-title.data-v-13a58514 {
  font-size: 17px;
  font-weight: 700;
  color: #333;
}
.new-rules-close.data-v-13a58514 {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: #999;
  border-radius: 50%;
  background: #f8f8f8;
}
.new-rules-content.data-v-13a58514 {
  padding: 18px;
  max-height: 60vh;
  overflow-y: auto;
}
.new-rule-item.data-v-13a58514 {
  margin-bottom: 16px;
}
.new-rule-item.data-v-13a58514:last-child {
  margin-bottom: 0;
}
.new-rule-title.data-v-13a58514 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}
.rule-badge.data-v-13a58514 {
  width: 6px;
  height: 6px;
  background: #1677FF;
  border-radius: 50%;
}
.new-rule-title text.data-v-13a58514 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
.new-rule-desc.data-v-13a58514 {
  font-size: 13px;
  color: #999;
  line-height: 1.6;
  padding-left: 14px;
}

/* 添加总积分显示样式 */
.task-item:first-child .task-points.data-v-13a58514 {
  position: relative;
  display: flex;
  align-items: center;
}
.task-item:first-child .task-points.data-v-13a58514::after {
  content: "(共+10)";
  font-size: 10px;
  color: #1677FF;
  font-weight: normal;
  position: static;
  margin-left: 4px;
  background-color: rgba(22, 119, 255, 0.08);
  padding: 1px 4px;
  border-radius: 8px;
}
.task-item:nth-child(2) .task-points.data-v-13a58514 {
  position: relative;
  display: flex;
  align-items: center;
}
.task-item:nth-child(2) .task-points.data-v-13a58514::after {
  content: "(共+5)";
  font-size: 10px;
  color: #1677FF;
  font-weight: normal;
  position: static;
  margin-left: 4px;
  background-color: rgba(22, 119, 255, 0.08);
  padding: 1px 4px;
  border-radius: 8px;
}
.task-item:nth-child(3) .task-points.data-v-13a58514 {
  position: relative;
  display: flex;
  align-items: center;
}
.task-item:nth-child(3) .task-points.data-v-13a58514::after {
  content: "(共+5)";
  font-size: 10px;
  color: #1677FF;
  font-weight: normal;
  position: static;
  margin-left: 4px;
  background-color: rgba(22, 119, 255, 0.08);
  padding: 1px 4px;
  border-radius: 8px;
}
.task-item:nth-child(4) .task-points.data-v-13a58514 {
  position: relative;
  display: flex;
  align-items: center;
}
.task-item:nth-child(4) .task-points.data-v-13a58514::after {
  content: "(共+5)";
  font-size: 10px;
  color: #1677FF;
  font-weight: normal;
  position: static;
  margin-left: 4px;
  background-color: rgba(22, 119, 255, 0.08);
  padding: 1px 4px;
  border-radius: 8px;
}
.task-item:nth-child(5) .task-points.data-v-13a58514 {
  position: relative;
  display: flex;
  align-items: center;
}
.task-item:nth-child(5) .task-points.data-v-13a58514::after {
  content: "";
  font-size: 10px;
  color: #1677FF;
  font-weight: normal;
  position: static;
  margin-left: 0;
  background-color: transparent;
  padding: 0;
  border-radius: 8px;
}