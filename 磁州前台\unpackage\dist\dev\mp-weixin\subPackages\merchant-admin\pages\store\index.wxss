/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.store-entry-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}
.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}
.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}
.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}
.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon, .more-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}
.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}
.bg-circle-2 {
  top: 10%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}
.bg-circle-3 {
  bottom: 5%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 店铺信息卡片样式 */
.store-info-card {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.store-header {
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}
.store-avatar-wrapper {
  position: relative;
}
.store-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  border: 4rpx solid #FFFFFF;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}
.edit-avatar-btn {
  position: absolute;
  right: -10rpx;
  bottom: -10rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: #0A84FF;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.edit-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}
.store-info {
  flex: 1;
  padding: 0 20rpx;
}
.store-name-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.store-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 10rpx;
}
.store-badge {
  padding: 4rpx 12rpx;
  background-color: #34C759;
  color: #FFFFFF;
  font-size: 20rpx;
  border-radius: 10rpx;
}
.store-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  height: 34rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.store-stats {
  display: flex;
  align-items: center;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}
.stat-label {
  font-size: 20rpx;
  color: #999;
}
.stat-divider {
  width: 2rpx;
  height: 30rpx;
  background-color: #E5E5EA;
  margin: 0 30rpx;
}
.preview-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  background-color: #0A84FF;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
}
.store-status-bar {
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #F2F2F7;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}
.status-indicator {
  display: flex;
  align-items: center;
}
.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #8E8E93;
  margin-right: 10rpx;
}
.status-dot.active {
  background-color: #34C759;
}

/* 内容滚动区域 */
.content-scroll {
  padding: 20rpx 30rpx;
  padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 120rpx);
  height: calc(100vh - 280rpx - var(--status-bar-height, 20px));
}

/* 功能区块样式 */
.function-section {
  margin-bottom: 40rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.section-header {
  margin-bottom: 20rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.section-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}
.function-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.function-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}
.function-item:active {
  transform: scale(0.95);
  opacity: 0.9;
}
.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}
.icon-wrapper::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  border-radius: 50%;
  z-index: 1;
}
.function-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
  z-index: 2;
}
.function-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}
.function-desc {
  font-size: 20rpx;
  color: #999;
  text-align: center;
  padding: 0 10rpx;
  height: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 图标背景颜色 */
.logo-bg {
  background: linear-gradient(135deg, #1976D2, #64B5F6);
}
.operation-bg {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}
.location-bg {
  background: linear-gradient(135deg, #38ADAE, #30CE9B);
}
.category-bg {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
}
.product-bg {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}
.service-bg {
  background: linear-gradient(135deg, #FF6B6B, #F04172);
}
.album-bg {
  background: linear-gradient(135deg, #FDEB71, #F8D800);
}
.video-bg {
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
}
.culture-bg {
  background: linear-gradient(135deg, #795548, #A1887F);
}
.verify-bg {
  background: linear-gradient(135deg, #38ADAE, #30CE9B);
}
.qualification-bg {
  background: linear-gradient(135deg, #FF5858, #FF0000);
}
.remind-bg {
  background: linear-gradient(135deg, #4481EB, #04BEFE);
}
.activity-bg {
  background: linear-gradient(135deg, #FF6B6B, #F04172);
}
.data-bg {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
.store-entry-container {
    background-color: #1C1C1E;
}
.store-info-card,
.function-section,
.function-item {
    background-color: #2C2C2E;
}
.store-name,
.section-title,
.function-name,
.stat-value {
    color: #FFFFFF;
}
.store-desc,
.section-subtitle,
.function-desc,
.stat-label,
.operation-hours,
.status-text {
    color: #8E8E93;
}
.store-status-bar {
    border-top-color: #3A3A3C;
}
}
/* 底部导航栏样式 */
.tab-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 10px;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #FFFFFF;
  border-top: 1px solid var(--border-light, #F0F0F0);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  flex-shrink: 0;
}
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  transition: all 0.2s ease;
  padding: 8px 0;
  margin: 0 8px;
}
.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 3px;
  transition: all 0.25s cubic-bezier(0.3, 0.7, 0.4, 1.5);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 22px;
  opacity: 0.7;
}
.tab-text {
  font-size: 10px;
  color: var(--text-tertiary, #999999);
  transition: color 0.2s ease, transform 0.25s ease;
  transform: scale(0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 0 2px;
}
.active-indicator {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 16px;
  height: 3px;
  border-radius: 0 0 4px 4px;
  background: linear-gradient(90deg, var(--brand-primary, #0A84FF), var(--accent-purple, #5E5CE6));
}
.tab-item.active .tab-icon {
  transform: translateY(-2px);
  opacity: 1;
}
.tab-item.active .tab-text {
  color: var(--brand-primary, #0A84FF);
  font-weight: 500;
  transform: scale(1);
}

/* 导航图标样式 */
.tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}
.tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}
.tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'/%3E%3C/svg%3E");
}
.tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}
.tab-icon.customers {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z'/%3E%3C/svg%3E");
}
.tab-icon.analytics {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-5h2v5zm4 0h-2v-3h2v3zm0-5h-2V7h2v2zm4 5h-2V7h2v10z'/%3E%3C/svg%3E");
}
.tab-icon.settings {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E");
}
.tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}
@media screen and (max-width: 375px) {
  /* 在较小屏幕上优化导航栏 */
.tab-text {
    font-size: 9px;
}
.tab-icon {
    margin-bottom: 2px;
    background-size: 20px;
}
}
/* 在较宽屏幕上优化导航栏，确保元素不会挤压 */
@media screen and (min-width: 400px) {
.tab-bar {
    padding: 0 10px;
}
.tab-item {
    margin: 0 5px;
}
}
/* 添加底部安全区域 */
.safe-area-bottom {
  height: calc(60px + env(safe-area-inset-bottom));
}