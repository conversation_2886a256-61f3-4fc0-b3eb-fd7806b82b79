"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
if (!Math) {
  (SearchAndCategory + ProductCard + ShopCard)();
}
const SearchAndCategory = () => "../../components/mall/SearchAndCategory.js";
const ProductCard = () => "../../components/mall/ProductCard.js";
const ShopCard = () => "../../components/mall/ShopCard.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const unreadNotifications = common_vendor.ref(5);
    const unreadMessageCount = common_vendor.ref(3);
    const categories = common_vendor.ref([
      { name: "全部", icon: "/static/images/category/all.png", bgColor: "#FFF0F5" },
      { name: "美食", icon: "/static/images/category/food.png", bgColor: "#FFF4E8" },
      { name: "服饰", icon: "/static/images/category/clothing.png", bgColor: "#E8F5FF" },
      { name: "美妆", icon: "/static/images/category/beauty.png", bgColor: "#F0FFF0" },
      { name: "电子", icon: "/static/images/category/electronics.png", bgColor: "#F5E8FF" },
      { name: "家居", icon: "/static/images/category/home.png", bgColor: "#E8FFFF" },
      { name: "母婴", icon: "/static/images/category/baby.png", bgColor: "#FFF0E8" },
      { name: "更多", icon: "/static/images/category/more.png", bgColor: "#F2F2F7" },
      { name: "生鲜", icon: "/static/images/category/fresh.png", bgColor: "#E8FFF0" },
      { name: "书籍", icon: "/static/images/category/books.png", bgColor: "#FFE8F5" }
    ]);
    const flashSaleProducts = common_vendor.ref([
      {
        id: 1,
        title: "iPhone 14 Pro 深空黑 256G",
        coverImage: "https://via.placeholder.com/300x300",
        shopName: "Apple授权专卖店",
        shopLogo: "https://via.placeholder.com/100",
        price: 7999,
        originalPrice: 8999,
        soldCount: 235,
        tag: "限时秒杀",
        labels: [
          { type: "discount", text: "满3000减300" },
          { type: "new", text: "新品" }
        ]
      },
      {
        id: 2,
        title: "华为Mate 50 Pro 曜金黑 512G",
        coverImage: "https://via.placeholder.com/300x300",
        shopName: "华为官方旗舰店",
        shopLogo: "https://via.placeholder.com/100",
        price: 6999,
        originalPrice: 7999,
        soldCount: 189,
        tag: "限时特惠",
        labels: [
          { type: "discount", text: "满5000减500" },
          { type: "hot", text: "热卖" }
        ]
      },
      {
        id: 3,
        title: "小米12S Ultra 陶瓷白 256G",
        coverImage: "https://via.placeholder.com/300x300",
        shopName: "小米官方旗舰店",
        shopLogo: "https://via.placeholder.com/100",
        price: 5999,
        originalPrice: 6999,
        soldCount: 156,
        tag: "限时秒杀",
        labels: [
          { type: "coupon", text: "满3000减400" }
        ]
      },
      {
        id: 4,
        title: "OPPO Find X5 Pro 陶瓷白 256G",
        coverImage: "https://via.placeholder.com/300x300",
        shopName: "OPPO官方旗舰店",
        shopLogo: "https://via.placeholder.com/100",
        price: 4999,
        originalPrice: 5999,
        soldCount: 132,
        tag: "限时特惠",
        labels: [
          { type: "new", text: "新品" }
        ]
      }
    ]);
    const nearbyShops = common_vendor.ref([
      {
        id: 1,
        name: "星巴克咖啡(万达广场店)",
        logo: "https://via.placeholder.com/100",
        coverImage: "https://via.placeholder.com/750x300",
        rating: 4.8,
        distance: "500m",
        orderCount: 5689,
        tags: ["咖啡", "甜点", "下午茶"],
        description: "提供优质咖啡和舒适环境的星巴克门店，欢迎品尝我们的季节限定饮品。"
      },
      {
        id: 2,
        name: "海底捞火锅(环球中心店)",
        logo: "https://via.placeholder.com/100",
        coverImage: "https://via.placeholder.com/750x300",
        rating: 4.9,
        distance: "1.2km",
        orderCount: 8976,
        tags: ["火锅", "川菜", "聚餐"],
        description: "提供优质服务和美味火锅的海底捞门店，欢迎您的光临。"
      }
    ]);
    const hotProducts = common_vendor.ref([
      {
        id: 5,
        title: "Apple AirPods Pro 2代",
        coverImage: "https://via.placeholder.com/300x300",
        shopName: "Apple授权专卖店",
        shopLogo: "https://via.placeholder.com/100",
        price: 1999,
        originalPrice: 2299,
        soldCount: 456,
        distance: "2.5km",
        labels: [
          { type: "hot", text: "热卖" }
        ]
      },
      {
        id: 6,
        title: "戴森吹风机 Supersonic HD08",
        coverImage: "https://via.placeholder.com/300x300",
        shopName: "戴森官方旗舰店",
        shopLogo: "https://via.placeholder.com/100",
        price: 3190,
        originalPrice: 3690,
        soldCount: 325,
        distance: "3.1km",
        labels: [
          { type: "discount", text: "满3000减300" }
        ]
      },
      {
        id: 7,
        title: "NIKE Air Jordan 1 高帮篮球鞋",
        coverImage: "https://via.placeholder.com/300x300",
        shopName: "NIKE官方旗舰店",
        shopLogo: "https://via.placeholder.com/100",
        price: 1299,
        originalPrice: 1499,
        soldCount: 567,
        distance: "4.2km",
        labels: [
          { type: "new", text: "新品" }
        ]
      },
      {
        id: 8,
        title: "三星Galaxy Watch 5 Pro",
        coverImage: "https://via.placeholder.com/300x300",
        shopName: "三星电子旗舰店",
        shopLogo: "https://via.placeholder.com/100",
        price: 2999,
        originalPrice: 3299,
        soldCount: 234,
        distance: "1.8km",
        labels: [
          { type: "coupon", text: "满2000减200" }
        ]
      }
    ]);
    const showNotifications = () => {
      common_vendor.index.showToast({
        title: "通知功能开发中",
        icon: "none"
      });
    };
    const showScanCode = () => {
      common_vendor.index.scanCode({
        success: (res) => {
          common_vendor.index.showToast({
            title: "扫码成功: " + res.result,
            icon: "none"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "扫码失败",
            icon: "none"
          });
        }
      });
    };
    const onCategoryChange = (index) => {
      common_vendor.index.showToast({
        title: `已切换到${categories.value[index].name}分类`,
        icon: "none"
      });
    };
    const viewProductDetail = (product) => {
      navigateTo(`/subPackages/activity-showcase/pages/products/detail?id=${product.id}`);
    };
    const viewShopDetail = (shop) => {
      navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.id}`);
    };
    const addToCart = (product) => {
      common_vendor.index.showToast({
        title: "已添加到购物车",
        icon: "success"
      });
    };
    const switchTab = (tab) => {
      switch (tab) {
        case "home":
          common_vendor.index.redirectTo({
            url: "/subPackages/activity-showcase/pages/index/index"
          });
          break;
        case "distribution":
          common_vendor.index.redirectTo({
            url: "/subPackages/activity-showcase/pages/distribution/index"
          });
          break;
        case "message":
          common_vendor.index.redirectTo({
            url: "/subPackages/activity-showcase/pages/message/index"
          });
          break;
        case "my":
          common_vendor.index.redirectTo({
            url: "/subPackages/activity-showcase/pages/my/index"
          });
          break;
      }
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    const loadMore = () => {
      common_vendor.index.showToast({
        title: "已加载全部数据",
        icon: "none"
      });
    };
    common_vendor.onMounted(() => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M11 17.25a6.25 6.25 0 110-12.5 6.25 6.25 0 010 12.5zm0 0L21 21",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        c: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/search/index")),
        d: common_vendor.p({
          d: "M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9M13.73 21a2 2 0 01-3.46 0",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        f: unreadNotifications.value > 0
      }, unreadNotifications.value > 0 ? {
        g: common_vendor.t(unreadNotifications.value > 99 ? "99+" : unreadNotifications.value)
      } : {}, {
        h: common_vendor.o(showNotifications),
        i: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/search/index")),
        j: common_vendor.o(showScanCode),
        k: common_vendor.o(onCategoryChange),
        l: common_vendor.p({
          categories: categories.value
        }),
        m: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF3B69",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        o: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/flash-sale/index")),
        p: common_vendor.f(flashSaleProducts.value, (product, index, i0) => {
          return {
            a: common_vendor.o(($event) => viewProductDetail(product), index),
            b: common_vendor.o(($event) => addToCart(), index),
            c: "25c293b4-7-" + i0,
            d: common_vendor.p({
              product,
              showShop: false,
              cardStyle: {
                width: "280rpx",
                marginRight: index === flashSaleProducts.value.length - 1 ? "0" : "20rpx"
              }
            }),
            e: index
          };
        }),
        q: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF3B69",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        r: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        s: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/shops/index")),
        t: common_vendor.f(nearbyShops.value, (shop, index, i0) => {
          return {
            a: index,
            b: common_vendor.o(($event) => viewShopDetail(shop), index),
            c: common_vendor.o(($event) => viewShopDetail(shop), index),
            d: "25c293b4-10-" + i0,
            e: common_vendor.p({
              shop
            })
          };
        }),
        v: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#FF3B69",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        x: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/products/hot")),
        y: common_vendor.f(hotProducts.value, (product, index, i0) => {
          return {
            a: common_vendor.o(($event) => viewProductDetail(product), index),
            b: common_vendor.o(($event) => addToCart(), index),
            c: "25c293b4-13-" + i0,
            d: common_vendor.p({
              product
            }),
            e: index
          };
        }),
        z: common_vendor.o(loadMore),
        A: common_vendor.o(($event) => switchTab("home")),
        B: common_vendor.o(($event) => switchTab("distribution")),
        C: unreadMessageCount.value > 0
      }, unreadMessageCount.value > 0 ? {
        D: common_vendor.t(unreadMessageCount.value > 99 ? "99+" : unreadMessageCount.value)
      } : {}, {
        E: common_vendor.o(($event) => switchTab("message")),
        F: common_vendor.o(($event) => switchTab("my"))
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-25c293b4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/discover/index.js.map
