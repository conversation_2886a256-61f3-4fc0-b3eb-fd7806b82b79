// 活动详情模拟数据
export const activityDetail = {
  id: 'activity-001',
  title: '磁县第二届美食文化节',
  startTime: '2024-04-15 10:00',
  endTime: '2024-04-17 22:00',
  location: '磁县中央广场',
  address: '磁县城区中央广场（人民路与振兴路交叉口）',
  category: '美食',
  status: 'upcoming', // upcoming, ongoing, ended
  images: [
    '/static/images/activity/activity-1.jpg',
    '/static/images/activity/activity-1-1.jpg',
    '/static/images/activity/activity-1-2.jpg',
    '/static/images/activity/activity-1-3.jpg'
  ],
  organizer: {
    name: '磁县文化旅游局',
    logo: '/static/images/logo/culture-bureau.png',
    phone: '0310-12345678',
    isOfficial: true
  },
  coOrganizers: [
    '磁县商务局',
    '磁县餐饮协会'
  ],
  participants: 256,
  maxParticipants: 500,
  isFree: true,
  price: 0,
  isOfficial: true,
  tags: ['美食', '文化', '展览'],
  description: '为弘扬磁县特色美食文化，促进餐饮业发展，由磁县文化旅游局主办，磁县商务局、磁县餐饮协会协办的"磁县第二届美食文化节"将于2024年4月15日至17日在磁县中央广场隆重举行。\n\n本届美食文化节以"品味磁州，舌尖上的记忆"为主题，设有特色美食展示区、非遗美食制作展示区、美食品鉴区、文艺表演区等多个功能区域。活动期间将举办特色美食评选、厨艺大赛、美食文化讲座等系列活动，同时还有精彩的文艺表演和互动游戏。\n\n欢迎广大市民朋友前来参观品尝，共同感受磁州美食文化的魅力！',
  schedule: [
    {
      date: '2024-04-15',
      events: [
        { time: '10:00-11:00', title: '开幕式' },
        { time: '11:00-17:00', title: '特色美食展示' },
        { time: '14:00-16:00', title: '非遗美食制作展示' },
        { time: '19:00-21:00', title: '文艺晚会' }
      ]
    },
    {
      date: '2024-04-16',
      events: [
        { time: '09:00-17:00', title: '特色美食展示' },
        { time: '10:00-12:00', title: '厨艺大赛初赛' },
        { time: '14:00-16:00', title: '美食文化讲座' },
        { time: '19:00-21:00', title: '互动游戏' }
      ]
    },
    {
      date: '2024-04-17',
      events: [
        { time: '09:00-17:00', title: '特色美食展示' },
        { time: '10:00-12:00', title: '厨艺大赛决赛' },
        { time: '14:00-16:00', title: '特色美食评选' },
        { time: '16:00-17:00', title: '颁奖仪式' },
        { time: '19:00-22:00', title: '闭幕式晚会' }
      ]
    }
  ],
  notices: [
    '活动期间，中央广场周边道路将实施交通管制，请市民提前规划出行路线',
    '现场将设置临时停车场，请按照工作人员指引有序停放车辆',
    '活动现场禁止携带易燃易爆物品',
    '请自觉维护活动现场秩序，爱护公共设施'
  ],
  contact: {
    name: '活动组委会',
    phone: '0310-12345678',
    email: '<EMAIL>'
  },
  location: {
    latitude: 36.314736,
    longitude: 114.711234
  },
  publishTime: '2024-03-01 09:00:00',
  views: 1256,
  likes: 89,
  collections: 45,
  shares: 67,
  isParticipated: false,
  commentCount: 28,
  commentList: [
    {
      id: 'comment-001',
      user: {
        id: 'user-001',
        nickname: '磁州居民',
        avatar: '/static/images/tabbar/user-blue.png'
      },
      content: '期待已久的美食节终于来了，去年参加过，非常精彩，今年一定要再去！',
      time: '2024-03-02 10:30',
      likes: 15,
      isLiked: false
    },
    {
      id: 'comment-002',
      user: {
        id: 'user-002',
        nickname: '美食爱好者',
        avatar: '/static/images/tabbar/user-blue.png'
      },
      content: '希望今年能有更多特色小吃，特别期待磁州窑烤肉！',
      time: '2024-03-03 14:15',
      likes: 8,
      isLiked: false
    },
    {
      id: 'comment-003',
      user: {
        id: 'official-001',
        nickname: '磁县文化旅游局',
        avatar: '/static/images/logo/culture-bureau.png',
        isOfficial: true
      },
      content: '感谢大家的关注和支持！本届美食节规模将比去年更大，特色小吃种类更丰富，欢迎大家参与！',
      time: '2024-03-04 09:45',
      likes: 32,
      isLiked: false
    }
  ]
};

// 相关活动模拟数据
export const relatedActivities = [
  {
    id: 'activity-002',
    title: '磁州窑非遗文化体验活动',
    startTime: '2024-04-10 09:30',
    endTime: '2024-04-10 16:30',
    location: '磁州窑博物馆',
    category: '文化',
    status: 'upcoming',
    image: '/static/images/activity/activity-2.jpg',
    organizer: '磁州窑博物馆',
    participants: 78,
    maxParticipants: 100,
    isFree: false,
    price: 20,
    tags: ['非遗', '文化', '体验']
  },
  {
    id: 'activity-003',
    title: '春季健步走活动',
    startTime: '2024-03-25 07:00',
    endTime: '2024-03-25 09:00',
    location: '磁县人民公园',
    category: '运动',
    status: 'upcoming',
    image: '/static/images/activity/activity-3.jpg',
    organizer: '磁县全民健身协会',
    participants: 145,
    maxParticipants: 300,
    isFree: true,
    tags: ['健身', '户外', '公益']
  },
  {
    id: 'activity-004',
    title: '磁县青年创业分享会',
    startTime: '2024-03-20 14:00',
    endTime: '2024-03-20 17:00',
    location: '磁县青年创业中心',
    category: '创业',
    status: 'ongoing',
    image: '/static/images/activity/activity-4.jpg',
    organizer: '磁县青年创业协会',
    participants: 68,
    maxParticipants: 100,
    isFree: true,
    tags: ['创业', '分享', '交流']
  }
];

// 获取活动详情的API函数
export const fetchActivityDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(activityDetail);
    }, 500);
  });
};

// 获取相关活动的API函数
export const fetchRelatedActivities = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(relatedActivities);
    }, 500);
  });
};

// 获取更多评论的API函数
export const fetchMoreComments = (activityId, page = 2) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const moreComments = [
        {
          id: 'comment-004',
          user: {
            id: 'user-003',
            nickname: '老磁州',
            avatar: '/static/images/tabbar/user-blue.png'
          },
          content: '建议增加一些适合孩子参与的互动环节，让孩子们也能感受美食文化的魅力',
          time: '2024-03-05 16:20',
          likes: 7,
          isLiked: false
        },
        {
          id: 'comment-005',
          user: {
            id: 'user-004',
            nickname: '城市摄影师',
            avatar: '/static/images/tabbar/user-blue.png'
          },
          content: '去年拍了很多美食照片，今年打算做一个美食vlog，记录磁县特色小吃',
          time: '2024-03-06 11:35',
          likes: 5,
          isLiked: false
        }
      ];
      
      resolve({
        list: moreComments,
        hasMore: false
      });
    }, 800);
  });
};

// 参与活动的API函数
export const participateActivity = (activityId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '报名成功',
        data: {
          isParticipated: true,
          participants: activityDetail.participants + 1
        }
      });
    }, 500);
  });
};

// 取消参与活动的API函数
export const cancelParticipation = (activityId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '已取消报名',
        data: {
          isParticipated: false,
          participants: activityDetail.participants
        }
      });
    }, 500);
  });
}; 