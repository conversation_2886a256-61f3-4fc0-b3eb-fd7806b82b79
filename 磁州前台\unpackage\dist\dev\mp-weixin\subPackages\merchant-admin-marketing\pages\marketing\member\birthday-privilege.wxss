
/* 生日特权页面样式开始 */
.birthday-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 100rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
}

/* 特权内容样式 */
.privilege-content {
  padding: 20rpx;
}
.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 开关样式 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.switch-content {
  flex: 1;
}
.switch-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.switch-desc {
  font-size: 24rpx;
  color: #999;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}
.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}
.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}
.form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 20rpx;
}
.radio-item {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}
.radio-item.active {
  background: rgba(74, 0, 224, 0.1);
  border-color: #4A00E0;
}
.radio-text {
  font-size: 26rpx;
  color: #666;
}
.radio-item.active .radio-text {
  color: #4A00E0;
  font-weight: 600;
}

/* 特权列表样式 */
.privilege-list {
  margin-bottom: 20rpx;
}
.privilege-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.privilege-item:last-child {
  border-bottom: none;
}
.privilege-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.privilege-checkbox.checked {
  border-color: #4A00E0;
  background: #4A00E0;
}
.checkbox-inner {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background: #fff;
}
.privilege-content {
  flex: 1;
}
.privilege-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.privilege-desc {
  font-size: 24rpx;
  color: #999;
}
.privilege-config {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  background: rgba(74, 0, 224, 0.1);
}
.config-text {
  font-size: 24rpx;
  color: #4A00E0;
}

/* 会员等级列表 */
.level-list {
  margin-bottom: 20rpx;
}
.level-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.level-item:last-child {
  border-bottom: none;
}
.level-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.level-checkbox.checked {
  border-color: #4A00E0;
  background: #4A00E0;
}
.level-content {
  flex: 1;
}
.level-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.level-desc {
  font-size: 24rpx;
  color: #999;
}

/* 祝福语预览 */
.greeting-preview {
  margin-top: 30rpx;
}
.preview-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}
.preview-card {
  border: 1rpx solid #f0f0f0;
  border-radius: 12rpx;
  overflow: hidden;
}
.preview-header {
  background: #4A00E0;
  padding: 20rpx;
  display: flex;
  align-items: center;
}
.preview-logo {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
}
.preview-shop-name {
  font-size: 28rpx;
  color: #fff;
  font-weight: 600;
}
.preview-content {
  padding: 30rpx;
  background: #fff;
}
.preview-greeting {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}
.preview-footer {
  padding: 20rpx;
  display: flex;
  justify-content: center;
  border-top: 1rpx solid #f0f0f0;
}
.preview-btn {
  font-size: 28rpx;
  color: #4A00E0;
  font-weight: 600;
}

/* 添加按钮 */
.add-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 底部保存栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.save-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  width: 100%;
}
/* 生日特权页面样式结束 */
