/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.discount-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FDEB71, #F8D800);
  color: #333;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(248, 216, 0, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #333;
  border-bottom: 2px solid #333;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}
.overview-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}
.overview-card {
  width: 25%;
  padding: 0 5px;
  box-sizing: border-box;
}
.card-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #F8D800;
  text-align: center;
  margin-bottom: 5px;
}
.card-label {
  display: block;
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* 操作栏样式 */
.action-bar {
  margin: 0 15px 15px;
  display: flex;
  align-items: center;
}
.search-box {
  flex: 1;
  height: 36px;
  background: #fff;
  border-radius: 18px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #999;
}
.icon-svg {
  width: 16px;
  height: 16px;
}
.search-input {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #333;
  border: none;
  background: transparent;
}
.filter-btn {
  height: 36px;
  padding: 0 12px;
  background: #fff;
  border-radius: 18px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  color: #666;
}
.btn-text {
  font-size: 14px;
  color: #666;
}

/* 满减活动列表样式 */
.discount-list {
  padding: 0 15px;
  margin-bottom: 80px;
  /* 为悬浮按钮留出空间 */
}
.discount-item {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
}
.discount-item.hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.discount-status {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  color: white;
}
.status-active {
  background: #34C759;
}
.status-expired {
  background: #8E8E93;
}
.status-upcoming {
  background: #FF9500;
}
.status-paused {
  background: #FF9500;
}
.discount-content {
  margin-right: 70px;
  /* 为状态标签留出空间 */
  margin-bottom: 15px;
}
.discount-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}
.discount-rules {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}
.rule-item {
  background: rgba(248, 216, 0, 0.1);
  border-radius: 15px;
  padding: 5px 10px;
  margin-right: 8px;
  margin-bottom: 8px;
}
.rule-text {
  font-size: 13px;
  color: #D4B100;
  font-weight: 500;
}
.discount-info {
  display: flex;
  flex-wrap: wrap;
}
.info-item {
  width: 50%;
  margin-bottom: 5px;
  display: flex;
}
.info-label {
  font-size: 12px;
  color: #999;
  margin-right: 5px;
}
.info-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}
.discount-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}
.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}
.action-btn .btn-icon {
  width: 20px;
  height: 20px;
  margin-right: 0;
  margin-bottom: 3px;
}
.action-btn .btn-text {
  font-size: 12px;
}
.action-btn.edit {
  color: #007AFF;
}
.action-btn.pause {
  color: #FF9500;
}
.action-btn.activate {
  color: #34C759;
}
.action-btn.delete {
  color: #FF3B30;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}
.empty-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: rgba(248, 216, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: #F8D800;
}
.empty-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}
.empty-subtext {
  font-size: 14px;
  color: #999;
}

/* 顶部操作区样式 */
.top-actions {
  padding: 15px;
  display: flex;
  justify-content: flex-end;
  background-color: #fff;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
.overview-card {
    width: 50%;
    margin-bottom: 10px;
}
}