{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/reminders/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNccmVtaW5kZXJzXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"reminders-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-bg\"></view>\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @click=\"goBack\">\n          <image src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\" class=\"back-icon\"></image>\n        </view>\n        <view class=\"navbar-title\">活动提醒</view>\n        <view class=\"navbar-right\">\n          <view class=\"settings-btn\" @click=\"showSettings\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n              <path d=\"M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n            </svg>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 提醒类型标签栏 -->\n    <view class=\"reminder-tabs\">\n      <view \n        v-for=\"(tab, index) in reminderTabs\" \n        :key=\"index\"\n        class=\"tab-item\"\n        :class=\"{ active: currentTab === index }\"\n        @click=\"switchTab(index)\"\n      >\n        <text class=\"tab-text\">{{ tab.name }}</text>\n        <view class=\"tab-indicator\" v-if=\"currentTab === index\" :style=\"{\n          background: 'linear-gradient(90deg, #34C759 0%, #7ED321 100%)'\n        }\"></view>\n      </view>\n    </view>\n\n    <!-- 提醒列表区域 -->\n    <swiper class=\"reminders-swiper\" :current=\"currentTab\" @change=\"onSwiperChange\">\n      <swiper-item v-for=\"(tab, tabIndex) in reminderTabs\" :key=\"tabIndex\">\n        <scroll-view \n          class=\"tab-content\" \n          scroll-y \n          refresher-enabled\n          :refresher-triggered=\"isRefreshing\"\n          @refresherrefresh=\"onRefresh\"\n          @scrolltolower=\"loadMore\"\n        >\n          <view class=\"reminders-list\">\n            <view \n              v-for=\"reminder in getRemindersByType(tab.type)\" \n              :key=\"reminder.id\"\n              class=\"reminder-card\"\n              :class=\"{ 'read': reminder.isRead }\"\n            >\n              <!-- 提醒图标 -->\n              <view class=\"reminder-icon\" :style=\"{\n                background: getReminderIconBg(reminder.category)\n              }\">\n                <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n                  <path :d=\"getReminderIconPath(reminder.category)\" stroke=\"#FFFFFF\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                </svg>\n              </view>\n              \n              <!-- 提醒内容 -->\n              <view class=\"reminder-content\" @click=\"viewReminderDetail(reminder)\">\n                <view class=\"reminder-header\">\n                  <text class=\"reminder-title\">{{ reminder.title }}</text>\n                  <text class=\"reminder-time\">{{ reminder.time }}</text>\n                </view>\n                <text class=\"reminder-desc\">{{ reminder.description }}</text>\n                \n                <!-- 活动信息 -->\n                <view class=\"activity-info\" v-if=\"reminder.activityInfo\">\n                  <image :src=\"reminder.activityInfo.image\" class=\"activity-image\" mode=\"aspectFill\"></image>\n                  <view class=\"activity-details\">\n                    <text class=\"activity-name\">{{ reminder.activityInfo.name }}</text>\n                    <view class=\"activity-meta\">\n                      <text class=\"activity-date\">{{ reminder.activityInfo.date }}</text>\n                      <text class=\"activity-location\">{{ reminder.activityInfo.location }}</text>\n                    </view>\n                  </view>\n                </view>\n                \n                <!-- 操作按钮 -->\n                <view class=\"reminder-actions\">\n                  <view \n                    class=\"action-btn\"\n                    :style=\"{\n                      background: getPrimaryActionBgColor(reminder.category),\n                      color: '#FFFFFF'\n                    }\"\n                    @click.stop=\"handlePrimaryAction(reminder)\"\n                  >\n                    {{ getPrimaryActionText(reminder.category) }}\n                  </view>\n                  \n                  <view \n                    class=\"action-btn secondary\"\n                    @click.stop=\"markAsRead(reminder)\"\n                    v-if=\"!reminder.isRead\"\n                  >\n                    标记为已读\n                  </view>\n                  \n                  <view \n                    class=\"action-btn secondary\"\n                    @click.stop=\"deleteReminder(reminder)\"\n                    v-else\n                  >\n                    删除\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 空状态 -->\n          <view class=\"empty-state\" v-if=\"getRemindersByType(tab.type).length === 0\">\n            <image class=\"empty-image\" :src=\"tab.emptyImage || '/static/images/empty-reminders.png'\"></image>\n            <text class=\"empty-text\">{{ tab.emptyText || '暂无相关提醒' }}</text>\n            <view class=\"action-btn\" @click=\"navigateTo('/subPackages/activity-showcase/pages/list/index')\" :style=\"{\n              background: 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',\n              borderRadius: '35px',\n              boxShadow: '0 5px 15px rgba(52,199,89,0.3)'\n            }\">\n              <text>去参与活动</text>\n            </view>\n          </view>\n        </scroll-view>\n      </swiper-item>\n    </swiper>\n\n    <!-- 设置弹窗 -->\n    <uni-popup ref=\"settingsPopup\" type=\"bottom\">\n      <view class=\"settings-popup\">\n        <view class=\"settings-header\">\n          <text class=\"settings-title\">提醒设置</text>\n          <view class=\"settings-close\" @click=\"closeSettings\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\">\n              <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n              <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></line>\n            </svg>\n          </view>\n        </view>\n        \n        <view class=\"settings-content\">\n          <view class=\"settings-section\">\n            <text class=\"section-title\">通知设置</text>\n            <view class=\"settings-item\">\n              <text class=\"item-label\">活动开始提醒</text>\n              <switch :checked=\"settings.activityStart\" @change=\"toggleSetting('activityStart')\" color=\"#34C759\" />\n            </view>\n            <view class=\"settings-item\">\n              <text class=\"item-label\">活动变更提醒</text>\n              <switch :checked=\"settings.activityChange\" @change=\"toggleSetting('activityChange')\" color=\"#34C759\" />\n            </view>\n            <view class=\"settings-item\">\n              <text class=\"item-label\">报名成功提醒</text>\n              <switch :checked=\"settings.registrationSuccess\" @change=\"toggleSetting('registrationSuccess')\" color=\"#34C759\" />\n            </view>\n            <view class=\"settings-item\">\n              <text class=\"item-label\">活动取消提醒</text>\n              <switch :checked=\"settings.activityCancel\" @change=\"toggleSetting('activityCancel')\" color=\"#34C759\" />\n            </view>\n          </view>\n          \n          <view class=\"settings-section\">\n            <text class=\"section-title\">提醒时间</text>\n            <view class=\"settings-item\">\n              <text class=\"item-label\">提前提醒时间</text>\n              <picker \n                mode=\"selector\" \n                :range=\"reminderTimeOptions\" \n                :value=\"reminderTimeIndex\" \n                @change=\"onReminderTimeChange\"\n              >\n                <view class=\"picker-value\">\n                  <text>{{ reminderTimeOptions[reminderTimeIndex] }}</text>\n                  <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n                    <path d=\"M6 9l6 6 6-6\" stroke=\"#999999\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n                  </svg>\n                </view>\n              </picker>\n            </view>\n          </view>\n          \n          <view class=\"settings-section\">\n            <text class=\"section-title\">清除记录</text>\n            <view class=\"clear-options\">\n              <view class=\"clear-btn\" @click=\"clearReadReminders\">\n                <text>清除已读提醒</text>\n              </view>\n              <view class=\"clear-btn danger\" @click=\"clearAllReminders\">\n                <text>清除全部提醒</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue';\n\n// 页面状态\nconst currentTab = ref(0);\nconst isRefreshing = ref(false);\nconst remindersList = ref([]);\nconst settingsPopup = ref(null);\n\n// 设置选项\nconst settings = ref({\n  activityStart: true,\n  activityChange: true,\n  registrationSuccess: true,\n  activityCancel: true\n});\n\nconst reminderTimeOptions = ['提前15分钟', '提前30分钟', '提前1小时', '提前2小时', '提前1天'];\nconst reminderTimeIndex = ref(2);\n\n// 提醒标签页\nconst reminderTabs = [\n  { name: '全部', type: 'all', emptyText: '暂无提醒', emptyImage: '/static/images/empty-reminders.png' },\n  { name: '未读', type: 'unread', emptyText: '暂无未读提醒', emptyImage: '/static/images/empty-unread.png' },\n  { name: '活动', type: 'activity', emptyText: '暂无活动提醒', emptyImage: '/static/images/empty-activity.png' },\n  { name: '系统', type: 'system', emptyText: '暂无系统提醒', emptyImage: '/static/images/empty-system.png' }\n];\n\n// 模拟数据\nconst mockReminders = [\n  {\n    id: '1001',\n    title: '活动即将开始',\n    description: '您报名参加的\"磁州文化节\"将于明天开始，请准时参加。',\n    category: 'activity_start',\n    type: 'activity',\n    time: '10分钟前',\n    isRead: false,\n    activityInfo: {\n      id: '1001',\n      name: '磁州文化节',\n      date: '2024-06-15 09:00',\n      location: '磁州文化广场',\n      image: '/static/demo/activity1.jpg'\n    }\n  },\n  {\n    id: '1002',\n    title: '报名成功通知',\n    description: '恭喜您成功报名\"亲子户外拓展活动\"，请按时参加。',\n    category: 'registration_success',\n    type: 'activity',\n    time: '2小时前',\n    isRead: false,\n    activityInfo: {\n      id: '1002',\n      name: '亲子户外拓展活动',\n      date: '2024-05-28 14:00',\n      location: '磁州森林公园',\n      image: '/static/demo/activity2.jpg'\n    }\n  },\n  {\n    id: '1003',\n    title: '活动地点变更',\n    description: '\"社区篮球赛\"的活动地点已变更，请查看详情。',\n    category: 'activity_change',\n    type: 'activity',\n    time: '昨天 15:30',\n    isRead: true,\n    activityInfo: {\n      id: '1003',\n      name: '社区篮球赛',\n      date: '2024-05-20 10:00',\n      location: '磁州体育中心(新)',\n      image: '/static/demo/activity3.jpg'\n    }\n  },\n  {\n    id: '1004',\n    title: '活动已取消',\n    description: '很抱歉，\"传统文化体验课\"因故取消，报名费用将原路退回。',\n    category: 'activity_cancel',\n    type: 'activity',\n    time: '昨天 10:15',\n    isRead: true,\n    activityInfo: {\n      id: '1004',\n      name: '传统文化体验课',\n      date: '2024-05-15 15:00',\n      location: '磁州文化馆',\n      image: '/static/demo/activity4.jpg'\n    }\n  },\n  {\n    id: '1005',\n    title: '系统通知',\n    description: '您的账号已成功升级为VIP会员，可享受更多活动优惠。',\n    category: 'system',\n    type: 'system',\n    time: '2天前',\n    isRead: false\n  }\n];\n\n// 生命周期\nonMounted(() => {\n  loadReminders();\n});\n\n// 方法\nconst loadReminders = () => {\n  // 模拟加载数据\n  remindersList.value = mockReminders;\n};\n\nconst getRemindersByType = (type) => {\n  if (type === 'all') {\n    return remindersList.value;\n  } else if (type === 'unread') {\n    return remindersList.value.filter(reminder => !reminder.isRead);\n  } else {\n    return remindersList.value.filter(reminder => reminder.type === type);\n  }\n};\n\nconst switchTab = (index) => {\n  currentTab.value = index;\n};\n\nconst onSwiperChange = (e) => {\n  currentTab.value = e.detail.current;\n};\n\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  setTimeout(() => {\n    loadReminders();\n    isRefreshing.value = false;\n  }, 1000);\n};\n\nconst loadMore = () => {\n  // 模拟加载更多\n  console.log('加载更多提醒');\n};\n\nconst getReminderIconPath = (category) => {\n  const iconMap = {\n    'activity_start': 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',\n    'registration_success': 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',\n    'activity_change': 'M19 21v-2a4 4 0 00-4-4H9a4 4 0 00-4 4v2M12 11a4 4 0 100-8 4 4 0 000 8z',\n    'activity_cancel': 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z',\n    'system': 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'\n  };\n  return iconMap[category] || iconMap['system'];\n};\n\nconst getReminderIconBg = (category) => {\n  const bgMap = {\n    'activity_start': 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',\n    'registration_success': 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',\n    'activity_change': 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',\n    'activity_cancel': 'linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)',\n    'system': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)'\n  };\n  return bgMap[category] || bgMap['system'];\n};\n\nconst getPrimaryActionText = (category) => {\n  const actionMap = {\n    'activity_start': '查看详情',\n    'registration_success': '查看订单',\n    'activity_change': '查看变更',\n    'activity_cancel': '查看退款',\n    'system': '了解详情'\n  };\n  return actionMap[category] || '查看详情';\n};\n\nconst getPrimaryActionBgColor = (category) => {\n  const bgColorMap = {\n    'activity_start': 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)',\n    'registration_success': 'linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)',\n    'activity_change': 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',\n    'activity_cancel': 'linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)',\n    'system': 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)'\n  };\n  return bgColorMap[category] || 'linear-gradient(135deg, #34C759 0%, #7ED321 100%)';\n};\n\nconst handlePrimaryAction = (reminder) => {\n  switch (reminder.category) {\n    case 'activity_start':\n      if (reminder.activityInfo) {\n        navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${reminder.activityInfo.id}`);\n      }\n      break;\n    case 'registration_success':\n      navigateTo('/subPackages/activity-showcase/pages/orders/index?status=all');\n      break;\n    case 'activity_change':\n      if (reminder.activityInfo) {\n        navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${reminder.activityInfo.id}`);\n      }\n      break;\n    case 'activity_cancel':\n      navigateTo('/subPackages/activity-showcase/pages/orders/index?status=cancelled');\n      break;\n    default:\n      viewReminderDetail(reminder);\n  }\n  \n  // 标记为已读\n  if (!reminder.isRead) {\n    markAsRead(reminder);\n  }\n};\n\nconst viewReminderDetail = (reminder) => {\n  // 标记为已读\n  if (!reminder.isRead) {\n    markAsRead(reminder);\n  }\n  \n  // 查看详情\n  if (reminder.activityInfo) {\n    navigateTo(`/subPackages/activity-showcase/pages/activity-detail/index?id=${reminder.activityInfo.id}`);\n  } else {\n    uni.showModal({\n      title: reminder.title,\n      content: reminder.description,\n      showCancel: false\n    });\n  }\n};\n\nconst markAsRead = (reminder) => {\n  const index = remindersList.value.findIndex(item => item.id === reminder.id);\n  if (index !== -1) {\n    remindersList.value[index].isRead = true;\n  }\n};\n\nconst deleteReminder = (reminder) => {\n  uni.showModal({\n    title: '提示',\n    content: '确认删除该提醒吗？',\n    success: function(res) {\n      if (res.confirm) {\n        const index = remindersList.value.findIndex(item => item.id === reminder.id);\n        if (index !== -1) {\n          remindersList.value.splice(index, 1);\n        }\n        \n        uni.showToast({\n          title: '删除成功',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\nconst showSettings = () => {\n  settingsPopup.value.open();\n};\n\nconst closeSettings = () => {\n  settingsPopup.value.close();\n};\n\nconst toggleSetting = (key) => {\n  settings.value[key] = !settings.value[key];\n};\n\nconst onReminderTimeChange = (e) => {\n  reminderTimeIndex.value = e.detail.value;\n};\n\nconst clearReadReminders = () => {\n  uni.showModal({\n    title: '提示',\n    content: '确认清除所有已读提醒吗？',\n    success: function(res) {\n      if (res.confirm) {\n        remindersList.value = remindersList.value.filter(item => !item.isRead);\n        \n        uni.showToast({\n          title: '清除成功',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\nconst clearAllReminders = () => {\n  uni.showModal({\n    title: '提示',\n    content: '确认清除所有提醒吗？',\n    success: function(res) {\n      if (res.confirm) {\n        remindersList.value = [];\n        \n        uni.showToast({\n          title: '清除成功',\n          icon: 'success'\n        });\n      }\n    }\n  });\n};\n\nconst goBack = () => {\n  uni.navigateBack();\n};\n\nconst navigateTo = (url) => {\n  uni.navigateTo({ url });\n};\n</script>\n\n<style scoped>\n.reminders-container {\n  min-height: 100vh;\n  background-color: #F5F5F5;\n  padding-bottom: 30rpx;\n}\n\n/* 导航栏样式 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 100;\n}\n\n.navbar-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #34C759 0%, #7ED321 100%);\n}\n\n.navbar-content {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 112rpx; /* 原来是107rpx，再增加5rpx */\n  padding: var(--status-bar-height) 30rpx 0;\n}\n\n.back-btn, .settings-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 36rpx;\n  height: 36rpx;\n}\n\n.navbar-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #FFFFFF;\n}\n\n.navbar-right {\n  display: flex;\n  align-items: center;\n}\n\n/* 标签栏样式 */\n.reminder-tabs {\n  display: flex;\n  background: #FFFFFF;\n  padding: 0 20rpx;\n  margin-top: calc(var(--status-bar-height) + 112rpx); /* 原来是107rpx，再增加5rpx */\n  border-bottom: 1rpx solid #EEEEEE;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n}\n\n.tab-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20rpx 0;\n  position: relative;\n}\n\n.tab-text {\n  font-size: 28rpx;\n  color: #333333;\n  padding: 0 10rpx;\n}\n\n.tab-item.active .tab-text {\n  color: #34C759;\n  font-weight: 500;\n}\n\n.tab-indicator {\n  position: absolute;\n  bottom: 0;\n  width: 40rpx;\n  height: 6rpx;\n  border-radius: 3rpx;\n}\n\n/* 提醒列表样式 */\n.reminders-swiper {\n  height: calc(100vh - var(--status-bar-height) - 112rpx - 70rpx); /* 原来是107rpx，再增加5rpx */\n}\n\n.tab-content {\n  height: 100%;\n  padding: 20rpx;\n}\n\n.reminders-list {\n  padding-bottom: 30rpx;\n}\n\n.reminder-card {\n  display: flex;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n  border-left: 6rpx solid #34C759;\n}\n\n.reminder-card.read {\n  border-left: 6rpx solid #DDDDDD;\n  opacity: 0.8;\n}\n\n.reminder-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 30rpx;\n}\n\n.reminder-content {\n  flex: 1;\n  padding: 20rpx 30rpx 20rpx 0;\n}\n\n.reminder-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.reminder-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n}\n\n.reminder-time {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n.reminder-desc {\n  font-size: 28rpx;\n  color: #666666;\n  margin-bottom: 20rpx;\n  line-height: 1.5;\n}\n\n.activity-info {\n  display: flex;\n  background: #F9F9F9;\n  border-radius: 10rpx;\n  padding: 15rpx;\n  margin-bottom: 20rpx;\n}\n\n.activity-image {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 10rpx;\n  margin-right: 15rpx;\n}\n\n.activity-details {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.activity-name {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 10rpx;\n}\n\n.activity-meta {\n  display: flex;\n  flex-direction: column;\n}\n\n.activity-date, .activity-location {\n  font-size: 24rpx;\n  color: #999999;\n  margin-bottom: 5rpx;\n}\n\n.reminder-actions {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.action-btn {\n  padding: 10rpx 30rpx;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  margin-left: 20rpx;\n  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.action-btn.secondary {\n  background: #F5F5F5;\n  color: #666666;\n  border: 1rpx solid #DDDDDD;\n  box-shadow: none;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n\n.empty-text {\n  font-size: 28rpx;\n  color: #999999;\n  margin-bottom: 30rpx;\n}\n\n.empty-state .action-btn {\n  padding: 15rpx 60rpx;\n  font-size: 28rpx;\n  color: #FFFFFF;\n}\n\n/* 设置弹窗样式 */\n.settings-popup {\n  background: #FFFFFF;\n  border-top-left-radius: 30rpx;\n  border-top-right-radius: 30rpx;\n  padding: 30rpx;\n  max-height: 70vh;\n}\n\n.settings-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.settings-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333333;\n}\n\n.settings-close {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.settings-content {\n  max-height: calc(70vh - 180rpx);\n  overflow-y: auto;\n}\n\n.settings-section {\n  margin-bottom: 30rpx;\n}\n\n.section-title {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 500;\n  margin-bottom: 20rpx;\n}\n\n.settings-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #F0F0F0;\n}\n\n.settings-item:last-child {\n  border-bottom: none;\n}\n\n.item-label {\n  font-size: 28rpx;\n  color: #333333;\n}\n\n.picker-value {\n  display: flex;\n  align-items: center;\n  font-size: 28rpx;\n  color: #666666;\n}\n\n.clear-options {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 20rpx;\n}\n\n.clear-btn {\n  flex: 1;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  background: #F5F5F5;\n  color: #666666;\n  margin-right: 20rpx;\n}\n\n.clear-btn.danger {\n  background: linear-gradient(135deg, #FF3B30 0%, #FF9580 100%);\n  color: #FFFFFF;\n  box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.2);\n  margin-right: 0;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/reminders/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni"], "mappings": ";;;;;;;;;;;;;;AAgNA,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,IAAI;AAG9B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,gBAAgB;AAAA,IAClB,CAAC;AAED,UAAM,sBAAsB,CAAC,UAAU,UAAU,SAAS,SAAS,MAAM;AACzE,UAAM,oBAAoBA,cAAAA,IAAI,CAAC;AAG/B,UAAM,eAAe;AAAA,MACnB,EAAE,MAAM,MAAM,MAAM,OAAO,WAAW,QAAQ,YAAY,qCAAsC;AAAA,MAChG,EAAE,MAAM,MAAM,MAAM,UAAU,WAAW,UAAU,YAAY,kCAAmC;AAAA,MAClG,EAAE,MAAM,MAAM,MAAM,YAAY,WAAW,UAAU,YAAY,oCAAqC;AAAA,MACtG,EAAE,MAAM,MAAM,MAAM,UAAU,WAAW,UAAU,YAAY,kCAAmC;AAAA,IACpG;AAGA,UAAM,gBAAgB;AAAA,MACpB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,cAAc;AAAA,UACZ,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,cAAc;AAAA,UACZ,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,cAAc;AAAA,UACZ,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,cAAc;AAAA,UACZ,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAE1B,oBAAc,QAAQ;AAAA,IACxB;AAEA,UAAM,qBAAqB,CAAC,SAAS;AACnC,UAAI,SAAS,OAAO;AAClB,eAAO,cAAc;AAAA,MACzB,WAAa,SAAS,UAAU;AAC5B,eAAO,cAAc,MAAM,OAAO,cAAY,CAAC,SAAS,MAAM;AAAA,MAClE,OAAS;AACL,eAAO,cAAc,MAAM,OAAO,cAAY,SAAS,SAAS,IAAI;AAAA,MACrE;AAAA,IACH;AAEA,UAAM,YAAY,CAAC,UAAU;AAC3B,iBAAW,QAAQ;AAAA,IACrB;AAEA,UAAM,iBAAiB,CAAC,MAAM;AAC5B,iBAAW,QAAQ,EAAE,OAAO;AAAA,IAC9B;AAEA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,iBAAW,MAAM;AACf;AACA,qBAAa,QAAQ;AAAA,MACtB,GAAE,GAAI;AAAA,IACT;AAEA,UAAM,WAAW,MAAM;AAErBC,oBAAAA,MAAY,MAAA,OAAA,kEAAA,QAAQ;AAAA,IACtB;AAEA,UAAM,sBAAsB,CAAC,aAAa;AACxC,YAAM,UAAU;AAAA,QACd,kBAAkB;AAAA,QAClB,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,UAAU;AAAA,MACd;AACE,aAAO,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;AAAA,IAC9C;AAEA,UAAM,oBAAoB,CAAC,aAAa;AACtC,YAAM,QAAQ;AAAA,QACZ,kBAAkB;AAAA,QAClB,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,UAAU;AAAA,MACd;AACE,aAAO,MAAM,QAAQ,KAAK,MAAM,QAAQ;AAAA,IAC1C;AAEA,UAAM,uBAAuB,CAAC,aAAa;AACzC,YAAM,YAAY;AAAA,QAChB,kBAAkB;AAAA,QAClB,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,UAAU;AAAA,MACd;AACE,aAAO,UAAU,QAAQ,KAAK;AAAA,IAChC;AAEA,UAAM,0BAA0B,CAAC,aAAa;AAC5C,YAAM,aAAa;AAAA,QACjB,kBAAkB;AAAA,QAClB,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,UAAU;AAAA,MACd;AACE,aAAO,WAAW,QAAQ,KAAK;AAAA,IACjC;AAEA,UAAM,sBAAsB,CAAC,aAAa;AACxC,cAAQ,SAAS,UAAQ;AAAA,QACvB,KAAK;AACH,cAAI,SAAS,cAAc;AACzB,uBAAW,iEAAiE,SAAS,aAAa,EAAE,EAAE;AAAA,UACvG;AACD;AAAA,QACF,KAAK;AACH,qBAAW,8DAA8D;AACzE;AAAA,QACF,KAAK;AACH,cAAI,SAAS,cAAc;AACzB,uBAAW,iEAAiE,SAAS,aAAa,EAAE,EAAE;AAAA,UACvG;AACD;AAAA,QACF,KAAK;AACH,qBAAW,oEAAoE;AAC/E;AAAA,QACF;AACE,6BAAmB,QAAQ;AAAA,MAC9B;AAGD,UAAI,CAAC,SAAS,QAAQ;AACpB,mBAAW,QAAQ;AAAA,MACpB;AAAA,IACH;AAEA,UAAM,qBAAqB,CAAC,aAAa;AAEvC,UAAI,CAAC,SAAS,QAAQ;AACpB,mBAAW,QAAQ;AAAA,MACpB;AAGD,UAAI,SAAS,cAAc;AACzB,mBAAW,iEAAiE,SAAS,aAAa,EAAE,EAAE;AAAA,MAC1G,OAAS;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,SAAS;AAAA,UAChB,SAAS,SAAS;AAAA,UAClB,YAAY;AAAA,QAClB,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,aAAa,CAAC,aAAa;AAC/B,YAAM,QAAQ,cAAc,MAAM,UAAU,UAAQ,KAAK,OAAO,SAAS,EAAE;AAC3E,UAAI,UAAU,IAAI;AAChB,sBAAc,MAAM,KAAK,EAAE,SAAS;AAAA,MACrC;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,aAAa;AACnCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AACf,kBAAM,QAAQ,cAAc,MAAM,UAAU,UAAQ,KAAK,OAAO,SAAS,EAAE;AAC3E,gBAAI,UAAU,IAAI;AAChB,4BAAc,MAAM,OAAO,OAAO,CAAC;AAAA,YACpC;AAEDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AACzB,oBAAc,MAAM;IACtB;AAEA,UAAM,gBAAgB,MAAM;AAC1B,oBAAc,MAAM;IACtB;AAEA,UAAM,gBAAgB,CAAC,QAAQ;AAC7B,eAAS,MAAM,GAAG,IAAI,CAAC,SAAS,MAAM,GAAG;AAAA,IAC3C;AAEA,UAAM,uBAAuB,CAAC,MAAM;AAClC,wBAAkB,QAAQ,EAAE,OAAO;AAAA,IACrC;AAEA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AACf,0BAAc,QAAQ,cAAc,MAAM,OAAO,UAAQ,CAAC,KAAK,MAAM;AAErEA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AACf,0BAAc,QAAQ;AAEtBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAEA,UAAM,aAAa,CAAC,QAAQ;AAC1BA,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3gBA,GAAG,WAAW,eAAe;"}