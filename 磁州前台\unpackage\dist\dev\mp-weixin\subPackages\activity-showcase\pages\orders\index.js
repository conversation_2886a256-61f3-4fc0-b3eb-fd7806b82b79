"use strict";
const common_vendor = require("../../../../common/vendor.js");
const common_assets = require("../../../../common/assets.js");
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_circle + _component_path + _component_svg)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const isRefreshing = common_vendor.ref(false);
    const orderList = common_vendor.ref([]);
    const mockOrders = [
      {
        id: "202405120001",
        shopName: "磁州活动中心",
        status: "pending_payment",
        createTime: "2024-05-12 14:30",
        totalAmount: 199,
        items: [
          {
            id: 1,
            name: "夏季亲子户外拓展活动",
            specification: "2大1小家庭套餐",
            price: 199,
            quantity: 1,
            image: "/static/demo/activity1.jpg"
          }
        ]
      },
      {
        id: "202405110002",
        shopName: "磁州文化馆",
        status: "pending_delivery",
        createTime: "2024-05-11 10:15",
        totalAmount: 98,
        items: [
          {
            id: 2,
            name: "传统文化体验课",
            specification: "单人票",
            price: 49,
            quantity: 2,
            image: "/static/demo/activity2.jpg"
          }
        ]
      },
      {
        id: "202405100003",
        shopName: "磁州体育中心",
        status: "pending_receipt",
        createTime: "2024-05-10 16:45",
        totalAmount: 150,
        items: [
          {
            id: 3,
            name: "篮球训练营",
            specification: "月卡",
            price: 150,
            quantity: 1,
            image: "/static/demo/activity3.jpg"
          }
        ]
      },
      {
        id: "202405090004",
        shopName: "磁州美食街",
        status: "pending_review",
        createTime: "2024-05-09 19:20",
        totalAmount: 88,
        items: [
          {
            id: 4,
            name: "美食节门票",
            specification: "双人票",
            price: 88,
            quantity: 1,
            image: "/static/demo/activity4.jpg"
          }
        ]
      },
      {
        id: "202405080005",
        shopName: "磁州艺术中心",
        status: "completed",
        createTime: "2024-05-08 13:10",
        totalAmount: 120,
        items: [
          {
            id: 5,
            name: "油画体验课",
            specification: "单人票",
            price: 120,
            quantity: 1,
            image: "/static/demo/activity5.jpg"
          }
        ]
      }
    ];
    const orderTabs = [
      { name: "全部", status: "all", emptyText: "暂无订单", emptyImage: "/static/images/empty-orders.png" },
      { name: "待付款", status: "pending_payment", emptyText: "暂无待付款订单", emptyImage: "/static/images/empty-payment.png" },
      { name: "待发货", status: "pending_delivery", emptyText: "暂无待发货订单", emptyImage: "/static/images/empty-delivery.png" },
      { name: "待收货", status: "pending_receipt", emptyText: "暂无待收货订单", emptyImage: "/static/images/empty-receipt.png" },
      { name: "待评价", status: "pending_review", emptyText: "暂无待评价订单", emptyImage: "/static/images/empty-review.png" }
    ];
    common_vendor.onMounted(() => {
      loadOrders();
    });
    const loadOrders = () => {
      orderList.value = mockOrders;
    };
    const getOrdersByStatus = (status) => {
      if (status === "all") {
        return orderList.value;
      }
      return orderList.value.filter((order) => order.status === status);
    };
    const switchTab = (index) => {
      currentTab.value = index;
    };
    const onSwiperChange = (e) => {
      currentTab.value = e.detail.current;
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      setTimeout(() => {
        loadOrders();
        isRefreshing.value = false;
      }, 1e3);
    };
    const loadMore = () => {
      common_vendor.index.__f__("log", "at subPackages/activity-showcase/pages/orders/index.vue:284", "加载更多订单");
    };
    const getTotalQuantity = (order) => {
      return order.items.reduce((total, item) => total + item.quantity, 0);
    };
    const getStatusText = (status) => {
      const statusMap = {
        "pending_payment": "待付款",
        "pending_delivery": "待发货",
        "pending_receipt": "待收货",
        "pending_review": "待评价",
        "completed": "已完成",
        "cancelled": "已取消",
        "after_sale": "售后中"
      };
      return statusMap[status] || "未知状态";
    };
    const getStatusColor = (status) => {
      const colorMap = {
        "pending_payment": "#FF9500",
        "pending_delivery": "#5AC8FA",
        "pending_receipt": "#5AC8FA",
        "pending_review": "#34C759",
        "completed": "#8E8E93",
        "cancelled": "#8E8E93",
        "after_sale": "#FF3B30"
      };
      return colorMap[status] || "#8E8E93";
    };
    const getPrimaryActionText = (status) => {
      const actionMap = {
        "pending_payment": "立即付款",
        "pending_delivery": "提醒发货",
        "pending_receipt": "确认收货",
        "pending_review": "去评价",
        "completed": "再次购买",
        "cancelled": "删除订单",
        "after_sale": "查看进度"
      };
      return actionMap[status] || "查看详情";
    };
    const getPrimaryActionBgColor = (status) => {
      const bgColorMap = {
        "pending_payment": "linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)",
        "pending_delivery": "linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)",
        "pending_receipt": "linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)",
        "pending_review": "linear-gradient(135deg, #34C759 0%, #7ED321 100%)",
        "completed": "linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)",
        "cancelled": "linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)",
        "after_sale": "linear-gradient(135deg, #FF3B30 0%, #FF9580 100%)"
      };
      return bgColorMap[status] || "linear-gradient(135deg, #5AC8FA 0%, #90E0FF 100%)";
    };
    const handlePrimaryAction = (order) => {
      switch (order.status) {
        case "pending_payment":
          navigateTo(`/subPackages/activity-showcase/pages/payment/index?orderId=${order.id}`);
          break;
        case "pending_delivery":
          remindDelivery();
          break;
        case "pending_receipt":
          confirmReceipt(order);
          break;
        case "pending_review":
          navigateTo(`/subPackages/activity-showcase/pages/review/index?orderId=${order.id}`);
          break;
        case "completed":
          buyAgain();
          break;
        case "cancelled":
          deleteOrder(order);
          break;
        default:
          viewOrderDetail(order);
      }
    };
    const viewOrderDetail = (order) => {
      navigateTo(`/subPackages/activity-showcase/pages/orders/detail?id=${order.id}`);
    };
    const cancelOrder = (order) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确认取消该订单吗？",
        success: function(res) {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "订单已取消",
              icon: "success"
            });
            const index = orderList.value.findIndex((item) => item.id === order.id);
            if (index !== -1) {
              orderList.value[index].status = "cancelled";
            }
          }
        }
      });
    };
    const remindDelivery = (order) => {
      common_vendor.index.showToast({
        title: "已提醒商家发货",
        icon: "success"
      });
    };
    const confirmReceipt = (order) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确认已收到商品吗？",
        success: function(res) {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "确认收货成功",
              icon: "success"
            });
            const index = orderList.value.findIndex((item) => item.id === order.id);
            if (index !== -1) {
              orderList.value[index].status = "pending_review";
            }
          }
        }
      });
    };
    const buyAgain = (order) => {
      common_vendor.index.showToast({
        title: "已添加到购物车",
        icon: "success"
      });
    };
    const deleteOrder = (order) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确认删除该订单吗？",
        success: function(res) {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "订单已删除",
              icon: "success"
            });
            const index = orderList.value.findIndex((item) => item.id === order.id);
            if (index !== -1) {
              orderList.value.splice(index, 1);
            }
          }
        }
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_vendor.p({
          cx: "11",
          cy: "11",
          r: "8",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        d: common_vendor.p({
          d: "M21 21l-4.35-4.35",
          stroke: "#FFFFFF",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "24",
          height: "24"
        }),
        f: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/search/index?type=orders")),
        g: common_vendor.f(orderTabs, (tab, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab.name),
            b: currentTab.value === index
          }, currentTab.value === index ? {} : {}, {
            c: index,
            d: currentTab.value === index ? 1 : "",
            e: common_vendor.o(($event) => switchTab(index), index)
          });
        }),
        h: common_vendor.f(orderTabs, (tab, tabIndex, i0) => {
          return common_vendor.e({
            a: common_vendor.f(getOrdersByStatus(tab.status), (order, k1, i1) => {
              return common_vendor.e({
                a: "1e56167c-4-" + i0 + "-" + i1 + "," + ("1e56167c-3-" + i0 + "-" + i1),
                b: "1e56167c-5-" + i0 + "-" + i1 + "," + ("1e56167c-3-" + i0 + "-" + i1),
                c: "1e56167c-3-" + i0 + "-" + i1,
                d: common_vendor.t(order.shopName),
                e: "1e56167c-7-" + i0 + "-" + i1 + "," + ("1e56167c-6-" + i0 + "-" + i1),
                f: "1e56167c-6-" + i0 + "-" + i1,
                g: common_vendor.t(getStatusText(order.status)),
                h: getStatusColor(order.status),
                i: common_vendor.f(order.items, (item, itemIndex, i2) => {
                  return {
                    a: item.image,
                    b: common_vendor.t(item.name),
                    c: common_vendor.t(item.specification),
                    d: common_vendor.t(item.price.toFixed(2)),
                    e: common_vendor.t(item.quantity),
                    f: itemIndex
                  };
                }),
                j: common_vendor.t(getTotalQuantity(order)),
                k: common_vendor.t(order.totalAmount.toFixed(2)),
                l: order.status === "pending_payment"
              }, order.status === "pending_payment" ? {
                m: common_vendor.o(($event) => cancelOrder(order), order.id)
              } : {}, {
                n: common_vendor.t(getPrimaryActionText(order.status)),
                o: getPrimaryActionBgColor(order.status),
                p: common_vendor.o(($event) => handlePrimaryAction(order), order.id),
                q: order.id,
                r: common_vendor.o(($event) => viewOrderDetail(order), order.id)
              });
            }),
            b: getOrdersByStatus(tab.status).length === 0
          }, getOrdersByStatus(tab.status).length === 0 ? {
            c: tab.emptyImage || "/static/images/empty-orders.png",
            d: common_vendor.t(tab.emptyText || "暂无相关订单"),
            e: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/index/index"), tabIndex)
          } : {}, {
            f: common_vendor.o(onRefresh, tabIndex),
            g: common_vendor.o(loadMore, tabIndex),
            h: tabIndex
          });
        }),
        i: common_vendor.p({
          d: "M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        j: common_vendor.p({
          d: "M9 22V12h6v10",
          stroke: "#5AC8FA",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        k: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        l: common_vendor.p({
          d: "M9 18l6-6-6-6",
          stroke: "#999999",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "16",
          height: "16"
        }),
        n: isRefreshing.value,
        o: currentTab.value,
        p: common_vendor.o(onSwiperChange)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1e56167c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/pages/orders/index.js.map
