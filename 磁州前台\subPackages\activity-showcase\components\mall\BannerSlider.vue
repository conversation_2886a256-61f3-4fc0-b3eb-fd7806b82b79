<template>
  <view class="banner-slider" :style="sliderStyle">
    <swiper 
      class="banner-swiper" 
      :indicator-dots="showIndicator" 
      :autoplay="autoplay" 
      :interval="interval" 
      :duration="duration"
      :circular="circular"
      @change="onSwiperChange"
    >
      <swiper-item v-for="(banner, index) in banners" :key="index" @click="onBannerClick(banner)">
        <image class="banner-image" :src="banner.image" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
    
    <!-- 自定义指示器 -->
    <view class="custom-indicator" v-if="showCustomIndicator">
      <view 
        v-for="(banner, index) in banners" 
        :key="index"
        class="indicator-dot"
        :class="{ active: currentIndex === index }"
      ></view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

// 组件属性定义
const props = defineProps({
  banners: {
    type: Array,
    default: () => []
  },
  showIndicator: {
    type: Boolean,
    default: false
  },
  showCustomIndicator: {
    type: Boolean,
    default: true
  },
  autoplay: {
    type: Boolean,
    default: true
  },
  interval: {
    type: Number,
    default: 5000
  },
  duration: {
    type: Number,
    default: 500
  },
  circular: {
    type: Boolean,
    default: true
  },
  sliderStyle: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
defineEmits(['click']);

// 当前轮播索引
const currentIndex = ref(0);

// 轮播变化
const onSwiperChange = (e) => {
  currentIndex.value = e.detail.current;
};

// 点击轮播图
const onBannerClick = (banner) => {
  $emit('click', banner);
};
</script>

<style lang="scss" scoped>
.banner-slider {
  width: 100%;
  height: 300rpx;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  margin-bottom: 20rpx;
  box-shadow: 0 6px 16px rgba(0,0,0,0.1);
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
}

.custom-indicator {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  
  .indicator-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 8rpx;
    background-color: rgba(255,255,255,0.6);
    margin: 0 8rpx;
    transition: all 0.3s ease;
    
    &.active {
      width: 32rpx;
      background-color: #FF3B69;
    }
  }
}
</style> 