<template>
  <view class="points-management-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-left">
        <view class="back-button" @tap="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">
        <text class="title-text">积分商城管理</text>
      </view>
      <view class="navbar-right">
        <!-- 不再使用帮助按钮，保持与营销中心一致 -->
      </view>
    </view>
    
    <!-- 顶部操作区 -->
    <view class="top-actions">
      <CreateButton text="创建积分商品" theme="points" @click="createPointsItem" />
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view scroll-y class="content-area">
      <!-- 积分概览区域 -->
      <view class="overview-section">
        <view class="overview-header">
          <text class="section-title">积分概览</text>
          <view class="date-picker">
            <text class="date-text">近30天</text>
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="overview-cards">
          <view class="overview-card">
            <view class="card-value">{{pointsData.totalPoints}}</view>
            <view class="card-label">累计发放积分</view>
          </view>
          <view class="overview-card">
            <view class="card-value">{{pointsData.usedPoints}}</view>
            <view class="card-label">已使用积分</view>
          </view>
          <view class="overview-card">
            <view class="card-value">{{pointsData.activeUsers}}</view>
            <view class="card-label">活跃用户数</view>
          </view>
          <view class="overview-card">
            <view class="card-value">{{pointsData.conversionRate}}%</view>
            <view class="card-label">兑换转化率</view>
          </view>
        </view>
      </view>
      
      <!-- 积分商品列表 -->
      <view class="points-items-section">
        <view class="section-header">
          <text class="section-title">积分商品</text>
          <view class="filter-buttons">
            <view class="filter-button" :class="{ active: activeFilter === 'all' }" @tap="setFilter('all')">全部</view>
            <view class="filter-button" :class="{ active: activeFilter === 'active' }" @tap="setFilter('active')">进行中</view>
            <view class="filter-button" :class="{ active: activeFilter === 'draft' }" @tap="setFilter('draft')">草稿</view>
            <view class="filter-button" :class="{ active: activeFilter === 'ended' }" @tap="setFilter('ended')">已结束</view>
          </view>
        </view>

        <!-- 商品列表 -->
        <view class="points-items-list">
          <view class="points-item" v-for="(item, index) in filteredItems" :key="index" @tap="viewItemDetail(item)">
            <view class="item-image-container">
              <image class="item-image" :src="item.image" mode="aspectFill"></image>
              <view class="item-status" :class="item.statusClass">{{item.statusText}}</view>
            </view>
            <view class="item-content">
              <view class="item-name">{{item.name}}</view>
              <view class="item-points">
                <text class="points-value">{{item.points}}</text>
                <text class="points-label">积分</text>
              </view>
              <view class="item-stats">
                <view class="stat-item">
                  <text class="stat-value">{{item.stock}}</text>
                  <text class="stat-label">库存</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">{{item.redeemed}}</text>
                  <text class="stat-label">已兑换</text>
                </view>
                <view class="stat-item">
                  <text class="stat-value">{{item.views}}</text>
                  <text class="stat-label">浏览</text>
                </view>
              </view>
              <view class="item-time">{{item.timeRange}}</view>
            </view>
            <view class="item-actions">
              <view class="action-button edit" @tap.stop="editItem(item)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
              <view class="action-button delete" @tap.stop="deleteItem(item)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3 6H5H21" stroke="#FF3B30" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF3B30" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="filteredItems.length === 0">
          <image class="empty-image" src="/static/images/empty-points.png" mode="aspectFit"></image>
          <text class="empty-text">暂无{{filterLabels[activeFilter]}}积分商品</text>
        </view>
      </view>

      <!-- 积分规则设置 -->
      <view class="points-rules-section">
        <view class="section-header">
          <text class="section-title">积分规则设置</text>
          <view class="edit-button" @tap="editRules">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="#5E5CE6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <text>编辑</text>
          </view>
        </view>

        <view class="rules-list">
          <view class="rule-item" v-for="(rule, index) in pointsRules" :key="index">
            <view class="rule-icon" :class="rule.type">
              <svg v-if="rule.type === 'purchase'" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 6H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M16 10C16 11.0609 15.5786 12.0783 14.8284 12.8284C14.0783 13.5786 13.0609 14 12 14C10.9391 14 9.92172 13.5786 9.17157 12.8284C8.42143 12.0783 8 11.0609 8 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-if="rule.type === 'checkin'" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 11L12 14L22 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 12V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-if="rule.type === 'share'" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8.59 13.51L15.42 17.49" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15.41 6.51L8.59 10.49" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
            <view class="rule-content">
              <view class="rule-title">{{rule.title}}</view>
              <view class="rule-desc">{{rule.description}}</view>
            </view>
            <view class="rule-points">+{{rule.points}}</view>
          </view>
        </view>
      </view>

      
      <!-- 底部空间 -->
      <view class="bottom-space"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import CreateButton from '/subPackages/merchant-admin-marketing/components/CreateButton.vue';

// 积分数据
const pointsData = reactive({
  totalPoints: '125,680',
  usedPoints: '87,456',
  activeUsers: '1,254',
  conversionRate: '68.5'
});

// 筛选相关
const activeFilter = ref('all');
const filterLabels = {
  all: '全部',
  active: '进行中',
  draft: '草稿',
  ended: '已结束'
};

// 积分商品数据
const pointsItems = ref([
  {
    id: 1,
    name: '精美保温杯',
    points: 2000,
    stock: 100,
    redeemed: 35,
    views: 1240,
    image: '/static/images/points-item1.jpg',
    timeRange: '2023-05-01 ~ 2023-12-31',
    status: 'active',
    statusText: '进行中',
    statusClass: 'status-active'
  },
  {
    id: 2,
    name: '无线蓝牙耳机',
    points: 5000,
    stock: 50,
    redeemed: 12,
    views: 876,
    image: '/static/images/points-item2.jpg',
    timeRange: '2023-05-15 ~ 2023-12-31',
    status: 'active',
    statusText: '进行中',
    statusClass: 'status-active'
  },
  {
    id: 3,
    name: '会员月卡',
    points: 1000,
    stock: 999,
    redeemed: 128,
    views: 3560,
    image: '/static/images/points-item3.jpg',
    timeRange: '2023-04-01 ~ 2023-12-31',
    status: 'active',
    statusText: '进行中',
    statusClass: 'status-active'
  },
  {
    id: 4,
    name: '限定礼盒套装',
    points: 8000,
    stock: 20,
    redeemed: 0,
    views: 0,
    image: '/static/images/points-item4.jpg',
    timeRange: '未发布',
    status: 'draft',
    statusText: '草稿',
    statusClass: 'status-draft'
  },
  {
    id: 5,
    name: '春节限定福袋',
    points: 3000,
    stock: 200,
    redeemed: 200,
    views: 4280,
    image: '/static/images/points-item5.jpg',
    timeRange: '2023-01-15 ~ 2023-02-15',
    status: 'ended',
    statusText: '已结束',
    statusClass: 'status-ended'
  }
]);

// 根据筛选条件过滤商品
const filteredItems = computed(() => {
  if (activeFilter.value === 'all') {
    return pointsItems.value;
  }
  return pointsItems.value.filter(item => item.status === activeFilter.value);
});

// 设置筛选条件
const setFilter = (filter) => {
  activeFilter.value = filter;
};

// 查看商品详情
const viewItemDetail = (item) => {
  uni.showToast({
    title: `查看商品: ${item.name}`,
    icon: 'none'
  });
};

// 编辑商品
const editItem = (item) => {
  uni.showToast({
    title: `编辑商品: ${item.name}`,
    icon: 'none'
  });
};

// 删除商品
const deleteItem = (item) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除"${item.name}"吗？`,
    success: (res) => {
      if (res.confirm) {
        // 在实际应用中应该调用API删除商品
        const index = pointsItems.value.findIndex(i => i.id === item.id);
        if (index !== -1) {
          pointsItems.value.splice(index, 1);
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    }
  });
};

// 积分规则数据
const pointsRules = ref([
  {
    id: 1,
    type: 'purchase',
    title: '商品购买',
    description: '每消费1元获得1积分',
    points: 1
  },
  {
    id: 2,
    type: 'checkin',
    title: '每日签到',
    description: '每日签到获得积分奖励',
    points: 10
  },
  {
    id: 3,
    type: 'share',
    title: '分享商品',
    description: '分享商品到社交媒体获得积分',
    points: 5
  }
]);

// 编辑积分规则
const editRules = () => {
  uni.navigateTo({
    url: '/subPackages/merchant-admin-marketing/pages/marketing/points/rules'
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 创建积分商品
const createPointsItem = () => {
  uni.showToast({
    title: '创建新的积分商品',
    icon: 'none',
    duration: 2000
  });
  
  // 跳转到创建页面
  setTimeout(() => {
    uni.navigateTo({
      url: '/subPackages/merchant-admin-marketing/pages/marketing/points/create'
    });
  }, 500);
};

// 页面加载
onMounted(() => {
  console.log('积分商城管理页面已加载');
});
</script>

<style lang="scss">
/* 页面容器 */
.points-management-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}

.navbar-left {
  width: 40px;
}

.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
}

.navbar-right {
  width: 40px;
}

/* 顶部操作区 */
.top-actions {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: calc(100vh - 80px);
}

/* 概览区块 */
.overview-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 16px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.date-picker {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  border-radius: 16px;
  padding: 6px 12px;
}

.date-text {
  font-size: 12px;
  color: #666666;
}

.arrow-icon {
  width: 12px;
  height: 12px;
  border-right: 2px solid #999;
  border-bottom: 2px solid #999;
  margin-left: 4px;
  transform: rotate(45deg);
}

.overview-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.overview-card {
  width: 50%;
  padding: 10px;
  box-sizing: border-box;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
}

.card-label {
  font-size: 14px;
  color: #999999;
}

/* 积分商品列表 */
.points-items-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filter-buttons {
  display: flex;
  align-items: center;
}

.filter-button {
  padding: 8px 16px;
  border-radius: 16px;
  background-color: #F5F7FA;
  margin-left: 8px;
  font-size: 14px;
}

.filter-button.active {
  background-color: #FF7600;
  color: white;
}

.points-items-list {
  margin-bottom: 16px;
}

.points-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #F0F0F0;
  position: relative;
}

.item-image-container {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 16px;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-status {
  position: absolute;
  top: 0;
  left: 0;
  padding: 4px 8px;
  font-size: 12px;
  color: white;
  z-index: 2;
}

.status-active {
  background-color: #34C759;
}

.status-draft {
  background-color: #999999;
}

.status-ended {
  background-color: #FF3B30;
}

.item-content {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
}

.item-points {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.points-value {
  font-size: 16px;
  font-weight: 600;
  color: #FF7600;
  margin-right: 4px;
}

.points-label {
  font-size: 14px;
  color: #999999;
}

.item-stats {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.stat-item {
  margin-right: 16px;
  display: flex;
  align-items: center;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  margin-right: 4px;
}

.stat-label {
  font-size: 12px;
  color: #999999;
}

.item-time {
  font-size: 12px;
  color: #999999;
}

.item-actions {
  display: flex;
  align-items: center;
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.action-button.edit {
  background-color: rgba(94, 92, 230, 0.1);
}

.action-button.delete {
  background-color: rgba(255, 59, 48, 0.1);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  text-align: center;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
  margin-bottom: 16px;
}

/* 积分规则 */
.points-rules-section {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.edit-button {
  display: flex;
  align-items: center;
  color: #5E5CE6;
  font-size: 14px;
}

.edit-button text {
  margin-left: 4px;
}

.rules-list {
  margin-top: 16px;
}

.rule-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.rule-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.rule-icon.purchase {
  background-color: rgba(25, 137, 250, 0.1);
  color: #1989FA;
}

.rule-icon.checkin {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.rule-icon.share {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.rule-content {
  flex: 1;
}

.rule-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}

.rule-desc {
  font-size: 12px;
  color: #999999;
}

.rule-points {
  font-size: 16px;
  font-weight: 600;
  color: #FF7600;
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}
</style> 