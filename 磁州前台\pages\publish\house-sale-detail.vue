<template>
  <view class="house-sale-container">
    <!-- 添加自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">房屋出售详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <view class="house-sale-wrapper">
      <!-- 房屋基本信息卡片 -->
      <view class="content-card house-info-card">
        <view class="house-header">
          <view class="house-title-row">
            <text class="house-title">{{houseData.title}}</text>
            <text class="house-price">{{houseData.price}}</text>
          </view>
          <view class="house-meta">
            <view class="house-tag-group">
              <view class="house-tag" v-for="(tag, index) in houseData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="house-publish-time">发布于 {{formatTime(houseData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 房屋图片轮播 -->
        <swiper class="house-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
          <swiper-item v-for="(image, index) in houseData.images" :key="index">
            <image :src="image" mode="aspectFill" class="house-image"></image>
          </swiper-item>
        </swiper>
        
        <!-- 基本信息 -->
        <view class="house-basic-info">
          <view class="info-item">
            <text class="info-label">房屋类型</text>
            <text class="info-value">{{houseData.type}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">所在区域</text>
            <text class="info-value">{{houseData.area}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">房屋面积</text>
            <text class="info-value">{{houseData.size}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">房屋朝向</text>
            <text class="info-value">{{houseData.orientation}}</text>
          </view>
        </view>
      </view>
      
      <!-- 房屋配置 -->
      <view class="content-card house-config-card">
        <view class="section-title">房屋配置</view>
        <view class="config-list">
          <view class="config-item" v-for="(item, index) in houseData.configs" :key="index">
            <text class="config-icon iconfont" :class="item.icon"></text>
            <text class="config-text">{{item.name}}</text>
          </view>
        </view>
      </view>
      
      <!-- 房屋详情 -->
      <view class="content-card house-detail-card">
        <view class="section-title">房屋详情</view>
        <view class="detail-list">
          <view class="detail-item" v-for="(item, index) in houseData.details" :key="index">
            <text class="detail-label">{{item.label}}</text>
            <text class="detail-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 位置信息 -->
      <view class="content-card location-card">
        <view class="section-title">位置信息</view>
        <view class="location-content">
          <view class="location-address">
            <text class="address-icon iconfont icon-location"></text>
            <text class="address-text">{{houseData.location.address}}</text>
          </view>
          <view class="location-map">
            <map class="map" :latitude="houseData.location.latitude" :longitude="houseData.location.longitude" :markers="markers"></map>
          </view>
          <view class="location-surroundings">
            <view class="surrounding-item" v-for="(item, index) in houseData.location.surroundings" :key="index">
              <text class="surrounding-icon iconfont" :class="item.icon"></text>
              <text class="surrounding-text">{{item.name}}</text>
              <text class="surrounding-distance">{{item.distance}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 交易信息 -->
      <view class="content-card transaction-card">
        <view class="section-title">交易信息</view>
        <view class="transaction-content">
          <view class="transaction-item" v-for="(item, index) in houseData.transaction" :key="index">
            <text class="transaction-label">{{item.label}}</text>
            <text class="transaction-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 产权信息 -->
      <view class="content-card property-card">
        <view class="section-title">产权信息</view>
        <view class="property-content">
          <view class="property-item" v-for="(item, index) in houseData.property" :key="index">
            <text class="property-label">{{item.label}}</text>
            <text class="property-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 房源描述 -->
      <view class="content-card description-card">
        <view class="section-title">房源描述</view>
        <view class="description-content">
          <text class="description-text">{{houseData.description}}</text>
        </view>
      </view>
      
      <!-- 业主信息 -->
      <view class="content-card owner-card">
        <view class="owner-header">
          <view class="owner-avatar">
            <image :src="houseData.owner.avatar" mode="aspectFill"></image>
          </view>
          <view class="owner-info">
            <text class="owner-name">{{houseData.owner.name}}</text>
            <view class="owner-meta">
              <text class="owner-type">{{houseData.owner.type}}</text>
              <text class="owner-rating">信用等级 {{houseData.owner.rating}}</text>
            </view>
          </view>
          <view class="owner-auth" v-if="houseData.owner.isVerified">
            <text class="iconfont icon-verified"></text>
            <text class="auth-text">已认证</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="contact-header">
          <text class="section-title">联系方式</text>
        </view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{houseData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{houseData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 相关推荐模块 -->
      <view class="content-card related-houses-card">
        <view class="section-title">相关推荐</view>
        <view class="related-houses-content">
          <view class="related-houses-list">
            <view class="related-house-item" v-for="(house, index) in relatedHouses.slice(0, 3)" :key="index" @click="navigateToHouse(house.id)">
              <view class="house-item-content">
                <view class="house-item-left">
                  <image class="house-image" :src="house.image" mode="aspectFill"></image>
                </view>
                <view class="house-item-middle">
                  <text class="house-item-title">{{house.title}}</text>
                  <view class="house-item-meta">{{house.type}} · {{house.area}}</view>
                  <view class="house-item-tags">
                    <text class="house-item-tag" v-for="(tag, tagIndex) in house.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="house-item-tag-more" v-if="house.tags && house.tags.length > 2">+{{house.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="house-item-right">
                  <text class="house-item-price">{{house.price}}</text>
                </view>
              </view>
            </view>
            <view class="empty-related-houses" v-if="relatedHouses.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关推荐</text>
            </view>
          </view>
          <view class="view-more-btn" v-if="relatedHouses.length > 0" @click.stop="navigateToHouseList">
            <text class="view-more-text">查看更多房源</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 状态栏高度
const statusBarHeight = ref(20);

// 获取状态栏高度
onMounted(() => {
  try {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
  } catch (e) {
    console.error('获取状态栏高度失败', e);
  }
});

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  });
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);
const houseData = ref({
  id: 'house12345',
  title: '精装三室两厅出售',
  price: '120万',
  tags: ['精装修', '满五唯一', '学区房'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  images: [
    '/static/images/house1.jpg',
    '/static/images/house2.jpg',
    '/static/images/house3.jpg'
  ],
  type: '三室两厅',
  area: '磁县城区',
  size: '120平方米',
  orientation: '南北通透',
  configs: [
    { name: '空调', icon: 'icon-ac' },
    { name: '热水器', icon: 'icon-water-heater' },
    { name: '洗衣机', icon: 'icon-washer' },
    { name: '冰箱', icon: 'icon-fridge' },
    { name: '电视', icon: 'icon-tv' },
    { name: '宽带', icon: 'icon-wifi' }
  ],
  details: [
    { label: '装修情况', value: '精装修' },
    { label: '所在楼层', value: '6/18层' },
    { label: '建筑年代', value: '2018年' },
    { label: '房屋用途', value: '住宅' }
  ],
  location: {
    address: '磁县城区XX路XX号',
    latitude: 36.123456,
    longitude: 114.123456,
    surroundings: [
      { name: '地铁站', distance: '500米', icon: 'icon-subway' },
      { name: '公交站', distance: '200米', icon: 'icon-bus' },
      { name: '超市', distance: '300米', icon: 'icon-supermarket' },
      { name: '学校', distance: '800米', icon: 'icon-school' }
    ]
  },
  transaction: [
    { label: '房屋总价', value: '120万' },
    { label: '单价', value: '10000元/㎡' },
    { label: '首付比例', value: '30%' },
    { label: '月供参考', value: '约5000元' }
  ],
  property: [
    { label: '产权年限', value: '70年' },
    { label: '产权类型', value: '商品房' },
    { label: '产权状态', value: '满五唯一' },
    { label: '抵押情况', value: '无抵押' }
  ],
  description: '房屋位于磁县城区核心地段，交通便利，配套齐全。精装修，拎包入住。满五唯一，税费低。周边有地铁、公交、超市、学校等配套设施。',
  owner: {
    name: '王先生',
    avatar: '/static/images/avatar.png',
    type: '个人',
    rating: 'A+',
    isVerified: true
  },
  contact: {
    name: '王先生',
    phone: '13912345678'
  }
});

const markers = ref([{
  id: 1,
  latitude: houseData.value.location.latitude,
  longitude: houseData.value.location.longitude,
  title: houseData.value.title
}]);

const relatedHouses = ref([
  {
    id: 'house001',
    title: '精装三室两厅',
    price: '110万',
    type: '三室两厅',
    area: '磁县城区',
    image: '/static/images/house-similar1.jpg',
    tags: ['精装修', '学区房', '满五唯一']
  },
  {
    id: 'house002',
    title: '南北通透大三居',
    price: '128万',
    type: '三室两厅',
    area: '磁县城区',
    image: '/static/images/house-similar2.jpg',
    tags: ['南北通透', '带车位']
  },
  {
    id: 'house003',
    title: '高层景观房',
    price: '135万',
    type: '三室两厅',
    area: '磁县城区',
    image: '/static/images/house-similar3.jpg',
    tags: ['高层', '景观好']
  }
])

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: houseData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

const navigateToHouse = (id) => {
  if (id === houseData.value.id) return;
  uni.navigateTo({ url: `/pages/publish/house-sale-detail?id=${id}` });
}
const navigateToHouseList = (e) => {
  if (e) e.stopPropagation();
  const houseCategory = houseData.value.tags?.[0] || '';
  uni.navigateTo({ 
    url: `/subPackages/service/pages/filter?type=house_sell&title=${encodeURIComponent('房屋出售')}&category=${encodeURIComponent(houseCategory)}&active=house_sell` 
  });
}

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  if (!houseData.value.owner || !houseData.value.owner.id) {
    uni.showToast({
      title: '无法获取业主信息',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/index?userId=${houseData.value.owner.id}&username=${encodeURIComponent(houseData.value.owner.name || '业主')}`
  });
};

// 生命周期钩子
onMounted(() => {
  // 修改页面标题
  uni.setNavigationBarTitle({
    title: '房屋详情'
  });
});
</script>

<style>
.house-sale-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 150rpx;
}

.house-sale-wrapper {
  padding: 150rpx 20rpx 120rpx; /* 增加顶部内边距，为固定导航栏留出空间 */
}

.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-top: 20rpx; /* 添加与标题栏的间隙 */
}

/* 房屋基本信息卡片 */
.house-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.house-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.house-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}

.house-meta {
  margin-bottom: 24rpx;
}

.house-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.house-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.house-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.house-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.house-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.house-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 房屋配置 */
.config-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12rpx;
}

.config-item {
  width: 33.33%;
  padding: 12rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.config-icon {
  font-size: 32rpx;
  color: #1890ff;
  margin-right: 8rpx;
}

.config-text {
  font-size: 26rpx;
  color: #666;
}

/* 房屋详情 */
.detail-list {
  display: flex;
  flex-direction: column;
}

.detail-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 位置信息 */
.location-content {
  display: flex;
  flex-direction: column;
}

.location-address {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.address-icon {
  font-size: 32rpx;
  color: #1890ff;
  margin-right: 8rpx;
}

.address-text {
  font-size: 28rpx;
  color: #333;
}

.location-map {
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.map {
  width: 100%;
  height: 100%;
}

.location-surroundings {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.surrounding-item {
  width: 50%;
  padding: 8rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.surrounding-icon {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 8rpx;
}

.surrounding-text {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.surrounding-distance {
  font-size: 24rpx;
  color: #999;
}

/* 交易信息 */
.transaction-content {
  display: flex;
  flex-direction: column;
}

.transaction-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.transaction-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 产权信息 */
.property-content {
  display: flex;
  flex-direction: column;
}

.property-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.property-item:last-child {
  border-bottom: none;
}

.property-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.property-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 房源描述 */
.description-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 业主信息 */
.owner-header {
  display: flex;
  align-items: center;
}

.owner-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.owner-avatar image {
  width: 100%;
  height: 100%;
}

.owner-info {
  flex: 1;
}

.owner-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.owner-meta {
  display: flex;
  align-items: center;
}

.owner-type, .owner-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 联系方式 */
.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.contact-item {
  display: flex;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.contact-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.contact-phone {
  color: #1890ff;
}

.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}

.tips-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 8rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
}

/* 相关推荐模块 */
.related-houses-card {
  margin-top: 24rpx;
}

.related-houses-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.related-houses-list {
  margin-bottom: 24rpx;
}

.related-house-item {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.house-item-content {
  display: flex;
  align-items: center;
}

.house-item-left {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.house-item-middle {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.house-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.house-item-meta {
  font-size: 24rpx;
  color: #999;
}

.house-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.house-item-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
}

.house-item-tag-more {
  font-size: 24rpx;
  color: #999;
}

.house-item-right {
  width: 160rpx;
  font-size: 28rpx;
  color: #ff4d4f;
  font-weight: 500;
}

.empty-related-houses {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.view-more-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx;
  background-color: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
}

.view-more-text {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 8rpx;
}

.view-more-icon {
  font-size: 28rpx;
  color: #1890ff;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 原来的section-title样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}

.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style> 