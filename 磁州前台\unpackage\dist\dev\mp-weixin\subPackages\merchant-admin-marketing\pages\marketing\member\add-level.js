"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 表单数据
      levelForm: {
        name: "",
        icon: "",
        color: "linear-gradient(135deg, #8E2DE2, #4A00E0)",
        conditionType: "consumption",
        conditionValue: "",
        autoUpgrade: true,
        levelProtection: false,
        protectionDays: 30,
        discount: "9.5",
        pointsRatio: "1.2",
        customPrivileges: [""]
      },
      // 颜色选项
      colorOptions: [
        { name: "紫色", value: "linear-gradient(135deg, #8E2DE2, #4A00E0)" },
        { name: "金色", value: "linear-gradient(135deg, #FFC837, #FF8008)" },
        { name: "银色", value: "linear-gradient(135deg, #D4D4D8, #A1A1AA)" },
        { name: "蓝色", value: "linear-gradient(135deg, #2196F3, #0D47A1)" },
        { name: "红色", value: "linear-gradient(135deg, #FF5E3A, #FF2A68)" },
        { name: "绿色", value: "linear-gradient(135deg, #43E97B, #38F9D7)" }
      ],
      // 特权列表
      privileges: [
        { id: 1, name: "会员折扣", selected: true },
        { id: 2, name: "积分加速", selected: true },
        { id: 3, name: "免费配送", selected: false },
        { id: 4, name: "生日礼包", selected: true },
        { id: 5, name: "专属客服", selected: false },
        { id: 6, name: "优先发货", selected: false }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    chooseIcon() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.levelForm.icon = res.tempFilePaths[0];
        }
      });
    },
    selectColor(color) {
      this.levelForm.color = color;
    },
    setConditionType(type) {
      this.levelForm.conditionType = type;
    },
    toggleAutoUpgrade(e) {
      this.levelForm.autoUpgrade = e.detail.value;
    },
    toggleLevelProtection(e) {
      this.levelForm.levelProtection = e.detail.value;
    },
    togglePrivilege(privilege) {
      const index = this.privileges.findIndex((item) => item.id === privilege.id);
      if (index !== -1) {
        this.privileges[index].selected = !this.privileges[index].selected;
      }
    },
    addCustomPrivilege() {
      this.levelForm.customPrivileges.push("");
    },
    deleteCustomPrivilege(index) {
      if (this.levelForm.customPrivileges.length > 1) {
        this.levelForm.customPrivileges.splice(index, 1);
      } else {
        this.levelForm.customPrivileges = [""];
      }
    },
    validateForm() {
      if (!this.levelForm.name) {
        common_vendor.index.showToast({
          title: "请输入等级名称",
          icon: "none"
        });
        return false;
      }
      if (!this.levelForm.icon) {
        common_vendor.index.showToast({
          title: "请上传等级图标",
          icon: "none"
        });
        return false;
      }
      if (!this.levelForm.conditionValue) {
        common_vendor.index.showToast({
          title: `请输入${this.levelForm.conditionType === "consumption" ? "消费金额" : "成长值"}`,
          icon: "none"
        });
        return false;
      }
      return true;
    },
    saveLevel() {
      if (!this.validateForm())
        return;
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "会员等级添加成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.levelForm.name,
    c: common_vendor.o(($event) => $data.levelForm.name = $event.detail.value),
    d: $data.levelForm.icon
  }, $data.levelForm.icon ? {
    e: $data.levelForm.icon
  } : {}, {
    f: common_vendor.o((...args) => $options.chooseIcon && $options.chooseIcon(...args)),
    g: common_vendor.f($data.colorOptions, (color, index, i0) => {
      return {
        a: index,
        b: color.value,
        c: $data.levelForm.color === color.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectColor(color.value), index)
      };
    }),
    h: $data.levelForm.conditionType === "consumption" ? 1 : "",
    i: common_vendor.o(($event) => $options.setConditionType("consumption")),
    j: $data.levelForm.conditionType === "growth" ? 1 : "",
    k: common_vendor.o(($event) => $options.setConditionType("growth")),
    l: common_vendor.t($data.levelForm.conditionType === "consumption" ? "消费金额" : "成长值"),
    m: `请输入${$data.levelForm.conditionType === "consumption" ? "消费金额" : "成长值"}`,
    n: $data.levelForm.conditionValue,
    o: common_vendor.o(($event) => $data.levelForm.conditionValue = $event.detail.value),
    p: common_vendor.t($data.levelForm.conditionType === "consumption" ? "元" : "分"),
    q: $data.levelForm.autoUpgrade,
    r: common_vendor.o((...args) => $options.toggleAutoUpgrade && $options.toggleAutoUpgrade(...args)),
    s: $data.levelForm.levelProtection,
    t: common_vendor.o((...args) => $options.toggleLevelProtection && $options.toggleLevelProtection(...args)),
    v: $data.levelForm.levelProtection
  }, $data.levelForm.levelProtection ? {
    w: $data.levelForm.protectionDays,
    x: common_vendor.o(($event) => $data.levelForm.protectionDays = $event.detail.value)
  } : {}, {
    y: $data.levelForm.discount,
    z: common_vendor.o(($event) => $data.levelForm.discount = $event.detail.value),
    A: $data.levelForm.pointsRatio,
    B: common_vendor.o(($event) => $data.levelForm.pointsRatio = $event.detail.value),
    C: common_vendor.f($data.privileges, (privilege, index, i0) => {
      return common_vendor.e({
        a: privilege.selected
      }, privilege.selected ? {} : {}, {
        b: privilege.selected ? 1 : "",
        c: common_vendor.o(($event) => $options.togglePrivilege(privilege), index),
        d: common_vendor.t(privilege.name),
        e: index
      });
    }),
    D: common_vendor.f($data.levelForm.customPrivileges, (item, index, i0) => {
      return {
        a: $data.levelForm.customPrivileges[index],
        b: common_vendor.o(($event) => $data.levelForm.customPrivileges[index] = $event.detail.value, index),
        c: common_vendor.o(($event) => $options.deleteCustomPrivilege(index), index),
        d: index
      };
    }),
    E: common_vendor.o((...args) => $options.addCustomPrivilege && $options.addCustomPrivilege(...args)),
    F: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    G: common_vendor.o((...args) => $options.saveLevel && $options.saveLevel(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/add-level.js.map
