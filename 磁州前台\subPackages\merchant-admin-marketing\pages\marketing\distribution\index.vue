<template>
  <view class="distribution-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">分销系统</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 数据概览卡片 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">分销概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{distributionData.totalDistributors}}</view>
          <view class="card-label">分销员总数</view>
          <view class="card-trend" :class="distributionData.distributorsTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{distributionData.distributorsGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">¥{{formatNumber(distributionData.totalCommission)}}</view>
          <view class="card-label">累计佣金</view>
          <view class="card-trend" :class="distributionData.commissionTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{distributionData.commissionGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{distributionData.totalOrders}}</view>
          <view class="card-label">分销订单数</view>
          <view class="card-trend" :class="distributionData.ordersTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{distributionData.ordersGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">¥{{formatNumber(distributionData.averageCommission)}}</view>
          <view class="card-label">平均佣金</view>
          <view class="card-trend" :class="distributionData.averageTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{distributionData.averageGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 分销员榜单 -->
    <view class="leaderboard-section">
      <view class="section-header">
        <text class="section-title">分销员排行榜</text>
        <text class="view-all" @click="viewAllDistributors">查看全部</text>
      </view>
      
      <view class="leaderboard-list">
        <view class="leaderboard-item" v-for="(item, index) in topDistributors" :key="index" @click="viewDistributorDetail(item)">
          <view class="rank-badge" :class="{'top-rank': index < 3}">{{index + 1}}</view>
          <image class="distributor-avatar" :src="item.avatar" mode="aspectFill"></image>
          <view class="distributor-info">
            <view class="distributor-name">{{item.name}}</view>
            <view class="distributor-level">{{item.level}}</view>
          </view>
          <view class="distributor-stats">
            <view class="stat-item">
              <text class="stat-value">¥{{formatNumber(item.commission)}}</text>
              <text class="stat-label">佣金</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{item.orders}}</text>
              <text class="stat-label">订单</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 分销设置卡片 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-title">分销设置</text>
        <view class="switch-container">
          <text class="switch-label">分销功能</text>
          <switch :checked="distributionEnabled" @change="toggleDistribution" color="#6B0FBE" />
        </view>
      </view>
      
      <view class="settings-list">
        <view class="settings-item" @click="navigateToSetting('conditions')">
          <view class="item-left">
            <view class="item-icon conditions"></view>
            <text class="item-title">成为分销员条件</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{distributionSettings.conditionText}}</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('levels')">
          <view class="item-left">
            <view class="item-icon levels"></view>
            <text class="item-title">分销等级设置</text>
          </view>
          <view class="item-right">
            <text class="item-value">{{distributionSettings.levelCount}}个等级</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('withdrawal')">
          <view class="item-left">
            <view class="item-icon withdrawal"></view>
            <text class="item-title">提现设置</text>
          </view>
          <view class="item-right">
            <text class="item-value">最低¥{{distributionSettings.minWithdrawal}}元</text>
            <view class="item-arrow"></view>
          </view>
        </view>
        
        <view class="settings-item" @click="navigateToSetting('agreement')">
          <view class="item-left">
            <view class="item-icon agreement"></view>
            <text class="item-title">分销协议</text>
          </view>
          <view class="item-right">
            <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 佣金规则卡片 -->
    <view class="commission-section">
      <view class="section-header">
        <text class="section-title">佣金规则</text>
        <view class="edit-btn" @click="editCommissionRules">编辑</view>
      </view>
      
      <view class="commission-rules">
        <view class="rule-card">
          <view class="rule-header">
            <text class="rule-title">一级分销</text>
            <text class="rule-value">{{commissionRules.level1}}%</text>
          </view>
          <view class="rule-desc">直接推广获得订单金额的佣金比例</view>
        </view>
        
        <view class="rule-card">
          <view class="rule-header">
            <text class="rule-title">二级分销</text>
            <text class="rule-value">{{commissionRules.level2}}%</text>
          </view>
          <view class="rule-desc">间接推广获得订单金额的佣金比例</view>
        </view>
        
        <view class="rule-card" v-if="commissionRules.level3 > 0">
          <view class="rule-header">
            <text class="rule-title">三级分销</text>
            <text class="rule-value">{{commissionRules.level3}}%</text>
          </view>
          <view class="rule-desc">三级推广获得订单金额的佣金比例</view>
        </view>
      </view>
    </view>
    
    <!-- 推广工具卡片 -->
    <view class="promotion-section">
      <view class="section-header">
        <text class="section-title">推广工具</text>
      </view>
      
      <view class="promotion-tools">
        <view class="tool-card" v-for="(tool, index) in promotionTools" :key="index" @click="usePromotionTool(tool)">
          <image class="tool-icon" :src="tool.icon" mode="aspectFit"></image>
          <text class="tool-name">{{tool.name}}</text>
          <text class="tool-desc">{{tool.description}}</text>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="showActionMenu">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      distributionEnabled: true,
      
      // 分销数据概览
      distributionData: {
        totalDistributors: 268,
        distributorsTrend: 'up',
        distributorsGrowth: '12%',
        
        totalCommission: 26584.50,
        commissionTrend: 'up',
        commissionGrowth: '8.5%',
        
        totalOrders: 1256,
        ordersTrend: 'up',
        ordersGrowth: '15.2%',
        
        averageCommission: 99.20,
        averageTrend: 'down',
        averageGrowth: '3.1%'
      },
      
      // 分销员排行榜
      topDistributors: [
        {
          id: 1,
          name: '张小明',
          avatar: '/static/images/avatar-1.png',
          level: '钻石分销员',
          commission: 5862.50,
          orders: 86
        },
        {
          id: 2,
          name: '李佳怡',
          avatar: '/static/images/avatar-2.png',
          level: '金牌分销员',
          commission: 4235.80,
          orders: 64
        },
        {
          id: 3,
          name: '王大力',
          avatar: '/static/images/avatar-3.png',
          level: '金牌分销员',
          commission: 3756.20,
          orders: 58
        },
        {
          id: 4,
          name: '赵丽丽',
          avatar: '/static/images/avatar-4.png',
          level: '银牌分销员',
          commission: 2845.60,
          orders: 42
        },
        {
          id: 5,
          name: '陈小红',
          avatar: '/static/images/avatar-5.png',
          level: '银牌分销员',
          commission: 2356.80,
          orders: 35
        }
      ],
      
      // 分销设置
      distributionSettings: {
        conditionText: '购买商品并申请',
        levelCount: 3,
        minWithdrawal: 50
      },
      
      // 佣金规则
      commissionRules: {
        level1: 15,
        level2: 5,
        level3: 2
      },
      
      // 推广工具
      promotionTools: [
        {
          id: 1,
          name: '推广海报',
          description: '生成专属推广海报',
          icon: '/static/images/poster-icon.png'
        },
        {
          id: 2,
          name: '推广二维码',
          description: '生成专属推广二维码',
          icon: '/static/images/qrcode-icon.png'
        },
        {
          id: 3,
          name: '商品推广卡片',
          description: '生成商品推广卡片',
          icon: '/static/images/product-card-icon.png'
        },
        {
          id: 4,
          name: '分销员邀请',
          description: '邀请好友成为分销员',
          icon: '/static/images/invite-icon.png'
        },
        {
          id: 5,
          name: '推广文案',
          description: '获取推广文案模板',
          icon: '/static/images/text-icon.png'
        },
        {
          id: 6,
          name: '推广活动',
          description: '管理推广营销活动',
          icon: '/static/images/activity-icon.png'
        },
        {
          id: 7,
          name: '素材库',
          description: '分销推广素材管理',
          icon: '/static/images/materials-icon.png'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showHelp() {
      uni.showModal({
        title: '分销系统帮助',
        content: '分销系统是一种营销模式，通过发展分销员推广商品，获取佣金的方式促进销售增长。',
        showCancel: false
      });
    },
    
    formatNumber(number) {
      return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    
    showDatePicker() {
      // 实现日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    
    viewAllDistributors() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors'
      });
    },
    
    viewDistributorDetail(distributor) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${distributor.id}`
      });
    },
    
    toggleDistribution(e) {
      this.distributionEnabled = e.detail.value;
      uni.showToast({
        title: this.distributionEnabled ? '分销功能已开启' : '分销功能已关闭',
        icon: 'none'
      });
    },
    
    navigateToSetting(type) {
      const routes = {
        conditions: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions',
        levels: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/levels',
        withdrawal: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal',
        agreement: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement'
      };
      
      uni.navigateTo({
        url: routes[type]
      });
    },
    
    editCommissionRules() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules'
      });
    },
    
    usePromotionTool(tool) {
      // 根据工具ID跳转到不同页面
      let url = '';
      
      switch(tool.id) {
        case 1: // 推广海报
          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool?id=1&name=推广海报';
          break;
        case 2: // 推广二维码
          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode';
          break;
        case 3: // 商品推广卡片
          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/product-cards';
          break;
        case 4: // 分销员渠道
          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/channels';
          break;
        case 5: // 推广文案
          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-text';
          break;
        case 6: // 推广活动
          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-activity';
          break;
        case 7: // 素材库
          url = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-materials';
          break;
        default:
          url = `/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool?id=${tool.id}&name=${tool.name}`;
      }
      
      uni.navigateTo({
        url: url
      });
    },
    
    showActionMenu() {
      uni.showActionSheet({
        itemList: ['新增分销员', '导出分销数据', '佣金发放', '分销活动创建'],
        success: (res) => {
          const actions = [
            () => this.addDistributor(),
            () => this.exportData(),
            () => this.payCommission(),
            () => this.createActivity()
          ];
          
          actions[res.tapIndex]();
        }
      });
    },
    
    addDistributor() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/add-distributor'
      });
    },
    
    exportData() {
      uni.showLoading({
        title: '导出中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '数据导出成功',
          icon: 'success'
        });
      }, 1500);
    },
    
    payCommission() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/pay-commission'
      });
    },
    
    createActivity() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/create-activity'
      });
    }
  }
}
</script>

<style lang="scss">
.distribution-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.view-all {
  font-size: 14px;
  color: #6B0FBE;
}

.edit-btn {
  background: rgba(107, 15, 190, 0.1);
  color: #6B0FBE;
  font-size: 14px;
  padding: 5px 12px;
  border-radius: 15px;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #6B0FBE;
}

.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}

.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}

.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 排行榜样式 */
.leaderboard-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.leaderboard-list {
  margin-top: 10px;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.leaderboard-item:last-child {
  border-bottom: none;
}

.rank-badge {
  width: 24px;
  height: 24px;
  background: #F5F7FA;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: #999;
  margin-right: 10px;
}

.rank-badge.top-rank {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
}

.distributor-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
  background-color: #f5f5f5;
}

.distributor-info {
  flex: 1;
}

.distributor-name {
  font-size: 15px;
  color: #333;
  margin-bottom: 3px;
}

.distributor-level {
  font-size: 12px;
  color: #6B0FBE;
  background: rgba(107, 15, 190, 0.1);
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
}

.distributor-stats {
  display: flex;
  margin-left: 10px;
}

.stat-item {
  margin-left: 15px;
  text-align: right;
}

.stat-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

/* 设置部分样式 */
.settings-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.switch-container {
  display: flex;
  align-items: center;
}

.switch-label {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.settings-list {
  margin-top: 10px;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.item-icon.conditions {
  background: rgba(255, 149, 0, 0.1);
}

.item-icon.conditions::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid #FF9500;
  border-radius: 10px;
  position: absolute;
}

.item-icon.levels {
  background: rgba(0, 122, 255, 0.1);
}

.item-icon.levels::before {
  content: '';
  width: 18px;
  height: 4px;
  background: #007AFF;
  position: absolute;
  top: 12px;
}

.item-icon.levels::after {
  content: '';
  width: 18px;
  height: 4px;
  background: #007AFF;
  position: absolute;
  top: 20px;
}

.item-icon.withdrawal {
  background: rgba(52, 199, 89, 0.1);
}

.item-icon.withdrawal::before {
  content: '';
  width: 16px;
  height: 10px;
  border: 2px solid #34C759;
  border-radius: 2px;
  position: absolute;
}

.item-icon.agreement {
  background: rgba(88, 86, 214, 0.1);
}

.item-icon.agreement::before {
  content: '';
  width: 14px;
  height: 18px;
  border: 2px solid #5856D6;
  border-radius: 2px;
  position: absolute;
}

.item-icon.agreement::after {
  content: '';
  width: 10px;
  height: 2px;
  background: #5856D6;
  position: absolute;
  top: 14px;
}

.item-title {
  font-size: 15px;
  color: #333;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 14px;
  color: #999;
  margin-right: 10px;
}

.item-arrow {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #999;
  border-right: 1.5px solid #999;
  transform: rotate(45deg);
}

/* 佣金规则样式 */
.commission-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.commission-rules {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.rule-card {
  width: calc(100% / 3);
  padding: 7.5px;
  box-sizing: border-box;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.rule-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.rule-value {
  font-size: 16px;
  font-weight: 600;
  color: #6B0FBE;
}

.rule-desc {
  font-size: 12px;
  color: #999;
  height: 32px;
}

/* 推广工具样式 */
.promotion-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.promotion-tools {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.tool-card {
  width: 25%;
  padding: 7.5px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tool-icon {
  width: 50px;
  height: 50px;
  margin-bottom: 8px;
}

.tool-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 3px;
  text-align: center;
}

.tool-desc {
  font-size: 10px;
  color: #999;
  text-align: center;
  height: 28px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  box-shadow: 0 4px 15px rgba(107, 15, 190, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .rule-card {
    width: 50%;
  }
  
  .tool-card {
    width: 33.33%;
  }
}

@media screen and (max-width: 320px) {
  .stats-card {
    width: 100%;
  }
  
  .tool-card {
    width: 50%;
  }
}
</style> 