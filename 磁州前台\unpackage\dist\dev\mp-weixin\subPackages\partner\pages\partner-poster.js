"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_userProfile = require("../../../utils/userProfile.js");
const _sfc_main = {
  __name: "partner-poster",
  setup(__props) {
    const partnerInfo = common_vendor.reactive({
      level: 2,
      // 默认等级
      nickname: "",
      avatar: ""
    });
    const currentPoster = common_vendor.ref("/static/images/cizhou.png");
    const posterTemplates = common_vendor.ref([
      "/static/images/cizhou.png",
      "/static/images/tabbar/推广海报.png",
      "/static/images/tabbar/我的二维码.png",
      "/static/images/tabbar/规则说明.png"
    ]);
    const selectedTemplate = common_vendor.ref(0);
    const isGenerating = common_vendor.ref(false);
    const showModal = common_vendor.ref(false);
    const showShareModal = common_vendor.ref(false);
    const promotionData = common_vendor.reactive({
      viewCount: "0",
      registerCount: "0",
      orderCount: "0",
      income: "¥0.00"
    });
    const getUserInfo = () => {
      const userInfo = utils_userProfile.getLocalUserInfo();
      if (userInfo) {
        partnerInfo.nickname = userInfo.nickname;
        partnerInfo.avatar = userInfo.avatar;
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const getPartnerInfo = () => {
      partnerInfo.level = 2;
    };
    const getPromotionData = () => {
      promotionData.viewCount = "28";
      promotionData.registerCount = "5";
      promotionData.orderCount = "3";
      promotionData.income = "¥35.60";
    };
    const generatePoster = () => {
      isGenerating.value = true;
      setTimeout(() => {
        currentPoster.value = posterTemplates.value[selectedTemplate.value];
        isGenerating.value = false;
        common_vendor.index.showToast({
          title: "海报生成成功",
          icon: "success",
          duration: 1500
        });
      }, 1500);
    };
    const refreshPoster = () => {
      generatePoster();
    };
    const savePoster = () => {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      common_vendor.index.downloadFile({
        url: currentPoster.value,
        success: (res) => {
          if (res.statusCode === 200) {
            common_vendor.index.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                common_vendor.index.showToast({
                  title: "保存成功",
                  icon: "success"
                });
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at subPackages/partner/pages/partner-poster.vue:425", "保存失败:", err);
                if (err.errMsg.indexOf("auth deny") !== -1) {
                  common_vendor.index.showModal({
                    title: "提示",
                    content: "请授权保存图片到相册",
                    success: (res2) => {
                      if (res2.confirm) {
                        common_vendor.index.openSetting();
                      }
                    }
                  });
                } else {
                  common_vendor.index.showToast({
                    title: "保存失败",
                    icon: "none"
                  });
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "图片下载失败",
              icon: "none"
            });
          }
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "网络错误，请重试",
            icon: "none"
          });
        },
        complete: () => {
          common_vendor.index.hideLoading();
        }
      });
    };
    const sharePoster = () => {
      showShareModal.value = true;
    };
    const showTemplateModal = () => {
      showModal.value = true;
    };
    const hideTemplateModal = () => {
      showModal.value = false;
    };
    const selectTemplate = (index) => {
      selectedTemplate.value = index;
    };
    const confirmTemplate = () => {
      hideTemplateModal();
      generatePoster();
    };
    const hideShareModal = () => {
      showShareModal.value = false;
    };
    const shareToWechat = () => {
      common_vendor.index.showToast({
        title: "分享成功",
        icon: "success"
      });
      hideShareModal();
    };
    const shareToMoments = () => {
      common_vendor.index.showToast({
        title: "分享成功",
        icon: "success"
      });
      hideShareModal();
    };
    const shareToQQ = () => {
      common_vendor.index.showToast({
        title: "分享成功",
        icon: "success"
      });
      hideShareModal();
    };
    const shareToWeibo = () => {
      common_vendor.index.showToast({
        title: "分享成功",
        icon: "success"
      });
      hideShareModal();
    };
    const getCommissionRate = (level, partnerLevel) => {
      var _a, _b;
      const commissionRates = {
        1: { 1: 5, 2: 2 },
        // 普通合伙人：一级5%，二级2%
        2: { 1: 8, 2: 3 },
        // 银牌合伙人：一级8%，二级3%
        3: { 1: 12, 2: 5 },
        // 金牌合伙人：一级12%，二级5%
        4: { 1: 15, 2: 8 }
        // 钻石合伙人：一级15%，二级8%
      };
      if (partnerLevel) {
        return ((_a = commissionRates[partnerLevel]) == null ? void 0 : _a[level]) || 0;
      }
      return ((_b = commissionRates[partnerInfo.level]) == null ? void 0 : _b[level]) || 0;
    };
    const getLevelName = (level) => {
      const levelNames = {
        1: "普通合伙人",
        2: "银牌合伙人",
        3: "金牌合伙人",
        4: "钻石合伙人"
      };
      return levelNames[level] || "未知等级";
    };
    const goToUpgrade = () => {
      common_vendor.index.navigateTo({
        url: "/pages/new-partner/partner-upgrade",
        success: () => {
          common_vendor.index.__f__("log", "at subPackages/partner/pages/partner-poster.vue:571", "跳转到合伙人升级页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at subPackages/partner/pages/partner-poster.vue:574", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    };
    common_vendor.onMounted(() => {
      getUserInfo();
      getPartnerInfo();
      getPromotionData();
      generatePoster();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: common_assets._imports_1$19,
        d: common_vendor.o(showTemplateModal),
        e: currentPoster.value,
        f: isGenerating.value
      }, isGenerating.value ? {} : {}, {
        g: common_assets._imports_2$14,
        h: common_vendor.o(refreshPoster),
        i: common_assets._imports_3$14,
        j: common_vendor.o(savePoster),
        k: common_assets._imports_0$15,
        l: common_vendor.o(sharePoster),
        m: common_vendor.t(promotionData.viewCount),
        n: common_vendor.t(promotionData.registerCount),
        o: common_vendor.t(promotionData.orderCount),
        p: common_vendor.t(promotionData.income),
        q: common_assets._imports_1$15,
        r: common_vendor.o(goToUpgrade),
        s: common_vendor.t(getLevelName(partnerInfo.level)),
        t: common_vendor.t(partnerInfo.level),
        v: common_assets._imports_6$10,
        w: common_vendor.t(getCommissionRate(1)),
        x: common_assets._imports_7$5,
        y: common_vendor.t(getCommissionRate(2)),
        z: common_assets._imports_2$18,
        A: common_assets._imports_0$15,
        B: common_assets._imports_9$4,
        C: common_assets._imports_10$3,
        D: common_vendor.t(getCommissionRate(1, 1)),
        E: common_vendor.t(getCommissionRate(2, 1)),
        F: common_vendor.t(getCommissionRate(1, 2)),
        G: common_vendor.t(getCommissionRate(2, 2)),
        H: common_vendor.t(getCommissionRate(1, 3)),
        I: common_vendor.t(getCommissionRate(2, 3)),
        J: common_vendor.t(getCommissionRate(1, 4)),
        K: common_vendor.t(getCommissionRate(2, 4)),
        L: common_vendor.o(goToUpgrade),
        M: showModal.value
      }, showModal.value ? {
        N: common_vendor.o(hideTemplateModal),
        O: common_vendor.f(posterTemplates.value, (template, index, i0) => {
          return common_vendor.e({
            a: template,
            b: selectedTemplate.value === index
          }, selectedTemplate.value === index ? {
            c: common_assets._imports_11$7
          } : {}, {
            d: index,
            e: selectedTemplate.value === index ? 1 : "",
            f: common_vendor.o(($event) => selectTemplate(index), index)
          });
        }),
        P: common_vendor.o(confirmTemplate),
        Q: common_vendor.o(() => {
        }),
        R: common_vendor.o(hideTemplateModal)
      } : {}, {
        S: showShareModal.value
      }, showShareModal.value ? {
        T: common_vendor.o(hideShareModal),
        U: common_assets._imports_4$9,
        V: common_vendor.o(shareToWechat),
        W: common_assets._imports_5$9,
        X: common_vendor.o(shareToMoments),
        Y: common_assets._imports_6$9,
        Z: common_vendor.o(shareToQQ),
        aa: common_assets._imports_15$4,
        ab: common_vendor.o(shareToWeibo),
        ac: common_vendor.o(hideShareModal),
        ad: common_vendor.o(() => {
        }),
        ae: common_vendor.o(hideShareModal)
      } : {}, {
        af: common_assets._imports_2$14,
        ag: common_vendor.o(refreshPoster)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f8ca8baa"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/partner/pages/partner-poster.js.map
