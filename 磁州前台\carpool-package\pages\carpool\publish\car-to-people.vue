<template>
  <view class="publish-container">
    <!-- 导航栏 -->
    <carpool-nav title="发布车找人信息"></carpool-nav>
    
    <view class="form-container">
      <view class="form-group">
        <view class="form-title">行程信息</view>
        
        <view class="form-item">
          <text class="label">出发地</text>
          <view class="input-wrapper">
            <input type="text" v-model="formData.startPoint" placeholder="请输入出发地" />
            <view class="location-btn" @click="chooseLocation('start')">
              <image src="/static/images/tabbar/location.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">目的地</text>
          <view class="input-wrapper">
            <input type="text" v-model="formData.endPoint" placeholder="请输入目的地" />
            <view class="location-btn" @click="chooseLocation('end')">
              <image src="/static/images/tabbar/location.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">途径地点</text>
          <view class="via-points-container">
            <view v-for="(point, index) in formData.viaPoints" :key="index" class="via-point-item">
              <view class="input-wrapper">
                <input type="text" v-model="formData.viaPoints[index]" placeholder="请输入途径地点" />
                <view class="location-btn" @click="chooseLocation('via', index)">
                  <image src="/static/images/tabbar/location.png" mode="aspectFit"></image>
                </view>
              </view>
              <view class="via-point-actions">
                <view class="via-point-delete" @click="removeViaPoint(index)">
                  <text class="delete-icon">×</text>
                </view>
              </view>
            </view>
            
            <view class="add-via-point" @click="addViaPoint" v-if="formData.viaPoints.length < 3">
              <text class="add-icon">+</text>
              <text class="add-text">添加途径地点</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">出发日期</text>
          <picker mode="date" :value="formData.departureDate" start="2023-01-01" end="2030-12-31" @change="onDateChange">
            <view class="picker-value">
              <text>{{formData.departureDate || '请选择出发日期'}}</text>
              <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="label">出发时间</text>
          <picker mode="time" :value="formData.departureTime" @change="onTimeChange">
            <view class="picker-value">
              <text>{{formData.departureTime || '请选择出发时间'}}</text>
              <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="label">车型</text>
          <input type="text" v-model="formData.carType" placeholder="请输入车型，如：大众朗逸" />
        </view>
        
        <view class="form-item">
          <text class="label">空座数量</text>
          <picker mode="selector" :range="seatsOptions" :value="seatsIndex" @change="onSeatsChange">
            <view class="picker-value">
              <text>{{formData.availableSeats || '请选择空座数量'}}</text>
              <image src="/static/images/tabbar/arrow-right.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="label">每人价格</text>
          <view class="price-input">
            <input type="digit" v-model="formData.price" placeholder="请输入每人价格（选填）" />
            <text class="unit">元</text>
          </view>
        </view>
      </view>
      
      <view class="form-group">
        <view class="form-title">联系方式</view>
        
        <view class="form-item">
          <text class="label">联系人</text>
          <input type="text" v-model="formData.contactName" placeholder="请输入联系人姓名（选填）" />
        </view>
        
        <view class="form-item">
          <text class="label">手机号码</text>
          <input type="number" v-model="formData.contactPhone" placeholder="请输入手机号码" maxlength="11" />
        </view>
      </view>
      
      <view class="form-group">
        <view class="form-title">补充说明</view>
        
        <view class="form-item">
          <textarea v-model="formData.remark" placeholder="请输入补充说明（选填）" maxlength="200" />
          <view class="word-count">{{formData.remark.length}}/200</view>
        </view>
      </view>
    </view>
    
    <view class="submit-section">
      <view class="agreement">
        <checkbox-group @change="onAgreementChange">
          <label>
            <checkbox :checked="formData.agreement" color="#0A84FF" />
            <text>我已阅读并同意</text>
            <text class="link" @click.stop="viewAgreement">《拼车服务协议》</text>
          </label>
        </checkbox-group>
      </view>
      
      <!-- 发布方式选择 - 使用ConfigurablePremiumActions组件 -->
      <view class="publish-options">
        <view class="options-title">选择发布方式</view>
        
        <!-- 使用ConfigurablePremiumActions组件 -->
        <ConfigurablePremiumActions
          :pageType="'publish'"
          :showMode="'direct'"
          @action-completed="handleActionCompleted"
        />
      </view>
      
      <button class="submit-btn" :disabled="!formData.agreement" @click="submitForm">
        发布信息
      </button>
    </view>
  </view>
</template>

<script setup>
import CarpoolNav from '/components/carpool-nav.vue';
import { ref, onMounted } from 'vue';
import ConfigurablePremiumActions from '/components/premium/ConfigurablePremiumActions.vue';

// 状态栏高度
const statusBarHeight = ref(20);

// 表单数据
const formData = ref({
  startPoint: '',
  endPoint: '',
  departureDate: '',
  departureTime: '',
  carType: '',
  availableSeats: '',
  price: '',
  contactName: '',
  contactPhone: '',
  remark: '',
  agreement: false,
  viaPoints: []
});

// 座位选项
const seatsOptions = ref(['1个座位', '2个座位', '3个座位', '4个座位', '5个座位', '6个座位']);
const seatsIndex = ref(0);

// 发布模式
const publishMode = ref('ad'); // 默认为广告模式

// 生命周期钩子
onMounted(() => {
  setStatusBarHeight();
});

// 设置状态栏高度
const setStatusBarHeight = () => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 选择位置
const chooseLocation = (type, index) => {
  uni.chooseLocation({
    success: (res) => {
      if (type === 'start') {
        formData.value.startPoint = res.name;
      } else if (type === 'end') {
        formData.value.endPoint = res.name;
      } else if (type === 'via' && typeof index === 'number') {
        // Vue 3 不需要使用 $set 方法
        formData.value.viaPoints[index] = res.name;
      }
    }
  });
};

// 日期选择
const onDateChange = (e) => {
  formData.value.departureDate = e.detail.value;
};

// 时间选择
const onTimeChange = (e) => {
  formData.value.departureTime = e.detail.value;
};

// 座位数选择
const onSeatsChange = (e) => {
  seatsIndex.value = e.detail.value;
  formData.value.availableSeats = seatsOptions.value[seatsIndex.value];
};

// 协议同意状态变更
const onAgreementChange = (e) => {
  formData.value.agreement = e.detail.value.length > 0;
};

// 查看协议
const viewAgreement = () => {
  uni.navigateTo({
    url: '/pages/carpool/agreement'
  });
};

// 提交表单
const submitForm = () => {
  // 表单验证
  if (!formData.value.startPoint) {
    showToast('请输入出发地');
    return;
  }
  if (!formData.value.endPoint) {
    showToast('请输入目的地');
    return;
  }
  if (!formData.value.departureDate) {
    showToast('请选择出发日期');
    return;
  }
  if (!formData.value.departureTime) {
    showToast('请选择出发时间');
    return;
  }
  if (!formData.value.carType) {
    showToast('请输入车型');
    return;
  }
  if (!formData.value.availableSeats) {
    showToast('请选择空座数量');
    return;
  }
  // 价格为选填，移除验证
  if (!formData.value.contactPhone) {
    showToast('请输入手机号码');
    return;
  }
  if (!/^1\d{10}$/.test(formData.value.contactPhone)) {
    showToast('手机号码格式不正确');
    return;
  }
  
  // 使用选择的操作进行处理
  if (!selectedOption.value) {
    showToast('请选择发布方式');
    return;
  }
  
  // 表单直接提交到服务器
  submitToServer();
};

// 处理广告发布
const handleAdPublish = () => {
  // 显示广告
  uni.showLoading({
    title: '正在加载广告...'
  });
  
  // 模拟广告加载
  setTimeout(() => {
    uni.hideLoading();
    
    // 模拟广告播放完成
    uni.showModal({
      title: '广告播放完成',
      content: '感谢您观看广告，现在可以免费发布拼车信息',
      showCancel: false,
      success: () => {
        submitToServer();
      }
    });
  }, 1500);
};

// 处理付费发布
const handlePremiumPublish = () => {
  uni.showModal({
    title: '付费发布',
    content: '您将支付5元获得置顶发布特权，是否继续？',
    success: (res) => {
      if (res.confirm) {
        // 模拟支付过程
        uni.showLoading({
          title: '正在支付...'
        });
        
        setTimeout(() => {
          uni.hideLoading();
          uni.showToast({
            title: '支付成功',
            icon: 'success'
          });
          
          // 提交到服务器
          submitToServer();
        }, 1500);
      }
    }
  });
};

// 提交到服务器
const submitToServer = () => {
  // 提交表单
  uni.showLoading({
    title: '提交中...'
  });
  
  // 整理表单数据，包括途径地点
  const formDataToSubmit = {
    ...formData.value,
    // 过滤掉空的途径地点
    viaPoints: formData.value.viaPoints.filter(point => point.trim() !== '')
  };
  
  console.log('提交的表单数据：', formDataToSubmit);
  
  // 模拟提交
  setTimeout(() => {
    uni.hideLoading();
    
    // 生成一个模拟的发布ID
    const publishId = Date.now().toString();
    
    // 跳转到成功页面
    uni.navigateTo({
      url: `/carpool-package/pages/carpool/publish/success?id=${publishId}&type=car-to-people&mode=${publishMode.value}`
    });
  }, 1000);
};

// 显示提示
const showToast = (title) => {
  uni.showToast({
    title,
    icon: 'none'
  });
};

// 选择发布方式
const selectPublishMode = (mode) => {
  publishMode.value = mode;
};

// 添加途径地点
const addViaPoint = () => {
  formData.value.viaPoints.push('');
};

// 移除途径地点
const removeViaPoint = (index) => {
  formData.value.viaPoints.splice(index, 1);
};

// 选择的选项
const selectedOption = ref(null);

// 处理操作完成后的逻辑
const handleActionCompleted = (actionType, result) => {
  // 保存选择的选项
  selectedOption.value = result;
  
  if (actionType === 'ad') {
    publishMode.value = 'ad';
  } else if (actionType === 'paid') {
    publishMode.value = 'premium';
  }
  
  console.log('选择的推广选项:', actionType, result);
};
</script>

<style lang="scss">
.publish-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  height: 120rpx;
  padding: 0 30rpx;
  background-color: #0A84FF;
  position: relative;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 表单容器 */
.form-container {
  padding: 30rpx;
}

.form-group {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #0A84FF;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

input, .picker-value {
  width: 100%;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
}

.picker-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.picker-value image {
  width: 40rpx;
  height: 40rpx;
}

.input-wrapper {
  position: relative;
}

.location-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-btn image {
  width: 40rpx;
  height: 40rpx;
}

.price-input {
  position: relative;
}

.unit {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #666666;
}

textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
}

.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}

/* 提交区域 */
.submit-section {
  padding: 0 30rpx;
  margin-top: 40rpx;
}

.agreement {
  margin-bottom: 30rpx;
  font-size: 26rpx;
  color: #666666;
}

.link {
  color: #0A84FF;
}

.submit-btn {
  height: 90rpx;
  border-radius: 45rpx;
  background-image: linear-gradient(135deg, #0A84FF, #0055B8);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 16rpx rgba(10, 132, 255, 0.3);
}

.submit-btn[disabled] {
  background-image: linear-gradient(135deg, #cccccc, #999999);
  box-shadow: none;
  color: #ffffff;
}

/* 发布方式选择 */
.publish-options {
  margin-bottom: 30rpx;
}

.options-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.option-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
}

.option-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.08);
}

.option-selected {
  border: 2rpx solid #0A84FF;
  background-color: rgba(10, 132, 255, 0.05);
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.option-icon image {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

.ad-icon {
  background: linear-gradient(135deg, #2c96ff, #5f65ff);
}

.premium-icon {
  background: linear-gradient(135deg, #ff9500, #ff2d55);
}

.option-content {
  flex: 1;
}

.option-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
  display: flex;
  align-items: center;
}

.option-tag {
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
  color: #ffffff;
}

.recommend {
  background-color: #1677FF;
}

.premium {
  background-color: #ff9500;
}

.option-desc {
  font-size: 24rpx;
  color: #666666;
}

.option-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #dddddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.option-selected .option-checkbox {
  border-color: #0A84FF;
}

.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: #0A84FF;
  border-radius: 50%;
}

/* 途径地点样式 */
.via-points-container {
  display: flex;
  flex-wrap: wrap;
}

.via-point-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-right: 20rpx;
}

.via-point-actions {
  display: flex;
  align-items: center;
}

.via-point-delete {
  width: 20rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}

.delete-icon {
  color: #0A84FF;
}

.add-via-point {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.add-icon {
  font-size: 32rpx;
  font-weight: 600;
  color: #0A84FF;
  margin-right: 10rpx;
}

.add-text {
  font-size: 28rpx;
  color: #0A84FF;
}
</style> 