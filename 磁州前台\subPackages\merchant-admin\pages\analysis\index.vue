<template>
  <view class="analysis-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">分析洞察</text>
      <view class="navbar-right">
        <view class="help-icon">?</view>
      </view>
    </view>
    
    <!-- 日期选择器 -->
    <view class="date-selector-container">
      <view class="date-selector" @click="showDatePicker">
        <text class="date-text">{{currentDateRange}}</text>
        <text class="date-icon">📅</text>
      </view>
    </view>
    
    <!-- 销售分析模块 -->
    <view class="analysis-section">
      <view class="section-header">
        <text class="section-title">销售分析</text>
        <text class="section-more" @click="navigateTo('./sales')">查看更多</text>
      </view>
      
      <view class="analysis-grid">
        <!-- 商品销量分析 -->
        <view class="analysis-card" @click="navigateTo('./sales/product')">
          <view class="card-icon sales-icon">📊</view>
          <text class="card-name">商品销量分析</text>
          <text class="card-desc">热销品/滞销品识别</text>
        </view>
        
        <!-- 时段分析 -->
        <view class="analysis-card" @click="navigateTo('./sales/time')">
          <view class="card-icon time-icon">⏱️</view>
          <text class="card-name">时段分析</text>
          <text class="card-desc">销售高峰/低谷识别</text>
        </view>
        
        <!-- 利润分析 -->
        <view class="analysis-card" @click="navigateTo('./sales/profit')">
          <view class="card-icon profit-icon">💰</view>
          <text class="card-name">利润分析</text>
          <text class="card-desc">高利润/低利润商品分析</text>
        </view>
      </view>
    </view>
    
    <!-- 客户分析模块 -->
    <view class="analysis-section">
      <view class="section-header">
        <text class="section-title">客户分析</text>
        <text class="section-more" @click="navigateTo('./customer')">查看更多</text>
      </view>
      
      <view class="analysis-grid">
        <!-- 新老客户占比 -->
        <view class="analysis-card" @click="navigateTo('./customer/new-old')">
          <view class="card-icon customer-icon">👥</view>
          <text class="card-name">新老客户占比</text>
          <text class="card-desc">客户构成分析</text>
        </view>
        
        <!-- 客户生命周期分析 -->
        <view class="analysis-card" @click="navigateTo('./customer/lifecycle')">
          <view class="card-icon lifecycle-icon">🔄</view>
          <text class="card-name">客户生命周期分析</text>
          <text class="card-desc">客户留存与流失</text>
        </view>
        
        <!-- 客户价值分布 -->
        <view class="analysis-card" @click="navigateTo('./customer/value')">
          <view class="card-icon value-icon">💎</view>
          <text class="card-name">客户价值分布</text>
          <text class="card-desc">高价值客户识别</text>
        </view>
        
        <!-- 流失预警与召回建议 -->
        <view class="analysis-card" @click="navigateTo('./customer/churn')">
          <view class="card-icon churn-icon">⚠️</view>
          <text class="card-name">流失预警与召回建议</text>
          <text class="card-desc">客户流失风险管理</text>
        </view>
      </view>
    </view>
    
    <!-- 营销效果分析 -->
    <view class="analysis-section">
      <view class="section-header">
        <text class="section-title">营销效果分析</text>
        <text class="section-more" @click="navigateTo('./marketing')">查看更多</text>
      </view>
      
      <view class="analysis-grid">
        <!-- 各类营销活动ROI对比 -->
        <view class="analysis-card" @click="navigateTo('./marketing/roi')">
          <view class="card-icon roi-icon">📈</view>
          <text class="card-name">营销活动ROI对比</text>
          <text class="card-desc">投资回报率分析</text>
        </view>
        
        <!-- 引流转化漏斗分析 -->
        <view class="analysis-card" @click="navigateTo('./marketing/funnel')">
          <view class="card-icon funnel-icon">🔍</view>
          <text class="card-name">引流转化漏斗分析</text>
          <text class="card-desc">客户转化路径优化</text>
        </view>
        
        <!-- 促销敏感度分析 -->
        <view class="analysis-card" @click="navigateTo('./marketing/sensitivity')">
          <view class="card-icon sensitivity-icon">🎯</view>
          <text class="card-name">促销敏感度分析</text>
          <text class="card-desc">促销效果评估</text>
        </view>
      </view>
    </view>
    
    <!-- 竞争力分析 -->
    <view class="analysis-section">
      <view class="section-header">
        <text class="section-title">竞争力分析</text>
        <text class="section-more" @click="navigateTo('./competition')">查看更多</text>
      </view>
      
      <view class="analysis-grid">
        <!-- 行业对标分析 -->
        <view class="analysis-card" @click="navigateTo('./competition/industry')">
          <view class="card-icon industry-icon">🏢</view>
          <text class="card-name">行业对标分析</text>
          <text class="card-desc">行业位置评估</text>
        </view>
        
        <!-- 价格竞争力分析 -->
        <view class="analysis-card" @click="navigateTo('./competition/price')">
          <view class="card-icon price-icon">💲</view>
          <text class="card-name">价格竞争力分析</text>
          <text class="card-desc">价格策略优化</text>
        </view>
        
        <!-- 差异化优势识别 -->
        <view class="analysis-card" @click="navigateTo('./competition/advantage')">
          <view class="card-icon advantage-icon">🌟</view>
          <text class="card-name">差异化优势识别</text>
          <text class="card-desc">特色优势分析</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDateRange: '2023-05-01 至 2023-05-31'
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.analysis-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.date-selector-container {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

.date-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.date-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.date-icon {
  font-size: 16px;
}

.analysis-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #1677FF;
}

.analysis-grid {
  padding: 0 16px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.analysis-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 12px;
}

.sales-icon, .roi-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.time-icon, .funnel-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.profit-icon, .sensitivity-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.customer-icon, .industry-icon {
  background-color: #e6f7ff;
  color: #1677FF;
}

.lifecycle-icon, .price-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.value-icon, .advantage-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.churn-icon {
  background-color: #fff1f0;
  color: #f5222d;
}

.card-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  text-align: center;
}

.card-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
}
</style> 