{"version": 3, "file": "index.js", "sources": ["store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\n\n// Vue 3不再需要Vue.use(Vuex)\n\nexport default createStore({\n  state: {\n    user: null,\n    token: null,\n    distributorInfo: null\n  },\n  mutations: {\n    SET_USER(state, user) {\n      state.user = user\n    },\n    SET_TOKEN(state, token) {\n      state.token = token\n    },\n    SET_DISTRIBUTOR_INFO(state, info) {\n      state.distributorInfo = info\n    }\n  },\n  actions: {\n    setUser({ commit }, user) {\n      commit('SET_USER', user)\n    },\n    setToken({ commit }, token) {\n      commit('SET_TOKEN', token)\n    },\n    setDistributorInfo({ commit }, info) {\n      commit('SET_DISTRIBUTOR_INFO', info)\n    }\n  },\n  getters: {\n    user: state => state.user,\n    token: state => state.token,\n    distributorInfo: state => state.distributorInfo,\n    isDistributor: state => !!state.distributorInfo\n  }\n}) "], "names": ["createStore"], "mappings": ";;AAIeA,cAAAA,YAAY;AAAA,EACzB,OAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,iBAAiB;AAAA,EAClB;AAAA,EACD,WAAW;AAAA,IACT,SAAS,OAAO,MAAM;AACpB,YAAM,OAAO;AAAA,IACd;AAAA,IACD,UAAU,OAAO,OAAO;AACtB,YAAM,QAAQ;AAAA,IACf;AAAA,IACD,qBAAqB,OAAO,MAAM;AAChC,YAAM,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,QAAQ,EAAE,OAAQ,GAAE,MAAM;AACxB,aAAO,YAAY,IAAI;AAAA,IACxB;AAAA,IACD,SAAS,EAAE,OAAQ,GAAE,OAAO;AAC1B,aAAO,aAAa,KAAK;AAAA,IAC1B;AAAA,IACD,mBAAmB,EAAE,OAAQ,GAAE,MAAM;AACnC,aAAO,wBAAwB,IAAI;AAAA,IACpC;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,MAAM,WAAS,MAAM;AAAA,IACrB,OAAO,WAAS,MAAM;AAAA,IACtB,iBAAiB,WAAS,MAAM;AAAA,IAChC,eAAe,WAAS,CAAC,CAAC,MAAM;AAAA,EACjC;AACH,CAAC;"}