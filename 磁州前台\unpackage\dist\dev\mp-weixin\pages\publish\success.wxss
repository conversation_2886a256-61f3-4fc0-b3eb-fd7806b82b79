
.page-bg.data-v-1e37871e {
	min-height: 100vh;
  background: linear-gradient(180deg, #4a90e2 0%, #eaf6ff 100%);
	position: relative;
  padding-bottom: 60rpx;
}
.navbar.data-v-1e37871e {
  height: 120rpx;
  padding-top: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
  background: linear-gradient(90deg, #2580e6 0%, #4a90e2 100%);
	position: relative;
}
.nav-left.data-v-1e37871e, .nav-right.data-v-1e37871e {
	position: absolute;
  top: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
}
.nav-left.data-v-1e37871e { 
  left: 30rpx; 
  top: 70rpx;
  height: 40rpx;
}
.nav-right.data-v-1e37871e { 
  right: 30rpx;
}
.nav-title.data-v-1e37871e {
  color: #fff;
	font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}
.nav-icon.data-v-1e37871e {
  width: 38rpx;
  height: 38rpx;
}
.main-card.data-v-1e37871e {
  background: #fff;
  border-radius: 24rpx;
  margin: 40rpx 30rpx 0 30rpx;
  padding: 40rpx 30rpx 30rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
	display: flex;
  flex-direction: column;
	align-items: center;
}
.main-title.data-v-1e37871e {
  color: #1976d2;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-align: center;
}
.main-desc.data-v-1e37871e {
  color: #888;
  font-size: 24rpx;
  margin-bottom: 36rpx;
  text-align: center;
}
.main-btns.data-v-1e37871e {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 10rpx;
}
.main-btn.data-v-1e37871e {
  flex: 1;
  margin: 0 10rpx;
  background: #eaf3ff;
  color: #1976d2;
  border-radius: 32rpx;
  font-weight: 500;
  text-align: center;
  padding: 25rpx 0;
  height: 180rpx;
  transition: background 0.2s, color 0.2s;
	display: flex;
	align-items: center;
	justify-content: center;
}
.btn-text-container.data-v-1e37871e {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
  height: 100%;
  padding: 10rpx 0;
}
.btn-text.data-v-1e37871e {
  font-size: 28rpx;
  line-height: 1.8;
  display: block;
  font-weight: bold;
  letter-spacing: 2rpx;
}
.main-btn-active.data-v-1e37871e {
  background: linear-gradient(90deg, #1976d2 0%, #4a90e2 100%);
  color: #fff;
}
.premium-card.data-v-1e37871e {
  background: #fff;
  border-radius: 24rpx;
  margin: 36rpx 30rpx 0 30rpx;
  padding: 36rpx 30rpx 30rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}
.premium-title.data-v-1e37871e {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.premium-title-icon.data-v-1e37871e {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
  background: linear-gradient(135deg, #3b7dfc 0%, #5e96ff 100%);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
}
.premium-title-icon image.data-v-1e37871e {
  width: 28rpx;
  height: 28rpx;
  filter: brightness(10);
}
.float-btn.data-v-1e37871e {
  position: fixed;
  right: 40rpx;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.12);
	display: flex;
	align-items: center;
	justify-content: center;
  z-index: 100;
}
.share-btn.data-v-1e37871e { bottom: 180rpx;
}
.kefu-btn.data-v-1e37871e { bottom: 70rpx;
}
.float-icon.data-v-1e37871e {
  width: 48rpx;
  height: 48rpx;
}
.top-result.data-v-1e37871e {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
	display: flex;
	justify-content: center;
  align-items: center;
}
.top-result-content.data-v-1e37871e {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  width: 80%;
  max-width: 600rpx;
  text-align: center;
}
.top-result-icon.data-v-1e37871e {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}
.top-result-text.data-v-1e37871e {
  font-size: 28rpx;
  color: #333;
}
.share-tips-overlay.data-v-1e37871e {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
	display: flex;
	justify-content: center;
	align-items: center;
  animation: fadeIn-1e37871e 0.3s ease;
}
@keyframes fadeIn-1e37871e {
from { opacity: 0;
}
to { opacity: 1;
}
}
.share-tips-card.data-v-1e37871e {
  width: 85%;
	background-color: #FFFFFF;
	border-radius: 28rpx;
	overflow: hidden;
	display: flex;
	flex-direction: column;
  position: relative;
  animation: zoomIn-1e37871e 0.3s ease;
}
@keyframes zoomIn-1e37871e {
from { transform: scale(0.9); opacity: 0;
}
to { transform: scale(1); opacity: 1;
}
}
.share-tips-icon.data-v-1e37871e {
  text-align: center;
  margin-top: 30rpx;
}
.crown-icon.data-v-1e37871e {
  width: 100rpx;
  height: 100rpx;
}
.share-tips-title.data-v-1e37871e {
  color: #FF6600;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin: 20rpx 0 40rpx;
  text-shadow: 0 1rpx 3rpx rgba(255, 102, 0, 0.2);
}
.share-tips-item.data-v-1e37871e {
	display: flex;
  padding: 20rpx 30rpx;
  align-items: flex-start;
  margin-bottom: 10rpx;
}
.tips-item-number.data-v-1e37871e {
  width: 44rpx;
  height: 44rpx;
  background-color: #007AFF;
  color: #FFFFFF;
  border-radius: 50%;
  text-align: center;
  line-height: 44rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.tips-item-content.data-v-1e37871e {
	flex: 1;
}
.tips-text.data-v-1e37871e {
  font-size: 28rpx;
	color: #333333;
  line-height: 1.6;
  display: block;
}
.tips-counter.data-v-1e37871e {
  background-color: #F1F1F1;
  color: #999999;
  font-size: 24rpx;
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
  display: inline-block;
  margin-top: 10rpx;
}
.share-tips-btns.data-v-1e37871e {
	display: flex;
  margin-top: 30rpx;
  border-top: 1rpx solid #EEEEEE;
  flex-direction: row;
  width: 100%;
}
.share-btn-item.data-v-1e37871e {
  flex: 1;
  height: 90rpx;
	display: flex;
	align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
  text-align: center;
}
button.share-btn-item.data-v-1e37871e {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 90rpx !important;
  font-size: 30rpx !important;
  font-weight: 500 !important;
  box-sizing: border-box !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  flex: 1 !important;
  height: 90rpx !important;
  width: auto !important;
  min-width: 0 !important;
  min-height: 0 !important;
  background-color: initial;
  border: none !important;
  position: relative;
}
.close-btn.data-v-1e37871e {
  color: #999999;
  border-right: 1rpx solid #EEEEEE;
  background-color: #FFFFFF;
}
.share-btn-blue.data-v-1e37871e {
  background-color: #007AFF !important;
  color: #FFFFFF !important;
  position: relative !important;
  overflow: visible !important;
  min-height: 90rpx !important;
  font-weight: bold;
  animation: pulsing-1e37871e 2s infinite;
}
.share-btn-blue view.data-v-1e37871e {
  background-color: #007AFF !important;
  color: #FFFFFF !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
@keyframes pulsing-1e37871e {
0% {
    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.7);
    transform: scale(1);
}
70% {
    box-shadow: 0 0 0 10px rgba(0, 122, 255, 0);
    transform: scale(1.05);
}
100% {
    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0);
    transform: scale(1);
}
}
.pulsing-btn.data-v-1e37871e {
  animation: pulsing-1e37871e 2s infinite;
}
button.share-btn-item.data-v-1e37871e::after {
  display: none !important;
  border: none !important;
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  content: none !important;
}
button.share-btn-blue.data-v-1e37871e::after {
  display: none !important;
}
button.float-btn.data-v-1e37871e {
  margin: 0;
  padding: 0;
  line-height: normal;
  background-color: #fff;
  border: none;
}
.share-btn-green.data-v-1e37871e {
  background-color: #07C160;
  color: #FFFFFF;
}
.view-count.data-v-1e37871e {
  color: #FF6600;
  font-weight: bold;
  margin: 0 6rpx;
}
.qrcode-overlay.data-v-1e37871e {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn-1e37871e 0.3s ease;
}
.qrcode-card.data-v-1e37871e {
  width: 85%;
  max-width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  animation: zoomIn-1e37871e 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.qrcode-header.data-v-1e37871e {
  position: relative;
  height: 40rpx;
  padding: 20rpx;
}
.qrcode-close.data-v-1e37871e {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}
.close-icon.data-v-1e37871e {
  width: 32rpx;
  height: 32rpx;
}
.qrcode-content.data-v-1e37871e {
  padding: 0 40rpx 30rpx;
}
.qrcode-title-container.data-v-1e37871e {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.qrcode-title-icon.data-v-1e37871e {
  width: 48rpx;
  height: 48rpx;
  margin-right: 12rpx;
}
.qrcode-title.data-v-1e37871e {
  color: #1677FF;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin: 0;
}
.qrcode-desc.data-v-1e37871e {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}
.qrcode-image-container.data-v-1e37871e {
  position: relative;
  width: 100%;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qrcode-image.data-v-1e37871e {
  width: 320rpx;
  height: 320rpx;
  object-fit: contain;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  background-color: #ffffff;
  padding: 20rpx;
}
.qrcode-scan-hint.data-v-1e37871e {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
  color: #666;
  font-size: 24rpx;
}
.scan-icon-container.data-v-1e37871e {
  margin-right: 8rpx;
  color: #1677FF;
}
.qrcode-info-container.data-v-1e37871e {
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}
.qrcode-info-item.data-v-1e37871e {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  color: #333;
}
.qrcode-info-item.data-v-1e37871e:last-child {
  margin-bottom: 0;
}
.info-icon.data-v-1e37871e {
  margin-right: 10rpx;
  color: #1677FF;
}
.qrcode-actions.data-v-1e37871e {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}
.qrcode-btn.data-v-1e37871e {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  position: relative;
}
.qrcode-btn.data-v-1e37871e:active {
  background-color: rgba(0, 0, 0, 0.05);
}
.copy-btn.data-v-1e37871e {
  color: #1677FF;
  border-right: 1rpx solid #f0f0f0;
}
.save-btn.data-v-1e37871e {
  background: linear-gradient(135deg, #1677FF, #0062FF);
  color: #FFFFFF;
}
.btn-icon.data-v-1e37871e {
  margin-right: 8rpx;
}
