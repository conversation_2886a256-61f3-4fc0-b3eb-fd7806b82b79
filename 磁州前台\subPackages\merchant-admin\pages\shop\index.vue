<template>
  <view class="shop-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">店铺管理</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 店铺基础信息配置 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-icon">
          <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
        </view>
        <text class="section-title">基础信息配置</text>
      </view>
      
      <view class="menu-list">
        <view class="menu-item" @click="navigateTo('./basic/profile')">
          <view class="item-left">
            <text class="item-name">店铺形象</text>
            <text class="item-desc">LOGO、封面图、简介、联系方式</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./basic/location')">
          <view class="item-left">
            <text class="item-name">位置管理</text>
            <text class="item-desc">门店地址、导航设置、服务半径</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./basic/business')">
          <view class="item-left">
            <text class="item-name">经营信息</text>
            <text class="item-desc">营业时间、配送范围、服务标签</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 商品服务管理 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-icon product-icon">
          <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M6 2L3 6v14a2 2 0 002 2h14a2 2 0 002-2V6l-3-4z"></path>
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <path d="M16 10a4 4 0 01-8 0"></path>
          </svg>
        </view>
        <text class="section-title">商品服务管理</text>
      </view>
      
      <view class="menu-list">
        <view class="menu-item" @click="navigateTo('./product/category')">
          <view class="item-left">
            <text class="item-name">分类管理</text>
            <text class="item-desc">自定义商品分类体系</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./product/list')">
          <view class="item-left">
            <text class="item-name">商品管理</text>
            <text class="item-desc">批量上传、库存管理、价格设置</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./product/service')">
          <view class="item-left">
            <text class="item-name">服务项目</text>
            <text class="item-desc">服务定义、服务说明、预约设置</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 门店形象管理 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-icon store-icon">
          <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <circle cx="8.5" cy="8.5" r="1.5"></circle>
            <polyline points="21 15 16 10 5 21"></polyline>
          </svg>
        </view>
        <text class="section-title">门店形象管理</text>
      </view>
      
      <view class="menu-list">
        <view class="menu-item" @click="navigateTo('./store/photos')">
          <view class="item-left">
            <text class="item-name">店铺相册</text>
            <text class="item-desc">环境展示、产品展示、服务展示</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./store/videos')">
          <view class="item-left">
            <text class="item-name">视频展示</text>
            <text class="item-desc">店铺宣传视频、服务流程展示</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./store/story')">
          <view class="item-left">
            <text class="item-name">商家故事</text>
            <text class="item-desc">品牌文化、创始人故事、特色介绍</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 认证与资质管理 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-icon cert-icon">
          <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
          </svg>
        </view>
        <text class="section-title">认证与资质管理</text>
      </view>
      
      <view class="menu-list">
        <view class="menu-item" @click="navigateTo('./cert/business')">
          <view class="item-left">
            <text class="item-name">商家认证</text>
            <text class="item-desc">工商/个体工商户认证</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./cert/qualification')">
          <view class="item-left">
            <text class="item-name">资质管理</text>
            <text class="item-desc">经营许可证、特殊行业资质</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./cert/renewal')">
          <view class="item-left">
            <text class="item-name">资质到期提醒</text>
            <text class="item-desc">到期管理与续期提醒</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 我的活动管理 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-icon activity-icon">
          <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
        </view>
        <text class="section-title">我的活动管理</text>
      </view>
      
      <view class="menu-list">
        <view class="menu-item" @click="navigateTo('./activity/list')">
          <view class="item-left">
            <text class="item-name">活动列表</text>
            <text class="item-desc">展示商家已发布的所有活动</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./activity/operation')">
          <view class="item-left">
            <text class="item-name">活动操作</text>
            <text class="item-desc">可对活动进行编辑、暂停、删除</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
        
        <view class="menu-item" @click="navigateTo('./activity/data')">
          <view class="item-left">
            <text class="item-name">活动数据</text>
            <text class="item-desc">实时查看各个活动的浏览量、参与人数、转化率等核心数据</text>
          </view>
          <view class="item-right">
            <view class="arrow-icon"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    showHelp() {
      uni.showToast({
        title: '帮助中心功能开发中',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.shop-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
}

.bg-circle-1 {
  width: 300rpx;
  height: 300rpx;
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.1), rgba(6, 93, 210, 0.05));
  top: -100rpx;
  right: -100rpx;
}

.bg-circle-2 {
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.08), rgba(6, 93, 210, 0.03));
  top: 200rpx;
  left: -100rpx;
}

.bg-circle-3 {
  width: 400rpx;
  height: 400rpx;
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.05), rgba(6, 93, 210, 0.02));
  bottom: -200rpx;
  right: -150rpx;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.section-card {
  margin: 16px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
  animation: card-in 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.section-card:nth-child(2) { animation-delay: 0.1s; }
.section-card:nth-child(3) { animation-delay: 0.2s; }
.section-card:nth-child(4) { animation-delay: 0.3s; }
.section-card:nth-child(5) { animation-delay: 0.4s; }
.section-card:nth-child(6) { animation-delay: 0.5s; }

@keyframes card-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: #e6f7ff;
  color: #1677FF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.section-icon.product-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.section-icon.store-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.section-icon.cert-icon {
  background-color: #fcf4ff;
  color: #722ed1;
}

.section-icon.activity-icon {
  background-color: #fff2e8;
  color: #fa541c;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.menu-list {
  padding: 0 16px;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f9f9f9;
}

.item-left {
  flex: 1;
}

.item-name {
  font-size: 15px;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.item-desc {
  font-size: 12px;
  color: #999;
  display: block;
}

.item-right {
  display: flex;
  align-items: center;
}

.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  transform: rotate(45deg);
}
</style> 