<view class="bank-container"><view class="custom-navbar" style="{{'padding-top:' + c}}"><view class="navbar-left" bindtap="{{b}}"><image src="{{a}}" class="back-icon"></image></view><view class="navbar-title">银行卡管理</view><view class="navbar-right"></view></view><view class="bank-card-list" style="{{'margin-top:' + g}}"><view wx:if="{{d}}"><view wx:for="{{e}}" wx:for-item="card" wx:key="j" class="{{['bank-card-item', card.k && 'selected']}}" bindtap="{{card.l}}"><view class="card-header"><view class="bank-logo"><image src="{{card.a}}" class="bank-logo-img"></image></view><view class="bank-name">{{card.b}}</view><view class="card-type">{{card.c}}</view></view><view class="card-number">**** **** **** {{card.d}}</view><view class="card-footer"><view class="card-holder">{{card.e}}</view><view class="card-actions"><view wx:if="{{card.f}}" class="default-tag">默认</view><view wx:if="{{card.g}}" class="card-action" catchtap="{{card.h}}">设为默认</view><view class="card-action delete" catchtap="{{card.i}}">删除</view></view></view></view></view><view wx:else class="empty-view"><image class="empty-icon" src="{{f}}" mode="aspectFit"></image><view class="empty-text">暂无银行卡</view></view></view><view class="add-card-btn" bindtap="{{i}}"><image src="{{h}}" class="add-icon"></image><text>添加银行卡</text></view><view wx:if="{{j}}" class="popup-mask" bindtap="{{k}}"></view><view wx:if="{{l}}" class="popup-content"><view class="popup-header"><view class="popup-title">添加银行卡</view><view class="popup-close" bindtap="{{m}}">×</view></view><view class="popup-body"><view class="form-item"><view class="form-label">持卡人</view><input class="form-input" placeholder="请输入持卡人姓名" value="{{n}}" bindinput="{{o}}"/></view><view class="form-item"><view class="form-label">卡号</view><input class="form-input" placeholder="请输入银行卡号" type="number" maxlength="19" bindinput="{{p}}" value="{{q}}"/></view><view class="form-item"><view class="form-label">开户银行</view><picker class="form-picker" range="{{s}}" range-key="name" bindchange="{{t}}"><view class="picker-value">{{r}}</view></picker></view><view class="form-item"><view class="form-label">手机号</view><input class="form-input" placeholder="请输入银行预留手机号" type="number" maxlength="11" value="{{v}}" bindinput="{{w}}"/></view><view class="form-checkbox"><checkbox checked="{{x}}" bindtap="{{y}}" color="#0052CC"/><text class="checkbox-label">设为默认银行卡</text></view></view><view class="popup-footer"><button class="cancel-btn" bindtap="{{z}}">取消</button><button class="confirm-btn" bindtap="{{A}}" disabled="{{B}}">确定</button></view></view></view>