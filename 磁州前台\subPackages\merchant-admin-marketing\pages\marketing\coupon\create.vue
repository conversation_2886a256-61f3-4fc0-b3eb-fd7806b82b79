<template>
  <view class="coupon-create-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @tap="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">创建优惠券</text>
      <view class="navbar-right">
        <view class="help-icon" @tap="showHelp">?</view>
      </view>
    </view>
    
    <!-- 表单容器 -->
    <scroll-view scroll-y class="form-container">
      <!-- 优惠券类型选择 -->
      <view class="type-selection-section">
        <view class="type-option" 
          v-for="(type, index) in couponTypes" 
          :key="index"
          :class="{'active': selectedType === type.value}"
          @tap="selectCouponType(type.value)">
          <view class="type-icon" :class="'bg-'+type.value">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12s4.48 10 10 10 10-4.48 10-10z"></path>
              <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
              <line x1="9" y1="9" x2="9.01" y2="9"></line>
              <line x1="15" y1="9" x2="15.01" y2="9"></line>
            </svg>
          </view>
          <text class="type-name">{{type.name}}</text>
          <text class="type-desc">{{type.description}}</text>
          <view class="type-check" v-if="selectedType === type.value">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
          </view>
        </view>
      </view>
      
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">基本信息</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">优惠券名称</text>
          <input type="text" class="item-input" v-model="couponForm.title" placeholder="请输入优惠券名称" />
        </view>
        
        <view class="form-item" v-if="selectedType === 'discount'">
          <text class="item-label">优惠金额</text>
          <view class="amount-input-wrapper">
            <text class="amount-symbol">¥</text>
            <input type="digit" class="amount-input" v-model="couponForm.value" placeholder="请输入优惠金额" />
          </view>
        </view>
        
<view class="form-item" v-if="selectedType === 'percent'">
  <text class="item-label">折扣比例</text>
  <view class="amount-input-wrapper">
    <input type="digit" class="amount-input" v-model="couponForm.discount" placeholder="请输入折扣比例" />
    <text class="amount-unit">折</text>
  </view>
</view>

<view class="form-item" v-if="selectedType !== 'free'">
  <text class="item-label">使用门槛</text>
  <view class="amount-input-wrapper">
    <text class="threshold-text">满</text>
    <input type="digit" class="amount-input" v-model="couponForm.minSpend" placeholder="请输入最低消费金额" />
    <text class="threshold-text">元可用</text>
  </view>
</view>
      </view>
      
      <!-- 有效期设置 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">有效期设置</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">有效期类型</text>
          <view class="validity-type-selector">
            <view 
              class="type-option" 
              :class="{'active': validityType === 'fixed'}"
              @tap="setValidityType('fixed')">
              <view class="option-radio">
                <view class="radio-inner" v-if="validityType === 'fixed'"></view>
              </view>
              <text class="option-text">固定日期</text>
            </view>
            
            <view 
              class="type-option" 
              :class="{'active': validityType === 'dynamic'}"
              @tap="setValidityType('dynamic')">
              <view class="option-radio">
                <view class="radio-inner" v-if="validityType === 'dynamic'"></view>
              </view>
              <text class="option-text">领取后生效</text>
            </view>
          </view>
        </view>
        
        <view class="form-item" v-if="validityType === 'fixed'">
          <text class="item-label">开始日期</text>
          <view class="date-selector" @tap="showStartDatePicker">
            <text class="date-value">{{couponForm.startDate}}</text>
            <view class="date-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </view>
          </view>
        </view>
        
        <view class="form-item" v-if="validityType === 'fixed'">
          <text class="item-label">结束日期</text>
          <view class="date-selector" @tap="showEndDatePicker">
            <text class="date-value">{{couponForm.expireDate}}</text>
            <view class="date-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </view>
          </view>
        </view>
        
        <view class="form-item" v-if="validityType === 'dynamic'">
          <text class="item-label">有效天数</text>
          <view class="days-input-wrapper">
            <input type="number" class="days-input" v-model="couponForm.validDays" placeholder="请输入有效天数" />
            <text class="days-text">天</text>
          </view>
        </view>
      </view>
      
      <!-- 发放设置 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">发放设置</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">发放总量</text>
          <view class="amount-input-wrapper">
            <input type="number" class="amount-input" v-model="couponForm.totalCount" placeholder="请输入发放总量" />
            <text class="amount-unit">张</text>
          </view>
        </view>
        
        <view class="form-item">
          <text class="item-label">每人限领</text>
          <view class="amount-input-wrapper">
            <input type="number" class="amount-input" v-model="couponForm.perPersonLimit" placeholder="请输入每人限领数量" />
            <text class="amount-unit">张</text>
          </view>
        </view>
      </view>
      
      <!-- 使用规则 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">使用规则</text>
        </view>
        
        <view class="form-item">
          <text class="item-label">适用商品</text>
          <view class="product-selector" @tap="showProductSelector">
            <text class="selector-value">{{couponForm.applicableProducts}}</text>
            <view class="selector-arrow"></view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="item-label">使用说明</text>
          <textarea class="item-textarea" v-model="couponForm.instructions" placeholder="请输入使用说明" />
        </view>
      </view>
      
      <!-- 分销设置 -->
      <view class="form-section" v-if="hasMerchantDistribution">
        <view class="section-header">
          <text class="section-title">分销设置</text>
          <text class="section-subtitle">设置优惠券分销佣金</text>
        </view>
        
        <DistributionSetting 
          :settings="couponForm.distributionSettings"
          @update-settings="updateDistributionSettings"
        />
      </view>
      
      <!-- 活动推广 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">活动推广</text>
          <text class="section-subtitle">选择发布方式</text>
        </view>
        
        <MarketingPromotionActions 
          :activity-type="'coupon'"
          :activity-id="tempCouponId"
          :publish-mode-only="true"
          :show-actions="['publish']"
          @action-completed="handlePromotionCompleted"
        />
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-button preview" @tap="previewCoupon">
        <text class="button-text">预览</text>
      </view>
      <view class="action-button create" @tap="createCoupon">
        <text class="button-text">创建</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import MarketingPromotionActions from '/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue';
import DistributionSetting from '/subPackages/merchant-admin-marketing/pages/marketing/distribution/components/DistributionSetting.vue';

export default {
  name: 'CouponCreate',
  components: {
    MarketingPromotionActions,
    DistributionSetting
  },
  setup() {
    // 响应式状态
    const selectedType = ref('discount');
    const validityType = ref('fixed');
    const tempCouponId = ref('temp-' + Date.now()); // 临时ID，实际应该从后端获取
    const hasMerchantDistribution = ref(false); // 商家是否开通分销功能
    
    const couponTypes = [
      {
        value: 'discount',
        name: '满减券',
        description: '满足消费金额门槛后减免固定金额'
      },
      {
        value: 'percent',
        name: '折扣券',
        description: '满足消费金额门槛后按比例折扣'
      },
      {
        value: 'free',
        name: '无门槛券',
        description: '无消费金额门槛，直接减免固定金额'
      }
    ];
    
    const couponForm = reactive({
      title: '',
      value: '',
      discount: '',
      minSpend: '',
      startDate: formatDate(new Date()),
      expireDate: formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)), // 30天后
      validDays: '30',
      totalCount: '',
      perPersonLimit: '1',
      applicableProducts: '全部商品',
      instructions: '',
      
      // 分销设置
      distributionSettings: {
        enabled: false,
        commissionMode: 'percentage',
        commissions: {
          level1: '',
          level2: '',
          level3: ''
        },
        enableLevel3: false
      }
    });
    
    // 方法
    function goBack() {
      uni.navigateBack();
    }
    
    function showHelp() {
      uni.showModal({
        title: '帮助信息',
        content: '创建优惠券页面可以设置优惠券的各项参数，包括类型、金额、有效期等。创建后可在优惠券管理页面查看和管理。',
        showCancel: false
      });
    }
    
    function selectCouponType(type) {
      selectedType.value = type;
    }
    
    function setValidityType(type) {
      validityType.value = type;
    }
    
    function showStartDatePicker() {
      uni.showDatePicker({
        current: couponForm.startDate,
        success: (res) => {
          couponForm.startDate = res.date;
        }
      });
    }
    
    function showEndDatePicker() {
      uni.showDatePicker({
        current: couponForm.expireDate,
        success: (res) => {
          couponForm.expireDate = res.date;
        }
      });
    }
    
    function showProductSelector() {
      uni.showActionSheet({
        itemList: ['全部商品', '指定商品', '指定分类'],
        success: (res) => {
          const options = ['全部商品', '指定商品', '指定分类'];
          couponForm.applicableProducts = options[res.tapIndex];
          
          if (res.tapIndex > 0) {
            // 如果选择了指定商品或分类，显示选择界面
            uni.showToast({
              title: '请在下一页选择',
              icon: 'none'
            });
          }
        }
      });
    }
    
    function previewCoupon() {
      // 表单验证
      if (!validateForm()) {
        return;
      }
      
      // 显示预览弹窗
      uni.showToast({
        title: '预览功能开发中',
        icon: 'none'
      });
    }
    
    async function createCoupon() {
      // 表单验证
      if (!validateForm()) {
        return;
      }
      
      // 在实际应用中，这里应该调用API创建优惠券
      
      uni.showLoading({
        title: '创建中...'
      });
      
      // 保存分销设置
      if (hasMerchantDistribution.value && couponForm.distributionSettings.enabled) {
        const success = await saveDistributionSettings();
        if (!success) {
          uni.hideLoading();
          uni.showToast({
            title: '分销设置保存失败',
            icon: 'none'
          });
          return;
        }
      }
      
      setTimeout(() => {
        uni.hideLoading();
        
        uni.showToast({
          title: '创建成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
    
    function validateForm() {
      if (!couponForm.title) {
        uni.showToast({
          title: '请输入优惠券名称',
          icon: 'none'
        });
        return false;
      }
      
      if (selectedType.value === 'discount' && !couponForm.value) {
        uni.showToast({
          title: '请输入优惠金额',
          icon: 'none'
        });
        return false;
      }
      
      if (selectedType.value === 'percent' && !couponForm.discount) {
        uni.showToast({
          title: '请输入折扣比例',
          icon: 'none'
        });
        return false;
      }
      
      if (selectedType.value !== 'free' && !couponForm.minSpend) {
        uni.showToast({
          title: '请输入使用门槛',
          icon: 'none'
        });
        return false;
      }
      
      if (!couponForm.totalCount) {
        uni.showToast({
          title: '请输入发放总量',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    }
    
    function formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    
    function loadTemplateData(templateId) {
      // 在实际应用中，这里应该调用API获取模板数据
      
      // 模拟加载模板数据
      uni.showLoading({
        title: '加载模板...'
      });
      
      setTimeout(() => {
        // 模拟模板数据
        Object.assign(couponForm, {
          title: '模板优惠券',
          value: '20',
          minSpend: '200',
          totalCount: '1000',
          perPersonLimit: '2',
          applicableProducts: '全部商品',
          instructions: '模板优惠券使用说明'
        });
        
        uni.hideLoading();
      }, 500);
    }
    
    // 更新分销设置
    function updateDistributionSettings(settings) {
      couponForm.distributionSettings = settings;
    }
    
    // 检查商家是否开通分销功能
    function checkMerchantDistribution() {
      // 调用API检查商家是否开通分销功能
      // 这里模拟API调用
      setTimeout(() => {
        hasMerchantDistribution.value = true;
      }, 500);
    }
    
    // 保存分销设置
    async function saveDistributionSettings() {
      // 模拟API调用
      return new Promise((resolve) => {
        if (hasMerchantDistribution.value && couponForm.distributionSettings.enabled) {
          setTimeout(() => {
            console.log('分销设置已保存');
            resolve(true);
          }, 500);
        } else {
          resolve(true);
        }
      });
    }
    
    // 处理推广操作完成事件
    function handlePromotionCompleted(data) {
      console.log('推广操作完成:', data);
      // 根据不同操作类型处理结果
      if (data.action === 'publish') {
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        });
      }
    }
    
    // 在挂载时检查分销功能
    onMounted(() => {
      // 可以在这里处理从模板创建的情况
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.$page?.options || {};
      
      if (options.template) {
        // 从模板创建，加载模板数据
        loadTemplateData(options.template);
      }
      
      // 检查商家是否开通分销功能
      checkMerchantDistribution();
    });
    
    return {
      selectedType,
      validityType,
      couponTypes,
      couponForm,
      hasMerchantDistribution,
      tempCouponId,
      goBack,
      showHelp,
      selectCouponType,
      setValidityType,
      showStartDatePicker,
      showEndDatePicker,
      showProductSelector,
      previewCoupon,
      createCoupon,
      updateDistributionSettings,
      handlePromotionCompleted
    };
  }
}
</script>

<style lang="scss">
.coupon-create-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 80px; /* 为底部操作栏留出空间 */
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF9966, #FF5E62);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 94, 98, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 表单容器样式 */
.form-container {
  height: calc(100vh - 80px - 44px - 15px - 15px);
}

/* 优惠券类型选择样式 */
.type-selection-section {
  margin: 15px;
  display: flex;
  flex-wrap: wrap;
}

.type-option {
  width: calc(33.33% - 10px);
  margin: 0 5px 10px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transition: all 0.3s;
}

.type-option.active {
  border: 2px solid #FF5E62;
  padding: 13px;
}

.type-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  color: #fff;
}

.bg-discount {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}

.bg-percent {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

.bg-free {
  background: linear-gradient(135deg, #11998e, #38ef7d);
}

.type-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.type-desc {
  font-size: 12px;
  color: #999;
  text-align: center;
  line-height: 1.3;
}

.type-check {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  color: #FF5E62;
}

/* 表单部分样式 */
.form-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.form-item {
  margin-bottom: 15px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.item-input {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 44px;
}

.amount-symbol {
  font-size: 14px;
  color: #333;
  margin-right: 5px;
}

.amount-input {
  flex: 1;
  height: 44px;
  font-size: 14px;
  color: #333;
  background: transparent;
}

.amount-unit {
  font-size: 14px;
  color: #666;
}

.threshold-text {
  font-size: 14px;
  color: #666;
  margin: 0 5px;
}

.validity-type-selector {
  display: flex;
}

.type-option {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.option-radio {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  border: 1px solid #ddd;
  margin-right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-inner {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background: #FF5E62;
}

.option-text {
  font-size: 14px;
  color: #333;
}

.type-option.active .option-text {
  color: #FF5E62;
}

.date-selector {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.date-value {
  font-size: 14px;
  color: #333;
}

.date-icon {
  width: 20px;
  height: 20px;
  color: #999;
}

.days-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 44px;
}

.days-input {
  flex: 1;
  height: 44px;
  font-size: 14px;
  color: #333;
  background: transparent;
}

.days-text {
  font-size: 14px;
  color: #666;
}

.product-selector {
  width: 100%;
  height: 44px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selector-value {
  font-size: 14px;
  color: #333;
}

.selector-arrow {
  width: 10px;
  height: 10px;
  border-right: 2px solid #999;
  border-bottom: 2px solid #999;
  transform: rotate(45deg);
}

.item-textarea {
  width: 100%;
  height: 100px;
  background: #F5F7FA;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

/* 底部操作栏样式 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  padding: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 90;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
}

.action-button.preview {
  background: #F5F7FA;
  border: 1px solid #FF5E62;
}

.action-button.create {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}

.button-text {
  font-size: 16px;
  font-weight: 500;
}

.action-button.preview .button-text {
  color: #FF5E62;
}

.action-button.create .button-text {
  color: #fff;
}
</style>