<view class="page-bg data-v-1e37871e"><view class="navbar data-v-1e37871e"><view class="nav-left data-v-1e37871e" bindtap="{{b}}"><image class="nav-icon data-v-1e37871e" src="{{a}}"></image></view><view class="nav-title data-v-1e37871e">发布成功</view><view class="nav-right data-v-1e37871e"><image class="nav-icon data-v-1e37871e" src="{{c}}"></image><image class="nav-icon data-v-1e37871e" src="{{d}}" style="margin-left:16rpx"></image></view></view><view class="main-card data-v-1e37871e"><view class="main-title data-v-1e37871e">信息发布成功 - 磁州生活网</view><view class="main-desc data-v-1e37871e">完善信息或置顶、红包，可以大幅度提高信息传播效果哦</view><view class="main-btns data-v-1e37871e"><view wx:for="{{e}}" wx:for-item="btn" wx:key="c" class="{{['data-v-1e37871e', 'main-btn', btn.d]}}" bindtap="{{btn.e}}"><view class="btn-text-container data-v-1e37871e"><text class="btn-text data-v-1e37871e">{{btn.a}}</text><text class="btn-text data-v-1e37871e">{{btn.b}}</text></view></view></view></view><view class="premium-card data-v-1e37871e"><view class="premium-title data-v-1e37871e"><view class="premium-title-icon data-v-1e37871e"><image class="data-v-1e37871e" src="{{f}}" mode="aspectFit"></image></view><text class="data-v-1e37871e">置顶信息，提升10倍曝光率</text></view><configurable-premium-actions wx:if="{{i}}" class="data-v-1e37871e" bindactionCompleted="{{g}}" bindactionCancelled="{{h}}" u-i="1e37871e-0" bind:__l="__l" u-p="{{i}}"/></view><button class="float-btn share-btn data-v-1e37871e" open-type="share" bindtap="{{k}}"><image class="float-icon data-v-1e37871e" src="{{j}}"></image></button><view class="float-btn kefu-btn data-v-1e37871e" bindtap="{{m}}"><image class="float-icon data-v-1e37871e" src="{{l}}"></image></view><view wx:if="{{n}}" class="top-result data-v-1e37871e"><view class="top-result-content data-v-1e37871e"><image class="top-result-icon data-v-1e37871e" src="{{o}}"></image><view class="top-result-text data-v-1e37871e">{{p}}</view></view></view><view wx:if="{{q}}" class="share-tips-overlay data-v-1e37871e"><view class="share-tips-card data-v-1e37871e" catchtap="{{w}}"><view class="share-tips-icon data-v-1e37871e"><image src="{{r}}" mode="aspectFit" class="crown-icon data-v-1e37871e"></image></view><view class="share-tips-title data-v-1e37871e">恭喜你!获得免费置顶和群发机会</view><view class="share-tips-item data-v-1e37871e"><view class="tips-item-number data-v-1e37871e">1</view><view class="tips-item-content data-v-1e37871e"><text class="tips-text data-v-1e37871e">把信息分享到朋友圈、微信群或好友处，</text><text class="tips-text data-v-1e37871e">您的信息将自动置顶1天！</text></view></view><view class="share-tips-item data-v-1e37871e"><view class="tips-item-number data-v-1e37871e">2</view><view class="tips-item-content data-v-1e37871e"><text class="tips-text data-v-1e37871e">把你发布的信息发送给客服后，客服会给</text><text class="tips-text data-v-1e37871e">您群发多个群，扩大曝光量!</text></view></view><view class="share-tips-btns data-v-1e37871e"><view class="share-btn-item close-btn data-v-1e37871e" bindtap="{{s}}" style="background-color:#FFFFFF;color:#999999;border-right:1rpx solid #EEEEEE;height:90rpx;font-size:30rpx;font-weight:500;display:flex;align-items:center;justify-content:center">关闭</view><view class="share-btn-item share-btn-blue pulsing-btn data-v-1e37871e" bindtap="{{t}}" style="background-color:#007AFF !important;color:#FFFFFF !important;position:relative;line-height:90rpx;height:90rpx;padding:0;overflow:visible;border:none;box-sizing:border-box;display:flex;align-items:center;justify-content:center"><text class="data-v-1e37871e" style="color:#FFFFFF !important;font-size:30rpx;font-weight:bold">去分享</text></view><view class="share-btn-item share-btn-green data-v-1e37871e" bindtap="{{v}}" style="background-color:#07C160;color:#FFFFFF;height:90rpx;font-size:30rpx;font-weight:500;display:flex;align-items:center;justify-content:center">加客服</view></view></view></view><view wx:if="{{x}}" class="qrcode-overlay data-v-1e37871e" bindtap="{{Q}}"><view class="qrcode-card data-v-1e37871e" catchtap="{{P}}"><view class="qrcode-header data-v-1e37871e"><view class="qrcode-close data-v-1e37871e" bindtap="{{A}}"><svg wx:if="{{z}}" u-s="{{['d']}}" class="close-icon data-v-1e37871e" u-i="1e37871e-1" bind:__l="__l" u-p="{{z}}"><path wx:if="{{y}}" class="data-v-1e37871e" u-i="1e37871e-2,1e37871e-1" bind:__l="__l" u-p="{{y}}"></path></svg></view></view><view class="qrcode-content data-v-1e37871e"><view class="qrcode-title-container data-v-1e37871e"><image class="qrcode-title-icon data-v-1e37871e" src="{{B}}" mode="aspectFit"></image><text class="qrcode-title data-v-1e37871e">微信扫码添加客服</text></view><view class="qrcode-desc data-v-1e37871e">添加客服微信，提供更多发布推广服务</view><view class="qrcode-image-container data-v-1e37871e"><image src="{{C}}" mode="aspectFit" class="qrcode-image data-v-1e37871e"></image><view class="qrcode-scan-hint data-v-1e37871e"><view class="scan-icon-container data-v-1e37871e"><svg wx:if="{{E}}" u-s="{{['d']}}" class="scan-icon data-v-1e37871e" u-i="1e37871e-3" bind:__l="__l" u-p="{{E}}"><path wx:if="{{D}}" class="data-v-1e37871e" u-i="1e37871e-4,1e37871e-3" bind:__l="__l" u-p="{{D}}"/></svg></view><text class="data-v-1e37871e">长按识别二维码添加客服</text></view></view><view class="qrcode-info-container data-v-1e37871e"><view class="qrcode-info-item data-v-1e37871e"><svg wx:if="{{G}}" u-s="{{['d']}}" class="info-icon data-v-1e37871e" u-i="1e37871e-5" bind:__l="__l" u-p="{{G}}"><path wx:if="{{F}}" class="data-v-1e37871e" u-i="1e37871e-6,1e37871e-5" bind:__l="__l" u-p="{{F}}"/></svg><text class="data-v-1e37871e">客服在线时间: 8:00-22:00</text></view><view class="qrcode-info-item data-v-1e37871e"><svg wx:if="{{I}}" u-s="{{['d']}}" class="info-icon data-v-1e37871e" u-i="1e37871e-7" bind:__l="__l" u-p="{{I}}"><path wx:if="{{H}}" class="data-v-1e37871e" u-i="1e37871e-8,1e37871e-7" bind:__l="__l" u-p="{{H}}"/></svg><text class="data-v-1e37871e">客服微信: cishangtc</text></view></view></view><view class="qrcode-actions data-v-1e37871e"><view class="qrcode-btn copy-btn data-v-1e37871e" bindtap="{{L}}"><svg wx:if="{{K}}" u-s="{{['d']}}" class="btn-icon data-v-1e37871e" u-i="1e37871e-9" bind:__l="__l" u-p="{{K}}"><path wx:if="{{J}}" class="data-v-1e37871e" u-i="1e37871e-10,1e37871e-9" bind:__l="__l" u-p="{{J}}"/></svg><text class="data-v-1e37871e">复制微信号</text></view><view class="qrcode-btn save-btn data-v-1e37871e" bindtap="{{O}}"><svg wx:if="{{N}}" u-s="{{['d']}}" class="btn-icon data-v-1e37871e" u-i="1e37871e-11" bind:__l="__l" u-p="{{N}}"><path wx:if="{{M}}" class="data-v-1e37871e" u-i="1e37871e-12,1e37871e-11" bind:__l="__l" u-p="{{M}}"/></svg><text class="data-v-1e37871e">保存图片</text></view></view></view></view></view>