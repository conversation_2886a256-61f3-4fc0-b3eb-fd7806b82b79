<template>
  <view class="message-center-container">
    <!-- 自定义导航栏 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="left-action" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
        </view>
        <view class="title-area">
          <text class="page-title">消息中心</text>
        </view>
        <view class="right-action">
          <!-- 预留空间 -->
        </view>
      </view>
    </view>
    
    <!-- 消息类型 Tab -->
    <view class="message-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{tab}}</text>
        <view class="active-line" v-if="currentTab === index"></view>
      </view>
    </view>
    
    <!-- 消息列表 -->
    <scroll-view class="scrollable-content" scroll-y @scrolltolower="loadMore" refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="isRefreshing">
      <!-- 系统消息列表 -->
      <view class="message-list" v-if="currentTab === 0">
        <view class="message-item system" v-for="(item, index) in systemMessages" :key="item.id" @click="readMessage(item)">
          <view class="unread-indicator" v-if="!item.isRead"></view>
          <view class="message-icon-wrapper">
            <image src="/static/images/icons/system-message.png" mode="aspectFit" class="message-icon"></image>
          </view>
          <view class="message-content">
            <view class="message-header">
              <text class="message-title">{{item.title}}</text>
              <text class="message-time">{{item.time}}</text>
            </view>
            <text class="message-brief">{{item.content}}</text>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="systemMessages.length === 0 && !isLoading">
          <image src="/static/images/empty/no-message.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无系统消息</text>
        </view>
      </view>
      
      <!-- 拼车消息列表 -->
      <view class="message-list" v-if="currentTab === 1">
        <view class="message-item carpool" v-for="(item, index) in carpoolMessages" :key="item.id" @click="readMessage(item)">
          <view class="unread-indicator" v-if="!item.isRead"></view>
          <view class="message-icon-wrapper">
            <image src="/static/images/icons/carpool-message.png" mode="aspectFit" class="message-icon"></image>
          </view>
          <view class="message-content">
            <view class="message-header">
              <text class="message-title">{{item.title}}</text>
              <text class="message-time">{{item.time}}</text>
            </view>
            <text class="message-brief">{{item.content}}</text>
            
            <!-- 拼车相关信息 -->
            <view class="carpool-info" v-if="item.carpoolInfo">
              <view class="route-brief">
                <text class="route-text">{{item.carpoolInfo.startPoint}} → {{item.carpoolInfo.endPoint}}</text>
                <text class="departure-time">{{item.carpoolInfo.departureTime}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="carpoolMessages.length === 0 && !isLoading">
          <image src="/static/images/empty/no-message.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无拼车消息</text>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="isLoading && !isRefreshing">
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 到底提示 -->
      <view class="list-bottom" v-if="messageList.length > 0 && !hasMore">
        <text class="bottom-text">— 已经到底啦 —</text>
      </view>
    </scroll-view>
    
    <!-- 底部悬浮清空按钮 -->
    <view class="float-clear-btn" @click="showClearConfirm" v-if="messageList.length > 0">
      <text class="float-clear-text">清空消息</text>
    </view>

    <!-- 消息详情弹窗 -->
    <view class="popup-mask" v-if="showMessageDetail" @click="closeMessageDetail"></view>
    <view class="popup-container" v-if="showMessageDetail">
      <view class="popup-header">
        <text class="popup-title">{{currentMessage.title}}</text>
        <view class="popup-close" @click="closeMessageDetail">×</view>
      </view>
      <scroll-view class="popup-content" scroll-y>
        <view class="message-time-display">{{currentMessage.time}}</view>
        <view class="message-full-content">
          <text class="content-text">{{currentMessage.fullContent || currentMessage.content}}</text>
        </view>
        
        <!-- 拼车详情信息 -->
        <view class="carpool-detail" v-if="currentMessage.carpoolInfo">
          <view class="detail-title">相关拼车信息</view>
          <view class="route-info">
            <view class="route-points">
              <view class="start-point">
                <view class="point-marker start"></view>
                <text class="point-text">{{currentMessage.carpoolInfo.startPoint}}</text>
              </view>
              <view class="route-line"></view>
              <view class="end-point">
                <view class="point-marker end"></view>
                <text class="point-text">{{currentMessage.carpoolInfo.endPoint}}</text>
              </view>
            </view>
            <view class="trip-info">
              <view class="info-item">
                <image src="/static/images/icons/calendar.png" mode="aspectFit" class="info-icon"></image>
                <text class="info-text">{{currentMessage.carpoolInfo.departureTime}}</text>
              </view>
              <view class="info-item">
                <image src="/static/images/icons/people.png" mode="aspectFit" class="info-icon"></image>
                <text class="info-text">{{currentMessage.carpoolInfo.seatCount}}个座位</text>
              </view>
              <view class="info-item" v-if="currentMessage.carpoolInfo.price">
                <image src="/static/images/icons/price.png" mode="aspectFit" class="info-icon"></image>
                <text class="info-text price">¥{{currentMessage.carpoolInfo.price}}/人</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
      
      <view class="popup-footer">
        <button class="popup-button delete" @click="deleteCurrentMessage">删除消息</button>
        <button class="popup-button confirm" @click="viewCarpoolDetail" v-if="currentMessage.carpoolInfo">查看详情</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 状态栏高度
const statusBarHeight = ref(20);

// 数据定义
const tabs = ref(['系统消息', '拼车消息'])
const currentTab = ref(0)
const messageList = ref([])
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const isLoading = ref(false)
const isRefreshing = ref(false)
const showMessageDetail = ref(false)
const currentMessage = ref({})

// 计算属性
const systemMessages = computed(() => {
  return messageList.value.filter(msg => msg.type === 'system')
})

const carpoolMessages = computed(() => {
  return messageList.value.filter(msg => msg.type === 'carpool')
})

// 加载数据
const loadData = () => {
  if (isLoading.value) return
  isLoading.value = true
  
  // 模拟数据加载
  setTimeout(() => {
    // 模拟数据
    const mockData = [
      {
        id: '5001',
        type: 'system',
        title: '系统通知',
        content: '您的账号已完成实名认证，现在可以发布拼车信息了。',
        fullContent: '尊敬的用户：\n\n恭喜您！您的账号已通过实名认证审核，现在您可以使用平台的全部功能，包括发布拼车信息、查看联系方式等。\n\n为了保障平台用户的安全，我们对所有用户进行严格的身份审核。感谢您的配合与支持！\n\n如有任何疑问，请联系客服。',
        time: '2023-10-15 16:30',
        isRead: true
      },
      {
        id: '5002',
        type: 'system',
        title: '活动通知',
        content: '新用户专享优惠：首次发布拼车信息可获得置顶券一张。',
        time: '2023-10-14 10:15',
        isRead: false
      },
      {
        id: '5003',
        type: 'carpool',
        title: '拼车申请',
        content: '用户"王先生"申请加入您发布的拼车行程。',
        time: '2023-10-13 14:20',
        isRead: false,
        carpoolInfo: {
          id: '1001',
          startPoint: '磁县公交站',
          endPoint: '邯郸东站',
          departureTime: '2023-10-16 08:30',
          seatCount: 3,
          price: 15
        }
      },
      {
        id: '5004',
        type: 'carpool',
        title: '行程提醒',
        content: '您有一个拼车行程即将开始，请提前做好准备。',
        time: '2023-10-12 18:30',
        isRead: true,
        carpoolInfo: {
          id: '1002',
          startPoint: '磁县老城区',
          endPoint: '邯郸高新区',
          departureTime: '2023-10-13 07:30',
          seatCount: 4,
          price: 10
        }
      }
    ]
    
    if (page.value === 1) {
      messageList.value = mockData
    } else {
      messageList.value = [...messageList.value, ...mockData]
    }
    
    // 模拟没有更多数据
    if (page.value >= 2) {
      hasMore.value = false
    }
    
    isLoading.value = false
    isRefreshing.value = false
  }, 1000)
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || isLoading.value) return
  page.value++
  loadData()
}

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true
  page.value = 1
  hasMore.value = true
  loadData()
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 切换标签页
const switchTab = (index) => {
  currentTab.value = index
}

// 阅读消息
const readMessage = (message) => {
  // 标记为已读
  if (!message.isRead) {
    messageList.value = messageList.value.map(item => {
      if (item.id === message.id) {
        return { ...item, isRead: true }
      }
      return item
    })
  }
  
  // 显示详情
  currentMessage.value = message
  showMessageDetail.value = true
}

// 关闭消息详情
const closeMessageDetail = () => {
  showMessageDetail.value = false
}

// 删除当前消息
const deleteCurrentMessage = () => {
  uni.showModal({
    title: '提示',
    content: '确定要删除此条消息吗？',
    success: (res) => {
      if (res.confirm) {
        // 删除消息
        messageList.value = messageList.value.filter(item => item.id !== currentMessage.value.id)
        closeMessageDetail()
        
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}

// 查看拼车详情
const viewCarpoolDetail = () => {
  if (!currentMessage.value.carpoolInfo) return
  
  uni.navigateTo({
    url: `/carpool-package/pages/carpool/detail/index?id=${currentMessage.value.carpoolInfo.id}`
  })
  
  closeMessageDetail()
}

// 显示清空确认
const showClearConfirm = () => {
  uni.showModal({
    title: '提示',
    content: `确定要清空${currentTab.value === 0 ? '系统' : '拼车'}消息吗？此操作不可恢复。`,
    success: (res) => {
      if (res.confirm) {
        clearMessages()
      }
    }
  })
}

// 清空消息
const clearMessages = () => {
  const messageType = currentTab.value === 0 ? 'system' : 'carpool'
  
  // 清空当前类型的消息
  messageList.value = messageList.value.filter(item => item.type !== messageType)
  
  uni.showToast({
    title: '已清空',
    icon: 'success'
  })
}

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight || 20;
  
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.$page?.options || {}
  
  // 如果有指定tab参数，则切换到对应tab
  if (options.tab) {
    currentTab.value = parseInt(options.tab) || 0
  }
  
  loadData()
})

// 暴露方法给外部访问
defineExpose({
  loadData,
  switchTab
})
</script>

<style lang="scss">
.message-center-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #1677FF;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.clear-text {
  color: #FFFFFF;
  font-size: 14px;
}

/* 消息类型Tab */
.message-tabs {
  position: fixed;
  top: calc(44px + var(--status-bar-height));
  left: 0;
  right: 0;
  height: 44px;
  background-color: #FFFFFF;
  display: flex;
  z-index: 99;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  height: 44px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tab-text {
  font-size: 15px;
  color: #666666;
}

.tab-item.active .tab-text {
  color: #1677FF;
  font-weight: 500;
}

.active-line {
  position: absolute;
  bottom: 0;
  width: 20px;
  height: 3px;
  background-color: #1677FF;
  border-radius: 1.5px;
}

/* 内容区域 */
.scrollable-content {
  flex: 1;
  margin-top: calc(88px + var(--status-bar-height));
  padding: 8px 0; /* 减少左右内边距，确保卡片圆角完全显示 */
}

/* 消息列表 */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 14px; /* 增加卡片之间的间距 */
  padding: 6px 0; /* 添加上下内边距 */
}

.message-item {
  position: relative;
  background-color: #FFFFFF;
  border-radius: 16px; /* 增加圆角 */
  overflow: hidden;
  padding: 18px 20px; /* 调整内边距 */
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06); /* 增强阴影效果 */
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 80%; /* 再次缩短卡片宽度 */
  margin: 0 auto; /* 居中显示 */
}

.unread-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #FF5722;
}

.message-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: rgba(22, 119, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-item.system .message-icon-wrapper {
  background-color: rgba(22, 119, 255, 0.1);
}

.message-item.carpool .message-icon-wrapper {
  background-color: rgba(255, 87, 34, 0.1);
}

.message-icon {
  width: 24px;
  height: 24px;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

.message-time {
  font-size: 12px;
  color: #999999;
}

.message-brief {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 拼车信息 */
.carpool-info {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #EEEEEE;
}

.route-brief {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.route-text {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.departure-time {
  font-size: 12px;
  color: #1677FF;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999999;
}

/* 加载状态 */
.loading-state {
  padding: 16px 0;
  text-align: center;
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 列表底部 */
.list-bottom {
  padding: 16px 0;
  text-align: center;
}

.bottom-text {
  font-size: 14px;
  color: #999999;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.popup-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #EEEEEE;
}

.popup-title {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
}

.popup-close {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}

.popup-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.message-time-display {
  font-size: 12px;
  color: #999999;
  text-align: center;
  margin-bottom: 16px;
}

.message-full-content {
  padding-bottom: 16px;
}

.content-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 拼车详情 */
.carpool-detail {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #EEEEEE;
}

.detail-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12px;
}

.route-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #F8FAFB;
  padding: 12px;
  border-radius: 8px;
}

.route-points {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.start-point, .end-point {
  display: flex;
  align-items: center;
  gap: 10px;
}

.point-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.start {
  background-color: #1677FF;
}

.end {
  background-color: #FF5722;
}

.route-line {
  width: 2px;
  height: 20px;
  background-color: #DDDDDD;
  margin-left: 5px;
}

.point-text {
  font-size: 16px;
  color: #333333;
}

.trip-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-icon {
  width: 16px;
  height: 16px;
}

.info-text {
  font-size: 14px;
  color: #666666;
}

.price {
  color: #FF5722;
  font-weight: 500;
}

.popup-footer {
  display: flex;
  border-top: 1px solid #EEEEEE;
}

.popup-button {
  flex: 1;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 16px;
}

.popup-button.delete {
  color: #FF5722;
  border-right: 1px solid #EEEEEE;
}

.popup-button.confirm {
  color: #1677FF;
  font-weight: 500;
}

/* 底部悬浮清空按钮 */
.float-clear-btn {
  position: fixed;
  bottom: 30px;
  right: 20px;
  background: linear-gradient(135deg, #FF5722, #FF7043);
  height: 44px;
  padding: 0 20px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 87, 34, 0.3);
  z-index: 99;
}

.clear-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  filter: brightness(0) invert(1); /* 将图标变为白色 */
}

.float-clear-text {
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
}
</style> 