<template>
  <view class="verify-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <view class="safe-area-top"></view>
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">商家认证</view>
        <view class="navbar-right"></view>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 认证状态卡片 -->
      <view class="status-card" :class="[verifyInfo.status]">
        <view class="status-icon">
          <image :src="getStatusIcon()" class="icon-image"></image>
        </view>
        <view class="status-info">
          <view class="status-title">{{getStatusTitle()}}</view>
          <view class="status-desc">{{getStatusDesc()}}</view>
        </view>
        <view class="status-action" v-if="verifyInfo.status === 'unapplied' || verifyInfo.status === 'rejected'">
          <button class="action-btn" @click="startVerify">立即认证</button>
        </view>
      </view>
      
      <!-- 基本认证信息 -->
      <view class="verify-section" v-if="verifyInfo.status !== 'unapplied'">
        <view class="section-header">
          <text class="section-title">认证信息</text>
        </view>
        <view class="info-list">
          <view class="info-item">
            <text class="item-label">认证类型</text>
            <text class="item-value">{{verifyInfo.type}}</text>
          </view>
          <view class="info-item">
            <text class="item-label">认证名称</text>
            <text class="item-value">{{verifyInfo.name}}</text>
          </view>
          <view class="info-item">
            <text class="item-label">认证编号</text>
            <text class="item-value">{{verifyInfo.number}}</text>
          </view>
          <view class="info-item">
            <text class="item-label">认证时间</text>
            <text class="item-value">{{verifyInfo.time}}</text>
          </view>
          <view class="info-item" v-if="verifyInfo.status === 'rejected'">
            <text class="item-label">驳回原因</text>
            <text class="item-value reject-reason">{{verifyInfo.rejectReason}}</text>
          </view>
        </view>
      </view>
      
      <!-- 认证说明 -->
      <view class="guide-section">
        <view class="section-header">
          <text class="section-title">认证说明</text>
        </view>
        <view class="guide-content">
          <view class="guide-item">
            <view class="item-title">
              <text class="title-icon">1</text>
              <text class="title-text">为什么要认证？</text>
            </view>
            <view class="item-desc">商家认证后将获得平台信任标识，提高店铺信任度和曝光率</view>
          </view>
          <view class="guide-item">
            <view class="item-title">
              <text class="title-icon">2</text>
              <text class="title-text">如何进行认证？</text>
            </view>
            <view class="item-desc">点击"立即认证"按钮，按要求提交企业营业执照或个体工商户营业执照</view>
          </view>
          <view class="guide-item">
            <view class="item-title">
              <text class="title-icon">3</text>
              <text class="title-text">认证需要多久？</text>
            </view>
            <view class="item-desc">认证申请提交后，平台将在1-3个工作日内完成审核</view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 认证表单弹窗 -->
    <view class="modal-overlay" v-if="showVerifyModal" @click="cancelVerify"></view>
    <view class="verify-modal" v-if="showVerifyModal">
      <view class="modal-header">
        <text class="modal-title">商家认证</text>
        <view class="close-btn" @click="cancelVerify">×</view>
      </view>
      <view class="modal-content">
        <!-- 表单内容简化版 -->
        <view class="form-item">
          <text class="form-label required">认证类型</text>
          <view class="radio-group">
            <view class="radio-item" :class="{active: formData.type === '企业'}" @click="formData.type = '企业'">
              <text class="radio-text">企业</text>
            </view>
            <view class="radio-item" :class="{active: formData.type === '个体工商户'}" @click="formData.type = '个体工商户'">
              <text class="radio-text">个体工商户</text>
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label required">营业执照</text>
          <view class="upload-area" @click="uploadLicense" v-if="!formData.license">
            <text class="upload-icon">+</text>
            <text class="upload-text">上传营业执照照片</text>
          </view>
          <view class="license-preview" v-else>
            <image :src="formData.license" mode="aspectFit" class="license-image"></image>
            <view class="preview-actions">
              <view class="preview-btn" @click="uploadLicense">更换</view>
              <view class="preview-btn delete" @click="removeLicense">删除</view>
            </view>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="cancelVerify">取消</button>
        <button class="modal-btn confirm" @click="submitVerify" :disabled="!canSubmit">提交认证</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      showVerifyModal: false,
      verifyInfo: {
        status: 'unapplied', // unapplied(未认证), pending(审核中), verified(已认证), rejected(已驳回)
        type: '',
        name: '',
        number: '',
        time: '',
        rejectReason: ''
      },
      formData: {
        type: '企业',
        license: ''
      }
    }
  },
  computed: {
    canSubmit() {
      return this.formData.type && this.formData.license;
    }
  },
  onLoad() {
    this.setStatusBarHeight();
    this.loadVerifyInfo();
  },
  methods: {
    setStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    goBack() {
      uni.navigateBack();
    },
    loadVerifyInfo() {
      // 模拟加载认证信息
      // 实际应该从后端API获取
      setTimeout(() => {
        // 示例数据，根据实际情况修改
        this.verifyInfo = {
          status: 'verified', // 示例：已认证状态
          type: '企业',
          name: '磁州小吃美食店',
          number: 'GS123456789',
          time: '2023-10-15'
        };
      }, 500);
    },
    getStatusIcon() {
      const icons = {
        unapplied: '/static/images/verify-default.png',
        pending: '/static/images/verify-pending.png',
        verified: '/static/images/verify-success.png',
        rejected: '/static/images/verify-rejected.png'
      };
      return icons[this.verifyInfo.status];
    },
    getStatusTitle() {
      const titles = {
        unapplied: '未认证',
        pending: '认证审核中',
        verified: '已认证',
        rejected: '认证未通过'
      };
      return titles[this.verifyInfo.status];
    },
    getStatusDesc() {
      const descs = {
        unapplied: '商家尚未提交认证申请，请立即认证提高店铺可信度',
        pending: '您的认证申请正在审核中，预计1-3个工作日完成',
        verified: '恭喜！您的店铺已通过平台认证',
        rejected: '很遗憾，您的认证申请未通过审核'
      };
      return descs[this.verifyInfo.status];
    },
    startVerify() {
      this.showVerifyModal = true;
    },
    cancelVerify() {
      this.showVerifyModal = false;
    },
    uploadLicense() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.formData.license = res.tempFilePaths[0];
        }
      });
    },
    removeLicense() {
      this.formData.license = '';
    },
    submitVerify() {
      if (!this.canSubmit) {
        uni.showToast({
          title: '请填写完整认证信息',
          icon: 'none'
        });
        return;
      }
      
      uni.showLoading({
        title: '提交中...',
        mask: true
      });
      
      // 模拟提交
      setTimeout(() => {
        uni.hideLoading();
        this.showVerifyModal = false;
        
        // 更新认证状态为审核中
        this.verifyInfo.status = 'pending';
        this.verifyInfo.type = this.formData.type;
        this.verifyInfo.time = new Date().toLocaleDateString();
        
        uni.showToast({
          title: '提交成功',
          icon: 'success'
        });
      }, 1500);
    }
  }
}
</script>

<style lang="scss">
.verify-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(110rpx + var(--status-bar-height, 20px));
}

.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

.content-scroll {
  padding: 30rpx;
}

.status-card {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 16rpx;
  height: 100%;
}

.status-card.unapplied::before {
  background-color: #8E8E93;
}

.status-card.pending::before {
  background-color: #FF9500;
}

.status-card.verified::before {
  background-color: #34C759;
}

.status-card.rejected::before {
  background-color: #FF3B30;
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.icon-image {
  width: 100%;
  height: 100%;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.status-action {
  margin-left: 20rpx;
}

.action-btn {
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 4rpx 10rpx rgba(10, 132, 255, 0.3);
}

.verify-section, .guide-section {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #0A84FF;
  border-radius: 4rpx;
}

.info-list {
  padding: 10rpx 0;
}

.info-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #F2F2F7;
}

.info-item:last-child {
  border-bottom: none;
}

.item-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.item-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.reject-reason {
  color: #FF3B30;
}

.guide-content {
  padding: 10rpx 0;
}

.guide-item {
  margin-bottom: 30rpx;
}

.item-title {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.title-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #0A84FF;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.item-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding-left: 56rpx;
}

.safe-area-bottom {
  height: 40rpx;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.verify-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #F2F2F7;
  position: relative;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 36rpx;
  color: #999;
}

.modal-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required::after {
  content: '*';
  color: #FF3B30;
  margin-left: 6rpx;
}

.radio-group {
  display: flex;
}

.radio-item {
  padding: 16rpx 30rpx;
  background-color: #F5F8FC;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.radio-item.active {
  background-color: rgba(10, 132, 255, 0.1);
  border: 2rpx solid #0A84FF;
}

.radio-text {
  font-size: 28rpx;
  color: #333;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #F5F8FC;
  border: 2rpx dashed #CCCCCC;
  border-radius: 16rpx;
  padding: 40rpx 0;
}

.upload-icon {
  font-size: 48rpx;
  color: #0A84FF;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 28rpx;
  color: #666;
}

.license-preview {
  position: relative;
}

.license-image {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  background-color: #F5F8FC;
}

.preview-actions {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
}

.preview-btn {
  padding: 8rpx 16rpx;
  background-color: rgba(10, 132, 255, 0.5);
  color: #FFFFFF;
  font-size: 24rpx;
  border-radius: 8rpx;
  margin-left: 10rpx;
}

.preview-btn.delete {
  background-color: rgba(255, 59, 48, 0.5);
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background-color: transparent;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

.confirm[disabled] {
  color: #CCCCCC;
}

@media (prefers-color-scheme: dark) {
  .verify-container {
    background-color: #1C1C1E;
  }
  
  .status-card,
  .verify-section,
  .guide-section,
  .verify-modal {
    background-color: #2C2C2E;
  }
  
  .status-title,
  .section-title,
  .title-text,
  .modal-title,
  .form-label,
  .radio-text {
    color: #FFFFFF;
  }
  
  .status-desc,
  .item-desc {
    color: #A8A8A8;
  }
  
  .item-value {
    color: #DDDDDD;
  }
  
  .info-item {
    border-color: #3A3A3C;
  }
  
  .radio-item,
  .upload-area,
  .license-image {
    background-color: #3A3A3C;
  }
  
  .upload-text {
    color: #A8A8A8;
  }
  
  .modal-header,
  .modal-footer {
    border-color: #3A3A3C;
  }
}
</style> 