"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "home-service-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.onMounted(() => {
      try {
        const sysInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = sysInfo.statusBarHeight || 20;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/home-service-detail.vue:244", "获取状态栏高度失败", e);
      }
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const isCollected = common_vendor.ref(false);
    const posterImagePath = common_vendor.ref("");
    const showPosterFlag = common_vendor.ref(false);
    const relatedServices = common_vendor.ref([]);
    const loadRelatedServices = () => {
      const currentType = serviceData.value.type;
      const currentTypeCode = getSubTypeCode(currentType);
      setTimeout(() => {
        switch (currentTypeCode) {
          case "locksmith":
            relatedServices.value = [
              {
                id: "locksmith001",
                title: "24小时紧急开锁",
                price: "60元起",
                providerName: "安全开锁中心",
                providerLogo: "/static/images/tabbar/开锁.png",
                tags: ["24小时服务", "快速上门", "技术精湛"],
                type: "locksmith"
              },
              {
                id: "locksmith002",
                title: "智能锁安装服务",
                price: "150元起",
                providerName: "智能家居安装",
                providerLogo: "/static/images/tabbar/智能锁.png",
                tags: ["指纹锁", "密码锁", "刷卡锁"],
                type: "locksmith"
              },
              {
                id: "locksmith003",
                title: "保险柜开锁服务",
                price: "200元起",
                providerName: "专业开锁技师",
                providerLogo: "/static/images/tabbar/保险柜.png",
                tags: ["保险柜", "保险箱", "密码箱"],
                type: "locksmith"
              }
            ];
            break;
          case "installation":
            relatedServices.value = [
              {
                id: "installation001",
                title: "家具组装服务",
                price: "80元起",
                providerName: "家具安装中心",
                providerLogo: "/static/images/tabbar/家具.png",
                tags: ["专业工具", "经验丰富", "安装保障"],
                type: "installation"
              },
              {
                id: "installation002",
                title: "电器安装服务",
                price: "100元起",
                providerName: "电器安装专家",
                providerLogo: "/static/images/tabbar/电器.png",
                tags: ["空调安装", "电视安装", "洗衣机安装"],
                type: "installation"
              },
              {
                id: "installation003",
                title: "灯具安装服务",
                price: "60元起",
                providerName: "灯具安装师傅",
                providerLogo: "/static/images/tabbar/灯具.png",
                tags: ["吊灯安装", "射灯安装", "壁灯安装"],
                type: "installation"
              }
            ];
            break;
          case "home_cleaning":
          default:
            relatedServices.value = [
              {
                id: "service001",
                title: "专业擦玻璃服务",
                price: "40元/平方",
                providerName: "阿姨到家家政",
                providerLogo: "/static/images/tabbar/清洁.png",
                tags: ["高空作业", "安全保障", "专业设备"],
                type: "home_cleaning"
              },
              {
                id: "service002",
                title: "空调深度清洗",
                price: "100元/台起",
                providerName: "磁县家电清洗中心",
                providerLogo: "/static/images/tabbar/空调.png",
                tags: ["上门服务", "专业工具", "彻底除菌"],
                type: "home_cleaning"
              },
              {
                id: "service003",
                title: "深度除螨服务",
                price: "288元起",
                providerName: "优家净家政",
                providerLogo: "/static/images/tabbar/除螨.png",
                tags: ["沙发床垫", "紫外除螨", "深度杀菌"],
                type: "home_cleaning"
              }
            ];
            break;
        }
      }, 500);
    };
    const navigateToServiceDetail = (id, type) => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (id === options.id) {
        return;
      }
      const serviceType = type || getSubTypeCode(serviceData.value.type);
      common_vendor.index.navigateTo({
        url: `/pages/publish/home-service-detail?id=${id}&type=${serviceType}`
      });
    };
    const navigateToServiceList = (e) => {
      if (e)
        e.stopPropagation();
      const serviceCategory = serviceData.value.type || "";
      const serviceTypeCode = getSubTypeCode(serviceCategory);
      common_vendor.index.navigateTo({
        url: `/pages/service/home-service-list?subType=${serviceTypeCode}&subName=${encodeURIComponent(serviceCategory)}`
      });
    };
    const getSubTypeCode = (categoryName) => {
      const categoryMap = {
        "家政保洁": "home_cleaning",
        "家政服务": "home_cleaning",
        "维修改造": "repair",
        "上门安装": "installation",
        "开锁换锁": "locksmith",
        "搬家拉货": "moving",
        "上门美容": "beauty",
        "上门家教": "tutor",
        "宠物服务": "pet_service",
        "上门疏通": "plumbing"
      };
      return categoryMap[categoryName] || "home_cleaning";
    };
    const serviceData = common_vendor.ref({
      id: "service12345",
      title: "专业家政保洁服务",
      price: "50-120元/小时起",
      tags: ["专业保洁", "上门服务", "可预约", "环保用品", "全城服务"],
      publishTime: Date.now() - 864e5 * 2,
      // 2天前
      images: [
        "/static/images/service1.jpg",
        "/static/images/service2.jpg",
        "/static/images/service3.jpg",
        "/static/images/service4.jpg",
        "/static/images/service5.jpg"
      ],
      type: "家政保洁",
      area: "磁县城区及周边10公里",
      time: "8:00-20:00（节假日不休）",
      method: "上门服务，提前预约",
      contents: [
        { name: "日常保洁（擦拭、除尘、吸尘、拖地等）", icon: "icon-clean" },
        { name: "深度保洁（角落清洁、污渍处理等）", icon: "icon-deep-clean" },
        { name: "开荒保洁（新房入住前全面清洁）", icon: "icon-new-clean" },
        { name: "家电清洗（空调、冰箱、洗衣机等）", icon: "icon-appliance" },
        { name: "玻璃清洗（内外玻璃、高层玻璃）", icon: "icon-window" },
        { name: "地板打蜡（木地板、瓷砖等）", icon: "icon-floor" },
        { name: "沙发清洗（布艺、真皮沙发）", icon: "icon-sofa" },
        { name: "厨房深度清洁（油烟机、灶台等）", icon: "icon-kitchen" }
      ],
      description: "<p>磁县家政服务中心提供专业的家政保洁服务，服务范围包括但不限于：</p><ul><li>日常保洁：室内清洁、地面清洁、家具清洁、卫生间清洁等</li><li>深度保洁：死角清洁、顽固污渍处理、除菌消毒等</li><li>开荒保洁：新房入住前的全面清洁，包括装修后遗留物清理</li><li>家电清洗：专业拆卸清洗空调、冰箱、洗衣机、热水器等</li><li>玻璃清洗：内外玻璃、门窗、高层落地窗等</li><li>地板打蜡：各类地板专业打蜡、抛光、养护</li></ul><p>我们的服务特点：</p><ul><li>专业团队：全部员工经过专业培训，持证上岗</li><li>专业设备：使用先进清洁设备，提高清洁效率和质量</li><li>环保材料：使用环保清洁用品，安全健康无污染</li><li>灵活预约：支持线上预约，可提前1-7天预约服务</li><li>售后保障：服务不满意可重做，损坏物品照价赔偿</li></ul>",
      guarantees: [
        {
          icon: "icon-quality",
          title: "品质保障",
          desc: "专业保洁人员，持证上岗，统一着装，规范操作"
        },
        {
          icon: "icon-safe",
          title: "安全保障",
          desc: "员工背景调查，使用环保清洁用品，损坏赔付"
        },
        {
          icon: "icon-time",
          title: "准时服务",
          desc: "准时上门，不迟到，如有延误提前通知"
        },
        {
          icon: "icon-satisfaction",
          title: "满意保障",
          desc: "服务不满意可重做，服务质量有问题可投诉退款"
        },
        {
          icon: "icon-privacy",
          title: "隐私保障",
          desc: "保护客户隐私，不拍摄客户家庭环境"
        }
      ],
      provider: {
        name: "磁县家政服务中心",
        avatar: "/static/images/avatar.png",
        type: "专业机构（5年经验）",
        rating: 98,
        isVerified: true
      },
      contact: {
        name: "王经理（服务总监）",
        phone: "13912345678"
      }
    });
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: serviceData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const openChat = () => {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!userInfo) {
        common_vendor.index.showModal({
          title: "提示",
          content: "发送私信需要先登录，是否前往登录？",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/login/login?redirect=" + encodeURIComponent(`/pages/publish/home-service-detail?id=${serviceData.value.id}`)
              });
            }
          }
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/chat/chat?targetId=${serviceData.value.provider.id}&targetName=${serviceData.value.provider.name}&targetAvatar=${encodeURIComponent(serviceData.value.provider.avatar)}`
      });
    };
    const generateShareImage = () => {
      common_vendor.index.showLoading({
        title: "正在生成海报...",
        mask: true
      });
      const posterData = {
        title: serviceData.value.title,
        price: serviceData.value.price,
        type: serviceData.value.type,
        address: serviceData.value.area,
        phone: serviceData.value.contact ? serviceData.value.contact.phone : "",
        description: serviceData.value.description ? serviceData.value.description.substring(0, 60) + "..." : "",
        qrcode: "/static/images/tabbar/客服微信.png",
        logo: "/static/images/tabbar/家政服务.png",
        bgImage: serviceData.value.cover || "/static/images/banner/banner-1.png"
      };
      const ctx = common_vendor.index.createCanvasContext("posterCanvas");
      ctx.save();
      ctx.drawImage(posterData.bgImage, 0, 0, 600, 400);
      ctx.setFillStyle("rgba(0, 0, 0, 0.35)");
      ctx.fillRect(0, 0, 600, 900);
      ctx.restore();
      ctx.save();
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(30, 280, 540, 550);
      ctx.restore();
      ctx.save();
      ctx.beginPath();
      ctx.arc(300, 200, 80, 0, 2 * Math.PI);
      ctx.setFillStyle("#ffffff");
      ctx.fill();
      ctx.clip();
      ctx.drawImage(posterData.logo, 220, 120, 160, 160);
      ctx.restore();
      ctx.setFillStyle("#333333");
      ctx.setFontSize(32);
      ctx.setTextAlign("center");
      ctx.fillText(posterData.title, 300, 350);
      ctx.setFillStyle("#FF6B6B");
      ctx.setFontSize(28);
      ctx.fillText(posterData.price, 300, 400);
      ctx.beginPath();
      ctx.setStrokeStyle("#eeeeee");
      ctx.setLineWidth(2);
      ctx.moveTo(100, 430);
      ctx.lineTo(500, 430);
      ctx.stroke();
      ctx.setFillStyle("#666666");
      ctx.setFontSize(24);
      ctx.setTextAlign("left");
      ctx.fillText("服务类型: " + posterData.type, 80, 480);
      ctx.fillText("服务区域: " + posterData.address, 80, 520);
      const wrapText = (ctx2, text, x, y, maxWidth, lineHeight) => {
        if (text.length === 0)
          return;
        const words = text.split("");
        let line = "";
        let testLine = "";
        let lineCount = 0;
        for (let n = 0; n < words.length; n++) {
          testLine += words[n];
          const metrics = ctx2.measureText(testLine);
          const testWidth = metrics.width;
          if (testWidth > maxWidth && n > 0) {
            ctx2.fillText(line, x, y + lineCount * lineHeight);
            line = words[n];
            testLine = words[n];
            lineCount++;
            if (lineCount >= 3) {
              line += "...";
              ctx2.fillText(line, x, y + lineCount * lineHeight);
              break;
            }
          } else {
            line = testLine;
          }
        }
        if (lineCount < 3) {
          ctx2.fillText(line, x, y + lineCount * lineHeight);
        }
      };
      ctx.setFillStyle("#666666");
      wrapText(ctx, posterData.description, 80, 560, 440, 35);
      if (posterData.phone) {
        ctx.fillText("联系电话: " + posterData.phone, 80, 680);
      }
      ctx.drawImage(posterData.qrcode, 225, 720, 150, 150);
      ctx.setFillStyle("#999999");
      ctx.setFontSize(20);
      ctx.setTextAlign("center");
      ctx.fillText("长按识别二维码查看详情", 300, 880);
      ctx.setFillStyle("#333333");
      ctx.setFontSize(24);
      ctx.fillText("磁县同城 - 家政服务", 300, 840);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "posterCanvas",
            success: (res) => {
              common_vendor.index.hideLoading();
              showPosterModal(res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/publish/home-service-detail.vue:721", "生成海报失败", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "生成海报失败",
                icon: "none"
              });
            }
          });
        }, 800);
      });
    };
    const showPosterModal = (posterPath) => {
      posterImagePath.value = posterPath;
      showPosterFlag.value = true;
      common_vendor.index.showModal({
        title: "海报已生成",
        content: "海报已生成，是否保存到相册？",
        confirmText: "保存",
        success: (res) => {
          if (res.confirm) {
            savePosterToAlbum(posterPath);
          } else {
            common_vendor.index.previewImage({
              urls: [posterPath],
              current: posterPath
            });
          }
        }
      });
    };
    const savePosterToAlbum = (posterPath) => {
      common_vendor.index.showLoading({
        title: "正在保存..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: posterPath,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "已保存到相册",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/publish/home-service-detail.vue:773", "保存失败", err);
          if (err.errMsg.indexOf("auth deny") > -1) {
            common_vendor.index.showModal({
              title: "提示",
              content: "保存失败，请授权相册权限后重试",
              confirmText: "去设置",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    };
    const showReportOptions = () => {
      common_vendor.index.showActionSheet({
        itemList: ["虚假信息", "违法内容", "色情内容", "侵权投诉", "诱导欺骗", "其他问题"],
        success: (res) => {
          const reportReasonIndex = res.tapIndex;
          const reasons = ["虚假信息", "违法内容", "色情内容", "侵权投诉", "诱导欺骗", "其他问题"];
          showReportInputDialog(reasons[reportReasonIndex]);
        }
      });
    };
    const showReportInputDialog = (reason) => {
      const hasLogin = common_vendor.index.getStorageSync("token") || false;
      if (!hasLogin) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后再进行举报",
          confirmText: "去登录",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/login/login"
              });
            }
          }
        });
        return;
      }
      common_vendor.index.showModal({
        title: "举报内容",
        content: `您选择的举报原因是: ${reason}，请确认是否提交举报？`,
        confirmText: "确认举报",
        success: (res) => {
          if (res.confirm) {
            submitReport();
          }
        }
      });
    };
    const submitReport = (reason, content) => {
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "举报成功",
          icon: "success"
        });
      }, 1500);
    };
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      const id = options.id || "";
      const serviceType = options.type || "";
      loadServiceData(id, serviceType);
      loadRelatedServices();
    });
    const loadServiceData = (id, serviceType) => {
      common_vendor.index.__f__("log", "at pages/publish/home-service-detail.vue:890", "加载服务类型:", serviceType, "服务ID:", id || "默认服务");
      switch (serviceType) {
        case "installation":
          loadInstallationData(id);
          break;
        case "locksmith":
          loadLocksmithData(id);
          break;
        case "repair":
          loadRepairData(id);
          break;
        case "moving":
          loadMovingData(id);
          break;
        case "beauty":
          loadBeautyData(id);
          break;
        case "tutor":
          loadTutorData(id);
          break;
        case "pet_service":
          loadPetServiceData(id);
          break;
        case "plumbing":
          loadPlumbingData(id);
          break;
      }
      common_vendor.index.setNavigationBarTitle({
        title: serviceData.value.title || "服务详情"
      });
    };
    const loadInstallationData = (id) => {
      serviceData.value = {
        id: id || "installation12345",
        title: "专业上门安装服务",
        price: "60-200元/次起",
        tags: ["专业工具", "上门服务", "可预约", "全城服务"],
        publishTime: Date.now() - 864e5 * 3,
        // 3天前
        images: [
          "/static/images/service2.jpg",
          "/static/images/service3.jpg",
          "/static/images/service1.jpg"
        ],
        type: "上门安装",
        area: "磁县城区及周边15公里",
        time: "8:00-21:00（节假日照常服务）",
        method: "上门服务，提前预约",
        contents: [
          { name: "家具安装（床、柜、桌椅等）", icon: "icon-furniture" },
          { name: "电器安装（电视、空调等）", icon: "icon-appliance" },
          { name: "灯具安装（吊灯、壁灯等）", icon: "icon-light" },
          { name: "卫浴安装（花洒、马桶等）", icon: "icon-bathroom" },
          { name: "门窗安装（室内门、推拉门等）", icon: "icon-door" },
          { name: "五金安装（挂钩、置物架等）", icon: "icon-hardware" }
        ],
        description: "<p>磁县专业上门安装服务，提供各类家具、电器、灯具等安装服务：</p><ul><li>家具安装：床、衣柜、书柜、桌椅、沙发等</li><li>电器安装：电视机、空调、洗衣机、热水器等</li><li>灯具安装：吊灯、吸顶灯、壁灯、射灯等</li><li>卫浴安装：花洒、马桶、浴室柜、浴缸等</li><li>门窗安装：室内门、推拉门、折叠门等</li><li>五金安装：窗帘杆、挂钩、置物架等</li></ul><p>服务特点：</p><ul><li>专业师傅：经验丰富，技术精湛</li><li>专业工具：配备各类专业安装工具</li><li>上门服务：直接到府上安装，省时省力</li><li>质量保障：安装完成后检查确认，确保质量</li></ul>",
        guarantees: [
          {
            icon: "icon-quality",
            title: "品质保障",
            desc: "专业安装师傅，持证上岗，经验丰富"
          },
          {
            icon: "icon-safe",
            title: "安全保障",
            desc: "师傅背景调查，安装规范，损坏赔付"
          },
          {
            icon: "icon-time",
            title: "准时服务",
            desc: "准时上门，不迟到，如有延误提前通知"
          }
        ],
        provider: {
          name: "磁县家居安装服务中心",
          avatar: "/static/images/avatar.png",
          type: "专业机构（6年经验）",
          rating: 97,
          isVerified: true
        },
        contact: {
          name: "李师傅（安装主管）",
          phone: "13987654321"
        }
      };
    };
    const loadLocksmithData = (id) => {
      serviceData.value = {
        id: id || "locksmith12345",
        title: "专业开锁换锁服务",
        price: "60-180元/次起",
        tags: ["24小时服务", "快速上门", "专业技师", "全城服务"],
        publishTime: Date.now() - 864e5 * 1,
        // 1天前
        images: [
          "/static/images/service3.jpg",
          "/static/images/service1.jpg",
          "/static/images/service2.jpg"
        ],
        type: "开锁换锁",
        area: "磁县城区及周边20公里",
        time: "全天24小时（节假日不休）",
        method: "上门服务，随叫随到",
        contents: [
          { name: "开锁服务（防盗门、保险柜等）", icon: "icon-unlock" },
          { name: "换锁服务（门锁、抽屉锁等）", icon: "icon-lock" },
          { name: "修锁服务（锁芯修复等）", icon: "icon-repair" },
          { name: "配钥匙（各类钥匙配制）", icon: "icon-key" },
          { name: "安装锁具（新锁安装）", icon: "icon-install" },
          { name: "智能锁安装（指纹锁、密码锁）", icon: "icon-smart" }
        ],
        description: "<p>磁县专业开锁换锁服务，提供24小时上门服务：</p><ul><li>开锁服务：防盗门、木门、保险柜、抽屉、汽车等各类锁具开启</li><li>换锁服务：更换各类门锁、抽屉锁、保险柜锁等</li><li>修锁服务：修复锁芯、门锁调整等</li><li>配钥匙：各类钥匙复制、配制</li><li>安装锁具：新锁具安装、加装等</li><li>智能锁安装：指纹锁、密码锁、刷卡锁等智能锁安装</li></ul><p>服务特点：</p><ul><li>24小时服务：随时响应您的需求</li><li>快速上门：接单后30分钟内到达</li><li>专业技师：持证上岗，技术精湛</li><li>合理收费：透明价格，无隐形消费</li></ul>",
        guarantees: [
          {
            icon: "icon-quality",
            title: "品质保障",
            desc: "专业开锁师傅，持证上岗，经验丰富"
          },
          {
            icon: "icon-safe",
            title: "安全保障",
            desc: "师傅身份验证，开锁需验证身份"
          },
          {
            icon: "icon-time",
            title: "快速响应",
            desc: "30分钟内上门，紧急情况优先处理"
          }
        ],
        provider: {
          name: "磁县安全开锁服务中心",
          avatar: "/static/images/avatar.png",
          type: "专业机构（8年经验）",
          rating: 98,
          isVerified: true
        },
        contact: {
          name: "王师傅（开锁技师）",
          phone: "13876543210"
        }
      };
    };
    const loadRepairData = (id) => {
      serviceData.value = {
        id: id || "repair12345",
        title: "专业维修改造服务",
        price: "80-300元/次起",
        tags: ["专业维修", "上门服务", "可预约", "全城服务"],
        publishTime: Date.now() - 864e5 * 2,
        // 2天前
        images: [
          "/static/images/service1.jpg",
          "/static/images/service2.jpg",
          "/static/images/service3.jpg"
        ],
        type: "维修改造",
        area: "磁县城区及周边15公里",
        time: "8:00-20:00（节假日照常服务）",
        method: "上门服务，提前预约",
        contents: [
          { name: "水电维修（水管、电路等）", icon: "icon-water-electric" },
          { name: "家具维修（桌椅、柜子等）", icon: "icon-furniture" },
          { name: "家电维修（冰箱、洗衣机等）", icon: "icon-appliance" },
          { name: "门窗维修（门锁、合页等）", icon: "icon-door" },
          { name: "墙面维修（墙漆、裂缝等）", icon: "icon-wall" },
          { name: "小型改造（隔断、架子等）", icon: "icon-renovation" }
        ],
        description: "<p>磁县专业维修改造服务，解决您家中各类维修需求：</p><ul><li>水电维修：水管漏水、电路故障、开关插座更换等</li><li>家具维修：桌椅修复、柜门调整、五金更换等</li><li>家电维修：常见家电故障排除、清洗保养等</li><li>门窗维修：门锁更换、合页调整、密封条更换等</li><li>墙面维修：墙漆修补、裂缝处理、壁纸修复等</li><li>小型改造：简易隔断、置物架安装、小型装修等</li></ul><p>服务特点：</p><ul><li>专业师傅：经验丰富，技术全面</li><li>快速响应：当天预约，快速上门</li><li>合理收费：先检查后报价，认可后施工</li><li>质量保障：维修后保修期内免费返修</li></ul>",
        guarantees: [
          {
            icon: "icon-quality",
            title: "品质保障",
            desc: "专业维修师傅，技术全面，经验丰富"
          },
          {
            icon: "icon-safe",
            title: "安全保障",
            desc: "规范操作，确保安全，损坏赔付"
          },
          {
            icon: "icon-time",
            title: "保修服务",
            desc: "维修后提供保修期，期内免费返修"
          }
        ],
        provider: {
          name: "磁县万能维修中心",
          avatar: "/static/images/avatar.png",
          type: "专业机构（7年经验）",
          rating: 96,
          isVerified: true
        },
        contact: {
          name: "张师傅（维修主管）",
          phone: "13765432109"
        }
      };
    };
    const loadMovingData = (id) => {
      serviceData.value = {
        id: id || "moving12345",
        title: "专业搬家拉货服务",
        price: "100-500元/次起",
        tags: ["专业搬运", "全城服务", "可预约", "价格合理"],
        type: "搬家拉货"
        // ...其他搬家拉货相关数据
      };
    };
    const loadBeautyData = (id) => {
      serviceData.value = {
        id: id || "beauty12345",
        title: "专业上门美容服务",
        price: "80-300元/次起",
        tags: ["专业美容", "上门服务", "可预约", "全城服务"],
        type: "上门美容"
        // ...其他上门美容相关数据
      };
    };
    const loadTutorData = (id) => {
      serviceData.value = {
        id: id || "tutor12345",
        title: "专业上门家教服务",
        price: "100-200元/小时起",
        tags: ["专业教师", "上门授课", "可预约", "全城服务"],
        type: "上门家教"
        // ...其他上门家教相关数据
      };
    };
    const loadPetServiceData = (id) => {
      serviceData.value = {
        id: id || "pet_service12345",
        title: "专业宠物上门服务",
        price: "80-300元/次起",
        tags: ["宠物美容", "宠物医疗", "上门服务", "全城服务"],
        type: "宠物服务"
        // ...其他宠物服务相关数据
      };
    };
    const loadPlumbingData = (id) => {
      serviceData.value = {
        id: id || "plumbing12345",
        title: "专业上门疏通服务",
        price: "80-200元/次起",
        tags: ["管道疏通", "马桶疏通", "上门服务", "全城服务"],
        type: "上门疏通"
        // ...其他上门疏通相关数据
      };
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_assets._imports_10,
        e: common_vendor.o(generateShareImage),
        f: common_vendor.t(serviceData.value.title),
        g: common_vendor.t(serviceData.value.price),
        h: common_vendor.f(serviceData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        i: common_vendor.t(formatTime(serviceData.value.publishTime)),
        j: common_vendor.f(serviceData.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        k: common_vendor.t(serviceData.value.type),
        l: common_vendor.t(serviceData.value.area),
        m: common_vendor.t(serviceData.value.time),
        n: common_vendor.t(serviceData.value.method),
        o: common_vendor.f(serviceData.value.contents, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.name),
            c: index
          };
        }),
        p: serviceData.value.description,
        q: common_vendor.f(serviceData.value.guarantees, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.desc),
            d: index
          };
        }),
        r: serviceData.value.provider.avatar,
        s: common_vendor.t(serviceData.value.provider.name),
        t: common_vendor.t(serviceData.value.provider.type),
        v: common_vendor.t(serviceData.value.provider.rating),
        w: serviceData.value.provider.isVerified
      }, serviceData.value.provider.isVerified ? {} : {}, {
        x: common_vendor.t(serviceData.value.contact.name),
        y: common_vendor.t(serviceData.value.contact.phone),
        z: common_vendor.o(callPhone),
        A: common_assets._imports_0$6,
        B: common_vendor.o(showReportOptions),
        C: common_vendor.f(relatedServices.value.slice(0, 3), (service, index, i0) => {
          return common_vendor.e({
            a: service.providerLogo,
            b: common_vendor.t(service.title),
            c: common_vendor.t(service.providerName),
            d: common_vendor.f(service.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            e: service.tags.length > 2
          }, service.tags.length > 2 ? {
            f: common_vendor.t(service.tags.length - 2)
          } : {}, {
            g: common_vendor.t(service.price),
            h: index,
            i: common_vendor.o(($event) => navigateToServiceDetail(service.id, service.type), index)
          });
        }),
        D: relatedServices.value.length === 0
      }, relatedServices.value.length === 0 ? {
        E: common_assets._imports_1$3
      } : {}, {
        F: relatedServices.value.length > 0
      }, relatedServices.value.length > 0 ? {
        G: common_vendor.o(navigateToServiceList)
      } : {}, {
        H: common_assets._imports_12,
        I: common_vendor.o(goToHome),
        J: common_assets._imports_3$2,
        K: common_vendor.o(toggleCollect),
        L: common_assets._imports_3$3,
        M: common_assets._imports_14,
        N: common_vendor.o(openChat),
        O: common_vendor.o(callPhone)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/home-service-detail.js.map
