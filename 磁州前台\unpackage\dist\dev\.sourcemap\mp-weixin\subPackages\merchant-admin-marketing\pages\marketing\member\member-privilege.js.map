{"version": 3, "file": "member-privilege.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/member/member-privilege.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xtZW1iZXJcbWVtYmVyLXByaXZpbGVnZS52dWU"], "sourcesContent": ["<!-- 会员特权页面开始 -->\r\n<template>\r\n  <view class=\"member-privilege-container\">\r\n    <!-- 页面标题区域 -->\r\n    <view class=\"page-header\">\r\n      <view class=\"title-section\">\r\n        <text class=\"page-title\">会员特权</text>\r\n        <text class=\"page-subtitle\">管理店铺会员专属特权</text>\r\n      </view>\r\n      \r\n      <!-- 添加特权按钮 -->\r\n      <view class=\"add-privilege-btn\" @click=\"showAddPrivilegeModal\">\r\n        <text class=\"btn-text\">添加特权</text>\r\n        <text class=\"btn-icon\">+</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 特权类型快捷入口 -->\r\n    <view class=\"privilege-shortcuts\">\r\n      <view class=\"shortcut-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/member-discount')\">\r\n        <view class=\"shortcut-icon\" style=\"background-color: #FF6B22;\">\r\n          <text class=\"icon-text\">折</text>\r\n        </view>\r\n        <text class=\"shortcut-name\">会员折扣</text>\r\n      </view>\r\n      <view class=\"shortcut-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/points-acceleration')\">\r\n        <view class=\"shortcut-icon\" style=\"background-color: #1E90FF;\">\r\n          <text class=\"icon-text\">积</text>\r\n        </view>\r\n        <text class=\"shortcut-name\">积分加速</text>\r\n      </view>\r\n      <view class=\"shortcut-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/free-shipping')\">\r\n        <view class=\"shortcut-icon\" style=\"background-color: #32CD32;\">\r\n          <text class=\"icon-text\">送</text>\r\n        </view>\r\n        <text class=\"shortcut-name\">免费配送</text>\r\n      </view>\r\n      <view class=\"shortcut-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/birthday-gift')\">\r\n        <view class=\"shortcut-icon\" style=\"background-color: #FF69B4;\">\r\n          <text class=\"icon-text\">礼</text>\r\n        </view>\r\n        <text class=\"shortcut-name\">生日礼包</text>\r\n      </view>\r\n      <view class=\"shortcut-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/member/vip-service')\">\r\n        <view class=\"shortcut-icon\" style=\"background-color: #8A2BE2;\">\r\n          <text class=\"icon-text\">客</text>\r\n        </view>\r\n        <text class=\"shortcut-name\">专属客服</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 会员特权列表 -->\r\n    <view class=\"privilege-list\">\r\n      <view v-if=\"privileges.length === 0\" class=\"empty-tip\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty-data.svg\"></image>\r\n        <text class=\"empty-text\">暂无会员特权，点击\"添加特权\"创建</text>\r\n      </view>\r\n      \r\n      <view v-else class=\"privilege-cards\">\r\n        <view v-for=\"(privilege, index) in privileges\" :key=\"index\" class=\"privilege-card\">\r\n          <view class=\"privilege-card-header\" :style=\"{ backgroundColor: privilege.color }\">\r\n            <view class=\"privilege-name\">{{ privilege.name }}</view>\r\n            <view class=\"privilege-actions\">\r\n              <text class=\"action-btn edit\" @click=\"editPrivilege(privilege)\">编辑</text>\r\n              <text class=\"action-btn delete\" @click=\"confirmDeletePrivilege(privilege)\">删除</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"privilege-card-body\">\r\n            <view class=\"privilege-info-item\">\r\n              <text class=\"info-label\">特权图标：</text>\r\n              <image class=\"privilege-icon\" :src=\"privilege.icon\" mode=\"aspectFit\"></image>\r\n            </view>\r\n            <view class=\"privilege-info-item\">\r\n              <text class=\"info-label\">适用等级：</text>\r\n              <view class=\"level-tags\">\r\n                <text \r\n                  v-for=\"(level, idx) in privilege.applicableLevels\" \r\n                  :key=\"idx\" \r\n                  class=\"level-tag\"\r\n                >{{ level }}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"privilege-info-item\">\r\n              <text class=\"info-label\">特权类型：</text>\r\n              <text class=\"info-value\">{{ privilege.type }}</text>\r\n            </view>\r\n            <view class=\"privilege-info-item\">\r\n              <text class=\"info-label\">特权价值：</text>\r\n              <text class=\"info-value\">{{ privilege.value }}</text>\r\n            </view>\r\n            <view class=\"privilege-info-item\">\r\n              <text class=\"info-label\">特权说明：</text>\r\n              <text class=\"info-value\">{{ privilege.description || '暂无特权说明' }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 添加/编辑特权弹窗 -->\r\n    <uni-popup ref=\"privilegeFormPopup\" type=\"center\">\r\n      <view class=\"privilege-form-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">{{ isEditing ? '编辑特权' : '添加特权' }}</text>\r\n          <text class=\"popup-close\" @click=\"closePrivilegeModal\">×</text>\r\n        </view>\r\n        <view class=\"popup-body\">\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">特权名称</text>\r\n            <input class=\"form-input\" v-model=\"privilegeForm.name\" placeholder=\"请输入特权名称\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">特权颜色</text>\r\n            <view class=\"color-picker\">\r\n              <view \r\n                v-for=\"(color, idx) in colorOptions\" \r\n                :key=\"idx\" \r\n                class=\"color-option\"\r\n                :class=\"{ active: privilegeForm.color === color }\"\r\n                :style=\"{ backgroundColor: color }\"\r\n                @click=\"privilegeForm.color = color\"\r\n              ></view>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">特权图标</text>\r\n            <view class=\"icon-upload\">\r\n              <image v-if=\"privilegeForm.icon\" class=\"preview-icon\" :src=\"privilegeForm.icon\" mode=\"aspectFit\"></image>\r\n              <view v-else class=\"upload-btn\" @click=\"chooseIcon\">\r\n                <text class=\"upload-icon\">+</text>\r\n                <text class=\"upload-text\">上传图标</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">特权类型</text>\r\n            <picker class=\"form-picker\" :range=\"privilegeTypes\" @change=\"onTypeChange\">\r\n              <view class=\"picker-value\">{{ privilegeForm.type || '请选择特权类型' }}</view>\r\n            </picker>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">特权价值</text>\r\n            <input class=\"form-input\" v-model=\"privilegeForm.value\" placeholder=\"请输入特权价值，如折扣力度、赠送数量等\" />\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">适用等级</text>\r\n            <view class=\"level-checkboxes\">\r\n              <view \r\n                v-for=\"(level, idx) in memberLevels\" \r\n                :key=\"idx\" \r\n                class=\"level-checkbox\"\r\n                :class=\"{ active: isLevelSelected(level.name) }\"\r\n                @click=\"toggleLevelSelection(level.name)\"\r\n              >\r\n                <text class=\"checkbox-icon\">{{ isLevelSelected(level.name) ? '✓' : '' }}</text>\r\n                <text class=\"checkbox-label\">{{ level.name }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"form-item\">\r\n            <text class=\"form-label\">特权说明</text>\r\n            <textarea class=\"form-textarea\" v-model=\"privilegeForm.description\" placeholder=\"请输入特权说明\"></textarea>\r\n          </view>\r\n        </view>\r\n        <view class=\"popup-footer\">\r\n          <button class=\"cancel-btn\" @click=\"closePrivilegeModal\">取消</button>\r\n          <button class=\"confirm-btn\" @click=\"savePrivilegeForm\">确认</button>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 删除确认弹窗 -->\r\n    <uni-popup ref=\"deleteConfirmPopup\" type=\"dialog\">\r\n      <uni-popup-dialog\r\n        type=\"warning\"\r\n        title=\"删除确认\"\r\n        content=\"确定要删除该会员特权吗？删除后将无法恢复。\"\r\n        :before-close=\"true\"\r\n        @confirm=\"deletePrivilege\"\r\n        @close=\"closeDeleteConfirm\"\r\n      ></uni-popup-dialog>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      privileges: [], // 会员特权列表\r\n      privilegeForm: {\r\n        id: '',\r\n        name: '',\r\n        color: '#FF6B22', // 默认橙色\r\n        icon: '',\r\n        type: '',\r\n        value: '',\r\n        applicableLevels: [],\r\n        description: ''\r\n      },\r\n      isEditing: false, // 是否为编辑模式\r\n      currentPrivilegeId: null, // 当前编辑的特权ID\r\n      colorOptions: [\r\n        '#FF6B22', // 橙色\r\n        '#8A2BE2', // 紫色\r\n        '#1E90FF', // 道奇蓝\r\n        '#32CD32', // 酸橙绿\r\n        '#FFD700', // 金色\r\n        '#FF69B4', // 热粉红\r\n        '#20B2AA', // 浅海绿\r\n        '#FF8C00'  // 深橙色\r\n      ],\r\n      privilegeTypes: [\r\n        '折扣优惠',\r\n        '积分加速',\r\n        '专属礼品',\r\n        '免费服务',\r\n        '专属活动',\r\n        '专属客服',\r\n        '其他特权'\r\n      ],\r\n      memberLevels: [] // 会员等级列表\r\n    };\r\n  },\r\n  onLoad() {\r\n    this.fetchPrivileges();\r\n    this.fetchMemberLevels();\r\n  },\r\n  methods: {\r\n    // 页面导航\r\n    navigateTo(url) {\r\n      uni.navigateTo({\r\n        url: url\r\n      });\r\n    },\r\n    \r\n    // 获取会员特权列表\r\n    fetchPrivileges() {\r\n      // 模拟数据，实际项目中应从API获取\r\n      this.privileges = [\r\n        {\r\n          id: '1',\r\n          name: '生日礼包',\r\n          color: '#FF69B4',\r\n          icon: '/static/images/privilege-birthday.svg',\r\n          type: '专属礼品',\r\n          value: '价值100元礼品',\r\n          applicableLevels: ['金卡会员', '钻石会员'],\r\n          description: '会员生日当月可领取专属生日礼包一份'\r\n        },\r\n        {\r\n          id: '2',\r\n          name: '专属折扣',\r\n          color: '#FF6B22',\r\n          icon: '/static/images/privilege-discount.svg',\r\n          type: '折扣优惠',\r\n          value: '9折起',\r\n          applicableLevels: ['银卡会员', '金卡会员', '钻石会员'],\r\n          description: '会员专享商品折扣，最低享受9折优惠'\r\n        },\r\n        {\r\n          id: '3',\r\n          name: '积分翻倍',\r\n          color: '#1E90FF',\r\n          icon: '/static/images/privilege-points.svg',\r\n          type: '积分加速',\r\n          value: '最高2倍积分',\r\n          applicableLevels: ['金卡会员', '钻石会员'],\r\n          description: '消费即可获得最高2倍积分奖励'\r\n        }\r\n      ];\r\n    },\r\n    \r\n    // 获取会员等级列表\r\n    fetchMemberLevels() {\r\n      // 模拟数据，实际项目中应从API获取\r\n      this.memberLevels = [\r\n        {\r\n          id: '1',\r\n          name: '普通会员'\r\n        },\r\n        {\r\n          id: '2',\r\n          name: '银卡会员'\r\n        },\r\n        {\r\n          id: '3',\r\n          name: '金卡会员'\r\n        },\r\n        {\r\n          id: '4',\r\n          name: '钻石会员'\r\n        }\r\n      ];\r\n    },\r\n    \r\n    // 显示添加特权弹窗\r\n    showAddPrivilegeModal() {\r\n      this.isEditing = false;\r\n      this.resetPrivilegeForm();\r\n      this.$refs.privilegeFormPopup.open();\r\n    },\r\n    \r\n    // 编辑特权\r\n    editPrivilege(privilege) {\r\n      this.isEditing = true;\r\n      this.currentPrivilegeId = privilege.id;\r\n      this.privilegeForm = { ...privilege };\r\n      this.$refs.privilegeFormPopup.open();\r\n    },\r\n    \r\n    // 关闭特权表单弹窗\r\n    closePrivilegeModal() {\r\n      this.$refs.privilegeFormPopup.close();\r\n      this.resetPrivilegeForm();\r\n    },\r\n    \r\n    // 重置表单\r\n    resetPrivilegeForm() {\r\n      this.privilegeForm = {\r\n        id: '',\r\n        name: '',\r\n        color: '#FF6B22',\r\n        icon: '',\r\n        type: '',\r\n        value: '',\r\n        applicableLevels: [],\r\n        description: ''\r\n      };\r\n      this.currentPrivilegeId = null;\r\n    },\r\n    \r\n    // 选择图标\r\n    chooseIcon() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        success: (res) => {\r\n          this.privilegeForm.icon = res.tempFilePaths[0];\r\n          // 实际项目中应上传图片到服务器并获取URL\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 特权类型选择变化\r\n    onTypeChange(e) {\r\n      const index = e.detail.value;\r\n      this.privilegeForm.type = this.privilegeTypes[index];\r\n    },\r\n    \r\n    // 检查等级是否已选中\r\n    isLevelSelected(levelName) {\r\n      return this.privilegeForm.applicableLevels.includes(levelName);\r\n    },\r\n    \r\n    // 切换等级选择状态\r\n    toggleLevelSelection(levelName) {\r\n      const index = this.privilegeForm.applicableLevels.indexOf(levelName);\r\n      if (index === -1) {\r\n        this.privilegeForm.applicableLevels.push(levelName);\r\n      } else {\r\n        this.privilegeForm.applicableLevels.splice(index, 1);\r\n      }\r\n    },\r\n    \r\n    // 保存特权表单\r\n    savePrivilegeForm() {\r\n      // 表单验证\r\n      if (!this.privilegeForm.name) {\r\n        uni.showToast({\r\n          title: '请输入特权名称',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.privilegeForm.type) {\r\n        uni.showToast({\r\n          title: '请选择特权类型',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.privilegeForm.value) {\r\n        uni.showToast({\r\n          title: '请输入特权价值',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (this.privilegeForm.applicableLevels.length === 0) {\r\n        uni.showToast({\r\n          title: '请选择适用等级',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (this.isEditing) {\r\n        // 编辑现有特权\r\n        const index = this.privileges.findIndex(item => item.id === this.currentPrivilegeId);\r\n        if (index !== -1) {\r\n          this.privileges[index] = { ...this.privilegeForm, id: this.currentPrivilegeId };\r\n        }\r\n      } else {\r\n        // 添加新特权\r\n        const newPrivilege = {\r\n          ...this.privilegeForm,\r\n          id: Date.now().toString() // 生成临时ID，实际项目中应由后端生成\r\n        };\r\n        this.privileges.push(newPrivilege);\r\n      }\r\n      \r\n      // 关闭弹窗\r\n      this.$refs.privilegeFormPopup.close();\r\n      this.resetPrivilegeForm();\r\n      \r\n      // 提示成功\r\n      uni.showToast({\r\n        title: this.isEditing ? '编辑成功' : '添加成功',\r\n        icon: 'success'\r\n      });\r\n    },\r\n    \r\n    // 确认删除特权\r\n    confirmDeletePrivilege(privilege) {\r\n      this.currentPrivilegeId = privilege.id;\r\n      this.$refs.deleteConfirmPopup.open();\r\n    },\r\n    \r\n    // 关闭删除确认弹窗\r\n    closeDeleteConfirm() {\r\n      this.$refs.deleteConfirmPopup.close();\r\n    },\r\n    \r\n    // 删除特权\r\n    deletePrivilege() {\r\n      const index = this.privileges.findIndex(item => item.id === this.currentPrivilegeId);\r\n      if (index !== -1) {\r\n        this.privileges.splice(index, 1);\r\n        uni.showToast({\r\n          title: '删除成功',\r\n          icon: 'success'\r\n        });\r\n      }\r\n      this.currentPrivilegeId = null;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 会员特权页面样式开始 */\r\n.member-privilege-container {\r\n  padding: 30rpx;\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.title-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  \r\n  .page-title {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n  \r\n  .page-subtitle {\r\n    font-size: 24rpx;\r\n    color: #666;\r\n    margin-top: 8rpx;\r\n  }\r\n}\r\n\r\n.add-privilege-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #FF6B22;\r\n  color: #fff;\r\n  padding: 16rpx 30rpx;\r\n  border-radius: 50rpx;\r\n  \r\n  .btn-text {\r\n    font-size: 28rpx;\r\n  }\r\n  \r\n  .btn-icon {\r\n    font-size: 32rpx;\r\n    margin-left: 8rpx;\r\n  }\r\n}\r\n\r\n/* 特权类型快捷入口样式 */\r\n.privilege-shortcuts {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 20rpx 10rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .shortcut-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    width: 20%;\r\n    \r\n    .shortcut-icon {\r\n      width: 80rpx;\r\n      height: 80rpx;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-bottom: 10rpx;\r\n      \r\n      .icon-text {\r\n        color: #fff;\r\n        font-size: 36rpx;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n    \r\n    .shortcut-name {\r\n      font-size: 24rpx;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n.privilege-list {\r\n  margin-top: 20rpx;\r\n  \r\n  .empty-tip {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 100rpx 0;\r\n    background-color: #fff;\r\n    border-radius: 12rpx;\r\n    \r\n    .empty-icon {\r\n      width: 200rpx;\r\n      height: 200rpx;\r\n      margin-bottom: 20rpx;\r\n    }\r\n    \r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n}\r\n\r\n.privilege-cards {\r\n  .privilege-card {\r\n    background-color: #fff;\r\n    border-radius: 12rpx;\r\n    margin-bottom: 20rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n    \r\n    .privilege-card-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 20rpx 24rpx;\r\n      color: #fff;\r\n      \r\n      .privilege-name {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n      }\r\n      \r\n      .privilege-actions {\r\n        display: flex;\r\n        \r\n        .action-btn {\r\n          font-size: 24rpx;\r\n          padding: 6rpx 16rpx;\r\n          border-radius: 30rpx;\r\n          margin-left: 16rpx;\r\n          \r\n          &.edit {\r\n            background-color: rgba(255, 255, 255, 0.3);\r\n          }\r\n          \r\n          &.delete {\r\n            background-color: rgba(255, 255, 255, 0.3);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .privilege-card-body {\r\n      padding: 24rpx;\r\n      \r\n      .privilege-info-item {\r\n        display: flex;\r\n        margin-bottom: 16rpx;\r\n        \r\n        .info-label {\r\n          width: 160rpx;\r\n          font-size: 28rpx;\r\n          color: #666;\r\n        }\r\n        \r\n        .info-value {\r\n          flex: 1;\r\n          font-size: 28rpx;\r\n          color: #333;\r\n        }\r\n        \r\n        .privilege-icon {\r\n          width: 60rpx;\r\n          height: 60rpx;\r\n        }\r\n        \r\n        .level-tags {\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          \r\n          .level-tag {\r\n            font-size: 24rpx;\r\n            color: #4A00E0;\r\n            background-color: rgba(74, 0, 224, 0.1);\r\n            padding: 6rpx 16rpx;\r\n            border-radius: 6rpx;\r\n            margin-right: 12rpx;\r\n            margin-bottom: 12rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.privilege-form-popup {\r\n  width: 650rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  \r\n  .popup-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24rpx;\r\n    border-bottom: 1rpx solid #eee;\r\n    \r\n    .popup-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n    \r\n    .popup-close {\r\n      font-size: 40rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n  \r\n  .popup-body {\r\n    padding: 24rpx;\r\n    max-height: 60vh;\r\n    overflow-y: auto;\r\n    \r\n    .form-item {\r\n      margin-bottom: 24rpx;\r\n      \r\n      .form-label {\r\n        display: block;\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        margin-bottom: 12rpx;\r\n      }\r\n      \r\n      .form-input {\r\n        width: 100%;\r\n        height: 80rpx;\r\n        border: 1rpx solid #ddd;\r\n        border-radius: 8rpx;\r\n        padding: 0 20rpx;\r\n        font-size: 28rpx;\r\n        box-sizing: border-box;\r\n      }\r\n      \r\n      .form-textarea {\r\n        width: 100%;\r\n        height: 160rpx;\r\n        border: 1rpx solid #ddd;\r\n        border-radius: 8rpx;\r\n        padding: 20rpx;\r\n        font-size: 28rpx;\r\n        box-sizing: border-box;\r\n      }\r\n      \r\n      .form-picker {\r\n        width: 100%;\r\n        height: 80rpx;\r\n        border: 1rpx solid #ddd;\r\n        border-radius: 8rpx;\r\n        padding: 0 20rpx;\r\n        font-size: 28rpx;\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        .picker-value {\r\n          color: #333;\r\n        }\r\n      }\r\n      \r\n      .color-picker {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        \r\n        .color-option {\r\n          width: 60rpx;\r\n          height: 60rpx;\r\n          border-radius: 50%;\r\n          margin-right: 20rpx;\r\n          margin-bottom: 20rpx;\r\n          position: relative;\r\n          \r\n          &.active::after {\r\n            content: '✓';\r\n            position: absolute;\r\n            top: 50%;\r\n            left: 50%;\r\n            transform: translate(-50%, -50%);\r\n            color: #fff;\r\n            font-size: 32rpx;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .icon-upload {\r\n        .preview-icon {\r\n          width: 100rpx;\r\n          height: 100rpx;\r\n          border-radius: 8rpx;\r\n        }\r\n        \r\n        .upload-btn {\r\n          width: 100rpx;\r\n          height: 100rpx;\r\n          border: 1rpx dashed #ddd;\r\n          border-radius: 8rpx;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          \r\n          .upload-icon {\r\n            font-size: 40rpx;\r\n            color: #999;\r\n            margin-bottom: 4rpx;\r\n          }\r\n          \r\n          .upload-text {\r\n            font-size: 20rpx;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .level-checkboxes {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        \r\n        .level-checkbox {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-right: 30rpx;\r\n          margin-bottom: 20rpx;\r\n          \r\n          .checkbox-icon {\r\n            width: 40rpx;\r\n            height: 40rpx;\r\n            border: 1rpx solid #ddd;\r\n            border-radius: 8rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 10rpx;\r\n            color: #fff;\r\n            font-size: 24rpx;\r\n          }\r\n          \r\n          &.active .checkbox-icon {\r\n            background-color: #4A00E0;\r\n            border-color: #4A00E0;\r\n          }\r\n          \r\n          .checkbox-label {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .popup-footer {\r\n    display: flex;\r\n    border-top: 1rpx solid #eee;\r\n    \r\n    button {\r\n      flex: 1;\r\n      height: 90rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 32rpx;\r\n      border-radius: 0;\r\n      \r\n      &.cancel-btn {\r\n        background-color: #f5f5f5;\r\n        color: #666;\r\n      }\r\n      \r\n      &.confirm-btn {\r\n        background-color: #4A00E0;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<!-- 会员特权页面结束 --> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/member/member-privilege.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA0LA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY,CAAE;AAAA;AAAA,MACd,eAAe;AAAA,QACb,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,kBAAkB,CAAE;AAAA,QACpB,aAAa;AAAA,MACd;AAAA,MACD,WAAW;AAAA;AAAA,MACX,oBAAoB;AAAA;AAAA,MACpB,cAAc;AAAA,QACZ;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACD;AAAA,MACD,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,cAAc;;;EAEjB;AAAA,EACD,SAAS;AACP,SAAK,gBAAe;AACpB,SAAK,kBAAiB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAEhB,WAAK,aAAa;AAAA,QAChB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,kBAAkB,CAAC,QAAQ,MAAM;AAAA,UACjC,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,kBAAkB,CAAC,QAAQ,QAAQ,MAAM;AAAA,UACzC,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,kBAAkB,CAAC,QAAQ,MAAM;AAAA,UACjC,aAAa;AAAA,QACf;AAAA;IAEH;AAAA;AAAA,IAGD,oBAAoB;AAElB,WAAK,eAAe;AAAA,QAClB;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACP;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,QACR;AAAA;IAEH;AAAA;AAAA,IAGD,wBAAwB;AACtB,WAAK,YAAY;AACjB,WAAK,mBAAkB;AACvB,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,cAAc,WAAW;AACvB,WAAK,YAAY;AACjB,WAAK,qBAAqB,UAAU;AACpC,WAAK,gBAAgB,EAAE,GAAG;AAC1B,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,sBAAsB;AACpB,WAAK,MAAM,mBAAmB;AAC9B,WAAK,mBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,gBAAgB;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,kBAAkB,CAAE;AAAA,QACpB,aAAa;AAAA;AAEf,WAAK,qBAAqB;AAAA,IAC3B;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,SAAS,CAAC,QAAQ;AAChB,eAAK,cAAc,OAAO,IAAI,cAAc,CAAC;AAAA,QAE/C;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,YAAM,QAAQ,EAAE,OAAO;AACvB,WAAK,cAAc,OAAO,KAAK,eAAe,KAAK;AAAA,IACpD;AAAA;AAAA,IAGD,gBAAgB,WAAW;AACzB,aAAO,KAAK,cAAc,iBAAiB,SAAS,SAAS;AAAA,IAC9D;AAAA;AAAA,IAGD,qBAAqB,WAAW;AAC9B,YAAM,QAAQ,KAAK,cAAc,iBAAiB,QAAQ,SAAS;AACnE,UAAI,UAAU,IAAI;AAChB,aAAK,cAAc,iBAAiB,KAAK,SAAS;AAAA,aAC7C;AACL,aAAK,cAAc,iBAAiB,OAAO,OAAO,CAAC;AAAA,MACrD;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAElB,UAAI,CAAC,KAAK,cAAc,MAAM;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,cAAc,MAAM;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,cAAc,OAAO;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,KAAK,cAAc,iBAAiB,WAAW,GAAG;AACpDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,KAAK,WAAW;AAElB,cAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,KAAK,kBAAkB;AACnF,YAAI,UAAU,IAAI;AAChB,eAAK,WAAW,KAAK,IAAI,EAAE,GAAG,KAAK,eAAe,IAAI,KAAK;QAC7D;AAAA,aACK;AAEL,cAAM,eAAe;AAAA,UACnB,GAAG,KAAK;AAAA,UACR,IAAI,KAAK,IAAK,EAAC;;;AAEjB,aAAK,WAAW,KAAK,YAAY;AAAA,MACnC;AAGA,WAAK,MAAM,mBAAmB;AAC9B,WAAK,mBAAkB;AAGvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,YAAY,SAAS;AAAA,QACjC,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,uBAAuB,WAAW;AAChC,WAAK,qBAAqB,UAAU;AACpC,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,MAAM,mBAAmB;IAC/B;AAAA;AAAA,IAGD,kBAAkB;AAChB,YAAM,QAAQ,KAAK,WAAW,UAAU,UAAQ,KAAK,OAAO,KAAK,kBAAkB;AACnF,UAAI,UAAU,IAAI;AAChB,aAAK,WAAW,OAAO,OAAO,CAAC;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChcA,GAAG,WAAW,eAAe;"}