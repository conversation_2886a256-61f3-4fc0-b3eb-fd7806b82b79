"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const utils_errorHandler = require("./utils/errorHandler.js");
const components_RedPacket_index = require("./components/RedPacket/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/test-navigation.js";
  "./pages/redirect-fix.js";
  "./pages/publish/info-detail.js";
  "./pages/publish/job-detail.js";
  "./pages/publish/home-service-detail.js";
  "./pages/publish/business-transfer-detail.js";
  "./pages/publish/find-service-detail.js";
  "./pages/publish/job-seeking-detail.js";
  "./pages/publish/house-rent-detail.js";
  "./pages/publish/house-sale-detail.js";
  "./pages/publish/car-detail.js";
  "./pages/publish/pet-detail.js";
  "./pages/publish/second-hand-detail.js";
  "./pages/publish/dating-detail.js";
  "./pages/publish/merchant-activity-detail.js";
  "./pages/publish/carpool-detail.js";
  "./pages/publish/education-detail.js";
  "./pages/carpool-entry/index.js";
  "./pages/business/business.js";
  "./pages/publish/publish.js";
  "./pages/business/join.js";
  "./pages/business/success.js";
  "./pages/business/verify.js";
  "./pages/business/verify-success.js";
  "./pages/business/shop-detail.js";
  "./pages/business/filter.js";
  "./pages/group/group.js";
  "./pages/my/my.js";
  "./pages/my/publish.js";
  "./pages/my/refresh-package.js";
  "./pages/location/select.js";
  "./pages/error/index.js";
  "./pages/user/my-red-packets.js";
  "./pages/user/group-orders.js";
  "./pages/red-packet/detail.js";
  "./pages/red-packet/list.js";
  "./pages/publish/detail.js";
  "./pages/test/shop-test.js";
  "./pages/message/chat.js";
  "./pages/user-center/profile.js";
  "./pages/user-center/activities.js";
  "./pages/publish/success.js";
  "./pages/wallet/index.js";
  "./subPackages/partner/pages/partner.js";
  "./subPackages/partner/pages/partner-levels.js";
  "./subPackages/partner/pages/partner-poster.js";
  "./subPackages/partner/pages/partner-fans.js";
  "./subPackages/partner/pages/partner-team.js";
  "./subPackages/partner/pages/partner-income.js";
  "./subPackages/partner/pages/partner-withdraw.js";
  "./subPackages/partner/pages/partner-qrcode.js";
  "./subPackages/partner/pages/partner-rules.js";
  "./subPackages/payment/pages/index.js";
  "./subPackages/payment/pages/success.js";
  "./subPackages/payment/pages/wallet.js";
  "./subPackages/payment/pages/withdraw.js";
  "./subPackages/payment/pages/recharge.js";
  "./subPackages/payment/pages/detail.js";
  "./subPackages/payment/pages/bills.js";
  "./subPackages/payment/pages/bank.js";
  "./subPackages/activity/pages/square.js";
  "./subPackages/activity/pages/detail-with-rewards.js";
  "./subPackages/activity/pages/city-events.js";
  "./subPackages/activity/pages/detail.js";
  "./subPackages/activity/pages/list.js";
  "./subPackages/service/pages/filter.js";
  "./subPackages/service/pages/list.js";
  "./subPackages/service/pages/category.js";
  "./subPackages/service/pages/home-service-list.js";
  "./subPackages/user/pages/group-orders.js";
  "./subPackages/user/pages/my-red-packets.js";
  "./subPackages/user/pages/profile.js";
  "./subPackages/user/pages/my-benefits.js";
  "./subPackages/user/pages/messages.js";
  "./subPackages/user/pages/history.js";
  "./subPackages/user/pages/call-history.js";
  "./subPackages/user/pages/favorites.js";
  "./carpool-package/pages/carpool-main/index.js";
  "./carpool-package/pages/carpool/groups/index.js";
  "./carpool-package/pages/carpool/groups/create.js";
  "./carpool-package/pages/carpool/my/index.js";
  "./carpool-package/pages/carpool/detail/index.js";
  "./carpool-package/pages/carpool/my/message-center.js";
  "./carpool-package/pages/carpool/my/driver-verification.js";
  "./carpool-package/pages/carpool/my/feedback.js";
  "./carpool-package/pages/carpool/my/trip-records.js";
  "./carpool-package/pages/carpool/my/driver-ratings.js";
  "./carpool-package/pages/carpool/my/driver-profile.js";
  "./carpool-package/pages/carpool/my/contact-history.js";
  "./carpool-package/pages/carpool/my/published-list.js";
  "./carpool-package/pages/carpool/my/pending-list.js";
  "./carpool-package/pages/carpool/my/expired-list.js";
  "./carpool-package/pages/carpool/my/favorites.js";
  "./carpool-package/pages/carpool/publish/people-to-car.js";
  "./carpool-package/pages/carpool/publish/car-to-people.js";
  "./carpool-package/pages/carpool/publish/goods-to-car.js";
  "./carpool-package/pages/carpool/publish/car-to-goods.js";
  "./carpool-package/pages/carpool/publish/success.js";
  "./carpool-package/pages/carpool/premium/top.js";
  "./carpool-package/pages/carpool/my/create-rating.js";
  "./subPackages/news/pages/list.js";
  "./subPackages/news/pages/detail.js";
  "./subPackages/checkin/pages/daily-checkin.js";
  "./subPackages/checkin/pages/points.js";
  "./subPackages/checkin/pages/points-detail.js";
  "./subPackages/checkin/pages/points-rank.js";
  "./subPackages/checkin/pages/points-mall.js";
  "./subPackages/checkin/pages/exchange-history.js";
  "./subPackages/franchise/pages/index.js";
  "./subPackages/franchise/pages/application-form.js";
  "./subPackages/franchise/pages/agreement.js";
  "./subPackages/merchant-admin-home/pages/merchant.js";
  "./subPackages/merchant-admin-home/pages/merchant-home/index.js";
  "./subPackages/merchant-admin-home/pages/settings/index.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/index.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/coupon/detail.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/coupon/edit.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/coupon/create.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/coupon/records.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/coupon/management.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/discount/management.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/discount/detail.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/discount/edit.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/discount/create.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/group/management.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/group/create.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/group/package-management.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/group/package-detail.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/group/create-package-type.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/group/create-package-price.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/flash/management.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/flash/detail.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/flash/edit.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/flash/create.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/points/management.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/points/create.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/points/rules.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/create-coupon.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/index.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/create.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/edit.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/detail.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/data-analysis.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/guide.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/settings.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/mass-sending.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/red-rain.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/create-template.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/tool.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/festival-redpacket-strategy.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/redpacket-marketing-overview.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/redpacket/fission-redpacket-guide.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/ai/trend-analysis.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/index.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/product-cards.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/channels.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/reports.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/create-activity.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/pay-commission.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/add-distributor.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/levels.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/distribution/distributors.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/verification/index.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/verification/scan.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/verification/manual.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/verification/records.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/member-level.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/member-privilege.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/tool.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/points-rule.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/growth-rule.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/birthday-privilege.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/member-agreement.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/add-level.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/add-privilege.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/member-discount.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/points-acceleration.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/free-shipping.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/birthday-gift.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/vip-service.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/points.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/tasks.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/coupons.js";
  "./subPackages/merchant-admin-marketing/pages/marketing/member/activities.js";
  "./subPackages/merchant-admin-order/pages/order/index.js";
  "./subPackages/merchant-admin-order/pages/orders/list.js";
  "./subPackages/merchant-admin-order/pages/orders/detail.js";
  "./subPackages/merchant-admin-order/pages/orders/after-sale.js";
  "./subPackages/merchant-admin-customer/pages/customer/index.js";
  "./subPackages/merchant-admin-customer/pages/marketing/distribution/index.js";
  "./subPackages/merchant-admin-customer/pages/marketing/distribution/distributors.js";
  "./subPackages/merchant-admin/pages/store/index.js";
  "./subPackages/merchant-admin/pages/analysis/index.js";
  "./subPackages/merchant-admin/pages/settings/index.js";
  "./subPackages/merchant-admin/pages/activity/index.js";
  "./subPackages/merchant-admin/pages/products/create.js";
  "./subPackages/merchant-admin/pages/store/product.js";
  "./subPackages/promotion/pages/promotion-tool.js";
  "./subPackages/promotion/pages/qrcode.js";
  "./subPackages/distribution/pages/index.js";
  "./subPackages/distribution/pages/apply.js";
  "./subPackages/distribution/pages/products.js";
  "./subPackages/distribution/pages/team.js";
  "./subPackages/distribution/pages/commission.js";
  "./subPackages/distribution/pages/withdraw.js";
  "./subPackages/distribution/pages/promotion.js";
  "./subPackages/distribution/pages/merchant-apply.js";
  "./subPackages/cashback/pages/index/index.js";
  "./subPackages/cashback/pages/search/index.js";
  "./subPackages/cashback/pages/platforms/index.js";
  "./subPackages/cashback/pages/category/index.js";
  "./subPackages/cashback/pages/product-detail/index.js";
  "./subPackages/cashback/pages/life-cashback/index.js";
  "./subPackages/cashback/pages/life-service/index.js";
  "./subPackages/activity-showcase/pages/index/index.js";
  "./subPackages/activity-showcase/pages/flash-sale/index.js";
  "./subPackages/activity-showcase/pages/group-buy/index.js";
  "./subPackages/activity-showcase/pages/discount/index.js";
  "./subPackages/activity-showcase/pages/coupon/index.js";
  "./subPackages/activity-showcase/pages/detail/index.js";
  "./subPackages/activity-showcase/pages/flash-sale/detail.js";
  "./subPackages/activity-showcase/pages/coupon/detail.js";
  "./subPackages/activity-showcase/pages/discount/detail.js";
  "./subPackages/activity-showcase/pages/my/index.js";
  "./subPackages/activity-showcase/pages/discover/index.js";
  "./subPackages/activity-showcase/pages/message/index.js";
  "./subPackages/activity-showcase/pages/message/detail.js";
  "./subPackages/activity-showcase/pages/search/index.js";
  "./subPackages/activity-showcase/pages/distribution/index.js";
  "./subPackages/activity-showcase/pages/distribution/orders/index.js";
  "./subPackages/activity-showcase/pages/distribution/team/index.js";
  "./subPackages/activity-showcase/pages/distribution/earnings/index.js";
  "./subPackages/activity-showcase/pages/distribution/withdraw/index.js";
  "./subPackages/activity-showcase/pages/distribution/records/index.js";
  "./subPackages/activity-showcase/pages/distribution/poster/index.js";
  "./subPackages/activity-showcase/pages/distribution/products/index.js";
  "./subPackages/activity-showcase/pages/orders/index.js";
  "./subPackages/activity-showcase/pages/activity-records/index.js";
  "./subPackages/activity-showcase/pages/reminders/index.js";
  "./subPackages/activity-showcase/pages/share-records/index.js";
  "./subPackages/activity-showcase/pages/shops/index.js";
  "./subPackages/activity-showcase/pages/products/hot/index.js";
  "./subPackages/activity-showcase/pages/promotion-center/index.js";
  "./subPackages/activity-showcase/pages/shops/detail.js";
  "./subPackages/activity-showcase/pages/user-profile/index.js";
  "./subPackages/activity-showcase/pages/favorites/index.js";
  "./subPackages/activity-showcase/pages/history/index.js";
  "./subPackages/activity-showcase/pages/cart/index.js";
  "./subPackages/activity-showcase/pages/products/reviews/index.js";
  "./subPackages/activity-showcase/pages/orders/confirm/index.js";
}
const pages = [
  {
    path: "pages/index/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "磁州生活网",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white",
      enablePullDownRefresh: true,
      "app-plus": {
        titleNView: {
          backgroundColor: "#1677FF",
          buttons: [
            {
              text: "搜索",
              fontSize: "14px",
              width: "60px"
            }
          ]
        }
      }
    }
  },
  {
    path: "pages/business/business",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "同城商圈",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/business/join",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "商家入驻",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/business/success",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "入驻成功",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/business/verify",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "商家认证",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/business/verify-success",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "认证申请",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/business/shop-detail",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "商家详情",
      navigationBarBackgroundColor: "#3846cd",
      navigationBarTextStyle: "white",
      "app-plus": {
        titleNView: false,
        animationType: "slide-in-right",
        background: "#3846cd",
        statusbar: {
          background: "#3846cd"
        }
      }
    }
  },
  {
    path: "pages/business/filter",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "商家筛选",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/publish",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "发布信息",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/detail",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "发布详情",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/success",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "发布成功",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/group/group",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "进群",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/my/my",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "我的",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/my/publish",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "我的发布",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/activity/list",
    style: {
      navigationBarTitleText: "活动列表",
      enablePullDownRefresh: true
    }
  },
  {
    path: "pages/activity/detail",
    style: {
      navigationBarTitleText: "活动详情"
    }
  },
  {
    path: "pages/news/list",
    style: {
      navigationBarTitleText: "同城资讯",
      enablePullDownRefresh: true
    }
  },
  {
    path: "pages/news/detail",
    style: {
      navigationBarTitleText: "资讯详情"
    }
  },
  {
    path: "pages/pay/index",
    style: {
      navigationBarTitleText: "支付",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/pay/success",
    style: {
      navigationBarTitleText: "支付成功",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/location/select",
    style: {
      navigationBarTitleText: "选择位置",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/info-detail",
    style: {
      navigationBarTitleText: "信息详情",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/info-list",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "信息列表",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white",
      enablePullDownRefresh: true
    }
  },
  {
    path: "pages/publish/info-detail-auto-share",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "信息详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/education-detail",
    style: {
      navigationBarTitleText: "教育培训详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/home-service-detail",
    style: {
      navigationBarTitleText: "到家服务详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/find-service-detail",
    style: {
      navigationBarTitleText: "寻找服务详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/business-transfer-detail",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "商业转让详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/job-detail",
    style: {
      navigationBarTitleText: "招聘信息详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/house-rent-detail",
    style: {
      navigationBarTitleText: "房屋出租详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/car-detail",
    style: {
      navigationBarTitleText: "二手车详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/carpool-detail",
    style: {
      navigationBarTitleText: "拼车详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/second-hand-detail",
    style: {
      navigationBarTitleText: "二手物品详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/job-seeking-detail",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "求职信息详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/pet-detail",
    style: {
      navigationBarTitleText: "宠物服务详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/house-sale-detail",
    style: {
      navigationBarTitleText: "房屋出售详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/vehicle-service-detail",
    style: {
      navigationBarTitleText: "车辆服务详情",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/publish/service-category",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "选择服务类型",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/service/list",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "服务列表",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white",
      enablePullDownRefresh: true
    }
  },
  {
    path: "pages/service/home-service-list",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "到家服务",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white",
      enablePullDownRefresh: true
    }
  },
  {
    path: "pages/service/category",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "选择服务类型",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/service/filter",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "筛选服务",
      navigationBarBackgroundColor: "#0052CC",
      navigationBarTextStyle: "white",
      enablePullDownRefresh: true
    }
  },
  {
    path: "pages/error/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "错误提示",
      navigationBarBackgroundColor: "#0066FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/top-boost",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "商家置顶",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/goods",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "商品管理",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/orders",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "订单管理",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/services",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "服务管理",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/reviews",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "评价管理",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "营销中心",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/finance",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "财务管理",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/staff",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "员工管理",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/analysis",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "数据分析",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/coupon/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "优惠券管理",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/reduction/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "满减活动",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/discount/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "限时折扣",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/member/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "会员价",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/seckill/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "秒杀活动",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/groupon/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "拼团活动",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/newcomer/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "新客专享",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/fullgift/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "满赠活动",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/poster/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "店铺海报",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/share/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "分享有礼",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/livestream/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "店铺直播",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/invite/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "邀请有礼",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/verification/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "核销中心",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/verification/scan",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "扫码核销",
      navigationBarBackgroundColor: "#000000",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/verification/manual",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "手动核销",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/verification/records",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "核销记录",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/groupon/verification",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "拼团核销",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/marketing/groupon/verification-records",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "拼团核销记录",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white"
    }
  },
  {
    path: "pages/merchant/red-packet-manage",
    style: {
      navigationBarTitleText: "红包管理",
      navigationBarBackgroundColor: "#1677FF",
      navigationBarTextStyle: "white",
      enablePullDownRefresh: true
    }
  },
  {
    path: "pages/red-packet/list",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "抢红包",
      navigationBarBackgroundColor: "#FF6B6B",
      navigationBarTextStyle: "white",
      enablePullDownRefresh: true
    }
  },
  {
    path: "pages/test/shop-test",
    style: {
      navigationBarTitleText: "商家页面测试",
      navigationBarBackgroundColor: "#ffffff",
      navigationBarTextStyle: "black"
    }
  },
  {
    path: "pages/carpool-entry/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "磁州拼车",
      navigationBarBackgroundColor: "#a4e73b",
      navigationBarTextStyle: "black"
    }
  },
  {
    path: "pages/carpool/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "磁州拼车",
      navigationBarBackgroundColor: "#a4e73b",
      navigationBarTextStyle: "black"
    }
  },
  {
    path: "pages/carpool-main/index",
    style: {
      navigationStyle: "custom",
      navigationBarTitleText: "磁州拼车",
      navigationBarBackgroundColor: "#a4e73b",
      navigationBarTextStyle: "black",
      usingComponents: {}
    }
  }
];
const subPackages = [
  {
    root: "subPackages/user-center",
    pages: [
      "points",
      "points-mall",
      "points-detail",
      "exchange-history",
      "points-rank",
      "messages",
      "profile",
      "profile-edit",
      "follows",
      "fans",
      "likes",
      "favorites",
      "history",
      "call-history"
    ]
  },
  {
    root: "subPackages/merchant-center",
    pages: [
      "merchant"
    ]
  },
  {
    root: "subPackages/services",
    pages: [
      "refresh-package",
      "coupon",
      "renew-info",
      "refresh-info",
      "renew-shop",
      "renew-activity"
    ]
  },
  {
    root: "subPackages/new-franchise",
    pages: [
      "index",
      "application-form",
      "agreement"
    ]
  },
  {
    root: "subPackages/wallet",
    pages: [
      "index",
      "withdraw",
      "recharge",
      "detail",
      "bills",
      "bank"
    ]
  },
  {
    root: "subPackages/marketing",
    pages: [
      "coupon/index",
      "coupon/create",
      "coupon/edit",
      "coupon/detail",
      "discount/index",
      "discount/create",
      "discount/edit",
      "discount/detail",
      "reduction/index",
      "reduction/create",
      "reduction/edit",
      "reduction/detail",
      "member/index",
      "member/settings",
      "member/products",
      "member/rights",
      "member/analysis",
      "seckill/index",
      "seckill/create",
      "seckill/edit",
      "seckill/detail",
      "newUser/index",
      "newUser/create",
      "newUser/edit",
      "newUser/detail",
      "fullGift/index",
      "fullGift/create",
      "fullGift/edit",
      "fullGift/detail",
      "invite/index",
      "invite/create",
      "invite/edit",
      "invite/detail",
      "share/index",
      "share/create",
      "share/edit",
      "share/detail"
    ]
  },
  {
    root: "subPackages/merchant-new",
    pages: [
      "index",
      "test",
      "activity-preview",
      "marketing/index",
      "marketing/groupon/index",
      "marketing/groupon/create",
      "marketing/groupon/edit",
      "marketing/groupon/detail",
      "marketing/redpacket/index",
      "marketing/redpacket/create",
      "marketing/redpacket/edit",
      "marketing/redpacket/detail",
      "orders/index",
      "orders/detail",
      "products/index",
      "products/publish",
      "products/edit",
      "customers/index",
      "shop/decorate",
      "analytics/index",
      "analytics/visitors",
      "finance/index",
      "verify/index",
      "profile/index",
      "marketing/activity/publish",
      "marketing/seckill/index"
    ]
  },
  {
    root: "subPackages/carpool-main",
    pages: [
      "index"
    ]
  },
  {
    root: "subPackages/carpool",
    pages: [
      "index",
      "search/index",
      "list/index",
      "detail/index",
      "publish/index",
      "routes/index",
      "people-to-car/index",
      "car-to-people/index",
      "goods-to-car/index",
      "car-to-goods/index",
      "all/index",
      "activity/discount",
      "activity/newuser"
    ]
  },
  {
    root: "subPackages/carpool-entry",
    pages: [
      "index"
    ]
  },
  {
    root: "subPackages/merchant-admin-marketing",
    pages: [
      "pages/marketing/index",
      "pages/marketing/ai/index",
      "pages/marketing/group/index",
      "pages/marketing/group/create",
      "pages/marketing/member/index",
      "pages/marketing/segment/index",
      "pages/marketing/redpacket/edit",
      "pages/marketing/advanced/index",
      "pages/marketing/create-coupon",
      "pages/marketing/distribution/index",
      "pages/marketing/distribution/levels",
      "pages/marketing/distribution/agreement",
      "pages/marketing/distribution/conditions",
      "pages/marketing/distribution/withdrawal",
      "pages/marketing/distribution/distributors",
      "pages/marketing/distribution/promotion-tool",
      "pages/distribution/reports/index",
      "pages/distribution/qrcode/index",
      "pages/distribution/product-cards/index",
      "pages/distribution/channels/index"
    ]
  },
  {
    root: "subPackages/cashback",
    pages: [
      "pages/index/index",
      "pages/platform/list",
      "pages/platform/detail",
      "pages/store/index",
      "pages/stores/index",
      "pages/detail/index",
      "pages/search/index",
      "pages/orders/index",
      "pages/orders/detail",
      "pages/wallet/index",
      "pages/wallet/withdraw",
      "pages/wallet/records",
      "pages/invite/index",
      "pages/invite/records",
      "pages/guide/list",
      "pages/guide/detail",
      "pages/tutorial/index",
      "pages/price-history/index",
      "pages/agreement/user",
      "pages/agreement/privacy",
      "pages/agreement/cashback"
    ]
  },
  {
    root: "subPackages/activity-showcase",
    pages: [
      "pages/index/index",
      "pages/flash-sale/index",
      "pages/group-buy/index",
      "pages/discount/index",
      "pages/coupon/index",
      "pages/detail/index"
    ]
  }
];
const globalStyle = {
  navigationBarTextStyle: "white",
  navigationBarTitleText: "磁州生活网",
  navigationBarBackgroundColor: "#0052CC",
  backgroundColor: "#F8F8F8"
};
const tabBar = {
  color: "#7A7E83",
  selectedColor: "#0052CC",
  backgroundColor: "#ffffff",
  borderStyle: "black",
  list: [
    {
      pagePath: "pages/index/index",
      text: "首页",
      iconPath: "static/images/tabbar/home-grey.png",
      selectedIconPath: "static/images/tabbar/home-blue.png"
    },
    {
      pagePath: "pages/business/business",
      text: "商圈",
      iconPath: "static/images/tabbar/shop-grey.png",
      selectedIconPath: "static/images/tabbar/shop-blue.png"
    },
    {
      pagePath: "pages/publish/publish",
      text: "发布",
      iconPath: "static/images/tabbar/edit-grey.png",
      selectedIconPath: "static/images/tabbar/edit-blue.png"
    },
    {
      pagePath: "pages/carpool-main/index",
      text: "拼车",
      iconPath: "static/images/tabbar/拼车.png",
      selectedIconPath: "static/images/tabbar/拼车选中.png"
    },
    {
      pagePath: "pages/my/my",
      text: "我的",
      iconPath: "static/images/tabbar/user-grey.png",
      selectedIconPath: "static/images/tabbar/user-blue.png"
    }
  ]
};
const preloadRule = {
  "pages/my/my": {
    network: "all",
    packages: [
      "subPackages/user-center",
      "subPackages/new-partner",
      "subPackages/new-franchise",
      "subPackages/new-points",
      "subPackages/activity-showcase"
    ]
  },
  "pages/index/index": {
    network: "all",
    packages: [
      "subPackages/services"
    ]
  },
  "pages/business/business": {
    network: "all",
    packages: [
      "subPackages/merchant-center"
    ]
  },
  "pages/carpool-main/index": {
    network: "all",
    packages: [
      "subPackages/carpool"
    ]
  }
};
const uniIdRouter = {};
const usingComponents = {
  "fab-buttons": "./components/FabButtons"
};
const App = {
  pages,
  subPackages,
  globalStyle,
  tabBar,
  preloadRule,
  uniIdRouter,
  usingComponents
};
const cuCustom = () => "./components/cu-custom.js";
const merchantPlugin = {
  getMerchantInfo: function(id) {
    return { id, name: "测试商家" };
  }
};
const { reportError } = utils_errorHandler.setupGlobalErrorHandlers();
function createApp() {
  const app = common_vendor.createSSRApp(App);
  app.use(components_RedPacket_index.RedPacketComponents);
  app.config.globalProperties.$merchant = merchantPlugin;
  app.component("cu-custom", cuCustom);
  app.config.errorHandler = (err, instance, info) => {
    common_vendor.index.__f__("error", "at main.js:193", "[Vue错误]", err, info);
    reportError({
      error: err,
      info,
      component: instance ? instance.$options.name || "anonymous" : null,
      timestamp: Date.now()
    });
  };
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
