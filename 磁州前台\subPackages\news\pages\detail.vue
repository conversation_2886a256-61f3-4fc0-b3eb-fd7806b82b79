<template>
	<view class="detail-container">
		<!-- 顶部区域 -->
		<view class="detail-header card-style">
			<view class="header-top">
				<view class="category-badge">{{newsInfo.category}}</view>
			<text class="detail-title">{{newsInfo.title}}</text>
			</view>
			<view class="detail-meta">
				<view class="meta-item source-item">
					<image src="/static/images/tabbar/作者.png" class="meta-icon"></image>
					<text>{{newsInfo.source}}</text>
				</view>
				<view class="meta-item time-item">
					<image src="/static/images/tabbar/时间.png" class="meta-icon"></image>
					<text>{{newsInfo.time}}</text>
				</view>
				<view class="meta-item view-count">
					<image src="/static/images/tabbar/浏览.png" class="meta-icon"></image>
					<text>{{newsInfo.views}}浏览</text>
				</view>
			</view>
		</view>
		
		<!-- 添加真正的悬浮导航键到页面根元素 -->
		<view class="info-nav-btn" @click="navigateTo('/pages/index/index')">
			<view class="info-hot-tag">火热</view>
			<view class="info-btn-content">
				<view class="info-icon-group">
					<view class="info-icon-item"></view>
					<view class="info-icon-item"></view>
					<view class="info-icon-item"></view>
					<view class="info-icon-item"></view>
				</view>
				<view class="info-nav-text">便民</view>
				<view class="info-nav-text">信息</view>
			</view>
		</view>
		
		<!-- 添加悬浮推广按钮 -->
		<FloatPromotionButton 
			@click="showPromotionTool" 
			:position="{right: '30rpx', bottom: '280rpx'}"
			size="90rpx"
		/>
		
		<!-- 内容区域 -->
		<view class="detail-content card-style">
			<!-- 主图区域 -->
			<view class="content-image-container" v-if="newsInfo.image">
				<image :src="newsInfo.image" class="content-image" mode="widthFix"></image>
				<view class="image-caption" v-if="newsInfo.imageCaption">{{newsInfo.imageCaption}}</view>
			</view>
			
			<!-- 正文区域 -->
			<view class="text-container">
			<text class="content-text">{{newsInfo.content}}</text>
		</view>
		
			<!-- 来源标记 -->
			<view class="content-footer">
				<text class="content-source">作者：{{newsInfo.source}}</text>
			</view>
		</view>
		
		<!-- 互动区域 -->
		<view class="detail-stats card-style">
			<view class="stat-item" @click="toggleLike">
				<image :src="newsInfo.isLiked ? '/static/images/tabbar/已点赞选中.png' : '/static/images/tabbar/点赞.png'" class="stat-icon"></image>
				<text class="stat-text" :class="{ 'active': newsInfo.isLiked }">{{newsInfo.likes}}</text>
			</view>
			<view class="stat-item" @click="scrollToComment">
				<image src="/static/images/tabbar/评论.png" class="stat-icon"></image>
				<text class="stat-text">{{newsInfo.comments}}</text>
			</view>
			<view class="stat-item" @click="toggleCollect">
				<image :src="newsInfo.isCollected ? '/static/images/tabbar/已收藏选中.png' : '/static/images/tabbar/收藏.png'" class="stat-icon"></image>
				<text class="stat-text" :class="{ 'active': newsInfo.isCollected }">收藏</text>
			</view>
			<button class="share-btn stat-item" open-type="share" @click="beforeShare">
				<image src="/static/images/tabbar/分享.png" class="stat-icon"></image>
				<text class="stat-text">分享</text>
			</button>
		</view>
		
		<!-- 评论区 -->
		<view class="comment-section card-style" id="comment-section" v-if="newsInfo.commentList && newsInfo.commentList.length > 0">
			<view class="section-title">
				<text class="title-text">热门评论</text>
				<text class="comment-count">({{newsInfo.comments}})</text>
			</view>
			
			<!-- 评论列表 -->
			<view class="comment-list">
				<view class="comment-item" v-for="(comment, index) in newsInfo.commentList" :key="index">
					<image :src="comment.avatar" class="comment-avatar" mode="aspectFill"></image>
					<view class="comment-content">
						<!-- 评论头部 -->
						<view class="comment-header">
						<text class="comment-name">{{comment.name}}</text>
							<!-- 徽章 -->
							<view class="user-badge" v-if="comment.isOfficial">官方</view>
						</view>
						
						<!-- 评论正文 -->
						<text class="comment-text">{{comment.content}}</text>
						
						<!-- 评论底部 -->
						<view class="comment-footer">
							<text class="comment-time">{{comment.time}}</text>
							<view class="comment-actions">
								<view class="action-item reply-btn" @click="showReplyInput(index, comment.name)">
									<image src="/static/images/tabbar/回复.png" class="action-icon"></image>
									<text>回复</text>
								</view>
								<view class="action-item like-btn" @click="toggleCommentLike(index)">
									<image :src="comment.isLiked ? '/static/images/tabbar/已点赞选中.png' : '/static/images/tabbar/点赞.png'" class="action-icon"></image>
								<text :class="{ 'active': comment.isLiked }">{{comment.likes}}</text>
							</view>
						</view>
					</view>
						
						<!-- 回复列表 -->
						<view class="reply-list" v-if="comment.replies && comment.replies.length > 0">
							<view class="reply-item" v-for="(reply, replyIndex) in comment.showAllReplies ? comment.replies : comment.replies.slice(0, 2)" :key="replyIndex" @click="showReplyInput(index, reply.name)">
								<view class="reply-content">
									<text class="reply-name">{{reply.name}}</text>
									<view class="reply-badge" v-if="reply.isOfficial">官方</view>
									<text v-if="reply.replyTo" class="reply-to">回复</text>
									<text v-if="reply.replyTo" class="reply-name">@{{reply.replyTo}}</text>
									<text class="reply-text">：{{reply.content}}</text>
				</view>
								<view class="reply-footer">
									<text class="reply-time">{{reply.time}}</text>
			</view>
		</view>
		
							<!-- 查看更多回复 -->
							<view class="view-more-replies" v-if="comment.replies.length > 2 && !comment.showAllReplies" @click.stop="toggleReplies(index)">
								查看更多{{comment.replies.length - 2}}条回复 <text class="more-icon">∨</text>
		</view>
							<view class="view-more-replies" v-if="comment.replies.length > 2 && comment.showAllReplies" @click.stop="toggleReplies(index)">
								收起回复 <text class="more-icon">∧</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 查看更多评论 -->
			<view class="view-more-comments" @click="loadMoreComments" v-if="hasMoreComments">
				<text>查看更多评论</text>
				<image src="/static/images/tabbar/更多.png" class="more-comments-icon"></image>
			</view>
		</view>
		
		<!-- 无评论提示 -->
		<view class="no-comment card-style" v-else>
			<image src="/static/images/tabbar/暂无评论.png" class="no-comment-icon"></image>
			<text class="no-comment-text">暂无评论，快来发表你的看法吧</text>
		</view>
		
		<!-- 评论输入框 -->
		<view class="comment-input-container card-style">
			<view class="comment-input-wrapper">
				<input 
					type="text" 
					class="comment-input" 
					:placeholder="replyPlaceholder" 
					v-model="commentText" 
					:focus="inputFocus"
					confirm-type="send"
					@confirm="submitComment"
					@focus="onInputFocus"
					@blur="onInputBlur"
				/>
				<view class="send-btn" :class="{ 'active': commentText.trim().length > 0 }" @click="submitComment">发送</view>
			</view>
		</view>
		
		<!-- 磁县同城信息模块 -->
		<view class="related-info-section">
			<view class="related-info-title">
				<view class="premium-bar"></view>
				<text class="related-title-text">同城信息推荐</text>
			</view>
			<InfoList 
				:allInfoList="filteredInfoList" 
				:toppedInfoList="filteredToppedInfoList" 
				:adBanner="adBanner" 
				@tab-change="handleInfoTabChange" />
		</view>
		
		<!-- 底部导航栏 -->
		<view class="bottom-tabbar">
			<view class="tabbar-item" @click="navigateTo('/pages/index/index')">
				<image class="tabbar-icon" src="/static/images/tabbar/home-grey.png" mode="aspectFit"></image>
				<text class="tabbar-text">首页</text>
			</view>
			<view class="tabbar-item" @click="navigateTo('/pages/business/business')">
				<image class="tabbar-icon" src="/static/images/tabbar/shop-grey.png" mode="aspectFit"></image>
				<text class="tabbar-text">商圈</text>
			</view>
			<view class="tabbar-item" @click="navigateTo('/pages/publish/publish')">
				<image class="tabbar-icon" src="/static/images/tabbar/edit-grey.png" mode="aspectFit"></image>
				<text class="tabbar-text">发布</text>
			</view>
			<view class="tabbar-item" @click="navigateTo('/pages/group/group')">
				<image class="tabbar-icon" src="/static/images/tabbar/chat-grey.png" mode="aspectFit"></image>
				<text class="tabbar-text">进群</text>
			</view>
			<view class="tabbar-item" @click="navigateTo('/pages/my/my')">
				<image class="tabbar-icon" src="/static/images/tabbar/user-grey.png" mode="aspectFit"></image>
				<text class="tabbar-text">我的</text>
			</view>
		</view>
		
		<!-- 回到顶部按钮 -->
		<view class="back-to-top" v-if="showBackToTop" @click="scrollToTop">
			<image src="/static/images/tabbar/arrow-up.png" class="top-icon"></image>
		</view>
	</view>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import InfoList from '@/components/index/InfoList.vue';
import FloatPromotionButton from '@/components/FloatPromotionButton.vue';
import mockApi from '@/mock/api';
import { infoCategories, visibleCategories } from '@/mock/info/categories';
import { infoListData } from '@/static/data/infoListData.js';

	export default {
	components: {
		InfoList,
		FloatPromotionButton
	},
  setup() {
    // 响应式状态
    const commentText = ref('');
    const currentCommentIndex = ref(-1);
    const replyToUser = ref('');
    const inputBottom = ref(0);
    const inputFocus = ref(false);
    const showBackToTop = ref(false);
    const hasMoreComments = ref(true);
    const statusBarHeight = ref(20);
    const newsId = ref(0);
			
			// 同城信息相关
    const currentInfoTab = ref(0);
    const isTabsFixed = ref(false);
    const adBanner = ref({
				image: '/static/images/ad-banner.jpg',
				url: '/pages/activity/detail?id=3'
    });
    
    // 新闻详情数据
    const newsInfo = reactive({
					id: 1,
      title: '',
      time: '',
      source: '',
      category: '',
      image: '',
      imageCaption: '',
					isCollected: false,
      content: '',
      views: 0,
      likes: 0,
					isLiked: false,
      comments: 0,
      commentList: []
    });
    
    // 置顶和普通信息列表
    const toppedInfoList = ref([]);
    const allInfoList = ref([]);
    
    // 其他状态
    const _tabsPlaceholderTop = ref(null);
    const _currentScrollTop = ref(0);
    
    // 计算属性
    const replyPlaceholder = computed(() => {
      return replyToUser.value ? `回复 @${replyToUser.value}` : '写下你的评论...';
    });
    
		// 根据当前标签过滤信息列表
    const filteredInfoList = computed(() => {
			// 如果是第一个标签（最新发布），返回所有信息
      if (currentInfoTab.value === 0) {
        return allInfoList.value;
			}
			
			// 否则，根据选中的分类标签过滤信息
      const selectedCategory = infoCategories[currentInfoTab.value - 1];
			
			// 返回匹配分类的信息
      return allInfoList.value.filter(item => item.category === selectedCategory);
    });
    
		// 根据当前标签过滤置顶信息列表
    const filteredToppedInfoList = computed(() => {
			// 如果是第一个标签（最新发布），返回所有置顶信息
      if (currentInfoTab.value === 0) {
        return toppedInfoList.value;
			}
			
			// 否则，根据选中的分类标签过滤置顶信息
      const selectedCategory = infoCategories[currentInfoTab.value - 1];
			
			// 返回匹配分类的置顶信息
      return toppedInfoList.value.filter(item => item.category === selectedCategory);
    });
    
    // 生命周期钩子 - 页面加载
    function onPageLoad(options) {
			// 记录资讯ID
			if (options.id) {
        newsId.value = options.id;
        console.log('资讯ID:', newsId.value);
			}
			
			// 获取状态栏高度
			uni.getSystemInfo({
				success: (res) => {
          statusBarHeight.value = res.statusBarHeight;
				}
			});
			
			// 初始化页面数据
      fetchNewsDetail(newsId.value);
      fetchInfoData();
			
			// 获取标签位置
			setTimeout(() => {
        getTabsPosition();
			}, 500);
    }
    
    // 获取新闻详情数据
    function fetchNewsDetail(id) {
				uni.showLoading({
					title: '加载中'
				});
				
      // 使用模拟API获取新闻详情
      mockApi.news.getDetail(id).then(data => {
        // 将获取的数据复制到响应式对象中
        Object.assign(newsInfo, data);
					uni.hideLoading();
        
        // 增加浏览量记录
        recordView(id);
      }).catch(err => {
        uni.hideLoading();
								uni.showToast({
          title: '获取资讯失败',
									icon: 'none'
								});
        console.error('获取资讯失败:', err);
      });
    }
    
    // 获取信息数据
    function fetchInfoData() {
      // 从静态数据文件获取数据，与首页保持一致
      
      // 获取置顶信息
      toppedInfoList.value = infoListData.toppedInfoList.map(item => {
        // 确保每个项目都有必要的字段
        return {
          ...item,
          images: item.images || [],
          tags: item.tags || [],
          views: item.views || 0,
          likes: item.likes || 0,
          comments: item.comments || 0
        };
      });
      
      // 获取普通信息
      allInfoList.value = infoListData.allInfoList.map(item => {
        // 确保每个项目都有必要的字段
        return {
          ...item,
          images: item.images || [],
          tags: item.tags || [],
          views: item.views || 0,
          likes: item.likes || 0,
          comments: item.comments || 0
        };
      });
      
      // 获取广告横幅
      adBanner.value = {
        image: '/static/images/banner/ad-banner.jpg',
        url: '/pages/ad/detail',
        title: '广告横幅'
      };
    }
			
			// 记录浏览量
    function recordView(id) {
				console.log('记录浏览量，ID:', id);
      // 实际应用中，这里应该调用后端API记录浏览量
    }
			
			// 点赞功能
    function toggleLike() {
				// 添加触感反馈
				uni.vibrateShort({
					success: () => {
						console.log('振动成功');
					}
				});
				
      newsInfo.isLiked = !newsInfo.isLiked;
      newsInfo.likes += newsInfo.isLiked ? 1 : -1;
				
				// 显示交互反馈
				uni.showToast({
        title: newsInfo.isLiked ? '已点赞' : '已取消点赞',
					icon: 'none',
					duration: 1500
				});
    }
			
			// 收藏功能
    function toggleCollect() {
				// 添加触感反馈
				uni.vibrateShort({
					success: () => {
						console.log('振动成功');
					}
				});
				
      newsInfo.isCollected = !newsInfo.isCollected;
				
				// 显示交互反馈
				uni.showToast({
        title: newsInfo.isCollected ? '已收藏' : '已取消收藏',
					icon: 'none',
					duration: 1500
				});
    }
			
			// 分享功能
    function beforeShare() {
				// 添加触感反馈
				uni.vibrateShort();
				console.log('准备分享...');
				// 可以在这里设置分享内容，但实际分享参数在onShareAppMessage中定义
    }
			
			// 显示推广工具
			function showPromotionTool() {
				uni.navigateTo({
					url: `/subPackages/promotion/pages/promotion-tool?type=content&id=${newsId.value}`
				});
    }
			
			// 滚动到评论区
    function scrollToComment() {
				uni.createSelectorQuery()
					.select('#comment-section')
					.boundingClientRect(data => {
						if (data) {
							uni.pageScrollTo({
								scrollTop: data.top,
								duration: 300
							});
						} else {
							// 如果没有评论，滚动到底部
							uni.pageScrollTo({
								scrollTop: 9999,
								duration: 300
							});
						}
					})
					.exec();
    }
			
			// 切换评论点赞状态
    function toggleCommentLike(index) {
				// 添加触感反馈
				uni.vibrateShort({
					success: () => {
						console.log('振动成功');
					}
				});
				
      const comment = newsInfo.commentList[index];
				comment.isLiked = !comment.isLiked;
				comment.likes += comment.isLiked ? 1 : -1;
    }
			
			// 显示回复输入框
    function showReplyInput(commentIndex, userName) {
      currentCommentIndex.value = commentIndex;
      replyToUser.value = userName;
      inputFocus.value = true;
				
				// 滚动到评论输入框
				setTimeout(() => {
					uni.createSelectorQuery()
						.select('.comment-input-container')
          .boundingClientRect(data => {
							if (data) {
								uni.pageScrollTo({
									scrollTop: data.top,
									duration: 300
								});
							}
						})
						.exec();
				}, 200);
    }
			
			// 重置回复状态
    function resetReplyState() {
      currentCommentIndex.value = -1;
      replyToUser.value = '';
      commentText.value = '';
      inputFocus.value = false;
    }
			
			// 提交评论或回复
    function submitComment() {
      if (!commentText.value.trim()) {
					uni.showToast({
						title: '请输入评论内容',
						icon: 'none'
					});
					return;
				}
				
				// 添加触感反馈
				uni.vibrateShort({
					success: () => {
						console.log('振动成功');
					}
				});
				
				// 如果是回复评论
      if (currentCommentIndex.value >= 0 && replyToUser.value) {
        const comment = newsInfo.commentList[currentCommentIndex.value];
					
					// 如果没有replies数组，创建一个
					if (!comment.replies) {
						comment.replies = [];
					}
					
					// 添加回复
					comment.replies.unshift({
						name: '游客',
          content: commentText.value,
						time: '刚刚',
          replyTo: replyToUser.value,
						isOfficial: false
					});
					
					// 增加评论总数
        newsInfo.comments++;
					
					// 自动展开回复
					comment.showAllReplies = true;
					
					uni.showToast({
						title: '回复成功',
						icon: 'success'
					});
					
					// 滚动到该评论
					setTimeout(() => {
          const commentSelector = `.comment-item:nth-child(${currentCommentIndex.value + 1})`;
						uni.createSelectorQuery()
							.select(commentSelector)
            .boundingClientRect(data => {
								if (data) {
									uni.pageScrollTo({
										scrollTop: data.top,
										duration: 300
									});
								}
							})
							.exec();
					}, 300);
				} else {
					// 添加新评论
        newsInfo.commentList.unshift({
					avatar: '/static/images/tabbar/user-blue.png',
					name: '游客',
          content: commentText.value,
					time: '刚刚',
					likes: 0,
						isLiked: false,
						isOfficial: false,
						showAllReplies: false,
						replies: []
					});
					
					// 增加评论总数
        newsInfo.comments++;
				
				uni.showToast({
					title: '评论成功',
					icon: 'success'
					});
					
					// 滚动到评论区顶部
					setTimeout(() => {
          scrollToComment();
					}, 300);
				}
				
				// 重置状态
      resetReplyState();
    }
			
			// 切换回复列表展开/收起
    function toggleReplies(index) {
				// 添加触感反馈
				uni.vibrateShort({
					success: () => {
						console.log('振动成功');
					}
				});
				
      newsInfo.commentList[index].showAllReplies = !newsInfo.commentList[index].showAllReplies;
    }
			
			// 加载更多评论
    function loadMoreComments() {
				uni.showLoading({
					title: '加载中'
				});
				
      // 使用模拟API获取更多评论
      mockApi.news.getMoreComments().then(comments => {
        // 添加到评论列表
        newsInfo.commentList.push(...comments);
					
					// 测试数据，模拟没有更多评论了
        hasMoreComments.value = false;
					
        uni.hideLoading();
					uni.showToast({
						title: '已加载全部评论',
						icon: 'none'
					});
      }).catch(err => {
        uni.hideLoading();
        console.error('加载更多评论失败:', err);
      });
    }
			
			// 输入框获取焦点
    function onInputFocus(e) {
      inputBottom.value = e.detail.height || 0;
    }
			
			// 输入框失去焦点
    function onInputBlur() {
      inputBottom.value = 0;
    }
			
			// 页面导航
    function navigateTo(url) {
				uni.switchTab({
					url: url,
					fail: (err) => {
						console.error('导航失败:', err);
						// 如果不是tabBar页面，使用普通导航
						uni.navigateTo({
							url: url
						});
					}
				});
    }
		
    // 导航到信息详情页
    function navigateToInfoDetail(item) {
			uni.navigateTo({
				url: `/pages/publish/${item.pageType || 'info-detail'}?id=${item.id}`
			});
    }
		
		// 处理信息标签切换
    function handleInfoTabChange(tab) {
			console.log('切换到标签:', tab);
      currentInfoTab.value = tab.index;
    }
    
    // 获取标签位置
    function getTabsPosition() {
      const query = uni.createSelectorQuery();
      query.select('#tabsContainer').boundingClientRect(data => {
        if (data) {
          const tabsPosition = data;
          const tabsHeight = data.height || 0;
        }
      }).exec();
    }
    
    // 获取标签占位符位置
    function getTabsPlaceholderPosition() {
      nextTick(() => {
        const query = uni.createSelectorQuery();
        query.select('#tabsPlaceholder').boundingClientRect(data => {
          if (data) {
            _tabsPlaceholderTop.value = (_currentScrollTop.value || 0) + data.top;
            console.log('标签占位符位置:', _tabsPlaceholderTop.value);
          }
        }).exec();
      });
    }
    
    // 回到顶部
    function scrollToTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }
    
    // 页面滚动事件处理
    function onPageScroll(e) {
      // 检查是否显示回到顶部按钮
      showBackToTop.value = e.scrollTop > 500;
    }
    
    // 页面下拉刷新
    function onPullDownRefresh() {
      console.log('下拉刷新');
      // 刷新当前资讯
      fetchNewsDetail(newsId.value);
      // 停止下拉刷新动画
      setTimeout(() => {
        uni.stopPullDownRefresh();
      }, 1000);
    }
    
    // 页面触底事件
    function onReachBottom() {
      console.log('页面触底');
      if (hasMoreComments.value) {
        loadMoreComments();
      }
    }
    
    // 分享到微信好友
    function onShareAppMessage() {
      return {
        title: newsInfo.title,
        path: `/pages/subPackages/news/detail/index?id=${newsId.value}`
      };
    }
    
    // 分享到朋友圈
    function onShareTimeline() {
      return {
        title: newsInfo.title,
        query: `id=${newsInfo.id}`,
        path: `/pages/subPackages/news/detail/index?id=${newsId.value}`,
        imageUrl: newsInfo.image
      };
    }
    
    return {
      // 数据
      commentText,
      currentCommentIndex,
      replyToUser,
      inputBottom,
      inputFocus,
      showBackToTop,
      hasMoreComments,
      statusBarHeight,
      newsId,
      currentInfoTab,
      isTabsFixed,
      visibleCategories,
      infoCategories,
      adBanner,
      newsInfo,
      toppedInfoList,
      allInfoList,
      _tabsPlaceholderTop,
      _currentScrollTop,
      
      // 计算属性
      replyPlaceholder,
      filteredInfoList,
      filteredToppedInfoList,
      
      // 方法
      onPageLoad,
      fetchNewsDetail,
      recordView,
      toggleLike,
      toggleCollect,
      beforeShare,
      scrollToComment,
      toggleCommentLike,
      showReplyInput,
      resetReplyState,
      submitComment,
      toggleReplies,
      loadMoreComments,
      onInputFocus,
      onInputBlur,
      navigateTo,
      navigateToInfoDetail,
      handleInfoTabChange,
      getTabsPosition,
      getTabsPlaceholderPosition,
      scrollToTop,
      onPageScroll,
      onPullDownRefresh,
      onReachBottom,
      onShareAppMessage,
      onShareTimeline,
      showPromotionTool
    };
  },
  // uni-app 页面生命周期钩子
  onLoad(options) {
    this.onPageLoad(options);
  },
  onReady() {
    console.log('页面渲染完成');
  },
  onShow() {
    console.log('页面显示');
  },
  onHide() {
    console.log('页面隐藏');
  },
  onUnload() {
    console.log('页面卸载');
  },
  onPullDownRefresh() {
    this.onPullDownRefresh();
  },
  onReachBottom() {
    this.onReachBottom();
  },
  onPageScroll(e) {
    this.onPageScroll(e);
  },
  onShareAppMessage() {
    return this.onShareAppMessage();
  },
  onShareTimeline() {
    return this.onShareTimeline();
		}
	}
</script>

<style>
	.detail-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx; /* 为底部导航栏留出空间 */
		font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
	}
	
	/* 卡片通用样式 */
	.card-style {
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		margin: 20rpx 24rpx;
		overflow: hidden;
		position: relative;
	}
	
	/* 相关信息区域样式 */
	.related-info-section {
		margin: 20rpx 0 30rpx 0;
		background: #fff;
	}
	
	.related-info-title {
		display: flex;
		align-items: center;
		padding: 24rpx;
		margin: 0 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.premium-bar {
		width: 6rpx;
		height: 36rpx;
		background: linear-gradient(180deg, #0052CC, #1677FF);
		border-radius: 3rpx;
		margin-right: 16rpx;
	}
	
	.related-title-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		letter-spacing: 0.5rpx;
	}
	
	/* 顶部区域样式 */
	.detail-header {
		padding: 30rpx;
		position: relative;
		overflow: visible;
	}
	
	.header-top {
		margin-bottom: 20rpx;
	}
	
	.category-badge {
		display: inline-block;
		font-size: 22rpx;
		font-weight: 500;
		color: #0052CC;
		background: rgba(0, 82, 204, 0.1);
		padding: 6rpx 18rpx;
		border-radius: 20rpx;
		margin-bottom: 16rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 82, 204, 0.2);
	}
	
	.detail-title {
		font-size: 42rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 26rpx;
		line-height: 1.4;
		letter-spacing: 0.5rpx;
	}
	
	.detail-meta {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		color: #888;
		border-top: 1rpx solid rgba(0, 0, 0, 0.05);
		padding-top: 20rpx;
		position: relative;
	}
	
	.meta-item {
		display: flex;
		align-items: center;
		margin-right: 24rpx;
		font-size: 24rpx;
	}
	
	.meta-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
		opacity: 0.8;
	}
	
	/* 导航按钮样式 - 便民信息 */
	.home-nav-btn {
		display: none; /* 隐藏旧的导航按钮 */
	}
	
	/* 真正的悬浮导航键样式 */
	.info-nav-btn {
		position: fixed;
		right: 30rpx;
		top: 150rpx;
		background: #ffffff;
		border-radius: 45rpx;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
		padding: 0;
		width: 80rpx;
		height: 160rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		overflow: visible;
		z-index: 100;
		transition: all 0.3s;
	}
	
	.info-nav-btn:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
	}
	
	.info-hot-tag {
		background: #ff4d4f;
		color: #fff;
		font-size: 16rpx;
		padding: 4rpx 12rpx;
		position: absolute;
		top: -15rpx;
		right: -20rpx;
		border-radius: 10rpx;
		font-weight: bold;
		transform: rotate(15deg);
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
		z-index: 999;
	}
	
	.info-btn-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 22rpx;
		width: 100%;
		height: 100%;
	}
	
	.info-icon-group {
		width: 44rpx;
		height: 44rpx;
		display: grid;
		grid-template-columns: 1fr 1fr;
		grid-template-rows: 1fr 1fr;
		gap: 4rpx;
		margin-bottom: 8rpx;
	}
	
	.info-icon-item {
		width: 100%;
		height: 100%;
		background-color: #0052CC;
		border-radius: 2rpx;
	}
	
	.info-icon-item:first-child {
		background-color: #ff4d4f;
	}
	
	.info-nav-text {
		font-size: 20rpx;
		color: #333;
		font-weight: 700;
		line-height: 1.2;
		margin-bottom: 4rpx;
	}
	
	/* 内容区域样式 */
	.detail-content {
		padding: 30rpx;
	}
	
	.content-image-container {
		position: relative;
		margin: 0 -30rpx 30rpx;
		overflow: hidden;
	}
	
	.content-image {
		width: 100%;
	}
	
	.image-caption {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
		color: #fff;
		font-size: 24rpx;
		padding: 24rpx 20rpx 16rpx;
		text-align: center;
	}
	
	.text-container {
		margin-bottom: 30rpx;
	}
	
	.content-text {
		font-size: 32rpx;
		color: #333;
		line-height: 1.8;
		white-space: pre-wrap;
		letter-spacing: 0.5rpx;
	}
	
	.content-footer {
		display: flex;
		justify-content: space-between;
		padding-top: 30rpx;
		border-top: 1rpx dashed rgba(0, 0, 0, 0.1);
		font-size: 24rpx;
		color: #999;
	}
	
	.content-source {
		font-style: italic;
	}
	
	.report-text {
		color: #666;
		background: rgba(0, 0, 0, 0.05);
		padding: 6rpx 16rpx;
		border-radius: 20rpx;
	}
	
	/* 互动区域样式 */
	.detail-stats {
		display: flex;
		align-items: center;
		justify-content: space-around;
		padding: 20rpx 30rpx;
	}
	
	.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 14rpx 20rpx;
		flex: 1;
		position: relative;
		transition: transform 0.2s;
	}
	
	.stat-item:active {
		transform: scale(0.95);
	}
	
	.stat-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 8rpx;
	}
	
	.stat-text {
		font-size: 24rpx;
		color: #666;
		font-weight: 500;
	}
	
	.stat-text.active {
		color: #0052CC;
		font-weight: 600;
	}
	
	.stat-text.active {
		color: #0052CC;
		font-weight: 600;
	}
	
	.share-btn {
		background: transparent;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0;
		line-height: 1;
		border: none;
		font-size: 24rpx;
		color: #666;
		font-weight: 500;
	}
	
	.share-btn::after {
		display: none;
		border: none;
		background: none;
	}
	
	/* 评论区样式 */
	.comment-section {
		padding: 30rpx;
	}
	
	.section-title {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		position: relative;
		padding-left: 20rpx;
	}
	
	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.comment-count {
		font-size: 26rpx;
		color: #999;
		margin-left: 10rpx;
		position: relative;
		top: 2rpx;
	}
	
	.comment-list {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
	}
	
	.comment-item {
		display: flex;
		padding-bottom: 24rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	}
	
	.comment-item:last-child {
		padding-bottom: 0;
		border-bottom: none;
	}
	
	.comment-avatar {
		width: 72rpx;
		height: 72rpx;
		border-radius: 50%;
		margin-right: 20rpx;
		background-color: #f5f5f5;
		border: 1rpx solid #f0f0f0;
		flex-shrink: 0;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}
	
	.comment-content {
		flex: 1;
		overflow: hidden;
	}
	
	.comment-header {
		display: flex;
		align-items: center;
	}
	
	.comment-name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-right: 12rpx;
	}
	
	.user-badge {
		background: linear-gradient(to right, #0052CC, #2196F3);
		color: #fff;
		font-size: 20rpx;
		padding: 2rpx 10rpx;
		border-radius: 10rpx;
		font-weight: normal;
		box-shadow: 0 2rpx 6rpx rgba(0, 82, 204, 0.2);
	}
	
	.comment-text {
		font-size: 30rpx;
		color: #333;
		line-height: 1.6;
		margin: 12rpx 0;
		word-break: break-all;
	}
	
	.comment-footer {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 8rpx 0;
	}
	
	.comment-time {
		font-size: 24rpx;
		color: #999;
	}
	
	.comment-actions {
		display: flex;
		align-items: center;
	}
	
	.action-item {
		display: flex;
		align-items: center;
		margin-left: 30rpx;
		padding: 4rpx 8rpx;
	}
	
	.action-icon {
		width: 30rpx;
		height: 30rpx;
		margin-right: 8rpx;
	}
	
	.reply-btn text, .like-btn text {
		font-size: 24rpx;
		color: #666;
	}
	
	.like-btn text.active {
		color: #0052CC;
	}
	
	/* 回复区域 */
	.reply-list {
		margin: 20rpx 0 10rpx 0;
		padding: 20rpx;
		background-color: #f8f9fc;
		border-radius: 12rpx;
		border: 1rpx solid rgba(0, 0, 0, 0.03);
	}
	
	.reply-item {
		margin-bottom: 16rpx;
		padding-bottom: 16rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	}
	
	.reply-item:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
	}
	
	.reply-content {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}
	
	.reply-name {
		font-size: 26rpx;
		font-weight: bold;
		color: #0052CC;
	}
	
	.reply-badge {
		background: linear-gradient(to right, #0052CC, #2196F3);
		color: #fff;
		font-size: 18rpx;
		padding: 0 8rpx;
		border-radius: 8rpx;
		margin: 0 6rpx;
		line-height: 1.5;
	}
	
	.reply-to {
		font-size: 26rpx;
		color: #999;
		margin: 0 6rpx;
	}
	
	.reply-text {
		font-size: 26rpx;
		color: #333;
		line-height: 1.5;
		word-break: break-all;
	}
	
	.reply-footer {
		display: flex;
		justify-content: space-between;
		margin-top: 8rpx;
	}
	
	.reply-time {
		font-size: 22rpx;
		color: #999;
	}
	
	.view-more-replies {
		font-size: 24rpx;
		color: #0052CC;
		text-align: center;
		padding: 12rpx 0;
		margin-top: 10rpx;
		background: rgba(0, 82, 204, 0.05);
		border-radius: 8rpx;
	}
	
	.more-icon {
		display: inline-block;
		transform: scale(0.7);
	}
	
	.view-more-comments {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 0;
		margin-top: 10rpx;
		font-size: 28rpx;
		color: #0052CC;
		background: rgba(0, 82, 204, 0.05);
		border-radius: 8rpx;
	}
	
	.more-comments-icon {
		width: 24rpx;
		height: 24rpx;
		margin-left: 8rpx;
	}
	
	/* 无评论提示 */
	.no-comment {
		padding: 60rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	
	.no-comment-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
		opacity: 0.6;
	}
	
	.no-comment-text {
		font-size: 28rpx;
		color: #999;
	}
	
	/* 评论输入框 */
	.comment-input-container {
		padding: 20rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom) / 2);
		margin-bottom: 100rpx; /* 为底部导航栏留出空间 */
	}
	
	.comment-input-wrapper {
		display: flex;
		align-items: center;
		background-color: #f5f7fa;
		border-radius: 36rpx;
		padding: 6rpx 10rpx 6rpx 30rpx;
		box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
	}
	
	.comment-input {
		flex: 1;
		height: 68rpx;
		font-size: 28rpx;
		color: #333;
		background-color: transparent;
	}
	
	.send-btn {
		height: 60rpx;
		line-height: 60rpx;
		background-color: #eaecf0;
		color: #999;
		font-size: 28rpx;
		font-weight: 500;
		text-align: center;
		border-radius: 30rpx;
		padding: 0 24rpx;
		margin-left: 10rpx;
		transition: all 0.2s;
	}
	
	.send-btn.active {
		background: linear-gradient(to right, #0052CC, #2196F3);
		color: #fff;
		box-shadow: 0 2rpx 8rpx rgba(0, 82, 204, 0.3);
	}
	
	/* 底部导航栏样式 */
	.bottom-tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(env(safe-area-inset-bottom) / 2);
		background-color: #ffffff;
		display: flex;
		justify-content: space-around;
		align-items: center;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.08);
		z-index: 100;
		border-top: 1rpx solid rgba(0, 0, 0, 0.05);
		transform: translateY(-5rpx); /* 向上移动一点距离 */
	}
	
	.tabbar-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex: 1;
		height: 100%;
		padding: 10rpx 0;
	}
	
	.tabbar-icon {
		width: 44rpx;
		height: 44rpx;
		margin-bottom: 6rpx;
	}
	
	.tabbar-text {
		font-size: 22rpx;
		color: #7A7E83;
		line-height: 1;
	}
	
	/* 回到顶部按钮 */
	.back-to-top {
		position: fixed;
		right: 30rpx;
		bottom: 240rpx; /* 增加距离底部的高度 */
		width: 50rpx;
		height: 50rpx;
		background: rgba(255, 255, 255, 0.95);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
		z-index: 90;
		opacity: 0.9;
		transition: all 0.3s;
	}
	
	.back-to-top:active {
		transform: scale(0.9);
	}
	
	.top-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	/* 全部信息模块样式 */
	.all-info {
		margin-bottom: 30rpx;
		padding: 0 0 22rpx 0;
		margin-left: 25rpx;
		margin-right: 25rpx;
		overflow: hidden;
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.05);
	}
	
	.all-info-title-row {
		display: flex;
		align-items: center;
		margin: 26rpx 26rpx 20rpx 26rpx;
		padding-top: 10rpx;
		border-bottom: 0.5px solid rgba(230, 233, 240, 0.8);
		padding-bottom: 20rpx;
	}
	
	.all-info-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 12rpx;
		vertical-align: middle;
	}
	
	.all-info-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		display: inline-block;
		margin: 0;
		letter-spacing: 0.5px;
		text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
	}
	
	.all-info-tabs-container {
		position: relative;
		width: 100%;
		z-index: 10;
		padding-top: 5rpx;
		padding-bottom: 5rpx;
	}
	
	/* 分类标签 */
	.all-info-tabs {
		margin: 0 15rpx 22rpx 15rpx;
		padding: 0;
		white-space: nowrap;
		display: flex;
		flex-wrap: nowrap;
		overflow-x: auto;
		-webkit-overflow-scrolling: touch;
		scrollbar-width: none;
		width: auto;
		box-sizing: border-box;
		padding-bottom: 15rpx;
		padding-top: 8rpx;
		background-color: #ffffff;
		z-index: 100;
		transition: all 0.3s ease;
	}
	
	/* 固定在顶部的标签样式 */
	.fixed-tabs {
		position: fixed;
		top: calc(var(--status-bar-height, 44px) + 44px); /* 确保在导航栏下方 */
		left: 0;
		right: 0;
		margin: 0;
		padding: 15rpx 20rpx 15rpx 30rpx;
		z-index: 98; /* 确保在导航栏下方但高于其他内容 */
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
		border-bottom: 1px solid rgba(235, 238, 245, 0.8);
		background-color: rgba(255, 255, 255, 0.97);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		animation: fadeInDown 0.3s ease;
		width: 100%;
		box-sizing: border-box;
	}

	/* 添加一个淡入动画 */
	@keyframes fadeInDown {
		from {
			opacity: 0;
			transform: translateY(-15px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.all-info-tabs::-webkit-scrollbar {
		display: none;
	}
	
	.all-info-tab {
		display: inline-block;
		padding: 12rpx 24rpx;
		margin-right: 12rpx;
		font-size: 26rpx;
		color: #555;
		border-radius: 16rpx;
		background: none;
		vertical-align: middle;
		flex-shrink: 0;
		position: relative;
		z-index: 1;
		line-height: 1.4;
		transition: all 0.2s ease;
	}
	
	.all-info-tab:active {
		opacity: 0.7;
		transform: scale(0.96);
		background-color: rgba(0, 0, 0, 0.05);
	}
	
	.all-info-tabs .all-info-tab:last-child {
		margin-right: 30rpx;
	}
	
	.all-info-tab.active {
		color: #1677ff;
		background: linear-gradient(to bottom, #e6f0ff, #d2e6ff);
		font-weight: 500;
		box-shadow: 0 2rpx 6rpx rgba(22, 119, 255, 0.15);
		transform: translateY(-2rpx);
		transition: all 0.3s ease;
	}
	
	.all-info-list {
		padding: 0 22rpx;
		overflow: hidden;
	}
	
	.all-info-item {
		background: #ffffff;
		border-radius: 12rpx;
		padding: 26rpx 24rpx 22rpx 24rpx;
		margin-bottom: 22rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
	}
	
	.info-item-header {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
	}
	
	.info-tag {
		font-size: 22rpx;
		color: #1677ff;
		background: rgba(230, 240, 255, 0.7);
		padding: 6rpx 16rpx;
		border-radius: 6rpx;
		margin-right: 12rpx;
		font-weight: 500;
		box-shadow: 0 1rpx 3rpx rgba(22, 119, 255, 0.1);
	}
	
	.info-time {
		font-size: 22rpx;
		color: #999;
	}
	
	.info-content {
		font-size: 26rpx;
		color: #333;
		margin-bottom: 15rpx;
		line-height: 1.5;
	}
	
	.info-meta {
		font-size: 22rpx;
		color: #999;
		display: flex;
		justify-content: space-between;
	}
	
	.info-views {
		display: inline-block;
	}
	
	.topped-item {
		background: #fefefe;
		border-left: 4rpx solid #ff6b6b;
		box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.08);
	}
	
	.premium-topped {
		border-left: 4rpx solid #ffab2b;
		box-shadow: 0 4rpx 12rpx rgba(255, 171, 43, 0.08);
	}
	
	.topped-tag {
		color: #ff6b6b;
		background: rgba(255, 107, 107, 0.1);
		box-shadow: 0 1rpx 3rpx rgba(255, 107, 107, 0.1);
	}
	
	.topped-badge {
		color: #ff6b6b;
		background: rgba(255, 107, 107, 0.1);
	}
	
	.premium-badge {
		color: #ffab2b;
		background: rgba(255, 171, 43, 0.1);
		box-shadow: 0 1rpx 3rpx rgba(255, 171, 43, 0.1);
	}
	
	.tabs-placeholder {
		height: 80rpx;
		width: 100%;
	}
	
	.fade-in {
		animation: fadeIn 0.5s ease;
	}
	
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	.ad-banner {
		margin-bottom: 22rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	}
	
	.ad-banner-image {
		width: 100%;
		height: 140rpx;
		border-radius: 12rpx;
	}
	
	.tabs-placeholder-container {
		height: 80rpx;
		width: 100%;
	}
	
	/* 红包相关样式 */
	.red-packet-item {
		background-color: #FFF9F9;
		border-left: 4rpx solid #FF4D4F;
	}
	
	.red-packet-tag {
		background-color: #FF4D4F;
		color: #FFFFFF;
	}
	
	.red-packet-badge {
		background-color: #FF4D4F;
		color: #FFFFFF;
		font-weight: bold;
		animation: pulse 1.5s infinite;
	}
	
	@keyframes pulse {
		0% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.05);
		}
		100% {
			transform: scale(1);
		}
	}
	
	.info-meta {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.info-meta-left {
		display: flex;
		align-items: center;
	}
	
	.red-packet-info {
		display: flex;
		align-items: center;
		background-color: #FFF0F0;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
	}
	
	.red-packet-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 6rpx;
	}
	
	.red-packet-amount {
		color: #FF4D4F;
		font-size: 24rpx;
		font-weight: bold;
		margin-right: 6rpx;
	}
	
	.red-packet-count {
		color: #FF4D4F;
		font-size: 22rpx;
	}
</style> 