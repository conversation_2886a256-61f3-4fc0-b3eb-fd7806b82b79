"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Array) {
  const _component_uni_tag = common_vendor.resolveComponent("uni-tag");
  _component_uni_tag();
}
if (!Math) {
  RedPacketEntry();
}
const RedPacketEntry = () => "../../components/RedPacket/RedPacketEntry.js";
const _sfc_main = {
  __name: "info-detail",
  setup(__props) {
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const postId = common_vendor.ref("");
    const commentText = common_vendor.ref("");
    const comments = common_vendor.ref([]);
    common_vendor.ref(false);
    const isCollected = common_vendor.ref(false);
    common_vendor.ref(false);
    const showShareGuideLayer = common_vendor.ref(false);
    const post = common_vendor.ref({
      id: "",
      title: "精装修三室两厅，交通便利，拎包入住",
      content: "位于城市中心，周边配套设施齐全，距离地铁站仅500米。房屋为精装修，家具家电齐全，随时可以入住。小区环境优美，安静舒适，24小时保安巡逻，安全有保障。适合一家人居住，看房方便，欢迎随时联系。",
      category: "房屋出租",
      createdAt: Date.now() - 36e5 * 24 * 3,
      views: 256,
      tags: ["精装修", "地铁附近", "拎包入住", "家电齐全"],
      images: [
        "https://via.placeholder.com/800x450?text=Room+1",
        "https://via.placeholder.com/800x450?text=Room+2",
        "https://via.placeholder.com/800x450?text=Room+3"
      ],
      publisher: {
        username: "房产小能手",
        avatar: "https://via.placeholder.com/100?text=Avatar",
        posts: 32,
        followers: 128,
        rating: 4.8,
        phone: "13912345678"
      },
      location: {
        address: "某某市某某区某某街123号",
        latitude: 30.123456,
        longitude: 120.123456
      }
    });
    const relatedPosts = common_vendor.ref([]);
    const loadRelatedPosts = () => {
      setTimeout(() => {
        relatedPosts.value = [
          {
            id: "post001",
            title: "全新装修两室一厅出租，家电齐全",
            category: "房屋出租",
            createdAt: Date.now() - 864e5 * 1,
            views: 158,
            image: "https://via.placeholder.com/100?text=Room"
          },
          {
            id: "post002",
            title: "市中心单身公寓，拎包入住",
            category: "房屋出租",
            createdAt: Date.now() - 864e5 * 2,
            views: 243,
            image: "https://via.placeholder.com/100?text=Apartment"
          },
          {
            id: "post003",
            title: "南北通透三居室，学区房，周边配套齐全",
            category: "房屋出租",
            createdAt: Date.now() - 864e5 * 3,
            views: 325,
            image: "https://via.placeholder.com/100?text=House"
          }
        ];
      }, 500);
    };
    const navigateToPostDetail = (id) => {
      if (id === postId.value) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/publish/info-detail?id=${id}`
      });
    };
    const navigateToInfoList = (e) => {
      var _a;
      if (e)
        e.stopPropagation();
      const infoCategory = ((_a = post.value.tags) == null ? void 0 : _a[0]) || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=info&title=${encodeURIComponent("生活信息")}&category=${encodeURIComponent(infoCategory)}&active=info`
      });
    };
    common_vendor.onMounted(() => {
      var _a;
      common_vendor.index.setNavigationBarTitle({
        title: "信息详情"
      });
      common_vendor.index.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#0052CC"
      });
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      postId.value = options.id || "1";
      post.value.id = postId.value;
      common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:357", "加载信息详情:", postId.value);
      loadRelatedPosts();
      const shouldDirectShare = options.directShare === "1" || common_vendor.index.getStorageSync("directShare") === 1;
      if (common_vendor.index.getStorageSync("directShare")) {
        common_vendor.index.removeStorageSync("directShare");
      }
      if (options.autoShare === "true") {
        common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:372", "检测到autoShare参数，准备显示分享菜单");
        setTimeout(() => {
          showShareGuideLayer.value = true;
        }, 500);
      }
      if (shouldDirectShare) {
        common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:383", "检测到直接分享标记，准备直接分享");
        setTimeout(() => {
          triggerDirectShare();
        }, 1e3);
      }
      comments.value = [
        {
          id: 1,
          username: "用户123",
          avatar: "https://via.placeholder.com/40?text=U1",
          content: "房子位置很好，周边设施齐全，交通便利。",
          createdAt: Date.now() - 36e5 * 48
        },
        {
          id: 2,
          username: "小明",
          avatar: "https://via.placeholder.com/40?text=U2",
          content: "请问有停车位吗？",
          createdAt: Date.now() - 36e5 * 24
        }
      ];
      const { hasRedPacket, redPacketAmount, redPacketType, redPacketCount, redPacketRemain } = options;
      if (hasRedPacket === "1" && redPacketAmount) {
        form.value.redPacket = {
          amount: redPacketAmount,
          count: redPacketCount || "0",
          remain: redPacketRemain || "0",
          type: redPacketType || "fixed",
          taskType: 0
          // 默认任务类型
        };
        common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:420", "设置红包信息:", JSON.stringify(form.value.redPacket));
      }
    });
    const previewImage = (index) => {
      common_vendor.index.previewImage({
        current: index,
        urls: post.value.images
      });
    };
    const contactPublisher = () => {
      var _a;
      const phone = ((_a = post.value.publisher) == null ? void 0 : _a.phone) || "13800138000";
      common_vendor.index.showModal({
        title: "联系发布者",
        content: `确定要拨打${phone}吗？
记得说在"磁州生活网"看到的哦~`,
        confirmText: "立即拨打",
        confirmColor: "#0052CC",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.makePhoneCall({
              phoneNumber: phone,
              success: () => {
                common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:496", "拨打电话成功");
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/publish/info-detail.vue:499", "拨打电话失败", err);
                common_vendor.index.showToast({
                  title: "拨打电话失败",
                  icon: "none"
                });
              }
            });
          }
        }
      });
    };
    const openChat = () => {
      common_vendor.index.navigateTo({
        url: `/pages/chat/index?userId=${post.value.publisher.id}&username=${encodeURIComponent(post.value.publisher.username)}`
      });
    };
    const submitComment = () => {
      if (!commentText.value.trim()) {
        common_vendor.index.showToast({
          title: "评论内容不能为空",
          icon: "none"
        });
        return;
      }
      comments.value.unshift({
        id: Date.now(),
        username: "当前用户",
        avatar: "https://via.placeholder.com/40?text=Me",
        content: commentText.value,
        createdAt: Date.now()
      });
      commentText.value = "";
      common_vendor.index.showToast({
        title: "评论成功",
        icon: "success"
      });
    };
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      common_vendor.index.showToast({
        title: isCollected.value ? "收藏成功" : "已取消收藏",
        icon: "none"
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const makePhoneCall = () => {
      if (post.value.publisher && post.value.publisher.phone) {
        common_vendor.index.makePhoneCall({
          phoneNumber: post.value.publisher.phone,
          success: () => {
            common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:574", "拨打电话成功");
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/publish/info-detail.vue:577", "拨打电话失败:", err);
            common_vendor.index.showToast({
              title: "拨打电话失败",
              icon: "none"
            });
          }
        });
      } else {
        common_vendor.index.showModal({
          title: "提示",
          content: "该信息暂无联系电话，请通过私信联系发布者",
          showCancel: false
        });
      }
    };
    const receiveRedPacket = () => {
      common_vendor.index.showModal({
        title: "领取红包",
        content: "确定要领取这个红包吗？需要完成转发或助力任务后才能获得。",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "领取成功，请完成任务",
              icon: "success"
            });
          }
        }
      });
    };
    const triggerDirectShare = () => {
      common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:674", "尝试直接触发分享");
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        showShareItems: ["wechatFriends", "wechatMoment"],
        success: () => {
          common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:683", "成功显示分享菜单");
          setTimeout(() => {
            try {
              common_vendor.wx$1.showShareMenu({
                withShareTicket: true,
                menus: ["shareAppMessage", "shareTimeline"],
                success: () => {
                  common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:708", "微信API成功显示分享菜单");
                  common_vendor.wx$1.updateShareMenu({
                    withShareTicket: true,
                    success: () => {
                      common_vendor.index.__f__("log", "at pages/publish/info-detail.vue:714", "更新分享菜单成功");
                    }
                  });
                }
              });
            } catch (e) {
              common_vendor.index.__f__("error", "at pages/publish/info-detail.vue:720", "微信分享API调用失败", e);
            }
            common_vendor.index.showToast({
              title: "请点击右上角进行分享",
              icon: "none",
              duration: 2e3
            });
          }, 500);
        }
      });
    };
    const form = common_vendor.reactive({
      redPacket: null
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e;
      return common_vendor.e({
        a: common_vendor.t(post.value.title),
        b: common_vendor.t(formatTime(post.value.createdAt)),
        c: common_vendor.t(post.value.category),
        d: common_vendor.t(post.value.views),
        e: post.value.images && post.value.images.length > 0
      }, post.value.images && post.value.images.length > 0 ? common_vendor.e({
        f: post.value.images.length > 1
      }, post.value.images.length > 1 ? {
        g: common_vendor.f(post.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index,
            c: common_vendor.o(($event) => previewImage(index), index)
          };
        })
      } : {
        h: post.value.images[0],
        i: common_vendor.o(($event) => previewImage(0))
      }) : {}, {
        j: post.value.content,
        k: post.value.tags && post.value.tags.length > 0
      }, post.value.tags && post.value.tags.length > 0 ? {
        l: common_vendor.f(post.value.tags, (tag, k0, i0) => {
          return {
            a: tag,
            b: "19056fca-0-" + i0,
            c: common_vendor.p({
              text: tag,
              size: "small",
              type: "default",
              inverted: true
            })
          };
        })
      } : {}, {
        m: (_a = post.value.publisher) == null ? void 0 : _a.avatar,
        n: common_vendor.t((_b = post.value.publisher) == null ? void 0 : _b.username),
        o: common_vendor.t(((_c = post.value.publisher) == null ? void 0 : _c.posts) || 0),
        p: common_vendor.t(((_d = post.value.publisher) == null ? void 0 : _d.followers) || 0),
        q: common_vendor.t(((_e = post.value.publisher) == null ? void 0 : _e.rating) || "5.0"),
        r: common_vendor.o(contactPublisher),
        s: post.value.location
      }, post.value.location ? {
        t: common_vendor.t(post.value.location.address)
      } : {}, {
        v: comments.value.length === 0
      }, comments.value.length === 0 ? {} : {}, {
        w: common_vendor.f(comments.value, (comment, index, i0) => {
          return {
            a: comment.avatar,
            b: common_vendor.t(comment.username),
            c: common_vendor.t(formatTime(comment.createdAt)),
            d: common_vendor.t(comment.content),
            e: index
          };
        }),
        x: common_vendor.o(submitComment),
        y: commentText.value,
        z: common_vendor.o(($event) => commentText.value = $event.detail.value),
        A: common_vendor.o(submitComment),
        B: common_vendor.f(relatedPosts.value.slice(0, 3), (relatedPost, index, i0) => {
          return common_vendor.e({
            a: relatedPost.image
          }, relatedPost.image ? {
            b: relatedPost.image
          } : {}, {
            c: common_vendor.t(relatedPost.title),
            d: common_vendor.t(relatedPost.category),
            e: common_vendor.t(formatTime(relatedPost.createdAt)),
            f: common_vendor.t(relatedPost.views),
            g: index,
            h: common_vendor.o(($event) => navigateToPostDetail(relatedPost.id), index)
          });
        }),
        C: relatedPosts.value.length === 0
      }, relatedPosts.value.length === 0 ? {
        D: common_assets._imports_1$3
      } : {}, {
        E: relatedPosts.value.length > 0
      }, relatedPosts.value.length > 0 ? {
        F: common_vendor.o(navigateToInfoList)
      } : {}, {
        G: common_vendor.o(($event) => form.redPacket = $event),
        H: common_vendor.p({
          modelValue: form.redPacket
        }),
        I: form.redPacket && form.redPacket.amount
      }, form.redPacket && form.redPacket.amount ? {
        J: common_vendor.t(form.redPacket.amount),
        K: common_vendor.t(form.redPacket.count),
        L: common_vendor.t(form.redPacket.remain),
        M: common_vendor.t(form.redPacket.type === "fixed" ? "普通红包" : "拼手气红包"),
        N: common_vendor.o(receiveRedPacket)
      } : {}, {
        O: common_assets._imports_12,
        P: common_vendor.o(goToHome),
        Q: common_assets._imports_3$2,
        R: common_vendor.o(toggleCollect),
        S: common_assets._imports_3$3,
        T: common_assets._imports_14,
        U: common_vendor.o(openChat),
        V: common_vendor.o(makePhoneCall),
        W: showShareGuideLayer.value
      }, showShareGuideLayer.value ? {
        X: common_vendor.o(($event) => showShareGuideLayer.value = false),
        Y: common_vendor.o(() => {
        }),
        Z: common_vendor.o(($event) => showShareGuideLayer.value = false)
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/info-detail.js.map
