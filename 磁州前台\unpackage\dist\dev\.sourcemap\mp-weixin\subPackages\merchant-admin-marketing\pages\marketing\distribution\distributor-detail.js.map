{"version": 3, "file": "distributor-detail.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xkaXN0cmlidXRpb25cZGlzdHJpYnV0b3ItZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"detail-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">分销员详情</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 基本信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"distributor-header\">\n        <image class=\"avatar\" :src=\"distributorInfo.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\n        <view class=\"info-content\">\n          <view class=\"name-wrap\">\n            <text class=\"name\">{{distributorInfo.name || '未知用户'}}</text>\n            <text class=\"level-tag\" :style=\"{ backgroundColor: distributorInfo.levelColor || '#67C23A' }\">\n              {{distributorInfo.levelName || '普通分销员'}}\n            </text>\n            <text class=\"status-tag\" :class=\"getStatusClass(distributorInfo.status)\">\n              {{getStatusText(distributorInfo.status)}}\n            </text>\n          </view>\n          <text class=\"phone\">{{distributorInfo.phone || '无联系方式'}}</text>\n        </view>\n      </view>\n      \n      <view class=\"info-grid\">\n        <view class=\"info-item\">\n          <text class=\"item-label\">注册时间</text>\n          <text class=\"item-value\">{{formatDate(distributorInfo.createdAt)}}</text>\n        </view>\n        <view class=\"info-item\">\n          <text class=\"item-label\">邀请码</text>\n          <text class=\"item-value\">{{distributorInfo.inviteCode || '无'}}</text>\n        </view>\n        <view class=\"info-item\">\n          <text class=\"item-label\">上级分销员</text>\n          <text class=\"item-value\">{{distributorInfo.parentName || '无'}}</text>\n        </view>\n        <view class=\"info-item\">\n          <text class=\"item-label\">团队人数</text>\n          <text class=\"item-value\">{{distributorInfo.teamCount || 0}}人</text>\n        </view>\n      </view>\n      \n      <view class=\"action-btns\">\n        <block v-if=\"distributorInfo.status === 'pending'\">\n          <view class=\"action-btn approve\" @click=\"approveDistributor\">通过申请</view>\n          <view class=\"action-btn reject\" @click=\"rejectDistributor\">拒绝申请</view>\n        </block>\n        \n        <block v-else-if=\"distributorInfo.status === 'active'\">\n          <view class=\"action-btn disable\" @click=\"disableDistributor\">禁用分销员</view>\n          <view class=\"action-btn set-level\" @click=\"setLevel\">设置等级</view>\n        </block>\n        \n        <block v-else-if=\"distributorInfo.status === 'disabled'\">\n          <view class=\"action-btn enable\" @click=\"enableDistributor\">启用分销员</view>\n        </block>\n      </view>\n    </view>\n    \n    <!-- 数据概览 -->\n    <view class=\"stats-card\">\n      <view class=\"card-header\">\n        <text class=\"card-title\">数据概览</text>\n      </view>\n      \n      <view class=\"stats-grid\">\n        <view class=\"stats-item\">\n          <text class=\"stats-value\">{{distributorInfo.orderCount || 0}}</text>\n          <text class=\"stats-label\">推广订单</text>\n        </view>\n        <view class=\"stats-item\">\n          <text class=\"stats-value\">¥{{formatAmount(distributorInfo.commissionTotal)}}</text>\n          <text class=\"stats-label\">累计佣金</text>\n        </view>\n        <view class=\"stats-item\">\n          <text class=\"stats-value\">¥{{formatAmount(distributorInfo.commissionAvailable)}}</text>\n          <text class=\"stats-label\">可提现佣金</text>\n        </view>\n        <view class=\"stats-item\">\n          <text class=\"stats-value\">¥{{formatAmount(distributorInfo.commissionWithdrawn)}}</text>\n          <text class=\"stats-label\">已提现佣金</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 选项卡 -->\n    <view class=\"tabs\">\n      <view \n        class=\"tab-item\" \n        :class=\"{ 'active': activeTab === 'orders' }\"\n        @click=\"activeTab = 'orders'\"\n      >\n        <text>推广订单</text>\n      </view>\n      <view \n        class=\"tab-item\" \n        :class=\"{ 'active': activeTab === 'team' }\"\n        @click=\"activeTab = 'team'\"\n      >\n        <text>团队成员</text>\n      </view>\n      <view \n        class=\"tab-item\" \n        :class=\"{ 'active': activeTab === 'commission' }\"\n        @click=\"activeTab = 'commission'\"\n      >\n        <text>佣金记录</text>\n      </view>\n      <view \n        class=\"tab-item\" \n        :class=\"{ 'active': activeTab === 'withdraw' }\"\n        @click=\"activeTab = 'withdraw'\"\n      >\n        <text>提现记录</text>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"tab-content\">\n      <!-- 推广订单 -->\n      <view v-if=\"activeTab === 'orders'\">\n        <view class=\"order-list\" v-if=\"orders.length > 0\">\n          <view \n            v-for=\"(item, index) in orders\" \n            :key=\"index\" \n            class=\"order-item\"\n          >\n            <view class=\"order-header\">\n              <text class=\"order-id\">订单号：{{item.orderNo}}</text>\n              <text class=\"order-status\">{{item.statusText}}</text>\n            </view>\n            \n            <view class=\"order-content\">\n              <view class=\"product-info\">\n                <image class=\"product-image\" :src=\"item.productImage\" mode=\"aspectFill\"></image>\n                <view class=\"product-detail\">\n                  <text class=\"product-name\">{{item.productName}}</text>\n                  <view class=\"product-meta\">\n                    <text class=\"product-price\">¥{{item.productPrice}}</text>\n                    <text class=\"product-quantity\">x{{item.quantity}}</text>\n                  </view>\n                </view>\n              </view>\n              \n              <view class=\"order-footer\">\n                <view class=\"order-time\">{{formatDate(item.createdAt)}}</view>\n                <view class=\"order-amount\">\n                  <text>订单金额：</text>\n                  <text class=\"amount\">¥{{item.orderAmount}}</text>\n                </view>\n                <view class=\"commission-info\">\n                  <text>佣金：</text>\n                  <text class=\"commission\">¥{{item.commission}}</text>\n                  <text class=\"commission-status\" :class=\"getCommissionStatusClass(item.commissionStatus)\">\n                    {{getCommissionStatusText(item.commissionStatus)}}\n                  </text>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-else-if=\"!orderLoading\">\n          <image class=\"empty-image\" src=\"/static/images/empty-order.png\" mode=\"aspectFit\"></image>\n          <text class=\"empty-text\">暂无推广订单</text>\n        </view>\n        \n        <!-- 加载中 -->\n        <view class=\"loading-state\" v-if=\"orderLoading\">\n          <view class=\"loading-icon\"></view>\n          <text class=\"loading-text\">加载中...</text>\n        </view>\n        \n        <!-- 分页 -->\n        <view class=\"pagination\" v-if=\"orders.length > 0 && !orderLoading\">\n          <view class=\"page-info\">\n            <text>共 {{orderPagination.total}} 条记录，当前 {{orderPagination.current}}/{{orderPagination.totalPages}} 页</text>\n          </view>\n          <view class=\"page-actions\">\n            <view \n              class=\"page-btn prev\" \n              :class=\"{ 'disabled': orderPagination.current <= 1 }\"\n              @click=\"prevOrderPage\"\n            >上一页</view>\n            <view \n              class=\"page-btn next\" \n              :class=\"{ 'disabled': orderPagination.current >= orderPagination.totalPages }\"\n              @click=\"nextOrderPage\"\n            >下一页</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 团队成员 -->\n      <view v-if=\"activeTab === 'team'\">\n        <view class=\"team-list\" v-if=\"teamMembers.length > 0\">\n          <view \n            v-for=\"(item, index) in teamMembers\" \n            :key=\"index\" \n            class=\"team-item\"\n            @click=\"viewTeamMember(item)\"\n          >\n            <view class=\"member-info\">\n              <image class=\"member-avatar\" :src=\"item.avatar || '/static/images/default-avatar.png'\" mode=\"aspectFill\"></image>\n              <view class=\"member-detail\">\n                <view class=\"member-name-wrap\">\n                  <text class=\"member-name\">{{item.name}}</text>\n                  <text class=\"member-level\" :style=\"{ backgroundColor: item.levelColor || '#67C23A' }\">{{item.levelName}}</text>\n                </view>\n                <text class=\"member-phone\">{{item.phone}}</text>\n              </view>\n            </view>\n            \n            <view class=\"member-stats\">\n              <view class=\"member-stat\">\n                <text class=\"stat-label\">推广订单</text>\n                <text class=\"stat-value\">{{item.orderCount || 0}}</text>\n              </view>\n              <view class=\"member-stat\">\n                <text class=\"stat-label\">累计佣金</text>\n                <text class=\"stat-value\">¥{{formatAmount(item.commissionTotal)}}</text>\n              </view>\n              <view class=\"member-stat\">\n                <text class=\"stat-label\">团队人数</text>\n                <text class=\"stat-value\">{{item.teamCount || 0}}</text>\n              </view>\n            </view>\n            \n            <view class=\"member-footer\">\n              <text class=\"join-time\">加入时间：{{formatDate(item.createdAt)}}</text>\n              <view class=\"arrow-right\"></view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-else-if=\"!teamLoading\">\n          <image class=\"empty-image\" src=\"/static/images/empty-team.png\" mode=\"aspectFit\"></image>\n          <text class=\"empty-text\">暂无团队成员</text>\n        </view>\n        \n        <!-- 加载中 -->\n        <view class=\"loading-state\" v-if=\"teamLoading\">\n          <view class=\"loading-icon\"></view>\n          <text class=\"loading-text\">加载中...</text>\n        </view>\n        \n        <!-- 分页 -->\n        <view class=\"pagination\" v-if=\"teamMembers.length > 0 && !teamLoading\">\n          <view class=\"page-info\">\n            <text>共 {{teamPagination.total}} 条记录，当前 {{teamPagination.current}}/{{teamPagination.totalPages}} 页</text>\n          </view>\n          <view class=\"page-actions\">\n            <view \n              class=\"page-btn prev\" \n              :class=\"{ 'disabled': teamPagination.current <= 1 }\"\n              @click=\"prevTeamPage\"\n            >上一页</view>\n            <view \n              class=\"page-btn next\" \n              :class=\"{ 'disabled': teamPagination.current >= teamPagination.totalPages }\"\n              @click=\"nextTeamPage\"\n            >下一页</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 佣金记录 -->\n      <view v-if=\"activeTab === 'commission'\">\n        <view class=\"commission-list\" v-if=\"commissions.length > 0\">\n          <view \n            v-for=\"(item, index) in commissions\" \n            :key=\"index\" \n            class=\"commission-item\"\n          >\n            <view class=\"commission-header\">\n              <view class=\"commission-type\" :class=\"getCommissionTypeClass(item.type)\">\n                <text>{{getCommissionTypeText(item.type)}}</text>\n              </view>\n              <text class=\"commission-amount\" :class=\"{ 'income': item.amount > 0, 'expense': item.amount < 0 }\">\n                {{item.amount > 0 ? '+' : ''}}{{formatAmount(item.amount)}}\n              </text>\n            </view>\n            \n            <view class=\"commission-content\">\n              <text class=\"commission-desc\">{{item.description || '佣金记录'}}</text>\n              <text class=\"commission-order\" v-if=\"item.orderNo\">订单号：{{item.orderNo}}</text>\n              <text class=\"commission-time\">{{formatDateTime(item.createdAt)}}</text>\n            </view>\n            \n            <view class=\"commission-footer\">\n              <text class=\"commission-status\" :class=\"getCommissionStatusClass(item.status)\">\n                {{getCommissionStatusText(item.status)}}\n              </text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-else-if=\"!commissionLoading\">\n          <image class=\"empty-image\" src=\"/static/images/empty-commission.png\" mode=\"aspectFit\"></image>\n          <text class=\"empty-text\">暂无佣金记录</text>\n        </view>\n        \n        <!-- 加载中 -->\n        <view class=\"loading-state\" v-if=\"commissionLoading\">\n          <view class=\"loading-icon\"></view>\n          <text class=\"loading-text\">加载中...</text>\n        </view>\n        \n        <!-- 分页 -->\n        <view class=\"pagination\" v-if=\"commissions.length > 0 && !commissionLoading\">\n          <view class=\"page-info\">\n            <text>共 {{commissionPagination.total}} 条记录，当前 {{commissionPagination.current}}/{{commissionPagination.totalPages}} 页</text>\n          </view>\n          <view class=\"page-actions\">\n            <view \n              class=\"page-btn prev\" \n              :class=\"{ 'disabled': commissionPagination.current <= 1 }\"\n              @click=\"prevCommissionPage\"\n            >上一页</view>\n            <view \n              class=\"page-btn next\" \n              :class=\"{ 'disabled': commissionPagination.current >= commissionPagination.totalPages }\"\n              @click=\"nextCommissionPage\"\n            >下一页</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 提现记录 -->\n      <view v-if=\"activeTab === 'withdraw'\">\n        <view class=\"withdraw-list\" v-if=\"withdrawals.length > 0\">\n          <view \n            v-for=\"(item, index) in withdrawals\" \n            :key=\"index\" \n            class=\"withdraw-item\"\n          >\n            <view class=\"withdraw-header\">\n              <text class=\"withdraw-id\">提现单号：{{item.withdrawNo}}</text>\n              <text class=\"withdraw-status\" :class=\"getWithdrawStatusClass(item.status)\">\n                {{getWithdrawStatusText(item.status)}}\n              </text>\n            </view>\n            \n            <view class=\"withdraw-content\">\n              <view class=\"withdraw-info\">\n                <text class=\"withdraw-label\">提现金额</text>\n                <text class=\"withdraw-amount\">¥{{formatAmount(item.amount)}}</text>\n              </view>\n              \n              <view class=\"withdraw-info\">\n                <text class=\"withdraw-label\">手续费</text>\n                <text class=\"withdraw-fee\">¥{{formatAmount(item.fee)}}</text>\n              </view>\n              \n              <view class=\"withdraw-info\">\n                <text class=\"withdraw-label\">实际到账</text>\n                <text class=\"withdraw-actual\">¥{{formatAmount(item.actualAmount)}}</text>\n              </view>\n              \n              <view class=\"withdraw-info\">\n                <text class=\"withdraw-label\">提现方式</text>\n                <text class=\"withdraw-method\">{{getWithdrawMethodText(item.method)}}</text>\n              </view>\n              \n              <view class=\"withdraw-info\">\n                <text class=\"withdraw-label\">提现账号</text>\n                <text class=\"withdraw-account\">{{item.account}}</text>\n              </view>\n              \n              <view class=\"withdraw-info\">\n                <text class=\"withdraw-label\">申请时间</text>\n                <text class=\"withdraw-time\">{{formatDateTime(item.createdAt)}}</text>\n              </view>\n              \n              <view class=\"withdraw-info\" v-if=\"item.processedAt\">\n                <text class=\"withdraw-label\">处理时间</text>\n                <text class=\"withdraw-time\">{{formatDateTime(item.processedAt)}}</text>\n              </view>\n              \n              <view class=\"withdraw-info\" v-if=\"item.remark\">\n                <text class=\"withdraw-label\">备注</text>\n                <text class=\"withdraw-remark\">{{item.remark}}</text>\n              </view>\n            </view>\n            \n            <view class=\"withdraw-footer\" v-if=\"item.status === 'pending'\">\n              <view class=\"withdraw-btn approve\" @click=\"approveWithdrawal(item)\">通过</view>\n              <view class=\"withdraw-btn reject\" @click=\"rejectWithdrawal(item)\">拒绝</view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-else-if=\"!withdrawLoading\">\n          <image class=\"empty-image\" src=\"/static/images/empty-withdraw.png\" mode=\"aspectFit\"></image>\n          <text class=\"empty-text\">暂无提现记录</text>\n        </view>\n        \n        <!-- 加载中 -->\n        <view class=\"loading-state\" v-if=\"withdrawLoading\">\n          <view class=\"loading-icon\"></view>\n          <text class=\"loading-text\">加载中...</text>\n        </view>\n        \n        <!-- 分页 -->\n        <view class=\"pagination\" v-if=\"withdrawals.length > 0 && !withdrawLoading\">\n          <view class=\"page-info\">\n            <text>共 {{withdrawPagination.total}} 条记录，当前 {{withdrawPagination.current}}/{{withdrawPagination.totalPages}} 页</text>\n          </view>\n          <view class=\"page-actions\">\n            <view \n              class=\"page-btn prev\" \n              :class=\"{ 'disabled': withdrawPagination.current <= 1 }\"\n              @click=\"prevWithdrawPage\"\n            >上一页</view>\n            <view \n              class=\"page-btn next\" \n              :class=\"{ 'disabled': withdrawPagination.current >= withdrawPagination.totalPages }\"\n              @click=\"nextWithdrawPage\"\n            >下一页</view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted, watch } from 'vue';\nimport distributionService from '@/utils/distributionService';\n\n// 分销员ID\nconst distributorId = ref('');\n\n// 分销员详情\nconst distributorInfo = ref({});\n\n// 当前激活的选项卡\nconst activeTab = ref('orders');\n\n// 推广订单列表\nconst orders = ref([]);\n\n// 订单加载状态\nconst orderLoading = ref(false);\n\n// 订单分页信息\nconst orderPagination = reactive({\n  current: 1,\n  pageSize: 10,\n  total: 0,\n  totalPages: 0\n});\n\n// 团队成员列表\nconst teamMembers = ref([]);\n\n// 团队加载状态\nconst teamLoading = ref(false);\n\n// 团队分页信息\nconst teamPagination = reactive({\n  current: 1,\n  pageSize: 10,\n  total: 0,\n  totalPages: 0\n});\n\n// 佣金记录列表\nconst commissions = ref([]);\n\n// 佣金加载状态\nconst commissionLoading = ref(false);\n\n// 佣金分页信息\nconst commissionPagination = reactive({\n  current: 1,\n  pageSize: 10,\n  total: 0,\n  totalPages: 0\n});\n\n// 提现记录列表\nconst withdrawals = ref([]);\n\n// 提现加载状态\nconst withdrawLoading = ref(false);\n\n// 提现分页信息\nconst withdrawPagination = reactive({\n  current: 1,\n  pageSize: 10,\n  total: 0,\n  totalPages: 0\n});\n\n// 页面加载\nonMounted(() => {\n  // 获取路由参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.$page?.options || {};\n  \n  // 设置分销员ID\n  distributorId.value = options.id || '';\n  \n  // 获取分销员详情\n  if (distributorId.value) {\n    getDistributorDetail();\n  } else {\n    uni.showToast({\n      title: '缺少分销员ID',\n      icon: 'none'\n    });\n    \n    setTimeout(() => {\n      goBack();\n    }, 1500);\n  }\n});\n\n// 获取分销员详情\nconst getDistributorDetail = async () => {\n  try {\n    uni.showLoading({\n      title: '加载中...',\n      mask: true\n    });\n    \n    const result = await distributionService.getDistributorDetail(distributorId.value);\n    \n    uni.hideLoading();\n    \n    if (result) {\n      distributorInfo.value = result;\n    } else {\n      uni.showToast({\n        title: '获取分销员详情失败',\n        icon: 'none'\n      });\n    }\n  } catch (error) {\n    uni.hideLoading();\n    console.error('获取分销员详情失败', error);\n    uni.showToast({\n      title: '获取分销员详情失败',\n      icon: 'none'\n    });\n  }\n};\n\n// 格式化金额\nconst formatAmount = (amount) => {\n  return (amount || 0).toFixed(2);\n};\n\n// 格式化日期\nconst formatDate = (dateString) => {\n  if (!dateString) return '未知';\n  \n  const date = new Date(dateString);\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  \n  return `${year}-${month}-${day}`;\n};\n\n// 获取状态样式类\nconst getStatusClass = (status) => {\n  switch (status) {\n    case 'active':\n      return 'status-active';\n    case 'disabled':\n      return 'status-disabled';\n    case 'pending':\n      return 'status-pending';\n    case 'rejected':\n      return 'status-rejected';\n    default:\n      return '';\n  }\n};\n\n// 获取状态文本\nconst getStatusText = (status) => {\n  switch (status) {\n    case 'active':\n      return '已启用';\n    case 'disabled':\n      return '已禁用';\n    case 'pending':\n      return '待审核';\n    case 'rejected':\n      return '已拒绝';\n    default:\n      return '未知';\n  }\n};\n\n// 审核通过\nconst approveDistributor = () => {\n  uni.showModal({\n    title: '审核通过',\n    content: `确定通过 ${distributorInfo.value.name} 的分销员申请吗？`,\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.reviewDistributorApplication({\n            id: distributorId.value,\n            status: 'approved'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '审核通过成功',\n              icon: 'success'\n            });\n            \n            // 刷新详情\n            getDistributorDetail();\n          } else {\n            uni.showModal({\n              title: '审核失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('审核失败', error);\n          uni.showToast({\n            title: '审核失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 拒绝申请\nconst rejectDistributor = () => {\n  uni.showModal({\n    title: '拒绝申请',\n    content: `确定拒绝 ${distributorInfo.value.name} 的分销员申请吗？`,\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.reviewDistributorApplication({\n            id: distributorId.value,\n            status: 'rejected'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '已拒绝申请',\n              icon: 'success'\n            });\n            \n            // 刷新详情\n            getDistributorDetail();\n          } else {\n            uni.showModal({\n              title: '操作失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('操作失败', error);\n          uni.showToast({\n            title: '操作失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 禁用分销员\nconst disableDistributor = () => {\n  uni.showModal({\n    title: '禁用分销员',\n    content: `确定禁用 ${distributorInfo.value.name} 的分销员资格吗？禁用后该用户将无法进行分销活动。`,\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.toggleDistributorStatus({\n            id: distributorId.value,\n            status: 'disabled'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '禁用成功',\n              icon: 'success'\n            });\n            \n            // 刷新详情\n            getDistributorDetail();\n          } else {\n            uni.showModal({\n              title: '操作失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('操作失败', error);\n          uni.showToast({\n            title: '操作失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 启用分销员\nconst enableDistributor = () => {\n  uni.showModal({\n    title: '启用分销员',\n    content: `确定启用 ${distributorInfo.value.name} 的分销员资格吗？`,\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.toggleDistributorStatus({\n            id: distributorId.value,\n            status: 'active'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '启用成功',\n              icon: 'success'\n            });\n            \n            // 刷新详情\n            getDistributorDetail();\n          } else {\n            uni.showModal({\n              title: '操作失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('操作失败', error);\n          uni.showToast({\n            title: '操作失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 设置等级\nconst setLevel = () => {\n  uni.navigateTo({\n    url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/set-level?id=${distributorId.value}`\n  });\n};\n\n// 返回上一页\nconst goBack = () => {\n      uni.navigateBack();\n};\n\n// 显示帮助\nconst showHelp = () => {\n      uni.showModal({\n        title: '分销员详情帮助',\n    content: '在此页面您可以查看分销员的详细信息，包括基本资料、推广订单、团队成员、佣金记录和提现记录等。您还可以对分销员进行审核、禁用/启用和设置等级等操作。',\n        showCancel: false\n      });\n};\n\n// 监听选项卡变化\nwatch(activeTab, (newVal) => {\n  if (newVal === 'orders' && orders.value.length === 0) {\n    getDistributionOrders();\n  } else if (newVal === 'team' && teamMembers.value.length === 0) {\n    getTeamMembers();\n  } else if (newVal === 'commission' && commissions.value.length === 0) {\n    getCommissionRecords();\n  } else if (newVal === 'withdraw' && withdrawals.value.length === 0) {\n    getWithdrawRecords();\n  }\n});\n\n// 获取推广订单列表\nconst getDistributionOrders = async () => {\n  try {\n    orderLoading.value = true;\n    \n    const params = {\n      distributorId: distributorId.value,\n      page: orderPagination.current,\n      pageSize: orderPagination.pageSize\n    };\n    \n    const result = await distributionService.getDistributionOrders(params);\n    \n    if (result) {\n      orders.value = result.list || [];\n      \n      // 更新分页信息\n      orderPagination.current = result.pagination.current;\n      orderPagination.total = result.pagination.total;\n      orderPagination.totalPages = result.pagination.totalPages;\n    }\n  } catch (error) {\n    console.error('获取推广订单列表失败', error);\n    uni.showToast({\n      title: '获取推广订单列表失败',\n      icon: 'none'\n    });\n  } finally {\n    orderLoading.value = false;\n  }\n};\n\n// 获取佣金状态样式类\nconst getCommissionStatusClass = (status) => {\n  switch (status) {\n    case 'paid':\n      return 'status-paid';\n    case 'pending':\n      return 'status-pending';\n    case 'frozen':\n      return 'status-frozen';\n    case 'cancelled':\n      return 'status-cancelled';\n    default:\n      return '';\n  }\n};\n\n// 获取佣金状态文本\nconst getCommissionStatusText = (status) => {\n  switch (status) {\n    case 'paid':\n      return '已结算';\n    case 'pending':\n      return '待结算';\n    case 'frozen':\n      return '已冻结';\n    case 'cancelled':\n      return '已取消';\n    default:\n      return '未知';\n  }\n};\n\n// 上一页订单\nconst prevOrderPage = () => {\n  if (orderPagination.current > 1) {\n    orderPagination.current--;\n    getDistributionOrders();\n  }\n};\n\n// 下一页订单\nconst nextOrderPage = () => {\n  if (orderPagination.current < orderPagination.totalPages) {\n    orderPagination.current++;\n    getDistributionOrders();\n  }\n};\n\n// 获取团队成员列表\nconst getTeamMembers = async () => {\n  try {\n    teamLoading.value = true;\n    \n    const params = {\n      distributorId: distributorId.value,\n      page: teamPagination.current,\n      pageSize: teamPagination.pageSize\n    };\n    \n    const result = await distributionService.getTeamMembers(params);\n    \n    if (result) {\n      teamMembers.value = result.list || [];\n      \n      // 更新分页信息\n      teamPagination.current = result.pagination.current;\n      teamPagination.total = result.pagination.total;\n      teamPagination.totalPages = result.pagination.totalPages;\n    }\n  } catch (error) {\n    console.error('获取团队成员列表失败', error);\n    uni.showToast({\n      title: '获取团队成员列表失败',\n      icon: 'none'\n    });\n  } finally {\n    teamLoading.value = false;\n  }\n};\n\n// 查看团队成员详情\nconst viewTeamMember = (item) => {\n  uni.navigateTo({\n    url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${item.id}`\n  });\n};\n\n// 上一页团队\nconst prevTeamPage = () => {\n  if (teamPagination.current > 1) {\n    teamPagination.current--;\n    getTeamMembers();\n  }\n};\n\n// 下一页团队\nconst nextTeamPage = () => {\n  if (teamPagination.current < teamPagination.totalPages) {\n    teamPagination.current++;\n    getTeamMembers();\n  }\n};\n\n// 获取佣金记录列表\nconst getCommissionRecords = async () => {\n  try {\n    commissionLoading.value = true;\n    \n    const params = {\n      distributorId: distributorId.value,\n      page: commissionPagination.current,\n      pageSize: commissionPagination.pageSize\n    };\n    \n    const result = await distributionService.getCommissionList(params);\n    \n    if (result) {\n      commissions.value = result.list || [];\n      \n      // 更新分页信息\n      commissionPagination.current = result.pagination.current;\n      commissionPagination.total = result.pagination.total;\n      commissionPagination.totalPages = result.pagination.totalPages;\n    }\n  } catch (error) {\n    console.error('获取佣金记录列表失败', error);\n    uni.showToast({\n      title: '获取佣金记录列表失败',\n      icon: 'none'\n    });\n  } finally {\n    commissionLoading.value = false;\n  }\n};\n\n// 格式化日期时间\nconst formatDateTime = (dateString) => {\n  if (!dateString) return '未知';\n  \n  const date = new Date(dateString);\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  const hour = String(date.getHours()).padStart(2, '0');\n  const minute = String(date.getMinutes()).padStart(2, '0');\n  \n  return `${year}-${month}-${day} ${hour}:${minute}`;\n};\n\n// 获取佣金类型样式类\nconst getCommissionTypeClass = (type) => {\n  switch (type) {\n    case 'order':\n      return 'type-order';\n    case 'withdraw':\n      return 'type-withdraw';\n    case 'refund':\n      return 'type-refund';\n    case 'adjust':\n      return 'type-adjust';\n    default:\n      return 'type-other';\n  }\n};\n\n// 获取佣金类型文本\nconst getCommissionTypeText = (type) => {\n  switch (type) {\n    case 'order':\n      return '订单佣金';\n    case 'withdraw':\n      return '佣金提现';\n    case 'refund':\n      return '订单退款';\n    case 'adjust':\n      return '佣金调整';\n    default:\n      return '其他';\n  }\n};\n\n// 上一页佣金\nconst prevCommissionPage = () => {\n  if (commissionPagination.current > 1) {\n    commissionPagination.current--;\n    getCommissionRecords();\n  }\n};\n\n// 下一页佣金\nconst nextCommissionPage = () => {\n  if (commissionPagination.current < commissionPagination.totalPages) {\n    commissionPagination.current++;\n    getCommissionRecords();\n  }\n};\n\n// 获取提现记录列表\nconst getWithdrawRecords = async () => {\n  try {\n    withdrawLoading.value = true;\n    \n    const params = {\n      distributorId: distributorId.value,\n      page: withdrawPagination.current,\n      pageSize: withdrawPagination.pageSize\n    };\n    \n    const result = await distributionService.getWithdrawRecords(params);\n    \n    if (result) {\n      withdrawals.value = result.list || [];\n      \n      // 更新分页信息\n      withdrawPagination.current = result.pagination.current;\n      withdrawPagination.total = result.pagination.total;\n      withdrawPagination.totalPages = result.pagination.totalPages;\n    }\n  } catch (error) {\n    console.error('获取提现记录列表失败', error);\n    uni.showToast({\n      title: '获取提现记录列表失败',\n      icon: 'none'\n    });\n  } finally {\n    withdrawLoading.value = false;\n  }\n};\n\n// 获取提现状态样式类\nconst getWithdrawStatusClass = (status) => {\n  switch (status) {\n    case 'pending':\n      return 'status-pending';\n    case 'approved':\n      return 'status-approved';\n    case 'rejected':\n      return 'status-rejected';\n    case 'processing':\n      return 'status-processing';\n    case 'completed':\n      return 'status-completed';\n    case 'failed':\n      return 'status-failed';\n    default:\n      return '';\n  }\n};\n\n// 获取提现状态文本\nconst getWithdrawStatusText = (status) => {\n  switch (status) {\n    case 'pending':\n      return '待审核';\n    case 'approved':\n      return '已通过';\n    case 'rejected':\n      return '已拒绝';\n    case 'processing':\n      return '处理中';\n    case 'completed':\n      return '已完成';\n    case 'failed':\n      return '提现失败';\n    default:\n      return '未知';\n  }\n};\n\n// 获取提现方式文本\nconst getWithdrawMethodText = (method) => {\n  switch (method) {\n    case 'wechat':\n      return '微信零钱';\n    case 'alipay':\n      return '支付宝';\n    case 'bank':\n      return '银行卡';\n    default:\n      return '其他';\n  }\n};\n\n// 审核通过提现\nconst approveWithdrawal = (item) => {\n  uni.showModal({\n    title: '审核通过',\n    content: `确定通过此笔 ${formatAmount(item.amount)}元 的提现申请吗？`,\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.reviewWithdrawal({\n            id: item.id,\n            status: 'approved'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '审核通过成功',\n              icon: 'success'\n            });\n            \n            // 刷新提现列表\n            getWithdrawRecords();\n          } else {\n            uni.showModal({\n              title: '审核失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('审核失败', error);\n          uni.showToast({\n            title: '审核失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 拒绝提现\nconst rejectWithdrawal = (item) => {\n  uni.showModal({\n    title: '拒绝提现',\n    content: '请输入拒绝原因',\n    editable: true,\n    placeholderText: '请输入拒绝原因',\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          uni.showLoading({\n            title: '处理中...',\n            mask: true\n          });\n          \n          const result = await distributionService.reviewWithdrawal({\n            id: item.id,\n            status: 'rejected',\n            remark: res.content || '管理员拒绝'\n          });\n          \n          uni.hideLoading();\n          \n          if (result.success) {\n            uni.showToast({\n              title: '已拒绝提现',\n              icon: 'success'\n            });\n            \n            // 刷新提现列表\n            getWithdrawRecords();\n          } else {\n            uni.showModal({\n              title: '操作失败',\n              content: result.message || '请稍后再试',\n              showCancel: false\n            });\n          }\n        } catch (error) {\n          uni.hideLoading();\n          console.error('操作失败', error);\n          uni.showToast({\n            title: '操作失败',\n            icon: 'none'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 上一页提现\nconst prevWithdrawPage = () => {\n  if (withdrawPagination.current > 1) {\n    withdrawPagination.current--;\n    getWithdrawRecords();\n  }\n};\n\n// 下一页提现\nconst nextWithdrawPage = () => {\n  if (withdrawPagination.current < withdrawPagination.totalPages) {\n    withdrawPagination.current++;\n    getWithdrawRecords();\n  }\n};\n</script>\n\n<style lang=\"scss\">\n.detail-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 30rpx;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #A764CA, #6B0FBE);\n  color: #fff;\n  padding: 88rpx 32rpx 30rpx;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);\n}\n\n.navbar-back {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 24rpx;\n  height: 24rpx;\n  border-left: 4rpx solid #fff;\n  border-bottom: 4rpx solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 36rpx;\n  font-weight: 600;\n  letter-spacing: 1rpx;\n}\n\n.navbar-right {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 24rpx;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  font-weight: bold;\n}\n\n/* 基本信息卡片 */\n.info-card {\n  margin: 30rpx;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.distributor-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 60rpx;\n  margin-right: 30rpx;\n  background-color: #f5f5f5;\n}\n\n.info-content {\n  flex: 1;\n}\n\n.name-wrap {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-bottom: 16rpx;\n}\n\n.name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  margin-right: 16rpx;\n}\n\n.level-tag {\n  font-size: 24rpx;\n  color: #fff;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n  margin-right: 16rpx;\n}\n\n.status-tag {\n  font-size: 24rpx;\n  color: #fff;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.status-active {\n  background-color: #67C23A;\n}\n\n.status-disabled {\n  background-color: #909399;\n}\n\n.status-pending {\n  background-color: #E6A23C;\n}\n\n.status-rejected {\n  background-color: #F56C6C;\n}\n\n.phone {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.info-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0 -10rpx;\n  padding-bottom: 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.info-item {\n  width: 50%;\n  padding: 10rpx;\n  box-sizing: border-box;\n}\n\n.item-label {\n  font-size: 26rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n  display: block;\n}\n\n.item-value {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.action-btns {\n  display: flex;\n  justify-content: center;\n  margin-top: 30rpx;\n}\n\n.action-btn {\n  padding: 16rpx 30rpx;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  margin: 0 16rpx;\n}\n\n.action-btn.approve {\n  background-color: #67C23A;\n  color: #fff;\n}\n\n.action-btn.reject {\n  background-color: #F56C6C;\n  color: #fff;\n}\n\n.action-btn.disable {\n  background-color: #909399;\n  color: #fff;\n}\n\n.action-btn.enable {\n  background-color: #409EFF;\n  color: #fff;\n}\n\n.action-btn.set-level {\n  background-color: #6B0FBE;\n  color: #fff;\n}\n\n/* 数据概览 */\n.stats-card {\n  margin: 30rpx;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.card-header {\n  margin-bottom: 20rpx;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.stats-grid {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.stats-item {\n  width: 50%;\n  padding: 20rpx 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stats-value {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.stats-label {\n  font-size: 26rpx;\n  color: #999;\n}\n\n/* 选项卡 */\n.tabs {\n  display: flex;\n  background: #FFFFFF;\n  margin: 30rpx;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.tab-item {\n  flex: 1;\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  color: #666;\n  position: relative;\n}\n\n.tab-item.active {\n  color: #6B0FBE;\n  font-weight: 600;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40rpx;\n  height: 4rpx;\n  background-color: #6B0FBE;\n  border-radius: 2rpx;\n}\n\n/* 内容区域 */\n.tab-content {\n  margin: 0 30rpx 30rpx;\n}\n\n.placeholder {\n  height: 300rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.placeholder text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 订单列表 */\n.order-list {\n  margin-bottom: 30rpx;\n}\n\n.order-item {\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.order-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.order-id {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.order-status {\n  font-size: 28rpx;\n  color: #6B0FBE;\n}\n\n.product-info {\n  display: flex;\n  margin-bottom: 20rpx;\n}\n\n.product-image {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 10rpx;\n  margin-right: 20rpx;\n  background-color: #f5f5f5;\n}\n\n.product-detail {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.product-name {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.product-meta {\n  display: flex;\n  justify-content: space-between;\n}\n\n.product-price {\n  font-size: 28rpx;\n  color: #FF5722;\n}\n\n.product-quantity {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.order-footer {\n  padding-top: 20rpx;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.order-time {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 10rpx;\n}\n\n.order-amount {\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n}\n\n.amount {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 600;\n}\n\n.commission-info {\n  font-size: 26rpx;\n  color: #666;\n  display: flex;\n  align-items: center;\n}\n\n.commission {\n  font-size: 28rpx;\n  color: #FF5722;\n  font-weight: 600;\n  margin-right: 10rpx;\n}\n\n.commission-status {\n  font-size: 24rpx;\n  color: #fff;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.status-paid {\n  background-color: #67C23A;\n}\n\n.status-pending {\n  background-color: #E6A23C;\n}\n\n.status-frozen {\n  background-color: #409EFF;\n}\n\n.status-cancelled {\n  background-color: #F56C6C;\n}\n\n/* 团队成员列表 */\n.team-list {\n  margin-bottom: 30rpx;\n}\n\n.team-item {\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.member-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.member-avatar {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50rpx;\n  margin-right: 20rpx;\n  background-color: #f5f5f5;\n}\n\n.member-detail {\n  flex: 1;\n}\n\n.member-name-wrap {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.member-name {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n  margin-right: 16rpx;\n}\n\n.member-level {\n  font-size: 22rpx;\n  color: #fff;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.member-phone {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.member-stats {\n  display: flex;\n  justify-content: space-between;\n  padding: 20rpx 0;\n  border-top: 1rpx solid #f0f0f0;\n  border-bottom: 1rpx solid #f0f0f0;\n  margin-bottom: 20rpx;\n}\n\n.member-stat {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 8rpx;\n}\n\n.stat-value {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.member-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.join-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.arrow-right {\n  width: 16rpx;\n  height: 16rpx;\n  border-top: 2rpx solid #999;\n  border-right: 2rpx solid #999;\n  transform: rotate(45deg);\n}\n\n/* 佣金记录列表 */\n.commission-list {\n  margin-bottom: 30rpx;\n}\n\n.commission-item {\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.commission-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.commission-type {\n  font-size: 24rpx;\n  color: #fff;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n}\n\n.type-order {\n  background-color: #409EFF;\n}\n\n.type-withdraw {\n  background-color: #67C23A;\n}\n\n.type-refund {\n  background-color: #F56C6C;\n}\n\n.type-adjust {\n  background-color: #E6A23C;\n}\n\n.type-other {\n  background-color: #909399;\n}\n\n.commission-amount {\n  font-size: 32rpx;\n  font-weight: 600;\n}\n\n.commission-amount.income {\n  color: #67C23A;\n}\n\n.commission-amount.expense {\n  color: #F56C6C;\n}\n\n.commission-content {\n  padding: 20rpx 0;\n  border-top: 1rpx solid #f0f0f0;\n  border-bottom: 1rpx solid #f0f0f0;\n  margin-bottom: 20rpx;\n}\n\n.commission-desc {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.commission-order {\n  font-size: 26rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.commission-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.commission-footer {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.commission-status {\n  font-size: 24rpx;\n  color: #fff;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n/* 提现记录列表 */\n.withdraw-list {\n  margin-bottom: 30rpx;\n}\n\n.withdraw-item {\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.withdraw-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.withdraw-id {\n  font-size: 28rpx;\n  color: #333;\n}\n\n.withdraw-status {\n  font-size: 24rpx;\n  color: #fff;\n  padding: 4rpx 12rpx;\n  border-radius: 20rpx;\n}\n\n.status-pending {\n  background-color: #E6A23C;\n}\n\n.status-approved {\n  background-color: #409EFF;\n}\n\n.status-rejected {\n  background-color: #F56C6C;\n}\n\n.status-processing {\n  background-color: #909399;\n}\n\n.status-completed {\n  background-color: #67C23A;\n}\n\n.status-failed {\n  background-color: #F56C6C;\n}\n\n.withdraw-content {\n  padding: 20rpx 0;\n  border-top: 1rpx solid #f0f0f0;\n  border-bottom: 1rpx solid #f0f0f0;\n  margin-bottom: 20rpx;\n}\n\n.withdraw-info {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 16rpx;\n}\n\n.withdraw-info:last-child {\n  margin-bottom: 0;\n}\n\n.withdraw-label {\n  font-size: 26rpx;\n  color: #999;\n}\n\n.withdraw-amount,\n.withdraw-fee,\n.withdraw-actual {\n  font-size: 28rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.withdraw-method,\n.withdraw-account,\n.withdraw-time,\n.withdraw-remark {\n  font-size: 26rpx;\n  color: #666;\n  max-width: 70%;\n  text-align: right;\n}\n\n.withdraw-footer {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.withdraw-btn {\n  font-size: 24rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  margin-left: 16rpx;\n}\n\n.withdraw-btn.approve {\n  background-color: #67C23A;\n  color: #fff;\n}\n\n.withdraw-btn.reject {\n  background-color: #F56C6C;\n  color: #fff;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onMounted", "uni", "distributionService", "watch", "MiniProgramPage"], "mappings": ";;;;;;;AA2bA,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAG5B,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAG9B,UAAM,YAAYA,cAAAA,IAAI,QAAQ;AAG9B,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AAGrB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAG9B,UAAM,kBAAkBC,cAAAA,SAAS;AAAA,MAC/B,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,cAAcD,cAAAA,IAAI,CAAA,CAAE;AAG1B,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAG7B,UAAM,iBAAiBC,cAAAA,SAAS;AAAA,MAC9B,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,cAAcD,cAAAA,IAAI,CAAA,CAAE;AAG1B,UAAM,oBAAoBA,cAAAA,IAAI,KAAK;AAGnC,UAAM,uBAAuBC,cAAAA,SAAS;AAAA,MACpC,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,cAAcD,cAAAA,IAAI,CAAA,CAAE;AAG1B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AAGjC,UAAM,qBAAqBC,cAAAA,SAAS;AAAA,MAClC,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,IACd,CAAC;AAGDC,kBAAAA,UAAU,MAAM;;AAEd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,YAAU,iBAAY,UAAZ,mBAAmB,YAAW,CAAA;AAG9C,oBAAc,QAAQ,QAAQ,MAAM;AAGpC,UAAI,cAAc,OAAO;AACvB;MACJ,OAAS;AACLC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,mBAAW,MAAM;AACf;QACD,GAAE,IAAI;AAAA,MACR;AAAA,IACH,CAAC;AAGD,UAAM,uBAAuB,YAAY;AACvC,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,SAAS,MAAMC,0BAAmB,oBAAC,qBAAqB,cAAc,KAAK;AAEjFD,sBAAG,MAAC,YAAW;AAEf,YAAI,QAAQ;AACV,0BAAgB,QAAQ;AAAA,QAC9B,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,mGAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,WAAW;AAC/B,cAAQ,UAAU,GAAG,QAAQ,CAAC;AAAA,IAChC;AAGA,UAAM,aAAa,CAAC,eAAe;AACjC,UAAI,CAAC;AAAY,eAAO;AAExB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAElD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,gBAAgB,MAAM,IAAI;AAAA,QAC3C,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMC,0BAAmB,oBAAC,6BAA6B;AAAA,gBACpE,IAAI,cAAc;AAAA,gBAClB,QAAQ;AAAA,cACpB,CAAW;AAEDD,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,mGAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,gBAAgB,MAAM,IAAI;AAAA,QAC3C,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMC,0BAAmB,oBAAC,6BAA6B;AAAA,gBACpE,IAAI,cAAc;AAAA,gBAClB,QAAQ;AAAA,cACpB,CAAW;AAEDD,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,mGAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,gBAAgB,MAAM,IAAI;AAAA,QAC3C,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMC,0BAAmB,oBAAC,wBAAwB;AAAA,gBAC/D,IAAI,cAAc;AAAA,gBAClB,QAAQ;AAAA,cACpB,CAAW;AAEDD,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,mGAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,QAAQ,gBAAgB,MAAM,IAAI;AAAA,QAC3C,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMC,0BAAmB,oBAAC,wBAAwB;AAAA,gBAC/D,IAAI,cAAc;AAAA,gBAClB,QAAQ;AAAA,cACpB,CAAW;AAEDD,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,mGAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,mFAAmF,cAAc,KAAK;AAAA,MAC/G,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACfA,oBAAG,MAAC,aAAY;AAAA,IACtB;AAGA,UAAM,WAAW,MAAM;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACX,SAAS;AAAA,QACL,YAAY;AAAA,MACpB,CAAO;AAAA,IACP;AAGAE,kBAAAA,MAAM,WAAW,CAAC,WAAW;AAC3B,UAAI,WAAW,YAAY,OAAO,MAAM,WAAW,GAAG;AACpD;MACJ,WAAa,WAAW,UAAU,YAAY,MAAM,WAAW,GAAG;AAC9D;MACJ,WAAa,WAAW,gBAAgB,YAAY,MAAM,WAAW,GAAG;AACpE;MACJ,WAAa,WAAW,cAAc,YAAY,MAAM,WAAW,GAAG;AAClE;MACD;AAAA,IACH,CAAC;AAGD,UAAM,wBAAwB,YAAY;AACxC,UAAI;AACF,qBAAa,QAAQ;AAErB,cAAM,SAAS;AAAA,UACb,eAAe,cAAc;AAAA,UAC7B,MAAM,gBAAgB;AAAA,UACtB,UAAU,gBAAgB;AAAA,QAChC;AAEI,cAAM,SAAS,MAAMD,0BAAAA,oBAAoB,sBAAsB,MAAM;AAErE,YAAI,QAAQ;AACV,iBAAO,QAAQ,OAAO,QAAQ,CAAA;AAG9B,0BAAgB,UAAU,OAAO,WAAW;AAC5C,0BAAgB,QAAQ,OAAO,WAAW;AAC1C,0BAAgB,aAAa,OAAO,WAAW;AAAA,QAChD;AAAA,MACF,SAAQ,OAAO;AACdD,sBAAc,MAAA,MAAA,SAAA,mGAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;AAGA,UAAM,2BAA2B,CAAC,WAAW;AAC3C,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,0BAA0B,CAAC,WAAW;AAC1C,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,UAAI,gBAAgB,UAAU,GAAG;AAC/B,wBAAgB;AAChB;MACD;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,UAAI,gBAAgB,UAAU,gBAAgB,YAAY;AACxD,wBAAgB;AAChB;MACD;AAAA,IACH;AAGA,UAAM,iBAAiB,YAAY;AACjC,UAAI;AACF,oBAAY,QAAQ;AAEpB,cAAM,SAAS;AAAA,UACb,eAAe,cAAc;AAAA,UAC7B,MAAM,eAAe;AAAA,UACrB,UAAU,eAAe;AAAA,QAC/B;AAEI,cAAM,SAAS,MAAMC,0BAAAA,oBAAoB,eAAe,MAAM;AAE9D,YAAI,QAAQ;AACV,sBAAY,QAAQ,OAAO,QAAQ,CAAA;AAGnC,yBAAe,UAAU,OAAO,WAAW;AAC3C,yBAAe,QAAQ,OAAO,WAAW;AACzC,yBAAe,aAAa,OAAO,WAAW;AAAA,QAC/C;AAAA,MACF,SAAQ,OAAO;AACdD,sBAAc,MAAA,MAAA,SAAA,mGAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,oBAAY,QAAQ;AAAA,MACrB;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4FAA4F,KAAK,EAAE;AAAA,MAC5G,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,eAAe,UAAU,GAAG;AAC9B,uBAAe;AACf;MACD;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,eAAe,UAAU,eAAe,YAAY;AACtD,uBAAe;AACf;MACD;AAAA,IACH;AAGA,UAAM,uBAAuB,YAAY;AACvC,UAAI;AACF,0BAAkB,QAAQ;AAE1B,cAAM,SAAS;AAAA,UACb,eAAe,cAAc;AAAA,UAC7B,MAAM,qBAAqB;AAAA,UAC3B,UAAU,qBAAqB;AAAA,QACrC;AAEI,cAAM,SAAS,MAAMC,0BAAAA,oBAAoB,kBAAkB,MAAM;AAEjE,YAAI,QAAQ;AACV,sBAAY,QAAQ,OAAO,QAAQ,CAAA;AAGnC,+BAAqB,UAAU,OAAO,WAAW;AACjD,+BAAqB,QAAQ,OAAO,WAAW;AAC/C,+BAAqB,aAAa,OAAO,WAAW;AAAA,QACrD;AAAA,MACF,SAAQ,OAAO;AACdD,sBAAc,MAAA,MAAA,SAAA,mGAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,0BAAkB,QAAQ;AAAA,MAC3B;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,eAAe;AACrC,UAAI,CAAC;AAAY,eAAO;AAExB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,YAAM,OAAO,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACpD,YAAM,SAAS,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAExD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAAA,IAClD;AAGA,UAAM,yBAAyB,CAAC,SAAS;AACvC,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,wBAAwB,CAAC,SAAS;AACtC,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/B,UAAI,qBAAqB,UAAU,GAAG;AACpC,6BAAqB;AACrB;MACD;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/B,UAAI,qBAAqB,UAAU,qBAAqB,YAAY;AAClE,6BAAqB;AACrB;MACD;AAAA,IACH;AAGA,UAAM,qBAAqB,YAAY;AACrC,UAAI;AACF,wBAAgB,QAAQ;AAExB,cAAM,SAAS;AAAA,UACb,eAAe,cAAc;AAAA,UAC7B,MAAM,mBAAmB;AAAA,UACzB,UAAU,mBAAmB;AAAA,QACnC;AAEI,cAAM,SAAS,MAAMC,0BAAAA,oBAAoB,mBAAmB,MAAM;AAElE,YAAI,QAAQ;AACV,sBAAY,QAAQ,OAAO,QAAQ,CAAA;AAGnC,6BAAmB,UAAU,OAAO,WAAW;AAC/C,6BAAmB,QAAQ,OAAO,WAAW;AAC7C,6BAAmB,aAAa,OAAO,WAAW;AAAA,QACnD;AAAA,MACF,SAAQ,OAAO;AACdD,sBAAc,MAAA,MAAA,SAAA,oGAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,wBAAgB,QAAQ;AAAA,MACzB;AAAA,IACH;AAGA,UAAM,yBAAyB,CAAC,WAAW;AACzC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,wBAAwB,CAAC,WAAW;AACxC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,wBAAwB,CAAC,WAAW;AACxC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,UAAU,aAAa,KAAK,MAAM,CAAC;AAAA,QAC5C,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMC,0BAAmB,oBAAC,iBAAiB;AAAA,gBACxD,IAAI,KAAK;AAAA,gBACT,QAAQ;AAAA,cACpB,CAAW;AAEDD,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,oGAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,SAAS;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,oBAAM,SAAS,MAAMC,0BAAmB,oBAAC,iBAAiB;AAAA,gBACxD,IAAI,KAAK;AAAA,gBACT,QAAQ;AAAA,gBACR,QAAQ,IAAI,WAAW;AAAA,cACnC,CAAW;AAEDD,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS;AAClBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD;cACZ,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,SAAS,OAAO,WAAW;AAAA,kBAC3B,YAAY;AAAA,gBAC1B,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,oGAAc,QAAQ,KAAK;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAC7B,UAAI,mBAAmB,UAAU,GAAG;AAClC,2BAAmB;AACnB;MACD;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAC7B,UAAI,mBAAmB,UAAU,mBAAmB,YAAY;AAC9D,2BAAmB;AACnB;MACD;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChvCA,GAAG,WAAWG,SAAe;"}