
/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 20px;
  height: 20px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #FFFFFF; /* 修改为白色文字 */
}
.navbar-right {
  width: 60px;
}
.education-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 110rpx;
}
.education-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 课程基本信息卡片 */
.course-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.course-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.course-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}
.course-meta {
  margin-bottom: 24rpx;
}
.course-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}
.course-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}
.course-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.course-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}
.course-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.course-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}
.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 课程内容 */
.content-list {
  display: flex;
  flex-direction: column;
}
.content-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.content-item:last-child {
  border-bottom: none;
}
.content-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.content-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 课程安排 */
.schedule-list {
  display: flex;
  flex-direction: column;
}
.schedule-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.schedule-item:last-child {
  border-bottom: none;
}
.schedule-time {
  width: 200rpx;
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 500;
}
.schedule-info {
  flex: 1;
}
.schedule-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.schedule-desc {
  font-size: 26rpx;
  color: #666;
}

/* 教学特色 */
.features-list {
  display: flex;
  flex-direction: column;
}
.feature-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.feature-item:last-child {
  border-bottom: none;
}
.feature-icon {
  font-size: 40rpx;
  color: #1890ff;
  margin-right: 20rpx;
}
.feature-info {
  flex: 1;
}
.feature-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.feature-desc {
  font-size: 26rpx;
  color: #666;
}

/* 教师信息 */
.teacher-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.teacher-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}
.teacher-avatar image {
  width: 100%;
  height: 100%;
}
.teacher-info {
  flex: 1;
}
.teacher-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.teacher-meta {
  display: flex;
  align-items: center;
}
.teacher-title, .teacher-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}
.teacher-intro {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.intro-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 相似课程推荐样式优化 */
.similar-courses-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.related-courses-content {
  padding: 0 16px 16px;
  overflow: hidden;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 10px;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 3px;
  height: 16px;
  background-color: #0052CC;
  border-radius: 3px;
}

/* 相关课程列表样式 */
.related-courses-list {
  margin-bottom: 12px;
}
.related-course-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}
.related-course-item:last-child {
  border-bottom: none;
}
.course-item-content {
  display: flex;
  align-items: center;
}
.course-item-left {
  margin-right: 12px;
}
.course-image {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  background-color: #f5f7fa;
  object-fit: cover;
}
.course-item-middle {
  flex: 1;
}
.course-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}
.course-item-target {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}
.course-item-tags {
  display: flex;
  flex-wrap: wrap;
}
.course-item-tag {
  font-size: 12px;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  margin-right: 8px;
  margin-bottom: 4px;
}
.course-item-tag-more {
  font-size: 12px;
  color: #999;
}
.course-item-right {
  text-align: right;
  margin-left: 10px;
}
.course-item-price {
  font-size: 16px;
  font-weight: 500;
  color: #ff4d4f;
}

/* 查看更多按钮 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  border-top: 1px solid #f5f5f5;
}
.view-more-text {
  font-size: 14px;
  color: #1890ff;
  margin-right: 4px;
}
.view-more-icon {
  font-size: 12px;
  color: #1890ff;
}

/* 空状态 */
.empty-related-courses {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-image {
  width: 100px;
  height: 100px;
  margin-bottom: 10px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 底部操作栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.toolbar-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}
.toolbar-text {
  font-size: 20rpx;
  color: #666;
}
.call-button {
  flex: 2;
  background-color: #1890ff;
  color: #fff;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.call-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
}
.call-subtitle {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4rpx;
}
.share-button {
  background-color: transparent;
  padding: 0;
  margin: 0;
  line-height: normal;
  border: none;
}
.share-button::after {
  border: none;
}
.hidden-share-btn {
  position: absolute;
  top: -999px;
  left: -999px;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}
