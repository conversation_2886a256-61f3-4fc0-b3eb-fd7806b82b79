{"version": 3, "file": "api.js", "sources": ["mock/api.js"], "sourcesContent": ["// 导入各模块的API\nimport * as newsApi from './news/index';\nimport * as infoApi from './info/index';\nimport * as commonApi from './common/index';\nimport * as businessApi from './business/index';\nimport * as publishApi from './publish/index';\nimport * as userApi from './user/index';\nimport * as activityApi from './activity/index';\nimport * as paymentApi from './payment/index';\nimport * as serviceApi from './service/index';\n\n// 导入服务相关API\nimport { fetchServiceList, fetchServiceCategories, fetchHotServices } from './service/serviceList';\nimport { fetchPublishList, fetchPublishDetail } from './service/publishList';\n\n// 创建一个新的serviceApi对象，而不是修改导入的对象\nconst extendedServiceApi = {\n  ...serviceApi,\n  fetchPublishList,\n  fetchPublishDetail\n};\n\n// 统一导出API服务\nexport default {\n  // 新闻相关API\n  news: {\n    getCategories: () => newsApi.newsCategories,\n    getList: newsApi.fetchNewsList,\n    getDetail: newsApi.fetchNewsDetail,\n    getMoreComments: newsApi.fetchMoreComments\n  },\n  \n  // 信息相关API\n  info: {\n    getCategories: () => infoApi.infoCategories,\n    getVisibleCategories: () => infoApi.visibleCategories,\n    getTopped: infoApi.fetchToppedInfo,\n    getAll: infoApi.fetchAllInfo,\n    getByCategory: infoApi.fetchInfoByCategory\n  },\n  \n  // 商圈相关API\n  business: {\n    getCategories: businessApi.fetchBusinessCategories,\n    getShopList: businessApi.fetchShopList,\n    getShopDetail: businessApi.fetchShopDetail\n  },\n  \n  // 发布相关API\n  publish: {\n    // 生意转让\n    getBusinessTransferDetail: publishApi.fetchBusinessTransferDetail,\n    getRelatedShops: publishApi.fetchRelatedShops,\n    \n    // 房屋出租\n    getHouseRentDetail: publishApi.fetchHouseRentDetail,\n    getRelatedHouses: publishApi.fetchRelatedHouses,\n    \n    // 招聘信息\n    getJobDetail: publishApi.fetchJobDetail,\n    getRelatedJobs: publishApi.fetchRelatedJobs,\n    \n    // 房屋出售\n    getHouseSaleDetail: publishApi.fetchHouseSaleDetail,\n    getRelatedHouseSales: publishApi.fetchRelatedHouseSales,\n    \n    // 二手车辆\n    getCarDetail: publishApi.fetchCarDetail,\n    getRelatedCars: publishApi.fetchRelatedCars,\n    \n    // 二手闲置\n    getSecondHandDetail: publishApi.fetchSecondHandDetail,\n    getRelatedSecondHands: publishApi.fetchRelatedSecondHands\n  },\n  \n  // 用户相关API\n  user: {\n    // 用户资料相关\n    getProfile: userApi.fetchUserProfile,\n    getStats: userApi.fetchUserStats,\n    updateProfile: userApi.updateUserProfile,\n    \n    // 用户设置相关\n    getSettings: userApi.fetchUserSettings,\n    updateSettings: userApi.updateUserSettings,\n    \n    // 用户认证相关\n    login: userApi.login,\n    register: userApi.register,\n    getVerificationCode: userApi.getVerificationCode,\n    logout: userApi.logout,\n    refreshToken: userApi.refreshAuthToken\n  },\n  \n  // 活动相关API\n  activity: {\n    // 活动列表相关\n    getCategories: activityApi.fetchActivityCategories,\n    getList: activityApi.fetchActivityList,\n    \n    // 活动详情相关\n    getDetail: activityApi.fetchActivityDetail,\n    getRelated: activityApi.fetchRelatedActivities,\n    getMoreComments: activityApi.fetchMoreComments,\n    \n    // 活动参与相关\n    participate: activityApi.participateActivity,\n    cancelParticipation: activityApi.cancelParticipation\n  },\n  \n  // 支付相关API\n  payment: {\n    // 钱包相关\n    getWalletInfo: paymentApi.fetchWalletInfo,\n    recharge: paymentApi.rechargeWallet,\n    withdraw: paymentApi.withdrawWallet,\n    addBankCard: paymentApi.addBankCard,\n    setDefaultBankCard: paymentApi.setDefaultBankCard,\n    \n    // 交易记录相关\n    getTransactionList: paymentApi.fetchTransactionList,\n    getTransactionDetail: paymentApi.fetchTransactionDetail,\n    getTransactionStats: paymentApi.fetchTransactionStats\n  },\n  \n  // 服务相关API\n  service: {\n    // 服务列表相关\n    getCategories: serviceApi.fetchServiceCategories,\n    getList: serviceApi.fetchServiceList,\n    getHotServices: serviceApi.fetchHotServices,\n    \n    // 服务详情相关\n    getDetail: serviceApi.fetchServiceDetail,\n    getRelated: serviceApi.fetchRelatedServices,\n    getMoreReviews: serviceApi.fetchMoreReviews,\n    \n    // 服务收藏相关\n    collect: serviceApi.collectService,\n    cancelCollect: serviceApi.cancelCollectService,\n    \n    // 发布列表相关API\n    fetchPublishList,\n    fetchPublishDetail\n  },\n  \n  // 广告相关API\n  ad: {\n    getBanner: commonApi.fetchAdBanner\n  }\n};\n\n// 导出扩展后的serviceApi\nexport { extendedServiceApi as serviceApi }; "], "names": ["serviceApi", "fetchPublishList", "fetchPublishDetail", "newsApi.newsCategories", "newsApi.fetchNewsList", "newsApi.fetchNewsDetail", "newsApi.fetchMoreComments", "infoApi.infoCategories", "infoApi.visibleCategories", "infoApi.fetchToppedInfo", "infoApi.fetchAllInfo", "infoApi.fetchInfoByCategory", "businessApi.fetchBusinessCategories", "businessApi.fetchShopList", "businessApi.fetchShopDetail", "publishApi.fetchBusinessTransferDetail", "publishApi.fetchRelatedShops", "publishApi.fetchHouseRentDetail", "publishApi.fetchRelatedHouses", "publishApi.fetchJobDetail", "publishApi.fetchRelatedJobs", "publishApi.fetchHouseSaleDetail", "publishApi.fetchRelatedHouseSales", "publishApi.fetchCarDetail", "publishApi.fetchRelatedCars", "publishApi.fetchSecondHandDetail", "publishApi.fetchRelatedSecondHands", "userApi.fetchUserProfile", "userApi.fetchUserStats", "userApi.updateUserProfile", "userApi.fetchUserSettings", "userApi.updateUserSettings", "userApi.login", "userApi.register", "userApi.getVerificationCode", "userApi.logout", "userApi.refreshAuthToken", "activityApi.fetchActivityCategories", "activityApi.fetchActivityList", "activityApi.fetchActivityDetail", "activityApi.fetchRelatedActivities", "activityApi.fetchMoreComments", "activityApi.participateActivity", "activityApi.cancelParticipation", "paymentApi.fetchWalletInfo", "paymentApi.rechargeWallet", "paymentApi.withdrawWallet", "paymentApi.addBankCard", "paymentApi.setDefaultBankCard", "paymentApi.fetchTransactionList", "paymentApi.fetchTransactionDetail", "paymentApi.fetchTransactionStats", "serviceApi.fetchServiceCategories", "serviceApi.fetchServiceList", "serviceApi.fetchHotServices", "serviceApi.fetchServiceDetail", "serviceApi.fetchRelatedServices", "serviceApi.fetchMoreReviews", "serviceApi.collectService", "serviceApi.cancelCollectService", "commonApi.fetchAdBanner"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBK,MAAC,qBAAqB;AAAA,EACzB,GAAGA,mBAAU;AAAA,EACf,kBAAEC,yBAAgB;AAAA,EAClB,oBAAEC,yBAAkB;AACpB;AAGA,MAAe,UAAA;AAAA;AAAA,EAEb,MAAM;AAAA,IACJ,eAAe,MAAMC,gBAAsB;AAAA,IAC3C,SAASC,mBAAqB;AAAA,IAC9B,WAAWC,qBAAuB;AAAA,IAClC,iBAAiBC,qBAAyB;AAAA,EAC3C;AAAA;AAAA,EAGD,MAAM;AAAA,IACJ,eAAe,MAAMC,qBAAsB;AAAA,IAC3C,sBAAsB,MAAMC,qBAAyB;AAAA,IACrD,WAAWC,qBAAuB;AAAA,IAClC,QAAQC,qBAAoB;AAAA,IAC5B,eAAeC,qBAA2B;AAAA,EAC3C;AAAA;AAAA,EAGD,UAAU;AAAA,IACR,eAAeC,yBAAmC;AAAA,IAClD,aAAaC,uBAAyB;AAAA,IACtC,eAAeC,uBAA2B;AAAA,EAC3C;AAAA;AAAA,EAGD,SAAS;AAAA;AAAA,IAEP,2BAA2BC,8BAAsC;AAAA,IACjE,iBAAiBC,8BAA4B;AAAA;AAAA,IAG7C,oBAAoBC,uBAA+B;AAAA,IACnD,kBAAkBC,uBAA6B;AAAA;AAAA,IAG/C,cAAcC,qBAAyB;AAAA,IACvC,gBAAgBC,qBAA2B;AAAA;AAAA,IAG3C,oBAAoBC,uBAA+B;AAAA,IACnD,sBAAsBC,uBAAiC;AAAA;AAAA,IAGvD,cAAcC,qBAAyB;AAAA,IACvC,gBAAgBC,qBAA2B;AAAA;AAAA,IAG3C,qBAAqBC,wBAAgC;AAAA,IACrD,uBAAuBC,wBAAkC;AAAA,EAC1D;AAAA;AAAA,EAGD,MAAM;AAAA;AAAA,IAEJ,YAAYC,kBAAwB;AAAA,IACpC,UAAUC,kBAAsB;AAAA,IAChC,eAAeC,kBAAyB;AAAA;AAAA,IAGxC,aAAaC,mBAAyB;AAAA,IACtC,gBAAgBC,mBAA0B;AAAA;AAAA,IAG1C,OAAOC,eAAa;AAAA,IACpB,UAAUC,eAAgB;AAAA,IAC1B,qBAAqBC,eAA2B;AAAA,IAChD,QAAQC,eAAc;AAAA,IACtB,cAAcC,eAAwB;AAAA,EACvC;AAAA;AAAA,EAGD,UAAU;AAAA;AAAA,IAER,eAAeC,2BAAmC;AAAA,IAClD,SAASC,2BAA6B;AAAA;AAAA,IAGtC,WAAWC,6BAA+B;AAAA,IAC1C,YAAYC,6BAAkC;AAAA,IAC9C,iBAAiBC,6BAA6B;AAAA;AAAA,IAG9C,aAAaC,6BAA+B;AAAA,IAC5C,qBAAqBC,6BAA+B;AAAA,EACrD;AAAA;AAAA,EAGD,SAAS;AAAA;AAAA,IAEP,eAAeC,oBAA0B;AAAA,IACzC,UAAUC,oBAAyB;AAAA,IACnC,UAAUC,oBAAyB;AAAA,IACnC,aAAaC,oBAAsB;AAAA,IACnC,oBAAoBC,oBAA6B;AAAA;AAAA,IAGjD,oBAAoBC,yBAA+B;AAAA,IACnD,sBAAsBC,yBAAiC;AAAA,IACvD,qBAAqBC,yBAAgC;AAAA,EACtD;AAAA;AAAA,EAGD,SAAS;AAAA;AAAA,IAEP,eAAeC,yBAAiC;AAAA,IAChD,SAASC,yBAA2B;AAAA,IACpC,gBAAgBC,yBAA2B;AAAA;AAAA,IAG3C,WAAWC,2BAA6B;AAAA,IACxC,YAAYC,2BAA+B;AAAA,IAC3C,gBAAgBC,2BAA2B;AAAA;AAAA,IAG3C,SAASC,2BAAyB;AAAA,IAClC,eAAeC,2BAA+B;AAAA;AAAA,IAGlD,kBAAI1D,yBAAgB;AAAA,IACpB,oBAAIC,yBAAkB;AAAA,EACnB;AAAA;AAAA,EAGD,IAAI;AAAA,IACF,WAAW0D,qBAAuB;AAAA,EACnC;AACH;;;"}