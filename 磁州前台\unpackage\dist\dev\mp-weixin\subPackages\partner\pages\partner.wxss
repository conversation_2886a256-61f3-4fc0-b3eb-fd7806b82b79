/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-338ed979, html.data-v-338ed979, #app.data-v-338ed979, .index-container.data-v-338ed979 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.partner-container.data-v-338ed979 {
  padding-bottom: 30rpx;
  min-height: 100vh;
  background-color: #F6F8FB;
}
.safe-area.data-v-338ed979 {
  height: 120rpx;
  width: 100%;
}
.safe-area-top.data-v-338ed979 {
  height: 180rpx;
  width: 100%;
}
.partner-card.data-v-338ed979 {
  margin: 30rpx;
  border-radius: 20rpx;
  background: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(22, 119, 255, 0.08);
  border: 1px solid rgba(22, 119, 255, 0.1);
  transition: all 0.3s ease;
}
.partner-card.data-v-338ed979:hover, .partner-card.data-v-338ed979:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 24rpx rgba(22, 119, 255, 0.12);
}
.partner-header.data-v-338ed979 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  position: relative;
}
.partner-header.data-v-338ed979::after {
  content: "";
  position: absolute;
  bottom: -15rpx;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background: #1677FF;
  border-radius: 2rpx;
}
.partner-header .title.data-v-338ed979 {
  font-size: 34rpx;
  font-weight: 700;
  color: #333333;
}
.partner-header .more.data-v-338ed979 {
  font-size: 26rpx;
  color: #1677FF;
  display: flex;
  align-items: center;
}
.partner-header .more .cuIcon-right.data-v-338ed979 {
  font-size: 24rpx;
  margin-left: 4rpx;
}
.user-card.data-v-338ed979 {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #64B5FF, #A0D2FF);
  margin: 30rpx 30rpx 30rpx;
  /* 调整顶部边距 */
  border-radius: 20rpx;
  color: #FFFFFF;
  box-shadow: 0 10rpx 30rpx rgba(22, 119, 255, 0.3);
}
.user-card .partner-avatar.data-v-338ed979 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #FFFFFF;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  background-color: #FFFFFF;
}
.user-card .user-info.data-v-338ed979 {
  flex: 1;
}
.user-card .user-info .nickname.data-v-338ed979 {
  font-size: 38rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.user-card .user-info .level-info.data-v-338ed979 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.user-card .user-info .level-info .level-icon.data-v-338ed979 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.user-card .user-info .level-info .level-name.data-v-338ed979 {
  font-size: 28rpx;
}
.user-card .user-info .progress-bar.data-v-338ed979 {
  height: 12rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 6rpx;
  overflow: hidden;
}
.user-card .user-info .progress-bar .progress.data-v-338ed979 {
  height: 100%;
  background-color: #FFFFFF;
  border-radius: 6rpx;
  transition: width 0.8s ease;
}
.user-card .partner-upgrade.data-v-338ed979 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14rpx 28rpx;
  background: linear-gradient(90deg, #1677FF, #4F9DFF);
  border-radius: 30rpx;
  margin-left: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}
.user-card .partner-upgrade.data-v-338ed979:active {
  transform: scale(0.95);
}
.user-card .partner-upgrade .upgrade-text.data-v-338ed979 {
  font-size: 26rpx;
  color: #FFFFFF;
  text-align: center;
  font-weight: 700;
}
.income-card.data-v-338ed979 {
  margin-bottom: 30rpx;
}
.income-card .income-header.data-v-338ed979 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.income-card .income-header .title.data-v-338ed979 {
  font-size: 34rpx;
  font-weight: 700;
  color: #333333;
}
.income-card .income-header .total-income.data-v-338ed979 {
  font-size: 28rpx;
  color: #666666;
}
.income-card .income-header .total-income text.data-v-338ed979 {
  color: #FF6B00;
  font-weight: 600;
  margin-left: 10rpx;
  font-size: 32rpx;
}
.income-card .data-row.data-v-338ed979 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.income-card .data-row .data-item.data-v-338ed979 {
  flex: 1;
  text-align: center;
  position: relative;
}
.income-card .data-row .data-item.data-v-338ed979::after {
  content: "";
  position: absolute;
  bottom: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 3rpx;
  background: linear-gradient(90deg, transparent, #1677FF, transparent);
}
.income-card .chart-container.data-v-338ed979 {
  height: 260rpx;
  width: 100%;
  position: relative;
  margin-top: 20rpx;
  background-color: #FFFFFF;
  padding: 20rpx 10rpx 0;
}
.income-card .chart-bars.data-v-338ed979 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  height: 200rpx;
}
.income-card .chart-bar.data-v-338ed979 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  height: 100%;
}
.income-card .bar-value.data-v-338ed979 {
  font-size: 20rpx;
  color: #666666;
  margin-bottom: 4rpx;
}
.income-card .bar-item.data-v-338ed979 {
  width: 30rpx;
  background: linear-gradient(180deg, #1677FF, #64B5FF);
  border-radius: 15rpx 15rpx 0 0;
}
.income-card .bar-date.data-v-338ed979 {
  margin-top: 10rpx;
  font-size: 22rpx;
  color: #999999;
}
.data-value.data-v-338ed979 {
  font-size: 42rpx;
  font-weight: 700 !important;
  color: #1677FF !important;
  margin-bottom: 10rpx;
  letter-spacing: 1rpx;
}
.data-label.data-v-338ed979 {
  font-size: 24rpx;
  color: #76ABFF !important;
  font-weight: 400 !important;
}
.function-card .function-list.data-v-338ed979 {
  display: flex;
  flex-wrap: wrap;
}
.function-card .function-list .function-item.data-v-338ed979 {
  width: 33.33%;
  text-align: center;
  padding: 30rpx 0;
  transition: all 0.3s ease;
}
.function-card .function-list .function-item.data-v-338ed979:active {
  transform: scale(0.95);
  background-color: rgba(22, 119, 255, 0.05);
  border-radius: 10rpx;
}
.function-card .function-list .function-item .icon-block.data-v-338ed979 {
  width: 90rpx;
  height: 90rpx;
  margin-bottom: 16rpx;
  transition: transform 0.3s ease;
  border-radius: 24rpx;
  margin: 0 auto 16rpx;
}
.function-card .function-list .function-item:active .icon-block.data-v-338ed979 {
  transform: scale(1.1);
}
.function-card .function-list .function-item .name.data-v-338ed979 {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}
.level-card .level-benefits.data-v-338ed979 {
  margin-top: 20rpx;
}
.level-card .level-benefits .benefit-item.data-v-338ed979 {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #F5F5F5;
}
.level-card .level-benefits .benefit-item.data-v-338ed979:last-child {
  border-bottom: none;
}
.level-card .level-benefits .benefit-item .icon.data-v-338ed979 {
  width: 70rpx;
  height: 70rpx;
  margin-right: 20rpx;
  background-color: rgba(22, 119, 255, 0.1);
  padding: 12rpx;
  border-radius: 50%;
}
.level-card .level-benefits .benefit-item .info.data-v-338ed979 {
  flex: 1;
}
.level-card .level-benefits .benefit-item .info .name.data-v-338ed979 {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 8rpx;
  font-weight: 500;
}
.level-card .level-benefits .benefit-item .info .desc.data-v-338ed979 {
  font-size: 24rpx;
  color: #999999;
}
.level-card .level-benefits .benefit-item .rate.data-v-338ed979 {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF6B00;
  background-color: rgba(255, 107, 0, 0.1);
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
}
.promotion-card .promotion-data.data-v-338ed979 {
  display: flex;
  flex-wrap: wrap;
}
.promotion-card .promotion-data .data-item.data-v-338ed979 {
  width: 50%;
  padding: 24rpx 0;
  position: relative;
}
.promotion-card .promotion-data .data-item.data-v-338ed979::after {
  content: "";
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 3rpx;
  background: linear-gradient(90deg, transparent, #1677FF, transparent);
}
.promotion-card .promotion-data .data-item .cuIcon-info.data-v-338ed979 {
  margin-left: 6rpx;
  color: #76ABFF;
}
.summary-items.data-v-338ed979 {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.summary-items .summary-item.data-v-338ed979 {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.summary-items .summary-item .item-title.data-v-338ed979 {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 10rpx;
}
.summary-items .summary-item .item-value.data-v-338ed979 {
  font-size: 38rpx;
  font-weight: 700;
  color: #1677FF;
}

/* 增强文本样式 */
.text-bold.data-v-338ed979 {
  font-weight: 700 !important;
}

/* 导航栏样式 */
.navbar.data-v-338ed979 {
  position: relative;
  z-index: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义导航栏 */
.custom-navbar.data-v-338ed979 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px;
  /* 状态栏高度 */
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  /* 改为与发布页一致的渐变角度 */
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100;
  /* 提高z-index确保在最上层 */
}
.navbar-title.data-v-338ed979 {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}
.navbar-right.data-v-338ed979 {
  width: 40rpx;
  height: 40rpx;
}
.navbar-left.data-v-338ed979 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20;
  /* 确保在标题上层，可以被点击 */
}
.back-icon.data-v-338ed979 {
  width: 100%;
  height: 100%;
}