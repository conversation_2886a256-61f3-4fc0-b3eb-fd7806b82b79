{"version": 3, "file": "index.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/verification/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1x2ZXJpZmljYXRpb25caW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"verification-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">核销中心</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 核销功能模块 -->\n    <view class=\"verification-modules\">\n      <!-- 扫码核销 -->\n      <view class=\"module-card\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/verification/scan')\">\n        <view class=\"module-icon scan\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"></rect>\n            <line x1=\"3\" y1=\"9\" x2=\"21\" y2=\"9\"></line>\n            <line x1=\"3\" y1=\"15\" x2=\"21\" y2=\"15\"></line>\n            <line x1=\"9\" y1=\"3\" x2=\"9\" y2=\"21\"></line>\n            <line x1=\"15\" y1=\"3\" x2=\"15\" y2=\"21\"></line>\n          </svg>\n        </view>\n        <view class=\"module-content\">\n          <text class=\"module-title\">扫码核销</text>\n          <text class=\"module-desc\">扫描用户核销码完成核销</text>\n        </view>\n        <view class=\"module-arrow\">\n          <view class=\"arrow-icon\"></view>\n        </view>\n      </view>\n      \n      <!-- 手动核销 -->\n      <view class=\"module-card\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/verification/manual')\">\n        <view class=\"module-icon manual\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"></path>\n            <path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"></path>\n          </svg>\n        </view>\n        <view class=\"module-content\">\n          <text class=\"module-title\">手动核销</text>\n          <text class=\"module-desc\">手动输入核销码完成核销</text>\n        </view>\n        <view class=\"module-arrow\">\n          <view class=\"arrow-icon\"></view>\n        </view>\n      </view>\n      \n      <!-- 核销记录 -->\n      <view class=\"module-card\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/verification/records')\">\n        <view class=\"module-icon records\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n            <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n            <polyline points=\"14 2 14 8 20 8\"></polyline>\n            <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n            <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n            <polyline points=\"10 9 9 9 8 9\"></polyline>\n          </svg>\n        </view>\n        <view class=\"module-content\">\n          <text class=\"module-title\">核销记录</text>\n          <text class=\"module-desc\">查看历史核销记录</text>\n        </view>\n        <view class=\"module-arrow\">\n          <view class=\"arrow-icon\"></view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 最近核销记录 -->\n    <view class=\"recent-records\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">最近核销</text>\n        <text class=\"view-all\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/verification/records')\">查看全部</text>\n      </view>\n      \n      <view class=\"records-list\">\n        <view v-if=\"recentRecords.length === 0\" class=\"empty-records\">\n          <text class=\"empty-text\">暂无核销记录</text>\n        </view>\n        <view v-else class=\"record-item\" v-for=\"(record, index) in recentRecords\" :key=\"index\">\n          <view class=\"record-type\" :class=\"record.typeClass\">{{record.typeText}}</view>\n          <view class=\"record-content\">\n            <view class=\"record-main\">\n              <text class=\"record-title\">{{record.title}}</text>\n              <text class=\"record-code\">{{record.code}}</text>\n            </view>\n            <view class=\"record-info\">\n              <text class=\"record-user\">用户：{{record.user}}</text>\n              <text class=\"record-time\">{{record.time}}</text>\n            </view>\n          </view>\n          <view class=\"record-status\" :class=\"record.statusClass\">{{record.status}}</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 核销统计 -->\n    <view class=\"verification-stats\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">核销统计</text>\n        <view class=\"date-filter\">\n          <text class=\"date-text\">{{dateRange}}</text>\n          <view class=\"date-arrow\"></view>\n        </view>\n      </view>\n      \n      <view class=\"stats-cards\">\n        <view class=\"stats-card\">\n          <text class=\"stats-value\">{{stats.totalCount}}</text>\n          <text class=\"stats-label\">总核销数</text>\n        </view>\n        <view class=\"stats-card\">\n          <text class=\"stats-value\">{{stats.todayCount}}</text>\n          <text class=\"stats-label\">今日核销</text>\n        </view>\n        <view class=\"stats-card\">\n          <text class=\"stats-value\">¥{{stats.totalAmount}}</text>\n          <text class=\"stats-label\">核销金额</text>\n        </view>\n      </view>\n      \n      <view class=\"stats-chart\">\n        <text class=\"chart-title\">近7日核销趋势</text>\n        <view class=\"chart-container\">\n          <!-- 简易图表，实际项目中可以使用图表组件 -->\n          <view class=\"chart-bars\">\n            <view class=\"chart-bar\" v-for=\"(item, index) in chartData\" :key=\"index\" :style=\"{ height: item.height + '%' }\">\n              <text class=\"bar-value\">{{item.value}}</text>\n            </view>\n          </view>\n          <view class=\"chart-labels\">\n            <text class=\"label-text\" v-for=\"(item, index) in chartData\" :key=\"index\">{{item.date}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      dateRange: '最近7天',\n      recentRecords: [\n        {\n          typeText: '拼团',\n          typeClass: 'type-group',\n          title: '双人下午茶套餐拼团',\n          code: 'GP20230618001',\n          user: '张三',\n          time: '今天 14:30',\n          status: '已核销',\n          statusClass: 'status-success'\n        },\n        {\n          typeText: '优惠券',\n          typeClass: 'type-coupon',\n          title: '新店开业满100减20券',\n          code: 'CP20230618002',\n          user: '李四',\n          time: '今天 11:15',\n          status: '已核销',\n          statusClass: 'status-success'\n        },\n        {\n          typeText: '秒杀',\n          typeClass: 'type-flash',\n          title: '限时特价烤鸭套餐',\n          code: 'FS20230617005',\n          user: '王五',\n          time: '昨天 18:45',\n          status: '已核销',\n          statusClass: 'status-success'\n        }\n      ],\n      stats: {\n        totalCount: 128,\n        todayCount: 15,\n        totalAmount: '12,846'\n      },\n      chartData: [\n        { date: '6/12', value: 8, height: 40 },\n        { date: '6/13', value: 12, height: 60 },\n        { date: '6/14', value: 10, height: 50 },\n        { date: '6/15', value: 15, height: 75 },\n        { date: '6/16', value: 9, height: 45 },\n        { date: '6/17', value: 14, height: 70 },\n        { date: '6/18', value: 15, height: 75 }\n      ]\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    showHelp() {\n      uni.showToast({\n        title: '核销中心帮助',\n        icon: 'none'\n      });\n    },\n    navigateTo(url) {\n      uni.navigateTo({\n        url: url\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.verification-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  padding-bottom: 20px;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #27ae60, #219150);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 4px 20px rgba(39, 174, 96, 0.2);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n}\n\n/* 核销功能模块样式 */\n.verification-modules {\n  padding: 20px 15px;\n}\n\n.module-card {\n  background: #FFFFFF;\n  border-radius: 16px;\n  margin-bottom: 16px;\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\n  transition: transform 0.2s, box-shadow 0.2s;\n}\n\n.module-card:active {\n  transform: scale(0.98);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);\n}\n\n.module-icon {\n  width: 56px;\n  height: 56px;\n  border-radius: 28px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 18px;\n}\n\n.module-icon svg {\n  width: 28px;\n  height: 28px;\n}\n\n.module-icon.scan {\n  background-color: rgba(39, 174, 96, 0.15);\n  color: #27ae60;\n}\n\n.module-icon.manual {\n  background-color: rgba(41, 128, 185, 0.15);\n  color: #2980b9;\n}\n\n.module-icon.records {\n  background-color: rgba(243, 156, 18, 0.15);\n  color: #f39c12;\n}\n\n.module-content {\n  flex: 1;\n}\n\n.module-title {\n  font-size: 17px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 6px;\n}\n\n.module-desc {\n  font-size: 13px;\n  color: #999;\n}\n\n.module-arrow {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.arrow-icon {\n  width: 8px;\n  height: 8px;\n  border-top: 2px solid #CCC;\n  border-right: 2px solid #CCC;\n  transform: rotate(45deg);\n}\n\n/* 最近核销记录样式 */\n.recent-records {\n  margin: 0 15px 20px;\n  background: #FFFFFF;\n  border-radius: 16px;\n  padding: 20px;\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 18px;\n}\n\n.section-title {\n  font-size: 17px;\n  font-weight: 600;\n  color: #333;\n}\n\n.view-all {\n  font-size: 14px;\n  color: #2980b9;\n  font-weight: 500;\n}\n\n.empty-records {\n  padding: 30px 0;\n  text-align: center;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n}\n\n.record-item {\n  display: flex;\n  align-items: center;\n  padding: 14px 0;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.record-item:last-child {\n  border-bottom: none;\n  padding-bottom: 0;\n}\n\n.record-type {\n  width: 60px;\n  height: 28px;\n  border-radius: 14px;\n  font-size: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 14px;\n  font-weight: 500;\n}\n\n.type-group {\n  background-color: rgba(39, 174, 96, 0.15);\n  color: #27ae60;\n}\n\n.type-coupon {\n  background-color: rgba(41, 128, 185, 0.15);\n  color: #2980b9;\n}\n\n.type-flash {\n  background-color: rgba(231, 76, 60, 0.15);\n  color: #e74c3c;\n}\n\n.record-content {\n  flex: 1;\n}\n\n.record-main {\n  margin-bottom: 4px;\n}\n\n.record-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  margin-right: 8px;\n}\n\n.record-code {\n  font-size: 12px;\n  color: #999;\n}\n\n.record-info {\n  display: flex;\n  font-size: 12px;\n  color: #999;\n}\n\n.record-user {\n  margin-right: 12px;\n}\n\n.record-status {\n  font-size: 13px;\n  font-weight: 500;\n}\n\n.status-success {\n  color: #30D158;\n}\n\n.status-pending {\n  color: #FF9500;\n}\n\n.status-failed {\n  color: #FF453A;\n}\n\n/* 核销统计样式 */\n.verification-stats {\n  margin: 0 15px 20px;\n  background: #FFFFFF;\n  border-radius: 16px;\n  padding: 20px;\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\n}\n\n.date-filter {\n  display: flex;\n  align-items: center;\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: 16px;\n  padding: 6px 12px;\n}\n\n.date-text {\n  font-size: 12px;\n  color: #666;\n  margin-right: 5px;\n}\n\n.date-arrow {\n  width: 8px;\n  height: 8px;\n  border-top: 2px solid #666;\n  border-right: 2px solid #666;\n  transform: rotate(135deg);\n}\n\n.stats-cards {\n  display: flex;\n  justify-content: space-between;\n  margin: 20px 0;\n}\n\n.stats-card {\n  flex: 1;\n  text-align: center;\n  padding: 15px 0;\n  position: relative;\n}\n\n.stats-card:not(:last-child):after {\n  content: '';\n  position: absolute;\n  right: 0;\n  top: 20%;\n  height: 60%;\n  width: 1px;\n  background: rgba(0, 0, 0, 0.05);\n}\n\n.stats-value {\n  font-size: 22px;\n  font-weight: 700;\n  color: #333;\n  margin-bottom: 6px;\n  display: block;\n}\n\n.stats-label {\n  font-size: 12px;\n  color: #999;\n}\n\n.stats-chart {\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\n  padding-top: 20px;\n}\n\n.chart-title {\n  font-size: 15px;\n  color: #666;\n  margin-bottom: 20px;\n  display: block;\n  font-weight: 500;\n}\n\n.chart-container {\n  height: 160px;\n}\n\n.chart-bars {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  height: 120px;\n}\n\n.chart-bar {\n  width: 36px;\n  background: linear-gradient(180deg, #27ae60, #219150);\n  border-radius: 4px 4px 0 0;\n  position: relative;\n  display: flex;\n  justify-content: center;\n}\n\n.bar-value {\n  position: absolute;\n  top: -20px;\n  font-size: 12px;\n  color: #666;\n}\n\n.chart-labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 8px;\n}\n\n.label-text {\n  width: 30px;\n  text-align: center;\n  font-size: 12px;\n  color: #999;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/verification/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiJA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA,QACb;AAAA,UACE,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,QACf;AAAA,MACD;AAAA,MACD,OAAO;AAAA,QACL,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACD,WAAW;AAAA,QACT,EAAE,MAAM,QAAQ,OAAO,GAAG,QAAQ,GAAI;AAAA,QACtC,EAAE,MAAM,QAAQ,OAAO,IAAI,QAAQ,GAAI;AAAA,QACvC,EAAE,MAAM,QAAQ,OAAO,IAAI,QAAQ,GAAI;AAAA,QACvC,EAAE,MAAM,QAAQ,OAAO,IAAI,QAAQ,GAAI;AAAA,QACvC,EAAE,MAAM,QAAQ,OAAO,GAAG,QAAQ,GAAI;AAAA,QACtC,EAAE,MAAM,QAAQ,OAAO,IAAI,QAAQ,GAAI;AAAA,QACvC,EAAE,MAAM,QAAQ,OAAO,IAAI,QAAQ,GAAG;AAAA,MACxC;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpNA,GAAG,WAAW,eAAe;"}