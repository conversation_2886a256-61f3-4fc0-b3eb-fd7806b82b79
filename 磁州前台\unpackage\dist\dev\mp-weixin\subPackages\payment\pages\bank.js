"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "bank",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const navbarHeight = common_vendor.ref(64);
    const bankCards = common_vendor.ref([
      {
        id: "card001",
        bankName: "中国建设银行",
        bankLogo: "/static/images/banks/ccb.png",
        cardType: "储蓄卡",
        cardNumber: "6217 0012 3456 7890",
        cardNumberLast4: "7890",
        cardHolder: "张三",
        isDefault: true
      },
      {
        id: "card002",
        bankName: "中国工商银行",
        bankLogo: "/static/images/banks/icbc.png",
        cardType: "储蓄卡",
        cardNumber: "6222 0212 3456 1234",
        cardNumberLast4: "1234",
        cardHolder: "张三",
        isDefault: false
      }
    ]);
    const selectedCardId = common_vendor.ref("card001");
    const showPopup = common_vendor.ref(false);
    const newCard = common_vendor.reactive({
      cardHolder: "",
      cardNumber: "",
      bankName: "",
      bankId: "",
      phone: "",
      isDefault: false
    });
    const bankList = [
      { id: "icbc", name: "中国工商银行", logo: "/static/images/banks/icbc.png" },
      { id: "abc", name: "中国农业银行", logo: "/static/images/banks/abc.png" },
      { id: "boc", name: "中国银行", logo: "/static/images/banks/boc.png" },
      { id: "ccb", name: "中国建设银行", logo: "/static/images/banks/ccb.png" },
      { id: "psbc", name: "中国邮政储蓄银行", logo: "/static/images/banks/psbc.png" },
      { id: "cmb", name: "招商银行", logo: "/static/images/banks/cmb.png" },
      { id: "spdb", name: "浦发银行", logo: "/static/images/banks/spdb.png" },
      { id: "cib", name: "兴业银行", logo: "/static/images/banks/cib.png" }
    ];
    const canAddCard = common_vendor.computed(() => {
      return newCard.cardHolder && newCard.cardNumber && newCard.cardNumber.length >= 16 && newCard.bankName && newCard.phone && newCard.phone.length === 11;
    });
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight;
      navbarHeight.value = statusBarHeight.value + 44;
      getBankCards();
    });
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const getBankCards = () => {
    };
    const selectCard = (cardId) => {
      selectedCardId.value = cardId;
    };
    const setAsDefault = (cardId) => {
      common_vendor.index.showLoading({
        title: "设置中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        bankCards.value.forEach((card) => {
          card.isDefault = card.id === cardId;
        });
        common_vendor.index.showToast({
          title: "设置成功",
          icon: "success"
        });
      }, 500);
    };
    const deleteCard = (cardId) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除此银行卡吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "删除中..."
            });
            setTimeout(() => {
              common_vendor.index.hideLoading();
              const index = bankCards.value.findIndex((card) => card.id === cardId);
              if (index !== -1) {
                const isDefault = bankCards.value[index].isDefault;
                bankCards.value.splice(index, 1);
                if (isDefault && bankCards.value.length > 0) {
                  bankCards.value[0].isDefault = true;
                }
                if (selectedCardId.value === cardId) {
                  selectedCardId.value = bankCards.value.length > 0 ? bankCards.value[0].id : "";
                }
              }
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    };
    const showAddCardPopup = () => {
      showPopup.value = true;
      resetNewCard();
    };
    const closePopup = () => {
      showPopup.value = false;
    };
    const resetNewCard = () => {
      Object.assign(newCard, {
        cardHolder: "",
        cardNumber: "",
        bankName: "",
        bankId: "",
        phone: "",
        isDefault: false
      });
    };
    const formatCardNumber = (e) => {
      let value = e.detail.value.replace(/\s/g, "");
      let formattedValue = "";
      for (let i = 0; i < value.length; i++) {
        if (i > 0 && i % 4 === 0) {
          formattedValue += " ";
        }
        formattedValue += value[i];
      }
      newCard.cardNumber = formattedValue;
    };
    const onBankChange = (e) => {
      const index = e.detail.value;
      const selectedBank = bankList[index];
      newCard.bankName = selectedBank.name;
      newCard.bankId = selectedBank.id;
    };
    const addCard = () => {
      if (!canAddCard.value)
        return;
      common_vendor.index.showLoading({
        title: "添加中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const cardNumber = newCard.cardNumber.replace(/\s/g, "");
        const cardNumberLast4 = cardNumber.slice(-4);
        const selectedBank = bankList.find((bank) => bank.id === newCard.bankId);
        const newCardInfo = {
          id: "card" + Date.now(),
          bankName: newCard.bankName,
          bankLogo: selectedBank ? selectedBank.logo : "/static/images/banks/default.png",
          cardType: "储蓄卡",
          cardNumber: newCard.cardNumber,
          cardNumberLast4,
          cardHolder: newCard.cardHolder,
          isDefault: newCard.isDefault
        };
        if (newCardInfo.isDefault) {
          bankCards.value.forEach((card) => {
            card.isDefault = false;
          });
        }
        bankCards.value.push(newCardInfo);
        selectedCardId.value = newCardInfo.id;
        closePopup();
        common_vendor.index.showToast({
          title: "添加成功",
          icon: "success"
        });
      }, 800);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: bankCards.value.length > 0
      }, bankCards.value.length > 0 ? {
        e: common_vendor.f(bankCards.value, (card, index, i0) => {
          return common_vendor.e({
            a: card.bankLogo,
            b: common_vendor.t(card.bankName),
            c: common_vendor.t(card.cardType),
            d: common_vendor.t(card.cardNumberLast4),
            e: common_vendor.t(card.cardHolder),
            f: card.isDefault
          }, card.isDefault ? {} : {}, {
            g: !card.isDefault
          }, !card.isDefault ? {
            h: common_vendor.o(($event) => setAsDefault(card.id), index)
          } : {}, {
            i: common_vendor.o(($event) => deleteCard(card.id), index),
            j: index,
            k: selectedCardId.value === card.id ? 1 : "",
            l: common_vendor.o(($event) => selectCard(card.id), index)
          });
        })
      } : {
        f: common_assets._imports_1$3
      }, {
        g: navbarHeight.value + 10 + "px",
        h: common_assets._imports_2$12,
        i: common_vendor.o(showAddCardPopup),
        j: showPopup.value
      }, showPopup.value ? {
        k: common_vendor.o(closePopup)
      } : {}, {
        l: showPopup.value
      }, showPopup.value ? {
        m: common_vendor.o(closePopup),
        n: newCard.cardHolder,
        o: common_vendor.o(($event) => newCard.cardHolder = $event.detail.value),
        p: common_vendor.o([($event) => newCard.cardNumber = $event.detail.value, formatCardNumber]),
        q: newCard.cardNumber,
        r: common_vendor.t(newCard.bankName || "请选择开户银行"),
        s: bankList,
        t: common_vendor.o(onBankChange),
        v: newCard.phone,
        w: common_vendor.o(($event) => newCard.phone = $event.detail.value),
        x: newCard.isDefault,
        y: common_vendor.o(($event) => newCard.isDefault = !newCard.isDefault),
        z: common_vendor.o(closePopup),
        A: common_vendor.o(addCard),
        B: !canAddCard.value
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/payment/pages/bank.js.map
