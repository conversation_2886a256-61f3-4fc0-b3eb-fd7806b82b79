<template>
  <view class="carpool-card" :class="item.type" @click="$emit('click', item)">
    <!-- 优化后的拼车卡片布局 -->
    <view class="card-header">
      <view class="card-type-indicator"></view>
      <view class="route-direction">
        <view class="route-info-row">
          <text class="route-label">出发:</text>
          <text class="route-value">{{item.startPoint}}</text>
        </view>
        <view class="route-info-row via-points" v-if="item.viaPoints && item.viaPoints.length > 0">
          <text class="route-label">途经:</text>
          <view class="route-value-wrapper">
            <text class="via-icon">⟡</text>
            <text class="route-value">{{item.viaPoints.join(' → ')}}</text>
          </view>
        </view>
        <view class="route-info-row">
          <text class="route-label">到达:</text>
          <text class="route-value">{{item.endPoint}}</text>
        </view>
      </view>
      <!-- 类型标签 -->
      <view class="card-type-tag" :class="item.type + '-tag'">
        <text>{{typeText}}</text>
      </view>
      <!-- 发布方式标签 -->
      <view class="publish-mode-tag" :class="item.publishMode">
        <text>{{item.publishMode === 'premium' ? '置顶' : '广告'}}</text>
      </view>
    </view>
    
    <view class="card-footer">
      <view class="trip-info">
        <view class="info-item">
          <image src="/static/images/tabbar/时间.png" mode="aspectFit" class="info-icon"></image>
          <text class="info-value">{{item.departureTime}}</text>
        </view>
        
        <view class="info-item" v-if="item.price">
          <image src="/static/images/tabbar/价格.png" mode="aspectFit" class="info-icon"></image>
          <text class="info-value">{{item.price}}元</text>
        </view>
        
        <view class="info-item" v-if="item.seats">
          <image src="/static/images/tabbar/座位.png" mode="aspectFit" class="info-icon"></image>
          <text class="info-value">{{item.seats}}座</text>
        </view>
      </view>
      
      <view class="user-actions">
        <view class="user-info">
          <image class="user-avatar" :src="item.avatar" mode="aspectFill"></image>
          <view class="user-name-wrapper">
            <text class="user-name">{{item.username}}</text>
            <!-- 已认证标签 -->
            <view class="verified-badge" v-if="item.isVerified">
              <image src="/static/images/tabbar/verified.png" mode="aspectFit" class="verified-icon"></image>
              <text class="verified-text">已认证</text>
            </view>
          </view>
        </view>
        
        <view class="card-actions">
          <button class="action-button phone" @click.stop="onPhoneClick" data-label="拨打电话">
            <image src="/static/images/tabbar/电话.png" mode="aspectFit"></image>
          </button>
          <button class="action-button chat" @click.stop="onChatClick" data-label="发送私信">
            <image src="/static/images/tabbar/消息.png" mode="aspectFit"></image>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

// 定义props
const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

// 定义事件
const emit = defineEmits(['click', 'phone-click', 'chat-click']);

// 计算类型文本
const typeText = computed(() => {
  switch(props.item.type) {
    case 'people-to-car': return '人找车';
    case 'car-to-people': return '车找人';
    case 'goods-to-car': return '货找车';
    case 'car-to-goods': return '车找货';
    default: return '';
  }
});

// 点击事件处理
const onPhoneClick = (event) => {
  event.stopPropagation();
  emit('phone-click', props.item);
};

const onChatClick = (event) => {
  event.stopPropagation();
  emit('chat-click', props.item);
};
</script>

<style lang="scss">
.carpool-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  border-left: 6rpx solid #0A84FF;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  
  /* 添加顶部渐变边框 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 6rpx;
    right: 0;
    height: 4rpx;
    background: linear-gradient(to right, transparent, rgba(10, 132, 255, 0.3), transparent);
    z-index: 2;
  }
}

/* 不同类型的卡片左侧边框颜色 */
.carpool-card.people-to-car {
  border-left-color: #0A84FF;
}

.carpool-card.car-to-people {
  border-left-color: #FF2D55;
}

.carpool-card.goods-to-car {
  border-left-color: #30D158;
}

.carpool-card.car-to-goods {
  border-left-color: #FF9F0A;
}

.card-header {
  padding: 24rpx;
  position: relative;
}

.card-type-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background-color: #0A84FF;
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}

.route-direction {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.route-info-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.route-label {
  font-size: 26rpx;
  color: #666666;
  width: 80rpx;
}

.route-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.via-points {
  color: #666666;
}

.via-icon {
  margin-right: 8rpx;
  color: #0A84FF;
}

.route-value-wrapper {
  display: flex;
  align-items: center;
}

.card-type-tag {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: #FFFFFF;
}

.people-to-car-tag {
  background-color: #0A84FF;
}

.car-to-people-tag {
  background-color: #FF2D55;
}

.goods-to-car-tag {
  background-color: #30D158;
}

.car-to-goods-tag {
  background-color: #FF9F0A;
}

.publish-mode-tag {
  position: absolute;
  top: 70rpx;
  right: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: #FFFFFF;
}

.premium {
  background-color: #FF2D55;
}

.ad {
  background-color: #FF9F0A;
}

.card-footer {
  padding: 24rpx;
  border-top: 1px solid #F2F2F7;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
}

.trip-info {
  display: flex;
  gap: 24rpx;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.user-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-actions {
  display: flex;
  gap: 16rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-icon {
  width: 32rpx;
  height: 32rpx;
}

.info-value {
  font-size: 26rpx;
  color: #666666;
}

.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.user-name-wrapper {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.user-name {
  font-size: 26rpx;
  color: #333333;
  max-width: 120rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 已认证标签样式 */
.verified-badge {
  display: flex;
  align-items: center;
  background-color: rgba(10, 132, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(10, 132, 255, 0.3);
}

.verified-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.verified-text {
  font-size: 20rpx;
  color: #0A84FF;
  font-weight: 500;
}

.action-button {
  width: 88rpx;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0;
  padding: 0;
}

.action-button.phone {
  background-color: #34C759;
  box-shadow: 0 4rpx 12rpx rgba(52, 199, 89, 0.3);
}

.action-button.chat {
  background-color: #0A84FF;
  box-shadow: 0 4rpx 12rpx rgba(10, 132, 255, 0.3);
}

.action-button image {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}
</style> 