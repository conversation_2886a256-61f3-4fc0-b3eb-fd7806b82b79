<template>
  <view class="agreement-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="back-btn" @click="navigateBack">
        <image class="back-icon" src="/static/images/tabbar/arrow-up.png"></image>
      </view>
      <view class="navbar-title">{{ title }}</view>
    </view>
    
    <!-- 协议内容 -->
    <scroll-view scroll-y class="content-scroll">
      <view class="agreement-content">
        <!-- 服务协议 -->
        <block v-if="type === 'service'">
          <view class="section">
            <view class="section-title">一、协议的范围</view>
            <view class="section-content">
              <view class="paragraph">1. 本服务协议适用于同城信息平台（以下简称"本平台"）的所有用户，包括但不限于信息发布者、浏览者等。</view>
              <view class="paragraph">2. 本协议是用户与本平台运营方之间关于使用本平台服务的法律协议，具有法律效力。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">二、用户注册与账号管理</view>
            <view class="section-content">
              <view class="paragraph">1. 用户在注册账号时，应提供真实、准确、完整的个人信息，如姓名、联系方式、地址等。如因提供虚假信息导致的任何法律问题，用户应承担全部责任。</view>
              <view class="paragraph">2. 用户应妥善保管账号和密码，不得将账号转让、出租或出借给他人使用。如发现账号被盗用或存在安全问题，应立即通知本平台。</view>
              <view class="paragraph">3. 用户有权根据本平台规定申请注销账号，账号注销后，相关信息将按照本平台规定进行处理。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">三、服务内容与使用规则</view>
            <view class="section-content">
              <view class="paragraph">1. 本平台为用户提供同城信息发布、查询、交流等服务，包括但不限于招聘求职、房屋租售、二手交易、生活服务等各类信息。</view>
              <view class="paragraph">2. 用户发布的信息应符合法律法规和社会公德，不得包含违法、违规、虚假、欺诈、低俗、骚扰等内容。不得利用本平台进行任何违法犯罪活动或侵犯他人合法权益的行为。</view>
              <view class="paragraph">3. 用户在使用本平台服务时，应遵守本平台的各项规定和操作流程，不得恶意破坏平台功能、干扰平台正常运行。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">四、平台权利与义务</view>
            <view class="section-content">
              <view class="paragraph">1. 本平台有权对用户发布的信息进行审核、管理和删除，如发现违法违规或违反本协议的信息，有权立即采取措施。</view>
              <view class="paragraph">2. 本平台有权根据业务发展需要对平台功能、服务内容进行调整、升级或暂停，但应提前通知用户。</view>
              <view class="paragraph">3. 本平台应尽力维护平台的稳定性和安全性，保障用户信息和数据的安全。为用户提供必要的技术支持和客服服务，及时处理用户的咨询和投诉。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">五、责任限制与免责</view>
            <view class="section-content">
              <view class="paragraph">1. 本平台仅对因自身故意或重大过失给用户造成的直接损失承担责任，对于间接损失、偶然损失或其他非直接损失，本平台不承担责任。</view>
              <view class="paragraph">2. 因不可抗力、政府行为、第三方原因（如网络服务提供商、技术供应商等）导致本平台服务中断、信息丢失或其他问题，本平台不承担责任。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">六、知识产权</view>
            <view class="section-content">
              <view class="paragraph">1. 用户在本平台发布的信息，其知识产权归用户所有，但用户授予本平台在全球范围内、免费、非排他性的使用许可，包括但不限于复制、传播、展示、推广等权利，以便本平台为用户提供服务。</view>
              <view class="paragraph">2. 本平台的商标、标识、专利、著作权等知识产权归本平台运营方所有，用户不得侵犯。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">七、法律适用与争议解决</view>
            <view class="section-content">
              <view class="paragraph">1. 本协议受中华人民共和国法律的管辖。</view>
              <view class="paragraph">2. 如用户与本平台之间发生争议，应首先通过友好协商解决；协商不成的，任何一方均有权向有管辖权的人民法院提起诉讼。</view>
            </view>
          </view>
        </block>
        
        <!-- 隐私政策 -->
        <block v-if="type === 'privacy'">
          <view class="section">
            <view class="section-title">一、引言</view>
            <view class="section-content">
              <view class="paragraph">本隐私政策适用于使用同城信息平台（以下简称"本平台"）的所有用户。本平台致力于保护用户的个人信息安全和隐私。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">二、定义</view>
            <view class="section-content">
              <view class="paragraph">1. "个人信息"是指能够单独或者与其他信息结合识别特定自然人身份或者反映特定自然人活动情况的各种信息。</view>
              <view class="paragraph">2. "敏感个人信息"是指一旦泄露、非法提供或滥用可能危害人身和财产安全，极易导致个人名誉、身心健康受到损害或歧视性待遇等的个人信息。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">三、个人信息的收集</view>
            <view class="section-content">
              <view class="paragraph">1. <text class="bold">注册信息</text>：收集用户的姓名、联系方式（如手机号码、电子邮箱）、用户名、密码等用于注册账号和身份验证。</view>
              <view class="paragraph">2. <text class="bold">服务相关信息</text>：根据用户使用的具体服务功能，收集相应信息。例如发布招聘信息时收集企业信息、招聘岗位要求；发布租房信息时收集房屋地址、面积、租金等。</view>
              <view class="paragraph">3. <text class="bold">设备信息</text>：收集用户设备的型号、操作系统、IP地址、浏览器类型等，以保障平台安全和优化服务。</view>
              <view class="paragraph">4. <text class="bold">位置信息</text>：经用户授权，收集位置信息，以便提供附近的信息推荐和本地化服务。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">四、个人信息的使用</view>
            <view class="section-content">
              <view class="paragraph">1. <text class="bold">提供服务</text>：用于为用户提供平台的各项服务，如展示信息、处理交易、提供个性化推荐等。</view>
              <view class="paragraph">2. <text class="bold">安全保障</text>：用于保障平台的安全运行，防范安全风险和违法违规行为，对用户账号进行安全验证和监测。</view>
              <view class="paragraph">3. <text class="bold">改进服务</text>：分析用户的使用行为和偏好，以改进平台的功能和服务质量，提升用户体验。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">五、个人信息的共享、转让与公开披露</view>
            <view class="section-content">
              <view class="paragraph">1. <text class="bold">共享</text>：在必要情况下，与关联公司、合作伙伴（如技术服务提供商、支付机构）共享用户个人信息，但会要求其遵守严格的保密义务。</view>
              <view class="paragraph">2. <text class="bold">转让</text>：一般不会将用户个人信息转让给第三方，除非经过用户明确同意或法律法规有明确要求。</view>
              <view class="paragraph">3. <text class="bold">公开披露</text>：仅在法律要求或为维护公共利益等特殊情况下，才会公开披露用户个人信息，并会在公开披露前告知用户相关信息。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">六、个人信息的保护</view>
            <view class="section-content">
              <view class="paragraph">1. <text class="bold">安全措施</text>：采取加密存储、访问控制、数据备份等技术措施，保护用户个人信息的安全，防止信息泄露、篡改和丢失。</view>
              <view class="paragraph">2. <text class="bold">人员管理</text>：对接触用户个人信息的员工进行严格管理，要求其遵守保密制度，对违规行为进行处罚。</view>
              <view class="paragraph">3. <text class="bold">安全事件处理</text>：如发生个人信息安全事件，将立即启动应急预案，采取措施降低损害，并按照法律法规要求及时通知用户和相关监管部门。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">七、用户的权利</view>
            <view class="section-content">
              <view class="paragraph">1. <text class="bold">查询权</text>：用户有权查询自己在本平台的个人信息。</view>
              <view class="paragraph">2. <text class="bold">更正权</text>：如发现个人信息有误，用户有权要求更正。</view>
              <view class="paragraph">3. <text class="bold">删除权</text>：在符合法律法规和本平台规定的情况下，用户有权要求删除个人信息。</view>
              <view class="paragraph">4. <text class="bold">撤回同意权</text>：用户有权随时撤回对个人信息收集和使用的同意，但不影响撤回前已进行的处理行为的合法性。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">八、未成年人保护</view>
            <view class="section-content">
              <view class="paragraph">1. 本平台重视未成年人个人信息保护，如用户是未成年人，建议在监护人指导下使用本平台。</view>
              <view class="paragraph">2. 如发现收集了未成年人的个人信息，将尽快删除，除非获得监护人的有效同意。</view>
            </view>
          </view>
          
          <view class="section">
            <view class="section-title">九、隐私政策的更新</view>
            <view class="section-content">
              <view class="paragraph">本平台会根据法律法规变化、业务发展等情况适时更新隐私政策。更新后的政策将在平台上公布，用户继续使用本平台服务即视为接受更新后的隐私政策。</view>
            </view>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 获取URL参数
const type = ref('');
const title = ref('');

onMounted(() => {
  // 获取页面参数
  const query = uni.getLaunchOptionsSync().query || {};
  type.value = query.type || 'service';
  title.value = decodeURIComponent(query.title || '服务协议');
  
  // 设置导航栏标题
  uni.setNavigationBarTitle({
    title: title.value
  });
});

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};
</script>

<style>
.agreement-container {
  background-color: #f8f9fc;
  min-height: 100vh;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  background-color: #ffffff;
  z-index: 999;
  border-bottom: 1px solid #f0f0f0;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
  transform: rotate(270deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.content-scroll {
  margin-top: 88rpx;
  height: calc(100vh - 88rpx);
}

.agreement-content {
  padding: 30rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

.paragraph {
  margin-bottom: 16rpx;
  text-align: justify;
}

.bold {
  font-weight: bold;
}
</style> 