<template>
  <view class="car-detail-container">
    <!-- 添加自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">二手车辆详情</view>
      <view class="navbar-right">
        <!-- 占位 -->
      </view>
    </view>
    
    <!-- 隐藏的分享按钮，用于自动触发 -->
    <button id="shareButton" class="hidden-share-btn" open-type="share"></button>
    
    <view class="car-detail-wrapper">
      <!-- 车辆基本信息卡片 -->
      <view class="content-card car-info-card">
        <view class="car-header">
          <view class="car-title-row">
            <text class="car-title">{{carData.title}}</text>
            <text class="car-price">{{carData.price}}</text>
          </view>
          <view class="car-meta">
            <view class="car-tag-group">
              <view class="car-tag" v-for="(tag, index) in carData.tags" :key="index">{{tag}}</view>
            </view>
            <text class="car-publish-time">发布于 {{formatTime(carData.publishTime)}}</text>
          </view>
        </view>
        
        <!-- 车辆图片轮播 -->
        <swiper class="car-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
          <swiper-item v-for="(image, index) in carData.images" :key="index">
            <image :src="image" mode="aspectFill" class="car-image"></image>
          </swiper-item>
        </swiper>
        
        <!-- 基本信息 -->
        <view class="car-basic-info">
          <view class="info-item">
            <text class="info-label">品牌型号</text>
            <text class="info-value">{{carData.brand}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">上牌时间</text>
            <text class="info-value">{{carData.registerTime}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">行驶里程</text>
            <text class="info-value">{{carData.mileage}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">排放标准</text>
            <text class="info-value">{{carData.emission}}</text>
          </view>
        </view>
      </view>
      
      <!-- 车辆配置 -->
      <view class="content-card car-config-card">
        <view class="section-title">车辆配置</view>
        <view class="config-list">
          <view class="config-item" v-for="(item, index) in carData.configs" :key="index">
            <text class="config-icon iconfont" :class="item.icon"></text>
            <text class="config-text">{{item.name}}</text>
          </view>
        </view>
      </view>
      
      <!-- 车况信息 -->
      <view class="content-card car-condition-card">
        <view class="section-title">车况信息</view>
        <view class="condition-list">
          <view class="condition-item" v-for="(item, index) in carData.conditions" :key="index">
            <text class="condition-label">{{item.label}}</text>
            <text class="condition-value">{{item.value}}</text>
          </view>
        </view>
      </view>
      
      <!-- 车辆描述 -->
      <view class="content-card description-card">
        <view class="section-title">车辆描述</view>
        <view class="description-content">
          <text class="description-text">{{carData.description}}</text>
        </view>
      </view>
      
      <!-- 车主信息 -->
      <view class="content-card owner-card">
        <view class="owner-header">
          <view class="owner-avatar">
            <image :src="carData.owner.avatar" mode="aspectFill"></image>
          </view>
          <view class="owner-info">
            <text class="owner-name">{{carData.owner.name}}</text>
            <view class="owner-meta">
              <text class="owner-type">{{carData.owner.type}}</text>
              <text class="owner-rating">信用等级 {{carData.owner.rating}}</text>
            </view>
          </view>
          <view class="owner-auth" v-if="carData.owner.isVerified">
            <text class="iconfont icon-verified"></text>
            <text class="auth-text">已认证</text>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="content-card contact-card">
        <view class="contact-header">
          <text class="card-title">联系方式</text>
        </view>
        <view class="contact-content">
          <view class="contact-item">
            <text class="contact-label">联系人</text>
            <text class="contact-value">{{carData.contact.name}}</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">电话</text>
            <text class="contact-value contact-phone" @click="callPhone">{{carData.contact.phone}}</text>
          </view>
          <view class="contact-tips">
            <text class="tips-icon iconfont icon-info"></text>
            <text class="tips-text">请说明在"磁州生活网"看到的信息</text>
          </view>
        </view>
      </view>
      
      <!-- 举报卡片 -->
      <report-card></report-card>
      
      <!-- 相似车辆推荐 -->
      <view class="content-card related-cars-card">
        <view class="section-title">相关车辆推荐</view>
        <view class="related-cars-content">
          <!-- 简洁的车辆列表 -->
          <view class="related-cars-list">
            <view class="related-car-item" 
                 v-for="(car, index) in relatedCars.slice(0, 3)" 
                 :key="index" 
                 @click="navigateToCarDetail(car.id)">
              <view class="car-item-content">
                <view class="car-item-left">
                  <image class="car-image" :src="car.image" mode="aspectFill"></image>
                </view>
                <view class="car-item-middle">
                  <text class="car-item-title">{{car.title}}</text>
                  <view class="car-item-brand">{{car.brand}} · {{car.mileage}}</view>
                  <view class="car-item-tags">
                    <text class="car-item-tag" v-for="(tag, tagIndex) in car.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
                    <text class="car-item-tag-more" v-if="car.tags && car.tags.length > 2">+{{car.tags.length - 2}}</text>
                  </view>
                </view>
                <view class="car-item-right">
                  <text class="car-item-price">{{car.price}}</text>
                </view>
              </view>
            </view>
            
            <!-- 暂无数据提示 -->
            <view class="empty-related-cars" v-if="relatedCars.length === 0">
              <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
              <text class="empty-text">暂无相关车辆</text>
            </view>
          </view>
          
          <!-- 查看更多按钮 -->
          <view class="view-more-btn" v-if="relatedCars.length > 0" @click.stop="navigateToCarList">
            <text class="view-more-text">查看更多车辆信息</text>
            <text class="view-more-icon iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="interaction-toolbar">
      <view class="toolbar-item" @click="goToHome">
        <image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
        <text class="toolbar-text">首页</text>
      </view>
      <view class="toolbar-item" @click="toggleCollect">
        <image src="/static/images/tabbar/a收藏.png" class="toolbar-icon"></image>
        <text class="toolbar-text">收藏</text>
      </view>
      <button class="share-button toolbar-item" open-type="share">
        <image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
        <text class="toolbar-text">分享</text>
      </button>
      <view class="toolbar-item" @click="openChat">
        <image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
        <text class="toolbar-text">私信</text>
      </view>
      <view class="toolbar-item call-button" @click="callPhone">
        <view class="call-button-content">
          <text class="call-text">打电话</text>
          <text class="call-subtitle">请说在磁州生活网看到的</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ReportCard from '@/components/ReportCard.vue'

// 状态栏高度
const statusBarHeight = ref(20);

// 获取状态栏高度
onMounted(() => {
  try {
    const sysInfo = uni.getSystemInfoSync();
    statusBarHeight.value = sysInfo.statusBarHeight || 20;
  } catch (e) {
    console.error('获取状态栏高度失败', e);
  }
  
  // 获取路由参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 获取车辆ID
  const id = options.id || '';
  console.log('车辆详情页ID:', id);
  
  // 加载相关车辆推荐
  loadRelatedCars();
});

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  });
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 响应式数据
const isCollected = ref(false);
const carData = ref({
  id: 'car12345',
  title: '2019款大众帕萨特',
  price: '15.8万',
  tags: ['一手车', '车况良好', '可分期'],
  publishTime: Date.now() - 86400000 * 2, // 2天前
  images: [
    '/static/images/car1.jpg',
    '/static/images/car2.jpg',
    '/static/images/car3.jpg'
  ],
  brand: '大众帕萨特 2019款 330TSI 豪华版',
  registerTime: '2019年6月',
  mileage: '3.5万公里',
  emission: '国六',
  configs: [
    { name: '自动挡', icon: 'icon-gear' },
    { name: '真皮座椅', icon: 'icon-seat' },
    { name: '倒车影像', icon: 'icon-camera' },
    { name: '定速巡航', icon: 'icon-cruise' },
    { name: '天窗', icon: 'icon-sunroof' },
    { name: '导航', icon: 'icon-navigation' }
  ],
  conditions: [
    { label: '车况', value: '良好' },
    { label: '过户次数', value: '0次' },
    { label: '保养记录', value: '全程4S店' },
    { label: '事故情况', value: '无事故' }
  ],
  description: '2019年6月上牌，一手车，全程4S店保养，车况良好，无事故，无泡水，无火烧。配置齐全，真皮座椅，倒车影像，定速巡航，天窗，导航等。价格可谈，支持分期付款。',
  owner: {
    name: '张先生',
    avatar: '/static/images/avatar.png',
    type: '个人',
    rating: 'A+',
    isVerified: true
  },
  contact: {
    name: '张先生',
    phone: '13912345678'
  }
});

const similarCars = ref([
  {
    id: 'car001',
    title: '2018款大众帕萨特',
    price: '14.5万',
    brand: '大众帕萨特',
    mileage: '4.2万公里',
    image: '/static/images/car-similar1.jpg'
  },
  {
    id: 'car002',
    title: '2020款大众帕萨特',
    price: '16.8万',
    brand: '大众帕萨特',
    mileage: '2.8万公里',
    image: '/static/images/car-similar2.jpg'
  }
]);

// 相关车辆推荐数据
const relatedCars = ref([]);

// 加载相关车辆推荐
const loadRelatedCars = () => {
  // 这里应该调用API获取数据
  // 实际项目中应该根据当前车辆的品牌、价格区间等进行相关性匹配
  
  // 模拟数据，使用现有的similarCars数据，并增加一些额外数据
  setTimeout(() => {
    relatedCars.value = [
      ...similarCars.value.map(car => ({
        ...car,
        tags: ['车况好', '无事故']
      })),
      {
        id: 'car003',
        title: '2019款本田雅阁',
        price: '16.2万',
        brand: '本田雅阁',
        mileage: '3.6万公里',
        image: '/static/images/car-similar3.jpg',
        tags: ['一手车', '全程4S保养', '无事故']
      }
    ];
  }, 500);
};

// 跳转到车辆详情页
const navigateToCarDetail = (carId) => {
  // 防止跳转到当前页面
  if (carId === carData.value.id) {
    return;
  }
  
  uni.navigateTo({
    url: `/pages/publish/car-detail?id=${carId}`
  });
};

// 跳转到二手车列表页
const navigateToCarList = (e) => {
  if (e) e.stopPropagation();
  const carCategory = carData.value.tags?.[0] || '';
  uni.navigateTo({ 
    url: `/subPackages/service/pages/filter?type=second_car&title=${encodeURIComponent('二手车辆')}&category=${encodeURIComponent(carCategory)}&active=second_car` 
  });
};

// 方法
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  if (isCollected.value) {
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    });
  }
};

const showShareOptions = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  });
};

const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: carData.value.contact.phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

// 跳转到首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 打开私信聊天
const openChat = () => {
  if (!carData.value.owner || !carData.value.owner.id) {
    uni.showToast({
      title: '无法获取发布者信息',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到聊天页面
  uni.navigateTo({
    url: `/pages/chat/index?userId=${carData.value.owner.id}&username=${encodeURIComponent(carData.value.owner.name || '用户')}`
  });
};
</script>

<style>
.car-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 150rpx;
  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */
}

.car-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 车辆基本信息卡片 */
.car-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.car-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.car-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}

.car-meta {
  margin-bottom: 24rpx;
}

.car-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.car-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

.car-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.car-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.car-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.car-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 车辆配置 */
.config-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12rpx;
}

.config-item {
  width: 33.33%;
  padding: 12rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.config-icon {
  font-size: 32rpx;
  color: #1890ff;
  margin-right: 8rpx;
}

.config-text {
  font-size: 26rpx;
  color: #666;
}

/* 车况信息 */
.condition-list {
  display: flex;
  flex-direction: column;
}

.condition-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.condition-item:last-child {
  border-bottom: none;
}

.condition-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}

.condition-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 车辆描述 */
.description-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 车主信息 */
.owner-header {
  display: flex;
  align-items: center;
}

.owner-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.owner-avatar image {
  width: 100%;
  height: 100%;
}

.owner-info {
  flex: 1;
}

.owner-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.owner-meta {
  display: flex;
  align-items: center;
}

.owner-type, .owner-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 相似车辆推荐样式 */
.related-cars-card {
  margin-top: 12px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.related-cars-content {
  padding: 0 16px 16px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  position: relative;
  padding-left: 10px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  width: 3px;
  height: 16px;
  background-color: #0052CC;
  border-radius: 3px;
}

/* 相关车辆列表样式 */
.related-cars-list {
  margin-bottom: 12px;
}

.related-car-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.related-car-item:last-child {
  border-bottom: none;
}

.car-item-content {
  display: flex;
  align-items: center;
}

.car-item-left {
  margin-right: 12px;
}

.car-image {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  background-color: #f5f7fa;
  object-fit: cover;
}

.car-item-middle {
  flex: 1;
}

.car-item-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.car-item-brand {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.car-item-tags {
  display: flex;
  flex-wrap: wrap;
}

.car-item-tag {
  font-size: 12px;
  color: #666;
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
  margin-bottom: 4px;
}

.car-item-tag-more {
  font-size: 12px;
  color: #999;
  padding: 2px 0;
}

.car-item-right {
  text-align: right;
}

.car-item-price {
  font-size: 15px;
  color: #ff4d4f;
  font-weight: 500;
}

/* 查看更多按钮 */
.view-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #f7f9fc;
  border-radius: 8px;
  margin-top: 8px;
}

.view-more-text {
  font-size: 14px;
  color: #0052CC;
}

.view-more-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #0052CC;
}

/* 空数据提示 */
.empty-related-cars {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}

.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}

.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.toolbar-text {
  font-size: 22rpx;
  color: #666;
}

.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}

.share-button::after {
  display: none;
}

.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}

.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}

.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 自定义导航栏样式 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}

.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style> 