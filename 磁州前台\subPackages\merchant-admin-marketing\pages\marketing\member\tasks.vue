<template>
  <view class="tasks-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">会员任务</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 任务概览 -->
    <view class="overview-section">
      <view class="overview-header">
        <text class="section-title">任务概览</text>
        <view class="date-picker" @click="showDatePicker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="stats-cards">
        <view class="stats-card">
          <view class="card-value">{{taskData.totalTasks}}</view>
          <view class="card-label">总任务数</view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{taskData.activeTasks}}</view>
          <view class="card-label">进行中任务</view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{taskData.completionRate}}%</view>
          <view class="card-label">任务完成率</view>
          <view class="card-trend" :class="taskData.completionTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{taskData.completionGrowth}}</text>
          </view>
        </view>
        
        <view class="stats-card">
          <view class="card-value">{{taskData.participationRate}}%</view>
          <view class="card-label">会员参与率</view>
          <view class="card-trend" :class="taskData.participationTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{taskData.participationGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 任务分类标签 -->
    <view class="tabs-section">
      <scroll-view scroll-x class="tabs-scroll" show-scrollbar="false">
        <view class="tabs">
          <view 
            class="tab-item" 
            v-for="(tab, index) in tabs" 
            :key="index"
            :class="{ active: currentTab === index }"
            @click="switchTab(index)"
          >
            {{tab}}
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 任务列表 -->
    <view class="tasks-section">
      <view class="section-header">
        <text class="section-title">{{tabs[currentTab]}}</text>
        <view class="add-btn" @click="createTask">
          <text class="btn-text">添加任务</text>
          <view class="plus-icon"></view>
        </view>
      </view>
      
      <view class="tasks-list">
        <view class="task-item" v-for="(task, index) in filteredTasks" :key="index" @click="editTask(task)">
          <view class="task-icon" :style="{ background: task.iconBg }">
            <svg class="svg-icon" viewBox="0 0 24 24" :style="{ fill: task.iconColor }">
              <path :d="task.iconPath"></path>
            </svg>
          </view>
          <view class="task-content">
            <view class="task-header">
              <text class="task-name">{{task.name}}</text>
              <text class="task-status" :class="task.status">{{task.statusText}}</text>
            </view>
            <view class="task-desc">{{task.description}}</view>
            <view class="task-meta">
              <view class="task-reward">
                <text class="reward-label">奖励: </text>
                <text class="reward-value">{{task.reward}}</text>
              </view>
              <view class="task-period">
                <text class="period-label">周期: </text>
                <text class="period-value">{{task.period}}</text>
              </view>
            </view>
          </view>
          <view class="task-action">
            <switch :checked="task.enabled" @change="(e) => toggleTask(task, e)" color="#F6D365" />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @click="createTask">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateRange: '2023-04-01 ~ 2023-04-30',
      currentTab: 0,
      
      // 任务数据概览
      taskData: {
        totalTasks: 12,
        activeTasks: 8,
        completionRate: 68.5,
        completionTrend: 'up',
        completionGrowth: '5.2%',
        participationRate: 42.3,
        participationTrend: 'up',
        participationGrowth: '3.8%'
      },
      
      // 任务分类标签
      tabs: ['全部任务', '日常任务', '成长任务', '消费任务', '社交任务'],
      
      // 任务列表
      tasks: [
        {
          id: 1,
          name: '每日签到',
          description: '会员每日签到获得积分奖励',
          reward: '5积分/次',
          period: '每日一次',
          category: '日常任务',
          status: 'active',
          statusText: '进行中',
          enabled: true,
          iconPath: 'M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z',
          iconBg: 'rgba(246, 211, 101, 0.1)',
          iconColor: '#F6D365'
        },
        {
          id: 2,
          name: '浏览商品',
          description: '每日浏览5件商品获得积分奖励',
          reward: '10积分/次',
          period: '每日一次',
          category: '日常任务',
          status: 'active',
          statusText: '进行中',
          enabled: true,
          iconPath: 'M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z',
          iconBg: 'rgba(246, 211, 101, 0.1)',
          iconColor: '#F6D365'
        },
        {
          id: 3,
          name: '分享商品',
          description: '分享商品给好友获得积分奖励',
          reward: '15积分/次',
          period: '每日三次',
          category: '社交任务',
          status: 'active',
          statusText: '进行中',
          enabled: true,
          iconPath: 'M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z',
          iconBg: 'rgba(246, 211, 101, 0.1)',
          iconColor: '#F6D365'
        },
        {
          id: 4,
          name: '邀请好友',
          description: '邀请好友注册成为会员',
          reward: '50积分/人',
          period: '长期有效',
          category: '社交任务',
          status: 'active',
          statusText: '进行中',
          enabled: true,
          iconPath: 'M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z',
          iconBg: 'rgba(246, 211, 101, 0.1)',
          iconColor: '#F6D365'
        },
        {
          id: 5,
          name: '完善资料',
          description: '完善个人资料获得一次性奖励',
          reward: '30积分',
          period: '一次性',
          category: '成长任务',
          status: 'active',
          statusText: '进行中',
          enabled: true,
          iconPath: 'M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z',
          iconBg: 'rgba(246, 211, 101, 0.1)',
          iconColor: '#F6D365'
        },
        {
          id: 6,
          name: '首次购物',
          description: '首次在商城购物获得奖励',
          reward: '100积分',
          period: '一次性',
          category: '消费任务',
          status: 'active',
          statusText: '进行中',
          enabled: true,
          iconPath: 'M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2H1zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z',
          iconBg: 'rgba(246, 211, 101, 0.1)',
          iconColor: '#F6D365'
        },
        {
          id: 7,
          name: '商品评价',
          description: '购买商品后评价获得积分奖励',
          reward: '20积分/次',
          period: '每单一次',
          category: '消费任务',
          status: 'active',
          statusText: '进行中',
          enabled: true,
          iconPath: 'M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z',
          iconBg: 'rgba(246, 211, 101, 0.1)',
          iconColor: '#F6D365'
        },
        {
          id: 8,
          name: '连续签到7天',
          description: '连续签到7天获得额外奖励',
          reward: '50积分',
          period: '每周一次',
          category: '成长任务',
          status: 'active',
          statusText: '进行中',
          enabled: true,
          iconPath: 'M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z',
          iconBg: 'rgba(246, 211, 101, 0.1)',
          iconColor: '#F6D365'
        }
      ]
    }
  },
  computed: {
    filteredTasks() {
      if (this.currentTab === 0) {
        return this.tasks;
      } else {
        const category = this.tabs[this.currentTab];
        return this.tasks.filter(task => task.category === category);
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    showHelp() {
      uni.showModal({
        title: '会员任务帮助',
        content: '会员任务是指会员可以通过完成特定任务获得积分或其他奖励的活动，可以提高会员活跃度和忠诚度。',
        showCancel: false
      });
    },
    
    showDatePicker() {
      // 实现日期选择器
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    
    switchTab(index) {
      this.currentTab = index;
    },
    
    createTask() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/member/create-task'
      });
    },
    
    editTask(task) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/member/edit-task?id=${task.id}`
      });
    },
    
    toggleTask(task, e) {
      // 更新任务状态
      const index = this.tasks.findIndex(item => item.id === task.id);
      if (index !== -1) {
        this.tasks[index].enabled = e.detail.value;
        this.tasks[index].status = e.detail.value ? 'active' : 'inactive';
        this.tasks[index].statusText = e.detail.value ? '进行中' : '已暂停';
      }
      
      uni.showToast({
        title: e.detail.value ? `${task.name}已启用` : `${task.name}已禁用`,
        icon: 'none'
      });
    }
  }
}
</script>

<style lang="scss">
.tasks-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #F6D365, #FDA085);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(246, 211, 101, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 通用部分样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  background: #F6D365;
  border-radius: 15px;
  padding: 5px 12px;
  color: white;
}

.btn-text {
  font-size: 13px;
  margin-right: 5px;
}

.plus-icon {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon:before,
.plus-icon:after {
  content: '';
  position: absolute;
  background: white;
}

.plus-icon:before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon:after {
  height: 12px;
  width: 2px;
  left: 5px;
  top: 0;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.stats-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
  position: relative;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  background: #F8FAFC;
  padding: 15px;
  border-radius: 10px;
  border-left: 3px solid #F6D365;
}

.card-label {
  font-size: 12px;
  color: #999;
  position: absolute;
  bottom: 20px;
  left: 25px;
}

.card-trend {
  position: absolute;
  bottom: 20px;
  right: 25px;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}

.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 标签页样式 */
.tabs-section {
  margin: 15px 15px 0;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs {
  display: inline-flex;
  padding: 5px 0;
}

.tab-item {
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.tab-item.active {
  background: #F6D365;
  color: white;
  box-shadow: 0 2px 8px rgba(246, 211, 101, 0.3);
}

/* 任务列表样式 */
.tasks-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.tasks-list {
  margin-top: 10px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background: #F8FAFC;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.task-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.svg-icon {
  width: 28px;
  height: 28px;
}

.task-content {
  flex: 1;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.task-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.task-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.task-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.task-status.inactive {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.task-desc {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}

.task-meta {
  display: flex;
  align-items: center;
}

.task-reward {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.reward-label {
  font-size: 12px;
  color: #999;
}

.reward-value {
  font-size: 12px;
  color: #F6D365;
  font-weight: 500;
}

.task-period {
  display: flex;
  align-items: center;
}

.period-label {
  font-size: 12px;
  color: #999;
}

.period-value {
  font-size: 12px;
  color: #666;
}

.task-action {
  margin-left: 10px;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #F6D365, #FDA085);
  box-shadow: 0 4px 15px rgba(246, 211, 101, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 375px) {
  .stats-card {
    width: 100%;
  }
}
</style> 