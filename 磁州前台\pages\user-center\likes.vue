<template>
  <view class="likes-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">获赞记录</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 获赞记录列表 -->
    <view class="likes-list" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <!-- 无数据提示 -->
      <view class="empty-container" v-if="likesList.length === 0">
        <image src="/static/images/empty.png" class="empty-icon"></image>
        <view class="empty-text">暂无获赞记录</view>
      </view>
      
      <!-- 获赞列表 -->
      <view class="like-item" v-for="(item, index) in likesList" :key="index" @click="goToDetail(item)">
        <!-- 用户头像 -->
        <view class="user-avatar">
          <image :src="item.userAvatar || '/static/images/default-avatar.png'" class="avatar-img" mode="aspectFill"></image>
        </view>
        
        <!-- 点赞信息 -->
        <view class="like-info">
          <view class="like-user">{{ item.userName }} <text class="like-action">赞了你的{{ item.contentType }}</text></view>
          <view class="like-time">{{ item.time }}</view>
          <view class="like-content">{{ item.content }}</view>
        </view>
        
        <!-- 内容预览图 -->
        <view class="content-preview" v-if="item.contentImage">
          <image :src="item.contentImage" class="preview-img" mode="aspectFill"></image>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" v-if="likesList.length > 0 && hasMore">
        <text class="loading-text">加载中...</text>
      </view>
      <view class="no-more" v-if="likesList.length > 0 && !hasMore">
        <text class="no-more-text">没有更多了</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      likesList: [],
      page: 1,
      pageSize: 10,
      hasMore: true,
      isLoading: false
    }
  },
  created() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
  },
  onLoad() {
    this.getLikesList();
  },
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.page++;
      this.getLikesList();
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取获赞列表
    getLikesList() {
      if (this.isLoading) return;
      this.isLoading = true;
      
      // 模拟获取获赞列表数据
      setTimeout(() => {
        // 模拟数据
        const mockData = [
          {
            id: '3001',
            userName: '张三',
            userAvatar: '/static/images/default-avatar.png',
            contentType: '动态',
            content: '今天天气真好，出去走走吧！',
            contentImage: '/static/images/default-avatar.png',
            time: '2023-06-15 10:30',
            contentId: '5001'
          },
          {
            id: '3002',
            userName: '李四',
            userAvatar: '/static/images/default-avatar.png',
            contentType: '评论',
            content: '这个地方我也去过，风景确实不错！',
            contentImage: '',
            time: '2023-06-14 15:45',
            contentId: '5002'
          },
          {
            id: '3003',
            userName: '王五',
            userAvatar: '/static/images/default-avatar.png',
            contentType: '动态',
            content: '分享一张美丽的风景照',
            contentImage: '/static/images/default-avatar.png',
            time: '2023-06-13 09:20',
            contentId: '5003'
          },
          {
            id: '3004',
            userName: '赵六',
            userAvatar: '/static/images/default-avatar.png',
            contentType: '评论',
            content: '非常赞同你的观点！',
            contentImage: '',
            time: '2023-06-12 18:05',
            contentId: '5004'
          },
          {
            id: '3005',
            userName: '钱七',
            userAvatar: '/static/images/default-avatar.png',
            contentType: '动态',
            content: '推荐一家不错的餐厅，味道很赞！',
            contentImage: '/static/images/default-avatar.png',
            time: '2023-06-11 12:30',
            contentId: '5005'
          }
        ];
        
        // 第一页直接赋值，后续页面追加
        if (this.page === 1) {
          this.likesList = mockData;
        } else {
          this.likesList = [...this.likesList, ...mockData];
        }
        
        // 判断是否还有更多数据
        this.hasMore = this.page < 3; // 模拟只有3页数据
        this.isLoading = false;
      }, 500);
    },
    
    // 跳转到内容详情
    goToDetail(item) {
      let url = '';
      
      // 根据内容类型跳转到不同页面
      if (item.contentType === '动态') {
        url = `/pages/publish/info-detail?id=${item.contentId}`;
      } else if (item.contentType === '评论') {
        url = `/pages/publish/info-detail?id=${item.contentId}&showComment=1`;
      }
      
      if (url) {
        uni.navigateTo({ url });
      }
    }
  }
}
</script>

<style>
.likes-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 获赞列表样式 */
.likes-list {
  padding: 20rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 获赞列表项 */
.like-item {
  display: flex;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 40rpx;
}

.like-info {
  flex: 1;
  overflow: hidden;
}

.like-user {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.like-action {
  font-weight: normal;
  color: #666;
}

.like-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.like-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content-preview {
  width: 120rpx;
  height: 120rpx;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.preview-img {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

/* 加载更多 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text, .no-more-text {
  font-size: 24rpx;
  color: #999;
}
</style> 