<template>
  <view class="activity-management">
    <!-- 状态栏高度占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-left" @tap.stop="goBack">
        <view class="back-btn">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
      <view class="navbar-title">活动管理</view>
      <view class="navbar-right">
        <view class="navbar-action" @tap.stop="showMoreOptions">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z" fill="white" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z" fill="white" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z" fill="white" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 页面内容区域 -->
    <scroll-view class="content-area" scroll-y refresher-enabled 
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh">
      
      <!-- 活动统计卡片 -->
      <view class="statistics-card">
        <view class="statistics-item">
          <text class="statistics-value">{{statistics.total}}</text>
          <text class="statistics-label">活动总数</text>
          </view>
        <view class="statistics-divider"></view>
        <view class="statistics-item">
          <text class="statistics-value">{{statistics.running}}</text>
          <text class="statistics-label">进行中</text>
          </view>
        <view class="statistics-divider"></view>
        <view class="statistics-item">
          <text class="statistics-value">{{statistics.upcoming}}</text>
          <text class="statistics-label">未开始</text>
          </view>
        <view class="statistics-divider"></view>
        <view class="statistics-item">
          <text class="statistics-value">{{statistics.ended}}</text>
          <text class="statistics-label">已结束</text>
        </view>
      </view>
      
      <!-- 筛选标签栏 -->
      <view class="filter-section">
        <!-- 活动类型选择 -->
        <view class="filter-category">
          <view class="category-title">活动类型</view>
          <scroll-view class="filter-tabs" scroll-x show-scrollbar="false">
            <view 
              class="filter-tab" 
              v-for="(type, index) in activityTypes" 
              :key="'type-' + index"
              :class="{ active: currentType === type.value }"
              @tap.stop="switchType(type.value)"
            >
              <text>{{type.label}}</text>
          </view>
          </scroll-view>
          </view>
        
        <!-- 活动状态选择 -->
        <view class="filter-category">
          <view class="category-title">活动状态</view>
          <scroll-view class="filter-tabs" scroll-x show-scrollbar="false">
            <view 
              class="filter-tab" 
              v-for="(tab, index) in filterTabs" 
              :key="'status-' + index"
              :class="{ active: currentTab === tab.value }"
              @tap.stop="switchTab(tab.value)"
            >
              <text>{{tab.label}}</text>
          </view>
          </scroll-view>
        </view>
      </view>
      
      <!-- 活动列表占位 -->
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 活动列表 -->
      <view v-else>
        <!-- 空状态 -->
        <view v-if="activityList.length === 0" class="empty-container">
          <image src="/static/images/tabbar/无数据.png" class="empty-image"></image>
          <text class="empty-text">暂无活动数据</text>
          <view class="empty-action" @tap.stop="createNewActivity">
            <text>立即创建</text>
          </view>
        </view>
        
        <!-- 活动卡片列表 -->
        <view v-else class="activity-list">
          <view 
            v-for="(item, index) in activityList" 
            :key="index" 
            class="activity-card"
            @tap.stop="viewActivityDetail(item.id)"
          >
            <!-- 顶部标签和状态 -->
            <view class="card-header">
              <view class="status-tags">
                <view class="type-tag" :class="'type-' + item.type">
                  <text>{{ getTypeText(item.type) }}</text>
                </view>
                <view class="status-tag" :class="'status-' + item.status">
                  <text>{{ getStatusText(item.status) }}</text>
                </view>
                <view v-if="item.isTop" class="top-tag">
                  <text>已置顶</text>
                </view>
          </view>
        </view>
        
            <!-- 卡片内容 -->
            <view class="card-content">
              <!-- 活动封面 -->
              <view class="card-image">
                <image :src="item.coverImage" mode="aspectFill"></image>
            </view>
              
              <!-- 活动信息 -->
              <view class="card-info">
                <text class="activity-title">{{ item.title }}</text>
                <view class="activity-time">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <text>{{ formatDateRange(item.startTime, item.endTime) }}</text>
                </view>
              <view class="activity-stats">
                <view class="stat-item">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M2.45825 12C3.73253 7.94288 7.52281 5 12.0004 5C16.4781 5 20.2684 7.94291 21.5426 12C20.2684 16.0571 16.4781 19 12.0005 19C7.52281 19 3.73251 16.0571 2.45825 12Z" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <text>{{ item.views }}</text>
                </view>
                <view class="stat-item">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17 21V19C17 16.7909 15.2091 15 13 15H5C2.79086 15 1 16.7909 1 19V21M23 21V19C22.9986 17.1771 21.765 15.5857 20 15.13M16 3.13C17.7699 3.58317 19.0078 5.17799 19.0078 7.005C19.0078 8.83201 17.7699 10.4268 16 10.88M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <text>{{ item.participants }}</text>
                </view>
                </view>
              </view>
                </view>
            
            <!-- 卡片操作按钮 -->
            <view class="card-actions">
              <view class="action-button edit" @tap.stop="editActivity(item.id)">
                <text>编辑</text>
              </view>
              <view class="action-button promote" @tap.stop="promoteActivity(item.id)">
                <text>推广</text>
            </view>
              <view class="action-button share" @tap.stop="shareActivity(item.id)">
                <text>转发</text>
          </view>
              <view class="action-button more" @tap.stop="showActivityOptions(item.id)">
                <text>更多</text>
        </view>
            </view>
        </view>
      </view>
      
        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more" @tap.stop="loadMoreActivities">
          <text>加载更多</text>
        </view>
        <view v-else-if="activityList.length > 0" class="no-more">
          <text>已加载全部数据</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 发布按钮 - 去掉图标，缩小尺寸 -->
    <view class="fab-button" @tap.stop="createNewActivity">
      <text class="fab-text">发布</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20, // 默认值，将在onLoad中获取真实高度
      refreshing: false,
      loading: true,
      currentTab: 'all',
      currentType: 'all', // 当前选中的活动类型
      hasMore: true,
      page: 1,
      
      // 活动统计数据
      statistics: {
        total: 8,
        running: 3,
        upcoming: 1,
        ended: 4
      },
      
      // 筛选标签
      filterTabs: [
        { label: '全部', value: 'all' },
        { label: '进行中', value: 'running' },
        { label: '未开始', value: 'upcoming' },
        { label: '已结束', value: 'ended' },
        { label: '最近发布', value: 'recent' },
        { label: '高参与度', value: 'popular' }
      ],
      
      // 活动类型选项
      activityTypes: [
        { label: '全部类型', value: 'all' },
        { label: '团购活动', value: 'groupon' },
        { label: '优惠券', value: 'coupon' },
        { label: '秒杀活动', value: 'seckill' },
        { label: '满减活动', value: 'discount' },
        { label: '积分兑换', value: 'points' }
      ],
      
      // 活动列表数据
      activityList: [
        {
          id: 1,
          title: '夏季特惠，全场满300减50',
          coverImage: '/static/images/activity-1.jpg',
          startTime: '2023-07-01',
          endTime: '2023-08-31',
          status: 'running',
          views: 1258,
          participants: 78,
          isTop: true,
          type: 'discount' // 活动类型：满减活动
        },
        {
          id: 2,
          title: '开业庆典，免费品尝活动',
          coverImage: '/static/images/activity-2.jpg',
          startTime: '2023-06-15',
          endTime: '2023-06-20',
          status: 'ended',
          views: 876,
          participants: 126,
          isTop: false,
          type: 'coupon' // 活动类型：优惠券
        },
        {
          id: 3,
          title: '周年庆典，抽奖赢大礼',
          coverImage: '/static/images/activity-3.jpg',
          startTime: '2023-12-01',
          endTime: '2023-12-10',
          status: 'upcoming',
          views: 322,
          participants: 0,
          isTop: false,
          type: 'groupon' // 活动类型：团购活动
        }
      ]
    }
  },
  onLoad() {
    this.getStatusBarHeight();
    this.loadActivityData();
  },
  methods: {
    // 获取状态栏高度
    getStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
        }
      });
    },
    // 返回上一页
    goBack() {
      console.log('返回按钮点击');
      uni.navigateBack();
    },
    // 显示更多选项
    showMoreOptions() {
      console.log('更多选项按钮点击');
      uni.showActionSheet({
        itemList: ['批量管理', '活动统计', '帮助中心'],
        success: (res) => {
          // 根据选择执行不同操作
          console.log('选择了: ', res.tapIndex);
        }
      });
    },
    // 创建新活动
    createNewActivity() {
      console.log('创建新活动按钮点击');
      
      // 显示交互反馈
      uni.showLoading({
        title: '正在跳转...',
        mask: true
      });
      
      // 延迟处理，确保视觉效果
      setTimeout(() => {
        uni.hideLoading();
        
        // 跳转到正确的营销中心页面
        uni.navigateTo({
          url: '/subPackages/merchant-admin-marketing/pages/marketing/index',
          success: () => {
            console.log('跳转到营销中心成功');
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            uni.showModal({
              title: '提示',
              content: '无法访问营销中心页面',
              showCancel: false
            });
          }
        });
      }, 300);
    },
    // 下拉刷新
    onRefresh() {
      console.log('下拉刷新触发');
      this.refreshing = true;
      this.page = 1;
      this.hasMore = true;
      this.loadActivityData();
    },
    // 加载活动数据
    loadActivityData() {
      this.loading = true;
      
      // 模拟加载数据
      setTimeout(() => {
        // 如果是第一页，清空现有数据
        if (this.page === 1) {
          // 模拟根据类型和状态筛选数据
          const filteredList = this.getFilteredActivities();
          this.activityList = filteredList;
        } else {
          // 模拟加载更多数据
          // 实际项目中这里应该调用API获取更多数据
          console.log('加载更多数据，页码：', this.page);
        }
        
        this.loading = false;
        this.refreshing = false;
        
        // 模拟没有更多数据
        if (this.page > 1) {
          this.hasMore = false;
        }
      }, 1000);
    },
    // 获取筛选后的活动列表
    getFilteredActivities() {
      // 模拟从服务器获取的完整活动列表
      const allActivities = [
        {
          id: 1,
          title: '夏季特惠，全场满300减50',
          coverImage: '/static/images/activity-1.jpg',
          startTime: '2023-07-01',
          endTime: '2023-08-31',
          status: 'running',
          views: 1258,
          participants: 78,
          isTop: true,
          type: 'discount'
        },
        {
          id: 2,
          title: '开业庆典，免费品尝活动',
          coverImage: '/static/images/activity-2.jpg',
          startTime: '2023-06-15',
          endTime: '2023-06-20',
          status: 'ended',
          views: 876,
          participants: 126,
          isTop: false,
          type: 'coupon'
        },
        {
          id: 3,
          title: '周年庆典，抽奖赢大礼',
          coverImage: '/static/images/activity-3.jpg',
          startTime: '2023-12-01',
          endTime: '2023-12-10',
          status: 'upcoming',
          views: 322,
          participants: 0,
          isTop: false,
          type: 'groupon'
        },
        {
          id: 4,
          title: '限时秒杀，爆款5折起',
          coverImage: '/static/images/activity-1.jpg',
          startTime: '2023-08-15',
          endTime: '2023-08-17',
          status: 'running',
          views: 1890,
          participants: 245,
          isTop: false,
          type: 'seckill'
        },
        {
          id: 5,
          title: '会员积分兑换活动',
          coverImage: '/static/images/activity-2.jpg',
          startTime: '2023-07-20',
          endTime: '2023-09-20',
          status: 'running',
          views: 768,
          participants: 124,
          isTop: false,
          type: 'points'
        }
      ];
      
      // 根据类型和状态筛选
      return allActivities.filter(activity => {
        // 类型筛选
        const typeMatch = this.currentType === 'all' || activity.type === this.currentType;
        
        // 状态筛选
        let statusMatch = true;
        if (this.currentTab === 'running') {
          statusMatch = activity.status === 'running';
        } else if (this.currentTab === 'upcoming') {
          statusMatch = activity.status === 'upcoming';
        } else if (this.currentTab === 'ended') {
          statusMatch = activity.status === 'ended';
        }
        
        return typeMatch && statusMatch;
      });
    },
    // 加载更多活动
    loadMoreActivities() {
      console.log('加载更多按钮点击');
      if (!this.hasMore) return;
      
      this.page++;
      this.loadActivityData();
    },
    // 切换标签
    switchTab(tabValue) {
      console.log('切换状态标签：', tabValue);
      this.currentTab = tabValue;
      this.page = 1;
      this.hasMore = true;
      this.loadActivityData();
    },
    // 显示排序选项
    showSortOptions() {
      console.log('显示排序选项');
      uni.showActionSheet({
        itemList: ['最新发布', '即将开始', '即将结束', '参与人数'],
        success: (res) => {
          uni.showToast({
            title: '排序方式已更改',
            icon: 'none'
          });
        }
      });
    },
    // 显示筛选选项
    showFilterOptions() {
      console.log('显示筛选选项');
        uni.navigateTo({
        url: '/subPackages/merchant-admin/pages/activity/filter'
        });
    },
    // 获取活动状态文本
    getStatusText(status) {
      const statusMap = {
        'running': '进行中',
        'ended': '已结束',
        'upcoming': '未开始'
      };
      return statusMap[status] || '未知状态';
    },
    // 格式化日期范围
    formatDateRange(start, end) {
      if (!start || !end) return '';
      
      const startDate = new Date(start);
      const endDate = new Date(end);
      
      const startMonth = startDate.getMonth() + 1;
      const startDay = startDate.getDate();
      const endMonth = endDate.getMonth() + 1;
      const endDay = endDate.getDate();
      
      return `${startMonth}.${startDay} - ${endMonth}.${endDay}`;
    },
    // 查看活动详情
    viewActivityDetail(id) {
      console.log('查看活动详情，ID：', id);
      uni.navigateTo({
        url: `/subPackages/merchant-admin/pages/activity/detail?id=${id}`
      });
    },
    // 编辑活动
    editActivity(id) {
      console.log('编辑活动按钮点击，ID：', id);
      uni.navigateTo({
        url: `/subPackages/merchant-admin/pages/activity/edit?id=${id}`
      });
    },
    // 推广活动
    promoteActivity(id) {
      console.log('推广活动按钮点击，ID：', id);
      // 找到当前活动
      const activity = this.activityList.find(item => item.id === id);
      
      // 根据当前置顶状态显示不同选项
      const topAction = activity && activity.isTop ? '取消置顶' : '置顶活动';
      
      uni.showActionSheet({
        itemList: [topAction, '推荐到首页', '短信推广', '分享到群', '朋友圈宣传'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 切换置顶状态
            this.toggleTopStatus(id);
          } else {
            uni.showToast({
              title: '推广设置成功',
              icon: 'success'
            });
          }
        }
      });
    },
    // 切换活动置顶状态
    toggleTopStatus(id) {
      const index = this.activityList.findIndex(item => item.id === id);
      if (index !== -1) {
        // 切换置顶状态
        const isTop = !this.activityList[index].isTop;
        this.activityList[index].isTop = isTop;
        
        // 显示成功提示
        uni.showToast({
          title: isTop ? '活动已置顶' : '已取消置顶',
          icon: 'success'
        });
      }
    },
      // 分享活动
    shareActivity(id) {
      console.log('分享活动按钮点击，ID：', id);
      uni.showToast({
        title: '已打开分享菜单',
        icon: 'none',
        duration: 1500
      });
      
      // 注释掉原来的分享API调用，使用showShareMenu代替
      setTimeout(() => {
        uni.showToast({
          title: '活动已分享',
          icon: 'success'
        });
      }, 1500);
    },
    // 显示活动更多选项
    showActivityOptions(id) {
      console.log('更多按钮点击，ID：', id);
      uni.showActionSheet({
        itemList: ['查看数据', '复制活动', '下架活动', '删除活动'],
        success: (res) => {
          // 根据选择执行不同操作
          const actions = ['viewData', 'duplicateActivity', 'stopActivity', 'deleteActivity'];
          const action = actions[res.tapIndex];
          
          if (action && this[action]) {
            this[action](id);
          }
        }
      });
    },
    // 查看数据
    viewData(id) {
      console.log('查看数据，ID：', id);
      uni.navigateTo({
        url: `/subPackages/merchant-admin/pages/activity/data?id=${id}`
      });
    },
    // 复制活动
    duplicateActivity(id) {
      console.log('复制活动，ID：', id);
      uni.showModal({
        title: '复制活动',
        content: '确定要复制该活动吗？',
        success: (res) => {
          if (res.confirm) {
          uni.showToast({
              title: '活动复制成功',
            icon: 'success'
          });
          }
        }
      });
    },
    // 下架活动
    stopActivity(id) {
      console.log('下架活动，ID：', id);
      uni.showModal({
        title: '下架活动',
        content: '确定要下架该活动吗？',
        success: (res) => {
          if (res.confirm) {
          uni.showToast({
              title: '活动已下架',
            icon: 'success'
          });
        }
        }
      });
    },
    // 删除活动
    deleteActivity(id) {
      console.log('删除活动，ID：', id);
      uni.showModal({
        title: '删除活动',
        content: '确定要删除该活动吗？删除后不可恢复',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '活动已删除',
              icon: 'success'
            });
          }
        }
      });
    },
    // 切换活动类型
    switchType(typeValue) {
      console.log('切换活动类型：', typeValue);
      this.currentType = typeValue;
      this.page = 1;
      this.hasMore = true;
      this.loadActivityData();
    },
    // 获取活动类型文本
    getTypeText(type) {
      const typeMap = {
        'groupon': '团购活动',
        'coupon': '优惠券',
        'seckill': '秒杀活动',
        'discount': '满减活动',
        'points': '积分兑换'
      };
      return typeMap[type] || '未知类型';
    }
  }
}
</script>

<style lang="scss">
.activity-management {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 状态栏样式 */
.status-bar {
  background: linear-gradient(135deg, #007AFF, #5856D6);
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1001;
}

/* 导航栏样式 */
.navbar {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  margin-top: v-bind('statusBarHeight + "px"');
  box-shadow: 0 2px 10px rgba(0, 122, 255, 0.2);
}

.navbar-left, .navbar-right {
  display: flex;
  align-items: center;
  min-width: 32px;
}

/* 添加返回按钮样式 */
.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.2s ease;
  
  &:active {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(0.92);
  }
  
  svg {
    width: 20px;
    height: 20px;
  }
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.navbar-action {
  padding: 4px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  padding: 16px;
  margin-top: v-bind('statusBarHeight + 44 + "px"'); /* 状态栏+导航栏高度 */
  height: v-bind('`calc(100vh - ${statusBarHeight + 44}px)`');
}

/* 统计卡片 */
.statistics-card {
  display: flex;
  justify-content: space-between;
  background-color: white;
  border-radius: 16px;
  padding: 20px 10px;
  margin: 0 auto 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 92%;
  transform: translateZ(0);
  position: relative;
  overflow: hidden;
}

.statistics-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.8);
  z-index: 1;
  pointer-events: none;
}

.statistics-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.statistics-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.statistics-label {
  font-size: 12px;
  color: #666;
}

.statistics-divider {
  width: 1px;
  height: 30px;
  background-color: #eee;
}

/* 筛选区域 */
.filter-section {
  margin: 0 auto 16px;
  width: 92%; /* 减小宽度，确保边缘有足够的间距 */
  background-color: #fff;
  border-radius: 12px;
  padding: 14px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.filter-category {
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.category-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  padding-left: 4px;
}

.filter-tabs {
  white-space: nowrap;
  padding-right: 16px;
  margin-bottom: 4px;
  overflow-x: auto;
}

.filter-tabs::-webkit-scrollbar {
  display: none;
}

.filter-tab {
  display: inline-block;
  padding: 6px 16px;
  margin-right: 8px;
  background-color: #F2F2F7;
  border-radius: 20px;
  font-size: 13px;
  color: #666;
  transition: all 0.2s ease;
  
  &:last-child {
    margin-right: 0;
  }
  
  &.active {
    background-color: #007AFF;
    color: white;
  font-weight: 500;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

/* 活动列表 */
.activity-list {
  margin: 0 auto 16px;
  width: 96%;
}

.activity-card {
  background-color: white;
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  position: relative;
  transform: translateZ(0);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  width: 100%;
}

.activity-card:active {
  transform: scale(0.98) translateZ(0);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.activity-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8), transparent);
  z-index: 1;
}

.card-header {
  display: flex;
  justify-content: center;
  padding: 12px 16px 0;
}

.status-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
  gap: 6px;
}

.status-tag, .type-tag, .top-tag {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.type-tag {
  background-color: #F2F2F7;
  color: #8E8E93;
}

.type-groupon {
  background-color: rgba(125, 122, 255, 0.1);
  color: #7D7AFF;
}

.type-coupon {
  background-color: rgba(255, 69, 58, 0.1);
  color: #FF453A;
}

.type-seckill {
  background-color: rgba(255, 55, 95, 0.1);
  color: #FF375F;
}

.type-discount {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.type-points {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.card-content {
  display: flex;
  padding: 16px 20px;
}

.card-image {
  width: 100px;
  height: 100px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;
}

.card-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 17px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.activity-time {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
}

.activity-time svg {
  margin-right: 4px;
}

.activity-stats {
  display: flex;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 13px;
  color: #666;
}

.stat-item svg {
  margin-right: 4px;
}

.card-actions {
  display: flex;
  border-top: 1px solid #f5f5f5;
  padding: 12px 16px;
  justify-content: space-around;
  gap: 10px;
}

.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  color: white;
  padding: 6px 14px;
  border-radius: 18px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  font-weight: 500;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  &.edit {
    background: linear-gradient(135deg, #0A84FF, #0060DF);
  }
  
  &.promote {
    background: linear-gradient(135deg, #FF9500, #E66000);
  }
  
  &.share {
    background: linear-gradient(135deg, #34C759, #28A745);
  }
  
  &.more {
    background: linear-gradient(135deg, #8E8E93, #636366);
  }
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  width: 92%;
  margin: 0 auto;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}

.empty-action {
  padding: 8px 20px;
  background-color: #0A84FF;
  color: white;
  border-radius: 20px;
  font-size: 14px;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  width: 92%;
  margin: 0 auto;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0A84FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载更多和没有更多 */
.load-more, .no-more {
  text-align: center;
  padding: 16px 0;
  font-size: 14px;
  color: #999;
  width: 92%;
  margin: 0 auto;
}

.load-more {
  color: #0A84FF;
}

/* 浮动按钮 */
.fab-button {
  position: fixed;
  right: 24px;
  bottom: 24px;
  width: 52px;
  height: 36px;
  border-radius: 18px;
  background: linear-gradient(135deg, #0A84FF, #0060DF);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(10, 132, 255, 0.5);
  z-index: 1000;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  
  /* 移除之前的after伪元素 */
  &::after {
    content: none;
  }
  
  /* 添加激活状态 */
  &:active {
    transform: scale(0.92);
    box-shadow: 0 2px 8px rgba(10, 132, 255, 0.3);
    background: linear-gradient(135deg, #0060DF, #0A84FF);
  }
  
  /* 发布文字样式 */
  .fab-text {
    font-size: 14px;
    color: white;
    font-weight: 500;
    line-height: 1;
    text-align: center;
  }
}

/* 为置顶卡片添加特殊样式 */
.activity-card {
  &:has(.top-tag) {
    border-left: 3px solid #FF9500;
  }
}

.status-running {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.status-upcoming {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.status-ended {
  background-color: rgba(142, 142, 147, 0.1);
    color: #8E8E93;
  }

.top-tag {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
</style> 
