<view class="second-hand-detail-container"><view class="custom-navbar" style="{{'padding-top:' + c}}"><view class="navbar-left" bindtap="{{b}}"><image src="{{a}}" class="back-icon"></image></view><view class="navbar-title">二手闲置详情</view><view class="navbar-right"></view></view><button id="shareButton" class="hidden-share-btn" open-type="share"></button><view class="second-hand-detail-wrapper"><view class="content-card goods-info-card"><view class="goods-header"><view class="goods-title-row"><text class="goods-title">{{d}}</text><text class="goods-price">{{e}}</text></view><view class="goods-meta"><view class="goods-tag-group"><view wx:for="{{f}}" wx:for-item="tag" wx:key="b" class="goods-tag">{{tag.a}}</view></view><text class="goods-publish-time">发布于 {{g}}</text></view></view><swiper class="goods-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}"><swiper-item wx:for="{{h}}" wx:for-item="image" wx:key="b"><image src="{{image.a}}" mode="aspectFill" class="goods-image"></image></swiper-item></swiper><view class="goods-basic-info"><view class="info-item"><text class="info-label">商品类型</text><text class="info-value">{{i}}</text></view><view class="info-item"><text class="info-label">新旧程度</text><text class="info-value">{{j}}</text></view><view class="info-item"><text class="info-label">购买时间</text><text class="info-value">{{k}}</text></view><view class="info-item"><text class="info-label">交易地点</text><text class="info-value">{{l}}</text></view></view></view><view class="content-card description-card"><view class="section-title">商品描述</view><view class="description-content"><text class="description-text">{{m}}</text></view></view><view class="content-card details-card"><view class="section-title">商品详情</view><view class="details-list"><view wx:for="{{n}}" wx:for-item="item" wx:key="c" class="details-item"><text class="details-label">{{item.a}}</text><text class="details-value">{{item.b}}</text></view></view></view><view class="content-card trade-card"><view class="section-title">交易方式</view><view class="trade-list"><view wx:for="{{o}}" wx:for-item="item" wx:key="d" class="trade-item"><text class="{{['trade-icon', 'iconfont', item.a]}}"></text><view class="trade-info"><text class="trade-title">{{item.b}}</text><text class="trade-desc">{{item.c}}</text></view></view></view></view><view class="content-card seller-card"><view class="seller-header"><view class="seller-avatar"><image src="{{p}}" mode="aspectFill"></image></view><view class="seller-info"><text class="seller-name">{{q}}</text><view class="seller-meta"><text class="seller-type">{{r}}</text><text class="seller-rating">信用等级 {{s}}</text></view></view><view wx:if="{{t}}" class="seller-auth"><text class="iconfont icon-verified"></text><text class="auth-text">已认证</text></view></view></view><view class="content-card contact-card"><view class="contact-header"><text class="card-title">联系方式</text></view><view class="contact-content"><view class="contact-item"><text class="contact-label">联系人</text><text class="contact-value">{{v}}</text></view><view class="contact-item"><text class="contact-label">电话</text><text class="contact-value contact-phone" bindtap="{{x}}">{{w}}</text></view><view class="contact-tips"><text class="tips-icon iconfont icon-info"></text><text class="tips-text">请说明在"磁州生活网"看到的信息</text></view></view></view><report-card u-i="db452eca-0" bind:__l="__l"></report-card><view class="content-card related-goods-card"><view class="section-title">相关物品推荐</view><view class="related-goods-content"><view class="related-goods-list"><view wx:for="{{y}}" wx:for-item="item" wx:key="h" class="related-goods-item" bindtap="{{item.i}}"><view class="goods-item-content"><view class="goods-item-left"><image class="goods-image" src="{{item.a}}" mode="aspectFill"></image></view><view class="goods-item-middle"><text class="goods-item-title">{{item.b}}</text><view class="goods-item-condition">{{item.c}}</view><view class="goods-item-tags"><text wx:for="{{item.d}}" wx:for-item="tag" wx:key="b" class="goods-item-tag">{{tag.a}}</text><text wx:if="{{item.e}}" class="goods-item-tag-more">+{{item.f}}</text></view></view><view class="goods-item-right"><text class="goods-item-price">{{item.g}}</text></view></view></view><view wx:if="{{z}}" class="empty-related-goods"><image src="{{A}}" class="empty-image" mode="aspectFit"></image><text class="empty-text">暂无相关物品</text></view></view><view wx:if="{{B}}" class="view-more-btn" catchtap="{{C}}"><text class="view-more-text">查看更多二手物品</text><text class="view-more-icon iconfont icon-right"></text></view></view></view></view><view class="interaction-toolbar"><view class="toolbar-item" bindtap="{{E}}"><image src="{{D}}" class="toolbar-icon"></image><text class="toolbar-text">首页</text></view><view class="toolbar-item" bindtap="{{G}}"><image src="{{F}}" class="toolbar-icon"></image><text class="toolbar-text">收藏</text></view><button class="share-button toolbar-item" open-type="share"><image src="{{H}}" class="toolbar-icon"></image><text class="toolbar-text">分享</text></button><view class="toolbar-item" bindtap="{{J}}"><image src="{{I}}" class="toolbar-icon"></image><text class="toolbar-text">私信</text></view><view class="toolbar-item call-button" bindtap="{{K}}"><view class="call-button-content"><text class="call-text">打电话</text><text class="call-subtitle">请说在磁州生活网看到的</text></view></view></view></view>