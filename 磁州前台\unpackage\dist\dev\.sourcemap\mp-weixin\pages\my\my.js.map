{"version": 3, "file": "my.js", "sources": ["pages/my/my.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvbXkudnVl"], "sourcesContent": ["<template>\n\t<view class=\"my-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-navbar\">\n\t\t\t<view class=\"navbar-title\">我的</view>\n\t\t\t<view class=\"navbar-right\">\n\t\t\t\t<!-- 预留位置与发布页面保持一致 -->\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 用户信息区域 -->\n\t\t<view class=\"user-card\" @click=\"handleUserCardClick\">\n\t\t\t<image class=\"avatar\" :src=\"userInfo.avatar || '/static/images/default-avatar.png'\"></image>\n\t\t\t<view class=\"user-info\">\n\t\t\t\t<text class=\"user-name\">{{ userInfo.nickname || '微信用户' }}</text>\n\t\t\t\t<text class=\"user-desc\">{{ userInfo.nickname ? '欢迎来到磁州同城' : '点击授权登录，享受更多服务' }}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 用户授权弹窗 -->\n\t\t<view class=\"auth-popup\" v-if=\"showAuthPopup\" @click=\"closeAuthPopup\">\n\t\t\t<view class=\"auth-container\" @click.stop>\n\t\t\t\t<view class=\"auth-header\">\n\t\t\t\t\t<text class=\"auth-title\">微信授权</text>\n\t\t\t\t\t<view class=\"close-btn\" @click=\"closeAuthPopup\">×</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"auth-content\">\n\t\t\t\t\t<image class=\"auth-logo\" src=\"/static/images/cizhou-logo.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t<text class=\"auth-desc\">磁州同城申请获取您的头像和昵称</text>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 用户授权组件 -->\n\t\t\t\t\t<UserAuth \n\t\t\t\t\t\tbuttonText=\"一键授权\" \n\t\t\t\t\t\t@update:userInfo=\"onUserInfoUpdated\">\n\t\t\t\t\t\t<view class=\"auth-success\">\n\t\t\t\t\t\t\t<text class=\"success-icon\">✓</text>\n\t\t\t\t\t\t\t<text class=\"success-text\">授权成功</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</UserAuth>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 我的服务区域 -->\n\t\t<view class=\"section-card\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">我的服务</text>\n\t\t\t</view>\n\t\t\t<view class=\"service-grid\">\n\t\t\t\t<!-- 第一排 -->\n\t\t\t\t<view class=\"service-row\">\n\t\t\t\t\t<!-- 活动中心 (直接实现而不是使用组件) -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/activity-showcase/pages/index/index')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap activity-icon-wrap has-badge\">\n\t\t\t\t\t\t\t<view class=\"activity-icon-bg\">\n\t\t\t\t\t\t\t\t<svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\">\n\t\t\t\t\t\t\t\t\t<path d=\"M512 85.333c-235.52 0-426.667 191.147-426.667 426.667S276.48 938.667 512 938.667 938.667 747.52 938.667 512 747.52 85.333 512 85.333zm0 768c-188.16 0-341.333-153.173-341.333-341.333S323.84 170.667 512 170.667 853.333 323.84 853.333 512 700.16 853.333 512 853.333z\" fill=\"#FFFFFF\"></path>\n\t\t\t\t\t\t\t\t\t<path d=\"M512 256c-23.68 0-42.667 19.2-42.667 42.667v213.333c0 11.52 4.693 22.187 12.373 29.867l149.333 149.333c16.64 16.64 43.733 16.64 60.373 0 16.64-16.64 16.64-43.733 0-60.373L554.667 494.08V298.667c0-23.467-18.987-42.667-42.667-42.667z\" fill=\"#FFFFFF\"></path>\n\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"badge\">热门</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">活动中心</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 我的福利 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/user/pages/my-benefits')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap benefits-icon-wrap has-badge\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/福利.png\"></image>\n\t\t\t\t\t\t\t<text class=\"badge\">6</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">我的福利</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 每日签到 (使用新每日签到系统) -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/checkin/pages/points')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap points-icon-wrap\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/每日签到.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">每日签到</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 我的钱包 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/payment/pages/wallet')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/我的钱包.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">我的钱包</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 个人主页 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/pages/user-center/profile')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/我的主页.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">个人主页</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t<!-- 第二排 -->\n\t\t\t\t<view class=\"service-row\">\n\t\t\t\t\t<!-- 返利商城 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateToCashback\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap cashback-icon-wrap has-badge\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/钱包.png\"></image>\n\t\t\t\t\t\t\t<text class=\"badge\">新</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">返利商城</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 我的消息 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/user/pages/messages')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap has-badge\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/消息.png\"></image>\n\t\t\t\t\t\t\t<text class=\"badge\">3</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">我的消息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t\t<!-- 团购订单 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/user/pages/group-orders')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap has-badge\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/团购.png\"></image>\n\t\t\t\t\t\t\t<text class=\"badge\">2</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">团购订单</text>\n\t\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t\t<!-- 我的发布 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/pages/my/publish')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap has-badge\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/发布.png\"></image>\n\t\t\t\t\t\t\t<text class=\"badge\">3</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">我的发布</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 第三排 -->\n\t\t\t\t<view class=\"service-row\">\n\t\t\t\t\t<!-- 商家后台 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/merchant-admin-home/pages/merchant-home/index')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/规则说明.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">商家后台</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 进群服务 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/pages/group/group')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/chat-grey.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">进群</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 我的工具区域 -->\n\t\t<view class=\"section-card\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">我的工具</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 收藏/关注 -->\n\t\t\t<view class=\"tool-item\" @click=\"navigateTo('/subPackages/user/pages/favorites')\">\n\t\t\t\t<view class=\"tool-left\">\n\t\t\t\t\t<image class=\"tool-icon\" src=\"/static/images/tabbar/收藏关注.png\"></image>\n\t\t\t\t\t<text class=\"tool-name\">收藏/关注</text>\n\t\t\t\t</view>\n\t\t\t\t<image class=\"arrow-icon\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 接打记录 -->\n\t\t\t<view class=\"tool-item\" @click=\"navigateTo('/subPackages/user/pages/call-history')\">\n\t\t\t\t<view class=\"tool-left\">\n\t\t\t\t\t<image class=\"tool-icon\" src=\"/static/images/tabbar/拨打记录.png\"></image>\n\t\t\t\t\t<text class=\"tool-name\">接打记录</text>\n\t\t\t\t</view>\n\t\t\t\t<image class=\"arrow-icon\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 浏览记录 -->\n\t\t\t<view class=\"tool-item\" @click=\"navigateTo('/subPackages/user/pages/history')\">\n\t\t\t\t<view class=\"tool-left\">\n\t\t\t\t\t<image class=\"tool-icon\" src=\"/static/images/tabbar/历史记录.png\"></image>\n\t\t\t\t\t<text class=\"tool-name\">浏览记录</text>\n\t\t\t\t</view>\n\t\t\t\t<image class=\"arrow-icon\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 我的卡券 -->\n\t\t\t<view class=\"tool-item\" @click=\"navigateTo('/subPackages/service/pages/coupon')\">\n\t\t\t\t<view class=\"tool-left\">\n\t\t\t\t\t<image class=\"tool-icon\" src=\"/static/images/tabbar/卡券.png\"></image>\n\t\t\t\t\t<text class=\"tool-name\">我的卡券</text>\n\t\t\t\t</view>\n\t\t\t\t<image class=\"arrow-icon\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 刷新套餐 -->\n\t\t\t<view class=\"tool-item\" @click=\"goToRefreshPackage\">\n\t\t\t\t<view class=\"tool-left\">\n\t\t\t\t\t<image class=\"tool-icon\" src=\"/static/images/tabbar/套餐.png\"></image>\n\t\t\t\t\t<text class=\"tool-name\">刷新套餐</text>\n\t\t\t\t</view>\n\t\t\t\t<image class=\"arrow-icon\" src=\"/static/images/tabbar/arrow-up.png\"></image>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 合作共赢区域 -->\n\t\t<view class=\"section-card\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">合作共赢</text>\n\t\t\t</view>\n\t\t\t<view class=\"service-grid\">\n\t\t\t\t<view class=\"service-row\">\n\t\t\t\t\t<!-- 分销中心 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/distribution/pages/index')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap distribution-icon-wrap\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/distribution/icon-distribution.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">我要赚钱</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 合伙人中心 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/partner/pages/partner')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap partner-icon-wrap\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/合伙人.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">合伙人</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 区域加盟 -->\n\t\t\t\t\t<view class=\"service-item\" @click=\"navigateTo('/subPackages/franchise/pages/index')\">\n\t\t\t\t\t\t<view class=\"service-icon-wrap franchise-icon-wrap\">\n\t\t\t\t\t\t\t<image class=\"service-icon\" src=\"/static/images/tabbar/加盟.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"service-name\">区域加盟</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\n\t</view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\n\timport UserAuth from '@/components/UserAuth.vue';\n\n// --- 响应式状态 ---\nconst userInfo = ref({\n\t\t\t\t\tnickname: '',\n\t\t\t\t\tavatar: ''\n});\nconst showAuthPopup = ref(false);\n\n// --- 方法 ---\n\n// 处理用户卡片点击事件\nconst handleUserCardClick = () => {\n  if (!userInfo.value.nickname) {\n    showAuthPopup.value = true;\n\t\t\t\t} else {\n    // 已登录，可以跳转到用户信息编辑页或个人主页\n    navigateTo('/pages/user-center/profile');\n\t\t\t\t}\n};\n\t\t\t\n\t\t\t// 关闭授权弹窗\nconst closeAuthPopup = () => {\n  showAuthPopup.value = false;\n};\n\t\t\t\n\t\t\t// 用户信息更新回调\nconst onUserInfoUpdated = (newUserInfo) => {\n  userInfo.value = newUserInfo;\n  showAuthPopup.value = false; // 授权成功后关闭弹窗\n};\n\n// 页面跳转\nconst navigateTo = (url) => {\n  uni.navigateTo({ url });\n};\n\n// 跳转到刷新套餐页面\nconst goToRefreshPackage = () => {\n  console.log('跳转到刷新套餐页面');\n  \n  // 直接打开页面\n  uni.navigateTo({\n    url: '/pages/my/refresh-package',\n    fail: (err) => {\n      console.error('跳转失败:', err);\n      // 尝试使用相对路径\n      uni.navigateTo({\n        url: './refresh-package',\n        fail: (err2) => {\n          console.error('相对路径跳转失败:', err2);\n          uni.showToast({\n            title: '页面加载中，请稍后再试',\n            icon: 'none',\n            duration: 2000\n          });\n        }\n      });\n    }\n  });\n};\n\n// 跳转到返利商城\nconst navigateToCashback = () => {\n  console.log('跳转到返利商城');\n  \n  // 使用redirectTo而不是navigateTo\n  uni.redirectTo({\n    url: '/subPackages/cashback/pages/index/index',\n    fail: (err) => {\n      console.error('返利商城跳转失败:', err);\n      // 尝试不同的路径格式\n      uni.redirectTo({\n        url: 'subPackages/cashback/pages/index/index',\n        fail: (err2) => {\n          console.error('第二次尝试跳转失败:', err2);\n          // 最后尝试使用reLaunch\n          uni.reLaunch({\n            url: '/subPackages/cashback/pages/index/index',\n            fail: (err3) => {\n              console.error('reLaunch跳转失败:', err3);\n              uni.showToast({\n                title: '返利商城正在升级中，请稍后再试',\n                icon: 'none',\n                duration: 2000\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n};\n\n// 加载本地存储的用户信息\nconst loadUserInfo = () => {\n  const storedUserInfo = uni.getStorageSync('userInfo');\n  if (storedUserInfo) {\n    userInfo.value = storedUserInfo;\n  }\n};\n\n// --- 生命周期 ---\nonMounted(() => {\n  loadUserInfo();\n  // 可以在这里获取真实的活动和拼团数量\n});\n</script>\n\n<style lang=\"scss\">\n\t.my-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #F5F5F7; /* 更浅的苹果风格背景色 */\n\t\tpadding-bottom: 30rpx;\n\t\tpadding-top: 44px; /* 为状态栏预留空间 */\n\t}\n\t\n\t/* 自定义导航栏 */\n\t.custom-navbar {\n\t\tbackground: linear-gradient(135deg, #0A84FF, #0066FF); /* 更鲜明的苹果蓝渐变 */\n\t\theight: 88rpx;\n\t\tpadding-top: 44px; /* 状态栏高度 */\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: fixed; /* 固定定位 */\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbox-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.2); /* 更明显的阴影 */\n\t\tz-index: 100;\n\t\tbackdrop-filter: blur(10px); /* 苹果风格模糊效果 */\n\t\t-webkit-backdrop-filter: blur(10px);\n\t}\n\t\n\t.navbar-title {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 600;\n\t\tletter-spacing: 1px; /* 增加字间距 */\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tmargin: 0 auto;\n\t\ttext-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* 文字阴影 */\n\t}\n\t\n\t/* 用户信息卡片 */\n\t.user-card {\n\t\tbackground: linear-gradient(135deg, #FFFFFF, #F8F8FA); /* 微妙的渐变背景 */\n\t\tmargin: 150rpx 24rpx 30rpx; /* 增加顶部边距，为固定导航栏留出空间 */\n\t\tpadding: 40rpx 36rpx; /* 增加内边距 */\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder-radius: 32rpx; /* 更大圆角 */\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), \n\t\t            0 2rpx 4rpx rgba(0, 0, 0, 0.03); /* 双层阴影增强立体感 */\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tborder: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */\n\t}\n\t\n\t/* 用户卡片装饰元素 */\n\t.user-card::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: -80rpx;\n\t\tright: -80rpx;\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tbackground: linear-gradient(135deg, rgba(10, 132, 255, 0.05), rgba(10, 132, 255, 0.1));\n\t\tborder-radius: 100rpx;\n\t\tz-index: 0;\n\t}\n\t\n\t.avatar {\n\t\twidth: 128rpx;\n\t\theight: 128rpx;\n\t\tborder-radius: 64rpx; /* 完美圆形 */\n\t\tbackground-color: #f0f0f0;\n\t\tborder: 3rpx solid #FFFFFF;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12); /* 增强阴影 */\n\t\tposition: relative;\n\t\tz-index: 1;\n\t}\n\t\n\t.user-info {\n\t\tmargin-left: 28rpx; /* 增大间距 */\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tposition: relative;\n\t\tz-index: 1;\n\t}\n\t\n\t.user-name {\n\t\tfont-size: 36rpx; /* 增大字体 */\n\t\tfont-weight: 600; /* 增强字重 */\n\t\tcolor: #1A1A1A; /* 苹果深灰色 */\n\t\tmargin-bottom: 10rpx;\n\t\tletter-spacing: 0.5px;\n\t}\n\t\n\t.user-desc {\n\t\tfont-size: 26rpx;\n\t\tcolor: #8E8E93; /* 苹果次要文本颜色 */\n\t\tletter-spacing: 0.3px;\n\t}\n\t\n\t/* 区域卡片样式 */\n\t.section-card {\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 32rpx; /* 更大圆角 */\n\t\tmargin: 24rpx;\n\t\tpadding: 28rpx 24rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06), \n\t\t            0 1rpx 4rpx rgba(0, 0, 0, 0.03); /* 双层阴影增强立体感 */\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tborder: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */\n\t\ttransition: transform 0.2s, box-shadow 0.2s;\n\t}\n\t\n\t.section-card:active {\n\t\ttransform: scale(0.99);\n\t\tbox-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.04);\n\t}\n\t\n\t.section-header {\n\t\tpadding: 10rpx 16rpx 20rpx;\n\t\tborder-bottom: 0.5px solid rgba(60, 60, 67, 0.08); /* 更薄的分隔线 */\n\t\tmargin-bottom: 16rpx;\n\t\tposition: relative;\n\t}\n\t\n\t.section-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600; /* 苹果风格中等字重 */\n\t\tcolor: #1A1A1A; /* 苹果深灰色 */\n\t\tposition: relative;\n\t\tpadding-left: 20rpx;\n\t\tletter-spacing: 0.5px;\n\t}\n\t\n\t.section-title::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 4rpx;\n\t\theight: 24rpx;\n\t\tbackground: linear-gradient(to bottom, #0A84FF, #0066FF); /* 渐变色装饰线 */\n\t\tborder-radius: 2rpx;\n\t}\n\t\n\t/* 服务网格样式 */\n\t.service-grid {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 24rpx 10rpx 10rpx;\n\t}\n\t\n\t.service-row {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\twidth: 100%;\n\t\tmargin-bottom: 36rpx; /* 增大行间距 */\n\t}\n\t\n\t.service-item {\n\t\twidth: 25%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmargin-bottom: 15rpx;\n\t}\n\t\n\t.service-icon-wrap {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tposition: relative;\n\t\tmargin-bottom: 12rpx;\n\t\t\n\t\t&.has-badge {\n\t\t\t.badge {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: -10rpx;\n\t\t\t\tright: -10rpx;\n\t\t\t\tbackground-color: #FF3B30;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tpadding: 2rpx 8rpx;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\ttransform: scale(0.8);\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.activity-icon-wrap {\n\t\t\t.activity-icon-bg {\n\t\t\t\twidth: 80rpx;\n\t\t\t\theight: 80rpx;\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t\tbackground: linear-gradient(135deg, #FF5A5F 0%, #FF3B30 100%);\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t\tbox-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.3);\n\t}\n\t\t}\n\t\t\n\t\t&.benefits-icon-wrap {\n\t\t\tbackground: linear-gradient(135deg, #FF9500 0%, #FF5E3A 100%);\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.3);\n\t}\n\t\n\t\t&.points-icon-wrap {\n\t\t\tbackground: linear-gradient(135deg, #4CD964 0%, #34C759 100%);\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(76, 217, 100, 0.3);\n\t}\n\t\n\t\t&.cashback-icon-wrap {\n\t\t\tbackground: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 122, 255, 0.3);\n\t}\n\t\n\t\t&.distribution-icon-wrap {\n\t\t\tbackground: linear-gradient(135deg, #FF2D55 0%, #FF3B30 100%);\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(255, 45, 85, 0.3);\n\t}\n\t\n\t\t&.partner-icon-wrap {\n\t\t\tbackground: linear-gradient(135deg, #5856D6 0%, #AF52DE 100%);\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(88, 86, 214, 0.3);\n\t}\n\t\n\t\t&.franchise-icon-wrap {\n\t\t\tbackground: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(255, 204, 0, 0.3);\n\t\t}\n\t}\n\t\n\t.has-badge .badge {\n\t\tposition: absolute;\n\t\ttop: -6rpx;\n\t\tright: -6rpx;\n\t\tmin-width: 36rpx;\n\t\theight: 36rpx;\n\t\tpadding: 0 8rpx;\n\t\tbackground-color: #FF3B30; /* 苹果红色 */\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 20rpx;\n\t\tfont-weight: 600;\n\t\tborder-radius: 18rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 2rpx 6rpx rgba(255, 59, 48, 0.3); /* 徽章阴影 */\n\t\tborder: 1px solid #FFFFFF; /* 白色边框 */\n\t}\n\t\n\t.service-icon {\n\t\twidth: 52rpx;\n\t\theight: 52rpx;\n\t\tfilter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1)); /* SVG图标阴影 */\n\t}\n\t\n\t.service-name {\n\t\tfont-size: 26rpx;\n\t\tcolor: #4D4D4D;\n\t\tmargin-top: 4rpx;\n\t\tfont-weight: 500; /* 稍微加粗 */\n\t}\n\t\n\t/* 工具项样式 */\n\t.tool-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 32rpx 28rpx;\n\t\tmargin: 8rpx 16rpx;\n\t\tborder-radius: 24rpx; /* 增大圆角 */\n\t\ttransition: all 0.2s ease;\n\t\tposition: relative;\n\t}\n\t\n\t.tool-item:active {\n\t\tbackground-color: rgba(0, 0, 0, 0.04); /* 点击时的背景变化 */\n\t\ttransform: scale(0.99);\n\t}\n\t\n\t.tool-item:not(:last-child)::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 80rpx;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\theight: 0.5px;\n\t\tbackground-color: rgba(60, 60, 67, 0.08); /* 苹果风格分隔线 */\n\t}\n\t\n\t.tool-left {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.tool-icon {\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t\tmargin-right: 24rpx;\n\t\tbackground: linear-gradient(135deg, #F0F6FF, #E6F0FF); /* 渐变背景 */\n\t\tborder-radius: 16rpx; /* 增大圆角 */\n\t\tpadding: 12rpx;\n\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05); /* 微妙的阴影 */\n\t}\n\t\n\t.tool-name {\n\t\tfont-size: 30rpx;\n\t\tcolor: #1A1A1A; /* 苹果深灰色 */\n\t\tfont-weight: 500; /* 稍微加粗 */\n\t}\n\t\n\t.tool-badge {\n\t\tpadding: 6rpx 14rpx;\n\t\tbackground-color: #FF3B30;\n\t\tcolor: #fff;\n\t\tfont-size: 20rpx;\n\t\tfont-weight: 600;\n\t\tborder-radius: 20rpx;\n\t\tmargin-right: 16rpx;\n\t\tbox-shadow: 0 2rpx 6rpx rgba(255, 59, 48, 0.2); /* 徽章阴影 */\n\t}\n\t\n\t.arrow-icon {\n\t\twidth: 28rpx;\n\t\theight: 28rpx;\n\t\ttransform: rotate(90deg);\n\t\topacity: 0.3;\n\t}\n\t\n\t/* 授权弹窗样式 */\n\t.auth-popup {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.6); /* 更深的半透明背景 */\n\t\tz-index: 999;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackdrop-filter: blur(10px); /* 苹果风格模糊背景 */\n\t\t-webkit-backdrop-filter: blur(10px);\n\t}\n\t\n\t.auth-container {\n\t\twidth: 600rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.95); /* 半透明背景 */\n\t\tborder-radius: 32rpx; /* 更大圆角 */\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.15); /* 明显的卡片阴影 */\n\t\tborder: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */\n\t\tanimation: authPopIn 0.3s ease-out; /* 弹出动画 */\n\t}\n\t\n\t@keyframes authPopIn {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: scale(0.9);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: scale(1);\n\t\t}\n\t}\n\t\n\t.auth-header {\n\t\tposition: relative;\n\t\tpadding: 32rpx;\n\t\tborder-bottom: 0.5px solid rgba(60, 60, 67, 0.1); /* 苹果风格分隔线 */\n\t\ttext-align: center;\n\t}\n\t\n\t.auth-title {\n\t\tfont-size: 34rpx;\n\t\tfont-weight: 600; /* 苹果字重 */\n\t\tcolor: #1A1A1A; /* 苹果深灰色 */\n\t\tletter-spacing: 0.5px;\n\t}\n\t\n\t.close-btn {\n\t\tposition: absolute;\n\t\tright: 20rpx;\n\t\ttop: 20rpx;\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 40rpx;\n\t\tcolor: #8E8E93; /* 苹果次要文本颜色 */\n\t\tborder-radius: 22rpx;\n\t\tbackground-color: rgba(60, 60, 67, 0.05);\n\t\ttransition: all 0.2s ease;\n\t}\n\t\n\t.close-btn:active {\n\t\tbackground-color: rgba(60, 60, 67, 0.1); /* 点击效果 */\n\t\ttransform: scale(0.95);\n\t}\n\t\n\t.auth-content {\n\t\tpadding: 48rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\t\n\t.auth-logo {\n\t\twidth: 128rpx;\n\t\theight: 128rpx;\n\t\tmargin-bottom: 36rpx;\n\t\tborder-radius: 64rpx;\n\t\tbackground-color: #f0f0f0;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12); /* 增强阴影 */\n\t\tborder: 3rpx solid #FFFFFF;\n\t}\n\t\n\t.auth-desc {\n\t\tfont-size: 30rpx;\n\t\tcolor: #666666;\n\t\tmargin-bottom: 48rpx;\n\t\ttext-align: center;\n\t\tline-height: 1.5;\n\t}\n\t\n\t.auth-success {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmargin-top: 24rpx;\n\t}\n\t\n\t.success-icon {\n\t\tfont-size: 64rpx;\n\t\tcolor: #34C759; /* 苹果绿色 */\n\t\tmargin-bottom: 20rpx;\n\t\ttext-shadow: 0 2rpx 6rpx rgba(52, 199, 89, 0.2); /* 文字阴影 */\n\t}\n\t\n\t.success-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #34C759; /* 苹果绿色 */\n\t\tfont-weight: 600; /* 苹果字重 */\n\t\tletter-spacing: 0.5px;\n\t}\n\t\n\t/* 功能入口区样式 */\n\t.function-section {\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 32rpx; /* 更大圆角 */\n\t\tmargin: 24rpx;\n\t\tpadding: 28rpx 24rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06),\n\t\t            0 1rpx 4rpx rgba(0, 0, 0, 0.03); /* 双层阴影增强立体感 */\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t\tborder: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */\n\t}\n\t\n\t.function-row {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\twidth: 100%;\n\t\tmargin-bottom: 24rpx;\n\t}\n\t\n\t.function-item {\n\t\twidth: 25%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.function-icon-wrap {\n\t\tposition: relative;\n\t\twidth: 96rpx;\n\t\theight: 96rpx;\n\t\tmargin-bottom: 16rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackground: linear-gradient(135deg, #F0F6FF, #E6F0FF); /* 渐变背景 */\n\t\tborder-radius: 48rpx; /* 完全圆形 */\n\t\ttransition: all 0.25s ease;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05),\n\t\t            0 1rpx 3rpx rgba(0, 0, 0, 0.03); /* 双层阴影 */\n\t\tborder: 0.5px solid rgba(255, 255, 255, 0.8); /* 微妙的边框 */\n\t}\n\t\n\t.function-icon-wrap:active {\n\t\ttransform: scale(0.92); /* 点击时的缩放效果 */\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);\n\t}\n\t\n\t.function-icon {\n\t\twidth: 52rpx;\n\t\theight: 52rpx;\n\t\tfilter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1)); /* SVG图标阴影 */\n\t}\n\t\n\t.function-name {\n\t\tfont-size: 26rpx;\n\t\tcolor: #4D4D4D;\n\t\tfont-weight: 500; /* 稍微加粗 */\n\t}\n\t\n\t/* 新商家后台入口样式 */\n\t.menu-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 32rpx 28rpx;\n\t\tmargin: 8rpx 16rpx;\n\t\tborder-radius: 24rpx; /* 增大圆角 */\n\t\ttransition: all 0.2s ease;\n\t\tposition: relative;\n\t}\n\t\n\t.menu-item:active {\n\t\tbackground-color: rgba(0, 0, 0, 0.04); /* 点击时的背景变化 */\n\t\ttransform: scale(0.99);\n\t}\n\t\n\t.menu-item:not(:last-child)::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 80rpx;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\theight: 0.5px;\n\t\tbackground-color: rgba(60, 60, 67, 0.08); /* 苹果风格分隔线 */\n\t}\n\t\n\t.menu-icon {\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t\tmargin-right: 24rpx;\n\t\tbackground: linear-gradient(135deg, #F0F6FF, #E6F0FF); /* 渐变背景 */\n\t\tborder-radius: 16rpx; /* 增大圆角 */\n\t\tpadding: 12rpx;\n\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05); /* 微妙的阴影 */\n\t}\n\t\n\t.menu-text {\n\t\tfont-size: 30rpx;\n\t\tcolor: #1A1A1A; /* 苹果深灰色 */\n\t\tfont-weight: 500; /* 稍微加粗 */\n\t}\n\t\n\t.menu-badge {\n\t\tfont-size: 20rpx;\n\t\tcolor: #FF3B30; /* 苹果红色 */\n\t\tfont-weight: 600;\n\t\tmargin-left: 8rpx;\n\t\tpadding: 4rpx 12rpx;\n\t\tbackground-color: rgba(255, 59, 48, 0.1);\n\t\tborder-radius: 10rpx;\n\t}\n\t\n\t.menu-arrow {\n\t\twidth: 28rpx;\n\t\theight: 28rpx;\n\t\ttransform: rotate(90deg);\n\t\topacity: 0.3;\n\t}\n</style> \n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/my/my.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;;;;;AA2PC,MAAM,WAAW,MAAW;;;;AAG7B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MAChB,UAAU;AAAA,MACV,QAAQ;AAAA,IACb,CAAC;AACD,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAK/B,UAAM,sBAAsB,MAAM;AAChC,UAAI,CAAC,SAAS,MAAM,UAAU;AAC5B,sBAAc,QAAQ;AAAA,MAC1B,OAAW;AAEP,mBAAW,4BAA4B;AAAA,MACtC;AAAA,IACL;AAGA,UAAM,iBAAiB,MAAM;AAC3B,oBAAc,QAAQ;AAAA,IACxB;AAGA,UAAM,oBAAoB,CAAC,gBAAgB;AACzC,eAAS,QAAQ;AACjB,oBAAc,QAAQ;AAAA,IACxB;AAGA,UAAM,aAAa,CAAC,QAAQ;AAC1BC,oBAAAA,MAAI,WAAW,EAAE,IAAG,CAAE;AAAA,IACxB;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAA,MAAA,OAAA,0BAAY,WAAW;AAGvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,0BAAc,SAAS,GAAG;AAE1BA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,SAAS;AACdA,4BAAA,MAAA,MAAA,SAAA,0BAAc,aAAa,IAAI;AAC/BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cACtB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/BA,oBAAAA,MAAA,MAAA,OAAA,0BAAY,SAAS;AAGrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,0BAAA,aAAa,GAAG;AAE9BA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,SAAS;AACdA,4BAAA,MAAA,MAAA,SAAA,0BAAc,cAAc,IAAI;AAEhCA,4BAAAA,MAAI,SAAS;AAAA,gBACX,KAAK;AAAA,gBACL,MAAM,CAAC,SAAS;AACdA,gCAAc,MAAA,MAAA,SAAA,0BAAA,iBAAiB,IAAI;AACnCA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,oBACN,UAAU;AAAA,kBAC1B,CAAe;AAAA,gBACF;AAAA,cACb,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,UAAU;AACpD,UAAI,gBAAgB;AAClB,iBAAS,QAAQ;AAAA,MAClB;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AACd;IAEF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpWD,GAAG,WAAWC,SAAe;"}