{"version": 3, "file": "management.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/flash/management.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xmbGFzaFxtYW5hZ2VtZW50LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"flash-management-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-left\">\n        <view class=\"back-button\" @tap=\"goBack\">\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M15 18L9 12L15 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </view>\n      </view>\n      <view class=\"navbar-title\">\n        <text class=\"title-text\">限时秒杀管理</text>\n      </view>\n      <view class=\"navbar-right\">\n        <!-- 与营销中心保持一致 -->\n      </view>\n    </view>\n    \n    <!-- 顶部操作区 -->\n    <view class=\"top-actions\">\n      <CreateButton text=\"创建秒杀活动\" theme=\"flash\" @click=\"createFlashSale\" />\n    </view>\n    \n    <!-- 页面内容区域 -->\n    <scroll-view scroll-y class=\"content-area\">\n      <!-- 数据概览区域 -->\n      <view class=\"overview-section\">\n        <view class=\"overview-header\">\n          <text class=\"section-title\">数据概览</text>\n          <view class=\"date-picker\">\n            <text class=\"date-text\">近30天</text>\n            <view class=\"arrow-icon\"></view>\n          </view>\n        </view>\n        \n        <view class=\"overview-cards\">\n          <view class=\"overview-card\">\n            <view class=\"card-value\">{{flashData.totalSales}}</view>\n            <view class=\"card-label\">总销售额 (元)</view>\n          </view>\n          <view class=\"overview-card\">\n            <view class=\"card-value\">{{flashData.totalOrders}}</view>\n            <view class=\"card-label\">累计订单数</view>\n          </view>\n          <view class=\"overview-card\">\n            <view class=\"card-value\">{{flashData.conversionRate}}%</view>\n            <view class=\"card-label\">平均转化率</view>\n          </view>\n          <view class=\"overview-card\">\n            <view class=\"card-value\">{{flashData.avgOrderValue}}</view>\n            <view class=\"card-label\">客单价 (元)</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 秒杀活动管理 -->\n      <view class=\"flash-sales-section\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">秒杀活动</text>\n          <view class=\"filter-buttons\">\n            <view class=\"filter-button\" :class=\"{ active: activeFilter === 'all' }\" @tap=\"setFilter('all')\">全部</view>\n            <view class=\"filter-button\" :class=\"{ active: activeFilter === 'active' }\" @tap=\"setFilter('active')\">进行中</view>\n            <view class=\"filter-button\" :class=\"{ active: activeFilter === 'upcoming' }\" @tap=\"setFilter('upcoming')\">未开始</view>\n            <view class=\"filter-button\" :class=\"{ active: activeFilter === 'ended' }\" @tap=\"setFilter('ended')\">已结束</view>\n          </view>\n        </view>\n        \n        <!-- 秒杀活动列表 -->\n        <view class=\"flash-sales-list\" v-if=\"filteredActivities.length > 0\">\n          <view class=\"flash-sale-item\" v-for=\"(item, index) in filteredActivities\" :key=\"index\" @tap=\"viewFlashDetail(item)\">\n            <view class=\"item-main\">\n              <view class=\"item-image-container\">\n                <image class=\"item-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\n                <view class=\"item-status\" :class=\"item.statusClass\">{{item.statusText}}</view>\n              </view>\n              <view class=\"item-content\">\n                <view class=\"item-name\">{{item.name}}</view>\n                <view class=\"item-price\">\n                  <text class=\"price-now\">¥{{item.flashPrice}}</text>\n                  <text class=\"price-original\">¥{{item.originalPrice}}</text>\n                </view>\n                <view class=\"item-stats\">\n                  <view class=\"stat-item\">\n                    <text class=\"stat-value\">{{item.stockSold}}/{{item.stockTotal}}</text>\n                    <text class=\"stat-label\">已售/总库存</text>\n                  </view>\n                  <view class=\"stat-item\">\n                    <text class=\"stat-value\">{{item.viewCount}}</text>\n                    <text class=\"stat-label\">浏览量</text>\n                  </view>\n                </view>\n                <view class=\"item-time\">{{item.timeRange}}</view>\n              </view>\n            </view>\n            <view class=\"item-actions\">\n              <view class=\"action-button edit\" @tap.stop=\"editFlashSale(item)\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13\" stroke=\"#5E5CE6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                  <path d=\"M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z\" stroke=\"#5E5CE6\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                </svg>\n              </view>\n              <view class=\"action-button share\" @tap.stop=\"shareFlashSale(item)\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                  <path d=\"M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                  <path d=\"M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                  <path d=\"M8.59 13.51L15.42 17.49\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                  <path d=\"M15.41 6.51L8.59 10.49\" stroke=\"#34C759\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                </svg>\n              </view>\n              <view class=\"action-button delete\" @tap.stop=\"deleteFlashSale(item)\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M3 6H5H21\" stroke=\"#FF3B30\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                  <path d=\"M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z\" stroke=\"#FF3B30\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                </svg>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 空状态 -->\n        <view class=\"empty-state\" v-if=\"filteredActivities.length === 0\">\n          <image class=\"empty-image\" src=\"/static/images/empty-flash.png\" mode=\"aspectFit\"></image>\n          <text class=\"empty-text\">暂无{{filterLabels[activeFilter]}}秒杀活动</text>\n          <view class=\"empty-desc\">秒杀活动是限时限量特价促销的有效方式</view>\n        </view>\n      </view>\n      \n      <!-- 秒杀数据分析 -->\n      <view class=\"analytics-section\" v-if=\"filteredActivities.length > 0\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">销售趋势</text>\n          <view class=\"time-selector\">\n            <text class=\"time-option\" :class=\"{ active: activeTimeRange === '7d' }\" @tap=\"setTimeRange('7d')\">7天</text>\n            <text class=\"time-option\" :class=\"{ active: activeTimeRange === '30d' }\" @tap=\"setTimeRange('30d')\">30天</text>\n            <text class=\"time-option\" :class=\"{ active: activeTimeRange === '90d' }\" @tap=\"setTimeRange('90d')\">90天</text>\n          </view>\n        </view>\n        \n        <view class=\"analytics-chart\">\n          <view class=\"chart-legend\">\n            <view class=\"legend-item\">\n              <view class=\"legend-color sales\"></view>\n              <text class=\"legend-text\">销售额</text>\n            </view>\n            <view class=\"legend-item\">\n              <view class=\"legend-color orders\"></view>\n              <text class=\"legend-text\">订单量</text>\n            </view>\n          </view>\n          \n          <view class=\"chart-container\">\n            <!-- 简化的图表，实际项目中可以使用echarts等图表库 -->\n            <view class=\"chart-bars\">\n              <view class=\"chart-bar\" v-for=\"(item, index) in chartData\" :key=\"index\">\n                <view class=\"bar-group\">\n                  <view class=\"bar sales\" :style=\"{ height: item.salesHeight + '%' }\"></view>\n                  <view class=\"bar orders\" :style=\"{ height: item.ordersHeight + '%' }\"></view>\n                </view>\n                <text class=\"bar-label\">{{item.date}}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 秒杀活动技巧 -->\n      <view class=\"tips-section\" v-if=\"filteredActivities.length === 0\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">秒杀活动技巧</text>\n        </view>\n        \n        <view class=\"tips-list\">\n          <view class=\"tip-item\">\n            <view class=\"tip-icon\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                <path d=\"M12 16V12\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                <path d=\"M12 8H12.01\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n            <view class=\"tip-content\">\n              <view class=\"tip-title\">选择热门时段</view>\n              <view class=\"tip-desc\">选择用户活跃度高的时段发布秒杀活动，如12:00-13:00或18:00-20:00</view>\n            </view>\n          </view>\n          <view class=\"tip-item\">\n            <view class=\"tip-icon\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n            <view class=\"tip-content\">\n              <view class=\"tip-title\">明确价格优势</view>\n              <view class=\"tip-desc\">秒杀商品价格应明显低于原价，建议达到5-7折的优惠幅度</view>\n            </view>\n          </view>\n          <view class=\"tip-item\">\n            <view class=\"tip-icon\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z\" stroke=\"#FF7600\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              </svg>\n            </view>\n            <view class=\"tip-content\">\n              <view class=\"tip-title\">提前预热宣传</view>\n              <view class=\"tip-desc\">秒杀活动发布前1-3天进行预热宣传，增加用户关注度</view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 底部空间 -->\n      <view class=\"bottom-space\"></view>\n    </scroll-view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport CreateButton from '/subPackages/merchant-admin-marketing/components/CreateButton.vue';\n\n// 数据概览\nconst flashData = reactive({\n  totalSales: '15,632.80',\n  totalOrders: '342',\n  conversionRate: '28.6',\n  avgOrderValue: '45.8'\n});\n\n// 筛选相关\nconst activeFilter = ref('all');\nconst filterLabels = {\n  all: '全部',\n  active: '进行中',\n  upcoming: '未开始',\n  ended: '已结束'\n};\n\n// 秒杀活动数据\nconst flashActivities = ref([\n  {\n    id: 1,\n    name: '夏季清凉风扇特惠',\n    flashPrice: 59.9,\n    originalPrice: 129.9,\n    stockTotal: 100,\n    stockSold: 86,\n    viewCount: 1564,\n    image: '/static/images/flash-item1.jpg',\n    timeRange: '2023-07-01 12:00 ~ 14:00',\n    status: 'active',\n    statusText: '进行中',\n    statusClass: 'status-active'\n  },\n  {\n    id: 2,\n    name: '夏季新品T恤限时秒杀',\n    flashPrice: 39.9,\n    originalPrice: 99.9,\n    stockTotal: 200,\n    stockSold: 143,\n    viewCount: 2238,\n    image: '/static/images/flash-item2.jpg',\n    timeRange: '2023-07-02 18:00 ~ 20:00',\n    status: 'active',\n    statusText: '进行中',\n    statusClass: 'status-active'\n  },\n  {\n    id: 3,\n    name: '智能手环超值特惠',\n    flashPrice: 89.9,\n    originalPrice: 199.9,\n    stockTotal: 50,\n    stockSold: 0,\n    viewCount: 562,\n    image: '/static/images/flash-item3.jpg',\n    timeRange: '2023-07-05 12:00 ~ 14:00',\n    status: 'upcoming',\n    statusText: '未开始',\n    statusClass: 'status-upcoming'\n  },\n  {\n    id: 4,\n    name: '厨房小家电秒杀',\n    flashPrice: 129.9,\n    originalPrice: 299.9,\n    stockTotal: 80,\n    stockSold: 80,\n    viewCount: 1876,\n    image: '/static/images/flash-item4.jpg',\n    timeRange: '2023-06-25 18:00 ~ 20:00',\n    status: 'ended',\n    statusText: '已结束',\n    statusClass: 'status-ended'\n  },\n  {\n    id: 5,\n    name: '儿童玩具特惠',\n    flashPrice: 49.9,\n    originalPrice: 99.9,\n    stockTotal: 150,\n    stockSold: 98,\n    viewCount: 1245,\n    image: '/static/images/flash-item5.jpg',\n    timeRange: '2023-06-20 12:00 ~ 14:00',\n    status: 'ended',\n    statusText: '已结束',\n    statusClass: 'status-ended'\n  }\n]);\n\n// 图表数据\nconst activeTimeRange = ref('30d');\nconst chartData = ref([\n  { date: '06-01', sales: 1200, orders: 28, salesHeight: 40, ordersHeight: 35 },\n  { date: '06-05', sales: 1850, orders: 42, salesHeight: 61.7, ordersHeight: 52.5 },\n  { date: '06-10', sales: 1500, orders: 35, salesHeight: 50, ordersHeight: 43.75 },\n  { date: '06-15', sales: 2400, orders: 56, salesHeight: 80, ordersHeight: 70 },\n  { date: '06-20', sales: 3000, orders: 80, salesHeight: 100, ordersHeight: 100 },\n  { date: '06-25', sales: 2200, orders: 52, salesHeight: 73.3, ordersHeight: 65 },\n  { date: '06-30', sales: 1800, orders: 40, salesHeight: 60, ordersHeight: 50 }\n]);\n\n// 根据筛选条件过滤活动\nconst filteredActivities = computed(() => {\n  if (activeFilter.value === 'all') {\n    return flashActivities.value;\n  }\n  return flashActivities.value.filter(item => item.status === activeFilter.value);\n});\n\n// 设置筛选条件\nconst setFilter = (filter) => {\n  activeFilter.value = filter;\n};\n\n// 设置时间范围\nconst setTimeRange = (range) => {\n  activeTimeRange.value = range;\n  // 在实际应用中，这里应该根据选择的时间范围重新加载图表数据\n  loadChartData(range);\n};\n\n// 加载图表数据\nconst loadChartData = (range) => {\n  // 模拟不同时间范围的数据\n  if (range === '7d') {\n    chartData.value = [\n      { date: '06-24', sales: 1200, orders: 28, salesHeight: 40, ordersHeight: 35 },\n      { date: '06-25', sales: 1850, orders: 42, salesHeight: 61.7, ordersHeight: 52.5 },\n      { date: '06-26', sales: 1500, orders: 35, salesHeight: 50, ordersHeight: 43.75 },\n      { date: '06-27', sales: 2400, orders: 56, salesHeight: 80, ordersHeight: 70 },\n      { date: '06-28', sales: 3000, orders: 80, salesHeight: 100, ordersHeight: 100 },\n      { date: '06-29', sales: 2200, orders: 52, salesHeight: 73.3, ordersHeight: 65 },\n      { date: '06-30', sales: 1800, orders: 40, salesHeight: 60, ordersHeight: 50 }\n    ];\n  } else if (range === '30d') {\n    chartData.value = [\n      { date: '06-01', sales: 1200, orders: 28, salesHeight: 40, ordersHeight: 35 },\n      { date: '06-05', sales: 1850, orders: 42, salesHeight: 61.7, ordersHeight: 52.5 },\n      { date: '06-10', sales: 1500, orders: 35, salesHeight: 50, ordersHeight: 43.75 },\n      { date: '06-15', sales: 2400, orders: 56, salesHeight: 80, ordersHeight: 70 },\n      { date: '06-20', sales: 3000, orders: 80, salesHeight: 100, ordersHeight: 100 },\n      { date: '06-25', sales: 2200, orders: 52, salesHeight: 73.3, ordersHeight: 65 },\n      { date: '06-30', sales: 1800, orders: 40, salesHeight: 60, ordersHeight: 50 }\n    ];\n  } else if (range === '90d') {\n    chartData.value = [\n      { date: '04-01', sales: 1000, orders: 22, salesHeight: 33.3, ordersHeight: 27.5 },\n      { date: '04-15', sales: 1400, orders: 32, salesHeight: 46.7, ordersHeight: 40 },\n      { date: '05-01', sales: 1700, orders: 38, salesHeight: 56.7, ordersHeight: 47.5 },\n      { date: '05-15', sales: 2100, orders: 48, salesHeight: 70, ordersHeight: 60 },\n      { date: '06-01', sales: 2500, orders: 58, salesHeight: 83.3, ordersHeight: 72.5 },\n      { date: '06-15', sales: 2800, orders: 65, salesHeight: 93.3, ordersHeight: 81.25 },\n      { date: '06-30', sales: 3000, orders: 80, salesHeight: 100, ordersHeight: 100 }\n    ];\n  }\n};\n\n// 查看秒杀活动详情\nconst viewFlashDetail = (item) => {\n  uni.navigateTo({\n    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/detail?id=${item.id}`\n  });\n};\n\n// 编辑秒杀活动\nconst editFlashSale = (item) => {\n      uni.navigateTo({\n    url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/edit?id=${item.id}`\n  });\n};\n\n// 分享秒杀活动\nconst shareFlashSale = (item) => {\n  uni.showToast({\n    title: '生成分享链接中...',\n    icon: 'loading',\n    duration: 1500\n  });\n  \n  setTimeout(() => {\n    uni.showModal({\n      title: '分享秒杀活动',\n      content: `活动\"${item.name}\"的分享链接已创建，可发送给客户或分享到社交媒体`,\n      confirmText: '复制链接',\n      cancelText: '取消',\n      success: (res) => {\n        if (res.confirm) {\n          // 实际应用中这里应该有真实的分享链接\n          uni.setClipboardData({\n            data: `https://example.com/flash-sale/${item.id}`,\n            success: () => {\n              uni.showToast({\n                title: '链接已复制',\n                icon: 'success'\n              });\n            }\n          });\n        }\n      }\n    });\n  }, 1500);\n};\n\n// 删除秒杀活动\nconst deleteFlashSale = (item) => {\n  uni.showModal({\n    title: '确认删除',\n    content: `确定要删除\"${item.name}\"秒杀活动吗？一旦删除将无法恢复。`,\n    confirmText: '删除',\n    confirmColor: '#FF3B30',\n    cancelText: '取消',\n    success: (res) => {\n      if (res.confirm) {\n        // 在实际应用中这里应该调用API删除活动\n        const index = flashActivities.value.findIndex(i => i.id === item.id);\n        if (index !== -1) {\n          flashActivities.value.splice(index, 1);\n          uni.showToast({\n            title: '删除成功',\n            icon: 'success'\n          });\n        }\n      }\n    }\n  });\n};\n\n// 创建秒杀活动\nconst createFlashSale = () => {\n  uni.navigateTo({\n    url: '/subPackages/merchant-admin-marketing/pages/marketing/flash/create'\n  });\n};\n\n// 返回上一页\nconst goBack = () => {\n  uni.navigateBack();\n};\n\n// 页面加载\nonMounted(() => {\n  console.log('限时秒杀管理页面已加载');\n  // 加载秒杀活动数据（实际应用中应该从API获取）\n});\n</script>\n\n<style lang=\"scss\">\n/* 页面容器 */\n.flash-management-container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  position: relative;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #FF7600, #FF3C00);\n  color: white;\n  padding: 48px 20px 16px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);\n}\n\n.navbar-left {\n  width: 40px;\n}\n\n.back-button {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n}\n\n.title-text {\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.navbar-right {\n  width: 40px;\n}\n\n/* 顶部操作区 */\n.top-actions {\n  padding: 16px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n/* 内容区域 */\n.content-area {\n  flex: 1;\n  padding: 16px;\n  box-sizing: border-box;\n  height: calc(100vh - 80px);\n}\n\n/* 底部空间 */\n.bottom-space {\n  height: 20px;\n}\n\n/* 概览区域 */\n.overview-section {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.overview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.section-title {\n  font-size: 17px;\n  font-weight: 600;\n  color: #333333;\n}\n\n.date-picker {\n  display: flex;\n  align-items: center;\n  background: rgba(255, 120, 0, 0.08);\n  padding: 4px 8px;\n  border-radius: 16px;\n}\n\n.date-text {\n  font-size: 14px;\n  color: #FF7600;\n  margin-right: 4px;\n}\n\n.arrow-icon {\n  width: 0;\n  height: 0;\n  border-left: 4px solid transparent;\n  border-right: 4px solid transparent;\n  border-top: 5px solid #FF7600;\n}\n\n.overview-cards {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n\n.overview-card {\n  width: 48%;\n  background: #F9F9F9;\n  border-radius: 8px;\n  padding: 12px;\n  margin-bottom: 12px;\n}\n\n.card-value {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 4px;\n}\n\n.card-label {\n  font-size: 12px;\n  color: #666666;\n}\n\n/* 秒杀活动管理 */\n.flash-sales-section {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n}\n\n.filter-buttons {\n  display: flex;\n  overflow-x: auto;\n  margin-top: 8px;\n  width: 100%;\n}\n\n.filter-button {\n  padding: 6px 12px;\n  margin-right: 8px;\n  border-radius: 16px;\n  font-size: 14px;\n  color: #666666;\n  background: #F5F5F5;\n  white-space: nowrap;\n  transition: all 0.3s ease;\n}\n\n.filter-button.active {\n  background: #FF7600;\n  color: white;\n}\n\n/* 秒杀活动列表 */\n.flash-sales-list {\n  margin-top: 8px;\n}\n\n.flash-sale-item {\n  background: #FFFFFF;\n  border-radius: 8px;\n  padding: 12px;\n  margin-bottom: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n  border: 1px solid #EEEEEE;\n}\n\n.item-main {\n  display: flex;\n}\n\n.item-image-container {\n  position: relative;\n  width: 80px;\n  height: 80px;\n  margin-right: 12px;\n  overflow: hidden;\n  border-radius: 4px;\n}\n\n.item-image {\n  width: 100%;\n  height: 100%;\n}\n\n.item-status {\n  position: absolute;\n  top: 0;\n  right: 0;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 0 0 0 8px;\n  color: white;\n  z-index: 2;\n}\n\n.status-active {\n  background: #34C759;\n}\n\n.status-upcoming {\n  background: #007AFF;\n}\n\n.status-ended {\n  background: #8E8E93;\n}\n\n.item-content {\n  flex: 1;\n}\n\n.item-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 6px;\n  line-height: 1.3;\n}\n\n.item-price {\n  margin-bottom: 6px;\n}\n\n.price-now {\n  font-size: 17px;\n  font-weight: 600;\n  color: #FF3B30;\n  margin-right: 8px;\n}\n\n.price-original {\n  font-size: 14px;\n  color: #999999;\n  text-decoration: line-through;\n}\n\n.item-stats {\n  display: flex;\n  margin-bottom: 6px;\n}\n\n.stat-item {\n  margin-right: 16px;\n}\n\n.stat-value {\n  font-size: 14px;\n  color: #333333;\n  margin-right: 4px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #999999;\n}\n\n.item-time {\n  font-size: 12px;\n  color: #666666;\n}\n\n.item-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 12px;\n  border-top: 1px solid #F5F5F5;\n  padding-top: 12px;\n}\n\n.action-button {\n  width: 32px;\n  height: 32px;\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 12px;\n  background: #F5F5F5;\n}\n\n.action-button.edit {\n  background: rgba(94, 92, 230, 0.1);\n}\n\n.action-button.share {\n  background: rgba(52, 199, 89, 0.1);\n}\n\n.action-button.delete {\n  background: rgba(255, 59, 48, 0.1);\n}\n\n/* 空状态 */\n.empty-state {\n  padding: 32px 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.empty-image {\n  width: 100px;\n  height: 100px;\n  margin-bottom: 16px;\n}\n\n.empty-text {\n  font-size: 17px;\n  font-weight: 600;\n  color: #333333;\n  margin-bottom: 8px;\n}\n\n.empty-desc {\n  font-size: 14px;\n  color: #666666;\n  text-align: center;\n  margin-bottom: 20px;\n}\n\n/* 数据分析 */\n.analytics-section {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.time-selector {\n  display: flex;\n  margin-top: 8px;\n  background: #F5F5F5;\n  border-radius: 16px;\n  overflow: hidden;\n  align-self: flex-end;\n}\n\n.time-option {\n  padding: 6px 12px;\n  font-size: 14px;\n  color: #666666;\n  text-align: center;\n}\n\n.time-option.active {\n  background: #FF7600;\n  color: white;\n}\n\n.analytics-chart {\n  padding: 12px 0;\n}\n\n.chart-legend {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 16px;\n}\n\n.legend-item {\n  display: flex;\n  align-items: center;\n  margin: 0 16px;\n}\n\n.legend-color {\n  width: 16px;\n  height: 8px;\n  border-radius: 4px;\n  margin-right: 8px;\n}\n\n.legend-color.sales {\n  background: #FF7600;\n}\n\n.legend-color.orders {\n  background: #007AFF;\n}\n\n.legend-text {\n  font-size: 12px;\n  color: #666666;\n}\n\n.chart-container {\n  height: 200px;\n  padding: 16px 0;\n}\n\n.chart-bars {\n  display: flex;\n  align-items: flex-end;\n  height: 150px;\n  justify-content: space-between;\n  padding: 0 8px;\n}\n\n.chart-bar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 14%;\n}\n\n.bar-group {\n  display: flex;\n  width: 100%;\n  height: 150px;\n  align-items: flex-end;\n  justify-content: center;\n}\n\n.bar {\n  border-radius: 2px;\n}\n\n.bar.sales {\n  width: 12px;\n  background: #FF7600;\n  margin-right: 4px;\n}\n\n.bar.orders {\n  width: 12px;\n  background: #007AFF;\n}\n\n.bar-label {\n  font-size: 10px;\n  color: #999999;\n  margin-top: 8px;\n}\n\n/* 秒杀活动技巧 */\n.tips-section {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.tips-list {\n  margin-top: 8px;\n}\n\n.tip-item {\n  display: flex;\n  padding: 12px 0;\n  border-bottom: 1px solid #F5F5F5;\n}\n\n.tip-item:last-child {\n  border-bottom: none;\n}\n\n.tip-icon {\n  width: 36px;\n  height: 36px;\n  margin-right: 12px;\n  color: #FF7600;\n}\n\n.tip-content {\n  flex: 1;\n}\n\n.tip-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 4px;\n}\n\n.tip-desc {\n  font-size: 14px;\n  color: #666666;\n  line-height: 1.5;\n}\n\n@media screen and (min-width: 768px) {\n  .overview-card {\n    width: 23%;\n  }\n  \n  .filter-buttons {\n    width: auto;\n    margin-top: 0;\n  }\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/flash/management.vue'\nwx.createPage(MiniProgramPage)"], "names": ["reactive", "ref", "computed", "uni", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;;;;;AA4NA,MAAM,eAAe,MAAW;;;;AAGhC,UAAM,YAAYA,cAAAA,SAAS;AAAA,MACzB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,eAAe;AAAA,IACjB,CAAC;AAGD,UAAM,eAAeC,cAAAA,IAAI,KAAK;AAC9B,UAAM,eAAe;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAGA,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,MAC7E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,KAAM;AAAA,MACjF,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,MAAO;AAAA,MAChF,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,MAC7E,EAAE,MAAM,SAAS,OAAO,KAAM,QAAQ,IAAI,aAAa,KAAK,cAAc,IAAK;AAAA,MAC/E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,GAAI;AAAA,MAC/E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,IAC/E,CAAC;AAGD,UAAM,qBAAqBC,cAAQ,SAAC,MAAM;AACxC,UAAI,aAAa,UAAU,OAAO;AAChC,eAAO,gBAAgB;AAAA,MACxB;AACD,aAAO,gBAAgB,MAAM,OAAO,UAAQ,KAAK,WAAW,aAAa,KAAK;AAAA,IAChF,CAAC;AAGD,UAAM,YAAY,CAAC,WAAW;AAC5B,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,eAAe,CAAC,UAAU;AAC9B,sBAAgB,QAAQ;AAExB,oBAAc,KAAK;AAAA,IACrB;AAGA,UAAM,gBAAgB,CAAC,UAAU;AAE/B,UAAI,UAAU,MAAM;AAClB,kBAAU,QAAQ;AAAA,UAChB,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,UAC7E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,KAAM;AAAA,UACjF,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,MAAO;AAAA,UAChF,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,UAC7E,EAAE,MAAM,SAAS,OAAO,KAAM,QAAQ,IAAI,aAAa,KAAK,cAAc,IAAK;AAAA,UAC/E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,GAAI;AAAA,UAC/E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,QACnF;AAAA,MACA,WAAa,UAAU,OAAO;AAC1B,kBAAU,QAAQ;AAAA,UAChB,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,UAC7E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,KAAM;AAAA,UACjF,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,MAAO;AAAA,UAChF,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,UAC7E,EAAE,MAAM,SAAS,OAAO,KAAM,QAAQ,IAAI,aAAa,KAAK,cAAc,IAAK;AAAA,UAC/E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,GAAI;AAAA,UAC/E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,QACnF;AAAA,MACA,WAAa,UAAU,OAAO;AAC1B,kBAAU,QAAQ;AAAA,UAChB,EAAE,MAAM,SAAS,OAAO,KAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,KAAM;AAAA,UACjF,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,GAAI;AAAA,UAC/E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,KAAM;AAAA,UACjF,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,IAAI,cAAc,GAAI;AAAA,UAC7E,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,KAAM;AAAA,UACjF,EAAE,MAAM,SAAS,OAAO,MAAM,QAAQ,IAAI,aAAa,MAAM,cAAc,MAAO;AAAA,UAClF,EAAE,MAAM,SAAS,OAAO,KAAM,QAAQ,IAAI,aAAa,KAAK,cAAc,IAAK;AAAA,QACrF;AAAA,MACG;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,SAAS;AAChCC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yEAAyE,KAAK,EAAE;AAAA,MACzF,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACjB,KAAK,uEAAuE,KAAK,EAAE;AAAA,MACvF,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,SAAS;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AAED,iBAAW,MAAM;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS,MAAM,KAAK,IAAI;AAAA,UACxB,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AAEfA,4BAAAA,MAAI,iBAAiB;AAAA,gBACnB,MAAM,kCAAkC,KAAK,EAAE;AAAA,gBAC/C,SAAS,MAAM;AACbA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBACtB,CAAe;AAAA,gBACF;AAAA,cACb,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF,GAAE,IAAI;AAAA,IACT;AAGA,UAAM,kBAAkB,CAAC,SAAS;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,IAAI;AAAA,QAC3B,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEf,kBAAM,QAAQ,gBAAgB,MAAM,UAAU,OAAK,EAAE,OAAO,KAAK,EAAE;AACnE,gBAAI,UAAU,IAAI;AAChB,8BAAgB,MAAM,OAAO,OAAO,CAAC;AACrCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGAC,kBAAAA,UAAU,MAAM;AACdD,oBAAAA,MAAY,MAAA,OAAA,oFAAA,aAAa;AAAA,IAE3B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACldD,GAAG,WAAWE,SAAe;"}