{"version": 3, "file": "create-package-type.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/group/create-package-type.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xncm91cFxjcmVhdGUtcGFja2FnZS10eXBlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"create-package-type-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">拼团活动</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 步骤指示器 -->\n    <view class=\"step-indicator\">\n      <view class=\"step-progress\">\n        <view class=\"step-progress-bar\" style=\"width: 20%\"></view>\n      </view>\n      <view class=\"step-text\">步骤 1/5</view>\n    </view>\n    \n    <!-- 页面内容 -->\n    <scroll-view scroll-y class=\"page-content\">\n      <view class=\"page-title\">选择套餐类型</view>\n      <view class=\"page-subtitle\">请选择适合您业务的团购套餐类型</view>\n      \n      <view class=\"package-types\">\n        <view class=\"package-type\" :class=\"{ active: selectedType === 'food' }\" @tap=\"selectType('food')\">\n          <view class=\"type-icon food-icon\"></view>\n          <view class=\"type-info\">\n            <text class=\"type-name\">餐饮套餐</text>\n            <text class=\"type-desc\">适合餐厅、饭店等餐饮商家</text>\n          </view>\n          <view class=\"type-check\" v-if=\"selectedType === 'food'\"></view>\n        </view>\n        \n        <view class=\"package-type\" :class=\"{ active: selectedType === 'service' }\" @tap=\"selectType('service')\">\n          <view class=\"type-icon service-icon\"></view>\n          <view class=\"type-info\">\n            <text class=\"type-name\">服务套餐</text>\n            <text class=\"type-desc\">适合美容、洗车等服务商家</text>\n          </view>\n          <view class=\"type-check\" v-if=\"selectedType === 'service'\"></view>\n        </view>\n        \n        <view class=\"package-type\" :class=\"{ active: selectedType === 'product' }\" @tap=\"selectType('product')\">\n          <view class=\"type-icon product-icon\"></view>\n          <view class=\"type-info\">\n            <text class=\"type-name\">商品套餐</text>\n            <text class=\"type-desc\">适合零售、超市等商品销售</text>\n          </view>\n          <view class=\"type-check\" v-if=\"selectedType === 'product'\"></view>\n        </view>\n        \n        <view class=\"package-type\" :class=\"{ active: selectedType === 'custom' }\" @tap=\"selectType('custom')\">\n          <view class=\"type-icon custom-icon\"></view>\n          <view class=\"type-info\">\n            <text class=\"type-name\">自定义套餐</text>\n            <text class=\"type-desc\">自由组合商品和服务</text>\n          </view>\n          <view class=\"type-check\" v-if=\"selectedType === 'custom'\"></view>\n        </view>\n      </view>\n      \n      <!-- 支付和核销设置 -->\n      <view class=\"settings-section\">\n        <view class=\"section-title\">支付和核销设置</view>\n        \n        <view class=\"setting-item\">\n          <view class=\"setting-label\">支付方式</view>\n          <view class=\"setting-options\">\n            <view class=\"option\" :class=\"{ active: paymentType === 'offline' }\" @tap=\"setPaymentType('offline')\">\n              <text>到店支付</text>\n              <view class=\"option-check\" v-if=\"paymentType === 'offline'\"></view>\n            </view>\n            <view class=\"option\" :class=\"{ active: paymentType === 'online' }\" @tap=\"setPaymentType('online')\">\n              <text>在线支付</text>\n              <view class=\"option-check\" v-if=\"paymentType === 'online'\"></view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"setting-item\">\n          <view class=\"setting-label\">核销方式</view>\n          <view class=\"setting-options\">\n            <view class=\"option\" :class=\"{ active: verifyType === 'offline' }\" @tap=\"setVerifyType('offline')\">\n              <text>到店核销</text>\n              <view class=\"option-check\" v-if=\"verifyType === 'offline'\"></view>\n            </view>\n            <view class=\"option\" :class=\"{ active: verifyType === 'online' }\" @tap=\"setVerifyType('online')\">\n              <text>在线核销</text>\n              <view class=\"option-check\" v-if=\"verifyType === 'online'\"></view>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"setting-item\" v-if=\"verifyType === 'offline'\">\n          <view class=\"setting-label\">核销码有效期</view>\n          <view class=\"setting-input\">\n            <input type=\"number\" v-model=\"verifyCodeValidDays\" class=\"days-input\" />\n            <text class=\"unit\">天</text>\n          </view>\n        </view>\n        \n        <view class=\"setting-item\" v-if=\"verifyType === 'offline'\">\n          <view class=\"setting-label\">可核销次数</view>\n          <view class=\"setting-input\">\n            <input type=\"number\" v-model=\"verifyTimes\" class=\"times-input\" />\n            <text class=\"unit\">次</text>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部按钮 -->\n    <view class=\"footer-buttons\">\n      <button class=\"btn btn-secondary\" @click=\"goBack\">取消</button>\n      <button class=\"btn btn-primary\" @click=\"nextStep\">下一步</button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      selectedType: 'food',\n      paymentType: 'offline', // offline: 到店支付, online: 在线支付\n      verifyType: 'offline', // offline: 到店核销, online: 在线核销\n      verifyCodeValidDays: 30,\n      verifyTimes: 1\n    }\n  },\n  onLoad() {\n    // 尝试从本地存储获取之前保存的数据\n    try {\n      const savedType = uni.getStorageSync('packageType');\n      const savedPaymentInfo = uni.getStorageSync('packagePaymentInfo');\n      \n      if (savedType) {\n        this.selectedType = savedType;\n      }\n      \n      if (savedPaymentInfo) {\n        const paymentInfo = JSON.parse(savedPaymentInfo);\n        this.paymentType = paymentInfo.paymentType;\n        this.verifyType = paymentInfo.verifyType;\n        this.verifyCodeValidDays = paymentInfo.verifyCodeValidDays;\n        this.verifyTimes = paymentInfo.verifyTimes;\n      }\n    } catch (e) {\n      console.error('读取本地存储失败:', e);\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack();\n    },\n    selectType(type) {\n      this.selectedType = type;\n    },\n    setPaymentType(type) {\n      this.paymentType = type;\n    },\n    setVerifyType(type) {\n      this.verifyType = type;\n    },\n    nextStep() {\n      // 保存选择的套餐类型和支付核销设置\n      try {\n        uni.setStorageSync('packageType', this.selectedType);\n        \n        const paymentInfo = {\n          paymentType: this.paymentType,\n          verifyType: this.verifyType,\n          verifyCodeValidDays: this.verifyCodeValidDays,\n          verifyTimes: this.verifyTimes\n        };\n        \n        uni.setStorageSync('packagePaymentInfo', JSON.stringify(paymentInfo));\n      } catch (e) {\n        console.error('保存数据失败:', e);\n      }\n      \n      // 跳转到下一步\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-info'\n      });\n    },\n    showHelp() {\n      uni.showToast({\n        title: '帮助信息',\n        icon: 'none'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.create-package-type-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: 1px solid #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #fff;\n}\n\n/* 步骤指示器 */\n.step-indicator {\n  padding: 15px;\n  background: #FFFFFF;\n}\n\n.step-progress {\n  height: 4px;\n  background-color: #EBEDF5;\n  border-radius: 2px;\n  margin-bottom: 5px;\n  position: relative;\n}\n\n.step-progress-bar {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  background: linear-gradient(90deg, #9040FF, #5E35B1);\n  border-radius: 2px;\n}\n\n.step-text {\n  font-size: 12px;\n  color: #999;\n  text-align: right;\n}\n\n/* 页面内容 */\n.page-content {\n  flex: 1;\n  padding: 20px 15px;\n}\n\n.page-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 20px;\n}\n\n/* 套餐类型样式 */\n.package-types {\n  margin-bottom: 20px;\n}\n\n.package-type {\n  display: flex;\n  align-items: center;\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  position: relative;\n}\n\n.package-type.active {\n  border: 2px solid #9040FF;\n}\n\n.type-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  margin-right: 15px;\n  background-position: center;\n  background-repeat: no-repeat;\n  background-size: 24px;\n}\n\n.food-icon {\n  background-color: #FFE8CC;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23FF9500\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z\"/></svg>');\n}\n\n.service-icon {\n  background-color: #E3F2FD;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%230A84FF\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z\"/></svg>');\n}\n\n.product-icon {\n  background-color: #E8F5E9;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%2334C759\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z\"/></svg>');\n}\n\n.custom-icon {\n  background-color: #F0E8FF;\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%239040FF\"><path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7z\"/></svg>');\n}\n\n.type-info {\n  flex: 1;\n}\n\n.type-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n  display: block;\n}\n\n.type-desc {\n  font-size: 12px;\n  color: #999;\n}\n\n.type-check {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: #9040FF;\n  position: absolute;\n  top: 15px;\n  right: 15px;\n}\n\n.type-check::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 10px;\n  height: 5px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: translate(-50%, -60%) rotate(-45deg);\n}\n\n/* 设置区域样式 */\n.settings-section {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px dashed #EBEDF5;\n}\n\n.setting-item {\n  margin-bottom: 15px;\n}\n\n.setting-item:last-child {\n  margin-bottom: 0;\n}\n\n.setting-label {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 10px;\n}\n\n.setting-options {\n  display: flex;\n  gap: 15px;\n}\n\n.option {\n  flex: 1;\n  height: 40px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #333;\n  position: relative;\n  border: 1px solid #EBEDF5;\n}\n\n.option.active {\n  background: #F0E8FF;\n  border-color: #9040FF;\n  color: #9040FF;\n}\n\n.option-check {\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  background: #9040FF;\n  position: absolute;\n  top: -8px;\n  right: -8px;\n}\n\n.option-check::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 8px;\n  height: 4px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: translate(-50%, -60%) rotate(-45deg);\n}\n\n.setting-input {\n  display: flex;\n  align-items: center;\n}\n\n.days-input, .times-input {\n  width: 60px;\n  height: 40px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  border: 1px solid #EBEDF5;\n  text-align: center;\n  font-size: 14px;\n  color: #333;\n  margin-right: 10px;\n}\n\n.unit {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 底部按钮 */\n.footer-buttons {\n  padding: 15px;\n  background: #FFFFFF;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-between;\n  gap: 15px;\n}\n\n.btn {\n  flex: 1;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: 600;\n  border: none;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #FFFFFF;\n}\n\n.btn-secondary {\n  background: #F5F7FA;\n  color: #666;\n  border: 1px solid #EBEDF5;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-type.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2HA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,cAAc;AAAA,MACd,aAAa;AAAA;AAAA,MACb,YAAY;AAAA;AAAA,MACZ,qBAAqB;AAAA,MACrB,aAAa;AAAA,IACf;AAAA,EACD;AAAA,EACD,SAAS;AAEP,QAAI;AACF,YAAM,YAAYA,cAAAA,MAAI,eAAe,aAAa;AAClD,YAAM,mBAAmBA,cAAAA,MAAI,eAAe,oBAAoB;AAEhE,UAAI,WAAW;AACb,aAAK,eAAe;AAAA,MACtB;AAEA,UAAI,kBAAkB;AACpB,cAAM,cAAc,KAAK,MAAM,gBAAgB;AAC/C,aAAK,cAAc,YAAY;AAC/B,aAAK,aAAa,YAAY;AAC9B,aAAK,sBAAsB,YAAY;AACvC,aAAK,cAAc,YAAY;AAAA,MACjC;AAAA,IACF,SAAS,GAAG;AACVA,oBAAc,MAAA,MAAA,SAAA,6FAAA,aAAa,CAAC;AAAA,IAC9B;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW,MAAM;AACf,WAAK,eAAe;AAAA,IACrB;AAAA,IACD,eAAe,MAAM;AACnB,WAAK,cAAc;AAAA,IACpB;AAAA,IACD,cAAc,MAAM;AAClB,WAAK,aAAa;AAAA,IACnB;AAAA,IACD,WAAW;AAET,UAAI;AACFA,sBAAAA,MAAI,eAAe,eAAe,KAAK,YAAY;AAEnD,cAAM,cAAc;AAAA,UAClB,aAAa,KAAK;AAAA,UAClB,YAAY,KAAK;AAAA,UACjB,qBAAqB,KAAK;AAAA,UAC1B,aAAa,KAAK;AAAA;AAGpBA,sBAAG,MAAC,eAAe,sBAAsB,KAAK,UAAU,WAAW,CAAC;AAAA,MACtE,SAAS,GAAG;AACVA,wIAAc,WAAW,CAAC;AAAA,MAC5B;AAGAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnMA,GAAG,WAAW,eAAe;"}