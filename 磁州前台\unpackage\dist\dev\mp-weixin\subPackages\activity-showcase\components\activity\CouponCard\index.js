"use strict";
const common_vendor = require("../../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_line = common_vendor.resolveComponent("line");
  (_component_path + _component_svg + _component_rect + _component_line)();
}
if (!Math) {
  ActivityCard();
}
const ActivityCard = () => "../ActivityCard.js";
const _sfc_main = {
  __name: "index",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const couponTypeText = common_vendor.computed(() => {
      if (!props.item.couponType)
        return "优惠券";
      switch (props.item.couponType) {
        case "cash":
          return "现金券";
        case "discount":
          return "折扣券";
        case "exchange":
          return "兑换券";
        case "gift":
          return "礼品券";
        default:
          return "优惠券";
      }
    });
    const progressWidth = common_vendor.computed(() => {
      if (!props.item.claimedCount || !props.item.totalCount)
        return 0;
      return props.item.claimedCount / props.item.totalCount * 100;
    });
    const remainCount = common_vendor.computed(() => {
      if (props.item.claimedCount === void 0 || !props.item.totalCount)
        return 0;
      return props.item.totalCount - props.item.claimedCount;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.item.couponType === "cash"
      }, __props.item.couponType === "cash" ? {} : {}, {
        b: common_vendor.t(__props.item.couponValue),
        c: __props.item.couponType === "discount"
      }, __props.item.couponType === "discount" ? {} : {}, {
        d: common_vendor.t(couponTypeText.value),
        e: __props.item.couponCondition
      }, __props.item.couponCondition ? {
        f: common_vendor.p({
          d: "M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
        }),
        g: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        h: common_vendor.t(__props.item.couponCondition)
      } : {}, {
        i: __props.item.couponScope
      }, __props.item.couponScope ? {
        j: common_vendor.p({
          x: "3",
          y: "3",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2"
        }),
        k: common_vendor.p({
          x1: "9",
          y1: "3",
          x2: "9",
          y2: "21"
        }),
        l: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.t(__props.item.couponScope)
      } : {}, {
        n: common_vendor.p({
          x: "3",
          y: "4",
          width: "18",
          height: "18",
          rx: "2",
          ry: "2"
        }),
        o: common_vendor.p({
          x1: "16",
          y1: "2",
          x2: "16",
          y2: "6"
        }),
        p: common_vendor.p({
          x1: "8",
          y1: "2",
          x2: "8",
          y2: "6"
        }),
        q: common_vendor.p({
          x1: "3",
          y1: "10",
          x2: "21",
          y2: "10"
        }),
        r: common_vendor.p({
          xmlns: "http://www.w3.org/2000/svg",
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        s: common_vendor.t(__props.item.couponValidity),
        t: __props.item.totalCount && __props.item.claimedCount !== void 0
      }, __props.item.totalCount && __props.item.claimedCount !== void 0 ? common_vendor.e({
        v: common_vendor.t(__props.item.claimedCount),
        w: common_vendor.t(__props.item.totalCount),
        x: progressWidth.value + "%",
        y: remainCount.value > 0
      }, remainCount.value > 0 ? {
        z: common_vendor.t(remainCount.value)
      } : {}) : {}, {
        A: common_vendor.o(($event) => _ctx.$emit("favorite", __props.item.id)),
        B: common_vendor.o(($event) => _ctx.$emit("action", {
          id: __props.item.id,
          type: __props.item.type,
          status: __props.item.status
        })),
        C: common_vendor.p({
          item: __props.item
        })
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8566755a"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/activity/CouponCard/index.js.map
