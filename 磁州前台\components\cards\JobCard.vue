<template>
  <BaseInfoCard :item="item">
    <template #content>
      <view class="job-details">
        <!-- 公司信息 -->
        <view class="company-info" v-if="item.company">
          <view class="company-logo" v-if="item.companyLogo">
            <image :src="item.companyLogo" class="company-logo-img" mode="aspectFill"></image>
          </view>
          <view class="company-data">
            <view class="company-name">{{item.company}}</view>
            <view class="company-meta">
              <text class="company-size" v-if="item.companySize">{{item.companySize}}</text>
              <text class="company-type" v-if="item.companyType">{{item.companyType}}</text>
            </view>
          </view>
          <view class="company-auth" v-if="item.isVerified">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <text class="auth-text">已认证</text>
          </view>
        </view>
        
        <!-- 职位要求 -->
        <view class="job-requirements">
          <view class="req-item" v-if="item.education">
            <view class="req-icon education-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 10v6M2 10l10-5 10 5-10 5z"></path>
                <path d="M6 12v5c3 3 9 3 12 0v-5"></path>
              </svg>
            </view>
            <text class="req-text">{{item.education}}</text>
          </view>
          
          <view class="req-item" v-if="item.experience">
            <view class="req-icon experience-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
              </svg>
            </view>
            <text class="req-text">{{item.experience}}</text>
          </view>
          
          <view class="req-item" v-if="item.jobType">
            <view class="req-icon jobtype-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
            </view>
            <text class="req-text">{{item.jobType}}</text>
          </view>
        </view>
        
        <!-- 福利标签 - 过滤掉与tags重复的项 -->
        <view class="job-benefits" v-if="filteredBenefits.length">
          <view class="benefit-tag" v-for="(benefit, index) in filteredBenefits" :key="index">
            <text class="benefit-text">{{benefit}}</text>
          </view>
        </view>
      </view>
    </template>
  </BaseInfoCard>
</template>

<script setup>
import { computed } from 'vue';
import BaseInfoCard from './BaseInfoCard.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

// 过滤掉与tags重复的福利项
const filteredBenefits = computed(() => {
  if (!props.item.benefits || !props.item.benefits.length) return [];
  if (!props.item.tags || !props.item.tags.length) return props.item.benefits;
  
  // 将tags转换为小写，用于不区分大小写的比较
  const tagsLowerCase = props.item.tags.map(tag => tag.toLowerCase());
  
  // 过滤benefits，去除与tags重复的项
  return props.item.benefits.filter(benefit => 
    !tagsLowerCase.includes(benefit.toLowerCase())
  );
});
</script>

<style scoped>
.job-details {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

/* 公司信息 */
.company-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  background: rgba(0, 0, 0, 0.02);
  padding: 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.company-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 16rpx;
  background: #fff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.company-logo-img {
  width: 100%;
  height: 100%;
}

.company-data {
  flex: 1;
}

.company-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1c1c1e;
  margin-bottom: 6rpx;
}

.company-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.company-size, .company-type {
  font-size: 22rpx;
  color: #8e8e93;
}

.company-auth {
  display: flex;
  align-items: center;
  background: rgba(0, 122, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}

.auth-text {
  font-size: 22rpx;
  color: #007AFF;
  margin-left: 4rpx;
}

/* 职位要求 */
.job-requirements {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.req-item {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.02);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
}

.req-icon {
  margin-right: 8rpx;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.education-icon {
  color: #5856D6;
}

.experience-icon {
  color: #FF9500;
}

.jobtype-icon {
  color: #34C759;
}

.req-text {
  font-size: 24rpx;
  color: #636366;
}

/* 福利标签 */
.job-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.benefit-tag {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid rgba(0, 122, 255, 0.2);
}

.benefit-text {
  font-size: 22rpx;
  color: #007AFF;
  font-weight: 500;
}
</style> 