<template>
  <view class="activity-my-container">
    <!-- 自定义导航栏 - 强调返回到平台主"我的"页面 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="backToMainMyPage">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">我的活动</view>
        <view class="navbar-right">
          <view class="filter-btn" @click="showActivityFilter">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y="true" 
      refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <!-- 用户信息模块 -->
      <view class="user-profile-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(0,0,0,0.08)',
        background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
        padding: '30rpx',
        marginBottom: '30rpx',
        position: 'relative',
        overflow: 'hidden'
      }">
        <!-- 背景装饰 -->
        <view class="profile-bg-decoration" :style="{
          position: 'absolute',
          top: '-50rpx',
          right: '-50rpx',
          width: '300rpx',
          height: '300rpx',
          borderRadius: '50%',
          background: 'rgba(255,255,255,0.1)',
          zIndex: '1'
        }"></view>
        <view class="profile-bg-decoration" :style="{
          position: 'absolute',
          bottom: '-80rpx',
          left: '-80rpx',
          width: '250rpx',
          height: '250rpx',
          borderRadius: '50%',
          background: 'rgba(255,255,255,0.08)',
          zIndex: '1'
        }"></view>
        
        <!-- 用户基本信息 -->
        <view class="user-basic-info" :style="{
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          zIndex: '2'
        }">
          <view class="avatar-container" :style="{
            position: 'relative',
            marginRight: '20rpx'
          }">
            <image 
              class="user-avatar" 
              :src="userInfo.avatar || '/static/images/default-avatar.png'" 
              mode="aspectFill"
              :style="{
                width: '120rpx',
                height: '120rpx',
                borderRadius: '50%',
                border: '4rpx solid rgba(255,255,255,0.8)',
                boxShadow: '0 4rpx 10rpx rgba(0,0,0,0.1)'
              }"
            ></image>
            <view class="vip-badge" v-if="userInfo.isVip" :style="{
              position: 'absolute',
              bottom: '0',
              right: '0',
              background: '#FFD700',
              color: '#8B4513',
              fontSize: '20rpx',
              fontWeight: 'bold',
              padding: '4rpx 10rpx',
              borderRadius: '10rpx',
              transform: 'translateY(50%)',
              boxShadow: '0 2rpx 5rpx rgba(0,0,0,0.2)'
            }">VIP</view>
            </view>
          
          <view class="user-info" :style="{
            flex: '1'
          }">
            <view class="user-name-row" :style="{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '10rpx'
            }">
              <text class="user-name" :style="{
                fontSize: '36rpx',
                fontWeight: 'bold',
                color: '#FFFFFF',
                marginRight: '10rpx',
                textShadow: '0 2rpx 4rpx rgba(0,0,0,0.1)'
              }">{{ userInfo.nickname || '游客' }}</text>
              <view class="level-tag" :style="{
                fontSize: '22rpx',
                color: '#FF3B69',
                background: 'rgba(255,255,255,0.9)',
                padding: '2rpx 10rpx',
                borderRadius: '10rpx',
                fontWeight: '500'
              }">Lv.{{ userInfo.level || 1 }}</view>
          </view>
            
            <text class="user-id" :style="{
              fontSize: '24rpx',
              color: 'rgba(255,255,255,0.8)',
              marginBottom: '10rpx',
              display: 'block'
            }">ID: {{ userInfo.id || '未设置' }}</text>
            
            <view class="user-points" :style="{
              display: 'flex',
              alignItems: 'center'
            }">
              <text class="points-label" :style="{
                fontSize: '24rpx',
                color: 'rgba(255,255,255,0.9)'
              }">积分:</text>
              <text class="points-value" :style="{
                fontSize: '28rpx',
                fontWeight: '600',
                color: '#FFFFFF',
                marginLeft: '8rpx'
              }">{{ userInfo.points || 0 }}</text>
          </view>
        </view>
        
          <view class="profile-action" @click="navigateTo('/subPackages/activity-showcase/pages/user-profile/index')" :style="{
            padding: '10rpx 30rpx',
            background: 'rgba(255,255,255,0.2)',
            borderRadius: '30rpx',
            fontSize: '26rpx',
            color: '#FFFFFF',
            fontWeight: '500',
            border: '1rpx solid rgba(255,255,255,0.3)'
          }">
            编辑资料
            </view>
          </view>
        
        <!-- 用户数据统计 -->
        <view class="user-stats" :style="{
          display: 'flex',
          justifyContent: 'space-around',
          marginTop: '30rpx',
          position: 'relative',
          zIndex: '2',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '20rpx',
          padding: '20rpx 0'
        }">
          <view class="stat-item" @click="navigateTo('/subPackages/activity-showcase/pages/favorites/index')">
            <text class="stat-value" :style="{
              fontSize: '32rpx',
              fontWeight: 'bold',
              color: '#FFFFFF',
              display: 'block',
              textAlign: 'center'
            }">{{ userInfo.favoriteCount || 0 }}</text>
            <text class="stat-label" :style="{
              fontSize: '24rpx',
              color: 'rgba(255,255,255,0.8)',
              display: 'block',
              textAlign: 'center'
            }">收藏</text>
          </view>
          
          <view class="stat-item" @click="navigateTo('/subPackages/activity-showcase/pages/history/index')">
            <text class="stat-value" :style="{
              fontSize: '32rpx',
              fontWeight: 'bold',
              color: '#FFFFFF',
              display: 'block',
              textAlign: 'center'
            }">{{ userInfo.historyCount || 0 }}</text>
            <text class="stat-label" :style="{
              fontSize: '24rpx',
              color: 'rgba(255,255,255,0.8)',
              display: 'block',
              textAlign: 'center'
            }">浏览</text>
        </view>
        
          <view class="stat-item" @click="navigateTo('/subPackages/activity-showcase/pages/coupon/index')">
            <text class="stat-value" :style="{
              fontSize: '32rpx',
              fontWeight: 'bold',
              color: '#FFFFFF',
              display: 'block',
              textAlign: 'center'
            }">{{ userInfo.couponCount || 0 }}</text>
            <text class="stat-label" :style="{
              fontSize: '24rpx',
              color: 'rgba(255,255,255,0.8)',
              display: 'block',
              textAlign: 'center'
            }">优惠券</text>
          </view>
          
          <view class="stat-item" @click="navigateTo('/subPackages/activity-showcase/pages/orders/index')">
            <text class="stat-value" :style="{
              fontSize: '32rpx',
              fontWeight: 'bold',
              color: '#FFFFFF',
              display: 'block',
              textAlign: 'center'
            }">{{ userInfo.orderCount || 0 }}</text>
            <text class="stat-label" :style="{
              fontSize: '24rpx',
              color: 'rgba(255,255,255,0.8)',
              display: 'block',
              textAlign: 'center'
            }">订单</text>
          </view>
          </view>
        </view>
        
      <!-- 统一活动中心 -->
      <view class="activity-center-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(255,59,105,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-header">
          <text class="card-title">活动中心</text>
          <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/activity-records/index')">
            <text class="view-all">查看全部</text>
              <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
        </view>
      </view>

        <!-- 主标签容器 -->
        <view class="main-tabs-container">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index"
            class="main-tab-item"
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          <text class="tab-text">{{ tab.name }}</text>
          <view class="tab-indicator" v-if="currentTab === index" :style="{
            background: 'linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%)'
          }"></view>
        </view>
      </view>
        
        <!-- 子标签容器 -->
        <view class="sub-tabs-container">
          <view 
            v-for="(activityType, index) in activityTypes" 
            :key="index"
            class="sub-tab-item"
            :class="{ active: currentActivityType === index }"
            @click="switchActivityType(index)"
          >
            <text class="tab-text">{{ activityType.name }}</text>
            <view class="tab-indicator" v-if="currentActivityType === index" :style="{
            background: 'linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%)'
          }"></view>
        </view>
      </view>

      <!-- 活动列表区域 -->
      <view class="activities-container">
        <swiper class="activities-swiper" :current="currentTab" @change="onSwiperChange" :style="{ height: swiperHeight + 'px' }">
          <swiper-item v-for="(tab, tabIndex) in tabs" :key="tabIndex">
            <scroll-view class="tab-content" scroll-y :id="`tab-content-${tabIndex}`">
              <view class="activity-list" :id="`activity-list-${tabIndex}`">
                <ActivityStatusCard 
                    v-for="activity in getFilteredActivities(tab.status, activityTypes[currentActivityType].type)" 
                  :key="activity.id"
                  :activity="activity"
                    :showCountdown="activity.type === 'flash'"
                    :canCancel="activity.status === 'registered'"
                  @click="viewActivityDetail(activity)"
                  @share="shareActivity(activity)"
                  @cancel="cancelActivity(activity)"
                    @favorite="activity.isFavorite = !activity.isFavorite"
                />
                </view>
                
                <!-- 空状态 -->
                <view class="empty-state" v-if="getFilteredActivities(tab.status, activityTypes[currentActivityType].type).length === 0">
                  <image class="empty-image" :src="tab.emptyImage"></image>
                  <text class="empty-text">{{ tab.emptyText }}</text>
                  <view class="action-btn" @click="navigateTo('/subPackages/activity-showcase/pages/index/index')" :style="{
                    background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
                    borderRadius: '35px',
                    boxShadow: '0 5px 15px rgba(255,59,105,0.3)'
                  }">
                    <text>{{ tab.actionText }}</text>
                </view>
              </view>
            </scroll-view>
          </swiper-item>
        </swiper>
      </view>
      
      <!-- 订单管理卡片 -->
      <view class="orders-card" :style="{
          borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(90,200,250,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="card-header">
          <text class="card-title">订单管理</text>
          <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/orders/index')">
            <text class="view-all">查看全部</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="order-status-grid">
          <view class="status-item" @click="navigateTo('/subPackages/activity-showcase/pages/orders/index?status=pending_payment')">
            <view class="status-icon" :style="{
              background: 'rgba(90,200,250,0.1)',
              borderRadius: '50%'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <rect x="2" y="4" width="20" height="16" rx="2" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                <line x1="12" y1="16" x2="12" y2="16.01" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                <path d="M8 12h8M8 8h8" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            <text class="status-name">待付款</text>
            <view class="status-badge" v-if="orderCounts.pendingPayment > 0">{{ orderCounts.pendingPayment }}</view>
          </view>
          
          <view class="status-item" @click="navigateTo('/subPackages/activity-showcase/pages/orders/index?status=pending_delivery')">
            <view class="status-icon" :style="{
              background: 'rgba(90,200,250,0.1)',
              borderRadius: '50%'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <rect x="2" y="4" width="20" height="16" rx="2" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></rect>
                <path d="M16 10V6M8 10V6M4 10h16" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            <text class="status-name">待发货</text>
            <view class="status-badge" v-if="orderCounts.pendingDelivery > 0">{{ orderCounts.pendingDelivery }}</view>
          </view>
          
          <view class="status-item" @click="navigateTo('/subPackages/activity-showcase/pages/orders/index?status=pending_receipt')">
            <view class="status-icon" :style="{
              background: 'rgba(90,200,250,0.1)',
              borderRadius: '50%'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <circle cx="12" cy="10" r="3" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              </svg>
            </view>
            <text class="status-name">待收货</text>
            <view class="status-badge" v-if="orderCounts.pendingReceipt > 0">{{ orderCounts.pendingReceipt }}</view>
          </view>
          
          <view class="status-item" @click="navigateTo('/subPackages/activity-showcase/pages/orders/index?status=pending_review')">
            <view class="status-icon" :style="{
              background: 'rgba(90,200,250,0.1)',
              borderRadius: '50%'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            <text class="status-name">待评价</text>
            <view class="status-badge" v-if="orderCounts.pendingReview > 0">{{ orderCounts.pendingReview }}</view>
          </view>
          
          <view class="status-item" @click="navigateTo('/subPackages/activity-showcase/pages/orders/index?status=after_sale')">
            <view class="status-icon" :style="{
              background: 'rgba(90,200,250,0.1)',
              borderRadius: '50%'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <path d="M16 15v4a2 2 0 01-2 2h-4a2 2 0 01-2-2v-4M8.929 9.571L12 12.643l3.071-3.072M12 3v9.643" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            <text class="status-name">售后/退款</text>
            <view class="status-badge" v-if="orderCounts.afterSale > 0">{{ orderCounts.afterSale }}</view>
          </view>
        </view>
      </view>
      
      <!-- 优惠券专区 -->
      <view class="coupon-card" :style="{
          borderRadius: '35px',
        boxShadow: '0 10px 25px rgba(255,149,0,0.12)',
        background: 'linear-gradient(to bottom, #FFFFFF, #FFFAF2)',
        padding: '30rpx',
        marginBottom: '30rpx',
        border: '1rpx solid rgba(255,149,0,0.1)'
      }">
        <view class="card-header">
          <text class="card-title">优惠券专区</text>
          <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/coupon/index')">
            <text class="view-all">查看全部</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="coupon-status-tabs">
          <view 
            v-for="(couponStatus, index) in couponStatuses" 
            :key="index"
            class="coupon-status-tab"
            :class="{ active: currentCouponStatus === index }"
            @click="switchCouponStatus(index)"
          >
            <text>{{ couponStatus.name }}</text>
            <view class="tab-indicator" v-if="currentCouponStatus === index" :style="{
              background: 'linear-gradient(90deg, #FF9500 0%, #FFCC00 100%)'
            }"></view>
          </view>
        </view>
        
        <view class="coupons-list">
          <view 
            v-for="(coupon, index) in getCouponsByStatus(couponStatuses[currentCouponStatus].status)"
            :key="coupon.id"
            class="coupon-item"
            :style="{
              background: getCouponBackground(coupon.status)
            }"
            @click="viewCouponDetail(coupon)"
          >
            <view class="coupon-left">
              <view class="coupon-value">
                <text class="currency">¥</text>
                <text class="amount">{{ coupon.value }}</text>
              </view>
              <text class="coupon-condition">{{ coupon.condition }}</text>
            </view>
            <view class="coupon-divider"></view>
            <view class="coupon-right">
              <text class="coupon-name">{{ coupon.name }}</text>
              <text class="coupon-shop">{{ coupon.shopName }}</text>
              <text class="coupon-validity">{{ coupon.validityPeriod }}</text>
              <view 
                v-if="coupon.status === 'unused'" 
                class="coupon-use-btn" 
                :style="{ 
                  background: 'rgba(255,128,0,0.9)',
                  border: '1rpx solid rgba(255,128,0,0.3)'
                }"
                @click.stop="useCoupon(coupon)"
              >
                <text>立即使用</text>
              </view>
              <view 
                v-else-if="coupon.status === 'used'" 
                class="coupon-use-btn" 
                :style="{ 
                  background: 'rgba(46,184,77,0.9)',
                  border: '1rpx solid rgba(46,184,77,0.3)'
                }"
              >
                <text>已使用</text>
              </view>
              <view 
                v-else 
                class="coupon-use-btn" 
                :style="{ 
                  background: 'rgba(142,142,147,0.9)',
                  border: '1rpx solid rgba(142,142,147,0.3)'
                }"
              >
                <text>已过期</text>
              </view>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-state" v-if="getCouponsByStatus(couponStatuses[currentCouponStatus].status).length === 0">
            <image class="empty-image" :src="couponStatuses[currentCouponStatus].emptyImage"></image>
            <text class="empty-text">{{ couponStatuses[currentCouponStatus].emptyText }}</text>
            <view class="action-btn" @click="navigateTo('/subPackages/activity-showcase/pages/coupon/index')" :style="{
              background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',
              borderRadius: '35px',
              boxShadow: '0 5px 15px rgba(255,149,0,0.3)'
            }">
              <text>去领取优惠券</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 活动工具箱 -->
      <view class="tools-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(88,86,214,0.15)',
        background: '#FFFFFF',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="toolbox-header">
          <text class="toolbox-title">活动工具</text>
        </view>
        <view class="tool-grid">
          <view class="tool-item" @click="navigateTo('/subPackages/activity-showcase/pages/reminders/index')">
            <view class="tool-icon reminder-icon" :style="{
              background: 'linear-gradient(135deg, #FF9500 0%, #FF3B30 100%)',
              borderRadius: '50%',
              boxShadow: '0 5px 15px rgba(255,59,48,0.2)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            <text class="tool-name">活动提醒</text>
          </view>
          
          <view class="tool-item" @click="navigateTo('/subPackages/activity-showcase/pages/share-records/index')">
            <view class="tool-icon share-icon" :style="{
              background: 'linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%)',
              borderRadius: '50%',
              boxShadow: '0 5px 15px rgba(255,59,105,0.2)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <circle cx="18" cy="5" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <circle cx="6" cy="12" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <circle cx="18" cy="19" r="3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              </svg>
            </view>
            <text class="tool-name">分享工具</text>
          </view>
          
          <view class="tool-item" @click="navigateTo('/subPackages/activity-showcase/pages/favorites/index')">
            <view class="tool-icon favorite-icon" :style="{
              background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',
              borderRadius: '50%',
              boxShadow: '0 5px 15px rgba(255,149,0,0.2)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
            <text class="tool-name">收藏夹</text>
          </view>
          
          <view class="tool-item" @click="navigateTo('/subPackages/activity-showcase/pages/history/index')">
            <view class="tool-icon history-icon" :style="{
              background: 'linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%)',
              borderRadius: '50%',
              boxShadow: '0 5px 15px rgba(142,142,147,0.2)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <circle cx="12" cy="12" r="10" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <polyline points="12 6 12 12 16 14" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polyline>
              </svg>
            </view>
            <text class="tool-name">浏览记录</text>
          </view>
          
          <view class="tool-item" @click="navigateToMainSettings">
            <view class="tool-icon help-icon" :style="{
              background: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)',
              borderRadius: '50%',
              boxShadow: '0 5px 15px rgba(52,199,89,0.2)'
            }">
              <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
                <circle cx="12" cy="12" r="10" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <line x1="12" y1="17" x2="12.01" y2="17" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              </svg>
            </view>
            <text class="tool-name">帮助</text>
          </view>
        </view>
      </view>
      
      <!-- 推荐活动模块 -->
      <view class="recommended-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(90,200,250,0.15)',
        background: 'linear-gradient(to bottom, #FFFFFF, #F0F8FF)',
        padding: '30rpx',
        marginBottom: '30rpx',
        border: '1rpx solid rgba(90,200,250,0.1)'
      }">
        <view class="card-header">
          <text class="card-title">为您推荐</text>
          <view class="header-right" @click="navigateTo('/subPackages/activity-showcase/pages/recommendations/index')">
            <text class="view-all">查看全部</text>
            <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
              <path d="M9 18l6-6-6-6" stroke="#5AC8FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        
        <view class="recommendation-info">
          <text class="recommendation-subtitle">根据您的历史参与数据，我们为您推荐以下活动</text>
        </view>
        
        <scroll-view class="recommendation-scroll" scroll-x="true" show-scrollbar="false">
          <view class="recommendation-list">
            <view 
              v-for="(activity, index) in recommendedActivities" 
              :key="activity.id"
              class="recommendation-item"
              @click="viewActivityDetail(activity)"
              :style="{
                background: getRecommendationBackground(activity.type)
              }"
            >
              <image class="recommendation-image" :src="activity.coverImage" mode="aspectFill"></image>
              <view class="recommendation-content">
                <view class="recommendation-tag" :style="{
                  background: getRecommendationTagBackground(activity.type)
                }">
                  <text>{{ getActivityTypeText(activity.type) }}</text>
                </view>
                <text class="recommendation-title">{{ activity.title }}</text>
                <text class="recommendation-shop">{{ activity.shopName }}</text>
                <view class="recommendation-price-row">
                  <text class="current-price">¥{{ activity.currentPrice }}</text>
                  <text class="original-price" v-if="activity.originalPrice">¥{{ activity.originalPrice }}</text>
                </view>
                <view class="recommendation-stats">
                  <text class="match-rate">{{ activity.matchRate }}%匹配</text>
                  <text class="participation-count">{{ activity.participationCount }}人参与</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="recommendedActivities.length === 0">
          <image class="empty-image" src="/static/images/empty/empty-recommendation.png"></image>
          <text class="empty-text">暂无推荐活动</text>
          <view class="action-btn" @click="navigateTo('/subPackages/activity-showcase/pages/index/index')" :style="{
            background: 'linear-gradient(135deg, #5AC8FA 0%, #64D2FF 100%)',
            borderRadius: '35px',
            boxShadow: '0 5px 15px rgba(90,200,250,0.3)'
          }">
            <text>去浏览更多活动</text>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <view class="tabbar">
          <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'home'}" 
        @click="switchTabBar('home')"
        data-tab="home"
      >
        <view class="tab-icon home"></view>
        <text class="tabbar-text">首页</text>
          </view>
          <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'discover'}" 
        @click="switchTabBar('discover')"
        data-tab="discover"
      >
        <view class="tab-icon discover"></view>
        <text class="tabbar-text">本地商城</text>
              </view>
          <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'distribution'}" 
        @click="switchTabBar('distribution')"
        data-tab="distribution"
      >
        <view class="tab-icon distribution"></view>
        <text class="tabbar-text">分销中心</text>
              </view>
          <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'message'}" 
        @click="switchTabBar('message')"
        data-tab="message"
      >
        <view class="tab-icon message">
          <view class="badge" v-if="unreadMessageCount > 0">{{ unreadMessageCount > 99 ? '99+' : unreadMessageCount }}</view>
          </view>
        <text class="tabbar-text">消息</text>
        </view>
          <view 
        class="tabbar-item" 
        :class="{active: currentTabBar === 'my'}" 
        @click="switchTabBar('my')"
        data-tab="my"
      >
        <view class="tab-icon user"></view>
        <text class="tabbar-text">我的</text>
              </view>
            </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import ActivityStatusCard from '../../components/activity/ActivityStatusCard.vue';
import { onLoad, onShow } from '@dcloudio/uni-app';

// 下拉刷新状态
const isRefreshing = ref(false);

// 当前选中的标签页
const currentTab = ref(0);
const currentActivityType = ref(0);
const currentCouponStatus = ref(0);
const currentTabBar = ref('my'); // 当前底部导航选中项

// 未读消息数量
const unreadMessageCount = ref(3);

// 用户信息数据
const userInfo = ref({
  nickname: '张三',
  avatar: 'https://via.placeholder.com/120',
  id: '10086',
  level: 5,
  isVip: true,
  points: 1280,
  favoriteCount: 16,
  historyCount: 42,
  couponCount: 8,
  orderCount: 12
});

// 标签页数据
const tabs = ref([
  { name: '进行中', status: 'ongoing', emptyText: '暂无进行中的活动', actionText: '去参与活动', emptyImage: '/static/images/empty/empty-ongoing.png' },
  { name: '已报名', status: 'registered', emptyText: '暂无已报名的活动', actionText: '去浏览活动', emptyImage: '/static/images/empty/empty-registered.png' },
  { name: '已完成', status: 'completed', emptyText: '暂无已完成的活动', actionText: '去参与活动', emptyImage: '/static/images/empty/empty-completed.png' },
  { name: '已收藏', status: 'favorite', emptyText: '暂无收藏的活动', actionText: '去浏览活动', emptyImage: '/static/images/empty/empty-favorite.png' }
]);

// 活动类型标签
const activityTypes = ref([
  { name: '全部', type: 'all', emptyText: '暂无活动参与记录', emptyImage: '/static/images/empty/empty-activity-all.png' },
  { name: '拼团', type: 'group', emptyText: '暂无拼团活动参与记录', emptyImage: '/static/images/empty/empty-activity-group.png' },
  { name: '秒杀', type: 'flash', emptyText: '暂无秒杀活动参与记录', emptyImage: '/static/images/empty/empty-activity-flash.png' },
  { name: '优惠券', type: 'coupon', emptyText: '暂无优惠券活动参与记录', emptyImage: '/static/images/empty/empty-activity-coupon.png' }
]);

// 优惠券状态标签
const couponStatuses = ref([
  { name: '全部', status: 'all', emptyText: '暂无优惠券', emptyImage: '/static/images/empty/empty-coupon-all.png' },
  { name: '未使用', status: 'unused', emptyText: '暂无未使用优惠券', emptyImage: '/static/images/empty/empty-coupon-unused.png' },
  { name: '已使用', status: 'used', emptyText: '暂无已使用优惠券', emptyImage: '/static/images/empty/empty-coupon-used.png' },
  { name: '已过期', status: 'expired', emptyText: '暂无已过期优惠券', emptyImage: '/static/images/empty/empty-coupon-expired.png' }
]);

// 订单统计数据
const orderCounts = ref({
  pendingPayment: 3,
  pendingDelivery: 2,
  pendingReceipt: 1,
  pendingReview: 5,
  afterSale: 0
});

// 活动列表数据
const activities = ref([]);

// 活动日历数据
const activityEvents = ref([
  {
    id: 1,
    title: '限时秒杀：iPhone 14 Pro',
    startTime: new Date().getTime(),
    location: '线上活动',
    type: 'flash'
  },
  {
    id: 2,
    title: '3人团：小米空气净化器',
    startTime: new Date().getTime() + 2 * 60 * 60 * 1000, // 2小时后
    location: '线上活动',
    type: 'group'
  },
  {
    id: 3,
    title: '满300减50全场优惠',
    startTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, // 明天
    location: '线上活动',
    type: 'discount'
  }
]);

// 轮播图高度
const swiperHeight = ref(600);

// 返回主"我的"页面
const backToMainMyPage = () => {
  uni.navigateBack();
};

// 显示活动筛选
const showActivityFilter = () => {
  uni.showToast({
    title: '筛选功能开发中',
    icon: 'none'
  });
};

// 切换标签页
const switchTab = (index) => {
  currentTab.value = index;
  updateSwiperHeight(index);
};

// 轮播图变化
const onSwiperChange = (e) => {
  currentTab.value = e.detail.current;
  updateSwiperHeight(e.detail.current);
};

// 切换活动类型
const switchActivityType = (index) => {
  currentActivityType.value = index;
  updateSwiperHeight(currentTab.value);
};

// 更新轮播图高度
const updateSwiperHeight = (tabIndex = 0) => {
  setTimeout(() => {
    const query = uni.createSelectorQuery();
    query.select(`#activity-list-${tabIndex}`).boundingClientRect(data => {
      if (data && data.height > 0) {
        swiperHeight.value = Math.max(600, data.height + 100); // 设置最小高度为600px，添加额外空间
      } else {
        // 如果没有获取到高度，设置一个默认高度
        const filteredActivities = getFilteredActivities(
          tabs.value[tabIndex].status, 
          activityTypes.value[currentActivityType.value].type
        );
        
        // 根据活动数量设置高度或显示空状态的高度
        if (filteredActivities.length > 0) {
          swiperHeight.value = filteredActivities.length * 400 + 100; // 每个卡片大约400px高
        } else {
          swiperHeight.value = 600; // 空状态的高度
        }
      }
    }).exec();
  }, 300);
};

// 根据状态获取活动列表
const getActivitiesByStatus = (status) => {
  // 模拟数据
  if (status === 'ongoing') {
    return [
      {
        id: 1,
        title: '限时秒杀：iPhone 14 Pro',
        coverImage: 'https://via.placeholder.com/300x200',
        shopName: 'Apple授权专卖店',
        startTime: new Date().getTime() - 2 * 60 * 60 * 1000, // 2小时前
        endTime: new Date().getTime() + 10 * 60 * 60 * 1000, // 10小时后
        currentPrice: 6999,
        originalPrice: 8999,
        type: 'flash',
        status: 'ongoing'
      },
      {
        id: 2,
        title: '3人团：小米空气净化器',
        coverImage: 'https://via.placeholder.com/300x200',
        shopName: '小米官方旗舰店',
        startTime: new Date().getTime() - 5 * 60 * 60 * 1000, // 5小时前
        endTime: new Date().getTime() + 24 * 60 * 60 * 1000, // 24小时后
        currentPrice: 699,
        originalPrice: 999,
        type: 'group',
        status: 'ongoing'
      }
    ];
  } else if (status === 'registered') {
    return [
      {
        id: 3,
        title: '满300减50全场优惠',
        coverImage: 'https://via.placeholder.com/300x200',
        shopName: '京东自营',
        startTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, // 明天
        endTime: new Date().getTime() + 8 * 24 * 60 * 60 * 1000, // 8天后
        currentPrice: 300,
        type: 'discount',
        status: 'registered'
      }
    ];
  } else if (status === 'completed') {
    return [
      {
        id: 4,
        title: '星巴克咖啡买一送一',
        coverImage: 'https://via.placeholder.com/300x200',
        shopName: '星巴克(万达广场店)',
        startTime: new Date().getTime() - 10 * 24 * 60 * 60 * 1000, // 10天前
        endTime: new Date().getTime() - 3 * 24 * 60 * 60 * 1000, // 3天前
        currentPrice: 30,
        type: 'coupon',
        status: 'completed'
      }
    ];
  } else if (status === 'favorite') {
    return [
      {
        id: 5,
        title: '华为P50 Pro限时特惠',
        coverImage: 'https://via.placeholder.com/300x200',
        shopName: '华为授权体验店',
        startTime: new Date().getTime() - 1 * 24 * 60 * 60 * 1000, // 1天前
        endTime: new Date().getTime() + 6 * 24 * 60 * 60 * 1000, // 6天后
        currentPrice: 5299,
        originalPrice: 6488,
        type: 'flash',
        status: 'favorite'
      }
    ];
  }
  
  return [];
};

// 获取活动记录列表
const getActivityRecordsByType = (type) => {
  if (type === 'all') {
    return [
      { id: 1, title: '限时秒杀：iPhone 14 Pro', coverImage: 'https://via.placeholder.com/300x200', shopName: 'Apple授权专卖店', startTime: new Date().getTime() - 2 * 60 * 60 * 1000, endTime: new Date().getTime() + 10 * 60 * 60 * 1000, status: 'ongoing' },
      { id: 2, title: '3人团：小米空气净化器', coverImage: 'https://via.placeholder.com/300x200', shopName: '小米官方旗舰店', startTime: new Date().getTime() - 5 * 60 * 60 * 1000, endTime: new Date().getTime() + 24 * 60 * 60 * 1000, status: 'ongoing' },
      { id: 3, title: '满300减50全场优惠', coverImage: 'https://via.placeholder.com/300x200', shopName: '京东自营', startTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, endTime: new Date().getTime() + 8 * 24 * 60 * 60 * 1000, status: 'registered' }
    ];
  } else if (type === 'group') {
    return [
      { id: 1, title: '3人团：小米空气净化器', coverImage: 'https://via.placeholder.com/300x200', shopName: '小米官方旗舰店', startTime: new Date().getTime() - 2 * 60 * 60 * 1000, endTime: new Date().getTime() + 24 * 60 * 60 * 1000, status: 'ongoing' }
    ];
  } else if (type === 'flash') {
    return [
      { id: 1, title: '限时秒杀：iPhone 14 Pro', coverImage: 'https://via.placeholder.com/300x200', shopName: 'Apple授权专卖店', startTime: new Date().getTime() - 2 * 60 * 60 * 1000, endTime: new Date().getTime() + 10 * 60 * 60 * 1000, status: 'ongoing' }
    ];
  } else if (type === 'coupon') {
    return [
      { id: 1, title: '满300减50全场优惠', coverImage: 'https://via.placeholder.com/300x200', shopName: '京东自营', startTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, endTime: new Date().getTime() + 8 * 24 * 60 * 60 * 1000, status: 'registered' }
    ];
  }
  
  return [];
};

// 获取优惠券列表
const getCouponsByStatus = (status) => {
  if (status === 'all') {
    return [
      { id: 1, name: '满300减50全场优惠券', value: 50, condition: '满300元可用', validityPeriod: '2023-12-31', shopName: '京东自营', status: 'unused' },
      { id: 2, name: '星巴克咖啡买一送一优惠券', value: 30, condition: '满30元可用', validityPeriod: '2023-11-30', shopName: '星巴克(万达广场店)', status: 'unused' },
      { id: 3, name: '满100减10优惠券', value: 10, condition: '满100元可用', validityPeriod: '2023-12-15', shopName: 'Apple授权专卖店', status: 'used' }
    ];
  } else if (status === 'unused') {
    return [
      { id: 1, name: '满300减50全场优惠券', value: 50, condition: '满300元可用', validityPeriod: '2023-12-31', shopName: '京东自营', status: 'unused' },
      { id: 2, name: '星巴克咖啡买一送一优惠券', value: 30, condition: '满30元可用', validityPeriod: '2023-11-30', shopName: '星巴克(万达广场店)', status: 'unused' }
    ];
  } else if (status === 'used') {
    return [
      { id: 1, name: '满100减10优惠券', value: 10, condition: '满100元可用', validityPeriod: '2023-12-15', shopName: 'Apple授权专卖店', status: 'used' }
    ];
  } else if (status === 'expired') {
    return [
      { id: 1, name: '满50减5优惠券', value: 5, condition: '满50元可用', validityPeriod: '2023-11-20', shopName: '小米官方旗舰店', status: 'expired' }
    ];
  }
  
  return [];
};

// 获取同时按状态和类型筛选的活动列表
const getFilteredActivities = (status, type) => {
  // 先按状态筛选
  const statusFiltered = getActivitiesByStatus(status);
  
  // 如果类型是"全部"，直接返回按状态筛选的结果
  if (type === 'all') {
    return statusFiltered;
  }
  
  // 再按类型筛选
  return statusFiltered.filter(activity => activity.type === type);
};

// 查看活动详情
const viewActivityDetail = (activity) => {
  let url = '';
  
  switch(activity.type) {
    case 'flash':
      url = `/subPackages/activity-showcase/pages/flash-sale/detail?id=${activity.id}`;
      break;
    case 'group':
      url = `/subPackages/activity-showcase/pages/group-buy/detail?id=${activity.id}`;
      break;
    case 'discount':
      url = `/subPackages/activity-showcase/pages/discount/detail?id=${activity.id}`;
      break;
    case 'coupon':
      url = `/subPackages/activity-showcase/pages/coupon/detail?id=${activity.id}`;
      break;
    default:
      url = `/subPackages/activity-showcase/pages/detail/index?id=${activity.id}&type=${activity.type}`;
  }
  
  navigateTo(url);
};

// 分享活动
const shareActivity = (activity) => {
  uni.showShareMenu({
    withShareTicket: true,
    success() {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
    }
  });
};

// 取消活动
const cancelActivity = (activity) => {
  uni.showModal({
    title: '取消活动',
    content: '确定要取消该活动吗？',
    success: (res) => {
      if (res.confirm) {
        // 模拟取消活动
        uni.showToast({
          title: '已取消活动',
          icon: 'success'
        });
      }
    }
  });
};

// 查看日期活动
const viewDateActivities = (date) => {
  uni.showToast({
    title: `查看${date}活动`,
    icon: 'none'
  });
};

// 切换优惠券状态
const switchCouponStatus = (index) => {
  currentCouponStatus.value = index;
};

// 使用优惠券
const useCoupon = (coupon) => {
  uni.showToast({
    title: `已使用优惠券: ${coupon.name}`,
    icon: 'success'
  });
};

// 查看优惠券详情
const viewCouponDetail = (coupon) => {
  uni.showToast({
    title: `查看优惠券: ${coupon.name} 详情`,
    icon: 'none'
  });
};

// 格式化活动时间
const formatActivityTime = (startTime, endTime) => {
  const startDate = new Date(startTime);
  const endDate = new Date(endTime);
  const startMonth = startDate.getMonth() + 1;
  const startDay = startDate.getDate();
  const endMonth = endDate.getMonth() + 1;
  const endDay = endDate.getDate();
  return `${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`;
};

// 获取活动状态颜色
const getActivityStatusColor = (status) => {
  switch (status) {
    case 'ongoing':
      return '#FF3B69'; // 进行中
    case 'registered':
      return '#FF9500'; // 已报名
    case 'completed':
      return '#34C759'; // 已完成
    case 'favorite':
      return '#FF3B69'; // 已收藏
    default:
      return '#8E8E93'; // 默认
  }
};

// 获取活动状态背景色
const getActivityStatusBgColor = (status) => {
  switch (status) {
    case 'ongoing':
      return 'rgba(255,59,105,0.1)'; // 进行中
    case 'registered':
      return 'rgba(255,149,0,0.1)'; // 已报名
    case 'completed':
      return 'rgba(52,199,89,0.1)'; // 已完成
    case 'favorite':
      return 'rgba(255,59,105,0.1)'; // 已收藏
    default:
      return 'rgba(142,142,147,0.1)'; // 默认
  }
};

// 获取活动状态文本
const getActivityStatusText = (status) => {
  switch (status) {
    case 'ongoing':
      return '进行中';
    case 'registered':
      return '已报名';
    case 'completed':
      return '已完成';
    case 'favorite':
      return '已收藏';
    default:
      return '未知';
  }
};

// 获取优惠券背景色
const getCouponBackground = (status) => {
  switch (status) {
    case 'unused':
      return 'linear-gradient(135deg, #FF8000 0%, #FFA500 80%, #FFBF00 100%)'; // 未使用，更深的橙色
    case 'used':
      return 'linear-gradient(135deg, #2EB84D 0%, #34C759 80%, #30D158 100%)'; // 已使用，更深的绿色
    case 'expired':
      return 'linear-gradient(135deg, #636366 0%, #8E8E93 80%, #AEAEB2 100%)'; // 已过期，更深的灰色
    default:
      return 'linear-gradient(135deg, #FF8000 0%, #FFA500 80%, #FFBF00 100%)'; // 默认
  }
};

// 页面导航
const navigateTo = (url) => {
  uni.navigateTo({ url });
};

// 导航到平台主设置页面
const navigateToMainSettings = () => {
  uni.navigateTo({
    url: '/subPackages/activity-showcase/pages/settings/index'
  });
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  
  // 模拟加载数据
  setTimeout(() => {
    // 加载数据...
    isRefreshing.value = false;
    
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    });
  }, 1000);
};

// 加载更多
const loadMore = () => {
  // 加载更多数据...
  uni.showToast({
    title: '已加载全部数据',
    icon: 'none'
  });
};

// 页面加载
onMounted(() => {
  // 初始化数据
  activities.value = getActivitiesByStatus(tabs.value[currentTab.value].status);
  
  // 确保组件渲染完成后再计算高度
  setTimeout(() => {
    updateSwiperHeight(currentTab.value);
  }, 500);
});

onShow(() => {
  // 每次页面显示时更新数据
  activities.value = getActivitiesByStatus(tabs.value[currentTab.value].status);
  
  // 确保组件渲染完成后再计算高度
  setTimeout(() => {
    updateSwiperHeight(currentTab.value);
  }, 500);
});

// 切换底部导航栏
const switchTabBar = (tab) => {
  if (currentTabBar.value === tab) return;
      
  currentTabBar.value = tab;
      
  // 根据选中的标签页进行相应的导航
  switch(tab) {
    case 'home':
      uni.reLaunch({
        url: '/subPackages/activity-showcase/pages/index/index',
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
    case 'discover':
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/discover/index',
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
    case 'distribution':
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/distribution/index',
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
    case 'message':
      uni.navigateTo({
        url: '/subPackages/activity-showcase/pages/message/index',
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      break;
    case 'my':
      // 已在我的页面，不需要导航
      break;
  }
};

// 推荐活动数据
const recommendedActivities = ref([
  {
    id: 101,
    title: '3人拼团：戴森吹风机',
    coverImage: 'https://via.placeholder.com/300x200',
    shopName: '戴森官方旗舰店',
    startTime: new Date().getTime(),
    endTime: new Date().getTime() + 3 * 24 * 60 * 60 * 1000, // 3天后
    currentPrice: 1999,
    originalPrice: 2999,
    type: 'group',
    matchRate: 95,
    participationCount: 1283
  },
  {
    id: 102,
    title: '限时秒杀：小米手环8',
    coverImage: 'https://via.placeholder.com/300x200',
    shopName: '小米官方旗舰店',
    startTime: new Date().getTime(),
    endTime: new Date().getTime() + 1 * 24 * 60 * 60 * 1000, // 1天后
    currentPrice: 199,
    originalPrice: 299,
    type: 'flash',
    matchRate: 87,
    participationCount: 3521
  },
  {
    id: 103,
    title: '满200减50：星巴克咖啡',
    coverImage: 'https://via.placeholder.com/300x200',
    shopName: '星巴克(万达广场店)',
    startTime: new Date().getTime(),
    endTime: new Date().getTime() + 7 * 24 * 60 * 60 * 1000, // 7天后
    currentPrice: 200,
    type: 'discount',
    matchRate: 82,
    participationCount: 872
  }
]);

// 获取活动类型文本
const getActivityTypeText = (type) => {
  switch (type) {
    case 'flash':
      return '秒杀';
    case 'group':
      return '拼团';
    case 'discount':
      return '优惠';
    case 'coupon':
      return '券';
    default:
      return '活动';
  }
};

// 获取推荐背景色
const getRecommendationBackground = (type) => {
  switch (type) {
    case 'flash':
      return 'linear-gradient(to bottom right, #FFFFFF, #FFF5F5)';
    case 'group':
      return 'linear-gradient(to bottom right, #FFFFFF, #F5F5FF)';
    case 'discount':
      return 'linear-gradient(to bottom right, #FFFFFF, #F5FFFA)';
    case 'coupon':
      return 'linear-gradient(to bottom right, #FFFFFF, #FFFAF5)';
    default:
      return 'linear-gradient(to bottom right, #FFFFFF, #F8F8F8)';
  }
};

// 获取推荐标签背景色
const getRecommendationTagBackground = (type) => {
  switch (type) {
    case 'flash':
      return 'linear-gradient(135deg, #FF3B30 0%, #FF6E6E 100%)';
    case 'group':
      return 'linear-gradient(135deg, #5856D6 0%, #7A7AFF 100%)';
    case 'discount':
      return 'linear-gradient(135deg, #34C759 0%, #5EE077 100%)';
    case 'coupon':
      return 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)';
    default:
      return 'linear-gradient(135deg, #5AC8FA 0%, #64D2FF 100%)';
  }
};
</script>

<style scoped>
.activity-my-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(255,59,105,0.15);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
  }
  
  .back-btn, .filter-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}
    
.back-btn .icon, .filter-btn .icon {
      width: 48rpx;
      height: 48rpx;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  height: calc(100vh - var(--status-bar-height, 25px) - 62px);
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  padding: 30rpx;
  box-sizing: border-box;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx; /* 底部安全区域高度，包括导航栏高度 */
  padding-bottom: env(safe-area-inset-bottom);
}

/* 标签页容器 */
.tabs-container {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-radius: 35px 35px 0 0;
}
  
  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding: 10rpx 0;
}
    
    .tab-text {
      font-size: 28rpx;
      color: #8E8E93;
      transition: color 0.3s ease;
    }
    
    .tab-indicator {
      position: absolute;
      bottom: -10rpx;
      width: 40rpx;
      height: 3px;
      border-radius: 1.5px;
      transition: all 0.3s ease;
    }
    
.tab-item.active .tab-text {
        color: #FF3B69;
        font-weight: 500;
}

/* 活动容器 */
.activities-container {
  width: 100%;
  position: relative;
  margin-bottom: 30rpx;
}

/* 活动轮播区域 */
.activities-swiper {
  width: 100%;
  margin-bottom: 30rpx;
  height: 600px; /* 默认高度 */
}
  
  .tab-content {
    height: 100%;
    overflow: visible;
    padding-bottom: 20rpx;
}
    
    .activity-list {
      padding: 10rpx 0;
      display: flex;
      flex-direction: column;
      width: 100%;
}

/* 活动状态卡片 */
.activity-status-card {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.header-right {
  display: flex;
  align-items: center;
}

.view-all {
  font-size: 28rpx;
  color: #FF3B69;
  margin-right: 5rpx;
}

.icon {
  width: 16rpx;
  height: 16rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #8E8E93;
    margin-bottom: 30rpx;
  }
  
  .action-btn {
    padding: 16rpx 40rpx;
    border-radius: 35px;
    color: #FFFFFF;
    font-size: 28rpx;
}
    
.action-btn:active {
      opacity: 0.9;
      transform: scale(0.98);
    }

/* 订单状态网格 */
.order-status-grid {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.status-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
    margin-bottom: 20rpx;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
      display: flex;
      align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.status-name {
  font-size: 24rpx;
        color: #333333;
}

.status-badge {
  position: absolute;
  top: -10rpx;
  right: 10rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
        display: flex;
        justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
}

/* 优惠券列表 */
.coupon-list {
  margin-top: 20rpx;
}

/* 主标签容器 */
.main-tabs-container {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #F2F2F7;
}

.main-tab-item {
  flex: 1;
        display: flex;
  flex-direction: column;
        align-items: center;
  position: relative;
  padding: 15rpx 0;
}

.main-tab-item .tab-text {
  font-size: 28rpx;
  color: #8E8E93;
  transition: color 0.3s ease;
}

.main-tab-item.active .tab-text {
  color: #FF3B69;
  font-weight: 600;
}

/* 子标签容器 */
.sub-tabs-container {
    display: flex;
  justify-content: flex-start;
    margin-bottom: 20rpx;
  overflow-x: auto;
  white-space: nowrap;
}

.sub-tab-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
      position: relative;
  padding: 10rpx 30rpx;
}

.sub-tab-item .tab-text {
  font-size: 26rpx;
  color: #8E8E93;
  transition: color 0.3s ease;
}

.sub-tab-item.active .tab-text {
  color: #FF3B69;
  font-weight: 500;
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
        bottom: 0;
  left: 0;
        right: 0;
  height: 100rpx;
  background: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
        display: flex;
  justify-content: space-around;
        align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 99;
  border-top: 1rpx solid #EEEEEE;
}
  
.tabbar-item {
  flex: 1;
      display: flex;
      flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}
    
.tabbar-item:active {
  transform: scale(0.9);
}
    
.tabbar-item.active .tab-icon {
  transform: translateY(-5rpx);
}
      
.tabbar-item.active .tabbar-text {
  color: #FF3B69;
  font-weight: 600;
  transform: translateY(-2rpx);
}
    
.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
    display: flex;
  justify-content: center;
      align-items: center;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 首页图标 */
.tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="home"] .tab-icon.home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
}

/* 发现图标 */
.tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="discover"] .tab-icon.discover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5.5-2.5l7.51-3.49L17.5 6.5 9.99 9.99 6.5 17.5zm5.5-6.6c.61 0 1.1.49 1.1 1.1s-.49 1.1-1.1 1.1-1.1-.49-1.1-1.1.49-1.1 1.1-1.1z'/%3E%3C/svg%3E");
}

/* 消息图标 */
.tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="message"] .tab-icon.message {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 分销图标 */
.tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="distribution"] .tab-icon.distribution {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20h14c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2z'/%3E%3C/svg%3E");
}

/* 我的图标 */
.tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active[data-tab="my"] .tab-icon.user {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF3B69'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.tabbar-item.active .tab-icon {
  filter: drop-shadow(0 1px 2px rgba(255, 59, 105, 0.3));
}
      
.badge {
  position: absolute;
  top: -8rpx;
  right: -12rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #FF453A, #FF2D55);
  color: #FFFFFF;
  font-size: 18rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  font-weight: 600;
  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transform: scale(0.9);
}
    
.tabbar-text {
  font-size: 22rpx;
      color: #8E8E93;
  margin-top: 2rpx;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
    
.tabbar-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 30rpx;
  height: 4rpx;
  background: #FF3B69;
  border-radius: 2rpx;
  transition: transform 0.3s ease;
}
    
.tabbar-item.active::after {
  transform: translateX(-50%) scaleX(1);
}

/* 卡片通用样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 工具箱样式 */
.tools-card {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(88,86,214,0.15);
  background: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
  
  .toolbox-header {
    margin-bottom: 20rpx;
}
    
    .toolbox-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
  }
  
  .tool-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 20rpx;
  margin-top: 20rpx;
}
    
    .tool-item {
      display: flex;
      flex-direction: column;
      align-items: center;
  margin-bottom: 10rpx;
}
      
      .tool-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10rpx;
  border-radius: 50%;
      }
      
      .tool-name {
        font-size: 24rpx;
        color: #333333;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 优惠券专区样式 */
.coupon-card {
  border-radius: 35px;
  box-shadow: 0 10px 25px rgba(255,149,0,0.12);
  background: linear-gradient(to bottom, #FFFFFF, #FFFAF2);
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid rgba(255,149,0,0.1);
}

.coupon-status-tabs {
    display: flex;
  justify-content: space-around;
    margin-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(242,242,247,0.8);
  padding-bottom: 5rpx;
}

.coupon-status-tab {
  flex: 1;
      display: flex;
  flex-direction: column;
      align-items: center;
  position: relative;
  padding: 15rpx 0;
}

.coupon-status-tab text {
        font-size: 28rpx;
  color: #8E8E93;
  transition: color 0.3s ease;
}

.coupon-status-tab.active text {
  color: #FF9500;
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: -5rpx;
  width: 40rpx;
  height: 4rpx;
  border-radius: 2rpx;
}

.coupons-list {
    margin-top: 20rpx;
}

.coupon-item {
  position: relative;
  display: flex;
  margin-bottom: 25rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}

.coupon-item:active {
  transform: scale(0.98);
}

.coupon-left {
  width: 200rpx;
  padding: 25rpx 20rpx;
      display: flex;
      flex-direction: column;
  justify-content: center;
      align-items: center;
      position: relative;
  overflow: hidden;
}

.coupon-left::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
  z-index: 1;
}

.coupon-value {
        display: flex;
  align-items: baseline;
  position: relative;
  z-index: 2;
}

.currency {
  font-size: 26rpx;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.amount {
  font-size: 52rpx;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.coupon-condition {
  font-size: 22rpx;
  color: #FFFFFF;
  margin-top: 10rpx;
  position: relative;
  z-index: 2;
  text-shadow: 0 1rpx 3rpx rgba(0,0,0,0.2);
  background-color: rgba(0,0,0,0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
}

.coupon-divider {
        position: absolute;
  left: 200rpx;
  top: 0;
  bottom: 0;
  width: 0;
  border-left: 1px dashed rgba(255,255,255,0.5);
  z-index: 2;
}

.coupon-divider::before, .coupon-divider::after {
  content: '';
  position: absolute;
  left: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #F8F8F8;
  box-shadow: inset 0 0 5rpx rgba(0,0,0,0.05);
  z-index: 2;
}

.coupon-divider::before {
  top: -10rpx;
}

.coupon-divider::after {
  bottom: -10rpx;
}

.coupon-right {
  flex: 1;
  padding: 25rpx;
  display: flex;
  flex-direction: column;
    justify-content: space-between;
  background-color: #FFFFFF;
}

.coupon-name {
  font-size: 28rpx;
      font-weight: 600;
      color: #333333;
  margin-bottom: 10rpx;
}

.coupon-shop {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 15rpx;
}

.coupon-validity {
  font-size: 22rpx;
  color: #999999;
}

.coupon-use-btn {
  align-self: flex-end;
      display: flex;
  justify-content: center;
      align-items: center;
  height: 60rpx;
  width: 160rpx;
  border-radius: 30rpx;
  background: rgba(255,149,0,0.9);
  font-size: 26rpx;
  font-weight: 500;
  color: #FFFFFF;
  border: 1rpx solid rgba(255,149,0,0.3);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.coupon-use-btn:active {
  transform: scale(0.95);
  background: rgba(255,149,0,1);
}

/* 活动日历样式 */
.calendar-card {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(52,199,89,0.15);
  background: #FFFFFF;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.calendar-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 20rpx;
}

.calendar-item {
  width: 31%;
  padding: 15rpx;
  border-radius: 16rpx;
  margin-bottom: 15rpx;
  box-sizing: border-box;
}

.calendar-date {
    display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10rpx;
}

.date-day {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.date-month {
  font-size: 20rpx;
  color: #666666;
}

.calendar-event {
      display: flex;
      flex-direction: column;
      align-items: center;
}

.event-title {
  font-size: 22rpx;
  font-weight: 500;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
}

.event-type {
  font-size: 20rpx;
  color: #666666;
  margin-top: 5rpx;
}

/* 分销中心样式 */
.distribution-card {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(172,57,255,0.15);
  background: linear-gradient(135deg, #FFFFFF 0%, #F9F5FF 100%);
  padding: 30rpx;
  margin-bottom: 30rpx;
  transform: perspective(1000px) rotateX(-2deg);
}

.header-left {
  display: flex;
  align-items: center;
}

.distributor-level {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40rpx;
  padding: 0 15rpx;
  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
  border-radius: 20rpx;
  margin-left: 15rpx;
}

.level-text {
  font-size: 22rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.view-more-btn {
    display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
}

.view-more-btn text {
  font-size: 24rpx;
  color: #FFFFFF;
  margin-right: 5rpx;
}

.distributor-info {
      display: flex;
  align-items: center;
  margin: 20rpx 0;
}

.distributor-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
      overflow: hidden;
      position: relative;
  margin-right: 20rpx;
}

.distributor-avatar image {
  width: 100%;
  height: 100%;
}

.distributor-badge {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #FFFFFF;
}

.distributor-details {
        display: flex;
        flex-direction: column;
}

.distributor-name {
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 5rpx;
        }

.distributor-id {
          font-size: 24rpx;
  color: #666666;
}

.earnings-overview {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}

.earnings-item {
  display: flex;
  flex-direction: column;
    align-items: center;
}

.earnings-value {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
  margin-bottom: 5rpx;
}

.earnings-label {
  font-size: 24rpx;
  color: #666666;
}

.progress-section {
  margin-top: 20rpx;
}

.progress-header {
      display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.progress-title {
  font-size: 26rpx;
  color: #333333;
}

.progress-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #AC39FF;
}

.progress-bar-bg {
  width: 100%;
  height: 10rpx;
  background: rgba(172,57,255,0.2);
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);
  border-radius: 10rpx;
}

.progress-hint {
  font-size: 22rpx;
  color: #666666;
}

/* 用户信息模块 */
.user-profile-card {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(0,0,0,0.08);
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  padding: 30rpx;
  margin-bottom: 30rpx;
      position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.profile-bg-decoration {
        position: absolute;
  top: -50rpx;
  right: -50rpx;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.1);
  z-index: 1;
}

.profile-bg-decoration:last-child {
  position: absolute;
  bottom: -80rpx;
  left: -80rpx;
  width: 250rpx;
  height: 250rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.08);
  z-index: 1;
}

/* 用户基本信息 */
.user-basic-info {
    display: flex;
  align-items: center;
      position: relative;
  z-index: 2;
}

.avatar-container {
        position: relative;
  margin-right: 20rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255,255,255,0.8);
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.1);
}

.vip-badge {
          position: absolute;
          bottom: 0;
  right: 0;
  background: #FFD700;
  color: #8B4513;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  transform: translateY(50%);
  box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.2);
}

.user-info {
  flex: 1;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-right: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.level-tag {
  font-size: 22rpx;
  color: #FF3B69;
  background: rgba(255,255,255,0.9);
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  font-weight: 500;
}

.user-id {
          font-size: 24rpx;
  color: rgba(255,255,255,0.8);
  margin-bottom: 10rpx;
  display: block;
}

.user-points {
        display: flex;
  align-items: center;
}

.points-label {
          font-size: 24rpx;
  color: rgba(255,255,255,0.9);
        }

.points-value {
  font-size: 28rpx;
  font-weight: 600;
          color: #FFFFFF;
  margin-left: 8rpx;
}

.profile-action {
  padding: 10rpx 30rpx;
  background: rgba(255,255,255,0.2);
          border-radius: 30rpx;
  font-size: 26rpx;
  color: #FFFFFF;
  font-weight: 500;
  border: 1rpx solid rgba(255,255,255,0.3);
}

/* 用户数据统计 */
.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  position: relative;
  z-index: 2;
  background: rgba(255,255,255,0.1);
  border-radius: 20rpx;
  padding: 20rpx 0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
    color: #FFFFFF;
  display: block;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255,255,255,0.8);
  display: block;
}

/* 推荐活动模块样式 */
.recommended-card {
  border-radius: 35px;
  box-shadow: 0 8px 20px rgba(90,200,250,0.15);
  background: linear-gradient(to bottom, #FFFFFF, #F0F8FF);
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid rgba(90,200,250,0.1);
}

.recommendation-info {
  margin-bottom: 20rpx;
}

.recommendation-subtitle {
  font-size: 26rpx;
  color: #666666;
}

.recommendation-scroll {
  width: 100%;
  white-space: nowrap;
}

.recommendation-list {
  display: flex;
  padding: 10rpx 0;
}

.recommendation-item {
  display: inline-flex;
  flex-direction: column;
  width: 300rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}

.recommendation-item:active {
  transform: scale(0.98);
}

.recommendation-image {
  width: 100%;
  height: 180rpx;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}

.recommendation-content {
  padding: 15rpx;
  position: relative;
}

.recommendation-tag {
  position: absolute;
  top: -15rpx;
  left: 15rpx;
  padding: 5rpx 15rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 500;
  box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);
}

.recommendation-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
  white-space: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 72rpx;
}

.recommendation-shop {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recommendation-price-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.current-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF3B69;
  margin-right: 10rpx;
}

.original-price {
  font-size: 22rpx;
  color: #999999;
  text-decoration: line-through;
}

.recommendation-stats {
  display: flex;
  justify-content: space-between;
  font-size: 20rpx;
  color: #666666;
}

.match-rate {
  color: #5AC8FA;
  font-weight: 500;
}

.participation-count {
  color: #999999;
}
</style>