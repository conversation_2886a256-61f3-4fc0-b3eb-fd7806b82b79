<template>
	<view class="news-container">
		<!-- 资讯分类 -->
		<scroll-view class="category-scroll" scroll-x>
			<view class="category-list">
				<view 
					class="category-item" 
					:class="{ active: currentCategory === item.id }"
					v-for="item in categories" 
					:key="item.id"
					@click="switchCategory(item.id)"
				>
					{{item.name}}
				</view>
			</view>
		</scroll-view>
		
		<!-- 资讯列表 -->
		<view class="news-list">
			<view class="news-item card-section fade-in" v-for="(item, index) in newsList" :key="index" @click="goToDetail(item.id)">
				<view class="news-content">
					<view class="news-info">
						<text class="news-title">{{item.title}}</text>
						<text class="news-desc">{{item.description}}</text>
						<view class="news-meta">
							<text class="news-time">{{item.time}}</text>
							<text class="news-category">{{item.category}}</text>
						</view>
					</view>
					<image v-if="item.image" :src="item.image" class="news-image" mode="aspectFill"></image>
				</view>
				<view class="news-stats">
					<view class="stat-item">
						<image src="/static/images/tabbar/浏览.png" class="stat-icon"></image>
						<text class="stat-text">{{item.views}}</text>
					</view>
					<view class="stat-item">
						<image src="/static/images/tabbar/点赞.png" class="stat-icon"></image>
						<text class="stat-text">{{item.likes}}</text>
					</view>
					<view class="stat-item">
						<image src="/static/images/tabbar/评论.png" class="stat-icon"></image>
						<text class="stat-text">{{item.comments}}</text>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore && !loading" @click="loadMore">
				加载更多
			</view>
			<view class="loading" v-if="loading">
				加载中...
			</view>
			<view class="no-more" v-if="!hasMore && newsList.length > 0">
				没有更多了
			</view>
			<view class="empty-list" v-if="!loading && newsList.length === 0">
				暂无资讯
			</view>
		</view>
		<fab-buttons />
	</view>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import FabButtons from '@/components/FabButtons.vue';
import mockApi from '@/mock/api';

// 响应式数据
const currentCategory = ref(0);
const categories = ref([]);
const newsList = ref([]);
const loading = ref(false);
const page = ref(1);
const hasMore = ref(true);

// 方法
const switchCategory = (categoryId) => {
	if (currentCategory.value === categoryId) return;
	
	currentCategory.value = categoryId;
	page.value = 1;
	newsList.value = [];
	hasMore.value = true;
	fetchNewsList();
};

const goToDetail = (id) => {
	uni.navigateTo({
		url: `/subPackages/news/pages/detail?id=${id}`
	});
};

const loadMore = () => {
	if (loading.value || !hasMore.value) return;
	
	page.value++;
	fetchNewsList();
};

const fetchNewsList = async () => {
	loading.value = true;
	
	try {
		const result = await mockApi.news.getList(currentCategory.value, page.value);
		
		if (page.value === 1) {
			newsList.value = result.list;
		} else {
			newsList.value = [...newsList.value, ...result.list];
		}
		
		hasMore.value = result.hasMore;
	} catch (error) {
		console.error('获取新闻列表失败:', error);
		uni.showToast({
			title: '获取资讯失败',
			icon: 'none'
		});
	} finally {
		loading.value = false;
	}
};

const fetchCategories = async () => {
	try {
		const result = await mockApi.news.getCategories();
		categories.value = result;
	} catch (error) {
		console.error('获取新闻分类失败:', error);
	}
};

// 监听分类变化
watch(currentCategory, () => {
	// 滚动到顶部
	uni.pageScrollTo({
		scrollTop: 0,
		duration: 300
	});
});

// 生命周期钩子
onMounted(() => {
	fetchCategories();
	fetchNewsList();
});
</script>

<style>
	.news-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}
	
	.category-scroll {
		background-color: #fff;
		padding: 20rpx 0;
		position: sticky;
		top: 0;
		z-index: 10;
	}
	
	.category-list {
		display: flex;
		padding: 0 20rpx;
		white-space: nowrap;
	}
	
	.category-item {
		display: inline-block;
		padding: 12rpx 30rpx;
		margin-right: 20rpx;
		font-size: 28rpx;
		color: #666;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		transition: all 0.3s;
	}
	
	.category-item.active {
		color: #fff;
		background: linear-gradient(to right, #0052CC, #2196F3);
	}
	
	.news-list {
		padding: 20rpx;
	}
	
	.news-item {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.news-content {
		display: flex;
		margin-bottom: 16rpx;
	}
	
	.news-info {
		flex: 1;
		margin-right: 20rpx;
	}
	
	.news-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 12rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}
	
	.news-desc {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 12rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}
	
	.news-image {
		width: 200rpx;
		height: 150rpx;
		border-radius: 8rpx;
	}
	
	.news-meta {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #999;
	}
	
	.news-time {
		margin-right: 20rpx;
	}
	
	.news-category {
		color: #0052CC;
	}
	
	.news-stats {
		display: flex;
		border-top: 1rpx solid #eee;
		padding-top: 16rpx;
	}
	
	.stat-item {
		display: flex;
		align-items: center;
		margin-right: 40rpx;
	}
	
	.stat-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
	}
	
	.stat-text {
		font-size: 24rpx;
		color: #999;
	}
	
	/* 继承首页的卡片和动画样式 */
	.card-section {
		background-color: #ffffff;
		margin-bottom: 30rpx;
		padding: 26rpx 22rpx;
		border-radius: 16rpx;
		box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.05);
	}
	
	.fade-in {
		animation: fadeIn 0.5s ease;
	}
	
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	/* 加载更多样式 */
	.load-more, .no-more, .loading, .empty-list {
		text-align: center;
		padding: 30rpx 0;
		font-size: 28rpx;
		color: #999;
	}
	
	.load-more {
		color: #0052CC;
	}
</style>