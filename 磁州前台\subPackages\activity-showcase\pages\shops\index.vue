<template>
  <view class="shops-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">商铺列表</view>
        <view class="navbar-right">
          <view class="search-btn" @click="navigateTo('/subPackages/activity-showcase/pages/search/index?type=shops')">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <circle cx="11" cy="11" r="8" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
              <path d="M21 21l-4.35-4.35" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选区域 -->
    <view class="search-filter-bar">
      <view class="search-box" @click="navigateTo('/subPackages/activity-showcase/pages/search/index?type=shops')">
        <svg class="search-icon" viewBox="0 0 24 24" width="20" height="20">
          <circle cx="11" cy="11" r="8" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
          <path d="M21 21l-4.35-4.35" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <text class="search-placeholder">搜索商铺名称、类别</text>
      </view>
      
      <view class="filter-options">
        <view 
          class="filter-option"
          :class="{ active: currentFilter === 'distance' }"
          @click="setFilter('distance')"
        >
          <text>距离</text>
          <svg class="sort-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M7 10l5 5 5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        
        <view 
          class="filter-option"
          :class="{ active: currentFilter === 'rating' }"
          @click="setFilter('rating')"
        >
          <text>评分</text>
          <svg class="sort-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M7 10l5 5 5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        
        <view 
          class="filter-option"
          :class="{ active: currentFilter === 'sales' }"
          @click="setFilter('sales')"
        >
          <text>销量</text>
          <svg class="sort-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M7 10l5 5 5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        
        <view 
          class="filter-option"
          @click="showFilter"
        >
          <text>筛选</text>
          <svg class="filter-icon" viewBox="0 0 24 24" width="16" height="16">
            <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
    </view>

    <!-- 商铺分类标签栏 -->
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view 
          v-for="(category, index) in shopCategories" 
          :key="index"
          class="category-item"
          :class="{ active: currentCategory === index }"
          @click="switchCategory(index)"
        >
          <text>{{ category.name }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 商铺列表区域 -->
    <scroll-view 
      class="shops-scroll" 
      scroll-y 
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view class="shops-list">
        <view 
          v-for="shop in getFilteredShops()" 
          :key="shop.id"
          class="shop-card"
          @click="viewShopDetail(shop)"
        >
          <!-- 商铺Logo -->
          <image :src="shop.logo" class="shop-logo" mode="aspectFill"></image>
          
          <!-- 商铺信息 -->
          <view class="shop-info">
            <view class="shop-header">
              <text class="shop-name">{{ shop.name }}</text>
              <view class="shop-rating">
                <text class="rating-value">{{ shop.rating }}</text>
                <svg class="star-icon" viewBox="0 0 24 24" width="16" height="16">
                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" fill="#FF9500" stroke="#FF9500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polygon>
                </svg>
              </view>
            </view>
            
            <view class="shop-meta">
              <view class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                  <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  <path d="M9 22V12h6v10" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                <text class="meta-text">{{ shop.category }}</text>
              </view>
              
              <view class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  <circle cx="12" cy="10" r="3" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                </svg>
                <text class="meta-text">{{ shop.distance }}</text>
              </view>
              
              <view class="meta-item">
                <svg class="meta-icon" viewBox="0 0 24 24" width="16" height="16">
                  <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  <circle cx="9" cy="7" r="4" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                  <path d="M23 21v-2a4 4 0 00-3-3.87m-4-12a4 4 0 010 7.75" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                <text class="meta-text">月售{{ shop.monthlySales }}+</text>
              </view>
            </view>
            
            <view class="shop-tags">
              <view 
                v-for="(tag, tagIndex) in shop.tags.slice(0, 3)" 
                :key="tagIndex"
                class="shop-tag"
              >
                {{ tag }}
              </view>
              <view class="more-tag" v-if="shop.tags.length > 3">+{{ shop.tags.length - 3 }}</view>
            </view>
            
            <view class="shop-promotion" v-if="shop.promotion">
              <view class="promotion-tag">{{ shop.promotion.type }}</view>
              <text class="promotion-text">{{ shop.promotion.text }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="isLoadingMore">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="getFilteredShops().length === 0">
        <image class="empty-image" src="/static/images/empty-shops.png"></image>
        <text class="empty-text">暂无相关商铺</text>
        <view class="action-btn" @click="resetFilters" :style="{
          background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',
          borderRadius: '35px',
          boxShadow: '0 5px 15px rgba(255,149,0,0.3)'
        }">
          <text>重置筛选</text>
        </view>
      </view>
    </scroll-view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-popup">
        <view class="filter-header">
          <text class="filter-title">筛选</text>
          <view class="filter-close" @click="closeFilter">
            <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
              <line x1="18" y1="6" x2="6" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
              <line x1="6" y1="6" x2="18" y2="18" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></line>
            </svg>
          </view>
        </view>
        
        <view class="filter-content">
          <!-- 商铺分类筛选 -->
          <view class="filter-section">
            <text class="section-title">商铺分类</text>
            <view class="filter-options">
              <view 
                v-for="(category, index) in allCategories" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedCategories.includes(index) }"
                @click="toggleCategory(index)"
              >
                {{ category.name }}
              </view>
            </view>
          </view>
          
          <!-- 评分筛选 -->
          <view class="filter-section">
            <text class="section-title">商铺评分</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in ratingOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedRating === index }"
                @click="selectRating(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
          
          <!-- 特色服务筛选 -->
          <view class="filter-section">
            <text class="section-title">特色服务</text>
            <view class="filter-options">
              <view 
                v-for="(option, index) in serviceOptions" 
                :key="index"
                class="filter-option"
                :class="{ active: selectedServices.includes(index) }"
                @click="toggleService(index)"
              >
                {{ option.label }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="filter-footer">
          <view class="filter-reset" @click="resetFilters">
            <text>重置</text>
          </view>
          <view class="filter-apply" @click="applyFilter">
            <text>确定</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue';

// 页面状态
const currentCategory = ref(0);
const currentFilter = ref('distance');
const isRefreshing = ref(false);
const isLoadingMore = ref(false);
const shopsList = ref([]);
const filterPopup = ref(null);

// 筛选选项
const selectedCategories = ref([0]);
const selectedRating = ref(0);
const selectedServices = ref([]);

// 商铺分类
const shopCategories = [
  { name: '全部', value: 'all' },
  { name: '餐饮美食', value: 'food' },
  { name: '休闲娱乐', value: 'entertainment' },
  { name: '旅游住宿', value: 'travel' },
  { name: '生活服务', value: 'service' },
  { name: '文化教育', value: 'education' },
  { name: '体育健身', value: 'sports' },
  { name: '亲子活动', value: 'family' }
];

// 所有分类（包含子分类）
const allCategories = [
  { name: '全部', value: 'all' },
  { name: '餐饮美食', value: 'food' },
  { name: '休闲娱乐', value: 'entertainment' },
  { name: '旅游住宿', value: 'travel' },
  { name: '生活服务', value: 'service' },
  { name: '文化教育', value: 'education' },
  { name: '体育健身', value: 'sports' },
  { name: '亲子活动', value: 'family' },
  { name: '中餐', value: 'chinese_food' },
  { name: '西餐', value: 'western_food' },
  { name: '快餐', value: 'fast_food' },
  { name: '咖啡甜品', value: 'cafe' },
  { name: '酒吧', value: 'bar' }
];

// 评分选项
const ratingOptions = [
  { label: '全部', value: 0 },
  { label: '4.5分以上', value: 4.5 },
  { label: '4.0分以上', value: 4.0 },
  { label: '3.5分以上', value: 3.5 }
];

// 特色服务选项
const serviceOptions = [
  { label: '免费WiFi', value: 'wifi' },
  { label: '停车场', value: 'parking' },
  { label: '无障碍设施', value: 'accessibility' },
  { label: '提供发票', value: 'invoice' },
  { label: '接受预订', value: 'reservation' },
  { label: '会员特惠', value: 'member' }
];

// 模拟数据
const mockShops = [
  {
    id: '1001',
    name: '磁州文化体验馆',
    logo: '/static/demo/shop1.jpg',
    category: '文化教育',
    rating: 4.8,
    distance: '1.2km',
    monthlySales: 256,
    tags: ['文化体验', '非遗传承', '亲子互动', '团建活动'],
    promotion: {
      type: '满减',
      text: '满200减30'
    }
  },
  {
    id: '1002',
    name: '磁州美食城',
    logo: '/static/demo/shop2.jpg',
    category: '餐饮美食',
    rating: 4.5,
    distance: '0.8km',
    monthlySales: 512,
    tags: ['特色美食', '团购优惠', '家庭聚餐'],
    promotion: {
      type: '优惠',
      text: '新用户立减15元'
    }
  },
  {
    id: '1003',
    name: '磁州户外拓展中心',
    logo: '/static/demo/shop3.jpg',
    category: '体育健身',
    rating: 4.7,
    distance: '3.5km',
    monthlySales: 128,
    tags: ['户外活动', '团队建设', '亲子互动', '野外生存'],
    promotion: null
  },
  {
    id: '1004',
    name: '磁州亲子乐园',
    logo: '/static/demo/shop4.jpg',
    category: '亲子活动',
    rating: 4.6,
    distance: '2.1km',
    monthlySales: 320,
    tags: ['儿童娱乐', '亲子互动', '教育启蒙'],
    promotion: {
      type: '折扣',
      text: '周末8.5折'
    }
  },
  {
    id: '1005',
    name: '磁州艺术中心',
    logo: '/static/demo/shop5.jpg',
    category: '文化教育',
    rating: 4.9,
    distance: '1.5km',
    monthlySales: 96,
    tags: ['艺术展览', '文化讲座', '艺术培训'],
    promotion: {
      type: '活动',
      text: '新展开幕，门票半价'
    }
  }
];

// 生命周期
onMounted(() => {
  loadShops();
});

// 方法
const loadShops = () => {
  // 模拟加载数据
  shopsList.value = mockShops;
};

const getFilteredShops = () => {
  let result = [...shopsList.value];
  
  // 应用分类筛选
  if (currentCategory.value !== 0) {
    const category = shopCategories[currentCategory.value].name;
    result = result.filter(shop => shop.category === category);
  }
  
  // 应用排序
  switch (currentFilter.value) {
    case 'distance':
      result.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
      break;
    case 'rating':
      result.sort((a, b) => b.rating - a.rating);
      break;
    case 'sales':
      result.sort((a, b) => b.monthlySales - a.monthlySales);
      break;
  }
  
  return result;
};

const switchCategory = (index) => {
  currentCategory.value = index;
};

const setFilter = (filter) => {
  currentFilter.value = filter;
};

const onRefresh = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    loadShops();
    isRefreshing.value = false;
  }, 1000);
};

const loadMore = () => {
  // 模拟加载更多
  if (shopsList.value.length >= 10) return;
  
  isLoadingMore.value = true;
  setTimeout(() => {
    // 模拟添加更多数据
    const newShops = [
      {
        id: '1006',
        name: '磁州咖啡馆',
        logo: '/static/demo/shop6.jpg',
        category: '休闲娱乐',
        rating: 4.4,
        distance: '0.5km',
        monthlySales: 420,
        tags: ['咖啡', '下午茶', '轻食'],
        promotion: {
          type: '优惠',
          text: '下午茶套餐8折'
        }
      },
      {
        id: '1007',
        name: '磁州民宿',
        logo: '/static/demo/shop7.jpg',
        category: '旅游住宿',
        rating: 4.8,
        distance: '4.2km',
        monthlySales: 85,
        tags: ['特色民宿', '乡村体验', '亲近自然'],
        promotion: {
          type: '满减',
          text: '连住3晚减100'
        }
      }
    ];
    
    shopsList.value = [...shopsList.value, ...newShops];
    isLoadingMore.value = false;
  }, 1500);
};

const viewShopDetail = (shop) => {
  navigateTo(`/subPackages/activity-showcase/pages/shops/detail?id=${shop.id}`);
};

const showFilter = () => {
  filterPopup.value.open();
};

const closeFilter = () => {
  filterPopup.value.close();
};

const toggleCategory = (index) => {
  const position = selectedCategories.value.indexOf(index);
  if (index === 0) {
    // 如果选择"全部"，清除其他选项
    selectedCategories.value = [0];
  } else {
    // 如果选择其他选项，移除"全部"选项
    if (selectedCategories.value.includes(0)) {
      selectedCategories.value = selectedCategories.value.filter(item => item !== 0);
    }
    
    // 切换选中状态
    if (position !== -1) {
      selectedCategories.value.splice(position, 1);
      // 如果没有选项，默认选中"全部"
      if (selectedCategories.value.length === 0) {
        selectedCategories.value = [0];
      }
    } else {
      selectedCategories.value.push(index);
    }
  }
};

const selectRating = (index) => {
  selectedRating.value = index;
};

const toggleService = (index) => {
  const position = selectedServices.value.indexOf(index);
  if (position !== -1) {
    selectedServices.value.splice(position, 1);
  } else {
    selectedServices.value.push(index);
  }
};

const resetFilters = () => {
  selectedCategories.value = [0];
  selectedRating.value = 0;
  selectedServices.value = [];
  currentCategory.value = 0;
  currentFilter.value = 'distance';
};

const applyFilter = () => {
  // 应用筛选
  console.log('应用筛选', {
    categories: selectedCategories.value.map(index => allCategories[index].value),
    rating: ratingOptions[selectedRating.value].value,
    services: selectedServices.value.map(index => serviceOptions[index].value)
  });
  
  // 模拟筛选结果
  uni.showToast({
    title: '筛选已应用',
    icon: 'success'
  });
  
  closeFilter();
};

const goBack = () => {
  uni.navigateBack();
};

const navigateTo = (url) => {
  uni.navigateTo({ url });
};
</script>

<style scoped>
.shops-container {
  min-height: 100vh;
  background-color: #F5F5F5;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.navbar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: var(--status-bar-height) 30rpx 0;
}

.back-btn, .search-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* 搜索和筛选栏样式 */
.search-filter-bar {
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  padding: 20rpx;
  margin-top: calc(var(--status-bar-height) + 90rpx);
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.search-box {
  display: flex;
  align-items: center;
  height: 70rpx;
  background: #F5F5F5;
  border-radius: 35rpx;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.search-icon {
  margin-right: 10rpx;
}

.search-placeholder {
  font-size: 28rpx;
  color: #999999;
}

.filter-options {
  display: flex;
  justify-content: space-around;
}

.filter-option {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
  padding: 10rpx 0;
}

.filter-option.active {
  color: #FF9500;
  font-weight: 500;
}

.sort-icon, .filter-icon {
  margin-left: 6rpx;
}

/* 分类标签栏样式 */
.category-scroll {
  background: #FFFFFF;
  padding: 20rpx 0;
  white-space: nowrap;
  border-bottom: 1rpx solid #EEEEEE;
}

.category-list {
  display: flex;
  padding: 0 20rpx;
}

.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666666;
  background: #F5F5F5;
}

.category-item.active {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
  border: 1rpx solid rgba(255, 149, 0, 0.3);
}

/* 商铺列表样式 */
.shops-scroll {
  height: calc(100vh - var(--status-bar-height) - 90rpx - 180rpx - 80rpx);
}

.shops-list {
  padding: 20rpx;
}

.shop-card {
  display: flex;
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.shop-logo {
  width: 180rpx;
  height: 180rpx;
  object-fit: cover;
}

.shop-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.shop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.shop-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.shop-rating {
  display: flex;
  align-items: center;
}

.rating-value {
  font-size: 28rpx;
  color: #FF9500;
  font-weight: 500;
  margin-right: 6rpx;
}

.shop-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.meta-icon {
  margin-right: 6rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #999999;
}

.shop-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.shop-tag {
  font-size: 22rpx;
  color: #666666;
  background: #F5F5F5;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.more-tag {
  font-size: 22rpx;
  color: #999999;
  padding: 4rpx 12rpx;
}

.shop-promotion {
  display: flex;
  align-items: center;
  margin-top: auto;
}

.promotion-tag {
  font-size: 22rpx;
  color: #FFFFFF;
  background: #FF9500;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.promotion-text {
  font-size: 24rpx;
  color: #FF9500;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #EEEEEE;
  border-top-color: #FF9500;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.empty-state .action-btn {
  padding: 15rpx 60rpx;
  font-size: 28rpx;
  color: #FFFFFF;
}

/* 筛选弹窗样式 */
.filter-popup {
  background: #FFFFFF;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  padding: 30rpx;
  max-height: 70vh;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.filter-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-content {
  max-height: calc(70vh - 180rpx);
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666666;
  background: #F5F5F5;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.filter-option.active {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
  border: 1rpx solid rgba(255, 149, 0, 0.3);
}

.filter-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #F0F0F0;
}

.filter-reset, .filter-apply {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.filter-reset {
  background: #F5F5F5;
  color: #666666;
  margin-right: 20rpx;
}

.filter-apply {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.2);
}
</style>