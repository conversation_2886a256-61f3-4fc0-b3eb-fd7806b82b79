<template>
  <view class="group-create-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">创建拼团活动</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 使用进度步骤组件 -->
    <progress-steps 
      :steps="['选择商品', '拼团设置', '确认创建']" 
      :current-step="currentStep">
    </progress-steps>
    
    <!-- 表单容器 -->
    <scroll-view scroll-y class="form-scroll-view" enable-back-to-top :scroll-into-view="scrollToId">
      <!-- 步骤1: 选择商品 -->
      <view class="form-section" v-if="currentStep === 1" id="step1">
        <!-- 使用区块标题组件 -->
        <section-header title="选择商品方式"></section-header>
        
        <view class="selection-options">
          <view class="selection-option" 
                :class="{ active: formData.productSelectionType === 'existing' }"
                @tap="formData.productSelectionType = 'existing'">
            <view class="option-radio">
              <view class="radio-inner" v-if="formData.productSelectionType === 'existing'"></view>
            </view>
            <view class="option-content">
              <text class="option-title">选择现有商品</text>
              <text class="option-desc">从已有商品库中选择商品添加到拼团</text>
            </view>
          </view>
          
          <view class="selection-option"
                :class="{ active: formData.productSelectionType === 'new' }"
                @tap="formData.productSelectionType = 'new'">
            <view class="option-radio">
              <view class="radio-inner" v-if="formData.productSelectionType === 'new'"></view>
            </view>
            <view class="option-content">
              <text class="option-title">新建拼团商品</text>
              <text class="option-desc">创建新商品并添加到拼团活动中</text>
            </view>
          </view>
        </view>
        
        <!-- 选择现有商品 -->
        <view class="product-selection" v-if="formData.productSelectionType === 'existing'">
          <view class="search-bar">
            <!-- 使用SVG图标组件 -->
            <svg-icon name="search" size="18" color="#999"></svg-icon>
            <input class="search-input" placeholder="搜索商品名称" v-model="searchKeyword" @input="searchProducts"/>
          </view>
          
          <view class="filter-options">
            <view class="filter-item" @tap="toggleFilterDropdown('category')">
              <text>商品类别</text>
              <svg-icon name="arrow-right" size="14" color="#999"></svg-icon>
            </view>
            <view class="filter-item" @tap="toggleFilterDropdown('price')">
              <text>价格区间</text>
              <svg-icon name="arrow-right" size="14" color="#999"></svg-icon>
            </view>
            <view class="filter-item" @tap="toggleFilterDropdown('stock')">
              <text>库存状态</text>
              <svg-icon name="arrow-right" size="14" color="#999"></svg-icon>
            </view>
          </view>
          
          <!-- 商品列表 -->
          <view class="product-list">
            <view class="product-item" v-for="(product, index) in productList" :key="index" @tap="toggleProductSelection(product)">
              <view class="checkbox" :class="{ checked: isProductSelected(product) }">
                <view class="checkbox-inner" v-if="isProductSelected(product)"></view>
              </view>
              <image class="product-img" :src="product.image" mode="aspectFill"></image>
              <view class="product-info">
                <text class="product-name">{{product.name}}</text>
                <view class="product-price">
                  <text class="price-current">¥{{product.price}}</text>
                  <text class="price-original" v-if="product.originalPrice">¥{{product.originalPrice}}</text>
                </view>
                <text class="product-stock">库存: {{product.stock}}件</text>
              </view>
            </view>
          </view>
          
          <!-- 已选商品 -->
          <view class="selected-products" v-if="formData.selectedProducts.length > 0">
            <view class="selected-header">
              <text class="selected-title">已选商品({{formData.selectedProducts.length}})</text>
              <text class="clear-all" @tap="clearSelectedProducts">清空</text>
            </view>
            <view class="selected-list">
              <view class="selected-item" v-for="(product, index) in formData.selectedProducts" :key="index">
                <image class="selected-img" :src="product.image" mode="aspectFill"></image>
                <view class="selected-info">
                  <text class="selected-name">{{product.name}}</text>
                  <text class="selected-price">¥{{product.price}}</text>
                </view>
                <view class="remove-btn" @tap.stop="removeProduct(index)">×</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 新建商品 -->
        <view class="create-product" v-if="formData.productSelectionType === 'new'">
          <!-- 使用图标按钮组件 -->
          <icon-button 
            icon="plus" 
            text="新建商品" 
            type="primary" 
            size="large"
            @click="navigateToCreateProduct">
          </icon-button>
          <text class="create-product-tip">创建新商品后可自动关联到拼团活动</text>
        </view>
      </view>
      
      <!-- 步骤2: 拼团设置 -->
      <view class="form-section" v-if="currentStep === 2" id="step2">
        <!-- 使用区块标题组件 -->
        <section-header title="拼团活动设置"></section-header>
        
        <view class="form-content">
          <!-- 使用表单组件 -->
          <!-- 拼团活动名称 -->
          <form-group label="活动名称" :is-required="true">
            <input class="form-input" type="text" v-model="formData.groupName" placeholder="请输入拼团活动名称" maxlength="30"/>
            <text class="input-counter">{{formData.groupName.length}}/30</text>
          </form-group>
          
          <!-- 拼团类型选择 -->
          <form-group label="拼团类型" :is-required="true" tip="选择普通拼团或套餐拼团">
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.groupType === 'normal' }" 
                @tap="formData.groupType = 'normal'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.groupType === 'normal'"></view>
                </view>
                <text class="radio-label">普通拼团</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.groupType === 'package' }" 
                @tap="formData.groupType = 'package'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.groupType === 'package'"></view>
                </view>
                <text class="radio-label">套餐拼团</text>
              </view>
            </view>
          </form-group>
          
          <!-- 套餐拼团设置 -->
          <view class="package-settings" v-if="formData.groupType === 'package'">
            <form-group label="套餐名称" :is-required="true">
              <input class="form-input" type="text" v-model="formData.packageName" placeholder="如：四菜一汤家庭套餐" maxlength="30"/>
              <text class="input-counter">{{formData.packageName.length}}/30</text>
            </form-group>
            
            <form-group label="套餐描述">
              <textarea class="form-textarea" v-model="formData.packageDescription" placeholder="请输入套餐描述信息" maxlength="100"></textarea>
              <text class="input-counter">{{formData.packageDescription.length}}/100</text>
            </form-group>
            
            <!-- 套餐商品列表 -->
            <form-group label="套餐商品" :is-required="true" tip="添加组成套餐的商品">
              <view class="package-items">
                <view class="package-item" v-for="(item, idx) in formData.packageItems" :key="idx">
                  <view class="package-item-header">
                    <text class="package-item-title">商品 {{idx + 1}}</text>
                    <view class="package-item-remove" @tap="removePackageItem(idx)" v-if="formData.packageItems.length > 1">×</view>
                  </view>
                  <view class="package-item-content">
                    <view class="package-item-row">
                      <text class="package-item-label">商品名称:</text>
                      <input class="package-item-input" v-model="item.name" placeholder="请输入商品名称"/>
                    </view>
                    <view class="package-item-row">
                      <text class="package-item-label">原价(元):</text>
                      <input class="package-item-input" type="digit" v-model="item.originalPrice" placeholder="0.00"/>
                    </view>
                    <view class="package-item-row">
                      <text class="package-item-label">数量:</text>
                      <view class="number-picker small">
                        <view class="number-btn minus" @tap="decrementItemQuantity(idx)">-</view>
                        <input class="number-input" type="number" v-model="item.quantity" />
                        <view class="number-btn plus" @tap="incrementItemQuantity(idx)">+</view>
                      </view>
                    </view>
                  </view>
                </view>
                
                <!-- 添加商品按钮 -->
                <view class="add-package-item" @tap="addPackageItem">
                  <view class="add-icon">+</view>
                  <text class="add-text">添加商品</text>
                </view>
              </view>
            </form-group>
          </view>
          
          <!-- 市场价 -->
          <form-group label="市场价" :is-required="true" tip="商品的市场参考价格">
            <view class="price-input-wrapper">
              <text class="price-symbol">¥</text>
              <input class="form-input price-input" type="digit" v-model="formData.marketPrice" placeholder="请输入商品市场价"/>
            </view>
          </form-group>
          
          <!-- 日常价 -->
          <form-group label="日常价" :is-required="true" tip="店铺内商品平时销售的价格">
            <view class="price-input-wrapper">
              <text class="price-symbol">¥</text>
              <input class="form-input price-input" type="digit" v-model="formData.regularPrice" placeholder="请输入商品日常价"/>
            </view>
          </form-group>
          
          <!-- 拼团价格 -->
          <form-group label="拼团价格" :is-required="true">
            <view class="price-input-wrapper">
              <text class="price-symbol">¥</text>
              <input class="form-input price-input" type="digit" v-model="formData.groupPrice" placeholder="请输入拼团价格"/>
            </view>
          </form-group>
          
          <!-- 开启拼团按钮 -->
          <form-group label="开启拼团" tip="开启后需要拼团才能享受优惠价，关闭则无需拼团直接享受优惠">
            <view class="switch-container">
              <switch :checked="formData.requireGroup" @change="e => formData.requireGroup = e.detail.value" color="#9040FF" />
            </view>
          </form-group>
          
          <!-- 成团人数 -->
          <form-group label="成团人数" :is-required="true">
            <view class="number-picker">
              <view class="number-btn minus" @tap="decrementGroupSize">-</view>
              <input class="number-input" type="number" v-model="formData.minGroupSize" />
              <view class="number-btn plus" @tap="incrementGroupSize">+</view>
            </view>
          </form-group>
          
          <!-- 单人限购 -->
          <form-group label="单人限购">
            <view class="number-picker">
              <view class="number-btn minus" @tap="decrementLimitPerUser">-</view>
              <input class="number-input" type="number" v-model="formData.limitPerUser" />
              <view class="number-btn plus" @tap="incrementLimitPerUser">+</view>
              <text class="unit-text">件</text>
            </view>
          </form-group>
          
          <!-- 活动有效期 -->
          <form-group label="活动有效期" :is-required="true">
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.validity.type === 'fixed' }" 
                @tap="formData.validity.type = 'fixed'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.validity.type === 'fixed'"></view>
                </view>
                <text class="radio-label">固定时间段</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.validity.type === 'dynamic' }" 
                @tap="formData.validity.type = 'dynamic'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.validity.type === 'dynamic'"></view>
                </view>
                <text class="radio-label">动态时间段</text>
              </view>
            </view>
            
            <!-- 固定时间段选择器 -->
            <view class="datetime-picker" v-if="formData.validity.type === 'fixed'">
              <picker mode="date" :value="formData.validity.startDate" @change="(e) => dateChange('start', e)" class="picker-component">
                <view class="datetime-field">
                  <text class="datetime-placeholder" v-if="!formData.validity.startDate">开始日期</text>
                  <text class="datetime-value" v-else>{{formData.validity.startDate}}</text>
                  <view class="datetime-icon"></view>
                </view>
              </picker>
              <picker mode="time" :value="formData.validity.startTime" @change="(e) => timeChange('start', e)" class="picker-component">
                <view class="datetime-field">
                  <text class="datetime-placeholder" v-if="!formData.validity.startTime">开始时间</text>
                  <text class="datetime-value" v-else>{{formData.validity.startTime}}</text>
                  <view class="datetime-icon"></view>
                </view>
              </picker>
              <text class="datetime-separator">至</text>
              <picker mode="date" :value="formData.validity.endDate" @change="(e) => dateChange('end', e)" class="picker-component">
                <view class="datetime-field">
                  <text class="datetime-placeholder" v-if="!formData.validity.endDate">结束日期</text>
                  <text class="datetime-value" v-else>{{formData.validity.endDate}}</text>
                  <view class="datetime-icon"></view>
                </view>
              </picker>
              <picker mode="time" :value="formData.validity.endTime" @change="(e) => timeChange('end', e)" class="picker-component">
                <view class="datetime-field">
                  <text class="datetime-placeholder" v-if="!formData.validity.endTime">结束时间</text>
                  <text class="datetime-value" v-else>{{formData.validity.endTime}}</text>
                  <view class="datetime-icon"></view>
                </view>
              </picker>
            </view>
            
            <!-- 动态时间段设置 -->
            <view class="dynamic-days" v-if="formData.validity.type === 'dynamic'">
              <view class="number-picker">
                <view class="number-btn minus" @tap="decrementValidityDays">-</view>
                <input class="number-input" type="number" v-model="formData.validity.days" />
                <view class="number-btn plus" @tap="incrementValidityDays">+</view>
                <text class="unit-text">天</text>
              </view>
              <text class="form-tip">拼团创建成功后，活动自动生效并持续指定天数</text>
            </view>
          </form-group>
          
          <!-- 成团时限 -->
          <form-group label="成团时限" :is-required="true">
            <view class="number-picker">
              <view class="number-btn minus" @tap="decrementTimeLimit">-</view>
              <input class="number-input" type="number" v-model="formData.timeLimit" />
              <view class="number-btn plus" @tap="incrementTimeLimit">+</view>
            </view>
          </form-group>
          
          <!-- 支付和核销设置 -->
          <form-group label="支付方式" :is-required="true">
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.paymentType === 'store' }" 
                @tap="formData.paymentType = 'store'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.paymentType === 'store'"></view>
                </view>
                <text class="radio-label">到店支付</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.paymentType === 'online' }" 
                @tap="formData.paymentType = 'online'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.paymentType === 'online'"></view>
                </view>
                <text class="radio-label">在线支付</text>
              </view>
            </view>
          </form-group>
          
          <form-group label="核销方式" :is-required="true">
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.verifyType === 'store' }" 
                @tap="formData.verifyType = 'store'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.verifyType === 'store'"></view>
                </view>
                <text class="radio-label">到店核销</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.verifyType === 'online' }" 
                @tap="formData.verifyType = 'online'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.verifyType === 'online'"></view>
                </view>
                <text class="radio-label">在线核销</text>
              </view>
            </view>
          </form-group>
          
          <form-group label="生成核销码" :is-required="true">
            <view class="switch-container">
              <switch :checked="formData.verificationCode" @change="e => formData.verificationCode = e.detail.value" color="#9040FF" />
            </view>
          </form-group>
          
          <form-group label="核销码有效期" :is-required="true">
            <view class="number-picker">
              <view class="number-btn minus" @tap="decrementVerificationExpiry">-</view>
              <input class="number-input" type="number" v-model="formData.verificationExpiry" />
              <view class="number-btn plus" @tap="incrementVerificationExpiry">+</view>
              <text class="unit-text">天</text>
            </view>
          </form-group>
          
          <form-group label="允许部分核销">
            <view class="switch-container">
              <switch :checked="formData.allowPartialVerification" @change="e => formData.allowPartialVerification = e.detail.value" color="#9040FF" />
            </view>
          </form-group>
          
          <!-- 添加分销设置 -->
          <form-group label="分销设置" v-if="hasMerchantDistribution">
            <distribution-setting 
              :initial-settings="formData.distributionSettings"
              @update="updateDistributionSettings"
            />
          </form-group>
        </view>
      </view>
      
      <!-- 步骤3: 确认创建 -->
      <view class="form-section" v-if="currentStep === 3" id="step3">
        <!-- 使用区块标题组件 -->
        <section-header title="确认拼团信息"></section-header>
        
        <view class="form-content">
          <!-- 拼团信息预览 -->
          <view class="preview-card">
            <view class="preview-header">
              <text class="preview-title">{{formData.groupName || '拼团活动名称'}}</text>
              <text class="preview-dates">{{formData.startDate}} 至 {{formData.endDate}}</text>
            </view>
            
            <view class="preview-content">
              <!-- 商品预览 -->
              <view class="preview-products">
                <view 
                  class="preview-product-item" 
                  v-for="(product, index) in formData.selectedProducts" 
                  :key="index"
                >
                  <image class="product-image" :src="product.image" mode="aspectFill"></image>
                  <view class="product-info">
                    <text class="product-name">{{product.name}}</text>
                    <view class="product-price">
                      <text class="group-price">¥{{product.groupPrice || product.price}}</text>
                      <text class="original-price">¥{{product.originalPrice || product.price}}</text>
                </view>
              </view>
                </view>
              </view>
              
              <!-- 拼团规则 -->
              <view class="group-rules">
                <view class="rule-item">
                  <text class="rule-label">拼团人数:</text>
                  <text class="rule-value">{{formData.groupSize}}人</text>
                </view>
              <view class="rule-item">
                  <text class="rule-label">拼团时限:</text>
                  <text class="rule-value">{{formData.groupDuration}}小时</text>
              </view>
              <view class="rule-item">
                  <text class="rule-label">单人限购:</text>
                  <text class="rule-value">{{formData.purchaseLimit || '不限'}}</text>
              </view>
              <view class="rule-item">
                  <text class="rule-label">活动库存:</text>
                  <text class="rule-value">{{formData.stockLimit || '不限'}}</text>
              </view>
              </view>
            </view>
          </view>
          
          <!-- 活动推广 -->
          <form-group label="活动推广" tip="发布、置顶或刷新活动，提升曝光率">
            <MarketingPromotionActions 
              :activity-type="'group'"
              :activity-id="tempGroupId"
              :publish-mode-only="true"
              :show-actions="['publish']"
              @action-completed="handlePromotionCompleted"
            />
          </form-group>
          
          <!-- 提交按钮 -->
          <view class="form-actions">
            <button class="btn-secondary" @click="prevStep">上一步</button>
            <button class="btn-primary" @click="submitGroupActivity">创建拼团</button>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮区域 -->
    <view class="footer-buttons">
      <button class="btn btn-secondary" @tap="prevStep" v-if="currentStep > 1">上一步</button>
      <button class="btn btn-primary" @tap="nextStep" v-if="currentStep < 3">下一步</button>
      <button class="btn btn-primary" @tap="submitForm" v-if="currentStep === 3">创建活动</button>
    </view>
  </view>
</template>

<script>
// 导入组件
import SectionHeader from '/subPackages/merchant-admin/components/ui/SectionHeader.vue';
import FormGroup from '/subPackages/merchant-admin/components/ui/FormGroup.vue';
import IconButton from '/subPackages/merchant-admin/components/ui/IconButton.vue';
import SvgIcon from '/subPackages/merchant-admin/components/ui/SvgIcon.vue';
import ProgressSteps from '/subPackages/merchant-admin/components/ui/ProgressSteps.vue';
import DistributionSetting from '../distribution/components/DistributionSetting.vue';
import distributionMixin from '/subPackages/merchant-admin-marketing/mixins/distributionMixin';
import MarketingPromotionActions from '/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue';

export default {
  components: {
    SectionHeader,
    FormGroup,
    IconButton,
    SvgIcon,
    ProgressSteps,
    DistributionSetting,
    MarketingPromotionActions
  },
  mixins: [distributionMixin], // 使用分销混入
  data() {
    return {
      currentStep: 1,
      scrollToId: 'step1',
      searchKeyword: '',
      
      // 表单数据
      formData: {
        // 步骤1：商品信息
        productSelectionType: 'existing', // existing=选择已有商品, new=新建商品
        selectedProducts: [], // 选择的商品列表
        
        // 步骤2：拼团设置
        groupName: '', // 拼团活动名称
        groupType: 'normal', // normal=普通拼团, package=套餐拼团
        packageName: '', // 套餐名称
        packageDescription: '', // 套餐描述
        packageItems: [{ // 套餐商品列表
          name: '',
          originalPrice: '',
          quantity: 1
        }],
        marketPrice: '', // 市场价
        regularPrice: '', // 日常价
        groupPrice: '', // 拼团价格
        requireGroup: true, // 是否需要拼团才能享受优惠价
        minGroupSize: 2, // 成团人数
        limitPerUser: 1, // 单用户限购
        validity: { // 有效期
          type: 'fixed', // fixed=固定时间段, dynamic=动态时间段
          startDate: '', // 开始日期
          startTime: '', // 开始时间
          endDate: '', // 结束日期
          endTime: '', // 结束时间
          days: 7 // 动态时间段的天数
        },
        timeLimit: 24, // 成团时限(小时)
        rules: [], // 自定义规则
        
        // 支付和核销设置
        paymentType: 'store', // online=在线支付, store=到店支付
        verifyType: 'store', // online=在线核销, store=到店核销
        verificationCode: true, // 是否生成核销码
        verificationExpiry: 7, // 核销码有效期(天)
        allowPartialVerification: false, // 是否允许部分核销
        distributionSettings: {
          enabled: false,
          commissionMode: 'percentage',
          commissions: {
            level1: '',
            level2: '',
            level3: ''
          },
          enableLevel3: false
        },
        groupSize: 2, // 拼团人数
        groupDuration: 24, // 拼团时限(小时)
        purchaseLimit: '不限', // 单人限购
        stockLimit: '不限' // 活动库存
      },
      
      // 模拟商品列表
      productList: [
        {
          id: 1,
          name: 'iPhone 14 Pro Max',
          price: '8999.00',
          originalPrice: '9999.00',
          stock: 120,
          image: '/static/images/products/iphone.jpg'
        },
        {
          id: 2,
          name: 'Apple Watch Series 8',
          price: '3099.00',
          originalPrice: '3299.00',
          stock: 85,
          image: '/static/images/products/watch.jpg'
        },
        {
          id: 3,
          name: 'AirPods Pro 2代',
          price: '1799.00',
          originalPrice: '1999.00',
          stock: 200,
          image: '/static/images/products/airpods.jpg'
        },
        {
          id: 4,
          name: 'MacBook Air M2',
          price: '7599.00',
          originalPrice: '7999.00',
          stock: 50,
          image: '/static/images/products/macbook.jpg'
        },
        {
          id: 5,
          name: 'iPad Pro 2022',
          price: '6299.00',
          originalPrice: '6799.00',
          stock: 75,
          image: '/static/images/products/ipad.jpg'
        }
      ],
      hasMerchantDistribution: false, // 商家是否开通分销功能
      tempGroupId: 'temp-' + Date.now() // 临时ID，实际应该从后端获取
    };
  },
  computed: {
    progressPercentage() {
      // 计算进度百分比
      return (this.currentStep / 3) * 100;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '拼团活动帮助',
        icon: 'none'
      });
    },
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep -= 1;
        this.scrollToId = `step${this.currentStep}`;
      }
    },
    nextStep() {
      // 表单验证
      if (this.currentStep === 1) {
        // 验证是否选择了商品
        if (this.formData.productSelectionType === 'existing' && this.formData.selectedProducts.length === 0) {
          uni.showToast({
            title: '请至少选择一个商品',
            icon: 'none'
          });
          return;
        }
      } else if (this.currentStep === 2) {
        // 验证拼团设置
        if (!this.formData.groupName) {
          uni.showToast({ title: '请输入活动名称', icon: 'none' });
          return;
        }
        
        // 套餐拼团特有验证
        if (this.formData.groupType === 'package') {
          if (!this.formData.packageName) {
            uni.showToast({ title: '请输入套餐名称', icon: 'none' });
            return;
          }
          
          // 验证套餐商品
          let hasEmptyItem = false;
          this.formData.packageItems.forEach(item => {
            if (!item.name || !item.originalPrice) {
              hasEmptyItem = true;
            }
          });
          
          if (hasEmptyItem) {
            uni.showToast({ title: '请完善套餐商品信息', icon: 'none' });
            return;
          }
        }
        
        if (!this.formData.marketPrice) {
          uni.showToast({ title: '请输入市场价', icon: 'none' });
          return;
        }
        
        if (!this.formData.regularPrice) {
          uni.showToast({ title: '请输入日常价', icon: 'none' });
          return;
        }
        
        if (!this.formData.groupPrice) {
          uni.showToast({ title: '请输入拼团价格', icon: 'none' });
          return;
        }
        
        // 检查价格大小关系
        const marketPrice = parseFloat(this.formData.marketPrice);
        const regularPrice = parseFloat(this.formData.regularPrice);
        const groupPrice = parseFloat(this.formData.groupPrice);
        
        if (marketPrice <= regularPrice) {
          uni.showToast({ title: '市场价应高于日常价', icon: 'none' });
          return;
        }
        
        if (regularPrice <= groupPrice) {
          uni.showToast({ title: '日常价应高于拼团价', icon: 'none' });
          return;
        }
        
        if (this.formData.validity.type === 'fixed') {
          if (!this.formData.validity.startDate || !this.formData.validity.endDate || !this.formData.validity.startTime || !this.formData.validity.endTime) {
            uni.showToast({ title: '请选择活动有效期', icon: 'none' });
            return;
          }
        }
      }
      
      // 通过验证，进入下一步
      if (this.currentStep < 3) {
        this.currentStep += 1;
        this.scrollToId = `step${this.currentStep}`;
      }
    },
    submitForm() {
      // 表单提交逻辑
      uni.showLoading({
        title: '创建中...'
      });
      
      // 准备提交的数据
      const submitData = {
        // 基本信息
        groupName: this.formData.groupName,
        groupType: this.formData.groupType,
        marketPrice: parseFloat(this.formData.marketPrice),
        regularPrice: parseFloat(this.formData.regularPrice),
        groupPrice: parseFloat(this.formData.groupPrice),
        requireGroup: this.formData.requireGroup,
        minGroupSize: this.formData.minGroupSize,
        limitPerUser: this.formData.limitPerUser,
        timeLimit: this.formData.timeLimit,
        
        // 有效期
        validityType: this.formData.validity.type,
        validityStartDate: this.formData.validity.startDate,
        validityStartTime: this.formData.validity.startTime,
        validityEndDate: this.formData.validity.endDate,
        validityEndTime: this.formData.validity.endTime,
        validityDays: this.formData.validity.days,
        
        // 支付和核销设置
        paymentType: this.formData.paymentType,
        verifyType: this.formData.verifyType,
        verificationCode: this.formData.verificationCode,
        verificationExpiry: this.formData.verificationExpiry,
        allowPartialVerification: this.formData.allowPartialVerification
      };
      
      // 根据拼团类型添加不同的数据
      if (this.formData.groupType === 'normal') {
        // 普通拼团 - 添加商品信息
        submitData.products = this.formData.selectedProducts.map(product => ({
          id: product.id,
          name: product.name,
          price: product.price,
          originalPrice: product.originalPrice,
          image: product.image,
          stock: product.stock
        }));
      } else if (this.formData.groupType === 'package') {
        // 套餐拼团 - 添加套餐信息
        submitData.packageName = this.formData.packageName;
        submitData.packageDescription = this.formData.packageDescription;
        submitData.packageItems = this.formData.packageItems.map(item => ({
          name: item.name,
          originalPrice: parseFloat(item.originalPrice),
          quantity: parseInt(item.quantity)
        }));
        submitData.totalOriginalPrice = parseFloat(this.calculateTotalOriginalPrice());
        submitData.totalSavings = parseFloat(this.calculateSavings());
        submitData.totalItems = this.getTotalItemQuantity();
      }
      
      // 处理分销设置
      if (this.hasMerchantDistribution && this.formData.distributionSettings.enabled) {
        // 添加分销相关数据
        submitData.distributionSettings = this.formData.distributionSettings;
      }
      
      console.log('提交数据:', submitData);
      
      // 模拟API请求
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '拼团活动创建成功！',
          icon: 'success',
          duration: 2000
        });
        
        // 返回拼团管理页面
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
      }, 1500);
    },
    searchProducts() {
      // 实现商品搜索逻辑
      console.log('搜索商品:', this.searchKeyword);
      // 这里可以添加实际的搜索逻辑
    },
    toggleFilterDropdown(filterType) {
      // 打开筛选下拉列表
      uni.showToast({
        title: `打开${filterType}筛选`,
        icon: 'none'
      });
    },
    toggleProductSelection(product) {
      // 切换商品选择状态
      const index = this.formData.selectedProducts.findIndex(p => p.id === product.id);
      if (index === -1) {
        this.formData.selectedProducts.push(product);
      } else {
        this.formData.selectedProducts.splice(index, 1);
      }
    },
    isProductSelected(product) {
      // 检查商品是否已选择
      return this.formData.selectedProducts.findIndex(p => p.id === product.id) !== -1;
    },
    removeProduct(index) {
      // 从已选列表中移除商品
      this.formData.selectedProducts.splice(index, 1);
    },
    clearSelectedProducts() {
      // 清空已选商品列表
      this.formData.selectedProducts = [];
    },
    navigateToCreateProduct() {
      // 导航到创建商品页
      uni.showToast({
        title: '跳转到创建商品页面',
        icon: 'none'
      });
    },
    // 步骤2中的方法
    decrementGroupSize() {
      if (this.formData.minGroupSize > 2) {
        this.formData.minGroupSize -= 1;
      }
    },
    incrementGroupSize() {
      if (this.formData.minGroupSize < 100) {
        this.formData.minGroupSize += 1;
      }
    },
    decrementLimitPerUser() {
      if (this.formData.limitPerUser > 0) {
        this.formData.limitPerUser -= 1;
      }
    },
    incrementLimitPerUser() {
      if (this.formData.limitPerUser < 999) {
        this.formData.limitPerUser += 1;
      }
    },
    // 套餐商品相关方法
    addPackageItem() {
      this.formData.packageItems.push({
        name: '',
        originalPrice: '',
        quantity: 1
      });
    },
    removePackageItem(index) {
      if (this.formData.packageItems.length > 1) {
        this.formData.packageItems.splice(index, 1);
      }
    },
    decrementItemQuantity(index) {
      if (this.formData.packageItems[index].quantity > 1) {
        this.formData.packageItems[index].quantity -= 1;
      }
    },
    incrementItemQuantity(index) {
      if (this.formData.packageItems[index].quantity < 99) {
        this.formData.packageItems[index].quantity += 1;
      }
    },
    decrementValidityDays() {
      if (this.formData.validity.days > 1) {
        this.formData.validity.days -= 1;
      }
    },
    incrementValidityDays() {
      if (this.formData.validity.days < 90) {
        this.formData.validity.days += 1;
      }
    },
    decrementTimeLimit() {
      if (this.formData.timeLimit > 1) {
        this.formData.timeLimit -= 1;
      }
    },
    incrementTimeLimit() {
      if (this.formData.timeLimit < 72) {
        this.formData.timeLimit += 1;
      }
    },
    decrementVerificationExpiry() {
      if (this.formData.verificationExpiry > 1) {
        this.formData.verificationExpiry -= 1;
      }
    },
    incrementVerificationExpiry() {
      if (this.formData.verificationExpiry < 90) {
        this.formData.verificationExpiry += 1;
      }
    },
    dateChange(type, e) {
      // 处理日期选择
      this.formData.validity[`${type}Date`] = e.detail.value;
    },
    timeChange(type, e) {
      // 处理时间选择
      this.formData.validity[`${type}Time`] = e.detail.value;
    },
    // 套餐计算方法
    getTotalItemQuantity() {
      if (!this.formData.packageItems || this.formData.packageItems.length === 0) {
        return 0;
      }
      return this.formData.packageItems.reduce((sum, item) => sum + parseInt(item.quantity || 0), 0);
    },
    calculateTotalOriginalPrice() {
      if (!this.formData.packageItems || this.formData.packageItems.length === 0) {
        return '0.00';
      }
      const total = this.formData.packageItems.reduce((sum, item) => {
        const price = parseFloat(item.originalPrice || 0);
        const quantity = parseInt(item.quantity || 0);
        return sum + (price * quantity);
      }, 0);
      return total.toFixed(2);
    },
    calculateSavings() {
      const totalOriginal = parseFloat(this.calculateTotalOriginalPrice());
      const groupPrice = parseFloat(this.formData.groupPrice || 0);
      const savings = totalOriginal - groupPrice;
      return savings > 0 ? savings.toFixed(2) : '0.00';
    },
    // 更新分销设置
    updateDistributionSettings(settings) {
      this.formData.distributionSettings = settings;
    },
    
    // 检查商家是否开通分销功能
    checkMerchantDistribution() {
      // 调用API检查商家是否开通分销功能
      // 这里模拟API调用
      setTimeout(() => {
        this.hasMerchantDistribution = true;
      }, 500);
    },
    
    // 保存拼团活动
    async saveGroupActivity() {
      // ... existing code ...
      
      // 处理分销设置
      if (this.hasMerchantDistribution && this.formData.distributionSettings.enabled) {
        const success = await this.saveActivityDistributionSettings('group', groupId);
        if (!success) {
          return;
        }
      }
      
      // ... existing code ...
    },
    
    // 处理推广操作完成事件
    handlePromotionCompleted(data) {
      console.log('推广操作完成:', data);
      // 根据不同操作类型处理结果
      if (data.action === 'publish') {
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        });
      } else if (data.action === 'top') {
        uni.showToast({
          title: '置顶成功',
          icon: 'success'
        });
      } else if (data.action === 'refresh') {
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }
    }
  },
  mounted() {
    // 检查商家是否开通分销功能
    this.checkMerchantDistribution();
  }
};
</script>

<style lang="scss">
.group-create-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 100px;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 进度指示器 */
.progress-container {
  padding: 20px 15px;
  background-color: #fff;
  margin-bottom: 10px;
  box-shadow: 0 1px 5px rgba(0,0,0,0.05);
}

.progress-bar {
  height: 4px;
  background-color: #EBEDF5;
  border-radius: 2px;
  margin-bottom: 15px;
  position: relative;
  overflow: hidden;
}

.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: #EBEDF5;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(to right, #9040FF, #5E35B1);
  border-radius: 2px;
  transition: width 0.3s;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.step-line {
  height: 1px;
  background-color: #EBEDF5;
  flex: 1;
  margin: 0 5px;
  transition: background-color 0.3s;
  
  &.active {
    background-color: #9040FF;
  }
}

.step-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #EBEDF5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  transition: all 0.3s;
  
  .check-icon {
    width: 10px;
    height: 6px;
    border-left: 2px solid #fff;
    border-bottom: 2px solid #fff;
    transform: rotate(-45deg) translate(1px, -1px);
  }
}

.step-label {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.step.active {
  .step-dot {
    background-color: #9040FF;
    color: #fff;
  }
  
  .step-label {
    color: #9040FF;
    font-weight: 500;
  }
}

.step.completed {
  .step-dot {
    background-color: #9040FF;
  }
  
  .step-label {
    color: #9040FF;
  }
}

/* 表单容器 */
.form-scroll-view {
  flex: 1;
  padding-bottom: 20px;
}

.form-section {
  margin: 15px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  overflow: hidden;
}

.section-header {
  padding: 15px;
  border-bottom: 1px solid #F0F0F0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 选择方式 */
.selection-options {
  padding: 15px;
}

.selection-option {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  background-color: #F9F9F9;
  transition: all 0.3s;
  
  &.active {
    background-color: rgba(126, 48, 225, 0.05);
    border-left: 3px solid #7E30E1;
  }
}

.option-radio {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  border: 2px solid #CCCCCC;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .radio-inner {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #7E30E1;
  }
}

.selection-option.active .option-radio {
  border-color: #7E30E1;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.option-desc {
  font-size: 12px;
  color: #999;
}

/* 商品选择 */
.product-selection {
  padding: 15px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 20px;
  padding: 8px 15px;
  margin-bottom: 15px;
}

.search-icon {
  width: 14px;
  height: 14px;
  border: 2px solid #999;
  border-radius: 50%;
  position: relative;
  margin-right: 8px;
  
  &:after {
    content: '';
    position: absolute;
    width: 2px;
    height: 6px;
    background-color: #999;
    bottom: -4px;
    right: -2px;
    transform: rotate(-45deg);
  }
}

.search-input {
  flex: 1;
  height: 20px;
  font-size: 14px;
}

.filter-options {
  display: flex;
  justify-content: space-around;
  padding: 10px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid #F0F0F0;
}

.filter-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.arrow-down {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #666;
  margin-left: 5px;
}

/* 商品列表 */
.product-list {
  margin-bottom: 20px;
}

.product-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #F0F0F0;
}

.checkbox {
  width: 22px;
  height: 22px;
  border: 2px solid #CCC;
  border-radius: 4px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.checked {
    background-color: #7E30E1;
    border-color: #7E30E1;
  }
  
  .checkbox-inner {
    width: 10px;
    height: 6px;
    border-left: 2px solid #fff;
    border-bottom: 2px solid #fff;
    transform: rotate(-45deg) translate(1px, -1px);
  }
}

.product-img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  margin-right: 10px;
  background-color: #f5f5f5;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  display: block;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.price-current {
  font-size: 15px;
  font-weight: 600;
  color: #FF3B30;
  margin-right: 5px;
}

.price-original {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-stock {
  font-size: 12px;
  color: #999;
}

/* 已选商品 */
.selected-products {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #F0F0F0;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.selected-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.clear-all {
  font-size: 12px;
  color: #7E30E1;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
}

.selected-item {
  width: calc(33.33% - 10px);
  margin-right: 10px;
  margin-bottom: 10px;
  position: relative;
  background-color: #F9F9F9;
  border-radius: 8px;
  padding: 8px;
  overflow: hidden;
}

.selected-img {
  width: 100%;
  height: 70px;
  border-radius: 4px;
  margin-bottom: 5px;
  object-fit: cover;
}

.selected-info {
  padding-top: 5px;
}

.selected-name {
  font-size: 12px;
  color: #333;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.selected-price {
  font-size: 12px;
  color: #FF3B30;
  font-weight: 500;
}

.remove-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0,0,0,0.3);
  color: #fff;
  font-size: 16px;
  border-radius: 0 0 0 8px;
}

/* 新建商品 */
.create-product {
  padding: 30px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.create-product-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60%;
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #34C759, #32ADE6);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  box-shadow: 0 5px 15px rgba(52, 199, 89, 0.2);
}

.btn-icon {
  font-size: 20px;
  margin-right: 5px;
}

.create-product-tip {
  font-size: 12px;
  color: #999;
}

/* 步骤2: 表单样式 */
.form-content {
  padding: 15px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  margin-bottom: 10px;
}

.label-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  
  &.required:after {
    content: '*';
    color: #FF3B30;
    margin-left: 4px;
  }
}

.form-field {
  position: relative;
}

.form-input {
  width: 100%;
  height: 44px;
  background-color: #F5F5F5;
  border-radius: 8px;
  padding: 0 15px;
  font-size: 14px;
  color: #333;
}

.input-counter {
  position: absolute;
  right: 15px;
  top: 12px;
  font-size: 12px;
  color: #999;
}

.price-input-wrapper {
  position: relative;
}

.price-symbol {
  position: absolute;
  left: 15px;
  top: 12px;
  font-size: 14px;
  color: #333;
}

.price-input {
  padding-left: 30px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  display: block;
}

.number-picker {
  display: flex;
  align-items: center;
  height: 44px;
}

.number-btn {
  width: 44px;
  height: 44px;
  background-color: #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: #999;
  
  &.minus {
    border-radius: 8px 0 0 8px;
  }
  
  &.plus {
    border-radius: 0 8px 8px 0;
  }
}

.number-input {
  width: 60px;
  height: 44px;
  background-color: #F5F5F5;
  text-align: center;
  font-size: 16px;
  color: #333;
}

.unit-text {
  margin-left: 8px;
  font-size: 14px;
  color: #666;
}

.radio-group {
  display: flex;
  margin-bottom: 15px;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
  padding: 5px 0;
}

.radio-dot {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid #CCCCCC;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .radio-dot-inner {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #7E30E1;
  }
}

.radio-item.active .radio-dot {
  border-color: #7E30E1;
}

.radio-label {
  font-size: 14px;
  color: #333;
}

.datetime-picker {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.datetime-field {
  background: #F8FAFC;
  border-radius: 4px;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 8px;
  position: relative;
}

.datetime-placeholder {
  color: #999;
  font-size: 14px;
}

.datetime-value {
  color: #333;
  font-size: 14px;
}

.datetime-icon {
  width: 10px;
  height: 10px;
  border-top: 2px solid #999;
  border-right: 2px solid #999;
  transform: rotate(45deg);
  position: absolute;
  right: 12px;
}

.datetime-separator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 0;
  color: #666;
}

.picker-component {
  width: 100%;
}

/* 调整时间日期选择布局 */
@media screen and (min-width: 375px) {
  .datetime-picker {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .picker-component {
    width: 48%;
    margin-right: 2%;
  }
  
  .datetime-separator {
    display: flex;
    width: 100%;
    justify-content: center;
    margin: 5px 0;
  }
}

/* 步骤3：确认创建样式 */
.confirm-content {
  padding: 15px;
}

.activity-card {
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.activity-header {
  position: relative;
  height: 120px;
}

.activity-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7));
}

.activity-info {
  position: relative;
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.activity-name {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.activity-time {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.activity-body {
  padding: 15px;
  background-color: #fff;
  display: flex;
}

.product-preview {
  width: 70px;
  height: 70px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 15px;
}

.product-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-count {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 2px 6px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 10px;
  border-top-left-radius: 8px;
}

.activity-detail {
  flex: 1;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-label {
  font-size: 13px;
  color: #666;
}

.detail-value {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  
  &.price {
    color: #FF3B30;
  }
}

.confirm-products,
.confirm-rules {
  margin-bottom: 20px;
}

.confirm-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.confirm-product-list {
  background-color: #F9F9F9;
  border-radius: 8px;
}

.confirm-product-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #F0F0F0;
  
  &:last-child {
    border-bottom: none;
  }
}

.confirm-product-img {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  margin-right: 10px;
}

.confirm-product-info {
  flex: 1;
}

.confirm-product-name {
  font-size: 13px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.confirm-product-price {
  display: flex;
  flex-direction: column;
}

.confirm-price-market {
  color: #999;
  text-decoration: line-through;
  font-size: 11px;
  margin-right: 8px;
  display: block;
}

.confirm-price-regular {
  color: #FF6B00;
  text-decoration: line-through;
  font-size: 11px;
  margin-right: 8px;
  display: block;
  margin-top: 2px;
}

.confirm-price-group {
  font-size: 12px;
  color: #666;
  
  .price-value {
    color: #FF3B30;
    font-weight: 500;
  }
}

.rules-content {
  background-color: #F9F9F9;
  border-radius: 8px;
  padding: 12px 15px;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.rule-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #7E30E1;
  margin-top: 6px;
  margin-right: 8px;
  flex-shrink: 0;
}

.rule-text {
  font-size: 13px;
  color: #666;
  flex: 1;
  line-height: 1.5;
}

.preview-tip {
  display: flex;
  align-items: center;
  background-color: rgba(126, 48, 225, 0.05);
  border-radius: 8px;
  padding: 12px;
}

.tip-icon {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #7E30E1;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-style: italic;
  margin-right: 10px;
}

.tip-text {
  font-size: 12px;
  color: #7E30E1;
  flex: 1;
  line-height: 1.5;
}

/* 底部操作按钮 */
.footer-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px 15px;
  padding-bottom: calc(10px + env(safe-area-inset-bottom));
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
  z-index: 99;
}

.btn-primary {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  border: none;
  border-radius: 25px;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-left: 10px;
  box-shadow: 0 5px 15px rgba(126, 48, 225, 0.2);
  transition: all 0.3s;
  
  &:active {
    transform: translateY(2px);
    box-shadow: 0 3px 8px rgba(126, 48, 225, 0.2);
  }
}

.btn-secondary {
  background-color: #F0F0F0;
  color: #666;
  border: none;
  border-radius: 25px;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-weight: 500;
  width: 120px;
  transition: all 0.3s;
  
  &:active {
    background-color: #E6E6E6;
  }
}

/* 开关容器 */
.switch-container {
  display: flex;
  align-items: center;
  height: 40px;
}

.price-regular {
  color: #FF6B00;
  text-decoration: line-through;
  font-size: 12px;
  margin-right: 8px;
}

.price-market {
  color: #999;
  text-decoration: line-through;
  font-size: 12px;
  margin-right: 8px;
}

.confirm-price-market {
  color: #999;
  text-decoration: line-through;
  font-size: 11px;
  margin-right: 8px;
  display: block;
}

.confirm-price-regular {
  color: #FF6B00;
  text-decoration: line-through;
  font-size: 11px;
  margin-right: 8px;
  display: block;
  margin-top: 2px;
}

/* 套餐拼团样式 */
.package-settings {
  margin-top: 15px;
  padding: 15px;
  background-color: #F8F9FC;
  border-radius: 10px;
}

.form-textarea {
  width: 100%;
  height: 80px;
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  padding: 10px;
  font-size: 14px;
  color: #333;
}

.package-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.package-item {
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  overflow: hidden;
}

.package-item-header {
  background: #F0F2F5;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.package-item-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.package-item-remove {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
}

.package-item-content {
  padding: 10px 15px;
}

.package-item-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.package-item-row:last-child {
  margin-bottom: 0;
}

.package-item-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.package-item-input {
  flex: 1;
  height: 36px;
  background: #F5F7FA;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  padding: 0 10px;
  font-size: 14px;
  color: #333;
}

.add-package-item {
  height: 44px;
  background: rgba(144, 64, 255, 0.1);
  border: 1px dashed #9040FF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.add-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #9040FF;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 8px;
}

.add-text {
  font-size: 14px;
  color: #9040FF;
}

.number-picker.small {
  height: 32px;
}

.number-picker.small .number-btn {
  width: 24px;
  height: 24px;
}

.number-picker.small .number-input {
  width: 40px;
  height: 24px;
  font-size: 13px;
}

/* 套餐预览样式 */
.package-preview {
  width: 100px;
  height: 100px;
  background: #F5F7FA;
  border-radius: 8px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  overflow: hidden;
}

.package-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: #9040FF;
  color: #FFFFFF;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 0 8px 0;
}

.package-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  text-align: center;
  padding: 0 5px;
}

.package-items-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.package-item-text {
  font-size: 10px;
  color: #666;
  line-height: 1.4;
}

.confirm-package {
  margin-top: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.package-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #E5E7EB;
}

.package-items-list {
  margin-bottom: 15px;
}

.package-item-detail {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #F5F7FA;
}

.package-item-detail:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.package-item-number {
  width: 24px;
  height: 24px;
  background: #F0F2F5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-right: 10px;
}

.package-item-info {
  flex: 1;
}

.package-item-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.package-item-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.package-item-price {
  font-size: 12px;
  color: #FF3B30;
}

.package-item-quantity {
  font-size: 12px;
  color: #999;
}

.package-summary {
  background: #F8FAFC;
  border-radius: 8px;
  padding: 10px 15px;
}

.package-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.package-summary-row:last-child {
  margin-bottom: 0;
}

.package-summary-row.highlight {
  margin: 10px 0;
  padding: 8px 0;
  border-top: 1px dashed #E5E7EB;
  border-bottom: 1px dashed #E5E7EB;
}

.summary-label {
  font-size: 13px;
  color: #666;
}

.summary-value {
  font-size: 13px;
  color: #333;
  font-weight: 600;
}

.summary-value.save {
  color: #FF3B30;
}

.confirm-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

/* 支付和核销设置 */
.confirm-payment-verification {
  margin-top: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.setting-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #7E30E1;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.setting-info {
  flex: 1;
}

.setting-label {
  font-size: 14px;
  color: #333;
}

.setting-value {
  font-size: 14px;
  color: #666;
}

/* 预览卡片样式 */
.preview-card {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

.preview-header {
  padding: 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.preview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.preview-dates {
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
  display: block;
}

.preview-content {
  padding: 20rpx;
}

.preview-products {
  margin-bottom: 20rpx;
}

.preview-product-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.preview-product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price {
  display: flex;
  align-items: baseline;
}

.group-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF6B6B;
}

.original-price {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
  margin-left: 12rpx;
}

.group-rules {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.rule-item {
  display: flex;
  justify-content: space-between;
  padding: 8rpx 0;
}

.rule-label {
  font-size: 26rpx;
  color: #666666;
}

.rule-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

/* 表单操作区 */
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.btn-secondary, .btn-primary {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-secondary {
  background-color: #F5F5F5;
  color: #666666;
  margin-right: 20rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #6B0FBE, #9013FE);
  color: #FFFFFF;
}
</style>
