package com.cizhou.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 刷新Token请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "刷新Token请求")
public class RefreshTokenRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "刷新Token")
    @NotBlank(message = "刷新Token不能为空")
    private String refreshToken;
}
