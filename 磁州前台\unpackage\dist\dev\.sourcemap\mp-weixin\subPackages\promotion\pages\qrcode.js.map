{"version": 3, "file": "qrcode.js", "sources": ["subPackages/promotion/pages/qrcode.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccHJvbW90aW9uXHBhZ2VzXHFyY29kZS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"qrcode-page\">\r\n    <!-- 导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">推广二维码</text>\r\n    </view>\r\n    \r\n    <!-- 二维码容器 -->\r\n    <view class=\"qrcode-container\">\r\n      <view class=\"qrcode-card\">\r\n        <view class=\"qrcode-header\">\r\n          <text class=\"qrcode-title\">{{ title }}</text>\r\n          <text class=\"qrcode-subtitle\">扫码查看详情</text>\r\n        </view>\r\n        \r\n        <view class=\"qrcode-content\">\r\n          <image v-if=\"qrcodePath\" class=\"qrcode-image\" :src=\"qrcodePath\" mode=\"aspectFit\"></image>\r\n          <view v-else class=\"qrcode-loading\">\r\n            <view class=\"loading-spinner\"></view>\r\n            <text class=\"loading-text\">生成中...</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"qrcode-footer\">\r\n          <text class=\"qrcode-tip\">长按保存图片或分享</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 操作按钮 -->\r\n    <view class=\"action-buttons\">\r\n      <button class=\"action-btn save-btn\" @click=\"saveQrcode\">保存到相册</button>\r\n      <button class=\"action-btn share-btn\" @click=\"shareQrcode\">分享</button>\r\n    </view>\r\n    \r\n    <!-- 推广提示 -->\r\n    <view class=\"promotion-tips\">\r\n      <text class=\"tips-title\">使用说明</text>\r\n      <text class=\"tips-content\">1. 保存二维码图片到手机相册</text>\r\n      <text class=\"tips-content\">2. 将二维码分享到微信、QQ等社交平台</text>\r\n      <text class=\"tips-content\">3. 他人扫码后将直接进入内容详情页</text>\r\n      <text class=\"tips-content\">4. 推广成功后可获得相应奖励</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\nimport promotionService from '@/utils/promotionService';\r\n\r\n// 页面参数\r\nconst contentType = ref('');\r\nconst contentId = ref('');\r\nconst title = ref('磁州生活网');\r\n\r\n// 二维码路径\r\nconst qrcodePath = ref('');\r\n\r\n// 当前用户ID\r\nconst userId = ref('');\r\n\r\n// 页面加载\r\nonMounted(() => {\r\n  // 获取页面参数\r\n  const pages = getCurrentPages();\r\n  const currentPage = pages[pages.length - 1];\r\n  const options = currentPage.options || {};\r\n  \r\n  contentType.value = options.type || '';\r\n  contentId.value = options.id || '';\r\n  title.value = options.title || '磁州生活网';\r\n  \r\n  // 获取用户信息\r\n  const userInfo = uni.getStorageSync('userInfo');\r\n  if (userInfo && userInfo.userId) {\r\n    userId.value = userInfo.userId;\r\n  }\r\n  \r\n  // 生成二维码\r\n  generateQrcode();\r\n});\r\n\r\n// 生成二维码\r\nconst generateQrcode = async () => {\r\n  if (!contentType.value || !contentId.value) {\r\n    uni.showToast({\r\n      title: '参数错误',\r\n      icon: 'none'\r\n    });\r\n    setTimeout(() => {\r\n      goBack();\r\n    }, 1500);\r\n    return;\r\n  }\r\n  \r\n  try {\r\n    // 生成二维码\r\n    qrcodePath.value = await promotionService.generateQrcode(\r\n      contentType.value,\r\n      contentId.value,\r\n      userId.value\r\n    );\r\n    \r\n    // 记录推广数据\r\n    promotionService.recordPromotion(contentType.value, contentId.value, 'qrcode');\r\n  } catch (err) {\r\n    console.error('生成二维码失败', err);\r\n    uni.showToast({\r\n      title: '生成二维码失败',\r\n      icon: 'none'\r\n    });\r\n  }\r\n};\r\n\r\n// 保存二维码到相册\r\nconst saveQrcode = async () => {\r\n  if (!qrcodePath.value) {\r\n    uni.showToast({\r\n      title: '二维码未生成',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  try {\r\n    await promotionService.savePosterToAlbum(qrcodePath.value);\r\n  } catch (err) {\r\n    console.error('保存二维码失败', err);\r\n  }\r\n};\r\n\r\n// 分享二维码\r\nconst shareQrcode = () => {\r\n  if (!qrcodePath.value) {\r\n    uni.showToast({\r\n      title: '二维码未生成',\r\n      icon: 'none'\r\n    });\r\n    return;\r\n  }\r\n  \r\n  uni.showShareMenu({\r\n    withShareTicket: true,\r\n    menus: ['shareAppMessage', 'shareTimeline']\r\n  });\r\n};\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.qrcode-page {\r\n  min-height: 100vh;\r\n  background-color: #f8f8f8;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #3846cd, #2c3aa0);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 10px rgba(56, 70, 205, 0.15);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.qrcode-container {\r\n  margin: 30rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.qrcode-card {\r\n  width: 100%;\r\n  background-color: #fff;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  padding: 30rpx;\r\n}\r\n\r\n.qrcode-header {\r\n  text-align: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.qrcode-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.qrcode-subtitle {\r\n  font-size: 26rpx;\r\n  color: #999;\r\n  display: block;\r\n}\r\n\r\n.qrcode-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.qrcode-image {\r\n  width: 400rpx;\r\n  height: 400rpx;\r\n}\r\n\r\n.qrcode-loading {\r\n  width: 400rpx;\r\n  height: 400rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border: 4rpx solid #f3f3f3;\r\n  border-top: 4rpx solid #3846cd;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.loading-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.qrcode-footer {\r\n  text-align: center;\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.qrcode-tip {\r\n  font-size: 26rpx;\r\n  color: #999;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n  margin-top: 40rpx;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  text-align: center;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  margin: 0 20rpx;\r\n}\r\n\r\n.save-btn {\r\n  background: linear-gradient(135deg, #3846cd, #2c3aa0);\r\n  color: #fff;\r\n  border: none;\r\n}\r\n\r\n.share-btn {\r\n  background-color: #fff;\r\n  color: #3846cd;\r\n  border: 1px solid #3846cd;\r\n}\r\n\r\n.promotion-tips {\r\n  margin: 40rpx 30rpx 30rpx;\r\n  border-radius: 16rpx;\r\n  background-color: #fff;\r\n  padding: 20rpx;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.tips-title {\r\n  font-size: 30rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.tips-content {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  line-height: 1.6;\r\n  display: block;\r\n  margin-bottom: 10rpx;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/promotion/pages/qrcode.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "uni", "promotionService"], "mappings": ";;;;;;AAsDA,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,YAAYA,cAAAA,IAAI,EAAE;AACxB,UAAM,QAAQA,cAAAA,IAAI,OAAO;AAGzB,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,SAASA,cAAAA,IAAI,EAAE;AAGrBC,kBAAAA,UAAU,MAAM;AAEd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAEvC,kBAAY,QAAQ,QAAQ,QAAQ;AACpC,gBAAU,QAAQ,QAAQ,MAAM;AAChC,YAAM,QAAQ,QAAQ,SAAS;AAG/B,YAAM,WAAWC,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,YAAY,SAAS,QAAQ;AAC/B,eAAO,QAAQ,SAAS;AAAA,MACzB;AAGD;IACF,CAAC;AAGD,UAAM,iBAAiB,YAAY;AACjC,UAAI,CAAC,YAAY,SAAS,CAAC,UAAU,OAAO;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,mBAAW,MAAM;AACf;QACD,GAAE,IAAI;AACP;AAAA,MACD;AAED,UAAI;AAEF,mBAAW,QAAQ,MAAMC,uBAAAA,iBAAiB;AAAA,UACxC,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,OAAO;AAAA,QACb;AAGIA,+BAAgB,iBAAC,gBAAgB,YAAY,OAAO,UAAU,OAAO,QAAQ;AAAA,MAC9E,SAAQ,KAAK;AACZD,sBAAA,MAAA,MAAA,SAAA,iDAAc,WAAW,GAAG;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,aAAa,YAAY;AAC7B,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI;AACF,cAAMC,wCAAiB,kBAAkB,WAAW,KAAK;AAAA,MAC1D,SAAQ,KAAK;AACZD,sBAAA,MAAA,MAAA,SAAA,iDAAc,WAAW,GAAG;AAAA,MAC7B;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,cAAc;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,MAC9C,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,MAAM;AACnBA,oBAAG,MAAC,aAAY;AAAA,IAClB;;;;;;;;;;;;;;;;ACxJA,GAAG,WAAW,eAAe;"}