@echo off
chcp 65001 >nul

echo.
echo ========================================
echo   磁州生活网纯前端演示版
echo ========================================
echo.

echo 此方案仅启动前端，使用模拟数据进行演示
echo 适合快速查看界面效果，无需后端服务
echo.

echo ========================================
echo 检查环境
echo ========================================
echo.

echo 检查 Node.js...
where node >nul 2>&1
if errorlevel 1 (
    echo X Node.js 未安装
    echo   请访问 https://nodejs.org/ 下载安装
    pause
    exit /b 1
) else (
    echo √ Node.js 已安装
    node --version
)

echo.
echo 检查 npm...
where npm >nul 2>&1
if errorlevel 1 (
    echo X npm 未安装
    pause
    exit /b 1
) else (
    echo √ npm 已安装
    npm --version
)

echo.
echo ========================================
echo 启动前端服务
echo ========================================
echo.

cd frontend

echo 检查依赖...
if not exist "node_modules" (
    echo 首次运行，安装依赖...
    echo 设置npm镜像源...
    npm config set registry https://registry.npmmirror.com/
    
    echo 安装依赖包...
    npm install
    
    if errorlevel 1 (
        echo X 依赖安装失败
        echo 尝试清理缓存...
        npm cache clean --force
        npm install
        
        if errorlevel 1 (
            echo X 依赖安装仍然失败
            echo 请检查网络连接或手动安装
            pause
            exit /b 1
        )
    )
    
    echo √ 依赖安装完成
) else (
    echo √ 依赖已存在
)

echo.
echo 启动开发服务器...
echo.

echo ========================================
echo 前端演示版启动完成
echo ========================================
echo.

echo 访问地址: http://localhost:3000
echo.

echo 演示账号:
echo   用户名: admin
echo   密码:   admin123
echo.

echo 注意事项:
echo   - 此为纯前端演示版
echo   - 使用模拟数据，无法保存真实数据
echo   - 如需完整功能，请启动后端服务
echo.

echo 按 Ctrl+C 停止服务
echo.

REM 启动前端开发服务器
npm run dev

echo.
echo 前端服务已停止
pause
