"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  __name: "points",
  setup(__props, { expose: __expose }) {
    const statusBarHeight = common_vendor.ref(20);
    const userPoints = common_vendor.ref(1280);
    const isTodaySigned = common_vendor.ref(false);
    const continuousDays = common_vendor.ref(3);
    const calendarDays = common_vendor.ref([]);
    const showRulesPopup = common_vendor.ref(false);
    const _data = common_vendor.reactive({
      _pendingShareTask: null,
      _pendingCommentTask: null,
      _pendingProfileTask: null,
      _categoryViewInterval: null
    });
    const dailyTasks = common_vendor.ref([
      {
        name: "浏览同城团购",
        description: "浏览社区团购页面，每次+2分",
        icon: "/static/images/tabbar/阿团购.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 2,
        progress: 1,
        target: 5,
        completed: false
      },
      {
        name: "浏览商家",
        description: "浏览商家详情页，每次+1分",
        icon: "/static/images/tabbar/阿商家.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 1,
        progress: 1,
        target: 5,
        completed: false
      },
      {
        name: "浏览商家活动",
        description: "浏览商家活动页面，每次+1分",
        icon: "/static/images/tabbar/阿商家.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 1,
        progress: 1,
        target: 5,
        completed: false
      },
      {
        name: "浏览分类信息",
        description: "浏览招聘、兼职、转让等分类信息",
        icon: "/static/images/tabbar/阿分类.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 1,
        progress: 1,
        target: 5,
        completed: false
      },
      {
        name: "完成同城团购",
        description: "参与并完成一次同城团购订单",
        icon: "/static/images/tabbar/阿团购.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 20,
        progress: 0,
        target: 1,
        completed: false
      },
      {
        name: "发布信息",
        description: "发布一条同城信息",
        icon: "/static/images/tabbar/阿发布.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 2,
        progress: 0,
        target: 1,
        completed: false
      },
      {
        name: "分享小程序",
        description: "分享小程序到群聊",
        icon: "/static/images/tabbar/阿分享.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 5,
        progress: 0,
        target: 1,
        completed: false
      },
      {
        name: "参与本地活动",
        description: "浏览活动页并报名参加",
        icon: "/static/images/tabbar/阿活动.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 5,
        progress: 0,
        target: 1,
        completed: false
      },
      {
        name: "评论互动",
        description: "发表评论",
        icon: "/static/images/tabbar/阿评论.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 1,
        progress: 1,
        target: 1,
        completed: false
      },
      {
        name: "完善个人资料",
        description: "补充完善个人信息",
        icon: "/static/images/tabbar/阿资料.png",
        iconBg: "rgba(22, 119, 255, 0.08)",
        points: 10,
        progress: 0,
        target: 1,
        completed: false
      }
    ]);
    const contentMarginTop = common_vendor.computed(() => {
      return statusBarHeight.value + 88 + "px";
    });
    common_vendor.onMounted(() => {
      common_vendor.index.setNavigationBarTitle({
        title: "每日签到"
      });
      try {
        const windowInfo = common_vendor.index.getWindowInfo();
        statusBarHeight.value = windowInfo.statusBarHeight || 20;
        common_vendor.index.setStorageSync("statusBarHeight", statusBarHeight.value);
      } catch (e) {
        common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:316", "获取系统信息失败:", e);
        statusBarHeight.value = 20;
      }
      generateCalendarDays();
      getUserPointsData();
    });
    common_vendor.onHide(() => {
      if (_data._categoryViewInterval) {
        clearInterval(_data._categoryViewInterval);
        _data._categoryViewInterval = null;
      }
    });
    common_vendor.onUnmounted(() => {
      if (_data._categoryViewInterval) {
        clearInterval(_data._categoryViewInterval);
        _data._categoryViewInterval = null;
      }
    });
    const generateCalendarDays = () => {
      const now = /* @__PURE__ */ new Date();
      const currentDay = now.getDate();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();
      const firstDayOfWeek = new Date(currentYear, currentMonth, 1).getDay();
      const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
      const days = [];
      for (let i = 0; i < firstDayOfWeek; i++) {
        days.push({
          day: "",
          signed: false,
          isToday: false,
          isFuture: false,
          points: 0,
          empty: true
        });
      }
      for (let i = 1; i <= daysInMonth; i++) {
        days.push({
          day: i,
          signed: i < currentDay,
          // 假设当天之前的都已签到
          isToday: i === currentDay,
          isFuture: i > currentDay,
          points: getSignPoints(),
          empty: false
        });
      }
      const totalDays = firstDayOfWeek + daysInMonth;
      const remainingCells = totalDays % 7 === 0 ? 0 : 7 - totalDays % 7;
      for (let i = 0; i < remainingCells; i++) {
        days.push({
          day: "",
          signed: false,
          isToday: false,
          isFuture: false,
          points: 0,
          empty: true
        });
      }
      calendarDays.value = days;
      const todayObj = calendarDays.value.find((day) => day.isToday);
      if (todayObj) {
        isTodaySigned.value = todayObj.signed;
      }
    };
    const getSignPoints = (day) => {
      return 2;
    };
    const getUserPointsData = () => {
      setTimeout(() => {
      }, 500);
    };
    const signIn = () => {
      if (isTodaySigned.value)
        return;
      common_vendor.index.showLoading({ title: "签到中..." });
      setTimeout(() => {
        const todayIndex = calendarDays.value.findIndex((day) => day.isToday);
        if (todayIndex !== -1) {
          calendarDays.value[todayIndex].signed = true;
          userPoints.value += calendarDays.value[todayIndex].points;
          isTodaySigned.value = true;
          continuousDays.value += 1;
        }
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "签到成功 +" + calendarDays.value[todayIndex].points + "积分",
          icon: "none"
        });
      }, 800);
    };
    const handleTask = (index) => {
      const task = dailyTasks.value[index];
      if (task.completed) {
        common_vendor.index.showToast({
          title: "任务已完成",
          icon: "none"
        });
        return;
      }
      switch (task.name) {
        case "浏览商家":
          common_vendor.index.switchTab({
            url: "/pages/business/business",
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:468", "成功跳转到商家页面");
              if (task.progress >= task.target) {
                userPoints.value += task.points;
                common_vendor.index.showToast({
                  title: "已浏览商家 +1积分",
                  icon: "none",
                  duration: 2e3
                });
              } else {
                updateTaskProgress(index, 1);
                common_vendor.index.showToast({
                  title: "已浏览商家 +1积分",
                  icon: "none",
                  duration: 2e3
                });
              }
            },
            fail: () => {
              common_vendor.index.navigateTo({
                url: "/pages/business/business",
                success: () => {
                  common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:493", "成功跳转到商家页面(navigateTo)");
                  if (task.progress >= task.target) {
                    userPoints.value += task.points;
                    common_vendor.index.showToast({
                      title: "已浏览商家 +1积分",
                      icon: "none",
                      duration: 2e3
                    });
                  } else {
                    updateTaskProgress(index, 1);
                    common_vendor.index.showToast({
                      title: "已浏览商家 +1积分",
                      icon: "none",
                      duration: 2e3
                    });
                  }
                },
                fail: (err) => {
                  common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:514", "跳转失败:", err);
                  common_vendor.index.showToast({
                    title: "页面跳转失败",
                    icon: "none"
                  });
                }
              });
            }
          });
          break;
        case "浏览商家活动":
          common_vendor.index.navigateTo({
            url: "/pages/business/activity",
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:529", "成功跳转到商家活动页面");
              if (task.progress >= task.target) {
                userPoints.value += task.points;
                common_vendor.index.showToast({
                  title: "已浏览商家活动 +1积分",
                  icon: "none",
                  duration: 2e3
                });
              } else {
                updateTaskProgress(index, 1);
                common_vendor.index.showToast({
                  title: "已浏览商家活动 +1积分",
                  icon: "none",
                  duration: 2e3
                });
              }
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:550", "商家活动页面跳转失败:", err);
              common_vendor.index.switchTab({
                url: "/pages/business/business",
                success: () => {
                  common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:555", "跳转到商家页面替代");
                  common_vendor.index.showToast({
                    title: "商家活动页面开发中，请浏览商家页面完成任务",
                    icon: "none",
                    duration: 2e3
                  });
                  if (task.progress >= task.target) {
                    userPoints.value += task.points;
                  } else {
                    updateTaskProgress(index, 1);
                  }
                },
                fail: () => {
                  common_vendor.index.showToast({
                    title: "页面跳转失败",
                    icon: "none"
                  });
                }
              });
            }
          });
          break;
        case "浏览分类信息":
          common_vendor.index.switchTab({
            url: "/pages/index/index",
            success: () => {
              updateTaskProgress(index);
              setTimeout(() => {
                const pages = getCurrentPages();
                const indexPage = pages[pages.length - 1];
                if (indexPage && indexPage.$vm && typeof indexPage.$vm.scrollToInfoSection === "function") {
                  indexPage.$vm.scrollToInfoSection();
                }
              }, 500);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:598", "首页跳转失败:", err);
            }
          });
          break;
        case "发布信息":
          common_vendor.index.switchTab({
            url: "/pages/publish/publish",
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:606", "成功跳转到发布页面");
              updateTaskProgress(index);
            },
            fail: () => {
              common_vendor.index.navigateTo({
                url: "/pages/publish/publish",
                success: () => {
                  common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:613", "成功跳转到发布页面(navigateTo)");
                  updateTaskProgress(index);
                },
                fail: (err) => {
                  common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:617", "跳转失败:", err);
                  common_vendor.index.showToast({
                    title: "页面跳转失败",
                    icon: "none"
                  });
                }
              });
            }
          });
          break;
        case "分享小程序":
          common_vendor.index.showShareMenu({
            withShareTicket: true,
            // 获取分享票据，用于验证是否分享到群
            menus: ["shareAppMessage", "shareTimeline"],
            // 显示分享到好友和朋友圈菜单
            success: () => {
              common_vendor.index.showToast({
                title: "请点击右上角分享到群聊",
                icon: "none",
                duration: 3e3
              });
              _data._pendingShareTask = index;
              setTimeout(() => {
                if (_data._pendingShareTask === index) {
                  _data._pendingShareTask = null;
                  common_vendor.index.showToast({
                    title: "分享超时，请重试",
                    icon: "none"
                  });
                }
              }, 1e4);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:654", "分享菜单显示失败:", err);
              common_vendor.index.showToast({
                title: "分享功能不可用",
                icon: "none"
              });
            }
          });
          break;
        case "浏览同城团购":
          common_vendor.index.navigateTo({
            url: "/pages/community/shopping",
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:667", "成功跳转到社区团购页面");
              if (task.progress >= task.target) {
                userPoints.value += task.points;
                common_vendor.index.showToast({
                  title: "已浏览同城团购 +2积分",
                  icon: "none",
                  duration: 2e3
                });
              } else {
                updateTaskProgress(index, 1);
                common_vendor.index.showToast({
                  title: "已浏览同城团购 +2积分",
                  icon: "none",
                  duration: 2e3
                });
              }
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:688", "社区团购页面跳转失败:", err);
              common_vendor.index.switchTab({
                url: "/pages/business/business",
                success: () => {
                  common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:693", "跳转到商家页面替代");
                  common_vendor.index.showToast({
                    title: "同城团购正在开发中，请浏览商家页面完成任务",
                    icon: "none",
                    duration: 2e3
                  });
                  if (task.progress >= task.target) {
                    userPoints.value += task.points;
                  } else {
                    updateTaskProgress(index, 1);
                  }
                },
                fail: () => {
                  common_vendor.index.showToast({
                    title: "页面跳转失败",
                    icon: "none"
                  });
                }
              });
            }
          });
          break;
        case "完成同城团购":
          common_vendor.index.navigateTo({
            url: "/pages/community/shopping",
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:724", "成功跳转到社区团购页面");
              common_vendor.index.showToast({
                title: "请完成一笔团购订单获取积分",
                icon: "none",
                duration: 3e3
              });
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:732", "社区团购页面跳转失败:", err);
              common_vendor.index.switchTab({
                url: "/pages/business/business",
                success: () => {
                  common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:737", "跳转到商家页面替代");
                  common_vendor.index.showToast({
                    title: "同城团购功能开发中，请稍后再试",
                    icon: "none",
                    duration: 2e3
                  });
                },
                fail: () => {
                  common_vendor.index.showToast({
                    title: "页面跳转失败",
                    icon: "none"
                  });
                }
              });
            }
          });
          break;
        case "参与本地活动":
          common_vendor.index.navigateTo({
            url: "/pages/activity/list",
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:758", "成功跳转到活动列表页");
              updateTaskProgress(index);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:762", "活动列表跳转失败:", err);
              common_vendor.index.showToast({
                title: "活动页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
        case "评论互动":
          common_vendor.index.switchTab({
            url: "/pages/index/index",
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:774", "成功跳转到首页");
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "请在内容下方评论",
                  icon: "none",
                  duration: 2e3
                });
              }, 500);
              _data._pendingCommentTask = index;
              setTimeout(() => {
                if (_data._pendingCommentTask === index) {
                  updateTaskProgress(index);
                  _data._pendingCommentTask = null;
                }
              }, 1e4);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:796", "首页跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
          break;
        case "完善个人资料":
          common_vendor.index.navigateTo({
            url: "/subPackages/checkin/pages/profile",
            success: () => {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:809", "成功跳转到个人资料页");
              _data._pendingProfileTask = index;
              setTimeout(() => {
                if (_data._pendingProfileTask === index) {
                  updateTaskProgress(index);
                  _data._pendingProfileTask = null;
                }
              }, 5e3);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:823", "个人资料页跳转失败(相对路径):", err);
              common_vendor.index.navigateTo({
                url: "/pages/new-points/profile",
                success: () => {
                  common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:828", "成功跳转到个人资料页(绝对路径)");
                  _data._pendingProfileTask = index;
                  setTimeout(() => {
                    if (_data._pendingProfileTask === index) {
                      updateTaskProgress(index);
                      _data._pendingProfileTask = null;
                    }
                  }, 5e3);
                },
                fail: (e) => {
                  common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:841", "个人资料页跳转失败(绝对路径):", e);
                  common_vendor.index.showToast({
                    title: "个人资料页面不存在",
                    icon: "none"
                  });
                }
              });
            }
          });
          break;
        default:
          common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:852", "未知任务类型:", task.name);
          common_vendor.index.showToast({
            title: "任务类型未定义",
            icon: "none"
          });
          break;
      }
    };
    const updateTaskProgress = (index, incrementValue = 1) => {
      const task = dailyTasks.value[index];
      if (task.progress < task.target) {
        const newProgress = Math.min(task.progress + incrementValue, task.target);
        dailyTasks.value[index].progress = newProgress;
        if (newProgress >= task.target) {
          dailyTasks.value[index].completed = true;
          userPoints.value += task.points;
        }
      }
    };
    const navigateTo = (url, successCallback) => {
      if (url.startsWith("/pages/new-points/")) {
        const relativePath = url.replace("/pages/new-points/", "./");
        common_vendor.index.navigateTo({
          url: relativePath,
          success: () => {
            if (successCallback && typeof successCallback === "function") {
              successCallback();
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:887", "导航失败:", err);
            common_vendor.index.navigateTo({
              url,
              success: successCallback,
              fail: (e) => {
                common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:893", "绝对路径导航也失败:", e);
                if (successCallback && typeof successCallback === "function") {
                  successCallback();
                }
                common_vendor.index.showToast({
                  title: "已增加任务进度",
                  icon: "none"
                });
              }
            });
          }
        });
      } else if (url.startsWith("/subPackages/checkin/pages/")) {
        const relativePath = url.replace("/subPackages/checkin/pages/", "./");
        common_vendor.index.navigateTo({
          url: relativePath,
          success: () => {
            if (successCallback && typeof successCallback === "function") {
              successCallback();
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:916", "导航失败:", err);
            common_vendor.index.navigateTo({
              url,
              success: successCallback,
              fail: (e) => {
                common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:922", "绝对路径导航也失败:", e);
                if (successCallback && typeof successCallback === "function") {
                  successCallback();
                }
                common_vendor.index.showToast({
                  title: "已增加任务进度",
                  icon: "none"
                });
              }
            });
          }
        });
      } else {
        common_vendor.index.navigateTo({
          url,
          success: successCallback,
          fail: (err) => {
            common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:941", "导航失败:", err);
            if (successCallback && typeof successCallback === "function") {
              successCallback();
            }
            common_vendor.index.showToast({
              title: "已增加任务进度",
              icon: "none"
            });
          }
        });
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showPointsRules = () => {
      showRulesPopup.value = true;
    };
    const closePointsRules = () => {
      showRulesPopup.value = false;
    };
    __expose({
      onShareAppMessage(res) {
        const shareData = {
          title: "同城小程序 - 每日签到赚积分",
          path: "/subPackages/checkin/pages/points",
          imageUrl: "/static/images/share-cover.png",
          success: (res2) => {
            if (res2.shareTickets && res2.shareTickets.length > 0) {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:981", "成功分享到群聊");
              if (_data._pendingShareTask !== null && _data._pendingShareTask !== void 0) {
                const taskIndex = _data._pendingShareTask;
                _data._pendingShareTask = null;
                updateTaskProgress(taskIndex);
                common_vendor.index.showToast({
                  title: "群分享成功，获得积分！",
                  icon: "success"
                });
              }
            } else {
              common_vendor.index.__f__("log", "at subPackages/checkin/pages/points.vue:996", "分享成功，但不是分享到群");
              common_vendor.index.showToast({
                title: "请分享到群聊才能获得积分",
                icon: "none"
              });
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at subPackages/checkin/pages/points.vue:1004", "分享失败:", err);
          }
        };
        return shareData;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: common_assets._imports_0$7,
        c: common_vendor.o(goBack),
        d: statusBarHeight.value + "px",
        e: common_vendor.t(userPoints.value),
        f: common_vendor.o(($event) => navigateTo("/subPackages/checkin/pages/points-detail")),
        g: common_vendor.o(($event) => navigateTo("/subPackages/checkin/pages/points-mall")),
        h: common_assets._imports_1$44,
        i: common_vendor.o(showPointsRules),
        j: common_assets._imports_2$39,
        k: common_vendor.o(($event) => navigateTo("./points-rank")),
        l: common_vendor.f(["日", "一", "二", "三", "四", "五", "六"], (day, k0, i0) => {
          return {
            a: common_vendor.t(day),
            b: day
          };
        }),
        m: common_vendor.f(calendarDays.value, (day, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(day.day),
            b: day.signed
          }, day.signed ? {
            c: common_vendor.t(day.points)
          } : day.isToday && !day.signed ? {} : {}, {
            d: day.isToday && !day.signed,
            e: index,
            f: day.signed ? 1 : "",
            g: day.isToday ? 1 : "",
            h: day.isFuture ? 1 : "",
            i: !day.day ? 1 : ""
          });
        }),
        n: common_vendor.t(isTodaySigned.value ? "今日已签到" : "立即签到"),
        o: isTodaySigned.value ? 1 : "",
        p: isTodaySigned.value,
        q: common_vendor.o(signIn),
        r: common_vendor.t(continuousDays.value),
        s: common_vendor.f(dailyTasks.value, (task, index, i0) => {
          return common_vendor.e({
            a: task.icon,
            b: task.iconBg,
            c: common_vendor.t(task.name),
            d: common_vendor.t(task.description),
            e: common_vendor.t(task.points),
            f: task.completed
          }, task.completed ? {} : task.progress > 0 ? {
            h: common_vendor.t(task.progress),
            i: common_vendor.t(task.target)
          } : {}, {
            g: task.progress > 0,
            j: task.completed ? 1 : "",
            k: task.progress < task.target && task.progress > 0 ? 1 : "",
            l: common_vendor.o(($event) => handleTask(index), index),
            m: task.progress < task.target && task.progress > 0
          }, task.progress < task.target && task.progress > 0 ? {
            n: task.progress / task.target * 100 + "%"
          } : {}, {
            o: index
          });
        }),
        t: contentMarginTop.value,
        v: showRulesPopup.value
      }, showRulesPopup.value ? {
        w: common_vendor.o(closePointsRules)
      } : {}, {
        x: showRulesPopup.value
      }, showRulesPopup.value ? {
        y: common_vendor.o(closePointsRules)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-13a58514"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/checkin/pages/points.js.map
