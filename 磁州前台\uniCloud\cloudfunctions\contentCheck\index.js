'use strict';

// 引入阿里云内容安全SDK
const Core = require('@alicloud/pop-core');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const { getOssFile } = require('./utils');

// 阿里云内容安全配置
const config = {
  accessKeyId: process.env.ALI_CONTENT_ACCESS_KEY_ID || 'YOUR_ACCESS_KEY_ID',
  accessKeySecret: process.env.ALI_CONTENT_ACCESS_KEY_SECRET || 'YOUR_ACCESS_KEY_SECRET',
  endpoint: 'https://green.cn-shanghai.aliyuncs.com',
  apiVersion: '2018-05-09'
};

// 创建阿里云内容安全客户端
const client = new Core({
  accessKeyId: config.accessKeyId,
  accessKeySecret: config.accessKeySecret,
  endpoint: config.endpoint,
  apiVersion: config.apiVersion
});

// 违规类型映射
const VIOLATION_TYPES = {
  NORMAL: 0,       // 正常内容
  SENSITIVE: 1,    // 敏感内容
  POLITICAL: 2,    // 政治敏感
  PORN: 3,         // 色情
  ABUSE: 4,        // 辱骂
  VIOLENCE: 5,     // 暴力
  FRAUD: 6,        // 诈骗
  ILLEGAL: 7,      // 其他违法
  AD: 8,           // 广告
  SPAM: 9          // 垃圾信息
};

// 阿里云场景码映射到我们的违规类型
const SCENE_MAPPING = {
  'porn': VIOLATION_TYPES.PORN,
  'terrorism': VIOLATION_TYPES.VIOLENCE,
  'ad': VIOLATION_TYPES.AD,
  'live': VIOLATION_TYPES.NORMAL,
  'qrcode': VIOLATION_TYPES.AD,
  'politics': VIOLATION_TYPES.POLITICAL,
  'abuse': VIOLATION_TYPES.ABUSE,
  'flood': VIOLATION_TYPES.SPAM,
  'contraband': VIOLATION_TYPES.ILLEGAL,
  'customized': VIOLATION_TYPES.SENSITIVE
};

/**
 * 审核文本内容
 * @param {String} text 文本内容
 * @returns {Promise} 审核结果
 */
async function checkTextContent(text) {
  if (!text || text.trim() === '') {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容正常'
    };
  }
  
  // 开发环境下模拟检测
  if (process.env.NODE_ENV === 'development') {
    return mockTextCheck(text);
  }
  
  try {
    // 构建请求参数
    const params = {
      bizType: 'default',
      scenes: ['antispam'],
      tasks: [{
        dataId: `text_${Date.now()}`,
        content: text
      }]
    };
    
    // 发起请求
    const result = await client.request('TextScan', params, {
      method: 'POST',
      formatParams: false,
      contentType: 'application/json'
    });
    
    // 请求成功，处理结果
    if (result && result.code === 200) {
      const taskResults = result.data[0].results;
      
      // 如果没有审核结果，默认通过
      if (!taskResults || taskResults.length === 0) {
        return {
          pass: true,
          type: VIOLATION_TYPES.NORMAL,
          message: '内容正常'
        };
      }
      
      // 检查是否存在不通过的结果
      for (const item of taskResults) {
        // 命中违规 (suggestion为block时表示违规)
        if (item.suggestion === 'block') {
          return {
            pass: false,
            type: SCENE_MAPPING[item.scene] || VIOLATION_TYPES.SENSITIVE,
            message: `内容违规: ${item.label}`,
            detail: item
          };
        } 
        // 需要人工审核 (suggestion为review时表示需要人工审核)
        else if (item.suggestion === 'review') {
          return {
            pass: true,
            type: SCENE_MAPPING[item.scene] || VIOLATION_TYPES.SENSITIVE,
            message: '内容需要人工审核',
            needReview: true,
            detail: item
          };
        }
      }
      
      // 通过审核
      return {
        pass: true,
        type: VIOLATION_TYPES.NORMAL,
        message: '内容正常'
      };
    } 
    
    // 请求失败，记录错误信息并默认通过
    console.error('文本审核API调用失败:', result);
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容审核服务异常，需人工审核',
      needReview: true
    };
  } catch (error) {
    console.error('文本审核异常:', error);
    // 出现异常时，默认放行，但标记需要人工审核
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容审核异常，需人工审核',
      needReview: true
    };
  }
}

/**
 * 审核图片内容
 * @param {String} imageUrl 图片URL
 * @returns {Promise} 审核结果
 */
async function checkImageContent(imageUrl) {
  if (!imageUrl) {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容正常'
    };
  }
  
  // 开发环境下模拟检测
  if (process.env.NODE_ENV === 'development') {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '开发环境图片默认通过'
    };
  }
  
  try {
    // 如果是OSS上的图片，需要确保有公开访问权限
    // 如果是非OSS图片，确保能被阿里云访问到
    const processedUrl = await processImageUrl(imageUrl);
    
    // 构建请求参数
    const params = {
      bizType: 'default',
      scenes: ['porn', 'terrorism', 'ad', 'qrcode', 'live'],
      tasks: [{
        dataId: `img_${Date.now()}`,
        url: processedUrl
      }]
    };
    
    // 发起请求
    const result = await client.request('ImageSyncScan', params, {
      method: 'POST',
      formatParams: false,
      contentType: 'application/json'
    });
    
    // 请求成功，处理结果
    if (result && result.code === 200) {
      const taskResults = result.data[0].results;
      
      // 如果没有审核结果，默认通过
      if (!taskResults || taskResults.length === 0) {
        return {
          pass: true,
          type: VIOLATION_TYPES.NORMAL,
          message: '图片内容正常',
          url: imageUrl
        };
      }
      
      // 检查是否存在不通过的结果
      for (const item of taskResults) {
        // 命中违规 (suggestion为block时表示违规)
        if (item.suggestion === 'block') {
          return {
            pass: false,
            type: SCENE_MAPPING[item.scene] || VIOLATION_TYPES.SENSITIVE,
            message: `图片内容违规: ${item.label}`,
            detail: item,
            url: imageUrl
          };
        } 
        // 需要人工审核 (suggestion为review时表示需要人工审核)
        else if (item.suggestion === 'review') {
          return {
            pass: true,
            type: SCENE_MAPPING[item.scene] || VIOLATION_TYPES.SENSITIVE,
            message: '图片内容需要人工审核',
            needReview: true,
            detail: item,
            url: imageUrl
          };
        }
      }
      
      // 通过审核
      return {
        pass: true,
        type: VIOLATION_TYPES.NORMAL,
        message: '图片内容正常',
        url: imageUrl
      };
    } 
    
    // 请求失败，记录错误信息并默认通过
    console.error('图片审核API调用失败:', result);
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '图片审核服务异常，需人工审核',
      needReview: true,
      url: imageUrl
    };
  } catch (error) {
    console.error('图片审核异常:', error);
    // 出现异常时，默认放行，但标记需要人工审核
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '图片审核异常，需人工审核',
      needReview: true,
      url: imageUrl
    };
  }
}

/**
 * 处理图片URL，确保阿里云内容安全服务能够访问到
 * @param {String} url 原始图片URL
 * @returns {Promise<String>} 处理后的图片URL
 */
async function processImageUrl(url) {
  // 如果是HTTP(S)链接，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // 如果是OSS存储的图片，获取其公开访问URL
  if (url.startsWith('cloud://') || url.startsWith('oss://')) {
    try {
      const publicUrl = await getOssFile(url);
      return publicUrl;
    } catch (error) {
      console.error('获取OSS图片URL失败:', error);
      throw new Error('图片地址处理失败');
    }
  }
  
  // 对于其他类型的URL，可能需要额外处理
  throw new Error('不支持的图片URL格式');
}

/**
 * 模拟文本检测（开发环境使用）
 * @param {String} text 文本内容
 * @returns {Object} 检测结果
 */
function mockTextCheck(text) {
  const sensitiveWords = [
    '赌博', '博彩', '色情', '暴力', '毒品', '违禁品', 
    '诈骗', '传销', '非法', '黄色', '办证', '枪支'
  ];
  
  // 检测敏感词
  for (const word of sensitiveWords) {
    if (text.includes(word)) {
      return {
        pass: false,
        type: VIOLATION_TYPES.SENSITIVE,
        message: `内容包含违规词汇: ${word}`,
        detail: { label: '敏感词', word }
      };
    }
  }
  
  // 检测广告特征
  if (
    (text.includes('优惠') && text.includes('活动')) ||
    (text.includes('推广') && text.includes('促销'))
  ) {
    return {
      pass: false,
      type: VIOLATION_TYPES.AD,
      message: '内容疑似广告信息',
      detail: { label: '广告' }
    };
  }
  
  // 正常内容
  return {
    pass: true,
    type: VIOLATION_TYPES.NORMAL,
    message: '内容正常'
  };
}

/**
 * 云函数入口
 * @param {Object} event 请求参数
 * @param {Object} context 云函数上下文
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  console.log('内容审核请求:', event);
  
  const { type, content, url } = event;
  
  try {
    // 根据审核类型调用不同的处理函数
    if (type === 'text') {
      return await checkTextContent(content);
    } else if (type === 'image') {
      return await checkImageContent(url);
    } else {
      return {
        pass: false,
        message: '不支持的审核类型'
      };
    }
  } catch (error) {
    console.error('内容审核处理异常:', error);
    return {
      pass: true,
      message: '内容审核失败，将进行人工复核',
      needReview: true,
      error: error.message
    };
  }
}; 