{"version": 3, "file": "manual.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/verification/manual.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1x2ZXJpZmljYXRpb25cbWFudWFsLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"manual-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">手动核销</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 手动输入区域 -->\r\n    <view class=\"input-area\">\r\n      <view class=\"input-header\">\r\n        <text class=\"input-title\">请输入核销码</text>\r\n      </view>\r\n      \r\n      <view class=\"code-input-box\">\r\n        <input \r\n          class=\"code-input\" \r\n          type=\"text\" \r\n          placeholder=\"请输入核销码\" \r\n          v-model=\"verificationCode\"\r\n          maxlength=\"16\"\r\n          @input=\"onCodeInput\"\r\n          @confirm=\"verifyCode\"\r\n        />\r\n        <view class=\"clear-btn\" v-if=\"verificationCode\" @click=\"clearCode\">×</view>\r\n      </view>\r\n      \r\n      <button class=\"verify-btn\" :class=\"{ 'active': verificationCode.length > 0 }\" @click=\"verifyCode\">立即核销</button>\r\n      \r\n      <view class=\"tip-text\">\r\n        <text>提示：核销码通常为12-16位字母数字组合</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 历史记录 -->\r\n    <view class=\"history-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">最近核销记录</text>\r\n        <text class=\"view-all\" @click=\"viewAllRecords\">查看全部</text>\r\n      </view>\r\n      \r\n      <view class=\"history-list\">\r\n        <view v-if=\"historyRecords.length === 0\" class=\"empty-history\">\r\n          <text class=\"empty-text\">暂无核销记录</text>\r\n        </view>\r\n        <view v-else class=\"history-item\" v-for=\"(record, index) in historyRecords\" :key=\"index\" @click=\"showRecordDetail(record)\">\r\n          <view class=\"record-type\" :class=\"record.typeClass\">{{record.typeText}}</view>\r\n          <view class=\"record-content\">\r\n            <view class=\"record-main\">\r\n              <text class=\"record-title\">{{record.title}}</text>\r\n              <text class=\"record-code\">{{record.code}}</text>\r\n            </view>\r\n            <view class=\"record-info\">\r\n              <text class=\"record-time\">{{record.time}}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"record-status\" :class=\"record.statusClass\">{{record.status}}</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 核销成功弹窗 -->\r\n    <view class=\"verification-popup\" v-if=\"showVerificationPopup\">\r\n      <view class=\"popup-content\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">核销信息</text>\r\n          <view class=\"popup-close\" @click=\"closePopup\">×</view>\r\n        </view>\r\n        \r\n        <view class=\"verification-info\">\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">核销类型</text>\r\n            <text class=\"info-value\">{{verificationData.type}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">商品名称</text>\r\n            <text class=\"info-value\">{{verificationData.name}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">用户信息</text>\r\n            <text class=\"info-value\">{{verificationData.user}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">核销码</text>\r\n            <text class=\"info-value\">{{verificationData.code}}</text>\r\n          </view>\r\n          <view class=\"info-item\">\r\n            <text class=\"info-label\">有效期至</text>\r\n            <text class=\"info-value\">{{verificationData.expiry}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"verification-actions\">\r\n          <button class=\"btn-cancel\" @click=\"closePopup\">取消</button>\r\n          <button class=\"btn-confirm\" @click=\"confirmVerification\">确认核销</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      verificationCode: '',\r\n      showVerificationPopup: false,\r\n      verificationData: {\r\n        type: '',\r\n        name: '',\r\n        user: '',\r\n        code: '',\r\n        expiry: ''\r\n      },\r\n      historyRecords: [\r\n        {\r\n          typeText: '拼团',\r\n          typeClass: 'type-group',\r\n          title: '双人下午茶套餐拼团',\r\n          code: 'GP20230618001',\r\n          time: '今天 14:30',\r\n          status: '已核销',\r\n          statusClass: 'status-success'\r\n        },\r\n        {\r\n          typeText: '优惠券',\r\n          typeClass: 'type-coupon',\r\n          title: '新店开业满100减20券',\r\n          code: 'CP20230618002',\r\n          time: '今天 11:15',\r\n          status: '已核销',\r\n          statusClass: 'status-success'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    showHelp() {\r\n      uni.showToast({\r\n        title: '手动核销帮助',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    onCodeInput(e) {\r\n      this.verificationCode = e.detail.value;\r\n    },\r\n    clearCode() {\r\n      this.verificationCode = '';\r\n    },\r\n    verifyCode() {\r\n      if (!this.verificationCode) {\r\n        uni.showToast({\r\n          title: '请输入核销码',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      uni.showLoading({\r\n        title: '验证中...'\r\n      });\r\n      \r\n      // 模拟验证请求\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        \r\n        // 模拟核销信息\r\n        this.verificationData = {\r\n          type: '拼团活动',\r\n          name: '双人下午茶套餐拼团',\r\n          user: '张三 (138****8888)',\r\n          code: this.verificationCode,\r\n          expiry: '2023-06-25 23:59:59'\r\n        };\r\n        \r\n        this.showVerificationPopup = true;\r\n      }, 1000);\r\n    },\r\n    viewAllRecords() {\r\n      uni.navigateTo({\r\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/verification/records'\r\n      });\r\n    },\r\n    showRecordDetail(record) {\r\n      // 显示记录详情\r\n      uni.showToast({\r\n        title: '查看记录: ' + record.code,\r\n        icon: 'none'\r\n      });\r\n    },\r\n    closePopup() {\r\n      this.showVerificationPopup = false;\r\n    },\r\n    confirmVerification() {\r\n      uni.showLoading({\r\n        title: '核销中...'\r\n      });\r\n      \r\n      // 模拟核销请求\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '核销成功',\r\n          icon: 'success'\r\n        });\r\n        \r\n        // 关闭弹窗\r\n        this.closePopup();\r\n        \r\n        // 清空输入框\r\n        this.verificationCode = '';\r\n        \r\n        // 添加到历史记录\r\n        this.historyRecords.unshift({\r\n          typeText: '拼团',\r\n          typeClass: 'type-group',\r\n          title: this.verificationData.name,\r\n          code: this.verificationData.code,\r\n          time: '刚刚',\r\n          status: '已核销',\r\n          statusClass: 'status-success'\r\n        });\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.manual-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #2980b9, #2c3e50);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4px 20px rgba(41, 128, 185, 0.2);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 输入区域样式 */\r\n.input-area {\r\n  margin: 25px 15px;\r\n  background: #FFFFFF;\r\n  border-radius: 20px;\r\n  padding: 25px 20px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.input-header {\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.input-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.code-input-box {\r\n  position: relative;\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.code-input {\r\n  width: 100%;\r\n  height: 56px;\r\n  background: #F5F7FA;\r\n  border-radius: 16px;\r\n  padding: 0 18px;\r\n  font-size: 17px;\r\n  color: #333;\r\n  border: 1px solid rgba(0, 0, 0, 0.05);\r\n  transition: border-color 0.2s, box-shadow 0.2s;\r\n}\r\n\r\n.code-input:focus {\r\n  border-color: #2980b9;\r\n  box-shadow: 0 0 0 3px rgba(41, 128, 185, 0.1);\r\n}\r\n\r\n.clear-btn {\r\n  position: absolute;\r\n  right: 18px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: #C7C7CC;\r\n  color: #FFFFFF;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.verify-btn {\r\n  width: 100%;\r\n  height: 56px;\r\n  background: #E5E5EA;\r\n  color: #8E8E93;\r\n  border-radius: 16px;\r\n  font-size: 17px;\r\n  font-weight: 600;\r\n  margin-bottom: 18px;\r\n  transition: background-color 0.2s, transform 0.1s;\r\n}\r\n\r\n.verify-btn.active {\r\n  background: #2980b9;\r\n  color: #FFFFFF;\r\n}\r\n\r\n.verify-btn.active:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.tip-text {\r\n  text-align: center;\r\n  font-size: 13px;\r\n  color: #8E8E93;\r\n}\r\n\r\n/* 历史记录样式 */\r\n.history-section {\r\n  margin: 0 15px 25px;\r\n  background: #FFFFFF;\r\n  border-radius: 20px;\r\n  padding: 25px 20px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.view-all {\r\n  font-size: 14px;\r\n  color: #2980b9;\r\n  font-weight: 500;\r\n}\r\n\r\n.empty-history {\r\n  padding: 30px 0;\r\n  text-align: center;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n.history-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 15px 0;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.history-item:last-child {\r\n  border-bottom: none;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.record-type {\r\n  width: 60px;\r\n  height: 28px;\r\n  border-radius: 14px;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.type-group {\r\n  background-color: rgba(39, 174, 96, 0.15);\r\n  color: #27ae60;\r\n}\r\n\r\n.type-coupon {\r\n  background-color: rgba(41, 128, 185, 0.15);\r\n  color: #2980b9;\r\n}\r\n\r\n.type-flash {\r\n  background-color: rgba(231, 76, 60, 0.15);\r\n  color: #e74c3c;\r\n}\r\n\r\n.record-content {\r\n  flex: 1;\r\n}\r\n\r\n.record-main {\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.record-title {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-right: 8px;\r\n}\r\n\r\n.record-code {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.record-info {\r\n  display: flex;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.record-status {\r\n  font-size: 13px;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-success {\r\n  color: #30D158;\r\n}\r\n\r\n.status-pending {\r\n  color: #FF9500;\r\n}\r\n\r\n.status-failed {\r\n  color: #FF453A;\r\n}\r\n\r\n/* 核销弹窗样式 */\r\n.verification-popup {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  backdrop-filter: blur(5px);\r\n  -webkit-backdrop-filter: blur(5px);\r\n  z-index: 999;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.popup-content {\r\n  width: 85%;\r\n  background: #fff;\r\n  border-radius: 20px;\r\n  overflow: hidden;\r\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 18px 20px;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n  background: #f8f8f8;\r\n}\r\n\r\n.popup-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.popup-close {\r\n  font-size: 22px;\r\n  color: #777;\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 18px;\r\n}\r\n\r\n.popup-close:active {\r\n  background: rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.verification-info {\r\n  padding: 20px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.info-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.info-label {\r\n  width: 90px;\r\n  font-size: 15px;\r\n  color: #777;\r\n}\r\n\r\n.info-value {\r\n  flex: 1;\r\n  font-size: 15px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.verification-actions {\r\n  display: flex;\r\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.btn-cancel, .btn-confirm {\r\n  flex: 1;\r\n  height: 56px;\r\n  line-height: 56px;\r\n  text-align: center;\r\n  font-size: 17px;\r\n  font-weight: 500;\r\n  border: none;\r\n  border-radius: 0;\r\n}\r\n\r\n.btn-cancel {\r\n  background: #f8f8f8;\r\n  color: #666;\r\n}\r\n\r\n.btn-confirm {\r\n  background: #2980b9;\r\n  color: #fff;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/verification/manual.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2GA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,kBAAkB;AAAA,MAClB,uBAAuB;AAAA,MACvB,kBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD,gBAAgB;AAAA,QACd;AAAA,UACE,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,QACd;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,YAAY,GAAG;AACb,WAAK,mBAAmB,EAAE,OAAO;AAAA,IAClC;AAAA,IACD,YAAY;AACV,WAAK,mBAAmB;AAAA,IACzB;AAAA,IACD,aAAa;AACX,UAAI,CAAC,KAAK,kBAAkB;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,aAAK,mBAAmB;AAAA,UACtB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM,KAAK;AAAA,UACX,QAAQ;AAAA;AAGV,aAAK,wBAAwB;AAAA,MAC9B,GAAE,GAAI;AAAA,IACR;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,iBAAiB,QAAQ;AAEvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,WAAW,OAAO;AAAA,QACzB,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,aAAa;AACX,WAAK,wBAAwB;AAAA,IAC9B;AAAA,IACD,sBAAsB;AACpBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAGD,aAAK,WAAU;AAGf,aAAK,mBAAmB;AAGxB,aAAK,eAAe,QAAQ;AAAA,UAC1B,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO,KAAK,iBAAiB;AAAA,UAC7B,MAAM,KAAK,iBAAiB;AAAA,UAC5B,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,QACf,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxOA,GAAG,WAAW,eAAe;"}