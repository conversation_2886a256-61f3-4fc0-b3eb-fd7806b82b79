/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-600d0744, html.data-v-600d0744, #app.data-v-600d0744, .index-container.data-v-600d0744 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.platform-card.data-v-600d0744 {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}
.platform-card .platform-logo-container.data-v-600d0744 {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 12px;
  background-color: #F5F7FA;
  display: flex;
  align-items: center;
  justify-content: center;
}
.platform-card .platform-logo-container .platform-logo.data-v-600d0744 {
  width: 48px;
  height: 48px;
}
.platform-card .platform-info.data-v-600d0744 {
  width: 100%;
  text-align: center;
}
.platform-card .platform-name.data-v-600d0744 {
  margin-bottom: 6px;
}
.platform-card .platform-name text.data-v-600d0744 {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.platform-card .platform-cashback.data-v-600d0744 {
  padding: 2px 8px;
  background-color: rgba(156, 39, 176, 0.1);
  border-radius: 10px;
  display: inline-block;
}
.platform-card .platform-cashback text.data-v-600d0744 {
  font-size: 12px;
  color: #9C27B0;
}