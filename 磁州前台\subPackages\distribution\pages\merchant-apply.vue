<template>
  <view class="merchant-apply-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">申请商家专属分销</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 专属分销说明 -->
    <view class="intro-card">
      <view class="intro-header">
        <text class="intro-title">商家专属分销特权</text>
      </view>
      
      <view class="benefits-list">
        <view class="benefit-item">
          <view class="benefit-icon higher"></view>
          <view class="benefit-info">
            <text class="benefit-title">更高佣金</text>
            <text class="benefit-desc">享受比普通分销员更高的佣金比例</text>
          </view>
        </view>
        
        <view class="benefit-item">
          <view class="benefit-icon priority"></view>
          <view class="benefit-info">
            <text class="benefit-title">优先推广</text>
            <text class="benefit-desc">获得商家新品优先推广权</text>
          </view>
        </view>
        
        <view class="benefit-item">
          <view class="benefit-icon exclusive"></view>
          <view class="benefit-info">
            <text class="benefit-title">专属活动</text>
            <text class="benefit-desc">参与商家专属分销员活动</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 商家列表 -->
    <view class="section-header">
      <text class="section-title">选择商家申请</text>
    </view>
    
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrap">
        <view class="search-icon"></view>
        <input 
          class="search-input" 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索商家名称" 
          confirm-type="search"
          @confirm="searchMerchants"
        />
        <view class="clear-icon" v-if="searchKeyword" @click="clearSearch"></view>
      </view>
      <view class="search-btn" @click="searchMerchants">搜索</view>
    </view>
    
    <!-- 商家列表 -->
    <view class="merchants-list" v-if="merchants.length > 0">
      <view 
        v-for="(merchant, index) in merchants" 
        :key="index" 
        class="merchant-card"
        @click="selectMerchant(merchant)"
      >
        <view class="merchant-info">
          <image class="merchant-logo" :src="merchant.logo" mode="aspectFill"></image>
          <view class="merchant-details">
            <text class="merchant-name">{{merchant.name}}</text>
            <view class="merchant-meta">
              <text class="merchant-category">{{merchant.category}}</text>
              <text class="merchant-commission">佣金比例 {{merchant.commissionRate}}%</text>
            </view>
          </view>
          <view class="merchant-status" :class="{ 'applied': merchant.applied }">
            <text>{{merchant.applied ? '已申请' : '申请'}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-image" src="/static/images/empty-merchants.png" mode="aspectFit"></image>
      <text class="empty-text">暂无可申请的商家</text>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMoreData && merchants.length > 0">
      <text v-if="loading">加载中...</text>
      <text v-else @click="loadMore">点击加载更多</text>
    </view>
    
    <!-- 申请弹窗 -->
    <view class="apply-modal" v-if="showApplyModal">
      <view class="modal-mask" @click="closeApplyModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">申请成为专属分销员</text>
          <view class="close-icon" @click="closeApplyModal"></view>
        </view>
        
        <view class="modal-merchant">
          <image class="merchant-logo" :src="selectedMerchant.logo" mode="aspectFill"></image>
          <text class="merchant-name">{{selectedMerchant.name}}</text>
        </view>
        
        <view class="form-content">
          <view class="form-item">
            <text class="form-label">申请理由</text>
            <textarea class="form-textarea" v-model="formData.reason" placeholder="请简要描述您申请成为该商家专属分销员的理由" maxlength="200" />
            <text class="textarea-counter">{{formData.reason.length}}/200</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">销售经验</text>
            <textarea class="form-textarea" v-model="formData.experience" placeholder="请简要描述您的销售经验或相关背景" maxlength="200" />
            <text class="textarea-counter">{{formData.experience.length}}/200</text>
          </view>
          
          <view class="form-item">
            <text class="form-label">联系方式</text>
            <input class="form-input" type="number" v-model="formData.contact" placeholder="请输入您的手机号" maxlength="11" />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="cancel-btn" @click="closeApplyModal">取消</button>
          <button class="submit-btn" :disabled="!canSubmit" :class="{ 'disabled': !canSubmit }" @click="submitApplication">提交申请</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import distributionService from '@/utils/distributionService';

// 搜索关键词
const searchKeyword = ref('');

// 商家列表
const merchants = ref([]);

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 是否有更多数据
const hasMoreData = ref(false);

// 是否正在加载
const loading = ref(false);

// 申请弹窗
const showApplyModal = ref(false);

// 选中的商家
const selectedMerchant = ref({});

// 表单数据
const formData = reactive({
  reason: '',
  experience: '',
  contact: ''
});

// 是否可以提交
const canSubmit = computed(() => {
  return formData.reason.trim() !== '' && 
         formData.experience.trim() !== '' && 
         formData.contact.trim() !== '' &&
         formData.contact.length === 11;
});

// 页面加载
onMounted(async () => {
  // 获取商家列表
  await getMerchants();
});

// 获取商家列表
const getMerchants = async (loadMore = false) => {
  if (loading.value) return;
  
  try {
    loading.value = true;
    
    const page = loadMore ? pagination.page + 1 : 1;
    
    const result = await distributionService.getMerchantsWithDistribution({
      page,
      pageSize: pagination.pageSize,
      keyword: searchKeyword.value
    });
    
    if (result) {
      // 更新商家列表
      if (loadMore) {
        merchants.value = [...merchants.value, ...result.list];
      } else {
        merchants.value = result.list;
      }
      
      // 更新分页信息
      pagination.page = page;
      pagination.total = result.pagination.total;
      pagination.totalPages = result.pagination.totalPages;
      
      // 更新是否有更多数据
      hasMoreData.value = pagination.page < pagination.totalPages;
    }
  } catch (error) {
    console.error('获取商家列表失败', error);
    uni.showToast({
      title: '获取商家列表失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
};

// 搜索商家
const searchMerchants = () => {
  getMerchants();
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = '';
  getMerchants();
};

// 加载更多
const loadMore = () => {
  if (hasMoreData.value && !loading.value) {
    getMerchants(true);
  }
};

// 选择商家
const selectMerchant = (merchant) => {
  if (merchant.applied) {
    uni.showToast({
      title: '您已申请该商家',
      icon: 'none'
    });
    return;
  }
  
  selectedMerchant.value = merchant;
  showApplyModal.value = true;
};

// 关闭申请弹窗
const closeApplyModal = () => {
  showApplyModal.value = false;
};

// 提交申请
const submitApplication = async () => {
  if (!canSubmit.value) return;
  
  try {
    uni.showLoading({
      title: '提交中...',
      mask: true
    });
    
    const result = await distributionService.applyMerchantDistributor({
      merchantId: selectedMerchant.value.id,
      reason: formData.reason,
      experience: formData.experience,
      contact: formData.contact
    });
    
    uni.hideLoading();
    
    if (result.success) {
      uni.showModal({
        title: '申请提交成功',
        content: '您的专属分销员申请已提交，请耐心等待商家审核。',
        showCancel: false,
        success: () => {
          // 更新商家状态
          const index = merchants.value.findIndex(m => m.id === selectedMerchant.value.id);
          if (index !== -1) {
            merchants.value[index].applied = true;
          }
          
          // 关闭弹窗
          closeApplyModal();
          
          // 清空表单
          formData.reason = '';
          formData.experience = '';
          formData.contact = '';
        }
      });
    } else {
      uni.showModal({
        title: '申请提交失败',
        content: result.message || '请稍后再试',
        showCancel: false
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('提交申请失败', error);
    uni.showToast({
      title: '提交申请失败',
      icon: 'none'
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '商家专属分销帮助',
    content: '成为商家专属分销员后，您可以获得更高的佣金比例和更多专属特权。选择您感兴趣的商家，提交申请后等待商家审核。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.merchant-apply-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 专属分销说明 */
.intro-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.intro-header {
  margin-bottom: 30rpx;
}

.intro-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.benefits-list {
  margin-bottom: 20rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.benefit-item:last-child {
  margin-bottom: 0;
}

.benefit-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 10rpx;
  position: relative;
}

.benefit-icon.higher {
  background-color: #FF9500;
}

.benefit-icon.higher::before {
  content: '';
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  top: 15rpx;
  left: 15rpx;
  background-color: white;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z' fill='white'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z' fill='white'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
}

.benefit-icon.priority {
  background-color: #34C759;
}

.benefit-icon.priority::before {
  content: '';
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  top: 15rpx;
  left: 15rpx;
  background-color: white;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M13,19H14A1,1 0 0,1 15,20H22V22H15A1,1 0 0,1 14,23H10A1,1 0 0,1 9,22H2V20H9A1,1 0 0,1 10,19H11V17H4A1,1 0 0,1 3,16V12A1,1 0 0,1 4,11H20A1,1 0 0,1 21,12V16A1,1 0 0,1 20,17H13V19M4,3H20A1,1 0 0,1 21,4V8A1,1 0 0,1 20,9H4A1,1 0 0,1 3,8V4A1,1 0 0,1 4,3M9,7H10V5H9V7M9,15H10V13H9V15M5,5V7H7V5H5M5,13V15H7V13H5Z' fill='white'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M13,19H14A1,1 0 0,1 15,20H22V22H15A1,1 0 0,1 14,23H10A1,1 0 0,1 9,22H2V20H9A1,1 0 0,1 10,19H11V17H4A1,1 0 0,1 3,16V12A1,1 0 0,1 4,11H20A1,1 0 0,1 21,12V16A1,1 0 0,1 20,17H13V19M4,3H20A1,1 0 0,1 21,4V8A1,1 0 0,1 20,9H4A1,1 0 0,1 3,8V4A1,1 0 0,1 4,3M9,7H10V5H9V7M9,15H10V13H9V15M5,5V7H7V5H5M5,13V15H7V13H5Z' fill='white'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
}

.benefit-icon.exclusive {
  background-color: #6B0FBE;
}

.benefit-icon.exclusive::before {
  content: '';
  position: absolute;
  width: 50rpx;
  height: 50rpx;
  top: 15rpx;
  left: 15rpx;
  background-color: white;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12,8L10.67,8.09C9.81,7.07 7.4,4.5 5,4.5C5,4.5 3.03,7.46 4.96,11.41C4.41,12.24 4.07,12.67 4,13.66L2.07,13.95L2.28,14.93L4.04,14.67L4.18,15.38L2.61,16.32L3.08,17.21L4.53,16.32L5.5,17.94L4.8,19.15L5.74,19.85L6.75,18.22L8.07,18.22L8.07,20.39L9.05,20.39L9.16,18.22L10.41,18.22L11.3,19.85L12.23,19.15L11.45,17.86L12.47,16.32L13.93,17.21L14.39,16.32L12.83,15.38L12.96,14.67L14.73,14.93L14.92,13.95L13.03,13.66C12.96,12.67 12.59,12.24 12.04,11.41C13.97,7.46 12,4.5 12,4.5C9.6,4.5 7.18,7.07 6.32,8.09L5,8L5.01,9H6.34C6.63,9.32 7.3,10.13 7.46,10.13C7.61,10.13 8.12,9.44 8.46,9H12L12,8Z' fill='white'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12,8L10.67,8.09C9.81,7.07 7.4,4.5 5,4.5C5,4.5 3.03,7.46 4.96,11.41C4.41,12.24 4.07,12.67 4,13.66L2.07,13.95L2.28,14.93L4.04,14.67L4.18,15.38L2.61,16.32L3.08,17.21L4.53,16.32L5.5,17.94L4.8,19.15L5.74,19.85L6.75,18.22L8.07,18.22L8.07,20.39L9.05,20.39L9.16,18.22L10.41,18.22L11.3,19.85L12.23,19.15L11.45,17.86L12.47,16.32L13.93,17.21L14.39,16.32L12.83,15.38L12.96,14.67L14.73,14.93L14.92,13.95L13.03,13.66C12.96,12.67 12.59,12.24 12.04,11.41C13.97,7.46 12,4.5 12,4.5C9.6,4.5 7.18,7.07 6.32,8.09L5,8L5.01,9H6.34C6.63,9.32 7.3,10.13 7.46,10.13C7.61,10.13 8.12,9.44 8.46,9H12L12,8Z' fill='white'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
}

.benefit-info {
  flex: 1;
}

.benefit-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.benefit-desc {
  font-size: 24rpx;
  color: #666;
}

/* 商家列表 */
.section-header {
  margin: 30rpx 30rpx 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #FFFFFF;
  margin-bottom: 20rpx;
}

.search-input-wrap {
  flex: 1;
  height: 72rpx;
  background: #F5F7FA;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-right: 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #6B0FBE;
  border-radius: 50%;
  position: relative;
  margin-right: 10rpx;
}

.search-icon::before {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border: 2rpx solid white;
  border-radius: 50%;
  top: 6rpx;
  left: 6rpx;
}

.search-icon::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 2rpx;
  background-color: white;
  transform: rotate(45deg);
  bottom: 8rpx;
  right: 6rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: #cccccc;
  border-radius: 50%;
  position: relative;
}

.clear-icon::before,
.clear-icon::after {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 2rpx;
  background-color: white;
  top: 50%;
  left: 50%;
}

.clear-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.clear-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.search-btn {
  font-size: 28rpx;
  color: #6B0FBE;
}

/* 商家列表 */
.merchants-list {
  margin: 0 30rpx;
}

.merchant-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.merchant-info {
  padding: 30rpx;
  display: flex;
  align-items: center;
}

.merchant-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.merchant-details {
  flex: 1;
}

.merchant-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.merchant-meta {
  display: flex;
  align-items: center;
}

.merchant-category {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

.merchant-commission {
  font-size: 24rpx;
  color: #6B0FBE;
}

.merchant-status {
  width: 120rpx;
  height: 60rpx;
  background: #6B0FBE;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #fff;
}

.merchant-status.applied {
  background: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #666;
}

/* 申请弹窗 */
.apply-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-radius: 40rpx 40rpx 0 0;
  padding: 30rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}

.close-icon::before,
.close-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32rpx;
  height: 2rpx;
  background-color: #999;
}

.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.modal-merchant {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-merchant .merchant-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 16rpx;
  margin-right: 0;
}

.modal-merchant .merchant-name {
  margin-bottom: 0;
}

.form-content {
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.textarea-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #F5F7FA;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
}

.cancel-btn,
.submit-btn {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.cancel-btn {
  background: #F5F7FA;
  color: #666;
}

.submit-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
}

.submit-btn.disabled {
  background: #cccccc;
  color: #ffffff;
}
</style> 