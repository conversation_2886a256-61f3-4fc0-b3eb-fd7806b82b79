"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
if (!Math) {
  CreateButton();
}
const CreateButton = () => "../../../components/CreateButton.js";
const _sfc_main = {
  __name: "management",
  setup(__props) {
    const flashData = common_vendor.reactive({
      totalSales: "15,632.80",
      totalOrders: "342",
      conversionRate: "28.6",
      avgOrderValue: "45.8"
    });
    const activeFilter = common_vendor.ref("all");
    const filterLabels = {
      all: "全部",
      active: "进行中",
      upcoming: "未开始",
      ended: "已结束"
    };
    const flashActivities = common_vendor.ref([
      {
        id: 1,
        name: "夏季清凉风扇特惠",
        flashPrice: 59.9,
        originalPrice: 129.9,
        stockTotal: 100,
        stockSold: 86,
        viewCount: 1564,
        image: "/static/images/flash-item1.jpg",
        timeRange: "2023-07-01 12:00 ~ 14:00",
        status: "active",
        statusText: "进行中",
        statusClass: "status-active"
      },
      {
        id: 2,
        name: "夏季新品T恤限时秒杀",
        flashPrice: 39.9,
        originalPrice: 99.9,
        stockTotal: 200,
        stockSold: 143,
        viewCount: 2238,
        image: "/static/images/flash-item2.jpg",
        timeRange: "2023-07-02 18:00 ~ 20:00",
        status: "active",
        statusText: "进行中",
        statusClass: "status-active"
      },
      {
        id: 3,
        name: "智能手环超值特惠",
        flashPrice: 89.9,
        originalPrice: 199.9,
        stockTotal: 50,
        stockSold: 0,
        viewCount: 562,
        image: "/static/images/flash-item3.jpg",
        timeRange: "2023-07-05 12:00 ~ 14:00",
        status: "upcoming",
        statusText: "未开始",
        statusClass: "status-upcoming"
      },
      {
        id: 4,
        name: "厨房小家电秒杀",
        flashPrice: 129.9,
        originalPrice: 299.9,
        stockTotal: 80,
        stockSold: 80,
        viewCount: 1876,
        image: "/static/images/flash-item4.jpg",
        timeRange: "2023-06-25 18:00 ~ 20:00",
        status: "ended",
        statusText: "已结束",
        statusClass: "status-ended"
      },
      {
        id: 5,
        name: "儿童玩具特惠",
        flashPrice: 49.9,
        originalPrice: 99.9,
        stockTotal: 150,
        stockSold: 98,
        viewCount: 1245,
        image: "/static/images/flash-item5.jpg",
        timeRange: "2023-06-20 12:00 ~ 14:00",
        status: "ended",
        statusText: "已结束",
        statusClass: "status-ended"
      }
    ]);
    const activeTimeRange = common_vendor.ref("30d");
    const chartData = common_vendor.ref([
      { date: "06-01", sales: 1200, orders: 28, salesHeight: 40, ordersHeight: 35 },
      { date: "06-05", sales: 1850, orders: 42, salesHeight: 61.7, ordersHeight: 52.5 },
      { date: "06-10", sales: 1500, orders: 35, salesHeight: 50, ordersHeight: 43.75 },
      { date: "06-15", sales: 2400, orders: 56, salesHeight: 80, ordersHeight: 70 },
      { date: "06-20", sales: 3e3, orders: 80, salesHeight: 100, ordersHeight: 100 },
      { date: "06-25", sales: 2200, orders: 52, salesHeight: 73.3, ordersHeight: 65 },
      { date: "06-30", sales: 1800, orders: 40, salesHeight: 60, ordersHeight: 50 }
    ]);
    const filteredActivities = common_vendor.computed(() => {
      if (activeFilter.value === "all") {
        return flashActivities.value;
      }
      return flashActivities.value.filter((item) => item.status === activeFilter.value);
    });
    const setFilter = (filter) => {
      activeFilter.value = filter;
    };
    const setTimeRange = (range) => {
      activeTimeRange.value = range;
      loadChartData(range);
    };
    const loadChartData = (range) => {
      if (range === "7d") {
        chartData.value = [
          { date: "06-24", sales: 1200, orders: 28, salesHeight: 40, ordersHeight: 35 },
          { date: "06-25", sales: 1850, orders: 42, salesHeight: 61.7, ordersHeight: 52.5 },
          { date: "06-26", sales: 1500, orders: 35, salesHeight: 50, ordersHeight: 43.75 },
          { date: "06-27", sales: 2400, orders: 56, salesHeight: 80, ordersHeight: 70 },
          { date: "06-28", sales: 3e3, orders: 80, salesHeight: 100, ordersHeight: 100 },
          { date: "06-29", sales: 2200, orders: 52, salesHeight: 73.3, ordersHeight: 65 },
          { date: "06-30", sales: 1800, orders: 40, salesHeight: 60, ordersHeight: 50 }
        ];
      } else if (range === "30d") {
        chartData.value = [
          { date: "06-01", sales: 1200, orders: 28, salesHeight: 40, ordersHeight: 35 },
          { date: "06-05", sales: 1850, orders: 42, salesHeight: 61.7, ordersHeight: 52.5 },
          { date: "06-10", sales: 1500, orders: 35, salesHeight: 50, ordersHeight: 43.75 },
          { date: "06-15", sales: 2400, orders: 56, salesHeight: 80, ordersHeight: 70 },
          { date: "06-20", sales: 3e3, orders: 80, salesHeight: 100, ordersHeight: 100 },
          { date: "06-25", sales: 2200, orders: 52, salesHeight: 73.3, ordersHeight: 65 },
          { date: "06-30", sales: 1800, orders: 40, salesHeight: 60, ordersHeight: 50 }
        ];
      } else if (range === "90d") {
        chartData.value = [
          { date: "04-01", sales: 1e3, orders: 22, salesHeight: 33.3, ordersHeight: 27.5 },
          { date: "04-15", sales: 1400, orders: 32, salesHeight: 46.7, ordersHeight: 40 },
          { date: "05-01", sales: 1700, orders: 38, salesHeight: 56.7, ordersHeight: 47.5 },
          { date: "05-15", sales: 2100, orders: 48, salesHeight: 70, ordersHeight: 60 },
          { date: "06-01", sales: 2500, orders: 58, salesHeight: 83.3, ordersHeight: 72.5 },
          { date: "06-15", sales: 2800, orders: 65, salesHeight: 93.3, ordersHeight: 81.25 },
          { date: "06-30", sales: 3e3, orders: 80, salesHeight: 100, ordersHeight: 100 }
        ];
      }
    };
    const viewFlashDetail = (item) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/detail?id=${item.id}`
      });
    };
    const editFlashSale = (item) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/flash/edit?id=${item.id}`
      });
    };
    const shareFlashSale = (item) => {
      common_vendor.index.showToast({
        title: "生成分享链接中...",
        icon: "loading",
        duration: 1500
      });
      setTimeout(() => {
        common_vendor.index.showModal({
          title: "分享秒杀活动",
          content: `活动"${item.name}"的分享链接已创建，可发送给客户或分享到社交媒体`,
          confirmText: "复制链接",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.setClipboardData({
                data: `https://example.com/flash-sale/${item.id}`,
                success: () => {
                  common_vendor.index.showToast({
                    title: "链接已复制",
                    icon: "success"
                  });
                }
              });
            }
          }
        });
      }, 1500);
    };
    const deleteFlashSale = (item) => {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除"${item.name}"秒杀活动吗？一旦删除将无法恢复。`,
        confirmText: "删除",
        confirmColor: "#FF3B30",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            const index = flashActivities.value.findIndex((i) => i.id === item.id);
            if (index !== -1) {
              flashActivities.value.splice(index, 1);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            }
          }
        }
      });
    };
    const createFlashSale = () => {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/flash/create"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/flash/management.vue:466", "限时秒杀管理页面已加载");
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          d: "M15 18L9 12L15 6",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        b: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        c: common_vendor.o(goBack),
        d: common_vendor.o(createFlashSale),
        e: common_vendor.p({
          text: "创建秒杀活动",
          theme: "flash"
        }),
        f: common_vendor.t(flashData.totalSales),
        g: common_vendor.t(flashData.totalOrders),
        h: common_vendor.t(flashData.conversionRate),
        i: common_vendor.t(flashData.avgOrderValue),
        j: activeFilter.value === "all" ? 1 : "",
        k: common_vendor.o(($event) => setFilter("all")),
        l: activeFilter.value === "active" ? 1 : "",
        m: common_vendor.o(($event) => setFilter("active")),
        n: activeFilter.value === "upcoming" ? 1 : "",
        o: common_vendor.o(($event) => setFilter("upcoming")),
        p: activeFilter.value === "ended" ? 1 : "",
        q: common_vendor.o(($event) => setFilter("ended")),
        r: filteredActivities.value.length > 0
      }, filteredActivities.value.length > 0 ? {
        s: common_vendor.f(filteredActivities.value, (item, index, i0) => {
          return {
            a: item.image,
            b: common_vendor.t(item.statusText),
            c: common_vendor.n(item.statusClass),
            d: common_vendor.t(item.name),
            e: common_vendor.t(item.flashPrice),
            f: common_vendor.t(item.originalPrice),
            g: common_vendor.t(item.stockSold),
            h: common_vendor.t(item.stockTotal),
            i: common_vendor.t(item.viewCount),
            j: common_vendor.t(item.timeRange),
            k: "0db905f6-4-" + i0 + "," + ("0db905f6-3-" + i0),
            l: "0db905f6-5-" + i0 + "," + ("0db905f6-3-" + i0),
            m: "0db905f6-3-" + i0,
            n: common_vendor.o(($event) => editFlashSale(item), index),
            o: "0db905f6-7-" + i0 + "," + ("0db905f6-6-" + i0),
            p: "0db905f6-8-" + i0 + "," + ("0db905f6-6-" + i0),
            q: "0db905f6-9-" + i0 + "," + ("0db905f6-6-" + i0),
            r: "0db905f6-10-" + i0 + "," + ("0db905f6-6-" + i0),
            s: "0db905f6-11-" + i0 + "," + ("0db905f6-6-" + i0),
            t: "0db905f6-6-" + i0,
            v: common_vendor.o(($event) => shareFlashSale(item), index),
            w: "0db905f6-13-" + i0 + "," + ("0db905f6-12-" + i0),
            x: "0db905f6-14-" + i0 + "," + ("0db905f6-12-" + i0),
            y: "0db905f6-12-" + i0,
            z: common_vendor.o(($event) => deleteFlashSale(item), index),
            A: index,
            B: common_vendor.o(($event) => viewFlashDetail(item), index)
          };
        }),
        t: common_vendor.p({
          d: "M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13",
          stroke: "#5E5CE6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        v: common_vendor.p({
          d: "M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z",
          stroke: "#5E5CE6",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        w: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        x: common_vendor.p({
          d: "M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        y: common_vendor.p({
          d: "M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        z: common_vendor.p({
          d: "M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        A: common_vendor.p({
          d: "M8.59 13.51L15.42 17.49",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        B: common_vendor.p({
          d: "M15.41 6.51L8.59 10.49",
          stroke: "#34C759",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        C: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        D: common_vendor.p({
          d: "M3 6H5H21",
          stroke: "#FF3B30",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        E: common_vendor.p({
          d: "M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z",
          stroke: "#FF3B30",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        F: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        })
      } : {}, {
        G: filteredActivities.value.length === 0
      }, filteredActivities.value.length === 0 ? {
        H: common_assets._imports_0$32,
        I: common_vendor.t(filterLabels[activeFilter.value])
      } : {}, {
        J: filteredActivities.value.length > 0
      }, filteredActivities.value.length > 0 ? {
        K: activeTimeRange.value === "7d" ? 1 : "",
        L: common_vendor.o(($event) => setTimeRange("7d")),
        M: activeTimeRange.value === "30d" ? 1 : "",
        N: common_vendor.o(($event) => setTimeRange("30d")),
        O: activeTimeRange.value === "90d" ? 1 : "",
        P: common_vendor.o(($event) => setTimeRange("90d")),
        Q: common_vendor.f(chartData.value, (item, index, i0) => {
          return {
            a: item.salesHeight + "%",
            b: item.ordersHeight + "%",
            c: common_vendor.t(item.date),
            d: index
          };
        })
      } : {}, {
        R: filteredActivities.value.length === 0
      }, filteredActivities.value.length === 0 ? {
        S: common_vendor.p({
          d: "M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z",
          stroke: "#FF7600",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        T: common_vendor.p({
          d: "M12 16V12",
          stroke: "#FF7600",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        U: common_vendor.p({
          d: "M12 8H12.01",
          stroke: "#FF7600",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        V: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        W: common_vendor.p({
          d: "M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z",
          stroke: "#FF7600",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        X: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        Y: common_vendor.p({
          d: "M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z",
          stroke: "#FF7600",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        Z: common_vendor.p({
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        })
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/flash/management.js.map
