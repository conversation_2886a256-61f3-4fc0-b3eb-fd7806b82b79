
.pet-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 150rpx;
  padding-top: 0; /* 移除顶部内边距，由导航栏控制 */
}
.pet-detail-wrapper {
  padding: 24rpx;
  padding-top: 144px; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}
.content-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 宠物基本信息卡片 */
.pet-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.pet-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.pet-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff4d4f;
}
.pet-meta {
  margin-bottom: 24rpx;
}
.pet-tag-group {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}
.pet-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}
.pet-publish-time {
  font-size: 24rpx;
  color: #999;
}

/* 轮播图 */
.pet-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}
.pet-image {
  width: 100%;
  height: 100%;
}

/* 基本信息 */
.pet-basic-info {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.info-item {
  width: 50%;
  padding: 12rpx 24rpx;
  box-sizing: border-box;
}
.info-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 宠物特征 */
.features-list {
  display: flex;
  flex-direction: column;
}
.feature-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.feature-item:last-child {
  border-bottom: none;
}
.feature-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.feature-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 健康状况 */
.health-list {
  display: flex;
  flex-direction: column;
}
.health-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.health-item:last-child {
  border-bottom: none;
}
.health-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.health-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 宠物描述 */
.description-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 主人信息 */
.owner-header {
  display: flex;
  align-items: center;
}
.owner-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}
.owner-avatar image {
  width: 100%;
  height: 100%;
}
.owner-info {
  flex: 1;
}
.owner-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.owner-meta {
  display: flex;
  align-items: center;
}
.owner-type, .owner-rating {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 联系方式 */
.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
}
.contact-content {
  display: flex;
  flex-direction: column;
}
.contact-item {
  display: flex;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.contact-item:last-child {
  border-bottom: none;
}
.contact-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.contact-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.contact-phone {
  color: #1890ff;
}
.contact-tips {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
}
.tips-icon {
  margin-right: 8rpx;
}
.tips-text {
  font-size: 24rpx;
  color: #999;
}

/* 相关推荐模块 */
.related-pets-card {
  margin-top: 24rpx;
}
.related-pets-content {
  padding: 24rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
}
.related-pets-list {
  display: flex;
  flex-direction: column;
}
.related-pet-item {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.pet-item-content {
  display: flex;
  align-items: center;
}
.pet-item-left {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}
.pet-item-middle {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.pet-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.pet-item-meta {
  font-size: 24rpx;
  color: #999;
}
.pet-item-tags {
  display: flex;
  flex-wrap: wrap;
}
.pet-item-tag {
  font-size: 24rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}
.pet-item-tag-more {
  font-size: 24rpx;
  color: #ff4d4f;
}
.pet-item-right {
  width: 120rpx;
  text-align: right;
}
.pet-item-price {
  font-size: 28rpx;
  color: #ff4d4f;
}
.empty-related-pets {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}
.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.view-more-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12rpx 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-top: 24rpx;
}
.view-more-text {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 8rpx;
}
.view-more-icon {
  font-size: 24rpx;
  color: #999;
}

/* 底部互动工具栏 */
.interaction-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10rpx 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 120rpx;
  z-index: 100;
}
.toolbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
  margin: 0 4rpx;
}
.toolbar-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}
.toolbar-text {
  font-size: 22rpx;
  color: #666;
}
.share-button {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  border-radius: 0;
  flex: 1;
}
.share-button::after {
  display: none;
}
.call-button {
  flex: 3; /* 占据更多空间，原来是2，现在改为3让按钮更长 */
  background: linear-gradient(135deg, #0052CC, #0066FF);
  height: 90rpx;
  margin: 0 0 0 10rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
}
.call-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.call-text {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.2;
}
.call-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  line-height: 1.2;
}

/* 隐藏原来的底部操作栏 */
.action-bar {
  display: none;
}

/* 隐藏的分享按钮 */
.hidden-share-btn {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  opacity: 0;
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #0066FF, #0052CC);
  height: 88rpx;
  padding-top: 44px; /* 状态栏高度 */
  display: flex;
  align-items: center;
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
  z-index: 100; /* 提高z-index确保在最上层 */
}
.navbar-left {
  width: 60px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 20px;
  height: 20px;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  color: #fff;
}
.navbar-right {
  width: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
