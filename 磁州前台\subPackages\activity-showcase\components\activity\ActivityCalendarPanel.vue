<template>
  <view class="activity-calendar-panel" :style="calendarStyle">
    <view class="panel-header">
      <text class="panel-title">活动日历</text>
      <view class="panel-more" @click="$emit('viewCalendar')">
        <text>查看更多</text>
        <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
          <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </view>
    </view>
    
    <view class="calendar-container">
      <!-- 月份导航 -->
      <view class="month-navigation">
        <view class="month-arrow" @click="prevMonth">
          <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
            <path d="M15 18l-6-6 6-6" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        
        <view class="current-month">{{ currentYear }}年{{ currentMonth + 1 }}月</view>
        
        <view class="month-arrow" @click="nextMonth">
          <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
            <path d="M9 18l6-6-6-6" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
      </view>
      
      <!-- 星期标题 -->
      <view class="weekday-header">
        <text class="weekday-item" v-for="day in weekdays" :key="day">{{ day }}</text>
      </view>
      
      <!-- 日期网格 -->
      <view class="date-grid">
        <view 
          class="date-item"
          v-for="(date, index) in calendarDates"
          :key="index"
          :class="{
            'other-month': date.otherMonth,
            'today': isToday(date),
            'has-events': hasEvents(date),
            'selected': isSelected(date)
          }"
          @click="selectDate(date)"
        >
          <text class="date-number">{{ date.day }}</text>
          <view class="event-indicator" v-if="hasEvents(date)"></view>
        </view>
      </view>
      
      <!-- 活动列表 -->
      <view class="events-list" v-if="selectedDateEvents.length > 0">
        <view class="events-header">
          <text class="events-title">{{ formatSelectedDate }}活动</text>
          <text class="events-count">{{ selectedDateEvents.length }}个</text>
        </view>
        
        <view 
          class="event-item"
          v-for="(event, index) in selectedDateEvents"
          :key="index"
          @click="$emit('viewEvent', event)"
        >
          <view class="event-time">{{ formatEventTime(event.startTime) }}</view>
          <view class="event-content">
            <text class="event-title">{{ event.title }}</text>
            <text class="event-location" v-if="event.location">{{ event.location }}</text>
          </view>
          <view class="event-tag" :class="getEventTagClass(event.type)">{{ getEventTypeText(event.type) }}</view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-events" v-else-if="selectedDate">
        <image class="empty-image" src="/static/images/empty/empty-calendar.png" mode="aspectFit"></image>
        <text class="empty-text">{{ formatSelectedDate }}暂无活动</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 组件属性定义
const props = defineProps({
  activityEvents: {
    type: Array,
    default: () => []
  },
  calendarStyle: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
defineEmits(['selectDate', 'viewCalendar', 'viewEvent']);

// 星期几标题
const weekdays = ['日', '一', '二', '三', '四', '五', '六'];

// 当前日期
const today = new Date();
const currentYear = ref(today.getFullYear());
const currentMonth = ref(today.getMonth());
const selectedDate = ref(null);

// 选择日期
const selectDate = (date) => {
  if (date.otherMonth) {
    // 如果选择的是其他月份的日期，切换到对应月份
    if (date.day > 20) {
      // 上个月的日期
      prevMonth();
    } else {
      // 下个月的日期
      nextMonth();
    }
  }
  
  selectedDate.value = new Date(date.year, date.month, date.day);
  
  // 触发事件
  const formattedDate = `${date.year}-${String(date.month + 1).padStart(2, '0')}-${String(date.day).padStart(2, '0')}`;
  $emit('selectDate', formattedDate);
};

// 上个月
const prevMonth = () => {
  if (currentMonth.value === 0) {
    currentYear.value--;
    currentMonth.value = 11;
  } else {
    currentMonth.value--;
  }
};

// 下个月
const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentYear.value++;
    currentMonth.value = 0;
  } else {
    currentMonth.value++;
  }
};

// 判断是否是今天
const isToday = (date) => {
  const now = new Date();
  return date.year === now.getFullYear() && 
         date.month === now.getMonth() && 
         date.day === now.getDate();
};

// 判断是否是选中日期
const isSelected = (date) => {
  if (!selectedDate.value) return false;
  
  return date.year === selectedDate.value.getFullYear() && 
         date.month === selectedDate.value.getMonth() && 
         date.day === selectedDate.value.getDate();
};

// 判断日期是否有活动
const hasEvents = (date) => {
  return props.activityEvents.some(event => {
    const eventDate = new Date(event.startTime);
    return date.year === eventDate.getFullYear() && 
           date.month === eventDate.getMonth() && 
           date.day === eventDate.getDate();
  });
};

// 获取当前月的日历数据
const calendarDates = computed(() => {
  const year = currentYear.value;
  const month = currentMonth.value;
  
  // 获取当月第一天是星期几
  const firstDay = new Date(year, month, 1).getDay();
  
  // 获取当月天数
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  
  // 获取上个月的天数
  const daysInPrevMonth = new Date(year, month, 0).getDate();
  
  const dates = [];
  
  // 添加上个月的日期
  const prevYear = month === 0 ? year - 1 : year;
  const prevMonth = month === 0 ? 11 : month - 1;
  
  for (let i = 0; i < firstDay; i++) {
    const day = daysInPrevMonth - firstDay + i + 1;
    dates.push({
      year: prevYear,
      month: prevMonth,
      day,
      otherMonth: true
    });
  }
  
  // 添加当月的日期
  for (let i = 1; i <= daysInMonth; i++) {
    dates.push({
      year,
      month,
      day: i,
      otherMonth: false
    });
  }
  
  // 添加下个月的日期，补齐6行7列
  const nextYear = month === 11 ? year + 1 : year;
  const nextMonth = month === 11 ? 0 : month + 1;
  
  const remainingDays = 42 - dates.length; // 6行7列 = 42
  
  for (let i = 1; i <= remainingDays; i++) {
    dates.push({
      year: nextYear,
      month: nextMonth,
      day: i,
      otherMonth: true
    });
  }
  
  return dates;
});

// 获取选中日期的活动
const selectedDateEvents = computed(() => {
  if (!selectedDate.value) return [];
  
  return props.activityEvents.filter(event => {
    const eventDate = new Date(event.startTime);
    return selectedDate.value.getFullYear() === eventDate.getFullYear() && 
           selectedDate.value.getMonth() === eventDate.getMonth() && 
           selectedDate.value.getDate() === eventDate.getDate();
  }).sort((a, b) => new Date(a.startTime) - new Date(b.startTime));
});

// 格式化选中日期
const formatSelectedDate = computed(() => {
  if (!selectedDate.value) return '';
  
  const year = selectedDate.value.getFullYear();
  const month = selectedDate.value.getMonth() + 1;
  const day = selectedDate.value.getDate();
  
  return `${month}月${day}日`;
});

// 格式化活动时间
const formatEventTime = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const hours = date.getHours();
  const minutes = date.getMinutes();
  
  return `${hours}:${minutes.toString().padStart(2, '0')}`;
};

// 获取活动类型文本
const getEventTypeText = (type) => {
  switch(type) {
    case 'flash':
      return '秒杀';
    case 'group':
      return '拼团';
    case 'discount':
      return '满减';
    case 'coupon':
      return '优惠券';
    default:
      return '活动';
  }
};

// 获取活动标签样式类
const getEventTagClass = (type) => {
  switch(type) {
    case 'flash':
      return 'tag-flash';
    case 'group':
      return 'tag-group';
    case 'discount':
      return 'tag-discount';
    case 'coupon':
      return 'tag-coupon';
    default:
      return '';
  }
};

// 初始化选中今天
onMounted(() => {
  selectedDate.value = new Date();
});
</script>

<style lang="scss" scoped>
.activity-calendar-panel {
  width: 100%;
  border-radius: 35px;
  background-color: #FFFFFF;
  padding: 30rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
  box-shadow: 0 10px 30px rgba(0,0,0,0.08);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .panel-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .panel-more {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #FF3B69;
    
    .icon {
      margin-left: 4rpx;
    }
  }
}

.calendar-container {
  .month-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .month-arrow {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 30rpx;
      background-color: rgba(0,0,0,0.03);
      
      &:active {
        background-color: rgba(0,0,0,0.06);
      }
    }
    
    .current-month {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
    }
  }
  
  .weekday-header {
    display: flex;
    margin-bottom: 10rpx;
    
    .weekday-item {
      flex: 1;
      text-align: center;
      font-size: 24rpx;
      color: #8E8E93;
      padding: 10rpx 0;
    }
  }
  
  .date-grid {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30rpx;
    
    .date-item {
      width: calc(100% / 7);
      height: 80rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      
      .date-number {
        font-size: 28rpx;
        color: #333333;
      }
      
      .event-indicator {
        width: 6rpx;
        height: 6rpx;
        border-radius: 3rpx;
        background-color: #FF3B69;
        margin-top: 6rpx;
      }
      
      &.other-month {
        .date-number {
          color: #C7C7CC;
        }
      }
      
      &.today {
        .date-number {
          color: #FFFFFF;
          background-color: #FF3B69;
          width: 60rpx;
          height: 60rpx;
          border-radius: 30rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      
      &.selected:not(.today) {
        background-color: rgba(255,59,105,0.1);
        border-radius: 10rpx;
      }
      
      &.has-events:not(.today) {
        .date-number {
          font-weight: 500;
          color: #FF3B69;
        }
      }
      
      &:active {
        background-color: rgba(0,0,0,0.03);
        border-radius: 10rpx;
      }
    }
  }
  
  .events-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .events-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
    }
    
    .events-count {
      font-size: 24rpx;
      color: #FF3B69;
      background-color: rgba(255,59,105,0.1);
      padding: 4rpx 12rpx;
      border-radius: 16rpx;
    }
  }
  
  .events-list {
    .event-item {
      display: flex;
      align-items: center;
      padding: 20rpx;
      background-color: rgba(0,0,0,0.02);
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      
      .event-time {
        font-size: 24rpx;
        font-weight: 500;
        color: #FF3B69;
        margin-right: 20rpx;
        min-width: 80rpx;
      }
      
      .event-content {
        flex: 1;
        
        .event-title {
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 4rpx;
        }
        
        .event-location {
          font-size: 24rpx;
          color: #8E8E93;
        }
      }
      
      .event-tag {
        font-size: 22rpx;
        color: #FFFFFF;
        padding: 4rpx 12rpx;
        border-radius: 10rpx;
      }
      
      &:active {
        transform: scale(0.98);
      }
    }
  }
  
  .empty-events {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;
    
    .empty-image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #8E8E93;
    }
  }
}

/* 标签样式 */
.tag-flash {
  background-color: #FF3B30;
}

.tag-group {
  background-color: #34C759;
}

.tag-discount {
  background-color: #5856D6;
}

.tag-coupon {
  background-color: #FF9500;
}
</style> 