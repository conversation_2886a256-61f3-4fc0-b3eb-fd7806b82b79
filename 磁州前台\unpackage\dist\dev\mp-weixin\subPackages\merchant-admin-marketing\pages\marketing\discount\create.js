"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const subPackages_merchantAdminMarketing_services_distributionService = require("../../../services/distributionService.js");
const DistributionSetting = () => "../distribution/components/DistributionSetting.js";
const MarketingPromotionActions = () => "../../../components/MarketingPromotionActions.js";
const _sfc_main = {
  components: {
    DistributionSetting,
    MarketingPromotionActions
  },
  setup() {
    const discountForm = common_vendor.reactive({
      title: "",
      startDate: "",
      endDate: "",
      status: "active",
      rules: [],
      applicableProducts: "全部商品",
      canStack: false,
      perPersonLimit: "3",
      instructions: "",
      distributionSettings: {
        enabled: false,
        commissionMode: "percentage",
        commissions: {
          level1: "",
          level2: "",
          level3: ""
        },
        enableLevel3: false
      }
    });
    const showDatePickerDialog = common_vendor.ref(false);
    const currentDatePicker = common_vendor.ref("");
    const hasMerchantDistribution = common_vendor.ref(false);
    const tempDiscountId = common_vendor.ref("temp-" + Date.now());
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function showDatePicker(type) {
      currentDatePicker.value = type;
      showDatePickerDialog.value = true;
    }
    function closeDatePicker() {
      showDatePickerDialog.value = false;
    }
    function onDateSelect(e) {
      const selectedDate = e.fulldate;
      if (currentDatePicker.value === "start") {
        discountForm.startDate = selectedDate;
      } else {
        discountForm.endDate = selectedDate;
      }
      closeDatePicker();
    }
    function onStatusChange(e) {
      discountForm.status = e.detail.value ? "active" : "paused";
    }
    function onStackChange(e) {
      discountForm.canStack = e.detail.value;
    }
    function addRule() {
      discountForm.rules.push({
        minAmount: "",
        discountAmount: ""
      });
    }
    function deleteRule(index) {
      discountForm.rules.splice(index, 1);
    }
    function selectProducts() {
      common_vendor.index.showActionSheet({
        itemList: ["全部商品", "指定商品", "指定分类"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              discountForm.applicableProducts = "全部商品";
              break;
            case 1:
              discountForm.applicableProducts = "指定商品";
              break;
            case 2:
              discountForm.applicableProducts = "指定分类";
              break;
          }
        }
      });
    }
    function updateDistributionSettings(settings) {
      discountForm.distributionSettings = settings;
    }
    async function checkMerchantDistribution() {
      try {
        const instance = common_vendor.getCurrentInstance();
        const merchantInfo = instance.proxy.$store.state.merchant.merchantInfo || {};
        hasMerchantDistribution.value = await subPackages_merchantAdminMarketing_services_distributionService.distributionService.checkMerchantDistribution(merchantInfo);
        if (hasMerchantDistribution.value) {
          const settings = await subPackages_merchantAdminMarketing_services_distributionService.distributionService.getMerchantDistributionSettings(merchantInfo);
          discountForm.distributionSettings = settings;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/discount/create.vue:359", "检查分销功能失败", error);
        hasMerchantDistribution.value = false;
      }
    }
    function validateForm() {
      if (!discountForm.title.trim()) {
        common_vendor.index.showToast({
          title: "请输入活动名称",
          icon: "none"
        });
        return false;
      }
      if (!discountForm.startDate || !discountForm.endDate) {
        common_vendor.index.showToast({
          title: "请选择活动时间",
          icon: "none"
        });
        return false;
      }
      if (discountForm.rules.length === 0) {
        common_vendor.index.showToast({
          title: "请添加至少一条满减规则",
          icon: "none"
        });
        return false;
      }
      for (const rule of discountForm.rules) {
        if (!rule.minAmount || !rule.discountAmount) {
          common_vendor.index.showToast({
            title: "请完善满减规则",
            icon: "none"
          });
          return false;
        }
        if (parseFloat(rule.discountAmount) >= parseFloat(rule.minAmount)) {
          common_vendor.index.showToast({
            title: "优惠金额不能大于等于满减金额",
            icon: "none"
          });
          return false;
        }
      }
      return true;
    }
    async function saveDiscount() {
      if (!validateForm()) {
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      if (hasMerchantDistribution.value && discountForm.distributionSettings.enabled) {
        const { valid, errors } = subPackages_merchantAdminMarketing_services_distributionService.distributionService.validateDistributionSettings(discountForm.distributionSettings);
        if (!valid) {
          common_vendor.index.showToast({
            title: errors[0],
            icon: "none"
          });
          return;
        }
        const success = await subPackages_merchantAdminMarketing_services_distributionService.distributionService.saveActivityDistributionSettings("discount", "temp-id-123", discountForm.distributionSettings);
        if (!success) {
          return;
        }
      }
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "创建成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    }
    const handlePromotionCompleted = (data) => {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-marketing/pages/marketing/discount/create.vue:457", "推广操作完成:", data);
      if (data.action === "publish") {
        common_vendor.index.showToast({
          title: "发布成功",
          icon: "success"
        });
      } else if (data.action === "top") {
        common_vendor.index.showToast({
          title: "置顶成功",
          icon: "success"
        });
      } else if (data.action === "refresh") {
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }
    };
    common_vendor.onMounted(() => {
      addRule();
      checkMerchantDistribution();
    });
    return {
      discountForm,
      showDatePickerDialog,
      currentDatePicker,
      goBack,
      showDatePicker,
      closeDatePicker,
      onDateSelect,
      onStatusChange,
      onStackChange,
      addRule,
      deleteRule,
      selectProducts,
      saveDiscount,
      updateDistributionSettings,
      checkMerchantDistribution,
      hasMerchantDistribution,
      handlePromotionCompleted,
      tempDiscountId
    };
  }
};
if (!Array) {
  const _component_distribution_setting = common_vendor.resolveComponent("distribution-setting");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_MarketingPromotionActions = common_vendor.resolveComponent("MarketingPromotionActions");
  const _component_uni_calendar = common_vendor.resolveComponent("uni-calendar");
  (_component_distribution_setting + _component_circle + _component_line + _component_svg + _component_MarketingPromotionActions + _component_uni_calendar)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    b: common_vendor.o((...args) => $setup.saveDiscount && $setup.saveDiscount(...args)),
    c: $setup.discountForm.title,
    d: common_vendor.o(($event) => $setup.discountForm.title = $event.detail.value),
    e: common_vendor.t($setup.discountForm.title.length),
    f: common_vendor.t($setup.discountForm.startDate || "开始日期"),
    g: common_vendor.o(($event) => $setup.showDatePicker("start")),
    h: common_vendor.t($setup.discountForm.endDate || "结束日期"),
    i: common_vendor.o(($event) => $setup.showDatePicker("end")),
    j: common_vendor.t($setup.discountForm.status === "active" ? "启用" : "暂停"),
    k: $setup.discountForm.status === "active",
    l: common_vendor.o((...args) => $setup.onStatusChange && $setup.onStatusChange(...args)),
    m: common_vendor.o((...args) => $setup.addRule && $setup.addRule(...args)),
    n: common_vendor.f($setup.discountForm.rules, (rule, index, i0) => {
      return {
        a: rule.minAmount,
        b: common_vendor.o(($event) => rule.minAmount = $event.detail.value, index),
        c: rule.discountAmount,
        d: common_vendor.o(($event) => rule.discountAmount = $event.detail.value, index),
        e: common_vendor.o(($event) => $setup.deleteRule(index), index),
        f: index
      };
    }),
    o: $setup.discountForm.rules.length === 0
  }, $setup.discountForm.rules.length === 0 ? {} : {}, {
    p: common_vendor.t($setup.discountForm.applicableProducts || "全部商品"),
    q: common_vendor.o((...args) => $setup.selectProducts && $setup.selectProducts(...args)),
    r: $setup.discountForm.canStack,
    s: common_vendor.o((...args) => $setup.onStackChange && $setup.onStackChange(...args)),
    t: $setup.discountForm.perPersonLimit,
    v: common_vendor.o(($event) => $setup.discountForm.perPersonLimit = $event.detail.value),
    w: $setup.discountForm.instructions,
    x: common_vendor.o(($event) => $setup.discountForm.instructions = $event.detail.value),
    y: common_vendor.t($setup.discountForm.instructions.length),
    z: $setup.hasMerchantDistribution
  }, $setup.hasMerchantDistribution ? {
    A: common_vendor.o($setup.updateDistributionSettings),
    B: common_vendor.p({
      ["initial-settings"]: $setup.discountForm.distributionSettings
    })
  } : {}, {
    C: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    D: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12",
      y2: "12"
    }),
    E: common_vendor.p({
      x1: "12",
      y1: "16",
      x2: "12.01",
      y2: "16"
    }),
    F: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    G: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    H: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12",
      y2: "12"
    }),
    I: common_vendor.p({
      x1: "12",
      y1: "16",
      x2: "12.01",
      y2: "16"
    }),
    J: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    K: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    L: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12",
      y2: "12"
    }),
    M: common_vendor.p({
      x1: "12",
      y1: "16",
      x2: "12.01",
      y2: "16"
    }),
    N: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    O: common_vendor.o($setup.handlePromotionCompleted),
    P: common_vendor.p({
      ["activity-type"]: "discount",
      ["activity-id"]: $setup.tempDiscountId,
      ["publish-mode-only"]: true,
      ["show-actions"]: ["publish"]
    }),
    Q: common_vendor.o((...args) => $setup.saveDiscount && $setup.saveDiscount(...args)),
    R: $setup.showDatePickerDialog
  }, $setup.showDatePickerDialog ? {
    S: common_vendor.o($setup.onDateSelect),
    T: common_vendor.o($setup.closeDatePicker),
    U: common_vendor.p({
      insert: false,
      ["start-date"]: "2020-01-01",
      ["end-date"]: "2030-12-31"
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/discount/create.js.map
