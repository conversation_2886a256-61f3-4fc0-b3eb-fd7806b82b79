<template>
  <view class="service-list-container ios-style">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="back-btn" @click="navigateBack">
        <view class="back-icon"></view>
      </view>
      <view class="navbar-title">{{ currentCategory || '家政服务' }}</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 分类标签栏 -->
    <view class="category-tabs">
      <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false" :scroll-into-view="activeTabId">
        <view 
          v-for="(category, index) in categories" 
          :key="index" 
          :id="'tab-' + index"
          class="tab-item" 
          :class="{ active: currentCategory === category }"
          @click="switchCategory(category)"
        >
          {{ category }}
        </view>
      </scroll-view>
    </view>
    
    <!-- 筛选栏 -->
    <view class="filter-section">
      <!-- 区域筛选 -->
      <view class="filter-item" ref="areaBtn" @click="toggleAreaFilter">
        <text class="filter-text" :class="{ 'active-filter': selectedArea !== '全部区域' }">
          {{selectedArea}}
        </text>
        <view class="filter-arrow" :class="{ 'arrow-up': showAreaFilter }"></view>
      </view>
      
      <!-- 排序筛选 -->
      <view class="filter-item" ref="sortBtn" @click="toggleSortFilter">
        <text class="filter-text" :class="{ 'active-filter': selectedSort !== '默认排序' }">
          {{selectedSort}}
        </text>
        <view class="filter-arrow" :class="{ 'arrow-up': showSortFilter }"></view>
      </view>
      </view>
    
    <!-- 区域筛选弹出内容 -->
    <view class="filter-dropdown area-dropdown" v-if="showAreaFilter" :style="{ top: areaDropdownTop + 'px' }">
      <scroll-view scroll-y class="dropdown-scroll">
        <view class="dropdown-item" 
          v-for="(area, index) in areaList" 
          :key="index"
          :class="{ 'active-item': area === selectedArea }"
          @click="selectArea(area)">
          <text class="dropdown-item-text">{{area}}</text>
          <text class="dropdown-item-check" v-if="area === selectedArea">✓</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 排序筛选弹出内容 -->
    <view class="filter-dropdown sort-dropdown" v-if="showSortFilter" :style="{ top: sortDropdownTop + 'px' }">
      <view class="dropdown-item" 
        v-for="(sort, index) in sortList" 
        :key="index"
        :class="{ 'active-item': sort === selectedSort }"
        @click="selectSort(sort)">
        <text class="dropdown-item-text">{{sort}}</text>
        <text class="dropdown-item-check" v-if="sort === selectedSort">✓</text>
      </view>
    </view>
    
    <!-- 遮罩层 -->
    <view class="filter-mask" 
      v-if="showAreaFilter || showSortFilter"
      @click="closeAllFilters"></view>
    
    <!-- 服务列表 -->
    <scroll-view class="service-scroll" scroll-y="true" @scrolltolower="loadMore">
      <view class="service-list">
        <!-- 空状态 -->
        <view v-if="serviceList.length === 0 && !isLoading" class="empty-state">
          <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无相关服务信息</text>
        </view>
        
        <!-- 服务卡片列表 -->
        <view 
          v-for="(service, index) in serviceList" 
          :key="index" 
          class="service-item"
          @click="navigateToDetail(service.id)"
        >
          <view class="service-content">
            <view class="service-header">
              <view class="service-tag-container">
                <text class="service-tag">{{ service.category }}</text>
            </view>
              <text class="service-time">{{ service.time }}</text>
            </view>
            
            <text class="service-title">{{ service.title }}</text>
            
            <view class="service-images" v-if="service.images && service.images.length > 0">
              <image 
                v-for="(img, imgIndex) in service.images.slice(0, 3)" 
                :key="imgIndex" 
                class="service-image"
                :src="img" 
                mode="aspectFill" 
              ></image>
            </view>
            
            <view class="service-info-card">
              <view class="info-row">
                <view class="info-item">
                  <text class="info-label">服务区域</text>
                  <text class="info-value">{{ service.area || '全城' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">价格参考</text>
                  <text class="info-value highlight">{{ service.price || '面议' }}</text>
                </view>
              </view>
            </view>
            
            <view class="service-footer">
              <view class="service-meta">
                <view class="views-icon"></view>
                <text class="service-views">{{ service.views }}次浏览</text>
              </view>
              <view class="service-actions">
                <view class="action-btn" @click.stop="contactService(service.id)">
                  <view class="contact-icon"></view>
                  <text class="action-text">立即联系</text>
            </view>
          </view>
        </view>
      </view>
      </view>
      
        <!-- 加载更多 -->
        <view v-if="loading && serviceList.length > 0" class="loading-more">
          <view class="loading-indicator"></view>
        <text class="loading-text">加载中...</text>
      </view>
        
        <!-- 没有更多数据 -->
        <view v-if="!hasMore && serviceList.length > 0" class="loading-more">
          <text class="loading-text">没有更多数据了</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 发布按钮 -->
    <view class="publish-btn" @click="navigateToPublish">
      <text class="publish-icon">+</text>
      <text class="publish-text">发布服务</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted } from 'vue';

// 状态变量
const statusBarHeight = ref(20);
const currentTab = ref(0);
const sortBy = ref('latest');
const isRefreshing = ref(false);
const hasMore = ref(true);
const page = ref(1);
const limit = ref(10);
const serviceList = ref([]);
const subCategories = ref([
  { name: '全部', type: 'all' },
  { name: '家政服务', type: 'home_cleaning' },
  { name: '维修改造', type: 'repair' },
  { name: '上门安装', type: 'installation' },
  { name: '开锁换锁', type: 'locksmith' },
  { name: '搬家拉货', type: 'moving' },
  { name: '上门美容', type: 'beauty' },
  { name: '上门家教', type: 'tutor' },
  { name: '宠物服务', type: 'pet_service' },
  { name: '上门疏通', type: 'plumbing' },
  { name: '其他类型', type: 'other' }
]);
// 为每个子分类添加示例数据，实际应从数据库获取
const sampleServiceData = ref([]);
const currentCategory = ref('全部');
const categories = ref(['全部', '家政服务', '维修改造', '上门安装', '开锁换锁', '搬家拉货', '上门美容', '上门家教', '宠物服务', '上门疏通', '其他类型']);
const sortType = ref('time');
const filterActive = ref(false);
const loading = ref(false);
const isLoading = ref(false);

// 新增筛选相关数据
const showAreaFilter = ref(false);
const showSortFilter = ref(false);
const selectedArea = ref('全部区域');
const selectedSort = ref('默认排序');
const areaDropdownTop = ref(120); // 区域筛选弹窗位置
const sortDropdownTop = ref(120); // 排序筛选弹窗位置

// 筛选选项
const areaList = ref(['全部区域', '城区', '磁州镇', '讲武城镇', '岳城镇', '观台镇', '白土镇', '黄沙镇']);
const sortList = ref(['默认排序', '离我最近', '最新发布', '热门推荐', '价格最低', '价格最高']);

// 当前用户位置
const userLocation = ref(null);

// 示例变量，用于演示目的
const demoServiceCategory = ref('上门安装');

// 当前活动tab的ID，用于scroll-into-view
const activeTabId = ref('tab-0');

// 初始化示例数据
const initSampleData = () => {
  sampleServiceData.value = [
    {
      id: '1',
      type: 'home_service',
      serviceType: 'home_cleaning',
      serviceTypeName: '家政服务',
      content: '专业保洁团队，提供家庭、办公室清洁服务，价格实惠',
      time: '2023-06-30 10:25',
      views: 125,
      images: ['/static/images/sample/cleaning1.jpg', '/static/images/sample/cleaning2.jpg']
    },
    {
      id: '2',
      type: 'home_service',
      serviceType: 'repair',
      serviceTypeName: '维修改造',
      content: '水电维修，空调维修，灯具安装，墙面翻新等各类家庭维修服务',
      time: '2023-06-29 15:40',
      views: 98,
      images: ['/static/images/sample/repair1.jpg']
    },
    {
      id: '3',
      type: 'home_service',
      serviceType: 'installation',
      serviceTypeName: '上门安装',
      content: '家具组装，家电安装，网络设备安装，提供专业上门服务',
      time: '2023-06-28 09:15',
      views: 76
    },
    {
      id: '4',
      type: 'home_service',
      serviceType: 'locksmith',
      serviceTypeName: '开锁换锁',
      content: '24小时开锁服务，换锁，修锁，保险柜开锁，汽车开锁',
      time: '2023-06-27 21:05',
      views: 112,
      images: ['/static/images/sample/lock1.jpg', '/static/images/sample/lock2.jpg']
    },
    {
      id: '5',
      type: 'home_service',
      serviceType: 'moving',
      serviceTypeName: '搬家拉货',
      content: '专业搬家服务，小型搬家，大件运输，长途托运，价格合理',
      time: '2023-06-26 14:30',
      views: 88
    },
    {
      id: '6',
      type: 'home_service',
      serviceType: 'beauty',
      serviceTypeName: '上门美容',
      content: '专业美甲美睫，上门服务，使用进口产品，安全卫生',
      time: '2023-06-25 16:20',
      views: 143,
      images: ['/static/images/sample/beauty1.jpg']
    },
    {
      id: '7',
      type: 'home_service',
      serviceType: 'tutor',
      serviceTypeName: '上门家教',
      content: '小学初中高中各科家教，有经验的老师，耐心负责',
      time: '2023-06-24 11:00',
      views: 67
    },
    {
      id: '8',
      type: 'home_service',
      serviceType: 'pet_service',
      serviceTypeName: '宠物服务',
      content: '宠物洗澡，美容，寄养，上门遛狗，专业服务',
      time: '2023-06-23 13:45',
      views: 124,
      images: ['/static/images/sample/pet1.jpg', '/static/images/sample/pet2.jpg']
    },
    {
      id: '9',
      type: 'home_service',
      serviceType: 'plumbing',
      serviceTypeName: '上门疏通',
      content: '管道疏通，马桶疏通，下水道疏通，快速上门',
      time: '2023-06-22 08:30',
      views: 92
    },
    {
      id: '10',
      type: 'home_service',
      serviceType: 'other',
      serviceTypeName: '其他类型',
      content: '提供各类上门服务，有需要请联系，价格面议',
      time: '2023-06-21 17:10',
      views: 78,
      images: ['/static/images/sample/other1.jpg']
    }
  ];
};

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 切换标签
const switchTab = (index) => {
  if (currentTab.value !== index) {
    currentTab.value = index;
    page.value = 1;
    serviceList.value = [];
    hasMore.value = true;
    loadServiceList();
  }
};

// 切换排序方式
const toggleSort = (sort) => {
  if (sortBy.value !== sort) {
    sortBy.value = sort;
    page.value = 1;
    serviceList.value = [];
    hasMore.value = true;
    loadServiceList();
  }
};

// 加载服务列表数据
const loadServiceList = async () => {
  if (loading.value || !hasMore.value) return;
  
  loading.value = true;
  
  try {
    // 减少模拟延迟时间，提高响应速度
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 获取模拟数据（每次最多获取5条，减少渲染压力）
    const newData = getMockServiceData();
    
    // 根据当前分类筛选数据
    let filteredData = newData;
    if (currentCategory.value !== '全部') {
      filteredData = newData.filter(item => item.category === currentCategory.value);
    }
    
    // 根据选择的区域筛选
    if (selectedArea.value !== '全部区域') {
      filteredData = filteredData.filter(item => item.area === selectedArea.value || 
                              (item.area && item.area.includes(selectedArea.value)));
    }
    
    // 根据排序方式处理数据
    applySorting(filteredData);
    
    // 更新列表数据
    if (page.value === 1) {
      serviceList.value = filteredData;
    } else {
      serviceList.value = [...serviceList.value, ...filteredData];
    }
    
    // 更新分页信息
    hasMore.value = filteredData.length >= limit.value;
    page.value++;
    
  } catch (error) {
    console.error('加载服务列表失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
    isRefreshing.value = false;
  }
};

// 提取排序逻辑为单独方法，优化代码结构
const applySorting = (data) => {
  if (!data || data.length === 0) return;
  
  if (sortType.value === 'popular') {
    data.sort((a, b) => b.views - a.views);
  } else if (sortType.value === 'price_low') {
    // 价格低到高排序（去掉价格单位，仅比较数字部分）
    data.sort((a, b) => {
      const priceA = parseFloat(a.price ? a.price.replace(/[^\d.]/g, '') : '99999');
      const priceB = parseFloat(b.price ? b.price.replace(/[^\d.]/g, '') : '99999');
      return priceA - priceB;
    });
  } else if (sortType.value === 'price_high') {
    // 价格高到低排序
    data.sort((a, b) => {
      const priceA = parseFloat(a.price ? a.price.replace(/[^\d.]/g, '') : '0');
      const priceB = parseFloat(b.price ? b.price.replace(/[^\d.]/g, '') : '0');
      return priceB - priceA;
    });
  } else if (sortType.value === 'distance' && userLocation.value) {
    // 按距离排序
    data.sort((a, b) => {
      // 计算两个服务到用户的距离
      const distanceA = calculateDistance(a.location || getLocationFromArea(a.area));
      const distanceB = calculateDistance(b.location || getLocationFromArea(b.area));
      return distanceA - distanceB;
    });
  }
  
  return data;
};

const getMockServiceData = () => {
  // 模拟服务数据 - 减少返回数据量，提高渲染速度
  const mockServices = [
    {
      id: '101',
      category: '家政服务',
      title: '专业家庭保洁 深度清洁 开荒保洁 玻璃清洗',
      time: '2小时前',
      views: 128,
      area: '城区',
      price: '80元/小时',
      images: [
        '/static/images/service/cleaning1.jpg'
      ],
      location: { latitude: 36.358, longitude: 114.518 }
    },
    {
      id: '102',
      category: '维修改造',
      title: '专业水电维修安装 水管漏水 马桶疏通 灯具维修',
      time: '3小时前',
      views: 95,
      area: '高新区',
      price: '上门费 30元',
      images: [
        '/static/images/service/repair1.jpg'
      ],
      location: { latitude: 36.368, longitude: 114.528 }
    },
    {
      id: '103',
      category: '上门安装',
      title: '专业安装窗帘 晾衣架 各类家具 价格实惠 服务好',
      time: '昨天',
      views: 210,
      area: '全城',
      price: '面议',
      images: [
        '/static/images/service/install1.jpg'
      ],
      location: { latitude: 36.354, longitude: 114.514 }
    },
    {
      id: '104',
      category: '搬家拉货',
      title: '专业小型搬家 单身公寓 居民搬家 长短途运输',
      time: '3天前',
      views: 156,
      area: '磁州镇',
      price: '98元起',
      images: [
        '/static/images/service/moving1.jpg'
      ],
      location: { latitude: 36.378, longitude: 114.553 }
    },
    {
      id: '105',
      category: '开锁换锁',
      title: '专业开锁换锁 汽车锁 保险柜 指纹密码锁安装',
      time: '4天前',
      views: 86,
      area: '岳城镇',
      price: '开锁 50元起',
      images: [
        '/static/images/service/lock1.jpg'
      ],
      location: { latitude: 36.398, longitude: 114.493 }
    }
  ];
  
  // 根据分类过滤
  let filteredData = mockServices;
  if (currentCategory.value !== '全部') {
    filteredData = mockServices.filter(item => item.category === currentCategory.value);
  }
  
  // 减少每页返回的数据量
  const pageSize = Math.min(5, limit.value);
  return filteredData.slice(0, pageSize);
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  page.value = 1;
  hasMore.value = true;
  loadServiceList();
};

// 加载更多
const loadMore = () => {
  loadServiceList();
};

// 跳转到详情页
const navigateToDetail = (id) => {
  // 找到对应的服务
  const service = serviceList.value.find(item => item.id === id);
  if (!service) return;
  
  // 获取服务的分类，如果服务项没有分类则使用当前选中的分类
  const serviceCategory = service.category || currentCategory.value || '家政服务';
  console.log('跳转到详情页 - 服务ID:', id, '分类:', serviceCategory);
  
  // 根据分类名称获取对应的服务类型代码
  let serviceType = service.serviceType || '';
  if (!serviceType) {
    switch(serviceCategory) {
      case '开锁换锁':
        serviceType = 'locksmith';
        break;
      case '上门安装':
        serviceType = 'installation';
        break;
      case '维修改造':
        serviceType = 'repair';
        break;
      case '搬家拉货':
        serviceType = 'moving';
        break;
      case '上门美容':
        serviceType = 'beauty';
        break;
      case '上门家教':
        serviceType = 'tutor';
        break;
      case '宠物服务':
        serviceType = 'pet_service';
        break;
      case '上门疏通':
        serviceType = 'plumbing';
        break;
      case '家政保洁':
      case '家政服务':
      default:
        serviceType = 'home_cleaning';
        break;
    }
  }
  
  // 添加触感反馈
  uni.vibrateShort && uni.vibrateShort();
  
  // 显示加载提示
  uni.showLoading({
    title: '正在加载...',
    mask: true
  });
  
  // 构建完整的URL，确保所有必要参数都被传递
  const url = `/pages/publish/home-service-detail?id=${id}&category=${encodeURIComponent(serviceCategory)}&type=${serviceType}`;
  console.log('详情页跳转URL:', url);
  
  // 执行跳转
  uni.navigateTo({
    url: url,
    success: () => {
      console.log('跳转成功');
      uni.hideLoading();
    },
    fail: (err) => {
      console.error('跳转失败:', err);
      uni.hideLoading();
      uni.showToast({
        title: '页面跳转失败，请重试',
        icon: 'none'
      });
    }
  });
};

// 跳转到发布页
const navigateToPublish = () => {
  uni.navigateTo({
    url: '/pages/publish/service-category'
  });
};

const switchCategory = (category) => {
  if (currentCategory.value !== category) {
    currentCategory.value = category;
    page.value = 1;
    serviceList.value = [];
    hasMore.value = true;
    loadServiceList();
    
    // 设置活动tab的ID
    const index = categories.value.findIndex(item => item === category);
    if (index >= 0) {
      activeTabId.value = 'tab-' + index;
    }
  }
};

const sortByTime = () => {
  sortType.value = 'time';
  page.value = 1;
  serviceList.value = [];
  hasMore.value = true;
  loadServiceList();
};

const sortByPopular = () => {
  sortType.value = 'popular';
  page.value = 1;
  serviceList.value = [];
  hasMore.value = true;
  loadServiceList();
};

const openFilter = () => {
  filterActive.value = !filterActive.value;
  
  // 添加触感反馈
  uni.vibrateShort();
  
  // 打开筛选页面
  uni.navigateTo({
    url: `/subPackages/service/pages/filter?type=home&title=${encodeURIComponent('到家服务')}&category=${encodeURIComponent(currentCategory.value)}`
  });
};

const contactService = (id) => {
  // 阻止冒泡
  event.stopPropagation();
  
  // 找到对应的服务
  const service = serviceList.value.find(item => item.id === id);
  if (!service) return;
  
  // 显示联系方式对话框
  uni.showModal({
    title: '联系方式',
    content: '电话: 188****1234\n微信: same-as-phone',
    confirmText: '拨打电话',
    cancelText: '复制微信',
    success: (res) => {
      if (res.confirm) {
        // 拨打电话
        uni.makePhoneCall({
          phoneNumber: '18812341234'
        });
      } else if (res.cancel) {
        // 复制微信号
        uni.setClipboardData({
          data: 'same-as-phone',
          success: () => {
            uni.showToast({
              title: '微信号已复制',
              icon: 'none'
            });
          }
        });
      }
    }
  });
};

// 选择区域
const selectArea = (area) => {
  // 触感反馈
  uni.vibrateShort();
  
  selectedArea.value = area;
  showAreaFilter.value = false;
  resetListAndReload();
};

// 选择排序方式
const selectSort = (sort) => {
  // 触感反馈
  uni.vibrateShort();
  
  selectedSort.value = sort;
  showSortFilter.value = false;
  resetListAndReload();
  
  // 根据选择的排序设置内部排序类型
  if (sort === '最新发布') {
    sortType.value = 'time';
  } else if (sort === '热门推荐') {
    sortType.value = 'popular';
  } else if (sort === '价格最低') {
    sortType.value = 'price_low';
  } else if (sort === '价格最高') {
    sortType.value = 'price_high';
  } else if (sort === '离我最近') {
    sortType.value = 'distance';
    sortByDistance();
  } else {
    sortType.value = 'default';
  }
};

// 按距离排序
const sortByDistance = () => {
  // 获取当前位置
  uni.getLocation({
    type: 'gcj02',
    success: (res) => {
      const location = {
        latitude: res.latitude,
        longitude: res.longitude
      };
      
      // 存储当前位置
      userLocation.value = location;
      
      // 刷新列表
      resetListAndReload();
    },
    fail: (err) => {
      console.error('获取位置失败', err);
      uni.showToast({
        title: '无法获取位置信息，请检查定位权限',
        icon: 'none'
      });
      
      // 如果获取位置失败，改回默认排序
      selectedSort.value = '默认排序';
      sortType.value = 'default';
    }
  });
};

// 关闭所有筛选弹窗
const closeAllFilters = () => {
  showAreaFilter.value = false;
  showSortFilter.value = false;
};

// 重置列表并重新加载
const resetListAndReload = () => {
  page.value = 1;
  serviceList.value = [];
  hasMore.value = true;
  loadServiceList();
};

// 根据地区名称获取模拟位置坐标
const getLocationFromArea = (area) => {
  // 这里模拟不同区域的地理坐标
  const locationMap = {
    '城区': { latitude: 36.354, longitude: 114.511 },
    '磁州镇': { latitude: 36.374, longitude: 114.551 },
    '讲武城镇': { latitude: 36.334, longitude: 114.471 },
    '岳城镇': { latitude: 36.394, longitude: 114.491 },
    '观台镇': { latitude: 36.314, longitude: 114.531 },
    '白土镇': { latitude: 36.284, longitude: 114.501 },
    '黄沙镇': { latitude: 36.404, longitude: 114.461 },
    '全城': { latitude: 36.354, longitude: 114.511 },
    '高新区': { latitude: 36.368, longitude: 114.528 }
  };
  
  return locationMap[area] || { latitude: 36.354, longitude: 114.511 };
};

// 计算两点之间的距离（使用Haversine公式计算球面距离）
const calculateDistance = (serviceLocation) => {
  if (!userLocation.value || !serviceLocation) {
    return Number.MAX_VALUE; // 返回最大值，排到最后
  }
  
  // 地球半径（单位：千米）
  const R = 6371;
  
  // 将经纬度转换为弧度
  const lat1 = userLocation.value.latitude * Math.PI / 180;
  const lat2 = serviceLocation.latitude * Math.PI / 180;
  const dLat = (serviceLocation.latitude - userLocation.value.latitude) * Math.PI / 180;
  const dLon = (serviceLocation.longitude - userLocation.value.longitude) * Math.PI / 180;
  
  // 使用Haversine公式计算两点之间的距离
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos(lat1) * Math.cos(lat2) * 
          Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return distance;
};

// 添加自动滚动到选中分类的方法
const scrollToCategory = () => {
  try {
    // 获取当前分类的索引
    const index = categories.value.findIndex(item => item === currentCategory.value);
    if (index >= 0) {
      // 设置activeTabId触发scroll-into-view
      activeTabId.value = 'tab-' + index;
      console.log('设置滚动到:', activeTabId.value);
    }
  } catch (e) {
    console.error('自动滚动失败:', e);
  }
};

// 示例方法：在服务详情页中如何实现"查看更多信息"的跳转
// 这个方法应该在服务详情页实现，这里仅作为示例
const exampleViewMoreFromDetail = () => {
  // 假设当前是在服务详情页，category是当前服务的分类
  const category = demoServiceCategory.value; // 例如"上门安装"
  
  // 跳转到服务列表页，并传递分类参数
  uni.navigateTo({
    url: `/subPackages/service/pages/home-service-list?category=${encodeURIComponent(category)}`
  });
  
  // 注意：在实际的详情页面中，应该这样使用：
  // uni.navigateTo({
  //   url: `/subPackages/service/pages/home-service-list?category=${encodeURIComponent(serviceData.category)}`
  // });
};

// 切换区域筛选
const toggleAreaFilter = () => {
  showAreaFilter.value = !showAreaFilter.value;
  showSortFilter.value = false;
  
  if (showAreaFilter.value) {
    // 动态获取按钮底部位置
    nextTick(() => {
      const query = uni.createSelectorQuery();
      query.select('.filter-item[ref=areaBtn]').boundingClientRect(rect => {
        if (rect) {
          areaDropdownTop.value = rect.bottom;
        }
      }).exec();
    });
  }
};

// 切换排序筛选
const toggleSortFilter = () => {
  showSortFilter.value = !showSortFilter.value;
  showAreaFilter.value = false;
  
  if (showSortFilter.value) {
    // 动态获取按钮底部位置
    nextTick(() => {
      const query = uni.createSelectorQuery();
      query.select('.filter-item[ref=sortBtn]').boundingClientRect(rect => {
        if (rect) {
          sortDropdownTop.value = rect.bottom;
        }
      }).exec();
    });
  }
};

// 页面加载时执行
onMounted(() => {
  // 获取状态栏高度
  const sysInfo = uni.getSystemInfoSync();
  statusBarHeight.value = sysInfo.statusBarHeight;
  
  // 获取路由参数
  const pages = getCurrentPages();
  const page = pages[pages.length - 1];
  const options = page.$page?.options || {};
  
  // 初始化示例数据 - 改为延迟初始化，减轻页面初始加载压力
  setTimeout(() => {
    initSampleData();
  }, 100);
  
  // 如果有子分类参数，自动切换到对应分类
  if (options.subType && options.subName) {
    // 设置当前分类
    currentCategory.value = decodeURIComponent(options.subName);
    // 重置页码
    page.value = 1;
    serviceList.value = [];
    hasMore.value = true;
    
    // 设置activeTabId
    const index = categories.value.findIndex(item => item === currentCategory.value);
    if (index >= 0) {
      activeTabId.value = 'tab-' + index;
    }
  } else if (options.category) {
    // 如果直接传递了分类名称参数
    const categoryName = decodeURIComponent(options.category);
    // 检查是否存在于分类列表中
    if (categories.value.includes(categoryName)) {
      currentCategory.value = categoryName;
      page.value = 1;
      serviceList.value = [];
      hasMore.value = true;
      
      // 设置activeTabId
      const index = categories.value.findIndex(item => item === currentCategory.value);
      if (index >= 0) {
        activeTabId.value = 'tab-' + index;
      }
    }
  }
  
  // 加载服务列表数据 - 延迟加载以减轻初始渲染压力
  setTimeout(() => {
    loadServiceList();
    
    // 自动滚动到选中的分类
    nextTick(() => {
      scrollToCategory();
    });
  }, 200);
});

// 下拉刷新处理
uni.onPullDownRefresh(() => {
  page.value = 1;
  serviceList.value = [];
  hasMore.value = true;
  loadServiceList();
  setTimeout(() => {
    uni.stopPullDownRefresh();
  }, 1000);
});
</script>

<style lang="scss" scoped>
.service-list-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background: #1677FF;
  color: #fff;
  padding: 0 15px;
  position: relative;
  z-index: 100;
  box-shadow: none;
  border-radius: 0;
}

.back-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  width: 10px;
  height: 10px;
  border-top: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 40px;
}

/* 分类标签栏 */
.category-tabs {
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: none;
  border-bottom: 1rpx solid #f5f5f5;
  margin-top: 0;
}

.tabs-scroll {
  white-space: nowrap;
  height: 80rpx;
  padding: 0 10rpx;
}

.tab-item {
  display: inline-block;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #333;
  position: relative;
}

.tab-item.active {
  color: #1677FF;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1677FF;
  border-radius: 2rpx;
}

/* 筛选栏 */
.filter-section {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.filter-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 30rpx;
  background-color: #f0f0f0;
}

.filter-text {
  font-size: 28rpx;
  color: #666;
}

.active-filter {
  color: #1677FF;
  font-weight: 500;
}

.filter-arrow {
  width: 14rpx;
  height: 14rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(45deg);
  margin-left: 8rpx;
  margin-top: -6rpx;
  transition: transform 0.2s;
}

.filter-arrow.arrow-up {
  transform: rotate(-135deg);
  margin-top: 6rpx;
  border-right: 2rpx solid #1677FF;
  border-bottom: 2rpx solid #1677FF;
}

.filter-icon {
  width: 24rpx;
  height: 24rpx;
  border: none;
  position: relative;
  margin-left: 8rpx;
  transform: none;
}

.filter-icon::before, .filter-icon::after {
  content: '';
  position: absolute;
  background-color: #999;
}

.filter-icon::before {
  width: 100%;
  height: 2rpx;
  top: 6rpx;
  left: 0;
}

.filter-icon::after {
  width: 100%;
  height: 2rpx;
  bottom: 6rpx;
  left: 0;
}

.filter-icon.active-icon::before, .filter-icon.active-icon::after {
  background-color: #1677FF;
}

/* 下拉菜单样式 */
.filter-dropdown {
  position: absolute;
  background-color: #fff;
  z-index: 101;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  max-height: 600rpx;
  animation: dropDown 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
  border-radius: 0 0 12rpx 12rpx;
  overflow: hidden;
}

@keyframes dropDown {
  from { transform: translateY(-8rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.dropdown-scroll {
  max-height: 600rpx;
}

.dropdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.dropdown-item.active-item {
  color: #1677FF;
}

.dropdown-item-text {
  font-size: 28rpx;
}

.dropdown-item-check {
  color: #1677FF;
  font-weight: bold;
}

.dropdown-title {
  font-size: 28rpx;
  font-weight: 500;
}

/* 遮罩层 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 100;
}

/* iOS风格服务列表 */
.service-scroll {
  flex: 1;
  overflow: hidden;
  padding: 0;
  margin-bottom: 16px;
}

.service-list {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.service-item {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
  width: 94%;
}

.service-item:active {
  transform: scale(0.98);
}

.service-content {
  padding: 16px;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.service-tag-container {
  position: relative;
}

.service-tag {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007aff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.service-time {
  font-size: 12px;
  color: #8e8e93;
}

.service-title {
  font-size: 16px;
  line-height: 1.4;
  color: #000;
  margin-bottom: 12px;
  font-weight: 500;
  /* 标题最多显示两行，超出部分显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.service-images {
  display: flex;
  margin-bottom: 12px;
  gap: 8px;
  justify-content: center;
}

.service-image {
  width: 102px;
  height: 102px;
  border-radius: 6px;
  object-fit: cover;
}

/* iOS风格信息卡片 */
.service-info-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 10px 12px;
  margin-bottom: 10px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
}

.info-label {
  font-size: 12px;
  color: #8e8e93;
}

.info-value {
  font-size: 14px;
  color: #1c1c1e;
  font-weight: 500;
}

.info-value.highlight {
  color: #ff3b30;
  font-weight: 600;
}

/* iOS风格底部栏 */
.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: 6px;
}

.service-meta {
  display: flex;
  align-items: center;
}

.views-icon {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%238e8e93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 4px;
}

.service-views {
  font-size: 13px;
  color: #8e8e93;
}

.service-actions {
  display: flex;
}

.action-btn {
  display: flex;
  align-items: center;
  background: #007aff;
  padding: 6px 12px;
  border-radius: 16px;
  transition: background-color 0.2s ease;
}

.action-btn:active {
  background: #0062cc;
}

.action-text {
  font-size: 13px;
  color: #fff;
  font-weight: 500;
}

.contact-icon {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 4px;
}

/* iOS风格空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  margin-top: 20px;
  width: 100%;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
  opacity: 0.8;
}

.empty-text {
  font-size: 16px;
  color: #8e8e93;
  margin-bottom: 8px;
}

.empty-tips {
  font-size: 14px;
  color: #aeaeb2;
}

/* iOS风格发布按钮 */
.publish-btn {
  position: fixed;
  right: 16px;
  bottom: 50px;
  background: #007aff;
  width: 120px;
  height: 42px;
  border-radius: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
  z-index: 100;
}

.publish-btn:active {
  background: #0062cc;
}

.publish-icon {
  font-size: 20px;
  color: #fff;
  margin-right: 4px;
  font-weight: 400;
  line-height: 20px;
}

.publish-text {
  color: #fff;
  font-size: 15px;
  font-weight: 500;
}

/* iOS风格加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  width: 100%;
}

.loading-indicator {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 122, 255, 0.2);
  border-top-color: #007aff;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #8e8e93;
}

/* iOS风格整体样式适配 */
.ios-style {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
  letter-spacing: -0.2px;
}

.ios-style .service-item {
  border: none;
}

@media (hover: hover) {
  .tab-item:hover {
    background-color: rgba(0, 122, 255, 0.05);
  }
  
  .service-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }
  
  .action-btn:hover {
    background: #0062cc;
  }
}

.area-dropdown {
  left: 0;
  width: 50%;
}

.sort-dropdown {
  right: 0;
  width: 50%;
}
</style>