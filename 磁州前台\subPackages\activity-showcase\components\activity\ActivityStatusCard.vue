<template>
  <view class="activity-status-card" :style="cardStyle" @click="$emit('click', activity)">
    <!-- 活动图片 -->
    <view class="card-image-container">
      <image class="card-image" :src="activity.coverImage" mode="aspectFill"></image>
      <view class="card-tag" :class="getTagClass(activity.type)">{{ getTypeText(activity.type) }}</view>
      
      <!-- 收藏按钮 -->
      <view class="favorite-btn" @click.stop="$emit('favorite', activity.id)">
        <svg class="icon" viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
      </view>
      
      <!-- 倒计时 (仅在showCountdown为true时显示) -->
      <view class="countdown-container" v-if="showCountdown && activity.endTime">
        <view class="countdown-label">距结束</view>
        <view class="countdown-timer">
          <view class="time-block">{{ countdownHours }}</view>
          <view class="time-separator">:</view>
          <view class="time-block">{{ countdownMinutes }}</view>
          <view class="time-separator">:</view>
          <view class="time-block">{{ countdownSeconds }}</view>
        </view>
      </view>
      
      <!-- 活动状态指示器 -->
      <view class="status-indicator" :class="getStatusClass(activity.status)">
        <view class="status-dot"></view>
        {{ getStatusText(activity.status) }}
      </view>
    </view>
    
    <!-- 活动信息 -->
    <view class="card-info">
      <view class="card-header">
        <view class="card-title-container">
      <view class="card-title">{{ activity.title }}</view>
          <view class="card-shop">
            <svg class="shop-icon" viewBox="0 0 24 24" width="14" height="14">
              <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0116 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
            </svg>
            <text>{{ activity.shopName }}</text>
          </view>
        </view>
      </view>
      
      <view class="card-details">
        <!-- 价格区域 -->
        <view class="price-section">
          <view class="current-price">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{ activity.currentPrice }}</text>
          </view>
          <view class="price-info" v-if="activity.originalPrice">
            <text class="price-original">¥{{ activity.originalPrice }}</text>
            <text class="discount-tag">{{ discountPercent }}折</text>
          </view>
        </view>
        
        <!-- 活动时间 -->
        <view class="activity-time">
          <svg class="time-icon" viewBox="0 0 24 24" width="14" height="14">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
            <polyline points="12 6 12 12 16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polyline>
            </svg>
          <text>{{ formatDate(activity.startTime) }}</text>
          </view>
          
        <!-- 参与人数 -->
        <view class="participants-info" v-if="activity.participants">
          <view class="avatar-group">
            <image 
              v-for="(participant, idx) in displayParticipants" 
              :key="idx" 
              :src="participant.avatar" 
              class="participant-avatar"
              :style="{zIndex: 10-idx, right: `${idx * 15}rpx`}"
            ></image>
          </view>
          <text class="participant-count">{{ activity.participants.length }}人参与</text>
        </view>
      </view>
      
      <view class="card-actions">
        <view class="action-btn primary-btn" @click.stop="$emit('click', activity)">
          <text>查看详情</text>
        </view>
        <view class="action-btn share-btn" @click.stop="$emit('share', activity)">
          <svg class="icon" viewBox="0 0 24 24" width="18" height="18">
            <path d="M18 8A3 3 0 1 0 15 5.83L8 9.41A3 3 0 0 0 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 2-.76l7 3.58A3 3 0 0 0 15 22a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-2 .76l-7-3.58A3 3 0 0 0 6 12a3 3 0 0 0 0-.17L13 8.24a3 3 0 0 0 2 .76 3 3 0 0 0 3-3z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"></path>
          </svg>
          <text>分享</text>
        </view>
        <view class="action-btn cancel-btn" v-if="canCancel" @click.stop="$emit('cancel', activity)">
          <svg class="icon" viewBox="0 0 24 24" width="18" height="18">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
          <text>取消</text>
        </view>
      </view>
      
      <!-- 进度条 (如果有销售数据) -->
      <view class="progress-container" v-if="activity.soldCount && activity.totalCount">
        <view class="progress-bar">
          <view class="progress-fill" :style="{width: `${Math.min(100, (activity.soldCount / activity.totalCount) * 100)}%`}"></view>
        </view>
        <view class="progress-text">
          <text>已售 {{ activity.soldCount }}/{{ activity.totalCount }}</text>
          <text v-if="activity.totalCount > activity.soldCount">仅剩 {{ activity.totalCount - activity.soldCount }} 件</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 组件属性定义
const props = defineProps({
  activity: {
    type: Object,
    required: true
  },
  showCountdown: {
    type: Boolean,
    default: false
  },
  canCancel: {
    type: Boolean,
    default: false
  },
  cardStyle: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
defineEmits(['click', 'share', 'cancel', 'favorite']);

// 获取活动类型文本
const getTypeText = (type) => {
  switch(type) {
    case 'flash':
      return '秒杀';
    case 'group':
      return '拼团';
    case 'discount':
      return '满减';
    case 'coupon':
      return '优惠券';
    default:
      return '活动';
  }
};

// 获取标签样式类
const getTagClass = (type) => {
  switch(type) {
    case 'flash':
      return 'tag-flash';
    case 'group':
      return 'tag-group';
    case 'discount':
      return 'tag-discount';
    case 'coupon':
      return 'tag-coupon';
    default:
      return '';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch(status) {
    case 'ongoing':
      return '进行中';
    case 'registered':
      return '已报名';
    case 'completed':
      return '已完成';
    case 'favorite':
      return '已收藏';
    case 'cancelled':
      return '已取消';
    default:
      return '';
  }
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch(status) {
    case 'ongoing':
      return 'status-ongoing';
    case 'registered':
      return 'status-registered';
    case 'completed':
      return 'status-completed';
    case 'favorite':
      return 'status-favorite';
    case 'cancelled':
      return 'status-cancelled';
    default:
      return '';
  }
};

// 计算折扣百分比
const discountPercent = computed(() => {
  if (!props.activity.currentPrice || !props.activity.originalPrice) return '';
  return Math.floor((props.activity.currentPrice / props.activity.originalPrice) * 10);
});

// 参与者头像显示
const displayParticipants = computed(() => {
  if (!props.activity.participants) return [];
  return props.activity.participants.slice(0, 3);
});

// 倒计时相关
const countdownHours = ref('00');
const countdownMinutes = ref('00');
const countdownSeconds = ref('00');
let countdownTimer = null;

// 更新倒计时
const updateCountdown = () => {
  if (!props.activity.endTime) return;
  
  const now = new Date().getTime();
  const end = new Date(props.activity.endTime).getTime();
  const diff = end - now;
  
  if (diff <= 0) {
    countdownHours.value = '00';
    countdownMinutes.value = '00';
    countdownSeconds.value = '00';
    clearInterval(countdownTimer);
    return;
  }
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  countdownHours.value = hours.toString().padStart(2, '0');
  countdownMinutes.value = minutes.toString().padStart(2, '0');
  countdownSeconds.value = seconds.toString().padStart(2, '0');
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
};

// 格式化倒计时
const formatCountdown = (endTime) => {
  if (!endTime) return '';
  
  const now = new Date().getTime();
  const end = new Date(endTime).getTime();
  const diff = end - now;
  
  if (diff <= 0) return '已结束';
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  if (days > 0) {
    return `${days}天${hours}小时`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分`;
  } else {
    return `${minutes}分钟`;
  }
};

// 初始化倒计时
onMounted(() => {
  if (props.showCountdown) {
    updateCountdown();
    countdownTimer = setInterval(updateCountdown, 1000);
  }
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});
</script>

<style lang="scss" scoped>
.activity-status-card {
  width: 100%;
  border-radius: 24px;
  background-color: #FFFFFF;
  box-shadow: 0 10px 30px rgba(0,0,0,0.08), 0 0 0 1px rgba(0,0,0,0.02);
  margin-bottom: 30rpx;
  overflow: hidden;
  position: relative;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  &:active {
    transform: scale(0.98) !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05), 0 0 0 1px rgba(0,0,0,0.03);
  }
}

.card-image-container {
  width: 100%;
  height: 360rpx;
  position: relative;
  overflow: hidden;
  
  .card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .card-tag {
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    padding: 8rpx 20rpx;
    font-size: 22rpx;
    font-weight: 600;
    color: #FFFFFF;
    border-radius: 20rpx;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
    z-index: 10;
  }
  
  .favorite-btn {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    z-index: 10;
    color: #FF3B69;
    
    &:active {
      transform: scale(0.9);
    }
  }
  
  .countdown-container {
    position: absolute;
    bottom: 20rpx;
    right: 20rpx;
    background: rgba(0,0,0,0.7);
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    z-index: 10;
    
    .countdown-label {
      font-size: 20rpx;
      color: rgba(255,255,255,0.8);
      margin-bottom: 4rpx;
    }
    
    .countdown-timer {
      display: flex;
      align-items: center;
      
      .time-block {
        width: 40rpx;
        height: 40rpx;
        background: rgba(255,255,255,0.2);
        border-radius: 6rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      font-size: 24rpx;
        font-weight: 600;
        color: #FFFFFF;
      }
      
      .time-separator {
        margin: 0 4rpx;
      color: #FFFFFF;
        font-weight: 600;
      }
    }
  }
  
  .status-indicator {
    position: absolute;
    top: 20rpx;
    right: 100rpx;
    padding: 8rpx 20rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    z-index: 10;
    
    .status-dot {
      width: 8rpx;
      height: 8rpx;
      border-radius: 50%;
      margin-right: 8rpx;
    }
  }
}

.card-info {
  padding: 30rpx;
  position: relative;
  
  .card-header {
    margin-bottom: 20rpx;
    
    .card-title-container {
  .card-title {
    font-size: 32rpx;
        font-weight: 700;
    color: #333333;
        margin-bottom: 10rpx;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
      .card-shop {
    display: flex;
        align-items: center;
      font-size: 24rpx;
      color: #8E8E93;
    
        .shop-icon {
          margin-right: 6rpx;
      color: #8E8E93;
        }
      }
    }
  }
  
  .card-details {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20rpx;
    
    .price-section {
      flex: 1;
      margin-right: 20rpx;
      margin-bottom: 16rpx;
      
      .current-price {
      display: flex;
      align-items: baseline;
      
      .price-symbol {
        font-size: 24rpx;
        color: #FF3B69;
        margin-right: 4rpx;
      }
      
      .price-value {
          font-size: 40rpx;
          font-weight: 700;
        color: #FF3B69;
          line-height: 1;
        }
      }
      
      .price-info {
        display: flex;
        align-items: center;
        margin-top: 8rpx;
      
      .price-original {
        font-size: 24rpx;
        color: #8E8E93;
        text-decoration: line-through;
          margin-right: 10rpx;
        }
        
        .discount-tag {
          padding: 4rpx 10rpx;
          background-color: rgba(255,59,105,0.1);
          border-radius: 10rpx;
          font-size: 20rpx;
          color: #FF3B69;
          font-weight: 600;
        }
      }
    }
    
    .activity-time {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #8E8E93;
      margin-bottom: 16rpx;
      
      .time-icon {
        margin-right: 6rpx;
        color: #8E8E93;
      }
    }
    
    .participants-info {
        display: flex;
        align-items: center;
      margin-top: 16rpx;
      
      .avatar-group {
        position: relative;
        height: 50rpx;
        margin-right: 20rpx;
        
        .participant-avatar {
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
          border: 2rpx solid #FFFFFF;
          position: absolute;
          top: 0;
        }
      }
      
      .participant-count {
        font-size: 24rpx;
        color: #8E8E93;
      }
    }
  }
  
  .card-actions {
    display: flex;
    margin-top: 20rpx;
    
    .action-btn {
      height: 70rpx;
      border-radius: 35rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 30rpx;
      margin-right: 20rpx;
      font-size: 26rpx;
      font-weight: 600;
      transition: all 0.2s ease;
      
      .icon {
        margin-right: 8rpx;
      }
      
      &:active {
        transform: scale(0.95);
      }
    }
    
    .primary-btn {
      background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
      color: #FFFFFF;
      box-shadow: 0 4px 10px rgba(255,59,105,0.3);
      flex: 1;
    }
    
    .share-btn {
      background-color: rgba(255,59,105,0.1);
      color: #FF3B69;
    }
    
    .cancel-btn {
      background-color: rgba(142,142,147,0.1);
      color: #8E8E93;
    }
  }
  
  .progress-container {
    margin-top: 20rpx;
    
    .progress-bar {
      width: 100%;
      height: 10rpx;
      background-color: rgba(0,0,0,0.05);
      border-radius: 5rpx;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%);
        border-radius: 5rpx;
        transition: width 0.5s ease;
      }
    }
    
    .progress-text {
      display: flex;
      justify-content: space-between;
      margin-top: 8rpx;
    font-size: 22rpx;
      color: #8E8E93;
    }
  }
}

/* 标签样式 */
.tag-flash {
  background: linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%);
}

.tag-group {
  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
}

.tag-discount {
  background: linear-gradient(135deg, #5856D6 0%, #5E5CE6 100%);
}

.tag-coupon {
  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);
}

/* 状态样式 */
.status-ongoing {
  color: #34C759;
  background-color: rgba(52,199,89,0.9);
  color: #FFFFFF;
  
  .status-dot {
    background-color: #FFFFFF;
    box-shadow: 0 0 0 2rpx rgba(255,255,255,0.5);
    animation: pulse 2s infinite;
  }
}

.status-registered {
  background-color: rgba(255,59,105,0.9);
  color: #FFFFFF;
  
  .status-dot {
    background-color: #FFFFFF;
  }
}

.status-completed {
  background-color: rgba(142,142,147,0.9);
  color: #FFFFFF;
  
  .status-dot {
    background-color: #FFFFFF;
  }
}

.status-favorite {
  background-color: rgba(255,149,0,0.9);
  color: #FFFFFF;
  
  .status-dot {
    background-color: #FFFFFF;
  }
}

.status-cancelled {
  background-color: rgba(255,59,48,0.9);
  color: #FFFFFF;
  
  .status-dot {
    background-color: #FFFFFF;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style> 