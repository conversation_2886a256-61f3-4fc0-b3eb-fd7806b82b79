/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-d4b650e0, html.data-v-d4b650e0, #app.data-v-d4b650e0, .index-container.data-v-d4b650e0 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.search-page.data-v-d4b650e0 {
  padding: 0;
  background: #fff;
  min-height: 100vh;
}
.search-page .search-header.data-v-d4b650e0 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-radius: 0;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.search-page .search-header .search-input-wrap.data-v-d4b650e0 {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}
.search-page .search-header .search-input-wrap .search-icon.data-v-d4b650e0 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}
.search-page .search-header .search-input-wrap .search-icon svg.data-v-d4b650e0 {
  width: 100%;
  height: 100%;
}
.search-page .search-header .search-input-wrap .search-input.data-v-d4b650e0 {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}
.search-page .search-header .search-input-wrap .clear-icon.data-v-d4b650e0 {
  width: 40rpx;
  height: 40rpx;
}
.search-page .search-header .search-input-wrap .clear-icon svg.data-v-d4b650e0 {
  width: 100%;
  height: 100%;
}
.search-page .search-header .cancel-btn.data-v-d4b650e0 {
  padding-left: 24rpx;
  font-size: 28rpx;
  color: #007AFF;
}
.search-page .search-history.data-v-d4b650e0,
.search-page .hot-search.data-v-d4b650e0 {
  margin-top: 24rpx;
}
.search-page .search-history .section-header.data-v-d4b650e0,
.search-page .hot-search .section-header.data-v-d4b650e0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.search-page .search-history .section-header .section-title.data-v-d4b650e0,
.search-page .hot-search .section-header .section-title.data-v-d4b650e0 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.search-page .search-history .section-header .clear-btn.data-v-d4b650e0,
.search-page .hot-search .section-header .clear-btn.data-v-d4b650e0 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}
.search-page .search-history .section-header .clear-btn .icon.data-v-d4b650e0,
.search-page .hot-search .section-header .clear-btn .icon.data-v-d4b650e0 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.search-page .search-history .tag-list.data-v-d4b650e0,
.search-page .hot-search .tag-list.data-v-d4b650e0 {
  display: flex;
  flex-wrap: wrap;
}
.search-page .search-history .tag-list .tag-item.data-v-d4b650e0,
.search-page .hot-search .tag-list .tag-item.data-v-d4b650e0 {
  padding: 12rpx 24rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  position: relative;
}
.search-page .search-history .tag-list .tag-item.hot.data-v-d4b650e0,
.search-page .hot-search .tag-list .tag-item.hot.data-v-d4b650e0 {
  padding-left: 48rpx;
  color: #FF6B6B;
  background: rgba(255, 107, 107, 0.1);
}
.search-page .search-history .tag-list .tag-item .hot-rank.data-v-d4b650e0,
.search-page .hot-search .tag-list .tag-item .hot-rank.data-v-d4b650e0 {
  position: absolute;
  left: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  font-weight: 600;
}
.search-page .search-results.data-v-d4b650e0 {
  padding: 24rpx;
}
.search-page .search-results .filter-tabs.data-v-d4b650e0 {
  display: flex;
  padding: 0;
  margin-bottom: 24rpx;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fff;
}
.search-page .search-results .filter-tabs .filter-tab.data-v-d4b650e0 {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.search-page .search-results .filter-tabs .filter-tab.active.data-v-d4b650e0 {
  color: #007AFF;
  font-weight: 500;
}
.search-page .search-results .filter-tabs .filter-tab.active.data-v-d4b650e0::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 48rpx;
  height: 4rpx;
  background: #007AFF;
  border-radius: 2rpx;
}
.search-page .search-results .result-list .activity-item.data-v-d4b650e0,
.search-page .search-results .result-list .product-item.data-v-d4b650e0 {
  display: flex;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.search-page .search-results .result-list .activity-item .item-image.data-v-d4b650e0,
.search-page .search-results .result-list .product-item .item-image.data-v-d4b650e0 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}
.search-page .search-results .result-list .activity-item .item-info.data-v-d4b650e0,
.search-page .search-results .result-list .product-item .item-info.data-v-d4b650e0 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.search-page .search-results .result-list .activity-item .item-info .item-title.data-v-d4b650e0,
.search-page .search-results .result-list .product-item .item-info .item-title.data-v-d4b650e0 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}
.search-page .search-results .result-list .activity-item .item-info .item-desc.data-v-d4b650e0,
.search-page .search-results .result-list .product-item .item-info .item-desc.data-v-d4b650e0 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  flex: 1;
}
.search-page .search-results .result-list .activity-item .item-info .item-meta.data-v-d4b650e0,
.search-page .search-results .result-list .product-item .item-info .item-meta.data-v-d4b650e0 {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
}
.search-page .search-results .result-list .activity-item .item-info .item-meta .time.data-v-d4b650e0,
.search-page .search-results .result-list .product-item .item-info .item-meta .time.data-v-d4b650e0 {
  color: #999;
}
.search-page .search-results .result-list .activity-item .item-info .item-meta .price.data-v-d4b650e0,
.search-page .search-results .result-list .product-item .item-info .item-meta .price.data-v-d4b650e0 {
  color: #FF6B6B;
  font-weight: 500;
}
.search-page .search-results .result-list .activity-item .item-info .item-price .current.data-v-d4b650e0,
.search-page .search-results .result-list .product-item .item-info .item-price .current.data-v-d4b650e0 {
  font-size: 32rpx;
  color: #FF6B6B;
  font-weight: 500;
  margin-right: 16rpx;
}
.search-page .search-results .result-list .activity-item .item-info .item-price .original.data-v-d4b650e0,
.search-page .search-results .result-list .product-item .item-info .item-price .original.data-v-d4b650e0 {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}
.search-page .search-results .result-list .article-item.data-v-d4b650e0 {
  display: flex;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.search-page .search-results .result-list .article-item .item-info.data-v-d4b650e0 {
  flex: 1;
  margin-right: 24rpx;
}
.search-page .search-results .result-list .article-item .item-info .item-title.data-v-d4b650e0 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}
.search-page .search-results .result-list .article-item .item-info .item-desc.data-v-d4b650e0 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.search-page .search-results .result-list .article-item .item-info .item-meta.data-v-d4b650e0 {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}
.search-page .search-results .result-list .article-item .item-image.data-v-d4b650e0 {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
}
.search-page .search-results .loading-state.data-v-d4b650e0,
.search-page .search-results .load-more.data-v-d4b650e0,
.search-page .search-results .no-more.data-v-d4b650e0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 0;
}
.search-page .search-results .loading-state text.data-v-d4b650e0,
.search-page .search-results .load-more text.data-v-d4b650e0,
.search-page .search-results .no-more text.data-v-d4b650e0 {
  font-size: 28rpx;
  color: #999;
}
.search-page .search-results .loading-state .loading-indicator.data-v-d4b650e0 {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  margin-bottom: 16rpx;
  /* 使用静态样式代替动画 */
}
.search-page .search-results .load-more text.data-v-d4b650e0 {
  color: #007AFF;
}