{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/pages/coupon/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcY291cG9uXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"coupon-page\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"goBack\">\r\n          <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">优惠券</view>\r\n        <view class=\"navbar-right\">\r\n          <view class=\"close-btn\" @click=\"goBack\">\r\n            <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"22\" height=\"22\">\r\n              <path d=\"M512 421.490332L331.349941 240.840273c-24.988383-24.988383-65.35828-24.988383-90.346664 0-24.988383 24.988383-24.988383 65.35828 0 90.346664L421.653336 512 240.840273 692.812059c-24.988383 24.988383-24.988383 65.35828 0 90.346664 24.988383 24.988383 65.35828 24.988383 90.346664 0L512 602.509668l180.650059 180.650059c24.988383 24.988383 65.35828 24.988383 90.346664 0 24.988383-24.988383 24.988383-65.35828 0-90.346664L602.346664 512l180.813063-180.812059c24.988383-24.988383 24.988383-65.35828 0-90.346664-24.988383-24.988383-65.35828-24.988383-90.346664 0L512 421.490332z\" fill=\"#FFFFFF\"></path>\r\n            </svg>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 分类选项卡 -->\r\n    <view class=\"category-tabs\">\r\n      <view \r\n        class=\"tab-item\" \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\"\r\n        :class=\"{ active: currentTabIndex === index }\"\r\n        @click=\"switchTab(index)\"\r\n      >\r\n        <text>{{ tab.name }}</text>\r\n      </view>\r\n      <view class=\"tab-line\" :style=\"tabLineStyle\"></view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <scroll-view \r\n      class=\"content-scroll\" \r\n      scroll-y \r\n      @scrolltolower=\"loadMore\"\r\n    >\r\n      <!-- 可领取优惠券 -->\r\n      <view class=\"coupon-list\" v-if=\"currentTabIndex === 0\">\r\n        <view \r\n          class=\"coupon-item\" \r\n          v-for=\"(item, index) in availableCoupons\" \r\n          :key=\"index\"\r\n        >\r\n          <view class=\"coupon-left\">\r\n            <view class=\"coupon-amount\">\r\n              <text class=\"amount-symbol\" v-if=\"item.type === 'cash'\">¥</text>\r\n              <text class=\"amount-value\">{{ item.value }}</text>\r\n              <text class=\"amount-condition\" v-if=\"item.minAmount > 0\">满{{ item.minAmount }}可用</text>\r\n              <text class=\"amount-condition\" v-else>无门槛</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"coupon-right\">\r\n            <view class=\"coupon-info\" @click=\"viewCouponDetail(item.id)\">\r\n              <view class=\"coupon-title\">{{ item.title }}</view>\r\n              <view class=\"coupon-desc\">{{ item.description }}</view>\r\n              <view class=\"coupon-time\">{{ getTimeText(item.startTime, item.endTime) }}</view>\r\n            </view>\r\n            <view class=\"coupon-btn\" @click=\"receiveCoupon(item.id)\">\r\n              <text>立即领取</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"coupon-tag\" v-if=\"item.tag\">{{ item.tag }}</view>\r\n          <view class=\"coupon-border\"></view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 我的优惠券 -->\r\n      <view class=\"my-coupon-section\" v-else>\r\n        <view class=\"coupon-status-tabs\">\r\n          <view \r\n            class=\"status-tab\" \r\n            v-for=\"(status, index) in couponStatus\" \r\n            :key=\"index\"\r\n            :class=\"{ active: currentStatusIndex === index }\"\r\n            @click=\"switchStatus(index)\"\r\n          >\r\n            <text>{{ status.name }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"my-coupon-list\" v-if=\"getMyCoupons().length > 0\">\r\n          <view \r\n            class=\"coupon-item\" \r\n            v-for=\"(item, index) in getMyCoupons()\" \r\n            :key=\"index\"\r\n            :class=\"{ 'coupon-used': item.status === 'used', 'coupon-expired': item.status === 'expired' }\"\r\n          >\r\n            <view class=\"coupon-left\">\r\n              <view class=\"coupon-amount\">\r\n                <text class=\"amount-symbol\" v-if=\"item.type === 'cash'\">¥</text>\r\n                <text class=\"amount-value\">{{ item.value }}</text>\r\n                <text class=\"amount-condition\" v-if=\"item.minAmount > 0\">满{{ item.minAmount }}可用</text>\r\n                <text class=\"amount-condition\" v-else>无门槛</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"coupon-right\">\r\n              <view class=\"coupon-info\">\r\n                <view class=\"coupon-title\">{{ item.title }}</view>\r\n                <view class=\"coupon-desc\">{{ item.description }}</view>\r\n                <view class=\"coupon-time\">{{ getTimeText(item.startTime, item.endTime) }}</view>\r\n              </view>\r\n              <view class=\"coupon-btn\" v-if=\"item.status === 'valid'\" @click=\"useCoupon(item.id)\">\r\n                <text>立即使用</text>\r\n              </view>\r\n              <view class=\"coupon-status\" v-else>\r\n                <text>{{ getStatusText(item.status) }}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"coupon-tag\" v-if=\"item.tag\">{{ item.tag }}</view>\r\n            <view class=\"coupon-border\"></view>\r\n            <view class=\"coupon-mask\" v-if=\"item.status !== 'valid'\"></view>\r\n            <view class=\"coupon-stamp\" v-if=\"item.status === 'used'\">已使用</view>\r\n            <view class=\"coupon-stamp\" v-else-if=\"item.status === 'expired'\">已过期</view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"empty-tip\" v-else>\r\n          <image class=\"empty-image\" src=\"/static/images/empty-coupon.png\" mode=\"aspectFit\"></image>\r\n          <text class=\"empty-text\">{{ getEmptyText() }}</text>\r\n          <view class=\"empty-btn\" @click=\"switchTab(0)\" v-if=\"currentStatusIndex === 0\">\r\n            <text>去领券</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多提示 -->\r\n      <view class=\"loading-more\" v-if=\"loading\">\r\n        <text>加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 到底了提示 -->\r\n      <view class=\"no-more\" v-if=\"noMore\">\r\n        <text>已经到底啦~</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      refreshing: false,\r\n      loading: false,\r\n      noMore: false,\r\n      currentTabIndex: 0,\r\n      currentStatusIndex: 0,\r\n      tabs: [\r\n        { name: '可领取' },\r\n        { name: '我的券' }\r\n      ],\r\n      couponStatus: [\r\n        { name: '未使用' },\r\n        { name: '已使用' },\r\n        { name: '已过期' }\r\n      ],\r\n      availableCoupons: [\r\n        {\r\n          id: 1,\r\n          title: '新人专享券',\r\n          description: '仅限新用户使用',\r\n          type: 'cash',\r\n          value: '30',\r\n          minAmount: 0,\r\n          startTime: new Date(Date.now()).toISOString(),\r\n          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),\r\n          tag: '新人专享'\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '满减优惠券',\r\n          description: '全场通用',\r\n          type: 'cash',\r\n          value: '10',\r\n          minAmount: 99,\r\n          startTime: new Date(Date.now()).toISOString(),\r\n          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),\r\n          tag: '热门'\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '满减优惠券',\r\n          description: '全场通用',\r\n          type: 'cash',\r\n          value: '50',\r\n          minAmount: 299,\r\n          startTime: new Date(Date.now()).toISOString(),\r\n          endTime: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(),\r\n          tag: '限量'\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '满减优惠券',\r\n          description: '仅限食品类',\r\n          type: 'cash',\r\n          value: '20',\r\n          minAmount: 129,\r\n          startTime: new Date(Date.now()).toISOString(),\r\n          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString()\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '满减优惠券',\r\n          description: '仅限电器类',\r\n          type: 'cash',\r\n          value: '100',\r\n          minAmount: 1000,\r\n          startTime: new Date(Date.now()).toISOString(),\r\n          endTime: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString(),\r\n          tag: '特惠'\r\n        }\r\n      ],\r\n      myCoupons: [\r\n        {\r\n          id: 6,\r\n          title: '满减优惠券',\r\n          description: '全场通用',\r\n          type: 'cash',\r\n          value: '15',\r\n          minAmount: 99,\r\n          startTime: new Date(Date.now()).toISOString(),\r\n          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),\r\n          status: 'valid',\r\n          tag: '热门'\r\n        },\r\n        {\r\n          id: 7,\r\n          title: '满减优惠券',\r\n          description: '仅限服装类',\r\n          type: 'cash',\r\n          value: '30',\r\n          minAmount: 199,\r\n          startTime: new Date(Date.now()).toISOString(),\r\n          endTime: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),\r\n          status: 'valid'\r\n        },\r\n        {\r\n          id: 8,\r\n          title: '满减优惠券',\r\n          description: '全场通用',\r\n          type: 'cash',\r\n          value: '50',\r\n          minAmount: 299,\r\n          startTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),\r\n          endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),\r\n          status: 'expired',\r\n          tag: '限量'\r\n        },\r\n        {\r\n          id: 9,\r\n          title: '满减优惠券',\r\n          description: '仅限食品类',\r\n          type: 'cash',\r\n          value: '20',\r\n          minAmount: 129,\r\n          startTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),\r\n          endTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),\r\n          status: 'used'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    tabLineStyle() {\r\n      const width = 100 / this.tabs.length\r\n      const left = this.currentTabIndex * width\r\n      return {\r\n        width: width + '%',\r\n        transform: `translateX(${left * 100}%)`\r\n      }\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 页面加载时获取数据\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 切换选项卡\r\n    switchTab(index) {\r\n      this.currentTabIndex = index\r\n      this.fetchData()\r\n    },\r\n    \r\n    // 切换优惠券状态\r\n    switchStatus(index) {\r\n      this.currentStatusIndex = index\r\n    },\r\n    \r\n    // 获取我的优惠券列表\r\n    getMyCoupons() {\r\n      const statusMap = ['valid', 'used', 'expired']\r\n      const status = statusMap[this.currentStatusIndex]\r\n      return this.myCoupons.filter(item => item.status === status)\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      if (this.loading || this.noMore) return\r\n      \r\n      this.loading = true\r\n      \r\n      // 模拟加载更多数据\r\n      setTimeout(() => {\r\n        if (this.currentTabIndex === 0) {\r\n          // 添加更多可领取优惠券\r\n          const moreCoupons = [\r\n            {\r\n              id: 10,\r\n              title: '满减优惠券',\r\n              description: '仅限母婴类',\r\n              type: 'cash',\r\n              value: '25',\r\n              minAmount: 199,\r\n              startTime: new Date(Date.now()).toISOString(),\r\n              endTime: new Date(Date.now() + 18 * 24 * 60 * 60 * 1000).toISOString()\r\n            },\r\n            {\r\n              id: 11,\r\n              title: '满减优惠券',\r\n              description: '仅限美妆类',\r\n              type: 'cash',\r\n              value: '40',\r\n              minAmount: 299,\r\n              startTime: new Date(Date.now()).toISOString(),\r\n              endTime: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000).toISOString(),\r\n              tag: '爆款'\r\n            }\r\n          ]\r\n          \r\n          this.availableCoupons = [...this.availableCoupons, ...moreCoupons]\r\n        }\r\n        \r\n        this.noMore = true // 示例中加载一次后就没有更多数据\r\n        this.loading = false\r\n      }, 1500)\r\n    },\r\n    \r\n    // 获取数据\r\n    fetchData() {\r\n      // 实际项目中，这里应该根据当前选中的选项卡调用API获取数据\r\n      // 示例中使用的是静态数据\r\n    },\r\n    \r\n    // 领取优惠券\r\n    receiveCoupon(id) {\r\n      // 实际项目中，这里应该调用API领取优惠券\r\n      uni.showLoading({\r\n        title: '领取中...'\r\n      })\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading()\r\n        \r\n        // 模拟领取成功\r\n        const coupon = this.availableCoupons.find(item => item.id === id)\r\n        if (coupon) {\r\n          // 将优惠券添加到我的优惠券列表\r\n          const myCoupon = {\r\n            ...coupon,\r\n            id: Date.now(), // 生成新ID\r\n            status: 'valid'\r\n          }\r\n          this.myCoupons.push(myCoupon)\r\n          \r\n          uni.showToast({\r\n            title: '领取成功',\r\n            icon: 'success'\r\n          })\r\n        }\r\n      }, 1000)\r\n    },\r\n    \r\n    // 使用优惠券\r\n    useCoupon(id) {\r\n      // 跳转到优惠券详情页\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity-showcase/pages/coupon/detail?id=${id}`\r\n      })\r\n    },\r\n    \r\n    // 查看优惠券详情\r\n    viewCouponDetail(id) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/activity-showcase/pages/coupon/detail?id=${id}`\r\n      });\r\n    },\r\n    \r\n    // 获取活动时间文本\r\n    getTimeText(startTime, endTime) {\r\n      const start = new Date(startTime)\r\n      const end = new Date(endTime)\r\n      \r\n      const startMonth = start.getMonth() + 1\r\n      const startDay = start.getDate()\r\n      const endMonth = end.getMonth() + 1\r\n      const endDay = end.getDate()\r\n      \r\n      return `${startMonth}.${startDay}-${endMonth}.${endDay}`\r\n    },\r\n    \r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'valid': '未使用',\r\n        'used': '已使用',\r\n        'expired': '已过期'\r\n      }\r\n      return statusMap[status] || ''\r\n    },\r\n    \r\n    // 获取空状态提示文本\r\n    getEmptyText() {\r\n      const textMap = [\r\n        '您还没有未使用的优惠券',\r\n        '您还没有已使用的优惠券',\r\n        '您还没有已过期的优惠券'\r\n      ]\r\n      return textMap[this.currentStatusIndex] || '暂无数据'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.coupon-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #F2F2F7;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    padding-left: 30rpx;\r\n    padding-right: 30rpx;\r\n    box-sizing: border-box;\r\n    \r\n    .back-btn {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n    \r\n    .back-icon {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    \r\n    .navbar-title {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #FFFFFF;\r\n      position: absolute;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n    }\r\n    \r\n    .navbar-right {\r\n      width: 80rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: flex-end;\r\n      \r\n      .close-btn {\r\n        width: 64rpx;\r\n        height: 64rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 分类选项卡 */\r\n.category-tabs {\r\n  position: relative;\r\n  display: flex;\r\n  background-color: #FFFFFF;\r\n  height: 88rpx;\r\n  margin-top: calc(var(--status-bar-height, 25px) + 62px);\r\n  \r\n  .tab-item {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: 28rpx;\r\n    color: #666666;\r\n    position: relative;\r\n    \r\n    &.active {\r\n      color: #FF9500;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n  \r\n  .tab-line {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    height: 4rpx;\r\n    background-color: #FF9500;\r\n    transition: transform 0.3s ease;\r\n  }\r\n}\r\n\r\n/* 内容区域 */\r\n.content-scroll {\r\n  flex: 1;\r\n  width: 100%;\r\n}\r\n\r\n/* 优惠券列表 */\r\n.coupon-list {\r\n  padding: 20rpx;\r\n}\r\n\r\n.coupon-item {\r\n  position: relative;\r\n  display: flex;\r\n  margin-bottom: 20rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\r\n  \r\n  &.coupon-used, &.coupon-expired {\r\n    opacity: 0.7;\r\n  }\r\n  \r\n  .coupon-left {\r\n    width: 200rpx;\r\n    background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 20rpx;\r\n    position: relative;\r\n    \r\n    .coupon-amount {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      color: #FFFFFF;\r\n      \r\n      .amount-symbol {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n      }\r\n      \r\n      .amount-value {\r\n        font-size: 60rpx;\r\n        font-weight: 700;\r\n        line-height: 1;\r\n      }\r\n      \r\n      .amount-condition {\r\n        font-size: 22rpx;\r\n        margin-top: 8rpx;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .coupon-right {\r\n    flex: 1;\r\n    padding: 20rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    \r\n    .coupon-info {\r\n      .coupon-title {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #333333;\r\n        margin-bottom: 8rpx;\r\n      }\r\n      \r\n      .coupon-desc {\r\n        font-size: 24rpx;\r\n        color: #666666;\r\n        margin-bottom: 12rpx;\r\n      }\r\n      \r\n      .coupon-time {\r\n        font-size: 22rpx;\r\n        color: #999999;\r\n      }\r\n    }\r\n    \r\n    .coupon-btn {\r\n      align-self: flex-end;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      height: 60rpx;\r\n      width: 160rpx;\r\n      border-radius: 30rpx;\r\n      background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);\r\n      font-size: 26rpx;\r\n      font-weight: 500;\r\n      color: #FFFFFF;\r\n    }\r\n    \r\n    .coupon-status {\r\n      align-self: flex-end;\r\n      font-size: 26rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n  \r\n  .coupon-tag {\r\n    position: absolute;\r\n    top: 20rpx;\r\n    right: 20rpx;\r\n    padding: 4rpx 12rpx;\r\n    font-size: 22rpx;\r\n    color: #FFFFFF;\r\n    border-radius: 10rpx;\r\n    background-color: rgba(255, 59, 48, 0.8);\r\n  }\r\n  \r\n  .coupon-border {\r\n    position: absolute;\r\n    left: 200rpx;\r\n    top: 0;\r\n    bottom: 0;\r\n    width: 0;\r\n    border-left: 1px dashed #F2F2F7;\r\n    \r\n    &::before, &::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: -10rpx;\r\n      width: 20rpx;\r\n      height: 20rpx;\r\n      border-radius: 50%;\r\n      background-color: #F2F2F7;\r\n    }\r\n    \r\n    &::before {\r\n      top: -10rpx;\r\n    }\r\n    \r\n    &::after {\r\n      bottom: -10rpx;\r\n    }\r\n  }\r\n  \r\n  .coupon-mask {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: rgba(0, 0, 0, 0.1);\r\n    pointer-events: none;\r\n  }\r\n  \r\n  .coupon-stamp {\r\n    position: absolute;\r\n    top: 50%;\r\n    right: 60rpx;\r\n    transform: translateY(-50%) rotate(-30deg);\r\n    font-size: 60rpx;\r\n    font-weight: 700;\r\n    color: rgba(255, 59, 48, 0.6);\r\n    border: 6rpx solid rgba(255, 59, 48, 0.6);\r\n    padding: 10rpx 20rpx;\r\n    border-radius: 16rpx;\r\n  }\r\n}\r\n\r\n/* 我的优惠券 */\r\n.my-coupon-section {\r\n  .coupon-status-tabs {\r\n    display: flex;\r\n    background-color: #FFFFFF;\r\n    padding: 20rpx;\r\n    \r\n    .status-tab {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      height: 60rpx;\r\n      font-size: 26rpx;\r\n      color: #666666;\r\n      position: relative;\r\n      border-radius: 30rpx;\r\n      margin: 0 10rpx;\r\n      \r\n      &.active {\r\n        color: #FFFFFF;\r\n        background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);\r\n      }\r\n    }\r\n  }\r\n  \r\n  .my-coupon-list {\r\n    padding: 20rpx;\r\n  }\r\n  \r\n  .empty-tip {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding: 60rpx 0;\r\n    \r\n    .empty-image {\r\n      width: 200rpx;\r\n      height: 200rpx;\r\n      margin-bottom: 20rpx;\r\n    }\r\n    \r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999999;\r\n      margin-bottom: 30rpx;\r\n    }\r\n    \r\n    .empty-btn {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      height: 80rpx;\r\n      width: 300rpx;\r\n      border-radius: 40rpx;\r\n      background: linear-gradient(135deg, #FFCC00 0%, #FF9500 100%);\r\n      font-size: 28rpx;\r\n      font-weight: 500;\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n}\r\n\r\n/* 加载更多和到底了提示 */\r\n.loading-more, .no-more {\r\n  text-align: center;\r\n  padding: 20rpx 0;\r\n  font-size: 24rpx;\r\n  color: #999999;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/coupon/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA+IA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,MAAM;AAAA,QACJ,EAAE,MAAM,MAAO;AAAA,QACf,EAAE,MAAM,MAAM;AAAA,MACf;AAAA,MACD,cAAc;AAAA,QACZ,EAAE,MAAM,MAAO;AAAA,QACf,EAAE,MAAM,MAAO;AAAA,QACf,EAAE,MAAM,MAAM;AAAA,MACf;AAAA,MACD,kBAAkB;AAAA,QAChB;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,IAAI,KAAK,KAAK,IAAK,CAAA,EAAE,YAAa;AAAA,UAC7C,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACtE,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,IAAI,KAAK,KAAK,IAAK,CAAA,EAAE,YAAa;AAAA,UAC7C,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACtE,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,IAAI,KAAK,KAAK,IAAK,CAAA,EAAE,YAAa;AAAA,UAC7C,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACtE,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,IAAI,KAAK,KAAK,IAAK,CAAA,EAAE,YAAa;AAAA,UAC7C,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACtE;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,IAAI,KAAK,KAAK,IAAK,CAAA,EAAE,YAAa;AAAA,UAC7C,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACtE,KAAK;AAAA,QACP;AAAA,MACD;AAAA,MACD,WAAW;AAAA,QACT;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,IAAI,KAAK,KAAK,IAAK,CAAA,EAAE,YAAa;AAAA,UAC7C,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACtE,QAAQ;AAAA,UACR,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,IAAI,KAAK,KAAK,IAAK,CAAA,EAAE,YAAa;AAAA,UAC7C,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACtE,QAAQ;AAAA,QACT;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACvE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACrE,QAAQ;AAAA,UACR,KAAK;AAAA,QACN;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACxE,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,UACrE,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,eAAe;AACb,YAAM,QAAQ,MAAM,KAAK,KAAK;AAC9B,YAAM,OAAO,KAAK,kBAAkB;AACpC,aAAO;AAAA,QACL,OAAO,QAAQ;AAAA,QACf,WAAW,cAAc,OAAO,GAAG;AAAA,MACrC;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,UAAU;AAAA,EAChB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,kBAAkB;AACvB,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,aAAa,OAAO;AAClB,WAAK,qBAAqB;AAAA,IAC3B;AAAA;AAAA,IAGD,eAAe;AACb,YAAM,YAAY,CAAC,SAAS,QAAQ,SAAS;AAC7C,YAAM,SAAS,UAAU,KAAK,kBAAkB;AAChD,aAAO,KAAK,UAAU,OAAO,UAAQ,KAAK,WAAW,MAAM;AAAA,IAC5D;AAAA;AAAA,IAGD,WAAW;AACT,UAAI,KAAK,WAAW,KAAK;AAAQ;AAEjC,WAAK,UAAU;AAGf,iBAAW,MAAM;AACf,YAAI,KAAK,oBAAoB,GAAG;AAE9B,gBAAM,cAAc;AAAA,YAClB;AAAA,cACE,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,aAAa;AAAA,cACb,MAAM;AAAA,cACN,OAAO;AAAA,cACP,WAAW;AAAA,cACX,WAAW,IAAI,KAAK,KAAK,IAAK,CAAA,EAAE,YAAa;AAAA,cAC7C,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,YACtE;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,OAAO;AAAA,cACP,aAAa;AAAA,cACb,MAAM;AAAA,cACN,OAAO;AAAA,cACP,WAAW;AAAA,cACX,WAAW,IAAI,KAAK,KAAK,IAAK,CAAA,EAAE,YAAa;AAAA,cAC7C,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa;AAAA,cACtE,KAAK;AAAA,YACP;AAAA,UACF;AAEA,eAAK,mBAAmB,CAAC,GAAG,KAAK,kBAAkB,GAAG,WAAW;AAAA,QACnE;AAEA,aAAK,SAAS;AACd,aAAK,UAAU;AAAA,MAChB,GAAE,IAAI;AAAA,IACR;AAAA;AAAA,IAGD,YAAY;AAAA,IAGX;AAAA;AAAA,IAGD,cAAc,IAAI;AAEhBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,OACR;AAED,iBAAW,MAAM;AACfA,sBAAAA,MAAI,YAAY;AAGhB,cAAM,SAAS,KAAK,iBAAiB,KAAK,UAAQ,KAAK,OAAO,EAAE;AAChE,YAAI,QAAQ;AAEV,gBAAM,WAAW;AAAA,YACf,GAAG;AAAA,YACH,IAAI,KAAK,IAAK;AAAA;AAAA,YACd,QAAQ;AAAA,UACV;AACA,eAAK,UAAU,KAAK,QAAQ;AAE5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACD,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,UAAU,IAAI;AAEZA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yDAAyD,EAAE;AAAA,OACjE;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,IAAI;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yDAAyD,EAAE;AAAA,MAClE,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,WAAW,SAAS;AAC9B,YAAM,QAAQ,IAAI,KAAK,SAAS;AAChC,YAAM,MAAM,IAAI,KAAK,OAAO;AAE5B,YAAM,aAAa,MAAM,SAAQ,IAAK;AACtC,YAAM,WAAW,MAAM,QAAQ;AAC/B,YAAM,WAAW,IAAI,SAAQ,IAAK;AAClC,YAAM,SAAS,IAAI,QAAQ;AAE3B,aAAO,GAAG,UAAU,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM;AAAA,IACvD;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,WAAW;AAAA,MACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,eAAe;AACb,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO,QAAQ,KAAK,kBAAkB,KAAK;AAAA,IAC7C;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5aA,GAAG,WAAW,eAAe;"}