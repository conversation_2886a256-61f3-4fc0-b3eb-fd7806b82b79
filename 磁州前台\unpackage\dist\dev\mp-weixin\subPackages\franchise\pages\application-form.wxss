/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-b3ec23a4, html.data-v-b3ec23a4, #app.data-v-b3ec23a4, .index-container.data-v-b3ec23a4 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.application-container.data-v-b3ec23a4 {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 40rpx;
}
.form-wrapper.data-v-b3ec23a4 {
  padding: 30rpx;
}
.form-section.data-v-b3ec23a4 {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.section-header.data-v-b3ec23a4 {
  padding: 30rpx;
  border-bottom: 1px solid #F0F0F0;
}
.section-title.data-v-b3ec23a4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}
.section-title.data-v-b3ec23a4::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background-color: #1677FF;
  border-radius: 3rpx;
}
.section-subtitle.data-v-b3ec23a4 {
  font-size: 24rpx;
  color: #999999;
  margin-left: 20rpx;
}
.section-content.data-v-b3ec23a4 {
  padding: 20rpx 30rpx;
}
.form-item.data-v-b3ec23a4 {
  margin-bottom: 30rpx;
}
.form-item.data-v-b3ec23a4:last-child {
  margin-bottom: 0;
}
.item-label.data-v-b3ec23a4 {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 15rpx;
}
.item-label.required.data-v-b3ec23a4::before {
  content: "*";
  color: #FF3B30;
  margin-right: 6rpx;
}
.form-input.data-v-b3ec23a4 {
  height: 90rpx;
  line-height: 90rpx;
  padding: 0 20rpx;
  background-color: #F9FCFF;
  border-radius: 8rpx;
  border: 1px solid #E5E5E5;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}
.form-textarea.data-v-b3ec23a4 {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background-color: #F9FCFF;
  border-radius: 8rpx;
  border: 1px solid #E5E5E5;
  font-size: 28rpx;
  box-sizing: border-box;
}
.textarea-counter.data-v-b3ec23a4 {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}
.selected-region.data-v-b3ec23a4 {
  font-size: 28rpx;
  color: #1677FF;
  font-weight: 500;
  background-color: rgba(22, 119, 255, 0.05);
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  display: inline-block;
}
.upload-box.data-v-b3ec23a4 {
  width: 280rpx;
  height: 400rpx;
  border: 1px dashed #CCCCCC;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.upload-placeholder.data-v-b3ec23a4 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.upload-placeholder .cuIcon-camerafill.data-v-b3ec23a4 {
  font-size: 60rpx;
  color: #CCCCCC;
  margin-bottom: 15rpx;
}
.upload-placeholder .upload-text.data-v-b3ec23a4 {
  font-size: 24rpx;
  color: #999999;
}
.license-image.data-v-b3ec23a4 {
  width: 100%;
  height: 100%;
}
.selector-picker.data-v-b3ec23a4 {
  width: 100%;
}
.picker-value.data-v-b3ec23a4 {
  height: 90rpx;
  line-height: 90rpx;
  padding: 0 20rpx;
  background-color: #F9FCFF;
  border-radius: 8rpx;
  border: 1px solid #E5E5E5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333333;
}
.checkbox-group.data-v-b3ec23a4 {
  display: flex;
  flex-direction: column;
}
.checkbox-item.data-v-b3ec23a4 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.checkbox-item.data-v-b3ec23a4:last-child {
  margin-bottom: 0;
}
.checkbox-item.checked .checkbox-label.data-v-b3ec23a4 {
  color: #1677FF;
}
.checkbox-icon.data-v-b3ec23a4 {
  font-size: 40rpx;
  margin-right: 15rpx;
}
.checkbox-label.data-v-b3ec23a4 {
  font-size: 28rpx;
  color: #333333;
}
.agreement-box.data-v-b3ec23a4 {
  display: flex;
  align-items: center;
  margin: 40rpx 0 20rpx;
  padding: 0 10rpx;
}
.agreement-checkbox.data-v-b3ec23a4 {
  margin-right: 15rpx;
}
.agreement-checkbox .checkbox-icon.data-v-b3ec23a4 {
  font-size: 40rpx;
}
.agreement-text.data-v-b3ec23a4 {
  font-size: 26rpx;
  color: #666666;
}
.agreement-link.data-v-b3ec23a4 {
  color: #1677FF;
}
.error-message.data-v-b3ec23a4 {
  font-size: 24rpx;
  color: #FF3B30;
  margin-top: 10rpx;
}
.error-message.centered.data-v-b3ec23a4 {
  text-align: center;
}
.submit-btn-wrapper.data-v-b3ec23a4 {
  margin: 60rpx 0 40rpx;
  padding: 0 30rpx;
}
.submit-btn.data-v-b3ec23a4 {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(90deg, #1677FF, #4F9DFF);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-btn[disabled].data-v-b3ec23a4 {
  background: linear-gradient(90deg, #CCCCCC, #E5E5E5);
  color: #FFFFFF;
  opacity: 1;
}
.loading-icon.data-v-b3ec23a4 {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #FFFFFF;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin-b3ec23a4 1s linear infinite;
}
@keyframes spin-b3ec23a4 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}