{"version": 3, "file": "detail.js", "sources": ["subPackages/activity-showcase/pages/coupon/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcYWN0aXZpdHktc2hvd2Nhc2VccGFnZXNcY291cG9uXGRldGFpbC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"coupon-detail-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-bg\"></view>\r\n      <view class=\"navbar-content\">\r\n        <view class=\"back-btn\" @click=\"goBack\">\r\n          <image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n        <view class=\"navbar-title\">优惠券详情</view>\r\n        <view class=\"navbar-right\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载状态 -->\r\n    <view class=\"loading-container\" v-if=\"loading\">\r\n      <view class=\"loading-spinner\"></view>\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view>\r\n\r\n    <block v-else>\r\n      <!-- 优惠券信息卡片 -->\r\n      <view class=\"coupon-card\" :style=\"{ marginTop: navbarHeight + 'px' }\">\r\n        <view class=\"coupon-header\">\r\n          <view class=\"coupon-amount\">\r\n            <text class=\"amount-symbol\">¥</text>\r\n            <text class=\"amount-value\">{{ coupon.value }}</text>\r\n          </view>\r\n          <view class=\"coupon-condition\">\r\n            <text v-if=\"coupon.minAmount > 0\">满{{ coupon.minAmount }}可用</text>\r\n            <text v-else>无门槛</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"coupon-info\">\r\n          <view class=\"coupon-title\">{{ coupon.title }}</view>\r\n          <view class=\"coupon-time\">{{ getTimeText(coupon.startTime, coupon.endTime) }}</view>\r\n        </view>\r\n        \r\n        <view class=\"coupon-tag\" v-if=\"coupon.tag\">{{ coupon.tag }}</view>\r\n        \r\n        <!-- 分销组件 - 醒目位置 -->\r\n        <distribution-section \r\n          :itemId=\"id\"\r\n          itemType=\"coupon\"\r\n          :itemTitle=\"coupon.title\"\r\n          :itemPrice=\"coupon.value\"\r\n          :commissionRate=\"coupon.commissionRate || 10\"\r\n        />\r\n      </view>\r\n      \r\n      <!-- 使用说明 -->\r\n      <view class=\"usage-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">使用说明</text>\r\n        </view>\r\n        <view class=\"usage-content\">\r\n          <view class=\"usage-item\" v-for=\"(item, index) in coupon.usageRules\" :key=\"index\">\r\n            <view class=\"item-dot\"></view>\r\n            <text class=\"item-text\">{{ item }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 适用商品 -->\r\n      <view class=\"products-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">适用商品</text>\r\n          <view class=\"card-more\" @click=\"viewAllProducts\">\r\n            <text>查看全部</text>\r\n            <text class=\"arrow-icon\">›</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"product-list\">\r\n          <view class=\"product-item\" \r\n            v-for=\"(item, index) in recommendProducts\" \r\n            :key=\"index\"\r\n            @click=\"goToProductDetail(item.id)\">\r\n            <image :src=\"item.image\" mode=\"aspectFill\" class=\"product-image\"></image>\r\n            <view class=\"product-info\">\r\n              <text class=\"product-name\">{{ item.name }}</text>\r\n              <view class=\"product-price\">\r\n                <text class=\"price-symbol\">¥</text>\r\n                <text class=\"price-value\">{{ item.price }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </block>\r\n    \r\n    <!-- 底部按钮 -->\r\n    <view class=\"bottom-bar\">\r\n      <view class=\"action-btn\" @click=\"shareCoupon\">\r\n        <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\r\n          <path d=\"M768 686.08c-32.768 0-61.952 12.8-84.48 32.768L399.872 573.952c2.56-10.24 4.608-20.992 4.608-32.256 0-11.264-2.048-22.016-4.608-32.256l283.648-144.896c22.528 19.968 51.712 32.768 84.48 32.768 70.144 0 127.488-57.344 127.488-127.488S838.144 142.336 768 142.336s-127.488 57.344-127.488 127.488c0 11.264 2.048 22.016 4.608 32.256L361.472 446.976c-22.528-19.968-51.712-32.768-84.48-32.768-70.144 0-127.488 57.344-127.488 127.488s57.344 127.488 127.488 127.488c32.768 0 61.952-12.8 84.48-32.768l283.648 144.896c-2.56 10.24-4.608 20.992-4.608 32.256 0 70.144 57.344 127.488 127.488 127.488s127.488-57.344 127.488-127.488S838.144 686.08 768 686.08z\" fill=\"#8E8E93\"></path>\r\n        </svg>\r\n        <text>分享</text>\r\n      </view>\r\n      <view class=\"action-btn\" @click=\"collectCoupon\">\r\n        <svg class=\"icon\" viewBox=\"0 0 1024 1024\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\">\r\n          <path d=\"M512 896l-307.2-268.8c-8.533-7.467-17.067-16-25.6-25.6-76.8-76.8-76.8-198.4 0-275.2 76.8-76.8 198.4-76.8 275.2 0L512 384l57.6-57.6c76.8-76.8 198.4-76.8 275.2 0 76.8 76.8 76.8 198.4 0 275.2-8.533 8.533-17.067 17.067-25.6 25.6L512 896z\" fill=\"#8E8E93\"></path>\r\n        </svg>\r\n        <text>收藏</text>\r\n      </view>\r\n      <view class=\"get-btn\" @click=\"getCoupon\">\r\n        <text>立即领取</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport DistributionSection from '@/components/distribution-section.vue';\r\n\r\nexport default {\r\n  components: {\r\n    DistributionSection\r\n  },\r\n  data() {\r\n    return {\r\n      id: null,\r\n      statusBarHeight: 20,\r\n      navbarHeight: 82,\r\n      loading: true,\r\n      coupon: {},\r\n      recommendProducts: [],\r\n      commissionAmount: '20'\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    if (options && options.id) {\r\n      this.id = options.id;\r\n    }\r\n    \r\n    // 获取状态栏高度\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = systemInfo.statusBarHeight;\r\n    this.navbarHeight = this.statusBarHeight + 62; // 状态栏 + 标题栏高度\r\n    \r\n    // 模拟加载数据\r\n    setTimeout(() => {\r\n      this.loadCouponDetail();\r\n    }, 500);\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 加载优惠券详情\r\n    loadCouponDetail() {\r\n      // 模拟API加载数据\r\n      this.loading = true;\r\n      \r\n      // 在实际应用中，这里应该是从API获取数据\r\n      setTimeout(() => {\r\n        // 模拟数据\r\n        this.coupon = {\r\n          id: this.id || 1,\r\n          title: '新人专享优惠券',\r\n          description: '仅限新用户使用',\r\n          type: 'cash',\r\n          value: '50',\r\n          minAmount: 199,\r\n          startTime: new Date(),\r\n          endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),\r\n          tag: '新人专享',\r\n          usageRules: [\r\n            '仅限新用户使用',\r\n            '每人限领1张',\r\n            '有效期15天',\r\n            '全场通用，部分特殊商品除外',\r\n            '不可与其他优惠券叠加使用'\r\n          ]\r\n        };\r\n        \r\n        // 模拟推荐商品数据\r\n        this.recommendProducts = [\r\n          {\r\n            id: 1,\r\n            name: 'iPhone 13 Pro Max',\r\n            price: '7999',\r\n            image: '/static/demo/product1.jpg'\r\n          },\r\n          {\r\n            id: 2,\r\n            name: 'MacBook Pro 14英寸',\r\n            price: '12999',\r\n            image: '/static/demo/product2.jpg'\r\n          },\r\n          {\r\n            id: 3,\r\n            name: 'AirPods Pro 2',\r\n            price: '1999',\r\n            image: '/static/demo/product3.jpg'\r\n          }\r\n        ];\r\n        \r\n        this.loading = false;\r\n      }, 1000);\r\n    },\r\n    \r\n    // 获取活动时间文本\r\n    getTimeText(startTime, endTime) {\r\n      const start = new Date(startTime);\r\n      const end = new Date(endTime);\r\n      \r\n      const startMonth = start.getMonth() + 1;\r\n      const startDay = start.getDate();\r\n      const endMonth = end.getMonth() + 1;\r\n      const endDay = end.getDate();\r\n      \r\n      return `${startMonth}.${startDay} - ${endMonth}.${endDay}`;\r\n    },\r\n    \r\n    // 查看全部适用商品\r\n    viewAllProducts() {\r\n      uni.navigateTo({\r\n        url: `/subPackages/product/pages/list?couponId=${this.id}`\r\n      });\r\n    },\r\n    \r\n    // 跳转到商品详情\r\n    goToProductDetail(productId) {\r\n      uni.navigateTo({\r\n        url: `/subPackages/product/pages/detail?id=${productId}`\r\n      });\r\n    },\r\n    \r\n    // 分享优惠券\r\n    shareCoupon() {\r\n      uni.showToast({\r\n        title: '分享功能开发中',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    \r\n    // 收藏优惠券\r\n    collectCoupon() {\r\n      uni.showToast({\r\n        title: '已收藏',\r\n        icon: 'success'\r\n      });\r\n    },\r\n    \r\n    // 领取优惠券\r\n    getCoupon() {\r\n      uni.showLoading({\r\n        title: '领取中...'\r\n      });\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        \r\n        uni.showToast({\r\n          title: '领取成功',\r\n          icon: 'success'\r\n        });\r\n        \r\n        // 延迟返回上一页\r\n        setTimeout(() => {\r\n          uni.navigateBack();\r\n        }, 1500);\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.coupon-detail-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 120rpx; /* 为底部按钮留出空间 */\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: calc(var(--status-bar-height, 25px) + 62px);\r\n  width: 100%;\r\n  z-index: 100;\r\n  \r\n  .navbar-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);\r\n  }\r\n  \r\n  .navbar-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 100%;\r\n    padding-top: var(--status-bar-height, 25px);\r\n    padding-left: 30rpx;\r\n    padding-right: 30rpx;\r\n    box-sizing: border-box;\r\n  }\r\n  \r\n  .back-btn {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    margin-right: 10rpx;\r\n  }\r\n  \r\n  .back-icon {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  \r\n  .navbar-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    position: absolute;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n  }\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100vh;\r\n  \r\n  .loading-spinner {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    border: 6rpx solid #f3f3f3;\r\n    border-top: 6rpx solid #FF9500;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    color: #8E8E93;\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 优惠券信息卡片 */\r\n.coupon-card {\r\n  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);\r\n  border-radius: 35rpx;\r\n  padding: 40rpx 30rpx;\r\n  margin: 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(255, 149, 0, 0.2);\r\n  position: relative;\r\n  overflow: hidden;\r\n  \r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -100rpx;\r\n    right: -100rpx;\r\n    width: 300rpx;\r\n    height: 300rpx;\r\n    border-radius: 50%;\r\n    background: rgba(255, 255, 255, 0.1);\r\n  }\r\n  \r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -80rpx;\r\n    left: -80rpx;\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    border-radius: 50%;\r\n    background: rgba(255, 255, 255, 0.1);\r\n  }\r\n  \r\n  .coupon-header {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  .coupon-amount {\r\n    display: flex;\r\n    align-items: baseline;\r\n    color: #FFFFFF;\r\n    \r\n    .amount-symbol {\r\n      font-size: 40rpx;\r\n      font-weight: 600;\r\n      margin-right: 5rpx;\r\n    }\r\n    \r\n    .amount-value {\r\n      font-size: 100rpx;\r\n      font-weight: 800;\r\n      line-height: 1;\r\n      text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n  \r\n  .coupon-condition {\r\n    font-size: 28rpx;\r\n    color: #FFFFFF;\r\n    font-weight: 600;\r\n    margin-top: 10rpx;\r\n    background-color: rgba(255, 255, 255, 0.2);\r\n    padding: 8rpx 20rpx;\r\n    border-radius: 20rpx;\r\n  }\r\n  \r\n  .coupon-info {\r\n    text-align: center;\r\n  }\r\n  \r\n  .coupon-title {\r\n    font-size: 36rpx;\r\n    font-weight: 700;\r\n    color: #FFFFFF;\r\n    margin-bottom: 10rpx;\r\n    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n  }\r\n  \r\n  .coupon-time {\r\n    font-size: 26rpx;\r\n    color: rgba(255, 255, 255, 0.9);\r\n  }\r\n  \r\n  /* 分销入口 */\r\n  .distribution-section {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin: 30rpx;\r\n    margin-top: 20rpx;\r\n    padding: 20rpx;\r\n    background: linear-gradient(135deg, #FFF9F9 0%, #FFF5F5 100%);\r\n    border-radius: 20rpx;\r\n    box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.1);\r\n    \r\n    .distribution-left {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n    \r\n    .distribution-icon {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      margin-right: 15rpx;\r\n      \r\n      .icon-image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    \r\n    .distribution-info {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n    \r\n    .distribution-title {\r\n      font-size: 28rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n      margin-bottom: 5rpx;\r\n    }\r\n    \r\n    .distribution-desc {\r\n      font-size: 24rpx;\r\n      color: #FF9500;\r\n      font-weight: 600;\r\n    }\r\n    \r\n    .distribution-right {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n    \r\n    .distribution-btn {\r\n      font-size: 26rpx;\r\n      color: #FF9500;\r\n      font-weight: 600;\r\n    }\r\n    \r\n    .arrow-icon {\r\n      font-size: 32rpx;\r\n      color: #FF9500;\r\n      margin-left: 5rpx;\r\n    }\r\n  }\r\n  \r\n  .coupon-tag {\r\n    position: absolute;\r\n    top: 30rpx;\r\n    right: 30rpx;\r\n    background-color: #FF3B30;\r\n    color: #FFFFFF;\r\n    font-size: 24rpx;\r\n    font-weight: 600;\r\n    padding: 6rpx 16rpx;\r\n    border-radius: 16rpx;\r\n    box-shadow: 0 4rpx 8rpx rgba(255, 59, 48, 0.3);\r\n    transform: rotate(15deg);\r\n  }\r\n}\r\n\r\n/* 使用说明卡片 */\r\n.usage-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 35rpx;\r\n  padding: 30rpx;\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);\r\n  \r\n  .card-header {\r\n    margin-bottom: 20rpx;\r\n    border-bottom: 1rpx solid #EFEFEF;\r\n    padding-bottom: 20rpx;\r\n  }\r\n  \r\n  .card-title {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #333333;\r\n  }\r\n  \r\n  .usage-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .usage-item {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    margin-bottom: 15rpx;\r\n    \r\n    .item-dot {\r\n      width: 12rpx;\r\n      height: 12rpx;\r\n      border-radius: 50%;\r\n      background-color: #FF9500;\r\n      margin-top: 12rpx;\r\n      margin-right: 15rpx;\r\n      flex-shrink: 0;\r\n    }\r\n    \r\n    .item-text {\r\n      font-size: 28rpx;\r\n      color: #666666;\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n}\r\n\r\n/* 适用商品卡片 */\r\n.products-card {\r\n  background-color: #FFFFFF;\r\n  border-radius: 35rpx;\r\n  padding: 30rpx;\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.08);\r\n  \r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    border-bottom: 1rpx solid #EFEFEF;\r\n    padding-bottom: 20rpx;\r\n    \r\n    .card-more {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 26rpx;\r\n      color: #8E8E93;\r\n      \r\n      .arrow-icon {\r\n        font-size: 30rpx;\r\n        margin-left: 5rpx;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .product-list {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin: 0 -10rpx;\r\n  }\r\n  \r\n  .product-item {\r\n    width: calc(33.33% - 20rpx);\r\n    margin: 10rpx;\r\n    border-radius: 20rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);\r\n    background-color: #FFFFFF;\r\n    transition: transform 0.3s ease;\r\n    \r\n    &:active {\r\n      transform: scale(0.98);\r\n    }\r\n    \r\n    .product-image {\r\n      width: 100%;\r\n      height: 180rpx;\r\n      object-fit: cover;\r\n    }\r\n    \r\n    .product-info {\r\n      padding: 15rpx;\r\n    }\r\n    \r\n    .product-name {\r\n      font-size: 24rpx;\r\n      color: #333333;\r\n      line-height: 1.4;\r\n      height: 68rpx;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 2;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n    \r\n    .product-price {\r\n      display: flex;\r\n      align-items: baseline;\r\n      margin-top: 10rpx;\r\n      \r\n      .price-symbol {\r\n        font-size: 22rpx;\r\n        color: #FF9500;\r\n      }\r\n      \r\n      .price-value {\r\n        font-size: 28rpx;\r\n        font-weight: 600;\r\n        color: #FF9500;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 底部按钮 */\r\n.bottom-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  height: 100rpx;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n  z-index: 99;\r\n  \r\n  .action-btn {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-right: 40rpx;\r\n    \r\n    .icon {\r\n      margin-bottom: 5rpx;\r\n    }\r\n    \r\n    text {\r\n      font-size: 22rpx;\r\n      color: #8E8E93;\r\n    }\r\n  }\r\n  \r\n  .get-btn {\r\n    flex: 1;\r\n    height: 80rpx;\r\n    background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);\r\n    border-radius: 40rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 30rpx;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    box-shadow: 0 8rpx 16rpx rgba(255, 149, 0, 0.2);\r\n    transition: transform 0.3s ease;\r\n    \r\n    &:active {\r\n      transform: translateY(5rpx);\r\n      box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.15);\r\n    }\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/pages/coupon/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAkHA,MAAO,sBAAqB,MAAW;AAEvC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,IAAI;AAAA,MACJ,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,SAAS;AAAA,MACT,QAAQ,CAAE;AAAA,MACV,mBAAmB,CAAE;AAAA,MACrB,kBAAkB;AAAA,IACpB;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,QAAI,WAAW,QAAQ,IAAI;AACzB,WAAK,KAAK,QAAQ;AAAA,IACpB;AAGA,UAAM,aAAaA,oBAAI;AACvB,SAAK,kBAAkB,WAAW;AAClC,SAAK,eAAe,KAAK,kBAAkB;AAG3C,eAAW,MAAM;AACf,WAAK,iBAAgB;AAAA,IACtB,GAAE,GAAG;AAAA,EACP;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,mBAAmB;AAEjB,WAAK,UAAU;AAGf,iBAAW,MAAM;AAEf,aAAK,SAAS;AAAA,UACZ,IAAI,KAAK,MAAM;AAAA,UACf,OAAO;AAAA,UACP,aAAa;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,UACX,WAAW,oBAAI,KAAM;AAAA,UACrB,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI;AAAA,UACvD,KAAK;AAAA,UACL,YAAY;AAAA,YACV;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA;AAIF,aAAK,oBAAoB;AAAA,UACvB;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,UACR;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA;AAGF,aAAK,UAAU;AAAA,MAChB,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,YAAY,WAAW,SAAS;AAC9B,YAAM,QAAQ,IAAI,KAAK,SAAS;AAChC,YAAM,MAAM,IAAI,KAAK,OAAO;AAE5B,YAAM,aAAa,MAAM,SAAQ,IAAK;AACtC,YAAM,WAAW,MAAM;AACvB,YAAM,WAAW,IAAI,SAAQ,IAAK;AAClC,YAAM,SAAS,IAAI;AAEnB,aAAO,GAAG,UAAU,IAAI,QAAQ,MAAM,QAAQ,IAAI,MAAM;AAAA,IACzD;AAAA;AAAA,IAGD,kBAAkB;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4CAA4C,KAAK,EAAE;AAAA,MAC1D,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,WAAW;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wCAAwC,SAAS;AAAA,MACxD,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY;AACVA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAGD,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACR,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5QA,GAAG,WAAW,eAAe;"}