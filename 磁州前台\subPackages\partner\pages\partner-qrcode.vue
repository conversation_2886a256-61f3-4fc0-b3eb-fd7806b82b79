<template>
  <view class="qrcode-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的二维码</view>
      <view class="navbar-right">
        <!-- 预留位置与发布页面保持一致 -->
      </view>
    </view>
    
    <!-- 添加顶部安全区域 -->
    <view class="safe-area-top"></view>

    <!-- 二维码卡片 -->
    <view class="qrcode-card">
      <view class="user-info">
        <image class="avatar" :src="userInfo.avatar || '/static/images/avatar/default.png'" mode="aspectFill"></image>
        <view class="user-detail">
          <view class="nickname">{{userInfo.nickname || '游客'}}</view>
          <view class="level-tag">{{getLevelName()}}</view>
        </view>
      </view>
      
      <view class="qrcode-wrapper">
        <image class="qrcode-bg" src="/static/images/tabbar/qrcode-bg.png" mode="aspectFill"></image>
        <view class="qrcode-box">
          <image class="qrcode-image" :src="qrCodeUrl" mode="aspectFit"></image>
        </view>
        <view class="qrcode-tip">扫描二维码加入我的团队</view>
      </view>
      
      <view class="action-buttons">
        <button class="action-btn save-btn" @tap="saveQrCode">
          <image class="btn-icon" src="/static/images/tabbar/save.png" mode="aspectFit"></image>
          <text>保存图片</text>
        </button>
        <button class="action-btn share-btn" @tap="shareQrCode">
          <image class="btn-icon" src="/static/images/tabbar/share.png" mode="aspectFit"></image>
          <text>分享</text>
        </button>
      </view>
    </view>
    
    <!-- 推广提示 -->
    <view class="promotion-tips">
      <view class="tips-header">
        <text class="tips-title">推广提示</text>
      </view>
      <view class="tips-content">
        <view class="tip-item">
          <view class="tip-dot"></view>
          <text class="tip-text">将二维码分享给好友，邀请他们扫码注册</text>
        </view>
        <view class="tip-item">
          <view class="tip-dot"></view>
          <text class="tip-text">好友通过您的二维码注册后，将自动成为您的团队成员</text>
        </view>
        <view class="tip-item">
          <view class="tip-dot"></view>
          <text class="tip-text">团队成员消费时，您将获得相应佣金奖励</text>
        </view>
        <view class="tip-item">
          <view class="tip-dot"></view>
          <text class="tip-text">佣金比例根据您的合伙人等级而定，等级越高佣金越多</text>
        </view>
      </view>
    </view>
    
    <!-- 分享弹窗 -->
    <view class="share-modal" v-if="showShareModal">
      <view class="modal-mask" @tap="closeShareModal"></view>
      <view class="modal-content">
        <view class="modal-title">分享到</view>
        <view class="share-options">
          <view class="share-option" @tap="shareToWechat">
            <image src="/static/images/tabbar/wechat.png" mode="aspectFit"></image>
            <text>微信好友</text>
          </view>
          <view class="share-option" @tap="shareToMoments">
            <image src="/static/images/tabbar/moments.png" mode="aspectFit"></image>
            <text>朋友圈</text>
          </view>
          <view class="share-option" @tap="shareToQQ">
            <image src="/static/images/tabbar/qq.png" mode="aspectFit"></image>
            <text>QQ好友</text>
          </view>
          <view class="share-option" @tap="copyLink">
            <image src="/static/images/tabbar/copy-link.png" mode="aspectFit"></image>
            <text>复制链接</text>
          </view>
        </view>
        <button class="cancel-btn" @tap="closeShareModal">取消</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getLocalUserInfo } from '@/utils/userProfile.js';

// 响应式数据
const userInfo = reactive({
  nickname: '',
  avatar: '',
  level: 1
});
const qrCodeUrl = ref('/static/images/tabbar/qrcode-demo.png'); // 默认二维码图片
const showShareModal = ref(false);

// 获取用户信息
const getUserInfo = () => {
  const localUserInfo = getLocalUserInfo();
  if (localUserInfo) {
    userInfo.nickname = localUserInfo.nickname;
    userInfo.avatar = localUserInfo.avatar;
    userInfo.level = localUserInfo.level || 1;
  }
};

// 获取等级名称
const getLevelName = () => {
  const names = {
    1: '普通合伙人',
    2: '银牌合伙人',
    3: '金牌合伙人',
    4: '钻石合伙人'
  };
  return names[userInfo.level] || names[1];
};

// 生成二维码
const generateQrCode = () => {
  // 这里应该调用API生成二维码，这里使用模拟数据
  // 实际开发中，应该调用后端API获取二维码图片URL或使用前端库生成
  
  // 模拟生成二维码的过程
  setTimeout(() => {
    // 实际项目中应该替换为真实的二维码图片URL
    qrCodeUrl.value = '/static/images/tabbar/qrcode-demo.png';
  }, 500);
};

// 保存二维码到相册
const saveQrCode = () => {
  uni.showLoading({
    title: '保存中...'
  });
  
  // 将二维码保存到相册
  uni.saveImageToPhotosAlbum({
    filePath: qrCodeUrl.value,
    success: () => {
      uni.hideLoading();
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
    },
    fail: (err) => {
      uni.hideLoading();
      
      // 如果是权限问题，提示用户授权
      if (err.errMsg.indexOf('auth deny') !== -1) {
        uni.showModal({
          title: '提示',
          content: '需要您授权保存图片到相册',
          confirmText: '去授权',
          success: (res) => {
            if (res.confirm) {
              uni.openSetting();
            }
          }
        });
      } else {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  });
};

// 分享二维码
const shareQrCode = () => {
  showShareModal.value = true;
};

// 关闭分享弹窗
const closeShareModal = () => {
  showShareModal.value = false;
};

// 分享到微信
const shareToWechat = () => {
  // 调用分享API
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 2,
    imageUrl: qrCodeUrl.value,
    title: `${userInfo.nickname}邀请您加入同城`,
    summary: '扫码注册，享受更多优惠',
    success: () => {
      closeShareModal();
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  });
};

// 分享到朋友圈
const shareToMoments = () => {
  // 调用分享API
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneTimeline',
    type: 2,
    imageUrl: qrCodeUrl.value,
    title: `${userInfo.nickname}邀请您加入同城`,
    summary: '扫码注册，享受更多优惠',
    success: () => {
      closeShareModal();
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  });
};

// 分享到QQ
const shareToQQ = () => {
  // 调用分享API
  uni.share({
    provider: 'qq',
    type: 2,
    imageUrl: qrCodeUrl.value,
    title: `${userInfo.nickname}邀请您加入同城`,
    summary: '扫码注册，享受更多优惠',
    success: () => {
      closeShareModal();
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      });
    }
  });
};

// 复制链接
const copyLink = () => {
  // 生成邀请链接
  const inviteLink = `https://example.com/register?inviter=${userInfo.id}`;
  
  uni.setClipboardData({
    data: inviteLink,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success'
      });
      closeShareModal();
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 生命周期钩子
onMounted(() => {
  // 获取用户信息
  getUserInfo();
  // 生成二维码
  generateQrCode();
});
</script>

<style lang="scss" scoped>
.qrcode-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
  padding-top: calc(44px + var(--status-bar-height));
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  padding-top: 44px; /* 状态栏高度 */
  position: fixed; /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
  box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.15);
  z-index: 100; /* 提高z-index确保在最上层 */
}

.navbar-title {
  position: absolute;
  left: 0;
  right: 0;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  text-align: center;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 20; /* 确保在标题上层，可以被点击 */
}

.back-icon {
  width: 100%;
  height: 100%;
}

.safe-area-top {
  height: var(--status-bar-height);
  width: 100%;
  background-image: linear-gradient(135deg, #0066FF, #0052CC);
}

/* 二维码卡片 */
.qrcode-card {
  margin: 30rpx;
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}

.user-detail {
  flex: 1;
}

.nickname {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}

.level-tag {
  display: inline-block;
  font-size: 24rpx;
  color: #0066FF;
  background-color: rgba(0, 102, 255, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.qrcode-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-bottom: 40rpx;
}

.qrcode-bg {
  width: 400rpx;
  height: 400rpx;
  opacity: 0.05;
  position: absolute;
  z-index: 1;
}

.qrcode-box {
  width: 400rpx;
  height: 400rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.qrcode-image {
  width: 360rpx;
  height: 360rpx;
}

.qrcode-tip {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666666;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  border: none;
  padding: 0;
  width: 200rpx;
  
  &::after {
    border: none;
  }
  
  .btn-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 10rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #333333;
  }
}

.save-btn {
  color: #0066FF;
}

.share-btn {
  color: #FF6B00;
}

/* 推广提示 */
.promotion-tips {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.08);
}

.tips-header {
  margin-bottom: 20rpx;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10rpx;
    width: 60rpx;
    height: 4rpx;
    background: linear-gradient(to right, #0066FF, #36CBCB);
    border-radius: 2rpx;
  }
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.tips-content {
  margin-top: 30rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.tip-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #0066FF;
  margin-top: 12rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.tip-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 分享弹窗 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
  width: 100%;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  position: relative;
  z-index: 1000;
}

.modal-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.share-options {
  display: flex;
  padding: 40rpx 30rpx;
  justify-content: space-around;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  image {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 20rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #333333;
  }
}

.cancel-btn {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333333;
  border-top: 10rpx solid #f5f5f5;
  
  &::after {
    border: none;
  }
}
</style>