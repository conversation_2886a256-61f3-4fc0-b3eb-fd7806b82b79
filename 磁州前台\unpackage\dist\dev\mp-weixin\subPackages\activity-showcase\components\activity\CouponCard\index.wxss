
/* 优惠券活动卡片特有样式 */
.coupon-card.data-v-8566755a {
  /* 继承基础卡片样式 */
}

/* 优惠券特有信息区域 */
.coupon-special.data-v-8566755a {
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.05), rgba(255, 59, 48, 0.05));
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
}
.coupon-special.data-v-8566755a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 10rpx 10rpx, transparent 12rpx, rgba(255, 149, 0, 0.03) 13rpx);
  background-size: 30rpx 30rpx;
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

/* 优惠券价值区域 */
.coupon-value-container.data-v-8566755a {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}
.coupon-value-wrapper.data-v-8566755a {
  display: flex;
  align-items: baseline;
  color: #ff9500;
}
.value-symbol.data-v-8566755a {
  font-size: 30rpx;
  font-weight: 600;
}
.value-number.data-v-8566755a {
  font-size: 60rpx;
  font-weight: 700;
  line-height: 1;
}
.value-unit.data-v-8566755a {
  font-size: 30rpx;
  font-weight: 600;
  margin-left: 4rpx;
}
.coupon-type.data-v-8566755a {
  margin-top: 8rpx;
  padding: 4rpx 16rpx;
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 20rpx;
}
.coupon-type text.data-v-8566755a {
  font-size: 22rpx;
  color: #ff9500;
  font-weight: 500;
}

/* 使用条件 */
.coupon-condition.data-v-8566755a {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
}
.condition-icon.data-v-8566755a {
  margin-right: 8rpx;
  color: #ff9500;
}
.condition-text.data-v-8566755a {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

/* 使用范围 */
.coupon-scope.data-v-8566755a {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 1;
}
.scope-icon.data-v-8566755a {
  margin-right: 8rpx;
  color: #ff9500;
}
.scope-text.data-v-8566755a {
  font-size: 24rpx;
  color: #333333;
}

/* 有效期 */
.coupon-validity.data-v-8566755a {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}
.validity-icon.data-v-8566755a {
  margin-right: 8rpx;
  color: #ff9500;
}
.validity-text.data-v-8566755a {
  font-size: 24rpx;
  color: #333333;
}

/* 领取进度 */
.coupon-progress.data-v-8566755a {
  margin-top: 16rpx;
  border-top: 1rpx dashed rgba(255, 149, 0, 0.2);
  padding-top: 16rpx;
  position: relative;
  z-index: 1;
}
.progress-header.data-v-8566755a {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.progress-title.data-v-8566755a {
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
}
.progress-status.data-v-8566755a {
  font-size: 24rpx;
  color: #ff9500;
  font-weight: 500;
}
.progress-bar.data-v-8566755a {
  height: 10rpx;
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}
.progress-inner.data-v-8566755a {
  height: 100%;
  background-color: #ff9500;
  border-radius: 5rpx;
  transition: width 0.3s ease;
}
.progress-tip.data-v-8566755a {
  margin-top: 8rpx;
}
.tip-text.data-v-8566755a {
  font-size: 22rpx;
  color: #ff9500;
}
