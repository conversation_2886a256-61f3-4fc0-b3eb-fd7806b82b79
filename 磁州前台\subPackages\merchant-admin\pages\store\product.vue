<template>
  <view class="product-container">
    <!-- 顶部导航栏 -->
    <view class="header-area">
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>
      
      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon" style="filter: brightness(0) invert(1);"></image>
        </view>
        <view class="navbar-title">商品管理</view>
        <view class="navbar-right">
          <text class="add-product-text" @click="navigateTo('addProduct')">添加</text>
        </view>
      </view>
      
      <!-- 分类切换栏 -->
      <scroll-view class="category-scroll" scroll-x show-scrollbar="false">
        <view class="category-tabs">
          <view 
            class="category-tab" 
            :class="{ active: currentCategory === -1 }"
            @click="switchCategory(-1)"
          >
            全部
          </view>
          <view 
            class="category-tab" 
            :class="{ active: currentCategory === index }"
            v-for="(category, index) in categories" 
            :key="index"
            @click="switchCategory(index)"
          >
            {{category.name}}
          </view>
        </view>
      </scroll-view>
      
      <!-- 搜索框 -->
      <view class="search-box">
        <image src="/static/images/tabbar/搜索.png" class="search-icon"></image>
        <input 
          type="text" 
          class="search-input" 
          placeholder="搜索商品名称/关键词" 
          confirm-type="search"
          v-model="searchKeyword"
          @confirm="searchProducts"
        />
        <text class="clear-icon" v-if="searchKeyword" @click="clearSearch">×</text>
      </view>
    </view>
    
    <!-- 背景装饰 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>
    
    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refreshList"
    >
      <!-- 搜索结果提示 -->
      <view class="search-result-tip" v-if="searchKeyword">
        <text class="search-keyword">"{{searchKeyword}}"</text>
        <text class="search-count">搜索结果：{{filteredProducts.length}}个商品</text>
      </view>
      
      <!-- 批量操作工具栏 -->
      <view class="batch-toolbar" v-if="isSelectMode">
        <view class="selection-info">
          已选择 <text class="selected-count">{{selectedProducts.length}}</text> 项
        </view>
        <view class="batch-actions">
          <view class="batch-btn" @click="batchSetStatus(1)">
            <text class="btn-text">上架</text>
          </view>
          <view class="batch-btn" @click="batchSetStatus(0)">
            <text class="btn-text">下架</text>
          </view>
          <view class="batch-btn delete" @click="confirmBatchDelete">
            <text class="btn-text">删除</text>
          </view>
        </view>
      </view>
      
      <!-- 商品列表 -->
      <view class="product-list">
        <view 
          class="product-item" 
          :class="{ selected: isProductSelected(product.id) }"
          v-for="product in filteredProducts" 
          :key="product.id"
          @tap="onProductTap(product)"
          @longpress="toggleSelectMode"
        >
          <!-- 选择框 -->
          <view class="select-checkbox" v-if="isSelectMode" @tap.stop="toggleProductSelection(product.id)">
            <view class="checkbox" :class="{ checked: isProductSelected(product.id) }"></view>
          </view>
          
          <!-- 商品内容 -->
          <view class="product-content">
            <image :src="product.image" mode="aspectFill" class="product-image"></image>
            <view class="product-info">
              <view class="product-name">{{product.name}}</view>
              <view class="product-category">{{getCategoryName(product.category_id)}}</view>
              <view class="product-price">
                <text class="price">¥{{product.price}}</text>
                <text class="original-price" v-if="product.original_price">¥{{product.original_price}}</text>
              </view>
              <view class="product-stats">
                <text class="sales-count">销量 {{product.sales}}</text>
                <text class="inventory">库存 {{product.inventory}}</text>
              </view>
            </view>
          </view>
          
          <!-- 商品状态 -->
          <view class="product-status" :class="{ 'status-off': product.status === 0 }">
            {{product.status === 1 ? '在售' : '下架'}}
          </view>
          
          <!-- 快捷操作 -->
          <view class="quick-actions" @tap.stop>
            <view class="action-btn" @click="toggleProductStatus(product)">
              {{product.status === 1 ? '下架' : '上架'}}
            </view>
            <view class="action-btn edit" @click="editProduct(product)">
              编辑
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多状态 -->
      <view class="loading-more" v-if="loading">
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 无数据提示 -->
      <view class="no-data" v-if="filteredProducts.length === 0 && !loading">
        <image src="/static/images/tabbar/无数据.png" class="no-data-icon"></image>
        <text class="no-data-text">{{searchKeyword ? '没有找到相关商品' : '暂无商品，快去添加吧'}}</text>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
    
    <!-- 批量操作浮动按钮 -->
    <view class="float-actions" v-if="!isSelectMode">
      <view class="float-btn select-mode-btn" @click="toggleSelectMode">
        <text class="float-btn-text">批量管理</text>
      </view>
    </view>
    
    <!-- 批量删除确认弹窗 -->
    <view class="modal-overlay" v-if="showDeleteModal"></view>
    <view class="delete-modal" v-if="showDeleteModal">
      <view class="modal-header">
        <text class="modal-title">删除商品</text>
      </view>
      <view class="modal-content">
        <view class="confirm-message">确定要删除所选的 {{selectedProducts.length}} 个商品吗？</view>
        <view class="confirm-warning">删除后将无法恢复，请谨慎操作</view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" @click="cancelDelete">取消</button>
        <button class="modal-btn confirm delete" @click="confirmDelete">删除</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 商品分类
      categories: [
        { id: 1, name: '热销推荐' },
        { id: 2, name: '主食' },
        { id: 3, name: '小吃' },
        { id: 4, name: '饮品' },
        { id: 5, name: '甜点' }
      ],
      // 当前选中的分类索引，-1 表示全部
      currentCategory: -1,
      // 商品列表
      products: [
        {
          id: 1,
          name: '磁州特色小酥肉',
          image: '/static/images/product-1.jpg',
          category_id: 1,
          price: '28.00',
          original_price: '35.00',
          sales: 156,
          inventory: 999,
          status: 1
        },
        {
          id: 2,
          name: '手工水饺',
          image: '/static/images/product-2.jpg',
          category_id: 2,
          price: '22.00',
          original_price: '',
          sales: 208,
          inventory: 500,
          status: 1
        },
        {
          id: 3,
          name: '香辣鸡翅',
          image: '/static/images/product-3.jpg',
          category_id: 3,
          price: '15.00',
          original_price: '18.00',
          sales: 321,
          inventory: 100,
          status: 1
        },
        {
          id: 4,
          name: '冰镇酸梅汤',
          image: '/static/images/product-4.jpg',
          category_id: 4,
          price: '8.00',
          original_price: '',
          sales: 213,
          inventory: 200,
          status: 1
        },
        {
          id: 5,
          name: '红豆双皮奶',
          image: '/static/images/product-5.jpg',
          category_id: 5,
          price: '12.00',
          original_price: '15.00',
          sales: 87,
          inventory: 150,
          status: 0
        }
      ],
      searchKeyword: '',
      loading: false,
      refreshing: false,
      isSelectMode: false,
      selectedProducts: [],
      showDeleteModal: false,
      statusBarHeight: 20
    }
  },
  computed: {
    // 根据分类和搜索关键词过滤商品
    filteredProducts() {
      let result = [...this.products];
      
      // 按分类筛选
      if (this.currentCategory !== -1) {
        const categoryId = this.categories[this.currentCategory].id;
        result = result.filter(item => item.category_id === categoryId);
      }
      
      // 按关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(item => 
          item.name.toLowerCase().includes(keyword)
        );
      }
      
      return result;
    }
  },
  onLoad() {
    // 页面加载完成后的处理
    this.setStatusBarHeight();
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      // 获取系统信息设置状态栏高度
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight;
          // 将状态栏高度设置为CSS变量
          if (typeof document !== 'undefined') {
            document.documentElement.style.setProperty('--status-bar-height', `${this.statusBarHeight}px`);
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 根据分类ID获取分类名称
    getCategoryName(categoryId) {
      const category = this.categories.find(item => item.id === categoryId);
      return category ? category.name : '未分类';
    },
    
    // 切换分类
    switchCategory(index) {
      this.currentCategory = index;
    },
    
    // 搜索商品
    searchProducts() {
      // 搜索逻辑已在计算属性中实现
      // 这里可以添加额外处理，如收起键盘
      uni.hideKeyboard();
    },
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
    },
    
    // 刷新列表
    refreshList() {
      this.refreshing = true;
      
      // 模拟刷新请求
      setTimeout(() => {
        // 这里可以添加实际的数据刷新逻辑
        this.refreshing = false;
        uni.showToast({
          title: '刷新成功',
          icon: 'none'
        });
      }, 1000);
    },
    
    // 加载更多
    loadMore() {
      if (this.loading) return;
      
      this.loading = true;
      
      // 模拟加载更多请求
      setTimeout(() => {
        // 这里可以添加实际的加载逻辑
        this.loading = false;
      }, 1000);
    },
    
    // 切换选择模式
    toggleSelectMode() {
      this.isSelectMode = !this.isSelectMode;
      if (!this.isSelectMode) {
        this.selectedProducts = [];
      }
    },
    
    // 判断商品是否被选中
    isProductSelected(productId) {
      return this.selectedProducts.includes(productId);
    },
    
    // 切换商品选择状态
    toggleProductSelection(productId) {
      const index = this.selectedProducts.indexOf(productId);
      if (index === -1) {
        this.selectedProducts.push(productId);
      } else {
        this.selectedProducts.splice(index, 1);
      }
    },
    
    // 商品点击处理
    onProductTap(product) {
      if (this.isSelectMode) {
        this.toggleProductSelection(product.id);
      } else {
        this.editProduct(product);
      }
    },
    
    // 切换商品上下架状态
    toggleProductStatus(product) {
      product.status = product.status === 1 ? 0 : 1;
      
      uni.showToast({
        title: product.status === 1 ? '商品已上架' : '商品已下架',
        icon: 'success'
      });
    },
    
    // 编辑商品
    editProduct(product) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin/pages/store/product-edit?id=${product.id}`
      });
    },
    
    // 批量设置商品状态
    batchSetStatus(status) {
      if (this.selectedProducts.length === 0) {
        uni.showToast({
          title: '请先选择商品',
          icon: 'none'
        });
        return;
      }
      
      // 更新选中商品的状态
      this.products.forEach(product => {
        if (this.selectedProducts.includes(product.id)) {
          product.status = status;
        }
      });
      
      uni.showToast({
        title: status === 1 ? '批量上架成功' : '批量下架成功',
        icon: 'success'
      });
      
      // 退出选择模式
      this.toggleSelectMode();
    },
    
    // 显示删除确认弹窗
    confirmBatchDelete() {
      if (this.selectedProducts.length === 0) {
        uni.showToast({
          title: '请先选择商品',
          icon: 'none'
        });
        return;
      }
      
      this.showDeleteModal = true;
    },
    
    // 取消删除
    cancelDelete() {
      this.showDeleteModal = false;
    },
    
    // 确认删除
    confirmDelete() {
      // 从商品列表中移除选中的商品
      this.products = this.products.filter(product => 
        !this.selectedProducts.includes(product.id)
      );
      
      this.showDeleteModal = false;
      
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      });
      
      // 退出选择模式
      this.toggleSelectMode();
    },
    
    // 导航到指定页面
    navigateTo(page) {
      const pageMap = {
        addProduct: '/subPackages/merchant-admin/pages/store/product-edit'
      };
      
      const url = pageMap[page];
      if (url) {
        uni.navigateTo({ url });
      }
    }
  }
}
</script>

<style lang="scss">
.product-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(220rpx + var(--status-bar-height, 20px));
}

/* 顶部导航栏样式 */
.header-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
  color: #fff;
}

.safe-area-top {
  height: var(--status-bar-height, 20px);
  width: 100%;
}

.custom-navbar {
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-left, .navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-right {
  width: auto;
}

.add-product-text {
  font-size: 32rpx;
  color: #FFFFFF;
  padding: 10rpx 0;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 分类切换栏 */
.category-scroll {
  width: 100%;
  white-space: nowrap;
  background-color: #0A84FF;
  padding: 0 20rpx;
}

.category-tabs {
  display: flex;
  padding-bottom: 20rpx;
}

.category-tab {
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  flex-shrink: 0;
}

.category-tab.active {
  color: #FFFFFF;
  font-weight: 600;
}

.category-tab.active::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #FFFFFF;
  border-radius: 3rpx;
}

/* 搜索框 */
.search-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 40rpx;
  margin: 0 30rpx 20rpx;
  padding: 0 20rpx;
  position: relative;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.search-input {
  height: 80rpx;
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

/* 背景装饰样式 */
.bg-decoration {
  position: absolute;
  z-index: -1;
  border-radius: 50%;
  opacity: 0.07;
  background-color: #0A84FF;
}

.bg-circle-1 {
  top: -100rpx;
  right: -150rpx;
  width: 400rpx;
  height: 400rpx;
}

.bg-circle-2 {
  top: 20%;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
}

.bg-circle-3 {
  bottom: 10%;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
}

/* 内容区域 */
.content-scroll {
  height: calc(100vh - 220rpx - var(--status-bar-height, 20px));
  padding: 30rpx;
  box-sizing: border-box;
}

/* 搜索结果提示 */
.search-result-tip {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.search-keyword {
  color: #0A84FF;
  font-weight: 600;
  margin-right: 10rpx;
}

.search-count {
  color: #999;
}

/* 批量操作工具栏 */
.batch-toolbar {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.selection-info {
  font-size: 28rpx;
  color: #333;
}

.selected-count {
  color: #0A84FF;
  font-weight: 600;
}

.batch-actions {
  display: flex;
  align-items: center;
}

.batch-btn {
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: rgba(10, 132, 255, 0.1);
  margin-left: 20rpx;
}

.batch-btn.delete {
  background-color: rgba(255, 59, 48, 0.1);
}

.btn-text {
  font-size: 26rpx;
  color: #0A84FF;
}

.delete .btn-text {
  color: #FF3B30;
}

/* 商品列表 */
.product-list {
  padding-bottom: 100rpx;
}

.product-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.product-item.selected {
  background-color: rgba(10, 132, 255, 0.05);
  border: 2rpx solid #0A84FF;
}

.product-content {
  display: flex;
  padding: 20rpx;
}

.select-checkbox {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 10;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  background-color: #FFFFFF;
}

.checkbox.checked {
  background-color: #0A84FF;
  border-color: #0A84FF;
  position: relative;
}

.checkbox.checked::after {
  content: '✓';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 24rpx;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.product-category {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
  background-color: #F5F8FC;
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.price {
  font-size: 32rpx;
  color: #FF3B30;
  font-weight: 600;
  margin-right: 10rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.product-stats {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.sales-count {
  margin-right: 20rpx;
}

.product-status {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.status-off {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

.quick-actions {
  display: flex;
  padding: 20rpx;
  border-top: 2rpx solid #F2F2F7;
  justify-content: flex-end;
}

.action-btn {
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  background-color: rgba(10, 132, 255, 0.1);
  font-size: 26rpx;
  color: #0A84FF;
  margin-left: 20rpx;
}

.action-btn.edit {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

/* 加载更多状态 */
.loading-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 无数据提示 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-data-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.no-data-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 100rpx;
}

/* 批量操作浮动按钮 */
.float-actions {
  position: fixed;
  right: 30rpx;
  bottom: calc(100rpx + env(safe-area-inset-bottom));
  z-index: 50;
}

.float-btn {
  width: 180rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #0A84FF, #0055FF);
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(10, 132, 255, 0.3);
}

.float-btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.delete-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 2rpx solid #F2F2F7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 30rpx;
}

.confirm-message {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.confirm-warning {
  font-size: 24rpx;
  color: #FF3B30;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #F2F2F7;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background-color: transparent;
}

.modal-btn::after {
  border: none;
}

.cancel {
  color: #999;
  border-right: 1px solid #F2F2F7;
}

.confirm {
  color: #0A84FF;
  font-weight: 500;
}

.delete {
  color: #FF3B30;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .product-container {
    background-color: #1C1C1E;
  }
  
  .search-box {
    background-color: #3A3A3C;
  }
  
  .search-input {
    color: #FFFFFF;
  }
  
  .batch-toolbar,
  .product-item,
  .delete-modal {
    background-color: #2C2C2E;
  }
  
  .product-item.selected {
    background-color: rgba(10, 132, 255, 0.15);
  }
  
  .product-name {
    color: #FFFFFF;
  }
  
  .product-category {
    background-color: #3A3A3C;
  }
  
  .modal-title,
  .confirm-message {
    color: #FFFFFF;
  }
  
  .modal-header,
  .modal-footer,
  .quick-actions {
    border-color: #3A3A3C;
  }
  
  .checkbox {
    background-color: #2C2C2E;
    border-color: #8E8E93;
  }
}
</style> 