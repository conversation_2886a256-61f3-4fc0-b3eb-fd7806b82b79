
.business-filter-container.data-v-13788ce4 {
  background-color: #f5f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 导航栏样式 */
.custom-navbar.data-v-13788ce4 {
  display: flex;
  align-items: center;
  height: 44px;
  position: relative;
  background-color: #fff;
  z-index: 99;
  border-bottom: 1px solid #f0f0f0;
}
.back-btn.data-v-13788ce4 {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-13788ce4 {
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 18l-6-6 6-6'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}
.navbar-title.data-v-13788ce4 {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: #333;
}
.navbar-right.data-v-13788ce4 {
  width: 44px;
  height: 44px;
}

/* 搜索框样式 */
.search-container.data-v-13788ce4 {
  padding: 15rpx 30rpx;
  background-color: #fff;
}
.search-box.data-v-13788ce4 {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  padding: 10rpx 20rpx;
}
.search-icon.data-v-13788ce4 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.search-input.data-v-13788ce4 {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
}
.search-btn.data-v-13788ce4 {
  padding: 6rpx 20rpx;
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #fff;
  font-size: 26rpx;
  border-radius: 12rpx;
  margin-left: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 分类标签样式 */
.top-category-tabs.data-v-13788ce4 {
  white-space: nowrap;
  background-color: #fff;
  padding: 15rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.top-category-item.data-v-13788ce4 {
  display: inline-block;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  position: relative;
}
.active-top-category.data-v-13788ce4 {
  color: #007AFF;
  font-weight: 500;
}
.active-top-category.data-v-13788ce4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #007AFF;
  border-radius: 2rpx;
}

/* 子分类标签栏 */
.subcategory-tabs.data-v-13788ce4 {
  white-space: nowrap;
  background-color: #f7f9ff;
  padding: 15rpx 0;
  border-bottom: 1px solid #e6eeff;
}
.subcategory-item.data-v-13788ce4 {
  display: inline-block;
  padding: 10rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  margin: 0 8rpx;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}
.active-subcategory.data-v-13788ce4 {
  color: #007AFF;
  font-weight: 500;
  background-color: rgba(0, 122, 255, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);
}
.active-subcategory.data-v-13788ce4::after {
  display: none;
}

/* 筛选条件样式 */
.filter-section.data-v-13788ce4 {
  display: flex;
  padding: 20rpx 0;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  z-index: 10;
}
.filter-item.data-v-13788ce4 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.filter-text.data-v-13788ce4 {
  font-size: 26rpx;
  color: #666;
}
.active-filter.data-v-13788ce4 {
  color: #007AFF;
  font-weight: 500;
}
.filter-arrow.data-v-13788ce4 {
  width: 0;
  height: 0;
  border-left: 6rpx solid transparent;
  border-right: 6rpx solid transparent;
  border-top: 6rpx solid #999;
  margin-left: 6rpx;
  transition: transform 0.3s;
}
.arrow-up.data-v-13788ce4 {
  transform: rotate(180deg);
}

/* 已选筛选标签样式 */
.selected-filters.data-v-13788ce4 {
  background-color: #fff;
  padding: 10rpx 20rpx;
  border-bottom: 1px solid #f0f0f0;
}
.filter-tags-scroll.data-v-13788ce4 {
  white-space: nowrap;
}
.filter-tags.data-v-13788ce4 {
  display: inline-flex;
  align-items: center;
}
.filter-tag.data-v-13788ce4 {
  display: inline-block;
  padding: 6rpx 16rpx;
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  font-size: 24rpx;
  border-radius: 12rpx;
  margin-right: 15rpx;
}
.tag-close.data-v-13788ce4 {
  display: inline-block;
  margin-left: 6rpx;
  font-size: 28rpx;
  font-weight: bold;
}
.reset-all.data-v-13788ce4 {
  display: inline-block;
  padding: 6rpx 16rpx;
  color: #999;
  font-size: 24rpx;
  border: 1px solid #eee;
  border-radius: 12rpx;
}

/* 筛选下拉内容样式 */
.filter-dropdown.data-v-13788ce4 {
  position: absolute;
  top: 160rpx;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 11;
  max-height: 600rpx;
  overflow-y: auto;
}
.area-dropdown.data-v-13788ce4, .sort-dropdown.data-v-13788ce4 {
  max-height: 400rpx;
}
.dropdown-item.data-v-13788ce4 {
  padding: 24rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
}
.title-item.data-v-13788ce4 {
  background-color: #f8f8f8;
  font-weight: 500;
}
.dropdown-title.data-v-13788ce4 {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}
.active-item.data-v-13788ce4 {
  color: #007AFF;
}
.dropdown-item-check.data-v-13788ce4 {
  color: #007AFF;
  font-weight: bold;
}
.dropdown-scroll.data-v-13788ce4 {
  max-height: 550rpx;
}

/* 遮罩层样式 */
.filter-mask.data-v-13788ce4 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 9;
}

/* 商家列表样式 */
.business-list-container.data-v-13788ce4 {
  flex: 1;
  background-color: #f5f7fa;
}
.result-stats.data-v-13788ce4 {
  padding: 20rpx 30rpx;
  font-size: 24rpx;
  color: #999;
}
.stats-number.data-v-13788ce4 {
  color: #007AFF;
  font-weight: 500;
}
.business-item.data-v-13788ce4 {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.business-logo.data-v-13788ce4 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}
.business-info.data-v-13788ce4 {
  flex: 1;
  overflow: hidden;
}
.business-name-row.data-v-13788ce4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.business-name.data-v-13788ce4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.business-distance.data-v-13788ce4 {
  font-size: 24rpx;
  color: #999;
}
.business-desc.data-v-13788ce4 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.business-meta.data-v-13788ce4 {
  display: flex;
  align-items: center;
}
.business-category.data-v-13788ce4, .business-subcategory.data-v-13788ce4, .business-scale.data-v-13788ce4, .business-area.data-v-13788ce4 {
  font-size: 22rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}
.business-subcategory.data-v-13788ce4 {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
  border: 1px solid rgba(0, 122, 255, 0.1);
}
.follow-btn.data-v-13788ce4 {
  position: absolute;
  right: 24rpx;
  bottom: 24rpx;
  background: linear-gradient(to right, #007AFF, #5AC8FA);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 24rpx;
  border: none;
  line-height: 1.5;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

/* 加载更多样式 */
.loading-more.data-v-13788ce4 {
  text-align: center;
  padding: 30rpx 0;
}
.loading-text.data-v-13788ce4 {
  font-size: 26rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state.data-v-13788ce4 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}
.empty-image.data-v-13788ce4 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-13788ce4 {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.empty-tips.data-v-13788ce4 {
  font-size: 26rpx;
  color: #999;
}
