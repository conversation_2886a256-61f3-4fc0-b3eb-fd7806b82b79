/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
page {
  background-color: #F8F8F8;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.page-root {
  position: relative;
  min-height: 100vh;
  background: #f6faff;
}
.nav-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #1677FF;
  z-index: 100;
  width: 100%;
}
.navbar-content {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 101;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  width: 100%;
}
.navbar-left, .navbar-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24px;
  height: 24px;
  display: block;
  background: none;
  border-radius: 0;
  margin: 0 auto;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  line-height: 44px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background: #eaf3ff;
  border-radius: 10px;
  overflow: hidden;
  margin: 0 0 10px 0;
  padding: 2px;
  height: 34px;
}
.tab-item {
  flex: 1;
  text-align: center;
  font-size: 13px;
  color: #1677FF;
  border-radius: 8px;
  background: none;
  line-height: 30px;
  transition: all 0.2s;
  margin: 0 2px;
}
.tab-item.active {
  background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
  color: #fff;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
}
.record-item {
  background: #fff;
  border-radius: 7px;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.04);
  border-left: 3px solid #1677FF;
  margin: 0 16px 7px 16px;
  padding: 14px 14px 14px 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  word-break: break-all;
}
.record-left {
  display: flex;
  flex-direction: column;
}
.record-title {
  font-size: 13px;
}
.record-time {
  font-size: 11px;
}
.record-points {
  font-size: 14px;
  font-weight: 700;
}
.record-points.income {
  color: #1677FF;
  font-weight: 700;
}
.record-points.expense {
  color: #ff4d4f;
  font-weight: 700;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}