# Param Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Transform into a lower cased string with dashes between words.

## Installation

```
npm install param-case --save
```

## Usage

```js
import { paramCase } from "param-case";

paramCase("string"); //=> "string"
paramCase("dot.case"); //=> "dot-case"
paramCase("PascalCase"); //=> "pascal-case"
paramCase("version 1.2.10"); //=> "version-1-2-10"
```

The function also accepts [`options`](https://github.com/blakeembrey/change-case#options).

## License

MIT

[npm-image]: https://img.shields.io/npm/v/param-case.svg?style=flat
[npm-url]: https://npmjs.org/package/param-case
[downloads-image]: https://img.shields.io/npm/dm/param-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/param-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/param-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=param-case
