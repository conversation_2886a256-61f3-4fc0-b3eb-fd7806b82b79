<template>
  <view class="earnings-stats-page">
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">收益统计</view>
    </view>

    <!-- 时间筛选 -->
    <view class="time-filter">
      <view 
        v-for="period in timePeriods" 
        :key="period.value"
        :class="['filter-item', { active: selectedPeriod === period.value }]"
        @click="selectPeriod(period.value)"
      >
        {{ period.label }}
      </view>
    </view>

    <!-- 收益概览 -->
    <view class="earnings-overview">
      <view class="overview-card total">
        <view class="card-title">总收益</view>
        <view class="card-amount">¥{{ earningsData.total }}</view>
        <view class="card-trend">
          <text :class="['trend-icon', earningsData.totalTrend > 0 ? 'up' : 'down']">
            {{ earningsData.totalTrend > 0 ? '↗' : '↘' }}
          </text>
          <text class="trend-text">{{ Math.abs(earningsData.totalTrend) }}%</text>
        </view>
      </view>
      
      <view class="overview-cards">
        <view class="overview-card">
          <view class="card-title">直推收益</view>
          <view class="card-amount">¥{{ earningsData.direct }}</view>
        </view>
        <view class="overview-card">
          <view class="card-title">间推收益</view>
          <view class="card-amount">¥{{ earningsData.indirect }}</view>
        </view>
      </view>
    </view>

    <!-- 收益图表 -->
    <view class="chart-section">
      <view class="section-title">收益趋势</view>
      <canvas canvas-id="earningsChart" class="chart-canvas"></canvas>
    </view>

    <!-- 收益明细 -->
    <view class="earnings-details">
      <view class="section-title">收益明细</view>
      <view v-for="item in earningsDetails" :key="item.id" class="detail-item">
        <view class="detail-info">
          <view class="detail-title">{{ item.title }}</view>
          <view class="detail-time">{{ item.time }}</view>
        </view>
        <view class="detail-amount" :class="item.type">
          {{ item.type === 'income' ? '+' : '-' }}¥{{ item.amount }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const selectedPeriod = ref('week')
const timePeriods = [
  { label: '今日', value: 'today' },
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本年', value: 'year' }
]

const earningsData = ref({
  total: '0.00',
  direct: '0.00',
  indirect: '0.00',
  totalTrend: 0
})

const earningsDetails = ref([])

const selectPeriod = (period) => {
  selectedPeriod.value = period
  loadEarningsData()
}

onMounted(() => {
  loadEarningsData()
  initChart()
})
</script>

<style lang="scss" scoped>
.time-filter {
  display: flex;
  background: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  
  .filter-item {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    border-radius: 20rpx;
    font-size: 26rpx;
    color: #666;
    
    &.active {
      background: #ff6b6b;
      color: #fff;
    }
  }
}

.earnings-overview {
  padding: 0 30rpx 20rpx;
  
  .overview-card {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    &.total {
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      color: #fff;
      
      .card-amount {
        font-size: 48rpx;
        font-weight: bold;
        margin: 20rpx 0;
      }
      
      .card-trend {
        display: flex;
        align-items: center;
        
        .trend-icon {
          margin-right: 8rpx;
          font-size: 24rpx;
          
          &.up { color: #4CAF50; }
          &.down { color: #f44336; }
        }
      }
    }
  }
  
  .overview-cards {
    display: flex;
    gap: 20rpx;
    
    .overview-card {
      flex: 1;
      text-align: center;
      
      .card-title {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 10rpx;
      }
      
      .card-amount {
        font-size: 32rpx;
        font-weight: bold;
        color: #ff6b6b;
      }
    }
  }
}

.chart-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .chart-canvas {
    width: 100%;
    height: 400rpx;
    margin-top: 20rpx;
  }
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 30rpx;
  margin-bottom: 2rpx;
  
  .detail-info {
    .detail-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .detail-time {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .detail-amount {
    font-size: 28rpx;
    font-weight: bold;
    
    &.income { color: #4CAF50; }
    &.expense { color: #f44336; }
  }
}
</style>