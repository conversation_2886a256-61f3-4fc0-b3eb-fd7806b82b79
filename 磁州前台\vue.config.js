const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  transpileDependencies: true,
  productionSourceMap: false, // 禁用源映射以增强安全性
  
  configureWebpack: {
    optimization: {
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true, // 移除console
              drop_debugger: true, // 移除debugger
            },
            mangle: {
              // 启用变量名混淆
              safari10: true,
            },
            output: {
              comments: false, // 移除注释
            },
          },
        }),
      ],
    },
  },
  
  // 其他配置...
}; 