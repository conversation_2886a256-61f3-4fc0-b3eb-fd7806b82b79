"use strict";
const common_vendor = require("../common/vendor.js");
const common_assets = require("../common/assets.js");
const _sfc_main = {
  name: "ReportCard",
  props: {
    contentId: {
      type: String,
      default: ""
    },
    contentType: {
      type: String,
      default: "info"
      // 默认为普通信息类型
    }
  },
  methods: {
    navigateToReport() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      const id = this.contentId || options.id || "";
      common_vendor.index.navigateTo({
        url: `/pages/common/report?id=${id}&type=${this.contentType}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$6,
    b: common_assets._imports_1$5,
    c: common_vendor.o((...args) => $options.navigateToReport && $options.navigateToReport(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4d53c986"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/ReportCard.js.map
