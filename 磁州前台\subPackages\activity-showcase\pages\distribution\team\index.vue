<template>
  <view class="team-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="navigateBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">我的团队</view>
        <view class="navbar-right">
          <view class="invite-btn" @click="showInviteQrcode">
            <svg class="icon" viewBox="0 0 24 24" width="20" height="20">
              <path d="M12 5v14M5 12h14" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content">
      <!-- 团队统计卡片 -->
      <view class="stats-card" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
        padding: '30rpx',
        marginBottom: '30rpx'
      }">
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{ teamStats.totalMembers }}</text>
            <text class="stat-label">团队总人数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ teamStats.directMembers }}</text>
            <text class="stat-label">直属成员</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ teamStats.indirectMembers }}</text>
            <text class="stat-label">间接成员</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ teamStats.newMembers }}</text>
            <text class="stat-label">本月新增</text>
          </view>
        </view>
      </view>
      
      <!-- 团队分类标签 -->
      <view class="filter-tabs" :style="{
        borderRadius: '35px',
        boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
        background: '#FFFFFF',
        padding: '20rpx 30rpx',
        marginBottom: '30rpx'
      }">
        <scroll-view class="tabs-scroll" scroll-x>
          <view class="tabs-container">
            <view 
              v-for="(tab, index) in teamTabs" 
              :key="index"
              class="tab-item"
              :class="{ active: currentTab === index }"
              @click="switchTab(index)"
            >
              <text>{{ tab.name }}</text>
              <view class="tab-indicator" v-if="currentTab === index"></view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 团队成员列表 -->
      <view class="members-list">
        <view 
          v-for="member in filteredMembers"
          :key="member.id"
          class="member-card"
          :style="{
            borderRadius: '35px',
            boxShadow: '0 8px 20px rgba(172,57,255,0.15)',
            background: '#FFFFFF',
            padding: '30rpx',
            marginBottom: '30rpx'
          }"
          @click="viewMemberDetail(member)"
        >
          <!-- 成员头部信息 -->
          <view class="member-header">
            <view class="member-avatar-section">
              <image class="member-avatar" :src="member.avatar" mode="aspectFill"></image>
              <view class="online-status" v-if="member.isOnline"></view>
            </view>
            <view class="member-basic-info">
              <view class="member-name-level">
                <text class="member-name">{{ member.name }}</text>
                <view class="member-level" :style="{
                  background: getLevelColor(member.level),
                  borderRadius: '20rpx',
                  padding: '6rpx 12rpx'
                }">
                  <text style="color: #FFFFFF; font-size: 20rpx;">{{ member.level }}</text>
                </view>
              </view>
              <view class="member-meta">
                <text class="join-time">{{ member.joinTime }} 加入</text>
                <text class="member-id">ID: {{ member.id }}</text>
              </view>
            </view>
            <view class="member-actions">
              <svg class="arrow-icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M9 18l6-6-6-6" stroke="#C7C7CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
            </view>
          </view>
          
          <!-- 成员数据统计 -->
          <view class="member-stats">
            <view class="stat-row">
              <view class="stat-item">
                <text class="stat-value">{{ member.orderCount }}</text>
                <text class="stat-label">订单数</text>
              </view>
              <view class="stat-item">
                <text class="stat-value">¥{{ member.contribution }}</text>
                <text class="stat-label">贡献佣金</text>
              </view>
              <view class="stat-item">
                <text class="stat-value">{{ member.teamSize }}</text>
                <text class="stat-label">下级团队</text>
              </view>
              <view class="stat-item">
                <text class="stat-value">{{ member.monthlyOrders }}</text>
                <text class="stat-label">本月订单</text>
              </view>
            </view>
          </view>
          
          <!-- 成员操作按钮 -->
          <view class="member-actions-bar" v-if="member.type === 'direct'">
            <view class="action-btn secondary" @click.stop="sendMessage(member)">
              <svg class="btn-icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z" stroke="#AC39FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
              <text>私信</text>
            </view>
            <view class="action-btn primary" @click.stop="viewTeamDetail(member)">
              <svg class="btn-icon" viewBox="0 0 24 24" width="16" height="16">
                <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
              <text>查看团队</text>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="filteredMembers.length === 0">
          <view class="empty-content" :style="{
            borderRadius: '35px',
            background: '#FFFFFF',
            padding: '80rpx 40rpx',
            textAlign: 'center'
          }">
            <svg class="empty-icon" viewBox="0 0 24 24" width="80" height="80">
              <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M9 11a4 4 0 100-8 4 4 0 000 8zM23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75" stroke="#C7C7CC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
            <text class="empty-text">暂无{{ teamTabs[currentTab].name }}</text>
            <view class="empty-action" @click="showInviteQrcode" :style="{
              background: 'linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%)',
              borderRadius: '30rpx',
              padding: '16rpx 32rpx',
              marginTop: '40rpx'
            }">
              <text style="color: #FFFFFF;">邀请好友加入</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </view>
    
    <!-- 邀请二维码弹窗 -->
    <view class="invite-modal" v-if="showInviteModal" @click="hideInviteQrcode">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">邀请好友加入团队</text>
          <view class="close-btn" @click="hideInviteQrcode">
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path d="M18 6L6 18M6 6l12 12" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </view>
        </view>
        <view class="qrcode-section">
          <image class="qrcode-image" src="https://via.placeholder.com/200" mode="aspectFit"></image>
          <text class="qrcode-tip">扫描二维码或分享链接邀请好友</text>
        </view>
        <view class="share-actions">
          <view class="share-btn" @click="shareToWechat">
            <text>分享到微信</text>
          </view>
          <view class="share-btn" @click="copyInviteLink">
            <text>复制邀请链接</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 页面状态
const currentTab = ref(0);
const showInviteModal = ref(false);

// 团队统计
const teamStats = ref({
  totalMembers: 28,
  directMembers: 12,
  indirectMembers: 16,
  newMembers: 5
});

// 团队分类标签
const teamTabs = ref([
  { name: '全部成员', type: 'all' },
  { name: '直属成员', type: 'direct' },
  { name: '间接成员', type: 'indirect' },
  { name: '本月新增', type: 'new' }
]);

// 团队成员列表
const memberList = ref([
  {
    id: 'M001',
    name: '李小明',
    avatar: 'https://via.placeholder.com/80',
    level: '高级分销商',
    joinTime: '2023-12-15',
    type: 'direct',
    isOnline: true,
    orderCount: 45,
    contribution: 1234.56,
    teamSize: 8,
    monthlyOrders: 12,
    isNew: false
  },
  {
    id: 'M002',
    name: '王小红',
    avatar: 'https://via.placeholder.com/80',
    level: '中级分销商',
    joinTime: '2024-01-08',
    type: 'direct',
    isOnline: false,
    orderCount: 28,
    contribution: 856.78,
    teamSize: 3,
    monthlyOrders: 8,
    isNew: true
  },
  {
    id: 'M003',
    name: '张小华',
    avatar: 'https://via.placeholder.com/80',
    level: '初级分销商',
    joinTime: '2024-01-12',
    type: 'indirect',
    isOnline: true,
    orderCount: 15,
    contribution: 345.67,
    teamSize: 0,
    monthlyOrders: 6,
    isNew: true
  },
  {
    id: 'M004',
    name: '刘小强',
    avatar: 'https://via.placeholder.com/80',
    level: '中级分销商',
    joinTime: '2023-11-20',
    type: 'direct',
    isOnline: false,
    orderCount: 32,
    contribution: 967.89,
    teamSize: 5,
    monthlyOrders: 10,
    isNew: false
  }
]);

// 计算属性
const filteredMembers = computed(() => {
  const type = teamTabs.value[currentTab.value].type;
  switch(type) {
    case 'all':
      return memberList.value;
    case 'direct':
      return memberList.value.filter(member => member.type === 'direct');
    case 'indirect':
      return memberList.value.filter(member => member.type === 'indirect');
    case 'new':
      return memberList.value.filter(member => member.isNew);
    default:
      return memberList.value;
  }
});

// 方法
const navigateBack = () => {
  uni.navigateBack();
};

const switchTab = (index) => {
  currentTab.value = index;
};

const getLevelColor = (level) => {
  switch(level) {
    case '高级分销商': return 'linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)';
    case '中级分销商': return 'linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%)';
    case '初级分销商': return 'linear-gradient(135deg, #45B7D1 0%, #96C93D 100%)';
    default: return 'linear-gradient(135deg, #8E8E93 0%, #A8A8A8 100%)';
  }
};

const viewMemberDetail = (member) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/distribution/member-detail/index?id=${member.id}`
  });
};

const sendMessage = (member) => {
  uni.showToast({
    title: `向${member.name}发送私信`,
    icon: 'none'
  });
};

const viewTeamDetail = (member) => {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/distribution/team/detail/index?id=${member.id}`
  });
};

const showInviteQrcode = () => {
  showInviteModal.value = true;
};

const hideInviteQrcode = () => {
  showInviteModal.value = false;
};

const shareToWechat = () => {
  uni.showToast({
    title: '分享到微信功能开发中',
    icon: 'none'
  });
};

const copyInviteLink = () => {
  uni.setClipboardData({
    data: 'https://example.com/invite?code=ABC123',
    success: () => {
      uni.showToast({
        title: '邀请链接已复制',
        icon: 'success'
      });
    }
  });
};

onMounted(() => {
  // 页面加载时的初始化操作
});
</script>

<style lang="scss" scoped>
.team-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(180deg, #F2F2F7 0%, #E8E8ED 100%);
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(172,57,255,0.15);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
  }
  
  .back-btn, .invite-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    
    .icon {
      width: 48rpx;
      height: 48rpx;
    }
  }
  
  .navbar-right {
    width: 80rpx;
    display: flex;
    justify-content: flex-end;
  }
}

.content {
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  padding: 30rpx;
  flex: 1;
}

.stats-card {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #FFFFFF;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.filter-tabs {
  .tabs-scroll {
    width: 100%;
    
    .tabs-container {
      display: flex;
      white-space: nowrap;
      
      .tab-item {
        position: relative;
        padding: 20rpx 30rpx;
        margin-right: 20rpx;
        
        text {
          font-size: 28rpx;
          color: #8E8E93;
          transition: color 0.3s ease;
        }
        
        .tab-indicator {
          position: absolute;
          bottom: 10rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          border-radius: 2rpx;
          background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);
        }
        
        &.active text {
          color: #AC39FF;
          font-weight: 500;
        }
      }
    }
  }
}

.member-card {
  .member-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .member-avatar-section {
      position: relative;
      margin-right: 20rpx;
      
      .member-avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        border: 4rpx solid #F0F0F0;
      }
      
      .online-status {
        position: absolute;
        bottom: 8rpx;
        right: 8rpx;
        width: 20rpx;
        height: 20rpx;
        background: #34C759;
        border-radius: 50%;
        border: 3rpx solid #FFFFFF;
      }
    }
    
    .member-basic-info {
      flex: 1;
      
      .member-name-level {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
        
        .member-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          margin-right: 16rpx;
        }
      }
      
      .member-meta {
        display: flex;
        align-items: center;
        gap: 20rpx;
        
        .join-time, .member-id {
          font-size: 24rpx;
          color: #8E8E93;
        }
      }
    }
    
    .member-actions {
      .arrow-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
  
  .member-stats {
    .stat-row {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20rpx;
      padding: 20rpx 0;
      border-top: 1rpx solid #F0F0F0;
      border-bottom: 1rpx solid #F0F0F0;
      margin-bottom: 20rpx;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #AC39FF;
          margin-bottom: 8rpx;
        }
        
        .stat-label {
          font-size: 22rpx;
          color: #8E8E93;
        }
      }
    }
  }
  
  .member-actions-bar {
    display: flex;
    justify-content: flex-end;
    gap: 20rpx;
    
    .action-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 20rpx;
      border-radius: 25rpx;
      
      .btn-icon {
        width: 32rpx;
        height: 32rpx;
      }
      
      text {
        font-size: 26rpx;
        font-weight: 500;
      }
      
      &.primary {
        background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
        
        text {
          color: #FFFFFF;
        }
      }
      
      &.secondary {
        background: transparent;
        border: 2rpx solid #AC39FF;
        
        text {
          color: #AC39FF;
        }
      }
    }
  }
}

.empty-state {
  .empty-content {
    .empty-icon {
      margin-bottom: 30rpx;
    }
    
    .empty-text {
      display: block;
      font-size: 28rpx;
      color: #8E8E93;
      margin-bottom: 20rpx;
    }
    
    .empty-action text {
      font-size: 28rpx;
      font-weight: 500;
    }
  }
}

.invite-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .modal-content {
    background: #FFFFFF;
    border-radius: 35px;
    padding: 40rpx;
    margin: 40rpx;
    max-width: 600rpx;
    width: 100%;
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40rpx;
      
      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }
      
      .close-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    
    .qrcode-section {
      text-align: center;
      margin-bottom: 40rpx;
      
      .qrcode-image {
        width: 400rpx;
        height: 400rpx;
        border-radius: 20rpx;
        margin-bottom: 20rpx;
      }
      
      .qrcode-tip {
        font-size: 26rpx;
        color: #8E8E93;
      }
    }
    
    .share-actions {
      display: flex;
      gap: 20rpx;
      
      .share-btn {
        flex: 1;
        text-align: center;
        padding: 20rpx;
        background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
        border-radius: 30rpx;
        
        text {
          color: #FFFFFF;
          font-size: 28rpx;
          font-weight: 500;
        }
      }
    }
  }
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}
</style>