"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  props: {
    activeTab: {
      type: String,
      default: "marketing"
    }
  },
  data() {
    return {
      tabs: [
        {
          id: "dashboard",
          name: "数据概览",
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>',
          path: ""
        },
        {
          id: "store",
          name: "店铺管理",
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor"><path d="M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z"/></svg>',
          path: "/subPackages/merchant-admin/pages/store/index"
        },
        {
          id: "marketing",
          name: "营销中心",
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>',
          path: "/subPackages/merchant-admin-marketing/pages/marketing/index"
        },
        {
          id: "order",
          name: "订单管理",
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
          path: "/subPackages/merchant-admin-order/pages/order/index"
        },
        {
          id: "more",
          name: "更多",
          icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor"><path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/></svg>',
          path: "/subPackages/merchant-admin-home/pages/merchant-home/index"
        }
      ]
    };
  },
  methods: {
    switchTab(tabId) {
      if (tabId !== this.activeTab) {
        const tab = this.tabs.find((t) => t.id === tabId);
        if (tab) {
          common_vendor.index.redirectTo({
            url: tab.path,
            fail: (err) => {
              common_vendor.index.switchTab({
                url: tab.path,
                fail: (switchErr) => {
                  common_vendor.index.__f__("error", "at components/TabBar.vue:74", "页面跳转失败:", switchErr);
                  common_vendor.index.showToast({
                    title: "页面跳转失败，请稍后再试",
                    icon: "none"
                  });
                }
              });
            }
          });
        }
      }
      this.$emit("tab-change", tabId);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: tab.icon,
        b: common_vendor.t(tab.name),
        c: index,
        d: $props.activeTab === tab.id ? 1 : "",
        e: tab.id,
        f: common_vendor.o(($event) => $options.switchTab(tab.id), index)
      };
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/TabBar.js.map
