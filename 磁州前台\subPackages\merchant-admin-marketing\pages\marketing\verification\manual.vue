<template>
  <view class="manual-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">手动核销</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 手动输入区域 -->
    <view class="input-area">
      <view class="input-header">
        <text class="input-title">请输入核销码</text>
      </view>
      
      <view class="code-input-box">
        <input 
          class="code-input" 
          type="text" 
          placeholder="请输入核销码" 
          v-model="verificationCode"
          maxlength="16"
          @input="onCodeInput"
          @confirm="verifyCode"
        />
        <view class="clear-btn" v-if="verificationCode" @click="clearCode">×</view>
      </view>
      
      <button class="verify-btn" :class="{ 'active': verificationCode.length > 0 }" @click="verifyCode">立即核销</button>
      
      <view class="tip-text">
        <text>提示：核销码通常为12-16位字母数字组合</text>
      </view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">最近核销记录</text>
        <text class="view-all" @click="viewAllRecords">查看全部</text>
      </view>
      
      <view class="history-list">
        <view v-if="historyRecords.length === 0" class="empty-history">
          <text class="empty-text">暂无核销记录</text>
        </view>
        <view v-else class="history-item" v-for="(record, index) in historyRecords" :key="index" @click="showRecordDetail(record)">
          <view class="record-type" :class="record.typeClass">{{record.typeText}}</view>
          <view class="record-content">
            <view class="record-main">
              <text class="record-title">{{record.title}}</text>
              <text class="record-code">{{record.code}}</text>
            </view>
            <view class="record-info">
              <text class="record-time">{{record.time}}</text>
            </view>
          </view>
          <view class="record-status" :class="record.statusClass">{{record.status}}</view>
        </view>
      </view>
    </view>
    
    <!-- 核销成功弹窗 -->
    <view class="verification-popup" v-if="showVerificationPopup">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">核销信息</text>
          <view class="popup-close" @click="closePopup">×</view>
        </view>
        
        <view class="verification-info">
          <view class="info-item">
            <text class="info-label">核销类型</text>
            <text class="info-value">{{verificationData.type}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">商品名称</text>
            <text class="info-value">{{verificationData.name}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">用户信息</text>
            <text class="info-value">{{verificationData.user}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">核销码</text>
            <text class="info-value">{{verificationData.code}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">有效期至</text>
            <text class="info-value">{{verificationData.expiry}}</text>
          </view>
        </view>
        
        <view class="verification-actions">
          <button class="btn-cancel" @click="closePopup">取消</button>
          <button class="btn-confirm" @click="confirmVerification">确认核销</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      verificationCode: '',
      showVerificationPopup: false,
      verificationData: {
        type: '',
        name: '',
        user: '',
        code: '',
        expiry: ''
      },
      historyRecords: [
        {
          typeText: '拼团',
          typeClass: 'type-group',
          title: '双人下午茶套餐拼团',
          code: 'GP20230618001',
          time: '今天 14:30',
          status: '已核销',
          statusClass: 'status-success'
        },
        {
          typeText: '优惠券',
          typeClass: 'type-coupon',
          title: '新店开业满100减20券',
          code: 'CP20230618002',
          time: '今天 11:15',
          status: '已核销',
          statusClass: 'status-success'
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showHelp() {
      uni.showToast({
        title: '手动核销帮助',
        icon: 'none'
      });
    },
    onCodeInput(e) {
      this.verificationCode = e.detail.value;
    },
    clearCode() {
      this.verificationCode = '';
    },
    verifyCode() {
      if (!this.verificationCode) {
        uni.showToast({
          title: '请输入核销码',
          icon: 'none'
        });
        return;
      }
      
      uni.showLoading({
        title: '验证中...'
      });
      
      // 模拟验证请求
      setTimeout(() => {
        uni.hideLoading();
        
        // 模拟核销信息
        this.verificationData = {
          type: '拼团活动',
          name: '双人下午茶套餐拼团',
          user: '张三 (138****8888)',
          code: this.verificationCode,
          expiry: '2023-06-25 23:59:59'
        };
        
        this.showVerificationPopup = true;
      }, 1000);
    },
    viewAllRecords() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/verification/records'
      });
    },
    showRecordDetail(record) {
      // 显示记录详情
      uni.showToast({
        title: '查看记录: ' + record.code,
        icon: 'none'
      });
    },
    closePopup() {
      this.showVerificationPopup = false;
    },
    confirmVerification() {
      uni.showLoading({
        title: '核销中...'
      });
      
      // 模拟核销请求
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '核销成功',
          icon: 'success'
        });
        
        // 关闭弹窗
        this.closePopup();
        
        // 清空输入框
        this.verificationCode = '';
        
        // 添加到历史记录
        this.historyRecords.unshift({
          typeText: '拼团',
          typeClass: 'type-group',
          title: this.verificationData.name,
          code: this.verificationData.code,
          time: '刚刚',
          status: '已核销',
          statusClass: 'status-success'
        });
      }, 1000);
    }
  }
}
</script>

<style lang="scss">
.manual-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 20px;
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #2980b9, #2c3e50);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(41, 128, 185, 0.2);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 输入区域样式 */
.input-area {
  margin: 25px 15px;
  background: #FFFFFF;
  border-radius: 20px;
  padding: 25px 20px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

.input-header {
  margin-bottom: 25px;
}

.input-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.code-input-box {
  position: relative;
  margin-bottom: 25px;
}

.code-input {
  width: 100%;
  height: 56px;
  background: #F5F7FA;
  border-radius: 16px;
  padding: 0 18px;
  font-size: 17px;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.code-input:focus {
  border-color: #2980b9;
  box-shadow: 0 0 0 3px rgba(41, 128, 185, 0.1);
}

.clear-btn {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: #C7C7CC;
  color: #FFFFFF;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.verify-btn {
  width: 100%;
  height: 56px;
  background: #E5E5EA;
  color: #8E8E93;
  border-radius: 16px;
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 18px;
  transition: background-color 0.2s, transform 0.1s;
}

.verify-btn.active {
  background: #2980b9;
  color: #FFFFFF;
}

.verify-btn.active:active {
  transform: scale(0.98);
}

.tip-text {
  text-align: center;
  font-size: 13px;
  color: #8E8E93;
}

/* 历史记录样式 */
.history-section {
  margin: 0 15px 25px;
  background: #FFFFFF;
  border-radius: 20px;
  padding: 25px 20px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.view-all {
  font-size: 14px;
  color: #2980b9;
  font-weight: 500;
}

.empty-history {
  padding: 30px 0;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.history-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.record-type {
  width: 60px;
  height: 28px;
  border-radius: 14px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  font-weight: 500;
}

.type-group {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}

.type-coupon {
  background-color: rgba(41, 128, 185, 0.15);
  color: #2980b9;
}

.type-flash {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}

.record-content {
  flex: 1;
}

.record-main {
  margin-bottom: 4px;
}

.record-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}

.record-code {
  font-size: 12px;
  color: #999;
}

.record-info {
  display: flex;
  font-size: 12px;
  color: #999;
}

.record-status {
  font-size: 13px;
  font-weight: 500;
}

.status-success {
  color: #30D158;
}

.status-pending {
  color: #FF9500;
}

.status-failed {
  color: #FF453A;
}

/* 核销弹窗样式 */
.verification-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 85%;
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: #f8f8f8;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.popup-close {
  font-size: 22px;
  color: #777;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
}

.popup-close:active {
  background: rgba(0, 0, 0, 0.05);
}

.verification-info {
  padding: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 90px;
  font-size: 15px;
  color: #777;
}

.info-value {
  flex: 1;
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.verification-actions {
  display: flex;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.btn-cancel, .btn-confirm {
  flex: 1;
  height: 56px;
  line-height: 56px;
  text-align: center;
  font-size: 17px;
  font-weight: 500;
  border: none;
  border-radius: 0;
}

.btn-cancel {
  background: #f8f8f8;
  color: #666;
}

.btn-confirm {
  background: #2980b9;
  color: #fff;
}
</style>
