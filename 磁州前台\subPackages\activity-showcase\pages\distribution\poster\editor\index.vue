<template>
  <view class="poster-editor-page">
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">海报编辑</view>
      <view class="nav-save" @click="savePoster">
        <text>保存</text>
      </view>
    </view>

    <!-- 海报预览区 -->
    <view class="poster-preview">
      <canvas canvas-id="posterCanvas" class="poster-canvas" 
              :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }">
      </canvas>
    </view>

    <!-- 模板选择 -->
    <view class="template-section">
      <view class="section-title">选择模板</view>
      <scroll-view scroll-x class="template-scroll">
        <view class="template-list">
          <view v-for="template in templates" :key="template.id" 
                :class="['template-item', { active: selectedTemplate === template.id }]"
                @click="selectTemplate(template)">
            <image :src="template.thumbnail" mode="aspectFill" />
            <text>{{ template.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 编辑工具栏 -->
    <view class="edit-toolbar">
      <view class="toolbar-section">
        <view class="section-title">商品信息</view>
        <view class="edit-item">
          <text class="edit-label">商品标题</text>
          <input v-model="posterData.productTitle" class="edit-input" />
        </view>
        <view class="edit-item">
          <text class="edit-label">价格</text>
          <input v-model="posterData.price" type="digit" class="edit-input" />
        </view>
        <view class="edit-item">
          <text class="edit-label">推广语</text>
          <textarea v-model="posterData.slogan" class="edit-textarea" />
        </view>
      </view>

      <view class="toolbar-section">
        <view class="section-title">个人信息</view>
        <view class="edit-item">
          <text class="edit-label">推广员昵称</text>
          <input v-model="posterData.promoterName" class="edit-input" />
        </view>
        <view class="edit-item">
          <text class="edit-label">联系方式</text>
          <input v-model="posterData.contact" class="edit-input" />
        </view>
      </view>

      <view class="toolbar-section">
        <view class="section-title">样式设置</view>
        <view class="style-controls">
          <view class="color-picker">
            <text>主题色</text>
            <view class="color-options">
              <view v-for="color in themeColors" :key="color"
                    :class="['color-item', { active: posterData.themeColor === color }]"
                    :style="{ backgroundColor: color }"
                    @click="posterData.themeColor = color">
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作 -->
    <view class="bottom-actions">
      <button class="btn-secondary" @click="previewPoster">预览</button>
      <button class="btn-primary" @click="generatePoster">生成海报</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

const canvasWidth = ref(375)
const canvasHeight = ref(667)
const selectedTemplate = ref(1)

const templates = ref([
  { id: 1, name: '简约风格', thumbnail: '/static/templates/template1.jpg' },
  { id: 2, name: '商务风格', thumbnail: '/static/templates/template2.jpg' },
  { id: 3, name: '活泼风格', thumbnail: '/static/templates/template3.jpg' }
])

const themeColors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']

const posterData = reactive({
  productTitle: '',
  price: '',
  slogan: '',
  promoterName: '',
  contact: '',
  themeColor: '#ff6b6b',
  qrCode: ''
})

const selectTemplate = (template) => {
  selectedTemplate.value = template.id
  renderPoster()
}

const renderPoster = () => {
  const ctx = uni.createCanvasContext('posterCanvas')
  // 绘制海报逻辑
  ctx.draw()
}

const generatePoster = () => {
  uni.showLoading({ title: '生成中...' })
  
  uni.canvasToTempFilePath({
    canvasId: 'posterCanvas',
    success: (res) => {
      uni.hideLoading()
      uni.previewImage({
        urls: [res.tempFilePath]
      })
    },
    fail: () => {
      uni.hideLoading()
      uni.showToast({ title: '生成失败', icon: 'error' })
    }
  })
}

onMounted(() => {
  renderPoster()
})
</script>

<style lang="scss" scoped>
.poster-preview {
  background: #f5f5f5;
  padding: 30rpx;
  display: flex;
  justify-content: center;
  
  .poster-canvas {
    background: #fff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  }
}

.template-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .template-scroll {
    margin-top: 20rpx;
  }
  
  .template-list {
    display: flex;
    gap: 20rpx;
    
    .template-item {
      flex-shrink: 0;
      width: 120rpx;
      text-align: center;
      
      image {
        width: 120rpx;
        height: 160rpx;
        border-radius: 8rpx;
        border: 2rpx solid transparent;
      }
      
      text {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-top: 10rpx;
      }
      
      &.active {
        image {
          border-color: #ff6b6b;
        }
        
        text {
          color: #ff6b6b;
        }
      }
    }
  }
}

.edit-toolbar {
  background: #fff;
  
  .toolbar-section {
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .edit-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .edit-label {
      width: 150rpx;
      font-size: 28rpx;
      color: #333;
    }
    
    .edit-input, .edit-textarea {
      flex: 1;
      padding: 15rpx;
      background: #f8f9fa;
      border-radius: 8rpx;
      font-size: 26rpx;
    }
    
    .edit-textarea {
      height: 120rpx;
    }
  }
  
  .color-picker {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .color-options {
      display: flex;
      gap: 15rpx;
      
      .color-item {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        border: 3rpx solid transparent;
        
        &.active {
          border-color: #333;
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  
  button {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    margin: 0 10rpx;
    
    &.btn-secondary {
      background: #f0f0f0;
      color: #666;
    }
    
    &.btn-primary {
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      color: #fff;
    }
  }
}
</style>