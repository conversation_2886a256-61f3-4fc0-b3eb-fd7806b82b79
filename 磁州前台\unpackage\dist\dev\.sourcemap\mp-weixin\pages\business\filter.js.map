{"version": 3, "file": "filter.js", "sources": ["pages/business/filter.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYnVzaW5lc3MvZmlsdGVyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"business-filter-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"back-btn\" @click=\"navigateBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <view class=\"navbar-title\">{{businessTitle}}</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 搜索框 -->\n    <view class=\"search-container\">\n      <view class=\"search-box\">\n        <image class=\"search-icon\" src=\"/static/images/tabbar/放大镜.png\"></image>\n        <input class=\"search-input\" type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索商家名称、商品或服务\" confirm-type=\"search\" @confirm=\"searchBusinesses\" />\n        <view class=\"search-btn\" @click=\"searchBusinesses\">搜索</view>\n      </view>\n    </view>\n    \n    <!-- 顶部一级分类标签栏 -->\n    <scroll-view class=\"top-category-tabs\" scroll-x :show-scrollbar=\"false\">\n      <view \n        class=\"top-category-item\" \n        v-for=\"(item, index) in categories\" \n        :key=\"index\"\n        :class=\"{'active-top-category': currentCategory === item.name}\"\n        @click=\"switchCategory(item)\">\n        {{item.name}}\n      </view>\n    </scroll-view>\n    \n    <!-- 子分类标签栏 -->\n    <scroll-view class=\"subcategory-tabs\" scroll-x :show-scrollbar=\"false\" v-if=\"subcategoryList.length > 0\">\n      <view \n        class=\"subcategory-item\" \n        v-for=\"(subcat, index) in subcategoryList\" \n        :key=\"index\"\n        :class=\"{'active-subcategory': selectedSubcategory === subcat}\"\n        @click=\"selectSubcategory(subcat)\">\n        {{subcat}}\n      </view>\n    </scroll-view>\n    \n    <!-- 筛选条件栏 -->\n    <view class=\"filter-section\">\n      <!-- 区域筛选 -->\n      <view class=\"filter-item\" @click=\"showAreaFilter = true\">\n        <text class=\"filter-text\" :class=\"{ 'active-filter': selectedArea !== '全部区域' }\">\n          {{selectedArea}}\n        </text>\n        <view class=\"filter-arrow\" :class=\"{ 'arrow-up': showAreaFilter }\"></view>\n      </view>\n      \n      <!-- 排序筛选 -->\n      <view class=\"filter-item\" @click=\"showSortFilter = true\">\n        <text class=\"filter-text\" :class=\"{ 'active-filter': selectedSort !== '默认排序' }\">\n          {{selectedSort}}\n        </text>\n        <view class=\"filter-arrow\" :class=\"{ 'arrow-up': showSortFilter }\"></view>\n      </view>\n    </view>\n    \n    <!-- 已选筛选标签 -->\n    <view class=\"selected-filters\" v-if=\"hasActiveFilters\">\n      <scroll-view scroll-x class=\"filter-tags-scroll\" show-scrollbar=\"false\">\n        <view class=\"filter-tags\">\n          <view class=\"filter-tag\" v-if=\"currentCategory !== '全部'\">\n            {{currentCategory}} <text class=\"tag-close\" @click=\"resetCategory\">×</text>\n          </view>\n          <view class=\"filter-tag\" v-if=\"selectedSubcategory !== '' && selectedSubcategory !== '全部'\">\n            {{selectedSubcategory}} <text class=\"tag-close\" @click=\"resetSubcategory\">×</text>\n          </view>\n          <view class=\"filter-tag\" v-if=\"selectedArea !== '全部区域'\">\n            {{selectedArea}} <text class=\"tag-close\" @click=\"resetArea\">×</text>\n          </view>\n          <view class=\"filter-tag\" v-if=\"selectedSort !== '默认排序'\">\n            {{selectedSort}} <text class=\"tag-close\" @click=\"resetSort\">×</text>\n          </view>\n          <view class=\"reset-all\" @click=\"resetAllFilters\">清除全部</view>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 区域筛选弹出内容 -->\n    <view class=\"filter-dropdown area-dropdown\" v-if=\"showAreaFilter\">\n      <scroll-view scroll-y class=\"dropdown-scroll\">\n        <view class=\"dropdown-item\" \n          v-for=\"(area, index) in areaList\" \n          :key=\"index\"\n          :class=\"{ 'active-item': area === selectedArea }\"\n          @click=\"selectArea(area)\">\n          <text class=\"dropdown-item-text\">{{area}}</text>\n          <text class=\"dropdown-item-check\" v-if=\"area === selectedArea\">✓</text>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 排序筛选弹出内容 -->\n    <view class=\"filter-dropdown sort-dropdown\" v-if=\"showSortFilter\">\n      <view class=\"dropdown-item\" \n        v-for=\"(sort, index) in sortList\" \n        :key=\"index\"\n        :class=\"{ 'active-item': sort === selectedSort }\"\n        @click=\"selectSort(sort)\">\n        <text class=\"dropdown-item-text\">{{sort}}</text>\n        <text class=\"dropdown-item-check\" v-if=\"sort === selectedSort\">✓</text>\n      </view>\n    </view>\n    \n    <!-- 遮罩层 -->\n    <view class=\"filter-mask\" \n      v-if=\"showAreaFilter || showSortFilter\"\n      @click=\"closeAllFilters\"></view>\n    \n    <!-- 内容列表 -->\n    <scroll-view \n      scroll-y \n      class=\"business-list-container\" \n      @scrolltolower=\"loadMore\" \n      refresher-enabled \n      :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\">\n      \n      <!-- 数据统计提示 -->\n      <view class=\"result-stats\" v-if=\"businessList.length > 0\">\n        共找到 <text class=\"stats-number\">{{businessList.length}}</text> 家商户\n      </view>\n      \n      <view v-if=\"businessList.length > 0\">\n        <!-- 商家列表 -->\n        <view \n          v-for=\"(item, index) in businessList\" \n          :key=\"index\" \n          class=\"business-item\"\n          @click=\"navigateToShopDetail(item.id)\">\n          <image class=\"business-logo\" :src=\"item.logo\" mode=\"aspectFill\"></image>\n          <view class=\"business-info\">\n            <view class=\"business-name-row\">\n              <text class=\"business-name\">{{item.name}}</text>\n              <text class=\"business-distance\" v-if=\"item.distance\">{{item.distance}}km</text>\n            </view>\n            <text class=\"business-desc\">{{item.description}}</text>\n            <view class=\"business-meta\">\n              <text class=\"business-category\">{{item.category}}</text>\n              <text class=\"business-subcategory\" v-if=\"item.subcategory\">{{item.subcategory}}</text>\n              <text class=\"business-scale\" v-if=\"item.scale\">{{item.scale}}</text>\n              <text class=\"business-area\" v-if=\"item.area\">{{item.area}}</text>\n            </view>\n          </view>\n          <button class=\"follow-btn\" @click.stop=\"followBusiness(item.id)\">+ 关注</button>\n        </view>\n        \n        <!-- 加载更多提示 -->\n        <view class=\"loading-more\" v-if=\"hasMore\">\n          <text class=\"loading-text\">加载中...</text>\n        </view>\n        <view class=\"loading-more\" v-else>\n          <text class=\"loading-text\">没有更多了</text>\n        </view>\n      </view>\n      \n      <!-- 空状态 -->\n      <view v-else class=\"empty-state\">\n        <image src=\"/static/images/empty.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\n        <text class=\"empty-text\">暂无相关商家</text>\n        <view class=\"empty-tips\">\n          尝试调整筛选条件，或查看其他分类\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script setup>\n// vue api\nimport { ref, computed, onMounted } from 'vue';\n// uni-app api\nimport { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';\n\n// vue api 开始\n// 状态栏高度\nconst statusBarHeight = ref(20);\n// 页面标题\nconst businessTitle = ref('商家筛选');\n// 搜索关键词\nconst searchKeyword = ref('');\n// 当前选中的一级分类\nconst currentCategory = ref('全部');\n// 当前选中的二级分类\nconst selectedSubcategory = ref('');\n// 正在刷新\nconst refreshing = ref(false);\n// 是否有更多数据\nconst hasMore = ref(true);\n// 当前页码\nconst page = ref(1);\n\n// 筛选弹窗显示状态\nconst showAreaFilter = ref(false);\nconst showSortFilter = ref(false);\n\n// 当前选中的筛选条件\nconst selectedArea = ref('全部区域');\nconst selectedSort = ref('默认排序');\n\n// 筛选选项列表\nconst areaList = ref(['全部区域', '城区', '磁州镇', '讲武城镇', '岳城镇', '观台镇', '白土镇', '黄沙镇']);\nconst categories = ref([\n    { name: '全部', icon: '' },\n    { name: '房产楼盘', icon: '/static/images/tabbar/房产楼盘.png' },\n    { name: '美食小吃', icon: '/static/images/tabbar/美食小吃.png' },\n    { name: '装修家居', icon: '/static/images/tabbar/装修家居.png' },\n    { name: '母婴专区', icon: '/static/images/tabbar/母婴专区.png' },\n    { name: '休闲娱乐', icon: '/static/images/tabbar/休闲娱乐.png' },\n    { name: '到家服务', icon: '/static/images/tabbar/商到家服务.png' },\n    { name: '开锁换锁', icon: '/static/images/tabbar/开锁换锁.png' },\n    { name: '数码通讯', icon: '/static/images/tabbar/数码通讯.png' },\n    { name: '车辆服务', icon: '/static/images/tabbar/商车辆服务.png' },\n    { name: '教育培训', icon: '/static/images/tabbar/商教育培训.png' },\n    { name: '婚纱摄影', icon: '/static/images/tabbar/婚纱摄影.png' },\n    { name: '农林牧渔', icon: '/static/images/tabbar/农林牧渔.png' },\n    { name: '广告传媒', icon: '/static/images/tabbar/广告传媒.png' },\n    { name: '其他行业', icon: '/static/images/tabbar/其他.png' }\n]);\nconst subcategoryMap = ref({\n    '全部': [],\n    '房产楼盘': ['全部', '新房', '二手房', '租房', '商铺', '办公室', '厂房', '别墅', '写字楼', '公寓'],\n    '美食小吃': ['全部', '火锅', '烧烤', '蛋糕店', '小吃', '炒菜', '烤鱼', '面馆', '西餐', '日料', '快餐', '饮品店', '早餐', '烘焙'],\n    '装修家居': ['全部', '全屋装修', '家具', '建材', '瓷砖', '橱柜', '卫浴', '灯饰', '窗帘', '地板', '五金', '电工', '油漆工', '水暖工'],\n    '母婴专区': ['全部', '婴儿用品', '儿童玩具', '孕妇用品', '童装', '早教', '月嫂', '育儿', '孕产服务', '母婴食品'],\n    '休闲娱乐': ['全部', 'KTV', '酒吧', '电影院', '咖啡厅', '茶馆', '网咖', '游乐园', '健身房', '美甲', '桌游', '棋牌室', '足浴'],\n    '到家服务': ['全部', '保洁', '维修', '安装', '搬家', '洗衣', '家电维修', '管道疏通', '保姆', '月嫂', '跑腿', '家居维修'],\n    '开锁换锁': ['全部', '开门锁', '开车锁', '换锁芯', '保险柜开锁', '智能锁安装', '防盗门维修', '配钥匙'],\n    '数码通讯': ['全部', '手机', '电脑', '相机', '维修', '配件', '网络设备', '电子产品', '游戏设备', '办公设备'],\n    '车辆服务': ['全部', '洗车', '美容', '维修', '保养', '补胎', '贴膜', '4S店', '汽车用品', '违章代办', '二手车', '汽车租赁'],\n    '教育培训': ['全部', '幼儿教育', '小学辅导', '中学辅导', '高考培训', '语言培训', '音乐培训', '美术培训', '体育培训', '职业技能', '才艺培训'],\n    '婚纱摄影': ['全部', '婚纱摄影', '婚礼策划', '婚车租赁', '婚礼司仪', '婚宴酒店', '婚礼跟拍', '婚戒定制', '婚纱礼服', '新娘跟妆'],\n    '农林牧渔': ['全部', '农产品', '种植', '养殖', '农资', '花卉', '苗木', '水产', '畜牧', '农机', '农技服务'],\n    '广告传媒': ['全部', '广告设计', '印刷', '展览展示', '标识标牌', '喷绘写真', '广告制作', '媒体投放', '摄影摄像', '网络推广'],\n    '其他行业': ['全部', '劳务派遣', '招聘服务', '法律咨询', '财务服务', '物流快递', '公司注册', '保险服务', '翻译服务', '代理记账']\n});\nconst subcategoryList = ref([]);\nconst sortList = ref(['默认排序', '热门优先', '距离最近', '最新加入', '评分最高']);\n\n// 商家列表数据\nconst businessList = ref([\n    {\n        id: \"1\",\n        logo: '/static/images/tabbar/入驻卡片.png',\n        name: '五分利电器',\n        description: '家电全网调货，全场特价，送货上门',\n        category: '数码电器',\n        subcategory: '电子产品',\n        scale: '10-20人',\n        area: '城区',\n        distance: '1.2'\n    },\n    {\n        id: \"2\",\n        logo: '/static/images/tabbar/入驻卡片.png',\n        name: '北方鑫雨装饰',\n        description: '专业设计，精工细作，打造温馨家园',\n        category: '装修家居',\n        subcategory: '全屋装修',\n        scale: '50-100人',\n        area: '磁州镇',\n        distance: '3.5'\n    }\n]);\n\n// 计算属性，判断是否有活动的筛选条件\nconst hasActiveFilters = computed(() => {\n    return currentCategory.value !== '全部' ||\n           (selectedSubcategory.value !== '' && selectedSubcategory.value !== '全部') ||\n           selectedArea.value !== '全部区域' ||\n           selectedSort.value !== '默认排序';\n});\n\n// onMounted and onLoad\nonMounted(() => {\n    const sysInfo = uni.getSystemInfoSync();\n    statusBarHeight.value = sysInfo.statusBarHeight || 20;\n});\n\nonLoad((options) => {\n    const category = options.category;\n    if (category) {\n        const foundCategory = categories.value.find(c => c.name === category);\n        if (foundCategory) {\n            switchCategory(foundCategory);\n        } else {\n            // 如果路由参数中的分类不存在，则加载默认数据\n            businessTitle.value = '全部商家';\n            fetchBusinessData(true);\n        }\n    } else {\n        // 如果没有路由参数，则加载默认数据\n        businessTitle.value = '全部商家';\n        fetchBusinessData(true);\n    }\n});\n\n// 页面下拉刷新\nonPullDownRefresh(() => {\n    onRefresh();\n});\n\n// 页面上拉触底\nonReachBottom(() => {\n    loadMore();\n});\n\n// 方法\nconst navigateBack = () => {\n    uni.navigateBack();\n};\n\nconst searchBusinesses = () => {\n    console.log('开始搜索:', searchKeyword.value);\n    resetAndFetch();\n};\n\nconst switchCategory = (item) => {\n    if (currentCategory.value === item.name) return;\n\n    currentCategory.value = item.name;\n    businessTitle.value = item.name === '全部' ? '全部商家' : item.name;\n    selectedSubcategory.value = ''; // 重置子分类\n    \n    // 更新子分类列表\n    subcategoryList.value = subcategoryMap.value[item.name] || [];\n    if (subcategoryList.value.length > 0) {\n        selectedSubcategory.value = '全部';\n    }\n    \n    resetAndFetch();\n};\n\nconst selectSubcategory = (subcat) => {\n    if (selectedSubcategory.value === subcat) return;\n    selectedSubcategory.value = subcat;\n    resetAndFetch();\n};\n\nconst selectArea = (area) => {\n    selectedArea.value = area;\n    showAreaFilter.value = false;\n    resetAndFetch();\n};\n\nconst selectSort = (sort) => {\n    selectedSort.value = sort;\n    showSortFilter.value = false;\n    resetAndFetch();\n};\n\nconst closeAllFilters = () => {\n    showAreaFilter.value = false;\n    showSortFilter.value = false;\n};\n\nconst resetCategory = () => {\n    switchCategory({ name: '全部' });\n};\n\nconst resetSubcategory = () => {\n    selectedSubcategory.value = '全部';\n    resetAndFetch();\n};\n\nconst resetArea = () => {\n    selectArea('全部区域');\n};\n\nconst resetSort = () => {\n    selectSort('默认排序');\n};\n\nconst resetAllFilters = () => {\n    currentCategory.value = '全部';\n    businessTitle.value = '全部商家';\n    selectedSubcategory.value = '';\n    subcategoryList.value = [];\n    selectedArea.value = '全部区域';\n    selectedSort.value = '默认排序';\n    searchKeyword.value = '';\n    closeAllFilters();\n    resetAndFetch();\n};\n\nconst fetchBusinessData = (isRefresh = false) => {\n    if (isRefresh) {\n        page.value = 1;\n        businessList.value = [];\n        hasMore.value = true;\n    }\n\n    if (!hasMore.value) {\n        if (refreshing.value) uni.stopPullDownRefresh();\n        return;\n    }\n    \n    console.log('Fetching data...', {\n        page: page.value,\n        keyword: searchKeyword.value,\n        category: currentCategory.value,\n        subcategory: selectedSubcategory.value,\n        area: selectedArea.value,\n        sort: selectedSort.value\n    });\n    \n    // 模拟API请求\n    setTimeout(() => {\n        const mockData = [\n            { id: \"1\", logo: '/static/images/tabbar/入驻卡片.png', name: '五分利电器', description: '家电全网调货，全场特价，送货上门', category: '数码电器', subcategory: '电子产品', scale: '10-20人', area: '城区', distance: '1.2' },\n            { id: \"2\", logo: '/static/images/tabbar/入驻卡片.png', name: '北方鑫雨装饰', description: '专业设计，精工细作，打造温馨家园', category: '装修家居', subcategory: '全屋装修', scale: '50-100人', area: '磁州镇', distance: '3.5' },\n            // 更多模拟数据...\n        ];\n        \n        // 模拟分页\n        if (page.value > 2) {\n            businessList.value = businessList.value.concat([]); // 加载空数组\n            hasMore.value = false;\n        } else {\n            businessList.value = isRefresh ? mockData : businessList.value.concat(mockData);\n            hasMore.value = true;\n        }\n\n        page.value++;\n        if (refreshing.value) {\n            uni.stopPullDownRefresh();\n            refreshing.value = false;\n        }\n    }, 1000);\n};\n\nconst resetAndFetch = () => {\n    fetchBusinessData(true);\n};\n\nconst onRefresh = () => {\n    if (refreshing.value) return;\n    refreshing.value = true;\n    fetchBusinessData(true);\n};\n\nconst loadMore = () => {\n    fetchBusinessData();\n};\n\nconst navigateToShopDetail = (id) => {\n    uni.navigateTo({\n        url: `/pages/business/shop-detail?id=${id}`\n    });\n};\n\nconst followBusiness = (id) => {\n    console.log('关注商家:', id);\n    uni.showToast({\n        title: '关注成功',\n        icon: 'success'\n    });\n};\n// vue api 结尾\n</script>\n\n<style scoped>\n.business-filter-container {\n  background-color: #f5f7fa;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  font-family: -apple-system, BlinkMacSystemFont, \"SF Pro Text\", \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n}\n\n/* 导航栏样式 */\n.custom-navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  position: relative;\n  background-color: #fff;\n  z-index: 99;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.back-btn {\n  width: 44px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 24px;\n  height: 24px;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 18l-6-6 6-6'/%3E%3C/svg%3E\");\n  background-position: center;\n  background-repeat: no-repeat;\n  background-size: contain;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 700;\n  color: #333;\n}\n\n.navbar-right {\n  width: 44px;\n  height: 44px;\n}\n\n/* 搜索框样式 */\n.search-container {\n  padding: 15rpx 30rpx;\n  background-color: #fff;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  background-color: #f5f5f5;\n  border-radius: 16rpx;\n  padding: 10rpx 20rpx;\n}\n\n.search-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 10rpx;\n}\n\n.search-input {\n  flex: 1;\n  height: 60rpx;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.search-btn {\n  padding: 6rpx 20rpx;\n  background: linear-gradient(to right, #007AFF, #5AC8FA);\n  color: #fff;\n  font-size: 26rpx;\n  border-radius: 12rpx;\n  margin-left: 10rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n/* 分类标签样式 */\n.top-category-tabs {\n  white-space: nowrap;\n  background-color: #fff;\n  padding: 15rpx 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.top-category-item {\n  display: inline-block;\n  padding: 15rpx 30rpx;\n  font-size: 28rpx;\n  color: #333;\n  position: relative;\n}\n\n.active-top-category {\n  color: #007AFF;\n  font-weight: 500;\n}\n\n.active-top-category::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 40rpx;\n  height: 4rpx;\n  background-color: #007AFF;\n  border-radius: 2rpx;\n}\n\n/* 子分类标签栏 */\n.subcategory-tabs {\n  white-space: nowrap;\n  background-color: #f7f9ff;\n  padding: 15rpx 0;\n  border-bottom: 1px solid #e6eeff;\n}\n\n.subcategory-item {\n  display: inline-block;\n  padding: 10rpx 24rpx;\n  font-size: 26rpx;\n  color: #666;\n  margin: 0 8rpx;\n  border-radius: 24rpx;\n  transition: all 0.3s ease;\n}\n\n.active-subcategory {\n  color: #007AFF;\n  font-weight: 500;\n  background-color: rgba(0, 122, 255, 0.1);\n  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);\n}\n\n.active-subcategory::after {\n  display: none;\n}\n\n/* 筛选条件样式 */\n.filter-section {\n  display: flex;\n  padding: 20rpx 0;\n  background-color: #fff;\n  border-bottom: 1px solid #f0f0f0;\n  position: relative;\n  z-index: 10;\n}\n\n.filter-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.filter-text {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.active-filter {\n  color: #007AFF;\n  font-weight: 500;\n}\n\n.filter-arrow {\n  width: 0;\n  height: 0;\n  border-left: 6rpx solid transparent;\n  border-right: 6rpx solid transparent;\n  border-top: 6rpx solid #999;\n  margin-left: 6rpx;\n  transition: transform 0.3s;\n}\n\n.arrow-up {\n  transform: rotate(180deg);\n}\n\n/* 已选筛选标签样式 */\n.selected-filters {\n  background-color: #fff;\n  padding: 10rpx 20rpx;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.filter-tags-scroll {\n  white-space: nowrap;\n}\n\n.filter-tags {\n  display: inline-flex;\n  align-items: center;\n}\n\n.filter-tag {\n  display: inline-block;\n  padding: 6rpx 16rpx;\n  background-color: rgba(0, 122, 255, 0.1);\n  color: #007AFF;\n  font-size: 24rpx;\n  border-radius: 12rpx;\n  margin-right: 15rpx;\n}\n\n.tag-close {\n  display: inline-block;\n  margin-left: 6rpx;\n  font-size: 28rpx;\n  font-weight: bold;\n}\n\n.reset-all {\n  display: inline-block;\n  padding: 6rpx 16rpx;\n  color: #999;\n  font-size: 24rpx;\n  border: 1px solid #eee;\n  border-radius: 12rpx;\n}\n\n/* 筛选下拉内容样式 */\n.filter-dropdown {\n  position: absolute;\n  top: 160rpx;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  z-index: 11;\n  max-height: 600rpx;\n  overflow-y: auto;\n}\n\n.area-dropdown, .sort-dropdown {\n  max-height: 400rpx;\n}\n\n.dropdown-item {\n  padding: 24rpx 30rpx;\n  font-size: 28rpx;\n  color: #333;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n.title-item {\n  background-color: #f8f8f8;\n  font-weight: 500;\n}\n\n.dropdown-title {\n  color: #333;\n  font-size: 28rpx;\n  font-weight: 500;\n}\n\n.active-item {\n  color: #007AFF;\n}\n\n.dropdown-item-check {\n  color: #007AFF;\n  font-weight: bold;\n}\n\n.dropdown-scroll {\n  max-height: 550rpx;\n}\n\n/* 遮罩层样式 */\n.filter-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.3);\n  z-index: 9;\n}\n\n/* 商家列表样式 */\n.business-list-container {\n  flex: 1;\n  background-color: #f5f7fa;\n}\n\n.result-stats {\n  padding: 20rpx 30rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n\n.stats-number {\n  color: #007AFF;\n  font-weight: 500;\n}\n\n.business-item {\n  margin: 20rpx 30rpx;\n  background-color: #fff;\n  border-radius: 24rpx;\n  padding: 24rpx;\n  display: flex;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n  position: relative;\n}\n\n.business-logo {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 12rpx;\n  margin-right: 20rpx;\n  background-color: #f5f5f5;\n}\n\n.business-info {\n  flex: 1;\n  overflow: hidden;\n}\n\n.business-name-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.business-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.business-distance {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.business-desc {\n  font-size: 26rpx;\n  color: #666;\n  line-height: 1.4;\n  margin-bottom: 12rpx;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.business-meta {\n  display: flex;\n  align-items: center;\n}\n\n.business-category, .business-subcategory, .business-scale, .business-area {\n  font-size: 22rpx;\n  color: #999;\n  background-color: #f5f5f5;\n  padding: 4rpx 12rpx;\n  border-radius: 6rpx;\n  margin-right: 10rpx;\n}\n\n.business-subcategory {\n  background-color: rgba(0, 122, 255, 0.1);\n  color: #007AFF;\n  border: 1px solid rgba(0, 122, 255, 0.1);\n}\n\n.follow-btn {\n  position: absolute;\n  right: 24rpx;\n  bottom: 24rpx;\n  background: linear-gradient(to right, #007AFF, #5AC8FA);\n  color: #FFFFFF;\n  font-size: 24rpx;\n  padding: 6rpx 20rpx;\n  border-radius: 24rpx;\n  border: none;\n  line-height: 1.5;\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n/* 加载更多样式 */\n.loading-more {\n  text-align: center;\n  padding: 30rpx 0;\n}\n\n.loading-text {\n  font-size: 26rpx;\n  color: #999;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding-top: 200rpx;\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n\n.empty-text {\n  font-size: 32rpx;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.empty-tips {\n  font-size: 26rpx;\n  color: #999;\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/business/filter.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "onLoad", "onPullDownRefresh", "onReachBottom"], "mappings": ";;;;;;AAsLA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAE9B,UAAM,gBAAgBA,cAAAA,IAAI,MAAM;AAEhC,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAE5B,UAAM,kBAAkBA,cAAAA,IAAI,IAAI;AAEhC,UAAM,sBAAsBA,cAAAA,IAAI,EAAE;AAElC,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAE5B,UAAM,UAAUA,cAAAA,IAAI,IAAI;AAExB,UAAM,OAAOA,cAAAA,IAAI,CAAC;AAGlB,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAGhC,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAC/B,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAG/B,UAAM,WAAWA,cAAG,IAAC,CAAC,QAAQ,MAAM,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,CAAC;AAC9E,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACnB,EAAE,MAAM,MAAM,MAAM,GAAI;AAAA,MACxB,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,kCAAmC;AAAA,MACzD,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,kCAAmC;AAAA,MACzD,EAAE,MAAM,QAAQ,MAAM,kCAAmC;AAAA,MACzD,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,iCAAkC;AAAA,MACxD,EAAE,MAAM,QAAQ,MAAM,+BAAgC;AAAA,IAC1D,CAAC;AACD,UAAM,iBAAiBA,cAAAA,IAAI;AAAA,MACvB,MAAM,CAAE;AAAA,MACR,QAAQ,CAAC,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,IAAI;AAAA,MACtE,QAAQ,CAAC,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,IAAI;AAAA,MAC7F,QAAQ,CAAC,MAAM,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,KAAK;AAAA,MAC/F,QAAQ,CAAC,MAAM,QAAQ,QAAQ,QAAQ,MAAM,MAAM,MAAM,MAAM,QAAQ,MAAM;AAAA,MAC7E,QAAQ,CAAC,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,OAAO,IAAI;AAAA,MAC3F,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,MAAM,MAAM;AAAA,MACrF,QAAQ,CAAC,MAAM,OAAO,OAAO,OAAO,SAAS,SAAS,SAAS,KAAK;AAAA,MACpE,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MAC3E,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,QAAQ,QAAQ,OAAO,MAAM;AAAA,MACvF,QAAQ,CAAC,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MAC7F,QAAQ,CAAC,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MACrF,QAAQ,CAAC,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AAAA,MAC5E,QAAQ,CAAC,MAAM,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MACnF,QAAQ,CAAC,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,IACzF,CAAC;AACD,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAC9B,UAAM,WAAWA,cAAAA,IAAI,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,CAAC;AAG7D,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACrB;AAAA,QACI,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,QACV,aAAa;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACb;AAAA,MACD;AAAA,QACI,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,QACV,aAAa;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACb;AAAA,IACL,CAAC;AAGD,UAAM,mBAAmBC,cAAQ,SAAC,MAAM;AACpC,aAAO,gBAAgB,UAAU,QACzB,oBAAoB,UAAU,MAAM,oBAAoB,UAAU,QACnE,aAAa,UAAU,UACvB,aAAa,UAAU;AAAA,IAClC,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACZ,YAAM,UAAUC,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ,mBAAmB;AAAA,IACvD,CAAC;AAEDC,kBAAM,OAAC,CAAC,YAAY;AAChB,YAAM,WAAW,QAAQ;AACzB,UAAI,UAAU;AACV,cAAM,gBAAgB,WAAW,MAAM,KAAK,OAAK,EAAE,SAAS,QAAQ;AACpE,YAAI,eAAe;AACf,yBAAe,aAAa;AAAA,QACxC,OAAe;AAEH,wBAAc,QAAQ;AACtB,4BAAkB,IAAI;AAAA,QACzB;AAAA,MACT,OAAW;AAEH,sBAAc,QAAQ;AACtB,0BAAkB,IAAI;AAAA,MACzB;AAAA,IACL,CAAC;AAGDC,kBAAAA,kBAAkB,MAAM;AACpB;IACJ,CAAC;AAGDC,kBAAAA,cAAc,MAAM;AAChB;IACJ,CAAC;AAGD,UAAM,eAAe,MAAM;AACvBH,oBAAG,MAAC,aAAY;AAAA,IACpB;AAEA,UAAM,mBAAmB,MAAM;AAC3BA,oBAAA,MAAA,MAAA,OAAA,oCAAY,SAAS,cAAc,KAAK;AACxC;IACJ;AAEA,UAAM,iBAAiB,CAAC,SAAS;AAC7B,UAAI,gBAAgB,UAAU,KAAK;AAAM;AAEzC,sBAAgB,QAAQ,KAAK;AAC7B,oBAAc,QAAQ,KAAK,SAAS,OAAO,SAAS,KAAK;AACzD,0BAAoB,QAAQ;AAG5B,sBAAgB,QAAQ,eAAe,MAAM,KAAK,IAAI,KAAK;AAC3D,UAAI,gBAAgB,MAAM,SAAS,GAAG;AAClC,4BAAoB,QAAQ;AAAA,MAC/B;AAED;IACJ;AAEA,UAAM,oBAAoB,CAAC,WAAW;AAClC,UAAI,oBAAoB,UAAU;AAAQ;AAC1C,0BAAoB,QAAQ;AAC5B;IACJ;AAEA,UAAM,aAAa,CAAC,SAAS;AACzB,mBAAa,QAAQ;AACrB,qBAAe,QAAQ;AACvB;IACJ;AAEA,UAAM,aAAa,CAAC,SAAS;AACzB,mBAAa,QAAQ;AACrB,qBAAe,QAAQ;AACvB;IACJ;AAEA,UAAM,kBAAkB,MAAM;AAC1B,qBAAe,QAAQ;AACvB,qBAAe,QAAQ;AAAA,IAC3B;AAEA,UAAM,gBAAgB,MAAM;AACxB,qBAAe,EAAE,MAAM,KAAI,CAAE;AAAA,IACjC;AAEA,UAAM,mBAAmB,MAAM;AAC3B,0BAAoB,QAAQ;AAC5B;IACJ;AAEA,UAAM,YAAY,MAAM;AACpB,iBAAW,MAAM;AAAA,IACrB;AAEA,UAAM,YAAY,MAAM;AACpB,iBAAW,MAAM;AAAA,IACrB;AAEA,UAAM,kBAAkB,MAAM;AAC1B,sBAAgB,QAAQ;AACxB,oBAAc,QAAQ;AACtB,0BAAoB,QAAQ;AAC5B,sBAAgB,QAAQ;AACxB,mBAAa,QAAQ;AACrB,mBAAa,QAAQ;AACrB,oBAAc,QAAQ;AACtB;AACA;IACJ;AAEA,UAAM,oBAAoB,CAAC,YAAY,UAAU;AAC7C,UAAI,WAAW;AACX,aAAK,QAAQ;AACb,qBAAa,QAAQ;AACrB,gBAAQ,QAAQ;AAAA,MACnB;AAED,UAAI,CAAC,QAAQ,OAAO;AAChB,YAAI,WAAW;AAAOA,wBAAG,MAAC,oBAAmB;AAC7C;AAAA,MACH;AAEDA,oBAAAA,MAAY,MAAA,OAAA,oCAAA,oBAAoB;AAAA,QAC5B,MAAM,KAAK;AAAA,QACX,SAAS,cAAc;AAAA,QACvB,UAAU,gBAAgB;AAAA,QAC1B,aAAa,oBAAoB;AAAA,QACjC,MAAM,aAAa;AAAA,QACnB,MAAM,aAAa;AAAA,MAC3B,CAAK;AAGD,iBAAW,MAAM;AACb,cAAM,WAAW;AAAA,UACb,EAAE,IAAI,KAAK,MAAM,kCAAkC,MAAM,SAAS,aAAa,oBAAoB,UAAU,QAAQ,aAAa,QAAQ,OAAO,UAAU,MAAM,MAAM,UAAU,MAAO;AAAA,UACxL,EAAE,IAAI,KAAK,MAAM,kCAAkC,MAAM,UAAU,aAAa,oBAAoB,UAAU,QAAQ,aAAa,QAAQ,OAAO,WAAW,MAAM,OAAO,UAAU,MAAO;AAAA;AAAA,QAEvM;AAGQ,YAAI,KAAK,QAAQ,GAAG;AAChB,uBAAa,QAAQ,aAAa,MAAM,OAAO,CAAE,CAAA;AACjD,kBAAQ,QAAQ;AAAA,QAC5B,OAAe;AACH,uBAAa,QAAQ,YAAY,WAAW,aAAa,MAAM,OAAO,QAAQ;AAC9E,kBAAQ,QAAQ;AAAA,QACnB;AAED,aAAK;AACL,YAAI,WAAW,OAAO;AAClBA,wBAAG,MAAC,oBAAmB;AACvB,qBAAW,QAAQ;AAAA,QACtB;AAAA,MACJ,GAAE,GAAI;AAAA,IACX;AAEA,UAAM,gBAAgB,MAAM;AACxB,wBAAkB,IAAI;AAAA,IAC1B;AAEA,UAAM,YAAY,MAAM;AACpB,UAAI,WAAW;AAAO;AACtB,iBAAW,QAAQ;AACnB,wBAAkB,IAAI;AAAA,IAC1B;AAEA,UAAM,WAAW,MAAM;AACnB;IACJ;AAEA,UAAM,uBAAuB,CAAC,OAAO;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACX,KAAK,kCAAkC,EAAE;AAAA,MACjD,CAAK;AAAA,IACL;AAEA,UAAM,iBAAiB,CAAC,OAAO;AAC3BA,oBAAY,MAAA,MAAA,OAAA,oCAAA,SAAS,EAAE;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,MACd,CAAK;AAAA,IACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9cA,GAAG,WAAW,eAAe;"}