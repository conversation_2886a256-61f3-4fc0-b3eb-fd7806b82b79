<template>
  <view class="page-bg">
    <!-- 顶部导航栏 -->
    <view class="navbar">
      <view class="nav-left" @click="goBack">
        <image class="nav-icon" src="/static/images/tabbar/返回.png"></image>
			</view>
      <view class="nav-title">发布成功</view>
      <view class="nav-right">
        <image class="nav-icon" src="/static/images/tabbar/menu.png"></image>
        <image class="nav-icon" src="/static/images/tabbar/refresh.png" style="margin-left: 16rpx;"></image>
			</view>
		</view>
		
    <!-- 主内容卡片 -->
    <view class="main-card">
      <view class="main-title">信息发布成功 - 磁州生活网</view>
      <view class="main-desc">完善信息或置顶、红包，可以大幅度提高信息传播效果哦</view>
      <view class="main-btns">
        <view
          v-for="(btn, idx) in btnList"
          :key="idx"
          :class="['main-btn', idx === 1 ? 'main-btn-active' : '']"
          @click="btn.action"
        >
          <view class="btn-text-container">
            <text class="btn-text">{{ btn.text1 }}</text>
            <text class="btn-text">{{ btn.text2 }}</text>
				</view>
				</view>
			</view>
		</view>
		
    <!-- 置顶推广卡片 - 使用ConfigurablePremiumActions组件 -->
    <view class="premium-card">
      <view class="premium-title">
        <view class="premium-title-icon">
          <image src="/static/images/premium/top.png" mode="aspectFit"></image>
        </view>
        <text>置顶信息，提升10倍曝光率</text>
      </view>
      <ConfigurablePremiumActions
        pageType="publish_top"
        showMode="direct"
        :itemData="publishData"
        @action-completed="handleTopActionCompleted"
        @action-cancelled="handleTopActionCancelled"
      />
    </view>
		
		<!-- 悬浮按钮 -->
		<button class="float-btn share-btn" open-type="share" @click="beforeShare">
			<image class="float-icon" src="/static/images/tabbar/share.png"></image>
		</button>
    <view class="float-btn kefu-btn" @click="showKefu">
      <image class="float-icon" src="/static/images/tabbar/kefu.png"></image>
		</view>
    
    <!-- 置顶结果提示 -->
    <view class="top-result" v-if="topResultVisible">
      <view class="top-result-content">
        <image class="top-result-icon" :src="topSuccess ? '/static/images/pay/success.png' : '/static/images/pay/fail.png'"></image>
        <view class="top-result-text">{{ topResultText }}</view>
      </view>
    </view>
    
    <!-- 分享提示弹窗 -->
    <view class="share-tips-overlay" v-if="shareTipsVisible">
      <view class="share-tips-card" @click.stop>
        <view class="share-tips-icon">
          <image src="/static/images/crown.png" mode="aspectFit" class="crown-icon"></image>
        </view>
        <view class="share-tips-title">恭喜你!获得免费置顶和群发机会</view>
        
        <view class="share-tips-item">
          <view class="tips-item-number">1</view>
          <view class="tips-item-content">
            <text class="tips-text">把信息分享到朋友圈、微信群或好友处，</text>
            <text class="tips-text">您的信息将自动置顶1天！</text>
          </view>
        </view>
        
        <view class="share-tips-item">
          <view class="tips-item-number">2</view>
          <view class="tips-item-content">
            <text class="tips-text">把你发布的信息发送给客服后，客服会给</text>
            <text class="tips-text">您群发多个群，扩大曝光量!</text>
          </view>
        </view>
        
        <view class="share-tips-btns">
          <view class="share-btn-item close-btn" @click="hideShareTips" style="background-color: #FFFFFF; color: #999999; border-right: 1rpx solid #EEEEEE; height: 90rpx; font-size: 30rpx; font-weight: 500; display: flex; align-items: center; justify-content: center;">关闭</view>
          <view class="share-btn-item share-btn-blue pulsing-btn" @click="jumpToDetailAndShare" style="background-color: #007AFF !important; color: #FFFFFF !important; position: relative; line-height: 90rpx; height: 90rpx; padding: 0; overflow: visible; border:none; box-sizing: border-box; display: flex; align-items: center; justify-content: center;">
            <text style="color: #FFFFFF !important; font-size: 30rpx; font-weight: bold;">去分享</text>
          </view>
          <view class="share-btn-item share-btn-green" @click="contactService" style="background-color: #07C160; color: #FFFFFF; height: 90rpx; font-size: 30rpx; font-weight: 500; display: flex; align-items: center; justify-content: center;">加客服</view>
        </view>
      </view>
    </view>
    
    <!-- 客服二维码弹窗 -->
    <view class="qrcode-overlay" v-if="qrcodeVisible" @click="hideQrcode">
      <view class="qrcode-card" @click.stop>
        <view class="qrcode-header">
          <view class="qrcode-close" @click="hideQrcode">
            <svg t="1692586074385" class="close-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="20">
              <path d="M572.16 512l183.466667-183.04a42.666667 42.666667 0 1 0-60.586667-60.586667L512 451.84 328.96 268.373333a42.666667 42.666667 0 0 0-60.586667 60.586667l183.04 183.04-183.04 183.466667a42.666667 42.666667 0 0 0 60.586667 60.586666L512 572.16l183.04 183.466667a42.666667 42.666667 0 0 0 60.586667-60.586667z" fill="#999999"></path>
            </svg>
          </view>
        </view>
        
        <view class="qrcode-content">
          <view class="qrcode-title-container">
            <image class="qrcode-title-icon" src="/static/images/icons/customer-service.png" mode="aspectFit"></image>
            <text class="qrcode-title">微信扫码添加客服</text>
          </view>
          
        <view class="qrcode-desc">添加客服微信，提供更多发布推广服务</view>
          
          <view class="qrcode-image-container">
        <image src="/static/images/qrcode.png" mode="aspectFit" class="qrcode-image"></image>
            <view class="qrcode-scan-hint">
              <view class="scan-icon-container">
                <svg viewBox="0 0 24 24" width="16" height="16" class="scan-icon">
                  <path fill="currentColor" d="M9.5,6.5v3h-3v-3H9.5 M11,5H5v6h6V5L11,5z M9.5,14.5v3h-3v-3H9.5 M11,13H5v6h6V13z M17.5,6.5v3h-3v-3H17.5 M19,5h-6v6h6V5L19,5z M13,13h1.5v1.5H13V13z M14.5,14.5H16V16h-1.5V14.5z M16,13h1.5v1.5H16V13z M13,16h1.5v1.5H13V16z M14.5,17.5H16V19h-1.5V17.5z M16,16h1.5v1.5H16V16z M17.5,14.5H19V16h-1.5V14.5z M17.5,17.5H19V19h-1.5V17.5z M22,7h-2V4h-3V2h5V7z M22,22v-5h-2v3h-3v2H22z M2,22h5v-2H4v-3H2V22z M2,2v5h2V4h3V2H2z"/>
                </svg>
        </view>
              <text>长按识别二维码添加客服</text>
        </view>
      </view>
          
          <view class="qrcode-info-container">
            <view class="qrcode-info-item">
              <svg viewBox="0 0 24 24" width="16" height="16" class="info-icon">
                <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,7H13V13H11V7M11,15H13V17H11V15Z"/>
              </svg>
              <text>客服在线时间: 8:00-22:00</text>
            </view>
            <view class="qrcode-info-item">
              <svg viewBox="0 0 24 24" width="16" height="16" class="info-icon">
                <path fill="currentColor" d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,6A2,2 0 0,0 10,8A2,2 0 0,0 12,10A2,2 0 0,0 14,8A2,2 0 0,0 12,6M12,13C14.67,13 20,14.33 20,17V20H4V17C4,14.33 9.33,13 12,13M12,14.9C9.03,14.9 5.9,16.36 5.9,17V18.1H18.1V17C18.1,16.36 14.97,14.9 12,14.9Z"/>
              </svg>
              <text>客服微信: cishangtc</text>
            </view>
          </view>
        </view>
        
        <view class="qrcode-actions">
          <view class="qrcode-btn copy-btn" @click="copyWechatId">
            <svg viewBox="0 0 24 24" width="16" height="16" class="btn-icon">
              <path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
            </svg>
            <text>复制微信号</text>
          </view>
          <view class="qrcode-btn save-btn" @click="saveQrcode">
            <svg viewBox="0 0 24 24" width="16" height="16" class="btn-icon">
              <path fill="currentColor" d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/>
            </svg>
            <text>保存图片</text>
          </view>
        </view>
      </view>
    </view>
	</view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';

const btnList = [
  { text1: '管理', text2: '信息', action: () => uni.reLaunch({ url: '/pages/my/publish' }) },
  { text1: '查看', text2: '信息', action: viewInfo },
  { text1: '再发', text2: '一条', action: () => uni.navigateTo({ url: '/pages/publish/publish' }) },
  { text1: '分享', text2: '信息', action: shareInfo },
  { text1: '首', text2: '页', action: () => uni.switchTab({ url: '/pages/index/index' }) }
];

// 置顶结果相关
const topResultVisible = ref(false);
const topSuccess = ref(false);
const topResultText = ref('');

// 分享提示相关
const shareTipsVisible = ref(false);

// 客服二维码相关
const qrcodeVisible = ref(false);
const wechatId = ref('cishangtc'); // 客服微信ID

// 发布ID
const publishId = ref('');

// 发布内容数据 - 适配ConfigurablePremiumActions组件
const publishData = ref({
  id: '',
  title: '信息置顶',
  description: '置顶您的信息，获得更多曝光'
});

// 原始发布内容数据
const originalPublishData = ref({
  title: '',
  content: '',
  images: [],
  category: ''
});

// 页面加载时显示分享提示
onMounted(() => {
  // 获取发布的信息数据
  const pubId = uni.getStorageSync('lastPublishId');
  publishId.value = pubId;
  const pubData = uni.getStorageSync('lastPublishData');
  
  if (pubData) {
    // 同步数据到首页相应分类
    updateHomePageData(pubData);

    // 保存原始发布数据，用于分享
    originalPublishData.value = {
      title: pubData.title || pubData.content || '磁州同城信息',
      content: pubData.content || pubData.description || '',
      images: pubData.images || [],
      category: pubData.category || pubData.categoryName || '同城信息'
    };

    // 设置ConfigurablePremiumActions组件需要的数据
    publishData.value = {
      id: pubId || 'publish_' + Date.now(),
      title: '信息置顶',
      description: `置顶"${pubData.title || pubData.content || '您的信息'}"，获得更多曝光`
    };
  }
  
  // 检查信息是否已经置顶
  checkTopStatus(pubId);
  
  // 添加显示分享提示卡片的代码
  setTimeout(() => {
    shareTipsVisible.value = true;
  }, 1000);
});

// 检查信息是否已经置顶
const checkTopStatus = async (infoId) => {
  if (!infoId) return;
  
  try {
    uni.showLoading({ title: '加载中' });
    
    const res = await uni.request({
      url: '/api/info/top/status',
      method: 'GET',
      data: { infoId }
    });
    
    if (res.data && res.data.isTopped) {
      // 信息已置顶，显示提示
      uni.showToast({
        title: `信息已置顶，剩余${res.data.remainingDays}天`,
        icon: 'none',
        duration: 3000
      });
    }
    
    uni.hideLoading();
  } catch (error) {
    console.error('检查置顶状态失败', error);
    uni.hideLoading();
  }
};

// 添加更新首页数据的函数
const updateHomePageData = (publishData) => {
  try {
    // 获取首页全部信息列表
    const allInfoList = uni.getStorageSync('homeAllInfoList') || [];
    
    // 创建新的信息项
    const newInfo = {
      id: Date.now(),
      category: publishData.category || publishData.categoryName,
      content: publishData.content || publishData.title,
      time: new Date().toLocaleString(),
      views: 0
    };
    
    // 将新信息添加到列表前面
    allInfoList.unshift(newInfo);
    
    // 存储更新后的列表
    uni.setStorageSync('homeAllInfoList', allInfoList);
    
    console.log('成功将发布的信息同步到首页', newInfo);
  } catch (e) {
    console.error('同步数据到首页失败', e);
  }
};

// 在查看信息按钮处理函数中添加更新首页数据的功能
const viewInfo = () => {
  // 获取最后发布的ID
  const pubId = uni.getStorageSync('lastPublishId');
  // 导航到详情页
  uni.navigateTo({ 
    url: '/pages/publish/detail?id=' + pubId 
  });
};

// 处理置顶操作完成事件
const handleTopActionCompleted = (result) => {
  console.log('置顶操作完成', result);
  
  // 显示操作结果
  topResultVisible.value = true;
  topSuccess.value = true;
  
  if (result.type === 'ad') {
    topResultText.value = '广告置顶成功！信息已置顶2小时';
  } else if (result.type === 'paid') {
    topResultText.value = `付费置顶成功！信息已置顶${result.option.duration}`;
  }
  
  // 2秒后隐藏结果提示
  setTimeout(() => {
    topResultVisible.value = false;
  }, 2000);
};

// 处理置顶操作取消事件
const handleTopActionCancelled = (result) => {
  console.log('置顶操作取消', result);

  if (result.type === 'ad') {
    uni.showToast({
      title: '已取消观看广告',
      icon: 'none'
    });
  } else if (result.type === 'payment') {
    uni.showToast({
      title: '已取消支付',
      icon: 'none'
    });
  }
};

const goBack = () => uni.navigateBack();

// 隐藏分享提示弹窗
const hideShareTips = () => {
  shareTipsVisible.value = false;
};

// 分享信息
const shareInfo = () => {
  // 调用beforeShare准备分享数据
  beforeShare();
  
  // 调用系统分享
  uni.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline'],
    success: () => {
      console.log('显示分享菜单成功');
    },
    fail: (err) => {
      console.error('显示分享菜单失败', err);
      // 如果显示分享菜单失败，则显示分享提示弹窗
  shareTipsVisible.value = true;
    }
  });
};

// 添加beforeShare函数，在分享前准备分享数据
const beforeShare = () => {
  console.log('准备分享数据');
  
  // 获取最新的发布数据
  const pubId = uni.getStorageSync('lastPublishId');
  
  if (!pubId) {
    console.error('未找到发布ID，无法获取分享内容');
    return;
  }
  
  // 使用本地数据作为备选，以确保分享功能可用
  const pubData = uni.getStorageSync('lastPublishData');
  
  // 收集发布信息的标题和内容
  let shareTitle = '';
  let shareImage = '';
  
  if (pubData) {
    // 根据不同类型的发布信息，提取合适的标题
    if (pubData.title) {
      shareTitle = pubData.title;
    } else if (pubData.content) {
      // 使用内容的前20个字符作为标题
      shareTitle = pubData.content.substring(0, 20) + (pubData.content.length > 20 ? '...' : '');
    } else {
      // 默认标题
      shareTitle = pubData.category || pubData.categoryName || '磁州同城信息';
    }
    
    // 提取第一张图片作为分享图片
    if (pubData.images && pubData.images.length > 0) {
      shareImage = pubData.images[0];
    }
  } else {
    shareTitle = '磁州同城信息';
  }
  
  // 设置全局分享数据
  const app = getApp();
  if (app.globalData) {
    app.globalData.shareInfo = {
      title: shareTitle,
      path: '/pages/publish/detail?id=' + pubId,
      imageUrl: shareImage
    };
  } else {
    app.globalData = { 
      shareInfo: {
        title: shareTitle,
        path: '/pages/publish/detail?id=' + pubId,
        imageUrl: shareImage
      }
    };
  }
  
  console.log('分享数据已准备:', app.globalData.shareInfo);
};

// 跳转到详情页并分享
const jumpToDetailAndShare = () => {
  const pubId = uni.getStorageSync('lastPublishId');
  if (pubId) {
    // 先准备分享数据
    beforeShare();
    // 隐藏当前提示
    hideShareTips();
    // 跳转到信息展示详情页并设置参数，表示需要自动打开分享菜单
    uni.navigateTo({
      url: `/pages/publish/info-detail?id=${pubId}&autoShare=true`,
      success: () => {
        console.log('已跳转到信息展示详情页，准备自动分享');
      },
      fail: (err) => {
        console.error('跳转信息展示详情页失败', err);
        uni.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
    });
      }
    });
  } else {
    uni.showToast({
      title: '信息ID不存在',
      icon: 'none'
    });
  }
};

// 联系客服
const contactService = () => {
  // 显示客服二维码弹窗
  qrcodeVisible.value = true;
  hideShareTips();
};

// 隐藏二维码弹窗
const hideQrcode = () => {
  qrcodeVisible.value = false;
};

// 复制微信号
const copyWechatId = () => {
  uni.setClipboardData({
    data: wechatId.value,
    success: function() {
					uni.showToast({
        title: '客服微信号已复制',
        icon: 'success'
      });
    }
  });
};

// 保存二维码图片
const saveQrcode = () => {
  uni.getSetting({
    success: (res) => {
      // 检查是否有保存到相册的权限
      if (!res.authSetting['scope.writePhotosAlbum']) {
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => {
            saveQrcodeToAlbum();
          },
          fail: () => {
            uni.showModal({
              title: '提示',
              content: '需要授权保存图片到相册',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting();
                }
              }
            });
          }
        });
      } else {
        saveQrcodeToAlbum();
      }
    }
  });
};

// 保存二维码到相册
const saveQrcodeToAlbum = () => {
  uni.showLoading({ title: '保存中...' });
  
  // 将网络图片先下载到本地
  uni.downloadFile({
    url: '/static/images/qrcode.png', // 这里应该是完整的URL路径
    success: (res) => {
      if (res.statusCode === 200) {
        // 保存图片到相册
        uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.hideLoading();
            uni.showToast({
              title: '二维码已保存到相册',
              icon: 'success'
            });
          },
          fail: (err) => {
            uni.hideLoading();
            console.error('保存失败', err);
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
      } else {
        uni.hideLoading();
        uni.showToast({
          title: '下载图片失败',
          icon: 'none'
        });
      }
    },
    fail: () => {
      uni.hideLoading();
      uni.showToast({
        title: '下载图片失败',
        icon: 'none'
      });
    }
  });
};

// 显示客服
const showKefu = () => {
  qrcodeVisible.value = true;
};
</script>

<style scoped>
.page-bg {
	min-height: 100vh;
  background: linear-gradient(180deg, #4a90e2 0%, #eaf6ff 100%);
	position: relative;
  padding-bottom: 60rpx;
}
.navbar {
  height: 120rpx;
  padding-top: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
  background: linear-gradient(90deg, #2580e6 0%, #4a90e2 100%);
	position: relative;
}
.nav-left, .nav-right {
	position: absolute;
  top: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
}
.nav-left { 
  left: 30rpx; 
  top: 70rpx;
  height: 40rpx;
}
.nav-right { 
  right: 30rpx; 
}
.nav-title {
  color: #fff;
	font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}
.nav-icon {
  width: 38rpx;
  height: 38rpx;
}
.main-card {
  background: #fff;
  border-radius: 24rpx;
  margin: 40rpx 30rpx 0 30rpx;
  padding: 40rpx 30rpx 30rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
	display: flex;
  flex-direction: column;
	align-items: center;
}
.main-title {
  color: #1976d2;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-align: center;
}
.main-desc {
  color: #888;
  font-size: 24rpx;
  margin-bottom: 36rpx;
  text-align: center;
}
.main-btns {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 10rpx;
}
.main-btn {
  flex: 1;
  margin: 0 10rpx;
  background: #eaf3ff;
  color: #1976d2;
  border-radius: 32rpx;
  font-weight: 500;
  text-align: center;
  padding: 25rpx 0;
  height: 180rpx;
  transition: background 0.2s, color 0.2s;
	display: flex;
	align-items: center;
	justify-content: center;
}
.btn-text-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
  height: 100%;
  padding: 10rpx 0;
}
.btn-text {
  font-size: 28rpx;
  line-height: 1.8;
  display: block;
  font-weight: bold;
  letter-spacing: 2rpx;
}
.main-btn-active {
  background: linear-gradient(90deg, #1976d2 0%, #4a90e2 100%);
  color: #fff;
}
.premium-card {
  background: #fff;
  border-radius: 24rpx;
  margin: 36rpx 30rpx 0 30rpx;
  padding: 36rpx 30rpx 30rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}
.premium-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.premium-title-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
  background: linear-gradient(135deg, #3b7dfc 0%, #5e96ff 100%);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
}
.premium-title-icon image {
  width: 28rpx;
  height: 28rpx;
  filter: brightness(10);
}
.float-btn {
  position: fixed;
  right: 40rpx;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.12);
	display: flex;
	align-items: center;
	justify-content: center;
  z-index: 100;
}
.share-btn { bottom: 180rpx; }
.kefu-btn { bottom: 70rpx; }
.float-icon {
  width: 48rpx;
  height: 48rpx;
}
.top-result {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
	display: flex;
	justify-content: center;
  align-items: center;
}
.top-result-content {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  width: 80%;
  max-width: 600rpx;
  text-align: center;
}
.top-result-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}
.top-result-text {
  font-size: 28rpx;
  color: #333;
}
.share-tips-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
	display: flex;
	justify-content: center;
	align-items: center;
  animation: fadeIn 0.3s ease;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
.share-tips-card {
  width: 85%;
	background-color: #FFFFFF;
	border-radius: 28rpx;
	overflow: hidden;
	display: flex;
	flex-direction: column;
  position: relative;
  animation: zoomIn 0.3s ease;
}
@keyframes zoomIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
.share-tips-icon {
  text-align: center;
  margin-top: 30rpx;
}
.crown-icon {
  width: 100rpx;
  height: 100rpx;
}
.share-tips-title {
  color: #FF6600;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin: 20rpx 0 40rpx;
  text-shadow: 0 1rpx 3rpx rgba(255, 102, 0, 0.2);
}
.share-tips-item {
	display: flex;
  padding: 20rpx 30rpx;
  align-items: flex-start;
  margin-bottom: 10rpx;
}
.tips-item-number {
  width: 44rpx;
  height: 44rpx;
  background-color: #007AFF;
  color: #FFFFFF;
  border-radius: 50%;
  text-align: center;
  line-height: 44rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.tips-item-content {
	flex: 1;
}
.tips-text {
  font-size: 28rpx;
	color: #333333;
  line-height: 1.6;
  display: block;
}
.tips-counter {
  background-color: #F1F1F1;
  color: #999999;
  font-size: 24rpx;
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
  display: inline-block;
  margin-top: 10rpx;
}
.share-tips-btns {
	display: flex;
  margin-top: 30rpx;
  border-top: 1rpx solid #EEEEEE;
  flex-direction: row;
  width: 100%;
}
.share-btn-item {
  flex: 1;
  height: 90rpx;
	display: flex;
	align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 500;
  text-align: center;
}
button.share-btn-item {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 90rpx !important;
  font-size: 30rpx !important;
  font-weight: 500 !important;
  box-sizing: border-box !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  flex: 1 !important;
  height: 90rpx !important;
  width: auto !important;
  min-width: 0 !important;
  min-height: 0 !important;
  background-color: initial;
  border: none !important;
  position: relative;
}
.close-btn {
  color: #999999;
  border-right: 1rpx solid #EEEEEE;
  background-color: #FFFFFF;
}
.share-btn-blue {
  background-color: #007AFF !important;
  color: #FFFFFF !important;
  position: relative !important;
  overflow: visible !important;
  min-height: 90rpx !important;
  font-weight: bold;
  animation: pulsing 2s infinite;
}

.share-btn-blue view {
  background-color: #007AFF !important;
  color: #FFFFFF !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

@keyframes pulsing {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.7);
    transform: scale(1);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 122, 255, 0);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0);
    transform: scale(1);
  }
}

.pulsing-btn {
  animation: pulsing 2s infinite;
}

button.share-btn-item::after {
  display: none !important;
  border: none !important;
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  content: none !important;
}

button.share-btn-blue::after {
  display: none !important;
}

button.float-btn {
  margin: 0;
  padding: 0;
  line-height: normal;
  background-color: #fff;
  border: none;
}

.share-btn-green {
  background-color: #07C160;
  color: #FFFFFF;
}
.view-count {
  color: #FF6600;
  font-weight: bold;
  margin: 0 6rpx;
}
.qrcode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

.qrcode-card {
  width: 85%;
  max-width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  animation: zoomIn 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.qrcode-header {
  position: relative;
  height: 40rpx;
  padding: 20rpx;
}

.qrcode-close {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.qrcode-content {
  padding: 0 40rpx 30rpx;
}

.qrcode-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.qrcode-title-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 12rpx;
}

.qrcode-title {
  color: #1677FF;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin: 0;
}

.qrcode-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.qrcode-image-container {
  position: relative;
  width: 100%;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-image {
  width: 320rpx;
  height: 320rpx;
  object-fit: contain;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  background-color: #ffffff;
  padding: 20rpx;
}

.qrcode-scan-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
  color: #666;
  font-size: 24rpx;
}

.scan-icon-container {
  margin-right: 8rpx;
  color: #1677FF;
}

.qrcode-info-container {
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.qrcode-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  color: #333;
}

.qrcode-info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  margin-right: 10rpx;
  color: #1677FF;
}

.qrcode-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.qrcode-btn {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  position: relative;
}

.qrcode-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.copy-btn {
  color: #1677FF;
  border-right: 1rpx solid #f0f0f0;
}

.save-btn {
  background: linear-gradient(135deg, #1677FF, #0062FF);
  color: #FFFFFF;
}

.btn-icon {
  margin-right: 8rpx;
}
</style> 