/**
 * 分销功能混入
 * 为使用Options API的组件提供分销相关功能
 */
import { distributionService } from '/subPackages/merchant-admin-marketing/services/distributionService';

export default {
  data() {
    return {
      hasMerchantDistribution: false, // 商家是否开通分销功能
      distributionSettings: {
        enabled: false,
        commissionMode: 'percentage', // 'percentage'百分比 或 'fixed'固定金额
        commissions: {
          level1: '',
          level2: '',
          level3: ''
        },
        enableLevel3: false
      }
    };
  },
  
  methods: {
    /**
     * 检查商家是否开通分销功能
     */
    async checkMerchantDistribution() {
      try {
        // 获取商家信息
        const merchantInfo = this.$store.state.merchant.merchantInfo || {};
        
        // 检查是否开通分销功能
        this.hasMerchantDistribution = await distributionService.checkMerchantDistribution(merchantInfo);
        
        // 如果开通了分销功能，获取默认设置
        if (this.hasMerchantDistribution) {
          const settings = await distributionService.getMerchantDistributionSettings(merchantInfo);
          this.distributionSettings = settings;
        }
        
        return this.hasMerchantDistribution;
      } catch (error) {
        console.error('检查分销功能失败', error);
        this.hasMerchantDistribution = false;
        return false;
      }
    },
    
    /**
     * 更新分销设置
     * @param {Object} settings - 分销设置
     */
    updateDistributionSettings(settings) {
      this.distributionSettings = settings;
    },
    
    /**
     * 保存活动分销设置
     * @param {string} activityType - 活动类型
     * @param {string} activityId - 活动ID
     * @returns {Promise<boolean>} 是否保存成功
     */
    async saveActivityDistributionSettings(activityType, activityId) {
      if (!this.hasMerchantDistribution || !this.distributionSettings.enabled) {
        return true; // 如果未启用分销，直接返回成功
      }
      
      try {
        // 验证分销设置
        const { valid, errors } = distributionService.validateDistributionSettings(this.distributionSettings);
        if (!valid) {
          uni.showToast({
            title: errors[0],
            icon: 'none'
          });
          return false;
        }
        
        // 保存分销设置
        const success = await distributionService.saveActivityDistributionSettings(
          activityType, 
          activityId, 
          this.distributionSettings
        );
        
        if (!success) {
          uni.showToast({
            title: '保存分销设置失败',
            icon: 'none'
          });
          return false;
        }
        
        return true;
      } catch (error) {
        console.error('保存分销设置失败', error);
        uni.showToast({
          title: '保存分销设置失败',
          icon: 'none'
        });
        return false;
      }
    }
  },
  
  mounted() {
    // 组件挂载时检查分销功能
    this.checkMerchantDistribution();
  }
}; 