<template>
  <view class="report-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="back-btn" @click="navigateBack">
        <view class="back-icon"></view>
      </view>
      <view class="navbar-title">举报信息</view>
      <view class="navbar-right"></view>
    </view>
    
    <view class="content-area">
      <!-- 举报选项 -->
      <view class="report-title">举报原因 (必选)</view>
      <view class="report-options">
        <view 
          v-for="(item, index) in reportOptions" 
          :key="index" 
          class="report-option-item"
          :class="{ 'selected': selectedOption === index }"
          @click="selectOption(index)"
        >
          <text class="option-text">{{ item }}</text>
          <view class="option-check" v-if="selectedOption === index">✓</view>
        </view>
      </view>
      
      <!-- 补充说明 -->
      <view class="report-title">补充说明 (选填)</view>
      <view class="description-area">
        <textarea 
          class="description-input" 
          v-model="description" 
          placeholder="请详细描述您遇到的问题，有助于我们更快核实和处理..." 
          maxlength="200"
        />
        <view class="word-count">{{ description.length }}/200</view>
      </view>
      
      <!-- 上传图片 -->
      <view class="report-title">上传凭证 (选填)</view>
      <view class="upload-area">
        <view 
          class="upload-item" 
          v-for="(img, index) in uploadedImages" 
          :key="index"
        >
          <image class="uploaded-image" :src="img" mode="aspectFill"></image>
          <view class="delete-btn" @click="deleteImage(index)">×</view>
        </view>
        
        <view class="upload-btn" v-if="uploadedImages.length < 3" @click="chooseImage">
          <text class="upload-icon">+</text>
          <text class="upload-text">添加图片</text>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="report-title">联系方式</view>
      <input 
        class="contact-input" 
        v-model="contactInfo" 
        placeholder="请留下您的手机号或微信，方便我们联系您" 
        maxlength="30"
      />
      
      <!-- 隐私提示 -->
      <view class="privacy-tip">
        <text class="tip-text">平台会对您的举报信息严格保密，请放心填写</text>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-area">
      <button class="submit-btn" :disabled="!selectedOption" @click="submitReport">提交举报</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      reportOptions: [
        '虚假信息',
        '色情低俗',
        '违法违规',
        '联系方式错误',
        '广告骚扰',
        '重复信息',
        '其他原因'
      ],
      selectedOption: null,
      description: '',
      uploadedImages: [],
      contactInfo: '',
      // 可以从路由参数获取被举报的信息ID
      reportedItemId: '',
      contentType: 'info' // 默认为普通信息
    }
  },
  onLoad(options) {
    // 获取路由参数
    if (options.id) {
      this.reportedItemId = options.id;
    }
    
    if (options.type) {
      this.contentType = options.type;
    }
    
    // 获取用户信息，可以自动填充联系方式
    const userInfo = uni.getStorageSync('userInfo');
    if (userInfo && userInfo.phone) {
      this.contactInfo = userInfo.phone;
    }
  },
  methods: {
    // 返回上一页
    navigateBack() {
      uni.navigateBack();
    },
    
    // 选择举报选项
    selectOption(index) {
      this.selectedOption = index;
    },
    
    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 3 - this.uploadedImages.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadedImages = [...this.uploadedImages, ...res.tempFilePaths];
        }
      });
    },
    
    // 删除图片
    deleteImage(index) {
      this.uploadedImages.splice(index, 1);
    },
    
    // 提交举报
    submitReport() {
      if (this.selectedOption === null) {
        uni.showToast({
          title: '请选择举报原因',
          icon: 'none'
        });
        return;
      }
      
      // 提交举报信息
      uni.showLoading({
        title: '提交中...'
      });
      
      // 构建举报数据
      const reportData = {
        reason: this.reportOptions[this.selectedOption],
        description: this.description,
        contactInfo: this.contactInfo,
        contentId: this.reportedItemId,
        contentType: this.contentType,
        images: this.uploadedImages
      };
      
      console.log('提交举报数据:', reportData);
      
      // 模拟API请求
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '举报成功',
          icon: 'success'
        });
        
        // 2秒后返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
      }, 1500);
    }
  }
}
</script>

<style lang="scss" scoped>
.report-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.custom-navbar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  border-bottom: 1rpx solid #f0f0f0;
  box-sizing: border-box;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #333;
  border-left: 4rpx solid #333;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  color: #333;
  font-size: 32rpx;
  font-weight: 600;
}

.navbar-right {
  width: 60rpx;
}

.content-area {
  padding: 30rpx;
}

.report-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
  margin-top: 40rpx;
}

.report-title:first-child {
  margin-top: 0;
}

.report-options {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.report-option-item {
  position: relative;
  padding: 28rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-option-item:last-child {
  border-bottom: none;
}

.report-option-item.selected {
  background-color: #f0f8ff;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.option-check {
  color: #1677FF;
  font-weight: bold;
  font-size: 32rpx;
}

.description-area {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  position: relative;
}

.description-input {
  width: 100%;
  height: 200rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.upload-area {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.upload-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  background-color: #f5f5f5;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 50rpx;
  color: #999;
  margin-bottom: 10rpx;
  line-height: 1;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

.contact-input {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 25rpx 20rpx;
  font-size: 28rpx;
  color: #333;
}

.privacy-tip {
  margin-top: 20rpx;
  padding: 0 10rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.submit-area {
  padding: 30rpx;
  margin-top: 20rpx;
}

.submit-btn {
  background-color: #1677FF;
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-weight: 500;
}

.submit-btn[disabled] {
  background-color: #ccc;
  color: #fff;
}
</style> 