/**
 * 内容审核工具模块
 * 用于检测用户输入的文本和上传的图片是否包含违规内容
 */
// 移除错误的导入方式，直接使用全局变量
// import { uniCloud } from '@dcloudio/uni-app';

// 违规词汇库（示例）- 实际应该放在云端或后端
const SENSITIVE_WORDS = [
  '赌博', '博彩', '色情', '暴力', '毒品', '违禁品', 
  '诈骗', '传销', '非法', '黄色', '办证', '枪支', 
  '走私', '病毒', '黑客', '私服', '外挂', '代考', 
  '代写', '代办', '伪造', '假证', '黑产', '裸聊'
];

// 违规类型定义
export const VIOLATION_TYPES = {
  NORMAL: 0,       // 正常内容
  SENSITIVE: 1,    // 敏感内容
  POLITICAL: 2,    // 政治敏感
  PORN: 3,         // 色情
  ABUSE: 4,        // 辱骂
  VIOLENCE: 5,     // 暴力
  FRAUD: 6,        // 诈骗
  ILLEGAL: 7,      // 其他违法
  AD: 8,           // 广告
  SPAM: 9          // 垃圾信息
};

/**
 * 文本内容审核
 * @param {String} text 待审核的文本内容
 * @returns {Promise} 审核结果
 */
export const checkText = async (text) => {
  if (!text || text.trim() === '') {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容正常'
    };
  }
  
  try {
    // 开发环境进行简易本地检测
    if (process.env.NODE_ENV === 'development') {
      return localCheckText(text);
    }
    
    // 生产环境调用云函数进行内容检测
    const result = await uniCloud.callFunction({
      name: 'contentCheck',
      data: {
        type: 'text',
        content: text
      }
    });
    
    return result.result || {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容正常'
    };
  } catch (error) {
    console.error('文本内容审核异常:', error);
    // 审核失败默认放行，由人工二次审核
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容审核失败，将进行人工复核',
      needReview: true
    };
  }
};

/**
 * 图片内容审核
 * @param {String|Array} images 图片路径或路径数组
 * @returns {Promise} 审核结果
 */
export const checkImage = async (images) => {
  if (!images) {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容正常'
    };
  }
  
  // 转换为数组形式处理
  const imageArray = Array.isArray(images) ? images : [images];
  
  if (imageArray.length === 0) {
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容正常'
    };
  }
  
  try {
    // 开发环境暂不做实际检测，默认通过
    if (process.env.NODE_ENV === 'development') {
      return {
        pass: true,
        type: VIOLATION_TYPES.NORMAL,
        message: '开发环境图片默认通过'
      };
    }
    
    // 检测每张图片
    const checkResults = await Promise.all(
      imageArray.map(async (imagePath) => {
        // 调用云函数检测图片
        const result = await uniCloud.callFunction({
          name: 'contentCheck',
          data: {
            type: 'image',
            url: imagePath
          }
        });
        
        return result.result || {
          pass: true,
          type: VIOLATION_TYPES.NORMAL,
          message: '内容正常',
          url: imagePath
        };
      })
    );
    
    // 只要有一张图片不通过，则整体不通过
    const failedCheck = checkResults.find(item => !item.pass);
    
    if (failedCheck) {
      return {
        pass: false,
        type: failedCheck.type,
        message: `图片内容违规: ${failedCheck.message}`,
        url: failedCheck.url
      };
    }
    
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '所有图片内容正常'
    };
  } catch (error) {
    console.error('图片内容审核异常:', error);
    // 审核失败默认放行，由人工二次审核
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '图片审核失败，将进行人工复核',
      needReview: true
    };
  }
};

/**
 * 本地简易文本检测（仅用于开发环境）
 * @param {String} text 待检测文本
 * @returns {Object} 检测结果
 */
const localCheckText = (text) => {
  if (!text) return { pass: true, type: VIOLATION_TYPES.NORMAL, message: '内容正常' };
  
  // 转小写便于匹配
  const lowerText = text.toLowerCase();
  
  // 检测是否包含违规词汇
  for (const word of SENSITIVE_WORDS) {
    if (lowerText.includes(word)) {
      return {
        pass: false,
        type: VIOLATION_TYPES.SENSITIVE,
        message: `内容包含违规词汇: ${word}`,
        word
      };
    }
  }
  
  // 检测电话号码泄露
  const phoneRegex = /1[3-9]\d{9}/g;
  if (phoneRegex.test(text)) {
    // 此处可根据业务需求决定是否放行手机号
    return {
      pass: true,
      type: VIOLATION_TYPES.NORMAL,
      message: '内容包含电话号码，请注意保护隐私',
      containsPhone: true
    };
  }
  
  // 检测垃圾广告特征
  if (
    (lowerText.includes('优惠') && lowerText.includes('活动')) ||
    (lowerText.includes('推广') && lowerText.includes('促销')) ||
    (lowerText.includes('免费') && lowerText.includes('送')) ||
    lowerText.includes('点击链接')
  ) {
    return {
      pass: false,
      type: VIOLATION_TYPES.AD,
      message: '内容疑似广告信息'
    };
  }
  
  return {
    pass: true,
    type: VIOLATION_TYPES.NORMAL,
    message: '内容正常'
  };
};

/**
 * 综合检测内容（文本+图片）
 * @param {Object} content 内容对象 {text, images}
 * @returns {Promise} 检测结果
 */
export const checkContent = async (content) => {
  const { text, images } = content;
  
  // 并行检测文本和图片
  const [textResult, imageResult] = await Promise.all([
    checkText(text),
    checkImage(images)
  ]);
  
  // 如果任一检测不通过，则整体不通过
  if (!textResult.pass || !imageResult.pass) {
    return {
      pass: false,
      textCheck: textResult,
      imageCheck: imageResult,
      message: textResult.pass ? imageResult.message : textResult.message
    };
  }
  
  // 是否需要人工复核
  const needReview = textResult.needReview || imageResult.needReview;
  
  return {
    pass: true,
    textCheck: textResult,
    imageCheck: imageResult,
    message: '内容审核通过',
    needReview
  };
};

export default {
  checkText,
  checkImage,
  checkContent,
  VIOLATION_TYPES
}; 