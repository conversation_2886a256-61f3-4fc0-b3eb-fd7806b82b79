<template>
  <view class="verification-container">
    <!-- 自定义导航栏 - 简化版本 -->
    <view class="nav-bar">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="nav-content" style="height: calc(50px - 4px);">
        <view class="back-btn" @click="goBack">
          <image src="/static/images/tabbar/最新返回键.png" class="back-icon"></image>
        </view>
        <view class="nav-title">司机认证</view>
        <view class="right-placeholder"></view>
      </view>
    </view>
    
    <!-- 认证状态卡片 -->
    <view class="status-card" v-if="verificationStatus !== 'none'">
      <view class="status-icon" :class="verificationStatus">
        <image 
          :src="verificationStatus === 'pending' ? '/static/images/icons/pending.png' : 
                verificationStatus === 'verified' ? '/static/images/icons/verified.png' : 
                '/static/images/icons/rejected.png'" 
          class="status-img">
        </image>
      </view>
      <view class="status-text">
        {{ verificationStatus === 'pending' ? '认证审核中' : 
           verificationStatus === 'verified' ? '认证已通过' : '认证未通过' }}
      </view>
      <view class="status-desc">
        {{ verificationStatus === 'pending' ? '您的认证申请正在审核中，请耐心等待' : 
           verificationStatus === 'verified' ? '您已通过司机认证，可以发布车找人/车找货信息' : 
           '认证未通过，原因：' + rejectReason }}
      </view>
      
      <button class="reapply-btn" v-if="verificationStatus === 'rejected'" @click="resetForm">重新申请</button>
    </view>
    
    <!-- 认证表单 -->
    <view class="verification-form" v-if="verificationStatus === 'none' || (verificationStatus === 'rejected' && showForm)">
      <view class="form-title">司机认证</view>
      <view class="form-subtitle">请填写真实信息，审核通过后可发布车找人/车找货信息</view>
      
      <view class="form-group">
        <view class="form-label">真实姓名</view>
        <input type="text" class="form-input" v-model="formData.realName" placeholder="请输入真实姓名" />
      </view>
      
      <view class="form-group">
        <view class="form-label">身份证号</view>
        <input type="idcard" class="form-input" v-model="formData.idCard" placeholder="请输入身份证号" maxlength="18" />
      </view>
      
      <view class="form-group">
        <view class="form-label">驾驶证号</view>
        <input type="text" class="form-input" v-model="formData.driverLicense" placeholder="请输入驾驶证号" />
      </view>
      
      <view class="form-group">
        <view class="form-label">驾驶证有效期</view>
        <view class="date-picker" @click="openDatePicker">
          <text class="date-text">{{ formData.licenseExpireDate || '请选择有效期' }}</text>
          <image src="/static/images/icons/calendar.png" class="date-icon"></image>
        </view>
      </view>
      
      <view class="form-group">
        <view class="form-label">车牌号</view>
        <input type="text" class="form-input" v-model="formData.carNumber" placeholder="请输入车牌号" />
      </view>
      
      <view class="form-group">
        <view class="form-label">车辆品牌型号</view>
        <input type="text" class="form-input" v-model="formData.carModel" placeholder="例如：本田雅阁 2022款" />
      </view>
      
      <view class="form-group">
        <view class="form-label">车辆颜色</view>
        <input type="text" class="form-input" v-model="formData.carColor" placeholder="请输入车辆颜色" />
      </view>
      
      <view class="form-group">
        <view class="form-label">车辆注册日期</view>
        <view class="date-picker" @click="openRegDatePicker">
          <text class="date-text">{{ formData.carRegDate || '请选择注册日期' }}</text>
          <image src="/static/images/icons/calendar.png" class="date-icon"></image>
        </view>
      </view>
      
      <view class="upload-section">
        <view class="upload-title">证件照片上传</view>
        
        <view class="upload-item">
          <view class="upload-label">身份证正面照</view>
          <view class="upload-box" @click="chooseImage('idCardFront')" v-if="!formData.idCardFront">
            <image src="/static/images/icons/upload.png" class="upload-icon"></image>
            <text class="upload-text">点击上传</text>
          </view>
          <view class="image-preview" v-else>
            <image :src="formData.idCardFront" mode="aspectFill" class="preview-image"></image>
            <view class="delete-btn" @click.stop="deleteImage('idCardFront')">
              <image src="/static/images/icons/delete.png" class="delete-icon"></image>
            </view>
          </view>
        </view>
        
        <view class="upload-item">
          <view class="upload-label">身份证背面照</view>
          <view class="upload-box" @click="chooseImage('idCardBack')" v-if="!formData.idCardBack">
            <image src="/static/images/icons/upload.png" class="upload-icon"></image>
            <text class="upload-text">点击上传</text>
          </view>
          <view class="image-preview" v-else>
            <image :src="formData.idCardBack" mode="aspectFill" class="preview-image"></image>
            <view class="delete-btn" @click.stop="deleteImage('idCardBack')">
              <image src="/static/images/icons/delete.png" class="delete-icon"></image>
            </view>
          </view>
        </view>
        
        <view class="upload-item">
          <view class="upload-label">驾驶证照片</view>
          <view class="upload-box" @click="chooseImage('driverLicenseImg')" v-if="!formData.driverLicenseImg">
            <image src="/static/images/icons/upload.png" class="upload-icon"></image>
            <text class="upload-text">点击上传</text>
          </view>
          <view class="image-preview" v-else>
            <image :src="formData.driverLicenseImg" mode="aspectFill" class="preview-image"></image>
            <view class="delete-btn" @click.stop="deleteImage('driverLicenseImg')">
              <image src="/static/images/icons/delete.png" class="delete-icon"></image>
            </view>
          </view>
        </view>
        
        <view class="upload-item">
          <view class="upload-label">行驶证照片</view>
          <view class="upload-box" @click="chooseImage('vehicleLicenseImg')" v-if="!formData.vehicleLicenseImg">
            <image src="/static/images/icons/upload.png" class="upload-icon"></image>
            <text class="upload-text">点击上传</text>
          </view>
          <view class="image-preview" v-else>
            <image :src="formData.vehicleLicenseImg" mode="aspectFill" class="preview-image"></image>
            <view class="delete-btn" @click.stop="deleteImage('vehicleLicenseImg')">
              <image src="/static/images/icons/delete.png" class="delete-icon"></image>
            </view>
          </view>
        </view>
      </view>
      
      <view class="agreement">
        <checkbox-group @change="checkboxChange">
          <label class="checkbox-label">
            <checkbox :checked="formData.agreement" color="#0A84FF" style="transform:scale(0.7)" />
            <text class="agreement-text">我已阅读并同意</text>
            <text class="agreement-link" @click="viewAgreement">《司机认证服务协议》</text>
          </label>
        </checkbox-group>
      </view>
      
      <button class="submit-btn" :disabled="!isFormValid" @click="submitVerification">提交认证</button>
    </view>
    
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 认证状态
const verificationStatus = ref('none'); // 'none', 'pending', 'verified', 'rejected'
const rejectReason = ref('');
const showForm = ref(false);

// 表单数据
const formData = ref({
        realName: '',
        idCard: '',
        driverLicense: '',
        licenseExpireDate: '',
        carNumber: '',
        carModel: '',
        carColor: '',
        carRegDate: '',
        idCardFront: '',
        idCardBack: '',
        driverLicenseImg: '',
        vehicleLicenseImg: '',
        agreement: false
});

// 计算表单是否有效
const isFormValid = computed(() => {
      return (
    formData.value.realName &&
    formData.value.idCard &&
    formData.value.idCard.length === 18 &&
    formData.value.driverLicense &&
    formData.value.licenseExpireDate &&
    formData.value.carNumber &&
    formData.value.carModel &&
    formData.value.carColor &&
    formData.value.carRegDate &&
    formData.value.idCardFront &&
    formData.value.idCardBack &&
    formData.value.driverLicenseImg &&
    formData.value.vehicleLicenseImg &&
    formData.value.agreement
      );
});

// 页面加载时检查认证状态
onMounted(() => {
  checkVerificationStatus();
});

    // 返回上一页
const goBack = () => {
      uni.navigateBack();
};
    
    // 检查认证状态
const checkVerificationStatus = () => {
      // 模拟API调用
      // 实际应用中应该从服务器获取认证状态
      setTimeout(() => {
        // 模拟数据
        // 可以根据需要更改为 'none', 'pending', 'verified', 'rejected'
    verificationStatus.value = 'none';
    rejectReason.value = '提供的证件信息不清晰，请重新上传清晰照片';
      }, 500);
};
    
    // 重置表单，重新申请
const resetForm = () => {
  showForm.value = true;
  formData.value = {
        realName: '',
        idCard: '',
        driverLicense: '',
        licenseExpireDate: '',
        carNumber: '',
        carModel: '',
        carColor: '',
        carRegDate: '',
        idCardFront: '',
        idCardBack: '',
        driverLicenseImg: '',
        vehicleLicenseImg: '',
        agreement: false
      };
};
    
    // 打开日期选择器 - 驾驶证有效期
const openDatePicker = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      
      uni.showDatePicker({
    date: formData.value.licenseExpireDate || `${year}-${month}-${day}`,
        success: (res) => {
      formData.value.licenseExpireDate = res.date;
        }
      });
};
    
    // 打开日期选择器 - 车辆注册日期
const openRegDatePicker = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      
      uni.showDatePicker({
    date: formData.value.carRegDate || `${year}-${month}-${day}`,
        success: (res) => {
      formData.value.carRegDate = res.date;
        }
      });
};
    
    // 选择图片
const chooseImage = (field) => {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
      formData.value[field] = res.tempFilePaths[0];
        }
      });
};
    
    // 删除图片
const deleteImage = (field) => {
  formData.value[field] = '';
};
    
    // 复选框变化
const checkboxChange = (e) => {
  formData.value.agreement = e.detail.value.length > 0;
};
    
    // 查看协议
const viewAgreement = () => {
      uni.showModal({
        title: '司机认证服务协议',
        content: '司机认证服务协议内容...',
        showCancel: false
      });
};
    
    // 提交认证
const submitVerification = () => {
  if (!isFormValid.value) {
        uni.showToast({
          title: '请完善所有必填信息',
          icon: 'none'
        });
        return;
      }
      
      uni.showLoading({
        title: '提交中...'
      });
      
      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading();
        
        // 模拟成功
        uni.showToast({
          title: '提交成功，等待审核',
          icon: 'success'
        });
        
        // 更新状态
    verificationStatus.value = 'pending';
    showForm.value = false;
        
        // 延迟返回
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 2000);
};
</script>

<style lang="scss">
/* 新导航栏样式 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #1677FF;
  z-index: 999;
  /* 标题栏高度再减少5px */
  height: calc(var(--status-bar-height) + 82px);
  width: 100vw;
}

.status-bar {
  width: 100%;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 49px;
  padding: 0 20px 8px; /* 左右内边距确保按钮与边缘有足够距离 */
  margin-top: 10px; /* 增加顶部间距，使内容整体下移 */
}

.back-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 左对齐 */
  position: relative;
  top: 29px; /* 再次下移3px */
}

.back-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  transform: translateX(8px) translateY(29px); /* 标题向右8px并下移29px */
  /* 确保文本可见 */
  display: block;
  position: relative;
  z-index: 1001;
  text-shadow: 0 1px 1px rgba(0,0,0,0.2); /* 添加文字阴影提高可读性 */
}

.right-placeholder {
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: flex-end; /* 右对齐 */
  position: relative;
  top: 29px; /* 与返回键保持一致的偏移 */
}

/* 主容器样式调整 */
.verification-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(var(--status-bar-height) + 82px); /* 再次调整后的导航栏高度 */
  padding-bottom: 40rpx;
}

/* 认证状态卡片 */
.status-card {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.status-icon.pending {
  background-color: #FF9F0A;
}

.status-icon.verified {
  background-color: #34C759;
}

.status-icon.rejected {
  background-color: #FF3B30;
}

.status-img {
  width: 60rpx;
  height: 60rpx;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  line-height: 1.5;
}

.reapply-btn {
  margin-top: 30rpx;
  background-color: #0A84FF;
  color: #FFFFFF;
  font-size: 28rpx;
  padding: 12rpx 40rpx;
  border-radius: 30rpx;
  line-height: 1.5;
}

/* 认证表单 */
.verification-form {
  margin: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}

.form-subtitle {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 30rpx;
}

.form-group {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
}

.date-picker {
  width: 100%;
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.date-text {
  font-size: 28rpx;
  color: #333333;
}

.date-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 上传部分 */
.upload-section {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}

.upload-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
}

.upload-item {
  margin-bottom: 24rpx;
}

.upload-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.upload-box {
  width: 100%;
  height: 200rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #8E8E93;
}

.image-preview {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 协议 */
.agreement {
  margin-bottom: 30rpx;
}

.checkbox-label {
  display: flex;
  align-items: center;
}

.agreement-text {
  font-size: 26rpx;
  color: #666666;
}

.agreement-link {
  font-size: 26rpx;
  color: #0A84FF;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #0A84FF, #0040DD);
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn[disabled] {
  background: #CCCCCC;
  color: #FFFFFF;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
}
</style> 