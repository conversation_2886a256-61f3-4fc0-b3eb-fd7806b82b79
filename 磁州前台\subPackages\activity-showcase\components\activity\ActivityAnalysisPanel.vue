<template>
  <view class="activity-analysis-panel" :style="chartStyle">
    <view class="panel-header">
      <text class="panel-title">活动数据分析</text>
      <view class="panel-more" @click="$emit('viewDetail')">
        <text>查看详情</text>
        <svg class="icon" viewBox="0 0 24 24" width="16" height="16">
          <path d="M9 18l6-6-6-6" stroke="#FF3B69" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </view>
    </view>
    
    <view class="chart-container">
      <view class="chart-tabs">
        <view 
          v-for="(tab, index) in chartTabs" 
          :key="index"
          class="chart-tab"
          :class="{ active: currentTab === index }"
          @click="currentTab = index"
        >
          {{ tab.name }}
        </view>
      </view>
      
      <!-- 活动参与趋势图 -->
      <view class="trend-chart" v-if="currentTab === 0">
        <view class="chart-canvas">
          <!-- 简易折线图实现 -->
          <view class="chart-grid">
            <view class="grid-line" v-for="i in 5" :key="i"></view>
          </view>
          
          <view class="chart-line">
            <view 
              class="line-segment" 
              v-for="(point, index) in trendPoints" 
              :key="index"
              :style="{
                left: `${index * (100 / (trendPoints.length - 1))}%`,
                bottom: `${point.value}%`,
                zIndex: index
              }"
            >
              <view class="point-dot"></view>
              <view class="point-value">{{ point.count }}</view>
            </view>
            
            <view 
              class="line-path"
              :style="{
                'clip-path': `polygon(${getLinePath()})`
              }"
            ></view>
          </view>
          
          <view class="chart-labels">
            <text 
              class="label-text" 
              v-for="(point, index) in trendPoints" 
              :key="index"
              :style="{
                left: `${index * (100 / (trendPoints.length - 1))}%`
              }"
            >
              {{ point.date }}
            </text>
          </view>
        </view>
      </view>
      
      <!-- 活动类型偏好 -->
      <view class="preference-chart" v-if="currentTab === 1">
        <view class="radar-chart">
          <!-- 雷达图网格 -->
          <view class="radar-grid">
            <view class="radar-polygon" v-for="level in 3" :key="level">
              <svg viewBox="0 0 200 200" width="100%" height="100%">
                <polygon :points="getRadarGrid(level)" fill="none" stroke="#E5E5EA" stroke-width="1" />
              </svg>
            </view>
          </view>
          
          <!-- 数据多边形 -->
          <view class="radar-data">
            <svg viewBox="0 0 200 200" width="100%" height="100%">
              <polygon :points="getRadarData()" fill="rgba(255,59,105,0.2)" stroke="#FF3B69" stroke-width="2" />
            </svg>
          </view>
          
          <!-- 坐标轴 -->
          <view class="radar-axes">
            <view 
              class="radar-axis" 
              v-for="(item, index) in radarData" 
              :key="index"
              :style="{
                transform: `rotate(${index * (360 / radarData.length)}deg)`
              }"
            ></view>
          </view>
          
          <!-- 标签 -->
          <view 
            class="radar-label" 
            v-for="(item, index) in radarData" 
            :key="index"
            :style="{
              left: `${50 + 45 * Math.cos((index * (360 / radarData.length) - 90) * Math.PI / 180)}%`,
              top: `${50 + 45 * Math.sin((index * (360 / radarData.length) - 90) * Math.PI / 180)}%`
            }"
          >
            {{ item.name }}
          </view>
        </view>
        
        <view class="preference-legend">
          <view class="legend-item" v-for="(item, index) in radarData" :key="index">
            <view class="legend-color" :style="{ backgroundColor: '#FF3B69' }"></view>
            <text class="legend-name">{{ item.name }}</text>
            <text class="legend-value">{{ item.value }}%</text>
          </view>
        </view>
      </view>
      
      <!-- 活动收益分析 -->
      <view class="revenue-chart" v-if="currentTab === 2">
        <view class="revenue-summary">
          <view class="summary-item">
            <text class="summary-value">¥{{ analysisData.totalSpent || 0 }}</text>
            <text class="summary-label">总消费</text>
          </view>
          
          <view class="summary-divider"></view>
          
          <view class="summary-item">
            <text class="summary-value">¥{{ analysisData.totalSaved || 0 }}</text>
            <text class="summary-label">总节省</text>
          </view>
          
          <view class="summary-divider"></view>
          
          <view class="summary-item">
            <text class="summary-value">{{ analysisData.roi || 0 }}%</text>
            <text class="summary-label">投入回报率</text>
          </view>
        </view>
        
        <view class="bar-chart">
          <view 
            class="bar-item" 
            v-for="(item, index) in revenueData" 
            :key="index"
          >
            <view class="bar-container">
              <view class="bar-fill" :style="{ height: `${item.percentage}%` }">
                <text class="bar-value">¥{{ item.value }}</text>
              </view>
            </view>
            <text class="bar-label">{{ item.month }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 组件属性定义
const props = defineProps({
  analysisData: {
    type: Object,
    default: () => ({
      trendData: [],
      preferenceData: [],
      revenueData: [],
      totalSpent: 0,
      totalSaved: 0,
      roi: 0
    })
  },
  chartStyle: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
defineEmits(['viewDetail']);

// 当前选中的标签页
const currentTab = ref(0);

// 图表标签页
const chartTabs = [
  { name: '参与趋势' },
  { name: '类型偏好' },
  { name: '收益分析' }
];

// 趋势图数据点
const trendPoints = computed(() => {
  if (!props.analysisData.trendData || props.analysisData.trendData.length === 0) {
    // 默认数据
    return [
      { date: '1月', count: 2, value: 20 },
      { date: '2月', count: 5, value: 50 },
      { date: '3月', count: 3, value: 30 },
      { date: '4月', count: 8, value: 80 },
      { date: '5月', count: 6, value: 60 },
      { date: '6月', count: 10, value: 100 }
    ];
  }
  
  // 计算最大值，用于归一化
  const maxCount = Math.max(...props.analysisData.trendData.map(item => item.count));
  
  return props.analysisData.trendData.map(item => ({
    date: item.date,
    count: item.count,
    value: maxCount > 0 ? (item.count / maxCount) * 100 : 0
  }));
});

// 获取折线图路径
const getLinePath = () => {
  if (!trendPoints.value || trendPoints.value.length === 0) return '';
  
  let path = '';
  
  // 添加起始点（左下角）
  path += '0% 0%, ';
  
  // 添加每个数据点
  trendPoints.value.forEach((point, index) => {
    const x = index * (100 / (trendPoints.value.length - 1));
    const y = point.value;
    path += `${x}% ${y}%, `;
  });
  
  // 添加结束点（右下角）
  path += '100% 0%';
  
  return path;
};

// 雷达图数据
const radarData = computed(() => {
  if (!props.analysisData.preferenceData || props.analysisData.preferenceData.length === 0) {
    // 默认数据
    return [
      { name: '拼团', value: 85 },
      { name: '秒杀', value: 65 },
      { name: '优惠券', value: 90 },
      { name: '满减', value: 75 },
      { name: '限时折扣', value: 60 }
    ];
  }
  
  return props.analysisData.preferenceData;
});

// 获取雷达图网格点
const getRadarGrid = (level) => {
  const points = [];
  const radius = (level / 3) * 80; // 最大半径的比例
  const sides = radarData.value.length;
  
  for (let i = 0; i < sides; i++) {
    const angle = (i * 2 * Math.PI / sides) - (Math.PI / 2);
    const x = 100 + radius * Math.cos(angle);
    const y = 100 + radius * Math.sin(angle);
    points.push(`${x},${y}`);
  }
  
  return points.join(' ');
};

// 获取雷达图数据点
const getRadarData = () => {
  const points = [];
  const sides = radarData.value.length;
  
  for (let i = 0; i < sides; i++) {
    const value = radarData.value[i].value / 100; // 归一化到0-1
    const radius = value * 80; // 最大半径的比例
    const angle = (i * 2 * Math.PI / sides) - (Math.PI / 2);
    const x = 100 + radius * Math.cos(angle);
    const y = 100 + radius * Math.sin(angle);
    points.push(`${x},${y}`);
  }
  
  return points.join(' ');
};

// 收益数据
const revenueData = computed(() => {
  if (!props.analysisData.revenueData || props.analysisData.revenueData.length === 0) {
    // 默认数据
    return [
      { month: '1月', value: 120, percentage: 40 },
      { month: '2月', value: 180, percentage: 60 },
      { month: '3月', value: 240, percentage: 80 },
      { month: '4月', value: 150, percentage: 50 },
      { month: '5月', value: 300, percentage: 100 },
      { month: '6月', value: 270, percentage: 90 }
    ];
  }
  
  // 计算最大值，用于归一化
  const maxValue = Math.max(...props.analysisData.revenueData.map(item => item.value));
  
  return props.analysisData.revenueData.map(item => ({
    month: item.month,
    value: item.value,
    percentage: maxValue > 0 ? (item.value / maxValue) * 100 : 0
  }));
});
</script>

<style lang="scss" scoped>
.activity-analysis-panel {
  width: 100%;
  border-radius: 35px;
  background-color: #FFFFFF;
  padding: 30rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
  box-shadow: 0 8px 20px rgba(255,59,105,0.15);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .panel-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .panel-more {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #FF3B69;
    
    .icon {
      margin-left: 4rpx;
    }
  }
}

.chart-container {
  .chart-tabs {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30rpx;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    
    .chart-tab {
      flex: 1;
      text-align: center;
      padding: 16rpx 0;
      font-size: 28rpx;
      color: #8E8E93;
      position: relative;
      
      &.active {
        color: #FF3B69;
        font-weight: 500;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 3px;
          background: linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%);
          border-radius: 1.5px;
        }
      }
    }
  }
}

/* 趋势图样式 */
.trend-chart {
  height: 400rpx;
  position: relative;
  
  .chart-canvas {
    width: 100%;
    height: 300rpx;
    position: relative;
    margin-top: 30rpx;
    
    .chart-grid {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .grid-line {
        width: 100%;
        height: 1px;
        background-color: rgba(0,0,0,0.05);
      }
    }
    
    .chart-line {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      
      .line-segment {
        position: absolute;
        transform: translate(-50%, 50%);
        
        .point-dot {
          width: 12rpx;
          height: 12rpx;
          border-radius: 6rpx;
          background-color: #FF3B69;
          box-shadow: 0 0 0 4rpx rgba(255,59,105,0.2);
        }
        
        .point-value {
          position: absolute;
          top: -30rpx;
          left: 50%;
          transform: translateX(-50%);
          font-size: 22rpx;
          color: #FF3B69;
          font-weight: 500;
        }
      }
      
      .line-path {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg, rgba(255,59,105,0.2) 0%, rgba(255,59,105,0) 100%);
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%);
          mask: linear-gradient(to top, transparent 0%, black 100%);
          -webkit-mask: linear-gradient(to top, transparent 0%, black 100%);
          opacity: 0.5;
        }
      }
    }
    
    .chart-labels {
      position: absolute;
      bottom: -40rpx;
      left: 0;
      width: 100%;
      height: 40rpx;
      
      .label-text {
        position: absolute;
        transform: translateX(-50%);
        font-size: 22rpx;
        color: #8E8E93;
      }
    }
  }
}

/* 雷达图样式 */
.preference-chart {
  .radar-chart {
    width: 100%;
    height: 400rpx;
    position: relative;
    margin-top: 30rpx;
    
    .radar-grid, .radar-data {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    
    .radar-axes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      
      .radar-axis {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 50%;
        height: 1px;
        background-color: rgba(0,0,0,0.1);
        transform-origin: left center;
      }
    }
    
    .radar-label {
      position: absolute;
      transform: translate(-50%, -50%);
      font-size: 22rpx;
      color: #333333;
      font-weight: 500;
      text-align: center;
    }
  }
  
  .preference-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin-top: 20rpx;
    
    .legend-item {
      display: flex;
      align-items: center;
      margin: 10rpx 0;
      width: 45%;
      
      .legend-color {
        width: 16rpx;
        height: 16rpx;
        border-radius: 8rpx;
        margin-right: 8rpx;
      }
      
      .legend-name {
        font-size: 24rpx;
        color: #333333;
        margin-right: 8rpx;
      }
      
      .legend-value {
        font-size: 24rpx;
        color: #FF3B69;
        font-weight: 500;
      }
    }
  }
}

/* 收益分析样式 */
.revenue-chart {
  margin-top: 30rpx;
  
  .revenue-summary {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 30rpx;
    
    .summary-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .summary-value {
        font-size: 36rpx;
        font-weight: 600;
        color: #FF3B69;
        margin-bottom: 8rpx;
      }
      
      .summary-label {
        font-size: 24rpx;
        color: #8E8E93;
      }
    }
    
    .summary-divider {
      width: 1px;
      height: 60rpx;
      background-color: rgba(0,0,0,0.1);
    }
  }
  
  .bar-chart {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    height: 300rpx;
    padding: 0 20rpx;
    
    .bar-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .bar-container {
        width: 40rpx;
        height: 200rpx;
        position: relative;
        margin-bottom: 16rpx;
        
        .bar-fill {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          background: linear-gradient(180deg, #FF3B69 0%, #FF7A9E 100%);
          border-radius: 20rpx 20rpx 0 0;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          
          .bar-value {
            font-size: 20rpx;
            color: #FFFFFF;
            transform: translateY(-24rpx);
            white-space: nowrap;
          }
        }
      }
      
      .bar-label {
        font-size: 22rpx;
        color: #8E8E93;
      }
    }
  }
}
</style> 