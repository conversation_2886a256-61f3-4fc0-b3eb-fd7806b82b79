<template>
  <view class="merchant-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">商家后台</view>
      <view class="navbar-right" @click="navigateToSettings">
        <image src="/static/images/tabbar/设置.png" class="setting-icon"></image>
      </view>
    </view>
    
    <!-- 商家信息卡片 -->
    <view class="merchant-card" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <view class="merchant-header">
        <image class="merchant-logo" :src="merchantInfo.logo || '/static/images/default-shop.png'" mode="aspectFill"></image>
        <view class="merchant-info">
          <view class="merchant-name">{{ merchantInfo.name || '未认证商家' }}</view>
          <view class="merchant-status" :class="{'verified': merchantInfo.verified}">
            <image class="status-icon" :src="merchantInfo.verified ? '/static/images/tabbar/已认证.png' : '/static/images/tabbar/未认证.png'"></image>
            <text class="status-text">{{ merchantInfo.verified ? '已认证' : '未认证' }}</text>
          </view>
        </view>
        <view class="merchant-action">
          <button v-if="!merchantInfo.verified" class="verify-btn" @click="navigateToVerify">立即认证</button>
          <button v-else class="edit-btn" @click="navigateToMerchantEdit">编辑资料</button>
        </view>
      </view>
      
      <view class="merchant-stats">
        <view class="stat-item">
          <view class="stat-number">{{ statsData.views }}</view>
          <view class="stat-label">今日浏览</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <view class="stat-number">{{ statsData.orders }}</view>
          <view class="stat-label">今日订单</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <view class="stat-number">{{ statsData.income }}</view>
          <view class="stat-label">今日收入</view>
        </view>
      </view>
    </view>
    
    <!-- 功能区域 -->
    <view class="function-section">
      <view class="section-header">
        <text class="section-title">经营管理</text>
      </view>
      
      <view class="function-grid">
        <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/goods')">
          <image class="function-icon" src="/static/images/tabbar/商品.png"></image>
          <text class="function-name">商品管理</text>
        </view>
        <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/orders')">
          <image class="function-icon" src="/static/images/tabbar/订单.png"></image>
          <text class="function-name">订单管理</text>
        </view>
        <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/groupbuy')">
          <image class="function-icon" src="/static/images/tabbar/团购.png"></image>
          <text class="function-name">团购活动</text>
        </view>
        <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/services')">
          <image class="function-icon" src="/static/images/tabbar/服务.png"></image>
          <text class="function-name">服务管理</text>
        </view>
        <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/reviews')">
          <image class="function-icon" src="/static/images/tabbar/评价.png"></image>
          <text class="function-name">评价管理</text>
        </view>
        <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/marketing')">
          <image class="function-icon" src="/static/images/tabbar/营销.png"></image>
          <text class="function-name">营销中心</text>
        </view>
        <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/finance')">
          <image class="function-icon" src="/static/images/tabbar/财务.png"></image>
          <text class="function-name">财务管理</text>
        </view>
        <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/staff')">
          <image class="function-icon" src="/static/images/tabbar/员工.png"></image>
          <text class="function-name">员工管理</text>
        </view>
        <view class="function-item" @click="navigateTo('/subPackages/merchant-plugin/pages/analysis')">
          <image class="function-icon" src="/static/images/tabbar/数据.png"></image>
          <text class="function-name">数据分析</text>
        </view>
      </view>
    </view>
    
    <!-- 快捷工具 -->
    <view class="tool-section">
      <view class="section-header">
        <text class="section-title">快捷工具</text>
      </view>
      
      <view class="tool-list">
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-plugin/pages/top-boost')">
          <view class="tool-left">
            <image class="tool-icon" src="/static/images/tabbar/置顶.png"></image>
            <text class="tool-name">商家置顶</text>
          </view>
          <view class="tool-right">
            <text class="tool-tag">热门</text>
            <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
          </view>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-plugin/pages/red-packet-manage')">
          <view class="tool-left">
            <image class="tool-icon" src="/static/images/tabbar/红包.png"></image>
            <text class="tool-name">红包管理</text>
          </view>
          <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-plugin/pages/coupon')">
          <view class="tool-left">
            <image class="tool-icon" src="/static/images/tabbar/优惠券.png"></image>
            <text class="tool-name">优惠券</text>
          </view>
          <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-plugin/pages/poster')">
          <view class="tool-left">
            <image class="tool-icon" src="/static/images/tabbar/海报.png"></image>
            <text class="tool-name">推广海报</text>
          </view>
          <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-plugin/pages/qrcode')">
          <view class="tool-left">
            <image class="tool-icon" src="/static/images/tabbar/二维码.png"></image>
            <text class="tool-name">店铺二维码</text>
          </view>
          <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
        </view>
        
        <view class="tool-item" @click="navigateTo('/subPackages/merchant-plugin/pages/settings')">
          <view class="tool-left">
            <image class="tool-icon" src="/static/images/tabbar/设置.png"></image>
            <text class="tool-name">店铺设置</text>
          </view>
          <image class="arrow-icon" src="/static/images/tabbar/arrow-up.png"></image>
        </view>
      </view>
    </view>
    
    <!-- 待处理事项 - 已移动到底部 -->
    <view class="pending-section">
      <view class="section-header">
        <text class="section-title">待处理事项</text>
        <text class="section-more" @click="navigateTo('/subPackages/merchant-plugin/pages/todo')">查看全部</text>
      </view>
      
      <view class="pending-list">
        <view v-if="pendingItems.length > 0">
          <view class="pending-item" v-for="(item, index) in pendingItems" :key="index" @click="handlePendingItem(item)">
            <view class="pending-left">
              <text class="pending-badge" :class="'badge-' + item.type">{{ item.count }}</text>
            </view>
            <view class="pending-center">
              <view class="pending-title">{{ item.title }}</view>
              <view class="pending-desc">{{ item.desc }}</view>
            </view>
            <view class="pending-right">
              <text class="action-btn">处理</text>
            </view>
          </view>
        </view>
        <view v-else class="empty-view">
          <image class="empty-icon" src="/static/images/empty-inbox.png" mode="aspectFit"></image>
          <view class="empty-text">暂无待处理事项</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { smartNavigate } from '@/utils/navigation.js';

export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      merchantInfo: {
        logo: '',
        name: '',
        verified: false,
        merchantId: ''
      },
      statsData: {
        views: 0,
        orders: 0,
        income: '0.00'
      },
      pendingItems: []
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 加载商家信息和数据
    this.loadMerchantInfo();
    this.loadStatsData();
    this.loadPendingItems();
  },
  
  onShareAppMessage() {
    // 自定义转发内容
    return {
      title: '商家后台 - 专业管理工具，助力商家成长',
      path: '/pages/my/merchant'
    }
  },
  
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 跳转到设置页面
    navigateToSettings() {
      smartNavigate("/subPackages/merchant-plugin/pages/settings').catch(err => {
        console.error('跳转到设置页面失败:', err);
      });
    },
    
    // 页面跳转
    navigateTo(url) {
      // 直接跳转，不再拦截未认证商家
      smartNavigate(url).catch(err => {
        console.error('页面跳转失败:', err);
      });
    },
    
    // 跳转到商家认证页面
    navigateToVerify() {
      smartNavigate("/subPackages/merchant-plugin/pages/verify');
    },
    
    // 跳转到商家资料编辑页面
    navigateToMerchantEdit() {
      smartNavigate("/subPackages/merchant-plugin/pages/profile');
    },
    
    // 处理待办事项
    handlePendingItem(item) {
      const urlMap = {
        'order': "/subPackages/merchant-plugin/pages/orders",
        'comment': "/subPackages/merchant-plugin/pages/reviews",
        'finance': "/subPackages/merchant-plugin/pages/finance",
        'approval': "/subPackages/merchant-plugin/pages/approval"
      };
      
      if (urlMap[item.type]) {
        this.navigateTo(urlMap[item.type]);
      }
    },
    
    // 加载商家信息
    loadMerchantInfo() {
      // 模拟API请求
      setTimeout(() => {
        // 随机商家信息，实际应该从API获取
        const verified = Math.random() > 0.3; // 70%概率已认证
        
        this.merchantInfo = {
          logo: verified ? '/static/images/shop-logo.png' : '',
          name: verified ? '磁州特色小吃店' : '',
          verified: verified,
          merchantId: 'M' + Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
        };
      }, 500);
    },
    
    // 加载数据统计
    loadStatsData() {
      // 模拟API请求
      setTimeout(() => {
        this.statsData = {
          views: Math.floor(Math.random() * 500),
          orders: Math.floor(Math.random() * 30),
          income: (Math.random() * 1000).toFixed(2)
        };
      }, 600);
    },
    
    // 加载待处理事项
    loadPendingItems() {
      // 模拟API请求
      setTimeout(() => {
        // 随机显示待处理事项，实际应该从API获取
        const havePendingItems = Math.random() > 0.3; // 70%概率有待处理事项
        
        if (havePendingItems) {
          this.pendingItems = [
            {
              type: 'order',
              count: Math.floor(Math.random() * 10) + 1,
              title: '待发货订单',
              desc: '有新的订单需要处理',
              url: '/subPackages/merchant-plugin/pages/orders'
            },
            {
              type: 'comment',
              count: Math.floor(Math.random() * 5) + 1,
              title: '新评价待回复',
              desc: '有顾客的评价需要回复',
              url: '/subPackages/merchant-plugin/pages/reviews'
            },
            {
              type: 'finance',
              count: Math.floor(Math.random() * 3) + 1,
              title: '待结算账单',
              desc: '有账单需要确认结算',
              url: '/subPackages/merchant-plugin/pages/finance'
            }
          ];
          
          // 随机显示0-3个待处理事项
          const count = Math.floor(Math.random() * 3) + 1;
          this.pendingItems = this.pendingItems.slice(0, count);
        } else {
          this.pendingItems = [];
        }
      }, 700);
    }
  }
}
</script>

<style>
.merchant-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.setting-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 商家信息卡片 */
.merchant-card {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
}

.merchant-header {
  padding: 30rpx;
  display: flex;
  align-items: center;
}

.merchant-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  background-color: #f5f7fa;
  margin-right: 30rpx;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.merchant-status {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #ff9800;
}

.verified .status-text {
  color: #07c160;
}

.merchant-action {
  margin-left: 30rpx;
}

.verify-btn {
  background-color: #ff9800;
  color: #fff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  height: 60rpx;
  line-height: 40rpx;
  margin: 0;
}

.edit-btn {
  background-color: #0066FF;
  color: #fff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  height: 60rpx;
  line-height: 40rpx;
  margin: 0;
}

/* 商家数据统计 */
.merchant-stats {
  display: flex;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 10rpx 0;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.stat-divider {
  width: 2rpx;
  background-color: #f0f0f0;
}

/* 功能区域 */
.function-section, .tool-section, .pending-section {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #0066FF;
}

/* 功能网格 */
.function-grid {
  display: flex;
  flex-wrap: wrap;
}

.function-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
  background-color: #f3f8ff;
  border-radius: 20rpx;
  padding: 16rpx;
}

.function-name {
  font-size: 26rpx;
  color: #333;
}

/* 工具列表 */
.tool-list {
  background-color: #fff;
}

.tool-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.tool-item:last-child {
  border-bottom: none;
}

.tool-left {
  display: flex;
  align-items: center;
}

.tool-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
  background-color: #f8f9fc;
  border-radius: 10rpx;
  padding: 10rpx;
}

.tool-name {
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  transform: rotate(90deg);
  opacity: 0.5;
}

/* 待处理事项 */
.pending-list {
  background-color: #fff;
}

.pending-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.pending-item:last-child {
  border-bottom: none;
}

.pending-left {
  margin-right: 20rpx;
}

.pending-badge {
  display: inline-block;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  text-align: center;
  color: #fff;
}

.badge-order {
  background-color: #ff5252;
}

.badge-comment {
  background-color: #ff9800;
}

.badge-finance {
  background-color: #0066FF;
}

.badge-approval {
  background-color: #9c27b0;
}

.pending-center {
  flex: 1;
}

.pending-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.pending-desc {
  font-size: 24rpx;
  color: #999;
}

.pending-right {
  margin-left: 20rpx;
}

.action-btn {
  display: inline-block;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #0066FF;
  border: 1rpx solid #0066FF;
  background-color: rgba(0, 102, 255, 0.05);
}

/* 空状态 */
.empty-view {
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  margin-top: 10px;
  font-size: 14px;
  color: #999;
}
</style> 