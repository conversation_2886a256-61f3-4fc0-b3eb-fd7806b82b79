"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_userProfile = require("../../../utils/userProfile.js");
const _sfc_main = {
  __name: "partner-qrcode",
  setup(__props) {
    const userInfo = common_vendor.reactive({
      nickname: "",
      avatar: "",
      level: 1
    });
    const qrCodeUrl = common_vendor.ref("/static/images/tabbar/qrcode-demo.png");
    const showShareModal = common_vendor.ref(false);
    const getUserInfo = () => {
      const localUserInfo = utils_userProfile.getLocalUserInfo();
      if (localUserInfo) {
        userInfo.nickname = localUserInfo.nickname;
        userInfo.avatar = localUserInfo.avatar;
        userInfo.level = localUserInfo.level || 1;
      }
    };
    const getLevelName = () => {
      const names = {
        1: "普通合伙人",
        2: "银牌合伙人",
        3: "金牌合伙人",
        4: "钻石合伙人"
      };
      return names[userInfo.level] || names[1];
    };
    const generateQrCode = () => {
      setTimeout(() => {
        qrCodeUrl.value = "/static/images/tabbar/qrcode-demo.png";
      }, 500);
    };
    const saveQrCode = () => {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: qrCodeUrl.value,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          if (err.errMsg.indexOf("auth deny") !== -1) {
            common_vendor.index.showModal({
              title: "提示",
              content: "需要您授权保存图片到相册",
              confirmText: "去授权",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    };
    const shareQrCode = () => {
      showShareModal.value = true;
    };
    const closeShareModal = () => {
      showShareModal.value = false;
    };
    const shareToWechat = () => {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 2,
        imageUrl: qrCodeUrl.value,
        title: `${userInfo.nickname}邀请您加入同城`,
        summary: "扫码注册，享受更多优惠",
        success: () => {
          closeShareModal();
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
    };
    const shareToMoments = () => {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneTimeline",
        type: 2,
        imageUrl: qrCodeUrl.value,
        title: `${userInfo.nickname}邀请您加入同城`,
        summary: "扫码注册，享受更多优惠",
        success: () => {
          closeShareModal();
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
    };
    const shareToQQ = () => {
      common_vendor.index.share({
        provider: "qq",
        type: 2,
        imageUrl: qrCodeUrl.value,
        title: `${userInfo.nickname}邀请您加入同城`,
        summary: "扫码注册，享受更多优惠",
        success: () => {
          closeShareModal();
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
    };
    const copyLink = () => {
      const inviteLink = `https://example.com/register?inviter=${userInfo.id}`;
      common_vendor.index.setClipboardData({
        data: inviteLink,
        success: () => {
          common_vendor.index.showToast({
            title: "链接已复制",
            icon: "success"
          });
          closeShareModal();
        }
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    common_vendor.onMounted(() => {
      getUserInfo();
      generateQrCode();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(goBack),
        c: userInfo.avatar || "/static/images/avatar/default.png",
        d: common_vendor.t(userInfo.nickname || "游客"),
        e: common_vendor.t(getLevelName()),
        f: common_assets._imports_1$23,
        g: qrCodeUrl.value,
        h: common_assets._imports_2$18,
        i: common_vendor.o(saveQrCode),
        j: common_assets._imports_0$15,
        k: common_vendor.o(shareQrCode),
        l: showShareModal.value
      }, showShareModal.value ? {
        m: common_vendor.o(closeShareModal),
        n: common_assets._imports_4$9,
        o: common_vendor.o(shareToWechat),
        p: common_assets._imports_5$9,
        q: common_vendor.o(shareToMoments),
        r: common_assets._imports_6$9,
        s: common_vendor.o(shareToQQ),
        t: common_assets._imports_7$6,
        v: common_vendor.o(copyLink),
        w: common_vendor.o(closeShareModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-31b6d405"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/partner/pages/partner-qrcode.js.map
