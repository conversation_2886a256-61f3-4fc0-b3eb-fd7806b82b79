@echo off
chcp 65001 >nul

echo.
echo ========================================
echo   磁州生活网本地服务一键安装
echo ========================================
echo.

echo 此脚本将帮助您安装以下服务：
echo   1. MySQL 8.0
echo   2. Redis 
echo   3. Node.js 18+
echo.

echo 注意：需要管理员权限和网络连接
echo.

set /p continue="是否继续安装？(y/N): "
if /i not "%continue%"=="y" (
    echo 已取消安装
    goto :end
)

echo.
echo ========================================
echo 第一步：检查现有安装
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if errorlevel 1 (
    echo X 需要管理员权限
    echo   请右键点击此脚本，选择"以管理员身份运行"
    goto :end
)

echo √ 管理员权限检查通过

REM 检查现有服务
echo.
echo 检查现有服务...

sc query mysql >nul 2>&1
if not errorlevel 1 (
    echo √ MySQL 已安装
    set "mysql_installed=true"
) else (
    sc query mysql80 >nul 2>&1
    if not errorlevel 1 (
        echo √ MySQL80 已安装
        set "mysql_installed=true"
    ) else (
        echo - MySQL 未安装
        set "mysql_installed=false"
    )
)

sc query redis >nul 2>&1
if not errorlevel 1 (
    echo √ Redis 已安装
    set "redis_installed=true"
) else (
    echo - Redis 未安装
    set "redis_installed=false"
)

where node >nul 2>&1
if not errorlevel 1 (
    echo √ Node.js 已安装
    node --version
    set "node_installed=true"
) else (
    echo - Node.js 未安装
    set "node_installed=false"
)

echo.
echo ========================================
echo 第二步：下载安装包
echo ========================================
echo.

if "%mysql_installed%"=="false" (
    echo 正在打开 MySQL 下载页面...
    start https://dev.mysql.com/downloads/mysql/
    echo.
    echo 请下载 MySQL Installer for Windows
    echo 建议选择 mysql-installer-web-community 版本
    echo.
    pause
)

if "%redis_installed%"=="false" (
    echo 正在打开 Redis 下载页面...
    start https://github.com/tporadowski/redis/releases
    echo.
    echo 请下载最新的 .msi 安装包
    echo 例如：Redis-x64-5.0.14.1.msi
    echo.
    pause
)

if "%node_installed%"=="false" (
    echo 正在打开 Node.js 下载页面...
    start https://nodejs.org/
    echo.
    echo 请下载 LTS 版本（推荐 18.x 或 20.x）
    echo 选择 Windows Installer (.msi) 64-bit
    echo.
    pause
)

echo.
echo ========================================
echo 第三步：安装配置指南
echo ========================================
echo.

if "%mysql_installed%"=="false" (
    echo MySQL 安装配置：
    echo   1. 运行下载的 mysql-installer-xxx.msi
    echo   2. 选择 "Developer Default" 安装类型
    echo   3. 设置 Root 密码为: cizhou123456
    echo   4. 选择 "Use Legacy Authentication Method"
    echo   5. 勾选 "Start the MySQL Server at System Startup"
    echo   6. 完成安装并测试连接
    echo.
)

if "%redis_installed%"=="false" (
    echo Redis 安装配置：
    echo   1. 运行下载的 Redis-xxx.msi
    echo   2. 勾选 "Add the Redis installation folder to the PATH"
    echo   3. 勾选 "Run the Redis Server"
    echo   4. 设置端口为 6379
    echo   5. 设置密码为: cizhou123456
    echo   6. 完成安装
    echo.
)

if "%node_installed%"=="false" (
    echo Node.js 安装配置：
    echo   1. 运行下载的 node-xxx.msi
    echo   2. 勾选 "Add to PATH" 选项
    echo   3. 勾选 "Install additional tools" (可选)
    echo   4. 完成安装
    echo   5. 重启命令提示符验证安装
    echo.
)

echo ========================================
echo 第四步：验证安装
echo ========================================
echo.

echo 安装完成后，请运行以下命令验证：
echo.

echo 验证 MySQL:
echo   mysql -uroot -pcizhou123456
echo.

echo 验证 Redis:
echo   redis-cli -a cizhou123456
echo   ping
echo.

echo 验证 Node.js:
echo   node --version
echo   npm --version
echo.

echo ========================================
echo 完成安装后的下一步
echo ========================================
echo.

echo 1. 重启命令提示符
echo 2. 运行: start-without-docker.bat
echo 3. 如果一切正常，系统将自动初始化数据库
echo 4. 然后可以启动应用服务
echo.

:end
echo ========================================
echo 安装向导完成
echo ========================================
pause
