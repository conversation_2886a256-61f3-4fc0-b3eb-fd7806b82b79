<template>
  <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
    <view class="header-content">
      <view class="left-action" @click="handleBack">
        <image src="/static/images/tabbar/最新返回键.png" class="action-icon back-icon"></image>
      </view>
      <view class="title-area">
        <text class="page-title">{{ title }}</text>
      </view>
      <view class="right-action" v-if="rightText || rightIcon" @click="handleRightAction">
        <text v-if="rightText" class="right-text">{{ rightText }}</text>
        <image v-if="rightIcon" :src="rightIcon" class="action-icon"></image>
      </view>
      <view v-else class="right-action">
        <!-- 右侧占位 -->
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 组件属性
const props = defineProps({
  title: {
    type: String,
    default: '页面标题'
  },
  rightText: {
    type: String,
    default: ''
  },
  rightIcon: {
    type: String,
    default: ''
  },
  // 是否显示返回按钮
  showBack: {
    type: Boolean,
    default: true
  }
});

// 事件
const emit = defineEmits(['back', 'rightAction']);

// 状态栏高度
const statusBarHeight = ref(20);

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 20;
});

// 返回事件处理
const handleBack = () => {
  if (props.showBack) {
    emit('back');
    // 默认行为是返回上一页
    uni.navigateBack();
  }
};

// 右侧操作事件处理
const handleRightAction = () => {
  emit('rightAction');
};
</script>

<style scoped>
/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
}

.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}

.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.right-text {
  color: #FFFFFF;
  font-size: 14px;
}
</style> 