package com.cizhou.merchant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 店铺实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("shops")
public class Shop implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商家ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 店铺简介
     */
    @TableField("shop_desc")
    private String shopDesc;

    /**
     * 店铺LOGO
     */
    @TableField("shop_logo")
    private String shopLogo;

    /**
     * 店铺封面
     */
    @TableField("shop_cover")
    private String shopCover;

    /**
     * 店铺轮播图
     */
    @TableField("shop_banners")
    private String shopBanners;

    /**
     * 店铺分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 联系电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 区县
     */
    @TableField("district")
    private String district;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 经度
     */
    @TableField("longitude")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

    /**
     * 营业开始时间
     */
    @TableField("open_time")
    private LocalTime openTime;

    /**
     * 营业结束时间
     */
    @TableField("close_time")
    private LocalTime closeTime;

    /**
     * 营业状态：0-休息中，1-营业中
     */
    @TableField("business_status")
    private Integer businessStatus;

    /**
     * 店铺状态：0-禁用，1-正常，2-冻结
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否推荐：0-否，1-是
     */
    @TableField("is_recommend")
    private Integer isRecommend;

    /**
     * 排序权重
     */
    @TableField("sort_weight")
    private Integer sortWeight;

    /**
     * 月销量
     */
    @TableField("month_sales")
    private Integer monthSales;

    /**
     * 总销量
     */
    @TableField("total_sales")
    private Integer totalSales;

    /**
     * 好评率
     */
    @TableField("good_rate")
    private BigDecimal goodRate;

    /**
     * 服务评分
     */
    @TableField("service_score")
    private BigDecimal serviceScore;

    /**
     * 浏览量
     */
    @TableField("view_count")
    private Integer viewCount;

    /**
     * 收藏量
     */
    @TableField("favorite_count")
    private Integer favoriteCount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
