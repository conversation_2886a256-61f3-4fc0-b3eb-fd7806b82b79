'use strict';var _fs=require('fs');var _fs2=_interopRequireDefault(_fs);var _=require('./');var _2=_interopRequireDefault(_);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}require.extensions['.json5']=function(module,filename){var content=_fs2.default.readFileSync(filename,'utf8');try{module.exports=_2.default.parse(content)}catch(err){err.message=filename+': '+err.message;throw err}};