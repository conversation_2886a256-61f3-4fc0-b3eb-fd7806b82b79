<template>
  <view class="payment-result-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goHome">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
        </view>
        <view class="navbar-title">支付结果</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 支付结果状态 -->
      <view class="result-status" :class="{ success: isSuccess, failed: !isSuccess }">
        <view class="status-icon">
          <svg v-if="isSuccess" xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
        </view>
        
        <text class="status-title">{{ statusTitle }}</text>
        <text class="status-desc">{{ statusDesc }}</text>
        
        <view class="payment-amount" v-if="isSuccess">
          <text class="amount-label">支付金额</text>
          <text class="amount-value">¥{{ paymentAmount }}</text>
        </view>
      </view>

      <!-- 订单信息 -->
      <view class="order-info">
        <view class="section-header">
          <text class="section-title">订单信息</text>
        </view>
        
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">订单号</text>
            <view class="info-value">
              <text class="value-text">{{ orderNo }}</text>
              <view class="copy-btn" @click="copyOrderNo">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </view>
            </view>
          </view>
          
          <view class="info-item" v-if="isSuccess">
            <text class="info-label">支付时间</text>
            <text class="info-value">{{ paymentTime }}</text>
          </view>
          
          <view class="info-item" v-if="isSuccess">
            <text class="info-label">支付方式</text>
            <text class="info-value">{{ paymentMethod }}</text>
          </view>
          
          <view class="info-item" v-if="!isSuccess">
            <text class="info-label">失败原因</text>
            <text class="info-value error">{{ failureReason }}</text>
          </view>
        </view>
      </view>

      <!-- 操作建议 -->
      <view class="action-suggestions" v-if="!isSuccess">
        <view class="section-header">
          <text class="section-title">解决方案</text>
        </view>
        
        <view class="suggestions-list">
          <view class="suggestion-item" v-for="(suggestion, index) in suggestions" :key="index">
            <view class="suggestion-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 11H1v3h8v3l3-4-3-4v2z"></path>
                <path d="M22 12h-7"></path>
              </svg>
            </view>
            <text class="suggestion-text">{{ suggestion }}</text>
          </view>
        </view>
      </view>

      <!-- 推荐商品 -->
      <view class="recommended-products" v-if="isSuccess">
        <view class="section-header">
          <text class="section-title">为您推荐</text>
          <text class="section-desc">猜你喜欢</text>
        </view>
        
        <view class="products-grid">
          <view 
            class="product-item" 
            v-for="(product, index) in recommendedProducts" 
            :key="product.id"
            @click="navigateToProduct(product.id)"
          >
            <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <text class="product-name">{{ product.name }}</text>
            <text class="product-price">¥{{ product.price }}</text>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <view class="action-btn secondary" @click="viewOrder">
        <text class="btn-text">查看订单</text>
      </view>
      
      <view class="action-btn primary" @click="handlePrimaryAction">
        <text class="btn-text">{{ primaryActionText }}</text>
      </view>
    </view>

    <!-- 支付成功动画 -->
    <view class="success-animation" v-if="showAnimation && isSuccess">
      <view class="animation-container">
        <view class="success-circle">
          <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </view>
        <text class="animation-text">支付成功！</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const isSuccess = ref(true)
const paymentAmount = ref('299.00')
const orderNo = ref('ORDER202401200001')
const paymentTime = ref('2024-01-20 14:35:28')
const paymentMethod = ref('微信支付')
const failureReason = ref('余额不足')
const showAnimation = ref(false)

// 解决方案建议
const suggestions = ref([
  '检查账户余额是否充足',
  '确认银行卡是否正常',
  '尝试更换支付方式',
  '联系客服获取帮助'
])

// 推荐商品
const recommendedProducts = ref([
  {
    id: 1,
    name: '热销商品A',
    image: '/static/images/products/recommend1.jpg',
    price: 199.00
  },
  {
    id: 2,
    name: '精选商品B',
    image: '/static/images/products/recommend2.jpg',
    price: 259.00
  },
  {
    id: 3,
    name: '优质商品C',
    image: '/static/images/products/recommend3.jpg',
    price: 179.00
  },
  {
    id: 4,
    name: '推荐商品D',
    image: '/static/images/products/recommend4.jpg',
    price: 299.00
  }
])

// 计算属性
const statusTitle = computed(() => {
  return isSuccess.value ? '支付成功' : '支付失败'
})

const statusDesc = computed(() => {
  return isSuccess.value ? '您的订单已支付成功，我们将尽快为您处理' : '很抱歉，您的支付未能成功完成'
})

const primaryActionText = computed(() => {
  return isSuccess.value ? '继续购物' : '重新支付'
})

// 页面加载
onMounted(() => {
  console.log('支付结果页面加载')
  
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options
  
  if (options.success !== undefined) {
    isSuccess.value = options.success === 'true'
  }
  if (options.amount) {
    paymentAmount.value = options.amount
  }
  if (options.orderNo) {
    orderNo.value = options.orderNo
  }
  
  // 如果支付成功，显示动画
  if (isSuccess.value) {
    setTimeout(() => {
      showAnimation.value = true
      setTimeout(() => {
        showAnimation.value = false
      }, 2000)
    }, 500)
  }
})

// 方法
function goHome() {
  uni.switchTab({
    url: '/pages/index/index'
  })
}

function copyOrderNo() {
  uni.setClipboardData({
    data: orderNo.value,
    success: () => {
      uni.showToast({
        title: '订单号已复制',
        icon: 'success'
      })
    }
  })
}

function viewOrder() {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/orders/detail/index?orderNo=${orderNo.value}`
  })
}

function handlePrimaryAction() {
  if (isSuccess.value) {
    // 继续购物
    uni.switchTab({
      url: '/pages/index/index'
    })
  } else {
    // 重新支付
    uni.navigateBack()
  }
}

function navigateToProduct(productId) {
  uni.navigateTo({
    url: `/subPackages/activity-showcase/pages/products/detail/index?id=${productId}`
  })
}
</script>

<style scoped>
/* 支付结果样式开始 */
.payment-result-container {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 80px;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
  border-bottom: 1px solid #f0f0f0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.back-btn svg {
  color: #333;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
}

/* 支付结果状态样式 */
.result-status {
  padding: 40px 20px;
  text-align: center;
  background: white;
  margin: 20px 16px;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.result-status.success {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  color: white;
}

.result-status.failed {
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;
}

.status-icon {
  margin-bottom: 16px;
}

.status-icon svg {
  color: currentColor;
}

.status-title {
  display: block;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.status-desc {
  display: block;
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 20px;
  line-height: 1.5;
}

.payment-amount {
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.amount-label {
  display: block;
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.amount-value {
  font-size: 32px;
  font-weight: 700;
}

/* 订单信息样式 */
.order-info {
  background: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-desc {
  font-size: 12px;
  color: #666;
}

.info-list {
  padding: 0 20px 16px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f8f9fa;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.value-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.value-text.error {
  color: #F44336;
}

.copy-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #f5f5f5;
}

.copy-btn svg {
  color: #666;
}

/* 操作建议样式 */
.action-suggestions {
  background: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  overflow: hidden;
}

.suggestions-list {
  padding: 0 20px 16px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFF3E0;
  border-radius: 16px;
}

.suggestion-icon svg {
  color: #FF9800;
}

.suggestion-text {
  flex: 1;
  font-size: 14px;
  color: #333;
}

/* 推荐商品样式 */
.recommended-products {
  background: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  overflow: hidden;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 0 20px 16px;
}

.product-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-bottom: 8px;
}

.product-name {
  font-size: 12px;
  color: #333;
  text-align: center;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.product-price {
  font-size: 14px;
  color: #E91E63;
  font-weight: 600;
}

/* 底部操作按钮样式 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  z-index: 1000;
}

.action-btn {
  flex: 1;
  padding: 12px;
  border-radius: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.action-btn.primary {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
}

.btn-text {
  font-size: 16px;
  font-weight: 500;
}

.action-btn.secondary .btn-text {
  color: #333;
}

.action-btn.primary .btn-text {
  color: white;
}

/* 支付成功动画样式 */
.success-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  animation: fadeIn 0.3s ease;
}

.animation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.success-circle {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4CAF50;
  border-radius: 60px;
  animation: scaleIn 0.5s ease;
}

.success-circle svg {
  color: white;
  animation: checkIn 0.8s ease 0.2s both;
}

.animation-text {
  font-size: 20px;
  color: white;
  font-weight: 600;
  animation: slideUp 0.5s ease 0.5s both;
  opacity: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

@keyframes checkIn {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 支付结果样式结束 */
</style>
