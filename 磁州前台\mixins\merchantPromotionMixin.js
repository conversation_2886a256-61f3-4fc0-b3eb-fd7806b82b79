/**
 * 商家推广混入
 * 为商家详情页提供推广能力
 */
import basePromotionMixin from './basePromotionMixin';

export default {
  mixins: [basePromotionMixin],

  data() {
    return {
      // 设置页面类型为商家
      pageType: 'merchant'
    };
  },

  methods: {
    /**
     * 重写：判断当前用户是否是内容所有者
     */
    isContentOwner() {
      // 获取当前用户ID
      const currentUserId = this.$store?.state?.user?.userId || '';
      // 获取商家ID
      const merchantId = this.shopData?.merchantId || this.shopData?.ownerId || '';

      // 判断当前用户是否是商家所有者
      return currentUserId && merchantId && currentUserId === merchantId;
    },

    /**
     * 重写：判断当前内容是否支持佣金
     */
    isCommissionContent() {
      // 商家通常支持佣金
      const canDistribute = this.shopData?.canDistribute !== false; // 默认为true
      
      return canDistribute;
    },

    /**
     * 重写：生成推广数据
     */
    generatePromotionData() {
      // 获取商家数据
      const shop = this.shopData || {};
      
      // 构建推广数据
      this.promotionData = {
        id: shop.id || '',
        title: shop.shopName || '商家详情',
        image: shop.logo || shop.images?.[0] || '/static/images/tabbar/商家入驻.png',
        category: shop.category || '',
        address: shop.address || '',
        contactPhone: shop.contactPhone || '',
        description: shop.description ? (shop.description.length > 50 ? shop.description.substring(0, 50) + '...' : shop.description) : '',
        // 如果有更多商家特定字段，可以在这里添加
      };
    },

    /**
     * 显示商家推广浮层
     */
    showMerchantPromotion() {
      // 如果没有推广权限，显示提示
      if (!this.hasPromotionPermission) {
        uni.showToast({
          title: '暂无推广权限',
          icon: 'none'
        });
        return;
      }

      // 打开推广工具
      this.openPromotionTools();
    }
  }
}; 