{"name": "uniq", "version": "1.0.1", "description": "Removes duplicates from a sorted array in place", "main": "uniq.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tape": "^2.12.3"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/uniq.git"}, "keywords": ["array", "duplicate", "unique", "uniq", "remove", "sort", "in", "place", "no", "copy"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "readmeFilename": "README.md", "gitHead": "e9828cfcb97e25a351f95b39fdf3c31876ff3985"}