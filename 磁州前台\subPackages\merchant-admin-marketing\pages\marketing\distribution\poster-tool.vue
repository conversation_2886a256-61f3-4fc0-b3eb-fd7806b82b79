<template>
  <view class="poster-tool-container">
    <view class="tool-header">
      <view class="title">推广海报</view>
      <view class="subtitle">选择海报模板，一键生成精美推广海报</view>
    </view>
    
    <!-- 模板选择区域 -->
    <view class="template-selection">
      <scroll-view scroll-x class="template-list">
        <view 
          class="template-item" 
          v-for="(template, index) in posterTemplates" 
          :key="index"
          :class="{ 'active': selectedTemplateIndex === index }"
          @click="selectTemplate(index)"
        >
          <image class="template-image" :src="template.thumbnail" mode="aspectFill"></image>
        </view>
      </scroll-view>
    </view>
    
    <!-- 海报预览区域 -->
    <view class="poster-preview">
      <view class="preview-title">预览效果</view>
      <view class="poster-canvas-wrapper">
        <image v-if="posterImageUrl" :src="posterImageUrl" class="poster-image" mode="widthFix"></image>
        <canvas v-else canvas-id="posterCanvas" class="poster-canvas"></canvas>
      </view>
    </view>
    
    <!-- 分享按钮 -->
    <view class="action-buttons">
      <button class="action-btn save" @click="savePoster">保存到相册</button>
      <button class="action-btn share" open-type="share">分享给好友</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 传入参数
      itemId: '',
      itemTitle: '',
      itemImage: '',
      promotionType: 'default',
      extraParams: {},
      
      // 海报相关
      posterTemplates: [
        { id: 1, name: '模板1', thumbnail: '/static/images/poster-template-1.jpg' },
        { id: 2, name: '模板2', thumbnail: '/static/images/poster-template-2.jpg' },
        { id: 3, name: '模板3', thumbnail: '/static/images/poster-template-3.jpg' }
      ],
      selectedTemplateIndex: 0,
      posterImageUrl: '', // 生成的海报图片URL
      promotionContent: {} // 推广内容
    };
  },
  
  onLoad(options) {
    // 获取推广类型和基础信息
    this.promotionType = options.type || 'default';
    this.itemId = options.id || '';
    this.itemTitle = options.title ? decodeURIComponent(options.title) : '';
    this.itemImage = options.image ? decodeURIComponent(options.image) : '';
    
    // 存储特定类型的额外参数
    this.extraParams = {};
    Object.keys(options).forEach(key => {
      if (!['type', 'id', 'title', 'image'].includes(key)) {
        this.extraParams[key] = decodeURIComponent(options[key] || '');
      }
    });
    
    // 根据不同类型加载适合的模板
    this.loadTemplatesByType();
    
    // 根据参数生成推广内容
    this.generatePromotionContent();
    
    // 生成海报
    this.$nextTick(() => {
      this.generatePoster();
    });
  },
  
  methods: {
    // 根据类型加载不同模板
    loadTemplatesByType() {
      // 这里可以根据promotionType加载不同模板
      // 暂时使用默认模板
    },
    
    // 生成推广内容
    generatePromotionContent() {
      // 基础内容
      const content = {
        title: this.itemTitle,
        image: this.itemImage,
        qrcode: this.generateQrcode()
      };
      
      // 根据不同类型添加特定内容
      switch(this.promotionType) {
        case 'carpool':
          content.subtitle = `${this.extraParams.departure} → ${this.extraParams.destination}`;
          content.details = [
            `出发时间：${this.extraParams.departureTime}`,
            `车费：¥${this.extraParams.price}`,
            `剩余座位：${this.extraParams.seats}个`
          ];
          break;
          
        case 'secondhand':
        case 'product':
          content.subtitle = `${this.extraParams.category || ''}`;
          content.details = [
            `价格：¥${this.extraParams.price}`,
            `原价：¥${this.extraParams.originalPrice || this.extraParams.price}`
          ];
          if (this.extraParams.condition) {
            content.details.push(`成色：${this.extraParams.condition}`);
          }
          break;
        
        case 'house':
          content.subtitle = this.extraParams.location || '';
          content.details = [
            `${this.extraParams.roomType || ''}`,
            `面积：${this.extraParams.area || 0}㎡`,
            `价格：¥${this.extraParams.price}${this.extraParams.rentType || ''}`,
          ];
          break;
          
        case 'content':
          content.subtitle = this.extraParams.author ? `作者：${this.extraParams.author}` : '';
          content.details = [
            this.extraParams.summary || ''
          ];
          break;
      }
      
      this.promotionContent = content;
    },
    
    // 生成带参数的二维码
    generateQrcode() {
      // 这里应该调用后端API生成带参数的二维码
      // 暂时返回示例图片
      return '/static/images/qrcode-example.png';
    },
    
    // 选择模板
    selectTemplate(index) {
      this.selectedTemplateIndex = index;
      // 重新生成海报
      this.generatePoster();
    },
    
    // 生成海报
    async generatePoster() {
      try {
        // 这里应该实现海报绘制逻辑
        // 可以使用canvas绘制，或者调用后端API生成
        // 暂时使用模拟数据
        setTimeout(() => {
          // 模拟生成完成
          this.posterImageUrl = '/static/images/poster-example.jpg';
          
          // 记录生成事件
          this.logPosterGeneration();
        }, 500);
      } catch (e) {
        console.error('生成海报失败', e);
        uni.showToast({
          title: '生成海报失败',
          icon: 'none'
        });
      }
    },
    
    // 保存海报到相册
    savePoster() {
      if (!this.posterImageUrl) {
        uni.showToast({
          title: '海报生成中，请稍后再试',
          icon: 'none'
        });
        return;
      }
      
      uni.saveImageToPhotosAlbum({
        filePath: this.posterImageUrl,
        success: () => {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
          
          // 记录保存事件
          this.logPosterSaving();
        },
        fail: () => {
          uni.showToast({
            title: '保存失败，请检查权限设置',
            icon: 'none'
          });
        }
      });
    },
    
    // 记录海报生成事件
    logPosterGeneration() {
      // 这里可以调用API记录生成事件
      console.log('记录海报生成', this.promotionType, this.itemId);
    },
    
    // 记录海报保存事件
    logPosterSaving() {
      // 这里可以调用API记录保存事件
      console.log('记录海报保存', this.promotionType, this.itemId);
    }
  },
  
  // 分享功能
  onShareAppMessage() {
    return {
      title: this.itemTitle,
      path: `/pages/index/index?type=${this.promotionType}&id=${this.itemId}&source=poster`,
      imageUrl: this.posterImageUrl || this.itemImage
    };
  }
};
</script>

<style lang="scss" scoped>
.poster-tool-container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.tool-header {
  text-align: center;
  padding-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 26rpx;
  color: #999;
  margin-top: 10rpx;
}

.template-selection {
  margin: 30rpx 0;
}

.template-list {
  white-space: nowrap;
}

.template-item {
  display: inline-block;
  width: 160rpx;
  height: 240rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  
  &.active {
    border: 4rpx solid #6B0FBE;
  }
}

.template-image {
  width: 100%;
  height: 100%;
}

.poster-preview {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.preview-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.poster-canvas-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
}

.poster-canvas, .poster-image {
  width: 600rpx;
  height: 900rpx;
}

.action-buttons {
  margin-top: 50rpx;
  display: flex;
  justify-content: space-between;
}

.action-btn {
  width: 45%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
}

.save {
  background-color: #f5f5f5;
  color: #333;
}

.share {
  background-color: #6B0FBE;
  color: #fff;
}
</style> 