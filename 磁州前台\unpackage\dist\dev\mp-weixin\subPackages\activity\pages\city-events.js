"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 20,
      refreshing: false,
      currentTab: 0,
      heroImages: [
        {
          image: "/static/images/cizhou/hero1.jpg",
          title: "磁州印象",
          desc: "千年窑火不熄，匠心守艺传承"
        },
        {
          image: "/static/images/cizhou/hero2.jpg",
          title: "磁州风光",
          desc: "山水相依，四季如画"
        },
        {
          image: "/static/images/cizhou/hero3.jpg",
          title: "人文磁州",
          desc: "历史悠久，文化灿烂"
        }
      ],
      tabs: [
        { name: "景点", icon: "attraction" },
        { name: "美食", icon: "food" },
        { name: "文化", icon: "culture" },
        { name: "活动", icon: "event" },
        { name: "住宿", icon: "hotel" },
        { name: "购物", icon: "shopping" }
      ],
      features: [
        {
          id: 1,
          name: "磁州窑瓷器",
          desc: "北方民窑之冠，千年窑火不熄",
          image: "/static/images/cizhou/feature1.jpg",
          tag: "非遗"
        },
        {
          id: 2,
          name: "民间剪纸",
          desc: "巧手剪就锦绣图，浓郁乡土风情",
          image: "/static/images/cizhou/feature2.jpg",
          tag: "传统"
        },
        {
          id: 3,
          name: "地方戏曲",
          desc: "磁州梆子，古老的民间艺术表演",
          image: "/static/images/cizhou/feature3.jpg",
          tag: "艺术"
        },
        {
          id: 4,
          name: "特色美食",
          desc: "老磁州炖菜，地道农家风味",
          image: "/static/images/cizhou/feature4.jpg",
          tag: "美食"
        }
      ],
      attractions: [
        {
          id: 1,
          name: "磁州窑博物馆",
          rating: 4.8,
          address: "磁县城南2公里处",
          image: "/static/images/cizhou/attraction1.jpg",
          tags: ["历史", "博物馆", "陶瓷"]
        },
        {
          id: 2,
          name: "朱山风景区",
          rating: 4.6,
          address: "磁县东北15公里",
          image: "/static/images/cizhou/attraction2.jpg",
          tags: ["自然", "山水", "远足"]
        },
        {
          id: 3,
          name: "古窑址公园",
          rating: 4.5,
          address: "磁县陶瓷产业园",
          image: "/static/images/cizhou/attraction3.jpg",
          tags: ["遗址", "公园", "文化"]
        },
        {
          id: 4,
          name: "古城墙遗址",
          rating: 4.3,
          address: "磁县老城区",
          image: "/static/images/cizhou/attraction4.jpg",
          tags: ["古迹", "历史", "文化"]
        }
      ],
      events: [
        {
          id: 1,
          title: "磁州窑文化节",
          month: "6月",
          day: "15",
          location: "磁州窑博物馆",
          status: "upcoming",
          actionText: "预约"
        },
        {
          id: 2,
          title: "民俗文化展演",
          month: "6月",
          day: "20",
          location: "文化广场",
          status: "upcoming",
          actionText: "预约"
        },
        {
          id: 3,
          title: "磁州好礼品鉴会",
          month: "6月",
          day: "10",
          location: "城市会展中心",
          status: "ongoing",
          actionText: "参与"
        },
        {
          id: 4,
          title: "传统工艺体验日",
          month: "6月",
          day: "5",
          location: "磁州文化馆",
          status: "ended",
          actionText: "回顾"
        }
      ],
      culturalItems: [
        {
          name: "磁州窑文化",
          desc: "了解磁州窑的历史和文化",
          icon: "/static/images/cizhou/culture1.jpg",
          bgColor: "#FFD700"
        },
        {
          name: "磁州剪纸艺术",
          desc: "探索磁州剪纸的独特魅力",
          icon: "/static/images/cizhou/culture2.jpg",
          bgColor: "#FF69B4"
        },
        {
          name: "磁州梆子戏曲",
          desc: "了解磁州梆子戏曲的历史和表演",
          icon: "/static/images/cizhou/culture3.jpg",
          bgColor: "#007AFF"
        },
        {
          name: "磁州美食",
          desc: "品尝磁州的传统美食",
          icon: "/static/images/cizhou/culture4.jpg",
          bgColor: "#FF4500"
        }
      ],
      guides: [
        {
          title: "磁州一日游攻略",
          image: "/static/images/cizhou/guide1.jpg",
          authorName: "张三",
          authorAvatar: "/static/images/cizhou/author1.jpg",
          views: "1,234",
          likes: "234"
        },
        {
          title: "磁州文化深度游",
          image: "/static/images/cizhou/guide2.jpg",
          authorName: "李四",
          authorAvatar: "/static/images/cizhou/author2.jpg",
          views: "890",
          likes: "123"
        },
        {
          title: "磁州美食探索",
          image: "/static/images/cizhou/guide3.jpg",
          authorName: "王五",
          authorAvatar: "/static/images/cizhou/author3.jpg",
          views: "765",
          likes: "98"
        },
        {
          title: "磁州住宿体验",
          image: "/static/images/cizhou/guide4.jpg",
          authorName: "赵六",
          authorAvatar: "/static/images/cizhou/author4.jpg",
          views: "654",
          likes: "76"
        }
      ]
    };
  },
  onLoad() {
    const sysInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    share() {
      common_vendor.index.showShareMenu({
        withShareTicket: true,
        menus: ["shareAppMessage", "shareTimeline"]
      });
    },
    onRefresh() {
      this.refreshing = true;
      setTimeout(() => {
        this.refreshing = false;
      }, 1200);
    },
    switchTab(index) {
      this.currentTab = index;
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    viewFeature(feature) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity/pages/feature-detail?id=${feature.id}`
      });
    },
    viewAttraction(attraction) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity/pages/attraction-detail?id=${attraction.id}`
      });
    },
    viewEvent(event) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity/pages/event-detail?id=${event.id}`
      });
    },
    viewCulture(item) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity/pages/culture-detail?name=${item.name}`
      });
    },
    viewGuide(guide) {
      common_vendor.index.navigateTo({
        url: `/subPackages/activity/pages/guide-detail?title=${guide.title}`
      });
    },
    getStatusText(status) {
      switch (status) {
        case "ongoing":
          return "进行中";
        case "upcoming":
          return "即将开始";
        case "ended":
          return "已结束";
        default:
          return "";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$5,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_assets._imports_2$1,
    d: common_vendor.o((...args) => $options.share && $options.share(...args)),
    e: $data.statusBarHeight + "px",
    f: common_vendor.f($data.heroImages, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.desc),
        d: index
      };
    }),
    g: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    h: common_vendor.o(($event) => $options.navigateTo("/subPackages/activity/pages/features")),
    i: common_vendor.f($data.features, (feature, index, i0) => {
      return common_vendor.e({
        a: feature.image,
        b: common_vendor.t(feature.name),
        c: common_vendor.t(feature.desc),
        d: feature.tag
      }, feature.tag ? {
        e: common_vendor.t(feature.tag)
      } : {}, {
        f: index,
        g: common_vendor.o(($event) => $options.viewFeature(feature), index)
      });
    }),
    j: common_vendor.o(($event) => $options.navigateTo("/subPackages/activity/pages/attractions")),
    k: common_vendor.f($data.attractions, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.rating),
        d: common_vendor.t(item.address),
        e: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        f: index,
        g: common_vendor.o(($event) => $options.viewAttraction(item), index)
      };
    }),
    l: common_assets._imports_3$20,
    m: common_vendor.o(($event) => $options.navigateTo("/subPackages/activity/pages/events")),
    n: common_vendor.f($data.events, (event, index, i0) => {
      return {
        a: common_vendor.t(event.month),
        b: common_vendor.t(event.day),
        c: common_vendor.t(event.title),
        d: common_vendor.t(event.location),
        e: common_vendor.t($options.getStatusText(event.status)),
        f: event.status === "ongoing" ? 1 : "",
        g: event.status === "upcoming" ? 1 : "",
        h: event.status === "ended" ? 1 : "",
        i: common_vendor.t(event.actionText),
        j: index,
        k: common_vendor.o(($event) => $options.viewEvent(event), index)
      };
    }),
    o: common_vendor.o(($event) => $options.navigateTo("/subPackages/activity/pages/culture")),
    p: common_vendor.f($data.culturalItems, (item, index, i0) => {
      return {
        a: item.icon,
        b: item.bgColor,
        c: common_vendor.t(item.name),
        d: common_vendor.t(item.desc),
        e: index,
        f: common_vendor.o(($event) => $options.viewCulture(item), index)
      };
    }),
    q: common_vendor.o(($event) => $options.navigateTo("/subPackages/activity/pages/guides")),
    r: common_vendor.f($data.guides, (guide, index, i0) => {
      return {
        a: guide.image,
        b: common_vendor.t(guide.title),
        c: guide.authorAvatar,
        d: common_vendor.t(guide.authorName),
        e: common_vendor.t(guide.views),
        f: common_vendor.t(guide.likes),
        g: index,
        h: common_vendor.o(($event) => $options.viewGuide(guide), index)
      };
    }),
    s: common_assets._imports_3$21,
    t: common_assets._imports_4$16,
    v: $data.refreshing,
    w: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    x: $data.statusBarHeight + 44 + "px"
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/activity/pages/city-events.js.map
