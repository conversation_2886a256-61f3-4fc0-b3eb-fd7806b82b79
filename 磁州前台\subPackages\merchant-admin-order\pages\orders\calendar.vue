<template>
  <view class="calendar-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">预约订单日历</text>
      <view class="navbar-right">
        <view class="filter-icon" @click="showFilterOptions">⚙️</view>
      </view>
    </view>
    
    <!-- 日历控件 -->
    <view class="calendar-wrapper">
      <view class="calendar-header">
        <view class="month-selector">
          <view class="prev-month" @click="prevMonth">
            <text class="arrow">◀</text>
          </view>
          <view class="current-month">{{currentYear}}年{{currentMonth + 1}}月</view>
          <view class="next-month" @click="nextMonth">
            <text class="arrow">▶</text>
          </view>
        </view>
        <view class="view-switch">
          <view 
            class="view-option" 
            :class="{'active': currentView === 'month'}"
            @click="switchView('month')">月</view>
          <view 
            class="view-option" 
            :class="{'active': currentView === 'week'}"
            @click="switchView('week')">周</view>
          <view 
            class="view-option" 
            :class="{'active': currentView === 'day'}"
            @click="switchView('day')">日</view>
        </view>
      </view>
      
      <!-- 星期标题 -->
      <view class="weekdays">
        <view class="weekday" v-for="(day, index) in weekdays" :key="index">{{day}}</view>
      </view>
      
      <!-- 月视图 -->
      <view v-if="currentView === 'month'" class="days-grid">
        <view 
          v-for="(day, index) in daysInMonth" 
          :key="index"
          :class="['day-cell', {
            'other-month': !day.currentMonth,
            'today': day.isToday,
            'has-orders': day.orderCount > 0
          }]"
          @click="selectDay(day)">
          <text class="day-number">{{day.day}}</text>
          <view v-if="day.orderCount > 0" class="order-indicator">
            <text class="order-count">{{day.orderCount}}</text>
          </view>
        </view>
      </view>
      
      <!-- 周视图 -->
      <view v-else-if="currentView === 'week'" class="week-view">
        <view 
          v-for="(day, index) in daysInWeek" 
          :key="index"
          :class="['week-day', {
            'today': day.isToday,
            'has-orders': day.orderCount > 0
          }]"
          @click="selectDay(day)">
          <view class="week-day-header">
            <text class="week-day-name">{{weekdays[index]}}</text>
            <text class="week-day-number">{{day.day}}</text>
          </view>
          <view class="week-day-content">
            <view 
              v-for="(timeSlot, slotIndex) in day.timeSlots" 
              :key="slotIndex"
              :class="['time-slot', {
                'has-appointment': timeSlot.hasAppointment
              }]"
              :style="{top: (timeSlot.hour - 8) * 60 + timeSlot.minute + 'px', height: timeSlot.duration + 'px'}"
              @click.stop="viewAppointment(timeSlot)">
              <text v-if="timeSlot.hasAppointment" class="slot-time">{{timeSlot.startTime}}</text>
              <text v-if="timeSlot.hasAppointment" class="slot-title">{{timeSlot.title}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 日视图 -->
      <view v-else class="day-view">
        <view class="day-header">
          <text class="day-title">{{selectedDay.year}}年{{selectedDay.month + 1}}月{{selectedDay.day}}日</text>
          <text class="day-weekday">{{weekdays[selectedDay.weekday]}}</text>
        </view>
        <view class="time-slots">
          <view 
            v-for="hour in 12" 
            :key="hour"
            class="hour-row">
            <text class="hour-label">{{hour + 7}}:00</text>
            <view class="hour-line"></view>
          </view>
          
          <view 
            v-for="(appointment, index) in selectedDayAppointments" 
            :key="index"
            class="appointment-slot"
            :style="{
              top: (appointment.hour - 8) * 60 + appointment.minute + 'px',
              height: appointment.duration + 'px'
            }"
            @click="viewAppointmentDetail(appointment)">
            <view class="appointment-content">
              <text class="appointment-time">{{appointment.startTime}} - {{appointment.endTime}}</text>
              <text class="appointment-title">{{appointment.title}}</text>
              <text class="appointment-customer">{{appointment.customer}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部订单列表 -->
    <view v-if="currentView !== 'day'" class="order-summary">
      <view class="summary-header">
        <text class="summary-title">{{selectedDay.year}}年{{selectedDay.month + 1}}月{{selectedDay.day}}日 预约订单</text>
        <text class="summary-count">共 {{selectedDayAppointments.length}} 个预约</text>
      </view>
      <scroll-view scroll-y="true" class="order-list">
        <view 
          v-for="(order, index) in selectedDayAppointments" 
          :key="index"
          class="order-item"
          @click="navigateToDetail(order.id)">
          <view class="order-time">{{order.startTime}} - {{order.endTime}}</view>
          <view class="order-info">
            <text class="order-title">{{order.title}}</text>
            <text class="order-customer">客户: {{order.customer}}</text>
          </view>
          <view class="order-status" :class="'status-' + order.status">{{getStatusText(order.status)}}</view>
        </view>
        
        <view v-if="selectedDayAppointments.length === 0" class="empty-tip">
          <text>当日暂无预约订单</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="action-bar">
      <view class="action-btn" @click="navigateToList">
        <text class="action-icon">📋</text>
        <text>列表视图</text>
      </view>
      <view class="action-btn primary" @click="addAppointment">
        <text class="action-icon">➕</text>
        <text>添加预约</text>
      </view>
      <view class="action-btn" @click="refreshCalendar">
        <text class="action-icon">🔄</text>
        <text>刷新</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentView: 'month', // month, week, day
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth(),
      selectedDay: {
        year: new Date().getFullYear(),
        month: new Date().getMonth(),
        day: new Date().getDate(),
        weekday: new Date().getDay()
      },
      weekdays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      daysInMonth: [],
      daysInWeek: [],
      appointments: [
        {
          id: '3001',
          title: '水果礼盒配送',
          customer: '张三',
          phone: '13800138000',
          date: '2023-05-15',
          startTime: '10:00',
          endTime: '11:00',
          hour: 10,
          minute: 0,
          duration: 60,
          status: 'pending',
          remark: '请准时送达'
        },
        {
          id: '3002',
          title: '蔬菜套餐配送',
          customer: '李四',
          phone: '13900139000',
          date: '2023-05-15',
          startTime: '14:30',
          endTime: '15:30',
          hour: 14,
          minute: 30,
          duration: 60,
          status: 'confirmed',
          remark: '电话联系'
        },
        {
          id: '3003',
          title: '生日蛋糕配送',
          customer: '王五',
          phone: '13700137000',
          date: '2023-05-16',
          startTime: '09:00',
          endTime: '10:00',
          hour: 9,
          minute: 0,
          duration: 60,
          status: 'completed',
          remark: '需要生日贺卡'
        }
      ],
      selectedDayAppointments: []
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    prevMonth() {
      if (this.currentMonth === 0) {
        this.currentYear--;
        this.currentMonth = 11;
      } else {
        this.currentMonth--;
      }
      this.generateCalendarDays();
    },
    nextMonth() {
      if (this.currentMonth === 11) {
        this.currentYear++;
        this.currentMonth = 0;
      } else {
        this.currentMonth++;
      }
      this.generateCalendarDays();
    },
    switchView(view) {
      this.currentView = view;
      if (view === 'week') {
        this.generateWeekDays();
      } else if (view === 'month') {
        this.generateCalendarDays();
      }
    },
    selectDay(day) {
      this.selectedDay = {
        year: day.year,
        month: day.month,
        day: day.day,
        weekday: new Date(day.year, day.month, day.day).getDay()
      };
      this.loadDayAppointments();
    },
    generateCalendarDays() {
      const year = this.currentYear;
      const month = this.currentMonth;
      
      // 获取当月第一天是星期几
      const firstDay = new Date(year, month, 1).getDay();
      
      // 获取当月天数
      const daysInMonth = new Date(year, month + 1, 0).getDate();
      
      // 获取上个月天数
      const daysInPrevMonth = new Date(year, month, 0).getDate();
      
      const days = [];
      
      // 添加上个月的日期
      for (let i = firstDay - 1; i >= 0; i--) {
        const prevMonth = month === 0 ? 11 : month - 1;
        const prevYear = month === 0 ? year - 1 : year;
        const day = daysInPrevMonth - i;
        days.push({
          day,
          month: prevMonth,
          year: prevYear,
          currentMonth: false,
          isToday: false,
          orderCount: this.getOrderCount(prevYear, prevMonth, day)
        });
      }
      
      // 添加当月的日期
      const today = new Date();
      for (let i = 1; i <= daysInMonth; i++) {
        const isToday = i === today.getDate() && month === today.getMonth() && year === today.getFullYear();
        days.push({
          day: i,
          month,
          year,
          currentMonth: true,
          isToday,
          orderCount: this.getOrderCount(year, month, i)
        });
      }
      
      // 添加下个月的日期，补满42个格子（6行7列）
      const remainingDays = 42 - days.length;
      for (let i = 1; i <= remainingDays; i++) {
        const nextMonth = month === 11 ? 0 : month + 1;
        const nextYear = month === 11 ? year + 1 : year;
        days.push({
          day: i,
          month: nextMonth,
          year: nextYear,
          currentMonth: false,
          isToday: false,
          orderCount: this.getOrderCount(nextYear, nextMonth, i)
        });
      }
      
      this.daysInMonth = days;
    },
    generateWeekDays() {
      const days = [];
      const currentDate = new Date(this.selectedDay.year, this.selectedDay.month, this.selectedDay.day);
      const dayOfWeek = currentDate.getDay();
      
      // 获取本周的起始日期（周日为起始）
      const startDate = new Date(currentDate);
      startDate.setDate(currentDate.getDate() - dayOfWeek);
      
      // 生成一周的日期
      for (let i = 0; i < 7; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);
        
        const day = date.getDate();
        const month = date.getMonth();
        const year = date.getFullYear();
        const isToday = day === new Date().getDate() && month === new Date().getMonth() && year === new Date().getFullYear();
        
        const timeSlots = [];
        
        // 为当天添加预约时间段
        this.appointments.forEach(appointment => {
          const appointmentDate = new Date(appointment.date);
          if (appointmentDate.getFullYear() === year && 
              appointmentDate.getMonth() === month && 
              appointmentDate.getDate() === day) {
            timeSlots.push({
              hour: appointment.hour,
              minute: appointment.minute,
              duration: appointment.duration,
              hasAppointment: true,
              startTime: appointment.startTime,
              title: appointment.title,
              id: appointment.id
            });
          }
        });
        
        days.push({
          day,
          month,
          year,
          isToday,
          orderCount: this.getOrderCount(year, month, day),
          timeSlots
        });
      }
      
      this.daysInWeek = days;
    },
    getOrderCount(year, month, day) {
      // 计算指定日期的订单数量
      const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      return this.appointments.filter(a => a.date === dateString).length;
    },
    loadDayAppointments() {
      // 加载选中日期的预约订单
      const dateString = `${this.selectedDay.year}-${String(this.selectedDay.month + 1).padStart(2, '0')}-${String(this.selectedDay.day).padStart(2, '0')}`;
      this.selectedDayAppointments = this.appointments.filter(a => a.date === dateString);
    },
    viewAppointment(timeSlot) {
      if (timeSlot.hasAppointment) {
        this.navigateToDetail(timeSlot.id);
      }
    },
    viewAppointmentDetail(appointment) {
      this.navigateToDetail(appointment.id);
    },
    navigateToDetail(id) {
      uni.navigateTo({
        url: `./detail?id=${id}`
      });
    },
    navigateToList() {
      uni.navigateTo({
        url: './list'
      });
    },
    addAppointment() {
      uni.showToast({
        title: '添加预约功能开发中',
        icon: 'none'
      });
    },
    refreshCalendar() {
      this.generateCalendarDays();
      this.loadDayAppointments();
      uni.showToast({
        title: '日历已刷新',
        icon: 'success'
      });
    },
    getStatusText(status) {
      const texts = {
        pending: '待确认',
        confirmed: '已确认',
        completed: '已完成',
        cancelled: '已取消'
      };
      return texts[status] || '未知状态';
    },
    showFilterOptions() {
      uni.showActionSheet({
        itemList: ['全部预约', '待确认', '已确认', '已完成', '已取消'],
        success: (res) => {
          uni.showToast({
            title: '筛选功能开发中',
            icon: 'none'
          });
        }
      });
    }
  },
  onLoad() {
    this.generateCalendarDays();
    this.loadDayAppointments();
  }
}
</script>

<style>
.calendar-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.filter-icon {
  font-size: 20px;
  color: #fff;
}

.calendar-wrapper {
  background-color: #fff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.month-selector {
  display: flex;
  align-items: center;
}

.prev-month, .next-month {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: 16px;
}

.arrow {
  font-size: 12px;
  color: #666;
}

.current-month {
  font-size: 16px;
  font-weight: 600;
  margin: 0 12px;
  min-width: 100px;
  text-align: center;
}

.view-switch {
  display: flex;
  background-color: #f5f7fa;
  border-radius: 16px;
  overflow: hidden;
}

.view-option {
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
}

.view-option.active {
  background-color: #1677FF;
  color: #fff;
}

.weekdays {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #999;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 1fr);
  gap: 1px;
  background-color: #f0f0f0;
}

.day-cell {
  height: 60px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px 0;
  position: relative;
}

.day-number {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.other-month .day-number {
  color: #ccc;
}

.today {
  background-color: #e6f7ff;
}

.today .day-number {
  background-color: #1677FF;
  color: #fff;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.order-indicator {
  width: 20px;
  height: 20px;
  background-color: #ff6a00;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4px;
}

.order-count {
  font-size: 12px;
  color: #fff;
}

.week-view {
  display: flex;
  height: 500px;
  position: relative;
}

.week-day {
  flex: 1;
  border-right: 1px solid #f0f0f0;
  position: relative;
}

.week-day:last-child {
  border-right: none;
}

.week-day-header {
  padding: 8px 0;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.week-day-name {
  font-size: 12px;
  color: #999;
  display: block;
}

.week-day-number {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.today .week-day-number {
  background-color: #1677FF;
  color: #fff;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-top: 4px;
}

.week-day-content {
  position: relative;
  height: 450px;
}

.time-slot {
  position: absolute;
  left: 2px;
  right: 2px;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 4px;
}

.time-slot.has-appointment {
  background-color: #e6f7ff;
  border-left: 3px solid #1677FF;
}

.slot-time {
  font-size: 12px;
  color: #666;
  display: block;
}

.slot-title {
  font-size: 12px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.day-view {
  padding: 16px;
}

.day-header {
  margin-bottom: 16px;
  text-align: center;
}

.day-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
}

.day-weekday {
  font-size: 14px;
  color: #666;
}

.time-slots {
  position: relative;
  height: 500px;
}

.hour-row {
  display: flex;
  height: 60px;
  position: relative;
}

.hour-label {
  width: 50px;
  font-size: 12px;
  color: #999;
  text-align: right;
  padding-right: 8px;
}

.hour-line {
  flex: 1;
  border-top: 1px solid #f0f0f0;
  margin-top: 10px;
}

.appointment-slot {
  position: absolute;
  left: 60px;
  right: 0;
  background-color: #e6f7ff;
  border-left: 3px solid #1677FF;
  border-radius: 4px;
  padding: 8px;
}

.appointment-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.appointment-time {
  font-size: 12px;
  color: #666;
}

.appointment-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 4px 0;
}

.appointment-customer {
  font-size: 12px;
  color: #666;
}

.order-summary {
  flex: 1;
  margin: 0 16px 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.summary-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.summary-count {
  font-size: 14px;
  color: #666;
}

.order-list {
  flex: 1;
  padding: 0 16px;
}

.order-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-time {
  width: 100px;
  font-size: 14px;
  color: #666;
}

.order-info {
  flex: 1;
}

.order-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.order-customer {
  font-size: 14px;
  color: #666;
}

.order-status {
  font-size: 14px;
  font-weight: 500;
}

.status-pending {
  color: #FF9800;
}

.status-confirmed {
  color: #2196F3;
}

.status-completed {
  color: #4CAF50;
}

.status-cancelled {
  color: #9E9E9E;
}

.empty-tip {
  padding: 40px 0;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.action-bar {
  display: flex;
  padding: 12px 16px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  height: 44px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  margin: 0 8px;
  background-color: #f5f7fa;
}

.action-btn.primary {
  background-color: #1677FF;
  color: #fff;
}

.action-icon {
  font-size: 18px;
  margin-bottom: 4px;
}
</style> 