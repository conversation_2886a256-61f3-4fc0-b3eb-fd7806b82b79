/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.distribution-center {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 88rpx 32rpx 30rpx;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 24rpx;
  height: 24rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.navbar-right {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 用户卡片样式 */
.user-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.user-info {
  display: flex;
  align-items: center;
}
.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
  border: 2rpx solid #f0f0f0;
}
.user-details {
  flex: 1;
}
.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}
.user-level {
  display: flex;
  align-items: center;
}
.level-tag {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}
.invite-code {
  font-size: 24rpx;
  color: #666;
}
.user-status {
  margin-top: 20rpx;
}
.apply-btn {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  line-height: 1.5;
}
.apply-btn.large {
  font-size: 32rpx;
  padding: 20rpx 40rpx;
  width: 80%;
}

/* 数据概览卡片 */
.stats-card {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.stats-action {
  display: flex;
  align-items: center;
}
.action-text {
  font-size: 26rpx;
  color: #666;
}
.action-arrow {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #666;
  border-top: 2rpx solid #666;
  transform: rotate(45deg);
  margin-left: 10rpx;
}
.stats-content {
  margin-bottom: 30rpx;
}
.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.stats-item.main {
  margin-bottom: 30rpx;
}
.stats-row {
  display: flex;
  justify-content: space-between;
}
.item-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.stats-item.main .item-value {
  font-size: 48rpx;
  color: #6B0FBE;
}
.item-label {
  font-size: 24rpx;
  color: #666;
}
.stats-footer {
  display: flex;
  justify-content: center;
}
.withdraw-btn {
  background: linear-gradient(135deg, #FF9500, #FF5722);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  padding: 12rpx 60rpx;
  line-height: 1.5;
}

/* 功能菜单 */
.menu-section {
  margin: 30rpx;
}
.menu-grid {
  display: flex;
  flex-wrap: wrap;
  background: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.menu-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}
.menu-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 40rpx;
}
.menu-icon.products {
  background-color: #FF9500;
}
.menu-icon.team {
  background-color: #409EFF;
}
.menu-icon.promotion {
  background-color: #67C23A;
}
.menu-icon.commission {
  background-color: #E6A23C;
}
.menu-name {
  font-size: 26rpx;
  color: #333;
}

/* 推荐商品 */
.products-section {
  margin: 30rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.section-action {
  display: flex;
  align-items: center;
}
.products-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.product-card {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.product-image {
  width: 100%;
  height: 300rpx;
  background-color: #f5f5f5;
}
.product-info {
  padding: 20rpx;
}
.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF5722;
}
.commission-tag {
  font-size: 24rpx;
  color: #6B0FBE;
  background-color: rgba(107, 15, 190, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

/* 分销员申请引导 */
.apply-guide {
  margin: 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.guide-header {
  margin-bottom: 30rpx;
}
.guide-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}
.guide-content {
  margin-bottom: 30rpx;
}
.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.benefit-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 40rpx;
}
.benefit-icon.income {
  background-color: #FF9500;
}
.benefit-icon.tools {
  background-color: #67C23A;
}
.benefit-icon.team {
  background-color: #409EFF;
}
.benefit-info {
  flex: 1;
}
.benefit-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.benefit-desc {
  font-size: 24rpx;
  color: #666;
}
.guide-footer {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}