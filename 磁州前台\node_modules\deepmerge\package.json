{"author": "<PERSON>", "name": "deepmerge", "description": "A library for deep (recursive) merging of Javascript objects", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "version": "1.5.2", "homepage": "https://github.com/KyleAMathews/deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "main": "dist/umd.js", "module": "dist/es.js", "browser": "dist/cjs.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd README.markdown"}, "devDependencies": {"is-mergeable-object": "1.1.0", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "license": "MIT"}