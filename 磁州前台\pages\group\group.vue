<template>
	<view class="group-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-title">进群</view>
		</view>
		
		<!-- 搜索框 -->
		<view class="search-box">
			<view class="search-icon-wrap">
				<image class="search-icon" src="/static/images/tabbar/search-dark.png"></image>
			</view>
			<input 
				class="search-input" 
				type="text" 
				v-model="searchKeyword" 
				placeholder="搜索群组名称" 
				confirm-type="search"
				@confirm="searchGroups"
			/>
			<view class="voice-icon-wrap" @click="useVoiceSearch">
				<image class="voice-icon" src="/static/images/tabbar/语音.png"></image>
			</view>
			<view class="search-btn" @click="searchGroups">
				<image class="search-btn-icon" src="/static/images/tabbar/放大镜.png"></image>
								</view>
							</view>
		
		<!-- 左侧类别栏和右侧内容区 -->
		<view class="content-container">
			<!-- 左侧类别栏 -->
			<scroll-view class="category-list" scroll-y>
				<view 
					class="category-item" 
					:class="{'active': currentCategory === category.id}"
					v-for="(category, index) in categories" 
					:key="index"
					@click="changeCategory(category.id)"
				>
					<text class="category-text">{{ category.name }}</text>
				</view>
			</scroll-view>
			
			<!-- 右侧内容区 -->
			<scroll-view class="group-content" scroll-y>
				<!-- 显示的群组 -->
				<view 
					class="group-item" 
					v-for="(group, index) in displayGroups" 
					:key="index"
				>
					<image class="group-avatar" :src="group.avatar || '/static/images/tabbar/default-group.png'"></image>
							<view class="group-info">
						<text class="group-name">{{ group.name }}</text>
						<text class="group-desc">{{ group.desc.length > 8 ? group.desc.substring(0, 8) + '...' : group.desc }}</text>
						<text class="group-count">{{ group.memberCount || '88' }}人</text>
					</view>
					<button class="join-button" @click="joinGroup(group)">加入</button>
				</view>
				
				<!-- 无数据提示 -->
				<view class="no-data" v-if="displayGroups.length === 0">
					<text class="no-data-text">暂无群组数据</text>
				</view>
			</scroll-view>
			</view>
		
		<!-- 悬浮按钮 -->
		<view class="share-button" @click="onShare">
			<image class="share-icon" src="/static/images/tabbar/share.png"></image>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchKeyword: '',
				currentCategory: 'hot',
				categories: [
					{ id: 'hot', name: '热门群' },
					{ id: 'service', name: '服务群' },
					{ id: 'bus', name: '拼车群' },
					{ id: 'community', name: '小区群' },
					{ id: 'town', name: '老乡群' },
					{ id: 'village', name: '乡镇群' },
					{ id: 'shop', name: '商家群' },
					{ id: 'job', name: '求职群' },
					{ id: 'parents', name: '家长群' },
					{ id: 'hobby', name: '兴趣群' }
				],
				groups: [
					{
						id: 1,
						name: '磁县招聘求职群',
						desc: '本地招聘求职信息发布平台，每日更新...',
						avatar: '/static/images/tabbar/group-avatar1.png',
						category: 'hot',
						memberCount: 358
					},
					{
						id: 2,
						name: '磁县生活服务群',
						desc: '本地生活服务信息共享',
						avatar: '/static/images/tabbar/group-avatar2.png',
						category: 'service',
						memberCount: 206
					},
					{
						id: 3,
						name: '磁县-邯郸拼车群',
						desc: '磁县到邯郸的拼车信息',
						avatar: '/static/images/tabbar/group-avatar3.png',
						category: 'bus',
						memberCount: 129
					},
					{
						id: 4,
						name: '怡馨小区业主群',
						desc: '小区业主交流互助平台',
						avatar: '/static/images/tabbar/group-avatar4.png',
						category: 'community',
						memberCount: 186
					},
					{
						id: 5,
						name: '磁县老乡群',
						desc: '磁县老乡交流群',
						avatar: '/static/images/tabbar/group-avatar5.png',
						category: 'town',
						memberCount: 412
					},
					{
						id: 6,
						name: '观台镇群',
						desc: '观台镇本地信息分享',
						avatar: '/static/images/tabbar/group-avatar6.png',
						category: 'village',
						memberCount: 168
					},
					{
						id: 7,
						name: '磁县家电商家群',
						desc: '家电商家交流平台',
						category: 'shop',
						memberCount: 98
					},
					{
						id: 8,
						name: '磁县初中家长群',
						desc: '初中家长信息交流平台',
						category: 'parents',
						memberCount: 145
					}
				],
				isSearching: false
			}
		},
		computed: {
			filteredGroups() {
				return this.groups.filter(group => group.category === this.currentCategory);
			},
			displayGroups() {
				// 如果搜索关键词为空，则显示当前分类下的群组
				if (!this.searchKeyword.trim() || !this.isSearching) {
					return this.filteredGroups;
				}
				
				// 如果有搜索关键词，则搜索所有群组
				const keyword = this.searchKeyword.toLowerCase().trim();
				return this.groups.filter(group => 
					group.name.toLowerCase().includes(keyword) || 
					group.desc.toLowerCase().includes(keyword)
				);
			}
		},
		onLoad() {
		},
		onShareAppMessage() {
			// 自定义转发内容
			return {
				title: '快来加入磁县拼车群，小区群，老乡群，求职招聘群，家长群把！',
				path: '/pages/group/group'
			}
		},
		methods: {
			changeCategory(categoryId) {
				this.currentCategory = categoryId;
				// 切换分类时重置搜索状态
				this.searchKeyword = '';
				this.isSearching = false;
			},
			searchGroups() {
				// 执行搜索操作
				this.isSearching = !!this.searchKeyword.trim();
			},
			useVoiceSearch() {
				// 语音搜索功能，需根据平台支持情况实现
				uni.showToast({
					title: '语音搜索功能开发中',
					icon: 'none'
				});
			},
			joinGroup(group) {
				uni.showModal({
					title: '加入群聊',
					content: `是否加入"${group.name}"？`,
					success: (res) => {
						if (res.confirm) {
				uni.showToast({
								title: '已发送加群申请',
					icon: 'success'
				});
						}
					}
				});
		},
			onShare() {
				if (wx && wx.showShareMenu) {
					wx.showShareMenu({ withShareTicket: true })
				}
				uni.showToast({ title: '请点击右上角"..."进行转发', icon: 'none' })
			}
		}
	}
</script>

<style>
	.group-page {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f7f7f7;
		position: relative;
	}
	
	/* 自定义导航栏 */
	.custom-navbar {
		background-image: linear-gradient(135deg, #0052CC, #0066FF);
		height: 88rpx;
		padding-top: 44px; /* 状态栏高度 */
		display: flex;
		align-items: center;
		position: relative;
		box-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);
		z-index: 10;
	}
	
	.navbar-title {
		flex: 1;
		text-align: center;
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: 500;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}
	
	/* 搜索框 */
	.search-box {
		background-color: #ffffff;
		margin: 20rpx;
		height: 72rpx;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid rgba(235, 238, 245, 0.8);
	}
	
	.search-icon-wrap {
		display: flex;
		align-items: center;
		padding: 0 10rpx;
	}
	
	.search-icon {
		width: 32rpx;
		height: 32rpx;
		opacity: 0.5;
	}
	
	.search-input {
		flex: 1;
		height: 72rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.voice-icon-wrap {
		padding: 0 10rpx;
		display: flex;
		align-items: center;
	}
	
	.voice-icon {
		width: 36rpx;
		height: 36rpx;
	}
	
	.search-btn {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
	}
	
	.search-btn-icon {
		width: 44rpx;
		height: 44rpx;
	}
	
	/* 内容区 */
	.content-container {
		display: flex;
		flex: 1;
		overflow: hidden;
		height: calc(100vh - 112rpx);
	}
	
	/* 左侧类别栏 */
	.category-list {
		width: 180rpx;
		background-color: #f2f2f2;
		height: calc(100vh - 112rpx);
	}
	
	.category-item {
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
	}
	
	.category-item.active {
		background-color: #ffffff;
	}
	
	.category-item.active::before {
		content: '';
		position: absolute;
		left: 0;
		top: 34rpx;
		height: 32rpx;
		width: 6rpx;
		background-color: #0052CC;
		border-radius: 0 3rpx 3rpx 0;
	}
	
	.category-text {
		font-size: 28rpx;
		color: #666;
		text-align: center;
	}
	
	.category-item.active .category-text {
		color: #0052CC;
		font-weight: 500;
	}
	
	/* 右侧群组内容 */
	.group-content {
		flex: 1;
		background-color: #ffffff;
		height: calc(100vh - 112rpx);
		padding: 0 20rpx;
	}
	
	.group-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f2f2f2;
	}
	
	.group-avatar {
		width: 90rpx;
		height: 90rpx;
		border-radius: 12rpx;
		background-color: #f0f0f0;
		margin-right: 20rpx;
	}
	
	.group-info {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.group-name {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 8rpx;
	}
	
	.group-desc {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 8rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 380rpx;
	}
	
	.group-count {
		font-size: 22rpx;
		color: #aaa;
	}
	
	.join-button {
		min-width: 120rpx;
		height: 60rpx;
		background-color: #0052CC;
		color: #ffffff;
		font-size: 26rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0;
		padding: 0 20rpx;
	}
	
	.no-data {
		padding: 60rpx 0;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.no-data-text {
		font-size: 28rpx;
		color: #999;
	}
	
	/* 悬浮分享按钮 */
	.share-button {
		position: fixed;
		right: 32rpx;
		bottom: 180rpx;
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background-color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
		z-index: 9;
	}
	
	.share-icon {
		width: 36rpx;
		height: 36rpx;
	}
</style> 