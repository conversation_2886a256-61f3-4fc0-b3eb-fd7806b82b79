/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.location-select-container {
  padding: 20rpx;
  background-color: #f7f7f7;
  min-height: 100vh;
}
.search-box {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.search-input-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 80rpx;
}
.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  margin: 0 20rpx;
}
.clear-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}
.cancel-btn {
  width: 120rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  margin-left: 20rpx;
  background: none;
  padding: 0;
  color: #0052CC;
}
.section-title {
  font-size: 28rpx;
  color: #666;
  margin: 20rpx 0;
}
.section-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0;
}
.clear-history {
  font-size: 24rpx;
  color: #999;
}
.current-location-box, .location-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}
.location-icon-box {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #0052CC;
  font-size: 32rpx;
  margin-right: 20rpx;
}
.location-info {
  flex: 1;
  overflow: hidden;
}
.location-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.location-detail {
  font-size: 24rpx;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.location-action {
  margin-left: 20rpx;
}
.use-btn {
  display: inline-block;
  padding: 6rpx 20rpx;
  background-color: rgba(0, 82, 204, 0.1);
  color: #0052CC;
  border-radius: 30rpx;
  font-size: 24rpx;
}
.empty-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}