<view class="add-level-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">添加会员等级</text><view class="navbar-right"></view></view><view class="form-content"><view class="section-card"><view class="section-title">基本信息</view><view class="form-item"><text class="form-label">等级名称</text><input class="form-input" placeholder="请输入等级名称" maxlength="10" value="{{b}}" bindinput="{{c}}"/></view><view class="form-item"><text class="form-label">等级图标</text><view class="icon-upload" bindtap="{{f}}"><image wx:if="{{d}}" class="preview-icon" src="{{e}}" mode="aspectFit"></image><view wx:else class="upload-placeholder"><text class="upload-icon">+</text><text class="upload-text">上传图标</text></view></view></view><view class="form-item"><text class="form-label">等级颜色</text><view class="color-picker"><view wx:for="{{g}}" wx:for-item="color" wx:key="a" style="{{'background:' + color.b}}" class="{{['color-option', color.c && 'active']}}" bindtap="{{color.d}}"></view></view></view></view><view class="section-card"><view class="section-title">升级条件</view><view class="form-item"><text class="form-label">条件类型</text><view class="radio-group"><view class="{{['radio-item', h && 'active']}}" bindtap="{{i}}"><text class="radio-text">累计消费金额</text></view><view class="{{['radio-item', j && 'active']}}" bindtap="{{k}}"><text class="radio-text">成长值</text></view></view></view><view class="form-item"><text class="form-label">{{l}}</text><view class="form-input-group"><input type="number" class="form-input" placeholder="{{m}}" value="{{n}}" bindinput="{{o}}"/><text class="input-suffix">{{p}}</text></view></view><view class="form-item switch-item"><text class="form-label">自动升级</text><switch checked="{{q}}" bindchange="{{r}}" color="#4A00E0"/></view><view class="form-item switch-item"><text class="form-label">等级保护</text><switch checked="{{s}}" bindchange="{{t}}" color="#4A00E0"/></view><view wx:if="{{v}}" class="form-item"><text class="form-label">保护期限</text><view class="form-input-group"><input type="number" class="form-input" placeholder="请输入保护天数" value="{{w}}" bindinput="{{x}}"/><text class="input-suffix">天</text></view></view></view><view class="section-card"><view class="section-title">等级权益</view><view class="form-item"><text class="form-label">折扣比例</text><view class="form-input-group"><input type="digit" class="form-input" placeholder="请输入折扣比例" value="{{y}}" bindinput="{{z}}"/><text class="input-suffix">折</text></view></view><view class="form-item"><text class="form-label">积分倍率</text><view class="form-input-group"><input type="digit" class="form-input" placeholder="请输入积分倍率" value="{{A}}" bindinput="{{B}}"/><text class="input-suffix">倍</text></view></view><view class="form-item"><text class="form-label">特权选择</text><view class="privilege-list"><view wx:for="{{C}}" wx:for-item="privilege" wx:key="e" class="privilege-item"><view class="{{['privilege-checkbox', privilege.b && 'checked']}}" bindtap="{{privilege.c}}"><view wx:if="{{privilege.a}}" class="checkbox-inner"></view></view><text class="privilege-name">{{privilege.d}}</text></view></view></view><view class="form-item"><text class="form-label">自定义权益</text><view class="custom-privileges"><view wx:for="{{D}}" wx:for-item="item" wx:key="d" class="custom-privilege-item"><input class="custom-privilege-input" placeholder="请输入权益内容" value="{{item.a}}" bindinput="{{item.b}}"/><view class="delete-btn" bindtap="{{item.c}}">×</view></view><view class="add-custom-btn" bindtap="{{E}}"><text class="add-custom-text">+ 添加自定义权益</text></view></view></view></view></view><view class="bottom-bar"><button class="cancel-btn" bindtap="{{F}}">取消</button><button class="save-btn" bindtap="{{G}}">保存</button></view></view>