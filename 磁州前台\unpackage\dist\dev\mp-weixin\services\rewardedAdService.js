"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
class RewardedAdService {
  constructor() {
    this.videoAd = null;
    this.currentRewardType = null;
    this.currentCarpoolId = null;
    this.currentMerchantId = null;
    this.currentInfoId = null;
    this.adUnitId = "adunit-9699c56c26082b54";
  }
  /**
   * 初始化激励视频广告
   */
  initRewardedVideoAd() {
    if (typeof common_vendor.wx$1 === "undefined" || !common_vendor.wx$1.createRewardedVideoAd) {
      common_vendor.index.__f__("warn", "at services/rewardedAdService.js:24", "当前环境不支持激励视频广告");
      return false;
    }
    try {
      this.videoAd = common_vendor.wx$1.createRewardedVideoAd({
        adUnitId: this.adUnitId
      });
      this.videoAd.onLoad(() => {
        common_vendor.index.__f__("log", "at services/rewardedAdService.js:35", "激励视频广告加载成功");
      });
      this.videoAd.onError((err) => {
        common_vendor.index.__f__("error", "at services/rewardedAdService.js:40", "激励视频广告加载失败", err);
        common_vendor.index.showToast({
          title: "广告加载失败",
          icon: "none"
        });
      });
      this.videoAd.onClose((res) => {
        if (res && res.isEnded || res === void 0) {
          common_vendor.index.__f__("log", "at services/rewardedAdService.js:51", "激励视频广告完成，发放奖励");
          this.grantReward();
        } else {
          common_vendor.index.__f__("log", "at services/rewardedAdService.js:55", "激励视频广告未完成");
          common_vendor.index.showToast({
            title: "请观看完整广告",
            icon: "none"
          });
        }
      });
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at services/rewardedAdService.js:65", "初始化激励视频广告失败", error);
      return false;
    }
  }
  /**
   * 显示激励视频广告
   * @param {string} rewardType - 奖励类型: 'free_publish', 'free_top', 'free_refresh', 'merchant_renew', 'merchant_join', 'merchant_top', 'merchant_refresh'
   * @param {number} carpoolId - 拼车信息ID（置顶和刷新时需要）
   * @param {number} merchantId - 商家ID（商家相关功能时需要）
   * @param {number} infoId - 信息ID（商家信息置顶和刷新时需要）
   */
  async showRewardedVideoAd(rewardType, carpoolId = null, merchantId = null, infoId = null) {
    this.currentRewardType = rewardType;
    this.currentCarpoolId = carpoolId;
    this.currentMerchantId = merchantId;
    this.currentInfoId = infoId;
    if (!this.videoAd) {
      const initSuccess = this.initRewardedVideoAd();
      if (!initSuccess) {
        common_vendor.index.showToast({
          title: "广告功能不可用",
          icon: "none"
        });
        return false;
      }
    }
    try {
      await this.videoAd.show();
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at services/rewardedAdService.js:100", "显示激励视频广告失败", error);
      try {
        await this.videoAd.load();
        await this.videoAd.show();
        return true;
      } catch (retryError) {
        common_vendor.index.__f__("error", "at services/rewardedAdService.js:108", "重试显示激励视频广告失败", retryError);
        common_vendor.index.showToast({
          title: "广告显示失败",
          icon: "none"
        });
        return false;
      }
    }
  }
  /**
   * 发放奖励
   */
  async grantReward() {
    var _a;
    try {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!userInfo || !userInfo.id) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        return;
      }
      const requestData = {
        reward_type: this.currentRewardType,
        user_id: userInfo.id,
        ad_unit_id: this.adUnitId,
        timestamp: Date.now()
      };
      if (this.currentCarpoolId) {
        requestData.carpool_id = this.currentCarpoolId;
      }
      if (this.currentMerchantId) {
        requestData.merchant_id = this.currentMerchantId;
      }
      if (this.currentInfoId) {
        requestData.info_id = this.currentInfoId;
      }
      let apiUrl = "/api/carpool/ads/reward";
      if (this.currentRewardType === "merchant_renew") {
        apiUrl = "/api/merchant/ads/renew";
      } else if (this.currentRewardType === "merchant_join") {
        apiUrl = "/api/merchant/ads/join";
      } else if (this.currentRewardType === "merchant_top") {
        apiUrl = "/api/merchant/ads/top";
      } else if (this.currentRewardType === "merchant_refresh") {
        apiUrl = "/api/merchant/ads/refresh";
      }
      const response = await utils_request.request.post(apiUrl, requestData);
      if (response.data && response.data.success) {
        const rewardData = response.data.data;
        this.showRewardSuccess(rewardData);
      } else {
        common_vendor.index.showToast({
          title: ((_a = response.data) == null ? void 0 : _a.message) || "奖励发放失败",
          icon: "none"
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at services/rewardedAdService.js:184", "发放奖励失败", error);
      common_vendor.index.showToast({
        title: "网络错误，请重试",
        icon: "none"
      });
    }
  }
  /**
   * 显示奖励成功提示
   * @param {Object} rewardData - 奖励数据
   */
  showRewardSuccess(rewardData) {
    let message = "奖励发放成功";
    switch (rewardData.type) {
      case "free_publish":
        message = "获得1次免费发布机会！";
        break;
      case "free_top":
        message = "置顶2小时成功！";
        break;
      case "free_refresh":
        message = "刷新成功！";
        break;
      case "merchant_renew":
        message = `商家入驻续费成功！延长${rewardData.days || 7}天`;
        break;
      case "merchant_join":
        message = "商家入驻成功！获得1个月免费特权";
        break;
      case "merchant_top":
        message = "商家信息置顶成功！置顶2小时";
        break;
      case "merchant_refresh":
        message = "商家信息刷新成功！";
        break;
    }
    common_vendor.index.showToast({
      title: message,
      icon: "success",
      duration: 2e3
    });
    common_vendor.index.$emit("rewardGranted", {
      type: rewardData.type,
      data: rewardData
    });
  }
  /**
   * 检查今日观看次数
   * @param {string} adType - 广告类型
   */
  async checkTodayWatchCount(adType) {
    try {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!userInfo || !userInfo.id) {
        return { canWatch: false, message: "请先登录" };
      }
      const response = await utils_request.request.get("/api/carpool/ads/stats", {
        params: {
          user_id: userInfo.id,
          ad_type: adType,
          date: (/* @__PURE__ */ new Date()).toISOString().split("T")[0]
        }
      });
      const stats = response.data;
      const dailyLimit = this.getDailyLimit(adType);
      if (stats.today_views >= dailyLimit) {
        return {
          canWatch: false,
          message: `今日观看次数已达上限（${dailyLimit}次）`
        };
      }
      return {
        canWatch: true,
        remaining: dailyLimit - stats.today_views
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at services/rewardedAdService.js:271", "检查观看次数失败", error);
      return { canWatch: true };
    }
  }
  /**
   * 获取每日观看限制
   * @param {string} adType - 广告类型
   */
  getDailyLimit(adType) {
    const limits = {
      "publish": 5,
      "top": 3,
      "refresh": 5,
      "merchant_renew": 2,
      // 商家续费每月限制2次
      "merchant_join": 1,
      // 商家入驻每月限制1次
      "merchant_top": 3,
      // 商家置顶每日限制3次
      "merchant_refresh": 5
      // 商家刷新每日限制5次
    };
    return limits[adType] || 3;
  }
  /**
   * 销毁广告实例
   */
  destroy() {
    if (this.videoAd) {
      this.videoAd.destroy();
      this.videoAd = null;
    }
  }
}
const rewardedAdService = new RewardedAdService();
exports.rewardedAdService = rewardedAdService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/services/rewardedAdService.js.map
