<template>
  <view class="profile-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">个人主页</view>
      <view class="navbar-right" @click="navigateToSettings">
        <image src="/static/images/tabbar/设置.png" class="setting-icon"></image>
      </view>
    </view>
    
    <!-- 个人资料卡片 -->
    <view :style="{ marginTop: (navbarHeight + 10) + 'px', padding: '0 30rpx' }">
      <UserProfileCard 
        :userInfo="{
          nickname: userInfo.nickname,
          avatar: userInfo.avatar,
          userId: '88965',
          joinDate: '2023年6月',
          bio: userInfo.signature,
          vipLevel: 3
        }"
        @edit-profile="editProfile"
        @data-analysis="goToDataAnalysis"
      />
    </view>
    
    <view class="profile-stats">
      <view class="stat-item" @click="navigateTo('/pages/my/follows')">
        <view class="stat-number">{{ userStats.follows }}</view>
        <view class="stat-label">关注</view>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item" @click="navigateTo('/pages/my/fans')">
        <view class="stat-number">{{ userStats.fans }}</view>
        <view class="stat-label">粉丝</view>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item" @click="navigateTo('/pages/my/likes')">
        <view class="stat-number">{{ userStats.likes }}</view>
        <view class="stat-label">获赞</view>
      </view>
    </view>
    
    <!-- 我的内容选项卡 -->
    <view class="content-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
      </view>
      <view class="tab-line" :style="tabLineStyle"></view>
    </view>
    
    <!-- 内容区域 -->
    <swiper class="content-swiper" :current="currentTab" @change="onSwiperChange" :style="{ height: contentHeight + 'px' }">
      <!-- 我的发布 -->
      <swiper-item>
        <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(0)" refresher-enabled :refresher-triggered="refreshing[0]" @refresherrefresh="onRefresh(0)">
          <view v-if="publishList.length > 0" class="content-list">
            <view class="content-item" v-for="(item, index) in publishList" :key="index" @click="viewDetail(item)">
              <view class="content-top">
                <view class="content-title">{{ item.title }}</view>
                <view class="content-time">{{ item.time }}</view>
              </view>
              <view class="content-desc">{{ item.desc }}</view>
              <view class="content-images" v-if="item.images && item.images.length > 0">
                <image 
                  class="content-image" 
                  v-for="(img, imgIndex) in item.images.slice(0, 3)" 
                  :key="imgIndex" 
                  :src="img" 
                  mode="aspectFill"
                ></image>
                <view class="image-more" v-if="item.images.length > 3">+{{ item.images.length - 3 }}</view>
              </view>
              <view class="content-footer">
                <view class="content-location" v-if="item.location">
                  <image src="/static/images/tabbar/位置.png" class="location-icon"></image>
                  <text class="location-text">{{ item.location }}</text>
                </view>
                <view class="content-stats">
                  <view class="stat">
                    <image src="/static/images/tabbar/浏览.png" class="stat-icon"></image>
                    <text>{{ item.views }}</text>
                  </view>
                  <view class="stat">
                    <image src="/static/images/tabbar/评论.png" class="stat-icon"></image>
                    <text>{{ item.comments }}</text>
                  </view>
                  <view class="stat">
                    <image src="/static/images/tabbar/点赞.png" class="stat-icon"></image>
                    <text>{{ item.likes }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-view">
            <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
            <view class="empty-text">暂无发布内容</view>
          </view>
          <view v-if="publishList.length > 0 && !hasMore[0]" class="list-bottom">没有更多了</view>
        </scroll-view>
      </swiper-item>
      
      <!-- 点赞收藏 -->
      <swiper-item>
        <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(1)" refresher-enabled :refresher-triggered="refreshing[1]" @refresherrefresh="onRefresh(1)">
          <view v-if="likesList.length > 0" class="content-list">
            <view class="content-item" v-for="(item, index) in likesList" :key="index" @click="viewDetail(item)">
              <view class="content-top">
                <view class="content-title">{{ item.title }}</view>
                <view class="content-time">{{ item.time }}</view>
              </view>
              <view class="content-desc">{{ item.desc }}</view>
              <view class="content-images" v-if="item.images && item.images.length > 0">
                <image 
                  class="content-image" 
                  v-for="(img, imgIndex) in item.images.slice(0, 3)" 
                  :key="imgIndex" 
                  :src="img" 
                  mode="aspectFill"
                ></image>
                <view class="image-more" v-if="item.images.length > 3">+{{ item.images.length - 3 }}</view>
              </view>
              <view class="content-footer">
                <view class="content-author">
                  <image :src="item.authorAvatar" class="author-avatar"></image>
                  <text class="author-name">{{ item.authorName }}</text>
                </view>
                <view class="content-stats">
                  <view class="stat">
                    <image src="/static/images/tabbar/浏览.png" class="stat-icon"></image>
                    <text>{{ item.views }}</text>
                  </view>
                  <view class="stat">
                    <image src="/static/images/tabbar/评论.png" class="stat-icon"></image>
                    <text>{{ item.comments }}</text>
                  </view>
                  <view class="stat">
                    <image src="/static/images/tabbar/点赞.png" class="stat-icon"></image>
                    <text>{{ item.likes }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-view">
            <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
            <view class="empty-text">暂无点赞内容</view>
          </view>
          <view v-if="likesList.length > 0 && !hasMore[1]" class="list-bottom">没有更多了</view>
        </scroll-view>
      </swiper-item>
      
      <!-- 评论 -->
      <swiper-item>
        <scroll-view scroll-y class="tab-scroll" @scrolltolower="loadMore(2)" refresher-enabled :refresher-triggered="refreshing[2]" @refresherrefresh="onRefresh(2)">
          <view v-if="commentsList.length > 0" class="comment-list">
            <view class="comment-item" v-for="(item, index) in commentsList" :key="index" @click="viewComment(item)">
              <view class="comment-content">{{ item.content }}</view>
              <view class="comment-target">
                <text class="comment-target-title">回复：{{ item.targetTitle }}</text>
              </view>
              <view class="comment-footer">
                <text class="comment-time">{{ item.time }}</text>
                <view class="comment-likes">
                  <image src="/static/images/tabbar/点赞.png" class="like-icon"></image>
                  <text class="like-count">{{ item.likes }}</text>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-view">
            <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
            <view class="empty-text">暂无评论内容</view>
          </view>
          <view v-if="commentsList.length > 0 && !hasMore[2]" class="list-bottom">没有更多了</view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import { smartNavigate } from '@/utils/navigation.js';
import UserProfileCard from '@/components/UserProfileCard.vue';

export default {
  components: {
    UserProfileCard
  },
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      contentHeight: 500, // 默认高度，将在onReady中计算
      tabs: [
        { name: '发布' },
        { name: '点赞' },
        { name: '评论' }
      ],
      currentTab: 0,
      userInfo: {
        avatar: '',
        nickname: '',
        signature: ''
      },
      userStats: {
        follows: 25,
        fans: 108,
        likes: 356
      },
      publishList: [],
      likesList: [],
      commentsList: [],
      page: [1, 1, 1], // 三个列表的当前页码
      hasMore: [true, true, true], // 是否有更多数据
      refreshing: [false, false, false], // 是否在刷新中
    }
  },
  computed: {
    tabLineStyle() {
      return {
        transform: `translateX(${this.currentTab * (100 / this.tabs.length)}%)`,
        width: `${100 / this.tabs.length}%`
      }
    }
  },
  onLoad() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
    
    // 获取用户信息
    this.getUserInfo();
    
    // 初始加载数据
    this.loadPublishList();
  },
  onReady() {
    // 计算内容区域高度
    this.calcContentHeight();
  },
  methods: {
    // 计算内容区域高度
    calcContentHeight() {
      const query = uni.createSelectorQuery().in(this);
      query.select('.content-tabs').boundingClientRect(data => {
        const tabsTop = data.top;
        const tabsHeight = data.height;
        const windowHeight = uni.getSystemInfoSync().windowHeight;
        this.contentHeight = windowHeight - tabsTop - tabsHeight;
      }).exec();
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 跳转到设置页面
    navigateToSettings() {
      smartNavigate('/pages/my/settings').catch(err => {
        console.error('跳转到设置页面失败:', err);
      });
    },
    
    // 页面跳转
    navigateTo(url) {
      smartNavigate(url).catch(err => {
        console.error('页面跳转失败:', err);
      });
    },
    
    // 编辑个人资料
    editProfile() {
      this.navigateTo('/pages/my/profile-edit');
    },
    
    // 前往数据分析页面
    goToDataAnalysis() {
      this.navigateTo('/pages/user-center/data-analysis');
    },
    
    // 切换选项卡
    switchTab(index) {
      if (this.currentTab === index) return;
      this.currentTab = index;
      
      // 加载对应选项卡的数据
      switch (index) {
        case 0:
          if (this.publishList.length === 0) this.loadPublishList();
          break;
        case 1:
          if (this.likesList.length === 0) this.loadLikesList();
          break;
        case 2:
          if (this.commentsList.length === 0) this.loadCommentsList();
          break;
      }
    },
    
    // 轮播图切换事件
    onSwiperChange(e) {
      this.switchTab(e.detail.current);
    },
    
    // 获取用户信息
    getUserInfo() {
      // 模拟API请求
      setTimeout(() => {
        this.userInfo = {
          avatar: '/static/images/default-avatar.png',
          nickname: '用户_88965',
          signature: '每一天都是新的开始'
        };
      }, 500);
    },
    
    // 加载发布列表
    loadPublishList() {
      // 模拟API请求
      setTimeout(() => {
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `publish_${this.page[0]}_${i}`,
          title: `发布内容标题 ${this.page[0]}_${i}`,
          desc: '这是一段发布内容的描述，可能包含多行文本内容...',
          time: '2023-10-15',
          images: [
            '/static/images/service1.jpg',
            '/static/images/service2.jpg',
            '/static/images/service3.jpg'
          ].slice(0, Math.floor(Math.random() * 4) + 1),
          location: '磁县',
          views: Math.floor(Math.random() * 1000),
          comments: Math.floor(Math.random() * 30),
          likes: Math.floor(Math.random() * 50)
        }));
        
        if (this.page[0] === 1) {
          this.publishList = mockData;
        } else {
          this.publishList = [...this.publishList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[0] = this.page[0] < 3;
        
        // 关闭刷新状态
        this.refreshing[0] = false;
      }, 600);
    },
    
    // 加载点赞列表
    loadLikesList() {
      // 模拟API请求
      setTimeout(() => {
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `likes_${this.page[1]}_${i}`,
          title: `点赞内容标题 ${this.page[1]}_${i}`,
          desc: '这是一段被点赞内容的描述，可能包含多行文本内容...',
          time: '2023-10-15',
          images: [
            '/static/images/service1.jpg',
            '/static/images/service2.jpg',
            '/static/images/service3.jpg'
          ].slice(0, Math.floor(Math.random() * 4)),
          authorName: `作者${i + 1}`,
          authorAvatar: '/static/images/default-avatar.png',
          views: Math.floor(Math.random() * 1000),
          comments: Math.floor(Math.random() * 30),
          likes: Math.floor(Math.random() * 50)
        }));
        
        if (this.page[1] === 1) {
          this.likesList = mockData;
        } else {
          this.likesList = [...this.likesList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[1] = this.page[1] < 3;
        
        // 关闭刷新状态
        this.refreshing[1] = false;
      }, 600);
    },
    
    // 加载评论列表
    loadCommentsList() {
      // 模拟API请求
      setTimeout(() => {
        const mockData = Array.from({ length: 5 }, (_, i) => ({
          id: `comment_${this.page[2]}_${i}`,
          content: `这是我的评论内容 ${this.page[2]}_${i}，可能包含一些文字描述...`,
          targetTitle: `被评论的内容标题 ${i}`,
          time: '2023-10-15',
          likes: Math.floor(Math.random() * 20)
        }));
        
        if (this.page[2] === 1) {
          this.commentsList = mockData;
        } else {
          this.commentsList = [...this.commentsList, ...mockData];
        }
        
        // 模拟是否还有更多数据
        this.hasMore[2] = this.page[2] < 3;
        
        // 关闭刷新状态
        this.refreshing[2] = false;
      }, 600);
    },
    
    // 加载更多数据
    loadMore(tabIndex) {
      if (!this.hasMore[tabIndex] || this.refreshing[tabIndex]) return;
      
      this.page[tabIndex]++;
      
      switch (tabIndex) {
        case 0:
          this.loadPublishList();
          break;
        case 1:
          this.loadLikesList();
          break;
        case 2:
          this.loadCommentsList();
          break;
      }
    },
    
    // 下拉刷新
    onRefresh(tabIndex) {
      this.refreshing[tabIndex] = true;
      this.page[tabIndex] = 1;
      
      switch (tabIndex) {
        case 0:
          this.loadPublishList();
          break;
        case 1:
          this.loadLikesList();
          break;
        case 2:
          this.loadCommentsList();
          break;
      }
    },
    
    // 查看详情
    viewDetail(item) {
      this.navigateTo(`/pages/publish/info-detail?id=${item.id}`);
    },
    
    // 查看评论
    viewComment(item) {
      this.navigateTo(`/pages/publish/info-detail?id=${item.id}&showComment=1`);
    }
  }
}
</script>

<style>
.profile-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.setting-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 个人资料卡片 */
.profile-card {
  background-color: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
}

.profile-header {
  padding: 30rpx;
  display: flex;
  align-items: center;
}

.profile-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  margin-right: 30rpx;
  border: 2rpx solid #f0f0f0;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.profile-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

.edit-btn {
  background-color: #0066FF;
  color: #fff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  height: 60rpx;
  line-height: 40rpx;
  margin: 0;
}

.profile-stats {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx 0;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 10rpx 0;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.stat-divider {
  width: 2rpx;
  background-color: #f0f0f0;
}

/* 内容选项卡 */
.content-tabs {
  background-color: #fff;
  display: flex;
  position: relative;
  margin: 0 30rpx 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tab-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  transition: all 0.3s;
}

.tab-item.active .tab-text {
  color: #0066FF;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  height: 4rpx;
  background-color: #0066FF;
  border-radius: 2rpx;
  transition: all 0.3s;
}

/* 内容区域 */
.content-swiper {
  width: 100%;
}

.tab-scroll {
  height: 100%;
}

/* 列表样式 */
.content-list {
  padding: 20rpx 30rpx;
}

.content-item {
  background-color: #fff;
  margin-bottom: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.content-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.content-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.content-time {
  font-size: 24rpx;
  color: #999;
}

.content-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.content-images {
  display: flex;
  margin-bottom: 20rpx;
  position: relative;
}

.content-image {
  width: 200rpx;
  height: 200rpx;
  margin-right: 10rpx;
  border-radius: 10rpx;
}

.image-more {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  padding: 4rpx 14rpx;
  border-radius: 20rpx;
}

.content-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-location {
  display: flex;
  align-items: center;
}

.location-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.location-text {
  font-size: 24rpx;
  color: #999;
}

.content-author {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 44rpx;
  height: 44rpx;
  border-radius: 22rpx;
  margin-right: 10rpx;
}

.author-name {
  font-size: 24rpx;
  color: #666;
}

.content-stats {
  display: flex;
}

.stat {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.stat-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
  opacity: 0.7;
}

.stat text {
  font-size: 24rpx;
  color: #999;
}

/* 评论列表 */
.comment-list {
  padding: 20rpx 30rpx;
}

.comment-item {
  background-color: #fff;
  margin-bottom: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.comment-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.comment-target {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.comment-target-title {
  font-size: 26rpx;
  color: #666;
}

.comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-likes {
  display: flex;
  align-items: center;
}

.like-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.like-count {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-view {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 30rpx 0;
}
</style> 