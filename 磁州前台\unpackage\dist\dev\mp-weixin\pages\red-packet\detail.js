"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_share = require("../../utils/share.js");
const utils_redPacket = require("../../utils/redPacket.js");
const _sfc_main = {
  data() {
    return {
      redPacket: null,
      isExpired: false,
      hasGrabbed: false
    };
  },
  onLoad(options) {
    this.loadRedPacketDetail(options.id);
  },
  methods: {
    async loadRedPacketDetail(id) {
      try {
        const res = await common_vendor.index.request({
          url: `/api/red-packets/${id}`,
          method: "GET"
        });
        if (res.data.code === 0) {
          this.redPacket = res.data.data;
          this.isExpired = /* @__PURE__ */ new Date() > new Date(this.redPacket.expireTime);
          this.hasGrabbed = this.redPacket.records.some((record) => record.userId === this.userId);
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "error"
        });
      }
    },
    getTypeText(type) {
      const typeMap = {
        [utils_redPacket.RED_PACKET_TYPE.NORMAL]: "普通红包",
        [utils_redPacket.RED_PACKET_TYPE.LUCKY]: "拼手气红包",
        [utils_redPacket.RED_PACKET_TYPE.FIXED]: "固定金额红包"
      };
      return typeMap[type] || "红包";
    },
    getStatusText(status) {
      const statusMap = {
        0: "进行中",
        1: "已领完",
        2: "已过期"
      };
      return statusMap[status] || "未知状态";
    },
    async handleShare() {
      try {
        await utils_share.showShareMenu({
          type: "redPacket",
          data: this.redPacket
        });
        common_vendor.index.showToast({
          title: "分享成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "分享失败",
          icon: "error"
        });
      }
    },
    async handleGrab() {
      if (this.isExpired || this.hasGrabbed)
        return;
      try {
        const res = await common_vendor.index.request({
          url: `/api/red-packets/${this.redPacket.id}/grab`,
          method: "POST"
        });
        if (res.data.code === 0) {
          common_vendor.index.showToast({
            title: "抢到红包啦！",
            icon: "success"
          });
          this.hasGrabbed = true;
          this.loadRedPacketDetail(this.redPacket.id);
        } else {
          common_vendor.index.showToast({
            title: res.data.message || "抢红包失败",
            icon: "error"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "抢红包失败",
          icon: "error"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.redPacket.title),
    b: common_vendor.t($data.redPacket.amount),
    c: common_vendor.t($options.getStatusText($data.redPacket.status)),
    d: common_vendor.t($options.getTypeText($data.redPacket.type)),
    e: common_vendor.t(_ctx.formatTime($data.redPacket.createTime)),
    f: common_vendor.t(_ctx.formatTime($data.redPacket.expireTime)),
    g: $data.redPacket.records && $data.redPacket.records.length > 0
  }, $data.redPacket.records && $data.redPacket.records.length > 0 ? {
    h: common_vendor.f($data.redPacket.records, (record, k0, i0) => {
      return {
        a: record.user.avatar,
        b: common_vendor.t(record.user.nickname),
        c: common_vendor.t(_ctx.formatTime(record.createTime)),
        d: common_vendor.t(record.amount),
        e: record.id
      };
    })
  } : {}, {
    i: common_vendor.o((...args) => $options.handleShare && $options.handleShare(...args)),
    j: !$data.isExpired && !$data.hasGrabbed
  }, !$data.isExpired && !$data.hasGrabbed ? {
    k: common_vendor.o((...args) => $options.handleGrab && $options.handleGrab(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/red-packet/detail.js.map
