
.detail-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
}
.detail-banner {
		width: 100%;
		height: 400rpx;
}
.detail-content {
		margin: -50rpx 20rpx 0;
		padding: 30rpx;
		background: #fff;
		border-radius: 20rpx;
		position: relative;
		z-index: 1;
}
.detail-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 30rpx;
}
.detail-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		flex: 1;
		margin-right: 20rpx;
}
.detail-status {
		padding: 4rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
}
.status-active {
		background-color: #e6f7ff;
		color: #0052CC;
}
.status-ended {
		background-color: #f5f5f5;
		color: #999;
}
.detail-meta {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
		margin-bottom: 30rpx;
}
.meta-item {
		display: flex;
		align-items: center;
}
.meta-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 12rpx;
}
.meta-text {
		font-size: 28rpx;
		color: #666;
}
.detail-section {
		margin-bottom: 30rpx;
}
.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16rpx;
		display: block;
}
.section-content {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
}
.rule-list {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
}
.rule-item {
		font-size: 28rpx;
		color: #666;
		line-height: 1.5;
}
.contact-info {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
}
.contact-item {
		display: flex;
		align-items: center;
}
.contact-label {
		font-size: 28rpx;
		color: #333;
		width: 140rpx;
}
.contact-value {
		font-size: 28rpx;
		color: #666;
}
.action-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background: #fff;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.share-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 60rpx;
		height: 60rpx;
		background: #f5f5f5;
		border: none;
		border-radius: 50%;
		margin-right: 20rpx;
}
.btn-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 0;
}
.join-btn {
		flex: 1;
		height: 80rpx;
		background: linear-gradient(to right, #0052CC, #2196F3);
		color: #fff;
		border: none;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: bold;
}
	
	/* 继承首页的卡片样式 */
.card-section {
		box-shadow: 0 8rpx 24rpx rgba(0,82,204,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
}
