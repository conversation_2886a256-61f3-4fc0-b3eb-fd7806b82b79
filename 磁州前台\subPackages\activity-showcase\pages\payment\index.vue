<template>
  <view class="payment-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">支付订单</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 支付金额 -->
      <view class="payment-amount">
        <view class="amount-label">支付金额</view>
        <view class="amount-value">¥{{ totalAmount }}</view>
        <view class="amount-desc">{{ orderInfo.itemCount }}件商品</view>
      </view>

      <!-- 订单信息 -->
      <view class="order-info">
        <view class="section-header">
          <text class="section-title">订单信息</text>
        </view>
        
        <view class="order-details">
          <view class="detail-item">
            <text class="detail-label">订单号</text>
            <text class="detail-value">{{ orderInfo.orderNo }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">下单时间</text>
            <text class="detail-value">{{ orderInfo.createTime }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">商品金额</text>
            <text class="detail-value">¥{{ orderInfo.productAmount }}</text>
          </view>
          <view class="detail-item" v-if="orderInfo.discountAmount > 0">
            <text class="detail-label">优惠金额</text>
            <text class="detail-value discount">-¥{{ orderInfo.discountAmount }}</text>
          </view>
          <view class="detail-item" v-if="orderInfo.shippingFee > 0">
            <text class="detail-label">运费</text>
            <text class="detail-value">¥{{ orderInfo.shippingFee }}</text>
          </view>
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="product-list">
        <view class="section-header">
          <text class="section-title">商品清单</text>
        </view>
        
        <view class="products">
          <view class="product-item" v-for="(product, index) in orderInfo.products" :key="index">
            <image class="product-image" :src="product.image" mode="aspectFill"></image>
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <text class="product-spec" v-if="product.spec">{{ product.spec }}</text>
              <view class="product-price-qty">
                <text class="product-price">¥{{ product.price }}</text>
                <text class="product-qty">×{{ product.quantity }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 支付方式 -->
      <view class="payment-methods">
        <view class="section-header">
          <text class="section-title">支付方式</text>
        </view>
        
        <view class="methods-list">
          <view 
            class="method-item" 
            v-for="(method, index) in paymentMethods" 
            :key="index"
            :class="{ active: selectedMethod === method.id }"
            @click="selectPaymentMethod(method.id)"
          >
            <view class="method-icon">
              <image class="method-logo" :src="method.icon" mode="aspectFit"></image>
            </view>
            <view class="method-info">
              <text class="method-name">{{ method.name }}</text>
              <text class="method-desc">{{ method.description }}</text>
            </view>
            <view class="method-radio">
              <view class="radio-btn" :class="{ checked: selectedMethod === method.id }">
                <view class="radio-dot" v-if="selectedMethod === method.id"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 优惠券 -->
      <view class="coupon-section" v-if="availableCoupons.length > 0">
        <view class="section-header">
          <text class="section-title">优惠券</text>
          <view class="coupon-toggle" @click="showCouponModal">
            <text class="toggle-text">{{ selectedCoupon ? `已选择 -¥${selectedCoupon.amount}` : '选择优惠券' }}</text>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </view>
        </view>
      </view>

      <!-- 支付协议 -->
      <view class="payment-agreement">
        <view class="agreement-item" @click="toggleAgreement">
          <view class="checkbox" :class="{ checked: agreedToTerms }">
            <svg v-if="agreedToTerms" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
          </view>
          <text class="agreement-text">我已阅读并同意</text>
          <text class="agreement-link" @click.stop="viewTerms">《支付协议》</text>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 底部支付按钮 -->
    <view class="payment-footer">
      <view class="payment-summary">
        <text class="summary-label">实付金额</text>
        <text class="summary-amount">¥{{ finalAmount }}</text>
      </view>
      <view class="payment-btn" :class="{ disabled: !canPay }" @click="handlePayment">
        <text class="btn-text">{{ paymentBtnText }}</text>
      </view>
    </view>

    <!-- 优惠券选择弹窗 -->
    <view class="coupon-modal" v-if="showCoupon" @click="hideCouponModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择优惠券</text>
          <view class="close-btn" @click="hideCouponModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        <view class="modal-body">
          <view class="coupon-list">
            <view 
              class="coupon-item" 
              v-for="(coupon, index) in availableCoupons" 
              :key="index"
              :class="{ selected: selectedCoupon && selectedCoupon.id === coupon.id }"
              @click="selectCoupon(coupon)"
            >
              <view class="coupon-left">
                <text class="coupon-amount">{{ coupon.amount }}</text>
                <text class="coupon-unit">元</text>
              </view>
              <view class="coupon-content">
                <text class="coupon-title">{{ coupon.title }}</text>
                <text class="coupon-desc">{{ coupon.description }}</text>
                <text class="coupon-expire">有效期至：{{ coupon.expireDate }}</text>
              </view>
              <view class="coupon-check" v-if="selectedCoupon && selectedCoupon.id === coupon.id">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </view>
            </view>
            
            <view class="no-coupon-item" :class="{ selected: !selectedCoupon }" @click="selectCoupon(null)">
              <text class="no-coupon-text">不使用优惠券</text>
              <view class="coupon-check" v-if="!selectedCoupon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </view>
            </view>
          </view>
        </view>
        <view class="modal-actions">
          <view class="action-btn confirm" @click="confirmCoupon">
            <text class="btn-text">确定</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付密码弹窗 -->
    <view class="password-modal" v-if="showPassword" @click="hidePasswordModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">输入支付密码</text>
        </view>
        <view class="modal-body">
          <view class="password-amount">
            <text class="amount-text">支付金额：¥{{ finalAmount }}</text>
          </view>
          <view class="password-input">
            <view 
              class="password-dot" 
              v-for="i in 6" 
              :key="i"
              :class="{ filled: paymentPassword.length >= i }"
            >
              <view class="dot" v-if="paymentPassword.length >= i"></view>
            </view>
          </view>
          <view class="password-keyboard">
            <view class="keyboard-row" v-for="(row, rowIndex) in keyboard" :key="rowIndex">
              <view 
                class="keyboard-key" 
                v-for="(key, keyIndex) in row" 
                :key="keyIndex"
                @click="inputPassword(key)"
              >
                <text class="key-text" v-if="key !== 'delete'">{{ key }}</text>
                <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 4H8l-7 8 7 8h13a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2z"></path>
                  <line x1="18" y1="9" x2="12" y2="15"></line>
                  <line x1="12" y1="9" x2="18" y2="15"></line>
                </svg>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const selectedMethod = ref('wechat')
const selectedCoupon = ref(null)
const agreedToTerms = ref(false)
const showCoupon = ref(false)
const showPassword = ref(false)
const paymentPassword = ref('')

// 订单信息
const orderInfo = ref({
  orderNo: 'ORDER202401200001',
  createTime: '2024-01-20 14:30:25',
  itemCount: 2,
  productAmount: 299.00,
  discountAmount: 20.00,
  shippingFee: 0.00,
  products: [
    {
      name: '精选优质商品A',
      spec: '红色 L码',
      price: 199.00,
      quantity: 1,
      image: '/static/images/products/product1.jpg'
    },
    {
      name: '热销商品B',
      spec: '蓝色 M码',
      price: 100.00,
      quantity: 1,
      image: '/static/images/products/product2.jpg'
    }
  ]
})

// 支付方式
const paymentMethods = ref([
  {
    id: 'wechat',
    name: '微信支付',
    description: '推荐使用微信支付',
    icon: '/static/images/payment/wechat.png'
  },
  {
    id: 'alipay',
    name: '支付宝',
    description: '支持花呗分期付款',
    icon: '/static/images/payment/alipay.png'
  },
  {
    id: 'balance',
    name: '余额支付',
    description: '当前余额：¥1,580.00',
    icon: '/static/images/payment/balance.png'
  }
])

// 可用优惠券
const availableCoupons = ref([
  {
    id: 1,
    title: '满减优惠券',
    description: '满200减20',
    amount: 20,
    expireDate: '2024-02-20'
  },
  {
    id: 2,
    title: '新用户专享',
    description: '满100减15',
    amount: 15,
    expireDate: '2024-02-15'
  }
])

// 数字键盘
const keyboard = ref([
  ['1', '2', '3'],
  ['4', '5', '6'],
  ['7', '8', '9'],
  ['', '0', 'delete']
])

// 计算属性
const totalAmount = computed(() => {
  return orderInfo.value.productAmount + orderInfo.value.shippingFee
})

const finalAmount = computed(() => {
  let amount = totalAmount.value - orderInfo.value.discountAmount
  if (selectedCoupon.value) {
    amount -= selectedCoupon.value.amount
  }
  return Math.max(amount, 0).toFixed(2)
})

const canPay = computed(() => {
  return selectedMethod.value && agreedToTerms.value
})

const paymentBtnText = computed(() => {
  if (!selectedMethod.value) return '请选择支付方式'
  if (!agreedToTerms.value) return '请同意支付协议'
  return `立即支付 ¥${finalAmount.value}`
})

// 页面加载
onMounted(() => {
  console.log('支付页面加载')
  loadPaymentData()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function selectPaymentMethod(methodId) {
  selectedMethod.value = methodId
}

function showCouponModal() {
  showCoupon.value = true
}

function hideCouponModal() {
  showCoupon.value = false
}

function selectCoupon(coupon) {
  selectedCoupon.value = coupon
}

function confirmCoupon() {
  hideCouponModal()
}

function toggleAgreement() {
  agreedToTerms.value = !agreedToTerms.value
}

function viewTerms() {
  uni.navigateTo({
    url: '/pages/legal/payment-terms'
  })
}

function handlePayment() {
  if (!canPay.value) return
  
  if (selectedMethod.value === 'balance') {
    // 余额支付需要输入密码
    showPassword.value = true
  } else {
    // 其他支付方式直接调起支付
    processPayment()
  }
}

function hidePasswordModal() {
  showPassword.value = false
  paymentPassword.value = ''
}

function inputPassword(key) {
  if (key === 'delete') {
    paymentPassword.value = paymentPassword.value.slice(0, -1)
  } else if (key && paymentPassword.value.length < 6) {
    paymentPassword.value += key
    
    // 密码输入完成
    if (paymentPassword.value.length === 6) {
      setTimeout(() => {
        processPayment()
        hidePasswordModal()
      }, 300)
    }
  }
}

function processPayment() {
  uni.showLoading({
    title: '支付中...'
  })
  
  // 模拟支付处理
  setTimeout(() => {
    uni.hideLoading()
    
    // 跳转到支付结果页
    uni.redirectTo({
      url: `/subPackages/activity-showcase/pages/payment/result/index?success=true&amount=${finalAmount.value}&orderNo=${orderInfo.value.orderNo}`
    })
  }, 2000)
}

function loadPaymentData() {
  // 模拟加载支付数据
  setTimeout(() => {
    console.log('支付数据加载完成')
  }, 500)
}
</script>

<style scoped>
/* 支付页面样式开始 */
.payment-container {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 80px;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
  border-bottom: 1px solid #f0f0f0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.back-btn svg {
  color: #333;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
}

/* 支付金额样式 */
.payment-amount {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px 20px;
  text-align: center;
  color: white;
}

.amount-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.amount-value {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 4px;
}

.amount-desc {
  font-size: 14px;
  opacity: 0.8;
}

/* 区块样式 */
.order-info, .product-list, .payment-methods, .coupon-section {
  background: white;
  margin: 8px 16px;
  border-radius: 12px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 订单信息样式 */
.order-details {
  padding: 0 20px 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f8f9fa;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 14px;
  color: #666;
}

.detail-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.detail-value.discount {
  color: #E91E63;
}

/* 商品列表样式 */
.products {
  padding: 0 20px 16px;
}

.product-item {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f8f9fa;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
}

.product-info {
  flex: 1;
}

.product-name {
  display: block;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
}

.product-spec {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.product-price-qty {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-price {
  font-size: 16px;
  color: #E91E63;
  font-weight: 600;
}

.product-qty {
  font-size: 14px;
  color: #666;
}

/* 支付方式样式 */
.methods-list {
  padding: 0 20px 16px;
}

.method-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f8f9fa;
  transition: all 0.3s ease;
}

.method-item:last-child {
  border-bottom: none;
}

.method-item.active {
  background: rgba(102, 126, 234, 0.05);
}

.method-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.method-logo {
  width: 32px;
  height: 32px;
}

.method-info {
  flex: 1;
}

.method-name {
  display: block;
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 2px;
}

.method-desc {
  display: block;
  font-size: 12px;
  color: #666;
}

.method-radio {
  width: 20px;
  height: 20px;
}

.radio-btn {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio-btn.checked {
  border-color: #667eea;
}

.radio-dot {
  width: 10px;
  height: 10px;
  background: #667eea;
  border-radius: 5px;
}

/* 优惠券样式 */
.coupon-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toggle-text {
  font-size: 14px;
  color: #667eea;
}

.coupon-toggle svg {
  color: #667eea;
}

/* 支付协议样式 */
.payment-agreement {
  background: white;
  margin: 8px 16px;
  padding: 16px 20px;
  border-radius: 12px;
}

.agreement-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #ddd;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #667eea;
  border-color: #667eea;
}

.checkbox svg {
  color: white;
}

.agreement-text {
  font-size: 14px;
  color: #666;
}

.agreement-link {
  font-size: 14px;
  color: #667eea;
  text-decoration: underline;
}

/* 底部支付按钮样式 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  z-index: 1000;
}

.payment-summary {
  flex: 1;
  text-align: right;
  margin-right: 16px;
}

.summary-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.summary-amount {
  font-size: 18px;
  color: #E91E63;
  font-weight: 700;
}

.payment-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  min-width: 140px;
  text-align: center;
  transition: all 0.3s ease;
}

.payment-btn.disabled {
  background: #ccc;
}

.btn-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

/* 弹窗样式 */
.coupon-modal, .password-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.close-btn svg {
  color: #666;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 优惠券列表样式 */
.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.coupon-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  color: white;
  position: relative;
  transition: all 0.3s ease;
}

.coupon-item.selected {
  transform: scale(1.02);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.coupon-left {
  display: flex;
  align-items: baseline;
  gap: 2px;
  margin-right: 16px;
  min-width: 60px;
}

.coupon-amount {
  font-size: 24px;
  font-weight: 700;
}

.coupon-unit {
  font-size: 14px;
}

.coupon-content {
  flex: 1;
}

.coupon-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.coupon-desc {
  display: block;
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.coupon-expire {
  display: block;
  font-size: 12px;
  opacity: 0.8;
}

.coupon-check {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.coupon-check svg {
  color: white;
}

.no-coupon-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.no-coupon-item.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.no-coupon-text {
  font-size: 16px;
  color: #333;
}

.modal-actions {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
}

.action-btn.confirm {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-btn .btn-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

/* 支付密码样式 */
.password-amount {
  text-align: center;
  margin-bottom: 24px;
}

.amount-text {
  font-size: 16px;
  color: #333;
}

.password-input {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 32px;
}

.password-dot {
  width: 40px;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.password-dot.filled {
  border-color: #667eea;
}

.dot {
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 6px;
}

.password-keyboard {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.keyboard-row {
  display: flex;
  gap: 12px;
}

.keyboard-key {
  flex: 1;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.keyboard-key:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.key-text {
  font-size: 20px;
  color: #333;
  font-weight: 500;
}

.keyboard-key svg {
  color: #666;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 支付页面样式结束 */
</style>
