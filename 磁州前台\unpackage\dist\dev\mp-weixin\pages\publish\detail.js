"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_serviceFormConfig = require("../../utils/service-form-config.js");
const utils_contentCheck = require("../../utils/contentCheck.js");
if (!Math) {
  ConfigurablePremiumActions();
}
const ConfigurablePremiumActions = () => "../../components/premium/ConfigurablePremiumActions.js";
const _sfc_main = {
  __name: "detail",
  setup(__props) {
    const categoryType = common_vendor.ref("");
    const categoryName = common_vendor.ref("");
    const formConfig = common_vendor.ref({});
    common_vendor.ref(false);
    const serviceType = common_vendor.ref("");
    const serviceTypeName = common_vendor.ref("");
    const showRedPacketDatePicker = common_vendor.ref(false);
    const hasSelectedPublishMethod = common_vendor.ref(false);
    const publishData = common_vendor.ref({
      id: "publish_" + Date.now(),
      title: "发布信息",
      description: "发布您的信息到平台"
    });
    const formData = common_vendor.reactive({
      name: "",
      gender: "",
      age: "",
      workArea: "",
      education: "",
      experience: "",
      jobPosition: "",
      salary: "",
      introduction: "",
      images: [],
      tags: [],
      contact: "",
      phone: "",
      code: "",
      wechat: "",
      petType: "",
      petBreed: "",
      purpose: "",
      petGender: "",
      petAge: "",
      petPrice: "",
      petVaccine: "",
      petDescription: "",
      petFeatures: [],
      companyName: "",
      workAddress: "",
      jobType: "",
      count: "",
      jobDescription: "",
      welfare: [],
      agree: false,
      redPacketEnabled: false,
      redPacketAmount: "",
      redPacketCount: "",
      redPacketType: "random",
      redPacketConditions: [],
      redPacketExpiry: "",
      redPacketDesc: ""
    });
    const navigateBack = () => common_vendor.index.navigateBack();
    const selectGender = (fieldName = "gender", value) => {
      formData[fieldName] = value;
    };
    const initFormConfig = () => {
      switch (categoryType.value) {
        case "home_service":
          if (serviceType.value) {
            const formKey = `${serviceType.value}Form`;
            formConfig.value = utils_serviceFormConfig.serviceFormConfig[formKey] || utils_serviceFormConfig.otherHomeServiceForm;
          } else {
            formConfig.value = utils_serviceFormConfig.otherHomeServiceForm;
          }
          break;
        case "find_service":
          formConfig.value = utils_serviceFormConfig.findServiceForm;
          break;
        case "hire":
          formConfig.value = utils_serviceFormConfig.recruitmentForm;
          break;
        case "job_wanted":
          formConfig.value = utils_serviceFormConfig.jobWantedForm;
          break;
        case "house_rent":
          formConfig.value = utils_serviceFormConfig.houseRentForm;
          break;
        case "house_sell":
          formConfig.value = utils_serviceFormConfig.houseSellForm;
          break;
        case "used_car":
          formConfig.value = utils_serviceFormConfig.usedCarForm;
          break;
        case "pet":
          formConfig.value = utils_serviceFormConfig.petForm;
          break;
        case "merchant_activity":
          formConfig.value = utils_serviceFormConfig.merchantActivityForm;
          break;
        case "car_service":
          formConfig.value = utils_serviceFormConfig.carServiceForm;
          break;
        case "second_hand":
          formConfig.value = utils_serviceFormConfig.secondHandForm;
          break;
        case "carpool":
          formConfig.value = utils_serviceFormConfig.carpoolForm;
          break;
        case "business_transfer":
          formConfig.value = utils_serviceFormConfig.businessTransferForm;
          break;
        case "education":
          formConfig.value = utils_serviceFormConfig.educationForm;
          break;
        case "dating":
          formConfig.value = utils_serviceFormConfig.datingForm;
          break;
        default:
          formConfig.value = utils_serviceFormConfig.defaultForm;
          break;
      }
    };
    const getPickerPlaceholder = (field) => {
      return formData[field.name] || field.placeholder || "请选择";
    };
    const shouldShowField = (field) => {
      if (!field.showIf)
        return true;
      const { field: conditionField, value } = field.showIf;
      return formData[conditionField] === value;
    };
    const getLocation = (fieldName) => {
      common_vendor.index.chooseLocation({
        success: (res) => {
          if (res.address)
            formData[fieldName] = res.address;
        }
      });
    };
    const chooseImage = (fieldName) => {
      var _a;
      const fieldConfig = (_a = formConfig.value.sections) == null ? void 0 : _a.flatMap((s) => s.fields).find((f) => f.name === fieldName);
      const maxCount = (fieldConfig == null ? void 0 : fieldConfig.maxCount) || 9;
      const currentImages = formData[fieldName] || [];
      common_vendor.index.chooseImage({
        count: maxCount - currentImages.length,
        success: (res) => {
          formData[fieldName] = [...currentImages, ...res.tempFilePaths];
        }
      });
    };
    const deleteImage = (fieldName, index) => {
      var _a;
      (_a = formData[fieldName]) == null ? void 0 : _a.splice(index, 1);
    };
    const isTagSelected = (tag, fieldName) => {
      var _a;
      return (_a = formData[fieldName]) == null ? void 0 : _a.includes(tag);
    };
    const toggleTag = (tag, fieldName, maxCount = 3) => {
      const tags = formData[fieldName] || [];
      const index = tags.indexOf(tag);
      if (index > -1) {
        tags.splice(index, 1);
      } else if (tags.length < maxCount) {
        tags.push(tag);
      } else {
        common_vendor.index.showToast({ title: `最多选择${maxCount}个标签`, icon: "none" });
      }
      formData[fieldName] = tags;
    };
    const toggleCondition = (condition) => {
      const conditions = formData.redPacketConditions || [];
      const index = conditions.indexOf(condition);
      if (index > -1) {
        conditions.splice(index, 1);
      } else {
        conditions.push(condition);
      }
      formData.redPacketConditions = conditions;
    };
    const showAgreement = (type) => {
      common_vendor.index.navigateTo({ url: `/pages/agreement/${type}` });
    };
    const submitForm = async (isFree = false) => {
      if (!formData.agree) {
        return common_vendor.index.showToast({ title: "请先阅读并同意协议", icon: "none" });
      }
      if (!hasSelectedPublishMethod.value) {
        return common_vendor.index.showToast({ title: "请先选择发布方式（看广告或付费）", icon: "none" });
      }
      for (const section of formConfig.value.sections || []) {
        for (const field of section.fields) {
          if (field.required && !formData[field.name]) {
            return common_vendor.index.showToast({ title: `${field.label}不能为空`, icon: "none" });
          }
          if (field.checkContent && formData[field.name]) {
            const isValid = await utils_contentCheck.checkContent(formData[field.name]);
            if (!isValid) {
              return common_vendor.index.showToast({ title: `"${field.label}"包含违规内容`, icon: "none" });
            }
          }
        }
      }
      common_vendor.index.showLoading({ title: "发布中...", mask: true });
      try {
        await new Promise((resolve) => setTimeout(resolve, 1500));
        const submissionData = { ...formData, id: Date.now().toString(), createTime: (/* @__PURE__ */ new Date()).toISOString() };
        const allData = common_vendor.index.getStorageSync("publishedData") || [];
        common_vendor.index.setStorageSync("publishedData", [...allData, submissionData]);
        common_vendor.index.hideLoading();
        common_vendor.index.redirectTo({ url: `/pages/publish/success?id=${submissionData.id}` });
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({ title: "发布失败，请重试", icon: "none" });
      }
    };
    const handlePublishCompleted = (result) => {
      common_vendor.index.__f__("log", "at pages/publish/detail.vue:514", "发布操作完成:", result);
      hasSelectedPublishMethod.value = true;
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "广告观看完成，可免费发布",
          icon: "success"
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: "付费发布成功",
          icon: "success"
        });
      }
      setTimeout(() => {
        submitForm(result.type === "ad");
      }, 1500);
    };
    const handlePublishCancelled = (result) => {
      common_vendor.index.__f__("log", "at pages/publish/detail.vue:539", "发布操作取消:", result);
      if (result.type === "ad") {
        common_vendor.index.showToast({
          title: "已取消观看广告",
          icon: "none"
        });
      } else if (result.type === "payment") {
        common_vendor.index.showToast({
          title: "已取消支付",
          icon: "none"
        });
      }
    };
    common_vendor.onLoad((options) => {
      categoryType.value = options.type || "";
      categoryName.value = decodeURIComponent(options.name || "");
      serviceType.value = options.serviceType || "";
      serviceTypeName.value = decodeURIComponent(options.serviceTypeName || "");
      if (options.autoShare === "true") {
        common_vendor.index.__f__("log", "at pages/publish/detail.vue:563", "检测到自动分享参数，准备显示分享菜单");
        setTimeout(() => {
          common_vendor.index.showShareMenu({
            withShareTicket: true,
            menus: ["shareAppMessage", "shareTimeline"],
            success: () => {
              common_vendor.index.__f__("log", "at pages/publish/detail.vue:570", "自动显示分享菜单成功");
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/publish/detail.vue:573", "自动显示分享菜单失败", err);
            }
          });
        }, 1e3);
      }
      initFormConfig();
    });
    return (_ctx, _cache) => {
      var _a, _b;
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(navigateBack),
        c: common_vendor.t(serviceTypeName.value ? serviceTypeName.value : categoryName.value),
        d: common_vendor.f(formConfig.value.sections, (section, sectionIndex, i0) => {
          return common_vendor.e({
            a: section.label
          }, section.label ? {
            b: common_vendor.t(section.label)
          } : {}, {
            c: common_vendor.f(section.fields, (field, fieldIndex, i1) => {
              return common_vendor.e({
                a: shouldShowField(field)
              }, shouldShowField(field) ? common_vendor.e({
                b: field.type === "input"
              }, field.type === "input" ? {
                c: common_vendor.t(field.label),
                d: field.required ? 1 : "",
                e: field.inputType || "text",
                f: field.placeholder || "请输入",
                g: formData[field.name],
                h: common_vendor.o(($event) => formData[field.name] = $event.detail.value, fieldIndex)
              } : field.type === "gender" ? {
                j: common_vendor.t(field.label),
                k: field.required ? 1 : "",
                l: formData[field.name] === "男" ? 1 : "",
                m: common_vendor.o(($event) => selectGender(field.name, "男"), fieldIndex),
                n: formData[field.name] === "女" ? 1 : "",
                o: common_vendor.o(($event) => selectGender(field.name, "女"), fieldIndex)
              } : field.type === "picker" ? {
                q: common_vendor.t(field.label),
                r: field.required ? 1 : "",
                s: getPickerPlaceholder(field),
                t: formData[field.name],
                v: common_vendor.o(($event) => formData[field.name] = $event.detail.value, fieldIndex)
              } : field.type === "location" ? {
                x: common_vendor.t(field.label),
                y: field.required ? 1 : "",
                z: field.placeholder || "请输入地址或点击右侧定位",
                A: formData[field.name],
                B: common_vendor.o(($event) => formData[field.name] = $event.detail.value, fieldIndex),
                C: common_assets._imports_3$4,
                D: common_vendor.o(($event) => getLocation(field.name), fieldIndex)
              } : field.type === "textarea" ? {
                F: common_vendor.t(field.label),
                G: field.required ? 1 : "",
                H: field.placeholder || "请输入",
                I: formData[field.name],
                J: common_vendor.o(($event) => formData[field.name] = $event.detail.value, fieldIndex)
              } : field.type === "upload" ? common_vendor.e({
                L: common_vendor.t(field.label),
                M: field.required ? 1 : "",
                N: common_assets._imports_2$7,
                O: common_vendor.o(($event) => chooseImage(field.name), fieldIndex),
                P: common_vendor.f(formData[field.name] || [], (item, index, i2) => {
                  return {
                    a: item,
                    b: common_vendor.o(($event) => deleteImage(field.name, index), index),
                    c: index
                  };
                }),
                Q: field.tip
              }, field.tip ? {
                R: common_vendor.t(field.tip)
              } : {}) : field.type === "tags" ? {
                T: common_vendor.t(field.label),
                U: common_vendor.t(field.maxCount || 3),
                V: common_vendor.f(field.options, (tag, tagIndex, i2) => {
                  return {
                    a: common_vendor.t(tag),
                    b: tagIndex,
                    c: isTagSelected(tag, field.name) ? 1 : "",
                    d: common_vendor.o(($event) => toggleTag(tag, field.name, field.maxCount || 3), tagIndex)
                  };
                })
              } : field.type === "code" ? {
                X: common_vendor.t(field.label),
                Y: field.required ? 1 : "",
                Z: formData[field.name],
                aa: common_vendor.o(($event) => formData[field.name] = $event.detail.value, fieldIndex)
              } : {}, {
                i: field.type === "gender",
                p: field.type === "picker",
                w: field.type === "location",
                E: field.type === "textarea",
                K: field.type === "upload",
                S: field.type === "tags",
                W: field.type === "code"
              }) : {}, {
                ab: fieldIndex
              });
            }),
            d: sectionIndex
          });
        }),
        e: formData.redPacketEnabled || false,
        f: common_vendor.o((e) => {
          formData.redPacketEnabled = e.detail.value;
        }),
        g: formData.redPacketEnabled
      }, formData.redPacketEnabled ? {
        h: formData.redPacketAmount,
        i: common_vendor.o(($event) => formData.redPacketAmount = $event.detail.value)
      } : {}, {
        j: formData.redPacketEnabled
      }, formData.redPacketEnabled ? {
        k: formData.redPacketCount,
        l: common_vendor.o(($event) => formData.redPacketCount = $event.detail.value)
      } : {}, {
        m: formData.redPacketEnabled
      }, formData.redPacketEnabled ? {
        n: formData.redPacketType === "average" ? 1 : "",
        o: common_vendor.o(($event) => formData.redPacketType = "average"),
        p: formData.redPacketType === "random" ? 1 : "",
        q: common_vendor.o(($event) => formData.redPacketType = "random")
      } : {}, {
        r: formData.redPacketEnabled
      }, formData.redPacketEnabled ? {
        s: ((_a = formData.redPacketConditions) == null ? void 0 : _a.includes("share")) ? 1 : "",
        t: common_vendor.o(($event) => toggleCondition("share")),
        v: ((_b = formData.redPacketConditions) == null ? void 0 : _b.includes("follow")) ? 1 : "",
        w: common_vendor.o(($event) => toggleCondition("follow"))
      } : {}, {
        x: formData.redPacketEnabled
      }, formData.redPacketEnabled ? {
        y: common_vendor.t(formData.redPacketExpiry || "请选择红包有效期"),
        z: common_assets._imports_3$10,
        A: common_vendor.o(($event) => showRedPacketDatePicker.value = true)
      } : {}, {
        B: formData.redPacketEnabled
      }, formData.redPacketEnabled ? {
        C: formData.redPacketDesc,
        D: common_vendor.o(($event) => formData.redPacketDesc = $event.detail.value)
      } : {}, {
        E: !formData.redPacketEnabled
      }, !formData.redPacketEnabled ? {
        F: common_assets._imports_4$3
      } : {}, {
        G: formData.redPacketEnabled && formData.redPacketAmount && formData.redPacketCount
      }, formData.redPacketEnabled && formData.redPacketAmount && formData.redPacketCount ? common_vendor.e({
        H: common_assets._imports_5$5,
        I: common_assets._imports_6$6,
        J: common_vendor.t(formData.redPacketAmount),
        K: common_vendor.t(formData.redPacketType === "average" ? "平均分配" : "随机金额"),
        L: common_vendor.t(formData.redPacketCount),
        M: formData.redPacketConditions && formData.redPacketConditions.length > 0
      }, formData.redPacketConditions && formData.redPacketConditions.length > 0 ? common_vendor.e({
        N: formData.redPacketConditions.includes("share")
      }, formData.redPacketConditions.includes("share") ? {} : {}, {
        O: formData.redPacketConditions.includes("follow")
      }, formData.redPacketConditions.includes("follow") ? {} : {}) : {}) : {}, {
        P: formData.agree,
        Q: common_vendor.o(($event) => showAgreement("service")),
        R: common_vendor.o(($event) => showAgreement("privacy")),
        S: common_vendor.o((e) => formData.agree = e.detail.value.length > 0),
        T: common_assets._imports_7$4,
        U: common_vendor.o(handlePublishCompleted),
        V: common_vendor.o(handlePublishCancelled),
        W: common_vendor.p({
          showMode: "direct",
          pageType: "publish",
          itemData: publishData.value
        }),
        X: !formData.agree,
        Y: common_vendor.o(submitForm),
        Z: common_vendor.n(categoryType.value)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/detail.js.map
