"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "filter",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    const businessTitle = common_vendor.ref("商家筛选");
    const searchKeyword = common_vendor.ref("");
    const currentCategory = common_vendor.ref("全部");
    const selectedSubcategory = common_vendor.ref("");
    const refreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const page = common_vendor.ref(1);
    const showAreaFilter = common_vendor.ref(false);
    const showSortFilter = common_vendor.ref(false);
    const selectedArea = common_vendor.ref("全部区域");
    const selectedSort = common_vendor.ref("默认排序");
    const areaList = common_vendor.ref(["全部区域", "城区", "磁州镇", "讲武城镇", "岳城镇", "观台镇", "白土镇", "黄沙镇"]);
    const categories = common_vendor.ref([
      { name: "全部", icon: "" },
      { name: "房产楼盘", icon: "/static/images/tabbar/房产楼盘.png" },
      { name: "美食小吃", icon: "/static/images/tabbar/美食小吃.png" },
      { name: "装修家居", icon: "/static/images/tabbar/装修家居.png" },
      { name: "母婴专区", icon: "/static/images/tabbar/母婴专区.png" },
      { name: "休闲娱乐", icon: "/static/images/tabbar/休闲娱乐.png" },
      { name: "到家服务", icon: "/static/images/tabbar/商到家服务.png" },
      { name: "开锁换锁", icon: "/static/images/tabbar/开锁换锁.png" },
      { name: "数码通讯", icon: "/static/images/tabbar/数码通讯.png" },
      { name: "车辆服务", icon: "/static/images/tabbar/商车辆服务.png" },
      { name: "教育培训", icon: "/static/images/tabbar/商教育培训.png" },
      { name: "婚纱摄影", icon: "/static/images/tabbar/婚纱摄影.png" },
      { name: "农林牧渔", icon: "/static/images/tabbar/农林牧渔.png" },
      { name: "广告传媒", icon: "/static/images/tabbar/广告传媒.png" },
      { name: "其他行业", icon: "/static/images/tabbar/其他.png" }
    ]);
    const subcategoryMap = common_vendor.ref({
      "全部": [],
      "房产楼盘": ["全部", "新房", "二手房", "租房", "商铺", "办公室", "厂房", "别墅", "写字楼", "公寓"],
      "美食小吃": ["全部", "火锅", "烧烤", "蛋糕店", "小吃", "炒菜", "烤鱼", "面馆", "西餐", "日料", "快餐", "饮品店", "早餐", "烘焙"],
      "装修家居": ["全部", "全屋装修", "家具", "建材", "瓷砖", "橱柜", "卫浴", "灯饰", "窗帘", "地板", "五金", "电工", "油漆工", "水暖工"],
      "母婴专区": ["全部", "婴儿用品", "儿童玩具", "孕妇用品", "童装", "早教", "月嫂", "育儿", "孕产服务", "母婴食品"],
      "休闲娱乐": ["全部", "KTV", "酒吧", "电影院", "咖啡厅", "茶馆", "网咖", "游乐园", "健身房", "美甲", "桌游", "棋牌室", "足浴"],
      "到家服务": ["全部", "保洁", "维修", "安装", "搬家", "洗衣", "家电维修", "管道疏通", "保姆", "月嫂", "跑腿", "家居维修"],
      "开锁换锁": ["全部", "开门锁", "开车锁", "换锁芯", "保险柜开锁", "智能锁安装", "防盗门维修", "配钥匙"],
      "数码通讯": ["全部", "手机", "电脑", "相机", "维修", "配件", "网络设备", "电子产品", "游戏设备", "办公设备"],
      "车辆服务": ["全部", "洗车", "美容", "维修", "保养", "补胎", "贴膜", "4S店", "汽车用品", "违章代办", "二手车", "汽车租赁"],
      "教育培训": ["全部", "幼儿教育", "小学辅导", "中学辅导", "高考培训", "语言培训", "音乐培训", "美术培训", "体育培训", "职业技能", "才艺培训"],
      "婚纱摄影": ["全部", "婚纱摄影", "婚礼策划", "婚车租赁", "婚礼司仪", "婚宴酒店", "婚礼跟拍", "婚戒定制", "婚纱礼服", "新娘跟妆"],
      "农林牧渔": ["全部", "农产品", "种植", "养殖", "农资", "花卉", "苗木", "水产", "畜牧", "农机", "农技服务"],
      "广告传媒": ["全部", "广告设计", "印刷", "展览展示", "标识标牌", "喷绘写真", "广告制作", "媒体投放", "摄影摄像", "网络推广"],
      "其他行业": ["全部", "劳务派遣", "招聘服务", "法律咨询", "财务服务", "物流快递", "公司注册", "保险服务", "翻译服务", "代理记账"]
    });
    const subcategoryList = common_vendor.ref([]);
    const sortList = common_vendor.ref(["默认排序", "热门优先", "距离最近", "最新加入", "评分最高"]);
    const businessList = common_vendor.ref([
      {
        id: "1",
        logo: "/static/images/tabbar/入驻卡片.png",
        name: "五分利电器",
        description: "家电全网调货，全场特价，送货上门",
        category: "数码电器",
        subcategory: "电子产品",
        scale: "10-20人",
        area: "城区",
        distance: "1.2"
      },
      {
        id: "2",
        logo: "/static/images/tabbar/入驻卡片.png",
        name: "北方鑫雨装饰",
        description: "专业设计，精工细作，打造温馨家园",
        category: "装修家居",
        subcategory: "全屋装修",
        scale: "50-100人",
        area: "磁州镇",
        distance: "3.5"
      }
    ]);
    const hasActiveFilters = common_vendor.computed(() => {
      return currentCategory.value !== "全部" || selectedSubcategory.value !== "" && selectedSubcategory.value !== "全部" || selectedArea.value !== "全部区域" || selectedSort.value !== "默认排序";
    });
    common_vendor.onMounted(() => {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = sysInfo.statusBarHeight || 20;
    });
    common_vendor.onLoad((options) => {
      const category = options.category;
      if (category) {
        const foundCategory = categories.value.find((c) => c.name === category);
        if (foundCategory) {
          switchCategory(foundCategory);
        } else {
          businessTitle.value = "全部商家";
          fetchBusinessData(true);
        }
      } else {
        businessTitle.value = "全部商家";
        fetchBusinessData(true);
      }
    });
    common_vendor.onPullDownRefresh(() => {
      onRefresh();
    });
    common_vendor.onReachBottom(() => {
      loadMore();
    });
    const navigateBack = () => {
      common_vendor.index.navigateBack();
    };
    const searchBusinesses = () => {
      common_vendor.index.__f__("log", "at pages/business/filter.vue:320", "开始搜索:", searchKeyword.value);
      resetAndFetch();
    };
    const switchCategory = (item) => {
      if (currentCategory.value === item.name)
        return;
      currentCategory.value = item.name;
      businessTitle.value = item.name === "全部" ? "全部商家" : item.name;
      selectedSubcategory.value = "";
      subcategoryList.value = subcategoryMap.value[item.name] || [];
      if (subcategoryList.value.length > 0) {
        selectedSubcategory.value = "全部";
      }
      resetAndFetch();
    };
    const selectSubcategory = (subcat) => {
      if (selectedSubcategory.value === subcat)
        return;
      selectedSubcategory.value = subcat;
      resetAndFetch();
    };
    const selectArea = (area) => {
      selectedArea.value = area;
      showAreaFilter.value = false;
      resetAndFetch();
    };
    const selectSort = (sort) => {
      selectedSort.value = sort;
      showSortFilter.value = false;
      resetAndFetch();
    };
    const closeAllFilters = () => {
      showAreaFilter.value = false;
      showSortFilter.value = false;
    };
    const resetCategory = () => {
      switchCategory({ name: "全部" });
    };
    const resetSubcategory = () => {
      selectedSubcategory.value = "全部";
      resetAndFetch();
    };
    const resetArea = () => {
      selectArea("全部区域");
    };
    const resetSort = () => {
      selectSort("默认排序");
    };
    const resetAllFilters = () => {
      currentCategory.value = "全部";
      businessTitle.value = "全部商家";
      selectedSubcategory.value = "";
      subcategoryList.value = [];
      selectedArea.value = "全部区域";
      selectedSort.value = "默认排序";
      searchKeyword.value = "";
      closeAllFilters();
      resetAndFetch();
    };
    const fetchBusinessData = (isRefresh = false) => {
      if (isRefresh) {
        page.value = 1;
        businessList.value = [];
        hasMore.value = true;
      }
      if (!hasMore.value) {
        if (refreshing.value)
          common_vendor.index.stopPullDownRefresh();
        return;
      }
      common_vendor.index.__f__("log", "at pages/business/filter.vue:404", "Fetching data...", {
        page: page.value,
        keyword: searchKeyword.value,
        category: currentCategory.value,
        subcategory: selectedSubcategory.value,
        area: selectedArea.value,
        sort: selectedSort.value
      });
      setTimeout(() => {
        const mockData = [
          { id: "1", logo: "/static/images/tabbar/入驻卡片.png", name: "五分利电器", description: "家电全网调货，全场特价，送货上门", category: "数码电器", subcategory: "电子产品", scale: "10-20人", area: "城区", distance: "1.2" },
          { id: "2", logo: "/static/images/tabbar/入驻卡片.png", name: "北方鑫雨装饰", description: "专业设计，精工细作，打造温馨家园", category: "装修家居", subcategory: "全屋装修", scale: "50-100人", area: "磁州镇", distance: "3.5" }
          // 更多模拟数据...
        ];
        if (page.value > 2) {
          businessList.value = businessList.value.concat([]);
          hasMore.value = false;
        } else {
          businessList.value = isRefresh ? mockData : businessList.value.concat(mockData);
          hasMore.value = true;
        }
        page.value++;
        if (refreshing.value) {
          common_vendor.index.stopPullDownRefresh();
          refreshing.value = false;
        }
      }, 1e3);
    };
    const resetAndFetch = () => {
      fetchBusinessData(true);
    };
    const onRefresh = () => {
      if (refreshing.value)
        return;
      refreshing.value = true;
      fetchBusinessData(true);
    };
    const loadMore = () => {
      fetchBusinessData();
    };
    const navigateToShopDetail = (id) => {
      common_vendor.index.navigateTo({
        url: `/pages/business/shop-detail?id=${id}`
      });
    };
    const followBusiness = (id) => {
      common_vendor.index.__f__("log", "at pages/business/filter.vue:459", "关注商家:", id);
      common_vendor.index.showToast({
        title: "关注成功",
        icon: "success"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(navigateBack),
        b: common_vendor.t(businessTitle.value),
        c: statusBarHeight.value + "px",
        d: common_assets._imports_0$10,
        e: common_vendor.o(searchBusinesses),
        f: searchKeyword.value,
        g: common_vendor.o(($event) => searchKeyword.value = $event.detail.value),
        h: common_vendor.o(searchBusinesses),
        i: common_vendor.f(categories.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.name),
            b: index,
            c: currentCategory.value === item.name ? 1 : "",
            d: common_vendor.o(($event) => switchCategory(item), index)
          };
        }),
        j: subcategoryList.value.length > 0
      }, subcategoryList.value.length > 0 ? {
        k: common_vendor.f(subcategoryList.value, (subcat, index, i0) => {
          return {
            a: common_vendor.t(subcat),
            b: index,
            c: selectedSubcategory.value === subcat ? 1 : "",
            d: common_vendor.o(($event) => selectSubcategory(subcat), index)
          };
        })
      } : {}, {
        l: common_vendor.t(selectedArea.value),
        m: selectedArea.value !== "全部区域" ? 1 : "",
        n: showAreaFilter.value ? 1 : "",
        o: common_vendor.o(($event) => showAreaFilter.value = true),
        p: common_vendor.t(selectedSort.value),
        q: selectedSort.value !== "默认排序" ? 1 : "",
        r: showSortFilter.value ? 1 : "",
        s: common_vendor.o(($event) => showSortFilter.value = true),
        t: hasActiveFilters.value
      }, hasActiveFilters.value ? common_vendor.e({
        v: currentCategory.value !== "全部"
      }, currentCategory.value !== "全部" ? {
        w: common_vendor.t(currentCategory.value),
        x: common_vendor.o(resetCategory)
      } : {}, {
        y: selectedSubcategory.value !== "" && selectedSubcategory.value !== "全部"
      }, selectedSubcategory.value !== "" && selectedSubcategory.value !== "全部" ? {
        z: common_vendor.t(selectedSubcategory.value),
        A: common_vendor.o(resetSubcategory)
      } : {}, {
        B: selectedArea.value !== "全部区域"
      }, selectedArea.value !== "全部区域" ? {
        C: common_vendor.t(selectedArea.value),
        D: common_vendor.o(resetArea)
      } : {}, {
        E: selectedSort.value !== "默认排序"
      }, selectedSort.value !== "默认排序" ? {
        F: common_vendor.t(selectedSort.value),
        G: common_vendor.o(resetSort)
      } : {}, {
        H: common_vendor.o(resetAllFilters)
      }) : {}, {
        I: showAreaFilter.value
      }, showAreaFilter.value ? {
        J: common_vendor.f(areaList.value, (area, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(area),
            b: area === selectedArea.value
          }, area === selectedArea.value ? {} : {}, {
            c: index,
            d: area === selectedArea.value ? 1 : "",
            e: common_vendor.o(($event) => selectArea(area), index)
          });
        })
      } : {}, {
        K: showSortFilter.value
      }, showSortFilter.value ? {
        L: common_vendor.f(sortList.value, (sort, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(sort),
            b: sort === selectedSort.value
          }, sort === selectedSort.value ? {} : {}, {
            c: index,
            d: sort === selectedSort.value ? 1 : "",
            e: common_vendor.o(($event) => selectSort(sort), index)
          });
        })
      } : {}, {
        M: showAreaFilter.value || showSortFilter.value
      }, showAreaFilter.value || showSortFilter.value ? {
        N: common_vendor.o(closeAllFilters)
      } : {}, {
        O: businessList.value.length > 0
      }, businessList.value.length > 0 ? {
        P: common_vendor.t(businessList.value.length)
      } : {}, {
        Q: businessList.value.length > 0
      }, businessList.value.length > 0 ? common_vendor.e({
        R: common_vendor.f(businessList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.logo,
            b: common_vendor.t(item.name),
            c: item.distance
          }, item.distance ? {
            d: common_vendor.t(item.distance)
          } : {}, {
            e: common_vendor.t(item.description),
            f: common_vendor.t(item.category),
            g: item.subcategory
          }, item.subcategory ? {
            h: common_vendor.t(item.subcategory)
          } : {}, {
            i: item.scale
          }, item.scale ? {
            j: common_vendor.t(item.scale)
          } : {}, {
            k: item.area
          }, item.area ? {
            l: common_vendor.t(item.area)
          } : {}, {
            m: common_vendor.o(($event) => followBusiness(item.id), index),
            n: index,
            o: common_vendor.o(($event) => navigateToShopDetail(item.id), index)
          });
        }),
        S: hasMore.value
      }, hasMore.value ? {} : {}) : {
        T: common_assets._imports_1$3
      }, {
        U: common_vendor.o(loadMore),
        V: refreshing.value,
        W: common_vendor.o(onRefresh)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-13788ce4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/business/filter.js.map
