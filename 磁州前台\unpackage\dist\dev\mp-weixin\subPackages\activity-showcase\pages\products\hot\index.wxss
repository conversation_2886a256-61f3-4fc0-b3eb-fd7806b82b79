/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-337cb590, html.data-v-337cb590, #app.data-v-337cb590, .index-container.data-v-337cb590 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.product-detail-container.data-v-337cb590 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar.data-v-337cb590 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-337cb590 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(255, 59, 105, 0.15);
}
.custom-navbar .navbar-content.data-v-337cb590 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
}
.custom-navbar .navbar-title.data-v-337cb590 {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.custom-navbar .back-btn.data-v-337cb590, .custom-navbar .share-btn.data-v-337cb590 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-navbar .back-btn.data-v-337cb590 {
  position: absolute;
  left: 30rpx;
}
.custom-navbar .navbar-right.data-v-337cb590 {
  position: absolute;
  right: 30rpx;
}

/* 内容区域 */
.content-scroll.data-v-337cb590 {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  margin-bottom: 120rpx;
  /* 底部操作栏高度 */
}

/* 商品轮播图 */
.product-swiper.data-v-337cb590 {
  width: 100%;
  height: 750rpx;
}
.product-swiper .swiper-item.data-v-337cb590 {
  width: 100%;
  height: 100%;
}
.product-swiper .product-image.data-v-337cb590 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 价格信息 */
.price-section.data-v-337cb590 {
  background-color: #FFFFFF;
  padding: 30rpx;
}
.price-section .price-row.data-v-337cb590 {
  display: flex;
  align-items: center;
}
.price-section .current-price.data-v-337cb590 {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF3B69;
}
.price-section .original-price.data-v-337cb590 {
  font-size: 28rpx;
  color: #8E8E93;
  text-decoration: line-through;
  margin-left: 16rpx;
}
.price-section .discount-badge.data-v-337cb590 {
  margin-left: 16rpx;
  background-color: rgba(255, 59, 105, 0.1);
  color: #FF3B69;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}
.price-section .sales-info.data-v-337cb590 {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 12rpx;
}

/* 商品标题 */
.product-title-section.data-v-337cb590 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 2rpx;
  display: flex;
  justify-content: space-between;
}
.product-title-section .product-title.data-v-337cb590 {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.5;
}
.product-title-section .collect-btn.data-v-337cb590 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 30rpx;
}
.product-title-section .collect-btn .collect-icon.data-v-337cb590 {
  margin-bottom: 6rpx;
}
.product-title-section .collect-btn .collect-icon.active.data-v-337cb590 {
  animation: heartBeat-337cb590 0.5s ease;
}
.product-title-section .collect-btn text.data-v-337cb590 {
  font-size: 22rpx;
  color: #8E8E93;
}
@keyframes heartBeat-337cb590 {
0% {
    transform: scale(1);
}
14% {
    transform: scale(1.3);
}
28% {
    transform: scale(1);
}
42% {
    transform: scale(1.3);
}
70% {
    transform: scale(1);
}
}
/* 商品标签 */
.tags-section.data-v-337cb590 {
  background-color: #FFFFFF;
  padding: 0 30rpx 30rpx;
  display: flex;
  flex-wrap: wrap;
}
.tags-section .tag-item.data-v-337cb590 {
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.tags-section .tag-item .tag-badge.data-v-337cb590 {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}
.tags-section .tag-item .tag-badge.discount.data-v-337cb590 {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}
.tags-section .tag-item .tag-badge.new.data-v-337cb590 {
  background-color: rgba(90, 200, 250, 0.1);
  color: #5AC8FA;
}
.tags-section .tag-item .tag-badge.hot.data-v-337cb590 {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}
.tags-section .tag-item .tag-badge.coupon.data-v-337cb590 {
  background-color: rgba(255, 45, 85, 0.1);
  color: #FF2D55;
}

/* 促销信息 */
.promotion-section.data-v-337cb590 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
}
.promotion-section .section-title.data-v-337cb590 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}
.promotion-section .promotion-list .promotion-item.data-v-337cb590 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.promotion-section .promotion-list .promotion-item .promo-badge.data-v-337cb590 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #FFFFFF;
  margin-right: 16rpx;
}
.promotion-section .promotion-list .promotion-item .promo-badge.discount.data-v-337cb590 {
  background-color: #FF9500;
}
.promotion-section .promotion-list .promotion-item .promo-badge.coupon.data-v-337cb590 {
  background-color: #FF2D55;
}
.promotion-section .promotion-list .promotion-item .promo-text.data-v-337cb590 {
  font-size: 26rpx;
  color: #333333;
}

/* 选择规格 */
.spec-section.data-v-337cb590 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 2rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.spec-section .section-title.data-v-337cb590 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.spec-section .spec-content.data-v-337cb590 {
  display: flex;
  align-items: center;
}
.spec-section .spec-content .spec-text.data-v-337cb590 {
  font-size: 26rpx;
  color: #8E8E93;
  margin-right: 10rpx;
}

/* 店铺信息 */
.shop-section.data-v-337cb590 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
}
.shop-section .shop-logo.data-v-337cb590 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.shop-section .shop-info.data-v-337cb590 {
  flex: 1;
}
.shop-section .shop-info .shop-name.data-v-337cb590 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}
.shop-section .shop-info .shop-rating.data-v-337cb590 {
  display: flex;
  align-items: center;
}
.shop-section .shop-info .shop-rating .rating-stars.data-v-337cb590 {
  display: flex;
  margin-right: 10rpx;
}
.shop-section .shop-info .shop-rating .rating-text.data-v-337cb590 {
  font-size: 24rpx;
  color: #FF9500;
}
.shop-section .enter-shop-btn.data-v-337cb590 {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: rgba(255, 59, 105, 0.1);
  border-radius: 30rpx;
}
.shop-section .enter-shop-btn text.data-v-337cb590 {
  font-size: 24rpx;
  color: #FF3B69;
  margin-right: 6rpx;
}

/* 商品详情 */
.detail-section.data-v-337cb590 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
}
.detail-section .detail-title.data-v-337cb590 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.detail-section .detail-title.data-v-337cb590::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #FF3B69, #FF7A9E);
  border-radius: 4rpx;
}
.detail-section .detail-content.data-v-337cb590 {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.6;
}
.detail-section .detail-images.data-v-337cb590 {
  margin-top: 20rpx;
}
.detail-section .detail-images .detail-image.data-v-337cb590 {
  width: 100%;
  height: auto;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

/* 商品评价 */
.reviews-section.data-v-337cb590 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
}
.reviews-section .section-header.data-v-337cb590 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.reviews-section .section-header .section-title.data-v-337cb590 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}
.reviews-section .section-header .section-title.data-v-337cb590::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #FF3B69, #FF7A9E);
  border-radius: 4rpx;
}
.reviews-section .section-header .view-all.data-v-337cb590 {
  display: flex;
  align-items: center;
}
.reviews-section .section-header .view-all text.data-v-337cb590 {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 6rpx;
}
.reviews-section .reviews-list .review-item.data-v-337cb590 {
  padding-bottom: 20rpx;
  border-bottom: 1px solid #F2F2F7;
  margin-bottom: 20rpx;
}
.reviews-section .reviews-list .review-item.data-v-337cb590:last-child {
  margin-bottom: 0;
  border-bottom: none;
}
.reviews-section .reviews-list .review-item .review-header.data-v-337cb590 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.reviews-section .reviews-list .review-item .review-header .user-avatar.data-v-337cb590 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.reviews-section .reviews-list .review-item .review-header .review-user-info.data-v-337cb590 {
  flex: 1;
}
.reviews-section .reviews-list .review-item .review-header .review-user-info .user-name.data-v-337cb590 {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 6rpx;
}
.reviews-section .reviews-list .review-item .review-header .review-user-info .review-rating.data-v-337cb590 {
  display: flex;
}
.reviews-section .reviews-list .review-item .review-header .review-time.data-v-337cb590 {
  font-size: 22rpx;
  color: #8E8E93;
}
.reviews-section .reviews-list .review-item .review-content.data-v-337cb590 {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 16rpx;
}
.reviews-section .reviews-list .review-item .review-images.data-v-337cb590 {
  display: flex;
  flex-wrap: wrap;
}
.reviews-section .reviews-list .review-item .review-images .review-image.data-v-337cb590 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.reviews-section .empty-reviews.data-v-337cb590 {
  padding: 60rpx 0;
  text-align: center;
}
.reviews-section .empty-reviews text.data-v-337cb590 {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 相关推荐 */
.recommend-section.data-v-337cb590 {
  background-color: #FFFFFF;
  padding: 30rpx;
  margin-top: 20rpx;
}
.recommend-section .section-title.data-v-337cb590 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.recommend-section .section-title.data-v-337cb590::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #FF3B69, #FF7A9E);
  border-radius: 4rpx;
}
.recommend-section .recommend-scroll.data-v-337cb590 {
  white-space: nowrap;
}
.recommend-section .recommend-list.data-v-337cb590 {
  display: inline-flex;
  padding: 10rpx 0;
}
.recommend-section .recommend-item.data-v-337cb590 {
  width: 240rpx;
  margin-right: 20rpx;
}
.recommend-section .recommend-item .recommend-image.data-v-337cb590 {
  width: 240rpx;
  height: 240rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}
.recommend-section .recommend-item .recommend-info .recommend-title.data-v-337cb590 {
  font-size: 26rpx;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 8rpx;
}
.recommend-section .recommend-item .recommend-info .recommend-price.data-v-337cb590 {
  font-size: 26rpx;
  font-weight: 500;
  color: #FF3B69;
}

/* 底部安全区域 */
.safe-area-bottom.data-v-337cb590 {
  height: 34px;
  /* iOS 安全区域高度 */
}

/* 底部操作栏 */
.bottom-bar.data-v-337cb590 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 90;
}
.bottom-bar .bottom-left.data-v-337cb590 {
  display: flex;
  flex: 1;
}
.bottom-bar .bottom-left .action-btn.data-v-337cb590 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  position: relative;
}
.bottom-bar .bottom-left .action-btn svg.data-v-337cb590 {
  margin-bottom: 6rpx;
}
.bottom-bar .bottom-left .action-btn text.data-v-337cb590 {
  font-size: 22rpx;
  color: #8E8E93;
}
.bottom-bar .bottom-left .action-btn .cart-badge.data-v-337cb590 {
  position: absolute;
  top: 0;
  right: 10rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
}
.bottom-bar .bottom-right.data-v-337cb590 {
  flex: 2;
  display: flex;
}
.bottom-bar .bottom-right .add-to-cart-btn.data-v-337cb590, .bottom-bar .bottom-right .buy-now-btn.data-v-337cb590 {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}
.bottom-bar .bottom-right .add-to-cart-btn.data-v-337cb590 {
  background-color: rgba(255, 59, 105, 0.1);
  color: #FF3B69;
  border-radius: 40rpx 0 0 40rpx;
}
.bottom-bar .bottom-right .buy-now-btn.data-v-337cb590 {
  background: linear-gradient(135deg, #FF3B69, #FF7A9E);
  color: #FFFFFF;
  border-radius: 0 40rpx 40rpx 0;
}

/* 规格选择弹窗 */
.spec-popup.data-v-337cb590 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.spec-popup .spec-popup-content.data-v-337cb590 {
  background-color: #FFFFFF;
  border-radius: 30rpx 30rpx 0 0;
  padding: 30rpx;
  animation: slideUp-337cb590 0.3s ease;
  max-height: 80vh;
  overflow-y: auto;
}
.spec-popup .spec-popup-header.data-v-337cb590 {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}
.spec-popup .spec-popup-header .spec-product-image.data-v-337cb590 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}
.spec-popup .spec-popup-header .spec-product-info.data-v-337cb590 {
  flex: 1;
}
.spec-popup .spec-popup-header .spec-product-info .spec-product-price.data-v-337cb590 {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF3B69;
  margin-bottom: 10rpx;
}
.spec-popup .spec-popup-header .spec-product-info .spec-product-stock.data-v-337cb590 {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 10rpx;
}
.spec-popup .spec-popup-header .spec-product-info .spec-selected.data-v-337cb590 {
  font-size: 24rpx;
  color: #333333;
}
.spec-popup .spec-popup-header .spec-close-btn.data-v-337cb590 {
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.spec-popup .spec-options .spec-option-group.data-v-337cb590 {
  margin-bottom: 30rpx;
}
.spec-popup .spec-options .spec-option-group .spec-option-title.data-v-337cb590 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}
.spec-popup .spec-options .spec-option-group .spec-option-list.data-v-337cb590 {
  display: flex;
  flex-wrap: wrap;
}
.spec-popup .spec-options .spec-option-group .spec-option-list .spec-option-item.data-v-337cb590 {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  background-color: #F2F2F7;
  font-size: 26rpx;
  color: #333333;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.spec-popup .spec-options .spec-option-group .spec-option-list .spec-option-item.active.data-v-337cb590 {
  background-color: rgba(255, 59, 105, 0.1);
  color: #FF3B69;
  border: 1px solid #FF3B69;
}
.spec-popup .spec-options .spec-option-group .spec-option-list .spec-option-item.disabled.data-v-337cb590 {
  color: #C8C8C8;
  background-color: #F8F8F8;
  pointer-events: none;
}
.spec-popup .quantity-selector.data-v-337cb590 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.spec-popup .quantity-selector .quantity-title.data-v-337cb590 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.spec-popup .quantity-selector .quantity-control.data-v-337cb590 {
  display: flex;
  align-items: center;
}
.spec-popup .quantity-selector .quantity-control .quantity-btn.data-v-337cb590 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #E0E0E0;
}
.spec-popup .quantity-selector .quantity-control .quantity-btn.minus.data-v-337cb590 {
  border-radius: 8rpx 0 0 8rpx;
}
.spec-popup .quantity-selector .quantity-control .quantity-btn.plus.data-v-337cb590 {
  border-radius: 0 8rpx 8rpx 0;
}
.spec-popup .quantity-selector .quantity-control .quantity-btn.disabled.data-v-337cb590 {
  color: #C8C8C8;
  background-color: #F8F8F8;
  pointer-events: none;
}
.spec-popup .quantity-selector .quantity-control .quantity-input.data-v-337cb590 {
  width: 80rpx;
  height: 60rpx;
  border-top: 1px solid #E0E0E0;
  border-bottom: 1px solid #E0E0E0;
  text-align: center;
  font-size: 28rpx;
}
.spec-popup .spec-popup-footer.data-v-337cb590 {
  display: flex;
}
.spec-popup .spec-popup-footer .add-to-cart-btn.data-v-337cb590, .spec-popup .spec-popup-footer .buy-now-btn.data-v-337cb590 {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}
.spec-popup .spec-popup-footer .add-to-cart-btn.data-v-337cb590 {
  background-color: rgba(255, 59, 105, 0.1);
  color: #FF3B69;
  border-radius: 40rpx 0 0 40rpx;
}
.spec-popup .spec-popup-footer .buy-now-btn.data-v-337cb590 {
  background: linear-gradient(135deg, #FF3B69, #FF7A9E);
  color: #FFFFFF;
  border-radius: 0 40rpx 40rpx 0;
}
@keyframes slideUp-337cb590 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}