<template>
  <view class="shop-card" :style="cardStyle" @click="$emit('click', shop)">
    <!-- 店铺封面 -->
    <view class="shop-cover-container">
      <image class="shop-cover" :src="shop.coverImage" mode="aspectFill"></image>
      <view class="shop-logo-container">
        <image class="shop-logo" :src="shop.logo" mode="aspectFill"></image>
      </view>
    </view>
    
    <!-- 店铺信息 -->
    <view class="shop-info">
      <view class="shop-header">
        <view class="shop-name">{{ shop.name }}</view>
        <view class="shop-rating">
          <view class="rating-stars">
            <view 
              class="star-item" 
              v-for="i in 5" 
              :key="i"
              :class="{ active: i <= Math.floor(shop.rating) }"
            >
              <svg class="star-icon" viewBox="0 0 24 24" width="12" height="12">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"></path>
              </svg>
            </view>
          </view>
          <text class="rating-value">{{ shop.rating }}</text>
        </view>
      </view>
      
      <view class="shop-meta">
        <view class="meta-item">
          <svg class="meta-icon" viewBox="0 0 24 24" width="14" height="14">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"></path>
            <circle cx="12" cy="9" r="2" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"></circle>
          </svg>
          <text class="meta-text">{{ shop.distance }}</text>
        </view>
        
        <view class="meta-item">
          <svg class="meta-icon" viewBox="0 0 24 24" width="14" height="14">
            <path d="M19 5H5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2z" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"></path>
            <path d="M3 7l9 6 9-6" stroke="#8E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"></path>
          </svg>
          <text class="meta-text">{{ shop.orderCount }}+订单</text>
        </view>
      </view>
      
      <!-- 店铺标签 -->
      <view class="shop-tags" v-if="shop.tags && shop.tags.length > 0">
        <view 
          class="tag-item"
          v-for="(tag, index) in shop.tags"
          :key="index"
        >
          {{ tag }}
        </view>
      </view>
      
      <!-- 店铺描述 -->
      <view class="shop-description" v-if="shop.description">
        {{ shop.description }}
      </view>
    </view>
    
    <!-- 进店按钮 -->
    <view class="enter-shop-btn" @click.stop="$emit('enter', shop)">
      <text>进店</text>
      <svg class="enter-icon" viewBox="0 0 24 24" width="16" height="16">
        <path d="M9 18l6-6-6-6" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
      </svg>
    </view>
  </view>
</template>

<script setup>
// 组件属性定义
const props = defineProps({
  shop: {
    type: Object,
    required: true
  },
  cardStyle: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
defineEmits(['click', 'enter']);
</script>

<style lang="scss" scoped>
.shop-card {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  margin-bottom: 20rpx;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  }
}

.shop-cover-container {
  width: 100%;
  height: 200rpx;
  position: relative;
  
  .shop-cover {
    width: 100%;
    height: 100%;
  }
  
  .shop-logo-container {
    position: absolute;
    bottom: -30rpx;
    left: 30rpx;
    width: 80rpx;
    height: 80rpx;
    border-radius: 16rpx;
    background-color: #FFFFFF;
    padding: 4rpx;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    
    .shop-logo {
      width: 100%;
      height: 100%;
      border-radius: 12rpx;
    }
  }
}

.shop-info {
  padding: 40rpx 30rpx 30rpx;
  
  .shop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    .shop-name {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .shop-rating {
      display: flex;
      align-items: center;
      
      .rating-stars {
        display: flex;
        margin-right: 8rpx;
        
        .star-item {
          color: #E5E5EA;
          margin-right: 2rpx;
          
          &.active {
            color: #FF9500;
          }
        }
      }
      
      .rating-value {
        font-size: 24rpx;
        color: #FF9500;
        font-weight: 500;
      }
    }
  }
  
  .shop-meta {
    display: flex;
    margin-bottom: 16rpx;
    
    .meta-item {
      display: flex;
      align-items: center;
      margin-right: 24rpx;
      
      .meta-icon {
        margin-right: 4rpx;
      }
      
      .meta-text {
        font-size: 24rpx;
        color: #8E8E93;
      }
    }
  }
  
  .shop-tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16rpx;
    
    .tag-item {
      padding: 4rpx 12rpx;
      border-radius: 6rpx;
      font-size: 20rpx;
      margin-right: 12rpx;
      margin-bottom: 8rpx;
      background-color: rgba(255,59,105,0.1);
      color: #FF3B69;
    }
  }
  
  .shop-description {
    font-size: 24rpx;
    color: #8E8E93;
    line-height: 1.4;
  }
}

.enter-shop-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  display: flex;
  align-items: center;
  box-shadow: 0 4px 8px rgba(255,59,105,0.3);
  
  text {
    font-size: 24rpx;
    color: #FFFFFF;
  }
  
  .enter-icon {
    color: #FFFFFF;
  }
  
  &:active {
    transform: scale(0.9);
  }
}
</style> 