"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Math) {
  (JobCard + HouseRentCard + SecondHandCard + BaseInfoCard)();
}
const BaseInfoCard = () => "./BaseInfoCard.js";
const JobCard = () => "./JobCard.js";
const HouseRentCard = () => "./HouseRentCard.js";
const SecondHandCard = () => "./SecondHandCard.js";
const _sfc_main = {
  __name: "InfoCardFactory",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const cardType = common_vendor.computed(() => {
      const categoryMap = {
        "招聘信息": "JobCard",
        "求职信息": "JobCard",
        "房屋出租": "HouseRentCard",
        "房屋出售": "HouseRentCard",
        "二手闲置": "SecondHandCard",
        "二手车辆": "SecondHandCard",
        "到家服务": "BaseInfoCard",
        "寻找服务": "BaseInfoCard",
        "生意转让": "BaseInfoCard",
        "宠物信息": "BaseInfoCard",
        "商家活动": "BaseInfoCard",
        "婚恋交友": "BaseInfoCard",
        "车辆服务": "BaseInfoCard",
        "磁州拼车": "BaseInfoCard",
        "教育培训": "BaseInfoCard",
        "其他服务": "BaseInfoCard"
      };
      return categoryMap[props.item.category] || "BaseInfoCard";
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: cardType.value === "JobCard"
      }, cardType.value === "JobCard" ? {
        b: common_vendor.p({
          item: __props.item
        })
      } : cardType.value === "HouseRentCard" ? {
        d: common_vendor.p({
          item: __props.item
        })
      } : cardType.value === "SecondHandCard" ? {
        f: common_vendor.p({
          item: __props.item
        })
      } : {
        g: common_vendor.p({
          item: __props.item
        })
      }, {
        c: cardType.value === "HouseRentCard",
        e: cardType.value === "SecondHandCard"
      });
    };
  }
};
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/cards/InfoCardFactory.js.map
