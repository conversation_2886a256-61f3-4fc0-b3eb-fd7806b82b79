// 招聘信息详情模拟数据
export const jobDetail = {
  id: 'job12345',
  title: '急招会计1名，五险一金，双休',
  salary: '4500-6000元/月',
  tags: ['会计', '财务', '双休', '五险一金', '2年经验'],
  publishTime: Date.now() - 86400000 * 1, // 1天前
  company: {
    name: '磁县鑫源贸易有限公司',
    logo: '/static/images/company-logo.png',
    size: '20-99人',
    industry: '贸易/批发/零售',
    address: '磁县经济开发区创业路18号',
    isVerified: true
  },
  jobType: '全职',
  experience: '2年以上',
  education: '大专及以上',
  headcount: '1人',
  department: '财务部',
  workingAddress: '磁县经济开发区创业路18号',
  workingTime: '8:30-17:30，周末双休',
  benefits: ['五险一金', '带薪年假', '节日福利', '员工旅游', '定期体检'],
  responsibilities: [
    '负责公司日常财务核算、报表编制',
    '负责税务申报、税务筹划',
    '负责与银行、税务等部门的对接工作',
    '协助财务经理完成其他财务工作'
  ],
  requirements: [
    '财务、会计等相关专业，大专及以上学历',
    '2年以上财务工作经验，有中小企业工作经验优先',
    '熟悉财务软件操作，熟悉Excel等办公软件',
    '具有良好的沟通能力和团队合作精神',
    '工作认真负责，有较强的学习能力'
  ],
  description: '我公司是一家成立于2010年的贸易企业，主要经营建材、五金、电器等产品。公司发展稳定，团队氛围和谐，福利待遇优厚。\n\n现因业务发展需要，急招会计1名，负责公司日常财务工作。我们提供有竞争力的薪资待遇和良好的晋升空间，欢迎有志之士加入我们的团队！',
  publisher: {
    id: 'hr12345',
    name: '张经理（HR）',
    avatar: '/static/images/avatar.png',
    position: '人事经理',
    isVerified: true
  },
  contact: {
    name: '张经理',
    phone: '13876543210',
    email: '<EMAIL>'
  }
};

// 相关职位推荐数据
export const relatedJobs = [
  {
    id: 'job001',
    title: '出纳文员',
    salary: '3500-4500元/月',
    company: '磁县华信会计师事务所',
    experience: '1-3年',
    education: '大专',
    tags: ['五险', '双休', '朝九晚五']
  },
  {
    id: 'job002',
    title: '财务主管',
    salary: '6000-8000元/月',
    company: '磁县恒通物流有限公司',
    experience: '3-5年',
    education: '本科',
    tags: ['五险一金', '年终奖', '管理岗位']
  },
  {
    id: 'job003',
    title: '会计助理',
    salary: '3000-4000元/月',
    company: '磁县宏达商贸有限公司',
    experience: '应届毕业生',
    education: '大专',
    tags: ['五险', '包吃住', '有培训']
  }
];

// 获取招聘信息详情的API函数
export const fetchJobDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(jobDetail);
    }, 300);
  });
};

// 获取相关职位的API函数
export const fetchRelatedJobs = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(relatedJobs);
    }, 300);
  });
}; 