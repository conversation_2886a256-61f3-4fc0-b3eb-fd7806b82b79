"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
if (!Math) {
  UserAuth();
}
const UserAuth = () => "../../components/UserAuth.js";
const _sfc_main = {
  __name: "my",
  setup(__props) {
    const userInfo = common_vendor.ref({
      nickname: "",
      avatar: ""
    });
    const showAuthPopup = common_vendor.ref(false);
    const handleUserCardClick = () => {
      if (!userInfo.value.nickname) {
        showAuthPopup.value = true;
      } else {
        navigateTo("/pages/user-center/profile");
      }
    };
    const closeAuthPopup = () => {
      showAuthPopup.value = false;
    };
    const onUserInfoUpdated = (newUserInfo) => {
      userInfo.value = newUserInfo;
      showAuthPopup.value = false;
    };
    const navigateTo = (url) => {
      common_vendor.index.navigateTo({ url });
    };
    const goToRefreshPackage = () => {
      common_vendor.index.__f__("log", "at pages/my/my.vue:291", "跳转到刷新套餐页面");
      common_vendor.index.navigateTo({
        url: "/pages/my/refresh-package",
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:297", "跳转失败:", err);
          common_vendor.index.navigateTo({
            url: "./refresh-package",
            fail: (err2) => {
              common_vendor.index.__f__("error", "at pages/my/my.vue:302", "相对路径跳转失败:", err2);
              common_vendor.index.showToast({
                title: "页面加载中，请稍后再试",
                icon: "none",
                duration: 2e3
              });
            }
          });
        }
      });
    };
    const navigateToCashback = () => {
      common_vendor.index.__f__("log", "at pages/my/my.vue:316", "跳转到返利商城");
      common_vendor.index.redirectTo({
        url: "/subPackages/cashback/pages/index/index",
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:322", "返利商城跳转失败:", err);
          common_vendor.index.redirectTo({
            url: "subPackages/cashback/pages/index/index",
            fail: (err2) => {
              common_vendor.index.__f__("error", "at pages/my/my.vue:327", "第二次尝试跳转失败:", err2);
              common_vendor.index.reLaunch({
                url: "/subPackages/cashback/pages/index/index",
                fail: (err3) => {
                  common_vendor.index.__f__("error", "at pages/my/my.vue:332", "reLaunch跳转失败:", err3);
                  common_vendor.index.showToast({
                    title: "返利商城正在升级中，请稍后再试",
                    icon: "none",
                    duration: 2e3
                  });
                }
              });
            }
          });
        }
      });
    };
    const loadUserInfo = () => {
      const storedUserInfo = common_vendor.index.getStorageSync("userInfo");
      if (storedUserInfo) {
        userInfo.value = storedUserInfo;
      }
    };
    common_vendor.onMounted(() => {
      loadUserInfo();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: userInfo.value.avatar || "/static/images/default-avatar.png",
        b: common_vendor.t(userInfo.value.nickname || "微信用户"),
        c: common_vendor.t(userInfo.value.nickname ? "欢迎来到磁州同城" : "点击授权登录，享受更多服务"),
        d: common_vendor.o(handleUserCardClick),
        e: showAuthPopup.value
      }, showAuthPopup.value ? {
        f: common_vendor.o(closeAuthPopup),
        g: common_assets._imports_0$17,
        h: common_vendor.o(onUserInfoUpdated),
        i: common_vendor.p({
          buttonText: "一键授权"
        }),
        j: common_vendor.o(() => {
        }),
        k: common_vendor.o(closeAuthPopup)
      } : {}, {
        l: common_vendor.p({
          d: "M512 85.333c-235.52 0-426.667 191.147-426.667 426.667S276.48 938.667 512 938.667 938.667 747.52 938.667 512 747.52 85.333 512 85.333zm0 768c-188.16 0-341.333-153.173-341.333-341.333S323.84 170.667 512 170.667 853.333 323.84 853.333 512 700.16 853.333 512 853.333z",
          fill: "#FFFFFF"
        }),
        m: common_vendor.p({
          d: "M512 256c-23.68 0-42.667 19.2-42.667 42.667v213.333c0 11.52 4.693 22.187 12.373 29.867l149.333 149.333c16.64 16.64 43.733 16.64 60.373 0 16.64-16.64 16.64-43.733 0-60.373L554.667 494.08V298.667c0-23.467-18.987-42.667-42.667-42.667z",
          fill: "#FFFFFF"
        }),
        n: common_vendor.p({
          viewBox: "0 0 1024 1024",
          xmlns: "http://www.w3.org/2000/svg",
          width: "32",
          height: "32"
        }),
        o: common_vendor.o(($event) => navigateTo("/subPackages/activity-showcase/pages/index/index")),
        p: common_assets._imports_1$10,
        q: common_vendor.o(($event) => navigateTo("/subPackages/user/pages/my-benefits")),
        r: common_assets._imports_4$5,
        s: common_vendor.o(($event) => navigateTo("/subPackages/checkin/pages/points")),
        t: common_assets._imports_3$8,
        v: common_vendor.o(($event) => navigateTo("/subPackages/payment/pages/wallet")),
        w: common_assets._imports_4$6,
        x: common_vendor.o(($event) => navigateTo("/pages/user-center/profile")),
        y: common_assets._imports_5$4,
        z: common_vendor.o(navigateToCashback),
        A: common_assets._imports_6$4,
        B: common_vendor.o(($event) => navigateTo("/subPackages/user/pages/messages")),
        C: common_assets._imports_7$3,
        D: common_vendor.o(($event) => navigateTo("/subPackages/user/pages/group-orders")),
        E: common_assets._imports_3$9,
        F: common_vendor.o(($event) => navigateTo("/pages/my/publish")),
        G: common_assets._imports_9$3,
        H: common_vendor.o(($event) => navigateTo("/subPackages/merchant-admin-home/pages/merchant-home/index")),
        I: common_assets._imports_11$5,
        J: common_vendor.o(($event) => navigateTo("/pages/group/group")),
        K: common_assets._imports_11$6,
        L: common_assets._imports_0$14,
        M: common_vendor.o(($event) => navigateTo("/subPackages/user/pages/favorites")),
        N: common_assets._imports_1$11,
        O: common_assets._imports_0$14,
        P: common_vendor.o(($event) => navigateTo("/subPackages/user/pages/call-history")),
        Q: common_assets._imports_14$3,
        R: common_assets._imports_0$14,
        S: common_vendor.o(($event) => navigateTo("/subPackages/user/pages/history")),
        T: common_assets._imports_1$12,
        U: common_assets._imports_0$14,
        V: common_vendor.o(($event) => navigateTo("/subPackages/service/pages/coupon")),
        W: common_assets._imports_1$13,
        X: common_assets._imports_0$14,
        Y: common_vendor.o(goToRefreshPackage),
        Z: common_assets._imports_17$1,
        aa: common_vendor.o(($event) => navigateTo("/subPackages/distribution/pages/index")),
        ab: common_assets._imports_1$14,
        ac: common_vendor.o(($event) => navigateTo("/subPackages/partner/pages/partner")),
        ad: common_assets._imports_19,
        ae: common_vendor.o(($event) => navigateTo("/subPackages/franchise/pages/index"))
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/my.js.map
