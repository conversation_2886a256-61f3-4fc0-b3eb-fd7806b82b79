<template>
  <view class="post-list-container">
    <!-- 头部搜索区域 -->
    <view class="search-header">
      <view class="search-wrapper">
        <view class="search-bar">
          <input 
            class="search-input" 
            v-model="searchQuery"
            placeholder="搜索信息" 
            confirm-type="search"
            @confirm="handleSearch"
          />
          <text class="iconfont icon-search search-icon"></text>
          <button class="search-btn" @click="handleSearch">搜索</button>
        </view>
        <view class="filter-options">
          <picker 
            :range="categoryNames" 
            @change="handleCategoryPick"
            class="category-picker"
          >
            <view class="picker-item">
              <text>{{ currentCategoryName }}</text>
              <text class="iconfont icon-down"></text>
            </view>
          </picker>
          
          <picker 
            :range="sortOptionNames" 
            @change="handleSortPick"
            class="sort-picker"
          >
            <view class="picker-item">
              <text>{{ currentSortName }}</text>
              <text class="iconfont icon-down"></text>
            </view>
          </picker>
          
          <button type="primary" size="mini" class="publish-btn" @click="createPost">
            <text class="iconfont icon-add"></text> 发布信息
          </button>
        </view>
      </view>
    </view>

    <view class="main-content">
      <!-- 左侧分类导航 -->
      <view class="category-sidebar">
        <view class="sidebar-card">
          <view class="sidebar-title">信息分类</view>
          <view class="category-list">
            <view 
              v-for="category in categories" 
              :key="category.value"
              :class="{ active: selectedCategory === category.value }"
              class="category-item"
              @click="selectCategory(category.value)"
            >
              <text class="iconfont" :class="category.icon"></text>
              <text class="category-label">{{ category.label }}</text>
              <text class="category-count">{{ category.count }}</text>
            </view>
          </view>
        </view>

        <view class="sidebar-card">
          <view class="sidebar-title">热门标签</view>
          <view class="tag-cloud">
            <view
              v-for="tag in hotTags"
              :key="tag.name"
              :class="['tag-item', tag.type]"
              @click="handleTagClick(tag.name)"
            >
              {{ tag.name }}
            </view>
          </view>
        </view>
      </view>

      <!-- 右侧信息列表 -->
      <view class="post-content">
        <!-- 顶部推荐信息 -->
        <view class="featured-posts" v-if="featuredPosts.length > 0">
          <view class="section-title">推荐信息</view>
          <swiper 
            class="featured-swiper" 
            indicator-dots 
            circular 
            :autoplay="true" 
            :interval="5000" 
            :duration="500"
          >
            <swiper-item v-for="post in featuredPosts" :key="post.id" @click="viewPost(post.id)">
              <view class="featured-post-card" :style="{ backgroundImage: `url(${post.coverImage})` }">
                <view class="featured-overlay">
                  <view class="featured-content">
                    <view class="featured-badge">推荐</view>
                    <view class="featured-title">{{ post.title }}</view>
                    <view class="featured-desc">{{ post.description }}</view>
                  </view>
                </view>
              </view>
            </swiper-item>
          </swiper>
        </view>

        <!-- 信息列表 -->
        <view class="post-list-section">
          <view class="section-title">
            {{ selectedCategory ? getCategoryLabel(selectedCategory) : '所有信息' }}
            <text class="post-count">共{{ totalPosts }}条</text>
          </view>
          
          <!-- 列表为空的提示 -->
          <view v-if="posts.length === 0" class="empty-list">
            <image src="https://via.placeholder.com/200?text=Empty" class="empty-image"></image>
            <text class="empty-text">暂无相关信息</text>
            <button type="primary" size="mini" class="post-btn" @click="createPost">发布信息</button>
          </view>
          
          <!-- 信息列表 -->
          <view v-else class="post-list">
            <view class="post-card" v-for="post in posts" :key="post.id" @click="viewPost(post.id)">
              <view class="post-image" v-if="post.coverImage">
                <image :src="post.coverImage" :alt="post.title" mode="aspectFill"></image>
                <view class="post-badge" v-if="post.badge">{{ post.badge }}</view>
              </view>
              <view class="post-info">
                <view class="post-title">{{ post.title }}</view>
                <view class="post-desc">{{ post.description }}</view>
                <view class="post-meta">
                  <view class="post-category">
                    <uni-tag :text="post.category" size="small" type="default" class="category-tag"></uni-tag>
                  </view>
                  <view class="post-stats">
                    <text class="post-price" v-if="post.price">¥{{ post.price }}</text>
                    <view class="post-views">
                      <text class="iconfont icon-eye"></text>
                      <text>{{ post.views }}</text>
                    </view>
                    <text class="post-time">{{ formatTime(post.createdAt) }}</text>
                  </view>
                </view>
                <view class="post-footer">
                  <view class="publisher-info">
                    <image class="publisher-avatar" :src="post.publisher.avatar" mode="aspectFill"></image>
                    <text class="publisher-name">{{ post.publisher.username }}</text>
                  </view>
                  <view class="action-buttons">
                    <view class="action-btn" @click.stop="sharePost(post.id)">
                      <text class="iconfont icon-share"></text>
                    </view>
                    <view class="action-btn" @click.stop="favoritePost(post.id)">
                      <text class="iconfont icon-star"></text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 分页 -->
          <view class="pagination-container" v-if="posts.length > 0">
            <uni-pagination
              :total="totalPosts"
              :pageSize="pageSize"
              :current="currentPage"
              @change="handlePageChange"
            ></uni-pagination>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedSort = ref('newest')
const currentPage = ref(1)
const pageSize = ref(10)
const totalPosts = ref(50)

// 分类选项
const categories = ref([
  { label: '房屋出租', value: 'house-rent', icon: 'icon-home', count: 128 },
  { label: '招聘求职', value: 'jobs', icon: 'icon-user', count: 96 },
  { label: '二手交易', value: 'second-hand', icon: 'icon-shop', count: 245 },
  { label: '本地服务', value: 'local-service', icon: 'icon-service', count: 84 },
  { label: '交友活动', value: 'social', icon: 'icon-coffee', count: 56 },
  { label: '宠物服务', value: 'pets', icon: 'icon-pet', count: 38 }
])

// 排序选项
const sortOptions = ref([
  { label: '最新发布', value: 'newest' },
  { label: '价格从低到高', value: 'price-asc' },
  { label: '价格从高到低', value: 'price-desc' },
  { label: '浏览量最高', value: 'popular' }
])

// 热门标签
const hotTags = ref([
  { name: '精装修', type: '' },
  { name: '急招', type: 'warning' },
  { name: '全新', type: 'success' },
  { name: '包邮', type: 'info' },
  { name: '免费', type: 'danger' },
  { name: '周末', type: '' },
  { name: '兼职', type: 'warning' },
  { name: '附近', type: 'info' },
  { name: '学生', type: '' },
  { name: '家政', type: '' },
  { name: '健身', type: 'success' },
  { name: '培训', type: 'warning' }
])

// 推荐信息数据
const featuredPosts = ref([
  {
    id: 1,
    title: '精装三室两厅，近地铁站，拎包入住',
    description: '市中心精装三室，家电齐全，交通便利，适合一家人居住',
    coverImage: 'https://via.placeholder.com/800x400?text=Featured+1',
    price: '3800/月',
    category: '房屋出租'
  },
  {
    id: 2,
    title: '高薪招聘Java开发工程师，五险一金',
    description: '知名互联网公司招聘Java工程师，提供具有竞争力的薪资和福利',
    coverImage: 'https://via.placeholder.com/800x400?text=Featured+2',
    price: '15k-25k',
    category: '招聘求职'
  },
  {
    id: 3,
    title: '全新iPhone 13 Pro Max，256G，国行未拆封',
    description: '全新未拆封，购买后不喜欢，现低价转让，可当面交易',
    coverImage: 'https://via.placeholder.com/800x400?text=Featured+3',
    price: '7999',
    category: '二手交易'
  }
])

// 信息列表数据
const posts = ref([
  {
    id: 101,
    title: '地铁口两室一厅，精装修，家电齐全',
    description: '小区环境优美，地理位置好，出行方便，房屋南北通透，采光好',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+1',
    price: '2800/月',
    category: '房屋出租',
    views: 568,
    createdAt: Date.now() - 3600000 * 5,
    badge: '精选',
    publisher: {
      username: '安心房产',
      avatar: 'https://via.placeholder.com/40?text=User1'
    }
  },
  {
    id: 102,
    title: '诚聘前端开发工程师，有Vue经验优先',
    description: '负责公司产品的前端开发，要求熟悉Vue、React等前端框架，有2年以上工作经验',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+2',
    price: '12k-18k',
    category: '招聘求职',
    views: 325,
    createdAt: Date.now() - 3600000 * 12,
    publisher: {
      username: '科技招聘官',
      avatar: 'https://via.placeholder.com/40?text=User2'
    }
  },
  {
    id: 103,
    title: '9成新MacBook Pro 2021款',
    description: '去年购买，因换新电脑出售，M1芯片，16G内存，512G硬盘，性能强劲',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+3',
    price: '8500',
    category: '二手交易',
    views: 724,
    createdAt: Date.now() - 3600000 * 24,
    badge: '急售',
    publisher: {
      username: '数码达人',
      avatar: 'https://via.placeholder.com/40?text=User3'
    }
  },
  {
    id: 104,
    title: '专业上门维修家电，价格实惠',
    description: '专业维修各类家电，包括空调、冰箱、洗衣机等，多年经验，服务周到',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+4',
    price: '上门费50元起',
    category: '本地服务',
    views: 461,
    createdAt: Date.now() - 3600000 * 36,
    publisher: {
      username: '家电维修师',
      avatar: 'https://via.placeholder.com/40?text=User4'
    }
  },
  {
    id: 105,
    title: '周末爬山活动，结交新朋友',
    description: '本周六组织爬山活动，欢迎热爱户外运动的朋友一起参加，结交新朋友',
    coverImage: 'https://via.placeholder.com/300x200?text=Post+5',
    category: '交友活动',
    views: 298,
    createdAt: Date.now() - 3600000 * 48,
    publisher: {
      username: '户外俱乐部',
      avatar: 'https://via.placeholder.com/40?text=User5'
    }
  }
])

// 计算属性
const categoryNames = computed(() => {
  const names = ['所有类别']
  categories.value.forEach(category => names.push(category.label))
  return names
})

const sortOptionNames = computed(() => {
  return sortOptions.value.map(option => option.label)
})

const currentCategoryName = computed(() => {
  if (!selectedCategory.value) return '所有类别'
  const category = categories.value.find(item => item.value === selectedCategory.value)
  return category ? category.label : '所有类别'
})

const currentSortName = computed(() => {
  const sortOption = sortOptions.value.find(item => item.value === selectedSort.value)
  return sortOption ? sortOption.label : '最新发布'
})

// 生命周期钩子
onMounted(() => {
  // 处理页面加载参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.$page?.options || {}
  
  if (options.category) {
    selectedCategory.value = options.category
  }
  
  if (options.tag) {
    searchQuery.value = options.tag
    handleSearch()
  }
  
  // 加载数据
  loadData()
})

// 方法
const getCategoryLabel = (value) => {
  const category = categories.value.find(item => item.value === value)
  return category ? category.label : '所有信息'
}

const formatTime = (time) => {
  const now = Date.now()
  const diff = now - time
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return Math.floor(diff / 60000) + '分钟前'
  } else if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前'
  } else if (diff < 2592000000) {
    return Math.floor(diff / 86400000) + '天前'
  } else if (diff < 31536000000) {
    return Math.floor(diff / 2592000000) + '个月前'
  } else {
    return Math.floor(diff / 31536000000) + '年前'
  }
}

const loadData = () => {
  console.log('加载信息列表数据')
  // 实际项目中这里应该调用API获取数据
  
  uni.showLoading({
    title: '加载中...'
  })
  
  setTimeout(() => {
    uni.hideLoading()
  }, 500)
}

const handleSearch = () => {
  currentPage.value = 1
  uni.showToast({
    title: `搜索：${searchQuery.value}`,
    icon: 'none'
  })
  loadData()
}

const handleCategoryPick = (e) => {
  const index = e.detail.value
  if (index === 0) {
    selectedCategory.value = ''
  } else {
    selectedCategory.value = categories.value[index - 1].value
  }
  currentPage.value = 1
  loadData()
}

const selectCategory = (value) => {
  selectedCategory.value = value
  currentPage.value = 1
  uni.showToast({
    title: `切换到分类：${getCategoryLabel(value)}`,
    icon: 'none'
  })
  loadData()
}

const handleSortPick = (e) => {
  const index = e.detail.value
  selectedSort.value = sortOptions.value[index].value
  uni.showToast({
    title: `排序方式：${sortOptions.value[index].label}`,
    icon: 'none'
  })
  loadData()
}

const handleTagClick = (tag) => {
  searchQuery.value = tag
  handleSearch()
}

const handlePageChange = (e) => {
  currentPage.value = e.current
  uni.showToast({
    title: `加载第${currentPage.value}页`,
    icon: 'none'
  })
  loadData()
}

const viewPost = (id) => {
  uni.navigateTo({
    url: `/pages/publish/info-detail?id=${id}`
  })
}

const createPost = () => {
  uni.navigateTo({
    url: '/pages/publish/publish'
  })
}

const sharePost = (id) => {
  uni.showActionSheet({
    itemList: ['分享到微信', '复制链接', '生成分享海报'],
    success: function (res) {
      uni.showToast({
        title: '分享功能待实现',
        icon: 'none'
      })
    }
  })
}

const favoritePost = (id) => {
  uni.showToast({
    title: '收藏成功',
    icon: 'success'
  })
}
</script>

<style>
/* 全局样式 */
.post-list-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 搜索头部样式 */
.search-header {
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 30rpx 0;
  box-shadow: 0 2rpx 20rpx 0 rgba(0, 0, 0, 0.05);
}

.search-wrapper {
  padding: 0 30rpx;
}

.search-bar {
  position: relative;
  margin-bottom: 20rpx;
}

.search-input {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 140rpx 0 70rpx;
  font-size: 28rpx;
}

.search-icon {
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 32rpx;
}

.search-btn {
  position: absolute;
  right: 0;
  top: 0;
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 30rpx;
  border-radius: 0 40rpx 40rpx 0;
  background-color: #0066FF;
  color: #fff;
  font-size: 28rpx;
}

.filter-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-item {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 15rpx 25rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
}

.picker-item .iconfont {
  font-size: 24rpx;
  margin-left: 10rpx;
  color: #666;
}

.publish-btn {
  border-radius: 30rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
}

/* 主内容区样式 */
.main-content {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

/* 在大屏幕上采用横向布局 */
@media screen and (min-width: 768px) {
  .main-content {
    flex-direction: row;
    gap: 30rpx;
  }
  
  .category-sidebar {
    width: 250rpx;
    flex-shrink: 0;
  }
  
 .post-content {
    flex: 1;
  }
}

/* 侧边栏样式 */
.sidebar-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.sidebar-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-list {
  display: flex;
  flex-direction: column;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 20rpx 15rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  transition: all 0.3s;
}

.category-item:hover, .category-item.active {
  background-color: #ecf5ff;
  color: #0066FF;
}

.category-item .iconfont {
  font-size: 36rpx;
  margin-right: 15rpx;
}

.category-label {
  flex: 1;
  font-size: 28rpx;
}

.category-count {
  background-color: #f0f0f0;
  border-radius: 20rpx;
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  color: #666;
}

.category-item.active .category-count {
  background-color: #0066FF;
  color: #fff;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  background-color: #f5f5f5;
  color: #666;
  padding: 8rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  transition: all 0.3s;
}

.tag-item:hover {
  background-color: #e0e0e0;
}

.tag-item.warning {
  background-color: #fef6e9;
  color: #e6a23c;
}

.tag-item.success {
  background-color: #f0f9eb;
  color: #67c23a;
}

.tag-item.info {
  background-color: #f4f4f5;
  color: #909399;
}

.tag-item.danger {
  background-color: #fef0f0;
  color: #f56c6c;
}

/* 右侧内容区 */
.post-content {
  flex: 1;
}

/* 推荐信息样式 */
.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.post-count {
  font-size: 26rpx;
  color: #909399;
  font-weight: normal;
  margin-left: 15rpx;
}

.featured-posts {
  margin-bottom: 30rpx;
}

.featured-swiper {
  height: 340rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.featured-post-card {
  height: 100%;
  border-radius: 16rpx;
  background-size: cover;
  background-position: center;
  position: relative;
}

.featured-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 60rpx 30rpx 30rpx;
  border-radius: 0 0 16rpx 16rpx;
}

.featured-badge {
  position: absolute;
  top: -50rpx;
  right: 30rpx;
  background-color: #f56c6c;
  color: #fff;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.featured-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 15rpx;
}

.featured-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 信息列表样式 */
.post-list-section {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.empty-list {
  padding: 60rpx 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #909399;
  margin-bottom: 30rpx;
}

.post-btn {
  width: 240rpx;
}

.post-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.post-card {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s;
  border: 1rpx solid #f0f0f0;
}

.post-card:active {
  transform: translateY(-3rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
}

.post-image {
  width: 220rpx;
  height: 180rpx;
  flex-shrink: 0;
  position: relative;
}

.post-image image {
  width: 100%;
  height: 100%;
}

.post-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: rgba(245, 108, 108, 0.9);
  color: #fff;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

.post-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.post-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.post-desc {
  font-size: 26rpx;
  color: #606266;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  flex-grow: 1;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.category-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
}

.post-stats {
  display: flex;
  align-items: center;
  gap: 20rpx;
  font-size: 24rpx;
  color: #909399;
}

.post-price {
  color: #f56c6c;
  font-weight: bold;
}

.post-views {
  display: flex;
  align-items: center;
}

.post-views .iconfont {
  margin-right: 6rpx;
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12rpx;
  border-top: 1rpx solid #f0f0f0;
}

.publisher-info {
  display: flex;
  align-items: center;
}

.publisher-avatar {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.publisher-name {
  font-size: 24rpx;
  color: #606266;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  font-size: 36rpx;
  color: #909399;
}

/* 分页样式 */
.pagination-container {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

/* 小屏幕适配 */
@media screen and (max-width: 375px) {
  .post-card {
    flex-direction: column;
  }
  
  .post-image {
    width: 100%;
    height: 220rpx;
  }
  
  .filter-options {
    flex-wrap: wrap;
    gap: 15rpx;
  }
  
  .category-picker, .sort-picker, .publish-btn {
    flex: 1;
  }
}
</style> 