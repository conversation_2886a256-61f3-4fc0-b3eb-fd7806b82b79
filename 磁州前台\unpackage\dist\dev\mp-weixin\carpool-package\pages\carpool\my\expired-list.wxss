
.expired-list-container.data-v-3545965b {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-navbar.data-v-3545965b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #9E9E9E, #757575);
  z-index: 100;
}
.navbar-content.data-v-3545965b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 15px;
}
.navbar-left.data-v-3545965b {
  width: 50px;
}
.back-icon.data-v-3545965b {
  width: 20px;
  height: 20px;
}
.navbar-title.data-v-3545965b {
  flex: 1;
  text-align: center;
  color: #FFFFFF;
  font-size: 18px;
  font-weight: 500;
}
.navbar-right.data-v-3545965b {
  width: 50px;
  text-align: right;
}
.clear-text.data-v-3545965b {
  font-size: 14px;
  color: #FFFFFF;
}

/* 内容区域 */
.scrollable-content.data-v-3545965b {
  flex: 1;
  margin-top: calc(44px + var(--status-bar-height));
  padding: 12px;
}

/* 卡片列表 */
.card-list.data-v-3545965b {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.card-item.data-v-3545965b {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 卡片头部 */
.card-header.data-v-3545965b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #F8FAFB;
  border-bottom: 1px solid #EEEEEE;
}
.header-left.data-v-3545965b {
  display: flex;
  align-items: center;
  gap: 8px;
}
.card-type.data-v-3545965b {
  font-size: 15px;
  font-weight: 500;
  color: #333333;
}
.publish-time.data-v-3545965b {
  font-size: 12px;
  color: #999999;
}
.status-tag.data-v-3545965b {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}
.status-expired.data-v-3545965b {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999999;
}

/* 卡片内容 */
.card-content.data-v-3545965b {
  padding: 16px;
}
.route-info.data-v-3545965b {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.route-points.data-v-3545965b {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.start-point.data-v-3545965b, .end-point.data-v-3545965b {
  display: flex;
  align-items: center;
  gap: 10px;
}
.point-marker.data-v-3545965b {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.start.data-v-3545965b {
  background-color: #1677FF;
}
.end.data-v-3545965b {
  background-color: #FF5722;
}
.route-line.data-v-3545965b {
  width: 2px;
  height: 20px;
  background-color: #DDDDDD;
  margin-left: 5px;
}
.point-text.data-v-3545965b {
  font-size: 16px;
  color: #333333;
}
.trip-info.data-v-3545965b {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}
.info-item.data-v-3545965b {
  display: flex;
  align-items: center;
  gap: 6px;
}
.info-icon.data-v-3545965b {
  width: 24px;
  height: 24px;
}
.info-text.data-v-3545965b {
  font-size: 14px;
  color: #666666;
}
.price.data-v-3545965b {
  color: #FF5722;
  font-weight: 500;
}

/* 过期信息 */
.expired-info.data-v-3545965b {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #EEEEEE;
}
.expired-icon.data-v-3545965b {
  width: 24px;
  height: 24px;
}
.expired-text.data-v-3545965b {
  font-size: 12px;
  color: #999999;
}

/* 卡片底部按钮 */
.card-actions.data-v-3545965b {
  display: flex;
  justify-content: flex-end;
  padding: 20px 24px;
  gap: 20px;
  border-top: 1px solid #EEEEEE;
}
.action-button.data-v-3545965b {
  padding: 10px 20px;
  font-size: 16px;
  height: 44px;
  min-width: 90px;
  border-radius: 6px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-button.outline.data-v-3545965b {
  color: #666666;
  border: 1px solid #DDDDDD;
}
.action-button.primary.data-v-3545965b {
  color: #FFFFFF;
  background-color: #1677FF;
  border: 1px solid #1677FF;
}

/* 空状态 */
.empty-state.data-v-3545965b {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}
.empty-image.data-v-3545965b {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}
.empty-text.data-v-3545965b {
  font-size: 16px;
  color: #999999;
  margin-bottom: 20px;
}

/* 加载状态 */
.loading-state.data-v-3545965b {
  padding: 16px 0;
  text-align: center;
}
.loading-text.data-v-3545965b {
  font-size: 14px;
  color: #999999;
}

/* 列表底部 */
.list-bottom.data-v-3545965b {
  padding: 16px 0;
  text-align: center;
}
.bottom-text.data-v-3545965b {
  font-size: 14px;
  color: #999999;
}
