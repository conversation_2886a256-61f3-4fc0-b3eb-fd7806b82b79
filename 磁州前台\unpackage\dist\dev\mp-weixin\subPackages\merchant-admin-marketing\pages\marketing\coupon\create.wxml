<view class="coupon-create-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">创建优惠券</text><view class="navbar-right"><view class="help-icon" bindtap="{{b}}">?</view></view></view><scroll-view scroll-y class="form-container"><view class="type-selection-section"><view wx:for="{{c}}" wx:for-item="type" wx:key="n" class="{{['type-option', type.o && 'active']}}" bindtap="{{type.p}}"><view class="{{['type-icon', type.f]}}"><svg wx:if="{{h}}" u-s="{{['d']}}" u-i="{{type.e}}" bind:__l="__l" u-p="{{h}}"><path wx:if="{{d}}" u-i="{{type.a}}" bind:__l="__l" u-p="{{d}}"></path><path wx:if="{{e}}" u-i="{{type.b}}" bind:__l="__l" u-p="{{e}}"></path><line wx:if="{{f}}" u-i="{{type.c}}" bind:__l="__l" u-p="{{f}}"></line><line wx:if="{{g}}" u-i="{{type.d}}" bind:__l="__l" u-p="{{g}}"></line></svg></view><text class="type-name">{{type.g}}</text><text class="type-desc">{{type.h}}</text><view wx:if="{{type.i}}" class="type-check"><svg wx:if="{{type.m}}" u-s="{{['d']}}" u-i="{{type.l}}" bind:__l="__l" u-p="{{type.m}}"><polyline wx:if="{{type.k}}" u-i="{{type.j}}" bind:__l="__l" u-p="{{type.k}}"></polyline></svg></view></view></view><view class="form-section"><view class="section-header"><text class="section-title">基本信息</text></view><view class="form-item"><text class="item-label">优惠券名称</text><input type="text" class="item-input" placeholder="请输入优惠券名称" value="{{i}}" bindinput="{{j}}"/></view><view wx:if="{{k}}" class="form-item"><text class="item-label">优惠金额</text><view class="amount-input-wrapper"><text class="amount-symbol">¥</text><input type="digit" class="amount-input" placeholder="请输入优惠金额" value="{{l}}" bindinput="{{m}}"/></view></view><view wx:if="{{n}}" class="form-item"><text class="item-label">折扣比例</text><view class="amount-input-wrapper"><input type="digit" class="amount-input" placeholder="请输入折扣比例" value="{{o}}" bindinput="{{p}}"/><text class="amount-unit">折</text></view></view><view wx:if="{{q}}" class="form-item"><text class="item-label">使用门槛</text><view class="amount-input-wrapper"><text class="threshold-text">满</text><input type="digit" class="amount-input" placeholder="请输入最低消费金额" value="{{r}}" bindinput="{{s}}"/><text class="threshold-text">元可用</text></view></view></view><view class="form-section"><view class="section-header"><text class="section-title">有效期设置</text></view><view class="form-item"><text class="item-label">有效期类型</text><view class="validity-type-selector"><view class="{{['type-option', v && 'active']}}" bindtap="{{w}}"><view class="option-radio"><view wx:if="{{t}}" class="radio-inner"></view></view><text class="option-text">固定日期</text></view><view class="{{['type-option', y && 'active']}}" bindtap="{{z}}"><view class="option-radio"><view wx:if="{{x}}" class="radio-inner"></view></view><text class="option-text">领取后生效</text></view></view></view><view wx:if="{{A}}" class="form-item"><text class="item-label">开始日期</text><view class="date-selector" bindtap="{{H}}"><text class="date-value">{{B}}</text><view class="date-icon"><svg wx:if="{{G}}" u-s="{{['d']}}" u-i="ea91bffe-7" bind:__l="__l" u-p="{{G}}"><rect wx:if="{{C}}" u-i="ea91bffe-8,ea91bffe-7" bind:__l="__l" u-p="{{C}}"></rect><line wx:if="{{D}}" u-i="ea91bffe-9,ea91bffe-7" bind:__l="__l" u-p="{{D}}"></line><line wx:if="{{E}}" u-i="ea91bffe-10,ea91bffe-7" bind:__l="__l" u-p="{{E}}"></line><line wx:if="{{F}}" u-i="ea91bffe-11,ea91bffe-7" bind:__l="__l" u-p="{{F}}"></line></svg></view></view></view><view wx:if="{{I}}" class="form-item"><text class="item-label">结束日期</text><view class="date-selector" bindtap="{{P}}"><text class="date-value">{{J}}</text><view class="date-icon"><svg wx:if="{{O}}" u-s="{{['d']}}" u-i="ea91bffe-12" bind:__l="__l" u-p="{{O}}"><rect wx:if="{{K}}" u-i="ea91bffe-13,ea91bffe-12" bind:__l="__l" u-p="{{K}}"></rect><line wx:if="{{L}}" u-i="ea91bffe-14,ea91bffe-12" bind:__l="__l" u-p="{{L}}"></line><line wx:if="{{M}}" u-i="ea91bffe-15,ea91bffe-12" bind:__l="__l" u-p="{{M}}"></line><line wx:if="{{N}}" u-i="ea91bffe-16,ea91bffe-12" bind:__l="__l" u-p="{{N}}"></line></svg></view></view></view><view wx:if="{{Q}}" class="form-item"><text class="item-label">有效天数</text><view class="days-input-wrapper"><input type="number" class="days-input" placeholder="请输入有效天数" value="{{R}}" bindinput="{{S}}"/><text class="days-text">天</text></view></view></view><view class="form-section"><view class="section-header"><text class="section-title">发放设置</text></view><view class="form-item"><text class="item-label">发放总量</text><view class="amount-input-wrapper"><input type="number" class="amount-input" placeholder="请输入发放总量" value="{{T}}" bindinput="{{U}}"/><text class="amount-unit">张</text></view></view><view class="form-item"><text class="item-label">每人限领</text><view class="amount-input-wrapper"><input type="number" class="amount-input" placeholder="请输入每人限领数量" value="{{V}}" bindinput="{{W}}"/><text class="amount-unit">张</text></view></view></view><view class="form-section"><view class="section-header"><text class="section-title">使用规则</text></view><view class="form-item"><text class="item-label">适用商品</text><view class="product-selector" bindtap="{{Y}}"><text class="selector-value">{{X}}</text><view class="selector-arrow"></view></view></view><view class="form-item"><text class="item-label">使用说明</text><block wx:if="{{r0}}"><textarea class="item-textarea" placeholder="请输入使用说明" value="{{Z}}" bindinput="{{aa}}"/></block></view></view><view wx:if="{{ab}}" class="form-section"><view class="section-header"><text class="section-title">分销设置</text><text class="section-subtitle">设置优惠券分销佣金</text></view><distribution-setting wx:if="{{ad}}" bindupdateSettings="{{ac}}" u-i="ea91bffe-17" bind:__l="__l" u-p="{{ad}}"/></view><view class="form-section"><view class="section-header"><text class="section-title">活动推广</text><text class="section-subtitle">选择发布方式</text></view><marketing-promotion-actions wx:if="{{af}}" bindactionCompleted="{{ae}}" u-i="ea91bffe-18" bind:__l="__l" u-p="{{af}}"/></view></scroll-view><view class="bottom-action-bar"><view class="action-button preview" bindtap="{{ag}}"><text class="button-text">预览</text></view><view class="action-button create" bindtap="{{ah}}"><text class="button-text">创建</text></view></view></view>