"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "2023-04-01 ~ 2023-04-30",
      distributionEnabled: true,
      // 分销数据概览
      distributionData: {
        totalDistributors: 268,
        distributorsTrend: "up",
        distributorsGrowth: "12%",
        totalCommission: 26584.5,
        commissionTrend: "up",
        commissionGrowth: "8.5%",
        totalOrders: 1256,
        ordersTrend: "up",
        ordersGrowth: "15.2%",
        averageCommission: 99.2,
        averageTrend: "down",
        averageGrowth: "3.1%"
      },
      // 分销员排行榜
      topDistributors: [
        {
          id: 1,
          name: "张小明",
          avatar: "/static/images/avatar-1.png",
          level: "钻石分销员",
          commission: 5862.5,
          orders: 86
        },
        {
          id: 2,
          name: "李佳怡",
          avatar: "/static/images/avatar-2.png",
          level: "金牌分销员",
          commission: 4235.8,
          orders: 64
        },
        {
          id: 3,
          name: "王大力",
          avatar: "/static/images/avatar-3.png",
          level: "金牌分销员",
          commission: 3756.2,
          orders: 58
        },
        {
          id: 4,
          name: "赵丽丽",
          avatar: "/static/images/avatar-4.png",
          level: "银牌分销员",
          commission: 2845.6,
          orders: 42
        },
        {
          id: 5,
          name: "陈小红",
          avatar: "/static/images/avatar-5.png",
          level: "银牌分销员",
          commission: 2356.8,
          orders: 35
        }
      ],
      // 分销设置
      distributionSettings: {
        conditionText: "购买商品并申请",
        levelCount: 3,
        minWithdrawal: 50
      },
      // 佣金规则
      commissionRules: {
        level1: 15,
        level2: 5,
        level3: 2
      },
      // 推广工具
      promotionTools: [
        {
          id: 1,
          name: "推广海报",
          description: "生成专属推广海报",
          icon: "/static/images/poster-icon.png"
        },
        {
          id: 2,
          name: "推广二维码",
          description: "生成专属推广二维码",
          icon: "/static/images/qrcode-icon.png"
        },
        {
          id: 3,
          name: "商品推广卡片",
          description: "生成商品推广卡片",
          icon: "/static/images/product-card-icon.png"
        },
        {
          id: 4,
          name: "分销员邀请",
          description: "邀请好友成为分销员",
          icon: "/static/images/invite-icon.png"
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showModal({
        title: "分销系统帮助",
        content: "分销系统是一种营销模式，通过发展分销员推广商品，获取佣金的方式促进销售增长。",
        showCancel: false
      });
    },
    formatNumber(number) {
      return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    showDatePicker() {
      common_vendor.index.showToast({
        title: "日期选择功能开发中",
        icon: "none"
      });
    },
    viewAllDistributors() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-customer/pages/marketing/distribution/distributors"
      });
    },
    viewDistributorDetail(distributor) {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${distributor.id}`
      });
    },
    toggleDistribution(e) {
      this.distributionEnabled = e.detail.value;
      common_vendor.index.showToast({
        title: this.distributionEnabled ? "分销功能已开启" : "分销功能已关闭",
        icon: "none"
      });
    },
    navigateToSetting(type) {
      const routes = {
        conditions: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/conditions",
        levels: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/levels",
        withdrawal: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/withdrawal",
        agreement: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/agreement"
      };
      common_vendor.index.navigateTo({
        url: routes[type]
      });
    },
    editCommissionRules() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/commission-rules"
      });
    },
    usePromotionTool(tool) {
      let url = "";
      switch (tool.id) {
        case 1:
          url = "/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool?id=1&name=推广海报";
          break;
        case 2:
          url = "/subPackages/merchant-admin-marketing/pages/marketing/distribution/qrcode";
          break;
        case 3:
          url = "/subPackages/merchant-admin-marketing/pages/marketing/distribution/product-cards";
          break;
        case 4:
          url = "/subPackages/merchant-admin-marketing/pages/marketing/distribution/channels";
          break;
        default:
          url = `/subPackages/merchant-admin-marketing/pages/marketing/distribution/promotion-tool?id=${tool.id}&name=${tool.name}`;
      }
      common_vendor.index.navigateTo({
        url
      });
    },
    showActionMenu() {
      common_vendor.index.showActionSheet({
        itemList: ["新增分销员", "导出分销数据", "佣金发放", "分销活动创建"],
        success: (res) => {
          const actions = [
            () => this.addDistributor(),
            () => this.exportData(),
            () => this.payCommission(),
            () => this.createActivity()
          ];
          actions[res.tapIndex]();
        }
      });
    },
    addDistributor() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/add-distributor"
      });
    },
    exportData() {
      common_vendor.index.showLoading({
        title: "导出中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "数据导出成功",
          icon: "success"
        });
      }, 1500);
    },
    payCommission() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/pay-commission"
      });
    },
    createActivity() {
      common_vendor.index.navigateTo({
        url: "/subPackages/merchant-admin-marketing/pages/marketing/distribution/create-activity"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.t($data.dateRange),
    d: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    e: common_vendor.t($data.distributionData.totalDistributors),
    f: common_vendor.t($data.distributionData.distributorsGrowth),
    g: common_vendor.n($data.distributionData.distributorsTrend),
    h: common_vendor.t($options.formatNumber($data.distributionData.totalCommission)),
    i: common_vendor.t($data.distributionData.commissionGrowth),
    j: common_vendor.n($data.distributionData.commissionTrend),
    k: common_vendor.t($data.distributionData.totalOrders),
    l: common_vendor.t($data.distributionData.ordersGrowth),
    m: common_vendor.n($data.distributionData.ordersTrend),
    n: common_vendor.t($options.formatNumber($data.distributionData.averageCommission)),
    o: common_vendor.t($data.distributionData.averageGrowth),
    p: common_vendor.n($data.distributionData.averageTrend),
    q: common_vendor.o((...args) => $options.viewAllDistributors && $options.viewAllDistributors(...args)),
    r: common_vendor.f($data.topDistributors, (item, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: index < 3 ? 1 : "",
        c: item.avatar,
        d: common_vendor.t(item.name),
        e: common_vendor.t(item.level),
        f: common_vendor.t($options.formatNumber(item.commission)),
        g: common_vendor.t(item.orders),
        h: index,
        i: common_vendor.o(($event) => $options.viewDistributorDetail(item), index)
      };
    }),
    s: $data.distributionEnabled,
    t: common_vendor.o((...args) => $options.toggleDistribution && $options.toggleDistribution(...args)),
    v: common_vendor.t($data.distributionSettings.conditionText),
    w: common_vendor.o(($event) => $options.navigateToSetting("conditions")),
    x: common_vendor.t($data.distributionSettings.levelCount),
    y: common_vendor.o(($event) => $options.navigateToSetting("levels")),
    z: common_vendor.t($data.distributionSettings.minWithdrawal),
    A: common_vendor.o(($event) => $options.navigateToSetting("withdrawal")),
    B: common_vendor.o(($event) => $options.navigateToSetting("agreement")),
    C: common_vendor.o((...args) => $options.editCommissionRules && $options.editCommissionRules(...args)),
    D: common_vendor.t($data.commissionRules.level1),
    E: common_vendor.t($data.commissionRules.level2),
    F: $data.commissionRules.level3 > 0
  }, $data.commissionRules.level3 > 0 ? {
    G: common_vendor.t($data.commissionRules.level3)
  } : {}, {
    H: common_vendor.f($data.promotionTools, (tool, index, i0) => {
      return {
        a: tool.icon,
        b: common_vendor.t(tool.name),
        c: common_vendor.t(tool.description),
        d: index,
        e: common_vendor.o(($event) => $options.usePromotionTool(tool), index)
      };
    }),
    I: common_vendor.o((...args) => $options.showActionMenu && $options.showActionMenu(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-customer/pages/marketing/distribution/index.js.map
