{"version": 3, "file": "detail.js", "sources": ["pages/publish/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9kZXRhaWwudnVl"], "sourcesContent": ["<template>\n\t<view class=\"publish-detail\" :class=\"categoryType\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-navbar\">\n\t\t\t<view class=\"back-btn\" @click=\"navigateBack\">\n\t\t\t\t<image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"navbar-title\">发布{{ serviceTypeName ? serviceTypeName : categoryName }}</view>\n\t\t\t<view class=\"navbar-right\">\n\t\t\t\t<!-- 删除原来的按钮 -->\n\t\t\t</view>\n\t\t</view>\n\t\t\t\t\t\n\t\t<!-- 免责声明 -->\n\t\t<view class=\"disclaimer\">\n\t\t\t<text class=\"disclaimer-text\">免责声明：本平台发布的所有信息展示，内容本身与平台本身无关，平台不负任何责任。</text>\n\t\t</view>\n\t\t\t\t\t\n\t\t<!-- 表单内容区域 -->\n\t\t<scroll-view scroll-y class=\"content-scroll\">\n\t\t\t<view class=\"form-container\">\n\n\t\t\t\t\n\t\t\t\t<!-- 动态渲染表单 -->\n\t\t\t\t<view class=\"form-section\" v-for=\"(section, sectionIndex) in formConfig.sections\" :key=\"sectionIndex\">\n\t\t\t\t\t<view class=\"form-item\" v-if=\"section.label\">\n\t\t\t\t\t\t<view class=\"section-title\">{{ section.label }}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<template v-for=\"(field, fieldIndex) in section.fields\" :key=\"fieldIndex\">\n\t\t\t\t\t\t<view class=\"form-item\" v-if=\"shouldShowField(field)\">\n\t\t\t\t\t\t\t<!-- 输入框字段 -->\n\t\t\t\t\t\t\t<template v-if=\"field.type === 'input'\">\n\t\t\t\t\t\t\t\t<view class=\"form-label\" :class=\"{ required: field.required }\">{{ field.label }}</view>\n\t\t\t\t\t\t\t\t<view class=\"form-input-wrapper\">\n\t\t\t\t\t\t\t\t\t<input :type=\"field.inputType || 'text'\" class=\"form-input\" :placeholder=\"field.placeholder || '请输入'\" v-model=\"formData[field.name]\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 性别选择字段 -->\n\t\t\t\t\t\t\t<template v-else-if=\"field.type === 'gender'\">\n\t\t\t\t\t\t\t\t<view class=\"form-label\" :class=\"{ required: field.required }\">{{ field.label }}</view>\n\t\t\t\t\t\t\t\t<view class=\"gender-selector\">\n\t\t\t\t\t\t\t\t\t<view class=\"gender-option\" :class=\"{'gender-selected': formData[field.name] === '男'}\" @click=\"selectGender(field.name, '男')\">\n\t\t\t\t\t\t\t\t\t\t<text>男</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"gender-option\" :class=\"{'gender-selected': formData[field.name] === '女'}\" @click=\"selectGender(field.name, '女')\">\n\t\t\t\t\t\t\t\t\t\t<text>女</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 下拉选择字段 -->\n\t\t\t\t\t\t\t<template v-else-if=\"field.type === 'picker'\">\n\t\t\t\t\t\t\t\t<view class=\"form-label\" :class=\"{ required: field.required }\">{{ field.label }}</view>\n\t\t\t\t\t\t\t\t<view class=\"form-input-wrapper\">\n\t\t\t\t\t\t\t\t\t<input type=\"text\" class=\"form-input\" :placeholder=\"getPickerPlaceholder(field)\" v-model=\"formData[field.name]\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 定位输入字段 -->\n\t\t\t\t\t\t\t<template v-else-if=\"field.type === 'location'\">\n\t\t\t\t\t\t\t\t<view class=\"form-label\" :class=\"{ required: field.required }\">{{ field.label }}</view>\n\t\t\t\t\t\t\t\t<view class=\"form-input-wrapper location-input-wrapper\">\n\t\t\t\t\t\t\t\t\t<input class=\"form-input\" type=\"text\" \n\t\t\t\t\t\t\t\t\t\tv-model=\"formData[field.name]\"\n\t\t\t\t\t\t\t\t\t\t:placeholder=\"field.placeholder || '请输入地址或点击右侧定位'\" />\n\t\t\t\t\t\t\t\t\t<view class=\"location-btn\" @click=\"getLocation(field.name)\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"location-icon\" src=\"/static/images/tabbar/定位.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 文本区域字段 -->\n\t\t\t\t\t\t\t<template v-else-if=\"field.type === 'textarea'\">\n\t\t\t\t\t\t\t\t<view class=\"form-label\" :class=\"{ required: field.required }\">{{ field.label }}</view>\n\t\t\t\t\t\t\t\t<view class=\"textarea-wrapper\">\n\t\t\t\t\t\t\t\t\t<textarea class=\"form-textarea\" :placeholder=\"field.placeholder || '请输入'\" v-model=\"formData[field.name]\"></textarea>\n\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 上传图片字段 -->\n\t\t\t\t\t\t\t<template v-else-if=\"field.type === 'upload'\">\n\t\t\t\t\t\t\t\t<view class=\"form-label\" :class=\"{ required: field.required }\">{{ field.label }}</view>\n\t\t\t\t\t\t\t\t<view class=\"upload-wrapper\">\n\t\t\t\t\t\t\t\t\t<view class=\"upload-btn\" @click=\"chooseImage(field.name)\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"upload-btn-inner\">\n\t\t\t\t\t\t\t\t\t\t\t<image class=\"upload-icon\" src=\"/static/images/tabbar/上传.png\"></image>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"upload-text\">上传照片</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"upload-preview\" v-for=\"(item, index) in formData[field.name] || []\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t\t<image class=\"preview-image\" :src=\"item\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t\t\t<view class=\"delete-btn\" @click=\"deleteImage(field.name, index)\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"delete-icon\">×</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"upload-tip\" v-if=\"field.tip\">{{ field.tip }}</text>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 标签选择字段 -->\n\t\t\t\t\t\t\t<template v-else-if=\"field.type === 'tags'\">\n\t\t\t\t\t\t\t\t<view class=\"tag-section\">\n\t\t\t\t\t\t\t\t\t<view class=\"tag-title\">{{ field.label }}（最多选择{{ field.maxCount || 3 }}个）</view>\n\t\t\t\t\t\t\t\t\t<view class=\"tag-list\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"tag-item\" \n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(tag, tagIndex) in field.options\" \n\t\t\t\t\t\t\t\t\t\t\t:key=\"tagIndex\" \n\t\t\t\t\t\t\t\t\t\t\t:class=\"{ 'tag-active': isTagSelected(tag, field.name) }\" \n\t\t\t\t\t\t\t\t\t\t\t@click=\"toggleTag(tag, field.name, field.maxCount || 3)\">\n\t\t\t\t\t\t\t\t\t\t\t{{ tag }}\n\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 验证码字段 -->\n\t\t\t\t\t\t\t<template v-else-if=\"field.type === 'code'\">\n\t\t\t\t\t\t\t\t<view class=\"form-label\" :class=\"{ required: field.required }\">{{ field.label }}</view>\n\t\t\t\t\t\t\t\t<view class=\"form-input-wrapper code-input-wrapper\">\n\t\t\t\t\t\t\t\t\t<input type=\"number\" class=\"form-input code-input\" placeholder=\"请输入验证码\" v-model=\"formData[field.name]\" />\n\t\t\t\t\t\t\t\t\t<view class=\"code-btn\">获取验证码</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t</view>\n\t\t\t\t\t</template>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 红包功能区域 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"section-title\">红包设置</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"form-label\">是否开启红包</view>\n\t\t\t\t\t\t<switch \n\t\t\t\t\t\t\t:checked=\"formData.redPacketEnabled || false\" \n\t\t\t\t\t\t\tcolor=\"#FF4D4F\" \n\t\t\t\t\t\t\t@change=\"(e) => { formData.redPacketEnabled = e.detail.value }\"\n\t\t\t\t\t\t\tclass=\"switch-control\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"formData.redPacketEnabled\">\n\t\t\t\t\t\t<view class=\"form-label required\">红包金额</view>\n\t\t\t\t\t\t<view class=\"form-input-wrapper\">\n\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\ttype=\"digit\" \n\t\t\t\t\t\t\t\tv-model=\"formData.redPacketAmount\" \n\t\t\t\t\t\t\t\tplaceholder=\"请输入红包金额\" \n\t\t\t\t\t\t\t\tclass=\"form-input\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<text class=\"input-unit\">元</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"formData.redPacketEnabled\">\n\t\t\t\t\t\t<view class=\"form-label required\">红包个数</view>\n\t\t\t\t\t\t<view class=\"form-input-wrapper\">\n\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\ttype=\"number\" \n\t\t\t\t\t\t\t\tv-model=\"formData.redPacketCount\" \n\t\t\t\t\t\t\t\tplaceholder=\"请输入红包个数\" \n\t\t\t\t\t\t\t\tclass=\"form-input\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<text class=\"input-unit\">个</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"formData.redPacketEnabled\">\n\t\t\t\t\t\t<view class=\"form-label\">红包类型</view>\n\t\t\t\t\t\t<view class=\"radio-group\">\n\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"formData.redPacketType = 'average'\">\n\t\t\t\t\t\t\t\t<view class=\"radio-circle\" :class=\"{ active: formData.redPacketType === 'average' }\"></view>\n\t\t\t\t\t\t\t\t<text class=\"radio-text\">平均分配</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"formData.redPacketType = 'random'\">\n\t\t\t\t\t\t\t\t<view class=\"radio-circle\" :class=\"{ active: formData.redPacketType === 'random' }\"></view>\n\t\t\t\t\t\t\t\t<text class=\"radio-text\">随机金额</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"formData.redPacketEnabled\">\n\t\t\t\t\t\t<view class=\"form-label\">领取条件</view>\n\t\t\t\t\t\t<view class=\"checkbox-group\">\n\t\t\t\t\t\t\t<view class=\"checkbox-item\" @click=\"toggleCondition('share')\">\n\t\t\t\t\t\t\t\t<view class=\"checkbox-square\" :class=\"{ active: formData.redPacketConditions?.includes('share') }\"></view>\n\t\t\t\t\t\t\t\t<text class=\"checkbox-text\">转发分享</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"checkbox-item\" @click=\"toggleCondition('follow')\">\n\t\t\t\t\t\t\t\t<view class=\"checkbox-square\" :class=\"{ active: formData.redPacketConditions?.includes('follow') }\"></view>\n\t\t\t\t\t\t\t\t<text class=\"checkbox-text\">关注店铺</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"formData.redPacketEnabled\">\n\t\t\t\t\t\t<view class=\"form-label\">有效期</view>\n\t\t\t\t\t\t<view class=\"form-input-wrapper\" @click=\"showRedPacketDatePicker = true\">\n\t\t\t\t\t\t\t<view class=\"date-picker-text\">{{ formData.redPacketExpiry || '请选择红包有效期' }}</view>\n\t\t\t\t\t\t\t<image src=\"/static/images/tabbar/日历.png\" class=\"date-icon\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"form-item\" v-if=\"formData.redPacketEnabled\">\n\t\t\t\t\t\t<view class=\"form-label\">红包说明</view>\n\t\t\t\t\t\t<view class=\"form-input-wrapper\">\n\t\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\t\tv-model=\"formData.redPacketDesc\" \n\t\t\t\t\t\t\t\tplaceholder=\"请输入红包说明，如参与方式、注意事项等\" \n\t\t\t\t\t\t\t\tclass=\"form-textarea\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 添加红包提示词 -->\n\t\t\t\t\t<view class=\"redpacket-tips\" v-if=\"!formData.redPacketEnabled\">\n\t\t\t\t\t\t<image src=\"/static/images/tabbar/提示.png\" class=\"tips-icon\"></image>\n\t\t\t\t\t\t<text class=\"tips-text\">添加红包可提高信息曝光率，让更多人看到您的信息，转发分享后可抢红包，提高信息传播效果！</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 红包预览 -->\n\t\t\t\t\t<view class=\"red-packet-preview\" v-if=\"formData.redPacketEnabled && formData.redPacketAmount && formData.redPacketCount\">\n\t\t\t\t\t\t<view class=\"red-packet-preview-container\">\n\t\t\t\t\t\t\t<image class=\"red-packet-preview-bg\" src=\"/static/images/tabbar/红包背景.png\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t<view class=\"red-packet-preview-content\">\n\t\t\t\t\t\t\t\t<view class=\"red-packet-preview-title\">\n\t\t\t\t\t\t\t\t\t<image class=\"red-packet-preview-icon\" src=\"/static/images/tabbar/红包图标.png\"></image>\n\t\t\t\t\t\t\t\t\t<text>红包福利</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"red-packet-preview-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"red-packet-preview-amount\">¥{{ formData.redPacketAmount }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"red-packet-preview-desc\">{{ formData.redPacketType === 'average' ? '平均分配' : '随机金额' }} · {{ formData.redPacketCount }}个红包</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"red-packet-preview-conditions\" v-if=\"formData.redPacketConditions && formData.redPacketConditions.length > 0\">\n\t\t\t\t\t\t\t\t\t<text class=\"red-packet-preview-condition-label\">领取条件：</text>\n\t\t\t\t\t\t\t\t\t<text class=\"red-packet-preview-condition-item\" v-if=\"formData.redPacketConditions.includes('share')\">转发分享</text>\n\t\t\t\t\t\t\t\t\t<text class=\"red-packet-preview-condition-item\" v-if=\"formData.redPacketConditions.includes('follow')\">关注店铺</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 原广告位已被替换为ConfigurablePremiumActions组件 -->\n\t\t\t\t\n\t\t\t\t<!-- 协议 -->\n\t\t\t\t<view class=\"agreement-section\">\n\t\t\t\t\t<checkbox-group @change=\"e => formData.agree = e.detail.value.length > 0\">\n\t\t\t\t\t\t<label class=\"agreement-row\">\n\t\t\t\t\t\t\t<checkbox value=\"agree\" :checked=\"formData.agree\" />\n\t\t\t\t\t\t\t<text class=\"agreement-text\">我已阅读并同意</text>\n\t\t\t\t\t\t\t<text class=\"agreement-link\" @click.stop=\"showAgreement('service')\">《服务协议》</text>\n\t\t\t\t\t\t\t<text class=\"agreement-text\">和</text>\n\t\t\t\t\t\t\t<text class=\"agreement-link\" @click.stop=\"showAgreement('privacy')\">《隐私政策》</text>\n\t\t\t\t\t\t</label>\n\t\t\t\t\t</checkbox-group>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 发布方式选择 -->\n\t\t\t\t<view class=\"publish-method-section\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<view class=\"section-icon\">\n\t\t\t\t\t\t\t<image src=\"/static/images/premium/publish-icon.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"section-title\">选择发布方式</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<ConfigurablePremiumActions\n\t\t\t\t\t\tshowMode=\"direct\"\n\t\t\t\t\t\tpageType=\"publish\"\n\t\t\t\t\t\t:itemData=\"publishData\"\n\t\t\t\t\t\t@action-completed=\"handlePublishCompleted\"\n\t\t\t\t\t\t@action-cancelled=\"handlePublishCancelled\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 发布按钮 -->\n\t\t\t\t<view class=\"publish-btn-wrapper\">\n\t\t\t\t\t<button class=\"publish-btn\" :disabled=\"!formData.agree\" @click=\"submitForm\">确认发布</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, reactive } from 'vue';\nimport { onLoad } from '@dcloudio/uni-app';\nimport * as serviceFormConfig from '../../utils/service-form-config.js';\nimport { checkContent } from '@/utils/contentCheck.js';\nimport ConfigurablePremiumActions from '@/components/premium/ConfigurablePremiumActions.vue';\n\n// --- 响应式状态定义 ---\nconst categoryType = ref('');\nconst categoryName = ref('');\nconst formConfig = ref({});\nconst showPayOptions = ref(false);\nconst serviceType = ref('');\nconst serviceTypeName = ref('');\nconst showRedPacketDatePicker = ref(false);\nconst hasSelectedPublishMethod = ref(false);\n\n// ConfigurablePremiumActions组件需要的数据\nconst publishData = ref({\n  id: 'publish_' + Date.now(),\n  title: '发布信息',\n  description: '发布您的信息到平台'\n});\n\nconst formData = reactive({\n\t\t\t\tname: '',\n\t\t\t\tgender: '',\n\t\t\t\tage: '',\n\t\t\t\tworkArea: '',\n\t\t\t\teducation: '',\n\t\t\t\texperience: '',\n\t\t\t\tjobPosition: '',\n\t\t\t\tsalary: '',\n\t\t\t\tintroduction: '',\n\t\t\t\timages: [],\n\t\t\t\ttags: [],\n\t\t\t\tcontact: '',\n\t\t\t\tphone: '',\n\t\t\t\tcode: '',\n\t\t\t\twechat: '',\n\tpetType: '',\n\tpetBreed: '',\n\tpurpose: '',\n\tpetGender: '',\n\tpetAge: '',\n\tpetPrice: '',\n\tpetVaccine: '',\n\tpetDescription: '',\n\tpetFeatures: [],\n\t\t\t\tcompanyName: '',\n\t\t\t\tworkAddress: '',\n\t\t\t\tjobType: '',\n\t\t\t\tcount: '',\n\t\t\t\tjobDescription: '',\n\t\t\t\twelfare: [],\n\t\t\t\tagree: false,\n\t\t\t\tredPacketEnabled: false,\n\t\t\t\tredPacketAmount: '',\n\t\t\t\tredPacketCount: '',\n\t\t\t\tredPacketType: 'random',\n\t\t\t\tredPacketConditions: [],\n\t\t\t\tredPacketExpiry: '',\n\t\t\t\tredPacketDesc: ''\n});\n\n// --- 方法定义 ---\nconst navigateBack = () => uni.navigateBack();\n\nconst togglePayOptions = () => {\n\tshowPayOptions.value = !showPayOptions.value;\n};\n\nconst selectGender = (fieldName = 'gender', value) => {\n\tformData[fieldName] = value;\n};\n\nconst initFormConfig = () => {\n    switch(categoryType.value) {\n        case 'home_service':\n            if (serviceType.value) {\n                const formKey = `${serviceType.value}Form`;\n                formConfig.value = serviceFormConfig[formKey] || serviceFormConfig.otherHomeServiceForm;\n            } else {\n                formConfig.value = serviceFormConfig.otherHomeServiceForm;\n            }\n\t\t\t\t\t\tbreak;\n        case 'find_service': formConfig.value = serviceFormConfig.findServiceForm; break;\n        case 'hire': formConfig.value = serviceFormConfig.recruitmentForm; break;\n        case 'job_wanted': formConfig.value = serviceFormConfig.jobWantedForm; break;\n        case 'house_rent': formConfig.value = serviceFormConfig.houseRentForm; break;\n        case 'house_sell': formConfig.value = serviceFormConfig.houseSellForm; break;\n        case 'used_car': formConfig.value = serviceFormConfig.usedCarForm; break;\n        case 'pet': formConfig.value = serviceFormConfig.petForm; break;\n        case 'merchant_activity': formConfig.value = serviceFormConfig.merchantActivityForm; break;\n        case 'car_service': formConfig.value = serviceFormConfig.carServiceForm; break;\n        case 'second_hand': formConfig.value = serviceFormConfig.secondHandForm; break;\n        case 'carpool': formConfig.value = serviceFormConfig.carpoolForm; break;\n        case 'business_transfer': formConfig.value = serviceFormConfig.businessTransferForm; break;\n        case 'education': formConfig.value = serviceFormConfig.educationForm; break;\n        case 'dating': formConfig.value = serviceFormConfig.datingForm; break;\n        default: formConfig.value = serviceFormConfig.defaultForm; break;\n    }\n};\n\nconst getPickerPlaceholder = (field) => {\n\treturn formData[field.name] || field.placeholder || '请选择';\n};\n\nconst shouldShowField = (field) => {\n\tif (!field.showIf) return true;\n\tconst { field: conditionField, value } = field.showIf;\n\treturn formData[conditionField] === value;\n};\n\nconst getLocation = (fieldName) => {\n\tuni.chooseLocation({\n\t\tsuccess: (res) => {\n\t\t\tif (res.address) formData[fieldName] = res.address;\n\t\t}\n\t});\n};\n\nconst chooseImage = (fieldName) => {\n\tconst fieldConfig = formConfig.value.sections?.flatMap(s => s.fields).find(f => f.name === fieldName);\n\tconst maxCount = fieldConfig?.maxCount || 9;\n\tconst currentImages = formData[fieldName] || [];\n\t\n\tuni.chooseImage({\n\t\tcount: maxCount - currentImages.length,\n\t\tsuccess: (res) => {\n\t\t\tformData[fieldName] = [...currentImages, ...res.tempFilePaths];\n\t\t}\n\t});\n};\n\nconst deleteImage = (fieldName, index) => {\n\tformData[fieldName]?.splice(index, 1);\n};\n\nconst isTagSelected = (tag, fieldName) => {\n\treturn formData[fieldName]?.includes(tag);\n};\n\nconst toggleTag = (tag, fieldName, maxCount = 3) => {\n\tconst tags = formData[fieldName] || [];\n\tconst index = tags.indexOf(tag);\n\tif (index > -1) {\n\t\ttags.splice(index, 1);\n\t} else if (tags.length < maxCount) {\n\t\ttags.push(tag);\n\t} else {\n\t\tuni.showToast({ title: `最多选择${maxCount}个标签`, icon: 'none' });\n\t}\n\tformData[fieldName] = tags;\n};\n\nconst toggleCondition = (condition) => {\n\tconst conditions = formData.redPacketConditions || [];\n\tconst index = conditions.indexOf(condition);\n\tif (index > -1) {\n\t\tconditions.splice(index, 1);\n\t} else {\n\t\tconditions.push(condition);\n\t}\n\tformData.redPacketConditions = conditions;\n};\n\nconst showAgreement = (type) => {\n\tuni.navigateTo({ url: `/pages/agreement/${type}` });\n};\n\nconst watchAdToPublish = () => {\n\tuni.showToast({ title: '激励广告功能待集成', icon: 'none' });\n\tsetTimeout(() => submitForm(true), 2000);\n};\n\nconst selectPublishDuration = (duration, price) => {\n\t// 在响应式状态中没有定义这些，如果需要可以添加\n\t// selectedDuration.value = duration;\n\t// publishPrice.value = price;\n\tsubmitForm();\n};\n\nconst submitForm = async (isFree = false) => {\n\tif (!formData.agree) {\n\t\treturn uni.showToast({ title: '请先阅读并同意协议', icon: 'none' });\n\t}\n\t\n\t// 检查用户是否已选择发布方式\n\tif (!hasSelectedPublishMethod.value) {\n\t\treturn uni.showToast({ title: '请先选择发布方式（看广告或付费）', icon: 'none' });\n\t}\n\n\tfor (const section of formConfig.value.sections || []) {\n\t\tfor (const field of section.fields) {\n\t\t\tif (field.required && !formData[field.name]) {\n\t\t\t\treturn uni.showToast({ title: `${field.label}不能为空`, icon: 'none' });\n\t\t\t}\n\t\t\tif (field.checkContent && formData[field.name]) {\n\t\t\t\tconst isValid = await checkContent(formData[field.name], 'text');\n\t\t\t\tif (!isValid) {\n\t\t\t\t\treturn uni.showToast({ title: `\"${field.label}\"包含违规内容`, icon: 'none' });\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tuni.showLoading({ title: '发布中...', mask: true });\n\ttry {\n\t\tawait new Promise(resolve => setTimeout(resolve, 1500));\n\t\t\n\t\tconst submissionData = { ...formData, id: Date.now().toString(), createTime: new Date().toISOString() };\n\t\tconst allData = uni.getStorageSync('publishedData') || [];\n\t\tuni.setStorageSync('publishedData', [...allData, submissionData]);\n\t\t\n\t\tuni.hideLoading();\n\t\tuni.redirectTo({ url: `/pages/publish/success?id=${submissionData.id}` });\n\t} catch (error) {\n\t\tuni.hideLoading();\n\t\tuni.showToast({ title: '发布失败，请重试', icon: 'none' });\n\t}\n};\n\n// 处理发布操作完成\nconst handlePublishCompleted = (result) => {\n\tconsole.log('发布操作完成:', result);\n\n\t// 标记用户已选择发布方式\n\thasSelectedPublishMethod.value = true;\n\n\tif (result.type === 'ad') {\n\t\tuni.showToast({\n\t\t\ttitle: '广告观看完成，可免费发布',\n\t\t\ticon: 'success'\n\t\t});\n\t} else if (result.type === 'payment') {\n\t\tuni.showToast({\n\t\t\ttitle: '付费发布成功',\n\t\t\ticon: 'success'\n\t\t});\n\t}\n\n\t// 继续提交流程\n\tsetTimeout(() => {\n\t\tsubmitForm(result.type === 'ad');\n\t}, 1500);\n};\n\n// 处理发布操作取消\nconst handlePublishCancelled = (result) => {\n\tconsole.log('发布操作取消:', result);\n\n\tif (result.type === 'ad') {\n\t\tuni.showToast({\n\t\t\ttitle: '已取消观看广告',\n\t\t\ticon: 'none'\n\t\t});\n\t} else if (result.type === 'payment') {\n\t\tuni.showToast({\n\t\t\ttitle: '已取消支付',\n\t\t\ticon: 'none'\n\t\t});\n\t}\n};\n\n// --- 生命周期 ---\nonLoad((options) => {\n\tcategoryType.value = options.type || '';\n\tcategoryName.value = decodeURIComponent(options.name || '');\n\tserviceType.value = options.serviceType || '';\n\tserviceTypeName.value = decodeURIComponent(options.serviceTypeName || '');\n\t\n\t// 处理自动分享参数\n\tif (options.autoShare === 'true') {\n\t\tconsole.log('检测到自动分享参数，准备显示分享菜单');\n\t\t// 延迟执行，确保页面已完全加载\n\t\tsetTimeout(() => {\n\t\t\tuni.showShareMenu({\n\t\t\t\twithShareTicket: true,\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline'],\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tconsole.log('自动显示分享菜单成功');\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('自动显示分享菜单失败', err);\n\t\t\t\t}\n\t\t\t});\n\t\t}, 1000);\n\t}\n\t\n\tinitFormConfig();\n});\n</script>\n\n<style>\n\t/* 红包设置相关样式 */\n\t.switch-control {\n\ttransform: scale(0.8);\n}\n\n.input-unit {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-left: 10rpx;\n\tmargin-right: 10rpx;\n}\n\n.radio-group, .checkbox-group {\n\tdisplay: flex;\n\tflex-direction: row;\n\tflex-wrap: wrap;\n}\n\n.radio-item, .checkbox-item {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-right: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.radio-circle {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tborder-radius: 50%;\n\tborder: 2rpx solid #CCCCCC;\n\tmargin-right: 10rpx;\n\tposition: relative;\n}\n\n.radio-circle.active {\n\tborder-color: #FF4D4F;\n}\n\n.radio-circle.active::after {\n\tcontent: '';\n\tposition: absolute;\n\twidth: 24rpx;\n\theight: 24rpx;\n\tbackground-color: #FF4D4F;\n\tborder-radius: 50%;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n.checkbox-square {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tborder: 2rpx solid #CCCCCC;\n\tmargin-right: 10rpx;\n\tposition: relative;\n}\n\n.checkbox-square.active {\n\tbackground-color: #FF4D4F;\n\tborder-color: #FF4D4F;\n}\n\n.checkbox-square.active::after {\n\tcontent: '✓';\n\tcolor: white;\n\tposition: absolute;\n\tfont-size: 24rpx;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n.radio-text, .checkbox-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.date-picker-text {\n\tflex: 1;\n\tcolor: #333;\n}\n\n.date-icon {\n\twidth: 40rpx;\n\theight: 40rpx;\n}\n\n.redpacket-tips {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tbackground-color: #FFF7F7;\n\tpadding: 20rpx;\n\tborder-radius: 10rpx;\n\tmargin: 20rpx 0;\n}\n\n.tips-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tmargin-right: 10rpx;\n\tflex-shrink: 0;\n\tmargin-top: 6rpx;\n}\n\n.tips-text {\n\tfont-size: 24rpx;\n\tcolor: #FF6B6B;\n\tline-height: 1.5;\n}\n\n/* 红包样式 */\n.red-packet-section {\n\tmargin: 20rpx 0;\n\tpadding: 0 30rpx;\n}\n\n.red-packet-container {\n\tposition: relative;\n\theight: 200rpx;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.red-packet-bg {\n\tposition: absolute;\n\twidth: 100%;\n\theight: 100%;\n\tz-index: 1;\n}\n\n.red-packet-content {\n\tposition: relative;\n\tz-index: 2;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n\theight: 100%;\n\tpadding: 20rpx 30rpx;\n\tcolor: #fff;\n}\n\n.red-packet-title {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.red-packet-icon {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tmargin-right: 10rpx;\n}\n\n.red-packet-info {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.red-packet-amount {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 6rpx;\n}\n\n.red-packet-desc {\n\tfont-size: 24rpx;\n\topacity: 0.9;\n}\n\n.grab-red-packet-btn {\n\twidth: 240rpx;\n\theight: 70rpx;\n\tline-height: 70rpx;\n\tbackground: linear-gradient(to right, #FFD700, #FF8C00);\n\tcolor: #fff;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tborder-radius: 35rpx;\n\ttext-align: center;\n\talign-self: flex-end;\n\tmargin-top: 10rpx;\n\tbox-shadow: 0 4rpx 8rpx rgba(255, 140, 0, 0.3);\n}\n\t\n\t@keyframes fadeInUp {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(20rpx);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\t\n\t@keyframes scaleIn {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: scale(0.95);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: scale(1);\n\t\t}\n\t}\n\t\n\t@keyframes shimmer {\n\t\t0% {\n\t\t\tleft: -100%;\n\t\t}\n\t\t100% {\n\t\t\tleft: 100%;\n\t\t}\n\t}\n\t\n\t/* 添加样式变量以支持不同类目的主题色 */\n\t.job_wanted {\n\t\t--theme-color: #3366FF;\n\t\t--theme-gradient: linear-gradient(135deg, #3366FF, #6699FF);\n\t}\n\t\n\t.hire {\n\t\t--theme-color: #0099CC;\n\t\t--theme-gradient: linear-gradient(135deg, #0099CC, #33CCFF);\n\t}\n\t\n\t.pet {\n\t\t--theme-color: #FF9966;\n\t\t--theme-gradient: linear-gradient(135deg, #FF9966, #FFCC99);\n\t}\n\t\n\t.house_rent, .house_sell {\n\t\t--theme-color: #33CC99;\n\t\t--theme-gradient: linear-gradient(135deg, #33CC99, #66FFCC);\n\t}\n\t\n\t.used_car, .car_service {\n\t\t--theme-color: #CC3366;\n\t\t--theme-gradient: linear-gradient(135deg, #CC3366, #FF6699);\n\t}\n\t\n\t.second_hand {\n\t\t--theme-color: #9966CC;\n\t\t--theme-gradient: linear-gradient(135deg, #9966CC, #CC99FF);\n\t}\n\t\n\t.education {\n\t\t--theme-color: #FF6633;\n\t\t--theme-gradient: linear-gradient(135deg, #FF6633, #FF9966);\n\t}\n\t\n\t.carpool {\n\t\t--theme-color: #339999;\n\t\t--theme-gradient: linear-gradient(135deg, #339999, #66CCCC);\n\t}\n\t\n\t.business_transfer {\n\t\t--theme-color: #FF9900;\n\t\t--theme-gradient: linear-gradient(135deg, #FF9900, #FFCC66);\n\t}\n\t\n\t.other_service {\n\t\t--theme-color: #666699;\n\t\t--theme-gradient: linear-gradient(135deg, #666699, #9999CC);\n\t}\n\t\n\t.home_service {\n\t\t--theme-color: #3399CC;\n\t\t--theme-gradient: linear-gradient(135deg, #3399CC, #66CCFF);\n\t}\n\t\n\t.find_service {\n\t\t--theme-color: #6633CC;\n\t\t--theme-gradient: linear-gradient(135deg, #6633CC, #9966FF);\n\t}\n\t\n\t.publish-detail {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f7fa;\n\t\tbackground-image: linear-gradient(to bottom, #eef2f7, #f5f7fa);\n\t}\n\t\n\t/* 自定义导航栏 - 统一为蓝色系 */\n\t.custom-navbar {\n\t\tbackground-image: linear-gradient(135deg, #0052CC, #0066FF);\n\t\theight: 88rpx;\n\t\tpadding-top: 44px; /* 状态栏高度 */\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: fixed; /* 改为固定定位 */\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tpadding-bottom: 10rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n\t\tz-index: 100; /* 提高z-index确保在最上层 */\n\t}\n\t\n\t.back-btn {\n\t\tposition: absolute;\n\t\tleft: 20rpx;\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.back-btn:active {\n\t\ttransform: scale(0.95);\n\t}\n\t\n\t.back-icon {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t}\n\t\n\t.navbar-title {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 500;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.navbar-right {\n\t\tposition: absolute;\n\t\tright: 20rpx;\n\t\tdisplay: flex;\n\t}\n\t\n\t.navbar-btn {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.navbar-btn:active {\n\t\ttransform: scale(0.95);\n\t}\n\t\n\t.navbar-btn-icon {\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t\tfilter: drop-shadow(0 2rpx 2rpx rgba(0, 0, 0, 0.1));\n\t}\n\t\n\t/* 免责声明 */\n\t.disclaimer {\n\t\tpadding: 20rpx 30rpx;\n\t\tmargin: 170rpx 20rpx 0; /* 往下移动10px，从160rpx改为170rpx */\n\t}\n\t\n\t.disclaimer-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999; /* 改为灰色 */\n\t\tline-height: 1.4;\n\t}\n\t\n\t/* 内容区域 */\n\t.content-scroll {\n\t\tflex: 1;\n\t\theight: calc(100vh - 180rpx);\n\t\tpadding-bottom: 30rpx;\n\t\tmargin-top: 0; /* 移除顶部边距，因为免责声明已经有了足够的边距 */\n\t}\n\t\n\t/* 表单部分 */\n\t.form-container {\n\t\tpadding: 20rpx;\n\t}\n\t\n\t.form-section {\n\t\tbackground-color: #FFFFFF;\n\t\tmargin-top: 30rpx;\n\t\tborder-radius: 16rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);\n\t\ttransform: translateZ(0);\n\t\ttransition: transform 0.3s, box-shadow 0.3s;\n\t\tborder: 1rpx solid rgba(0, 0, 0, 0.03);\n\t\tanimation: fadeInUp 0.5s ease-out forwards;\n\t\tposition: relative;\n\t}\n\t\n\t/* 添加卡片顶部彩色边框 */\n\t.form-section::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 6rpx;\n\t\tbackground: var(--theme-gradient, linear-gradient(to right, #0052CC, #0066FF));\n\t}\n\t\n\t.form-section:nth-child(1) {\n\t\tanimation-delay: 0.1s;\n\t}\n\t\n\t.form-section:nth-child(2) {\n\t\tanimation-delay: 0.2s;\n\t}\n\t\n\t.form-section:nth-child(3) {\n\t\tanimation-delay: 0.3s;\n\t}\n\t\n\t.form-section:nth-child(4) {\n\t\tanimation-delay: 0.4s;\n\t}\n\t\n\t.form-section:active {\n\t\ttransform: translateY(2rpx);\n\t\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.form-item {\n\t\tpadding: 30rpx;\n\t\tposition: relative;\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.03);\n\t\ttransition: background-color 0.3s;\n\t}\n\t\n\t.form-item:hover, .form-item:active {\n\t\tbackground-color: rgba(0, 0, 0, 0.01);\n\t}\n\t\n\t.form-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t.section-title {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 20rpx;\n\t\tposition: relative;\n\t\tpadding-left: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.section-title::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 8rpx;\n\t\theight: 28rpx;\n\t\tbackground: linear-gradient(to bottom, #0052CC, #0066FF);\n\t\tborder-radius: 4rpx;\n\t}\n\t\n\t/* 添加标题底部阴影线 */\n\t.form-item:first-child {\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\t\tbackground-color: rgba(0, 0, 0, 0.01);\n\t\tpadding-bottom: 20rpx;\n\t}\n\t\n\t/* 表单项组 - 增强分组布局 */\n\t.form-group {\n\t\tmargin-bottom: 30rpx;\n\t}\n\t\n\t.form-group:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\t\n\t.form-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.form-label-icon {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\tmargin-right: 10rpx;\n\t\topacity: 0.7;\n\t}\n\t\n\t.required:after {\n\t\tcontent: '*';\n\t\tcolor: #ff4d4f;\n\t\tmargin-left: 4rpx;\n\t\tfont-size: 32rpx;\n\t}\n\t\n\t/* 提示文本 */\n\t.form-tip {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-top: 12rpx;\n\t\tpadding-left: 10rpx;\n\t\tline-height: 1.4;\n\t}\n\t\n\t/* 优化表单输入框样式 */\n\t.form-input-wrapper {\n\t\theight: 80rpx;\n\t\tborder-radius: 12rpx;\n\t\tbackground-color: #f5f7fa;\n\t\tpadding: 0 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tbox-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);\n\t\tborder: 1rpx solid rgba(0, 0, 0, 0.02);\n\t\ttransition: all 0.3s;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\t\n\t.form-input-wrapper:focus-within::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 3rpx;\n\t\tbackground: var(--theme-gradient, linear-gradient(to right, #1677FF, #1677FF));\n\t}\n\t\n\t.form-input-wrapper:focus-within {\n\t\tbackground-color: rgba(22, 119, 255, 0.05);\n\t\tbox-shadow: inset 0 2rpx 5rpx rgba(22, 119, 255, 0.1);\n\t\tborder: 1rpx solid rgba(22, 119, 255, 0.2);\n\t\ttransform: translateY(-2rpx);\n\t}\n\t\n\t.form-input {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tpadding: 0 20rpx;\n\t}\n\t\n\t.placeholder-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t.selected-text {\n\t\tcolor: #333;\n\t}\n\t\n\t.arrow-icon {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\ttransform: rotate(90deg);\n\t\topacity: 0.5;\n\t}\n\t\n\t.textarea-wrapper {\n\t\tbackground-color: #f5f7fa;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 20rpx;\n\t\tbox-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);\n\t\tborder: 1rpx solid rgba(0, 0, 0, 0.02);\n\t\ttransition: all 0.3s;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\t\n\t.textarea-wrapper:focus-within::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 3rpx;\n\t\tbackground: var(--theme-gradient, linear-gradient(to right, #1677FF, #1677FF));\n\t}\n\t\n\t.textarea-wrapper:focus-within {\n\t\tbackground-color: rgba(22, 119, 255, 0.05);\n\t\tbox-shadow: inset 0 2rpx 5rpx rgba(22, 119, 255, 0.1);\n\t\tborder: 1rpx solid rgba(22, 119, 255, 0.2);\n\t\ttransform: translateY(-2rpx);\n\t}\n\t\n\t.form-textarea {\n\t\twidth: 100%;\n\t\theight: 200rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tline-height: 1.6;\n\t}\n\t\n\t/* 美化上传组件 */\n\t.upload-wrapper {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tmargin: 0 -10rpx;\n\t}\n\t\n\t.upload-btn {\n\t\twidth: 160rpx;\n\t\theight: 160rpx;\n\t\tbackground-color: #f5f7fa;\n\t\tborder-radius: 12rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin: 0 10rpx 20rpx;\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);\n\t\tborder: 1rpx dashed rgba(0, 0, 0, 0.1);\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.upload-btn:active {\n\t\ttransform: scale(0.98);\n\t\tbackground-color: rgba(0, 82, 204, 0.05);\n\t}\n\t\n\t.upload-btn-inner {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\t\n\t.upload-icon {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tmargin-bottom: 10rpx;\n\t\tfilter: drop-shadow(0 2rpx 2rpx rgba(0, 0, 0, 0.1));\n\t}\n\t\n\t.upload-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\t\n\t.upload-preview {\n\t\twidth: 160rpx;\n\t\theight: 160rpx;\n\t\tmargin: 0 10rpx 20rpx;\n\t\tposition: relative;\n\t\tborder-radius: 12rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\n\t\tborder: 2rpx solid #fff;\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.upload-preview:active {\n\t\ttransform: scale(0.98);\n\t}\n\t\n\t.preview-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t}\n\t\n\t.delete-btn {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 2;\n\t}\n\t\n\t.delete-icon {\n\t\tcolor: white;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.upload-tip {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin-top: 10rpx;\n\t\tfont-style: italic;\n\t}\n\t\n\t/* 性别选择器 */\n\t.gender-selector {\n\t\tdisplay: flex;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.gender-option {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tbackground-color: #f5f7fa;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tmargin-right: 20rpx;\n\t\tborder-radius: 12rpx;\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);\n\t\tborder: 1rpx solid rgba(0, 0, 0, 0.02);\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.gender-option:last-child {\n\t\tmargin-right: 0;\n\t}\n\t\n\t.gender-option:active {\n\t\ttransform: scale(0.98);\n\t}\n\t\n\t.gender-selected {\n\t\tbackground: linear-gradient(to bottom, #e6f0ff, #d6e6ff);\n\t\tcolor: var(--theme-color, #0052CC);\n\t\tfont-weight: 500;\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.1);\n\t\tborder: 1rpx solid rgba(0, 82, 204, 0.1);\n\t}\n\t\n\t/* 优化标签选择器样式 */\n\t.tag-section {\n\t\tmargin-top: 10rpx;\n\t}\n\t\n\t.tag-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.tag-list {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tmargin: 0 -10rpx;\n\t}\n\t\n\t.tag-item {\n\t\tpadding: 10rpx 24rpx;\n\t\tbackground-color: #f5f7fa;\n\t\tborder-radius: 30rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t\tmargin: 0 10rpx 20rpx;\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);\n\t\tborder: 1rpx solid rgba(0, 0, 0, 0.02);\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.tag-item:active {\n\t\ttransform: scale(0.95);\n\t}\n\t\n\t.tag-active {\n\t\tbackground: var(--theme-gradient, linear-gradient(to bottom, #e6f0ff, #d6e6ff));\n\t\tcolor: var(--theme-color, #0052CC);\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.1);\n\t\tborder: 1rpx solid rgba(0, 82, 204, 0.1);\n\t\tfont-weight: 500;\n\t}\n\t\n\t.input-arrow {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\ttransform: rotate(90deg);\n\t\topacity: 0.5;\n\t}\n\t\n\t.code-input-wrapper {\n\t\tjustify-content: space-between;\n\t}\n\t\n\t.code-input {\n\t\tflex: 0.7;\n\t}\n\t\n\t.code-btn {\n\t\tpadding: 0 30rpx;\n\t\theight: 60rpx;\n\t\tbackground: linear-gradient(to right, #0052CC, #0066FF);\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 26rpx;\n\t\tborder-radius: 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.15);\n\t\ttransition: all 0.3s;\n\t}\n\t\n\t.code-btn:active {\n\t\ttransform: scale(0.95);\n\t\tbox-shadow: 0 2rpx 4rpx rgba(0, 82, 204, 0.15);\n\t}\n\t\n\t/* 广告部分 */\n\t.ad-section {\n\t\tmargin: 30rpx 20rpx;\n\t\tborder-radius: 16rpx;\n\t\toverflow: hidden;\n\t\tbackground-color: #FFFFFF;\n\t\tanimation: scaleIn 0.5s ease-out 0.5s forwards;\n\t\topacity: 0;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\n\t\tpadding: 20rpx 15rpx 5rpx;\n\t}\n\t\n\t.publish-method-title {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tmargin: 0rpx 30rpx 20rpx;\n\t\tposition: relative;\n\t\tpadding-left: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.publish-method-title::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 8rpx;\n\t\theight: 28rpx;\n\t\tbackground: linear-gradient(to bottom, #0052CC, #0066FF);\n\t\tborder-radius: 4rpx;\n\t}\n\t\n\t.ad-item {\n\t\tpadding: 25rpx 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: relative;\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\t\ttransition: all 0.3s;\n\t\tmargin: 0 15rpx 15rpx;\n\t\tborder-radius: 12rpx;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\n\t}\n\t\n\t.ad-item:active {\n\t\topacity: 0.9;\n\t\ttransform: scale(0.98);\n\t}\n\t\n\t.free-ad {\n\t\tbackground: linear-gradient(to right, #E0F2FF, #B8E2FF, #88C9FF);\n\t}\n\t\n\t.pay-ad {\n\t\tbackground: linear-gradient(to right, #FFE8C0, #FFD285, #FFC455);\n\t}\n\t\n\t.ad-icon-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t\tborder-radius: 15rpx;\n\t\tmargin-right: 20rpx;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.9);\n\t}\n\t\n\t.ad-icon {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t}\n\t\n\t.ad-content {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.ad-title {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.ad-desc {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\topacity: 0.9;\n\t}\n\t\n\t.ad-btn {\n\t\tpadding: 10rpx 30rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 600;\n\t\tborder-radius: 40rpx;\n\t\ttransition: all 0.3s;\n\t\tcolor: #FFFFFF;\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);\n\t\tletter-spacing: 2rpx;\n\t}\n\t\n\t.free-btn {\n\t\tbackground-color: #0052CC;\n\t\tbackground-image: linear-gradient(135deg, #0066FF, #004BB9);\n\t}\n\t\n\t.pay-btn {\n\t\tbackground-color: #E67700;\n\t\tbackground-image: linear-gradient(135deg, #FF8800, #E06600);\n\t}\n\t\n\t.ad-btn:active {\n\t\ttransform: scale(0.95);\n\t\tbox-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t\topacity: 0.9;\n\t}\n\t\n\t.arrow-wrap {\n\t\tmargin-left: 10rpx;\n\t\theight: 40rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.dropdown-icon {\n\t\twidth: 32rpx;\n\t\theight: 32rpx;\n\t\ttransform: rotate(90deg);\n\t\topacity: 0.5;\n\t}\n\t\n\t/* 美化协议部分 */\n\t.agreement-section {\n\t\tpadding: 20rpx 0;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.agreement-row {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-wrap: wrap;\n\t}\n\t\n\t.agreement-checkbox {\n\t\ttransform: scale(0.8);\n\t\tmargin-right: 10rpx;\n\t}\n\t\n\t.agreement-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t\tmargin: 0 6rpx;\n\t}\n\t\n\t.agreement-link {\n\t\tfont-size: 26rpx;\n\t\tcolor: #0052CC;\n\t\ttext-decoration: underline;\n\t}\n\t\n\t/* 发布按钮 - 统一为蓝色系 */\n\t.publish-btn-wrapper {\n\t\tpadding: 20rpx 30rpx 40rpx;\n\t\tanimation: fadeInUp 0.5s ease-out 0.7s forwards;\n\t\topacity: 0;\n\t}\n\t\n\t.publish-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: #0076FF; /* 更改为与打电话按钮相似的蓝色 */\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tborder-radius: 44rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder: none;\n\t\tbox-shadow: 0 8rpx 16rpx rgba(0, 118, 255, 0.2);\n\t\ttransition: all 0.3s;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\t\n\t.publish-btn::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: -100%;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n\t\tanimation: shimmer 2s infinite;\n\t}\n\t\n\t@keyframes shimmer {\n\t\t0% {\n\t\t\tleft: -100%;\n\t\t}\n\t\t100% {\n\t\t\tleft: 100%;\n\t\t}\n\t}\n\t\n\t.publish-btn:active {\n\t\ttransform: scale(0.98);\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 82, 204, 0.15);\n\t}\n\t\n\t.publish-btn[disabled] {\n\t\tbackground: linear-gradient(to right, #cccccc, #dddddd);\n\t\tcolor: #FFFFFF;\n\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.publish-btn[disabled]::after {\n\t\tdisplay: none;\n\t}\n\t\n\t/* 添加类目特定主题颜色 */\n\t.job_wanted .section-title::before {\n\t\tbackground: linear-gradient(to bottom, #3366FF, #6699FF);\n\t}\n\t\n\t.hire .section-title::before {\n\t\tbackground: linear-gradient(to bottom, #0099CC, #33CCFF);\n\t}\n\t\n\t.pet .section-title::before {\n\t\tbackground: linear-gradient(to bottom, #FF9966, #FFCC99);\n\t}\n\t\n\t.house_rent .section-title::before,\n\t.house_sell .section-title::before {\n\t\tbackground: linear-gradient(to bottom, #33CC99, #66FFCC);\n\t}\n\t\n\t.used_car .section-title::before,\n\t.car_service .section-title::before {\n\t\tbackground: linear-gradient(to bottom, #CC3366, #FF6699);\n\t}\n\t\n\t.second_hand .section-title::before {\n\t\tbackground: linear-gradient(to bottom, #9966CC, #CC99FF);\n\t}\n\t\n\t.education .section-title::before {\n\t\tbackground: linear-gradient(to bottom, #FF6633, #FF9966);\n\t}\n\n\t/* 发布方式部分样式 */\n\t.publish-method-section {\n\t\tmargin: 30rpx 0;\n\t\tpadding: 20rpx 30rpx 30rpx;\n\t\tbackground: linear-gradient(135deg, #ffffff 0%, #f9fbff 100%);\n\t\tborder-radius: 20rpx;\n\t\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\n\t.publish-method-section::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 6rpx;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(to bottom, #3b7dfc 0%, #5e96ff 100%);\n\t}\n\n\t.publish-method-section .section-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 24rpx;\n\t\tpadding-bottom: 20rpx;\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\t}\n\n\t.section-icon {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tmargin-right: 16rpx;\n\t\tbackground: linear-gradient(135deg, #3b7dfc 0%, #5e96ff 100%);\n\t\tborder-radius: 12rpx;\n\t\tpadding: 10rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(59, 125, 252, 0.25);\n\t}\n\n\t.section-icon image {\n\t\twidth: 28rpx;\n\t\theight: 28rpx;\n\t\tfilter: brightness(10);\n\t}\n\n\t.section-title {\n\t\tfont-size: 34rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n\t\tletter-spacing: 1rpx;\n\t}\n</style>\n\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "serviceFormConfig", "serviceFormConfig.otherHomeServiceForm", "serviceFormConfig.findServiceForm", "serviceFormConfig.recruitmentForm", "serviceFormConfig.jobWantedForm", "serviceFormConfig.houseRentForm", "serviceFormConfig.houseSellForm", "serviceFormConfig.usedCarForm", "serviceFormConfig.petForm", "serviceFormConfig.merchantActivityForm", "serviceFormConfig.carServiceForm", "serviceFormConfig.secondHandForm", "serviceFormConfig.carpoolForm", "serviceFormConfig.businessTransferForm", "serviceFormConfig.educationForm", "serviceFormConfig.datingForm", "serviceFormConfig.defaultForm", "checkContent", "onLoad", "MiniProgramPage"], "mappings": ";;;;;;;;AAqSA,MAAM,6BAA6B,MAAW;;;;AAG9C,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AACFA,kBAAG,IAAC,KAAK;AAChC,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,0BAA0BA,cAAAA,IAAI,KAAK;AACzC,UAAM,2BAA2BA,cAAAA,IAAI,KAAK;AAG1C,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,IAAI,aAAa,KAAK,IAAK;AAAA,MAC3B,OAAO;AAAA,MACP,aAAa;AAAA,IACf,CAAC;AAED,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACtB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,QAAQ,CAAE;AAAA,MACV,MAAM,CAAE;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,aAAa,CAAE;AAAA,MACZ,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,MACT,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,SAAS,CAAE;AAAA,MACX,OAAO;AAAA,MACP,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,qBAAqB,CAAE;AAAA,MACvB,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACnB,CAAC;AAGD,UAAM,eAAe,MAAMC,oBAAI;AAM/B,UAAM,eAAe,CAAC,YAAY,UAAU,UAAU;AACrD,eAAS,SAAS,IAAI;AAAA,IACvB;AAEA,UAAM,iBAAiB,MAAM;AACzB,cAAO,aAAa,OAAK;AAAA,QACrB,KAAK;AACD,cAAI,YAAY,OAAO;AACnB,kBAAM,UAAU,GAAG,YAAY,KAAK;AACpC,uBAAW,QAAQC,wBAAAA,kBAAkB,OAAO,KAAKC,wBAAAA;AAAAA,UACjE,OAAmB;AACH,uBAAW,QAAQA;UACtB;AACP;AAAA,QACE,KAAK;AAAgB,qBAAW,QAAQC,wBAAAA;AAAmC;AAAA,QAC3E,KAAK;AAAQ,qBAAW,QAAQC,wBAAAA;AAAmC;AAAA,QACnE,KAAK;AAAc,qBAAW,QAAQC,wBAAAA;AAAiC;AAAA,QACvE,KAAK;AAAc,qBAAW,QAAQC,wBAAAA;AAAiC;AAAA,QACvE,KAAK;AAAc,qBAAW,QAAQC,wBAAAA;AAAiC;AAAA,QACvE,KAAK;AAAY,qBAAW,QAAQC,wBAAAA;AAA+B;AAAA,QACnE,KAAK;AAAO,qBAAW,QAAQC,wBAAAA;AAA2B;AAAA,QAC1D,KAAK;AAAqB,qBAAW,QAAQC,wBAAAA;AAAwC;AAAA,QACrF,KAAK;AAAe,qBAAW,QAAQC,wBAAAA;AAAkC;AAAA,QACzE,KAAK;AAAe,qBAAW,QAAQC,wBAAAA;AAAkC;AAAA,QACzE,KAAK;AAAW,qBAAW,QAAQC,wBAAAA;AAA+B;AAAA,QAClE,KAAK;AAAqB,qBAAW,QAAQC,wBAAAA;AAAwC;AAAA,QACrF,KAAK;AAAa,qBAAW,QAAQC,wBAAAA;AAAiC;AAAA,QACtE,KAAK;AAAU,qBAAW,QAAQC,wBAAAA;AAA8B;AAAA,QAChE;AAAS,qBAAW,QAAQC,wBAA6B;AAAE;AAAA,MAC9D;AAAA,IACL;AAEA,UAAM,uBAAuB,CAAC,UAAU;AACvC,aAAO,SAAS,MAAM,IAAI,KAAK,MAAM,eAAe;AAAA,IACrD;AAEA,UAAM,kBAAkB,CAAC,UAAU;AAClC,UAAI,CAAC,MAAM;AAAQ,eAAO;AAC1B,YAAM,EAAE,OAAO,gBAAgB,MAAK,IAAK,MAAM;AAC/C,aAAO,SAAS,cAAc,MAAM;AAAA,IACrC;AAEA,UAAM,cAAc,CAAC,cAAc;AAClCjB,oBAAAA,MAAI,eAAe;AAAA,QAClB,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI;AAAS,qBAAS,SAAS,IAAI,IAAI;AAAA,QAC3C;AAAA,MACH,CAAE;AAAA,IACF;AAEA,UAAM,cAAc,CAAC,cAAc;;AAClC,YAAM,eAAc,gBAAW,MAAM,aAAjB,mBAA2B,QAAQ,OAAK,EAAE,QAAQ,KAAK,OAAK,EAAE,SAAS;AAC3F,YAAM,YAAW,2CAAa,aAAY;AAC1C,YAAM,gBAAgB,SAAS,SAAS,KAAK,CAAA;AAE7CA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,WAAW,cAAc;AAAA,QAChC,SAAS,CAAC,QAAQ;AACjB,mBAAS,SAAS,IAAI,CAAC,GAAG,eAAe,GAAG,IAAI,aAAa;AAAA,QAC7D;AAAA,MACH,CAAE;AAAA,IACF;AAEA,UAAM,cAAc,CAAC,WAAW,UAAU;;AACzC,qBAAS,SAAS,MAAlB,mBAAqB,OAAO,OAAO;AAAA,IACpC;AAEA,UAAM,gBAAgB,CAAC,KAAK,cAAc;;AACzC,cAAO,cAAS,SAAS,MAAlB,mBAAqB,SAAS;AAAA,IACtC;AAEA,UAAM,YAAY,CAAC,KAAK,WAAW,WAAW,MAAM;AACnD,YAAM,OAAO,SAAS,SAAS,KAAK,CAAA;AACpC,YAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,UAAI,QAAQ,IAAI;AACf,aAAK,OAAO,OAAO,CAAC;AAAA,MACtB,WAAY,KAAK,SAAS,UAAU;AAClC,aAAK,KAAK,GAAG;AAAA,MACf,OAAQ;AACNA,sBAAAA,MAAI,UAAU,EAAE,OAAO,OAAO,QAAQ,OAAO,MAAM,OAAM,CAAE;AAAA,MAC3D;AACD,eAAS,SAAS,IAAI;AAAA,IACvB;AAEA,UAAM,kBAAkB,CAAC,cAAc;AACtC,YAAM,aAAa,SAAS,uBAAuB;AACnD,YAAM,QAAQ,WAAW,QAAQ,SAAS;AAC1C,UAAI,QAAQ,IAAI;AACf,mBAAW,OAAO,OAAO,CAAC;AAAA,MAC5B,OAAQ;AACN,mBAAW,KAAK,SAAS;AAAA,MACzB;AACD,eAAS,sBAAsB;AAAA,IAChC;AAEA,UAAM,gBAAgB,CAAC,SAAS;AAC/BA,oBAAG,MAAC,WAAW,EAAE,KAAK,oBAAoB,IAAI,GAAE,CAAE;AAAA,IACnD;AAcA,UAAM,aAAa,OAAO,SAAS,UAAU;AAC5C,UAAI,CAAC,SAAS,OAAO;AACpB,eAAOA,cAAAA,MAAI,UAAU,EAAE,OAAO,aAAa,MAAM,OAAM,CAAE;AAAA,MACzD;AAGD,UAAI,CAAC,yBAAyB,OAAO;AACpC,eAAOA,cAAAA,MAAI,UAAU,EAAE,OAAO,oBAAoB,MAAM,OAAM,CAAE;AAAA,MAChE;AAED,iBAAW,WAAW,WAAW,MAAM,YAAY,CAAA,GAAI;AACtD,mBAAW,SAAS,QAAQ,QAAQ;AACnC,cAAI,MAAM,YAAY,CAAC,SAAS,MAAM,IAAI,GAAG;AAC5C,mBAAOA,oBAAI,UAAU,EAAE,OAAO,GAAG,MAAM,KAAK,QAAQ,MAAM,OAAQ,CAAA;AAAA,UAClE;AACD,cAAI,MAAM,gBAAgB,SAAS,MAAM,IAAI,GAAG;AAC/C,kBAAM,UAAU,MAAMkB,mBAAY,aAAC,SAAS,MAAM,IAAI,CAAS;AAC/D,gBAAI,CAAC,SAAS;AACb,qBAAOlB,oBAAI,UAAU,EAAE,OAAO,IAAI,MAAM,KAAK,WAAW,MAAM,OAAQ,CAAA;AAAA,YACtE;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEDA,oBAAG,MAAC,YAAY,EAAE,OAAO,UAAU,MAAM,KAAI,CAAE;AAC/C,UAAI;AACH,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,IAAI,CAAC;AAEtD,cAAM,iBAAiB,EAAE,GAAG,UAAU,IAAI,KAAK,IAAG,EAAG,SAAQ,GAAI,aAAY,oBAAI,KAAI,GAAG,YAAa,EAAA;AACrG,cAAM,UAAUA,cAAG,MAAC,eAAe,eAAe,KAAK,CAAA;AACvDA,sBAAG,MAAC,eAAe,iBAAiB,CAAC,GAAG,SAAS,cAAc,CAAC;AAEhEA,sBAAG,MAAC,YAAW;AACfA,4BAAI,WAAW,EAAE,KAAK,6BAA6B,eAAe,EAAE,GAAE,CAAE;AAAA,MACxE,SAAQ,OAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAM,CAAE;AAAA,MACjD;AAAA,IACF;AAGA,UAAM,yBAAyB,CAAC,WAAW;AAC1CA,0EAAY,WAAW,MAAM;AAG7B,+BAAyB,QAAQ;AAEjC,UAAI,OAAO,SAAS,MAAM;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,WAAY,OAAO,SAAS,WAAW;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAGD,iBAAW,MAAM;AAChB,mBAAW,OAAO,SAAS,IAAI;AAAA,MAC/B,GAAE,IAAI;AAAA,IACR;AAGA,UAAM,yBAAyB,CAAC,WAAW;AAC1CA,0EAAY,WAAW,MAAM;AAE7B,UAAI,OAAO,SAAS,MAAM;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,WAAY,OAAO,SAAS,WAAW;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAGAmB,kBAAM,OAAC,CAAC,YAAY;AACnB,mBAAa,QAAQ,QAAQ,QAAQ;AACrC,mBAAa,QAAQ,mBAAmB,QAAQ,QAAQ,EAAE;AAC1D,kBAAY,QAAQ,QAAQ,eAAe;AAC3C,sBAAgB,QAAQ,mBAAmB,QAAQ,mBAAmB,EAAE;AAGxE,UAAI,QAAQ,cAAc,QAAQ;AACjCnB,sBAAAA,sDAAY,oBAAoB;AAEhC,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,cAAc;AAAA,YACjB,iBAAiB;AAAA,YACjB,OAAO,CAAC,mBAAmB,eAAe;AAAA,YAC1C,SAAS,MAAM;AACdA,4BAAAA,MAAY,MAAA,OAAA,mCAAA,YAAY;AAAA,YACxB;AAAA,YACD,MAAM,CAAC,QAAQ;AACdA,oFAAc,cAAc,GAAG;AAAA,YAC/B;AAAA,UACL,CAAI;AAAA,QACD,GAAE,GAAI;AAAA,MACP;AAED;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClkBD,GAAG,WAAWoB,SAAe;"}