<template>
  <view class="checkin-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">签到中心</view>
        <view class="navbar-right">
          <view class="points-info">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
            </svg>
            <text class="points-text">{{ userPoints }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 签到状态卡片 -->
      <view class="checkin-status-card">
        <view class="status-header">
          <view class="status-icon" :class="{ checked: todayChecked }">
            <svg v-if="todayChecked" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
          </view>
          <view class="status-info">
            <text class="status-title">{{ todayChecked ? '今日已签到' : '今日未签到' }}</text>
            <text class="status-desc">{{ todayChecked ? `获得${todayPoints}积分` : `签到可获得${nextPoints}积分` }}</text>
          </view>
        </view>
        
        <view class="checkin-stats">
          <view class="stat-item">
            <text class="stat-value">{{ consecutiveDays }}</text>
            <text class="stat-label">连续签到</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ totalDays }}</text>
            <text class="stat-label">累计签到</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ totalPoints }}</text>
            <text class="stat-label">累计积分</text>
          </view>
        </view>

        <view class="checkin-btn" :class="{ disabled: todayChecked }" @click="handleCheckin">
          <text class="checkin-text">{{ todayChecked ? '已签到' : '立即签到' }}</text>
        </view>
      </view>

      <!-- 签到日历 -->
      <view class="checkin-calendar">
        <view class="calendar-header">
          <text class="calendar-title">{{ currentMonth }}月签到记录</text>
          <view class="calendar-nav">
            <view class="nav-btn" @click="prevMonth">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </view>
            <view class="nav-btn" @click="nextMonth">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
        </view>

        <view class="calendar-weekdays">
          <text class="weekday" v-for="day in weekdays" :key="day">{{ day }}</text>
        </view>

        <view class="calendar-days">
          <view 
            class="calendar-day" 
            v-for="(day, index) in calendarDays" 
            :key="index"
            :class="{ 
              'other-month': day.otherMonth,
              'today': day.isToday,
              'checked': day.checked,
              'future': day.future
            }"
          >
            <text class="day-number">{{ day.day }}</text>
            <view class="day-status" v-if="day.checked">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </view>
          </view>
        </view>
      </view>

      <!-- 签到奖励 -->
      <view class="checkin-rewards">
        <view class="rewards-header">
          <text class="rewards-title">连续签到奖励</text>
          <text class="rewards-desc">连续签到天数越多，奖励越丰厚</text>
        </view>

        <view class="rewards-list">
          <view 
            class="reward-item" 
            v-for="(reward, index) in rewards" 
            :key="index"
            :class="{ 
              'completed': consecutiveDays >= reward.days,
              'current': consecutiveDays + 1 === reward.days
            }"
          >
            <view class="reward-day">
              <text class="day-text">第{{ reward.days }}天</text>
            </view>
            <view class="reward-content">
              <view class="reward-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polygon points="12 2 15.09 8.26 22 9 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9 8.91 8.26 12 2"></polygon>
                </svg>
              </view>
              <view class="reward-info">
                <text class="reward-name">{{ reward.name }}</text>
                <text class="reward-points">+{{ reward.points }}积分</text>
              </view>
            </view>
            <view class="reward-status">
              <svg v-if="consecutiveDays >= reward.days" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
              <text v-else-if="consecutiveDays + 1 === reward.days" class="current-text">明日可得</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 签到规则 -->
      <view class="checkin-rules">
        <view class="rules-header">
          <text class="rules-title">签到规则</text>
        </view>
        <view class="rules-content">
          <view class="rule-item">
            <text class="rule-text">• 每日签到可获得基础积分奖励</text>
          </view>
          <view class="rule-item">
            <text class="rule-text">• 连续签到天数越多，奖励越丰厚</text>
          </view>
          <view class="rule-item">
            <text class="rule-text">• 中断签到后连续天数重新计算</text>
          </view>
          <view class="rule-item">
            <text class="rule-text">• 积分可用于兑换商城商品</text>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 签到成功弹窗 -->
    <view class="checkin-modal" v-if="showCheckinModal" @click="hideCheckinModal">
      <view class="modal-content" @click.stop>
        <view class="modal-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </view>
        <text class="modal-title">签到成功！</text>
        <text class="modal-desc">获得{{ todayPoints }}积分奖励</text>
        <view class="modal-btn" @click="hideCheckinModal">
          <text class="btn-text">确定</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const userPoints = ref(1580)
const todayChecked = ref(false)
const todayPoints = ref(0)
const nextPoints = ref(10)
const consecutiveDays = ref(5)
const totalDays = ref(28)
const totalPoints = ref(350)
const currentMonth = ref(new Date().getMonth() + 1)
const showCheckinModal = ref(false)

// 星期数组
const weekdays = ['日', '一', '二', '三', '四', '五', '六']

// 奖励配置
const rewards = ref([
  { days: 1, name: '新手奖励', points: 10 },
  { days: 3, name: '坚持奖励', points: 15 },
  { days: 7, name: '周签奖励', points: 30 },
  { days: 15, name: '半月奖励', points: 50 },
  { days: 30, name: '月签奖励', points: 100 }
])

// 签到记录（模拟数据）
const checkinRecords = ref([
  '2024-01-15', '2024-01-16', '2024-01-17', '2024-01-18', '2024-01-19'
])

// 计算日历天数
const calendarDays = computed(() => {
  const year = new Date().getFullYear()
  const month = currentMonth.value - 1
  const today = new Date()
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  
  const days = []
  const current = new Date(startDate)
  
  for (let i = 0; i < 42; i++) {
    const dateStr = current.toISOString().split('T')[0]
    const isToday = current.toDateString() === today.toDateString()
    const isCurrentMonth = current.getMonth() === month
    const isFuture = current > today
    const isChecked = checkinRecords.value.includes(dateStr)
    
    days.push({
      day: current.getDate(),
      date: dateStr,
      isToday,
      otherMonth: !isCurrentMonth,
      future: isFuture,
      checked: isChecked && isCurrentMonth
    })
    
    current.setDate(current.getDate() + 1)
  }
  
  return days
})

// 页面加载
onMounted(() => {
  console.log('签到中心页面加载')
  loadCheckinData()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function handleCheckin() {
  if (todayChecked.value) return
  
  // 执行签到逻辑
  todayChecked.value = true
  todayPoints.value = nextPoints.value
  userPoints.value += nextPoints.value
  consecutiveDays.value += 1
  totalDays.value += 1
  totalPoints.value += nextPoints.value
  
  // 添加今日签到记录
  const today = new Date().toISOString().split('T')[0]
  checkinRecords.value.push(today)
  
  // 显示成功弹窗
  showCheckinModal.value = true
}

function hideCheckinModal() {
  showCheckinModal.value = false
}

function prevMonth() {
  if (currentMonth.value > 1) {
    currentMonth.value -= 1
  } else {
    currentMonth.value = 12
  }
}

function nextMonth() {
  if (currentMonth.value < 12) {
    currentMonth.value += 1
  } else {
    currentMonth.value = 1
  }
}

function loadCheckinData() {
  // 模拟加载签到数据
  setTimeout(() => {
    // 检查今日是否已签到
    const today = new Date().toISOString().split('T')[0]
    todayChecked.value = checkinRecords.value.includes(today)
    
    if (todayChecked.value) {
      todayPoints.value = 10
    }
  }, 500)
}
</script>

<style scoped>
/* 签到中心样式开始 */
.checkin-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(76, 175, 80, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
}

.back-btn svg {
  color: white;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.points-info {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  gap: 4px;
}

.points-info svg {
  color: #FFD700;
}

.points-text {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
  height: 100vh;
}

/* 签到状态卡片样式 */
.checkin-status-card {
  margin: 20px 16px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.status-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 28px;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.status-icon.checked {
  background: linear-gradient(135deg, #4CAF50, #45A049);
}

.status-icon svg {
  color: #999;
}

.status-icon.checked svg {
  color: white;
}

.status-info {
  flex: 1;
}

.status-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.status-desc {
  display: block;
  font-size: 14px;
  color: #666;
}

.checkin-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 24px;
  padding: 20px 0;
  background: #f8f9fa;
  border-radius: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #4CAF50;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.checkin-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #4CAF50, #45A049);
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.checkin-btn.disabled {
  background: #e0e0e0;
}

.checkin-text {
  font-size: 16px;
  color: white;
  font-weight: 600;
}

.checkin-btn.disabled .checkin-text {
  color: #999;
}

/* 签到日历样式 */
.checkin-calendar {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.calendar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.calendar-nav {
  display: flex;
  gap: 8px;
}

.nav-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.nav-btn svg {
  color: #666;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 8px;
}

.weekday {
  text-align: center;
  font-size: 12px;
  color: #666;
  font-weight: 500;
  padding: 8px 0;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.calendar-day {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.calendar-day.other-month {
  opacity: 0.3;
}

.calendar-day.today {
  background: #4CAF50;
}

.calendar-day.today .day-number {
  color: white;
  font-weight: 600;
}

.calendar-day.checked {
  background: #E8F5E8;
}

.calendar-day.future {
  opacity: 0.5;
}

.day-number {
  font-size: 14px;
  color: #333;
}

.day-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4CAF50;
  border-radius: 8px;
}

.day-status svg {
  color: white;
}

/* 签到奖励样式 */
.checkin-rewards {
  margin: 0 16px 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.rewards-header {
  margin-bottom: 16px;
}

.rewards-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.rewards-desc {
  display: block;
  font-size: 12px;
  color: #666;
}

.rewards-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reward-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.reward-item.completed {
  background: #E8F5E8;
  border-color: #4CAF50;
}

.reward-item.current {
  background: #FFF3E0;
  border-color: #FF9800;
}

.reward-day {
  width: 60px;
  text-align: center;
}

.day-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.reward-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.reward-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFD700;
  border-radius: 16px;
}

.reward-icon svg {
  color: white;
}

.reward-info {
  flex: 1;
}

.reward-name {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.reward-points {
  display: block;
  font-size: 12px;
  color: #4CAF50;
  font-weight: 600;
}

.reward-status {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reward-status svg {
  color: #4CAF50;
}

.current-text {
  font-size: 10px;
  color: #FF9800;
  font-weight: 500;
}

/* 签到规则样式 */
.checkin-rules {
  margin: 0 16px 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
}

.rules-header {
  margin-bottom: 12px;
}

.rules-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.rules-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rule-item {
  padding: 4px 0;
}

.rule-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 签到成功弹窗样式 */
.checkin-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  min-width: 280px;
}

.modal-icon {
  margin-bottom: 16px;
}

.modal-icon svg {
  color: #4CAF50;
}

.modal-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.modal-desc {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
}

.modal-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #4CAF50, #45A049);
  border-radius: 8px;
  text-align: center;
}

.btn-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 签到中心样式结束 */
</style>
