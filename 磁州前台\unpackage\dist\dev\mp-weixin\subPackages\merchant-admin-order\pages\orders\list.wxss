
.order-list-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 16px;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}
.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}
.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.search-icon {
  font-size: 20px;
  color: #fff;
}
.search-bar {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
.search-input {
  flex: 1;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 18px;
  padding: 0 12px;
  font-size: 14px;
}
.search-btn {
  padding: 0 12px;
  height: 36px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 18px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
.cancel-btn {
  padding: 0 12px;
  height: 36px;
  color: #666;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
.filter-section {
  display: flex;
  padding: 12px 16px;
  background-color: #fff;
  margin-top: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
.filter-scroll {
  flex: 1;
  white-space: nowrap;
}
.status-tag {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 8px;
  background-color: #f0f0f0;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
}
.status-tag.active {
  background-color: #e6f7ff;
  color: #1677FF;
  border: 1px solid #91caff;
}
.filter-button {
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-left: 8px;
  border-left: 1px solid #eee;
}
.filter-icon {
  margin-right: 4px;
}
.sort-section {
  display: flex;
  background-color: #fff;
  padding: 12px 16px;
  margin-top: 1px;
}
.sort-option {
  margin-right: 24px;
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
}
.sort-option.active {
  color: #1677FF;
  font-weight: 500;
}
.sort-icon {
  margin-left: 4px;
}
.order-list {
  padding: 16px;
}
.order-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}
.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}
.order-number {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}
.order-status {
  font-size: 14px;
  font-weight: 500;
}
.order-info {
  margin-bottom: 12px;
}
.customer-info, .time-info, .amount-info {
  display: flex;
  margin-bottom: 6px;
}
.label {
  width: 70px;
  color: #666;
  font-size: 13px;
}
.value {
  flex: 1;
  font-size: 13px;
  color: #333;
}
.price {
  font-weight: 600;
  color: #ff6a00;
}
.product-preview {
  display: flex;
  margin-bottom: 12px;
}
.product-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  margin-right: 8px;
  background-color: #f0f0f0;
}
.more-products {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  background-color: rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
}
.order-actions {
  display: flex;
  justify-content: flex-end;
}
.action-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  margin-left: 8px;
  background-color: #f0f0f0;
  color: #333;
}
.action-btn.primary {
  background-color: #1677FF;
  color: #fff;
}
.load-more {
  text-align: center;
  padding: 16px 0;
}
.load-text {
  font-size: 14px;
  color: #666;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}
.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 16px;
}
.refresh-btn {
  padding: 8px 24px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
}
.batch-action-btn {
  position: fixed;
  right: 16px;
  bottom: 80px;
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #1677FF;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 100;
}
.batch-icon {
  color: #fff;
  font-size: 24px;
}
.batch-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  padding: 12px 16px;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}
.selection-info {
  font-size: 14px;
  color: #666;
}
.selected-count {
  color: #1677FF;
  font-weight: 600;
}
.batch-actions {
  display: flex;
}
.batch-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  margin-left: 8px;
  background-color: #f0f0f0;
  color: #333;
}
.batch-btn.primary {
  background-color: #1677FF;
  color: #fff;
}
