"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  setup() {
    const couponId = common_vendor.ref("");
    const couponData = common_vendor.reactive({
      id: 1,
      title: "新客专享优惠",
      status: "active",
      statusText: "进行中",
      value: 10,
      minSpend: 100,
      startDate: "2023-04-15",
      expireDate: "2023-05-15",
      qrCodeUrl: "/static/images/coupon-qrcode-placeholder.png",
      totalCount: 500,
      claimedCount: 234,
      usedCount: 156,
      conversionRate: 66.7,
      typeText: "满减券",
      applicableProducts: "全部商品",
      perPersonLimit: 1,
      instructions: "仅限新用户使用，不可与其他优惠同时使用",
      recentRecords: [
        {
          id: 1,
          userName: "张三",
          userAvatar: "/static/images/avatar-1.jpg",
          time: "2023-04-20 14:30",
          isUsed: true
        },
        {
          id: 2,
          userName: "李四",
          userAvatar: "/static/images/avatar-2.jpg",
          time: "2023-04-20 15:45",
          isUsed: false
        },
        {
          id: 3,
          userName: "王五",
          userAvatar: "/static/images/avatar-3.jpg",
          time: "2023-04-21 09:20",
          isUsed: false
        }
      ]
    });
    const showSharePopup = common_vendor.ref(false);
    function goBack() {
      common_vendor.index.navigateBack();
    }
    function showMoreOptions() {
      common_vendor.index.showActionSheet({
        itemList: ["复制优惠券信息", "导出数据", "设为模板"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              common_vendor.index.setClipboardData({
                data: `${couponData.title}：满${couponData.minSpend}减${couponData.value}，有效期至${couponData.expireDate}`,
                success: () => {
                  common_vendor.index.showToast({
                    title: "已复制优惠券信息",
                    icon: "success"
                  });
                }
              });
              break;
            case 1:
              common_vendor.index.showToast({
                title: "正在导出数据...",
                icon: "loading"
              });
              setTimeout(() => {
                common_vendor.index.showToast({
                  title: "导出成功",
                  icon: "success"
                });
              }, 1500);
              break;
            case 2:
              common_vendor.index.showToast({
                title: "已设为模板",
                icon: "success"
              });
              break;
          }
        }
      });
    }
    function toggleCouponStatus() {
      const isActive = couponData.status === "active";
      const newStatus = isActive ? "paused" : "active";
      const statusText = isActive ? "已暂停" : "进行中";
      couponData.status = newStatus;
      couponData.statusText = statusText;
      common_vendor.index.showToast({
        title: isActive ? "已暂停优惠券" : "已启用优惠券",
        icon: "success"
      });
    }
    function confirmDeleteCoupon() {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除"${couponData.title}"吗？此操作无法撤销。`,
        confirmColor: "#FF3B30",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "已删除优惠券",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          }
        }
      });
    }
    function editCoupon() {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon/edit?id=${couponData.id}`,
        animationType: "slide-in-right"
      });
    }
    function viewAllRecords() {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon/records?id=${couponData.id}`,
        animationType: "slide-in-right"
      });
    }
    function shareCoupon() {
      showSharePopup.value = true;
    }
    function hideSharePopup() {
      showSharePopup.value = false;
    }
    function shareToChannel(channel) {
      common_vendor.index.showToast({
        title: `已分享到${channel === "wechat" ? "微信" : channel === "moments" ? "朋友圈" : channel === "link" ? "链接已复制" : "二维码已保存"}`,
        icon: "success"
      });
      hideSharePopup();
    }
    function loadCouponData(id) {
      couponId.value = id;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
      }, 500);
    }
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      if (options.id) {
        loadCouponData(options.id);
      }
    });
    return {
      couponData,
      showSharePopup,
      goBack,
      showMoreOptions,
      toggleCouponStatus,
      confirmDeleteCoupon,
      editCoupon,
      viewAllRecords,
      shareCoupon,
      hideSharePopup,
      shareToChannel
    };
  }
};
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_circle + _component_svg + _component_path + _component_rect + _component_polygon + _component_line + _component_polyline)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $setup.goBack && $setup.goBack(...args)),
    b: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "1"
    }),
    c: common_vendor.p({
      cx: "19",
      cy: "12",
      r: "1"
    }),
    d: common_vendor.p({
      cx: "5",
      cy: "12",
      r: "1"
    }),
    e: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    f: common_vendor.o((...args) => $setup.showMoreOptions && $setup.showMoreOptions(...args)),
    g: common_vendor.t($setup.couponData.title),
    h: common_vendor.t($setup.couponData.statusText),
    i: common_vendor.n("status-" + $setup.couponData.status),
    j: common_vendor.t($setup.couponData.value),
    k: common_vendor.t($setup.couponData.minSpend),
    l: common_vendor.t($setup.couponData.startDate),
    m: common_vendor.t($setup.couponData.expireDate),
    n: $setup.couponData.status === "active"
  }, $setup.couponData.status === "active" ? {
    o: $setup.couponData.qrCodeUrl
  } : {}, {
    p: common_vendor.t($setup.couponData.totalCount),
    q: common_vendor.t($setup.couponData.claimedCount),
    r: common_vendor.t($setup.couponData.usedCount),
    s: common_vendor.t($setup.couponData.conversionRate),
    t: $setup.couponData.claimedCount / $setup.couponData.totalCount * 100 + "%",
    v: common_vendor.t($setup.couponData.claimedCount),
    w: common_vendor.t($setup.couponData.totalCount),
    x: common_vendor.t($setup.couponData.typeText),
    y: common_vendor.t($setup.couponData.minSpend),
    z: common_vendor.t($setup.couponData.applicableProducts),
    A: common_vendor.t($setup.couponData.perPersonLimit),
    B: common_vendor.t($setup.couponData.instructions),
    C: common_vendor.o((...args) => $setup.viewAllRecords && $setup.viewAllRecords(...args)),
    D: common_vendor.f($setup.couponData.recentRecords, (record, index, i0) => {
      return {
        a: record.userAvatar,
        b: common_vendor.t(record.userName),
        c: common_vendor.t(record.time),
        d: common_vendor.t(record.isUsed ? "已使用" : "未使用"),
        e: record.isUsed ? 1 : "",
        f: index
      };
    }),
    E: $setup.couponData.recentRecords.length === 0
  }, $setup.couponData.recentRecords.length === 0 ? {} : {}, {
    F: common_vendor.p({
      d: "M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"
    }),
    G: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    H: common_vendor.o((...args) => $setup.editCoupon && $setup.editCoupon(...args)),
    I: $setup.couponData.status === "active"
  }, $setup.couponData.status === "active" ? {
    J: common_vendor.p({
      x: "6",
      y: "4",
      width: "4",
      height: "16"
    }),
    K: common_vendor.p({
      x: "14",
      y: "4",
      width: "4",
      height: "16"
    }),
    L: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    })
  } : {
    M: common_vendor.p({
      points: "5 3 19 12 5 21 5 3"
    }),
    N: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    })
  }, {
    O: common_vendor.t($setup.couponData.status === "active" ? "暂停" : "启用"),
    P: common_vendor.n($setup.couponData.status === "active" ? "pause" : "activate"),
    Q: common_vendor.o((...args) => $setup.toggleCouponStatus && $setup.toggleCouponStatus(...args)),
    R: common_vendor.p({
      cx: "18",
      cy: "5",
      r: "3"
    }),
    S: common_vendor.p({
      cx: "6",
      cy: "12",
      r: "3"
    }),
    T: common_vendor.p({
      cx: "18",
      cy: "19",
      r: "3"
    }),
    U: common_vendor.p({
      x1: "8.59",
      y1: "13.51",
      x2: "15.42",
      y2: "17.49"
    }),
    V: common_vendor.p({
      x1: "15.41",
      y1: "6.51",
      x2: "8.59",
      y2: "10.49"
    }),
    W: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    X: common_vendor.o((...args) => $setup.shareCoupon && $setup.shareCoupon(...args)),
    Y: common_vendor.p({
      points: "3 6 5 6 21 6"
    }),
    Z: common_vendor.p({
      d: "M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
    }),
    aa: common_vendor.p({
      x1: "10",
      y1: "11",
      x2: "10",
      y2: "17"
    }),
    ab: common_vendor.p({
      x1: "14",
      y1: "11",
      x2: "14",
      y2: "17"
    }),
    ac: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    ad: common_vendor.o((...args) => $setup.confirmDeleteCoupon && $setup.confirmDeleteCoupon(...args)),
    ae: $setup.showSharePopup
  }, $setup.showSharePopup ? {
    af: common_vendor.o((...args) => $setup.hideSharePopup && $setup.hideSharePopup(...args)),
    ag: common_vendor.o((...args) => $setup.hideSharePopup && $setup.hideSharePopup(...args)),
    ah: common_vendor.p({
      d: "M9.5 9.5c-.3-2.1 1.6-4 4.8-4.3 3.1-.3 5.5 1.2 5.9 3.5.4 2.3-1.5 4.8-4.5 5.2-1 .1-1.9 0-2.7-.4l-2.4 1.2.5-2.2c-1.1-.8-1.8-1.9-1.6-3z"
    }),
    ai: common_vendor.p({
      d: "M4 14.5c-.5-2.5 2.3-5 6.2-5.5 3.9-.5 7.5 1 8 3.5.5 2.5-1.8 5-5.7 5.5-1.2.2-2.3 0-3.4-.4l-3 1.5.6-2.7c-1.4-1-2.4-2.3-2.7-3.9z"
    }),
    aj: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    ak: common_vendor.o(($event) => $setup.shareToChannel("wechat")),
    al: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    am: common_vendor.p({
      d: "M12 8L12 16"
    }),
    an: common_vendor.p({
      d: "M8 12L16 12"
    }),
    ao: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    ap: common_vendor.o(($event) => $setup.shareToChannel("moments")),
    aq: common_vendor.p({
      d: "M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"
    }),
    ar: common_vendor.p({
      d: "M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"
    }),
    as: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    at: common_vendor.o(($event) => $setup.shareToChannel("link")),
    av: common_vendor.p({
      x: "3",
      y: "3",
      width: "7",
      height: "7"
    }),
    aw: common_vendor.p({
      x: "14",
      y: "3",
      width: "7",
      height: "7"
    }),
    ax: common_vendor.p({
      x: "3",
      y: "14",
      width: "7",
      height: "7"
    }),
    ay: common_vendor.p({
      x: "14",
      y: "14",
      width: "7",
      height: "7"
    }),
    az: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    aA: common_vendor.o(($event) => $setup.shareToChannel("qrcode"))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/coupon/detail.js.map
