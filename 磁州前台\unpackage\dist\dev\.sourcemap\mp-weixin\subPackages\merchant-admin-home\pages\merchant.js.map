{"version": 3, "file": "merchant.js", "sources": ["subPackages/merchant-admin-home/pages/merchant.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4taG9tZVxwYWdlc1xtZXJjaGFudC52dWU"], "sourcesContent": ["<template>\n  <view class=\"merchant-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-left\" @click=\"goBack\">\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\n      </view>\n      <view class=\"navbar-title\">商家后台</view>\n      <view class=\"navbar-right\" @click=\"navigateToSettings\">\n        <image src=\"/static/images/tabbar/设置.png\" class=\"setting-icon\"></image>\n      </view>\n    </view>\n    \n    <!-- 商家信息卡片 -->\n    <view class=\"merchant-card\" :style=\"{ marginTop: (navbarHeight + 10) + 'px' }\">\n      <view class=\"merchant-header\">\n        <image class=\"merchant-logo\" :src=\"merchantInfo.logo || '/static/images/default-shop.png'\" mode=\"aspectFill\"></image>\n        <view class=\"merchant-info\">\n          <view class=\"merchant-name\">{{ merchantInfo.name || '未认证商家' }}</view>\n          <view class=\"merchant-status\" :class=\"{'verified': merchantInfo.verified}\">\n            <image class=\"status-icon\" :src=\"merchantInfo.verified ? '/static/images/tabbar/已认证.png' : '/static/images/tabbar/未认证.png'\"></image>\n            <text class=\"status-text\">{{ merchantInfo.verified ? '已认证' : '未认证' }}</text>\n          </view>\n        </view>\n        <view class=\"merchant-action\">\n          <button v-if=\"!merchantInfo.verified\" class=\"verify-btn\" @click=\"navigateToVerify\">立即认证</button>\n          <button v-else class=\"edit-btn\" @click=\"navigateToMerchantEdit\">编辑资料</button>\n        </view>\n      </view>\n      \n      <view class=\"merchant-stats\">\n        <view class=\"stat-item\">\n          <view class=\"stat-number\">{{ statsData.views }}</view>\n          <view class=\"stat-label\">今日浏览</view>\n        </view>\n        <view class=\"stat-divider\"></view>\n        <view class=\"stat-item\">\n          <view class=\"stat-number\">{{ statsData.leads }}</view>\n          <view class=\"stat-label\">咨询量</view>\n        </view>\n        <view class=\"stat-divider\"></view>\n        <view class=\"stat-item\">\n          <view class=\"stat-number\">{{ statsData.income }}</view>\n          <view class=\"stat-label\">本月收入</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 推广模块 (新增) -->\n    <view class=\"promotion-section\">\n      <view class=\"favorite-button\" @click=\"goToProfile\">\n        <view class=\"favorite-icon-wrapper\">\n          <image class=\"favorite-icon\" src=\"/static/images/tabbar/星星.png\"></image>\n        </view>\n        <text class=\"favorite-text\">收藏/关注</text>\n      </view>\n    </view>\n    \n    <!-- 核心业务模块 (修改) -->\n    <view class=\"core-business-section\">\n      <view class=\"section-header\">\n        <view class=\"title-wrapper\">\n          <view class=\"title-indicator title-indicator-core\"></view>\n          <text class=\"section-title\">信息管理</text>\n      </view>\n        <text class=\"section-subtitle\">管理您发布的信息内容</text>\n    </view>\n    \n      <view class=\"core-grid\">\n        <view class=\"core-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/info-publish')\">\n          <view class=\"core-icon-wrapper bg-publish\">\n            <image class=\"core-icon\" src=\"/static/images/tabbar/发布.png\"></image>\n          </view>\n          <text class=\"core-name\">发布信息</text>\n        </view>\n        \n        <view class=\"core-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/info-manage')\">\n          <view class=\"core-icon-wrapper bg-manage\">\n            <image class=\"core-icon\" src=\"/static/images/tabbar/管理.png\"></image>\n          </view>\n          <text class=\"core-name\">信息管理</text>\n        </view>\n        \n        <view class=\"core-item\" @click=\"navigateTo('/subPackages/merchant-admin/pages/products/create')\">\n          <view class=\"core-icon-wrapper bg-goods\">\n            <image class=\"core-icon\" src=\"/static/images/tabbar/商品.png\"></image>\n          </view>\n          <text class=\"core-name\">商品管理</text>\n        </view>\n        \n        <view class=\"core-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/reviews')\">\n          <view class=\"core-icon-wrapper bg-reviews\">\n            <image class=\"core-icon\" src=\"/static/images/tabbar/评价.png\"></image>\n          </view>\n          <text class=\"core-name\">评价留言</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 营销推广 -->\n    <view class=\"marketing-section\">\n      <view class=\"section-header\">\n        <view class=\"title-wrapper\">\n          <view class=\"title-indicator title-indicator-marketing\"></view>\n          <text class=\"section-title\">营销推广</text>\n        </view>\n        <text class=\"section-subtitle\">提升店铺曝光与转化</text>\n      </view>\n      \n      <view class=\"marketing-grid\">\n        <view class=\"marketing-item\" @click=\"navigateTo('/subPackages/merchant-admin-marketing/pages/marketing/index')\">\n          <view class=\"marketing-icon-wrapper bg-marketing\">\n            <image class=\"marketing-icon\" src=\"/static/images/tabbar/营销.png\"></image>\n          </view>\n          <text class=\"marketing-name\">营销中心</text>\n          </view>\n        \n        <view class=\"marketing-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/coupon')\">\n          <view class=\"marketing-icon-wrapper bg-coupon\">\n            <image class=\"marketing-icon\" src=\"/static/images/tabbar/优惠券.png\"></image>\n          </view>\n          <text class=\"marketing-name\">优惠券</text>\n        </view>\n        \n        <view class=\"marketing-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/red-packet-manage')\">\n          <view class=\"marketing-icon-wrapper bg-redpacket\">\n            <image class=\"marketing-icon\" src=\"/static/images/tabbar/红包.png\"></image>\n          </view>\n          <text class=\"marketing-name\">红包</text>\n        </view>\n        \n        <view class=\"marketing-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/poster')\">\n          <view class=\"marketing-icon-wrapper bg-poster\">\n            <image class=\"marketing-icon\" src=\"/static/images/tabbar/海报.png\"></image>\n          </view>\n          <text class=\"marketing-name\">宣传海报</text>\n        </view>\n      </view>\n    </view>\n        \n    <!-- 经营分析 -->\n    <view class=\"analysis-section\">\n      <view class=\"section-header\">\n        <view class=\"title-wrapper\">\n          <view class=\"title-indicator title-indicator-analysis\"></view>\n          <text class=\"section-title\">经营分析</text>\n          </view>\n        <text class=\"section-subtitle\">了解店铺经营数据</text>\n        </view>\n        \n      <view class=\"analysis-grid\">\n        <view class=\"analysis-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/analysis')\">\n          <view class=\"analysis-icon-wrapper bg-analysis\">\n            <image class=\"analysis-icon\" src=\"/static/images/tabbar/数据.png\"></image>\n          </view>\n          <text class=\"analysis-name\">数据分析</text>\n        </view>\n        \n        <view class=\"analysis-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/finance')\">\n          <view class=\"analysis-icon-wrapper bg-finance\">\n            <image class=\"analysis-icon\" src=\"/static/images/tabbar/财务.png\"></image>\n          </view>\n          <text class=\"analysis-name\">财务管理</text>\n        </view>\n        \n        <view class=\"analysis-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/visitor')\">\n          <view class=\"analysis-icon-wrapper bg-visitor\">\n            <image class=\"analysis-icon\" src=\"/static/images/tabbar/访客.png\"></image>\n          </view>\n          <text class=\"analysis-name\">访客统计</text>\n        </view>\n    \n        <view class=\"analysis-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/leads')\">\n          <view class=\"analysis-icon-wrapper bg-leads\">\n            <image class=\"analysis-icon\" src=\"/static/images/tabbar/咨询.png\"></image>\n          </view>\n          <text class=\"analysis-name\">咨询管理</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 快捷工具 -->\n    <view class=\"tools-section\">\n      <view class=\"section-header\">\n        <view class=\"title-wrapper\">\n          <view class=\"title-indicator title-indicator-tools\"></view>\n          <text class=\"section-title\">快捷工具</text>\n        </view>\n      </view>\n      \n      <view class=\"tools-grid\">\n        <view class=\"tool-item\" @click=\"navigateTo('/subPackages/merchant-admin-order/pages/orders/list')\">\n          <view class=\"tool-icon-wrapper bg-orders\">\n            <image class=\"tool-icon\" src=\"/static/images/tabbar/订单.png\"></image>\n            </view>\n          <text class=\"tool-name\">订单管理</text>\n            </view>\n        \n        <view class=\"tool-item\" @click=\"() => {\n          uni.navigateTo({\n            url: '/subPackages/merchant-admin-marketing/pages/marketing/verification/index'\n          });\n        }\">\n          <view class=\"tool-icon-wrapper bg-verification\">\n            <image class=\"tool-icon\" src=\"/static/images/tabbar/核销.png\"></image>\n            </view>\n          <text class=\"tool-name\">核销中心</text>\n          </view>\n        \n        <view class=\"tool-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/customer')\">\n          <view class=\"tool-icon-wrapper bg-customer\">\n            <image class=\"tool-icon\" src=\"/static/images/tabbar/客户.png\"></image>\n        </view>\n          <text class=\"tool-name\">客户管理</text>\n        </view>\n        \n        <view class=\"tool-item\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/settings')\">\n          <view class=\"tool-icon-wrapper bg-settings\">\n            <image class=\"tool-icon\" src=\"/static/images/tabbar/设置.png\"></image>\n          </view>\n          <text class=\"tool-name\">店铺设置</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 行业专属功能 - 根据商家类目动态显示 -->\n    <view class=\"industry-section\" v-if=\"merchantCategory\">\n      <view class=\"section-header\">\n        <view class=\"section-title-large\">{{ getCategoryTitle(merchantCategory) }}</view>\n        <text class=\"section-more\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/industry-features')\">更多</text>\n      </view>\n      \n      <view class=\"industry-features\">\n        <template v-if=\"merchantCategory === 'food'\">\n          <!-- 美食小吃行业功能 -->\n          <view class=\"industry-card\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/food/menu')\">\n            <image class=\"industry-card-bg\" src=\"/static/images/food-menu.jpg\" mode=\"aspectFill\"></image>\n            <view class=\"industry-card-content\">\n              <view class=\"industry-card-title\">菜单管理</view>\n              <view class=\"industry-card-desc\">管理店铺菜品和价格</view>\n            </view>\n          </view>\n        </template>\n        \n        <template v-else-if=\"merchantCategory === 'house'\">\n          <!-- 房产中介行业功能 -->\n          <view class=\"industry-card\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/house/listing')\">\n            <image class=\"industry-card-bg\" src=\"/static/images/house-listing.jpg\" mode=\"aspectFill\"></image>\n            <view class=\"industry-card-content\">\n              <view class=\"industry-card-title\">房源管理</view>\n              <view class=\"industry-card-desc\">管理出售/出租房源</view>\n            </view>\n          </view>\n        </template>\n        \n        <template v-else-if=\"merchantCategory === 'decoration'\">\n          <!-- 装修家居行业功能 -->\n          <view class=\"industry-card\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/decoration/case')\">\n            <image class=\"industry-card-bg\" src=\"/static/images/decoration-case.jpg\" mode=\"aspectFill\"></image>\n            <view class=\"industry-card-content\">\n              <view class=\"industry-card-title\">案例展示</view>\n              <view class=\"industry-card-desc\">管理装修案例和效果图</view>\n            </view>\n          </view>\n        </template>\n        \n        <template v-else-if=\"merchantCategory === 'service'\">\n          <!-- 到家服务行业功能 -->\n          <view class=\"industry-card\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/services')\">\n            <image class=\"industry-card-bg\" src=\"/static/images/service-manage.jpg\" mode=\"aspectFill\"></image>\n            <view class=\"industry-card-content\">\n              <view class=\"industry-card-title\">服务管理</view>\n              <view class=\"industry-card-desc\">管理服务项目和价格</view>\n            </view>\n          </view>\n        </template>\n      </view>\n    </view>\n    \n    <!-- 待处理事项 - 已移动到底部 -->\n    <view class=\"pending-section\">\n      <view class=\"section-header\">\n        <text class=\"section-title\">待处理事项</text>\n        <text class=\"section-more\" @click=\"navigateTo('/subPackages/merchant-plugin/pages/todo')\">查看全部</text>\n      </view>\n      \n      <view class=\"pending-list\">\n        <view v-if=\"pendingItems.length > 0\">\n          <view class=\"pending-item\" v-for=\"(item, index) in pendingItems\" :key=\"index\" @click=\"handlePendingItem(item)\">\n            <view class=\"pending-left\">\n              <text class=\"pending-badge\" :class=\"'badge-' + item.type\">{{ item.count }}</text>\n            </view>\n            <view class=\"pending-center\">\n              <view class=\"pending-title\">{{ item.title }}</view>\n              <view class=\"pending-desc\">{{ item.desc }}</view>\n            </view>\n            <view class=\"pending-right\">\n              <text class=\"action-btn\">处理</text>\n            </view>\n          </view>\n        </view>\n        <view v-else class=\"empty-view\">\n          <image class=\"empty-icon\" src=\"/static/images/empty-inbox.png\" mode=\"aspectFit\"></image>\n          <view class=\"empty-text\">暂无待处理事项</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { smartNavigate } from '@/utils/navigation.js';\n\nexport default {\n  data() {\n    return {\n      statusBarHeight: 20,\n      navbarHeight: 64,\n      merchantInfo: {\n        logo: '',\n        name: '',\n        verified: false,\n        merchantId: ''\n      },\n      statsData: {\n        views: 0,\n        leads: 0,\n        income: '0.00'\n      },\n      pendingItems: [],\n      merchantCategory: ''\n    }\n  },\n  onLoad() {\n    // 获取状态栏高度\n    const sysInfo = uni.getSystemInfoSync();\n    this.statusBarHeight = sysInfo.statusBarHeight;\n    this.navbarHeight = this.statusBarHeight + 44;\n    \n    // 加载商家信息和数据\n    this.loadMerchantInfo();\n    this.loadStatsData();\n    this.loadPendingItems();\n  },\n  \n\n  \n  onShareAppMessage() {\n    // 自定义转发内容\n    return {\n      title: '商家后台 - 专业管理工具，助力商家成长',\n      path: '/pages/my/merchant'\n    }\n  },\n  \n  methods: {\n    // 返回上一页\n    goBack() {\n      uni.navigateBack();\n    },\n    \n    // 跳转到设置页面\n    navigateToSettings() {\n      smartNavigate('/subPackages/merchant-plugin/pages/settings').catch(err => {\n        console.error('跳转到设置页面失败:', err);\n      });\n    },\n    \n    // 页面跳转\n    navigateTo(url) {\n      // 直接跳转，不再拦截未认证商家\n      smartNavigate(url).catch(err => {\n        console.error('页面跳转失败:', err);\n      });\n    },\n    \n    // 跳转到商家认证页面\n    navigateToVerify() {\n      smartNavigate('/subPackages/merchant-plugin/pages/verify');\n    },\n    \n    // 跳转到商家资料编辑页面\n    navigateToMerchantEdit() {\n      smartNavigate('/subPackages/merchant-plugin/pages/profile');\n    },\n    \n    // 处理待办事项\n    handlePendingItem(item) {\n      const urlMap = {\n        'order': '/subPackages/merchant-plugin/pages/orders',\n        'comment': '/subPackages/merchant-plugin/pages/reviews',\n        'finance': '/subPackages/merchant-plugin/pages/finance',\n        'approval': '/subPackages/merchant-plugin/pages/approval'\n      };\n      \n      if (urlMap[item.type]) {\n        this.navigateTo(urlMap[item.type]);\n      }\n    },\n    \n    // 加载商家信息\n    loadMerchantInfo() {\n      // 模拟API请求\n      setTimeout(() => {\n        // 随机商家信息，实际应该从API获取\n        const verified = Math.random() > 0.3; // 70%概率已认证\n        \n        this.merchantInfo = {\n          logo: verified ? '/static/images/shop-logo.png' : '',\n          name: verified ? '磁州特色小吃店' : '',\n          verified: verified,\n          merchantId: 'M' + Math.floor(Math.random() * 1000000).toString().padStart(6, '0')\n        };\n      }, 500);\n    },\n    \n    // 加载数据统计\n    loadStatsData() {\n      // 模拟API请求\n      setTimeout(() => {\n        this.statsData = {\n          views: Math.floor(Math.random() * 500),\n          leads: Math.floor(Math.random() * 100),\n          income: (Math.random() * 1000).toFixed(2)\n        };\n      }, 600);\n    },\n    \n    // 加载待处理事项\n    loadPendingItems() {\n      // 模拟API请求\n      setTimeout(() => {\n        // 随机显示待处理事项，实际应该从API获取\n        const havePendingItems = Math.random() > 0.3; // 70%概率有待处理事项\n        \n        if (havePendingItems) {\n          this.pendingItems = [\n            {\n              type: 'service',\n              count: Math.floor(Math.random() * 10) + 1,\n              title: '待处理服务',\n              desc: '有新的服务预约需要确认',\n              url: '/subPackages/merchant-plugin/pages/service-orders'\n            },\n            {\n              type: 'comment',\n              count: Math.floor(Math.random() * 5) + 1,\n              title: '新评价待回复',\n              desc: '有客户的评价需要回复',\n              url: '/subPackages/merchant-plugin/pages/reviews'\n            },\n            {\n              type: 'finance',\n              count: Math.floor(Math.random() * 3) + 1,\n              title: '待结算账单',\n              desc: '有账单需要确认结算',\n              url: '/subPackages/merchant-plugin/pages/finance'\n            },\n            {\n              type: 'info',\n              count: Math.floor(Math.random() * 2) + 1,\n              title: '信息即将到期',\n              desc: '有信息需要更新或续期',\n              url: '/subPackages/merchant-plugin/pages/info-manage'\n            }\n          ];\n          \n          // 随机显示0-3个待处理事项\n          const count = Math.floor(Math.random() * 3) + 1;\n          this.pendingItems = this.pendingItems.slice(0, count);\n        } else {\n          this.pendingItems = [];\n        }\n      }, 700);\n    },\n    \n    // 获取商家类目标题\n    getCategoryTitle(category) {\n      const categoryTitles = {\n        'food': '美食小吃',\n        'house': '房产中介',\n        'decoration': '装修家居',\n        'service': '到家服务'\n      };\n      return categoryTitles[category] || '未分类商家';\n    },\n\n    // 跳转到个人资料页面\n    goToProfile() {\n      // 使用smartNavigate跳转到个人资料页面\n      uni.navigateTo({\n        url: '/pages/user-center/profile',\n        fail: (err) => {\n          console.error('跳转到个人资料页面失败:', err);\n          uni.showToast({\n            title: '跳转失败，请稍后再试',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    \n\n  }\n}\n</script>\n\n<style>\n.merchant-container {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  padding-bottom: 40rpx;\n  height: 100vh;\n  overflow-y: auto;\n}\n\n/* 自定义导航栏 */\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 44px;\n  background: linear-gradient(135deg, #007AFF, #5856D6);\n  color: #fff;\n  display: flex;\n  align-items: center;\n  padding: 0 20px;\n  z-index: 999;\n  box-shadow: 0 2px 10px rgba(0, 122, 255, 0.2);\n}\n\n.navbar-left {\n  width: 80rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n}\n\n.back-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 500;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 80rpx;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.setting-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n/* 商家信息卡片 */\n.merchant-card {\n  background-color: #fff;\n  margin: 0 30rpx 30rpx;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);\n  transform: translateZ(0);\n}\n\n.merchant-header {\n  padding: 30rpx;\n  display: flex;\n  align-items: center;\n  position: relative;\n  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));\n}\n\n.merchant-logo {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 20rpx;\n  background-color: #f5f7fa;\n  margin-right: 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n}\n\n.merchant-info {\n  flex: 1;\n}\n\n.merchant-name {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.merchant-status {\n  display: flex;\n  align-items: center;\n}\n\n.status-icon {\n  width: 28rpx;\n  height: 28rpx;\n  margin-right: 8rpx;\n}\n\n.status-text {\n  font-size: 24rpx;\n  color: #ff9800;\n}\n\n.verified .status-text {\n  color: #34C759;\n}\n\n.merchant-action {\n  margin-left: 30rpx;\n}\n\n.verify-btn {\n  background: linear-gradient(135deg, #FF9500, #FF5E3A);\n  color: #fff;\n  font-size: 26rpx;\n  padding: 10rpx 30rpx;\n  border-radius: 30rpx;\n  height: 60rpx;\n  line-height: 40rpx;\n  margin: 0;\n  box-shadow: 0 4rpx 12rpx rgba(255, 94, 58, 0.2);\n}\n\n.edit-btn {\n  background: linear-gradient(135deg, #007AFF, #5856D6);\n  color: #fff;\n  font-size: 26rpx;\n  padding: 10rpx 30rpx;\n  border-radius: 30rpx;\n  height: 60rpx;\n  line-height: 40rpx;\n  margin: 0;\n  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);\n}\n\n/* 商家数据统计 */\n.merchant-stats {\n  display: flex;\n  padding: 20rpx 0;\n  border-top: 1rpx solid #f0f0f0;\n  background: rgba(248, 249, 250, 0.5);\n}\n\n.stat-item {\n  flex: 1;\n  text-align: center;\n  padding: 10rpx 0;\n}\n\n.stat-number {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 4rpx;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.stat-divider {\n  width: 2rpx;\n  background-color: #f0f0f0;\n}\n\n/* 核心业务模块 */\n.core-business-section {\n  background-color: #fff;\n  margin: 0 30rpx 30rpx;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\n  transform: translateZ(0);\n}\n\n.title-wrapper {\n  display: flex;\n  align-items: center;\n}\n\n.title-indicator {\n  width: 6rpx;\n  height: 30rpx;\n  border-radius: 3rpx;\n  margin-right: 15rpx;\n}\n\n.title-indicator-core {\n  background: linear-gradient(to bottom, #FF9500, #FF5E3A);\n}\n\n.section-subtitle {\n  font-size: 26rpx;\n  color: #999;\n  margin-top: 10rpx;\n}\n\n.core-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 20rpx -10rpx 0;\n}\n\n.core-item {\n  width: 25%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30rpx;\n  padding: 0 10rpx;\n  box-sizing: border-box;\n  transition: all 0.3s;\n}\n\n.core-item:active {\n  transform: scale(0.95);\n}\n\n.core-icon-wrapper {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 15rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s;\n}\n\n.bg-publish {\n  background: linear-gradient(135deg, #34C759, #30B3FF);\n}\n\n.bg-manage {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n}\n\n.bg-goods {\n  background: linear-gradient(135deg, #FF9500, #FF5E3A);\n}\n\n.bg-reviews {\n  background: linear-gradient(135deg, #5AC8FA, #34C759);\n}\n\n.core-icon {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.core-name {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 快捷工具 */\n.tools-section {\n  background-color: #fff;\n  margin: 0 30rpx 30rpx;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\n  transform: translateZ(0);\n}\n\n.title-indicator-tools {\n  background: linear-gradient(to bottom, #34C759, #30B3FF);\n}\n\n.tools-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 20rpx -10rpx 0;\n}\n\n.tool-item {\n  position: relative;\n  width: 25%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30rpx;\n  padding: 0 10rpx;\n  box-sizing: border-box;\n  transition: all 0.3s;\n}\n\n.tool-item:active {\n  transform: scale(0.95);\n}\n\n.tool-card-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 15rpx;\n  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);\n}\n\n.tool-card-icon image {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.bg-qrcode {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n}\n\n.bg-poster {\n  background: linear-gradient(135deg, #FFCC00, #FF9500);\n}\n\n.bg-analysis {\n  background: linear-gradient(135deg, #5856D6, #AF52DE);\n}\n\n.bg-staff {\n  background: linear-gradient(135deg, #34C759, #30B3FF);\n}\n\n.bg-settings {\n  background: linear-gradient(135deg, #8E8E93, #636366);\n}\n\n.tool-card-name {\n  font-size: 26rpx;\n  color: #333;\n  text-align: center;\n}\n\n.tool-card-tag {\n  position: absolute;\n  top: -8rpx;\n  right: -8rpx;\n  font-size: 20rpx;\n  color: #fff;\n  padding: 2rpx 10rpx;\n  border-radius: 16rpx;\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);\n  z-index: 1;\n}\n\n/* 运营专区 */\n.operation-section {\n  background-color: #fff;\n  margin: 0 30rpx 30rpx;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\n  transform: translateZ(0);\n}\n\n.section-title-large {\n  font-size: 34rpx;\n  font-weight: 600;\n  color: #333;\n  position: relative;\n  padding-left: 24rpx;\n}\n\n.section-title-large::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 8rpx;\n  height: 34rpx;\n  background: linear-gradient(to bottom, #007AFF, #5856D6);\n  border-radius: 4rpx;\n}\n\n.operation-cards {\n  margin-top: 30rpx;\n}\n\n.operation-card {\n  position: relative;\n  width: 100%;\n  height: 240rpx;\n  border-radius: 20rpx;\n  overflow: hidden;\n  margin-bottom: 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);\n  transform: translateZ(0);\n  transition: all 0.3s;\n}\n\n.operation-card:active {\n  transform: scale(0.98);\n}\n\n.operation-card-bg {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.operation-card-content {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 30rpx;\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));\n}\n\n.operation-card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #fff;\n  margin-bottom: 10rpx;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.operation-card-desc {\n  font-size: 26rpx;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 15rpx;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.operation-card-btn {\n  display: inline-block;\n  padding: 8rpx 24rpx;\n  border-radius: 30rpx;\n  font-size: 24rpx;\n  color: #fff;\n  background: rgba(255, 255, 255, 0.25);\n  backdrop-filter: blur(10px);\n  border: 1rpx solid rgba(255, 255, 255, 0.5);\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n  transition: all 0.3s;\n}\n\n/* 行业专属功能 */\n.industry-section {\n  background-color: #fff;\n  margin: 0 30rpx 30rpx;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\n  transform: translateZ(0);\n}\n\n.industry-features {\n  margin-top: 30rpx;\n}\n\n.industry-card {\n  position: relative;\n  width: 100%;\n  height: 240rpx;\n  border-radius: 20rpx;\n  overflow: hidden;\n  margin-bottom: 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);\n  transform: translateZ(0);\n  transition: all 0.3s;\n}\n\n.industry-card:active {\n  transform: scale(0.98);\n}\n\n.industry-card-bg {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.industry-card-content {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 30rpx;\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));\n}\n\n.industry-card-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #fff;\n  margin-bottom: 10rpx;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.industry-card-desc {\n  font-size: 26rpx;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 15rpx;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n/* 新增背景色定义 */\n.bg-publish {\n  background: linear-gradient(135deg, #34C759, #30B3FF);\n}\n\n.bg-manage {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n}\n\n.bg-visitor {\n  background: linear-gradient(135deg, #FF9500, #FFCC00);\n}\n\n.bg-leads {\n  background: linear-gradient(135deg, #5AC8FA, #34C759);\n}\n\n.bg-category {\n  background: linear-gradient(135deg, #AF52DE, #5856D6);\n}\n\n.title-indicator-marketing {\n  background: linear-gradient(to bottom, #FF9500, #FF5E3A);\n}\n\n.title-indicator-analysis {\n  background: linear-gradient(to bottom, #5856D6, #AF52DE);\n}\n\n/* 营销推广样式 */\n.marketing-section {\n  background-color: #fff;\n  margin: 0 30rpx 30rpx;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\n  transform: translateZ(0);\n}\n\n.marketing-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 20rpx -10rpx 0;\n}\n\n.marketing-item {\n  width: 25%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30rpx;\n  padding: 0 10rpx;\n  box-sizing: border-box;\n  transition: all 0.3s;\n}\n\n.marketing-item:active {\n  transform: scale(0.95);\n}\n\n.marketing-icon-wrapper {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 15rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s;\n}\n\n.marketing-icon {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.marketing-name {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n/* 经营分析样式 */\n.analysis-section {\n  background-color: #fff;\n  margin: 0 30rpx 30rpx;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\n  transform: translateZ(0);\n}\n\n.analysis-grid {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 20rpx -10rpx 0;\n}\n\n.analysis-item {\n  width: 25%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30rpx;\n  padding: 0 10rpx;\n  box-sizing: border-box;\n  transition: all 0.3s;\n}\n\n.analysis-item:active {\n  transform: scale(0.95);\n}\n\n.analysis-icon-wrapper {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 15rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\n  transition: all 0.3s;\n}\n\n.analysis-icon {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.analysis-name {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.bg-product {\n  background: linear-gradient(135deg, #FF9500, #FF5E3A);\n}\n\n.bg-marketing {\n  background: linear-gradient(135deg, #34C759, #30B3FF);\n}\n\n.bg-finance {\n  background: linear-gradient(135deg, #5856D6, #AF52DE);\n}\n\n.bg-coupon {\n  background: linear-gradient(135deg, #FF9500, #FFCC00);\n}\n\n.bg-redpacket {\n  background: linear-gradient(135deg, #FF3B30, #FF5E3A);\n}\n\n.bg-reviews {\n  background: linear-gradient(135deg, #5AC8FA, #34C759);\n}\n\n/* 待处理事项 */\n.pending-section {\n  background-color: #fff;\n  margin: 0 30rpx 30rpx;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\n  transform: translateZ(0);\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n}\n\n.section-more {\n  font-size: 26rpx;\n  color: #007AFF;\n}\n\n.pending-list {\n  background-color: #fff;\n}\n\n.pending-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f5f5f5;\n  transition: all 0.3s;\n}\n\n.pending-item:active {\n  background-color: #f8f9fa;\n  transform: scale(0.98);\n}\n\n.pending-item:last-child {\n  border-bottom: none;\n}\n\n.pending-left {\n  margin-right: 20rpx;\n}\n\n.pending-badge {\n  display: inline-block;\n  min-width: 32rpx;\n  height: 32rpx;\n  border-radius: 16rpx;\n  padding: 4rpx 12rpx;\n  font-size: 24rpx;\n  text-align: center;\n  color: #fff;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.badge-order {\n  background: linear-gradient(135deg, #FF3B30, #FF9500);\n}\n\n.badge-comment {\n  background: linear-gradient(135deg, #FF9500, #FFCC00);\n}\n\n.badge-finance {\n  background: linear-gradient(135deg, #007AFF, #5AC8FA);\n}\n\n.badge-approval {\n  background: linear-gradient(135deg, #5856D6, #AF52DE);\n}\n\n.badge-service {\n  background: linear-gradient(135deg, #34C759, #30B3FF);\n}\n\n.badge-info {\n  background: linear-gradient(135deg, #FF9500, #FFCC00);\n}\n\n.pending-center {\n  flex: 1;\n}\n\n.pending-title {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 6rpx;\n}\n\n.pending-desc {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.pending-right {\n  margin-left: 20rpx;\n}\n\n.action-btn {\n  display: inline-block;\n  padding: 6rpx 20rpx;\n  border-radius: 30rpx;\n  font-size: 24rpx;\n  color: #007AFF;\n  border: 1rpx solid #007AFF;\n  background-color: rgba(0, 122, 255, 0.05);\n  transition: all 0.3s;\n}\n\n.action-btn:active {\n  transform: scale(0.95);\n  background-color: rgba(0, 122, 255, 0.1);\n}\n\n/* 空状态 */\n.empty-view {\n  padding: 40rpx 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.empty-icon {\n  width: 160rpx;\n  height: 160rpx;\n  margin-bottom: 20rpx;\n  opacity: 0.7;\n}\n\n.empty-text {\n  margin-top: 10px;\n  font-size: 14px;\n  color: #999;\n}\n\n/* 推广模块 */\n.promotion-section {\n  margin: 20rpx 30rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 20rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);\n  border: 1px solid #F5F5F5;\n}\n\n.favorite-button {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  height: 60rpx;\n  padding: 0 20rpx;\n  background-color: #FFFFFF;\n  justify-content: flex-start;\n}\n\n.favorite-icon-wrapper {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12rpx;\n}\n\n.favorite-icon {\n  width: 36rpx;\n  height: 36rpx;\n}\n\n.favorite-text {\n  font-size: 28rpx;\n  color: #333333;\n  font-weight: 400;\n}\n\n.bg-orders {\n  background: linear-gradient(135deg, #FF9500, #FF5E3A);\n}\n\n.bg-verification {\n  background: linear-gradient(135deg, #9040FF, #6C2FF2);\n}\n\n.bg-customer {\n  background: linear-gradient(135deg, #5AC8FA, #34C759);\n}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-home/pages/merchant.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "smartNavigate"], "mappings": ";;;;AAyTA,MAAA,YAAA;AAAA,EACE,OAAA;AACE,WAAA;AAAA,MACE,iBAAA;AAAA,MACA,cAAA;AAAA;QAEE,MAAA;AAAA,QACA,MAAA;AAAA,QACA,UAAA;AAAA;;;QAIA,OAAA;AAAA,QACA,OAAA;AAAA;;MAGF,cAAA,CAAA;AAAA,MACA,kBAAA;AAAA,IACF;AAAA;EAEF,SAAA;AAEE,UAAA,UAAAA,oBAAA;AACA,SAAA,kBAAA,QAAA;AACA,SAAA,eAAA,KAAA,kBAAA;;AAIA,SAAA,cAAA;;;EAMF,oBAAA;AAEE,WAAA;AAAA;;IAGA;AAAA;EAGF,SAAA;AAAA;AAAA,IAEE,SAAA;AACEA,oBAAA,MAAA,aAAA;AAAA;;IAIF,qBAAA;AACEC,uBAAAA,cAAA,6CAAA,EAAA,MAAA,SAAA;;MAEA,CAAA;AAAA;;IAIF,WAAA,KAAA;AAEEA,uBAAAA,cAAA,GAAA,EAAA,MAAA,SAAA;AACED,sBAAA,MAAA,MAAA,SAAA,6DAAA,WAAA,GAAA;AAAA,MACF,CAAA;AAAA;;IAIF,mBAAA;;;;;;;;;;;;;;;AAkBE,UAAA,OAAA,KAAA,IAAA,GAAA;AACE,aAAA,WAAA,OAAA,KAAA,IAAA,CAAA;AAAA,MACF;AAAA;;IAIF,mBAAA;AAEE,iBAAA,MAAA;AAEE,cAAA,WAAA,KAAA,OAAA,IAAA;AAEA,aAAA,eAAA;AAAA;;UAGE;AAAA,UACA,YAAA,MAAA,KAAA,MAAA,KAAA,OAAA,IAAA,GAAA,EAAA,SAAA,EAAA,SAAA,GAAA,GAAA;AAAA;MAEJ,GAAA,GAAA;AAAA;;IAIF,gBAAA;AAEE,iBAAA,MAAA;AACE,aAAA,YAAA;AAAA,UACE,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,GAAA;AAAA,UACA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,GAAA;AAAA,UACA,SAAA,KAAA,OAAA,IAAA,KAAA,QAAA,CAAA;AAAA;MAEJ,GAAA,GAAA;AAAA;;IAIF,mBAAA;AAEE,iBAAA,MAAA;;AAIE,YAAA,kBAAA;AACE,eAAA,eAAA;AAAA,YACE;AAAA,cACE,MAAA;AAAA,cACA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,EAAA,IAAA;AAAA;cAEA,MAAA;AAAA;;YAGF;AAAA,cACE,MAAA;AAAA,cACA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,IAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA;;YAGF;AAAA,cACE,MAAA;AAAA,cACA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,IAAA;AAAA;cAEA,MAAA;AAAA;;YAGF;AAAA;cAEE,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,CAAA,IAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA;YAEF;AAAA;AAIF,gBAAA,QAAA,KAAA,MAAA,KAAA,WAAA,CAAA,IAAA;;;AAGA,eAAA,eAAA;QACF;AAAA,MACF,GAAA,GAAA;AAAA;;;AAKA,YAAA,iBAAA;AAAA;QAEE,SAAA;AAAA,QACA,cAAA;AAAA,QACA,WAAA;AAAA;AAEF,aAAA,eAAA,QAAA,KAAA;AAAA;;;AAMAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA;AAAA;;AAGEA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA;UAEF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;EAIJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvfA,GAAG,WAAW,eAAe;"}