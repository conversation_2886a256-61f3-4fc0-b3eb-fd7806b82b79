"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const utils_promotionService = require("../../../utils/promotionService.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  (_component_path + _component_svg + _component_circle + _component_line)();
}
const defaultPosterPath = "/static/images/default-poster.png";
const _sfc_main = {
  __name: "promotion-tool",
  setup(__props) {
    const contentType = common_vendor.ref("");
    const contentId = common_vendor.ref("");
    const previewData = common_vendor.reactive({
      type: "",
      title: "",
      image: "",
      description: "",
      // 拼车特有字段
      departure: "",
      destination: "",
      departureTime: "",
      // 商品特有字段
      price: 0,
      originalPrice: 0,
      // 房产特有字段
      roomType: "",
      area: 0,
      priceUnit: "",
      // 商家特有字段
      category: "",
      address: ""
    });
    const hasCommission = common_vendor.ref(false);
    const commissionRate = common_vendor.ref(0);
    const estimatedCommission = common_vendor.ref("0.00");
    common_vendor.ref(false);
    const posterPath = common_vendor.ref("");
    const userId = common_vendor.ref("");
    const posterTemplates = common_vendor.ref([
      {
        name: "商品推广",
        thumb: "/static/images/distribution/poster-thumb-1.png",
        url: "/static/images/distribution/poster-1.png",
        type: "product"
      },
      {
        name: "店铺推广",
        thumb: "/static/images/distribution/poster-thumb-2.png",
        url: "/static/images/distribution/poster-2.png",
        type: "store"
      },
      {
        name: "活动推广",
        thumb: "/static/images/distribution/poster-thumb-3.png",
        url: "/static/images/distribution/poster-3.png",
        type: "activity"
      },
      {
        name: "会员推广",
        thumb: "/static/images/distribution/poster-thumb-4.png",
        url: "/static/images/distribution/poster-4.png",
        type: "member"
      },
      {
        name: "节日主题",
        thumb: "/static/images/distribution/poster-thumb-5.png",
        url: "/static/images/distribution/poster-5.png",
        type: "general"
      }
    ]);
    const typeTemplates = {
      "carpool": [
        {
          name: "拼车专用",
          thumb: "/static/images/distribution/carpool-thumb-1.png",
          url: "/static/images/distribution/carpool-1.png",
          type: "carpool"
        }
      ],
      "secondhand": [
        {
          name: "二手商品专用",
          thumb: "/static/images/distribution/secondhand-thumb-1.png",
          url: "/static/images/distribution/secondhand-1.png",
          type: "secondhand"
        }
      ],
      "house": [
        {
          name: "房屋租售专用",
          thumb: "/static/images/distribution/house-thumb-1.png",
          url: "/static/images/distribution/house-1.png",
          type: "house"
        }
      ]
    };
    const themeColors = [
      { bgColor: "#FFFFFF", textColor: "#333333" },
      { bgColor: "#3846CD", textColor: "#FFFFFF" },
      { bgColor: "#2C3AA0", textColor: "#FFFFFF" },
      { bgColor: "#F5F7FA", textColor: "#333333" },
      { bgColor: "#FFE8F0", textColor: "#FF3B30" },
      { bgColor: "#E8F8FF", textColor: "#007AFF" }
    ];
    const currentPosterIndex = common_vendor.ref(0);
    const selectedTheme = common_vendor.ref(themeColors[0]);
    const showId = common_vendor.ref(true);
    const showPromoText = common_vendor.ref(false);
    const customText = common_vendor.ref("");
    const currentPoster = common_vendor.computed(() => {
      if (posterTemplates.value.length === 0)
        return "";
      return posterTemplates.value[currentPosterIndex.value].url;
    });
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      contentType.value = options.type || "";
      contentId.value = options.id || "";
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo && userInfo.userId) {
        userId.value = userInfo.userId;
      }
      loadContentData();
      loadPosterTemplates();
      setTimeout(() => {
        generatePoster();
      }, 1500);
    });
    const loadPosterTemplates = () => {
      const typeSpecificTemplates = typeTemplates[contentType.value] || [];
      posterTemplates.value = [
        ...typeSpecificTemplates,
        ...posterTemplates.value.filter(
          (template) => template.type === "general" || template.type === contentType.value
        )
      ];
    };
    const selectPoster = (index) => {
      currentPosterIndex.value = index;
      setTimeout(() => {
        generatePoster();
      }, 100);
    };
    const selectTheme = (theme) => {
      selectedTheme.value = theme;
      setTimeout(() => {
        generatePoster();
      }, 100);
    };
    const toggleId = (e) => {
      showId.value = e.detail.value;
      setTimeout(() => {
        generatePoster();
      }, 100);
    };
    const togglePromoText = (e) => {
      showPromoText.value = e.detail.value;
      if (showPromoText.value && !customText.value) {
        generateDefaultText();
      }
      setTimeout(() => {
        generatePoster();
      }, 100);
    };
    const onCustomTextChange = () => {
      if (window.customTextTimer) {
        clearTimeout(window.customTextTimer);
      }
      window.customTextTimer = setTimeout(() => {
        generatePoster();
      }, 500);
    };
    const generateDefaultText = () => {
      switch (contentType.value) {
        case "carpool":
          customText.value = `【${previewData.title}】${previewData.departure}→${previewData.destination}，出发时间：${previewData.departureTime}`;
          break;
        case "product":
          customText.value = `【${previewData.title}】价格：¥${previewData.price}`;
          break;
        case "house":
          customText.value = `【${previewData.title}】${previewData.roomType}，${previewData.area}㎡，${previewData.price}${previewData.priceUnit}`;
          break;
        default:
          customText.value = `【${previewData.title}】`;
      }
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "推广海报使用帮助",
        content: "选择喜欢的海报模板，自定义颜色和文案，生成精美海报进行分享推广。",
        showCancel: false
      });
    };
    const loadContentData = () => {
      if (!contentType.value || !contentId.value) {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "none"
        });
        setTimeout(() => {
          goBack();
        }, 1500);
        return;
      }
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      switch (contentType.value) {
        case "carpool":
          loadCarpoolData();
          break;
        case "product":
          loadProductData();
          break;
        case "merchant":
          loadMerchantData();
          break;
        case "house":
          loadHouseData();
          break;
        case "service":
          loadServiceData();
          break;
        case "community":
          loadCommunityData();
          break;
        case "activity":
          loadActivityData();
          break;
        case "content":
          loadContentPageData();
          break;
        default:
          loadGenericData();
      }
    };
    const loadCarpoolData = () => {
      setTimeout(() => {
        previewData.type = "carpool";
        previewData.title = "磁县到邯郸拼车";
        previewData.image = "/static/images/carpool-default.jpg";
        previewData.departure = "磁县城区";
        previewData.destination = "邯郸火车站";
        previewData.departureTime = "2023-07-20 08:00";
        previewData.price = 15;
        hasCommission.value = false;
        commissionRate.value = 0;
        estimatedCommission.value = "0.00";
        common_vendor.index.hideLoading();
      }, 500);
    };
    const loadProductData = () => {
      setTimeout(() => {
        previewData.type = "product";
        previewData.title = "有机新鲜蔬菜礼盒";
        previewData.image = "/static/images/product-default.jpg";
        previewData.price = 99.8;
        previewData.originalPrice = 128;
        previewData.description = "本地新鲜蔬菜，无农药，当天采摘";
        hasCommission.value = true;
        commissionRate.value = 10;
        estimatedCommission.value = (previewData.price * commissionRate.value / 100).toFixed(2);
        common_vendor.index.hideLoading();
      }, 500);
    };
    const loadMerchantData = () => {
      setTimeout(() => {
        previewData.type = "merchant";
        previewData.title = "磁州同城生活馆";
        previewData.image = "/static/images/shop-default.jpg";
        previewData.category = "生活服务";
        previewData.address = "磁县城区中心广场东侧100米";
        previewData.description = "提供本地生活服务、特产销售等";
        hasCommission.value = true;
        commissionRate.value = 15;
        estimatedCommission.value = "5.00";
        common_vendor.index.hideLoading();
      }, 500);
    };
    const loadHouseData = () => {
      setTimeout(() => {
        previewData.type = "house";
        previewData.title = "城区精装两居室出租";
        previewData.image = "/static/images/house-default.jpg";
        previewData.roomType = "2室1厅1卫";
        previewData.area = 89;
        previewData.price = 1200;
        previewData.priceUnit = "/月";
        previewData.description = "城区中心位置，交通便利，拎包入住";
        hasCommission.value = true;
        commissionRate.value = 30;
        estimatedCommission.value = "360.00";
        common_vendor.index.hideLoading();
      }, 500);
    };
    const loadServiceData = () => {
      setTimeout(() => {
        previewData.type = "service";
        previewData.title = "专业家电维修";
        previewData.image = "/static/images/service-default.jpg";
        previewData.price = 50;
        previewData.description = "各类家电维修，上门服务，快速响应";
        hasCommission.value = true;
        commissionRate.value = 20;
        estimatedCommission.value = "10.00";
        common_vendor.index.hideLoading();
      }, 500);
    };
    const loadCommunityData = () => {
      setTimeout(() => {
        previewData.type = "community";
        previewData.title = "社区便民信息";
        previewData.image = "/static/images/community-default.jpg";
        previewData.description = "社区便民服务信息，包括水电维修、家政服务等";
        hasCommission.value = false;
        common_vendor.index.hideLoading();
      }, 500);
    };
    const loadActivityData = () => {
      setTimeout(() => {
        previewData.type = "activity";
        previewData.title = "周末农产品展销会";
        previewData.image = "/static/images/activity-default.jpg";
        previewData.description = "本周末在城区广场举办农产品展销会，欢迎参加";
        hasCommission.value = true;
        commissionRate.value = 5;
        estimatedCommission.value = "2.50";
        common_vendor.index.hideLoading();
      }, 500);
    };
    const loadContentPageData = () => {
      setTimeout(() => {
        previewData.type = "content";
        previewData.title = "磁州特产推荐";
        previewData.image = "/static/images/content-default.jpg";
        previewData.description = "磁州地区特色农产品与手工艺品推荐";
        hasCommission.value = false;
        common_vendor.index.hideLoading();
      }, 500);
    };
    const loadGenericData = () => {
      setTimeout(() => {
        previewData.type = contentType.value;
        previewData.title = "磁州生活网内容";
        previewData.image = "/static/images/default.jpg";
        previewData.description = "磁州生活网为您提供本地生活服务";
        hasCommission.value = false;
        common_vendor.index.hideLoading();
      }, 500);
    };
    const shareToWechat = () => {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        title: previewData.title,
        summary: customText.value || previewData.description || "分享来自磁州生活网",
        imageUrl: posterPath.value || previewData.image,
        href: utils_promotionService.promotionService.generateShareLink(contentType.value, contentId.value, userId.value),
        success: function() {
          common_vendor.index.__f__("log", "at subPackages/promotion/pages/promotion-tool.vue:640", "分享成功");
          utils_promotionService.promotionService.recordPromotion(contentType.value, contentId.value, "wechat");
        },
        fail: function() {
          common_vendor.index.__f__("log", "at subPackages/promotion/pages/promotion-tool.vue:645", "分享失败");
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
    };
    const generatePoster = async () => {
      try {
        posterPath.value = "";
        common_vendor.index.showLoading({
          title: "生成中..."
        });
        posterPath.value = await utils_promotionService.promotionService.generatePoster(
          contentType.value,
          previewData,
          userId.value,
          {
            template: currentPoster.value,
            theme: selectedTheme.value,
            showId: showId.value,
            customText: showPromoText.value ? customText.value : ""
          }
        );
        common_vendor.index.hideLoading();
      } catch (err) {
        common_vendor.index.__f__("error", "at subPackages/promotion/pages/promotion-tool.vue:679", "生成海报失败", err);
        common_vendor.index.hideLoading();
        posterPath.value = previewData.image || defaultPosterPath;
        common_vendor.index.showToast({
          title: "生成海报失败",
          icon: "none"
        });
      }
    };
    const savePoster = async () => {
      if (!posterPath.value) {
        await generatePoster();
      }
      if (posterPath.value) {
        try {
          common_vendor.index.showLoading({
            title: "保存中..."
          });
          await utils_promotionService.promotionService.savePosterToAlbum(posterPath.value);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        } catch (err) {
          common_vendor.index.__f__("error", "at subPackages/promotion/pages/promotion-tool.vue:713", "保存海报失败", err);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "none"
          });
        }
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: posterPath.value || previewData.image || defaultPosterPath,
        d: selectedTheme.value.textColor,
        e: selectedTheme.value.bgColor,
        f: common_vendor.p({
          d: "M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        g: common_vendor.p({
          d: "M7 10L12 15L17 10",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        h: common_vendor.p({
          d: "M12 15V3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        i: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        j: common_vendor.o(savePoster),
        k: common_vendor.p({
          cx: "18",
          cy: "5",
          r: "3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        l: common_vendor.p({
          cx: "6",
          cy: "12",
          r: "3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        m: common_vendor.p({
          cx: "18",
          cy: "19",
          r: "3",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        n: common_vendor.p({
          x1: "8.59",
          y1: "13.51",
          x2: "15.42",
          y2: "17.49",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.p({
          x1: "15.41",
          y1: "6.51",
          x2: "8.59",
          y2: "10.49",
          stroke: "white",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          width: "16",
          height: "16",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }),
        q: common_vendor.o(shareToWechat),
        r: common_vendor.t(previewData.title || "加载中..."),
        s: previewData.type === "carpool"
      }, previewData.type === "carpool" ? {
        t: common_vendor.t(previewData.departure),
        v: common_vendor.t(previewData.destination),
        w: common_vendor.t(previewData.departureTime)
      } : previewData.type === "product" ? common_vendor.e({
        y: common_vendor.t(previewData.price),
        z: previewData.originalPrice
      }, previewData.originalPrice ? {
        A: common_vendor.t(previewData.originalPrice)
      } : {}) : previewData.type === "house" ? {
        C: common_vendor.t(previewData.roomType),
        D: common_vendor.t(previewData.area),
        E: common_vendor.t(previewData.price),
        F: common_vendor.t(previewData.priceUnit)
      } : previewData.type === "merchant" ? {
        H: common_vendor.t(previewData.category),
        I: common_vendor.t(previewData.address)
      } : {
        J: common_vendor.t(previewData.description)
      }, {
        x: previewData.type === "product",
        B: previewData.type === "house",
        G: previewData.type === "merchant",
        K: hasCommission.value
      }, hasCommission.value ? {
        L: common_assets._imports_0$49,
        M: common_vendor.t(commissionRate.value),
        N: common_vendor.t(estimatedCommission.value)
      } : {}, {
        O: common_vendor.f(posterTemplates.value, (poster, index, i0) => {
          return {
            a: poster.thumb,
            b: index,
            c: currentPosterIndex.value === index ? 1 : "",
            d: common_vendor.o(($event) => selectPoster(index), index)
          };
        }),
        P: common_vendor.f(themeColors, (theme, index, i0) => {
          return common_vendor.e({
            a: selectedTheme.value === theme
          }, selectedTheme.value === theme ? {
            b: "7b3d7571-11-" + i0 + "," + ("7b3d7571-10-" + i0),
            c: common_vendor.p({
              d: "M20 6L9 17L4 12",
              stroke: "white",
              ["stroke-width"]: "2",
              ["stroke-linecap"]: "round",
              ["stroke-linejoin"]: "round"
            }),
            d: "7b3d7571-10-" + i0,
            e: common_vendor.p({
              width: "16",
              height: "16",
              viewBox: "0 0 24 24",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg"
            })
          } : {}, {
            f: index,
            g: selectedTheme.value === theme ? 1 : "",
            h: theme.bgColor,
            i: common_vendor.o(($event) => selectTheme(theme), index)
          });
        }),
        Q: showId.value,
        R: common_vendor.o(toggleId),
        S: showPromoText.value,
        T: common_vendor.o(togglePromoText),
        U: showPromoText.value
      }, showPromoText.value ? {
        V: common_vendor.o([($event) => customText.value = $event.detail.value, onCustomTextChange]),
        W: customText.value
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7b3d7571"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subPackages/promotion/pages/promotion-tool.js.map
