/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-c16fb8e4, html.data-v-c16fb8e4, #app.data-v-c16fb8e4, .index-container.data-v-c16fb8e4 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.team-container.data-v-c16fb8e4 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(180deg, #F2F2F7 0%, #E8E8ED 100%);
}
.custom-navbar.data-v-c16fb8e4 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-c16fb8e4 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(172, 57, 255, 0.15);
}
.custom-navbar .navbar-content.data-v-c16fb8e4 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 30rpx;
  padding-top: var(--status-bar-height, 25px);
  box-sizing: border-box;
}
.custom-navbar .navbar-title.data-v-c16fb8e4 {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
}
.custom-navbar .back-btn.data-v-c16fb8e4, .custom-navbar .invite-btn.data-v-c16fb8e4 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}
.custom-navbar .back-btn .icon.data-v-c16fb8e4, .custom-navbar .invite-btn .icon.data-v-c16fb8e4 {
  width: 48rpx;
  height: 48rpx;
}
.custom-navbar .navbar-right.data-v-c16fb8e4 {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}
.content.data-v-c16fb8e4 {
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
  padding: 30rpx;
  flex: 1;
}
.stats-card .stats-grid.data-v-c16fb8e4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.stats-card .stats-grid .stat-item.data-v-c16fb8e4 {
  text-align: center;
}
.stats-card .stats-grid .stat-item .stat-number.data-v-c16fb8e4 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 8rpx;
}
.stats-card .stats-grid .stat-item .stat-label.data-v-c16fb8e4 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.filter-tabs .tabs-scroll.data-v-c16fb8e4 {
  width: 100%;
}
.filter-tabs .tabs-scroll .tabs-container.data-v-c16fb8e4 {
  display: flex;
  white-space: nowrap;
}
.filter-tabs .tabs-scroll .tabs-container .tab-item.data-v-c16fb8e4 {
  position: relative;
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
}
.filter-tabs .tabs-scroll .tabs-container .tab-item text.data-v-c16fb8e4 {
  font-size: 28rpx;
  color: #8E8E93;
  transition: color 0.3s ease;
}
.filter-tabs .tabs-scroll .tabs-container .tab-item .tab-indicator.data-v-c16fb8e4 {
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  border-radius: 2rpx;
  background: linear-gradient(90deg, #AC39FF 0%, #B87AFF 100%);
}
.filter-tabs .tabs-scroll .tabs-container .tab-item.active text.data-v-c16fb8e4 {
  color: #AC39FF;
  font-weight: 500;
}
.member-card .member-header.data-v-c16fb8e4 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.member-card .member-header .member-avatar-section.data-v-c16fb8e4 {
  position: relative;
  margin-right: 20rpx;
}
.member-card .member-header .member-avatar-section .member-avatar.data-v-c16fb8e4 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid #F0F0F0;
}
.member-card .member-header .member-avatar-section .online-status.data-v-c16fb8e4 {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 20rpx;
  height: 20rpx;
  background: #34C759;
  border-radius: 50%;
  border: 3rpx solid #FFFFFF;
}
.member-card .member-header .member-basic-info.data-v-c16fb8e4 {
  flex: 1;
}
.member-card .member-header .member-basic-info .member-name-level.data-v-c16fb8e4 {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.member-card .member-header .member-basic-info .member-name-level .member-name.data-v-c16fb8e4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}
.member-card .member-header .member-basic-info .member-meta.data-v-c16fb8e4 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.member-card .member-header .member-basic-info .member-meta .join-time.data-v-c16fb8e4, .member-card .member-header .member-basic-info .member-meta .member-id.data-v-c16fb8e4 {
  font-size: 24rpx;
  color: #8E8E93;
}
.member-card .member-header .member-actions .arrow-icon.data-v-c16fb8e4 {
  width: 32rpx;
  height: 32rpx;
}
.member-card .member-stats .stat-row.data-v-c16fb8e4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 20rpx 0;
  border-top: 1rpx solid #F0F0F0;
  border-bottom: 1rpx solid #F0F0F0;
  margin-bottom: 20rpx;
}
.member-card .member-stats .stat-row .stat-item.data-v-c16fb8e4 {
  text-align: center;
}
.member-card .member-stats .stat-row .stat-item .stat-value.data-v-c16fb8e4 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #AC39FF;
  margin-bottom: 8rpx;
}
.member-card .member-stats .stat-row .stat-item .stat-label.data-v-c16fb8e4 {
  font-size: 22rpx;
  color: #8E8E93;
}
.member-card .member-actions-bar.data-v-c16fb8e4 {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}
.member-card .member-actions-bar .action-btn.data-v-c16fb8e4 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
}
.member-card .member-actions-bar .action-btn .btn-icon.data-v-c16fb8e4 {
  width: 32rpx;
  height: 32rpx;
}
.member-card .member-actions-bar .action-btn text.data-v-c16fb8e4 {
  font-size: 26rpx;
  font-weight: 500;
}
.member-card .member-actions-bar .action-btn.primary.data-v-c16fb8e4 {
  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
}
.member-card .member-actions-bar .action-btn.primary text.data-v-c16fb8e4 {
  color: #FFFFFF;
}
.member-card .member-actions-bar .action-btn.secondary.data-v-c16fb8e4 {
  background: transparent;
  border: 2rpx solid #AC39FF;
}
.member-card .member-actions-bar .action-btn.secondary text.data-v-c16fb8e4 {
  color: #AC39FF;
}
.empty-state .empty-content .empty-icon.data-v-c16fb8e4 {
  margin-bottom: 30rpx;
}
.empty-state .empty-content .empty-text.data-v-c16fb8e4 {
  display: block;
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 20rpx;
}
.empty-state .empty-content .empty-action text.data-v-c16fb8e4 {
  font-size: 28rpx;
  font-weight: 500;
}
.invite-modal.data-v-c16fb8e4 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.invite-modal .modal-content.data-v-c16fb8e4 {
  background: #FFFFFF;
  border-radius: 35px;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
}
.invite-modal .modal-content .modal-header.data-v-c16fb8e4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}
.invite-modal .modal-content .modal-header .modal-title.data-v-c16fb8e4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.invite-modal .modal-content .modal-header .close-btn.data-v-c16fb8e4 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.invite-modal .modal-content .qrcode-section.data-v-c16fb8e4 {
  text-align: center;
  margin-bottom: 40rpx;
}
.invite-modal .modal-content .qrcode-section .qrcode-image.data-v-c16fb8e4 {
  width: 400rpx;
  height: 400rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}
.invite-modal .modal-content .qrcode-section .qrcode-tip.data-v-c16fb8e4 {
  font-size: 26rpx;
  color: #8E8E93;
}
.invite-modal .modal-content .share-actions.data-v-c16fb8e4 {
  display: flex;
  gap: 20rpx;
}
.invite-modal .modal-content .share-actions .share-btn.data-v-c16fb8e4 {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #AC39FF 0%, #B87AFF 100%);
  border-radius: 30rpx;
}
.invite-modal .modal-content .share-actions .share-btn text.data-v-c16fb8e4 {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
}
.safe-area-bottom.data-v-c16fb8e4 {
  height: env(safe-area-inset-bottom);
}