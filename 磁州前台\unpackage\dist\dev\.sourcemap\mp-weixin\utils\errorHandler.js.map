{"version": 3, "file": "errorHandler.js", "sources": ["utils/errorHandler.js"], "sourcesContent": ["/**\r\n * 错误处理工具\r\n * 统一处理项目中的各类错误，包括：\r\n * - 网络请求错误\r\n * - 业务逻辑错误\r\n * - 运行时错误\r\n */\r\n\r\n// 错误类型\r\nexport const ErrorType = {\r\n  NETWORK: 'network', // 网络错误\r\n  API: 'api', // API错误\r\n  AUTH: 'auth', // 认证错误\r\n  PERMISSION: 'permission', // 权限错误\r\n  VALIDATION: 'validation', // 数据验证错误\r\n  BUSINESS: 'business', // 业务逻辑错误\r\n  UNKNOWN: 'unknown' // 未知错误\r\n}\r\n\r\n// HTTP状态码错误映射\r\nconst HTTP_STATUS_MAP = {\r\n  400: { type: ErrorType.VALIDATION, message: '请求参数错误' },\r\n  401: { type: ErrorType.AUTH, message: '未授权，请重新登录' },\r\n  403: { type: ErrorType.PERMISSION, message: '权限不足，无法访问' },\r\n  404: { type: ErrorType.API, message: '请求的资源不存在' },\r\n  500: { type: ErrorType.API, message: '服务器内部错误' },\r\n  502: { type: ErrorType.NETWORK, message: '网关错误' },\r\n  503: { type: ErrorType.NETWORK, message: '服务不可用' },\r\n  504: { type: ErrorType.NETWORK, message: '网关超时' }\r\n}\r\n\r\n// 网络异常错误码映射\r\nconst NETWORK_ERROR_MAP = {\r\n  'ECONNABORTED': { type: ErrorType.NETWORK, message: '请求超时，请检查网络' },\r\n  'ECONNREFUSED': { type: ErrorType.NETWORK, message: '服务器拒绝连接' },\r\n  'ENOTFOUND': { type: ErrorType.NETWORK, message: '无法连接到服务器' }\r\n}\r\n\r\n// 创建标准错误对象\r\nexport const createError = (error, defaultMessage = '未知错误') => {\r\n  // 已经是标准错误对象格式的直接返回\r\n  if (error && error.type && error.message) {\r\n    return error\r\n  }\r\n\r\n  // HTTP 响应错误\r\n  if (error && error.response) {\r\n    const { statusCode, data } = error.response\r\n    const statusError = HTTP_STATUS_MAP[statusCode]\r\n    \r\n    if (statusError) {\r\n      return {\r\n        type: statusError.type,\r\n        message: data?.message || statusError.message,\r\n        statusCode,\r\n        data: data,\r\n        originalError: error\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 网络错误\r\n  if (error && error.errMsg) {\r\n    // uni-app 网络请求错误\r\n    const errMsg = error.errMsg\r\n    \r\n    if (errMsg.includes('timeout')) {\r\n      return {\r\n        type: ErrorType.NETWORK,\r\n        message: '请求超时，请检查网络',\r\n        originalError: error\r\n      }\r\n    }\r\n    \r\n    if (errMsg.includes('fail')) {\r\n      return {\r\n        type: ErrorType.NETWORK,\r\n        message: '网络连接失败',\r\n        originalError: error\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 处理 Node.js 类网络错误\r\n  if (error && error.code && NETWORK_ERROR_MAP[error.code]) {\r\n    const networkError = NETWORK_ERROR_MAP[error.code]\r\n    return {\r\n      type: networkError.type,\r\n      message: networkError.message,\r\n      originalError: error\r\n    }\r\n  }\r\n  \r\n  // 业务逻辑错误\r\n  if (error && error.code !== undefined && error.message) {\r\n    return {\r\n      type: ErrorType.BUSINESS,\r\n      message: error.message,\r\n      code: error.code,\r\n      originalError: error\r\n    }\r\n  }\r\n  \r\n  // 默认未知错误\r\n  return {\r\n    type: ErrorType.UNKNOWN,\r\n    message: error?.message || defaultMessage,\r\n    originalError: error\r\n  }\r\n}\r\n\r\n// 处理错误并显示提示\r\nexport const handleError = (error, options = {}) => {\r\n  const {\r\n    showToast = true,\r\n    autoLogin = true,\r\n    silent = false,\r\n    onAuthError,\r\n    onBusinessError,\r\n    onNetworkError\r\n  } = options\r\n  \r\n  // 转换为标准错误对象\r\n  const standardError = createError(error)\r\n  \r\n  // 记录错误日志\r\n  console.error('[错误处理]', standardError)\r\n  \r\n  // 如果是静默处理，只记录不提示\r\n  if (silent) return standardError\r\n  \r\n  // 特殊错误处理\r\n  switch (standardError.type) {\r\n    case ErrorType.AUTH:\r\n      // 身份验证错误，可以触发重新登录\r\n      if (autoLogin && onAuthError) {\r\n        onAuthError(standardError)\r\n      } else if (showToast) {\r\n        uni.showToast({\r\n          title: standardError.message,\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n      }\r\n      break\r\n      \r\n    case ErrorType.NETWORK:\r\n      // 网络错误处理\r\n      if (onNetworkError) {\r\n        onNetworkError(standardError)\r\n      } else if (showToast) {\r\n        uni.showToast({\r\n          title: standardError.message,\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n      }\r\n      break\r\n      \r\n    case ErrorType.BUSINESS:\r\n      // 业务逻辑错误处理\r\n      if (onBusinessError) {\r\n        onBusinessError(standardError)\r\n      } else if (showToast) {\r\n        uni.showToast({\r\n          title: standardError.message,\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n      }\r\n      break\r\n      \r\n    default:\r\n      // 默认错误提示\r\n      if (showToast) {\r\n        uni.showToast({\r\n          title: standardError.message,\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n      }\r\n  }\r\n  \r\n  return standardError\r\n}\r\n\r\n// 业务错误类\r\nexport class BusinessError extends Error {\r\n  constructor(message, code) {\r\n    super(message)\r\n    this.name = 'BusinessError'\r\n    this.code = code\r\n  }\r\n}\r\n\r\n// 捕获全局未处理的Promise错误\r\nexport const setupGlobalErrorHandlers = () => {\r\n  // 全局Promise拒绝处理\r\n  // 注意：小程序环境可能不支持，需要视情况使用\r\n  if (typeof window !== 'undefined' && window.addEventListener) {\r\n    window.addEventListener('unhandledrejection', (event) => {\r\n      console.error('[全局未处理Promise错误]', event.reason)\r\n      // 阻止默认处理\r\n      event.preventDefault()\r\n    })\r\n  }\r\n  \r\n  // 业务日志上报\r\n  const reportError = (error) => {\r\n    // 在这里实现错误上报逻辑\r\n    // 如发送到服务器或第三方监控平台\r\n    console.warn('[错误上报]', error)\r\n    \r\n    // TODO: 实现实际的错误上报逻辑\r\n    // 示例：\r\n    // uni.request({\r\n    //   url: 'https://api.example.com/log/error',\r\n    //   method: 'POST',\r\n    //   data: { error }\r\n    // })\r\n  }\r\n  \r\n  // 返回上报函数供手动调用\r\n  return {\r\n    reportError\r\n  }\r\n}\r\n\r\n// 页面错误处理 Mixin\r\nexport const errorHandlerMixin = {\r\n  onLoad() {\r\n    this.$errorHandler = handleError\r\n  },\r\n  methods: {\r\n    // 包装API调用\r\n    async callApi(apiPromise, options = {}) {\r\n      try {\r\n        return await apiPromise\r\n      } catch (error) {\r\n        // 处理错误并返回错误对象\r\n        const handledError = handleError(error, options)\r\n        \r\n        // 如果需要抛出错误继续传递，则重新抛出\r\n        if (options.rethrow) {\r\n          throw handledError\r\n        }\r\n        \r\n        // 返回空值或指定的默认值\r\n        return options.defaultValue || null\r\n      }\r\n    }\r\n  }\r\n} "], "names": ["uni"], "mappings": ";;AAoMY,MAAC,2BAA2B,MAAM;AAG5C,MAAI,OAAO,WAAW,eAAe,OAAO,kBAAkB;AAC5D,WAAO,iBAAiB,sBAAsB,CAAC,UAAU;AACvDA,yEAAc,oBAAoB,MAAM,MAAM;AAE9C,YAAM,eAAgB;AAAA,IAC5B,CAAK;AAAA,EACF;AAGD,QAAM,cAAc,CAAC,UAAU;AAG7BA,kBAAAA,MAAA,MAAA,QAAA,gCAAa,UAAU,KAAK;AAAA,EAS7B;AAGD,SAAO;AAAA,IACL;AAAA,EACD;AACH;;"}