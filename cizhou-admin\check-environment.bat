@echo off
chcp 65001 >nul

echo.
echo ========================================
echo   磁州生活网后台管理系统环境检查
echo ========================================
echo.

set "missing_tools="
set "all_good=true"

echo 正在检查必要工具...
echo.

REM 检查Node.js
echo [1/4] 检查 Node.js...
where node >nul 2>&1
if errorlevel 1 (
    echo    X Node.js 未安装
    set "missing_tools=%missing_tools% Node.js"
    set "all_good=false"
) else (
    node --version >nul 2>&1
    if errorlevel 1 (
        echo    X Node.js 命令执行失败
        set "missing_tools=%missing_tools% Node.js"
        set "all_good=false"
    ) else (
        echo    √ Node.js 已安装
        node --version
    )
)

REM 检查npm
echo [2/4] 检查 npm...
where npm >nul 2>&1
if errorlevel 1 (
    echo    X npm 未安装
    set "missing_tools=%missing_tools% npm"
    set "all_good=false"
) else (
    npm --version >nul 2>&1
    if errorlevel 1 (
        echo    X npm 命令执行失败
        set "missing_tools=%missing_tools% npm"
        set "all_good=false"
    ) else (
        echo    √ npm 已安装
        npm --version
    )
)

REM 检查Java
echo [3/4] 检查 Java...
where java >nul 2>&1
if errorlevel 1 (
    echo    X Java 未安装
    set "missing_tools=%missing_tools% Java"
    set "all_good=false"
) else (
    java -version >nul 2>&1
    if errorlevel 1 (
        echo    X Java 命令执行失败
        set "missing_tools=%missing_tools% Java"
        set "all_good=false"
    ) else (
        echo    √ Java 已安装
        java -version 2>&1 | findstr "version"
    )
)

REM 检查Docker（可选）
echo [4/5] 检查 Docker (可选)...
where docker >nul 2>&1
if errorlevel 1 (
    echo    - Docker 未安装 (可使用其他方案)
) else (
    docker --version >nul 2>&1
    if errorlevel 1 (
        echo    - Docker 命令执行失败
    ) else (
        echo    √ Docker 已安装
        docker --version

        REM 检查Docker是否运行
        echo      检查 Docker 运行状态...
        docker info >nul 2>&1
        if errorlevel 1 (
            echo      - Docker 已安装但未运行
        ) else (
            echo      √ Docker 运行正常
        )
    )
)

REM 检查本地数据库服务
echo [5/5] 检查本地服务 (无Docker方案)...
sc query mysql >nul 2>&1
if errorlevel 1 (
    echo    - MySQL 服务未安装
) else (
    echo    √ MySQL 服务已安装
)

sc query redis >nul 2>&1
if errorlevel 1 (
    echo    - Redis 服务未安装
) else (
    echo    √ Redis 服务已安装
)

echo.
echo ========================================

if "%all_good%"=="true" (
    echo.
    echo 环境检查通过！所有必要工具都已安装并可用。
    echo.
    echo 您可以选择以下启动方案：
    echo    start-all.bat              - Docker方案 (需要Docker)
    echo    start-without-docker.bat   - 本地服务方案
    echo    start-with-cloud.bat       - 云服务方案
    echo    start-frontend-only.bat    - 纯前端演示
    echo.
) else (
    echo.
    echo 环境检查失败！缺少以下工具：%missing_tools%
    echo.
    echo 请按照以下步骤安装缺少的工具：
    echo.
    
    echo %missing_tools% | findstr "Node.js" >nul
    if not errorlevel 1 (
        echo 🟢 安装 Node.js:
        echo    1. 访问 https://nodejs.org/
        echo    2. 下载 LTS 版本 ^(推荐 18.x 或更高版本^)
        echo    3. 运行安装程序，按默认设置安装
        echo    4. 重启命令提示符
        echo.
    )
    
    echo %missing_tools% | findstr "Java" >nul
    if not errorlevel 1 (
        echo ☕ 安装 Java:
        echo    1. 访问 https://adoptium.net/
        echo    2. 下载 JDK 17 或更高版本
        echo    3. 运行安装程序
        echo    4. 配置 JAVA_HOME 环境变量
        echo    5. 重启命令提示符
        echo.
    )
    
    echo %missing_tools% | findstr "Docker" >nul
    if not errorlevel 1 (
        echo 🐳 安装 Docker:
        echo    1. 访问 https://www.docker.com/products/docker-desktop/
        echo    2. 下载 Docker Desktop for Windows
        echo    3. 运行安装程序
        echo    4. 启动 Docker Desktop
        echo    5. 等待 Docker 完全启动
        echo.
    )
    
    echo 💡 安装完成后，请重新运行此脚本进行验证。
)

echo.
echo ========================================
echo 📞 如需帮助，请查看项目文档或联系技术支持
pause
