/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.red-packet-card {
  position: relative;
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}
.red-packet-card:active {
  transform: scale(0.98);
}
.red-packet-card.is-expired {
  opacity: 0.8;
}
.red-packet-card .card-content {
  background-color: #fff;
}
.red-packet-card .card-content .card-cover {
  position: relative;
  height: 300rpx;
}
.red-packet-card .card-content .card-cover .cover-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.red-packet-card .card-content .card-cover .cover-content {
  position: relative;
  z-index: 1;
  height: 100%;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}
.red-packet-card .card-content .card-cover .cover-content .sender-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.red-packet-card .card-content .card-cover .cover-content .sender-info .avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  margin-right: 16rpx;
}
.red-packet-card .card-content .card-cover .cover-content .sender-info .nickname {
  font-size: 28rpx;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.red-packet-card .card-content .card-cover .cover-content .packet-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.red-packet-card .card-content .card-cover .cover-content .packet-info .title {
  font-size: 36rpx;
  color: #fff;
  font-weight: 500;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.red-packet-card .card-content .card-cover .cover-content .packet-info .amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}
.red-packet-card .card-content .card-cover .cover-content .packet-info .amount .label {
  font-size: 28rpx;
  color: #fff;
  margin-right: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.red-packet-card .card-content .card-cover .cover-content .packet-info .amount .value {
  font-size: 48rpx;
  color: #fff;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.red-packet-card .card-content .card-cover .cover-content .packet-info .amount.expired {
  font-size: 32rpx;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.red-packet-card .card-content .card-cover .cover-content .progress-bar {
  height: 4rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2rpx;
  position: relative;
}
.red-packet-card .card-content .card-cover .cover-content .progress-bar .progress-inner {
  height: 100%;
  background-color: #fff;
  border-radius: 2rpx;
  transition: width 0.3s ease;
}
.red-packet-card .card-content .card-cover .cover-content .progress-bar .progress-text {
  position: absolute;
  right: 0;
  top: 8rpx;
  font-size: 20rpx;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.red-packet-card .card-content .card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
}
.red-packet-card .card-content .card-actions .action-info .time {
  font-size: 24rpx;
  color: #999;
  margin-right: 16rpx;
}
.red-packet-card .card-content .card-actions .action-info .type {
  font-size: 24rpx;
  color: #666;
}
.red-packet-card .card-content .card-actions .action-btn {
  font-size: 28rpx;
  color: #fff;
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  border-radius: 32rpx;
  padding: 12rpx 32rpx;
  line-height: 1.5;
}
.red-packet-card .card-content .card-actions .action-btn.is-expired {
  background: #f5f5f5;
  color: #999;
}
.red-packet-card .card-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}
.red-packet-card .card-animation .animation-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: scaleIn 0.3s ease;
}
.red-packet-card .card-animation .animation-content .animation-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  animation: rotate 1s linear infinite;
}
.red-packet-card .card-animation .animation-content .animation-text {
  font-size: 36rpx;
  color: #fff;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes scaleIn {
from {
    transform: scale(0.8);
}
to {
    transform: scale(1);
}
}
@keyframes rotate {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.action-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.grab-btn, .share-btn {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}
.grab-btn {
  background: linear-gradient(45deg, #ff4d4f, #ff7875);
  color: #fff;
}
.grab-btn[disabled] {
  background: #ccc;
  color: #fff;
}
.share-btn {
  background: #f5f5f5;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}
.share-btn .iconfont {
  margin-right: 6rpx;
  font-size: 24rpx;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.red-packet-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  /* 抢红包阶段样式 */
  /* 正在打开红包样式 */
  /* 红包结果样式 */
  /* 红包详情样式 */
}
.red-packet-popup.show {
  opacity: 1;
  visibility: visible;
}
.red-packet-popup .mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.red-packet-popup .red-packet-container {
  position: relative;
  width: 650rpx;
  max-height: 80vh;
  z-index: 1001;
  transform: scale(0.8);
  transition: transform 0.3s;
}
.red-packet-popup .red-packet-container.animation-scale {
  transform: scale(1);
}
.red-packet-popup .red-packet-container .close-btn {
  position: absolute;
  top: -80rpx;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}
.red-packet-popup .red-packet-container .close-btn .icon {
  color: #fff;
  font-size: 40rpx;
  line-height: 1;
}
.red-packet-popup .red-packet-grab {
  width: 100%;
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(255, 8, 68, 0.3);
}
.red-packet-popup .red-packet-grab .red-packet-top {
  padding: 40rpx 30rpx;
}
.red-packet-popup .red-packet-grab .red-packet-top .packet-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.red-packet-popup .red-packet-grab .red-packet-top .packet-header .sender-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 20rpx;
}
.red-packet-popup .red-packet-grab .red-packet-top .packet-header .packet-info {
  color: #fff;
}
.red-packet-popup .red-packet-grab .red-packet-top .packet-header .packet-info .sender-name {
  display: block;
  font-size: 28rpx;
  margin-bottom: 8rpx;
  opacity: 0.9;
}
.red-packet-popup .red-packet-grab .red-packet-top .packet-header .packet-info .packet-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
}
.red-packet-popup .red-packet-grab .red-packet-top .packet-envelope {
  width: 100%;
  height: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.red-packet-popup .red-packet-grab .red-packet-top .packet-envelope .envelope-image {
  width: 400rpx;
  height: 400rpx;
  transform: rotate(-5deg);
}
.red-packet-popup .red-packet-grab .red-packet-top .packet-envelope .open-text {
  position: absolute;
  bottom: 40rpx;
  color: #FFEE95;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  animation: pulse 1.5s infinite;
}
.red-packet-popup .red-packet-opening {
  width: 100%;
  height: 750rpx;
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.red-packet-popup .red-packet-opening .opening-animation {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
}
.red-packet-popup .red-packet-opening .opening-animation image {
  width: 100%;
  height: 100%;
}
.red-packet-popup .red-packet-opening .opening-text {
  color: #fff;
  font-size: 36rpx;
  font-weight: 500;
}
.red-packet-popup .red-packet-result {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}
.red-packet-popup .red-packet-result .result-header {
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  display: flex;
  align-items: center;
}
.red-packet-popup .red-packet-result .result-header .sender-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 20rpx;
}
.red-packet-popup .red-packet-result .result-header .result-info {
  color: #fff;
}
.red-packet-popup .red-packet-result .result-header .result-info .packet-desc {
  display: block;
  font-size: 28rpx;
  margin-bottom: 8rpx;
  opacity: 0.9;
}
.red-packet-popup .red-packet-result .result-header .result-info .packet-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
}
.red-packet-popup .red-packet-result .result-content {
  padding: 60rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.red-packet-popup .red-packet-result .result-content .result-success {
  text-align: center;
}
.red-packet-popup .red-packet-result .result-content .result-success .result-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}
.red-packet-popup .red-packet-result .result-content .result-success .amount-text {
  font-size: 80rpx;
  font-weight: 600;
  color: #FF3B30;
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
}
.red-packet-popup .red-packet-result .result-content .result-success .amount-unit {
  font-size: 36rpx;
  color: #FF3B30;
  margin-left: 8rpx;
}
.red-packet-popup .red-packet-result .result-content .result-success .result-tips {
  display: block;
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #999;
}
.red-packet-popup .red-packet-result .result-content .result-fail {
  text-align: center;
}
.red-packet-popup .red-packet-result .result-content .result-fail .result-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}
.red-packet-popup .red-packet-result .result-content .result-fail .fail-text {
  font-size: 32rpx;
  color: #666;
}
.red-packet-popup .red-packet-result .action-area {
  padding: 0 30rpx 40rpx;
  display: flex;
  justify-content: center;
}
.red-packet-popup .red-packet-result .action-area .action-btn {
  width: 320rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
}
.red-packet-popup .red-packet-result .action-area .action-btn.check-wallet {
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  color: #fff;
}
.red-packet-popup .red-packet-result .action-area .action-btn.know {
  background-color: #F5F5F5;
  color: #666;
}
.red-packet-popup .red-packet-detail {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.red-packet-popup .red-packet-detail .detail-header {
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  display: flex;
  align-items: center;
}
.red-packet-popup .red-packet-detail .detail-header .sender-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 20rpx;
}
.red-packet-popup .red-packet-detail .detail-header .detail-info {
  color: #fff;
}
.red-packet-popup .red-packet-detail .detail-header .detail-info .packet-desc {
  display: block;
  font-size: 28rpx;
  margin-bottom: 8rpx;
  opacity: 0.9;
}
.red-packet-popup .red-packet-detail .detail-header .detail-info .packet-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
}
.red-packet-popup .red-packet-detail .detail-summary {
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #F0F0F0;
}
.red-packet-popup .red-packet-detail .detail-summary .summary-text {
  font-size: 28rpx;
  color: #666;
}
.red-packet-popup .red-packet-detail .grab-records {
  flex: 1;
  max-height: 600rpx;
  padding: 0 30rpx;
}
.red-packet-popup .red-packet-detail .grab-records .record-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #F5F5F5;
}
.red-packet-popup .red-packet-detail .grab-records .record-item:last-child {
  border-bottom: none;
}
.red-packet-popup .red-packet-detail .grab-records .record-item .record-user {
  display: flex;
  align-items: center;
  flex: 1;
}
.red-packet-popup .red-packet-detail .grab-records .record-item .record-user .user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.red-packet-popup .red-packet-detail .grab-records .record-item .record-user .user-name {
  font-size: 28rpx;
  color: #333;
  max-width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.red-packet-popup .red-packet-detail .grab-records .record-item .record-amount {
  display: flex;
  align-items: center;
}
.red-packet-popup .red-packet-detail .grab-records .record-item .record-amount .amount-value {
  font-size: 36rpx;
  color: #FF3B30;
  font-weight: 600;
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
}
.red-packet-popup .red-packet-detail .grab-records .record-item .record-amount .amount-unit {
  font-size: 24rpx;
  color: #FF3B30;
  margin-left: 4rpx;
}
.red-packet-popup .red-packet-detail .grab-records .record-item .record-amount .best-tag {
  margin-left: 12rpx;
  font-size: 20rpx;
  color: #fff;
  background-color: #FF9500;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}
.red-packet-popup .red-packet-detail .grab-records .record-item .record-time {
  font-size: 24rpx;
  color: #999;
  margin-left: 24rpx;
}
.red-packet-popup .red-packet-detail .grab-records .no-records {
  padding: 80rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}
.red-packet-popup .red-packet-detail .action-area {
  padding: 24rpx 30rpx;
  display: flex;
  justify-content: center;
  border-top: 1px solid #F0F0F0;
}
.red-packet-popup .red-packet-detail .action-area .action-btn {
  width: 320rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
}
.red-packet-popup .red-packet-detail .action-area .action-btn.know {
  background-color: #F5F5F5;
  color: #666;
}

/* 红包动画 */
@keyframes pulse {
0% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
100% {
    transform: scale(1);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.red-packet-creator {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}
.red-packet-creator .creator-form .form-item {
  margin-bottom: 30rpx;
}
.red-packet-creator .creator-form .form-item:last-child {
  margin-bottom: 40rpx;
}
.red-packet-creator .creator-form .form-item .form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}
.red-packet-creator .creator-form .type-selector {
  display: flex;
  justify-content: space-between;
}
.red-packet-creator .creator-form .type-selector .type-item {
  flex: 1;
  height: 110rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
}
.red-packet-creator .creator-form .type-selector .type-item:first-child {
  margin-left: 0;
}
.red-packet-creator .creator-form .type-selector .type-item:last-child {
  margin-right: 0;
}
.red-packet-creator .creator-form .type-selector .type-item.active {
  background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);
  border: 1px solid #FF6347;
}
.red-packet-creator .creator-form .type-selector .type-item .type-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}
.red-packet-creator .creator-form .type-selector .type-item .type-name {
  font-size: 24rpx;
  color: #333;
}
.red-packet-creator .creator-form .amount-input-container {
  position: relative;
  height: 90rpx;
}
.red-packet-creator .creator-form .amount-input-container .amount-input {
  height: 100%;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 0 100rpx 0 30rpx;
  font-size: 32rpx;
  color: #333;
}
.red-packet-creator .creator-form .amount-input-container .amount-unit {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #999;
}
.red-packet-creator .creator-form .available-balance {
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
  display: block;
}
.red-packet-creator .creator-form .count-selector {
  display: flex;
  align-items: center;
  height: 90rpx;
}
.red-packet-creator .creator-form .count-selector .count-btn {
  width: 90rpx;
  height: 90rpx;
  background-color: #F8F8F8;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #333;
  margin: 0;
  padding: 0;
  line-height: 1;
}
.red-packet-creator .creator-form .count-selector .count-btn.minus {
  border-radius: 8rpx 0 0 8rpx;
}
.red-packet-creator .creator-form .count-selector .count-btn.plus {
  border-radius: 0 8rpx 8rpx 0;
}
.red-packet-creator .creator-form .count-selector .count-input {
  flex: 1;
  height: 100%;
  background-color: #F8F8F8;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  margin: 0 2rpx;
}
.red-packet-creator .creator-form .amount-tips {
  font-size: 24rpx;
  color: #FF6347;
  margin-top: 12rpx;
  display: block;
}
.red-packet-creator .creator-form .blessing-input-container {
  position: relative;
  height: 90rpx;
}
.red-packet-creator .creator-form .blessing-input-container .blessing-input {
  height: 100%;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  padding: 0 80rpx 0 30rpx;
  font-size: 28rpx;
  color: #333;
}
.red-packet-creator .creator-form .blessing-input-container .blessing-count {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
}
.red-packet-creator .creator-form .expire-selector {
  display: flex;
  justify-content: space-between;
}
.red-packet-creator .creator-form .expire-selector .expire-item {
  flex: 1;
  height: 80rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10rpx;
  font-size: 26rpx;
  color: #333;
}
.red-packet-creator .creator-form .expire-selector .expire-item:first-child {
  margin-left: 0;
}
.red-packet-creator .creator-form .expire-selector .expire-item:last-child {
  margin-right: 0;
}
.red-packet-creator .creator-form .expire-selector .expire-item.active {
  background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);
  border: 1px solid #FF6347;
  color: #FF6347;
}
.red-packet-creator .action-area .balance-warning {
  text-align: center;
  margin-bottom: 20rpx;
}
.red-packet-creator .action-area .balance-warning .warning-text {
  font-size: 26rpx;
  color: #FF3B30;
}
.red-packet-creator .action-area .submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  border-radius: 45rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}
.red-packet-creator .action-area .submit-btn[disabled] {
  opacity: 0.6;
  background: linear-gradient(135deg, #FF4B2B 0%, #FF0844 100%);
  color: #fff;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.red-packet-selector {
  width: 100%;
}
.red-packet-selector .selector-btn {
  display: flex;
  align-items: center;
  background-color: #FFF;
  border-radius: 8rpx;
  padding: 20rpx 24rpx;
  margin-bottom: 20rpx;
}
.red-packet-selector .selector-btn .btn-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}
.red-packet-selector .selector-btn .btn-icon image {
  width: 100%;
  height: 100%;
}
.red-packet-selector .selector-btn .btn-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.red-packet-selector .selector-btn .btn-arrow {
  width: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.red-packet-selector .selector-btn .btn-arrow .arrow {
  font-size: 36rpx;
  color: #999;
  transform: rotate(90deg);
}
.red-packet-selector .selector-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
}
.red-packet-selector .selector-popup .popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.red-packet-selector .selector-popup .popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #F8F8F8;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  overflow: hidden;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}
.red-packet-selector .selector-popup .popup-content .popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100rpx;
  position: relative;
  background-color: #FFF;
  border-bottom: 1px solid #F0F0F0;
}
.red-packet-selector .selector-popup .popup-content .popup-header .title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.red-packet-selector .selector-popup .popup-content .popup-header .close-btn {
  position: absolute;
  right: 20rpx;
  top: 0;
  bottom: 0;
  width: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.red-packet-selector .selector-popup .popup-content .popup-header .close-btn .close-icon {
  font-size: 40rpx;
  color: #999;
}
.red-packet-selector .selector-popup .popup-content .popup-body {
  padding: 30rpx;
  overflow-y: auto;
  flex: 1;
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}