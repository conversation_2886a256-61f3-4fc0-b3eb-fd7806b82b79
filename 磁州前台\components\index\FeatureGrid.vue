<template>
  <!-- 四宫格特色功能区 -->
  <view class="feature-grid card-section fade-in">
    <view 
      class="feature-item" 
      style="background-image: linear-gradient(to left, #EF8BA6 0%, #FFFFFF 100%);" 
      @click="navigateTo('/pages/red-packet/list')"
    >
      <image class="feature-icon" src="/static/images/tabbar/抢红包.gif"></image>
      <view class="feature-content">
        <text class="feature-title">抢红包</text>
        <text class="feature-desc">最高得99元</text>
      </view>
    </view>
    
    <view 
      class="feature-item" 
      style="background-image: linear-gradient(to left, #6FA3D0 0%, #FFFFFF 100%);" 
      @click="goToSignIn"
    >
      <image class="feature-icon" src="/static/images/tabbar/每日签到.gif"></image>
      <view class="feature-content">
        <text class="feature-title">每日签到</text>
        <text class="feature-desc">连续领好礼</text>
      </view>
    </view>
    
    <view 
      class="feature-item" 
      style="background-image: linear-gradient(to left, #AA7BA0 0%, #FFFFFF 100%);" 
      @click="navigateTo('/subPackages/activity-showcase/pages/index/index')"
    >
      <image class="feature-icon" src="/static/images/tabbar/商家活动.gif"></image>
      <view class="feature-content">
        <text class="feature-title">活动中心</text>
        <text class="feature-desc promo-text">推广赚佣金</text>
      </view>
    </view>
    
    <view 
      class="feature-item" 
      style="background-image: linear-gradient(to left, #6EC3B3 0%, #FFFFFF 100%);" 
      @click="navigateTo('/subPackages/activity/pages/city-events')"
    >
      <image class="feature-icon" src="/static/images/tabbar/同城活动.png"></image>
      <view class="feature-content">
        <text class="feature-title">最美磁州</text>
        <text class="feature-desc">丰富多彩</text>
      </view>
    </view>
  </view>
</template>

<script setup>
function navigateTo(url) {
  if (!url) return;
  
  uni.navigateTo({
    url: url,
    fail: (err) => {
      console.error('页面跳转失败:', err);
      // 尝试使用switchTab
      uni.switchTab({
        url: url,
        fail: (err2) => {
          console.error('switchTab也失败:', err2);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  });
}

function goToSignIn() {
  // 签到功能
  uni.navigateTo({
    url: '/subPackages/checkin/pages/points',
    fail: (err) => {
      console.error('签到页面跳转失败:', err);
      uni.showToast({
        title: '签到功能暂未开放',
        icon: 'none'
      });
    }
  });
}
</script>

<style lang="scss" scoped>
/* 四宫格特色功能区样式 */
.feature-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 30rpx 24rpx;
  background-color: #FFFFFF;
  border-radius: 32rpx;
  margin: 0 25rpx 34rpx;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.feature-item {
  width: 48%;
  height: 164rpx;
  border-radius: 26rpx;
  display: flex;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: none;
}

.feature-item:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  z-index: 2;
  position: relative;
  filter: drop-shadow(0 2rpx 3rpx rgba(0, 0, 0, 0.2));
}

.feature-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 20rpx;
  z-index: 1;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 800;
  color: #333333;
  margin-bottom: 6rpx;
  text-shadow: none;
}

.feature-desc {
  font-size: 24rpx;
  font-weight: 600;
  color: #666666;
  text-shadow: none;
}

/* 推广赚佣金动态效果 */
.promo-text {
  position: relative;
  animation: promo-pulse 1.5s infinite ease-in-out;
  text-shadow: 0 0 5rpx rgba(170, 123, 160, 0.3);
}

@keyframes promo-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.card-section {
  margin-bottom: 20rpx;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}
</style> 