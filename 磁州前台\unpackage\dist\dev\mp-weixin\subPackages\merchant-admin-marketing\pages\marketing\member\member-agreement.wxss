
/* 会员协议页面样式开始 */
.agreement-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 100rpx;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(74, 0, 224, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
}

/* 协议内容样式 */
.agreement-content {
  padding: 20rpx;
}
.section-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 开关样式 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.switch-content {
  flex: 1;
}
.switch-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.switch-desc {
  font-size: 24rpx;
  color: #999;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}
.form-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.form-input-group {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}
.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.form-input-full {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  box-sizing: border-box;
}
.input-suffix {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  height: 80rpx;
  line-height: 80rpx;
}
.input-btn {
  padding: 0 30rpx;
  font-size: 26rpx;
  color: #4A00E0;
  background: rgba(74, 0, 224, 0.1);
  height: 80rpx;
  line-height: 80rpx;
}
.form-textarea {
  width: 100%;
  height: 400rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.form-tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 编辑器工具栏 */
.editor-toolbar {
  display: flex;
  background: #f5f5f5;
  border-radius: 8rpx 8rpx 0 0;
  border: 1rpx solid #ddd;
  border-bottom: none;
}
.toolbar-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1rpx solid #ddd;
}
.toolbar-btn:last-child {
  border-right: none;
}
.toolbar-icon {
  font-size: 28rpx;
  color: #666;
}
.toolbar-icon.bold {
  font-weight: bold;
}
.toolbar-icon.italic {
  font-style: italic;
}
.toolbar-icon.underline {
  text-decoration: underline;
}

/* 预览样式 */
.preview-container {
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}
.preview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}
.preview-content {
  padding: 30rpx;
  max-height: 400rpx;
  overflow-y: auto;
}
.preview-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
}
.preview-btn {
  background: rgba(74, 0, 224, 0.1);
  color: #4A00E0;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

/* 全屏预览 */
.fullscreen-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}
.preview-header {
  height: 100rpx;
  background: #4A00E0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.preview-header-title {
  font-size: 32rpx;
  font-weight: 600;
}
.preview-close {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-scroll {
  flex: 1;
  padding: 30rpx;
}

/* 底部保存栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.save-btn {
  background: #4A00E0;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  width: 100%;
}
/* 会员协议页面样式结束 */
