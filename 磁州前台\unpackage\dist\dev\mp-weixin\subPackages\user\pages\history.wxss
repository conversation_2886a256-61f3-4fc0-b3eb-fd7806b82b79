
.history-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: #0052CC;
  color: #fff;
  z-index: 100;
}
.navbar-left {
  width: 80rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}
.navbar-right {
  width: 80rpx;
  text-align: right;
}
.clear-text {
  font-size: 14px;
  color: #fff;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 历史记录内容 */
.history-content {
  padding: 15px;
}

/* 日期分组 */
.date-group {
  margin-bottom: 20px;
}
.date-header {
  padding: 10px 5px;
  margin-bottom: 10px;
}
.date-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 历史记录项 */
.history-item {
  display: flex;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.history-left {
  width: 140rpx;
  height: 140rpx;
  margin-right: 15px;
}
.history-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
.history-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.history-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.history-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.history-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}
.history-type {
  color: #0052CC;
  background-color: rgba(0, 82, 204, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}
.history-time {
  color: #999;
}

/* 空状态 */
.empty-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20px;
}
.empty-text {
  font-size: 14px;
  color: #999;
}

/* 列表底部 */
.list-bottom {
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  color: #999;
}

/* 清空确认弹窗 */
.clear-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 101;
}
.clear-dialog {
  width: 80%;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
}
.clear-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 10px;
}
.clear-desc {
  font-size: 14px;
  color: #999;
  text-align: center;
  margin-bottom: 20px;
}
.clear-actions {
  display: flex;
  border-top: 1px solid #eee;
  padding-top: 15px;
}
.clear-btn {
  flex: 1;
  text-align: center;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
}
.cancel {
  color: #999;
}
.confirm {
  color: #ff4d4f;
  font-weight: 500;
}
