"use strict";
const common_vendor = require("../../../../common/vendor.js");
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  (_component_path + _component_svg + _component_circle + _component_polyline)();
}
const _sfc_main = {
  __name: "ActivityStatusCard",
  props: {
    activity: {
      type: Object,
      required: true
    },
    showCountdown: {
      type: Boolean,
      default: false
    },
    canCancel: {
      type: Boolean,
      default: false
    },
    cardStyle: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["click", "share", "cancel", "favorite"],
  setup(__props) {
    const props = __props;
    const getTypeText = (type) => {
      switch (type) {
        case "flash":
          return "秒杀";
        case "group":
          return "拼团";
        case "discount":
          return "满减";
        case "coupon":
          return "优惠券";
        default:
          return "活动";
      }
    };
    const getTagClass = (type) => {
      switch (type) {
        case "flash":
          return "tag-flash";
        case "group":
          return "tag-group";
        case "discount":
          return "tag-discount";
        case "coupon":
          return "tag-coupon";
        default:
          return "";
      }
    };
    const getStatusText = (status) => {
      switch (status) {
        case "ongoing":
          return "进行中";
        case "registered":
          return "已报名";
        case "completed":
          return "已完成";
        case "favorite":
          return "已收藏";
        case "cancelled":
          return "已取消";
        default:
          return "";
      }
    };
    const getStatusClass = (status) => {
      switch (status) {
        case "ongoing":
          return "status-ongoing";
        case "registered":
          return "status-registered";
        case "completed":
          return "status-completed";
        case "favorite":
          return "status-favorite";
        case "cancelled":
          return "status-cancelled";
        default:
          return "";
      }
    };
    const discountPercent = common_vendor.computed(() => {
      if (!props.activity.currentPrice || !props.activity.originalPrice)
        return "";
      return Math.floor(props.activity.currentPrice / props.activity.originalPrice * 10);
    });
    const displayParticipants = common_vendor.computed(() => {
      if (!props.activity.participants)
        return [];
      return props.activity.participants.slice(0, 3);
    });
    const countdownHours = common_vendor.ref("00");
    const countdownMinutes = common_vendor.ref("00");
    const countdownSeconds = common_vendor.ref("00");
    let countdownTimer = null;
    const updateCountdown = () => {
      if (!props.activity.endTime)
        return;
      const now = (/* @__PURE__ */ new Date()).getTime();
      const end = new Date(props.activity.endTime).getTime();
      const diff = end - now;
      if (diff <= 0) {
        countdownHours.value = "00";
        countdownMinutes.value = "00";
        countdownSeconds.value = "00";
        clearInterval(countdownTimer);
        return;
      }
      const hours = Math.floor(diff / (1e3 * 60 * 60));
      const minutes = Math.floor(diff % (1e3 * 60 * 60) / (1e3 * 60));
      const seconds = Math.floor(diff % (1e3 * 60) / 1e3);
      countdownHours.value = hours.toString().padStart(2, "0");
      countdownMinutes.value = minutes.toString().padStart(2, "0");
      countdownSeconds.value = seconds.toString().padStart(2, "0");
    };
    const formatDate = (timestamp) => {
      if (!timestamp)
        return "";
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes().toString().padStart(2, "0")}`;
    };
    common_vendor.onMounted(() => {
      if (props.showCountdown) {
        updateCountdown();
        countdownTimer = setInterval(updateCountdown, 1e3);
      }
    });
    common_vendor.onUnmounted(() => {
      if (countdownTimer) {
        clearInterval(countdownTimer);
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.activity.coverImage,
        b: common_vendor.t(getTypeText(__props.activity.type)),
        c: common_vendor.n(getTagClass(__props.activity.type)),
        d: common_vendor.p({
          d: "M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
        }),
        e: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "22",
          height: "22",
          fill: "none",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        f: common_vendor.o(($event) => _ctx.$emit("favorite", __props.activity.id)),
        g: __props.showCountdown && __props.activity.endTime
      }, __props.showCountdown && __props.activity.endTime ? {
        h: common_vendor.t(countdownHours.value),
        i: common_vendor.t(countdownMinutes.value),
        j: common_vendor.t(countdownSeconds.value)
      } : {}, {
        k: common_vendor.t(getStatusText(__props.activity.status)),
        l: common_vendor.n(getStatusClass(__props.activity.status)),
        m: common_vendor.t(__props.activity.title),
        n: common_vendor.p({
          d: "M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0116 0z",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        o: common_vendor.p({
          cx: "12",
          cy: "10",
          r: "3",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        p: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "14",
          height: "14"
        }),
        q: common_vendor.t(__props.activity.shopName),
        r: common_vendor.t(__props.activity.currentPrice),
        s: __props.activity.originalPrice
      }, __props.activity.originalPrice ? {
        t: common_vendor.t(__props.activity.originalPrice),
        v: common_vendor.t(discountPercent.value)
      } : {}, {
        w: common_vendor.p({
          cx: "12",
          cy: "12",
          r: "10",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        x: common_vendor.p({
          points: "12 6 12 12 16 14",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        y: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "14",
          height: "14"
        }),
        z: common_vendor.t(formatDate(__props.activity.startTime)),
        A: __props.activity.participants
      }, __props.activity.participants ? {
        B: common_vendor.f(displayParticipants.value, (participant, idx, i0) => {
          return {
            a: idx,
            b: participant.avatar,
            c: 10 - idx,
            d: `${idx * 15}rpx`
          };
        }),
        C: common_vendor.t(__props.activity.participants.length)
      } : {}, {
        D: common_vendor.o(($event) => _ctx.$emit("click", __props.activity)),
        E: common_vendor.p({
          d: "M18 8A3 3 0 1 0 15 5.83L8 9.41A3 3 0 0 0 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 2-.76l7 3.58A3 3 0 0 0 15 22a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-2 .76l-7-3.58A3 3 0 0 0 6 12a3 3 0 0 0 0-.17L13 8.24a3 3 0 0 0 2 .76 3 3 0 0 0 3-3z",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round",
          fill: "none"
        }),
        F: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "18",
          height: "18"
        }),
        G: common_vendor.o(($event) => _ctx.$emit("share", __props.activity)),
        H: __props.canCancel
      }, __props.canCancel ? {
        I: common_vendor.p({
          d: "M18 6L6 18M6 6l12 12",
          stroke: "currentColor",
          ["stroke-width"]: "2",
          ["stroke-linecap"]: "round",
          ["stroke-linejoin"]: "round"
        }),
        J: common_vendor.p({
          viewBox: "0 0 24 24",
          width: "18",
          height: "18"
        }),
        K: common_vendor.o(($event) => _ctx.$emit("cancel", __props.activity))
      } : {}, {
        L: __props.activity.soldCount && __props.activity.totalCount
      }, __props.activity.soldCount && __props.activity.totalCount ? common_vendor.e({
        M: `${Math.min(100, __props.activity.soldCount / __props.activity.totalCount * 100)}%`,
        N: common_vendor.t(__props.activity.soldCount),
        O: common_vendor.t(__props.activity.totalCount),
        P: __props.activity.totalCount > __props.activity.soldCount
      }, __props.activity.totalCount > __props.activity.soldCount ? {
        Q: common_vendor.t(__props.activity.totalCount - __props.activity.soldCount)
      } : {}) : {}, {
        R: common_vendor.s(__props.cardStyle),
        S: common_vendor.o(($event) => _ctx.$emit("click", __props.activity))
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-abc6c209"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/activity-showcase/components/activity/ActivityStatusCard.js.map
