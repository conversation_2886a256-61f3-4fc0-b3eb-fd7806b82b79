"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const utils_distributionService = require("../../../../../utils/distributionService.js");
const _sfc_main = {
  __name: "distributor-detail",
  setup(__props) {
    const distributorId = common_vendor.ref("");
    const distributorInfo = common_vendor.ref({});
    const activeTab = common_vendor.ref("orders");
    const orders = common_vendor.ref([]);
    const orderLoading = common_vendor.ref(false);
    const orderPagination = common_vendor.reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });
    const teamMembers = common_vendor.ref([]);
    const teamLoading = common_vendor.ref(false);
    const teamPagination = common_vendor.reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });
    const commissions = common_vendor.ref([]);
    const commissionLoading = common_vendor.ref(false);
    const commissionPagination = common_vendor.reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });
    const withdrawals = common_vendor.ref([]);
    const withdrawLoading = common_vendor.ref(false);
    const withdrawPagination = common_vendor.reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = ((_a = currentPage.$page) == null ? void 0 : _a.options) || {};
      distributorId.value = options.id || "";
      if (distributorId.value) {
        getDistributorDetail();
      } else {
        common_vendor.index.showToast({
          title: "缺少分销员ID",
          icon: "none"
        });
        setTimeout(() => {
          goBack();
        }, 1500);
      }
    });
    const getDistributorDetail = async () => {
      try {
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        const result = await utils_distributionService.distributionService.getDistributorDetail(distributorId.value);
        common_vendor.index.hideLoading();
        if (result) {
          distributorInfo.value = result;
        } else {
          common_vendor.index.showToast({
            title: "获取分销员详情失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:555", "获取分销员详情失败", error);
        common_vendor.index.showToast({
          title: "获取分销员详情失败",
          icon: "none"
        });
      }
    };
    const formatAmount = (amount) => {
      return (amount || 0).toFixed(2);
    };
    const formatDate = (dateString) => {
      if (!dateString)
        return "未知";
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    };
    const getStatusClass = (status) => {
      switch (status) {
        case "active":
          return "status-active";
        case "disabled":
          return "status-disabled";
        case "pending":
          return "status-pending";
        case "rejected":
          return "status-rejected";
        default:
          return "";
      }
    };
    const getStatusText = (status) => {
      switch (status) {
        case "active":
          return "已启用";
        case "disabled":
          return "已禁用";
        case "pending":
          return "待审核";
        case "rejected":
          return "已拒绝";
        default:
          return "未知";
      }
    };
    const approveDistributor = () => {
      common_vendor.index.showModal({
        title: "审核通过",
        content: `确定通过 ${distributorInfo.value.name} 的分销员申请吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.reviewDistributorApplication({
                id: distributorId.value,
                status: "approved"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "审核通过成功",
                  icon: "success"
                });
                getDistributorDetail();
              } else {
                common_vendor.index.showModal({
                  title: "审核失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:649", "审核失败", error);
              common_vendor.index.showToast({
                title: "审核失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const rejectDistributor = () => {
      common_vendor.index.showModal({
        title: "拒绝申请",
        content: `确定拒绝 ${distributorInfo.value.name} 的分销员申请吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.reviewDistributorApplication({
                id: distributorId.value,
                status: "rejected"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "已拒绝申请",
                  icon: "success"
                });
                getDistributorDetail();
              } else {
                common_vendor.index.showModal({
                  title: "操作失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:697", "操作失败", error);
              common_vendor.index.showToast({
                title: "操作失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const disableDistributor = () => {
      common_vendor.index.showModal({
        title: "禁用分销员",
        content: `确定禁用 ${distributorInfo.value.name} 的分销员资格吗？禁用后该用户将无法进行分销活动。`,
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.toggleDistributorStatus({
                id: distributorId.value,
                status: "disabled"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "禁用成功",
                  icon: "success"
                });
                getDistributorDetail();
              } else {
                common_vendor.index.showModal({
                  title: "操作失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:745", "操作失败", error);
              common_vendor.index.showToast({
                title: "操作失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const enableDistributor = () => {
      common_vendor.index.showModal({
        title: "启用分销员",
        content: `确定启用 ${distributorInfo.value.name} 的分销员资格吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.toggleDistributorStatus({
                id: distributorId.value,
                status: "active"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "启用成功",
                  icon: "success"
                });
                getDistributorDetail();
              } else {
                common_vendor.index.showModal({
                  title: "操作失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:793", "操作失败", error);
              common_vendor.index.showToast({
                title: "操作失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const setLevel = () => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/set-level?id=${distributorId.value}`
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "分销员详情帮助",
        content: "在此页面您可以查看分销员的详细信息，包括基本资料、推广订单、团队成员、佣金记录和提现记录等。您还可以对分销员进行审核、禁用/启用和设置等级等操作。",
        showCancel: false
      });
    };
    common_vendor.watch(activeTab, (newVal) => {
      if (newVal === "orders" && orders.value.length === 0) {
        getDistributionOrders();
      } else if (newVal === "team" && teamMembers.value.length === 0) {
        getTeamMembers();
      } else if (newVal === "commission" && commissions.value.length === 0) {
        getCommissionRecords();
      } else if (newVal === "withdraw" && withdrawals.value.length === 0) {
        getWithdrawRecords();
      }
    });
    const getDistributionOrders = async () => {
      try {
        orderLoading.value = true;
        const params = {
          distributorId: distributorId.value,
          page: orderPagination.current,
          pageSize: orderPagination.pageSize
        };
        const result = await utils_distributionService.distributionService.getDistributionOrders(params);
        if (result) {
          orders.value = result.list || [];
          orderPagination.current = result.pagination.current;
          orderPagination.total = result.pagination.total;
          orderPagination.totalPages = result.pagination.totalPages;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:860", "获取推广订单列表失败", error);
        common_vendor.index.showToast({
          title: "获取推广订单列表失败",
          icon: "none"
        });
      } finally {
        orderLoading.value = false;
      }
    };
    const getCommissionStatusClass = (status) => {
      switch (status) {
        case "paid":
          return "status-paid";
        case "pending":
          return "status-pending";
        case "frozen":
          return "status-frozen";
        case "cancelled":
          return "status-cancelled";
        default:
          return "";
      }
    };
    const getCommissionStatusText = (status) => {
      switch (status) {
        case "paid":
          return "已结算";
        case "pending":
          return "待结算";
        case "frozen":
          return "已冻结";
        case "cancelled":
          return "已取消";
        default:
          return "未知";
      }
    };
    const prevOrderPage = () => {
      if (orderPagination.current > 1) {
        orderPagination.current--;
        getDistributionOrders();
      }
    };
    const nextOrderPage = () => {
      if (orderPagination.current < orderPagination.totalPages) {
        orderPagination.current++;
        getDistributionOrders();
      }
    };
    const getTeamMembers = async () => {
      try {
        teamLoading.value = true;
        const params = {
          distributorId: distributorId.value,
          page: teamPagination.current,
          pageSize: teamPagination.pageSize
        };
        const result = await utils_distributionService.distributionService.getTeamMembers(params);
        if (result) {
          teamMembers.value = result.list || [];
          teamPagination.current = result.pagination.current;
          teamPagination.total = result.pagination.total;
          teamPagination.totalPages = result.pagination.totalPages;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:940", "获取团队成员列表失败", error);
        common_vendor.index.showToast({
          title: "获取团队成员列表失败",
          icon: "none"
        });
      } finally {
        teamLoading.value = false;
      }
    };
    const viewTeamMember = (item) => {
      common_vendor.index.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail?id=${item.id}`
      });
    };
    const prevTeamPage = () => {
      if (teamPagination.current > 1) {
        teamPagination.current--;
        getTeamMembers();
      }
    };
    const nextTeamPage = () => {
      if (teamPagination.current < teamPagination.totalPages) {
        teamPagination.current++;
        getTeamMembers();
      }
    };
    const getCommissionRecords = async () => {
      try {
        commissionLoading.value = true;
        const params = {
          distributorId: distributorId.value,
          page: commissionPagination.current,
          pageSize: commissionPagination.pageSize
        };
        const result = await utils_distributionService.distributionService.getCommissionList(params);
        if (result) {
          commissions.value = result.list || [];
          commissionPagination.current = result.pagination.current;
          commissionPagination.total = result.pagination.total;
          commissionPagination.totalPages = result.pagination.totalPages;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:995", "获取佣金记录列表失败", error);
        common_vendor.index.showToast({
          title: "获取佣金记录列表失败",
          icon: "none"
        });
      } finally {
        commissionLoading.value = false;
      }
    };
    const formatDateTime = (dateString) => {
      if (!dateString)
        return "未知";
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hour = String(date.getHours()).padStart(2, "0");
      const minute = String(date.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}`;
    };
    const getCommissionTypeClass = (type) => {
      switch (type) {
        case "order":
          return "type-order";
        case "withdraw":
          return "type-withdraw";
        case "refund":
          return "type-refund";
        case "adjust":
          return "type-adjust";
        default:
          return "type-other";
      }
    };
    const getCommissionTypeText = (type) => {
      switch (type) {
        case "order":
          return "订单佣金";
        case "withdraw":
          return "佣金提现";
        case "refund":
          return "订单退款";
        case "adjust":
          return "佣金调整";
        default:
          return "其他";
      }
    };
    const prevCommissionPage = () => {
      if (commissionPagination.current > 1) {
        commissionPagination.current--;
        getCommissionRecords();
      }
    };
    const nextCommissionPage = () => {
      if (commissionPagination.current < commissionPagination.totalPages) {
        commissionPagination.current++;
        getCommissionRecords();
      }
    };
    const getWithdrawRecords = async () => {
      try {
        withdrawLoading.value = true;
        const params = {
          distributorId: distributorId.value,
          page: withdrawPagination.current,
          pageSize: withdrawPagination.pageSize
        };
        const result = await utils_distributionService.distributionService.getWithdrawRecords(params);
        if (result) {
          withdrawals.value = result.list || [];
          withdrawPagination.current = result.pagination.current;
          withdrawPagination.total = result.pagination.total;
          withdrawPagination.totalPages = result.pagination.totalPages;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:1089", "获取提现记录列表失败", error);
        common_vendor.index.showToast({
          title: "获取提现记录列表失败",
          icon: "none"
        });
      } finally {
        withdrawLoading.value = false;
      }
    };
    const getWithdrawStatusClass = (status) => {
      switch (status) {
        case "pending":
          return "status-pending";
        case "approved":
          return "status-approved";
        case "rejected":
          return "status-rejected";
        case "processing":
          return "status-processing";
        case "completed":
          return "status-completed";
        case "failed":
          return "status-failed";
        default:
          return "";
      }
    };
    const getWithdrawStatusText = (status) => {
      switch (status) {
        case "pending":
          return "待审核";
        case "approved":
          return "已通过";
        case "rejected":
          return "已拒绝";
        case "processing":
          return "处理中";
        case "completed":
          return "已完成";
        case "failed":
          return "提现失败";
        default:
          return "未知";
      }
    };
    const getWithdrawMethodText = (method) => {
      switch (method) {
        case "wechat":
          return "微信零钱";
        case "alipay":
          return "支付宝";
        case "bank":
          return "银行卡";
        default:
          return "其他";
      }
    };
    const approveWithdrawal = (item) => {
      common_vendor.index.showModal({
        title: "审核通过",
        content: `确定通过此笔 ${formatAmount(item.amount)}元 的提现申请吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.reviewWithdrawal({
                id: item.id,
                status: "approved"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "审核通过成功",
                  icon: "success"
                });
                getWithdrawRecords();
              } else {
                common_vendor.index.showModal({
                  title: "审核失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:1190", "审核失败", error);
              common_vendor.index.showToast({
                title: "审核失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const rejectWithdrawal = (item) => {
      common_vendor.index.showModal({
        title: "拒绝提现",
        content: "请输入拒绝原因",
        editable: true,
        placeholderText: "请输入拒绝原因",
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "处理中...",
                mask: true
              });
              const result = await utils_distributionService.distributionService.reviewWithdrawal({
                id: item.id,
                status: "rejected",
                remark: res.content || "管理员拒绝"
              });
              common_vendor.index.hideLoading();
              if (result.success) {
                common_vendor.index.showToast({
                  title: "已拒绝提现",
                  icon: "success"
                });
                getWithdrawRecords();
              } else {
                common_vendor.index.showModal({
                  title: "操作失败",
                  content: result.message || "请稍后再试",
                  showCancel: false
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.vue:1241", "操作失败", error);
              common_vendor.index.showToast({
                title: "操作失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const prevWithdrawPage = () => {
      if (withdrawPagination.current > 1) {
        withdrawPagination.current--;
        getWithdrawRecords();
      }
    };
    const nextWithdrawPage = () => {
      if (withdrawPagination.current < withdrawPagination.totalPages) {
        withdrawPagination.current++;
        getWithdrawRecords();
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.o(showHelp),
        c: distributorInfo.value.avatar || "/static/images/default-avatar.png",
        d: common_vendor.t(distributorInfo.value.name || "未知用户"),
        e: common_vendor.t(distributorInfo.value.levelName || "普通分销员"),
        f: distributorInfo.value.levelColor || "#67C23A",
        g: common_vendor.t(getStatusText(distributorInfo.value.status)),
        h: common_vendor.n(getStatusClass(distributorInfo.value.status)),
        i: common_vendor.t(distributorInfo.value.phone || "无联系方式"),
        j: common_vendor.t(formatDate(distributorInfo.value.createdAt)),
        k: common_vendor.t(distributorInfo.value.inviteCode || "无"),
        l: common_vendor.t(distributorInfo.value.parentName || "无"),
        m: common_vendor.t(distributorInfo.value.teamCount || 0),
        n: distributorInfo.value.status === "pending"
      }, distributorInfo.value.status === "pending" ? {
        o: common_vendor.o(approveDistributor),
        p: common_vendor.o(rejectDistributor)
      } : distributorInfo.value.status === "active" ? {
        r: common_vendor.o(disableDistributor),
        s: common_vendor.o(setLevel)
      } : distributorInfo.value.status === "disabled" ? {
        v: common_vendor.o(enableDistributor)
      } : {}, {
        q: distributorInfo.value.status === "active",
        t: distributorInfo.value.status === "disabled",
        w: common_vendor.t(distributorInfo.value.orderCount || 0),
        x: common_vendor.t(formatAmount(distributorInfo.value.commissionTotal)),
        y: common_vendor.t(formatAmount(distributorInfo.value.commissionAvailable)),
        z: common_vendor.t(formatAmount(distributorInfo.value.commissionWithdrawn)),
        A: activeTab.value === "orders" ? 1 : "",
        B: common_vendor.o(($event) => activeTab.value = "orders"),
        C: activeTab.value === "team" ? 1 : "",
        D: common_vendor.o(($event) => activeTab.value = "team"),
        E: activeTab.value === "commission" ? 1 : "",
        F: common_vendor.o(($event) => activeTab.value = "commission"),
        G: activeTab.value === "withdraw" ? 1 : "",
        H: common_vendor.o(($event) => activeTab.value = "withdraw"),
        I: activeTab.value === "orders"
      }, activeTab.value === "orders" ? common_vendor.e({
        J: orders.value.length > 0
      }, orders.value.length > 0 ? {
        K: common_vendor.f(orders.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.orderNo),
            b: common_vendor.t(item.statusText),
            c: item.productImage,
            d: common_vendor.t(item.productName),
            e: common_vendor.t(item.productPrice),
            f: common_vendor.t(item.quantity),
            g: common_vendor.t(formatDate(item.createdAt)),
            h: common_vendor.t(item.orderAmount),
            i: common_vendor.t(item.commission),
            j: common_vendor.t(getCommissionStatusText(item.commissionStatus)),
            k: common_vendor.n(getCommissionStatusClass(item.commissionStatus)),
            l: index
          };
        })
      } : !orderLoading.value ? {
        M: common_assets._imports_0$44
      } : {}, {
        L: !orderLoading.value,
        N: orderLoading.value
      }, orderLoading.value ? {} : {}, {
        O: orders.value.length > 0 && !orderLoading.value
      }, orders.value.length > 0 && !orderLoading.value ? {
        P: common_vendor.t(orderPagination.total),
        Q: common_vendor.t(orderPagination.current),
        R: common_vendor.t(orderPagination.totalPages),
        S: orderPagination.current <= 1 ? 1 : "",
        T: common_vendor.o(prevOrderPage),
        U: orderPagination.current >= orderPagination.totalPages ? 1 : "",
        V: common_vendor.o(nextOrderPage)
      } : {}) : {}, {
        W: activeTab.value === "team"
      }, activeTab.value === "team" ? common_vendor.e({
        X: teamMembers.value.length > 0
      }, teamMembers.value.length > 0 ? {
        Y: common_vendor.f(teamMembers.value, (item, index, i0) => {
          return {
            a: item.avatar || "/static/images/default-avatar.png",
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.levelName),
            d: item.levelColor || "#67C23A",
            e: common_vendor.t(item.phone),
            f: common_vendor.t(item.orderCount || 0),
            g: common_vendor.t(formatAmount(item.commissionTotal)),
            h: common_vendor.t(item.teamCount || 0),
            i: common_vendor.t(formatDate(item.createdAt)),
            j: index,
            k: common_vendor.o(($event) => viewTeamMember(item), index)
          };
        })
      } : !teamLoading.value ? {
        aa: common_assets._imports_0$43
      } : {}, {
        Z: !teamLoading.value,
        ab: teamLoading.value
      }, teamLoading.value ? {} : {}, {
        ac: teamMembers.value.length > 0 && !teamLoading.value
      }, teamMembers.value.length > 0 && !teamLoading.value ? {
        ad: common_vendor.t(teamPagination.total),
        ae: common_vendor.t(teamPagination.current),
        af: common_vendor.t(teamPagination.totalPages),
        ag: teamPagination.current <= 1 ? 1 : "",
        ah: common_vendor.o(prevTeamPage),
        ai: teamPagination.current >= teamPagination.totalPages ? 1 : "",
        aj: common_vendor.o(nextTeamPage)
      } : {}) : {}, {
        ak: activeTab.value === "commission"
      }, activeTab.value === "commission" ? common_vendor.e({
        al: commissions.value.length > 0
      }, commissions.value.length > 0 ? {
        am: common_vendor.f(commissions.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(getCommissionTypeText(item.type)),
            b: common_vendor.n(getCommissionTypeClass(item.type)),
            c: common_vendor.t(item.amount > 0 ? "+" : ""),
            d: common_vendor.t(formatAmount(item.amount)),
            e: item.amount > 0 ? 1 : "",
            f: item.amount < 0 ? 1 : "",
            g: common_vendor.t(item.description || "佣金记录"),
            h: item.orderNo
          }, item.orderNo ? {
            i: common_vendor.t(item.orderNo)
          } : {}, {
            j: common_vendor.t(formatDateTime(item.createdAt)),
            k: common_vendor.t(getCommissionStatusText(item.status)),
            l: common_vendor.n(getCommissionStatusClass(item.status)),
            m: index
          });
        })
      } : !commissionLoading.value ? {
        ao: common_assets._imports_2$41
      } : {}, {
        an: !commissionLoading.value,
        ap: commissionLoading.value
      }, commissionLoading.value ? {} : {}, {
        aq: commissions.value.length > 0 && !commissionLoading.value
      }, commissions.value.length > 0 && !commissionLoading.value ? {
        ar: common_vendor.t(commissionPagination.total),
        as: common_vendor.t(commissionPagination.current),
        at: common_vendor.t(commissionPagination.totalPages),
        av: commissionPagination.current <= 1 ? 1 : "",
        aw: common_vendor.o(prevCommissionPage),
        ax: commissionPagination.current >= commissionPagination.totalPages ? 1 : "",
        ay: common_vendor.o(nextCommissionPage)
      } : {}) : {}, {
        az: activeTab.value === "withdraw"
      }, activeTab.value === "withdraw" ? common_vendor.e({
        aA: withdrawals.value.length > 0
      }, withdrawals.value.length > 0 ? {
        aB: common_vendor.f(withdrawals.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.withdrawNo),
            b: common_vendor.t(getWithdrawStatusText(item.status)),
            c: common_vendor.n(getWithdrawStatusClass(item.status)),
            d: common_vendor.t(formatAmount(item.amount)),
            e: common_vendor.t(formatAmount(item.fee)),
            f: common_vendor.t(formatAmount(item.actualAmount)),
            g: common_vendor.t(getWithdrawMethodText(item.method)),
            h: common_vendor.t(item.account),
            i: common_vendor.t(formatDateTime(item.createdAt)),
            j: item.processedAt
          }, item.processedAt ? {
            k: common_vendor.t(formatDateTime(item.processedAt))
          } : {}, {
            l: item.remark
          }, item.remark ? {
            m: common_vendor.t(item.remark)
          } : {}, {
            n: item.status === "pending"
          }, item.status === "pending" ? {
            o: common_vendor.o(($event) => approveWithdrawal(item), index),
            p: common_vendor.o(($event) => rejectWithdrawal(item), index)
          } : {}, {
            q: index
          });
        })
      } : !withdrawLoading.value ? {
        aD: common_assets._imports_3$35
      } : {}, {
        aC: !withdrawLoading.value,
        aE: withdrawLoading.value
      }, withdrawLoading.value ? {} : {}, {
        aF: withdrawals.value.length > 0 && !withdrawLoading.value
      }, withdrawals.value.length > 0 && !withdrawLoading.value ? {
        aG: common_vendor.t(withdrawPagination.total),
        aH: common_vendor.t(withdrawPagination.current),
        aI: common_vendor.t(withdrawPagination.totalPages),
        aJ: withdrawPagination.current <= 1 ? 1 : "",
        aK: common_vendor.o(prevWithdrawPage),
        aL: withdrawPagination.current >= withdrawPagination.totalPages ? 1 : "",
        aM: common_vendor.o(nextWithdrawPage)
      } : {}) : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/distribution/distributor-detail.js.map
