<template>
  <view class="order-list-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">订单列表</text>
      <view class="navbar-right">
        <view class="search-icon" @click="showSearch">🔍</view>
      </view>
    </view>
    
    <!-- 搜索框 -->
    <view v-if="showSearchBar" class="search-bar">
      <input 
        class="search-input" 
        placeholder="搜索订单号、客户名称" 
        v-model="searchKeyword"
        @confirm="searchOrders" />
      <view class="search-btn" @click="searchOrders">搜索</view>
      <view class="cancel-btn" @click="cancelSearch">取消</view>
    </view>
    
    <!-- 筛选条件 -->
    <view class="filter-section">
      <scroll-view scroll-x="true" class="filter-scroll">
        <view 
          v-for="(status, index) in orderStatuses" 
          :key="index"
          :class="['status-tag', {'active': currentStatus === status.value}]"
          @click="filterByStatus(status.value)">
          {{status.name}}
        </view>
      </scroll-view>
      
      <view class="filter-button" @click="showFilterOptions">
        <text class="filter-icon">⚙️</text>
        <text>筛选</text>
      </view>
    </view>
    
    <!-- 排序选项 -->
    <view class="sort-section">
      <view 
        v-for="(option, index) in sortOptions" 
        :key="index"
        :class="['sort-option', {'active': currentSort === option.value}]"
        @click="sortOrders(option.value)">
        {{option.name}}
        <text v-if="currentSort === option.value" class="sort-icon">{{sortDirection === 'asc' ? '↑' : '↓'}}</text>
      </view>
    </view>
    
    <!-- 订单列表 -->
    <view class="order-list">
      <view 
        v-for="(order, index) in filteredOrders" 
        :key="index"
        class="order-item"
        @click="viewOrderDetail(order.id)">
        <view class="order-header">
          <text class="order-number">订单号: {{order.orderNo}}</text>
          <text class="order-status" :style="{color: getStatusColor(order.status)}">{{getStatusText(order.status)}}</text>
        </view>
        <view class="order-info">
          <view class="customer-info">
            <text class="label">客户:</text>
            <text class="value">{{order.customerName}}</text>
          </view>
          <view class="time-info">
            <text class="label">下单时间:</text>
            <text class="value">{{order.createTime}}</text>
          </view>
          <view class="amount-info">
            <text class="label">订单金额:</text>
            <text class="value price">¥{{order.totalAmount}}</text>
          </view>
        </view>
        <view class="product-preview">
          <image 
            v-for="(product, productIndex) in order.products.slice(0, 3)" 
            :key="productIndex"
            :src="product.image"
            mode="aspectFill"
            class="product-image"></image>
          <view v-if="order.products.length > 3" class="more-products">+{{order.products.length - 3}}</view>
        </view>
        <view class="order-actions">
          <view class="action-btn primary" @click.stop="handleOrder(order.id)">处理订单</view>
          <view class="action-btn" @click.stop="contactCustomer(order.id)">联系客户</view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view v-if="hasMoreOrders" class="load-more" @click="loadMoreOrders">
        <text class="load-text">加载更多</text>
      </view>
      
      <!-- 空状态 -->
      <view v-if="filteredOrders.length === 0" class="empty-state">
        <view class="empty-icon">📭</view>
        <text class="empty-text">暂无订单数据</text>
        <view class="refresh-btn" @click="refreshOrders">刷新</view>
      </view>
    </view>
    
    <!-- 批量操作浮动按钮 -->
    <view class="batch-action-btn" @click="toggleBatchMode">
      <text class="batch-icon">{{batchMode ? '✓' : '☰'}}</text>
    </view>
    
    <!-- 批量操作底部栏 -->
    <view v-if="batchMode" class="batch-action-bar">
      <view class="selection-info">
        已选择 <text class="selected-count">{{selectedOrders.length}}</text> 个订单
      </view>
      <view class="batch-actions">
        <view class="batch-btn" @click="batchExport">导出</view>
        <view class="batch-btn" @click="batchPrint">打印</view>
        <view class="batch-btn primary" @click="batchProcess">批量处理</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showSearchBar: false,
      searchKeyword: '',
      currentStatus: 'all',
      currentSort: 'time',
      sortDirection: 'desc',
      batchMode: false,
      selectedOrders: [],
      hasMoreOrders: true,
      orderStatuses: [
        { name: '全部', value: 'all' },
        { name: '待处理', value: 'pending' },
        { name: '处理中', value: 'processing' },
        { name: '已完成', value: 'completed' },
        { name: '已取消', value: 'cancelled' },
        { name: '退款中', value: 'refunding' }
      ],
      sortOptions: [
        { name: '下单时间', value: 'time' },
        { name: '订单金额', value: 'amount' },
        { name: '客户名称', value: 'customer' }
      ],
      orders: [
        {
          id: '1001',
          orderNo: 'CZ20230501001',
          status: 'pending',
          customerName: '张三',
          createTime: '2023-05-01 10:30',
          totalAmount: '128.00',
          products: [
            { id: '2001', name: '精品水果礼盒', image: '/static/images/product-1.jpg' },
            { id: '2002', name: '有机蔬菜套餐', image: '/static/images/product-2.jpg' }
          ]
        },
        {
          id: '1002',
          orderNo: 'CZ20230501002',
          status: 'processing',
          customerName: '李四',
          createTime: '2023-05-01 11:45',
          totalAmount: '256.50',
          products: [
            { id: '2003', name: '生日蛋糕', image: '/static/images/product-3.jpg' },
            { id: '2004', name: '鲜花束', image: '/static/images/product-4.jpg' },
            { id: '2005', name: '贺卡', image: '/static/images/product-5.jpg' }
          ]
        },
        {
          id: '1003',
          orderNo: 'CZ20230502003',
          status: 'completed',
          customerName: '王五',
          createTime: '2023-05-02 09:15',
          totalAmount: '89.90',
          products: [
            { id: '2006', name: '进口零食礼包', image: '/static/images/product-6.jpg' }
          ]
        },
        {
          id: '1004',
          orderNo: 'CZ20230502004',
          status: 'cancelled',
          customerName: '赵六',
          createTime: '2023-05-02 14:20',
          totalAmount: '199.00',
          products: [
            { id: '2007', name: '红酒套装', image: '/static/images/product-7.jpg' },
            { id: '2008', name: '高档茶叶', image: '/static/images/product-8.jpg' }
          ]
        },
        {
          id: '1005',
          orderNo: 'CZ20230503005',
          status: 'refunding',
          customerName: '钱七',
          createTime: '2023-05-03 16:35',
          totalAmount: '158.80',
          products: [
            { id: '2009', name: '护肤品套装', image: '/static/images/product-9.jpg' },
            { id: '2010', name: '面膜', image: '/static/images/product-10.jpg' },
            { id: '2011', name: '洗面奶', image: '/static/images/product-11.jpg' },
            { id: '2012', name: '爽肤水', image: '/static/images/product-12.jpg' }
          ]
        }
      ]
    }
  },
  computed: {
    filteredOrders() {
      let result = [...this.orders];
      
      // 状态筛选
      if (this.currentStatus !== 'all') {
        result = result.filter(order => order.status === this.currentStatus);
      }
      
      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(order => 
          order.orderNo.toLowerCase().includes(keyword) || 
          order.customerName.toLowerCase().includes(keyword)
        );
      }
      
      // 排序
      result.sort((a, b) => {
        let compareResult = 0;
        
        if (this.currentSort === 'time') {
          compareResult = new Date(a.createTime) - new Date(b.createTime);
        } else if (this.currentSort === 'amount') {
          compareResult = parseFloat(a.totalAmount) - parseFloat(b.totalAmount);
        } else if (this.currentSort === 'customer') {
          compareResult = a.customerName.localeCompare(b.customerName);
        }
        
        return this.sortDirection === 'asc' ? compareResult : -compareResult;
      });
      
      return result;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    showSearch() {
      this.showSearchBar = true;
    },
    cancelSearch() {
      this.showSearchBar = false;
      this.searchKeyword = '';
    },
    searchOrders() {
      console.log('搜索关键词:', this.searchKeyword);
      // 实际项目中，这里应该调用API进行搜索
    },
    filterByStatus(status) {
      this.currentStatus = status;
    },
    showFilterOptions() {
      uni.showActionSheet({
        itemList: ['按时间范围筛选', '按商品类型筛选', '按支付方式筛选'],
        success: (res) => {
          uni.showToast({
            title: '高级筛选功能开发中',
            icon: 'none'
          });
        }
      });
    },
    sortOrders(sortType) {
      if (this.currentSort === sortType) {
        // 切换排序方向
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        this.currentSort = sortType;
        this.sortDirection = 'desc'; // 默认降序
      }
    },
    viewOrderDetail(orderId) {
      uni.navigateTo({
        url: `./detail?id=${orderId}`
      });
    },
    handleOrder(orderId) {
      uni.navigateTo({
        url: `./detail?id=${orderId}&action=process`
      });
    },
    contactCustomer(orderId) {
      uni.showToast({
        title: '联系客户功能开发中',
        icon: 'none'
      });
    },
    loadMoreOrders() {
      uni.showLoading({
        title: '加载中...'
      });
      
      // 模拟加载更多订单
      setTimeout(() => {
        uni.hideLoading();
        
        // 这里应该是实际加载更多数据的逻辑
        // 模拟没有更多数据了
        this.hasMoreOrders = false;
        
        uni.showToast({
          title: '没有更多订单了',
          icon: 'none'
        });
      }, 1000);
    },
    refreshOrders() {
      uni.showLoading({
        title: '刷新中...'
      });
      
      // 模拟刷新订单列表
      setTimeout(() => {
        uni.hideLoading();
        
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 1000);
    },
    toggleBatchMode() {
      this.batchMode = !this.batchMode;
      if (!this.batchMode) {
        this.selectedOrders = [];
      }
    },
    toggleOrderSelection(orderId) {
      const index = this.selectedOrders.indexOf(orderId);
      if (index === -1) {
        this.selectedOrders.push(orderId);
      } else {
        this.selectedOrders.splice(index, 1);
      }
    },
    batchExport() {
      if (this.selectedOrders.length === 0) {
        uni.showToast({
          title: '请先选择订单',
          icon: 'none'
        });
        return;
      }
      
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      });
    },
    batchPrint() {
      if (this.selectedOrders.length === 0) {
        uni.showToast({
          title: '请先选择订单',
          icon: 'none'
        });
        return;
      }
      
      uni.showToast({
        title: '打印功能开发中',
        icon: 'none'
      });
    },
    batchProcess() {
      if (this.selectedOrders.length === 0) {
        uni.showToast({
          title: '请先选择订单',
          icon: 'none'
        });
        return;
      }
      
      uni.showModal({
        title: '批量处理',
        content: `确认处理选中的 ${this.selectedOrders.length} 个订单？`,
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '批量处理功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },
    getStatusColor(status) {
      const colors = {
        pending: '#FF9800',
        processing: '#2196F3',
        completed: '#4CAF50',
        cancelled: '#9E9E9E',
        refunding: '#F44336'
      };
      return colors[status] || '#333333';
    },
    getStatusText(status) {
      const texts = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成',
        cancelled: '已取消',
        refunding: '退款中'
      };
      return texts[status] || '未知状态';
    }
  }
}
</script>

<style>
.order-list-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 16px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.search-icon {
  font-size: 20px;
  color: #fff;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.search-input {
  flex: 1;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 18px;
  padding: 0 12px;
  font-size: 14px;
}

.search-btn {
  padding: 0 12px;
  height: 36px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 18px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.cancel-btn {
  padding: 0 12px;
  height: 36px;
  color: #666;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.filter-section {
  display: flex;
  padding: 12px 16px;
  background-color: #fff;
  margin-top: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
}

.status-tag {
  display: inline-block;
  padding: 6px 12px;
  margin-right: 8px;
  background-color: #f0f0f0;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
}

.status-tag.active {
  background-color: #e6f7ff;
  color: #1677FF;
  border: 1px solid #91caff;
}

.filter-button {
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-left: 8px;
  border-left: 1px solid #eee;
}

.filter-icon {
  margin-right: 4px;
}

.sort-section {
  display: flex;
  background-color: #fff;
  padding: 12px 16px;
  margin-top: 1px;
}

.sort-option {
  margin-right: 24px;
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
}

.sort-option.active {
  color: #1677FF;
  font-weight: 500;
}

.sort-icon {
  margin-left: 4px;
}

.order-list {
  padding: 16px;
}

.order-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.order-number {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.order-status {
  font-size: 14px;
  font-weight: 500;
}

.order-info {
  margin-bottom: 12px;
}

.customer-info, .time-info, .amount-info {
  display: flex;
  margin-bottom: 6px;
}

.label {
  width: 70px;
  color: #666;
  font-size: 13px;
}

.value {
  flex: 1;
  font-size: 13px;
  color: #333;
}

.price {
  font-weight: 600;
  color: #ff6a00;
}

.product-preview {
  display: flex;
  margin-bottom: 12px;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  margin-right: 8px;
  background-color: #f0f0f0;
}

.more-products {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  background-color: rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  margin-left: 8px;
  background-color: #f0f0f0;
  color: #333;
}

.action-btn.primary {
  background-color: #1677FF;
  color: #fff;
}

.load-more {
  text-align: center;
  padding: 16px 0;
}

.load-text {
  font-size: 14px;
  color: #666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 16px;
}

.refresh-btn {
  padding: 8px 24px;
  background-color: #1677FF;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
}

.batch-action-btn {
  position: fixed;
  right: 16px;
  bottom: 80px;
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #1677FF;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 100;
}

.batch-icon {
  color: #fff;
  font-size: 24px;
}

.batch-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  padding: 12px 16px;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.selection-info {
  font-size: 14px;
  color: #666;
}

.selected-count {
  color: #1677FF;
  font-weight: 600;
}

.batch-actions {
  display: flex;
}

.batch-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  margin-left: 8px;
  background-color: #f0f0f0;
  color: #333;
}

.batch-btn.primary {
  background-color: #1677FF;
  color: #fff;
}
</style> 