{"version": 3, "file": "list.js", "sources": ["subPackages/service/pages/list.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcc2VydmljZVxwYWdlc1xsaXN0LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"service-list-container premium-style\" :data-service-type=\"serviceType\">\n    <!-- 高级自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"back-btn\" @click=\"navigateBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <view class=\"navbar-title\">{{serviceTitle}}</view>\n      <view class=\"navbar-right\"></view>\n    </view>\n    \n    <!-- 高级搜索框 -->\n    <view class=\"search-container\" v-if=\"showSearchBox\">\n      <view class=\"search-box\">\n        <image class=\"search-icon\" src=\"/static/images/tabbar/放大镜.png\"></image>\n        <input class=\"search-input\" type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索职位、公司或关键词\" confirm-type=\"search\" @confirm=\"applyKeywordSearch\" />\n        <view class=\"search-cancel\" v-if=\"searchKeyword\" @click=\"searchKeyword = ''\">×</view>\n      </view>\n    </view>\n    \n    <!-- 高级一级分类标签栏 -->\n    <scroll-view class=\"category-tabs\" scroll-x show-scrollbar=\"false\" v-if=\"showSubCategories\" enhanced :bounces=\"true\">\n        <view \n          v-for=\"(tab, index) in subCategories\" \n          :key=\"index\" \n          class=\"tab-item\" \n          :class=\"{ active: currentTab === index }\"\n          @click=\"switchTab(index)\"\n        >\n          {{tab.name}}\n        </view>\n      </scroll-view>\n    \n    <!-- 高级子分类标签栏 -->\n    <scroll-view class=\"subcategory-tabs\" scroll-x show-scrollbar=\"false\" v-if=\"showSubSubCategories && subSubCategories.length > 0\" enhanced :bounces=\"true\">\n      <view \n        v-for=\"(subTab, index) in subSubCategories\" \n        :key=\"index\" \n        class=\"subtab-item\" \n        :class=\"{ active: currentSubTab === index }\"\n        @click=\"switchSubTab(index)\"\n      >\n        {{subTab.name}}\n    </view>\n    </scroll-view>\n    \n    <!-- 高级筛选栏 -->\n    <view class=\"filter-container\">\n      <view class=\"filter-wrapper\">\n        <view class=\"filter-item\" ref=\"areaBtn\" @click=\"toggleAreaFilter\">\n          <text :class=\"{ 'active-filter': selectedArea !== '全部区域' }\">{{selectedArea}}</text>\n          <view class=\"filter-arrow\" :class=\"{ 'arrow-up': showAreaFilter }\"></view>\n      </view>\n        <view class=\"filter-item\" ref=\"sortBtn\" @click=\"toggleSort('default')\">\n          <text :class=\"{ 'active-filter': sortBy !== 'default' }\">{{sortBy === 'default' ? '最新' : sortBy}}</text>\n          <view class=\"filter-arrow\" :class=\"{ 'arrow-up': showSortFilter }\"></view>\n      </view>\n      </view>\n    </view>\n    \n    <!-- 高级区域筛选弹出层 -->\n    <view class=\"filter-dropdown area-dropdown\" v-if=\"showAreaFilter\" :style=\"{ top: areaDropdownTop + 'px' }\">\n      <scroll-view scroll-y class=\"dropdown-scroll\">\n        <view class=\"dropdown-item\" \n          v-for=\"(area, index) in areaList\" \n          :key=\"index\"\n          :class=\"{ 'active-item': area === selectedArea }\"\n          @click=\"selectArea(area)\">\n          <text class=\"dropdown-item-text\">{{area}}</text>\n          <text class=\"dropdown-item-check\" v-if=\"area === selectedArea\">✓</text>\n        </view>\n      </scroll-view>\n    </view>\n    \n    <!-- 高级排序筛选弹出层 -->\n    <view class=\"filter-dropdown sort-dropdown\" v-if=\"showSortFilter\" :style=\"{ top: sortDropdownTop + 'px' }\">\n      <view class=\"dropdown-item\" \n        v-for=\"(sort, index) in sortList\" \n        :key=\"index\"\n        :class=\"{ 'active-item': (sortBy === 'default' && sort === '最新') || sort === sortBy }\"\n        @click=\"selectSort(sort)\">\n        <text class=\"dropdown-item-text\">{{sort}}</text>\n        <text class=\"dropdown-item-check\" v-if=\"(sortBy === 'default' && sort === '最新') || sort === sortBy\">✓</text>\n      </view>\n    </view>\n    \n    <!-- 遮罩层 -->\n    <view class=\"filter-mask\" v-if=\"showAreaFilter || showSortFilter\" @click=\"closeAllFilters\"></view>\n    \n    <!-- 高级列表内容 -->\n    <scroll-view \n      scroll-y \n      class=\"service-scroll\" \n      @scrolltolower=\"loadMore\" \n      refresher-enabled \n      :refresher-triggered=\"isRefreshing\"\n      @refresherrefresh=\"onRefresh\"\n      enhanced\n      :bounces=\"true\"\n      :show-scrollbar=\"false\"\n    >\n      <view v-if=\"serviceList.length > 0\" class=\"service-list\">\n        <!-- 高级职位卡片 -->\n        <view \n          v-for=\"(item, index) in serviceList\" \n          :key=\"index\" \n          class=\"service-item\"\n          hover-class=\"service-item-hover\"\n          @click=\"navigateToDetail(item)\"\n        >\n          <view class=\"service-content\">\n            <view class=\"service-header\">\n              <view class=\"service-header-left\">\n              <text class=\"service-tag\">{{item.category}}</text>\n                <text class=\"service-subcategory\" v-if=\"item.subcategory\">{{item.subcategory}}</text>\n            </view>\n              <view class=\"service-meta-right\">\n                <text class=\"service-area\">{{item.area || '全城'}}</text>\n              </view>\n            </view>\n            \n            <view class=\"service-main\">\n              <view class=\"service-title-wrapper\">\n            <text class=\"service-title\">{{item.content}}</text>\n                <text class=\"service-price\" v-if=\"item.price\">¥{{item.price}}</text>\n              </view>\n            \n            <!-- 图片区域，如果有图片则显示 -->\n            <view class=\"service-images\" v-if=\"item.images && item.images.length > 0\">\n              <image \n                v-for=\"(img, imgIndex) in item.images.slice(0, 3)\" \n                :key=\"imgIndex\" \n                :src=\"img\" \n                mode=\"aspectFill\" \n                class=\"service-image\"\n                  :class=\"{'single-image': item.images.length === 1}\"\n              ></image>\n            </view>\n            \n            <view class=\"service-footer\">\n              <view class=\"service-meta\">\n                  <view class=\"meta-tag\">\n                    <text class=\"meta-views\">{{item.views || 0}}人浏览</text>\n                  </view>\n                  <view class=\"meta-time\">{{item.time}}</view>\n              </view>\n              <view class=\"service-actions\">\n                  <view class=\"action-btn contact-btn\">\n                  <text class=\"action-text\">联系</text>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 高级空状态 -->\n      <view v-else class=\"empty-state\">\n        <image src=\"/static/images/empty.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\n        <text class=\"empty-text\">暂无相关职位信息</text>\n        <text class=\"empty-subtext\">换个筛选条件试试吧</text>\n        <view class=\"empty-btn\" @click=\"resetFilters\">重置筛选</view>\n      </view>\n      \n      <!-- 加载更多状态 -->\n      <view class=\"loading-more\" v-if=\"serviceList.length > 0 && hasMore\">\n        <view class=\"loading-indicator\"></view>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n      \n      <!-- 加载完成提示 -->\n      <view class=\"loading-done\" v-if=\"serviceList.length > 0 && !hasMore\">\n        <text class=\"loading-done-text\">— 已经到底啦 —</text>\n      </view>\n    </scroll-view>\n    \n    <!-- 高级悬浮发布按钮 -->\n    <view class=\"publish-btn\" hover-class=\"publish-btn-hover\" @click=\"navigateToPublish\">\n      <text class=\"publish-icon\">+</text>\n      <text class=\"publish-text\">发布</text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, nextTick } from 'vue';\n\n// 响应式状态\nconst statusBarHeight = ref(20);\nconst serviceType = ref('');\nconst serviceTitle = ref('服务列表');\nconst currentTab = ref(0);\nconst currentSubTab = ref(0);\nconst sortBy = ref('default');\nconst isRefreshing = ref(false);\nconst hasMore = ref(true);\nconst page = ref(1);\nconst limit = ref(10);\nconst serviceList = ref([]);\nconst showSubCategories = ref(false);\nconst subCategories = ref([]);\nconst showSubSubCategories = ref(false);\nconst subSubCategories = ref([]);\nconst showSearchBox = ref(false);\nconst searchKeyword = ref('');\nconst selectedArea = ref('全部区域');\nconst areaList = ref(['全部区域', '城区', '磁州镇', '讲武城镇', '岳城镇', '观台镇', '白土镇', '黄沙镇']);\nconst showAreaFilter = ref(false);\nconst showSortFilter = ref(false);\nconst sortList = ref(['最新', '最热', '附近']);\nconst areaDropdownTop = ref(44);\nconst sortDropdownTop = ref(44);\nconst areaBtn = ref(null);\nconst sortBtn = ref(null);\n\n// 设置服务标题\nconst setServiceTitle = (type) => {\n  const titleMap = {\n    'home': '到家服务',\n    'find': '寻找服务',\n    'business': '生意转让',\n    'job': '招聘信息',\n    'resume': '求职信息',\n    'house_rent': '房屋出租',\n    'house_sell': '房屋出售',\n    'second_car': '二手车辆',\n    'pet': '宠物信息',\n    'dating': '婚恋交友',\n    'merchant_activity': '商家活动',\n    'car': '车辆服务',\n    'second_hand': '二手闲置',\n    'carpool': '磁州拼车',\n    'education': '教育培训',\n    'other': '其他服务'\n  };\n  \n  serviceTitle.value = titleMap[type] || '服务列表';\n};\n\n// 切换标签\nconst switchTab = (index) => {\n  if (currentTab.value !== index) {\n    currentTab.value = index;\n    \n    // 获取选中的服务类型\n    const selectedType = subCategories.value[index].type;\n    \n    // 更新当前服务类型\n    serviceType.value = selectedType;\n    \n    // 更新服务标题\n    setServiceTitle(selectedType);\n    \n    // 加载对应的二级分类\n    loadSecondLevelCategories(selectedType);\n    \n    // 重置列表数据\n    page.value = 1;\n    serviceList.value = [];\n    hasMore.value = true;\n    loadServiceList();\n  }\n};\n\n// 切换子分类的标签\nconst switchSubTab = (index) => {\n  if (currentSubTab.value !== index) {\n    currentSubTab.value = index;\n    \n    // 重置列表数据\n    page.value = 1;\n    serviceList.value = [];\n    hasMore.value = true;\n    loadServiceList();\n  }\n};\n\n// 切换排序方式\nconst toggleSort = (sort) => {\n  if (sort === 'default') {\n    showSortFilter.value = !showSortFilter.value;\n    // 关闭其他筛选\n    showAreaFilter.value = false;\n    \n    if (showSortFilter.value) {\n      // 动态获取筛选栏底部位置\n      nextTick(() => {\n        const query = uni.createSelectorQuery();\n        query.select('.filter-container').boundingClientRect(container => {\n          if (container) {\n            // 下拉菜单应该紧贴筛选栏底部\n            sortDropdownTop.value = container.height + container.top;\n          }\n        }).exec();\n      });\n    }\n  } else {\n    sortBy.value = sort;\n    page.value = 1;\n    serviceList.value = [];\n    hasMore.value = true;\n    loadServiceList();\n  }\n};\n\n// 选择排序\nconst selectSort = (sort) => {\n  // 判断是否修改了排序方式\n  const previousSort = sortBy.value;\n  \n  // 更新排序方式\n  if (sort === '最新') {\n    sortBy.value = 'default';\n  } else {\n    sortBy.value = sort; // 直接使用选择的排序名称\n  }\n  \n  // 隐藏排序筛选\n  showSortFilter.value = false;\n  \n  // 如果排序方式变化，重新加载数据\n  if (previousSort !== sortBy.value) {\n    page.value = 1;\n    serviceList.value = [];\n    hasMore.value = true;\n    loadServiceList();\n  }\n};\n\n// 加载服务列表数据\nconst loadServiceList = () => {\n  // 模拟请求延迟\n  uni.showLoading({\n    title: '加载中'\n  });\n  \n  // 获取当前二级分类类型\n  const subCategoryType = showSubSubCategories.value && currentSubTab.value > 0\n    ? subSubCategories.value[currentSubTab.value].type\n    : 'all';\n  \n  // 模拟异步加载数据\n  setTimeout(() => {\n    // 这里模拟从本地存储获取发布的信息\n    try {\n      // 获取所有发布信息\n      const allPublishedInfo = uni.getStorageSync('publishedInfo') || [];\n      \n      // 根据当前页面类型过滤\n      let filteredList = [];\n      \n      // 根据当前选中的服务类型进行过滤\n      switch (serviceType.value) {\n        case 'home':\n          // 到家服务\n        if (subCategoryType === 'all') {\n          filteredList = allPublishedInfo.filter(item => \n            item.type === 'home_service'\n          );\n        } else {\n          filteredList = allPublishedInfo.filter(item => \n            item.type === 'home_service' && item.serviceType === subCategoryType\n            );\n          }\n          break;\n          \n        case 'find':\n          // 寻找服务\n          if (subCategoryType === 'all') {\n            filteredList = allPublishedInfo.filter(item => \n              item.type === 'find_service'\n            );\n          } else {\n            filteredList = allPublishedInfo.filter(item => \n              item.type === 'find_service' && item.findCategory === subCategoryType\n            );\n          }\n          break;\n          \n        case 'business':\n          // 生意转让\n  if (subCategoryType === 'all') {\n    filteredList = allPublishedInfo.filter(item => \n              item.type === 'business_transfer'\n    );\n  } else {\n    filteredList = allPublishedInfo.filter(item => \n              item.type === 'business_transfer' && item.businessCategory === subCategoryType\n    );\n  }\n  break;\n  \ncase 'job':\n  // 招聘信息\n  if (subCategoryType === 'all') {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'hire'\n    );\n  } else {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'hire' && item.jobCategory === subCategoryType\n    );\n  }\n  break;\n  \ncase 'resume':\n  // 求职信息\n  if (subCategoryType === 'all') {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'job_wanted'\n    );\n  } else {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'job_wanted' && item.resumeCategory === subCategoryType\n    );\n  }\n  break;\n  \ncase 'car':\n  // 车辆服务\n  if (subCategoryType === 'all') {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'car_service'\n    );\n  } else {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'car_service' && item.carServiceType === subCategoryType\n    );\n  }\n  break;\n  \ncase 'second_hand':\n  // 二手闲置\n  if (subCategoryType === 'all') {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'second_hand'\n    );\n  } else {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'second_hand' && item.secondHandCategory === subCategoryType\n    );\n  }\n  break;\n  \ncase 'carpool':\n  // 磁州拼车\n  if (subCategoryType === 'all') {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'carpool'\n    );\n  } else {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'carpool' && item.carpoolType === subCategoryType\n    );\n  }\n  break;\n  \ncase 'education':\n  // 教育培训\n  if (subCategoryType === 'all') {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'education'\n    );\n  } else {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'education' && item.educationType === subCategoryType\n    );\n  }\n  break;\n  \n        case 'dating':\n          // 婚恋交友\n          if (subCategoryType === 'all') {\n            filteredList = allPublishedInfo.filter(item => \n              item.type === 'dating'\n            );\n          } else {\n            filteredList = allPublishedInfo.filter(item => \n              item.type === 'dating' && item.datingCategory === subCategoryType\n            );\n          }\n          break;\n          \n        case 'merchant_activity':\n          // 商家活动\n          if (subCategoryType === 'all') {\n            filteredList = allPublishedInfo.filter(item => \n              item.type === 'merchant_activity'\n            );\n          } else {\n            filteredList = allPublishedInfo.filter(item => \n              item.type === 'merchant_activity' && item.activityCategory === subCategoryType\n    );\n  }\n  break;\n  \ncase 'other':\n  // 其他服务\n  if (subCategoryType === 'all') {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'other_service'\n    );\n  } else {\n    filteredList = allPublishedInfo.filter(item => \n      item.type === 'other_service' && item.otherServiceType === subCategoryType\n    );\n  }\n  break;\n  \n        case 'house_rent':\n          // 房屋出租\n  filteredList = allPublishedInfo.filter(item => \n            item.type === 'house_rent'\n          );\n          break;\n          \n        case 'house_sell':\n          // 房屋出售\n          filteredList = allPublishedInfo.filter(item => \n            item.type === 'house_sell'\n          );\n          break;\n          \n        case 'second_car':\n          // 二手车辆\n          filteredList = allPublishedInfo.filter(item => \n            item.type === 'used_car'\n          );\n          break;\n          \n        case 'pet':\n          // 宠物信息\n          filteredList = allPublishedInfo.filter(item => \n            item.type === 'pet'\n          );\n          break;\n          \n        default:\n          // 默认显示所有信息\n          filteredList = allPublishedInfo;\n}\n\n// 按排序方式排序\nif (sortBy.value === 'default') {\n  // 最新：按发布时间排序\n  filteredList.sort((a, b) => new Date(b.publishTime || 0) - new Date(a.publishTime || 0));\n} else if (sortBy.value === '最热') {\n  // 最热：按浏览量排序\n  filteredList.sort((a, b) => (b.views || 0) - (a.views || 0));\n} else if (sortBy.value === '附近') {\n  // 附近：按距离排序，有距离信息的显示在前面\n  filteredList.sort((a, b) => {\n    // 如果有距离信息，按距离排序\n    if (a.distance && b.distance) {\n      return (a.distance || 999999) - (b.distance || 999999);\n    }\n    // 如果只有a有距离信息，a排前面\n    if (a.distance) return -1;\n    // 如果只有b有距离信息，b排前面\n    if (b.distance) return 1;\n    // 都没有距离信息，保持原顺序\n    return 0;\n  });\n}\n\n// 分页处理\nconst start = (page.value - 1) * limit.value;\nconst end = page.value * limit.value;\nconst pageData = filteredList.slice(start, end);\n\n// 更新数据和状态\nif (pageData.length > 0) {\n  serviceList.value = [...serviceList.value, ...pageData];\n  hasMore.value = filteredList.length > serviceList.value.length;\n} else {\n  hasMore.value = false;\n}\n\n// 如果没有数据，添加一些示例数据\nif (serviceList.value.length === 0) {\n  addSampleData();\n}\n    } catch (e) {\n      console.error('加载服务列表失败', e);\n      addSampleData();\n    }\n    \n    uni.hideLoading();\n    \n    // 如果是下拉刷新，结束刷新状态\n    if (isRefreshing.value) {\n      isRefreshing.value = false;\n    }\n  }, 800);\n};\n\n// 添加示例数据\nconst addSampleData = () => {\n  // 根据当前选中的服务类型添加示例数据\n  switch (serviceType.value) {\n    case 'home':\n      // 到家服务示例数据\n        serviceList.value.push({\n          id: 'sample-home-1',\n          category: '家政服务',\n          content: '专业家政保洁，开荒保洁，日常保洁，家电清洗，擦玻璃等服务',\n          time: '2024-05-01 10:30',\n          views: 128,\n          images: ['/static/images/banner/banner-1.png'],\n          type: 'home_service',\n          pageType: 'home-service-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-home-2',\n          category: '维修改造',\n          content: '专业水电安装维修，管道疏通，灯具安装，墙面翻新',\n          time: '2024-05-01 09:45',\n          views: 156,\n          images: ['/static/images/banner/banner-2.png'],\n          type: 'home_service',\n          pageType: 'home-service-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-home-3',\n          category: '开锁换锁',\n          content: '专业开锁换锁，汽车开锁，保险柜开锁，安装智能锁',\n          time: '2024-05-01 08:20',\n          views: 89,\n          type: 'home_service',\n          pageType: 'home-service-detail'\n        });\n      break;\n      \n    case 'business':\n      // 生意转让示例数据\n      serviceList.value.push({\n        id: 'sample-business-1',\n        category: '餐饮店铺',\n        subcategory: '中餐店',\n        content: '黄金地段餐饮店整体转让，客流稳定，接手即可盈利',\n        time: '2024-05-01 14:30',\n        views: 198,\n        images: ['/static/images/banner/banner-3.png'],\n        type: 'business_transfer',\n        pageType: 'business-transfer-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-business-2',\n        category: '零售店铺',\n        subcategory: '服装店',\n        content: '县城中心奶茶店转让，设备齐全，接手即可营业',\n        time: '2024-05-01 15:20',\n        views: 156,\n        type: 'business_transfer',\n        pageType: 'business-transfer-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-business-3',\n        category: '美容美发',\n        content: '美容美发店转让，位置好，客源稳定，接手即可营业',\n        time: '2024-05-01 16:10',\n        views: 142,\n        type: 'business_transfer',\n        pageType: 'business-transfer-detail'\n      });\n      break;\n      \n    case 'resume':\n      // 求职信息示例数据\n      serviceList.value.push({\n        id: 'sample-resume-1',\n        category: '求职信息',\n        content: '会计专业毕业，有初级会计证书，两年工作经验，求职会计相关工作',\n        time: '2024-05-01 15:30',\n        views: 95,\n        type: 'job_wanted',\n        pageType: 'job-seeking-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-resume-2',\n        category: '求职信息',\n        content: '有多年销售经验，善于沟通，能吃苦耐劳，求职销售相关工作',\n        time: '2024-05-01 14:45',\n        views: 112,\n        type: 'job_wanted',\n        pageType: 'job-seeking-detail'\n      });\n      break;\n      \n    case 'find':\n      // 寻找服务示例数据\n      serviceList.value.push({\n        id: 'sample-find-1',\n        category: '维修服务',\n        subcategory: '水电维修',\n        content: '找水电工维修厨房水管漏水，今天急修',\n        time: '2024-05-01 11:30',\n        views: 75,\n        type: 'find_service',\n        pageType: 'find-service-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-find-2',\n        category: '家政服务',\n        subcategory: '保洁服务',\n        content: '寻找家政阿姨，每周定期打扫家庭卫生',\n        time: '2024-05-01 10:45',\n        views: 86,\n        type: 'find_service',\n        pageType: 'find-service-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-find-3',\n        category: '安装服务',\n        subcategory: '家具安装',\n        content: '寻找搬家服务，三室两厅搬家，本周六上午',\n        time: '2024-05-01 09:15',\n        views: 92,\n        type: 'find_service',\n        pageType: 'find-service-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-find-4',\n        category: '教育服务',\n        content: '寻找高中数学家教，高三学生，一对一辅导',\n        time: '2024-05-01 08:45',\n        views: 68,\n        type: 'find_service',\n        pageType: 'find-service-detail'\n      });\n      break;\n      \n    case 'dating':\n      // 婚恋交友示例数据\n        serviceList.value.push({\n          id: 'sample-dating-1',\n          category: '男士征婚',\n          content: '35岁，身高178cm，本科学历，公务员，诚心寻找一位温柔贤惠的女士共度余生',\n          time: '2024-05-01 09:30',\n          views: 156,\n          type: 'dating',\n          pageType: 'dating-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-dating-2',\n          category: '女士征婚',\n          content: '32岁，身高165cm，硕士学历，教师，希望找一位成熟稳重、有责任心的男士',\n          time: '2024-05-01 10:15',\n          views: 188,\n          type: 'dating',\n          pageType: 'dating-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-dating-3',\n          category: '相亲活动',\n          content: '本周六下午2点，城区文化广场举办大型相亲交友活动，单身男女免费参加',\n          time: '2024-05-01 11:20',\n          views: 235,\n          images: ['/static/images/banner/banner-3.jpg'],\n          type: 'dating',\n          pageType: 'dating-detail'\n        });\n      break;\n      \n    case 'merchant_activity':\n      // 商家活动示例数据\n        serviceList.value.push({\n          id: 'sample-merchant-1',\n          category: '促销活动',\n          content: '五一大促销，全场商品5折起，部分商品买一送一，活动时间5月1日至5月5日',\n          time: '2024-05-01 08:30',\n          views: 245,\n          images: ['/static/images/banner/banner-1.png'],\n          type: 'merchant_activity',\n          pageType: 'merchant-activity-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-merchant-2',\n          category: '新店开业',\n          content: '新店开业大酬宾，前100名顾客免费赠送精美礼品一份，消费满100元送50元代金券',\n          time: '2024-05-01 09:45',\n          views: 198,\n          images: ['/static/images/banner/banner-2.png'],\n          type: 'merchant_activity',\n          pageType: 'merchant-activity-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-merchant-3',\n          category: '满减活动',\n          content: '本周末满减活动，满100减30，满200减80，满300减150，多买多省',\n          time: '2024-05-01 10:30',\n          views: 176,\n          type: 'merchant_activity',\n          pageType: 'merchant-activity-detail'\n        });\n      break;\n      \n    case 'job':\n      // 招聘信息示例数据\n        serviceList.value.push({\n          id: 'sample-job-1',\n          category: '销售',\n          content: '招聘销售人员3名，底薪3000+提成，要求有销售经验',\n          time: '2024-05-01 09:15',\n          views: 210,\n          type: 'hire',\n          pageType: 'job-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-job-2',\n          category: '服务员',\n          content: '餐厅招聘服务员2名，有经验优先，包吃住',\n          time: '2024-05-01 11:30',\n          views: 150,\n          type: 'hire',\n          pageType: 'job-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-job-3',\n          category: '司机',\n          content: '招聘专职司机，C1驾照，有3年以上驾龄，底薪4500',\n          time: '2024-05-01 08:45',\n          views: 180,\n          type: 'hire',\n          pageType: 'job-detail'\n        });\n      break;\n      \n    case 'car':\n      // 车辆服务示例数据\n        serviceList.value.push({\n          id: 'sample-car-1',\n          category: '汽车维修',\n          content: '专业汽车维修，各种故障检测维修，价格合理',\n          time: '2024-05-01 10:15',\n          views: 95,\n          type: 'car_service',\n        pageType: 'car-service-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-car-2',\n          category: '汽车保养',\n          content: '汽车保养套餐，包含机油机滤更换，全车检查等服务',\n          time: '2024-05-01 09:30',\n          views: 120,\n          type: 'car_service',\n        pageType: 'car-service-detail'\n        });\n      break;\n      \n    case 'second_hand':\n      // 二手闲置示例数据\n        serviceList.value.push({\n          id: 'sample-second-hand-1',\n          category: '手机数码',\n          content: '出售二手iPhone 13，128G，成色95新，有发票保修',\n          time: '2024-05-01 14:20',\n          views: 230,\n          images: ['/static/images/banner/banner-3.png'],\n          type: 'second_hand',\n          pageType: 'info-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-second-hand-2',\n          category: '家具家居',\n          content: '搬家出售九成新沙发一套，茶几，餐桌等家具',\n          time: '2024-05-01 13:15',\n          views: 185,\n          type: 'second_hand',\n          pageType: 'info-detail'\n        });\n      break;\n      \n    case 'education':\n      // 教育培训示例数据\n        serviceList.value.push({\n          id: 'sample-education-1',\n          category: '小学辅导',\n          content: '小学一对一辅导，语数英全科，有丰富教学经验',\n          time: '2024-05-01 16:30',\n          views: 145,\n          type: 'education',\n          pageType: 'info-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-education-2',\n          category: '英语培训',\n          content: '英语口语培训，外教一对一，提高口语能力',\n          time: '2024-05-01 15:20',\n          views: 168,\n          type: 'education',\n          pageType: 'info-detail'\n        });\n      break;\n      \n    case 'carpool':\n      // 磁州拼车示例数据\n        serviceList.value.push({\n          id: 'sample-carpool-1',\n          category: '上下班拼车',\n          content: '工作日上下班拼车，磁州到市区，早7点发车，晚6点返回',\n          time: '2024-05-01 18:30',\n          views: 112,\n          type: 'carpool',\n          pageType: 'info-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-carpool-2',\n          category: '城际拼车',\n          content: '周末磁州到石家庄拼车，周六早出发，周日晚返回',\n          time: '2024-05-01 17:15',\n          views: 135,\n          type: 'carpool',\n          pageType: 'info-detail'\n        });\n      break;\n      \n    case 'house_rent':\n      // 房屋出租示例数据\n      serviceList.value.push({\n        id: 'sample-house-rent-1',\n        category: '整租',\n        content: '城区两室一厅出租，精装修，家电齐全，拎包入住',\n        time: '2024-05-01 14:30',\n        views: 220,\n        images: ['/static/images/banner/banner-2.png'],\n        type: 'house_rent',\n        pageType: 'info-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-house-rent-2',\n        category: '合租',\n        content: '城区三室一厅合租，主卧带阳台，家电齐全，拎包入住',\n        time: '2024-05-01 13:20',\n        views: 175,\n        type: 'house_rent',\n        pageType: 'info-detail'\n      });\n      break;\n      \n    case 'house_sell':\n      // 房屋出售示例数据\n      serviceList.value.push({\n        id: 'sample-house-sell-1',\n        category: '商品房',\n        content: '城区新小区三室两厅出售，110平米，精装修，南北通透',\n        time: '2024-05-01 15:30',\n        views: 245,\n        images: ['/static/images/banner/banner-3.png'],\n        type: 'house_sell',\n        pageType: 'info-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-house-sell-2',\n        category: '二手房',\n        content: '老城区两室一厅出售，70平米，简装修，交通便利',\n        time: '2024-05-01 14:15',\n        views: 198,\n        type: 'house_sell',\n        pageType: 'info-detail'\n      });\n      break;\n      \n    case 'second_car':\n      // 二手车辆示例数据\n      serviceList.value.push({\n        id: 'sample-second-car-1',\n        category: '轿车',\n        content: '2020年大众朗逸，1.5L自动挡，行驶3万公里，车况良好',\n        time: '2024-05-01 11:30',\n        views: 210,\n        images: ['/static/images/banner/banner-1.png'],\n        type: 'used_car',\n        pageType: 'info-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-second-car-2',\n        category: 'SUV',\n        content: '2019年本田CR-V，2.0L自动挡，行驶5万公里，车况良好',\n        time: '2024-05-01 10:15',\n        views: 185,\n        type: 'used_car',\n        pageType: 'info-detail'\n      });\n      break;\n      \n    case 'pet':\n      // 宠物信息示例数据\n      serviceList.value.push({\n        id: 'sample-pet-1',\n        category: '狗狗',\n        content: '出售纯种金毛幼犬，2个月大，已打疫苗，健康活泼',\n        time: '2024-05-01 13:30',\n        views: 165,\n        images: ['/static/images/banner/banner-3.png'],\n        type: 'pet',\n        pageType: 'info-detail'\n      });\n      \n      serviceList.value.push({\n        id: 'sample-pet-2',\n        category: '猫咪',\n        content: '出售英短蓝猫，3个月大，已打疫苗，粘人可爱',\n        time: '2024-05-01 12:15',\n        views: 145,\n        type: 'pet',\n        pageType: 'info-detail'\n      });\n      break;\n      \n    case 'other':\n      // 其他服务示例数据\n        serviceList.value.push({\n          id: 'sample-other-1',\n          category: '法律服务',\n          content: '专业律师咨询服务，合同审核，法律纠纷解决',\n          time: '2024-05-01 11:45',\n          views: 78,\n          type: 'other_service',\n          pageType: 'info-detail'\n        });\n      \n        serviceList.value.push({\n          id: 'sample-other-2',\n          category: '设计服务',\n          content: '平面设计，logo设计，海报设计，价格合理',\n          time: '2024-05-01 10:50',\n          views: 92,\n          type: 'other_service',\n          pageType: 'info-detail'\n      });\n      break;\n      \n    default:\n      // 默认示例数据\n      serviceList.value.push({\n        id: 'sample-default-1',\n        category: '服务信息',\n        content: '这是一条示例服务信息',\n        time: '2024-05-01 12:00',\n        views: 50,\n        type: serviceType.value,\n        pageType: 'info-detail'\n      });\n  }\n  \n  // 随机添加更多示例数据\n  hasMore.value = serviceList.value.length >= 5;\n};\n\n// 下拉刷新\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  page.value = 1;\n  serviceList.value = [];\n  hasMore.value = true;\n  loadServiceList();\n};\n\n// 加载更多\nconst loadMore = () => {\n  if (hasMore.value) {\n    page.value++;\n    loadServiceList();\n  }\n};\n\n// 导航到详情页\nconst navigateToDetail = (item) => {\n  let url = '';\n  \n  // 根据不同的类型跳转到不同的详情页\n  if (item.pageType) {\n    // 如果是到家服务，确保传递正确的服务类型参数\n    if (item.type === 'home_service' && item.serviceType) {\n      url = `/pages/publish/${item.pageType}?id=${item.id}&type=${item.serviceType}`;\n    } else {\n      url = `/pages/publish/${item.pageType}?id=${item.id}`;\n    }\n  } else {\n    // 根据当前服务类型确定详情页\n    switch (serviceType.value) {\n      case 'home':\n        // 获取当前选中的子分类\n    let subType = 'home_cleaning'; // 默认为家政服务\n    if (currentTab.value > 0 && subCategories.value[currentTab.value]) {\n      subType = subCategories.value[currentTab.value].type;\n    }\n    url = `/pages/publish/home-service-detail?id=${item.id}&type=${item.serviceType || subType}`;\n        break;\n        \n      case 'find':\n    url = `/pages/publish/find-service-detail?id=${item.id}`;\n        break;\n        \n      case 'business':\n        url = `/pages/publish/business-transfer-detail?id=${item.id}`;\n        break;\n        \n      case 'job':\n    url = `/pages/publish/job-detail?id=${item.id}`;\n        break;\n        \n      case 'resume':\n        url = `/pages/publish/job-seeking-detail?id=${item.id}`;\n        break;\n        \n      case 'dating':\n    url = `/pages/publish/dating-detail?id=${item.id}`;\n        break;\n        \n      case 'merchant_activity':\n    url = `/pages/publish/merchant-activity-detail?id=${item.id}`;\n        break;\n        \n      case 'car':\n        url = `/pages/publish/car-service-detail?id=${item.id}`;\n        break;\n        \n      case 'second_hand':\n        url = `/pages/publish/second-hand-detail?id=${item.id}`;\n        break;\n        \n      case 'carpool':\n    url = `/pages/publish/carpool-detail?id=${item.id}`;\n        break;\n        \n      case 'education':\n    url = `/pages/publish/education-detail?id=${item.id}`;\n        break;\n        \n      default:\n        url = `/pages/publish/info-detail?id=${item.id}&type=${serviceType.value}`;\n    }\n  }\n  \n  console.log('跳转到详情页：', url);\n  \n  uni.navigateTo({\n    url\n  });\n};\n\n// 导航到发布页面\nconst navigateToPublish = () => {\n  if (serviceType.value === 'home') {\n    // 到家服务特殊处理，先选择子分类\n    uni.navigateTo({\n      url: '/pages/publish/service-category'\n    });\n  } else {\n    // 其他服务直接跳转到对应发布页\n    const typeMap = {\n      'find': 'find_service',\n      'business': 'business_transfer',\n      'job': 'hire',\n      'resume': 'job_wanted',\n      'house_rent': 'house_rent',\n      'house_sell': 'house_sell',\n      'second_car': 'used_car',\n      'pet': 'pet',\n      'dating': 'dating',\n      'merchant_activity': 'merchant_activity',\n      'car': 'car_service',\n      'second_hand': 'second_hand',\n      'carpool': 'carpool',\n      'education': 'education',\n      'other': 'other_service'\n    };\n    \n    const publishType = typeMap[serviceType.value] || 'other_service';\n    const publishName = serviceTitle.value;\n    \n    uni.navigateTo({\n      url: `/pages/publish/detail?type=${publishType}&name=${encodeURIComponent(publishName)}&categoryType=${publishType}&categoryName=${encodeURIComponent(publishName)}`\n    });\n  }\n};\n\n// 返回上一页\nconst navigateBack = () => {\n  uni.navigateBack();\n};\n\n// 打开筛选页面\nconst openFilter = () => {\n  // 添加触感反馈\n  uni.vibrateShort();\n  \n  // 打开筛选页面\n  uni.navigateTo({\n    url: `/subPackages/service/pages/filter?type=${serviceType.value}&title=${encodeURIComponent(serviceTitle.value)}&active=${serviceType.value}`\n  });\n};\n\n// 切换区域筛选\nconst toggleAreaFilter = () => {\n  showAreaFilter.value = !showAreaFilter.value;\n  // 关闭其他筛选\n  showSortFilter.value = false;\n  \n  if (showAreaFilter.value) {\n    // 动态获取按钮底部位置\n    nextTick(() => {\n      const query = uni.createSelectorQuery();\n      query.select('.filter-container').boundingClientRect(container => {\n        if (container) {\n          // 下拉菜单应该紧贴筛选栏底部\n          areaDropdownTop.value = container.height + container.top;\n        }\n      }).exec();\n    });\n  }\n};\n\n// 选择区域\nconst selectArea = (area) => {\n  if (selectedArea.value !== area) {\n    selectedArea.value = area;\n    // 重置列表数据\n    page.value = 1;\n    serviceList.value = [];\n    hasMore.value = true;\n    loadServiceList();\n  }\n  showAreaFilter.value = false;\n};\n\n// 关闭所有筛选\nconst closeAllFilters = () => {\n  showAreaFilter.value = false;\n  showSortFilter.value = false;\n};\n\n// 应用关键词搜索\nconst applyKeywordSearch = () => {\n  // 实现关键词搜索逻辑\n  console.log('应用关键词搜索：', searchKeyword.value);\n};\n\n// 重置筛选\nconst resetFilters = () => {\n  showAreaFilter.value = false;\n  showSortFilter.value = false;\n  page.value = 1;\n  serviceList.value = [];\n  hasMore.value = true;\n  loadServiceList();\n};\n\n// 页面加载\nonMounted(() => {\n  // 获取状态栏高度\n  const sysInfo = uni.getSystemInfoSync();\n  statusBarHeight.value = sysInfo.statusBarHeight;\n  \n  // 获取页面参数\n  const pages = getCurrentPages();\n  const currentPage = pages[pages.length - 1];\n  const options = currentPage.options || {};\n  \n  // 获取服务类型\n  if (options.type) {\n    serviceType.value = options.type;\n    setServiceTitle(options.type);\n    \n    // 一级分类显示\n    showSubCategories.value = true;\n    \n    // 初始化一级分类列表 - 这里是所有可用的服务类型\n    subCategories.value = [\n      { name: '到家服务', type: 'home' },\n      { name: '寻找服务', type: 'find' },\n      { name: '生意转让', type: 'business' },\n      { name: '招聘信息', type: 'job' },\n      { name: '求职信息', type: 'resume' },\n      { name: '房屋出租', type: 'house_rent' },\n      { name: '房屋出售', type: 'house_sell' },\n      { name: '二手车辆', type: 'second_car' },\n      { name: '宠物信息', type: 'pet' },\n      { name: '商家活动', type: 'merchant_activity' },\n      { name: '婚恋交友', type: 'dating' },\n      { name: '车辆服务', type: 'car' },\n      { name: '二手闲置', type: 'second_hand' },\n      { name: '磁州拼车', type: 'carpool' },\n      { name: '教育培训', type: 'education' },\n      { name: '其他服务', type: 'other' }\n    ];\n    \n    // 设置当前选中的一级分类\n    const currentTypeIndex = subCategories.value.findIndex(item => item.type === options.type);\n    if (currentTypeIndex > -1) {\n      currentTab.value = currentTypeIndex;\n    }\n    \n    // 根据当前选中的一级分类加载对应的二级分类\n    loadSecondLevelCategories(options.type);\n    \n    // 根据服务类型设置搜索框显示\n    switch(options.type) {\n      case 'home':\n      case 'find':\n      case 'business':\n      case 'job':\n      case 'resume':\n      case 'dating':\n      case 'merchant_activity':\n        showSearchBox.value = true;\n        break;\n      default:\n        showSearchBox.value = false;\n    }\n  }\n\n  // 加载服务列表数据\n  loadServiceList();\n});\n\n// 加载二级分类\nconst loadSecondLevelCategories = (type) => {\n  // 根据一级分类类型加载对应的二级分类\n  switch(type) {\n    case 'home':\n      // 到家服务二级分类\n      subSubCategories.value = [\n        { name: '全部', type: 'all' },\n        { name: '家政服务', type: 'home_cleaning' },\n        { name: '维修改造', type: 'repair' },\n        { name: '上门安装', type: 'installation' },\n        { name: '开锁换锁', type: 'locksmith' },\n        { name: '搬家拉货', type: 'moving' },\n        { name: '上门美容', type: 'beauty' },\n        { name: '上门家教', type: 'tutor' },\n        { name: '宠物服务', type: 'pet_service' },\n        { name: '上门疏通', type: 'plumbing' },\n        { name: '其他类型', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n      break;\n      \n    case 'find':\n      // 寻找服务二级分类\n      subSubCategories.value = [\n        { name: '全部服务', type: 'all' },\n        { name: '家政服务', type: 'home_service' },\n        { name: '维修服务', type: 'repair' },\n        { name: '安装服务', type: 'installation' },\n        { name: '搬家服务', type: 'moving' },\n        { name: '美容服务', type: 'beauty' },\n        { name: '教育服务', type: 'education' },\n        { name: '其他服务', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n      break;\n      \n    case 'business':\n      // 生意转让二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '餐饮店铺', type: 'restaurant' },\n        { name: '零售店铺', type: 'retail' },\n        { name: '美容美发', type: 'beauty_salon' },\n        { name: '服装店铺', type: 'clothing' },\n        { name: '便利超市', type: 'convenience' },\n        { name: '其他店铺', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n      break;\n      \n      case 'job':\n      // 招聘信息二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '销售', type: 'sales' },\n        { name: '服务员', type: 'waiter' },\n        { name: '技工', type: 'technician' },\n        { name: '司机', type: 'driver' },\n        { name: '厨师', type: 'chef' },\n        { name: '会计', type: 'accountant' },\n        { name: '文员', type: 'clerk' },\n        { name: '保安', type: 'security' },\n        { name: '其他', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n        break;\n      \n      case 'resume':\n      // 求职信息二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '销售类', type: 'sales' },\n        { name: '技术类', type: 'tech' },\n        { name: '服务类', type: 'service' },\n        { name: '行政类', type: 'admin' },\n        { name: '教育类', type: 'education' },\n        { name: '其他类', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n        break;\n      \n      case 'dating':\n      // 婚恋交友二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '男士征婚', type: 'male_dating' },\n        { name: '女士征婚', type: 'female_dating' },\n        { name: '恋爱交友', type: 'friendship' },\n        { name: '相亲活动', type: 'matchmaking' },\n        { name: '婚恋平台', type: 'dating_platform' },\n        { name: '其他', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n        break;\n      \n      case 'merchant_activity':\n      // 商家活动二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '促销活动', type: 'promotion' },\n        { name: '新店开业', type: 'opening' },\n        { name: '优惠券', type: 'coupon' },\n        { name: '满减活动', type: 'discount' },\n        { name: '限时特价', type: 'special_price' },\n        { name: '积分活动', type: 'points' },\n        { name: '其他活动', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n        break;\n      \n      case 'car':\n      // 车辆服务二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '汽车维修', type: 'repair' },\n        { name: '汽车保养', type: 'maintenance' },\n        { name: '汽车美容', type: 'beauty' },\n        { name: '汽车改装', type: 'modification' },\n        { name: '汽车救援', type: 'rescue' },\n        { name: '其他', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n        break;\n      \n      case 'second_hand':\n      // 二手闲置二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '手机数码', type: 'digital' },\n        { name: '家用电器', type: 'appliance' },\n        { name: '家具家居', type: 'furniture' },\n        { name: '服装鞋帽', type: 'clothing' },\n        { name: '母婴用品', type: 'baby' },\n        { name: '运动户外', type: 'sports' },\n        { name: '图书音像', type: 'books' },\n        { name: '其他', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n        break;\n      \n      case 'carpool':\n      // 磁州拼车二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '上下班拼车', type: 'commute' },\n        { name: '城际拼车', type: 'intercity' },\n        { name: '回乡拼车', type: 'hometown' },\n        { name: '其他', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n        break;\n      \n      case 'education':\n      // 教育培训二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '学前教育', type: 'preschool' },\n        { name: '小学辅导', type: 'primary' },\n        { name: '中学辅导', type: 'secondary' },\n        { name: '高考辅导', type: 'college_entrance' },\n        { name: '英语培训', type: 'english' },\n        { name: '音乐培训', type: 'music' },\n        { name: '美术培训', type: 'art' },\n        { name: '体育培训', type: 'sports' },\n        { name: '职业技能', type: 'vocational' },\n        { name: '其他', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n        break;\n      \n      case 'other':\n      // 其他服务二级分类\n      subSubCategories.value = [\n        { name: '全部分类', type: 'all' },\n        { name: '法律服务', type: 'legal' },\n        { name: '金融服务', type: 'financial' },\n        { name: '设计服务', type: 'design' },\n        { name: '网络服务', type: 'internet' },\n        { name: '翻译服务', type: 'translation' },\n        { name: '婚庆服务', type: 'wedding' },\n        { name: '摄影服务', type: 'photography' },\n        { name: '其他', type: 'other' }\n      ];\n      showSubSubCategories.value = true;\n        break;\n      \n      default:\n      // 默认不显示二级分类\n      subSubCategories.value = [];\n      showSubSubCategories.value = false;\n  }\n  \n  // 重置二级分类选中状态\n  currentSubTab.value = 0;\n};\n</script>\n\n<style lang=\"scss\" scoped>\n/* 全局样式 */\n.service-list-container {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f6f8fa;\n  \n  &.premium-style {\n    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n  }\n}\n\n/* 固定头部区域 */\n.custom-navbar,\n.search-container,\n.category-tabs,\n.subcategory-tabs,\n.filter-container {\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  background-color: #fff;\n}\n\n/* 自定义导航栏 - 最顶层 */\n.custom-navbar {\n  display: flex;\n  align-items: center;\n  height: 44px;\n  background: linear-gradient(120deg, #0070f3, #00a1ff);\n  padding: 0 16px;\n  position: sticky;\n  top: 0;\n  z-index: 105;\n  box-shadow: 0 1px 12px rgba(0, 112, 243, 0.18);\n}\n\n/* 搜索框 - 第二层 */\n.search-container {\n  padding: 12px 16px 16px;\n  background: #fff;\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n  margin-bottom: 8px;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n  position: sticky;\n  top: 44px;\n  z-index: 104;\n}\n\n/* 一级分类标签栏 - 第三层 */\n.category-tabs {\n  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));\n  padding: 14px 0 12px;\n  white-space: nowrap;\n  border-radius: 16px;\n  margin: 0 12px 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\n  position: sticky;\n  top: calc(44px + 68px); /* 导航栏高度 + 搜索框高度 */\n  z-index: 103;\n}\n\n/* 二级分类标签栏 - 第四层 */\n.subcategory-tabs {\n  background: rgba(255, 255, 255, 0.95);\n  padding: 10px 0;\n  white-space: nowrap;\n  border-radius: 16px;\n  margin: 0 12px 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\n  position: sticky;\n  top: calc(44px + 68px + 60px); /* 导航栏高度 + 搜索框高度 + 一级分类高度 */\n  z-index: 102;\n}\n\n/* 筛选栏 - 第五层 */\n.filter-container {\n  display: flex;\n  height: 54px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 16px;\n  position: sticky;\n  top: calc(44px + 68px + 60px + 54px); /* 导航栏高度 + 搜索框高度 + 一级分类高度 + 二级分类高度 */\n  justify-content: center;\n  padding: 0;\n  z-index: 101;\n  margin: 0 12px 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\n}\n\n/* 列表内容区域 - 需要添加足够的上边距以避免被固定头部遮挡 */\n.service-scroll {\n  flex: 1;\n  box-sizing: border-box;\n  padding: 0 12px;\n  margin-top: 12px; /* 添加一些顶部边距 */\n}\n\n.back-btn {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  position: relative;\n  z-index: 2;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-top: 2px solid #fff;\n  border-left: 2px solid #fff;\n  transform: rotate(-45deg);\n  transition: transform 0.2s ease;\n}\n\n.back-btn:active .back-icon {\n  transform: rotate(-45deg) scale(0.9);\n}\n\n.navbar-title {\n  position: absolute;\n  left: 0;\n  right: 0;\n  text-align: center;\n  color: #fff;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: -0.2px;\n}\n\n.navbar-right {\n  width: 36px;\n  position: relative;\n  z-index: 2;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  background-color: #f5f7fa;\n  border-radius: 12px;\n  padding: 0 12px;\n  height: 40px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);\n}\n\n.search-icon {\n  width: 18px;\n  height: 18px;\n  margin-right: 8px;\n  opacity: 0.6;\n}\n\n.search-input {\n  flex: 1;\n  height: 40px;\n  font-size: 15px;\n  color: #333;\n  background: transparent;\n}\n\n.search-cancel {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  color: #999;\n  border-radius: 50%;\n  background-color: #f0f0f0;\n}\n\n.tab-item {\n  display: inline-block;\n  padding: 8px 20px;\n  margin: 0 5px;\n  font-size: 14px;\n  color: #555;\n  border-radius: 20px;\n  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);\n  position: relative;\n}\n\n.tab-item:first-child {\n  margin-left: 16px;\n}\n\n.tab-item:last-child {\n  margin-right: 16px;\n}\n\n.tab-item.active {\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  color: #fff;\n  font-weight: 500;\n  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);\n}\n\n.subtab-item {\n  display: inline-block;\n  padding: 6px 16px;\n  margin: 0 5px;\n  font-size: 13px;\n  color: #666;\n  border-radius: 16px;\n  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);\n  background-color: #f1f2f6;\n}\n\n.subtab-item:first-child {\n  margin-left: 16px;\n}\n\n.subtab-item:last-child {\n  margin-right: 16px;\n}\n\n.subtab-item.active {\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  color: #fff;\n  font-weight: 500;\n  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);\n}\n\n.filter-wrapper {\n  display: flex;\n  width: 100%;\n  height: 54px;\n  border-radius: 16px;\n  overflow: hidden;\n}\n\n.filter-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  height: 100%;\n  font-size: 15px;\n  color: #333;\n  font-weight: 500;\n  transition: background-color 0.2s ease;\n}\n\n.filter-item:active {\n  background-color: rgba(0, 0, 0, 0.02);\n}\n\n.filter-item:not(:last-child)::after {\n  content: '';\n  position: absolute;\n  right: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 1px;\n  height: 24px;\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.active-filter {\n  color: #0070f3;\n  font-weight: 600;\n}\n\n.filter-arrow {\n  width: 0;\n  height: 0;\n  border-left: 5px solid transparent;\n  border-right: 5px solid transparent;\n  border-top: 5px solid #999;\n  margin-left: 8px;\n  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);\n}\n\n.arrow-up {\n  transform: rotate(180deg);\n  border-top-color: #0070f3;\n}\n\n/* 高级筛选下拉菜单 */\n.filter-dropdown {\n  position: absolute;\n  background-color: rgba(255, 255, 255, 0.98);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n  z-index: 99;\n  max-height: 60vh;\n  animation: dropDown 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);\n  border-radius: 16px;\n  overflow: hidden;\n  margin-top: 8px;\n}\n\n@keyframes dropDown {\n  from { opacity: 0; transform: translateY(-12px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n.area-dropdown {\n  left: 16px;\n  width: calc(50% - 24px);\n}\n\n.sort-dropdown {\n  right: 16px;\n  width: calc(50% - 24px);\n}\n\n.dropdown-scroll {\n  max-height: 50vh;\n}\n\n.dropdown-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  font-size: 15px;\n  color: #333;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.03);\n  transition: all 0.2s;\n}\n\n.dropdown-item:active {\n  background-color: rgba(0, 0, 0, 0.02);\n}\n\n.dropdown-item.active-item {\n  color: #0070f3;\n  font-weight: 600;\n  background-color: rgba(0, 112, 243, 0.05);\n}\n\n.dropdown-item-text {\n  flex: 1;\n}\n\n.dropdown-item-check {\n  color: #0070f3;\n  font-weight: bold;\n  margin-left: 8px;\n}\n\n/* 遮罩层 */\n.filter-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.4);\n  z-index: 90;\n  backdrop-filter: blur(2px);\n  animation: fadeIn 0.3s ease;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n.service-list {\n  padding-bottom: 16px;\n}\n\n.service-item {\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));\n  border-radius: 18px;\n  margin-bottom: 16px;\n  overflow: hidden;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);\n  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);\n  border: 1px solid rgba(255, 255, 255, 0.8);\n}\n\n.service-item-hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);\n}\n\n.service-content {\n  padding: 18px;\n}\n\n.service-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 16px;\n}\n\n.service-header-left {\n  display: flex;\n  align-items: center;\n}\n\n.service-meta-right {\n  display: flex;\n  align-items: center;\n}\n\n.service-tag {\n  background: linear-gradient(135deg, rgba(0, 112, 243, 0.1), rgba(0, 161, 255, 0.15));\n  color: #0070f3;\n  font-size: 13px;\n  padding: 4px 12px;\n  border-radius: 8px;\n  font-weight: 600;\n  letter-spacing: -0.2px;\n}\n\n.service-subcategory {\n  background-color: rgba(0, 112, 243, 0.06);\n  color: #0070f3;\n  font-size: 13px;\n  padding: 4px 12px;\n  border-radius: 8px;\n  margin-left: 8px;\n  letter-spacing: -0.2px;\n}\n\n.service-area {\n  background-color: #f1f2f6;\n  color: #666;\n  font-size: 13px;\n  padding: 4px 12px;\n  border-radius: 8px;\n}\n\n.service-main {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.service-title-wrapper {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.service-title {\n  font-size: 17px;\n  color: #222;\n  line-height: 1.5;\n  font-weight: 500;\n  letter-spacing: -0.2px;\n  flex: 1;\n}\n\n.service-price {\n  color: #ff3b30;\n  font-weight: 600;\n  font-size: 17px;\n  margin-left: 12px;\n}\n\n.service-images {\n  display: flex;\n  gap: 8px;\n  margin: 4px 0;\n}\n\n.service-image {\n  width: 32%;\n  height: 100px;\n  border-radius: 12px;\n  background-color: #f5f5f5;\n  object-fit: cover;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.service-image.single-image {\n  width: 100%;\n  height: 160px;\n}\n\n.service-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 4px;\n}\n\n.service-meta {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.meta-tag {\n  background-color: rgba(0, 0, 0, 0.04);\n  padding: 4px 12px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n}\n\n.meta-views {\n  font-size: 13px;\n  color: #666;\n}\n\n.meta-time {\n  font-size: 13px;\n  color: #999;\n}\n\n.service-actions {\n  display: flex;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8px 20px;\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  border-radius: 24px;\n  font-size: 14px;\n  color: #fff;\n  font-weight: 500;\n  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);\n  transition: all 0.2s ease;\n}\n\n.action-btn:active {\n  transform: scale(0.96);\n  box-shadow: 0 2px 8px rgba(0, 112, 243, 0.2);\n}\n\n.contact-btn {\n  letter-spacing: 1px;\n}\n\n/* 高级空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80px 0;\n}\n\n.empty-image {\n  width: 140px;\n  height: 140px;\n  margin-bottom: 24px;\n  opacity: 0.8;\n}\n\n.empty-text {\n  color: #333;\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 8px;\n  letter-spacing: -0.3px;\n}\n\n.empty-subtext {\n  color: #999;\n  font-size: 15px;\n  margin-bottom: 24px;\n}\n\n.empty-btn {\n  padding: 10px 24px;\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  color: #fff;\n  font-size: 15px;\n  font-weight: 500;\n  border-radius: 24px;\n  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);\n}\n\n/* 加载更多状态 */\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px 0;\n  gap: 8px;\n}\n\n.loading-indicator {\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(0, 112, 243, 0.3);\n  border-top: 2px solid #0070f3;\n  border-radius: 50%;\n  animation: spin 0.8s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n  color: #777;\n  font-size: 14px;\n}\n\n.loading-done {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.loading-done-text {\n  color: #999;\n  font-size: 14px;\n}\n\n/* 高级悬浮发布按钮 */\n.publish-btn {\n  position: fixed;\n  right: 20px;\n  bottom: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 24px 0 22px;\n  height: 56px;\n  border-radius: 28px;\n  background: linear-gradient(135deg, #0070f3, #00a1ff);\n  box-shadow: 0 6px 16px rgba(0, 112, 243, 0.3);\n  z-index: 90;\n  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);\n}\n\n.publish-btn-hover {\n  transform: scale(0.95);\n  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.25);\n}\n\n.publish-icon {\n  color: #fff;\n  font-size: 24px;\n  font-weight: 300;\n  line-height: 1;\n  margin-right: 6px;\n}\n\n.publish-text {\n  color: #fff;\n  font-size: 16px;\n  font-weight: 500;\n}\n</style>", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/service/pages/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "nextTick", "uni", "onMounted"], "mappings": ";;;;;;AA6LA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAC/B,UAAM,aAAaA,cAAAA,IAAI,CAAC;AACxB,UAAM,gBAAgBA,cAAAA,IAAI,CAAC;AAC3B,UAAM,SAASA,cAAAA,IAAI,SAAS;AAC5B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,OAAOA,cAAAA,IAAI,CAAC;AAClB,UAAM,QAAQA,cAAAA,IAAI,EAAE;AACpB,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,oBAAoBA,cAAAA,IAAI,KAAK;AACnC,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,uBAAuBA,cAAAA,IAAI,KAAK;AACtC,UAAM,mBAAmBA,cAAAA,IAAI,CAAA,CAAE;AAC/B,UAAM,gBAAgBA,cAAAA,IAAI,KAAK;AAC/B,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,eAAeA,cAAAA,IAAI,MAAM;AAC/B,UAAM,WAAWA,cAAG,IAAC,CAAC,QAAQ,MAAM,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,CAAC;AAC9E,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,WAAWA,cAAG,IAAC,CAAC,MAAM,MAAM,IAAI,CAAC;AACvC,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AACdA,kBAAG,IAAC,IAAI;AACRA,kBAAG,IAAC,IAAI;AAGxB,UAAM,kBAAkB,CAAC,SAAS;AAChC,YAAM,WAAW;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,QACP,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,OAAO;AAAA,QACP,eAAe;AAAA,QACf,WAAW;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,MACb;AAEE,mBAAa,QAAQ,SAAS,IAAI,KAAK;AAAA,IACzC;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,UAAI,WAAW,UAAU,OAAO;AAC9B,mBAAW,QAAQ;AAGnB,cAAM,eAAe,cAAc,MAAM,KAAK,EAAE;AAGhD,oBAAY,QAAQ;AAGpB,wBAAgB,YAAY;AAG5B,kCAA0B,YAAY;AAGtC,aAAK,QAAQ;AACb,oBAAY,QAAQ;AACpB,gBAAQ,QAAQ;AAChB;MACD;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,UAAU;AAC9B,UAAI,cAAc,UAAU,OAAO;AACjC,sBAAc,QAAQ;AAGtB,aAAK,QAAQ;AACb,oBAAY,QAAQ;AACpB,gBAAQ,QAAQ;AAChB;MACD;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,SAAS,WAAW;AACtB,uBAAe,QAAQ,CAAC,eAAe;AAEvC,uBAAe,QAAQ;AAEvB,YAAI,eAAe,OAAO;AAExBC,wBAAAA,WAAS,MAAM;AACb,kBAAM,QAAQC,oBAAI;AAClB,kBAAM,OAAO,mBAAmB,EAAE,mBAAmB,eAAa;AAChE,kBAAI,WAAW;AAEb,gCAAgB,QAAQ,UAAU,SAAS,UAAU;AAAA,cACtD;AAAA,YACX,CAAS,EAAE,KAAI;AAAA,UACf,CAAO;AAAA,QACF;AAAA,MACL,OAAS;AACL,eAAO,QAAQ;AACf,aAAK,QAAQ;AACb,oBAAY,QAAQ;AACpB,gBAAQ,QAAQ;AAChB;MACD;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAE3B,YAAM,eAAe,OAAO;AAG5B,UAAI,SAAS,MAAM;AACjB,eAAO,QAAQ;AAAA,MACnB,OAAS;AACL,eAAO,QAAQ;AAAA,MAChB;AAGD,qBAAe,QAAQ;AAGvB,UAAI,iBAAiB,OAAO,OAAO;AACjC,aAAK,QAAQ;AACb,oBAAY,QAAQ;AACpB,gBAAQ,QAAQ;AAChB;MACD;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAE5BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAGD,YAAM,kBAAkB,qBAAqB,SAAS,cAAc,QAAQ,IACxE,iBAAiB,MAAM,cAAc,KAAK,EAAE,OAC5C;AAGJ,iBAAW,MAAM;AAEf,YAAI;AAEF,gBAAM,mBAAmBA,cAAG,MAAC,eAAe,eAAe,KAAK,CAAA;AAGhE,cAAI,eAAe,CAAA;AAGnB,kBAAQ,YAAY,OAAK;AAAA,YACvB,KAAK;AAEL,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBAC1B;AAAA,cACA,OAAe;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,kBAAkB,KAAK,gBAAgB;AAAA,gBACjE;AAAA,cACW;AACD;AAAA,YAEF,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBAC5B;AAAA,cACA,OAAiB;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,kBAAkB,KAAK,iBAAiB;AAAA,gBACpE;AAAA,cACW;AACD;AAAA,YAEF,KAAK;AAEX,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UAC7B,KAAK,SAAS;AAAA,gBAC5B;AAAA,cACA,OAAS;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UAC7B,KAAK,SAAS,uBAAuB,KAAK,qBAAqB;AAAA,gBAC7E;AAAA,cACG;AACD;AAAA,YAEF,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBACpB;AAAA,cACA,OAAS;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,UAAU,KAAK,gBAAgB;AAAA,gBACnD;AAAA,cACG;AACD;AAAA,YAEF,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBACpB;AAAA,cACA,OAAS;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,gBAAgB,KAAK,mBAAmB;AAAA,gBAC5D;AAAA,cACG;AACD;AAAA,YAEF,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBACpB;AAAA,cACA,OAAS;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,iBAAiB,KAAK,mBAAmB;AAAA,gBAC7D;AAAA,cACG;AACD;AAAA,YAEF,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBACpB;AAAA,cACA,OAAS;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,iBAAiB,KAAK,uBAAuB;AAAA,gBACjE;AAAA,cACG;AACD;AAAA,YAEF,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBACpB;AAAA,cACA,OAAS;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,aAAa,KAAK,gBAAgB;AAAA,gBACtD;AAAA,cACG;AACD;AAAA,YAEF,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBACpB;AAAA,cACA,OAAS;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,eAAe,KAAK,kBAAkB;AAAA,gBAC1D;AAAA,cACG;AACD;AAAA,YAEM,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBAC5B;AAAA,cACA,OAAiB;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,YAAY,KAAK,mBAAmB;AAAA,gBAChE;AAAA,cACW;AACD;AAAA,YAEF,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBAC5B;AAAA,cACA,OAAiB;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,uBAAuB,KAAK,qBAAqB;AAAA,gBAC7E;AAAA,cACG;AACD;AAAA,YAEF,KAAK;AAEH,kBAAI,oBAAoB,OAAO;AAC7B,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS;AAAA,gBACpB;AAAA,cACA,OAAS;AACL,+BAAe,iBAAiB;AAAA,kBAAO,UACrC,KAAK,SAAS,mBAAmB,KAAK,qBAAqB;AAAA,gBACjE;AAAA,cACG;AACD;AAAA,YAEM,KAAK;AAEX,6BAAe,iBAAiB;AAAA,gBAAO,UAC7B,KAAK,SAAS;AAAA,cAC1B;AACU;AAAA,YAEF,KAAK;AAEH,6BAAe,iBAAiB;AAAA,gBAAO,UACrC,KAAK,SAAS;AAAA,cAC1B;AACU;AAAA,YAEF,KAAK;AAEH,6BAAe,iBAAiB;AAAA,gBAAO,UACrC,KAAK,SAAS;AAAA,cAC1B;AACU;AAAA,YAEF,KAAK;AAEH,6BAAe,iBAAiB;AAAA,gBAAO,UACrC,KAAK,SAAS;AAAA,cAC1B;AACU;AAAA,YAEF;AAEE,6BAAe;AAAA,UACzB;AAGA,cAAI,OAAO,UAAU,WAAW;AAE9B,yBAAa,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,eAAe,CAAC,IAAI,IAAI,KAAK,EAAE,eAAe,CAAC,CAAC;AAAA,UACzF,WAAW,OAAO,UAAU,MAAM;AAEhC,yBAAa,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAAA,UAC7D,WAAW,OAAO,UAAU,MAAM;AAEhC,yBAAa,KAAK,CAAC,GAAG,MAAM;AAE1B,kBAAI,EAAE,YAAY,EAAE,UAAU;AAC5B,wBAAQ,EAAE,YAAY,WAAW,EAAE,YAAY;AAAA,cAChD;AAED,kBAAI,EAAE;AAAU,uBAAO;AAEvB,kBAAI,EAAE;AAAU,uBAAO;AAEvB,qBAAO;AAAA,YACX,CAAG;AAAA,UACH;AAGA,gBAAM,SAAS,KAAK,QAAQ,KAAK,MAAM;AACvC,gBAAM,MAAM,KAAK,QAAQ,MAAM;AAC/B,gBAAM,WAAW,aAAa,MAAM,OAAO,GAAG;AAG9C,cAAI,SAAS,SAAS,GAAG;AACvB,wBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAG,QAAQ;AACtD,oBAAQ,QAAQ,aAAa,SAAS,YAAY,MAAM;AAAA,UAC1D,OAAO;AACL,oBAAQ,QAAQ;AAAA,UAClB;AAGA,cAAI,YAAY,MAAM,WAAW,GAAG;AAClC;UACF;AAAA,QACK,SAAQ,GAAG;AACVA,wBAAc,MAAA,MAAA,SAAA,6CAAA,YAAY,CAAC;AAC3B;QACD;AAEDA,sBAAG,MAAC,YAAW;AAGf,YAAI,aAAa,OAAO;AACtB,uBAAa,QAAQ;AAAA,QACtB;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,gBAAgB,MAAM;AAE1B,cAAQ,YAAY,OAAK;AAAA,QACvB,KAAK;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AACH;AAAA,QAEF,KAAK;AAEH,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,aAAa;AAAA,YACb,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,aAAa;AAAA,YACb,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AACD;AAAA,QAEF,KAAK;AAEH,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AACD;AAAA,QAEF,KAAK;AAEH,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,aAAa;AAAA,YACb,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,aAAa;AAAA,YACb,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,aAAa;AAAA,YACb,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AACD;AAAA,QAEF,KAAK;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AACH;AAAA,QAEF,KAAK;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AACH;AAAA,QAEF,KAAK;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AACH;AAAA,QAEF,KAAK;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACR,UAAU;AAAA,UAClB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACR,UAAU;AAAA,UAClB,CAAS;AACH;AAAA,QAEF,KAAK;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AACH;AAAA,QAEF,KAAK;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AACH;AAAA,QAEF,KAAK;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AACH;AAAA,QAEF,KAAK;AAEH,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AACD;AAAA,QAEF,KAAK;AAEH,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AACD;AAAA,QAEF,KAAK;AAEH,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AACD;AAAA,QAEF,KAAK;AAEH,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ,CAAC,oCAAoC;AAAA,YAC7C,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AACD;AAAA,QAEF,KAAK;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAS;AAED,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACpB,CAAO;AACD;AAAA,QAEF;AAEE,sBAAY,MAAM,KAAK;AAAA,YACrB,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,YAAY;AAAA,YAClB,UAAU;AAAA,UAClB,CAAO;AAAA,MACJ;AAGD,cAAQ,QAAQ,YAAY,MAAM,UAAU;AAAA,IAC9C;AAGA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB,WAAK,QAAQ;AACb,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAChB;IACF;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,QAAQ,OAAO;AACjB,aAAK;AACL;MACD;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,SAAS;AACjC,UAAI,MAAM;AAGV,UAAI,KAAK,UAAU;AAEjB,YAAI,KAAK,SAAS,kBAAkB,KAAK,aAAa;AACpD,gBAAM,kBAAkB,KAAK,QAAQ,OAAO,KAAK,EAAE,SAAS,KAAK,WAAW;AAAA,QAClF,OAAW;AACL,gBAAM,kBAAkB,KAAK,QAAQ,OAAO,KAAK,EAAE;AAAA,QACpD;AAAA,MACL,OAAS;AAEL,gBAAQ,YAAY,OAAK;AAAA,UACvB,KAAK;AAEP,gBAAI,UAAU;AACd,gBAAI,WAAW,QAAQ,KAAK,cAAc,MAAM,WAAW,KAAK,GAAG;AACjE,wBAAU,cAAc,MAAM,WAAW,KAAK,EAAE;AAAA,YACjD;AACD,kBAAM,yCAAyC,KAAK,EAAE,SAAS,KAAK,eAAe,OAAO;AACtF;AAAA,UAEF,KAAK;AACP,kBAAM,yCAAyC,KAAK,EAAE;AAClD;AAAA,UAEF,KAAK;AACH,kBAAM,8CAA8C,KAAK,EAAE;AAC3D;AAAA,UAEF,KAAK;AACP,kBAAM,gCAAgC,KAAK,EAAE;AACzC;AAAA,UAEF,KAAK;AACH,kBAAM,wCAAwC,KAAK,EAAE;AACrD;AAAA,UAEF,KAAK;AACP,kBAAM,mCAAmC,KAAK,EAAE;AAC5C;AAAA,UAEF,KAAK;AACP,kBAAM,8CAA8C,KAAK,EAAE;AACvD;AAAA,UAEF,KAAK;AACH,kBAAM,wCAAwC,KAAK,EAAE;AACrD;AAAA,UAEF,KAAK;AACH,kBAAM,wCAAwC,KAAK,EAAE;AACrD;AAAA,UAEF,KAAK;AACP,kBAAM,oCAAoC,KAAK,EAAE;AAC7C;AAAA,UAEF,KAAK;AACP,kBAAM,sCAAsC,KAAK,EAAE;AAC/C;AAAA,UAEF;AACE,kBAAM,iCAAiC,KAAK,EAAE,SAAS,YAAY,KAAK;AAAA,QAC3E;AAAA,MACF;AAEDA,oBAAY,MAAA,MAAA,OAAA,8CAAA,WAAW,GAAG;AAE1BA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACJ,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9B,UAAI,YAAY,UAAU,QAAQ;AAEhCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACX,CAAK;AAAA,MACL,OAAS;AAEL,cAAM,UAAU;AAAA,UACd,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,OAAO;AAAA,UACP,UAAU;AAAA,UACV,qBAAqB;AAAA,UACrB,OAAO;AAAA,UACP,eAAe;AAAA,UACf,WAAW;AAAA,UACX,aAAa;AAAA,UACb,SAAS;AAAA,QACf;AAEI,cAAM,cAAc,QAAQ,YAAY,KAAK,KAAK;AAClD,cAAM,cAAc,aAAa;AAEjCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,8BAA8B,WAAW,SAAS,mBAAmB,WAAW,CAAC,iBAAiB,WAAW,iBAAiB,mBAAmB,WAAW,CAAC;AAAA,QACxK,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzBA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAcA,UAAM,mBAAmB,MAAM;AAC7B,qBAAe,QAAQ,CAAC,eAAe;AAEvC,qBAAe,QAAQ;AAEvB,UAAI,eAAe,OAAO;AAExBD,sBAAAA,WAAS,MAAM;AACb,gBAAM,QAAQC,oBAAI;AAClB,gBAAM,OAAO,mBAAmB,EAAE,mBAAmB,eAAa;AAChE,gBAAI,WAAW;AAEb,8BAAgB,QAAQ,UAAU,SAAS,UAAU;AAAA,YACtD;AAAA,UACT,CAAO,EAAE,KAAI;AAAA,QACb,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,aAAa,UAAU,MAAM;AAC/B,qBAAa,QAAQ;AAErB,aAAK,QAAQ;AACb,oBAAY,QAAQ;AACpB,gBAAQ,QAAQ;AAChB;MACD;AACD,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AACvB,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,qBAAqB,MAAM;AAE/BA,oBAAA,MAAA,MAAA,OAAA,8CAAY,YAAY,cAAc,KAAK;AAAA,IAC7C;AAGA,UAAM,eAAe,MAAM;AACzB,qBAAe,QAAQ;AACvB,qBAAe,QAAQ;AACvB,WAAK,QAAQ;AACb,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAChB;IACF;AAGAC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUD,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAGhC,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY,WAAW;AAGvC,UAAI,QAAQ,MAAM;AAChB,oBAAY,QAAQ,QAAQ;AAC5B,wBAAgB,QAAQ,IAAI;AAG5B,0BAAkB,QAAQ;AAG1B,sBAAc,QAAQ;AAAA,UACpB,EAAE,MAAM,QAAQ,MAAM,OAAQ;AAAA,UAC9B,EAAE,MAAM,QAAQ,MAAM,OAAQ;AAAA,UAC9B,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,UAClC,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,UAC7B,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,UAChC,EAAE,MAAM,QAAQ,MAAM,aAAc;AAAA,UACpC,EAAE,MAAM,QAAQ,MAAM,aAAc;AAAA,UACpC,EAAE,MAAM,QAAQ,MAAM,aAAc;AAAA,UACpC,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,UAC7B,EAAE,MAAM,QAAQ,MAAM,oBAAqB;AAAA,UAC3C,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,UAChC,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,UAC7B,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,UACrC,EAAE,MAAM,QAAQ,MAAM,UAAW;AAAA,UACjC,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,UACnC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,QACrC;AAGI,cAAM,mBAAmB,cAAc,MAAM,UAAU,UAAQ,KAAK,SAAS,QAAQ,IAAI;AACzF,YAAI,mBAAmB,IAAI;AACzB,qBAAW,QAAQ;AAAA,QACpB;AAGD,kCAA0B,QAAQ,IAAI;AAGtC,gBAAO,QAAQ,MAAI;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,0BAAc,QAAQ;AACtB;AAAA,UACF;AACE,0BAAc,QAAQ;AAAA,QACzB;AAAA,MACF;AAGD;IACF,CAAC;AAGD,UAAM,4BAA4B,CAAC,SAAS;AAE1C,cAAO,MAAI;AAAA,QACT,KAAK;AAEH,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,MAAM,MAAM,MAAO;AAAA,YAC3B,EAAE,MAAM,QAAQ,MAAM,gBAAiB;AAAA,YACvC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,eAAgB;AAAA,YACtC,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,YACnC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,YAC/B,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,YACrC,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,YAClC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,UACvC;AACM,+BAAqB,QAAQ;AAC7B;AAAA,QAEF,KAAK;AAEH,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,QAAQ,MAAM,eAAgB;AAAA,YACtC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,eAAgB;AAAA,YACtC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,YACnC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,UACvC;AACM,+BAAqB,QAAQ;AAC7B;AAAA,QAEF,KAAK;AAEH,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,QAAQ,MAAM,aAAc;AAAA,YACpC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,eAAgB;AAAA,YACtC,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,YAClC,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,YACrC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,UACvC;AACM,+BAAqB,QAAQ;AAC7B;AAAA,QAEA,KAAK;AAEL,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,YAC7B,EAAE,MAAM,OAAO,MAAM,SAAU;AAAA,YAC/B,EAAE,MAAM,MAAM,MAAM,aAAc;AAAA,YAClC,EAAE,MAAM,MAAM,MAAM,SAAU;AAAA,YAC9B,EAAE,MAAM,MAAM,MAAM,OAAQ;AAAA,YAC5B,EAAE,MAAM,MAAM,MAAM,aAAc;AAAA,YAClC,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,YAC7B,EAAE,MAAM,MAAM,MAAM,WAAY;AAAA,YAChC,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,UACrC;AACM,+BAAqB,QAAQ;AAC3B;AAAA,QAEF,KAAK;AAEL,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,OAAO,MAAM,QAAS;AAAA,YAC9B,EAAE,MAAM,OAAO,MAAM,OAAQ;AAAA,YAC7B,EAAE,MAAM,OAAO,MAAM,UAAW;AAAA,YAChC,EAAE,MAAM,OAAO,MAAM,QAAS;AAAA,YAC9B,EAAE,MAAM,OAAO,MAAM,YAAa;AAAA,YAClC,EAAE,MAAM,OAAO,MAAM,QAAS;AAAA,UACtC;AACM,+BAAqB,QAAQ;AAC3B;AAAA,QAEF,KAAK;AAEL,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,YACrC,EAAE,MAAM,QAAQ,MAAM,gBAAiB;AAAA,YACvC,EAAE,MAAM,QAAQ,MAAM,aAAc;AAAA,YACpC,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,YACrC,EAAE,MAAM,QAAQ,MAAM,kBAAmB;AAAA,YACzC,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,UACrC;AACM,+BAAqB,QAAQ;AAC3B;AAAA,QAEF,KAAK;AAEL,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,YACnC,EAAE,MAAM,QAAQ,MAAM,UAAW;AAAA,YACjC,EAAE,MAAM,OAAO,MAAM,SAAU;AAAA,YAC/B,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,YAClC,EAAE,MAAM,QAAQ,MAAM,gBAAiB;AAAA,YACvC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,UACvC;AACM,+BAAqB,QAAQ;AAC3B;AAAA,QAEF,KAAK;AAEL,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,YACrC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,eAAgB;AAAA,YACtC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,UACrC;AACM,+BAAqB,QAAQ;AAC3B;AAAA,QAEF,KAAK;AAEL,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,QAAQ,MAAM,UAAW;AAAA,YACjC,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,YACnC,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,YACnC,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,YAClC,EAAE,MAAM,QAAQ,MAAM,OAAQ;AAAA,YAC9B,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,YAC/B,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,UACrC;AACM,+BAAqB,QAAQ;AAC3B;AAAA,QAEF,KAAK;AAEL,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,SAAS,MAAM,UAAW;AAAA,YAClC,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,YACnC,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,YAClC,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,UACrC;AACM,+BAAqB,QAAQ;AAC3B;AAAA,QAEF,KAAK;AAEL,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,YACnC,EAAE,MAAM,QAAQ,MAAM,UAAW;AAAA,YACjC,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,YACnC,EAAE,MAAM,QAAQ,MAAM,mBAAoB;AAAA,YAC1C,EAAE,MAAM,QAAQ,MAAM,UAAW;AAAA,YACjC,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,YAC/B,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,aAAc;AAAA,YACpC,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,UACrC;AACM,+BAAqB,QAAQ;AAC3B;AAAA,QAEF,KAAK;AAEL,2BAAiB,QAAQ;AAAA,YACvB,EAAE,MAAM,QAAQ,MAAM,MAAO;AAAA,YAC7B,EAAE,MAAM,QAAQ,MAAM,QAAS;AAAA,YAC/B,EAAE,MAAM,QAAQ,MAAM,YAAa;AAAA,YACnC,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC,EAAE,MAAM,QAAQ,MAAM,WAAY;AAAA,YAClC,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,YACrC,EAAE,MAAM,QAAQ,MAAM,UAAW;AAAA,YACjC,EAAE,MAAM,QAAQ,MAAM,cAAe;AAAA,YACrC,EAAE,MAAM,MAAM,MAAM,QAAS;AAAA,UACrC;AACM,+BAAqB,QAAQ;AAC3B;AAAA,QAEF;AAEA,2BAAiB,QAAQ;AACzB,+BAAqB,QAAQ;AAAA,MAChC;AAGD,oBAAc,QAAQ;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9/CA,GAAG,WAAW,eAAe;"}