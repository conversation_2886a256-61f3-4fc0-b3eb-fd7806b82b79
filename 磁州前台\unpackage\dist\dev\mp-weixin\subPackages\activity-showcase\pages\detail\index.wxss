/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-1470128c, html.data-v-1470128c, #app.data-v-1470128c, .index-container.data-v-1470128c {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 整体容器 */
.detail-container.data-v-1470128c {
  background-color: #F5F7FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 导航栏 */
.nav-bar.data-v-1470128c {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}
.nav-content.data-v-1470128c {
  display: flex;
  align-items: center;
  height: 80rpx;
}
.nav-icon.data-v-1470128c {
  width: 24rpx;
  height: 24rpx;
  padding: 0 10rpx;
}
.nav-title.data-v-1470128c {
  font-size: 24rpx;
  font-weight: 500;
}
.back-button.data-v-1470128c {
  display: flex;
  align-items: center;
}
.back-icon.data-v-1470128c {
  width: 38rpx;
  height: 38rpx;
}

/* 商品轮播图 */
.product-swiper.data-v-1470128c {
  height: 520rpx;
  /* 从580rpx进一步减小到520rpx */
  width: 100%;
  border-radius: 0 0 24rpx 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.swiper-image.data-v-1470128c {
  width: 100%;
  height: 100%;
}

/* 商品基本信息卡片 */
.basic-info-card.data-v-1470128c {
  margin: -30rpx 24rpx 24rpx;
  /* 从-40rpx调整为-30rpx */
  padding: 24rpx 30rpx;
  /* 从30rpx调整为24rpx 30rpx，减小上下内边距 */
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}
.price-section.data-v-1470128c {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12rpx;
  /* 从16rpx减小到12rpx */
}
.price-main.data-v-1470128c {
  display: flex;
  align-items: baseline;
}
.price-symbol.data-v-1470128c {
  font-size: 36rpx;
  font-weight: 500;
  color: #FF3B30;
}
.price-value.data-v-1470128c {
  font-size: 54rpx;
  /* 从60rpx减小到54rpx */
  font-weight: 700;
  color: #FF3B30;
  line-height: 1;
}
.price-tag.data-v-1470128c {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 12rpx;
}
.price-tag-text.data-v-1470128c {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 500;
}
.price-compare.data-v-1470128c {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.compare-item.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}
.compare-label.data-v-1470128c {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 8rpx;
}
.compare-price.data-v-1470128c {
  font-size: 26rpx;
  color: #8E8E93;
  text-decoration: line-through;
}
.save-info.data-v-1470128c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  /* 从20rpx减小到16rpx */
}
.save-tag.data-v-1470128c {
  display: flex;
  align-items: center;
  background-color: rgba(255, 59, 48, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
}
.save-icon.data-v-1470128c {
  width: 32rpx;
  height: 32rpx;
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  text-align: center;
  line-height: 32rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}
.save-text.data-v-1470128c {
  font-size: 24rpx;
  color: #FF3B30;
  font-weight: 600;
}
.limited-text.data-v-1470128c {
  font-size: 24rpx;
  color: #FF9500;
  font-weight: 600;
}
.market-price.data-v-1470128c {
  font-size: 28rpx;
  color: #8E8E93;
  text-decoration: line-through;
  margin-right: 12rpx;
}
.product-title-row.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  /* 从24rpx减小到20rpx */
}
.group-tag.data-v-1470128c {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
}
.product-title.data-v-1470128c {
  font-size: 32rpx;
  /* 从36rpx减小到32rpx */
  font-weight: 700;
  color: #000000;
}

/* 销量和倒计时 */
.sales-countdown.data-v-1470128c {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(255, 59, 48, 0.1);
  padding: 16rpx;
  border-radius: 12rpx;
}
.sales-info.data-v-1470128c {
  display: flex;
  align-items: center;
}
.sales-count.data-v-1470128c {
  font-size: 28rpx;
  color: #8E8E93;
  margin-right: 16rpx;
}
.sales-divider.data-v-1470128c {
  color: #8E8E93;
  margin: 0 16rpx;
}
.view-count.data-v-1470128c {
  font-size: 28rpx;
  color: #8E8E93;
}
.mini-countdown.data-v-1470128c {
  display: flex;
  align-items: center;
}
.countdown-text.data-v-1470128c {
  font-size: 28rpx;
  color: #8E8E93;
  margin-right: 16rpx;
}
.time-block.data-v-1470128c {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 600;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}
.time-colon.data-v-1470128c {
  color: #FF3B30;
  margin: 0 8rpx;
  font-weight: 600;
}

/* 拼团信息卡片 */
.group-card.data-v-1470128c {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.group-header.data-v-1470128c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.group-title.data-v-1470128c {
  display: flex;
  align-items: center;
}
.group-icon.data-v-1470128c {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}
.group-more.data-v-1470128c {
  font-size: 26rpx;
  color: #8E8E93;
}
.group-location.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx dashed #F2F2F7;
}
.location-icon.data-v-1470128c {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.location-text.data-v-1470128c {
  font-size: 26rpx;
  color: #333333;
}
.group-avatars.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.avatar-item.data-v-1470128c {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  overflow: hidden;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.avatar-image.data-v-1470128c {
  width: 100%;
  height: 100%;
}
.avatar-more.data-v-1470128c {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F2F2F7;
  color: #8E8E93;
  font-size: 24rpx;
  margin-right: 16rpx;
}
.recent-join.data-v-1470128c {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.recent-user.data-v-1470128c {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
}
.join-time.data-v-1470128c {
  font-size: 24rpx;
  color: #FF3B30;
}
.group-progress.data-v-1470128c {
  margin-bottom: 24rpx;
}
.progress-bar.data-v-1470128c {
  height: 20rpx;
  background-color: #F2F2F7;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}
.progress-fill.data-v-1470128c {
  height: 100%;
  background: linear-gradient(to right, #FF3B30, #FF9500);
  border-radius: 10rpx;
}
.progress-text.data-v-1470128c {
  font-size: 26rpx;
  color: #8E8E93;
  text-align: center;
}
.group-tips.data-v-1470128c {
  display: flex;
  align-items: center;
  background-color: rgba(255, 59, 48, 0.1);
  padding: 16rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.tip-icon.data-v-1470128c {
  margin-right: 8rpx;
  color: #FF3B30;
}
.tip-text.data-v-1470128c {
  font-size: 28rpx;
  color: #333333;
}
.verification-info.data-v-1470128c {
  margin-bottom: 20rpx;
}
.verification-title.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.verification-icon.data-v-1470128c {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.verification-text.data-v-1470128c {
  font-size: 28rpx;
  color: #333333;
}
.verification-address.data-v-1470128c {
  font-size: 26rpx;
  color: #666666;
}
.verification-time.data-v-1470128c {
  font-size: 26rpx;
  color: #8E8E93;
}
.local-benefits.data-v-1470128c {
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
  border-top: 1rpx dashed #F2F2F7;
}
.benefit-item.data-v-1470128c {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.benefit-icon.data-v-1470128c {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}
.benefit-text.data-v-1470128c {
  font-size: 24rpx;
  color: #333333;
}

/* 优惠券卡片 */
.coupon-card.data-v-1470128c {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.coupon-left.data-v-1470128c {
  flex: 1;
}
.coupon-title.data-v-1470128c {
  font-size: 36rpx;
  font-weight: 700;
  color: #000000;
  margin-bottom: 8rpx;
}
.coupon-desc.data-v-1470128c {
  font-size: 28rpx;
  color: #8E8E93;
}
.coupon-right.data-v-1470128c {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
}
.coupon-btn.data-v-1470128c {
  font-size: 28rpx;
  font-weight: 600;
}

/* 商家信息卡片 */
.shop-card.data-v-1470128c {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}
.shop-info.data-v-1470128c {
  flex: 1;
  display: flex;
  align-items: center;
}
.shop-logo.data-v-1470128c {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.shop-details.data-v-1470128c {
  flex: 1;
}
.shop-name.data-v-1470128c {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
}
.shop-rating.data-v-1470128c {
  display: flex;
  align-items: center;
}
.rating-stars.data-v-1470128c {
  display: flex;
  margin-right: 12rpx;
}
.star-icon.data-v-1470128c {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}
.rating-score.data-v-1470128c {
  font-size: 26rpx;
  color: #FF9500;
}
.rating-count.data-v-1470128c {
  font-size: 26rpx;
  color: #8E8E93;
  margin-left: 4rpx;
}
.shop-action.data-v-1470128c {
  background-color: #F2F2F7;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #000000;
}
.chevron-icon.data-v-1470128c {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

/* 商品参数卡片 */
.params-card.data-v-1470128c {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.params-header.data-v-1470128c {
  font-size: 36rpx;
  font-weight: 700;
  color: #000000;
  margin-bottom: 24rpx;
}
.params-list.data-v-1470128c {
  display: flex;
  flex-wrap: wrap;
}
.param-item.data-v-1470128c {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
}
.param-name.data-v-1470128c {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 4rpx;
}
.param-value.data-v-1470128c {
  font-size: 32rpx;
  font-weight: 700;
  color: #000000;
}

/* 详情选项卡 */
.detail-tabs.data-v-1470128c {
  margin: 0 24rpx 24rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.tab-header.data-v-1470128c {
  display: flex;
  border-bottom: 1px solid #F2F2F7;
}
.tab-item.data-v-1470128c {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #8E8E93;
  position: relative;
}
.tab-item.active.data-v-1470128c {
  color: #000000;
  font-weight: 500;
}
.tab-line.data-v-1470128c {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #FF3B30;
  border-radius: 2rpx;
}
.tab-content.data-v-1470128c {
  padding: 30rpx;
}

/* 商品详情 */
.detail-desc.data-v-1470128c {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 24rpx;
}
.detail-image.data-v-1470128c {
  width: 100%;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
}

/* 活动规则 */
.rule-item.data-v-1470128c {
  display: flex;
  margin-bottom: 20rpx;
}
.rule-number.data-v-1470128c {
  margin-right: 16rpx;
  color: #FF3B30;
  font-weight: 600;
}
.rule-text.data-v-1470128c {
  flex: 1;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 用户评价 */
.reviews-header.data-v-1470128c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.reviews-title.data-v-1470128c {
  font-size: 36rpx;
  font-weight: 700;
  color: #000000;
}
.reviews-rate.data-v-1470128c {
  font-size: 28rpx;
  color: #8E8E93;
}
.review-tags.data-v-1470128c {
  display: flex;
  margin-bottom: 24rpx;
}
.review-tag.data-v-1470128c {
  background-color: #F2F2F7;
  color: #8E8E93;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
}
.review-tag.active.data-v-1470128c {
  background-color: #FF3B30;
  color: #FFFFFF;
}
.review-item.data-v-1470128c {
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid #F2F2F7;
}
.review-item.data-v-1470128c:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.review-header.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.user-avatar.data-v-1470128c {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.review-user.data-v-1470128c {
  flex: 1;
}
.user-name.data-v-1470128c {
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 4rpx;
}
.review-rating.data-v-1470128c {
  display: flex;
}
.star.data-v-1470128c {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}
.review-time.data-v-1470128c {
  font-size: 24rpx;
  color: #8E8E93;
}
.review-content.data-v-1470128c {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}
.review-images.data-v-1470128c {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.review-image.data-v-1470128c {
  width: 160rpx;
  height: 160rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
}
.review-actions.data-v-1470128c {
  display: flex;
}
.review-action.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-right: 24rpx;
  color: #8E8E93;
  font-size: 24rpx;
}
.empty-reviews.data-v-1470128c {
  text-align: center;
  padding: 60rpx 0;
  color: #8E8E93;
  font-size: 28rpx;
}
.more-reviews.data-v-1470128c {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  border-top: 1px solid #F2F2F7;
  color: #8E8E93;
  font-size: 28rpx;
}
.recommend-section.data-v-1470128c {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.recommend-header.data-v-1470128c {
  font-size: 36rpx;
  font-weight: 700;
  color: #000000;
  margin-bottom: 24rpx;
}
.recommend-list.data-v-1470128c {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}
.recommend-item.data-v-1470128c {
  width: calc(50% - 16rpx);
  margin: 0 8rpx 16rpx;
}
.recommend-image.data-v-1470128c {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.recommend-info.data-v-1470128c {
  padding: 0 8rpx;
}
.recommend-name.data-v-1470128c {
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.recommend-price.data-v-1470128c {
  font-size: 28rpx;
  color: #FF3B30;
  font-weight: 500;
}

/* 底部安全区 */
.safe-area-inset-bottom.data-v-1470128c {
  height: 120rpx;
}

/* 底部购买栏 */
.bottom-bar.data-v-1470128c {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.bottom-left.data-v-1470128c {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}
.action-btn.data-v-1470128c {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20rpx;
}
.action-icon.data-v-1470128c {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}
.action-btn text.data-v-1470128c {
  font-size: 20rpx;
  color: #8E8E93;
}
.buy-buttons.data-v-1470128c {
  flex: 1;
  display: flex;
  height: 100%;
}
.buy-btn.data-v-1470128c {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.normal-buy.data-v-1470128c {
  background-color: #F2F2F7;
}
.group-buy.data-v-1470128c {
  background: linear-gradient(135deg, #FF3B30, #FF9500);
}
.buy-price.data-v-1470128c {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}
.group-buy .buy-price.data-v-1470128c {
  color: #FFFFFF;
}
.buy-price-row.data-v-1470128c {
  display: flex;
  align-items: center;
}
.original-tag.data-v-1470128c {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: line-through;
  margin-left: 8rpx;
}
.buy-label.data-v-1470128c {
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 4rpx;
  display: flex;
  align-items: center;
}
.group-buy .buy-label.data-v-1470128c {
  color: #FFFFFF;
}
.save-amount.data-v-1470128c {
  background-color: #FFFFFF;
  color: #FF3B30;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 8rpx;
  font-weight: 600;
}

/* 购买弹窗 */
.buy-popup.data-v-1470128c {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.popup-mask.data-v-1470128c {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.popup-content.data-v-1470128c {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  transform: translateY(0);
  animation: slideUp-1470128c 0.3s ease;
}
@keyframes slideUp-1470128c {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.popup-header.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.popup-product-image.data-v-1470128c {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}
.popup-product-info.data-v-1470128c {
  flex: 1;
}
.popup-price.data-v-1470128c {
  font-size: 36rpx;
  color: #FF3B30;
  font-weight: 700;
  margin-bottom: 8rpx;
}
.popup-stock.data-v-1470128c {
  font-size: 24rpx;
  color: #8E8E93;
}
.popup-verify-tag.data-v-1470128c {
  background-color: #FF9500;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  display: inline-block;
  margin-top: 8rpx;
}
.popup-close.data-v-1470128c {
  width: 48rpx;
  height: 48rpx;
}
.popup-section.data-v-1470128c {
  margin-bottom: 30rpx;
}
.section-title.data-v-1470128c {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 16rpx;
}
.quantity-selector.data-v-1470128c {
  display: flex;
  align-items: center;
  border: 1px solid #E5E5EA;
  border-radius: 8rpx;
  width: 240rpx;
}
.quantity-btn.data-v-1470128c {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #8E8E93;
}
.quantity-input.data-v-1470128c {
  flex: 1;
  height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-left: 1px solid #E5E5EA;
  border-right: 1px solid #E5E5EA;
}
.popup-verify-info.data-v-1470128c {
  margin-top: 20rpx;
  background-color: #F9F9F9;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
}
.verify-title.data-v-1470128c {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 10rpx;
  display: block;
}
.verify-item.data-v-1470128c {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
}
.popup-footer.data-v-1470128c {
  margin-top: 60rpx;
}
.popup-buy-btn.data-v-1470128c {
  height: 88rpx;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
}

/* 加载状态 */
.loading-container.data-v-1470128c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  width: 100%;
}
.loading-spinner.data-v-1470128c {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #F2F2F7;
  border-top: 6rpx solid #FF3B30;
  border-radius: 50%;
  animation: spin-1470128c 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-text.data-v-1470128c {
  font-size: 28rpx;
  color: #8E8E93;
}
@keyframes spin-1470128c {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 分享弹窗 */
.share-popup.data-v-1470128c {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.share-content.data-v-1470128c {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  transform: translateY(0);
  animation: slideUp-1470128c 0.3s ease;
}
.share-header.data-v-1470128c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.share-options.data-v-1470128c {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.share-option.data-v-1470128c {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.share-icon.data-v-1470128c {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}
.share-option text.data-v-1470128c {
  font-size: 24rpx;
  color: #8E8E93;
}
.share-poster.data-v-1470128c {
  height: 88rpx;
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
}

/* 底部购买栏的样式 */
.verify-tag.data-v-1470128c {
  position: absolute;
  top: -20rpx;
  right: 20rpx;
  background-color: #FF9500;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  transform: rotate(5deg);
}

/* 套餐内容样式 */
.package-content.data-v-1470128c {
  background-color: #F9F9F9;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.package-title.data-v-1470128c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 20rpx;
}
.package-title.data-v-1470128c::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #FF3B30;
  border-radius: 4rpx;
}
.package-items.data-v-1470128c {
  margin-bottom: 16rpx;
}
.package-item.data-v-1470128c {
  padding: 16rpx 0;
  border-bottom: 1px dashed #E5E5EA;
}
.package-item.data-v-1470128c:last-child {
  border-bottom: none;
}
.package-item-header.data-v-1470128c {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}
.package-item-name.data-v-1470128c {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.package-item-quantity.data-v-1470128c {
  font-size: 28rpx;
  color: #FF3B30;
}
.package-item-desc.data-v-1470128c {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}
.package-notice.data-v-1470128c {
  background-color: rgba(255, 149, 0, 0.1);
  padding: 16rpx;
  border-radius: 8rpx;
}
.notice-title.data-v-1470128c {
  font-size: 26rpx;
  font-weight: 600;
  color: #FF9500;
  margin-bottom: 8rpx;
  display: block;
}
.notice-text.data-v-1470128c {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
}
.buy-btn.group-buy.data-v-1470128c {
  background-color: #FF3B30;
  flex: 2;
}
.buy-btn.full-width.data-v-1470128c {
  flex: 1;
  width: 100%;
}
.buy-price.data-v-1470128c {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
}
.custom-navbar.data-v-1470128c {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
}
.custom-navbar .navbar-bg.data-v-1470128c {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
}
.custom-navbar .navbar-content.data-v-1470128c {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding-top: var(--status-bar-height, 25px);
  padding-left: 30rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
}
.custom-navbar .navbar-content .back-btn.data-v-1470128c {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-navbar .navbar-content .back-icon.data-v-1470128c {
  width: 100%;
  height: 100%;
}
.custom-navbar .navbar-content .navbar-title.data-v-1470128c {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.custom-navbar .navbar-content .navbar-right.data-v-1470128c {
  width: 40rpx;
}

/* 分享弹窗样式 */
.share-popup.data-v-1470128c {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

/* 分销弹窗样式 */
.distribution-popup.data-v-1470128c {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.distribution-content.data-v-1470128c {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  transform: translateY(0);
  animation: slideUp-1470128c 0.3s ease;
}
.distribution-header.data-v-1470128c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #F2F2F7;
}
.distribution-header text.data-v-1470128c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.distribution-body.data-v-1470128c {
  padding: 20rpx 0;
}
.distribution-info.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.distribution-image.data-v-1470128c {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
}
.distribution-text.data-v-1470128c {
  flex: 1;
}
.distribution-title.data-v-1470128c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}
.distribution-desc.data-v-1470128c {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  display: block;
}
.distribution-data.data-v-1470128c {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #FFF9F9;
  border-radius: 16rpx;
}
.data-item.data-v-1470128c {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.data-value.data-v-1470128c {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF3B30;
  margin-bottom: 8rpx;
}
.data-label.data-v-1470128c {
  font-size: 24rpx;
  color: #666666;
}
.distribution-btns.data-v-1470128c {
  display: flex;
  justify-content: space-between;
}
.distribution-btn.data-v-1470128c {
  width: 48%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
}
.apply-btn.data-v-1470128c {
  background: linear-gradient(135deg, #FF3B30, #FF9500);
  color: #FFFFFF;
}
.poster-btn.data-v-1470128c {
  background-color: #F2F2F7;
  color: #333333;
  border: 1px solid #E5E5EA;
}

/* 适用门店列表卡片 */
.store-list-card.data-v-1470128c {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.card-header.data-v-1470128c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.card-title.data-v-1470128c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}
.card-title.data-v-1470128c::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  border-radius: 4rpx;
}
.card-more.data-v-1470128c {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999999;
}
.store-list.data-v-1470128c {
  border-radius: 12rpx;
  overflow: hidden;
}
.store-item.data-v-1470128c {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #F5F5F7;
}
.store-item.data-v-1470128c:last-child {
  border-bottom: none;
}
.store-logo.data-v-1470128c {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.store-info.data-v-1470128c {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.store-name.data-v-1470128c {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.store-address.data-v-1470128c {
  font-size: 24rpx;
  color: #999999;
}
.store-distance.data-v-1470128c {
  font-size: 24rpx;
  color: #999999;
  padding: 0 10rpx;
}

/* 本店其他拼团卡片 */
.other-group-card.data-v-1470128c {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.other-group-list.data-v-1470128c {
  border-radius: 12rpx;
  overflow: hidden;
}
.other-group-item.data-v-1470128c {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #F5F5F7;
  position: relative;
}
.other-group-item.data-v-1470128c:last-child {
  border-bottom: none;
}
.sold-tag-corner.data-v-1470128c {
  position: absolute;
  top: 10rpx;
  right: 0;
  background: #FF3B5C;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 2rpx 12rpx;
  border-radius: 12rpx 0 0 12rpx;
  z-index: 1;
}
.item-image-container.data-v-1470128c {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
}
.other-group-image.data-v-1470128c {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.other-group-info.data-v-1470128c {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.shop-location.data-v-1470128c {
  display: flex;
  flex-direction: column;
  margin-bottom: 6rpx;
}
.shop-location .shop-name.data-v-1470128c {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}
.shop-location .location-info.data-v-1470128c {
  font-size: 22rpx;
  color: #999999;
  margin-top: 4rpx;
}
.other-group-title.data-v-1470128c {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 8rpx;
}
.other-group-price.data-v-1470128c {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.group-price-value.data-v-1470128c {
  font-size: 32rpx;
  color: #FF2C54;
  font-weight: 600;
  margin-right: 12rpx;
}
.market-price-value.data-v-1470128c {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
  margin-right: 12rpx;
}
.discount-tag.data-v-1470128c {
  font-size: 22rpx;
  color: #FF2C54;
  background-color: rgba(255, 44, 84, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}
.other-group-bottom.data-v-1470128c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sold-info.data-v-1470128c {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #999999;
}
.sold-tag.data-v-1470128c {
  background-color: #F5F5F7;
  color: #666666;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}
.sold-count.data-v-1470128c {
  margin-right: 8rpx;
}
.usage-info.data-v-1470128c {
  background-color: #F5F5F7;
  color: #666666;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}
.buy-now-btn.data-v-1470128c {
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  font-weight: 500;
}

/* 推荐商品部分样式调整 */
.recommend-section.data-v-1470128c {
  margin: 0 24rpx 24rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.recommend-header.data-v-1470128c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
}
.recommend-header.data-v-1470128c::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #FF2C54 0%, #FF4F64 100%);
  border-radius: 4rpx;
}
.recommend-list.data-v-1470128c {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}
.recommend-item.data-v-1470128c {
  width: calc(50% - 16rpx);
  margin: 0 8rpx 16rpx;
}
.recommend-image.data-v-1470128c {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}
.recommend-info.data-v-1470128c {
  padding: 0 8rpx;
}
.recommend-name.data-v-1470128c {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.recommend-price.data-v-1470128c {
  font-size: 28rpx;
  color: #FF2C54;
  font-weight: 500;
}