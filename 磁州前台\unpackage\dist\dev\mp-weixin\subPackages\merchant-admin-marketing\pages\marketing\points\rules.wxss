/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 页面容器 */
.points-rules-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: white;
  padding: 48px 20px 16px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(255, 120, 0, 0.15);
}
.navbar-left {
  width: 40px;
}
.back-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar-title {
  flex: 1;
  text-align: center;
}
.title-text {
  font-size: 18px;
  font-weight: 600;
}
.navbar-right {
  width: 40px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  height: calc(100vh - 80px);
}

/* 规则卡片 */
.rules-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}
.edit-text {
  font-size: 14px;
  color: #5E5CE6;
}

/* 规则列表 */
.rules-list {
  margin-top: 16px;
}
.rule-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}
.rule-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}
.icon-circle {
  width: 32px;
  height: 32px;
  border-radius: 16px;
}
.rule-icon.purchase {
  background-color: rgba(25, 137, 250, 0.1);
}
.rule-icon.purchase .icon-circle {
  background-color: #1989FA;
}
.rule-icon.checkin {
  background-color: rgba(52, 199, 89, 0.1);
}
.rule-icon.checkin .icon-circle {
  background-color: #34C759;
}
.rule-icon.share {
  background-color: rgba(255, 149, 0, 0.1);
}
.rule-icon.share .icon-circle {
  background-color: #FF9500;
}
.rule-icon.review {
  background-color: rgba(94, 92, 230, 0.1);
}
.rule-icon.review .icon-circle {
  background-color: #5E5CE6;
}
.rule-icon.register {
  background-color: rgba(255, 59, 48, 0.1);
}
.rule-icon.register .icon-circle {
  background-color: #FF3B30;
}
.rule-icon.birthday {
  background-color: rgba(255, 45, 85, 0.1);
}
.rule-icon.birthday .icon-circle {
  background-color: #FF2D55;
}
.rule-content {
  flex: 1;
}
.rule-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}
.rule-desc {
  font-size: 12px;
  color: #999999;
}
.rule-points {
  font-size: 16px;
  font-weight: 600;
  color: #FF7600;
}
.rule-points-edit {
  display: flex;
  align-items: center;
}
.plus-sign {
  font-size: 16px;
  color: #FF7600;
  margin-right: 2px;
}
.points-input {
  width: 50px;
  height: 32px;
  background-color: #F5F7FA;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 16px;
  color: #FF7600;
  text-align: center;
}

/* 添加规则按钮 */
.add-rule-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px dashed #CCCCCC;
}
.add-icon {
  font-size: 20px;
  color: #999999;
  margin-right: 8px;
}
.add-text {
  font-size: 16px;
  color: #999999;
}

/* 底部空间 */
.bottom-space {
  height: 20px;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;
}
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #F0F0F0;
}
.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}
.modal-close {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}
.modal-body {
  padding: 20px;
}
.form-item {
  margin-bottom: 16px;
}
.form-label {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
  display: block;
}
.form-input, .picker {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
  display: flex;
  align-items: center;
}
.modal-footer {
  display: flex;
  border-top: 1px solid #F0F0F0;
}
.modal-button {
  flex: 1;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}
.modal-button.cancel {
  color: #666666;
  background-color: #FFFFFF;
}
.modal-button.confirm {
  color: #FFFFFF;
  background-color: #FF7600;
}