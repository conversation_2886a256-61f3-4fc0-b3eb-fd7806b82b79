<template>
  <view class="marketing-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">营销中心</text>
    </view>
    
    <!-- 页面内容 -->
    <scroll-view scroll-y class="page-content">
    
    <!-- 常用营销工具 -->
    <view class="tool-section">
      <view class="section-header">
        <text class="section-title">营销工具</text>
      </view>
      
      <view class="tool-grid">
        <view class="tool-card" 
          v-for="(tool, index) in marketingTools" 
          :key="index" 
          @tap="navigateToTool(tool)" 
          :class="{'hover': hoveredTool === tool.id}"
          @mouseenter="setHoveredTool(tool.id)"
          @mouseleave="clearHoveredTool()">
          <view class="tool-icon-wrap" :class="tool.class">
            <view class="tool-icon-svg" v-html="tool.svg"></view>
          </view>
          <text class="tool-name" :class="{'three-chars': tool.name.length === 3}">{{tool.name}}</text>
          <text class="tool-desc">{{tool.description}}</text>
        </view>
      </view>
    </view>
    
    <!-- 营销数据概览 -->
    <view class="overview-section">
      <view class="overview-title">
        <text class="title-text">营销效果概览</text>
        <view class="date-picker">
          <text class="date-text">{{dateRange}}</text>
          <view class="date-icon"></view>
        </view>
      </view>
      
      <view class="data-cards">
        <view class="data-card">
          <view class="card-content">
            <text class="card-value">{{formatNumber(marketingData.revenue)}}</text>
            <text class="card-label">营销收入 (元)</text>
          </view>
          <view class="card-trend" :class="marketingData.revenueTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{marketingData.revenueGrowth}}</text>
          </view>
        </view>
        
        <view class="data-card">
          <view class="card-content">
            <text class="card-value">{{marketingData.conversion}}%</text>
            <text class="card-label">转化率</text>
          </view>
          <view class="card-trend" :class="marketingData.conversionTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{marketingData.conversionGrowth}}</text>
          </view>
        </view>
        
        <view class="data-card">
          <view class="card-content">
            <text class="card-value">{{marketingData.roi}}%</text>
            <text class="card-label">投资回报率</text>
          </view>
          <view class="card-trend" :class="marketingData.roiTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{marketingData.roiGrowth}}</text>
          </view>
        </view>
        
        <view class="data-card">
          <view class="card-content">
            <text class="card-value">{{marketingData.customers}}</text>
            <text class="card-label">新增客户</text>
          </view>
          <view class="card-trend" :class="marketingData.customersTrend">
            <view class="trend-arrow"></view>
            <text class="trend-value">{{marketingData.customersGrowth}}</text>
          </view>
        </view>
      </view>
    </view>
      
      <!-- 智能营销助手 -->
      <view class="ai-marketing-section">
        <view class="section-header">
          <text class="section-title">智能营销助手</text>
          <text class="view-all" @tap="viewAllAiTools">更多</text>
        </view>
        
        <view class="ai-tools-grid">
          <!-- 消费趋势分析 -->
          <view class="ai-tool-card" @tap="useAiTool('trend')">
            <view class="ai-tool-icon blue">
              <view class="ai-tool-icon-svg">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="#1989FA">
                  <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"/>
                </svg>
              </view>
            </view>
            <view class="ai-tool-content">
              <text class="ai-tool-title">消费趋势分析</text>
              <text class="ai-tool-desc">近期顾客偏好变化，建议调整商品结构</text>
            </view>
          </view>
          
          <!-- 竞品价格监测 -->
          <view class="ai-tool-card" @tap="useAiTool('price')">
            <view class="ai-tool-icon yellow">
              <view class="ai-tool-icon-svg">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="#FF9500">
                  <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
                </svg>
              </view>
            </view>
            <view class="ai-tool-content">
              <text class="ai-tool-title">竞品价格监测</text>
              <text class="ai-tool-desc">同类商品市场价格下降5%，建议调整策略</text>
            </view>
          </view>
          
          <!-- 销售预测模型 -->
          <view class="ai-tool-card" @tap="useAiTool('forecast')">
            <view class="ai-tool-icon green">
              <view class="ai-tool-icon-svg">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="#34C759">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                </svg>
              </view>
            </view>
            <view class="ai-tool-content">
              <text class="ai-tool-title">销售预测模型</text>
              <text class="ai-tool-desc">基于历史数据，下月销量预计增长12%</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 营销工具导航 -->
    <view class="tool-navigation">
      <scroll-view scroll-x class="tab-scroll" show-scrollbar="false">
        <view 
          class="tab-item" 
          v-for="(tab, index) in tabList" 
          :key="index"
          :class="{ active: currentTab === index }"
          @tap="switchTab(index)">
          <text class="tab-text">{{tab}}</text>
        </view>
      </scroll-view>
    </view>
    
    <!-- 活动效果图表 (概览标签) -->
    <block v-if="currentTab === 0">
      <view class="chart-section">
        <view class="section-header">
          <text class="section-title">活动效果分析</text>
          <view class="filter-dropdown">
            <text class="selected-value">最近7天</text>
            <view class="dropdown-arrow"></view>
          </view>
        </view>
        
        <view class="chart-container">
          <view class="chart-area">
            <!-- Y轴标签 -->
            <view class="y-axis-labels">
              <text class="y-label">¥15k</text>
              <text class="y-label">¥10k</text>
              <text class="y-label">¥5k</text>
              <text class="y-label">0</text>
            </view>
            
            <!-- 图表区域 -->
            <view class="chart-grid">
              <!-- 水平网格线 -->
              <view class="h-grid-line" style="top: 0%"></view>
              <view class="h-grid-line" style="top: 33.33%"></view>
              <view class="h-grid-line" style="top: 66.66%"></view>
              <view class="h-grid-line" style="top: 100%"></view>
              
              <!-- 垂直网格线和X轴标签 -->
              <view v-for="(item, index) in chartData" :key="index" class="chart-column" :style="{ left: (index * (100/(chartData.length-1))) + '%' }">
                <view class="v-grid-line"></view>
                <text class="x-label">{{item.date}}</text>
              </view>
              
              <!-- 连线区域 -->
              <view class="chart-lines-container">
                <view class="revenue-area" :style="{ 'clip-path': revenueClipPath }"></view>
                <view class="conversion-area" :style="{ 'clip-path': conversionClipPath }"></view>
              </view>
              
              <!-- 营销收入数据点 -->
              <view v-for="(item, index) in chartData" :key="'rev-'+index" class="data-point revenue" :style="{ left: (index * (100/(chartData.length-1))) + '%', bottom: (item.revenueHeight) + '%' }"></view>
              
              <!-- 转化率数据点 -->
              <view v-for="(item, index) in chartData" :key="'conv-'+index" class="data-point conversion" :style="{ left: (index * (100/(chartData.length-1))) + '%', bottom: (item.conversionHeight) + '%' }"></view>
            </view>
          </view>
        </view>
        
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color revenue"></view>
            <text class="legend-text">营销收入</text>
          </view>
          <view class="legend-item">
            <view class="legend-color conversion"></view>
            <text class="legend-text">转化率</text>
          </view>
        </view>
      </view>
      
      <!-- 进行中的营销活动 -->
      <view class="active-campaigns">
      <view class="section-header">
        <text class="section-title">进行中的活动</text>
        <text class="view-all" @tap="viewAllCampaigns">查看全部</text>
      </view>
      
      <view class="campaign-list">
        <view class="campaign-item" v-for="(campaign, index) in activeCampaigns" :key="index" @tap="viewCampaignDetail(campaign)">
          <view class="campaign-status" :class="campaign.statusClass">{{campaign.status}}</view>
          <view class="campaign-info">
            <text class="campaign-name">{{campaign.name}}</text>
            <view class="campaign-meta">
            <text class="campaign-time">{{campaign.timeRange}}</text>
            </view>
          </view>
          <view class="campaign-metrics">
            <view class="metric-item">
              <text class="metric-value">{{campaign.views}}</text>
              <text class="metric-label">浏览</text>
            </view>
            <view class="metric-item">
              <text class="metric-value">{{campaign.conversions}}</text>
              <text class="metric-label">转化</text>
            </view>
            <view class="metric-item">
              <text class="metric-value">¥{{campaign.revenue}}</text>
              <text class="metric-label">收入</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 高级营销功能 -->
    <view class="advanced-section">
      <view class="section-header">
        <text class="section-title">高级营销功能</text>
      </view>
      
      <view class="advanced-tools">
        <view class="advanced-tool" v-for="(tool, index) in advancedTools" :key="index" @tap="navigateToAdvancedTool(tool)">
          <view class="tool-top">
            <view class="tool-icon" :class="tool.class">
              <image class="icon-image" :src="tool.icon"></image>
            </view>
            <view class="tool-info">
              <text class="tool-title">{{tool.title}}</text>
              <text class="tool-subtitle">{{tool.subtitle}}</text>
            </view>
          </view>
          <text class="tool-description">{{tool.description}}</text>
          <view class="tool-tags">
            <view class="tool-tag" v-for="(tag, tagIndex) in tool.tags" :key="tagIndex">
              {{tag}}
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 营销日历 -->
    <view class="calendar-section">
      <view class="section-header">
        <text class="section-title">营销日历</text>
        <text class="calendar-month">2023年4月</text>
      </view>
      
      <view class="calendar-events">
        <view class="event-item" v-for="(event, index) in calendarEvents" :key="index">
          <view class="event-date">
            <text class="date-day">{{event.day}}</text>
            <text class="date-month">{{event.month}}</text>
          </view>
          <view class="event-content">
            <text class="event-title">{{event.title}}</text>
            <view class="event-info">
              <view class="event-type" :class="event.typeClass">{{event.type}}</view>
              <text class="event-time">{{event.time}}</text>
            </view>
          </view>
          <view class="event-action" @tap.stop="manageEvent(event)">
            <text class="action-text">管理</text>
          </view>
        </view>
      </view>
      
      <view class="add-event-btn" @tap="addNewEvent">
        <view class="plus-icon"></view>
        <text class="btn-text">添加新活动</text>
      </view>
    </view>
    </block>
    
    <!-- 优惠券标签内容 -->
    <view v-if="currentTab === 3" class="tab-content">
      <view class="tool-detail-section">
        <view class="section-header">
          <text class="section-title">优惠券管理</text>
          <view class="add-tool-btn" @tap="createCoupon">
            <text class="btn-text">创建优惠券</text>
            <view class="plus-icon-small"></view>
          </view>
        </view>
        
        <view class="tool-cards">
          <view class="coupon-card" v-for="(item, index) in couponsList" :key="index" @tap="viewCouponDetail(item)">
            <view class="coupon-header">
              <text class="coupon-title">{{item.title}}</text>
              <view class="coupon-status" :class="'status-'+item.status">{{item.statusText}}</view>
            </view>
            <view class="coupon-value">
              <text class="discount-symbol">¥</text>
              <text class="discount-amount">{{item.value}}</text>
            </view>
            <view class="coupon-info">
              <text class="coupon-condition">满{{item.minSpend}}元可用</text>
              <text class="coupon-date">有效期至: {{item.expireDate}}</text>
            </view>
            <view class="coupon-stats">
              <view class="stat-item">
                <text class="stat-value">{{item.usedCount}}/{{item.totalCount}}</text>
                <text class="stat-label">已使用/总数</text>
              </view>
              <view class="stat-item">
                <text class="stat-value">{{item.conversionRate}}%</text>
                <text class="stat-label">转化率</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 满减标签内容 -->
    <view v-if="currentTab === 4" class="tab-content">
      <view class="tool-detail-section">
        <view class="section-header">
          <text class="section-title">满减活动管理</text>
          <view class="add-tool-btn" @tap="createDiscount">
            <text class="btn-text">创建满减</text>
            <view class="plus-icon-small"></view>
          </view>
        </view>
        
        <view class="discount-list">
          <view class="discount-item" v-for="(item, index) in discountList" :key="index" @tap="viewDiscountDetail(item)">
            <view class="discount-content">
              <text class="discount-title">{{item.title}}</text>
              <view class="discount-rules">
                <view class="rule-item" v-for="(rule, ruleIndex) in item.rules" :key="ruleIndex">
                  <text class="rule-text">满{{rule.minAmount}}减{{rule.discountAmount}}</text>
                </view>
              </view>
              <text class="discount-time">{{item.timeRange}}</text>
            </view>
            <view class="discount-stats">
              <view class="stat-row">
                <text class="stat-label">使用次数:</text>
                <text class="stat-value">{{item.usageCount}}次</text>
              </view>
              <view class="stat-row">
                <text class="stat-label">优惠金额:</text>
                <text class="stat-value">¥{{item.totalDiscount}}</text>
              </view>
            </view>
            <view class="item-arrow"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 拼团标签内容 -->
    <view v-if="currentTab === 1" class="tab-content">
      <view class="empty-state">
        <view class="empty-icon"></view>
        <text class="empty-text">暂无拼团活动</text>
        <view class="empty-action" @tap="createGroup">
          <text class="action-text">创建拼团活动</text>
        </view>
      </view>
    </view>
    
    <!-- 秒杀标签内容 -->
    <view v-if="currentTab === 5" class="tab-content">
      <view class="empty-state">
        <view class="empty-icon"></view>
        <text class="empty-text">暂无秒杀活动</text>
        <view class="empty-action" @tap="createFlash">
          <text class="action-text">创建秒杀活动</text>
        </view>
      </view>
    </view>
    
    <!-- 分销标签内容 -->
    <view v-if="currentTab === 2" class="tab-content">
      <view class="distribution-overview">
        <view class="overview-card">
          <view class="overview-header">
            <text class="overview-title">分销概览</text>
            <view class="overview-period">本月</view>
          </view>
          <view class="overview-stats">
            <view class="stat-box">
              <text class="stat-number">{{distributionStats.distributors}}</text>
              <text class="stat-desc">总分销员</text>
            </view>
            <view class="stat-box">
              <text class="stat-number">¥{{distributionStats.commission}}</text>
              <text class="stat-desc">总佣金</text>
            </view>
            <view class="stat-box">
              <text class="stat-number">{{distributionStats.orders}}</text>
              <text class="stat-desc">分销订单</text>
            </view>
          </view>
        </view>
        <view class="distribution-settings" @tap="manageDistribution">
          <text class="settings-text">管理分销设置</text>
          <view class="settings-arrow"></view>
        </view>
      </view>
    </view>
    
    <!-- 营销建议 -->
    <view class="ai-insights">
      <view class="ai-header">
        <view class="ai-badge">AI</view>
        <text class="ai-title">智能营销建议</text>
      </view>
      
      <view class="insights-list">
        <view class="insight-item" v-for="(insight, index) in marketingInsights" :key="index">
          <view class="insight-icon" :class="insight.iconType"></view>
          <view class="insight-content">
            <text class="insight-title">{{insight.title}}</text>
            <text class="insight-desc">{{insight.description}}</text>
          </view>
          <view class="insight-actions">
            <view class="action-btn primary" @tap="applyInsight(insight)">应用</view>
            <view class="action-btn secondary" @tap="ignoreInsight(insight)">忽略</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 浮动添加按钮 -->
    <view class="floating-action-button" @tap="createNewCampaign">
      <view class="fab-icon">+</view>
      </view>
      
      <!-- 底部空间，防止内容被底部导航栏遮挡 -->
      <view class="bottom-space"></view>
    </scroll-view>
    
    <!-- 页面底部导航栏 -->
    <view class="tab-bar">
      <view 
        v-for="(tab, index) in visibleTabs" 
        :key="index"
        class="tab-item"
        :class="{ active: navTab === tab.id }"
        @tap="switchNavTab(tab.id)"
      >
        <view class="tab-icon" :class="tab.icon"></view>
        <text class="tab-text">{{ tab.text }}</text>
        <view class="active-indicator" v-if="navTab === tab.id"></view>
      </view>
    </view>

    <!-- 浮动操作按钮 -->
    <view class="floating-action-button" @tap="showActionMenu">
      <view class="fab-content">
        <view class="fab-icon-plus"></view>
        <text class="fab-text">发布</text>
      </view>
    </view>

    <!-- 底部占位 -->
    <view class="safe-area-bottom"></view>
    
    <!-- 底部导航栏 -->
    <view class="tab-bar">
      <view 
        class="tab-item" 
        v-for="(tab, index) in visibleTabs" 
        :key="index"
        :class="{ active: navTab === tab.id }"
        @tap="switchNavTab(tab.id)">
        <view class="active-indicator" v-if="navTab === tab.id"></view>
        <view class="tab-icon" :class="tab.icon"></view>
        <text class="tab-text">{{ tab.text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
// 移除TabBar导入
import { ref, reactive, onMounted, computed } from 'vue';

export default {
  name: 'MarketingCenter',
  // 移除TabBar组件注册
  components: {
  },
  data() {
    return {
      currentTab: 2, // 当前是营销中心
      hoveredTool: null,
      dateRange: '2023-04-01 ~ 2023-04-15',
      currentTab: 0, // 当前选中的内容标签（概览、优惠券、满减等）
      navTab: 2, // 底部导航栏当前选中标签为营销中心
      tabList: ['概览', '拼团', '分销', '优惠券', '满减', '秒杀', '会员'],
      
      // 图表数据
      chartData: [
        { date: '4/10', revenue: 8500, conversion: 3.2, revenueHeight: 35, conversionHeight: 32 },
        { date: '4/11', revenue: 10200, conversion: 3.8, revenueHeight: 42, conversionHeight: 38 },
        { date: '4/12', revenue: 9800, conversion: 3.6, revenueHeight: 40, conversionHeight: 36 },
        { date: '4/13', revenue: 12400, conversion: 4.5, revenueHeight: 51, conversionHeight: 45 },
        { date: '4/14', revenue: 15000, conversion: 5.2, revenueHeight: 62, conversionHeight: 52 },
        { date: '4/15', revenue: 13600, conversion: 4.8, revenueHeight: 56, conversionHeight: 48 },
        { date: '4/16', revenue: 14800, conversion: 5.1, revenueHeight: 60, conversionHeight: 51 }
      ],
      
      // 优惠券列表
      couponsList: [
        {
          id: 1,
          title: '新客专享优惠',
          status: 'active',
          statusText: '进行中',
          value: 10,
          minSpend: 100,
          expireDate: '2023-05-15',
          usedCount: 234,
          totalCount: 500,
          conversionRate: 46.8
        },
        {
          id: 2,
          title: '满减优惠券',
          status: 'active',
          statusText: '进行中',
          value: 20,
          minSpend: 200,
          expireDate: '2023-05-20',
          usedCount: 156,
          totalCount: 300,
          conversionRate: 52.0
        },
        {
          id: 3,
          title: '节日特别券',
          status: 'upcoming',
          statusText: '未开始',
          value: 50,
          minSpend: 300,
          expireDate: '2023-06-10',
          usedCount: 0,
          totalCount: 200,
          conversionRate: 0
        }
      ],
      
      // 满减活动列表
      discountList: [
        {
          id: 1,
          title: '春季促销活动',
          rules: [
            { minAmount: 100, discountAmount: 10 },
            { minAmount: 200, discountAmount: 25 },
            { minAmount: 300, discountAmount: 50 }
          ],
          timeRange: '2023-04-01 ~ 2023-04-30',
          usageCount: 352,
          totalDiscount: 8562.50
        },
        {
          id: 2,
          title: '周末特惠',
          rules: [
            { minAmount: 150, discountAmount: 15 },
            { minAmount: 300, discountAmount: 40 }
          ],
          timeRange: '每周五至周日',
          usageCount: 126,
          totalDiscount: 3240.00
        }
      ],
      
      // 分销数据
      distributionStats: {
        distributors: 128,
        commission: '15,682.50',
        orders: 356
      },
      
      marketingData: {
        revenue: 23586.50,
        revenueTrend: 'up',
        revenueGrowth: '15.2%',
        conversion: 5.8,
        conversionTrend: 'up',
        conversionGrowth: '0.8%',
        roi: 286,
        roiTrend: 'up',
        roiGrowth: '23%',
        customers: 168,
        customersTrend: 'up',
        customersGrowth: '12%'
      },
      
      activeCampaigns: [
        {
          id: 1,
          name: '春季新品5折优惠',
          status: '进行中',
          statusClass: 'active',
          timeRange: '2023-04-01 ~ 2023-04-20',
          views: 3862,
          conversions: 215,
          revenue: 15632.50
        },
        {
          id: 2,
          name: '满300减50活动',
          status: '即将结束',
          statusClass: 'ending',
          timeRange: '2023-04-05 ~ 2023-04-18',
          views: 2451,
          conversions: 128,
          revenue: 7954.00
        },
        {
          id: 3,
          name: '会员专享折扣',
          status: '进行中',
          statusClass: 'active',
          timeRange: '2023-04-10 ~ 2023-04-30',
          views: 1752,
          conversions: 86,
          revenue: 4328.00
        }
      ],
      
      marketingTools: [
        {
          id: 1,
          name: '优惠券',
          description: '创建多样化的优惠券',
          icon: 'coupon',
          class: 'coupon',
          path: '/coupon/management',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 12v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-8"/><path d="M18 5.5V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v1.5"/><path d="M2 12h20"/><path d="M12 2v20"/><path d="M8 16h.01"/><path d="M16 16h.01"/><path d="M8 19h.01"/><path d="M16 19h.01"/></svg>'
        },
        {
          id: 2,
          name: '满减活动',
          description: '设置满额减免活动',
          icon: 'discount',
          class: 'discount',
          path: '/discount/management',
          directPath: '/subPackages/merchant-admin-marketing/pages/marketing/discount/management',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 15-5-5 5-5"/><path d="M4 10h16"/><path d="M15 9v6"/><path d="M12 15h6"/><circle cx="9" cy="20" r="1"/><circle cx="20" cy="20" r="1"/></svg>'
        },
        {
          id: 3,
          name: '拼团活动',
          description: '创建团购优惠活动',
          icon: 'group',
          class: 'group',
          path: '/group/management',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="17" cy="5" r="3"/><circle cx="7" cy="5" r="3"/><circle cx="17" cy="19" r="3"/><circle cx="7" cy="19" r="3"/><path d="M8 14h8"/><path d="M12 8v6"/></svg>'
        },
        {
          id: 4,
          name: '限时秒杀',
          description: '限时限量特价活动',
          icon: 'flash',
          class: 'flash',
          path: '/flash/management',
          directPath: '/subPackages/merchant-admin-marketing/pages/marketing/flash/management',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/></svg>'
        },
        {
          id: 5,
          name: '积分商城',
          description: '设置积分兑换商品',
          icon: 'points',
          class: 'points',
          path: '/points/management',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/></svg>'
        },
        {
          id: 6,
          name: '分销系统',
          description: '设置分销规则与佣金',
          icon: 'distribution',
          class: 'distribution',
          path: '/distribution/index',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14"/><path d="M5 12h14"/><path d="M19 5v14"/><path d="M5 5v14"/></svg>'
        },
        {
          id: 7,
          name: '会员特权',
          description: '设置会员专属优惠',
          icon: 'member',
          class: 'member',
          path: '/member/index',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>'
        },
        {
          id: 8,
          name: '红包营销',
          description: '发放现金红包活动',
          icon: 'redpacket',
          class: 'redpacket',
          path: '/redpacket/index',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z"/><path d="M4 14h16a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2z"/><path d="M2 10h20"/></svg>'
        },
        {
          id: 9,
          name: '营销自动化',
          description: '自动触发的营销流程',
          icon: 'automation',
          class: 'automation',
          path: '/automation/index',
          svg: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/></svg>'
        }
      ],
      
      advancedTools: [
        {
          id: 1,
          title: '智能营销助手',
          subtitle: 'AI驱动的营销策划工具',
          description: '基于历史数据和行业洞察，智能生成最优营销策略与执行方案',
          icon: '/static/images/ai-marketing.png',
          class: 'ai',
          tags: ['AI驱动', '数据分析', '自动优化'],
          path: '/ai/index'
        },
        {
          id: 2,
          title: '客户群体细分',
          subtitle: '精准划分客户群体',
          description: '对客户进行多维度分析与分类，实现更精准的营销触达',
          icon: '/static/images/user-segment.png',
          class: 'segment',
          tags: ['客户分析', '精准营销', '提升转化'],
          path: '/segment/index'
        },
        {
          id: 3,
          title: '营销自动化',
          subtitle: '设置自动触发营销流',
          description: '基于客户行为自动触发相应营销动作，提高运营效率',
          icon: '/static/images/marketing-automation.png',
          class: 'automation',
          tags: ['自动触发', '多渠道', '效率提升'],
          path: '/automation/index'
        }
      ],
      
      calendarEvents: [
        {
          id: 1,
          day: '15',
          month: '4月',
          title: '春季新品发布会',
          type: '新品发布',
          typeClass: 'new-product',
          time: '10:00 - 12:00'
        },
        {
          id: 2,
          day: '18',
          month: '4月',
          title: '限时特惠活动',
          type: '折扣活动',
          typeClass: 'discount',
          time: '全天'
        },
        {
          id: 3,
          day: '22',
          month: '4月',
          title: '会员专享日',
          type: '会员活动',
          typeClass: 'member',
          time: '全天'
        }
      ],
      
      marketingInsights: [
        {
          id: 1,
          iconType: 'insight',
          title: '客群扩展机会',
          description: '您当前的25-35岁女性客户转化率较高，建议针对此群体增加营销预算，预计可提高20%销售额',
        },
        {
          id: 2,
          iconType: 'warning',
          title: '活动优化建议',
          description: '满300减50活动的转化率低于平均水平，建议调整为满200减40，预计可提高15%的转化',
        },
        {
          id: 3,
          iconType: 'opportunity',
          title: '节日营销机会',
          description: '五一假期即将到来，根据往年数据，建议提前7天开始促销活动，预计可增加30%的销售额',
        }
      ],
      
      // 底部导航栏显示的导航项（前4个核心功能 + 更多）
      visibleTabs: [
        {
          id: 0,
          icon: 'dashboard',
          text: '商家中心',
          url: '/subPackages/merchant-admin-home/pages/merchant-home/index'
        },
        {
          id: 1,
          icon: 'store',
          text: '店铺管理',
          url: '/subPackages/merchant-admin/pages/store/index'
        },
        {
          id: 2,
          icon: 'marketing',
          text: '营销中心',
          url: '/subPackages/merchant-admin-marketing/pages/marketing/index'
        },
        {
          id: 3,
          icon: 'orders',
          text: '订单管理',
          url: '/subPackages/merchant-admin-order/pages/order/index'
        },
        {
          id: 'more',
          icon: 'more',
          text: '更多',
          url: ''
        }
      ]
    }
  },
  computed: {
    // 计算营销收入区域路径
    revenueClipPath() {
      const points = this.chartData.map((item, index) => {
        const x = index * (100/(this.chartData.length-1));
        const y = 100 - item.revenueHeight;
        return `${x}% ${y}%`;
      });
      
      // 添加首尾连接点，形成多边形
      const lastX = 100;
      const lastY = 100;
      const firstX = 0;
      const firstY = 100;
      
      return `polygon(${points.join(', ')}, ${lastX}% ${lastY}%, ${firstX}% ${firstY}%)`;
    },
    
    // 计算转化率区域路径
    conversionClipPath() {
      const points = this.chartData.map((item, index) => {
        const x = index * (100/(this.chartData.length-1));
        const y = 100 - item.conversionHeight;
        return `${x}% ${y}%`;
      });
      
      // 添加首尾连接点，形成多边形
      const lastX = 100;
      const lastY = 100;
      const firstX = 0;
      const firstY = 100;
      
      return `polygon(${points.join(', ')}, ${lastX}% ${lastY}%, ${firstX}% ${firstY}%)`;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    formatNumber(number) {
      return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    switchTab(index) {
      this.currentTab = index;
    },
    switchNavTab(tabId) {
      // 处理"更多"选项
      if (tabId === 'more') {
        this.showMoreOptions();
        return;
      }
      
      // 如果点击的是当前选中的标签，则不进行跳转
      if (tabId === this.navTab) return;
      
      this.navTab = tabId;
      
      // 特殊处理营销中心标签
      if (tabId === 2) {
        // 当前已经在营销中心页面，不需要跳转
        return;
      }
      
      // 使用redirectTo而不是navigateTo，避免堆栈过多
      uni.redirectTo({
        url: this.visibleTabs[tabId].url,
        fail: (err) => {
          console.error('redirectTo失败:', err);
          // 如果redirectTo失败，尝试使用switchTab
          uni.switchTab({
            url: this.visibleTabs[tabId].url,
            fail: (switchErr) => {
              console.error('switchTab也失败:', switchErr);
              uni.showToast({
                title: '页面跳转失败，请稍后再试',
                icon: 'none'
              });
            }
          });
        }
      });
    },
    viewAllCampaigns() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/campaign-list'
      });
    },
    viewCampaignDetail(campaign) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/campaign-detail?id=${campaign.id}`
      });
    },
    navigateToTool(tool) {
      // 使用Vue3的Composition API进行路由导航
      let route = '';
      
      // 根据工具类型确定正确的路由路径
      switch(tool.id) {
        case 2: // 满减活动
          route = '/subPackages/merchant-admin-marketing/pages/marketing/discount/management';
          break;
        case 4: // 限时秒杀
          route = `/subPackages/merchant-admin-marketing/pages/marketing${tool.path}`;
          break;
        case 6: // 分销系统
          route = '/subPackages/merchant-admin-marketing/pages/marketing/distribution/index';
          break;
        case 7: // 会员特权
          route = '/subPackages/merchant-admin-marketing/pages/marketing/member/index';
          break;
        case 8: // 红包营销
          route = '/subPackages/merchant-admin-marketing/pages/marketing/redpacket/index';
          break;
        case 9: // 核销中心
          route = '/subPackages/merchant-admin-marketing/pages/marketing/verification/index';
          break;
        default:
          // 其他工具使用原有路径
          route = `/subPackages/merchant-admin-marketing/pages/marketing${tool.path}`;
      }
      
      // 添加过渡动画参数
      uni.navigateTo({
        url: route,
        animationType: 'slide-in-right',
        animationDuration: 300,
        success: () => {
          // 使用Vue3的响应式API记录用户行为
          uni.$emit('marketing-tool-click', {
            toolId: tool.id,
            toolName: tool.name,
            timestamp: Date.now()
          });
        },
        fail: (err) => {
          console.error('导航失败:', err);
          // 页面不存在时的优雅降级处理
          uni.showToast({
            title: '功能正在建设中',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },
    navigateToAdvancedTool(tool) {
      // 确保路径正确
      let route = `/subPackages/merchant-admin-marketing/pages/marketing${tool.path}`;
      
      // 打印路径便于调试
      console.log('导航路径:', route);
      
      uni.navigateTo({
        url: route,
        animationType: 'slide-in-right',
        animationDuration: 300,
        success: () => {
          // 记录用户行为
          uni.$emit('advanced-tool-click', {
            toolId: tool.id,
            toolName: tool.title,
            timestamp: Date.now()
          });
        },
        fail: (err) => {
          console.error('导航失败:', err);
          // 页面不存在时的优雅降级处理
          uni.showToast({
            title: '高级功能正在建设中',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },
    manageEvent(event) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/event-detail?id=${event.id}`
      });
    },
    addNewEvent() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/create-event'
      });
    },
    createNewCampaign() {
      uni.showActionSheet({
        itemList: ['创建优惠券', '创建满减活动', '创建拼团活动', '创建秒杀活动', '创建分销计划'],
        success: (res) => {
          const urls = [
            '/subPackages/merchant-admin-marketing/pages/marketing/coupon/create',
            '/subPackages/merchant-admin-marketing/pages/marketing/discount/create',
            '/subPackages/merchant-admin-marketing/pages/marketing/group/create',
            '/subPackages/merchant-admin-marketing/pages/marketing/flash/create',
            '/subPackages/merchant-admin-marketing/pages/marketing/distribution/create'
          ];
          
          uni.navigateTo({
            url: urls[res.tapIndex]
          });
        }
      });
    },
    applyInsight(insight) {
      uni.showToast({
        title: '已应用该建议',
        icon: 'success'
      });
    },
    ignoreInsight(insight) {
      uni.showToast({
        title: '已忽略该建议',
        icon: 'none'
      });
    },
    
    // 优惠券相关方法
    createCoupon() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/create-coupon'
      });
    },
    viewCouponDetail(coupon) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/coupon-detail?id=${coupon.id}`
      });
    },
    
    // 满减相关方法
    createDiscount() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/discount/create'
      });
    },
    viewDiscountDetail(discount) {
      uni.navigateTo({
        url: `/subPackages/merchant-admin-marketing/pages/marketing/discount/detail?id=${discount.id}`
      });
    },
    
    // 拼团相关方法
    createGroup() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/create-group'
      });
    },
    
    // 秒杀相关方法
    createFlash() {
      console.log('正在跳转到秒杀活动创建页面');
      uni.navigateTo({
        url: '/pages/marketing/flash/create',
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '跳转失败，请检查路径',
            icon: 'none'
          });
        }
      });
    },
    
    // 分销相关方法
    manageDistribution() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/distribution/index'
      });
    },
    
    // 工具卡片交互相关方法
    setHoveredTool(toolId) {
      this.hoveredTool = toolId;
    },
    
    clearHoveredTool() {
      this.hoveredTool = null;
    },
    
    // 已用switchNavTab方法替代，此方法不再需要
    
    showMoreOptions() {
      // 准备更多菜单中的选项
      const moreOptions = ['客户运营', '分析洞察', '系统设置'];
      
      uni.showActionSheet({
        itemList: moreOptions,
        success: (res) => {
          // 根据选择的选项进行跳转
          const routes = [
            '/subPackages/merchant-admin-customer/pages/customer/index',
            '/subPackages/merchant-admin/pages/settings/index'
          ];
          uni.navigateTo({
            url: routes[res.tapIndex]
          });
        }
      });
    },
    
    showPublishOptions() {
      uni.showActionSheet({
        itemList: ['发布优惠券', '发布满减活动', '发布拼团活动', '发布秒杀活动'],
        success: (res) => {
          const actions = [
            this.createCoupon,
            this.createDiscount,
            this.createGroup,
            this.createFlash
          ];
          
          if (actions[res.tapIndex]) {
            actions[res.tapIndex]();
          }
        }
      });
    },
    
    // 智能营销助手相关方法
    viewAllAiTools() {
      uni.navigateTo({
        url: '/subPackages/merchant-admin-marketing/pages/marketing/ai/index'
      });
    },
    
    useAiTool(toolType) {
      switch(toolType) {
        case 'trend':
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/ai/trend-analysis'
          });
          break;
        case 'price':
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/ai/price-monitor'
          });
          break;
        case 'forecast':
          uni.navigateTo({
            url: '/subPackages/merchant-admin-marketing/pages/marketing/ai/sales-forecast'
          });
          break;
        default:
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          });
      }
    },
    
    showActionMenu() {
      uni.showActionSheet({
        itemList: ['发布优惠券', '发布满减活动', '发布拼团活动', '发布秒杀活动'],
        success: (res) => {
          const actions = [
            this.createCoupon,
            this.createDiscount,
            this.createGroup,
            this.createFlash
          ];
          
          if (actions[res.tapIndex]) {
            actions[res.tapIndex]();
          }
        }
      });
    },
    
    // 添加处理标签页切换的方法
    handleTabChange(tabId) {
      console.log('切换到标签:', tabId);
      this.navTab = tabId === 'marketing' ? 2 : 0;
    }
  }
}
</script>

<style lang="scss">
.marketing-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  position: relative;
}

.page-content {
  height: calc(100vh - 77px); /* 减去导航栏高度 */
  box-sizing: border-box;
  padding-bottom: 60px; /* 添加底部空间，避免内容被底部导航栏遮挡 */
}

.bottom-space {
  height: 60px; /* 底部导航栏高度 + 一些额外空间 */
}

/* 浮动操作按钮样式已经在底部导航样式中定义 */

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  color: #fff;
  padding: 44px 16px 10px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(255, 120, 0, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 概览部分样式 */
.overview-section {
  margin: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.overview-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-picker {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.date-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}

/* 数据卡片样式 */
.data-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.data-card {
  width: 50%;
  padding: 7.5px;
  box-sizing: border-box;
}

.card-content {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  border-left: 3px solid #FF7600;
}

.card-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.card-label {
  font-size: 12px;
  color: #999;
}

.card-trend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 12px;
  margin-top: 8px;
}

.card-trend.up {
  color: #34C759;
}

.card-trend.down {
  color: #FF3B30;
}

.trend-arrow {
  width: 0;
  height: 0;
  margin-right: 3px;
}

.card-trend.up .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #34C759;
}

.card-trend.down .trend-arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #FF3B30;
}

/* 工具导航样式 */
.tool-navigation {
  position: sticky;
  top: 77px; /* 导航栏高度 */
  z-index: 99;
  background: #fff;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.tab-scroll {
  white-space: nowrap;
  padding: 0 8px;
  height: 56px; /* 增加高度容纳更大字体 */
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* 提高iOS滑动体验 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏滚动条 */
.tab-scroll::-webkit-scrollbar {
  display: none;
}

/* 直接给tab-text设置样式，这是实际显示文本的元素 */
.tool-navigation .tab-scroll .tab-item .tab-text {
  font-size: 15px !important; /* 调整为15px */
  font-weight: 700 !important; /* 加粗显示 */
}

.tool-navigation .tab-scroll .tab-item.active .tab-text {
  font-size: 16px !important; /* 调整为16px */
  font-weight: 800 !important; /* 更粗的字体 */
  color: #FF7600;
}

/* 增加更多样式特异性来确保生效 */
.tab-scroll text.tab-text {
  font-size: 15px !important;
  font-weight: 700 !important; /* 加粗显示 */
}

.tab-scroll .tab-item.active text.tab-text {
  font-size: 16px !important;
  font-weight: 800 !important; /* 更粗的字体 */
}

.tool-navigation .tab-item {
  display: inline-block;
  padding: 0 12px; /* 稍微减少内边距 */
  height: 46px; /* 增加高度 */
  line-height: 46px; /* 增加行高 */
  color: #666;
  position: relative;
  margin: 0 4px; /* 增加标签间距 */
  transition: all 0.2s ease; /* 添加过渡动画 */
  text-align: center; /* 文字居中 */
  -webkit-tap-highlight-color: transparent; /* 去除默认点击高亮 */
}

.tool-navigation .tab-item.active {
  color: #FF7600;
  font-weight: 700; /* 加粗激活的标签 */
  font-size: 26px !important; /* 使用!important确保样式生效 */
  text-shadow: 0 1px 2px rgba(255, 118, 0, 0.1); /* 添加文字阴影 */
}

/* 为第一个和最后一个标签添加特殊间距，让边缘美观 */
.tool-navigation .tab-item:first-child {
  margin-left: 2px;
}

.tool-navigation .tab-item:last-child {
  margin-right: 2px;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15px;
  right: 15px;
  height: 4px; /* 增加下划线高度 */
  background: #FF7600;
  border-radius: 4px 4px 0 0; /* 调整下划线圆角 */
}

/* 图表部分样式 */
.chart-section {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.filter-dropdown {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 15px;
  padding: 5px 10px;
}

.selected-value {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.dropdown-arrow {
  width: 8px;
  height: 8px;
  border-top: 1.5px solid #666;
  border-right: 1.5px solid #666;
  transform: rotate(135deg);
}

.chart-container {
  height: auto;
  margin: 10px 0;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.chart-area {
  position: relative;
  height: 220px;
  margin: 10px 0 30px;
  background-color: #FCFCFF;
  border-radius: 8px;
  border: 1px solid rgba(200, 210, 230, 0.3);
  padding: 20px;
  box-sizing: border-box;
}

.y-axis-labels {
  position: absolute;
  left: 10px;
  top: 10px;
  bottom: 30px;
  width: 30px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.y-label {
  font-size: 11px;
  color: #8E8E93;
  text-align: right;
  transform: translateY(-50%);
}

.chart-grid {
  position: absolute;
  left: 45px;
  right: 15px;
  top: 10px;
  bottom: 30px;
  z-index: 1;
}

.h-grid-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: rgba(200, 210, 230, 0.3);
}

.chart-column {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  z-index: 2;
}

.v-grid-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: rgba(200, 210, 230, 0.2);
}

.x-label {
  position: absolute;
  font-size: 11px;
  color: #8E8E93;
  transform: translateX(-50%);
  text-align: center;
  bottom: -25px;
  white-space: nowrap;
}

.chart-lines-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.revenue-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 118, 0, 0.2), rgba(255, 118, 0, 0.05));
  opacity: 0.8;
  z-index: 2;
  border-top: 2px solid #FF7600;
}

.conversion-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 122, 255, 0.2), rgba(0, 122, 255, 0.05));
  opacity: 0.8;
  z-index: 2;
  border-top: 2px solid #007AFF;
}

.data-point {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  transform: translate(-50%, 50%);
  z-index: 10;
  background-color: #fff;
}

.data-point.revenue {
  border: 2px solid #FF7600;
  box-shadow: 0 1px 3px rgba(255, 118, 0, 0.2);
}

.data-point.conversion {
  border: 2px solid #007AFF;
  box-shadow: 0 1px 3px rgba(0, 122, 255, 0.2);
}

.chart-legend {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 5px;
}

.legend-color.revenue {
  background: #FF7600;
}

.legend-color.conversion {
  background: #007AFF;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

/* 优惠券标签样式 */
.tab-content {
  margin: 15px;
}

.tool-detail-section {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  margin-bottom: 15px;
}

.add-tool-btn {
  display: flex;
  align-items: center;
  background: #FF7600;
  border-radius: 15px;
  padding: 5px 10px;
  color: white;
}

.btn-text {
  font-size: 13px;
  margin-right: 5px;
}

.plus-icon-small {
  width: 12px;
  height: 12px;
  position: relative;
}

.plus-icon-small:before,
.plus-icon-small:after {
  content: '';
  position: absolute;
  background: white;
}

.plus-icon-small:before {
  width: 12px;
  height: 2px;
  top: 5px;
  left: 0;
}

.plus-icon-small:after {
  height: 12px;
  width: 2px;
  left: 5px;
  top: 0;
}

/* 优惠券卡片样式 */
.tool-cards {
  margin-top: 15px;
}

.coupon-card {
  background: linear-gradient(135deg, #FFF8F2, #FFFFFF);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(255, 118, 0, 0.1);
  border-left: 4px solid #FF7600;
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.coupon-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.coupon-status {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  color: white;
}

.status-active {
  background: #34C759;
}

.status-expired {
  background: #8E8E93;
}

.status-upcoming {
  background: #FF9500;
}

.coupon-value {
  font-size: 24px;
  font-weight: bold;
  color: #FF7600;
  margin: 10px 0;
}

.discount-symbol {
  font-size: 16px;
  margin-right: 2px;
}

.coupon-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 13px;
  color: #666;
}

.coupon-stats {
  display: flex;
  border-top: 1px dashed #E5E5EA;
  padding-top: 10px;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8E8E93;
}

/* 满减活动样式 */
.discount-list {
  margin-top: 15px;
}

.discount-item {
  display: flex;
  background: #FFFFFF;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #FF7600;
  position: relative;
}

.discount-content {
  flex: 2;
}

.discount-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.discount-rules {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.rule-item {
  background: rgba(255, 118, 0, 0.1);
  border-radius: 12px;
  padding: 3px 8px;
  margin-right: 8px;
  margin-bottom: 5px;
}

.rule-text {
  font-size: 12px;
  color: #FF7600;
  font-weight: 500;
}

.discount-time {
  font-size: 12px;
  color: #8E8E93;
}

.discount-stats {
  flex: 1;
  padding-left: 15px;
  border-left: 1px dashed #E5E5EA;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.item-arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 10px;
  height: 10px;
  border-top: 2px solid #C7C7CC;
  border-right: 2px solid #C7C7CC;
  transform: rotate(45deg);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 40px 15px;
  margin-top: 20px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 118, 0, 0.1);
  border-radius: 40px;
  margin-bottom: 20px;
  position: relative;
}

.empty-icon:before,
.empty-icon:after {
  content: '';
  position: absolute;
  background: #FF7600;
}

.empty-icon:before {
  width: 40px;
  height: 4px;
  top: 38px;
  left: 20px;
}

.empty-icon:after {
  width: 4px;
  height: 40px;
  left: 38px;
  top: 20px;
}

.empty-text {
  font-size: 16px;
  color: #8E8E93;
  margin-bottom: 20px;
}

.empty-action {
  background: #FF7600;
  border-radius: 20px;
  padding: 10px 20px;
}

.action-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

/* 分销概览样式 */
.distribution-overview {
  margin-bottom: 15px;
}

.overview-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  margin-bottom: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.overview-period {
  background: #F5F7FA;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  color: #666;
}

.overview-stats {
  display: flex;
  justify-content: space-around;
}

.stat-box {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #FF7600;
  margin-bottom: 5px;
}

.stat-desc {
  font-size: 13px;
  color: #8E8E93;
}

.distribution-settings {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-text {
  font-size: 15px;
  color: #333;
}

.settings-arrow {
  width: 10px;
  height: 10px;
  border-top: 2px solid #C7C7CC;
  border-right: 2px solid #C7C7CC;
  transform: rotate(45deg);
}

/* 活动列表样式 */
.active-campaigns {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.view-all {
  font-size: 14px;
  color: #FF7600;
}

.campaign-list {
  margin-top: 10px;
}

.campaign-item {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.campaign-status {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 10px;
  z-index: 2; /* 确保状态标签在最上层 */
}

.campaign-status.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.campaign-status.ending {
  background: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.campaign-info {
  margin-bottom: 15px;
  position: relative;
  width: 100%; /* 确保宽度占满，便于管理布局 */
}

.campaign-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  margin-right: 60px; /* 为状态标签预留空间 */
  display: block; /* 确保名称单独一行 */
}

.campaign-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.campaign-time {
  font-size: 12px;
  color: #999;
  display: block; /* 确保时间单独一行 */
  width: auto; /* 根据内容自动调整宽度 */
  max-width: 60%; /* 最大宽度限制 */
  text-overflow: ellipsis; /* 文本溢出显示省略号 */
  white-space: nowrap; /* 防止换行 */
  overflow: hidden; /* 隐藏溢出部分 */
}

.campaign-metrics {
  display: flex;
}

.metric-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
  border-right: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-right: none;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}

.metric-label {
  font-size: 12px;
  color: #999;
}

/* 营销工具网格 */
.tool-section {
  margin: 10px 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.tool-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -7.5px;
}

.tool-card {
  width: 25%;
  padding: 7.5px;
  box-sizing: border-box;
  margin-bottom: 15px;
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  position: relative;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.tool-card.hover {
  transform: translateY(-5px) scale(1.03);
}

.tool-icon-wrap {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  background: white;
  position: relative;
  overflow: hidden;
}

.tool-card.hover .tool-icon-wrap {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.tool-icon-wrap::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  border-radius: 50%;
  z-index: 1;
}

.tool-icon-svg {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  transition: all 0.3s ease;
}

.tool-icon-wrap.coupon {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}

.tool-icon-wrap.discount {
  background: linear-gradient(135deg, #FDEB71, #F8D800);
}

.tool-icon-wrap.group {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

.tool-icon-wrap.flash {
  background: linear-gradient(135deg, #FF6B6B, #F04172);
}

.tool-icon-wrap.points {
  background: linear-gradient(135deg, #38ADAE, #30CE9B);
}

.tool-icon-wrap.distribution {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
}

.tool-icon-wrap.member {
  background: linear-gradient(135deg, #8E2DE2, #4A00E0);
}

.tool-icon-wrap.redpacket {
  background: linear-gradient(135deg, #FF5858, #FF0000);
}

/* SVG图标样式 */
.tool-icon-svg {
  color: #fff;
}

.tool-name {
  font-size: 14px;
  color: #333;
  text-align: center;
  margin-bottom: 3px;
  font-weight: 500;
  transition: color 0.3s ease;
  width: 100%;
  display: inline-block;
}

.tool-name.three-chars {
  letter-spacing: 2px;
  text-indent: 2px;
}

.tool-card.hover .tool-name {
  color: #FF7600;
}

.tool-desc {
  font-size: 10px;
  color: #999;
  text-align: center;
  height: 28px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  transition: color 0.3s ease;
}

/* 高级营销功能样式 */
.advanced-section {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.advanced-tools {
  margin-top: 10px;
}

.advanced-tool {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.tool-top {
  display: flex;
  margin-bottom: 10px;
}

.tool-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 15px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.tool-icon.ai {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
}

.tool-icon.segment {
  background: linear-gradient(135deg, #4481EB, #04BEFE);
}

.tool-icon.automation {
  background: linear-gradient(135deg, #FFB74D, #FF9800);
}

.icon-image {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.tool-info {
  flex: 1;
}

.tool-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.tool-subtitle {
  font-size: 12px;
  color: #666;
}

.tool-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10px;
}

.tool-tags {
  display: flex;
  flex-wrap: wrap;
}

.tool-tag {
  font-size: 10px;
  color: #FF7600;
  background: rgba(255, 118, 0, 0.1);
  padding: 3px 8px;
  border-radius: 10px;
  margin-right: 8px;
  margin-bottom: 5px;
}

/* 营销日历样式 */
.calendar-section {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.calendar-month {
  font-size: 14px;
  color: #999;
}

.calendar-events {
  margin-top: 10px;
}

.event-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.event-item:last-child {
  border-bottom: none;
}

.event-date {
  width: 50px;
  height: 50px;
  background: #F8FAFC;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.date-day {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.date-month {
  font-size: 12px;
  color: #999;
}

.event-content {
  flex: 1;
  margin-right: 15px;
}

.event-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.event-info {
  display: flex;
  align-items: center;
}

.event-type {
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 10px;
}

.event-type.new-product {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.event-type.discount {
  background: rgba(255, 69, 58, 0.1);
  color: #FF453A;
}

.event-type.member {
  background: rgba(88, 86, 214, 0.1);
  color: #5856D6;
}

.event-time {
  font-size: 12px;
  color: #999;
}

.event-action {
  background: #F8FAFC;
  border-radius: 15px;
  padding: 5px 10px;
}

.action-text {
  font-size: 12px;
  color: #FF7600;
}

.add-event-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  margin-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.plus-icon {
  width: 16px;
  height: 16px;
  background: #FF7600;
  border-radius: 8px;
  position: relative;
  margin-right: 8px;
}

.plus-icon::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 2px;
  background: #fff;
  top: 7px;
  left: 4px;
}

.plus-icon::after {
  content: '';
  position: absolute;
  width: 2px;
  height: 8px;
  background: #fff;
  top: 4px;
  left: 7px;
}

.btn-text {
  font-size: 14px;
  color: #FF7600;
}

/* AI营销建议样式 */
.ai-insights {
  margin: 0 15px 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.ai-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.ai-badge {
  background: linear-gradient(135deg, #9040FF, #5E35B1);
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.ai-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.insights-list {
  margin-top: 10px;
}

.insight-item {
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.insight-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  margin-right: 12px;
  margin-bottom: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.insight-icon.insight {
  background: rgba(52, 199, 89, 0.1);
  position: relative;
}

.insight-icon.insight::before {
  content: '';
  width: 18px;
  height: 18px;
  background: #34C759;
  border-radius: 9px;
  position: absolute;
}

.insight-icon.insight::after {
  content: 'i';
  color: #fff;
  font-weight: bold;
  font-size: 14px;
  position: relative;
  z-index: 1;
}

.insight-icon.warning {
  background: rgba(255, 149, 0, 0.1);
  position: relative;
}

.insight-icon.warning::before {
  content: '!';
  color: #FF9500;
  font-weight: bold;
  font-size: 20px;
}

.insight-icon.opportunity {
  background: rgba(88, 86, 214, 0.1);
  position: relative;
}

.insight-icon.opportunity::before {
  content: '+';
  color: #5856D6;
  font-weight: bold;
  font-size: 20px;
}

.insight-content {
  margin-bottom: 15px;
}

.insight-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.insight-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.insight-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  margin-left: 10px;
}

.action-btn.primary {
  background: #FF7600;
  color: #fff;
}

.action-btn.secondary {
  background: #F0F0F0;
  color: #666;
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: #FF7600;
  box-shadow: 0 4px 15px rgba(255, 118, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.fab-icon {
  font-size: 28px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin-top: -2px;
}

/* 响应式调整 */
@media screen and (max-width: 350px) {
  .tool-card {
    width: 33.33%;
  }
}

/* 智能营销助手样式 */
.ai-marketing-section {
  margin: 15px;
  padding: 15px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.ai-tools-grid {
  margin-top: 10px;
}

.ai-tool-card {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background: #F8FAFC;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.ai-tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.ai-tool-icon.blue {
  background-color: rgba(25, 137, 250, 0.1);
}

.ai-tool-icon.yellow {
  background-color: rgba(255, 149, 0, 0.1);
}

.ai-tool-icon.green {
  background-color: rgba(52, 199, 89, 0.1);
}

.ai-tool-content {
  flex: 1;
}

.ai-tool-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.ai-tool-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

/* 底部导航栏样式调整，确保图标正确显示 */
::v-deep .tab-icon {
  display: block !important;
}

/* 确保底部导航栏的SVG图标正确显示 */
::v-deep .tab-icon {
  svg {
    width: 22px;
    height: 22px;
    fill: currentColor;
  }
}

/* 营销中心特定的样式调整 */
::v-deep .tab-item.active[data-tab="marketing"] .tab-icon {
  color: #FF7600;
}

::v-deep .tab-item.active[data-tab="marketing"] .tab-text {
  color: #FF7600;
}

/* 底部导航栏 */
.tab-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  background-color: var(--bg-primary, #FFFFFF);
  border-top: 1px solid var(--border-light, #F0F0F0);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 0 10px;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #FFFFFF;
  flex-shrink: 0;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  transition: all 0.2s ease;
  padding: 8px 0;
  margin: 0 8px;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 3px;
  transition: all 0.25s cubic-bezier(0.3, 0.7, 0.4, 1.5);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 22px;
  opacity: 0.7;
}

.tab-text {
  font-size: 10px;
  color: var(--text-tertiary, #999999);
  transition: color 0.2s ease, transform 0.25s ease;
  transform: scale(0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 0 2px;
}

.active-indicator {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 16px;
  height: 3px;
  border-radius: 0 0 4px 4px;
  background: linear-gradient(90deg, var(--brand-primary, #1677FF), var(--accent-purple, #7265E6));
}

.tab-item.active .tab-icon {
  transform: translateY(-2px);
  opacity: 1;
}

.tab-item.active .tab-text {
  color: var(--brand-primary, #1677FF);
  font-weight: 500;
  transform: scale(1);
}

/* 导航图标样式 */
.tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}

.tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}

.tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'/%3E%3C/svg%3E");
}

.tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}

.tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

@media screen and (max-width: 375px) {
  /* 在较小屏幕上优化导航栏 */
  .tab-text {
    font-size: 9px;
  }
  
  .tab-icon {
    margin-bottom: 2px;
    background-size: 20px;
  }
}

/* 导航图标样式 */
.tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}

.tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}

.tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7600'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}

.tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}

.tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 浮动操作按钮 */
.floating-action-button {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #FF7600, #FF4500);
  box-shadow: 0 4px 16px rgba(255, 118, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-action-button:active {
  transform: scale(0.95) translateY(2px);
  box-shadow: 0 2px 8px rgba(255, 118, 0, 0.2);
}

.fab-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.fab-icon-plus {
  width: 20px;
  height: 20px;
  position: relative;
  margin-bottom: 2px;
}

.fab-icon-plus::before,
.fab-icon-plus::after {
  content: "";
  position: absolute;
  background-color: white;
}

.fab-icon-plus::before {
  width: 20px;
  height: 2px;
  top: 9px;
  left: 0;
}

.fab-icon-plus::after {
  width: 2px;
  height: 20px;
  top: 0;
  left: 9px;
}

.fab-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
  margin-top: 2px;
}

.safe-area-bottom {
  height: calc(60px + env(safe-area-inset-bottom));
}

@media screen and (max-width: 375px) {
  /* 在较小屏幕上优化导航栏 */
  .tab-text {
    font-size: 9px;
  }
  
  .tab-icon {
    margin-bottom: 2px;
    background-size: 20px;
  }
}

/* 在较宽屏幕上优化导航栏，确保元素不会挤压 */
@media screen and (min-width: 400px) {
  .tab-bar {
    padding: 0 10px;
  }
  
  .tab-item {
    margin: 0 5px;
  }
}

/* 导航图标样式 */
.tab-bar .tab-icon.dashboard {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E");
}

.tab-bar .tab-icon.store {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z'/%3E%3C/svg%3E");
}

.tab-bar .tab-icon.marketing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF7600'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}

.tab-bar .tab-icon.orders {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}

.tab-bar .tab-icon.more {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230A84FF'%3E%3Cpath d='M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 底部导航栏 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 56px;
  background-color: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #F0F0F0;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 100;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 0;
  box-sizing: border-box;
  position: relative;
}

.nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
}

.nav-text {
  font-size: 10px;
  color: #999999;
}

.nav-item.active .nav-icon {
  color: #1989FA;
}

.nav-item.active .nav-text {
  color: #1989FA;
}

/* 营销中心用橙色 */
.nav-item.active:nth-child(3) .nav-icon {
  color: #FF7600;
}

.nav-item.active:nth-child(3) .nav-text {
  color: #FF7600;
}

/* 数据概览用蓝色 */
.nav-item.active:nth-child(1) .nav-icon {
  color: #1989FA;
}

.nav-item.active:nth-child(1) .nav-text {
  color: #1989FA;
}

.safe-area-bottom {
  height: calc(56px + env(safe-area-inset-bottom));
}
</style> 