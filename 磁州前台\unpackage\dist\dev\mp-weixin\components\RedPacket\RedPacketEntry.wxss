
.red-packet-card.data-v-851f2d80 {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 18rpx;
  box-shadow: 0 4rpx 16rpx rgba(22,119,255,0.08);
  padding: 24rpx 30rpx;
  margin: 32rpx 0;
  cursor: pointer;
}
.icon.data-v-851f2d80 {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}
.info.data-v-851f2d80 {
  flex: 1;
}
.title.data-v-851f2d80 {
  font-size: 30rpx;
  font-weight: 600;
  color: #1677FF;
}
.desc.data-v-851f2d80 {
  font-size: 24rpx;
  color: #888;
  margin-top: 4rpx;
}
.status.data-v-851f2d80 {
  font-size: 24rpx;
  color: #bbb;
  margin-right: 16rpx;
}
.status.set.data-v-851f2d80 {
  color: #1677FF;
}
.arrow.data-v-851f2d80 {
  width: 32rpx;
  height: 32rpx;
}
.red-packet-setup-popup.data-v-851f2d80 {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx 30rpx 30rpx;
}
.popup-title.data-v-851f2d80 {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
  text-align: center;
  margin-bottom: 30rpx;
}
.form-row.data-v-851f2d80 {
  display: flex;
  align-items: center;
  margin-bottom: 28rpx;
}
.label.data-v-851f2d80 {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}
input.data-v-851f2d80 {
  flex: 1;
  font-size: 28rpx;
  border: none;
  border-bottom: 1rpx solid #eee;
  background: transparent;
  padding: 10rpx 0;
  margin: 0 10rpx;
}
.unit.data-v-851f2d80 {
  font-size: 26rpx;
  color: #888;
}
.picker-value.data-v-851f2d80 {
  flex: 1;
  font-size: 28rpx;
  color: #1677FF;
  padding: 10rpx 0;
}
.primary-btn.data-v-851f2d80 {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #1677FF 60%, #50aaff 100%);
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  margin-top: 30rpx;
  font-weight: 600;
}
