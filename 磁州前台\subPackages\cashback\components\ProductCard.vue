<template>
  <view class="product-card" @tap="navigateToDetail">
    <!-- 商品图片 -->
    <view class="product-image-container">
      <image class="product-image" :src="product.image" mode="aspectFill" />
      <view v-if="product.discount" class="discount-tag">
        <text>{{ product.discount }}</text>
      </view>
    </view>
    
    <!-- 商品信息 -->
    <view class="product-info">
      <!-- 商品平台 -->
      <view class="product-platform">
        <image v-if="product.platformIcon" class="platform-icon" :src="product.platformIcon" mode="aspectFit" />
        <text class="platform-name">{{ product.platformName }}</text>
      </view>
      
      <!-- 商品标题 -->
      <view class="product-title">
        <text>{{ product.title }}</text>
      </view>
      
      <!-- 价格和返利 -->
      <view class="product-price-row">
        <view class="product-price">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{ formatPrice(product.price) }}</text>
        </view>
        <view class="product-cashback">
          <text>返{{ product.cashbackRate }}</text>
        </view>
      </view>
      
      <!-- 销量和收藏 -->
      <view class="product-meta">
        <view class="product-sales">
          <text>已售{{ formatSales(product.sales) }}</text>
        </view>
        <view class="product-favorite" @tap.stop="toggleFavorite">
          <svg class="favorite-icon" viewBox="0 0 24 24" width="16" height="16">
            <path v-if="isFavorite" fill="#FF6B6B" d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z" />
            <path v-else fill="#999999" d="M12.1,18.55L12,18.65L11.89,18.55C7.14,14.24 4,11.39 4,8.5C4,6.5 5.5,5 7.5,5C9.04,5 10.54,6 11.07,7.36H12.93C13.46,6 14.96,5 16.5,5C18.5,5 20,6.5 20,8.5C20,11.39 16.86,14.24 12.1,18.55M16.5,3C14.76,3 13.09,3.81 12,5.08C10.91,3.81 9.24,3 7.5,3C4.42,3 2,5.41 2,8.5C2,12.27 5.4,15.36 10.55,20.03L12,21.35L13.45,20.03C18.6,15.36 22,12.27 22,8.5C22,5.41 19.58,3 16.5,3Z" />
          </svg>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ProductCard',
  props: {
    product: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isFavorite: false
    };
  },
  created() {
    // 初始化收藏状态
    this.isFavorite = this.product.isFavorite || false;
  },
  methods: {
    // 导航到商品详情
    navigateToDetail() {
      uni.navigateTo({
        url: `/subPackages/cashback/pages/detail/index?id=${this.product.id}`
      });
    },
    
    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      this.$emit('favorite', {
        id: this.product.id,
        isFavorite: this.isFavorite
      });
    },
    
    // 格式化价格
    formatPrice(price) {
      return parseFloat(price).toFixed(2);
    },
    
    // 格式化销量
    formatSales(sales) {
      if (sales >= 10000) {
        return (sales / 10000).toFixed(1) + '万';
      }
      return sales;
    }
  }
};
</script>

<style lang="scss" scoped>
.product-card {
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 12px;
  
  .product-image-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    overflow: hidden;
    
    .product-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .discount-tag {
      position: absolute;
      top: 8px;
      left: 8px;
      padding: 4px 8px;
      background: linear-gradient(135deg, #FF6B6B 0%, #FF4F4F 100%);
      border-radius: 12px;
      
      text {
        color: #FFFFFF;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
  
  .product-info {
    padding: 12px;
  }
  
  .product-platform {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    
    .platform-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    
    .platform-name {
      font-size: 12px;
      color: #999999;
    }
  }
  
  .product-title {
    margin-bottom: 8px;
    
    text {
      font-size: 14px;
      color: #333333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  
  .product-price-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    
    .product-price {
      display: flex;
      align-items: baseline;
      
      .price-symbol {
        font-size: 12px;
        color: #FF6B6B;
        margin-right: 2px;
      }
      
      .price-value {
        font-size: 18px;
        font-weight: 600;
        color: #FF6B6B;
      }
    }
    
    .product-cashback {
      padding: 2px 6px;
      background-color: rgba(156, 39, 176, 0.1);
      border-radius: 10px;
      
      text {
        font-size: 12px;
        color: #9C27B0;
      }
    }
  }
  
  .product-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .product-sales {
      text {
        font-size: 12px;
        color: #999999;
      }
    }
    
    .product-favorite {
      padding: 6px;
      margin-right: -6px;
    }
  }
}
</style> 