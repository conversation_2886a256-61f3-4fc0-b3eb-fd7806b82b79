"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      filterTabs: ["全部", "拼团", "优惠券", "秒杀"],
      currentTab: 0,
      dateRange: "最近7天",
      searchKeyword: "",
      showSearchPopup: false,
      showDetailPopup: false,
      currentRecord: {},
      hasMore: true,
      records: [
        {
          id: 1,
          typeText: "拼团",
          typeClass: "type-group",
          title: "双人下午茶套餐拼团",
          code: "GP20230618001",
          user: "张三 (138****8888)",
          time: "2023-06-18 14:30",
          status: "已核销",
          statusClass: "status-success",
          operator: "王店长"
        },
        {
          id: 2,
          typeText: "优惠券",
          typeClass: "type-coupon",
          title: "新店开业满100减20券",
          code: "CP20230618002",
          user: "李四 (139****9999)",
          time: "2023-06-18 11:15",
          status: "已核销",
          statusClass: "status-success",
          operator: "王店长"
        },
        {
          id: 3,
          typeText: "秒杀",
          typeClass: "type-flash",
          title: "限时特价烤鸭套餐",
          code: "FS20230617005",
          user: "王五 (137****7777)",
          time: "2023-06-17 18:45",
          status: "已核销",
          statusClass: "status-success",
          operator: "张经理"
        },
        {
          id: 4,
          typeText: "拼团",
          typeClass: "type-group",
          title: "亲子套餐拼团",
          code: "GP20230617003",
          user: "赵六 (136****6666)",
          time: "2023-06-17 12:20",
          status: "已核销",
          statusClass: "status-success",
          operator: "张经理"
        },
        {
          id: 5,
          typeText: "优惠券",
          typeClass: "type-coupon",
          title: "生日特惠券",
          code: "CP20230616008",
          user: "钱七 (135****5555)",
          time: "2023-06-16 19:30",
          status: "已核销",
          statusClass: "status-success",
          operator: "李助理"
        },
        {
          id: 6,
          typeText: "秒杀",
          typeClass: "type-flash",
          title: "周末特价牛排套餐",
          code: "FS20230616002",
          user: "孙八 (134****4444)",
          time: "2023-06-16 18:10",
          status: "已核销",
          statusClass: "status-success",
          operator: "李助理"
        }
      ]
    };
  },
  computed: {
    filteredRecords() {
      let result = [...this.records];
      if (this.currentTab > 0) {
        const tabType = this.filterTabs[this.currentTab];
        result = result.filter((record) => record.typeText === tabType);
      }
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(
          (record) => record.title.toLowerCase().includes(keyword) || record.code.toLowerCase().includes(keyword) || record.user.toLowerCase().includes(keyword)
        );
      }
      return result;
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "核销记录帮助",
        icon: "none"
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    showDatePicker() {
      common_vendor.index.showActionSheet({
        itemList: ["今天", "昨天", "最近7天", "最近30天", "全部"],
        success: (res) => {
          const dateOptions = ["今天", "昨天", "最近7天", "最近30天", "全部"];
          this.dateRange = dateOptions[res.tapIndex];
          common_vendor.index.showToast({
            title: `已选择${this.dateRange}`,
            icon: "none"
          });
        }
      });
    },
    showSearch() {
      this.showSearchPopup = true;
    },
    hideSearch() {
      this.showSearchPopup = false;
      this.searchKeyword = "";
    },
    clearSearch() {
      this.searchKeyword = "";
    },
    doSearch() {
      this.showSearchPopup = false;
      if (this.filteredRecords.length === 0) {
        common_vendor.index.showToast({
          title: "未找到相关记录",
          icon: "none"
        });
      }
    },
    showRecordDetail(record) {
      this.currentRecord = record;
      this.showDetailPopup = true;
    },
    closeDetailPopup() {
      this.showDetailPopup = false;
    },
    loadMore() {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const moreRecords = [
          {
            id: 7,
            typeText: "拼团",
            typeClass: "type-group",
            title: "四人火锅套餐拼团",
            code: "GP20230615004",
            user: "周九 (133****3333)",
            time: "2023-06-15 20:15",
            status: "已核销",
            statusClass: "status-success",
            operator: "王店长"
          },
          {
            id: 8,
            typeText: "优惠券",
            typeClass: "type-coupon",
            title: "下午茶优惠券",
            code: "CP20230615007",
            user: "吴十 (132****2222)",
            time: "2023-06-15 15:40",
            status: "已核销",
            statusClass: "status-success",
            operator: "张经理"
          }
        ];
        this.records = [...this.records, ...moreRecords];
        this.hasMore = false;
        common_vendor.index.showToast({
          title: "已加载全部数据",
          icon: "none"
        });
      }, 1e3);
    }
  }
};
if (!Array) {
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_circle + _component_line + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.f($data.filterTabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    d: common_vendor.t($data.dateRange),
    e: common_vendor.o((...args) => $options.showDatePicker && $options.showDatePicker(...args)),
    f: common_vendor.p({
      cx: "11",
      cy: "11",
      r: "8"
    }),
    g: common_vendor.p({
      x1: "21",
      y1: "21",
      x2: "16.65",
      y2: "16.65"
    }),
    h: common_vendor.p({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      width: "18",
      height: "18",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    i: common_vendor.o((...args) => $options.showSearch && $options.showSearch(...args)),
    j: $options.filteredRecords.length === 0
  }, $options.filteredRecords.length === 0 ? {} : {
    k: common_vendor.f($options.filteredRecords, (record, index, i0) => {
      return {
        a: common_vendor.t(record.typeText),
        b: common_vendor.n(record.typeClass),
        c: common_vendor.t(record.title),
        d: common_vendor.t(record.code),
        e: common_vendor.t(record.user),
        f: common_vendor.t(record.time),
        g: common_vendor.t(record.status),
        h: common_vendor.n(record.statusClass),
        i: index,
        j: common_vendor.o(($event) => $options.showRecordDetail(record), index)
      };
    })
  }, {
    l: $data.hasMore
  }, $data.hasMore ? {
    m: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  } : {}, {
    n: $data.showSearchPopup
  }, $data.showSearchPopup ? common_vendor.e({
    o: common_vendor.o((...args) => $options.doSearch && $options.doSearch(...args)),
    p: $data.searchKeyword,
    q: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    r: $data.searchKeyword
  }, $data.searchKeyword ? {
    s: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    t: common_vendor.o((...args) => $options.hideSearch && $options.hideSearch(...args))
  }) : {}, {
    v: $data.showDetailPopup
  }, $data.showDetailPopup ? {
    w: common_vendor.o((...args) => $options.closeDetailPopup && $options.closeDetailPopup(...args)),
    x: common_vendor.t($data.currentRecord.status),
    y: common_vendor.n($data.currentRecord.statusClass),
    z: common_vendor.t($data.currentRecord.typeText),
    A: common_vendor.t($data.currentRecord.title),
    B: common_vendor.t($data.currentRecord.code),
    C: common_vendor.t($data.currentRecord.user),
    D: common_vendor.t($data.currentRecord.time),
    E: common_vendor.t($data.currentRecord.operator || "系统"),
    F: common_vendor.o((...args) => $options.closeDetailPopup && $options.closeDetailPopup(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/verification/records.js.map
