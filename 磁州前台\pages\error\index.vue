<template>
	<view class="error-page">
		<!-- 头部背景 -->
		<view class="error-header-bg"></view>
		
		<!-- 错误图标 -->
		<view class="error-icon-container">
			<image class="error-icon" src="/static/images/error.png" mode="aspectFit"></image>
		</view>
		
		<!-- 错误信息 -->
		<view class="error-content">
			<text class="error-title">{{errorTitle}}</text>
			<text class="error-message">{{errorMessage}}</text>
			<text class="error-details" v-if="showDetails">{{errorDetails}}</text>
		</view>
		
		<!-- 操作按钮 -->
		<view class="error-actions">
			<button class="action-btn primary" @click="goHome">返回首页</button>
			<button class="action-btn secondary" @click="goBack">返回上页</button>
			<button class="action-btn tertiary" @click="toggleDetails">
				{{showDetails ? '隐藏' : '显示'}}详情
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			errorCode: '',
			errorMessage: '抱歉，应用遇到了一些问题',
			errorDetails: '',
			fromPage: '',
			showDetails: false,
			errorType: 'general' // general, network, permission, auth
		}
	},
	computed: {
		errorTitle() {
			const titles = {
				general: '应用错误',
				network: '网络错误',
				permission: '权限错误',
				auth: '授权错误'
			}
			return titles[this.errorType] || '应用错误'
		}
	},
	onLoad(options) {
		// 获取错误信息
		if (options.error) {
			this.errorMessage = decodeURIComponent(options.error) || this.errorMessage
		}
		
		if (options.details) {
			this.errorDetails = decodeURIComponent(options.details)
		}
		
		if (options.from) {
			this.fromPage = decodeURIComponent(options.from)
		}
		
		if (options.type) {
			this.errorType = options.type
		}
		
		if (options.code) {
			this.errorCode = options.code
		}
		
		// 记录错误日志
		console.error('Error page loaded:', {
			message: this.errorMessage,
			details: this.errorDetails,
			from: this.fromPage,
			type: this.errorType,
			code: this.errorCode
		})
	},
	methods: {
		// 返回首页
		goHome() {
			uni.switchTab({
				url: '/pages/index/index'
			})
		},
		
		// 返回上一页
		goBack() {
			// 如果有来源页面且不是当前错误页面，则返回
			if (this.fromPage && this.fromPage !== 'pages/error/index') {
				// 尝试返回到来源页面
				try {
					// 检查是否可以直接跳转
					if (this.fromPage.startsWith('/pages/')) {
						uni.navigateTo({
							url: this.fromPage,
							fail: () => {
								// 如果跳转失败，直接返回上一页
								uni.navigateBack({
									delta: 1
								})
							}
						})
					} else {
						// 默认返回上一页
						uni.navigateBack({
							delta: 1
						})
					}
				} catch (e) {
					// 出错时返回首页
					this.goHome()
				}
			} else {
				// 没有来源页面时，返回上一页
				uni.navigateBack({
					delta: 1,
					fail: () => {
						// 如果无法返回上一页，则回到首页
						this.goHome()
					}
				})
			}
		},
		
		// 切换显示详情
		toggleDetails() {
			this.showDetails = !this.showDetails
		}
	}
}
</script>

<style>
.error-page {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-height: 100vh;
	background-color: #f5f7fa;
	position: relative;
	padding: 0 30rpx;
}

.error-header-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 40vh;
	background: linear-gradient(to bottom, #0066FF, #409EFF);
	border-radius: 0 0 50rpx 50rpx;
	z-index: 0;
}

.error-icon-container {
	margin-top: 15vh;
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 240rpx;
	height: 240rpx;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	box-shadow: 0 8rpx 24rpx rgba(0, 102, 255, 0.15);
}

.error-icon {
	width: 160rpx;
	height: 160rpx;
}

.error-content {
	width: 100%;
	padding: 40rpx;
	margin-top: 60rpx;
	background-color: #FFFFFF;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	z-index: 1;
	text-align: center;
}

.error-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 20rpx;
	display: block;
}

.error-message {
	font-size: 30rpx;
	color: #666666;
	line-height: 1.5;
	margin-bottom: 20rpx;
	display: block;
}

.error-details {
	font-size: 26rpx;
	color: #999999;
	background-color: #f7f7f7;
	padding: 20rpx;
	border-radius: 12rpx;
	text-align: left;
	word-break: break-word;
	margin-top: 20rpx;
	display: block;
	max-height: 300rpx;
	overflow-y: auto;
}

.error-actions {
	width: 100%;
	margin-top: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 1;
}

.action-btn {
	width: 80%;
	margin-bottom: 20rpx;
	height: 80rpx;
	line-height: 80rpx;
	font-size: 30rpx;
	border-radius: 40rpx;
}

.primary {
	background-color: #0066FF;
	color: #FFFFFF;
}

.secondary {
	background-color: #FFFFFF;
	color: #0066FF;
	border: 1rpx solid #0066FF;
}

.tertiary {
	background-color: transparent;
	color: #666666;
	font-size: 26rpx;
}
</style> 