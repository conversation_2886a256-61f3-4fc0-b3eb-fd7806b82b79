<template>
  <view class="share-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </view>
        <view class="navbar-title">分享</view>
        <view class="navbar-right">
          <view class="more-btn" @click="showMoreOptions">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="1"></circle>
              <circle cx="19" cy="12" r="1"></circle>
              <circle cx="5" cy="12" r="1"></circle>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 分享预览 -->
      <view class="share-preview">
        <view class="preview-card">
          <image class="preview-image" :src="shareContent.image" mode="aspectFill"></image>
          <view class="preview-content">
            <text class="preview-title">{{ shareContent.title }}</text>
            <text class="preview-desc">{{ shareContent.description }}</text>
            <view class="preview-meta">
              <text class="preview-price" v-if="shareContent.price">¥{{ shareContent.price }}</text>
              <text class="preview-source">来自{{ shareContent.source }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 分享方式 -->
      <view class="share-methods">
        <view class="section-header">
          <text class="section-title">分享到</text>
        </view>
        
        <view class="methods-grid">
          <view 
            class="method-item" 
            v-for="(method, index) in shareMethods" 
            :key="index"
            @click="handleShare(method)"
          >
            <view class="method-icon" :class="method.type">
              <image class="method-logo" :src="method.icon" mode="aspectFit"></image>
            </view>
            <text class="method-name">{{ method.name }}</text>
          </view>
        </view>
      </view>

      <!-- 分享文案 -->
      <view class="share-text">
        <view class="section-header">
          <text class="section-title">分享文案</text>
          <view class="edit-btn" @click="editShareText">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
            <text class="edit-text">编辑</text>
          </view>
        </view>
        
        <view class="text-content">
          <textarea 
            class="share-textarea" 
            v-model="shareText" 
            placeholder="添加分享文案..."
            :disabled="!isEditing"
            :maxlength="200"
          ></textarea>
          <view class="text-count">{{ shareText.length }}/200</view>
        </view>
        
        <view class="text-templates" v-if="isEditing">
          <text class="templates-title">推荐文案</text>
          <view class="templates-list">
            <view 
              class="template-item" 
              v-for="(template, index) in textTemplates" 
              :key="index"
              @click="useTemplate(template)"
            >
              <text class="template-text">{{ template }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 分享设置 -->
      <view class="share-settings">
        <view class="section-header">
          <text class="section-title">分享设置</text>
        </view>
        
        <view class="settings-list">
          <view class="setting-item">
            <view class="setting-info">
              <text class="setting-title">包含价格信息</text>
              <text class="setting-desc">分享时显示商品价格</text>
            </view>
            <switch :checked="shareSettings.includePrice" @change="toggleIncludePrice" />
          </view>
          
          <view class="setting-item">
            <view class="setting-info">
              <text class="setting-title">生成分享海报</text>
              <text class="setting-desc">自动生成精美分享海报</text>
            </view>
            <switch :checked="shareSettings.generatePoster" @change="toggleGeneratePoster" />
          </view>
          
          <view class="setting-item">
            <view class="setting-info">
              <text class="setting-title">添加邀请码</text>
              <text class="setting-desc">分享时包含我的邀请码</text>
            </view>
            <switch :checked="shareSettings.includeInviteCode" @change="toggleIncludeInviteCode" />
          </view>
        </view>
      </view>

      <!-- 分享记录 -->
      <view class="share-history">
        <view class="section-header">
          <text class="section-title">最近分享</text>
          <view class="clear-btn" @click="clearHistory">
            <text class="clear-text">清空</text>
          </view>
        </view>
        
        <view class="history-list" v-if="shareHistoryList.length > 0">
          <view 
            class="history-item" 
            v-for="(item, index) in shareHistoryList" 
            :key="index"
            @click="reshare(item)"
          >
            <view class="history-icon">
              <image class="history-logo" :src="item.methodIcon" mode="aspectFit"></image>
            </view>
            <view class="history-content">
              <text class="history-title">{{ item.title }}</text>
              <text class="history-time">{{ item.time }}</text>
            </view>
            <view class="history-action">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </view>
          </view>
        </view>
        
        <view class="empty-history" v-else>
          <view class="empty-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="18" cy="5" r="3"></circle>
              <circle cx="6" cy="12" r="3"></circle>
              <circle cx="18" cy="19" r="3"></circle>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
            </svg>
          </view>
          <text class="empty-text">暂无分享记录</text>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 分享海报弹窗 -->
    <view class="poster-modal" v-if="showPoster" @click="hidePosterModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">分享海报</text>
          <view class="close-btn" @click="hidePosterModal">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </view>
        </view>
        <view class="modal-body">
          <view class="poster-container">
            <canvas 
              class="poster-canvas" 
              canvas-id="shareCanvas"
              :style="{ width: posterWidth + 'px', height: posterHeight + 'px' }"
            ></canvas>
          </view>
        </view>
        <view class="modal-actions">
          <view class="action-btn save" @click="savePoster">
            <text class="btn-text">保存到相册</text>
          </view>
          <view class="action-btn share" @click="sharePoster">
            <text class="btn-text">立即分享</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const isEditing = ref(false)
const showPoster = ref(false)
const posterWidth = ref(300)
const posterHeight = ref(400)

// 分享内容
const shareContent = ref({
  title: '精选优质商品',
  description: '高品质，值得信赖的选择，限时特价优惠中！',
  image: '/static/images/products/share-product.jpg',
  price: 299.00,
  source: '磁州商城'
})

// 分享文案
const shareText = ref('发现了一个不错的商品，推荐给大家！')

// 分享方式
const shareMethods = ref([
  {
    name: '微信好友',
    type: 'wechat',
    icon: '/static/images/share/wechat.png'
  },
  {
    name: '朋友圈',
    type: 'moments',
    icon: '/static/images/share/moments.png'
  },
  {
    name: 'QQ好友',
    type: 'qq',
    icon: '/static/images/share/qq.png'
  },
  {
    name: 'QQ空间',
    type: 'qzone',
    icon: '/static/images/share/qzone.png'
  },
  {
    name: '微博',
    type: 'weibo',
    icon: '/static/images/share/weibo.png'
  },
  {
    name: '复制链接',
    type: 'copy',
    icon: '/static/images/share/copy.png'
  }
])

// 文案模板
const textTemplates = ref([
  '发现了一个不错的商品，推荐给大家！',
  '这个商品真的很棒，性价比超高！',
  '限时特价，错过就没有了！',
  '好物分享，值得拥有！'
])

// 分享设置
const shareSettings = ref({
  includePrice: true,
  generatePoster: false,
  includeInviteCode: false
})

// 分享记录
const shareHistoryList = ref([
  {
    title: '分享到微信好友',
    time: '2024-01-20 14:30',
    methodIcon: '/static/images/share/wechat.png',
    method: 'wechat'
  },
  {
    title: '分享到朋友圈',
    time: '2024-01-20 10:15',
    methodIcon: '/static/images/share/moments.png',
    method: 'moments'
  }
])

// 页面加载
onMounted(() => {
  console.log('分享页面加载')
  loadShareData()
})

// 方法
function goBack() {
  uni.navigateBack()
}

function showMoreOptions() {
  uni.showActionSheet({
    itemList: ['举报内容', '不感兴趣'],
    success: (res) => {
      console.log('选择了：', res.tapIndex)
    }
  })
}

function handleShare(method) {
  console.log('分享到：', method.name)
  
  // 添加到分享记录
  const historyItem = {
    title: `分享到${method.name}`,
    time: new Date().toLocaleString(),
    methodIcon: method.icon,
    method: method.type
  }
  shareHistoryList.value.unshift(historyItem)
  
  // 限制记录数量
  if (shareHistoryList.value.length > 10) {
    shareHistoryList.value.pop()
  }
  
  switch(method.type) {
    case 'wechat':
      shareToWechat()
      break
    case 'moments':
      shareToMoments()
      break
    case 'qq':
      shareToQQ()
      break
    case 'qzone':
      shareToQzone()
      break
    case 'weibo':
      shareToWeibo()
      break
    case 'copy':
      copyShareLink()
      break
  }
}

function shareToWechat() {
  uni.showToast({
    title: '分享到微信好友',
    icon: 'none'
  })
}

function shareToMoments() {
  uni.showToast({
    title: '分享到朋友圈',
    icon: 'none'
  })
}

function shareToQQ() {
  uni.showToast({
    title: '分享到QQ好友',
    icon: 'none'
  })
}

function shareToQzone() {
  uni.showToast({
    title: '分享到QQ空间',
    icon: 'none'
  })
}

function shareToWeibo() {
  uni.showToast({
    title: '分享到微博',
    icon: 'none'
  })
}

function copyShareLink() {
  const shareLink = `${shareText.value} ${shareContent.value.title} - 来自${shareContent.value.source}`
  
  uni.setClipboardData({
    data: shareLink,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success'
      })
    }
  })
}

function editShareText() {
  isEditing.value = !isEditing.value
}

function useTemplate(template) {
  shareText.value = template
  isEditing.value = false
}

function toggleIncludePrice(e) {
  shareSettings.value.includePrice = e.detail.value
}

function toggleGeneratePoster(e) {
  shareSettings.value.generatePoster = e.detail.value
  if (e.detail.value) {
    generatePoster()
  }
}

function toggleIncludeInviteCode(e) {
  shareSettings.value.includeInviteCode = e.detail.value
}

function clearHistory() {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有分享记录吗？',
    success: (res) => {
      if (res.confirm) {
        shareHistoryList.value = []
        uni.showToast({
          title: '已清空',
          icon: 'success'
        })
      }
    }
  })
}

function reshare(item) {
  const method = shareMethods.value.find(m => m.type === item.method)
  if (method) {
    handleShare(method)
  }
}

function generatePoster() {
  showPoster.value = true
  
  // 模拟生成海报
  setTimeout(() => {
    drawPoster()
  }, 100)
}

function drawPoster() {
  const ctx = uni.createCanvasContext('shareCanvas')
  
  // 绘制背景
  ctx.setFillStyle('#ffffff')
  ctx.fillRect(0, 0, posterWidth.value, posterHeight.value)
  
  // 绘制标题
  ctx.setFillStyle('#333333')
  ctx.setFontSize(18)
  ctx.fillText(shareContent.value.title, 20, 40)
  
  // 绘制描述
  ctx.setFillStyle('#666666')
  ctx.setFontSize(14)
  ctx.fillText(shareContent.value.description, 20, 70)
  
  // 绘制价格
  if (shareSettings.value.includePrice) {
    ctx.setFillStyle('#E91E63')
    ctx.setFontSize(24)
    ctx.fillText(`¥${shareContent.value.price}`, 20, 110)
  }
  
  ctx.draw()
}

function hidePosterModal() {
  showPoster.value = false
}

function savePoster() {
  uni.canvasToTempFilePath({
    canvasId: 'shareCanvas',
    success: (res) => {
      uni.saveImageToPhotosAlbum({
        filePath: res.tempFilePath,
        success: () => {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        }
      })
    }
  })
}

function sharePoster() {
  uni.canvasToTempFilePath({
    canvasId: 'shareCanvas',
    success: (res) => {
      // 分享海报
      uni.showToast({
        title: '分享海报',
        icon: 'none'
      })
    }
  })
}

function loadShareData() {
  // 模拟加载分享数据
  setTimeout(() => {
    console.log('分享数据加载完成')
  }, 500)
}
</script>

<style scoped>
/* 分享页面样式开始 */
.share-container {
  min-height: 100vh;
  background: #f7f7f7;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding-top: var(--status-bar-height, 44px);
  border-bottom: 1px solid #f0f0f0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
}

.back-btn, .more-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.back-btn svg, .more-btn svg {
  color: #333;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 内容区域样式 */
.content-scroll {
  padding-top: calc(var(--status-bar-height, 44px) + 44px);
}

/* 分享预览样式 */
.share-preview {
  padding: 20px 16px;
}

.preview-card {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.preview-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
}

.preview-content {
  flex: 1;
}

.preview-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
}

.preview-desc {
  display: block;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
}

.preview-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.preview-price {
  font-size: 18px;
  color: #E91E63;
  font-weight: 700;
}

.preview-source {
  font-size: 12px;
  color: #999;
}

/* 区块样式 */
.share-methods, .share-text, .share-settings, .share-history {
  background: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 分享方式样式 */
.methods-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
}

.method-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.method-item:active {
  transform: scale(0.95);
}

.method-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 28px;
  background: #f5f5f5;
}

.method-logo {
  width: 40px;
  height: 40px;
}

.method-name {
  font-size: 12px;
  color: #333;
  text-align: center;
}

/* 分享文案样式 */
.edit-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 12px;
}

.edit-btn svg {
  color: #666;
}

.edit-text {
  font-size: 12px;
  color: #666;
}

.text-content {
  position: relative;
  padding: 20px;
}

.share-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  resize: none;
}

.text-count {
  position: absolute;
  bottom: 24px;
  right: 32px;
  font-size: 12px;
  color: #999;
}

.text-templates {
  padding: 0 20px 20px;
  border-top: 1px solid #f0f0f0;
}

.templates-title {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.templates-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.template-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.template-item:active {
  background: #e9ecef;
}

.template-text {
  font-size: 14px;
  color: #333;
}

/* 分享设置样式 */
.settings-list {
  padding: 0 20px 16px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.setting-desc {
  display: block;
  font-size: 12px;
  color: #666;
}

/* 分享记录样式 */
.clear-btn {
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 12px;
}

.clear-text {
  font-size: 12px;
  color: #666;
}

.history-list {
  padding: 0 20px 16px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f8f9fa;
  transition: all 0.3s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: #f8f9fa;
}

.history-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-logo {
  width: 24px;
  height: 24px;
}

.history-content {
  flex: 1;
}

.history-title {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.history-time {
  display: block;
  font-size: 12px;
  color: #666;
}

.history-action svg {
  color: #999;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
}

.empty-icon {
  margin-bottom: 12px;
  opacity: 0.5;
}

.empty-icon svg {
  color: #999;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 海报弹窗样式 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 350px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
}

.close-btn svg {
  color: #666;
}

.modal-body {
  padding: 20px;
  text-align: center;
}

.poster-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.poster-canvas {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
}

.action-btn.save {
  background: #f5f5f5;
}

.action-btn.share {
  background: #E91E63;
}

.btn-text {
  font-size: 14px;
  font-weight: 500;
}

.action-btn.save .btn-text {
  color: #333;
}

.action-btn.share .btn-text {
  color: white;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background: transparent;
}
/* 分享页面样式结束 */
</style>
