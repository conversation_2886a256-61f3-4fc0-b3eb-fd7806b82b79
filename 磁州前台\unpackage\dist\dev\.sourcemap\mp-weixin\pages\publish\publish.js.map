{"version": 3, "file": "publish.js", "sources": ["pages/publish/publish.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHVibGlzaC9wdWJsaXNoLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"publish-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"custom-navbar\">\n\t\t\t<view class=\"navbar-left\" @click=\"goBack\">\n\t\t\t\t<image class=\"back-icon\" src=\"/static/images/tabbar/最新返回键.png\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"navbar-title\">发布信息</view>\n\t\t\t<view class=\"navbar-right\">\n\t\t\t\t<!-- 删除原来的按钮 -->\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 内容区域 -->\n\t\t<scroll-view scroll-y class=\"content-scroll\">\n\t\t\t<!-- 免责声明 -->\n\t\t\t<view class=\"disclaimer\">\n\t\t\t\t<view class=\"disclaimer-list\">\n\t\t\t\t\t<view class=\"disclaimer-item\">\n\t\t\t\t\t\t<text class=\"disclaimer-content\">内容规范：禁止发布违法、侵权或违背公序良俗的信息，禁发网络兼职，信用贷款等垃圾信息！违者需自行承担法律后果。</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 恢复商家入驻卡片 -->\n\t\t\t<view class=\"entry-cards\">\n\t\t\t\t<!-- 商家入驻卡片 -->\n\t\t\t\t<view class=\"entry-card merchant-card\" @click=\"handleMerchantApply\">\n\t\t\t\t\t<view class=\"join-card-left\">\n\t\t\t\t\t\t<view class=\"join-icon-wrap\">\n\t\t\t\t\t\t\t<image class=\"join-icon\" src=\"/static/images/tabbar/入驻卡片.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"join-info\">\n\t\t\t\t\t\t\t<text class=\"join-title\">我是商家，点击入驻商圈</text>\n\t\t\t\t\t\t\t<text class=\"join-desc\">超低成本，获得更多商机！</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<button class=\"join-btn\" @click.stop=\"handleMerchantApply\">立即申请</button>\n\t\t\t\t\t<view class=\"hot-badge\">热门</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 发布类目选择 -->\n\t\t\t<view class=\"category-section\">\n\t\t\t\t<view class=\"category-section-title\">请选择发布类目</view>\n\t\t\t\t\n\t\t\t\t<view class=\"category-grid\">\n\t\t\t\t\t<!-- 第一行 -->\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToServiceCategory\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/到家服务.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">到家服务</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '寻找服务', type: 'find_service'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/寻找服务.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">寻找服务</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '招聘信息', type: 'hire'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/招聘信息.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">招聘信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '求职信息', type: 'job_wanted'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/求职信息.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">求职信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '房屋出租', type: 'house_rent'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/出租.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">房屋出租</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 第二行 -->\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '房屋出售', type: 'house_sell'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/房屋出售.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">房屋出售</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '二手车辆', type: 'used_car'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/二手车辆.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">二手车辆</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '宠物信息', type: 'pet'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/宠物信息.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">宠物信息</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '商家活动', type: 'merchant_activity'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/商家活动.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">商家活动</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '车辆服务', type: 'car_service'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/车辆服务.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">车辆服务</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '二手闲置', type: 'second_hand'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/二手闲置.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">二手闲置</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 第三行 -->\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '磁州拼车', type: 'carpool'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/磁州拼车.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">磁州拼车</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '生意转让', type: 'business_transfer'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/生意转让.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">生意转让</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '教育培训', type: 'education'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/教育培训.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">教育培训</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '其他服务', type: 'other_service'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/其他服务.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">其他服务</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '婚恋交友', type: 'dating'})\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper\">\n\t\t\t\t\t\t\t<image class=\"category-icon\" src=\"/static/images/tabbar/婚恋交友.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">婚恋交友</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"category-item test-category-item\" hover-class=\"category-item-hover\" @click=\"navigateToPublishDetail({name: '测试发布', type: 'test'})\" @longpress=\"showTestDataManager\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper test-icon-wrapper\">\n\t\t\t\t\t\t\t<text class=\"test-icon\">测</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">测试发布</text>\n\t\t\t\t\t\t<text class=\"test-badge\" v-if=\"testPublishCount > 0\">{{testPublishCount}}</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 新增商家入驻测试按钮 -->\n\t\t\t\t\t<view class=\"category-item merchant-test-category-item\" hover-class=\"category-item-hover\" @click=\"testMerchantJoin\">\n\t\t\t\t\t\t<view class=\"category-icon-wrapper merchant-test-icon-wrapper\">\n\t\t\t\t\t\t\t<text class=\"test-icon\">商</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"category-name\">商家入驻测试</text>\n\t\t\t\t\t\t<text class=\"merchant-test-badge\" v-if=\"merchantTestCount > 0\">{{merchantTestCount}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<RedPacketEntry v-model=\"form.redPacket\" />\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\nimport { onLoad, onShow } from '@dcloudio/uni-app';\n\n// --- 数据迁移开始 ---\nconst imageLoadStatus = ref({});\nconst preloadComplete = ref(false);\nconst testPublishCount = ref(0);\nconst merchantTestCount = ref(0);\nconst form = ref({\n\tredPacket: null\n});\n// --- 数据迁移结束 ---\n\n// --- 方法迁移开始 ---\n// 设置页面优先级\nconst setPagePriority = () => {\n\tif (uni.canIUse && uni.canIUse('setPageInfo')) {\n\t\ttry {\n\t\t\t// @ts-ignore\n\t\t\twx.setPageInfo({\n\t\t\t\tpageType: 'homePage',\n\t\t\t\tperformanceLevel: 'high'\n\t\t\t});\n\t\t} catch (e) {\n\t\t\tconsole.error('设置页面优先级失败', e);\n\t\t}\n\t}\n};\n\n// 预加载图片\nconst preloadImages = () => {\n\tconst imageList = [\n\t\t'/static/images/tabbar/到家服务.png',\n\t\t'/static/images/tabbar/寻找服务.png',\n\t\t'/static/images/tabbar/招聘信息.png',\n\t\t'/static/images/tabbar/求职信息.png',\n\t\t'/static/images/tabbar/出租.png',\n\t\t'/static/images/tabbar/房屋出售.png',\n\t\t'/static/images/tabbar/二手车辆.png',\n\t\t'/static/images/tabbar/宠物信息.png',\n\t\t'/static/images/tabbar/商家活动.png',\n\t\t'/static/images/tabbar/车辆服务.png',\n\t\t'/static/images/tabbar/二手闲置.png',\n\t\t'/static/images/tabbar/磁州拼车.png',\n\t\t'/static/images/tabbar/生意转让.png',\n\t\t'/static/images/tabbar/教育培训.png',\n\t\t'/static/images/tabbar/其他服务.png'\n\t];\n\n\tlet loadedCount = 0;\n\tconst totalImages = imageList.length;\n\n\timageList.forEach(img => {\n\t\tuni.getImageInfo({\n\t\t\tsrc: img,\n\t\t\tsuccess: () => {\n\t\t\t\timageLoadStatus.value[img] = true;\n\t\t\t\tloadedCount++;\n\t\t\t\tif (loadedCount === totalImages) {\n\t\t\t\t\tpreloadComplete.value = true;\n\t\t\t\t}\n\t\t\t},\n\t\t\tfail: () => {\n\t\t\t\tconsole.error('图片加载失败:', img);\n\t\t\t\tloadedCount++;\n\t\t\t\tif (loadedCount === totalImages) {\n\t\t\t\t\tpreloadComplete.value = true;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t});\n\n\tsetTimeout(() => {\n\t\tpreloadComplete.value = true;\n\t}, 1000);\n};\n\n// 加载测试发布数据计数\nconst loadTestPublishCount = () => {\n\ttry {\n\t\tconst testData = uni.getStorageSync('testPublishData') || [];\n\t\ttestPublishCount.value = testData.length;\n\t} catch (e) {\n\t\tconsole.error('加载测试数据计数失败', e);\n\t\ttestPublishCount.value = 0;\n\t}\n};\n\n// 加载商家入驻测试数据计数\nconst loadMerchantTestCount = () => {\n\ttry {\n\t\tconst merchantTestData = uni.getStorageSync('merchantTestData') || [];\n\t\tmerchantTestCount.value = merchantTestData.length;\n\t} catch (e) {\n\t\tconsole.error('加载商家入驻测试数据计数失败', e);\n\t\tmerchantTestCount.value = 0;\n\t}\n};\n\n// 商家入驻处理\nconst handleMerchantApply = () => {\n\tuni.navigateTo({\n\t\turl: '/pages/business/join'\n\t});\n};\n\n// 个人入驻处理\nconst handlePersonalApply = () => {\n\tuni.showToast({\n\t\ttitle: '正在开发个人入驻功能',\n\t\ticon: 'none'\n\t});\n};\n\n// 商家入驻测试功能\nconst testMerchantJoin = () => {\n\tuni.showModal({\n\t\ttitle: '商家入驻测试',\n\t\tcontent: '请输入任意内容，完成快速商家入驻测试',\n\t\teditable: true,\n\t\tplaceholderText: '输入商家名称',\n\t\tsuccess: (res) => {\n\t\t\tif (res.confirm) {\n\t\t\t\tconst shopName = res.content || '测试商家';\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '处理中',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst merchantTestData = uni.getStorageSync('merchantTestData') || [];\n\t\t\t\t\t\tconst newMerchantData = {\n\t\t\t\t\t\t\tid: Date.now().toString(),\n\t\t\t\t\t\t\tshopName: shopName,\n\t\t\t\t\t\t\taddress: '测试地址',\n\t\t\t\t\t\t\tcategory: '测试类目',\n\t\t\t\t\t\t\tscale: '1-10人',\n\t\t\t\t\t\t\tbusinessTime: '09:00-21:00',\n\t\t\t\t\t\t\tcontactPhone: '***********',\n\t\t\t\t\t\t\tdescription: '这是一个测试商家',\n\t\t\t\t\t\t\tcreateTime: new Date().toLocaleString(),\n\t\t\t\t\t\t\tjoinMethod: 'free',\n\t\t\t\t\t\t\tshopImage: '/static/images/tabbar/商家入驻.png',\n\t\t\t\t\t\t\tlogo: '/static/images/tabbar/商家入驻.png',\n\t\t\t\t\t\t\tqrcode: '',\n\t\t\t\t\t\t\talbum: []\n\t\t\t\t\t\t};\n\t\t\t\t\t\tmerchantTestData.push(newMerchantData);\n\t\t\t\t\t\tuni.setStorageSync('merchantTestData', merchantTestData);\n\t\t\t\t\t\tmerchantTestCount.value = merchantTestData.length;\n\t\t\t\t\t\tuni.setStorageSync('lastMerchantId', newMerchantData.id);\n\t\t\t\t\t\tuni.setStorageSync('lastMerchantData', newMerchantData);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 跳转到商家入驻成功页面\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: `/pages/business/success?id=${newMerchantData.id}&shopName=${encodeURIComponent(shopName)}&memberType=${encodeURIComponent('测试版')}&isTest=true`,\n\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\tconsole.error('跳转到商家入驻成功页面失败:', err);\n\t\t\t\t\t\t\t\t// 如果商家入驻成功页面不存在，则尝试跳转到通用成功页面\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/publish/success?id=' + newMerchantData.id + '&type=merchant',\n\t\t\t\t\t\t\t\t\tfail: (err2) => {\n\t\t\t\t\t\t\t\t\t\tconsole.error('跳转到通用成功页面也失败:', err2);\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '入驻成功',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error('存储商家测试数据失败', e);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '入驻成功',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}, 800);\n\t\t\t}\n\t\t}\n\t});\n};\n\n// 导航到详情页\nconst navigateToPublishDetail = (item) => {\n\tif (item.type === 'test') {\n\t\tsetTimeout(() => {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '测试发布',\n\t\t\t\tcontent: '请输入任意内容进行测试发布',\n\t\t\t\teditable: true,\n\t\t\t\tplaceholderText: '随意输入一个字符',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tconst content = res.content || '测试内容';\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '发布中',\n\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tconst testData = uni.getStorageSync('testPublishData') || [];\n\t\t\t\t\t\t\t\tconst newData = {\n\t\t\t\t\t\t\t\t\tid: Date.now().toString(),\n\t\t\t\t\t\t\t\t\ttype: 'test',\n\t\t\t\t\t\t\t\t\tcategory: '测试发布',\n\t\t\t\t\t\t\t\t\tcategoryName: '测试发布',\n\t\t\t\t\t\t\t\t\ttitle: '测试发布',\n\t\t\t\t\t\t\t\t\tcontent: content,\n\t\t\t\t\t\t\t\t\tcreateTime: new Date().toLocaleString()\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\ttestData.push(newData);\n\t\t\t\t\t\t\t\tuni.setStorageSync('testPublishData', testData);\n\t\t\t\t\t\t\t\ttestPublishCount.value = testData.length;\n\t\t\t\t\t\t\t\tuni.setStorageSync('lastPublishId', newData.id);\n\t\t\t\t\t\t\t\tuni.setStorageSync('lastPublishData', newData);\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/publish/success?id=' + newData.id,\n\t\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\t\tconsole.error('跳转到发布成功页面失败:', err);\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '发布成功',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\tconsole.error('存储测试数据失败', e);\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '发布成功',\n\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, 800);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}, 300);\n\t\treturn;\n\t}\n\n\tuni.showLoading({\n\t\ttitle: '加载中',\n\t\tmask: true\n\t});\n\tconst params = {\n\t\ttype: item.type,\n\t\tname: item.name,\n\t\tcategory: item.name,\n\t\tcategoryType: item.type\n\t};\n\tconst queryString = Object.keys(params)\n\t\t.map(key => `${key}=${encodeURIComponent(params[key])}`)\n\t\t.join('&');\n\ttry {\n\t\tuni.navigateTo({\n\t\t\turl: `/pages/publish/detail?${queryString}&isPublish=true`,\n\t\t\tsuccess: function() {\n\t\t\t\tconsole.log('导航成功');\n\t\t\t\tuni.hideLoading();\n\t\t\t},\n\t\t\tfail: function(err) {\n\t\t\t\tconsole.error('导航失败:', err);\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `../publish/detail?${queryString}&isPublish=true`,\n\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(err2) {\n\t\t\t\t\t\tconsole.error('第二次导航失败:', err2);\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '页面跳转失败，请重启应用',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t} catch (e) {\n\t\tconsole.error('导航异常:', e);\n\t\tuni.hideLoading();\n\t\tuni.showToast({\n\t\t\ttitle: '系统繁忙，请稍后再试',\n\t\t\ticon: 'none'\n\t\t});\n\t}\n};\n\nconst showTestDataManager = () => {\n\ttry {\n\t\tconst testData = uni.getStorageSync('testPublishData') || [];\n\t\tif (testData.length === 0) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '暂无测试数据',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\treturn;\n\t\t}\n\t\tuni.showActionSheet({\n\t\t\titemList: ['查看测试数据', '清除测试数据'],\n\t\t\tsuccess: (res) => {\n\t\t\t\tif (res.tapIndex === 0) {\n\t\t\t\t\tviewTestData(testData);\n\t\t\t\t} else if (res.tapIndex === 1) {\n\t\t\t\t\tclearTestData();\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t} catch (e) {\n\t\tconsole.error('打开测试数据管理器失败', e);\n\t\tuni.showToast({\n\t\t\ttitle: '操作失败，请重试',\n\t\t\ticon: 'none'\n\t\t});\n\t}\n};\n\nconst viewTestData = (testData) => {\n\tconst dataList = testData.map((item, index) => {\n\t\treturn `${index + 1}. ${item.content} (${item.date})`;\n\t}).join('\\n\\n');\n\tuni.showModal({\n\t\ttitle: '测试数据列表',\n\t\tcontent: dataList,\n\t\tconfirmText: '关闭',\n\t\tshowCancel: false\n\t});\n};\n\nconst clearTestData = () => {\n\tuni.showModal({\n\t\ttitle: '确认清除',\n\t\tcontent: '确定要清除所有测试数据吗？',\n\t\tsuccess: (res) => {\n\t\t\tif (res.confirm) {\n\t\t\t\ttry {\n\t\t\t\t\tuni.removeStorageSync('testPublishData');\n\t\t\t\t\ttestPublishCount.value = 0;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '测试数据已清除',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('清除测试数据失败', e);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '清除失败，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n};\n\nconst navigateToServiceCategory = () => {\n\tconsole.log('跳转到服务类型选择页面');\n\tuni.navigateTo({\n\t\turl: '/pages/publish/service-category'\n\t});\n};\n// --- 方法迁移结束 ---\n\n// --- 生命周期钩子迁移开始 ---\nonLoad(() => {\n\tsetPagePriority();\n\tpreloadImages();\n\tloadTestPublishCount();\n\tloadMerchantTestCount();\n});\n\nonShow(() => {\n\tloadTestPublishCount();\n\tloadMerchantTestCount();\n});\n// --- 生命周期钩子迁移结束 ---\n</script>\n\n<style lang=\"scss\">\n/* 使用系统默认字体 */\n.publish-container {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n\n/* 替换所有使用阿里妈妈字体的地方 */\n.title, .heading, .subtitle, .publish-title, .section-title {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  font-weight: bold; /* 使用系统字体的粗体代替阿里妈妈黑体 */\n}\n\n\t.publish-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f7fa;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tposition: relative;\n\t\tpadding-bottom: 30rpx;\n\t\tpadding-top: 44px; /* 为状态栏预留空间 */\n\t}\n\t\n\t/* 内容区滚动视图 */\n\t.content-scroll {\n\t\tflex: 1;\n\t\tbox-sizing: border-box;\n\t\tpadding-top: 150rpx; /* 增加顶部内边距，为固定导航栏留出空间 */\n\t}\n\t\n\t/* 自定义导航栏 */\n\t.custom-navbar {\n\t\tbackground: linear-gradient(135deg, #0066FF, #0052CC);\n\t\theight: 88rpx;\n\t\tpadding-top: 44px; /* 状态栏高度 */\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tposition: fixed; /* 改为固定定位 */\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tpadding-bottom: 10rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 82, 204, 0.2);\n\t\tz-index: 100; /* 提高z-index确保在最上层 */\n\t}\n\t\n\t.navbar-title {\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 600;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.navbar-left {\n\t\tposition: absolute;\n\t\tleft: 20rpx;\n\t\theight: 88rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.back-icon {\n\t\twidth: 48rpx;\n\t\theight: 48rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.navbar-right {\n\t\tposition: absolute;\n\t\tright: 20rpx;\n\t\tdisplay: flex;\n\t}\n\t\n\t.navbar-btn {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 50%;\n\t\ttransition: all 0.2s;\n\t}\n\t\n\t.navbar-btn:active {\n\t\tbackground-color: rgba(255, 255, 255, 0.2);\n\t}\n\t\n\t.navbar-btn-icon {\n\t\twidth: 44rpx;\n\t\theight: 44rpx;\n\t}\n\t\n\t/* 免责声明 */\n\t.disclaimer {\n\t\tpadding: 20rpx 30rpx 10rpx 30rpx;\n\t\tmargin: 10rpx 20rpx 25rpx;\n\t\t/* 去掉背景卡片 */\n\t\t/* background: #fff; */\n\t\t/* border-radius: 10rpx; */\n\t}\n\t\n\t.disclaimer-list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 12rpx;\n\t}\n\t\n\t.disclaimer-item {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tfont-size: 24rpx;\n\t\tcolor: #444;\n\t\tline-height: 1.7;\n\t}\n\t\n\t.disclaimer-index {\n\t\tfont-weight: bold;\n\t\tmargin-right: 8rpx;\n\t\tcolor: #0052CC;\n\t\tfont-size: 26rpx;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.disclaimer-content {\n\t\tflex: 1;\n\t\tcolor: #999;\n\t}\n\t\n\t.disclaimer-note {\n\t\tmargin-top: 12rpx;\n\t\tfont-size: 20rpx;\n\t\tcolor: #999;\n\t\ttext-align: left;\n\t}\n\t\n\t/* 入驻卡片 */\n\t.entry-cards {\n padding: 0 30rpx;\n\tmargin-bottom: 15rpx;\n\tposition: relative;\n\tz-index: 5;\n\t}\n\t\n\t.entry-card {\n\tmargin-bottom: 15rpx;\n\tbackground: #EEF1F6;\n\t\tborder-radius: 16rpx;\n\tbox-shadow: 10rpx 10rpx 20rpx rgba(174, 184, 210, 0.8),\n\t\t\t\t-10rpx -10rpx 20rpx rgba(255, 255, 255, 0.9),\n\t\t\t\tinset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx 20rpx 20rpx 20rpx;\n\t\tposition: relative;\n\tz-index: 2;\n\tborder: none;\n\t\toverflow: hidden;\n\ttransition: all 0.2s ease;\n\t}\n\t\n\t.entry-card:active {\n\ttransform: scale(0.98);\n\tbox-shadow: 6rpx 6rpx 12rpx rgba(174, 184, 210, 0.8),\n\t\t\t\t-6rpx -6rpx 12rpx rgba(255, 255, 255, 0.9),\n\t\t\t\tinset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);\n\t}\n\t\n.entry-card::before {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n left: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);\n\t\tz-index: 0;\n\t}\n\t\n.join-card-left {\n\tdisplay: flex;\n\talign-items: center;\n\tposition: relative;\n\tz-index: 2;\n\t}\n\t\n.join-icon-wrap {\n\twidth: 70rpx;\n\theight: 70rpx;\n\tbackground: #EEF1F6;\n\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\tmargin-right: 20rpx;\n\tbox-shadow: 5rpx 5rpx 10rpx rgba(174, 184, 210, 0.6),\n\t\t\t\t-5rpx -5rpx 10rpx rgba(255, 255, 255, 0.9),\n\t\t\t\tinset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.4);\n\t\tposition: relative;\n\tz-index: 3;\n\tborder: none;\n\ttransition: all 0.2s ease;\n\t}\n\t\n.personal-icon-wrap {\n\tbackground: #EEF1F6;\n}\n\n.join-icon {\n\twidth: 42rpx;\n\theight: 42rpx;\n\topacity: 1;\n\tfilter: none;\n}\n\n.join-info {\n\tdisplay: flex;\n\tflex-direction: column;\n\t}\n\t\n.join-title {\n\tcolor: #3D56C1;\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tmargin-bottom: 6rpx;\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);\n\tfont-family: 'AlimamaShuHeiTi', sans-serif;\n\tletter-spacing: 1rpx;\n\t}\n\t\n.personal-title {\n\tcolor: #4a89dc;\n}\n\n.join-desc {\n\tcolor: #5F6A8A;\n\tfont-size: 22rpx;\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.6);\n\t}\n\t\n.join-btn {\n\tbackground: linear-gradient(145deg, #394FC2, #4A67D7);\n\tcolor: #ffffff;\n\tfont-size: 24rpx;\n\tpadding: 0 24rpx;\n\theight: 60rpx;\n\tline-height: 60rpx;\n\tborder-radius: 30rpx;\n\tmargin: 0;\n\t\tposition: relative;\n\tz-index: 2;\n\tbox-shadow: 5rpx 5rpx 10rpx rgba(61, 86, 193, 0.3),\n\t\t\t\t-2rpx -2rpx 8rpx rgba(255, 255, 255, 0.3),\n\t\t\t\tinset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);\n\tfont-weight: 600;\n\tborder: none;\n\ttransition: all 0.2s ease;\n\tfont-family: 'AlimamaShuHeiTi', sans-serif;\n\ttext-align: center;\n\tletter-spacing: 1px;\n\t}\n\t\n.join-btn:active {\n\ttransform: scale(0.96);\n\tbox-shadow: 2rpx 2rpx 4rpx rgba(61, 86, 193, 0.3),\n\t\t\t\t-1rpx -1rpx 4rpx rgba(255, 255, 255, 0.3),\n\t\t\t\tinset 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.1);\n}\n\n.personal-join-btn {\n\tbackground: linear-gradient(145deg, #3e7ece, #4a89dc);\n\tbox-shadow: 5rpx 5rpx 10rpx rgba(74, 137, 220, 0.3),\n\t\t\t\t-2rpx -2rpx 8rpx rgba(255, 255, 255, 0.3),\n\t\t\t\tinset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.2);\n\t}\n\t\n.hot-badge {\n\tposition: absolute;\n\ttop: -2rpx;\n\tright: 6rpx;\n\tbackground: #FF5757;\n\tcolor: #ffffff;\n\tfont-size: 18rpx;\n\tfont-weight: bold;\n\tpadding: 4rpx 10rpx;\n\tborder-radius: 0 0 10rpx 10rpx;\n\tbox-shadow: 0 3rpx 6rpx rgba(255, 87, 87, 0.4);\n\tdisplay: block;\n\t}\n\t\n\t/* 发布类目 */\n\t.category-section {\n margin: 0 20rpx 30rpx;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 16rpx;\n padding: 20rpx 25rpx;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n\t\tanimation: fadeIn 0.6s ease-in-out;\n\t\tanimation-delay: 0.3s;\n\t\tanimation-fill-mode: both;\n\t}\n\t\n\t.category-section-title {\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tfont-weight: 600;\n padding: 8rpx 0 16rpx;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t\n\t.category-section-title::before {\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\twidth: 8rpx;\n\t\theight: 32rpx;\n\t\tbackground-color: #0052CC;\n\t\tborder-radius: 4rpx;\n\t\tmargin-right: 16rpx;\n\t}\n\t\n\t.category-grid {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tmargin: 0 -10rpx;\n\t}\n\t\n\t.category-item {\n\t\twidth: 25%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n padding: 15rpx 0;\n\t\tposition: relative;\n\t\ttransition: all 0.2s;\n\t\tanimation: fadeIn 0.3s ease-in-out;\n\t\tanimation-fill-mode: both;\n\t}\n\t\n\t/* 简化延迟动画 */\n\t.category-item:nth-child(1) { animation-delay: 0.1s; }\n\t.category-item:nth-child(2) { animation-delay: 0.1s; }\n\t.category-item:nth-child(3) { animation-delay: 0.1s; }\n\t.category-item:nth-child(4) { animation-delay: 0.1s; }\n\t.category-item:nth-child(5) { animation-delay: 0.1s; }\n\t.category-item:nth-child(6) { animation-delay: 0.15s; }\n\t.category-item:nth-child(7) { animation-delay: 0.15s; }\n\t.category-item:nth-child(8) { animation-delay: 0.15s; }\n\t.category-item:nth-child(9) { animation-delay: 0.15s; }\n\t.category-item:nth-child(10) { animation-delay: 0.15s; }\n\t.category-item:nth-child(11) { animation-delay: 0.2s; }\n\t.category-item:nth-child(12) { animation-delay: 0.2s; }\n\t.category-item:nth-child(13) { animation-delay: 0.2s; }\n\t.category-item:nth-child(14) { animation-delay: 0.2s; }\n\t.category-item:nth-child(15) { animation-delay: 0.2s; }\n\t\n\t.category-item::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 1;\n\t\tborder-radius: 0;\n\t}\n\t\n\t.category-item-hover {\n\t\ttransform: scale(0.95);\n\t\tbackground-color: rgba(0, 0, 0, 0.03);\n\t}\n\t\n\t.category-icon-wrapper {\n width: 100rpx;\n\theight: 100rpx;\n\tmargin-bottom: 8rpx;\n\t\tposition: relative;\n\t\tz-index: 2;\n\t\ttransition: all 0.2s;\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: transparent;\n\t\toverflow: hidden;\n\t}\n\t\n\t.category-item-hover .category-icon-wrapper {\n\t\ttransform: scale(0.9);\n\t}\n\t\n\t.category-icon {\n width: 75rpx;\n\theight: 75rpx;\n\t}\n\t\n\t.category-name {\n\t\tfont-size: 24rpx;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t\tposition: relative;\n\t\tz-index: 2;\n\t\tfont-weight: 500;\n margin-top: 6rpx;\n\t}\n\t\n\t.test-icon-wrapper {\n\t\tbackground-color: rgba(255, 107, 107, 0.1);\n\t\tborder: 2rpx dashed #ff6b6b;\n\t}\n\t\n\t.test-icon {\n\t\tfont-size: 32rpx;\n\t\tcolor: #ff4f4f;\n\t\tfont-weight: 600;\n\t}\n\t\n\t.test-category-item {\n\t\tposition: relative;\n\t}\n\t\n\t.test-badge {\n\t\tposition: absolute;\n\t\ttop: 6rpx;\n\t\tright: 8rpx;\n\t\tbackground: linear-gradient(135deg, #ff6b6b, #ff4f4f);\n\t\tcolor: white;\n\t\tfont-size: 16rpx;\n\t\tpadding: 2rpx 8rpx;\n\t\tborder-radius: 8rpx;\n\t\tbox-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.3);\n\t\tfont-weight: 500;\n\t\tz-index: 3;\n\t}\n\n/* 商家入驻测试按钮样式 */\n.merchant-test-category-item {\n\tposition: relative;\n}\n\n.merchant-test-icon-wrapper {\n\tbackground-color: #0052CC;\n}\n\n.merchant-test-badge {\n\tposition: absolute;\n\ttop: -5px;\n\tright: -5px;\n\tbackground: #ff4d4f;\n\tcolor: #ffffff;\n\tfont-size: 10px;\n\theight: 16px;\n\tmin-width: 16px;\n\tline-height: 16px;\n\ttext-align: center;\n\tborder-radius: 8px;\n\tpadding: 0 4px;\n\tfont-weight: bold;\n\t}\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/publish/publish.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "wx", "onLoad", "onShow", "MiniProgramPage"], "mappings": ";;;;;;;;;;AA8LA,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAC9B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,mBAAmBA,cAAAA,IAAI,CAAC;AAC9B,UAAM,oBAAoBA,cAAAA,IAAI,CAAC;AAC/B,UAAM,OAAOA,cAAAA,IAAI;AAAA,MAChB,WAAW;AAAA,IACZ,CAAC;AAKD,UAAM,kBAAkB,MAAM;AAC7B,UAAIC,cAAG,MAAC,WAAWA,cAAG,MAAC,QAAQ,aAAa,GAAG;AAC9C,YAAI;AAEHC,wBAAAA,KAAG,YAAY;AAAA,YACd,UAAU;AAAA,YACV,kBAAkB;AAAA,UACtB,CAAI;AAAA,QACD,SAAQ,GAAG;AACXD,wBAAA,MAAA,MAAA,SAAA,oCAAc,aAAa,CAAC;AAAA,QAC5B;AAAA,MACD;AAAA,IACF;AAGA,UAAM,gBAAgB,MAAM;AAC3B,YAAM,YAAY;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEC,UAAI,cAAc;AAClB,YAAM,cAAc,UAAU;AAE9B,gBAAU,QAAQ,SAAO;AACxBA,sBAAAA,MAAI,aAAa;AAAA,UAChB,KAAK;AAAA,UACL,SAAS,MAAM;AACd,4BAAgB,MAAM,GAAG,IAAI;AAC7B;AACA,gBAAI,gBAAgB,aAAa;AAChC,8BAAgB,QAAQ;AAAA,YACxB;AAAA,UACD;AAAA,UACD,MAAM,MAAM;AACXA,mFAAc,WAAW,GAAG;AAC5B;AACA,gBAAI,gBAAgB,aAAa;AAChC,8BAAgB,QAAQ;AAAA,YACxB;AAAA,UACD;AAAA,QACJ,CAAG;AAAA,MACH,CAAE;AAED,iBAAW,MAAM;AAChB,wBAAgB,QAAQ;AAAA,MACxB,GAAE,GAAI;AAAA,IACR;AAGA,UAAM,uBAAuB,MAAM;AAClC,UAAI;AACH,cAAM,WAAWA,cAAG,MAAC,eAAe,iBAAiB,KAAK,CAAA;AAC1D,yBAAiB,QAAQ,SAAS;AAAA,MAClC,SAAQ,GAAG;AACXA,sBAAc,MAAA,MAAA,SAAA,oCAAA,cAAc,CAAC;AAC7B,yBAAiB,QAAQ;AAAA,MACzB;AAAA,IACF;AAGA,UAAM,wBAAwB,MAAM;AACnC,UAAI;AACH,cAAM,mBAAmBA,cAAG,MAAC,eAAe,kBAAkB,KAAK,CAAA;AACnE,0BAAkB,QAAQ,iBAAiB;AAAA,MAC3C,SAAQ,GAAG;AACXA,+EAAc,kBAAkB,CAAC;AACjC,0BAAkB,QAAQ;AAAA,MAC1B;AAAA,IACF;AAGA,UAAM,sBAAsB,MAAM;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAWA,UAAM,mBAAmB,MAAM;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,kBAAM,WAAW,IAAI,WAAW;AAChCA,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,cACP,MAAM;AAAA,YACX,CAAK;AACD,uBAAW,MAAM;AAChBA,4BAAG,MAAC,YAAW;AACf,kBAAI;AACH,sBAAM,mBAAmBA,cAAG,MAAC,eAAe,kBAAkB,KAAK,CAAA;AACnE,sBAAM,kBAAkB;AAAA,kBACvB,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,kBACzB;AAAA,kBACA,SAAS;AAAA,kBACT,UAAU;AAAA,kBACV,OAAO;AAAA,kBACP,cAAc;AAAA,kBACd,cAAc;AAAA,kBACd,aAAa;AAAA,kBACb,aAAY,oBAAI,KAAM,GAAC,eAAgB;AAAA,kBACvC,YAAY;AAAA,kBACZ,WAAW;AAAA,kBACX,MAAM;AAAA,kBACN,QAAQ;AAAA,kBACR,OAAO,CAAE;AAAA,gBAChB;AACM,iCAAiB,KAAK,eAAe;AACrCA,8BAAAA,MAAI,eAAe,oBAAoB,gBAAgB;AACvD,kCAAkB,QAAQ,iBAAiB;AAC3CA,8BAAAA,MAAI,eAAe,kBAAkB,gBAAgB,EAAE;AACvDA,8BAAAA,MAAI,eAAe,oBAAoB,eAAe;AAGtDA,8BAAAA,MAAI,WAAW;AAAA,kBACd,KAAK,8BAA8B,gBAAgB,EAAE,aAAa,mBAAmB,QAAQ,CAAC,eAAe,mBAAmB,KAAK,CAAC;AAAA,kBACtI,MAAM,CAAC,QAAQ;AACdA,kCAAA,MAAA,MAAA,SAAA,oCAAc,kBAAkB,GAAG;AAEnCA,kCAAAA,MAAI,WAAW;AAAA,sBACd,KAAK,+BAA+B,gBAAgB,KAAK;AAAA,sBACzD,MAAM,CAAC,SAAS;AACfA,sCAAc,MAAA,MAAA,SAAA,oCAAA,iBAAiB,IAAI;AACnCA,sCAAAA,MAAI,UAAU;AAAA,0BACb,OAAO;AAAA,0BACP,MAAM;AAAA,0BACN,UAAU;AAAA,wBACrB,CAAW;AAAA,sBACD;AAAA,oBACV,CAAS;AAAA,kBACD;AAAA,gBACR,CAAO;AAAA,cACD,SAAQ,GAAG;AACXA,8BAAc,MAAA,MAAA,SAAA,oCAAA,cAAc,CAAC;AAC7BA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA,gBACjB,CAAO;AAAA,cACD;AAAA,YACD,GAAE,GAAG;AAAA,UACN;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,UAAM,0BAA0B,CAAC,SAAS;AACzC,UAAI,KAAK,SAAS,QAAQ;AACzB,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS;AAAA,YACT,UAAU;AAAA,YACV,iBAAiB;AAAA,YACjB,SAAS,CAAC,QAAQ;AACjB,kBAAI,IAAI,SAAS;AAChB,sBAAM,UAAU,IAAI,WAAW;AAC/BA,8BAAAA,MAAI,YAAY;AAAA,kBACf,OAAO;AAAA,kBACP,MAAM;AAAA,gBACb,CAAO;AACD,2BAAW,MAAM;AAChBA,gCAAG,MAAC,YAAW;AACf,sBAAI;AACH,0BAAM,WAAWA,cAAG,MAAC,eAAe,iBAAiB,KAAK,CAAA;AAC1D,0BAAM,UAAU;AAAA,sBACf,IAAI,KAAK,IAAK,EAAC,SAAU;AAAA,sBACzB,MAAM;AAAA,sBACN,UAAU;AAAA,sBACV,cAAc;AAAA,sBACd,OAAO;AAAA,sBACP;AAAA,sBACA,aAAY,oBAAI,KAAM,GAAC,eAAgB;AAAA,oBAChD;AACQ,6BAAS,KAAK,OAAO;AACrBA,kCAAAA,MAAI,eAAe,mBAAmB,QAAQ;AAC9C,qCAAiB,QAAQ,SAAS;AAClCA,kCAAAA,MAAI,eAAe,iBAAiB,QAAQ,EAAE;AAC9CA,kCAAAA,MAAI,eAAe,mBAAmB,OAAO;AAC7CA,kCAAAA,MAAI,WAAW;AAAA,sBACd,KAAK,+BAA+B,QAAQ;AAAA,sBAC5C,MAAM,CAAC,QAAQ;AACdA,sCAAA,MAAA,MAAA,SAAA,oCAAc,gBAAgB,GAAG;AACjCA,sCAAAA,MAAI,UAAU;AAAA,0BACb,OAAO;AAAA,0BACP,MAAM;AAAA,0BACN,UAAU;AAAA,wBACrB,CAAW;AAAA,sBACD;AAAA,oBACV,CAAS;AAAA,kBACD,SAAQ,GAAG;AACXA,kCAAc,MAAA,MAAA,SAAA,oCAAA,YAAY,CAAC;AAC3BA,kCAAAA,MAAI,UAAU;AAAA,sBACb,OAAO;AAAA,sBACP,MAAM;AAAA,sBACN,UAAU;AAAA,oBACnB,CAAS;AAAA,kBACD;AAAA,gBACD,GAAE,GAAG;AAAA,cACN;AAAA,YACD;AAAA,UACL,CAAI;AAAA,QACD,GAAE,GAAG;AACN;AAAA,MACA;AAEDA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AACD,YAAM,SAAS;AAAA,QACd,MAAM,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,QACX,UAAU,KAAK;AAAA,QACf,cAAc,KAAK;AAAA,MACrB;AACC,YAAM,cAAc,OAAO,KAAK,MAAM,EACpC,IAAI,SAAO,GAAG,GAAG,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EACtD,KAAK,GAAG;AACV,UAAI;AACHA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,yBAAyB,WAAW;AAAA,UACzC,SAAS,WAAW;AACnBA,0BAAAA,MAAY,MAAA,OAAA,oCAAA,MAAM;AAClBA,0BAAG,MAAC,YAAW;AAAA,UACf;AAAA,UACD,MAAM,SAAS,KAAK;AACnBA,0BAAc,MAAA,MAAA,SAAA,oCAAA,SAAS,GAAG;AAC1BA,0BAAAA,MAAI,WAAW;AAAA,cACd,KAAK,qBAAqB,WAAW;AAAA,cACrC,SAAS,WAAW;AACnBA,8BAAG,MAAC,YAAW;AAAA,cACf;AAAA,cACD,MAAM,SAAS,MAAM;AACpBA,8BAAc,MAAA,MAAA,SAAA,oCAAA,YAAY,IAAI;AAC9BA,8BAAG,MAAC,YAAW;AACfA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACb,CAAO;AAAA,cACD;AAAA,YACN,CAAK;AAAA,UACD;AAAA,QACJ,CAAG;AAAA,MACD,SAAQ,GAAG;AACXA,sBAAA,MAAA,MAAA,SAAA,oCAAc,SAAS,CAAC;AACxBA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAEA,UAAM,sBAAsB,MAAM;AACjC,UAAI;AACH,cAAM,WAAWA,cAAG,MAAC,eAAe,iBAAiB,KAAK,CAAA;AAC1D,YAAI,SAAS,WAAW,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD;AAAA,QACA;AACDA,sBAAAA,MAAI,gBAAgB;AAAA,UACnB,UAAU,CAAC,UAAU,QAAQ;AAAA,UAC7B,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,aAAa,GAAG;AACvB,2BAAa,QAAQ;AAAA,YAC1B,WAAe,IAAI,aAAa,GAAG;AAC9B;YACA;AAAA,UACD;AAAA,QACJ,CAAG;AAAA,MACD,SAAQ,GAAG;AACXA,sBAAc,MAAA,MAAA,SAAA,oCAAA,eAAe,CAAC;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAEA,UAAM,eAAe,CAAC,aAAa;AAClC,YAAM,WAAW,SAAS,IAAI,CAAC,MAAM,UAAU;AAC9C,eAAO,GAAG,QAAQ,CAAC,KAAK,KAAK,OAAO,KAAK,KAAK,IAAI;AAAA,MACpD,CAAE,EAAE,KAAK,MAAM;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,MACd,CAAE;AAAA,IACF;AAEA,UAAM,gBAAgB,MAAM;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,gBAAI;AACHA,kCAAI,kBAAkB,iBAAiB;AACvC,+BAAiB,QAAQ;AACzBA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACZ,CAAM;AAAA,YACD,SAAQ,GAAG;AACXA,4BAAc,MAAA,MAAA,SAAA,oCAAA,YAAY,CAAC;AAC3BA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACZ,CAAM;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAEA,UAAM,4BAA4B,MAAM;AACvCA,oBAAAA,MAAY,MAAA,OAAA,oCAAA,aAAa;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAIAE,kBAAAA,OAAO,MAAM;AACZ;AACA;AACA;AACA;IACD,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AACZ;AACA;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtjBD,GAAG,WAAWC,SAAe;"}