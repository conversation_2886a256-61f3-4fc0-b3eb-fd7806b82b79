/*!
  * vue-i18n v9.1.7
  * (c) 2021 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
import{ref as e,getCurrentInstance as t,computed as n,watch as r,createVNode as a,Text as o,h as s,Fragment as l,inject as c,onMounted as i,onUnmounted as u,isRef as f}from"vue";const m="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,p=e=>m?Symbol(e):e,g=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),d=e=>"number"==typeof e&&isFinite(e),b=e=>"[object RegExp]"===$(e),h=e=>M(e)&&0===Object.keys(e).length;function k(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const _=Object.assign;function v(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const y=Object.prototype.hasOwnProperty;function F(e,t){return y.call(e,t)}const L=Array.isArray,w=e=>"function"==typeof e,T=e=>"string"==typeof e,x=e=>"boolean"==typeof e,P=e=>null!==e&&"object"==typeof e,C=Object.prototype.toString,$=e=>C.call(e),M=e=>"[object Object]"===$(e),O=[];O[0]={w:[0],i:[3,0],"[":[4],o:[7]},O[1]={w:[1],".":[2],"[":[4],o:[7]},O[2]={w:[2],i:[3,0],0:[3,0]},O[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},O[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},O[5]={"'":[4,0],o:8,l:[5,0]},O[6]={'"':[4,0],o:8,l:[6,0]};const W=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function N(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function S(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(W.test(t)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t)}const I=new Map;function E(e,t){if(!P(e))return null;let n=I.get(t);if(n||(n=function(e){const t=[];let n,r,a,o,s,l,c,i=-1,u=0,f=0;const m=[];function p(){const t=e[i+1];if(5===u&&"'"===t||6===u&&'"'===t)return i++,a="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=a:r+=a},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,u=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=S(r),!1===r)return!1;m[1]()}};null!==u;)if(i++,n=e[i],"\\"!==n||!p()){if(o=N(n),c=O[u],s=c[o]||c.l||8,8===s)return;if(u=s[0],void 0!==s[1]&&(l=m[s[1]],l&&(a=n,!1===l())))return;if(7===u)return t}}(t),n&&I.set(t,n)),!n)return null;const r=n.length;let a=e,o=0;for(;o<r;){const e=a[n[o]];if(void 0===e)return null;a=e,o++}return a}function H(e){if(!P(e))return e;for(const t in e)if(F(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let a=e;for(let e=0;e<r;e++)n[e]in a||(a[n[e]]={}),a=a[n[e]];a[n[r]]=e[t],delete e[t],P(a[n[r]])&&H(a[n[r]])}else P(e[t])&&H(e[t]);return e}const j=e=>e,R=e=>"",D=e=>0===e.length?"":e.join(""),A=e=>null==e?"":L(e)||M(e)&&e.toString===C?JSON.stringify(e,null,2):String(e);function U(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function z(e={}){const t=e.locale,n=function(e){const t=d(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(d(e.named.count)||d(e.named.n))?d(e.named.count)?e.named.count:d(e.named.n)?e.named.n:t:t}(e),r=P(e.pluralRules)&&T(t)&&w(e.pluralRules[t])?e.pluralRules[t]:U,a=P(e.pluralRules)&&T(t)&&w(e.pluralRules[t])?U:void 0,o=e.list||[],s=e.named||{};d(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,s);function l(t){const n=w(e.messages)?e.messages(t):!!P(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):R)}const c=M(e.processor)&&w(e.processor.normalize)?e.processor.normalize:D,i=M(e.processor)&&w(e.processor.interpolate)?e.processor.interpolate:A,u={list:e=>o[e],named:e=>s[e],plural:e=>e[r(n,e.length,a)],linked:(t,n)=>{const r=l(t)(u);return T(n)?(a=n,e.modifiers?e.modifiers[a]:j)(r):r;var a},message:l,type:M(e.processor)&&T(e.processor.type)?e.processor.type:"text",interpolate:i,normalize:c};return u}function J(e){throw e}function V(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const q=String.fromCharCode(8232),B=String.fromCharCode(8233);function G(e){const t=e;let n=0,r=1,a=1,o=0;const s=e=>"\r"===t[e]&&"\n"===t[e+1],l=e=>t[e]===B,c=e=>t[e]===q,i=e=>s(e)||(e=>"\n"===t[e])(e)||l(e)||c(e),u=e=>s(e)||l(e)||c(e)?"\n":t[e];function f(){return o=0,i(n)&&(r++,a=0),s(n)&&n++,n++,a++,t[n]}return{index:()=>n,line:()=>r,column:()=>a,peekOffset:()=>o,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+o),next:f,peek:function(){return s(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,a=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)f();o=0}}}const Y=void 0;function K(e,t={}){const n=!1!==t.location,r=G(e),a=()=>r.index(),o=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},s=o(),l=a(),c={currentType:14,offset:l,startLoc:s,endLoc:s,lastType:14,lastOffset:l,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},i=()=>c,{onError:u}=t;function f(e,t,r){e.endLoc=o(),e.currentType=t;const a={type:t};return n&&(a.loc=V(e.startLoc,e.endLoc)),null!=r&&(a.value=r),a}const m=e=>f(e,14);function p(e,t){return e.currentChar()===t?(e.next(),t):(o(),"")}function g(e){let t="";for(;" "===e.currentPeek()||"\n"===e.currentPeek();)t+=e.currentPeek(),e.peek();return t}function d(e){const t=g(e);return e.skipToPeek(),t}function b(e){if(e===Y)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function h(e,t){const{currentType:n}=t;if(2!==n)return!1;g(e);const r=function(e){if(e===Y)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function k(e){g(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function _(e,t=!0){const n=(t=!1,r="",a=!1)=>{const o=e.currentPeek();return"{"===o?"%"!==r&&t:"@"!==o&&o?"%"===o?(e.peek(),n(t,"%",!0)):"|"===o?!("%"!==r&&!a)||!(" "===r||"\n"===r):" "===o?(e.peek(),n(!0," ",a)):"\n"!==o||(e.peek(),n(!0,"\n",a)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function v(e,t){const n=e.currentChar();return n===Y?Y:t(n)?(e.next(),n):null}function y(e){return v(e,(e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}))}function F(e){return v(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57}))}function L(e){return v(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}))}function w(e){let t="",n="";for(;t=F(e);)n+=t;return n}function T(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return x(e,t,4);case"U":return x(e,t,6);default:return o(),""}}function x(e,t,n){p(e,t);let r="";for(let t=0;t<n;t++){const t=L(e);if(!t){o(),e.currentChar();break}r+=t}return`\\${t}${r}`}function P(e){d(e);const t=p(e,"|");return d(e),t}function C(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&o(),e.next(),n=f(t,2,"{"),d(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&o(),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&d(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&o(),n=$(e,t)||m(t),t.braceNest=0,n;default:let r=!0,a=!0,s=!0;if(k(e))return t.braceNest>0&&o(),n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return o(),t.braceNest=0,M(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;g(e);const r=b(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){d(e);let t="",n="";for(;t=y(e);)n+=t;return e.currentChar()===Y&&o(),n}(e)),d(e),n;if(a=h(e,t))return n=f(t,6,function(e){d(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${w(e)}`):t+=w(e),e.currentChar()===Y&&o(),t}(e)),d(e),n;if(s=function(e,t){const{currentType:n}=t;if(2!==n)return!1;g(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){d(e),p(e,"'");let t="",n="";const r=e=>"'"!==e&&"\n"!==e;for(;t=v(e,r);)n+="\\"===t?T(e):t;const a=e.currentChar();return"\n"===a||a===Y?(o(),"\n"===a&&(e.next(),p(e,"'")),n):(p(e,"'"),n)}(e)),d(e),n;if(!r&&!a&&!s)return n=f(t,13,function(e){d(e);let t="",n="";const r=e=>"{"!==e&&"}"!==e&&" "!==e&&"\n"!==e;for(;t=v(e,r);)n+=t;return n}(e)),o(),d(e),n}return n}function $(e,t){const{currentType:n}=t;let r=null;const a=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||"\n"!==a&&" "!==a||o(),a){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return d(e),e.next(),f(t,9,".");case":":return d(e),e.next(),f(t,10,":");default:return k(e)?(r=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;g(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;g(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(d(e),$(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;g(e);const r=b(e.currentPeek());return e.resetPeek(),r}(e,t)?(d(e),f(t,12,function(e){let t="",n="";for(;t=y(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?b(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||" "===t||!t)&&("\n"===t?(e.peek(),r()):b(t))},a=r();return e.resetPeek(),a}(e,t)?(d(e),"{"===a?C(e,t)||r:f(t,11,function(e){const t=(n=!1,r)=>{const a=e.currentChar();return"{"!==a&&"%"!==a&&"@"!==a&&"|"!==a&&a?" "===a?r:"\n"===a?(r+=a,e.next(),t(n,r)):(r+=a,e.next(),t(!0,r)):r};return t(!1,"")}(e))):(8===n&&o(),t.braceNest=0,t.inLinked=!1,M(e,t))}}function M(e,t){let n={type:14};if(t.braceNest>0)return C(e,t)||m(t);if(t.inLinked)return $(e,t)||m(t);const r=e.currentChar();switch(r){case"{":return C(e,t)||m(t);case"}":return o(),e.next(),f(t,3,"}");case"@":return $(e,t)||m(t);default:if(k(e))return n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;if(_(e))return f(t,0,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"}"!==r&&"@"!==r&&r?"%"===r?_(e)?(n+=r,e.next(),t(n)):n:"|"===r?n:" "===r||"\n"===r?_(e)?(n+=r,e.next(),t(n)):k(e)?n:(n+=r,e.next(),t(n)):(n+=r,e.next(),t(n)):n};return t("")}(e));if("%"===r)return e.next(),f(t,4,"%")}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:s}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=n,c.lastEndLoc=s,c.offset=a(),c.startLoc=o(),r.currentChar()===Y?f(c,14):M(r,c)},currentOffset:a,currentPosition:o,context:i}}const Z=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Q(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function X(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const a={type:e,start:n,end:n};return t&&(a.loc={start:r,end:r}),a}function a(e,n,r,a){e.end=n,a&&(e.type=a),t&&e.loc&&(e.loc.end=r)}function o(e,t){const n=e.context(),o=r(3,n.offset,n.startLoc);return o.value=t,a(o,e.currentOffset(),e.currentPosition()),o}function s(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(5,o,s);return l.index=parseInt(t,10),e.nextToken(),a(l,e.currentOffset(),e.currentPosition()),l}function l(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(4,o,s);return l.key=t,e.nextToken(),a(l,e.currentOffset(),e.currentPosition()),l}function c(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(9,o,s);return l.value=t.replace(Z,Q),e.nextToken(),a(l,e.currentOffset(),e.currentPosition()),l}function i(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let o=e.nextToken();if(9===o.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:o,lastStartLoc:s}=n,l=r(8,o,s);return 12!==t.type?(l.value="",a(l,o,s),{nextConsumeToken:t,node:l}):(null==t.value&&ee(t),l.value=t.value||"",a(l,e.currentOffset(),e.currentPosition()),{node:l})}(e);n.modifier=t.node,o=t.nextConsumeToken||e.nextToken()}switch(10!==o.type&&ee(o),o=e.nextToken(),2===o.type&&(o=e.nextToken()),o.type){case 11:null==o.value&&ee(o),n.key=function(e,t){const n=e.context(),o=r(7,n.offset,n.startLoc);return o.value=t,a(o,e.currentOffset(),e.currentPosition()),o}(e,o.value||"");break;case 5:null==o.value&&ee(o),n.key=l(e,o.value||"");break;case 6:null==o.value&&ee(o),n.key=s(e,o.value||"");break;case 7:null==o.value&&ee(o),n.key=c(e,o.value||"");break;default:const t=e.context(),i=r(7,t.offset,t.startLoc);return i.value="",a(i,t.offset,t.startLoc),n.key=i,a(n,t.offset,t.startLoc),{nextConsumeToken:o,node:n}}return a(n,e.currentOffset(),e.currentPosition()),{node:n}}function u(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let u=null;do{const t=u||e.nextToken();switch(u=null,t.type){case 0:null==t.value&&ee(t),n.items.push(o(e,t.value||""));break;case 6:null==t.value&&ee(t),n.items.push(s(e,t.value||""));break;case 5:null==t.value&&ee(t),n.items.push(l(e,t.value||""));break;case 7:null==t.value&&ee(t),n.items.push(c(e,t.value||""));break;case 8:const r=i(e);n.items.push(r.node),u=r.nextConsumeToken||null}}while(14!==t.currentType&&1!==t.currentType);return a(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function f(e){const t=e.context(),{offset:n,startLoc:o}=t,s=u(e);return 14===t.currentType?s:function(e,t,n,o){const s=e.context();let l=0===o.items.length;const c=r(1,t,n);c.cases=[],c.cases.push(o);do{const t=u(e);l||(l=0===t.items.length),c.cases.push(t)}while(14!==s.currentType);return a(c,e.currentOffset(),e.currentPosition()),c}(e,n,o,s)}return{parse:function(n){const o=K(n,_({},e)),s=o.context(),l=r(0,s.offset,s.startLoc);return t&&l.loc&&(l.loc.source=n),l.body=f(o),a(l,o.currentOffset(),o.currentPosition()),l}}}function ee(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function te(e,t){for(let n=0;n<e.length;n++)ne(e[n],t)}function ne(e,t){switch(e.type){case 1:te(e.cases,t),t.helper("plural");break;case 2:te(e.items,t);break;case 6:ne(e.key,t),t.helper("linked");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function re(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&ne(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function ae(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?ae(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const a=t.cases.length;for(let n=0;n<a&&(ae(e,t.cases[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const a=t.items.length;for(let n=0;n<a&&(ae(e,t.items[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),ae(e,t.key),t.modifier&&(e.push(", "),ae(e,t.modifier)),e.push(")")}(e,t);break;case 8:case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:case 3:e.push(JSON.stringify(t.value),t)}}function oe(e,t={}){const n=_({},t),r=X(n).parse(e);return re(r,n),((e,t={})=>{const n=T(t.mode)?t.mode:"normal",r=T(t.filename)?t.filename:"message.intl",a=t.needIndent?t.needIndent:"arrow"!==n,o=e.helpers||[],s=function(e,t){const{filename:n,breakLineCode:r,needIndent:a}=t,o={source:e.loc.source,filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:a,indentLevel:0};function s(e,t){o.code+=e}function l(e,t=!0){const n=t?r:"";s(a?n+"  ".repeat(e):n)}return{context:()=>o,push:s,indent:function(e=!0){const t=++o.indentLevel;e&&l(t)},deindent:function(e=!0){const t=--o.indentLevel;e&&l(t)},newline:function(){l(o.indentLevel)},helper:e=>`_${e}`,needIndent:()=>o.needIndent}}(e,{mode:n,filename:r,sourceMap:!!t.sourceMap,breakLineCode:null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",needIndent:a});s.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),s.indent(a),o.length>0&&(s.push(`const { ${o.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),s.newline()),s.push("return "),ae(s,e),s.deindent(a),s.push("}");const{code:l,map:c}=s.context();return{ast:e,code:l,map:c?c.toJSON():void 0}})(r,n)}let se;let le=0;function ce(e={}){const t=T(e.version)?e.version:"9.1.7",n=T(e.locale)?e.locale:"en-US",r=L(e.fallbackLocale)||M(e.fallbackLocale)||T(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,a=M(e.messages)?e.messages:{[n]:{}},o=M(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},s=M(e.numberFormats)?e.numberFormats:{[n]:{}},l=_({},e.modifiers||{},{upper:e=>T(e)?e.toUpperCase():e,lower:e=>T(e)?e.toLowerCase():e,capitalize:e=>T(e)?`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`:e}),c=e.pluralRules||{},i=w(e.missing)?e.missing:null,u=!x(e.missingWarn)&&!b(e.missingWarn)||e.missingWarn,f=!x(e.fallbackWarn)&&!b(e.fallbackWarn)||e.fallbackWarn,m=!!e.fallbackFormat,p=!!e.unresolving,g=w(e.postTranslation)?e.postTranslation:null,d=M(e.processor)?e.processor:null,h=!x(e.warnHtmlMessage)||e.warnHtmlMessage,v=!!e.escapeParameter,y=w(e.messageCompiler)?e.messageCompiler:se,F=w(e.onWarn)?e.onWarn:k,C=e,$=P(C.__datetimeFormatters)?C.__datetimeFormatters:new Map,O=P(C.__numberFormatters)?C.__numberFormatters:new Map,W=P(C.__meta)?C.__meta:{};le++;return{version:t,cid:le,locale:n,fallbackLocale:r,messages:a,datetimeFormats:o,numberFormats:s,modifiers:l,pluralRules:c,missing:i,missingWarn:u,fallbackWarn:f,fallbackFormat:m,unresolving:p,postTranslation:g,processor:d,warnHtmlMessage:h,escapeParameter:v,messageCompiler:y,onWarn:F,__datetimeFormatters:$,__numberFormatters:O,__meta:W}}function ie(e,t,n,r,a){const{missing:o}=e;if(null!==o){const r=o(e,n,t,a);return T(r)?r:t}return t}function ue(e,t,n){const r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let a=r.__localeChainCache.get(n);if(!a){a=[];let e=[n];for(;L(e);)e=fe(a,e,t);const o=L(t)?t:M(t)?t.default?t.default:null:t;e=T(o)?[o]:o,L(e)&&fe(a,e,!1),r.__localeChainCache.set(n,a)}return a}function fe(e,t,n){let r=!0;for(let a=0;a<t.length&&x(r);a++){T(t[a])&&(r=me(e,t[a],n))}return r}function me(e,t,n){let r;const a=t.split("-");do{r=pe(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function pe(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(L(n)||M(n))&&n[a]&&(r=n[a])}return r}function ge(e,t,n){e.__localeChainCache=new Map,ue(e,n,t)}const de=e=>e;let be=Object.create(null);const he=()=>"",ke=e=>w(e);function _e(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:a,fallbackLocale:o,messages:s}=e,[l,c]=ye(...t),i=(x(c.missingWarn),x(c.fallbackWarn),x(c.escapeParameter)?c.escapeParameter:e.escapeParameter),u=!!c.resolvedMessage,f=T(c.default)||x(c.default)?x(c.default)?l:c.default:n?l:"",m=n||""!==f,p=T(c.locale)?c.locale:e.locale;i&&function(e){L(e.list)?e.list=e.list.map((e=>T(e)?v(e):e)):P(e.named)&&Object.keys(e.named).forEach((t=>{T(e.named[t])&&(e.named[t]=v(e.named[t]))}))}(c);let[g,b,h]=u?[l,p,s[p]||{}]:function(e,t,n,r,a,o){const{messages:s}=e,l=ue(e,r,n);let c,i={},u=null;const f="translate";for(let n=0;n<l.length&&(c=l[n],i=s[c]||{},null===(u=E(i,t))&&(u=i[t]),!T(u)&&!w(u));n++){const n=ie(e,t,c,0,f);n!==t&&(u=n)}return[u,c,i]}(e,l,p,o),k=l;if(u||T(g)||ke(g)||m&&(g=f,k=g),!(u||(T(g)||ke(g))&&T(b)))return a?-1:l;let _=!1;const y=ke(g)?g:ve(e,l,b,g,k,(()=>{_=!0}));if(_)return g;const F=function(e,t,n){return t(n)}(0,y,z(function(e,t,n,r){const{modifiers:a,pluralRules:o}=e,s={locale:t,modifiers:a,pluralRules:o,messages:r=>{const a=E(n,r);if(T(a)){let n=!1;const o=ve(e,r,t,a,r,(()=>{n=!0}));return n?he:o}return ke(a)?a:he}};e.processor&&(s.processor=e.processor);r.list&&(s.list=r.list);r.named&&(s.named=r.named);d(r.plural)&&(s.pluralIndex=r.plural);return s}(e,b,h,c)));return r?r(F):F}function ve(e,t,n,r,a,o){const{messageCompiler:s,warnHtmlMessage:l}=e;if(ke(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}const c=s(r,function(e,t,n,r,a,o){return{warnHtmlMessage:a,onError:e=>{throw o&&o(e),e},onCacheKey:e=>((e,t,n)=>g({l:e,k:t,s:n}))(t,n,e)}}(0,n,a,0,l,o));return c.locale=n,c.key=t,c.source=r,c}function ye(...e){const[t,n,r]=e,a={};if(!T(t)&&!d(t)&&!ke(t))throw Error(14);const o=d(t)?String(t):(ke(t),t);return d(n)?a.plural=n:T(n)?a.default=n:M(n)&&!h(n)?a.named=n:L(n)&&(a.list=n),d(r)?a.plural=r:T(r)?a.default=r:M(r)&&_(a,r),[o,a]}function Fe(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a}=e,{__datetimeFormatters:o}=e,[s,l,c,i]=Le(...t);x(c.missingWarn);x(c.fallbackWarn);const u=!!c.part,f=T(c.locale)?c.locale:e.locale,m=ue(e,a,f);if(!T(s)||""===s)return new Intl.DateTimeFormat(f).format(l);let p,g={},d=null;for(let t=0;t<m.length&&(p=m[t],g=n[p]||{},d=g[s],!M(d));t++)ie(e,s,p,0,"datetime format");if(!M(d)||!T(p))return r?-1:s;let b=`${p}__${s}`;h(i)||(b=`${b}__${JSON.stringify(i)}`);let k=o.get(b);return k||(k=new Intl.DateTimeFormat(p,_({},d,i)),o.set(b,k)),u?k.formatToParts(l):k.format(l)}function Le(...e){const[t,n,r,a]=e;let o,s={},l={};if(T(t)){if(!/\d{4}-\d{2}-\d{2}(T.*)?/.test(t))throw Error(16);o=new Date(t);try{o.toISOString()}catch(e){throw Error(16)}}else if("[object Date]"===$(t)){if(isNaN(t.getTime()))throw Error(15);o=t}else{if(!d(t))throw Error(14);o=t}return T(n)?s.key=n:M(n)&&(s=n),T(r)?s.locale=r:M(r)&&(l=r),M(a)&&(l=a),[s.key||"",o,s,l]}function we(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__datetimeFormatters.has(n)&&r.__datetimeFormatters.delete(n)}}function Te(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a}=e,{__numberFormatters:o}=e,[s,l,c,i]=xe(...t);x(c.missingWarn);x(c.fallbackWarn);const u=!!c.part,f=T(c.locale)?c.locale:e.locale,m=ue(e,a,f);if(!T(s)||""===s)return new Intl.NumberFormat(f).format(l);let p,g={},d=null;for(let t=0;t<m.length&&(p=m[t],g=n[p]||{},d=g[s],!M(d));t++)ie(e,s,p,0,"number format");if(!M(d)||!T(p))return r?-1:s;let b=`${p}__${s}`;h(i)||(b=`${b}__${JSON.stringify(i)}`);let k=o.get(b);return k||(k=new Intl.NumberFormat(p,_({},d,i)),o.set(b,k)),u?k.formatToParts(l):k.format(l)}function xe(...e){const[t,n,r,a]=e;let o={},s={};if(!d(t))throw Error(14);const l=t;return T(n)?o.key=n:M(n)&&(o=n),T(r)?o.locale=r:M(r)&&(s=r),M(a)&&(s=a),[o.key||"",l,o,s]}function Pe(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__numberFormatters.has(n)&&r.__numberFormatters.delete(n)}}const Ce="9.1.7",$e=p("__transrateVNode"),Me=p("__datetimeParts"),Oe=p("__numberParts"),We=p("__setPluralRules");let Ne=0;function Se(e){return(n,r,a,o)=>e(r,a,t()||void 0,o)}function Ie(e,t){const{messages:n,__i18n:r}=t,a=M(n)?n:L(r)?{}:{[e]:{}};if(L(r)&&r.forEach((({locale:e,resource:t})=>{e?(a[e]=a[e]||{},He(t,a[e])):He(t,a)})),t.flatJson)for(const e in a)F(a,e)&&H(a[e]);return a}const Ee=e=>!P(e)||L(e);function He(e,t){if(Ee(e)||Ee(t))throw Error(20);for(const n in e)F(e,n)&&(Ee(e[n])||Ee(t[n])?t[n]=e[n]:He(e[n],t[n]))}function je(t={}){const{__root:s}=t,l=void 0===s;let c=!x(t.inheritLocale)||t.inheritLocale;const i=e(s&&c?s.locale.value:T(t.locale)?t.locale:"en-US"),u=e(s&&c?s.fallbackLocale.value:T(t.fallbackLocale)||L(t.fallbackLocale)||M(t.fallbackLocale)||!1===t.fallbackLocale?t.fallbackLocale:i.value),f=e(Ie(i.value,t)),m=e(M(t.datetimeFormats)?t.datetimeFormats:{[i.value]:{}}),p=e(M(t.numberFormats)?t.numberFormats:{[i.value]:{}});let g=s?s.missingWarn:!x(t.missingWarn)&&!b(t.missingWarn)||t.missingWarn,h=s?s.fallbackWarn:!x(t.fallbackWarn)&&!b(t.fallbackWarn)||t.fallbackWarn,k=s?s.fallbackRoot:!x(t.fallbackRoot)||t.fallbackRoot,v=!!t.fallbackFormat,y=w(t.missing)?t.missing:null,F=w(t.missing)?Se(t.missing):null,C=w(t.postTranslation)?t.postTranslation:null,$=!x(t.warnHtmlMessage)||t.warnHtmlMessage,O=!!t.escapeParameter;const W=s?s.modifiers:M(t.modifiers)?t.modifiers:{};let N,S=t.pluralRules||s&&s.pluralRules;N=ce({version:"9.1.7",locale:i.value,fallbackLocale:u.value,messages:f.value,datetimeFormats:m.value,numberFormats:p.value,modifiers:W,pluralRules:S,missing:null===F?void 0:F,missingWarn:g,fallbackWarn:h,fallbackFormat:v,unresolving:!0,postTranslation:null===C?void 0:C,warnHtmlMessage:$,escapeParameter:O,__datetimeFormatters:M(N)?N.__datetimeFormatters:void 0,__numberFormatters:M(N)?N.__numberFormatters:void 0,__v_emitter:M(N)?N.__v_emitter:void 0,__meta:{framework:"vue"}}),ge(N,i.value,u.value);const I=n({get:()=>i.value,set:e=>{i.value=e,N.locale=i.value}}),H=n({get:()=>u.value,set:e=>{u.value=e,N.fallbackLocale=u.value,ge(N,i.value,e)}}),j=n((()=>f.value)),R=n((()=>m.value)),D=n((()=>p.value));function A(e,t,n,r,a,o){let l;if(l=e(N),d(l)&&-1===l){const[e,n]=t();return s&&k?r(s):a(e)}if(o(l))return l;throw Error(14)}function U(...e){return A((t=>_e(t,...e)),(()=>ye(...e)),0,(t=>t.t(...e)),(e=>e),(e=>T(e)))}const z={normalize:function(e){return e.map((e=>T(e)?a(o,null,e,0):e))},interpolate:e=>e,type:"vnode"};function J(e){return f.value[e]||{}}Ne++,s&&(r(s.locale,(e=>{c&&(i.value=e,N.locale=e,ge(N,i.value,u.value))})),r(s.fallbackLocale,(e=>{c&&(u.value=e,N.fallbackLocale=e,ge(N,i.value,u.value))})));return{id:Ne,locale:I,fallbackLocale:H,get inheritLocale(){return c},set inheritLocale(e){c=e,e&&s&&(i.value=s.locale.value,u.value=s.fallbackLocale.value,ge(N,i.value,u.value))},get availableLocales(){return Object.keys(f.value).sort()},messages:j,datetimeFormats:R,numberFormats:D,get modifiers(){return W},get pluralRules(){return S||{}},get isGlobal(){return l},get missingWarn(){return g},set missingWarn(e){g=e,N.missingWarn=g},get fallbackWarn(){return h},set fallbackWarn(e){h=e,N.fallbackWarn=h},get fallbackRoot(){return k},set fallbackRoot(e){k=e},get fallbackFormat(){return v},set fallbackFormat(e){v=e,N.fallbackFormat=v},get warnHtmlMessage(){return $},set warnHtmlMessage(e){$=e,N.warnHtmlMessage=e},get escapeParameter(){return O},set escapeParameter(e){O=e,N.escapeParameter=e},t:U,rt:function(...e){const[t,n,r]=e;if(r&&!P(r))throw Error(15);return U(t,n,_({resolvedMessage:!0},r||{}))},d:function(...e){return A((t=>Fe(t,...e)),(()=>Le(...e)),0,(t=>t.d(...e)),(()=>""),(e=>T(e)))},n:function(...e){return A((t=>Te(t,...e)),(()=>xe(...e)),0,(t=>t.n(...e)),(()=>""),(e=>T(e)))},te:function(e,t){return null!==E(J(T(t)?t:i.value),e)},tm:function(e){const t=function(e){let t=null;const n=ue(N,u.value,i.value);for(let r=0;r<n.length;r++){const a=E(f.value[n[r]]||{},e);if(null!=a){t=a;break}}return t}(e);return null!=t?t:s&&s.tm(e)||{}},getLocaleMessage:J,setLocaleMessage:function(e,t){f.value[e]=t,N.messages=f.value},mergeLocaleMessage:function(e,t){f.value[e]=f.value[e]||{},He(t,f.value[e]),N.messages=f.value},getDateTimeFormat:function(e){return m.value[e]||{}},setDateTimeFormat:function(e,t){m.value[e]=t,N.datetimeFormats=m.value,we(N,e,t)},mergeDateTimeFormat:function(e,t){m.value[e]=_(m.value[e]||{},t),N.datetimeFormats=m.value,we(N,e,t)},getNumberFormat:function(e){return p.value[e]||{}},setNumberFormat:function(e,t){p.value[e]=t,N.numberFormats=p.value,Pe(N,e,t)},mergeNumberFormat:function(e,t){p.value[e]=_(p.value[e]||{},t),N.numberFormats=p.value,Pe(N,e,t)},getPostTranslationHandler:function(){return w(C)?C:null},setPostTranslationHandler:function(e){C=e,N.postTranslation=e},getMissingHandler:function(){return y},setMissingHandler:function(e){null!==e&&(F=Se(e)),y=e,N.missing=F},[$e]:function(...e){return A((t=>{let n;const r=t;try{r.processor=z,n=_e(r,...e)}finally{r.processor=null}return n}),(()=>ye(...e)),0,(t=>t[$e](...e)),(e=>[a(o,null,e,0)]),(e=>L(e)))},[Oe]:function(...e){return A((t=>Te(t,...e)),(()=>xe(...e)),0,(t=>t[Oe](...e)),(()=>[]),(e=>T(e)||L(e)))},[Me]:function(...e){return A((t=>Fe(t,...e)),(()=>Le(...e)),0,(t=>t[Me](...e)),(()=>[]),(e=>T(e)||L(e)))},[We]:function(e){S=e,N.pluralRules=S}}}function Re(e={}){const t=je(function(e){const t=T(e.locale)?e.locale:"en-US",n=T(e.fallbackLocale)||L(e.fallbackLocale)||M(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=w(e.missing)?e.missing:void 0,a=!x(e.silentTranslationWarn)&&!b(e.silentTranslationWarn)||!e.silentTranslationWarn,o=!x(e.silentFallbackWarn)&&!b(e.silentFallbackWarn)||!e.silentFallbackWarn,s=!x(e.fallbackRoot)||e.fallbackRoot,l=!!e.formatFallbackMessages,c=M(e.modifiers)?e.modifiers:{},i=e.pluralizationRules,u=w(e.postTranslation)?e.postTranslation:void 0,f=!T(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,m=!!e.escapeParameterHtml,p=!x(e.sync)||e.sync;let g=e.messages;if(M(e.sharedMessages)){const t=e.sharedMessages;g=Object.keys(t).reduce(((e,n)=>{const r=e[n]||(e[n]={});return _(r,t[n]),e}),g||{})}const{__i18n:d,__root:h}=e;return{locale:t,fallbackLocale:n,messages:g,flatJson:e.flatJson,datetimeFormats:e.datetimeFormats,numberFormats:e.numberFormats,missing:r,missingWarn:a,fallbackWarn:o,fallbackRoot:s,fallbackFormat:l,modifiers:c,pluralRules:i,postTranslation:u,warnHtmlMessage:f,escapeParameter:m,inheritLocale:p,__i18n:d,__root:h}}(e)),n={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return x(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=x(e)?!e:e},get silentFallbackWarn(){return x(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=x(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[n,r,a]=e,o={};let s=null,l=null;if(!T(n))throw Error(15);const c=n;return T(r)?o.locale=r:L(r)?s=r:M(r)&&(l=r),L(a)?s=a:M(a)&&(l=a),t.t(c,s||l||{},o)},rt:(...e)=>t.rt(...e),tc(...e){const[n,r,a]=e,o={plural:1};let s=null,l=null;if(!T(n))throw Error(15);const c=n;return T(r)?o.locale=r:d(r)?o.plural=r:L(r)?s=r:M(r)&&(l=r),T(a)?o.locale=a:L(a)?s=a:M(a)&&(l=a),t.t(c,s||l||{},o)},te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>t.d(...e),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>t.n(...e),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)},getChoiceIndex:(e,t)=>-1,__onComponentInstanceCreated(t){const{componentInstanceCreatedListener:r}=e;r&&r(t,n)}};return n}const De={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}},Ae={name:"i18n-t",props:_({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>d(e)||!isNaN(e)}},De),setup(e,t){const{slots:n,attrs:r}=t,a=e.i18n||Ke({useScope:e.scope}),o=Object.keys(n).filter((e=>"_"!==e));return()=>{const n={};e.locale&&(n.locale=e.locale),void 0!==e.plural&&(n.plural=T(e.plural)?+e.plural:e.plural);const c=function({slots:e},t){return 1===t.length&&"default"===t[0]?e.default?e.default():[]:t.reduce(((t,n)=>{const r=e[n];return r&&(t[n]=r()),t}),{})}(t,o),i=a[$e](e.keypath,c,n),u=_({},r);return T(e.tag)||P(e.tag)?s(e.tag,u,i):s(l,u,i)}}};function Ue(e,t,n,r){const{slots:a,attrs:o}=t;return()=>{const t={part:!0};let c={};e.locale&&(t.locale=e.locale),T(e.format)?t.key=e.format:P(e.format)&&(T(e.format.key)&&(t.key=e.format.key),c=Object.keys(e.format).reduce(((t,r)=>n.includes(r)?_({},t,{[r]:e.format[r]}):t),{}));const i=r(e.value,t,c);let u=[t.key];L(i)?u=i.map(((e,t)=>{const n=a[e.type];return n?n({[e.type]:e.value,index:t,parts:i}):[e.value]})):T(i)&&(u=[i]);const f=_({},o);return T(e.tag)||P(e.tag)?s(e.tag,f,u):s(l,f,u)}}const ze=["localeMatcher","style","unit","unitDisplay","currency","currencyDisplay","useGrouping","numberingSystem","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","notation","formatMatcher"],Je={name:"i18n-n",props:_({value:{type:Number,required:!0},format:{type:[String,Object]}},De),setup(e,t){const n=e.i18n||Ke({useScope:"parent"});return Ue(e,t,ze,((...e)=>n[Oe](...e)))}},Ve=["dateStyle","timeStyle","fractionalSecondDigits","calendar","dayPeriod","numberingSystem","localeMatcher","timeZone","hour12","hourCycle","formatMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName"],qe={name:"i18n-d",props:_({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},De),setup(e,t){const n=e.i18n||Ke({useScope:"parent"});return Ue(e,t,Ve,((...e)=>n[Me](...e)))}};function Be(e){const t=(t,{instance:n,value:r})=>{if(!n||!n.$)throw Error(22);const a=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),o=function(e){if(T(e))return{path:e};if(M(e)){if(!("path"in e))throw Error(19,"path");return e}throw Error(20)}(r);t.textContent=a.t(...function(e){const{path:t,locale:n,args:r,choice:a,plural:o}=e,s={},l=r||{};T(n)&&(s.locale=n);d(a)&&(s.plural=a);d(o)&&(s.plural=o);return[t,l,s]}(o))};return{beforeMount:t,beforeUpdate:t}}function Ge(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[We](t.pluralizationRules||e.pluralizationRules);const n=Ie(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}function Ye(e={}){const n=!x(e.legacy)||e.legacy,r=!!e.globalInjection,a=new Map,o=n?Re(e):je(e),s=p(""),l={get mode(){return n?"legacy":"composition"},async install(e,...a){e.__VUE_I18N_SYMBOL__=s,e.provide(e.__VUE_I18N_SYMBOL__,l),!n&&r&&function(e,t){const n=Object.create(null);Ze.forEach((e=>{const r=Object.getOwnPropertyDescriptor(t,e);if(!r)throw Error(22);const a=f(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(n,e,a)})),e.config.globalProperties.$i18n=n,Qe.forEach((n=>{const r=Object.getOwnPropertyDescriptor(t,n);if(!r||!r.value)throw Error(22);Object.defineProperty(e.config.globalProperties,`$${n}`,r)}))}(e,l.global),function(e,t,...n){const r=M(n[0])?n[0]:{},a=!!r.useI18nComponentName;(!x(r.globalInstall)||r.globalInstall)&&(e.component(a?"i18n":Ae.name,Ae),e.component(Je.name,Je),e.component(qe.name,qe)),e.directive("t",Be(t))}(e,l,...a),n&&e.mixin(function(e,n,r){return{beforeCreate(){const a=t();if(!a)throw Error(22);const o=this.$options;if(o.i18n){const t=o.i18n;o.__i18n&&(t.__i18n=o.__i18n),t.__root=n,this.$i18n=this===this.$root?Ge(e,t):Re(t)}else this.$i18n=o.__i18n?this===this.$root?Ge(e,o):Re({__i18n:o.__i18n,__root:n}):e;e.__onComponentInstanceCreated(this.$i18n),r.__setInstance(a,this.$i18n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e)},mounted(){},beforeUnmount(){const e=t();if(!e)throw Error(22);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,r.__deleteInstance(e),delete this.$i18n}}}(o,o.__composer,l))},get global(){return o},__instances:a,__getInstance:e=>a.get(e)||null,__setInstance(e,t){a.set(e,t)},__deleteInstance(e){a.delete(e)}};return l}function Ke(e={}){const n=t();if(null==n)throw Error(16);if(!n.appContext.app.__VUE_I18N_SYMBOL__)throw Error(17);const r=c(n.appContext.app.__VUE_I18N_SYMBOL__);if(!r)throw Error(22);const a="composition"===r.mode?r.global:r.global.__composer,o=h(e)?"__i18n"in n.type?"local":"global":e.useScope?e.useScope:"local";if("global"===o){let t=P(e.messages)?e.messages:{};"__i18nGlobal"in n.type&&(t=Ie(a.locale.value,{messages:t,__i18n:n.type.__i18nGlobal}));const r=Object.keys(t);if(r.length&&r.forEach((e=>{a.mergeLocaleMessage(e,t[e])})),P(e.datetimeFormats)){const t=Object.keys(e.datetimeFormats);t.length&&t.forEach((t=>{a.mergeDateTimeFormat(t,e.datetimeFormats[t])}))}if(P(e.numberFormats)){const t=Object.keys(e.numberFormats);t.length&&t.forEach((t=>{a.mergeNumberFormat(t,e.numberFormats[t])}))}return a}if("parent"===o){let e=function(e,t){let n=null;const r=t.root;let a=t.parent;for(;null!=a;){const t=e;if("composition"===e.mode)n=t.__getInstance(a);else{const e=t.__getInstance(a);null!=e&&(n=e.__composer)}if(null!=n)break;if(r===a)break;a=a.parent}return n}(r,n);return null==e&&(e=a),e}if("legacy"===r.mode)throw Error(18);const s=r;let l=s.__getInstance(n);if(null==l){const t=n.type,r=_({},e);t.__i18n&&(r.__i18n=t.__i18n),a&&(r.__root=a),l=je(r),function(e,t,n){i((()=>{}),t),u((()=>{e.__deleteInstance(t)}),t)}(s,n),s.__setInstance(n,l)}return l}const Ze=["locale","fallbackLocale","availableLocales"],Qe=["t","rt","d","n","tm"];se=function(e,t={}){{const n=(t.onCacheKey||de)(e),r=be[n];if(r)return r;let a=!1;const o=t.onError||J;t.onError=e=>{a=!0,o(e)};const{code:s}=oe(e,t),l=new Function(`return ${s}`)();return a?l:be[n]=l}};export{qe as DatetimeFormat,Je as NumberFormat,Ae as Translation,Ce as VERSION,Ye as createI18n,Ke as useI18n,Be as vTDirective};
