<template>
	<view class="nav-bar">
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		<view class="nav-content">
			<view class="left-area" @click="goBack">
				<text v-if="showBack" class="back-icon">&#xe60e;</text>
			</view>
			<text class="title">{{ title }}</text>
			<view class="right-area">
				<slot name="right"></slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'NavBar',
		props: {
			title: {
				type: String,
				default: ''
			},
			showBack: {
				type: Boolean,
				default: true
			},
			bgColor: {
				type: String,
				default: '#0052CC'
			},
			textColor: {
				type: String,
				default: '#ffffff'
			}
		},
		data() {
			return {
				statusBarHeight: 20
			};
		},
		created() {
			// 获取状态栏高度
			this.getStatusBarHeight();
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				// #ifdef APP-PLUS
				this.statusBarHeight = plus.navigator.getStatusbarHeight();
				// #endif
				
				// #ifdef MP-WEIXIN
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight;
				// #endif
			},
			
			// 返回上一页
			goBack() {
				if (this.showBack) {
					uni.navigateBack({
						delta: 1,
						fail: () => {
							uni.switchTab({
								url: '/pages/index/index'
							});
						}
					});
				}
			}
		}
	}
</script>

<style>
	.nav-bar {
		width: 750rpx;
		background-color: #0052CC;
		color: #ffffff;
		position: fixed;
		top: 0;
		z-index: 999;
	}
	
	.status-bar {
		width: 750rpx;
	}
	
	.nav-content {
		width: 750rpx;
		height: 80rpx;
		position: relative;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}
	
	.left-area {
		position: absolute;
		left: 30rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
	}
	
	.back-icon {
		font-family: iconfont;
		font-size: 32rpx;
	}
	
	.title {
		font-size: 28rpx;
		font-weight: 500;
	}
	
	.right-area {
		position: absolute;
		right: 30rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
	}
</style> 