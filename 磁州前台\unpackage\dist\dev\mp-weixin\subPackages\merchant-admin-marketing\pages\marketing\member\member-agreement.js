"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 协议设置
      agreementSettings: {
        enabled: true,
        title: "磁州生活网会员服务协议",
        content: `尊敬的用户：

感谢您选择磁州生活网！本协议是您与磁州生活网之间关于成为会员、使用会员服务所订立的契约。请您仔细阅读本协议，确保理解协议中各条款的含义，特别是免责条款和服务限制条款。

一、会员服务内容
1.1 会员等级：本平台设有多个会员等级，不同等级享受不同权益。
1.2 会员权益：包括但不限于会员折扣、积分加速、免费配送、生日礼包、专属客服等。
1.3 会员积分：会员可通过消费、签到等方式获取积分，积分可用于兑换商品或服务。

二、会员规则
2.1 会员资格获取：用户可通过注册账号并满足相应条件获取会员资格。
2.2 会员等级晋升：会员等级根据累计消费金额、成长值等因素自动晋升。
2.3 会员有效期：除特殊说明外，会员资格长期有效。

三、会员权利与义务
3.1 会员有权享受平台提供的各项会员权益。
3.2 会员应遵守平台各项规则，不得利用会员身份从事违法或损害平台利益的行为。
3.3 会员应妥善保管账号信息，因会员个人原因导致的账号安全问题由会员自行承担责任。

四、协议修改
4.1 本平台有权根据业务发展需要修改本协议，修改后的协议将通过网站公告或其他方式通知会员。
4.2 会员如不同意修改后的协议，可申请终止会员服务；继续使用会员服务则视为接受修改后的协议。

五、免责声明
5.1 因不可抗力或第三方原因导致的服务中断或终止，本平台不承担责任。
5.2 本平台有权根据实际情况调整会员权益，但会提前通知会员。

六、其他条款
6.1 本协议的解释权归磁州生活网所有。
6.2 本协议自会员同意之日起生效。

如您对本协议有任何疑问，请联系客服咨询。`,
        forceRead: true,
        minReadTime: 10,
        updateNotify: true,
        reconfirmAfterUpdate: true,
        privacyEnabled: true,
        privacyUrl: "https://www.cizhou.com/privacy"
      },
      // 预览控制
      showPreview: false
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    toggleAgreement(e) {
      this.agreementSettings.enabled = e.detail.value;
    },
    formatText(type) {
      common_vendor.index.showToast({
        title: "富文本编辑功能开发中",
        icon: "none"
      });
    },
    toggleForceRead(e) {
      this.agreementSettings.forceRead = e.detail.value;
    },
    toggleUpdateNotify(e) {
      this.agreementSettings.updateNotify = e.detail.value;
    },
    toggleReconfirm(e) {
      this.agreementSettings.reconfirmAfterUpdate = e.detail.value;
    },
    togglePrivacyPolicy(e) {
      this.agreementSettings.privacyEnabled = e.detail.value;
    },
    editPrivacyPolicy() {
      common_vendor.index.showToast({
        title: "隐私政策编辑功能开发中",
        icon: "none"
      });
    },
    showFullPreview() {
      this.showPreview = true;
    },
    closePreview() {
      this.showPreview = false;
    },
    saveSettings() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "会员协议设置保存成功",
          icon: "success"
        });
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.agreementSettings.enabled,
    c: common_vendor.o((...args) => $options.toggleAgreement && $options.toggleAgreement(...args)),
    d: $data.agreementSettings.enabled
  }, $data.agreementSettings.enabled ? common_vendor.e({
    e: $data.agreementSettings.title,
    f: common_vendor.o(($event) => $data.agreementSettings.title = $event.detail.value),
    g: common_vendor.o(($event) => $options.formatText("bold")),
    h: common_vendor.o(($event) => $options.formatText("italic")),
    i: common_vendor.o(($event) => $options.formatText("underline")),
    j: common_vendor.o(($event) => $options.formatText("header")),
    k: common_vendor.o(($event) => $options.formatText("list")),
    l: $data.agreementSettings.content,
    m: common_vendor.o(($event) => $data.agreementSettings.content = $event.detail.value),
    n: common_vendor.t($data.agreementSettings.title || "会员协议"),
    o: common_vendor.t($data.agreementSettings.content || "请输入会员协议内容"),
    p: common_vendor.o((...args) => $options.showFullPreview && $options.showFullPreview(...args)),
    q: $data.agreementSettings.forceRead,
    r: common_vendor.o((...args) => $options.toggleForceRead && $options.toggleForceRead(...args)),
    s: $data.agreementSettings.forceRead
  }, $data.agreementSettings.forceRead ? {
    t: $data.agreementSettings.minReadTime,
    v: common_vendor.o(($event) => $data.agreementSettings.minReadTime = $event.detail.value)
  } : {}, {
    w: $data.agreementSettings.updateNotify,
    x: common_vendor.o((...args) => $options.toggleUpdateNotify && $options.toggleUpdateNotify(...args)),
    y: $data.agreementSettings.reconfirmAfterUpdate,
    z: common_vendor.o((...args) => $options.toggleReconfirm && $options.toggleReconfirm(...args)),
    A: $data.agreementSettings.privacyEnabled,
    B: common_vendor.o((...args) => $options.togglePrivacyPolicy && $options.togglePrivacyPolicy(...args)),
    C: $data.agreementSettings.privacyEnabled
  }, $data.agreementSettings.privacyEnabled ? {
    D: $data.agreementSettings.privacyUrl,
    E: common_vendor.o(($event) => $data.agreementSettings.privacyUrl = $event.detail.value),
    F: common_vendor.o((...args) => $options.editPrivacyPolicy && $options.editPrivacyPolicy(...args))
  } : {}) : {}, {
    G: $data.agreementSettings.enabled
  }, $data.agreementSettings.enabled ? {
    H: common_vendor.o((...args) => $options.saveSettings && $options.saveSettings(...args))
  } : {}, {
    I: $data.showPreview
  }, $data.showPreview ? {
    J: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args)),
    K: common_vendor.t($data.agreementSettings.title || "会员协议"),
    L: common_vendor.t($data.agreementSettings.content || "请输入会员协议内容")
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/member-agreement.js.map
