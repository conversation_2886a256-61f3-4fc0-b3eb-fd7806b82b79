<template>
  <view class="qrcode-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">推广二维码</text>
      <view class="navbar-right">
        <view class="help-icon" @click="showHelp">?</view>
      </view>
    </view>
    
    <!-- 二维码预览 -->
    <view class="qrcode-preview-section">
      <view class="preview-card" :style="{ backgroundColor: selectedTheme.bgColor }">
        <view class="preview-header">
          <image class="store-logo" :src="storeInfo.logo" mode="aspectFill"></image>
          <view class="store-info">
            <text class="store-name">{{storeInfo.name}}</text>
            <text class="store-slogan">{{storeInfo.slogan}}</text>
          </view>
        </view>
        
        <view class="qrcode-wrapper">
          <view class="qrcode-image-container">
            <image class="qrcode-image" :src="currentQrcode" mode="aspectFit"></image>
            <view class="qrcode-logo" v-if="showLogo">
              <image :src="storeInfo.logo" mode="aspectFill"></image>
            </view>
          </view>
        </view>
        
        <view class="preview-footer">
          <text class="scan-tip">{{customText}}</text>
          <text class="distributor-id">推广员ID: {{distributorId}}</text>
        </view>
      </view>
      
      <view class="action-buttons">
        <button class="action-button save" @click="saveQrcode">
          <view class="button-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7 10L12 15L17 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 15V3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text>保存到相册</text>
        </button>
        
        <button class="action-button share" @click="shareQrcode">
          <view class="button-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="18" cy="5" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="6" cy="12" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="18" cy="19" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </view>
          <text>分享二维码</text>
        </button>
      </view>
    </view>
    
    <!-- 自定义选项 -->
    <view class="customization-section">
      <!-- 二维码类型选择 -->
      <view class="option-group">
        <text class="option-title">二维码类型</text>
        <view class="type-options">
          <view 
            v-for="(type, index) in displayQrcodeTypes" 
            :key="index" 
            class="type-option" 
            :class="{ active: selectedType === type.value }"
            @click="selectType(type.value)"
          >
            <view class="option-icon" v-html="type.icon"></view>
            <text class="option-name">{{type.name}}</text>
          </view>
        </view>
      </view>
      
      <!-- 主题颜色选择 -->
      <view class="option-group">
        <text class="option-title">主题颜色</text>
        <view class="theme-options">
          <view 
            v-for="(theme, index) in themeColors" 
            :key="index" 
            class="theme-option" 
            :class="{ active: selectedTheme === theme }"
            :style="{ backgroundColor: theme.bgColor }"
            @click="selectTheme(theme)"
          >
            <view class="theme-check" v-if="selectedTheme === theme">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 6L9 17L4 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 其他选项 -->
      <view class="option-group">
        <view class="toggle-option">
          <text class="toggle-label">显示店铺LOGO</text>
          <switch :checked="showLogo" @change="toggleLogo" color="#6B0FBE" />
        </view>
        
        <view class="toggle-option">
          <text class="toggle-label">显示推广员ID</text>
          <switch :checked="showId" @change="toggleId" color="#6B0FBE" />
        </view>
      </view>
      
      <!-- 自定义文案 -->
      <view class="option-group">
        <text class="option-title">自定义文案</text>
        <input 
          class="custom-input" 
          type="text" 
          v-model="customText" 
          placeholder="输入自定义文案（最多20字）" 
          maxlength="20"
        />
      </view>
    </view>
    
    <!-- 二维码历史记录 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">历史记录</text>
        <text class="clear-history" @click="clearHistory">清空</text>
      </view>
      
      <scroll-view class="history-scroll" scroll-x>
        <view class="history-list">
          <view 
            v-for="(item, index) in historyQrcodes" 
            :key="index" 
            class="history-item"
            @click="loadHistoryQrcode(item)"
          >
            <image class="history-image" :src="item.image" mode="aspectFit"></image>
            <text class="history-date">{{item.date}}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import promotionService from '@/utils/promotionService';

// 推广参数
const promotionParams = reactive({
  type: 'product', // 默认类型
  id: '',
  title: '',
  image: '',
  extraParams: {} // 存储额外参数
});

// 店铺信息
const storeInfo = reactive({
  name: '磁州同城生活',
  slogan: '本地生活好物优选',
  logo: '/static/images/logo.png'
});

// 分销员ID
const distributorId = ref('D88652');

// 二维码类型
const qrcodeTypes = [
  {
    name: '店铺',
    value: 'store',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M9 22V12H15V22" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  {
    name: '商品',
    value: 'product',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 7L12 3L4 7M20 7V17L12 21M20 7L12 11M12 21L4 17V7M12 21V11M4 7L12 11" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  {
    name: '活动',
    value: 'activity',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  {
    name: '会员',
    value: 'member',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  }
];

// 根据业务类型动态添加二维码类型
const dynamicQrcodeTypes = {
  'carpool': {
    name: '拼车',
    value: 'carpool',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7 17H2V12H7M17 17H22V12H17M14 5H10L8 10H16L14 5ZM5 11V17H8V19H16V17H19V11L16 4H8L5 11Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  'secondhand': {
    name: '二手',
    value: 'secondhand',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 4H8V12H16V4ZM16 16H8V20H16V16ZM4 20H6V4H4V20ZM18 4V20H20V4H18Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  'house': {
    name: '房屋',
    value: 'house',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M9 22V12H15V22" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  'service': {
    name: '服务',
    value: 'service',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  },
  'community': {
    name: '社区',
    value: 'community',
    icon: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13M16 3.13C16.8604 3.3503 17.623 3.8507 18.1676 4.55231C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89317 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7Z" stroke="#6B0FBE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
  }
};

// 主题颜色
const themeColors = [
  { bgColor: '#FFFFFF', textColor: '#333333' },
  { bgColor: '#6B0FBE', textColor: '#FFFFFF' },
  { bgColor: '#A764CA', textColor: '#FFFFFF' },
  { bgColor: '#F5F7FA', textColor: '#333333' },
  { bgColor: '#FFE8F0', textColor: '#FF3B30' },
  { bgColor: '#E8F8FF', textColor: '#007AFF' }
];

// 历史记录
const historyQrcodes = reactive([
  { image: '/static/images/distribution/qrcode-1.png', date: '2023-04-25' },
  { image: '/static/images/distribution/qrcode-2.png', date: '2023-04-20' },
  { image: '/static/images/distribution/qrcode-3.png', date: '2023-04-15' }
]);

// 状态变量
const selectedType = ref('product');
const selectedTheme = ref(themeColors[0]);
const showLogo = ref(true);
const showId = ref(true);
const customText = ref('');

// 当前二维码
const currentQrcode = ref('/static/images/distribution/qrcode-sample.png');

// 显示的二维码类型选项
const displayQrcodeTypes = computed(() => {
  // 基础类型
  let types = [...qrcodeTypes];
  
  // 如果有特定业务类型，添加到选项中
  if (dynamicQrcodeTypes[promotionParams.type]) {
    types = [dynamicQrcodeTypes[promotionParams.type], ...types];
  }
  
  return types;
});

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  // 解析推广参数
  parsePromotionParams(options);
  
  // 生成二维码
  generateQrcode();
  
  // 生成推广文案
  generatePromotionText();
});

// 解析推广参数
const parsePromotionParams = (options) => {
  // 设置基本参数
  promotionParams.type = options.type || 'product';
  promotionParams.id = options.id || '';
  promotionParams.title = options.title ? decodeURIComponent(options.title) : '';
  promotionParams.image = options.image ? decodeURIComponent(options.image) : '';
  
  // 设置额外参数
  const extraParams = {};
  Object.keys(options).forEach(key => {
    if (!['type', 'id', 'title', 'image'].includes(key)) {
      extraParams[key] = decodeURIComponent(options[key] || '');
    }
  });
  promotionParams.extraParams = extraParams;
  
  // 设置默认选中类型
  selectedType.value = promotionParams.type;
};

// 生成二维码
const generateQrcode = () => {
  // 这里应该调用实际的二维码生成API
  // 暂时使用示例图片
  currentQrcode.value = '/static/images/distribution/qrcode-sample.png';
  
  // 实际项目中，应该根据参数生成二维码
  // const params = promotionService.generateQrcodeParams(
  //   promotionParams.type,
  //   promotionParams.id,
  //   distributorId.value
  // );
  // 调用二维码生成API
};

// 生成推广文案
const generatePromotionText = () => {
  // 根据不同类型生成不同的推广文案
  switch(promotionParams.type) {
    case 'carpool':
      customText.value = `扫码查看【${promotionParams.title}】拼车信息`;
      break;
      
    case 'secondhand':
      customText.value = `扫码查看【${promotionParams.title}】二手商品`;
      break;
      
    case 'house':
      customText.value = `扫码查看【${promotionParams.title}】房源信息`;
      break;
      
    case 'service':
      customText.value = `扫码预约【${promotionParams.title}】服务`;
      break;
      
    case 'product':
      customText.value = `扫码购买【${promotionParams.title}】`;
      break;
      
    case 'content':
      customText.value = `扫码阅读【${promotionParams.title}】`;
      break;
      
    case 'community':
      customText.value = `扫码查看【${promotionParams.title}】社区信息`;
      break;
      
    default:
      customText.value = `扫码查看【${promotionParams.title}】`;
  }
};

// 选择二维码类型
const selectType = (type) => {
  selectedType.value = type;
  generateQrcode();
};

// 选择主题颜色
const selectTheme = (theme) => {
  selectedTheme.value = theme;
};

// 切换显示店铺LOGO
const toggleLogo = (e) => {
  showLogo.value = e.detail.value;
};

// 切换显示推广员ID
const toggleId = (e) => {
  showId.value = e.detail.value;
};

// 保存二维码到相册
const saveQrcode = () => {
  uni.showLoading({
    title: '保存中...'
  });
  
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 记录到历史记录
    const now = new Date();
    const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    historyQrcodes.unshift({
      image: currentQrcode.value,
      date: dateStr
    });
  }, 1500);
};

// 分享二维码
const shareQrcode = () => {
  uni.showActionSheet({
    itemList: ['分享给朋友', '分享到朋友圈'],
    success: (res) => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      });
      
      // 记录分享事件
      const shareType = res.tapIndex === 0 ? 'friend' : 'timeline';
      logShareEvent(shareType);
    }
  });
};

// 记录分享事件
const logShareEvent = (shareType) => {
  console.log('记录分享事件', {
    type: promotionParams.type,
    id: promotionParams.id,
    shareType
  });
};

// 加载历史二维码
const loadHistoryQrcode = (item) => {
  // 实际应用中应该加载历史二维码的配置
  uni.showToast({
    title: '加载历史二维码',
    icon: 'none'
  });
};

// 清空历史记录
const clearHistory = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空历史记录吗？',
    success: (res) => {
      if (res.confirm) {
        historyQrcodes.splice(0, historyQrcodes.length);
        uni.showToast({
          title: '已清空',
          icon: 'success'
        });
      }
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示帮助
const showHelp = () => {
  uni.showModal({
    title: '推广二维码使用帮助',
    content: '选择喜欢的二维码样式，自定义颜色和文案，生成精美二维码进行分享推广。',
    showCancel: false
  });
};
</script>

<style lang="scss">
.qrcode-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}

.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 二维码预览部分 */
.qrcode-preview-section {
  margin: 16px;
}

.preview-card {
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  overflow: hidden;
  position: relative;
}

.preview-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.store-logo {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.store-info {
  flex: 1;
}

.store-name {
  font-size: 16px;
  font-weight: 600;
  color: inherit;
  margin-bottom: 4px;
}

.store-slogan {
  font-size: 12px;
  color: inherit;
  opacity: 0.7;
}

.qrcode-wrapper {
  display: flex;
  justify-content: center;
  margin: 10px 0 20px;
}

.qrcode-image-container {
  position: relative;
  width: 200px;
  height: 200px;
}

.qrcode-image {
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  border-radius: 10px;
}

.qrcode-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qrcode-logo image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.preview-footer {
  text-align: center;
}

.scan-tip {
  font-size: 14px;
  font-weight: 500;
  color: inherit;
  margin-bottom: 8px;
  display: block;
}

.distributor-id {
  font-size: 12px;
  color: inherit;
  opacity: 0.7;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  border: none;
}

.action-button.save {
  background: linear-gradient(135deg, #34C759, #32D74B);
  color: #FFFFFF;
}

.action-button.share {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
}

.button-icon {
  margin-right: 6px;
}

/* 自定义选项部分 */
.customization-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.option-group {
  margin-bottom: 20px;
}

.option-group:last-child {
  margin-bottom: 0;
}

.option-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.type-options {
  display: flex;
  justify-content: space-between;
}

.type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 0;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.type-option.active {
  background-color: rgba(107, 15, 190, 0.05);
}

.option-icon {
  margin-bottom: 8px;
}

.option-name {
  font-size: 12px;
  color: #666;
}

.type-option.active .option-name {
  color: #6B0FBE;
  font-weight: 500;
}

.theme-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.theme-option {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
}

.theme-option.active {
  border-color: #6B0FBE;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(107, 15, 190, 0.2);
}

.theme-check {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.toggle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.toggle-option:last-child {
  border-bottom: none;
}

.toggle-label {
  font-size: 14px;
  color: #333;
}

.custom-input {
  width: 100%;
  height: 44px;
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 14px;
  color: #333;
  border: 1px solid transparent;
}

.custom-input:focus {
  border-color: #6B0FBE;
  background-color: #FFFFFF;
}

/* 历史记录部分 */
.history-section {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.clear-history {
  font-size: 14px;
  color: #FF3B30;
}

.history-scroll {
  width: 100%;
}

.history-list {
  display: flex;
  padding-bottom: 8px;
}

.history-item {
  width: 80px;
  margin-right: 12px;
  flex-shrink: 0;
}

.history-item:last-child {
  margin-right: 0;
}

.history-image {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  border: 1px solid #EEEEEE;
  margin-bottom: 6px;
}

.history-date {
  font-size: 10px;
  color: #999;
  text-align: center;
  display: block;
}
</style> 