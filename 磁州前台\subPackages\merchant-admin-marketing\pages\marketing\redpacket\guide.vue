<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">红包营销指南</text>
      <view class="navbar-right">
        <view class="share-icon" @click="shareGuide">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
          </svg>
        </view>
      </view>
    </view>
    
    <!-- 指南内容 -->
    <scroll-view scroll-y class="guide-content">
      <!-- 指南头部 -->
      <view class="guide-header">
        <image class="guide-banner" src="/static/images/redpacket-guide-banner.png" mode="aspectFill"></image>
        <view class="guide-intro">
          <text class="guide-title">红包营销全攻略</text>
          <text class="guide-subtitle">提升用户活跃度与转化率的必备工具</text>
        </view>
      </view>
      
      <!-- 指南章节 -->
      <view class="guide-sections">
        <!-- 章节1 -->
        <view class="guide-section">
          <view class="section-header">
            <view class="section-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                <path d="M2 17l10 5 10-5"></path>
                <path d="M2 12l10 5 10-5"></path>
              </svg>
            </view>
            <text class="section-title">什么是红包营销？</text>
          </view>
          <view class="section-content">
            <text class="section-text">红包营销是一种基于用户心理的营销方式，通过发放红包激励用户参与活动，提高用户粘性，促进交易转化。在商家营销中，红包已成为不可或缺的工具。</text>
            <view class="section-image-container">
              <image class="section-image" src="/static/images/redpacket-intro.png" mode="aspectFill"></image>
            </view>
          </view>
        </view>
        
        <!-- 章节2 -->
        <view class="guide-section">
          <view class="section-header">
            <view class="section-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="16"></line>
                <line x1="8" y1="12" x2="16" y2="12"></line>
              </svg>
            </view>
            <text class="section-title">红包营销的优势</text>
          </view>
          <view class="section-content">
            <view class="advantage-item">
              <view class="advantage-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </view>
              <text class="advantage-text">提高用户活跃度与参与感</text>
            </view>
            <view class="advantage-item">
              <view class="advantage-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </view>
              <text class="advantage-text">促进用户分享，实现裂变增长</text>
            </view>
            <view class="advantage-item">
              <view class="advantage-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </view>
              <text class="advantage-text">提升品牌曝光度与美誉度</text>
            </view>
            <view class="advantage-item">
              <view class="advantage-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </view>
              <text class="advantage-text">刺激消费，提高转化率</text>
            </view>
          </view>
        </view>
        
        <!-- 章节3 -->
        <view class="guide-section">
          <view class="section-header">
            <view class="section-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            </view>
            <text class="section-title">红包营销的场景应用</text>
          </view>
          <view class="section-content">
            <view class="scenario-item">
              <text class="scenario-title">新客获取</text>
              <text class="scenario-desc">通过发放红包吸引新用户注册，降低获客成本</text>
            </view>
            <view class="scenario-item">
              <text class="scenario-title">节日营销</text>
              <text class="scenario-desc">在重要节日发放红包，提升用户好感度</text>
            </view>
            <view class="scenario-item">
              <text class="scenario-title">社交裂变</text>
              <text class="scenario-desc">鼓励用户分享红包给好友，实现低成本获客</text>
            </view>
            <view class="scenario-item">
              <text class="scenario-title">会员福利</text>
              <text class="scenario-desc">为会员提供专属红包，提升会员忠诚度</text>
            </view>
          </view>
        </view>
        
        <!-- 章节4 -->
        <view class="guide-section">
          <view class="section-header">
            <view class="section-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
            </view>
            <text class="section-title">红包营销最佳实践</text>
          </view>
          <view class="section-content">
            <view class="practice-item">
              <text class="practice-number">01</text>
              <view class="practice-detail">
                <text class="practice-title">设定明确的营销目标</text>
                <text class="practice-desc">在开始红包活动前，明确活动目标是获客、促活还是转化</text>
              </view>
            </view>
            <view class="practice-item">
              <text class="practice-number">02</text>
              <view class="practice-detail">
                <text class="practice-title">精准的用户定向</text>
                <text class="practice-desc">根据用户画像和行为数据，向最有价值的用户群体投放红包</text>
              </view>
            </view>
            <view class="practice-item">
              <text class="practice-number">03</text>
              <view class="practice-detail">
                <text class="practice-title">设计合理的红包金额</text>
                <text class="practice-desc">根据商品价格和预期转化率，设计具有吸引力的红包金额</text>
              </view>
            </view>
            <view class="practice-item">
              <text class="practice-number">04</text>
              <view class="practice-detail">
                <text class="practice-title">数据追踪与优化</text>
                <text class="practice-desc">实时监控红包活动数据，根据效果及时调整策略</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 章节5 -->
        <view class="guide-section">
          <view class="section-header">
            <view class="section-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </view>
            <text class="section-title">常见问题解答</text>
          </view>
          <view class="section-content">
            <view class="faq-item" @click="toggleFaq(0)">
              <view class="faq-question">
                <text class="question-text">如何设置红包金额才最有效？</text>
                <view class="arrow-icon" :class="{ 'arrow-down': openFaq === 0 }"></view>
              </view>
              <view class="faq-answer" v-if="openFaq === 0">
                <text class="answer-text">红包金额应根据商品价格、目标转化率和ROI来设定。一般建议设置为商品价格的5%-20%，既能吸引用户又能保证营销效益。</text>
              </view>
            </view>
            <view class="faq-item" @click="toggleFaq(1)">
              <view class="faq-question">
                <text class="question-text">红包活动多久举办一次比较合适？</text>
                <view class="arrow-icon" :class="{ 'arrow-down': openFaq === 1 }"></view>
              </view>
              <view class="faq-answer" v-if="openFaq === 1">
                <text class="answer-text">红包活动频率应根据业务周期和用户行为来决定。一般建议每月1-2次大型红包活动，结合节假日和促销季，避免过于频繁导致用户疲劳。</text>
              </view>
            </view>
            <view class="faq-item" @click="toggleFaq(2)">
              <view class="faq-question">
                <text class="question-text">如何防止红包被恶意领取？</text>
                <view class="arrow-icon" :class="{ 'arrow-down': openFaq === 2 }"></view>
              </view>
              <view class="faq-answer" v-if="openFaq === 2">
                <text class="answer-text">可以设置领取条件和限制，如实名认证、手机号验证、每人限领一次等。同时设置红包使用门槛和有效期，减少恶意领取的可能性。</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 联系我们 -->
      <view class="contact-section">
        <text class="contact-title">需要更多帮助？</text>
        <text class="contact-desc">如果您对红包营销有任何疑问，请联系我们的客服团队</text>
        <button class="contact-btn" @click="contactService">联系客服</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      openFaq: -1,
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    shareGuide() {
      uni.showActionSheet({
        itemList: ['分享给好友', '分享到朋友圈', '复制链接'],
        success: function(res) {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        }
      });
    },
    toggleFaq(index) {
      this.openFaq = this.openFaq === index ? -1 : index;
    },
    contactService() {
      uni.makePhoneCall({
        phoneNumber: '************'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
}

.navbar-back {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #333;
  border-left: 2px solid #333;
  transform: rotate(-45deg);
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-icon {
  color: #333;
}

/* 指南内容样式 */
.guide-content {
  flex: 1;
  padding-bottom: 20px;
}

.guide-header {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.guide-banner {
  width: 100%;
  height: 100%;
}

.guide-intro {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
}

.guide-title {
  font-size: 22px;
  font-weight: 700;
  color: #fff;
  display: block;
  margin-bottom: 5px;
}

.guide-subtitle {
  font-size: 14px;
  color: rgba(255,255,255,0.9);
  display: block;
}

.guide-sections {
  padding: 15px;
}

.guide-section {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.section-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: rgba(255, 77, 79, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-content {
  padding-left: 46px;
}

.section-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.section-image-container {
  margin-top: 15px;
  border-radius: 8px;
  overflow: hidden;
}

.section-image {
  width: 100%;
  height: 150px;
}

/* 优势项样式 */
.advantage-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.advantage-icon {
  margin-right: 10px;
  color: #FF4D4F;
}

.advantage-text {
  font-size: 14px;
  color: #666;
}

/* 场景项样式 */
.scenario-item {
  margin-bottom: 12px;
}

.scenario-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.scenario-desc {
  font-size: 13px;
  color: #666;
  display: block;
}

/* 最佳实践样式 */
.practice-item {
  display: flex;
  margin-bottom: 15px;
}

.practice-number {
  font-size: 18px;
  font-weight: 700;
  color: #FF4D4F;
  margin-right: 12px;
}

.practice-detail {
  flex: 1;
}

.practice-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.practice-desc {
  font-size: 13px;
  color: #666;
  display: block;
}

/* FAQ样式 */
.faq-item {
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-text {
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.arrow-icon {
  width: 12px;
  height: 12px;
  border-top: 1px solid #999;
  border-right: 1px solid #999;
  transform: rotate(45deg);
  transition: transform 0.3s;
}

.arrow-down {
  transform: rotate(135deg);
}

.faq-answer {
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.answer-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 联系我们样式 */
.contact-section {
  margin: 0 15px 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.contact-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.contact-desc {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 15px;
}

.contact-btn {
  background-color: #FF4D4F;
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
}
</style>