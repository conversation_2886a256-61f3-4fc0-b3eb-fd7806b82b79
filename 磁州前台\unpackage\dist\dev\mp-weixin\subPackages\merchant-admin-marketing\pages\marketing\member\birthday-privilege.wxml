<view class="birthday-container"><view class="navbar"><view class="navbar-back" bindtap="{{a}}"><view class="back-icon"></view></view><text class="navbar-title">生日特权</text><view class="navbar-right"></view></view><view class="privilege-content"><view class="section-card"><view class="switch-item"><view class="switch-content"><text class="switch-title">生日特权</text><text class="switch-desc">开启后，会员在生日当月可享受专属特权</text></view><switch checked="{{b}}" bindchange="{{c}}" color="#4A00E0"/></view></view><block wx:if="{{d}}"><view class="section-card"><view class="section-title">特权设置</view><view class="form-item"><text class="form-label">特权有效期</text><view class="radio-group"><view class="{{['radio-item', e && 'active']}}" bindtap="{{f}}"><text class="radio-text">生日当天</text></view><view class="{{['radio-item', g && 'active']}}" bindtap="{{h}}"><text class="radio-text">生日当周</text></view><view class="{{['radio-item', i && 'active']}}" bindtap="{{j}}"><text class="radio-text">生日当月</text></view></view></view><view class="form-item"><text class="form-label">提前通知</text><view class="form-input-group"><input type="number" class="form-input" value="{{k}}" bindinput="{{l}}"/><text class="input-suffix">天</text></view></view><view class="form-item switch-item"><text class="form-label">短信通知</text><switch checked="{{m}}" bindchange="{{n}}" color="#4A00E0"/></view><view class="form-item switch-item"><text class="form-label">微信通知</text><switch checked="{{o}}" bindchange="{{p}}" color="#4A00E0"/></view></view><view class="section-card"><view class="section-title">生日礼包</view><view class="privilege-list"><view wx:for="{{q}}" wx:for-item="privilege" wx:key="g" class="privilege-item"><view class="{{['privilege-checkbox', privilege.b && 'checked']}}" bindtap="{{privilege.c}}"><view wx:if="{{privilege.a}}" class="checkbox-inner"></view></view><view class="privilege-content"><text class="privilege-name">{{privilege.d}}</text><text class="privilege-desc">{{privilege.e}}</text></view><view class="privilege-config" bindtap="{{privilege.f}}"><text class="config-text">设置</text></view></view></view><button class="add-btn" bindtap="{{r}}">添加生日特权</button></view><view class="section-card"><view class="section-title">适用会员等级</view><view class="level-list"><view wx:for="{{s}}" wx:for-item="level" wx:key="f" class="level-item"><view class="{{['level-checkbox', level.b && 'checked']}}" bindtap="{{level.c}}"><view wx:if="{{level.a}}" class="checkbox-inner"></view></view><view class="level-content"><text class="level-name">{{level.d}}</text><text class="level-desc">{{level.e}}名会员</text></view></view></view></view><view class="section-card"><view class="section-title">生日祝福语</view><view class="form-item"><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入生日祝福语" value="{{t}}" bindinput="{{v}}"/></block></view><view class="greeting-preview"><view class="preview-title">预览效果</view><view class="preview-card"><view class="preview-header"><image class="preview-logo" src="{{w}}" mode="aspectFit"></image><text class="preview-shop-name">磁州生活网</text></view><view class="preview-content"><text class="preview-greeting">{{x}}</text></view><view class="preview-footer"><text class="preview-btn">查看生日礼包</text></view></view></view></view></block></view><view wx:if="{{y}}" class="bottom-bar"><button class="save-btn" bindtap="{{z}}">保存设置</button></view></view>