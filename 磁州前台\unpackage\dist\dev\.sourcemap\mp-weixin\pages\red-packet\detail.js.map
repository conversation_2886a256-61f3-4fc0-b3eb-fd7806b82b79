{"version": 3, "file": "detail.js", "sources": ["pages/red-packet/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcmVkLXBhY2tldC9kZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"red-packet-detail\">\r\n    <view class=\"header\">\r\n      <view class=\"title\">{{ redPacket.title }}</view>\r\n      <view class=\"amount\">¥{{ redPacket.amount }}</view>\r\n      <view class=\"status\">{{ getStatusText(redPacket.status) }}</view>\r\n    </view>\r\n    \r\n    <view class=\"info-section\">\r\n      <view class=\"info-item\">\r\n        <text class=\"label\">红包类型</text>\r\n        <text class=\"value\">{{ getTypeText(redPacket.type) }}</text>\r\n      </view>\r\n      <view class=\"info-item\">\r\n        <text class=\"label\">发送时间</text>\r\n        <text class=\"value\">{{ formatTime(redPacket.createTime) }}</text>\r\n      </view>\r\n      <view class=\"info-item\">\r\n        <text class=\"label\">过期时间</text>\r\n        <text class=\"value\">{{ formatTime(redPacket.expireTime) }}</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"records-section\" v-if=\"redPacket.records && redPacket.records.length > 0\">\r\n      <view class=\"section-title\">领取记录</view>\r\n      <view class=\"record-list\">\r\n        <view class=\"record-item\" v-for=\"record in redPacket.records\" :key=\"record.id\">\r\n          <image class=\"avatar\" :src=\"record.user.avatar\" mode=\"aspectFill\" />\r\n          <view class=\"user-info\">\r\n            <text class=\"nickname\">{{ record.user.nickname }}</text>\r\n            <text class=\"time\">{{ formatTime(record.createTime) }}</text>\r\n          </view>\r\n          <text class=\"amount\">¥{{ record.amount }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"action-bar\">\r\n      <button class=\"share-btn\" @click=\"handleShare\">\r\n        <text class=\"iconfont icon-share\"></text>\r\n        分享红包\r\n      </button>\r\n      <button class=\"grab-btn\" v-if=\"!isExpired && !hasGrabbed\" @click=\"handleGrab\">抢红包</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { formatTime } from '@/utils/format';\r\nimport { showShareMenu } from '@/utils/share';\r\nimport { RED_PACKET_TYPE } from '@/utils/redPacket';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      redPacket: null,\r\n      isExpired: false,\r\n      hasGrabbed: false\r\n    };\r\n  },\r\n  \r\n  onLoad(options) {\r\n    this.loadRedPacketDetail(options.id);\r\n  },\r\n  \r\n  methods: {\r\n    async loadRedPacketDetail(id) {\r\n      try {\r\n        const res = await uni.request({\r\n          url: `/api/red-packets/${id}`,\r\n          method: 'GET'\r\n        });\r\n        \r\n        if (res.data.code === 0) {\r\n          this.redPacket = res.data.data;\r\n          this.isExpired = new Date() > new Date(this.redPacket.expireTime);\r\n          this.hasGrabbed = this.redPacket.records.some(record => record.userId === this.userId);\r\n        }\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '加载失败',\r\n          icon: 'error'\r\n        });\r\n      }\r\n    },\r\n    \r\n    getTypeText(type) {\r\n      const typeMap = {\r\n        [RED_PACKET_TYPE.NORMAL]: '普通红包',\r\n        [RED_PACKET_TYPE.LUCKY]: '拼手气红包',\r\n        [RED_PACKET_TYPE.FIXED]: '固定金额红包'\r\n      };\r\n      return typeMap[type] || '红包';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '进行中',\r\n        1: '已领完',\r\n        2: '已过期'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n    \r\n    async handleShare() {\r\n      try {\r\n        await showShareMenu({\r\n          type: 'redPacket',\r\n          data: this.redPacket\r\n        });\r\n        uni.showToast({\r\n          title: '分享成功',\r\n          icon: 'success'\r\n        });\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '分享失败',\r\n          icon: 'error'\r\n        });\r\n      }\r\n    },\r\n    \r\n    async handleGrab() {\r\n      if (this.isExpired || this.hasGrabbed) return;\r\n      \r\n      try {\r\n        const res = await uni.request({\r\n          url: `/api/red-packets/${this.redPacket.id}/grab`,\r\n          method: 'POST'\r\n        });\r\n        \r\n        if (res.data.code === 0) {\r\n          uni.showToast({\r\n            title: '抢到红包啦！',\r\n            icon: 'success'\r\n          });\r\n          this.hasGrabbed = true;\r\n          this.loadRedPacketDetail(this.redPacket.id);\r\n        } else {\r\n          uni.showToast({\r\n            title: res.data.message || '抢红包失败',\r\n            icon: 'error'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '抢红包失败',\r\n          icon: 'error'\r\n        });\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.red-packet-detail {\r\n  padding: 30rpx;\r\n  background: #fff;\r\n  min-height: 100vh;\r\n}\r\n\r\n.header {\r\n  text-align: center;\r\n  padding: 40rpx 0;\r\n  background: linear-gradient(45deg, #ff4d4f, #ff7875);\r\n  border-radius: 20rpx;\r\n  color: #fff;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.amount {\r\n  font-size: 60rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.status {\r\n  font-size: 28rpx;\r\n  opacity: 0.8;\r\n}\r\n\r\n.info-section {\r\n  background: #f8f8f8;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.info-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.label {\r\n  color: #666;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.value {\r\n  color: #333;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.records-section {\r\n  background: #f8f8f8;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.record-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.record-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.avatar {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.user-info {\r\n  flex: 1;\r\n}\r\n\r\n.nickname {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 6rpx;\r\n}\r\n\r\n.time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.amount {\r\n  font-size: 32rpx;\r\n  color: #ff4d4f;\r\n  font-weight: bold;\r\n}\r\n\r\n.action-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  display: flex;\r\n  padding: 20rpx 30rpx;\r\n  background: #fff;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.share-btn, .grab-btn {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  text-align: center;\r\n  border-radius: 40rpx;\r\n  font-size: 32rpx;\r\n  margin: 0 10rpx;\r\n}\r\n\r\n.share-btn {\r\n  background: #f5f5f5;\r\n  color: #666;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.share-btn .iconfont {\r\n  margin-right: 6rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.grab-btn {\r\n  background: linear-gradient(45deg, #ff4d4f, #ff7875);\r\n  color: #fff;\r\n}\r\n\r\n.grab-btn[disabled] {\r\n  background: #ccc;\r\n  color: #fff;\r\n}\r\n</style> ", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/pages/red-packet/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "RED_PACKET_TYPE", "showShareMenu"], "mappings": ";;;;AAoDA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA;EAEf;AAAA,EAED,OAAO,SAAS;AACd,SAAK,oBAAoB,QAAQ,EAAE;AAAA,EACpC;AAAA,EAED,SAAS;AAAA,IACP,MAAM,oBAAoB,IAAI;AAC5B,UAAI;AACF,cAAM,MAAM,MAAMA,cAAG,MAAC,QAAQ;AAAA,UAC5B,KAAK,oBAAoB,EAAE;AAAA,UAC3B,QAAQ;AAAA,QACV,CAAC;AAED,YAAI,IAAI,KAAK,SAAS,GAAG;AACvB,eAAK,YAAY,IAAI,KAAK;AAC1B,eAAK,YAAY,oBAAI,KAAO,IAAE,IAAI,KAAK,KAAK,UAAU,UAAU;AAChE,eAAK,aAAa,KAAK,UAAU,QAAQ,KAAK,YAAU,OAAO,WAAW,KAAK,MAAM;AAAA,QACvF;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IAED,YAAY,MAAM;AAChB,YAAM,UAAU;AAAA,QACd,CAACC,gBAAAA,gBAAgB,MAAM,GAAG;AAAA,QAC1B,CAACA,gBAAAA,gBAAgB,KAAK,GAAG;AAAA,QACzB,CAACA,gBAAAA,gBAAgB,KAAK,GAAG;AAAA;AAE3B,aAAO,QAAQ,IAAI,KAAK;AAAA,IACzB;AAAA,IAED,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA;AAEL,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA,IAED,MAAM,cAAc;AAClB,UAAI;AACF,cAAMC,0BAAc;AAAA,UAClB,MAAM;AAAA,UACN,MAAM,KAAK;AAAA,QACb,CAAC;AACDF,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IAED,MAAM,aAAa;AACjB,UAAI,KAAK,aAAa,KAAK;AAAY;AAEvC,UAAI;AACF,cAAM,MAAM,MAAMA,cAAG,MAAC,QAAQ;AAAA,UAC5B,KAAK,oBAAoB,KAAK,UAAU,EAAE;AAAA,UAC1C,QAAQ;AAAA,QACV,CAAC;AAED,YAAI,IAAI,KAAK,SAAS,GAAG;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,eAAK,aAAa;AAClB,eAAK,oBAAoB,KAAK,UAAU,EAAE;AAAA,eACrC;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,KAAK,WAAW;AAAA,YAC3B,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvJA,GAAG,WAAW,eAAe;"}