{"version": 3, "file": "index.js", "sources": ["subPackages/activity-showcase/components/activity/FlashSaleCard/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9hY3Rpdml0eS9GbGFzaFNhbGVDYXJkL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <!-- 秒杀活动卡片 - 苹果风格设计 -->\r\n  <view class=\"flash-sale-card\">\r\n    <!-- 使用基础活动卡片 -->\r\n    <ActivityCard \r\n      :item=\"item\" \r\n      @favorite=\"$emit('favorite', item.id)\"\r\n      @action=\"$emit('action', { id: item.id, type: item.type, status: item.status })\"\r\n    >\r\n      <!-- 秒杀特有信息插槽 -->\r\n      <template #special-info>\r\n        <view class=\"flash-sale-special\">\r\n          <!-- 价格区域 -->\r\n          <view class=\"price-section\">\r\n            <view class=\"current-price\">\r\n              <text class=\"price-symbol\">¥</text>\r\n              <text class=\"price-value\">{{item.salePrice}}</text>\r\n            </view>\r\n            <view class=\"original-price\">\r\n              <text class=\"price-label\">原价</text>\r\n              <text class=\"price-through\">¥{{item.originalPrice}}</text>\r\n            </view>\r\n            <view class=\"discount-tag\">\r\n              <text class=\"discount-value\">{{discountPercent}}折</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 倒计时 -->\r\n          <view class=\"countdown\" v-if=\"item.status === 'ongoing' || item.status === 'upcoming'\">\r\n            <view class=\"countdown-header\">\r\n              <text class=\"countdown-title\">{{item.status === 'ongoing' ? '距结束' : '距开始'}}</text>\r\n              <text class=\"countdown-status\">{{timeStatus}}</text>\r\n            </view>\r\n            <view class=\"countdown-timer\">\r\n              <view class=\"time-block\">{{countdownHours}}</view>\r\n              <view class=\"time-separator\">:</view>\r\n              <view class=\"time-block\">{{countdownMinutes}}</view>\r\n              <view class=\"time-separator\">:</view>\r\n              <view class=\"time-block\">{{countdownSeconds}}</view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 库存进度 -->\r\n          <view class=\"stock-progress\">\r\n            <view class=\"progress-header\">\r\n              <text class=\"progress-title\">抢购进度</text>\r\n              <text class=\"progress-status\">{{item.soldCount}}/{{item.totalStock}}件</text>\r\n            </view>\r\n            <view class=\"progress-bar\">\r\n              <view class=\"progress-inner\" :style=\"{width: progressWidth + '%'}\"></view>\r\n            </view>\r\n            <view class=\"progress-tip\" v-if=\"item.status === 'ongoing' && remainStock > 0\">\r\n              <text class=\"tip-text\">仅剩{{remainStock}}件，抓紧抢购！</text>\r\n            </view>\r\n            <view class=\"progress-tip\" v-else-if=\"item.status === 'ongoing' && remainStock <= 0\">\r\n              <text class=\"tip-text\">已售罄，下次早点来哦~</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </template>\r\n    </ActivityCard>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue';\r\nimport ActivityCard from '../ActivityCard.vue';\r\n\r\nconst props = defineProps({\r\n  item: {\r\n    type: Object,\r\n    required: true\r\n  }\r\n});\r\n\r\n// 计算折扣百分比\r\nconst discountPercent = computed(() => {\r\n  if (!props.item.salePrice || !props.item.originalPrice) return '';\r\n  return Math.floor((props.item.salePrice / props.item.originalPrice) * 10);\r\n});\r\n\r\n// 计算进度条宽度\r\nconst progressWidth = computed(() => {\r\n  if (!props.item.soldCount || !props.item.totalStock) return 0;\r\n  return (props.item.soldCount / props.item.totalStock) * 100;\r\n});\r\n\r\n// 计算剩余库存\r\nconst remainStock = computed(() => {\r\n  if (!props.item.soldCount || !props.item.totalStock) return 0;\r\n  return props.item.totalStock - props.item.soldCount;\r\n});\r\n\r\n// 倒计时相关\r\nconst countdownHours = ref('00');\r\nconst countdownMinutes = ref('00');\r\nconst countdownSeconds = ref('00');\r\nconst timeStatus = ref('火热抢购中');\r\nlet countdownTimer = null;\r\n\r\n// 更新倒计时\r\nconst updateCountdown = () => {\r\n  if (!props.item.endTime) return;\r\n  \r\n  const now = new Date();\r\n  const targetTime = props.item.status === 'ongoing' ? \r\n    new Date(props.item.endTime) : new Date(props.item.startTime);\r\n  \r\n  const timeDiff = targetTime - now;\r\n  \r\n  if (timeDiff <= 0) {\r\n    // 倒计时结束\r\n    countdownHours.value = '00';\r\n    countdownMinutes.value = '00';\r\n    countdownSeconds.value = '00';\r\n    \r\n    if (props.item.status === 'ongoing') {\r\n      timeStatus.value = '已结束';\r\n    } else {\r\n      timeStatus.value = '已开始';\r\n    }\r\n    \r\n    if (countdownTimer) {\r\n      clearInterval(countdownTimer);\r\n    }\r\n    return;\r\n  }\r\n  \r\n  // 计算时分秒\r\n  const hours = Math.floor(timeDiff / (1000 * 60 * 60));\r\n  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));\r\n  const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);\r\n  \r\n  // 格式化显示\r\n  countdownHours.value = hours.toString().padStart(2, '0');\r\n  countdownMinutes.value = minutes.toString().padStart(2, '0');\r\n  countdownSeconds.value = seconds.toString().padStart(2, '0');\r\n  \r\n  // 更新状态文本\r\n  if (props.item.status === 'ongoing') {\r\n    if (hours > 0) {\r\n      timeStatus.value = '火热抢购中';\r\n    } else if (minutes > 30) {\r\n      timeStatus.value = '火热抢购中';\r\n    } else if (minutes > 10) {\r\n      timeStatus.value = '即将结束';\r\n    } else {\r\n      timeStatus.value = '最后机会';\r\n    }\r\n  } else {\r\n    if (hours > 24) {\r\n      timeStatus.value = '即将开始';\r\n    } else if (hours > 0) {\r\n      timeStatus.value = '即将开始';\r\n    } else if (minutes > 30) {\r\n      timeStatus.value = '即将开始';\r\n    } else {\r\n      timeStatus.value = '马上开始';\r\n    }\r\n  }\r\n};\r\n\r\n// 初始化倒计时\r\nonMounted(() => {\r\n  updateCountdown();\r\n  countdownTimer = setInterval(updateCountdown, 1000);\r\n});\r\n\r\n// 组件卸载时清除定时器\r\nonUnmounted(() => {\r\n  if (countdownTimer) {\r\n    clearInterval(countdownTimer);\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n/* 秒杀活动卡片特有样式 */\r\n.flash-sale-card {\r\n  /* 继承基础卡片样式 */\r\n}\r\n\r\n/* 秒杀特有信息区域 */\r\n.flash-sale-special {\r\n  padding: 20rpx;\r\n  background-color: rgba(255, 59, 48, 0.05);\r\n  border-radius: 20rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n/* 价格区域 */\r\n.price-section {\r\n  display: flex;\r\n  align-items: baseline;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.current-price {\r\n  display: flex;\r\n  align-items: baseline;\r\n  color: #ff3b30;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.price-symbol {\r\n  font-size: 24rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.price-value {\r\n  font-size: 40rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.original-price {\r\n  display: flex;\r\n  align-items: baseline;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.price-label {\r\n  font-size: 22rpx;\r\n  color: #8e8e93;\r\n  margin-right: 4rpx;\r\n}\r\n\r\n.price-through {\r\n  font-size: 24rpx;\r\n  color: #8e8e93;\r\n  text-decoration: line-through;\r\n}\r\n\r\n.discount-tag {\r\n  padding: 4rpx 10rpx;\r\n  background-color: rgba(255, 59, 48, 0.1);\r\n  border-radius: 10rpx;\r\n}\r\n\r\n.discount-value {\r\n  font-size: 22rpx;\r\n  color: #ff3b30;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 倒计时 */\r\n.countdown {\r\n  margin-bottom: 20rpx;\r\n  border-top: 1rpx dashed rgba(255, 59, 48, 0.2);\r\n  padding-top: 16rpx;\r\n}\r\n\r\n.countdown-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.countdown-title {\r\n  font-size: 24rpx;\r\n  color: #000000;\r\n  font-weight: 500;\r\n}\r\n\r\n.countdown-status {\r\n  font-size: 24rpx;\r\n  color: #ff3b30;\r\n  font-weight: 500;\r\n}\r\n\r\n.countdown-timer {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.time-block {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  background-color: #1c1c1e;\r\n  color: #ffffff;\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  border-radius: 10rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.time-separator {\r\n  margin: 0 8rpx;\r\n  color: #1c1c1e;\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 库存进度 */\r\n.stock-progress {\r\n  margin-top: 16rpx;\r\n  border-top: 1rpx dashed rgba(255, 59, 48, 0.2);\r\n  padding-top: 16rpx;\r\n}\r\n\r\n.progress-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.progress-title {\r\n  font-size: 24rpx;\r\n  color: #000000;\r\n  font-weight: 500;\r\n}\r\n\r\n.progress-status {\r\n  font-size: 24rpx;\r\n  color: #ff3b30;\r\n  font-weight: 500;\r\n}\r\n\r\n.progress-bar {\r\n  height: 10rpx;\r\n  background-color: rgba(255, 59, 48, 0.1);\r\n  border-radius: 5rpx;\r\n  overflow: hidden;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.progress-inner {\r\n  height: 100%;\r\n  background-color: #ff3b30;\r\n  border-radius: 5rpx;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-tip {\r\n  margin-top: 8rpx;\r\n}\r\n\r\n.tip-text {\r\n  font-size: 22rpx;\r\n  color: #ff3b30;\r\n}\r\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/activity/FlashSaleCard/index.vue'\nwx.createComponent(Component)"], "names": ["computed", "ref", "onMounted", "onUnmounted"], "mappings": ";;;;;AAkEA,MAAM,eAAe,MAAW;;;;;;;;;;AAEhC,UAAM,QAAQ;AAQd,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;AACrC,UAAI,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,KAAK;AAAe,eAAO;AAC/D,aAAO,KAAK,MAAO,MAAM,KAAK,YAAY,MAAM,KAAK,gBAAiB,EAAE;AAAA,IAC1E,CAAC;AAGD,UAAM,gBAAgBA,cAAQ,SAAC,MAAM;AACnC,UAAI,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,KAAK;AAAY,eAAO;AAC5D,aAAQ,MAAM,KAAK,YAAY,MAAM,KAAK,aAAc;AAAA,IAC1D,CAAC;AAGD,UAAM,cAAcA,cAAQ,SAAC,MAAM;AACjC,UAAI,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,KAAK;AAAY,eAAO;AAC5D,aAAO,MAAM,KAAK,aAAa,MAAM,KAAK;AAAA,IAC5C,CAAC;AAGD,UAAM,iBAAiBC,cAAAA,IAAI,IAAI;AAC/B,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AACjC,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AACjC,UAAM,aAAaA,cAAAA,IAAI,OAAO;AAC9B,QAAI,iBAAiB;AAGrB,UAAM,kBAAkB,MAAM;AAC5B,UAAI,CAAC,MAAM,KAAK;AAAS;AAEzB,YAAM,MAAM,oBAAI;AAChB,YAAM,aAAa,MAAM,KAAK,WAAW,YACvC,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS;AAE9D,YAAM,WAAW,aAAa;AAE9B,UAAI,YAAY,GAAG;AAEjB,uBAAe,QAAQ;AACvB,yBAAiB,QAAQ;AACzB,yBAAiB,QAAQ;AAEzB,YAAI,MAAM,KAAK,WAAW,WAAW;AACnC,qBAAW,QAAQ;AAAA,QACzB,OAAW;AACL,qBAAW,QAAQ;AAAA,QACpB;AAED,YAAI,gBAAgB;AAClB,wBAAc,cAAc;AAAA,QAC7B;AACD;AAAA,MACD;AAGD,YAAM,QAAQ,KAAK,MAAM,YAAY,MAAO,KAAK,GAAG;AACpD,YAAM,UAAU,KAAK,MAAO,YAAY,MAAO,KAAK,OAAQ,MAAO,GAAG;AACtE,YAAM,UAAU,KAAK,MAAO,YAAY,MAAO,MAAO,GAAI;AAG1D,qBAAe,QAAQ,MAAM,SAAU,EAAC,SAAS,GAAG,GAAG;AACvD,uBAAiB,QAAQ,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG;AAC3D,uBAAiB,QAAQ,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG;AAG3D,UAAI,MAAM,KAAK,WAAW,WAAW;AACnC,YAAI,QAAQ,GAAG;AACb,qBAAW,QAAQ;AAAA,QACzB,WAAe,UAAU,IAAI;AACvB,qBAAW,QAAQ;AAAA,QACzB,WAAe,UAAU,IAAI;AACvB,qBAAW,QAAQ;AAAA,QACzB,OAAW;AACL,qBAAW,QAAQ;AAAA,QACpB;AAAA,MACL,OAAS;AACL,YAAI,QAAQ,IAAI;AACd,qBAAW,QAAQ;AAAA,QACzB,WAAe,QAAQ,GAAG;AACpB,qBAAW,QAAQ;AAAA,QACzB,WAAe,UAAU,IAAI;AACvB,qBAAW,QAAQ;AAAA,QACzB,OAAW;AACL,qBAAW,QAAQ;AAAA,QACpB;AAAA,MACF;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AACd;AACA,uBAAiB,YAAY,iBAAiB,GAAI;AAAA,IACpD,CAAC;AAGDC,kBAAAA,YAAY,MAAM;AAChB,UAAI,gBAAgB;AAClB,sBAAc,cAAc;AAAA,MAC7B;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5KD,GAAG,gBAAgB,SAAS;"}