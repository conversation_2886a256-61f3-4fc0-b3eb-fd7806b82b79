<template>
  <view class="coupon-create-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @tap="goBack">
        <view class="back-arrow"></view>
      </view>
      <text class="navbar-title">创建优惠券</text>
      <view class="navbar-right">
        <view class="help-btn">
          <text class="help-icon">?</text>
        </view>
      </view>
    </view>
    
    <!-- 进度指示器 -->
    <view class="progress-container">
      <view class="progress-bar">
        <view class="progress-track"></view>
        <view class="progress-fill" :style="{width: progressPercentage + '%'}"></view>
      </view>
      <view class="progress-steps">
        <view class="step" :class="{active: currentStep >= 1, completed: currentStep > 1}">
          <view class="step-dot">
            <text v-if="currentStep <= 1">1</text>
            <view class="check-icon" v-else></view>
          </view>
          <text class="step-label">基本信息</text>
        </view>
        <view class="step-line" :class="{active: currentStep > 1}"></view>
        <view class="step" :class="{active: currentStep >= 2, completed: currentStep > 2}">
          <view class="step-dot">
            <text v-if="currentStep <= 2">2</text>
            <view class="check-icon" v-else></view>
          </view>
          <text class="step-label">使用规则</text>
        </view>
        <view class="step-line" :class="{active: currentStep > 2}"></view>
        <view class="step" :class="{active: currentStep >= 3, completed: currentStep > 3}">
          <view class="step-dot">
            <text v-if="currentStep <= 3">3</text>
            <view class="check-icon" v-else></view>
          </view>
          <text class="step-label">发放设置</text>
        </view>
        <view class="step-line" :class="{active: currentStep > 3}"></view>
        <view class="step" :class="{active: currentStep >= 4}">
          <view class="step-dot">
            <text>4</text>
          </view>
          <text class="step-label">确认创建</text>
        </view>
      </view>
    </view>
    
    <!-- 表单容器 -->
    <scroll-view scroll-y class="form-scroll-view" enable-back-to-top :scroll-into-view="scrollToId">
      <!-- 步骤1: 基本信息 -->
      <view class="form-section" v-if="currentStep === 1" id="step1">
        <view class="section-header">
          <text class="section-title">基本信息</text>
          <text class="section-subtitle">设置优惠券的基本信息和样式</text>
        </view>

        <view class="coupon-preview-wrapper">
          <text class="preview-title">优惠券预览</text>
          <view class="coupon-preview" :style="{ background: formData.couponColor }">
            <view class="coupon-preview-content">
              <text class="coupon-name">{{formData.name || '优惠券名称'}}</text>
              <view class="coupon-value-container">
                <text class="coupon-value-symbol" v-if="formData.type === 'amount'">¥</text>
                <text class="coupon-value">{{formData.type === 'amount' ? formData.value : formData.value + '折'}}</text>
              </view>
              <text class="coupon-desc">{{formData.description || '优惠券描述文字'}}</text>
              <view class="coupon-dates">
                <text class="date-label">有效期：</text>
                <text class="date-value">{{formData.validPeriod ? formData.validPeriod + '天' : '2023.10.01-2023.10.31'}}</text>
              </view>
            </view>
            <view class="coupon-dash-border"></view>
            <view class="coupon-extra">
              <text class="coupon-limit">{{formData.minAmount ? '满' + formData.minAmount + '元可用' : '无门槛'}}</text>
            </view>
          </view>
        </view>

        <view class="form-block">
          <view class="form-item">
            <text class="form-label required">优惠券名称</text>
            <input class="form-input" 
              v-model="formData.name" 
              placeholder="请输入优惠券名称" 
              maxlength="20"
              placeholder-style="color: #bbb" />
            <text class="input-limit">{{formData.name.length}}/20</text>
          </view>
          
          <view class="form-item">
            <text class="form-label required">优惠券类型</text>
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.type === 'amount' }" 
                @tap="formData.type = 'amount'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.type === 'amount'"></view>
                </view>
                <text class="radio-label">满减券</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.type === 'discount' }" 
                @tap="formData.type = 'discount'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.type === 'discount'"></view>
                </view>
                <text class="radio-label">折扣券</text>
              </view>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label required">{{formData.type === 'amount' ? '优惠金额' : '折扣力度'}}</text>
            <view class="value-input-wrap">
              <input class="form-input value-input" 
                v-model="formData.value" 
                type="digit"
                :placeholder="formData.type === 'amount' ? '请输入优惠金额' : '请输入折扣(1-9.9)'" 
                placeholder-style="color: #bbb" />
              <text class="value-unit">{{formData.type === 'amount' ? '元' : '折'}}</text>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">优惠券颜色</text>
            <view class="color-picker">
              <view class="color-option" 
                v-for="(color, index) in colorOptions" 
                :key="index"
                :style="{ background: color }"
                :class="{ active: formData.couponColor === color }"
                @tap="formData.couponColor = color">
                <view class="color-check" v-if="formData.couponColor === color">
                  <view class="check-mark"></view>
                </view>
              </view>
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">优惠券描述</text>
            <textarea class="form-textarea" 
              v-model="formData.description" 
              placeholder="请输入优惠券的使用说明或描述" 
              maxlength="50"
              placeholder-style="color: #bbb" />
            <text class="input-limit">{{formData.description.length}}/50</text>
          </view>
        </view>
      </view>
      
      <!-- 步骤2: 使用规则 -->
      <view class="form-section" v-if="currentStep === 2" id="step2">
        <view class="section-header">
          <text class="section-title">使用规则</text>
          <text class="section-subtitle">设置优惠券的使用条件和有效期</text>
        </view>
        
        <view class="form-block">
          <!-- 使用门槛 -->
          <view class="form-item">
            <text class="form-label">使用门槛</text>
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.useThreshold === 'no' }" 
                @tap="formData.useThreshold = 'no'; formData.minAmount = ''">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.useThreshold === 'no'"></view>
                </view>
                <text class="radio-label">无门槛</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.useThreshold === 'yes' }" 
                @tap="formData.useThreshold = 'yes'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.useThreshold === 'yes'"></view>
                </view>
                <text class="radio-label">满额可用</text>
              </view>
            </view>
            
            <view class="amount-input-container" v-if="formData.useThreshold === 'yes'">
              <text class="input-prefix">满</text>
              <input class="form-input threshold-input" 
                v-model="formData.minAmount" 
                type="digit"
                placeholder="请输入消费满额金额" 
                placeholder-style="color: #bbb" />
              <text class="input-suffix">元可用</text>
            </view>
          </view>
          
          <!-- 适用商品 -->
          <view class="form-item">
            <text class="form-label">适用商品</text>
            <view class="radio-group goods-limit-group">
              <view class="radio-item" 
                :class="{ active: formData.goodsLimit === 'all' }" 
                @tap="formData.goodsLimit = 'all'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.goodsLimit === 'all'"></view>
                </view>
                <text class="radio-label">全部商品</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.goodsLimit === 'category' }" 
                @tap="formData.goodsLimit = 'category'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.goodsLimit === 'category'"></view>
                </view>
                <text class="radio-label">指定品类</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.goodsLimit === 'specific' }" 
                @tap="formData.goodsLimit = 'specific'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.goodsLimit === 'specific'"></view>
                </view>
                <text class="radio-label">指定商品</text>
              </view>
            </view>
            
            <!-- 指定品类 -->
            <view class="category-selection" v-if="formData.goodsLimit === 'category'">
              <view class="selected-categories">
                <view class="selected-tag" v-for="(cat, index) in selectedCategories" :key="index">
                  <text class="tag-text">{{cat.name}}</text>
                  <text class="tag-close" @tap="removeCategory(index)">×</text>
                </view>
                <view class="add-tag" @tap="showCategoryPicker">
                  <view class="add-icon"></view>
                  <text class="add-text">添加品类</text>
                </view>
              </view>
            </view>
            
            <!-- 指定商品 -->
            <view class="goods-selection" v-if="formData.goodsLimit === 'specific'">
              <view class="goods-search">
                <view class="search-icon"></view>
                <input class="search-input" placeholder="搜索商品名称" />
              </view>
              
              <view class="selected-goods">
                <view class="empty-tip" v-if="selectedGoods.length === 0">
                  <text class="empty-text">暂无已选商品，点击下方按钮添加</text>
                </view>
                <view class="goods-item" v-for="(goods, index) in selectedGoods" :key="index">
                  <image class="goods-image" :src="goods.image" mode="aspectFill"></image>
                  <view class="goods-info">
                    <text class="goods-name">{{goods.name}}</text>
                    <text class="goods-price">¥{{goods.price}}</text>
                  </view>
                  <view class="remove-goods" @tap="removeGoods(index)">
                    <text class="remove-icon">×</text>
                  </view>
                </view>
              </view>
              
              <view class="add-goods-btn" @tap="selectGoods">
                <text class="btn-text">选择商品</text>
              </view>
            </view>
          </view>
          
          <!-- 有效期设置 -->
          <view class="form-item">
            <text class="form-label required">有效期设置</text>
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.validityType === 'days' }" 
                @tap="formData.validityType = 'days'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.validityType === 'days'"></view>
                </view>
                <text class="radio-label">领取后N天内有效</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.validityType === 'fixed' }" 
                @tap="formData.validityType = 'fixed'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.validityType === 'fixed'"></view>
                </view>
                <text class="radio-label">固定有效期</text>
              </view>
            </view>
            
            <!-- 领取后N天内有效 -->
            <view class="validity-days" v-if="formData.validityType === 'days'">
              <view class="value-input-wrap">
                <input class="form-input validity-input" 
                  v-model="formData.validPeriod" 
                  type="number"
                  placeholder="请输入天数" 
                  placeholder-style="color: #bbb" />
                <text class="value-unit">天</text>
              </view>
            </view>
            
            <!-- 固定有效期 -->
            <view class="fixed-date-range" v-if="formData.validityType === 'fixed'">
              <view class="date-picker-wrap">
                <view class="date-input" @tap="showDatePicker('start')">
                  <text class="date-text" :class="{placeholder: !formData.startDate}">
                    {{formData.startDate || '开始日期'}}
                  </text>
                  <view class="calendar-icon"></view>
                </view>
                <text class="date-separator">至</text>
                <view class="date-input" @tap="showDatePicker('end')">
                  <text class="date-text" :class="{placeholder: !formData.endDate}">
                    {{formData.endDate || '结束日期'}}
                  </text>
                  <view class="calendar-icon"></view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 使用时间段限制 -->
          <view class="form-item">
            <view class="switch-container">
              <text class="switch-label">使用时间段限制</text>
              <switch checked="{{formData.useTimeLimit}}" color="#FF7600" @change="toggleTimeLimit" />
            </view>
            
            <view class="time-limit-container" v-if="formData.useTimeLimit">
              <view class="time-range">
                <view class="time-input" @tap="showTimePicker('start')">
                  <text class="time-text" :class="{placeholder: !formData.useTimeStart}">
                    {{formData.useTimeStart || '开始时间'}}
                  </text>
                  <view class="time-icon"></view>
                </view>
                <text class="time-separator">至</text>
                <view class="time-input" @tap="showTimePicker('end')">
                  <text class="time-text" :class="{placeholder: !formData.useTimeEnd}">
                    {{formData.useTimeEnd || '结束时间'}}
                  </text>
                  <view class="time-icon"></view>
                </view>
              </view>
              <text class="time-hint">例如：设置为 10:00-22:00，则优惠券仅在该时间段内可用</text>
            </view>
          </view>
          
          <!-- 使用说明 -->
          <view class="form-item">
            <text class="form-label">使用说明</text>
            <textarea class="form-textarea" 
              v-model="formData.useInstructions" 
              placeholder="请输入优惠券的使用说明或注意事项" 
              maxlength="100"
              placeholder-style="color: #bbb" />
            <text class="input-limit">{{(formData.useInstructions || '').length}}/100</text>
          </view>
        </view>
      </view>
      
      <!-- 步骤3: 发放设置 -->
      <view class="form-section" v-if="currentStep === 3" id="step3">
        <view class="section-header">
          <text class="section-title">发放设置</text>
          <text class="section-subtitle">设置优惠券的发放方式和数量</text>
        </view>
        
        <view class="form-block">
          <!-- 发行总量 -->
          <view class="form-item">
            <text class="form-label required">发行总量</text>
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.quantityType === 'unlimited' }" 
                @tap="formData.quantityType = 'unlimited'; formData.totalQuantity = ''">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.quantityType === 'unlimited'"></view>
                </view>
                <text class="radio-label">不限制</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.quantityType === 'limited' }" 
                @tap="formData.quantityType = 'limited'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.quantityType === 'limited'"></view>
                </view>
                <text class="radio-label">限制数量</text>
              </view>
            </view>
            
            <view class="value-input-wrap" v-if="formData.quantityType === 'limited'">
              <input class="form-input" 
                v-model="formData.totalQuantity" 
                type="number"
                placeholder="请输入发行总量" 
                placeholder-style="color: #bbb" />
              <text class="value-unit">张</text>
            </view>
          </view>
          
          <!-- 每人限领 -->
          <view class="form-item">
            <text class="form-label required">每人限领</text>
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.userLimitType === 'unlimited' }" 
                @tap="formData.userLimitType = 'unlimited'; formData.perUserLimit = ''">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.userLimitType === 'unlimited'"></view>
                </view>
                <text class="radio-label">不限制</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.userLimitType === 'limited' }" 
                @tap="formData.userLimitType = 'limited'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.userLimitType === 'limited'"></view>
                </view>
                <text class="radio-label">限制数量</text>
              </view>
            </view>
            
            <view class="value-input-wrap" v-if="formData.userLimitType === 'limited'">
              <input class="form-input" 
                v-model="formData.perUserLimit" 
                type="number"
                placeholder="请输入每人可领数量" 
                placeholder-style="color: #bbb" />
              <text class="value-unit">张</text>
            </view>
          </view>
          
          <!-- 发放方式 -->
          <view class="form-item">
            <text class="form-label required">发放方式</text>
            <view class="issue-methods">
              <view class="issue-method" 
                :class="{ active: formData.issueType === 'manual' }"
                @tap="formData.issueType = 'manual'">
                <view class="method-icon manual" :class="{ active: formData.issueType === 'manual' }"></view>
                <view class="method-info">
                  <text class="method-name">用户手动领取</text>
                  <text class="method-desc">由用户在领券中心主动领取</text>
                </view>
                <view class="method-check" v-if="formData.issueType === 'manual'">
                  <view class="check-mark"></view>
                </view>
              </view>
              
              <view class="issue-method" 
                :class="{ active: formData.issueType === 'auto' }"
                @tap="formData.issueType = 'auto'">
                <view class="method-icon auto" :class="{ active: formData.issueType === 'auto' }"></view>
                <view class="method-info">
                  <text class="method-name">系统自动发放</text>
                  <text class="method-desc">满足条件时系统自动发放给用户</text>
                </view>
                <view class="method-check" v-if="formData.issueType === 'auto'">
                  <view class="check-mark"></view>
                </view>
              </view>
              
              <view class="issue-method" 
                :class="{ active: formData.issueType === 'admin' }"
                @tap="formData.issueType = 'admin'">
                <view class="method-icon admin" :class="{ active: formData.issueType === 'admin' }"></view>
                <view class="method-info">
                  <text class="method-name">商家手动发放</text>
                  <text class="method-desc">商家通过后台手动发放给指定用户</text>
                </view>
                <view class="method-check" v-if="formData.issueType === 'admin'">
                  <view class="check-mark"></view>
                </view>
              </view>
              
              <view class="issue-method" 
                :class="{ active: formData.issueType === 'share' }"
                @tap="formData.issueType = 'share'">
                <view class="method-icon share" :class="{ active: formData.issueType === 'share' }"></view>
                <view class="method-info">
                  <text class="method-name">分享领取</text>
                  <text class="method-desc">用户分享给好友一起领取</text>
                </view>
                <view class="method-check" v-if="formData.issueType === 'share'">
                  <view class="check-mark"></view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 自动发放条件 -->
          <view class="form-item" v-if="formData.issueType === 'auto'">
            <text class="form-label">自动发放条件</text>
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.autoIssueCondition === 'new' }" 
                @tap="formData.autoIssueCondition = 'new'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.autoIssueCondition === 'new'"></view>
                </view>
                <text class="radio-label">新用户注册</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.autoIssueCondition === 'birthday' }" 
                @tap="formData.autoIssueCondition = 'birthday'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.autoIssueCondition === 'birthday'"></view>
                </view>
                <text class="radio-label">会员生日</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.autoIssueCondition === 'amount' }" 
                @tap="formData.autoIssueCondition = 'amount'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.autoIssueCondition === 'amount'"></view>
                </view>
                <text class="radio-label">订单满额</text>
              </view>
            </view>
            
            <!-- 订单满额发放 -->
            <view class="order-amount" v-if="formData.autoIssueCondition === 'amount'">
              <view class="amount-input-container">
                <text class="input-prefix">满</text>
                <input class="form-input threshold-input" 
                  v-model="formData.autoIssueAmount" 
                  type="digit"
                  placeholder="请输入订单满额金额" 
                  placeholder-style="color: #bbb" />
                <text class="input-suffix">元发放</text>
              </view>
            </view>
          </view>
          
          <!-- 领券中心 -->
          <view class="form-item" v-if="['manual', 'share'].includes(formData.issueType)">
            <view class="switch-container">
              <text class="switch-label">在领券中心展示</text>
              <switch checked="{{formData.showInCenter}}" color="#FF7600" @change="toggleShowInCenter" />
            </view>
          </view>
          
          <!-- 发放时间 -->
          <view class="form-item">
            <text class="form-label">发放时间</text>
            <view class="radio-group">
              <view class="radio-item" 
                :class="{ active: formData.issueTimeType === 'now' }" 
                @tap="formData.issueTimeType = 'now'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.issueTimeType === 'now'"></view>
                </view>
                <text class="radio-label">立即发放</text>
              </view>
              <view class="radio-item" 
                :class="{ active: formData.issueTimeType === 'scheduled' }" 
                @tap="formData.issueTimeType = 'scheduled'">
                <view class="radio-dot">
                  <view class="radio-dot-inner" v-if="formData.issueTimeType === 'scheduled'"></view>
                </view>
                <text class="radio-label">定时发放</text>
              </view>
            </view>
            
            <!-- 定时发放 -->
            <view class="scheduled-time" v-if="formData.issueTimeType === 'scheduled'">
              <view class="date-picker-wrap">
                <view class="date-input" @tap="showDateTimePicker('issue')">
                  <text class="date-text" :class="{placeholder: !formData.issueTime}">
                    {{formData.issueTime || '请选择发放时间'}}
                  </text>
                  <view class="calendar-icon"></view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 步骤4: 确认创建 -->
      <view class="form-section" v-if="currentStep === 4" id="step4">
        <view class="section-header">
          <text class="section-title">确认创建</text>
          <text class="section-subtitle">确认优惠券信息并创建</text>
        </view>
        
        <view class="form-block">
          <view class="preview-card">
            <text class="preview-title">优惠券预览</text>
            <view class="coupon-preview" :style="{ background: formData.couponColor }">
              <view class="coupon-preview-content">
                <text class="coupon-name">{{formData.name}}</text>
                <view class="coupon-value-container">
                  <text class="coupon-value-symbol" v-if="formData.type === 'amount'">¥</text>
                  <text class="coupon-value">{{formData.type === 'amount' ? formData.value : formData.value + '折'}}</text>
                </view>
                <text class="coupon-desc">{{formData.description || '暂无描述'}}</text>
                <view class="coupon-dates">
                  <text class="date-label">有效期：</text>
                  <text class="date-value">{{formData.validPeriod ? formData.validPeriod + '天' : '2023.10.01-2023.10.31'}}</text>
                </view>
              </view>
              <view class="coupon-dash-border"></view>
              <view class="coupon-extra">
                <text class="coupon-limit">{{formData.minAmount ? '满' + formData.minAmount + '元可用' : '无门槛'}}</text>
              </view>
            </view>
          </view>
          
          <view class="summary-block">
            <text class="summary-title">优惠券信息</text>
            <view class="summary-item">
              <text class="summary-label">优惠券名称</text>
              <text class="summary-value">{{formData.name}}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">优惠券类型</text>
              <text class="summary-value">{{formData.type === 'amount' ? '满减券' : '折扣券'}}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">{{formData.type === 'amount' ? '优惠金额' : '折扣力度'}}</text>
              <text class="summary-value">{{formData.type === 'amount' ? formData.value + '元' : formData.value + '折'}}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">使用门槛</text>
              <text class="summary-value">{{formData.useThreshold === 'yes' ? '满' + formData.minAmount + '元可用' : '无门槛'}}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">有效期</text>
              <text class="summary-value">{{getValidityText()}}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">发放总量</text>
              <text class="summary-value">{{formData.totalQuantity || '不限'}}</text>
            </view>
            <view class="summary-item">
              <text class="summary-label">每人限领</text>
              <text class="summary-value">{{formData.perUserLimit || '不限'}}</text>
            </view>
          </view>
        </view>
        
        <!-- 添加发布组件 -->
        <view class="form-block">
          <text class="block-title">活动推广</text>
          <MarketingPromotionActions 
            :activity-type="'coupon'"
            :activity-id="tempCouponId"
            :publish-mode-only="true"
            :show-actions="['publish']"
            @action-completed="handlePromotionCompleted"
          />
        </view>
        
        <view class="form-actions">
          <button class="btn-prev" @tap="prevStep">上一步</button>
          <button class="btn-create" @tap="saveCoupon">创建优惠券</button>
        </view>
      </view>
      
      <!-- 底部操作按钮 -->
      <view class="bottom-buttons">
        <view class="btn secondary" v-if="currentStep > 1" @tap="prevStep">上一步</view>
        <view class="btn primary" @tap="nextStep">{{currentStep < 4 ? '下一步' : '创建优惠券'}}</view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import DistributionSetting from './distribution/components/DistributionSetting.vue';
// 使用 require 语法导入
const distributionMixin = require('/subPackages/merchant-admin-marketing/mixins/distributionMixin').default;
import MarketingPromotionActions from '/subPackages/merchant-admin-marketing/components/MarketingPromotionActions.vue';

export default {
  name: 'CouponCreate',
  components: {
    DistributionSetting,
    MarketingPromotionActions
  },
  mixins: [distributionMixin], // 使用分销混入
  data() {
    return {
      currentStep: 1,
      scrollToId: 'step1',
      formData: {
        // 基本信息
        name: '',
        type: 'amount',  // amount: 满减券, discount: 折扣券
        value: '',       // 金额或者折扣值
        description: '',
        couponColor: 'linear-gradient(135deg, #FF9966, #FF5E62)',
        
        // 使用规则
        minAmount: '',   // 最低消费金额
        goodsLimit: 'all', // all: 全部商品, category: 品类, specific: 指定商品
        selectedCategories: [],
        selectedGoods: [],
        validityType: 'days',  // days: 领取后N天有效, fixed: 固定日期
        validPeriod: '',  // validityType为days时使用
        startDate: '',    // validityType为fixed时使用
        endDate: '',      // validityType为fixed时使用
        useTimeLimit: false, // 是否限制使用时间段
        useTimeStart: '',    // 使用时间段开始
        useTimeEnd: '',      // 使用时间段结束
        
        // 发放设置
        totalQuantity: '',  // 发行总量
        perUserLimit: '1',  // 每人限领
        issueType: 'manual', // manual: 手动领取, auto: 自动发放
        autoIssueCondition: 'new', // new: 新用户注册, amount: 订单满额
        autoIssueAmount: '', // issueCondition为amount时使用

        // 新增属性
        useThreshold: 'no',  // 使用门槛: no - 无门槛, yes - 满额可用
        useInstructions: '', // 使用说明
        quantityType: 'unlimited',  // 发行总量类型: unlimited - 不限制, limited - 限制数量
        userLimitType: 'limited',   // 每人限领类型: unlimited - 不限制, limited - 限制数量
        issueTimeType: 'now',       // 发放时间类型: now - 立即发放, scheduled - 定时发放
        issueTime: '',              // 定时发放时间
        showInCenter: true,         // 是否在领券中心展示

        // 分销设置
        distributionSettings: {
          enabled: false,
          commissionMode: 'percentage',
          commissions: {
            level1: '',
            level2: '',
            level3: ''
          },
          enableLevel3: false
        }
      },
      
      // 颜色选项
      colorOptions: [
        'linear-gradient(135deg, #FF9966, #FF5E62)',
        'linear-gradient(135deg, #FFA62E, #EA4D2C)',
        'linear-gradient(135deg, #36D1DC, #5B86E5)',
        'linear-gradient(135deg, #3A1C71, #D76D77)',
        'linear-gradient(135deg, #4776E6, #8E54E9)',
        'linear-gradient(135deg, #00B09B, #96C93D)',
      ],
      
      // 模拟数据
      selectedCategories: [
        { id: 1, name: '女装' },
        { id: 2, name: '男装' }
      ],
      selectedGoods: [
        { 
          id: 1, 
          name: '2023春季新款连衣裙', 
          price: 299.00,
          image: '/static/images/goods-1.jpg'
        }
      ],
      hasMerchantDistribution: false, // 商家是否开通分销功能
      tempCouponId: 'temp-' + Date.now(), // 临时ID，实际应该从后端获取
    }
  },
  computed: {
    progressPercentage() {
      return (this.currentStep / 4) * 100;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep -= 1;
        this.scrollToId = `step${this.currentStep}`;
      }
    },
    nextStep() {
      // 简单验证
      if (this.currentStep === 1) {
        if (!this.formData.name || !this.formData.value) {
          uni.showToast({
            title: '请填写必填项',
            icon: 'none'
          });
          return;
        }
        
        // 验证优惠券金额或折扣
        if (this.formData.type === 'amount') {
          if (isNaN(this.formData.value) || parseFloat(this.formData.value) <= 0) {
            uni.showToast({
              title: '请输入有效的优惠金额',
              icon: 'none'
            });
            return;
          }
        } else {
          const discValue = parseFloat(this.formData.value);
          if (isNaN(discValue) || discValue <= 0 || discValue >= 10) {
            uni.showToast({
              title: '请输入有效的折扣(1-9.9)',
              icon: 'none'
            });
            return;
          }
        }
      }
      
      if (this.currentStep < 4) {
        this.currentStep += 1;
        this.scrollToId = `step${this.currentStep}`;
      } else {
        // 提交表单
        this.submitForm();
      }
    },
    submitForm() {
      uni.showLoading({
        title: '创建中...'
      });
      
      // 模拟API请求
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '创建成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            setTimeout(() => {
              uni.navigateBack();
            }, 2000);
          }
        });
      }, 1500);
    },
    toggleTimeLimit(e) {
      this.formData.useTimeLimit = e.detail.value;
    },
    removeCategory(index) {
      this.selectedCategories.splice(index, 1);
    },
    removeGoods(index) {
      this.selectedGoods.splice(index, 1);
    },
    showCategoryPicker() {
      // 显示品类选择弹窗
      uni.showToast({
        title: '品类选择功能待开发',
        icon: 'none'
      });
    },
    selectGoods() {
      // 显示商品选择弹窗
      uni.showToast({
        title: '商品选择功能待开发',
        icon: 'none'
      });
    },
    showDatePicker(type) {
      // 显示日期选择器
      uni.showToast({
        title: `${type === 'start' ? '开始' : '结束'}日期选择功能待开发`,
        icon: 'none'
      });
    },
    showTimePicker(type) {
      // 显示时间选择器
      uni.showToast({
        title: `${type === 'start' ? '开始' : '结束'}时间选择功能待开发`,
        icon: 'none'
      });
    },
    toggleShowInCenter(e) {
      this.formData.showInCenter = e.detail.value;
    },
    showDateTimePicker(type) {
      // 显示日期时间选择器
      uni.showToast({
        title: '日期时间选择功能待开发',
        icon: 'none'
      });
    },
    getValidityText() {
      if (this.formData.validityType === 'days') {
        return this.formData.validPeriod ? `领取后${this.formData.validPeriod}天内有效` : '领取后N天内有效';
      } else {
        if (this.formData.startDate && this.formData.endDate) {
          return `${this.formData.startDate} 至 ${this.formData.endDate}`;
        } else {
          return '2023.10.01-2023.10.31'; // 默认显示
        }
      }
    },
    getGoodsLimitText() {
      switch (this.formData.goodsLimit) {
        case 'all':
          return '全部商品';
        case 'category':
          return `指定品类(${this.selectedCategories.length}个)`;
        case 'specific':
          return `指定商品(${this.selectedGoods.length}个)`;
        default:
          return '全部商品';
      }
    },
    getIssueTypeText() {
      switch (this.formData.issueType) {
        case 'manual':
          return '用户手动领取';
        case 'auto':
          return '系统自动发放';
        case 'admin':
          return '商家手动发放';
        case 'share':
          return '分享领取';
        default:
          return '用户手动领取';
      }
    },
    getAutoIssueConditionText() {
      switch (this.formData.autoIssueCondition) {
        case 'new':
          return '新用户注册';
        case 'birthday':
          return '会员生日';
        case 'amount':
          return `订单满${this.formData.autoIssueAmount || 0}元`;
        default:
          return '新用户注册';
      }
    },
    
    // 更新分销设置
    updateDistributionSettings(settings) {
      this.formData.distributionSettings = settings;
    },
    
    // 检查商家是否开通分销功能
    checkMerchantDistribution() {
      // 调用API检查商家是否开通分销功能
      // 这里模拟API调用
      setTimeout(() => {
        this.hasMerchantDistribution = true;
      }, 500);
    },
    
    // 保存优惠券
    async saveCoupon() {
      // 模拟API调用
      uni.showLoading({
        title: '保存中...'
      });
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            setTimeout(() => {
              uni.navigateBack();
            }, 2000);
          }
        });
      }, 1500);
      
      // 保存分销设置
      if (this.hasMerchantDistribution && this.formData.distributionSettings.enabled) {
        const success = await this.saveActivityDistributionSettings('coupon', this.tempCouponId);
        if (!success) {
          return;
        }
      }
    },
    
    // 处理推广操作完成事件
    handlePromotionCompleted(data) {
      console.log('推广操作完成:', data);
      // 根据不同操作类型处理结果
      if (data.action === 'publish') {
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        });
      } else if (data.action === 'top') {
        uni.showToast({
          title: '置顶成功',
          icon: 'success'
        });
      } else if (data.action === 'refresh') {
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }
    },
  },
  mounted() {
    this.checkMerchantDistribution();
  }
}
</script>

<style lang="scss">
.coupon-create-container {
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #FF7600, #FF3C00);
  position: relative;
  z-index: 100;
}

.navbar-left {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-arrow {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.help-btn {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-icon {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

/* 进度指示器样式 */
.progress-container {
  background: #fff;
  padding: 20px 15px 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.progress-bar {
  height: 4px;
  background: #EAEAEA;
  border-radius: 2px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: #EAEAEA;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #FF7600;
  transition: width 0.3s ease;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
}

.step-dot {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: #EAEAEA;
  color: #999;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
}

.step.active .step-dot {
  background: #FF7600;
  color: #fff;
}

.step.completed .step-dot {
  background: #FF7600;
  border-color: rgba(255, 118, 0, 0.2);
}

.check-icon {
  width: 10px;
  height: 6px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
  margin-top: -2px;
}

.step-label {
  font-size: 12px;
  color: #999;
}

.step.active .step-label {
  color: #333;
  font-weight: 500;
}

.step-line {
  flex: 1;
  height: 1px;
  background: #EAEAEA;
  margin: 0 5px;
  margin-top: -18px;
  transition: background 0.3s ease;
}

.step-line.active {
  background: #FF7600;
}

/* 表单容器样式 */
.form-scroll-view {
  height: calc(100vh - 180px); /* 减去导航栏和进度条的高度 */
}

.form-section {
  padding: 15px;
  margin-bottom: 60px; /* 为底部按钮留出空间 */
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.section-subtitle {
  font-size: 14px;
  color: #666;
}

/* 优惠券预览样式 */
.coupon-preview-wrapper {
  margin-bottom: 30px;
}

.preview-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}

.coupon-preview {
  width: 100%;
  height: 160px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  display: flex;
  flex-direction: column;
}

.coupon-preview-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.coupon-name {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.coupon-value-container {
  display: flex;
  align-items: flex-end;
  margin-top: 10px;
  height: 50px;
}

.coupon-value-symbol {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 5px;
}

.coupon-value {
  font-size: 40px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.coupon-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 10px;
}

.coupon-dates {
  display: flex;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.coupon-dash-border {
  height: 4px;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.7) 50%, transparent 50%);
  background-size: 16px 2px;
  background-repeat: repeat-x;
}

.coupon-extra {
  height: 36px;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.coupon-limit {
  font-size: 12px;
  color: #fff;
}

/* 表单项样式 */
.form-block {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.form-label.required::before {
  content: '*';
  color: #FF3B30;
  margin-right: 4px;
}

.form-input {
  width: 100%;
  height: 44px;
  background: #F8FAFC;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 0 12px;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s;
}

.form-input:focus {
  border-color: #FF7600;
  background: #FFF;
  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);
}

.input-limit {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
  display: block;
}

.form-textarea {
  width: 100%;
  height: 80px;
  background: #F8FAFC;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 12px;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s;
}

.form-textarea:focus {
  border-color: #FF7600;
  background: #FFF;
  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);
}

/* 单选按钮组样式 */
.radio-group {
  display: flex;
  margin-top: 5px;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 30px;
  cursor: pointer;
}

.radio-dot {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  border: 2px solid #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  transition: all 0.2s;
}

.radio-dot-inner {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background: #FF7600;
}

.radio-item.active .radio-dot {
  border-color: #FF7600;
}

.radio-label {
  font-size: 14px;
  color: #333;
}

/* 金额输入框样式 */
.value-input-wrap {
  position: relative;
  width: 100%;
}

.value-input {
  padding-right: 40px;
}

.value-unit {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #666;
}

/* 颜色选择器样式 */
.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}

.color-option.active {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.color-check {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-mark {
  width: 10px;
  height: 6px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(-45deg);
  margin-top: -2px;
}

/* 底部按钮样式 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  padding-bottom: env(safe-area-inset-bottom);
}

.btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  margin: 0 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s;
}

.btn:active {
  transform: scale(0.97);
  opacity: 0.9;
}

.btn.primary {
  background: linear-gradient(135deg, #FF7600, #FF4D00);
  color: #fff;
}

.btn.secondary {
  background: #F5F5F5;
  color: #666;
}

/* 使用规则样式 - 步骤2 */
.sub-section {
  margin-top: 25px;
  margin-bottom: 20px;
}

.sub-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.switch-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.switch-label {
  font-size: 14px;
  color: #333;
}

/* 发放设置样式 - 步骤3 */
.issue-methods {
  margin-top: 10px;
}

.issue-method {
  display: flex;
  align-items: center;
  background: #F8FAFC;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
  position: relative;
}

.issue-method.active {
  background: #FFF9F5;
  border: 1px solid #FF7600;
}

.method-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}

.method-icon.manual {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

.method-icon.auto {
  background: linear-gradient(135deg, #FF9966, #FF5E62);
}

.method-icon.admin {
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
}

.method-icon.share {
  background: linear-gradient(135deg, #FDEB71, #F8D800);
}

.method-icon::before {
  font-size: 20px;
  color: #fff;
  font-weight: 600;
}

.method-icon.manual::before {
  content: '券';
}

.method-icon.auto::before {
  content: '自';
}

.method-icon.admin::before {
  content: '商';
}

.method-icon.share::before {
  content: '享';
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.method-desc {
  font-size: 12px;
  color: #999;
}

.method-check {
  width: 22px;
  height: 22px;
  border-radius: 11px;
  background: #FF7600;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分销设置样式 */
.distribution-setting {
  margin-top: 20px;
  padding: 15px;
  background: #F8FAFC;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.distribution-setting .section-header {
  margin-bottom: 15px;
}

.distribution-setting .section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.distribution-setting .section-subtitle {
  font-size: 13px;
  color: #666;
}

.distribution-setting .form-block {
  padding: 0;
  box-shadow: none;
}

.distribution-setting .form-item {
  margin-bottom: 15px;
}

.distribution-setting .form-item:last-child {
  margin-bottom: 0;
}

.distribution-setting .form-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.distribution-setting .form-label.required::before {
  content: '';
  margin-right: 0;
}

.distribution-setting .form-input {
  width: 100%;
  height: 44px;
  background: #FFF;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 0 12px;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s;
}

.distribution-setting .form-input:focus {
  border-color: #FF7600;
  background: #FFF;
  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);
}

.distribution-setting .input-limit {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
  display: block;
}

.distribution-setting .form-textarea {
  width: 100%;
  height: 80px;
  background: #FFF;
  border: 1px solid #EAEAEA;
  border-radius: 8px;
  padding: 12px;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s;
}

.distribution-setting .form-textarea:focus {
  border-color: #FF7600;
  background: #FFF;
  box-shadow: 0 0 0 2px rgba(255, 118, 0, 0.1);
}

.distribution-setting .radio-group {
  margin-top: 0;
}

.distribution-setting .radio-item {
  margin-right: 20px;
}

.distribution-setting .radio-dot {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  border: 1px solid #CCCCCC;
  margin-right: 6px;
}

.distribution-setting .radio-dot-inner {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #FF7600;
}

.distribution-setting .radio-item.active .radio-dot {
  border-color: #FF7600;
}

.distribution-setting .radio-label {
  font-size: 14px;
  color: #333;
}

.distribution-setting .value-input-wrap {
  position: relative;
  width: 100%;
}

.distribution-setting .value-input {
  padding-right: 30px;
}

.distribution-setting .value-unit {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #666;
}

.distribution-setting .color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.distribution-setting .color-option {
  width: 36px;
  height: 36px;
  border-radius: 7px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.distribution-setting .color-option.active {
  transform: scale(1.05);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.distribution-setting .color-check {
  width: 16px;
  height: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

.distribution-setting .check-mark {
  width: 8px;
  height: 5px;
  border-left: 1.5px solid #fff;
  border-bottom: 1.5px solid #fff;
  transform: rotate(-45deg);
  margin-top: -1.5px;
}

/* 确认页样式 - 步骤4 */
.summary-card {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  margin-bottom: 15px;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 14px;
  color: #666;
}

.summary-value {
  font-size: 14px;
  color: #333;
  text-align: right;
}

/* 新增样式 */
.preview-card {
  margin-bottom: 20px;
}

.block-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: #FFFFFF;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}

.btn-prev, .btn-create {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  margin: 0 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s;
}

.btn-prev {
  background: #F5F5F5;
  color: #666;
}

.btn-create {
  background: linear-gradient(135deg, #FF7600, #FF4D00);
  color: #fff;
}

.btn-prev:active, .btn-create:active {
  transform: scale(0.97);
  opacity: 0.9;
}
</style> 