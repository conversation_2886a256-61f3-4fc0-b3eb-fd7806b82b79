<template>
	<view class="page-root">
		<!-- 顶部蓝色背景，包含状态栏+导航栏 -->
		<view class="nav-bg" :style="{ height: (statusBarHeight + 44) + 'px' }"></view>
		<!-- 导航栏内容绝对定位在蓝色背景上，顶部margin为statusBarHeight -->
		<view class="navbar-content" :style="{ top: statusBarHeight + 'px', height: '44px' }">
			<view class="navbar-left" @click="goBack">
				<image class="back-icon" src="/static/images/tabbar/最新返回键.png" mode="aspectFit"></image>
			</view>
			<text class="navbar-title">积分明细</text>
			<view class="navbar-right"></view>
		</view>
		<view class="points-detail-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<view 
				class="tab-item" 
				:class="{ active: currentTab === index }"
				v-for="(tab, index) in tabs" 
				:key="index"
				@click="switchTab(index)"
			>
				{{ tab }}
			</view>
		</view>
		
		<!-- 记录列表 -->
		<scroll-view 
			scroll-y 
			class="record-list" 
			refresher-enabled 
			@refresherrefresh="refreshRecords" 
			:refresher-triggered="isRefreshing"
		>
			<view v-if="filteredRecords.length > 0">
				<!-- 日期分组 -->
				<block v-for="(group, groupIndex) in groupedRecords" :key="groupIndex">
					<view class="date-header">{{ group.date }}</view>
					
					<!-- 每组下的记录列表 -->
					<view class="record-item" v-for="(record, index) in group.records" :key="index">
						<view class="record-left">
							<view class="record-title">{{ record.title }}</view>
							<view class="record-time">{{ record.time }}</view>
						</view>
						<view class="record-points" :class="{ 'income': record.type === 'income', 'expense': record.type === 'expense' }">
							{{ record.type === 'income' ? '+' : '-' }}{{ record.points }}
						</view>
					</view>
				</block>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-else>
				<image class="empty-icon" src="/static/images/tabbar/empty.png"></image>
				<view class="empty-text">暂无积分记录</view>
			</view>
		</scroll-view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Vue3迁移代码开始
// 响应式状态
const statusBarHeight = ref(20);
const currentTab = ref(0);
const isRefreshing = ref(false);

// 常量数据
const tabs = ['全部', '获取', '使用'];

// 记录数据
const records = ref([
	{
		id: 1,
		title: '每日签到',
		time: '10:25',
		date: '2023-11-25',
		points: 5,
		type: 'income'
	},
	{
		id: 2,
		title: '浏览商家完成',
		time: '15:40',
		date: '2023-11-25',
		points: 10,
		type: 'income'
	},
	{
		id: 3,
		title: '兑换优惠券',
		time: '18:33',
		date: '2023-11-24',
		points: 100,
		type: 'expense'
	},
	{
		id: 4,
		title: '分享小程序',
		time: '09:15',
		date: '2023-11-23',
		points: 20,
		type: 'income'
	},
	{
		id: 5,
		title: '评论互动',
		time: '14:22',
		date: '2023-11-22',
		points: 5,
		type: 'income'
	},
	{
		id: 6,
		title: '兑换会员月卡',
		time: '11:05',
		date: '2023-11-20',
		points: 500,
		type: 'expense'
	}
]);

// 计算属性 - 根据当前选择的标签筛选记录
const filteredRecords = computed(() => {
	if (currentTab.value === 0) {
		return records.value;
	} else if (currentTab.value === 1) {
		// 获取积分记录
		return records.value.filter(record => record.type === 'income');
	} else {
		// 使用积分记录
		return records.value.filter(record => record.type === 'expense');
	}
});

// 格式化日期标签
function formatDateLabel(dateStr) {
	const today = new Date().toISOString().split('T')[0];
	const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];
	
	if (dateStr === today) {
		return '今天';
	} else if (dateStr === yesterday) {
		return '昨天';
	} else {
		const date = new Date(dateStr);
		return `${date.getMonth() + 1}月${date.getDate()}日`;
	}
}

// 计算属性 - 按日期分组记录
const groupedRecords = computed(() => {
	const groups = {};
	
	// 按日期分组
	filteredRecords.value.forEach(record => {
		if (!groups[record.date]) {
			groups[record.date] = {
				date: formatDateLabel(record.date),
				records: []
			};
		}
		
		groups[record.date].records.push(record);
	});
	
	// 转换为数组并按日期排序
	return Object.values(groups).sort((a, b) => {
		const dateA = new Date(a.date.replace('今天', new Date().toISOString().split('T')[0]));
		const dateB = new Date(b.date.replace('今天', new Date().toISOString().split('T')[0]));
		return dateB - dateA;
	});
});

// 生命周期钩子
onMounted(() => {
	// 获取状态栏高度
	const windowInfo = uni.getWindowInfo();
	statusBarHeight.value = windowInfo.statusBarHeight || 20;
	
	// 加载积分记录
	loadPointsRecords();
});

// 方法
// 切换标签
function switchTab(index) {
	currentTab.value = index;
}

// 刷新记录
function refreshRecords() {
	isRefreshing.value = true;
	
	// 模拟刷新操作
	setTimeout(() => {
		loadPointsRecords();
		isRefreshing.value = false;
		
		uni.showToast({
			title: '刷新成功',
			icon: 'none'
		});
	}, 1500);
}

// 加载积分记录
function loadPointsRecords() {
	// 在实际应用中，应该从API获取数据
	// 这里使用模拟数据
	// setTimeout(() => {
	//   records.value = [...];
	// }, 500);
}

// 返回上一页
function goBack() {
	uni.navigateBack();
}
// Vue3迁移代码结束
</script>

<style lang="scss">
page {
	background-color: #F8F8F8;
	font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.page-root {
	position: relative;
	min-height: 100vh;
	background: #f6faff;
}

.nav-bg {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: #1677FF;
	z-index: 100;
	width: 100%;
}

.navbar-content {
	position: fixed;
	left: 0;
	right: 0;
	z-index: 101;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: transparent;
	width: 100%;
}

.navbar-left, .navbar-right {
	width: 44px;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	width: 24px;
	height: 24px;
	display: block;
	background: none;
	border-radius: 0;
	margin: 0 auto;
}

.navbar-title {
	flex: 1;
	text-align: center;
	font-size: 18px;
	font-weight: 600;
	color: #fff;
	line-height: 44px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 筛选标签 */
.filter-tabs {
	display: flex;
	background: #eaf3ff;
	border-radius: 10px;
	overflow: hidden;
	margin: 0 0 10px 0;
	padding: 2px;
	height: 34px;
}

.tab-item {
	flex: 1;
	text-align: center;
	font-size: 13px;
	color: #1677FF;
	border-radius: 8px;
	background: none;
	line-height: 30px;
	transition: all 0.2s;
	margin: 0 2px;
}

.tab-item.active {
	background: linear-gradient(90deg, #1677FF 0%, #007aff 100%);
	color: #fff;
	font-weight: 600;
	box-shadow: 0 2px 8px rgba(22,119,255,0.10);
}

.record-item {
	background: #fff;
	border-radius: 7px;
	box-shadow: 0 2px 8px rgba(22,119,255,0.04);
	border-left: 3px solid #1677FF;
	margin: 0 16px 7px 16px;
	padding: 14px 14px 14px 14px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	word-break: break-all;
}

.record-left {
	display: flex;
	flex-direction: column;
}

.record-title {
	font-size: 13px;
}

.record-time {
	font-size: 11px;
}

.record-points {
	font-size: 14px;
	font-weight: 700;
}

.record-points.income {
	color: #1677FF;
	font-weight: 700;
}

.record-points.expense {
	color: #ff4d4f;
	font-weight: 700;
}

/* 空状态 */
.empty-state {
	padding: 100rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}
</style> 
