/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.conditions-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(107, 15, 190, 0.15);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 条件卡片样式 */
.conditions-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  padding: 8px 0;
}
.condition-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}
.condition-item:last-child {
  border-bottom: none;
}
.radio-button {
  width: 20px;
  height: 20px;
  border-radius: 10px;
  border: 2px solid #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}
.radio-button.active {
  border-color: #6B0FBE;
}
.radio-inner {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #6B0FBE;
}
.condition-content {
  flex: 1;
}
.condition-title {
  font-size: 15px;
  color: #333;
  margin-bottom: 4px;
}
.condition-desc {
  font-size: 12px;
  color: #999;
}

/* 附加条件卡片样式 */
.additional-card, .approval-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}
.card-header {
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}
.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
}
.form-item:last-child {
  border-bottom: none;
}
.form-label {
  font-size: 15px;
  color: #333;
}
.form-input-group {
  display: flex;
  align-items: center;
  background-color: #F5F7FA;
  border-radius: 8px;
  padding: 0 12px;
  height: 36px;
}
.input-prefix {
  font-size: 15px;
  color: #333;
  margin-right: 4px;
}
.form-input {
  height: 36px;
  width: 80px;
  font-size: 15px;
  color: #333;
  text-align: right;
}
.form-switch {
  height: 36px;
  display: flex;
  align-items: center;
}

/* 商品列表样式 */
.product-list {
  padding: 16px;
}
.product-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background-color: #F5F7FA;
  border-radius: 12px;
  padding: 10px;
}
.product-image {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  margin-right: 12px;
}
.product-info {
  flex: 1;
}
.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}
.product-price {
  font-size: 13px;
  color: #FF3B30;
}
.product-remove {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}
.add-product {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border: 1px dashed #CCCCCC;
  border-radius: 12px;
}
.add-icon {
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: #6B0FBE;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 16px;
}
.add-text {
  font-size: 14px;
  color: #6B0FBE;
}

/* 按钮样式 */
.button-container {
  margin: 24px 16px;
}
.save-button {
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #A764CA, #6B0FBE);
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}