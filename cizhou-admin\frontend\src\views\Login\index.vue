<template>
  <div class="login-container">
    <div class="login-background">
      <div class="bg-animation"></div>
    </div>
    
    <div class="login-content">
      <div class="login-form-container">
        <div class="login-header">
          <div class="logo">
            <img src="/logo.svg" alt="磁州生活网" class="logo-image" />
            <h1 class="logo-text">磁州生活网</h1>
          </div>
          <p class="subtitle">后台管理系统</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              prefix-icon="User"
              clearable
              autocomplete="username"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
              autocomplete="current-password"
            />
          </el-form-item>

          <el-form-item v-if="showCaptcha" prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="请输入验证码"
                prefix-icon="Key"
                clearable
                class="captcha-input"
              />
              <div class="captcha-image" @click="refreshCaptcha">
                <img :src="captchaUrl" alt="验证码" />
                <div class="captcha-refresh">
                  <el-icon><Refresh /></el-icon>
                </div>
              </div>
            </div>
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe">
                记住我
              </el-checkbox>
              <el-link type="primary" @click="handleForgotPassword">
                忘记密码？
              </el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <p class="copyright">
            © 2024 磁州生活网. All rights reserved.
          </p>
          <div class="links">
            <el-link href="#" type="info">使用条款</el-link>
            <el-divider direction="vertical" />
            <el-link href="#" type="info">隐私政策</el-link>
            <el-divider direction="vertical" />
            <el-link href="#" type="info">帮助中心</el-link>
          </div>
        </div>
      </div>

      <div class="login-banner">
        <div class="banner-content">
          <h2>企业级后台管理系统</h2>
          <p>基于Vue3 + TypeScript + Element Plus构建</p>
          <ul class="feature-list">
            <li><el-icon><Check /></el-icon> 微服务架构</li>
            <li><el-icon><Check /></el-icon> 高性能设计</li>
            <li><el-icon><Check /></el-icon> 安全可靠</li>
            <li><el-icon><Check /></el-icon> 响应式布局</li>
          </ul>
        </div>
        <div class="banner-image">
          <img src="/login-banner.svg" alt="管理系统" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Key, Refresh, Check } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import type { FormInstance, FormRules } from 'element-plus'
import type { LoginParams } from '@/types/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 状态
const loading = ref(false)
const showCaptcha = ref(false)
const captchaUrl = ref('')

// 登录表单
const loginForm = reactive<LoginParams & { rememberMe: boolean }>({
  username: '',
  password: '',
  captcha: '',
  rememberMe: false
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为 4 位', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    
    loading.value = true
    
    await userStore.login({
      username: loginForm.username,
      password: loginForm.password,
      captcha: showCaptcha.value ? loginForm.captcha : undefined,
      rememberMe: loginForm.rememberMe
    })

    ElMessage.success('登录成功')
    
    // 跳转到目标页面或首页
    const redirect = route.query.redirect as string
    await router.push(redirect || '/dashboard')
    
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 如果是验证码错误，显示验证码
    if (error.message?.includes('验证码')) {
      showCaptcha.value = true
      refreshCaptcha()
    }
    
    // 清空密码
    loginForm.password = ''
    loginForm.captcha = ''
  } finally {
    loading.value = false
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  captchaUrl.value = `/api/auth/captcha?t=${Date.now()}`
}

// 忘记密码
const handleForgotPassword = () => {
  ElMessage.info('请联系系统管理员重置密码')
}

// 组件挂载时的初始化
onMounted(() => {
  // 如果已经登录，直接跳转到首页
  if (userStore.isLoggedIn) {
    router.push('/dashboard')
    return
  }
  
  // 检查是否需要显示验证码
  const failedAttempts = localStorage.getItem('loginFailedAttempts')
  if (failedAttempts && parseInt(failedAttempts) >= 3) {
    showCaptcha.value = true
    refreshCaptcha()
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  
  .bg-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('/bg-pattern.svg') repeat;
    animation: float 20s ease-in-out infinite;
    opacity: 0.1;
  }
}

.login-content {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.login-form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  
  @media (min-width: 768px) {
    max-width: 500px;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .logo-image {
      width: 48px;
      height: 48px;
    }
    
    .logo-text {
      font-size: 28px;
      font-weight: 600;
      color: var(--el-color-primary);
      margin: 0;
    }
  }
  
  .subtitle {
    font-size: 16px;
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.login-form {
  width: 100%;
  max-width: 400px;
  
  .captcha-container {
    display: flex;
    gap: 12px;
    
    .captcha-input {
      flex: 1;
    }
    
    .captcha-image {
      position: relative;
      width: 120px;
      height: 40px;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      cursor: pointer;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .captcha-refresh {
        position: absolute;
        top: 0;
        right: 0;
        width: 24px;
        height: 24px;
        background: rgba(0, 0, 0, 0.5);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.3s;
      }
      
      &:hover .captcha-refresh {
        opacity: 1;
      }
    }
  }
  
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}

.login-footer {
  margin-top: 40px;
  text-align: center;
  
  .copyright {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-bottom: 16px;
  }
  
  .links {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

.login-banner {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  color: white;
  
  @media (max-width: 768px) {
    display: none;
  }
  
  .banner-content {
    text-align: center;
    margin-bottom: 40px;
    
    h2 {
      font-size: 36px;
      font-weight: 600;
      margin-bottom: 16px;
    }
    
    p {
      font-size: 18px;
      opacity: 0.9;
      margin-bottom: 32px;
    }
    
    .feature-list {
      list-style: none;
      padding: 0;
      margin: 0;
      
      li {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        margin-bottom: 12px;
        
        .el-icon {
          color: #67c23a;
        }
      }
    }
  }
  
  .banner-image {
    img {
      max-width: 400px;
      width: 100%;
      height: auto;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
  }
  
  .login-form-container {
    max-width: none;
    padding: 20px;
  }
  
  .login-header .logo .logo-text {
    font-size: 24px;
  }
}
</style>
