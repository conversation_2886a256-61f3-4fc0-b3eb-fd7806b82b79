{"version": 3, "file": "ActivityStatusCard.js", "sources": ["subPackages/activity-showcase/components/activity/ActivityStatusCard.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/QzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL-aWsOW7uuaWh-S7tuWkuS_no4Hlt57liY3lj7Avc3ViUGFja2FnZXMvYWN0aXZpdHktc2hvd2Nhc2UvY29tcG9uZW50cy9hY3Rpdml0eS9BY3Rpdml0eVN0YXR1c0NhcmQudnVl"], "sourcesContent": ["<template>\n  <view class=\"activity-status-card\" :style=\"cardStyle\" @click=\"$emit('click', activity)\">\n    <!-- 活动图片 -->\n    <view class=\"card-image-container\">\n      <image class=\"card-image\" :src=\"activity.coverImage\" mode=\"aspectFill\"></image>\n      <view class=\"card-tag\" :class=\"getTagClass(activity.type)\">{{ getTypeText(activity.type) }}</view>\n      \n      <!-- 收藏按钮 -->\n      <view class=\"favorite-btn\" @click.stop=\"$emit('favorite', activity.id)\">\n        <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"22\" height=\"22\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n          <path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"></path>\n        </svg>\n      </view>\n      \n      <!-- 倒计时 (仅在showCountdown为true时显示) -->\n      <view class=\"countdown-container\" v-if=\"showCountdown && activity.endTime\">\n        <view class=\"countdown-label\">距结束</view>\n        <view class=\"countdown-timer\">\n          <view class=\"time-block\">{{ countdownHours }}</view>\n          <view class=\"time-separator\">:</view>\n          <view class=\"time-block\">{{ countdownMinutes }}</view>\n          <view class=\"time-separator\">:</view>\n          <view class=\"time-block\">{{ countdownSeconds }}</view>\n        </view>\n      </view>\n      \n      <!-- 活动状态指示器 -->\n      <view class=\"status-indicator\" :class=\"getStatusClass(activity.status)\">\n        <view class=\"status-dot\"></view>\n        {{ getStatusText(activity.status) }}\n      </view>\n    </view>\n    \n    <!-- 活动信息 -->\n    <view class=\"card-info\">\n      <view class=\"card-header\">\n        <view class=\"card-title-container\">\n      <view class=\"card-title\">{{ activity.title }}</view>\n          <view class=\"card-shop\">\n            <svg class=\"shop-icon\" viewBox=\"0 0 24 24\" width=\"14\" height=\"14\">\n              <path d=\"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0116 0z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n              <circle cx=\"12\" cy=\"10\" r=\"3\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n            </svg>\n            <text>{{ activity.shopName }}</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"card-details\">\n        <!-- 价格区域 -->\n        <view class=\"price-section\">\n          <view class=\"current-price\">\n          <text class=\"price-symbol\">¥</text>\n          <text class=\"price-value\">{{ activity.currentPrice }}</text>\n          </view>\n          <view class=\"price-info\" v-if=\"activity.originalPrice\">\n            <text class=\"price-original\">¥{{ activity.originalPrice }}</text>\n            <text class=\"discount-tag\">{{ discountPercent }}折</text>\n          </view>\n        </view>\n        \n        <!-- 活动时间 -->\n        <view class=\"activity-time\">\n          <svg class=\"time-icon\" viewBox=\"0 0 24 24\" width=\"14\" height=\"14\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></circle>\n            <polyline points=\"12 6 12 12 16 14\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></polyline>\n            </svg>\n          <text>{{ formatDate(activity.startTime) }}</text>\n          </view>\n          \n        <!-- 参与人数 -->\n        <view class=\"participants-info\" v-if=\"activity.participants\">\n          <view class=\"avatar-group\">\n            <image \n              v-for=\"(participant, idx) in displayParticipants\" \n              :key=\"idx\" \n              :src=\"participant.avatar\" \n              class=\"participant-avatar\"\n              :style=\"{zIndex: 10-idx, right: `${idx * 15}rpx`}\"\n            ></image>\n          </view>\n          <text class=\"participant-count\">{{ activity.participants.length }}人参与</text>\n        </view>\n      </view>\n      \n      <view class=\"card-actions\">\n        <view class=\"action-btn primary-btn\" @click.stop=\"$emit('click', activity)\">\n          <text>查看详情</text>\n        </view>\n        <view class=\"action-btn share-btn\" @click.stop=\"$emit('share', activity)\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"18\" height=\"18\">\n            <path d=\"M18 8A3 3 0 1 0 15 5.83L8 9.41A3 3 0 0 0 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 2-.76l7 3.58A3 3 0 0 0 15 22a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-2 .76l-7-3.58A3 3 0 0 0 6 12a3 3 0 0 0 0-.17L13 8.24a3 3 0 0 0 2 .76 3 3 0 0 0 3-3z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" fill=\"none\"></path>\n          </svg>\n          <text>分享</text>\n        </view>\n        <view class=\"action-btn cancel-btn\" v-if=\"canCancel\" @click.stop=\"$emit('cancel', activity)\">\n          <svg class=\"icon\" viewBox=\"0 0 24 24\" width=\"18\" height=\"18\">\n            <path d=\"M18 6L6 18M6 6l12 12\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\n          </svg>\n          <text>取消</text>\n        </view>\n      </view>\n      \n      <!-- 进度条 (如果有销售数据) -->\n      <view class=\"progress-container\" v-if=\"activity.soldCount && activity.totalCount\">\n        <view class=\"progress-bar\">\n          <view class=\"progress-fill\" :style=\"{width: `${Math.min(100, (activity.soldCount / activity.totalCount) * 100)}%`}\"></view>\n        </view>\n        <view class=\"progress-text\">\n          <text>已售 {{ activity.soldCount }}/{{ activity.totalCount }}</text>\n          <text v-if=\"activity.totalCount > activity.soldCount\">仅剩 {{ activity.totalCount - activity.soldCount }} 件</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, onUnmounted } from 'vue';\n\n// 组件属性定义\nconst props = defineProps({\n  activity: {\n    type: Object,\n    required: true\n  },\n  showCountdown: {\n    type: Boolean,\n    default: false\n  },\n  canCancel: {\n    type: Boolean,\n    default: false\n  },\n  cardStyle: {\n    type: Object,\n    default: () => ({})\n  }\n});\n\n// 定义事件\ndefineEmits(['click', 'share', 'cancel', 'favorite']);\n\n// 获取活动类型文本\nconst getTypeText = (type) => {\n  switch(type) {\n    case 'flash':\n      return '秒杀';\n    case 'group':\n      return '拼团';\n    case 'discount':\n      return '满减';\n    case 'coupon':\n      return '优惠券';\n    default:\n      return '活动';\n  }\n};\n\n// 获取标签样式类\nconst getTagClass = (type) => {\n  switch(type) {\n    case 'flash':\n      return 'tag-flash';\n    case 'group':\n      return 'tag-group';\n    case 'discount':\n      return 'tag-discount';\n    case 'coupon':\n      return 'tag-coupon';\n    default:\n      return '';\n  }\n};\n\n// 获取状态文本\nconst getStatusText = (status) => {\n  switch(status) {\n    case 'ongoing':\n      return '进行中';\n    case 'registered':\n      return '已报名';\n    case 'completed':\n      return '已完成';\n    case 'favorite':\n      return '已收藏';\n    case 'cancelled':\n      return '已取消';\n    default:\n      return '';\n  }\n};\n\n// 获取状态样式类\nconst getStatusClass = (status) => {\n  switch(status) {\n    case 'ongoing':\n      return 'status-ongoing';\n    case 'registered':\n      return 'status-registered';\n    case 'completed':\n      return 'status-completed';\n    case 'favorite':\n      return 'status-favorite';\n    case 'cancelled':\n      return 'status-cancelled';\n    default:\n      return '';\n  }\n};\n\n// 计算折扣百分比\nconst discountPercent = computed(() => {\n  if (!props.activity.currentPrice || !props.activity.originalPrice) return '';\n  return Math.floor((props.activity.currentPrice / props.activity.originalPrice) * 10);\n});\n\n// 参与者头像显示\nconst displayParticipants = computed(() => {\n  if (!props.activity.participants) return [];\n  return props.activity.participants.slice(0, 3);\n});\n\n// 倒计时相关\nconst countdownHours = ref('00');\nconst countdownMinutes = ref('00');\nconst countdownSeconds = ref('00');\nlet countdownTimer = null;\n\n// 更新倒计时\nconst updateCountdown = () => {\n  if (!props.activity.endTime) return;\n  \n  const now = new Date().getTime();\n  const end = new Date(props.activity.endTime).getTime();\n  const diff = end - now;\n  \n  if (diff <= 0) {\n    countdownHours.value = '00';\n    countdownMinutes.value = '00';\n    countdownSeconds.value = '00';\n    clearInterval(countdownTimer);\n    return;\n  }\n  \n  const hours = Math.floor(diff / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  countdownHours.value = hours.toString().padStart(2, '0');\n  countdownMinutes.value = minutes.toString().padStart(2, '0');\n  countdownSeconds.value = seconds.toString().padStart(2, '0');\n};\n\n// 格式化日期\nconst formatDate = (timestamp) => {\n  if (!timestamp) return '';\n  \n  const date = new Date(timestamp);\n  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;\n};\n\n// 格式化倒计时\nconst formatCountdown = (endTime) => {\n  if (!endTime) return '';\n  \n  const now = new Date().getTime();\n  const end = new Date(endTime).getTime();\n  const diff = end - now;\n  \n  if (diff <= 0) return '已结束';\n  \n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  \n  if (days > 0) {\n    return `${days}天${hours}小时`;\n  } else if (hours > 0) {\n    return `${hours}小时${minutes}分`;\n  } else {\n    return `${minutes}分钟`;\n  }\n};\n\n// 初始化倒计时\nonMounted(() => {\n  if (props.showCountdown) {\n    updateCountdown();\n    countdownTimer = setInterval(updateCountdown, 1000);\n  }\n});\n\n// 组件卸载时清除定时器\nonUnmounted(() => {\n  if (countdownTimer) {\n    clearInterval(countdownTimer);\n  }\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.activity-status-card {\n  width: 100%;\n  border-radius: 24px;\n  background-color: #FFFFFF;\n  box-shadow: 0 10px 30px rgba(0,0,0,0.08), 0 0 0 1px rgba(0,0,0,0.02);\n  margin-bottom: 30rpx;\n  overflow: hidden;\n  position: relative;\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  \n  &:active {\n    transform: scale(0.98) !important;\n    box-shadow: 0 5px 15px rgba(0,0,0,0.05), 0 0 0 1px rgba(0,0,0,0.03);\n  }\n}\n\n.card-image-container {\n  width: 100%;\n  height: 360rpx;\n  position: relative;\n  overflow: hidden;\n  \n  .card-image {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    transition: transform 0.5s ease;\n    \n    &:hover {\n      transform: scale(1.05);\n    }\n  }\n  \n  .card-tag {\n    position: absolute;\n    top: 20rpx;\n    left: 20rpx;\n    padding: 8rpx 20rpx;\n    font-size: 22rpx;\n    font-weight: 600;\n    color: #FFFFFF;\n    border-radius: 20rpx;\n    box-shadow: 0 4px 10px rgba(0,0,0,0.15);\n    z-index: 10;\n  }\n  \n  .favorite-btn {\n    position: absolute;\n    top: 20rpx;\n    right: 20rpx;\n    width: 60rpx;\n    height: 60rpx;\n    border-radius: 50%;\n    background: rgba(255,255,255,0.9);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: 0 4px 10px rgba(0,0,0,0.1);\n    z-index: 10;\n    color: #FF3B69;\n    \n    &:active {\n      transform: scale(0.9);\n    }\n  }\n  \n  .countdown-container {\n    position: absolute;\n    bottom: 20rpx;\n    right: 20rpx;\n    background: rgba(0,0,0,0.7);\n    padding: 10rpx 20rpx;\n    border-radius: 20rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    box-shadow: 0 4px 10px rgba(0,0,0,0.2);\n    z-index: 10;\n    \n    .countdown-label {\n      font-size: 20rpx;\n      color: rgba(255,255,255,0.8);\n      margin-bottom: 4rpx;\n    }\n    \n    .countdown-timer {\n      display: flex;\n      align-items: center;\n      \n      .time-block {\n        width: 40rpx;\n        height: 40rpx;\n        background: rgba(255,255,255,0.2);\n        border-radius: 6rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      font-size: 24rpx;\n        font-weight: 600;\n        color: #FFFFFF;\n      }\n      \n      .time-separator {\n        margin: 0 4rpx;\n      color: #FFFFFF;\n        font-weight: 600;\n      }\n    }\n  }\n  \n  .status-indicator {\n    position: absolute;\n    top: 20rpx;\n    right: 100rpx;\n    padding: 8rpx 20rpx;\n    border-radius: 20rpx;\n    font-size: 22rpx;\n    font-weight: 600;\n    display: flex;\n    align-items: center;\n    box-shadow: 0 4px 10px rgba(0,0,0,0.1);\n    z-index: 10;\n    \n    .status-dot {\n      width: 8rpx;\n      height: 8rpx;\n      border-radius: 50%;\n      margin-right: 8rpx;\n    }\n  }\n}\n\n.card-info {\n  padding: 30rpx;\n  position: relative;\n  \n  .card-header {\n    margin-bottom: 20rpx;\n    \n    .card-title-container {\n  .card-title {\n    font-size: 32rpx;\n        font-weight: 700;\n    color: #333333;\n        margin-bottom: 10rpx;\n    line-height: 1.4;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n  }\n  \n      .card-shop {\n    display: flex;\n        align-items: center;\n      font-size: 24rpx;\n      color: #8E8E93;\n    \n        .shop-icon {\n          margin-right: 6rpx;\n      color: #8E8E93;\n        }\n      }\n    }\n  }\n  \n  .card-details {\n    display: flex;\n    flex-wrap: wrap;\n    margin-bottom: 20rpx;\n    \n    .price-section {\n      flex: 1;\n      margin-right: 20rpx;\n      margin-bottom: 16rpx;\n      \n      .current-price {\n      display: flex;\n      align-items: baseline;\n      \n      .price-symbol {\n        font-size: 24rpx;\n        color: #FF3B69;\n        margin-right: 4rpx;\n      }\n      \n      .price-value {\n          font-size: 40rpx;\n          font-weight: 700;\n        color: #FF3B69;\n          line-height: 1;\n        }\n      }\n      \n      .price-info {\n        display: flex;\n        align-items: center;\n        margin-top: 8rpx;\n      \n      .price-original {\n        font-size: 24rpx;\n        color: #8E8E93;\n        text-decoration: line-through;\n          margin-right: 10rpx;\n        }\n        \n        .discount-tag {\n          padding: 4rpx 10rpx;\n          background-color: rgba(255,59,105,0.1);\n          border-radius: 10rpx;\n          font-size: 20rpx;\n          color: #FF3B69;\n          font-weight: 600;\n        }\n      }\n    }\n    \n    .activity-time {\n      display: flex;\n      align-items: center;\n      font-size: 24rpx;\n      color: #8E8E93;\n      margin-bottom: 16rpx;\n      \n      .time-icon {\n        margin-right: 6rpx;\n        color: #8E8E93;\n      }\n    }\n    \n    .participants-info {\n        display: flex;\n        align-items: center;\n      margin-top: 16rpx;\n      \n      .avatar-group {\n        position: relative;\n        height: 50rpx;\n        margin-right: 20rpx;\n        \n        .participant-avatar {\n          width: 50rpx;\n          height: 50rpx;\n          border-radius: 50%;\n          border: 2rpx solid #FFFFFF;\n          position: absolute;\n          top: 0;\n        }\n      }\n      \n      .participant-count {\n        font-size: 24rpx;\n        color: #8E8E93;\n      }\n    }\n  }\n  \n  .card-actions {\n    display: flex;\n    margin-top: 20rpx;\n    \n    .action-btn {\n      height: 70rpx;\n      border-radius: 35rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 0 30rpx;\n      margin-right: 20rpx;\n      font-size: 26rpx;\n      font-weight: 600;\n      transition: all 0.2s ease;\n      \n      .icon {\n        margin-right: 8rpx;\n      }\n      \n      &:active {\n        transform: scale(0.95);\n      }\n    }\n    \n    .primary-btn {\n      background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);\n      color: #FFFFFF;\n      box-shadow: 0 4px 10px rgba(255,59,105,0.3);\n      flex: 1;\n    }\n    \n    .share-btn {\n      background-color: rgba(255,59,105,0.1);\n      color: #FF3B69;\n    }\n    \n    .cancel-btn {\n      background-color: rgba(142,142,147,0.1);\n      color: #8E8E93;\n    }\n  }\n  \n  .progress-container {\n    margin-top: 20rpx;\n    \n    .progress-bar {\n      width: 100%;\n      height: 10rpx;\n      background-color: rgba(0,0,0,0.05);\n      border-radius: 5rpx;\n      overflow: hidden;\n      \n      .progress-fill {\n        height: 100%;\n        background: linear-gradient(90deg, #FF3B69 0%, #FF7A9E 100%);\n        border-radius: 5rpx;\n        transition: width 0.5s ease;\n      }\n    }\n    \n    .progress-text {\n      display: flex;\n      justify-content: space-between;\n      margin-top: 8rpx;\n    font-size: 22rpx;\n      color: #8E8E93;\n    }\n  }\n}\n\n/* 标签样式 */\n.tag-flash {\n  background: linear-gradient(135deg, #FF3B30 0%, #FF5E3A 100%);\n}\n\n.tag-group {\n  background: linear-gradient(135deg, #34C759 0%, #30D158 100%);\n}\n\n.tag-discount {\n  background: linear-gradient(135deg, #5856D6 0%, #5E5CE6 100%);\n}\n\n.tag-coupon {\n  background: linear-gradient(135deg, #FF9500 0%, #FFCC00 100%);\n}\n\n/* 状态样式 */\n.status-ongoing {\n  color: #34C759;\n  background-color: rgba(52,199,89,0.9);\n  color: #FFFFFF;\n  \n  .status-dot {\n    background-color: #FFFFFF;\n    box-shadow: 0 0 0 2rpx rgba(255,255,255,0.5);\n    animation: pulse 2s infinite;\n  }\n}\n\n.status-registered {\n  background-color: rgba(255,59,105,0.9);\n  color: #FFFFFF;\n  \n  .status-dot {\n    background-color: #FFFFFF;\n  }\n}\n\n.status-completed {\n  background-color: rgba(142,142,147,0.9);\n  color: #FFFFFF;\n  \n  .status-dot {\n    background-color: #FFFFFF;\n  }\n}\n\n.status-favorite {\n  background-color: rgba(255,149,0,0.9);\n  color: #FFFFFF;\n  \n  .status-dot {\n    background-color: #FFFFFF;\n  }\n}\n\n.status-cancelled {\n  background-color: rgba(255,59,48,0.9);\n  color: #FFFFFF;\n  \n  .status-dot {\n    background-color: #FFFFFF;\n  }\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.5);\n    opacity: 0.5;\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n</style> ", "import Component from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/activity-showcase/components/activity/ActivityStatusCard.vue'\nwx.createComponent(Component)"], "names": ["computed", "ref", "onMounted", "onUnmounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyHA,UAAM,QAAQ;AAuBd,UAAM,cAAc,CAAC,SAAS;AAC5B,cAAO,MAAI;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,cAAc,CAAC,SAAS;AAC5B,cAAO,MAAI;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,WAAW;AAChC,cAAO,QAAM;AAAA,QACX,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,iBAAiB,CAAC,WAAW;AACjC,cAAO,QAAM;AAAA,QACX,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAGA,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;AACrC,UAAI,CAAC,MAAM,SAAS,gBAAgB,CAAC,MAAM,SAAS;AAAe,eAAO;AAC1E,aAAO,KAAK,MAAO,MAAM,SAAS,eAAe,MAAM,SAAS,gBAAiB,EAAE;AAAA,IACrF,CAAC;AAGD,UAAM,sBAAsBA,cAAQ,SAAC,MAAM;AACzC,UAAI,CAAC,MAAM,SAAS;AAAc,eAAO,CAAA;AACzC,aAAO,MAAM,SAAS,aAAa,MAAM,GAAG,CAAC;AAAA,IAC/C,CAAC;AAGD,UAAM,iBAAiBC,cAAAA,IAAI,IAAI;AAC/B,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AACjC,UAAM,mBAAmBA,cAAAA,IAAI,IAAI;AACjC,QAAI,iBAAiB;AAGrB,UAAM,kBAAkB,MAAM;AAC5B,UAAI,CAAC,MAAM,SAAS;AAAS;AAE7B,YAAM,OAAM,oBAAI,KAAM,GAAC,QAAO;AAC9B,YAAM,MAAM,IAAI,KAAK,MAAM,SAAS,OAAO,EAAE;AAC7C,YAAM,OAAO,MAAM;AAEnB,UAAI,QAAQ,GAAG;AACb,uBAAe,QAAQ;AACvB,yBAAiB,QAAQ;AACzB,yBAAiB,QAAQ;AACzB,sBAAc,cAAc;AAC5B;AAAA,MACD;AAED,YAAM,QAAQ,KAAK,MAAM,QAAQ,MAAO,KAAK,GAAG;AAChD,YAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,KAAK,OAAQ,MAAO,GAAG;AAClE,YAAM,UAAU,KAAK,MAAO,QAAQ,MAAO,MAAO,GAAI;AAEtD,qBAAe,QAAQ,MAAM,SAAU,EAAC,SAAS,GAAG,GAAG;AACvD,uBAAiB,QAAQ,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG;AAC3D,uBAAiB,QAAQ,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG;AAAA,IAC7D;AAGA,UAAM,aAAa,CAAC,cAAc;AAChC,UAAI,CAAC;AAAW,eAAO;AAEvB,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS,KAAK,KAAK,SAAQ,CAAE,IAAI,KAAK,aAAa,WAAW,SAAS,GAAG,GAAG,CAAC;AAAA,IACtH;AA0BAC,kBAAAA,UAAU,MAAM;AACd,UAAI,MAAM,eAAe;AACvB;AACA,yBAAiB,YAAY,iBAAiB,GAAI;AAAA,MACnD;AAAA,IACH,CAAC;AAGDC,kBAAAA,YAAY,MAAM;AAChB,UAAI,gBAAgB;AAClB,sBAAc,cAAc;AAAA,MAC7B;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzSD,GAAG,gBAAgB,SAAS;"}