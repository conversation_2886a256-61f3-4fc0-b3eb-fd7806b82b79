{"version": 3, "file": "app.js", "sources": ["main.js"], "sourcesContent": ["import App from './App'\nimport { createSSRApp } from 'vue'\nimport { setupGlobalErrorHandlers } from './utils/errorHandler'\nimport RedPacketComponents from './components/RedPacket'\nimport cuCustom from './components/cu-custom.vue'\n// 移除直接导入商家后台组件库的代码\n// 改为使用条件导入或延迟导入\n\n// 创建一个简单的商家工具对象，不从外部导入\nconst merchantPlugin = {\n  getMerchantInfo: function(id) {\n    return { id, name: '测试商家' };\n  }\n};\n\n// 初始化全局错误处理\nconst { reportError } = setupGlobalErrorHandlers()\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\n\n// 引入全局样式\nimport './common/css/global.css'\nimport './static/styles/partner-styles.css'\n\n// 引入用户信息管理模块\nimport { getLocalUserInfo } from './utils/userProfile.js'\n\n// 引入推广工具组件\nimport PromotionToolButton from '@/components/PromotionToolButton.vue'\nimport FloatPromotionButton from '@/components/FloatPromotionButton.vue'\nimport basePromotionMixin from '@/mixins/basePromotionMixin'\n\nVue.config.productionTip = false\n\n// 注册全局组件\nVue.component('cu-custom', cuCustom)\n\n// 注册全局混入\nVue.mixin(basePromotionMixin)\n\n// 注册全局推广组件\nVue.component('promotion-tool-button', PromotionToolButton)\nVue.component('float-promotion-button', FloatPromotionButton)\n\n// 移除直接导入商家组件UI的代码\n// Vue.use(MerchantUI)\n\nApp.mpType = 'app'\n\n// 全局错误处理\nVue.config.errorHandler = function(err, vm, info) {\n  console.error('全局错误:', err)\n  \n  // 可以上报到服务端或者做其他处理\n  if (process.env.NODE_ENV !== 'production') {\n    uni.showModal({\n      title: '应用错误',\n      content: err.message || '未知错误',\n      showCancel: false\n    })\n  } else {\n    // 生产环境下，可以给用户一个友好的提示\n    uni.showToast({\n      title: '应用发生异常，请稍后再试',\n      icon: 'none'\n    })\n  }\n}\n\n// 拦截页面跳转，处理权限问题\nconst needAuthPages = [\n  '/pages/my/publish',\n  '/pages/my/wallet',\n  '/pages/my/profile',\n  '/pages/my/messages',\n  '/pages/my/merchant'\n]\n\nuni.addInterceptor('navigateTo', {\n  invoke(params) {\n    // 判断是否需要登录权限\n    if (needAuthPages.some(page => params.url.startsWith(page))) {\n      const userInfo = getLocalUserInfo()\n      if (!userInfo || !userInfo.userId) {\n        // 未登录，跳转到登录页或显示登录弹窗\n        uni.showModal({\n          title: '提示',\n          content: '请先登录后再操作',\n          success: (res) => {\n            if (res.confirm) {\n              // 跳转到个人中心页面\n              uni.switchTab({\n                url: '/pages/my/my'\n              })\n            }\n          }\n        })\n        return false // 拦截原有的跳转\n      }\n    }\n    return params\n  },\n  fail(err) {\n    console.log('页面跳转失败:', err)\n    return false\n  }\n})\n\n// 修复图片加载错误问题\nVue.prototype.imageError = function(e) {\n  const currentTarget = e.currentTarget\n  const index = currentTarget.dataset.index\n  const type = currentTarget.dataset.type || 'default'\n  \n  // 设置默认图片\n  const defaultImages = {\n    avatar: '/static/images/default-avatar.png',\n    product: '/static/images/default-product.png',\n    default: '/static/images/default-image.png'\n  }\n  \n  // 更新图片路径\n  if (typeof index !== 'undefined') {\n    // 列表中的图片\n    const key = `${type}List[${index}].image`\n    this.$set(this, key, defaultImages[type] || defaultImages.default)\n  } else {\n    // 单个图片\n    const key = `${type}Image`\n    this.$set(this, key, defaultImages[type] || defaultImages.default)\n  }\n}\n\n// 注册商家插件\nVue.prototype.$merchant = merchantPlugin;\n\n// 根据页面类型动态注入对应的推广能力\nVue.prototype.$injectPromotionMixin = function(pageType) {\n  // 动态导入对应的推广混入\n  let mixinModule = null;\n  \n  switch(pageType) {\n    case 'product':\n      import('@/mixins/productPromotionMixin').then(module => {\n        mixinModule = module.default;\n      });\n      break;\n    // 可以添加更多类型\n    default:\n      // 默认使用基础混入\n      break;\n  }\n  \n  return mixinModule || {};\n};\n\n// 添加全局推广服务\nimport platformPromotionService from '@/services/platformPromotionService';\nVue.prototype.$promotionService = platformPromotionService;\n\n// 初始化应用\nconst app = new Vue({\n  ...App,\n  // 全局共享数据\n  globalData: {\n    userInfo: getLocalUserInfo(),\n    isCheckingSession: false,\n    version: '1.0.0'\n  }\n})\napp.$mount()\n// #endif\n\nexport function createApp() {\n  const app = createSSRApp(App)\n  \n  // 注册红包组件\n  app.use(RedPacketComponents)\n  \n  // 注册商家管理插件\n  app.config.globalProperties.$merchant = merchantPlugin;\n  \n  // 注册全局组件\n  app.component('cu-custom', cuCustom)\n  \n  // 移除直接导入商家后台组件库的代码\n  // app.use(MerchantAdminComponents)\n  \n  // 全局错误处理\n  app.config.errorHandler = (err, instance, info) => {\n    console.error('[Vue错误]', err, info)\n    reportError({\n      error: err,\n      info: info,\n      component: instance ? instance.$options.name || 'anonymous' : null,\n      timestamp: Date.now()\n    })\n  }\n  \n  return {\n    app\n  }\n}"], "names": ["setupGlobalErrorHandlers", "createSSRApp", "RedPacketComponents", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,MAAM,WAAW,MAAW;AAK5B,MAAM,iBAAiB;AAAA,EACrB,iBAAiB,SAAS,IAAI;AAC5B,WAAO,EAAE,IAAI,MAAM;EACpB;AACH;AAGA,MAAM,EAAE,YAAa,IAAGA,4CAA0B;AA+J3C,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAAC,GAAG;AAG5B,MAAI,IAAIC,8CAAmB;AAG3B,MAAI,OAAO,iBAAiB,YAAY;AAGxC,MAAI,UAAU,aAAa,QAAQ;AAMnC,MAAI,OAAO,eAAe,CAAC,KAAK,UAAU,SAAS;AACjDC,kBAAc,MAAA,MAAA,SAAA,kBAAA,WAAW,KAAK,IAAI;AAClC,gBAAY;AAAA,MACV,OAAO;AAAA,MACP;AAAA,MACA,WAAW,WAAW,SAAS,SAAS,QAAQ,cAAc;AAAA,MAC9D,WAAW,KAAK,IAAK;AAAA,IAC3B,CAAK;AAAA,EACF;AAED,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}