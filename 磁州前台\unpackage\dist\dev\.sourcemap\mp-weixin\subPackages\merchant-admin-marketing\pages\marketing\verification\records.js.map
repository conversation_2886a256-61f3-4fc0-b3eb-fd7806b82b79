{"version": 3, "file": "records.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/verification/records.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1x2ZXJpZmljYXRpb25ccmVjb3Jkcy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"records-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"navbar\">\r\n      <view class=\"navbar-back\" @click=\"goBack\">\r\n        <view class=\"back-icon\"></view>\r\n      </view>\r\n      <text class=\"navbar-title\">核销记录</text>\r\n      <view class=\"navbar-right\">\r\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 筛选区域 -->\r\n    <view class=\"filter-area\">\r\n      <view class=\"filter-tabs\">\r\n        <view \r\n          class=\"filter-tab\" \r\n          v-for=\"(tab, index) in filterTabs\" \r\n          :key=\"index\"\r\n          :class=\"{ 'active': currentTab === index }\"\r\n          @click=\"switchTab(index)\"\r\n        >\r\n          <text>{{tab}}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"filter-actions\">\r\n        <view class=\"date-filter\" @click=\"showDatePicker\">\r\n          <text class=\"date-text\">{{dateRange}}</text>\r\n          <view class=\"date-arrow\"></view>\r\n        </view>\r\n        \r\n        <view class=\"search-btn\" @click=\"showSearch\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"18\" height=\"18\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\r\n            <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\r\n            <line x1=\"21\" y1=\"21\" x2=\"16.65\" y2=\"16.65\"></line>\r\n          </svg>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 记录列表 -->\r\n    <view class=\"records-list\">\r\n      <view v-if=\"filteredRecords.length === 0\" class=\"empty-records\">\r\n        <text class=\"empty-text\">暂无核销记录</text>\r\n      </view>\r\n      <view v-else class=\"record-item\" v-for=\"(record, index) in filteredRecords\" :key=\"index\" @click=\"showRecordDetail(record)\">\r\n        <view class=\"record-type\" :class=\"record.typeClass\">{{record.typeText}}</view>\r\n        <view class=\"record-content\">\r\n          <view class=\"record-main\">\r\n            <text class=\"record-title\">{{record.title}}</text>\r\n            <text class=\"record-code\">{{record.code}}</text>\r\n          </view>\r\n          <view class=\"record-info\">\r\n            <text class=\"record-user\">用户：{{record.user}}</text>\r\n            <text class=\"record-time\">{{record.time}}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"record-status\" :class=\"record.statusClass\">{{record.status}}</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 加载更多 -->\r\n    <view class=\"load-more\" v-if=\"hasMore\">\r\n      <text class=\"load-text\" @click=\"loadMore\">加载更多</text>\r\n    </view>\r\n    \r\n    <!-- 搜索弹窗 -->\r\n    <view class=\"search-popup\" v-if=\"showSearchPopup\">\r\n      <view class=\"search-header\">\r\n        <view class=\"search-input-box\">\r\n          <input \r\n            class=\"search-input\" \r\n            type=\"text\" \r\n            placeholder=\"搜索核销码/商品名称/用户\" \r\n            v-model=\"searchKeyword\"\r\n            focus\r\n            @confirm=\"doSearch\"\r\n          />\r\n          <view class=\"clear-btn\" v-if=\"searchKeyword\" @click=\"clearSearch\">×</view>\r\n        </view>\r\n        <view class=\"cancel-btn\" @click=\"hideSearch\">取消</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 记录详情弹窗 -->\r\n    <view class=\"detail-popup\" v-if=\"showDetailPopup\">\r\n      <view class=\"popup-content\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">核销详情</text>\r\n          <view class=\"popup-close\" @click=\"closeDetailPopup\">×</view>\r\n        </view>\r\n        \r\n        <view class=\"detail-info\">\r\n          <view class=\"detail-status\" :class=\"currentRecord.statusClass\">{{currentRecord.status}}</view>\r\n          \r\n          <view class=\"detail-item\">\r\n            <text class=\"detail-label\">核销类型</text>\r\n            <text class=\"detail-value\">{{currentRecord.typeText}}</text>\r\n          </view>\r\n          <view class=\"detail-item\">\r\n            <text class=\"detail-label\">商品名称</text>\r\n            <text class=\"detail-value\">{{currentRecord.title}}</text>\r\n          </view>\r\n          <view class=\"detail-item\">\r\n            <text class=\"detail-label\">核销码</text>\r\n            <text class=\"detail-value\">{{currentRecord.code}}</text>\r\n          </view>\r\n          <view class=\"detail-item\">\r\n            <text class=\"detail-label\">用户信息</text>\r\n            <text class=\"detail-value\">{{currentRecord.user}}</text>\r\n          </view>\r\n          <view class=\"detail-item\">\r\n            <text class=\"detail-label\">核销时间</text>\r\n            <text class=\"detail-value\">{{currentRecord.time}}</text>\r\n          </view>\r\n          <view class=\"detail-item\">\r\n            <text class=\"detail-label\">核销人员</text>\r\n            <text class=\"detail-value\">{{currentRecord.operator || '系统'}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <button class=\"close-btn\" @click=\"closeDetailPopup\">关闭</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      filterTabs: ['全部', '拼团', '优惠券', '秒杀'],\r\n      currentTab: 0,\r\n      dateRange: '最近7天',\r\n      searchKeyword: '',\r\n      showSearchPopup: false,\r\n      showDetailPopup: false,\r\n      currentRecord: {},\r\n      hasMore: true,\r\n      records: [\r\n        {\r\n          id: 1,\r\n          typeText: '拼团',\r\n          typeClass: 'type-group',\r\n          title: '双人下午茶套餐拼团',\r\n          code: 'GP20230618001',\r\n          user: '张三 (138****8888)',\r\n          time: '2023-06-18 14:30',\r\n          status: '已核销',\r\n          statusClass: 'status-success',\r\n          operator: '王店长'\r\n        },\r\n        {\r\n          id: 2,\r\n          typeText: '优惠券',\r\n          typeClass: 'type-coupon',\r\n          title: '新店开业满100减20券',\r\n          code: 'CP20230618002',\r\n          user: '李四 (139****9999)',\r\n          time: '2023-06-18 11:15',\r\n          status: '已核销',\r\n          statusClass: 'status-success',\r\n          operator: '王店长'\r\n        },\r\n        {\r\n          id: 3,\r\n          typeText: '秒杀',\r\n          typeClass: 'type-flash',\r\n          title: '限时特价烤鸭套餐',\r\n          code: 'FS20230617005',\r\n          user: '王五 (137****7777)',\r\n          time: '2023-06-17 18:45',\r\n          status: '已核销',\r\n          statusClass: 'status-success',\r\n          operator: '张经理'\r\n        },\r\n        {\r\n          id: 4,\r\n          typeText: '拼团',\r\n          typeClass: 'type-group',\r\n          title: '亲子套餐拼团',\r\n          code: 'GP20230617003',\r\n          user: '赵六 (136****6666)',\r\n          time: '2023-06-17 12:20',\r\n          status: '已核销',\r\n          statusClass: 'status-success',\r\n          operator: '张经理'\r\n        },\r\n        {\r\n          id: 5,\r\n          typeText: '优惠券',\r\n          typeClass: 'type-coupon',\r\n          title: '生日特惠券',\r\n          code: 'CP20230616008',\r\n          user: '钱七 (135****5555)',\r\n          time: '2023-06-16 19:30',\r\n          status: '已核销',\r\n          statusClass: 'status-success',\r\n          operator: '李助理'\r\n        },\r\n        {\r\n          id: 6,\r\n          typeText: '秒杀',\r\n          typeClass: 'type-flash',\r\n          title: '周末特价牛排套餐',\r\n          code: 'FS20230616002',\r\n          user: '孙八 (134****4444)',\r\n          time: '2023-06-16 18:10',\r\n          status: '已核销',\r\n          statusClass: 'status-success',\r\n          operator: '李助理'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    filteredRecords() {\r\n      let result = [...this.records];\r\n      \r\n      // 根据标签筛选\r\n      if (this.currentTab > 0) {\r\n        const tabType = this.filterTabs[this.currentTab];\r\n        result = result.filter(record => record.typeText === tabType);\r\n      }\r\n      \r\n      // 根据搜索关键词筛选\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase();\r\n        result = result.filter(record => \r\n          record.title.toLowerCase().includes(keyword) || \r\n          record.code.toLowerCase().includes(keyword) || \r\n          record.user.toLowerCase().includes(keyword)\r\n        );\r\n      }\r\n      \r\n      return result;\r\n    }\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n    showHelp() {\r\n      uni.showToast({\r\n        title: '核销记录帮助',\r\n        icon: 'none'\r\n      });\r\n    },\r\n    switchTab(index) {\r\n      this.currentTab = index;\r\n    },\r\n    showDatePicker() {\r\n      uni.showActionSheet({\r\n        itemList: ['今天', '昨天', '最近7天', '最近30天', '全部'],\r\n        success: (res) => {\r\n          const dateOptions = ['今天', '昨天', '最近7天', '最近30天', '全部'];\r\n          this.dateRange = dateOptions[res.tapIndex];\r\n          \r\n          uni.showToast({\r\n            title: `已选择${this.dateRange}`,\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n    showSearch() {\r\n      this.showSearchPopup = true;\r\n    },\r\n    hideSearch() {\r\n      this.showSearchPopup = false;\r\n      this.searchKeyword = '';\r\n    },\r\n    clearSearch() {\r\n      this.searchKeyword = '';\r\n    },\r\n    doSearch() {\r\n      this.showSearchPopup = false;\r\n      \r\n      // 如果没有搜索结果，显示提示\r\n      if (this.filteredRecords.length === 0) {\r\n        uni.showToast({\r\n          title: '未找到相关记录',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    showRecordDetail(record) {\r\n      this.currentRecord = record;\r\n      this.showDetailPopup = true;\r\n    },\r\n    closeDetailPopup() {\r\n      this.showDetailPopup = false;\r\n    },\r\n    loadMore() {\r\n      uni.showLoading({\r\n        title: '加载中...'\r\n      });\r\n      \r\n      // 模拟加载更多数据\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        \r\n        // 添加更多模拟数据\r\n        const moreRecords = [\r\n          {\r\n            id: 7,\r\n            typeText: '拼团',\r\n            typeClass: 'type-group',\r\n            title: '四人火锅套餐拼团',\r\n            code: 'GP20230615004',\r\n            user: '周九 (133****3333)',\r\n            time: '2023-06-15 20:15',\r\n            status: '已核销',\r\n            statusClass: 'status-success',\r\n            operator: '王店长'\r\n          },\r\n          {\r\n            id: 8,\r\n            typeText: '优惠券',\r\n            typeClass: 'type-coupon',\r\n            title: '下午茶优惠券',\r\n            code: 'CP20230615007',\r\n            user: '吴十 (132****2222)',\r\n            time: '2023-06-15 15:40',\r\n            status: '已核销',\r\n            statusClass: 'status-success',\r\n            operator: '张经理'\r\n          }\r\n        ];\r\n        \r\n        this.records = [...this.records, ...moreRecords];\r\n        \r\n        // 假设没有更多数据了\r\n        this.hasMore = false;\r\n        \r\n        uni.showToast({\r\n          title: '已加载全部数据',\r\n          icon: 'none'\r\n        });\r\n      }, 1000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.records-container {\r\n  min-height: 100vh;\r\n  background-color: #F5F7FA;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n/* 导航栏样式 */\r\n.navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(135deg, #e67e22, #d35400);\r\n  color: #fff;\r\n  padding: 44px 16px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n  box-shadow: 0 4px 20px rgba(230, 126, 34, 0.2);\r\n}\r\n\r\n.navbar-back {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-left: 2px solid #fff;\r\n  border-bottom: 2px solid #fff;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.navbar-right {\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.help-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 筛选区域样式 */\r\n.filter-area {\r\n  background: #FFFFFF;\r\n  padding: 20px;\r\n  margin-bottom: 15px;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n  padding-bottom: 12px;\r\n  margin-bottom: 18px;\r\n}\r\n\r\n.filter-tab {\r\n  margin-right: 25px;\r\n  padding: 6px 0;\r\n  font-size: 15px;\r\n  color: #666;\r\n  position: relative;\r\n  transition: color 0.2s;\r\n}\r\n\r\n.filter-tab.active {\r\n  color: #e67e22;\r\n  font-weight: 600;\r\n}\r\n\r\n.filter-tab.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -13px;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: #e67e22;\r\n  border-radius: 1.5px;\r\n}\r\n\r\n.filter-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.date-filter {\r\n  display: flex;\r\n  align-items: center;\r\n  background: #F5F7FA;\r\n  border-radius: 18px;\r\n  padding: 8px 14px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.date-filter:active {\r\n  background: #EBEEF2;\r\n}\r\n\r\n.date-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-right: 8px;\r\n  font-weight: 500;\r\n}\r\n\r\n.date-arrow {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-top: 2px solid #666;\r\n  border-right: 2px solid #666;\r\n  transform: rotate(135deg);\r\n}\r\n\r\n.search-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 18px;\r\n  background: #F5F7FA;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #666;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.search-btn:active {\r\n  background: #EBEEF2;\r\n}\r\n\r\n/* 记录列表样式 */\r\n.records-list {\r\n  background: #FFFFFF;\r\n  margin: 0 15px;\r\n  border-radius: 20px;\r\n  padding: 20px;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.empty-records {\r\n  padding: 50px 0;\r\n  text-align: center;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #999;\r\n}\r\n\r\n.record-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.record-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.record-type {\r\n  width: 60px;\r\n  height: 28px;\r\n  border-radius: 14px;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.type-group {\r\n  background-color: rgba(39, 174, 96, 0.15);\r\n  color: #27ae60;\r\n}\r\n\r\n.type-coupon {\r\n  background-color: rgba(41, 128, 185, 0.15);\r\n  color: #2980b9;\r\n}\r\n\r\n.type-flash {\r\n  background-color: rgba(231, 76, 60, 0.15);\r\n  color: #e74c3c;\r\n}\r\n\r\n.record-content {\r\n  flex: 1;\r\n}\r\n\r\n.record-main {\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.record-title {\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.record-code {\r\n  font-size: 13px;\r\n  color: #777;\r\n}\r\n\r\n.record-info {\r\n  display: flex;\r\n  font-size: 13px;\r\n  color: #777;\r\n  margin-top: 6px;\r\n}\r\n\r\n.record-user {\r\n  margin-right: 12px;\r\n}\r\n\r\n.record-status {\r\n  font-size: 13px;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-success {\r\n  color: #27ae60;\r\n}\r\n\r\n.status-pending {\r\n  color: #e67e22;\r\n}\r\n\r\n.status-failed {\r\n  color: #e74c3c;\r\n}\r\n\r\n/* 加载更多样式 */\r\n.load-more {\r\n  text-align: center;\r\n  padding: 20px 0;\r\n}\r\n\r\n.load-text {\r\n  font-size: 15px;\r\n  color: #2980b9;\r\n  font-weight: 500;\r\n  padding: 10px 20px;\r\n  border-radius: 18px;\r\n  background: rgba(41, 128, 185, 0.1);\r\n  display: inline-block;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.load-text:active {\r\n  background: rgba(41, 128, 185, 0.15);\r\n}\r\n\r\n/* 搜索弹窗样式 */\r\n.search-popup {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: #FFFFFF;\r\n  z-index: 999;\r\n}\r\n\r\n.search-header {\r\n  padding: 44px 15px 15px;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.search-input-box {\r\n  flex: 1;\r\n  position: relative;\r\n  margin-right: 15px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  height: 40px;\r\n  background: #F5F7FA;\r\n  border-radius: 20px;\r\n  padding: 0 18px;\r\n  font-size: 15px;\r\n  color: #333;\r\n  border: 1px solid rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.clear-btn {\r\n  position: absolute;\r\n  right: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 18px;\r\n  height: 18px;\r\n  border-radius: 9px;\r\n  background: #C7C7CC;\r\n  color: #FFFFFF;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.cancel-btn {\r\n  font-size: 15px;\r\n  color: #2980b9;\r\n  font-weight: 500;\r\n  padding: 8px 12px;\r\n  border-radius: 16px;\r\n}\r\n\r\n.cancel-btn:active {\r\n  background: rgba(41, 128, 185, 0.1);\r\n}\r\n\r\n/* 记录详情弹窗样式 */\r\n.detail-popup {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  backdrop-filter: blur(5px);\r\n  -webkit-backdrop-filter: blur(5px);\r\n  z-index: 999;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.popup-content {\r\n  width: 85%;\r\n  background: #fff;\r\n  border-radius: 20px;\r\n  overflow: hidden;\r\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 18px 20px;\r\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n  background: #f8f8f8;\r\n}\r\n\r\n.popup-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.popup-close {\r\n  font-size: 22px;\r\n  color: #777;\r\n  width: 36px;\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 18px;\r\n}\r\n\r\n.popup-close:active {\r\n  background: rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.detail-info {\r\n  padding: 20px;\r\n  position: relative;\r\n}\r\n\r\n.detail-status {\r\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n  font-size: 15px;\r\n  font-weight: 600;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.detail-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.detail-label {\r\n  width: 90px;\r\n  font-size: 15px;\r\n  color: #777;\r\n}\r\n\r\n.detail-value {\r\n  flex: 1;\r\n  font-size: 15px;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.close-btn {\r\n  width: 100%;\r\n  height: 56px;\r\n  line-height: 56px;\r\n  text-align: center;\r\n  font-size: 17px;\r\n  font-weight: 500;\r\n  color: #2980b9;\r\n  border: none;\r\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\r\n  border-radius: 0;\r\n  background: #fff;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/verification/records.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAkIA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY,CAAC,MAAM,MAAM,OAAO,IAAI;AAAA,MACpC,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,eAAe,CAAE;AAAA,MACjB,SAAS;AAAA,MACT,SAAS;AAAA,QACP;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,kBAAkB;AAChB,UAAI,SAAS,CAAC,GAAG,KAAK,OAAO;AAG7B,UAAI,KAAK,aAAa,GAAG;AACvB,cAAM,UAAU,KAAK,WAAW,KAAK,UAAU;AAC/C,iBAAS,OAAO,OAAO,YAAU,OAAO,aAAa,OAAO;AAAA,MAC9D;AAGA,UAAI,KAAK,eAAe;AACtB,cAAM,UAAU,KAAK,cAAc,YAAW;AAC9C,iBAAS,OAAO;AAAA,UAAO,YACrB,OAAO,MAAM,cAAc,SAAS,OAAO,KAC3C,OAAO,KAAK,cAAc,SAAS,OAAO,KAC1C,OAAO,KAAK,cAAc,SAAS,OAAO;AAAA;MAE9C;AAEA,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,UAAU,OAAO;AACf,WAAK,aAAa;AAAA,IACnB;AAAA,IACD,iBAAiB;AACfA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,MAAM,MAAM,QAAQ,SAAS,IAAI;AAAA,QAC5C,SAAS,CAAC,QAAQ;AAChB,gBAAM,cAAc,CAAC,MAAM,MAAM,QAAQ,SAAS,IAAI;AACtD,eAAK,YAAY,YAAY,IAAI,QAAQ;AAEzCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM,KAAK,SAAS;AAAA,YAC3B,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,aAAa;AACX,WAAK,kBAAkB;AAAA,IACxB;AAAA,IACD,aAAa;AACX,WAAK,kBAAkB;AACvB,WAAK,gBAAgB;AAAA,IACtB;AAAA,IACD,cAAc;AACZ,WAAK,gBAAgB;AAAA,IACtB;AAAA,IACD,WAAW;AACT,WAAK,kBAAkB;AAGvB,UAAI,KAAK,gBAAgB,WAAW,GAAG;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IACD,iBAAiB,QAAQ;AACvB,WAAK,gBAAgB;AACrB,WAAK,kBAAkB;AAAA,IACxB;AAAA,IACD,mBAAmB;AACjB,WAAK,kBAAkB;AAAA,IACxB;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,cAAM,cAAc;AAAA,UAClB;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,WAAW;AAAA,YACX,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,UAAU;AAAA,UACX;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,WAAW;AAAA,YACX,OAAO;AAAA,YACP,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,UAAU;AAAA,UACZ;AAAA;AAGF,aAAK,UAAU,CAAC,GAAG,KAAK,SAAS,GAAG,WAAW;AAG/C,aAAK,UAAU;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvVA,GAAG,WAAW,eAAe;"}