/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.published-list-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F5F7FA;
}

/* 导航栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #0A84FF;
}
.header-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}
.left-action, .right-action {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 24px;
  height: 24px;
}
.back-icon {
  width: 24px;
  height: 24px;
  /* 图标是黑色的，需要转为白色 */
  filter: brightness(0) invert(1);
}
.title-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.plus-icon {
  width: 24px;
  height: 24px;
  /* 确保图标可见 */
  filter: brightness(0) invert(1);
}

/* 状态标签栏 */
.status-tabs {
  width: 100%;
  height: 44px;
  background-color: #FFFFFF;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: fixed;
  left: 0;
  right: 0;
  z-index: 99;
  white-space: nowrap;
}
.tabs-container {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 12px;
}
.tab-item {
  position: relative;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.tab-text {
  font-size: 15px;
  font-weight: 500;
  color: #64748B;
  transition: all 0.3s;
}
.tab-count {
  font-size: 13px;
  color: #94A3B8;
  margin-left: 3px;
}
.tab-item.active .tab-text {
  color: #0A84FF;
  font-weight: 600;
}
.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background-color: #0A84FF;
  border-radius: 1.5px;
}

/* 列表内容区域 */
.list-scroll {
  flex: 1;
  width: 100%;
}

/* 行程卡片 */
.trip-card {
  background: #FFFFFF;
  border-radius: 8px;
  margin: 10px 16px;
  padding: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}
.trip-card.is-topped {
  background: linear-gradient(to right, #FFFFFF, #F0F9FF);
  border-left: 3px solid #007AFF;
}
.trip-card:active {
  transform: scale(0.98);
}

/* 路线信息 */
.trip-route {
  margin-bottom: 12px;
}
.route-points {
  position: relative;
  margin-bottom: 10px;
  padding-left: 30px;
  min-height: 60px;
}
.route-line-container {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.point-marker {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  z-index: 2;
}
.point-marker.start {
  background-color: #10B981;
}
.point-marker.end {
  background-color: #EF4444;
  margin-top: auto;
}
.route-line {
  position: absolute;
  left: 50%;
  top: 10px;
  bottom: 10px;
  width: 1px;
  background-color: #E5E7EB;
  transform: translateX(-50%);
}
.route-text {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
.start-point, .end-point {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}
.point-text {
  font-size: 15px;
  color: #1F2937;
  font-weight: 500;
}
.trip-meta {
  display: flex;
  padding: 10px;
  background-color: #F9FAFB;
  border-radius: 6px;
  align-items: center;
}
.meta-left {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}
.time-text {
  font-size: 13px;
  color: #4B5563;
}
.seat-text {
  font-size: 13px;
  color: #4B5563;
  padding: 2px 6px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
.status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}
.status-badge.pending {
  background-color: #FFF7ED;
  color: #F59E0B;
}
.status-badge.ongoing {
  background-color: #EFF6FF;
  color: #3B82F6;
}
.status-badge.completed {
  background-color: #ECFDF5;
  color: #10B981;
}
.status-badge.canceled {
  background-color: #F3F4F6;
  color: #6B7280;
}
.top-badge {
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #F0F9FF;
}
.top-badge text {
  font-size: 12px;
  color: #0284C7;
}
.meta-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.price-value {
  font-size: 16px;
  font-weight: 600;
  color: #EF4444;
}

/* 操作按钮区域 */
.action-bar {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 12px;
}
.action-btn {
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
  border: none;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.action-btn:nth-child(1) {
  background: linear-gradient(135deg, #0acffe, #495aff);
}
.action-btn:nth-child(2) {
  background: linear-gradient(135deg, #ff6a88, #ff99ac);
}
.action-btn:nth-child(3) {
  background: linear-gradient(135deg, #b275ff, #7c4dff);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}
.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 24px;
}
.empty-text {
  font-size: 15px;
  color: #6B7280;
  margin-bottom: 24px;
}
.publish-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  background-color: #007AFF;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}
.publish-btn text {
  font-size: 15px;
  color: #FFFFFF;
  margin-right: 8px;
  text-align: center;
}
.arrow-icon {
  width: 16px;
  height: 16px;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
}
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #E5E7EB;
  border-top-color: #007AFF;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 0.8s linear infinite;
}
@keyframes spin {
to {
    transform: rotate(360deg);
}
}
.loading-more text {
  font-size: 13px;
  color: #6B7280;
}
.no-more {
  text-align: center;
  padding: 16px 0;
}
.no-more text {
  font-size: 13px;
  color: #9CA3AF;
}

/* 自定义弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  transition: all 0.3s;
}
.action-sheet {
  background-color: #FFFFFF;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  margin: 0 12px 12px;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}
.sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F3F4F6;
}
.sheet-title {
  font-size: 16px;
  font-weight: 600;
  color: #1F2937;
}
.close-btn {
  padding: 4px;
}
.close-icon {
  width: 20px;
  height: 20px;
  opacity: 0.6;
}
.sheet-content {
  padding: 8px 0;
}
.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F3F4F6;
}
.option-item:last-child {
  border-bottom: none;
}
.option-left {
  display: flex;
  align-items: center;
}
.option-icon {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}
.option-info {
  display: flex;
  flex-direction: column;
}
.option-title {
  font-size: 16px;
  color: #1F2937;
}
.option-desc {
  font-size: 13px;
  color: #6B7280;
  margin-top: 4px;
}
.option-price {
  font-size: 16px;
  font-weight: 600;
  color: #EF4444;
}
.option-price.free {
  color: #10B981;
}
.sheet-footer {
  padding: 16px;
  border-top: 1px solid #F3F4F6;
}
.cancel-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #F9FAFB;
  border: none;
  font-size: 16px;
  color: #4B5563;
  border-radius: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 更多操作弹窗样式 */
.sheet-content .option-item {
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}
.sheet-content .option-item .option-title {
  font-size: 16px;
  font-weight: normal;
  color: #333;
}

/* 推广弹窗底部取消按钮 */
.promotion-sheet .sheet-footer {
  padding: 16px 20px 25px;
}
.promotion-sheet .sheet-footer .cancel-btn {
  background: linear-gradient(to right, #f8f9fa, #ffffff, #f8f9fa);
  border: 1px solid #E5E7EB;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  height: 44px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}
.promotion-sheet .sheet-footer .cancel-btn:active {
  transform: scale(0.98);
  background: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 推广内容区域样式 */
.promotion-content {
  padding: 10px 15px;
}
.promotion-card {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}
.promotion-card::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 100%);
  z-index: 1;
}
.promotion-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: rgba(18, 150, 219, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(18, 150, 219, 0.15);
}
.refresh-icon {
  background-color: rgba(99, 102, 241, 0.2);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.15);
}
.promotion-icon .icon {
  width: 28px;
  height: 28px;
}
.promotion-info {
  flex: 1;
}
.promotion-title {
  font-size: 16px;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 6px;
}
.promotion-desc {
  font-size: 13px;
  color: #6B7280;
  margin-bottom: 6px;
}
.promotion-tag {
  display: flex;
  gap: 5px;
}
.tag-item {
  display: inline-block;
  padding: 2px 6px;
  font-size: 10px;
  color: #FFFFFF;
  border-radius: 4px;
}
.hot {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
}
.promotion-action {
  margin-left: 10px;
  flex-shrink: 0;
}
.promotion-action .action-btn {
  padding: 6px 12px;
  background: linear-gradient(135deg, #1296DB, #0074CC);
  border-radius: 15px;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
.refresh-btn {
  background: linear-gradient(135deg, #6366F1, #4F46E5);
}
.promotion-sheet {
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

/* 标签渐变效果 */
.tag-item.hot {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

/* 按钮悬浮效果 */
.action-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 统一推广组件容器 */
.unified-promotion-container {
  padding: 10px 15px;
}

/* 紧凑卡片模式下的样式调整 */
 .compact-card {
  margin: 0;
}