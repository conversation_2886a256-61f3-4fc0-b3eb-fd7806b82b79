/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body.data-v-8e015a84, html.data-v-8e015a84, #app.data-v-8e015a84, .index-container.data-v-8e015a84 {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.shop-card.data-v-8e015a84 {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.shop-card.data-v-8e015a84:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.shop-cover-container.data-v-8e015a84 {
  width: 100%;
  height: 200rpx;
  position: relative;
}
.shop-cover-container .shop-cover.data-v-8e015a84 {
  width: 100%;
  height: 100%;
}
.shop-cover-container .shop-logo-container.data-v-8e015a84 {
  position: absolute;
  bottom: -30rpx;
  left: 30rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background-color: #FFFFFF;
  padding: 4rpx;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.shop-cover-container .shop-logo-container .shop-logo.data-v-8e015a84 {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.shop-info.data-v-8e015a84 {
  padding: 40rpx 30rpx 30rpx;
}
.shop-info .shop-header.data-v-8e015a84 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.shop-info .shop-header .shop-name.data-v-8e015a84 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.shop-info .shop-header .shop-rating.data-v-8e015a84 {
  display: flex;
  align-items: center;
}
.shop-info .shop-header .shop-rating .rating-stars.data-v-8e015a84 {
  display: flex;
  margin-right: 8rpx;
}
.shop-info .shop-header .shop-rating .rating-stars .star-item.data-v-8e015a84 {
  color: #E5E5EA;
  margin-right: 2rpx;
}
.shop-info .shop-header .shop-rating .rating-stars .star-item.active.data-v-8e015a84 {
  color: #FF9500;
}
.shop-info .shop-header .shop-rating .rating-value.data-v-8e015a84 {
  font-size: 24rpx;
  color: #FF9500;
  font-weight: 500;
}
.shop-info .shop-meta.data-v-8e015a84 {
  display: flex;
  margin-bottom: 16rpx;
}
.shop-info .shop-meta .meta-item.data-v-8e015a84 {
  display: flex;
  align-items: center;
  margin-right: 24rpx;
}
.shop-info .shop-meta .meta-item .meta-icon.data-v-8e015a84 {
  margin-right: 4rpx;
}
.shop-info .shop-meta .meta-item .meta-text.data-v-8e015a84 {
  font-size: 24rpx;
  color: #8E8E93;
}
.shop-info .shop-tags.data-v-8e015a84 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.shop-info .shop-tags .tag-item.data-v-8e015a84 {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
  background-color: rgba(255, 59, 105, 0.1);
  color: #FF3B69;
}
.shop-info .shop-description.data-v-8e015a84 {
  font-size: 24rpx;
  color: #8E8E93;
  line-height: 1.4;
}
.enter-shop-btn.data-v-8e015a84 {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
  display: flex;
  align-items: center;
  box-shadow: 0 4px 8px rgba(255, 59, 105, 0.3);
}
.enter-shop-btn text.data-v-8e015a84 {
  font-size: 24rpx;
  color: #FFFFFF;
}
.enter-shop-btn .enter-icon.data-v-8e015a84 {
  color: #FFFFFF;
}
.enter-shop-btn.data-v-8e015a84:active {
  transform: scale(0.9);
}