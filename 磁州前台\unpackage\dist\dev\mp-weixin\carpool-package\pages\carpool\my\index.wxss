/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.carpool-my-container {
  min-height: 100vh;
  background-color: #F5F8FC;
  position: relative;
  padding-top: calc(var(--status-bar-height) + 90rpx);
  /* 只考虑标题栏高度 */
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom, 0));
}

/* 自定义标题栏模块 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #1677FF;
  /* 恢复为实色背景 */
  z-index: 103;
  box-shadow: none;
}

/* 标题栏内容 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  position: relative;
  z-index: 102;
}
.left-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title-area {
  flex: 1;
  text-align: center;
}
.page-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.right-action {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}

/* 用户信息卡片 - 简化版 */
.user-card {
  margin: 60rpx 32rpx 20rpx;
  border-radius: 20rpx;
  background-color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 24rpx;
}
.user-avatar-container {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 12rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 2rpx solid #F2F2F7;
}
.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50% !important;
  object-fit: cover;
}
.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
}
.user-id {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 6rpx;
}
.user-credit {
  font-size: 24rpx;
  color: #8E8E93;
  margin-bottom: 20rpx;
}
.credit-score {
  color: #FF9F0A;
  font-weight: 500;
}
.user-actions {
  display: flex;
  width: 100%;
  border-top: 1px solid #F2F2F7;
  padding-top: 20rpx;
  margin-top: 4rpx;
}
.action-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.action-divider {
  width: 1px;
  height: 32rpx;
  background-color: #F2F2F7;
}
.action-text {
  font-size: 28rpx;
  color: #666666;
}

/* 统计数据卡片 */
.stats-card {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 24rpx 0;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-value {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.stat-label {
  font-size: 24rpx;
  color: #8E8E93;
}

/* 功能区块 */
.feature-section {
  margin: 0 32rpx 32rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.section-header {
  margin-bottom: 24rpx;
}
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  padding-left: 16rpx;
}
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 6rpx;
  bottom: 6rpx;
  width: 4rpx;
  background: linear-gradient(to bottom, #0A84FF, #5AC8FA);
  border-radius: 2rpx;
}
.feature-grid {
  display: flex;
  flex-wrap: wrap;
}
.feature-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
}
.feature-icon-wrapper {
  width: 88rpx;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 14rpx;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
}
.blue {
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
}
.orange {
  background: linear-gradient(135deg, #FF9F0A, #FF2D55);
}
.green {
  background: linear-gradient(135deg, #30D158, #34C759);
}
.gray {
  background: linear-gradient(135deg, #8E8E93, #636366);
}
.purple {
  background: linear-gradient(135deg, #BF5AF2, #A34FC9);
}
.red {
  background: linear-gradient(135deg, #FF2D55, #FF2D55);
}
.feature-icon {
  width: 44rpx;
  height: 44rpx;
  filter: brightness(0) invert(1);
}
.feature-text {
  font-size: 26rpx;
  color: #666666;
}
.badge {
  position: absolute;
  top: -8rpx;
  right: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #FF2D55;
  color: #ffffff;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 45, 85, 0.3);
}
.hot-badge {
  background-color: #FF2D55;
  color: #ffffff;
  padding: 0 8rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

/* 列表样式 */
.feature-list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
}
.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  position: relative;
}
.list-item:not(:last-child) {
  border-bottom: 1px solid #F2F2F7;
}
.item-left {
  display: flex;
  align-items: center;
}
.item-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}
.item-icon {
  width: 36rpx;
  height: 36rpx;
  filter: brightness(0) invert(1);
}
.item-text {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}
.item-right {
  display: flex;
  align-items: center;
}
.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
}
.notification-badge {
  background-color: #FF3B30;
  color: #FFFFFF;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  margin-right: 10rpx;
}
.verification-badge {
  background-color: #34C759;
  color: #FFFFFF;
  font-size: 20rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  margin-right: 10rpx;
}

/* 底部导航栏 */
.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 110rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 9999;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  position: relative;
  transition: all 0.2s ease;
}
.tabbar-item:active {
  opacity: 0.7;
}
.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}
.tabbar-text {
  font-size: 22rpx;
  color: #999999;
  line-height: 1;
}
.active-text {
  color: #0A84FF;
  font-weight: 500;
}
.tabbar-item.active .tabbar-icon {
  transform: scale(1.1);
}
.safe-area-bottom {
  height: calc(env(safe-area-inset-bottom) + 30rpx);
  width: 100%;
}

/* 测试按钮样式 */
.test-button {
  margin: 0 32rpx 20rpx;
  background: linear-gradient(135deg, #0A84FF, #5AC8FA);
  border-radius: 45rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(10, 132, 255, 0.3);
}
.test-button-text {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
}