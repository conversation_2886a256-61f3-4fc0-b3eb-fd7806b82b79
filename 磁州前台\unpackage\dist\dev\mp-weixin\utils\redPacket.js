"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("./request.js");
const RED_PACKET_TYPE = {
  NORMAL: "normal",
  // 普通红包
  LUCKY: "lucky",
  // 拼手气红包
  FIXED: "fixed",
  // 固定金额红包
  CONSUME: "consume"
  // 消费满额红包
};
async function getMyRedPackets(params) {
  try {
    const res = await utils_request.request({
      url: "/api/redpacket/my",
      method: "GET",
      data: params
    });
    return res.data;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/redPacket.js:69", "获取红包列表失败:", error);
    throw error;
  }
}
async function grabRedPacket(id) {
  try {
    const res = await utils_request.request({
      url: `/api/redpacket/${id}/grab`,
      method: "POST"
    });
    return res.data;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/redPacket.js:105", "抢红包失败:", error);
    throw error;
  }
}
async function createRedPacket(data) {
  try {
    const res = await utils_request.request({
      url: "/api/redpacket",
      method: "POST",
      data
    });
    return res.data;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/redPacket.js:124", "创建红包失败:", error);
    throw error;
  }
}
function formatRedPacketAmount(amount) {
  if (typeof amount !== "number")
    return "0.00";
  return (amount / 100).toFixed(2);
}
exports.RED_PACKET_TYPE = RED_PACKET_TYPE;
exports.createRedPacket = createRedPacket;
exports.formatRedPacketAmount = formatRedPacketAmount;
exports.getMyRedPackets = getMyRedPackets;
exports.grabRedPacket = grabRedPacket;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/redPacket.js.map
