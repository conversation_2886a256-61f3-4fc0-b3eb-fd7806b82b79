{"name": "同城前端", "appid": "__UNI__BCAC02A", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Payment": {}, "Pasteboard": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.CLIPBOARD_READ\" />", "<uses-permission android:name=\"android.permission.CLIPBOARD_WRITE\" />"], "permissionPhoneSettings": {"request": "none"}, "permissionExternalStorage": {"request": "none"}, "permissionPhoneState": {"request": "none"}, "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "ios": {"privacyDescription": {"NSPasteboardUsageDescription": "应用需要访问剪贴板以便为您提供商品链接识别功能"}}, "sdkConfigs": {"payment": {"weixin": {"appid": "wx8e2036f9b26c6488", "UniversalLinks": "https://your-domain.com/app-open"}}}}}, "quickapp": {}, "mp-weixin": {"appid": "wx8e2036f9b26c6488", "setting": {"urlCheck": false, "packNpmManually": true, "packNpmRelationList": [], "minified": true, "es6": true, "postcss": true, "minifyWXSS": true, "minifyWXML": true}, "optimization": {"subPackages": true}, "usingComponents": true, "requiredPrivateInfos": ["getLocation"], "permission": {"scope.userLocation": {"desc": "获取您的位置信息用于为您提供更好的同城服务"}}, "lazyCodeLoading": "requiredComponents", "packOptions": {"ignore": [], "include": []}}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "networkTimeout": {"request": 60000, "connectSocket": 60000, "uploadFile": 60000, "downloadFile": 60000}, "subPackages": true, "subPackageLimit": 4000}