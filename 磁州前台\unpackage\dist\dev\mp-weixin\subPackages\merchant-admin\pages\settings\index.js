"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {};
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({ url });
    },
    showHelp() {
      common_vendor.index.showToast({
        title: "帮助中心功能开发中",
        icon: "none"
      });
    }
  }
};
if (!Array) {
  const _component_rect = common_vendor.resolveComponent("rect");
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_line = common_vendor.resolveComponent("line");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_circle = common_vendor.resolveComponent("circle");
  const _component_polygon = common_vendor.resolveComponent("polygon");
  (_component_rect + _component_path + _component_svg + _component_line + _component_polyline + _component_circle + _component_polygon)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    c: common_vendor.p({
      x: "3",
      y: "11",
      width: "18",
      height: "11",
      rx: "2",
      ry: "2"
    }),
    d: common_vendor.p({
      d: "M7 11V7a5 5 0 0110 0v4"
    }),
    e: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    f: common_vendor.o(($event) => $options.navigateTo("./security/password")),
    g: common_vendor.p({
      x: "5",
      y: "2",
      width: "14",
      height: "20",
      rx: "2",
      ry: "2"
    }),
    h: common_vendor.p({
      x1: "12",
      y1: "18",
      x2: "12",
      y2: "18"
    }),
    i: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    j: common_vendor.o(($event) => $options.navigateTo("./security/devices")),
    k: common_vendor.p({
      d: "M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"
    }),
    l: common_vendor.p({
      points: "14 2 14 8 20 8"
    }),
    m: common_vendor.p({
      x1: "16",
      y1: "13",
      x2: "8",
      y2: "13"
    }),
    n: common_vendor.p({
      x1: "16",
      y1: "17",
      x2: "8",
      y2: "17"
    }),
    o: common_vendor.p({
      points: "10 9 9 9 8 9"
    }),
    p: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    q: common_vendor.o(($event) => $options.navigateTo("./security/logs")),
    r: common_vendor.p({
      d: "M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"
    }),
    s: common_vendor.p({
      cx: "9",
      cy: "7",
      r: "4"
    }),
    t: common_vendor.p({
      d: "M23 21v-2a4 4 0 00-3-3.87"
    }),
    v: common_vendor.p({
      d: "M16 3.13a4 4 0 010 7.75"
    }),
    w: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    x: common_vendor.o(($event) => $options.navigateTo("./staff/roles")),
    y: common_vendor.p({
      d: "M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"
    }),
    z: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    A: common_vendor.o(($event) => $options.navigateTo("./staff/permissions")),
    B: common_vendor.p({
      d: "M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"
    }),
    C: common_vendor.p({
      cx: "12",
      cy: "7",
      r: "4"
    }),
    D: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    E: common_vendor.o(($event) => $options.navigateTo("./staff/users")),
    F: common_vendor.p({
      d: "M22 16.92v3a2 2 0 01-2.18 2 19.79 19.79 0 01-8.63-3.07 19.5 19.5 0 01-6-6 19.79 19.79 0 01-3.07-8.67A2 2 0 014.11 2h3a2 2 0 012 1.72 12.84 12.84 0 00.7 2.81 2 2 0 01-.45 2.11L8.09 9.91a16 16 0 006 6l1.27-1.27a2 2 0 012.11-.45 12.84 12.84 0 002.81.7A2 2 0 0122 16.92z"
    }),
    G: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    H: common_vendor.o(($event) => $options.navigateTo("./notification/channels")),
    I: common_vendor.p({
      cx: "12",
      cy: "12",
      r: "10"
    }),
    J: common_vendor.p({
      x1: "12",
      y1: "8",
      x2: "12",
      y2: "16"
    }),
    K: common_vendor.p({
      x1: "8",
      y1: "12",
      x2: "16",
      y2: "12"
    }),
    L: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    M: common_vendor.o(($event) => $options.navigateTo("./notification/types")),
    N: common_vendor.p({
      d: "M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"
    }),
    O: common_vendor.p({
      points: "14 2 14 8 20 8"
    }),
    P: common_vendor.p({
      x1: "16",
      y1: "13",
      x2: "8",
      y2: "13"
    }),
    Q: common_vendor.p({
      x1: "16",
      y1: "17",
      x2: "8",
      y2: "17"
    }),
    R: common_vendor.p({
      points: "10 9 9 9 8 9"
    }),
    S: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    T: common_vendor.o(($event) => $options.navigateTo("./notification/templates")),
    U: common_vendor.p({
      points: "6 9 6 2 18 2 18 9"
    }),
    V: common_vendor.p({
      d: "M6 18H4a2 2 0 01-2-2v-5a2 2 0 012-2h16a2 2 0 012 2v5a2 2 0 01-2 2h-2"
    }),
    W: common_vendor.p({
      x: "6",
      y: "14",
      width: "12",
      height: "8"
    }),
    X: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    Y: common_vendor.o(($event) => $options.navigateTo("./integration/printer")),
    Z: common_vendor.p({
      x: "1",
      y: "3",
      width: "15",
      height: "13"
    }),
    aa: common_vendor.p({
      points: "16 8 20 8 23 11 23 16 16 16 16 8"
    }),
    ab: common_vendor.p({
      cx: "5.5",
      cy: "18.5",
      r: "2.5"
    }),
    ac: common_vendor.p({
      cx: "18.5",
      cy: "18.5",
      r: "2.5"
    }),
    ad: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    ae: common_vendor.o(($event) => $options.navigateTo("./integration/logistics")),
    af: common_vendor.p({
      x: "2",
      y: "4",
      width: "20",
      height: "16",
      rx: "2",
      ry: "2"
    }),
    ag: common_vendor.p({
      x1: "2",
      y1: "10",
      x2: "22",
      y2: "10"
    }),
    ah: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    ai: common_vendor.o(($event) => $options.navigateTo("./integration/payment")),
    aj: common_vendor.p({
      x: "3",
      y: "3",
      width: "7",
      height: "7"
    }),
    ak: common_vendor.p({
      x: "14",
      y: "3",
      width: "7",
      height: "7"
    }),
    al: common_vendor.p({
      x: "14",
      y: "14",
      width: "7",
      height: "7"
    }),
    am: common_vendor.p({
      x: "3",
      y: "14",
      width: "7",
      height: "7"
    }),
    an: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2"
    }),
    ao: common_vendor.o(($event) => $options.navigateTo("./integration/apps"))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin/pages/settings/index.js.map
