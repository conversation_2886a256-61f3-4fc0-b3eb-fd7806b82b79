<template>
  <view class="reviews-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-bg"></view>
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <svg class="icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </view>
        <view class="navbar-title">商品评价</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view 
      class="content-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
    >
      <!-- 商品信息 -->
      <view class="product-info">
        <image class="product-image" :src="product.image" mode="aspectFill"></image>
        <view class="product-detail">
          <view class="product-name">{{ product.name }}</view>
          <view class="product-price">¥{{ product.price.toFixed(2) }}</view>
        </view>
      </view>
      
      <!-- 评价统计 -->
      <view class="review-stats">
        <view class="stats-header">
          <view class="stats-title">商品评价({{ totalReviews }})</view>
          <view class="positive-rate">好评率 {{ positiveRate }}%</view>
        </view>
        
        <view class="tag-list">
          <view 
            class="tag-item" 
            :class="{ active: activeTag === tag.id }" 
            v-for="tag in reviewTags" 
            :key="tag.id"
            @click="selectTag(tag.id)"
          >
            {{ tag.name }}({{ tag.count }})
          </view>
        </view>
      </view>
      
      <!-- 评价列表 -->
      <view class="review-list" v-if="reviews.length > 0">
        <view class="review-item" v-for="(review, index) in reviews" :key="index">
          <view class="review-header">
            <image class="user-avatar" :src="review.userAvatar" mode="aspectFill"></image>
            <view class="user-info">
              <view class="user-name">{{ review.userName }}</view>
              <view class="review-time">{{ review.time }}</view>
            </view>
            <view class="rating">
              <view 
                class="star" 
                v-for="i in 5" 
                :key="i" 
                :class="{ filled: i <= review.rating }"
              ></view>
            </view>
          </view>
          
          <view class="review-content">{{ review.content }}</view>
          
          <view class="review-images" v-if="review.images && review.images.length > 0">
            <image 
              class="review-image" 
              v-for="(image, imgIndex) in review.images" 
              :key="imgIndex" 
              :src="image" 
              mode="aspectFill"
              @click="previewImage(review.images, imgIndex)"
            ></image>
          </view>
          
          <view class="review-specs" v-if="review.specs">{{ review.specs }}</view>
          
          <view class="shop-reply" v-if="review.shopReply">
            <view class="reply-title">商家回复：</view>
            <view class="reply-content">{{ review.shopReply }}</view>
          </view>
          
          <view class="review-actions">
            <view class="action-item" @click="toggleLike(review)">
              <svg class="icon" viewBox="0 0 24 24" width="16" height="16" :class="{ active: review.isLiked }">
                <path d="M14 9V5a3 3 0 00-3-3l-4 9v11h11.28a2 2 0 002-1.7l1.38-9a2 2 0 00-2-2.3H14zM7 22H4a2 2 0 01-2-2v-7a2 2 0 012-2h3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
              <text>{{ review.likes || 0 }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空评价 -->
      <view class="empty-reviews" v-else>
        <image class="empty-icon" src="/static/images/empty-reviews.png" mode="aspectFit"></image>
        <view class="empty-text">暂无相关评价</view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 商品ID
const productId = ref('');

// 商品信息
const product = ref({
  id: '',
  name: '商品加载中...',
  price: 0,
  image: 'https://via.placeholder.com/300x300'
});

// 评价标签
const reviewTags = ref([
  { id: 'all', name: '全部', count: 235 },
  { id: 'positive', name: '好评', count: 220 },
  { id: 'neutral', name: '中评', count: 10 },
  { id: 'negative', name: '差评', count: 5 },
  { id: 'hasImage', name: '有图', count: 120 },
  { id: 'hasVideo', name: '有视频', count: 30 }
]);

// 当前选中的标签
const activeTag = ref('all');

// 评价列表
const reviews = ref([]);

// 评价总数
const totalReviews = computed(() => {
  return reviewTags.value.find(tag => tag.id === 'all')?.count || 0;
});

// 好评率
const positiveRate = computed(() => {
  const positive = reviewTags.value.find(tag => tag.id === 'positive')?.count || 0;
  const total = totalReviews.value;
  return total > 0 ? Math.round((positive / total) * 100) : 100;
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 选择标签
const selectTag = (tagId) => {
  activeTag.value = tagId;
  loadReviews();
};

// 切换点赞状态
const toggleLike = (review) => {
  review.isLiked = !review.isLiked;
  if (review.isLiked) {
    review.likes = (review.likes || 0) + 1;
  } else {
    review.likes = Math.max(0, (review.likes || 0) - 1);
  }
};

// 预览图片
const previewImage = (images, current) => {
  uni.previewImage({
    urls: images,
    current: images[current]
  });
};

// 加载更多
const loadMore = () => {
  // 模拟加载更多数据
  uni.showToast({
    title: '已加载全部评价',
    icon: 'none'
  });
};

// 加载评价
const loadReviews = () => {
  // 模拟评价数据
  reviews.value = [
    {
      id: '1',
      userName: '用户***123',
      userAvatar: 'https://via.placeholder.com/100',
      rating: 5,
      content: '手机质量很好，拍照效果出色，电池续航也不错，总体来说非常满意！屏幕显示效果特别好，色彩鲜艳，动态岛设计很有创意。',
      time: '2023-09-15',
      specs: '颜色：深空黑；内存：256G',
      images: [
        'https://via.placeholder.com/300x300',
        'https://via.placeholder.com/300x300',
        'https://via.placeholder.com/300x300'
      ],
      likes: 12,
      isLiked: false
    },
    {
      id: '2',
      userName: '张**',
      userAvatar: 'https://via.placeholder.com/100',
      rating: 4,
      content: '手机整体不错，就是电池续航一般，重度使用一天需要充两次电。相机效果很棒，尤其是夜景模式表现优秀。',
      time: '2023-09-10',
      specs: '颜色：银色；内存：256G',
      images: [
        'https://via.placeholder.com/300x300',
        'https://via.placeholder.com/300x300'
      ],
      shopReply: '感谢您的评价！关于电池续航问题，建议您检查是否有后台应用过度耗电，或者可以尝试更新到最新系统版本，这可能会优化电池性能。如有其他问题，欢迎随时联系我们的客服。',
      likes: 8,
      isLiked: false
    },
    {
      id: '3',
      userName: '李**',
      userAvatar: 'https://via.placeholder.com/100',
      rating: 5,
      content: '物流超快，第二天就收到了。包装很好，手机外观漂亮，系统流畅，总之很满意的一次购物体验！',
      time: '2023-09-05',
      specs: '颜色：紫色；内存：512G',
      likes: 5,
      isLiked: false
    },
    {
      id: '4',
      userName: '王**',
      userAvatar: 'https://via.placeholder.com/100',
      rating: 3,
      content: '手机还行吧，但感觉不值这个价，性能提升不是很明显，还不如用旧手机多用几年。',
      time: '2023-09-01',
      specs: '颜色：深空黑；内存：128G',
      shopReply: '感谢您的反馈。我们的新款手机在处理器、相机系统和屏幕显示等方面都有显著提升。如果您有任何使用问题，欢迎联系我们的售后服务团队，我们将竭诚为您服务。',
      likes: 2,
      isLiked: false
    }
  ];
};

// 获取商品信息
const getProductInfo = (id) => {
  // 模拟获取商品数据
  setTimeout(() => {
    product.value = {
      id: id,
      name: 'iPhone 14 Pro 深空黑 256G',
      price: 7999,
      image: 'https://via.placeholder.com/300x300'
    };
  }, 500);
};

onMounted(() => {
  const query = uni.getSystemInfoSync().platform === 'devtools' 
    ? { id: '1' } // 开发环境模拟数据
    : uni.$u.route.query;
    
  if (query.id) {
    productId.value = query.id;
    getProductInfo(query.id);
  }
  
  loadReviews();
});
</script>

<style lang="scss" scoped>
.reviews-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F2F2F7;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 25px) + 62px);
  width: 100%;
  z-index: 100;
  
  .navbar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(255,59,105,0.15);
  }
  
  .navbar-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 25px);
    box-sizing: border-box;
  }
  
  .navbar-left, .navbar-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
  }
  
  .navbar-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #FFFFFF;
    letter-spacing: 0.5px;
  }
  
  .icon {
    width: 48rpx;
    height: 48rpx;
  }
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height, 25px) + 62px);
}

/* 商品信息 */
.product-info {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
  
  .product-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 8rpx;
  }
  
  .product-detail {
    flex: 1;
    margin-left: 20rpx;
    
    .product-name {
      font-size: 28rpx;
      color: #333333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
    
    .product-price {
      font-size: 28rpx;
      color: #FF3B69;
      font-weight: 600;
      margin-top: 10rpx;
    }
  }
}

/* 评价统计 */
.review-stats {
  margin-top: 20rpx;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
  
  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .stats-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .positive-rate {
      font-size: 28rpx;
      color: #FF3B69;
    }
  }
  
  .tag-list {
    display: flex;
    flex-wrap: wrap;
    
    .tag-item {
      margin-right: 20rpx;
      margin-bottom: 20rpx;
      padding: 10rpx 20rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      color: #666666;
      background-color: #F2F2F7;
      
      &.active {
        color: #FFFFFF;
        background: linear-gradient(135deg, #FF3B69 0%, #FF7A9E 100%);
      }
    }
  }
}

/* 评价列表 */
.review-list {
  margin-top: 20rpx;
  background-color: #FFFFFF;
}

.review-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #F2F2F7;
  
  &:last-child {
    border-bottom: none;
  }
  
  .review-header {
    display: flex;
    align-items: center;
    
    .user-avatar {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
    }
    
    .user-info {
      flex: 1;
      margin-left: 20rpx;
      
      .user-name {
        font-size: 28rpx;
        color: #333333;
      }
      
      .review-time {
        font-size: 24rpx;
        color: #999999;
        margin-top: 4rpx;
      }
    }
    
    .rating {
      display: flex;
      
      .star {
        width: 24rpx;
        height: 24rpx;
        margin-left: 4rpx;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23E0E0E0'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        
        &.filled {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF9500'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
        }
      }
    }
  }
  
  .review-content {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #333333;
    line-height: 1.6;
  }
  
  .review-images {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20rpx;
    
    .review-image {
      width: 160rpx;
      height: 160rpx;
      margin-right: 10rpx;
      margin-bottom: 10rpx;
      border-radius: 8rpx;
    }
  }
  
  .review-specs {
    margin-top: 20rpx;
    font-size: 24rpx;
    color: #999999;
  }
  
  .shop-reply {
    margin-top: 20rpx;
    padding: 20rpx;
    background-color: #F9F9F9;
    border-radius: 8rpx;
    
    .reply-title {
      font-size: 24rpx;
      color: #666666;
      font-weight: 600;
    }
    
    .reply-content {
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #666666;
      line-height: 1.6;
    }
  }
  
  .review-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20rpx;
    
    .action-item {
      display: flex;
      align-items: center;
      padding: 10rpx;
      
      .icon {
        color: #999999;
        margin-right: 6rpx;
        
        &.active {
          color: #FF3B69;
        }
      }
      
      text {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}

/* 空评价 */
.empty-reviews {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: #FFFFFF;
  margin-top: 20rpx;
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 34px; /* iOS 安全区域高度 */
}
</style> 