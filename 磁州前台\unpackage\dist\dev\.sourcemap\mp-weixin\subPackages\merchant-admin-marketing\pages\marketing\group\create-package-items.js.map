{"version": 3, "file": "create-package-items.js", "sources": ["subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNcbWVyY2hhbnQtYWRtaW4tbWFya2V0aW5nXHBhZ2VzXG1hcmtldGluZ1xncm91cFxjcmVhdGUtcGFja2FnZS1pdGVtcy52dWU"], "sourcesContent": ["<!-- 创建中 -->\n<template>\n  <view class=\"create-package-items-container\">\n    <!-- 自定义导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"navbar-back\" @click=\"goBack\">\n        <view class=\"back-icon\"></view>\n      </view>\n      <text class=\"navbar-title\">拼团活动</text>\n      <view class=\"navbar-right\">\n        <view class=\"help-icon\" @click=\"showHelp\">?</view>\n      </view>\n    </view>\n    \n    <!-- 步骤指示器 -->\n    <view class=\"step-indicator\">\n      <view class=\"step-progress\">\n        <view class=\"step-progress-bar\" style=\"width: 80%\"></view>\n      </view>\n      <view class=\"step-text\">步骤 4/5</view>\n    </view>\n    \n    <!-- 页面内容 -->\n    <scroll-view scroll-y class=\"page-content\">\n      <view class=\"page-title\">添加套餐内容</view>\n      <view class=\"page-subtitle\">请添加团购套餐包含的商品或服务</view>\n      \n      <!-- 套餐内容列表 -->\n      <view class=\"package-items\">\n        <view v-for=\"(item, index) in packageItems\" :key=\"index\" class=\"package-item\">\n          <view class=\"item-header\">\n            <text class=\"item-title\">套餐项 {{index + 1}}</text>\n            <view class=\"item-actions\">\n              <view class=\"action-btn edit\" @tap=\"editItem(index)\">编辑</view>\n              <view class=\"action-btn delete\" @tap=\"deleteItem(index)\">删除</view>\n            </view>\n          </view>\n          \n          <view class=\"item-content\">\n            <view class=\"item-info\">\n              <text class=\"info-label\">名称:</text>\n              <text class=\"info-value\">{{item.name}}</text>\n            </view>\n            \n            <view class=\"item-info\">\n              <text class=\"info-label\">数量:</text>\n              <text class=\"info-value\">{{item.quantity}} {{item.unit}}</text>\n            </view>\n            \n            <view class=\"item-info\">\n              <text class=\"info-label\">原价:</text>\n              <text class=\"info-value\">¥{{item.price}}</text>\n            </view>\n            \n            <view class=\"item-info\" v-if=\"item.description\">\n              <text class=\"info-label\">描述:</text>\n              <text class=\"info-value\">{{item.description}}</text>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 添加按钮 -->\n        <view class=\"add-item-btn\" @tap=\"showAddItemModal\">\n          <view class=\"add-icon\">+</view>\n          <text class=\"add-text\">添加套餐项</text>\n        </view>\n      </view>\n      \n      <!-- 套餐总价值预览 -->\n      <view class=\"total-value-preview\">\n        <view class=\"preview-header\">套餐总价值</view>\n        \n        <view class=\"preview-content\">\n          <view class=\"preview-item\">\n            <text class=\"preview-label\">套餐项数量</text>\n            <text class=\"preview-value\">{{packageItems.length}}项</text>\n          </view>\n          \n          <view class=\"preview-item\">\n            <text class=\"preview-label\">套餐原价总值</text>\n            <text class=\"preview-value\">¥{{calculateTotalValue()}}</text>\n          </view>\n          \n          <view class=\"preview-item\">\n            <text class=\"preview-label\">拼团价</text>\n            <text class=\"preview-value group-price\">¥{{groupPrice}}</text>\n          </view>\n          \n          <view class=\"preview-item\">\n            <text class=\"preview-label\">折扣率</text>\n            <text class=\"preview-value discount\">{{calculateDiscount()}}折</text>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 底部按钮 -->\n    <view class=\"footer-buttons\">\n      <button class=\"btn btn-secondary\" @click=\"goBack\">上一步</button>\n      <button class=\"btn btn-primary\" @click=\"nextStep\">下一步</button>\n    </view>\n    \n    <!-- 添加/编辑套餐项弹窗 -->\n    <view class=\"modal\" v-if=\"showModal\">\n      <view class=\"modal-mask\" @tap=\"hideModal\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">{{isEditing ? '编辑套餐项' : '添加套餐项'}}</text>\n          <view class=\"modal-close\" @tap=\"hideModal\">×</view>\n        </view>\n        \n        <view class=\"modal-body\">\n          <view class=\"form-item\">\n            <text class=\"form-label\">名称 <text class=\"required\">*</text></text>\n            <input class=\"form-input\" type=\"text\" v-model=\"currentItem.name\" placeholder=\"请输入套餐项名称\" maxlength=\"30\" />\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">数量 <text class=\"required\">*</text></text>\n            <view class=\"quantity-input\">\n              <input class=\"form-input\" type=\"number\" v-model=\"currentItem.quantity\" placeholder=\"请输入数量\" />\n              <input class=\"unit-input\" type=\"text\" v-model=\"currentItem.unit\" placeholder=\"单位\" maxlength=\"4\" />\n            </view>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">原价 <text class=\"required\">*</text></text>\n            <view class=\"price-input-wrapper\">\n              <text class=\"price-symbol\">¥</text>\n              <input class=\"form-input\" type=\"digit\" v-model=\"currentItem.price\" placeholder=\"请输入原价\" />\n            </view>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">描述</text>\n            <textarea class=\"form-textarea\" v-model=\"currentItem.description\" placeholder=\"请输入套餐项描述\" maxlength=\"100\"></textarea>\n          </view>\n        </view>\n        \n        <view class=\"modal-footer\">\n          <button class=\"modal-btn cancel\" @tap=\"hideModal\">取消</button>\n          <button class=\"modal-btn confirm\" @tap=\"confirmItem\">确定</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      packageItems: [],\n      showModal: false,\n      isEditing: false,\n      editIndex: -1,\n      currentItem: {\n        name: '',\n        quantity: 1,\n        unit: '份',\n        price: '',\n        description: ''\n      },\n      groupPrice: '0.00'\n    }\n  },\n  onLoad() {\n    // 尝试从本地存储获取之前保存的数据\n    try {\n      const savedItems = uni.getStorageSync('packageItems');\n      if (savedItems) {\n        this.packageItems = JSON.parse(savedItems);\n      }\n      \n      const priceInfo = uni.getStorageSync('packagePriceInfo');\n      if (priceInfo) {\n        const parsedPriceInfo = JSON.parse(priceInfo);\n        this.groupPrice = parsedPriceInfo.groupPrice || '0.00';\n      }\n    } catch (e) {\n      console.error('读取本地存储失败:', e);\n    }\n  },\n  methods: {\n    goBack() {\n      // 保存当前页面数据\n      this.saveData();\n      uni.navigateBack();\n    },\n    nextStep() {\n      // 表单验证\n      if (this.packageItems.length === 0) {\n        uni.showToast({\n          title: '请至少添加一个套餐项',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 保存数据并跳转到下一步\n      this.saveData();\n      uni.navigateTo({\n        url: '/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-confirm'\n      });\n    },\n    saveData() {\n      // 保存当前页面数据到本地存储\n      try {\n        uni.setStorageSync('packageItems', JSON.stringify(this.packageItems));\n      } catch (e) {\n        console.error('保存数据失败:', e);\n      }\n    },\n    calculateTotalValue() {\n      let total = 0;\n      this.packageItems.forEach(item => {\n        total += parseFloat(item.price) * parseFloat(item.quantity);\n      });\n      return total.toFixed(2);\n    },\n    calculateDiscount() {\n      const totalValue = parseFloat(this.calculateTotalValue());\n      if (totalValue <= 0) return '10.0';\n      \n      const groupPrice = parseFloat(this.groupPrice);\n      const discount = (groupPrice / totalValue) * 10;\n      return discount.toFixed(1);\n    },\n    showAddItemModal() {\n      this.isEditing = false;\n      this.editIndex = -1;\n      this.currentItem = {\n        name: '',\n        quantity: 1,\n        unit: '份',\n        price: '',\n        description: ''\n      };\n      this.showModal = true;\n    },\n    editItem(index) {\n      this.isEditing = true;\n      this.editIndex = index;\n      this.currentItem = JSON.parse(JSON.stringify(this.packageItems[index]));\n      this.showModal = true;\n    },\n    deleteItem(index) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这个套餐项吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.packageItems.splice(index, 1);\n          }\n        }\n      });\n    },\n    hideModal() {\n      this.showModal = false;\n    },\n    confirmItem() {\n      // 表单验证\n      if (!this.currentItem.name) {\n        uni.showToast({\n          title: '请输入套餐项名称',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.currentItem.quantity || parseFloat(this.currentItem.quantity) <= 0) {\n        uni.showToast({\n          title: '请输入有效数量',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.currentItem.unit) {\n        uni.showToast({\n          title: '请输入单位',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (!this.currentItem.price || parseFloat(this.currentItem.price) <= 0) {\n        uni.showToast({\n          title: '请输入有效原价',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      if (this.isEditing) {\n        // 编辑现有项\n        this.packageItems[this.editIndex] = JSON.parse(JSON.stringify(this.currentItem));\n      } else {\n        // 添加新项\n        this.packageItems.push(JSON.parse(JSON.stringify(this.currentItem)));\n      }\n      \n      this.hideModal();\n    },\n    showHelp() {\n      uni.showToast({\n        title: '帮助信息',\n        icon: 'none'\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.create-package-items-container {\n  min-height: 100vh;\n  background-color: #F5F7FA;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 导航栏样式 */\n.navbar {\n  position: sticky;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #fff;\n  padding: 44px 16px 15px;\n  display: flex;\n  align-items: center;\n  z-index: 100;\n  box-shadow: 0 2px 10px rgba(126, 48, 225, 0.15);\n}\n\n.navbar-back {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.back-icon {\n  width: 12px;\n  height: 12px;\n  border-left: 2px solid #fff;\n  border-bottom: 2px solid #fff;\n  transform: rotate(45deg);\n}\n\n.navbar-title {\n  flex: 1;\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.navbar-right {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.help-icon {\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  border: 1px solid #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #fff;\n}\n\n/* 步骤指示器 */\n.step-indicator {\n  padding: 15px;\n  background: #FFFFFF;\n}\n\n.step-progress {\n  height: 4px;\n  background-color: #EBEDF5;\n  border-radius: 2px;\n  margin-bottom: 5px;\n  position: relative;\n}\n\n.step-progress-bar {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  background: linear-gradient(90deg, #9040FF, #5E35B1);\n  border-radius: 2px;\n}\n\n.step-text {\n  font-size: 12px;\n  color: #999;\n  text-align: right;\n}\n\n/* 页面内容 */\n.page-content {\n  flex: 1;\n  padding: 20px 15px;\n}\n\n.page-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 20px;\n}\n\n/* 套餐项样式 */\n.package-items {\n  margin-bottom: 20px;\n}\n\n.package-item {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 15px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n  padding-bottom: 10px;\n  border-bottom: 1px dashed #EBEDF5;\n}\n\n.item-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.item-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.action-btn {\n  padding: 5px 10px;\n  font-size: 12px;\n  border-radius: 15px;\n}\n\n.action-btn.edit {\n  background: #F0F7FF;\n  color: #007AFF;\n}\n\n.action-btn.delete {\n  background: #FFF0F0;\n  color: #FF3B30;\n}\n\n.item-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.item-info {\n  display: flex;\n  align-items: flex-start;\n}\n\n.info-label {\n  width: 60px;\n  font-size: 14px;\n  color: #666;\n}\n\n.info-value {\n  flex: 1;\n  font-size: 14px;\n  color: #333;\n}\n\n.add-item-btn {\n  background: #FFFFFF;\n  border: 1px dashed #CCCCCC;\n  border-radius: 12px;\n  padding: 15px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-icon {\n  width: 30px;\n  height: 30px;\n  background: #F5F7FA;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  color: #9040FF;\n  margin-bottom: 5px;\n}\n\n.add-text {\n  font-size: 14px;\n  color: #9040FF;\n}\n\n/* 总价值预览 */\n.total-value-preview {\n  background: #FFFFFF;\n  border-radius: 12px;\n  padding: 15px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.preview-header {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px dashed #EBEDF5;\n}\n\n.preview-content {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.preview-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.preview-label {\n  font-size: 14px;\n  color: #666;\n}\n\n.preview-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.preview-value.group-price {\n  color: #FF3B30;\n}\n\n.preview-value.discount {\n  color: #34C759;\n}\n\n/* 底部按钮 */\n.footer-buttons {\n  padding: 15px;\n  background: #FFFFFF;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: space-between;\n  gap: 15px;\n}\n\n.btn {\n  flex: 1;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: 600;\n  border: none;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #FFFFFF;\n}\n\n.btn-secondary {\n  background: #F5F7FA;\n  color: #666;\n  border: 1px solid #EBEDF5;\n}\n\n/* 弹窗样式 */\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 999;\n}\n\n.modal-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n}\n\n.modal-content {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #FFFFFF;\n  border-radius: 20px 20px 0 0;\n  padding: 20px;\n  transform: translateY(0);\n  transition: transform 0.3s;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.modal-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.modal-close {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  color: #999;\n}\n\n.modal-body {\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 20px;\n  gap: 15px;\n}\n\n.modal-btn {\n  flex: 1;\n  height: 50px;\n  border-radius: 25px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: 600;\n  border: none;\n}\n\n.modal-btn.cancel {\n  background: #F5F7FA;\n  color: #666;\n  border: 1px solid #EBEDF5;\n}\n\n.modal-btn.confirm {\n  background: linear-gradient(135deg, #9040FF, #5E35B1);\n  color: #FFFFFF;\n}\n\n/* 表单样式 */\n.form-item {\n  margin-bottom: 20px;\n}\n\n.form-label {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 8px;\n  display: block;\n}\n\n.required {\n  color: #FF3B30;\n}\n\n.form-input {\n  width: 100%;\n  height: 45px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid #EBEDF5;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 100px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 12px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid #EBEDF5;\n}\n\n.quantity-input {\n  display: flex;\n  gap: 10px;\n}\n\n.unit-input {\n  width: 80px;\n  height: 45px;\n  background: #F5F7FA;\n  border-radius: 8px;\n  padding: 0 12px;\n  font-size: 14px;\n  color: #333;\n  border: 1px solid #EBEDF5;\n}\n\n.price-input-wrapper {\n  display: flex;\n  align-items: center;\n  background: #F5F7FA;\n  border-radius: 8px;\n  border: 1px solid #EBEDF5;\n  padding: 0 12px;\n}\n\n.price-symbol {\n  font-size: 14px;\n  color: #333;\n  margin-right: 5px;\n}\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/merchant-admin-marketing/pages/marketing/group/create-package-items.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAqJA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,cAAc,CAAE;AAAA,MAChB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,QACX,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACd;AAAA,MACD,YAAY;AAAA,IACd;AAAA,EACD;AAAA,EACD,SAAS;AAEP,QAAI;AACF,YAAM,aAAaA,cAAAA,MAAI,eAAe,cAAc;AACpD,UAAI,YAAY;AACd,aAAK,eAAe,KAAK,MAAM,UAAU;AAAA,MAC3C;AAEA,YAAM,YAAYA,cAAAA,MAAI,eAAe,kBAAkB;AACvD,UAAI,WAAW;AACb,cAAM,kBAAkB,KAAK,MAAM,SAAS;AAC5C,aAAK,aAAa,gBAAgB,cAAc;AAAA,MAClD;AAAA,IACF,SAAS,GAAG;AACVA,oBAAc,MAAA,MAAA,SAAA,8FAAA,aAAa,CAAC;AAAA,IAC9B;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AAEP,WAAK,SAAQ;AACbA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,WAAW;AAET,UAAI,KAAK,aAAa,WAAW,GAAG;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,WAAK,SAAQ;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IACD,WAAW;AAET,UAAI;AACFA,sBAAG,MAAC,eAAe,gBAAgB,KAAK,UAAU,KAAK,YAAY,CAAC;AAAA,MACtE,SAAS,GAAG;AACVA,yIAAc,WAAW,CAAC;AAAA,MAC5B;AAAA,IACD;AAAA,IACD,sBAAsB;AACpB,UAAI,QAAQ;AACZ,WAAK,aAAa,QAAQ,UAAQ;AAChC,iBAAS,WAAW,KAAK,KAAK,IAAI,WAAW,KAAK,QAAQ;AAAA,MAC5D,CAAC;AACD,aAAO,MAAM,QAAQ,CAAC;AAAA,IACvB;AAAA,IACD,oBAAoB;AAClB,YAAM,aAAa,WAAW,KAAK,oBAAqB,CAAA;AACxD,UAAI,cAAc;AAAG,eAAO;AAE5B,YAAM,aAAa,WAAW,KAAK,UAAU;AAC7C,YAAM,WAAY,aAAa,aAAc;AAC7C,aAAO,SAAS,QAAQ,CAAC;AAAA,IAC1B;AAAA,IACD,mBAAmB;AACjB,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,cAAc;AAAA,QACjB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA;AAEf,WAAK,YAAY;AAAA,IAClB;AAAA,IACD,SAAS,OAAO;AACd,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,cAAc,KAAK,MAAM,KAAK,UAAU,KAAK,aAAa,KAAK,CAAC,CAAC;AACtE,WAAK,YAAY;AAAA,IAClB;AAAA,IACD,WAAW,OAAO;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,aAAa,OAAO,OAAO,CAAC;AAAA,UACnC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,YAAY;AACV,WAAK,YAAY;AAAA,IAClB;AAAA,IACD,cAAc;AAEZ,UAAI,CAAC,KAAK,YAAY,MAAM;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,YAAY,YAAY,WAAW,KAAK,YAAY,QAAQ,KAAK,GAAG;AAC5EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,YAAY,MAAM;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,YAAY,SAAS,WAAW,KAAK,YAAY,KAAK,KAAK,GAAG;AACtEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,KAAK,WAAW;AAElB,aAAK,aAAa,KAAK,SAAS,IAAI,KAAK,MAAM,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,aAC1E;AAEL,aAAK,aAAa,KAAK,KAAK,MAAM,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC;AAAA,MACrE;AAEA,WAAK,UAAS;AAAA,IACf;AAAA,IACD,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtTA,GAAG,WAAW,eAAe;"}