<template>
	<view class="shop-detail-page">
		<!-- 隐藏的Canvas用于绘制海报 -->
		<canvas canvas-id="posterCanvas" class="poster-canvas" style="width: 600px; height: 900px; position: fixed; top: -9999px; left: -9999px;"></canvas>
		
		<!-- 状态栏占位和导航栏 -->
		<view class="blue-header-container">
			<view class="blue-header" :style="{'padding-top': isIOS ? (safeAreaInsetTop + 'px') : (statusBarHeight + 'px')}">
		<view class="navbar">
			<view class="navbar-left" @click="goBack">
				<view class="back-icon-wrap">
					<image src="/static/images/tabbar/返回.png" class="back-icon"></image>
				</view>
			</view>
					<view class="navbar-title">商家详情</view>
					<view class="navbar-right"></view>
				</view>
			</view>
		</view>
		
		<!-- 添加悬浮转发按钮组件 -->
		<FabButtons 
			pageName="shop-detail" 
			:pageInfo="{
				title: shopData.shopName + ' - 商家详情，点击查看更多信息',
				path: '/pages/business/shop-detail?id=' + shopData.id,
				imageUrl: shopData.images && shopData.images.length > 0 ? shopData.images[0] : ''
			}" 
		/>
		
		<!-- 悬浮海报按钮 -->
		<view class="float-poster-btn" @click="generateShareImage">
			<image src="/static/images/tabbar/海报.png" class="poster-icon"></image>
			<text class="poster-text">海报</text>
		</view>
		
		<!-- 顶部图片区域 -->
		<view class="shop-gallery">
			<swiper class="gallery-swiper" circular :indicator-dots="true" :autoplay="true" :interval="4000" 
				indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#ffffff">
				<swiper-item v-for="(img, index) in shopData.images" :key="index">
					<image class="gallery-image" :src="img" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 商家基本信息 -->
		<view class="shop-info-card">
			<view class="shop-basic-info">
				<view class="shop-logo-container">
					<image :src="shopData.logo || '/static/images/tabbar/商家入驻.png'" class="shop-logo"></image>
				</view>
				<view class="shop-title-container">
					<text class="shop-name">{{shopData.shopName}}</text>
					<view class="shop-category">
						<text class="category-tag">{{shopData.category}}</text>
						<text class="shop-scale">{{shopData.scale}}</text>
					</view>
					<view class="shop-stats">
						<text class="stat-item">浏览 {{shopData.viewCount}}</text>
						<text class="stat-divider">|</text>
						<text class="stat-item">收藏 {{shopData.favoriteCount}}</text>
					</view>
				</view>
			</view>
			
			<view class="action-buttons">
				<button class="action-btn follow-btn" :class="{'following': isFollowing}" @click="toggleFollow">
					<view class="btn-content">
					<image :src="isFollowing ? '/static/images/tabbar/已关注.png' : '/static/images/tabbar/关注.png'" class="btn-icon"></image>
						<text class="btn-text">{{isFollowing ? '已关注' : '关注'}}</text>
					</view>
				</button>
				<button class="action-btn contact-btn" @click="contactShop">
					<view class="btn-content">
						<image src="/static/images/tabbar/电话.png" class="btn-icon phone-icon"></image>
						<text class="btn-text">联系商家</text>
					</view>
				</button>
			</view>
		</view>
		
		<!-- 商家信息卡片 -->
		<view class="detail-section">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/tabbar/商家信息.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">商家信息</view>
				</view>
			</view>
			
			<view class="info-list">
				<view class="info-item location-item" @click="openLocation">
					<view class="info-icon-wrap">
						<image src="/static/images/tabbar/商家地址.png" class="info-icon"></image>
					</view>
					<view class="info-content">
						<text class="info-label">商家地址</text>
						<text class="info-value">{{shopData.address}}</text>
					</view>
					<view class="navigate-btn">
						<text>导航</text>
					</view>
				</view>
				
				<view class="info-item" @click="makePhoneCall">
					<view class="info-icon-wrap">
						<image src="/static/images/tabbar/电话.png" class="info-icon"></image>
					</view>
					<view class="info-content">
						<text class="info-label">联系电话</text>
						<text class="info-value">{{shopData.contactPhone}}</text>
						<text class="info-tip">打电话时请说在磁州生活网看到的</text>
					</view>
					<view class="action-icon-wrap">
						<image src="/static/images/tabbar/箭头.png" class="action-icon"></image>
					</view>
				</view>
				
				<view class="info-item">
					<view class="info-icon-wrap">
						<image src="/static/images/tabbar/营业时间.png" class="info-icon"></image>
					</view>
					<view class="info-content">
						<text class="info-label">营业时间</text>
						<text class="info-value">{{shopData.businessTime}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 举报提示卡片 -->
		<view class="report-tip-section" @click="showReportOptions">
			<view class="report-tip-content">
				<image src="/static/images/tabbar/举报.png" class="report-tip-icon"></image>
				<text class="report-tip-text">如遇无效、虚假、诈骗信息，请立即举报</text>
				<image src="/static/images/tabbar/箭头.png" class="report-arrow-icon"></image>
			</view>
		</view>
		
		<!-- 商家介绍卡片 -->
		<view class="detail-section">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/tabbar/商家介绍.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">商家介绍</view>
				</view>
			</view>
			<view class="description-content">
				<text class="description-text">{{shopData.description}}</text>
			</view>
		</view>
		
		<!-- 消费满额红包卡片 -->
		<view class="detail-section" v-if="shopData.hasConsumeRedPacket">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/red-packet-icon.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">消费红包</view>
				</view>
				<view class="header-right" v-if="shopData.hasConsumeRedPacket">
					<text class="more-text">活动中</text>
				</view>
			</view>
			<view class="consume-red-packet-container">
				<view class="consume-activity-card">
					<view class="activity-header">
						<image class="merchant-logo" :src="shopData.merchantLogo || shopData.logo" mode="aspectFill"></image>
						<view class="activity-info">
							<text class="activity-title">消费满额抽红包</text>
							<text class="activity-desc">到店消费满100元，最高可得88元红包</text>
						</view>
					</view>
					<view class="activity-details">
						<text class="detail-item">活动时间：{{formatDate(new Date().getTime() + 30*24*60*60*1000)}}</text>
						<view class="button-area">
							<button class="draw-button" @click="handleUseRedPacket">立即抽红包</button>
							<text class="detail-text">已有{{Math.floor(Math.random()*200+50)}}人参与</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 商家相册 -->
		<view class="detail-section">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/tabbar/商家相册.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">商家相册</view>
				</view>
				<view class="view-all" @click="viewAllPhotos">
					<text>查看全部</text>
					<image src="/static/images/tabbar/箭头.png" class="arrow-icon"></image>
				</view>
			</view>
			<view class="photos-grid">
				<view class="photo-item" v-for="(photo, index) in shopData.albumImages.slice(0, 6)" :key="index" @click="previewImage(index)">
					<image :src="photo" class="photo-image" mode="aspectFill"></image>
				</view>
			</view>
		</view>
		
		<!-- 客服微信二维码 -->
		<view class="detail-section">
			<view class="section-header">
				<view class="header-left">
					<view class="section-icon-wrap">
						<image src="/static/images/tabbar/客服微信.png" class="section-icon" mode="aspectFit"></image>
					</view>
					<view class="section-title-text">客服微信</view>
				</view>
			</view>
			<view class="qrcode-container">
				<image :src="shopData.qrcode" class="qrcode-image" @click="previewQRCode"></image>
				<text class="qrcode-tip">点击保存图片，微信扫码添加</text>
			</view>
		</view>
		
		<!-- 抽奖动画弹窗 -->
		<SimplePopup ref="redPacketPopup" type="center">
			<view class="red-packet-popup">
				<view class="drawing-area" v-if="isDrawing">
					<view class="red-packet-animation">
						<image class="red-packet-img" src="/static/images/red-packet-icon.png" mode="aspectFit"></image>
						<text class="drawing-text">正在抽取红包...</text>
					</view>
				</view>
				
				<view class="result-area" v-if="hasDrawn">
					<view class="result-content">
						<image class="result-icon" src="/static/images/red-packet-icon.png" mode="aspectFit"></image>
						<text class="result-title">恭喜获得红包</text>
						<text class="result-amount">¥{{drawResult.amount}}</text>
						<text class="result-desc">{{drawResult.message || '红包已发放到您的账户'}}</text>
						<view class="result-buttons">
							<button class="use-now-btn" @click="useDrawnRedPacket">立即使用</button>
							<button class="close-btn" @click="closeRedPacketPopup">关闭</button>
						</view>
					</view>
				</view>
			</view>
		</SimplePopup>
		
		<!-- 底部功能区 -->
		<view class="bottom-toolbar">
			<view class="toolbar-btn" @click="backToHome">
				<image src="/static/images/tabbar/a首页.png" class="toolbar-icon"></image>
					<text class="toolbar-text">首页</text>
				</view>
			<view class="toolbar-btn" @click="shareShop">
				<image src="/static/images/tabbar/a分享.png" class="toolbar-icon"></image>
				<text class="toolbar-text">分享</text>
				</view>
			<view class="toolbar-btn message-btn" @click="contactShop">
				<image src="/static/images/tabbar/a消息.png" class="toolbar-icon"></image>
				<text class="toolbar-text">私信</text>
			</view>
			<view class="toolbar-call-btn" @click="callShop">
				<text class="call-text">打电话</text>
				<text class="call-subtext">请说在磁州生活网看到的</text>
			</view>
		</view>
	</view>
</template>

<script>
// 导入FabButtons组件
import FabButtons from '@/components/FabButtons.vue'
// 导入消费红包组件
import ConsumeRedPacket from '@/components/ConsumeRedPacket.vue'
// 导入自定义弹窗组件
import SimplePopup from '@/components/SimplePopup.vue'

export default {
	components: {
		FabButtons,
		ConsumeRedPacket,
		SimplePopup
	},
	data() {
		return {
			shopId: null,
			isFollowing: false,
			isFavorite: false,
			statusBarHeight: 20,
			navbarHeight: 44,
			menuButtonHeight: 0,
			menuButtonWidth: 0,
			menuButtonTop: 0,
			menuButtonRight: 0,
			safeAreaInsetTop: 0,
			isIOS: false,
			showPoster: false, // 是否显示海报
			posterPath: '', // 海报图片路径
			// 商家列表数据
			shopList: [
				{
					id: "1",
					shopName: "五分利电器",
					category: "数码电器",
					scale: "10-20人",
					address: "河北省邯郸市磁县祥和路",
					contactPhone: "188-8888-8888",
					businessTime: "09:00-21:00",
					description: '五分利电器是本地知名的电器销售与维修服务商。我们提供各类家用电器，包括冰箱、洗衣机、空调、电视等。全场特价，送货上门，并提供专业安装和维修服务。我们的技术人员经验丰富，能够高效解决各类电器故障问题。\n\n本店支持分期付款，以旧换新，并提供长期的售后保障。欢迎新老顾客前来选购！',
					viewCount: 2345,
					favoriteCount: 128,
					logo: "/static/images/tabbar/商家入驻.png",
					qrcode: "/static/images/tabbar/二维码示例.png",
					supportMessage: true,
					images: [
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-3.jpg"
					],
					albumImages: [
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-3.jpg",
					]
				},
				{
					id: "2",
					shopName: "金鼎家居",
					category: "家居家装",
					scale: "20-50人",
					address: "河北省邯郸市磁县滏阳路68号",
					contactPhone: "133-7777-6666",
					businessTime: "08:30-20:30",
					description: '金鼎家居是磁县规模最大的家居家装综合体验馆。汇聚国内外数十个知名家具品牌，提供全屋定制、软装搭配、设计施工一站式服务。\n\n我们拥有专业的设计团队和施工队伍，致力于为每位客户打造温馨舒适的家居环境。定期举办家装设计沙龙和优惠促销活动，欢迎各位新老客户前来参观选购！',
					viewCount: 3156,
					favoriteCount: 265,
					logo: "/static/images/tabbar/商家入驻.png",
					qrcode: "/static/images/tabbar/二维码示例.png",
					supportMessage: true,
					images: [
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-1.png"
					],
					albumImages: [
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-1.png",
					]
				},
				{
					id: "3",
					shopName: "鲜丰水果",
					category: "生鲜果蔬",
					scale: "5-10人",
					address: "河北省邯郸市磁县时代广场A区112号",
					contactPhone: "177-6666-5555",
					businessTime: "07:00-22:00",
					description: '鲜丰水果是磁县连锁经营的专业水果零售店，直接从原产地采购，确保每一件水果新鲜美味。我们经营各类时令水果、进口水果、果篮礼盒和干果零食。\n\n本店坚持"每日现货、严格筛选、保证新鲜"的经营理念，让您吃得放心、吃得健康。支持微信下单，全城配送，会员每周还有特价优惠活动。欢迎惠顾！',
					viewCount: 1789,
					favoriteCount: 98,
					logo: "/static/images/tabbar/商家入驻.png",
					qrcode: "/static/images/tabbar/二维码示例.png",
					supportMessage: false,
					images: [
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-2.png"
					],
					albumImages: [
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-2.png",
					]
				},
				{
					id: "4",
					shopName: "磁州书院",
					category: "文化教育",
					scale: "10-15人",
					address: "河北省邯郸市磁县文化路29号",
					contactPhone: "155-5555-6666",
					businessTime: "08:30-21:30",
					description: '磁州书院创立于2010年，是磁县知名的综合性文化教育机构。我们提供少儿教育、成人培训、艺术培训等多元化课程，并设有专业的阅读空间和文创产品销售区。\n\n书院拥有一支高素质的教师团队，致力于传承中华优秀传统文化，培养学生的综合素质和创新能力。定期举办读书会、文化讲座和书法展览等活动，欢迎广大市民参与。',
					viewCount: 2567,
					favoriteCount: 187,
					logo: "/static/images/tabbar/商家入驻.png",
					qrcode: "/static/images/tabbar/二维码示例.png",
					images: [
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-2.png"
					],
					albumImages: [
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-2.png",
					]
				},
				{
					id: "5",
					shopName: "康美大药房",
					category: "医疗健康",
					scale: "15-20人",
					address: "河北省邯郸市磁县健康路45号",
					contactPhone: "199-9999-8888",
					businessTime: "全天24小时",
					description: '康美大药房是磁县规模最大的连锁药店之一，经营各类处方药、非处方药、保健品和医疗器械。我们与多家医院、医保定点合作，方便患者就近配药。\n\n本店拥有执业药师坐诊，提供专业的用药咨询和健康指导服务。定期举办健康讲座和义诊活动，关注社区居民的健康需求。支持医保刷卡、送药上门，让您的健康更有保障！',
					viewCount: 3012,
					favoriteCount: 213,
					logo: "/static/images/tabbar/商家入驻.png",
					qrcode: "/static/images/tabbar/二维码示例.png",
					images: [
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-3.jpg"
					],
					albumImages: [
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-3.jpg",
						"/static/images/banner/banner-2.png",
						"/static/images/banner/banner-1.png",
						"/static/images/banner/banner-3.jpg",
					]
				}
			],
			// 当前展示的商家数据
			shopData: {},
			
			// 红包相关数据
			redPacketVisible: false,
			isDrawing: false,
			hasDrawn: false,
			drawResult: {
				amount: 0,
				message: ''
			}
		}
	},
	// 设置页面全屏状态栏
	navigationStyle: 'custom',
	// 页面样式配置
	navigationBarTextStyle: 'white',
	
	onLoad(options) {
		// 获取状态栏高度
		const sysInfo = uni.getSystemInfoSync();
		this.statusBarHeight = sysInfo.statusBarHeight || 20;
		this.navbarHeight = sysInfo.platform === 'ios' ? 44 : 48;
		this.isIOS = sysInfo.platform === 'ios';
		this.safeAreaInsetTop = sysInfo.safeAreaInsets ? sysInfo.safeAreaInsets.top : 0;
		
		// 获取胶囊按钮位置信息
		try {
			const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.menuButtonHeight = menuButtonInfo.height;
			this.menuButtonWidth = menuButtonInfo.width;
			this.menuButtonTop = menuButtonInfo.top;
			this.menuButtonRight = sysInfo.windowWidth - menuButtonInfo.right;
		} catch (e) {
			console.error('获取胶囊按钮信息失败', e);
		}
		
		// 设置状态栏样式
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#3846cd'
		});
		
		if (options.id) {
			this.shopId = options.id;
			// 加载商家数据
			this.loadShopData();
		} else {
			// 默认加载第一个商家数据
			this.shopData = this.shopList[0];
		}
	},
	
	onShareAppMessage() {
		// 自定义转发内容
		return {
			title: this.shopData.shopName + ' - 商家详情，点击查看更多信息',
			path: '/pages/business/shop-detail?id=' + this.shopData.id
		}
	},
	
	onReady() {
		// 再次确保状态栏样式正确
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#3846cd'
		});
	},
	methods: {
		loadShopData() {
			// 实际项目中应通过API获取数据
			// uni.request({
			//     url: '/api/shop/' + this.shopId,
			//     success: (res) => {
			//         this.shopData = res.data;
			//     }
			// })
			
			// 模拟从商家列表中获取数据
			const shop = this.shopList.find(item => item.id === this.shopId);
			if (shop) {
				// 添加消费红包相关信息 - 确保所有商家都有红包功能
				shop.hasConsumeRedPacket = true;
				shop.consumeRedPacketId = 'rp_' + shop.id;
				
				// 添加商家Logo作为示例
				if (!shop.merchantLogo) {
					shop.merchantLogo = shop.logo || '/static/images/tabbar/商家入驻.png';
				}
				
				this.shopData = shop;
				
				// 尝试更新页面标题（如果不是自定义导航栏）
				try {
					uni.setNavigationBarTitle({
						title: shop.shopName
					});
				} catch (e) {
					console.log('自定义导航栏模式，无法设置标题');
				}
				
				// 模拟增加浏览量
				this.shopData.viewCount++;
				
				console.log('商家数据加载完成，红包ID:', shop.consumeRedPacketId);
			} else {
				// 如果找不到商家，使用第一个商家作为默认值，而不是显示错误
				console.log('未找到ID为', this.shopId, '的商家，使用默认商家');
				this.shopData = this.shopList[0];
				// 为默认商家也添加红包功能
				this.shopData.hasConsumeRedPacket = true;
				this.shopData.consumeRedPacketId = 'rp_default';
				
				// 更新shopId为当前显示的商家ID
				this.shopId = this.shopData.id;
				
				// 尝试更新页面标题（如果不是自定义导航栏）
				try {
					uni.setNavigationBarTitle({
						title: this.shopData.shopName
					});
				} catch (e) {
					console.log('自定义导航栏模式，无法设置标题');
				}
				
				// 模拟增加浏览量
				this.shopData.viewCount++;
			}
			
			console.log('商家数据加载完成');
		},
		goBack() {
			uni.navigateBack({
				delta: 1
			});
		},
		backToHome() {
			uni.switchTab({
				url: '/pages/index/index'
			})
		},
		callShop() {
			uni.makePhoneCall({
				phoneNumber: this.shopData.contactPhone,
				success: () => {
					console.log('拨打电话成功');
				},
				fail: (err) => {
					console.log('拨打电话失败', err);
					uni.showToast({
						title: '拨打电话失败',
						icon: 'none'
					});
				}
			});
		},
		shareShop() {
			const shopInfo = {
				title: this.shopData.shopName,
				summary: this.shopData.description.substring(0, 50) + '...',
				imageUrl: this.shopData.images[0],
				path: `/pages/business/shop-detail?id=${this.shopData.id}`
			};
			
			// 根据平台区分分享行为
			// #ifdef APP-PLUS
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				type: 0,
				title: shopInfo.title,
				summary: shopInfo.summary,
				imageUrl: shopInfo.imageUrl,
				href: shopInfo.path,
				success: function() {
					console.log('分享成功');
				},
				fail: function() {
					console.log('分享失败');
				}
			});
			// #endif
			
			// #ifdef MP
			uni.showActionSheet({
				itemList: ['分享到微信', '生成分享图片', '复制链接'],
				success: (res) => {
					switch (res.tapIndex) {
						case 0:
							// 小程序内分享(调用页面的onShareAppMessage即可)
			uni.showToast({
								title: '点击右上角进行微信分享',
				icon: 'none'
							});
							break;
						case 1:
							// 生成分享图片
							this.generateShareImage();
							break;
						case 2:
							// 复制链接
							uni.setClipboardData({
								data: `磁县同城 - ${shopInfo.title}，打开小程序查看详情`,
								success: () => {
									uni.showToast({
										title: '链接已复制',
										icon: 'success'
									});
								}
							});
							break;
					}
				}
			});
			// #endif
			
			// #ifdef H5
			uni.showActionSheet({
				itemList: ['复制链接'],
				success: (res) => {
					if (res.tapIndex === 0) {
						const currentUrl = window.location.href;
						uni.setClipboardData({
							data: currentUrl,
							success: () => {
								uni.showToast({
									title: '链接已复制，可分享给好友',
									icon: 'none'
								});
							}
						});
					}
				}
			});
			// #endif
		},
		generateShareImage() {
			uni.showLoading({
				title: '正在生成海报...',
				mask: true
			});
			
			// 创建海报数据对象
			const posterData = {
				shopName: this.shopData.shopName,
				category: this.shopData.category,
				address: this.shopData.address,
				phone: this.shopData.contactPhone,
				description: this.shopData.description.substring(0, 60) + '...',
				qrcode: this.shopData.qrcode || '/static/images/tabbar/客服微信.png',
				logo: this.shopData.logo || '/static/images/tabbar/商家入驻.png',
				bgImage: this.shopData.images[0]
			};
			
			// #ifdef H5
			// H5环境不支持canvas绘制图片保存，提示用户
			setTimeout(() => {
				uni.hideLoading();
				uni.showModal({
					title: '提示',
					content: 'H5环境暂不支持保存海报，请使用App或小程序',
					showCancel: false
				});
			}, 1000);
			return;
			// #endif
			
			// 绘制海报
			const ctx = uni.createCanvasContext('posterCanvas', this);
			
			// 绘制背景
			ctx.save();
			ctx.drawImage(posterData.bgImage, 0, 0, 600, 900);
			// 添加半透明渐变蒙层
			const grd = ctx.createLinearGradient(0, 0, 0, 900);
			grd.addColorStop(0, 'rgba(40, 40, 40, 0.5)');
			grd.addColorStop(1, 'rgba(0, 0, 0, 0.8)');
			ctx.setFillStyle(grd);
			ctx.fillRect(0, 0, 600, 900);
			ctx.restore();
			
			// 绘制白色卡片背景
			ctx.save();
			ctx.setFillStyle('#ffffff');
			ctx.setShadow(0, 2, 15, 'rgba(0, 0, 0, 0.15)');
			ctx.fillRect(50, 200, 500, 600);
			ctx.restore();
			
			// 绘制店铺Logo
			ctx.save();
			ctx.beginPath();
			ctx.arc(300, 150, 80, 0, 2 * Math.PI);
			ctx.setFillStyle('#ffffff');
			ctx.fill();
			ctx.setShadow(0, 0, 10, 'rgba(0, 0, 0, 0.1)');
			ctx.clip();
			ctx.drawImage(posterData.logo, 220, 70, 160, 160);
			ctx.restore();
			
			// 绘制店铺名称
			ctx.save();
			ctx.setFillStyle('#333333');
			ctx.setFontSize(36);
			ctx.setTextAlign('center');
			ctx.setTextBaseline('middle');
			ctx.fillText(posterData.shopName, 300, 270);
			ctx.restore();
			
			// 绘制分类标签
			ctx.save();
			ctx.setFillStyle('#f2f2f2');
			const textWidth = ctx.measureText(posterData.category).width;
			ctx.fillRect(300 - textWidth/2 - 20, 300, textWidth + 40, 40);
			ctx.setFillStyle('#666666');
			ctx.setFontSize(24);
			ctx.setTextAlign('center');
			ctx.setTextBaseline('middle');
			ctx.fillText(posterData.category, 300, 320);
			ctx.restore();
			
			// 绘制分割线
			ctx.save();
			ctx.beginPath();
			ctx.setLineDash([5, 3]);
			ctx.setStrokeStyle('#dddddd');
			ctx.setLineWidth(2);
			ctx.moveTo(80, 360);
			ctx.lineTo(520, 360);
			ctx.stroke();
			ctx.restore();
			
			// 绘制地址图标和地址
			ctx.save();
			ctx.drawImage('/static/images/tabbar/定位.png', 80, 380, 30, 30);
			ctx.setFillStyle('#666666');
			ctx.setFontSize(24);
			ctx.fillText('地址: ' + posterData.address, 120, 400);
			ctx.restore();
			
			// 绘制电话图标和电话
			ctx.save();
			ctx.drawImage('/static/images/tabbar/电话.png', 80, 430, 28, 28);
			ctx.setFillStyle('#666666');
			ctx.setFontSize(24);
			ctx.fillText('联系电话: ' + posterData.phone, 120, 445);
			ctx.restore();
			
			// 绘制分割线
			ctx.save();
			ctx.beginPath();
			ctx.setStrokeStyle('#eeeeee');
			ctx.setLineWidth(1);
			ctx.moveTo(80, 480);
			ctx.lineTo(520, 480);
			ctx.stroke();
			ctx.restore();
			
			// 绘制商家介绍标题
			ctx.save();
			ctx.setFillStyle('#333333');
			ctx.setFontSize(28);
			ctx.fillText('商家介绍', 80, 520);
			ctx.restore();
			
			// 绘制简介(自动换行)
			ctx.save();
			// A wrap text function
			const wrapText = (ctx, text, x, y, maxWidth, lineHeight) => {
				if (!text || text.length === 0) return;
				
				const words = text.split('');
				let line = '';
				let testLine = '';
				let lineCount = 0;
				
				for (let n = 0; n < words.length; n++) {
					testLine += words[n];
					const metrics = ctx.measureText(testLine);
					const testWidth = metrics.width;
					
					if (testWidth > maxWidth && n > 0) {
						ctx.fillText(line, x, y + (lineCount * lineHeight));
						line = words[n];
						testLine = words[n];
						lineCount++;
						
						if (lineCount >= 3) {
							line += '...';
							ctx.fillText(line, x, y + (lineCount * lineHeight));
							break;
						}
					} else {
						line = testLine;
					}
				}
				
				if (lineCount < 3) {
					ctx.fillText(line, x, y + (lineCount * lineHeight));
				}
			};
			
			ctx.setFillStyle('#666666');
			ctx.setFontSize(24);
			wrapText(ctx, posterData.description, 80, 550, 440, 40);
			ctx.restore();
			
			// 绘制二维码框
			ctx.save();
			ctx.setFillStyle('#f9f9f9');
			ctx.fillRect(180, 670, 240, 110);
			ctx.drawImage(posterData.qrcode, 225, 680, 90, 90);
			
			// 提示文字
			ctx.setFillStyle('#999999');
			ctx.setFontSize(20);
			ctx.setTextAlign('right');
			ctx.fillText('长按识别二维码查看详情', 420, 735);
			ctx.restore();
			
			// 底部平台Logo
			ctx.save();
			ctx.setFillStyle('#333333');
			ctx.setFontSize(24);
			ctx.setTextAlign('center');
			ctx.fillText('磁县同城 - 掌上商家', 300, 800);
			ctx.restore();
			
			// 绘制完成，输出图片
			ctx.draw(false, () => {
				setTimeout(() => {
					// 延迟确保canvas已完成渲染
					uni.canvasToTempFilePath({
						canvasId: 'posterCanvas',
						success: (res) => {
							uni.hideLoading();
							this.showPosterModal(res.tempFilePath);
						},
						fail: (err) => {
							console.error('生成海报失败', err);
							uni.hideLoading();
							uni.showToast({
								title: '生成海报失败',
								icon: 'none'
							});
						}
					}, this);
				}, 800);
			});
		},
		
		// 显示海报预览和保存选项
		showPosterModal(posterPath) {
			this.posterPath = posterPath;
			this.showPoster = true;
			
			uni.showModal({
				title: '海报已生成',
				content: '海报已生成，是否保存到相册？',
				confirmText: '保存',
				success: (res) => {
					if (res.confirm) {
						this.savePosterToAlbum(posterPath);
					} else {
						// 预览图片
						uni.previewImage({
							urls: [posterPath],
							current: posterPath
						});
					}
				}
			});
		},
		
		// 保存海报到相册
		savePosterToAlbum(posterPath) {
			uni.showLoading({
				title: '正在保存...'
			});
			
			uni.saveImageToPhotosAlbum({
				filePath: posterPath,
				success: () => {
					uni.hideLoading();
					uni.showToast({
						title: '已保存到相册',
						icon: 'success'
					});
				},
				fail: (err) => {
					uni.hideLoading();
					console.error('保存失败', err);
					
					if (err.errMsg.indexOf('auth deny') > -1) {
						uni.showModal({
							title: '提示',
							content: '保存失败，请授权相册权限后重试',
							confirmText: '去设置',
							success: (res) => {
								if (res.confirm) {
									uni.openSetting();
								}
							}
						});
					} else {
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						});
					}
				}
			});
		},
		toggleFollow() {
			this.isFollowing = !this.isFollowing;
			uni.showToast({
				title: this.isFollowing ? '已关注' : '已取消关注',
				icon: 'none'
			});
		},
		toggleFavorite() {
			this.isFavorite = !this.isFavorite;
			uni.showToast({
				title: this.isFavorite ? '已收藏' : '已取消收藏',
				icon: 'none'
			});
		},
		contactShop() {
			// 检查是否登录
			const userInfo = uni.getStorageSync('userInfo');
			if (!userInfo) {
				uni.showModal({
					title: '提示',
					content: '发送私信需要先登录，是否前往登录？',
				success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login/login?redirect=' + encodeURIComponent('/pages/business/shop-detail?id=' + this.shopData.id)
							});
						}
					}
				});
				return;
			}
			
			// 检查商家是否支持私信
			if (!this.shopData.supportMessage) {
				uni.showModal({
					title: '提示',
					content: '该商家暂不支持私信功能，您可以直接拨打电话联系',
					confirmText: '拨打电话',
					success: (res) => {
						if (res.confirm) {
							this.callShop();
						}
					}
				});
				return;
			}
			
			// 跳转到聊天界面
			uni.navigateTo({
				url: `/pages/chat/chat?targetId=${this.shopData.id}&targetName=${this.shopData.shopName}&targetAvatar=${encodeURIComponent(this.shopData.logo)}`
			});
		},
		makePhoneCall() {
			uni.makePhoneCall({
				phoneNumber: this.shopData.contactPhone,
				fail: () => {
					uni.showToast({
						title: '拨打电话失败',
						icon: 'none'
					});
				}
			});
		},
		openLocation() {
			// 实际应用中需要转换为经纬度
			uni.openLocation({
				latitude: 36.313076,
				longitude: 114.347312,
				name: this.shopData.shopName,
				address: this.shopData.address,
				scale: 18
			});
		},
		previewImage(index) {
			uni.previewImage({
				current: this.shopData.albumImages[index],
				urls: this.shopData.albumImages
			});
		},
		previewQRCode() {
			uni.previewImage({
				current: this.shopData.qrcode,
				urls: [this.shopData.qrcode]
			});
		},
		viewAllPhotos() {
			uni.navigateTo({
				url: '/pages/business/shop-photos?id=' + this.shopId
			});
		},
		// 显示举报选项
		showReportOptions() {
			uni.showActionSheet({
				itemList: ['虚假信息', '违法内容', '色情低俗', '侵权投诉', '诱导欺骗', '其他问题'],
				success: (res) => {
					// 获取用户选择的举报原因
					const reportTypes = ['虚假信息', '违法内容', '色情低俗', '侵权投诉', '诱导欺骗', '其他问题'];
					const selectedType = reportTypes[res.tapIndex];
					
					// 检查是否登录
					const userInfo = uni.getStorageSync('userInfo');
					if (!userInfo) {
						uni.showModal({
							title: '提示',
							content: '举报需要先登录，是否前往登录？',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/login/login?redirect=' + encodeURIComponent('/pages/business/shop-detail?id=' + this.shopData.id)
									});
								}
							}
						});
						return;
					}
					
					// 如果是"其他问题"，弹出输入框让用户填写
					if (selectedType === '其他问题') {
						this.showReportInputDialog();
					} else {
						this.submitReport(selectedType);
					}
				}
			});
		},
		
		// 显示其他问题输入框
		showReportInputDialog() {
			uni.showModal({
				title: '请描述问题',
				placeholderText: '请详细描述您遇到的问题',
				editable: true,
				success: (res) => {
					if (res.confirm && res.content) {
						this.submitReport('其他问题: ' + res.content);
					}
				}
			});
		},
		
		// 提交举报
		submitReport(reportReason) {
			uni.showLoading({
				title: '提交中...'
			});
			
			// 实际项目中应调用接口提交举报
			// uni.request({
			//     url: '/api/report',
			//     method: 'POST',
			//     data: {
			//         targetId: this.shopData.id,
			//         targetType: 'shop',
			//         reason: reportReason
			//     },
			//     success: () => {...},
			//     fail: () => {...}
			// });
			
			// 模拟接口调用
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '举报成功',
					icon: 'success'
				});
			}, 1000);
		},
		
		// 处理使用红包
		handleUseRedPacket() {
			// 检查登录状态
			const userInfo = uni.getStorageSync('userInfo');
			if (!userInfo) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				
				// 跳转到登录页
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login?redirect=' + encodeURIComponent('/pages/business/shop-detail?id=' + this.shopData.id)
					});
				}, 1500);
				return;
			}
			
			// 打开红包弹窗
			this.isDrawing = true;
			this.hasDrawn = false;
			this.$refs.redPacketPopup.open();
			
			// 模拟抽奖过程
			setTimeout(() => {
				try {
					// 随机红包金额 (1-88元)
					const amount = (Math.random() * 87 + 1).toFixed(2);
					
					// 显示结果
					this.drawResult = {
						amount: amount,
						message: amount >= 20 ? '恭喜您，手气不错！' : '谢谢参与，下次再来！'
					};
				} catch (error) {
					console.error('抽红包失败', error);
					this.drawResult = {
						amount: 0,
						message: '网络异常，请重试'
					};
				} finally {
					this.isDrawing = false;
					this.hasDrawn = true;
				}
			}, 2000);
		},
		
		// 使用已抽取的红包
		useDrawnRedPacket() {
			uni.showToast({
				title: `红包已添加到您的账户`,
				icon: 'success'
			});
			this.closeRedPacketPopup();
		},
		
		// 关闭红包弹窗
		closeRedPacketPopup() {
			this.$refs.redPacketPopup.close();
			setTimeout(() => {
				this.isDrawing = false;
				this.hasDrawn = false;
			}, 300);
		},
		
		// 格式化日期，用于显示活动到期时间
		formatDate(timestamp) {
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		}
	}
}
</script>

<style lang="scss">
/* 覆盖整个顶部区域的蓝色容器 */
.blue-header-container {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	width: 100%;
	z-index: 100;
	pointer-events: auto;
}

/* 蓝色头部区域 */
.blue-header {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	z-index: 10;
	background-color: #3846cd;
	width: 100%;
	/* 这里的padding-top会被动态设置 */
	pointer-events: auto;
}

.safe-area-inset-top {
	display: none; /* 不再需要这个元素 */
}

/* 页面整体样式 */
.shop-detail-page {
	min-height: 100vh;
	width: 100vw;
	background-color: #f7f8fc;
	position: relative;
	padding-bottom: 120rpx;
	overflow-x: hidden;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 系统标题栏 */
.navbar {
	height: 90rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx 0 20rpx;
	width: 100%;
	box-sizing: border-box;
}

.navbar-left, .navbar-right {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon-wrap {
	width: 72rpx;
	height: 72rpx;
	border-radius: 50%;
	background-color: transparent;
	backdrop-filter: none;
	-webkit-backdrop-filter: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.back-icon-wrap:active {
	transform: scale(0.92);
	background-color: transparent;
}

.back-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 1;
	filter: brightness(0) invert(1);
}

.navbar-title {
	flex: 1;
	text-align: center;
	font-size: 34rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 顶部图片轮播 */
.shop-gallery {
	position: relative;
	width: 100%;
	height: 520rpx;
	margin-top: calc(var(--status-bar-height, 44px) + 90rpx);
	border-radius: 0 0 40rpx 40rpx;
	overflow: hidden;
	box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.08);
}

.gallery-swiper {
	width: 100%;
	height: 100%;
}

.gallery-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* 商家基本信息卡片 */
.shop-info-card {
	margin: -70rpx 30rpx 30rpx;
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 28rpx;
	padding: 36rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);
	position: relative;
	z-index: 2;
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.shop-basic-info {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.shop-logo-container {
	width: 130rpx;
	height: 130rpx;
	border-radius: 24rpx;
	background-color: #f5f7fa;
	overflow: hidden;
	margin-right: 24rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
	border: 2rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
}

.shop-logo-container::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 24rpx;
	box-shadow: inset 0 0 0 1rpx rgba(255, 255, 255, 0.4);
	pointer-events: none;
}

.shop-logo {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.shop-title-container {
	flex: 1;
}

.shop-name {
	font-size: 40rpx;
	font-weight: 600;
	color: #222;
	margin-bottom: 16rpx;
	display: block;
	letter-spacing: -0.5rpx;
}

.shop-category {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.category-tag {
	font-size: 24rpx;
	color: #2738C0;
	background-color: rgba(39, 56, 192, 0.08);
	padding: 6rpx 20rpx;
	border-radius: 20rpx;
	margin-right: 16rpx;
	font-weight: 500;
}

.shop-scale {
	font-size: 24rpx;
	color: #666;
	background-color: #f0f2f5;
	padding: 6rpx 20rpx;
	border-radius: 20rpx;
}

.shop-stats {
	display: flex;
	align-items: center;
}

.stat-item {
	font-size: 24rpx;
	color: #888;
}

.stat-divider {
	margin: 0 16rpx;
	color: #ddd;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	justify-content: space-between;
	margin-top: 36rpx;
}

.action-btn {
	flex: 1;
	height: 84rpx;
	border-radius: 42rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: 500;
	margin: 0 10rpx;
	padding: 0;
	line-height: 1;
	transition: all 0.25s ease;
	box-sizing: border-box;
}

.action-btn:active {
	transform: scale(0.96);
	opacity: 0.9;
}

.follow-btn {
	background-color: #f0f5ff;
	color: #3846cd;
	border: 1px solid rgba(56, 70, 205, 0.2);
	box-shadow: 0 4rpx 10rpx rgba(56, 70, 205, 0.1);
}

.follow-btn.following {
	background-color: #f5f7fb;
	color: #888;
	border: 1px solid rgba(0, 0, 0, 0.05);
	box-shadow: none;
}

.contact-btn {
	background: linear-gradient(135deg, #5264e9, #4557e0);
	color: #ffffff;
	border: none;
	box-shadow: 0 8rpx 20rpx rgba(39, 56, 192, 0.15);
}

.btn-content {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
	position: relative;
}

.btn-icon {
	width: 28rpx;
	height: 28rpx;
	margin-right: 8rpx;
	flex-shrink: 0;
}

.phone-icon {
	filter: brightness(0) invert(1);
}

.btn-text {
	color: inherit;
	font-family: 'AlimamaShuHeiTi', sans-serif;
	font-size: 28rpx;
	line-height: 28rpx;
	padding-top: 2rpx;
}

/* 详情卡片通用样式 */
.detail-section {
	margin: 30rpx 30rpx 0;
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 28rpx;
	padding: 36rpx;
	box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.06);
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	transition: all 0.3s ease;
}

.section-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.header-left {
	display: flex;
	align-items: center;
}

.section-icon-wrap {
	width: 56rpx;
	height: 56rpx;
	border-radius: 16rpx;
	background-color: rgba(39, 56, 192, 0.08);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
}

.section-icon {
	width: 32rpx;
	height: 32rpx;
	display: block;
}

.section-title {
	display: none !important;
}

.section-title-text {
	font-size: 34rpx;
	font-weight: 600;
	color: #222;
	letter-spacing: -0.5rpx;
	line-height: 1.2;
	padding: 0 4rpx;
	display: inline-block;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.view-all {
	display: flex;
	align-items: center;
	font-size: 26rpx;
	color: #2738C0;
	background-color: rgba(39, 56, 192, 0.08);
	padding: 8rpx 20rpx;
	border-radius: 28rpx;
}

.arrow-icon {
	width: 24rpx;
	height: 24rpx;
	margin-left: 8rpx;
}

/* 信息列表 */
.info-list {
	background-color: rgba(249, 250, 252, 0.7);
	border-radius: 24rpx;
	overflow: hidden;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.info-item {
	display: flex;
	align-items: center;
	padding: 28rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.info-item:last-child {
	border-bottom: none;
}

.info-icon-wrap {
	width: 68rpx;
	height: 68rpx;
	border-radius: 20rpx;
	background-color: rgba(39, 56, 192, 0.08);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.info-icon {
	width: 36rpx;
	height: 36rpx;
}

.info-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.info-label {
	font-size: 26rpx;
	color: #888;
	margin-bottom: 8rpx;
}

.info-value {
	font-size: 30rpx;
	color: #222;
	font-weight: 500;
}

.navigate-btn {
	padding: 6rpx 24rpx;
	background-color: rgba(39, 56, 192, 0.08);
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #2738C0;
	margin-left: 20rpx;
	font-weight: 500;
}

.action-icon-wrap {
	margin-left: 20rpx;
}

.action-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 0.3;
}

.location-item {
	align-items: flex-start;
}

/* 商家介绍 */
.description-content {
	background-color: rgba(249, 250, 252, 0.7);
	border-radius: 24rpx;
	padding: 28rpx;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.description-text {
	font-size: 30rpx;
	color: #444;
	line-height: 1.7;
	white-space: pre-wrap;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 商家相册 */
.photos-grid {
	display: flex;
	flex-wrap: wrap;
	margin: 0 -8rpx;
}

.photo-item {
	width: calc(33.33% - 16rpx);
	aspect-ratio: 1;
	margin: 8rpx;
	border-radius: 20rpx;
	overflow: hidden;
	background-color: #f5f7fa;
	box-shadow: 0 8rpx 15rpx rgba(0, 0, 0, 0.05);
	position: relative;
	transform: translateZ(0);
	transition: transform 0.2s ease;
}

.photo-item:active {
	transform: scale(0.97);
}

.photo-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* 二维码 */
.qrcode-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 0;
}

.qrcode-image {
	width: 320rpx;
	height: 320rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.1);
	border-radius: 20rpx;
	background-color: white;
	padding: 20rpx;
}

.qrcode-tip {
	font-size: 26rpx;
	color: #888;
	background-color: rgba(39, 56, 192, 0.08);
	padding: 12rpx 30rpx;
	border-radius: 30rpx;
}

/* 底部工具栏 */
.bottom-toolbar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	height: 110rpx;
	background-color: rgba(255, 255, 255, 0.95);
	box-shadow: 0 -5rpx 20rpx rgba(0, 0, 0, 0.05);
	display: flex;
	align-items: center;
	padding: 0;
	z-index: 100;
	border-top: 1rpx solid rgba(0, 0, 0, 0.03);
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
}

.toolbar-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	flex: 1;
	position: relative;
	transition: opacity 0.2s ease;
}

.toolbar-btn:active {
	opacity: 0.7;
}

.toolbar-call-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 85%;
	flex: 2.5;
	background-color: #2738C0;
	border-radius: 36rpx;
	margin: 12rpx 15rpx;
	box-shadow: 0 6rpx 15rpx rgba(39, 56, 192, 0.2);
	transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.toolbar-call-btn:active {
	transform: scale(0.97);
	box-shadow: 0 4rpx 10rpx rgba(39, 56, 192, 0.2);
}

.call-text {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 600;
}

.call-subtext {
	font-size: 18rpx;
	color: rgba(255, 255, 255, 0.85);
	margin-top: 2rpx;
	white-space: nowrap;
}

.toolbar-icon {
	width: 44rpx;
	height: 44rpx;
	margin-bottom: 6rpx;
}

.toolbar-text {
	font-size: 22rpx;
	color: #444;
}

/* 添加联系电话下方的提示样式 */
.info-tip {
	font-size: 22rpx;
	color: #3846cd;
	margin-top: 6rpx;
}

/* 悬浮海报按钮 */
.float-poster-btn {
	position: fixed;
	right: 30rpx;
	bottom: 200rpx;
	width: 100rpx;
	height: 100rpx;
	background: rgba(240, 240, 240, 0.9);
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
	z-index: 90;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1rpx solid rgba(230, 230, 230, 0.6);
	transition: all 0.2s ease;
}

.float-poster-btn:active {
	transform: scale(0.95);
	box-shadow: 0 3rpx 10rpx rgba(0,0,0,0.08);
}

.poster-icon {
	width: 40rpx;
	height: 40rpx;
	margin-bottom: 4rpx;
}

.poster-text {
	font-size: 20rpx;
	color: #444;
	line-height: 1;
}

/* 添加举报图标样式 */
.report-icon-wrap {
	width: 72rpx;
	height: 72rpx;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.report-icon-wrap:active {
	transform: scale(0.92);
	background-color: rgba(255, 255, 255, 0.25);
}

.report-icon {
	width: 36rpx;
	height: 36rpx;
	opacity: 1;
	filter: brightness(0) invert(1);
}

/* 举报提示卡片样式 */
.report-tip-section {
	margin: 30rpx 30rpx 0;
	background-color: #f9fafe;
	border-radius: 28rpx;
	padding: 20rpx 24rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.report-tip-content {
	display: flex;
	align-items: center;
}

.report-tip-icon {
	width: 28rpx;
	height: 28rpx;
	margin-right: 12rpx;
}

.report-tip-text {
	flex: 1;
	font-size: 24rpx;
	color: #666;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.report-arrow-icon {
	width: 24rpx;
	height: 24rpx;
	opacity: 0.3;
}

/* 确保所有文本在真机上正确显示的全局设置 */
text, view {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 消费红包相关样式 */
.consume-red-packet-container {
	padding: 20rpx;
}

.consume-activity-card {
	background: linear-gradient(135deg, #fff5f5, #fff1f0);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(255, 69, 58, 0.1);
	border: 1px solid rgba(255, 69, 58, 0.15);
}

.activity-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.merchant-logo {
	width: 80rpx;
	height: 80rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
	border: 1px solid rgba(255, 255, 255, 0.6);
}

.activity-info {
	flex: 1;
}

.activity-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff4538;
	margin-bottom: 10rpx;
	display: block;
}

.activity-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.activity-details {
	margin-top: 20rpx;
}

.detail-item {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 20rpx;
	display: block;
}

.button-area {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20rpx;
}

.draw-button {
	background: linear-gradient(135deg, #ff4538, #ff7b6c);
	color: #fff;
	font-size: 28rpx;
	padding: 10rpx 40rpx;
	border-radius: 30rpx;
	border: none;
	box-shadow: 0 4rpx 8rpx rgba(255, 69, 58, 0.3);
}

.detail-text {
	font-size: 24rpx;
	color: #999;
}

.more-text {
	font-size: 24rpx;
	color: #ff4538;
	padding: 4rpx 12rpx;
	background: rgba(255, 69, 58, 0.1);
	border-radius: 20rpx;
}

/* 红包弹窗样式 */
.red-packet-popup {
	background-color: transparent;
	width: 600rpx;
}

.drawing-area {
	background: rgba(0, 0, 0, 0.6);
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.red-packet-animation {
	text-align: center;
}

.red-packet-img {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.drawing-text {
	font-size: 32rpx;
	color: #fff;
	margin-top: 20rpx;
}

.result-area {
	background: linear-gradient(135deg, #FA2A2D, #FF4E4E);
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 16rpx rgba(255, 0, 0, 0.2);
}

.result-content {
	text-align: center;
}

.result-icon {
	width: 160rpx;
	height: 160rpx;
	margin-bottom: 20rpx;
}

.result-title {
	font-size: 36rpx;
	color: #FFE4B5;
	margin-bottom: 20rpx;
	display: block;
}

.result-amount {
	font-size: 80rpx;
	font-weight: bold;
	color: #FFFFFF;
	margin: 20rpx 0;
	display: block;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.result-desc {
	font-size: 28rpx;
	color: #FFE4B5;
	margin-bottom: 40rpx;
	display: block;
}

.result-buttons {
	display: flex;
	justify-content: space-between;
	margin-top: 30rpx;
}

.use-now-btn, .close-btn {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	margin: 0 10rpx;
}

.use-now-btn {
	background-color: #FFFFFF;
	color: #FA2A2D;
	border: none;
}

.close-btn {
	background-color: rgba(255, 255, 255, 0.2);
	color: #FFFFFF;
	border: 1rpx solid rgba(255, 255, 255, 0.5);
}
</style> 