<template>
  <view class="visitors-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-left" @click="goBack">
        <image src="/static/images/tabbar/返回键.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">我的访客</view>
      <view class="navbar-right"></view>
    </view>
    
    <!-- 访客统计 -->
    <view class="visitors-stats" :style="{ marginTop: (navbarHeight + 10) + 'px' }">
      <view class="stats-item">
        <view class="stats-number">{{ totalVisitors }}</view>
        <view class="stats-label">总访问</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{ todayVisitors }}</view>
        <view class="stats-label">今日访问</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{ uniqueVisitors }}</view>
        <view class="stats-label">访客数</view>
      </view>
    </view>
    
    <!-- 访客列表 -->
    <view class="visitors-list">
      <!-- 日期分组 -->
      <block v-for="(group, groupIndex) in visitorGroups" :key="groupIndex">
        <view class="date-header">
          <view class="date-line"></view>
          <view class="date-text">{{ group.date }}</view>
          <view class="date-line"></view>
        </view>
        
        <!-- 访客列表 -->
        <view class="visitor-item" v-for="(visitor, index) in group.visitors" :key="index" @click="goToUserProfile(visitor)">
          <!-- 访客头像 -->
          <view class="visitor-avatar">
            <image :src="visitor.avatar || '/static/images/default-avatar.png'" class="avatar-img" mode="aspectFill"></image>
          </view>
          
          <!-- 访客信息 -->
          <view class="visitor-info">
            <view class="visitor-name">{{ visitor.nickname }}</view>
            <view class="visitor-signature">{{ visitor.signature || '这个人很懒，什么都没留下' }}</view>
          </view>
          
          <!-- 访问时间和次数 -->
          <view class="visitor-stats">
            <view class="visit-time">{{ visitor.visitTime }}</view>
            <view class="visit-count" v-if="visitor.visitCount > 1">来访 {{ visitor.visitCount }} 次</view>
          </view>
        </view>
      </block>
      
      <!-- 无数据提示 -->
      <view class="empty-container" v-if="visitorGroups.length === 0">
        <image src="/static/images/empty.png" class="empty-icon"></image>
        <view class="empty-text">暂无访客记录</view>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" v-if="visitorGroups.length > 0 && hasMore">
        <text class="loading-text">加载中...</text>
      </view>
      <view class="no-more" v-if="visitorGroups.length > 0 && !hasMore">
        <text class="no-more-text">没有更多了</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64,
      totalVisitors: 189,
      todayVisitors: 12,
      uniqueVisitors: 68,
      visitorGroups: [],
      page: 1,
      pageSize: 10,
      hasMore: true,
      isLoading: false
    }
  },
  created() {
    // 获取状态栏高度
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
    this.navbarHeight = this.statusBarHeight + 44;
  },
  onLoad() {
    this.getVisitorsList();
  },
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.page++;
      this.getVisitorsList();
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 获取访客列表
    getVisitorsList() {
      if (this.isLoading) return;
      this.isLoading = true;
      
      // 模拟获取访客列表数据
      setTimeout(() => {
        // 模拟数据
        const today = '今天';
        const yesterday = '昨天';
        const threeDaysAgo = '3天前';
        
        const mockData = [
          {
            date: today,
            visitors: [
              {
                userId: '3001',
                nickname: '张三',
                avatar: '/static/images/default-avatar.png',
                signature: '生活不止眼前的苟且，还有诗和远方',
                visitTime: '10:30',
                visitCount: 5
              },
              {
                userId: '3002',
                nickname: '李四',
                avatar: '/static/images/default-avatar.png',
                signature: '做一个简单的人',
                visitTime: '09:15',
                visitCount: 1
              }
            ]
          },
          {
            date: yesterday,
            visitors: [
              {
                userId: '3003',
                nickname: '王五',
                avatar: '/static/images/default-avatar.png',
                signature: '每天都是新的开始',
                visitTime: '19:45',
                visitCount: 3
              },
              {
                userId: '3004',
                nickname: '赵六',
                avatar: '/static/images/default-avatar.png',
                signature: '人生如戏，全靠演技',
                visitTime: '16:20',
                visitCount: 2
              }
            ]
          },
          {
            date: threeDaysAgo,
            visitors: [
              {
                userId: '3005',
                nickname: '钱七',
                avatar: '/static/images/default-avatar.png',
                signature: '不忘初心，方得始终',
                visitTime: '14:10',
                visitCount: 1
              }
            ]
          }
        ];
        
        // 第一页直接赋值，后续页面追加
        if (this.page === 1) {
          this.visitorGroups = mockData;
        } else {
          // 模拟加载更多数据
          const moreData = [
            {
              date: '一周前',
              visitors: [
                {
                  userId: '3006',
                  nickname: '孙八',
                  avatar: '/static/images/default-avatar.png',
                  signature: '努力奋斗，创造未来',
                  visitTime: '11:30',
                  visitCount: 1
                },
                {
                  userId: '3007',
                  nickname: '周九',
                  avatar: '/static/images/default-avatar.png',
                  signature: '简单生活，快乐每一天',
                  visitTime: '08:45',
                  visitCount: 4
                }
              ]
            }
          ];
          
          this.visitorGroups = [...this.visitorGroups, ...moreData];
        }
        
        // 判断是否还有更多数据
        this.hasMore = this.page < 2; // 模拟只有2页数据
        this.isLoading = false;
      }, 500);
    },
    
    // 跳转到用户主页
    goToUserProfile(visitor) {
      uni.navigateTo({
        url: `/pages/user-center/profile?userId=${visitor.userId}`,
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '查看用户主页功能已实现',
            icon: 'success',
            duration: 2000
          });
        }
      });
    }
  }
}
</script>

<style>
.visitors-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background-color: #0052CC;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 999;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.navbar-right {
  width: 80rpx;
}

/* 访客统计 */
.visitors-stats {
  display: flex;
  background-color: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  padding: 30rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.stats-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.stats-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background-color: #f0f0f0;
}

.stats-number {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
}

/* 访客列表样式 */
.visitors-list {
  padding: 0 20rpx 20rpx;
}

/* 日期分组 */
.date-header {
  display: flex;
  align-items: center;
  margin: 30rpx 0 20rpx;
}

.date-line {
  flex: 1;
  height: 1px;
  background-color: #f0f0f0;
}

.date-text {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 访客列表项 */
.visitor-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.visitor-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50rpx;
}

.visitor-info {
  flex: 1;
  overflow: hidden;
}

.visitor-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.visitor-signature {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.visitor-stats {
  text-align: right;
}

.visit-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.visit-count {
  font-size: 22rpx;
  color: #0052CC;
  background-color: rgba(0, 82, 204, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text, .no-more-text {
  font-size: 24rpx;
  color: #999;
}
</style> 