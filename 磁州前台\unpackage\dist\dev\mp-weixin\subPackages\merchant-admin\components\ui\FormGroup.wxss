/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.form-group {
  margin-bottom: 20px;
}
.form-label {
  margin-bottom: 8px;
}
.label-text {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}
.label-text.required::after {
  content: " *";
  color: #FF3B30;
}
.form-field {
  position: relative;
}
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

/* 基础表单输入框样式 */
.form-input {
  width: 100%;
  height: 44px;
  background-color: #F8FAFC;
  border-radius: 8px;
  padding: 0 15px;
  font-size: 15px;
  color: #333;
  box-sizing: border-box;
}

/* 价格输入框样式 */
.price-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #F8FAFC;
  border-radius: 8px;
  padding: 0 15px;
  height: 44px;
}
.price-symbol {
  font-size: 16px;
  color: #333;
  margin-right: 5px;
}
.price-input {
  flex: 1;
  height: 44px;
  font-size: 15px;
  background-color: transparent;
  padding: 0;
}

/* 数字选择器样式 */
.number-picker {
  display: flex;
  align-items: center;
  height: 44px;
}
.number-btn {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: #F0F0F0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: #666;
}
.number-input {
  width: 60px;
  height: 32px;
  text-align: center;
  margin: 0 8px;
  background-color: #F8FAFC;
  border-radius: 4px;
  font-size: 15px;
}
.unit-text {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

/* 单选按钮组样式 */
.radio-group {
  display: flex;
  margin-bottom: 15px;
}
.radio-item {
  display: flex;
  align-items: center;
  margin-right: 24px;
}
.radio-item.active .radio-label {
  color: #0A84FF;
}
.radio-dot {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 1px solid #D1D1D6;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.active .radio-dot {
  border-color: #0A84FF;
}
.radio-dot-inner {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #0A84FF;
}
.radio-label {
  font-size: 14px;
  color: #333;
}