/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 移除阿里妈妈字体引用，改用系统默认字体 */
/* 移除原有阿里妈妈字体定义
$uni-font-family: 'AlimamaShuHeiTi';
$uni-font-family-text: 'AlimamaShuHeiTi', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
*/
body, html, #app, .index-container {
  font-family: "AlimamaShuHeiTi", -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}
.verification-container {
  min-height: 100vh;
  background-color: #F5F7FA;
  padding-bottom: 20px;
}

/* 导航栏样式 */
.navbar {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #27ae60, #219150);
  color: #fff;
  padding: 44px 16px 15px;
  display: flex;
  align-items: center;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(39, 174, 96, 0.2);
}
.navbar-back {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}
.navbar-right {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.help-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* 核销功能模块样式 */
.verification-modules {
  padding: 20px 15px;
}
.module-card {
  background: #FFFFFF;
  border-radius: 16px;
  margin-bottom: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s, box-shadow 0.2s;
}
.module-card:active {
  transform: scale(0.98);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
}
.module-icon {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18px;
}
.module-icon svg {
  width: 28px;
  height: 28px;
}
.module-icon.scan {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}
.module-icon.manual {
  background-color: rgba(41, 128, 185, 0.15);
  color: #2980b9;
}
.module-icon.records {
  background-color: rgba(243, 156, 18, 0.15);
  color: #f39c12;
}
.module-content {
  flex: 1;
}
.module-title {
  font-size: 17px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
}
.module-desc {
  font-size: 13px;
  color: #999;
}
.module-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrow-icon {
  width: 8px;
  height: 8px;
  border-top: 2px solid #CCC;
  border-right: 2px solid #CCC;
  transform: rotate(45deg);
}

/* 最近核销记录样式 */
.recent-records {
  margin: 0 15px 20px;
  background: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}
.section-title {
  font-size: 17px;
  font-weight: 600;
  color: #333;
}
.view-all {
  font-size: 14px;
  color: #2980b9;
  font-weight: 500;
}
.empty-records {
  padding: 30px 0;
  text-align: center;
}
.empty-text {
  font-size: 14px;
  color: #999;
}
.record-item {
  display: flex;
  align-items: center;
  padding: 14px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.record-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.record-type {
  width: 60px;
  height: 28px;
  border-radius: 14px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  font-weight: 500;
}
.type-group {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}
.type-coupon {
  background-color: rgba(41, 128, 185, 0.15);
  color: #2980b9;
}
.type-flash {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}
.record-content {
  flex: 1;
}
.record-main {
  margin-bottom: 4px;
}
.record-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}
.record-code {
  font-size: 12px;
  color: #999;
}
.record-info {
  display: flex;
  font-size: 12px;
  color: #999;
}
.record-user {
  margin-right: 12px;
}
.record-status {
  font-size: 13px;
  font-weight: 500;
}
.status-success {
  color: #30D158;
}
.status-pending {
  color: #FF9500;
}
.status-failed {
  color: #FF453A;
}

/* 核销统计样式 */
.verification-stats {
  margin: 0 15px 20px;
  background: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}
.date-filter {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  padding: 6px 12px;
}
.date-text {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
.date-arrow {
  width: 8px;
  height: 8px;
  border-top: 2px solid #666;
  border-right: 2px solid #666;
  transform: rotate(135deg);
}
.stats-cards {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
}
.stats-card {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  position: relative;
}
.stats-card:not(:last-child):after {
  content: "";
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background: rgba(0, 0, 0, 0.05);
}
.stats-value {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin-bottom: 6px;
  display: block;
}
.stats-label {
  font-size: 12px;
  color: #999;
}
.stats-chart {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 20px;
}
.chart-title {
  font-size: 15px;
  color: #666;
  margin-bottom: 20px;
  display: block;
  font-weight: 500;
}
.chart-container {
  height: 160px;
}
.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 120px;
}
.chart-bar {
  width: 36px;
  background: linear-gradient(180deg, #27ae60, #219150);
  border-radius: 4px 4px 0 0;
  position: relative;
  display: flex;
  justify-content: center;
}
.bar-value {
  position: absolute;
  top: -20px;
  font-size: 12px;
  color: #666;
}
.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}
.label-text {
  width: 30px;
  text-align: center;
  font-size: 12px;
  color: #999;
}