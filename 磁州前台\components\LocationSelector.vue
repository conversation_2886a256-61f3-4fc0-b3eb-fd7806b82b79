<template>
	<view class="location-selector" @click="onLocationClick">
		<view class="location-display">
			<image class="location-icon" src="/static/images/location.png"></image>
			<text class="location-text">{{ locationName || '定位' }}</text>
			<image class="arrow-icon" src="/static/images/arrow_down.png"></image>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			locationName: '',
			locationData: null
		}
	},
	created() {
		// 监听位置更新
		uni.$on('locationUpdated', this.handleLocationUpdate);
		
		// 检查是否有缓存的位置信息
		this.checkCachedLocation();
	},
	beforeDestroy() {
		// 移除监听
		uni.$off('locationUpdated', this.handleLocationUpdate);
	},
	methods: {
		// 检查缓存位置
		checkCachedLocation() {
			uni.getStorage({
				key: 'userLocationName',
				success: (res) => {
					if (res.data) {
						this.locationName = res.data;
					}
				}
			});
		},
		
		// 处理位置更新事件
		handleLocationUpdate(location) {
			// 位置已更新，使用新位置获取地址
			if (location) {
				this.getAddressFromLocation(location.latitude, location.longitude);
			}
		},
		
		// 点击位置时
		onLocationClick() {
			// 显示位置选择器
			uni.showActionSheet({
				itemList: ['刷新当前位置', '选择其他位置'],
				success: (res) => {
					if (res.tapIndex === 0) {
						// 手动更新位置
						this.updateLocation();
					} else if (res.tapIndex === 1) {
						// 跳转到位置选择页面
						this.navigateToLocationSelector();
					}
				}
			});
		},
		
		// 手动更新位置
		updateLocation() {
			uni.showLoading({
				title: '获取位置中...'
			});
			
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					// 保存位置信息
					const location = {
						latitude: res.latitude,
						longitude: res.longitude,
						timestamp: Date.now()
					};
					
					// 储存位置信息
					uni.setStorage({
						key: 'userLocation',
						data: location
					});
					
					// 获取地址信息
					this.getAddressFromLocation(res.latitude, res.longitude);
				},
				fail: () => {
					uni.showToast({
						title: '位置获取失败',
						icon: 'none'
					});
				},
				complete: () => {
					uni.hideLoading();
				}
			});
		},
		
		// 根据坐标获取地址
		getAddressFromLocation(latitude, longitude) {
			// 调用逆地理编码API或使用uniCloud函数获取地址
			// 这里使用示例数据
			const addressInfo = {
				city: '北京市',
				district: '海淀区',
				street: '中关村大街'
			};
			
			// 设置位置名称（例如：海淀区）
			this.locationName = addressInfo.district;
			
			// 存储位置名称
			uni.setStorage({
				key: 'userLocationName',
				data: this.locationName
			});
			
			// 发布位置名称更新事件
			uni.$emit('locationNameUpdated', this.locationName);
		},
		
		// 导航到位置选择页面
		navigateToLocationSelector() {
			uni.navigateTo({
				url: '/pages/common/location-select'
			});
		}
	}
}
</script>

<style scoped>
.location-selector {
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	height: 60rpx;
}

.location-display {
	display: flex;
	align-items: center;
}

.location-icon {
	width: 30rpx;
	height: 30rpx;
	margin-right: 8rpx;
}

.location-text {
	font-size: 28rpx;
	color: #ffffff;
	max-width: 160rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.arrow-icon {
	width: 24rpx;
	height: 24rpx;
	margin-left: 8rpx;
}
</style> 