{"version": 3, "file": "bills.js", "sources": ["subPackages/payment/pages/bills.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3ViUGFja2FnZXNccGF5bWVudFxwYWdlc1xiaWxscy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"bills-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\r\n      <view class=\"navbar-left\" @click=\"goBack\">\r\n        <image src=\"/static/images/tabbar/返回键.png\" class=\"back-icon\"></image>\r\n      </view>\r\n      <view class=\"navbar-title\">收支记录</view>\r\n      <view class=\"navbar-right\"></view>\r\n    </view>\r\n    \r\n    <!-- 筛选区域 -->\r\n    <view class=\"filter-section\" :style=\"{ marginTop: (navbarHeight + 10) + 'px' }\">\r\n      <view class=\"filter-tabs\">\r\n        <view \r\n          class=\"filter-tab\" \r\n          v-for=\"(tab, index) in tabs\" \r\n          :key=\"index\"\r\n          :class=\"{'active': activeTab === index}\"\r\n          @click=\"switchTab(index)\"\r\n        >\r\n          {{ tab.name }}\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"filter-date\">\r\n        <picker \r\n          mode=\"date\" \r\n          fields=\"month\" \r\n          :value=\"selectedDate\" \r\n          @change=\"onDateChange\"\r\n        >\r\n          <view class=\"date-picker\">\r\n            <text>{{ formatDate(selectedDate) }}</text>\r\n            <image src=\"/static/images/tabbar/下拉.png\" class=\"date-icon\"></image>\r\n          </view>\r\n        </picker>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 收支统计 -->\r\n    <view class=\"statistics-section\">\r\n      <view class=\"statistics-item\">\r\n        <view class=\"statistics-label\">收入</view>\r\n        <view class=\"statistics-value income\">+{{ statistics.income.toFixed(2) }}</view>\r\n      </view>\r\n      <view class=\"statistics-divider\"></view>\r\n      <view class=\"statistics-item\">\r\n        <view class=\"statistics-label\">支出</view>\r\n        <view class=\"statistics-value expense\">-{{ statistics.expense.toFixed(2) }}</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 交易记录列表 -->\r\n    <view class=\"transaction-list\">\r\n      <view v-if=\"filteredTransactions.length > 0\">\r\n        <!-- 按日期分组的交易记录 -->\r\n        <block v-for=\"(group, date) in groupedTransactions\" :key=\"date\">\r\n          <view class=\"date-header\">\r\n            <view class=\"date-text\">{{ formatDayDate(date) }}</view>\r\n            <view class=\"date-summary\">\r\n              <text class=\"date-income\" v-if=\"getDateIncome(group) > 0\">收入: {{ getDateIncome(group).toFixed(2) }}</text>\r\n              <text class=\"date-expense\" v-if=\"getDateExpense(group) > 0\">支出: {{ getDateExpense(group).toFixed(2) }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"transaction-item\" v-for=\"(item, index) in group\" :key=\"index\">\r\n            <view class=\"transaction-left\">\r\n              <view class=\"transaction-icon\" :class=\"getTransactionTypeClass(item.type)\">\r\n                <image :src=\"getTransactionTypeIcon(item.type)\" class=\"type-icon\"></image>\r\n              </view>\r\n            </view>\r\n            <view class=\"transaction-center\">\r\n              <view class=\"transaction-title\">{{ item.title }}</view>\r\n              <view class=\"transaction-time\">{{ item.time }}</view>\r\n            </view>\r\n            <view class=\"transaction-right\">\r\n              <view class=\"transaction-amount\" :class=\"{'income': item.type === 'income', 'expense': item.type === 'expense'}\">\r\n                {{ item.type === 'income' ? '+' : '-' }}{{ item.amount.toFixed(2) }}\r\n              </view>\r\n              <view class=\"transaction-status\">{{ item.status }}</view>\r\n            </view>\r\n          </view>\r\n        </block>\r\n        \r\n        <!-- 加载更多 -->\r\n        <view class=\"load-more\" v-if=\"hasMoreData\" @click=\"loadMoreData\">\r\n          <text>加载更多</text>\r\n        </view>\r\n        <view class=\"no-more\" v-else>\r\n          <text>没有更多数据了</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-else class=\"empty-view\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n        <view class=\"empty-text\">暂无交易记录</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\n\r\n// 响应式状态\r\nconst statusBarHeight = ref(20);\r\nconst navbarHeight = ref(64);\r\nconst activeTab = ref(0);\r\nconst tabs = ref([\r\n  { name: '全部', type: 'all' },\r\n  { name: '收入', type: 'income' },\r\n  { name: '支出', type: 'expense' }\r\n]);\r\nconst selectedDate = ref(new Date().toISOString().split('T')[0].substr(0, 7));\r\nconst statistics = ref({\r\n  income: 0,\r\n  expense: 0\r\n});\r\nconst transactions = ref([\r\n  {\r\n    id: 'tx001',\r\n    title: '充值',\r\n    date: '2023-11-05',\r\n    time: '14:30',\r\n    fullTime: '2023-11-05 14:30',\r\n    amount: 100.00,\r\n    type: 'income',\r\n    status: '已完成'\r\n  },\r\n  {\r\n    id: 'tx002',\r\n    title: '服务支付',\r\n    date: '2023-11-03',\r\n    time: '09:15',\r\n    fullTime: '2023-11-03 09:15',\r\n    amount: 35.00,\r\n    type: 'expense',\r\n    status: '已完成'\r\n  },\r\n  {\r\n    id: 'tx003',\r\n    title: '提现',\r\n    date: '2023-11-03',\r\n    time: '16:22',\r\n    fullTime: '2023-11-03 16:22',\r\n    amount: 50.00,\r\n    type: 'expense',\r\n    status: '已完成'\r\n  },\r\n  {\r\n    id: 'tx004',\r\n    title: '充值',\r\n    date: '2023-11-01',\r\n    time: '11:05',\r\n    fullTime: '2023-11-01 11:05',\r\n    amount: 100.00,\r\n    type: 'income',\r\n    status: '已完成'\r\n  },\r\n  {\r\n    id: 'tx005',\r\n    title: '任务收入',\r\n    date: '2023-10-30',\r\n    time: '18:45',\r\n    fullTime: '2023-10-30 18:45',\r\n    amount: 88.00,\r\n    type: 'income',\r\n    status: '已完成'\r\n  }\r\n]);\r\nconst page = ref(1);\r\nconst pageSize = ref(10);\r\nconst hasMoreData = ref(true);\r\n\r\n// 计算属性\r\nconst filteredTransactions = computed(() => {\r\n  const yearMonth = selectedDate.value;\r\n  let result = transactions.value.filter(item => item.date.startsWith(yearMonth));\r\n  \r\n  if (activeTab.value !== 0) {\r\n    const type = tabs.value[activeTab.value].type;\r\n    result = result.filter(item => item.type === type);\r\n  }\r\n  \r\n  return result;\r\n});\r\n\r\nconst groupedTransactions = computed(() => {\r\n  const groups = {};\r\n  \r\n  filteredTransactions.value.forEach(item => {\r\n    if (!groups[item.date]) {\r\n      groups[item.date] = [];\r\n    }\r\n    groups[item.date].push(item);\r\n  });\r\n  \r\n  // 按日期降序排序\r\n  const sortedGroups = {};\r\n  Object.keys(groups).sort((a, b) => new Date(b) - new Date(a)).forEach(key => {\r\n    sortedGroups[key] = groups[key];\r\n  });\r\n  \r\n  return sortedGroups;\r\n});\r\n\r\n// 方法\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack();\r\n};\r\n\r\n// 切换标签\r\nconst switchTab = (index) => {\r\n  activeTab.value = index;\r\n  calculateStatistics();\r\n};\r\n\r\n// 日期选择变化\r\nconst onDateChange = (e) => {\r\n  selectedDate.value = e.detail.value;\r\n  page.value = 1;\r\n  getTransactions();\r\n};\r\n\r\n// 格式化日期显示\r\nconst formatDate = (dateStr) => {\r\n  if (!dateStr) return '';\r\n  const [year, month] = dateStr.split('-');\r\n  return `${year}年${month}月`;\r\n};\r\n\r\n// 格式化日期显示(日)\r\nconst formatDayDate = (dateStr) => {\r\n  if (!dateStr) return '';\r\n  const date = new Date(dateStr);\r\n  const year = date.getFullYear();\r\n  const month = date.getMonth() + 1;\r\n  const day = date.getDate();\r\n  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n  const weekDay = weekDays[date.getDay()];\r\n  return `${month}月${day}日 ${weekDay}`;\r\n};\r\n\r\n// 获取交易记录\r\nconst getTransactions = () => {\r\n  // 模拟API请求获取交易记录\r\n  // 实际应用中应该从服务器获取数据\r\n  setTimeout(() => {\r\n    // 如果是第一页，直接替换数据\r\n    if (page.value === 1) {\r\n      // 数据已经在data中初始化\r\n      calculateStatistics();\r\n    } else {\r\n      // 如果是加载更多，追加数据\r\n      if (page.value >= 3) {\r\n        hasMoreData.value = false;\r\n      } else {\r\n        // 模拟加载更多数据\r\n        const moreData = [\r\n          {\r\n            id: 'tx006',\r\n            title: '服务支付',\r\n            date: '2023-10-22',\r\n            time: '10:30',\r\n            fullTime: '2023-10-22 10:30',\r\n            amount: 25.00,\r\n            type: 'expense',\r\n            status: '已完成'\r\n          },\r\n          {\r\n            id: 'tx007',\r\n            title: '充值',\r\n            date: '2023-10-15',\r\n            time: '16:40',\r\n            fullTime: '2023-10-15 16:40',\r\n            amount: 50.00,\r\n            type: 'income',\r\n            status: '已完成'\r\n          }\r\n        ];\r\n        transactions.value = [...transactions.value, ...moreData];\r\n        calculateStatistics();\r\n      }\r\n    }\r\n  }, 500);\r\n};\r\n\r\n// 计算统计数据\r\nconst calculateStatistics = () => {\r\n  let income = 0;\r\n  let expense = 0;\r\n  \r\n  filteredTransactions.value.forEach(item => {\r\n    if (item.type === 'income') {\r\n      income += item.amount;\r\n    } else if (item.type === 'expense') {\r\n      expense += item.amount;\r\n    }\r\n  });\r\n  \r\n  statistics.value = { income, expense };\r\n};\r\n\r\n// 获取某天的收入总额\r\nconst getDateIncome = (transactions) => {\r\n  return transactions.reduce((sum, item) => {\r\n    return item.type === 'income' ? sum + item.amount : sum;\r\n  }, 0);\r\n};\r\n\r\n// 获取某天的支出总额\r\nconst getDateExpense = (transactions) => {\r\n  return transactions.reduce((sum, item) => {\r\n    return item.type === 'expense' ? sum + item.amount : sum;\r\n  }, 0);\r\n};\r\n\r\n// 加载更多数据\r\nconst loadMoreData = () => {\r\n  if (!hasMoreData.value) return;\r\n  \r\n  page.value++;\r\n  getTransactions();\r\n};\r\n\r\n// 获取交易类型对应的图标\r\nconst getTransactionTypeIcon = (type) => {\r\n  const icons = {\r\n    'income': '/static/images/tabbar/收入.png',\r\n    'expense': '/static/images/tabbar/支出.png'\r\n  };\r\n  return icons[type] || icons['income'];\r\n};\r\n\r\n// 获取交易类型对应的样式类\r\nconst getTransactionTypeClass = (type) => {\r\n  return {\r\n    'income-icon': type === 'income',\r\n    'expense-icon': type === 'expense'\r\n  };\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取状态栏高度\r\n  const sysInfo = uni.getSystemInfoSync();\r\n  statusBarHeight.value = sysInfo.statusBarHeight;\r\n  navbarHeight.value = statusBarHeight.value + 44;\r\n  \r\n  // 获取交易记录\r\n  getTransactions();\r\n});\r\n</script>\r\n\r\n<style>\r\n.bills-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30rpx;\r\n}\r\n\r\n/* 自定义导航栏 */\r\n.custom-navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 44px;\r\n  background-color: #0052CC;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  z-index: 999;\r\n}\r\n\r\n.navbar-left {\r\n  width: 80rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.navbar-title {\r\n  flex: 1;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.navbar-right {\r\n  width: 80rpx;\r\n}\r\n\r\n/* 筛选区域 */\r\n.filter-section {\r\n  background-color: #fff;\r\n  padding: 20rpx 30rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1rpx solid #f5f5f5;\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n}\r\n\r\n.filter-tab {\r\n  padding: 10rpx 30rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  position: relative;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.filter-tab.active {\r\n  color: #0052CC;\r\n  font-weight: 500;\r\n}\r\n\r\n.filter-tab.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40rpx;\r\n  height: 4rpx;\r\n  background-color: #0052CC;\r\n  border-radius: 2rpx;\r\n}\r\n\r\n.filter-date {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.date-picker {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.date-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  margin-left: 10rpx;\r\n}\r\n\r\n/* 统计区域 */\r\n.statistics-section {\r\n  background-color: #fff;\r\n  padding: 30rpx;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.statistics-item {\r\n  text-align: center;\r\n}\r\n\r\n.statistics-label {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.statistics-value {\r\n  font-size: 36rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.income {\r\n  color: #07c160;\r\n}\r\n\r\n.expense {\r\n  color: #f56c6c;\r\n}\r\n\r\n.statistics-divider {\r\n  width: 1px;\r\n  height: 60rpx;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n/* 交易记录列表 */\r\n.transaction-list {\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.date-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.date-text {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.date-summary {\r\n  font-size: 24rpx;\r\n}\r\n\r\n.date-income {\r\n  color: #07c160;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.date-expense {\r\n  color: #f56c6c;\r\n}\r\n\r\n.transaction-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background-color: #fff;\r\n  margin-bottom: 20rpx;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.transaction-left {\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.transaction-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 40rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.income-icon {\r\n  background-color: rgba(7, 193, 96, 0.1);\r\n}\r\n\r\n.expense-icon {\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n}\r\n\r\n.type-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n\r\n.transaction-center {\r\n  flex: 1;\r\n}\r\n\r\n.transaction-title {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.transaction-time {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.transaction-right {\r\n  text-align: right;\r\n}\r\n\r\n.transaction-amount {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.transaction-status {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more, .no-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.load-more {\r\n  color: #0052CC;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-view {\r\n  padding: 100rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.empty-icon {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n</style> \r\n", "import MiniProgramPage from 'C:/Users/<USER>/Desktop/新建文件夹/磁州前台/subPackages/payment/pages/bills.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "transactions", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;AA2GA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,YAAYA,cAAAA,IAAI,CAAC;AACvB,UAAM,OAAOA,cAAAA,IAAI;AAAA,MACf,EAAE,MAAM,MAAM,MAAM,MAAO;AAAA,MAC3B,EAAE,MAAM,MAAM,MAAM,SAAU;AAAA,MAC9B,EAAE,MAAM,MAAM,MAAM,UAAW;AAAA,IACjC,CAAC;AACD,UAAM,eAAeA,cAAAA,KAAI,oBAAI,KAAI,GAAG,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC;AAC5E,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AACD,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,IACH,CAAC;AACD,UAAM,OAAOA,cAAAA,IAAI,CAAC;AACDA,kBAAG,IAAC,EAAE;AACvB,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAG5B,UAAM,uBAAuBC,cAAQ,SAAC,MAAM;AAC1C,YAAM,YAAY,aAAa;AAC/B,UAAI,SAAS,aAAa,MAAM,OAAO,UAAQ,KAAK,KAAK,WAAW,SAAS,CAAC;AAE9E,UAAI,UAAU,UAAU,GAAG;AACzB,cAAM,OAAO,KAAK,MAAM,UAAU,KAAK,EAAE;AACzC,iBAAS,OAAO,OAAO,UAAQ,KAAK,SAAS,IAAI;AAAA,MAClD;AAED,aAAO;AAAA,IACT,CAAC;AAED,UAAM,sBAAsBA,cAAQ,SAAC,MAAM;AACzC,YAAM,SAAS,CAAA;AAEf,2BAAqB,MAAM,QAAQ,UAAQ;AACzC,YAAI,CAAC,OAAO,KAAK,IAAI,GAAG;AACtB,iBAAO,KAAK,IAAI,IAAI;QACrB;AACD,eAAO,KAAK,IAAI,EAAE,KAAK,IAAI;AAAA,MAC/B,CAAG;AAGD,YAAM,eAAe,CAAA;AACrB,aAAO,KAAK,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,QAAQ,SAAO;AAC3E,qBAAa,GAAG,IAAI,OAAO,GAAG;AAAA,MAClC,CAAG;AAED,aAAO;AAAA,IACT,CAAC;AAID,UAAM,SAAS,MAAM;AACnBC,oBAAG,MAAC,aAAY;AAAA,IAClB;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,gBAAU,QAAQ;AAClB;IACF;AAGA,UAAM,eAAe,CAAC,MAAM;AAC1B,mBAAa,QAAQ,EAAE,OAAO;AAC9B,WAAK,QAAQ;AACb;IACF;AAGA,UAAM,aAAa,CAAC,YAAY;AAC9B,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,CAAC,MAAM,KAAK,IAAI,QAAQ,MAAM,GAAG;AACvC,aAAO,GAAG,IAAI,IAAI,KAAK;AAAA,IACzB;AAGA,UAAM,gBAAgB,CAAC,YAAY;AACjC,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAChB,WAAK,YAAc;AAChC,YAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,YAAM,MAAM,KAAK;AACjB,YAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,YAAM,UAAU,SAAS,KAAK,OAAQ,CAAA;AACtC,aAAO,GAAG,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,IACpC;AAGA,UAAM,kBAAkB,MAAM;AAG5B,iBAAW,MAAM;AAEf,YAAI,KAAK,UAAU,GAAG;AAEpB;QACN,OAAW;AAEL,cAAI,KAAK,SAAS,GAAG;AACnB,wBAAY,QAAQ;AAAA,UAC5B,OAAa;AAEL,kBAAM,WAAW;AAAA,cACf;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU;AAAA,gBACV,QAAQ;AAAA,gBACR,MAAM;AAAA,gBACN,QAAQ;AAAA,cACT;AAAA,cACD;AAAA,gBACE,IAAI;AAAA,gBACJ,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,UAAU;AAAA,gBACV,QAAQ;AAAA,gBACR,MAAM;AAAA,gBACN,QAAQ;AAAA,cACT;AAAA,YACX;AACQ,yBAAa,QAAQ,CAAC,GAAG,aAAa,OAAO,GAAG,QAAQ;AACxD;UACD;AAAA,QACF;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,sBAAsB,MAAM;AAChC,UAAI,SAAS;AACb,UAAI,UAAU;AAEd,2BAAqB,MAAM,QAAQ,UAAQ;AACzC,YAAI,KAAK,SAAS,UAAU;AAC1B,oBAAU,KAAK;AAAA,QACrB,WAAe,KAAK,SAAS,WAAW;AAClC,qBAAW,KAAK;AAAA,QACjB;AAAA,MACL,CAAG;AAED,iBAAW,QAAQ,EAAE,QAAQ,QAAO;AAAA,IACtC;AAGA,UAAM,gBAAgB,CAACC,kBAAiB;AACtC,aAAOA,cAAa,OAAO,CAAC,KAAK,SAAS;AACxC,eAAO,KAAK,SAAS,WAAW,MAAM,KAAK,SAAS;AAAA,MACrD,GAAE,CAAC;AAAA,IACN;AAGA,UAAM,iBAAiB,CAACA,kBAAiB;AACvC,aAAOA,cAAa,OAAO,CAAC,KAAK,SAAS;AACxC,eAAO,KAAK,SAAS,YAAY,MAAM,KAAK,SAAS;AAAA,MACtD,GAAE,CAAC;AAAA,IACN;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,YAAY;AAAO;AAExB,WAAK;AACL;IACF;AAGA,UAAM,yBAAyB,CAAC,SAAS;AACvC,YAAM,QAAQ;AAAA,QACZ,UAAU;AAAA,QACV,WAAW;AAAA,MACf;AACE,aAAO,MAAM,IAAI,KAAK,MAAM,QAAQ;AAAA,IACtC;AAGA,UAAM,0BAA0B,CAAC,SAAS;AACxC,aAAO;AAAA,QACL,eAAe,SAAS;AAAA,QACxB,gBAAgB,SAAS;AAAA,MAC7B;AAAA,IACA;AAGAC,kBAAAA,UAAU,MAAM;AAEd,YAAM,UAAUF,oBAAI;AACpB,sBAAgB,QAAQ,QAAQ;AAChC,mBAAa,QAAQ,gBAAgB,QAAQ;AAG7C;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjWD,GAAG,WAAWG,SAAe;"}