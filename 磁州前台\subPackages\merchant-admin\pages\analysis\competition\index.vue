<template>
  <view class="competition-analysis-container">
    <!-- 自定义导航栏 -->
    <view class="navbar">
      <view class="navbar-back" @click="goBack">
        <view class="back-icon"></view>
      </view>
      <text class="navbar-title">竞争力分析</text>
      <view class="navbar-right">
        <view class="export-icon" @click="exportData">📊</view>
      </view>
    </view>
    
    <!-- 日期选择器 -->
    <view class="date-selector-container">
      <view class="date-selector" @click="showDatePicker">
        <text class="date-text">{{currentDateRange}}</text>
        <text class="date-icon">📅</text>
      </view>
    </view>
    
    <!-- 竞争力概览 -->
    <view class="overview-section">
      <view class="overview-card">
        <view class="overview-item">
          <text class="overview-value">{{competitionData.marketRanking}}</text>
          <text class="overview-label">市场排名</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{competitionData.marketShare}}%</text>
          <text class="overview-label">市场份额</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{competitionData.competitiveIndex}}</text>
          <text class="overview-label">竞争力指数</text>
        </view>
        <view class="overview-item">
          <text class="overview-value">{{competitionData.growthTrend}}</text>
          <text class="overview-label">增长趋势</text>
          <view :class="['trend-icon', competitionData.growthTrend === '上升' ? 'up' : 'down']"></view>
        </view>
      </view>
    </view>
    
    <!-- 行业对标分析 -->
    <view class="industry-section">
      <view class="section-header">
        <text class="section-title">行业对标分析</text>
        <text class="section-more" @click="navigateTo('./industry')">查看详情</text>
      </view>
      
      <view class="industry-card">
        <view class="industry-chart">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">行业对标雷达图</text>
          </view>
        </view>
        
        <view class="competitor-list">
          <text class="sub-title">主要竞争对手</text>
          <view 
            v-for="(competitor, index) in industryData.competitors" 
            :key="index"
            class="competitor-item">
            <view class="competitor-header">
              <view class="competitor-rank">{{index + 1}}</view>
              <text class="competitor-name">{{competitor.name}}</text>
              <view class="competitor-tag" v-if="index === 0">行业领先</view>
            </view>
            <view class="competitor-stats">
              <view class="stat-item">
                <text class="stat-label">市场份额</text>
                <text class="stat-value">{{competitor.marketShare}}%</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">客户满意度</text>
                <text class="stat-value">{{competitor.satisfaction}}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">产品数量</text>
                <text class="stat-value">{{competitor.productCount}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="industry-insights">
          <text class="sub-title">行业洞察</text>
          <view class="insights-list">
            <view 
              v-for="(insight, index) in industryData.insights" 
              :key="index"
              class="insight-item">
              <view class="insight-icon">{{insight.icon}}</view>
              <view class="insight-content">
                <text class="insight-text">{{insight.text}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 价格竞争力分析 -->
    <view class="price-section">
      <view class="section-header">
        <text class="section-title">价格竞争力分析</text>
        <text class="section-more" @click="navigateTo('./price')">查看详情</text>
      </view>
      
      <view class="price-card">
        <view class="price-chart">
          <!-- 这里应该是图表组件，暂时用占位符 -->
          <view class="chart-placeholder">
            <text class="chart-text">价格对比图</text>
          </view>
        </view>
        
        <view class="price-categories">
          <text class="sub-title">主要品类价格对比</text>
          <view class="category-list">
            <view 
              v-for="(category, index) in priceData.categories" 
              :key="index"
              class="price-category-item">
              <view class="category-header">
                <text class="category-name">{{category.name}}</text>
                <text :class="['price-position', {'positive': category.position === '低于平均', 'neutral': category.position === '平均水平', 'negative': category.position === '高于平均'}]">{{category.position}}</text>
              </view>
              <view class="price-comparison">
                <view class="price-bar">
                  <view class="market-low">{{category.marketLow}}</view>
                  <view class="price-range">
                    <view class="range-fill"></view>
                    <view class="our-price-marker" :style="{left: category.ourPricePosition + '%'}"></view>
                    <view class="average-price-marker" :style="{left: '50%'}"></view>
                  </view>
                  <view class="market-high">{{category.marketHigh}}</view>
                </view>
                <view class="price-labels">
                  <text class="our-price">我们: ¥{{category.ourPrice}}</text>
                  <text class="avg-price">平均: ¥{{category.avgPrice}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <view class="price-strategy">
          <text class="sub-title">价格策略建议</text>
          <view class="strategy-content">
            <text class="strategy-text">{{priceData.strategyTip}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 差异化优势识别 -->
    <view class="advantage-section">
      <view class="section-header">
        <text class="section-title">差异化优势识别</text>
        <text class="section-more" @click="navigateTo('./advantage')">查看详情</text>
      </view>
      
      <view class="advantage-card">
        <view class="advantage-list">
          <view 
            v-for="(advantage, index) in advantageData.advantages" 
            :key="index"
            class="advantage-item">
            <view class="advantage-icon" :style="{backgroundColor: advantage.color}">{{advantage.icon}}</view>
            <view class="advantage-content">
              <text class="advantage-title">{{advantage.title}}</text>
              <text class="advantage-desc">{{advantage.description}}</text>
              <view class="advantage-bar">
                <text class="bar-label">竞争优势</text>
                <view class="bar-container">
                  <view class="bar-fill" :style="{width: advantage.score + '%', backgroundColor: advantage.color}"></view>
                </view>
                <text class="bar-value">{{advantage.score}}%</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="advantage-tips">
          <view class="tip-icon">💡</view>
          <view class="tip-content">
            <text class="tip-title">差异化战略建议</text>
            <text class="tip-text">{{advantageData.strategyTip}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDateRange: '2023-05-01 至 2023-05-31',
      competitionData: {
        marketRanking: '3',
        marketShare: '15.6',
        competitiveIndex: '7.8',
        growthTrend: '上升'
      },
      industryData: {
        competitors: [
          {
            name: '鲜果优选',
            marketShare: '28.5',
            satisfaction: '4.8',
            productCount: '1,250'
          },
          {
            name: '每日生鲜',
            marketShare: '22.3',
            satisfaction: '4.6',
            productCount: '980'
          },
          {
            name: '磁州生活网',
            marketShare: '15.6',
            satisfaction: '4.7',
            productCount: '860'
          },
          {
            name: '鲜丰水果',
            marketShare: '12.8',
            satisfaction: '4.5',
            productCount: '720'
          },
          {
            name: '优鲜生活',
            marketShare: '10.2',
            satisfaction: '4.3',
            productCount: '650'
          }
        ],
        insights: [
          {
            icon: '📈',
            text: '行业整体呈现稳步增长态势，预计未来3年复合增长率保持在15%左右'
          },
          {
            icon: '🔍',
            text: '我们在客户满意度方面表现优异，是提升市场份额的关键优势'
          },
          {
            icon: '⚠️',
            text: '产品数量与行业领先者仍有差距，建议适当扩充产品线'
          }
        ]
      },
      priceData: {
        categories: [
          {
            name: '新鲜水果',
            position: '低于平均',
            marketLow: '¥8.5',
            marketHigh: '¥25.8',
            ourPrice: '12.5',
            avgPrice: '15.8',
            ourPricePosition: 30
          },
          {
            name: '有机蔬菜',
            position: '平均水平',
            marketLow: '¥5.2',
            marketHigh: '¥18.6',
            ourPrice: '10.8',
            avgPrice: '11.2',
            ourPricePosition: 48
          },
          {
            name: '进口食品',
            position: '高于平均',
            marketLow: '¥28.5',
            marketHigh: '¥86.5',
            ourPrice: '68.5',
            avgPrice: '58.2',
            ourPricePosition: 65
          },
          {
            name: '日用百货',
            position: '低于平均',
            marketLow: '¥12.8',
            marketHigh: '¥45.6',
            ourPrice: '18.5',
            avgPrice: '25.6',
            ourPricePosition: 25
          }
        ],
        strategyTip: '建议对进口食品类别进行价格调整，降低5-10%以提升竞争力；同时可适当提高新鲜水果类别价格，因其价格优势明显，有提价空间'
      },
      advantageData: {
        advantages: [
          {
            title: '本地化配送速度',
            description: '2小时极速配送，比竞争对手平均快50%',
            score: 92,
            icon: '🚚',
            color: '#1677FF'
          },
          {
            title: '产品新鲜度',
            description: '当日采摘，确保最佳新鲜度',
            score: 88,
            icon: '🥬',
            color: '#52c41a'
          },
          {
            title: '会员服务体系',
            description: '差异化会员权益，专属客服通道',
            score: 85,
            icon: '👑',
            color: '#fa8c16'
          },
          {
            title: '社区互动',
            description: '线上线下社区活动，增强客户粘性',
            score: 78,
            icon: '👥',
            color: '#eb2f96'
          },
          {
            title: '价格竞争力',
            description: '部分品类价格优势明显',
            score: 65,
            icon: '💰',
            color: '#722ed1'
          }
        ],
        strategyTip: '建议进一步强化本地化配送速度和产品新鲜度两大核心优势，在营销传播中重点突出这些差异化特点，同时完善会员服务体系，提升客户忠诚度'
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateTo(url) {
      uni.navigateTo({ url });
    },
    showDatePicker() {
      uni.showToast({
        title: '日期选择功能开发中',
        icon: 'none'
      });
    },
    exportData() {
      uni.showActionSheet({
        itemList: ['导出Excel', '导出PDF', '生成分析报告'],
        success: (res) => {
          uni.showToast({
            title: '导出功能开发中',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style>
.competition-analysis-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 16px 10px;
  background: linear-gradient(135deg, #1677FF, #065DD2);
  position: relative;
  z-index: 100;
}

.navbar-back {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  transform: rotate(45deg);
}

.navbar-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.export-icon {
  font-size: 20px;
  color: #fff;
}

.date-selector-container {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

.date-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.date-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.date-icon {
  font-size: 16px;
}

.overview-section, .industry-section, .price-section, .advantage-section {
  padding: 0 16px;
  margin-bottom: 20px;
}

.overview-card, .industry-card, .price-card, .advantage-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.overview-card {
  display: flex;
  justify-content: space-between;
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.overview-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #999;
}

.trend-icon {
  width: 0;
  height: 0;
  position: absolute;
  right: -15px;
  top: 8px;
}

.trend-icon.up {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 8px solid #52c41a;
}

.trend-icon.down {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid #f5222d;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 12px;
  color: #1677FF;
}

.industry-chart, .price-chart {
  height: 180px;
  margin-bottom: 16px;
}

.chart-placeholder {
  height: 100%;
  background-color: #f9f9f9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 14px;
  color: #999;
}

.sub-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.competitor-list {
  margin-bottom: 16px;
}

.competitor-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.competitor-item:last-child {
  border-bottom: none;
}

.competitor-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.competitor-rank {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-right: 8px;
}

.competitor-item:nth-child(1) .competitor-rank {
  background-color: #FFD700;
  color: #fff;
}

.competitor-item:nth-child(2) .competitor-rank {
  background-color: #C0C0C0;
  color: #fff;
}

.competitor-item:nth-child(3) .competitor-rank {
  background-color: #CD7F32;
  color: #fff;
}

.competitor-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.competitor-tag {
  font-size: 12px;
  color: #fff;
  background-color: #f5222d;
  padding: 2px 6px;
  border-radius: 2px;
}

.competitor-stats {
  display: flex;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.insights-list {
  
}

.insight-item {
  display: flex;
  margin-bottom: 12px;
}

.insight-item:last-child {
  margin-bottom: 0;
}

.insight-icon {
  font-size: 18px;
  margin-right: 12px;
}

.insight-content {
  flex: 1;
}

.insight-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.price-categories {
  margin-bottom: 16px;
}

.price-category-item {
  margin-bottom: 16px;
}

.price-category-item:last-child {
  margin-bottom: 0;
}

.category-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.price-position {
  font-size: 12px;
}

.price-position.positive {
  color: #52c41a;
}

.price-position.neutral {
  color: #faad14;
}

.price-position.negative {
  color: #f5222d;
}

.price-comparison {
  
}

.price-bar {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.market-low, .market-high {
  font-size: 12px;
  color: #999;
  width: 40px;
}

.market-high {
  text-align: right;
}

.price-range {
  flex: 1;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  margin: 0 8px;
  position: relative;
}

.range-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(to right, #52c41a, #f5222d);
  border-radius: 2px;
}

.our-price-marker {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background-color: #1677FF;
  top: -4px;
  transform: translateX(-50%);
}

.average-price-marker {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background-color: #faad14;
  top: -4px;
  transform: translateX(-50%);
}

.price-labels {
  display: flex;
  justify-content: space-between;
  padding: 0 40px;
}

.our-price, .avg-price {
  font-size: 12px;
}

.our-price {
  color: #1677FF;
}

.avg-price {
  color: #faad14;
}

.price-strategy, .advantage-tips {
  background-color: #f6ffed;
  padding: 12px;
  border-radius: 4px;
}

.strategy-content {
  
}

.strategy-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.advantage-list {
  margin-bottom: 16px;
}

.advantage-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.advantage-item:last-child {
  border-bottom: none;
}

.advantage-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 12px;
  color: #fff;
}

.advantage-content {
  flex: 1;
}

.advantage-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.advantage-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.advantage-bar {
  display: flex;
  align-items: center;
}

.bar-label {
  font-size: 12px;
  color: #999;
  width: 60px;
}

.bar-container {
  flex: 1;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 8px;
}

.bar-fill {
  height: 100%;
  border-radius: 3px;
}

.bar-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  width: 40px;
  text-align: right;
}

.advantage-tips {
  display: flex;
}

.tip-icon {
  font-size: 20px;
  margin-right: 12px;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.tip-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}
</style> 