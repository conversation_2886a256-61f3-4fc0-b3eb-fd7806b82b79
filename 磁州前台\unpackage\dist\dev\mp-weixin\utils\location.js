"use strict";
const common_vendor = require("../common/vendor.js");
const DEFAULT_LOCATION = {
  latitude: 36.313076,
  longitude: 114.347312,
  province: "河北省",
  city: "邯郸市",
  district: "磁县",
  address: "河北省邯郸市磁县",
  location: "河北省 邯郸市 磁县"
};
const getUserLocation = (options = {}) => {
  return new Promise((resolve, reject) => {
    const isRequestingLocation = common_vendor.index.getStorageSync("isRequestingLocation");
    if (isRequestingLocation === "true" || isRequestingLocation === true) {
      common_vendor.index.__f__("log", "at utils/location.js:22", "已有位置请求正在进行，避免重复请求");
      if (options.useDefaultOnFail !== false) {
        resolve({ ...DEFAULT_LOCATION, timestamp: Date.now() });
      } else {
        reject(new Error("已有位置请求正在进行"));
      }
      return;
    }
    common_vendor.index.setStorageSync("isRequestingLocation", "true");
    const savedLocation = common_vendor.index.getStorageSync("user_location");
    if (savedLocation && !options.forceRefresh) {
      common_vendor.index.__f__("log", "at utils/location.js:39", "使用本地存储的位置信息");
      common_vendor.index.removeStorageSync("isRequestingLocation");
      resolve(savedLocation);
      return;
    }
    const locationAuthChecked = common_vendor.index.getStorageSync("locationAuthChecked");
    common_vendor.index.getSetting({
      success: (res) => {
        if (res.authSetting && res.authSetting["scope.userLocation"]) {
          getLocationInfo(options, resolve, reject);
        } else if (!locationAuthChecked) {
          common_vendor.index.setStorageSync("locationAuthChecked", true);
          common_vendor.index.authorize({
            scope: "scope.userLocation",
            success: () => {
              getLocationInfo(options, resolve, reject);
            },
            fail: (err) => {
              common_vendor.index.__f__("log", "at utils/location.js:68", "用户拒绝授权位置权限:", err);
              common_vendor.index.removeStorageSync("isRequestingLocation");
              useDefaultLocation(options, resolve, reject);
            }
          });
        } else {
          common_vendor.index.__f__("log", "at utils/location.js:77", "用户之前已拒绝位置授权，使用默认位置");
          common_vendor.index.removeStorageSync("isRequestingLocation");
          useDefaultLocation(options, resolve, reject);
        }
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at utils/location.js:84", "获取设置失败:", err);
        common_vendor.index.removeStorageSync("isRequestingLocation");
        useDefaultLocation(options, resolve, reject);
      }
    });
  });
};
function getLocationInfo(options, resolve, reject) {
  common_vendor.index.getLocation({
    type: options.type || "gcj02",
    altitude: options.altitude || false,
    isHighAccuracy: options.isHighAccuracy || true,
    highAccuracyExpireTime: options.timeout || 3e3,
    success: (res) => {
      common_vendor.index.__f__("log", "at utils/location.js:101", "获取位置成功:", res);
      const location = {
        latitude: res.latitude,
        longitude: res.longitude,
        // 以下信息需要通过逆地理编码获取，这里使用默认值
        province: DEFAULT_LOCATION.province,
        city: DEFAULT_LOCATION.city,
        district: DEFAULT_LOCATION.district,
        address: DEFAULT_LOCATION.address,
        location: DEFAULT_LOCATION.location,
        timestamp: Date.now()
      };
      common_vendor.index.setStorageSync("user_location", location);
      common_vendor.index.removeStorageSync("isRequestingLocation");
      resolve(location);
    },
    fail: (err) => {
      common_vendor.index.__f__("error", "at utils/location.js:126", "获取位置失败:", err);
      common_vendor.index.removeStorageSync("isRequestingLocation");
      useDefaultLocation(options, resolve, reject);
    }
  });
}
function useDefaultLocation(options, resolve, reject) {
  if (options.useDefaultOnFail !== false) {
    common_vendor.index.__f__("log", "at utils/location.js:137", "使用默认位置");
    const defaultLocation = {
      ...DEFAULT_LOCATION,
      timestamp: Date.now()
    };
    common_vendor.index.setStorageSync("user_location", defaultLocation);
    resolve(defaultLocation);
  } else {
    reject(new Error("获取位置失败，且不允许使用默认位置"));
  }
}
exports.getUserLocation = getUserLocation;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/location.js.map
