{"version": 3, "file": "activityList.js", "sources": ["mock/activity/activityList.js"], "sourcesContent": ["// 活动列表模拟数据\r\nexport const activityList = [\r\n  {\r\n    id: 'activity-001',\r\n    title: '磁县第二届美食文化节',\r\n    startTime: '2024-04-15 10:00',\r\n    endTime: '2024-04-17 22:00',\r\n    location: '磁县中央广场',\r\n    category: '美食',\r\n    status: 'upcoming', // upcoming, ongoing, ended\r\n    image: '/static/images/activity/activity-1.jpg',\r\n    organizer: '磁县文化旅游局',\r\n    participants: 256,\r\n    maxParticipants: 500,\r\n    isFree: true,\r\n    isOfficial: true,\r\n    tags: ['美食', '文化', '展览']\r\n  },\r\n  {\r\n    id: 'activity-002',\r\n    title: '磁州窑非遗文化体验活动',\r\n    startTime: '2024-04-10 09:30',\r\n    endTime: '2024-04-10 16:30',\r\n    location: '磁州窑博物馆',\r\n    category: '文化',\r\n    status: 'upcoming',\r\n    image: '/static/images/activity/activity-2.jpg',\r\n    organizer: '磁州窑博物馆',\r\n    participants: 78,\r\n    maxParticipants: 100,\r\n    isFree: false,\r\n    price: 20,\r\n    isOfficial: true,\r\n    tags: ['非遗', '文化', '体验']\r\n  },\r\n  {\r\n    id: 'activity-003',\r\n    title: '春季健步走活动',\r\n    startTime: '2024-03-25 07:00',\r\n    endTime: '2024-03-25 09:00',\r\n    location: '磁县人民公园',\r\n    category: '运动',\r\n    status: 'upcoming',\r\n    image: '/static/images/activity/activity-3.jpg',\r\n    organizer: '磁县全民健身协会',\r\n    participants: 145,\r\n    maxParticipants: 300,\r\n    isFree: true,\r\n    isOfficial: false,\r\n    tags: ['健身', '户外', '公益']\r\n  },\r\n  {\r\n    id: 'activity-004',\r\n    title: '磁县青年创业分享会',\r\n    startTime: '2024-03-20 14:00',\r\n    endTime: '2024-03-20 17:00',\r\n    location: '磁县青年创业中心',\r\n    category: '创业',\r\n    status: 'ongoing',\r\n    image: '/static/images/activity/activity-4.jpg',\r\n    organizer: '磁县青年创业协会',\r\n    participants: 68,\r\n    maxParticipants: 100,\r\n    isFree: true,\r\n    isOfficial: false,\r\n    tags: ['创业', '分享', '交流']\r\n  },\r\n  {\r\n    id: 'activity-005',\r\n    title: '磁县春季招聘会',\r\n    startTime: '2024-03-18 09:00',\r\n    endTime: '2024-03-18 16:00',\r\n    location: '磁县体育馆',\r\n    category: '招聘',\r\n    status: 'ended',\r\n    image: '/static/images/activity/activity-5.jpg',\r\n    organizer: '磁县人力资源和社会保障局',\r\n    participants: 520,\r\n    maxParticipants: 1000,\r\n    isFree: true,\r\n    isOfficial: true,\r\n    tags: ['招聘', '就业', '人才']\r\n  }\r\n];\r\n\r\n// 活动分类\r\nexport const activityCategories = [\r\n  { id: 0, name: '全部' },\r\n  { id: 1, name: '美食' },\r\n  { id: 2, name: '文化' },\r\n  { id: 3, name: '运动' },\r\n  { id: 4, name: '创业' },\r\n  { id: 5, name: '招聘' },\r\n  { id: 6, name: '教育' },\r\n  { id: 7, name: '公益' },\r\n  { id: 8, name: '亲子' }\r\n];\r\n\r\n// 获取活动列表的API函数\r\nexport const fetchActivityList = (categoryId = 0, status = 'all', page = 1, pageSize = 10) => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      let result = [...activityList];\r\n      \r\n      // 按分类筛选\r\n      if (categoryId !== 0) {\r\n        const categoryMap = {\r\n          1: '美食',\r\n          2: '文化',\r\n          3: '运动',\r\n          4: '创业',\r\n          5: '招聘',\r\n          6: '教育',\r\n          7: '公益',\r\n          8: '亲子'\r\n        };\r\n        result = result.filter(item => item.category === categoryMap[categoryId]);\r\n      }\r\n      \r\n      // 按状态筛选\r\n      if (status !== 'all') {\r\n        result = result.filter(item => item.status === status);\r\n      }\r\n      \r\n      // 分页处理\r\n      const start = (page - 1) * pageSize;\r\n      const end = start + pageSize;\r\n      const data = result.slice(start, end);\r\n      \r\n      // 返回数据和分页信息\r\n      resolve({\r\n        list: data,\r\n        total: result.length,\r\n        hasMore: end < result.length\r\n      });\r\n    }, 500);\r\n  });\r\n};\r\n\r\n// 获取活动分类的API函数\r\nexport const fetchActivityCategories = () => {\r\n  return new Promise((resolve) => {\r\n    setTimeout(() => {\r\n      resolve(activityCategories);\r\n    }, 300);\r\n  });\r\n}; "], "names": [], "mappings": ";AACO,MAAM,eAAe;AAAA,EAC1B;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,EACxB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,EACxB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,EACxB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,EACxB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,EACxB;AACH;AAGO,MAAM,qBAAqB;AAAA,EAChC,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,EACrB,EAAE,IAAI,GAAG,MAAM,KAAM;AACvB;AAGY,MAAC,oBAAoB,CAAC,aAAa,GAAG,SAAS,OAAO,OAAO,GAAG,WAAW,OAAO;AAC5F,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,UAAI,SAAS,CAAC,GAAG,YAAY;AAG7B,UAAI,eAAe,GAAG;AACpB,cAAM,cAAc;AAAA,UAClB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACb;AACQ,iBAAS,OAAO,OAAO,UAAQ,KAAK,aAAa,YAAY,UAAU,CAAC;AAAA,MACzE;AAGD,UAAI,WAAW,OAAO;AACpB,iBAAS,OAAO,OAAO,UAAQ,KAAK,WAAW,MAAM;AAAA,MACtD;AAGD,YAAM,SAAS,OAAO,KAAK;AAC3B,YAAM,MAAM,QAAQ;AACpB,YAAM,OAAO,OAAO,MAAM,OAAO,GAAG;AAGpC,cAAQ;AAAA,QACN,MAAM;AAAA,QACN,OAAO,OAAO;AAAA,QACd,SAAS,MAAM,OAAO;AAAA,MAC9B,CAAO;AAAA,IACF,GAAE,GAAG;AAAA,EACV,CAAG;AACH;AAGY,MAAC,0BAA0B,MAAM;AAC3C,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,MAAM;AACf,cAAQ,kBAAkB;AAAA,IAC3B,GAAE,GAAG;AAAA,EACV,CAAG;AACH;;;"}