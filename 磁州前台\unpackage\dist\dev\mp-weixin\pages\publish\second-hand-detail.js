"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Math) {
  ReportCard();
}
const ReportCard = () => "../../components/ReportCard.js";
const _sfc_main = {
  __name: "second-hand-detail",
  setup(__props) {
    const statusBarHeight = common_vendor.ref(20);
    common_vendor.onMounted(() => {
      try {
        const sysInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = sysInfo.statusBarHeight || 20;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/publish/second-hand-detail.vue:223", "获取状态栏高度失败", e);
      }
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      const id = options.id || "";
      common_vendor.index.__f__("log", "at pages/publish/second-hand-detail.vue:233", "商品详情页ID:", id);
      loadRelatedGoods();
    });
    const goBack = () => {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    };
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    };
    const isCollected = common_vendor.ref(false);
    const goodsData = common_vendor.ref({
      id: "goods12345",
      title: "iPhone 13 Pro Max",
      price: "5999元",
      tags: ["95新", "无维修", "可面交"],
      publishTime: Date.now() - 864e5 * 2,
      // 2天前
      images: [
        "/static/images/goods1.jpg",
        "/static/images/goods2.jpg",
        "/static/images/goods3.jpg"
      ],
      type: "手机数码",
      condition: "95新",
      purchaseTime: "2023年1月",
      location: "磁县城区",
      description: "iPhone 13 Pro Max 256G 远峰蓝，2023年1月购买，无维修无进水，配件齐全，可面交验机。",
      details: [
        { label: "品牌型号", value: "iPhone 13 Pro Max" },
        { label: "内存容量", value: "256G" },
        { label: "颜色", value: "远峰蓝" },
        { label: "配件", value: "原装充电器、数据线、包装盒" },
        { label: "保修", value: "已过保" }
      ],
      tradeMethods: [
        {
          icon: "icon-face",
          title: "当面交易",
          description: "支持当面验机，确认无误后交易"
        },
        {
          icon: "icon-delivery",
          title: "快递交易",
          description: "支持快递发货，收到货后确认"
        }
      ],
      seller: {
        name: "张先生",
        avatar: "/static/images/avatar.png",
        type: "个人",
        rating: "A+",
        isVerified: true
      },
      contact: {
        name: "张先生",
        phone: "13912345678"
      }
    });
    const relatedGoods = common_vendor.ref([]);
    const loadRelatedGoods = () => {
      setTimeout(() => {
        relatedGoods.value = [
          {
            id: "goods001",
            title: "iPhone 12 Pro 128G",
            condition: "95新",
            price: "4599元",
            image: "/static/images/goods1.jpg",
            tags: ["无维修", "面交验机", "保修中"]
          },
          {
            id: "goods002",
            title: "iPhone 14 256G",
            condition: "99新",
            price: "6999元",
            image: "/static/images/goods2.jpg",
            tags: ["官换机", "发票齐全"]
          },
          {
            id: "goods003",
            title: "MacBook Pro 2022",
            condition: "9成新",
            price: "9800元",
            image: "/static/images/goods3.jpg",
            tags: ["M2芯片", "16G内存", "官方保修"]
          }
        ];
      }, 500);
    };
    const navigateToGoodsDetail = (goodsId) => {
      if (goodsId === goodsData.value.id) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/publish/second-hand-detail?id=${goodsId}`
      });
    };
    const navigateToGoodsList = (e) => {
      if (e)
        e.stopPropagation();
      const goodsCategory = goodsData.value.type || "";
      common_vendor.index.navigateTo({
        url: `/subPackages/service/pages/filter?type=second-hand&title=${encodeURIComponent("二手闲置")}&category=${encodeURIComponent(goodsCategory)}&active=second_hand`
      });
    };
    const toggleCollect = () => {
      isCollected.value = !isCollected.value;
      if (isCollected.value) {
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
    };
    const callPhone = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: goodsData.value.contact.phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    };
    const goToHome = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    const openChat = () => {
      if (!goodsData.value.seller || !goodsData.value.seller.id) {
        common_vendor.index.showToast({
          title: "无法获取卖家信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/chat/index?userId=${goodsData.value.seller.id}&username=${encodeURIComponent(goodsData.value.seller.name || "卖家")}`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$5,
        b: common_vendor.o(goBack),
        c: statusBarHeight.value + "px",
        d: common_vendor.t(goodsData.value.title),
        e: common_vendor.t(goodsData.value.price),
        f: common_vendor.f(goodsData.value.tags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        g: common_vendor.t(formatTime(goodsData.value.publishTime)),
        h: common_vendor.f(goodsData.value.images, (image, index, i0) => {
          return {
            a: image,
            b: index
          };
        }),
        i: common_vendor.t(goodsData.value.type),
        j: common_vendor.t(goodsData.value.condition),
        k: common_vendor.t(goodsData.value.purchaseTime),
        l: common_vendor.t(goodsData.value.location),
        m: common_vendor.t(goodsData.value.description),
        n: common_vendor.f(goodsData.value.details, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: common_vendor.t(item.value),
            c: index
          };
        }),
        o: common_vendor.f(goodsData.value.tradeMethods, (item, index, i0) => {
          return {
            a: common_vendor.n(item.icon),
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.description),
            d: index
          };
        }),
        p: goodsData.value.seller.avatar,
        q: common_vendor.t(goodsData.value.seller.name),
        r: common_vendor.t(goodsData.value.seller.type),
        s: common_vendor.t(goodsData.value.seller.rating),
        t: goodsData.value.seller.isVerified
      }, goodsData.value.seller.isVerified ? {} : {}, {
        v: common_vendor.t(goodsData.value.contact.name),
        w: common_vendor.t(goodsData.value.contact.phone),
        x: common_vendor.o(callPhone),
        y: common_vendor.f(relatedGoods.value.slice(0, 3), (item, index, i0) => {
          return common_vendor.e({
            a: item.image,
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.condition),
            d: common_vendor.f(item.tags.slice(0, 2), (tag, tagIndex, i1) => {
              return {
                a: common_vendor.t(tag),
                b: tagIndex
              };
            }),
            e: item.tags.length > 2
          }, item.tags.length > 2 ? {
            f: common_vendor.t(item.tags.length - 2)
          } : {}, {
            g: common_vendor.t(item.price),
            h: index,
            i: common_vendor.o(($event) => navigateToGoodsDetail(item.id), index)
          });
        }),
        z: relatedGoods.value.length === 0
      }, relatedGoods.value.length === 0 ? {
        A: common_assets._imports_1$3
      } : {}, {
        B: relatedGoods.value.length > 0
      }, relatedGoods.value.length > 0 ? {
        C: common_vendor.o(navigateToGoodsList)
      } : {}, {
        D: common_assets._imports_12,
        E: common_vendor.o(goToHome),
        F: common_assets._imports_3$2,
        G: common_vendor.o(toggleCollect),
        H: common_assets._imports_3$3,
        I: common_assets._imports_14,
        J: common_vendor.o(openChat),
        K: common_vendor.o(callPhone)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/publish/second-hand-detail.js.map
