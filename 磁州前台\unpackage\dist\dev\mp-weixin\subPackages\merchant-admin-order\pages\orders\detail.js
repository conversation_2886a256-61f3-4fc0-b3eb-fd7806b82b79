"use strict";
const common_vendor = require("../../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      orderId: "",
      orderData: {
        id: "1001",
        orderNo: "CZ20230501001",
        status: "pending",
        // pending, processing, completed, cancelled, refunding
        createTime: "2023-05-01 10:30:25",
        paymentMethod: "微信支付",
        paymentStatus: "paid",
        // paid, unpaid
        deliveryType: "delivery",
        // delivery, self-pickup
        productTotal: "118.00",
        deliveryFee: "10.00",
        discount: "0.00",
        totalAmount: "128.00",
        remark: "请尽快发货，谢谢！",
        customer: {
          id: "10086",
          name: "张三",
          avatar: "/static/images/default-avatar.png",
          phone: "13800138000"
        },
        address: {
          name: "张三",
          phone: "13800138000",
          province: "河北省",
          city: "邯郸市",
          district: "磁县",
          detail: "北辰小区3号楼2单元101"
        },
        items: [
          {
            id: "2001",
            name: "精品水果礼盒",
            spec: "3kg装",
            price: "88.00",
            quantity: 1,
            image: "/static/images/product-1.jpg"
          },
          {
            id: "2002",
            name: "有机蔬菜套餐",
            spec: "标准版",
            price: "30.00",
            quantity: 1,
            image: "/static/images/product-2.jpg"
          }
        ],
        logs: [
          {
            time: "2023-05-01 10:30:25",
            content: "用户下单成功"
          },
          {
            time: "2023-05-01 10:31:05",
            content: "用户完成支付"
          }
        ]
      },
      orderTimeline: [
        {
          title: "下单",
          time: "2023-05-01 10:30",
          completed: true,
          current: false
        },
        {
          title: "支付",
          time: "2023-05-01 10:31",
          completed: true,
          current: false
        },
        {
          title: "商家接单",
          time: "",
          completed: false,
          current: true
        },
        {
          title: "配送中",
          time: "",
          completed: false,
          current: false
        },
        {
          title: "已完成",
          time: "",
          completed: false,
          current: false
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    getStatusText(status) {
      const texts = {
        pending: "待处理",
        processing: "处理中",
        completed: "已完成",
        cancelled: "已取消",
        refunding: "退款中"
      };
      return texts[status] || "未知状态";
    },
    getStatusDescription(status) {
      const descriptions = {
        pending: "订单已提交，等待商家处理",
        processing: "商家正在处理订单",
        completed: "订单已完成",
        cancelled: "订单已取消",
        refunding: "订单正在申请退款"
      };
      return descriptions[status] || "";
    },
    contactCustomer() {
      common_vendor.index.showActionSheet({
        itemList: ["拨打电话", "发送消息"],
        success: (res) => {
          if (res.tapIndex === 0) {
            common_vendor.index.makePhoneCall({
              phoneNumber: this.orderData.customer.phone,
              fail: () => {
                common_vendor.index.showToast({
                  title: "拨打电话失败",
                  icon: "none"
                });
              }
            });
          } else if (res.tapIndex === 1) {
            common_vendor.index.showToast({
              title: "消息功能开发中",
              icon: "none"
            });
          }
        }
      });
    },
    processOrder() {
      common_vendor.index.showModal({
        title: "确认接单",
        content: "接单后将开始处理该订单，是否继续？",
        success: (res) => {
          if (res.confirm) {
            this.orderData.status = "processing";
            this.orderData.logs.push({
              time: this.getCurrentTime(),
              content: "商家已接单"
            });
            this.orderTimeline[2].completed = true;
            this.orderTimeline[2].current = false;
            this.orderTimeline[2].time = this.getCurrentTime();
            this.orderTimeline[3].current = true;
            common_vendor.index.showToast({
              title: "接单成功",
              icon: "success"
            });
          }
        }
      });
    },
    completeOrder() {
      common_vendor.index.showModal({
        title: "确认完成",
        content: "确认订单已完成？",
        success: (res) => {
          if (res.confirm) {
            this.orderData.status = "completed";
            this.orderData.logs.push({
              time: this.getCurrentTime(),
              content: "订单已完成"
            });
            this.orderTimeline[3].completed = true;
            this.orderTimeline[3].current = false;
            this.orderTimeline[3].time = this.getCurrentTime();
            this.orderTimeline[4].completed = true;
            this.orderTimeline[4].current = true;
            this.orderTimeline[4].time = this.getCurrentTime();
            common_vendor.index.showToast({
              title: "订单已完成",
              icon: "success"
            });
          }
        }
      });
    },
    cancelOrder() {
      common_vendor.index.showModal({
        title: "取消订单",
        content: "确认取消该订单？取消后将无法恢复",
        success: (res) => {
          if (res.confirm) {
            this.orderData.status = "cancelled";
            this.orderData.logs.push({
              time: this.getCurrentTime(),
              content: "商家取消订单"
            });
            common_vendor.index.showToast({
              title: "订单已取消",
              icon: "success"
            });
          }
        }
      });
    },
    printOrder() {
      common_vendor.index.showToast({
        title: "打印功能开发中",
        icon: "none"
      });
    },
    getCurrentTime() {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const hour = String(now.getHours()).padStart(2, "0");
      const minute = String(now.getMinutes()).padStart(2, "0");
      const second = String(now.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    loadOrderData() {
      common_vendor.index.__f__("log", "at subPackages/merchant-admin-order/pages/orders/detail.vue:389", "加载订单数据，ID:", this.orderId);
      setTimeout(() => {
      }, 500);
    }
  },
  onLoad(options) {
    this.orderId = options.id || "";
    this.loadOrderData();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($options.getStatusText($data.orderData.status)),
    c: common_vendor.t($options.getStatusDescription($data.orderData.status)),
    d: common_vendor.f($data.orderTimeline, (step, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(step.title),
        b: common_vendor.t(step.time),
        c: index < $data.orderTimeline.length - 1
      }, index < $data.orderTimeline.length - 1 ? {} : {}, {
        d: index,
        e: step.completed ? 1 : "",
        f: step.current ? 1 : ""
      });
    }),
    e: common_vendor.n("status-" + $data.orderData.status),
    f: common_vendor.t($data.orderData.orderNo),
    g: common_vendor.t($data.orderData.createTime),
    h: common_vendor.t($data.orderData.paymentMethod),
    i: common_vendor.t($data.orderData.paymentStatus === "paid" ? "已支付" : "待支付"),
    j: common_vendor.n("payment-" + $data.orderData.paymentStatus),
    k: $data.orderData.customer.avatar || "/static/images/default-avatar.png",
    l: common_vendor.t($data.orderData.customer.name),
    m: common_vendor.t($data.orderData.customer.id),
    n: common_vendor.o((...args) => $options.contactCustomer && $options.contactCustomer(...args)),
    o: $data.orderData.deliveryType === "delivery"
  }, $data.orderData.deliveryType === "delivery" ? {
    p: common_vendor.t($data.orderData.address.name),
    q: common_vendor.t($data.orderData.address.phone),
    r: common_vendor.t($data.orderData.address.province),
    s: common_vendor.t($data.orderData.address.city),
    t: common_vendor.t($data.orderData.address.district),
    v: common_vendor.t($data.orderData.address.detail)
  } : {}, {
    w: common_vendor.f($data.orderData.items, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.spec),
        d: common_vendor.t(item.price),
        e: common_vendor.t(item.quantity),
        f: index
      };
    }),
    x: common_vendor.t($data.orderData.productTotal),
    y: common_vendor.t($data.orderData.deliveryFee),
    z: $data.orderData.discount > 0
  }, $data.orderData.discount > 0 ? {
    A: common_vendor.t($data.orderData.discount)
  } : {}, {
    B: common_vendor.t($data.orderData.totalAmount),
    C: $data.orderData.remark
  }, $data.orderData.remark ? {
    D: common_vendor.t($data.orderData.remark)
  } : {}, {
    E: common_vendor.f($data.orderData.logs, (log, index, i0) => {
      return {
        a: common_vendor.t(log.time),
        b: common_vendor.t(log.content),
        c: index
      };
    }),
    F: $data.orderData.status === "pending"
  }, $data.orderData.status === "pending" ? {
    G: common_vendor.o((...args) => $options.processOrder && $options.processOrder(...args))
  } : {}, {
    H: $data.orderData.status === "processing"
  }, $data.orderData.status === "processing" ? {
    I: common_vendor.o((...args) => $options.completeOrder && $options.completeOrder(...args))
  } : {}, {
    J: ["pending", "processing"].includes($data.orderData.status)
  }, ["pending", "processing"].includes($data.orderData.status) ? {
    K: common_vendor.o((...args) => $options.cancelOrder && $options.cancelOrder(...args))
  } : {}, {
    L: common_vendor.o((...args) => $options.printOrder && $options.printOrder(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-order/pages/orders/detail.js.map
