"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const common_assets = require("../../../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      // 生日特权设置
      birthdaySettings: {
        enabled: true,
        validPeriod: "month",
        notifyDays: 3,
        smsNotify: true,
        wechatNotify: true,
        greetingText: "亲爱的会员，祝您生日快乐！我们为您准备了专属生日礼包，点击查看详情。"
      },
      // 生日特权列表
      birthdayPrivileges: [
        {
          id: 1,
          name: "生日优惠券",
          description: "赠送生日专属优惠券",
          selected: true
        },
        {
          id: 2,
          name: "积分翻倍",
          description: "生日期间消费积分翻倍",
          selected: true
        },
        {
          id: 3,
          name: "生日礼品",
          description: "到店领取生日礼品",
          selected: false
        },
        {
          id: 4,
          name: "专属折扣",
          description: "生日当天消费享受额外折扣",
          selected: true
        },
        {
          id: 5,
          name: "免费配送",
          description: "生日期间订单免费配送",
          selected: false
        }
      ],
      // 会员等级
      memberLevels: [
        {
          id: 1,
          name: "普通会员",
          memberCount: 2156,
          selected: false
        },
        {
          id: 2,
          name: "银卡会员",
          memberCount: 864,
          selected: true
        },
        {
          id: 3,
          name: "金卡会员",
          memberCount: 426,
          selected: true
        },
        {
          id: 4,
          name: "钻石会员",
          memberCount: 116,
          selected: true
        }
      ]
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    toggleBirthdayPrivilege(e) {
      this.birthdaySettings.enabled = e.detail.value;
    },
    setValidPeriod(period) {
      this.birthdaySettings.validPeriod = period;
    },
    toggleSmsNotify(e) {
      this.birthdaySettings.smsNotify = e.detail.value;
    },
    toggleWechatNotify(e) {
      this.birthdaySettings.wechatNotify = e.detail.value;
    },
    togglePrivilege(privilege) {
      const index = this.birthdayPrivileges.findIndex((item) => item.id === privilege.id);
      if (index !== -1) {
        this.birthdayPrivileges[index].selected = !this.birthdayPrivileges[index].selected;
      }
    },
    configPrivilege(privilege) {
      common_vendor.index.showToast({
        title: `${privilege.name}设置功能开发中`,
        icon: "none"
      });
    },
    addPrivilege() {
      common_vendor.index.showToast({
        title: "添加特权功能开发中",
        icon: "none"
      });
    },
    toggleLevel(level) {
      const index = this.memberLevels.findIndex((item) => item.id === level.id);
      if (index !== -1) {
        this.memberLevels[index].selected = !this.memberLevels[index].selected;
      }
    },
    saveSettings() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "生日特权设置保存成功",
          icon: "success"
        });
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.birthdaySettings.enabled,
    c: common_vendor.o((...args) => $options.toggleBirthdayPrivilege && $options.toggleBirthdayPrivilege(...args)),
    d: $data.birthdaySettings.enabled
  }, $data.birthdaySettings.enabled ? {
    e: $data.birthdaySettings.validPeriod === "day" ? 1 : "",
    f: common_vendor.o(($event) => $options.setValidPeriod("day")),
    g: $data.birthdaySettings.validPeriod === "week" ? 1 : "",
    h: common_vendor.o(($event) => $options.setValidPeriod("week")),
    i: $data.birthdaySettings.validPeriod === "month" ? 1 : "",
    j: common_vendor.o(($event) => $options.setValidPeriod("month")),
    k: $data.birthdaySettings.notifyDays,
    l: common_vendor.o(($event) => $data.birthdaySettings.notifyDays = $event.detail.value),
    m: $data.birthdaySettings.smsNotify,
    n: common_vendor.o((...args) => $options.toggleSmsNotify && $options.toggleSmsNotify(...args)),
    o: $data.birthdaySettings.wechatNotify,
    p: common_vendor.o((...args) => $options.toggleWechatNotify && $options.toggleWechatNotify(...args)),
    q: common_vendor.f($data.birthdayPrivileges, (privilege, index, i0) => {
      return common_vendor.e({
        a: privilege.selected
      }, privilege.selected ? {} : {}, {
        b: privilege.selected ? 1 : "",
        c: common_vendor.o(($event) => $options.togglePrivilege(privilege), index),
        d: common_vendor.t(privilege.name),
        e: common_vendor.t(privilege.description),
        f: common_vendor.o(($event) => $options.configPrivilege(privilege), index),
        g: index
      });
    }),
    r: common_vendor.o((...args) => $options.addPrivilege && $options.addPrivilege(...args)),
    s: common_vendor.f($data.memberLevels, (level, index, i0) => {
      return common_vendor.e({
        a: level.selected
      }, level.selected ? {} : {}, {
        b: level.selected ? 1 : "",
        c: common_vendor.o(($event) => $options.toggleLevel(level), index),
        d: common_vendor.t(level.name),
        e: common_vendor.t(level.memberCount),
        f: index
      });
    }),
    t: $data.birthdaySettings.greetingText,
    v: common_vendor.o(($event) => $data.birthdaySettings.greetingText = $event.detail.value),
    w: common_assets._imports_0$47,
    x: common_vendor.t($data.birthdaySettings.greetingText || "亲爱的会员，祝您生日快乐！我们为您准备了专属生日礼包，点击查看详情。")
  } : {}, {
    y: $data.birthdaySettings.enabled
  }, $data.birthdaySettings.enabled ? {
    z: common_vendor.o((...args) => $options.saveSettings && $options.saveSettings(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/subPackages/merchant-admin-marketing/pages/marketing/member/birthday-privilege.js.map
